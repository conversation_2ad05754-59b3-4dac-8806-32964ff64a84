# 第一阶段：简单存放预编译的Go可执行文件
FROM alpine:latest AS binary-stage
WORKDIR /app
COPY bin/configtrans-service /app/configtrans-service

# 第二阶段：使用预先构建的libyang镜像
FROM libyang-builder:latest AS libyang-stage

# 基础镜像阶段 - 包含所有依赖和环境配置，较少更新
FROM golang:1.22 AS base-image

# 安装Python和基本依赖
RUN apt-get update && apt-get install -y \
    python3 \
    python3-dev \
    python3-pip \
    python3-setuptools \
    python3-wheel \
    python3-venv \
    libxml2 \
    libxml2-dev \
    libxslt1-dev \
    libpcre3-dev \
    libpcre2-dev \
    openssl \
    iputils-ping \
    net-tools \
    curl \
    redis-server \
    tzdata \
    gcc \
    build-essential \
    bash \
    gettext \
    procps \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 设置时区为北京时间
ENV TZ=Asia/Shanghai
RUN ln -sf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 设置Python环境
RUN ln -sf /usr/bin/python3 /usr/bin/python && ln -sf /usr/bin/pip3 /usr/bin/pip && \
    python -m venv /opt/venv && \
    /opt/venv/bin/pip install --upgrade pip

ENV PATH="/opt/venv/bin:$PATH" \
    VIRTUAL_ENV="/opt/venv"

# 复制必要的库文件和可执行文件
COPY --from=libyang-stage /usr/local/lib/ /usr/local/lib/
COPY --from=libyang-stage /usr/local/bin/yang* /usr/local/bin/
COPY --from=libyang-stage /usr/local/include/libyang /usr/local/include/libyang

# 设置库路径并运行ldconfig
RUN echo "/usr/local/lib" > /etc/ld.so.conf.d/local.conf && ldconfig && \
    chmod +x /usr/local/bin/yang*

# 验证yanglint是否可用
RUN yanglint --version

# 创建应用目录结构
RUN mkdir -p /app/engine/lib/system /app/engine/lib/x86 \
    /app/data/uploads /app/data/temp /app/data/output /app/data/mappings /app/logs

# 设置工作目录
WORKDIR /app

# 安装Python常用依赖
RUN pip install lxml pyyaml requests 

# 设置默认环境变量
ENV DEBUG=false \
    LOGLEVEL=info \
    HOST=0.0.0.0 \
    PORT=9005 \
    NGINX_HOST=localhost \
    NGINX_PORT=9527 \
    NGINX_PATH=/api \
    NGINX_MAX_BODY_SIZE=50 \
    READ_TIMEOUT=60 \
    WRITE_TIMEOUT=60 \
    MAX_SIZE=10240 \
    PPROF_ENABLED=true \
    DB_ENABLED=false \
    PYTHONPATH=/app \
    CONFIG_FILE=/app/application.yml \
    TEMPLATE_CONFIG_FILE=/app/docker-application.yml.template \
    DEFAULT_CONFIG_FILE=/app/docker-application.yml \
    USE_DEFAULT_CONFIG=false \
    LD_LIBRARY_PATH=/usr/local/lib:/usr/lib:/lib:$LD_LIBRARY_PATH \
    PATH="/opt/venv/bin:/usr/local/bin:$PATH"

# 暴露应用端口
EXPOSE 9005

# 应用镜像阶段 - 只包含应用程序代码和配置文件，频繁更新
FROM base-image AS app-image

# 复制应用文件
COPY --from=binary-stage /app/configtrans-service /app/
COPY start.sh watchdog.sh /app/
COPY docker-application.yml.template /app/docker-application.yml.template
COPY docker-application.yml /app/docker-application.yml
COPY apis/rbac_model.conf /app/rbac_model.conf
COPY apis/locales/ /app/locales/
COPY engine/ /app/engine/
COPY requirements.txt /app/

# 设置可执行权限
RUN chmod +x /app/configtrans-service /app/start.sh /app/watchdog.sh && \
    if [ -f /app/engine/main.py ]; then chmod +x /app/engine/main.py; fi && \
    find /app/engine -name "*.py" -exec chmod +x {} \;

# 安装Python依赖
RUN pip install -r requirements.txt && \
    if [ -f /app/engine/requirements.txt ]; then pip install -r /app/engine/requirements.txt; fi

# 为脚本添加注释，说明如何启用静态配置
RUN echo '# 如需使用静态配置文件而非环境变量替换，请设置 USE_DEFAULT_CONFIG=true 环境变量' > /app/README.config && \
    echo '# 例如: docker run -e USE_DEFAULT_CONFIG=true -p 9005:9005 your-image-name' >> /app/README.config && \
    echo '# 或者，您可以通过复制 docker-application.yml 到 application.yml 来手动设置静态配置' >> /app/README.config

# 设置Docker健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -s -f http://localhost:9005/ || exit 1

# 使用看门狗脚本作为入口点
ENTRYPOINT ["/app/watchdog.sh"]
CMD ["/app/configtrans-service"] 