# 应用镜像 Dockerfile - 只包含应用程序代码和配置
# 该镜像基于基础镜像，构建速度快，更新频繁

# 第一阶段：简单存放预编译的Go可执行文件
FROM alpine:latest AS binary-stage
WORKDIR /app
COPY bin/configtrans-service /app/configtrans-service

# 使用预先构建好的基础镜像
# 注意：此处必须使用完全相同的镜像名称，不要修改
FROM registry.cn-hangzhou.aliyuncs.com/secloud/config-converter-base:2.9.1

# 直接从二进制阶段复制文件，跳过压缩步骤
COPY --from=binary-stage /app/configtrans-service /app/
COPY start.sh /app/
COPY docker-application.yml /app/docker-application.yml
COPY supervisor.conf /etc/supervisor/conf.d/app.conf

# 复制国际化文件到可执行文件所在目录下的 locales 目录
COPY apis/locales/ /app/locales/

# 直接复制整个engine目录，简化复制过程
COPY engine/ /app/engine/

# 确保国际化(i18n)目录存在
RUN mkdir -p /app/engine/locales

# 设置可执行权限和处理国际化文件
RUN chmod +x /app/configtrans-service /app/start.sh && \
    if [ -f /app/engine/main.py ]; then chmod +x /app/engine/main.py; fi && \
    find /app/engine -name "*.py" -exec chmod +x {} \; && \
    # 确保国际化文件有正确的权限
    chmod -R 755 /app/locales && \
    if [ -d /app/engine/locales ]; then chmod -R 755 /app/engine/locales; fi && \
    # 直接授权整个lib目录，确保所有库文件和子目录都有正确权限
    chmod -R 755 /app/engine/lib

# 安装应用特定的Python依赖（如果有新的依赖）
# 注意：通用依赖已在基础镜像中安装（包括新架构依赖：psutil, pydantic, typing_extensions）
RUN if [ -f /app/engine/requirements.txt ]; then \
      grep -v -e "^lxml" -e "^pyyaml" -e "^requests" -e "^setuptools" -e "^wheel" -e "^psutil" -e "^pydantic" -e "^typing_extensions" /app/engine/requirements.txt > /tmp/filtered_requirements.txt 2>/dev/null || : ; \
      if [ -s /tmp/filtered_requirements.txt ]; then \
        pip install --no-cache-dir --no-compile -r /tmp/filtered_requirements.txt; \
      fi; \
    fi

# 设置国际化支持的环境变量
ENV LANG=C.UTF-8 \
    LC_ALL=C.UTF-8 \
    PYTHONIOENCODING=utf-8

# 设置Docker健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -s -f http://localhost:9005/ || exit 1

# 使用supervisor作为入口点
ENTRYPOINT ["supervisord"]
CMD ["-n", "-c", "/etc/supervisor/conf.d/app.conf"] 