# 基础镜像 Dockerfile - 包含所有依赖和环境配置
# 该镜像较少更新，完成构建后推送至仓库供应用镜像使用
#
# 阿里云仓库地址：registry.cn-hangzhou.aliyuncs.com/secloud/config-converter-base:2.9.1
# 版本2.9.1: 新增新架构支持，添加psutil、pydantic等依赖

# 使用预先构建的libyang镜像
FROM libyang-builder:latest AS libyang-stage

# 基础镜像 - 使用Debian基础的golang镜像以确保库兼容性
FROM golang:1.22

# 安装Python和基本依赖
RUN apt-get update && apt-get install -y \
    python3 \
    python3-dev \
    python3-pip \
    python3-setuptools \
    python3-wheel \
    python3-venv \
    libxml2 \
    libxml2-dev \
    libxslt1-dev \
    libpcre3-dev \
    libpcre2-dev \
    openssl \
    iputils-ping \
    net-tools \
    curl \
    redis-server \
    tzdata \
    gcc \
    build-essential \
    bash \
    gettext \
    procps \
    supervisor \
    patchelf \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 设置时区为北京时间
ENV TZ=Asia/Shanghai
RUN ln -sf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 设置Python环境
RUN ln -sf /usr/bin/python3 /usr/bin/python && ln -sf /usr/bin/pip3 /usr/bin/pip && \
    python -m venv /opt/venv && \
    /opt/venv/bin/pip install --upgrade pip

ENV PATH="/opt/venv/bin:$PATH" \
    VIRTUAL_ENV="/opt/venv"

# 复制必要的库文件和可执行文件
COPY --from=libyang-stage /usr/local/lib/ /usr/local/lib/
COPY --from=libyang-stage /usr/local/bin/yang* /usr/local/bin/
COPY --from=libyang-stage /usr/local/include/libyang /usr/local/include/libyang

# 设置库路径并运行ldconfig
RUN echo "/usr/local/lib" > /etc/ld.so.conf.d/local.conf && ldconfig && \
    chmod +x /usr/local/bin/yang*

# 验证yanglint是否可用
RUN yanglint --version

# 创建应用目录结构
RUN mkdir -p /app/engine/lib/system /app/engine/lib/x86 /app/engine/lib/crypto \
    /app/data/uploads /app/data/temp /app/data/output /app/data/mappings /app/logs

# 设置工作目录
WORKDIR /app

# 复制通用脚本和配置文件
COPY healthcheck.sh docker-application.yml.template /app/
COPY apis/rbac_model.conf /app/rbac_model.conf
RUN chmod +x /app/healthcheck.sh 

# 复制加密库文件到基础镜像
# 注意：需要确保构建上下文中有 engine/lib/x86 目录及相关库文件
COPY engine/lib/x86/ /app/engine/lib/x86/

# 处理加密库文件
RUN cd /app/engine/lib/x86 && \
    # 首先，删除可能冲突的系统库文件
    if [ -f libpthread.so.0 ]; then rm -f libpthread.so.0; fi && \
    if [ -f libc.so.6 ]; then rm -f libc.so.6; fi && \
    if [ -f libdl.so.2 ]; then rm -f libdl.so.2; fi && \
    if [ -f librt.so.1 ]; then rm -f librt.so.1; fi && \
    if [ -f ld-linux-x86-64.so.2 ]; then rm -f ld-linux-x86-64.so.2; fi && \
    # 然后移动加密相关库到专用目录
    for lib in libconf_compress.so libttylog.so libcrypto.so.1.1 libssl.so.1.1 libpwd_crypto.so libmemory_mgmt.so; do \
        if [ -f "$lib" ]; then \
            cp -f "$lib" /app/engine/lib/crypto/; \
            chmod 755 /app/engine/lib/crypto/"$lib"; \
        fi; \
    done && \
    # 显示库目录内容
    ls -la /app/engine/lib/crypto/

# 将加密库复制到系统目录以便运行时加载
RUN mkdir -p /usr/lib && \
    # 复制加密库文件到系统库目录
    cp -f /app/engine/lib/crypto/*.so* /usr/lib/ 2>/dev/null || : && \
    chmod 755 /usr/lib/*.so* 2>/dev/null || : && \
    # 使用patchelf修复库路径
    for lib in /usr/lib/libconf_compress.so /usr/lib/libttylog.so /usr/lib/libpwd_crypto.so /usr/lib/libmemory_mgmt.so; do \
        if [ -f "$lib" ]; then \
            patchelf --set-rpath /usr/lib "$lib"; \
        fi; \
    done && \
    # 运行ldconfig更新库缓存
    ldconfig

# 安装Python常用依赖（包含新架构所需依赖）
RUN pip install --no-cache-dir \
    lxml>=4.6.3 \
    pyyaml>=5.4.1 \
    requests \
    setuptools \
    wheel \
    psutil>=5.8.0 \
    pydantic>=1.8.0 \
    typing_extensions>=4.0.0

# 配置supervisor
RUN mkdir -p /var/log/supervisor /etc/supervisor/conf.d
RUN echo_supervisord_conf > /etc/supervisor/supervisord.conf
RUN echo "[include]" >> /etc/supervisor/supervisord.conf && \
    echo "files = /etc/supervisor/conf.d/*.conf" >> /etc/supervisor/supervisord.conf

# 设置默认环境变量
ENV DEBUG=false \
    LOGLEVEL=info \
    HOST=0.0.0.0 \
    PORT=9005 \
    NGINX_HOST=localhost \
    NGINX_PORT=9527 \
    NGINX_PATH=/api \
    NGINX_MAX_BODY_SIZE=50 \
    READ_TIMEOUT=60 \
    WRITE_TIMEOUT=60 \
    MAX_SIZE=10240 \
    PPROF_ENABLED=true \
    DB_ENABLED=false \
    PYTHONPATH=/app \
    CONFIG_FILE=/app/application.yml \
    TEMPLATE_CONFIG_FILE=/app/docker-application.yml.template \
    DEFAULT_CONFIG_FILE=/app/docker-application.yml \
    USE_DEFAULT_CONFIG=false \
    LD_LIBRARY_PATH=/usr/lib:/usr/local/lib \
    CRYPTO_LIBS_PATH=/usr/lib \
    PATH="/opt/venv/bin:/usr/local/bin:$PATH"

# 暴露应用端口
EXPOSE 9005 