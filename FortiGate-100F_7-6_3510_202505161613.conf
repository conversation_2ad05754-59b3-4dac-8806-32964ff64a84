#config-version=FG100F-7.6.3-FW-build3510-250415:opmode=0:vdom=0:user=admin
#conf_file_ver=1632761882725586
#buildno=3510
#global_vdom=1
config system global
    set admin-https-redirect disable
    set admintimeout 480
    set alias "FortiGate-100F"
    set gui-auto-upgrade-setup-warning disable
    set hostname "FortiGate-100F"
    set language simch
    set management-ip "**********"
    set switch-controller enable
    set timezone "Asia/Shanghai"
    set virtual-switch-vlan enable
end
config system accprofile
    edit "prof_admin"
        set secfabgrp read-write
        set ftviewgrp read-write
        set authgrp read-write
        set sysgrp read-write
        set netgrp read-write
        set loggrp read-write
        set fwgrp read-write
        set vpngrp read-write
        set utmgrp read-write
        set wifi read-write
        set cli-get enable
        set cli-show enable
        set cli-exec enable
        set cli-config enable
    next
end
config system np6xlite
    edit "np6xlite_0"
    next
end
config system switch-interface
    edit "t1"
        set vdom "root"
        set member "port9"
    next
    edit "t2"
        set vdom "root"
        set member "port10"
    next
end
config system interface
    edit "dmz"
        set vdom "root"
        set ip ********** *************
        set allowaccess ping https fabric
        set type physical
        set role dmz
        set snmp-index 1
    next
    edit "mgmt"
        set vdom "root"
        set ip ********** *************
        set allowaccess ping https ssh snmp http fgfm radius-acct fabric ftm speed-test scim
        set type physical
        set lldp-reception disable
        set lldp-transmission disable
        set role lan
        set snmp-index 2
        set ip-managed-by-fortiipam disable
    next
    edit "wan1"
        set vdom "root"
        set mode dhcp
        set allowaccess ping
        set type physical
        set role wan
        set snmp-index 3
    next
    edit "wan2"
        set vdom "root"
        set mode dhcp
        set allowaccess ping
        set type physical
        set role wan
        set snmp-index 4
    next
    edit "ha1"
        set vdom "root"
        set type physical
        set snmp-index 5
    next
    edit "ha2"
        set vdom "root"
        set type physical
        set snmp-index 6
    next
    edit "port1"
        set vdom "root"
        set type physical
        set snmp-index 7
    next
    edit "port2"
        set vdom "root"
        set type physical
        set snmp-index 8
    next
    edit "port3"
        set vdom "root"
        set type physical
        set snmp-index 9
    next
    edit "port4"
        set vdom "root"
        set type physical
        set snmp-index 10
    next
    edit "port5"
        set vdom "root"
        set type physical
        set snmp-index 11
    next
    edit "port6"
        set vdom "root"
        set type physical
        set snmp-index 12
    next
    edit "port7"
        set vdom "root"
        set type physical
        set snmp-index 13
    next
    edit "port8"
        set vdom "root"
        set type physical
        set snmp-index 14
    next
    edit "port9"
        set vdom "root"
        set type physical
        set snmp-index 15
    next
    edit "port10"
        set vdom "root"
        set type physical
        set snmp-index 16
    next
    edit "port11"
        set vdom "root"
        set type physical
        set role wan
        set snmp-index 17
    next
    edit "port12"
        set vdom "root"
        set type physical
        set snmp-index 18
    next
    edit "x1"
        set vdom "root"
        set ips-sniffer-mode enable
        set type physical
        set mediatype sr
        set monitor-bandwidth enable
        set snmp-index 19
        set speed 10000full
    next
    edit "x2"
        set vdom "root"
        set ips-sniffer-mode enable
        set status down
        set type physical
        set mediatype sr
        set monitor-bandwidth enable
        set snmp-index 20
        set speed 10000full
    next
    edit "port13"
        set vdom "root"
        set type physical
        set mediatype none
        set snmp-index 21
        set speed 1000full
    next
    edit "port14"
        set vdom "root"
        set type physical
        set mediatype none
        set snmp-index 22
        set speed 1000full
    next
    edit "port15"
        set vdom "root"
        set type physical
        set mediatype none
        set snmp-index 23
        set speed 1000full
    next
    edit "port16"
        set vdom "root"
        set type physical
        set mediatype none
        set snmp-index 24
        set speed 1000full
    next
    edit "port17"
        set vdom "root"
        set type physical
        set snmp-index 25
        set speed 1000full
    next
    edit "port18"
        set vdom "root"
        set type physical
        set snmp-index 26
        set speed 1000full
    next
    edit "port19"
        set vdom "root"
        set type physical
        set snmp-index 27
        set speed 1000full
    next
    edit "port20"
        set vdom "root"
        set type physical
        set snmp-index 28
        set speed 1000full
    next
    edit "modem"
        set vdom "root"
        set mode pppoe
        set status down
        set type physical
        set snmp-index 29
    next
    edit "naf.root"
        set vdom "root"
        set type tunnel
        set src-check disable
        set snmp-index 30
    next
    edit "l2t.root"
        set vdom "root"
        set type tunnel
        set snmp-index 31
    next
    edit "ssl.root"
        set vdom "root"
        set type tunnel
        set alias "SSL VPN interface"
        set snmp-index 32
    next
    edit "t1"
        set vdom "root"
        set type switch
        set device-identification enable
        set lldp-transmission enable
        set role lan
        set snmp-index 35
    next
    edit "t2"
        set vdom "root"
        set type switch
        set device-identification enable
        set lldp-transmission enable
        set role lan
        set snmp-index 36
    next
    edit "lan"
        set vdom "root"
        set ip ************** *************
        set allowaccess ping https ssh fabric
        set type hard-switch
        set stp enable
        set role lan
        set snmp-index 33
    next
    edit "fortilink"
        set vdom "root"
        set fortilink enable
        set ip ********** *************
        set allowaccess ping fabric
        set type aggregate
        set lldp-reception enable
        set lldp-transmission enable
        set snmp-index 34
    next
    edit "testppp91"
        set vdom "root"
        set mode pppoe
        set device-identification enable
        set role lan
        set snmp-index 37
        set username "asdf"
        set password ENC 1/d9OwJqCrAseC42m2/DHpJaruQ1Dy6AenkxBGjLtoCYDNykNBe2hKmUW3RqOOsiSjQOpl+ok2XAXqKDKG27AZgfxRoocqnGMPE5Gf7VbWwvlU9ZrSJtj1DiBSy0urofywngYgS0nA+DqcAK1mgFv08PF+/ICSdbvSIrbXU6Jt+dVeN1DiDBXoOouAqITUYPr2Z52VlmMjY3dkVA
        set interface "port9"
        set vlanid 91
    next
    edit "testppp92"
        set vdom "root"
        set mode pppoe
        set device-identification enable
        set role lan
        set snmp-index 38
        set username "user92"
        set password ENC IVC67WfRAsBlJpPLgPSxrgh5WuwKMh2wDCPFrx7Dl4cwm5M9WeWVh0vo1UYNgJgvSlP97RvflAk7yLuycyTnEwor2lntdX50LH1VcymDH8p8Y5TRu8SOjPk4cpnJbJ3IWfcF2dLCecorP97VtHy2hjLbBkFUA+CriLvlf8v6gzYT3iIMgoeU3ut8qyndjpFeJoTCt1lmMjY3dkVA
        set interface "port9"
        set vlanid 92
    next
end
config system physical-switch
    edit "sw0"
        set age-val 0
    next
end
config system virtual-switch
    edit "lan"
        set physical-switch "sw0"
        config port
            edit "port1"
            next
            edit "port2"
            next
            edit "port3"
            next
            edit "port4"
            next
            edit "port5"
            next
            edit "port6"
            next
            edit "port7"
            next
            edit "port8"
            next
            edit "port11"
            next
            edit "port12"
            next
            edit "port13"
            next
            edit "port14"
            next
            edit "port15"
            next
            edit "port16"
            next
            edit "port17"
            next
            edit "port18"
            next
            edit "port19"
            next
            edit "port20"
            next
        end
    next
end
config system custom-language
    edit "en"
        set filename "en"
    next
    edit "fr"
        set filename "fr"
    next
    edit "sp"
        set filename "sp"
    next
    edit "pg"
        set filename "pg"
    next
    edit "x-sjis"
        set filename "x-sjis"
    next
    edit "big5"
        set filename "big5"
    next
    edit "GB2312"
        set filename "GB2312"
    next
    edit "euc-kr"
        set filename "euc-kr"
    next
end
config system admin
    edit "admin"
        set old-password ENC SH2fTIx4qBkO3q4Y5eZIzP1Cs7WH6ysJAvjxgRgV7R0z77baAVSta93FaiYeaY=
        set accprofile "super_admin"
        set vdom "root"
        config gui-dashboard
            edit 1
                set name "Status"
                set vdom "root"
                set permanent enable
                config widget
                    edit 1
                        set width 1
                        set height 1
                    next
                    edit 2
                        set type licinfo
                        set x-pos 1
                        set width 1
                        set height 1
                    next
                    edit 3
                        set type forticloud
                        set x-pos 2
                        set width 1
                        set height 1
                    next
                    edit 4
                        set type security-fabric
                        set x-pos 3
                        set width 1
                        set height 1
                    next
                    edit 5
                        set type admins
                        set x-pos 4
                        set width 1
                        set height 1
                    next
                    edit 6
                        set type cpu-usage
                        set x-pos 5
                        set width 2
                        set height 1
                    next
                    edit 7
                        set type memory-usage
                        set x-pos 6
                        set width 1
                        set height 1
                    next
                    edit 8
                        set type sessions
                        set x-pos 7
                        set width 2
                        set height 1
                    next
                    edit 9
                        set type tr-history
                        set x-pos 8
                        set width 2
                        set height 1
                        set interface "x1"
                        set timeframe 1-hour
                        set csf-device "FG100FTK22031403"
                    next
                    edit 10
                        set type tr-history
                        set x-pos 9
                        set width 3
                        set height 1
                        set interface "x2"
                        set timeframe 1-hour
                        set csf-device "FG100FTK22031403"
                    next
                end
            next
            edit 2
                set name "Security"
                set vdom "root"
                config widget
                    edit 1
                        set type fortiview
                        set width 2
                        set height 1
                        set fortiview-type "compromisedHosts"
                        set fortiview-sort-by "verdict"
                        set fortiview-timeframe "hour"
                        set fortiview-visualization "table"
                    next
                    edit 2
                        set type fortiview
                        set x-pos 1
                        set width 2
                        set height 1
                        set fortiview-type "threats"
                        set fortiview-sort-by "threatLevel"
                        set fortiview-timeframe "hour"
                        set fortiview-visualization "table"
                    next
                    edit 3
                        set type device-vulnerability
                        set x-pos 2
                        set width 2
                        set height 1
                    next
                end
            next
            edit 3
                set name "Network"
                set vdom "root"
                config widget
                    edit 1
                        set type routing
                        set width 2
                        set height 1
                        set router-view-type "staticdynamic"
                    next
                    edit 2
                        set type dhcp
                        set x-pos 1
                        set width 2
                        set height 1
                    next
                    edit 3
                        set type virtual-wan
                        set x-pos 2
                        set width 2
                        set height 1
                    next
                    edit 4
                        set type ipsec-vpn
                        set x-pos 3
                        set width 2
                        set height 1
                    next
                end
            next
            edit 4
                set name "Assets & Identities"
                set vdom "root"
                config widget
                    edit 1
                        set type device-inventory
                        set width 2
                        set height 1
                    next
                    edit 2
                        set type identities
                        set x-pos 1
                        set width 2
                        set height 1
                    next
                    edit 3
                        set type firewall-user
                        set x-pos 2
                        set width 2
                        set height 1
                    next
                    edit 4
                        set type quarantine
                        set x-pos 3
                        set width 2
                        set height 1
                    next
                    edit 5
                        set type nac-vlans
                        set x-pos 4
                        set width 2
                        set height 1
                    next
                end
            next
            edit 5
                set name "WiFi"
                set vdom "root"
                config widget
                    edit 1
                        set type ap-status
                        set width 2
                        set height 1
                    next
                    edit 2
                        set type channel-utilization
                        set x-pos 1
                        set width 2
                        set height 1
                        set wifi-band "both"
                    next
                    edit 3
                        set type clients-by-ap
                        set x-pos 2
                        set width 2
                        set height 1
                        set wifi-band "both"
                    next
                    edit 4
                        set type client-signal-strength
                        set x-pos 3
                        set width 2
                        set height 1
                        set wifi-band "both"
                    next
                    edit 5
                        set type rogue-ap
                        set x-pos 4
                        set width 2
                        set height 1
                    next
                    edit 6
                        set type historical-clients
                        set x-pos 5
                        set width 2
                        set height 1
                        set timeframe 1-hour
                        set wifi-band "both"
                    next
                    edit 7
                        set type interfering-ssids
                        set x-pos 6
                        set width 2
                        set height 1
                        set wifi-band "both"
                    next
                    edit 8
                        set type wifi-login-failures
                        set x-pos 7
                        set width 2
                        set height 1
                    next
                end
            next
            edit 6
                set name "FortiView Sources"
                set vdom "root"
                set layout-type standalone
                set csf disable
                config widget
                    edit 1
                        set type fortiview
                        set width 6
                        set height 3
                        set fortiview-type "source"
                        set fortiview-sort-by "bytes"
                        set fortiview-timeframe "hour"
                        set fortiview-visualization "table"
                    next
                end
            next
            edit 7
                set name "FortiView Destinations"
                set vdom "root"
                set layout-type standalone
                set csf disable
                config widget
                    edit 1
                        set type fortiview
                        set width 6
                        set height 3
                        set fortiview-type "destination"
                        set fortiview-sort-by "bytes"
                        set fortiview-timeframe "hour"
                        set fortiview-visualization "table"
                    next
                end
            next
            edit 8
                set name "FortiView Applications"
                set vdom "root"
                set layout-type standalone
                set csf disable
                config widget
                    edit 1
                        set type fortiview
                        set width 6
                        set height 3
                        set fortiview-type "application"
                        set fortiview-sort-by "bytes"
                        set fortiview-timeframe "hour"
                        set fortiview-visualization "table"
                    next
                end
            next
            edit 9
                set name "FortiView Web Sites"
                set vdom "root"
                set layout-type standalone
                set csf disable
                config widget
                    edit 1
                        set type fortiview
                        set width 6
                        set height 3
                        set fortiview-type "website"
                        set fortiview-sort-by "sessions"
                        set fortiview-timeframe "hour"
                        set fortiview-visualization "table"
                    next
                end
            next
            edit 10
                set name "FortiView Policies"
                set vdom "root"
                set layout-type standalone
                set csf disable
                config widget
                    edit 1
                        set type fortiview
                        set width 6
                        set height 3
                        set fortiview-type "policy"
                        set fortiview-sort-by "bytes"
                        set fortiview-timeframe "hour"
                        set fortiview-visualization "table"
                    next
                end
            next
            edit 11
                set name "FortiView Sessions"
                set vdom "root"
                set layout-type standalone
                set csf disable
                config widget
                    edit 1
                        set type fortiview
                        set width 6
                        set height 3
                        set fortiview-type "realtimeSessions"
                        set fortiview-timeframe "realtime"
                        set fortiview-visualization "table"
                    next
                end
            next
        end
        set gui-ignore-release-overview-version "7.6"
        set password ENC PB2IXytvgXKLz9EP7O0E+WKDSxZePZX6vJON2m0EplADkQyUiWB6YY2yoVr7phaSzcOvQ57NEGtRm5D8fa+5aIr/NKIaTkT7dk7JHVAv7fkXdc=
    next
end
config system sso-admin
end
config system ha
    set override disable
end
config system dns
    set primary ***********
    set secondary ***********
    set protocol dot
    set server-hostname "globalsdns.fortinet.net"
end
config system replacemsg-image
    edit "logo_fnet"
    next
    edit "logo_fguard_wf"
    next
    edit "logo_v3_fguard_app"
    next
end
config system replacemsg mail "partial"
end
config system replacemsg http "url-block"
end
config system replacemsg http "urlfilter-err"
end
config system replacemsg http "infcache-block"
end
config system replacemsg http "http-contenttypeblock"
end
config system replacemsg http "https-invalid-cert-block"
end
config system replacemsg http "https-untrusted-cert-block"
end
config system replacemsg http "https-blocklisted-cert-block"
end
config system replacemsg http "https-ech-block"
end
config system replacemsg http "switching-protocols-block"
end
config system replacemsg http "http-antiphish-block"
end
config system replacemsg http "videofilter-block"
end
config system replacemsg http "videofilter-block-text"
end
config system replacemsg webproxy "deny"
end
config system replacemsg webproxy "user-limit"
end
config system replacemsg webproxy "auth-challenge"
end
config system replacemsg webproxy "auth-login-fail"
end
config system replacemsg webproxy "auth-group-info-fail"
end
config system replacemsg webproxy "http-err"
end
config system replacemsg webproxy "auth-ip-blackout"
end
config system replacemsg webproxy "ztna-invalid-cert"
end
config system replacemsg webproxy "ztna-empty-cert"
end
config system replacemsg webproxy "ztna-manageable-empty-cert"
end
config system replacemsg webproxy "ztna-no-api-gwy-matched"
end
config system replacemsg webproxy "ztna-cant-find-real-srv"
end
config system replacemsg webproxy "ztna-fqdn-dns-failed"
end
config system replacemsg webproxy "ztna-ssl-bookmark-failed"
end
config system replacemsg webproxy "ztna-no-policy-matched"
end
config system replacemsg webproxy "ztna-matched-deny-policy"
end
config system replacemsg webproxy "ztna-client-cert-revoked"
end
config system replacemsg webproxy "ztna-denied-by-matched-tags"
end
config system replacemsg webproxy "ztna-denied-no-matched-tags"
end
config system replacemsg webproxy "ztna-no-dev-info"
end
config system replacemsg webproxy "ztna-dev-is-offline"
end
config system replacemsg webproxy "ztna-dev-is-unmanageable"
end
config system replacemsg webproxy "ztna-auth-fail"
end
config system replacemsg webproxy "casb-block"
end
config system replacemsg webproxy "swp-empty-cert"
end
config system replacemsg webproxy "swp-manageable-empty-cert"
end
config system replacemsg ftp "ftp-explicit-banner"
end
config system replacemsg fortiguard-wf "ftgd-block"
end
config system replacemsg fortiguard-wf "ftgd-ovrd"
end
config system replacemsg fortiguard-wf "ftgd-quota"
end
config system replacemsg fortiguard-wf "ftgd-warning"
end
config system replacemsg spam "ipblocklist"
end
config system replacemsg spam "smtp-spam-dnsbl"
end
config system replacemsg spam "smtp-spam-feip"
end
config system replacemsg spam "smtp-spam-helo"
end
config system replacemsg spam "smtp-spam-emailblock-to"
end
config system replacemsg spam "smtp-spam-emailblock-from"
end
config system replacemsg spam "smtp-spam-emailblock-subject"
end
config system replacemsg spam "smtp-spam-mimeheader"
end
config system replacemsg spam "reversedns"
end
config system replacemsg spam "smtp-spam-ase"
end
config system replacemsg spam "submit"
end
config system replacemsg alertmail "alertmail-virus"
end
config system replacemsg alertmail "alertmail-block"
end
config system replacemsg alertmail "alertmail-nids-event"
end
config system replacemsg alertmail "alertmail-crit-event"
end
config system replacemsg alertmail "alertmail-disk-full"
end
config system replacemsg admin "pre_admin-disclaimer-text"
end
config system replacemsg admin "post_admin-disclaimer-text"
end
config system replacemsg auth "auth-disclaimer-page-1"
end
config system replacemsg auth "auth-disclaimer-page-2"
end
config system replacemsg auth "auth-disclaimer-page-3"
end
config system replacemsg auth "auth-proxy-reject-page"
end
config system replacemsg auth "auth-reject-page"
end
config system replacemsg auth "auth-login-page"
end
config system replacemsg auth "auth-login-failed-page"
end
config system replacemsg auth "auth-token-login-page"
end
config system replacemsg auth "auth-token-login-failed-page"
end
config system replacemsg auth "auth-success-msg"
end
config system replacemsg auth "auth-challenge-page"
end
config system replacemsg auth "auth-keepalive-page"
end
config system replacemsg auth "auth-portal-page"
end
config system replacemsg auth "auth-password-page"
end
config system replacemsg auth "auth-fortitoken-page"
end
config system replacemsg auth "auth-next-fortitoken-page"
end
config system replacemsg auth "auth-email-token-page"
end
config system replacemsg auth "auth-sms-token-page"
end
config system replacemsg auth "auth-email-harvesting-page"
end
config system replacemsg auth "auth-email-failed-page"
end
config system replacemsg auth "auth-cert-passwd-page"
end
config system replacemsg auth "auth-guest-print-page"
end
config system replacemsg auth "auth-guest-email-page"
end
config system replacemsg auth "auth-success-page"
end
config system replacemsg auth "auth-block-notification-page"
end
config system replacemsg auth "auth-quarantine-page"
end
config system replacemsg auth "auth-qtn-reject-page"
end
config system replacemsg auth "auth-saml-page"
end
config system replacemsg sslvpn "sslvpn-login"
end
config system replacemsg sslvpn "sslvpn-header"
end
config system replacemsg sslvpn "sslvpn-limit"
end
config system replacemsg sslvpn "hostcheck-error"
end
config system replacemsg sslvpn "sslvpn-provision-user"
end
config system replacemsg sslvpn "sslvpn-provision-user-sms"
end
config system replacemsg nac-quar "nac-quar-virus"
end
config system replacemsg nac-quar "nac-quar-dos"
end
config system replacemsg nac-quar "nac-quar-ips"
end
config system replacemsg nac-quar "nac-quar-dlp"
end
config system replacemsg nac-quar "nac-quar-admin"
end
config system replacemsg nac-quar "nac-quar-app"
end
config system replacemsg traffic-quota "per-ip-shaper-block"
end
config system replacemsg utm "virus-html"
end
config system replacemsg utm "client-virus-html"
end
config system replacemsg utm "virus-text"
end
config system replacemsg utm "dlp-html"
end
config system replacemsg utm "dlp-text"
end
config system replacemsg utm "appblk-html"
end
config system replacemsg utm "ipsblk-html"
end
config system replacemsg utm "virpatchblk-html"
end
config system replacemsg utm "ipsfail-html"
end
config system replacemsg utm "exe-text"
end
config system replacemsg utm "waf-html"
end
config system replacemsg utm "outbreak-prevention-html"
end
config system replacemsg utm "outbreak-prevention-text"
end
config system replacemsg utm "external-blocklist-html"
end
config system replacemsg utm "external-blocklist-text"
end
config system replacemsg utm "malware-stream-html"
end
config system replacemsg utm "malware-stream-text"
end
config system replacemsg utm "ems-threat-feed-html"
end
config system replacemsg utm "ems-threat-feed-text"
end
config system replacemsg utm "file-filter-html"
end
config system replacemsg utm "file-filter-text"
end
config system replacemsg utm "file-size-text"
end
config system replacemsg utm "transfer-size-text"
end
config system replacemsg utm "internal-error-text"
end
config system replacemsg utm "archive-block-html"
end
config system replacemsg utm "archive-block-text"
end
config system replacemsg utm "file-av-fail-text"
end
config system replacemsg utm "transfer-av-fail-text"
end
config system replacemsg utm "banned-word-html"
end
config system replacemsg utm "banned-word-text"
end
config system replacemsg utm "block-html"
end
config system replacemsg utm "block-text"
end
config system replacemsg utm "decompress-limit-text"
end
config system replacemsg utm "dlp-subject-text"
end
config system replacemsg utm "file-size-html"
end
config system replacemsg utm "client-file-size-html"
end
config system replacemsg utm "inline-scan-timeout-html"
end
config system replacemsg utm "inline-scan-timeout-text"
end
config system replacemsg utm "inline-scan-error-html"
end
config system replacemsg utm "inline-scan-error-text"
end
config system replacemsg utm "icap-block-text"
end
config system replacemsg utm "icap-error-text"
end
config system replacemsg utm "icap-http-error"
end
config system replacemsg icap "icap-req-resp"
end
config system replacemsg automation "automation-email"
end
config system snmp sysinfo
end
config system autoupdate schedule
    set frequency daily
    set time 01:60
end
config firewall internet-service-name
    edit "Google-Other"
        set internet-service-id 65536
    next
    edit "Google-Web"
        set internet-service-id 65537
    next
    edit "Google-ICMP"
        set internet-service-id 65538
    next
    edit "Google-DNS"
        set internet-service-id 65539
    next
    edit "Google-Outbound_Email"
        set internet-service-id 65540
    next
    edit "Google-SSH"
        set internet-service-id 65542
    next
    edit "Google-FTP"
        set internet-service-id 65543
    next
    edit "Google-NTP"
        set internet-service-id 65544
    next
    edit "Google-Inbound_Email"
        set internet-service-id 65545
    next
    edit "Google-LDAP"
        set internet-service-id 65550
    next
    edit "Google-NetBIOS.Session.Service"
        set internet-service-id 65551
    next
    edit "Google-RTMP"
        set internet-service-id 65552
    next
    edit "Google-NetBIOS.Name.Service"
        set internet-service-id 65560
    next
    edit "Google-Google.Cloud"
        set internet-service-id 65641
    next
    edit "Google-Google.Bot"
        set internet-service-id 65643
    next
    edit "Google-Gmail"
        set internet-service-id 65646
    next
    edit "Meta-Other"
        set internet-service-id 131072
    next
    edit "Meta-Web"
        set internet-service-id 131073
    next
    edit "Meta-ICMP"
        set internet-service-id 131074
    next
    edit "Meta-DNS"
        set internet-service-id 131075
    next
    edit "Meta-Outbound_Email"
        set internet-service-id 131076
    next
    edit "Meta-SSH"
        set internet-service-id 131078
    next
    edit "Meta-FTP"
        set internet-service-id 131079
    next
    edit "Meta-NTP"
        set internet-service-id 131080
    next
    edit "Meta-Inbound_Email"
        set internet-service-id 131081
    next
    edit "Meta-LDAP"
        set internet-service-id 131086
    next
    edit "Meta-NetBIOS.Session.Service"
        set internet-service-id 131087
    next
    edit "Meta-RTMP"
        set internet-service-id 131088
    next
    edit "Meta-NetBIOS.Name.Service"
        set internet-service-id 131096
    next
    edit "Meta-Whatsapp"
        set internet-service-id 131184
    next
    edit "Meta-Instagram"
        set internet-service-id 131189
    next
    edit "Apple-Other"
        set internet-service-id 196608
    next
    edit "Apple-Web"
        set internet-service-id 196609
    next
    edit "Apple-ICMP"
        set internet-service-id 196610
    next
    edit "Apple-DNS"
        set internet-service-id 196611
    next
    edit "Apple-Outbound_Email"
        set internet-service-id 196612
    next
    edit "Apple-SSH"
        set internet-service-id 196614
    next
    edit "Apple-FTP"
        set internet-service-id 196615
    next
    edit "Apple-NTP"
        set internet-service-id 196616
    next
    edit "Apple-Inbound_Email"
        set internet-service-id 196617
    next
    edit "Apple-LDAP"
        set internet-service-id 196622
    next
    edit "Apple-NetBIOS.Session.Service"
        set internet-service-id 196623
    next
    edit "Apple-RTMP"
        set internet-service-id 196624
    next
    edit "Apple-NetBIOS.Name.Service"
        set internet-service-id 196632
    next
    edit "Apple-App.Store"
        set internet-service-id 196723
    next
    edit "Apple-APNs"
        set internet-service-id 196747
    next
    edit "Yahoo-Other"
        set internet-service-id 262144
    next
    edit "Yahoo-Web"
        set internet-service-id 262145
    next
    edit "Yahoo-ICMP"
        set internet-service-id 262146
    next
    edit "Yahoo-DNS"
        set internet-service-id 262147
    next
    edit "Yahoo-Outbound_Email"
        set internet-service-id 262148
    next
    edit "Yahoo-SSH"
        set internet-service-id 262150
    next
    edit "Yahoo-FTP"
        set internet-service-id 262151
    next
    edit "Yahoo-NTP"
        set internet-service-id 262152
    next
    edit "Yahoo-Inbound_Email"
        set internet-service-id 262153
    next
    edit "Yahoo-LDAP"
        set internet-service-id 262158
    next
    edit "Yahoo-NetBIOS.Session.Service"
        set internet-service-id 262159
    next
    edit "Yahoo-RTMP"
        set internet-service-id 262160
    next
    edit "Yahoo-NetBIOS.Name.Service"
        set internet-service-id 262168
    next
    edit "Microsoft-Other"
        set internet-service-id 327680
    next
    edit "Microsoft-Web"
        set internet-service-id 327681
    next
    edit "Microsoft-ICMP"
        set internet-service-id 327682
    next
    edit "Microsoft-DNS"
        set internet-service-id 327683
    next
    edit "Microsoft-Outbound_Email"
        set internet-service-id 327684
    next
    edit "Microsoft-SSH"
        set internet-service-id 327686
    next
    edit "Microsoft-FTP"
        set internet-service-id 327687
    next
    edit "Microsoft-NTP"
        set internet-service-id 327688
    next
    edit "Microsoft-Inbound_Email"
        set internet-service-id 327689
    next
    edit "Microsoft-LDAP"
        set internet-service-id 327694
    next
    edit "Microsoft-NetBIOS.Session.Service"
        set internet-service-id 327695
    next
    edit "Microsoft-RTMP"
        set internet-service-id 327696
    next
    edit "Microsoft-NetBIOS.Name.Service"
        set internet-service-id 327704
    next
    edit "Microsoft-Skype_Teams"
        set internet-service-id 327781
    next
    edit "Microsoft-Office365"
        set internet-service-id 327782
    next
    edit "Microsoft-Azure"
        set internet-service-id 327786
    next
    edit "Microsoft-Bing.Bot"
        set internet-service-id 327788
    next
    edit "Microsoft-Outlook"
        set internet-service-id 327791
    next
    edit "Microsoft-Microsoft.Update"
        set internet-service-id 327793
    next
    edit "Microsoft-Dynamics"
        set internet-service-id 327837
    next
    edit "Microsoft-WNS"
        set internet-service-id 327839
    next
    edit "Microsoft-Office365.Published"
        set internet-service-id 327880
    next
    edit "Microsoft-Intune"
        set internet-service-id 327886
    next
    edit "Microsoft-Office365.Published.Optimize"
        set internet-service-id 327902
    next
    edit "Microsoft-Office365.Published.Allow"
        set internet-service-id 327903
    next
    edit "Microsoft-Office365.Published.USGOV"
        set internet-service-id 327917
    next
    edit "Microsoft-Azure.Monitor"
        set internet-service-id 327958
    next
    edit "Microsoft-Azure.SQL"
        set internet-service-id 327959
    next
    edit "Microsoft-Azure.AD"
        set internet-service-id 327960
    next
    edit "Microsoft-Azure.Data.Factory"
        set internet-service-id 327961
    next
    edit "Microsoft-Azure.Virtual.Desktop"
        set internet-service-id 327962
    next
    edit "Microsoft-Azure.Power.BI"
        set internet-service-id 327963
    next
    edit "Microsoft-Azure.Connectors"
        set internet-service-id 327980
    next
    edit "Microsoft-Teams.Published.Worldwide.Optimize"
        set internet-service-id 327991
    next
    edit "Microsoft-Teams.Published.Worldwide.Allow"
        set internet-service-id 327992
    next
    edit "Microsoft-Azure.Front.Door"
        set internet-service-id 327993
    next
    edit "Microsoft-Azure.Service.Bus"
        set internet-service-id 328007
    next
    edit "Microsoft-Azure.Microsoft.Defender"
        set internet-service-id 328009
    next
    edit "Microsoft-Azure.Resource.Manager"
        set internet-service-id 328013
    next
    edit "Microsoft-Azure.Arc.Infrastructure"
        set internet-service-id 328014
    next
    edit "Microsoft-Azure.Storage"
        set internet-service-id 328015
    next
    edit "Microsoft-Azure.ATP"
        set internet-service-id 328016
    next
    edit "Microsoft-Azure.Traffic.Manager"
        set internet-service-id 328017
    next
    edit "Microsoft-Azure.Windows.Admin.Center"
        set internet-service-id 328018
    next
    edit "Microsoft-Azure.KeyVault"
        set internet-service-id 328021
    next
    edit "Microsoft-Azure.Databricks"
        set internet-service-id 328034
    next
    edit "Microsoft-Azure.Event.Hub"
        set internet-service-id 328035
    next
    edit "Microsoft-Azure.Power.Platform"
        set internet-service-id 328043
    next
    edit "Amazon-Other"
        set internet-service-id 393216
    next
    edit "Amazon-Web"
        set internet-service-id 393217
    next
    edit "Amazon-ICMP"
        set internet-service-id 393218
    next
    edit "Amazon-DNS"
        set internet-service-id 393219
    next
    edit "Amazon-Outbound_Email"
        set internet-service-id 393220
    next
    edit "Amazon-SSH"
        set internet-service-id 393222
    next
    edit "Amazon-FTP"
        set internet-service-id 393223
    next
    edit "Amazon-NTP"
        set internet-service-id 393224
    next
    edit "Amazon-Inbound_Email"
        set internet-service-id 393225
    next
    edit "Amazon-LDAP"
        set internet-service-id 393230
    next
    edit "Amazon-NetBIOS.Session.Service"
        set internet-service-id 393231
    next
    edit "Amazon-RTMP"
        set internet-service-id 393232
    next
    edit "Amazon-NetBIOS.Name.Service"
        set internet-service-id 393240
    next
    edit "Amazon-AWS"
        set internet-service-id 393320
    next
    edit "Amazon-AWS.WorkSpaces.Gateway"
        set internet-service-id 393403
    next
    edit "Amazon-Twitch"
        set internet-service-id 393446
    next
    edit "Amazon-AWS.GovCloud.US"
        set internet-service-id 393452
    next
    edit "Amazon-AWS.EBS"
        set internet-service-id 393470
    next
    edit "Amazon-AWS.Cloud9"
        set internet-service-id 393471
    next
    edit "Amazon-AWS.DynamoDB"
        set internet-service-id 393472
    next
    edit "Amazon-AWS.Route53"
        set internet-service-id 393473
    next
    edit "Amazon-AWS.S3"
        set internet-service-id 393474
    next
    edit "Amazon-AWS.Kinesis.Video.Streams"
        set internet-service-id 393475
    next
    edit "Amazon-AWS.Global.Accelerator"
        set internet-service-id 393476
    next
    edit "Amazon-AWS.EC2"
        set internet-service-id 393477
    next
    edit "Amazon-AWS.API.Gateway"
        set internet-service-id 393478
    next
    edit "Amazon-AWS.Chime.Voice.Connector"
        set internet-service-id 393479
    next
    edit "Amazon-AWS.Connect"
        set internet-service-id 393480
    next
    edit "Amazon-AWS.CloudFront"
        set internet-service-id 393481
    next
    edit "Amazon-AWS.CodeBuild"
        set internet-service-id 393482
    next
    edit "Amazon-AWS.Chime.Meetings"
        set internet-service-id 393483
    next
    edit "Amazon-AWS.AppFlow"
        set internet-service-id 393484
    next
    edit "Amazon-Amazon.SES"
        set internet-service-id 393493
    next
    edit "eBay-Other"
        set internet-service-id 458752
    next
    edit "eBay-Web"
        set internet-service-id 458753
    next
    edit "eBay-ICMP"
        set internet-service-id 458754
    next
    edit "eBay-DNS"
        set internet-service-id 458755
    next
    edit "eBay-Outbound_Email"
        set internet-service-id 458756
    next
    edit "eBay-SSH"
        set internet-service-id 458758
    next
    edit "eBay-FTP"
        set internet-service-id 458759
    next
    edit "eBay-NTP"
        set internet-service-id 458760
    next
    edit "eBay-Inbound_Email"
        set internet-service-id 458761
    next
    edit "eBay-LDAP"
        set internet-service-id 458766
    next
    edit "eBay-NetBIOS.Session.Service"
        set internet-service-id 458767
    next
    edit "eBay-RTMP"
        set internet-service-id 458768
    next
    edit "eBay-NetBIOS.Name.Service"
        set internet-service-id 458776
    next
    edit "PayPal-Other"
        set internet-service-id 524288
    next
    edit "PayPal-Web"
        set internet-service-id 524289
    next
    edit "PayPal-ICMP"
        set internet-service-id 524290
    next
    edit "PayPal-DNS"
        set internet-service-id 524291
    next
    edit "PayPal-Outbound_Email"
        set internet-service-id 524292
    next
    edit "PayPal-SSH"
        set internet-service-id 524294
    next
    edit "PayPal-FTP"
        set internet-service-id 524295
    next
    edit "PayPal-NTP"
        set internet-service-id 524296
    next
    edit "PayPal-Inbound_Email"
        set internet-service-id 524297
    next
    edit "PayPal-LDAP"
        set internet-service-id 524302
    next
    edit "PayPal-NetBIOS.Session.Service"
        set internet-service-id 524303
    next
    edit "PayPal-RTMP"
        set internet-service-id 524304
    next
    edit "PayPal-NetBIOS.Name.Service"
        set internet-service-id 524312
    next
    edit "Box-Other"
        set internet-service-id 589824
    next
    edit "Box-Web"
        set internet-service-id 589825
    next
    edit "Box-ICMP"
        set internet-service-id 589826
    next
    edit "Box-DNS"
        set internet-service-id 589827
    next
    edit "Box-Outbound_Email"
        set internet-service-id 589828
    next
    edit "Box-SSH"
        set internet-service-id 589830
    next
    edit "Box-FTP"
        set internet-service-id 589831
    next
    edit "Box-NTP"
        set internet-service-id 589832
    next
    edit "Box-Inbound_Email"
        set internet-service-id 589833
    next
    edit "Box-LDAP"
        set internet-service-id 589838
    next
    edit "Box-NetBIOS.Session.Service"
        set internet-service-id 589839
    next
    edit "Box-RTMP"
        set internet-service-id 589840
    next
    edit "Box-NetBIOS.Name.Service"
        set internet-service-id 589848
    next
    edit "Salesforce-Other"
        set internet-service-id 655360
    next
    edit "Salesforce-Web"
        set internet-service-id 655361
    next
    edit "Salesforce-ICMP"
        set internet-service-id 655362
    next
    edit "Salesforce-DNS"
        set internet-service-id 655363
    next
    edit "Salesforce-Outbound_Email"
        set internet-service-id 655364
    next
    edit "Salesforce-SSH"
        set internet-service-id 655366
    next
    edit "Salesforce-FTP"
        set internet-service-id 655367
    next
    edit "Salesforce-NTP"
        set internet-service-id 655368
    next
    edit "Salesforce-Inbound_Email"
        set internet-service-id 655369
    next
    edit "Salesforce-LDAP"
        set internet-service-id 655374
    next
    edit "Salesforce-NetBIOS.Session.Service"
        set internet-service-id 655375
    next
    edit "Salesforce-RTMP"
        set internet-service-id 655376
    next
    edit "Salesforce-NetBIOS.Name.Service"
        set internet-service-id 655384
    next
    edit "Salesforce-Email.Relay"
        set internet-service-id 655530
    next
    edit "Salesforce-Hyperforce"
        set internet-service-id 655738
    next
    edit "Dropbox-Other"
        set internet-service-id 720896
    next
    edit "Dropbox-Web"
        set internet-service-id 720897
    next
    edit "Dropbox-ICMP"
        set internet-service-id 720898
    next
    edit "Dropbox-DNS"
        set internet-service-id 720899
    next
    edit "Dropbox-Outbound_Email"
        set internet-service-id 720900
    next
    edit "Dropbox-SSH"
        set internet-service-id 720902
    next
    edit "Dropbox-FTP"
        set internet-service-id 720903
    next
    edit "Dropbox-NTP"
        set internet-service-id 720904
    next
    edit "Dropbox-Inbound_Email"
        set internet-service-id 720905
    next
    edit "Dropbox-LDAP"
        set internet-service-id 720910
    next
    edit "Dropbox-NetBIOS.Session.Service"
        set internet-service-id 720911
    next
    edit "Dropbox-RTMP"
        set internet-service-id 720912
    next
    edit "Dropbox-NetBIOS.Name.Service"
        set internet-service-id 720920
    next
    edit "Netflix-Other"
        set internet-service-id 786432
    next
    edit "Netflix-Web"
        set internet-service-id 786433
    next
    edit "Netflix-ICMP"
        set internet-service-id 786434
    next
    edit "Netflix-DNS"
        set internet-service-id 786435
    next
    edit "Netflix-Outbound_Email"
        set internet-service-id 786436
    next
    edit "Netflix-SSH"
        set internet-service-id 786438
    next
    edit "Netflix-FTP"
        set internet-service-id 786439
    next
    edit "Netflix-NTP"
        set internet-service-id 786440
    next
    edit "Netflix-Inbound_Email"
        set internet-service-id 786441
    next
    edit "Netflix-LDAP"
        set internet-service-id 786446
    next
    edit "Netflix-NetBIOS.Session.Service"
        set internet-service-id 786447
    next
    edit "Netflix-RTMP"
        set internet-service-id 786448
    next
    edit "Netflix-NetBIOS.Name.Service"
        set internet-service-id 786456
    next
    edit "LinkedIn-Other"
        set internet-service-id 851968
    next
    edit "LinkedIn-Web"
        set internet-service-id 851969
    next
    edit "LinkedIn-ICMP"
        set internet-service-id 851970
    next
    edit "LinkedIn-DNS"
        set internet-service-id 851971
    next
    edit "LinkedIn-Outbound_Email"
        set internet-service-id 851972
    next
    edit "LinkedIn-SSH"
        set internet-service-id 851974
    next
    edit "LinkedIn-FTP"
        set internet-service-id 851975
    next
    edit "LinkedIn-NTP"
        set internet-service-id 851976
    next
    edit "LinkedIn-Inbound_Email"
        set internet-service-id 851977
    next
    edit "LinkedIn-LDAP"
        set internet-service-id 851982
    next
    edit "LinkedIn-NetBIOS.Session.Service"
        set internet-service-id 851983
    next
    edit "LinkedIn-RTMP"
        set internet-service-id 851984
    next
    edit "LinkedIn-NetBIOS.Name.Service"
        set internet-service-id 851992
    next
    edit "Adobe-Other"
        set internet-service-id 917504
    next
    edit "Adobe-Web"
        set internet-service-id 917505
    next
    edit "Adobe-ICMP"
        set internet-service-id 917506
    next
    edit "Adobe-DNS"
        set internet-service-id 917507
    next
    edit "Adobe-Outbound_Email"
        set internet-service-id 917508
    next
    edit "Adobe-SSH"
        set internet-service-id 917510
    next
    edit "Adobe-FTP"
        set internet-service-id 917511
    next
    edit "Adobe-NTP"
        set internet-service-id 917512
    next
    edit "Adobe-Inbound_Email"
        set internet-service-id 917513
    next
    edit "Adobe-LDAP"
        set internet-service-id 917518
    next
    edit "Adobe-NetBIOS.Session.Service"
        set internet-service-id 917519
    next
    edit "Adobe-RTMP"
        set internet-service-id 917520
    next
    edit "Adobe-NetBIOS.Name.Service"
        set internet-service-id 917528
    next
    edit "Adobe-Adobe.Experience.Cloud"
        set internet-service-id 917640
    next
    edit "Adobe-Adobe.Sign"
        set internet-service-id 917776
    next
    edit "Oracle-Other"
        set internet-service-id 983040
    next
    edit "Oracle-Web"
        set internet-service-id 983041
    next
    edit "Oracle-ICMP"
        set internet-service-id 983042
    next
    edit "Oracle-DNS"
        set internet-service-id 983043
    next
    edit "Oracle-Outbound_Email"
        set internet-service-id 983044
    next
    edit "Oracle-SSH"
        set internet-service-id 983046
    next
    edit "Oracle-FTP"
        set internet-service-id 983047
    next
    edit "Oracle-NTP"
        set internet-service-id 983048
    next
    edit "Oracle-Inbound_Email"
        set internet-service-id 983049
    next
    edit "Oracle-LDAP"
        set internet-service-id 983054
    next
    edit "Oracle-NetBIOS.Session.Service"
        set internet-service-id 983055
    next
    edit "Oracle-RTMP"
        set internet-service-id 983056
    next
    edit "Oracle-NetBIOS.Name.Service"
        set internet-service-id 983064
    next
    edit "Oracle-Oracle.Cloud"
        set internet-service-id 983171
    next
    edit "Hulu-Other"
        set internet-service-id 1048576
    next
    edit "Hulu-Web"
        set internet-service-id 1048577
    next
    edit "Hulu-ICMP"
        set internet-service-id 1048578
    next
    edit "Hulu-DNS"
        set internet-service-id 1048579
    next
    edit "Hulu-Outbound_Email"
        set internet-service-id 1048580
    next
    edit "Hulu-SSH"
        set internet-service-id 1048582
    next
    edit "Hulu-FTP"
        set internet-service-id 1048583
    next
    edit "Hulu-NTP"
        set internet-service-id 1048584
    next
    edit "Hulu-Inbound_Email"
        set internet-service-id 1048585
    next
    edit "Hulu-LDAP"
        set internet-service-id 1048590
    next
    edit "Hulu-NetBIOS.Session.Service"
        set internet-service-id 1048591
    next
    edit "Hulu-RTMP"
        set internet-service-id 1048592
    next
    edit "Hulu-NetBIOS.Name.Service"
        set internet-service-id 1048600
    next
    edit "Pinterest-Other"
        set internet-service-id 1114112
    next
    edit "Pinterest-Web"
        set internet-service-id 1114113
    next
    edit "Pinterest-ICMP"
        set internet-service-id 1114114
    next
    edit "Pinterest-DNS"
        set internet-service-id 1114115
    next
    edit "Pinterest-Outbound_Email"
        set internet-service-id 1114116
    next
    edit "Pinterest-SSH"
        set internet-service-id 1114118
    next
    edit "Pinterest-FTP"
        set internet-service-id 1114119
    next
    edit "Pinterest-NTP"
        set internet-service-id 1114120
    next
    edit "Pinterest-Inbound_Email"
        set internet-service-id 1114121
    next
    edit "Pinterest-LDAP"
        set internet-service-id 1114126
    next
    edit "Pinterest-NetBIOS.Session.Service"
        set internet-service-id 1114127
    next
    edit "Pinterest-RTMP"
        set internet-service-id 1114128
    next
    edit "Pinterest-NetBIOS.Name.Service"
        set internet-service-id 1114136
    next
    edit "LogMeIn-Other"
        set internet-service-id 1179648
    next
    edit "LogMeIn-Web"
        set internet-service-id 1179649
    next
    edit "LogMeIn-ICMP"
        set internet-service-id 1179650
    next
    edit "LogMeIn-DNS"
        set internet-service-id 1179651
    next
    edit "LogMeIn-Outbound_Email"
        set internet-service-id 1179652
    next
    edit "LogMeIn-SSH"
        set internet-service-id 1179654
    next
    edit "LogMeIn-FTP"
        set internet-service-id 1179655
    next
    edit "LogMeIn-NTP"
        set internet-service-id 1179656
    next
    edit "LogMeIn-Inbound_Email"
        set internet-service-id 1179657
    next
    edit "LogMeIn-LDAP"
        set internet-service-id 1179662
    next
    edit "LogMeIn-NetBIOS.Session.Service"
        set internet-service-id 1179663
    next
    edit "LogMeIn-RTMP"
        set internet-service-id 1179664
    next
    edit "LogMeIn-NetBIOS.Name.Service"
        set internet-service-id 1179672
    next
    edit "LogMeIn-GoTo.Suite"
        set internet-service-id 1179767
    next
    edit "Fortinet-Other"
        set internet-service-id 1245184
    next
    edit "Fortinet-Web"
        set internet-service-id 1245185
    next
    edit "Fortinet-ICMP"
        set internet-service-id 1245186
    next
    edit "Fortinet-DNS"
        set internet-service-id 1245187
    next
    edit "Fortinet-Outbound_Email"
        set internet-service-id 1245188
    next
    edit "Fortinet-SSH"
        set internet-service-id 1245190
    next
    edit "Fortinet-FTP"
        set internet-service-id 1245191
    next
    edit "Fortinet-NTP"
        set internet-service-id 1245192
    next
    edit "Fortinet-Inbound_Email"
        set internet-service-id 1245193
    next
    edit "Fortinet-LDAP"
        set internet-service-id 1245198
    next
    edit "Fortinet-NetBIOS.Session.Service"
        set internet-service-id 1245199
    next
    edit "Fortinet-RTMP"
        set internet-service-id 1245200
    next
    edit "Fortinet-NetBIOS.Name.Service"
        set internet-service-id 1245208
    next
    edit "Fortinet-FortiGuard"
        set internet-service-id 1245324
    next
    edit "Fortinet-FortiMail.Cloud"
        set internet-service-id 1245325
    next
    edit "Fortinet-FortiCloud"
        set internet-service-id 1245326
    next
    edit "Fortinet-FortiVoice.Cloud"
        set internet-service-id 1245432
    next
    edit "Fortinet-FortiGuard.Secure.DNS"
        set internet-service-id 1245454
    next
    edit "Fortinet-FortiEDR"
        set internet-service-id 1245475
    next
    edit "Fortinet-FortiClient.EMS"
        set internet-service-id 1245477
    next
    edit "Fortinet-FortiWeb.Cloud"
        set internet-service-id 1245480
    next
    edit "Fortinet-FortiSASE"
        set internet-service-id 1245481
    next
    edit "Fortinet-FortiGuard.SOCaaS"
        set internet-service-id 1245514
    next
    edit "Fortinet-FortiDLP.Cloud"
        set internet-service-id 1245546
    next
    edit "Fortinet-FortiMonitor"
        set internet-service-id 1245558
    next
    edit "Fortinet-FortiSandbox"
        set internet-service-id 1245560
    next
    edit "Fortinet-FortiSandbox.Cloud"
        set internet-service-id 1245561
    next
    edit "Kaspersky-Other"
        set internet-service-id 1310720
    next
    edit "Kaspersky-Web"
        set internet-service-id 1310721
    next
    edit "Kaspersky-ICMP"
        set internet-service-id 1310722
    next
    edit "Kaspersky-DNS"
        set internet-service-id 1310723
    next
    edit "Kaspersky-Outbound_Email"
        set internet-service-id 1310724
    next
    edit "Kaspersky-SSH"
        set internet-service-id 1310726
    next
    edit "Kaspersky-FTP"
        set internet-service-id 1310727
    next
    edit "Kaspersky-NTP"
        set internet-service-id 1310728
    next
    edit "Kaspersky-Inbound_Email"
        set internet-service-id 1310729
    next
    edit "Kaspersky-LDAP"
        set internet-service-id 1310734
    next
    edit "Kaspersky-NetBIOS.Session.Service"
        set internet-service-id 1310735
    next
    edit "Kaspersky-RTMP"
        set internet-service-id 1310736
    next
    edit "Kaspersky-NetBIOS.Name.Service"
        set internet-service-id 1310744
    next
    edit "McAfee-Other"
        set internet-service-id 1376256
    next
    edit "McAfee-Web"
        set internet-service-id 1376257
    next
    edit "McAfee-ICMP"
        set internet-service-id 1376258
    next
    edit "McAfee-DNS"
        set internet-service-id 1376259
    next
    edit "McAfee-Outbound_Email"
        set internet-service-id 1376260
    next
    edit "McAfee-SSH"
        set internet-service-id 1376262
    next
    edit "McAfee-FTP"
        set internet-service-id 1376263
    next
    edit "McAfee-NTP"
        set internet-service-id 1376264
    next
    edit "McAfee-Inbound_Email"
        set internet-service-id 1376265
    next
    edit "McAfee-LDAP"
        set internet-service-id 1376270
    next
    edit "McAfee-NetBIOS.Session.Service"
        set internet-service-id 1376271
    next
    edit "McAfee-RTMP"
        set internet-service-id 1376272
    next
    edit "McAfee-NetBIOS.Name.Service"
        set internet-service-id 1376280
    next
    edit "Symantec-Other"
        set internet-service-id 1441792
    next
    edit "Symantec-Web"
        set internet-service-id 1441793
    next
    edit "Symantec-ICMP"
        set internet-service-id 1441794
    next
    edit "Symantec-DNS"
        set internet-service-id 1441795
    next
    edit "Symantec-Outbound_Email"
        set internet-service-id 1441796
    next
    edit "Symantec-SSH"
        set internet-service-id 1441798
    next
    edit "Symantec-FTP"
        set internet-service-id 1441799
    next
    edit "Symantec-NTP"
        set internet-service-id 1441800
    next
    edit "Symantec-Inbound_Email"
        set internet-service-id 1441801
    next
    edit "Symantec-LDAP"
        set internet-service-id 1441806
    next
    edit "Symantec-NetBIOS.Session.Service"
        set internet-service-id 1441807
    next
    edit "Symantec-RTMP"
        set internet-service-id 1441808
    next
    edit "Symantec-NetBIOS.Name.Service"
        set internet-service-id 1441816
    next
    edit "Symantec-Symantec.Cloud"
        set internet-service-id 1441922
    next
    edit "VMware-Other"
        set internet-service-id 1507328
    next
    edit "VMware-Web"
        set internet-service-id 1507329
    next
    edit "VMware-ICMP"
        set internet-service-id 1507330
    next
    edit "VMware-DNS"
        set internet-service-id 1507331
    next
    edit "VMware-Outbound_Email"
        set internet-service-id 1507332
    next
    edit "VMware-SSH"
        set internet-service-id 1507334
    next
    edit "VMware-FTP"
        set internet-service-id 1507335
    next
    edit "VMware-NTP"
        set internet-service-id 1507336
    next
    edit "VMware-Inbound_Email"
        set internet-service-id 1507337
    next
    edit "VMware-LDAP"
        set internet-service-id 1507342
    next
    edit "VMware-NetBIOS.Session.Service"
        set internet-service-id 1507343
    next
    edit "VMware-RTMP"
        set internet-service-id 1507344
    next
    edit "VMware-NetBIOS.Name.Service"
        set internet-service-id 1507352
    next
    edit "VMware-Workspace.ONE"
        set internet-service-id 1507461
    next
    edit "AOL-Other"
        set internet-service-id 1572864
    next
    edit "AOL-Web"
        set internet-service-id 1572865
    next
    edit "AOL-ICMP"
        set internet-service-id 1572866
    next
    edit "AOL-DNS"
        set internet-service-id 1572867
    next
    edit "AOL-Outbound_Email"
        set internet-service-id 1572868
    next
    edit "AOL-SSH"
        set internet-service-id 1572870
    next
    edit "AOL-FTP"
        set internet-service-id 1572871
    next
    edit "AOL-NTP"
        set internet-service-id 1572872
    next
    edit "AOL-Inbound_Email"
        set internet-service-id 1572873
    next
    edit "AOL-LDAP"
        set internet-service-id 1572878
    next
    edit "AOL-NetBIOS.Session.Service"
        set internet-service-id 1572879
    next
    edit "AOL-RTMP"
        set internet-service-id 1572880
    next
    edit "AOL-NetBIOS.Name.Service"
        set internet-service-id 1572888
    next
    edit "RealNetworks-Other"
        set internet-service-id 1638400
    next
    edit "RealNetworks-Web"
        set internet-service-id 1638401
    next
    edit "RealNetworks-ICMP"
        set internet-service-id 1638402
    next
    edit "RealNetworks-DNS"
        set internet-service-id 1638403
    next
    edit "RealNetworks-Outbound_Email"
        set internet-service-id 1638404
    next
    edit "RealNetworks-SSH"
        set internet-service-id 1638406
    next
    edit "RealNetworks-FTP"
        set internet-service-id 1638407
    next
    edit "RealNetworks-NTP"
        set internet-service-id 1638408
    next
    edit "RealNetworks-Inbound_Email"
        set internet-service-id 1638409
    next
    edit "RealNetworks-LDAP"
        set internet-service-id 1638414
    next
    edit "RealNetworks-NetBIOS.Session.Service"
        set internet-service-id 1638415
    next
    edit "RealNetworks-RTMP"
        set internet-service-id 1638416
    next
    edit "RealNetworks-NetBIOS.Name.Service"
        set internet-service-id 1638424
    next
    edit "Zoho-Other"
        set internet-service-id 1703936
    next
    edit "Zoho-Web"
        set internet-service-id 1703937
    next
    edit "Zoho-ICMP"
        set internet-service-id 1703938
    next
    edit "Zoho-DNS"
        set internet-service-id 1703939
    next
    edit "Zoho-Outbound_Email"
        set internet-service-id 1703940
    next
    edit "Zoho-SSH"
        set internet-service-id 1703942
    next
    edit "Zoho-FTP"
        set internet-service-id 1703943
    next
    edit "Zoho-NTP"
        set internet-service-id 1703944
    next
    edit "Zoho-Inbound_Email"
        set internet-service-id 1703945
    next
    edit "Zoho-LDAP"
        set internet-service-id 1703950
    next
    edit "Zoho-NetBIOS.Session.Service"
        set internet-service-id 1703951
    next
    edit "Zoho-RTMP"
        set internet-service-id 1703952
    next
    edit "Zoho-NetBIOS.Name.Service"
        set internet-service-id 1703960
    next
    edit "Zoho-Site24x7.Monitor"
        set internet-service-id 1704153
    next
    edit "Mozilla-Other"
        set internet-service-id 1769472
    next
    edit "Mozilla-Web"
        set internet-service-id 1769473
    next
    edit "Mozilla-ICMP"
        set internet-service-id 1769474
    next
    edit "Mozilla-DNS"
        set internet-service-id 1769475
    next
    edit "Mozilla-Outbound_Email"
        set internet-service-id 1769476
    next
    edit "Mozilla-SSH"
        set internet-service-id 1769478
    next
    edit "Mozilla-FTP"
        set internet-service-id 1769479
    next
    edit "Mozilla-NTP"
        set internet-service-id 1769480
    next
    edit "Mozilla-Inbound_Email"
        set internet-service-id 1769481
    next
    edit "Mozilla-LDAP"
        set internet-service-id 1769486
    next
    edit "Mozilla-NetBIOS.Session.Service"
        set internet-service-id 1769487
    next
    edit "Mozilla-RTMP"
        set internet-service-id 1769488
    next
    edit "Mozilla-NetBIOS.Name.Service"
        set internet-service-id 1769496
    next
    edit "TeamViewer-Other"
        set internet-service-id 1835008
    next
    edit "TeamViewer-Web"
        set internet-service-id 1835009
    next
    edit "TeamViewer-ICMP"
        set internet-service-id 1835010
    next
    edit "TeamViewer-DNS"
        set internet-service-id 1835011
    next
    edit "TeamViewer-Outbound_Email"
        set internet-service-id 1835012
    next
    edit "TeamViewer-SSH"
        set internet-service-id 1835014
    next
    edit "TeamViewer-FTP"
        set internet-service-id 1835015
    next
    edit "TeamViewer-NTP"
        set internet-service-id 1835016
    next
    edit "TeamViewer-Inbound_Email"
        set internet-service-id 1835017
    next
    edit "TeamViewer-LDAP"
        set internet-service-id 1835022
    next
    edit "TeamViewer-NetBIOS.Session.Service"
        set internet-service-id 1835023
    next
    edit "TeamViewer-RTMP"
        set internet-service-id 1835024
    next
    edit "TeamViewer-NetBIOS.Name.Service"
        set internet-service-id 1835032
    next
    edit "TeamViewer-TeamViewer"
        set internet-service-id 1835117
    next
    edit "HP-Other"
        set internet-service-id 1900544
    next
    edit "HP-Web"
        set internet-service-id 1900545
    next
    edit "HP-ICMP"
        set internet-service-id 1900546
    next
    edit "HP-DNS"
        set internet-service-id 1900547
    next
    edit "HP-Outbound_Email"
        set internet-service-id 1900548
    next
    edit "HP-SSH"
        set internet-service-id 1900550
    next
    edit "HP-FTP"
        set internet-service-id 1900551
    next
    edit "HP-NTP"
        set internet-service-id 1900552
    next
    edit "HP-Inbound_Email"
        set internet-service-id 1900553
    next
    edit "HP-LDAP"
        set internet-service-id 1900558
    next
    edit "HP-NetBIOS.Session.Service"
        set internet-service-id 1900559
    next
    edit "HP-RTMP"
        set internet-service-id 1900560
    next
    edit "HP-NetBIOS.Name.Service"
        set internet-service-id 1900568
    next
    edit "HP-Aruba"
        set internet-service-id 1900726
    next
    edit "Cisco-Other"
        set internet-service-id 1966080
    next
    edit "Cisco-Web"
        set internet-service-id 1966081
    next
    edit "Cisco-ICMP"
        set internet-service-id 1966082
    next
    edit "Cisco-DNS"
        set internet-service-id 1966083
    next
    edit "Cisco-Outbound_Email"
        set internet-service-id 1966084
    next
    edit "Cisco-SSH"
        set internet-service-id 1966086
    next
    edit "Cisco-FTP"
        set internet-service-id 1966087
    next
    edit "Cisco-NTP"
        set internet-service-id 1966088
    next
    edit "Cisco-Inbound_Email"
        set internet-service-id 1966089
    next
    edit "Cisco-LDAP"
        set internet-service-id 1966094
    next
    edit "Cisco-NetBIOS.Session.Service"
        set internet-service-id 1966095
    next
    edit "Cisco-RTMP"
        set internet-service-id 1966096
    next
    edit "Cisco-NetBIOS.Name.Service"
        set internet-service-id 1966104
    next
    edit "Cisco-Webex"
        set internet-service-id 1966183
    next
    edit "Cisco-Meraki.Cloud"
        set internet-service-id 1966218
    next
    edit "Cisco-Duo.Security"
        set internet-service-id 1966225
    next
    edit "Cisco-AppDynamic"
        set internet-service-id 1966260
    next
    edit "Cisco-Webex.FedRAMP"
        set internet-service-id 1966315
    next
    edit "Cisco-Secure.Endpoint"
        set internet-service-id 1966324
    next
    edit "IBM-Other"
        set internet-service-id 2031616
    next
    edit "IBM-Web"
        set internet-service-id 2031617
    next
    edit "IBM-ICMP"
        set internet-service-id 2031618
    next
    edit "IBM-DNS"
        set internet-service-id 2031619
    next
    edit "IBM-Outbound_Email"
        set internet-service-id 2031620
    next
    edit "IBM-SSH"
        set internet-service-id 2031622
    next
    edit "IBM-FTP"
        set internet-service-id 2031623
    next
    edit "IBM-NTP"
        set internet-service-id 2031624
    next
    edit "IBM-Inbound_Email"
        set internet-service-id 2031625
    next
    edit "IBM-LDAP"
        set internet-service-id 2031630
    next
    edit "IBM-NetBIOS.Session.Service"
        set internet-service-id 2031631
    next
    edit "IBM-RTMP"
        set internet-service-id 2031632
    next
    edit "IBM-NetBIOS.Name.Service"
        set internet-service-id 2031640
    next
    edit "IBM-IBM.Cloud"
        set internet-service-id 2031748
    next
    edit "Citrix-Other"
        set internet-service-id 2097152
    next
    edit "Citrix-Web"
        set internet-service-id 2097153
    next
    edit "Citrix-ICMP"
        set internet-service-id 2097154
    next
    edit "Citrix-DNS"
        set internet-service-id 2097155
    next
    edit "Citrix-Outbound_Email"
        set internet-service-id 2097156
    next
    edit "Citrix-SSH"
        set internet-service-id 2097158
    next
    edit "Citrix-FTP"
        set internet-service-id 2097159
    next
    edit "Citrix-NTP"
        set internet-service-id 2097160
    next
    edit "Citrix-Inbound_Email"
        set internet-service-id 2097161
    next
    edit "Citrix-LDAP"
        set internet-service-id 2097166
    next
    edit "Citrix-NetBIOS.Session.Service"
        set internet-service-id 2097167
    next
    edit "Citrix-RTMP"
        set internet-service-id 2097168
    next
    edit "Citrix-NetBIOS.Name.Service"
        set internet-service-id 2097176
    next
    edit "Twitter-Other"
        set internet-service-id 2162688
    next
    edit "Twitter-Web"
        set internet-service-id 2162689
    next
    edit "Twitter-ICMP"
        set internet-service-id 2162690
    next
    edit "Twitter-DNS"
        set internet-service-id 2162691
    next
    edit "Twitter-Outbound_Email"
        set internet-service-id 2162692
    next
    edit "Twitter-SSH"
        set internet-service-id 2162694
    next
    edit "Twitter-FTP"
        set internet-service-id 2162695
    next
    edit "Twitter-NTP"
        set internet-service-id 2162696
    next
    edit "Twitter-Inbound_Email"
        set internet-service-id 2162697
    next
    edit "Twitter-LDAP"
        set internet-service-id 2162702
    next
    edit "Twitter-NetBIOS.Session.Service"
        set internet-service-id 2162703
    next
    edit "Twitter-RTMP"
        set internet-service-id 2162704
    next
    edit "Twitter-NetBIOS.Name.Service"
        set internet-service-id 2162712
    next
    edit "Dell-Other"
        set internet-service-id 2228224
    next
    edit "Dell-Web"
        set internet-service-id 2228225
    next
    edit "Dell-ICMP"
        set internet-service-id 2228226
    next
    edit "Dell-DNS"
        set internet-service-id 2228227
    next
    edit "Dell-Outbound_Email"
        set internet-service-id 2228228
    next
    edit "Dell-SSH"
        set internet-service-id 2228230
    next
    edit "Dell-FTP"
        set internet-service-id 2228231
    next
    edit "Dell-NTP"
        set internet-service-id 2228232
    next
    edit "Dell-Inbound_Email"
        set internet-service-id 2228233
    next
    edit "Dell-LDAP"
        set internet-service-id 2228238
    next
    edit "Dell-NetBIOS.Session.Service"
        set internet-service-id 2228239
    next
    edit "Dell-RTMP"
        set internet-service-id 2228240
    next
    edit "Dell-NetBIOS.Name.Service"
        set internet-service-id 2228248
    next
    edit "Vimeo-Other"
        set internet-service-id 2293760
    next
    edit "Vimeo-Web"
        set internet-service-id 2293761
    next
    edit "Vimeo-ICMP"
        set internet-service-id 2293762
    next
    edit "Vimeo-DNS"
        set internet-service-id 2293763
    next
    edit "Vimeo-Outbound_Email"
        set internet-service-id 2293764
    next
    edit "Vimeo-SSH"
        set internet-service-id 2293766
    next
    edit "Vimeo-FTP"
        set internet-service-id 2293767
    next
    edit "Vimeo-NTP"
        set internet-service-id 2293768
    next
    edit "Vimeo-Inbound_Email"
        set internet-service-id 2293769
    next
    edit "Vimeo-LDAP"
        set internet-service-id 2293774
    next
    edit "Vimeo-NetBIOS.Session.Service"
        set internet-service-id 2293775
    next
    edit "Vimeo-RTMP"
        set internet-service-id 2293776
    next
    edit "Vimeo-NetBIOS.Name.Service"
        set internet-service-id 2293784
    next
    edit "Redhat-Other"
        set internet-service-id 2359296
    next
    edit "Redhat-Web"
        set internet-service-id 2359297
    next
    edit "Redhat-ICMP"
        set internet-service-id 2359298
    next
    edit "Redhat-DNS"
        set internet-service-id 2359299
    next
    edit "Redhat-Outbound_Email"
        set internet-service-id 2359300
    next
    edit "Redhat-SSH"
        set internet-service-id 2359302
    next
    edit "Redhat-FTP"
        set internet-service-id 2359303
    next
    edit "Redhat-NTP"
        set internet-service-id 2359304
    next
    edit "Redhat-Inbound_Email"
        set internet-service-id 2359305
    next
    edit "Redhat-LDAP"
        set internet-service-id 2359310
    next
    edit "Redhat-NetBIOS.Session.Service"
        set internet-service-id 2359311
    next
    edit "Redhat-RTMP"
        set internet-service-id 2359312
    next
    edit "Redhat-NetBIOS.Name.Service"
        set internet-service-id 2359320
    next
    edit "VK-Other"
        set internet-service-id 2424832
    next
    edit "VK-Web"
        set internet-service-id 2424833
    next
    edit "VK-ICMP"
        set internet-service-id 2424834
    next
    edit "VK-DNS"
        set internet-service-id 2424835
    next
    edit "VK-Outbound_Email"
        set internet-service-id 2424836
    next
    edit "VK-SSH"
        set internet-service-id 2424838
    next
    edit "VK-FTP"
        set internet-service-id 2424839
    next
    edit "VK-NTP"
        set internet-service-id 2424840
    next
    edit "VK-Inbound_Email"
        set internet-service-id 2424841
    next
    edit "VK-LDAP"
        set internet-service-id 2424846
    next
    edit "VK-NetBIOS.Session.Service"
        set internet-service-id 2424847
    next
    edit "VK-RTMP"
        set internet-service-id 2424848
    next
    edit "VK-NetBIOS.Name.Service"
        set internet-service-id 2424856
    next
    edit "TrendMicro-Other"
        set internet-service-id 2490368
    next
    edit "TrendMicro-Web"
        set internet-service-id 2490369
    next
    edit "TrendMicro-ICMP"
        set internet-service-id 2490370
    next
    edit "TrendMicro-DNS"
        set internet-service-id 2490371
    next
    edit "TrendMicro-Outbound_Email"
        set internet-service-id 2490372
    next
    edit "TrendMicro-SSH"
        set internet-service-id 2490374
    next
    edit "TrendMicro-FTP"
        set internet-service-id 2490375
    next
    edit "TrendMicro-NTP"
        set internet-service-id 2490376
    next
    edit "TrendMicro-Inbound_Email"
        set internet-service-id 2490377
    next
    edit "TrendMicro-LDAP"
        set internet-service-id 2490382
    next
    edit "TrendMicro-NetBIOS.Session.Service"
        set internet-service-id 2490383
    next
    edit "TrendMicro-RTMP"
        set internet-service-id 2490384
    next
    edit "TrendMicro-NetBIOS.Name.Service"
        set internet-service-id 2490392
    next
    edit "Tencent-Other"
        set internet-service-id 2555904
    next
    edit "Tencent-Web"
        set internet-service-id 2555905
    next
    edit "Tencent-ICMP"
        set internet-service-id 2555906
    next
    edit "Tencent-DNS"
        set internet-service-id 2555907
    next
    edit "Tencent-Outbound_Email"
        set internet-service-id 2555908
    next
    edit "Tencent-SSH"
        set internet-service-id 2555910
    next
    edit "Tencent-FTP"
        set internet-service-id 2555911
    next
    edit "Tencent-NTP"
        set internet-service-id 2555912
    next
    edit "Tencent-Inbound_Email"
        set internet-service-id 2555913
    next
    edit "Tencent-LDAP"
        set internet-service-id 2555918
    next
    edit "Tencent-NetBIOS.Session.Service"
        set internet-service-id 2555919
    next
    edit "Tencent-RTMP"
        set internet-service-id 2555920
    next
    edit "Tencent-NetBIOS.Name.Service"
        set internet-service-id 2555928
    next
    edit "Tencent-VooV.Meeting"
        set internet-service-id 2556219
    next
    edit "Ask-Other"
        set internet-service-id 2621440
    next
    edit "Ask-Web"
        set internet-service-id 2621441
    next
    edit "Ask-ICMP"
        set internet-service-id 2621442
    next
    edit "Ask-DNS"
        set internet-service-id 2621443
    next
    edit "Ask-Outbound_Email"
        set internet-service-id 2621444
    next
    edit "Ask-SSH"
        set internet-service-id 2621446
    next
    edit "Ask-FTP"
        set internet-service-id 2621447
    next
    edit "Ask-NTP"
        set internet-service-id 2621448
    next
    edit "Ask-Inbound_Email"
        set internet-service-id 2621449
    next
    edit "Ask-LDAP"
        set internet-service-id 2621454
    next
    edit "Ask-NetBIOS.Session.Service"
        set internet-service-id 2621455
    next
    edit "Ask-RTMP"
        set internet-service-id 2621456
    next
    edit "Ask-NetBIOS.Name.Service"
        set internet-service-id 2621464
    next
    edit "CNN-Other"
        set internet-service-id 2686976
    next
    edit "CNN-Web"
        set internet-service-id 2686977
    next
    edit "CNN-ICMP"
        set internet-service-id 2686978
    next
    edit "CNN-DNS"
        set internet-service-id 2686979
    next
    edit "CNN-Outbound_Email"
        set internet-service-id 2686980
    next
    edit "CNN-SSH"
        set internet-service-id 2686982
    next
    edit "CNN-FTP"
        set internet-service-id 2686983
    next
    edit "CNN-NTP"
        set internet-service-id 2686984
    next
    edit "CNN-Inbound_Email"
        set internet-service-id 2686985
    next
    edit "CNN-LDAP"
        set internet-service-id 2686990
    next
    edit "CNN-NetBIOS.Session.Service"
        set internet-service-id 2686991
    next
    edit "CNN-RTMP"
        set internet-service-id 2686992
    next
    edit "CNN-NetBIOS.Name.Service"
        set internet-service-id 2687000
    next
    edit "Myspace-Other"
        set internet-service-id 2752512
    next
    edit "Myspace-Web"
        set internet-service-id 2752513
    next
    edit "Myspace-ICMP"
        set internet-service-id 2752514
    next
    edit "Myspace-DNS"
        set internet-service-id 2752515
    next
    edit "Myspace-Outbound_Email"
        set internet-service-id 2752516
    next
    edit "Myspace-SSH"
        set internet-service-id 2752518
    next
    edit "Myspace-FTP"
        set internet-service-id 2752519
    next
    edit "Myspace-NTP"
        set internet-service-id 2752520
    next
    edit "Myspace-Inbound_Email"
        set internet-service-id 2752521
    next
    edit "Myspace-LDAP"
        set internet-service-id 2752526
    next
    edit "Myspace-NetBIOS.Session.Service"
        set internet-service-id 2752527
    next
    edit "Myspace-RTMP"
        set internet-service-id 2752528
    next
    edit "Myspace-NetBIOS.Name.Service"
        set internet-service-id 2752536
    next
    edit "Tor-Relay.Node"
        set internet-service-id 2818238
    next
    edit "Tor-Exit.Node"
        set internet-service-id 2818243
    next
    edit "Baidu-Other"
        set internet-service-id 2883584
    next
    edit "Baidu-Web"
        set internet-service-id 2883585
    next
    edit "Baidu-ICMP"
        set internet-service-id 2883586
    next
    edit "Baidu-DNS"
        set internet-service-id 2883587
    next
    edit "Baidu-Outbound_Email"
        set internet-service-id 2883588
    next
    edit "Baidu-SSH"
        set internet-service-id 2883590
    next
    edit "Baidu-FTP"
        set internet-service-id 2883591
    next
    edit "Baidu-NTP"
        set internet-service-id 2883592
    next
    edit "Baidu-Inbound_Email"
        set internet-service-id 2883593
    next
    edit "Baidu-LDAP"
        set internet-service-id 2883598
    next
    edit "Baidu-NetBIOS.Session.Service"
        set internet-service-id 2883599
    next
    edit "Baidu-RTMP"
        set internet-service-id 2883600
    next
    edit "Baidu-NetBIOS.Name.Service"
        set internet-service-id 2883608
    next
    edit "ntp.org-Other"
        set internet-service-id 2949120
    next
    edit "ntp.org-Web"
        set internet-service-id 2949121
    next
    edit "ntp.org-ICMP"
        set internet-service-id 2949122
    next
    edit "ntp.org-DNS"
        set internet-service-id 2949123
    next
    edit "ntp.org-Outbound_Email"
        set internet-service-id 2949124
    next
    edit "ntp.org-SSH"
        set internet-service-id 2949126
    next
    edit "ntp.org-FTP"
        set internet-service-id 2949127
    next
    edit "ntp.org-NTP"
        set internet-service-id 2949128
    next
    edit "ntp.org-Inbound_Email"
        set internet-service-id 2949129
    next
    edit "ntp.org-LDAP"
        set internet-service-id 2949134
    next
    edit "ntp.org-NetBIOS.Session.Service"
        set internet-service-id 2949135
    next
    edit "ntp.org-RTMP"
        set internet-service-id 2949136
    next
    edit "ntp.org-NetBIOS.Name.Service"
        set internet-service-id 2949144
    next
    edit "Proxy-Proxy.Server"
        set internet-service-id 3014850
    next
    edit "Botnet-C&C.Server"
        set internet-service-id 3080383
    next
    edit "Spam-Spamming.Server"
        set internet-service-id 3145920
    next
    edit "Phishing-Phishing.Server"
        set internet-service-id 3211457
    next
    edit "Zendesk-Other"
        set internet-service-id 3407872
    next
    edit "Zendesk-Web"
        set internet-service-id 3407873
    next
    edit "Zendesk-ICMP"
        set internet-service-id 3407874
    next
    edit "Zendesk-DNS"
        set internet-service-id 3407875
    next
    edit "Zendesk-Outbound_Email"
        set internet-service-id 3407876
    next
    edit "Zendesk-SSH"
        set internet-service-id 3407878
    next
    edit "Zendesk-FTP"
        set internet-service-id 3407879
    next
    edit "Zendesk-NTP"
        set internet-service-id 3407880
    next
    edit "Zendesk-Inbound_Email"
        set internet-service-id 3407881
    next
    edit "Zendesk-LDAP"
        set internet-service-id 3407886
    next
    edit "Zendesk-NetBIOS.Session.Service"
        set internet-service-id 3407887
    next
    edit "Zendesk-RTMP"
        set internet-service-id 3407888
    next
    edit "Zendesk-NetBIOS.Name.Service"
        set internet-service-id 3407896
    next
    edit "Zendesk-Zendesk.Suite"
        set internet-service-id 3408047
    next
    edit "DocuSign-Other"
        set internet-service-id 3473408
    next
    edit "DocuSign-Web"
        set internet-service-id 3473409
    next
    edit "DocuSign-ICMP"
        set internet-service-id 3473410
    next
    edit "DocuSign-DNS"
        set internet-service-id 3473411
    next
    edit "DocuSign-Outbound_Email"
        set internet-service-id 3473412
    next
    edit "DocuSign-SSH"
        set internet-service-id 3473414
    next
    edit "DocuSign-FTP"
        set internet-service-id 3473415
    next
    edit "DocuSign-NTP"
        set internet-service-id 3473416
    next
    edit "DocuSign-Inbound_Email"
        set internet-service-id 3473417
    next
    edit "DocuSign-LDAP"
        set internet-service-id 3473422
    next
    edit "DocuSign-NetBIOS.Session.Service"
        set internet-service-id 3473423
    next
    edit "DocuSign-RTMP"
        set internet-service-id 3473424
    next
    edit "DocuSign-NetBIOS.Name.Service"
        set internet-service-id 3473432
    next
    edit "ServiceNow-Other"
        set internet-service-id 3538944
    next
    edit "ServiceNow-Web"
        set internet-service-id 3538945
    next
    edit "ServiceNow-ICMP"
        set internet-service-id 3538946
    next
    edit "ServiceNow-DNS"
        set internet-service-id 3538947
    next
    edit "ServiceNow-Outbound_Email"
        set internet-service-id 3538948
    next
    edit "ServiceNow-SSH"
        set internet-service-id 3538950
    next
    edit "ServiceNow-FTP"
        set internet-service-id 3538951
    next
    edit "ServiceNow-NTP"
        set internet-service-id 3538952
    next
    edit "ServiceNow-Inbound_Email"
        set internet-service-id 3538953
    next
    edit "ServiceNow-LDAP"
        set internet-service-id 3538958
    next
    edit "ServiceNow-NetBIOS.Session.Service"
        set internet-service-id 3538959
    next
    edit "ServiceNow-RTMP"
        set internet-service-id 3538960
    next
    edit "ServiceNow-NetBIOS.Name.Service"
        set internet-service-id 3538968
    next
    edit "GitHub-GitHub"
        set internet-service-id 3604638
    next
    edit "Workday-Other"
        set internet-service-id 3670016
    next
    edit "Workday-Web"
        set internet-service-id 3670017
    next
    edit "Workday-ICMP"
        set internet-service-id 3670018
    next
    edit "Workday-DNS"
        set internet-service-id 3670019
    next
    edit "Workday-Outbound_Email"
        set internet-service-id 3670020
    next
    edit "Workday-SSH"
        set internet-service-id 3670022
    next
    edit "Workday-FTP"
        set internet-service-id 3670023
    next
    edit "Workday-NTP"
        set internet-service-id 3670024
    next
    edit "Workday-Inbound_Email"
        set internet-service-id 3670025
    next
    edit "Workday-LDAP"
        set internet-service-id 3670030
    next
    edit "Workday-NetBIOS.Session.Service"
        set internet-service-id 3670031
    next
    edit "Workday-RTMP"
        set internet-service-id 3670032
    next
    edit "Workday-NetBIOS.Name.Service"
        set internet-service-id 3670040
    next
    edit "HubSpot-Other"
        set internet-service-id 3735552
    next
    edit "HubSpot-Web"
        set internet-service-id 3735553
    next
    edit "HubSpot-ICMP"
        set internet-service-id 3735554
    next
    edit "HubSpot-DNS"
        set internet-service-id 3735555
    next
    edit "HubSpot-Outbound_Email"
        set internet-service-id 3735556
    next
    edit "HubSpot-SSH"
        set internet-service-id 3735558
    next
    edit "HubSpot-FTP"
        set internet-service-id 3735559
    next
    edit "HubSpot-NTP"
        set internet-service-id 3735560
    next
    edit "HubSpot-Inbound_Email"
        set internet-service-id 3735561
    next
    edit "HubSpot-LDAP"
        set internet-service-id 3735566
    next
    edit "HubSpot-NetBIOS.Session.Service"
        set internet-service-id 3735567
    next
    edit "HubSpot-RTMP"
        set internet-service-id 3735568
    next
    edit "HubSpot-NetBIOS.Name.Service"
        set internet-service-id 3735576
    next
    edit "Twilio-Other"
        set internet-service-id 3801088
    next
    edit "Twilio-Web"
        set internet-service-id 3801089
    next
    edit "Twilio-ICMP"
        set internet-service-id 3801090
    next
    edit "Twilio-DNS"
        set internet-service-id 3801091
    next
    edit "Twilio-Outbound_Email"
        set internet-service-id 3801092
    next
    edit "Twilio-SSH"
        set internet-service-id 3801094
    next
    edit "Twilio-FTP"
        set internet-service-id 3801095
    next
    edit "Twilio-NTP"
        set internet-service-id 3801096
    next
    edit "Twilio-Inbound_Email"
        set internet-service-id 3801097
    next
    edit "Twilio-LDAP"
        set internet-service-id 3801102
    next
    edit "Twilio-NetBIOS.Session.Service"
        set internet-service-id 3801103
    next
    edit "Twilio-RTMP"
        set internet-service-id 3801104
    next
    edit "Twilio-NetBIOS.Name.Service"
        set internet-service-id 3801112
    next
    edit "Twilio-Elastic.SIP.Trunking"
        set internet-service-id 3801277
    next
    edit "Coupa-Other"
        set internet-service-id 3866624
    next
    edit "Coupa-Web"
        set internet-service-id 3866625
    next
    edit "Coupa-ICMP"
        set internet-service-id 3866626
    next
    edit "Coupa-DNS"
        set internet-service-id 3866627
    next
    edit "Coupa-Outbound_Email"
        set internet-service-id 3866628
    next
    edit "Coupa-SSH"
        set internet-service-id 3866630
    next
    edit "Coupa-FTP"
        set internet-service-id 3866631
    next
    edit "Coupa-NTP"
        set internet-service-id 3866632
    next
    edit "Coupa-Inbound_Email"
        set internet-service-id 3866633
    next
    edit "Coupa-LDAP"
        set internet-service-id 3866638
    next
    edit "Coupa-NetBIOS.Session.Service"
        set internet-service-id 3866639
    next
    edit "Coupa-RTMP"
        set internet-service-id 3866640
    next
    edit "Coupa-NetBIOS.Name.Service"
        set internet-service-id 3866648
    next
    edit "Atlassian-Other"
        set internet-service-id 3932160
    next
    edit "Atlassian-Web"
        set internet-service-id 3932161
    next
    edit "Atlassian-ICMP"
        set internet-service-id 3932162
    next
    edit "Atlassian-DNS"
        set internet-service-id 3932163
    next
    edit "Atlassian-Outbound_Email"
        set internet-service-id 3932164
    next
    edit "Atlassian-SSH"
        set internet-service-id 3932166
    next
    edit "Atlassian-FTP"
        set internet-service-id 3932167
    next
    edit "Atlassian-NTP"
        set internet-service-id 3932168
    next
    edit "Atlassian-Inbound_Email"
        set internet-service-id 3932169
    next
    edit "Atlassian-LDAP"
        set internet-service-id 3932174
    next
    edit "Atlassian-NetBIOS.Session.Service"
        set internet-service-id 3932175
    next
    edit "Atlassian-RTMP"
        set internet-service-id 3932176
    next
    edit "Atlassian-NetBIOS.Name.Service"
        set internet-service-id 3932184
    next
    edit "Atlassian-Atlassian.Cloud"
        set internet-service-id 3932388
    next
    edit "Atlassian-Atlassian.Notification"
        set internet-service-id 3932436
    next
    edit "Xero-Other"
        set internet-service-id 3997696
    next
    edit "Xero-Web"
        set internet-service-id 3997697
    next
    edit "Xero-ICMP"
        set internet-service-id 3997698
    next
    edit "Xero-DNS"
        set internet-service-id 3997699
    next
    edit "Xero-Outbound_Email"
        set internet-service-id 3997700
    next
    edit "Xero-SSH"
        set internet-service-id 3997702
    next
    edit "Xero-FTP"
        set internet-service-id 3997703
    next
    edit "Xero-NTP"
        set internet-service-id 3997704
    next
    edit "Xero-Inbound_Email"
        set internet-service-id 3997705
    next
    edit "Xero-LDAP"
        set internet-service-id 3997710
    next
    edit "Xero-NetBIOS.Session.Service"
        set internet-service-id 3997711
    next
    edit "Xero-RTMP"
        set internet-service-id 3997712
    next
    edit "Xero-NetBIOS.Name.Service"
        set internet-service-id 3997720
    next
    edit "Zuora-Other"
        set internet-service-id 4063232
    next
    edit "Zuora-Web"
        set internet-service-id 4063233
    next
    edit "Zuora-ICMP"
        set internet-service-id 4063234
    next
    edit "Zuora-DNS"
        set internet-service-id 4063235
    next
    edit "Zuora-Outbound_Email"
        set internet-service-id 4063236
    next
    edit "Zuora-SSH"
        set internet-service-id 4063238
    next
    edit "Zuora-FTP"
        set internet-service-id 4063239
    next
    edit "Zuora-NTP"
        set internet-service-id 4063240
    next
    edit "Zuora-Inbound_Email"
        set internet-service-id 4063241
    next
    edit "Zuora-LDAP"
        set internet-service-id 4063246
    next
    edit "Zuora-NetBIOS.Session.Service"
        set internet-service-id 4063247
    next
    edit "Zuora-RTMP"
        set internet-service-id 4063248
    next
    edit "Zuora-NetBIOS.Name.Service"
        set internet-service-id 4063256
    next
    edit "AdRoll-Other"
        set internet-service-id 4128768
    next
    edit "AdRoll-Web"
        set internet-service-id 4128769
    next
    edit "AdRoll-ICMP"
        set internet-service-id 4128770
    next
    edit "AdRoll-DNS"
        set internet-service-id 4128771
    next
    edit "AdRoll-Outbound_Email"
        set internet-service-id 4128772
    next
    edit "AdRoll-SSH"
        set internet-service-id 4128774
    next
    edit "AdRoll-FTP"
        set internet-service-id 4128775
    next
    edit "AdRoll-NTP"
        set internet-service-id 4128776
    next
    edit "AdRoll-Inbound_Email"
        set internet-service-id 4128777
    next
    edit "AdRoll-LDAP"
        set internet-service-id 4128782
    next
    edit "AdRoll-NetBIOS.Session.Service"
        set internet-service-id 4128783
    next
    edit "AdRoll-RTMP"
        set internet-service-id 4128784
    next
    edit "AdRoll-NetBIOS.Name.Service"
        set internet-service-id 4128792
    next
    edit "Xactly-Other"
        set internet-service-id 4194304
    next
    edit "Xactly-Web"
        set internet-service-id 4194305
    next
    edit "Xactly-ICMP"
        set internet-service-id 4194306
    next
    edit "Xactly-DNS"
        set internet-service-id 4194307
    next
    edit "Xactly-Outbound_Email"
        set internet-service-id 4194308
    next
    edit "Xactly-SSH"
        set internet-service-id 4194310
    next
    edit "Xactly-FTP"
        set internet-service-id 4194311
    next
    edit "Xactly-NTP"
        set internet-service-id 4194312
    next
    edit "Xactly-Inbound_Email"
        set internet-service-id 4194313
    next
    edit "Xactly-LDAP"
        set internet-service-id 4194318
    next
    edit "Xactly-NetBIOS.Session.Service"
        set internet-service-id 4194319
    next
    edit "Xactly-RTMP"
        set internet-service-id 4194320
    next
    edit "Xactly-NetBIOS.Name.Service"
        set internet-service-id 4194328
    next
    edit "Intuit-Other"
        set internet-service-id 4259840
    next
    edit "Intuit-Web"
        set internet-service-id 4259841
    next
    edit "Intuit-ICMP"
        set internet-service-id 4259842
    next
    edit "Intuit-DNS"
        set internet-service-id 4259843
    next
    edit "Intuit-Outbound_Email"
        set internet-service-id 4259844
    next
    edit "Intuit-SSH"
        set internet-service-id 4259846
    next
    edit "Intuit-FTP"
        set internet-service-id 4259847
    next
    edit "Intuit-NTP"
        set internet-service-id 4259848
    next
    edit "Intuit-Inbound_Email"
        set internet-service-id 4259849
    next
    edit "Intuit-LDAP"
        set internet-service-id 4259854
    next
    edit "Intuit-NetBIOS.Session.Service"
        set internet-service-id 4259855
    next
    edit "Intuit-RTMP"
        set internet-service-id 4259856
    next
    edit "Intuit-NetBIOS.Name.Service"
        set internet-service-id 4259864
    next
    edit "Marketo-Other"
        set internet-service-id 4325376
    next
    edit "Marketo-Web"
        set internet-service-id 4325377
    next
    edit "Marketo-ICMP"
        set internet-service-id 4325378
    next
    edit "Marketo-DNS"
        set internet-service-id 4325379
    next
    edit "Marketo-Outbound_Email"
        set internet-service-id 4325380
    next
    edit "Marketo-SSH"
        set internet-service-id 4325382
    next
    edit "Marketo-FTP"
        set internet-service-id 4325383
    next
    edit "Marketo-NTP"
        set internet-service-id 4325384
    next
    edit "Marketo-Inbound_Email"
        set internet-service-id 4325385
    next
    edit "Marketo-LDAP"
        set internet-service-id 4325390
    next
    edit "Marketo-NetBIOS.Session.Service"
        set internet-service-id 4325391
    next
    edit "Marketo-RTMP"
        set internet-service-id 4325392
    next
    edit "Marketo-NetBIOS.Name.Service"
        set internet-service-id 4325400
    next
    edit "Bill-Other"
        set internet-service-id 4456448
    next
    edit "Bill-Web"
        set internet-service-id 4456449
    next
    edit "Bill-ICMP"
        set internet-service-id 4456450
    next
    edit "Bill-DNS"
        set internet-service-id 4456451
    next
    edit "Bill-Outbound_Email"
        set internet-service-id 4456452
    next
    edit "Bill-SSH"
        set internet-service-id 4456454
    next
    edit "Bill-FTP"
        set internet-service-id 4456455
    next
    edit "Bill-NTP"
        set internet-service-id 4456456
    next
    edit "Bill-Inbound_Email"
        set internet-service-id 4456457
    next
    edit "Bill-LDAP"
        set internet-service-id 4456462
    next
    edit "Bill-NetBIOS.Session.Service"
        set internet-service-id 4456463
    next
    edit "Bill-RTMP"
        set internet-service-id 4456464
    next
    edit "Bill-NetBIOS.Name.Service"
        set internet-service-id 4456472
    next
    edit "Shopify-Other"
        set internet-service-id 4521984
    next
    edit "Shopify-Web"
        set internet-service-id 4521985
    next
    edit "Shopify-ICMP"
        set internet-service-id 4521986
    next
    edit "Shopify-DNS"
        set internet-service-id 4521987
    next
    edit "Shopify-Outbound_Email"
        set internet-service-id 4521988
    next
    edit "Shopify-SSH"
        set internet-service-id 4521990
    next
    edit "Shopify-FTP"
        set internet-service-id 4521991
    next
    edit "Shopify-NTP"
        set internet-service-id 4521992
    next
    edit "Shopify-Inbound_Email"
        set internet-service-id 4521993
    next
    edit "Shopify-LDAP"
        set internet-service-id 4521998
    next
    edit "Shopify-NetBIOS.Session.Service"
        set internet-service-id 4521999
    next
    edit "Shopify-RTMP"
        set internet-service-id 4522000
    next
    edit "Shopify-NetBIOS.Name.Service"
        set internet-service-id 4522008
    next
    edit "Shopify-Shopify"
        set internet-service-id 4522162
    next
    edit "MuleSoft-Other"
        set internet-service-id 4587520
    next
    edit "MuleSoft-Web"
        set internet-service-id 4587521
    next
    edit "MuleSoft-ICMP"
        set internet-service-id 4587522
    next
    edit "MuleSoft-DNS"
        set internet-service-id 4587523
    next
    edit "MuleSoft-Outbound_Email"
        set internet-service-id 4587524
    next
    edit "MuleSoft-SSH"
        set internet-service-id 4587526
    next
    edit "MuleSoft-FTP"
        set internet-service-id 4587527
    next
    edit "MuleSoft-NTP"
        set internet-service-id 4587528
    next
    edit "MuleSoft-Inbound_Email"
        set internet-service-id 4587529
    next
    edit "MuleSoft-LDAP"
        set internet-service-id 4587534
    next
    edit "MuleSoft-NetBIOS.Session.Service"
        set internet-service-id 4587535
    next
    edit "MuleSoft-RTMP"
        set internet-service-id 4587536
    next
    edit "MuleSoft-NetBIOS.Name.Service"
        set internet-service-id 4587544
    next
    edit "Cornerstone-Other"
        set internet-service-id 4653056
    next
    edit "Cornerstone-Web"
        set internet-service-id 4653057
    next
    edit "Cornerstone-ICMP"
        set internet-service-id 4653058
    next
    edit "Cornerstone-DNS"
        set internet-service-id 4653059
    next
    edit "Cornerstone-Outbound_Email"
        set internet-service-id 4653060
    next
    edit "Cornerstone-SSH"
        set internet-service-id 4653062
    next
    edit "Cornerstone-FTP"
        set internet-service-id 4653063
    next
    edit "Cornerstone-NTP"
        set internet-service-id 4653064
    next
    edit "Cornerstone-Inbound_Email"
        set internet-service-id 4653065
    next
    edit "Cornerstone-LDAP"
        set internet-service-id 4653070
    next
    edit "Cornerstone-NetBIOS.Session.Service"
        set internet-service-id 4653071
    next
    edit "Cornerstone-RTMP"
        set internet-service-id 4653072
    next
    edit "Cornerstone-NetBIOS.Name.Service"
        set internet-service-id 4653080
    next
    edit "Eventbrite-Other"
        set internet-service-id 4718592
    next
    edit "Eventbrite-Web"
        set internet-service-id 4718593
    next
    edit "Eventbrite-ICMP"
        set internet-service-id 4718594
    next
    edit "Eventbrite-DNS"
        set internet-service-id 4718595
    next
    edit "Eventbrite-Outbound_Email"
        set internet-service-id 4718596
    next
    edit "Eventbrite-SSH"
        set internet-service-id 4718598
    next
    edit "Eventbrite-FTP"
        set internet-service-id 4718599
    next
    edit "Eventbrite-NTP"
        set internet-service-id 4718600
    next
    edit "Eventbrite-Inbound_Email"
        set internet-service-id 4718601
    next
    edit "Eventbrite-LDAP"
        set internet-service-id 4718606
    next
    edit "Eventbrite-NetBIOS.Session.Service"
        set internet-service-id 4718607
    next
    edit "Eventbrite-RTMP"
        set internet-service-id 4718608
    next
    edit "Eventbrite-NetBIOS.Name.Service"
        set internet-service-id 4718616
    next
    edit "Paychex-Other"
        set internet-service-id 4784128
    next
    edit "Paychex-Web"
        set internet-service-id 4784129
    next
    edit "Paychex-ICMP"
        set internet-service-id 4784130
    next
    edit "Paychex-DNS"
        set internet-service-id 4784131
    next
    edit "Paychex-Outbound_Email"
        set internet-service-id 4784132
    next
    edit "Paychex-SSH"
        set internet-service-id 4784134
    next
    edit "Paychex-FTP"
        set internet-service-id 4784135
    next
    edit "Paychex-NTP"
        set internet-service-id 4784136
    next
    edit "Paychex-Inbound_Email"
        set internet-service-id 4784137
    next
    edit "Paychex-LDAP"
        set internet-service-id 4784142
    next
    edit "Paychex-NetBIOS.Session.Service"
        set internet-service-id 4784143
    next
    edit "Paychex-RTMP"
        set internet-service-id 4784144
    next
    edit "Paychex-NetBIOS.Name.Service"
        set internet-service-id 4784152
    next
    edit "NewRelic-Other"
        set internet-service-id 4849664
    next
    edit "NewRelic-Web"
        set internet-service-id 4849665
    next
    edit "NewRelic-ICMP"
        set internet-service-id 4849666
    next
    edit "NewRelic-DNS"
        set internet-service-id 4849667
    next
    edit "NewRelic-Outbound_Email"
        set internet-service-id 4849668
    next
    edit "NewRelic-SSH"
        set internet-service-id 4849670
    next
    edit "NewRelic-FTP"
        set internet-service-id 4849671
    next
    edit "NewRelic-NTP"
        set internet-service-id 4849672
    next
    edit "NewRelic-Inbound_Email"
        set internet-service-id 4849673
    next
    edit "NewRelic-LDAP"
        set internet-service-id 4849678
    next
    edit "NewRelic-NetBIOS.Session.Service"
        set internet-service-id 4849679
    next
    edit "NewRelic-RTMP"
        set internet-service-id 4849680
    next
    edit "NewRelic-NetBIOS.Name.Service"
        set internet-service-id 4849688
    next
    edit "NewRelic-Synthetic.Monitor"
        set internet-service-id 4849970
    next
    edit "Splunk-Other"
        set internet-service-id 4915200
    next
    edit "Splunk-Web"
        set internet-service-id 4915201
    next
    edit "Splunk-ICMP"
        set internet-service-id 4915202
    next
    edit "Splunk-DNS"
        set internet-service-id 4915203
    next
    edit "Splunk-Outbound_Email"
        set internet-service-id 4915204
    next
    edit "Splunk-SSH"
        set internet-service-id 4915206
    next
    edit "Splunk-FTP"
        set internet-service-id 4915207
    next
    edit "Splunk-NTP"
        set internet-service-id 4915208
    next
    edit "Splunk-Inbound_Email"
        set internet-service-id 4915209
    next
    edit "Splunk-LDAP"
        set internet-service-id 4915214
    next
    edit "Splunk-NetBIOS.Session.Service"
        set internet-service-id 4915215
    next
    edit "Splunk-RTMP"
        set internet-service-id 4915216
    next
    edit "Splunk-NetBIOS.Name.Service"
        set internet-service-id 4915224
    next
    edit "Domo-Other"
        set internet-service-id 4980736
    next
    edit "Domo-Web"
        set internet-service-id 4980737
    next
    edit "Domo-ICMP"
        set internet-service-id 4980738
    next
    edit "Domo-DNS"
        set internet-service-id 4980739
    next
    edit "Domo-Outbound_Email"
        set internet-service-id 4980740
    next
    edit "Domo-SSH"
        set internet-service-id 4980742
    next
    edit "Domo-FTP"
        set internet-service-id 4980743
    next
    edit "Domo-NTP"
        set internet-service-id 4980744
    next
    edit "Domo-Inbound_Email"
        set internet-service-id 4980745
    next
    edit "Domo-LDAP"
        set internet-service-id 4980750
    next
    edit "Domo-NetBIOS.Session.Service"
        set internet-service-id 4980751
    next
    edit "Domo-RTMP"
        set internet-service-id 4980752
    next
    edit "Domo-NetBIOS.Name.Service"
        set internet-service-id 4980760
    next
    edit "FreshBooks-Other"
        set internet-service-id 5046272
    next
    edit "FreshBooks-Web"
        set internet-service-id 5046273
    next
    edit "FreshBooks-ICMP"
        set internet-service-id 5046274
    next
    edit "FreshBooks-DNS"
        set internet-service-id 5046275
    next
    edit "FreshBooks-Outbound_Email"
        set internet-service-id 5046276
    next
    edit "FreshBooks-SSH"
        set internet-service-id 5046278
    next
    edit "FreshBooks-FTP"
        set internet-service-id 5046279
    next
    edit "FreshBooks-NTP"
        set internet-service-id 5046280
    next
    edit "FreshBooks-Inbound_Email"
        set internet-service-id 5046281
    next
    edit "FreshBooks-LDAP"
        set internet-service-id 5046286
    next
    edit "FreshBooks-NetBIOS.Session.Service"
        set internet-service-id 5046287
    next
    edit "FreshBooks-RTMP"
        set internet-service-id 5046288
    next
    edit "FreshBooks-NetBIOS.Name.Service"
        set internet-service-id 5046296
    next
    edit "Tableau-Other"
        set internet-service-id 5111808
    next
    edit "Tableau-Web"
        set internet-service-id 5111809
    next
    edit "Tableau-ICMP"
        set internet-service-id 5111810
    next
    edit "Tableau-DNS"
        set internet-service-id 5111811
    next
    edit "Tableau-Outbound_Email"
        set internet-service-id 5111812
    next
    edit "Tableau-SSH"
        set internet-service-id 5111814
    next
    edit "Tableau-FTP"
        set internet-service-id 5111815
    next
    edit "Tableau-NTP"
        set internet-service-id 5111816
    next
    edit "Tableau-Inbound_Email"
        set internet-service-id 5111817
    next
    edit "Tableau-LDAP"
        set internet-service-id 5111822
    next
    edit "Tableau-NetBIOS.Session.Service"
        set internet-service-id 5111823
    next
    edit "Tableau-RTMP"
        set internet-service-id 5111824
    next
    edit "Tableau-NetBIOS.Name.Service"
        set internet-service-id 5111832
    next
    edit "Druva-Other"
        set internet-service-id 5177344
    next
    edit "Druva-Web"
        set internet-service-id 5177345
    next
    edit "Druva-ICMP"
        set internet-service-id 5177346
    next
    edit "Druva-DNS"
        set internet-service-id 5177347
    next
    edit "Druva-Outbound_Email"
        set internet-service-id 5177348
    next
    edit "Druva-SSH"
        set internet-service-id 5177350
    next
    edit "Druva-FTP"
        set internet-service-id 5177351
    next
    edit "Druva-NTP"
        set internet-service-id 5177352
    next
    edit "Druva-Inbound_Email"
        set internet-service-id 5177353
    next
    edit "Druva-LDAP"
        set internet-service-id 5177358
    next
    edit "Druva-NetBIOS.Session.Service"
        set internet-service-id 5177359
    next
    edit "Druva-RTMP"
        set internet-service-id 5177360
    next
    edit "Druva-NetBIOS.Name.Service"
        set internet-service-id 5177368
    next
    edit "Act-on-Other"
        set internet-service-id 5242880
    next
    edit "Act-on-Web"
        set internet-service-id 5242881
    next
    edit "Act-on-ICMP"
        set internet-service-id 5242882
    next
    edit "Act-on-DNS"
        set internet-service-id 5242883
    next
    edit "Act-on-Outbound_Email"
        set internet-service-id 5242884
    next
    edit "Act-on-SSH"
        set internet-service-id 5242886
    next
    edit "Act-on-FTP"
        set internet-service-id 5242887
    next
    edit "Act-on-NTP"
        set internet-service-id 5242888
    next
    edit "Act-on-Inbound_Email"
        set internet-service-id 5242889
    next
    edit "Act-on-LDAP"
        set internet-service-id 5242894
    next
    edit "Act-on-NetBIOS.Session.Service"
        set internet-service-id 5242895
    next
    edit "Act-on-RTMP"
        set internet-service-id 5242896
    next
    edit "Act-on-NetBIOS.Name.Service"
        set internet-service-id 5242904
    next
    edit "GoodData-Other"
        set internet-service-id 5308416
    next
    edit "GoodData-Web"
        set internet-service-id 5308417
    next
    edit "GoodData-ICMP"
        set internet-service-id 5308418
    next
    edit "GoodData-DNS"
        set internet-service-id 5308419
    next
    edit "GoodData-Outbound_Email"
        set internet-service-id 5308420
    next
    edit "GoodData-SSH"
        set internet-service-id 5308422
    next
    edit "GoodData-FTP"
        set internet-service-id 5308423
    next
    edit "GoodData-NTP"
        set internet-service-id 5308424
    next
    edit "GoodData-Inbound_Email"
        set internet-service-id 5308425
    next
    edit "GoodData-LDAP"
        set internet-service-id 5308430
    next
    edit "GoodData-NetBIOS.Session.Service"
        set internet-service-id 5308431
    next
    edit "GoodData-RTMP"
        set internet-service-id 5308432
    next
    edit "GoodData-NetBIOS.Name.Service"
        set internet-service-id 5308440
    next
    edit "SurveyMonkey-Other"
        set internet-service-id 5373952
    next
    edit "SurveyMonkey-Web"
        set internet-service-id 5373953
    next
    edit "SurveyMonkey-ICMP"
        set internet-service-id 5373954
    next
    edit "SurveyMonkey-DNS"
        set internet-service-id 5373955
    next
    edit "SurveyMonkey-Outbound_Email"
        set internet-service-id 5373956
    next
    edit "SurveyMonkey-SSH"
        set internet-service-id 5373958
    next
    edit "SurveyMonkey-FTP"
        set internet-service-id 5373959
    next
    edit "SurveyMonkey-NTP"
        set internet-service-id 5373960
    next
    edit "SurveyMonkey-Inbound_Email"
        set internet-service-id 5373961
    next
    edit "SurveyMonkey-LDAP"
        set internet-service-id 5373966
    next
    edit "SurveyMonkey-NetBIOS.Session.Service"
        set internet-service-id 5373967
    next
    edit "SurveyMonkey-RTMP"
        set internet-service-id 5373968
    next
    edit "SurveyMonkey-NetBIOS.Name.Service"
        set internet-service-id 5373976
    next
    edit "Cvent-Other"
        set internet-service-id 5439488
    next
    edit "Cvent-Web"
        set internet-service-id 5439489
    next
    edit "Cvent-ICMP"
        set internet-service-id 5439490
    next
    edit "Cvent-DNS"
        set internet-service-id 5439491
    next
    edit "Cvent-Outbound_Email"
        set internet-service-id 5439492
    next
    edit "Cvent-SSH"
        set internet-service-id 5439494
    next
    edit "Cvent-FTP"
        set internet-service-id 5439495
    next
    edit "Cvent-NTP"
        set internet-service-id 5439496
    next
    edit "Cvent-Inbound_Email"
        set internet-service-id 5439497
    next
    edit "Cvent-LDAP"
        set internet-service-id 5439502
    next
    edit "Cvent-NetBIOS.Session.Service"
        set internet-service-id 5439503
    next
    edit "Cvent-RTMP"
        set internet-service-id 5439504
    next
    edit "Cvent-NetBIOS.Name.Service"
        set internet-service-id 5439512
    next
    edit "Blackbaud-Other"
        set internet-service-id 5505024
    next
    edit "Blackbaud-Web"
        set internet-service-id 5505025
    next
    edit "Blackbaud-ICMP"
        set internet-service-id 5505026
    next
    edit "Blackbaud-DNS"
        set internet-service-id 5505027
    next
    edit "Blackbaud-Outbound_Email"
        set internet-service-id 5505028
    next
    edit "Blackbaud-SSH"
        set internet-service-id 5505030
    next
    edit "Blackbaud-FTP"
        set internet-service-id 5505031
    next
    edit "Blackbaud-NTP"
        set internet-service-id 5505032
    next
    edit "Blackbaud-Inbound_Email"
        set internet-service-id 5505033
    next
    edit "Blackbaud-LDAP"
        set internet-service-id 5505038
    next
    edit "Blackbaud-NetBIOS.Session.Service"
        set internet-service-id 5505039
    next
    edit "Blackbaud-RTMP"
        set internet-service-id 5505040
    next
    edit "Blackbaud-NetBIOS.Name.Service"
        set internet-service-id 5505048
    next
    edit "InsideSales-Other"
        set internet-service-id 5570560
    next
    edit "InsideSales-Web"
        set internet-service-id 5570561
    next
    edit "InsideSales-ICMP"
        set internet-service-id 5570562
    next
    edit "InsideSales-DNS"
        set internet-service-id 5570563
    next
    edit "InsideSales-Outbound_Email"
        set internet-service-id 5570564
    next
    edit "InsideSales-SSH"
        set internet-service-id 5570566
    next
    edit "InsideSales-FTP"
        set internet-service-id 5570567
    next
    edit "InsideSales-NTP"
        set internet-service-id 5570568
    next
    edit "InsideSales-Inbound_Email"
        set internet-service-id 5570569
    next
    edit "InsideSales-LDAP"
        set internet-service-id 5570574
    next
    edit "InsideSales-NetBIOS.Session.Service"
        set internet-service-id 5570575
    next
    edit "InsideSales-RTMP"
        set internet-service-id 5570576
    next
    edit "InsideSales-NetBIOS.Name.Service"
        set internet-service-id 5570584
    next
    edit "ServiceMax-Other"
        set internet-service-id 5636096
    next
    edit "ServiceMax-Web"
        set internet-service-id 5636097
    next
    edit "ServiceMax-ICMP"
        set internet-service-id 5636098
    next
    edit "ServiceMax-DNS"
        set internet-service-id 5636099
    next
    edit "ServiceMax-Outbound_Email"
        set internet-service-id 5636100
    next
    edit "ServiceMax-SSH"
        set internet-service-id 5636102
    next
    edit "ServiceMax-FTP"
        set internet-service-id 5636103
    next
    edit "ServiceMax-NTP"
        set internet-service-id 5636104
    next
    edit "ServiceMax-Inbound_Email"
        set internet-service-id 5636105
    next
    edit "ServiceMax-LDAP"
        set internet-service-id 5636110
    next
    edit "ServiceMax-NetBIOS.Session.Service"
        set internet-service-id 5636111
    next
    edit "ServiceMax-RTMP"
        set internet-service-id 5636112
    next
    edit "ServiceMax-NetBIOS.Name.Service"
        set internet-service-id 5636120
    next
    edit "Apptio-Other"
        set internet-service-id 5701632
    next
    edit "Apptio-Web"
        set internet-service-id 5701633
    next
    edit "Apptio-ICMP"
        set internet-service-id 5701634
    next
    edit "Apptio-DNS"
        set internet-service-id 5701635
    next
    edit "Apptio-Outbound_Email"
        set internet-service-id 5701636
    next
    edit "Apptio-SSH"
        set internet-service-id 5701638
    next
    edit "Apptio-FTP"
        set internet-service-id 5701639
    next
    edit "Apptio-NTP"
        set internet-service-id 5701640
    next
    edit "Apptio-Inbound_Email"
        set internet-service-id 5701641
    next
    edit "Apptio-LDAP"
        set internet-service-id 5701646
    next
    edit "Apptio-NetBIOS.Session.Service"
        set internet-service-id 5701647
    next
    edit "Apptio-RTMP"
        set internet-service-id 5701648
    next
    edit "Apptio-NetBIOS.Name.Service"
        set internet-service-id 5701656
    next
    edit "Veracode-Other"
        set internet-service-id 5767168
    next
    edit "Veracode-Web"
        set internet-service-id 5767169
    next
    edit "Veracode-ICMP"
        set internet-service-id 5767170
    next
    edit "Veracode-DNS"
        set internet-service-id 5767171
    next
    edit "Veracode-Outbound_Email"
        set internet-service-id 5767172
    next
    edit "Veracode-SSH"
        set internet-service-id 5767174
    next
    edit "Veracode-FTP"
        set internet-service-id 5767175
    next
    edit "Veracode-NTP"
        set internet-service-id 5767176
    next
    edit "Veracode-Inbound_Email"
        set internet-service-id 5767177
    next
    edit "Veracode-LDAP"
        set internet-service-id 5767182
    next
    edit "Veracode-NetBIOS.Session.Service"
        set internet-service-id 5767183
    next
    edit "Veracode-RTMP"
        set internet-service-id 5767184
    next
    edit "Veracode-NetBIOS.Name.Service"
        set internet-service-id 5767192
    next
    edit "Anaplan-Other"
        set internet-service-id 5832704
    next
    edit "Anaplan-Web"
        set internet-service-id 5832705
    next
    edit "Anaplan-ICMP"
        set internet-service-id 5832706
    next
    edit "Anaplan-DNS"
        set internet-service-id 5832707
    next
    edit "Anaplan-Outbound_Email"
        set internet-service-id 5832708
    next
    edit "Anaplan-SSH"
        set internet-service-id 5832710
    next
    edit "Anaplan-FTP"
        set internet-service-id 5832711
    next
    edit "Anaplan-NTP"
        set internet-service-id 5832712
    next
    edit "Anaplan-Inbound_Email"
        set internet-service-id 5832713
    next
    edit "Anaplan-LDAP"
        set internet-service-id 5832718
    next
    edit "Anaplan-NetBIOS.Session.Service"
        set internet-service-id 5832719
    next
    edit "Anaplan-RTMP"
        set internet-service-id 5832720
    next
    edit "Anaplan-NetBIOS.Name.Service"
        set internet-service-id 5832728
    next
    edit "Rapid7-Other"
        set internet-service-id 5898240
    next
    edit "Rapid7-Web"
        set internet-service-id 5898241
    next
    edit "Rapid7-ICMP"
        set internet-service-id 5898242
    next
    edit "Rapid7-DNS"
        set internet-service-id 5898243
    next
    edit "Rapid7-Outbound_Email"
        set internet-service-id 5898244
    next
    edit "Rapid7-SSH"
        set internet-service-id 5898246
    next
    edit "Rapid7-FTP"
        set internet-service-id 5898247
    next
    edit "Rapid7-NTP"
        set internet-service-id 5898248
    next
    edit "Rapid7-Inbound_Email"
        set internet-service-id 5898249
    next
    edit "Rapid7-LDAP"
        set internet-service-id 5898254
    next
    edit "Rapid7-NetBIOS.Session.Service"
        set internet-service-id 5898255
    next
    edit "Rapid7-RTMP"
        set internet-service-id 5898256
    next
    edit "Rapid7-NetBIOS.Name.Service"
        set internet-service-id 5898264
    next
    edit "Rapid7-Scanner"
        set internet-service-id 5898406
    next
    edit "AnyDesk-AnyDesk"
        set internet-service-id 5963927
    next
    edit "ESET-Eset.Service"
        set internet-service-id 6029426
    next
    edit "Slack-Other"
        set internet-service-id 6094848
    next
    edit "Slack-Web"
        set internet-service-id 6094849
    next
    edit "Slack-ICMP"
        set internet-service-id 6094850
    next
    edit "Slack-DNS"
        set internet-service-id 6094851
    next
    edit "Slack-Outbound_Email"
        set internet-service-id 6094852
    next
    edit "Slack-SSH"
        set internet-service-id 6094854
    next
    edit "Slack-FTP"
        set internet-service-id 6094855
    next
    edit "Slack-NTP"
        set internet-service-id 6094856
    next
    edit "Slack-Inbound_Email"
        set internet-service-id 6094857
    next
    edit "Slack-LDAP"
        set internet-service-id 6094862
    next
    edit "Slack-NetBIOS.Session.Service"
        set internet-service-id 6094863
    next
    edit "Slack-RTMP"
        set internet-service-id 6094864
    next
    edit "Slack-NetBIOS.Name.Service"
        set internet-service-id 6094872
    next
    edit "Slack-Slack"
        set internet-service-id 6095024
    next
    edit "ADP-Other"
        set internet-service-id 6160384
    next
    edit "ADP-Web"
        set internet-service-id 6160385
    next
    edit "ADP-ICMP"
        set internet-service-id 6160386
    next
    edit "ADP-DNS"
        set internet-service-id 6160387
    next
    edit "ADP-Outbound_Email"
        set internet-service-id 6160388
    next
    edit "ADP-SSH"
        set internet-service-id 6160390
    next
    edit "ADP-FTP"
        set internet-service-id 6160391
    next
    edit "ADP-NTP"
        set internet-service-id 6160392
    next
    edit "ADP-Inbound_Email"
        set internet-service-id 6160393
    next
    edit "ADP-LDAP"
        set internet-service-id 6160398
    next
    edit "ADP-NetBIOS.Session.Service"
        set internet-service-id 6160399
    next
    edit "ADP-RTMP"
        set internet-service-id 6160400
    next
    edit "ADP-NetBIOS.Name.Service"
        set internet-service-id 6160408
    next
    edit "Blackboard-Other"
        set internet-service-id 6225920
    next
    edit "Blackboard-Web"
        set internet-service-id 6225921
    next
    edit "Blackboard-ICMP"
        set internet-service-id 6225922
    next
    edit "Blackboard-DNS"
        set internet-service-id 6225923
    next
    edit "Blackboard-Outbound_Email"
        set internet-service-id 6225924
    next
    edit "Blackboard-SSH"
        set internet-service-id 6225926
    next
    edit "Blackboard-FTP"
        set internet-service-id 6225927
    next
    edit "Blackboard-NTP"
        set internet-service-id 6225928
    next
    edit "Blackboard-Inbound_Email"
        set internet-service-id 6225929
    next
    edit "Blackboard-LDAP"
        set internet-service-id 6225934
    next
    edit "Blackboard-NetBIOS.Session.Service"
        set internet-service-id 6225935
    next
    edit "Blackboard-RTMP"
        set internet-service-id 6225936
    next
    edit "Blackboard-NetBIOS.Name.Service"
        set internet-service-id 6225944
    next
    edit "SAP-Other"
        set internet-service-id 6291456
    next
    edit "SAP-Web"
        set internet-service-id 6291457
    next
    edit "SAP-ICMP"
        set internet-service-id 6291458
    next
    edit "SAP-DNS"
        set internet-service-id 6291459
    next
    edit "SAP-Outbound_Email"
        set internet-service-id 6291460
    next
    edit "SAP-SSH"
        set internet-service-id 6291462
    next
    edit "SAP-FTP"
        set internet-service-id 6291463
    next
    edit "SAP-NTP"
        set internet-service-id 6291464
    next
    edit "SAP-Inbound_Email"
        set internet-service-id 6291465
    next
    edit "SAP-LDAP"
        set internet-service-id 6291470
    next
    edit "SAP-NetBIOS.Session.Service"
        set internet-service-id 6291471
    next
    edit "SAP-RTMP"
        set internet-service-id 6291472
    next
    edit "SAP-NetBIOS.Name.Service"
        set internet-service-id 6291480
    next
    edit "SAP-HANA"
        set internet-service-id 6291612
    next
    edit "SAP-SuccessFactors"
        set internet-service-id 6291618
    next
    edit "SAP-SAP.Ariba"
        set internet-service-id 6291766
    next
    edit "Snap-Snapchat"
        set internet-service-id 6357108
    next
    edit "Zoom.us-Zoom.Meeting"
        set internet-service-id 6422646
    next
    edit "Sophos-Other"
        set internet-service-id 6488064
    next
    edit "Sophos-Web"
        set internet-service-id 6488065
    next
    edit "Sophos-ICMP"
        set internet-service-id 6488066
    next
    edit "Sophos-DNS"
        set internet-service-id 6488067
    next
    edit "Sophos-Outbound_Email"
        set internet-service-id 6488068
    next
    edit "Sophos-SSH"
        set internet-service-id 6488070
    next
    edit "Sophos-FTP"
        set internet-service-id 6488071
    next
    edit "Sophos-NTP"
        set internet-service-id 6488072
    next
    edit "Sophos-Inbound_Email"
        set internet-service-id 6488073
    next
    edit "Sophos-LDAP"
        set internet-service-id 6488078
    next
    edit "Sophos-NetBIOS.Session.Service"
        set internet-service-id 6488079
    next
    edit "Sophos-RTMP"
        set internet-service-id 6488080
    next
    edit "Sophos-NetBIOS.Name.Service"
        set internet-service-id 6488088
    next
    edit "Cloudflare-Other"
        set internet-service-id 6553600
    next
    edit "Cloudflare-Web"
        set internet-service-id 6553601
    next
    edit "Cloudflare-ICMP"
        set internet-service-id 6553602
    next
    edit "Cloudflare-DNS"
        set internet-service-id 6553603
    next
    edit "Cloudflare-Outbound_Email"
        set internet-service-id 6553604
    next
    edit "Cloudflare-SSH"
        set internet-service-id 6553606
    next
    edit "Cloudflare-FTP"
        set internet-service-id 6553607
    next
    edit "Cloudflare-NTP"
        set internet-service-id 6553608
    next
    edit "Cloudflare-Inbound_Email"
        set internet-service-id 6553609
    next
    edit "Cloudflare-LDAP"
        set internet-service-id 6553614
    next
    edit "Cloudflare-NetBIOS.Session.Service"
        set internet-service-id 6553615
    next
    edit "Cloudflare-RTMP"
        set internet-service-id 6553616
    next
    edit "Cloudflare-NetBIOS.Name.Service"
        set internet-service-id 6553624
    next
    edit "Cloudflare-CDN"
        set internet-service-id 6553737
    next
    edit "Pexip-Pexip.Meeting"
        set internet-service-id 6619256
    next
    edit "Zscaler-Other"
        set internet-service-id 6684672
    next
    edit "Zscaler-Web"
        set internet-service-id 6684673
    next
    edit "Zscaler-ICMP"
        set internet-service-id 6684674
    next
    edit "Zscaler-DNS"
        set internet-service-id 6684675
    next
    edit "Zscaler-Outbound_Email"
        set internet-service-id 6684676
    next
    edit "Zscaler-SSH"
        set internet-service-id 6684678
    next
    edit "Zscaler-FTP"
        set internet-service-id 6684679
    next
    edit "Zscaler-NTP"
        set internet-service-id 6684680
    next
    edit "Zscaler-Inbound_Email"
        set internet-service-id 6684681
    next
    edit "Zscaler-LDAP"
        set internet-service-id 6684686
    next
    edit "Zscaler-NetBIOS.Session.Service"
        set internet-service-id 6684687
    next
    edit "Zscaler-RTMP"
        set internet-service-id 6684688
    next
    edit "Zscaler-NetBIOS.Name.Service"
        set internet-service-id 6684696
    next
    edit "Zscaler-Zscaler.Cloud"
        set internet-service-id 6684793
    next
    edit "Yandex-Other"
        set internet-service-id 6750208
    next
    edit "Yandex-Web"
        set internet-service-id 6750209
    next
    edit "Yandex-ICMP"
        set internet-service-id 6750210
    next
    edit "Yandex-DNS"
        set internet-service-id 6750211
    next
    edit "Yandex-Outbound_Email"
        set internet-service-id 6750212
    next
    edit "Yandex-SSH"
        set internet-service-id 6750214
    next
    edit "Yandex-FTP"
        set internet-service-id 6750215
    next
    edit "Yandex-NTP"
        set internet-service-id 6750216
    next
    edit "Yandex-Inbound_Email"
        set internet-service-id 6750217
    next
    edit "Yandex-LDAP"
        set internet-service-id 6750222
    next
    edit "Yandex-NetBIOS.Session.Service"
        set internet-service-id 6750223
    next
    edit "Yandex-RTMP"
        set internet-service-id 6750224
    next
    edit "Yandex-NetBIOS.Name.Service"
        set internet-service-id 6750232
    next
    edit "mail.ru-Other"
        set internet-service-id 6815744
    next
    edit "mail.ru-Web"
        set internet-service-id 6815745
    next
    edit "mail.ru-ICMP"
        set internet-service-id 6815746
    next
    edit "mail.ru-DNS"
        set internet-service-id 6815747
    next
    edit "mail.ru-Outbound_Email"
        set internet-service-id 6815748
    next
    edit "mail.ru-SSH"
        set internet-service-id 6815750
    next
    edit "mail.ru-FTP"
        set internet-service-id 6815751
    next
    edit "mail.ru-NTP"
        set internet-service-id 6815752
    next
    edit "mail.ru-Inbound_Email"
        set internet-service-id 6815753
    next
    edit "mail.ru-LDAP"
        set internet-service-id 6815758
    next
    edit "mail.ru-NetBIOS.Session.Service"
        set internet-service-id 6815759
    next
    edit "mail.ru-RTMP"
        set internet-service-id 6815760
    next
    edit "mail.ru-NetBIOS.Name.Service"
        set internet-service-id 6815768
    next
    edit "Alibaba-Other"
        set internet-service-id 6881280
    next
    edit "Alibaba-Web"
        set internet-service-id 6881281
    next
    edit "Alibaba-ICMP"
        set internet-service-id 6881282
    next
    edit "Alibaba-DNS"
        set internet-service-id 6881283
    next
    edit "Alibaba-Outbound_Email"
        set internet-service-id 6881284
    next
    edit "Alibaba-SSH"
        set internet-service-id 6881286
    next
    edit "Alibaba-FTP"
        set internet-service-id 6881287
    next
    edit "Alibaba-NTP"
        set internet-service-id 6881288
    next
    edit "Alibaba-Inbound_Email"
        set internet-service-id 6881289
    next
    edit "Alibaba-LDAP"
        set internet-service-id 6881294
    next
    edit "Alibaba-NetBIOS.Session.Service"
        set internet-service-id 6881295
    next
    edit "Alibaba-RTMP"
        set internet-service-id 6881296
    next
    edit "Alibaba-NetBIOS.Name.Service"
        set internet-service-id 6881304
    next
    edit "Alibaba-Alibaba.Cloud"
        set internet-service-id 6881402
    next
    edit "Alibaba-DingTalk"
        set internet-service-id 6881623
    next
    edit "GoDaddy-Other"
        set internet-service-id 6946816
    next
    edit "GoDaddy-Web"
        set internet-service-id 6946817
    next
    edit "GoDaddy-ICMP"
        set internet-service-id 6946818
    next
    edit "GoDaddy-DNS"
        set internet-service-id 6946819
    next
    edit "GoDaddy-Outbound_Email"
        set internet-service-id 6946820
    next
    edit "GoDaddy-SSH"
        set internet-service-id 6946822
    next
    edit "GoDaddy-FTP"
        set internet-service-id 6946823
    next
    edit "GoDaddy-NTP"
        set internet-service-id 6946824
    next
    edit "GoDaddy-Inbound_Email"
        set internet-service-id 6946825
    next
    edit "GoDaddy-LDAP"
        set internet-service-id 6946830
    next
    edit "GoDaddy-NetBIOS.Session.Service"
        set internet-service-id 6946831
    next
    edit "GoDaddy-RTMP"
        set internet-service-id 6946832
    next
    edit "GoDaddy-NetBIOS.Name.Service"
        set internet-service-id 6946840
    next
    edit "GoDaddy-GoDaddy.Email"
        set internet-service-id 6946939
    next
    edit "Bluejeans-Bluejeans.Meeting"
        set internet-service-id 7012476
    next
    edit "Webroot-Webroot.SecureAnywhere"
        set internet-service-id 7078013
    next
    edit "Avast-Other"
        set internet-service-id 7143424
    next
    edit "Avast-Web"
        set internet-service-id 7143425
    next
    edit "Avast-ICMP"
        set internet-service-id 7143426
    next
    edit "Avast-DNS"
        set internet-service-id 7143427
    next
    edit "Avast-Outbound_Email"
        set internet-service-id 7143428
    next
    edit "Avast-SSH"
        set internet-service-id 7143430
    next
    edit "Avast-FTP"
        set internet-service-id 7143431
    next
    edit "Avast-NTP"
        set internet-service-id 7143432
    next
    edit "Avast-Inbound_Email"
        set internet-service-id 7143433
    next
    edit "Avast-LDAP"
        set internet-service-id 7143438
    next
    edit "Avast-NetBIOS.Session.Service"
        set internet-service-id 7143439
    next
    edit "Avast-RTMP"
        set internet-service-id 7143440
    next
    edit "Avast-NetBIOS.Name.Service"
        set internet-service-id 7143448
    next
    edit "Avast-Avast.Security"
        set internet-service-id 7143550
    next
    edit "Wetransfer-Other"
        set internet-service-id 7208960
    next
    edit "Wetransfer-Web"
        set internet-service-id 7208961
    next
    edit "Wetransfer-ICMP"
        set internet-service-id 7208962
    next
    edit "Wetransfer-DNS"
        set internet-service-id 7208963
    next
    edit "Wetransfer-Outbound_Email"
        set internet-service-id 7208964
    next
    edit "Wetransfer-SSH"
        set internet-service-id 7208966
    next
    edit "Wetransfer-FTP"
        set internet-service-id 7208967
    next
    edit "Wetransfer-NTP"
        set internet-service-id 7208968
    next
    edit "Wetransfer-Inbound_Email"
        set internet-service-id 7208969
    next
    edit "Wetransfer-LDAP"
        set internet-service-id 7208974
    next
    edit "Wetransfer-NetBIOS.Session.Service"
        set internet-service-id 7208975
    next
    edit "Wetransfer-RTMP"
        set internet-service-id 7208976
    next
    edit "Wetransfer-NetBIOS.Name.Service"
        set internet-service-id 7208984
    next
    edit "Sendgrid-Sendgrid.Email"
        set internet-service-id 7274623
    next
    edit "Ubiquiti-UniFi"
        set internet-service-id 7340160
    next
    edit "Lifesize-Lifesize.Cloud"
        set internet-service-id 7405697
    next
    edit "Okta-Other"
        set internet-service-id 7471104
    next
    edit "Okta-Web"
        set internet-service-id 7471105
    next
    edit "Okta-ICMP"
        set internet-service-id 7471106
    next
    edit "Okta-DNS"
        set internet-service-id 7471107
    next
    edit "Okta-Outbound_Email"
        set internet-service-id 7471108
    next
    edit "Okta-SSH"
        set internet-service-id 7471110
    next
    edit "Okta-FTP"
        set internet-service-id 7471111
    next
    edit "Okta-NTP"
        set internet-service-id 7471112
    next
    edit "Okta-Inbound_Email"
        set internet-service-id 7471113
    next
    edit "Okta-LDAP"
        set internet-service-id 7471118
    next
    edit "Okta-NetBIOS.Session.Service"
        set internet-service-id 7471119
    next
    edit "Okta-RTMP"
        set internet-service-id 7471120
    next
    edit "Okta-NetBIOS.Name.Service"
        set internet-service-id 7471128
    next
    edit "Okta-Okta"
        set internet-service-id 7471307
    next
    edit "Cybozu-Other"
        set internet-service-id 7536640
    next
    edit "Cybozu-Web"
        set internet-service-id 7536641
    next
    edit "Cybozu-ICMP"
        set internet-service-id 7536642
    next
    edit "Cybozu-DNS"
        set internet-service-id 7536643
    next
    edit "Cybozu-Outbound_Email"
        set internet-service-id 7536644
    next
    edit "Cybozu-SSH"
        set internet-service-id 7536646
    next
    edit "Cybozu-FTP"
        set internet-service-id 7536647
    next
    edit "Cybozu-NTP"
        set internet-service-id 7536648
    next
    edit "Cybozu-Inbound_Email"
        set internet-service-id 7536649
    next
    edit "Cybozu-LDAP"
        set internet-service-id 7536654
    next
    edit "Cybozu-NetBIOS.Session.Service"
        set internet-service-id 7536655
    next
    edit "Cybozu-RTMP"
        set internet-service-id 7536656
    next
    edit "Cybozu-NetBIOS.Name.Service"
        set internet-service-id 7536664
    next
    edit "VNC-Other"
        set internet-service-id 7602176
    next
    edit "VNC-Web"
        set internet-service-id 7602177
    next
    edit "VNC-ICMP"
        set internet-service-id 7602178
    next
    edit "VNC-DNS"
        set internet-service-id 7602179
    next
    edit "VNC-Outbound_Email"
        set internet-service-id 7602180
    next
    edit "VNC-SSH"
        set internet-service-id 7602182
    next
    edit "VNC-FTP"
        set internet-service-id 7602183
    next
    edit "VNC-NTP"
        set internet-service-id 7602184
    next
    edit "VNC-Inbound_Email"
        set internet-service-id 7602185
    next
    edit "VNC-LDAP"
        set internet-service-id 7602190
    next
    edit "VNC-NetBIOS.Session.Service"
        set internet-service-id 7602191
    next
    edit "VNC-RTMP"
        set internet-service-id 7602192
    next
    edit "VNC-NetBIOS.Name.Service"
        set internet-service-id 7602200
    next
    edit "Egnyte-Egnyte"
        set internet-service-id 7667846
    next
    edit "CrowdStrike-CrowdStrike.Falcon.Cloud"
        set internet-service-id 7733383
    next
    edit "Aruba.it-Other"
        set internet-service-id 7798784
    next
    edit "Aruba.it-Web"
        set internet-service-id 7798785
    next
    edit "Aruba.it-ICMP"
        set internet-service-id 7798786
    next
    edit "Aruba.it-DNS"
        set internet-service-id 7798787
    next
    edit "Aruba.it-Outbound_Email"
        set internet-service-id 7798788
    next
    edit "Aruba.it-SSH"
        set internet-service-id 7798790
    next
    edit "Aruba.it-FTP"
        set internet-service-id 7798791
    next
    edit "Aruba.it-NTP"
        set internet-service-id 7798792
    next
    edit "Aruba.it-Inbound_Email"
        set internet-service-id 7798793
    next
    edit "Aruba.it-LDAP"
        set internet-service-id 7798798
    next
    edit "Aruba.it-NetBIOS.Session.Service"
        set internet-service-id 7798799
    next
    edit "Aruba.it-RTMP"
        set internet-service-id 7798800
    next
    edit "Aruba.it-NetBIOS.Name.Service"
        set internet-service-id 7798808
    next
    edit "ISLOnline-Other"
        set internet-service-id 7864320
    next
    edit "ISLOnline-Web"
        set internet-service-id 7864321
    next
    edit "ISLOnline-ICMP"
        set internet-service-id 7864322
    next
    edit "ISLOnline-DNS"
        set internet-service-id 7864323
    next
    edit "ISLOnline-Outbound_Email"
        set internet-service-id 7864324
    next
    edit "ISLOnline-SSH"
        set internet-service-id 7864326
    next
    edit "ISLOnline-FTP"
        set internet-service-id 7864327
    next
    edit "ISLOnline-NTP"
        set internet-service-id 7864328
    next
    edit "ISLOnline-Inbound_Email"
        set internet-service-id 7864329
    next
    edit "ISLOnline-LDAP"
        set internet-service-id 7864334
    next
    edit "ISLOnline-NetBIOS.Session.Service"
        set internet-service-id 7864335
    next
    edit "ISLOnline-RTMP"
        set internet-service-id 7864336
    next
    edit "ISLOnline-NetBIOS.Name.Service"
        set internet-service-id 7864344
    next
    edit "ISLOnline-ISLOnline"
        set internet-service-id 7864667
    next
    edit "Akamai-CDN"
        set internet-service-id 7929993
    next
    edit "Akamai-Linode.Cloud"
        set internet-service-id 7930148
    next
    edit "Rackspace-CDN"
        set internet-service-id 7995529
    next
    edit "Instart-CDN"
        set internet-service-id 8061065
    next
    edit "Bitdefender-Other"
        set internet-service-id 8126464
    next
    edit "Bitdefender-Web"
        set internet-service-id 8126465
    next
    edit "Bitdefender-ICMP"
        set internet-service-id 8126466
    next
    edit "Bitdefender-DNS"
        set internet-service-id 8126467
    next
    edit "Bitdefender-Outbound_Email"
        set internet-service-id 8126468
    next
    edit "Bitdefender-SSH"
        set internet-service-id 8126470
    next
    edit "Bitdefender-FTP"
        set internet-service-id 8126471
    next
    edit "Bitdefender-NTP"
        set internet-service-id 8126472
    next
    edit "Bitdefender-Inbound_Email"
        set internet-service-id 8126473
    next
    edit "Bitdefender-LDAP"
        set internet-service-id 8126478
    next
    edit "Bitdefender-NetBIOS.Session.Service"
        set internet-service-id 8126479
    next
    edit "Bitdefender-RTMP"
        set internet-service-id 8126480
    next
    edit "Bitdefender-NetBIOS.Name.Service"
        set internet-service-id 8126488
    next
    edit "Pingdom-Other"
        set internet-service-id 8192000
    next
    edit "Pingdom-Web"
        set internet-service-id 8192001
    next
    edit "Pingdom-ICMP"
        set internet-service-id 8192002
    next
    edit "Pingdom-DNS"
        set internet-service-id 8192003
    next
    edit "Pingdom-Outbound_Email"
        set internet-service-id 8192004
    next
    edit "Pingdom-SSH"
        set internet-service-id 8192006
    next
    edit "Pingdom-FTP"
        set internet-service-id 8192007
    next
    edit "Pingdom-NTP"
        set internet-service-id 8192008
    next
    edit "Pingdom-Inbound_Email"
        set internet-service-id 8192009
    next
    edit "Pingdom-LDAP"
        set internet-service-id 8192014
    next
    edit "Pingdom-NetBIOS.Session.Service"
        set internet-service-id 8192015
    next
    edit "Pingdom-RTMP"
        set internet-service-id 8192016
    next
    edit "Pingdom-NetBIOS.Name.Service"
        set internet-service-id 8192024
    next
    edit "UptimeRobot-Other"
        set internet-service-id 8257536
    next
    edit "UptimeRobot-Web"
        set internet-service-id 8257537
    next
    edit "UptimeRobot-ICMP"
        set internet-service-id 8257538
    next
    edit "UptimeRobot-DNS"
        set internet-service-id 8257539
    next
    edit "UptimeRobot-Outbound_Email"
        set internet-service-id 8257540
    next
    edit "UptimeRobot-SSH"
        set internet-service-id 8257542
    next
    edit "UptimeRobot-FTP"
        set internet-service-id 8257543
    next
    edit "UptimeRobot-NTP"
        set internet-service-id 8257544
    next
    edit "UptimeRobot-Inbound_Email"
        set internet-service-id 8257545
    next
    edit "UptimeRobot-LDAP"
        set internet-service-id 8257550
    next
    edit "UptimeRobot-NetBIOS.Session.Service"
        set internet-service-id 8257551
    next
    edit "UptimeRobot-RTMP"
        set internet-service-id 8257552
    next
    edit "UptimeRobot-NetBIOS.Name.Service"
        set internet-service-id 8257560
    next
    edit "UptimeRobot-UptimeRobot.Monitor"
        set internet-service-id 8257709
    next
    edit "Quovadisglobal-Other"
        set internet-service-id 8323072
    next
    edit "Quovadisglobal-Web"
        set internet-service-id 8323073
    next
    edit "Quovadisglobal-ICMP"
        set internet-service-id 8323074
    next
    edit "Quovadisglobal-DNS"
        set internet-service-id 8323075
    next
    edit "Quovadisglobal-Outbound_Email"
        set internet-service-id 8323076
    next
    edit "Quovadisglobal-SSH"
        set internet-service-id 8323078
    next
    edit "Quovadisglobal-FTP"
        set internet-service-id 8323079
    next
    edit "Quovadisglobal-NTP"
        set internet-service-id 8323080
    next
    edit "Quovadisglobal-Inbound_Email"
        set internet-service-id 8323081
    next
    edit "Quovadisglobal-LDAP"
        set internet-service-id 8323086
    next
    edit "Quovadisglobal-NetBIOS.Session.Service"
        set internet-service-id 8323087
    next
    edit "Quovadisglobal-RTMP"
        set internet-service-id 8323088
    next
    edit "Quovadisglobal-NetBIOS.Name.Service"
        set internet-service-id 8323096
    next
    edit "Splashtop-Splashtop"
        set internet-service-id 8388751
    next
    edit "Zoox-Other"
        set internet-service-id 8454144
    next
    edit "Zoox-Web"
        set internet-service-id 8454145
    next
    edit "Zoox-ICMP"
        set internet-service-id 8454146
    next
    edit "Zoox-DNS"
        set internet-service-id 8454147
    next
    edit "Zoox-Outbound_Email"
        set internet-service-id 8454148
    next
    edit "Zoox-SSH"
        set internet-service-id 8454150
    next
    edit "Zoox-FTP"
        set internet-service-id 8454151
    next
    edit "Zoox-NTP"
        set internet-service-id 8454152
    next
    edit "Zoox-Inbound_Email"
        set internet-service-id 8454153
    next
    edit "Zoox-LDAP"
        set internet-service-id 8454158
    next
    edit "Zoox-NetBIOS.Session.Service"
        set internet-service-id 8454159
    next
    edit "Zoox-RTMP"
        set internet-service-id 8454160
    next
    edit "Zoox-NetBIOS.Name.Service"
        set internet-service-id 8454168
    next
    edit "Skyfii-Other"
        set internet-service-id 8519680
    next
    edit "Skyfii-Web"
        set internet-service-id 8519681
    next
    edit "Skyfii-ICMP"
        set internet-service-id 8519682
    next
    edit "Skyfii-DNS"
        set internet-service-id 8519683
    next
    edit "Skyfii-Outbound_Email"
        set internet-service-id 8519684
    next
    edit "Skyfii-SSH"
        set internet-service-id 8519686
    next
    edit "Skyfii-FTP"
        set internet-service-id 8519687
    next
    edit "Skyfii-NTP"
        set internet-service-id 8519688
    next
    edit "Skyfii-Inbound_Email"
        set internet-service-id 8519689
    next
    edit "Skyfii-LDAP"
        set internet-service-id 8519694
    next
    edit "Skyfii-NetBIOS.Session.Service"
        set internet-service-id 8519695
    next
    edit "Skyfii-RTMP"
        set internet-service-id 8519696
    next
    edit "Skyfii-NetBIOS.Name.Service"
        set internet-service-id 8519704
    next
    edit "CoffeeBean-Other"
        set internet-service-id 8585216
    next
    edit "CoffeeBean-Web"
        set internet-service-id 8585217
    next
    edit "CoffeeBean-ICMP"
        set internet-service-id 8585218
    next
    edit "CoffeeBean-DNS"
        set internet-service-id 8585219
    next
    edit "CoffeeBean-Outbound_Email"
        set internet-service-id 8585220
    next
    edit "CoffeeBean-SSH"
        set internet-service-id 8585222
    next
    edit "CoffeeBean-FTP"
        set internet-service-id 8585223
    next
    edit "CoffeeBean-NTP"
        set internet-service-id 8585224
    next
    edit "CoffeeBean-Inbound_Email"
        set internet-service-id 8585225
    next
    edit "CoffeeBean-LDAP"
        set internet-service-id 8585230
    next
    edit "CoffeeBean-NetBIOS.Session.Service"
        set internet-service-id 8585231
    next
    edit "CoffeeBean-RTMP"
        set internet-service-id 8585232
    next
    edit "CoffeeBean-NetBIOS.Name.Service"
        set internet-service-id 8585240
    next
    edit "Cloud4Wi-Other"
        set internet-service-id 8650752
    next
    edit "Cloud4Wi-Web"
        set internet-service-id 8650753
    next
    edit "Cloud4Wi-ICMP"
        set internet-service-id 8650754
    next
    edit "Cloud4Wi-DNS"
        set internet-service-id 8650755
    next
    edit "Cloud4Wi-Outbound_Email"
        set internet-service-id 8650756
    next
    edit "Cloud4Wi-SSH"
        set internet-service-id 8650758
    next
    edit "Cloud4Wi-FTP"
        set internet-service-id 8650759
    next
    edit "Cloud4Wi-NTP"
        set internet-service-id 8650760
    next
    edit "Cloud4Wi-Inbound_Email"
        set internet-service-id 8650761
    next
    edit "Cloud4Wi-LDAP"
        set internet-service-id 8650766
    next
    edit "Cloud4Wi-NetBIOS.Session.Service"
        set internet-service-id 8650767
    next
    edit "Cloud4Wi-RTMP"
        set internet-service-id 8650768
    next
    edit "Cloud4Wi-NetBIOS.Name.Service"
        set internet-service-id 8650776
    next
    edit "Panda-Panda.Security"
        set internet-service-id 8716432
    next
    edit "Ewon-Talk2M"
        set internet-service-id 8781970
    next
    edit "Nutanix-Nutanix.Cloud"
        set internet-service-id 8847507
    next
    edit "Backblaze-Other"
        set internet-service-id 8912896
    next
    edit "Backblaze-Web"
        set internet-service-id 8912897
    next
    edit "Backblaze-ICMP"
        set internet-service-id 8912898
    next
    edit "Backblaze-DNS"
        set internet-service-id 8912899
    next
    edit "Backblaze-Outbound_Email"
        set internet-service-id 8912900
    next
    edit "Backblaze-SSH"
        set internet-service-id 8912902
    next
    edit "Backblaze-FTP"
        set internet-service-id 8912903
    next
    edit "Backblaze-NTP"
        set internet-service-id 8912904
    next
    edit "Backblaze-Inbound_Email"
        set internet-service-id 8912905
    next
    edit "Backblaze-LDAP"
        set internet-service-id 8912910
    next
    edit "Backblaze-NetBIOS.Session.Service"
        set internet-service-id 8912911
    next
    edit "Backblaze-RTMP"
        set internet-service-id 8912912
    next
    edit "Backblaze-NetBIOS.Name.Service"
        set internet-service-id 8912920
    next
    edit "Extreme-Extreme.Cloud"
        set internet-service-id 8978580
    next
    edit "XING-Other"
        set internet-service-id 9043968
    next
    edit "XING-Web"
        set internet-service-id 9043969
    next
    edit "XING-ICMP"
        set internet-service-id 9043970
    next
    edit "XING-DNS"
        set internet-service-id 9043971
    next
    edit "XING-Outbound_Email"
        set internet-service-id 9043972
    next
    edit "XING-SSH"
        set internet-service-id 9043974
    next
    edit "XING-FTP"
        set internet-service-id 9043975
    next
    edit "XING-NTP"
        set internet-service-id 9043976
    next
    edit "XING-Inbound_Email"
        set internet-service-id 9043977
    next
    edit "XING-LDAP"
        set internet-service-id 9043982
    next
    edit "XING-NetBIOS.Session.Service"
        set internet-service-id 9043983
    next
    edit "XING-RTMP"
        set internet-service-id 9043984
    next
    edit "XING-NetBIOS.Name.Service"
        set internet-service-id 9043992
    next
    edit "Genesys-PureCloud"
        set internet-service-id 9109653
    next
    edit "BlackBerry-Cylance"
        set internet-service-id 9175190
    next
    edit "DigiCert-OCSP"
        set internet-service-id 9240728
    next
    edit "Infomaniak-SwissTransfer"
        set internet-service-id 9306265
    next
    edit "Fuze-Fuze"
        set internet-service-id 9371802
    next
    edit "Truecaller-Truecaller"
        set internet-service-id 9437339
    next
    edit "GlobalSign-OCSP"
        set internet-service-id 9502872
    next
    edit "VeriSign-OCSP"
        set internet-service-id 9568408
    next
    edit "Sony-PlayStation.Network"
        set internet-service-id 9633952
    next
    edit "Acronis-Cyber.Cloud"
        set internet-service-id 9699489
    next
    edit "RingCentral-RingCentral"
        set internet-service-id 9765027
    next
    edit "FSecure-FSecure"
        set internet-service-id 9830564
    next
    edit "Kaseya-Kaseya.Cloud"
        set internet-service-id 9896101
    next
    edit "Shodan-Scanner"
        set internet-service-id 9961638
    next
    edit "Censys-Scanner"
        set internet-service-id 10027174
    next
    edit "Valve-Steam"
        set internet-service-id 10092711
    next
    edit "YouSeeU-Bongo"
        set internet-service-id 10158248
    next
    edit "Cato-Cato.Cloud"
        set internet-service-id 10223785
    next
    edit "SolarWinds-SpamExperts"
        set internet-service-id 10289323
    next
    edit "SolarWinds-Pingdom.Probe"
        set internet-service-id 10289326
    next
    edit "SolarWinds-SolarWinds.RMM"
        set internet-service-id 10289379
    next
    edit "8X8-8X8.Cloud"
        set internet-service-id 10354860
    next
    edit "Zattoo-Zattoo.TV"
        set internet-service-id 10420401
    next
    edit "Datto-Datto.RMM"
        set internet-service-id 10485939
    next
    edit "Datto-Datto.BCDR"
        set internet-service-id 10486083
    next
    edit "Barracuda-Barracuda.Cloud"
        set internet-service-id 10551477
    next
    edit "Naver-Line"
        set internet-service-id 10617015
    next
    edit "Disney-Disney+"
        set internet-service-id 10682552
    next
    edit "DNS-DoH_DoT"
        set internet-service-id 10748089
    next
    edit "DNS-Root.Name.Servers"
        set internet-service-id 10748156
    next
    edit "DNS-ARPA.Name.Servers"
        set internet-service-id 10748206
    next
    edit "Quad9-Quad9.Standard.DNS"
        set internet-service-id 10813626
    next
    edit "Stretchoid-Scanner"
        set internet-service-id 10879142
    next
    edit "Poly-RealConnect.Service"
        set internet-service-id 10944700
    next
    edit "Telegram-Telegram"
        set internet-service-id 11010249
    next
    edit "Spotify-Spotify"
        set internet-service-id 11075786
    next
    edit "NextDNS-NextDNS"
        set internet-service-id 11141324
    next
    edit "Fastly-CDN"
        set internet-service-id 11206793
    next
    edit "Neustar-UltraDNS.Probes"
        set internet-service-id 11272397
    next
    edit "Malicious-Malicious.Server"
        set internet-service-id 11337935
    next
    edit "NIST-ITS"
        set internet-service-id 11403472
    next
    edit "Jamf-Jamf.Cloud"
        set internet-service-id 11469009
    next
    edit "Alcatel.Lucent-Rainbow"
        set internet-service-id 11534546
    next
    edit "Forcepoint-Forcepoint.Cloud"
        set internet-service-id 11600083
    next
    edit "Datadog-Datadog"
        set internet-service-id 11665620
    next
    edit "Mimecast-Mimecast"
        set internet-service-id 11731157
    next
    edit "MediaFire-Other"
        set internet-service-id 11796480
    next
    edit "MediaFire-Web"
        set internet-service-id 11796481
    next
    edit "MediaFire-ICMP"
        set internet-service-id 11796482
    next
    edit "MediaFire-DNS"
        set internet-service-id 11796483
    next
    edit "MediaFire-Outbound_Email"
        set internet-service-id 11796484
    next
    edit "MediaFire-SSH"
        set internet-service-id 11796486
    next
    edit "MediaFire-FTP"
        set internet-service-id 11796487
    next
    edit "MediaFire-NTP"
        set internet-service-id 11796488
    next
    edit "MediaFire-Inbound_Email"
        set internet-service-id 11796489
    next
    edit "MediaFire-LDAP"
        set internet-service-id 11796494
    next
    edit "MediaFire-NetBIOS.Session.Service"
        set internet-service-id 11796495
    next
    edit "MediaFire-RTMP"
        set internet-service-id 11796496
    next
    edit "MediaFire-NetBIOS.Name.Service"
        set internet-service-id 11796504
    next
    edit "Pandora-Pandora"
        set internet-service-id 11862230
    next
    edit "SiriusXM-SiriusXM"
        set internet-service-id 11927767
    next
    edit "Hopin-Hopin"
        set internet-service-id 11993304
    next
    edit "RedShield-RedShield.Cloud"
        set internet-service-id 12058842
    next
    edit "InterneTTL-Scanner"
        set internet-service-id 12124326
    next
    edit "VadeSecure-VadeSecure.Cloud"
        set internet-service-id 12189915
    next
    edit "Netskope-Netskope.Cloud"
        set internet-service-id 12255452
    next
    edit "ClickMeeting-ClickMeeting"
        set internet-service-id 12320989
    next
    edit "Tenable-Tenable.io.Cloud.Scanner"
        set internet-service-id 12386528
    next
    edit "Vidyo-VidyoCloud"
        set internet-service-id 12452065
    next
    edit "OpenNIC-OpenNIC.DNS"
        set internet-service-id 12517602
    next
    edit "Sectigo-Sectigo"
        set internet-service-id 12583141
    next
    edit "DigitalOcean-DigitalOcean.Platform"
        set internet-service-id 12648679
    next
    edit "Pitney.Bowes-Pitney.Bowes.Data.Center"
        set internet-service-id 12714216
    next
    edit "VPN-Anonymous.VPN"
        set internet-service-id 12779753
    next
    edit "Blockchain-Crypto.Mining.Pool"
        set internet-service-id 12845290
    next
    edit "FactSet-FactSet"
        set internet-service-id 12910830
    next
    edit "Bloomberg-Bloomberg"
        set internet-service-id 12976367
    next
    edit "Five9-Five9"
        set internet-service-id 13041904
    next
    edit "Gigas-Gigas.Cloud"
        set internet-service-id 13107441
    next
    edit "Imperva-Imperva.Cloud.WAF"
        set internet-service-id 13172978
    next
    edit "INAP-INAP"
        set internet-service-id 13238515
    next
    edit "Azion-Azion.Platform"
        set internet-service-id 13304053
    next
    edit "Hurricane.Electric-Hurricane.Electric.Internet.Services"
        set internet-service-id 13369590
    next
    edit "NodePing-NodePing.Probe"
        set internet-service-id 13435127
    next
    edit "Frontline-Frontline"
        set internet-service-id 13500665
    next
    edit "Tally-Tally.ERP"
        set internet-service-id 13566202
    next
    edit "Hosting-Bulletproof.Hosting"
        set internet-service-id 13631739
    next
    edit "Okko-Okko.TV"
        set internet-service-id 13697277
    next
    edit "Voximplant-Voximplant.Platform"
        set internet-service-id 13762829
    next
    edit "OVHcloud-OVHcloud"
        set internet-service-id 13828367
    next
    edit "OVHcloud-OVH.Telecom"
        set internet-service-id 13828461
    next
    edit "SentinelOne-SentinelOne.Cloud"
        set internet-service-id 13893905
    next
    edit "Kakao-Kakao.Services"
        set internet-service-id 13959442
    next
    edit "Stripe-Stripe"
        set internet-service-id 14024979
    next
    edit "NetScout-Scanner"
        set internet-service-id 14090406
    next
    edit "Recyber-Scanner"
        set internet-service-id 14155942
    next
    edit "Cyber.Casa-Scanner"
        set internet-service-id 14221478
    next
    edit "GTHost-Dedicated.Instant.Servers"
        set internet-service-id 14287132
    next
    edit "ivi-ivi.Streaming"
        set internet-service-id 14352669
    next
    edit "BinaryEdge-Scanner"
        set internet-service-id 14418086
    next
    edit "Fintech-MarketMap.Terminal"
        set internet-service-id 14483742
    next
    edit "xMatters-xMatters.Platform"
        set internet-service-id 14549279
    next
    edit "Blizzard-Battle.Net"
        set internet-service-id 14614816
    next
    edit "Axon-Evidence"
        set internet-service-id 14680353
    next
    edit "CDN77-CDN"
        set internet-service-id 14745737
    next
    edit "GCore.Labs-CDN"
        set internet-service-id 14811273
    next
    edit "Matrix42-FastViewer"
        set internet-service-id 14876962
    next
    edit "Bunny.net-CDN"
        set internet-service-id 14942345
    next
    edit "StackPath-CDN"
        set internet-service-id 15007881
    next
    edit "Edgio-CDN"
        set internet-service-id 15073417
    next
    edit "CacheFly-CDN"
        set internet-service-id 15138953
    next
    edit "Paylocity-Paylocity"
        set internet-service-id 15204646
    next
    edit "Qualys-Qualys.Cloud.Platform"
        set internet-service-id 15270183
    next
    edit "Dailymotion-Other"
        set internet-service-id 15335424
    next
    edit "Dailymotion-Web"
        set internet-service-id 15335425
    next
    edit "Dailymotion-ICMP"
        set internet-service-id 15335426
    next
    edit "Dailymotion-DNS"
        set internet-service-id 15335427
    next
    edit "Dailymotion-Outbound_Email"
        set internet-service-id 15335428
    next
    edit "Dailymotion-SSH"
        set internet-service-id 15335430
    next
    edit "Dailymotion-FTP"
        set internet-service-id 15335431
    next
    edit "Dailymotion-NTP"
        set internet-service-id 15335432
    next
    edit "Dailymotion-Inbound_Email"
        set internet-service-id 15335433
    next
    edit "Dailymotion-LDAP"
        set internet-service-id 15335438
    next
    edit "Dailymotion-NetBIOS.Session.Service"
        set internet-service-id 15335439
    next
    edit "Dailymotion-RTMP"
        set internet-service-id 15335440
    next
    edit "Dailymotion-NetBIOS.Name.Service"
        set internet-service-id 15335448
    next
    edit "LaunchDarkly-LaunchDarkly.Platform"
        set internet-service-id 15401258
    next
    edit "Medianova-CDN"
        set internet-service-id 15466633
    next
    edit "NetDocuments-NetDocuments.Platform"
        set internet-service-id 15532331
    next
    edit "Vonage-Vonage.Contact.Center"
        set internet-service-id 15597869
    next
    edit "Vonage-Vonage.Video.API"
        set internet-service-id 15597872
    next
    edit "Veritas-Enterprise.Vault.Cloud"
        set internet-service-id 15663407
    next
    edit "UK.NCSC-Scanner"
        set internet-service-id 15728806
    next
    edit "Restream-Restream.Platform"
        set internet-service-id 15794481
    next
    edit "ArcticWolf-ArcticWolf.Cloud"
        set internet-service-id 15860019
    next
    edit "CounterPath-Bria"
        set internet-service-id 15925556
    next
    edit "CriminalIP-Scanner"
        set internet-service-id 15990950
    next
    edit "IPFS-IPFS.Gateway"
        set internet-service-id 16056629
    next
    edit "Internet.Census.Group-Scanner"
        set internet-service-id 16122022
    next
    edit "Performive-Performive.Cloud"
        set internet-service-id 16187706
    next
    edit "OneLogin-OneLogin"
        set internet-service-id 16253244
    next
    edit "Shadowserver-Scanner"
        set internet-service-id 16318630
    next
    edit "Turkcell-Suit.Conference"
        set internet-service-id 16384317
    next
    edit "LeakIX-Scanner"
        set internet-service-id 16449702
    next
    edit "Infoblox-BloxOne"
        set internet-service-id 16515390
    next
    edit "Nice-CXone"
        set internet-service-id 16580927
    next
    edit "Hetzner-Hetzner.Hosting.Service"
        set internet-service-id 16646464
    next
    edit "ThreatLocker-ThreatLocker"
        set internet-service-id 16712001
    next
    edit "ZPE-ZPE.Cloud"
        set internet-service-id 16777538
    next
    edit "ColoCrossing-ColoCrossing.Hosting.Service"
        set internet-service-id 16843076
    next
    edit "Sinch-Mailgun"
        set internet-service-id 16908613
    next
    edit "SpaceX-Starlink"
        set internet-service-id 16974150
    next
    edit "Ingenuity-Ingenuity.Cloud.Service"
        set internet-service-id 17039688
    next
    edit "Skyhigh.Security-Secure.Web.Gateway"
        set internet-service-id 17105227
    next
    edit "Stark.Industries-Stark.Industries.Hosting.Service"
        set internet-service-id 17170764
    next
    edit "StatusCake-StatusCake.Monitor"
        set internet-service-id 17236307
    next
    edit "NAP-NAPLAN"
        set internet-service-id 17301844
    next
    edit "Elastic-Elastic.Cloud"
        set internet-service-id 17367382
    next
    edit "NFON-NFON"
        set internet-service-id 17432920
    next
    edit "SERVERD-SERVERD.Hosting.Service"
        set internet-service-id 17498457
    next
    edit "MEGA-MEGA.Cloud"
        set internet-service-id 17563994
    next
    edit "Hadrian-Scanner"
        set internet-service-id 17629350
    next
    edit "Dotcom.Monitor-Dotcom.Monitor"
        set internet-service-id 17695068
    next
    edit "Ahrefs-AhrefsBot"
        set internet-service-id 17760605
    next
    edit "Semrush-SemrushBot"
        set internet-service-id 17826142
    next
    edit "Zero.Networks-Zero.Networks"
        set internet-service-id 17891679
    next
    edit "Vultr-Vultr.Cloud"
        set internet-service-id 17957216
    next
    edit "EGI-EGI.Hosting.Service"
        set internet-service-id 18022753
    next
    edit "ONYPHE-Scanner"
        set internet-service-id 18088102
    next
    edit "Proofpoint-Proofpoint"
        set internet-service-id 18153828
    next
    edit "Lookout-Lookout.Cloud"
        set internet-service-id 18219365
    next
    edit "Heimdal-Heimdal.Security"
        set internet-service-id 18284902
    next
    edit "Yealink-Yealink.Meeting"
        set internet-service-id 18350439
    next
    edit "Secomea-Secomea"
        set internet-service-id 18415976
    next
    edit "CallTower-CT.Cloud"
        set internet-service-id 18481513
    next
    edit "OpenAI-OpenAI.Bot"
        set internet-service-id 18547052
    next
    edit "Alpemix-Alpemix"
        set internet-service-id 18612590
    next
    edit "M247-M247.Hosting.Service"
        set internet-service-id 18678127
    next
    edit "Quintex-Quintex.Hosting.Service"
        set internet-service-id 18743664
    next
    edit "Aeza-Aeza.Hosting.Service"
        set internet-service-id 18809201
    next
    edit "Amanah-Amanah.Hosting.Service"
        set internet-service-id 18874738
    next
    edit "ByteDance-Lark"
        set internet-service-id 18940275
    next
    edit "KnowBe4-KnowBe4"
        set internet-service-id 19005812
    next
    edit "Keeper-Keeper.Security"
        set internet-service-id 19071349
    next
    edit "NinjaOne-NinjaOne"
        set internet-service-id 19136887
    next
    edit "Modat-Scanner"
        set internet-service-id 19202214
    next
    edit "Make-Make.Platform"
        set internet-service-id 19267963
    next
    edit "DNS-Generic.TLD.Name.Servers"
        set internet-service-id 10748284
    next
    edit "Cloudzy-Cloudzy.Hosting.Service"
        set internet-service-id 19333501
    next
    edit "Nokia-Deepfield.Genome.Crawler"
        set internet-service-id 19399038
    next
end
config firewall internet-service-definition
end
config telemetry-controller application predefine
    edit "Salesforce"
    next
    edit "Twilio"
    next
    edit "Zoom"
    next
    edit "GoToMeeting"
    next
    edit "Google.Maps"
    next
    edit "Webex"
    next
    edit "Yahoo"
    next
    edit "Google.Docs"
    next
    edit "Zendesk"
    next
    edit "Google.Drive"
    next
    edit "Atlassian.Cloud"
    next
    edit "Adobe"
    next
    edit "Microsoft.SharePoint"
    next
    edit "Dropbox"
    next
    edit "Microsoft.365"
    next
    edit "Microsoft.Teams"
    next
    edit "Slack"
    next
    edit "Google.Search"
    next
end
config telemetry-controller global
end
config system health-check-fortiguard
    edit "Fortinet"
        set server "www.fortinet.com"
        set protocol http
    next
end
config system resource-limits
end
config log syslogd setting
    set status enable
    set server "**********"
end
config system standalone-cluster
    config cluster-peer
    end
end
config system fortiguard
end
config endpoint-control fctems
    edit 1
    next
    edit 2
    next
    edit 3
    next
    edit 4
    next
    edit 5
    next
    edit 6
    next
    edit 7
    next
end
config ips global
    set database extended
end
config system email-server
    set server "fortinet-notifications.com"
    set port 465
    set security smtps
end
config system session-helper
    edit 1
        set name pptp
        set protocol 6
        set port 1723
    next
    edit 2
        set name h323
        set protocol 6
        set port 1720
    next
    edit 3
        set name ras
        set protocol 17
        set port 1719
    next
    edit 4
        set name tns
        set protocol 6
        set port 1521
    next
    edit 5
        set name tftp
        set protocol 17
        set port 69
    next
    edit 6
        set name rtsp
        set protocol 6
        set port 554
    next
    edit 7
        set name rtsp
        set protocol 6
        set port 7070
    next
    edit 8
        set name rtsp
        set protocol 6
        set port 8554
    next
    edit 9
        set name ftp
        set protocol 6
        set port 21
    next
    edit 10
        set name mms
        set protocol 6
        set port 1863
    next
    edit 11
        set name pmap
        set protocol 6
        set port 111
    next
    edit 12
        set name pmap
        set protocol 17
        set port 111
    next
    edit 13
        set name sip
        set protocol 17
        set port 5060
    next
    edit 14
        set name dns-udp
        set protocol 17
        set port 53
    next
    edit 15
        set name rsh
        set protocol 6
        set port 514
    next
    edit 16
        set name rsh
        set protocol 6
        set port 512
    next
    edit 17
        set name dcerpc
        set protocol 6
        set port 135
    next
    edit 18
        set name dcerpc
        set protocol 17
        set port 135
    next
    edit 19
        set name mgcp
        set protocol 17
        set port 2427
    next
    edit 20
        set name mgcp
        set protocol 17
        set port 2727
    next
end
config system auto-install
    set auto-install-config enable
    set auto-install-image enable
end
config system console
end
config system ntp
    set ntpsync enable
    set syncinterval 1
    set server-mode enable
    set interface "mgmt"
end
config system ftm-push
    set server-cert "Fortinet_GUI_Server"
end
config system automation-trigger
    edit "Compromised Host"
        set description "An incident of compromise has been detected on a host endpoint."
    next
    edit "Any Security Rating Notification"
        set description "A security rating summary report has been generated."
        set event-type security-rating-summary
        set report-type any
    next
    edit "AV & IPS DB update"
        set description "The antivirus and IPS database has been updated."
        set event-type virus-ips-db-updated
    next
    edit "Configuration Change"
        set description "An administrator\'s session that changed a FortiGate\'s configuration has ended."
        set event-type config-change
    next
    edit "Conserve Mode"
        set description "A FortiGate has entered conserve mode due to low memory."
        set event-type low-memory
    next
    edit "HA Failover"
        set description "A HA failover has occurred."
        set event-type ha-failover
    next
    edit "High CPU"
        set description "A FortiGate has high CPU usage."
        set event-type high-cpu
    next
    edit "License Expiry"
        set description "A FortiGate license is near expiration."
        set event-type license-near-expiry
        set license-type any
    next
    edit "Reboot"
        set description "A FortiGate is rebooted."
        set event-type reboot
    next
    edit "Anomaly Logs"
        set description "An anomalous event has occurred."
        set event-type anomaly-logs
    next
    edit "IPS Logs"
        set description "An IPS event has occurred."
        set event-type ips-logs
    next
    edit "SSH Logs"
        set description "An SSH event has occurred."
        set event-type ssh-logs
    next
    edit "Traffic Violation"
        set description "A traffic policy has been violated."
        set event-type traffic-violation
    next
    edit "Virus Logs"
        set description "A virus event has occurred."
        set event-type virus-logs
    next
    edit "Webfilter Violation"
        set description "A webfilter policy has been violated."
        set event-type webfilter-violation
    next
    edit "Admin Login"
        set description "A FortiOS event with specified log ID has occurred."
        set event-type event-log
        set logid 32001
    next
    edit "Incoming Webhook Call"
        set description "An incoming webhook call is received"
        set event-type incoming-webhook
    next
    edit "Weekly Trigger"
        set trigger-type scheduled
        set trigger-frequency weekly
        set trigger-weekday tuesday
        set trigger-hour 10
    next
    edit "FortiAnalyzer Connection Down"
        set description "A FortiAnalyzer connection is down."
        set event-type event-log
        set logid 22902
    next
    edit "Network Down"
        set description "A network connection is down."
        set event-type event-log
        set logid 20099
        config fields
            edit 1
                set name "status"
                set value "DOWN"
            next
        end
    next
    edit "Local Certificate Expiry"
        set description "A local certificate is near expiration."
        set event-type local-cert-near-expiry
    next
    edit "Auto Firmware upgrade"
        set description "Automatic firmware upgrade."
        set event-type event-log
        set logid 22094 22095 32263
    next
    edit "Super Admin Creation"
        set description "A super admin is created."
        set event-type event-log
        set logid 44560
    next
end
config system automation-action
    edit "Access Layer Quarantine"
        set description "Quarantine the MAC address on access layer devices (FortiSwitch and FortiAP)."
        set action-type quarantine
    next
    edit "FortiClient Quarantine"
        set description "Use FortiClient EMS to quarantine the endpoint device."
        set action-type quarantine-forticlient
    next
    edit "FortiNAC Quarantine"
        set description "Use FortiNAC to quarantine the endpoint device."
        set action-type quarantine-fortinac
    next
    edit "IP Ban"
        set description "Ban the IP address specified in the automation trigger event."
        set action-type ban-ip
    next
    edit "FortiExplorer Notification"
        set description "Automation action configuration for sending a notification to any FortiExplorer mobile application."
        set action-type fortiexplorer-notification
    next
    edit "Email Notification"
        set description "Send a custom email to the specified recipient(s)."
        set action-type email
        set forticare-email enable
        set email-subject "%%log.logdesc%%"
    next
    edit "CLI Script - System Status"
        set description "Execute a CLI script to return the system status."
        set action-type cli-script
        set script "get system status"
        set accprofile "super_admin_readonly"
    next
    edit "Reboot FortiGate"
        set description "Reboot this FortiGate unit."
        set action-type system-actions
        set system-action reboot
        set minimum-interval 300
    next
    edit "Shutdown FortiGate"
        set description "Shut down this FortiGate unit."
        set action-type system-actions
        set system-action shutdown
    next
    edit "Backup Config Disk"
        set description "Backup the configuration on disk."
        set action-type system-actions
        set system-action backup-config
    next
end
config system automation-stitch
    edit "Network Down"
        set description "Send an email when a network goes down."
        set status disable
        set trigger "Network Down"
        config actions
            edit 1
                set action "Email Notification"
            next
        end
    next
    edit "HA Failover"
        set description "Send an email when a HA failover is detected."
        set status disable
        set trigger "HA Failover"
        config actions
            edit 1
                set action "Email Notification"
            next
        end
    next
    edit "Reboot"
        set description "Send an email when a FortiGate is rebooted."
        set status disable
        set trigger "Reboot"
        config actions
            edit 1
                set action "Email Notification"
            next
        end
    next
    edit "FortiAnalyzer Connection Down"
        set description "Send a email notification when the connection to FortiAnalyzer is lost."
        set status disable
        set trigger "FortiAnalyzer Connection Down"
        config actions
            edit 1
                set action "Email Notification"
            next
        end
    next
    edit "License Expired Notification"
        set description "Send a email notification when a license is near expiration."
        set status disable
        set trigger "License Expiry"
        config actions
            edit 1
                set action "Email Notification"
            next
        end
    next
    edit "Compromised Host Quarantine"
        set description "Quarantine a compromised host on FortiAPs, FortiSwitches, and FortiClient EMS."
        set status disable
        set trigger "Compromised Host"
        config actions
            edit 1
                set action "Access Layer Quarantine"
            next
            edit 2
                set action "FortiClient Quarantine"
            next
        end
    next
    edit "Incoming Webhook Quarantine"
        set description "Quarantine a provided MAC address on FortiAPs, FortiSwitches, and FortiClient EMS using an Incoming Webhook."
        set status disable
        set trigger "Incoming Webhook Call"
        config actions
            edit 1
                set action "Access Layer Quarantine"
            next
            edit 2
                set action "FortiClient Quarantine"
            next
        end
    next
    edit "Security Rating Notification"
        set description "Send a email notification when a new Security Rating report is available."
        set status disable
        set trigger "Any Security Rating Notification"
        config actions
            edit 1
                set action "Email Notification"
            next
        end
    next
    edit "Firmware upgrade notification"
        set description "Automatic firmware upgrade notification."
        set trigger "Auto Firmware upgrade"
        set condition-logic or
        config actions
            edit 1
                set action "Email Notification"
            next
        end
    next
    edit "Super Admin Creation Notification"
        set description "Send a email notification when a super admin is created."
        set trigger "Super Admin Creation"
        config actions
            edit 1
                set action "Email Notification"
                set required enable
            next
        end
    next
end
config system federated-upgrade
    set status disabled
    set initial-version 7-6-2-3462
    set starter-admin "daemon_admin"
end
config system ike
end
config system ipam
    set status disable
    config pools
        edit "default-pool"
            set subnet ********** ***********
        next
        edit "lan-pool"
            set subnet *********** ***********
        next
    end
    config rules
        edit "role-lan"
            set device "*"
            set interface "*"
            set role lan
            set pool "lan-pool"
            set dhcp enable
        next
    end
end
config system object-tagging
    edit "default"
    next
end
config switch-controller traffic-policy
    edit "quarantine"
        set description "Rate control for quarantined traffic"
        set guaranteed-bandwidth 163840
        set guaranteed-burst 8192
        set maximum-burst 163840
        set cos-queue 0
        set id 1
    next
    edit "sniffer"
        set description "Rate control for sniffer mirrored traffic"
        set guaranteed-bandwidth 50000
        set guaranteed-burst 8192
        set maximum-burst 163840
        set cos-queue 0
        set id 2
    next
end
config system settings
    set gui-waf-profile enable
end
config system dhcp server
    edit 1
        set status disable
        set dns-service default
        set default-gateway **************
        set netmask *************
        set interface "lan"
        config ip-range
            edit 1
                set start-ip ***************
                set end-ip ***************
            next
        end
        set dhcp-settings-from-fortiipam enable
    next
    edit 3
        set dns-service default
        set ntp-service local
        set default-gateway **********
        set netmask *************
        set interface "fortilink"
        config ip-range
            edit 1
                set start-ip **********
                set end-ip **********54
            next
        end
        set vci-match enable
        set vci-string "FortiSwitch" "FortiExtender"
    next
end
config system zone
    edit "trust-ha"
        set interface "ha1"
    next
end
config firewall address
    edit "EMS_ALL_UNKNOWN_CLIENTS"
        set uuid 6980b60c-d6db-51ef-93ac-9ec99def00a5
        set type dynamic
        set sub-type ems-tag
        set dirty clean
    next
    edit "EMS_ALL_UNMANAGEABLE_CLIENTS"
        set uuid 6980dbf0-d6db-51ef-d74e-b0154037b661
        set type dynamic
        set sub-type ems-tag
        set dirty clean
    next
    edit "none"
        set uuid 66f7f198-d6db-51ef-aaff-919ebb5cce03
        set subnet 0.0.0.0 ***************
    next
    edit "login.microsoftonline.com"
        set uuid 66f81ba0-d6db-51ef-ed7d-58731213f579
        set type fqdn
        set fqdn "login.microsoftonline.com"
    next
    edit "login.microsoft.com"
        set uuid 66f832de-d6db-51ef-81c8-b6414eb81a2d
        set type fqdn
        set fqdn "login.microsoft.com"
    next
    edit "login.windows.net"
        set uuid 66f84896-d6db-51ef-394f-de92cbc294f4
        set type fqdn
        set fqdn "login.windows.net"
    next
    edit "gmail.com"
        set uuid 66f85e76-d6db-51ef-be35-8ff7b6f8a92d
        set type fqdn
        set fqdn "gmail.com"
    next
    edit "wildcard.google.com"
        set uuid 66f8742e-d6db-51ef-e543-be592a459a6d
        set type fqdn
        set fqdn "*.google.com"
    next
    edit "wildcard.dropbox.com"
        set uuid 66f889f0-d6db-51ef-1361-25b6b8444eb0
        set type fqdn
        set fqdn "*.dropbox.com"
    next
    edit "all"
        set uuid 6980f428-d6db-51ef-8034-a355d90a8aab
    next
    edit "FIREWALL_AUTH_PORTAL_ADDRESS"
        set uuid 6980facc-d6db-51ef-b3a7-8484af36a8e7
    next
    edit "FABRIC_DEVICE"
        set uuid 69810008-d6db-51ef-c539-feee5836703a
        set comment "IPv4 addresses of Fabric Devices."
    next
    edit "SSLVPN_TUNNEL_ADDR1"
        set uuid 6983cdc4-d6db-51ef-8741-e9b03fe795ff
        set type iprange
        set start-ip **************
        set end-ip **************
    next
    edit "dmz"
        set uuid 6d7acb76-d6db-51ef-7fd3-767acfabd199
        set type interface-subnet
        set subnet ********** *************
        set interface "dmz"
    next
    edit "lan"
        set uuid 6d7b24fe-d6db-51ef-86b9-b92c786c6997
        set type interface-subnet
        set subnet ************* *************
        set interface "lan"
    next
    edit "FCTEMS_ALL_FORTICLOUD_SERVERS"
        set uuid 4967240e-d6e1-51ef-a7b3-42b3df8e0466
        set type dynamic
        set sub-type ems-tag
        set dirty clean
    next
    edit "t1 address"
        set uuid 0363ecb6-16c0-51f0-343b-d433a144cf97
        set type interface-subnet
        set subnet 0.0.0.0 ***************
        set interface "t1"
    next
    edit "t2 address"
        set uuid 1a67880a-16c0-51f0-9620-ab732992d5e7
        set type interface-subnet
        set subnet 0.0.0.0 ***************
        set interface "t2"
    next
    edit "bad11"
        set uuid 8e22791c-2589-51f0-f56e-0b29ea88b722
        set subnet *************** ***************
    next
    edit "a4"
        set uuid f45200be-2a1f-51f0-68d7-ac1b143eb6c8
        set subnet ******* ***************
    next
end
config firewall multicast-address
    edit "all"
        set start-ip *********
        set end-ip ***************
    next
    edit "all_hosts"
        set start-ip *********
        set end-ip *********
    next
    edit "all_routers"
        set start-ip *********
        set end-ip *********
    next
    edit "Bonjour"
        set start-ip ***********
        set end-ip ***********
    next
    edit "EIGRP"
        set start-ip *********0
        set end-ip *********0
    next
    edit "OSPF"
        set start-ip *********
        set end-ip *********
    next
end
config firewall address6
    edit "SSLVPN_TUNNEL_IPv6_ADDR1"
        set uuid 6983d49a-d6db-51ef-3079-396f22af2b38
        set ip6 fdff:ffff::/120
    next
    edit "all"
        set uuid 66f91082-d6db-51ef-fb0a-4c921e996fde
    next
    edit "none"
        set uuid 66f935a8-d6db-51ef-a927-54e223118df4
        set ip6 ::/128
    next
end
config firewall multicast-address6
    edit "all"
        set ip6 ff00::/8
    next
end
config firewall addrgrp
    edit "G Suite"
        set member "gmail.com" "wildcard.google.com"
        set uuid 66f8a4e4-d6db-51ef-6672-dbe08fc2eeae
    next
    edit "Microsoft Office 365"
        set member "login.microsoftonline.com" "login.microsoft.com" "login.windows.net"
        set uuid 66f8d86a-d6db-51ef-6667-4dcc42571c3e
    next
end
config firewall wildcard-fqdn custom
    edit "adobe"
        set uuid 670e6590-d6db-51ef-7afa-20330113870c
        set wildcard-fqdn "*.adobe.com"
    next
    edit "Adobe Login"
        set uuid 670e7e0e-d6db-51ef-c113-03b4ba08b464
        set wildcard-fqdn "*.adobelogin.com"
    next
    edit "android"
        set uuid 670e8106-d6db-51ef-298a-3bc5a2fce302
        set wildcard-fqdn "*.android.com"
    next
    edit "apple"
        set uuid 670e841c-d6db-51ef-f814-2364b2494f9c
        set wildcard-fqdn "*.apple.com"
    next
    edit "appstore"
        set uuid 670e8728-d6db-51ef-c3a0-28c8044f502a
        set wildcard-fqdn "*.appstore.com"
    next
    edit "auth.gfx.ms"
        set uuid 670e8a70-d6db-51ef-bf59-d7dd91b582dd
        set wildcard-fqdn "*.auth.gfx.ms"
    next
    edit "citrix"
        set uuid 670e8d5e-d6db-51ef-6357-c7513c5db4e8
        set wildcard-fqdn "*.citrixonline.com"
    next
    edit "dropbox.com"
        set uuid 670e906a-d6db-51ef-7f74-8868471bdc65
        set wildcard-fqdn "*.dropbox.com"
    next
    edit "eease"
        set uuid 670e9358-d6db-51ef-ad53-c87a9a6c34f9
        set wildcard-fqdn "*.eease.com"
    next
    edit "firefox update server"
        set uuid 670e9650-d6db-51ef-54cc-388ca1e5ce30
        set wildcard-fqdn "aus*.mozilla.org"
    next
    edit "fortinet"
        set uuid 670e993e-d6db-51ef-e5de-c806d8e82ee9
        set wildcard-fqdn "*.fortinet.com"
    next
    edit "googleapis.com"
        set uuid 670e9c54-d6db-51ef-827c-d2cde4ec359a
        set wildcard-fqdn "*.googleapis.com"
    next
    edit "google-drive"
        set uuid 670e9f4c-d6db-51ef-19c4-95e75baf4fa7
        set wildcard-fqdn "*drive.google.com"
    next
    edit "google-play2"
        set uuid 670ea244-d6db-51ef-ba35-6f6cfb7e0aac
        set wildcard-fqdn "*.ggpht.com"
    next
    edit "google-play3"
        set uuid 670ea564-d6db-51ef-d0d8-6be6fb51862f
        set wildcard-fqdn "*.books.google.com"
    next
    edit "Gotomeeting"
        set uuid 670ea85c-d6db-51ef-08e7-9873225a62d8
        set wildcard-fqdn "*.gotomeeting.com"
    next
    edit "icloud"
        set uuid 670eb040-d6db-51ef-a650-dfb78da87eef
        set wildcard-fqdn "*.icloud.com"
    next
    edit "itunes"
        set uuid 670ec4ea-d6db-51ef-5193-721e5038ced7
        set wildcard-fqdn "*itunes.apple.com"
    next
    edit "microsoft"
        set uuid 670ec814-d6db-51ef-9fba-30e59565d74f
        set wildcard-fqdn "*.microsoft.com"
    next
    edit "skype"
        set uuid 670ecb34-d6db-51ef-5e67-aa72777a590e
        set wildcard-fqdn "*.messenger.live.com"
    next
    edit "softwareupdate.vmware.com"
        set uuid 670ece36-d6db-51ef-8369-9e16df526df3
        set wildcard-fqdn "*.softwareupdate.vmware.com"
    next
    edit "verisign"
        set uuid 670ed156-d6db-51ef-baba-478242baba22
        set wildcard-fqdn "*.verisign.com"
    next
    edit "Windows update 2"
        set uuid 670ed44e-d6db-51ef-5597-cc2bbc582956
        set wildcard-fqdn "*.windowsupdate.com"
    next
    edit "live.com"
        set uuid 670ed764-d6db-51ef-be07-e80089bfb224
        set wildcard-fqdn "*.live.com"
    next
    edit "google-play"
        set uuid 670eda66-d6db-51ef-75ef-3efd7badee33
        set wildcard-fqdn "*play.google.com"
    next
    edit "update.microsoft.com"
        set uuid 670edd86-d6db-51ef-8631-931af5912fe7
        set wildcard-fqdn "*update.microsoft.com"
    next
    edit "swscan.apple.com"
        set uuid 670ee07e-d6db-51ef-6061-0ac56ad6dcf4
        set wildcard-fqdn "*swscan.apple.com"
    next
    edit "autoupdate.opera.com"
        set uuid 670ee380-d6db-51ef-928e-dfff1b247085
        set wildcard-fqdn "*autoupdate.opera.com"
    next
    edit "cdn-apple"
        set uuid 670eec22-d6db-51ef-4fee-c5ced365efd1
        set wildcard-fqdn "*.cdn-apple.com"
    next
    edit "mzstatic-apple"
        set uuid 670eef4c-d6db-51ef-0864-3593c5837ef3
        set wildcard-fqdn "*.mzstatic.com"
    next
end
config firewall service category
    edit "General"
        set uuid 67067092-d6db-51ef-9dd3-5ee6f323df72
        set comment "General services."
    next
    edit "Web Access"
        set uuid 67068514-d6db-51ef-d0ce-323d36520225
        set comment "Web access."
    next
    edit "File Access"
        set uuid 670689d8-d6db-51ef-c2a9-35e168229f76
        set comment "File access."
    next
    edit "Email"
        set uuid 67068e88-d6db-51ef-bbee-28fd7b6ca8f4
        set comment "Email services."
    next
    edit "Network Services"
        set uuid 67069342-d6db-51ef-1438-dd08bc69d11b
        set comment "Network services."
    next
    edit "Authentication"
        set uuid 670697f2-d6db-51ef-52c3-83940fe21033
        set comment "Authentication service."
    next
    edit "Remote Access"
        set uuid 67069ca2-d6db-51ef-38ec-eff32a9dd08b
        set comment "Remote access."
    next
    edit "Tunneling"
        set uuid 6706a15c-d6db-51ef-4945-331a618aefd5
        set comment "Tunneling service."
    next
    edit "VoIP, Messaging & Other Applications"
        set uuid 6706a620-d6db-51ef-56a1-3b5030bb1e08
        set comment "VoIP, messaging, and other applications."
    next
    edit "Web Proxy"
        set uuid 6706aada-d6db-51ef-a66f-c96b7ebbaeb2
        set comment "Explicit web proxy."
    next
end
config firewall service custom
    edit "ALL"
        set uuid 6706b8c2-d6db-51ef-f09f-2ddb9375e8f8
        set category "General"
        set protocol IP
    next
    edit "FTP"
        set uuid 67071696-d6db-51ef-c6fd-dd86289e4244
        set category "File Access"
        set tcp-portrange 21
    next
    edit "FTP_GET"
        set uuid 67071c9a-d6db-51ef-be76-6b7dbca3e982
        set category "File Access"
        set tcp-portrange 21
    next
    edit "FTP_PUT"
        set uuid 67072294-d6db-51ef-ae09-1f9f8d9882ff
        set category "File Access"
        set tcp-portrange 21
    next
    edit "ALL_TCP"
        set uuid 6706cf92-d6db-51ef-7e74-cb7cbf05be74
        set category "General"
        set tcp-portrange 1-65535
    next
    edit "ALL_UDP"
        set uuid 6706d5a0-d6db-51ef-cc16-ddadf6856b3d
        set category "General"
        set udp-portrange 1-65535
    next
    edit "ALL_ICMP"
        set uuid 6706db9a-d6db-51ef-5fd5-943bcc4c4573
        set category "General"
        set protocol ICMP
        unset icmptype
    next
    edit "ALL_ICMP6"
        set uuid 6706e1d0-d6db-51ef-3528-91340e7b41ad
        set category "General"
        set protocol ICMP6
        unset icmptype
    next
    edit "GRE"
        set uuid 6706e810-d6db-51ef-845e-8e5d6ffe73ac
        set category "Tunneling"
        set protocol IP
        set protocol-number 47
    next
    edit "AH"
        set uuid 6706ee28-d6db-51ef-9acc-ec58161bed30
        set category "Tunneling"
        set protocol IP
        set protocol-number 51
    next
    edit "ESP"
        set uuid 6706f436-d6db-51ef-ab5a-28eea644a74d
        set category "Tunneling"
        set protocol IP
        set protocol-number 50
    next
    edit "AOL"
        set uuid 6706fa30-d6db-51ef-e947-c48238156b82
        set tcp-portrange 5190-5194
    next
    edit "BGP"
        set uuid 6706ff26-d6db-51ef-2953-c5d1c755a47a
        set category "Network Services"
        set tcp-portrange 179
    next
    edit "DHCP"
        set uuid 67070534-d6db-51ef-8c90-c177ddc58e63
        set category "Network Services"
        set udp-portrange 67-68
    next
    edit "DNS"
        set uuid 67070b74-d6db-51ef-f9e8-df47809caea7
        set category "Network Services"
        set tcp-portrange 53
        set udp-portrange 53
    next
    edit "FINGER"
        set uuid 6707116e-d6db-51ef-c5d1-4e74859cefc0
        set tcp-portrange 79
    next
    edit "GOPHER"
        set uuid 67072d7a-d6db-51ef-e1f7-ede6d7bdca29
        set tcp-portrange 70
    next
    edit "H323"
        set uuid 6707430a-d6db-51ef-eeed-12298fd09bc8
        set category "VoIP, Messaging & Other Applications"
        set tcp-portrange 1720 1503
        set udp-portrange 1719
    next
    edit "HTTP"
        set uuid 67074a1c-d6db-51ef-edac-d7b983ab865f
        set category "Web Access"
        set tcp-portrange 80
    next
    edit "HTTPS"
        set uuid 6707502a-d6db-51ef-bb4b-986ca71eb6e0
        set category "Web Access"
        set tcp-portrange 443
    next
    edit "IKE"
        set uuid 67075656-d6db-51ef-d48f-380a3e4425aa
        set category "Tunneling"
        set udp-portrange 500 4500
    next
    edit "IMAP"
        set uuid 67075cb4-d6db-51ef-1c3d-b365c893ff68
        set category "Email"
        set tcp-portrange 143
    next
    edit "IMAPS"
        set uuid 670762d6-d6db-51ef-7f17-657b6ddd50b6
        set category "Email"
        set tcp-portrange 993
    next
    edit "Internet-Locator-Service"
        set uuid 670768e4-d6db-51ef-02b2-bd7280345f30
        set tcp-portrange 389
    next
    edit "IRC"
        set uuid 67076de4-d6db-51ef-96b6-9ec93d38bd9a
        set category "VoIP, Messaging & Other Applications"
        set tcp-portrange 6660-6669
    next
    edit "L2TP"
        set uuid 670773fc-d6db-51ef-fd54-f8a1ee88ab01
        set category "Tunneling"
        set tcp-portrange 1701
        set udp-portrange 1701
    next
    edit "LDAP"
        set uuid 67077a64-d6db-51ef-18c4-838ead0105c4
        set category "Authentication"
        set tcp-portrange 389
    next
    edit "NetMeeting"
        set uuid 67078072-d6db-51ef-4b8e-1d719fc6911d
        set tcp-portrange 1720
    next
    edit "NFS"
        set uuid 67078590-d6db-51ef-f252-ad8436b212f2
        set category "File Access"
        set tcp-portrange 111 2049
        set udp-portrange 111 2049
    next
    edit "NNTP"
        set uuid 67078ba8-d6db-51ef-e513-14b8ed2c158b
        set tcp-portrange 119
    next
    edit "NTP"
        set uuid 670790b2-d6db-51ef-833f-2b0e4a076072
        set category "Network Services"
        set tcp-portrange 123
        set udp-portrange 123
    next
    edit "OSPF"
        set uuid 67079710-d6db-51ef-bac8-8839f7b6f8d6
        set category "Network Services"
        set protocol IP
        set protocol-number 89
    next
    edit "PC-Anywhere"
        set uuid 6707a1a6-d6db-51ef-ff30-4ba3cb8cecf3
        set category "Remote Access"
        set tcp-portrange 5631
        set udp-portrange 5632
    next
    edit "PING"
        set uuid 6707a886-d6db-51ef-c1e3-bf0d1cefd60b
        set category "Network Services"
        set protocol ICMP
        set icmptype 8
        unset icmpcode
    next
    edit "TIMESTAMP"
        set uuid 6707b164-d6db-51ef-7d85-2c9d863d70e6
        set protocol ICMP
        set icmptype 13
        unset icmpcode
    next
    edit "INFO_REQUEST"
        set uuid 6707b66e-d6db-51ef-882d-6fb3a535f4fc
        set protocol ICMP
        set icmptype 15
        unset icmpcode
    next
    edit "INFO_ADDRESS"
        set uuid 6707bb8c-d6db-51ef-3c6d-77ba0e44e97d
        set protocol ICMP
        set icmptype 17
        unset icmpcode
    next
    edit "ONC-RPC"
        set uuid 6707c0be-d6db-51ef-ce31-6ab23b87daf5
        set category "Remote Access"
        set tcp-portrange 111
        set udp-portrange 111
    next
    edit "DCE-RPC"
        set uuid 6707c6f4-d6db-51ef-2115-45990d01f848
        set category "Remote Access"
        set tcp-portrange 135
        set udp-portrange 135
    next
    edit "POP3"
        set uuid 6707cd02-d6db-51ef-e9e9-b16bb6d52f7e
        set category "Email"
        set tcp-portrange 110
    next
    edit "POP3S"
        set uuid 6707d310-d6db-51ef-67b1-16567e19f96b
        set category "Email"
        set tcp-portrange 995
    next
    edit "PPTP"
        set uuid 6707d95a-d6db-51ef-f08f-5037188e5a21
        set category "Tunneling"
        set tcp-portrange 1723
    next
    edit "QUAKE"
        set uuid 6707df7c-d6db-51ef-0408-c3a964ae8014
        set udp-portrange 26000 27000 27910 27960
    next
    edit "RAUDIO"
        set uuid 6707e486-d6db-51ef-04ea-4bcc4e3fdf99
        set udp-portrange 7070
    next
    edit "REXEC"
        set uuid 6707e986-d6db-51ef-35c6-72f5dac6b0cc
        set tcp-portrange 512
    next
    edit "RIP"
        set uuid 6707ee86-d6db-51ef-6f4d-1cf0b4114586
        set category "Network Services"
        set udp-portrange 520
    next
    edit "RLOGIN"
        set uuid 6707f49e-d6db-51ef-5fd7-9b6a98777f61
        set tcp-portrange 513:512-1023
    next
    edit "RSH"
        set uuid 6707f9c6-d6db-51ef-7c75-882420520dcd
        set tcp-portrange 514:512-1023
    next
    edit "SCCP"
        set uuid 670801b4-d6db-51ef-3997-8e33b6f28105
        set category "VoIP, Messaging & Other Applications"
        set tcp-portrange 2000
    next
    edit "SIP"
        set uuid 670818b6-d6db-51ef-15e1-938bd4aaaeec
        set category "VoIP, Messaging & Other Applications"
        set tcp-portrange 5060
        set udp-portrange 5060
    next
    edit "SIP-MSNmessenger"
        set uuid 67081ee2-d6db-51ef-815e-2df4dd6746d4
        set category "VoIP, Messaging & Other Applications"
        set tcp-portrange 1863
    next
    edit "SAMBA"
        set uuid 67082504-d6db-51ef-142e-3657c6d2309a
        set category "File Access"
        set tcp-portrange 139
    next
    edit "SMTP"
        set uuid 67082b44-d6db-51ef-1d85-ae16c8ad20a8
        set category "Email"
        set tcp-portrange 25
    next
    edit "SMTPS"
        set uuid 67083152-d6db-51ef-6f1f-e9fc90c8d7cf
        set category "Email"
        set tcp-portrange 465
    next
    edit "SNMP"
        set uuid 67083792-d6db-51ef-e50c-3bf55cc4083b
        set category "Network Services"
        set tcp-portrange 161-162
        set udp-portrange 161-162
    next
    edit "SSH"
        set uuid 67083daa-d6db-51ef-1370-6c9ec698f7d2
        set category "Remote Access"
        set tcp-portrange 22
    next
    edit "SYSLOG"
        set uuid 670843c2-d6db-51ef-f917-5f08324dc092
        set category "Network Services"
        set udp-portrange 514
    next
    edit "TALK"
        set uuid 670849f8-d6db-51ef-fb1d-9cc04f04b877
        set udp-portrange 517-518
    next
    edit "TELNET"
        set uuid 67084ef8-d6db-51ef-72fe-0f1de55df8d5
        set category "Remote Access"
        set tcp-portrange 23
    next
    edit "TFTP"
        set uuid 67085538-d6db-51ef-8093-efb6a23fa981
        set category "File Access"
        set udp-portrange 69
    next
    edit "MGCP"
        set uuid 67085b6e-d6db-51ef-14d1-9bf4bbb89e69
        set category "VoIP, Messaging & Other Applications"
        set tcp-portrange 2428
        set udp-portrange 2427 2727
    next
    edit "UUCP"
        set uuid 6708617c-d6db-51ef-0bb6-2b2d4766a387
        set tcp-portrange 540
    next
    edit "VDOLIVE"
        set uuid 670866ae-d6db-51ef-f7cb-0b9ef37387bb
        set tcp-portrange 7000-7010
    next
    edit "WAIS"
        set uuid 67086bb8-d6db-51ef-8e43-2d8240bc1191
        set tcp-portrange 210
    next
    edit "WINFRAME"
        set uuid 670874be-d6db-51ef-0276-0a7cd0b42b6f
        set tcp-portrange 1494 2598
    next
    edit "X-WINDOWS"
        set uuid 67087a86-d6db-51ef-edd9-42d99e33c441
        set category "Remote Access"
        set tcp-portrange 6000-6063
    next
    edit "PING6"
        set uuid 670880ee-d6db-51ef-02ac-38269de4437a
        set protocol ICMP6
        set icmptype 128
        unset icmpcode
    next
    edit "MS-SQL"
        set uuid 67088648-d6db-51ef-fa68-b5cbe6afab0b
        set category "VoIP, Messaging & Other Applications"
        set tcp-portrange 1433 1434
    next
    edit "MYSQL"
        set uuid 67088c7e-d6db-51ef-4281-e7215c48d180
        set category "VoIP, Messaging & Other Applications"
        set tcp-portrange 3306
    next
    edit "RDP"
        set uuid 670892b4-d6db-51ef-3ce5-65c62dae6a86
        set category "Remote Access"
        set tcp-portrange 3389
    next
    edit "VNC"
        set uuid 670898d6-d6db-51ef-9fae-f50213892a53
        set category "Remote Access"
        set tcp-portrange 5900
    next
    edit "DHCP6"
        set uuid 67089f16-d6db-51ef-e59d-bb303b2a3ccb
        set category "Network Services"
        set udp-portrange 546 547
    next
    edit "SQUID"
        set uuid 6708a54c-d6db-51ef-1bc1-2e627113555d
        set category "Tunneling"
        set tcp-portrange 3128
    next
    edit "SOCKS"
        set uuid 6708ab82-d6db-51ef-de01-31a747130456
        set category "Tunneling"
        set tcp-portrange 1080
        set udp-portrange 1080
    next
    edit "WINS"
        set uuid 6708b1ae-d6db-51ef-3bf7-752006e060f2
        set category "Remote Access"
        set tcp-portrange 1512
        set udp-portrange 1512
    next
    edit "RADIUS"
        set uuid 6708b7f8-d6db-51ef-d28d-2871afd3fda9
        set category "Authentication"
        set udp-portrange 1812 1813
    next
    edit "RADIUS-OLD"
        set uuid 6708be24-d6db-51ef-bb62-1c313af8b0c1
        set udp-portrange 1645 1646
    next
    edit "CVSPSERVER"
        set uuid 6708c36a-d6db-51ef-486f-c86365b8ca7a
        set tcp-portrange 2401
        set udp-portrange 2401
    next
    edit "AFS3"
        set uuid 6708c892-d6db-51ef-a96c-53a225f85055
        set category "File Access"
        set tcp-portrange 7000-7009
        set udp-portrange 7000-7009
    next
    edit "TRACEROUTE"
        set uuid 6708d4a4-d6db-51ef-a73e-dd9a5c25d0d2
        set category "Network Services"
        set udp-portrange 33434-33535
    next
    edit "RTSP"
        set uuid 6708dde6-d6db-51ef-215f-1ea0e102305d
        set category "VoIP, Messaging & Other Applications"
        set tcp-portrange 554 7070 8554
        set udp-portrange 554
    next
    edit "MMS"
        set uuid 6708e4ee-d6db-51ef-1b72-b1466132ae98
        set tcp-portrange 1755
        set udp-portrange 1024-5000
    next
    edit "KERBEROS"
        set uuid 6708ea2a-d6db-51ef-2aed-7b1cf59f6125
        set category "Authentication"
        set tcp-portrange 88 464
        set udp-portrange 88 464
    next
    edit "LDAP_UDP"
        set uuid 6708f07e-d6db-51ef-dc2b-22f7a2aab8e9
        set category "Authentication"
        set udp-portrange 389
    next
    edit "SMB"
        set uuid 6708f6c8-d6db-51ef-96d1-523d8a6bc58d
        set category "File Access"
        set tcp-portrange 445
    next
    edit "NONE"
        set uuid 6708fd12-d6db-51ef-4b4e-00f4df1a468f
        set tcp-portrange 0
    next
    edit "webproxy"
        set uuid 697fd002-d6db-51ef-f598-cabe054d062f
        set proxy enable
        set category "Web Proxy"
        set protocol ALL
        set tcp-portrange 0-65535:0-65535
    next
end
config firewall service group
    edit "Email Access"
        set uuid 69800a22-d6db-51ef-5700-9e106092ba3e
        set member "DNS" "IMAP" "IMAPS" "POP3" "POP3S" "SMTP" "SMTPS"
    next
    edit "Web Access"
        set uuid 69804762-d6db-51ef-a1c7-d3de316b8f63
        set member "DNS" "HTTP" "HTTPS"
    next
    edit "Windows AD"
        set uuid 69805e14-d6db-51ef-9a99-ab0d6f77a1cd
        set member "DCE-RPC" "DNS" "KERBEROS" "LDAP" "LDAP_UDP" "SAMBA" "SMB"
    next
    edit "Exchange Server"
        set uuid 69808696-d6db-51ef-6477-4110cd31ec8a
        set member "DCE-RPC" "DNS" "HTTPS"
    next
end
config firewall internet-service-custom
    edit "BuiltIn-Google-Gmail"
        set id 4278190080
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Meta-Whatsapp"
        set id 4278190081
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Apple-App.Store"
        set id 4278190082
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Microsoft-Skype_Teams"
        set id 4278190083
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Microsoft-Office365"
        set id 4278190084
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Microsoft-Azure"
        set id 4278190085
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Microsoft-Outlook"
        set id 4278190086
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Microsoft-Microsoft.Update"
        set id 4278190087
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Microsoft-WNS"
        set id 4278190088
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Microsoft-Intune"
        set id 4278190089
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Amazon-AWS"
        set id 4278190090
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Amazon-Amazon.SES"
        set id 4278190091
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Adobe-Adobe.Sign"
        set id 4278190092
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Oracle-Oracle.Cloud"
        set id 4278190093
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-LogMeIn-GoTo.Suite"
        set id 4278190094
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Symantec-Symantec.Cloud"
        set id 4278190095
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-VMware-Workspace.ONE"
        set id 4278190096
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-TeamViewer-TeamViewer"
        set id 4278190097
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-HP-Aruba"
        set id 4278190098
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Cisco-Webex"
        set id 4278190099
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Cisco-Meraki.Cloud"
        set id 4278190100
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Cisco-Duo.Security"
        set id 4278190101
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Cisco-AppDynamic"
        set id 4278190102
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Cisco-Secure.Endpoint"
        set id 4278190103
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-IBM-IBM.Cloud"
        set id 4278190104
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Tencent-VooV.Meeting"
        set id 4278190105
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Zendesk-Zendesk.Suite"
        set id 4278190106
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-GitHub-GitHub"
        set id 4278190107
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-AnyDesk-AnyDesk"
        set id 4278190108
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-ESET-Eset.Service"
        set id 4278190109
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Slack-Slack"
        set id 4278190110
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-SAP-HANA"
        set id 4278190111
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-SAP-SuccessFactors"
        set id 4278190112
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-SAP-SAP.Ariba"
        set id 4278190113
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Zoom.us-Zoom.Meeting"
        set id 4278190114
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Pexip-Pexip.Meeting"
        set id 4278190115
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Zscaler-Zscaler.Cloud"
        set id 4278190116
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Alibaba-DingTalk"
        set id 4278190117
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-GoDaddy-GoDaddy.Email"
        set id 4278190118
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Webroot-Webroot.SecureAnywhere"
        set id 4278190119
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Avast-Avast.Security"
        set id 4278190120
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Lifesize-Lifesize.Cloud"
        set id 4278190121
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Okta-Okta"
        set id 4278190122
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Egnyte-Egnyte"
        set id 4278190123
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-CrowdStrike-CrowdStrike.Falcon.Cloud"
        set id 4278190124
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Splashtop-Splashtop"
        set id 4278190125
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Panda-Panda.Security"
        set id 4278190126
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Ewon-Talk2M"
        set id 4278190127
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Nutanix-Nutanix.Cloud"
        set id 4278190128
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Extreme-Extreme.Cloud"
        set id 4278190129
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Genesys-PureCloud"
        set id 4278190130
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-BlackBerry-Cylance"
        set id 4278190131
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-DigiCert-OCSP"
        set id 4278190132
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Acronis-Cyber.Cloud"
        set id 4278190133
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-RingCentral-RingCentral"
        set id 4278190134
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Valve-Steam"
        set id 4278190135
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Cato-Cato.Cloud"
        set id 4278190136
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-SolarWinds-SpamExperts"
        set id 4278190137
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-8X8-8X8.Cloud"
        set id 4278190138
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Datto-Datto.RMM"
        set id 4278190139
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Datto-Datto.BCDR"
        set id 4278190140
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Barracuda-Barracuda.Cloud"
        set id 4278190141
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Poly-RealConnect.Service"
        set id 4278190142
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Jamf-Jamf.Cloud"
        set id 4278190143
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Alcatel.Lucent-Rainbow"
        set id 4278190144
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Datadog-Datadog"
        set id 4278190145
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Mimecast-Mimecast"
        set id 4278190146
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Pandora-Pandora"
        set id 4278190147
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-SiriusXM-SiriusXM"
        set id 4278190148
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-VadeSecure-VadeSecure.Cloud"
        set id 4278190149
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Netskope-Netskope.Cloud"
        set id 4278190150
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Sectigo-Sectigo"
        set id 4278190151
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Pitney.Bowes-Pitney.Bowes.Data.Center"
        set id 4278190152
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-FactSet-FactSet"
        set id 4278190153
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Bloomberg-Bloomberg"
        set id 4278190154
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Five9-Five9"
        set id 4278190155
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Frontline-Frontline"
        set id 4278190156
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Tally-Tally.ERP"
        set id 4278190157
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Voximplant-Voximplant.Platform"
        set id 4278190158
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Kakao-Kakao.Services"
        set id 4278190159
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Stripe-Stripe"
        set id 4278190160
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-xMatters-xMatters.Platform"
        set id 4278190161
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Axon-Evidence"
        set id 4278190162
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Matrix42-FastViewer"
        set id 4278190163
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-LaunchDarkly-LaunchDarkly.Platform"
        set id 4278190164
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-NetDocuments-NetDocuments.Platform"
        set id 4278190165
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Vonage-Vonage.Contact.Center"
        set id 4278190166
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Veritas-Enterprise.Vault.Cloud"
        set id 4278190167
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Restream-Restream.Platform"
        set id 4278190168
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-CounterPath-Bria"
        set id 4278190169
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-OneLogin-OneLogin"
        set id 4278190170
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Turkcell-Suit.Conference"
        set id 4278190171
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Infoblox-BloxOne"
        set id 4278190172
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Nice-CXone"
        set id 4278190173
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-ThreatLocker-ThreatLocker"
        set id 4278190174
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-ZPE-ZPE.Cloud"
        set id 4278190175
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Skyhigh.Security-Secure.Web.Gateway"
        set id 4278190176
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-MEGA-MEGA.Cloud"
        set id 4278190177
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
    edit "BuiltIn-Proofpoint-Proofpoint"
        set id 4278190178
        set comment "BuiltIn ffdb-fqdn entry"
        config entry
            edit 4
            next
            edit 6
                set addr-mode ipv6
            next
        end
    next
end
config telemetry-controller profile
    edit "default"
        set comment "Default telemetry profile"
        config application
            edit 1
                set app-name "Google.Docs"
            next
            edit 2
                set app-name "Microsoft.365"
            next
            edit 3
                set app-name "Salesforce"
            next
        end
    next
end
config vpn certificate ca
end
config vpn certificate local
    edit "Fortinet_CA_SSL"
        set password ENC Yhk/GDtBjftZg9P0j7HZiFaUqRxkU9ntdTfdTogwg1Lthfa1cV24mH1JN7nlegG7yL6cVv7//NlWV2w6OMucSWHvwsAYo1Oi7TIJ6uWoeacMKgFEeii3cj4MPYdfAAaaEOxW8IhHEbF3U8hTzbMI7TVYekZUvDepwcpHlixWAA6rz8Qr9x/76BQNnMLiivbDZ8guMllmMjY3dkVA
        set comments "This is the default CA certificate the SSL Inspection will use when generating new server certificates."
        set private-key "***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
        set certificate "-----BEGIN CERTIFICATE-----
MIIEEjCCAvqgAwIBAgIIKZ30IbEqnjQwDQYJKoZIhvcNAQELBQAwgakxCzAJBgNV
BAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQHDAlTdW5ueXZhbGUx
ETAPBgNVBAoMCEZvcnRpbmV0MR4wHAYDVQQLDBVDZXJ0aWZpY2F0ZSBBdXRob3Jp
dHkxGTAXBgNVBAMMEEZHMTAwRlRLMjIwMzE0MDMxIzAhBgkqhkiG9w0BCQEWFHN1
cHBvcnRAZm9ydGluZXQuY29tMB4XDTI1MDEyMDAzMDQ1MVoXDTM1MDEyMTAzMDQ1
MVowgakxCzAJBgNVBAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQH
DAlTdW5ueXZhbGUxETAPBgNVBAoMCEZvcnRpbmV0MR4wHAYDVQQLDBVDZXJ0aWZp
Y2F0ZSBBdXRob3JpdHkxGTAXBgNVBAMMEEZHMTAwRlRLMjIwMzE0MDMxIzAhBgkq
hkiG9w0BCQEWFHN1cHBvcnRAZm9ydGluZXQuY29tMIIBIjANBgkqhkiG9w0BAQEF
AAOCAQ8AMIIBCgKCAQEAi5cY/hVT8z3zUKBNJ4oStqunlCUukkVSgA4i/tiMBFqP
Uc9nOPEmQ9zJyODsciI0pb5h/gI1SWbpgkvOK4sjL6MuEQ6W2skYwSXpDAPfl1Tl
t2yD3Ee4KGQ/7PwPJWn6dtAAiw8kQ4RvUWbv6Y4ulG/6ZQsbaB0lJqtNY8d9Xvba
Vgaku9RWq9IWFtBPqpxr+THCTh5nwQnXzdkvRy18ict1IK3qKZsflWSQM13LHYd4
azjDzkfySlMGZJ7Uijb9V62D9kfR3KMzfnlB/ojO/exBVr1f9bpxE5g4bSRLtOyc
43prUjNxbbtM/TIjHeLByJcdnTd9kwRiR4vmS0Ay8QIDAQABozwwOjAMBgNVHRME
BTADAQH/MAsGA1UdDwQEAwICBDAdBgNVHQ4EFgQUf2lyk3beNrsvt+cRDQQ4FiDO
mcEwDQYJKoZIhvcNAQELBQADggEBAGn41sD+9hGqwJ+5Ud2r/B64gWXQaSprvT+l
hPpLCUOsPP/g9nZwKQIbYjfJnNv129mdGysfg7Fjqk8b2huE/SiXOnihPdw2xtQf
kf/bpvoRFTB4X3bygpt/PW2jGNXx0YmIf61yYZSkIBWIxT33Qap9X/R1hVG8Zs68
uP6CJQdqSEP8ClF+0FTYeuhCPgsj4eisKtmkF8Km4lIbazLB0DZgI58Zy04LGN4O
nm+/CMxoKRByJkTtGLCo35VkvNPKC7/NOLD3aT4b0aNTwtblXcM3POR94EUZ35N1
AHA5ygdgT/8QhETN9n2h0+qfpFPsoU88Hbuh6qqfx65zYr6g1k0=
-----END CERTIFICATE-----"
        set range global
        set source factory
        set last-updated 1737342328
    next
    edit "Fortinet_CA_Untrusted"
        set password ENC mbiKqVoLwVapjVPOlBSqJQ9ojTAgzorO6anPfyS8bEx/CGKp1LGBtlWetI2766GQyNZDGiQgiQwZpWlQYA85IvGO5XodmrSoxE6+i8iNHkuUg49dEeCy29wemBFm7Jl0sBe4WCPlU3kCqFkC/BTgqjUkI1igKtCKk9B1j2J+QQQ9myZJpvjOVo9wkyby44mnnyzxNVlmMjY3dkVA
        set comments "This is the default CA certificate the SSL Inspection will use when generating new server certificates."
        set private-key "***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
        set certificate "-----BEGIN CERTIFICATE-----
MIIEHDCCAwSgAwIBAgIIZXJ+B3/yTEwwDQYJKoZIhvcNAQELBQAwga4xCzAJBgNV
BAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQHDAlTdW5ueXZhbGUx
ETAPBgNVBAoMCEZvcnRpbmV0MR4wHAYDVQQLDBVDZXJ0aWZpY2F0ZSBBdXRob3Jp
dHkxHjAcBgNVBAMMFUZvcnRpbmV0IFVudHJ1c3RlZCBDQTEjMCEGCSqGSIb3DQEJ
ARYUc3VwcG9ydEBmb3J0aW5ldC5jb20wHhcNMjUwMTIwMDMwNDUyWhcNMzUwMTIx
MDMwNDUyWjCBrjELMAkGA1UEBhMCVVMxEzARBgNVBAgMCkNhbGlmb3JuaWExEjAQ
BgNVBAcMCVN1bm55dmFsZTERMA8GA1UECgwIRm9ydGluZXQxHjAcBgNVBAsMFUNl
cnRpZmljYXRlIEF1dGhvcml0eTEeMBwGA1UEAwwVRm9ydGluZXQgVW50cnVzdGVk
IENBMSMwIQYJKoZIhvcNAQkBFhRzdXBwb3J0QGZvcnRpbmV0LmNvbTCCASIwDQYJ
KoZIhvcNAQEBBQADggEPADCCAQoCggEBAKa1/Zr8n3Rap7uiQ5FXllOMvY0O7W9P
qXBEedmWhnWdZqQXhMx8P/j3IMDDandD4oT3cdQG52lvyY70yg4zB7T3pekbKXcb
/d2TswOIcR2ysloz1B8Ay4VJMPXj4qXfg4DJWi5WCOmq3mMmigizWkkT6FBZ5rsD
5xuIS14FvOSN+lc95hzuROOWLXLUkK8uCxtEd7N2o0RNuGlPHn5t0Vpmm/JiuiTd
tjpYcbfztkcaxzFpGIbX7E/zg/0oRS6SdeFtc7Km0mFxlSVgdJXxMflIyqERfX4r
6cBg+Pesqj11N3HoK24CA7c3kPwXQ8M4HXK4X/iHXbdsvTeMw4mucQkCAwEAAaM8
MDowDAYDVR0TBAUwAwEB/zALBgNVHQ8EBAMCAgQwHQYDVR0OBBYEFGcoZgDx2b46
8zvvILqApaw7XwbEMA0GCSqGSIb3DQEBCwUAA4IBAQBzQ/XBom6VkWHl1R8Zk47+
Znqjq8xoEqQSgK2XVPdHMYVhcPuQs0JdtJxFgmrP77A90+KZ1/t31Xv7n5Jn3wEa
MIfuqBEyAlALDudt8Ft/HOD3kmQl5vTRj0Z12M0TuUjzAkR2gcxcOwJMETMW1C+c
dMKroumBO4Acd1th8Ow85UnP4FxNQAwQv3hq2t6hNN+hFDshOObO5TA1EVM/sHOi
XuiMR7+nngXxqNWYbiBrHydeLLIv7JIoFJcDSR8dZhs1WdPYl697z0n5cBDzFy6x
2QOxcOCMj73YyfQbtl6Su+pb2FZOH2KRsuzr6Ntg16tA6fQGOs8hXAslc7lVUjWj
-----END CERTIFICATE-----"
        set range global
        set source factory
        set last-updated 1737342328
    next
    edit "Fortinet_SSL"
        set password ENC X5FzlycWSMeHAhklDXCK/5ILqLJnjJ5JZn7sbifl9vhxfzCuNiKN1nLnrkiD9KpP0/nppOyAn5WT27zcKgryMy06L/KYPNLRUo3iukofC6pbdMBqbP4n7STvDJ1dT6OkOGW80unN8qmoxTk16/RHIYBcsJYX9lCqNrg++LbfFx1/Ggk+qYCh/Gb+usk+5tO2PJRIzllmMjY3dkVA
        set comments "This certificate is embedded in the hardware at the factory and is unique to this unit. "
        set private-key "***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
        set certificate "-----BEGIN CERTIFICATE-----
MIID4DCCAsigAwIBAgIIeqCt2M9Tu0wwDQYJKoZIhvcNAQELBQAwgZ0xCzAJBgNV
BAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQHDAlTdW5ueXZhbGUx
ETAPBgNVBAoMCEZvcnRpbmV0MRIwEAYDVQQLDAlGb3J0aUdhdGUxGTAXBgNVBAMM
EEZHMTAwRlRLMjIwMzE0MDMxIzAhBgkqhkiG9w0BCQEWFHN1cHBvcnRAZm9ydGlu
ZXQuY29tMB4XDTI1MDEyMDAzMDQ1M1oXDTI3MDQyNTAzMDQ1M1owgZ0xCzAJBgNV
BAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQHDAlTdW5ueXZhbGUx
ETAPBgNVBAoMCEZvcnRpbmV0MRIwEAYDVQQLDAlGb3J0aUdhdGUxGTAXBgNVBAMM
EEZHMTAwRlRLMjIwMzE0MDMxIzAhBgkqhkiG9w0BCQEWFHN1cHBvcnRAZm9ydGlu
ZXQuY29tMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAt4NhLaPWIPex
NVPjtsnRBuxLbrA7TSEBjHvqsC4oRbiu+jTJFItQ8MXjazZCpNwnEP3/bnjSEkW+
W+E5ERMHxGsbP/dadFT+16Bab2KjgoE3NVxGpLK36xzhvGqOu6zHfiGNKPSitxY0
4ClrAuVg7Ri1znyJ1p2RsYZeoOgYPmNcQYojlmhX1oxMzJ5S070jkYdijwiwlSAT
XzjMQKGQtOL4rNtzbERffAc3Bhk+1QioM9Ap93KtFfzC6tMmCgbfZnfxTiQ8w7ns
hB221v6gd7DsEQ4I58rClJj5cUJF3bQYQVJjSkW3ZSRQG1yeOPLCKz99e2eXp5+T
fzIXXiUytQIDAQABoyIwIDAJBgNVHRMEAjAAMBMGA1UdJQQMMAoGCCsGAQUFBwMB
MA0GCSqGSIb3DQEBCwUAA4IBAQCMxu54l6/4pCSwqVwrqMMjKsUWeJRc+lHw2Owe
BNBPD2xtXpCm+uvNpu20YVrxOPs90IgaL414h3mHQpfgcI5gWStdQPbfMbGSvb6P
4NRsSf99te+0h5P86neFwjADrMAeJaiNuLnDW1dUN/61g49cLOjmQ1EpGQinEXf/
Kc+7bncJ32clmQCv+zdQLN4rDrFGwHHSlcKfKIliiS5sGYIBX736v2wvQJKDo8aW
t5nTiKiTLlgiNJYshebUIzDQYDmHcOX6YwiO/reH9bR4ThMHbNHaP2CLsXoR/7la
SFzeHoRjA0mcjs6p2I6QRcdgNSXghyo8rfHt6+2StCXNpOdN
-----END CERTIFICATE-----"
        set range global
        set source factory
        set last-updated 1737342328
    next
    edit "Fortinet_GUI_Server"
        set password ENC 1/mvOGAsO/x1V9IeyLoFUeQpGmjwnUWOsWl7lux47HbsBAExRyBmehTIHbSDatxS6JyO+VlP0XloS9RjplB7LKl5hXq46IQ/dJGMgP2/7EdPq6j5PvcFa/zksYmXztpZZIKgfn4u9xj574+fxt3mMQHufnjPhBgmFzcLqxQT2j46VeERiftVYjCOzKmNZGaStopXCFlmMjY3dkVA
        set comments "This is the default CA certificate the SSL Inspection will use when generating new server certificates."
        set private-key "***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
        set certificate "-----BEGIN CERTIFICATE-----
MIIFMDCCBBigAwIBAgIILDCOTdSi488wDQYJKoZIhvcNAQELBQAwgakxCzAJBgNV
BAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQHDAlTdW5ueXZhbGUx
ETAPBgNVBAoMCEZvcnRpbmV0MR4wHAYDVQQLDBVDZXJ0aWZpY2F0ZSBBdXRob3Jp
dHkxGTAXBgNVBAMMEEZHMTAwRlRLMjIwMzE0MDMxIzAhBgkqhkiG9w0BCQEWFHN1
cHBvcnRAZm9ydGluZXQuY29tMB4XDTI1MDEyMTExMTA1NFoXDTI3MDQyNjExMTA1
NFowdjELMAkGA1UEBhMCVVMxEzARBgNVBAgMCkNhbGlmb3JuaWExEjAQBgNVBAcM
CVN1bm55dmFsZTEWMBQGA1UECgwNRm9ydGluZXQgTHRkLjESMBAGA1UECwwJRm9y
dGlHYXRlMRIwEAYDVQQDDAlGb3J0aUdhdGUwggIiMA0GCSqGSIb3DQEBAQUAA4IC
DwAwggIKAoICAQCxXEw5VfDh0vZcliwQF+wQzEVvm7mqS3YT/dH64QQxbWFV6O28
iXDcPRZJZ/aomvbW2rUcieT5wseUgwwl5ziUfyFYdcia610h5m/mcnPEMJdphAdO
1+JQks8/WaOqs3vm1LVJ3/ou7kzL+cV3BNzGliiFPETpBIZfcq6B6Nmj8lgx29s4
5JJKD3o7RYhsQd89n2JNNJRpUY/cGoNuhQwkJ/uhqXGHi33j56DHSIHncAZQFQNM
MserJHG6z+P/T06nDna7FxjHxjPlHKWOWG6cZMHho6vuAYPJvLWZYIa3eyBdrBGi
Q1AahFHWBNPnVgbDGivzrpNHMeSGMHrmrP8g+5Q3sKwOZF7eXCmHaV1vjKxvzi1X
Vs+H9A8bNKAR69A5MUEAOiaXoPI0tQF1m+NImOvTdi9QUN4sf+9qdG2ZqN9ZvYC0
ezcOQ/LCr+vALuY0D6fahNOqaQRswL/se+QYpGYyAjn4Pd6od4A/OG/sIzGKTSKx
Dz6QtoY6GoVz8FoBXLC1q+FXhi9BV9FCO7ieLpG67bedofonKwgJaeTN8zRExLtE
66QEBygTg2Oij2iqTuQE0YoHTMrhXlN9OOtylClQt9NzbqYDhqynIbGEL3jJz3Ew
Rch0iTtpJUb68JvRfRz6WpenwY3I/wyHOPtsGRJJ6GwhYAwsIiW7sCVnlQIDAQAB
o4GNMIGKMAkGA1UdEwQCMAAwEwYDVR0lBAwwCgYIKwYBBQUHAwEwCwYDVR0PBAQD
AgeAMB0GA1UdDgQWBBQd0ve7/PcOeROKjmt2/BNAxegxkjAfBgNVHSMEGDAWgBR/
aXKTdt42uy+35xENBDgWIM6ZwTAbBgNVHREEFDAShwQKCgoBhwTAqGRjhwQKYB4F
MA0GCSqGSIb3DQEBCwUAA4IBAQAby17CnCaKTzlioZuYK79qjnq82n1B/Crx0p7k
+Qle7pzhpzhTCZeLZ7th6Cf/r0PfEJSCf0fyqLLE17H8fXT981ANbGcrTf+1su7y
pQZest3OwhqzY3sTJt5rZXo13gaRz/aQN3AoeNKpTj/TEHPaIfii/oHfw6B4YO7z
h7ju8Brwo5cto/aIX3qbc0vJFoAQwl5tfOLUX5juzRRMnSRallp74LFe+6ejHTp3
rGj43V7o6LCpJXkp/zP00kQiiexTjfiyuTII5gz37f9pC4jlCfmoi6vUl9IAaQdg
Wm3cEa4Uf8dU3PHHwqsd6YNxfWduW/MIzVS5dSJVasgMzMRq
-----END CERTIFICATE-----"
        set range global
        set source factory
        set last-updated 1737342328
    next
    edit "Fortinet_SSL_RSA1024"
        set password ENC V6GY093zxYv1XLe7VXtpS3koLLl7CObSln0wz9L6Pc0z7CdAD981Dt+WNgco9j4SGGNL169K9ASLJk5Ut2vuR+9FG7uKClzb/6Rqp+EUgMnulw2+H186VGcXZoBDdktAdgBhuJyScIzcny7gHqYPAU98DDj4nU6VWvIpqrcnFWYBpLSF4vVqseUQUYNUuOAQqM7m61lmMjY3dkVA
        set comments "This certificate is embedded in the hardware at the factory and is unique to this unit. "
        set private-key "***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
        set certificate "-----BEGIN CERTIFICATE-----
MIIC2zCCAkSgAwIBAgIIMhqt5sXPNJwwDQYJKoZIhvcNAQELBQAwgZ0xCzAJBgNV
BAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQHDAlTdW5ueXZhbGUx
ETAPBgNVBAoMCEZvcnRpbmV0MRIwEAYDVQQLDAlGb3J0aUdhdGUxGTAXBgNVBAMM
EEZHMTAwRlRLMjIwMzE0MDMxIzAhBgkqhkiG9w0BCQEWFHN1cHBvcnRAZm9ydGlu
ZXQuY29tMB4XDTI1MDEyMDAzMDQ1M1oXDTI3MDQyNTAzMDQ1M1owgZ0xCzAJBgNV
BAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQHDAlTdW5ueXZhbGUx
ETAPBgNVBAoMCEZvcnRpbmV0MRIwEAYDVQQLDAlGb3J0aUdhdGUxGTAXBgNVBAMM
EEZHMTAwRlRLMjIwMzE0MDMxIzAhBgkqhkiG9w0BCQEWFHN1cHBvcnRAZm9ydGlu
ZXQuY29tMIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCpyRIncBQZotbSAk9w
a3Sewr2kGIIbJVYdIlvkjQcdamywAcFQadW4aUpFkr1M6P8qRJJHUl56h0wOsdn8
+a50OaaEFPFQHXt+zBh/yOXEb4y+Qc/0lWWB4D6/x4V3mXO3Kc7E/tBGB+8UXIPr
iEbcyc9gSsdcaQJY7zGsX9FLFQIDAQABoyIwIDAJBgNVHRMEAjAAMBMGA1UdJQQM
MAoGCCsGAQUFBwMBMA0GCSqGSIb3DQEBCwUAA4GBAJlMY/v3Udpky7snsHyyIYB2
7abgOVeHOJYHBWvhLkAehgpAFlhw9f9mBa5nKotAupkqXBv2Vy8/CD1ZnS082vgL
4pTMRAAzje959Volkzl4BeyuITGVuZ1OJF3h5kEDpTi1Wxa/SyW8jSO+KZxqWZjk
oQSYtLYweJD+GIgEuvKx
-----END CERTIFICATE-----"
        set range global
        set source factory
        set last-updated 1737342328
    next
    edit "Fortinet_SSL_RSA2048"
        set password ENC fNqEhXOiSiUAmtCZegpm/iq708vALXiavlLosD/6PLzwNSzUsPEyuy+P6aJ0Zj9On4bI/3gTnwlB02SrIe27+tosxq4tFVMQ7FAMQbfan/xIGIlS7tLUzD4Hmt4C2JMBR0E02gddQPr/4Y8ia+bpht9ivbzHdGG72pH49pR/91oQNNHFmJUYiIbXx9UlT8VLg6VuIFlmMjY3dkVA
        set comments "This certificate is embedded in the hardware at the factory and is unique to this unit. "
        set private-key "***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
        set certificate "-----BEGIN CERTIFICATE-----
MIID4DCCAsigAwIBAgIIDZRFVwVysdowDQYJKoZIhvcNAQELBQAwgZ0xCzAJBgNV
BAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQHDAlTdW5ueXZhbGUx
ETAPBgNVBAoMCEZvcnRpbmV0MRIwEAYDVQQLDAlGb3J0aUdhdGUxGTAXBgNVBAMM
EEZHMTAwRlRLMjIwMzE0MDMxIzAhBgkqhkiG9w0BCQEWFHN1cHBvcnRAZm9ydGlu
ZXQuY29tMB4XDTI1MDEyMDAzMDQ1M1oXDTI3MDQyNTAzMDQ1M1owgZ0xCzAJBgNV
BAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQHDAlTdW5ueXZhbGUx
ETAPBgNVBAoMCEZvcnRpbmV0MRIwEAYDVQQLDAlGb3J0aUdhdGUxGTAXBgNVBAMM
EEZHMTAwRlRLMjIwMzE0MDMxIzAhBgkqhkiG9w0BCQEWFHN1cHBvcnRAZm9ydGlu
ZXQuY29tMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAloko5YzvEBIw
OHShsekx5aIg+7f8iNLEolneEA6HPSnKtr4zhsSBsWZbR9TyGdfp46Z2MPvAKu44
XpL/GOTFCja6CKgiX6bsq3iT4D6TRVfvR8y4kcV5DFcJQwfEbGynMsC1eUgwL3Eb
Q4xJBIXSLYM81sEwZ4e0ITkd2E5ijEdnEpZyLCf3g06Yv1PP3A76jtxVuvSTpvFE
Qy5ACQl2fO3nkYXT2K97rweDkfpqjW/W+0p1sHndVx+/Voc0z8egH9TcG+6c0L63
EgHLpCwbiWY6GBqjuyvBf8SWyriTpjQuskqRuqLb/olo1yeYRXAfOd9krVpeNdK0
bs2JJS622QIDAQABoyIwIDAJBgNVHRMEAjAAMBMGA1UdJQQMMAoGCCsGAQUFBwMB
MA0GCSqGSIb3DQEBCwUAA4IBAQBwhhDOhrYK+q801G2tx19zMgYjUs7GT4PIi681
3LkJKgW3QsmcqlUscGNtWRCyFIFb7Vzh9qS+7J8Nz+gCCHFAlmMh6RnLf1LrpS1T
dcdcQZzYvjSEIywwaugSaR4lJVbroQgv3hf5QH/dJGp2dQscb/LjWT1Y9S1IazTy
iesqkPj3mCaSMRZRUDiyq0E8Z2KAXNP6cT31t+Fbe1bu0g2BNqd3iZxXCBJez1kg
id8SOVNB5KcdPcG0yDcr04rE6adJPgtz6An5472Okg5RWbh+C1s7xPpGc6xEkD1F
KJbic+ErAy8m2h1dydVLnXxMvR/PrJJiQXXQopFVUBuQ0/+R
-----END CERTIFICATE-----"
        set range global
        set source factory
        set last-updated 1737342328
    next
    edit "Fortinet_SSL_RSA4096"
        set password ENC B3/WgEk/U8/sRVhcjBlxWeFj9Qq+ov+vu4edgHYEOjtTD+JJwHrmESfiVYc3tLD5xXogfWrdwXnIbo39lkEdtXbsALu8iw76AlN2CaBika6o3T5kjlrrVXxOk0nlsFl0KDfoawkNlMl/AK2tAILMM4oz9OCkipniX6XydZdLC6vGrvQz0MT/LwJRUNcPpW0Af6B83FlmMjY3dkVA
        set comments "This certificate is embedded in the hardware at the factory and is unique to this unit. "
        set private-key "***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
        set certificate "-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----"
        set range global
        set source factory
        set last-updated 1737342328
    next
    edit "Fortinet_SSL_DSA1024"
        set password ENC t87HJ5aQHmizdo7jsTDIcdLyrFv66hmWnJqldaBKao/9sphJVKMGTU2+zj5SL/hBtQlCragtykr6RGVyPdssawLE/c7tMFxo9+AyPnnoxTHq/Vm1lINI1gJdLvnXM0oLmnJcVpTjPkfh2wmAKIEz7Jd106wjxl75khgApL0Ewn7ZoCd7nmqsyKJRty7Fv4+h44jtk1lmMjY3dkVA
        set comments "This certificate is embedded in the hardware at the factory and is unique to this unit. "
        set private-key "-----BEGIN PRIVATE KEY-----
MIIBSgIBADCCASsGByqGSM44BAEwggEeAoGBAMSmO5hSIlJC9N3XkPhmzVb5RkHM
KLboBrms+5Af732tmFkuv3RrDBBz7AsYQJw8WfO7j3DD+Ed90u6sV2D+ccBzmO4T
aqZ0T8Luqm+9+MLeHiUqlR/OI+4Wyt4RUcsHK++k+q5k0DUgxCihlqAZr2KpwNQN
t9CX4/SoQkaiBbxNAhUA+Y/yGSLdZIy7ZKsBaIaX38dVcg0CgYBGFGOvthGUmed2
i+B7jrMCiFSJfmvmEUm1+9maUBjyvK7/Ns4vIma7NRIZx4oJpYK82B2qGdIzY2Rc
XfcKnofN4tpHdy/6ZAbwRoYKt4jD9cpQcdjyJRnoUxyCSrAyWPTIhO46Fy2HVz87
9T70WymM2qAEV/t9xpc+4k2OXQB/bgQWAhRbKBaphRrJJ7K1Rn68gA3wwZas4Q==
-----END PRIVATE KEY-----"
        set certificate "-----BEGIN CERTIFICATE-----
MIIDnDCCA1qgAwIBAgIIM7fUrlnA7iEwCwYJYIZIAWUDBAMCMIGdMQswCQYDVQQG
EwJVUzETMBEGA1UECAwKQ2FsaWZvcm5pYTESMBAGA1UEBwwJU3Vubnl2YWxlMREw
DwYDVQQKDAhGb3J0aW5ldDESMBAGA1UECwwJRm9ydGlHYXRlMRkwFwYDVQQDDBBG
RzEwMEZUSzIyMDMxNDAzMSMwIQYJKoZIhvcNAQkBFhRzdXBwb3J0QGZvcnRpbmV0
LmNvbTAeFw0yNTAxMjAwMzA1MDlaFw0yNzA0MjUwMzA1MDlaMIGdMQswCQYDVQQG
EwJVUzETMBEGA1UECAwKQ2FsaWZvcm5pYTESMBAGA1UEBwwJU3Vubnl2YWxlMREw
DwYDVQQKDAhGb3J0aW5ldDESMBAGA1UECwwJRm9ydGlHYXRlMRkwFwYDVQQDDBBG
RzEwMEZUSzIyMDMxNDAzMSMwIQYJKoZIhvcNAQkBFhRzdXBwb3J0QGZvcnRpbmV0
LmNvbTCCAbYwggErBgcqhkjOOAQBMIIBHgKBgQDEpjuYUiJSQvTd15D4Zs1W+UZB
zCi26Aa5rPuQH+99rZhZLr90awwQc+wLGECcPFnzu49ww/hHfdLurFdg/nHAc5ju
E2qmdE/C7qpvvfjC3h4lKpUfziPuFsreEVHLByvvpPquZNA1IMQooZagGa9iqcDU
DbfQl+P0qEJGogW8TQIVAPmP8hki3WSMu2SrAWiGl9/HVXINAoGARhRjr7YRlJnn
dovge46zAohUiX5r5hFJtfvZmlAY8ryu/zbOLyJmuzUSGceKCaWCvNgdqhnSM2Nk
XF33Cp6HzeLaR3cv+mQG8EaGCreIw/XKUHHY8iUZ6FMcgkqwMlj0yITuOhcth1c/
O/U+9FspjNqgBFf7fcaXPuJNjl0Af24DgYQAAoGALFjW1xDCZkZokY8oO66rT1HG
xg3Va2L/fRaQqvuE7mjZATavv1GNNkyjaQkT4D60sGO5RZCn8cjSBJqBaEWZh8PX
N2pHpcp9VgHK7Qlo2CrXyw6Cija6Lo5o911ii0yZI6pslJtjVgXuuFx+3Uy49opr
hWe9Zghvdc6XvG5JNNGjIjAgMAkGA1UdEwQCMAAwEwYDVR0lBAwwCgYIKwYBBQUH
AwEwCwYJYIZIAWUDBAMCAy8AMCwCFBO06o94leJkLHUd9CTHvR44u71xAhQcsL2J
T6Zq7gHpIoYz8QYs8qbUPw==
-----END CERTIFICATE-----"
        set range global
        set source factory
        set last-updated 1737342328
    next
    edit "Fortinet_SSL_DSA2048"
        set password ENC 2X4L8FNk08NHcyerfLVkeLqLSzlROuBTmNwY6VW+dGZsfaesN3YsXvHhb+RB5ggnMLfcRXYypL1N/UPbRsILD3SEGFiLJg8AZy2z/7Wq2GIfbpPA9smNP8SYwTq8Vq6KqtM2/ELSNCdpcVjd7gKiVELY3irVsVNVR24jB5QqNakUEO2swIQinFbERAcMUrNkgjGWR1lmMjY3dkVA
        set comments "This certificate is embedded in the hardware at the factory and is unique to this unit. "
        set private-key "-----BEGIN PRIVATE KEY-----
MIICZAIBADCCAjkGByqGSM44BAEwggIsAoIBAQCjtt5vasRsbpyUwOk8ZYrJtEQC
hHY8lV4MQsJ/ukmjuXhR9bYy9qjVmL8eLu74KqQKAE0pV2LpzcL9eLY/S4Q/OG65
KfEDv5cMm8c3mlKHWMBwtswIHx7fHeuk9eL09WZB2k5agjcJPHPZJC5Fig+EHL3x
VZKvLtqtFe+2TxP0fiZnJEKfO/tgU7y/M9JsjqCag4ccI2oK98SX/P/XWdwP3UFt
Zd0bP/utU+MxsRMvjPeSrilirC5c3dNJFTN0FdIX7kAusIPYt14VR+JTAVZGQFo9
l51SjNg0HcAE0gbjVCLTX6QrqCsbpIU9K2ogVV+GaZTvJhO6TpP+RxQCGpWjAiEA
0/nJgvkIOMyX5Dkej9/1UbdbaN2BF834cUvH8ycZrEECggEAHlJTXnsjEc7gkfnF
ArNshmyKL+xngoJ2/p9ey//mXDJ171TDm242/UWj4HbjoBqGm1ZahIElgqjSkatM
V1of3M2PjIw9h9zEy79OOCVz1wdGC3dz2MmLoBfAGHIGQxYXZp93Keaxrg7fph0a
y9peLC/6ClUR5I2qCjyfhPDQgMEPsesWAtTsA8KhSQxSsXh/9Xskuz2iZyKl60ht
OwZx9zsjSbsUAOE8Qi0ghyICWg3OhATPXjGUxGfPN3416/32gG+fjAZKBdE3nbhg
WIvkKc1/CxD8b6fko6i1d1OWaaPz5HLB6iztQCKci9R81mvbEQvJEIUXkAeJaQpB
BfjsCgQiAiAiY6TGsaWB9CHVujlg+hh2/lv83JACB7dTw08wMoljAA==
-----END PRIVATE KEY-----"
        set certificate "-----BEGIN CERTIFICATE-----
MIIFRDCCBOqgAwIBAgIIco9zpjeb6kowCwYJYIZIAWUDBAMCMIGdMQswCQYDVQQG
EwJVUzETMBEGA1UECAwKQ2FsaWZvcm5pYTESMBAGA1UEBwwJU3Vubnl2YWxlMREw
DwYDVQQKDAhGb3J0aW5ldDESMBAGA1UECwwJRm9ydGlHYXRlMRkwFwYDVQQDDBBG
RzEwMEZUSzIyMDMxNDAzMSMwIQYJKoZIhvcNAQkBFhRzdXBwb3J0QGZvcnRpbmV0
LmNvbTAeFw0yNTAxMjAwMzA1MTFaFw0yNzA0MjUwMzA1MTFaMIGdMQswCQYDVQQG
EwJVUzETMBEGA1UECAwKQ2FsaWZvcm5pYTESMBAGA1UEBwwJU3Vubnl2YWxlMREw
DwYDVQQKDAhGb3J0aW5ldDESMBAGA1UECwwJRm9ydGlHYXRlMRkwFwYDVQQDDBBG
RzEwMEZUSzIyMDMxNDAzMSMwIQYJKoZIhvcNAQkBFhRzdXBwb3J0QGZvcnRpbmV0
LmNvbTCCA0YwggI5BgcqhkjOOAQBMIICLAKCAQEAo7beb2rEbG6clMDpPGWKybRE
AoR2PJVeDELCf7pJo7l4UfW2Mvao1Zi/Hi7u+CqkCgBNKVdi6c3C/Xi2P0uEPzhu
uSnxA7+XDJvHN5pSh1jAcLbMCB8e3x3rpPXi9PVmQdpOWoI3CTxz2SQuRYoPhBy9
8VWSry7arRXvtk8T9H4mZyRCnzv7YFO8vzPSbI6gmoOHHCNqCvfEl/z/11ncD91B
bWXdGz/7rVPjMbETL4z3kq4pYqwuXN3TSRUzdBXSF+5ALrCD2LdeFUfiUwFWRkBa
PZedUozYNB3ABNIG41Qi01+kK6grG6SFPStqIFVfhmmU7yYTuk6T/kcUAhqVowIh
ANP5yYL5CDjMl+Q5Ho/f9VG3W2jdgRfN+HFLx/MnGaxBAoIBAB5SU157IxHO4JH5
xQKzbIZsii/sZ4KCdv6fXsv/5lwyde9Uw5tuNv1Fo+B246AahptWWoSBJYKo0pGr
TFdaH9zNj4yMPYfcxMu/Tjglc9cHRgt3c9jJi6AXwBhyBkMWF2afdynmsa4O36Yd
GsvaXiwv+gpVEeSNqgo8n4Tw0IDBD7HrFgLU7APCoUkMUrF4f/V7JLs9omcipetI
bTsGcfc7I0m7FADhPEItIIciAloNzoQEz14xlMRnzzd+Nev99oBvn4wGSgXRN524
YFiL5CnNfwsQ/G+n5KOotXdTlmmj8+Ryweos7UAinIvUfNZr2xELyRCFF5AHiWkK
QQX47AoDggEFAAKCAQBLxuerNZp02MOLo8kpv0Ct2svUZrzL5mO8ElrZoKzmObgz
VY5JJw3r+ae5XdQlJvIPyJFgLw91gsF5CTuBt0ruZSTSvodYOa/xEwXlLRPuIRNG
QAH8iFKnfDrYo1yOcUH3h/vqcqlFDrDz1mnDgOSIqF7a/F6WfT5tOXyZDqRuGm1i
95tl//GEUqyRNWllJK/NbaADSl/ZhQx7TGrtbGfzwoqKjIzZmDaFrbOulpG/xqy6
TO6MzN1g4eAw5UOD+TtNBx7v3X4upM7TnmbKzbCnn8VJwCf2Mvno8qABsPQMERow
TM8opLe/53m2yXJBuf3Yz5jQCoFyyxNYX51dodKUoyIwIDAJBgNVHRMEAjAAMBMG
A1UdJQQMMAoGCCsGAQUFBwMBMAsGCWCGSAFlAwQDAgNHADBEAiBLQmzxKrMI9c6R
+bUWM8O/S9lzcMraKJth9l1xb1ERmAIgImb1rG88HJA0vbQKP6163hxcj5X37OTm
VwCwnpuLjog=
-----END CERTIFICATE-----"
        set range global
        set source factory
        set last-updated 1737342328
    next
    edit "Fortinet_SSL_ECDSA256"
        set password ENC 9ro8Kj7gg3a/ux8v2kTBWDU6I9rpcRknDVsGuD8IC2FmCqQz6q+QNZeLxEA0iy4XCDfVvdPS6WZ0E6nc7EX/y7MPLTI++K9gxUjrIis3CiArTs+0eLu0Q5n9/jcgkyaPRyYSMupL+70hPCV4LrahhdeHVTrlRJwJAKEmhsvW9juPpom/cl/3sVH27uedTb5Nr7PtpllmMjY3dkVA
        set comments "This certificate is embedded in the hardware at the factory and is unique to this unit. "
        set private-key "************************************************************************************************************************************************************************************************************************************************"
        set certificate "-----BEGIN CERTIFICATE-----
MIICVDCCAfqgAwIBAgIIaFv2oq8TrJUwCgYIKoZIzj0EAwIwgZ0xCzAJBgNVBAYT
AlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQHDAlTdW5ueXZhbGUxETAP
BgNVBAoMCEZvcnRpbmV0MRIwEAYDVQQLDAlGb3J0aUdhdGUxGTAXBgNVBAMMEEZH
MTAwRlRLMjIwMzE0MDMxIzAhBgkqhkiG9w0BCQEWFHN1cHBvcnRAZm9ydGluZXQu
Y29tMB4XDTI1MDEyMDAzMDUxMVoXDTI3MDQyNTAzMDUxMVowgZ0xCzAJBgNVBAYT
AlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQHDAlTdW5ueXZhbGUxETAP
BgNVBAoMCEZvcnRpbmV0MRIwEAYDVQQLDAlGb3J0aUdhdGUxGTAXBgNVBAMMEEZH
MTAwRlRLMjIwMzE0MDMxIzAhBgkqhkiG9w0BCQEWFHN1cHBvcnRAZm9ydGluZXQu
Y29tMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAER2oC1AVsrLmPNIm0R9WWPJOI
tso6uZgoAFLrudfZcvVCK0B5JE6fdhZFPNiW2LRyt0Xa0MYRej63bzkqcED3p6Mi
MCAwCQYDVR0TBAIwADATBgNVHSUEDDAKBggrBgEFBQcDATAKBggqhkjOPQQDAgNI
ADBFAiEA8SN4kOy0JPDZ2hNUcd7ZkiXkp9D1TcESOI4s9tot2QYCIDvS35KuasWK
9pZ6RsK/KsZtyOoKqEtEKu2vmJBwdeSF
-----END CERTIFICATE-----"
        set range global
        set source factory
        set last-updated 1737342328
    next
    edit "Fortinet_SSL_ECDSA384"
        set password ENC D7ZnejkKN5uvi5k+0yVjwjPe8jgHMrHUgjQUlZwdhcG/w5fPZl0IUqgGtmoiWTUQfKDr3biQk2Ywqv2x0kOHLPxlqpeqR4ehxjWWc6V8eQrFsahBxQeh2j1pSXg8jP3+R2gzsVpJxpfLODndqzroI48ZGDdKjB5gNp6SfWeaGKKf+dR91ulAoOTSD+ItKtWj/G/Lh1lmMjY3dkVA
        set comments "This certificate is embedded in the hardware at the factory and is unique to this unit. "
        set private-key "*****************************************************************************************************************************************************************************************************************************************************************************************************************"
        set certificate "-----BEGIN CERTIFICATE-----
MIICkTCCAhegAwIBAgIIDPE+WhJNnA0wCgYIKoZIzj0EAwIwgZ0xCzAJBgNVBAYT
AlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQHDAlTdW5ueXZhbGUxETAP
BgNVBAoMCEZvcnRpbmV0MRIwEAYDVQQLDAlGb3J0aUdhdGUxGTAXBgNVBAMMEEZH
MTAwRlRLMjIwMzE0MDMxIzAhBgkqhkiG9w0BCQEWFHN1cHBvcnRAZm9ydGluZXQu
Y29tMB4XDTI1MDEyMDAzMDUxMVoXDTI3MDQyNTAzMDUxMVowgZ0xCzAJBgNVBAYT
AlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQHDAlTdW5ueXZhbGUxETAP
BgNVBAoMCEZvcnRpbmV0MRIwEAYDVQQLDAlGb3J0aUdhdGUxGTAXBgNVBAMMEEZH
MTAwRlRLMjIwMzE0MDMxIzAhBgkqhkiG9w0BCQEWFHN1cHBvcnRAZm9ydGluZXQu
Y29tMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEDGsTGQ5c3KCkGvhqH9DKui/8rwfq
s3rrv2JVidha8NJGoj6nFVJBTTw1fVKEpMpXGastLfeB6/ioufTmYOsAmhNgL29/
xMkUfp1mw/Xd1ocuHb8trAEYJKQPbtG1bcd0oyIwIDAJBgNVHRMEAjAAMBMGA1Ud
JQQMMAoGCCsGAQUFBwMBMAoGCCqGSM49BAMCA2gAMGUCMGqZqfQxqEQ9h6IOZK4G
WOg5H07uj7GwMJYgE0zYjg/vBDQMBEGbYWz/Sbb/63FQBwIxAK9qwI55MfdzZtuU
zv2S5XvrNbDkuyG15xRTHMjRn31jj3NFTwmPMIykFr9mZCun7w==
-----END CERTIFICATE-----"
        set range global
        set source factory
        set last-updated 1737342328
    next
    edit "Fortinet_SSL_ECDSA521"
        set password ENC FEYCRrw2W5D+LKzzHnw0p6bZ7aQFgZXLHU1e5CucU55sONO/8VeLm+0pF0b7VMbd2DpjdGbV4yZrmB+SBnPqe6C0MxtSUHSbLX2QlgbFK3vmeQQ8G6tWR1EWV2IF4buSyp89RBfjvHEsJRf2iGCZJreW/z8ZH1bUJ39IZkcLFOGWIG0saNEjIHs3lbrAL//Mn0Tux1lmMjY3dkVA
        set comments "This certificate is embedded in the hardware at the factory and is unique to this unit. "
        set private-key "***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
        set certificate "-----BEGIN CERTIFICATE-----
MIIC3DCCAj2gAwIBAgIIAjS4wGoKywQwCgYIKoZIzj0EAwIwgZ0xCzAJBgNVBAYT
AlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQHDAlTdW5ueXZhbGUxETAP
BgNVBAoMCEZvcnRpbmV0MRIwEAYDVQQLDAlGb3J0aUdhdGUxGTAXBgNVBAMMEEZH
MTAwRlRLMjIwMzE0MDMxIzAhBgkqhkiG9w0BCQEWFHN1cHBvcnRAZm9ydGluZXQu
Y29tMB4XDTI1MDEyMDAzMDUxMVoXDTI3MDQyNTAzMDUxMVowgZ0xCzAJBgNVBAYT
AlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQHDAlTdW5ueXZhbGUxETAP
BgNVBAoMCEZvcnRpbmV0MRIwEAYDVQQLDAlGb3J0aUdhdGUxGTAXBgNVBAMMEEZH
MTAwRlRLMjIwMzE0MDMxIzAhBgkqhkiG9w0BCQEWFHN1cHBvcnRAZm9ydGluZXQu
Y29tMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQBet9O1ePKK9PXytQCEi77EaD1
Qz5Yv+M/cAvmzSHxhLgynZkZRqUDU8mKA9cl8MxJJYFaSvQE9MnXAl5oefJOHXwB
v327+EHQa2lthmf2niw7ZRVT6AyBHsuf5cY3i2/WB508JoPcfHIHQJEzauCUKg7L
XF51O+KhmgkqQbMRRg1/QjmjIjAgMAkGA1UdEwQCMAAwEwYDVR0lBAwwCgYIKwYB
BQUHAwEwCgYIKoZIzj0EAwIDgYwAMIGIAkIAy27qH/U8Hr145nZVK2IEJXg5qTao
LRNeF5aRsYlIkK43SI5ObG0jir0OKD0wKS233dXBwVfw7DB74fI1Fq17OoECQgHo
GmNhHxVNdUuHN+iTMnta+kb3RKGM+c7jPiSkmj33K0B75w1+FfSvVTM2V20pys/4
8RC809uezZxbn7JCqE3GRg==
-----END CERTIFICATE-----"
        set range global
        set source factory
        set last-updated 1737342328
    next
    edit "Fortinet_SSL_ED25519"
        set password ENC hO+YV2lMYhtg3cH0wJuWNBbgOmMGBJT8tXmGYAvOQ95L1youVvRbQ4Jpr04NtYf7V6ndIoECAT/P+BdlE7sbZ1CNB+J3z7AaJ7ry/fgf1Y7/6JOOSg4nfF23M0gmKdCKTA8amxjT1MNiD0qKe7EdAe7ipBTy/NIssVYgCBn6/F+6zocsP8QV6/tN5X1n5yGEu0bec1lmMjY3dkVA
        set comments "This certificate is embedded in the hardware at the factory and is unique to this unit. "
        set private-key "-----BEGIN PRIVATE KEY-----
MC4CAQAwBQYDK2VwBCIEIHs7g4PW6W6tNeMHni46JdlM/nMJlLGuvEy5ynmtMlTW
-----END PRIVATE KEY-----"
        set certificate "-----BEGIN CERTIFICATE-----
MIICFDCCAcagAwIBAgIIQ5YuRuBuAR0wBQYDK2VwMIGdMQswCQYDVQQGEwJVUzET
MBEGA1UECAwKQ2FsaWZvcm5pYTESMBAGA1UEBwwJU3Vubnl2YWxlMREwDwYDVQQK
DAhGb3J0aW5ldDESMBAGA1UECwwJRm9ydGlHYXRlMRkwFwYDVQQDDBBGRzEwMEZU
SzIyMDMxNDAzMSMwIQYJKoZIhvcNAQkBFhRzdXBwb3J0QGZvcnRpbmV0LmNvbTAe
Fw0yNTAxMjAwMzA1MTFaFw0yNzA0MjUwMzA1MTFaMIGdMQswCQYDVQQGEwJVUzET
MBEGA1UECAwKQ2FsaWZvcm5pYTESMBAGA1UEBwwJU3Vubnl2YWxlMREwDwYDVQQK
DAhGb3J0aW5ldDESMBAGA1UECwwJRm9ydGlHYXRlMRkwFwYDVQQDDBBGRzEwMEZU
SzIyMDMxNDAzMSMwIQYJKoZIhvcNAQkBFhRzdXBwb3J0QGZvcnRpbmV0LmNvbTAq
MAUGAytlcAMhADNwzdyP5iWwdngKLJ7Xsaa7+Fre2hPmpqe1rVWFis8GoyIwIDAJ
BgNVHRMEAjAAMBMGA1UdJQQMMAoGCCsGAQUFBwMBMAUGAytlcANBALZjGIV+8lek
sMEDLRSfCyCKfz5/yLWEO5cjuROdZbksxWQIP4pD/gWn8oHW0bfYtVE6V5cjqNg0
8MotZv8gCw0=
-----END CERTIFICATE-----"
        set range global
        set source factory
        set last-updated 1737342328
    next
    edit "Fortinet_SSL_ED448"
        set password ENC Zf9P+2G36/0aiW9Mff1wjzE+dY2x1Gtb7WWWoCsaM2s6zhjjHrZyGx+ru3YRXP0lk3dciGw66J5aX/841vV2840OZlRuVoaVbvFzzxNySMASxebIfbwvn/0HlLZ20ZWeqlM9jXiHwtZXXTqKHl6KQypESXoWr5usR0JmcNmbBJ/kAc/Z4iwDFWdXIy9aDRIBhwx3oFlmMjY3dkVA
        set comments "This certificate is embedded in the hardware at the factory and is unique to this unit. "
        set private-key "-----BEGIN PRIVATE KEY-----
MEcCAQAwBQYDK2VxBDsEOcl4WV6JEVlbMz0YzhTlDOUwgA0wDbd9sEkUu4M0Hv4+
Rvt70BNeZqIyrsR1yXMVhO4ZYSix30KdeQ==
-----END PRIVATE KEY-----"
        set certificate "-----BEGIN CERTIFICATE-----
MIICXzCCAd+gAwIBAgIIYShGe8a+Za4wBQYDK2VxMIGdMQswCQYDVQQGEwJVUzET
MBEGA1UECAwKQ2FsaWZvcm5pYTESMBAGA1UEBwwJU3Vubnl2YWxlMREwDwYDVQQK
DAhGb3J0aW5ldDESMBAGA1UECwwJRm9ydGlHYXRlMRkwFwYDVQQDDBBGRzEwMEZU
SzIyMDMxNDAzMSMwIQYJKoZIhvcNAQkBFhRzdXBwb3J0QGZvcnRpbmV0LmNvbTAe
Fw0yNTAxMjAwMzA1MTFaFw0yNzA0MjUwMzA1MTFaMIGdMQswCQYDVQQGEwJVUzET
MBEGA1UECAwKQ2FsaWZvcm5pYTESMBAGA1UEBwwJU3Vubnl2YWxlMREwDwYDVQQK
DAhGb3J0aW5ldDESMBAGA1UECwwJRm9ydGlHYXRlMRkwFwYDVQQDDBBGRzEwMEZU
SzIyMDMxNDAzMSMwIQYJKoZIhvcNAQkBFhRzdXBwb3J0QGZvcnRpbmV0LmNvbTBD
MAUGAytlcQM6APd7EKPAJCx4oTV/WFuEbR17GpUFRyn9+M5YTBzG5OOstZAU7H8B
JoJuIn/VuqKd21vMxj1FnWWMgKMiMCAwCQYDVR0TBAIwADATBgNVHSUEDDAKBggr
BgEFBQcDATAFBgMrZXEDcwAoMD74kPTsqjKdvHxrPeZ16aVvO1Ex4Wicm3a9blIZ
s1Mmzue0Y/WTugcZFWnh+CjQo+Tto5uRzAAjk/UH0gZz99v7JISI5dLvjH91zhDl
vTNADX1ikTI48WGcp/WNzuCdSL1QWQ2BL1+EKvkLtLD9KAA=
-----END CERTIFICATE-----"
        set range global
        set source factory
        set last-updated 1737342328
    next
end
config webfilter ftgd-local-cat
    edit "custom1"
        set id 140
    next
    edit "custom2"
        set id 141
    next
end
config ips sensor
    edit "default"
        set comment "Prevent critical attacks."
        config entries
            edit 1
                set severity medium high critical
            next
        end
    next
    edit "sniffer-profile"
        set comment "Monitor IPS attacks."
        config entries
            edit 1
                set location server client
                set default-status enable
                set status enable
                set log-packet enable
                set action pass
            next
        end
    next
    edit "wifi-default"
        set comment "Default configuration for offloading WiFi traffic."
        config entries
            edit 1
                set severity medium high critical
            next
        end
    next
    edit "all_default"
        set comment "All predefined signatures with default setting."
        config entries
            edit 1
            next
        end
    next
    edit "all_default_pass"
        set comment "All predefined signatures with PASS action."
        config entries
            edit 1
                set action pass
            next
        end
    next
    edit "protect_http_server"
        set comment "Protect against HTTP server-side vulnerabilities."
        config entries
            edit 1
                set location server
                set protocol HTTP
            next
        end
    next
    edit "protect_email_server"
        set comment "Protect against email server-side vulnerabilities."
        config entries
            edit 1
                set location server
                set protocol SMTP POP3 IMAP
            next
        end
    next
    edit "protect_client"
        set comment "Protect against client-side vulnerabilities."
        config entries
            edit 1
                set location client
            next
        end
    next
    edit "high_security"
        set comment "Blocks all Critical/High/Medium and some Low severity vulnerabilities"
        set block-malicious-url enable
        config entries
            edit 1
                set severity medium high critical
                set status enable
                set action block
            next
            edit 2
                set severity low
            next
        end
    next
end
config firewall shaper traffic-shaper
    edit "high-priority"
        set maximum-bandwidth 1048576
        set per-policy enable
    next
    edit "medium-priority"
        set maximum-bandwidth 1048576
        set priority medium
        set per-policy enable
    next
    edit "low-priority"
        set maximum-bandwidth 1048576
        set priority low
        set per-policy enable
    next
    edit "guarantee-100kbps"
        set guaranteed-bandwidth 100
        set maximum-bandwidth 1048576
        set per-policy enable
    next
    edit "shared-1M-pipe"
        set maximum-bandwidth 1024
    next
end
config firewall proxy-address
    edit "IPv4-address"
        set uuid 69813140-d6db-51ef-3aef-91665c2167f2
        set type host-regex
        set host-regex "^([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])(\\.([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])){3}$"
    next
    edit "IPv6-address"
        set uuid 6981473e-d6db-51ef-3284-8a1d2c23cc63
        set type host-regex
        set host-regex "^\\[(([0-9a-f]{0,4}:){1,7}[0-9a-f]{1,4})\\]$"
    next
end
config web-proxy global
    set proxy-fqdn "default.fqdn"
end
config application list
    edit "default"
        set comment "Monitor all applications."
        config entries
            edit 1
                set action pass
            next
        end
    next
    edit "sniffer-profile"
        set comment "Monitor all applications."
        unset options
        config entries
            edit 1
                set action pass
            next
        end
    next
    edit "wifi-default"
        set comment "Default configuration for offloading WiFi traffic."
        set deep-app-inspection disable
        config entries
            edit 1
                set action pass
                set log disable
            next
        end
    next
    edit "block-high-risk"
        config entries
            edit 1
                set category 2 6
            next
            edit 2
                set action pass
            next
        end
    next
end
config dlp data-type
    edit "keyword"
        set pattern "built-in"
    next
    edit "regex"
        set pattern "built-in"
    next
    edit "hex"
        set pattern "built-in"
    next
    edit "mip-label"
        set pattern "^[[:xdigit:]]{8}-[[:xdigit:]]{4}-[[:xdigit:]]{4}-[[:xdigit:]]{4}-[[:xdigit:]]{12}$"
        set transform "built-in"
    next
    edit "credit-card"
        set pattern "\\b([2-6]{1}\\d{3})[- ]?(\\d{4})[- ]?(\\d{2})[- ]?(\\d{2})[- ]?(\\d{2,4})\\b"
        set verify "builtin)credit-card"
        set look-back 20
        set transform "\\b\\1[- ]?\\2[- ]?\\3[- ]?\\4[- ]?\\5\\b"
    next
    edit "ssn-us"
        set pattern "\\b(\\d{3})-(\\d{2})-(\\d{4})\\b"
        set verify "(?<!-)\\b(?!666|000|9\\d{2})\\d{3}-(?!00)\\d{2}-(?!0{4})\\d{4}\\b(?!-)"
        set look-back 12
        set transform "\\b\\1-\\2-\\3\\b"
    next
    edit "edm-keyword"
        set pattern ".+"
        set transform "/\\0/i"
    next
end
config dlp filepattern
    edit 1
        set name "builtin-patterns"
        config entries
            edit "*.bat"
            next
            edit "*.com"
            next
            edit "*.dll"
            next
            edit "*.doc"
            next
            edit "*.exe"
            next
            edit "*.gz"
            next
            edit "*.hta"
            next
            edit "*.ppt"
            next
            edit "*.rar"
            next
            edit "*.scr"
            next
            edit "*.tar"
            next
            edit "*.tgz"
            next
            edit "*.vb?"
            next
            edit "*.wps"
            next
            edit "*.xl?"
            next
            edit "*.zip"
            next
            edit "*.pif"
            next
            edit "*.cpl"
            next
        end
    next
    edit 2
        set name "all_executables"
        config entries
            edit "bat"
                set filter-type type
                set file-type bat
            next
            edit "exe"
                set filter-type type
                set file-type exe
            next
            edit "elf"
                set filter-type type
                set file-type elf
            next
            edit "hta"
                set filter-type type
                set file-type hta
            next
        end
    next
end
config dlp profile
    edit "default"
        set comment "Default profile."
    next
    edit "sniffer-profile"
        set comment "Log a summary of email and web traffic."
        set summary-proto smtp pop3 imap http-get http-post
    next
    edit "Content_Summary"
        set summary-proto smtp pop3 imap http-get http-post ftp nntp cifs
    next
    edit "Content_Archive"
        set summary-proto smtp pop3 imap http-get http-post ftp nntp cifs
    next
    edit "Large-File"
        config rule
            edit 1
                set name "Large-File-Filter"
                set proto smtp pop3 imap http-get http-post
                set file-size 5120
                set action log-only
            next
        end
    next
end
config webfilter ips-urlfilter-setting
end
config webfilter ips-urlfilter-setting6
end
config log threat-weight
    config web
        edit 1
            set category 26
            set level high
        next
        edit 2
            set category 61
            set level high
        next
        edit 3
            set category 86
            set level high
        next
        edit 4
            set category 1
            set level medium
        next
        edit 5
            set category 3
            set level medium
        next
        edit 6
            set category 4
            set level medium
        next
        edit 7
            set category 5
            set level medium
        next
        edit 8
            set category 6
            set level medium
        next
        edit 9
            set category 12
            set level medium
        next
        edit 10
            set category 59
            set level medium
        next
        edit 11
            set category 62
            set level medium
        next
        edit 12
            set category 83
            set level medium
        next
        edit 13
            set category 72
        next
        edit 14
            set category 14
        next
        edit 15
            set category 96
            set level medium
        next
    end
    config application
        edit 1
            set category 2
        next
        edit 2
            set category 6
            set level medium
        next
    end
end
config icap profile
    edit "default"
        config icap-headers
            edit 1
                set name "X-Authenticated-User"
                set content "$auth_user_uri"
                set base64-encoding enable
            next
            edit 2
                set name "X-Authenticated-Groups"
                set content "$auth_group_uri"
                set base64-encoding enable
            next
        end
    next
end
config user fortitoken
    edit "FTKMOB52F9AB2F2C"
        set license "FTMTRIAL047A4B48"
    next
    edit "FTKMOB524AEA6D82"
        set license "FTMTRIAL047A4B48"
    next
end
config user local
    edit "guest"
        set type password
        set passwd ENC nwJpnH7WlHb7LfDeHEyITVljZ76J2eN1YCXJhemy7oMWYbP0axo7G9lBsTklCW3cpatvSiF8OGePwCpG/aioNMvfEVhSNzAohxe+T8TFKgDvS+nVqkhZDNByFErnjiihuyHY3/PASoJfG3A4AX7MKLu3Kh/M9jT7w7tuSdIp1M8jq234yJrGFFjqusan2INp2rVt5FlmMjY3dkVA
    next
    edit "1"
        set type password
        set passwd-time 2025-04-28 14:21:18
        set passwd ENC RhQnY37jaD6CrxSxGOTcWMaWnYoRcHylaTPyX1/3s+nCxBxcZRT0bW+BKdNiVd53KL/Rst0y+utdtWHuuS6BtHJvlZdL4B8yJqHGndEUbS/5G76vd4XHhzMz3ZfBAhDdLafTa5lwRbPjzAHsoL2nR2UpfgTk3qVPs72iR6OGp8OHRUWbctyqAgAhR2vm6svXSR9Ef1lmMjY3dkVA
    next
end
config user setting
    set auth-cert "Fortinet_Factory"
end
config user group
    edit "SSO_Guest_Users"
    next
    edit "Guest-group"
        set member "guest"
    next
end
config vpn ssl web portal
    edit "full-access"
        set web-mode enable
    next
    edit "web-access"
        set web-mode enable
    next
    edit "tunnel-access"
    next
end
config vpn ssl settings
    set banned-cipher SHA1 SHA256 SHA384
    set servercert ''
    set port 443
end
config voip profile
    edit "default"
        set comment "Default VoIP profile."
        config sip
        end
    next
    edit "strict"
        config sip
            set malformed-request-line discard
            set malformed-header-via discard
            set malformed-header-from discard
            set malformed-header-to discard
            set malformed-header-call-id discard
            set malformed-header-cseq discard
            set malformed-header-rack discard
            set malformed-header-rseq discard
            set malformed-header-contact discard
            set malformed-header-record-route discard
            set malformed-header-route discard
            set malformed-header-expires discard
            set malformed-header-content-type discard
            set malformed-header-content-length discard
            set malformed-header-max-forwards discard
            set malformed-header-allow discard
            set malformed-header-p-asserted-identity discard
            set malformed-header-sdp-v discard
            set malformed-header-sdp-o discard
            set malformed-header-sdp-s discard
            set malformed-header-sdp-i discard
            set malformed-header-sdp-c discard
            set malformed-header-sdp-b discard
            set malformed-header-sdp-z discard
            set malformed-header-sdp-k discard
            set malformed-header-sdp-a discard
            set malformed-header-sdp-t discard
            set malformed-header-sdp-r discard
            set malformed-header-sdp-m discard
        end
    next
end
config system sdwan
    config zone
        edit "virtual-wan-link"
        next
    end
    config health-check
        edit "Default_DNS"
            set system-dns enable
            set interval 1000
            set probe-timeout 1000
            set recoverytime 10
            config sla
                edit 1
                    set latency-threshold 250
                    set jitter-threshold 50
                    set packetloss-threshold 5
                next
            end
        next
        edit "Default_Office_365"
            set server "www.office.com"
            set protocol https
            set interval 120000
            set probe-timeout 120000
            set recoverytime 10
            config sla
                edit 1
                    set latency-threshold 250
                    set jitter-threshold 50
                    set packetloss-threshold 5
                next
            end
        next
        edit "Default_Gmail"
            set server "gmail.com"
            set interval 1000
            set probe-timeout 1000
            set recoverytime 10
            config sla
                edit 1
                    set latency-threshold 250
                    set jitter-threshold 50
                    set packetloss-threshold 2
                next
            end
        next
        edit "Default_Google Search"
            set server "www.google.com"
            set protocol https
            set interval 120000
            set probe-timeout 120000
            set recoverytime 10
            config sla
                edit 1
                    set latency-threshold 250
                    set jitter-threshold 50
                    set packetloss-threshold 5
                next
            end
        next
        edit "Default_FortiGuard"
            set server "fortiguard.com"
            set protocol https
            set interval 120000
            set probe-timeout 120000
            set recoverytime 10
            config sla
                edit 1
                    set latency-threshold 250
                    set jitter-threshold 50
                    set packetloss-threshold 5
                next
            end
        next
    end
end
config dnsfilter profile
    edit "default"
        set comment "Default dns filtering."
        config ftgd-dns
            config filters
                edit 1
                    set category 2
                next
                edit 2
                    set category 7
                next
                edit 3
                    set category 8
                next
                edit 4
                    set category 9
                next
                edit 5
                    set category 11
                next
                edit 6
                    set category 12
                next
                edit 7
                    set category 13
                next
                edit 8
                    set category 14
                next
                edit 9
                    set category 15
                next
                edit 10
                    set category 16
                next
                edit 11
                next
                edit 12
                    set category 57
                next
                edit 13
                    set category 63
                next
                edit 14
                    set category 64
                next
                edit 15
                    set category 65
                next
                edit 16
                    set category 66
                next
                edit 17
                    set category 67
                next
                edit 18
                    set category 26
                    set action block
                next
                edit 19
                    set category 61
                    set action block
                next
                edit 20
                    set category 86
                    set action block
                next
                edit 21
                    set category 88
                    set action block
                next
                edit 22
                    set category 90
                    set action block
                next
                edit 23
                    set category 91
                    set action block
                next
                edit 24
                    set category 96
                    set action block
                next
                edit 25
                    set category 98
                    set action block
                next
                edit 26
                    set category 99
                    set action block
                next
            end
        end
        set block-botnet enable
    next
end
config antivirus settings
    set machine-learning-detection enable
end
config antivirus profile
    edit "default"
        set comment "Scan files and block viruses."
        config http
            set av-scan block
        end
        config ftp
            set av-scan block
        end
        config imap
            set av-scan block
            set executables virus
        end
        config pop3
            set av-scan block
            set executables virus
        end
        config smtp
            set av-scan block
            set executables virus
        end
    next
    edit "sniffer-profile"
        set comment "Scan files and monitor viruses."
        config http
            set av-scan monitor
        end
        config ftp
            set av-scan monitor
        end
        config imap
            set av-scan monitor
            set executables virus
        end
        config pop3
            set av-scan monitor
            set executables virus
        end
        config smtp
            set av-scan monitor
            set executables virus
        end
        config cifs
            set av-scan monitor
        end
    next
    edit "wifi-default"
        set comment "Default configuration for offloading WiFi traffic."
        config http
            set av-scan block
        end
        config ftp
            set av-scan block
        end
        config imap
            set av-scan block
            set executables virus
        end
        config pop3
            set av-scan block
            set executables virus
        end
        config smtp
            set av-scan block
            set executables virus
        end
    next
end
config file-filter profile
    edit "default"
        set comment "File type inspection."
    next
    edit "sniffer-profile"
        set comment "File type inspection."
    next
end
config webfilter ftgd-risk-level
    edit "high"
        set high 100
        set low 91
    next
    edit "suspicious"
        set high 90
        set low 71
    next
    edit "moderate"
        set high 70
        set low 51
    next
    edit "low"
        set high 50
        set low 21
    next
    edit "trustworthy"
        set high 20
        set low 1
    next
    edit "unrated"
    next
end
config webfilter profile
    edit "default"
        set comment "Default web filtering."
        config ftgd-wf
            unset options
            config filters
                edit 1
                    set action block
                next
                edit 2
                    set category 2
                    set action block
                next
                edit 3
                    set category 7
                    set action block
                next
                edit 4
                    set category 8
                    set action block
                next
                edit 5
                    set category 9
                    set action block
                next
                edit 6
                    set category 11
                    set action block
                next
                edit 7
                    set category 13
                    set action block
                next
                edit 8
                    set category 14
                    set action block
                next
                edit 9
                    set category 15
                    set action block
                next
                edit 10
                    set category 16
                    set action block
                next
                edit 11
                    set category 26
                    set action block
                next
                edit 12
                    set category 57
                    set action block
                next
                edit 13
                    set category 61
                    set action block
                next
                edit 14
                    set category 63
                    set action block
                next
                edit 15
                    set category 64
                    set action block
                next
                edit 16
                    set category 65
                    set action block
                next
                edit 17
                    set category 66
                    set action block
                next
                edit 18
                    set category 67
                    set action block
                next
                edit 19
                    set category 83
                    set action block
                next
                edit 20
                    set category 86
                    set action block
                next
                edit 21
                    set category 88
                    set action block
                next
                edit 22
                    set category 90
                    set action block
                next
                edit 23
                    set category 91
                    set action block
                next
                edit 24
                    set category 96
                    set action block
                next
                edit 25
                    set category 98
                    set action block
                next
                edit 26
                    set category 99
                    set action block
                next
                edit 27
                    set category 1
                next
                edit 28
                    set category 3
                next
                edit 29
                    set category 4
                next
                edit 30
                    set category 5
                next
                edit 31
                    set category 6
                next
                edit 32
                    set category 12
                next
                edit 33
                    set category 59
                next
                edit 34
                    set category 62
                next
            end
        end
    next
    edit "monitor-all"
        set comment "Monitor and log all visited URLs, flow-based."
        config ftgd-wf
            unset options
            config filters
                edit 1
                    set category 1
                next
                edit 2
                    set category 3
                next
                edit 3
                    set category 4
                next
                edit 4
                    set category 5
                next
                edit 5
                    set category 6
                next
                edit 6
                    set category 12
                next
                edit 7
                    set category 59
                next
                edit 8
                    set category 62
                next
                edit 9
                    set category 83
                next
                edit 10
                    set category 2
                next
                edit 11
                    set category 7
                next
                edit 12
                    set category 8
                next
                edit 13
                    set category 9
                next
                edit 14
                    set category 11
                next
                edit 15
                    set category 13
                next
                edit 16
                    set category 14
                next
                edit 17
                    set category 15
                next
                edit 18
                    set category 16
                next
                edit 19
                    set category 57
                next
                edit 20
                    set category 63
                next
                edit 21
                    set category 64
                next
                edit 22
                    set category 65
                next
                edit 23
                    set category 66
                next
                edit 24
                    set category 67
                next
                edit 25
                    set category 19
                next
                edit 26
                    set category 24
                next
                edit 27
                    set category 25
                next
                edit 28
                    set category 72
                next
                edit 29
                    set category 75
                next
                edit 30
                    set category 76
                next
                edit 31
                    set category 26
                next
                edit 32
                    set category 61
                next
                edit 33
                    set category 86
                next
                edit 34
                    set category 17
                next
                edit 35
                    set category 18
                next
                edit 36
                    set category 20
                next
                edit 37
                    set category 23
                next
                edit 38
                    set category 28
                next
                edit 39
                    set category 29
                next
                edit 40
                    set category 30
                next
                edit 41
                    set category 33
                next
                edit 42
                    set category 34
                next
                edit 43
                    set category 35
                next
                edit 44
                    set category 36
                next
                edit 45
                    set category 37
                next
                edit 46
                    set category 38
                next
                edit 47
                    set category 39
                next
                edit 48
                    set category 40
                next
                edit 49
                    set category 42
                next
                edit 50
                    set category 44
                next
                edit 51
                    set category 46
                next
                edit 52
                    set category 47
                next
                edit 53
                    set category 48
                next
                edit 54
                    set category 54
                next
                edit 55
                    set category 55
                next
                edit 56
                    set category 58
                next
                edit 57
                    set category 68
                next
                edit 58
                    set category 69
                next
                edit 59
                    set category 70
                next
                edit 60
                    set category 71
                next
                edit 61
                    set category 77
                next
                edit 62
                    set category 78
                next
                edit 63
                    set category 79
                next
                edit 64
                    set category 80
                next
                edit 65
                    set category 82
                next
                edit 66
                    set category 85
                next
                edit 67
                    set category 87
                next
                edit 68
                    set category 31
                next
                edit 69
                    set category 41
                next
                edit 70
                    set category 43
                next
                edit 71
                    set category 49
                next
                edit 72
                    set category 50
                next
                edit 73
                    set category 51
                next
                edit 74
                    set category 52
                next
                edit 75
                    set category 53
                next
                edit 76
                    set category 56
                next
                edit 77
                    set category 81
                next
                edit 78
                    set category 84
                next
                edit 79
                next
                edit 80
                    set category 88
                next
                edit 81
                    set category 89
                next
                edit 82
                    set category 90
                next
                edit 83
                    set category 91
                next
                edit 84
                    set category 92
                next
                edit 85
                    set category 93
                next
                edit 86
                    set category 94
                next
                edit 87
                    set category 95
                next
                edit 88
                    set category 96
                next
                edit 89
                    set category 97
                next
                edit 90
                    set category 98
                next
                edit 91
                    set category 99
                next
                edit 92
                    set category 100
                next
                edit 93
                    set category 101
                next
            end
        end
        set log-all-url enable
        set web-content-log disable
        set web-filter-command-block-log disable
        set web-filter-cookie-log disable
        set web-url-log disable
        set web-invalid-domain-log disable
        set web-ftgd-err-log disable
    next
    edit "sniffer-profile"
        set comment "Monitor web traffic."
        config ftgd-wf
            config filters
                edit 1
                next
                edit 2
                    set category 1
                next
                edit 3
                    set category 2
                next
                edit 4
                    set category 3
                next
                edit 5
                    set category 4
                next
                edit 6
                    set category 5
                next
                edit 7
                    set category 6
                next
                edit 8
                    set category 7
                next
                edit 9
                    set category 8
                next
                edit 10
                    set category 9
                next
                edit 11
                    set category 11
                next
                edit 12
                    set category 12
                next
                edit 13
                    set category 13
                next
                edit 14
                    set category 14
                next
                edit 15
                    set category 15
                next
                edit 16
                    set category 16
                next
                edit 17
                    set category 17
                next
                edit 18
                    set category 18
                next
                edit 19
                    set category 19
                next
                edit 20
                    set category 20
                next
                edit 21
                    set category 23
                next
                edit 22
                    set category 24
                next
                edit 23
                    set category 25
                next
                edit 24
                    set category 26
                next
                edit 25
                    set category 28
                next
                edit 26
                    set category 29
                next
                edit 27
                    set category 30
                next
                edit 28
                    set category 31
                next
                edit 29
                    set category 33
                next
                edit 30
                    set category 34
                next
                edit 31
                    set category 35
                next
                edit 32
                    set category 36
                next
                edit 33
                    set category 37
                next
                edit 34
                    set category 38
                next
                edit 35
                    set category 39
                next
                edit 36
                    set category 40
                next
                edit 37
                    set category 41
                next
                edit 38
                    set category 42
                next
                edit 39
                    set category 43
                next
                edit 40
                    set category 44
                next
                edit 41
                    set category 46
                next
                edit 42
                    set category 47
                next
                edit 43
                    set category 48
                next
                edit 44
                    set category 49
                next
                edit 45
                    set category 50
                next
                edit 46
                    set category 51
                next
                edit 47
                    set category 52
                next
                edit 48
                    set category 53
                next
                edit 49
                    set category 54
                next
                edit 50
                    set category 55
                next
                edit 51
                    set category 56
                next
                edit 52
                    set category 57
                next
                edit 53
                    set category 58
                next
                edit 54
                    set category 59
                next
                edit 55
                    set category 61
                next
                edit 56
                    set category 62
                next
                edit 57
                    set category 63
                next
                edit 58
                    set category 64
                next
                edit 59
                    set category 65
                next
                edit 60
                    set category 66
                next
                edit 61
                    set category 67
                next
                edit 62
                    set category 68
                next
                edit 63
                    set category 69
                next
                edit 64
                    set category 70
                next
                edit 65
                    set category 71
                next
                edit 66
                    set category 72
                next
                edit 67
                    set category 75
                next
                edit 68
                    set category 76
                next
                edit 69
                    set category 77
                next
                edit 70
                    set category 78
                next
                edit 71
                    set category 79
                next
                edit 72
                    set category 80
                next
                edit 73
                    set category 81
                next
                edit 74
                    set category 82
                next
                edit 75
                    set category 83
                next
                edit 76
                    set category 84
                next
                edit 77
                    set category 85
                next
                edit 78
                    set category 86
                next
                edit 79
                    set category 87
                next
                edit 80
                    set category 88
                next
                edit 81
                    set category 89
                next
                edit 82
                    set category 90
                next
                edit 83
                    set category 91
                next
                edit 84
                    set category 92
                next
                edit 85
                    set category 93
                next
                edit 86
                    set category 94
                next
                edit 87
                    set category 95
                next
                edit 88
                    set category 96
                next
                edit 89
                    set category 97
                next
                edit 90
                    set category 98
                next
                edit 91
                    set category 99
                next
                edit 92
                    set category 100
                next
                edit 93
                    set category 101
                next
            end
        end
    next
    edit "wifi-default"
        set comment "Default configuration for offloading WiFi traffic."
        set options block-invalid-url
        config ftgd-wf
            unset options
            config filters
                edit 1
                    set action block
                next
                edit 2
                    set category 2
                    set action block
                next
                edit 3
                    set category 7
                    set action block
                next
                edit 4
                    set category 8
                    set action block
                next
                edit 5
                    set category 9
                    set action block
                next
                edit 6
                    set category 11
                    set action block
                next
                edit 7
                    set category 13
                    set action block
                next
                edit 8
                    set category 14
                    set action block
                next
                edit 9
                    set category 15
                    set action block
                next
                edit 10
                    set category 16
                    set action block
                next
                edit 11
                    set category 26
                    set action block
                next
                edit 12
                    set category 57
                    set action block
                next
                edit 13
                    set category 61
                    set action block
                next
                edit 14
                    set category 63
                    set action block
                next
                edit 15
                    set category 64
                    set action block
                next
                edit 16
                    set category 65
                    set action block
                next
                edit 17
                    set category 66
                    set action block
                next
                edit 18
                    set category 67
                    set action block
                next
                edit 19
                    set category 83
                    set action block
                next
                edit 20
                    set category 86
                    set action block
                next
                edit 21
                    set category 88
                    set action block
                next
                edit 22
                    set category 90
                    set action block
                next
                edit 23
                    set category 91
                    set action block
                next
                edit 24
                    set category 96
                    set action block
                next
                edit 25
                    set category 98
                    set action block
                next
                edit 26
                    set category 99
                    set action block
                next
                edit 27
                    set category 1
                next
                edit 28
                    set category 3
                next
                edit 29
                    set category 4
                next
                edit 30
                    set category 5
                next
                edit 31
                    set category 6
                next
                edit 32
                    set category 12
                next
                edit 33
                    set category 59
                next
                edit 34
                    set category 62
                next
            end
        end
    next
end
config webfilter search-engine
    edit "google"
        set hostname ".*\\.google\\..*"
        set url "^\\/((custom|search|images|videosearch|webhp)\\?)"
        set query "q="
        set safesearch url
        set safesearch-str "&safe=active"
    next
    edit "yahoo"
        set hostname ".*\\.yahoo\\..*"
        set url "^\\/search(\\/video|\\/images){0,1}(\\?|;)"
        set query "p="
        set safesearch url
        set safesearch-str "&vm=r"
    next
    edit "bing"
        set hostname ".*\\.bing\\..*"
        set url "^(\\/images|\\/videos)?(\\/search|\\/async|\\/asyncv2)\\?"
        set query "q="
        set safesearch header
    next
    edit "yandex"
        set hostname "yandex\\..*"
        set url "^\\/((|yand|images\\/|video\\/)(search)|search\\/)\\?"
        set query "text="
        set safesearch url
        set safesearch-str "&family=yes"
    next
    edit "youtube"
        set hostname ".*youtube.*"
        set safesearch header
    next
    edit "baidu"
        set hostname ".*\\.baidu\\.com"
        set url "^\\/s?\\?"
        set query "wd="
    next
    edit "baidu2"
        set hostname ".*\\.baidu\\.com"
        set url "^\\/(ns|q|m|i|v)\\?"
        set query "word="
    next
    edit "baidu3"
        set hostname "tieba\\.baidu\\.com"
        set url "^\\/f\\?"
        set query "kw="
    next
    edit "vimeo"
        set hostname ".*vimeo.*"
        set url "^\\/search\\?"
        set query "q="
        set safesearch header
    next
    edit "yt-scan-1"
        set url "www.youtube.com/user/"
        set safesearch yt-scan
    next
    edit "yt-scan-2"
        set url "www.youtube.com/youtubei/v1/browse"
        set safesearch yt-scan
    next
    edit "yt-scan-3"
        set url "www.youtube.com/youtubei/v1/player"
        set safesearch yt-scan
    next
    edit "yt-scan-4"
        set url "www.youtube.com/youtubei/v1/navigator"
        set safesearch yt-scan
    next
    edit "yt-channel"
        set url "www.youtube.com/channel"
        set safesearch yt-channel
    next
    edit "yt-pattern"
        set url "youtube.com/channel/"
        set safesearch yt-pattern
    next
    edit "twitter"
        set hostname "twitter\\.com"
        set url "^\\/i\\/api\\/graphql\\/.*\\/UserByScreenName"
        set query "variables="
        set safesearch translate
        set safesearch-str "regex::%22screen_name%22:%22([A-Za-z0-9_]{4,15})%22::twitter.com/\\1"
    next
    edit "google-translate-1"
        set hostname "translate\\.google\\..*"
        set url "^\\/translate"
        set query "u="
        set safesearch translate
        set safesearch-str "regex::(?:\\?|&)u=([^&]+)::\\1"
    next
    edit "google-translate-2"
        set hostname ".*\\.translate\\.goog"
        set url "^\\/"
        set safesearch translate
        set safesearch-str "case::google-translate"
    next
end
config emailfilter profile
    edit "sniffer-profile"
        set comment "Malware and phishing URL monitoring."
        config imap
        end
        config pop3
        end
        config smtp
        end
    next
    edit "default"
        set comment "Malware and phishing URL filtering."
        config imap
        end
        config pop3
        end
        config smtp
        end
    next
end
config virtual-patch profile
    edit "default"
    next
end
config log memory setting
    set status enable
end
config log eventfilter
    set system disable
    set vpn disable
    set user disable
    set router disable
    set wireless-activity disable
    set wan-opt disable
    set endpoint disable
    set ha disable
    set fortiextender disable
    set connector disable
    set sdwan disable
    set cifs disable
    set switch-controller disable
    set webproxy disable
end
config log null-device setting
    set status disable
end
config log setting
    set local-out disable
end
config log gui-display
    set resolve-hosts disable
    set resolve-apps disable
end
config firewall schedule recurring
    edit "always"
        set uuid 66f9aaa6-d6db-51ef-17b3-b3949260160b
        set day sunday monday tuesday wednesday thursday friday saturday
    next
    edit "none"
        set uuid 66f9cdba-d6db-51ef-cd7b-b23279eea436
    next
    edit "default-darrp-optimize"
        set uuid 66f9daa8-d6db-51ef-f549-4109f267dd18
        set start 01:00
        set end 01:30
        set day sunday monday tuesday wednesday thursday friday saturday
    next
end
config firewall vip
    edit "testvip1"
        set uuid eea26de2-2a20-51f0-6462-e2444e0e84e7
        set extip *******
        set mappedip "***********"
        set extintf "t1"
        set portforward enable
        set extport 80
        set mappedport 8080
    next
end
config firewall ssh local-key
    edit "Fortinet_SSH_RSA2048"
        set password ENC AAAAgMTem4JzQI0KB4sjvI1UEYK0oiakOID7qUKn796ibGUrlQXfjuOxRCfiw2ibridtOvvDG5XMhK17iy5nJQQmHTbsa238tmecTY6jGz1PzIiL/xc1ChBo0idEeN9w8qOXOu8Mvzoaqr7Ddc9ANiyNQbtAVo036aJr+scwpdMzwaYgIPqqQ5FLcnbZvNNnGqB2NllmMjY3dkVA
        set private-key "-----BEGIN OPENSSH PRIVATE KEY-----
b3BlbnNzaC1rZXktdjEAAAAACmFlczI1Ni1jdHIAAAAGYmNyeXB0AAAAGAAAABC4FGR5d+
Mp/vv8oog9puJ/AAAAEAAAAAEAAAEXAAAAB3NzaC1yc2EAAAADAQABAAABAQC3mOY86RXZ
ohBEOH2ioohTDQK1H1Z5Yb6mGfDz62z4cWAxnf8hh3zJLLiuIBm3bKCQPDoATiM1kym8ze
vkYHaeMNRMa9gGP0bdYkfc2O56fOou1n16rMc+5A4hQrf476knA72LY/lmN2hW9ck6yC55
gNjO/InxKd4vq/+0e3MaY5Lof3yWVrm7HJUiXd7qcNd1cMhiUWDEHWT4+7P5GNQP12ToMs
BeG1UMxGVU/iK7tE3pm/LIjq/02InJSfKUUqUKIqerC1zCbd6r0NMVbtFZR4GA8qZg7LRq
+LpXPwPTyRBYfUiEA5sUoCXWXYOvkTysMVOt9Gsl1uNOuU2JKQ31AAADwNVv5BDrgksGha
UWkpURG2sUTbelDxxihBq/zZU7EmBtDzbXyrjw6/sweHeHqScoihWBUbiTWvvUwI0Ombhh
KRRVMNEZZmH1xjXkHn1ukpgEZfIFAVYI0cltbt1jr+xw9nyLJy/EizbDJsGJdPcsOIWGKa
GLlDgn/MLSaqc4dAyXQSP3daexK7AEvqEvet03d7lVXny0tF1i4SpBfDzjt/ybDm9vSk1J
GkXNC5wI7srSviRsGjnR0rBH2i6OLdNZczf9UXmLTaUL0YeYahf6e56KVfAAipOZvmX/BH
nmCWZyrsHfX4CSLu32bl0wPLFhOsrGTCafUyxsD8q8djDxGzCotg0Q2D9NDRQ/jOd9dChq
E5malxqJ2reVgbpfWdKVBd3GesJLEbtQRjrdWXRg9nWWw0VuGRF5RqPW0Q94hnbzMwLop9
iAXkNfkQbSRmORq6l6lLNay7NyS26mHTWB+Xh+mLfIzmxnmplRLoKthJHzOvDxNoSlSrmu
ok1B1loE7AknGfVFA1QzgggL53QlHsG6yXHV3Voz7mvrXBKnq1cDZuJwIC9lqra789xIbX
0f5ZqkNSm0Yfi8j7Cy3ISeIIzMp24Cgyj3TbGRd1FpLtO9nxcm+UXcqX3YALrctSkEGA0k
3MzCSkCPLXHeqzg/KnPSl7zRaiqxS6z5db7fJQzc5nYY/MoHNyvkFt/hqtto88uNm3ElKK
JZnNDSsUN/6h1WPOOc61x6yU8sHBP9JQ6Utpnjlh+wxZLvMG63MVXWtP5LqWKFtP8uSfyg
HkZGgtBkYM8LGXDPc2sccA9/uf7UDJmCIVgNDCUBtHOWerpj0qJX5ezGN2li+k/7rlnm3Y
K20rXYvHDGXN/zlVqalNn8qxDOKrDfSxY2tO0xiq1+Di9pY+zqUQeHE/P7GXMQzJTCuR2w
oQ1wqotI5TEa5tMXYL83UqkEHU5jc4Yg7Fn00b7OMr7SZJqlZdZXnT/UEQqbZYARaAJr0/
f+/Q7HDFhX7YOgFnK9yLvXddoYPEljL6vk4OF6btEVDaRZF4Nk1Sze/C3hgXPmm5ENEdIM
Ab2opiNXe57xS6yR3zOtpXjGmoEI6FEmQyXqcq+z+Sh1LmQiKlZu5j5hxuSVkSxCRFz0QP
SrNejxl0lLq18qm8IBlM5lpH0riXXyWAPsFE7aS8Pe7rn9bM5fi6qe1sCh11+jBsyui1o4
p3Y5bgk4tDjNmBxATyRm0xQ6JJ0uMVHinhseT1oC4Uxhfo7ufSbg92Wb/wUQsIsHHF25S4
Dy3n5xsg==
-----END OPENSSH PRIVATE KEY-----
"
        set public-key "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQC3mOY86RXZohBEOH2ioohTDQK1H1Z5Yb6mGfDz62z4cWAxnf8hh3zJLLiuIBm3bKCQPDoATiM1kym8zevkYHaeMNRMa9gGP0bdYkfc2O56fOou1n16rMc+5A4hQrf476knA72LY/lmN2hW9ck6yC55gNjO/InxKd4vq/+0e3MaY5Lof3yWVrm7HJUiXd7qcNd1cMhiUWDEHWT4+7P5GNQP12ToMsBeG1UMxGVU/iK7tE3pm/LIjq/02InJSfKUUqUKIqerC1zCbd6r0NMVbtFZR4GA8qZg7LRq+LpXPwPTyRBYfUiEA5sUoCXWXYOvkTysMVOt9Gsl1uNOuU2JKQ31"
        set source built-in
    next
    edit "Fortinet_SSH_DSA1024"
        set password ENC AAAAgM6r7/VkWINF306rTE21Bm2BGNhEgKnFsJT0MrCKo1Ku3205wE29KQlNDx3mDuLeYpPe2aUwG6o4j083l3pdASgo1Anx3R6CoucSQpK5sKuVbjKw+ZJ/JKxF6L5KEtn+pqgniJ+CH0XEuVNZxvBmOrN/zs7VkVveJK4dHG8vF7sOAaKnP+z0roHgAPy6J0A8EllmMjY3dkVA
        set private-key "-----BEGIN OPENSSH PRIVATE KEY-----
b3BlbnNzaC1rZXktdjEAAAAACmFlczI1Ni1jdHIAAAAGYmNyeXB0AAAAGAAAABB8OTuBFH
VTDlFvBdUj1Fh/AAAAEAAAAAEAAAGyAAAAB3NzaC1kc3MAAACBAOsmOQn14mmKkJrnmghV
yCSjTYhqM1G2ORI+y8xjrtY0cMJegPZYbvbPqyjVXXr1kLRQbKbUZh4Pfmc9oZ1gpfhtPH
Pcl7eIycPrkLnTlqHw/5KoyQLHXxY4wMNo3iAOajqL2C7kiBnE/BFeuCwwSXIHYX8n0h83
wJcAgXS4BW9rAAAAFQDSxWSu/xdgd99++ZNVDGUtofGuaQAAAIEA4PeEvu3mCaFcwnkw48
G26ApRDSNR7S/RVp3uE9hUrUaSdRTFAnb4SnmwjxDV6aWcY6jAKkJjWlGcc5eBiCbjsu36
1M/zBDqYUqbDIiNbZHzZtYJHjwbAdHKqq3TUfUeUW2QB7UAsxehurZtm7a3A/j984PGB+J
OBGBJAuz8WplwAAACATSC9jVUxave1XJ+GtEReT6H76XUYrqac3kB9UyGYtbhVLxYRGjgp
KS/bm7ltF6FMo5nHVG/0NQMG4//H1q4whN2otsvYoku0Cd+HCwvHrbtDCeX0goPAsSLCr+
ajyqPtWfUJ02t7sw4FCWdC2h0iSWXl5IrH5/XtMJtxlfIhc74AAAHgXh6UEYOgbQpMgowu
41y/cU9QooHq+eJAb1/ti0ZSl7YuAPEPb51IXdAs+AFDm3XMYaF3h9ZleWmP03lbabCrUe
CbzOLzJ9Ub9ctmQxyxpUlWBx2rmSgu807Y3wK+Qal5Na7mG4S9T4u+4zAe0G1hlTKSDFyB
Hdh0gSfdTdVp6MVrwTJlDjNdx4j5zwJxpwhnI5OQan1osxdYO2R0Hzzczymy6PBmMDQfs9
BRMy8E9fO7kQcqDIIYxJLBdxMcyUQPNiD+J8815kjIzaPucr4qay1Lybvm9f8ZnUFc1ZYj
/jZqXN47PmQRcZhUxadMdYbiURFpOZG38kUfJfE7Z3Y/bhU0j0GAo4ymTZc7HCYl2R6YAK
Rvf5TXHvtnxoRLwYpFV5GtiPCGqQKSFFZTJksUwSj1N9NeJY9oWX/EOdb6sgnoaZ0ZJSWp
AD7BbQUX0SbK0bMmrlhW8mq7t6GZTggKDj78rpus8FJrcNiAW+prm9CAKZ/o6wB/p3UXvr
I7W7ZltB0EuBiYBjOtFxW7mDVqBzuOOc5rNTpTXP/Xh2k2QGgfHfNINiU2tEm/50f4l5lA
u6venEL2aGbYSX9kuvMQ9je2bSotXdMcVndOS2nD38A0trW7G6qivhY66Fs+hNkl
-----END OPENSSH PRIVATE KEY-----
"
        set public-key "ssh-dss AAAAB3NzaC1kc3MAAACBAOsmOQn14mmKkJrnmghVyCSjTYhqM1G2ORI+y8xjrtY0cMJegPZYbvbPqyjVXXr1kLRQbKbUZh4Pfmc9oZ1gpfhtPHPcl7eIycPrkLnTlqHw/5KoyQLHXxY4wMNo3iAOajqL2C7kiBnE/BFeuCwwSXIHYX8n0h83wJcAgXS4BW9rAAAAFQDSxWSu/xdgd99++ZNVDGUtofGuaQAAAIEA4PeEvu3mCaFcwnkw48G26ApRDSNR7S/RVp3uE9hUrUaSdRTFAnb4SnmwjxDV6aWcY6jAKkJjWlGcc5eBiCbjsu361M/zBDqYUqbDIiNbZHzZtYJHjwbAdHKqq3TUfUeUW2QB7UAsxehurZtm7a3A/j984PGB+JOBGBJAuz8WplwAAACATSC9jVUxave1XJ+GtEReT6H76XUYrqac3kB9UyGYtbhVLxYRGjgpKS/bm7ltF6FMo5nHVG/0NQMG4//H1q4whN2otsvYoku0Cd+HCwvHrbtDCeX0goPAsSLCr+ajyqPtWfUJ02t7sw4FCWdC2h0iSWXl5IrH5/XtMJtxlfIhc74="
        set source built-in
    next
    edit "Fortinet_SSH_ECDSA256"
        set password ENC AAAAgGR2gaPdA8FKgUu9RqlBB5M1FnfB5rbEf9eN69DRPXtfGCg493VN9Lg/IodRkqpOB83h9D58ZhO2ySi2x86luCQitGzp+7R1eG4ASUVV5EQQpSzGF1N+fXG/R93NZofyu9ILIqqxso30Dfjv/AdRsKXVUvPYo9LrfVe3l4/OPNAac1J9nB+t8fIl1oj0PTfylVlmMjY3dkVA
        set private-key "-----BEGIN OPENSSH PRIVATE KEY-----
b3BlbnNzaC1rZXktdjEAAAAACmFlczI1Ni1jdHIAAAAGYmNyeXB0AAAAGAAAABC1CBeGaW
ZtRPDd1O/4qUL6AAAAEAAAAAEAAABoAAAAE2VjZHNhLXNoYTItbmlzdHAyNTYAAAAIbmlz
dHAyNTYAAABBBEeC5UvRJpakjLT91MR/TYxX7Woy+KN1OlJ0IzjcNBQjOI+VZcrlcI6pK1
8DkYHDzsPZsNGXeQAbkXmvVoBb6dUAAACgXYpOXTa2M0kf8ZpOOGeIJTKHgjter6DEJwbn
cejBJ9vRM5hl1kFtS3gaWsGjM7nPinaIM6RdNJvXaOrqyHPuHuQZjv3kQQvZMetlXrRHKb
ivIJeKU27mwEscmgYKjZNeq8hSbtXEFHebvVg1zerVDGwHBtaluUMpZZJa+3DplWNTnkQO
t5UhF/MVKKSBZKNmxTWca121lnleuq1cO8lxVQ==
-----END OPENSSH PRIVATE KEY-----
"
        set public-key "ecdsa-sha2-nistp256 AAAAE2VjZHNhLXNoYTItbmlzdHAyNTYAAAAIbmlzdHAyNTYAAABBBEeC5UvRJpakjLT91MR/TYxX7Woy+KN1OlJ0IzjcNBQjOI+VZcrlcI6pK18DkYHDzsPZsNGXeQAbkXmvVoBb6dU="
        set source built-in
    next
    edit "Fortinet_SSH_ECDSA384"
        set password ENC AAAAgN2/JGGUcLPrRkKkjRarltzTe9VkOlOAVNWAYA3lWBksTb/CfjYHLDF+CXOMCO/loy6lbhsmBIIm9aF0rULXA/XMEyPmsVqR/xgZwCblRnkN21AYNkk9VhZgcPoPFRxxRHSTeAU1orV4K4rl+9QMO39urHEwr006ApMLhPQ6e6PnY0oZCLNh4OTIP9dpgxuInVlmMjY3dkVA
        set private-key "-----BEGIN OPENSSH PRIVATE KEY-----
b3BlbnNzaC1rZXktdjEAAAAACmFlczI1Ni1jdHIAAAAGYmNyeXB0AAAAGAAAABCR+i1BXW
OfvJDzBCFz3eNWAAAAEAAAAAEAAACIAAAAE2VjZHNhLXNoYTItbmlzdHAzODQAAAAIbmlz
dHAzODQAAABhBHxf5CiDPfjunLaxOBVtvHUbeaghRm7/ijVGhtL2kDt//vIVFg2K7ErRcm
yMdtwH0yMFpbYavl01OKos5NpmfIpLWliVW4E4mhUdZ+6DcBx1yN9iMd6FjhCw8jjlSr/j
WwAAANAQ7hraGTwiLTpeNSfBR1bYV5/GLseaYD5gBBmO6k13Qz7mDpxRtH2BAmy7fHT5po
H/ahu/tbeMli7ZYpYzJzbZJORyvJI9eIhhjO4y8onJsyr9+l6dgV+IuP5jADh/y3zPI4cK
5cYLoyIshOr9crqexdlEe9GKnWj37KAtcSGXh7I489QlurVq557+wCztA5Tw0IH8Q30w2F
UVzdLdhxhhgIttFNWqs8V9ZVacOsQlalZJPqqTKPTK7UcrfvI8FDgbRHU5+4Zql7Cdtxme
NkbF
-----END OPENSSH PRIVATE KEY-----
"
        set public-key "ecdsa-sha2-nistp384 AAAAE2VjZHNhLXNoYTItbmlzdHAzODQAAAAIbmlzdHAzODQAAABhBHxf5CiDPfjunLaxOBVtvHUbeaghRm7/ijVGhtL2kDt//vIVFg2K7ErRcmyMdtwH0yMFpbYavl01OKos5NpmfIpLWliVW4E4mhUdZ+6DcBx1yN9iMd6FjhCw8jjlSr/jWw=="
        set source built-in
    next
    edit "Fortinet_SSH_ECDSA521"
        set password ENC AAAAgJE+LHGaFad77WdSirexNE5fdEX2GbBlwOigFbd6FlUY2TswK5hvsC1VOKMsROKzLSzMbn74tc39MeGPqA7S/bCgTS8xfAUVl7xG/+eUebM7qtLZHCD/9+jqYuGmJkGmCPOUxySmmLrbKMoussAGZZ10Hw353IyADYmZjCz5dc+CoHbqyxvmr+HsFrDIrg5tcFlmMjY3dkVA
        set private-key "-----BEGIN OPENSSH PRIVATE KEY-----
b3BlbnNzaC1rZXktdjEAAAAACmFlczI1Ni1jdHIAAAAGYmNyeXB0AAAAGAAAABA6UfmGRd
Iz1CKmff/a4DYKAAAAEAAAAAEAAACsAAAAE2VjZHNhLXNoYTItbmlzdHA1MjEAAAAIbmlz
dHA1MjEAAACFBACaiKpTFhrrnbeJf0DVkNec5cJ/AJePrpLORs2/3zPQy+Gy72DfcFu6a/
dV3sAEwma3Id1HY649jekQ+aJa1G9dFwFOkuUAc5F+0DzsKZm1EYQybOxY3/tgkgmLgvF9
EGVQSPXWlXEOFQYSOFgaszYxM3mdbO+EjXi1cMTnPPalPsDf0QAAAQD/1B4U1Uls9kdyMg
2E0fFfuq5x7rfV9gN/5t1iuD3caWy1qdSK7p0U6mBXP78a5Degk6fGHcLYpzLvYpizSPeV
KWrEezSjuFnjOjRKM3E6kW7Al/cUdIyliNy5MEf8hIPZk8bIVu3J9Irgv1v3Xp1Hr/Hpn/
HK7VGVogs4qeQhKUfhbf/IRtIbNJ8FI+e38YV2OO3TC70d9+zv6bkxG1v0o6+dvRfYevkO
GS1rW1P88mNfp+9NAXM+O9Hx3wj7oGKBYL7YXCHeJo5d/UGnNIe8uOe+TnumwSBCyL2fzF
J5fHjjQSSGVOUEkGuwtmN1pbNciVPdZOq1Mq/Ozcqfnudz
-----END OPENSSH PRIVATE KEY-----
"
        set public-key "ecdsa-sha2-nistp521 AAAAE2VjZHNhLXNoYTItbmlzdHA1MjEAAAAIbmlzdHA1MjEAAACFBACaiKpTFhrrnbeJf0DVkNec5cJ/AJePrpLORs2/3zPQy+Gy72DfcFu6a/dV3sAEwma3Id1HY649jekQ+aJa1G9dFwFOkuUAc5F+0DzsKZm1EYQybOxY3/tgkgmLgvF9EGVQSPXWlXEOFQYSOFgaszYxM3mdbO+EjXi1cMTnPPalPsDf0Q=="
        set source built-in
    next
    edit "Fortinet_SSH_ED25519"
        set password ENC 7p9ngFVi0+FDR/btlgtp437C7VV+CD/RFwYLews7q8aF+c6sh8mWYf0i7O7D8ykjbLQ+A0ryx3K9gYFnuO1T7+Jzdd4K3KJzmtJkM7KdjN/v7LeAFFySRwwBL4Onf+UG/lkzuSePfZrk21X+cfDRV0bDWRnbv2uIfT+9Jv+vH+rH/UYiXs6wvm/bV1pvRfmm7EKxUFlmMjY3dkVA
        set private-key "-----BEGIN OPENSSH PRIVATE KEY-----
b3BlbnNzaC1rZXktdjEAAAAACmFlczI1Ni1jdHIAAAAGYmNyeXB0AAAAGAAAABD2QeXKhj
sSsZt+v75B0z3fAAAAEAAAAAEAAAAzAAAAC3NzaC1lZDI1NTE5AAAAINVTsQsNIcm2319l
h72lDpdIxch160B9YkAw7d88vlBRAAAAkHn5SdRfgD7ziFoEzvchwW51V6cURfl8hBWsn2
ZHaMRdo75RqU6yO1JcU5BFS47J6CIOO4ZMmcu6mEKxG+9Ovfasmr88HCa38B7CWUp52s4E
KtwzJQnlHER2WHAQ6tDBKixYL2tO/VlVFvYUjKfCbFIHzyzZwUgU/vcabeBP4myKa2Ndb4
hTZJU0rjsEWyZQ0g==
-----END OPENSSH PRIVATE KEY-----
"
        set public-key "ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAINVTsQsNIcm2319lh72lDpdIxch160B9YkAw7d88vlBR"
        set source built-in
    next
end
config firewall ssh local-ca
    edit "Fortinet_SSH_CA"
        set password ENC AAAAgHUufr2SX3ygWPy6i+pjTo9G8esIwXt3fV/Xl7Tbs6kElOAvzMCE/I7LtXK0zurvdPhNhvz2zecDWwKn8DcMqZSgInGelXKJS/NsC5ZJ93nRT5Fedheo9fH3cHtWX4eqlbvG8V7EVS63vrZSh+nTF5YASP9yWCl+VJoka4WQFdQSQ7k0bOh8XOpjCNxgKXPPEVlmMjY3dkVA
        set private-key "-----BEGIN OPENSSH PRIVATE KEY-----
b3BlbnNzaC1rZXktdjEAAAAACmFlczI1Ni1jdHIAAAAGYmNyeXB0AAAAGAAAABCuv/wjTL
8fU8KUY5f5jEmTAAAAEAAAAAEAAAEXAAAAB3NzaC1yc2EAAAADAQABAAABAQCxnaCSiBgT
YRPMzPNi6I1tujvs4UfI4aYA3ZzPeNy3iznX6RWkEWD0HkG/HTsCjGHh6BVFKcpJIrl8Ek
rYD/vMpbPRxzxXcTN1FtZBAyzm2ymzUkHGgc5TtKfhqcFPa3ord/kygnvXXeewj5fGLZTD
7EMl2DtLGF4vw1FjoYLwC6VvW3sV8lqz32n2dukdr/lN963Safch8SZ7Oym31EaTWvG3SH
L8aWy5Mq0kDUFObhWTrJCH/c123+mEKZvOIi248X7nq2b0OynbwNJMdq6n+30J+9xzU6MR
d0fgwg6CVTmAAfYQ1Fw9sN+nGR7x9RsbGqCbSU/mQNj+G112GoxfAAADwAkRKUdKV4u3k5
s6ieQcgPyLyBDlVbFVmWPCTR8AJUPeLYRtmkFKuX4SnnaOW4BiOS9FGPBMxGIl1dp8gYhD
5slM8Q2mVfua75E0SPKK6hfKcl7Xj6oMUD/yaBEqwOoNK/cSbEvkU1Pri7PKUm9DCllVxG
mv9AtAYVNtE5EAEmH/t5VVf/PSLxtfDQpjFMGyUrnTcjuaNPFglLynB6wjRyfw0+hLofhi
N4Sjxi9PFwzkoqP/DFE+rIF9i5t3wbvXT93EZo8Jje1syP3xRaL13vPTrLREoKertUMg1H
Lj4SfRpEPqemuYJNX2gfnMvzMOqvQ1cr24z5UVB3lO2//7/qvZitE6vmN2pZDAy4b0vKRI
81Hy821SpHeH8Iw/Oo63X17gXuxDfz/YPNFTZZdR/abaN+4K5OT3syjHNqBWeODx4dfI98
IA9OTLIJWLHlPC8b+NFx8DTtv9sjVeojXx2LH2MRj/P4yqKpxZRWT8Pp3ti/YvXgveubMK
ckEF+kQbNSx2rimpcVkXai1+UFCd96qQJUczEt8R2MeNdQxqPGyPcdRNbYpaLTG+lhlPbV
uJFzvZhquXMifPUw2jIqBJMmqZVnMj7B/o9sh84NlP4xebd9plF9T0JYx5vNnFSriISUY6
9cUXfEX8ff2x45jgmgiXp3+3zjSWmICfEc02xtqEIL0d1ig+KmTbmldzvXkY2XPii3f+3Y
L0if6dJgEoZfeUHTLc/pB8fftXxgqDiFrxbjh/1vreEiWhsyfWir0Er/qg0dIonB809mEs
L52IWdPBlEMPXHscjoXRzQz1Dhhb0r6j0XsBOZt3xAd54NOQ/uSGDSfWMe0xKmZJr7ZCXm
hL2A11XK1yL7hBC+QwMPYSkOLZMVDuMnaLmRlTIgluWRG95H1gtNUBOaRCqC0I40nZvF6W
2JgSG83QRxkIaHzYrpwqTzXcAMaNNEiSDclKb7h8qQnAi/h7pWe+nxNrd1C9v1Zm6843OP
ux2AGoP3dKwZeqjI6MYUEXhrtQXxqEM7wC2TijZBsJ4GF0YR2INNoI/+lbPNGXS7E4V4VA
Y3RBBF9FfkuE3uAh534O0G/MyOVDXUIZNC7rr0tiK7Gc+HQATwxenSl/qFh3/6KPYM14y1
pWJKeNuNZ7qq+FJPydA6nQkdq4jtA+Lv9iSloYSIojKTbBL75IJtqk7M7dU4BRjBcSFm+S
vcK4ekj66qTlPtolWAR+vXNw+wPYXZ0kh+6AEGVAA6dncSJtmM6SPKIiRQjxosG9VLJJhO
PV+dPEHw==
-----END OPENSSH PRIVATE KEY-----
"
        set public-key "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQCxnaCSiBgTYRPMzPNi6I1tujvs4UfI4aYA3ZzPeNy3iznX6RWkEWD0HkG/HTsCjGHh6BVFKcpJIrl8EkrYD/vMpbPRxzxXcTN1FtZBAyzm2ymzUkHGgc5TtKfhqcFPa3ord/kygnvXXeewj5fGLZTD7EMl2DtLGF4vw1FjoYLwC6VvW3sV8lqz32n2dukdr/lN963Safch8SZ7Oym31EaTWvG3SHL8aWy5Mq0kDUFObhWTrJCH/c123+mEKZvOIi248X7nq2b0OynbwNJMdq6n+30J+9xzU6MRd0fgwg6CVTmAAfYQ1Fw9sN+nGR7x9RsbGqCbSU/mQNj+G112Goxf"
        set source built-in
    next
    edit "Fortinet_SSH_CA_Untrusted"
        set password ENC AAAAgIWZvMEpqDwvbLl+D/+jE1N/+ABO9xePVY+dIhyhH/PxDYhj1f4OXR/PXgcwrggRqbau+920BtOTyoOdRnsNnMw7IUV1++B3FDRh79B6jEJ2BZKtOpEpJA5LkxElQSutbsXN7GEsDI6bQ4lQeWXuYuZHZfNlp2Y1i4hVEb/154t3qWXVik/554N2WBxMv8nr7llmMjY3dkVA
        set private-key "-----BEGIN OPENSSH PRIVATE KEY-----
b3BlbnNzaC1rZXktdjEAAAAACmFlczI1Ni1jdHIAAAAGYmNyeXB0AAAAGAAAABBK7hX5GP
nd9jmLmaR4TH36AAAAEAAAAAEAAAEXAAAAB3NzaC1yc2EAAAADAQABAAABAQC9Wz02AEf/
bDkonv4Zp1UV9Arhc6EvR+Z7zvICuAwUF6qrfiH0Lrm4V/rII+j3IYdM2KsuR+9vyIdE/m
cdo0gAqmn75yGH4bayA0f3UK3wpRYAEX6UAbyl0Bddd6p04cHdXaAOmUCrr3DtSZzHdkXP
tpWaNGTcm8L0JG7kY3LIZ6IiS06aXQNrvNp4WskETfoFLaF/pcHvEPozwhOvZJYkF4Wi2q
lnIAGv1uNfZLY86crcMMU4zoPPIv2b6J0quD2ZbPAonyYAolA3cgFYnLgDqoJE59qFrUTI
TJ83rY+IrAjhY4/RmOzIzLmJyNFHCB48Ly2oAOnjMinwgR7uQNz3AAADwLZ5O9bGgzNuG0
iQoXJpUcyxrSlLSZE49sufP937B5Y79uy6Jy/GbHWd3PNSPL3U2uWtTgnkTpeE13Ll0QU0
rKKzEcancXvppXWN2/Nl794WUuH9KtI/6GXWS+73FbIFjPN6RXHJD1LMaq5AQ3McQB7tm2
ovvLYe2UTHyor4KPVSe3JaJwoQqdWtZZkyIPK0Ge8DkCkqMrvmYsTrC8CuXakFiGO5KBpT
bht9iwC31nL+Am7dTcfBbZ354Rn06WfgNIOQJtMy95WLF2tv7ll5+HrJsf9rm5w7KMlbGj
KeOh5Taf4mPVQmYOyn2rk1TRJwTEpFlnA3g1rhal0jBVCaJ3Wx2AqJbf5zAg5ueOX/2VGY
Ldh4wx1iUzhiQXIO0n2a8Mq0U4E7EmjGFZlXtg55e/uVbe4cpSeA0A2XPMbYg2YdeFOLjp
jsBCWvUB4H8ViY6NyLEszUZaPgRQuR+gnsXbrHB3hxKBV7td0i1T1jl+9RykLhl9B8fTXr
U+1O6qv/SMmGciuSfGCY/qZthIgnMu8qR6zKjl/qr3OjU/7IneZENwmEjqad5/EkT5nhgm
RF78BimrnlnZa9PAPbCyEmm/dcwj2j84gLp4f/Nr6QBVB9b1ERGm9Dbo0trVCSFozF15VB
s/Hb8rZMl4mGJ7tCgiUbLdBeMEIXez/FHfrt+X0ChslryCloAY9GAnWROfWBDIKpeq2gWw
vE4dlSaL28qOdj3rGEQIxeuUZlxdJXXCs8ay4PmE/mxXr3ZzImAMLpyynTX010wrLRoty9
dNlZDG2s29snkJK+fZ403cUwRq2wzHXyNdrcPUQIS7gFypg6K3Xy27mDHP1a7zGWcoCfQC
zmHZDKy4i3YXVJZj/ykdZ1y4yr0ZneVY8WaBc9Qo5r1EcA14Uf6DFrlfC/P5mr1S2xjgKq
nROTtnG+bRsjOQ8ejlxGuuVegr4ffn4Pyx0GnsNvSdqdvgFE1EA6nnPNdUnTb20dwVLwCd
t4YkJbDm93Pc61C+q0d3CuGbOb8a83UOzFzsLvMxz/EJNEJ31eCpaG48qm/2uGoWOJZ6ez
CEIPxAexrc8wdu5bCbJhDqJPT9VKDzrHkPtkh4PRegG82DX+l0Gr9rqa73NdOnVQ37rWWH
LkuJJntb7H2zzOeXEJlOd0vDcegYZAjSUnlZLfD5z7Kk4We+8qQy2RZQL0uYNWJBYgpIlv
AsO4ugLoO8e+a7p/fwxzot6AUTrR0IbeXohZDQ+q4WcLkshu12ptax39QSVJtEmGJhE+dx
w/f07m1w==
-----END OPENSSH PRIVATE KEY-----
"
        set public-key "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQC9Wz02AEf/bDkonv4Zp1UV9Arhc6EvR+Z7zvICuAwUF6qrfiH0Lrm4V/rII+j3IYdM2KsuR+9vyIdE/mcdo0gAqmn75yGH4bayA0f3UK3wpRYAEX6UAbyl0Bddd6p04cHdXaAOmUCrr3DtSZzHdkXPtpWaNGTcm8L0JG7kY3LIZ6IiS06aXQNrvNp4WskETfoFLaF/pcHvEPozwhOvZJYkF4Wi2qlnIAGv1uNfZLY86crcMMU4zoPPIv2b6J0quD2ZbPAonyYAolA3cgFYnLgDqoJE59qFrUTITJ83rY+IrAjhY4/RmOzIzLmJyNFHCB48Ly2oAOnjMinwgR7uQNz3"
        set source built-in
    next
end
config firewall ssh setting
    set caname "Fortinet_SSH_CA"
    set untrusted-caname "Fortinet_SSH_CA_Untrusted"
    set hostkey-rsa2048 "Fortinet_SSH_RSA2048"
    set hostkey-dsa1024 "Fortinet_SSH_DSA1024"
    set hostkey-ecdsa256 "Fortinet_SSH_ECDSA256"
    set hostkey-ecdsa384 "Fortinet_SSH_ECDSA384"
    set hostkey-ecdsa521 "Fortinet_SSH_ECDSA521"
    set hostkey-ed25519 "Fortinet_SSH_ED25519"
end
config firewall profile-protocol-options
    edit "default"
        set comment "All default services."
        config http
            set ports 80
            unset options
            unset post-lang
        end
        config ftp
            set ports 21
            set options splice
        end
        config imap
            set ports 143
            set options fragmail
        end
        config mapi
            set ports 135
            set options fragmail
        end
        config pop3
            set ports 110
            set options fragmail
        end
        config smtp
            set ports 25
            set options fragmail splice
        end
        config nntp
            set ports 119
            set options splice
        end
        config ssh
            unset options
        end
        config dns
            set ports 53
        end
        config cifs
            set ports 445
            unset options
        end
    next
end
config firewall ssl-ssh-profile
    edit "deep-inspection"
        set comment "Read-only deep inspection profile."
        config https
            set ports 443
            set status deep-inspection
            set quic inspect
        end
        config ftps
            set ports 990
            set status deep-inspection
        end
        config imaps
            set ports 993
            set status deep-inspection
        end
        config pop3s
            set ports 995
            set status deep-inspection
        end
        config smtps
            set ports 465
            set status deep-inspection
        end
        config ssh
            set ports 22
            set status disable
        end
        config dot
            set status disable
            set quic inspect
        end
        config ssl-exempt
            edit 1
                set fortiguard-category 31
            next
            edit 2
                set fortiguard-category 33
            next
            edit 3
                set type wildcard-fqdn
                set wildcard-fqdn "adobe"
            next
            edit 4
                set type wildcard-fqdn
                set wildcard-fqdn "Adobe Login"
            next
            edit 5
                set type wildcard-fqdn
                set wildcard-fqdn "android"
            next
            edit 6
                set type wildcard-fqdn
                set wildcard-fqdn "apple"
            next
            edit 7
                set type wildcard-fqdn
                set wildcard-fqdn "appstore"
            next
            edit 8
                set type wildcard-fqdn
                set wildcard-fqdn "auth.gfx.ms"
            next
            edit 9
                set type wildcard-fqdn
                set wildcard-fqdn "citrix"
            next
            edit 10
                set type wildcard-fqdn
                set wildcard-fqdn "dropbox.com"
            next
            edit 11
                set type wildcard-fqdn
                set wildcard-fqdn "eease"
            next
            edit 12
                set type wildcard-fqdn
                set wildcard-fqdn "firefox update server"
            next
            edit 13
                set type wildcard-fqdn
                set wildcard-fqdn "fortinet"
            next
            edit 14
                set type wildcard-fqdn
                set wildcard-fqdn "googleapis.com"
            next
            edit 15
                set type wildcard-fqdn
                set wildcard-fqdn "google-drive"
            next
            edit 16
                set type wildcard-fqdn
                set wildcard-fqdn "google-play2"
            next
            edit 17
                set type wildcard-fqdn
                set wildcard-fqdn "google-play3"
            next
            edit 18
                set type wildcard-fqdn
                set wildcard-fqdn "Gotomeeting"
            next
            edit 19
                set type wildcard-fqdn
                set wildcard-fqdn "icloud"
            next
            edit 20
                set type wildcard-fqdn
                set wildcard-fqdn "itunes"
            next
            edit 21
                set type wildcard-fqdn
                set wildcard-fqdn "microsoft"
            next
            edit 22
                set type wildcard-fqdn
                set wildcard-fqdn "skype"
            next
            edit 23
                set type wildcard-fqdn
                set wildcard-fqdn "softwareupdate.vmware.com"
            next
            edit 24
                set type wildcard-fqdn
                set wildcard-fqdn "verisign"
            next
            edit 25
                set type wildcard-fqdn
                set wildcard-fqdn "Windows update 2"
            next
            edit 26
                set type wildcard-fqdn
                set wildcard-fqdn "live.com"
            next
            edit 27
                set type wildcard-fqdn
                set wildcard-fqdn "google-play"
            next
            edit 28
                set type wildcard-fqdn
                set wildcard-fqdn "update.microsoft.com"
            next
            edit 29
                set type wildcard-fqdn
                set wildcard-fqdn "swscan.apple.com"
            next
            edit 30
                set type wildcard-fqdn
                set wildcard-fqdn "autoupdate.opera.com"
            next
            edit 31
                set type wildcard-fqdn
                set wildcard-fqdn "cdn-apple"
            next
            edit 32
                set type wildcard-fqdn
                set wildcard-fqdn "mzstatic-apple"
            next
        end
    next
    edit "custom-deep-inspection"
        set comment "Customizable deep inspection profile."
        config https
            set ports 443
            set status deep-inspection
            set quic inspect
        end
        config ftps
            set ports 990
            set status deep-inspection
        end
        config imaps
            set ports 993
            set status deep-inspection
        end
        config pop3s
            set ports 995
            set status deep-inspection
        end
        config smtps
            set ports 465
            set status deep-inspection
        end
        config ssh
            set ports 22
            set status disable
        end
        config dot
            set status disable
            set quic inspect
        end
        config ssl-exempt
            edit 1
                set fortiguard-category 31
            next
            edit 2
                set fortiguard-category 33
            next
            edit 3
                set type wildcard-fqdn
                set wildcard-fqdn "adobe"
            next
            edit 4
                set type wildcard-fqdn
                set wildcard-fqdn "Adobe Login"
            next
            edit 5
                set type wildcard-fqdn
                set wildcard-fqdn "android"
            next
            edit 6
                set type wildcard-fqdn
                set wildcard-fqdn "apple"
            next
            edit 7
                set type wildcard-fqdn
                set wildcard-fqdn "appstore"
            next
            edit 8
                set type wildcard-fqdn
                set wildcard-fqdn "auth.gfx.ms"
            next
            edit 9
                set type wildcard-fqdn
                set wildcard-fqdn "citrix"
            next
            edit 10
                set type wildcard-fqdn
                set wildcard-fqdn "dropbox.com"
            next
            edit 11
                set type wildcard-fqdn
                set wildcard-fqdn "eease"
            next
            edit 12
                set type wildcard-fqdn
                set wildcard-fqdn "firefox update server"
            next
            edit 13
                set type wildcard-fqdn
                set wildcard-fqdn "fortinet"
            next
            edit 14
                set type wildcard-fqdn
                set wildcard-fqdn "googleapis.com"
            next
            edit 15
                set type wildcard-fqdn
                set wildcard-fqdn "google-drive"
            next
            edit 16
                set type wildcard-fqdn
                set wildcard-fqdn "google-play2"
            next
            edit 17
                set type wildcard-fqdn
                set wildcard-fqdn "google-play3"
            next
            edit 18
                set type wildcard-fqdn
                set wildcard-fqdn "Gotomeeting"
            next
            edit 19
                set type wildcard-fqdn
                set wildcard-fqdn "icloud"
            next
            edit 20
                set type wildcard-fqdn
                set wildcard-fqdn "itunes"
            next
            edit 21
                set type wildcard-fqdn
                set wildcard-fqdn "microsoft"
            next
            edit 22
                set type wildcard-fqdn
                set wildcard-fqdn "skype"
            next
            edit 23
                set type wildcard-fqdn
                set wildcard-fqdn "softwareupdate.vmware.com"
            next
            edit 24
                set type wildcard-fqdn
                set wildcard-fqdn "verisign"
            next
            edit 25
                set type wildcard-fqdn
                set wildcard-fqdn "Windows update 2"
            next
            edit 26
                set type wildcard-fqdn
                set wildcard-fqdn "live.com"
            next
            edit 27
                set type wildcard-fqdn
                set wildcard-fqdn "google-play"
            next
            edit 28
                set type wildcard-fqdn
                set wildcard-fqdn "update.microsoft.com"
            next
            edit 29
                set type wildcard-fqdn
                set wildcard-fqdn "swscan.apple.com"
            next
            edit 30
                set type wildcard-fqdn
                set wildcard-fqdn "autoupdate.opera.com"
            next
            edit 31
                set type wildcard-fqdn
                set wildcard-fqdn "cdn-apple"
            next
            edit 32
                set type wildcard-fqdn
                set wildcard-fqdn "mzstatic-apple"
            next
        end
    next
    edit "no-inspection"
        set comment "Read-only profile that does no inspection."
        config https
            set status disable
            set quic bypass
        end
        config ftps
            set status disable
        end
        config imaps
            set status disable
        end
        config pop3s
            set status disable
        end
        config smtps
            set status disable
        end
        config ssh
            set ports 22
            set status disable
        end
        config dot
            set status disable
            set quic inspect
        end
    next
    edit "certificate-inspection"
        set comment "Read-only SSL handshake inspection profile."
        config https
            set ports 443
            set status certificate-inspection
            set quic inspect
        end
        config ftps
            set status disable
        end
        config imaps
            set status disable
        end
        config pop3s
            set status disable
        end
        config smtps
            set status disable
        end
        config ssh
            set ports 22
            set status disable
        end
        config dot
            set status disable
            set quic inspect
        end
    next
end
config waf profile
    edit "default"
        config signature
            config main-class 100000000
                set action block
                set severity high
            end
            config main-class 20000000
            end
            config main-class 30000000
                set status enable
                set action block
                set severity high
            end
            config main-class 40000000
            end
            config main-class 50000000
                set status enable
                set action block
                set severity high
            end
            config main-class 60000000
            end
            config main-class 70000000
                set status enable
                set action block
                set severity high
            end
            config main-class 80000000
                set status enable
                set severity low
            end
            config main-class 110000000
                set status enable
                set severity high
            end
            config main-class 90000000
                set status enable
                set action block
                set severity high
            end
            set disabled-signature 80080005 80200001 60030001 60120001 80080003 90410001 90410002
        end
        config constraint
            config header-length
                set status enable
                set log enable
                set severity low
            end
            config content-length
                set status enable
                set log enable
                set severity low
            end
            config param-length
                set status enable
                set log enable
                set severity low
            end
            config line-length
                set status enable
                set log enable
                set severity low
            end
            config url-param-length
                set status enable
                set log enable
                set severity low
            end
            config version
                set log enable
            end
            config method
                set action block
                set log enable
            end
            config hostname
                set action block
                set log enable
            end
            config malformed
                set log enable
            end
            config max-cookie
                set status enable
                set log enable
                set severity low
            end
            config max-header-line
                set status enable
                set log enable
                set severity low
            end
            config max-url-param
                set status enable
                set log enable
                set severity low
            end
            config max-range-segment
                set status enable
                set log enable
                set severity high
            end
        end
    next
end
config casb saas-application
end
config casb user-activity
end
config casb profile
    edit "default"
    next
end
config firewall policy
    edit 1
        set uuid 6d8810e2-d6db-51ef-3f8d-68696fa6c7b2
        set srcintf "lan"
        set dstintf "wan1"
        set action accept
        set srcaddr "all"
        set dstaddr "all"
        set schedule "always"
        set service "ALL"
        set nat enable
    next
    edit 2
        set name "test1"
        set uuid bc8a2b56-2589-51f0-eed8-9899242b32f4
        set srcintf "t2"
        set dstintf "t2"
        set action accept
        set srcaddr "bad11"
        set dstaddr "wildcard.google.com"
        set schedule "always"
        set service "FTP_GET"
        set nat enable
    next
    edit 3
        set name "testnat1"
        set uuid 2485879c-2a20-51f0-4a79-e65fb6a4fe65
        set srcintf "t1"
        set dstintf "t2"
        set action accept
        set srcaddr "a4"
        set dstaddr "none"
        set schedule "always"
        set service "SMB"
        set utm-status enable
        set av-profile "default"
        set webfilter-profile "default"
        set nat enable
        set port-preserve disable
        set comments "aaaaaaaaaaaaaaaa"
    next
end
config firewall local-in-policy
    edit 1
        set uuid 6cb652b4-d6db-51ef-4d2f-a5db4100cf93
        set intf "any"
        set dstaddr "all"
        set internet-service-src enable
        set internet-service-src-name "Malicious-Malicious.Server" "Tor-Exit.Node" "Tor-Relay.Node"
        set service "ALL"
        set schedule "always"
    next
end
config firewall sniffer
    edit 4
        set uuid 610a8d22-d7e4-51ef-fb9b-4a979e29a55f
        set interface "x1"
        set ips-sensor-status enable
        set ips-sensor "sniffer-profile"
        set av-profile-status enable
        set av-profile "sniffer-profile"
    next
    edit 2
        set uuid 76730f0a-d7e8-51ef-82d8-08f7a34c20af
        set interface "x2"
        set ips-sensor-status enable
        set ips-sensor "sniffer-profile"
        set av-profile-status enable
        set av-profile "sniffer-profile"
    next
end
config firewall on-demand-sniffer
    edit "x2_root"
        set interface "x2"
        set max-packet-count 5000
    next
end
config switch-controller security-policy 802-1X
    edit "802-1X-policy-default"
        set user-group "SSO_Guest_Users"
    next
end
config switch-controller security-policy local-access
    edit "default"
        set mgmt-allowaccess https ping ssh
        set internal-allowaccess https ping ssh
    next
end
config switch-controller lldp-profile
    edit "default"
        set med-tlvs inventory-management network-policy location-identification
        set auto-isl disable
        config med-network-policy
            edit "voice"
            next
            edit "voice-signaling"
            next
            edit "guest-voice"
            next
            edit "guest-voice-signaling"
            next
            edit "softphone-voice"
            next
            edit "video-conferencing"
            next
            edit "streaming-video"
            next
            edit "video-signaling"
            next
        end
        config med-location-service
            edit "coordinates"
            next
            edit "address-civic"
            next
            edit "elin-number"
            next
        end
    next
    edit "default-auto-isl"
    next
    edit "default-auto-mclag-icl"
        set auto-mclag-icl enable
    next
end
config switch-controller qos dot1p-map
    edit "voice-dot1p"
        set priority-0 queue-4
        set priority-1 queue-4
        set priority-2 queue-3
        set priority-3 queue-2
        set priority-4 queue-3
        set priority-5 queue-1
        set priority-6 queue-2
        set priority-7 queue-2
    next
end
config switch-controller qos ip-dscp-map
    edit "voice-dscp"
        config map
            edit "1"
                set cos-queue 1
                set value 46
            next
            edit "2"
                set cos-queue 2
                set value 24,26,48,56
            next
            edit "5"
                set cos-queue 3
                set value 34
            next
        end
    next
end
config switch-controller qos queue-policy
    edit "default"
        set schedule round-robin
        set rate-by kbps
        config cos-queue
            edit "queue-0"
            next
            edit "queue-1"
            next
            edit "queue-2"
            next
            edit "queue-3"
            next
            edit "queue-4"
            next
            edit "queue-5"
            next
            edit "queue-6"
            next
            edit "queue-7"
            next
        end
    next
    edit "voice-egress"
        set schedule weighted
        set rate-by kbps
        config cos-queue
            edit "queue-0"
            next
            edit "queue-1"
                set weight 0
            next
            edit "queue-2"
                set weight 6
            next
            edit "queue-3"
                set weight 37
            next
            edit "queue-4"
                set weight 12
            next
            edit "queue-5"
            next
            edit "queue-6"
            next
            edit "queue-7"
            next
        end
    next
end
config switch-controller qos qos-policy
    edit "default"
    next
    edit "voice-qos"
        set trust-dot1p-map "voice-dot1p"
        set trust-ip-dscp-map "voice-dscp"
        set queue-policy "voice-egress"
    next
end
config switch-controller storm-control-policy
    edit "default"
        set description "default storm control on all port"
    next
    edit "auto-config"
        set description "storm control policy for fortilink-isl-icl port"
        set storm-control-mode disabled
    next
end
config switch-controller auto-config policy
    edit "pse"
    next
    edit "default"
    next
    edit "default-icl"
        set poe-status disable
        set igmp-flood-report enable
        set igmp-flood-traffic enable
    next
end
config switch-controller initial-config template
    edit "_default"
        set vlanid 1
    next
    edit "quarantine"
        set vlanid 4093
        set dhcp-server enable
    next
    edit "rspan"
        set vlanid 4092
        set dhcp-server enable
    next
    edit "voice"
        set vlanid 4091
    next
    edit "video"
        set vlanid 4090
    next
    edit "onboarding"
        set vlanid 4089
    next
    edit "nac_segment"
        set vlanid 4088
        set dhcp-server enable
    next
end
config switch-controller switch-profile
    edit "default"
    next
end
config switch-controller ptp profile
    edit "default"
    next
end
config switch-controller ptp interface-policy
    edit "default"
    next
end
config switch-controller remote-log
    edit "syslogd"
    next
    edit "syslogd2"
    next
end
config wireless-controller setting
    set darrp-optimize-schedules "default-darrp-optimize"
end
config wireless-controller arrp-profile
    edit "arrp-default"
    next
end
config wireless-controller wids-profile
    edit "default"
        set comment "Default WIDS profile."
        set ap-scan enable
        set wireless-bridge enable
        set deauth-broadcast enable
        set null-ssid-probe-resp enable
        set long-duration-attack enable
        set invalid-mac-oui enable
        set weak-wep-iv enable
        set auth-frame-flood enable
        set assoc-frame-flood enable
        set spoofed-deauth enable
        set asleap-attack enable
        set eapol-start-flood enable
        set eapol-logoff-flood enable
        set eapol-succ-flood enable
        set eapol-fail-flood enable
        set eapol-pre-succ-flood enable
        set eapol-pre-fail-flood enable
    next
    edit "default-wids-apscan-enabled"
        set ap-scan enable
    next
end
config wireless-controller ble-profile
    edit "fortiap-discovery"
        set advertising ibeacon eddystone-uid eddystone-url
        set ibeacon-uuid "wtp-uuid"
    next
end
config router rip
    config redistribute "connected"
    end
    config redistribute "static"
    end
    config redistribute "ospf"
    end
    config redistribute "bgp"
    end
    config redistribute "isis"
    end
end
config router ripng
    config redistribute "connected"
    end
    config redistribute "static"
    end
    config redistribute "ospf"
    end
    config redistribute "bgp"
    end
    config redistribute "isis"
    end
end
config router static
    edit 1
        set gateway **********
        set distance 1
        set device "mgmt"
    next
end
config router ospf
    config redistribute "connected"
    end
    config redistribute "static"
    end
    config redistribute "rip"
    end
    config redistribute "bgp"
    end
    config redistribute "isis"
    end
end
config router ospf6
    config redistribute "connected"
    end
    config redistribute "static"
    end
    config redistribute "rip"
    end
    config redistribute "bgp"
    end
    config redistribute "isis"
    end
end
config router bgp
    config redistribute "connected"
    end
    config redistribute "rip"
    end
    config redistribute "ospf"
    end
    config redistribute "static"
    end
    config redistribute "isis"
    end
    config redistribute6 "connected"
    end
    config redistribute6 "rip"
    end
    config redistribute6 "ospf"
    end
    config redistribute6 "static"
    end
    config redistribute6 "isis"
    end
end
config router isis
    config redistribute "connected"
    end
    config redistribute "rip"
    end
    config redistribute "ospf"
    end
    config redistribute "bgp"
    end
    config redistribute "static"
    end
    config redistribute6 "connected"
    end
    config redistribute6 "rip"
    end
    config redistribute6 "ospf"
    end
    config redistribute6 "bgp"
    end
    config redistribute6 "static"
    end
end
config router multicast
end
