#config-version=FG4H1F-7.4.8-FW-build2795-250523:opmode=0:vdom=0:user=admin
#conf_file_ver=4738981016163733
#buildno=2795
#global_vdom=1
config system global
    set admin-https-redirect disable
    set admintimeout 480
    set alias "FortiGate-401F"
    set gui-auto-upgrade-setup-warning disable
    set hostname "FortiGate-401F"
    set language simch
    set switch-controller enable
    set timezone "Asia/Shanghai"
end
config system accprofile
    edit "prof_admin"
        set secfabgrp read-write
        set ftviewgrp read-write
        set authgrp read-write
        set sysgrp read-write
        set netgrp read-write
        set loggrp read-write
        set fwgrp read-write
        set vpngrp read-write
        set utmgrp read-write
        set wanoptgrp read-write
        set wifi read-write
        set cli-get enable
        set cli-show enable
        set cli-exec enable
        set cli-config enable
    next
    edit "all"
        set secfabgrp read-write
        set ftviewgrp read-write
        set authgrp read-write
        set sysgrp read-write
        set netgrp read-write
        set loggrp read-write
        set fwgrp read-write
        set vpngrp read-write
        set utmgrp read-write
        set wanoptgrp read-write
        set wifi read-write
    next
end
config system npu
    config np-queues
        config ethernet-type
            edit "ARP"
                set type 806
                set queue 9
            next
            edit "HA-SESSYNC"
                set type 8892
                set queue 11
            next
            edit "HA-DEF"
                set type 8890
                set queue 11
            next
            edit "HC-DEF"
                set type 8891
                set queue 11
            next
            edit "L2EP-DEF"
                set type 8893
                set queue 11
            next
            edit "LACP"
                set type 8809
                set queue 9
            next
        end
        config ip-protocol
            edit "OSPF"
                set protocol 89
                set queue 11
            next
            edit "IGMP"
                set protocol 2
                set queue 11
            next
            edit "ICMP"
                set protocol 1
                set queue 3
            next
        end
        config ip-service
            edit "IKE"
                set protocol 17
                set sport 500
                set dport 500
                set queue 11
            next
            edit "BGP"
                set protocol 6
                set sport 179
                set dport 179
                set queue 9
            next
            edit "BFD-single-hop"
                set protocol 17
                set sport 3784
                set dport 3784
                set queue 11
            next
            edit "BFD-multiple-hop"
                set protocol 17
                set sport 4784
                set dport 4784
                set queue 11
            next
            edit "SLBC-management"
                set protocol 17
                set dport 720
                set queue 11
            next
            edit "SLBC-1"
                set protocol 17
                set sport 11133
                set dport 11133
                set queue 11
            next
            edit "SLBC-2"
                set protocol 17
                set sport 65435
                set dport 65435
                set queue 11
            next
        end
    end
end
config system interface
    edit "ha"
        set vdom "root"
        set type physical
        set snmp-index 1
    next
    edit "mgmt"
        set vdom "root"
        set ip ************* *************
        set allowaccess ping https ssh snmp fgfm radius-acct fabric ftm speed-test
        set type physical
        set lldp-reception disable
        set lldp-transmission disable
        set role lan
        set snmp-index 2
    next
    edit "port1"
        set vdom "root"
        set ip ************* ***************
        set allowaccess ping https ssh snmp http radius-acct fabric ftm speed-test
        set type physical
        set description "test-静态IP测试"
        set alias "test1"
        set lldp-reception enable
        set estimated-upstream-bandwidth 500
        set estimated-downstream-bandwidth 600
        set role wan
        set snmp-index 3
    next
    edit "port2"
        set vdom "root"
        set mode dhcp
        set allowaccess ping https snmp
        set type physical
        set description "test-dhcp"
        set alias "test2"
        set lldp-reception enable
        set estimated-upstream-bandwidth 700
        set estimated-downstream-bandwidth 800
        set role wan
        set snmp-index 4
    next
    edit "port3"
        set vdom "root"
        set mode pppoe
        set allowaccess ping https ssh snmp fgfm radius-acct ftm
        set type physical
        set description "test-pppoe拨号"
        set alias "test3"
        set snmp-index 5
        set username "test"
        set password ENC GIFvleeRXy+Upu1ghnX2/aE68dHzEkBLZjBkC156wlTkKFvAAHKuyNjKoHglSunogIFXswjxuGlEtDos0/oynkC0nBhkAFNo06abLXrHkqLqhSv+EUmv6QHnlCOxauHDFxlSsaCQ66JbPwsmxSxRup6hrzY3mw/N4nHczR8C36OcpahdXo15wOX0PvIEl8OKMIgaRVlmMjY3dkVA
    next
    edit "port4"
        set vdom "root"
        set type physical
        set snmp-index 6
    next
    edit "port5"
        set vdom "root"
        set ips-sniffer-mode enable
        set type physical
        set snmp-index 7
    next
    edit "port6"
        set vdom "root"
        set mode dhcp
        set type physical
        set snmp-index 8
    next
    edit "port7"
        set vdom "root"
        set ip ********** *************
        set allowaccess ping https ssh snmp http fgfm ftm
        set type physical
        set snmp-index 9
    next
    edit "port8"
        set vdom "root"
        set type physical
        set snmp-index 10
    next
    edit "port9"
        set vdom "root"
        set type physical
        set snmp-index 11
    next
    edit "port10"
        set vdom "root"
        set type physical
        set snmp-index 12
    next
    edit "port11"
        set vdom "root"
        set type physical
        set snmp-index 13
    next
    edit "port12"
        set vdom "root"
        set type physical
        set snmp-index 14
    next
    edit "port13"
        set vdom "root"
        set type physical
        set snmp-index 15
    next
    edit "port14"
        set vdom "root"
        set type physical
        set snmp-index 16
    next
    edit "port15"
        set vdom "root"
        set mode dhcp
        set allowaccess ping https snmp http fgfm ftm
        set type physical
        set snmp-index 17
    next
    edit "port16"
        set vdom "root"
        set ips-sniffer-mode enable
        set type physical
        set monitor-bandwidth enable
        set snmp-index 18
    next
    edit "port17"
        set vdom "root"
        set type physical
        set snmp-index 19
        set speed 1000auto
    next
    edit "port18"
        set vdom "root"
        set type physical
        set snmp-index 20
        set speed 1000auto
    next
    edit "port19"
        set vdom "root"
        set type physical
        set snmp-index 21
        set speed 1000auto
    next
    edit "port20"
        set vdom "root"
        set type physical
        set snmp-index 22
        set speed 1000auto
    next
    edit "port21"
        set vdom "root"
        set type physical
        set snmp-index 23
        set speed 1000auto
    next
    edit "port22"
        set vdom "root"
        set type physical
        set snmp-index 24
        set speed 1000auto
    next
    edit "port23"
        set vdom "root"
        set type physical
        set snmp-index 25
        set speed 1000auto
    next
    edit "port24"
        set vdom "root"
        set type physical
        set snmp-index 26
        set speed 1000auto
    next
    edit "x1"
        set vdom "root"
        set type physical
        set snmp-index 27
        set speed 10000full
    next
    edit "x2"
        set vdom "root"
        set type physical
        set snmp-index 28
        set speed 10000full
    next
    edit "x3"
        set vdom "root"
        set type physical
        set snmp-index 29
        set speed 10000full
    next
    edit "x4"
        set vdom "root"
        set type physical
        set snmp-index 30
        set speed 10000full
    next
    edit "x5"
        set vdom "root"
        set type physical
        set snmp-index 31
        set speed 10000full
    next
    edit "x6"
        set vdom "root"
        set type physical
        set snmp-index 32
        set speed 10000full
    next
    edit "x7"
        set vdom "root"
        set ips-sniffer-mode enable
        set type physical
        set monitor-bandwidth enable
        set snmp-index 33
        set speed 10000full
    next
    edit "x8"
        set vdom "root"
        set ips-sniffer-mode enable
        set type physical
        set monitor-bandwidth enable
        set snmp-index 34
        set speed 10000full
    next
    edit "modem"
        set vdom "root"
        set mode pppoe
        set status down
        set type physical
        set snmp-index 35
    next
    edit "naf.root"
        set vdom "root"
        set type tunnel
        set src-check disable
        set snmp-index 36
    next
    edit "l2t.root"
        set vdom "root"
        set type tunnel
        set snmp-index 37
    next
    edit "ssl.root"
        set vdom "root"
        set type tunnel
        set alias "SSL VPN interface"
        set snmp-index 38
    next
    edit "fortilink"
        set vdom "root"
        set fortilink enable
        set ip ********** *************
        set allowaccess ping fabric
        set type aggregate
        set member "x1" "x2"
        set lldp-reception enable
        set lldp-transmission enable
        set snmp-index 39
    next
    edit "tct-vlan103"
        set vdom "root"
        set mode dhcp
        set allowaccess ping https
        set description "vlan103，开启https/ping，dhcp"
        set device-identification enable
        set role lan
        set snmp-index 40
        set interface "port3"
        set vlanid 103
    next
    edit "test-vlan1"
        set vdom "root"
        set ip ************* *************
        set allowaccess ping https ssh snmp fgfm ftm
        set description "子接口-静态"
        set device-identification enable
        set role lan
        set snmp-index 41
        set ip-managed-by-fortiipam disable
        set interface "port2"
        set vlanid 20
    next
    edit "test- vlan3"
        set vdom "root"
        set mode pppoe
        set allowaccess ping https ssh snmp
        set description "pppoe-test"
        set device-identification enable
        set role lan
        set snmp-index 42
        set username "test2"
        set password ENC krgjSCbwQxe7oXn0Y2DF2F5OAg0QCv7Q46Ms6SGw8JVG1LuVW+AD/5oeZRBGfAZNvUvFv1sIuGuUQ34rqwi0N24rKxKYwrAmodw+083oz2ey2RcsdmU6iZm31mpkC4htDCbTBJTDjEyNDKMPPqaZgnRElQbsUubGNdx2NEz9+Ph2HhfCxObXTQ3oGf4M6CM/vm67NllmMjY3dkVA
        set interface "port1"
        set vlanid 50
    next
end
config system physical-switch
    edit "sw0"
        set age-val 0
    next
end
config system custom-language
    edit "en"
        set filename "en"
    next
    edit "fr"
        set filename "fr"
    next
    edit "sp"
        set filename "sp"
    next
    edit "pg"
        set filename "pg"
    next
    edit "x-sjis"
        set filename "x-sjis"
    next
    edit "big5"
        set filename "big5"
    next
    edit "GB2312"
        set filename "GB2312"
    next
    edit "euc-kr"
        set filename "euc-kr"
    next
end
config system admin
    edit "admin"
        set old-password ENC SH2uF1225LFjUXxje0GHt9vYuG47IOPtm97tnZJ7d8cW3wOQtzcoZBexe0Kbyo=
        set accprofile "super_admin"
        set vdom "root"
        config gui-dashboard
            edit 1
                set name "Status"
                set vdom "root"
                set permanent enable
                config widget
                    edit 1
                        set width 1
                        set height 1
                    next
                    edit 2
                        set type licinfo
                        set x-pos 1
                        set width 1
                        set height 1
                    next
                    edit 3
                        set type forticloud
                        set x-pos 2
                        set width 1
                        set height 1
                    next
                    edit 4
                        set type security-fabric
                        set x-pos 3
                        set width 1
                        set height 1
                    next
                    edit 6
                        set type cpu-usage
                        set x-pos 4
                        set width 2
                        set height 1
                    next
                    edit 7
                        set type memory-usage
                        set x-pos 5
                        set width 2
                        set height 1
                    next
                    edit 5
                        set type tr-history
                        set x-pos 6
                        set width 2
                        set height 1
                        set interface "x7"
                        set timeframe 1-hour
                        set csf-device "FG4H1FT924902976"
                    next
                    edit 8
                        set type tr-history
                        set x-pos 7
                        set width 2
                        set height 1
                        set interface "x8"
                        set timeframe 1-hour
                        set csf-device "FG4H1FT924902976"
                    next
                end
            next
            edit 2
                set name "Security"
                set vdom "root"
                config widget
                    edit 1
                        set type fortiview
                        set width 2
                        set height 1
                        set fortiview-type "compromisedHosts"
                        set fortiview-sort-by "verdict"
                        set fortiview-timeframe "hour"
                        set fortiview-visualization "table"
                    next
                    edit 2
                        set type fortiview
                        set x-pos 1
                        set width 2
                        set height 1
                        set fortiview-type "threats"
                        set fortiview-sort-by "threatLevel"
                        set fortiview-timeframe "hour"
                        set fortiview-visualization "table"
                    next
                    edit 3
                        set type device-vulnerability
                        set x-pos 2
                        set width 2
                        set height 1
                    next
                end
            next
            edit 3
                set name "Network"
                set vdom "root"
                config widget
                    edit 1
                        set type routing
                        set width 2
                        set height 1
                        set router-view-type "staticdynamic"
                    next
                    edit 2
                        set type dhcp
                        set x-pos 1
                        set width 2
                        set height 1
                    next
                    edit 3
                        set type virtual-wan
                        set x-pos 2
                        set width 2
                        set height 1
                    next
                    edit 4
                        set type ipsec-vpn
                        set x-pos 3
                        set width 2
                        set height 1
                    next
                end
            next
            edit 4
                set name "Assets & Identities"
                set vdom "root"
                config widget
                    edit 1
                        set type device-inventory
                        set width 2
                        set height 1
                    next
                    edit 2
                        set type identities
                        set x-pos 1
                        set width 2
                        set height 1
                    next
                    edit 3
                        set type firewall-user
                        set x-pos 2
                        set width 2
                        set height 1
                    next
                    edit 4
                        set type quarantine
                        set x-pos 3
                        set width 2
                        set height 1
                    next
                    edit 5
                        set type nac-vlans
                        set x-pos 4
                        set width 2
                        set height 1
                    next
                end
            next
            edit 5
                set name "WiFi"
                set vdom "root"
                config widget
                    edit 1
                        set type ap-status
                        set width 2
                        set height 1
                    next
                    edit 2
                        set type channel-utilization
                        set x-pos 1
                        set width 2
                        set height 1
                        set wifi-band "both"
                    next
                    edit 3
                        set type clients-by-ap
                        set x-pos 2
                        set width 2
                        set height 1
                        set wifi-band "both"
                    next
                    edit 4
                        set type client-signal-strength
                        set x-pos 3
                        set width 2
                        set height 1
                        set wifi-band "both"
                    next
                    edit 5
                        set type rogue-ap
                        set x-pos 4
                        set width 2
                        set height 1
                    next
                    edit 6
                        set type historical-clients
                        set x-pos 5
                        set width 2
                        set height 1
                        set wifi-band "both"
                    next
                    edit 7
                        set type interfering-ssids
                        set x-pos 6
                        set width 2
                        set height 1
                        set wifi-band "both"
                    next
                    edit 8
                        set type wifi-login-failures
                        set x-pos 7
                        set width 2
                        set height 1
                    next
                end
            next
            edit 6
                set name "FortiView Sources"
                set vdom "root"
                set layout-type standalone
                set csf disable
                config widget
                    edit 1
                        set type fortiview
                        set width 6
                        set height 3
                        set fortiview-type "source"
                        set fortiview-sort-by "bytes"
                        set fortiview-timeframe "hour"
                        set fortiview-visualization "table"
                    next
                end
            next
            edit 7
                set name "FortiView Destinations"
                set vdom "root"
                set layout-type standalone
                set csf disable
                config widget
                    edit 1
                        set type fortiview
                        set width 6
                        set height 3
                        set fortiview-type "destination"
                        set fortiview-sort-by "bytes"
                        set fortiview-timeframe "hour"
                        set fortiview-visualization "table"
                    next
                end
            next
            edit 8
                set name "FortiView Applications"
                set vdom "root"
                set layout-type standalone
                set csf disable
                config widget
                    edit 1
                        set type fortiview
                        set width 6
                        set height 3
                        set fortiview-type "application"
                        set fortiview-sort-by "bytes"
                        set fortiview-timeframe "hour"
                        set fortiview-visualization "table"
                    next
                end
            next
            edit 9
                set name "FortiView Web Sites"
                set vdom "root"
                set layout-type standalone
                set csf disable
                config widget
                    edit 1
                        set type fortiview
                        set width 6
                        set height 3
                        set fortiview-type "website"
                        set fortiview-sort-by "sessions"
                        set fortiview-timeframe "hour"
                        set fortiview-visualization "table"
                    next
                end
            next
            edit 10
                set name "FortiView Policies"
                set vdom "root"
                set layout-type standalone
                set csf disable
                config widget
                    edit 1
                        set type fortiview
                        set width 6
                        set height 3
                        set fortiview-type "policy"
                        set fortiview-sort-by "bytes"
                        set fortiview-timeframe "hour"
                        set fortiview-visualization "table"
                    next
                end
            next
            edit 11
                set name "FortiView Sessions"
                set vdom "root"
                set layout-type standalone
                set csf disable
                config widget
                    edit 1
                        set type fortiview
                        set width 6
                        set height 3
                        set fortiview-type "realtimeSessions"
                        set fortiview-sort-by "bytes"
                        set fortiview-timeframe "realtime"
                        set fortiview-visualization "table"
                    next
                end
            next
        end
        set gui-ignore-release-overview-version "7.4"
        set password ENC PB2BA01wMKZ/IqRvpf/UrHhV2xvH2cSlP7Enlm3L24uLFbNgkASYsvviGZVRX6J/Af/ucheFwnaJK2PGEohjOF96thWwwS8WBNhR6iQafgdosw=
    next
end
config system api-user
    edit "test"
        set api-key ENC SH20OPmfemYxxgPOcQ4dwls148ox2T+henovBlJIwxTKu+iagQJfWiCpnIGZXg=
        set accprofile "super_admin_readonly"
    next
end
config system sso-admin
end
config system ha
    set override disable
end
config system storage
    edit "SSD1"
        set status enable
        set media-status enable
        set order 1
        set partition "LOGUSEDXA8308FC6"
        set device "/dev/sdb1"
        set size 449649
        set usage log
    next
    edit "SSD2"
        set status enable
        set media-status enable
        set order 2
        set partition "WANOPTXXE2C11F96"
        set device "/dev/sdc1"
        set size 127928
        set usage wanopt
        set wanopt-mode mix
    next
end
config system dns
    set primary ***********
    set secondary ***********
    set protocol dot
    set server-hostname "globalsdns.fortinet.net"
end
config system replacemsg-image
    edit "logo_fnet"
    next
    edit "logo_fguard_wf"
    next
    edit "logo_v3_fguard_app"
    next
end
config system replacemsg mail "partial"
end
config system replacemsg http "url-block"
end
config system replacemsg http "urlfilter-err"
end
config system replacemsg http "infcache-block"
end
config system replacemsg http "http-contenttypeblock"
end
config system replacemsg http "https-invalid-cert-block"
end
config system replacemsg http "https-untrusted-cert-block"
end
config system replacemsg http "https-blocklisted-cert-block"
end
config system replacemsg http "https-ech-block"
end
config system replacemsg http "switching-protocols-block"
end
config system replacemsg http "http-antiphish-block"
end
config system replacemsg http "videofilter-block"
end
config system replacemsg webproxy "deny"
end
config system replacemsg webproxy "user-limit"
end
config system replacemsg webproxy "auth-challenge"
end
config system replacemsg webproxy "auth-login-fail"
end
config system replacemsg webproxy "auth-group-info-fail"
end
config system replacemsg webproxy "http-err"
end
config system replacemsg webproxy "auth-ip-blackout"
end
config system replacemsg webproxy "ztna-invalid-cert"
end
config system replacemsg webproxy "ztna-empty-cert"
end
config system replacemsg webproxy "ztna-manageable-empty-cert"
end
config system replacemsg webproxy "ztna-no-api-gwy-matched"
end
config system replacemsg webproxy "ztna-cant-find-real-srv"
end
config system replacemsg webproxy "ztna-fqdn-dns-failed"
end
config system replacemsg webproxy "ztna-ssl-bookmark-failed"
end
config system replacemsg webproxy "ztna-no-policy-matched"
end
config system replacemsg webproxy "ztna-matched-deny-policy"
end
config system replacemsg webproxy "ztna-client-cert-revoked"
end
config system replacemsg webproxy "ztna-denied-by-matched-tags"
end
config system replacemsg webproxy "ztna-denied-no-matched-tags"
end
config system replacemsg webproxy "ztna-no-dev-info"
end
config system replacemsg webproxy "ztna-dev-is-offline"
end
config system replacemsg webproxy "ztna-dev-is-unmanageable"
end
config system replacemsg webproxy "ztna-auth-fail"
end
config system replacemsg webproxy "casb-block"
end
config system replacemsg webproxy "swp-empty-cert"
end
config system replacemsg webproxy "swp-manageable-empty-cert"
end
config system replacemsg ftp "ftp-explicit-banner"
end
config system replacemsg fortiguard-wf "ftgd-block"
end
config system replacemsg fortiguard-wf "ftgd-ovrd"
end
config system replacemsg fortiguard-wf "ftgd-quota"
end
config system replacemsg fortiguard-wf "ftgd-warning"
end
config system replacemsg spam "ipblocklist"
end
config system replacemsg spam "smtp-spam-dnsbl"
end
config system replacemsg spam "smtp-spam-feip"
end
config system replacemsg spam "smtp-spam-helo"
end
config system replacemsg spam "smtp-spam-emailblock-to"
end
config system replacemsg spam "smtp-spam-emailblock-from"
end
config system replacemsg spam "smtp-spam-emailblock-subject"
end
config system replacemsg spam "smtp-spam-mimeheader"
end
config system replacemsg spam "reversedns"
end
config system replacemsg spam "smtp-spam-ase"
end
config system replacemsg spam "submit"
end
config system replacemsg alertmail "alertmail-virus"
end
config system replacemsg alertmail "alertmail-block"
end
config system replacemsg alertmail "alertmail-nids-event"
end
config system replacemsg alertmail "alertmail-crit-event"
end
config system replacemsg alertmail "alertmail-disk-full"
end
config system replacemsg admin "pre_admin-disclaimer-text"
end
config system replacemsg admin "post_admin-disclaimer-text"
end
config system replacemsg auth "auth-disclaimer-page-1"
end
config system replacemsg auth "auth-disclaimer-page-2"
end
config system replacemsg auth "auth-disclaimer-page-3"
end
config system replacemsg auth "auth-proxy-reject-page"
end
config system replacemsg auth "auth-reject-page"
end
config system replacemsg auth "auth-login-page"
end
config system replacemsg auth "auth-login-failed-page"
end
config system replacemsg auth "auth-token-login-page"
end
config system replacemsg auth "auth-token-login-failed-page"
end
config system replacemsg auth "auth-success-msg"
end
config system replacemsg auth "auth-challenge-page"
end
config system replacemsg auth "auth-keepalive-page"
end
config system replacemsg auth "auth-portal-page"
end
config system replacemsg auth "auth-password-page"
end
config system replacemsg auth "auth-fortitoken-page"
end
config system replacemsg auth "auth-next-fortitoken-page"
end
config system replacemsg auth "auth-email-token-page"
end
config system replacemsg auth "auth-sms-token-page"
end
config system replacemsg auth "auth-email-harvesting-page"
end
config system replacemsg auth "auth-email-failed-page"
end
config system replacemsg auth "auth-cert-passwd-page"
end
config system replacemsg auth "auth-guest-print-page"
end
config system replacemsg auth "auth-guest-email-page"
end
config system replacemsg auth "auth-success-page"
end
config system replacemsg auth "auth-block-notification-page"
end
config system replacemsg auth "auth-quarantine-page"
end
config system replacemsg auth "auth-qtn-reject-page"
end
config system replacemsg auth "auth-saml-page"
end
config system replacemsg sslvpn "sslvpn-login"
end
config system replacemsg sslvpn "sslvpn-header"
end
config system replacemsg sslvpn "sslvpn-limit"
end
config system replacemsg sslvpn "hostcheck-error"
end
config system replacemsg sslvpn "sslvpn-provision-user"
end
config system replacemsg sslvpn "sslvpn-provision-user-sms"
end
config system replacemsg nac-quar "nac-quar-virus"
end
config system replacemsg nac-quar "nac-quar-dos"
end
config system replacemsg nac-quar "nac-quar-ips"
end
config system replacemsg nac-quar "nac-quar-dlp"
end
config system replacemsg nac-quar "nac-quar-admin"
end
config system replacemsg nac-quar "nac-quar-app"
end
config system replacemsg traffic-quota "per-ip-shaper-block"
end
config system replacemsg utm "virus-html"
end
config system replacemsg utm "client-virus-html"
end
config system replacemsg utm "virus-text"
end
config system replacemsg utm "dlp-html"
end
config system replacemsg utm "dlp-text"
end
config system replacemsg utm "appblk-html"
end
config system replacemsg utm "ipsblk-html"
end
config system replacemsg utm "virpatchblk-html"
end
config system replacemsg utm "ipsfail-html"
end
config system replacemsg utm "exe-text"
end
config system replacemsg utm "waf-html"
end
config system replacemsg utm "outbreak-prevention-html"
end
config system replacemsg utm "outbreak-prevention-text"
end
config system replacemsg utm "external-blocklist-html"
end
config system replacemsg utm "external-blocklist-text"
end
config system replacemsg utm "ems-threat-feed-html"
end
config system replacemsg utm "ems-threat-feed-text"
end
config system replacemsg utm "file-filter-html"
end
config system replacemsg utm "file-filter-text"
end
config system replacemsg utm "file-size-text"
end
config system replacemsg utm "transfer-size-text"
end
config system replacemsg utm "internal-error-text"
end
config system replacemsg utm "archive-block-html"
end
config system replacemsg utm "archive-block-text"
end
config system replacemsg utm "file-av-fail-text"
end
config system replacemsg utm "transfer-av-fail-text"
end
config system replacemsg utm "banned-word-html"
end
config system replacemsg utm "banned-word-text"
end
config system replacemsg utm "block-html"
end
config system replacemsg utm "block-text"
end
config system replacemsg utm "decompress-limit-text"
end
config system replacemsg utm "dlp-subject-text"
end
config system replacemsg utm "file-size-html"
end
config system replacemsg utm "client-file-size-html"
end
config system replacemsg utm "inline-scan-timeout-html"
end
config system replacemsg utm "inline-scan-timeout-text"
end
config system replacemsg utm "inline-scan-error-html"
end
config system replacemsg utm "inline-scan-error-text"
end
config system replacemsg utm "icap-block-text"
end
config system replacemsg utm "icap-error-text"
end
config system replacemsg utm "icap-http-error"
end
config system replacemsg icap "icap-req-resp"
end
config system replacemsg automation "automation-email"
end
config system snmp sysinfo
end
config system autoupdate schedule
    set frequency daily
    set time 01:60
end
config firewall internet-service-name
    edit "Google-Other"
        set internet-service-id 65536
    next
    edit "Google-Web"
        set internet-service-id 65537
    next
    edit "Google-ICMP"
        set internet-service-id 65538
    next
    edit "Google-DNS"
        set internet-service-id 65539
    next
    edit "Google-Outbound_Email"
        set internet-service-id 65540
    next
    edit "Google-SSH"
        set internet-service-id 65542
    next
    edit "Google-FTP"
        set internet-service-id 65543
    next
    edit "Google-NTP"
        set internet-service-id 65544
    next
    edit "Google-Inbound_Email"
        set internet-service-id 65545
    next
    edit "Google-LDAP"
        set internet-service-id 65550
    next
    edit "Google-NetBIOS.Session.Service"
        set internet-service-id 65551
    next
    edit "Google-RTMP"
        set internet-service-id 65552
    next
    edit "Google-NetBIOS.Name.Service"
        set internet-service-id 65560
    next
    edit "Google-Google.Cloud"
        set internet-service-id 65641
    next
    edit "Google-Google.Bot"
        set internet-service-id 65643
    next
    edit "Google-Gmail"
        set internet-service-id 65646
    next
    edit "Meta-Other"
        set internet-service-id 131072
    next
    edit "Meta-Web"
        set internet-service-id 131073
    next
    edit "Meta-ICMP"
        set internet-service-id 131074
    next
    edit "Meta-DNS"
        set internet-service-id 131075
    next
    edit "Meta-Outbound_Email"
        set internet-service-id 131076
    next
    edit "Meta-SSH"
        set internet-service-id 131078
    next
    edit "Meta-FTP"
        set internet-service-id 131079
    next
    edit "Meta-NTP"
        set internet-service-id 131080
    next
    edit "Meta-Inbound_Email"
        set internet-service-id 131081
    next
    edit "Meta-LDAP"
        set internet-service-id 131086
    next
    edit "Meta-NetBIOS.Session.Service"
        set internet-service-id 131087
    next
    edit "Meta-RTMP"
        set internet-service-id 131088
    next
    edit "Meta-NetBIOS.Name.Service"
        set internet-service-id 131096
    next
    edit "Meta-Whatsapp"
        set internet-service-id 131184
    next
    edit "Meta-Instagram"
        set internet-service-id 131189
    next
    edit "Apple-Other"
        set internet-service-id 196608
    next
    edit "Apple-Web"
        set internet-service-id 196609
    next
    edit "Apple-ICMP"
        set internet-service-id 196610
    next
    edit "Apple-DNS"
        set internet-service-id 196611
    next
    edit "Apple-Outbound_Email"
        set internet-service-id 196612
    next
    edit "Apple-SSH"
        set internet-service-id 196614
    next
    edit "Apple-FTP"
        set internet-service-id 196615
    next
    edit "Apple-NTP"
        set internet-service-id 196616
    next
    edit "Apple-Inbound_Email"
        set internet-service-id 196617
    next
    edit "Apple-LDAP"
        set internet-service-id 196622
    next
    edit "Apple-NetBIOS.Session.Service"
        set internet-service-id 196623
    next
    edit "Apple-RTMP"
        set internet-service-id 196624
    next
    edit "Apple-NetBIOS.Name.Service"
        set internet-service-id 196632
    next
    edit "Apple-App.Store"
        set internet-service-id 196723
    next
    edit "Apple-APNs"
        set internet-service-id 196747
    next
    edit "Yahoo-Other"
        set internet-service-id 262144
    next
    edit "Yahoo-Web"
        set internet-service-id 262145
    next
    edit "Yahoo-ICMP"
        set internet-service-id 262146
    next
    edit "Yahoo-DNS"
        set internet-service-id 262147
    next
    edit "Yahoo-Outbound_Email"
        set internet-service-id 262148
    next
    edit "Yahoo-SSH"
        set internet-service-id 262150
    next
    edit "Yahoo-FTP"
        set internet-service-id 262151
    next
    edit "Yahoo-NTP"
        set internet-service-id 262152
    next
    edit "Yahoo-Inbound_Email"
        set internet-service-id 262153
    next
    edit "Yahoo-LDAP"
        set internet-service-id 262158
    next
    edit "Yahoo-NetBIOS.Session.Service"
        set internet-service-id 262159
    next
    edit "Yahoo-RTMP"
        set internet-service-id 262160
    next
    edit "Yahoo-NetBIOS.Name.Service"
        set internet-service-id 262168
    next
    edit "Microsoft-Other"
        set internet-service-id 327680
    next
    edit "Microsoft-Web"
        set internet-service-id 327681
    next
    edit "Microsoft-ICMP"
        set internet-service-id 327682
    next
    edit "Microsoft-DNS"
        set internet-service-id 327683
    next
    edit "Microsoft-Outbound_Email"
        set internet-service-id 327684
    next
    edit "Microsoft-SSH"
        set internet-service-id 327686
    next
    edit "Microsoft-FTP"
        set internet-service-id 327687
    next
    edit "Microsoft-NTP"
        set internet-service-id 327688
    next
    edit "Microsoft-Inbound_Email"
        set internet-service-id 327689
    next
    edit "Microsoft-LDAP"
        set internet-service-id 327694
    next
    edit "Microsoft-NetBIOS.Session.Service"
        set internet-service-id 327695
    next
    edit "Microsoft-RTMP"
        set internet-service-id 327696
    next
    edit "Microsoft-NetBIOS.Name.Service"
        set internet-service-id 327704
    next
    edit "Microsoft-Skype_Teams"
        set internet-service-id 327781
    next
    edit "Microsoft-Office365"
        set internet-service-id 327782
    next
    edit "Microsoft-Azure"
        set internet-service-id 327786
    next
    edit "Microsoft-Bing.Bot"
        set internet-service-id 327788
    next
    edit "Microsoft-Outlook"
        set internet-service-id 327791
    next
    edit "Microsoft-Microsoft.Update"
        set internet-service-id 327793
    next
    edit "Microsoft-Dynamics"
        set internet-service-id 327837
    next
    edit "Microsoft-WNS"
        set internet-service-id 327839
    next
    edit "Microsoft-Office365.Published"
        set internet-service-id 327880
    next
    edit "Microsoft-Intune"
        set internet-service-id 327886
    next
    edit "Microsoft-Office365.Published.Optimize"
        set internet-service-id 327902
    next
    edit "Microsoft-Office365.Published.Allow"
        set internet-service-id 327903
    next
    edit "Microsoft-Office365.Published.USGOV"
        set internet-service-id 327917
    next
    edit "Microsoft-Azure.Monitor"
        set internet-service-id 327958
    next
    edit "Microsoft-Azure.SQL"
        set internet-service-id 327959
    next
    edit "Microsoft-Azure.AD"
        set internet-service-id 327960
    next
    edit "Microsoft-Azure.Data.Factory"
        set internet-service-id 327961
    next
    edit "Microsoft-Azure.Virtual.Desktop"
        set internet-service-id 327962
    next
    edit "Microsoft-Azure.Power.BI"
        set internet-service-id 327963
    next
    edit "Amazon-Other"
        set internet-service-id 393216
    next
    edit "Amazon-Web"
        set internet-service-id 393217
    next
    edit "Amazon-ICMP"
        set internet-service-id 393218
    next
    edit "Amazon-DNS"
        set internet-service-id 393219
    next
    edit "Amazon-Outbound_Email"
        set internet-service-id 393220
    next
    edit "Amazon-SSH"
        set internet-service-id 393222
    next
    edit "Amazon-FTP"
        set internet-service-id 393223
    next
    edit "Amazon-NTP"
        set internet-service-id 393224
    next
    edit "Amazon-Inbound_Email"
        set internet-service-id 393225
    next
    edit "Amazon-LDAP"
        set internet-service-id 393230
    next
    edit "Amazon-NetBIOS.Session.Service"
        set internet-service-id 393231
    next
    edit "Amazon-RTMP"
        set internet-service-id 393232
    next
    edit "Amazon-NetBIOS.Name.Service"
        set internet-service-id 393240
    next
    edit "Amazon-AWS"
        set internet-service-id 393320
    next
    edit "Amazon-AWS.WorkSpaces.Gateway"
        set internet-service-id 393403
    next
    edit "Amazon-Twitch"
        set internet-service-id 393446
    next
    edit "Amazon-AWS.GovCloud.US"
        set internet-service-id 393452
    next
    edit "Amazon-AWS.EBS"
        set internet-service-id 393470
    next
    edit "Amazon-AWS.Cloud9"
        set internet-service-id 393471
    next
    edit "Amazon-AWS.DynamoDB"
        set internet-service-id 393472
    next
    edit "Amazon-AWS.Route53"
        set internet-service-id 393473
    next
    edit "Amazon-AWS.S3"
        set internet-service-id 393474
    next
    edit "Amazon-AWS.Kinesis.Video.Streams"
        set internet-service-id 393475
    next
    edit "Amazon-AWS.Global.Accelerator"
        set internet-service-id 393476
    next
    edit "Amazon-AWS.EC2"
        set internet-service-id 393477
    next
    edit "Amazon-AWS.API.Gateway"
        set internet-service-id 393478
    next
    edit "Amazon-AWS.Chime.Voice.Connector"
        set internet-service-id 393479
    next
    edit "Amazon-AWS.Connect"
        set internet-service-id 393480
    next
    edit "Amazon-AWS.CloudFront"
        set internet-service-id 393481
    next
    edit "Amazon-AWS.CodeBuild"
        set internet-service-id 393482
    next
    edit "Amazon-AWS.Chime.Meetings"
        set internet-service-id 393483
    next
    edit "Amazon-AWS.AppFlow"
        set internet-service-id 393484
    next
    edit "Amazon-Amazon.SES"
        set internet-service-id 393493
    next
    edit "eBay-Other"
        set internet-service-id 458752
    next
    edit "eBay-Web"
        set internet-service-id 458753
    next
    edit "eBay-ICMP"
        set internet-service-id 458754
    next
    edit "eBay-DNS"
        set internet-service-id 458755
    next
    edit "eBay-Outbound_Email"
        set internet-service-id 458756
    next
    edit "eBay-SSH"
        set internet-service-id 458758
    next
    edit "eBay-FTP"
        set internet-service-id 458759
    next
    edit "eBay-NTP"
        set internet-service-id 458760
    next
    edit "eBay-Inbound_Email"
        set internet-service-id 458761
    next
    edit "eBay-LDAP"
        set internet-service-id 458766
    next
    edit "eBay-NetBIOS.Session.Service"
        set internet-service-id 458767
    next
    edit "eBay-RTMP"
        set internet-service-id 458768
    next
    edit "eBay-NetBIOS.Name.Service"
        set internet-service-id 458776
    next
    edit "PayPal-Other"
        set internet-service-id 524288
    next
    edit "PayPal-Web"
        set internet-service-id 524289
    next
    edit "PayPal-ICMP"
        set internet-service-id 524290
    next
    edit "PayPal-DNS"
        set internet-service-id 524291
    next
    edit "PayPal-Outbound_Email"
        set internet-service-id 524292
    next
    edit "PayPal-SSH"
        set internet-service-id 524294
    next
    edit "PayPal-FTP"
        set internet-service-id 524295
    next
    edit "PayPal-NTP"
        set internet-service-id 524296
    next
    edit "PayPal-Inbound_Email"
        set internet-service-id 524297
    next
    edit "PayPal-LDAP"
        set internet-service-id 524302
    next
    edit "PayPal-NetBIOS.Session.Service"
        set internet-service-id 524303
    next
    edit "PayPal-RTMP"
        set internet-service-id 524304
    next
    edit "PayPal-NetBIOS.Name.Service"
        set internet-service-id 524312
    next
    edit "Box-Other"
        set internet-service-id 589824
    next
    edit "Box-Web"
        set internet-service-id 589825
    next
    edit "Box-ICMP"
        set internet-service-id 589826
    next
    edit "Box-DNS"
        set internet-service-id 589827
    next
    edit "Box-Outbound_Email"
        set internet-service-id 589828
    next
    edit "Box-SSH"
        set internet-service-id 589830
    next
    edit "Box-FTP"
        set internet-service-id 589831
    next
    edit "Box-NTP"
        set internet-service-id 589832
    next
    edit "Box-Inbound_Email"
        set internet-service-id 589833
    next
    edit "Box-LDAP"
        set internet-service-id 589838
    next
    edit "Box-NetBIOS.Session.Service"
        set internet-service-id 589839
    next
    edit "Box-RTMP"
        set internet-service-id 589840
    next
    edit "Box-NetBIOS.Name.Service"
        set internet-service-id 589848
    next
    edit "Salesforce-Other"
        set internet-service-id 655360
    next
    edit "Salesforce-Web"
        set internet-service-id 655361
    next
    edit "Salesforce-ICMP"
        set internet-service-id 655362
    next
    edit "Salesforce-DNS"
        set internet-service-id 655363
    next
    edit "Salesforce-Outbound_Email"
        set internet-service-id 655364
    next
    edit "Salesforce-SSH"
        set internet-service-id 655366
    next
    edit "Salesforce-FTP"
        set internet-service-id 655367
    next
    edit "Salesforce-NTP"
        set internet-service-id 655368
    next
    edit "Salesforce-Inbound_Email"
        set internet-service-id 655369
    next
    edit "Salesforce-LDAP"
        set internet-service-id 655374
    next
    edit "Salesforce-NetBIOS.Session.Service"
        set internet-service-id 655375
    next
    edit "Salesforce-RTMP"
        set internet-service-id 655376
    next
    edit "Salesforce-NetBIOS.Name.Service"
        set internet-service-id 655384
    next
    edit "Salesforce-Email.Relay"
        set internet-service-id 655530
    next
    edit "Dropbox-Other"
        set internet-service-id 720896
    next
    edit "Dropbox-Web"
        set internet-service-id 720897
    next
    edit "Dropbox-ICMP"
        set internet-service-id 720898
    next
    edit "Dropbox-DNS"
        set internet-service-id 720899
    next
    edit "Dropbox-Outbound_Email"
        set internet-service-id 720900
    next
    edit "Dropbox-SSH"
        set internet-service-id 720902
    next
    edit "Dropbox-FTP"
        set internet-service-id 720903
    next
    edit "Dropbox-NTP"
        set internet-service-id 720904
    next
    edit "Dropbox-Inbound_Email"
        set internet-service-id 720905
    next
    edit "Dropbox-LDAP"
        set internet-service-id 720910
    next
    edit "Dropbox-NetBIOS.Session.Service"
        set internet-service-id 720911
    next
    edit "Dropbox-RTMP"
        set internet-service-id 720912
    next
    edit "Dropbox-NetBIOS.Name.Service"
        set internet-service-id 720920
    next
    edit "Netflix-Other"
        set internet-service-id 786432
    next
    edit "Netflix-Web"
        set internet-service-id 786433
    next
    edit "Netflix-ICMP"
        set internet-service-id 786434
    next
    edit "Netflix-DNS"
        set internet-service-id 786435
    next
    edit "Netflix-Outbound_Email"
        set internet-service-id 786436
    next
    edit "Netflix-SSH"
        set internet-service-id 786438
    next
    edit "Netflix-FTP"
        set internet-service-id 786439
    next
    edit "Netflix-NTP"
        set internet-service-id 786440
    next
    edit "Netflix-Inbound_Email"
        set internet-service-id 786441
    next
    edit "Netflix-LDAP"
        set internet-service-id 786446
    next
    edit "Netflix-NetBIOS.Session.Service"
        set internet-service-id 786447
    next
    edit "Netflix-RTMP"
        set internet-service-id 786448
    next
    edit "Netflix-NetBIOS.Name.Service"
        set internet-service-id 786456
    next
    edit "LinkedIn-Other"
        set internet-service-id 851968
    next
    edit "LinkedIn-Web"
        set internet-service-id 851969
    next
    edit "LinkedIn-ICMP"
        set internet-service-id 851970
    next
    edit "LinkedIn-DNS"
        set internet-service-id 851971
    next
    edit "LinkedIn-Outbound_Email"
        set internet-service-id 851972
    next
    edit "LinkedIn-SSH"
        set internet-service-id 851974
    next
    edit "LinkedIn-FTP"
        set internet-service-id 851975
    next
    edit "LinkedIn-NTP"
        set internet-service-id 851976
    next
    edit "LinkedIn-Inbound_Email"
        set internet-service-id 851977
    next
    edit "LinkedIn-LDAP"
        set internet-service-id 851982
    next
    edit "LinkedIn-NetBIOS.Session.Service"
        set internet-service-id 851983
    next
    edit "LinkedIn-RTMP"
        set internet-service-id 851984
    next
    edit "LinkedIn-NetBIOS.Name.Service"
        set internet-service-id 851992
    next
    edit "Adobe-Other"
        set internet-service-id 917504
    next
    edit "Adobe-Web"
        set internet-service-id 917505
    next
    edit "Adobe-ICMP"
        set internet-service-id 917506
    next
    edit "Adobe-DNS"
        set internet-service-id 917507
    next
    edit "Adobe-Outbound_Email"
        set internet-service-id 917508
    next
    edit "Adobe-SSH"
        set internet-service-id 917510
    next
    edit "Adobe-FTP"
        set internet-service-id 917511
    next
    edit "Adobe-NTP"
        set internet-service-id 917512
    next
    edit "Adobe-Inbound_Email"
        set internet-service-id 917513
    next
    edit "Adobe-LDAP"
        set internet-service-id 917518
    next
    edit "Adobe-NetBIOS.Session.Service"
        set internet-service-id 917519
    next
    edit "Adobe-RTMP"
        set internet-service-id 917520
    next
    edit "Adobe-NetBIOS.Name.Service"
        set internet-service-id 917528
    next
    edit "Adobe-Adobe.Experience.Cloud"
        set internet-service-id 917640
    next
    edit "Adobe-Adobe.Sign"
        set internet-service-id 917776
    next
    edit "Oracle-Other"
        set internet-service-id 983040
    next
    edit "Oracle-Web"
        set internet-service-id 983041
    next
    edit "Oracle-ICMP"
        set internet-service-id 983042
    next
    edit "Oracle-DNS"
        set internet-service-id 983043
    next
    edit "Oracle-Outbound_Email"
        set internet-service-id 983044
    next
    edit "Oracle-SSH"
        set internet-service-id 983046
    next
    edit "Oracle-FTP"
        set internet-service-id 983047
    next
    edit "Oracle-NTP"
        set internet-service-id 983048
    next
    edit "Oracle-Inbound_Email"
        set internet-service-id 983049
    next
    edit "Oracle-LDAP"
        set internet-service-id 983054
    next
    edit "Oracle-NetBIOS.Session.Service"
        set internet-service-id 983055
    next
    edit "Oracle-RTMP"
        set internet-service-id 983056
    next
    edit "Oracle-NetBIOS.Name.Service"
        set internet-service-id 983064
    next
    edit "Oracle-Oracle.Cloud"
        set internet-service-id 983171
    next
    edit "Hulu-Other"
        set internet-service-id 1048576
    next
    edit "Hulu-Web"
        set internet-service-id 1048577
    next
    edit "Hulu-ICMP"
        set internet-service-id 1048578
    next
    edit "Hulu-DNS"
        set internet-service-id 1048579
    next
    edit "Hulu-Outbound_Email"
        set internet-service-id 1048580
    next
    edit "Hulu-SSH"
        set internet-service-id 1048582
    next
    edit "Hulu-FTP"
        set internet-service-id 1048583
    next
    edit "Hulu-NTP"
        set internet-service-id 1048584
    next
    edit "Hulu-Inbound_Email"
        set internet-service-id 1048585
    next
    edit "Hulu-LDAP"
        set internet-service-id 1048590
    next
    edit "Hulu-NetBIOS.Session.Service"
        set internet-service-id 1048591
    next
    edit "Hulu-RTMP"
        set internet-service-id 1048592
    next
    edit "Hulu-NetBIOS.Name.Service"
        set internet-service-id 1048600
    next
    edit "Pinterest-Other"
        set internet-service-id 1114112
    next
    edit "Pinterest-Web"
        set internet-service-id 1114113
    next
    edit "Pinterest-ICMP"
        set internet-service-id 1114114
    next
    edit "Pinterest-DNS"
        set internet-service-id 1114115
    next
    edit "Pinterest-Outbound_Email"
        set internet-service-id 1114116
    next
    edit "Pinterest-SSH"
        set internet-service-id 1114118
    next
    edit "Pinterest-FTP"
        set internet-service-id 1114119
    next
    edit "Pinterest-NTP"
        set internet-service-id 1114120
    next
    edit "Pinterest-Inbound_Email"
        set internet-service-id 1114121
    next
    edit "Pinterest-LDAP"
        set internet-service-id 1114126
    next
    edit "Pinterest-NetBIOS.Session.Service"
        set internet-service-id 1114127
    next
    edit "Pinterest-RTMP"
        set internet-service-id 1114128
    next
    edit "Pinterest-NetBIOS.Name.Service"
        set internet-service-id 1114136
    next
    edit "LogMeIn-Other"
        set internet-service-id 1179648
    next
    edit "LogMeIn-Web"
        set internet-service-id 1179649
    next
    edit "LogMeIn-ICMP"
        set internet-service-id 1179650
    next
    edit "LogMeIn-DNS"
        set internet-service-id 1179651
    next
    edit "LogMeIn-Outbound_Email"
        set internet-service-id 1179652
    next
    edit "LogMeIn-SSH"
        set internet-service-id 1179654
    next
    edit "LogMeIn-FTP"
        set internet-service-id 1179655
    next
    edit "LogMeIn-NTP"
        set internet-service-id 1179656
    next
    edit "LogMeIn-Inbound_Email"
        set internet-service-id 1179657
    next
    edit "LogMeIn-LDAP"
        set internet-service-id 1179662
    next
    edit "LogMeIn-NetBIOS.Session.Service"
        set internet-service-id 1179663
    next
    edit "LogMeIn-RTMP"
        set internet-service-id 1179664
    next
    edit "LogMeIn-NetBIOS.Name.Service"
        set internet-service-id 1179672
    next
    edit "LogMeIn-GoTo.Suite"
        set internet-service-id 1179767
    next
    edit "Fortinet-Other"
        set internet-service-id 1245184
    next
    edit "Fortinet-Web"
        set internet-service-id 1245185
    next
    edit "Fortinet-ICMP"
        set internet-service-id 1245186
    next
    edit "Fortinet-DNS"
        set internet-service-id 1245187
    next
    edit "Fortinet-Outbound_Email"
        set internet-service-id 1245188
    next
    edit "Fortinet-SSH"
        set internet-service-id 1245190
    next
    edit "Fortinet-FTP"
        set internet-service-id 1245191
    next
    edit "Fortinet-NTP"
        set internet-service-id 1245192
    next
    edit "Fortinet-Inbound_Email"
        set internet-service-id 1245193
    next
    edit "Fortinet-LDAP"
        set internet-service-id 1245198
    next
    edit "Fortinet-NetBIOS.Session.Service"
        set internet-service-id 1245199
    next
    edit "Fortinet-RTMP"
        set internet-service-id 1245200
    next
    edit "Fortinet-NetBIOS.Name.Service"
        set internet-service-id 1245208
    next
    edit "Fortinet-FortiGuard"
        set internet-service-id 1245324
    next
    edit "Fortinet-FortiMail.Cloud"
        set internet-service-id 1245325
    next
    edit "Fortinet-FortiCloud"
        set internet-service-id 1245326
    next
    edit "Fortinet-FortiVoice.Cloud"
        set internet-service-id 1245432
    next
    edit "Fortinet-FortiGuard.Secure.DNS"
        set internet-service-id 1245454
    next
    edit "Fortinet-FortiEDR"
        set internet-service-id 1245475
    next
    edit "Kaspersky-Other"
        set internet-service-id 1310720
    next
    edit "Kaspersky-Web"
        set internet-service-id 1310721
    next
    edit "Kaspersky-ICMP"
        set internet-service-id 1310722
    next
    edit "Kaspersky-DNS"
        set internet-service-id 1310723
    next
    edit "Kaspersky-Outbound_Email"
        set internet-service-id 1310724
    next
    edit "Kaspersky-SSH"
        set internet-service-id 1310726
    next
    edit "Kaspersky-FTP"
        set internet-service-id 1310727
    next
    edit "Kaspersky-NTP"
        set internet-service-id 1310728
    next
    edit "Kaspersky-Inbound_Email"
        set internet-service-id 1310729
    next
    edit "Kaspersky-LDAP"
        set internet-service-id 1310734
    next
    edit "Kaspersky-NetBIOS.Session.Service"
        set internet-service-id 1310735
    next
    edit "Kaspersky-RTMP"
        set internet-service-id 1310736
    next
    edit "Kaspersky-NetBIOS.Name.Service"
        set internet-service-id 1310744
    next
    edit "McAfee-Other"
        set internet-service-id 1376256
    next
    edit "McAfee-Web"
        set internet-service-id 1376257
    next
    edit "McAfee-ICMP"
        set internet-service-id 1376258
    next
    edit "McAfee-DNS"
        set internet-service-id 1376259
    next
    edit "McAfee-Outbound_Email"
        set internet-service-id 1376260
    next
    edit "McAfee-SSH"
        set internet-service-id 1376262
    next
    edit "McAfee-FTP"
        set internet-service-id 1376263
    next
    edit "McAfee-NTP"
        set internet-service-id 1376264
    next
    edit "McAfee-Inbound_Email"
        set internet-service-id 1376265
    next
    edit "McAfee-LDAP"
        set internet-service-id 1376270
    next
    edit "McAfee-NetBIOS.Session.Service"
        set internet-service-id 1376271
    next
    edit "McAfee-RTMP"
        set internet-service-id 1376272
    next
    edit "McAfee-NetBIOS.Name.Service"
        set internet-service-id 1376280
    next
    edit "Symantec-Other"
        set internet-service-id 1441792
    next
    edit "Symantec-Web"
        set internet-service-id 1441793
    next
    edit "Symantec-ICMP"
        set internet-service-id 1441794
    next
    edit "Symantec-DNS"
        set internet-service-id 1441795
    next
    edit "Symantec-Outbound_Email"
        set internet-service-id 1441796
    next
    edit "Symantec-SSH"
        set internet-service-id 1441798
    next
    edit "Symantec-FTP"
        set internet-service-id 1441799
    next
    edit "Symantec-NTP"
        set internet-service-id 1441800
    next
    edit "Symantec-Inbound_Email"
        set internet-service-id 1441801
    next
    edit "Symantec-LDAP"
        set internet-service-id 1441806
    next
    edit "Symantec-NetBIOS.Session.Service"
        set internet-service-id 1441807
    next
    edit "Symantec-RTMP"
        set internet-service-id 1441808
    next
    edit "Symantec-NetBIOS.Name.Service"
        set internet-service-id 1441816
    next
    edit "Symantec-Symantec.Cloud"
        set internet-service-id 1441922
    next
    edit "VMware-Other"
        set internet-service-id 1507328
    next
    edit "VMware-Web"
        set internet-service-id 1507329
    next
    edit "VMware-ICMP"
        set internet-service-id 1507330
    next
    edit "VMware-DNS"
        set internet-service-id 1507331
    next
    edit "VMware-Outbound_Email"
        set internet-service-id 1507332
    next
    edit "VMware-SSH"
        set internet-service-id 1507334
    next
    edit "VMware-FTP"
        set internet-service-id 1507335
    next
    edit "VMware-NTP"
        set internet-service-id 1507336
    next
    edit "VMware-Inbound_Email"
        set internet-service-id 1507337
    next
    edit "VMware-LDAP"
        set internet-service-id 1507342
    next
    edit "VMware-NetBIOS.Session.Service"
        set internet-service-id 1507343
    next
    edit "VMware-RTMP"
        set internet-service-id 1507344
    next
    edit "VMware-NetBIOS.Name.Service"
        set internet-service-id 1507352
    next
    edit "VMware-Workspace.ONE"
        set internet-service-id 1507461
    next
    edit "AOL-Other"
        set internet-service-id 1572864
    next
    edit "AOL-Web"
        set internet-service-id 1572865
    next
    edit "AOL-ICMP"
        set internet-service-id 1572866
    next
    edit "AOL-DNS"
        set internet-service-id 1572867
    next
    edit "AOL-Outbound_Email"
        set internet-service-id 1572868
    next
    edit "AOL-SSH"
        set internet-service-id 1572870
    next
    edit "AOL-FTP"
        set internet-service-id 1572871
    next
    edit "AOL-NTP"
        set internet-service-id 1572872
    next
    edit "AOL-Inbound_Email"
        set internet-service-id 1572873
    next
    edit "AOL-LDAP"
        set internet-service-id 1572878
    next
    edit "AOL-NetBIOS.Session.Service"
        set internet-service-id 1572879
    next
    edit "AOL-RTMP"
        set internet-service-id 1572880
    next
    edit "AOL-NetBIOS.Name.Service"
        set internet-service-id 1572888
    next
    edit "RealNetworks-Other"
        set internet-service-id 1638400
    next
    edit "RealNetworks-Web"
        set internet-service-id 1638401
    next
    edit "RealNetworks-ICMP"
        set internet-service-id 1638402
    next
    edit "RealNetworks-DNS"
        set internet-service-id 1638403
    next
    edit "RealNetworks-Outbound_Email"
        set internet-service-id 1638404
    next
    edit "RealNetworks-SSH"
        set internet-service-id 1638406
    next
    edit "RealNetworks-FTP"
        set internet-service-id 1638407
    next
    edit "RealNetworks-NTP"
        set internet-service-id 1638408
    next
    edit "RealNetworks-Inbound_Email"
        set internet-service-id 1638409
    next
    edit "RealNetworks-LDAP"
        set internet-service-id 1638414
    next
    edit "RealNetworks-NetBIOS.Session.Service"
        set internet-service-id 1638415
    next
    edit "RealNetworks-RTMP"
        set internet-service-id 1638416
    next
    edit "RealNetworks-NetBIOS.Name.Service"
        set internet-service-id 1638424
    next
    edit "Zoho-Other"
        set internet-service-id 1703936
    next
    edit "Zoho-Web"
        set internet-service-id 1703937
    next
    edit "Zoho-ICMP"
        set internet-service-id 1703938
    next
    edit "Zoho-DNS"
        set internet-service-id 1703939
    next
    edit "Zoho-Outbound_Email"
        set internet-service-id 1703940
    next
    edit "Zoho-SSH"
        set internet-service-id 1703942
    next
    edit "Zoho-FTP"
        set internet-service-id 1703943
    next
    edit "Zoho-NTP"
        set internet-service-id 1703944
    next
    edit "Zoho-Inbound_Email"
        set internet-service-id 1703945
    next
    edit "Zoho-LDAP"
        set internet-service-id 1703950
    next
    edit "Zoho-NetBIOS.Session.Service"
        set internet-service-id 1703951
    next
    edit "Zoho-RTMP"
        set internet-service-id 1703952
    next
    edit "Zoho-NetBIOS.Name.Service"
        set internet-service-id 1703960
    next
    edit "Zoho-Site24x7.Monitor"
        set internet-service-id 1704153
    next
    edit "Mozilla-Other"
        set internet-service-id 1769472
    next
    edit "Mozilla-Web"
        set internet-service-id 1769473
    next
    edit "Mozilla-ICMP"
        set internet-service-id 1769474
    next
    edit "Mozilla-DNS"
        set internet-service-id 1769475
    next
    edit "Mozilla-Outbound_Email"
        set internet-service-id 1769476
    next
    edit "Mozilla-SSH"
        set internet-service-id 1769478
    next
    edit "Mozilla-FTP"
        set internet-service-id 1769479
    next
    edit "Mozilla-NTP"
        set internet-service-id 1769480
    next
    edit "Mozilla-Inbound_Email"
        set internet-service-id 1769481
    next
    edit "Mozilla-LDAP"
        set internet-service-id 1769486
    next
    edit "Mozilla-NetBIOS.Session.Service"
        set internet-service-id 1769487
    next
    edit "Mozilla-RTMP"
        set internet-service-id 1769488
    next
    edit "Mozilla-NetBIOS.Name.Service"
        set internet-service-id 1769496
    next
    edit "TeamViewer-Other"
        set internet-service-id 1835008
    next
    edit "TeamViewer-Web"
        set internet-service-id 1835009
    next
    edit "TeamViewer-ICMP"
        set internet-service-id 1835010
    next
    edit "TeamViewer-DNS"
        set internet-service-id 1835011
    next
    edit "TeamViewer-Outbound_Email"
        set internet-service-id 1835012
    next
    edit "TeamViewer-SSH"
        set internet-service-id 1835014
    next
    edit "TeamViewer-FTP"
        set internet-service-id 1835015
    next
    edit "TeamViewer-NTP"
        set internet-service-id 1835016
    next
    edit "TeamViewer-Inbound_Email"
        set internet-service-id 1835017
    next
    edit "TeamViewer-LDAP"
        set internet-service-id 1835022
    next
    edit "TeamViewer-NetBIOS.Session.Service"
        set internet-service-id 1835023
    next
    edit "TeamViewer-RTMP"
        set internet-service-id 1835024
    next
    edit "TeamViewer-NetBIOS.Name.Service"
        set internet-service-id 1835032
    next
    edit "TeamViewer-TeamViewer"
        set internet-service-id 1835117
    next
    edit "HP-Other"
        set internet-service-id 1900544
    next
    edit "HP-Web"
        set internet-service-id 1900545
    next
    edit "HP-ICMP"
        set internet-service-id 1900546
    next
    edit "HP-DNS"
        set internet-service-id 1900547
    next
    edit "HP-Outbound_Email"
        set internet-service-id 1900548
    next
    edit "HP-SSH"
        set internet-service-id 1900550
    next
    edit "HP-FTP"
        set internet-service-id 1900551
    next
    edit "HP-NTP"
        set internet-service-id 1900552
    next
    edit "HP-Inbound_Email"
        set internet-service-id 1900553
    next
    edit "HP-LDAP"
        set internet-service-id 1900558
    next
    edit "HP-NetBIOS.Session.Service"
        set internet-service-id 1900559
    next
    edit "HP-RTMP"
        set internet-service-id 1900560
    next
    edit "HP-NetBIOS.Name.Service"
        set internet-service-id 1900568
    next
    edit "HP-Aruba"
        set internet-service-id 1900726
    next
    edit "Cisco-Other"
        set internet-service-id 1966080
    next
    edit "Cisco-Web"
        set internet-service-id 1966081
    next
    edit "Cisco-ICMP"
        set internet-service-id 1966082
    next
    edit "Cisco-DNS"
        set internet-service-id 1966083
    next
    edit "Cisco-Outbound_Email"
        set internet-service-id 1966084
    next
    edit "Cisco-SSH"
        set internet-service-id 1966086
    next
    edit "Cisco-FTP"
        set internet-service-id 1966087
    next
    edit "Cisco-NTP"
        set internet-service-id 1966088
    next
    edit "Cisco-Inbound_Email"
        set internet-service-id 1966089
    next
    edit "Cisco-LDAP"
        set internet-service-id 1966094
    next
    edit "Cisco-NetBIOS.Session.Service"
        set internet-service-id 1966095
    next
    edit "Cisco-RTMP"
        set internet-service-id 1966096
    next
    edit "Cisco-NetBIOS.Name.Service"
        set internet-service-id 1966104
    next
    edit "Cisco-Webex"
        set internet-service-id 1966183
    next
    edit "Cisco-Meraki.Cloud"
        set internet-service-id 1966218
    next
    edit "Cisco-Duo.Security"
        set internet-service-id 1966225
    next
    edit "Cisco-AppDynamic"
        set internet-service-id 1966260
    next
    edit "Cisco-Webex.FedRAMP"
        set internet-service-id 1966315
    next
    edit "Cisco-Secure.Endpoint"
        set internet-service-id 1966324
    next
    edit "IBM-Other"
        set internet-service-id 2031616
    next
    edit "IBM-Web"
        set internet-service-id 2031617
    next
    edit "IBM-ICMP"
        set internet-service-id 2031618
    next
    edit "IBM-DNS"
        set internet-service-id 2031619
    next
    edit "IBM-Outbound_Email"
        set internet-service-id 2031620
    next
    edit "IBM-SSH"
        set internet-service-id 2031622
    next
    edit "IBM-FTP"
        set internet-service-id 2031623
    next
    edit "IBM-NTP"
        set internet-service-id 2031624
    next
    edit "IBM-Inbound_Email"
        set internet-service-id 2031625
    next
    edit "IBM-LDAP"
        set internet-service-id 2031630
    next
    edit "IBM-NetBIOS.Session.Service"
        set internet-service-id 2031631
    next
    edit "IBM-RTMP"
        set internet-service-id 2031632
    next
    edit "IBM-NetBIOS.Name.Service"
        set internet-service-id 2031640
    next
    edit "IBM-IBM.Cloud"
        set internet-service-id 2031748
    next
    edit "Citrix-Other"
        set internet-service-id 2097152
    next
    edit "Citrix-Web"
        set internet-service-id 2097153
    next
    edit "Citrix-ICMP"
        set internet-service-id 2097154
    next
    edit "Citrix-DNS"
        set internet-service-id 2097155
    next
    edit "Citrix-Outbound_Email"
        set internet-service-id 2097156
    next
    edit "Citrix-SSH"
        set internet-service-id 2097158
    next
    edit "Citrix-FTP"
        set internet-service-id 2097159
    next
    edit "Citrix-NTP"
        set internet-service-id 2097160
    next
    edit "Citrix-Inbound_Email"
        set internet-service-id 2097161
    next
    edit "Citrix-LDAP"
        set internet-service-id 2097166
    next
    edit "Citrix-NetBIOS.Session.Service"
        set internet-service-id 2097167
    next
    edit "Citrix-RTMP"
        set internet-service-id 2097168
    next
    edit "Citrix-NetBIOS.Name.Service"
        set internet-service-id 2097176
    next
    edit "Twitter-Other"
        set internet-service-id 2162688
    next
    edit "Twitter-Web"
        set internet-service-id 2162689
    next
    edit "Twitter-ICMP"
        set internet-service-id 2162690
    next
    edit "Twitter-DNS"
        set internet-service-id 2162691
    next
    edit "Twitter-Outbound_Email"
        set internet-service-id 2162692
    next
    edit "Twitter-SSH"
        set internet-service-id 2162694
    next
    edit "Twitter-FTP"
        set internet-service-id 2162695
    next
    edit "Twitter-NTP"
        set internet-service-id 2162696
    next
    edit "Twitter-Inbound_Email"
        set internet-service-id 2162697
    next
    edit "Twitter-LDAP"
        set internet-service-id 2162702
    next
    edit "Twitter-NetBIOS.Session.Service"
        set internet-service-id 2162703
    next
    edit "Twitter-RTMP"
        set internet-service-id 2162704
    next
    edit "Twitter-NetBIOS.Name.Service"
        set internet-service-id 2162712
    next
    edit "Dell-Other"
        set internet-service-id 2228224
    next
    edit "Dell-Web"
        set internet-service-id 2228225
    next
    edit "Dell-ICMP"
        set internet-service-id 2228226
    next
    edit "Dell-DNS"
        set internet-service-id 2228227
    next
    edit "Dell-Outbound_Email"
        set internet-service-id 2228228
    next
    edit "Dell-SSH"
        set internet-service-id 2228230
    next
    edit "Dell-FTP"
        set internet-service-id 2228231
    next
    edit "Dell-NTP"
        set internet-service-id 2228232
    next
    edit "Dell-Inbound_Email"
        set internet-service-id 2228233
    next
    edit "Dell-LDAP"
        set internet-service-id 2228238
    next
    edit "Dell-NetBIOS.Session.Service"
        set internet-service-id 2228239
    next
    edit "Dell-RTMP"
        set internet-service-id 2228240
    next
    edit "Dell-NetBIOS.Name.Service"
        set internet-service-id 2228248
    next
    edit "Vimeo-Other"
        set internet-service-id 2293760
    next
    edit "Vimeo-Web"
        set internet-service-id 2293761
    next
    edit "Vimeo-ICMP"
        set internet-service-id 2293762
    next
    edit "Vimeo-DNS"
        set internet-service-id 2293763
    next
    edit "Vimeo-Outbound_Email"
        set internet-service-id 2293764
    next
    edit "Vimeo-SSH"
        set internet-service-id 2293766
    next
    edit "Vimeo-FTP"
        set internet-service-id 2293767
    next
    edit "Vimeo-NTP"
        set internet-service-id 2293768
    next
    edit "Vimeo-Inbound_Email"
        set internet-service-id 2293769
    next
    edit "Vimeo-LDAP"
        set internet-service-id 2293774
    next
    edit "Vimeo-NetBIOS.Session.Service"
        set internet-service-id 2293775
    next
    edit "Vimeo-RTMP"
        set internet-service-id 2293776
    next
    edit "Vimeo-NetBIOS.Name.Service"
        set internet-service-id 2293784
    next
    edit "Redhat-Other"
        set internet-service-id 2359296
    next
    edit "Redhat-Web"
        set internet-service-id 2359297
    next
    edit "Redhat-ICMP"
        set internet-service-id 2359298
    next
    edit "Redhat-DNS"
        set internet-service-id 2359299
    next
    edit "Redhat-Outbound_Email"
        set internet-service-id 2359300
    next
    edit "Redhat-SSH"
        set internet-service-id 2359302
    next
    edit "Redhat-FTP"
        set internet-service-id 2359303
    next
    edit "Redhat-NTP"
        set internet-service-id 2359304
    next
    edit "Redhat-Inbound_Email"
        set internet-service-id 2359305
    next
    edit "Redhat-LDAP"
        set internet-service-id 2359310
    next
    edit "Redhat-NetBIOS.Session.Service"
        set internet-service-id 2359311
    next
    edit "Redhat-RTMP"
        set internet-service-id 2359312
    next
    edit "Redhat-NetBIOS.Name.Service"
        set internet-service-id 2359320
    next
    edit "VK-Other"
        set internet-service-id 2424832
    next
    edit "VK-Web"
        set internet-service-id 2424833
    next
    edit "VK-ICMP"
        set internet-service-id 2424834
    next
    edit "VK-DNS"
        set internet-service-id 2424835
    next
    edit "VK-Outbound_Email"
        set internet-service-id 2424836
    next
    edit "VK-SSH"
        set internet-service-id 2424838
    next
    edit "VK-FTP"
        set internet-service-id 2424839
    next
    edit "VK-NTP"
        set internet-service-id 2424840
    next
    edit "VK-Inbound_Email"
        set internet-service-id 2424841
    next
    edit "VK-LDAP"
        set internet-service-id 2424846
    next
    edit "VK-NetBIOS.Session.Service"
        set internet-service-id 2424847
    next
    edit "VK-RTMP"
        set internet-service-id 2424848
    next
    edit "VK-NetBIOS.Name.Service"
        set internet-service-id 2424856
    next
    edit "TrendMicro-Other"
        set internet-service-id 2490368
    next
    edit "TrendMicro-Web"
        set internet-service-id 2490369
    next
    edit "TrendMicro-ICMP"
        set internet-service-id 2490370
    next
    edit "TrendMicro-DNS"
        set internet-service-id 2490371
    next
    edit "TrendMicro-Outbound_Email"
        set internet-service-id 2490372
    next
    edit "TrendMicro-SSH"
        set internet-service-id 2490374
    next
    edit "TrendMicro-FTP"
        set internet-service-id 2490375
    next
    edit "TrendMicro-NTP"
        set internet-service-id 2490376
    next
    edit "TrendMicro-Inbound_Email"
        set internet-service-id 2490377
    next
    edit "TrendMicro-LDAP"
        set internet-service-id 2490382
    next
    edit "TrendMicro-NetBIOS.Session.Service"
        set internet-service-id 2490383
    next
    edit "TrendMicro-RTMP"
        set internet-service-id 2490384
    next
    edit "TrendMicro-NetBIOS.Name.Service"
        set internet-service-id 2490392
    next
    edit "Tencent-Other"
        set internet-service-id 2555904
    next
    edit "Tencent-Web"
        set internet-service-id 2555905
    next
    edit "Tencent-ICMP"
        set internet-service-id 2555906
    next
    edit "Tencent-DNS"
        set internet-service-id 2555907
    next
    edit "Tencent-Outbound_Email"
        set internet-service-id 2555908
    next
    edit "Tencent-SSH"
        set internet-service-id 2555910
    next
    edit "Tencent-FTP"
        set internet-service-id 2555911
    next
    edit "Tencent-NTP"
        set internet-service-id 2555912
    next
    edit "Tencent-Inbound_Email"
        set internet-service-id 2555913
    next
    edit "Tencent-LDAP"
        set internet-service-id 2555918
    next
    edit "Tencent-NetBIOS.Session.Service"
        set internet-service-id 2555919
    next
    edit "Tencent-RTMP"
        set internet-service-id 2555920
    next
    edit "Tencent-NetBIOS.Name.Service"
        set internet-service-id 2555928
    next
    edit "Ask-Other"
        set internet-service-id 2621440
    next
    edit "Ask-Web"
        set internet-service-id 2621441
    next
    edit "Ask-ICMP"
        set internet-service-id 2621442
    next
    edit "Ask-DNS"
        set internet-service-id 2621443
    next
    edit "Ask-Outbound_Email"
        set internet-service-id 2621444
    next
    edit "Ask-SSH"
        set internet-service-id 2621446
    next
    edit "Ask-FTP"
        set internet-service-id 2621447
    next
    edit "Ask-NTP"
        set internet-service-id 2621448
    next
    edit "Ask-Inbound_Email"
        set internet-service-id 2621449
    next
    edit "Ask-LDAP"
        set internet-service-id 2621454
    next
    edit "Ask-NetBIOS.Session.Service"
        set internet-service-id 2621455
    next
    edit "Ask-RTMP"
        set internet-service-id 2621456
    next
    edit "Ask-NetBIOS.Name.Service"
        set internet-service-id 2621464
    next
    edit "CNN-Other"
        set internet-service-id 2686976
    next
    edit "CNN-Web"
        set internet-service-id 2686977
    next
    edit "CNN-ICMP"
        set internet-service-id 2686978
    next
    edit "CNN-DNS"
        set internet-service-id 2686979
    next
    edit "CNN-Outbound_Email"
        set internet-service-id 2686980
    next
    edit "CNN-SSH"
        set internet-service-id 2686982
    next
    edit "CNN-FTP"
        set internet-service-id 2686983
    next
    edit "CNN-NTP"
        set internet-service-id 2686984
    next
    edit "CNN-Inbound_Email"
        set internet-service-id 2686985
    next
    edit "CNN-LDAP"
        set internet-service-id 2686990
    next
    edit "CNN-NetBIOS.Session.Service"
        set internet-service-id 2686991
    next
    edit "CNN-RTMP"
        set internet-service-id 2686992
    next
    edit "CNN-NetBIOS.Name.Service"
        set internet-service-id 2687000
    next
    edit "Myspace-Other"
        set internet-service-id 2752512
    next
    edit "Myspace-Web"
        set internet-service-id 2752513
    next
    edit "Myspace-ICMP"
        set internet-service-id 2752514
    next
    edit "Myspace-DNS"
        set internet-service-id 2752515
    next
    edit "Myspace-Outbound_Email"
        set internet-service-id 2752516
    next
    edit "Myspace-SSH"
        set internet-service-id 2752518
    next
    edit "Myspace-FTP"
        set internet-service-id 2752519
    next
    edit "Myspace-NTP"
        set internet-service-id 2752520
    next
    edit "Myspace-Inbound_Email"
        set internet-service-id 2752521
    next
    edit "Myspace-LDAP"
        set internet-service-id 2752526
    next
    edit "Myspace-NetBIOS.Session.Service"
        set internet-service-id 2752527
    next
    edit "Myspace-RTMP"
        set internet-service-id 2752528
    next
    edit "Myspace-NetBIOS.Name.Service"
        set internet-service-id 2752536
    next
    edit "Tor-Relay.Node"
        set internet-service-id 2818238
    next
    edit "Tor-Exit.Node"
        set internet-service-id 2818243
    next
    edit "Baidu-Other"
        set internet-service-id 2883584
    next
    edit "Baidu-Web"
        set internet-service-id 2883585
    next
    edit "Baidu-ICMP"
        set internet-service-id 2883586
    next
    edit "Baidu-DNS"
        set internet-service-id 2883587
    next
    edit "Baidu-Outbound_Email"
        set internet-service-id 2883588
    next
    edit "Baidu-SSH"
        set internet-service-id 2883590
    next
    edit "Baidu-FTP"
        set internet-service-id 2883591
    next
    edit "Baidu-NTP"
        set internet-service-id 2883592
    next
    edit "Baidu-Inbound_Email"
        set internet-service-id 2883593
    next
    edit "Baidu-LDAP"
        set internet-service-id 2883598
    next
    edit "Baidu-NetBIOS.Session.Service"
        set internet-service-id 2883599
    next
    edit "Baidu-RTMP"
        set internet-service-id 2883600
    next
    edit "Baidu-NetBIOS.Name.Service"
        set internet-service-id 2883608
    next
    edit "ntp.org-Other"
        set internet-service-id 2949120
    next
    edit "ntp.org-Web"
        set internet-service-id 2949121
    next
    edit "ntp.org-ICMP"
        set internet-service-id 2949122
    next
    edit "ntp.org-DNS"
        set internet-service-id 2949123
    next
    edit "ntp.org-Outbound_Email"
        set internet-service-id 2949124
    next
    edit "ntp.org-SSH"
        set internet-service-id 2949126
    next
    edit "ntp.org-FTP"
        set internet-service-id 2949127
    next
    edit "ntp.org-NTP"
        set internet-service-id 2949128
    next
    edit "ntp.org-Inbound_Email"
        set internet-service-id 2949129
    next
    edit "ntp.org-LDAP"
        set internet-service-id 2949134
    next
    edit "ntp.org-NetBIOS.Session.Service"
        set internet-service-id 2949135
    next
    edit "ntp.org-RTMP"
        set internet-service-id 2949136
    next
    edit "ntp.org-NetBIOS.Name.Service"
        set internet-service-id 2949144
    next
    edit "Proxy-Proxy.Server"
        set internet-service-id 3014850
    next
    edit "Botnet-C&C.Server"
        set internet-service-id 3080383
    next
    edit "Spam-Spamming.Server"
        set internet-service-id 3145920
    next
    edit "Phishing-Phishing.Server"
        set internet-service-id 3211457
    next
    edit "Zendesk-Other"
        set internet-service-id 3407872
    next
    edit "Zendesk-Web"
        set internet-service-id 3407873
    next
    edit "Zendesk-ICMP"
        set internet-service-id 3407874
    next
    edit "Zendesk-DNS"
        set internet-service-id 3407875
    next
    edit "Zendesk-Outbound_Email"
        set internet-service-id 3407876
    next
    edit "Zendesk-SSH"
        set internet-service-id 3407878
    next
    edit "Zendesk-FTP"
        set internet-service-id 3407879
    next
    edit "Zendesk-NTP"
        set internet-service-id 3407880
    next
    edit "Zendesk-Inbound_Email"
        set internet-service-id 3407881
    next
    edit "Zendesk-LDAP"
        set internet-service-id 3407886
    next
    edit "Zendesk-NetBIOS.Session.Service"
        set internet-service-id 3407887
    next
    edit "Zendesk-RTMP"
        set internet-service-id 3407888
    next
    edit "Zendesk-NetBIOS.Name.Service"
        set internet-service-id 3407896
    next
    edit "Zendesk-Zendesk.Suite"
        set internet-service-id 3408047
    next
    edit "DocuSign-Other"
        set internet-service-id 3473408
    next
    edit "DocuSign-Web"
        set internet-service-id 3473409
    next
    edit "DocuSign-ICMP"
        set internet-service-id 3473410
    next
    edit "DocuSign-DNS"
        set internet-service-id 3473411
    next
    edit "DocuSign-Outbound_Email"
        set internet-service-id 3473412
    next
    edit "DocuSign-SSH"
        set internet-service-id 3473414
    next
    edit "DocuSign-FTP"
        set internet-service-id 3473415
    next
    edit "DocuSign-NTP"
        set internet-service-id 3473416
    next
    edit "DocuSign-Inbound_Email"
        set internet-service-id 3473417
    next
    edit "DocuSign-LDAP"
        set internet-service-id 3473422
    next
    edit "DocuSign-NetBIOS.Session.Service"
        set internet-service-id 3473423
    next
    edit "DocuSign-RTMP"
        set internet-service-id 3473424
    next
    edit "DocuSign-NetBIOS.Name.Service"
        set internet-service-id 3473432
    next
    edit "ServiceNow-Other"
        set internet-service-id 3538944
    next
    edit "ServiceNow-Web"
        set internet-service-id 3538945
    next
    edit "ServiceNow-ICMP"
        set internet-service-id 3538946
    next
    edit "ServiceNow-DNS"
        set internet-service-id 3538947
    next
    edit "ServiceNow-Outbound_Email"
        set internet-service-id 3538948
    next
    edit "ServiceNow-SSH"
        set internet-service-id 3538950
    next
    edit "ServiceNow-FTP"
        set internet-service-id 3538951
    next
    edit "ServiceNow-NTP"
        set internet-service-id 3538952
    next
    edit "ServiceNow-Inbound_Email"
        set internet-service-id 3538953
    next
    edit "ServiceNow-LDAP"
        set internet-service-id 3538958
    next
    edit "ServiceNow-NetBIOS.Session.Service"
        set internet-service-id 3538959
    next
    edit "ServiceNow-RTMP"
        set internet-service-id 3538960
    next
    edit "ServiceNow-NetBIOS.Name.Service"
        set internet-service-id 3538968
    next
    edit "GitHub-GitHub"
        set internet-service-id 3604638
    next
    edit "Workday-Other"
        set internet-service-id 3670016
    next
    edit "Workday-Web"
        set internet-service-id 3670017
    next
    edit "Workday-ICMP"
        set internet-service-id 3670018
    next
    edit "Workday-DNS"
        set internet-service-id 3670019
    next
    edit "Workday-Outbound_Email"
        set internet-service-id 3670020
    next
    edit "Workday-SSH"
        set internet-service-id 3670022
    next
    edit "Workday-FTP"
        set internet-service-id 3670023
    next
    edit "Workday-NTP"
        set internet-service-id 3670024
    next
    edit "Workday-Inbound_Email"
        set internet-service-id 3670025
    next
    edit "Workday-LDAP"
        set internet-service-id 3670030
    next
    edit "Workday-NetBIOS.Session.Service"
        set internet-service-id 3670031
    next
    edit "Workday-RTMP"
        set internet-service-id 3670032
    next
    edit "Workday-NetBIOS.Name.Service"
        set internet-service-id 3670040
    next
    edit "HubSpot-Other"
        set internet-service-id 3735552
    next
    edit "HubSpot-Web"
        set internet-service-id 3735553
    next
    edit "HubSpot-ICMP"
        set internet-service-id 3735554
    next
    edit "HubSpot-DNS"
        set internet-service-id 3735555
    next
    edit "HubSpot-Outbound_Email"
        set internet-service-id 3735556
    next
    edit "HubSpot-SSH"
        set internet-service-id 3735558
    next
    edit "HubSpot-FTP"
        set internet-service-id 3735559
    next
    edit "HubSpot-NTP"
        set internet-service-id 3735560
    next
    edit "HubSpot-Inbound_Email"
        set internet-service-id 3735561
    next
    edit "HubSpot-LDAP"
        set internet-service-id 3735566
    next
    edit "HubSpot-NetBIOS.Session.Service"
        set internet-service-id 3735567
    next
    edit "HubSpot-RTMP"
        set internet-service-id 3735568
    next
    edit "HubSpot-NetBIOS.Name.Service"
        set internet-service-id 3735576
    next
    edit "Twilio-Other"
        set internet-service-id 3801088
    next
    edit "Twilio-Web"
        set internet-service-id 3801089
    next
    edit "Twilio-ICMP"
        set internet-service-id 3801090
    next
    edit "Twilio-DNS"
        set internet-service-id 3801091
    next
    edit "Twilio-Outbound_Email"
        set internet-service-id 3801092
    next
    edit "Twilio-SSH"
        set internet-service-id 3801094
    next
    edit "Twilio-FTP"
        set internet-service-id 3801095
    next
    edit "Twilio-NTP"
        set internet-service-id 3801096
    next
    edit "Twilio-Inbound_Email"
        set internet-service-id 3801097
    next
    edit "Twilio-LDAP"
        set internet-service-id 3801102
    next
    edit "Twilio-NetBIOS.Session.Service"
        set internet-service-id 3801103
    next
    edit "Twilio-RTMP"
        set internet-service-id 3801104
    next
    edit "Twilio-NetBIOS.Name.Service"
        set internet-service-id 3801112
    next
    edit "Twilio-Elastic.SIP.Trunking"
        set internet-service-id 3801277
    next
    edit "Coupa-Other"
        set internet-service-id 3866624
    next
    edit "Coupa-Web"
        set internet-service-id 3866625
    next
    edit "Coupa-ICMP"
        set internet-service-id 3866626
    next
    edit "Coupa-DNS"
        set internet-service-id 3866627
    next
    edit "Coupa-Outbound_Email"
        set internet-service-id 3866628
    next
    edit "Coupa-SSH"
        set internet-service-id 3866630
    next
    edit "Coupa-FTP"
        set internet-service-id 3866631
    next
    edit "Coupa-NTP"
        set internet-service-id 3866632
    next
    edit "Coupa-Inbound_Email"
        set internet-service-id 3866633
    next
    edit "Coupa-LDAP"
        set internet-service-id 3866638
    next
    edit "Coupa-NetBIOS.Session.Service"
        set internet-service-id 3866639
    next
    edit "Coupa-RTMP"
        set internet-service-id 3866640
    next
    edit "Coupa-NetBIOS.Name.Service"
        set internet-service-id 3866648
    next
    edit "Atlassian-Other"
        set internet-service-id 3932160
    next
    edit "Atlassian-Web"
        set internet-service-id 3932161
    next
    edit "Atlassian-ICMP"
        set internet-service-id 3932162
    next
    edit "Atlassian-DNS"
        set internet-service-id 3932163
    next
    edit "Atlassian-Outbound_Email"
        set internet-service-id 3932164
    next
    edit "Atlassian-SSH"
        set internet-service-id 3932166
    next
    edit "Atlassian-FTP"
        set internet-service-id 3932167
    next
    edit "Atlassian-NTP"
        set internet-service-id 3932168
    next
    edit "Atlassian-Inbound_Email"
        set internet-service-id 3932169
    next
    edit "Atlassian-LDAP"
        set internet-service-id 3932174
    next
    edit "Atlassian-NetBIOS.Session.Service"
        set internet-service-id 3932175
    next
    edit "Atlassian-RTMP"
        set internet-service-id 3932176
    next
    edit "Atlassian-NetBIOS.Name.Service"
        set internet-service-id 3932184
    next
    edit "Atlassian-Atlassian.Cloud"
        set internet-service-id 3932388
    next
    edit "Atlassian-Atlassian.Notification"
        set internet-service-id 3932436
    next
    edit "Xero-Other"
        set internet-service-id 3997696
    next
    edit "Xero-Web"
        set internet-service-id 3997697
    next
    edit "Xero-ICMP"
        set internet-service-id 3997698
    next
    edit "Xero-DNS"
        set internet-service-id 3997699
    next
    edit "Xero-Outbound_Email"
        set internet-service-id 3997700
    next
    edit "Xero-SSH"
        set internet-service-id 3997702
    next
    edit "Xero-FTP"
        set internet-service-id 3997703
    next
    edit "Xero-NTP"
        set internet-service-id 3997704
    next
    edit "Xero-Inbound_Email"
        set internet-service-id 3997705
    next
    edit "Xero-LDAP"
        set internet-service-id 3997710
    next
    edit "Xero-NetBIOS.Session.Service"
        set internet-service-id 3997711
    next
    edit "Xero-RTMP"
        set internet-service-id 3997712
    next
    edit "Xero-NetBIOS.Name.Service"
        set internet-service-id 3997720
    next
    edit "Zuora-Other"
        set internet-service-id 4063232
    next
    edit "Zuora-Web"
        set internet-service-id 4063233
    next
    edit "Zuora-ICMP"
        set internet-service-id 4063234
    next
    edit "Zuora-DNS"
        set internet-service-id 4063235
    next
    edit "Zuora-Outbound_Email"
        set internet-service-id 4063236
    next
    edit "Zuora-SSH"
        set internet-service-id 4063238
    next
    edit "Zuora-FTP"
        set internet-service-id 4063239
    next
    edit "Zuora-NTP"
        set internet-service-id 4063240
    next
    edit "Zuora-Inbound_Email"
        set internet-service-id 4063241
    next
    edit "Zuora-LDAP"
        set internet-service-id 4063246
    next
    edit "Zuora-NetBIOS.Session.Service"
        set internet-service-id 4063247
    next
    edit "Zuora-RTMP"
        set internet-service-id 4063248
    next
    edit "Zuora-NetBIOS.Name.Service"
        set internet-service-id 4063256
    next
    edit "AdRoll-Other"
        set internet-service-id 4128768
    next
    edit "AdRoll-Web"
        set internet-service-id 4128769
    next
    edit "AdRoll-ICMP"
        set internet-service-id 4128770
    next
    edit "AdRoll-DNS"
        set internet-service-id 4128771
    next
    edit "AdRoll-Outbound_Email"
        set internet-service-id 4128772
    next
    edit "AdRoll-SSH"
        set internet-service-id 4128774
    next
    edit "AdRoll-FTP"
        set internet-service-id 4128775
    next
    edit "AdRoll-NTP"
        set internet-service-id 4128776
    next
    edit "AdRoll-Inbound_Email"
        set internet-service-id 4128777
    next
    edit "AdRoll-LDAP"
        set internet-service-id 4128782
    next
    edit "AdRoll-NetBIOS.Session.Service"
        set internet-service-id 4128783
    next
    edit "AdRoll-RTMP"
        set internet-service-id 4128784
    next
    edit "AdRoll-NetBIOS.Name.Service"
        set internet-service-id 4128792
    next
    edit "Xactly-Other"
        set internet-service-id 4194304
    next
    edit "Xactly-Web"
        set internet-service-id 4194305
    next
    edit "Xactly-ICMP"
        set internet-service-id 4194306
    next
    edit "Xactly-DNS"
        set internet-service-id 4194307
    next
    edit "Xactly-Outbound_Email"
        set internet-service-id 4194308
    next
    edit "Xactly-SSH"
        set internet-service-id 4194310
    next
    edit "Xactly-FTP"
        set internet-service-id 4194311
    next
    edit "Xactly-NTP"
        set internet-service-id 4194312
    next
    edit "Xactly-Inbound_Email"
        set internet-service-id 4194313
    next
    edit "Xactly-LDAP"
        set internet-service-id 4194318
    next
    edit "Xactly-NetBIOS.Session.Service"
        set internet-service-id 4194319
    next
    edit "Xactly-RTMP"
        set internet-service-id 4194320
    next
    edit "Xactly-NetBIOS.Name.Service"
        set internet-service-id 4194328
    next
    edit "Intuit-Other"
        set internet-service-id 4259840
    next
    edit "Intuit-Web"
        set internet-service-id 4259841
    next
    edit "Intuit-ICMP"
        set internet-service-id 4259842
    next
    edit "Intuit-DNS"
        set internet-service-id 4259843
    next
    edit "Intuit-Outbound_Email"
        set internet-service-id 4259844
    next
    edit "Intuit-SSH"
        set internet-service-id 4259846
    next
    edit "Intuit-FTP"
        set internet-service-id 4259847
    next
    edit "Intuit-NTP"
        set internet-service-id 4259848
    next
    edit "Intuit-Inbound_Email"
        set internet-service-id 4259849
    next
    edit "Intuit-LDAP"
        set internet-service-id 4259854
    next
    edit "Intuit-NetBIOS.Session.Service"
        set internet-service-id 4259855
    next
    edit "Intuit-RTMP"
        set internet-service-id 4259856
    next
    edit "Intuit-NetBIOS.Name.Service"
        set internet-service-id 4259864
    next
    edit "Marketo-Other"
        set internet-service-id 4325376
    next
    edit "Marketo-Web"
        set internet-service-id 4325377
    next
    edit "Marketo-ICMP"
        set internet-service-id 4325378
    next
    edit "Marketo-DNS"
        set internet-service-id 4325379
    next
    edit "Marketo-Outbound_Email"
        set internet-service-id 4325380
    next
    edit "Marketo-SSH"
        set internet-service-id 4325382
    next
    edit "Marketo-FTP"
        set internet-service-id 4325383
    next
    edit "Marketo-NTP"
        set internet-service-id 4325384
    next
    edit "Marketo-Inbound_Email"
        set internet-service-id 4325385
    next
    edit "Marketo-LDAP"
        set internet-service-id 4325390
    next
    edit "Marketo-NetBIOS.Session.Service"
        set internet-service-id 4325391
    next
    edit "Marketo-RTMP"
        set internet-service-id 4325392
    next
    edit "Marketo-NetBIOS.Name.Service"
        set internet-service-id 4325400
    next
    edit "Bill-Other"
        set internet-service-id 4456448
    next
    edit "Bill-Web"
        set internet-service-id 4456449
    next
    edit "Bill-ICMP"
        set internet-service-id 4456450
    next
    edit "Bill-DNS"
        set internet-service-id 4456451
    next
    edit "Bill-Outbound_Email"
        set internet-service-id 4456452
    next
    edit "Bill-SSH"
        set internet-service-id 4456454
    next
    edit "Bill-FTP"
        set internet-service-id 4456455
    next
    edit "Bill-NTP"
        set internet-service-id 4456456
    next
    edit "Bill-Inbound_Email"
        set internet-service-id 4456457
    next
    edit "Bill-LDAP"
        set internet-service-id 4456462
    next
    edit "Bill-NetBIOS.Session.Service"
        set internet-service-id 4456463
    next
    edit "Bill-RTMP"
        set internet-service-id 4456464
    next
    edit "Bill-NetBIOS.Name.Service"
        set internet-service-id 4456472
    next
    edit "Shopify-Other"
        set internet-service-id 4521984
    next
    edit "Shopify-Web"
        set internet-service-id 4521985
    next
    edit "Shopify-ICMP"
        set internet-service-id 4521986
    next
    edit "Shopify-DNS"
        set internet-service-id 4521987
    next
    edit "Shopify-Outbound_Email"
        set internet-service-id 4521988
    next
    edit "Shopify-SSH"
        set internet-service-id 4521990
    next
    edit "Shopify-FTP"
        set internet-service-id 4521991
    next
    edit "Shopify-NTP"
        set internet-service-id 4521992
    next
    edit "Shopify-Inbound_Email"
        set internet-service-id 4521993
    next
    edit "Shopify-LDAP"
        set internet-service-id 4521998
    next
    edit "Shopify-NetBIOS.Session.Service"
        set internet-service-id 4521999
    next
    edit "Shopify-RTMP"
        set internet-service-id 4522000
    next
    edit "Shopify-NetBIOS.Name.Service"
        set internet-service-id 4522008
    next
    edit "Shopify-Shopify"
        set internet-service-id 4522162
    next
    edit "MuleSoft-Other"
        set internet-service-id 4587520
    next
    edit "MuleSoft-Web"
        set internet-service-id 4587521
    next
    edit "MuleSoft-ICMP"
        set internet-service-id 4587522
    next
    edit "MuleSoft-DNS"
        set internet-service-id 4587523
    next
    edit "MuleSoft-Outbound_Email"
        set internet-service-id 4587524
    next
    edit "MuleSoft-SSH"
        set internet-service-id 4587526
    next
    edit "MuleSoft-FTP"
        set internet-service-id 4587527
    next
    edit "MuleSoft-NTP"
        set internet-service-id 4587528
    next
    edit "MuleSoft-Inbound_Email"
        set internet-service-id 4587529
    next
    edit "MuleSoft-LDAP"
        set internet-service-id 4587534
    next
    edit "MuleSoft-NetBIOS.Session.Service"
        set internet-service-id 4587535
    next
    edit "MuleSoft-RTMP"
        set internet-service-id 4587536
    next
    edit "MuleSoft-NetBIOS.Name.Service"
        set internet-service-id 4587544
    next
    edit "Cornerstone-Other"
        set internet-service-id 4653056
    next
    edit "Cornerstone-Web"
        set internet-service-id 4653057
    next
    edit "Cornerstone-ICMP"
        set internet-service-id 4653058
    next
    edit "Cornerstone-DNS"
        set internet-service-id 4653059
    next
    edit "Cornerstone-Outbound_Email"
        set internet-service-id 4653060
    next
    edit "Cornerstone-SSH"
        set internet-service-id 4653062
    next
    edit "Cornerstone-FTP"
        set internet-service-id 4653063
    next
    edit "Cornerstone-NTP"
        set internet-service-id 4653064
    next
    edit "Cornerstone-Inbound_Email"
        set internet-service-id 4653065
    next
    edit "Cornerstone-LDAP"
        set internet-service-id 4653070
    next
    edit "Cornerstone-NetBIOS.Session.Service"
        set internet-service-id 4653071
    next
    edit "Cornerstone-RTMP"
        set internet-service-id 4653072
    next
    edit "Cornerstone-NetBIOS.Name.Service"
        set internet-service-id 4653080
    next
    edit "Eventbrite-Other"
        set internet-service-id 4718592
    next
    edit "Eventbrite-Web"
        set internet-service-id 4718593
    next
    edit "Eventbrite-ICMP"
        set internet-service-id 4718594
    next
    edit "Eventbrite-DNS"
        set internet-service-id 4718595
    next
    edit "Eventbrite-Outbound_Email"
        set internet-service-id 4718596
    next
    edit "Eventbrite-SSH"
        set internet-service-id 4718598
    next
    edit "Eventbrite-FTP"
        set internet-service-id 4718599
    next
    edit "Eventbrite-NTP"
        set internet-service-id 4718600
    next
    edit "Eventbrite-Inbound_Email"
        set internet-service-id 4718601
    next
    edit "Eventbrite-LDAP"
        set internet-service-id 4718606
    next
    edit "Eventbrite-NetBIOS.Session.Service"
        set internet-service-id 4718607
    next
    edit "Eventbrite-RTMP"
        set internet-service-id 4718608
    next
    edit "Eventbrite-NetBIOS.Name.Service"
        set internet-service-id 4718616
    next
    edit "Paychex-Other"
        set internet-service-id 4784128
    next
    edit "Paychex-Web"
        set internet-service-id 4784129
    next
    edit "Paychex-ICMP"
        set internet-service-id 4784130
    next
    edit "Paychex-DNS"
        set internet-service-id 4784131
    next
    edit "Paychex-Outbound_Email"
        set internet-service-id 4784132
    next
    edit "Paychex-SSH"
        set internet-service-id 4784134
    next
    edit "Paychex-FTP"
        set internet-service-id 4784135
    next
    edit "Paychex-NTP"
        set internet-service-id 4784136
    next
    edit "Paychex-Inbound_Email"
        set internet-service-id 4784137
    next
    edit "Paychex-LDAP"
        set internet-service-id 4784142
    next
    edit "Paychex-NetBIOS.Session.Service"
        set internet-service-id 4784143
    next
    edit "Paychex-RTMP"
        set internet-service-id 4784144
    next
    edit "Paychex-NetBIOS.Name.Service"
        set internet-service-id 4784152
    next
    edit "NewRelic-Other"
        set internet-service-id 4849664
    next
    edit "NewRelic-Web"
        set internet-service-id 4849665
    next
    edit "NewRelic-ICMP"
        set internet-service-id 4849666
    next
    edit "NewRelic-DNS"
        set internet-service-id 4849667
    next
    edit "NewRelic-Outbound_Email"
        set internet-service-id 4849668
    next
    edit "NewRelic-SSH"
        set internet-service-id 4849670
    next
    edit "NewRelic-FTP"
        set internet-service-id 4849671
    next
    edit "NewRelic-NTP"
        set internet-service-id 4849672
    next
    edit "NewRelic-Inbound_Email"
        set internet-service-id 4849673
    next
    edit "NewRelic-LDAP"
        set internet-service-id 4849678
    next
    edit "NewRelic-NetBIOS.Session.Service"
        set internet-service-id 4849679
    next
    edit "NewRelic-RTMP"
        set internet-service-id 4849680
    next
    edit "NewRelic-NetBIOS.Name.Service"
        set internet-service-id 4849688
    next
    edit "Splunk-Other"
        set internet-service-id 4915200
    next
    edit "Splunk-Web"
        set internet-service-id 4915201
    next
    edit "Splunk-ICMP"
        set internet-service-id 4915202
    next
    edit "Splunk-DNS"
        set internet-service-id 4915203
    next
    edit "Splunk-Outbound_Email"
        set internet-service-id 4915204
    next
    edit "Splunk-SSH"
        set internet-service-id 4915206
    next
    edit "Splunk-FTP"
        set internet-service-id 4915207
    next
    edit "Splunk-NTP"
        set internet-service-id 4915208
    next
    edit "Splunk-Inbound_Email"
        set internet-service-id 4915209
    next
    edit "Splunk-LDAP"
        set internet-service-id 4915214
    next
    edit "Splunk-NetBIOS.Session.Service"
        set internet-service-id 4915215
    next
    edit "Splunk-RTMP"
        set internet-service-id 4915216
    next
    edit "Splunk-NetBIOS.Name.Service"
        set internet-service-id 4915224
    next
    edit "Domo-Other"
        set internet-service-id 4980736
    next
    edit "Domo-Web"
        set internet-service-id 4980737
    next
    edit "Domo-ICMP"
        set internet-service-id 4980738
    next
    edit "Domo-DNS"
        set internet-service-id 4980739
    next
    edit "Domo-Outbound_Email"
        set internet-service-id 4980740
    next
    edit "Domo-SSH"
        set internet-service-id 4980742
    next
    edit "Domo-FTP"
        set internet-service-id 4980743
    next
    edit "Domo-NTP"
        set internet-service-id 4980744
    next
    edit "Domo-Inbound_Email"
        set internet-service-id 4980745
    next
    edit "Domo-LDAP"
        set internet-service-id 4980750
    next
    edit "Domo-NetBIOS.Session.Service"
        set internet-service-id 4980751
    next
    edit "Domo-RTMP"
        set internet-service-id 4980752
    next
    edit "Domo-NetBIOS.Name.Service"
        set internet-service-id 4980760
    next
    edit "FreshBooks-Other"
        set internet-service-id 5046272
    next
    edit "FreshBooks-Web"
        set internet-service-id 5046273
    next
    edit "FreshBooks-ICMP"
        set internet-service-id 5046274
    next
    edit "FreshBooks-DNS"
        set internet-service-id 5046275
    next
    edit "FreshBooks-Outbound_Email"
        set internet-service-id 5046276
    next
    edit "FreshBooks-SSH"
        set internet-service-id 5046278
    next
    edit "FreshBooks-FTP"
        set internet-service-id 5046279
    next
    edit "FreshBooks-NTP"
        set internet-service-id 5046280
    next
    edit "FreshBooks-Inbound_Email"
        set internet-service-id 5046281
    next
    edit "FreshBooks-LDAP"
        set internet-service-id 5046286
    next
    edit "FreshBooks-NetBIOS.Session.Service"
        set internet-service-id 5046287
    next
    edit "FreshBooks-RTMP"
        set internet-service-id 5046288
    next
    edit "FreshBooks-NetBIOS.Name.Service"
        set internet-service-id 5046296
    next
    edit "Tableau-Other"
        set internet-service-id 5111808
    next
    edit "Tableau-Web"
        set internet-service-id 5111809
    next
    edit "Tableau-ICMP"
        set internet-service-id 5111810
    next
    edit "Tableau-DNS"
        set internet-service-id 5111811
    next
    edit "Tableau-Outbound_Email"
        set internet-service-id 5111812
    next
    edit "Tableau-SSH"
        set internet-service-id 5111814
    next
    edit "Tableau-FTP"
        set internet-service-id 5111815
    next
    edit "Tableau-NTP"
        set internet-service-id 5111816
    next
    edit "Tableau-Inbound_Email"
        set internet-service-id 5111817
    next
    edit "Tableau-LDAP"
        set internet-service-id 5111822
    next
    edit "Tableau-NetBIOS.Session.Service"
        set internet-service-id 5111823
    next
    edit "Tableau-RTMP"
        set internet-service-id 5111824
    next
    edit "Tableau-NetBIOS.Name.Service"
        set internet-service-id 5111832
    next
    edit "Druva-Other"
        set internet-service-id 5177344
    next
    edit "Druva-Web"
        set internet-service-id 5177345
    next
    edit "Druva-ICMP"
        set internet-service-id 5177346
    next
    edit "Druva-DNS"
        set internet-service-id 5177347
    next
    edit "Druva-Outbound_Email"
        set internet-service-id 5177348
    next
    edit "Druva-SSH"
        set internet-service-id 5177350
    next
    edit "Druva-FTP"
        set internet-service-id 5177351
    next
    edit "Druva-NTP"
        set internet-service-id 5177352
    next
    edit "Druva-Inbound_Email"
        set internet-service-id 5177353
    next
    edit "Druva-LDAP"
        set internet-service-id 5177358
    next
    edit "Druva-NetBIOS.Session.Service"
        set internet-service-id 5177359
    next
    edit "Druva-RTMP"
        set internet-service-id 5177360
    next
    edit "Druva-NetBIOS.Name.Service"
        set internet-service-id 5177368
    next
    edit "Act-on-Other"
        set internet-service-id 5242880
    next
    edit "Act-on-Web"
        set internet-service-id 5242881
    next
    edit "Act-on-ICMP"
        set internet-service-id 5242882
    next
    edit "Act-on-DNS"
        set internet-service-id 5242883
    next
    edit "Act-on-Outbound_Email"
        set internet-service-id 5242884
    next
    edit "Act-on-SSH"
        set internet-service-id 5242886
    next
    edit "Act-on-FTP"
        set internet-service-id 5242887
    next
    edit "Act-on-NTP"
        set internet-service-id 5242888
    next
    edit "Act-on-Inbound_Email"
        set internet-service-id 5242889
    next
    edit "Act-on-LDAP"
        set internet-service-id 5242894
    next
    edit "Act-on-NetBIOS.Session.Service"
        set internet-service-id 5242895
    next
    edit "Act-on-RTMP"
        set internet-service-id 5242896
    next
    edit "Act-on-NetBIOS.Name.Service"
        set internet-service-id 5242904
    next
    edit "GoodData-Other"
        set internet-service-id 5308416
    next
    edit "GoodData-Web"
        set internet-service-id 5308417
    next
    edit "GoodData-ICMP"
        set internet-service-id 5308418
    next
    edit "GoodData-DNS"
        set internet-service-id 5308419
    next
    edit "GoodData-Outbound_Email"
        set internet-service-id 5308420
    next
    edit "GoodData-SSH"
        set internet-service-id 5308422
    next
    edit "GoodData-FTP"
        set internet-service-id 5308423
    next
    edit "GoodData-NTP"
        set internet-service-id 5308424
    next
    edit "GoodData-Inbound_Email"
        set internet-service-id 5308425
    next
    edit "GoodData-LDAP"
        set internet-service-id 5308430
    next
    edit "GoodData-NetBIOS.Session.Service"
        set internet-service-id 5308431
    next
    edit "GoodData-RTMP"
        set internet-service-id 5308432
    next
    edit "GoodData-NetBIOS.Name.Service"
        set internet-service-id 5308440
    next
    edit "SurveyMonkey-Other"
        set internet-service-id 5373952
    next
    edit "SurveyMonkey-Web"
        set internet-service-id 5373953
    next
    edit "SurveyMonkey-ICMP"
        set internet-service-id 5373954
    next
    edit "SurveyMonkey-DNS"
        set internet-service-id 5373955
    next
    edit "SurveyMonkey-Outbound_Email"
        set internet-service-id 5373956
    next
    edit "SurveyMonkey-SSH"
        set internet-service-id 5373958
    next
    edit "SurveyMonkey-FTP"
        set internet-service-id 5373959
    next
    edit "SurveyMonkey-NTP"
        set internet-service-id 5373960
    next
    edit "SurveyMonkey-Inbound_Email"
        set internet-service-id 5373961
    next
    edit "SurveyMonkey-LDAP"
        set internet-service-id 5373966
    next
    edit "SurveyMonkey-NetBIOS.Session.Service"
        set internet-service-id 5373967
    next
    edit "SurveyMonkey-RTMP"
        set internet-service-id 5373968
    next
    edit "SurveyMonkey-NetBIOS.Name.Service"
        set internet-service-id 5373976
    next
    edit "Cvent-Other"
        set internet-service-id 5439488
    next
    edit "Cvent-Web"
        set internet-service-id 5439489
    next
    edit "Cvent-ICMP"
        set internet-service-id 5439490
    next
    edit "Cvent-DNS"
        set internet-service-id 5439491
    next
    edit "Cvent-Outbound_Email"
        set internet-service-id 5439492
    next
    edit "Cvent-SSH"
        set internet-service-id 5439494
    next
    edit "Cvent-FTP"
        set internet-service-id 5439495
    next
    edit "Cvent-NTP"
        set internet-service-id 5439496
    next
    edit "Cvent-Inbound_Email"
        set internet-service-id 5439497
    next
    edit "Cvent-LDAP"
        set internet-service-id 5439502
    next
    edit "Cvent-NetBIOS.Session.Service"
        set internet-service-id 5439503
    next
    edit "Cvent-RTMP"
        set internet-service-id 5439504
    next
    edit "Cvent-NetBIOS.Name.Service"
        set internet-service-id 5439512
    next
    edit "Blackbaud-Other"
        set internet-service-id 5505024
    next
    edit "Blackbaud-Web"
        set internet-service-id 5505025
    next
    edit "Blackbaud-ICMP"
        set internet-service-id 5505026
    next
    edit "Blackbaud-DNS"
        set internet-service-id 5505027
    next
    edit "Blackbaud-Outbound_Email"
        set internet-service-id 5505028
    next
    edit "Blackbaud-SSH"
        set internet-service-id 5505030
    next
    edit "Blackbaud-FTP"
        set internet-service-id 5505031
    next
    edit "Blackbaud-NTP"
        set internet-service-id 5505032
    next
    edit "Blackbaud-Inbound_Email"
        set internet-service-id 5505033
    next
    edit "Blackbaud-LDAP"
        set internet-service-id 5505038
    next
    edit "Blackbaud-NetBIOS.Session.Service"
        set internet-service-id 5505039
    next
    edit "Blackbaud-RTMP"
        set internet-service-id 5505040
    next
    edit "Blackbaud-NetBIOS.Name.Service"
        set internet-service-id 5505048
    next
    edit "InsideSales-Other"
        set internet-service-id 5570560
    next
    edit "InsideSales-Web"
        set internet-service-id 5570561
    next
    edit "InsideSales-ICMP"
        set internet-service-id 5570562
    next
    edit "InsideSales-DNS"
        set internet-service-id 5570563
    next
    edit "InsideSales-Outbound_Email"
        set internet-service-id 5570564
    next
    edit "InsideSales-SSH"
        set internet-service-id 5570566
    next
    edit "InsideSales-FTP"
        set internet-service-id 5570567
    next
    edit "InsideSales-NTP"
        set internet-service-id 5570568
    next
    edit "InsideSales-Inbound_Email"
        set internet-service-id 5570569
    next
    edit "InsideSales-LDAP"
        set internet-service-id 5570574
    next
    edit "InsideSales-NetBIOS.Session.Service"
        set internet-service-id 5570575
    next
    edit "InsideSales-RTMP"
        set internet-service-id 5570576
    next
    edit "InsideSales-NetBIOS.Name.Service"
        set internet-service-id 5570584
    next
    edit "ServiceMax-Other"
        set internet-service-id 5636096
    next
    edit "ServiceMax-Web"
        set internet-service-id 5636097
    next
    edit "ServiceMax-ICMP"
        set internet-service-id 5636098
    next
    edit "ServiceMax-DNS"
        set internet-service-id 5636099
    next
    edit "ServiceMax-Outbound_Email"
        set internet-service-id 5636100
    next
    edit "ServiceMax-SSH"
        set internet-service-id 5636102
    next
    edit "ServiceMax-FTP"
        set internet-service-id 5636103
    next
    edit "ServiceMax-NTP"
        set internet-service-id 5636104
    next
    edit "ServiceMax-Inbound_Email"
        set internet-service-id 5636105
    next
    edit "ServiceMax-LDAP"
        set internet-service-id 5636110
    next
    edit "ServiceMax-NetBIOS.Session.Service"
        set internet-service-id 5636111
    next
    edit "ServiceMax-RTMP"
        set internet-service-id 5636112
    next
    edit "ServiceMax-NetBIOS.Name.Service"
        set internet-service-id 5636120
    next
    edit "Apptio-Other"
        set internet-service-id 5701632
    next
    edit "Apptio-Web"
        set internet-service-id 5701633
    next
    edit "Apptio-ICMP"
        set internet-service-id 5701634
    next
    edit "Apptio-DNS"
        set internet-service-id 5701635
    next
    edit "Apptio-Outbound_Email"
        set internet-service-id 5701636
    next
    edit "Apptio-SSH"
        set internet-service-id 5701638
    next
    edit "Apptio-FTP"
        set internet-service-id 5701639
    next
    edit "Apptio-NTP"
        set internet-service-id 5701640
    next
    edit "Apptio-Inbound_Email"
        set internet-service-id 5701641
    next
    edit "Apptio-LDAP"
        set internet-service-id 5701646
    next
    edit "Apptio-NetBIOS.Session.Service"
        set internet-service-id 5701647
    next
    edit "Apptio-RTMP"
        set internet-service-id 5701648
    next
    edit "Apptio-NetBIOS.Name.Service"
        set internet-service-id 5701656
    next
    edit "Veracode-Other"
        set internet-service-id 5767168
    next
    edit "Veracode-Web"
        set internet-service-id 5767169
    next
    edit "Veracode-ICMP"
        set internet-service-id 5767170
    next
    edit "Veracode-DNS"
        set internet-service-id 5767171
    next
    edit "Veracode-Outbound_Email"
        set internet-service-id 5767172
    next
    edit "Veracode-SSH"
        set internet-service-id 5767174
    next
    edit "Veracode-FTP"
        set internet-service-id 5767175
    next
    edit "Veracode-NTP"
        set internet-service-id 5767176
    next
    edit "Veracode-Inbound_Email"
        set internet-service-id 5767177
    next
    edit "Veracode-LDAP"
        set internet-service-id 5767182
    next
    edit "Veracode-NetBIOS.Session.Service"
        set internet-service-id 5767183
    next
    edit "Veracode-RTMP"
        set internet-service-id 5767184
    next
    edit "Veracode-NetBIOS.Name.Service"
        set internet-service-id 5767192
    next
    edit "Anaplan-Other"
        set internet-service-id 5832704
    next
    edit "Anaplan-Web"
        set internet-service-id 5832705
    next
    edit "Anaplan-ICMP"
        set internet-service-id 5832706
    next
    edit "Anaplan-DNS"
        set internet-service-id 5832707
    next
    edit "Anaplan-Outbound_Email"
        set internet-service-id 5832708
    next
    edit "Anaplan-SSH"
        set internet-service-id 5832710
    next
    edit "Anaplan-FTP"
        set internet-service-id 5832711
    next
    edit "Anaplan-NTP"
        set internet-service-id 5832712
    next
    edit "Anaplan-Inbound_Email"
        set internet-service-id 5832713
    next
    edit "Anaplan-LDAP"
        set internet-service-id 5832718
    next
    edit "Anaplan-NetBIOS.Session.Service"
        set internet-service-id 5832719
    next
    edit "Anaplan-RTMP"
        set internet-service-id 5832720
    next
    edit "Anaplan-NetBIOS.Name.Service"
        set internet-service-id 5832728
    next
    edit "Rapid7-Other"
        set internet-service-id 5898240
    next
    edit "Rapid7-Web"
        set internet-service-id 5898241
    next
    edit "Rapid7-ICMP"
        set internet-service-id 5898242
    next
    edit "Rapid7-DNS"
        set internet-service-id 5898243
    next
    edit "Rapid7-Outbound_Email"
        set internet-service-id 5898244
    next
    edit "Rapid7-SSH"
        set internet-service-id 5898246
    next
    edit "Rapid7-FTP"
        set internet-service-id 5898247
    next
    edit "Rapid7-NTP"
        set internet-service-id 5898248
    next
    edit "Rapid7-Inbound_Email"
        set internet-service-id 5898249
    next
    edit "Rapid7-LDAP"
        set internet-service-id 5898254
    next
    edit "Rapid7-NetBIOS.Session.Service"
        set internet-service-id 5898255
    next
    edit "Rapid7-RTMP"
        set internet-service-id 5898256
    next
    edit "Rapid7-NetBIOS.Name.Service"
        set internet-service-id 5898264
    next
    edit "AnyDesk-AnyDesk"
        set internet-service-id 5963927
    next
    edit "ESET-Eset.Service"
        set internet-service-id 6029426
    next
    edit "Slack-Other"
        set internet-service-id 6094848
    next
    edit "Slack-Web"
        set internet-service-id 6094849
    next
    edit "Slack-ICMP"
        set internet-service-id 6094850
    next
    edit "Slack-DNS"
        set internet-service-id 6094851
    next
    edit "Slack-Outbound_Email"
        set internet-service-id 6094852
    next
    edit "Slack-SSH"
        set internet-service-id 6094854
    next
    edit "Slack-FTP"
        set internet-service-id 6094855
    next
    edit "Slack-NTP"
        set internet-service-id 6094856
    next
    edit "Slack-Inbound_Email"
        set internet-service-id 6094857
    next
    edit "Slack-LDAP"
        set internet-service-id 6094862
    next
    edit "Slack-NetBIOS.Session.Service"
        set internet-service-id 6094863
    next
    edit "Slack-RTMP"
        set internet-service-id 6094864
    next
    edit "Slack-NetBIOS.Name.Service"
        set internet-service-id 6094872
    next
    edit "Slack-Slack"
        set internet-service-id 6095024
    next
    edit "ADP-Other"
        set internet-service-id 6160384
    next
    edit "ADP-Web"
        set internet-service-id 6160385
    next
    edit "ADP-ICMP"
        set internet-service-id 6160386
    next
    edit "ADP-DNS"
        set internet-service-id 6160387
    next
    edit "ADP-Outbound_Email"
        set internet-service-id 6160388
    next
    edit "ADP-SSH"
        set internet-service-id 6160390
    next
    edit "ADP-FTP"
        set internet-service-id 6160391
    next
    edit "ADP-NTP"
        set internet-service-id 6160392
    next
    edit "ADP-Inbound_Email"
        set internet-service-id 6160393
    next
    edit "ADP-LDAP"
        set internet-service-id 6160398
    next
    edit "ADP-NetBIOS.Session.Service"
        set internet-service-id 6160399
    next
    edit "ADP-RTMP"
        set internet-service-id 6160400
    next
    edit "ADP-NetBIOS.Name.Service"
        set internet-service-id 6160408
    next
    edit "Blackboard-Other"
        set internet-service-id 6225920
    next
    edit "Blackboard-Web"
        set internet-service-id 6225921
    next
    edit "Blackboard-ICMP"
        set internet-service-id 6225922
    next
    edit "Blackboard-DNS"
        set internet-service-id 6225923
    next
    edit "Blackboard-Outbound_Email"
        set internet-service-id 6225924
    next
    edit "Blackboard-SSH"
        set internet-service-id 6225926
    next
    edit "Blackboard-FTP"
        set internet-service-id 6225927
    next
    edit "Blackboard-NTP"
        set internet-service-id 6225928
    next
    edit "Blackboard-Inbound_Email"
        set internet-service-id 6225929
    next
    edit "Blackboard-LDAP"
        set internet-service-id 6225934
    next
    edit "Blackboard-NetBIOS.Session.Service"
        set internet-service-id 6225935
    next
    edit "Blackboard-RTMP"
        set internet-service-id 6225936
    next
    edit "Blackboard-NetBIOS.Name.Service"
        set internet-service-id 6225944
    next
    edit "SAP-Other"
        set internet-service-id 6291456
    next
    edit "SAP-Web"
        set internet-service-id 6291457
    next
    edit "SAP-ICMP"
        set internet-service-id 6291458
    next
    edit "SAP-DNS"
        set internet-service-id 6291459
    next
    edit "SAP-Outbound_Email"
        set internet-service-id 6291460
    next
    edit "SAP-SSH"
        set internet-service-id 6291462
    next
    edit "SAP-FTP"
        set internet-service-id 6291463
    next
    edit "SAP-NTP"
        set internet-service-id 6291464
    next
    edit "SAP-Inbound_Email"
        set internet-service-id 6291465
    next
    edit "SAP-LDAP"
        set internet-service-id 6291470
    next
    edit "SAP-NetBIOS.Session.Service"
        set internet-service-id 6291471
    next
    edit "SAP-RTMP"
        set internet-service-id 6291472
    next
    edit "SAP-NetBIOS.Name.Service"
        set internet-service-id 6291480
    next
    edit "SAP-HANA"
        set internet-service-id 6291612
    next
    edit "SAP-SuccessFactors"
        set internet-service-id 6291618
    next
    edit "Snap-Snapchat"
        set internet-service-id 6357108
    next
    edit "Zoom.us-Zoom.Meeting"
        set internet-service-id 6422646
    next
    edit "Sophos-Other"
        set internet-service-id 6488064
    next
    edit "Sophos-Web"
        set internet-service-id 6488065
    next
    edit "Sophos-ICMP"
        set internet-service-id 6488066
    next
    edit "Sophos-DNS"
        set internet-service-id 6488067
    next
    edit "Sophos-Outbound_Email"
        set internet-service-id 6488068
    next
    edit "Sophos-SSH"
        set internet-service-id 6488070
    next
    edit "Sophos-FTP"
        set internet-service-id 6488071
    next
    edit "Sophos-NTP"
        set internet-service-id 6488072
    next
    edit "Sophos-Inbound_Email"
        set internet-service-id 6488073
    next
    edit "Sophos-LDAP"
        set internet-service-id 6488078
    next
    edit "Sophos-NetBIOS.Session.Service"
        set internet-service-id 6488079
    next
    edit "Sophos-RTMP"
        set internet-service-id 6488080
    next
    edit "Sophos-NetBIOS.Name.Service"
        set internet-service-id 6488088
    next
    edit "Cloudflare-Other"
        set internet-service-id 6553600
    next
    edit "Cloudflare-Web"
        set internet-service-id 6553601
    next
    edit "Cloudflare-ICMP"
        set internet-service-id 6553602
    next
    edit "Cloudflare-DNS"
        set internet-service-id 6553603
    next
    edit "Cloudflare-Outbound_Email"
        set internet-service-id 6553604
    next
    edit "Cloudflare-SSH"
        set internet-service-id 6553606
    next
    edit "Cloudflare-FTP"
        set internet-service-id 6553607
    next
    edit "Cloudflare-NTP"
        set internet-service-id 6553608
    next
    edit "Cloudflare-Inbound_Email"
        set internet-service-id 6553609
    next
    edit "Cloudflare-LDAP"
        set internet-service-id 6553614
    next
    edit "Cloudflare-NetBIOS.Session.Service"
        set internet-service-id 6553615
    next
    edit "Cloudflare-RTMP"
        set internet-service-id 6553616
    next
    edit "Cloudflare-NetBIOS.Name.Service"
        set internet-service-id 6553624
    next
    edit "Cloudflare-CDN"
        set internet-service-id 6553737
    next
    edit "Pexip-Pexip.Meeting"
        set internet-service-id 6619256
    next
    edit "Zscaler-Other"
        set internet-service-id 6684672
    next
    edit "Zscaler-Web"
        set internet-service-id 6684673
    next
    edit "Zscaler-ICMP"
        set internet-service-id 6684674
    next
    edit "Zscaler-DNS"
        set internet-service-id 6684675
    next
    edit "Zscaler-Outbound_Email"
        set internet-service-id 6684676
    next
    edit "Zscaler-SSH"
        set internet-service-id 6684678
    next
    edit "Zscaler-FTP"
        set internet-service-id 6684679
    next
    edit "Zscaler-NTP"
        set internet-service-id 6684680
    next
    edit "Zscaler-Inbound_Email"
        set internet-service-id 6684681
    next
    edit "Zscaler-LDAP"
        set internet-service-id 6684686
    next
    edit "Zscaler-NetBIOS.Session.Service"
        set internet-service-id 6684687
    next
    edit "Zscaler-RTMP"
        set internet-service-id 6684688
    next
    edit "Zscaler-NetBIOS.Name.Service"
        set internet-service-id 6684696
    next
    edit "Zscaler-Zscaler.Cloud"
        set internet-service-id 6684793
    next
    edit "Yandex-Other"
        set internet-service-id 6750208
    next
    edit "Yandex-Web"
        set internet-service-id 6750209
    next
    edit "Yandex-ICMP"
        set internet-service-id 6750210
    next
    edit "Yandex-DNS"
        set internet-service-id 6750211
    next
    edit "Yandex-Outbound_Email"
        set internet-service-id 6750212
    next
    edit "Yandex-SSH"
        set internet-service-id 6750214
    next
    edit "Yandex-FTP"
        set internet-service-id 6750215
    next
    edit "Yandex-NTP"
        set internet-service-id 6750216
    next
    edit "Yandex-Inbound_Email"
        set internet-service-id 6750217
    next
    edit "Yandex-LDAP"
        set internet-service-id 6750222
    next
    edit "Yandex-NetBIOS.Session.Service"
        set internet-service-id 6750223
    next
    edit "Yandex-RTMP"
        set internet-service-id 6750224
    next
    edit "Yandex-NetBIOS.Name.Service"
        set internet-service-id 6750232
    next
    edit "mail.ru-Other"
        set internet-service-id 6815744
    next
    edit "mail.ru-Web"
        set internet-service-id 6815745
    next
    edit "mail.ru-ICMP"
        set internet-service-id 6815746
    next
    edit "mail.ru-DNS"
        set internet-service-id 6815747
    next
    edit "mail.ru-Outbound_Email"
        set internet-service-id 6815748
    next
    edit "mail.ru-SSH"
        set internet-service-id 6815750
    next
    edit "mail.ru-FTP"
        set internet-service-id 6815751
    next
    edit "mail.ru-NTP"
        set internet-service-id 6815752
    next
    edit "mail.ru-Inbound_Email"
        set internet-service-id 6815753
    next
    edit "mail.ru-LDAP"
        set internet-service-id 6815758
    next
    edit "mail.ru-NetBIOS.Session.Service"
        set internet-service-id 6815759
    next
    edit "mail.ru-RTMP"
        set internet-service-id 6815760
    next
    edit "mail.ru-NetBIOS.Name.Service"
        set internet-service-id 6815768
    next
    edit "Alibaba-Other"
        set internet-service-id 6881280
    next
    edit "Alibaba-Web"
        set internet-service-id 6881281
    next
    edit "Alibaba-ICMP"
        set internet-service-id 6881282
    next
    edit "Alibaba-DNS"
        set internet-service-id 6881283
    next
    edit "Alibaba-Outbound_Email"
        set internet-service-id 6881284
    next
    edit "Alibaba-SSH"
        set internet-service-id 6881286
    next
    edit "Alibaba-FTP"
        set internet-service-id 6881287
    next
    edit "Alibaba-NTP"
        set internet-service-id 6881288
    next
    edit "Alibaba-Inbound_Email"
        set internet-service-id 6881289
    next
    edit "Alibaba-LDAP"
        set internet-service-id 6881294
    next
    edit "Alibaba-NetBIOS.Session.Service"
        set internet-service-id 6881295
    next
    edit "Alibaba-RTMP"
        set internet-service-id 6881296
    next
    edit "Alibaba-NetBIOS.Name.Service"
        set internet-service-id 6881304
    next
    edit "Alibaba-Alibaba.Cloud"
        set internet-service-id 6881402
    next
    edit "GoDaddy-Other"
        set internet-service-id 6946816
    next
    edit "GoDaddy-Web"
        set internet-service-id 6946817
    next
    edit "GoDaddy-ICMP"
        set internet-service-id 6946818
    next
    edit "GoDaddy-DNS"
        set internet-service-id 6946819
    next
    edit "GoDaddy-Outbound_Email"
        set internet-service-id 6946820
    next
    edit "GoDaddy-SSH"
        set internet-service-id 6946822
    next
    edit "GoDaddy-FTP"
        set internet-service-id 6946823
    next
    edit "GoDaddy-NTP"
        set internet-service-id 6946824
    next
    edit "GoDaddy-Inbound_Email"
        set internet-service-id 6946825
    next
    edit "GoDaddy-LDAP"
        set internet-service-id 6946830
    next
    edit "GoDaddy-NetBIOS.Session.Service"
        set internet-service-id 6946831
    next
    edit "GoDaddy-RTMP"
        set internet-service-id 6946832
    next
    edit "GoDaddy-NetBIOS.Name.Service"
        set internet-service-id 6946840
    next
    edit "GoDaddy-GoDaddy.Email"
        set internet-service-id 6946939
    next
    edit "Bluejeans-Bluejeans.Meeting"
        set internet-service-id 7012476
    next
    edit "Webroot-Webroot.SecureAnywhere"
        set internet-service-id 7078013
    next
    edit "Avast-Other"
        set internet-service-id 7143424
    next
    edit "Avast-Web"
        set internet-service-id 7143425
    next
    edit "Avast-ICMP"
        set internet-service-id 7143426
    next
    edit "Avast-DNS"
        set internet-service-id 7143427
    next
    edit "Avast-Outbound_Email"
        set internet-service-id 7143428
    next
    edit "Avast-SSH"
        set internet-service-id 7143430
    next
    edit "Avast-FTP"
        set internet-service-id 7143431
    next
    edit "Avast-NTP"
        set internet-service-id 7143432
    next
    edit "Avast-Inbound_Email"
        set internet-service-id 7143433
    next
    edit "Avast-LDAP"
        set internet-service-id 7143438
    next
    edit "Avast-NetBIOS.Session.Service"
        set internet-service-id 7143439
    next
    edit "Avast-RTMP"
        set internet-service-id 7143440
    next
    edit "Avast-NetBIOS.Name.Service"
        set internet-service-id 7143448
    next
    edit "Avast-Avast.Security"
        set internet-service-id 7143550
    next
    edit "Wetransfer-Other"
        set internet-service-id 7208960
    next
    edit "Wetransfer-Web"
        set internet-service-id 7208961
    next
    edit "Wetransfer-ICMP"
        set internet-service-id 7208962
    next
    edit "Wetransfer-DNS"
        set internet-service-id 7208963
    next
    edit "Wetransfer-Outbound_Email"
        set internet-service-id 7208964
    next
    edit "Wetransfer-SSH"
        set internet-service-id 7208966
    next
    edit "Wetransfer-FTP"
        set internet-service-id 7208967
    next
    edit "Wetransfer-NTP"
        set internet-service-id 7208968
    next
    edit "Wetransfer-Inbound_Email"
        set internet-service-id 7208969
    next
    edit "Wetransfer-LDAP"
        set internet-service-id 7208974
    next
    edit "Wetransfer-NetBIOS.Session.Service"
        set internet-service-id 7208975
    next
    edit "Wetransfer-RTMP"
        set internet-service-id 7208976
    next
    edit "Wetransfer-NetBIOS.Name.Service"
        set internet-service-id 7208984
    next
    edit "Sendgrid-Sendgrid.Email"
        set internet-service-id 7274623
    next
    edit "Ubiquiti-UniFi"
        set internet-service-id 7340160
    next
    edit "Lifesize-Lifesize.Cloud"
        set internet-service-id 7405697
    next
    edit "Okta-Other"
        set internet-service-id 7471104
    next
    edit "Okta-Web"
        set internet-service-id 7471105
    next
    edit "Okta-ICMP"
        set internet-service-id 7471106
    next
    edit "Okta-DNS"
        set internet-service-id 7471107
    next
    edit "Okta-Outbound_Email"
        set internet-service-id 7471108
    next
    edit "Okta-SSH"
        set internet-service-id 7471110
    next
    edit "Okta-FTP"
        set internet-service-id 7471111
    next
    edit "Okta-NTP"
        set internet-service-id 7471112
    next
    edit "Okta-Inbound_Email"
        set internet-service-id 7471113
    next
    edit "Okta-LDAP"
        set internet-service-id 7471118
    next
    edit "Okta-NetBIOS.Session.Service"
        set internet-service-id 7471119
    next
    edit "Okta-RTMP"
        set internet-service-id 7471120
    next
    edit "Okta-NetBIOS.Name.Service"
        set internet-service-id 7471128
    next
    edit "Okta-Okta"
        set internet-service-id 7471307
    next
    edit "Cybozu-Other"
        set internet-service-id 7536640
    next
    edit "Cybozu-Web"
        set internet-service-id 7536641
    next
    edit "Cybozu-ICMP"
        set internet-service-id 7536642
    next
    edit "Cybozu-DNS"
        set internet-service-id 7536643
    next
    edit "Cybozu-Outbound_Email"
        set internet-service-id 7536644
    next
    edit "Cybozu-SSH"
        set internet-service-id 7536646
    next
    edit "Cybozu-FTP"
        set internet-service-id 7536647
    next
    edit "Cybozu-NTP"
        set internet-service-id 7536648
    next
    edit "Cybozu-Inbound_Email"
        set internet-service-id 7536649
    next
    edit "Cybozu-LDAP"
        set internet-service-id 7536654
    next
    edit "Cybozu-NetBIOS.Session.Service"
        set internet-service-id 7536655
    next
    edit "Cybozu-RTMP"
        set internet-service-id 7536656
    next
    edit "Cybozu-NetBIOS.Name.Service"
        set internet-service-id 7536664
    next
    edit "VNC-Other"
        set internet-service-id 7602176
    next
    edit "VNC-Web"
        set internet-service-id 7602177
    next
    edit "VNC-ICMP"
        set internet-service-id 7602178
    next
    edit "VNC-DNS"
        set internet-service-id 7602179
    next
    edit "VNC-Outbound_Email"
        set internet-service-id 7602180
    next
    edit "VNC-SSH"
        set internet-service-id 7602182
    next
    edit "VNC-FTP"
        set internet-service-id 7602183
    next
    edit "VNC-NTP"
        set internet-service-id 7602184
    next
    edit "VNC-Inbound_Email"
        set internet-service-id 7602185
    next
    edit "VNC-LDAP"
        set internet-service-id 7602190
    next
    edit "VNC-NetBIOS.Session.Service"
        set internet-service-id 7602191
    next
    edit "VNC-RTMP"
        set internet-service-id 7602192
    next
    edit "VNC-NetBIOS.Name.Service"
        set internet-service-id 7602200
    next
    edit "Egnyte-Egnyte"
        set internet-service-id 7667846
    next
    edit "CrowdStrike-CrowdStrike.Falcon.Cloud"
        set internet-service-id 7733383
    next
    edit "Aruba.it-Other"
        set internet-service-id 7798784
    next
    edit "Aruba.it-Web"
        set internet-service-id 7798785
    next
    edit "Aruba.it-ICMP"
        set internet-service-id 7798786
    next
    edit "Aruba.it-DNS"
        set internet-service-id 7798787
    next
    edit "Aruba.it-Outbound_Email"
        set internet-service-id 7798788
    next
    edit "Aruba.it-SSH"
        set internet-service-id 7798790
    next
    edit "Aruba.it-FTP"
        set internet-service-id 7798791
    next
    edit "Aruba.it-NTP"
        set internet-service-id 7798792
    next
    edit "Aruba.it-Inbound_Email"
        set internet-service-id 7798793
    next
    edit "Aruba.it-LDAP"
        set internet-service-id 7798798
    next
    edit "Aruba.it-NetBIOS.Session.Service"
        set internet-service-id 7798799
    next
    edit "Aruba.it-RTMP"
        set internet-service-id 7798800
    next
    edit "Aruba.it-NetBIOS.Name.Service"
        set internet-service-id 7798808
    next
    edit "ISLOnline-Other"
        set internet-service-id 7864320
    next
    edit "ISLOnline-Web"
        set internet-service-id 7864321
    next
    edit "ISLOnline-ICMP"
        set internet-service-id 7864322
    next
    edit "ISLOnline-DNS"
        set internet-service-id 7864323
    next
    edit "ISLOnline-Outbound_Email"
        set internet-service-id 7864324
    next
    edit "ISLOnline-SSH"
        set internet-service-id 7864326
    next
    edit "ISLOnline-FTP"
        set internet-service-id 7864327
    next
    edit "ISLOnline-NTP"
        set internet-service-id 7864328
    next
    edit "ISLOnline-Inbound_Email"
        set internet-service-id 7864329
    next
    edit "ISLOnline-LDAP"
        set internet-service-id 7864334
    next
    edit "ISLOnline-NetBIOS.Session.Service"
        set internet-service-id 7864335
    next
    edit "ISLOnline-RTMP"
        set internet-service-id 7864336
    next
    edit "ISLOnline-NetBIOS.Name.Service"
        set internet-service-id 7864344
    next
    edit "Akamai-CDN"
        set internet-service-id 7929993
    next
    edit "Akamai-Linode.Cloud"
        set internet-service-id 7930148
    next
    edit "Rackspace-CDN"
        set internet-service-id 7995529
    next
    edit "Instart-CDN"
        set internet-service-id 8061065
    next
    edit "Bitdefender-Other"
        set internet-service-id 8126464
    next
    edit "Bitdefender-Web"
        set internet-service-id 8126465
    next
    edit "Bitdefender-ICMP"
        set internet-service-id 8126466
    next
    edit "Bitdefender-DNS"
        set internet-service-id 8126467
    next
    edit "Bitdefender-Outbound_Email"
        set internet-service-id 8126468
    next
    edit "Bitdefender-SSH"
        set internet-service-id 8126470
    next
    edit "Bitdefender-FTP"
        set internet-service-id 8126471
    next
    edit "Bitdefender-NTP"
        set internet-service-id 8126472
    next
    edit "Bitdefender-Inbound_Email"
        set internet-service-id 8126473
    next
    edit "Bitdefender-LDAP"
        set internet-service-id 8126478
    next
    edit "Bitdefender-NetBIOS.Session.Service"
        set internet-service-id 8126479
    next
    edit "Bitdefender-RTMP"
        set internet-service-id 8126480
    next
    edit "Bitdefender-NetBIOS.Name.Service"
        set internet-service-id 8126488
    next
    edit "Pingdom-Other"
        set internet-service-id 8192000
    next
    edit "Pingdom-Web"
        set internet-service-id 8192001
    next
    edit "Pingdom-ICMP"
        set internet-service-id 8192002
    next
    edit "Pingdom-DNS"
        set internet-service-id 8192003
    next
    edit "Pingdom-Outbound_Email"
        set internet-service-id 8192004
    next
    edit "Pingdom-SSH"
        set internet-service-id 8192006
    next
    edit "Pingdom-FTP"
        set internet-service-id 8192007
    next
    edit "Pingdom-NTP"
        set internet-service-id 8192008
    next
    edit "Pingdom-Inbound_Email"
        set internet-service-id 8192009
    next
    edit "Pingdom-LDAP"
        set internet-service-id 8192014
    next
    edit "Pingdom-NetBIOS.Session.Service"
        set internet-service-id 8192015
    next
    edit "Pingdom-RTMP"
        set internet-service-id 8192016
    next
    edit "Pingdom-NetBIOS.Name.Service"
        set internet-service-id 8192024
    next
    edit "UptimeRobot-Other"
        set internet-service-id 8257536
    next
    edit "UptimeRobot-Web"
        set internet-service-id 8257537
    next
    edit "UptimeRobot-ICMP"
        set internet-service-id 8257538
    next
    edit "UptimeRobot-DNS"
        set internet-service-id 8257539
    next
    edit "UptimeRobot-Outbound_Email"
        set internet-service-id 8257540
    next
    edit "UptimeRobot-SSH"
        set internet-service-id 8257542
    next
    edit "UptimeRobot-FTP"
        set internet-service-id 8257543
    next
    edit "UptimeRobot-NTP"
        set internet-service-id 8257544
    next
    edit "UptimeRobot-Inbound_Email"
        set internet-service-id 8257545
    next
    edit "UptimeRobot-LDAP"
        set internet-service-id 8257550
    next
    edit "UptimeRobot-NetBIOS.Session.Service"
        set internet-service-id 8257551
    next
    edit "UptimeRobot-RTMP"
        set internet-service-id 8257552
    next
    edit "UptimeRobot-NetBIOS.Name.Service"
        set internet-service-id 8257560
    next
    edit "UptimeRobot-UptimeRobot.Monitor"
        set internet-service-id 8257709
    next
    edit "Quovadisglobal-Other"
        set internet-service-id 8323072
    next
    edit "Quovadisglobal-Web"
        set internet-service-id 8323073
    next
    edit "Quovadisglobal-ICMP"
        set internet-service-id 8323074
    next
    edit "Quovadisglobal-DNS"
        set internet-service-id 8323075
    next
    edit "Quovadisglobal-Outbound_Email"
        set internet-service-id 8323076
    next
    edit "Quovadisglobal-SSH"
        set internet-service-id 8323078
    next
    edit "Quovadisglobal-FTP"
        set internet-service-id 8323079
    next
    edit "Quovadisglobal-NTP"
        set internet-service-id 8323080
    next
    edit "Quovadisglobal-Inbound_Email"
        set internet-service-id 8323081
    next
    edit "Quovadisglobal-LDAP"
        set internet-service-id 8323086
    next
    edit "Quovadisglobal-NetBIOS.Session.Service"
        set internet-service-id 8323087
    next
    edit "Quovadisglobal-RTMP"
        set internet-service-id 8323088
    next
    edit "Quovadisglobal-NetBIOS.Name.Service"
        set internet-service-id 8323096
    next
    edit "Splashtop-Splashtop"
        set internet-service-id 8388751
    next
    edit "Zoox-Other"
        set internet-service-id 8454144
    next
    edit "Zoox-Web"
        set internet-service-id 8454145
    next
    edit "Zoox-ICMP"
        set internet-service-id 8454146
    next
    edit "Zoox-DNS"
        set internet-service-id 8454147
    next
    edit "Zoox-Outbound_Email"
        set internet-service-id 8454148
    next
    edit "Zoox-SSH"
        set internet-service-id 8454150
    next
    edit "Zoox-FTP"
        set internet-service-id 8454151
    next
    edit "Zoox-NTP"
        set internet-service-id 8454152
    next
    edit "Zoox-Inbound_Email"
        set internet-service-id 8454153
    next
    edit "Zoox-LDAP"
        set internet-service-id 8454158
    next
    edit "Zoox-NetBIOS.Session.Service"
        set internet-service-id 8454159
    next
    edit "Zoox-RTMP"
        set internet-service-id 8454160
    next
    edit "Zoox-NetBIOS.Name.Service"
        set internet-service-id 8454168
    next
    edit "Skyfii-Other"
        set internet-service-id 8519680
    next
    edit "Skyfii-Web"
        set internet-service-id 8519681
    next
    edit "Skyfii-ICMP"
        set internet-service-id 8519682
    next
    edit "Skyfii-DNS"
        set internet-service-id 8519683
    next
    edit "Skyfii-Outbound_Email"
        set internet-service-id 8519684
    next
    edit "Skyfii-SSH"
        set internet-service-id 8519686
    next
    edit "Skyfii-FTP"
        set internet-service-id 8519687
    next
    edit "Skyfii-NTP"
        set internet-service-id 8519688
    next
    edit "Skyfii-Inbound_Email"
        set internet-service-id 8519689
    next
    edit "Skyfii-LDAP"
        set internet-service-id 8519694
    next
    edit "Skyfii-NetBIOS.Session.Service"
        set internet-service-id 8519695
    next
    edit "Skyfii-RTMP"
        set internet-service-id 8519696
    next
    edit "Skyfii-NetBIOS.Name.Service"
        set internet-service-id 8519704
    next
    edit "CoffeeBean-Other"
        set internet-service-id 8585216
    next
    edit "CoffeeBean-Web"
        set internet-service-id 8585217
    next
    edit "CoffeeBean-ICMP"
        set internet-service-id 8585218
    next
    edit "CoffeeBean-DNS"
        set internet-service-id 8585219
    next
    edit "CoffeeBean-Outbound_Email"
        set internet-service-id 8585220
    next
    edit "CoffeeBean-SSH"
        set internet-service-id 8585222
    next
    edit "CoffeeBean-FTP"
        set internet-service-id 8585223
    next
    edit "CoffeeBean-NTP"
        set internet-service-id 8585224
    next
    edit "CoffeeBean-Inbound_Email"
        set internet-service-id 8585225
    next
    edit "CoffeeBean-LDAP"
        set internet-service-id 8585230
    next
    edit "CoffeeBean-NetBIOS.Session.Service"
        set internet-service-id 8585231
    next
    edit "CoffeeBean-RTMP"
        set internet-service-id 8585232
    next
    edit "CoffeeBean-NetBIOS.Name.Service"
        set internet-service-id 8585240
    next
    edit "Cloud4Wi-Other"
        set internet-service-id 8650752
    next
    edit "Cloud4Wi-Web"
        set internet-service-id 8650753
    next
    edit "Cloud4Wi-ICMP"
        set internet-service-id 8650754
    next
    edit "Cloud4Wi-DNS"
        set internet-service-id 8650755
    next
    edit "Cloud4Wi-Outbound_Email"
        set internet-service-id 8650756
    next
    edit "Cloud4Wi-SSH"
        set internet-service-id 8650758
    next
    edit "Cloud4Wi-FTP"
        set internet-service-id 8650759
    next
    edit "Cloud4Wi-NTP"
        set internet-service-id 8650760
    next
    edit "Cloud4Wi-Inbound_Email"
        set internet-service-id 8650761
    next
    edit "Cloud4Wi-LDAP"
        set internet-service-id 8650766
    next
    edit "Cloud4Wi-NetBIOS.Session.Service"
        set internet-service-id 8650767
    next
    edit "Cloud4Wi-RTMP"
        set internet-service-id 8650768
    next
    edit "Cloud4Wi-NetBIOS.Name.Service"
        set internet-service-id 8650776
    next
    edit "Panda-Panda.Security"
        set internet-service-id 8716432
    next
    edit "Ewon-Talk2M"
        set internet-service-id 8781970
    next
    edit "Nutanix-Nutanix.Cloud"
        set internet-service-id 8847507
    next
    edit "Backblaze-Other"
        set internet-service-id 8912896
    next
    edit "Backblaze-Web"
        set internet-service-id 8912897
    next
    edit "Backblaze-ICMP"
        set internet-service-id 8912898
    next
    edit "Backblaze-DNS"
        set internet-service-id 8912899
    next
    edit "Backblaze-Outbound_Email"
        set internet-service-id 8912900
    next
    edit "Backblaze-SSH"
        set internet-service-id 8912902
    next
    edit "Backblaze-FTP"
        set internet-service-id 8912903
    next
    edit "Backblaze-NTP"
        set internet-service-id 8912904
    next
    edit "Backblaze-Inbound_Email"
        set internet-service-id 8912905
    next
    edit "Backblaze-LDAP"
        set internet-service-id 8912910
    next
    edit "Backblaze-NetBIOS.Session.Service"
        set internet-service-id 8912911
    next
    edit "Backblaze-RTMP"
        set internet-service-id 8912912
    next
    edit "Backblaze-NetBIOS.Name.Service"
        set internet-service-id 8912920
    next
    edit "Extreme-Extreme.Cloud"
        set internet-service-id 8978580
    next
    edit "XING-Other"
        set internet-service-id 9043968
    next
    edit "XING-Web"
        set internet-service-id 9043969
    next
    edit "XING-ICMP"
        set internet-service-id 9043970
    next
    edit "XING-DNS"
        set internet-service-id 9043971
    next
    edit "XING-Outbound_Email"
        set internet-service-id 9043972
    next
    edit "XING-SSH"
        set internet-service-id 9043974
    next
    edit "XING-FTP"
        set internet-service-id 9043975
    next
    edit "XING-NTP"
        set internet-service-id 9043976
    next
    edit "XING-Inbound_Email"
        set internet-service-id 9043977
    next
    edit "XING-LDAP"
        set internet-service-id 9043982
    next
    edit "XING-NetBIOS.Session.Service"
        set internet-service-id 9043983
    next
    edit "XING-RTMP"
        set internet-service-id 9043984
    next
    edit "XING-NetBIOS.Name.Service"
        set internet-service-id 9043992
    next
    edit "Genesys-PureCloud"
        set internet-service-id 9109653
    next
    edit "BlackBerry-Cylance"
        set internet-service-id 9175190
    next
    edit "DigiCert-OCSP"
        set internet-service-id 9240728
    next
    edit "Infomaniak-SwissTransfer"
        set internet-service-id 9306265
    next
    edit "Fuze-Fuze"
        set internet-service-id 9371802
    next
    edit "Truecaller-Truecaller"
        set internet-service-id 9437339
    next
    edit "GlobalSign-OCSP"
        set internet-service-id 9502872
    next
    edit "VeriSign-OCSP"
        set internet-service-id 9568408
    next
    edit "Sony-PlayStation.Network"
        set internet-service-id 9633952
    next
    edit "Acronis-Cyber.Cloud"
        set internet-service-id 9699489
    next
    edit "RingCentral-RingCentral"
        set internet-service-id 9765027
    next
    edit "FSecure-FSecure"
        set internet-service-id 9830564
    next
    edit "Kaseya-Kaseya.Cloud"
        set internet-service-id 9896101
    next
    edit "Shodan-Scanner"
        set internet-service-id 9961638
    next
    edit "Censys-Scanner"
        set internet-service-id 10027174
    next
    edit "Valve-Steam"
        set internet-service-id 10092711
    next
    edit "YouSeeU-Bongo"
        set internet-service-id 10158248
    next
    edit "Cato-Cato.Cloud"
        set internet-service-id 10223785
    next
    edit "SolarWinds-SpamExperts"
        set internet-service-id 10289323
    next
    edit "SolarWinds-Pingdom.Probe"
        set internet-service-id 10289326
    next
    edit "SolarWinds-SolarWinds.RMM"
        set internet-service-id 10289379
    next
    edit "8X8-8X8.Cloud"
        set internet-service-id 10354860
    next
    edit "Zattoo-Zattoo.TV"
        set internet-service-id 10420401
    next
    edit "Datto-Datto.RMM"
        set internet-service-id 10485939
    next
    edit "Barracuda-Barracuda.Cloud"
        set internet-service-id 10551477
    next
    edit "Naver-Line"
        set internet-service-id 10617015
    next
    edit "Disney-Disney+"
        set internet-service-id 10682552
    next
    edit "DNS-DoH_DoT"
        set internet-service-id 10748089
    next
    edit "DNS-Root.Name.Servers"
        set internet-service-id 10748156
    next
    edit "Quad9-Quad9.Standard.DNS"
        set internet-service-id 10813626
    next
    edit "Stretchoid-Scanner"
        set internet-service-id 10879142
    next
    edit "Poly-RealConnect.Service"
        set internet-service-id 10944700
    next
    edit "Telegram-Telegram"
        set internet-service-id 11010249
    next
    edit "Spotify-Spotify"
        set internet-service-id 11075786
    next
    edit "NextDNS-NextDNS"
        set internet-service-id 11141324
    next
    edit "Fastly-CDN"
        set internet-service-id 11206793
    next
    edit "Neustar-UltraDNS.Probes"
        set internet-service-id 11272397
    next
    edit "Malicious-Malicious.Server"
        set internet-service-id 11337935
    next
    edit "NIST-ITS"
        set internet-service-id 11403472
    next
    edit "Jamf-Jamf.Cloud"
        set internet-service-id 11469009
    next
    edit "Alcatel.Lucent-Rainbow"
        set internet-service-id 11534546
    next
    edit "Forcepoint-Forcepoint.Cloud"
        set internet-service-id 11600083
    next
    edit "Datadog-Datadog"
        set internet-service-id 11665620
    next
    edit "Mimecast-Mimecast"
        set internet-service-id 11731157
    next
    edit "MediaFire-Other"
        set internet-service-id 11796480
    next
    edit "MediaFire-Web"
        set internet-service-id 11796481
    next
    edit "MediaFire-ICMP"
        set internet-service-id 11796482
    next
    edit "MediaFire-DNS"
        set internet-service-id 11796483
    next
    edit "MediaFire-Outbound_Email"
        set internet-service-id 11796484
    next
    edit "MediaFire-SSH"
        set internet-service-id 11796486
    next
    edit "MediaFire-FTP"
        set internet-service-id 11796487
    next
    edit "MediaFire-NTP"
        set internet-service-id 11796488
    next
    edit "MediaFire-Inbound_Email"
        set internet-service-id 11796489
    next
    edit "MediaFire-LDAP"
        set internet-service-id 11796494
    next
    edit "MediaFire-NetBIOS.Session.Service"
        set internet-service-id 11796495
    next
    edit "MediaFire-RTMP"
        set internet-service-id 11796496
    next
    edit "MediaFire-NetBIOS.Name.Service"
        set internet-service-id 11796504
    next
    edit "Pandora-Pandora"
        set internet-service-id 11862230
    next
    edit "SiriusXM-SiriusXM"
        set internet-service-id 11927767
    next
    edit "Hopin-Hopin"
        set internet-service-id 11993304
    next
    edit "RedShield-RedShield.Cloud"
        set internet-service-id 12058842
    next
    edit "InterneTTL-Scanner"
        set internet-service-id 12124326
    next
    edit "VadeSecure-VadeSecure.Cloud"
        set internet-service-id 12189915
    next
    edit "Netskope-Netskope.Cloud"
        set internet-service-id 12255452
    next
    edit "ClickMeeting-ClickMeeting"
        set internet-service-id 12320989
    next
    edit "Tenable-Tenable.io.Cloud.Scanner"
        set internet-service-id 12386528
    next
    edit "Vidyo-VidyoCloud"
        set internet-service-id 12452065
    next
    edit "OpenNIC-OpenNIC.DNS"
        set internet-service-id 12517602
    next
    edit "Sectigo-Sectigo"
        set internet-service-id 12583141
    next
    edit "DigitalOcean-DigitalOcean.Platform"
        set internet-service-id 12648679
    next
    edit "Pitney.Bowes-Pitney.Bowes.Data.Center"
        set internet-service-id 12714216
    next
    edit "VPN-Anonymous.VPN"
        set internet-service-id 12779753
    next
    edit "Blockchain-Crypto.Mining.Pool"
        set internet-service-id 12845290
    next
    edit "FactSet-FactSet"
        set internet-service-id 12910830
    next
    edit "Bloomberg-Bloomberg"
        set internet-service-id 12976367
    next
    edit "Five9-Five9"
        set internet-service-id 13041904
    next
    edit "Gigas-Gigas.Cloud"
        set internet-service-id 13107441
    next
    edit "Imperva-Imperva.Cloud.WAF"
        set internet-service-id 13172978
    next
    edit "INAP-INAP"
        set internet-service-id 13238515
    next
    edit "Azion-Azion.Platform"
        set internet-service-id 13304053
    next
    edit "Hurricane.Electric-Hurricane.Electric.Internet.Services"
        set internet-service-id 13369590
    next
    edit "NodePing-NodePing.Probe"
        set internet-service-id 13435127
    next
    edit "Frontline-Frontline"
        set internet-service-id 13500665
    next
    edit "Tally-Tally.ERP"
        set internet-service-id 13566202
    next
    edit "Hosting-Bulletproof.Hosting"
        set internet-service-id 13631739
    next
    edit "Okko-Okko.TV"
        set internet-service-id 13697277
    next
    edit "Voximplant-Voximplant.Platform"
        set internet-service-id 13762829
    next
    edit "OVHcloud-OVHcloud"
        set internet-service-id 13828367
    next
    edit "SentinelOne-SentinelOne.Cloud"
        set internet-service-id 13893905
    next
    edit "Kakao-Kakao.Services"
        set internet-service-id 13959442
    next
    edit "Stripe-Stripe"
        set internet-service-id 14024979
    next
    edit "NetScout-Scanner"
        set internet-service-id 14090406
    next
    edit "Recyber-Scanner"
        set internet-service-id 14155942
    next
    edit "Cyber.Casa-Scanner"
        set internet-service-id 14221478
    next
    edit "GTHost-Dedicated.Instant.Servers"
        set internet-service-id 14287132
    next
    edit "ivi-ivi.Streaming"
        set internet-service-id 14352669
    next
    edit "BinaryEdge-Scanner"
        set internet-service-id 14418086
    next
    edit "Fintech-MarketMap.Terminal"
        set internet-service-id 14483742
    next
    edit "xMatters-xMatters.Platform"
        set internet-service-id 14549279
    next
    edit "Blizzard-Battle.Net"
        set internet-service-id 14614816
    next
    edit "Axon-Evidence"
        set internet-service-id 14680353
    next
    edit "CDN77-CDN"
        set internet-service-id 14745737
    next
    edit "GCore.Labs-CDN"
        set internet-service-id 14811273
    next
    edit "Matrix42-FastViewer"
        set internet-service-id 14876962
    next
    edit "Bunny.net-CDN"
        set internet-service-id 14942345
    next
    edit "StackPath-CDN"
        set internet-service-id 15007881
    next
    edit "Edgio-CDN"
        set internet-service-id 15073417
    next
    edit "CacheFly-CDN"
        set internet-service-id 15138953
    next
    edit "Microsoft-Azure.Connectors"
        set internet-service-id 327980
    next
    edit "Microsoft-Teams.Published.Worldwide.Optimize"
        set internet-service-id 327991
    next
    edit "Microsoft-Teams.Published.Worldwide.Allow"
        set internet-service-id 327992
    next
    edit "Microsoft-Azure.Front.Door"
        set internet-service-id 327993
    next
    edit "Microsoft-Azure.Service.Bus"
        set internet-service-id 328007
    next
    edit "Microsoft-Azure.Microsoft.Defender"
        set internet-service-id 328009
    next
    edit "Microsoft-Azure.Resource.Manager"
        set internet-service-id 328013
    next
    edit "Microsoft-Azure.Arc.Infrastructure"
        set internet-service-id 328014
    next
    edit "Microsoft-Azure.Storage"
        set internet-service-id 328015
    next
    edit "Microsoft-Azure.ATP"
        set internet-service-id 328016
    next
    edit "Microsoft-Azure.Traffic.Manager"
        set internet-service-id 328017
    next
    edit "Microsoft-Azure.Windows.Admin.Center"
        set internet-service-id 328018
    next
    edit "Microsoft-Azure.KeyVault"
        set internet-service-id 328021
    next
    edit "Microsoft-Azure.Databricks"
        set internet-service-id 328034
    next
    edit "Microsoft-Azure.Event.Hub"
        set internet-service-id 328035
    next
    edit "Microsoft-Azure.Power.Platform"
        set internet-service-id 328043
    next
    edit "Salesforce-Hyperforce"
        set internet-service-id 655738
    next
    edit "Fortinet-FortiClient.EMS"
        set internet-service-id 1245477
    next
    edit "Fortinet-FortiWeb.Cloud"
        set internet-service-id 1245480
    next
    edit "Fortinet-FortiSASE"
        set internet-service-id 1245481
    next
    edit "Fortinet-FortiGuard.SOCaaS"
        set internet-service-id 1245514
    next
    edit "Fortinet-FortiDLP.Cloud"
        set internet-service-id 1245546
    next
    edit "Fortinet-FortiMonitor"
        set internet-service-id 1245558
    next
    edit "Fortinet-FortiSandbox"
        set internet-service-id 1245560
    next
    edit "Fortinet-FortiSandbox.Cloud"
        set internet-service-id 1245561
    next
    edit "Tencent-VooV.Meeting"
        set internet-service-id 2556219
    next
    edit "NewRelic-Synthetic.Monitor"
        set internet-service-id 4849970
    next
    edit "Rapid7-Scanner"
        set internet-service-id 5898406
    next
    edit "SAP-SAP.Ariba"
        set internet-service-id 6291766
    next
    edit "Alibaba-DingTalk"
        set internet-service-id 6881623
    next
    edit "ISLOnline-ISLOnline"
        set internet-service-id 7864667
    next
    edit "Datto-Datto.BCDR"
        set internet-service-id 10486083
    next
    edit "DNS-ARPA.Name.Servers"
        set internet-service-id 10748206
    next
    edit "DNS-Generic.TLD.Name.Servers"
        set internet-service-id 10748284
    next
    edit "OVHcloud-OVH.Telecom"
        set internet-service-id 13828461
    next
    edit "Paylocity-Paylocity"
        set internet-service-id 15204646
    next
    edit "Qualys-Qualys.Cloud.Platform"
        set internet-service-id 15270183
    next
    edit "Dailymotion-Other"
        set internet-service-id 15335424
    next
    edit "Dailymotion-Web"
        set internet-service-id 15335425
    next
    edit "Dailymotion-ICMP"
        set internet-service-id 15335426
    next
    edit "Dailymotion-DNS"
        set internet-service-id 15335427
    next
    edit "Dailymotion-Outbound_Email"
        set internet-service-id 15335428
    next
    edit "Dailymotion-SSH"
        set internet-service-id 15335430
    next
    edit "Dailymotion-FTP"
        set internet-service-id 15335431
    next
    edit "Dailymotion-NTP"
        set internet-service-id 15335432
    next
    edit "Dailymotion-Inbound_Email"
        set internet-service-id 15335433
    next
    edit "Dailymotion-LDAP"
        set internet-service-id 15335438
    next
    edit "Dailymotion-NetBIOS.Session.Service"
        set internet-service-id 15335439
    next
    edit "Dailymotion-RTMP"
        set internet-service-id 15335440
    next
    edit "Dailymotion-NetBIOS.Name.Service"
        set internet-service-id 15335448
    next
    edit "LaunchDarkly-LaunchDarkly.Platform"
        set internet-service-id 15401258
    next
    edit "Medianova-CDN"
        set internet-service-id 15466633
    next
    edit "NetDocuments-NetDocuments.Platform"
        set internet-service-id 15532331
    next
    edit "Vonage-Vonage.Contact.Center"
        set internet-service-id 15597869
    next
    edit "Vonage-Vonage.Video.API"
        set internet-service-id 15597872
    next
    edit "Veritas-Enterprise.Vault.Cloud"
        set internet-service-id 15663407
    next
    edit "UK.NCSC-Scanner"
        set internet-service-id 15728806
    next
    edit "Restream-Restream.Platform"
        set internet-service-id 15794481
    next
    edit "ArcticWolf-ArcticWolf.Cloud"
        set internet-service-id 15860019
    next
    edit "CounterPath-Bria"
        set internet-service-id 15925556
    next
    edit "CriminalIP-Scanner"
        set internet-service-id 15990950
    next
    edit "IPFS-IPFS.Gateway"
        set internet-service-id 16056629
    next
    edit "Internet.Census.Group-Scanner"
        set internet-service-id 16122022
    next
    edit "Performive-Performive.Cloud"
        set internet-service-id 16187706
    next
    edit "OneLogin-OneLogin"
        set internet-service-id 16253244
    next
    edit "Shadowserver-Scanner"
        set internet-service-id 16318630
    next
    edit "Turkcell-Suit.Conference"
        set internet-service-id 16384317
    next
    edit "LeakIX-Scanner"
        set internet-service-id 16449702
    next
    edit "Infoblox-BloxOne"
        set internet-service-id 16515390
    next
    edit "Nice-CXone"
        set internet-service-id 16580927
    next
    edit "Hetzner-Hetzner.Hosting.Service"
        set internet-service-id 16646464
    next
    edit "ThreatLocker-ThreatLocker"
        set internet-service-id 16712001
    next
    edit "ZPE-ZPE.Cloud"
        set internet-service-id 16777538
    next
    edit "ColoCrossing-ColoCrossing.Hosting.Service"
        set internet-service-id 16843076
    next
    edit "Sinch-Mailgun"
        set internet-service-id 16908613
    next
    edit "SpaceX-Starlink"
        set internet-service-id 16974150
    next
    edit "Ingenuity-Ingenuity.Cloud.Service"
        set internet-service-id 17039688
    next
    edit "Skyhigh.Security-Secure.Web.Gateway"
        set internet-service-id 17105227
    next
    edit "Stark.Industries-Stark.Industries.Hosting.Service"
        set internet-service-id 17170764
    next
    edit "StatusCake-StatusCake.Monitor"
        set internet-service-id 17236307
    next
    edit "NAP-NAPLAN"
        set internet-service-id 17301844
    next
    edit "Elastic-Elastic.Cloud"
        set internet-service-id 17367382
    next
    edit "NFON-NFON"
        set internet-service-id 17432920
    next
    edit "SERVERD-SERVERD.Hosting.Service"
        set internet-service-id 17498457
    next
    edit "MEGA-MEGA.Cloud"
        set internet-service-id 17563994
    next
    edit "Hadrian-Scanner"
        set internet-service-id 17629350
    next
    edit "Dotcom.Monitor-Dotcom.Monitor"
        set internet-service-id 17695068
    next
    edit "Ahrefs-AhrefsBot"
        set internet-service-id 17760605
    next
    edit "Semrush-SemrushBot"
        set internet-service-id 17826142
    next
    edit "Zero.Networks-Zero.Networks"
        set internet-service-id 17891679
    next
    edit "Vultr-Vultr.Cloud"
        set internet-service-id 17957216
    next
    edit "EGI-EGI.Hosting.Service"
        set internet-service-id 18022753
    next
    edit "ONYPHE-Scanner"
        set internet-service-id 18088102
    next
    edit "Proofpoint-Proofpoint"
        set internet-service-id 18153828
    next
    edit "Lookout-Lookout.Cloud"
        set internet-service-id 18219365
    next
    edit "Heimdal-Heimdal.Security"
        set internet-service-id 18284902
    next
    edit "Yealink-Yealink.Meeting"
        set internet-service-id 18350439
    next
    edit "Secomea-Secomea"
        set internet-service-id 18415976
    next
    edit "CallTower-CT.Cloud"
        set internet-service-id 18481513
    next
    edit "OpenAI-OpenAI.Bot"
        set internet-service-id 18547052
    next
    edit "Alpemix-Alpemix"
        set internet-service-id 18612590
    next
    edit "M247-M247.Hosting.Service"
        set internet-service-id 18678127
    next
    edit "Quintex-Quintex.Hosting.Service"
        set internet-service-id 18743664
    next
    edit "Aeza-Aeza.Hosting.Service"
        set internet-service-id 18809201
    next
    edit "Amanah-Amanah.Hosting.Service"
        set internet-service-id 18874738
    next
    edit "ByteDance-Lark"
        set internet-service-id 18940275
    next
    edit "KnowBe4-KnowBe4"
        set internet-service-id 19005812
    next
    edit "Keeper-Keeper.Security"
        set internet-service-id 19071349
    next
    edit "NinjaOne-NinjaOne"
        set internet-service-id 19136887
    next
    edit "Modat-Scanner"
        set internet-service-id 19202214
    next
    edit "Make-Make.Platform"
        set internet-service-id 19267963
    next
    edit "Cloudzy-Cloudzy.Hosting.Service"
        set internet-service-id 19333501
    next
    edit "Nokia-Deepfield.Genome.Crawler"
        set internet-service-id 19399038
    next
    edit "Neat-Neat.Cloud"
        set internet-service-id 19464575
    next
    edit "Tor-Tor.Node"
        set internet-service-id 2818432
    next
    edit "OpenAI-GPT.Actions"
        set internet-service-id 18547073
    next
end
config firewall internet-service-definition
end
config wanopt content-delivery-network-rule
    edit "vcache://"
        set comment "Static entries are not allowed to change except disable."
        set response-expires enable
        set text-response-vcache disable
        config rules
            edit "rule1"
                config match-entries
                    edit 1
                        set pattern "/*.m3u8"
                    next
                end
                config content-id
                    set target hls-manifest
                    set start-str "/"
                end
            next
            edit "rule2"
                config match-entries
                    edit 1
                        set pattern "/*.mpd"
                    next
                end
                config content-id
                    set target dash-manifest
                    set start-str "/"
                end
            next
            edit "rule3"
                config match-entries
                    edit 1
                        set pattern "/*.ts"
                    next
                end
                config content-id
                    set target hls-fragment
                    set start-str "/"
                end
            next
            edit "rule4"
                config match-entries
                    edit 1
                        set pattern "/*.*"
                    next
                end
                config content-id
                    set target dash-fragment
                    set start-str "/"
                end
            next
        end
    next
    edit "vcache://youtube/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "youtube.com"
        set category youtube
        set text-response-vcache disable
        config rules
            edit "rule1"
                config match-entries
                    edit 1
                        set pattern "/videoplayback"
                    next
                end
                config content-id
                    set target youtube-id
                    set start-str "v="
                    set start-skip 2
                    set end-str "&"
                end
            next
            edit "rule2"
                config match-entries
                    edit 1
                        set pattern "/videoplayback"
                    next
                end
                config content-id
                    set target youtube-id
                    set start-str "v="
                    set start-skip 2
                end
            next
            edit "rule3"
                set match-mode any
                config match-entries
                    edit 1
                        set pattern "/stream_204"
                    next
                    edit 2
                        set pattern "/ptracking"
                    next
                    edit 3
                        set pattern "/get_video_info"
                    next
                end
                config content-id
                    set target youtube-map
                    set start-str "/"
                end
            next
        end
    next
    edit "vcache://googlevideo/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "googlevideo.com"
        set category youtube
        set text-response-vcache disable
        config rules
            edit "rule1"
                config match-entries
                    edit 1
                        set pattern "/videoplayback"
                    next
                end
                config content-id
                    set target youtube-id
                    set start-str "v="
                    set start-skip 2
                    set end-str "&"
                end
            next
            edit "rule2"
                config match-entries
                    edit 1
                        set pattern "/videoplayback"
                    next
                end
                config content-id
                    set target youtube-id
                    set start-str "v="
                    set start-skip 2
                end
            next
            edit "rule3"
                set match-mode any
                config match-entries
                    edit 1
                        set pattern "/stream_204"
                    next
                    edit 2
                        set pattern "/ptracking"
                    next
                    edit 3
                        set pattern "/get_video_info"
                    next
                end
                config content-id
                    set target youtube-map
                    set start-str "/"
                end
            next
        end
    next
    edit "vcache://metacafe/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "mccont.com" "akvideos.metacafe.com" "cdn.metacafe.com"
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                set skip-rule-mode any
                config match-entries
                    edit 1
                        set pattern "/*.flv"
                    next
                    edit 2
                        set pattern "/*.mp4"
                    next
                    edit 3
                        set pattern "/*.ts"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://facebook/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "fbcdn.net" "facebook.com"
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                set skip-rule-mode any
                config match-entries
                    edit 1
                        set pattern "/*.flv"
                    next
                    edit 2
                        set pattern "/*.mp4"
                    next
                    edit 3
                        set pattern "/*.ts"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://dailymotion/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "dailymotion.com" "dmcdn.net"
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                config match-entries
                    edit 1
                        set pattern "/video/*.mp4"
                    next
                    edit 2
                        set pattern "/video/*.flv"
                    next
                    edit 3
                        set pattern "/video/*.ts"
                    next
                    edit 4
                        set pattern "/video/*.on2"
                    next
                    edit 5
                        set pattern "/video/*.aac"
                    next
                    edit 6
                        set pattern "/video/*.h264"
                    next
                    edit 7
                        set pattern "/video/*.h263"
                    next
                    edit 8
                        set pattern "/sec*.mp4"
                    next
                    edit 9
                        set pattern "/sec*.flv"
                    next
                    edit 10
                        set pattern "/sec*.on2"
                    next
                    edit 11
                        set pattern "/sec*.aac"
                    next
                    edit 12
                        set pattern "/sec*.h264"
                    next
                    edit 13
                        set pattern "/sec*.h263"
                    next
                    edit 14
                        set pattern "*.ts"
                    next
                end
                config skip-entries
                    edit 1
                        set target parameter
                        set pattern "start=*"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://break/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "break.com" "0ebe.edgecastcdn.net"
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                config match-entries
                    edit 1
                        set pattern "/dnet/media/*.flv"
                    next
                    edit 2
                        set pattern "/dnet/media/*.mp4"
                    next
                end
                config skip-entries
                    edit 1
                        set target parameter
                        set pattern "ec_seek=*"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
            edit "rule2"
                config match-entries
                    edit 1
                        set pattern "/*.mp4*"
                    next
                    edit 2
                        set pattern "*Seg*"
                    next
                    edit 3
                        set pattern "*Frag*"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://msn/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "video.msn.com"
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                set skip-rule-mode any
                config match-entries
                    edit 1
                        set pattern "/*.flv"
                    next
                    edit 2
                        set pattern "/*.mp4"
                    next
                    edit 3
                        set pattern "/*.ts"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://llnwd/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "llnwd.net"
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                config match-entries
                    edit 1
                        set pattern "/*.flv"
                    next
                    edit 2
                        set pattern "/*.mp4"
                    next
                    edit 3
                        set pattern "/*.fll"
                    next
                end
                config skip-entries
                    edit 1
                        set target parameter
                        set pattern "fs=*"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://yahoo/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "yimg.com"
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                config match-entries
                    edit 1
                        set pattern "/*.flv"
                    next
                    edit 2
                        set pattern "/*.mp4"
                    next
                    edit 3
                        set pattern "/*.m4s"
                    next
                end
                config content-id
                    set target parameter
                    set start-str "vid="
                end
            next
        end
    next
    edit "vcache://myspace/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "myspacecdn.com"
        set request-cache-control enable
        set response-cache-control enable
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                set skip-rule-mode any
                config match-entries
                    edit 1
                        set pattern "/*.flv"
                    next
                    edit 2
                        set pattern "/*.mp4"
                    next
                    edit 3
                        set pattern "/*.ts"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://vimeo/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "vimeo.com" "vimeocdn.com" "56skyfiregce-a.akamaihd.net"
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                config match-entries
                    edit 1
                        set pattern "/*.flv"
                    next
                    edit 2
                        set pattern "/*.mp4"
                    next
                    edit 3
                        set pattern "/*.m4s"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://blip.tv/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "blip.tv"
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                config match-entries
                    edit 1
                        set pattern "/*.m4v"
                    next
                    edit 2
                        set pattern "/*.flv"
                    next
                    edit 3
                        set pattern "/*.mp4"
                    next
                    edit 4
                        set pattern "/*.wmv"
                    next
                    edit 5
                        set pattern "/*.rm"
                    next
                    edit 6
                        set pattern "/*.ram"
                    next
                    edit 7
                        set pattern "/*.mov"
                    next
                    edit 8
                        set pattern "/*.avi"
                    next
                end
                config skip-entries
                    edit 1
                        set target parameter
                        set pattern "ms=*"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://maker.tv/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "videos-f.jwpsrv.com"
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                config match-entries
                    edit 1
                        set pattern "/*.ts"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://aol/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "stream.aol.com" "5min.com" "vidiblevod-vh.akamaihd.net" "stg-ec-ore-u.uplynk.com" "vidible.tv"
        set response-expires enable
        config rules
            edit "rule1"
                config match-entries
                    edit 1
                        set pattern "/*.mp4"
                    next
                end
                config skip-entries
                    edit 1
                        set target parameter
                        set pattern "*timeoffset=*"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
            edit "rule2"
                config match-entries
                    edit 1
                        set pattern "/*.ts"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://clipfish/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "clipfish.de" "universal-music.de"
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                config match-entries
                    edit 1
                        set pattern "/*.flv"
                    next
                    edit 2
                        set pattern "/*.f4v"
                    next
                    edit 3
                        set pattern "/*.mp4"
                    next
                    edit 4
                        set pattern "/*.m4v"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://cnn/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "cnn-vh.akamaihd.net"
        set response-expires enable
        config rules
            edit "rule1"
                config match-entries
                    edit 1
                        set pattern "/*.flv*"
                    next
                    edit 2
                        set pattern "*Seg*"
                    next
                    edit 3
                        set pattern "*Frag*"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
            edit "rule2"
                config match-entries
                    edit 1
                        set pattern "/*.mp4*"
                    next
                    edit 2
                        set pattern "*Seg*"
                    next
                    edit 3
                        set pattern "*Frag*"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
            edit "rule3"
                config match-entries
                    edit 1
                        set pattern "/*.ts*"
                    next
                    edit 2
                        set pattern "*Seg*"
                    next
                    edit 3
                        set pattern "*Frag*"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://foxnews/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "foxnews.com" "foxnews-f.akamaihd.net"
        set response-expires enable
        config rules
            edit "rule1"
                config match-entries
                    edit 1
                        set pattern "/*.mp4*"
                    next
                    edit 2
                        set target parameter
                        set pattern "*Seg*"
                    next
                    edit 3
                        set target parameter
                        set pattern "*Frag*"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://discovery/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "discovery.com" "discidevflash-f.akamaihd.net"
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                set skip-rule-mode any
                config match-entries
                    edit 1
                        set pattern "/*.flv"
                    next
                    edit 2
                        set pattern "/*.mp4"
                    next
                    edit 3
                        set pattern "/*.ts"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://liveleak/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "edge.liveleak.com" "cdn.liveleak.com"
        set response-expires enable
        config rules
            edit "rule1"
                config match-entries
                    edit 1
                        set pattern "/*.flv"
                    next
                    edit 2
                        set target parameter
                        set pattern "*seek=0"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
            edit "rule2"
                config match-entries
                    edit 1
                        set pattern "/*.mp4"
                    next
                    edit 2
                        set target parameter
                        set pattern "*seek=0"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
            edit "rule3"
                config match-entries
                    edit 1
                        set pattern "/*.wmv"
                    next
                    edit 2
                        set target parameter
                        set pattern "*seek=0"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://sevenload/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "sevenload.com"
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                config match-entries
                    edit 1
                        set pattern "/*.flv"
                    next
                    edit 2
                        set pattern "/*.mp4"
                    next
                end
                config skip-entries
                    edit 1
                        set target parameter
                        set pattern "aktimeoffset=*"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://stupidvideos/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "stupidvideos.com"
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                set skip-rule-mode any
                config match-entries
                    edit 1
                        set pattern "/*.flv"
                    next
                    edit 2
                        set pattern "/*.mp4"
                    next
                    edit 3
                        set pattern "/*.ts"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://howcast/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "media.howcast.com"
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                config match-entries
                    edit 1
                        set pattern "/*.flv"
                    next
                    edit 2
                        set pattern "/*.mp4"
                    next
                end
                config skip-entries
                    edit 1
                        set target parameter
                        set pattern "start=*"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://vevo/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "vevo.com"
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                set skip-rule-mode any
                config match-entries
                    edit 1
                        set pattern "/*.flv"
                    next
                    edit 2
                        set pattern "/*.mp4"
                    next
                    edit 3
                        set pattern "/*.ts"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://ooyala/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "ooyala.com"
        set response-expires enable
        config rules
            edit "rule1"
                config match-entries
                    edit 1
                        set pattern "*Seg*"
                    next
                    edit 2
                        set pattern "*Frag*"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://ms-ads/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "msads.net"
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                set skip-rule-mode any
                config match-entries
                    edit 1
                        set pattern "/*.flv"
                    next
                    edit 2
                        set pattern "/*.mp4"
                    next
                    edit 3
                        set pattern "/*.ts"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://yumenetworks-ads/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "yumenetworks.com"
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                set skip-rule-mode any
                config match-entries
                    edit 1
                        set pattern "/*.flv"
                    next
                    edit 2
                        set pattern "/*.mp4"
                    next
                    edit 3
                        set pattern "/*.ts"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://2mdn-ads/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "2mdn.net"
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                set skip-rule-mode any
                config match-entries
                    edit 1
                        set pattern "/*.flv"
                    next
                    edit 2
                        set pattern "/*.mp4"
                    next
                    edit 3
                        set pattern "/*.ts"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://eyewonder-ads/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "eyewonder.com"
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                set skip-rule-mode any
                config match-entries
                    edit 1
                        set pattern "/*.flv"
                    next
                    edit 2
                        set pattern "/*.mp4"
                    next
                    edit 3
                        set pattern "/*.ts"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://eyereturn-ads/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "eyereturn.com"
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                set skip-rule-mode any
                config match-entries
                    edit 1
                        set pattern "/*.flv"
                    next
                    edit 2
                        set pattern "/*.mp4"
                    next
                    edit 3
                        set pattern "/*.ts"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://serving-sys-ads/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "serving-sys.com"
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                set skip-rule-mode any
                config match-entries
                    edit 1
                        set pattern "/*.flv"
                    next
                    edit 2
                        set pattern "/*.mp4"
                    next
                    edit 3
                        set pattern "/*.ts"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://amazonaws-ads/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "amazonaws.com"
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                set skip-rule-mode any
                config match-entries
                    edit 1
                        set pattern "/*.flv"
                    next
                    edit 2
                        set pattern "/*.mp4"
                    next
                    edit 3
                        set pattern "/*.ts"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://edgesuite-ads/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "edgesuite.net"
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                set skip-rule-mode any
                config match-entries
                    edit 1
                        set pattern "/*.flv"
                    next
                    edit 2
                        set pattern "/*.mp4"
                    next
                    edit 3
                        set pattern "/*.ts"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://gorillanation-ads/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "video.gorillanation.com"
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                set skip-rule-mode any
                config match-entries
                    edit 1
                        set pattern "/*.flv"
                    next
                    edit 2
                        set pattern "/*.mp4"
                    next
                    edit 3
                        set pattern "/*.ts"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://youku/"
        set comment "Static entries are not allowed to change except disable."
        set response-expires enable
        config rules
            edit "rule1"
                config match-entries
                    edit 1
                        set pattern "/youku/*.mp4"
                    next
                    edit 2
                        set target parameter
                        set pattern "*start=0"
                    next
                end
                config content-id
                    set target youku-id
                    set start-str "/"
                    set start-skip 1
                    set start-direction backward
                end
            next
            edit "rule2"
                config match-entries
                    edit 1
                        set pattern "/youku/*.flv"
                    next
                    edit 2
                        set target parameter
                        set pattern "*start=0"
                    next
                end
                config content-id
                    set target youku-id
                    set start-str "/"
                    set start-skip 1
                    set start-direction backward
                end
            next
            edit "rule3"
                config match-entries
                    edit 1
                        set pattern "/youku/*.kux"
                    next
                    edit 2
                        set target parameter
                        set pattern "*start=0"
                    next
                end
                config content-id
                    set target youku-id
                    set start-str "/"
                    set start-skip 1
                    set start-direction backward
                end
            next
            edit "rule4"
                config match-entries
                    edit 1
                        set pattern "/youku/*.mp4"
                    next
                end
                config skip-entries
                    edit 1
                        set target parameter
                        set pattern "*start=*"
                    next
                end
                config content-id
                    set target youku-id
                    set start-str "/"
                    set start-skip 1
                    set start-direction backward
                end
            next
            edit "rule5"
                config match-entries
                    edit 1
                        set pattern "/youku/*.flv"
                    next
                end
                config skip-entries
                    edit 1
                        set target parameter
                        set pattern "*start=*"
                    next
                end
                config content-id
                    set target youku-id
                    set start-str "/"
                    set start-skip 1
                    set start-direction backward
                end
            next
            edit "rule6"
                config match-entries
                    edit 1
                        set pattern "/youku/*.kux"
                    next
                end
                config skip-entries
                    edit 1
                        set target parameter
                        set pattern "*start=*"
                    next
                end
                config content-id
                    set target youku-id
                    set start-str "/"
                    set start-skip 1
                    set start-direction backward
                end
            next
        end
    next
    edit "vcache://tudou/"
        set comment "Static entries are not allowed to change except disable."
        set response-expires enable
        config rules
            edit "rule1"
                config match-entries
                    edit 1
                        set pattern "/f4v/*"
                    next
                    edit 2
                        set target parameter
                        set pattern "*id=tudou*"
                    next
                end
                config skip-entries
                    edit 1
                        set target parameter
                        set pattern "*begin=*"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                    set start-direction backward
                end
            next
        end
    next
    edit "vcache://cbc/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "cbc.ca" "mobilehls-vh.akamaihd.net"
        set response-expires enable
        config rules
            edit "rule1"
                config match-entries
                    edit 1
                        set pattern "*.mp4*"
                    next
                    edit 2
                        set pattern "*Seg*"
                    next
                    edit 3
                        set pattern "*Frag*"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
            edit "rule2"
                set match-mode any
                config match-entries
                    edit 1
                        set pattern "*.ts"
                    next
                    edit 2
                        set pattern "*.mp4"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://megaupload/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "megaupload.com"
        set response-expires enable
        config rules
            edit "rule1"
                config match-entries
                    edit 1
                        set pattern "/files/*"
                    next
                end
                config content-id
                    set target referrer
                    set start-str "d="
                    set start-skip 2
                end
            next
        end
    next
    edit "update://windowsupdate/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "download.windowsupdate.com"
        set request-cache-control enable
        set response-cache-control enable
        set response-expires enable
        set updateserver enable
    next
end
config log syslogd setting
    set status enable
    set server "**********"
end
config log syslogd2 setting
    set status enable
    set server "************"
end
config log fortiguard setting
    set status enable
end
config system standalone-cluster
    config cluster-peer
    end
end
config system fortiguard
    set service-account-id "<EMAIL>"
end
config endpoint-control fctems
    edit 1
    next
    edit 2
    next
    edit 3
    next
    edit 4
    next
    edit 5
    next
    edit 6
    next
    edit 7
    next
end
config system email-server
    set server "fortinet-notifications.com"
    set port 465
    set security smtps
end
config system session-helper
    edit 1
        set name pptp
        set protocol 6
        set port 1723
    next
    edit 2
        set name h323
        set protocol 6
        set port 1720
    next
    edit 3
        set name ras
        set protocol 17
        set port 1719
    next
    edit 4
        set name tns
        set protocol 6
        set port 1521
    next
    edit 5
        set name tftp
        set protocol 17
        set port 69
    next
    edit 6
        set name rtsp
        set protocol 6
        set port 554
    next
    edit 7
        set name rtsp
        set protocol 6
        set port 7070
    next
    edit 8
        set name rtsp
        set protocol 6
        set port 8554
    next
    edit 9
        set name ftp
        set protocol 6
        set port 21
    next
    edit 10
        set name mms
        set protocol 6
        set port 1863
    next
    edit 11
        set name pmap
        set protocol 6
        set port 111
    next
    edit 12
        set name pmap
        set protocol 17
        set port 111
    next
    edit 13
        set name sip
        set protocol 17
        set port 5060
    next
    edit 14
        set name dns-udp
        set protocol 17
        set port 53
    next
    edit 15
        set name rsh
        set protocol 6
        set port 514
    next
    edit 16
        set name rsh
        set protocol 6
        set port 512
    next
    edit 17
        set name dcerpc
        set protocol 6
        set port 135
    next
    edit 18
        set name dcerpc
        set protocol 17
        set port 135
    next
    edit 19
        set name mgcp
        set protocol 17
        set port 2427
    next
    edit 20
        set name mgcp
        set protocol 17
        set port 2727
    next
end
config system auto-install
    set auto-install-config enable
    set auto-install-image enable
end
config system console
end
config system ntp
    set ntpsync enable
    set syncinterval 1
    set server-mode enable
    set interface "mgmt"
end
config system ftm-push
    set server-cert "Fortinet_GUI_Server"
end
config system automation-trigger
    edit "Compromised Host"
        set description "An incident of compromise has been detected on a host endpoint."
    next
    edit "Any Security Rating Notification"
        set description "A security rating summary report has been generated."
        set event-type security-rating-summary
        set report-type any
    next
    edit "AV & IPS DB update"
        set description "The antivirus and IPS database has been updated."
        set event-type virus-ips-db-updated
    next
    edit "Configuration Change"
        set description "An administrator\'s session that changed a FortiGate\'s configuration has ended."
        set event-type config-change
    next
    edit "Conserve Mode"
        set description "A FortiGate has entered conserve mode due to low memory."
        set event-type low-memory
    next
    edit "HA Failover"
        set description "A HA failover has occurred."
        set event-type ha-failover
    next
    edit "High CPU"
        set description "A FortiGate has high CPU usage."
        set event-type high-cpu
    next
    edit "License Expiry"
        set description "A FortiGate license is near expiration."
        set event-type license-near-expiry
        set license-type any
    next
    edit "Reboot"
        set description "A FortiGate is rebooted."
        set event-type reboot
    next
    edit "Anomaly Logs"
        set description "An anomalous event has occurred."
        set event-type anomaly-logs
    next
    edit "IPS Logs"
        set description "An IPS event has occurred."
        set event-type ips-logs
    next
    edit "SSH Logs"
        set description "An SSH event has occurred."
        set event-type ssh-logs
    next
    edit "Traffic Violation"
        set description "A traffic policy has been violated."
        set event-type traffic-violation
    next
    edit "Virus Logs"
        set description "A virus event has occurred."
        set event-type virus-logs
    next
    edit "Webfilter Violation"
        set description "A webfilter policy has been violated."
        set event-type webfilter-violation
    next
    edit "Admin Login"
        set description "A FortiOS event with specified log ID has occurred."
        set event-type event-log
        set logid 32001
    next
    edit "Incoming Webhook Call"
        set description "An incoming webhook call is received"
        set event-type incoming-webhook
    next
    edit "Weekly Trigger"
        set trigger-type scheduled
        set trigger-frequency weekly
        set trigger-weekday tuesday
        set trigger-hour 10
    next
    edit "FortiAnalyzer Connection Down"
        set description "A FortiAnalyzer connection is down."
        set event-type event-log
        set logid 22902
    next
    edit "Network Down"
        set description "A network connection is down."
        set event-type event-log
        set logid 20099
        config fields
            edit 1
                set name "status"
                set value "DOWN"
            next
        end
    next
    edit "Local Certificate Expiry"
        set description "A local certificate is near expiration."
        set event-type local-cert-near-expiry
    next
    edit "Auto Firmware upgrade"
        set description "Automatic firmware upgrade."
        set event-type event-log
        set logid 22094 22095 32263
    next
end
config system automation-action
    edit "Access Layer Quarantine"
        set description "Quarantine the MAC address on access layer devices (FortiSwitch and FortiAP)."
        set action-type quarantine
    next
    edit "FortiClient Quarantine"
        set description "Use FortiClient EMS to quarantine the endpoint device."
        set action-type quarantine-forticlient
    next
    edit "FortiNAC Quarantine"
        set description "Use FortiNAC to quarantine the endpoint device."
        set action-type quarantine-fortinac
    next
    edit "IP Ban"
        set description "Ban the IP address specified in the automation trigger event."
        set action-type ban-ip
    next
    edit "FortiExplorer Notification"
        set description "Automation action configuration for sending a notification to any FortiExplorer mobile application."
        set action-type fortiexplorer-notification
    next
    edit "Email Notification"
        set description "Send a custom email to the specified recipient(s)."
        set action-type email
        set forticare-email enable
        set email-subject "%%log.logdesc%%"
    next
    edit "CLI Script - System Status"
        set description "Execute a CLI script to return the system status."
        set action-type cli-script
        set script "get system status"
        set accprofile "super_admin_readonly"
    next
    edit "Reboot FortiGate"
        set description "Reboot this FortiGate unit."
        set action-type system-actions
        set system-action reboot
        set minimum-interval 300
    next
    edit "Shutdown FortiGate"
        set description "Shut down this FortiGate unit."
        set action-type system-actions
        set system-action shutdown
    next
    edit "Backup Config Disk"
        set description "Backup the configuration on disk."
        set action-type system-actions
        set system-action backup-config
    next
end
config system automation-stitch
    edit "Network Down"
        set description "Send an email when a network goes down."
        set status disable
        set trigger "Network Down"
        config actions
            edit 1
                set action "Email Notification"
            next
        end
    next
    edit "HA Failover"
        set description "Send an email when a HA failover is detected."
        set status disable
        set trigger "HA Failover"
        config actions
            edit 1
                set action "Email Notification"
            next
        end
    next
    edit "Reboot"
        set description "Send an email when a FortiGate is rebooted."
        set status disable
        set trigger "Reboot"
        config actions
            edit 1
                set action "Email Notification"
            next
        end
    next
    edit "FortiAnalyzer Connection Down"
        set description "Send a email notification when the connection to FortiAnalyzer is lost."
        set status disable
        set trigger "FortiAnalyzer Connection Down"
        config actions
            edit 1
                set action "Email Notification"
            next
        end
    next
    edit "License Expired Notification"
        set description "Send a email notification when a license is near expiration."
        set status disable
        set trigger "License Expiry"
        config actions
            edit 1
                set action "Email Notification"
            next
        end
    next
    edit "Compromised Host Quarantine"
        set description "Quarantine a compromised host on FortiAPs, FortiSwitches, and FortiClient EMS."
        set status disable
        set trigger "Compromised Host"
        config actions
            edit 1
                set action "Access Layer Quarantine"
            next
            edit 2
                set action "FortiClient Quarantine"
            next
        end
    next
    edit "Incoming Webhook Quarantine"
        set description "Quarantine a provided MAC address on FortiAPs, FortiSwitches, and FortiClient EMS using an Incoming Webhook."
        set status disable
        set trigger "Incoming Webhook Call"
        config actions
            edit 1
                set action "Access Layer Quarantine"
            next
            edit 2
                set action "FortiClient Quarantine"
            next
        end
    next
    edit "Security Rating Notification"
        set description "Send a email notification when a new Security Rating report is available."
        set status disable
        set trigger "Any Security Rating Notification"
        config actions
            edit 1
                set action "Email Notification"
            next
        end
    next
    edit "Firmware upgrade notification"
        set description "Automatic firmware upgrade notification."
        set trigger "Auto Firmware upgrade"
        set condition-logic or
        config actions
            edit 1
                set action "Email Notification"
            next
        end
    next
end
config system federated-upgrade
    set status done
    set source auto-firmware-upgrade
    set upgrade-id 1
    config node-list
        edit "FG4H1FT924902976"
            set timing immediate
            set maximum-minutes 151
            set setup-time 17:28 2025/06/02 UTC
            set upgrade-path 7-4-8
        next
    end
end
config system ike
end
config system ipam
    config pools
        edit "default-pool"
            set subnet ********** ***********
        next
        edit "lan-pool"
            set subnet *********** ***********
        next
    end
    config rules
        edit "role-lan"
            set device "*"
            set interface "*"
            set role lan
            set pool "lan-pool"
            set dhcp enable
        next
    end
end
config system object-tagging
    edit "default"
    next
end
config switch-controller traffic-policy
    edit "quarantine"
        set description "Rate control for quarantined traffic"
        set guaranteed-bandwidth 163840
        set guaranteed-burst 8192
        set maximum-burst 163840
        set cos-queue 0
        set id 1
    next
    edit "sniffer"
        set description "Rate control for sniffer mirrored traffic"
        set guaranteed-bandwidth 50000
        set guaranteed-burst 8192
        set maximum-burst 163840
        set cos-queue 0
        set id 2
    next
end
config system settings
    set gui-dlp-profile enable
end
config system dhcp server
    edit 2
        set dns-service default
        set ntp-service local
        set default-gateway **********
        set netmask *************
        set interface "fortilink"
        config ip-range
            edit 1
                set start-ip **********
                set end-ip **********54
            next
        end
        set vci-match enable
        set vci-string "FortiSwitch" "FortiExtender"
    next
    edit 3
        set dns-service default
        set default-gateway **********
        set netmask *************
        set interface "port7"
        config ip-range
            edit 1
                set start-ip **********
                set end-ip ************
            next
        end
    next
end
config system zone
    edit "tct-zone-1"
        set description "这个区域包含了port5"
        set interface "port5"
    next
end
config firewall address
    edit "EMS_ALL_UNKNOWN_CLIENTS"
        set uuid 7d0bbfd8-2cb1-51f0-162b-bb050e7bf0db
        set type dynamic
        set sub-type ems-tag
        set dirty clean
    next
    edit "EMS_ALL_UNMANAGEABLE_CLIENTS"
        set uuid 7d0bc79e-2cb1-51f0-b259-d52b357ac9e4
        set type dynamic
        set sub-type ems-tag
        set dirty clean
    next
    edit "none"
        set uuid 7c4fa348-2cb1-51f0-19ca-8d2fb26ef1e0
        set subnet 0.0.0.0 ***************
    next
    edit "login.microsoftonline.com"
        set uuid 7c4faa82-2cb1-51f0-aec9-2b103e4cfae7
        set type fqdn
        set fqdn "login.microsoftonline.com"
    next
    edit "login.microsoft.com"
        set uuid 7c4faf64-2cb1-51f0-d372-64a8bd929fad
        set type fqdn
        set fqdn "login.microsoft.com"
    next
    edit "login.windows.net"
        set uuid 7c4fb39c-2cb1-51f0-1488-9e78ec952286
        set type fqdn
        set fqdn "login.windows.net"
    next
    edit "gmail.com"
        set uuid 7c4fb7d4-2cb1-51f0-54f2-a67cc1ccd654
        set type fqdn
        set fqdn "gmail.com"
    next
    edit "wildcard.google.com"
        set uuid 7c4fbbe4-2cb1-51f0-00cd-79536b2f5105
        set type fqdn
        set fqdn "*.google.com"
    next
    edit "wildcard.dropbox.com"
        set uuid 7c4fbffe-2cb1-51f0-0729-73bef1d14173
        set type fqdn
        set fqdn "*.dropbox.com"
    next
    edit "all"
        set uuid 7d0bcd7a-2cb1-51f0-c8c5-7f3901e99928
    next
    edit "FIREWALL_AUTH_PORTAL_ADDRESS"
        set uuid 7d0bcec4-2cb1-51f0-0950-d56fbbf7d241
    next
    edit "FABRIC_DEVICE"
        set uuid 7d0bcfdc-2cb1-51f0-c2d9-52bcca58a318
        set comment "IPv4 addresses of Fabric Devices."
    next
    edit "SSLVPN_TUNNEL_ADDR1"
        set uuid 7d0ca128-2cb1-51f0-d26b-df9d3b6d8ac0
        set type iprange
        set start-ip **************
        set end-ip **************
    next
    edit "port5 address"
        set uuid 62b9ed8c-2cb4-51f0-be2a-d2b875d07f75
        set type interface-subnet
        set subnet 0.0.0.0 ***************
        set interface "port5"
    next
    edit "FCTEMS_ALL_FORTICLOUD_SERVERS"
        set uuid 15da8f04-2cb8-51f0-bb5d-7d62a4c77bd5
        set type dynamic
        set sub-type ems-tag
        set dirty clean
    next
    edit "测试"
        set uuid f34ab4a0-41d9-51f0-76e9-4583c17e15ff
        set subnet ******* ***************
    next
    edit "tct-addr-net-150"
        set uuid 26d9c32e-52f6-51f0-6473-ad1696c88187
        set comment "150.0.0.0-24子网，any接口"
        set subnet 150.0.0.0 *************
    next
    edit "tct-addr-net-160"
        set uuid 4a6903c2-52f6-51f0-2b3e-09c9df357c28
        set comment "160.0.0.0-24子网，any接口"
        set subnet 160.0.0.0 *************
    next
    edit "tct-addr-net-140"
        set uuid 449fe946-531a-51f0-65be-8abaecca21e5
        set comment "140.0.0.0-24子网, any接口"
        set subnet 140.0.0.0 *************
    next
    edit "tct-addr-net-170-1-10"
        set uuid 934a2962-532e-51f0-515c-1d3f1ada6795
        set type iprange
        set comment "*********-*********0范围，any接口"
        set start-ip *********
        set end-ip *********0
    next
    edit "tct-vlan103 address"
        set uuid ba3d1f14-5556-51f0-c455-271b461e7502
        set type interface-subnet
        set comment "接口子网，所属接口：tct-vlan103"
        set subnet 0.0.0.0 ***************
        set interface "tct-vlan103"
    next
    edit "test-vlan1 address"
        set uuid fd5a6dc0-5622-51f0-cc7f-ed55d5f04ded
        set type interface-subnet
        set subnet ************* *************
        set interface "test-vlan1"
    next
    edit "address-test01"
        set uuid d51c61ea-5624-51f0-e98f-30aa37ef5111
        set type iprange
        set comment "地址对象测试"
        set start-ip ************
        set end-ip **************
    next
    edit "test002"
        set uuid 6513e1ba-5625-51f0-ac7d-e3c857c576d8
        set subnet ************* *************
    next
end
config firewall multicast-address
    edit "all"
        set start-ip *********
        set end-ip ***************
    next
    edit "all_hosts"
        set start-ip *********
        set end-ip *********
    next
    edit "all_routers"
        set start-ip *********
        set end-ip *********
    next
    edit "Bonjour"
        set start-ip ***********
        set end-ip ***********
    next
    edit "EIGRP"
        set start-ip *********0
        set end-ip *********0
    next
    edit "OSPF"
        set start-ip *********
        set end-ip *********
    next
end
config firewall address6
    edit "SSLVPN_TUNNEL_IPv6_ADDR1"
        set uuid 7d0ca272-2cb1-51f0-143c-2c3acd5cfe0f
        set ip6 fdff:ffff::/120
    next
    edit "all"
        set uuid 7c4fdaca-2cb1-51f0-5512-85fbb5960c63
    next
    edit "none"
        set uuid 7c4fe038-2cb1-51f0-82ae-d6a6d04141a5
        set ip6 ::/128
    next
end
config firewall multicast-address6
    edit "all"
        set ip6 ff00::/8
    next
end
config firewall addrgrp
    edit "G Suite"
        set uuid 7c4fc54e-2cb1-51f0-c9e9-5fb15c9dda76
        set member "gmail.com" "wildcard.google.com"
    next
    edit "Microsoft Office 365"
        set uuid 7c4fd048-2cb1-51f0-d88b-f5c3bdf6c9f6
        set member "login.microsoftonline.com" "login.microsoft.com" "login.windows.net"
    next
    edit "adg-test1"
        set uuid 876672fa-5625-51f0-1409-ed6f606d9427
        set member "address-test01" "test002"
    next
    edit "adg-test2"
        set uuid a14f2086-5625-51f0-0396-4819dcd6d496
        set member "tct-addr-net-150" "wildcard.dropbox.com" "tct-vlan103 address"
    next
    edit "adg-test3"
        set uuid c9304a44-5625-51f0-325a-244965ed42ce
        set member "测试" "tct-addr-net-150" "tct-addr-net-160" "tct-addr-net-140" "tct-addr-net-170-1-10" "tct-vlan103 address" "test-vlan1 address" "address-test01" "test002"
    next
end
config firewall wildcard-fqdn custom
    edit "adobe"
        set uuid 7c53561e-2cb1-51f0-d8f0-16c404189869
        set wildcard-fqdn "*.adobe.com"
    next
    edit "Adobe Login"
        set uuid 7c535862-2cb1-51f0-5c28-8ae858543d8a
        set wildcard-fqdn "*.adobelogin.com"
    next
    edit "android"
        set uuid 7c535902-2cb1-51f0-bdc7-37229cb8d0fb
        set wildcard-fqdn "*.android.com"
    next
    edit "apple"
        set uuid 7c5359a2-2cb1-51f0-9473-7f3c0494a506
        set wildcard-fqdn "*.apple.com"
    next
    edit "appstore"
        set uuid 7c535a42-2cb1-51f0-f2a9-484bbfc95305
        set wildcard-fqdn "*.appstore.com"
    next
    edit "auth.gfx.ms"
        set uuid 7c535ae2-2cb1-51f0-ca88-73e1f9f7e21f
        set wildcard-fqdn "*.auth.gfx.ms"
    next
    edit "citrix"
        set uuid 7c535b82-2cb1-51f0-13d8-9f88c83a6436
        set wildcard-fqdn "*.citrixonline.com"
    next
    edit "dropbox.com"
        set uuid 7c535c2c-2cb1-51f0-2a5e-30bb04e5388a
        set wildcard-fqdn "*.dropbox.com"
    next
    edit "eease"
        set uuid 7c535cc2-2cb1-51f0-9055-e94fc7d06487
        set wildcard-fqdn "*.eease.com"
    next
    edit "firefox update server"
        set uuid 7c535d62-2cb1-51f0-1cee-90c45c7f927e
        set wildcard-fqdn "aus*.mozilla.org"
    next
    edit "fortinet"
        set uuid 7c535e02-2cb1-51f0-e3b7-49437dd1ba88
        set wildcard-fqdn "*.fortinet.com"
    next
    edit "googleapis.com"
        set uuid 7c535eac-2cb1-51f0-e2aa-26c3cfbee104
        set wildcard-fqdn "*.googleapis.com"
    next
    edit "google-drive"
        set uuid 7c535f4c-2cb1-51f0-bea8-d6e5aacfc5d0
        set wildcard-fqdn "*drive.google.com"
    next
    edit "google-play2"
        set uuid 7c535fe2-2cb1-51f0-3b64-3474bc1f64fd
        set wildcard-fqdn "*.ggpht.com"
    next
    edit "google-play3"
        set uuid 7c536082-2cb1-51f0-fd71-22d52118fdd2
        set wildcard-fqdn "*.books.google.com"
    next
    edit "Gotomeeting"
        set uuid 7c53612c-2cb1-51f0-3e1f-9683339ed75c
        set wildcard-fqdn "*.gotomeeting.com"
    next
    edit "icloud"
        set uuid 7c536258-2cb1-51f0-9030-aaae53f2a400
        set wildcard-fqdn "*.icloud.com"
    next
    edit "itunes"
        set uuid 7c5364a6-2cb1-51f0-9a13-8a7ae9fcc118
        set wildcard-fqdn "*itunes.apple.com"
    next
    edit "microsoft"
        set uuid 7c53655a-2cb1-51f0-9967-50c236d6a3a3
        set wildcard-fqdn "*.microsoft.com"
    next
    edit "skype"
        set uuid 7c5365fa-2cb1-51f0-5293-e232fc32cf42
        set wildcard-fqdn "*.messenger.live.com"
    next
    edit "softwareupdate.vmware.com"
        set uuid 7c53669a-2cb1-51f0-6cfb-46a41ef43508
        set wildcard-fqdn "*.softwareupdate.vmware.com"
    next
    edit "verisign"
        set uuid 7c53673a-2cb1-51f0-a68f-299015bda960
        set wildcard-fqdn "*.verisign.com"
    next
    edit "Windows update 2"
        set uuid 7c5367e4-2cb1-51f0-061a-eb219f8e2935
        set wildcard-fqdn "*.windowsupdate.com"
    next
    edit "live.com"
        set uuid 7c536884-2cb1-51f0-1955-0949d5fbdce4
        set wildcard-fqdn "*.live.com"
    next
    edit "google-play"
        set uuid 7c536924-2cb1-51f0-8c97-c7a9fa62967d
        set wildcard-fqdn "*play.google.com"
    next
    edit "update.microsoft.com"
        set uuid 7c5369ce-2cb1-51f0-0b99-f0bb9b7e6a46
        set wildcard-fqdn "*update.microsoft.com"
    next
    edit "swscan.apple.com"
        set uuid 7c536a6e-2cb1-51f0-6904-aa55739da9d7
        set wildcard-fqdn "*swscan.apple.com"
    next
    edit "autoupdate.opera.com"
        set uuid 7c536b0e-2cb1-51f0-1433-839a52f2b2ae
        set wildcard-fqdn "*autoupdate.opera.com"
    next
    edit "cdn-apple"
        set uuid 7c536bae-2cb1-51f0-77e7-9fc0f6de8bde
        set wildcard-fqdn "*.cdn-apple.com"
    next
    edit "mzstatic-apple"
        set uuid 7c536c58-2cb1-51f0-6300-c5e507610efd
        set wildcard-fqdn "*.mzstatic.com"
    next
end
config firewall service category
    edit "General"
        set comment "General services."
    next
    edit "Web Access"
        set comment "Web access."
    next
    edit "File Access"
        set comment "File access."
    next
    edit "Email"
        set comment "Email services."
    next
    edit "Network Services"
        set comment "Network services."
    next
    edit "Authentication"
        set comment "Authentication service."
    next
    edit "Remote Access"
        set comment "Remote access."
    next
    edit "Tunneling"
        set comment "Tunneling service."
    next
    edit "VoIP, Messaging & Other Applications"
        set comment "VoIP, messaging, and other applications."
    next
    edit "Web Proxy"
        set comment "Explicit web proxy."
    next
end
config firewall service custom
    edit "ALL"
        set uuid 7c524346-2cb1-51f0-f327-185841877021
        set category "General"
        set protocol IP
    next
    edit "FTP"
        set uuid 7c5253ae-2cb1-51f0-3961-3a71cb9e43f0
        set category "File Access"
        set tcp-portrange 21
    next
    edit "FTP_GET"
        set uuid 7c5254d0-2cb1-51f0-8093-5dc9b94bc50f
        set category "File Access"
        set tcp-portrange 21
    next
    edit "FTP_PUT"
        set uuid 7c5255e8-2cb1-51f0-1c05-ef40a258d56d
        set category "File Access"
        set tcp-portrange 21
    next
    edit "ALL_TCP"
        set uuid 7c524652-2cb1-51f0-e11b-96790d856c26
        set category "General"
        set tcp-portrange 1-65535
    next
    edit "ALL_UDP"
        set uuid 7c524792-2cb1-51f0-c556-81f2e3a4b233
        set category "General"
        set udp-portrange 1-65535
    next
    edit "ALL_ICMP"
        set uuid 7c5248aa-2cb1-51f0-e3c6-045ba0da1c57
        set category "General"
        set protocol ICMP
        unset icmptype
    next
    edit "ALL_ICMP6"
        set uuid 7c5249cc-2cb1-51f0-5a0a-f7cb87d2d4c9
        set category "General"
        set protocol ICMP6
        unset icmptype
    next
    edit "GRE"
        set uuid 7c524af8-2cb1-51f0-8c19-ec81e17cb6e6
        set category "Tunneling"
        set protocol IP
        set protocol-number 47
    next
    edit "AH"
        set uuid 7c524c2e-2cb1-51f0-70e9-9eb46d463567
        set category "Tunneling"
        set protocol IP
        set protocol-number 51
    next
    edit "ESP"
        set uuid 7c524d46-2cb1-51f0-95a4-1edb3e8c5f31
        set category "Tunneling"
        set protocol IP
        set protocol-number 50
    next
    edit "AOL"
        set uuid 7c524e68-2cb1-51f0-acb5-097dde11aff8
        set tcp-portrange 5190-5194
    next
    edit "BGP"
        set uuid 7c524f58-2cb1-51f0-7d66-d88a21b02045
        set category "Network Services"
        set tcp-portrange 179
    next
    edit "DHCP"
        set uuid 7c52507a-2cb1-51f0-70b5-76f4bd36b70c
        set category "Network Services"
        set udp-portrange 67-68
    next
    edit "DNS"
        set uuid 7c52519c-2cb1-51f0-6945-ad4cbbb64ad3
        set category "Network Services"
        set tcp-portrange 53
        set udp-portrange 53
    next
    edit "FINGER"
        set uuid 7c5252be-2cb1-51f0-cb35-9eb7eb6e4795
        set tcp-portrange 79
    next
    edit "GOPHER"
        set uuid 7c5257aa-2cb1-51f0-a452-80feb617f3f3
        set tcp-portrange 70
    next
    edit "H323"
        set uuid 7c525a66-2cb1-51f0-e43d-e42bdea540e8
        set category "VoIP, Messaging & Other Applications"
        set tcp-portrange 1720 1503
        set udp-portrange 1719
    next
    edit "HTTP"
        set uuid 7c525b88-2cb1-51f0-50d2-11257fe3363f
        set category "Web Access"
        set tcp-portrange 80
    next
    edit "HTTPS"
        set uuid 7c525caa-2cb1-51f0-c72b-a278f15a934e
        set category "Web Access"
        set tcp-portrange 443
    next
    edit "IKE"
        set uuid 7c525dcc-2cb1-51f0-e1e7-ed6222bbcb6b
        set category "Tunneling"
        set udp-portrange 500 4500
    next
    edit "IMAP"
        set uuid 7c525ef8-2cb1-51f0-d8ad-23250b7bac91
        set category "Email"
        set tcp-portrange 143
    next
    edit "IMAPS"
        set uuid 7c52602e-2cb1-51f0-29cb-6a19622b3a9c
        set category "Email"
        set tcp-portrange 993
    next
    edit "Internet-Locator-Service"
        set uuid 7c526150-2cb1-51f0-5981-fdcb68719e6f
        set tcp-portrange 389
    next
    edit "IRC"
        set uuid 7c526240-2cb1-51f0-622d-db87c46d4d32
        set category "VoIP, Messaging & Other Applications"
        set tcp-portrange 6660-6669
    next
    edit "L2TP"
        set uuid 7c52636c-2cb1-51f0-35aa-f703e8a58165
        set category "Tunneling"
        set tcp-portrange 1701
        set udp-portrange 1701
    next
    edit "LDAP"
        set uuid 7c526498-2cb1-51f0-b332-387c542d920d
        set category "Authentication"
        set tcp-portrange 389
    next
    edit "NetMeeting"
        set uuid 7c5265ba-2cb1-51f0-8050-c16b5003b7eb
        set tcp-portrange 1720
    next
    edit "NFS"
        set uuid 7c5266aa-2cb1-51f0-8159-7091712a6f84
        set category "File Access"
        set tcp-portrange 111 2049
        set udp-portrange 111 2049
    next
    edit "NNTP"
        set uuid 7c5267cc-2cb1-51f0-ea72-9620cf33cd78
        set tcp-portrange 119
    next
    edit "NTP"
        set uuid 7c5268bc-2cb1-51f0-ae4d-a5c5307d6d91
        set category "Network Services"
        set tcp-portrange 123
        set udp-portrange 123
    next
    edit "OSPF"
        set uuid 7c5269e8-2cb1-51f0-c3d4-640c4894de68
        set category "Network Services"
        set protocol IP
        set protocol-number 89
    next
    edit "PC-Anywhere"
        set uuid 7c526b96-2cb1-51f0-e4c7-9d7ab2c81b9d
        set category "Remote Access"
        set tcp-portrange 5631
        set udp-portrange 5632
    next
    edit "PING"
        set uuid 7c526ccc-2cb1-51f0-fca4-f4b6aa81414f
        set category "Network Services"
        set protocol ICMP
        set icmptype 8
        unset icmpcode
    next
    edit "TIMESTAMP"
        set uuid 7c526dee-2cb1-51f0-41c3-8107749dc06f
        set protocol ICMP
        set icmptype 13
        unset icmpcode
    next
    edit "INFO_REQUEST"
        set uuid 7c526f92-2cb1-51f0-6979-def66d5ed2ea
        set protocol ICMP
        set icmptype 15
        unset icmpcode
    next
    edit "INFO_ADDRESS"
        set uuid 7c52708c-2cb1-51f0-a5bc-16e2ead1b342
        set protocol ICMP
        set icmptype 17
        unset icmpcode
    next
    edit "ONC-RPC"
        set uuid 7c527186-2cb1-51f0-8d1b-6c7289813be6
        set category "Remote Access"
        set tcp-portrange 111
        set udp-portrange 111
    next
    edit "DCE-RPC"
        set uuid 7c5272a8-2cb1-51f0-6230-686f38450756
        set category "Remote Access"
        set tcp-portrange 135
        set udp-portrange 135
    next
    edit "POP3"
        set uuid 7c5273d4-2cb1-51f0-9faa-6f719437b1af
        set category "Email"
        set tcp-portrange 110
    next
    edit "POP3S"
        set uuid 7c5274f6-2cb1-51f0-7c71-55bdad1df3a8
        set category "Email"
        set tcp-portrange 995
    next
    edit "PPTP"
        set uuid 7c527622-2cb1-51f0-4104-011af5aeb916
        set category "Tunneling"
        set tcp-portrange 1723
    next
    edit "QUAKE"
        set uuid 7c527744-2cb1-51f0-1c07-25e4614f8eeb
        set udp-portrange 26000 27000 27910 27960
    next
    edit "RAUDIO"
        set uuid 7c52783e-2cb1-51f0-2511-b03960a20656
        set udp-portrange 7070
    next
    edit "REXEC"
        set uuid 7c52792e-2cb1-51f0-7fc1-db397b734207
        set tcp-portrange 512
    next
    edit "RIP"
        set uuid 7c527a1e-2cb1-51f0-fb62-cf6e3f3d6902
        set category "Network Services"
        set udp-portrange 520
    next
    edit "RLOGIN"
        set uuid 7c527b40-2cb1-51f0-b7f4-dff13f77d0b8
        set tcp-portrange 513:512-1023
    next
    edit "RSH"
        set uuid 7c527c3a-2cb1-51f0-c4a8-9bc8e949e1bb
        set tcp-portrange 514:512-1023
    next
    edit "SCCP"
        set uuid 7c527dca-2cb1-51f0-a2b5-3abe3b2327c2
        set category "VoIP, Messaging & Other Applications"
        set tcp-portrange 2000
    next
    edit "SIP"
        set uuid 7c52811c-2cb1-51f0-5dd9-d964bc3a09c1
        set category "VoIP, Messaging & Other Applications"
        set tcp-portrange 5060
        set udp-portrange 5060
    next
    edit "SIP-MSNmessenger"
        set uuid 7c52823e-2cb1-51f0-7860-526513ca3956
        set category "VoIP, Messaging & Other Applications"
        set tcp-portrange 1863
    next
    edit "SAMBA"
        set uuid 7c52836a-2cb1-51f0-5b90-8e49a8997e27
        set category "File Access"
        set tcp-portrange 139
    next
    edit "SMTP"
        set uuid 7c528496-2cb1-51f0-e1d2-b82a97b37d70
        set category "Email"
        set tcp-portrange 25
    next
    edit "SMTPS"
        set uuid 7c5285b8-2cb1-51f0-7a66-11c09bca82cd
        set category "Email"
        set tcp-portrange 465
    next
    edit "SNMP"
        set uuid 7c5286e4-2cb1-51f0-c988-7a63d5b6cd2a
        set category "Network Services"
        set tcp-portrange 161-162
        set udp-portrange 161-162
    next
    edit "SSH"
        set uuid 7c528806-2cb1-51f0-408d-267afe0ff2bc
        set category "Remote Access"
        set tcp-portrange 22
    next
    edit "SYSLOG"
        set uuid 7c528928-2cb1-51f0-466d-61396594e5a0
        set category "Network Services"
        set udp-portrange 514
    next
    edit "TALK"
        set uuid 7c528a54-2cb1-51f0-5651-d3f8adc65180
        set udp-portrange 517-518
    next
    edit "TELNET"
        set uuid 7c528b44-2cb1-51f0-49b7-d8c69822cfe9
        set category "Remote Access"
        set tcp-portrange 23
    next
    edit "TFTP"
        set uuid 7c528c66-2cb1-51f0-cc76-8682c7996728
        set category "File Access"
        set udp-portrange 69
    next
    edit "MGCP"
        set uuid 7c528d88-2cb1-51f0-2541-7ab92eaf25d0
        set category "VoIP, Messaging & Other Applications"
        set tcp-portrange 2428
        set udp-portrange 2427 2727
    next
    edit "UUCP"
        set uuid 7c528eaa-2cb1-51f0-c2c2-86c8c27d25bb
        set tcp-portrange 540
    next
    edit "VDOLIVE"
        set uuid 7c528fa4-2cb1-51f0-9a4d-364e3259b962
        set tcp-portrange 7000-7010
    next
    edit "WAIS"
        set uuid 7c529094-2cb1-51f0-ed4f-b050b77e222a
        set tcp-portrange 210
    next
    edit "WINFRAME"
        set uuid 7c529224-2cb1-51f0-2682-8b6a19594e90
        set tcp-portrange 1494 2598
    next
    edit "X-WINDOWS"
        set uuid 7c529328-2cb1-51f0-295d-61ebf8d2967b
        set category "Remote Access"
        set tcp-portrange 6000-6063
    next
    edit "PING6"
        set uuid 7c529454-2cb1-51f0-d471-5f0a04c0bd97
        set protocol ICMP6
        set icmptype 128
        unset icmpcode
    next
    edit "MS-SQL"
        set uuid 7c52954e-2cb1-51f0-079f-44b8a012fa17
        set category "VoIP, Messaging & Other Applications"
        set tcp-portrange 1433 1434
    next
    edit "MYSQL"
        set uuid 7c529670-2cb1-51f0-b261-69bd2b809471
        set category "VoIP, Messaging & Other Applications"
        set tcp-portrange 3306
    next
    edit "RDP"
        set uuid 7c529792-2cb1-51f0-e294-72726693a344
        set category "Remote Access"
        set tcp-portrange 3389
    next
    edit "VNC"
        set uuid 7c5298b4-2cb1-51f0-915b-1a5de521a0a4
        set category "Remote Access"
        set tcp-portrange 5900
    next
    edit "DHCP6"
        set uuid 7c5299d6-2cb1-51f0-a5de-639ed3ab1545
        set category "Network Services"
        set udp-portrange 546 547
    next
    edit "SQUID"
        set uuid 7c529b02-2cb1-51f0-fbdd-************
        set category "Tunneling"
        set tcp-portrange 3128
    next
    edit "SOCKS"
        set uuid 7c529c24-2cb1-51f0-5303-eb08145a971f
        set category "Tunneling"
        set tcp-portrange 1080
        set udp-portrange 1080
    next
    edit "WINS"
        set uuid 7c529d46-2cb1-51f0-8783-eaddb000c8c1
        set category "Remote Access"
        set tcp-portrange 1512
        set udp-portrange 1512
    next
    edit "RADIUS"
        set uuid 7c529e72-2cb1-51f0-ac27-d4bcb0cede04
        set category "Authentication"
        set udp-portrange 1812 1813
    next
    edit "RADIUS-OLD"
        set uuid 7c529f94-2cb1-51f0-1466-2834801f6d1d
        set udp-portrange 1645 1646
    next
    edit "CVSPSERVER"
        set uuid 7c52a098-2cb1-51f0-b29c-070acc991320
        set tcp-portrange 2401
        set udp-portrange 2401
    next
    edit "AFS3"
        set uuid 7c52a192-2cb1-51f0-f2eb-715b0cafcfec
        set category "File Access"
        set tcp-portrange 7000-7009
        set udp-portrange 7000-7009
    next
    edit "TRACEROUTE"
        set uuid 7c52a2b4-2cb1-51f0-c3c2-01d9ad5e2b1a
        set category "Network Services"
        set udp-portrange 33434-33535
    next
    edit "RTSP"
        set uuid 7c52a480-2cb1-51f0-1d90-87b91dbddbc6
        set category "VoIP, Messaging & Other Applications"
        set tcp-portrange 554 7070 8554
        set udp-portrange 554
    next
    edit "MMS"
        set uuid 7c52a5c0-2cb1-51f0-d59c-8424f9be6013
        set tcp-portrange 1755
        set udp-portrange 1024-5000
    next
    edit "KERBEROS"
        set uuid 7c52a6ba-2cb1-51f0-16b4-9caa33ef64e2
        set category "Authentication"
        set tcp-portrange 88 464
        set udp-portrange 88 464
    next
    edit "LDAP_UDP"
        set uuid 7c52a7e6-2cb1-51f0-ae19-f954168fe56e
        set category "Authentication"
        set udp-portrange 389
    next
    edit "SMB"
        set uuid 7c52a908-2cb1-51f0-abf7-5ccdb5505050
        set category "File Access"
        set tcp-portrange 445
    next
    edit "NONE"
        set uuid 7c52aa2a-2cb1-51f0-f7d8-899d9346b4cf
        set tcp-portrange 0
    next
    edit "webproxy"
        set uuid 7d0b9a30-2cb1-51f0-7896-e92422c2f85a
        set proxy enable
        set category "Web Proxy"
        set protocol ALL
        set tcp-portrange 0-65535:0-65535
    next
    edit "service-test01"
        set uuid 549b7990-5627-51f0-5059-9cde7eb21fdb
        set comment "服务测试001"
        set iprange ************-************00
        set tcp-portrange **************-1024
    next
    edit "service-test02"
        set uuid b5cc5342-5627-51f0-1204-91e7e052f987
        set comment "服务test02"
        set iprange ************-*************
        set tcp-portrange 8080 80 443 8443
    next
end
config firewall service group
    edit "Email Access"
        set uuid 7d0ba5de-2cb1-51f0-d56e-6b7bf4de9d68
        set member "DNS" "IMAP" "IMAPS" "POP3" "POP3S" "SMTP" "SMTPS"
    next
    edit "Web Access"
        set uuid 7d0bae3a-2cb1-51f0-75bc-2083038512bb
        set member "DNS" "HTTP" "HTTPS"
    next
    edit "Windows AD"
        set uuid 7d0bb22c-2cb1-51f0-53f4-06a2c1cf723d
        set member "DCE-RPC" "DNS" "KERBEROS" "LDAP" "LDAP_UDP" "SAMBA" "SMB"
    next
    edit "Exchange Server"
        set uuid 7d0bb812-2cb1-51f0-0650-0196435d173a
        set member "DCE-RPC" "DNS" "HTTPS"
    next
    edit "serviceg-test01"
        set uuid cf1a3e7c-5627-51f0-7adb-a375349b6e9f
        set member "service-test01" "service-test02"
        set comment "服务组测试"
    next
    edit "serviceg-test02"
        set uuid edb7c228-5627-51f0-0724-f9f20f9280dd
        set member "HTTP" "HTTPS" "SMB"
        set comment "服务组测试02"
    next
end
config vpn certificate ca
end
config vpn certificate local
    edit "Fortinet_CA_SSL"
        set password ENC SaVfQqh3Dtzv/GzUi42WcgTzf/1tjHQ8dfTTKLTZ9UGE4Ms4C+zpOnswy4C6Zlge/y25m1lDVBvvTIJsnNZkxHdIcw8ds3ehu/nAs+HJbzFm+YU9RsEVlPf1W+R0eI188ph5VLATbmTP3w/aanM3Lqp8V7FqwWFCMuQR4wt2QedM4gnYaUEVkuJ31yLWxim1g8tIzllmMjY3dkVA
        set comments "This is the default CA certificate the SSL Inspection will use when generating new server certificates."
        set private-key "-----BEGIN ENCRYPTED PRIVATE KEY-----
MIIFJDBWBgkqhkiG9w0BBQ0wSTAxBgkqhkiG9w0BBQwwJAQQNEHfK0ZapuvoD4bm
HqK2QgICCAAwDAYIKoZIhvcNAgkFADAUBggqhkiG9w0DBwQIjUd7fUlJBLgEggTI
04p66SfqmGOSXvA3IvLC28l7bPEIjdF9e3PcLnYlWEfW6x3ajBbfN4jfTmsCelfw
TK2QqNkE9kHHZrPaRYaEkl70C74KnJF761SUh+W31ZwKmYHdPM5D2f4eGPplEJOv
4sfhs/asgpgDTOInn0sR7RsKusd+qTUQJFT5epvxuBEcbI1GFbTCugk71wIkCJRo
6RKzIGdq8HloT1F5spBZIFHRC1NJzpb2q68HXV+/HD310XIqVqlG+uxUWq1TcWIb
vxxvQsaV6UHxfQpJvActjv2KA/L20QO2lw9NCLL28qtlXM6sh8GkDWfWmRKUTGr+
tp1Py9VXJP5Ca4owytpfSS96PGZNfFbLPUcXgr6KiXbarpJHXYMRVOHakZYRlZP0
a3IyPXuBVpuPPwpTcl7WCEv+dAcesds8frND4QQ/pyNj7OqmK+aM+57ruh8AzV/6
KPIOKV2QDZabhDbkujLWm5pWeqTW4/28/QRqKqfSodqDzvVJlcxllX8yK5E76z2J
xcYq6N7KwBq0LK8OHAUMAVgm/ntjWRGBrNlzSDCO+PG4pPGVVP8QL2hz2Ko1NBIS
zBsFieyVzEUifToImJOtZQeeewGRoakTuz3sFU0FTS4HBrWd73NTmoQLGFaIDyL4
QTzZcXYDCDnJZkprkbEPsURaly0Z1E7VjBezxHuOXu1yQUhC+wHTxA++768xG6/X
KeCGrQ//KDrqw7IQ99Qb4on4No59n9fxXsyAqKnilH5NsjAERtdUsb5kAUGevoDy
EYcMddcQDJSDPjc7H87G2nSIJQnNY0G1r185BGUr1Q2Ka9YgrO8Qvfg9hegIc7BV
vk1j5sNBQFpvRrWoKQSYKoYXZyjnyRylBGK+hldNPAi1Eq5Bn8Crh90QuUv/GBMx
yIDyXQkP0MIlXs3Nat3fgy0697M6zLEvB1+/hNuqVpJSVSMcP1T6BquFQGPwLf8Y
PaYBW9he2StSuq5BFLnTqLtc3mKbJx8c+wA78c1JAC6EFiNiJjOM5jXOYRKVt+Cw
FtQ0SzLATZoNcissnAtTjMn/sNAS+6x0apJmR5AWZ3mYsaxA8J3HDnO/vs1kecWZ
tSORjsId5WtxoL8tJhVO6qBNcBueXxfeO31fyZ7Y8qXjSA7sFfdiK+zKFd1ERu0u
ppNKLqUgR6rNBanrE3NrxBg6jtaDvk47yUvYGrwsSHYerkOsIT78+rzwJ2Rutpd0
ezrta/CyvXx6ga1ylUpZCgGJTlHww4we9uqp6SpMBglt5J25ZocjFAoi+De3J0V2
9sZaujcjbZY9dXicz68Mw3NkleZTSSHzT1ruUOEj3wkyFngGv4VNSyVjKK327tUc
2btPIMhnWSfHNqcEZW75SwX19d5CGPxsDwkEyL6k1gIVayCdVFy+UFuRAzwyN/C/
ClWw6rCO5jPLI0HQ4VipS7rQfwlJqLiAhMBUFhwazd0xQFbAAXBtUj4i5+Ts4/a8
RHHyXQU/pFDnLS8MjWSfLm8du1wvfMssEurjShtBtRSLY3oOwaBWiRFnFN5js43V
bOGkMwzwKSg8fkUnSfduVuN7rc4PV7CKEq+jltRzAL4PVRZdi6TGuB27TA7gLIO0
Qi4jYznUsuWnovB1EBCWQFzHqrXiAWQx
-----END ENCRYPTED PRIVATE KEY-----"
        set certificate "-----BEGIN CERTIFICATE-----
MIID8zCCAtugAwIBAgIIclANg9hP/AIwDQYJKoZIhvcNAQELBQAwgakxCzAJBgNV
BAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQHDAlTdW5ueXZhbGUx
ETAPBgNVBAoMCEZvcnRpbmV0MR4wHAYDVQQLDBVDZXJ0aWZpY2F0ZSBBdXRob3Jp
dHkxGTAXBgNVBAMMEEZHNEgxRlQ5MjQ5MDI5NzYxIzAhBgkqhkiG9w0BCQEWFHN1
cHBvcnRAZm9ydGluZXQuY29tMB4XDTI1MDUwOTA4NDIwMloXDTM1MDUxMDA4NDIw
MlowgakxCzAJBgNVBAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQH
DAlTdW5ueXZhbGUxETAPBgNVBAoMCEZvcnRpbmV0MR4wHAYDVQQLDBVDZXJ0aWZp
Y2F0ZSBBdXRob3JpdHkxGTAXBgNVBAMMEEZHNEgxRlQ5MjQ5MDI5NzYxIzAhBgkq
hkiG9w0BCQEWFHN1cHBvcnRAZm9ydGluZXQuY29tMIIBIjANBgkqhkiG9w0BAQEF
AAOCAQ8AMIIBCgKCAQEAvf8oqAUBU18DQ0vtdcvQz134JblaOmYK0zVjM+Hy/iAa
qtxIK1BzgXTzPxOv/PFtTe92TomufUUXuCpuCTNcKkC3paNk/tQpdrOR3bEKdqAV
axstx73eqCG2C9Wpi5L4+sQ01gU26i/hvgau3I97wgpOcck4SO2JdQ+nSW27SvXv
AQVuKNmmhBAGqnsmMpBPV5iKKb8l9udxNRElKzz0D5f+9YkxeBk1yv0OEdK3CUF/
iz1JnUTVdGLc5LP4piZjBNfjVfipG1DpIqTsCJz+oeyx7gmzTFryOGgNwxWteEvA
wfOL4qpNNAREbhUvsnbiZ+p9feArkKUn09zgw8uo0QIDAQABox0wGzAMBgNVHRME
BTADAQH/MAsGA1UdDwQEAwICBDANBgkqhkiG9w0BAQsFAAOCAQEAqdy33TCNu2JR
24Fc711NdlD5msicyLHcPXf+tuUPbL1h9Z1RUKFuMCHIfiy6B7Tk6e8nBfetO6+c
U5TTUY2yNYE6GjgEKQcF3hPua+huytM+z/ItyOjarCTapM5XYT/elCjx9SYSppjJ
lhTmPphOTyVfizT9sPKs6hU6JGmOPKLBXNQirwQRi73CAcPuoBaUawGOivdoXAJc
UUP489J2XhEzdtiNfCorH3mGcJ12uLkMJ1sPoWLe92/X+R+1BheVPV6P+sUI9902
hKADYH5H7qNddC0XTdaFW41mnYQK8hWHwRX6gwAwjTJMmpDJVLH8Ebw3Lr7/X2x1
xCFDtZkpnA==
-----END CERTIFICATE-----"
        set range global
        set source factory
        set last-updated 1746780125
    next
    edit "Fortinet_CA_Untrusted"
        set password ENC 1I1jTPl1glBfasVONIeGnlZ42Sst3X9lQPLc+IDkp22WIFzc6Yl/5+7YmZcTlMU0/PpMa5tddNnJpyAH6jJKsDllnEoxzT0GzIi7+D+37vmJB4OEeNWXNQMmIq4tw5csaQm6YE3DI/PK2z++bGIS7eEWUGwRHGeb6vQ5RbEiowA6W7oN8DOJ6WlMQCBT3bKN0tmZ1FlmMjY3dkVA
        set comments "This is the default CA certificate the SSL Inspection will use when generating new server certificates."
        set private-key "-----BEGIN ENCRYPTED PRIVATE KEY-----
MIIFJDBWBgkqhkiG9w0BBQ0wSTAxBgkqhkiG9w0BBQwwJAQQDPWbh4E2XqXIfV66
Yp/+OwICCAAwDAYIKoZIhvcNAgkFADAUBggqhkiG9w0DBwQIna2uYka3+YoEggTI
Sk759aLTEnVsxRZ6IMcGAjcqZc8wzPyFRkQOa5/x4cF2MsQCtvzlgVwxErASSBDU
4OKCy8U9aynEZBqXLarjbLHRyGsNpYUaDOJWrC0ihJhD1OUJWL2CLaZInBGYtcbg
VVd8mMQLL6jxQViWdtoxMznmhGYzf/j5zzucq3VKqV3WwyGt57ScWrFhhcEoGqDm
QnZkhlBvMN4yW2TsTvgCeXydtben4Hlia05gclS9WasrJ8eBUuB3QXXDS36qwHDh
VqQmRotSlTPID1b5fxDElOqhY3Z14thGbSZoGrZO4dOWldwEtQhbIWQhMwbFRe80
gD1YrgCOR9bzVjkuknOXcqzXxrKdUyTc8Jt4JnurJYuziZKGlhW54OSRaZBlbu2Y
OvgKiDj3EcWtS+4DjiRSc2C8RXjg6liEAoBOWFVlc1kzr+yxf1Pf11RFA9hvGlxF
ny8olUpSxjqfwhRAMn7tEc3BsJ828cs3KpTZk6XmK2j3kLAcGktW8FViMVVTFk1N
RrhAb+4HqhNjLiaMD6GExfp83Bh7qMUJhJZPot3R5laLZX6u53pWDBwa9B1W1Ze5
tNyG6f4Xrr85Su/L1e0DxOaxHLgJyITfOfadvLsaWzC1+4YIiQuYfYPY2l1EN8G9
JQ81DX0ckn7fDrClX0WoyHeewv5Re+Jp0EL68b3AUU23SA6G+NPsAjsbXOn1aZTA
Reh8eDHmir56KtXki83cEY//qDoPDyufINwdtd1FFoczVtzZjDlD98M6qfAi40k3
2Z5ECSAKCutdv04WAC3fFezZOlE5ONBRkIWyUqgDMeysRnms1Iq8Bb2WBrvbipW4
WWXuDgQsTnm5XHuv9Tyx4Ai8DSxV0VAABUEu6NzHcaK8rF+yoNGWN1+jCAwalKMB
LhDYIkjpqA2sh5FmuuzHnYTFsO4kZwzj9Z+g4/thsnouDvMIF+sKYXq9boqKPqQA
4bx2o1ySc0npGLqEzPTE4H006b2S/VDzkVr5yeGnICQlpOeVkfvlUOVEWIWRmzAT
3wdiCkl+bTr67UXHmlh/kNhdzoJckdawvjj6EjwuL2DJD+iqtO0bYt1F1RNfr8c1
SbGbfSUjeTHUVpG6MhY37tmYDXRwnijr91FPq6n8zbmyIEU0Wfmku0K2xlitRpmt
eTo2gexlja4QWDV6oK2Oa7DPfh8U7hnxOI25AJ4qklqchRU9lkhYEZyJSJueAY1g
DbpBkpl0Uv49Psr64dtc6PbDY2Ff5GHIOSTEpIulpadscCWS2yMtOvy8gwVSys5z
GpG/gInCIdODKIxR6pmy8Yvkiq9JXEsuWdCWIRqq+SlRX/mwGwpTS2NH7WcyyhyI
Wz8dvovgk/FZmKPrWxh5Z4uc/amJssbK2upF4xfUMAtNqraiQtqdGSkkCDiYA/KY
bjYmWNiqWgyEd6vP3c8zVttbJ3xfFrCZfDTZ4Umzn7ohOgkAof3V3Ttsg5S62L1n
kSO/QpC4MkpGyYmt+r8/DfT2vp92eBs/ZSzyfSbl1Mz9opFzeSRDDSVm6tWxECEg
D046xHRvvJq7cgiIHdym5dYRvywYe1vxJpDj9nTOmLnxgpFtSFEnjkKr4ZzHkuBd
K07uoIP6tzchRKkyTqrS4I6Awi9hE6Sr
-----END ENCRYPTED PRIVATE KEY-----"
        set certificate "-----BEGIN CERTIFICATE-----
MIID/TCCAuWgAwIBAgIIZjDUrK2g6eIwDQYJKoZIhvcNAQELBQAwga4xCzAJBgNV
BAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQHDAlTdW5ueXZhbGUx
ETAPBgNVBAoMCEZvcnRpbmV0MR4wHAYDVQQLDBVDZXJ0aWZpY2F0ZSBBdXRob3Jp
dHkxHjAcBgNVBAMMFUZvcnRpbmV0IFVudHJ1c3RlZCBDQTEjMCEGCSqGSIb3DQEJ
ARYUc3VwcG9ydEBmb3J0aW5ldC5jb20wHhcNMjUwNTA5MDg0MjAyWhcNMzUwNTEw
MDg0MjAyWjCBrjELMAkGA1UEBhMCVVMxEzARBgNVBAgMCkNhbGlmb3JuaWExEjAQ
BgNVBAcMCVN1bm55dmFsZTERMA8GA1UECgwIRm9ydGluZXQxHjAcBgNVBAsMFUNl
cnRpZmljYXRlIEF1dGhvcml0eTEeMBwGA1UEAwwVRm9ydGluZXQgVW50cnVzdGVk
IENBMSMwIQYJKoZIhvcNAQkBFhRzdXBwb3J0QGZvcnRpbmV0LmNvbTCCASIwDQYJ
KoZIhvcNAQEBBQADggEPADCCAQoCggEBAL3fkCHoJvgWThZgcr1BVxsPn48NoiGg
7+/A5GBmbrYUkASOpvi26mdjYKkm53cEvt1vKxw34dNVel87AgSmHeIJWHIq9ooi
B8Y6Dnrna0fFZm5lothrHozCZoTwMtHUTtVKENs8gwgmOPnHVYkqeIhHAlydkCLI
E0t2FX9XvCi92H6u8ainjHQlaOhoCG7L0iQGI2fn4ihalappk/yT54VI9pZwsfUE
Ely1BIF1RC0IxDo2/B6MMoGLXC2QwH/eXv5edq3+QSPojcimMb5HCu0ko/xaGpJj
oltpDMJ78afi2dgo2FeqAf+BCUJcxFZD1EOOrQCQ3VGLq2o9cxsfnc0CAwEAAaMd
MBswDAYDVR0TBAUwAwEB/zALBgNVHQ8EBAMCAgQwDQYJKoZIhvcNAQELBQADggEB
AKiKi62v8rjs8oTpRBO9TnZdVJPkT3nq3QlyGfOBtjKmLYJwCE1DzYnZ4URTqPVK
MFc1mCskBv1MMi775UsTebUeSeBlmEAMBK+OeRUcBO7RXKnnb0Ckdk5UOHvXVnNi
LOTR4i/mXjjmazJ5QIF4o12GzJl6qLLNpuChWk+Oa18CZ3qdSbTyQRETRTVdIXay
Xa0HVcjbEZ56wXD3n4yMOXGWkV4ZYZNlRkGemgdu9CN2TKHHV0ppmQkqgNFKgzY8
ICHFK++iWEwr6ACGs9/QO+7Wrh7aoAl2HDjhAmbFzwpv5myEWZqZAgZuVzf6xA9z
znyxA/qd5abE0sifBsuQ7J0=
-----END CERTIFICATE-----"
        set range global
        set source factory
        set last-updated 1746780125
    next
    edit "Fortinet_SSL"
        set password ENC 1IndGszAi82Kf1yTGwjnsZiNFU81iPYBksZAS1eL0NwilhhejzejpneMHv0GluQjPQsd24zs8go+fpwINrITVYjDvn0eHR7U7IUD1ifnq5k9q1i8If0H5sI1Wza1kuz9nHH8NnqHnjQxyKxH3gvjCzUps2+mfYiDe/cIQ1NkBmauYeYbYinlgY/iYlj7/h/iz46beFlmMjY3dkVA
        set comments "This certificate is embedded in the hardware at the factory and is unique to this unit. "
        set private-key "-----BEGIN ENCRYPTED PRIVATE KEY-----
MIIFJDBWBgkqhkiG9w0BBQ0wSTAxBgkqhkiG9w0BBQwwJAQQJfgrhxGaHm1jd77b
pOBpIAICCAAwDAYIKoZIhvcNAgkFADAUBggqhkiG9w0DBwQIpU8asrnFQKIEggTI
ahkOW1c6i9qTVdXMbTogKBfTxgOaOAVxFKxQzg2yCzv7xyreiAzsCac70fC1GKkj
mvPCGL/IWVS1d15H2we+H6CM4FwHrigfQ6LOW8Hb9wlSS1tkRzZx4s3N+in+wfLl
yrx0rVRtuQTePdgn3flt1fO+1ezKRaOMUfX/sKO2gRpojwc5RYKwXII+oX2AIfYo
ykqWxhX+xDI4cDrj0ouCLWmW3TqzsIsLctbGR5huTNBeyPBxgPEqUeQarDTf/+x8
VgkFuqy0aXq/ewn8SGVrfM8d/nB+UZ+ofSq5UuDFYQFwgjSSIii7YMi7HrY3BHtf
but4zVRd1NFkONuS7xAQqHEP57ELClXSW3WAaE0G0GmV6FhaGUFTTsL9b/S1MQ6j
STFLCWhgASeGEPuhkbozKODhQuxiS57rxsLvawGlDn08ieytP+Dy2MVW79CTk0zD
v4t9YSsANRJfgrMgJKf9p6S+s64YNkqH+65+vYtCGvk/3PxJRhWQPM+uxlhorFR2
U1BWCZ4zMd/ZdUHZcVdxEJmpBjfioOA8RYzRxCDz5Pf5A3aA72NXGXrOc55qqSyj
ops6H0YFK9JDCA4VBPJMaOtUITA+Opl3X03cZk5m7acYNjX3SPVKRPkJjV/knbr1
vMg5z2Bsru0z7ehRfYbSjkiaOci1aaHsOzQLn0xvYjR91J7IXKkljbqwqX+qS+GH
yHpHd3lCccY4WT46Ug8411LkUEIuJeKWn0eMigYNbLcXCHSYEsAQPMxy8TScnKGN
E3tO9zJJfXsnRe3EPsvduHFpenkP0F++BJ2ZbFwHRzMJ6U+Fqudta4nt3FszNPHr
/YCjgus6ruxMkUdT2/xmZIK6I6ra+mxKrh59cBDlqdBXurnZkuRTj32AQ/ao87IC
PFna1gzOPARvuKNoil7RIIkscy1LMTDd+cZZe+8VuAW4nRCjfjRH1HbMO/T2YTRD
w547fBzF5riQmLJYnbBvUa8CTS+0K4EeGw0cRFUWYH6q2VOw+Cjk5jI/DTzsbjWa
QKbsVCE4jLXZQdS1x6C/Q+4bsooaTwEQwyUNreXMPzgWN11Pz4LcnjEpVKuwcwyG
WLC3sfQjM639pqjbGTD3oi8hyQIBDIaxwTRGtSlHZ2A6jRWE238OSOlrG0Ltc/Jk
3yNB0P/iYKkfDoauupQhVaI1Fno/neWs161zv3piWJ+jt6PVnvmWcwqdUWv/xbnM
/JyskV1KFa9Eb9zLxsyOAJO5VTuAy/RO72letLV+dEqBVXODL/nLMo+ZZ2RaWDoZ
XDboDIoBRRjYCL9BX6f1Of6Qu7tg5FonZA8aSzSq/mGwPEgEZ7vxCRHf695WxEBQ
CvLNrOcTdlt3VvpZGJ38QUEKQO/XeqhmjBmfHTLvRkm6jKNy6ahzeiXgp+a1jJy/
uXwNjYwfTJNo1X7q7FlPIIFYo0wJTwdp+tNOlgXhtDBf9RyDEIz5PmW6OpHak2fa
Wpp0Wu+XxK+hSTrm5kBCmd/3TgvAjL7OExI+XzD8UqYyn6ybufKnmhO6Kkqkpl9N
8y5KM/lwLYyc5Fvs4Ph+c7DwXxkvq/ostFl83kCftEg79xcVv5OQM0EQQLDZ5Ma2
x2LloqM857jkFVhSEc+cmpFEhkIphwE7
-----END ENCRYPTED PRIVATE KEY-----"
        set certificate "-----BEGIN CERTIFICATE-----
MIID4DCCAsigAwIBAgIIKP13xkVCtOYwDQYJKoZIhvcNAQELBQAwgZ0xCzAJBgNV
BAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQHDAlTdW5ueXZhbGUx
ETAPBgNVBAoMCEZvcnRpbmV0MRIwEAYDVQQLDAlGb3J0aUdhdGUxGTAXBgNVBAMM
EEZHNEgxRlQ5MjQ5MDI5NzYxIzAhBgkqhkiG9w0BCQEWFHN1cHBvcnRAZm9ydGlu
ZXQuY29tMB4XDTI1MDUwOTA4NDIwMloXDTI3MDgxMjA4NDIwMlowgZ0xCzAJBgNV
BAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQHDAlTdW5ueXZhbGUx
ETAPBgNVBAoMCEZvcnRpbmV0MRIwEAYDVQQLDAlGb3J0aUdhdGUxGTAXBgNVBAMM
EEZHNEgxRlQ5MjQ5MDI5NzYxIzAhBgkqhkiG9w0BCQEWFHN1cHBvcnRAZm9ydGlu
ZXQuY29tMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAlY3rsVt50qPR
ZS4ADRvUzCWSnMfhX50HHsT5pXQ3EK7Wp+VOWqG7Y9K9xRQnvctw3gDFztu/rZKA
i43r2WFrfaC/SVr8Cni2eMdppnIKGNvzNw155vt95QvKzQisDBRGYodVxD6TQiD+
ZxBpHje9UiGDx5u6l5UluZ5L6s4qa7zGMKme1B7wk8Qj/BASsU3MSswG4dIB/E08
EGyXq5h2A4OX6VcPbnru4Z8OWlWhYkWM7Faz9A+Fr/OFOhK9kouxBIdNPtAZzIG9
fOigZADbg9lQ4AQVhXTXX7Bw4yhRavmSXmqbosfouz7CsRghX36I5uo/Tr82lpSD
JDfNKb4hyQIDAQABoyIwIDAJBgNVHRMEAjAAMBMGA1UdJQQMMAoGCCsGAQUFBwMB
MA0GCSqGSIb3DQEBCwUAA4IBAQCVTvRi+F3QTfbrgq/+RCBsAn2HTRd3K4eJYcMg
uKhq0+KsJAi+G3Xy3iIWGmUUdo+CPKXIOKGD+AYBExpizHODkNcfhjBHGwFpsn6N
wcoL2Fcg6OjaYIAoni5+rTdgmndPmrSt7GGqoW6MxFyd8StPRWPZQ78mA6xthTSN
gHiiEmLJUjs/6cMB9Y81lcZwKHsKMkXOZSENXKtifXO3clBSoZaXg/8KTvofwnAc
tuKLGzT/5MqMwdaab8g8B8ReqoY29wcIfs4ljkvJCB1yoniZS3W/5EtkVItz5tE7
ErOT3J0o0oH7GIKdWkhYx/bksuMVF5P4oTOmYD6BZDs7xevc
-----END CERTIFICATE-----"
        set range global
        set source factory
        set last-updated 1746780125
    next
    edit "Fortinet_GUI_Server"
        set password ENC H4WjuxO9egIsZTtap+4vP4ObAbku3DtwivhVHlynDZnLc5cxMTTzwxT+FlLsdgU1OckDyvF3ASBRU25+maE4RVwpTy5888RibhJ+RhLC1GRLwFLQM8MMj95rrXrBz3pfVdlozC1kghpIWzVNLpp8A2kAL05t5j4L7f7Y/PGwSSOWuuaJOq6OeWd5Y452DycqCzuyYVlmMjY3dkVA
        set comments "This is the default CA certificate the SSL Inspection will use when generating new server certificates."
        set private-key "-----BEGIN ENCRYPTED PRIVATE KEY-----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-----END ENCRYPTED PRIVATE KEY-----"
        set certificate "-----BEGIN CERTIFICATE-----
MIIF4TCCBMmgAwIBAgIIJIsHYDBqpsMwDQYJKoZIhvcNAQELBQAwgakxCzAJBgNV
BAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQHDAlTdW5ueXZhbGUx
ETAPBgNVBAoMCEZvcnRpbmV0MR4wHAYDVQQLDBVDZXJ0aWZpY2F0ZSBBdXRob3Jp
dHkxGTAXBgNVBAMMEEZHNEgxRlQ5MjQ5MDI5NzYxIzAhBgkqhkiG9w0BCQEWFHN1
cHBvcnRAZm9ydGluZXQuY29tMB4XDTI1MDcwMTAyMjgwNFoXDTI3MTAwNDAyMjgw
NFowdjELMAkGA1UEBhMCVVMxEzARBgNVBAgMCkNhbGlmb3JuaWExEjAQBgNVBAcM
CVN1bm55dmFsZTEWMBQGA1UECgwNRm9ydGluZXQgTHRkLjESMBAGA1UECwwJRm9y
dGlHYXRlMRIwEAYDVQQDDAlGb3J0aUdhdGUwggIiMA0GCSqGSIb3DQEBAQUAA4IC
DwAwggIKAoICAQC7QMHOvSuattRbj0w794NtVTguOqlC9PIXoAXOmvsIdHQb1JcP
X3vkW+MImpeG+PO/eJeYHZM1N0YUXBfXYCpCXx+vi3yLBPmJiRSVicSu/M4uepP0
zg7nfdvDorA6pQH3L464H/vkzmgohIr33QVmybwyMAHEtRDuLqY8kkgHOpQV4ZpZ
habwMPRwjb+zP2xRQMNECdQS4rf+EjF8YNOMx0SVclO48lCtt/fQzV/EkIzO3oHl
5706asfDefdLvniZLtqstCc7L7naH4SzDOBFP3uluwM584M4+om8O0ylh12TTGoQ
zyfMdzWqPRQ4ox0JtF+smd8lPaBBbq54L7KKmmegS9zMvM2x3XvnLVjVCfTUyP4W
kmSz0lde54pjAVjQqncFTqNSRQfSD66hZWtiAeTDVMIKWtB8KOvTRV+ncVTyPxDP
nJ4b/80FEPvZLwYaHafqbDg8q8/AtPwgW+t4eJjoEwzghCycIKFcAv1WMqTU7ehr
kLr6PwEuZl8C36OijdXowsKCo8cUe8Hr/SpzSi2m6UjPwQI5zCcLsHuJJalVXmw3
F0fyLfowo61/LqTPwI19fioZPwZTkYaTpXm++ml/iwWb0kYxdsyGPh4V/CqJW86i
eJ3xEnqEpexvlp8w3mavq2gBrHegEh/sOLHcGrjI0c7hi81HfsdccpVuXwIDAQAB
o4IBPTCCATkwCQYDVR0TBAIwADATBgNVHSUEDDAKBggrBgEFBQcDATALBgNVHQ8E
BAMCB4AwHQYDVR0OBBYEFFDdbCQQOr1Z+2Ifolb/rkCz2xNGMIHHBgNVHSMEgb8w
gbyhga+kgawwgakxCzAJBgNVBAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIw
EAYDVQQHDAlTdW5ueXZhbGUxETAPBgNVBAoMCEZvcnRpbmV0MR4wHAYDVQQLDBVD
ZXJ0aWZpY2F0ZSBBdXRob3JpdHkxGTAXBgNVBAMMEEZHNEgxRlQ5MjQ5MDI5NzYx
IzAhBgkqhkiG9w0BCQEWFHN1cHBvcnRAZm9ydGluZXQuY29tgghyUA2D2E/8AjAh
BgNVHREEGjAYhwQKYB4FhwTAqHsBhwRGRkYBhwTAqHwBMA0GCSqGSIb3DQEBCwUA
A4IBAQAyeuxHgivGDkfS2NWT4AksL30mvxkJqZi7LPUvkVLLpiyamk02EIkWzNAk
Fx8V5xnH8HLv7FoDn26MWUt2nt1YKgmxyV6ucbiQFJV2OPXjERMbdfC5Sfg79uWo
BIipr2pWpWKd0D11jhHanZ7XE0zSCXDslLSzUKjaPWrEXxbXnDT1J2y8GxhkMQL4
BHYEH72nDCF25l1J8Rfiz0jFsxpEThU/O0EYtqhuP5hMsMV3dEGOjKbZi+vaED0k
VHIJokk1hLKakO2xMJBpIWStIi4ankK5GYuZ2CU3laVjlkjsE+sQ7mj4npLPtlQs
FiwfbJgDmNBM5DN1D1KuZoPddpWR
-----END CERTIFICATE-----"
        set range global
        set source factory
        set last-updated 1746780125
    next
    edit "Fortinet_SSL_RSA1024"
        set password ENC xZcttGP7ZdYceUxXiWA0MIV6rmkzfz8iK2gQV3aZeys5e4NZpgiAIH4hIjwWozNQylIS2wpfQs5f6VLqXiikTVHHvWd67K09cqjjyGYNGGOBs0heCJSnhG2Rs04KAYFsEe6JR62eGNANHozbnYWWV7C3rgMSV2WJZpxPkCtaBxV4FOts8p6DXmfnaV639SVSZ2ZQ3llmMjY3dkVA
        set comments "This certificate is embedded in the hardware at the factory and is unique to this unit. "
        set private-key "-----BEGIN ENCRYPTED PRIVATE KEY-----
MIIC3DBWBgkqhkiG9w0BBQ0wSTAxBgkqhkiG9w0BBQwwJAQQl1bp2JA3o60c7JrD
Olf1CQICCAAwDAYIKoZIhvcNAgkFADAUBggqhkiG9w0DBwQI/2Aj9Azr/8AEggKA
ieUIkHWnjVX14qLslEovMMZFML7LvT0tgVEf+WUNTYAxnu1NYEJ4dBoxd1d+7j6A
MWKRvZVIbE91MXtYDkybN0h8NSLl5O8Jds62kSp+YtEMT9FmR9fwHRIaQNGbh3np
C8m1T7BpsZw2LCxsFpd3WelUpyfSEAJ2qkL5Xp9ya2uJFFOXn1FAV37J9rD39klN
peRYUTenUS4ngY7Q9WF3w3IArZD6uydXKND/wkVGiLaRyu0eFYJ/AT8PNqoGaH9g
i6354M4APy2xATtKOjgoP8phVIHTGQJWyUEcgKR+tegTYHBoekKGDyisfP8tzdJQ
6krJS71I/WjNdHfc3giyQwXAfTO4wSeAjGPCjg29dEjk2G6KvZFb6yY290hvMOgC
lokHPDROBKDF1X5n1NbSRJTt4JBN7J6jc5GRywa+ypoem3uABTHFeDFJiB0w9huX
D3R/nHDz7Zf+4Dcb8TC3j8Z07Gi79Mb4hmCvP8390xX0RKZDgwkRUhVQlBRMnH7k
7rrR9TmNq5v3g2QQ2MUl0Krcsa3jf4ZCNLzWnLSIdRaiEsZ4Fjut/Fwv5TbWNvPq
XsQJWilgVe8qXYR6T8NTwSkJoJ47bdk+z3MxOTTlrihUo82ekSEVmN9noOYMpN/j
JMieupoRCQL8s19dUTIH6bYROA5klen30uLCsMB+vjweiHtcL19C/H1XUGGjaDy/
FIdI3Uk14sQKQFYhfxnhklp8VlPRmUPc2DYY2Sz+wzrtmxssXiP09Q0K4rJbFWDb
WruxOrKT+eqJ44DP+bIFPdKw0jttMjyZUvaz2ouFE4uLUxF3hE4e+eckmilLVnZf
DIV5uze9V4Tbv+GyzpxCMQ==
-----END ENCRYPTED PRIVATE KEY-----"
        set certificate "-----BEGIN CERTIFICATE-----
MIIC2zCCAkSgAwIBAgIIKKIo3gDrFZ8wDQYJKoZIhvcNAQELBQAwgZ0xCzAJBgNV
BAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQHDAlTdW5ueXZhbGUx
ETAPBgNVBAoMCEZvcnRpbmV0MRIwEAYDVQQLDAlGb3J0aUdhdGUxGTAXBgNVBAMM
EEZHNEgxRlQ5MjQ5MDI5NzYxIzAhBgkqhkiG9w0BCQEWFHN1cHBvcnRAZm9ydGlu
ZXQuY29tMB4XDTI1MDUwOTA4NDIwMloXDTI3MDgxMjA4NDIwMlowgZ0xCzAJBgNV
BAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQHDAlTdW5ueXZhbGUx
ETAPBgNVBAoMCEZvcnRpbmV0MRIwEAYDVQQLDAlGb3J0aUdhdGUxGTAXBgNVBAMM
EEZHNEgxRlQ5MjQ5MDI5NzYxIzAhBgkqhkiG9w0BCQEWFHN1cHBvcnRAZm9ydGlu
ZXQuY29tMIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC+T5tSuPzoTeRduyGf
fsZYrDBCu1t0dHWsaIGYJjBkpKIi1LG9ixZrMqr3OQibOnlXYMfDMYgIrTw7eqUm
bs8XRlnRSXB5cxssaNMlc5ZracqSuLIgb3CVc8Q4BaENeR8Ui3ptBKaVU9TLg8QU
Hc0bdYxg4muz0Suh01g0N0JEKQIDAQABoyIwIDAJBgNVHRMEAjAAMBMGA1UdJQQM
MAoGCCsGAQUFBwMBMA0GCSqGSIb3DQEBCwUAA4GBAKwc+rbO2qzOUsg/iiuLQPsW
+7MHSY1U75NsZl1DLetr0vuM4aM6/OcWbFanyW1eopvlmThL7sgC1ZfkOWGWwwXM
v8zpDMqE3htGFZCf1r8kKz+0iCqLE6ZrPKtbdGPhX0pkCBKNgWROL2C3Qq8V67m6
DWrDvZQ9e8pSg7XBNm/y
-----END CERTIFICATE-----"
        set range global
        set source factory
        set last-updated 1746780125
    next
    edit "Fortinet_SSL_RSA2048"
        set password ENC Vi+f88cjHDRVVJ5jRw9M2dTrsSsDG+Id8CpCBiCPPAXpvDGH2vfB/EOMkbxew++WTCgWQv4pboEBM6J3BAsylUlrSg2WlGjeAa1X4JM21CVy969zuap8N50X/RfjvK9HRD5TRA1vlaErcNrL93gKXB7PSllKW56U9WxWBRw4ux8R5OafOnklAc0qitMbPbavYtfIFllmMjY3dkVA
        set comments "This certificate is embedded in the hardware at the factory and is unique to this unit. "
        set private-key "-----BEGIN ENCRYPTED PRIVATE KEY-----
MIIFJDBWBgkqhkiG9w0BBQ0wSTAxBgkqhkiG9w0BBQwwJAQQwh57jY6UFbc+J2Wv
ZRapPwICCAAwDAYIKoZIhvcNAgkFADAUBggqhkiG9w0DBwQIANw3Med+bjAEggTI
1/vMeG83G+V9XiKBWMzPekHU0bsQT9UlTTv7oDHAdZ7B0kNtw3BnGxzYbvBVKNZ+
PPMgf2g3KrSM+x6/IAqEwpH7bCW0/LYDoMme+NmYU4xzqXo1zULbLh1sQ69fk0jP
7Qff11dIi468Z0Csqx8PNr6XSz3pZC5V5weSfSvCzFiba/MK6mO+zezW1Cvo/9NZ
2EIElhWKehUCjnY/c97OyGJzFJlpW3giSjrobt5Kfxz7ky0OjdBexBmOrA1jhi1o
2OwqN9QYO35TGBLy+Gl6Jbv8HsQgHZdYyyhx2rrvvM+x3UlG2azbeePzF2uG6xxy
HQES5gG7aKRkfUVXQ8YrUGwRD9MCAwj2h1a4ELWTmbh80HvaL9WbnskrMTjrBpiz
vO/XQe3KtAAVGIy9Lg7/bq8a2kCcB5NMHfGRnL+3NHHn9doMeYFFWUpfMYnjPM5Q
3W9EQJPGKORACqetnoMdOuDWsLEcIPFCuD5nv2IykHd6YbOHXk7nqxRj8lLgoStW
AD1ypVjLhTyU1AQstirxv6bh+n6TMNYUhw+uDE/S/U39fB7Og/aXk2Y5ntn+Slks
HSTLMErvXkinICnECjDLIUw/h2C7LtBSf0mhi1L8pFHOlhzvAy3Cufjc3R2Yj6Pm
xxD+xOKXemx0iisOPs4bYmof3DcXFBd1DNo/c6RvoOrNpcwiuHq6BmZnWvwQi1Q+
KgIoXafcNrVKa9Wn49srFJdo/AaLCZyZOwHxvKluQIG/oHphy/VGXgtSE0IkbMSr
oVSDDFfqi4t5qZuyWHtf31P/MydsQz3I32LfyYfOMrx8oigq4cbFe2ErqHXGwAZO
EX35ziKfBwtzhPrvWBi7pTcL4R644DP1+KD+fWxGWCNB79No1HpjiDi87muPFUJ6
0PUxAth+m25pnr+SFrZF8m3sU9UYFGl0ziQCa85NCcRMTQQnoaica8VvWLZH3pRI
Zkm3KW35mKJeBK2BzYAB81ev7PgCewaQlLmmuIBtHzULD3458KJerPF8ZqgS68ad
A7l8OM2ax2PiyNosTuCwyUAMBIRzcgKbnbcZRacaU0vI0Yta+r8a8iRuhxIQzVCh
kMB8mSkdkk6Z13lC/0KfZrRDeQQlvqEKOVC/4C3l2O3SH6zGdYTvazJyoKtyc+Gy
VwMWSssJEGdvJetFbRj3XRaPq8rGBtrRstESKyeNoAGj9tN80ZNHI3eOjp9McnL5
uexIRaXEcLQ9grYQEKUew0ZutvQG/whwwW9T5lTDtGl2dhOAmcRYR+ZoGIDv0rot
DV8/WFLeFtS6FJijLiD1hPxfbUEFScttOek0bhq+R73BJ2JzsiumY7MDFxpzI3bY
MznnmrQBPOyLHEX0Q/I/13SgNadSyzfM1qN1hRhyP+4X8ASU0n0RN7LFJ3045reX
S/cwDbj5qdtu64HhE4sknup9Yu0WXap8dBdK7s3x2kz5mcu3lfEUIQL3kuw48YMr
PJGK2ygVki5bLNEi3vslwwdAUps2zNrxo5iuUxaCen6e2YP54PD2eGzv7YimOSYn
OgxBzmDrGZBx+aul6M3HW004lTT7qBvzxstZer7SqZ5DUku3jzWxG7x3hc4qZbS0
YD2iB7n3EJBXkX2d9i3nUtQT+4YKhb/9
-----END ENCRYPTED PRIVATE KEY-----"
        set certificate "-----BEGIN CERTIFICATE-----
MIID4DCCAsigAwIBAgIIK8qOeGhT86YwDQYJKoZIhvcNAQELBQAwgZ0xCzAJBgNV
BAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQHDAlTdW5ueXZhbGUx
ETAPBgNVBAoMCEZvcnRpbmV0MRIwEAYDVQQLDAlGb3J0aUdhdGUxGTAXBgNVBAMM
EEZHNEgxRlQ5MjQ5MDI5NzYxIzAhBgkqhkiG9w0BCQEWFHN1cHBvcnRAZm9ydGlu
ZXQuY29tMB4XDTI1MDUwOTA4NDIwMloXDTI3MDgxMjA4NDIwMlowgZ0xCzAJBgNV
BAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQHDAlTdW5ueXZhbGUx
ETAPBgNVBAoMCEZvcnRpbmV0MRIwEAYDVQQLDAlGb3J0aUdhdGUxGTAXBgNVBAMM
EEZHNEgxRlQ5MjQ5MDI5NzYxIzAhBgkqhkiG9w0BCQEWFHN1cHBvcnRAZm9ydGlu
ZXQuY29tMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAqz8Kq/j4+ySc
VZVzFVttvuI/3wkuG+yXC9oki/JNQbBU5FI1onphiVa/J+a1kpHPwyJFku6QE4Z7
hrubcikOMjn+oxdtWNt0UC4jCExOzvgvAh/jca+Uku1/zHEi8GelLC5UMkc3Tq05
OxoQJ6hPUnPgKEd2jcD5NqhHYW8ME2weZ3OHljTpt62PFyT5xz0JCw3C3c6R1+M5
FrTkmywH2l919hdRB5xELtcHZXpz4PIdle5nDdkULLt3PkEkYj4BzhzbBpLrdKaV
x6wS6DA2cgc+2CiYj2qkVlntJ10MVdpFkbqc82OAhMk1/uC4qHezQDfFOU7rIfjv
F96cPE9APQIDAQABoyIwIDAJBgNVHRMEAjAAMBMGA1UdJQQMMAoGCCsGAQUFBwMB
MA0GCSqGSIb3DQEBCwUAA4IBAQA78Z1IKoybz5J6ey6GhgA+32XvpdslhrFl/9Vp
GNVg4TNaDe/+HoWeXxhMd8QobiYGGz1xMmn3OPWBgZcinoFCJ9sW5wjwl3vwtsz3
sjyn6vdpfwTWCOIn0O1nRYVDKvIlx1/xy7Wftj3cLT2pv8eNa3yPQUEJdYdIrgwq
0JntmP5H+8l4HAcmGqMk2Lk4uEH32RY2Ipfd1GFijodSPkC+wVodyUXXzMXQrdNo
a2Lzufb9Z/4GuC8KZyTaq5Pdb45CLX5kDFzjiZ/vWlxABR+Ed6LMnfEprreemTNl
vE+GR0tsLE4r5qI6ksGeR+zskv8qqajTfOXJjuuprpIXKuL2
-----END CERTIFICATE-----"
        set range global
        set source factory
        set last-updated 1746780125
    next
    edit "Fortinet_SSL_RSA4096"
        set password ENC GOoRcDxtNc6W+gSwbFhikejhSQfSScwuE2L+loCzyIQNjmtCeTImFcvorlX7e8LFwdisSa9rVO83hhuqy6kB9VI5pMteTp/0DZX7MEI9UvxUmcGWQljCpcXQya23/cn3N2IBcXpFg8e1Tk+hwqaNyvt6J5moRY5folrl+5BE1qEztKPNvstV5DXm5ODH6X8VDVeB/VlmMjY3dkVA
        set comments "This certificate is embedded in the hardware at the factory and is unique to this unit. "
        set private-key "-----BEGIN ENCRYPTED PRIVATE KEY-----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-----END ENCRYPTED PRIVATE KEY-----"
        set certificate "-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----"
        set range global
        set source factory
        set last-updated 1746780125
    next
    edit "Fortinet_SSL_DSA1024"
        set password ENC BnN4+Dm85h8iDLzLrP/PAuxul4CseG5Wecbv7YIjnzK7jdE2ugNliRTSklJxMnUTbXXIbIZ08munWKugU+VZDI5h0VKMzai0XgGygrnrnAMuCZJNeYMyuJmcAF1dbJLgzixdFNGMH1QfRvMOe8SsNCioieSdhnLsctDpPFxElX0bGr1p1KNrlssrYj8Zi0jXupEZwllmMjY3dkVA
        set comments "This certificate is embedded in the hardware at the factory and is unique to this unit. "
        set private-key "-----BEGIN ENCRYPTED PRIVATE KEY-----
MIIBtDBWBgkqhkiG9w0BBQ0wSTAxBgkqhkiG9w0BBQwwJAQQ/13JP5UecUFsA6Bb
+Ziy/QICCAAwDAYIKoZIhvcNAgkFADAUBggqhkiG9w0DBwQIedoe01SMZK0EggFY
hVC1I5Za2fTFkSDE02sjinTluS47C4Dh3e0T/QQfqeX2jL4vwxsLJktg8vEc5QUK
AezuhGcROd1tchT7wFORqhyWs55MO90KaO4Y7nNrr2I+6Bkv7nu7o5cd3COQQtLQ
tiUz7dVTyCXYgmJk80cAl5c/tLf1EVcRvwet5k4X0zyrV1iOyXX09iwxj/PTVTm1
UsjDdIDqpkfEgjWnUj7zbuD+d0yO7RHWO544Jn+5bQ0kaWo5C/EqshaDzSmh7J/e
LDQbtzf3GdiF9tYfuUOlgjhTaxqQNtomFXGl7a9ml/tTc3EnHPzqixj+c+LSemMb
Gn9wsTlCpuwb0Fu7V5FRwO1D1qKsE5LF7QD/psCpI8rbVfwMjFjO7cA+mGn+47p3
KlSGsgrZmqnAQW5by0eZLWSikXeD7xJahoVebp4Lwj9fCPEl+YHQ0vecDNe6cqIz
+jjiMb2OqwM=
-----END ENCRYPTED PRIVATE KEY-----"
        set certificate "-----BEGIN CERTIFICATE-----
MIIDnTCCA1ugAwIBAgIIW2zcpLEPreAwCwYJYIZIAWUDBAMCMIGdMQswCQYDVQQG
EwJVUzETMBEGA1UECAwKQ2FsaWZvcm5pYTESMBAGA1UEBwwJU3Vubnl2YWxlMREw
DwYDVQQKDAhGb3J0aW5ldDESMBAGA1UECwwJRm9ydGlHYXRlMRkwFwYDVQQDDBBG
RzRIMUZUOTI0OTAyOTc2MSMwIQYJKoZIhvcNAQkBFhRzdXBwb3J0QGZvcnRpbmV0
LmNvbTAeFw0yNTA1MDkwODQyMDNaFw0yNzA4MTIwODQyMDNaMIGdMQswCQYDVQQG
EwJVUzETMBEGA1UECAwKQ2FsaWZvcm5pYTESMBAGA1UEBwwJU3Vubnl2YWxlMREw
DwYDVQQKDAhGb3J0aW5ldDESMBAGA1UECwwJRm9ydGlHYXRlMRkwFwYDVQQDDBBG
RzRIMUZUOTI0OTAyOTc2MSMwIQYJKoZIhvcNAQkBFhRzdXBwb3J0QGZvcnRpbmV0
LmNvbTCCAbcwggEsBgcqhkjOOAQBMIIBHwKBgQCc8v6ExckVkFXk0p5syrXFzhx+
750wxGJ5eG5sjmCJvU0kK4K5ra0BebLlZzbBFe/+T7jqXge9wBX3soLrdFM8sEAq
6UdjybOgYrw1SaYbL/ndjTPX8hsrp+xt2M2zPywzBZuVA9Qk08r1EiE14p9hI+dl
8iAqfxMqPKap0zuJtwIVAKIrqaDPVFxBdoNJDXi+JvEMur2LAoGBAJiRaBP8S10s
qFmHHK931ArASTun4u05FLxaNYcJR2GzV4h1EGjWpiB7yXDNnugtrtZpM1EBOTiq
RMt5bEmFNZVLWYt7rsKRihT/WN/VNLbaOB8g/S/g8XqrRyhmZPcuHIaTb25aD+nr
VIgz+B/cnykKk72cGUbulViHwZm0ZI05A4GEAAKBgCaHus5x0qlha1onfR6fgP2W
cX6ec6G7TvWXlrC+27MO41MNoQ1Lau3M8ufXN86Dc0xuH2UvgyWkDSo0Dmqs3gL/
Rf/amoS4mSsGLqC0S+/Z6XAfpvFNBc9WGPpG/Etv5d5b3MpjygBbV5gUniNvihhV
TCTH9cIWewaVaOFOXiz3oyIwIDAJBgNVHRMEAjAAMBMGA1UdJQQMMAoGCCsGAQUF
BwMBMAsGCWCGSAFlAwQDAgMvADAsAhREaUppQB5rAppIPQaAxwJAuqECswIUC4qt
Hx6Y349WSPV/3Hja5VIwYNU=
-----END CERTIFICATE-----"
        set range global
        set source factory
        set last-updated 1746780125
    next
    edit "Fortinet_SSL_DSA2048"
        set password ENC 3yErUE1f19yNkDynHhEp6z/0Af7PMWn0O+K5pj0X2hxGS3lh+AXmMGui3F+//AsMfO8MQu0tkaHmp8U8izzQYr8xrAob2M6clW4VxrUmHZ/+ORtgiXJwwNtQpuUD3WU4b4xp9Jgs7H9KLSJFSJBzH6S+u1oN5wAYdZ7GXCONRvYigz4QCOjGXqhrVQmYIGARYGZ7PllmMjY3dkVA
        set comments "This certificate is embedded in the hardware at the factory and is unique to this unit. "
        set private-key "-----BEGIN ENCRYPTED PRIVATE KEY-----
MIICzDBWBgkqhkiG9w0BBQ0wSTAxBgkqhkiG9w0BBQwwJAQQzyPQi6MP7MqjutBZ
f0I9IAICCAAwDAYIKoZIhvcNAgkFADAUBggqhkiG9w0DBwQIKp500gL6+i0EggJw
vh/v3fdU/vMfJ3we+St5taBnhBcFrQ1rZF0RcXCzBcaqUznbSDplmCZXIJlliiRH
rTWnRfJi4gpDtd4RIhtSoDJOazU2x0cj438H02bEyKvPyLfNtuXoexUCqtJLbUUb
MRBfezfQtx73KRp7Y/3nlnkZC/TPbo2VKQoIuvI7gr2MI6yr9N1/P1a8cGk2LOC2
q9drALJLkNU37NdiyGxExgvLK4c/COA25aRKGYJaB7A4gQTaNm+FaboUYV3wjl56
Dam/Q641yltibawZ23GoxemAIrW78dc6T5fnkIoh2C7yO0yG9Vmn0Y/0EleJ38FV
FhgWsDhHGK2Z/0BhZzW9DmNTh5id+Am5p+bl7KKS/o1jraiTBbnJmFguNj3XQiGa
+kVbJGcD1FKsFkFiiNmbic5wekFRWFdG2jV9P13qmqTg7NQed1GzHmS7FLplg7+c
y3A6i2bOqRv9mnX0PP1m2sGuOnujkE37bhqri8lb32LjeQVpOlbpuMNXeJHb1W+O
xXhg+lFiMAvJn8svsdE/VdwjWTyq483vJ9qd32JW2qO4NOtfzFZvheeR4r9jnjiB
E/jbnhBtPoc8Qr8rz2DCx/P5tg7MwtirEKHBFN1Y1/OFJfiV/+u3a2msUf8EZnhw
lpnb332cs1s8KJx8pFs5m03KDZV4VCbdIt63ZcEFMY6Pwry37wtjxvr46V9gx3RW
PixkgGOVYUu4zj/9eneWQvSk78VmbrepEtjlW+soJ+L1WkrbpRL4aglh1lZoVnGI
c/WyosOxwc3wGdZWaWZ5hoXy7k8P6tvTAyhAYW74D9Lc9vOsur6qiht1g4EF8sY7
-----END ENCRYPTED PRIVATE KEY-----"
        set certificate "-----BEGIN CERTIFICATE-----
MIIFRjCCBOugAwIBAgIIS4mIImXf6XMwCwYJYIZIAWUDBAMCMIGdMQswCQYDVQQG
EwJVUzETMBEGA1UECAwKQ2FsaWZvcm5pYTESMBAGA1UEBwwJU3Vubnl2YWxlMREw
DwYDVQQKDAhGb3J0aW5ldDESMBAGA1UECwwJRm9ydGlHYXRlMRkwFwYDVQQDDBBG
RzRIMUZUOTI0OTAyOTc2MSMwIQYJKoZIhvcNAQkBFhRzdXBwb3J0QGZvcnRpbmV0
LmNvbTAeFw0yNTA1MDkwODQyMDNaFw0yNzA4MTIwODQyMDNaMIGdMQswCQYDVQQG
EwJVUzETMBEGA1UECAwKQ2FsaWZvcm5pYTESMBAGA1UEBwwJU3Vubnl2YWxlMREw
DwYDVQQKDAhGb3J0aW5ldDESMBAGA1UECwwJRm9ydGlHYXRlMRkwFwYDVQQDDBBG
RzRIMUZUOTI0OTAyOTc2MSMwIQYJKoZIhvcNAQkBFhRzdXBwb3J0QGZvcnRpbmV0
LmNvbTCCA0cwggI5BgcqhkjOOAQBMIICLAKCAQEApwfs3iPI9QBuvRx/E0C+JLte
ipr3VfFsLdgaAH02ceQrCbYq6yu8Zw7o0vfOeuFETfbHmqLB+e0C2Y+l6CzEAfJT
UWRwnDgJagx5eUwyXDuthipx3UsEFkeUfe9hdntinXbQg39ken+51EOya9skRlOj
QfmpaoJoXlkldGRoHNiB68dyvZpx4evokH0kvTlpCiPvQkaBeAsty3u7050qjitv
2xzU8GJC9+c9C+UDfpc9R/1uV7rlTDfHEHjqNSUKUQOyZJ0wfChzGc2rktc0BHGp
G62wyePPKOogJZU/0hnknWW8L8D4z4n/5+0MB//9SEZF73oDI6MwYF5UTIsvewIh
AOA6Hv0C/pOYojKHiCSe8PMj4zoiX+LhBNNkMJS2mFWDAoIBAENydC3urJtPEKjb
SbkoaGN8KNN7KRMNgqu2xrALkVNKooU12ZSevb8nJ8CvOuk61fC+q2C6Blj2HkOg
fLJi880eW2gVrMaJQkFptlRQ8J7Mn+kywTfQkKVujq88aw0Z1nmJQJZkHXOpXYNo
R3X1ZL+7uYQEcUs8LTvsEPyH11CMsSaWOYwQgWWj62bwTwLARtQm+GwcPSgqhBHx
46XMUOoI4tzAFn7N4UEwPooIHax9pe2LQw4M+mILz3Btd6UfwSB8rzV+MPveCEo+
IGYg+ToZDGRRXxdkslVzI+QSWJ6laSZv+GTVAe0RNGqqeQgWD1auGwDxaFqI/0O/
lrElp7MDggEGAAKCAQEAg/Wha5rTIC5ve1z7DCEFz3CVdFnC17JRNljrc2Md+0+Q
/dCsjULnRIONrdeZThA0LDC9dA4vc0qWObxTNtjW9hgkY3yfrvR6xaG9a7UunK0B
eLMiz+GnCyhL5WymrHlFGWI0kC88wCkz8phA/uSC1HSEgfiKbfBDZeASQyYzLPnv
MDohwTL1fcKt6waKKoF1GJaIvognSdzFC4AjXr5jTAydcKXZxC0COvZvDRYz3z4c
h7wkNHK0kqeGZhsL6YUb7u1ZfLMt5sllH3eo0RbOYU2PpyongNPxgZsZ2RRLKMv2
ZSRiZsdLRFM/uKqjaSITKEAbYDG8pAZBbsAnKCzevaMiMCAwCQYDVR0TBAIwADAT
BgNVHSUEDDAKBggrBgEFBQcDATALBglghkgBZQMEAwIDSAAwRQIgZwPJxvawdlSg
cejyiAYsyiQQrVLn32BSPqpIV6GKP+UCIQCcyGU1sbQdj5ic3rsmYq5iS8DJsyK2
R0Pe9fYyqP79MQ==
-----END CERTIFICATE-----"
        set range global
        set source factory
        set last-updated 1746780125
    next
    edit "Fortinet_SSL_ECDSA256"
        set password ENC Uqmnrlg+H9XZ3L4FYii1hOOi3bjW07n9ODPol6KMzQ4OH2Dbelh0/OolNNtC7ait3ddZUGHHe4/cG3wTCEFdEeJpbLhoYDqUOj1hC5+gquBIuxymDz2jn5IBk4pRc13B9tAcsV3gpquvZZ9plIf0FhE0YWMZPZueMzdYGVxLP8cC/EaDgekZCtOr9nvnvXCzOG7tpllmMjY3dkVA
        set comments "This certificate is embedded in the hardware at the factory and is unique to this unit. "
        set private-key "-----BEGIN ENCRYPTED PRIVATE KEY-----
MIHrMFYGCSqGSIb3DQEFDTBJMDEGCSqGSIb3DQEFDDAkBBA+BBllYUhrDlH0m3oe
qN+RAgIIADAMBggqhkiG9w0CCQUAMBQGCCqGSIb3DQMHBAizEYbKnOqQRwSBkAPH
0q13g2+BpAVQYWUQZ5FieAeB2b3Z9FMmuqeLUHPrFgSBgWgfZuXx76EpxLRiXqa6
dlszeaIDi4381qRZCHrJzBikzgj2tAOBy9qEKWbZAmIsq0rKlMB7kXrDpS+2u1nu
aMYh8sDDOR7PmGt6m03IQeCO6qVKoBMMwWXi9UEtm/6X+N27eljkIA6NqrewFQ==
-----END ENCRYPTED PRIVATE KEY-----"
        set certificate "-----BEGIN CERTIFICATE-----
MIICVDCCAfqgAwIBAgIIGcca6RkmLU0wCgYIKoZIzj0EAwIwgZ0xCzAJBgNVBAYT
AlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQHDAlTdW5ueXZhbGUxETAP
BgNVBAoMCEZvcnRpbmV0MRIwEAYDVQQLDAlGb3J0aUdhdGUxGTAXBgNVBAMMEEZH
NEgxRlQ5MjQ5MDI5NzYxIzAhBgkqhkiG9w0BCQEWFHN1cHBvcnRAZm9ydGluZXQu
Y29tMB4XDTI1MDUwOTA4NDIwM1oXDTI3MDgxMjA4NDIwM1owgZ0xCzAJBgNVBAYT
AlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQHDAlTdW5ueXZhbGUxETAP
BgNVBAoMCEZvcnRpbmV0MRIwEAYDVQQLDAlGb3J0aUdhdGUxGTAXBgNVBAMMEEZH
NEgxRlQ5MjQ5MDI5NzYxIzAhBgkqhkiG9w0BCQEWFHN1cHBvcnRAZm9ydGluZXQu
Y29tMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE/V0cZeUJ6kw4iquhQFhaSxN1
8wPtU7q9A6hKlR0y4BW9f5RuqoBpn28ZN/1nLYOr/D6k6RwNQUQ2nMO7yNSjpKMi
MCAwCQYDVR0TBAIwADATBgNVHSUEDDAKBggrBgEFBQcDATAKBggqhkjOPQQDAgNI
ADBFAiEAl1g7jUgDQNYhzEhFqU6UuYQlTaVH/w9XTzzUJ+uAt5MCIClmHQvC/6cM
5NAW3vHfeq2YPL6iXU4JOglYvPNelkn5
-----END CERTIFICATE-----"
        set range global
        set source factory
        set last-updated 1746780125
    next
    edit "Fortinet_SSL_ECDSA384"
        set password ENC aV76fw5J4aHes+8P44hUKleqyrnlvWP8NbgbAsWzXyYKWoQjLnSsEx2QaMi71/jcTUS4sogPOZ7sM2nRz3CxTxZCZhxDoBtleaZUHmqlPDOeVh822IIf205lv5H+NIaUR5C7S8oro99lOTdXGg7Uo0SwzAv2p333UfscBBeEJBiZZJOeurQYysstCmKE7qwJzVM2p1lmMjY3dkVA
        set comments "This certificate is embedded in the hardware at the factory and is unique to this unit. "
        set private-key "-----BEGIN ENCRYPTED PRIVATE KEY-----
MIIBGzBWBgkqhkiG9w0BBQ0wSTAxBgkqhkiG9w0BBQwwJAQQXP+WvHtmkKOkS/KX
VV6YBQICCAAwDAYIKoZIhvcNAgkFADAUBggqhkiG9w0DBwQI7kH8mVECMnQEgcAg
bHzKPo266w/cE8+3nwg51Z1LI13ljsMJAatedg+8fY19dkgZ6QJt/fxEY9bdYRzT
9GlZT9hRlRbCFQIsGf/mbE2aaBAhQbcoqzRGuMNhCme6fDdpFnyZsCec42be2Tp5
LNK6PIWa88SphENhMAS56z74A32YKZRUcyFzkAKkt8mPB1wbCubYNNuE24tRDka1
IIXVdn5uoDV5LpT5YZnjrp/iLEsmZZ51Ed8mmR17qT64wu+WZeHwM7hqrt0WXIM=
-----END ENCRYPTED PRIVATE KEY-----"
        set certificate "-----BEGIN CERTIFICATE-----
MIICkjCCAhegAwIBAgIIblI1kAb4uw4wCgYIKoZIzj0EAwIwgZ0xCzAJBgNVBAYT
AlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQHDAlTdW5ueXZhbGUxETAP
BgNVBAoMCEZvcnRpbmV0MRIwEAYDVQQLDAlGb3J0aUdhdGUxGTAXBgNVBAMMEEZH
NEgxRlQ5MjQ5MDI5NzYxIzAhBgkqhkiG9w0BCQEWFHN1cHBvcnRAZm9ydGluZXQu
Y29tMB4XDTI1MDUwOTA4NDIwM1oXDTI3MDgxMjA4NDIwM1owgZ0xCzAJBgNVBAYT
AlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQHDAlTdW5ueXZhbGUxETAP
BgNVBAoMCEZvcnRpbmV0MRIwEAYDVQQLDAlGb3J0aUdhdGUxGTAXBgNVBAMMEEZH
NEgxRlQ5MjQ5MDI5NzYxIzAhBgkqhkiG9w0BCQEWFHN1cHBvcnRAZm9ydGluZXQu
Y29tMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEJwvWKNjUO4bs2nLAQGxJQbKwY3FW
MAjgHIcAR+uBlzxIdiD5+busD8NE+acinCyWGM+DcQkyMzvcvKzGPVHXL19AZxd9
akYfKVSiEOfxhTvNkXEjcb9m0AS87RZfliGeoyIwIDAJBgNVHRMEAjAAMBMGA1Ud
JQQMMAoGCCsGAQUFBwMBMAoGCCqGSM49BAMCA2kAMGYCMQC+gJUTRYzjWiVbslfa
d8ZqM9rJkptYvCa+AwmkFABRmhp6101n0qZTjzcfNC+5JkQCMQD3nCI4dI6ao5Xi
IKpFw73Sttnn4cY2T2NmGCDCXYrwM0uvMbCQXWMLKtRI3OwxlQA=
-----END CERTIFICATE-----"
        set range global
        set source factory
        set last-updated 1746780125
    next
    edit "Fortinet_SSL_ECDSA521"
        set password ENC ttrprIYSSjcFAvqkvDVBgEDlp79iRmJMlfZA5mTJNYTSMEvp/ykLNKwm5nsGZQTXDf+snW78HTIViZO2TEO/GoVD39aIXJ0OoLXlb9fyd3RVso9GqzqAQyzxmmB89Dw8zIxwqkAI15Q1oQBRKTLtxGXWO9HJBlBkoKlvRn0McQXy+tiIeRCD0MSGSRmBfAJr45lLfVlmMjY3dkVA
        set comments "This certificate is embedded in the hardware at the factory and is unique to this unit. "
        set private-key "-----BEGIN ENCRYPTED PRIVATE KEY-----
MIIBUzBWBgkqhkiG9w0BBQ0wSTAxBgkqhkiG9w0BBQwwJAQQaygpA21iSEgew47y
DauhZwICCAAwDAYIKoZIhvcNAgkFADAUBggqhkiG9w0DBwQIL8MFEYcqQgkEgfhF
x+nEyjYuMOm6PoqYfz4RqPg00IOrN0UiXkvycdNsdqYqTOgk4B0vxgCNLkgELDMm
dzFhYcYdBu87zgohGCVAFixfJAXEHJsmvCt/Ymo2zRaoN8641pwgVsotSM3m2fiG
X/1O6jd3Q4BheGJgHNw3ZmuLmUXG6qAdlBs3Rqr68tFI8f18l4S+6v3PnyhTwf4n
dwc+UG28czgtZj6XImPBuf/Kjz2I72oziQrniRNzFjlFpM9BuIT2XIHgirFBytXt
wAoFg9wLuBhznv/M3+prgoiSmPtDSW4yK/umEiBYJ2p1uHrt3CTHtjRi3Rf51UwT
Whqyw3a86w==
-----END ENCRYPTED PRIVATE KEY-----"
        set certificate "-----BEGIN CERTIFICATE-----
MIIC3DCCAj2gAwIBAgIIIahHmDIQHhEwCgYIKoZIzj0EAwIwgZ0xCzAJBgNVBAYT
AlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQHDAlTdW5ueXZhbGUxETAP
BgNVBAoMCEZvcnRpbmV0MRIwEAYDVQQLDAlGb3J0aUdhdGUxGTAXBgNVBAMMEEZH
NEgxRlQ5MjQ5MDI5NzYxIzAhBgkqhkiG9w0BCQEWFHN1cHBvcnRAZm9ydGluZXQu
Y29tMB4XDTI1MDUwOTA4NDIwM1oXDTI3MDgxMjA4NDIwM1owgZ0xCzAJBgNVBAYT
AlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQHDAlTdW5ueXZhbGUxETAP
BgNVBAoMCEZvcnRpbmV0MRIwEAYDVQQLDAlGb3J0aUdhdGUxGTAXBgNVBAMMEEZH
NEgxRlQ5MjQ5MDI5NzYxIzAhBgkqhkiG9w0BCQEWFHN1cHBvcnRAZm9ydGluZXQu
Y29tMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQAmGPAnPsV4XYh47mhTiwm4kIV
3/Raq7qAPae9Gjd7y8UWEt5lC+pPicNpcviHBSuATNKoon/5YrE6k1ub/ovyqCQA
HJJltTUQbj5KJG8T19f+sNw0J/EviV1LrEh0mP6vnX3TFuBK+74KG84TeSmomdO8
UgYvCd38fYQSy3rwmwt9UdWjIjAgMAkGA1UdEwQCMAAwEwYDVR0lBAwwCgYIKwYB
BQUHAwEwCgYIKoZIzj0EAwIDgYwAMIGIAkIAyEUzAP1ipbQC99ywGOhAcjarBZ7J
nD/cPIsB08MMF10y6SylarQOLPgAR+uOC9wpWJcOmlykkbvFwGMdie16GXMCQgCu
Ox08pPiNfMBJiDCn4ymnPDAafX5Jybn8iT7F0GwDVKENRDFzNPvVyq01YJzsGvVa
CBQGqdPoheiYgOsOq/ZwGQ==
-----END CERTIFICATE-----"
        set range global
        set source factory
        set last-updated 1746780125
    next
    edit "Fortinet_SSL_ED25519"
        set password ENC Tv+eJrKDzFgWpn6gZYpTgEYDA+pCfy3hlVRitBxBT6mItLN3bsQBgl1iWOkgQGmOukfmQm92Di1QV54B5qpDtEbHYSIJR9cVWE1KAXAAWk1RSBcr5BXM00nzmkzKkGJTQxKoJlGkY9cQ9iDJDGuy7NEyEALMzxQyrbBCrjDsTv2SXKWFoAB0iDCVL1HWliy2OQMBdVlmMjY3dkVA
        set comments "This certificate is embedded in the hardware at the factory and is unique to this unit. "
        set private-key "-----BEGIN ENCRYPTED PRIVATE KEY-----
MIGSMFYGCSqGSIb3DQEFDTBJMDEGCSqGSIb3DQEFDDAkBBCDbAvLLR0nMFL10CbH
QPC4AgIIADAMBggqhkiG9w0CCQUAMBQGCCqGSIb3DQMHBAiyqlDoEnMcTwQ4ZSnw
dvmfqI6uIZn/Aw5ym8ElOjomK1YOHNjsr9nyyBJGfzneO9Ozoww6LEFG67aB/Gd8
f9QqiAQ=
-----END ENCRYPTED PRIVATE KEY-----"
        set certificate "-----BEGIN CERTIFICATE-----
MIICFDCCAcagAwIBAgIIEoeINEa7WvswBQYDK2VwMIGdMQswCQYDVQQGEwJVUzET
MBEGA1UECAwKQ2FsaWZvcm5pYTESMBAGA1UEBwwJU3Vubnl2YWxlMREwDwYDVQQK
DAhGb3J0aW5ldDESMBAGA1UECwwJRm9ydGlHYXRlMRkwFwYDVQQDDBBGRzRIMUZU
OTI0OTAyOTc2MSMwIQYJKoZIhvcNAQkBFhRzdXBwb3J0QGZvcnRpbmV0LmNvbTAe
Fw0yNTA1MDkwODQyMDNaFw0yNzA4MTIwODQyMDNaMIGdMQswCQYDVQQGEwJVUzET
MBEGA1UECAwKQ2FsaWZvcm5pYTESMBAGA1UEBwwJU3Vubnl2YWxlMREwDwYDVQQK
DAhGb3J0aW5ldDESMBAGA1UECwwJRm9ydGlHYXRlMRkwFwYDVQQDDBBGRzRIMUZU
OTI0OTAyOTc2MSMwIQYJKoZIhvcNAQkBFhRzdXBwb3J0QGZvcnRpbmV0LmNvbTAq
MAUGAytlcAMhADvTCq9MuAMQmm/SU0E/zoi+v0lndwCmNm33XPR+ufh6oyIwIDAJ
BgNVHRMEAjAAMBMGA1UdJQQMMAoGCCsGAQUFBwMBMAUGAytlcANBAFfq5CFvZtmX
LcnY1pNq8oOOhGYmWkohqN1WiJhRAEVwRnhRswve+wvCUoiNhLvSRJI5Hml4j7+F
15BFvDfOQAg=
-----END CERTIFICATE-----"
        set range global
        set source factory
        set last-updated 1746780125
    next
    edit "Fortinet_SSL_ED448"
        set password ENC pcjdA3d8ewQnPQMV/WeHPGF5CTh56ftOm3vOGsQXA0PjSrT/fYroXy9IGOIx/L95/DFuIrJUI/Ncw/doNjgM5Eig53ilLLocIa60xV2+qy3pkvqT+DLc9+F+xlpiZ7QRiZ0VsaqzPylD5TVmLgYJTKRilhddMAt00qKWSU2dWYm0Pq68wcmFfcrgYdysrwE1dY76+VlmMjY3dkVA
        set comments "This certificate is embedded in the hardware at the factory and is unique to this unit. "
        set private-key "-----BEGIN ENCRYPTED PRIVATE KEY-----
MIGqMFYGCSqGSIb3DQEFDTBJMDEGCSqGSIb3DQEFDDAkBBAelMTwDnMlvRlUvG8S
nJbEAgIIADAMBggqhkiG9w0CCQUAMBQGCCqGSIb3DQMHBAhaznLADBkMsQRQ2XDP
OFARq6ME4JyInTLlQR6kcQJ5Vrp/JtNso8wZl1pxzP8ad95XydPTX186ucm54DnX
JmvsZXuR9DE4AApqdetz5tGQ1uboOiHBIvxEVBI=
-----END ENCRYPTED PRIVATE KEY-----"
        set certificate "-----BEGIN CERTIFICATE-----
MIICXzCCAd+gAwIBAgIISsBHs4zS8g4wBQYDK2VxMIGdMQswCQYDVQQGEwJVUzET
MBEGA1UECAwKQ2FsaWZvcm5pYTESMBAGA1UEBwwJU3Vubnl2YWxlMREwDwYDVQQK
DAhGb3J0aW5ldDESMBAGA1UECwwJRm9ydGlHYXRlMRkwFwYDVQQDDBBGRzRIMUZU
OTI0OTAyOTc2MSMwIQYJKoZIhvcNAQkBFhRzdXBwb3J0QGZvcnRpbmV0LmNvbTAe
Fw0yNTA1MDkwODQyMDNaFw0yNzA4MTIwODQyMDNaMIGdMQswCQYDVQQGEwJVUzET
MBEGA1UECAwKQ2FsaWZvcm5pYTESMBAGA1UEBwwJU3Vubnl2YWxlMREwDwYDVQQK
DAhGb3J0aW5ldDESMBAGA1UECwwJRm9ydGlHYXRlMRkwFwYDVQQDDBBGRzRIMUZU
OTI0OTAyOTc2MSMwIQYJKoZIhvcNAQkBFhRzdXBwb3J0QGZvcnRpbmV0LmNvbTBD
MAUGAytlcQM6AMH7sLL4DtaQtMJMlwzXuU7zBGwJpC3sO+rP7o3cZKDG9/z+IMhT
4MnEqEXuvq4Mc/6LojSWjNkcAKMiMCAwCQYDVR0TBAIwADATBgNVHSUEDDAKBggr
BgEFBQcDATAFBgMrZXEDcwDHC1qiQFkr5gtlXgGHpQUEoAb8HpjBpLL1F1CUw2Uh
b9GlfDwu36KoeVv2uGQRLhhJPBXdVMMAdIAMsq1CB+nMqZRFAGj5/hMfWW3fPj9S
WQNTgQhNjHcG/VtqIWcQkk2EfJOJlPhbQIfnmqSVc4TFEAA=
-----END CERTIFICATE-----"
        set range global
        set source factory
        set last-updated 1746780125
    next
end
config webfilter ftgd-local-cat
    edit "custom1"
        set id 140
    next
    edit "custom2"
        set id 141
    next
end
config ips sensor
    edit "default"
        set comment "Prevent critical attacks."
        config entries
            edit 1
                set severity medium high critical
            next
        end
    next
    edit "sniffer-profile"
        set comment "Monitor IPS attacks."
        config entries
            edit 1
                set location server client
                set default-status enable
                set status enable
                set log-packet enable
                set action pass
            next
        end
    next
    edit "wifi-default"
        set comment "Default configuration for offloading WiFi traffic."
        config entries
            edit 1
                set location server client
            next
        end
    next
    edit "all_default"
        set comment "All predefined signatures with default setting."
        config entries
            edit 1
            next
        end
    next
    edit "all_default_pass"
        set comment "All predefined signatures with PASS action."
        config entries
            edit 1
                set action pass
            next
        end
    next
    edit "protect_http_server"
        set comment "Protect against HTTP server-side vulnerabilities."
        config entries
            edit 1
                set location server
                set protocol HTTP
            next
        end
    next
    edit "protect_email_server"
        set comment "Protect against email server-side vulnerabilities."
        config entries
            edit 1
                set location server
                set protocol SMTP POP3 IMAP
            next
        end
    next
    edit "protect_client"
        set comment "Protect against client-side vulnerabilities."
        config entries
            edit 1
                set location client
            next
        end
    next
    edit "high_security"
        set comment "Blocks all Critical/High/Medium and some Low severity vulnerabilities"
        set block-malicious-url enable
        config entries
            edit 1
                set severity medium high critical
                set status enable
                set action block
            next
            edit 2
                set severity low
            next
        end
    next
end
config firewall shaper traffic-shaper
    edit "high-priority"
        set maximum-bandwidth 1048576
        set per-policy enable
    next
    edit "medium-priority"
        set maximum-bandwidth 1048576
        set priority medium
        set per-policy enable
    next
    edit "low-priority"
        set maximum-bandwidth 1048576
        set priority low
        set per-policy enable
    next
    edit "guarantee-100kbps"
        set guaranteed-bandwidth 100
        set maximum-bandwidth 1048576
        set per-policy enable
    next
    edit "shared-1M-pipe"
        set maximum-bandwidth 1024
    next
end
config firewall proxy-address
    edit "IPv4-address"
        set uuid 7d0bd6a8-2cb1-51f0-46fe-cd6b0439bd30
        set type host-regex
        set host-regex "^([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])(\\.([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])){3}$"
    next
    edit "IPv6-address"
        set uuid 7d0bd950-2cb1-51f0-fa33-69edaa26162b
        set type host-regex
        set host-regex "^\\[(([0-9a-f]{0,4}:){1,7}[0-9a-f]{1,4})\\]$"
    next
end
config web-proxy global
    set proxy-fqdn "default.fqdn"
end
config application list
    edit "default"
        set comment "Monitor all applications."
        config entries
            edit 1
                set action pass
            next
        end
    next
    edit "sniffer-profile"
        set comment "Monitor all applications."
        unset options
        config entries
            edit 1
                set action pass
            next
        end
    next
    edit "wifi-default"
        set comment "Default configuration for offloading WiFi traffic."
        set deep-app-inspection disable
        config entries
            edit 1
                set action pass
                set log disable
            next
        end
    next
    edit "block-high-risk"
        config entries
            edit 1
                set category 2 6
            next
            edit 2
                set action pass
            next
        end
    next
end
config dlp data-type
    edit "keyword"
        set pattern "built-in"
    next
    edit "regex"
        set pattern "built-in"
    next
    edit "hex"
        set pattern "built-in"
    next
    edit "mip-label"
        set pattern "^[[:xdigit:]]{8}-[[:xdigit:]]{4}-[[:xdigit:]]{4}-[[:xdigit:]]{4}-[[:xdigit:]]{12}$"
        set transform "built-in"
    next
    edit "credit-card"
        set pattern "\\b([2-6]{1}\\d{3})[- ]?(\\d{4})[- ]?(\\d{2})[- ]?(\\d{2})[- ]?(\\d{2,4})\\b"
        set verify "builtin)credit-card"
        set look-back 20
        set transform "\\b\\1[- ]?\\2[- ]?\\3[- ]?\\4[- ]?\\5\\b"
    next
    edit "ssn-us"
        set pattern "\\b(\\d{3})-(\\d{2})-(\\d{4})\\b"
        set verify "(?<!-)\\b(?!666|000|9\\d{2})\\d{3}-(?!00)\\d{2}-(?!0{4})\\d{4}\\b(?!-)"
        set look-back 12
        set transform "\\b\\1-\\2-\\3\\b"
    next
    edit "edm-keyword"
        set pattern ".+"
        set transform "/\\b\\0\\b/i"
    next
end
config dlp filepattern
    edit 1
        set name "builtin-patterns"
        config entries
            edit "*.bat"
            next
            edit "*.com"
            next
            edit "*.dll"
            next
            edit "*.doc"
            next
            edit "*.exe"
            next
            edit "*.gz"
            next
            edit "*.hta"
            next
            edit "*.ppt"
            next
            edit "*.rar"
            next
            edit "*.scr"
            next
            edit "*.tar"
            next
            edit "*.tgz"
            next
            edit "*.vb?"
            next
            edit "*.wps"
            next
            edit "*.xl?"
            next
            edit "*.zip"
            next
            edit "*.pif"
            next
            edit "*.cpl"
            next
        end
    next
    edit 2
        set name "all_executables"
        config entries
            edit "bat"
                set filter-type type
                set file-type bat
            next
            edit "exe"
                set filter-type type
                set file-type exe
            next
            edit "elf"
                set filter-type type
                set file-type elf
            next
            edit "hta"
                set filter-type type
                set file-type hta
            next
        end
    next
end
config dlp sensitivity
    edit "Private"
    next
    edit "Critical"
    next
    edit "Warning"
    next
end
config dlp profile
    edit "default"
        set comment "Default profile."
    next
    edit "sniffer-profile"
        set comment "Log a summary of email and web traffic."
        set summary-proto smtp pop3 imap http-get http-post
    next
    edit "Content_Summary"
        set summary-proto smtp pop3 imap http-get http-post ftp nntp cifs
    next
    edit "Content_Archive"
        set full-archive-proto smtp pop3 imap http-get http-post ftp nntp cifs
        set summary-proto smtp pop3 imap http-get http-post ftp nntp cifs
    next
    edit "Large-File"
        config rule
            edit 1
                set name "Large-File-Filter"
                set proto smtp pop3 imap http-get http-post
                set file-size 5120
                set action log-only
            next
        end
    next
end
config webfilter ips-urlfilter-setting
end
config webfilter ips-urlfilter-setting6
end
config log threat-weight
    config web
        edit 1
            set category 26
            set level high
        next
        edit 2
            set category 61
            set level high
        next
        edit 3
            set category 86
            set level high
        next
        edit 4
            set category 1
            set level medium
        next
        edit 5
            set category 3
            set level medium
        next
        edit 6
            set category 4
            set level medium
        next
        edit 7
            set category 5
            set level medium
        next
        edit 8
            set category 6
            set level medium
        next
        edit 9
            set category 12
            set level medium
        next
        edit 10
            set category 59
            set level medium
        next
        edit 11
            set category 62
            set level medium
        next
        edit 12
            set category 83
            set level medium
        next
        edit 13
            set category 72
        next
        edit 14
            set category 14
        next
        edit 15
            set category 96
            set level medium
        next
    end
    config application
        edit 1
            set category 2
        next
        edit 2
            set category 6
            set level medium
        next
    end
end
config icap profile
    edit "default"
        config icap-headers
            edit 1
                set name "X-Authenticated-User"
                set content "$auth_user_uri"
                set base64-encoding enable
            next
            edit 2
                set name "X-Authenticated-Groups"
                set content "$auth_group_uri"
                set base64-encoding enable
            next
        end
    next
end
config user fortitoken
    edit "FTKMOB548CC7EC0C"
        set license "FTMTRIAL081DABC6"
    next
    edit "FTKMOB549DFAC67C"
        set license "FTMTRIAL081DABC6"
    next
end
config user local
    edit "guest"
        set type password
        set passwd ENC HGw6a9HW5VS3NjgeOXxMas3fWssaUl1kL/lK/rlmkz5DxnfoEIQFxZmYvUXOk7xPbuX1vwN6NFrthcMjHIyfzV0bHccUb0coRP21bLhFysh7KXqvWnj8RJJc0IAX7Esm8yIfdAz8UiBvfHZXjHVh0UC89UE9CiWZ8bL4f6nlY8GVUM4hcZi29HhSItdv7YoXBYyLgllmMjY3dkVA
    next
end
config user setting
    set auth-cert "Fortinet_Factory"
end
config user group
    edit "SSO_Guest_Users"
    next
    edit "Guest-group"
        set member "guest"
    next
end
config vpn ssl web host-check-software
    edit "FortiClient-AV"
        set guid "1A0271D5-3D4F-46DB-0C2C-AB37BA90D9F7"
    next
    edit "FortiClient-FW"
        set type fw
        set guid "528CB157-D384-4593-AAAA-E42DFF111CED"
    next
    edit "FortiClient-AV-Vista"
        set guid "385618A6-2256-708E-3FB9-7E98B93F91F9"
    next
    edit "FortiClient-FW-Vista"
        set type fw
        set guid "006D9983-6839-71D6-14E6-D7AD47ECD682"
    next
    edit "FortiClient5-AV"
        set guid "5EEDDB8C-C27A-6714-3657-DBD811D1F1B7"
    next
    edit "AVG-Internet-Security-AV"
        set guid "17DDD097-36FF-435F-9E1B-52D74245D6BF"
    next
    edit "AVG-Internet-Security-FW"
        set type fw
        set guid "8DECF618-9569-4340-B34A-D78D28969B66"
    next
    edit "AVG-Internet-Security-AV-Vista-Win7"
        set guid "0C939084-9E57-CBDB-EA61-0B0C7F62AF82"
    next
    edit "AVG-Internet-Security-FW-Vista-Win7"
        set type fw
        set guid "34A811A1-D438-CA83-C13E-A23981B1E8F9"
    next
    edit "CA-Anti-Virus"
        set guid "17CFD1EA-56CF-40B5-A06B-BD3A27397C93"
    next
    edit "CA-Internet-Security-AV"
        set guid "6B98D35F-BB76-41C0-876B-A50645ED099A"
    next
    edit "CA-Internet-Security-FW"
        set type fw
        set guid "38102F93-1B6E-4922-90E1-A35D8DC6DAA3"
    next
    edit "CA-Internet-Security-AV-Vista-Win7"
        set guid "3EED0195-0A4B-4EF3-CC4F-4F401BDC245F"
    next
    edit "CA-Internet-Security-FW-Vista-Win7"
        set type fw
        set guid "06D680B0-4024-4FAB-E710-E675E50F6324"
    next
    edit "CA-Personal-Firewall"
        set type fw
        set guid "14CB4B80-8E52-45EA-905E-67C1267B4160"
    next
    edit "F-Secure-Internet-Security-AV"
        set guid "E7512ED5-4245-4B4D-AF3A-382D3F313F15"
    next
    edit "F-Secure-Internet-Security-FW"
        set type fw
        set guid "D4747503-0346-49EB-9262-997542F79BF4"
    next
    edit "F-Secure-Internet-Security-AV-Vista-Win7"
        set guid "15414183-282E-D62C-CA37-EF24860A2F17"
    next
    edit "F-Secure-Internet-Security-FW-Vista-Win7"
        set type fw
        set guid "2D7AC0A6-6241-D774-E168-461178D9686C"
    next
    edit "Kaspersky-AV"
        set guid "2C4D4BC6-0793-4956-A9F9-E252435469C0"
    next
    edit "Kaspersky-FW"
        set type fw
        set guid "2C4D4BC6-0793-4956-A9F9-E252435469C0"
    next
    edit "Kaspersky-AV-Vista-Win7"
        set guid "AE1D740B-8F0F-D137-211D-873D44B3F4AE"
    next
    edit "Kaspersky-FW-Vista-Win7"
        set type fw
        set guid "9626F52E-C560-D06F-0A42-2E08BA60B3D5"
    next
    edit "McAfee-Internet-Security-Suite-AV"
        set guid "84B5EE75-6421-4CDE-A33A-DD43BA9FAD83"
    next
    edit "McAfee-Internet-Security-Suite-FW"
        set type fw
        set guid "94894B63-8C7F-4050-BDA4-813CA00DA3E8"
    next
    edit "McAfee-Internet-Security-Suite-AV-Vista-Win7"
        set guid "*************-3EA7-ABB3-1B136EB04637"
    next
    edit "McAfee-Internet-Security-Suite-FW-Vista-Win7"
        set type fw
        set guid "BE0ED752-0A0B-3FFF-80EC-B2269063014C"
    next
    edit "McAfee-Virus-Scan-Enterprise"
        set guid "918A2B0B-2C60-4016-A4AB-E868DEABF7F0"
    next
    edit "Norton-360-2.0-AV"
        set guid "A5F1BC7C-EA33-4247-961C-0217208396C4"
    next
    edit "Norton-360-2.0-FW"
        set type fw
        set guid "371C0A40-5A0C-4AD2-A6E5-69C02037FBF3"
    next
    edit "Norton-360-3.0-AV"
        set guid "E10A9785-9598-4754-B552-92431C1C35F8"
    next
    edit "Norton-360-3.0-FW"
        set type fw
        set guid "7C21A4C9-F61F-4AC4-B722-A6E19C16F220"
    next
    edit "Norton-Internet-Security-AV"
        set guid "E10A9785-9598-4754-B552-92431C1C35F8"
    next
    edit "Norton-Internet-Security-FW"
        set type fw
        set guid "7C21A4C9-F61F-4AC4-B722-A6E19C16F220"
    next
    edit "Norton-Internet-Security-AV-Vista-Win7"
        set guid "88C95A36-8C3B-2F2C-1B8B-30FCCFDC4855"
    next
    edit "Norton-Internet-Security-FW-Vista-Win7"
        set type fw
        set guid "B0F2DB13-C654-2E74-30D4-99C9310F0F2E"
    next
    edit "Symantec-Endpoint-Protection-AV"
        set guid "FB06448E-52B8-493A-90F3-E43226D3305C"
    next
    edit "Symantec-Endpoint-Protection-FW"
        set type fw
        set guid "BE898FE3-CD0B-4014-85A9-03DB9923DDB6"
    next
    edit "Symantec-Endpoint-Protection-AV-Vista-Win7"
        set guid "88C95A36-8C3B-2F2C-1B8B-30FCCFDC4855"
    next
    edit "Symantec-Endpoint-Protection-FW-Vista-Win7"
        set type fw
        set guid "B0F2DB13-C654-2E74-30D4-99C9310F0F2E"
    next
    edit "Panda-Antivirus+Firewall-2008-AV"
        set guid "EEE2D94A-D4C1-421A-AB2C-2CE8FE51747A"
    next
    edit "Panda-Antivirus+Firewall-2008-FW"
        set type fw
        set guid "7B090DC0-8905-4BAF-8040-FD98A41C8FB8"
    next
    edit "Panda-Internet-Security-AV"
        set guid "4570FB70-5C9E-47E9-B16C-A3A6A06C4BF0"
    next
    edit "Panda-Internet-Security-2006~2007-FW"
        set type fw
        set guid "4570FB70-5C9E-47E9-B16C-A3A6A06C4BF0"
    next
    edit "Panda-Internet-Security-2008~2009-FW"
        set type fw
        set guid "7B090DC0-8905-4BAF-8040-FD98A41C8FB8"
    next
    edit "Sophos-Anti-Virus"
        set guid "3F13C776-3CBE-4DE9-8BF6-09E5183CA2BD"
    next
    edit "Sophos-Enpoint-Secuirty-and-Control-FW"
        set type fw
        set guid "0786E95E-326A-4524-9691-41EF88FB52EA"
    next
    edit "Sophos-Enpoint-Secuirty-and-Control-AV-Vista-Win7"
        set guid "479CCF92-4960-B3E0-7373-BF453B467D2C"
    next
    edit "Sophos-Enpoint-Secuirty-and-Control-FW-Vista-Win7"
        set type fw
        set guid "7FA74EB7-030F-B2B8-582C-1670C5953A57"
    next
    edit "Trend-Micro-AV"
        set guid "7D2296BC-32CC-4519-917E-52E652474AF5"
    next
    edit "Trend-Micro-FW"
        set type fw
        set guid "3E790E9E-6A5D-4303-A7F9-185EC20F3EB6"
    next
    edit "Trend-Micro-AV-Vista-Win7"
        set guid "48929DFC-7A52-A34F-8351-C4DBEDBD9C50"
    next
    edit "Trend-Micro-FW-Vista-Win7"
        set type fw
        set guid "70A91CD9-303D-A217-A80E-6DEE136EDB2B"
    next
    edit "ZoneAlarm-AV"
        set guid "5D467B10-818C-4CAB-9FF7-6893B5B8F3CF"
    next
    edit "ZoneAlarm-FW"
        set type fw
        set guid "829BDA32-94B3-44F4-8446-F8FCFF809F8B"
    next
    edit "ZoneAlarm-AV-Vista-Win7"
        set guid "D61596DF-D219-341C-49B3-AD30538CBC5B"
    next
    edit "ZoneAlarm-FW-Vista-Win7"
        set type fw
        set guid "EE2E17FA-9876-3544-62EC-0405AD5FFB20"
    next
    edit "ESET-Smart-Security-AV"
        set guid "19259FAE-8396-A113-46DB-15B0E7DFA289"
    next
    edit "ESET-Smart-Security-FW"
        set type fw
        set guid "211E1E8B-C9F9-A04B-6D84-BC85190CE5F2"
    next
end
config vpn ssl web portal
    edit "full-access"
        set tunnel-mode enable
        set ipv6-tunnel-mode enable
        set web-mode enable
        set ip-pools "SSLVPN_TUNNEL_ADDR1"
        set ipv6-pools "SSLVPN_TUNNEL_IPv6_ADDR1"
    next
    edit "web-access"
        set web-mode enable
    next
    edit "tunnel-access"
        set tunnel-mode enable
        set ipv6-tunnel-mode enable
        set ip-pools "SSLVPN_TUNNEL_ADDR1"
        set ipv6-pools "SSLVPN_TUNNEL_IPv6_ADDR1"
    next
end
config vpn ssl settings
    set banned-cipher SHA1 SHA256 SHA384
    set servercert ''
    set port 443
end
config voip profile
    edit "default"
        set comment "Default VoIP profile."
        config sip
        end
    next
    edit "strict"
        config sip
            set malformed-request-line discard
            set malformed-header-via discard
            set malformed-header-from discard
            set malformed-header-to discard
            set malformed-header-call-id discard
            set malformed-header-cseq discard
            set malformed-header-rack discard
            set malformed-header-rseq discard
            set malformed-header-contact discard
            set malformed-header-record-route discard
            set malformed-header-route discard
            set malformed-header-expires discard
            set malformed-header-content-type discard
            set malformed-header-content-length discard
            set malformed-header-max-forwards discard
            set malformed-header-allow discard
            set malformed-header-p-asserted-identity discard
            set malformed-header-sdp-v discard
            set malformed-header-sdp-o discard
            set malformed-header-sdp-s discard
            set malformed-header-sdp-i discard
            set malformed-header-sdp-c discard
            set malformed-header-sdp-b discard
            set malformed-header-sdp-z discard
            set malformed-header-sdp-k discard
            set malformed-header-sdp-a discard
            set malformed-header-sdp-t discard
            set malformed-header-sdp-r discard
            set malformed-header-sdp-m discard
        end
    next
end
config system sdwan
    config zone
        edit "virtual-wan-link"
        next
    end
    config health-check
        edit "Default_DNS"
            set system-dns enable
            set interval 1000
            set probe-timeout 1000
            set recoverytime 10
            config sla
                edit 1
                    set latency-threshold 250
                    set jitter-threshold 50
                    set packetloss-threshold 5
                next
            end
        next
        edit "Default_Office_365"
            set server "www.office.com"
            set protocol https
            set interval 120000
            set probe-timeout 120000
            set recoverytime 10
            config sla
                edit 1
                    set latency-threshold 250
                    set jitter-threshold 50
                    set packetloss-threshold 5
                next
            end
        next
        edit "Default_Gmail"
            set server "gmail.com"
            set interval 1000
            set probe-timeout 1000
            set recoverytime 10
            config sla
                edit 1
                    set latency-threshold 250
                    set jitter-threshold 50
                    set packetloss-threshold 2
                next
            end
        next
        edit "Default_Google Search"
            set server "www.google.com"
            set protocol https
            set interval 120000
            set probe-timeout 120000
            set recoverytime 10
            config sla
                edit 1
                    set latency-threshold 250
                    set jitter-threshold 50
                    set packetloss-threshold 5
                next
            end
        next
        edit "Default_FortiGuard"
            set server "fortiguard.com"
            set protocol https
            set interval 120000
            set probe-timeout 120000
            set recoverytime 10
            config sla
                edit 1
                    set latency-threshold 250
                    set jitter-threshold 50
                    set packetloss-threshold 5
                next
            end
        next
    end
end
config dnsfilter profile
    edit "default"
        set comment "Default dns filtering."
        config ftgd-dns
            config filters
                edit 1
                    set category 2
                next
                edit 2
                    set category 7
                next
                edit 3
                    set category 8
                next
                edit 4
                    set category 9
                next
                edit 5
                    set category 11
                next
                edit 6
                    set category 12
                next
                edit 7
                    set category 13
                next
                edit 8
                    set category 14
                next
                edit 9
                    set category 15
                next
                edit 10
                    set category 16
                next
                edit 11
                next
                edit 12
                    set category 57
                next
                edit 13
                    set category 63
                next
                edit 14
                    set category 64
                next
                edit 15
                    set category 65
                next
                edit 16
                    set category 66
                next
                edit 17
                    set category 67
                next
                edit 18
                    set category 26
                    set action block
                next
                edit 19
                    set category 61
                    set action block
                next
                edit 20
                    set category 86
                    set action block
                next
                edit 21
                    set category 88
                    set action block
                next
                edit 22
                    set category 90
                    set action block
                next
                edit 23
                    set category 91
                    set action block
                next
                edit 24
                    set category 96
                    set action block
                next
                edit 25
                    set category 98
                    set action block
                next
                edit 26
                    set category 99
                    set action block
                next
            end
        end
        set block-botnet enable
    next
end
config antivirus settings
    set machine-learning-detection enable
end
config antivirus profile
    edit "default"
        set comment "Scan files and block viruses."
        config http
            set av-scan block
        end
        config ftp
            set av-scan block
        end
        config imap
            set av-scan block
            set executables virus
        end
        config pop3
            set av-scan block
            set executables virus
        end
        config smtp
            set av-scan block
            set executables virus
        end
    next
    edit "sniffer-profile"
        set comment "Scan files and monitor viruses."
        config http
            set av-scan monitor
        end
        config ftp
            set av-scan monitor
        end
        config imap
            set av-scan monitor
            set executables virus
        end
        config pop3
            set av-scan monitor
            set executables virus
        end
        config smtp
            set av-scan monitor
            set executables virus
        end
        config cifs
            set av-scan monitor
        end
    next
    edit "wifi-default"
        set comment "Default configuration for offloading WiFi traffic."
        config http
            set av-scan block
        end
        config ftp
            set av-scan block
        end
        config imap
            set av-scan block
            set executables virus
        end
        config pop3
            set av-scan block
            set executables virus
        end
        config smtp
            set av-scan block
            set executables virus
        end
    next
end
config file-filter profile
    edit "default"
        set comment "File type inspection."
    next
    edit "sniffer-profile"
        set comment "File type inspection."
    next
end
config webfilter profile
    edit "default"
        set comment "Default web filtering."
        config ftgd-wf
            unset options
            config filters
                edit 1
                    set action block
                next
                edit 2
                    set category 2
                    set action block
                next
                edit 3
                    set category 7
                    set action block
                next
                edit 4
                    set category 8
                    set action block
                next
                edit 5
                    set category 9
                    set action block
                next
                edit 6
                    set category 11
                    set action block
                next
                edit 7
                    set category 13
                    set action block
                next
                edit 8
                    set category 14
                    set action block
                next
                edit 9
                    set category 15
                    set action block
                next
                edit 10
                    set category 16
                    set action block
                next
                edit 11
                    set category 26
                    set action block
                next
                edit 12
                    set category 57
                    set action block
                next
                edit 13
                    set category 61
                    set action block
                next
                edit 14
                    set category 63
                    set action block
                next
                edit 15
                    set category 64
                    set action block
                next
                edit 16
                    set category 65
                    set action block
                next
                edit 17
                    set category 66
                    set action block
                next
                edit 18
                    set category 67
                    set action block
                next
                edit 19
                    set category 83
                    set action block
                next
                edit 20
                    set category 86
                    set action block
                next
                edit 21
                    set category 88
                    set action block
                next
                edit 22
                    set category 90
                    set action block
                next
                edit 23
                    set category 91
                    set action block
                next
                edit 24
                    set category 96
                    set action block
                next
                edit 25
                    set category 98
                    set action block
                next
                edit 26
                    set category 99
                    set action block
                next
                edit 27
                    set category 1
                next
                edit 28
                    set category 3
                next
                edit 29
                    set category 4
                next
                edit 30
                    set category 5
                next
                edit 31
                    set category 6
                next
                edit 32
                    set category 12
                next
                edit 33
                    set category 59
                next
                edit 34
                    set category 62
                next
            end
        end
    next
    edit "sniffer-profile"
        set comment "Monitor web traffic."
        config ftgd-wf
            config filters
                edit 1
                next
                edit 2
                    set category 1
                next
                edit 3
                    set category 2
                next
                edit 4
                    set category 3
                next
                edit 5
                    set category 4
                next
                edit 6
                    set category 5
                next
                edit 7
                    set category 6
                next
                edit 8
                    set category 7
                next
                edit 9
                    set category 8
                next
                edit 10
                    set category 9
                next
                edit 11
                    set category 11
                next
                edit 12
                    set category 12
                next
                edit 13
                    set category 13
                next
                edit 14
                    set category 14
                next
                edit 15
                    set category 15
                next
                edit 16
                    set category 16
                next
                edit 17
                    set category 17
                next
                edit 18
                    set category 18
                next
                edit 19
                    set category 19
                next
                edit 20
                    set category 20
                next
                edit 21
                    set category 23
                next
                edit 22
                    set category 24
                next
                edit 23
                    set category 25
                next
                edit 24
                    set category 26
                next
                edit 25
                    set category 28
                next
                edit 26
                    set category 29
                next
                edit 27
                    set category 30
                next
                edit 28
                    set category 31
                next
                edit 29
                    set category 33
                next
                edit 30
                    set category 34
                next
                edit 31
                    set category 35
                next
                edit 32
                    set category 36
                next
                edit 33
                    set category 37
                next
                edit 34
                    set category 38
                next
                edit 35
                    set category 39
                next
                edit 36
                    set category 40
                next
                edit 37
                    set category 41
                next
                edit 38
                    set category 42
                next
                edit 39
                    set category 43
                next
                edit 40
                    set category 44
                next
                edit 41
                    set category 46
                next
                edit 42
                    set category 47
                next
                edit 43
                    set category 48
                next
                edit 44
                    set category 49
                next
                edit 45
                    set category 50
                next
                edit 46
                    set category 51
                next
                edit 47
                    set category 52
                next
                edit 48
                    set category 53
                next
                edit 49
                    set category 54
                next
                edit 50
                    set category 55
                next
                edit 51
                    set category 56
                next
                edit 52
                    set category 57
                next
                edit 53
                    set category 58
                next
                edit 54
                    set category 59
                next
                edit 55
                    set category 61
                next
                edit 56
                    set category 62
                next
                edit 57
                    set category 63
                next
                edit 58
                    set category 64
                next
                edit 59
                    set category 65
                next
                edit 60
                    set category 66
                next
                edit 61
                    set category 67
                next
                edit 62
                    set category 68
                next
                edit 63
                    set category 69
                next
                edit 64
                    set category 70
                next
                edit 65
                    set category 71
                next
                edit 66
                    set category 72
                next
                edit 67
                    set category 75
                next
                edit 68
                    set category 76
                next
                edit 69
                    set category 77
                next
                edit 70
                    set category 78
                next
                edit 71
                    set category 79
                next
                edit 72
                    set category 80
                next
                edit 73
                    set category 81
                next
                edit 74
                    set category 82
                next
                edit 75
                    set category 83
                next
                edit 76
                    set category 84
                next
                edit 77
                    set category 85
                next
                edit 78
                    set category 86
                next
                edit 79
                    set category 87
                next
                edit 80
                    set category 88
                next
                edit 81
                    set category 89
                next
                edit 82
                    set category 90
                next
                edit 83
                    set category 91
                next
                edit 84
                    set category 92
                next
                edit 85
                    set category 93
                next
                edit 86
                    set category 94
                next
                edit 87
                    set category 95
                next
                edit 88
                    set category 96
                next
                edit 89
                    set category 97
                next
                edit 90
                    set category 98
                next
                edit 91
                    set category 99
                next
                edit 92
                    set category 100
                next
                edit 93
                    set category 101
                next
            end
        end
    next
    edit "wifi-default"
        set comment "Default configuration for offloading WiFi traffic."
        set options block-invalid-url
        config ftgd-wf
            unset options
            config filters
                edit 1
                    set action block
                next
                edit 2
                    set category 2
                    set action block
                next
                edit 3
                    set category 7
                    set action block
                next
                edit 4
                    set category 8
                    set action block
                next
                edit 5
                    set category 9
                    set action block
                next
                edit 6
                    set category 11
                    set action block
                next
                edit 7
                    set category 13
                    set action block
                next
                edit 8
                    set category 14
                    set action block
                next
                edit 9
                    set category 15
                    set action block
                next
                edit 10
                    set category 16
                    set action block
                next
                edit 11
                    set category 26
                    set action block
                next
                edit 12
                    set category 57
                    set action block
                next
                edit 13
                    set category 61
                    set action block
                next
                edit 14
                    set category 63
                    set action block
                next
                edit 15
                    set category 64
                    set action block
                next
                edit 16
                    set category 65
                    set action block
                next
                edit 17
                    set category 66
                    set action block
                next
                edit 18
                    set category 67
                    set action block
                next
                edit 19
                    set category 83
                    set action block
                next
                edit 20
                    set category 86
                    set action block
                next
                edit 21
                    set category 88
                    set action block
                next
                edit 22
                    set category 90
                    set action block
                next
                edit 23
                    set category 91
                    set action block
                next
                edit 24
                    set category 96
                    set action block
                next
                edit 25
                    set category 98
                    set action block
                next
                edit 26
                    set category 99
                    set action block
                next
                edit 27
                    set category 1
                next
                edit 28
                    set category 3
                next
                edit 29
                    set category 4
                next
                edit 30
                    set category 5
                next
                edit 31
                    set category 6
                next
                edit 32
                    set category 12
                next
                edit 33
                    set category 59
                next
                edit 34
                    set category 62
                next
            end
        end
    next
    edit "monitor-all"
        set comment "Monitor and log all visited URLs, flow-based."
        config ftgd-wf
            unset options
            config filters
                edit 1
                    set category 1
                next
                edit 2
                    set category 3
                next
                edit 3
                    set category 4
                next
                edit 4
                    set category 5
                next
                edit 5
                    set category 6
                next
                edit 6
                    set category 12
                next
                edit 7
                    set category 59
                next
                edit 8
                    set category 62
                next
                edit 9
                    set category 83
                next
                edit 10
                    set category 2
                next
                edit 11
                    set category 7
                next
                edit 12
                    set category 8
                next
                edit 13
                    set category 9
                next
                edit 14
                    set category 11
                next
                edit 15
                    set category 13
                next
                edit 16
                    set category 14
                next
                edit 17
                    set category 15
                next
                edit 18
                    set category 16
                next
                edit 19
                    set category 57
                next
                edit 20
                    set category 63
                next
                edit 21
                    set category 64
                next
                edit 22
                    set category 65
                next
                edit 23
                    set category 66
                next
                edit 24
                    set category 67
                next
                edit 25
                    set category 19
                next
                edit 26
                    set category 24
                next
                edit 27
                    set category 25
                next
                edit 28
                    set category 72
                next
                edit 29
                    set category 75
                next
                edit 30
                    set category 76
                next
                edit 31
                    set category 26
                next
                edit 32
                    set category 61
                next
                edit 33
                    set category 86
                next
                edit 34
                    set category 17
                next
                edit 35
                    set category 18
                next
                edit 36
                    set category 20
                next
                edit 37
                    set category 23
                next
                edit 38
                    set category 28
                next
                edit 39
                    set category 29
                next
                edit 40
                    set category 30
                next
                edit 41
                    set category 33
                next
                edit 42
                    set category 34
                next
                edit 43
                    set category 35
                next
                edit 44
                    set category 36
                next
                edit 45
                    set category 37
                next
                edit 46
                    set category 38
                next
                edit 47
                    set category 39
                next
                edit 48
                    set category 40
                next
                edit 49
                    set category 42
                next
                edit 50
                    set category 44
                next
                edit 51
                    set category 46
                next
                edit 52
                    set category 47
                next
                edit 53
                    set category 48
                next
                edit 54
                    set category 54
                next
                edit 55
                    set category 55
                next
                edit 56
                    set category 58
                next
                edit 57
                    set category 68
                next
                edit 58
                    set category 69
                next
                edit 59
                    set category 70
                next
                edit 60
                    set category 71
                next
                edit 61
                    set category 77
                next
                edit 62
                    set category 78
                next
                edit 63
                    set category 79
                next
                edit 64
                    set category 80
                next
                edit 65
                    set category 82
                next
                edit 66
                    set category 85
                next
                edit 67
                    set category 87
                next
                edit 68
                    set category 31
                next
                edit 69
                    set category 41
                next
                edit 70
                    set category 43
                next
                edit 71
                    set category 49
                next
                edit 72
                    set category 50
                next
                edit 73
                    set category 51
                next
                edit 74
                    set category 52
                next
                edit 75
                    set category 53
                next
                edit 76
                    set category 56
                next
                edit 77
                    set category 81
                next
                edit 78
                    set category 84
                next
                edit 79
                next
                edit 80
                    set category 88
                next
                edit 81
                    set category 89
                next
                edit 82
                    set category 90
                next
                edit 83
                    set category 91
                next
                edit 84
                    set category 92
                next
                edit 85
                    set category 93
                next
                edit 86
                    set category 94
                next
                edit 87
                    set category 95
                next
                edit 88
                    set category 96
                next
                edit 89
                    set category 97
                next
                edit 90
                    set category 98
                next
                edit 91
                    set category 99
                next
                edit 92
                    set category 100
                next
                edit 93
                    set category 101
                next
            end
        end
        set log-all-url enable
        set web-content-log disable
        set web-filter-command-block-log disable
        set web-filter-cookie-log disable
        set web-url-log disable
        set web-invalid-domain-log disable
        set web-ftgd-err-log disable
    next
    edit "色情网站过滤"
        config ftgd-wf
            set options error-allow
            config filters
                edit 1
                    set category 1
                next
                edit 2
                    set category 3
                next
                edit 3
                    set category 4
                next
                edit 4
                    set category 5
                next
                edit 5
                    set category 6
                next
                edit 6
                    set category 12
                next
                edit 7
                    set category 59
                next
                edit 8
                    set category 62
                next
                edit 9
                    set category 83
                    set action block
                next
                edit 10
                    set category 96
                    set action block
                next
                edit 11
                    set category 98
                    set action block
                next
                edit 12
                    set category 99
                    set action block
                next
                edit 13
                    set category 2
                    set action warning
                next
                edit 14
                    set category 7
                    set action warning
                next
                edit 15
                    set category 8
                    set action warning
                    set warn-duration 10s
                next
                edit 16
                    set category 9
                    set action warning
                next
                edit 17
                    set category 11
                    set action warning
                next
                edit 18
                    set category 13
                    set action warning
                    set warn-duration 10s
                next
                edit 19
                    set category 14
                    set action warning
                    set warn-duration 10s
                next
                edit 20
                    set category 15
                    set action warning
                    set warn-duration 10s
                next
                edit 21
                    set category 16
                    set action warning
                next
                edit 22
                    set category 57
                    set action warning
                next
                edit 23
                    set category 63
                    set action warning
                next
                edit 24
                    set category 64
                    set action warning
                next
                edit 25
                    set category 65
                    set action warning
                next
                edit 26
                    set category 66
                    set action warning
                next
                edit 27
                    set category 67
                    set action warning
                next
                edit 28
                    set category 26
                    set action warning
                    set warn-duration 10s
                next
                edit 29
                    set category 61
                    set action warning
                    set warn-duration 10s
                next
                edit 30
                    set category 86
                    set action warning
                next
                edit 31
                    set category 88
                    set action warning
                next
                edit 32
                    set category 90
                    set action warning
                next
                edit 33
                    set category 91
                    set action warning
                next
                edit 34
                    set category 140
                next
                edit 35
                    set category 141
                next
            end
        end
    next
end
config webfilter search-engine
    edit "google"
        set hostname ".*\\.google\\..*"
        set url "^\\/((custom|search|images|videosearch|webhp)\\?)"
        set query "q="
        set safesearch url
        set safesearch-str "&safe=active"
    next
    edit "yahoo"
        set hostname ".*\\.yahoo\\..*"
        set url "^\\/search(\\/video|\\/images){0,1}(\\?|;)"
        set query "p="
        set safesearch url
        set safesearch-str "&vm=r"
    next
    edit "bing"
        set hostname ".*\\.bing\\..*"
        set url "^(\\/images|\\/videos)?(\\/search|\\/async|\\/asyncv2)\\?"
        set query "q="
        set safesearch header
    next
    edit "yandex"
        set hostname "yandex\\..*"
        set url "^\\/((|yand|images\\/|video\\/)(search)|search\\/)\\?"
        set query "text="
        set safesearch url
        set safesearch-str "&family=yes"
    next
    edit "youtube"
        set hostname ".*youtube.*"
        set safesearch header
    next
    edit "baidu"
        set hostname ".*\\.baidu\\.com"
        set url "^\\/s?\\?"
        set query "wd="
    next
    edit "baidu2"
        set hostname ".*\\.baidu\\.com"
        set url "^\\/(ns|q|m|i|v)\\?"
        set query "word="
    next
    edit "baidu3"
        set hostname "tieba\\.baidu\\.com"
        set url "^\\/f\\?"
        set query "kw="
    next
    edit "vimeo"
        set hostname ".*vimeo.*"
        set url "^\\/search\\?"
        set query "q="
        set safesearch header
    next
    edit "yt-scan-1"
        set url "www.youtube.com/user/"
        set safesearch yt-scan
    next
    edit "yt-scan-2"
        set url "www.youtube.com/youtubei/v1/browse"
        set safesearch yt-scan
    next
    edit "yt-scan-3"
        set url "www.youtube.com/youtubei/v1/player"
        set safesearch yt-scan
    next
    edit "yt-scan-4"
        set url "www.youtube.com/youtubei/v1/navigator"
        set safesearch yt-scan
    next
    edit "yt-channel"
        set url "www.youtube.com/channel"
        set safesearch yt-channel
    next
    edit "yt-pattern"
        set url "youtube.com/channel/"
        set safesearch yt-pattern
    next
    edit "twitter"
        set hostname "twitter\\.com"
        set url "^\\/i\\/api\\/graphql\\/.*\\/UserByScreenName"
        set query "variables="
        set safesearch translate
        set safesearch-str "regex::%22screen_name%22:%22([A-Za-z0-9_]{4,15})%22::twitter.com/\\1"
    next
    edit "google-translate-1"
        set hostname "translate\\.google\\..*"
        set url "^\\/translate"
        set query "u="
        set safesearch translate
        set safesearch-str "regex::(?:\\?|&)u=([^&]+)::\\1"
    next
    edit "google-translate-2"
        set hostname ".*\\.translate\\.goog"
        set url "^\\/"
        set safesearch translate
        set safesearch-str "case::google-translate"
    next
end
config emailfilter profile
    edit "sniffer-profile"
        set comment "Malware and phishing URL monitoring."
        config imap
        end
        config pop3
        end
        config smtp
        end
    next
    edit "default"
        set comment "Malware and phishing URL filtering."
        config imap
        end
        config pop3
        end
        config smtp
        end
    next
end
config virtual-patch profile
    edit "default"
    next
end
config report layout
    edit "default"
        set title "FortiGate System Analysis Report"
        set style-theme "default-report"
        set options include-table-of-content view-chart-as-heading
        set schedule-type weekly
        config page
            set paper letter
            set page-break-before heading1
            config header
                config header-item
                    edit 1
                        set type image
                        set style "header-image"
                        set img-src "fortinet_logo_small.png"
                    next
                end
            end
            config footer
                config footer-item
                    edit 1
                        set style "footer-text"
                        set content "FortiGate ${schedule_type} Security Report - Host Name: ${hostname}"
                    next
                    edit 2
                        set style "footer-pageno"
                    next
                end
            end
        end
        config body-item
            edit 101
                set type image
                set style "report-cover1"
                set img-src "fortigate_log.png"
            next
            edit 103
                set style "report-cover2"
                set content "FortiGate ${schedule_type} Security Report"
            next
            edit 105
                set style "report-cover3"
                set content "Report Date: ${started_time}"
            next
            edit 107
                set style "report-cover3"
                set content "Data Range: ${report_data_range}  (${hostname})"
            next
            edit 109
                set style "report-cover3"
                set content "${vdom}"
            next
            edit 111
                set type image
                set style "report-cover4"
                set img-src "fortinet_logo_small.png"
            next
            edit 121
                set type misc
                set misc-component page-break
            next
            edit 301
                set text-component heading1
                set content "Bandwidth and Applications"
            next
            edit 311
                set type chart
                set chart "traffic.bandwidth.history_c"
            next
            edit 321
                set type chart
                set chart "traffic.sessions.history_c"
            next
            edit 331
                set type chart
                set chart "traffic.statistics"
            next
            edit 411
                set type chart
                set chart "traffic.bandwidth.apps_c"
            next
            edit 421
                set type chart
                set chart "traffic.bandwidth.cats_c"
            next
            edit 511
                set type chart
                set chart "traffic.bandwidth.users_c"
            next
            edit 521
                set type chart
                set chart "traffic.users.history.hour_c"
            next
            edit 611
                set type chart
                set chart "traffic.bandwidth.destinations_tab"
            next
            edit 1001
                set text-component heading1
                set content "Web Usage"
            next
            edit 1011
                set type chart
                set chart "web.allowed-request.sites_c"
            next
            edit 1021
                set type chart
                set chart "web.bandwidth.sites_c"
            next
            edit 1031
                set type chart
                set chart "web.blocked-request.sites_c"
            next
            edit 1041
                set type chart
                set chart "web.blocked-request.users_c"
            next
            edit 1051
                set type chart
                set chart "web.requests.users_c"
            next
            edit 1061
                set type chart
                set chart "web.bandwidth.users_c"
            next
            edit 1071
                set type chart
                set chart "web.bandwidth.stream-sites_c"
            next
            edit 1301
                set text-component heading1
                set content "Emails"
            next
            edit 1311
                set type chart
                set chart "email.request.senders_c"
            next
            edit 1321
                set type chart
                set chart "email.bandwidth.senders_c"
            next
            edit 1331
                set type chart
                set chart "email.request.recipients_c"
            next
            edit 1341
                set type chart
                set chart "email.bandwidth.recipients_c"
            next
            edit 1501
                set text-component heading1
                set content "Threats"
            next
            edit 1511
                set type chart
                set top-n 80
                set chart "virus.count.viruses_c"
            next
            edit 1531
                set type chart
                set top-n 80
                set chart "virus.count.users_c"
            next
            edit 1541
                set type chart
                set top-n 80
                set chart "virus.count.sources_c"
            next
            edit 1551
                set type chart
                set chart "virus.count.history_c"
            next
            edit 1561
                set type chart
                set top-n 80
                set chart "botnet.count_c"
            next
            edit 1571
                set type chart
                set top-n 80
                set chart "botnet.count.users_c"
            next
            edit 1581
                set type chart
                set top-n 80
                set chart "botnet.count.sources_c"
            next
            edit 1591
                set type chart
                set chart "botnet.count.history_c"
            next
            edit 1601
                set type chart
                set top-n 80
                set chart "attack.count.attacks_c"
            next
            edit 1611
                set type chart
                set top-n 80
                set chart "attack.count.victims_c"
            next
            edit 1621
                set type chart
                set top-n 80
                set chart "attack.count.source_bar_c"
            next
            edit 1631
                set type chart
                set chart "attack.count.blocked_attacks_c"
            next
            edit 1641
                set type chart
                set chart "attack.count.severity_c"
            next
            edit 1651
                set type chart
                set chart "attack.count.history_c"
            next
            edit 1701
                set text-component heading1
                set content "VPN Usage"
            next
            edit 1711
                set type chart
                set top-n 80
                set chart "vpn.bandwidth.static-tunnels_c"
            next
            edit 1721
                set type chart
                set top-n 80
                set chart "vpn.bandwidth.dynamic-tunnels_c"
            next
            edit 1731
                set type chart
                set top-n 80
                set chart "vpn.bandwidth.ssl-tunnel.users_c"
            next
            edit 1741
                set type chart
                set top-n 80
                set chart "vpn.bandwidth.ssl-web.users_c"
            next
            edit 1901
                set text-component heading1
                set content "Admin Login and System Events"
            next
            edit 1911
                set type chart
                set top-n 80
                set chart "event.login.summary_c"
            next
            edit 1931
                set type chart
                set top-n 80
                set chart "event.failed.login_c"
            next
            edit 1961
                set type chart
                set top-n 80
                set chart "event.system.group_events_c"
            next
        end
    next
end
config wanopt settings
    set host-id "default-id"
end
config wanopt profile
    edit "default"
        set comments "Default WANopt profile."
    next
end
config log memory setting
    set status disable
end
config log disk setting
    set status enable
end
config log eventfilter
    set system disable
    set vpn disable
    set user disable
    set router disable
    set wireless-activity disable
    set wan-opt disable
    set endpoint disable
    set ha disable
    set fortiextender disable
    set connector disable
    set sdwan disable
    set cifs disable
    set switch-controller disable
    set webproxy disable
end
config log null-device setting
    set status disable
end
config log setting
    set local-out disable
end
config firewall schedule onetime
    edit "onetime-test001"
        set start 11:05 2025/07/01
        set end 12:05 2025/07/10
    next
    edit "onetime-test002"
        set start 11:06 2025/07/01
        set end 12:06 2025/07/25
    next
end
config firewall schedule recurring
    edit "always"
        set day sunday monday tuesday wednesday thursday friday saturday
    next
    edit "none"
    next
    edit "default-darrp-optimize"
        set start 01:00
        set end 01:30
        set day sunday monday tuesday wednesday thursday friday saturday
    next
    edit "worker-day"
        set day monday tuesday wednesday thursday friday
    next
    edit "recurring-test01"
        set start 09:00
        set end 23:59
        set day sunday monday tuesday thursday
    next
    edit "recurring-test02"
        set start 23:00
        set end 02:00
        set day sunday tuesday thursday
    next
end
config firewall vip
    edit "tct-vip-addr1"
        set uuid 510e1832-532f-51f0-1bf1-b475a2a45a30
        set comment "this is tct-vip-addr1"
        set extip ***************
        set mappedip "*********"
        set extintf "any"
    next
end
config firewall ssh local-key
    edit "Fortinet_SSH_RSA2048"
        set password ENC xUoFnptbGOCWHM0ozo+fukT989BJmg1zMKF6odjJU6WHYiKVK9gBp1A8B3DyGZYby0mElkek3bx7RjWlQPm8c0PWzgshkYFhty6dQac1zUfdpQ+Yn03VaJBO1D1m/Ypj/6HUt3bNEJhSfUAqV4T3h2WS/ROvv9y2dvgzJjM+f0j63cGRHmwElyhQWFCUkuDETKzFmVlmMjY3dkVA
        set private-key "-----BEGIN OPENSSH PRIVATE KEY-----
b3BlbnNzaC1rZXktdjEAAAAACmFlczI1Ni1jdHIAAAAGYmNyeXB0AAAAGAAAABD41fXgne
5Q3tLcsqJ8rn2MAAAAEAAAAAEAAAEXAAAAB3NzaC1yc2EAAAADAQABAAABAQCZlp7x//UB
V8QHYc68zowBXFxCTAHg+DgPzFmug4EUqrZkXuhasCGRTlp6Uu7qhD0/C7xfPEWG3eKdGt
SLsdjEvKNL8Sha6jMFsuT2X+/2zog/H4sI06we88CeFboZNwJ0dLWdZ54uOlOtzAPFEoBj
WI7+wQJ4jjSbIoU12jKc7BqVap2Wfd6ywJ1Ec88PKMXgoAb06OD91RCMNB1lNs9AITKV9F
dgWV9jbxkRrcgFn35eAhQhw8tXe68EPDImnBvLqkiqgKsnXU4dJhlnqRjD7/3nxn+WGxz7
lr2ZkIJ+pUctBMl8NGeQt8LceHsVaktw4cfGe2qcUvTf/OI/X9wNAAADwAX8yEgsP+clm5
MgNbjknhJOjsbGmUyceZ2NRYBIhlY4IIro96Vwa01C8Dw9REl2VLquBuzoHVwhhn3qjk6c
TehL0QtCnlpMO1363ndXrM5C/n1HMszTJvPvNNupP3kT50Ji7QixT2dM5cRY11Z3LoWhud
kJqNzZvlX5mNkP5jQ6W2t5mF1a3qrFpbkoof89x3cjp8vBYPaCOXjMaol2TQB/fx/Gwikz
gO83qpBQCgDCQeKgEB5myS7VSbiVc5R4tSo6pYhDM+9AMlBMew9AweovFaJTP7u7efMBk0
+5ziqqRxAPZjaID2G/T/IYF2gO4iM9hFcPIHdZeScDILN1GtGCyi7osqg7FVnIDSsmUcXd
3ZeWPNBtXA0na9t96IpM29V8St6/sTPzvJXoDGSPpa5bbaSIFQpGNq4xTWV3/1xJLWo/Da
0qHGtHo47UPhn1yLbJYlZKxotEfrGDw8/9t9YfVzdyeXXekN3KVS8UlMxo3x+pEmZFPTVy
YX1x6CKy/FKv4//46Oi0++LDM9IyN1bbxupDsQYv78RXbPhUqc9nmpOu+A+6+oBmhll/83
Wz0NMgsMS5Zq1DjSRt5Of9xmWE8tYTj8emIxQccgh7s5G3hdC54Pln0TT7A+LPLtNhdSOC
Kx+9YXv7IxhqicTiBrOdXiiFctpSxW7BbfR5AG/FwaZBo+Is/sL0lmRGsKI61W1gjv4FTP
NA2ROMeKnYlUdkopeH1jv2Kt7D7rS8xX+DkYympIjjWfIbPCkxKBkVcl1ZVrHLi+AGsxMV
6w2jjC3IpXw2V43U7T7ygNvpGCjIXs/foBd9+Us1ZGoq9ctxVACC2jHX56F8LLR0k63MFL
48yLnCyUBlbQK2/HE5b8RMs2Z3K3TeMEukYPPszDydnhjPesa/5ER3fAaUSWseeNFX23Hq
RKuEjwwdNX5ETfRy/8eqRyMNd3DTIFveWCH5FjYjB3FomGCCYj+EUk95gaCLHwZIq4Drq7
Pjc/uk3arbzKHShwI8lZPI/7tJvvBz4+ASTwkLrawY6lxEGkCpmg7HEY17ykGkWNlwcdRt
gigN5FCPy23Joq0hHXL5QFUuoLHAewj/vR7QDqC+EfmFxiVPGWPAx+yb2KtHuoZap1VGA4
CpukmsE+CaWv4zMhQpmns5f9JhKeB6pWsAv/iZWQPNx9Wh+/Qhww4HdM5ZPsRIvUB1r1Bt
MqE6+DEofq5xEptMLM5DpFuWDInnzXL24Tvgf3zgfiIOJwq/NWUx43bxsU/5lyQExVERgQ
5yMdsr5g==
-----END OPENSSH PRIVATE KEY-----
"
        set public-key "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQCZlp7x//UBV8QHYc68zowBXFxCTAHg+DgPzFmug4EUqrZkXuhasCGRTlp6Uu7qhD0/C7xfPEWG3eKdGtSLsdjEvKNL8Sha6jMFsuT2X+/2zog/H4sI06we88CeFboZNwJ0dLWdZ54uOlOtzAPFEoBjWI7+wQJ4jjSbIoU12jKc7BqVap2Wfd6ywJ1Ec88PKMXgoAb06OD91RCMNB1lNs9AITKV9FdgWV9jbxkRrcgFn35eAhQhw8tXe68EPDImnBvLqkiqgKsnXU4dJhlnqRjD7/3nxn+WGxz7lr2ZkIJ+pUctBMl8NGeQt8LceHsVaktw4cfGe2qcUvTf/OI/X9wN"
        set source built-in
    next
    edit "Fortinet_SSH_DSA1024"
        set password ENC i75zN4hA/iEa5ztRoc3IwUhZrZ1TxQ7vXHqCWRp1v/6xWjI9bjcaANSvbiAoA2CyEC1TBsaU839oCK3rDzvARmOUtQIBpbuY8d+aS68FLxzx8LmJyPqFxe1Fmy/FoS31pSZ+s7KzL6AojhOk9d3JZTEFbTARilgZRvUEjZ5bO6NO+d11GmVotscqFp9eoc7haHnV0llmMjY3dkVA
        set private-key "-----BEGIN OPENSSH PRIVATE KEY-----
b3BlbnNzaC1rZXktdjEAAAAACmFlczI1Ni1jdHIAAAAGYmNyeXB0AAAAGAAAABDIwQs86U
aM9Ww0+zu46SuxAAAAEAAAAAEAAAGyAAAAB3NzaC1kc3MAAACBAPraYRwJIF9VHfClD8Io
4kwt50aUYZQZGrDodL9VXxJEdRCTAIHbcgRl0JdlxypVZS3XOpZ2X/0Vf+oYu6YIjvVews
WEz8BYvIcn7usU7BfXDsWYP5KPuvH9EaQybExBVxPe1RAIkMPvogsohGJCNT/UCOwEGeWQ
dgXXkh91ku9RAAAAFQDIgOnb9WbB7gdE+YPcnJ0ZD2u5KwAAAIEAsT9erpcOhmhdASodD6
gD7VQ2DPHUqrkRq4QPh5ZTq8Oyp+GebdC3Saq6WjNNfems520n9pcw1InLPi6pKMgLJ/Z3
SaBM0JUsT+eVB5KndE5mQFyUpQ/fbYbpI1LNEifFU5gqCuet13C020XGO25tmVSGgWXk/N
ain7IE4uvP3WoAAACAThXWa/CZoJ9B4+FF01Qvr9ukZMeIswaslYuBZoJhBJjRFB5AoXWD
IJ7dYD9ANyvZVTQT+sBlKSxJoP56gVCcgU/x9VlTtRGtJOtfYgvXMY7uVKc5Xb0EFsY8KQ
R1Uk08viVjW+nGUziQteb+CfCTGmXC8E/NrAh68PPLlLwnEJQAAAHgtXphv+DuRM9k271i
w49vuqPx56chpP+VZZkRGeAW8f6pKCN3zoI3cdpSzPAds+DApxe5TrYjkUKx33QosESmYG
L5hqtuStkkM2xREhWwgcS9udWJGSRiliUuffRBTD+rZEt12gIJTW+KSnckXYQm69NtKcLp
8P4JKxv6fw/wf+nK8t+WkIL2EXaY2shL9424ihPhQWk4eUwCXLrF+rgWV9aDXkPUXcp0Mq
HnhEZEO3AHPAcxafEv1cq02BotDjn5a12bNSo3LSEiuUqGjtvY7CxR/EGkcKD0MacssGT8
ne3CEWlt/mLz+tbi///3ZXnmIDciKvBzOgSazwKxackxQh2PoOLPKhZKjHmJGVJs/qRYvg
MH9AMEITQVLhqW04+I0idYOV/CT4qSjkPLFshRIannCwx/7GTlCEvcrs6e/VeOyncuQmuK
x/d7VbjnO5/nPx7KksrZq3hIqlwUIH1VlI2jHK3sdEBHTz+sfbFpAJyzYuqtaXKsUe1jdZ
Glgz8RtABY1pMUcOMJ0gZR2iG5Js9VIzi/w+NRL7GkBW7o9XMse2u/NsWSfrdWdzBvCUaQ
SNuaFwpYygZGTtTBJRr5JkMZvmMjpuuAX7w1JNL+FxMppyX+Mpeau5PUi0ylMmhP
-----END OPENSSH PRIVATE KEY-----
"
        set public-key "ssh-dss AAAAB3NzaC1kc3MAAACBAPraYRwJIF9VHfClD8Io4kwt50aUYZQZGrDodL9VXxJEdRCTAIHbcgRl0JdlxypVZS3XOpZ2X/0Vf+oYu6YIjvVewsWEz8BYvIcn7usU7BfXDsWYP5KPuvH9EaQybExBVxPe1RAIkMPvogsohGJCNT/UCOwEGeWQdgXXkh91ku9RAAAAFQDIgOnb9WbB7gdE+YPcnJ0ZD2u5KwAAAIEAsT9erpcOhmhdASodD6gD7VQ2DPHUqrkRq4QPh5ZTq8Oyp+GebdC3Saq6WjNNfems520n9pcw1InLPi6pKMgLJ/Z3SaBM0JUsT+eVB5KndE5mQFyUpQ/fbYbpI1LNEifFU5gqCuet13C020XGO25tmVSGgWXk/Nain7IE4uvP3WoAAACAThXWa/CZoJ9B4+FF01Qvr9ukZMeIswaslYuBZoJhBJjRFB5AoXWDIJ7dYD9ANyvZVTQT+sBlKSxJoP56gVCcgU/x9VlTtRGtJOtfYgvXMY7uVKc5Xb0EFsY8KQR1Uk08viVjW+nGUziQteb+CfCTGmXC8E/NrAh68PPLlLwnEJQ="
        set source built-in
    next
    edit "Fortinet_SSH_ECDSA256"
        set password ENC pjyuDx+pxS2rhrIBpJgsXFygvks50BliPPbp8C3arUKI5CZdsYUWtV9Ogdp3E/AaSXFjh0/VUW4wGE7P1TW6C8tsf7H/nnSLJ0L3tq2fxkVx6mbVJ55c23B4rUfgnvSNvHjaI5cVSFMJ4Fwxv00bGLf+JJZJFkMuoKqvNbpj7zp6DKIE6BSYWOG5BdjM8b6GjpyxE1lmMjY3dkVA
        set private-key "-----BEGIN OPENSSH PRIVATE KEY-----
b3BlbnNzaC1rZXktdjEAAAAACmFlczI1Ni1jdHIAAAAGYmNyeXB0AAAAGAAAABBvYPHXxO
rVBu5KrYgQrC1xAAAAEAAAAAEAAABoAAAAE2VjZHNhLXNoYTItbmlzdHAyNTYAAAAIbmlz
dHAyNTYAAABBBOlMCI1utHn9xuyuv2ksef/KJxM4oAfmzvWpmV+0+BOWdq58x80R6bEPKk
8p23jqGnoVxWsf4Ms12+R/9YiKHogAAACgfEHxJ9nLc1aGtJTGWSbFKVZiuiu+JfiXUdTJ
yVPLO7fkCChJuTtfB54S/v6yYFYWsqO08lX4c5HMely/7NUoICIdpGyDV7w9EKsrPBz/Ba
QRiolxwhWew0uiNDQ/eWXwxOY0Ltor4DTEqnO0n7TYm/G/C8rwuTbonw+KSwJkDp3J94LT
C8FLPGRroSpwz+RETWzPwQZWHuAyosCnuv1cdg==
-----END OPENSSH PRIVATE KEY-----
"
        set public-key "ecdsa-sha2-nistp256 AAAAE2VjZHNhLXNoYTItbmlzdHAyNTYAAAAIbmlzdHAyNTYAAABBBOlMCI1utHn9xuyuv2ksef/KJxM4oAfmzvWpmV+0+BOWdq58x80R6bEPKk8p23jqGnoVxWsf4Ms12+R/9YiKHog="
        set source built-in
    next
    edit "Fortinet_SSH_ECDSA384"
        set password ENC hN4Zz+FKEFLV3Ok18dVedCW6gTFcOC0aPVFtg4PkDrFebLcCMYGEXT/65O1h+OWNRuC2A65Y54ZWICAIhyE7lTBh9OmTmyD/z5zR+AB9wcc05RJnBEWVYQF/2JyVwCyG2W/Msf/fVahD2iDZnr5+352YSGNAFdPTM7xhmv7YtvIlVfgXcTRS4nFP/z75H1aZ7Uu51llmMjY3dkVA
        set private-key "-----BEGIN OPENSSH PRIVATE KEY-----
b3BlbnNzaC1rZXktdjEAAAAACmFlczI1Ni1jdHIAAAAGYmNyeXB0AAAAGAAAABDAafibZq
Cm/bC6zTgD0cNUAAAAEAAAAAEAAACIAAAAE2VjZHNhLXNoYTItbmlzdHAzODQAAAAIbmlz
dHAzODQAAABhBIP6KXXdKVXmU72vB0FPyTxl5ohrSbu4PH3yPcU/sd6TCbpmZ2vOyfebEw
bvZHwKmLPlyDjw2Bks4AtrSs3FjA7UbimvunzUeD7E55bK5D1PqKtTz1FOJLlu6DOuG6Ck
rAAAANCIxydns5R1LT9JKzxir/dp9PPZwh4OzLSe4G6eT3Si2eTXOeYSQ3QMFZmDX33jt9
CN65YT/5zA6OtcZ/ZlGZSTIGGVzpZQrk5uk8XhA3vaFkh4CoYnLSWTxIRKgYs9AMr6GNXT
7jGzqW2YHk9F/HJnWC99wu0UmB7b4QXVNw/5LpZbr7NntMpNvpn+vw9UxraglZr1suZdnH
pmLRlxuXXZ4AUwSNIVnlwebxqTduov+BwXFLXymh6zgsnTfGkZ2rtZodPdodVAiOmpRGaG
H4ua
-----END OPENSSH PRIVATE KEY-----
"
        set public-key "ecdsa-sha2-nistp384 AAAAE2VjZHNhLXNoYTItbmlzdHAzODQAAAAIbmlzdHAzODQAAABhBIP6KXXdKVXmU72vB0FPyTxl5ohrSbu4PH3yPcU/sd6TCbpmZ2vOyfebEwbvZHwKmLPlyDjw2Bks4AtrSs3FjA7UbimvunzUeD7E55bK5D1PqKtTz1FOJLlu6DOuG6CkrA=="
        set source built-in
    next
    edit "Fortinet_SSH_ECDSA521"
        set password ENC zBzfN27PdzcCsJSPhYtAIy7wymKGgGp2jv2ZrGOl6OHvSTAbsffs+8qHKbjGQarVluKwy8ltx8TlF+doPtNvIF0JvH9mCo5fRbjAFqi5uV3XIBeFlM53/9DFA+w9K8Pxvii/y+JUVwjjl1ObQ6R3zJeHvJU7HFq3pPXj2cAkNe5KFBVNpmcN2ZWSf3kQuO+4wu6Lb1lmMjY3dkVA
        set private-key "-----BEGIN OPENSSH PRIVATE KEY-----
b3BlbnNzaC1rZXktdjEAAAAACmFlczI1Ni1jdHIAAAAGYmNyeXB0AAAAGAAAABDTKiC24F
iwIFwf71tHa40wAAAAEAAAAAEAAACsAAAAE2VjZHNhLXNoYTItbmlzdHA1MjEAAAAIbmlz
dHA1MjEAAACFBAA6EbPZgOJrgNCnxIopUAqjJIXxJfST+O8nf9rR5PqRB+uRaXHDWDItn/
1bqOPYGpHt5APfhUtKrIwdW8Gt9Zg7KwH0NgePp1DakabS5S7y5CE0fvYR3cOOREo4iuHl
+6lPnNUT//8lQSdR+bnQx3Yi1GIAIk/pbs7u7tHyHVdMwduGOgAAAQAafOPatuuCwhCL8i
0FfkR7p5sdSyB4KzxXIEHBqfC2KMfdLyOEXjC++kH0QP9gDj7acHT6FCv3zHcY9Zc7W19f
+O7EikFDDx28kNVub1wtr17tvXJkrHe9eFKN7EHRqVUKCWFX529X8/QSVsfoBgIP+5aKOf
mJEVrK0q++v3f+eLG8lA6R4XtqD7PGL9+p/rhDBXsQuP0H18AmSK4iYvB2PSfcUv/XI78e
a/YQIP4hv+gtfzOx6mC57hYNkKcS7F2TACchxWl8GJ5AMBmYsMfF07aZRM7NxEEOoh9sZo
VVZMBPzBSSzRnFJRdmbPfkryq0gp0RWFCxfEOI66CxaJxQ
-----END OPENSSH PRIVATE KEY-----
"
        set public-key "ecdsa-sha2-nistp521 AAAAE2VjZHNhLXNoYTItbmlzdHA1MjEAAAAIbmlzdHA1MjEAAACFBAA6EbPZgOJrgNCnxIopUAqjJIXxJfST+O8nf9rR5PqRB+uRaXHDWDItn/1bqOPYGpHt5APfhUtKrIwdW8Gt9Zg7KwH0NgePp1DakabS5S7y5CE0fvYR3cOOREo4iuHl+6lPnNUT//8lQSdR+bnQx3Yi1GIAIk/pbs7u7tHyHVdMwduGOg=="
        set source built-in
    next
    edit "Fortinet_SSH_ED25519"
        set password ENC wtr3pjXdouuDA8PJnpD0/4uN+Txd7zbo7DanE1rAI8c7Ub+m5U75+cO2MOEjyBk+Q6uTk0u+8UvHF4TD5Fa2nlV/6eGbwmQGuLru1D2Q7F8o4pjY6g8IIpFzaV2i78QqKEBEu5K5rdjjv5iuDJnJgpUn/UY4tqgbsTn49LMFaP9vSRnYSfAzyVMIyifbZmdL2pfatVlmMjY3dkVA
        set private-key "-----BEGIN OPENSSH PRIVATE KEY-----
b3BlbnNzaC1rZXktdjEAAAAACmFlczI1Ni1jdHIAAAAGYmNyeXB0AAAAGAAAABDkSATwRY
CF/coxgpZoza2wAAAAEAAAAAEAAAAzAAAAC3NzaC1lZDI1NTE5AAAAIAc3tMEzoxe+Xhvh
Bo1vjk6Fl44Qe7EeIGeCLGCgpFZ0AAAAkN9o77GgmUJejLJySDEUb32PQjW6g6J+fD9g5k
+CViTyaY7tZURU9Opz6yXLqYSHyqerb9mgbN9dsfn5WmWNaws8XbYKqW/U3fzTduwxbAAq
SZrQI0DEzg0q9KgE/yjsDD3/Y3wgnmQ3qxnr7kWMhKJG2QIxlcbRM1M2WTUGLuwmZRGJ4T
fVd5SeZlq78gIfKQ==
-----END OPENSSH PRIVATE KEY-----
"
        set public-key "ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIAc3tMEzoxe+XhvhBo1vjk6Fl44Qe7EeIGeCLGCgpFZ0"
        set source built-in
    next
end
config firewall ssh local-ca
    edit "Fortinet_SSH_CA"
        set password ENC S8N402MCAbywU/2MyHSvx5xFuFKmt8CjX4CNTe3R2OXV11qCu64KsuMoIOzGPsW/yrbFWyzbCQhP1iK0HyJBSCiW5jA6AQ/N28laxFMj2YfwLAuXUPFhLeW/G4kRJigRDFLO+CLe68rpW1y0LaEVAD+9wRC+xQmxOJHUlb0IZj7WkJp7naT/w9uBasOXIO2q56+df1lmMjY3dkVA
        set private-key "-----BEGIN OPENSSH PRIVATE KEY-----
b3BlbnNzaC1rZXktdjEAAAAACmFlczI1Ni1jdHIAAAAGYmNyeXB0AAAAGAAAABBGQIdWy+
BoRMWqA7JntmlkAAAAEAAAAAEAAAEXAAAAB3NzaC1yc2EAAAADAQABAAABAQDRfHBIS14g
D0ISvU8wDpaWgNW8EUTvKVQN7aklnnlntO5p0Plzvc8NIda2q0F/9f2VEO2tNgNvN7v17u
I1B545lujq7TiGu1bLNwfIleT0wsXl5aa5skPH7tQ7su+P6lSiAcp5fRAszcR3iDZEtKiU
5brMIM3cHztvMDPWIv92TBgxpqPn5VXP+zCahiGpk4tQ6XS1wt8KGjY83uS0QdmFxear55
3wYtu7CJARj9xWZw35ErbtTaPzNX6//KVnHIFdun3oRjEh6f+44rJcdqP+3ZM7e+HCcLPe
AC3rK278L7BlOv4MsPNgqJqPkh6tPTUXkxUtiYuW8NomoiJTLcALAAADwGg5MaZCnpWsbC
KnGzY+B6f1vS8E4wBaFXg9eW1XVemgTqD6406YvxAejrCNs+QhVdEpK8/zhKlmjGhGngaV
nao+NeSOjXDp/GN234OqoY5G2RbzxLeUffEFJIrLPY9+KaVGWbwfYv5CexDyAp5Mqa8xbJ
TJuwwaEzP2jDwQ+G+IMO02RBDWh79Mcs/Du14QEYn0w5erP6SOQahwi2GAwisMOPYp1ZXG
XSRqYCPIrck/mJiqQTDnbVL5iRuznY6DqyNeAPYm+ZOqjSNkilMs5tKwzd12o0iOi3RUYU
6cPyVD0K+MqN4FxpCRAyFDCuaUz3rsHR8bVUcwzy9p3Q7p9WLN7lxmA8HsoyTO7x2o5A/h
T1eE74BXbD0+SsYSSWi7bzAUNSZkDLA44rlUWr8sJN00oxgMwD55GNKH0mt3bNrGdmbvPy
b8wTl2pz3Ec0fIH2hptAEqW+T72ANAN/9Gzl7NVy3Ps5rQNakQCFuRLoJdf9k0jD807xG+
dbD+4UMcjNLejTQ4sjVm7vQ6Lhwt4sMvxwDCHYrmW7+9RUmmtNG7oEfpP1cBHDjad4GVdD
CXiq/b4es+zyoxilaGhYaUmqBY8JlFhruJyyEBO9ARG5zMlKSf7+kMVdxyL6qwipQHcvsV
gBfk37i7DUgeZ5hnquh1KBrP9K8smhOvQnOm2qjR53ldgBor58a3TdgOKkWNtcHEnBcxhE
UW11S02mfXM4qqF4y0KvFwwzT6qlAX+mcLSHmQO6OCjrtQOLNksFeM/puJCBQmAWbCotHT
7gQrU21cqKWkE7ZDnmHV9gFUSph42C81D0/v1Y3X8sEmqozhw1p+ZK7qSdB1hhB/zsBbWx
wXO93geMbxxhS9UkBiFkoYckd3nhuCiPLLNVXZIQp6em0sGYyDueZ7q2bMtqQGK4J1LW9D
x3q/7Bz2KFuEeWdrV+0WBN74mubQXAbO8mPCqwxwJYHyd4RJ/07CD1/rAlQtSM3j1pvjjO
/u6oHU7EI9XiYnTEWubgoliFb+9hbKkkm3XzwwPD4YkIBtznaRyX90mPalDmBeT/VJjDT6
8TdqVF3qxLnJOy1BBkZjYYNVqeMAL1DwizAvUmZMhw02N9MAb4481JXbA0Pd9P/fpHbyQL
g827EYYDYe8+GhxtEgIC00B/C4KPsSoDjsSan4bTwUmgQfwjBG5l1LcQ7NI9JdvPGFqQ9q
9tKZTiri6bXxFLZ1S/nWvsDLl1prbkIz8L/pPCY8CogQO8v2BVyjBzHsicoeqm5ifYVXzu
sEM7i5jA==
-----END OPENSSH PRIVATE KEY-----
"
        set public-key "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDRfHBIS14gD0ISvU8wDpaWgNW8EUTvKVQN7aklnnlntO5p0Plzvc8NIda2q0F/9f2VEO2tNgNvN7v17uI1B545lujq7TiGu1bLNwfIleT0wsXl5aa5skPH7tQ7su+P6lSiAcp5fRAszcR3iDZEtKiU5brMIM3cHztvMDPWIv92TBgxpqPn5VXP+zCahiGpk4tQ6XS1wt8KGjY83uS0QdmFxear553wYtu7CJARj9xWZw35ErbtTaPzNX6//KVnHIFdun3oRjEh6f+44rJcdqP+3ZM7e+HCcLPeAC3rK278L7BlOv4MsPNgqJqPkh6tPTUXkxUtiYuW8NomoiJTLcAL"
        set source built-in
    next
    edit "Fortinet_SSH_CA_Untrusted"
        set password ENC ferYEWCIBh6POJ6wWZY8L4dAO38GTDk3/vAGIsa3gmT29DPA4N8fZ19/vEyslTPA3sruWZrHhtoP8wW65kooozgP2RWirWVEea7HKmjwgwoX47QkfyA8D2HK6PToGH5P8UGEudU+rbnwVx1OqGYlzthG74FkBwUAqWV6S6Kg8gz5CXzq6FpBmAZQHm6bHZbtow/DqVlmMjY3dkVA
        set private-key "-----BEGIN OPENSSH PRIVATE KEY-----
b3BlbnNzaC1rZXktdjEAAAAACmFlczI1Ni1jdHIAAAAGYmNyeXB0AAAAGAAAABAAG92akg
Ljr7xnDV9Dnj14AAAAEAAAAAEAAAEXAAAAB3NzaC1yc2EAAAADAQABAAABAQCj4Gl+8JUE
HMm8QtdqLKseG6cqM7VyobpPsoR9KMA6STfQIGyCL5j6et3pqPvGAOk6zMQiyMcjVOwr6c
TaiJcqW4dSQCSg/ivdtr1naAcSVFvFO8j5dM9J+Bsk8Oxm0EkiumpHnnzJjgYZkHWtL04R
FYD4YZOVhdUU6EKsubvfAGZh7E0U09eN/WEZg7pe1daaHFHRUi/QPpRoITJyeDxe3V5vDJ
+sqjzTgCd8vvf5ORjvsBXDoRWa+bBvGqVHWqcO0qcGE8hWlUQ/RAcSX1GYp6vHmqIHRJYy
7ctu0EoJ88M4x7aqlTpjColmSiV+AoEtQsmUDTN+oeYgTTGWP/FlAAADwPL9M16YSyrIng
TtFstFk0mBRauKB1z0SAB+98NjprwleyE3d4SV5anV0Kuh44Ev/7KdC7J72e8tyYcLmCOv
+pcaFs8DdmgYSWZ6IVg5oX/Qe9FyZ6kX6yOpJjecRfnL/u+4fifqaNlnI/iTWk2MxBiSOr
NMRsM30FMq+Ha/xwIGGBaXyTOwQ7mUY/9Bg+sjTsQtzrR2U3qbFOG2h0FZ9fZOCMwipssT
ANYZD8/d9wWn6FMSgUToOEUXzkNMG+fuHwoKwb0jn9VVqZJQDAmwhMpF4QXRvNyQxXIlAs
p2VoAeGfrj/h77KlJK+nZhDg714n9SLbWPq2eYk8vJWDL137wpwUI8WJFr7MXDV97Wd3dB
nZcU5HccPu/eDbJLKtMqoik/N+FPoU29PVLnLZ9u1JB+Xw3Tg2aQW+Szh2xEe6OJoBHHX7
aBnxDqHYiLnUU34f5fElDzlAVQRpsvtOXZ9r+QZf39UdqFy5HUkSDlCl39uOyVibcx0Gx0
8JUeg/zClyq1gTMdUGLPQNsihjlGldoUOMmEpoQ02MWMsMSh4OFpT3BWKw/c/wFiOPiMFY
zrAE2VkyoVFpeNzrXKWpQms6yJPk1wBFN7/z95MHEAQvx9Rj3ZxPTA28ZziSvuZzoze3Hp
1IM4im2tmcQRn/ev8qOBFKDzKL1LoFroAlZUeKcz4kMAKmLT3iuW3gzUmDdhlIaW68lenP
lGhbbRupUOneXLu7JQ5tYyymNFM1WRjOdbDvn3iYDsieXH1lOh7Nfxt5l7WlTLqKdkPqNH
ec+rwsTvfuOtgJa+mNi6MRK9LBZIfo0q756HTYtSbIEG4uDYqZUPVg82n91O4SLWCF6867
S79xdDjCVG/YpFOM1KYPB4HuXwyuitqydQu3S0WY+fjYw2NlMqIUoVVbtTqc4VnCGlzEyu
iQiQJR8/jwoEACGtxg1qjniDsZp5ciMyo5OdnJJ/NVaVKogzLi1N+vSD1ahnzC753baCI+
01YBaetU/B6hy6/Cxm6gEMF6KuJpBJzMzkGuvsFRv612MajlPUQdJv+cYtoGQ3opQIt2mw
KFEJb7MImZ8WQ+YbIBBUK0Tk25xhcT6p3GKcCGa/pDTKurbis6NYUppwWtt1AHSQ8LBGmp
cGUi+NRj7DAgcb8t04pYJHExyO8K77Y7KvbY9LsZ8Om8/nZFu0fs6Jv19u6E+5T5z/8y7A
U5gXr3DOWxgJpjqjrElY+sp7wMz00jIDQbqN9C74qdNyDWgFy+5EzAncENVmi0z6LSai08
RCmcZcLQ==
-----END OPENSSH PRIVATE KEY-----
"
        set public-key "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQCj4Gl+8JUEHMm8QtdqLKseG6cqM7VyobpPsoR9KMA6STfQIGyCL5j6et3pqPvGAOk6zMQiyMcjVOwr6cTaiJcqW4dSQCSg/ivdtr1naAcSVFvFO8j5dM9J+Bsk8Oxm0EkiumpHnnzJjgYZkHWtL04RFYD4YZOVhdUU6EKsubvfAGZh7E0U09eN/WEZg7pe1daaHFHRUi/QPpRoITJyeDxe3V5vDJ+sqjzTgCd8vvf5ORjvsBXDoRWa+bBvGqVHWqcO0qcGE8hWlUQ/RAcSX1GYp6vHmqIHRJYy7ctu0EoJ88M4x7aqlTpjColmSiV+AoEtQsmUDTN+oeYgTTGWP/Fl"
        set source built-in
    next
end
config firewall ssh setting
    set caname "Fortinet_SSH_CA"
    set untrusted-caname "Fortinet_SSH_CA_Untrusted"
    set hostkey-rsa2048 "Fortinet_SSH_RSA2048"
    set hostkey-dsa1024 "Fortinet_SSH_DSA1024"
    set hostkey-ecdsa256 "Fortinet_SSH_ECDSA256"
    set hostkey-ecdsa384 "Fortinet_SSH_ECDSA384"
    set hostkey-ecdsa521 "Fortinet_SSH_ECDSA521"
    set hostkey-ed25519 "Fortinet_SSH_ED25519"
end
config firewall profile-protocol-options
    edit "default"
        set comment "All default services."
        config http
            set ports 80
            unset options
            unset post-lang
        end
        config ftp
            set ports 21
            set options splice
        end
        config imap
            set ports 143
            set options fragmail
        end
        config mapi
            set ports 135
            set options fragmail
        end
        config pop3
            set ports 110
            set options fragmail
        end
        config smtp
            set ports 25
            set options fragmail splice
        end
        config nntp
            set ports 119
            set options splice
        end
        config ssh
            unset options
        end
        config dns
            set ports 53
        end
        config cifs
            set ports 445
            unset options
        end
    next
end
config firewall ssl-ssh-profile
    edit "deep-inspection"
        set comment "Read-only deep inspection profile."
        config https
            set ports 443
            set status deep-inspection
            set quic inspect
        end
        config ftps
            set ports 990
            set status deep-inspection
        end
        config imaps
            set ports 993
            set status deep-inspection
        end
        config pop3s
            set ports 995
            set status deep-inspection
        end
        config smtps
            set ports 465
            set status deep-inspection
        end
        config ssh
            set ports 22
            set status disable
        end
        config dot
            set status disable
            set quic inspect
        end
        config ssl-exempt
            edit 1
                set fortiguard-category 31
            next
            edit 2
                set fortiguard-category 33
            next
            edit 3
                set type wildcard-fqdn
                set wildcard-fqdn "adobe"
            next
            edit 4
                set type wildcard-fqdn
                set wildcard-fqdn "Adobe Login"
            next
            edit 5
                set type wildcard-fqdn
                set wildcard-fqdn "android"
            next
            edit 6
                set type wildcard-fqdn
                set wildcard-fqdn "apple"
            next
            edit 7
                set type wildcard-fqdn
                set wildcard-fqdn "appstore"
            next
            edit 8
                set type wildcard-fqdn
                set wildcard-fqdn "auth.gfx.ms"
            next
            edit 9
                set type wildcard-fqdn
                set wildcard-fqdn "citrix"
            next
            edit 10
                set type wildcard-fqdn
                set wildcard-fqdn "dropbox.com"
            next
            edit 11
                set type wildcard-fqdn
                set wildcard-fqdn "eease"
            next
            edit 12
                set type wildcard-fqdn
                set wildcard-fqdn "firefox update server"
            next
            edit 13
                set type wildcard-fqdn
                set wildcard-fqdn "fortinet"
            next
            edit 14
                set type wildcard-fqdn
                set wildcard-fqdn "googleapis.com"
            next
            edit 15
                set type wildcard-fqdn
                set wildcard-fqdn "google-drive"
            next
            edit 16
                set type wildcard-fqdn
                set wildcard-fqdn "google-play2"
            next
            edit 17
                set type wildcard-fqdn
                set wildcard-fqdn "google-play3"
            next
            edit 18
                set type wildcard-fqdn
                set wildcard-fqdn "Gotomeeting"
            next
            edit 19
                set type wildcard-fqdn
                set wildcard-fqdn "icloud"
            next
            edit 20
                set type wildcard-fqdn
                set wildcard-fqdn "itunes"
            next
            edit 21
                set type wildcard-fqdn
                set wildcard-fqdn "microsoft"
            next
            edit 22
                set type wildcard-fqdn
                set wildcard-fqdn "skype"
            next
            edit 23
                set type wildcard-fqdn
                set wildcard-fqdn "softwareupdate.vmware.com"
            next
            edit 24
                set type wildcard-fqdn
                set wildcard-fqdn "verisign"
            next
            edit 25
                set type wildcard-fqdn
                set wildcard-fqdn "Windows update 2"
            next
            edit 26
                set type wildcard-fqdn
                set wildcard-fqdn "live.com"
            next
            edit 27
                set type wildcard-fqdn
                set wildcard-fqdn "google-play"
            next
            edit 28
                set type wildcard-fqdn
                set wildcard-fqdn "update.microsoft.com"
            next
            edit 29
                set type wildcard-fqdn
                set wildcard-fqdn "swscan.apple.com"
            next
            edit 30
                set type wildcard-fqdn
                set wildcard-fqdn "autoupdate.opera.com"
            next
            edit 31
                set type wildcard-fqdn
                set wildcard-fqdn "cdn-apple"
            next
            edit 32
                set type wildcard-fqdn
                set wildcard-fqdn "mzstatic-apple"
            next
        end
    next
    edit "custom-deep-inspection"
        set comment "Customizable deep inspection profile."
        config https
            set ports 443
            set status certificate-inspection
            set quic bypass
            set expired-server-cert allow
            set revoked-server-cert allow
            set cert-validation-failure allow
            set sni-server-cert-check disable
            set encrypted-client-hello allow
        end
        config ftps
            set status disable
            set expired-server-cert allow
            set revoked-server-cert allow
            set cert-validation-failure allow
        end
        config imaps
            set status disable
            set expired-server-cert allow
            set revoked-server-cert allow
            set cert-validation-failure allow
        end
        config pop3s
            set status disable
            set expired-server-cert allow
            set revoked-server-cert allow
            set cert-validation-failure allow
        end
        config smtps
            set status disable
            set expired-server-cert allow
            set revoked-server-cert allow
            set cert-validation-failure allow
        end
        config ssh
            set ports 22
            set status disable
        end
        config dot
            set status disable
            set quic bypass
            set expired-server-cert allow
            set revoked-server-cert allow
            set cert-validation-failure allow
        end
        set block-blocklisted-certificates disable
    next
    edit "no-inspection"
        set comment "Read-only profile that does no inspection."
        config https
            set status disable
            set quic bypass
        end
        config ftps
            set status disable
        end
        config imaps
            set status disable
        end
        config pop3s
            set status disable
        end
        config smtps
            set status disable
        end
        config ssh
            set ports 22
            set status disable
        end
        config dot
            set status disable
            set quic inspect
        end
    next
    edit "certificate-inspection"
        set comment "Read-only SSL handshake inspection profile."
        config https
            set ports 443
            set status certificate-inspection
            set quic inspect
        end
        config ftps
            set status disable
        end
        config imaps
            set status disable
        end
        config pop3s
            set status disable
        end
        config smtps
            set status disable
        end
        config ssh
            set ports 22
            set status disable
        end
        config dot
            set status disable
            set quic inspect
        end
    next
end
config waf profile
    edit "default"
        config signature
            config main-class 100000000
                set action block
                set severity high
            end
            config main-class 20000000
            end
            config main-class 30000000
                set status enable
                set action block
                set severity high
            end
            config main-class 40000000
            end
            config main-class 50000000
                set status enable
                set action block
                set severity high
            end
            config main-class 60000000
            end
            config main-class 70000000
                set status enable
                set action block
                set severity high
            end
            config main-class 80000000
                set status enable
                set severity low
            end
            config main-class 110000000
                set status enable
                set severity high
            end
            config main-class 90000000
                set status enable
                set action block
                set severity high
            end
            set disabled-signature 80080005 80200001 60030001 60120001 80080003 90410001 90410002
        end
        config constraint
            config header-length
                set status enable
                set log enable
                set severity low
            end
            config content-length
                set status enable
                set log enable
                set severity low
            end
            config param-length
                set status enable
                set log enable
                set severity low
            end
            config line-length
                set status enable
                set log enable
                set severity low
            end
            config url-param-length
                set status enable
                set log enable
                set severity low
            end
            config version
                set log enable
            end
            config method
                set action block
                set log enable
            end
            config hostname
                set action block
                set log enable
            end
            config malformed
                set log enable
            end
            config max-cookie
                set status enable
                set log enable
                set severity low
            end
            config max-header-line
                set status enable
                set log enable
                set severity low
            end
            config max-url-param
                set status enable
                set log enable
                set severity low
            end
            config max-range-segment
                set status enable
                set log enable
                set severity high
            end
        end
    next
end
config casb saas-application
end
config casb user-activity
end
config casb profile
    edit "default"
    next
end
config firewall policy
    edit 1
        set status disable
        set name "l7"
        set uuid f826e734-2cc9-51f0-7073-0e26536668fb
        set srcintf "port7"
        set dstintf "port1"
        set action accept
        set srcaddr "all"
        set dstaddr "all"
        set schedule "always"
        set service "ALL"
        set utm-status enable
        set ssl-ssh-profile "certificate-inspection"
        set webfilter-profile "色情网站过滤"
        set nat enable
        set comments "放行port7到port1流量，启用snat（保持源端口），并过滤色情网站。该规则已禁用。"
    next
    edit 2
        set status disable
        set name "成人网站过滤策略"
        set uuid e52f4162-3c2d-51f0-454b-1aef8c5925ee
        set srcintf "port7"
        set dstintf "port1"
        set action accept
        set srcaddr "none"
        set dstaddr "none"
        set schedule "always"
        set service "ALL"
        set utm-status enable
        set webfilter-profile "色情网站过滤"
        set nat enable
        set comments "放行port7到port1流量，源目的都是none，启用snat（保持源端口），并过滤色情网站。该规则已禁用。"
    next
    edit 3
        set name "tct-single-deny"
        set uuid 504957e2-52f6-51f0-6f49-22a50934f0d1
        set srcintf "port7"
        set dstintf "port1"
        set srcaddr "tct-addr-net-150"
        set dstaddr "tct-addr-net-160"
        set schedule "always"
        set service "SMB"
        set logtraffic disable
        set comments "拒绝port7到port1，从tct-addr-net-150到tct-addr-net-160的SMB流量。"
    next
    edit 4
        set name "tct-single-permt"
        set uuid 541369d4-531a-51f0-8cad-a29017008371
        set srcintf "tct-vlan103"
        set dstintf "port1"
        set action accept
        set srcaddr "tct-addr-net-140"
        set dstaddr "tct-addr-net-160"
        set schedule "always"
        set service "HTTP" "HTTPS"
        set comments "放行tct-vlan103到port1，tct-addr-net-140到tct-addr-net-160的HTTP-HTTPS流量，没有NAT规则，无安全检查"
    next
    edit 5
        set name "tct-snat"
        set uuid aeaa0d90-532d-51f0-fc62-31dbaa318f35
        set srcintf "port2"
        set dstintf "port6"
        set action accept
        set srcaddr "tct-addr-net-140"
        set dstaddr "all"
        set schedule "always"
        set service "ALL"
        set utm-status enable
        set av-profile "default"
        set ips-sensor "default"
        set nat enable
        set comments "port2到port6，来之tct-addr-net-140的做snat，同时开启AV，IPS；使用出接口，保持源端口"
    next
    edit 6
        set status disable
        set name "tct-test-disable"
        set uuid b134e714-532e-51f0-d0da-8d35a92d5278
        set srcintf "port3"
        set dstintf "port4"
        set action accept
        set srcaddr "tct-addr-net-150"
        set dstaddr "tct-addr-net-170-1-10"
        set schedule "always"
        set service "FTP"
        set comments "放行port3到port4，tct-addr-net-150到tct-addr-net170-1-10的ftp流量，不做nat，该规则暂时禁用"
    next
    edit 7
        set name "tct-dnat"
        set uuid 78da1348-532f-51f0-e8dc-d5d7c46a32c6
        set srcintf "port7"
        set dstintf "port4"
        set action accept
        set srcaddr "all"
        set dstaddr "tct-vip-addr1"
        set schedule "always"
        set service "HTTPS"
        set utm-status enable
        set ips-sensor "default"
        set comments "将来自port7，目的地址是***************，出接口是port4的https流，dnat到*********。同时开启IPS。"
    next
    edit 8
        set name "tct-test-zone"
        set uuid b98dc324-557f-51f0-1001-3b10a7c9eab3
        set srcintf "tct-zone-1"
        set dstintf "port4"
        set action accept
        set srcaddr "tct-addr-net-160"
        set dstaddr "tct-addr-net-150"
        set schedule "worker-day"
        set service "ALL"
        set nat enable
        set comments "放行从区域tct-zone-1到port4，tct-addr-net-160到tct-addr-net-150的流量，时间限制在工作日，同时开启snat"
    next
    edit 9
        set name "test-iprange-service-policy"
        set uuid *************-7777-6666-************
        set srcintf "port1"
        set dstintf "port2"
        set action accept
        set srcaddr "tct-addr-net-150"
        set dstaddr "tct-addr-net-160"
        set schedule "always"
        set service "service-test01"
        set comments "测试包含iprange的服务对象在策略中的使用"
    next
    edit 10
        set name "test-mixed-iprange-services"
        set uuid *************-6666-5555-************
        set srcintf "port2"
        set dstintf "port3"
        set action accept
        set srcaddr "tct-addr-net-160"
        set dstaddr "tct-addr-net-150"
        set schedule "always"
        set service "service-test01" "HTTP" "service-test02"
        set comments "测试混合服务策略，包含多个iprange服务"
    next
end
config firewall sniffer
    edit 1
        set uuid 6678e318-2ccf-51f0-6246-6d92df8e59d2
        set interface "port5"
        set ips-sensor-status enable
        set ips-sensor "sniffer-profile"
    next
    edit 2
        set uuid 441de7d2-401b-51f0-55d3-cf9b7b09348e
        set interface "x7"
        set ips-sensor-status enable
        set ips-sensor "sniffer-profile"
        set av-profile-status enable
        set av-profile "sniffer-profile"
    next
    edit 3
        set uuid 60b29b50-4024-51f0-a492-0b80c4344e92
        set interface "x8"
        set ips-sensor-status enable
        set ips-sensor "sniffer-profile"
        set av-profile-status enable
        set av-profile "sniffer-profile"
    next
end
config firewall on-demand-sniffer
    edit "port5_root"
        set interface "port5"
        set max-packet-count 5000
    next
end
config switch-controller security-policy 802-1X
    edit "802-1X-policy-default"
        set user-group "SSO_Guest_Users"
        set mac-auth-bypass disable
        set open-auth disable
        set eap-passthru enable
        set eap-auto-untagged-vlans enable
        set guest-vlan disable
        set auth-fail-vlan disable
        set framevid-apply enable
        set radius-timeout-overwrite disable
        set authserver-timeout-vlan disable
        set dacl disable
    next
end
config switch-controller security-policy local-access
    edit "default"
        set mgmt-allowaccess https ping ssh
        set internal-allowaccess https ping ssh
    next
end
config switch-controller lldp-profile
    edit "default"
        set med-tlvs inventory-management network-policy location-identification
        set auto-isl disable
        config med-network-policy
            edit "voice"
            next
            edit "voice-signaling"
            next
            edit "guest-voice"
            next
            edit "guest-voice-signaling"
            next
            edit "softphone-voice"
            next
            edit "video-conferencing"
            next
            edit "streaming-video"
            next
            edit "video-signaling"
            next
        end
        config med-location-service
            edit "coordinates"
            next
            edit "address-civic"
            next
            edit "elin-number"
            next
        end
    next
    edit "default-auto-isl"
    next
    edit "default-auto-mclag-icl"
        set auto-mclag-icl enable
    next
end
config switch-controller qos dot1p-map
    edit "voice-dot1p"
        set priority-0 queue-4
        set priority-1 queue-4
        set priority-2 queue-3
        set priority-3 queue-2
        set priority-4 queue-3
        set priority-5 queue-1
        set priority-6 queue-2
        set priority-7 queue-2
    next
end
config switch-controller qos ip-dscp-map
    edit "voice-dscp"
        config map
            edit "1"
                set cos-queue 1
                set value 46
            next
            edit "2"
                set cos-queue 2
                set value 24,26,48,56
            next
            edit "5"
                set cos-queue 3
                set value 34
            next
        end
    next
end
config switch-controller qos queue-policy
    edit "default"
        set schedule round-robin
        set rate-by kbps
        config cos-queue
            edit "queue-0"
            next
            edit "queue-1"
            next
            edit "queue-2"
            next
            edit "queue-3"
            next
            edit "queue-4"
            next
            edit "queue-5"
            next
            edit "queue-6"
            next
            edit "queue-7"
            next
        end
    next
    edit "voice-egress"
        set schedule weighted
        set rate-by kbps
        config cos-queue
            edit "queue-0"
            next
            edit "queue-1"
                set weight 0
            next
            edit "queue-2"
                set weight 6
            next
            edit "queue-3"
                set weight 37
            next
            edit "queue-4"
                set weight 12
            next
            edit "queue-5"
            next
            edit "queue-6"
            next
            edit "queue-7"
            next
        end
    next
end
config switch-controller qos qos-policy
    edit "default"
    next
    edit "voice-qos"
        set trust-dot1p-map "voice-dot1p"
        set trust-ip-dscp-map "voice-dscp"
        set queue-policy "voice-egress"
    next
end
config switch-controller storm-control-policy
    edit "default"
        set description "default storm control on all port"
    next
    edit "auto-config"
        set description "storm control policy for fortilink-isl-icl port"
        set storm-control-mode disabled
    next
end
config switch-controller auto-config policy
    edit "pse"
    next
    edit "default"
    next
    edit "default-icl"
        set poe-status disable
        set igmp-flood-report enable
        set igmp-flood-traffic enable
    next
end
config switch-controller initial-config template
    edit "_default"
        set vlanid 1
    next
    edit "quarantine"
        set vlanid 4093
        set dhcp-server enable
    next
    edit "rspan"
        set vlanid 4092
        set dhcp-server enable
    next
    edit "voice"
        set vlanid 4091
    next
    edit "video"
        set vlanid 4090
    next
    edit "onboarding"
        set vlanid 4089
    next
    edit "nac_segment"
        set vlanid 4088
        set dhcp-server enable
    next
end
config switch-controller switch-profile
    edit "default"
    next
end
config switch-controller ptp profile
    edit "default"
    next
end
config switch-controller ptp interface-policy
    edit "default"
    next
end
config switch-controller remote-log
    edit "syslogd"
    next
    edit "syslogd2"
    next
end
config wireless-controller setting
    set darrp-optimize-schedules "default-darrp-optimize"
end
config wireless-controller arrp-profile
    edit "arrp-default"
    next
end
config wireless-controller wids-profile
    edit "default"
        set comment "Default WIDS profile."
        set ap-scan enable
        set wireless-bridge enable
        set deauth-broadcast enable
        set null-ssid-probe-resp enable
        set long-duration-attack enable
        set invalid-mac-oui enable
        set weak-wep-iv enable
        set auth-frame-flood enable
        set assoc-frame-flood enable
        set spoofed-deauth enable
        set asleap-attack enable
        set eapol-start-flood enable
        set eapol-logoff-flood enable
        set eapol-succ-flood enable
        set eapol-fail-flood enable
        set eapol-pre-succ-flood enable
        set eapol-pre-fail-flood enable
    next
    edit "default-wids-apscan-enabled"
        set ap-scan enable
    next
end
config wireless-controller ble-profile
    edit "fortiap-discovery"
        set advertising ibeacon eddystone-uid eddystone-url
        set ibeacon-uuid "wtp-uuid"
    next
end
config router rip
    config redistribute "connected"
    end
    config redistribute "static"
    end
    config redistribute "ospf"
    end
    config redistribute "bgp"
    end
    config redistribute "isis"
    end
end
config router ripng
    config redistribute "connected"
    end
    config redistribute "static"
    end
    config redistribute "ospf"
    end
    config redistribute "bgp"
    end
    config redistribute "isis"
    end
end
config router static
    edit 1
        set gateway ***********
        set distance 1
        set device "mgmt"
    next
end
config router ospf
    config redistribute "connected"
    end
    config redistribute "static"
    end
    config redistribute "rip"
    end
    config redistribute "bgp"
    end
    config redistribute "isis"
    end
end
config router ospf6
    config redistribute "connected"
    end
    config redistribute "static"
    end
    config redistribute "rip"
    end
    config redistribute "bgp"
    end
    config redistribute "isis"
    end
end
config router bgp
    config redistribute "connected"
    end
    config redistribute "rip"
    end
    config redistribute "ospf"
    end
    config redistribute "static"
    end
    config redistribute "isis"
    end
    config redistribute6 "connected"
    end
    config redistribute6 "rip"
    end
    config redistribute6 "ospf"
    end
    config redistribute6 "static"
    end
    config redistribute6 "isis"
    end
end
config router isis
    config redistribute "connected"
    end
    config redistribute "rip"
    end
    config redistribute "ospf"
    end
    config redistribute "bgp"
    end
    config redistribute "static"
    end
    config redistribute6 "connected"
    end
    config redistribute6 "rip"
    end
    config redistribute6 "ospf"
    end
    config redistribute6 "bgp"
    end
    config redistribute6 "static"
    end
end
config router multicast
end
