#config-version=FG6H1E-7.4.8-FW-build2795-250523:opmode=0:vdom=0:user=admin
#conf_file_ver=257861246577264
#buildno=2795
#global_vdom=1
config system global
    set alias "FortiGate-601E"
    set gui-auto-upgrade-setup-warning disable
    set gui-device-latitude "36.708010"
    set gui-device-longitude "-119.555970"
    set hostname "FortiGate-601E"
    set language simch
    set switch-controller enable
    set timezone "US/Pacific"
end
config system accprofile
    edit "prof_admin"
        set secfabgrp read-write
        set ftviewgrp read-write
        set authgrp read-write
        set sysgrp read-write
        set netgrp read-write
        set loggrp read-write
        set fwgrp read-write
        set vpngrp read-write
        set utmgrp read-write
        set wanoptgrp read-write
        set wifi read-write
        set cli-get enable
        set cli-show enable
        set cli-exec enable
        set cli-config enable
    next
end
config system np6
    edit "np6_0"
    next
end
config system interface
    edit "ha"
        set vdom "root"
        set type physical
        set snmp-index 1
    next
    edit "mgmt"
        set vdom "root"
        set allowaccess ping https ssh snmp http fabric ftm speed-test
        set type physical
        set dedicated-to management
        set role lan
        set snmp-index 2
    next
    edit "port1"
        set vdom "root"
        set allowaccess ping https ssh snmp http radius-acct fabric ftm speed-test
        set type physical
        set snmp-index 3
    next
    edit "port2"
        set vdom "root"
        set type physical
        set snmp-index 4
    next
    edit "port3"
        set vdom "root"
        set allowaccess ping https ssh snmp http ftm speed-test
        set type physical
        set snmp-index 5
    next
    edit "port4"
        set vdom "root"
        set type physical
        set snmp-index 6
    next
    edit "port5"
        set vdom "root"
        set allowaccess ping https ssh snmp http radius-acct fabric ftm speed-test
        set type physical
        set snmp-index 7
    next
    edit "port6"
        set vdom "root"
        set type physical
        set snmp-index 8
    next
    edit "port7"
        set vdom "root"
        set type physical
        set snmp-index 9
    next
    edit "port8"
        set vdom "root"
        set type physical
        set snmp-index 10
    next
    edit "port9"
        set vdom "root"
        set type physical
        set snmp-index 11
    next
    edit "port10"
        set vdom "root"
        set type physical
        set snmp-index 12
    next
    edit "port11"
        set vdom "root"
        set type physical
        set snmp-index 13
    next
    edit "port12"
        set vdom "root"
        set type physical
        set snmp-index 14
    next
    edit "s1"
        set vdom "root"
        set ips-sniffer-mode enable
        set type physical
        set snmp-index 15
    next
    edit "s2"
        set vdom "root"
        set ips-sniffer-mode enable
        set type physical
        set snmp-index 16
    next
    edit "vw1"
        set vdom "root"
        set type physical
        set snmp-index 17
    next
    edit "vw2"
        set vdom "root"
        set type physical
        set snmp-index 18
    next
    edit "x1"
        set vdom "root"
        set type physical
        set snmp-index 19
    next
    edit "x2"
        set vdom "root"
        set type physical
        set snmp-index 20
    next
    edit "modem"
        set vdom "root"
        set status down
        set type physical
        set snmp-index 21
    next
    edit "fortilink"
        set vdom "root"
        set allowaccess ping fabric
        set status down
        set type aggregate
        set lldp-reception enable
        set lldp-transmission enable
        set snmp-index 25
    next
end
config system custom-language
    edit "en"
        set filename "en"
    next
    edit "fr"
        set filename "fr"
    next
    edit "sp"
        set filename "sp"
    next
    edit "pg"
        set filename "pg"
    next
    edit "x-sjis"
        set filename "x-sjis"
    next
    edit "big5"
        set filename "big5"
    next
    edit "GB2312"
        set filename "GB2312"
    next
    edit "euc-kr"
        set filename "euc-kr"
    next
end
config system admin
    edit "admin"
        set old-password ENC SH2CdSSDCVVS6BGzyvsbovUmUQondYFu4LOxGTBvO9R//0yaH1sdLzdLBN087M=
        set accprofile "super_admin"
        set vdom "root"
        config gui-dashboard
            edit 11
                set name "状态"
                set vdom "root"
                set permanent enable
                config widget
                    edit 1
                        set width 1
                        set height 1
                    next
                    edit 2
                        set type licinfo
                        set x-pos 1
                        set width 1
                        set height 1
                    next
                    edit 3
                        set type forticloud
                        set x-pos 2
                        set width 1
                        set height 1
                    next
                    edit 4
                        set type security-fabric
                        set x-pos 3
                        set width 1
                        set height 1
                    next
                    edit 5
                        set type admins
                        set x-pos 4
                        set width 1
                        set height 1
                    next
                    edit 6
                        set type cpu-usage
                        set x-pos 5
                        set width 2
                        set height 1
                    next
                    edit 7
                        set type memory-usage
                        set x-pos 6
                        set width 2
                        set height 1
                    next
                    edit 8
                        set type sessions
                        set x-pos 7
                        set width 2
                        set height 1
                    next
                end
            next
            edit 12
                set name "安全"
                set vdom "root"
                config widget
                    edit 1
                        set type fortiview
                        set width 2
                        set height 1
                        set fortiview-type "compromisedHosts"
                        set fortiview-sort-by "verdict"
                        set fortiview-timeframe "hour"
                        set fortiview-visualization "table"
                    next
                    edit 2
                        set type fortiview
                        set x-pos 1
                        set width 2
                        set height 1
                        set fortiview-type "threats"
                        set fortiview-sort-by "threatLevel"
                        set fortiview-timeframe "hour"
                        set fortiview-visualization "table"
                    next
                    edit 3
                        set type device-vulnerability
                        set x-pos 2
                        set width 2
                        set height 1
                    next
                end
            next
            edit 13
                set name "网络"
                set vdom "root"
                config widget
                    edit 1
                        set type routing
                        set width 2
                        set height 1
                        set router-view-type "staticdynamic"
                    next
                    edit 2
                        set type dhcp
                        set x-pos 1
                        set width 2
                        set height 1
                    next
                    edit 3
                        set type virtual-wan
                        set x-pos 2
                        set width 2
                        set height 1
                    next
                    edit 4
                        set type ipsec-vpn
                        set x-pos 3
                        set width 2
                        set height 1
                    next
                end
            next
            edit 14
                set name "Assets & Identities"
                set vdom "root"
                config widget
                    edit 1
                        set type device-inventory
                        set width 2
                        set height 1
                    next
                    edit 2
                        set type identities
                        set x-pos 1
                        set width 2
                        set height 1
                    next
                    edit 3
                        set type firewall-user
                        set x-pos 2
                        set width 2
                        set height 1
                    next
                    edit 4
                        set type quarantine
                        set x-pos 3
                        set width 2
                        set height 1
                    next
                    edit 5
                        set type nac-vlans
                        set x-pos 4
                        set width 2
                        set height 1
                    next
                end
            next
            edit 15
                set name "WiFi"
                set vdom "root"
                config widget
                    edit 1
                        set type ap-status
                        set width 2
                        set height 1
                    next
                    edit 2
                        set type channel-utilization
                        set x-pos 1
                        set width 2
                        set height 1
                        set wifi-band "both"
                    next
                    edit 3
                        set type clients-by-ap
                        set x-pos 2
                        set width 2
                        set height 1
                        set wifi-band "both"
                    next
                    edit 4
                        set type client-signal-strength
                        set x-pos 3
                        set width 2
                        set height 1
                        set wifi-band "both"
                    next
                    edit 5
                        set type rogue-ap
                        set x-pos 4
                        set width 2
                        set height 1
                    next
                    edit 6
                        set type historical-clients
                        set x-pos 5
                        set width 2
                        set height 1
                        set wifi-band "both"
                    next
                    edit 7
                        set type interfering-ssids
                        set x-pos 6
                        set width 2
                        set height 1
                        set wifi-band "both"
                    next
                    edit 8
                        set type wifi-login-failures
                        set x-pos 7
                        set width 2
                        set height 1
                    next
                end
            next
            edit 16
                set name "FortiView源"
                set vdom "root"
                set layout-type standalone
                set csf disable
                config widget
                    edit 1
                        set type fortiview
                        set width 6
                        set height 3
                        set fortiview-type "source"
                        set fortiview-sort-by "bytes"
                        set fortiview-timeframe "hour"
                        set fortiview-visualization "table"
                    next
                end
            next
            edit 17
                set name "FortiView目的"
                set vdom "root"
                set layout-type standalone
                set csf disable
                config widget
                    edit 1
                        set type fortiview
                        set width 6
                        set height 3
                        set fortiview-type "destination"
                        set fortiview-sort-by "bytes"
                        set fortiview-timeframe "hour"
                        set fortiview-visualization "table"
                    next
                end
            next
            edit 18
                set name "FortiView应用"
                set vdom "root"
                set layout-type standalone
                set csf disable
                config widget
                    edit 1
                        set type fortiview
                        set width 6
                        set height 3
                        set fortiview-type "application"
                        set fortiview-sort-by "bytes"
                        set fortiview-timeframe "hour"
                        set fortiview-visualization "table"
                    next
                end
            next
            edit 19
                set name "FortiView网站"
                set vdom "root"
                set layout-type standalone
                set csf disable
                config widget
                    edit 1
                        set type fortiview
                        set width 6
                        set height 3
                        set fortiview-type "website"
                        set fortiview-sort-by "sessions"
                        set fortiview-timeframe "hour"
                        set fortiview-visualization "table"
                    next
                end
            next
            edit 20
                set name "FortiView策略"
                set vdom "root"
                set layout-type standalone
                set csf disable
                config widget
                    edit 1
                        set type fortiview
                        set width 6
                        set height 3
                        set fortiview-type "policy"
                        set fortiview-sort-by "bytes"
                        set fortiview-timeframe "hour"
                        set fortiview-visualization "table"
                    next
                end
            next
            edit 21
                set name "FortiView会话"
                set vdom "root"
                set layout-type standalone
                set csf disable
                config widget
                    edit 1
                        set type fortiview
                        set width 6
                        set height 3
                        set fortiview-type "realtimeSessions"
                        set fortiview-sort-by "bytes"
                        set fortiview-timeframe "realtime"
                        set fortiview-visualization "table"
                    next
                end
            next
        end
        set gui-default-dashboard-template "minimal"
        set gui-ignore-release-overview-version "7.4"
        set password ENC PB2Pg/AR6uMSfRxOiE+3awXgKXNLNWrWbYrBu4gBh1AJ6waRFW7WB1Z8xRhqUugkpkiV8TtKxsZr4+YWzaedEtakA0qLsXKA0Kr2oLWWAf1Wsg=
    next
end
config system sso-admin
end
config system ha
    set override disable
end
config system storage
    edit "SSD1"
        set status enable
        set media-status enable
        set order 1
        set partition "LOGUSEDXEDAF30B4"
        set device "/dev/sda1"
        set size 225340
        set usage log
    next
    edit "SSD2"
        set status enable
        set media-status enable
        set order 2
        set partition "WANOPTXX928FD0B5"
        set device "/dev/sdb1"
        set size 64220
        set usage wanopt
        set wanopt-mode mix
    next
end
config system dns
    set primary ***********
    set secondary ***********
    set protocol dot
    set server-hostname "globalsdns.fortinet.net"
end
config system replacemsg-image
    edit "logo_fnet"
    next
    edit "logo_fguard_wf"
    next
    edit "logo_v3_fguard_app"
    next
end
config system replacemsg mail "partial"
end
config system replacemsg http "url-block"
end
config system replacemsg http "urlfilter-err"
end
config system replacemsg http "infcache-block"
end
config system replacemsg http "http-contenttypeblock"
end
config system replacemsg http "https-invalid-cert-block"
end
config system replacemsg http "https-untrusted-cert-block"
end
config system replacemsg http "https-blocklisted-cert-block"
end
config system replacemsg http "https-ech-block"
end
config system replacemsg http "switching-protocols-block"
end
config system replacemsg http "http-antiphish-block"
end
config system replacemsg http "videofilter-block"
end
config system replacemsg webproxy "deny"
end
config system replacemsg webproxy "user-limit"
end
config system replacemsg webproxy "auth-challenge"
end
config system replacemsg webproxy "auth-login-fail"
end
config system replacemsg webproxy "auth-group-info-fail"
end
config system replacemsg webproxy "http-err"
end
config system replacemsg webproxy "auth-ip-blackout"
end
config system replacemsg webproxy "ztna-invalid-cert"
end
config system replacemsg webproxy "ztna-empty-cert"
end
config system replacemsg webproxy "ztna-manageable-empty-cert"
end
config system replacemsg webproxy "ztna-no-api-gwy-matched"
end
config system replacemsg webproxy "ztna-cant-find-real-srv"
end
config system replacemsg webproxy "ztna-fqdn-dns-failed"
end
config system replacemsg webproxy "ztna-ssl-bookmark-failed"
end
config system replacemsg webproxy "ztna-no-policy-matched"
end
config system replacemsg webproxy "ztna-matched-deny-policy"
end
config system replacemsg webproxy "ztna-client-cert-revoked"
end
config system replacemsg webproxy "ztna-denied-by-matched-tags"
end
config system replacemsg webproxy "ztna-denied-no-matched-tags"
end
config system replacemsg webproxy "ztna-no-dev-info"
end
config system replacemsg webproxy "ztna-dev-is-offline"
end
config system replacemsg webproxy "ztna-dev-is-unmanageable"
end
config system replacemsg webproxy "ztna-auth-fail"
end
config system replacemsg webproxy "casb-block"
end
config system replacemsg webproxy "swp-empty-cert"
end
config system replacemsg webproxy "swp-manageable-empty-cert"
end
config system replacemsg ftp "ftp-explicit-banner"
end
config system replacemsg fortiguard-wf "ftgd-block"
end
config system replacemsg fortiguard-wf "ftgd-ovrd"
end
config system replacemsg fortiguard-wf "ftgd-quota"
end
config system replacemsg fortiguard-wf "ftgd-warning"
end
config system replacemsg spam "ipblocklist"
end
config system replacemsg spam "smtp-spam-dnsbl"
end
config system replacemsg spam "smtp-spam-feip"
end
config system replacemsg spam "smtp-spam-helo"
end
config system replacemsg spam "smtp-spam-emailblock-to"
end
config system replacemsg spam "smtp-spam-emailblock-from"
end
config system replacemsg spam "smtp-spam-emailblock-subject"
end
config system replacemsg spam "smtp-spam-mimeheader"
end
config system replacemsg spam "reversedns"
end
config system replacemsg spam "smtp-spam-ase"
end
config system replacemsg spam "submit"
end
config system replacemsg alertmail "alertmail-virus"
end
config system replacemsg alertmail "alertmail-block"
end
config system replacemsg alertmail "alertmail-nids-event"
end
config system replacemsg alertmail "alertmail-crit-event"
end
config system replacemsg alertmail "alertmail-disk-full"
end
config system replacemsg admin "pre_admin-disclaimer-text"
end
config system replacemsg admin "post_admin-disclaimer-text"
end
config system replacemsg auth "auth-disclaimer-page-1"
end
config system replacemsg auth "auth-disclaimer-page-2"
end
config system replacemsg auth "auth-disclaimer-page-3"
end
config system replacemsg auth "auth-proxy-reject-page"
end
config system replacemsg auth "auth-reject-page"
end
config system replacemsg auth "auth-login-page"
end
config system replacemsg auth "auth-login-failed-page"
end
config system replacemsg auth "auth-token-login-page"
end
config system replacemsg auth "auth-token-login-failed-page"
end
config system replacemsg auth "auth-success-msg"
end
config system replacemsg auth "auth-challenge-page"
end
config system replacemsg auth "auth-keepalive-page"
end
config system replacemsg auth "auth-portal-page"
end
config system replacemsg auth "auth-password-page"
end
config system replacemsg auth "auth-fortitoken-page"
end
config system replacemsg auth "auth-next-fortitoken-page"
end
config system replacemsg auth "auth-email-token-page"
end
config system replacemsg auth "auth-sms-token-page"
end
config system replacemsg auth "auth-email-harvesting-page"
end
config system replacemsg auth "auth-email-failed-page"
end
config system replacemsg auth "auth-cert-passwd-page"
end
config system replacemsg auth "auth-guest-print-page"
end
config system replacemsg auth "auth-guest-email-page"
end
config system replacemsg auth "auth-success-page"
end
config system replacemsg auth "auth-block-notification-page"
end
config system replacemsg auth "auth-quarantine-page"
end
config system replacemsg auth "auth-qtn-reject-page"
end
config system replacemsg auth "auth-saml-page"
end
config system replacemsg sslvpn "sslvpn-login"
end
config system replacemsg sslvpn "sslvpn-header"
end
config system replacemsg sslvpn "sslvpn-limit"
end
config system replacemsg sslvpn "hostcheck-error"
end
config system replacemsg sslvpn "sslvpn-provision-user"
end
config system replacemsg sslvpn "sslvpn-provision-user-sms"
end
config system replacemsg nac-quar "nac-quar-virus"
end
config system replacemsg nac-quar "nac-quar-dos"
end
config system replacemsg nac-quar "nac-quar-ips"
end
config system replacemsg nac-quar "nac-quar-dlp"
end
config system replacemsg nac-quar "nac-quar-admin"
end
config system replacemsg nac-quar "nac-quar-app"
end
config system replacemsg traffic-quota "per-ip-shaper-block"
end
config system replacemsg utm "virus-html"
end
config system replacemsg utm "client-virus-html"
end
config system replacemsg utm "virus-text"
end
config system replacemsg utm "dlp-html"
end
config system replacemsg utm "dlp-text"
end
config system replacemsg utm "appblk-html"
end
config system replacemsg utm "ipsblk-html"
end
config system replacemsg utm "virpatchblk-html"
end
config system replacemsg utm "ipsfail-html"
end
config system replacemsg utm "exe-text"
end
config system replacemsg utm "waf-html"
end
config system replacemsg utm "outbreak-prevention-html"
end
config system replacemsg utm "outbreak-prevention-text"
end
config system replacemsg utm "external-blocklist-html"
end
config system replacemsg utm "external-blocklist-text"
end
config system replacemsg utm "ems-threat-feed-html"
end
config system replacemsg utm "ems-threat-feed-text"
end
config system replacemsg utm "file-filter-html"
end
config system replacemsg utm "file-filter-text"
end
config system replacemsg utm "file-size-text"
end
config system replacemsg utm "transfer-size-text"
end
config system replacemsg utm "internal-error-text"
end
config system replacemsg utm "archive-block-html"
end
config system replacemsg utm "archive-block-text"
end
config system replacemsg utm "file-av-fail-text"
end
config system replacemsg utm "transfer-av-fail-text"
end
config system replacemsg utm "banned-word-html"
end
config system replacemsg utm "banned-word-text"
end
config system replacemsg utm "block-html"
end
config system replacemsg utm "block-text"
end
config system replacemsg utm "decompress-limit-text"
end
config system replacemsg utm "dlp-subject-text"
end
config system replacemsg utm "file-size-html"
end
config system replacemsg utm "client-file-size-html"
end
config system replacemsg utm "inline-scan-timeout-html"
end
config system replacemsg utm "inline-scan-timeout-text"
end
config system replacemsg utm "inline-scan-error-html"
end
config system replacemsg utm "inline-scan-error-text"
end
config system replacemsg utm "icap-block-text"
end
config system replacemsg utm "icap-error-text"
end
config system replacemsg utm "icap-http-error"
end
config system replacemsg icap "icap-req-resp"
end
config system replacemsg automation "automation-email"
end
config system snmp sysinfo
end
config system central-management
    set type fortiguard
end
config firewall internet-service-name
    edit "Google-Other"
        set internet-service-id 65536
    next
    edit "Google-Web"
        set internet-service-id 65537
    next
    edit "Google-ICMP"
        set internet-service-id 65538
    next
    edit "Google-DNS"
        set internet-service-id 65539
    next
    edit "Google-Outbound_Email"
        set internet-service-id 65540
    next
    edit "Google-SSH"
        set internet-service-id 65542
    next
    edit "Google-FTP"
        set internet-service-id 65543
    next
    edit "Google-NTP"
        set internet-service-id 65544
    next
    edit "Google-Inbound_Email"
        set internet-service-id 65545
    next
    edit "Google-LDAP"
        set internet-service-id 65550
    next
    edit "Google-NetBIOS.Session.Service"
        set internet-service-id 65551
    next
    edit "Google-RTMP"
        set internet-service-id 65552
    next
    edit "Google-NetBIOS.Name.Service"
        set internet-service-id 65560
    next
    edit "Google-Google.Cloud"
        set internet-service-id 65641
    next
    edit "Google-Google.Bot"
        set internet-service-id 65643
    next
    edit "Google-Gmail"
        set internet-service-id 65646
    next
    edit "Facebook-Other"
        set internet-service-id 131072
    next
    edit "Facebook-Web"
        set internet-service-id 131073
    next
    edit "Facebook-ICMP"
        set internet-service-id 131074
    next
    edit "Facebook-DNS"
        set internet-service-id 131075
    next
    edit "Facebook-Outbound_Email"
        set internet-service-id 131076
    next
    edit "Facebook-SSH"
        set internet-service-id 131078
    next
    edit "Facebook-FTP"
        set internet-service-id 131079
    next
    edit "Facebook-NTP"
        set internet-service-id 131080
    next
    edit "Facebook-Inbound_Email"
        set internet-service-id 131081
    next
    edit "Facebook-LDAP"
        set internet-service-id 131086
    next
    edit "Facebook-NetBIOS.Session.Service"
        set internet-service-id 131087
    next
    edit "Facebook-RTMP"
        set internet-service-id 131088
    next
    edit "Facebook-NetBIOS.Name.Service"
        set internet-service-id 131096
    next
    edit "Facebook-Whatsapp"
        set internet-service-id 131184
    next
    edit "Facebook-Instagram"
        set internet-service-id 131189
    next
    edit "Apple-Other"
        set internet-service-id 196608
    next
    edit "Apple-Web"
        set internet-service-id 196609
    next
    edit "Apple-ICMP"
        set internet-service-id 196610
    next
    edit "Apple-DNS"
        set internet-service-id 196611
    next
    edit "Apple-Outbound_Email"
        set internet-service-id 196612
    next
    edit "Apple-SSH"
        set internet-service-id 196614
    next
    edit "Apple-FTP"
        set internet-service-id 196615
    next
    edit "Apple-NTP"
        set internet-service-id 196616
    next
    edit "Apple-Inbound_Email"
        set internet-service-id 196617
    next
    edit "Apple-LDAP"
        set internet-service-id 196622
    next
    edit "Apple-NetBIOS.Session.Service"
        set internet-service-id 196623
    next
    edit "Apple-RTMP"
        set internet-service-id 196624
    next
    edit "Apple-NetBIOS.Name.Service"
        set internet-service-id 196632
    next
    edit "Apple-App.Store"
        set internet-service-id 196723
    next
    edit "Apple-APNs"
        set internet-service-id 196747
    next
    edit "Yahoo-Other"
        set internet-service-id 262144
    next
    edit "Yahoo-Web"
        set internet-service-id 262145
    next
    edit "Yahoo-ICMP"
        set internet-service-id 262146
    next
    edit "Yahoo-DNS"
        set internet-service-id 262147
    next
    edit "Yahoo-Outbound_Email"
        set internet-service-id 262148
    next
    edit "Yahoo-SSH"
        set internet-service-id 262150
    next
    edit "Yahoo-FTP"
        set internet-service-id 262151
    next
    edit "Yahoo-NTP"
        set internet-service-id 262152
    next
    edit "Yahoo-Inbound_Email"
        set internet-service-id 262153
    next
    edit "Yahoo-LDAP"
        set internet-service-id 262158
    next
    edit "Yahoo-NetBIOS.Session.Service"
        set internet-service-id 262159
    next
    edit "Yahoo-RTMP"
        set internet-service-id 262160
    next
    edit "Yahoo-NetBIOS.Name.Service"
        set internet-service-id 262168
    next
    edit "Microsoft-Other"
        set internet-service-id 327680
    next
    edit "Microsoft-Web"
        set internet-service-id 327681
    next
    edit "Microsoft-ICMP"
        set internet-service-id 327682
    next
    edit "Microsoft-DNS"
        set internet-service-id 327683
    next
    edit "Microsoft-Outbound_Email"
        set internet-service-id 327684
    next
    edit "Microsoft-SSH"
        set internet-service-id 327686
    next
    edit "Microsoft-FTP"
        set internet-service-id 327687
    next
    edit "Microsoft-NTP"
        set internet-service-id 327688
    next
    edit "Microsoft-Inbound_Email"
        set internet-service-id 327689
    next
    edit "Microsoft-LDAP"
        set internet-service-id 327694
    next
    edit "Microsoft-NetBIOS.Session.Service"
        set internet-service-id 327695
    next
    edit "Microsoft-RTMP"
        set internet-service-id 327696
    next
    edit "Microsoft-NetBIOS.Name.Service"
        set internet-service-id 327704
    next
    edit "Microsoft-Skype_Teams"
        set internet-service-id 327781
    next
    edit "Microsoft-Office365"
        set internet-service-id 327782
    next
    edit "Microsoft-Azure"
        set internet-service-id 327786
    next
    edit "Microsoft-Bing.Bot"
        set internet-service-id 327788
    next
    edit "Microsoft-Outlook"
        set internet-service-id 327791
    next
    edit "Microsoft-Microsoft.Update"
        set internet-service-id 327793
    next
    edit "Microsoft-Dynamics"
        set internet-service-id 327837
    next
    edit "Microsoft-WNS"
        set internet-service-id 327839
    next
    edit "Microsoft-Office365.Published"
        set internet-service-id 327880
    next
    edit "Amazon-Other"
        set internet-service-id 393216
    next
    edit "Amazon-Web"
        set internet-service-id 393217
    next
    edit "Amazon-ICMP"
        set internet-service-id 393218
    next
    edit "Amazon-DNS"
        set internet-service-id 393219
    next
    edit "Amazon-Outbound_Email"
        set internet-service-id 393220
    next
    edit "Amazon-SSH"
        set internet-service-id 393222
    next
    edit "Amazon-FTP"
        set internet-service-id 393223
    next
    edit "Amazon-NTP"
        set internet-service-id 393224
    next
    edit "Amazon-Inbound_Email"
        set internet-service-id 393225
    next
    edit "Amazon-LDAP"
        set internet-service-id 393230
    next
    edit "Amazon-NetBIOS.Session.Service"
        set internet-service-id 393231
    next
    edit "Amazon-RTMP"
        set internet-service-id 393232
    next
    edit "Amazon-NetBIOS.Name.Service"
        set internet-service-id 393240
    next
    edit "Amazon-AWS"
        set internet-service-id 393320
    next
    edit "Amazon-AWS.WorkSpaces.Gateway"
        set internet-service-id 393403
    next
    edit "eBay-Other"
        set internet-service-id 458752
    next
    edit "eBay-Web"
        set internet-service-id 458753
    next
    edit "eBay-ICMP"
        set internet-service-id 458754
    next
    edit "eBay-DNS"
        set internet-service-id 458755
    next
    edit "eBay-Outbound_Email"
        set internet-service-id 458756
    next
    edit "eBay-SSH"
        set internet-service-id 458758
    next
    edit "eBay-FTP"
        set internet-service-id 458759
    next
    edit "eBay-NTP"
        set internet-service-id 458760
    next
    edit "eBay-Inbound_Email"
        set internet-service-id 458761
    next
    edit "eBay-LDAP"
        set internet-service-id 458766
    next
    edit "eBay-NetBIOS.Session.Service"
        set internet-service-id 458767
    next
    edit "eBay-RTMP"
        set internet-service-id 458768
    next
    edit "eBay-NetBIOS.Name.Service"
        set internet-service-id 458776
    next
    edit "PayPal-Other"
        set internet-service-id 524288
    next
    edit "PayPal-Web"
        set internet-service-id 524289
    next
    edit "PayPal-ICMP"
        set internet-service-id 524290
    next
    edit "PayPal-DNS"
        set internet-service-id 524291
    next
    edit "PayPal-Outbound_Email"
        set internet-service-id 524292
    next
    edit "PayPal-SSH"
        set internet-service-id 524294
    next
    edit "PayPal-FTP"
        set internet-service-id 524295
    next
    edit "PayPal-NTP"
        set internet-service-id 524296
    next
    edit "PayPal-Inbound_Email"
        set internet-service-id 524297
    next
    edit "PayPal-LDAP"
        set internet-service-id 524302
    next
    edit "PayPal-NetBIOS.Session.Service"
        set internet-service-id 524303
    next
    edit "PayPal-RTMP"
        set internet-service-id 524304
    next
    edit "PayPal-NetBIOS.Name.Service"
        set internet-service-id 524312
    next
    edit "Box-Other"
        set internet-service-id 589824
    next
    edit "Box-Web"
        set internet-service-id 589825
    next
    edit "Box-ICMP"
        set internet-service-id 589826
    next
    edit "Box-DNS"
        set internet-service-id 589827
    next
    edit "Box-Outbound_Email"
        set internet-service-id 589828
    next
    edit "Box-SSH"
        set internet-service-id 589830
    next
    edit "Box-FTP"
        set internet-service-id 589831
    next
    edit "Box-NTP"
        set internet-service-id 589832
    next
    edit "Box-Inbound_Email"
        set internet-service-id 589833
    next
    edit "Box-LDAP"
        set internet-service-id 589838
    next
    edit "Box-NetBIOS.Session.Service"
        set internet-service-id 589839
    next
    edit "Box-RTMP"
        set internet-service-id 589840
    next
    edit "Box-NetBIOS.Name.Service"
        set internet-service-id 589848
    next
    edit "Salesforce-Other"
        set internet-service-id 655360
    next
    edit "Salesforce-Web"
        set internet-service-id 655361
    next
    edit "Salesforce-ICMP"
        set internet-service-id 655362
    next
    edit "Salesforce-DNS"
        set internet-service-id 655363
    next
    edit "Salesforce-Outbound_Email"
        set internet-service-id 655364
    next
    edit "Salesforce-SSH"
        set internet-service-id 655366
    next
    edit "Salesforce-FTP"
        set internet-service-id 655367
    next
    edit "Salesforce-NTP"
        set internet-service-id 655368
    next
    edit "Salesforce-Inbound_Email"
        set internet-service-id 655369
    next
    edit "Salesforce-LDAP"
        set internet-service-id 655374
    next
    edit "Salesforce-NetBIOS.Session.Service"
        set internet-service-id 655375
    next
    edit "Salesforce-RTMP"
        set internet-service-id 655376
    next
    edit "Salesforce-NetBIOS.Name.Service"
        set internet-service-id 655384
    next
    edit "Salesforce-Email.Relay"
        set internet-service-id 655530
    next
    edit "Dropbox-Other"
        set internet-service-id 720896
    next
    edit "Dropbox-Web"
        set internet-service-id 720897
    next
    edit "Dropbox-ICMP"
        set internet-service-id 720898
    next
    edit "Dropbox-DNS"
        set internet-service-id 720899
    next
    edit "Dropbox-Outbound_Email"
        set internet-service-id 720900
    next
    edit "Dropbox-SSH"
        set internet-service-id 720902
    next
    edit "Dropbox-FTP"
        set internet-service-id 720903
    next
    edit "Dropbox-NTP"
        set internet-service-id 720904
    next
    edit "Dropbox-Inbound_Email"
        set internet-service-id 720905
    next
    edit "Dropbox-LDAP"
        set internet-service-id 720910
    next
    edit "Dropbox-NetBIOS.Session.Service"
        set internet-service-id 720911
    next
    edit "Dropbox-RTMP"
        set internet-service-id 720912
    next
    edit "Dropbox-NetBIOS.Name.Service"
        set internet-service-id 720920
    next
    edit "Netflix-Other"
        set internet-service-id 786432
    next
    edit "Netflix-Web"
        set internet-service-id 786433
    next
    edit "Netflix-ICMP"
        set internet-service-id 786434
    next
    edit "Netflix-DNS"
        set internet-service-id 786435
    next
    edit "Netflix-Outbound_Email"
        set internet-service-id 786436
    next
    edit "Netflix-SSH"
        set internet-service-id 786438
    next
    edit "Netflix-FTP"
        set internet-service-id 786439
    next
    edit "Netflix-NTP"
        set internet-service-id 786440
    next
    edit "Netflix-Inbound_Email"
        set internet-service-id 786441
    next
    edit "Netflix-LDAP"
        set internet-service-id 786446
    next
    edit "Netflix-NetBIOS.Session.Service"
        set internet-service-id 786447
    next
    edit "Netflix-RTMP"
        set internet-service-id 786448
    next
    edit "Netflix-NetBIOS.Name.Service"
        set internet-service-id 786456
    next
    edit "LinkedIn-Other"
        set internet-service-id 851968
    next
    edit "LinkedIn-Web"
        set internet-service-id 851969
    next
    edit "LinkedIn-ICMP"
        set internet-service-id 851970
    next
    edit "LinkedIn-DNS"
        set internet-service-id 851971
    next
    edit "LinkedIn-Outbound_Email"
        set internet-service-id 851972
    next
    edit "LinkedIn-SSH"
        set internet-service-id 851974
    next
    edit "LinkedIn-FTP"
        set internet-service-id 851975
    next
    edit "LinkedIn-NTP"
        set internet-service-id 851976
    next
    edit "LinkedIn-Inbound_Email"
        set internet-service-id 851977
    next
    edit "LinkedIn-LDAP"
        set internet-service-id 851982
    next
    edit "LinkedIn-NetBIOS.Session.Service"
        set internet-service-id 851983
    next
    edit "LinkedIn-RTMP"
        set internet-service-id 851984
    next
    edit "LinkedIn-NetBIOS.Name.Service"
        set internet-service-id 851992
    next
    edit "Adobe-Other"
        set internet-service-id 917504
    next
    edit "Adobe-Web"
        set internet-service-id 917505
    next
    edit "Adobe-ICMP"
        set internet-service-id 917506
    next
    edit "Adobe-DNS"
        set internet-service-id 917507
    next
    edit "Adobe-Outbound_Email"
        set internet-service-id 917508
    next
    edit "Adobe-SSH"
        set internet-service-id 917510
    next
    edit "Adobe-FTP"
        set internet-service-id 917511
    next
    edit "Adobe-NTP"
        set internet-service-id 917512
    next
    edit "Adobe-Inbound_Email"
        set internet-service-id 917513
    next
    edit "Adobe-LDAP"
        set internet-service-id 917518
    next
    edit "Adobe-NetBIOS.Session.Service"
        set internet-service-id 917519
    next
    edit "Adobe-RTMP"
        set internet-service-id 917520
    next
    edit "Adobe-NetBIOS.Name.Service"
        set internet-service-id 917528
    next
    edit "Adobe-Adobe.Cloud"
        set internet-service-id 917640
    next
    edit "Oracle-Other"
        set internet-service-id 983040
    next
    edit "Oracle-Web"
        set internet-service-id 983041
    next
    edit "Oracle-ICMP"
        set internet-service-id 983042
    next
    edit "Oracle-DNS"
        set internet-service-id 983043
    next
    edit "Oracle-Outbound_Email"
        set internet-service-id 983044
    next
    edit "Oracle-SSH"
        set internet-service-id 983046
    next
    edit "Oracle-FTP"
        set internet-service-id 983047
    next
    edit "Oracle-NTP"
        set internet-service-id 983048
    next
    edit "Oracle-Inbound_Email"
        set internet-service-id 983049
    next
    edit "Oracle-LDAP"
        set internet-service-id 983054
    next
    edit "Oracle-NetBIOS.Session.Service"
        set internet-service-id 983055
    next
    edit "Oracle-RTMP"
        set internet-service-id 983056
    next
    edit "Oracle-NetBIOS.Name.Service"
        set internet-service-id 983064
    next
    edit "Oracle-Oracle.Cloud"
        set internet-service-id 983171
    next
    edit "Hulu-Other"
        set internet-service-id 1048576
    next
    edit "Hulu-Web"
        set internet-service-id 1048577
    next
    edit "Hulu-ICMP"
        set internet-service-id 1048578
    next
    edit "Hulu-DNS"
        set internet-service-id 1048579
    next
    edit "Hulu-Outbound_Email"
        set internet-service-id 1048580
    next
    edit "Hulu-SSH"
        set internet-service-id 1048582
    next
    edit "Hulu-FTP"
        set internet-service-id 1048583
    next
    edit "Hulu-NTP"
        set internet-service-id 1048584
    next
    edit "Hulu-Inbound_Email"
        set internet-service-id 1048585
    next
    edit "Hulu-LDAP"
        set internet-service-id 1048590
    next
    edit "Hulu-NetBIOS.Session.Service"
        set internet-service-id 1048591
    next
    edit "Hulu-RTMP"
        set internet-service-id 1048592
    next
    edit "Hulu-NetBIOS.Name.Service"
        set internet-service-id 1048600
    next
    edit "Pinterest-Other"
        set internet-service-id 1114112
    next
    edit "Pinterest-Web"
        set internet-service-id 1114113
    next
    edit "Pinterest-ICMP"
        set internet-service-id 1114114
    next
    edit "Pinterest-DNS"
        set internet-service-id 1114115
    next
    edit "Pinterest-Outbound_Email"
        set internet-service-id 1114116
    next
    edit "Pinterest-SSH"
        set internet-service-id 1114118
    next
    edit "Pinterest-FTP"
        set internet-service-id 1114119
    next
    edit "Pinterest-NTP"
        set internet-service-id 1114120
    next
    edit "Pinterest-Inbound_Email"
        set internet-service-id 1114121
    next
    edit "Pinterest-LDAP"
        set internet-service-id 1114126
    next
    edit "Pinterest-NetBIOS.Session.Service"
        set internet-service-id 1114127
    next
    edit "Pinterest-RTMP"
        set internet-service-id 1114128
    next
    edit "Pinterest-NetBIOS.Name.Service"
        set internet-service-id 1114136
    next
    edit "LogMeIn-Other"
        set internet-service-id 1179648
    next
    edit "LogMeIn-Web"
        set internet-service-id 1179649
    next
    edit "LogMeIn-ICMP"
        set internet-service-id 1179650
    next
    edit "LogMeIn-DNS"
        set internet-service-id 1179651
    next
    edit "LogMeIn-Outbound_Email"
        set internet-service-id 1179652
    next
    edit "LogMeIn-SSH"
        set internet-service-id 1179654
    next
    edit "LogMeIn-FTP"
        set internet-service-id 1179655
    next
    edit "LogMeIn-NTP"
        set internet-service-id 1179656
    next
    edit "LogMeIn-Inbound_Email"
        set internet-service-id 1179657
    next
    edit "LogMeIn-LDAP"
        set internet-service-id 1179662
    next
    edit "LogMeIn-NetBIOS.Session.Service"
        set internet-service-id 1179663
    next
    edit "LogMeIn-RTMP"
        set internet-service-id 1179664
    next
    edit "LogMeIn-NetBIOS.Name.Service"
        set internet-service-id 1179672
    next
    edit "LogMeIn-GoTo.Suite"
        set internet-service-id 1179767
    next
    edit "Fortinet-Other"
        set internet-service-id 1245184
    next
    edit "Fortinet-Web"
        set internet-service-id 1245185
    next
    edit "Fortinet-ICMP"
        set internet-service-id 1245186
    next
    edit "Fortinet-DNS"
        set internet-service-id 1245187
    next
    edit "Fortinet-Outbound_Email"
        set internet-service-id 1245188
    next
    edit "Fortinet-SSH"
        set internet-service-id 1245190
    next
    edit "Fortinet-FTP"
        set internet-service-id 1245191
    next
    edit "Fortinet-NTP"
        set internet-service-id 1245192
    next
    edit "Fortinet-Inbound_Email"
        set internet-service-id 1245193
    next
    edit "Fortinet-LDAP"
        set internet-service-id 1245198
    next
    edit "Fortinet-NetBIOS.Session.Service"
        set internet-service-id 1245199
    next
    edit "Fortinet-RTMP"
        set internet-service-id 1245200
    next
    edit "Fortinet-NetBIOS.Name.Service"
        set internet-service-id 1245208
    next
    edit "Fortinet-FortiGuard"
        set internet-service-id 1245324
    next
    edit "Fortinet-FortiMail.Cloud"
        set internet-service-id 1245325
    next
    edit "Fortinet-FortiCloud"
        set internet-service-id 1245326
    next
    edit "Kaspersky-Other"
        set internet-service-id 1310720
    next
    edit "Kaspersky-Web"
        set internet-service-id 1310721
    next
    edit "Kaspersky-ICMP"
        set internet-service-id 1310722
    next
    edit "Kaspersky-DNS"
        set internet-service-id 1310723
    next
    edit "Kaspersky-Outbound_Email"
        set internet-service-id 1310724
    next
    edit "Kaspersky-SSH"
        set internet-service-id 1310726
    next
    edit "Kaspersky-FTP"
        set internet-service-id 1310727
    next
    edit "Kaspersky-NTP"
        set internet-service-id 1310728
    next
    edit "Kaspersky-Inbound_Email"
        set internet-service-id 1310729
    next
    edit "Kaspersky-LDAP"
        set internet-service-id 1310734
    next
    edit "Kaspersky-NetBIOS.Session.Service"
        set internet-service-id 1310735
    next
    edit "Kaspersky-RTMP"
        set internet-service-id 1310736
    next
    edit "Kaspersky-NetBIOS.Name.Service"
        set internet-service-id 1310744
    next
    edit "McAfee-Other"
        set internet-service-id 1376256
    next
    edit "McAfee-Web"
        set internet-service-id 1376257
    next
    edit "McAfee-ICMP"
        set internet-service-id 1376258
    next
    edit "McAfee-DNS"
        set internet-service-id 1376259
    next
    edit "McAfee-Outbound_Email"
        set internet-service-id 1376260
    next
    edit "McAfee-SSH"
        set internet-service-id 1376262
    next
    edit "McAfee-FTP"
        set internet-service-id 1376263
    next
    edit "McAfee-NTP"
        set internet-service-id 1376264
    next
    edit "McAfee-Inbound_Email"
        set internet-service-id 1376265
    next
    edit "McAfee-LDAP"
        set internet-service-id 1376270
    next
    edit "McAfee-NetBIOS.Session.Service"
        set internet-service-id 1376271
    next
    edit "McAfee-RTMP"
        set internet-service-id 1376272
    next
    edit "McAfee-NetBIOS.Name.Service"
        set internet-service-id 1376280
    next
    edit "Symantec-Other"
        set internet-service-id 1441792
    next
    edit "Symantec-Web"
        set internet-service-id 1441793
    next
    edit "Symantec-ICMP"
        set internet-service-id 1441794
    next
    edit "Symantec-DNS"
        set internet-service-id 1441795
    next
    edit "Symantec-Outbound_Email"
        set internet-service-id 1441796
    next
    edit "Symantec-SSH"
        set internet-service-id 1441798
    next
    edit "Symantec-FTP"
        set internet-service-id 1441799
    next
    edit "Symantec-NTP"
        set internet-service-id 1441800
    next
    edit "Symantec-Inbound_Email"
        set internet-service-id 1441801
    next
    edit "Symantec-LDAP"
        set internet-service-id 1441806
    next
    edit "Symantec-NetBIOS.Session.Service"
        set internet-service-id 1441807
    next
    edit "Symantec-RTMP"
        set internet-service-id 1441808
    next
    edit "Symantec-NetBIOS.Name.Service"
        set internet-service-id 1441816
    next
    edit "Symantec-Symantec.Cloud"
        set internet-service-id 1441922
    next
    edit "VMware-Other"
        set internet-service-id 1507328
    next
    edit "VMware-Web"
        set internet-service-id 1507329
    next
    edit "VMware-ICMP"
        set internet-service-id 1507330
    next
    edit "VMware-DNS"
        set internet-service-id 1507331
    next
    edit "VMware-Outbound_Email"
        set internet-service-id 1507332
    next
    edit "VMware-SSH"
        set internet-service-id 1507334
    next
    edit "VMware-FTP"
        set internet-service-id 1507335
    next
    edit "VMware-NTP"
        set internet-service-id 1507336
    next
    edit "VMware-Inbound_Email"
        set internet-service-id 1507337
    next
    edit "VMware-LDAP"
        set internet-service-id 1507342
    next
    edit "VMware-NetBIOS.Session.Service"
        set internet-service-id 1507343
    next
    edit "VMware-RTMP"
        set internet-service-id 1507344
    next
    edit "VMware-NetBIOS.Name.Service"
        set internet-service-id 1507352
    next
    edit "VMware-Airwatch"
        set internet-service-id 1507461
    next
    edit "AOL-Other"
        set internet-service-id 1572864
    next
    edit "AOL-Web"
        set internet-service-id 1572865
    next
    edit "AOL-ICMP"
        set internet-service-id 1572866
    next
    edit "AOL-DNS"
        set internet-service-id 1572867
    next
    edit "AOL-Outbound_Email"
        set internet-service-id 1572868
    next
    edit "AOL-SSH"
        set internet-service-id 1572870
    next
    edit "AOL-FTP"
        set internet-service-id 1572871
    next
    edit "AOL-NTP"
        set internet-service-id 1572872
    next
    edit "AOL-Inbound_Email"
        set internet-service-id 1572873
    next
    edit "AOL-LDAP"
        set internet-service-id 1572878
    next
    edit "AOL-NetBIOS.Session.Service"
        set internet-service-id 1572879
    next
    edit "AOL-RTMP"
        set internet-service-id 1572880
    next
    edit "AOL-NetBIOS.Name.Service"
        set internet-service-id 1572888
    next
    edit "RealNetworks-Other"
        set internet-service-id 1638400
    next
    edit "RealNetworks-Web"
        set internet-service-id 1638401
    next
    edit "RealNetworks-ICMP"
        set internet-service-id 1638402
    next
    edit "RealNetworks-DNS"
        set internet-service-id 1638403
    next
    edit "RealNetworks-Outbound_Email"
        set internet-service-id 1638404
    next
    edit "RealNetworks-SSH"
        set internet-service-id 1638406
    next
    edit "RealNetworks-FTP"
        set internet-service-id 1638407
    next
    edit "RealNetworks-NTP"
        set internet-service-id 1638408
    next
    edit "RealNetworks-Inbound_Email"
        set internet-service-id 1638409
    next
    edit "RealNetworks-LDAP"
        set internet-service-id 1638414
    next
    edit "RealNetworks-NetBIOS.Session.Service"
        set internet-service-id 1638415
    next
    edit "RealNetworks-RTMP"
        set internet-service-id 1638416
    next
    edit "RealNetworks-NetBIOS.Name.Service"
        set internet-service-id 1638424
    next
    edit "Zoho-Other"
        set internet-service-id 1703936
    next
    edit "Zoho-Web"
        set internet-service-id 1703937
    next
    edit "Zoho-ICMP"
        set internet-service-id 1703938
    next
    edit "Zoho-DNS"
        set internet-service-id 1703939
    next
    edit "Zoho-Outbound_Email"
        set internet-service-id 1703940
    next
    edit "Zoho-SSH"
        set internet-service-id 1703942
    next
    edit "Zoho-FTP"
        set internet-service-id 1703943
    next
    edit "Zoho-NTP"
        set internet-service-id 1703944
    next
    edit "Zoho-Inbound_Email"
        set internet-service-id 1703945
    next
    edit "Zoho-LDAP"
        set internet-service-id 1703950
    next
    edit "Zoho-NetBIOS.Session.Service"
        set internet-service-id 1703951
    next
    edit "Zoho-RTMP"
        set internet-service-id 1703952
    next
    edit "Zoho-NetBIOS.Name.Service"
        set internet-service-id 1703960
    next
    edit "Mozilla-Other"
        set internet-service-id 1769472
    next
    edit "Mozilla-Web"
        set internet-service-id 1769473
    next
    edit "Mozilla-ICMP"
        set internet-service-id 1769474
    next
    edit "Mozilla-DNS"
        set internet-service-id 1769475
    next
    edit "Mozilla-Outbound_Email"
        set internet-service-id 1769476
    next
    edit "Mozilla-SSH"
        set internet-service-id 1769478
    next
    edit "Mozilla-FTP"
        set internet-service-id 1769479
    next
    edit "Mozilla-NTP"
        set internet-service-id 1769480
    next
    edit "Mozilla-Inbound_Email"
        set internet-service-id 1769481
    next
    edit "Mozilla-LDAP"
        set internet-service-id 1769486
    next
    edit "Mozilla-NetBIOS.Session.Service"
        set internet-service-id 1769487
    next
    edit "Mozilla-RTMP"
        set internet-service-id 1769488
    next
    edit "Mozilla-NetBIOS.Name.Service"
        set internet-service-id 1769496
    next
    edit "TeamViewer-Other"
        set internet-service-id 1835008
    next
    edit "TeamViewer-Web"
        set internet-service-id 1835009
    next
    edit "TeamViewer-ICMP"
        set internet-service-id 1835010
    next
    edit "TeamViewer-DNS"
        set internet-service-id 1835011
    next
    edit "TeamViewer-Outbound_Email"
        set internet-service-id 1835012
    next
    edit "TeamViewer-SSH"
        set internet-service-id 1835014
    next
    edit "TeamViewer-FTP"
        set internet-service-id 1835015
    next
    edit "TeamViewer-NTP"
        set internet-service-id 1835016
    next
    edit "TeamViewer-Inbound_Email"
        set internet-service-id 1835017
    next
    edit "TeamViewer-LDAP"
        set internet-service-id 1835022
    next
    edit "TeamViewer-NetBIOS.Session.Service"
        set internet-service-id 1835023
    next
    edit "TeamViewer-RTMP"
        set internet-service-id 1835024
    next
    edit "TeamViewer-NetBIOS.Name.Service"
        set internet-service-id 1835032
    next
    edit "TeamViewer-TeamViewer"
        set internet-service-id 1835117
    next
    edit "HP-Other"
        set internet-service-id 1900544
    next
    edit "HP-Web"
        set internet-service-id 1900545
    next
    edit "HP-ICMP"
        set internet-service-id 1900546
    next
    edit "HP-DNS"
        set internet-service-id 1900547
    next
    edit "HP-Outbound_Email"
        set internet-service-id 1900548
    next
    edit "HP-SSH"
        set internet-service-id 1900550
    next
    edit "HP-FTP"
        set internet-service-id 1900551
    next
    edit "HP-NTP"
        set internet-service-id 1900552
    next
    edit "HP-Inbound_Email"
        set internet-service-id 1900553
    next
    edit "HP-LDAP"
        set internet-service-id 1900558
    next
    edit "HP-NetBIOS.Session.Service"
        set internet-service-id 1900559
    next
    edit "HP-RTMP"
        set internet-service-id 1900560
    next
    edit "HP-NetBIOS.Name.Service"
        set internet-service-id 1900568
    next
    edit "HP-Aruba"
        set internet-service-id 1900726
    next
    edit "Cisco-Other"
        set internet-service-id 1966080
    next
    edit "Cisco-Web"
        set internet-service-id 1966081
    next
    edit "Cisco-ICMP"
        set internet-service-id 1966082
    next
    edit "Cisco-DNS"
        set internet-service-id 1966083
    next
    edit "Cisco-Outbound_Email"
        set internet-service-id 1966084
    next
    edit "Cisco-SSH"
        set internet-service-id 1966086
    next
    edit "Cisco-FTP"
        set internet-service-id 1966087
    next
    edit "Cisco-NTP"
        set internet-service-id 1966088
    next
    edit "Cisco-Inbound_Email"
        set internet-service-id 1966089
    next
    edit "Cisco-LDAP"
        set internet-service-id 1966094
    next
    edit "Cisco-NetBIOS.Session.Service"
        set internet-service-id 1966095
    next
    edit "Cisco-RTMP"
        set internet-service-id 1966096
    next
    edit "Cisco-NetBIOS.Name.Service"
        set internet-service-id 1966104
    next
    edit "Cisco-Webex"
        set internet-service-id 1966183
    next
    edit "Cisco-Meraki.Cloud"
        set internet-service-id 1966218
    next
    edit "Cisco-Duo.Security"
        set internet-service-id 1966225
    next
    edit "Cisco-AppDynamic"
        set internet-service-id 1966260
    next
    edit "IBM-Other"
        set internet-service-id 2031616
    next
    edit "IBM-Web"
        set internet-service-id 2031617
    next
    edit "IBM-ICMP"
        set internet-service-id 2031618
    next
    edit "IBM-DNS"
        set internet-service-id 2031619
    next
    edit "IBM-Outbound_Email"
        set internet-service-id 2031620
    next
    edit "IBM-SSH"
        set internet-service-id 2031622
    next
    edit "IBM-FTP"
        set internet-service-id 2031623
    next
    edit "IBM-NTP"
        set internet-service-id 2031624
    next
    edit "IBM-Inbound_Email"
        set internet-service-id 2031625
    next
    edit "IBM-LDAP"
        set internet-service-id 2031630
    next
    edit "IBM-NetBIOS.Session.Service"
        set internet-service-id 2031631
    next
    edit "IBM-RTMP"
        set internet-service-id 2031632
    next
    edit "IBM-NetBIOS.Name.Service"
        set internet-service-id 2031640
    next
    edit "IBM-IBM.Cloud"
        set internet-service-id 2031748
    next
    edit "Citrix-Other"
        set internet-service-id 2097152
    next
    edit "Citrix-Web"
        set internet-service-id 2097153
    next
    edit "Citrix-ICMP"
        set internet-service-id 2097154
    next
    edit "Citrix-DNS"
        set internet-service-id 2097155
    next
    edit "Citrix-Outbound_Email"
        set internet-service-id 2097156
    next
    edit "Citrix-SSH"
        set internet-service-id 2097158
    next
    edit "Citrix-FTP"
        set internet-service-id 2097159
    next
    edit "Citrix-NTP"
        set internet-service-id 2097160
    next
    edit "Citrix-Inbound_Email"
        set internet-service-id 2097161
    next
    edit "Citrix-LDAP"
        set internet-service-id 2097166
    next
    edit "Citrix-NetBIOS.Session.Service"
        set internet-service-id 2097167
    next
    edit "Citrix-RTMP"
        set internet-service-id 2097168
    next
    edit "Citrix-NetBIOS.Name.Service"
        set internet-service-id 2097176
    next
    edit "Twitter-Other"
        set internet-service-id 2162688
    next
    edit "Twitter-Web"
        set internet-service-id 2162689
    next
    edit "Twitter-ICMP"
        set internet-service-id 2162690
    next
    edit "Twitter-DNS"
        set internet-service-id 2162691
    next
    edit "Twitter-Outbound_Email"
        set internet-service-id 2162692
    next
    edit "Twitter-SSH"
        set internet-service-id 2162694
    next
    edit "Twitter-FTP"
        set internet-service-id 2162695
    next
    edit "Twitter-NTP"
        set internet-service-id 2162696
    next
    edit "Twitter-Inbound_Email"
        set internet-service-id 2162697
    next
    edit "Twitter-LDAP"
        set internet-service-id 2162702
    next
    edit "Twitter-NetBIOS.Session.Service"
        set internet-service-id 2162703
    next
    edit "Twitter-RTMP"
        set internet-service-id 2162704
    next
    edit "Twitter-NetBIOS.Name.Service"
        set internet-service-id 2162712
    next
    edit "Dell-Other"
        set internet-service-id 2228224
    next
    edit "Dell-Web"
        set internet-service-id 2228225
    next
    edit "Dell-ICMP"
        set internet-service-id 2228226
    next
    edit "Dell-DNS"
        set internet-service-id 2228227
    next
    edit "Dell-Outbound_Email"
        set internet-service-id 2228228
    next
    edit "Dell-SSH"
        set internet-service-id 2228230
    next
    edit "Dell-FTP"
        set internet-service-id 2228231
    next
    edit "Dell-NTP"
        set internet-service-id 2228232
    next
    edit "Dell-Inbound_Email"
        set internet-service-id 2228233
    next
    edit "Dell-LDAP"
        set internet-service-id 2228238
    next
    edit "Dell-NetBIOS.Session.Service"
        set internet-service-id 2228239
    next
    edit "Dell-RTMP"
        set internet-service-id 2228240
    next
    edit "Dell-NetBIOS.Name.Service"
        set internet-service-id 2228248
    next
    edit "Vimeo-Other"
        set internet-service-id 2293760
    next
    edit "Vimeo-Web"
        set internet-service-id 2293761
    next
    edit "Vimeo-ICMP"
        set internet-service-id 2293762
    next
    edit "Vimeo-DNS"
        set internet-service-id 2293763
    next
    edit "Vimeo-Outbound_Email"
        set internet-service-id 2293764
    next
    edit "Vimeo-SSH"
        set internet-service-id 2293766
    next
    edit "Vimeo-FTP"
        set internet-service-id 2293767
    next
    edit "Vimeo-NTP"
        set internet-service-id 2293768
    next
    edit "Vimeo-Inbound_Email"
        set internet-service-id 2293769
    next
    edit "Vimeo-LDAP"
        set internet-service-id 2293774
    next
    edit "Vimeo-NetBIOS.Session.Service"
        set internet-service-id 2293775
    next
    edit "Vimeo-RTMP"
        set internet-service-id 2293776
    next
    edit "Vimeo-NetBIOS.Name.Service"
        set internet-service-id 2293784
    next
    edit "Redhat-Other"
        set internet-service-id 2359296
    next
    edit "Redhat-Web"
        set internet-service-id 2359297
    next
    edit "Redhat-ICMP"
        set internet-service-id 2359298
    next
    edit "Redhat-DNS"
        set internet-service-id 2359299
    next
    edit "Redhat-Outbound_Email"
        set internet-service-id 2359300
    next
    edit "Redhat-SSH"
        set internet-service-id 2359302
    next
    edit "Redhat-FTP"
        set internet-service-id 2359303
    next
    edit "Redhat-NTP"
        set internet-service-id 2359304
    next
    edit "Redhat-Inbound_Email"
        set internet-service-id 2359305
    next
    edit "Redhat-LDAP"
        set internet-service-id 2359310
    next
    edit "Redhat-NetBIOS.Session.Service"
        set internet-service-id 2359311
    next
    edit "Redhat-RTMP"
        set internet-service-id 2359312
    next
    edit "Redhat-NetBIOS.Name.Service"
        set internet-service-id 2359320
    next
    edit "VK-Other"
        set internet-service-id 2424832
    next
    edit "VK-Web"
        set internet-service-id 2424833
    next
    edit "VK-ICMP"
        set internet-service-id 2424834
    next
    edit "VK-DNS"
        set internet-service-id 2424835
    next
    edit "VK-Outbound_Email"
        set internet-service-id 2424836
    next
    edit "VK-SSH"
        set internet-service-id 2424838
    next
    edit "VK-FTP"
        set internet-service-id 2424839
    next
    edit "VK-NTP"
        set internet-service-id 2424840
    next
    edit "VK-Inbound_Email"
        set internet-service-id 2424841
    next
    edit "VK-LDAP"
        set internet-service-id 2424846
    next
    edit "VK-NetBIOS.Session.Service"
        set internet-service-id 2424847
    next
    edit "VK-RTMP"
        set internet-service-id 2424848
    next
    edit "VK-NetBIOS.Name.Service"
        set internet-service-id 2424856
    next
    edit "TrendMicro-Other"
        set internet-service-id 2490368
    next
    edit "TrendMicro-Web"
        set internet-service-id 2490369
    next
    edit "TrendMicro-ICMP"
        set internet-service-id 2490370
    next
    edit "TrendMicro-DNS"
        set internet-service-id 2490371
    next
    edit "TrendMicro-Outbound_Email"
        set internet-service-id 2490372
    next
    edit "TrendMicro-SSH"
        set internet-service-id 2490374
    next
    edit "TrendMicro-FTP"
        set internet-service-id 2490375
    next
    edit "TrendMicro-NTP"
        set internet-service-id 2490376
    next
    edit "TrendMicro-Inbound_Email"
        set internet-service-id 2490377
    next
    edit "TrendMicro-LDAP"
        set internet-service-id 2490382
    next
    edit "TrendMicro-NetBIOS.Session.Service"
        set internet-service-id 2490383
    next
    edit "TrendMicro-RTMP"
        set internet-service-id 2490384
    next
    edit "TrendMicro-NetBIOS.Name.Service"
        set internet-service-id 2490392
    next
    edit "Tencent-Other"
        set internet-service-id 2555904
    next
    edit "Tencent-Web"
        set internet-service-id 2555905
    next
    edit "Tencent-ICMP"
        set internet-service-id 2555906
    next
    edit "Tencent-DNS"
        set internet-service-id 2555907
    next
    edit "Tencent-Outbound_Email"
        set internet-service-id 2555908
    next
    edit "Tencent-SSH"
        set internet-service-id 2555910
    next
    edit "Tencent-FTP"
        set internet-service-id 2555911
    next
    edit "Tencent-NTP"
        set internet-service-id 2555912
    next
    edit "Tencent-Inbound_Email"
        set internet-service-id 2555913
    next
    edit "Tencent-LDAP"
        set internet-service-id 2555918
    next
    edit "Tencent-NetBIOS.Session.Service"
        set internet-service-id 2555919
    next
    edit "Tencent-RTMP"
        set internet-service-id 2555920
    next
    edit "Tencent-NetBIOS.Name.Service"
        set internet-service-id 2555928
    next
    edit "Ask-Other"
        set internet-service-id 2621440
    next
    edit "Ask-Web"
        set internet-service-id 2621441
    next
    edit "Ask-ICMP"
        set internet-service-id 2621442
    next
    edit "Ask-DNS"
        set internet-service-id 2621443
    next
    edit "Ask-Outbound_Email"
        set internet-service-id 2621444
    next
    edit "Ask-SSH"
        set internet-service-id 2621446
    next
    edit "Ask-FTP"
        set internet-service-id 2621447
    next
    edit "Ask-NTP"
        set internet-service-id 2621448
    next
    edit "Ask-Inbound_Email"
        set internet-service-id 2621449
    next
    edit "Ask-LDAP"
        set internet-service-id 2621454
    next
    edit "Ask-NetBIOS.Session.Service"
        set internet-service-id 2621455
    next
    edit "Ask-RTMP"
        set internet-service-id 2621456
    next
    edit "Ask-NetBIOS.Name.Service"
        set internet-service-id 2621464
    next
    edit "CNN-Other"
        set internet-service-id 2686976
    next
    edit "CNN-Web"
        set internet-service-id 2686977
    next
    edit "CNN-ICMP"
        set internet-service-id 2686978
    next
    edit "CNN-DNS"
        set internet-service-id 2686979
    next
    edit "CNN-Outbound_Email"
        set internet-service-id 2686980
    next
    edit "CNN-SSH"
        set internet-service-id 2686982
    next
    edit "CNN-FTP"
        set internet-service-id 2686983
    next
    edit "CNN-NTP"
        set internet-service-id 2686984
    next
    edit "CNN-Inbound_Email"
        set internet-service-id 2686985
    next
    edit "CNN-LDAP"
        set internet-service-id 2686990
    next
    edit "CNN-NetBIOS.Session.Service"
        set internet-service-id 2686991
    next
    edit "CNN-RTMP"
        set internet-service-id 2686992
    next
    edit "CNN-NetBIOS.Name.Service"
        set internet-service-id 2687000
    next
    edit "Myspace-Other"
        set internet-service-id 2752512
    next
    edit "Myspace-Web"
        set internet-service-id 2752513
    next
    edit "Myspace-ICMP"
        set internet-service-id 2752514
    next
    edit "Myspace-DNS"
        set internet-service-id 2752515
    next
    edit "Myspace-Outbound_Email"
        set internet-service-id 2752516
    next
    edit "Myspace-SSH"
        set internet-service-id 2752518
    next
    edit "Myspace-FTP"
        set internet-service-id 2752519
    next
    edit "Myspace-NTP"
        set internet-service-id 2752520
    next
    edit "Myspace-Inbound_Email"
        set internet-service-id 2752521
    next
    edit "Myspace-LDAP"
        set internet-service-id 2752526
    next
    edit "Myspace-NetBIOS.Session.Service"
        set internet-service-id 2752527
    next
    edit "Myspace-RTMP"
        set internet-service-id 2752528
    next
    edit "Myspace-NetBIOS.Name.Service"
        set internet-service-id 2752536
    next
    edit "Tor-Relay.Node"
        set internet-service-id 2818238
    next
    edit "Tor-Exit.Node"
        set internet-service-id 2818243
    next
    edit "Baidu-Other"
        set internet-service-id 2883584
    next
    edit "Baidu-Web"
        set internet-service-id 2883585
    next
    edit "Baidu-ICMP"
        set internet-service-id 2883586
    next
    edit "Baidu-DNS"
        set internet-service-id 2883587
    next
    edit "Baidu-Outbound_Email"
        set internet-service-id 2883588
    next
    edit "Baidu-SSH"
        set internet-service-id 2883590
    next
    edit "Baidu-FTP"
        set internet-service-id 2883591
    next
    edit "Baidu-NTP"
        set internet-service-id 2883592
    next
    edit "Baidu-Inbound_Email"
        set internet-service-id 2883593
    next
    edit "Baidu-LDAP"
        set internet-service-id 2883598
    next
    edit "Baidu-NetBIOS.Session.Service"
        set internet-service-id 2883599
    next
    edit "Baidu-RTMP"
        set internet-service-id 2883600
    next
    edit "Baidu-NetBIOS.Name.Service"
        set internet-service-id 2883608
    next
    edit "ntp.org-Other"
        set internet-service-id 2949120
    next
    edit "ntp.org-Web"
        set internet-service-id 2949121
    next
    edit "ntp.org-ICMP"
        set internet-service-id 2949122
    next
    edit "ntp.org-DNS"
        set internet-service-id 2949123
    next
    edit "ntp.org-Outbound_Email"
        set internet-service-id 2949124
    next
    edit "ntp.org-SSH"
        set internet-service-id 2949126
    next
    edit "ntp.org-FTP"
        set internet-service-id 2949127
    next
    edit "ntp.org-NTP"
        set internet-service-id 2949128
    next
    edit "ntp.org-Inbound_Email"
        set internet-service-id 2949129
    next
    edit "ntp.org-LDAP"
        set internet-service-id 2949134
    next
    edit "ntp.org-NetBIOS.Session.Service"
        set internet-service-id 2949135
    next
    edit "ntp.org-RTMP"
        set internet-service-id 2949136
    next
    edit "ntp.org-NetBIOS.Name.Service"
        set internet-service-id 2949144
    next
    edit "Proxy-Proxy.Server"
        set internet-service-id 3014850
    next
    edit "Botnet-C&C.Server"
        set internet-service-id 3080383
    next
    edit "Spam-Spamming.Server"
        set internet-service-id 3145920
    next
    edit "Phishing-Phishing.Server"
        set internet-service-id 3211457
    next
    edit "Zendesk-Other"
        set internet-service-id 3407872
    next
    edit "Zendesk-Web"
        set internet-service-id 3407873
    next
    edit "Zendesk-ICMP"
        set internet-service-id 3407874
    next
    edit "Zendesk-DNS"
        set internet-service-id 3407875
    next
    edit "Zendesk-Outbound_Email"
        set internet-service-id 3407876
    next
    edit "Zendesk-SSH"
        set internet-service-id 3407878
    next
    edit "Zendesk-FTP"
        set internet-service-id 3407879
    next
    edit "Zendesk-NTP"
        set internet-service-id 3407880
    next
    edit "Zendesk-Inbound_Email"
        set internet-service-id 3407881
    next
    edit "Zendesk-LDAP"
        set internet-service-id 3407886
    next
    edit "Zendesk-NetBIOS.Session.Service"
        set internet-service-id 3407887
    next
    edit "Zendesk-RTMP"
        set internet-service-id 3407888
    next
    edit "Zendesk-NetBIOS.Name.Service"
        set internet-service-id 3407896
    next
    edit "Zendesk-Zendesk.Suite"
        set internet-service-id 3408047
    next
    edit "DocuSign-Other"
        set internet-service-id 3473408
    next
    edit "DocuSign-Web"
        set internet-service-id 3473409
    next
    edit "DocuSign-ICMP"
        set internet-service-id 3473410
    next
    edit "DocuSign-DNS"
        set internet-service-id 3473411
    next
    edit "DocuSign-Outbound_Email"
        set internet-service-id 3473412
    next
    edit "DocuSign-SSH"
        set internet-service-id 3473414
    next
    edit "DocuSign-FTP"
        set internet-service-id 3473415
    next
    edit "DocuSign-NTP"
        set internet-service-id 3473416
    next
    edit "DocuSign-Inbound_Email"
        set internet-service-id 3473417
    next
    edit "DocuSign-LDAP"
        set internet-service-id 3473422
    next
    edit "DocuSign-NetBIOS.Session.Service"
        set internet-service-id 3473423
    next
    edit "DocuSign-RTMP"
        set internet-service-id 3473424
    next
    edit "DocuSign-NetBIOS.Name.Service"
        set internet-service-id 3473432
    next
    edit "ServiceNow-Other"
        set internet-service-id 3538944
    next
    edit "ServiceNow-Web"
        set internet-service-id 3538945
    next
    edit "ServiceNow-ICMP"
        set internet-service-id 3538946
    next
    edit "ServiceNow-DNS"
        set internet-service-id 3538947
    next
    edit "ServiceNow-Outbound_Email"
        set internet-service-id 3538948
    next
    edit "ServiceNow-SSH"
        set internet-service-id 3538950
    next
    edit "ServiceNow-FTP"
        set internet-service-id 3538951
    next
    edit "ServiceNow-NTP"
        set internet-service-id 3538952
    next
    edit "ServiceNow-Inbound_Email"
        set internet-service-id 3538953
    next
    edit "ServiceNow-LDAP"
        set internet-service-id 3538958
    next
    edit "ServiceNow-NetBIOS.Session.Service"
        set internet-service-id 3538959
    next
    edit "ServiceNow-RTMP"
        set internet-service-id 3538960
    next
    edit "ServiceNow-NetBIOS.Name.Service"
        set internet-service-id 3538968
    next
    edit "GitHub-GitHub"
        set internet-service-id 3604638
    next
    edit "Workday-Other"
        set internet-service-id 3670016
    next
    edit "Workday-Web"
        set internet-service-id 3670017
    next
    edit "Workday-ICMP"
        set internet-service-id 3670018
    next
    edit "Workday-DNS"
        set internet-service-id 3670019
    next
    edit "Workday-Outbound_Email"
        set internet-service-id 3670020
    next
    edit "Workday-SSH"
        set internet-service-id 3670022
    next
    edit "Workday-FTP"
        set internet-service-id 3670023
    next
    edit "Workday-NTP"
        set internet-service-id 3670024
    next
    edit "Workday-Inbound_Email"
        set internet-service-id 3670025
    next
    edit "Workday-LDAP"
        set internet-service-id 3670030
    next
    edit "Workday-NetBIOS.Session.Service"
        set internet-service-id 3670031
    next
    edit "Workday-RTMP"
        set internet-service-id 3670032
    next
    edit "Workday-NetBIOS.Name.Service"
        set internet-service-id 3670040
    next
    edit "HubSpot-Other"
        set internet-service-id 3735552
    next
    edit "HubSpot-Web"
        set internet-service-id 3735553
    next
    edit "HubSpot-ICMP"
        set internet-service-id 3735554
    next
    edit "HubSpot-DNS"
        set internet-service-id 3735555
    next
    edit "HubSpot-Outbound_Email"
        set internet-service-id 3735556
    next
    edit "HubSpot-SSH"
        set internet-service-id 3735558
    next
    edit "HubSpot-FTP"
        set internet-service-id 3735559
    next
    edit "HubSpot-NTP"
        set internet-service-id 3735560
    next
    edit "HubSpot-Inbound_Email"
        set internet-service-id 3735561
    next
    edit "HubSpot-LDAP"
        set internet-service-id 3735566
    next
    edit "HubSpot-NetBIOS.Session.Service"
        set internet-service-id 3735567
    next
    edit "HubSpot-RTMP"
        set internet-service-id 3735568
    next
    edit "HubSpot-NetBIOS.Name.Service"
        set internet-service-id 3735576
    next
    edit "Twilio-Other"
        set internet-service-id 3801088
    next
    edit "Twilio-Web"
        set internet-service-id 3801089
    next
    edit "Twilio-ICMP"
        set internet-service-id 3801090
    next
    edit "Twilio-DNS"
        set internet-service-id 3801091
    next
    edit "Twilio-Outbound_Email"
        set internet-service-id 3801092
    next
    edit "Twilio-SSH"
        set internet-service-id 3801094
    next
    edit "Twilio-FTP"
        set internet-service-id 3801095
    next
    edit "Twilio-NTP"
        set internet-service-id 3801096
    next
    edit "Twilio-Inbound_Email"
        set internet-service-id 3801097
    next
    edit "Twilio-LDAP"
        set internet-service-id 3801102
    next
    edit "Twilio-NetBIOS.Session.Service"
        set internet-service-id 3801103
    next
    edit "Twilio-RTMP"
        set internet-service-id 3801104
    next
    edit "Twilio-NetBIOS.Name.Service"
        set internet-service-id 3801112
    next
    edit "Twilio-Elastic.SIP.Trunking"
        set internet-service-id 3801277
    next
    edit "Coupa-Other"
        set internet-service-id 3866624
    next
    edit "Coupa-Web"
        set internet-service-id 3866625
    next
    edit "Coupa-ICMP"
        set internet-service-id 3866626
    next
    edit "Coupa-DNS"
        set internet-service-id 3866627
    next
    edit "Coupa-Outbound_Email"
        set internet-service-id 3866628
    next
    edit "Coupa-SSH"
        set internet-service-id 3866630
    next
    edit "Coupa-FTP"
        set internet-service-id 3866631
    next
    edit "Coupa-NTP"
        set internet-service-id 3866632
    next
    edit "Coupa-Inbound_Email"
        set internet-service-id 3866633
    next
    edit "Coupa-LDAP"
        set internet-service-id 3866638
    next
    edit "Coupa-NetBIOS.Session.Service"
        set internet-service-id 3866639
    next
    edit "Coupa-RTMP"
        set internet-service-id 3866640
    next
    edit "Coupa-NetBIOS.Name.Service"
        set internet-service-id 3866648
    next
    edit "Atlassian-Other"
        set internet-service-id 3932160
    next
    edit "Atlassian-Web"
        set internet-service-id 3932161
    next
    edit "Atlassian-ICMP"
        set internet-service-id 3932162
    next
    edit "Atlassian-DNS"
        set internet-service-id 3932163
    next
    edit "Atlassian-Outbound_Email"
        set internet-service-id 3932164
    next
    edit "Atlassian-SSH"
        set internet-service-id 3932166
    next
    edit "Atlassian-FTP"
        set internet-service-id 3932167
    next
    edit "Atlassian-NTP"
        set internet-service-id 3932168
    next
    edit "Atlassian-Inbound_Email"
        set internet-service-id 3932169
    next
    edit "Atlassian-LDAP"
        set internet-service-id 3932174
    next
    edit "Atlassian-NetBIOS.Session.Service"
        set internet-service-id 3932175
    next
    edit "Atlassian-RTMP"
        set internet-service-id 3932176
    next
    edit "Atlassian-NetBIOS.Name.Service"
        set internet-service-id 3932184
    next
    edit "Xero-Other"
        set internet-service-id 3997696
    next
    edit "Xero-Web"
        set internet-service-id 3997697
    next
    edit "Xero-ICMP"
        set internet-service-id 3997698
    next
    edit "Xero-DNS"
        set internet-service-id 3997699
    next
    edit "Xero-Outbound_Email"
        set internet-service-id 3997700
    next
    edit "Xero-SSH"
        set internet-service-id 3997702
    next
    edit "Xero-FTP"
        set internet-service-id 3997703
    next
    edit "Xero-NTP"
        set internet-service-id 3997704
    next
    edit "Xero-Inbound_Email"
        set internet-service-id 3997705
    next
    edit "Xero-LDAP"
        set internet-service-id 3997710
    next
    edit "Xero-NetBIOS.Session.Service"
        set internet-service-id 3997711
    next
    edit "Xero-RTMP"
        set internet-service-id 3997712
    next
    edit "Xero-NetBIOS.Name.Service"
        set internet-service-id 3997720
    next
    edit "Zuora-Other"
        set internet-service-id 4063232
    next
    edit "Zuora-Web"
        set internet-service-id 4063233
    next
    edit "Zuora-ICMP"
        set internet-service-id 4063234
    next
    edit "Zuora-DNS"
        set internet-service-id 4063235
    next
    edit "Zuora-Outbound_Email"
        set internet-service-id 4063236
    next
    edit "Zuora-SSH"
        set internet-service-id 4063238
    next
    edit "Zuora-FTP"
        set internet-service-id 4063239
    next
    edit "Zuora-NTP"
        set internet-service-id 4063240
    next
    edit "Zuora-Inbound_Email"
        set internet-service-id 4063241
    next
    edit "Zuora-LDAP"
        set internet-service-id 4063246
    next
    edit "Zuora-NetBIOS.Session.Service"
        set internet-service-id 4063247
    next
    edit "Zuora-RTMP"
        set internet-service-id 4063248
    next
    edit "Zuora-NetBIOS.Name.Service"
        set internet-service-id 4063256
    next
    edit "AdRoll-Other"
        set internet-service-id 4128768
    next
    edit "AdRoll-Web"
        set internet-service-id 4128769
    next
    edit "AdRoll-ICMP"
        set internet-service-id 4128770
    next
    edit "AdRoll-DNS"
        set internet-service-id 4128771
    next
    edit "AdRoll-Outbound_Email"
        set internet-service-id 4128772
    next
    edit "AdRoll-SSH"
        set internet-service-id 4128774
    next
    edit "AdRoll-FTP"
        set internet-service-id 4128775
    next
    edit "AdRoll-NTP"
        set internet-service-id 4128776
    next
    edit "AdRoll-Inbound_Email"
        set internet-service-id 4128777
    next
    edit "AdRoll-LDAP"
        set internet-service-id 4128782
    next
    edit "AdRoll-NetBIOS.Session.Service"
        set internet-service-id 4128783
    next
    edit "AdRoll-RTMP"
        set internet-service-id 4128784
    next
    edit "AdRoll-NetBIOS.Name.Service"
        set internet-service-id 4128792
    next
    edit "Xactly-Other"
        set internet-service-id 4194304
    next
    edit "Xactly-Web"
        set internet-service-id 4194305
    next
    edit "Xactly-ICMP"
        set internet-service-id 4194306
    next
    edit "Xactly-DNS"
        set internet-service-id 4194307
    next
    edit "Xactly-Outbound_Email"
        set internet-service-id 4194308
    next
    edit "Xactly-SSH"
        set internet-service-id 4194310
    next
    edit "Xactly-FTP"
        set internet-service-id 4194311
    next
    edit "Xactly-NTP"
        set internet-service-id 4194312
    next
    edit "Xactly-Inbound_Email"
        set internet-service-id 4194313
    next
    edit "Xactly-LDAP"
        set internet-service-id 4194318
    next
    edit "Xactly-NetBIOS.Session.Service"
        set internet-service-id 4194319
    next
    edit "Xactly-RTMP"
        set internet-service-id 4194320
    next
    edit "Xactly-NetBIOS.Name.Service"
        set internet-service-id 4194328
    next
    edit "Intuit-Other"
        set internet-service-id 4259840
    next
    edit "Intuit-Web"
        set internet-service-id 4259841
    next
    edit "Intuit-ICMP"
        set internet-service-id 4259842
    next
    edit "Intuit-DNS"
        set internet-service-id 4259843
    next
    edit "Intuit-Outbound_Email"
        set internet-service-id 4259844
    next
    edit "Intuit-SSH"
        set internet-service-id 4259846
    next
    edit "Intuit-FTP"
        set internet-service-id 4259847
    next
    edit "Intuit-NTP"
        set internet-service-id 4259848
    next
    edit "Intuit-Inbound_Email"
        set internet-service-id 4259849
    next
    edit "Intuit-LDAP"
        set internet-service-id 4259854
    next
    edit "Intuit-NetBIOS.Session.Service"
        set internet-service-id 4259855
    next
    edit "Intuit-RTMP"
        set internet-service-id 4259856
    next
    edit "Intuit-NetBIOS.Name.Service"
        set internet-service-id 4259864
    next
    edit "Marketo-Other"
        set internet-service-id 4325376
    next
    edit "Marketo-Web"
        set internet-service-id 4325377
    next
    edit "Marketo-ICMP"
        set internet-service-id 4325378
    next
    edit "Marketo-DNS"
        set internet-service-id 4325379
    next
    edit "Marketo-Outbound_Email"
        set internet-service-id 4325380
    next
    edit "Marketo-SSH"
        set internet-service-id 4325382
    next
    edit "Marketo-FTP"
        set internet-service-id 4325383
    next
    edit "Marketo-NTP"
        set internet-service-id 4325384
    next
    edit "Marketo-Inbound_Email"
        set internet-service-id 4325385
    next
    edit "Marketo-LDAP"
        set internet-service-id 4325390
    next
    edit "Marketo-NetBIOS.Session.Service"
        set internet-service-id 4325391
    next
    edit "Marketo-RTMP"
        set internet-service-id 4325392
    next
    edit "Marketo-NetBIOS.Name.Service"
        set internet-service-id 4325400
    next
    edit "Bill-Other"
        set internet-service-id 4456448
    next
    edit "Bill-Web"
        set internet-service-id 4456449
    next
    edit "Bill-ICMP"
        set internet-service-id 4456450
    next
    edit "Bill-DNS"
        set internet-service-id 4456451
    next
    edit "Bill-Outbound_Email"
        set internet-service-id 4456452
    next
    edit "Bill-SSH"
        set internet-service-id 4456454
    next
    edit "Bill-FTP"
        set internet-service-id 4456455
    next
    edit "Bill-NTP"
        set internet-service-id 4456456
    next
    edit "Bill-Inbound_Email"
        set internet-service-id 4456457
    next
    edit "Bill-LDAP"
        set internet-service-id 4456462
    next
    edit "Bill-NetBIOS.Session.Service"
        set internet-service-id 4456463
    next
    edit "Bill-RTMP"
        set internet-service-id 4456464
    next
    edit "Bill-NetBIOS.Name.Service"
        set internet-service-id 4456472
    next
    edit "Shopify-Other"
        set internet-service-id 4521984
    next
    edit "Shopify-Web"
        set internet-service-id 4521985
    next
    edit "Shopify-ICMP"
        set internet-service-id 4521986
    next
    edit "Shopify-DNS"
        set internet-service-id 4521987
    next
    edit "Shopify-Outbound_Email"
        set internet-service-id 4521988
    next
    edit "Shopify-SSH"
        set internet-service-id 4521990
    next
    edit "Shopify-FTP"
        set internet-service-id 4521991
    next
    edit "Shopify-NTP"
        set internet-service-id 4521992
    next
    edit "Shopify-Inbound_Email"
        set internet-service-id 4521993
    next
    edit "Shopify-LDAP"
        set internet-service-id 4521998
    next
    edit "Shopify-NetBIOS.Session.Service"
        set internet-service-id 4521999
    next
    edit "Shopify-RTMP"
        set internet-service-id 4522000
    next
    edit "Shopify-NetBIOS.Name.Service"
        set internet-service-id 4522008
    next
    edit "Shopify-Shopify"
        set internet-service-id 4522162
    next
    edit "MuleSoft-Other"
        set internet-service-id 4587520
    next
    edit "MuleSoft-Web"
        set internet-service-id 4587521
    next
    edit "MuleSoft-ICMP"
        set internet-service-id 4587522
    next
    edit "MuleSoft-DNS"
        set internet-service-id 4587523
    next
    edit "MuleSoft-Outbound_Email"
        set internet-service-id 4587524
    next
    edit "MuleSoft-SSH"
        set internet-service-id 4587526
    next
    edit "MuleSoft-FTP"
        set internet-service-id 4587527
    next
    edit "MuleSoft-NTP"
        set internet-service-id 4587528
    next
    edit "MuleSoft-Inbound_Email"
        set internet-service-id 4587529
    next
    edit "MuleSoft-LDAP"
        set internet-service-id 4587534
    next
    edit "MuleSoft-NetBIOS.Session.Service"
        set internet-service-id 4587535
    next
    edit "MuleSoft-RTMP"
        set internet-service-id 4587536
    next
    edit "MuleSoft-NetBIOS.Name.Service"
        set internet-service-id 4587544
    next
    edit "Cornerstone-Other"
        set internet-service-id 4653056
    next
    edit "Cornerstone-Web"
        set internet-service-id 4653057
    next
    edit "Cornerstone-ICMP"
        set internet-service-id 4653058
    next
    edit "Cornerstone-DNS"
        set internet-service-id 4653059
    next
    edit "Cornerstone-Outbound_Email"
        set internet-service-id 4653060
    next
    edit "Cornerstone-SSH"
        set internet-service-id 4653062
    next
    edit "Cornerstone-FTP"
        set internet-service-id 4653063
    next
    edit "Cornerstone-NTP"
        set internet-service-id 4653064
    next
    edit "Cornerstone-Inbound_Email"
        set internet-service-id 4653065
    next
    edit "Cornerstone-LDAP"
        set internet-service-id 4653070
    next
    edit "Cornerstone-NetBIOS.Session.Service"
        set internet-service-id 4653071
    next
    edit "Cornerstone-RTMP"
        set internet-service-id 4653072
    next
    edit "Cornerstone-NetBIOS.Name.Service"
        set internet-service-id 4653080
    next
    edit "Eventbrite-Other"
        set internet-service-id 4718592
    next
    edit "Eventbrite-Web"
        set internet-service-id 4718593
    next
    edit "Eventbrite-ICMP"
        set internet-service-id 4718594
    next
    edit "Eventbrite-DNS"
        set internet-service-id 4718595
    next
    edit "Eventbrite-Outbound_Email"
        set internet-service-id 4718596
    next
    edit "Eventbrite-SSH"
        set internet-service-id 4718598
    next
    edit "Eventbrite-FTP"
        set internet-service-id 4718599
    next
    edit "Eventbrite-NTP"
        set internet-service-id 4718600
    next
    edit "Eventbrite-Inbound_Email"
        set internet-service-id 4718601
    next
    edit "Eventbrite-LDAP"
        set internet-service-id 4718606
    next
    edit "Eventbrite-NetBIOS.Session.Service"
        set internet-service-id 4718607
    next
    edit "Eventbrite-RTMP"
        set internet-service-id 4718608
    next
    edit "Eventbrite-NetBIOS.Name.Service"
        set internet-service-id 4718616
    next
    edit "Paychex-Other"
        set internet-service-id 4784128
    next
    edit "Paychex-Web"
        set internet-service-id 4784129
    next
    edit "Paychex-ICMP"
        set internet-service-id 4784130
    next
    edit "Paychex-DNS"
        set internet-service-id 4784131
    next
    edit "Paychex-Outbound_Email"
        set internet-service-id 4784132
    next
    edit "Paychex-SSH"
        set internet-service-id 4784134
    next
    edit "Paychex-FTP"
        set internet-service-id 4784135
    next
    edit "Paychex-NTP"
        set internet-service-id 4784136
    next
    edit "Paychex-Inbound_Email"
        set internet-service-id 4784137
    next
    edit "Paychex-LDAP"
        set internet-service-id 4784142
    next
    edit "Paychex-NetBIOS.Session.Service"
        set internet-service-id 4784143
    next
    edit "Paychex-RTMP"
        set internet-service-id 4784144
    next
    edit "Paychex-NetBIOS.Name.Service"
        set internet-service-id 4784152
    next
    edit "NewRelic-Other"
        set internet-service-id 4849664
    next
    edit "NewRelic-Web"
        set internet-service-id 4849665
    next
    edit "NewRelic-ICMP"
        set internet-service-id 4849666
    next
    edit "NewRelic-DNS"
        set internet-service-id 4849667
    next
    edit "NewRelic-Outbound_Email"
        set internet-service-id 4849668
    next
    edit "NewRelic-SSH"
        set internet-service-id 4849670
    next
    edit "NewRelic-FTP"
        set internet-service-id 4849671
    next
    edit "NewRelic-NTP"
        set internet-service-id 4849672
    next
    edit "NewRelic-Inbound_Email"
        set internet-service-id 4849673
    next
    edit "NewRelic-LDAP"
        set internet-service-id 4849678
    next
    edit "NewRelic-NetBIOS.Session.Service"
        set internet-service-id 4849679
    next
    edit "NewRelic-RTMP"
        set internet-service-id 4849680
    next
    edit "NewRelic-NetBIOS.Name.Service"
        set internet-service-id 4849688
    next
    edit "Splunk-Other"
        set internet-service-id 4915200
    next
    edit "Splunk-Web"
        set internet-service-id 4915201
    next
    edit "Splunk-ICMP"
        set internet-service-id 4915202
    next
    edit "Splunk-DNS"
        set internet-service-id 4915203
    next
    edit "Splunk-Outbound_Email"
        set internet-service-id 4915204
    next
    edit "Splunk-SSH"
        set internet-service-id 4915206
    next
    edit "Splunk-FTP"
        set internet-service-id 4915207
    next
    edit "Splunk-NTP"
        set internet-service-id 4915208
    next
    edit "Splunk-Inbound_Email"
        set internet-service-id 4915209
    next
    edit "Splunk-LDAP"
        set internet-service-id 4915214
    next
    edit "Splunk-NetBIOS.Session.Service"
        set internet-service-id 4915215
    next
    edit "Splunk-RTMP"
        set internet-service-id 4915216
    next
    edit "Splunk-NetBIOS.Name.Service"
        set internet-service-id 4915224
    next
    edit "Domo-Other"
        set internet-service-id 4980736
    next
    edit "Domo-Web"
        set internet-service-id 4980737
    next
    edit "Domo-ICMP"
        set internet-service-id 4980738
    next
    edit "Domo-DNS"
        set internet-service-id 4980739
    next
    edit "Domo-Outbound_Email"
        set internet-service-id 4980740
    next
    edit "Domo-SSH"
        set internet-service-id 4980742
    next
    edit "Domo-FTP"
        set internet-service-id 4980743
    next
    edit "Domo-NTP"
        set internet-service-id 4980744
    next
    edit "Domo-Inbound_Email"
        set internet-service-id 4980745
    next
    edit "Domo-LDAP"
        set internet-service-id 4980750
    next
    edit "Domo-NetBIOS.Session.Service"
        set internet-service-id 4980751
    next
    edit "Domo-RTMP"
        set internet-service-id 4980752
    next
    edit "Domo-NetBIOS.Name.Service"
        set internet-service-id 4980760
    next
    edit "FreshBooks-Other"
        set internet-service-id 5046272
    next
    edit "FreshBooks-Web"
        set internet-service-id 5046273
    next
    edit "FreshBooks-ICMP"
        set internet-service-id 5046274
    next
    edit "FreshBooks-DNS"
        set internet-service-id 5046275
    next
    edit "FreshBooks-Outbound_Email"
        set internet-service-id 5046276
    next
    edit "FreshBooks-SSH"
        set internet-service-id 5046278
    next
    edit "FreshBooks-FTP"
        set internet-service-id 5046279
    next
    edit "FreshBooks-NTP"
        set internet-service-id 5046280
    next
    edit "FreshBooks-Inbound_Email"
        set internet-service-id 5046281
    next
    edit "FreshBooks-LDAP"
        set internet-service-id 5046286
    next
    edit "FreshBooks-NetBIOS.Session.Service"
        set internet-service-id 5046287
    next
    edit "FreshBooks-RTMP"
        set internet-service-id 5046288
    next
    edit "FreshBooks-NetBIOS.Name.Service"
        set internet-service-id 5046296
    next
    edit "Tableau-Other"
        set internet-service-id 5111808
    next
    edit "Tableau-Web"
        set internet-service-id 5111809
    next
    edit "Tableau-ICMP"
        set internet-service-id 5111810
    next
    edit "Tableau-DNS"
        set internet-service-id 5111811
    next
    edit "Tableau-Outbound_Email"
        set internet-service-id 5111812
    next
    edit "Tableau-SSH"
        set internet-service-id 5111814
    next
    edit "Tableau-FTP"
        set internet-service-id 5111815
    next
    edit "Tableau-NTP"
        set internet-service-id 5111816
    next
    edit "Tableau-Inbound_Email"
        set internet-service-id 5111817
    next
    edit "Tableau-LDAP"
        set internet-service-id 5111822
    next
    edit "Tableau-NetBIOS.Session.Service"
        set internet-service-id 5111823
    next
    edit "Tableau-RTMP"
        set internet-service-id 5111824
    next
    edit "Tableau-NetBIOS.Name.Service"
        set internet-service-id 5111832
    next
    edit "Druva-Other"
        set internet-service-id 5177344
    next
    edit "Druva-Web"
        set internet-service-id 5177345
    next
    edit "Druva-ICMP"
        set internet-service-id 5177346
    next
    edit "Druva-DNS"
        set internet-service-id 5177347
    next
    edit "Druva-Outbound_Email"
        set internet-service-id 5177348
    next
    edit "Druva-SSH"
        set internet-service-id 5177350
    next
    edit "Druva-FTP"
        set internet-service-id 5177351
    next
    edit "Druva-NTP"
        set internet-service-id 5177352
    next
    edit "Druva-Inbound_Email"
        set internet-service-id 5177353
    next
    edit "Druva-LDAP"
        set internet-service-id 5177358
    next
    edit "Druva-NetBIOS.Session.Service"
        set internet-service-id 5177359
    next
    edit "Druva-RTMP"
        set internet-service-id 5177360
    next
    edit "Druva-NetBIOS.Name.Service"
        set internet-service-id 5177368
    next
    edit "Act-on-Other"
        set internet-service-id 5242880
    next
    edit "Act-on-Web"
        set internet-service-id 5242881
    next
    edit "Act-on-ICMP"
        set internet-service-id 5242882
    next
    edit "Act-on-DNS"
        set internet-service-id 5242883
    next
    edit "Act-on-Outbound_Email"
        set internet-service-id 5242884
    next
    edit "Act-on-SSH"
        set internet-service-id 5242886
    next
    edit "Act-on-FTP"
        set internet-service-id 5242887
    next
    edit "Act-on-NTP"
        set internet-service-id 5242888
    next
    edit "Act-on-Inbound_Email"
        set internet-service-id 5242889
    next
    edit "Act-on-LDAP"
        set internet-service-id 5242894
    next
    edit "Act-on-NetBIOS.Session.Service"
        set internet-service-id 5242895
    next
    edit "Act-on-RTMP"
        set internet-service-id 5242896
    next
    edit "Act-on-NetBIOS.Name.Service"
        set internet-service-id 5242904
    next
    edit "GoodData-Other"
        set internet-service-id 5308416
    next
    edit "GoodData-Web"
        set internet-service-id 5308417
    next
    edit "GoodData-ICMP"
        set internet-service-id 5308418
    next
    edit "GoodData-DNS"
        set internet-service-id 5308419
    next
    edit "GoodData-Outbound_Email"
        set internet-service-id 5308420
    next
    edit "GoodData-SSH"
        set internet-service-id 5308422
    next
    edit "GoodData-FTP"
        set internet-service-id 5308423
    next
    edit "GoodData-NTP"
        set internet-service-id 5308424
    next
    edit "GoodData-Inbound_Email"
        set internet-service-id 5308425
    next
    edit "GoodData-LDAP"
        set internet-service-id 5308430
    next
    edit "GoodData-NetBIOS.Session.Service"
        set internet-service-id 5308431
    next
    edit "GoodData-RTMP"
        set internet-service-id 5308432
    next
    edit "GoodData-NetBIOS.Name.Service"
        set internet-service-id 5308440
    next
    edit "SurveyMonkey-Other"
        set internet-service-id 5373952
    next
    edit "SurveyMonkey-Web"
        set internet-service-id 5373953
    next
    edit "SurveyMonkey-ICMP"
        set internet-service-id 5373954
    next
    edit "SurveyMonkey-DNS"
        set internet-service-id 5373955
    next
    edit "SurveyMonkey-Outbound_Email"
        set internet-service-id 5373956
    next
    edit "SurveyMonkey-SSH"
        set internet-service-id 5373958
    next
    edit "SurveyMonkey-FTP"
        set internet-service-id 5373959
    next
    edit "SurveyMonkey-NTP"
        set internet-service-id 5373960
    next
    edit "SurveyMonkey-Inbound_Email"
        set internet-service-id 5373961
    next
    edit "SurveyMonkey-LDAP"
        set internet-service-id 5373966
    next
    edit "SurveyMonkey-NetBIOS.Session.Service"
        set internet-service-id 5373967
    next
    edit "SurveyMonkey-RTMP"
        set internet-service-id 5373968
    next
    edit "SurveyMonkey-NetBIOS.Name.Service"
        set internet-service-id 5373976
    next
    edit "Cvent-Other"
        set internet-service-id 5439488
    next
    edit "Cvent-Web"
        set internet-service-id 5439489
    next
    edit "Cvent-ICMP"
        set internet-service-id 5439490
    next
    edit "Cvent-DNS"
        set internet-service-id 5439491
    next
    edit "Cvent-Outbound_Email"
        set internet-service-id 5439492
    next
    edit "Cvent-SSH"
        set internet-service-id 5439494
    next
    edit "Cvent-FTP"
        set internet-service-id 5439495
    next
    edit "Cvent-NTP"
        set internet-service-id 5439496
    next
    edit "Cvent-Inbound_Email"
        set internet-service-id 5439497
    next
    edit "Cvent-LDAP"
        set internet-service-id 5439502
    next
    edit "Cvent-NetBIOS.Session.Service"
        set internet-service-id 5439503
    next
    edit "Cvent-RTMP"
        set internet-service-id 5439504
    next
    edit "Cvent-NetBIOS.Name.Service"
        set internet-service-id 5439512
    next
    edit "Blackbaud-Other"
        set internet-service-id 5505024
    next
    edit "Blackbaud-Web"
        set internet-service-id 5505025
    next
    edit "Blackbaud-ICMP"
        set internet-service-id 5505026
    next
    edit "Blackbaud-DNS"
        set internet-service-id 5505027
    next
    edit "Blackbaud-Outbound_Email"
        set internet-service-id 5505028
    next
    edit "Blackbaud-SSH"
        set internet-service-id 5505030
    next
    edit "Blackbaud-FTP"
        set internet-service-id 5505031
    next
    edit "Blackbaud-NTP"
        set internet-service-id 5505032
    next
    edit "Blackbaud-Inbound_Email"
        set internet-service-id 5505033
    next
    edit "Blackbaud-LDAP"
        set internet-service-id 5505038
    next
    edit "Blackbaud-NetBIOS.Session.Service"
        set internet-service-id 5505039
    next
    edit "Blackbaud-RTMP"
        set internet-service-id 5505040
    next
    edit "Blackbaud-NetBIOS.Name.Service"
        set internet-service-id 5505048
    next
    edit "InsideSales-Other"
        set internet-service-id 5570560
    next
    edit "InsideSales-Web"
        set internet-service-id 5570561
    next
    edit "InsideSales-ICMP"
        set internet-service-id 5570562
    next
    edit "InsideSales-DNS"
        set internet-service-id 5570563
    next
    edit "InsideSales-Outbound_Email"
        set internet-service-id 5570564
    next
    edit "InsideSales-SSH"
        set internet-service-id 5570566
    next
    edit "InsideSales-FTP"
        set internet-service-id 5570567
    next
    edit "InsideSales-NTP"
        set internet-service-id 5570568
    next
    edit "InsideSales-Inbound_Email"
        set internet-service-id 5570569
    next
    edit "InsideSales-LDAP"
        set internet-service-id 5570574
    next
    edit "InsideSales-NetBIOS.Session.Service"
        set internet-service-id 5570575
    next
    edit "InsideSales-RTMP"
        set internet-service-id 5570576
    next
    edit "InsideSales-NetBIOS.Name.Service"
        set internet-service-id 5570584
    next
    edit "ServiceMax-Other"
        set internet-service-id 5636096
    next
    edit "ServiceMax-Web"
        set internet-service-id 5636097
    next
    edit "ServiceMax-ICMP"
        set internet-service-id 5636098
    next
    edit "ServiceMax-DNS"
        set internet-service-id 5636099
    next
    edit "ServiceMax-Outbound_Email"
        set internet-service-id 5636100
    next
    edit "ServiceMax-SSH"
        set internet-service-id 5636102
    next
    edit "ServiceMax-FTP"
        set internet-service-id 5636103
    next
    edit "ServiceMax-NTP"
        set internet-service-id 5636104
    next
    edit "ServiceMax-Inbound_Email"
        set internet-service-id 5636105
    next
    edit "ServiceMax-LDAP"
        set internet-service-id 5636110
    next
    edit "ServiceMax-NetBIOS.Session.Service"
        set internet-service-id 5636111
    next
    edit "ServiceMax-RTMP"
        set internet-service-id 5636112
    next
    edit "ServiceMax-NetBIOS.Name.Service"
        set internet-service-id 5636120
    next
    edit "Apptio-Other"
        set internet-service-id 5701632
    next
    edit "Apptio-Web"
        set internet-service-id 5701633
    next
    edit "Apptio-ICMP"
        set internet-service-id 5701634
    next
    edit "Apptio-DNS"
        set internet-service-id 5701635
    next
    edit "Apptio-Outbound_Email"
        set internet-service-id 5701636
    next
    edit "Apptio-SSH"
        set internet-service-id 5701638
    next
    edit "Apptio-FTP"
        set internet-service-id 5701639
    next
    edit "Apptio-NTP"
        set internet-service-id 5701640
    next
    edit "Apptio-Inbound_Email"
        set internet-service-id 5701641
    next
    edit "Apptio-LDAP"
        set internet-service-id 5701646
    next
    edit "Apptio-NetBIOS.Session.Service"
        set internet-service-id 5701647
    next
    edit "Apptio-RTMP"
        set internet-service-id 5701648
    next
    edit "Apptio-NetBIOS.Name.Service"
        set internet-service-id 5701656
    next
    edit "Veracode-Other"
        set internet-service-id 5767168
    next
    edit "Veracode-Web"
        set internet-service-id 5767169
    next
    edit "Veracode-ICMP"
        set internet-service-id 5767170
    next
    edit "Veracode-DNS"
        set internet-service-id 5767171
    next
    edit "Veracode-Outbound_Email"
        set internet-service-id 5767172
    next
    edit "Veracode-SSH"
        set internet-service-id 5767174
    next
    edit "Veracode-FTP"
        set internet-service-id 5767175
    next
    edit "Veracode-NTP"
        set internet-service-id 5767176
    next
    edit "Veracode-Inbound_Email"
        set internet-service-id 5767177
    next
    edit "Veracode-LDAP"
        set internet-service-id 5767182
    next
    edit "Veracode-NetBIOS.Session.Service"
        set internet-service-id 5767183
    next
    edit "Veracode-RTMP"
        set internet-service-id 5767184
    next
    edit "Veracode-NetBIOS.Name.Service"
        set internet-service-id 5767192
    next
    edit "Anaplan-Other"
        set internet-service-id 5832704
    next
    edit "Anaplan-Web"
        set internet-service-id 5832705
    next
    edit "Anaplan-ICMP"
        set internet-service-id 5832706
    next
    edit "Anaplan-DNS"
        set internet-service-id 5832707
    next
    edit "Anaplan-Outbound_Email"
        set internet-service-id 5832708
    next
    edit "Anaplan-SSH"
        set internet-service-id 5832710
    next
    edit "Anaplan-FTP"
        set internet-service-id 5832711
    next
    edit "Anaplan-NTP"
        set internet-service-id 5832712
    next
    edit "Anaplan-Inbound_Email"
        set internet-service-id 5832713
    next
    edit "Anaplan-LDAP"
        set internet-service-id 5832718
    next
    edit "Anaplan-NetBIOS.Session.Service"
        set internet-service-id 5832719
    next
    edit "Anaplan-RTMP"
        set internet-service-id 5832720
    next
    edit "Anaplan-NetBIOS.Name.Service"
        set internet-service-id 5832728
    next
    edit "Rapid7-Other"
        set internet-service-id 5898240
    next
    edit "Rapid7-Web"
        set internet-service-id 5898241
    next
    edit "Rapid7-ICMP"
        set internet-service-id 5898242
    next
    edit "Rapid7-DNS"
        set internet-service-id 5898243
    next
    edit "Rapid7-Outbound_Email"
        set internet-service-id 5898244
    next
    edit "Rapid7-SSH"
        set internet-service-id 5898246
    next
    edit "Rapid7-FTP"
        set internet-service-id 5898247
    next
    edit "Rapid7-NTP"
        set internet-service-id 5898248
    next
    edit "Rapid7-Inbound_Email"
        set internet-service-id 5898249
    next
    edit "Rapid7-LDAP"
        set internet-service-id 5898254
    next
    edit "Rapid7-NetBIOS.Session.Service"
        set internet-service-id 5898255
    next
    edit "Rapid7-RTMP"
        set internet-service-id 5898256
    next
    edit "Rapid7-NetBIOS.Name.Service"
        set internet-service-id 5898264
    next
    edit "Anydesk-Anydesk"
        set internet-service-id 5963927
    next
    edit "ESET-Eset.Service"
        set internet-service-id 6029426
    next
    edit "Slack-Other"
        set internet-service-id 6094848
    next
    edit "Slack-Web"
        set internet-service-id 6094849
    next
    edit "Slack-ICMP"
        set internet-service-id 6094850
    next
    edit "Slack-DNS"
        set internet-service-id 6094851
    next
    edit "Slack-Outbound_Email"
        set internet-service-id 6094852
    next
    edit "Slack-SSH"
        set internet-service-id 6094854
    next
    edit "Slack-FTP"
        set internet-service-id 6094855
    next
    edit "Slack-NTP"
        set internet-service-id 6094856
    next
    edit "Slack-Inbound_Email"
        set internet-service-id 6094857
    next
    edit "Slack-LDAP"
        set internet-service-id 6094862
    next
    edit "Slack-NetBIOS.Session.Service"
        set internet-service-id 6094863
    next
    edit "Slack-RTMP"
        set internet-service-id 6094864
    next
    edit "Slack-NetBIOS.Name.Service"
        set internet-service-id 6094872
    next
    edit "Slack-Slack"
        set internet-service-id 6095024
    next
    edit "ADP-Other"
        set internet-service-id 6160384
    next
    edit "ADP-Web"
        set internet-service-id 6160385
    next
    edit "ADP-ICMP"
        set internet-service-id 6160386
    next
    edit "ADP-DNS"
        set internet-service-id 6160387
    next
    edit "ADP-Outbound_Email"
        set internet-service-id 6160388
    next
    edit "ADP-SSH"
        set internet-service-id 6160390
    next
    edit "ADP-FTP"
        set internet-service-id 6160391
    next
    edit "ADP-NTP"
        set internet-service-id 6160392
    next
    edit "ADP-Inbound_Email"
        set internet-service-id 6160393
    next
    edit "ADP-LDAP"
        set internet-service-id 6160398
    next
    edit "ADP-NetBIOS.Session.Service"
        set internet-service-id 6160399
    next
    edit "ADP-RTMP"
        set internet-service-id 6160400
    next
    edit "ADP-NetBIOS.Name.Service"
        set internet-service-id 6160408
    next
    edit "Blackboard-Other"
        set internet-service-id 6225920
    next
    edit "Blackboard-Web"
        set internet-service-id 6225921
    next
    edit "Blackboard-ICMP"
        set internet-service-id 6225922
    next
    edit "Blackboard-DNS"
        set internet-service-id 6225923
    next
    edit "Blackboard-Outbound_Email"
        set internet-service-id 6225924
    next
    edit "Blackboard-SSH"
        set internet-service-id 6225926
    next
    edit "Blackboard-FTP"
        set internet-service-id 6225927
    next
    edit "Blackboard-NTP"
        set internet-service-id 6225928
    next
    edit "Blackboard-Inbound_Email"
        set internet-service-id 6225929
    next
    edit "Blackboard-LDAP"
        set internet-service-id 6225934
    next
    edit "Blackboard-NetBIOS.Session.Service"
        set internet-service-id 6225935
    next
    edit "Blackboard-RTMP"
        set internet-service-id 6225936
    next
    edit "Blackboard-NetBIOS.Name.Service"
        set internet-service-id 6225944
    next
    edit "SAP-Other"
        set internet-service-id 6291456
    next
    edit "SAP-Web"
        set internet-service-id 6291457
    next
    edit "SAP-ICMP"
        set internet-service-id 6291458
    next
    edit "SAP-DNS"
        set internet-service-id 6291459
    next
    edit "SAP-Outbound_Email"
        set internet-service-id 6291460
    next
    edit "SAP-SSH"
        set internet-service-id 6291462
    next
    edit "SAP-FTP"
        set internet-service-id 6291463
    next
    edit "SAP-NTP"
        set internet-service-id 6291464
    next
    edit "SAP-Inbound_Email"
        set internet-service-id 6291465
    next
    edit "SAP-LDAP"
        set internet-service-id 6291470
    next
    edit "SAP-NetBIOS.Session.Service"
        set internet-service-id 6291471
    next
    edit "SAP-RTMP"
        set internet-service-id 6291472
    next
    edit "SAP-NetBIOS.Name.Service"
        set internet-service-id 6291480
    next
    edit "SAP-HANA"
        set internet-service-id 6291612
    next
    edit "SAP-SuccessFactors"
        set internet-service-id 6291618
    next
    edit "Snap-Snapchat"
        set internet-service-id 6357108
    next
    edit "Zoom.us-Zoom.Meeting"
        set internet-service-id 6422646
    next
    edit "Sophos-Other"
        set internet-service-id 6488064
    next
    edit "Sophos-Web"
        set internet-service-id 6488065
    next
    edit "Sophos-ICMP"
        set internet-service-id 6488066
    next
    edit "Sophos-DNS"
        set internet-service-id 6488067
    next
    edit "Sophos-Outbound_Email"
        set internet-service-id 6488068
    next
    edit "Sophos-SSH"
        set internet-service-id 6488070
    next
    edit "Sophos-FTP"
        set internet-service-id 6488071
    next
    edit "Sophos-NTP"
        set internet-service-id 6488072
    next
    edit "Sophos-Inbound_Email"
        set internet-service-id 6488073
    next
    edit "Sophos-LDAP"
        set internet-service-id 6488078
    next
    edit "Sophos-NetBIOS.Session.Service"
        set internet-service-id 6488079
    next
    edit "Sophos-RTMP"
        set internet-service-id 6488080
    next
    edit "Sophos-NetBIOS.Name.Service"
        set internet-service-id 6488088
    next
    edit "Cloudflare-Other"
        set internet-service-id 6553600
    next
    edit "Cloudflare-Web"
        set internet-service-id 6553601
    next
    edit "Cloudflare-ICMP"
        set internet-service-id 6553602
    next
    edit "Cloudflare-DNS"
        set internet-service-id 6553603
    next
    edit "Cloudflare-Outbound_Email"
        set internet-service-id 6553604
    next
    edit "Cloudflare-SSH"
        set internet-service-id 6553606
    next
    edit "Cloudflare-FTP"
        set internet-service-id 6553607
    next
    edit "Cloudflare-NTP"
        set internet-service-id 6553608
    next
    edit "Cloudflare-Inbound_Email"
        set internet-service-id 6553609
    next
    edit "Cloudflare-LDAP"
        set internet-service-id 6553614
    next
    edit "Cloudflare-NetBIOS.Session.Service"
        set internet-service-id 6553615
    next
    edit "Cloudflare-RTMP"
        set internet-service-id 6553616
    next
    edit "Cloudflare-NetBIOS.Name.Service"
        set internet-service-id 6553624
    next
    edit "Cloudflare-CDN"
        set internet-service-id 6553737
    next
    edit "Pexip-Pexip.Meeting"
        set internet-service-id 6619256
    next
    edit "Zscaler-Other"
        set internet-service-id 6684672
    next
    edit "Zscaler-Web"
        set internet-service-id 6684673
    next
    edit "Zscaler-ICMP"
        set internet-service-id 6684674
    next
    edit "Zscaler-DNS"
        set internet-service-id 6684675
    next
    edit "Zscaler-Outbound_Email"
        set internet-service-id 6684676
    next
    edit "Zscaler-SSH"
        set internet-service-id 6684678
    next
    edit "Zscaler-FTP"
        set internet-service-id 6684679
    next
    edit "Zscaler-NTP"
        set internet-service-id 6684680
    next
    edit "Zscaler-Inbound_Email"
        set internet-service-id 6684681
    next
    edit "Zscaler-LDAP"
        set internet-service-id 6684686
    next
    edit "Zscaler-NetBIOS.Session.Service"
        set internet-service-id 6684687
    next
    edit "Zscaler-RTMP"
        set internet-service-id 6684688
    next
    edit "Zscaler-NetBIOS.Name.Service"
        set internet-service-id 6684696
    next
    edit "Zscaler-Zscaler.Cloud"
        set internet-service-id 6684793
    next
    edit "Yandex-Other"
        set internet-service-id 6750208
    next
    edit "Yandex-Web"
        set internet-service-id 6750209
    next
    edit "Yandex-ICMP"
        set internet-service-id 6750210
    next
    edit "Yandex-DNS"
        set internet-service-id 6750211
    next
    edit "Yandex-Outbound_Email"
        set internet-service-id 6750212
    next
    edit "Yandex-SSH"
        set internet-service-id 6750214
    next
    edit "Yandex-FTP"
        set internet-service-id 6750215
    next
    edit "Yandex-NTP"
        set internet-service-id 6750216
    next
    edit "Yandex-Inbound_Email"
        set internet-service-id 6750217
    next
    edit "Yandex-LDAP"
        set internet-service-id 6750222
    next
    edit "Yandex-NetBIOS.Session.Service"
        set internet-service-id 6750223
    next
    edit "Yandex-RTMP"
        set internet-service-id 6750224
    next
    edit "Yandex-NetBIOS.Name.Service"
        set internet-service-id 6750232
    next
    edit "mail.ru-Other"
        set internet-service-id 6815744
    next
    edit "mail.ru-Web"
        set internet-service-id 6815745
    next
    edit "mail.ru-ICMP"
        set internet-service-id 6815746
    next
    edit "mail.ru-DNS"
        set internet-service-id 6815747
    next
    edit "mail.ru-Outbound_Email"
        set internet-service-id 6815748
    next
    edit "mail.ru-SSH"
        set internet-service-id 6815750
    next
    edit "mail.ru-FTP"
        set internet-service-id 6815751
    next
    edit "mail.ru-NTP"
        set internet-service-id 6815752
    next
    edit "mail.ru-Inbound_Email"
        set internet-service-id 6815753
    next
    edit "mail.ru-LDAP"
        set internet-service-id 6815758
    next
    edit "mail.ru-NetBIOS.Session.Service"
        set internet-service-id 6815759
    next
    edit "mail.ru-RTMP"
        set internet-service-id 6815760
    next
    edit "mail.ru-NetBIOS.Name.Service"
        set internet-service-id 6815768
    next
    edit "Alibaba-Other"
        set internet-service-id 6881280
    next
    edit "Alibaba-Web"
        set internet-service-id 6881281
    next
    edit "Alibaba-ICMP"
        set internet-service-id 6881282
    next
    edit "Alibaba-DNS"
        set internet-service-id 6881283
    next
    edit "Alibaba-Outbound_Email"
        set internet-service-id 6881284
    next
    edit "Alibaba-SSH"
        set internet-service-id 6881286
    next
    edit "Alibaba-FTP"
        set internet-service-id 6881287
    next
    edit "Alibaba-NTP"
        set internet-service-id 6881288
    next
    edit "Alibaba-Inbound_Email"
        set internet-service-id 6881289
    next
    edit "Alibaba-LDAP"
        set internet-service-id 6881294
    next
    edit "Alibaba-NetBIOS.Session.Service"
        set internet-service-id 6881295
    next
    edit "Alibaba-RTMP"
        set internet-service-id 6881296
    next
    edit "Alibaba-NetBIOS.Name.Service"
        set internet-service-id 6881304
    next
    edit "Alibaba-Alibaba.Cloud"
        set internet-service-id 6881402
    next
    edit "GoDaddy-Other"
        set internet-service-id 6946816
    next
    edit "GoDaddy-Web"
        set internet-service-id 6946817
    next
    edit "GoDaddy-ICMP"
        set internet-service-id 6946818
    next
    edit "GoDaddy-DNS"
        set internet-service-id 6946819
    next
    edit "GoDaddy-Outbound_Email"
        set internet-service-id 6946820
    next
    edit "GoDaddy-SSH"
        set internet-service-id 6946822
    next
    edit "GoDaddy-FTP"
        set internet-service-id 6946823
    next
    edit "GoDaddy-NTP"
        set internet-service-id 6946824
    next
    edit "GoDaddy-Inbound_Email"
        set internet-service-id 6946825
    next
    edit "GoDaddy-LDAP"
        set internet-service-id 6946830
    next
    edit "GoDaddy-NetBIOS.Session.Service"
        set internet-service-id 6946831
    next
    edit "GoDaddy-RTMP"
        set internet-service-id 6946832
    next
    edit "GoDaddy-NetBIOS.Name.Service"
        set internet-service-id 6946840
    next
    edit "GoDaddy-GoDaddy.Email"
        set internet-service-id 6946939
    next
    edit "Bluejeans-Other"
        set internet-service-id 7012352
    next
    edit "Bluejeans-Web"
        set internet-service-id 7012353
    next
    edit "Bluejeans-ICMP"
        set internet-service-id 7012354
    next
    edit "Bluejeans-DNS"
        set internet-service-id 7012355
    next
    edit "Bluejeans-Outbound_Email"
        set internet-service-id 7012356
    next
    edit "Bluejeans-SSH"
        set internet-service-id 7012358
    next
    edit "Bluejeans-FTP"
        set internet-service-id 7012359
    next
    edit "Bluejeans-NTP"
        set internet-service-id 7012360
    next
    edit "Bluejeans-Inbound_Email"
        set internet-service-id 7012361
    next
    edit "Bluejeans-LDAP"
        set internet-service-id 7012366
    next
    edit "Bluejeans-NetBIOS.Session.Service"
        set internet-service-id 7012367
    next
    edit "Bluejeans-RTMP"
        set internet-service-id 7012368
    next
    edit "Bluejeans-NetBIOS.Name.Service"
        set internet-service-id 7012376
    next
    edit "Bluejeans-Bluejeans.Meeting"
        set internet-service-id 7012476
    next
    edit "Webroot-Webroot.SecureAnywhere"
        set internet-service-id 7078013
    next
    edit "Avast-Other"
        set internet-service-id 7143424
    next
    edit "Avast-Web"
        set internet-service-id 7143425
    next
    edit "Avast-ICMP"
        set internet-service-id 7143426
    next
    edit "Avast-DNS"
        set internet-service-id 7143427
    next
    edit "Avast-Outbound_Email"
        set internet-service-id 7143428
    next
    edit "Avast-SSH"
        set internet-service-id 7143430
    next
    edit "Avast-FTP"
        set internet-service-id 7143431
    next
    edit "Avast-NTP"
        set internet-service-id 7143432
    next
    edit "Avast-Inbound_Email"
        set internet-service-id 7143433
    next
    edit "Avast-LDAP"
        set internet-service-id 7143438
    next
    edit "Avast-NetBIOS.Session.Service"
        set internet-service-id 7143439
    next
    edit "Avast-RTMP"
        set internet-service-id 7143440
    next
    edit "Avast-NetBIOS.Name.Service"
        set internet-service-id 7143448
    next
    edit "Avast-Avast.Security"
        set internet-service-id 7143550
    next
    edit "Wetransfer-Other"
        set internet-service-id 7208960
    next
    edit "Wetransfer-Web"
        set internet-service-id 7208961
    next
    edit "Wetransfer-ICMP"
        set internet-service-id 7208962
    next
    edit "Wetransfer-DNS"
        set internet-service-id 7208963
    next
    edit "Wetransfer-Outbound_Email"
        set internet-service-id 7208964
    next
    edit "Wetransfer-SSH"
        set internet-service-id 7208966
    next
    edit "Wetransfer-FTP"
        set internet-service-id 7208967
    next
    edit "Wetransfer-NTP"
        set internet-service-id 7208968
    next
    edit "Wetransfer-Inbound_Email"
        set internet-service-id 7208969
    next
    edit "Wetransfer-LDAP"
        set internet-service-id 7208974
    next
    edit "Wetransfer-NetBIOS.Session.Service"
        set internet-service-id 7208975
    next
    edit "Wetransfer-RTMP"
        set internet-service-id 7208976
    next
    edit "Wetransfer-NetBIOS.Name.Service"
        set internet-service-id 7208984
    next
    edit "Sendgrid-Sendgrid.Email"
        set internet-service-id 7274623
    next
    edit "Ubiquiti-UniFi"
        set internet-service-id 7340160
    next
    edit "Lifesize-Lifesize.Cloud"
        set internet-service-id 7405697
    next
    edit "Okta-Other"
        set internet-service-id 7471104
    next
    edit "Okta-Web"
        set internet-service-id 7471105
    next
    edit "Okta-ICMP"
        set internet-service-id 7471106
    next
    edit "Okta-DNS"
        set internet-service-id 7471107
    next
    edit "Okta-Outbound_Email"
        set internet-service-id 7471108
    next
    edit "Okta-SSH"
        set internet-service-id 7471110
    next
    edit "Okta-FTP"
        set internet-service-id 7471111
    next
    edit "Okta-NTP"
        set internet-service-id 7471112
    next
    edit "Okta-Inbound_Email"
        set internet-service-id 7471113
    next
    edit "Okta-LDAP"
        set internet-service-id 7471118
    next
    edit "Okta-NetBIOS.Session.Service"
        set internet-service-id 7471119
    next
    edit "Okta-RTMP"
        set internet-service-id 7471120
    next
    edit "Okta-NetBIOS.Name.Service"
        set internet-service-id 7471128
    next
    edit "Okta-Okta"
        set internet-service-id 7471307
    next
    edit "Cybozu-Other"
        set internet-service-id 7536640
    next
    edit "Cybozu-Web"
        set internet-service-id 7536641
    next
    edit "Cybozu-ICMP"
        set internet-service-id 7536642
    next
    edit "Cybozu-DNS"
        set internet-service-id 7536643
    next
    edit "Cybozu-Outbound_Email"
        set internet-service-id 7536644
    next
    edit "Cybozu-SSH"
        set internet-service-id 7536646
    next
    edit "Cybozu-FTP"
        set internet-service-id 7536647
    next
    edit "Cybozu-NTP"
        set internet-service-id 7536648
    next
    edit "Cybozu-Inbound_Email"
        set internet-service-id 7536649
    next
    edit "Cybozu-LDAP"
        set internet-service-id 7536654
    next
    edit "Cybozu-NetBIOS.Session.Service"
        set internet-service-id 7536655
    next
    edit "Cybozu-RTMP"
        set internet-service-id 7536656
    next
    edit "Cybozu-NetBIOS.Name.Service"
        set internet-service-id 7536664
    next
    edit "VNC-Other"
        set internet-service-id 7602176
    next
    edit "VNC-Web"
        set internet-service-id 7602177
    next
    edit "VNC-ICMP"
        set internet-service-id 7602178
    next
    edit "VNC-DNS"
        set internet-service-id 7602179
    next
    edit "VNC-Outbound_Email"
        set internet-service-id 7602180
    next
    edit "VNC-SSH"
        set internet-service-id 7602182
    next
    edit "VNC-FTP"
        set internet-service-id 7602183
    next
    edit "VNC-NTP"
        set internet-service-id 7602184
    next
    edit "VNC-Inbound_Email"
        set internet-service-id 7602185
    next
    edit "VNC-LDAP"
        set internet-service-id 7602190
    next
    edit "VNC-NetBIOS.Session.Service"
        set internet-service-id 7602191
    next
    edit "VNC-RTMP"
        set internet-service-id 7602192
    next
    edit "VNC-NetBIOS.Name.Service"
        set internet-service-id 7602200
    next
    edit "Egnyte-Egnyte"
        set internet-service-id 7667846
    next
    edit "CrowdStrike-CrowdStrike.Cloud"
        set internet-service-id 7733383
    next
    edit "Aruba.it-Other"
        set internet-service-id 7798784
    next
    edit "Aruba.it-Web"
        set internet-service-id 7798785
    next
    edit "Aruba.it-ICMP"
        set internet-service-id 7798786
    next
    edit "Aruba.it-DNS"
        set internet-service-id 7798787
    next
    edit "Aruba.it-Outbound_Email"
        set internet-service-id 7798788
    next
    edit "Aruba.it-SSH"
        set internet-service-id 7798790
    next
    edit "Aruba.it-FTP"
        set internet-service-id 7798791
    next
    edit "Aruba.it-NTP"
        set internet-service-id 7798792
    next
    edit "Aruba.it-Inbound_Email"
        set internet-service-id 7798793
    next
    edit "Aruba.it-LDAP"
        set internet-service-id 7798798
    next
    edit "Aruba.it-NetBIOS.Session.Service"
        set internet-service-id 7798799
    next
    edit "Aruba.it-RTMP"
        set internet-service-id 7798800
    next
    edit "Aruba.it-NetBIOS.Name.Service"
        set internet-service-id 7798808
    next
    edit "ISLOnline-Other"
        set internet-service-id 7864320
    next
    edit "ISLOnline-Web"
        set internet-service-id 7864321
    next
    edit "ISLOnline-ICMP"
        set internet-service-id 7864322
    next
    edit "ISLOnline-DNS"
        set internet-service-id 7864323
    next
    edit "ISLOnline-Outbound_Email"
        set internet-service-id 7864324
    next
    edit "ISLOnline-SSH"
        set internet-service-id 7864326
    next
    edit "ISLOnline-FTP"
        set internet-service-id 7864327
    next
    edit "ISLOnline-NTP"
        set internet-service-id 7864328
    next
    edit "ISLOnline-Inbound_Email"
        set internet-service-id 7864329
    next
    edit "ISLOnline-LDAP"
        set internet-service-id 7864334
    next
    edit "ISLOnline-NetBIOS.Session.Service"
        set internet-service-id 7864335
    next
    edit "ISLOnline-RTMP"
        set internet-service-id 7864336
    next
    edit "ISLOnline-NetBIOS.Name.Service"
        set internet-service-id 7864344
    next
    edit "Akamai-CDN"
        set internet-service-id 7929993
    next
    edit "Rackspace-CDN"
        set internet-service-id 7995529
    next
    edit "Instart-CDN"
        set internet-service-id 8061065
    next
    edit "Bitdefender-Other"
        set internet-service-id 8126464
    next
    edit "Bitdefender-Web"
        set internet-service-id 8126465
    next
    edit "Bitdefender-ICMP"
        set internet-service-id 8126466
    next
    edit "Bitdefender-DNS"
        set internet-service-id 8126467
    next
    edit "Bitdefender-Outbound_Email"
        set internet-service-id 8126468
    next
    edit "Bitdefender-SSH"
        set internet-service-id 8126470
    next
    edit "Bitdefender-FTP"
        set internet-service-id 8126471
    next
    edit "Bitdefender-NTP"
        set internet-service-id 8126472
    next
    edit "Bitdefender-Inbound_Email"
        set internet-service-id 8126473
    next
    edit "Bitdefender-LDAP"
        set internet-service-id 8126478
    next
    edit "Bitdefender-NetBIOS.Session.Service"
        set internet-service-id 8126479
    next
    edit "Bitdefender-RTMP"
        set internet-service-id 8126480
    next
    edit "Bitdefender-NetBIOS.Name.Service"
        set internet-service-id 8126488
    next
    edit "Pingdom-Other"
        set internet-service-id 8192000
    next
    edit "Pingdom-Web"
        set internet-service-id 8192001
    next
    edit "Pingdom-ICMP"
        set internet-service-id 8192002
    next
    edit "Pingdom-DNS"
        set internet-service-id 8192003
    next
    edit "Pingdom-Outbound_Email"
        set internet-service-id 8192004
    next
    edit "Pingdom-SSH"
        set internet-service-id 8192006
    next
    edit "Pingdom-FTP"
        set internet-service-id 8192007
    next
    edit "Pingdom-NTP"
        set internet-service-id 8192008
    next
    edit "Pingdom-Inbound_Email"
        set internet-service-id 8192009
    next
    edit "Pingdom-LDAP"
        set internet-service-id 8192014
    next
    edit "Pingdom-NetBIOS.Session.Service"
        set internet-service-id 8192015
    next
    edit "Pingdom-RTMP"
        set internet-service-id 8192016
    next
    edit "Pingdom-NetBIOS.Name.Service"
        set internet-service-id 8192024
    next
    edit "UptimeRobot-Other"
        set internet-service-id 8257536
    next
    edit "UptimeRobot-Web"
        set internet-service-id 8257537
    next
    edit "UptimeRobot-ICMP"
        set internet-service-id 8257538
    next
    edit "UptimeRobot-DNS"
        set internet-service-id 8257539
    next
    edit "UptimeRobot-Outbound_Email"
        set internet-service-id 8257540
    next
    edit "UptimeRobot-SSH"
        set internet-service-id 8257542
    next
    edit "UptimeRobot-FTP"
        set internet-service-id 8257543
    next
    edit "UptimeRobot-NTP"
        set internet-service-id 8257544
    next
    edit "UptimeRobot-Inbound_Email"
        set internet-service-id 8257545
    next
    edit "UptimeRobot-LDAP"
        set internet-service-id 8257550
    next
    edit "UptimeRobot-NetBIOS.Session.Service"
        set internet-service-id 8257551
    next
    edit "UptimeRobot-RTMP"
        set internet-service-id 8257552
    next
    edit "UptimeRobot-NetBIOS.Name.Service"
        set internet-service-id 8257560
    next
    edit "UptimeRobot-UptimeRobot.Monitor"
        set internet-service-id 8257709
    next
    edit "Quovadisglobal-Other"
        set internet-service-id 8323072
    next
    edit "Quovadisglobal-Web"
        set internet-service-id 8323073
    next
    edit "Quovadisglobal-ICMP"
        set internet-service-id 8323074
    next
    edit "Quovadisglobal-DNS"
        set internet-service-id 8323075
    next
    edit "Quovadisglobal-Outbound_Email"
        set internet-service-id 8323076
    next
    edit "Quovadisglobal-SSH"
        set internet-service-id 8323078
    next
    edit "Quovadisglobal-FTP"
        set internet-service-id 8323079
    next
    edit "Quovadisglobal-NTP"
        set internet-service-id 8323080
    next
    edit "Quovadisglobal-Inbound_Email"
        set internet-service-id 8323081
    next
    edit "Quovadisglobal-LDAP"
        set internet-service-id 8323086
    next
    edit "Quovadisglobal-NetBIOS.Session.Service"
        set internet-service-id 8323087
    next
    edit "Quovadisglobal-RTMP"
        set internet-service-id 8323088
    next
    edit "Quovadisglobal-NetBIOS.Name.Service"
        set internet-service-id 8323096
    next
    edit "Splashtop-Splashtop"
        set internet-service-id 8388751
    next
    edit "Zoox-Other"
        set internet-service-id 8454144
    next
    edit "Zoox-Web"
        set internet-service-id 8454145
    next
    edit "Zoox-ICMP"
        set internet-service-id 8454146
    next
    edit "Zoox-DNS"
        set internet-service-id 8454147
    next
    edit "Zoox-Outbound_Email"
        set internet-service-id 8454148
    next
    edit "Zoox-SSH"
        set internet-service-id 8454150
    next
    edit "Zoox-FTP"
        set internet-service-id 8454151
    next
    edit "Zoox-NTP"
        set internet-service-id 8454152
    next
    edit "Zoox-Inbound_Email"
        set internet-service-id 8454153
    next
    edit "Zoox-LDAP"
        set internet-service-id 8454158
    next
    edit "Zoox-NetBIOS.Session.Service"
        set internet-service-id 8454159
    next
    edit "Zoox-RTMP"
        set internet-service-id 8454160
    next
    edit "Zoox-NetBIOS.Name.Service"
        set internet-service-id 8454168
    next
    edit "Skyfii-Other"
        set internet-service-id 8519680
    next
    edit "Skyfii-Web"
        set internet-service-id 8519681
    next
    edit "Skyfii-ICMP"
        set internet-service-id 8519682
    next
    edit "Skyfii-DNS"
        set internet-service-id 8519683
    next
    edit "Skyfii-Outbound_Email"
        set internet-service-id 8519684
    next
    edit "Skyfii-SSH"
        set internet-service-id 8519686
    next
    edit "Skyfii-FTP"
        set internet-service-id 8519687
    next
    edit "Skyfii-NTP"
        set internet-service-id 8519688
    next
    edit "Skyfii-Inbound_Email"
        set internet-service-id 8519689
    next
    edit "Skyfii-LDAP"
        set internet-service-id 8519694
    next
    edit "Skyfii-NetBIOS.Session.Service"
        set internet-service-id 8519695
    next
    edit "Skyfii-RTMP"
        set internet-service-id 8519696
    next
    edit "Skyfii-NetBIOS.Name.Service"
        set internet-service-id 8519704
    next
    edit "CoffeeBean-Other"
        set internet-service-id 8585216
    next
    edit "CoffeeBean-Web"
        set internet-service-id 8585217
    next
    edit "CoffeeBean-ICMP"
        set internet-service-id 8585218
    next
    edit "CoffeeBean-DNS"
        set internet-service-id 8585219
    next
    edit "CoffeeBean-Outbound_Email"
        set internet-service-id 8585220
    next
    edit "CoffeeBean-SSH"
        set internet-service-id 8585222
    next
    edit "CoffeeBean-FTP"
        set internet-service-id 8585223
    next
    edit "CoffeeBean-NTP"
        set internet-service-id 8585224
    next
    edit "CoffeeBean-Inbound_Email"
        set internet-service-id 8585225
    next
    edit "CoffeeBean-LDAP"
        set internet-service-id 8585230
    next
    edit "CoffeeBean-NetBIOS.Session.Service"
        set internet-service-id 8585231
    next
    edit "CoffeeBean-RTMP"
        set internet-service-id 8585232
    next
    edit "CoffeeBean-NetBIOS.Name.Service"
        set internet-service-id 8585240
    next
    edit "Cloud4Wi-Other"
        set internet-service-id 8650752
    next
    edit "Cloud4Wi-Web"
        set internet-service-id 8650753
    next
    edit "Cloud4Wi-ICMP"
        set internet-service-id 8650754
    next
    edit "Cloud4Wi-DNS"
        set internet-service-id 8650755
    next
    edit "Cloud4Wi-Outbound_Email"
        set internet-service-id 8650756
    next
    edit "Cloud4Wi-SSH"
        set internet-service-id 8650758
    next
    edit "Cloud4Wi-FTP"
        set internet-service-id 8650759
    next
    edit "Cloud4Wi-NTP"
        set internet-service-id 8650760
    next
    edit "Cloud4Wi-Inbound_Email"
        set internet-service-id 8650761
    next
    edit "Cloud4Wi-LDAP"
        set internet-service-id 8650766
    next
    edit "Cloud4Wi-NetBIOS.Session.Service"
        set internet-service-id 8650767
    next
    edit "Cloud4Wi-RTMP"
        set internet-service-id 8650768
    next
    edit "Cloud4Wi-NetBIOS.Name.Service"
        set internet-service-id 8650776
    next
    edit "Panda-Panda.Security"
        set internet-service-id 8716432
    next
    edit "Ewon-Talk2M"
        set internet-service-id 8781970
    next
    edit "Nutanix-Nutanix.Cloud"
        set internet-service-id 8847507
    next
    edit "Backblaze-Other"
        set internet-service-id 8912896
    next
    edit "Backblaze-Web"
        set internet-service-id 8912897
    next
    edit "Backblaze-ICMP"
        set internet-service-id 8912898
    next
    edit "Backblaze-DNS"
        set internet-service-id 8912899
    next
    edit "Backblaze-Outbound_Email"
        set internet-service-id 8912900
    next
    edit "Backblaze-SSH"
        set internet-service-id 8912902
    next
    edit "Backblaze-FTP"
        set internet-service-id 8912903
    next
    edit "Backblaze-NTP"
        set internet-service-id 8912904
    next
    edit "Backblaze-Inbound_Email"
        set internet-service-id 8912905
    next
    edit "Backblaze-LDAP"
        set internet-service-id 8912910
    next
    edit "Backblaze-NetBIOS.Session.Service"
        set internet-service-id 8912911
    next
    edit "Backblaze-RTMP"
        set internet-service-id 8912912
    next
    edit "Backblaze-NetBIOS.Name.Service"
        set internet-service-id 8912920
    next
    edit "Aerohive-Aerohive.Cloud"
        set internet-service-id 8978580
    next
    edit "XING-Other"
        set internet-service-id 9043968
    next
    edit "XING-Web"
        set internet-service-id 9043969
    next
    edit "XING-ICMP"
        set internet-service-id 9043970
    next
    edit "XING-DNS"
        set internet-service-id 9043971
    next
    edit "XING-Outbound_Email"
        set internet-service-id 9043972
    next
    edit "XING-SSH"
        set internet-service-id 9043974
    next
    edit "XING-FTP"
        set internet-service-id 9043975
    next
    edit "XING-NTP"
        set internet-service-id 9043976
    next
    edit "XING-Inbound_Email"
        set internet-service-id 9043977
    next
    edit "XING-LDAP"
        set internet-service-id 9043982
    next
    edit "XING-NetBIOS.Session.Service"
        set internet-service-id 9043983
    next
    edit "XING-RTMP"
        set internet-service-id 9043984
    next
    edit "XING-NetBIOS.Name.Service"
        set internet-service-id 9043992
    next
    edit "Genesys-PureCloud"
        set internet-service-id 9109653
    next
    edit "BlackBerry-Cylance"
        set internet-service-id 9175190
    next
    edit "DigiCert-OCSP"
        set internet-service-id 9240728
    next
    edit "Infomaniak-SwissTransfer"
        set internet-service-id 9306265
    next
    edit "Fuze-Fuze"
        set internet-service-id 9371802
    next
    edit "Truecaller-Truecaller"
        set internet-service-id 9437339
    next
    edit "GlobalSign-OCSP"
        set internet-service-id 9502872
    next
    edit "VeriSign-OCSP"
        set internet-service-id 9568408
    next
    edit "Sony-PlayStation.Network"
        set internet-service-id 9633952
    next
    edit "Acronis-Cyber.Cloud"
        set internet-service-id 9699489
    next
    edit "RingCentral-RingCentral"
        set internet-service-id 9765027
    next
    edit "FSecure-FSecure"
        set internet-service-id 9830564
    next
    edit "Kaseya-Kaseya.Cloud"
        set internet-service-id 9896101
    next
    edit "Shodan-Scanner"
        set internet-service-id 9961638
    next
    edit "Censys-Scanner"
        set internet-service-id 10027174
    next
    edit "Valve-Steam"
        set internet-service-id 10092711
    next
    edit "YouSeeU-Bongo"
        set internet-service-id 10158248
    next
    edit "Cato-Cato.Cloud"
        set internet-service-id 10223785
    next
    edit "Solarwinds-SpamExperts"
        set internet-service-id 10289323
    next
    edit "Solarwinds-Pingdom.Probe"
        set internet-service-id 10289326
    next
    edit "8X8-8X8.Cloud"
        set internet-service-id 10354860
    next
    edit "Zattoo-Zattoo.TV"
        set internet-service-id 10420401
    next
    edit "Datto-Datto.RMM"
        set internet-service-id 10485939
    next
    edit "Barracuda-Barracuda.Cloud"
        set internet-service-id 10551477
    next
    edit "Naver-Line"
        set internet-service-id 10617015
    next
    edit "Disney-Disney+"
        set internet-service-id 10682552
    next
    edit "DNS-DoH_DoT"
        set internet-service-id 10748089
    next
    edit "Quad9-Quad9.Standard.DNS"
        set internet-service-id 10813626
    next
    edit "Stretchoid-Scanner"
        set internet-service-id 10879142
    next
    edit "Poly-RealConnect.Service"
        set internet-service-id 10944700
    next
    edit "Telegram-Telegram"
        set internet-service-id 11010249
    next
    edit "Spotify-Spotify"
        set internet-service-id 11075786
    next
    edit "NextDNS-NextDNS"
        set internet-service-id 11141324
    next
    edit "Fastly-CDN"
        set internet-service-id 11206793
    next
    edit "Neustar-UltraDNS.Probes"
        set internet-service-id 11272397
    next
end
config firewall internet-service-definition
end
config wanopt content-delivery-network-rule
    edit "vcache://"
        set comment "Static entries are not allowed to change except disable."
        set response-expires enable
        set text-response-vcache disable
        config rules
            edit "rule1"
                config match-entries
                    edit 1
                        set pattern "/*.m3u8"
                    next
                end
                config content-id
                    set target hls-manifest
                    set start-str "/"
                end
            next
            edit "rule2"
                config match-entries
                    edit 1
                        set pattern "/*.mpd"
                    next
                end
                config content-id
                    set target dash-manifest
                    set start-str "/"
                end
            next
            edit "rule3"
                config match-entries
                    edit 1
                        set pattern "/*.ts"
                    next
                end
                config content-id
                    set target hls-fragment
                    set start-str "/"
                end
            next
            edit "rule4"
                config match-entries
                    edit 1
                        set pattern "/*.*"
                    next
                end
                config content-id
                    set target dash-fragment
                    set start-str "/"
                end
            next
        end
    next
    edit "vcache://youtube/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "youtube.com"
        set category youtube
        set text-response-vcache disable
        config rules
            edit "rule1"
                config match-entries
                    edit 1
                        set pattern "/videoplayback"
                    next
                end
                config content-id
                    set target youtube-id
                    set start-str "v="
                    set start-skip 2
                    set end-str "&"
                end
            next
            edit "rule2"
                config match-entries
                    edit 1
                        set pattern "/videoplayback"
                    next
                end
                config content-id
                    set target youtube-id
                    set start-str "v="
                    set start-skip 2
                end
            next
            edit "rule3"
                set match-mode any
                config match-entries
                    edit 1
                        set pattern "/stream_204"
                    next
                    edit 2
                        set pattern "/ptracking"
                    next
                    edit 3
                        set pattern "/get_video_info"
                    next
                end
                config content-id
                    set target youtube-map
                    set start-str "/"
                end
            next
        end
    next
    edit "vcache://googlevideo/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "googlevideo.com"
        set category youtube
        set text-response-vcache disable
        config rules
            edit "rule1"
                config match-entries
                    edit 1
                        set pattern "/videoplayback"
                    next
                end
                config content-id
                    set target youtube-id
                    set start-str "v="
                    set start-skip 2
                    set end-str "&"
                end
            next
            edit "rule2"
                config match-entries
                    edit 1
                        set pattern "/videoplayback"
                    next
                end
                config content-id
                    set target youtube-id
                    set start-str "v="
                    set start-skip 2
                end
            next
            edit "rule3"
                set match-mode any
                config match-entries
                    edit 1
                        set pattern "/stream_204"
                    next
                    edit 2
                        set pattern "/ptracking"
                    next
                    edit 3
                        set pattern "/get_video_info"
                    next
                end
                config content-id
                    set target youtube-map
                    set start-str "/"
                end
            next
        end
    next
    edit "vcache://metacafe/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "mccont.com" "akvideos.metacafe.com" "cdn.metacafe.com"
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                set skip-rule-mode any
                config match-entries
                    edit 1
                        set pattern "/*.flv"
                    next
                    edit 2
                        set pattern "/*.mp4"
                    next
                    edit 3
                        set pattern "/*.ts"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://facebook/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "fbcdn.net" "facebook.com"
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                set skip-rule-mode any
                config match-entries
                    edit 1
                        set pattern "/*.flv"
                    next
                    edit 2
                        set pattern "/*.mp4"
                    next
                    edit 3
                        set pattern "/*.ts"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://dailymotion/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "dailymotion.com" "dmcdn.net"
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                config match-entries
                    edit 1
                        set pattern "/video/*.mp4"
                    next
                    edit 2
                        set pattern "/video/*.flv"
                    next
                    edit 3
                        set pattern "/video/*.ts"
                    next
                    edit 4
                        set pattern "/video/*.on2"
                    next
                    edit 5
                        set pattern "/video/*.aac"
                    next
                    edit 6
                        set pattern "/video/*.h264"
                    next
                    edit 7
                        set pattern "/video/*.h263"
                    next
                    edit 8
                        set pattern "/sec*.mp4"
                    next
                    edit 9
                        set pattern "/sec*.flv"
                    next
                    edit 10
                        set pattern "/sec*.on2"
                    next
                    edit 11
                        set pattern "/sec*.aac"
                    next
                    edit 12
                        set pattern "/sec*.h264"
                    next
                    edit 13
                        set pattern "/sec*.h263"
                    next
                    edit 14
                        set pattern "*.ts"
                    next
                end
                config skip-entries
                    edit 1
                        set target parameter
                        set pattern "start=*"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://break/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "break.com" "0ebe.edgecastcdn.net"
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                config match-entries
                    edit 1
                        set pattern "/dnet/media/*.flv"
                    next
                    edit 2
                        set pattern "/dnet/media/*.mp4"
                    next
                end
                config skip-entries
                    edit 1
                        set target parameter
                        set pattern "ec_seek=*"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
            edit "rule2"
                config match-entries
                    edit 1
                        set pattern "/*.mp4*"
                    next
                    edit 2
                        set pattern "*Seg*"
                    next
                    edit 3
                        set pattern "*Frag*"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://msn/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "video.msn.com"
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                set skip-rule-mode any
                config match-entries
                    edit 1
                        set pattern "/*.flv"
                    next
                    edit 2
                        set pattern "/*.mp4"
                    next
                    edit 3
                        set pattern "/*.ts"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://llnwd/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "llnwd.net"
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                config match-entries
                    edit 1
                        set pattern "/*.flv"
                    next
                    edit 2
                        set pattern "/*.mp4"
                    next
                    edit 3
                        set pattern "/*.fll"
                    next
                end
                config skip-entries
                    edit 1
                        set target parameter
                        set pattern "fs=*"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://yahoo/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "yimg.com"
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                config match-entries
                    edit 1
                        set pattern "/*.flv"
                    next
                    edit 2
                        set pattern "/*.mp4"
                    next
                    edit 3
                        set pattern "/*.m4s"
                    next
                end
                config content-id
                    set target parameter
                    set start-str "vid="
                end
            next
        end
    next
    edit "vcache://myspace/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "myspacecdn.com"
        set request-cache-control enable
        set response-cache-control enable
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                set skip-rule-mode any
                config match-entries
                    edit 1
                        set pattern "/*.flv"
                    next
                    edit 2
                        set pattern "/*.mp4"
                    next
                    edit 3
                        set pattern "/*.ts"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://vimeo/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "vimeo.com" "vimeocdn.com" "56skyfiregce-a.akamaihd.net"
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                config match-entries
                    edit 1
                        set pattern "/*.flv"
                    next
                    edit 2
                        set pattern "/*.mp4"
                    next
                    edit 3
                        set pattern "/*.m4s"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://blip.tv/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "blip.tv"
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                config match-entries
                    edit 1
                        set pattern "/*.m4v"
                    next
                    edit 2
                        set pattern "/*.flv"
                    next
                    edit 3
                        set pattern "/*.mp4"
                    next
                    edit 4
                        set pattern "/*.wmv"
                    next
                    edit 5
                        set pattern "/*.rm"
                    next
                    edit 6
                        set pattern "/*.ram"
                    next
                    edit 7
                        set pattern "/*.mov"
                    next
                    edit 8
                        set pattern "/*.avi"
                    next
                end
                config skip-entries
                    edit 1
                        set target parameter
                        set pattern "ms=*"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://maker.tv/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "videos-f.jwpsrv.com"
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                config match-entries
                    edit 1
                        set pattern "/*.ts"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://aol/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "stream.aol.com" "5min.com" "vidiblevod-vh.akamaihd.net" "stg-ec-ore-u.uplynk.com" "vidible.tv"
        set response-expires enable
        config rules
            edit "rule1"
                config match-entries
                    edit 1
                        set pattern "/*.mp4"
                    next
                end
                config skip-entries
                    edit 1
                        set target parameter
                        set pattern "*timeoffset=*"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
            edit "rule2"
                config match-entries
                    edit 1
                        set pattern "/*.ts"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://clipfish/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "clipfish.de" "universal-music.de"
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                config match-entries
                    edit 1
                        set pattern "/*.flv"
                    next
                    edit 2
                        set pattern "/*.f4v"
                    next
                    edit 3
                        set pattern "/*.mp4"
                    next
                    edit 4
                        set pattern "/*.m4v"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://cnn/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "cnn-vh.akamaihd.net"
        set response-expires enable
        config rules
            edit "rule1"
                config match-entries
                    edit 1
                        set pattern "/*.flv*"
                    next
                    edit 2
                        set pattern "*Seg*"
                    next
                    edit 3
                        set pattern "*Frag*"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
            edit "rule2"
                config match-entries
                    edit 1
                        set pattern "/*.mp4*"
                    next
                    edit 2
                        set pattern "*Seg*"
                    next
                    edit 3
                        set pattern "*Frag*"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
            edit "rule3"
                config match-entries
                    edit 1
                        set pattern "/*.ts*"
                    next
                    edit 2
                        set pattern "*Seg*"
                    next
                    edit 3
                        set pattern "*Frag*"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://foxnews/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "foxnews.com" "foxnews-f.akamaihd.net"
        set response-expires enable
        config rules
            edit "rule1"
                config match-entries
                    edit 1
                        set pattern "/*.mp4*"
                    next
                    edit 2
                        set target parameter
                        set pattern "*Seg*"
                    next
                    edit 3
                        set target parameter
                        set pattern "*Frag*"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://discovery/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "discovery.com" "discidevflash-f.akamaihd.net"
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                set skip-rule-mode any
                config match-entries
                    edit 1
                        set pattern "/*.flv"
                    next
                    edit 2
                        set pattern "/*.mp4"
                    next
                    edit 3
                        set pattern "/*.ts"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://liveleak/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "edge.liveleak.com" "cdn.liveleak.com"
        set response-expires enable
        config rules
            edit "rule1"
                config match-entries
                    edit 1
                        set pattern "/*.flv"
                    next
                    edit 2
                        set target parameter
                        set pattern "*seek=0"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
            edit "rule2"
                config match-entries
                    edit 1
                        set pattern "/*.mp4"
                    next
                    edit 2
                        set target parameter
                        set pattern "*seek=0"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
            edit "rule3"
                config match-entries
                    edit 1
                        set pattern "/*.wmv"
                    next
                    edit 2
                        set target parameter
                        set pattern "*seek=0"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://sevenload/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "sevenload.com"
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                config match-entries
                    edit 1
                        set pattern "/*.flv"
                    next
                    edit 2
                        set pattern "/*.mp4"
                    next
                end
                config skip-entries
                    edit 1
                        set target parameter
                        set pattern "aktimeoffset=*"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://stupidvideos/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "stupidvideos.com"
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                set skip-rule-mode any
                config match-entries
                    edit 1
                        set pattern "/*.flv"
                    next
                    edit 2
                        set pattern "/*.mp4"
                    next
                    edit 3
                        set pattern "/*.ts"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://howcast/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "media.howcast.com"
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                config match-entries
                    edit 1
                        set pattern "/*.flv"
                    next
                    edit 2
                        set pattern "/*.mp4"
                    next
                end
                config skip-entries
                    edit 1
                        set target parameter
                        set pattern "start=*"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://vevo/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "vevo.com"
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                set skip-rule-mode any
                config match-entries
                    edit 1
                        set pattern "/*.flv"
                    next
                    edit 2
                        set pattern "/*.mp4"
                    next
                    edit 3
                        set pattern "/*.ts"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://ooyala/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "ooyala.com"
        set response-expires enable
        config rules
            edit "rule1"
                config match-entries
                    edit 1
                        set pattern "*Seg*"
                    next
                    edit 2
                        set pattern "*Frag*"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://ms-ads/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "msads.net"
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                set skip-rule-mode any
                config match-entries
                    edit 1
                        set pattern "/*.flv"
                    next
                    edit 2
                        set pattern "/*.mp4"
                    next
                    edit 3
                        set pattern "/*.ts"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://yumenetworks-ads/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "yumenetworks.com"
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                set skip-rule-mode any
                config match-entries
                    edit 1
                        set pattern "/*.flv"
                    next
                    edit 2
                        set pattern "/*.mp4"
                    next
                    edit 3
                        set pattern "/*.ts"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://2mdn-ads/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "2mdn.net"
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                set skip-rule-mode any
                config match-entries
                    edit 1
                        set pattern "/*.flv"
                    next
                    edit 2
                        set pattern "/*.mp4"
                    next
                    edit 3
                        set pattern "/*.ts"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://eyewonder-ads/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "eyewonder.com"
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                set skip-rule-mode any
                config match-entries
                    edit 1
                        set pattern "/*.flv"
                    next
                    edit 2
                        set pattern "/*.mp4"
                    next
                    edit 3
                        set pattern "/*.ts"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://eyereturn-ads/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "eyereturn.com"
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                set skip-rule-mode any
                config match-entries
                    edit 1
                        set pattern "/*.flv"
                    next
                    edit 2
                        set pattern "/*.mp4"
                    next
                    edit 3
                        set pattern "/*.ts"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://serving-sys-ads/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "serving-sys.com"
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                set skip-rule-mode any
                config match-entries
                    edit 1
                        set pattern "/*.flv"
                    next
                    edit 2
                        set pattern "/*.mp4"
                    next
                    edit 3
                        set pattern "/*.ts"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://amazonaws-ads/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "amazonaws.com"
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                set skip-rule-mode any
                config match-entries
                    edit 1
                        set pattern "/*.flv"
                    next
                    edit 2
                        set pattern "/*.mp4"
                    next
                    edit 3
                        set pattern "/*.ts"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://edgesuite-ads/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "edgesuite.net"
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                set skip-rule-mode any
                config match-entries
                    edit 1
                        set pattern "/*.flv"
                    next
                    edit 2
                        set pattern "/*.mp4"
                    next
                    edit 3
                        set pattern "/*.ts"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://gorillanation-ads/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "video.gorillanation.com"
        set response-expires enable
        config rules
            edit "rule1"
                set match-mode any
                set skip-rule-mode any
                config match-entries
                    edit 1
                        set pattern "/*.flv"
                    next
                    edit 2
                        set pattern "/*.mp4"
                    next
                    edit 3
                        set pattern "/*.ts"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://youku/"
        set comment "Static entries are not allowed to change except disable."
        set response-expires enable
        config rules
            edit "rule1"
                config match-entries
                    edit 1
                        set pattern "/youku/*.mp4"
                    next
                    edit 2
                        set target parameter
                        set pattern "*start=0"
                    next
                end
                config content-id
                    set target youku-id
                    set start-str "/"
                    set start-skip 1
                    set start-direction backward
                end
            next
            edit "rule2"
                config match-entries
                    edit 1
                        set pattern "/youku/*.flv"
                    next
                    edit 2
                        set target parameter
                        set pattern "*start=0"
                    next
                end
                config content-id
                    set target youku-id
                    set start-str "/"
                    set start-skip 1
                    set start-direction backward
                end
            next
            edit "rule3"
                config match-entries
                    edit 1
                        set pattern "/youku/*.kux"
                    next
                    edit 2
                        set target parameter
                        set pattern "*start=0"
                    next
                end
                config content-id
                    set target youku-id
                    set start-str "/"
                    set start-skip 1
                    set start-direction backward
                end
            next
            edit "rule4"
                config match-entries
                    edit 1
                        set pattern "/youku/*.mp4"
                    next
                end
                config skip-entries
                    edit 1
                        set target parameter
                        set pattern "*start=*"
                    next
                end
                config content-id
                    set target youku-id
                    set start-str "/"
                    set start-skip 1
                    set start-direction backward
                end
            next
            edit "rule5"
                config match-entries
                    edit 1
                        set pattern "/youku/*.flv"
                    next
                end
                config skip-entries
                    edit 1
                        set target parameter
                        set pattern "*start=*"
                    next
                end
                config content-id
                    set target youku-id
                    set start-str "/"
                    set start-skip 1
                    set start-direction backward
                end
            next
            edit "rule6"
                config match-entries
                    edit 1
                        set pattern "/youku/*.kux"
                    next
                end
                config skip-entries
                    edit 1
                        set target parameter
                        set pattern "*start=*"
                    next
                end
                config content-id
                    set target youku-id
                    set start-str "/"
                    set start-skip 1
                    set start-direction backward
                end
            next
        end
    next
    edit "vcache://tudou/"
        set comment "Static entries are not allowed to change except disable."
        set response-expires enable
        config rules
            edit "rule1"
                config match-entries
                    edit 1
                        set pattern "/f4v/*"
                    next
                    edit 2
                        set target parameter
                        set pattern "*id=tudou*"
                    next
                end
                config skip-entries
                    edit 1
                        set target parameter
                        set pattern "*begin=*"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                    set start-direction backward
                end
            next
        end
    next
    edit "vcache://cbc/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "cbc.ca" "mobilehls-vh.akamaihd.net"
        set response-expires enable
        config rules
            edit "rule1"
                config match-entries
                    edit 1
                        set pattern "*.mp4*"
                    next
                    edit 2
                        set pattern "*Seg*"
                    next
                    edit 3
                        set pattern "*Frag*"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
            edit "rule2"
                set match-mode any
                config match-entries
                    edit 1
                        set pattern "*.ts"
                    next
                    edit 2
                        set pattern "*.mp4"
                    next
                end
                config content-id
                    set start-str "/"
                    set start-skip 1
                end
            next
        end
    next
    edit "vcache://megaupload/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "megaupload.com"
        set response-expires enable
        config rules
            edit "rule1"
                config match-entries
                    edit 1
                        set pattern "/files/*"
                    next
                end
                config content-id
                    set target referrer
                    set start-str "d="
                    set start-skip 2
                end
            next
        end
    next
    edit "update://windowsupdate/"
        set comment "Static entries are not allowed to change except disable."
        set host-domain-name-suffix "download.windowsupdate.com"
        set request-cache-control enable
        set response-cache-control enable
        set response-expires enable
        set updateserver enable
    next
end
config log fortiguard setting
    set status enable
end
config system standalone-cluster
    config cluster-peer
    end
end
config system fortiguard
    set service-account-id "<EMAIL>"
end
config endpoint-control fctems
    edit 1
    next
    edit 2
    next
    edit 3
    next
    edit 4
    next
    edit 5
    next
    edit 6
    next
    edit 7
    next
end
config system email-server
    set server "fortinet-notifications.com"
    set port 465
    set security smtps
end
config system session-helper
    edit 1
        set name pptp
        set protocol 6
        set port 1723
    next
    edit 2
        set name h323
        set protocol 6
        set port 1720
    next
    edit 3
        set name ras
        set protocol 17
        set port 1719
    next
    edit 4
        set name tns
        set protocol 6
        set port 1521
    next
    edit 5
        set name tftp
        set protocol 17
        set port 69
    next
    edit 6
        set name rtsp
        set protocol 6
        set port 554
    next
    edit 7
        set name rtsp
        set protocol 6
        set port 7070
    next
    edit 8
        set name rtsp
        set protocol 6
        set port 8554
    next
    edit 9
        set name ftp
        set protocol 6
        set port 21
    next
    edit 10
        set name mms
        set protocol 6
        set port 1863
    next
    edit 11
        set name pmap
        set protocol 6
        set port 111
    next
    edit 12
        set name pmap
        set protocol 17
        set port 111
    next
    edit 13
        set name sip
        set protocol 17
        set port 5060
    next
    edit 14
        set name dns-udp
        set protocol 17
        set port 53
    next
    edit 15
        set name rsh
        set protocol 6
        set port 514
    next
    edit 16
        set name rsh
        set protocol 6
        set port 512
    next
    edit 17
        set name dcerpc
        set protocol 6
        set port 135
    next
    edit 18
        set name dcerpc
        set protocol 17
        set port 135
    next
    edit 19
        set name mgcp
        set protocol 17
        set port 2427
    next
    edit 20
        set name mgcp
        set protocol 17
        set port 2727
    next
end
config system auto-install
    set auto-install-config enable
    set auto-install-image enable
end
config system console
end
config system ntp
    set ntpsync enable
end
config system ftm-push
    set server-cert "Fortinet_GUI_Server"
end
config system automation-trigger
    edit "Network Down"
        set description "Default automation trigger configuration for when a network connection goes down."
        set event-type event-log
        set logid 20099
        config fields
            edit 1
                set name "status"
                set value "DOWN"
            next
        end
    next
    edit "HA Failover"
        set description "Default automation trigger configuration for when an HA failover occurs."
        set event-type ha-failover
    next
    edit "Reboot"
        set description "Default automation trigger configuration for when a FortiGate is rebooted."
        set event-type reboot
    next
    edit "FortiAnalyzer Connection Down"
        set description "Default automation trigger configuration for when the FortiAnalyzer connection is lost."
        set event-type event-log
        set logid 22902
    next
    edit "License Expired Notification"
        set description "Default automation trigger configuration for when a license is near expiration."
        set event-type license-near-expiry
        set license-type any
    next
    edit "Compromised Host - High"
        set description "Default automation trigger configuration for when a high severity compromised host is detected."
    next
    edit "Incoming Webhook Call"
        set description "Default automation trigger configuration for an incoming webhook."
        set event-type incoming-webhook
    next
    edit "Security Rating Notification"
        set description "Default automation trigger configuration for when a new Security Rating report is available."
        set event-type security-rating-summary
        set report-type any
    next
    edit "Compromised Host"
        set description "An incident of compromise has been detected on a host endpoint."
    next
    edit "Any Security Rating Notification"
        set description "A security rating summary report has been generated."
        set event-type security-rating-summary
    next
    edit "AV & IPS DB update"
        set description "The antivirus and IPS database has been updated."
        set event-type virus-ips-db-updated
    next
    edit "Configuration Change"
        set description "An administrator\'s session that changed a FortiGate\'s configuration has ended."
        set event-type config-change
    next
    edit "Conserve Mode"
        set description "A FortiGate has entered conserve mode due to low memory."
        set event-type low-memory
    next
    edit "High CPU"
        set description "A FortiGate has high CPU usage."
        set event-type high-cpu
    next
    edit "License Expiry"
        set description "A FortiGate license is near expiration."
        set event-type license-near-expiry
        set license-type any
    next
    edit "Anomaly Logs"
        set description "An anomalous event has occurred."
        set event-type anomaly-logs
    next
    edit "IPS Logs"
        set description "An IPS event has occurred."
        set event-type ips-logs
    next
    edit "SSH Logs"
        set description "A SSH event has occurred."
        set event-type ssh-logs
    next
    edit "Traffic Violation"
        set description "A traffic policy has been violated."
        set event-type traffic-violation
    next
    edit "Virus Logs"
        set description "A virus event has occurred."
        set event-type virus-logs
    next
    edit "Webfilter Violation"
        set description "A webfilter policy has been violated."
        set event-type webfilter-violation
    next
    edit "Admin Login"
        set description "A FortiOS event with specified log ID has occurred."
        set event-type event-log
        set logid 32001
    next
    edit "Local Certificate Expiry"
        set description "A local certificate is near expiration."
        set event-type local-cert-near-expiry
    next
    edit "Auto Firmware upgrade"
        set description "Automatic firmware upgrade."
        set event-type event-log
        set logid 22094 22095 32263
    next
end
config system automation-action
    edit "Default Email"
        set description "Default automation action configuration for sending an email with basic information on the log event."
        set action-type email
        set email-subject "%%log.logdesc%%"
    next
    edit "FortiExplorer Notification"
        set description "Default automation action configuration for sending a notification to any FortiExplorer mobile application."
        set action-type fortiexplorer-notification
    next
    edit "Quarantine on FortiSwitch + FortiAP"
        set description "Default automation action configuration for quarantining a MAC address on FortiSwitches and FortiAPs."
        set action-type quarantine
    next
    edit "Quarantine FortiClient EMS Endpoint"
        set description "Default automation action configuration for quarantining a FortiClient EMS endpoing device."
        set action-type quarantine-forticlient
    next
    edit "Access Layer Quarantine"
        set description "Quarantine the MAC address on access layer devices (FortiSwitch and FortiAP)."
        set action-type quarantine
    next
    edit "FortiClient Quarantine"
        set description "Use FortiClient EMS to quarantine the endpoint device."
        set action-type quarantine-forticlient
    next
    edit "FortiNAC Quarantine"
        set description "Use FortiNAC to quarantine the endpoint device."
        set action-type quarantine-fortinac
    next
    edit "IP Ban"
        set description "Ban the IP address specified in the automation trigger event."
        set action-type ban-ip
    next
    edit "Email Notification"
        set description "Send a custom email notification to the FortiCare email address registered on this device."
        set action-type email
        set forticare-email enable
        set email-subject "%%log.logdesc%%"
    next
    edit "CLI Script - System Status"
        set description "Execute a CLI script to return the system status."
        set action-type cli-script
        set script "get system status"
        set accprofile "super_admin_readonly"
    next
    edit "Reboot FortiGate"
        set description "Reboot this FortiGate."
        set action-type system-actions
        set system-action reboot
        set minimum-interval 300
    next
    edit "Shutdown FortiGate"
        set description "Shut down this FortiGate."
        set action-type system-actions
        set system-action shutdown
    next
    edit "Backup Config Disk"
        set description "Backup this FortiGate\'s configuration file to disk."
        set action-type system-actions
        set system-action backup-config
    next
end
config system automation-stitch
    edit "Network Down"
        set description "Default automation stitch to send an email when a network goes down."
        set status disable
        set trigger "Network Down"
        config actions
            edit 1
                set action "Default Email"
            next
        end
    next
    edit "HA Failover"
        set description "Default automation stitch to send an email when a HA failover is detected."
        set status disable
        set trigger "HA Failover"
        config actions
            edit 1
                set action "Default Email"
            next
        end
    next
    edit "Reboot"
        set description "Default automation stitch to send an email when a FortiGate is rebooted."
        set status disable
        set trigger "Reboot"
        config actions
            edit 1
                set action "Default Email"
            next
        end
    next
    edit "FortiAnalyzer Connection Down"
        set description "Default automation stitch to send a FortiExplorer notification when the connection to FortiAnalyzer is lost."
        set trigger "FortiAnalyzer Connection Down"
        config actions
            edit 1
                set action "FortiExplorer Notification"
            next
        end
    next
    edit "License Expired Notification"
        set description "Default automation stitch to send a FortiExplorer notification when a license is near expiration."
        set trigger "License Expired Notification"
        config actions
            edit 1
                set action "FortiExplorer Notification"
            next
        end
    next
    edit "Compromised Host Quarantine"
        set description "Default automation stitch to quarantine a high severity compromised host on FortiAPs, FortiSwitches, and FortiClient EMS."
        set status disable
        set trigger "Compromised Host - High"
        config actions
            edit 1
                set action "Quarantine on FortiSwitch + FortiAP"
            next
            edit 2
                set action "Quarantine FortiClient EMS Endpoint"
            next
        end
    next
    edit "Incoming Webhook Quarantine"
        set description "Default automation stitch to quarantine a provided MAC address on FortiAPs, FortiSwitches, and FortiClient EMS using an Incoming Webhook."
        set status disable
        set trigger "Incoming Webhook Call"
        config actions
            edit 1
                set action "Quarantine on FortiSwitch + FortiAP"
            next
            edit 2
                set action "Quarantine FortiClient EMS Endpoint"
            next
        end
    next
    edit "Security Rating Notification"
        set description "Default automation stitch to send a FortiExplorer notification when a new Security Rating report is available."
        set trigger "Security Rating Notification"
        config actions
            edit 1
                set action "FortiExplorer Notification"
            next
        end
    next
    edit "Firmware upgrade notification"
        set description "Automatic firmware upgrade notification."
        set trigger "Auto Firmware upgrade"
        set condition-logic or
        config actions
            edit 1
                set action "Email Notification"
            next
        end
    next
end
config system federated-upgrade
    set status disabled
end
config system ike
end
config system object-tagging
    edit "default"
    next
end
config switch-controller traffic-policy
    edit "quarantine"
        set description "Rate control for quarantined traffic"
        set guaranteed-bandwidth 163840
        set guaranteed-burst 8192
        set maximum-burst 163840
        set cos-queue 0
        set id 1
    next
    edit "sniffer"
        set description "Rate control for sniffer mirrored traffic"
        set guaranteed-bandwidth 50000
        set guaranteed-burst 8192
        set maximum-burst 163840
        set cos-queue 0
        set id 2
    next
end
config system settings
    set opmode transparent
    set manageip *************/*************
    set gui-explicit-proxy enable
    set gui-spamfilter enable
    set gui-waf-profile enable
    set gui-dlp-profile enable
    set gui-virtual-patch-profile enable
    set gui-casb enable
end
config system replacemsg-group
    edit "default"
        set comment "Default replacement message group."
    next
end
config firewall address
    edit "none"
        set uuid 01c609e2-42a9-51f0-6c97-824eb4c1632f
        set subnet 0.0.0.0 ***************
    next
    edit "login.microsoftonline.com"
        set uuid 01c616e4-42a9-51f0-4881-1381a8898b49
        set type fqdn
        set fqdn "login.microsoftonline.com"
    next
    edit "login.microsoft.com"
        set uuid 01c61f0e-42a9-51f0-baf8-b605a60e84d8
        set type fqdn
        set fqdn "login.microsoft.com"
    next
    edit "login.windows.net"
        set uuid 01c6268e-42a9-51f0-1ac1-b3d650929548
        set type fqdn
        set fqdn "login.windows.net"
    next
    edit "gmail.com"
        set uuid 01c62dfa-42a9-51f0-be59-ec42a90609e9
        set type fqdn
        set fqdn "gmail.com"
    next
    edit "wildcard.google.com"
        set uuid 01c63534-42a9-51f0-dfdc-84493d84ac48
        set type fqdn
        set fqdn "*.google.com"
    next
    edit "wildcard.dropbox.com"
        set uuid 01c63c6e-42a9-51f0-07a9-3c71ecc6586a
        set type fqdn
        set fqdn "*.dropbox.com"
    next
    edit "EMS_ALL_UNKNOWN_CLIENTS"
        set uuid 01cd4946-42a9-51f0-5664-be14e10795e4
        set type dynamic
        set sub-type ems-tag
        set dirty clean
    next
    edit "EMS_ALL_UNMANAGEABLE_CLIENTS"
        set uuid 01cd5558-42a9-51f0-fb95-3762fe844bed
        set type dynamic
        set sub-type ems-tag
        set dirty clean
    next
    edit "all"
        set uuid 01cd5e9a-42a9-51f0-d169-b04ba052d7ce
    next
    edit "FIREWALL_AUTH_PORTAL_ADDRESS"
        set uuid 01cd5fd0-42a9-51f0-6e2d-68df746e7842
    next
    edit "FABRIC_DEVICE"
        set uuid 01cd60ca-42a9-51f0-4731-b756779c3156
        set comment "IPv4 addresses of Fabric Devices."
    next
end
config firewall multicast-address
    edit "all_hosts"
        set start-ip *********
        set end-ip *********
    next
    edit "all_routers"
        set start-ip *********
        set end-ip *********
    next
    edit "Bonjour"
        set start-ip *********51
        set end-ip *********51
    next
    edit "EIGRP"
        set start-ip *********0
        set end-ip *********0
    next
    edit "OSPF"
        set start-ip *********
        set end-ip *********
    next
    edit "all"
        set start-ip *********
        set end-ip ***************
    next
end
config firewall address6
    edit "all"
        set uuid 01c6858e-42a9-51f0-a4a5-d8f118b79d01
    next
    edit "none"
        set uuid 01c691be-42a9-51f0-7ec1-41da1c5f472a
        set ip6 ::/128
    next
end
config firewall multicast-address6
    edit "all"
        set ip6 ff00::/8
    next
end
config firewall addrgrp
    edit "G Suite"
        set uuid 01c6474a-42a9-51f0-111b-28feb39ecc59
        set member "gmail.com" "wildcard.google.com"
    next
    edit "Microsoft Office 365"
        set uuid 01c666ee-42a9-51f0-fb6b-9b2d103d0e37
        set member "login.microsoftonline.com" "login.microsoft.com" "login.windows.net"
    next
end
config firewall wildcard-fqdn custom
    edit "adobe"
        set uuid 01cc4186-42a9-51f0-c64c-9c65f172799e
        set wildcard-fqdn "*.adobe.com"
    next
    edit "Adobe Login"
        set uuid 01cc4730-42a9-51f0-f137-018ef84f3497
        set wildcard-fqdn "*.adobelogin.com"
    next
    edit "android"
        set uuid 01cc47c6-42a9-51f0-1e9a-aec0978140f5
        set wildcard-fqdn "*.android.com"
    next
    edit "apple"
        set uuid 01cc4852-42a9-51f0-27bf-ba5d1e41d051
        set wildcard-fqdn "*.apple.com"
    next
    edit "appstore"
        set uuid 01cc48e8-42a9-51f0-d6fc-2756b4bac441
        set wildcard-fqdn "*.appstore.com"
    next
    edit "auth.gfx.ms"
        set uuid 01cc497e-42a9-51f0-f5e3-6c16496223eb
        set wildcard-fqdn "*.auth.gfx.ms"
    next
    edit "citrix"
        set uuid 01cc4a0a-42a9-51f0-3972-c6f92e780cf6
        set wildcard-fqdn "*.citrixonline.com"
    next
    edit "dropbox.com"
        set uuid 01cc4aa0-42a9-51f0-84aa-c70c1087810b
        set wildcard-fqdn "*.dropbox.com"
    next
    edit "eease"
        set uuid 01cc4b2c-42a9-51f0-12cc-5cba9b75fd6a
        set wildcard-fqdn "*.eease.com"
    next
    edit "firefox update server"
        set uuid 01cc4bb8-42a9-51f0-84a2-792222a540df
        set wildcard-fqdn "aus*.mozilla.org"
    next
    edit "fortinet"
        set uuid 01cc4c44-42a9-51f0-86a0-a220a75ac0e8
        set wildcard-fqdn "*.fortinet.com"
    next
    edit "googleapis.com"
        set uuid 01cc4cda-42a9-51f0-798e-19cf7d1a9df2
        set wildcard-fqdn "*.googleapis.com"
    next
    edit "google-drive"
        set uuid 01cc4d70-42a9-51f0-e155-bf5bc3b7cde5
        set wildcard-fqdn "*drive.google.com"
    next
    edit "google-play2"
        set uuid 01cc4dfc-42a9-51f0-bb60-86a3bbca7548
        set wildcard-fqdn "*.ggpht.com"
    next
    edit "google-play3"
        set uuid 01cc4e88-42a9-51f0-e4ba-fb0a9943bf97
        set wildcard-fqdn "*.books.google.com"
    next
    edit "Gotomeeting"
        set uuid 01cc4f28-42a9-51f0-af53-c5a264d27d75
        set wildcard-fqdn "*.gotomeeting.com"
    next
    edit "icloud"
        set uuid 01cc5108-42a9-51f0-d729-998e6577923c
        set wildcard-fqdn "*.icloud.com"
    next
    edit "itunes"
        set uuid 01cc56da-42a9-51f0-4e10-2a75dec7eff5
        set wildcard-fqdn "*itunes.apple.com"
    next
    edit "microsoft"
        set uuid 01cc5784-42a9-51f0-f7c1-df106d264c83
        set wildcard-fqdn "*.microsoft.com"
    next
    edit "skype"
        set uuid 01cc5810-42a9-51f0-3b22-61bdfcdb2142
        set wildcard-fqdn "*.messenger.live.com"
    next
    edit "softwareupdate.vmware.com"
        set uuid 01cc589c-42a9-51f0-9b8c-3bdf8e65b052
        set wildcard-fqdn "*.softwareupdate.vmware.com"
    next
    edit "verisign"
        set uuid 01cc5932-42a9-51f0-7f2b-2ae081fc27ed
        set wildcard-fqdn "*.verisign.com"
    next
    edit "Windows update 2"
        set uuid 01cc59c8-42a9-51f0-91e2-aeff5fa2c40c
        set wildcard-fqdn "*.windowsupdate.com"
    next
    edit "live.com"
        set uuid 01cc5a54-42a9-51f0-bf75-bc9a8c862ac4
        set wildcard-fqdn "*.live.com"
    next
    edit "google-play"
        set uuid 01cc5aea-42a9-51f0-f972-c80f866fb229
        set wildcard-fqdn "*play.google.com"
    next
    edit "update.microsoft.com"
        set uuid 01cc5b80-42a9-51f0-3968-362e5b892156
        set wildcard-fqdn "*update.microsoft.com"
    next
    edit "swscan.apple.com"
        set uuid 01cc5c0c-42a9-51f0-d618-9e63828b896b
        set wildcard-fqdn "*swscan.apple.com"
    next
    edit "autoupdate.opera.com"
        set uuid 01cc5ca2-42a9-51f0-b46c-77756acd3b7c
        set wildcard-fqdn "*autoupdate.opera.com"
    next
    edit "cdn-apple"
        set uuid 01cc5d2e-42a9-51f0-5c1b-918dea54854f
        set wildcard-fqdn "*.cdn-apple.com"
    next
    edit "mzstatic-apple"
        set uuid 01cc5dc4-42a9-51f0-0251-8313d7fd61f6
        set wildcard-fqdn "*.mzstatic.com"
    next
end
config firewall service category
    edit "General"
        set comment "General services."
    next
    edit "Web Access"
        set comment "Web access."
    next
    edit "File Access"
        set comment "File access."
    next
    edit "Email"
        set comment "Email services."
    next
    edit "Network Services"
        set comment "Network services."
    next
    edit "Authentication"
        set comment "Authentication service."
    next
    edit "Remote Access"
        set comment "Remote access."
    next
    edit "Tunneling"
        set comment "Tunneling service."
    next
    edit "VoIP, Messaging & Other Applications"
        set comment "VoIP, messaging, and other applications."
    next
    edit "Web Proxy"
        set comment "Explicit web proxy."
    next
end
config firewall service custom
    edit "ALL"
        set uuid 01ca83dc-42a9-51f0-457e-e5d6cf0ddb7e
        set category "General"
        set protocol IP
    next
    edit "ALL_TCP"
        set uuid 01ca8a08-42a9-51f0-2873-0f31c727a0eb
        set category "General"
        set tcp-portrange 1-65535
    next
    edit "ALL_UDP"
        set uuid 01ca8b52-42a9-51f0-8eea-e3e231d963d3
        set category "General"
        set udp-portrange 1-65535
    next
    edit "ALL_ICMP"
        set uuid 01ca8c7e-42a9-51f0-fd12-68f1baf08733
        set category "General"
        set protocol ICMP
        unset icmptype
    next
    edit "ALL_ICMP6"
        set uuid 01ca8da0-42a9-51f0-e035-e0f7ba107d31
        set category "General"
        set protocol ICMP6
        unset icmptype
    next
    edit "GRE"
        set uuid 01ca8ee0-42a9-51f0-b757-2729d29ecbf2
        set category "Tunneling"
        set protocol IP
        set protocol-number 47
    next
    edit "AH"
        set uuid 01ca900c-42a9-51f0-80d4-c36a06d3a4f9
        set category "Tunneling"
        set protocol IP
        set protocol-number 51
    next
    edit "ESP"
        set uuid 01ca912e-42a9-51f0-77d3-84cc4481061c
        set category "Tunneling"
        set protocol IP
        set protocol-number 50
    next
    edit "AOL"
        set uuid 01ca9250-42a9-51f0-8ff6-2b5206ca25dc
        set tcp-portrange 5190-5194
    next
    edit "BGP"
        set uuid 01ca9340-42a9-51f0-aaf3-2c7d17d3281a
        set category "Network Services"
        set tcp-portrange 179
    next
    edit "DHCP"
        set uuid 01ca9462-42a9-51f0-1c8b-f1b869172ddd
        set category "Network Services"
        set udp-portrange 67-68
    next
    edit "DNS"
        set uuid 01ca958e-42a9-51f0-81c0-e9b95b792532
        set category "Network Services"
        set tcp-portrange 53
        set udp-portrange 53
    next
    edit "FINGER"
        set uuid 01ca96b0-42a9-51f0-c689-7af3b12d7fbc
        set tcp-portrange 79
    next
    edit "FTP"
        set uuid 01ca97a0-42a9-51f0-bd92-575ee210d7dc
        set category "File Access"
        set tcp-portrange 21
    next
    edit "FTP_GET"
        set uuid 01ca98c2-42a9-51f0-c972-30fa2226c619
        set category "File Access"
        set tcp-portrange 21
    next
    edit "FTP_PUT"
        set uuid 01ca99e4-42a9-51f0-1cf1-a5f0da15d32e
        set category "File Access"
        set tcp-portrange 21
    next
    edit "GOPHER"
        set uuid 01ca9c82-42a9-51f0-f975-fd44de4a9db8
        set tcp-portrange 70
    next
    edit "H323"
        set uuid 01ca9f84-42a9-51f0-d48e-ace3d61deba5
        set category "VoIP, Messaging & Other Applications"
        set tcp-portrange 1720 1503
        set udp-portrange 1719
    next
    edit "HTTP"
        set uuid 01caa0ba-42a9-51f0-e3d5-a606527f6558
        set category "Web Access"
        set tcp-portrange 80
    next
    edit "HTTPS"
        set uuid 01caa1dc-42a9-51f0-2739-c9ccfec9fe72
        set category "Web Access"
        set tcp-portrange 443
    next
    edit "IKE"
        set uuid 01caa312-42a9-51f0-43c8-bee27993d6ad
        set category "Tunneling"
        set udp-portrange 500 4500
    next
    edit "IMAP"
        set uuid 01caa448-42a9-51f0-3adb-d412b675b684
        set category "Email"
        set tcp-portrange 143
    next
    edit "IMAPS"
        set uuid 01caa57e-42a9-51f0-55e2-f5d9c0ed961b
        set category "Email"
        set tcp-portrange 993
    next
    edit "Internet-Locator-Service"
        set uuid 01caa6a0-42a9-51f0-be92-2843cf25f93b
        set tcp-portrange 389
    next
    edit "IRC"
        set uuid 01caa79a-42a9-51f0-27c0-e26de4140fe6
        set category "VoIP, Messaging & Other Applications"
        set tcp-portrange 6660-6669
    next
    edit "L2TP"
        set uuid 01caa8bc-42a9-51f0-483a-1a2e538dce10
        set category "Tunneling"
        set tcp-portrange 1701
        set udp-portrange 1701
    next
    edit "LDAP"
        set uuid 01caa9f2-42a9-51f0-7ad2-f0b4bf487f46
        set category "Authentication"
        set tcp-portrange 389
    next
    edit "NetMeeting"
        set uuid 01caab14-42a9-51f0-8b3c-ab14b5aa84a3
        set tcp-portrange 1720
    next
    edit "NFS"
        set uuid 01caac0e-42a9-51f0-364c-ced8a468acd4
        set category "File Access"
        set tcp-portrange 111 2049
        set udp-portrange 111 2049
    next
    edit "NNTP"
        set uuid 01caad30-42a9-51f0-2de1-e2f6f3eb67c5
        set tcp-portrange 119
    next
    edit "NTP"
        set uuid 01caae20-42a9-51f0-6e39-2efb0bb99851
        set category "Network Services"
        set tcp-portrange 123
        set udp-portrange 123
    next
    edit "OSPF"
        set uuid 01caaf60-42a9-51f0-3745-1d2ed4dd55ce
        set category "Network Services"
        set protocol IP
        set protocol-number 89
    next
    edit "PC-Anywhere"
        set uuid 01cab26c-42a9-51f0-18e3-4227089649e9
        set category "Remote Access"
        set tcp-portrange 5631
        set udp-portrange 5632
    next
    edit "PING"
        set uuid 01cab3fc-42a9-51f0-0839-101853b5606b
        set category "Network Services"
        set protocol ICMP
        set icmptype 8
        unset icmpcode
    next
    edit "TIMESTAMP"
        set uuid 01cab532-42a9-51f0-e45e-926e9bce15c2
        set protocol ICMP
        set icmptype 13
        unset icmpcode
    next
    edit "INFO_REQUEST"
        set uuid 01cab636-42a9-51f0-305f-8b72aa1b8935
        set protocol ICMP
        set icmptype 15
        unset icmpcode
    next
    edit "INFO_ADDRESS"
        set uuid 01cab744-42a9-51f0-8533-dc059e4edc0c
        set protocol ICMP
        set icmptype 17
        unset icmpcode
    next
    edit "ONC-RPC"
        set uuid 01cab83e-42a9-51f0-c96a-092b5d5baa2c
        set category "Remote Access"
        set tcp-portrange 111
        set udp-portrange 111
    next
    edit "DCE-RPC"
        set uuid 01cab960-42a9-51f0-5153-702ba0277fa8
        set category "Remote Access"
        set tcp-portrange 135
        set udp-portrange 135
    next
    edit "POP3"
        set uuid 01caba8c-42a9-51f0-5e75-5c00755975b7
        set category "Email"
        set tcp-portrange 110
    next
    edit "POP3S"
        set uuid 01cabbae-42a9-51f0-f150-0798846fdc5e
        set category "Email"
        set tcp-portrange 995
    next
    edit "PPTP"
        set uuid 01cabce4-42a9-51f0-3995-3ec016729405
        set category "Tunneling"
        set tcp-portrange 1723
    next
    edit "QUAKE"
        set uuid 01cabe06-42a9-51f0-f073-118600441eda
        set udp-portrange 26000 27000 27910 27960
    next
    edit "RAUDIO"
        set uuid 01cabf00-42a9-51f0-6a48-f7aea52dc2d9
        set udp-portrange 7070
    next
    edit "REXEC"
        set uuid 01cabff0-42a9-51f0-32f4-8d60e9ff8791
        set tcp-portrange 512
    next
    edit "RIP"
        set uuid 01cac0e0-42a9-51f0-f4bd-5d057d3f7290
        set category "Network Services"
        set udp-portrange 520
    next
    edit "RLOGIN"
        set uuid 01cac20c-42a9-51f0-0a94-5a57a82cb4a1
        set tcp-portrange 513:512-1023
    next
    edit "RSH"
        set uuid 01cac306-42a9-51f0-c26d-e76fd8a608f0
        set tcp-portrange 514:512-1023
    next
    edit "SCCP"
        set uuid 01cac540-42a9-51f0-94dd-af95511bffa6
        set category "VoIP, Messaging & Other Applications"
        set tcp-portrange 2000
    next
    edit "SIP"
        set uuid 01cacb76-42a9-51f0-32f5-f11844b77aa6
        set category "VoIP, Messaging & Other Applications"
        set tcp-portrange 5060
        set udp-portrange 5060
    next
    edit "SIP-MSNmessenger"
        set uuid 01caccac-42a9-51f0-2b99-6d5de1cb2497
        set category "VoIP, Messaging & Other Applications"
        set tcp-portrange 1863
    next
    edit "SAMBA"
        set uuid 01cacdd8-42a9-51f0-6995-76a240d437dc
        set category "File Access"
        set tcp-portrange 139
    next
    edit "SMTP"
        set uuid 01cacf0e-42a9-51f0-8648-38fac90376a1
        set category "Email"
        set tcp-portrange 25
    next
    edit "SMTPS"
        set uuid 01cad03a-42a9-51f0-3bd9-603a66eed393
        set category "Email"
        set tcp-portrange 465
    next
    edit "SNMP"
        set uuid 01cad166-42a9-51f0-950d-a13516b6e04f
        set category "Network Services"
        set tcp-portrange 161-162
        set udp-portrange 161-162
    next
    edit "SSH"
        set uuid 01cad292-42a9-51f0-fe62-bc029077ae9e
        set category "Remote Access"
        set tcp-portrange 22
    next
    edit "SYSLOG"
        set uuid 01cad3b4-42a9-51f0-daa7-ff2642c272b6
        set category "Network Services"
        set udp-portrange 514
    next
    edit "TALK"
        set uuid 01cad4e0-42a9-51f0-55f9-203049574dcf
        set udp-portrange 517-518
    next
    edit "TELNET"
        set uuid 01cad5da-42a9-51f0-6ac8-df73d4618bb3
        set category "Remote Access"
        set tcp-portrange 23
    next
    edit "TFTP"
        set uuid 01cad710-42a9-51f0-2c13-43d8124c1d74
        set category "File Access"
        set udp-portrange 69
    next
    edit "MGCP"
        set uuid 01cad832-42a9-51f0-6f09-84ef86de9b7e
        set category "VoIP, Messaging & Other Applications"
        set tcp-portrange 2428
        set udp-portrange 2427 2727
    next
    edit "UUCP"
        set uuid 01cad95e-42a9-51f0-4a6b-20d552841624
        set tcp-portrange 540
    next
    edit "VDOLIVE"
        set uuid 01cada58-42a9-51f0-e829-a528f80ae11a
        set tcp-portrange 7000-7010
    next
    edit "WAIS"
        set uuid 01cadb52-42a9-51f0-82e4-a0a1a4be2ce5
        set tcp-portrange 210
    next
    edit "WINFRAME"
        set uuid 01cadd78-42a9-51f0-7b70-d3343a61a930
        set tcp-portrange 1494 2598
    next
    edit "X-WINDOWS"
        set uuid 01cadecc-42a9-51f0-b3b9-b4467dfb02fb
        set category "Remote Access"
        set tcp-portrange 6000-6063
    next
    edit "PING6"
        set uuid 01cae00c-42a9-51f0-03c1-5b0824d6c2e2
        set protocol ICMP6
        set icmptype 128
        unset icmpcode
    next
    edit "MS-SQL"
        set uuid 01cae11a-42a9-51f0-1fe6-00a5c0d1dfbb
        set category "VoIP, Messaging & Other Applications"
        set tcp-portrange 1433 1434
    next
    edit "MYSQL"
        set uuid 01cae246-42a9-51f0-f26f-b7090aa8867e
        set category "VoIP, Messaging & Other Applications"
        set tcp-portrange 3306
    next
    edit "RDP"
        set uuid 01cae37c-42a9-51f0-ab51-3e725dce59f2
        set category "Remote Access"
        set tcp-portrange 3389
    next
    edit "VNC"
        set uuid 01cae4a8-42a9-51f0-6caf-724efb36203f
        set category "Remote Access"
        set tcp-portrange 5900
    next
    edit "DHCP6"
        set uuid 01cae5e8-42a9-51f0-5a6e-e988b51c287f
        set category "Network Services"
        set udp-portrange 546 547
    next
    edit "SQUID"
        set uuid 01cae71e-42a9-51f0-90f9-75fcf202da94
        set category "Tunneling"
        set tcp-portrange 3128
    next
    edit "SOCKS"
        set uuid 01cae84a-42a9-51f0-e8d2-ea99f636f051
        set category "Tunneling"
        set tcp-portrange 1080
        set udp-portrange 1080
    next
    edit "WINS"
        set uuid 01cae976-42a9-51f0-2293-c95bb3b912bb
        set category "Remote Access"
        set tcp-portrange 1512
        set udp-portrange 1512
    next
    edit "RADIUS"
        set uuid 01caeaa2-42a9-51f0-b1df-0a7c4633e90e
        set category "Authentication"
        set udp-portrange 1812 1813
    next
    edit "RADIUS-OLD"
        set uuid 01caebce-42a9-51f0-4bf8-53d6009bd867
        set udp-portrange 1645 1646
    next
    edit "CVSPSERVER"
        set uuid 01caecd2-42a9-51f0-634d-2fb72bc75630
        set tcp-portrange 2401
        set udp-portrange 2401
    next
    edit "AFS3"
        set uuid 01caedcc-42a9-51f0-14eb-bbe3369cd71a
        set category "File Access"
        set tcp-portrange 7000-7009
        set udp-portrange 7000-7009
    next
    edit "TRACEROUTE"
        set uuid 01caeeee-42a9-51f0-a8f1-eab3849a8095
        set category "Network Services"
        set udp-portrange 33434-33535
    next
    edit "RTSP"
        set uuid 01caf15a-42a9-51f0-90f8-9690d42183cf
        set category "VoIP, Messaging & Other Applications"
        set tcp-portrange 554 7070 8554
        set udp-portrange 554
    next
    edit "MMS"
        set uuid 01caf2fe-42a9-51f0-4966-c60f3be9b7f0
        set tcp-portrange 1755
        set udp-portrange 1024-5000
    next
    edit "KERBEROS"
        set uuid 01caf402-42a9-51f0-a7f7-95f1465cc295
        set category "Authentication"
        set tcp-portrange 88 464
        set udp-portrange 88 464
    next
    edit "LDAP_UDP"
        set uuid 01caf538-42a9-51f0-7ace-c5ebb935091c
        set category "Authentication"
        set udp-portrange 389
    next
    edit "SMB"
        set uuid 01caf66e-42a9-51f0-3af8-f07b345e8085
        set category "File Access"
        set tcp-portrange 445
    next
    edit "NONE"
        set uuid 01caf79a-42a9-51f0-f769-15d4e3d21c1e
        set tcp-portrange 0
    next
    edit "webproxy"
        set uuid 01cd07f6-42a9-51f0-64da-7633fa657428
        set proxy enable
        set category "Web Proxy"
        set protocol ALL
        set tcp-portrange 0-65535:0-65535
    next
end
config firewall service group
    edit "Email Access"
        set uuid 01cd132c-42a9-51f0-c98d-f8c9087a895e
        set member "DNS" "IMAP" "IMAPS" "POP3" "POP3S" "SMTP" "SMTPS"
    next
    edit "Web Access"
        set uuid 01cd27f4-42a9-51f0-a975-694623859ba5
        set member "DNS" "HTTP" "HTTPS"
    next
    edit "Windows AD"
        set uuid 01cd2ed4-42a9-51f0-d20a-e848a998aa1f
        set member "DCE-RPC" "DNS" "KERBEROS" "LDAP" "LDAP_UDP" "SAMBA" "SMB"
    next
    edit "Exchange Server"
        set uuid 01cd3cda-42a9-51f0-1d42-aff0d0c35edc
        set member "DCE-RPC" "DNS" "HTTPS"
    next
end
config vpn certificate ca
end
config vpn certificate local
    edit "Fortinet_CA_SSL"
        set password ENC RciadNPYc0uh9FE8tODCbQtEFE593CalHPMHpzFooTyvyPiir1Smmnb5xoWbAbZ5ZnvwNo6aSY3saj2Kw48Vzyywdctl4SFU9brwt33HoLg4WdSu+QfSQMZZULijda0eFlvpl05uOzMBTBITCCT1oTxMkjzr9a1ws7BjPWwmqLi4cYgWWWuB2GT7Pj6yI9aSk2Zj7VlmMjY3dkVA
        set comments "This is the default CA certificate the SSL Inspection will use when generating new server certificates."
        set private-key "-----BEGIN ENCRYPTED PRIVATE KEY-----
MIIFJDBWBgkqhkiG9w0BBQ0wSTAxBgkqhkiG9w0BBQwwJAQQcsdjQYhbSBDko/EQ
dB+aSgICCAAwDAYIKoZIhvcNAgkFADAUBggqhkiG9w0DBwQIMsdp5yMtaxsEggTI
G9Aaxb9dKkgiI+zbPF948e4HmrlGDNu/biNBqDiiZSz82U6tPDBZf0jPJwlxYYwm
aCsnsM+u7CKdgkMVwbMDbEpIfN8+xYT3sewP778oxVXWx5iH2EAf1CjPdW/RepxH
FjfH/8DKoRykfgmMy2Fwb2XOvCK5uBjirD6E4E5L9gdFJ+zkaZbGBlbGoVrCflPo
O7e1uqIPNcvhUHzL5nm4x6Rcs/HhQTsYTLaKtaX7YEEGlk64jPj+a0tZq5daKwgH
i0uU4q7hjTnOctScQCGhJiMIL2Jo4D8iikTvNsYYLN6B+WT1VlFGyA01WANj9pN2
hRd3nXR7hXW7ZTlMvpYzMzOowRnS/rWKane735Z+MLCDkIyJ/GhkkLt5jQiN19Fw
7t1QtLvfLTt06YS87WQPcEmzQJK0kD24Z4xh957NxfsMS9ls3rfL0ziKrgM3Mduv
eWyxXg1Xml9u6gjY4Qjz7HWhl1tXqLK2pyrHFMyIUHyDaYLiYNoCvs0ANcBkn7LH
y9xoK/rm7olmRr5kld57FZWznkW5x9hhQAZxL6VeZhZNjBWKbYgSIEhL9sl+c+6o
d+OmtlxY1dWarh5TRqry31l86w7k06mqXaOgVu09bJFLGBoggjlx94EjyNt01m1a
bucqwmqcV5k6xCsLPX6dI6pdKQ91wK57+4OSGI/YFz6Aai5wHeK37EIiooYkxQT+
zI0x2ORybWvVs4YOffvjzg8sHP4QvN1+iwycULVZGynvaypU6FNiI0/jFlV2LuxB
yr/e9iYfRkIsI7GsGtz43XgOFzLBrUhUg4KCKDFVp2OC3Qf1Ayei1a6mOb0grt+M
fRt0nYk21BrC6wK47CxdeDIPUF633tYFZxkTXdSw07LK7MmJRgN8QEptLTjlO+ij
16okSeQvhy+7N41OaA6w7W+oIDU/VY0UU51+xKPh9a/MP9p+KqySd5CTmVYov2qu
FozMdKdlgvzdhjwXSzEDIHqYI6nw16zrrWBRbDDehjOkvgqomQpbhajLxNGKYWEp
rQprZcb210cgtQ9pVX1rYtPWZy01dvwo/Ys1wMV/Pb85gaHUDIbfEveTNVnejfqn
K9i4OvDG70b6Au1ohv8YjAO33qFp5yQLX40k/hpyF+BhBLfqxOwR2o+sIBDwZhfi
C2JLQLcrlCw6JeOxyCtfTuKGffmEm/lI0MZ2c8OJGCKmt5OuN6IYXoY6oJ9Rjwkb
h/8EAe21b5BZY+aah9iVWGXMXQMrJyqRqeE5sRes07N2zLhM8Wqeg4D/ro2WHlDs
slRhvv7/i4x0yRfAhcelGqoxX2OcVpwQBbS4FkEBrNlAlUBgOLJf3FkLgDYc/7II
c559JcyTLTsF7dsg82Nq3Q68w2W0dKk8QE/HpBEr7TTdDHbYp0X6aNl0s9e8zvh5
VlYnh4ybJ0PKhSiy5WXvCyID5IVteFA0QAOgWNSDSavLIDZ6eNlNjzAiAIvhmgib
lokIA4holcHsx/TWVP/uGPRJ9Rn2VP3xcmb8AuAg2o68oauZearbyJB3LxWIX1vt
6RF1LIW6xe5IVc/MLKGyaC3HTnG9RoLR2/QY20jv0nw/DMz8rAzfUkeQFRsLzHBR
bG+ZEEVPONiQKv4XqFHnKPR85QkzE1Zf
-----END ENCRYPTED PRIVATE KEY-----"
        set certificate "-----BEGIN CERTIFICATE-----
MIID8zCCAtugAwIBAgIIFzZRFNbAMNswDQYJKoZIhvcNAQELBQAwgakxCzAJBgNV
BAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQHDAlTdW5ueXZhbGUx
ETAPBgNVBAoMCEZvcnRpbmV0MR4wHAYDVQQLDBVDZXJ0aWZpY2F0ZSBBdXRob3Jp
dHkxGTAXBgNVBAMMEEZHNkgxRVRCMjA5MDcwMzExIzAhBgkqhkiG9w0BCQEWFHN1
cHBvcnRAZm9ydGluZXQuY29tMB4XDTI0MDUyMTA0NDAxMloXDTM0MDUyMjA0NDAx
MlowgakxCzAJBgNVBAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQH
DAlTdW5ueXZhbGUxETAPBgNVBAoMCEZvcnRpbmV0MR4wHAYDVQQLDBVDZXJ0aWZp
Y2F0ZSBBdXRob3JpdHkxGTAXBgNVBAMMEEZHNkgxRVRCMjA5MDcwMzExIzAhBgkq
hkiG9w0BCQEWFHN1cHBvcnRAZm9ydGluZXQuY29tMIIBIjANBgkqhkiG9w0BAQEF
AAOCAQ8AMIIBCgKCAQEAr/khi2KT8HOQBdbwyB6UOTnwVwW51UAyhKGxBqJESlj1
BUcYHFqRz6pIRtS7nDaTRqMGwNikmPcnKUp2p/kjlSNcyr/mhDQ2S9Eb5BJcwuK5
q8cSM1x1fEDR3tERTQG982eageSM1Hqk9O9au2LthdqIlWyHi5iu7a7kHxz37KYz
eLFr1hYbYMALSqqWdfkDVRAdRW7QUWHSWIgt6LY+bE+kTVegNEwGv7aBIA7ugKl6
dJd3F8kFdbGKdFc1sN3X1XN0vCgPjFC9UtFqHB7a2sUX5GDWwTAZRLfZ/ybqqRXB
NANHZCPBHMpezho8ddFrwFBSYcj6h0TXDJ+OjbW+cwIDAQABox0wGzAMBgNVHRME
BTADAQH/MAsGA1UdDwQEAwICBDANBgkqhkiG9w0BAQsFAAOCAQEAPt2ByoxrnEBw
nNIfZbMvKlQyYDBZyCGftf0iNr1OIVHWZkkfrhlUxRVlUo1pQQKhSTbAI6mIBlob
zAsPIkyHUYbcJ/whZA2/0UMFA/oT5ddZWWuXqTSEOFdFYOWtYK39FuRAK3FvHYyb
gZCfUNfApSsHI5ik9AOwb+30zFAY0MZw4gMPVQgWW3JfIjL6QZAz2Yx3OlbmxgXK
OFhoKoM8CNwBglmCG8UtJEh/lRa50D/CKr6W99D1QhuWOGR/Wc1utgq9VJRNXG4J
a70iaky+zB8jlDVsZfnEbaGW0iCrbxn5aaA3IrbIbXr24i2DyenB5dgrxOjtd7Yk
rY2QIWOeww==
-----END CERTIFICATE-----"
        set range global
        set source factory
        set last-updated 1749195409
    next
    edit "Fortinet_CA_Untrusted"
        set password ENC zIvnaYsXIb2GABNKejOyUb8CZYvVkkG/Z1MvzF176mrD9xxXVE44PO5gmKoMwM6Gshr/SOBPH4opz59ZrRXbytxFEDrHcAZrmiXS+i+/CzpBUoNfxDDy0/U44oP7B5mqYx2x1JUe1aXiTH/ETR1lQBOd1XCSSS4GnRT+vBdLarU0JQFLF0nqNtFnitICtkdR5E8d/VlmMjY3dkVA
        set comments "This is the default CA certificate the SSL Inspection will use when generating new server certificates."
        set private-key "-----BEGIN ENCRYPTED PRIVATE KEY-----
MIIFJDBWBgkqhkiG9w0BBQ0wSTAxBgkqhkiG9w0BBQwwJAQQjJm7StFdHzoQYI8f
MXln/wICCAAwDAYIKoZIhvcNAgkFADAUBggqhkiG9w0DBwQIqXXL4TC8szUEggTI
EW1fuE0ML+Z7dA9ReAYWp+tZn8WVZ4iwZidfaS+FJ/6PdTLxFcpDrVJSILtemYhQ
sEV+Nr/X81Z5VCWTR6Fq2TXJfIPm2vnuNYchqc3isEB7WioQ4A2LckcbBxfVY+qU
8NG9IHpr9+Da5s2C0SISOXA4buxcMaLQTVueK8ceaI2o2OvWqU1HOiY5/4Elanbo
htIUpvMTdFJ7eaMIZLhsYGy2GSb9jzgzHh6kkXNuf1vaITahpuEhj42xbzUfxFAT
x0D03WIyfk8fJXJu4FC46jqJzxVnGrqG+DfRNBHYDaIzaB/Mh4oQbllcGZftLbKs
qJtr2awQ80OaDWThUmVBql6L3r+EGWXo3xiWiaeQ1SiSrZirNPUQf46sBwz+m3zZ
nHkJM/bDuraO2vnS4GRhu0XCCW3QSnhTbCIYvukuqG3of3xhowlSsYB+yW59MVbd
shmpFYuv6qSPLE3UaZeRXZRydPGAnxgxK0nlz0QdewjyYoFv+akm9FSrr6qi8ch9
ycLfJuQYRmCJm63pDZzVAijWeeW7/WQi5IXPjRgG8xOp1B06lPia78VwMg1qj49z
lK1hv8ObEDH92WnURdnsmQIlSNVGBRoha1vD0LjnkA3BFjI0ym97FCWLr9RwfAY2
hLAZUNOg7tBtl26ZOaXmIYorBMb59FUpFCGJe2SXAIwQPxOlsM3FHhJx1GetvmCE
VH1TuoNyIU2oqJpX5cWdx3Tqt4GdeSgywCoKZz9OFw7JCDlYB/7LCQCG8SOy+Kft
2mQFFLLiHyNtpStjTeEywBvgPyhHANjxbJQMO0sUthVU2c/WPN4K3c9/eJYkcLJP
ca02kLsg77w905LQdH4YSNJRbP5FMK5ufnjoOw2dsIzlI5NdY+8ydtnaHfQ6IEcG
IasQ/q0TeC5dYawNq3/GQ4UMIffmyEoRR95X0uLpk23WCeXxrlS3YOX4xQ14HqPY
LVWw3LuMPjZiPd9WIer0r6wJ6Jg6k+EINwQ0ut9xKzgR7PXK4RUi8DYLdcfkMMGz
35Hcsxv05CypTswR1pdQbLTXjJHKRl1oi0hYe+qYt0q+TEU9HA2swRzzegdCuXjX
1pZSiWFXqmsZAc7qpagBsqduUy8Tokz03Kw9YDl3OmIdlYI3Hi584/yyXQKqXjJm
ScHM76MIL38JD1mLRVltaLQmH0JzIUzlVp/P8eZTBoeuEC+iIhk36utd8bZjlDQc
2LgIJqrzXYsYkZjjrlN59cIbX9bTfxXgZI/BP1oYWQ66LNq3aBsFqb+/FzhNl8ci
03M0h0CNb7dgt95wpDLTJvk2cq6ejF+k40YVEuTrmPMtI0WK8GWnJPzPRRVQpftv
Iw8RGDUS7ohk9EZey2qtlZTldc3MTOikCwsxo4KHNXVrwQM3SoLA/eUmPe9WMK2u
1LtrtJybi/ijVHoNtErA0I04EZQIsdZ85PFbZREwri2eilpi5wPaiR3Bi/aE4goI
Xr2HrvB2z4nNsCGqWjarE/u5HU26Eh6XVg5YvLyWtKPYAoixavoKGVFiYrxOfzNs
s8tQOPuonrpBpogWFTVRjLY/QKJOAO3y0fcJz+kCoTpDrlqzuZnSYSc2T7XwSAtk
9VX9D3FHjMG+7MkIG0PBTlnKOoh6/pDG
-----END ENCRYPTED PRIVATE KEY-----"
        set certificate "-----BEGIN CERTIFICATE-----
MIID/TCCAuWgAwIBAgIIK9zJpHzThAgwDQYJKoZIhvcNAQELBQAwga4xCzAJBgNV
BAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQHDAlTdW5ueXZhbGUx
ETAPBgNVBAoMCEZvcnRpbmV0MR4wHAYDVQQLDBVDZXJ0aWZpY2F0ZSBBdXRob3Jp
dHkxHjAcBgNVBAMMFUZvcnRpbmV0IFVudHJ1c3RlZCBDQTEjMCEGCSqGSIb3DQEJ
ARYUc3VwcG9ydEBmb3J0aW5ldC5jb20wHhcNMjQwNTIxMDQ0MDEyWhcNMzQwNTIy
MDQ0MDEyWjCBrjELMAkGA1UEBhMCVVMxEzARBgNVBAgMCkNhbGlmb3JuaWExEjAQ
BgNVBAcMCVN1bm55dmFsZTERMA8GA1UECgwIRm9ydGluZXQxHjAcBgNVBAsMFUNl
cnRpZmljYXRlIEF1dGhvcml0eTEeMBwGA1UEAwwVRm9ydGluZXQgVW50cnVzdGVk
IENBMSMwIQYJKoZIhvcNAQkBFhRzdXBwb3J0QGZvcnRpbmV0LmNvbTCCASIwDQYJ
KoZIhvcNAQEBBQADggEPADCCAQoCggEBANLMvDyWqZcD1dC/vkYlYVqaQ6PbKePg
959rn1C2fFIXYZCXxsk6UFdOxSH+fcttP43ZtMIHQFS8Tb5xbl0M4c1+tyZbUjrJ
0P3cZrQs/XivjjdthKMmssFTWK57VT/XL3anhPzAIcVZbP3sHNDDtrZ48xfsmsGs
oIV9XzhnSnR6BP5sjE5aNTTjdOENyp1Ks0jWAOYlTbFg6BsE44Y80oSZ8SshiuRV
R3cKIz/+4MB6rX9AhtToTkuhEXsczmMFJwtfGchgvt1Ri3Br9RsVq9IlXSl//Ge6
xwubqfkecPGziozReVMvQdTJS4c6KH6/S0WV8s93ZzJwLgNx5VHSGhkCAwEAAaMd
MBswDAYDVR0TBAUwAwEB/zALBgNVHQ8EBAMCAgQwDQYJKoZIhvcNAQELBQADggEB
ABwDTsLx/4PVyHRbR09esIRAunmhwNyWc2VrUrxNiJpJOiw1sUCfoEymLaJ5t5NC
/dFwMRt1GEsfbwnefTY9b+m4xqCT8JU4HADgJXVSrE7pj54IIAkIpUJ47SRaFYMh
+tcp/dQFBZiA6T6TxjgflkjAofcRoVC8O/HVUUEu+y9xn+BTHsV4ceXoIxQYphQN
wymaXFn6WTJe90hnrRCYDLMMGTA3NGkEl/OLQ+vCWUYXx/pa+QbmUkkHVji11yJn
keaUYmH2IhHjHzr3S5VyPsuga3gjvxLE5xG1AIia+0JNe3PbUNdL5OCOMgClFTU/
3t1N6zFOB/vKGW+xrz49Xt8=
-----END CERTIFICATE-----"
        set range global
        set source factory
        set last-updated 1749195409
    next
    edit "Fortinet_SSL"
        set password ENC CcXZEWi713ZSEOT2VWVZh8z6By+RWBWT+848lvMWAq1JZ/G9NEHBg5xVHu0nGfzBxnsL1QgrX3MyA6MBWUe27jYmPXeggKHhW4GIRtVd6RU7ppDAVTV36bLm0KJ+lyqZNdwERsN0MZQKllcJmjWw1NR8ojFnV73xVj4ff658KE3GOLuOEpYu1z+bf+mpCg6fwNrq9llmMjY3dkVA
        set comments "This certificate is embedded in the hardware at the factory and is unique to this unit. "
        set private-key "-----BEGIN ENCRYPTED PRIVATE KEY-----
MIIFJDBWBgkqhkiG9w0BBQ0wSTAxBgkqhkiG9w0BBQwwJAQQ4Nx4aEzYiniE2eOi
xlaPWwICCAAwDAYIKoZIhvcNAgkFADAUBggqhkiG9w0DBwQI6sEuNvPYykIEggTI
6GwvydrwkvARKfncg1xLLmRn8fK0H9j1Lbm7KqcJ8gVYzmaV43f28qpzqTURkXcx
4V4/RD2It+XzVXw2isaVO1CUCp1cH0YDkeV4hREmTXJIZnHirWIB/Ix0b4RBZ4n9
jelTDoM05OHm7oSfyCk2lwaLldN8Rwg5r68Cc8OH+CmIbiPpNPnc4nHzPhU/MUgl
t0SEAkdvYX37XdPCc8PWkUwYz1QDWSdR+rHhULmm/vu6/9oTmbJsrkEF6tObhmK3
PaP5uPFS7YZvlCALHipzgtBSe2VgSx/bzXEPrt5hYs8AK4cvv485owwPnGQcsbBv
MnigkDiWPbzfIiUAZreTLkG+LTxmV3Sziru1JN/75BNdfzE10FuRCfjBy+x5mHyR
TZBxLdbg6aGDYyeVFaI7fzSKZn5vPtxoD6fQ1MdolyIN2v3zt0iq2jODRFlhfNjk
JW1++o/agJyx7AMI3SbfOzEt9sYjt5/bywLK3Te1nzD3VuHRjJKeKvu+Q8SJTKtP
D10zb+BKtJg64SbGZrLFua5Qdwq8fm1EPeiodOVVwlHcEVb3Wdy5WnFbbUceaUNS
c1qSnRWRAtcAHBPVy43TLjAAJtMCJmZKfllkxjMpSdwREPiO4DWa/N+91J9CGmF6
JxlN0BSp7MezwYjCDMldRIbCo6jm8SPxlxuzr4Gz6Ya+xZE1zYojnCdELpzr51nF
hO9lwNEHGOyQIw0oDNEVixXt2L2FTprhmdepOYxJp3LzrAZGWvxKagd4stKgKgCq
RG3sdXy/XlF7ETKV8almE6sWzQfNMRnDV6yWzA6sNzqnbc8V9F5cVM4zNainvMH+
q8kM1JyoljYTdLEkfsCyfXGMyYIjIW3b0nS1tkoqFdlsfJ/eOjMrOHJXZRGlGivV
dO13LGthNj0gT971G9Tmd5X7J9Ridz4i5ZfLRYRQcbwZDQckyBX+OdZeFTLE+Wwf
I/D9lSchAt67F0Uv1cnT6NfRniraNPmC/D46EV/mKhelLA01c3Ff2JZLFny9rSTr
O2+UomZz4/5z3RMpl9EVCobzhq09l0FURoKj12LfMIS7aqELPQm0xdrQXMf3BNkC
GShhYMGiMZnNL3TSJlTv5acu4/h9+x6eO7GSn7gn1sGXpKlf9npk93Lnx7J5mhBc
0uin8YS3RgNF26lzXQ8I73mSshqjC5v2eAaFC55DO7glgwIwUkpNBjPa0Oa/RqH4
xpO6Us84MpTG7nF52wxSPLcuVwFaALzriIzu0kPEdKzo/1jDnx4uJFpiuXddp2lu
WBIYW9MZvPfp3QTkX8EdIEZlNCWIhZgipfcQ86dhH1S/qi0gyYIqGr3YkCVVL9Bw
a8WCJUpTw3gMs0SJBXc3qBfbBASvd7WwvkGz+qWsmEsEmCiwgfCnfpVFSoE+yaBW
pjjkiFwgTPPp8mwUGkwu/Li0UnRVlk3o+JlRKMiI+09C5gwNWXqCkbfRGiQmy5Oq
cMK6d4A2MQfi+1ZAYgs6AmAslfgT0nhMNktpJzhFcuK6R98mK/nEGQ2uRx2zqgVF
HDOibDs3hgGmKqtSitd/JVdSvMHHokdG3FlAdt+4yzom3/eOXVw6IS0pQHaM2MlR
ynBuUo6t1TpDm1FcbRUBacwxcp4Cc8Xy
-----END ENCRYPTED PRIVATE KEY-----"
        set certificate "-----BEGIN CERTIFICATE-----
MIID4DCCAsigAwIBAgIIJ2j0qeGINkgwDQYJKoZIhvcNAQELBQAwgZ0xCzAJBgNV
BAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQHDAlTdW5ueXZhbGUx
ETAPBgNVBAoMCEZvcnRpbmV0MRIwEAYDVQQLDAlGb3J0aUdhdGUxGTAXBgNVBAMM
EEZHNkgxRVRCMjA5MDcwMzExIzAhBgkqhkiG9w0BCQEWFHN1cHBvcnRAZm9ydGlu
ZXQuY29tMB4XDTI0MDUyMTA0NDAxMloXDTI2MDgyNDA0NDAxMlowgZ0xCzAJBgNV
BAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQHDAlTdW5ueXZhbGUx
ETAPBgNVBAoMCEZvcnRpbmV0MRIwEAYDVQQLDAlGb3J0aUdhdGUxGTAXBgNVBAMM
EEZHNkgxRVRCMjA5MDcwMzExIzAhBgkqhkiG9w0BCQEWFHN1cHBvcnRAZm9ydGlu
ZXQuY29tMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAyUUeVJcbOrN8
n6WaGPW1zvfKzrfb+NZ+WORQgH8UF6r7cAGv1+MGgLThm/i49EgTWgXs1ZTVcdLA
iGSg37dQPEHHAprUYekuKKIbwo30B3yRBOfGFnEwd+BsEdZidXzgYVV/ULR1SnDb
O9iTfFqzCfyEdiadkNZbE/mGNBYJr1arxCOskLurrRxlcPNJWuLqitxfxhG1ZB66
MJDCQxSi5hpaCW9XrlvZ/zayiWL8CPTRbkJdf43x78V5cUngXu0rHuIWJZWh6f+9
ek7bcmpMD65kano/Z+8OQUVZcSMe74a2ElEprvFnBR23aWEw44AISxJC2olfZmXb
YCeEFGir8QIDAQABoyIwIDAJBgNVHRMEAjAAMBMGA1UdJQQMMAoGCCsGAQUFBwMB
MA0GCSqGSIb3DQEBCwUAA4IBAQCIEIcf6zxFIiOVp0iY+h5EhXIr8yaJy9BeC0hz
1ES8qyvCSuB+KBZLEz+4Ry8zG9OJbSf9ZQ8M/n1VhD/gEYMmh2Bms6Ehv/H4paLL
bN+UYuGJLAPv2U74xmt208PuXptguDdsl0tFLP5aO5L7mU7CLHid8m3yYXVPRjfL
IfzzDtIGBRTkdgclwBPEM1AX0ryhWIEXP7dAYpfJZYjFVxOwpK8djpL+SRDvuRiw
Ewm5FMYx1mNT2SsyT09aCHgx+EIY64BK5w94yL0bJUf8gDtP2bedmO2b3RC+v1PC
mNO3WakE/7hH0YlaLvGIAR3EJticCl13eEz1hGtR4n+6MGMB
-----END CERTIFICATE-----"
        set range global
        set source factory
        set last-updated 1749195409
    next
    edit "Fortinet_GUI_Server"
        set password ENC FpS6IzBrVQ6Nr2zWy6C0LM8wzv+NWAumPmRLF0jilpoKMqFrMQp1LMjkgxcoBvRQMJ8eiJmuexqYfddLk/rcJGEnhTLKQOaX7NdIWIglfpQ6uNtHs3WzKEJqcbvfzRuVU5/ljV4EFCApFjYTwWCFnzvWc79FUnFZVdq3uFlTQJWmFqy6uwo+cSYHFZtA1WZKRCTgaFlmMjY3dkVA
        set comments "This is the default CA certificate the SSL Inspection will use when generating new server certificates."
        set private-key "-----BEGIN ENCRYPTED PRIVATE KEY-----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-----END ENCRYPTED PRIVATE KEY-----"
        set certificate "-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----"
        set range global
        set source factory
        set last-updated 1749195409
    next
    edit "Fortinet_SSL_RSA1024"
        set password ENC kbADC21PbOzHdIUq5Pg7qitdBqaf0A+QreOzlGbm0nCCSTklEkfovTti/evkJZteio4cM4FVo4ZUERP/UOuGDF2vQHwQOS5ynK/DPFRFX9nkGQbVQ4T9SNQI4PXYBrSi+TT9I7Fpb+6uRQZnMdzSsInlhuf/qctLDDu7l3JGifn5abl818rM2wEippuOnN2hGaHXf1lmMjY3dkVA
        set comments "This certificate is embedded in the hardware at the factory and is unique to this unit. "
        set private-key "-----BEGIN ENCRYPTED PRIVATE KEY-----
MIIC3DBWBgkqhkiG9w0BBQ0wSTAxBgkqhkiG9w0BBQwwJAQQCeNkHUHEPTHq0JTb
nvD9GQICCAAwDAYIKoZIhvcNAgkFADAUBggqhkiG9w0DBwQIYqErglYsG24EggKA
WNTpcyAoCk8G8o2g2/33zzJrGpyfe7l5x6OalgP4GPTXmnHCrOnu06NNu0/0dnBs
somPD2bwuRmsnEhh3uQ3WakuWFPYXTAAEyyy2tRvRMQsVI5pIC8gnCALZpaGaW8E
WJyCX79apySascmXfL/LEP4a22UVxtP3/lWSsAzubiLn3bdT6BDvZ5OKF0UAj+O3
QX4Kfmh53ZTSlGSq2tQFBZq3h7jAjHobKsBR3TfDchJLlNDEX+HsZEO58tz8us4o
gHMlj1Z+Hys0Z4g5pnvxhOgJymR4PSvgqSu0W2CpzPALKSdAYsbkHm7F3QRdNcVu
oaK6FA5NRtQcccKtuwAQks+RSfIW8Y1Mn4Mb9Nbdo3tabsa4Fm3nIOYzfazDo3gX
ggj57kKbs9cY+nCCYaHGFduwm1CF5X6m7iHcuJreitevC5eeUxcxZ/RNkQNn6Fg0
pqhw2TjDF1fay6sO19AEpVQO51et1tyv2GWkBvq+l0UV+27KELvcuHGUBgNg4xNb
/OyeMY0fDmXoUfc/yBoINBGHWHEnlUFq8pFm50Bre/waF9DzwEp/NlX11fiDcnVz
y76IG+k4o90uPenxlFBm35bi9Vuhqte6l0oYHkcGnoxCjhhYjrBSgVVvVwO/5C8z
u6azA43cspPcao8mCnRXD9HjGYNCZiW170ZcmG/VxnwFGOLW9SG2tDcC+6u4NrW+
7lHZ0BXkBovMmmeX99RFC7E3E0DndnYCktiyQN8qNzHWpqKsflu3niyhIKjsnrxQ
qRyQykBaLdV6M3e017SF6MLMblNbKPLbF8yg6UFN7Slh9Z8p03LfY8TYmrqDnMlR
oqa5ISXcVOD1vIE7tK9VFg==
-----END ENCRYPTED PRIVATE KEY-----"
        set certificate "-----BEGIN CERTIFICATE-----
MIIC2zCCAkSgAwIBAgIIG9+YJNELnf0wDQYJKoZIhvcNAQELBQAwgZ0xCzAJBgNV
BAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQHDAlTdW5ueXZhbGUx
ETAPBgNVBAoMCEZvcnRpbmV0MRIwEAYDVQQLDAlGb3J0aUdhdGUxGTAXBgNVBAMM
EEZHNkgxRVRCMjA5MDcwMzExIzAhBgkqhkiG9w0BCQEWFHN1cHBvcnRAZm9ydGlu
ZXQuY29tMB4XDTI0MDUyMTA0NDAxMloXDTI2MDgyNDA0NDAxMlowgZ0xCzAJBgNV
BAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQHDAlTdW5ueXZhbGUx
ETAPBgNVBAoMCEZvcnRpbmV0MRIwEAYDVQQLDAlGb3J0aUdhdGUxGTAXBgNVBAMM
EEZHNkgxRVRCMjA5MDcwMzExIzAhBgkqhkiG9w0BCQEWFHN1cHBvcnRAZm9ydGlu
ZXQuY29tMIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDVOk7fl4FmL1bDj80r
8qrNG0hSE4knbsJYfvpBixs+4xYUhHiqceWTqicQJsgxXUftW4SgUVQsq/APNt3/
0bK/VW870TAuiVV33UI91Y5iwlolD8nTRiKA7FpcvJoIgQNwxYrRvNumibjl6kmn
h6+wzv4B+gjgf/4ftS2vpiNbUQIDAQABoyIwIDAJBgNVHRMEAjAAMBMGA1UdJQQM
MAoGCCsGAQUFBwMBMA0GCSqGSIb3DQEBCwUAA4GBAFpu+nq0PUvBRcwijZfo4ewh
Rz2By2Zw0Etvowys4kRiSd744eFqrD8hPPFuTLYE8RYsVlW8PibO5cfzucJ8r0dO
05HshlG3u6JMcSvADleM+LcX5KLAArHXUEmcQXkpKOCCd5J8EhHeWEMuWJLzKvjb
dS1O+JWX1FHJAg/ij+Sr
-----END CERTIFICATE-----"
        set range global
        set source factory
        set last-updated 1749195409
    next
    edit "Fortinet_SSL_RSA2048"
        set password ENC 3hYeKyAEWHZJM+3G6Y8PQ2tNtkRs7Aid71j2BHaQUk51oaPTtRnUCgXeIdqNMcd5vQcikkPBxCDd5yDJ9cIp2oQw1z9+0GMCcxJZeJW5V3IuxgWEOL8d0v8yHpulSxAutDoXPwqIEmZ/I9OsGsaRk/TYD2H4tQN7cpjOjVWdX5yUa0yFGTVGZR32Su2i8EHIIH7g6FlmMjY3dkVA
        set comments "This certificate is embedded in the hardware at the factory and is unique to this unit. "
        set private-key "-----BEGIN ENCRYPTED PRIVATE KEY-----
MIIFJDBWBgkqhkiG9w0BBQ0wSTAxBgkqhkiG9w0BBQwwJAQQQVCO9vG8S0BcNp0F
1eTowgICCAAwDAYIKoZIhvcNAgkFADAUBggqhkiG9w0DBwQI6OOWUL4RxfwEggTI
KU8If3RvykzsxtbRrGh9pScqPOlQIF8TXLqBaCqlidRYdMTpge31BMzgJzMoMr/C
X10X+zhQ/iVYiXksfq4AxEJGfff3M0z85jWy2fgEQCUIv82VDXrdXGVJq+W56kUn
zvqjWYtLNAEL2V4du7m4+bud6qDwY4zL2weohu1RE6WiZu+epp29LW8X8SQlVssp
fDtbZAES5iQYHJXk+8ihKAXCGYTufj9QmXnlmdwHR07ssMSuY0pqrMZUrf4foGTj
2OakIof2wldTK8vgcfQ3fxKTO584U3GFpARc51Z5euBZeP9v08qKeN4FPDL18u+D
luWGtukx8FDj7jVtpGsOwtVMPQcKgU6jCLub5dwnTprybpsGvBS1Ys29qmyWvYbL
oRj7u93dQF/nt9SwTuMGeMDYsLL2Qj8hEhaPMzAB+cWtxd0hK+vb8+sZpvatIZqV
XtS34paLOmZkeP7WZdaXlYBmokm/S3V2lUQUt97p39Jeghy1yc7UHluI8d0FfA5A
yiiulD8hbZn0X4GarnM67XuwwG3uHM7X8KOJt1K1qMlNyDb5sCUgdPl2MBRMaa7P
zfqUKhA4cSJAB4Ojz28ZmYDw52737k7Qbyv00ZTScG9UpaMyYMDe08+8Ify9suDD
YoThHqMqpKwUWCc7Om+JH/Hq6RyL5yGUF/P9XWTT8ypZNChMGXWdMKHI7M8JmTmw
s4XR3qad1G7HLg8veUNl/w7xpU51PXw8QhBIAa82bQAbdR79cWJntfVoNEksG4XK
Jncm5QbvWJDb++Ot+IP4G+EsFb8EIqVukG3XmjeRzlnpJN0STDjePTAuRATtLVPt
Q1Dv9OD/BX1UTBVxB5Iz3HD+WB9M8hiSJJqz5J5ZrV0F0h5tesqmLT9TdK3OcodB
EXiJ3lncRrlV4M5cD6mcxuwrDeyIRMSY1iJFX3bOpNvIyG3mI/CGyRWOMK8bfxj2
uwKSf6/y69kR2p8ke1N4/l8u9Te42369ZZSDbVaRtqZ26o0T0xMFCPgqjiCoQmOU
ZUoAasG2IHipdzMvn8D8g299+9lQv8Ke2pgRhdkQ2+frpKEbz5mO8I1YeBYs7hx4
H/6bniBI+M772pEJTO7O7wFSfaJtlHmRO64DBO3PrlMMrIoQ9iwkNirkEv5PR87b
9v+HxWVliWgWH9QcKczEz5HONlGPRzYRpKzmJB16GA23InWFoZXSf0MQXkkKWs82
MIn42Pbp5TnvnWB92LDh7Ts5pEn/zCownrDZwAXlJB0/CmVKDZ/xzxTrWTOpOwY3
ieBuVKElfr/b3YmZPFIHYgLW14gL3w2HS19hTVVtMBWT+Z6kgTjzSCd5734QjaS+
tt9tLgRI8fOLA85ZRNc4b3v3ECbrdaIjDXbWkMHG4YxhpK2Yq6v5rCtcgcSbUEIl
GspI5dhOmPOEoZeB4ec9rL8xbuuw6AwQ/CjxIFzloPRzgWyNXSmDMoWXeymd/AcG
8zm0WTEFjBuNlEaZZx1KLsYd1limZfSa4zYM7OBrqNuycmSJfFjhRvA69ZdRgZVN
rzwAhvjfWHe4bLXK6pnSx8O+iozrN0xGAz6dGM7xDBM4fYVFBk8yEAPtsEw2xqAZ
eAdFEmHutoBh4OwCr9mFRS+vGGbFSbna
-----END ENCRYPTED PRIVATE KEY-----"
        set certificate "-----BEGIN CERTIFICATE-----
MIID4DCCAsigAwIBAgIIXGReWFP29+4wDQYJKoZIhvcNAQELBQAwgZ0xCzAJBgNV
BAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQHDAlTdW5ueXZhbGUx
ETAPBgNVBAoMCEZvcnRpbmV0MRIwEAYDVQQLDAlGb3J0aUdhdGUxGTAXBgNVBAMM
EEZHNkgxRVRCMjA5MDcwMzExIzAhBgkqhkiG9w0BCQEWFHN1cHBvcnRAZm9ydGlu
ZXQuY29tMB4XDTI0MDUyMTA0NDAxMloXDTI2MDgyNDA0NDAxMlowgZ0xCzAJBgNV
BAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQHDAlTdW5ueXZhbGUx
ETAPBgNVBAoMCEZvcnRpbmV0MRIwEAYDVQQLDAlGb3J0aUdhdGUxGTAXBgNVBAMM
EEZHNkgxRVRCMjA5MDcwMzExIzAhBgkqhkiG9w0BCQEWFHN1cHBvcnRAZm9ydGlu
ZXQuY29tMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA9y+Couw+0Jb4
btN/Amlv0tus62N9rPqng3eI4DA8sOzRER+qrzK6hoCMcJUMO70H/wOf5pfcrcrd
hds+d97lofYhitOnn7C+BWAmb/JpI1CVD3hJvNvR3Oi0SD4VGPi0lTh65IcxxJoo
Osb+3u8rVzyVuWEzSBIQ0EoQA2+nn0mTRkQgCvIe6OkPkNAyo3EZeR9Cw2HOVNgm
6uliL4za65Iy5cTOq2vwoUe6gWOPCLcizUDqeRCWrly8O3HrE0NNWAIUqNq3l7Es
/hsD7A7AsB3i/ztH3X2VvOSDta51iiFlHsdNQ7pkju2kt0If6/lkADPIFmOGsPUl
7DTQWnQVYQIDAQABoyIwIDAJBgNVHRMEAjAAMBMGA1UdJQQMMAoGCCsGAQUFBwMB
MA0GCSqGSIb3DQEBCwUAA4IBAQBCc3bfYfr1lSM3mEyZMY74KlvdgukUO32tCvTo
6WnJdgHIL3xAZhaT3pSQQWQCZGxOEHh7oqrLh0ST1bnSaz9XeJchUCU51cVuacRj
WydgYZJN5hZRRlU+qcsudALbmz+bFLzsqXBsMhQmPqxauVp5T2JqOifQ87fmaBbs
cA3SXR69AW50g4+UoqL+BIM+Q7IymQXVTTcvmJ1E3A2sGidQuzuKGlqnXS8rP5mN
kBXeHmg9xyXtX8hbiZY7uuRSZOPCQOlbssDAtaUSwgGpoLkIBNxrH/hl+gdZcl1e
Ms466Eh8SQpkjp+54eBZudfbrT9RkR80/TwCu2MH4XU85ZEZ
-----END CERTIFICATE-----"
        set range global
        set source factory
        set last-updated 1749195409
    next
    edit "Fortinet_SSL_RSA4096"
        set password ENC LVHWpmqh6EtPeF8eWjC5Oelcoo71COGohE4l8vUXVzloe+nk23P8NOJDiG+zIGNYcTiXrut/ckJ5QC/u0HpAyKsW0GUCcrYVSHVN4+Cfnat11c/bQV4MLNEHfsZuByADWw9giV/9DnufF7i+G9iQJroQE7rmfJi8EhIgzf+9JxmYY0ne+zu1hfTce9STHx7op6zepllmMjY3dkVA
        set comments "This certificate is embedded in the hardware at the factory and is unique to this unit. "
        set private-key "-----BEGIN ENCRYPTED PRIVATE KEY-----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-----END ENCRYPTED PRIVATE KEY-----"
        set certificate "-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----"
        set range global
        set source factory
        set last-updated 1749195409
    next
    edit "Fortinet_SSL_DSA1024"
        set password ENC NDKY+LlV/afbqk0cfUWDHiNX6N2kWKYOevF1XF3wV7l8xDdAHQHoxYYsrb6gWz3xVUK9/nrspzc5+l11B9FiCbMVI7Sk1LTbeVS1q8NRJ7C2bvfSootwA9eF9jjFxcgZuW3hhFSq7+bB/bAD2nf04iff6GJtyDNe1j6Ivrgo/Oq6PFxuO2SYFAFuqdM+hsTHkL6NQVlmMjY3dkVA
        set comments "This certificate is embedded in the hardware at the factory and is unique to this unit. "
        set private-key "-----BEGIN ENCRYPTED PRIVATE KEY-----
MIIBrDBWBgkqhkiG9w0BBQ0wSTAxBgkqhkiG9w0BBQwwJAQQYQ/eN90VRp9j2J2J
UR4vLgICCAAwDAYIKoZIhvcNAgkFADAUBggqhkiG9w0DBwQIdWO0EPGCUOoEggFQ
HJIMErkVNpKimICiD+97bpV8rwJVcwZRCH2j6XyP79KUbvhktRL2Lx39/6sZPMFm
V5cVW44WrTaWn51WLTezwUTTCi509qOPjii8UnxTrfNE4jl30svF/SrhV4ZcCRtB
ENAI1DQE/KizWONOAy+UKFhe7hPUr6KHGJSTjmtFug++RxUyzjWLFuReKMLRbSCO
Yw+vyeokE7ygdBTMWDftwySRXE6G/+C/3VBPLLoEBSXabLt1Z51DEVgk3be8c1nc
7+0yXRERUecVZ0fZznFA/Sq4SX0M4NlQjQfmUDpjzb/o4sg79nrrZiZtcCE8IvmL
T6fNViDZihq0ljGE52vcOxeXItAdRROyAPSa6racA+RxarPCpB8hI2KMIhoP49hS
SQKF//s4VAlx+/tSHqXqRATlRiR11Yu1znnjw8SEEtA2Bsrl2IR3JY/bmdKuQzxU
-----END ENCRYPTED PRIVATE KEY-----"
        set certificate "-----BEGIN CERTIFICATE-----
MIIDnDCCA1qgAwIBAgIIKUI20uSePn4wCwYJYIZIAWUDBAMCMIGdMQswCQYDVQQG
EwJVUzETMBEGA1UECAwKQ2FsaWZvcm5pYTESMBAGA1UEBwwJU3Vubnl2YWxlMREw
DwYDVQQKDAhGb3J0aW5ldDESMBAGA1UECwwJRm9ydGlHYXRlMRkwFwYDVQQDDBBG
RzZIMUVUQjIwOTA3MDMxMSMwIQYJKoZIhvcNAQkBFhRzdXBwb3J0QGZvcnRpbmV0
LmNvbTAeFw0yNDA1MjEwNDQwMTNaFw0yNjA4MjQwNDQwMTNaMIGdMQswCQYDVQQG
EwJVUzETMBEGA1UECAwKQ2FsaWZvcm5pYTESMBAGA1UEBwwJU3Vubnl2YWxlMREw
DwYDVQQKDAhGb3J0aW5ldDESMBAGA1UECwwJRm9ydGlHYXRlMRkwFwYDVQQDDBBG
RzZIMUVUQjIwOTA3MDMxMSMwIQYJKoZIhvcNAQkBFhRzdXBwb3J0QGZvcnRpbmV0
LmNvbTCCAbYwggErBgcqhkjOOAQBMIIBHgKBgQDMBLy2soSMUpNrSY8YpX0V51ac
tWvHfgyA8yaF3WVGjX5VwH5CKpJErwWxXfLebTCutQeQQN8G+LwkAX11J8jJN9m3
FQOEPDaPpLEktCRhIsmDuf5uTWgXRDZTKB+N2/+B4ooZMqit9ZHQKZo3tOaXQuj1
hFSkrRQqJiwliUiDpQIVAKhIkQkEsak3XcmIGEuu/blKmf05AoGAelbK0kFzAWO1
JwrZsGIN8DvOf7KoDf6wh2p7hQERbVx1+/SDmQ3S8oGOp/smW8+eYCysHa41/vJX
EoD82+NdYaqSY24YC3TZQx11Wug2+BnXHyXSo5ljLnZe/Xi5DcIufN+vFzXjKqim
lmSrRg77EuBULKImDHRspk8sADoXIyMDgYQAAoGAWqwPuB7gyiIOQRarQUQcJcBK
yzgxlr+15iuPox2/K18DQ8HM7roKo13fOiPuaqxm09tyDJWYs5egasa8uZa44+1X
VhsLMENkts1/68Fn5LcUgISi9mWi3uBPH6oZNlfDGvKEQzTwjzy5Xive5Z38I60O
Kz0uaw/OFH580OWkrv2jIjAgMAkGA1UdEwQCMAAwEwYDVR0lBAwwCgYIKwYBBQUH
AwEwCwYJYIZIAWUDBAMCAy8AMCwCFEqq2Kn0hRgiNH7i4O6hPZSYgJSFAhRf672D
oHVUIMFlAVrmQidcfpUDNw==
-----END CERTIFICATE-----"
        set range global
        set source factory
        set last-updated 1749195409
    next
    edit "Fortinet_SSL_DSA2048"
        set password ENC Kwwd6vPfpztlcTOMCB9kWODjf05FHgGbRr88TQK3hJj39zKaEm7ifoQhaCuZA9cgoEFWXBzUT1e4STiBduIIk2xjKcOYP1tY7r1sk7IhbEv5J3RRaFtW9Y7i2WwbhQ3+aYF930etsr5H7F1HE+x9CDtU5FiIMWWhywx4q73lNQQmKJTqAM6PPpqdRLEIRviYoYy1e1lmMjY3dkVA
        set comments "This certificate is embedded in the hardware at the factory and is unique to this unit. "
        set private-key "-----BEGIN ENCRYPTED PRIVATE KEY-----
MIICzDBWBgkqhkiG9w0BBQ0wSTAxBgkqhkiG9w0BBQwwJAQQtAr1bTwvV3l2XwHF
g6vOGwICCAAwDAYIKoZIhvcNAgkFADAUBggqhkiG9w0DBwQI6/fexLUwrkYEggJw
6BhXsERykKRVJb0Isp6DRDr3y1HYhHWBRhkKxThqpLUXwXsgTfs2LYoJiNCh0Hoa
C8bz0xfSxZBwIoexy4SeyoL7b9PF4ghhebm85dolCfR3HzOjOBQIFYPsLXnrPnDr
h3X3zpx8CbRziYnqaYKcSIaJDoZ/0Ly8o0PaiOKYXGlXA7iU1/lvE9hYCKezpR60
dF5OqFJ5Bk7fP/8H1lCz6vRnamqbe5g209r62JgdQPxkHCClOB829Qz1ruRS7WcN
h6ktwLg6AWrypXFjFiLhyZT2h5RiisEkMdVCk1mi6rbH3tAzGsERG87PFlfEyaFE
kqacAyxrnUfiAyyPxvzVOZJzZr4XHTO57yv4aB8t/bAygdxI8qknaHBclrr7zsHU
zkhqWEXC80IULoboJ/cmk6JYI3CXAwROL1fh+VFviMRHZhk09AUmv4QnxjYkud20
IG2mLSsTFuNkC21ajZgbH0tINsvdaLzd8LPub8QBQgEHMfQs+V75p5+7NQ6sNPJL
fLWlvunxkMhMKlbqwGTAip3+1O8Nbqo9gPMSlI5Q4JiuRr0MEjn/OHUp2+eP2A6U
9W6RREpB3PIYiwKUxcFrGhuj9IiCqLyVP15jkS9uXIdtqwWM2HnPyjm494EpnGRA
A+g+skHf48ZxiZQZcwCheHiAVE8oVglDpR7UI3QbYcaqWoGyL80tJvz4H7EoeCpj
Sd9OMKsrHGOehzuI/rwwgrrou/uUB4rrkaXc/r3Xlp0OKaIV4nzJuj0j/Zygyg7R
qbB745IolA00jkA5S1/KVzvtsjTvy0eRNcU1cBPVksUeEUC86qaEdXUe5kbjcW3M
-----END ENCRYPTED PRIVATE KEY-----"
        set certificate "-----BEGIN CERTIFICATE-----
MIIFRzCCBOygAwIBAgIIG2+7Sn34TvQwCwYJYIZIAWUDBAMCMIGdMQswCQYDVQQG
EwJVUzETMBEGA1UECAwKQ2FsaWZvcm5pYTESMBAGA1UEBwwJU3Vubnl2YWxlMREw
DwYDVQQKDAhGb3J0aW5ldDESMBAGA1UECwwJRm9ydGlHYXRlMRkwFwYDVQQDDBBG
RzZIMUVUQjIwOTA3MDMxMSMwIQYJKoZIhvcNAQkBFhRzdXBwb3J0QGZvcnRpbmV0
LmNvbTAeFw0yNDA1MjEwNDQwMTNaFw0yNjA4MjQwNDQwMTNaMIGdMQswCQYDVQQG
EwJVUzETMBEGA1UECAwKQ2FsaWZvcm5pYTESMBAGA1UEBwwJU3Vubnl2YWxlMREw
DwYDVQQKDAhGb3J0aW5ldDESMBAGA1UECwwJRm9ydGlHYXRlMRkwFwYDVQQDDBBG
RzZIMUVUQjIwOTA3MDMxMSMwIQYJKoZIhvcNAQkBFhRzdXBwb3J0QGZvcnRpbmV0
LmNvbTCCA0gwggI6BgcqhkjOOAQBMIICLQKCAQEAquhm7zTVHIAp4peEWrJmTvJz
VjQIHTe6nZlWbQkXafx50wO8Q5dbhMcLqZ5kzLBRjfzyNo08ooLMpm7tYntb7FZr
viAYxczS1vO1x68xAYf8hdGpXort056jVdSLpSVUFT5NbhZaLo8THRdD3e1gFZ4O
dOSDmMXib2LUQMLatICaxumh2M/r57JETwgb8Ta5DQi+APtwVVxGe0RjeTMdd7nH
0tVn93eikBFo2MA17tBBwb1I9t0UfOIinUvb+3S6aR8pctf3U3A+ZgTlo7YN0iA6
CEmS/9m2+7MKyuFMB/J00JbIy/uZhPdZr+UxFIaVngt154MBjZm7LlvHAqHr8QIh
AJHHxA28TaNRtR001dpDi4C8eDPPl6DiYbMjEy3g+w5lAoIBAQCQ1JL5/UaJNsWk
7DeNHgxULpSNAX7j0ZVB4Uk8pq4LwRIWf39G4Jtlzuc5DNbbAzp9/WJeoN4gf0/f
qPHXOr5pIK7hTXijtQbsNlaN2AAM9voGq8qOBroP3HDmAuiTMXRZ8a5Z2Na9sFY1
vnzXf2GxVa6dMJn+MFetwPjycHUtolO4+L/0ZZwdcx0VL8VLiGRUnJD3GNE+vxgS
V81LCTrXLLAW+gjUCfYMNTCxYVj7jNDIoyELxV8Su2IWf//cD+sDFWuw73/KXqUl
SrcSK4E8Xjgsncsi+MWWhPn1D1j+ZTGXya14JKapDFxyHl1CJsSKETK+bcmXEqhc
1+HtYlWnA4IBBgACggEBAJoi0jcrJbAMltgi7AYTCfTv5jcuX1+9GcmNAfKN62/P
odW92h+W4EmouqDgw7FzlRTK1/UpCorPj6zY1ZlBEVpHSs4211Akjfeft2PGy8Vi
/h1qfEq4Mw/GjHJpmx10QGvt7BjiBwePgszCUhL9hCvcsN+Yryka7astO1Trux17
S1tENNe5Hvs2NFea17haYCi7t4e01LRG55LfdpO76kasi1arGYKjmbqRHQpMzX82
dd/qtYieBZVoHhhlpEG8y1OQXBXou9xjlMirNjCtPYfaUMCTMyOdDluoFiAqLJbZ
3zTHLFb+xHiz8ewT1war4rQxtQCrwVVGIkNR7ttjDg6jIjAgMAkGA1UdEwQCMAAw
EwYDVR0lBAwwCgYIKwYBBQUHAwEwCwYJYIZIAWUDBAMCA0gAMEUCIQCKHQYQ3UKz
eW2EKaEXDNvXtMvvt/8anYadKtqoF+Pk6wIgOSYyOnEY/hwDlDqXfoRA/Kq3S8Lt
27m1lgshS2602u4=
-----END CERTIFICATE-----"
        set range global
        set source factory
        set last-updated 1749195409
    next
    edit "Fortinet_SSL_ECDSA256"
        set password ENC SVejRZKRzF5w0wrMO7OepmpE3bnuLGzR+O57OpfQ7YqPFifR9S346yyHYVXJpaAuE93O1pap65CBnxWlHVzEuzA7pzsMEduw9uCB4z1Z5iPuF2HFGk2yaK2+TtcBWRPopc1dPdAcwykY8gLe1SWGWv/pPmcWrpVwonL3+mnc+nsrtjkXjOYQnpAT4kqUw9NoRlWXM1lmMjY3dkVA
        set comments "This certificate is embedded in the hardware at the factory and is unique to this unit. "
        set private-key "-----BEGIN ENCRYPTED PRIVATE KEY-----
MIHrMFYGCSqGSIb3DQEFDTBJMDEGCSqGSIb3DQEFDDAkBBAgHfKG5T2FUauwgA2L
f3OjAgIIADAMBggqhkiG9w0CCQUAMBQGCCqGSIb3DQMHBAib3wZCegTn8wSBkKZd
69p28VGJc48+6x4QVFMjWFFK2Rr4MBE2ydzmTBsQsv7u3j1y9zyVLfzWIz8NL0St
U+Dfzl0F4t1VT86QJDUagx85N6QzNdGHO6H8uENNzeMRH2s11efEbqo6j5dQnmFd
fdjp3EQB63vfr3euMOaOppzBYGSjie474JPRYerVcJ3gGxftpwJZTuEn8CmAew==
-----END ENCRYPTED PRIVATE KEY-----"
        set certificate "-----BEGIN CERTIFICATE-----
MIICUzCCAfqgAwIBAgIIMKMDrTVieZowCgYIKoZIzj0EAwIwgZ0xCzAJBgNVBAYT
AlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQHDAlTdW5ueXZhbGUxETAP
BgNVBAoMCEZvcnRpbmV0MRIwEAYDVQQLDAlGb3J0aUdhdGUxGTAXBgNVBAMMEEZH
NkgxRVRCMjA5MDcwMzExIzAhBgkqhkiG9w0BCQEWFHN1cHBvcnRAZm9ydGluZXQu
Y29tMB4XDTI0MDUyMTA0NDAxM1oXDTI2MDgyNDA0NDAxM1owgZ0xCzAJBgNVBAYT
AlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQHDAlTdW5ueXZhbGUxETAP
BgNVBAoMCEZvcnRpbmV0MRIwEAYDVQQLDAlGb3J0aUdhdGUxGTAXBgNVBAMMEEZH
NkgxRVRCMjA5MDcwMzExIzAhBgkqhkiG9w0BCQEWFHN1cHBvcnRAZm9ydGluZXQu
Y29tMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEwff/UIPgcE3fR6LXSFXhzVGi
8lRk1dUgz/unyiNQdoBaXhgiszN8oFLeHHjE1RXsHdIlA9Jp/Ghfow+JAUYYaKMi
MCAwCQYDVR0TBAIwADATBgNVHSUEDDAKBggrBgEFBQcDATAKBggqhkjOPQQDAgNH
ADBEAiBAaWBGExXi1spubkCHvIFn7sNGcQW9GSgg69RUMHS7hgIgKoEvGWV87psV
lxl8C9rycQsHHgWPuG3gGLr5qrEq55k=
-----END CERTIFICATE-----"
        set range global
        set source factory
        set last-updated 1749195409
    next
    edit "Fortinet_SSL_ECDSA384"
        set password ENC jupGuO5xelm7NGS2LO5rRz9THUrQiUc8R4tRbW7s0XbPKiFAudC7lZuub9zNSvIKucZs8PFRjVjFKyD1rjCMkGMfm9XGQgoRfIqOfnjpY57ocZCgfTNoL1Oe4svz8B5iUysrIHI/4DsUbxtMMfwmiPmFKKJGFxTdCYi52DhNHRQFGZmU/FM4Yg5Y5QPkMUpb17LKh1lmMjY3dkVA
        set comments "This certificate is embedded in the hardware at the factory and is unique to this unit. "
        set private-key "-----BEGIN ENCRYPTED PRIVATE KEY-----
MIIBGzBWBgkqhkiG9w0BBQ0wSTAxBgkqhkiG9w0BBQwwJAQQnWsW7Q98Vu6SpJKh
kN/6kAICCAAwDAYIKoZIhvcNAgkFADAUBggqhkiG9w0DBwQIeYAzFRR/hagEgcAI
JJ4N+R2bv4cRQ6tNg9cbuM9umaQNGBoSHn4mj0kmNkOWpbpVzzugae93gshp3lia
9jIUdisqKV2CQz0adK5gsfeJ73G9II+mybaZY2Z8cxv3twuAVhvUVvnMQ03hiYcs
/jk+EMFEG8KXsUvgKHNFvkcydxznNiONW2rdyDrurLPSSCCyPCcq96RowBaHh3+Q
t/rfEsqo/9dObFWr4w4TPWjL6lDNlmhpkg8tDQivAzX8MTcu06olsntg4C3DL+A=
-----END ENCRYPTED PRIVATE KEY-----"
        set certificate "-----BEGIN CERTIFICATE-----
MIICkTCCAhegAwIBAgIIMZi/USVTlXwwCgYIKoZIzj0EAwIwgZ0xCzAJBgNVBAYT
AlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQHDAlTdW5ueXZhbGUxETAP
BgNVBAoMCEZvcnRpbmV0MRIwEAYDVQQLDAlGb3J0aUdhdGUxGTAXBgNVBAMMEEZH
NkgxRVRCMjA5MDcwMzExIzAhBgkqhkiG9w0BCQEWFHN1cHBvcnRAZm9ydGluZXQu
Y29tMB4XDTI0MDUyMTA0NDAxM1oXDTI2MDgyNDA0NDAxM1owgZ0xCzAJBgNVBAYT
AlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQHDAlTdW5ueXZhbGUxETAP
BgNVBAoMCEZvcnRpbmV0MRIwEAYDVQQLDAlGb3J0aUdhdGUxGTAXBgNVBAMMEEZH
NkgxRVRCMjA5MDcwMzExIzAhBgkqhkiG9w0BCQEWFHN1cHBvcnRAZm9ydGluZXQu
Y29tMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAERA0N495hYIDgVVTtNfeBuuH/MFIP
1gW05rMC+ES9fxWx1+T0QoiWraPS3SLYyrjOwn0EJ51PFf+fH+Du0DNQyc5qjF6F
EjFRv9Qax3bmSfkNL3Zo1hSKqrkTMlbC4urvoyIwIDAJBgNVHRMEAjAAMBMGA1Ud
JQQMMAoGCCsGAQUFBwMBMAoGCCqGSM49BAMCA2gAMGUCMQCJX1Dxu47EeVEzq8oC
T1hnpqoIjNTU7c9afgsj7kRJ6cF2D9jumaUZke77l0GqelYCMBpiykaReizuvQ/O
VmlhbN3e+k34qxK/rqeXoVYmC30nq9p8wRjONvZXxTG3Kw/ttw==
-----END CERTIFICATE-----"
        set range global
        set source factory
        set last-updated 1749195409
    next
    edit "Fortinet_SSL_ECDSA521"
        set password ENC hPX/tgh8iQBXWQoMuI7YNAiT0XaFCl1DXJNzudNw3NwnClS0W6uEdudsYFF8P0w2XrKWWhvOIDFIXdBXVCudUzDbOEFtTpif7ldMPMUxObbLWY8TslGXeWWAsJoryxCmOginjLv/uIwFEiXUSwnJg2yag3wPDEavFLskh8hpcBI+iB+Z/n3WuaXY9H6DVDvWZXEbEVlmMjY3dkVA
        set comments "This certificate is embedded in the hardware at the factory and is unique to this unit. "
        set private-key "-----BEGIN ENCRYPTED PRIVATE KEY-----
MIIBUzBWBgkqhkiG9w0BBQ0wSTAxBgkqhkiG9w0BBQwwJAQQbqLFzKIS7sBpqpMO
U0Am2wICCAAwDAYIKoZIhvcNAgkFADAUBggqhkiG9w0DBwQIcBAJPaeWHPwEgfhR
ynEj66hNK8QSOscy7R2b7w6dUIjNpUM2v3BP7COLnWFDo0RNuPkIu8CMI49Tho6O
HmoW8atw8wzj1+MU6f2PX47Q1en6reWSNJp6hJl0+UsVGOzlbhDv0oUSQrlcpUuN
ACol2QrGjMKfOelhhOOjnqQDB2oohCDrKRHUMX6YP7Wynz3tQ/QYkqwmAjjBidum
FY50mXOnYK7cnvzLeKaftvGtlCy/sL2b6+5lxNTcEFdq/dNPh8KKYdUJoOAZAVZ9
OalBqw6jFO6e9L7PxOCSReD72hvz1AHWHNS9sGXi5BRqbPm/C3QBTxADgAwiLK2T
vnmVmQG3XA==
-----END ENCRYPTED PRIVATE KEY-----"
        set certificate "-----BEGIN CERTIFICATE-----
MIIC3DCCAj2gAwIBAgIIacirCSRKTu8wCgYIKoZIzj0EAwIwgZ0xCzAJBgNVBAYT
AlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQHDAlTdW5ueXZhbGUxETAP
BgNVBAoMCEZvcnRpbmV0MRIwEAYDVQQLDAlGb3J0aUdhdGUxGTAXBgNVBAMMEEZH
NkgxRVRCMjA5MDcwMzExIzAhBgkqhkiG9w0BCQEWFHN1cHBvcnRAZm9ydGluZXQu
Y29tMB4XDTI0MDUyMTA0NDAxM1oXDTI2MDgyNDA0NDAxM1owgZ0xCzAJBgNVBAYT
AlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQHDAlTdW5ueXZhbGUxETAP
BgNVBAoMCEZvcnRpbmV0MRIwEAYDVQQLDAlGb3J0aUdhdGUxGTAXBgNVBAMMEEZH
NkgxRVRCMjA5MDcwMzExIzAhBgkqhkiG9w0BCQEWFHN1cHBvcnRAZm9ydGluZXQu
Y29tMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQAfOCrla392CU++ScmxGu9pHRp
wPHke5P/t6oQ2XrPmKshNEJTGHQp5R1lFS0GTNNgewz9I5CK2H3d67CIQzXmQUIA
FNYU4oZyDyuyp9CXpb393QL/1l/vnLEMGSoEkGoZcPYZWdZASU80PZS3BvqwycMk
4MHewlnLHnaeDEma9ibEFTWjIjAgMAkGA1UdEwQCMAAwEwYDVR0lBAwwCgYIKwYB
BQUHAwEwCgYIKoZIzj0EAwIDgYwAMIGIAkIB/XStoQgdOzPny1ney7ojuyKTmzRz
RbiRf5o3JMO9VkORSAPn1XLauOA3Nuqpdsswg7emZaCq0U7K81gYB9Mz/JkCQgHU
YXYWfE0nTbyO/wlmv9Yk8QUaT4PXK1M5VCehrVMlL6cEJF5Czxi3KLpgnQgROm6A
08R1R+TmPaBFgI3JEezzyw==
-----END CERTIFICATE-----"
        set range global
        set source factory
        set last-updated 1749195409
    next
    edit "Fortinet_SSL_ED25519"
        set password ENC Zo7cWLkHQyiPkhXtY0rBYZhY3u2A5xWjpwPLQl4YKwFEEBL1X7U3CMzWOhOxgJcxdJ45XHs7TYGgc8ut93L+Bt5EQf6Kx9NBAx8Urp2kR7pF9Y1V19QhrmkCvbSJR1XYDdVSarQsyyLFfyOahYw88P6eWG8DhnxzAIoFTmtunV/nqBs65cDR/s3cz5PaVy8O5FQRhllmMjY3dkVA
        set comments "This certificate is embedded in the hardware at the factory and is unique to this unit. "
        set private-key "-----BEGIN ENCRYPTED PRIVATE KEY-----
MIGSMFYGCSqGSIb3DQEFDTBJMDEGCSqGSIb3DQEFDDAkBBBDA8I85pAIzKT/evzm
PwTkAgIIADAMBggqhkiG9w0CCQUAMBQGCCqGSIb3DQMHBAjQ8ccjhHqQxQQ4Bu4z
hLHygap+JTCgmwttoSzcPCxsGbtLt37SsG8bjst5iLmRl44isFPuE1Em9Sz2s+Um
J197JxI=
-----END ENCRYPTED PRIVATE KEY-----"
        set certificate "-----BEGIN CERTIFICATE-----
MIICFDCCAcagAwIBAgIIeieZsO7O6oAwBQYDK2VwMIGdMQswCQYDVQQGEwJVUzET
MBEGA1UECAwKQ2FsaWZvcm5pYTESMBAGA1UEBwwJU3Vubnl2YWxlMREwDwYDVQQK
DAhGb3J0aW5ldDESMBAGA1UECwwJRm9ydGlHYXRlMRkwFwYDVQQDDBBGRzZIMUVU
QjIwOTA3MDMxMSMwIQYJKoZIhvcNAQkBFhRzdXBwb3J0QGZvcnRpbmV0LmNvbTAe
Fw0yNDA1MjEwNDQwMTNaFw0yNjA4MjQwNDQwMTNaMIGdMQswCQYDVQQGEwJVUzET
MBEGA1UECAwKQ2FsaWZvcm5pYTESMBAGA1UEBwwJU3Vubnl2YWxlMREwDwYDVQQK
DAhGb3J0aW5ldDESMBAGA1UECwwJRm9ydGlHYXRlMRkwFwYDVQQDDBBGRzZIMUVU
QjIwOTA3MDMxMSMwIQYJKoZIhvcNAQkBFhRzdXBwb3J0QGZvcnRpbmV0LmNvbTAq
MAUGAytlcAMhAJKdet4VYznVyiyEyGwulUMEbYsM3puBlRYMCibYV0eCoyIwIDAJ
BgNVHRMEAjAAMBMGA1UdJQQMMAoGCCsGAQUFBwMBMAUGAytlcANBAIjSrAug6054
LY0WPV06cbzxWtFiUTXXx5+5j73UJYRv5BoAydALqaHG9l9iSrsDyDzCZnaBd8zh
ERaRnzwkSgY=
-----END CERTIFICATE-----"
        set range global
        set source factory
        set last-updated 1749195409
    next
    edit "Fortinet_SSL_ED448"
        set password ENC Bx3uzsPpDyKJSW5uYtuhL3dlBLGdUhv4PxturQiVhQgxCTCQJ+TRBmtv4f0MTqbAdOY22S6Q34Uj+kIYov5Pb/IntT6JfWH+OrR4WGIns96EIk6LlhMV6k855mOQp6fXO8JoWWEQeUjQM2MVLfM+qpa0U76lOLLnrSbRSqbL//7D8l+vEvwHP8t8THblPqB/rIMjmFlmMjY3dkVA
        set comments "This certificate is embedded in the hardware at the factory and is unique to this unit. "
        set private-key "-----BEGIN ENCRYPTED PRIVATE KEY-----
MIGqMFYGCSqGSIb3DQEFDTBJMDEGCSqGSIb3DQEFDDAkBBDtI/HsHSK2HQyWSTDA
3aMIAgIIADAMBggqhkiG9w0CCQUAMBQGCCqGSIb3DQMHBAjygfmjeQQGJwRQ3Cyv
kZ4nakRla2hmREjQL+lNSWnupKSPO13STimSnmgXXCS2InH9QaOagEnfXfsrQk9T
/O0GLva2ZnRd1ya0pRkB3E5cLzOeh5CZzLx3nNo=
-----END ENCRYPTED PRIVATE KEY-----"
        set certificate "-----BEGIN CERTIFICATE-----
MIICXzCCAd+gAwIBAgIIFybup0M4390wBQYDK2VxMIGdMQswCQYDVQQGEwJVUzET
MBEGA1UECAwKQ2FsaWZvcm5pYTESMBAGA1UEBwwJU3Vubnl2YWxlMREwDwYDVQQK
DAhGb3J0aW5ldDESMBAGA1UECwwJRm9ydGlHYXRlMRkwFwYDVQQDDBBGRzZIMUVU
QjIwOTA3MDMxMSMwIQYJKoZIhvcNAQkBFhRzdXBwb3J0QGZvcnRpbmV0LmNvbTAe
Fw0yNDA1MjEwNDQwMTNaFw0yNjA4MjQwNDQwMTNaMIGdMQswCQYDVQQGEwJVUzET
MBEGA1UECAwKQ2FsaWZvcm5pYTESMBAGA1UEBwwJU3Vubnl2YWxlMREwDwYDVQQK
DAhGb3J0aW5ldDESMBAGA1UECwwJRm9ydGlHYXRlMRkwFwYDVQQDDBBGRzZIMUVU
QjIwOTA3MDMxMSMwIQYJKoZIhvcNAQkBFhRzdXBwb3J0QGZvcnRpbmV0LmNvbTBD
MAUGAytlcQM6AH6eZlKfXaO1j2aMefxRzxvqfNV0iojuj/Emy0xXL7vd3wSiZ938
lye2UQIZbZFm3gpwp/1ZGljOgKMiMCAwCQYDVR0TBAIwADATBgNVHSUEDDAKBggr
BgEFBQcDATAFBgMrZXEDcwD4wS9nNQGaIvhoc5y/2xDr93s3qgp4tnnn1mT/qrx0
T9+jmKyLG9OMzf6rPKDPONZlJohK3slncQBDFImnzgG1cnyBos9BRZM/fFCpuX/6
EZMfLp+zX4khREyeOlloGG3uv7KCANZc02uNvd5T1v4kEQA=
-----END CERTIFICATE-----"
        set range global
        set source factory
        set last-updated 1749195409
    next
end
config webfilter ftgd-local-cat
    edit "custom1"
        set id 140
    next
    edit "custom2"
        set id 141
    next
end
config ips sensor
    edit "default"
        set comment "Prevent critical attacks."
        config entries
            edit 1
                set severity medium high critical
            next
        end
    next
    edit "sniffer-profile"
        set comment "Monitor IPS attacks."
        config entries
            edit 1
                set severity medium high critical
            next
        end
    next
    edit "wifi-default"
        set comment "Default configuration for offloading WiFi traffic."
        config entries
            edit 1
                set severity medium high critical
            next
        end
    next
    edit "all_default"
        set comment "All predefined signatures with default setting."
        config entries
            edit 1
            next
        end
    next
    edit "all_default_pass"
        set comment "All predefined signatures with PASS action."
        config entries
            edit 1
                set action pass
            next
        end
    next
    edit "protect_http_server"
        set comment "Protect against HTTP server-side vulnerabilities."
        config entries
            edit 1
                set location server
                set protocol HTTP
            next
        end
    next
    edit "protect_email_server"
        set comment "Protect against email server-side vulnerabilities."
        config entries
            edit 1
                set location server
                set protocol SMTP POP3 IMAP
            next
        end
    next
    edit "protect_client"
        set comment "Protect against client-side vulnerabilities."
        config entries
            edit 1
                set location client
            next
        end
    next
    edit "high_security"
        set comment "Blocks all Critical/High/Medium and some Low severity vulnerabilities"
        set block-malicious-url enable
        config entries
            edit 1
                set severity medium high critical
                set status enable
                set action block
            next
            edit 2
                set severity low
            next
        end
    next
end
config firewall shaper traffic-shaper
    edit "high-priority"
        set maximum-bandwidth 1048576
        set per-policy enable
    next
    edit "medium-priority"
        set maximum-bandwidth 1048576
        set priority medium
        set per-policy enable
    next
    edit "low-priority"
        set maximum-bandwidth 1048576
        set priority low
        set per-policy enable
    next
    edit "guarantee-100kbps"
        set guaranteed-bandwidth 100
        set maximum-bandwidth 1048576
        set per-policy enable
    next
    edit "shared-1M-pipe"
        set maximum-bandwidth 1024
    next
end
config firewall proxy-address
    edit "IPv4-address"
        set uuid 01cd6c1e-42a9-51f0-6b3d-38b3b5c16599
        set type host-regex
        set host-regex "^([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])(\\.([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])){3}$"
    next
    edit "IPv6-address"
        set uuid 01cd72f4-42a9-51f0-d875-0034cb27e77c
        set type host-regex
        set host-regex "^\\[(([0-9a-f]{0,4}:){1,7}[0-9a-f]{1,4})\\]$"
    next
end
config web-proxy global
    set proxy-fqdn "default.fqdn"
end
config application list
    edit "default"
        set comment "Monitor all applications."
        config entries
            edit 1
                set action pass
            next
        end
    next
    edit "sniffer-profile"
        set comment "Monitor all applications."
        unset options
        config entries
            edit 1
                set action pass
            next
        end
    next
    edit "wifi-default"
        set comment "Default configuration for offloading WiFi traffic."
        set deep-app-inspection disable
        config entries
            edit 1
                set action pass
                set log disable
            next
        end
    next
    edit "block-high-risk"
        config entries
            edit 1
                set category 2 6
            next
            edit 2
                set action pass
            next
        end
    next
end
config dlp data-type
    edit "keyword"
        set pattern "built-in"
    next
    edit "regex"
        set pattern "built-in"
    next
    edit "hex"
        set pattern "built-in"
    next
    edit "mip-label"
        set pattern "^[[:xdigit:]]{8}-[[:xdigit:]]{4}-[[:xdigit:]]{4}-[[:xdigit:]]{4}-[[:xdigit:]]{12}$"
        set transform "built-in"
    next
    edit "credit-card"
        set pattern "\\b([2-6]{1}\\d{3})[- ]?(\\d{4})[- ]?(\\d{2})[- ]?(\\d{2})[- ]?(\\d{2,4})\\b"
        set verify "builtin)credit-card"
        set look-back 20
        set transform "\\b\\1[- ]?\\2[- ]?\\3[- ]?\\4[- ]?\\5\\b"
    next
    edit "ssn-us"
        set pattern "\\b(\\d{3})-(\\d{2})-(\\d{4})\\b"
        set verify "(?<!-)\\b(?!666|000|9\\d{2})\\d{3}-(?!00)\\d{2}-(?!0{4})\\d{4}\\b(?!-)"
        set look-back 12
        set transform "\\b\\1-\\2-\\3\\b"
    next
    edit "edm-keyword"
        set pattern ".+"
        set transform "/\\b\\0\\b/i"
    next
end
config dlp dictionary
    edit "phonenumber"
        set uuid 38bb03c2-45dc-51f0-b642-5080ba044279
        set comment "中国大陆地区电话号码"
        config entries
            edit 1
                set type "regex"
                set pattern "^(?:\\+?86)?1[3-9]\\d{9}$"
            next
            edit 2
                set type "regex"
                set pattern "^(?:\\+?86)?(?:0\\d{2,3}-?)?[1-9]\\d{6,7}(?:-\\d{1,5})?$"
            next
        end
    next
    edit "lastLoginIp"
        set uuid 0912fe1c-45dd-51f0-1ce5-bf7b04e41f25
        config entries
            edit 1
                set type "regex"
                set pattern "\"lastLoginIp\":\\s*\"((?:25[0-5]|2[0-4]\\d|1\\d{2}|[1-9]?\\d)\\.){3}(?:25[0-5]|2[0-4]\\d|1\\d{2}|[1-9]?\\d)\""
            next
        end
    next
    edit "leaderName"
        set uuid bdec144a-45dd-51f0-3d5c-c979eb63be3f
        config entries
            edit 1
                set type "regex"
                set pattern "^\"leaderName\":\\s*\"([a-zA-Z]+)\"$"
            next
        end
    next
end
config dlp sensor
    edit "PMS-LastLoginIp-Sensor"
        config entries
            edit 1
                set dictionary "lastLoginIp"
            next
        end
    next
    edit "PMS-leader-name-sensor"
        config entries
            edit 1
                set dictionary "leaderName"
            next
        end
    next
    edit "PMS-phonenumber-sensor"
        config entries
            edit 1
                set dictionary "phonenumber"
            next
        end
    next
end
config dlp filepattern
    edit 1
        set name "builtin-patterns"
        config entries
            edit "*.bat"
            next
            edit "*.com"
            next
            edit "*.dll"
            next
            edit "*.doc"
            next
            edit "*.exe"
            next
            edit "*.gz"
            next
            edit "*.hta"
            next
            edit "*.ppt"
            next
            edit "*.rar"
            next
            edit "*.scr"
            next
            edit "*.tar"
            next
            edit "*.tgz"
            next
            edit "*.vb?"
            next
            edit "*.wps"
            next
            edit "*.xl?"
            next
            edit "*.zip"
            next
            edit "*.pif"
            next
            edit "*.cpl"
            next
        end
    next
    edit 2
        set name "all_executables"
        config entries
            edit "bat"
                set filter-type type
                set file-type bat
            next
            edit "exe"
                set filter-type type
                set file-type exe
            next
            edit "elf"
                set filter-type type
                set file-type elf
            next
            edit "hta"
                set filter-type type
                set file-type hta
            next
        end
    next
end
config dlp sensitivity
    edit "Private"
    next
    edit "Critical"
    next
    edit "Warning"
    next
end
config dlp profile
    edit "default"
        set comment "Default profile."
    next
    edit "sniffer-profile"
        set comment "Log a summary of email and web traffic."
        set summary-proto smtp pop3 imap http-get http-post
    next
    edit "Content_Summary"
        set summary-proto smtp pop3 imap http-get http-post ftp nntp cifs
    next
    edit "Content_Archive"
        set full-archive-proto smtp pop3 imap http-get http-post ftp nntp cifs
        set summary-proto smtp pop3 imap http-get http-post ftp nntp cifs
    next
    edit "Large-File"
        config rule
            edit 1
                set name "Large-File-Filter"
                set proto smtp pop3 imap http-get http-post
                set file-size 5120
                set action log-only
            next
        end
    next
    edit "PMS-DLP"
        set feature-set proxy
        config rule
            edit 1
                set name "PMS-DLP"
                set type message
                set proto http-post
                set filter-by sensor
                set sensor "PMS-LastLoginIp-Sensor" "PMS-leader-name-sensor" "PMS-phonenumber-sensor"
                set action log-only
            next
        end
    next
end
config webfilter ips-urlfilter-setting
end
config webfilter ips-urlfilter-setting6
end
config log threat-weight
    config web
        edit 1
            set category 26
            set level high
        next
        edit 2
            set category 61
            set level high
        next
        edit 3
            set category 86
            set level high
        next
        edit 4
            set category 1
            set level medium
        next
        edit 5
            set category 3
            set level medium
        next
        edit 6
            set category 4
            set level medium
        next
        edit 7
            set category 5
            set level medium
        next
        edit 8
            set category 6
            set level medium
        next
        edit 9
            set category 12
            set level medium
        next
        edit 10
            set category 59
            set level medium
        next
        edit 11
            set category 62
            set level medium
        next
        edit 12
            set category 83
            set level medium
        next
        edit 13
            set category 72
        next
        edit 14
            set category 14
        next
        edit 15
            set category 96
            set level medium
        next
    end
    config application
        edit 1
            set category 2
        next
        edit 2
            set category 6
            set level medium
        next
    end
end
config icap profile
    edit "default"
        config icap-headers
            edit 1
                set name "X-Authenticated-User"
                set content "$auth_user_uri"
                set base64-encoding enable
            next
            edit 2
                set name "X-Authenticated-Groups"
                set content "$auth_group_uri"
                set base64-encoding enable
            next
        end
    next
end
config user local
    edit "guest"
        set type password
        set passwd-time 2025-06-06 00:36:49
        set passwd ENC Ct7FnRNmxv5shnNdn2G2uBwI9JMEjSHwKLVc8chxKKpu3YEnpitE581+gahtfyP12nCvNNrO3sXrxMn+Kh3S88OxgLTvfT+YNozb6TtJlXMI9nVOzB4Ls+PqRc0P1IEg1OvReACjcXh5QAuRNwHMn0J6BIF7XHZioCCwxN4VCv0peM0bqId8h3Y1dp/71rZVp6qKw1lmMjY3dkVA
    next
end
config user setting
    set auth-cert "Fortinet_Factory"
end
config user group
    edit "SSO_Guest_Users"
    next
    edit "Guest-group"
        set member "guest"
    next
end
config voip profile
    edit "default"
        set comment "Default VoIP profile."
        config sip
        end
    next
    edit "strict"
        config sip
            set malformed-request-line discard
            set malformed-header-via discard
            set malformed-header-from discard
            set malformed-header-to discard
            set malformed-header-call-id discard
            set malformed-header-cseq discard
            set malformed-header-rack discard
            set malformed-header-rseq discard
            set malformed-header-contact discard
            set malformed-header-record-route discard
            set malformed-header-route discard
            set malformed-header-expires discard
            set malformed-header-content-type discard
            set malformed-header-content-length discard
            set malformed-header-max-forwards discard
            set malformed-header-allow discard
            set malformed-header-p-asserted-identity discard
            set malformed-header-sdp-v discard
            set malformed-header-sdp-o discard
            set malformed-header-sdp-s discard
            set malformed-header-sdp-i discard
            set malformed-header-sdp-c discard
            set malformed-header-sdp-b discard
            set malformed-header-sdp-z discard
            set malformed-header-sdp-k discard
            set malformed-header-sdp-a discard
            set malformed-header-sdp-t discard
            set malformed-header-sdp-r discard
            set malformed-header-sdp-m discard
        end
    next
end
config dnsfilter profile
    edit "default"
        set comment "Default dns filtering."
        config ftgd-dns
            config filters
                edit 1
                    set category 2
                next
                edit 2
                    set category 7
                next
                edit 3
                    set category 8
                next
                edit 4
                    set category 9
                next
                edit 5
                    set category 11
                next
                edit 6
                    set category 12
                next
                edit 7
                    set category 13
                next
                edit 8
                    set category 14
                next
                edit 9
                    set category 15
                next
                edit 10
                    set category 16
                next
                edit 11
                next
                edit 12
                    set category 57
                next
                edit 13
                    set category 63
                next
                edit 14
                    set category 64
                next
                edit 15
                    set category 65
                next
                edit 16
                    set category 66
                next
                edit 17
                    set category 67
                next
                edit 18
                    set category 26
                    set action block
                next
                edit 19
                    set category 61
                    set action block
                next
                edit 20
                    set category 86
                    set action block
                next
                edit 21
                    set category 88
                    set action block
                next
                edit 22
                    set category 90
                    set action block
                next
                edit 23
                    set category 91
                    set action block
                next
                edit 24
                    set category 96
                    set action block
                next
                edit 25
                    set category 98
                    set action block
                next
                edit 26
                    set category 99
                    set action block
                next
            end
        end
        set block-botnet enable
    next
end
config antivirus settings
    set machine-learning-detection enable
    set grayware enable
end
config antivirus profile
    edit "default"
        set comment "Scan files and block viruses."
        config http
            set av-scan block
        end
        config ftp
            set av-scan block
        end
        config imap
            set av-scan block
            set executables virus
        end
        config pop3
            set av-scan block
            set executables virus
        end
        config smtp
            set av-scan block
            set executables virus
        end
    next
    edit "sniffer-profile"
        set comment "Scan files and monitor viruses."
        config http
            set av-scan block
        end
        config ftp
            set av-scan block
        end
        config imap
            set av-scan block
            set executables virus
        end
        config pop3
            set av-scan block
            set executables virus
        end
        config smtp
            set av-scan block
            set executables virus
        end
    next
    edit "wifi-default"
        set comment "Default configuration for offloading WiFi traffic."
        config http
            set av-scan block
        end
        config ftp
            set av-scan block
        end
        config imap
            set av-scan block
            set executables virus
        end
        config pop3
            set av-scan block
            set executables virus
        end
        config smtp
            set av-scan block
            set executables virus
        end
    next
end
config file-filter profile
    edit "default"
        set comment "File type inspection."
    next
    edit "sniffer-profile"
        set comment "File type inspection."
    next
end
config webfilter profile
    edit "default"
        set comment "Default web filtering."
        config ftgd-wf
            unset options
            config filters
                edit 1
                    set action block
                next
                edit 2
                    set category 2
                    set action block
                next
                edit 3
                    set category 7
                    set action block
                next
                edit 4
                    set category 8
                    set action block
                next
                edit 5
                    set category 9
                    set action block
                next
                edit 6
                    set category 11
                    set action block
                next
                edit 7
                    set category 13
                    set action block
                next
                edit 8
                    set category 14
                    set action block
                next
                edit 9
                    set category 15
                    set action block
                next
                edit 10
                    set category 16
                    set action block
                next
                edit 11
                    set category 26
                    set action block
                next
                edit 12
                    set category 57
                    set action block
                next
                edit 13
                    set category 61
                    set action block
                next
                edit 14
                    set category 63
                    set action block
                next
                edit 15
                    set category 64
                    set action block
                next
                edit 16
                    set category 65
                    set action block
                next
                edit 17
                    set category 66
                    set action block
                next
                edit 18
                    set category 67
                    set action block
                next
                edit 19
                    set category 83
                    set action block
                next
                edit 20
                    set category 86
                    set action block
                next
                edit 21
                    set category 88
                    set action block
                next
                edit 22
                    set category 90
                    set action block
                next
                edit 23
                    set category 91
                    set action block
                next
                edit 24
                    set category 96
                    set action block
                next
                edit 25
                    set category 98
                    set action block
                next
                edit 26
                    set category 99
                    set action block
                next
                edit 27
                    set category 1
                next
                edit 28
                    set category 3
                next
                edit 29
                    set category 4
                next
                edit 30
                    set category 5
                next
                edit 31
                    set category 6
                next
                edit 32
                    set category 12
                next
                edit 33
                    set category 59
                next
                edit 34
                    set category 62
                next
            end
        end
    next
    edit "sniffer-profile"
        set comment "Monitor web traffic."
        config ftgd-wf
            config filters
                edit 1
                next
                edit 2
                    set category 1
                next
                edit 3
                    set category 2
                next
                edit 4
                    set category 3
                next
                edit 5
                    set category 4
                next
                edit 6
                    set category 5
                next
                edit 7
                    set category 6
                next
                edit 8
                    set category 7
                next
                edit 9
                    set category 8
                next
                edit 10
                    set category 9
                next
                edit 11
                    set category 11
                next
                edit 12
                    set category 12
                next
                edit 13
                    set category 13
                next
                edit 14
                    set category 14
                next
                edit 15
                    set category 15
                next
                edit 16
                    set category 16
                next
                edit 17
                    set category 17
                next
                edit 18
                    set category 18
                next
                edit 19
                    set category 19
                next
                edit 20
                    set category 20
                next
                edit 21
                    set category 23
                next
                edit 22
                    set category 24
                next
                edit 23
                    set category 25
                next
                edit 24
                    set category 26
                next
                edit 25
                    set category 28
                next
                edit 26
                    set category 29
                next
                edit 27
                    set category 30
                next
                edit 28
                    set category 31
                next
                edit 29
                    set category 33
                next
                edit 30
                    set category 34
                next
                edit 31
                    set category 35
                next
                edit 32
                    set category 36
                next
                edit 33
                    set category 37
                next
                edit 34
                    set category 38
                next
                edit 35
                    set category 39
                next
                edit 36
                    set category 40
                next
                edit 37
                    set category 41
                next
                edit 38
                    set category 42
                next
                edit 39
                    set category 43
                next
                edit 40
                    set category 44
                next
                edit 41
                    set category 46
                next
                edit 42
                    set category 47
                next
                edit 43
                    set category 48
                next
                edit 44
                    set category 49
                next
                edit 45
                    set category 50
                next
                edit 46
                    set category 51
                next
                edit 47
                    set category 52
                next
                edit 48
                    set category 53
                next
                edit 49
                    set category 54
                next
                edit 50
                    set category 55
                next
                edit 51
                    set category 56
                next
                edit 52
                    set category 57
                next
                edit 53
                    set category 58
                next
                edit 54
                    set category 59
                next
                edit 55
                    set category 61
                next
                edit 56
                    set category 62
                next
                edit 57
                    set category 63
                next
                edit 58
                    set category 64
                next
                edit 59
                    set category 65
                next
                edit 60
                    set category 66
                next
                edit 61
                    set category 67
                next
                edit 62
                    set category 68
                next
                edit 63
                    set category 69
                next
                edit 64
                    set category 70
                next
                edit 65
                    set category 71
                next
                edit 66
                    set category 72
                next
                edit 67
                    set category 75
                next
                edit 68
                    set category 76
                next
                edit 69
                    set category 77
                next
                edit 70
                    set category 78
                next
                edit 71
                    set category 79
                next
                edit 72
                    set category 80
                next
                edit 73
                    set category 81
                next
                edit 74
                    set category 82
                next
                edit 75
                    set category 83
                next
                edit 76
                    set category 84
                next
                edit 77
                    set category 85
                next
                edit 78
                    set category 86
                next
                edit 79
                    set category 87
                next
                edit 80
                    set category 88
                next
                edit 81
                    set category 89
                next
                edit 82
                    set category 90
                next
                edit 83
                    set category 91
                next
                edit 84
                    set category 92
                next
                edit 85
                    set category 93
                next
                edit 86
                    set category 94
                next
                edit 87
                    set category 95
                next
                edit 88
                    set category 96
                next
                edit 89
                    set category 97
                next
                edit 90
                    set category 98
                next
                edit 91
                    set category 99
                next
                edit 92
                    set category 100
                next
                edit 93
                    set category 101
                next
            end
        end
    next
    edit "wifi-default"
        set comment "Default configuration for offloading WiFi traffic."
        set options block-invalid-url
        config ftgd-wf
            unset options
            config filters
                edit 1
                    set action block
                next
                edit 2
                    set category 2
                    set action block
                next
                edit 3
                    set category 7
                    set action block
                next
                edit 4
                    set category 8
                    set action block
                next
                edit 5
                    set category 9
                    set action block
                next
                edit 6
                    set category 11
                    set action block
                next
                edit 7
                    set category 13
                    set action block
                next
                edit 8
                    set category 14
                    set action block
                next
                edit 9
                    set category 15
                    set action block
                next
                edit 10
                    set category 16
                    set action block
                next
                edit 11
                    set category 26
                    set action block
                next
                edit 12
                    set category 57
                    set action block
                next
                edit 13
                    set category 61
                    set action block
                next
                edit 14
                    set category 63
                    set action block
                next
                edit 15
                    set category 64
                    set action block
                next
                edit 16
                    set category 65
                    set action block
                next
                edit 17
                    set category 66
                    set action block
                next
                edit 18
                    set category 67
                    set action block
                next
                edit 19
                    set category 83
                    set action block
                next
                edit 20
                    set category 86
                    set action block
                next
                edit 21
                    set category 88
                    set action block
                next
                edit 22
                    set category 90
                    set action block
                next
                edit 23
                    set category 91
                    set action block
                next
                edit 24
                    set category 96
                    set action block
                next
                edit 25
                    set category 98
                    set action block
                next
                edit 26
                    set category 99
                    set action block
                next
                edit 27
                    set category 1
                next
                edit 28
                    set category 3
                next
                edit 29
                    set category 4
                next
                edit 30
                    set category 5
                next
                edit 31
                    set category 6
                next
                edit 32
                    set category 12
                next
                edit 33
                    set category 59
                next
                edit 34
                    set category 62
                next
            end
        end
    next
    edit "monitor-all"
        set comment "Monitor and log all visited URLs, flow-based."
        config ftgd-wf
            unset options
            config filters
                edit 1
                    set category 1
                next
                edit 2
                    set category 3
                next
                edit 3
                    set category 4
                next
                edit 4
                    set category 5
                next
                edit 5
                    set category 6
                next
                edit 6
                    set category 12
                next
                edit 7
                    set category 59
                next
                edit 8
                    set category 62
                next
                edit 9
                    set category 83
                next
                edit 10
                    set category 2
                next
                edit 11
                    set category 7
                next
                edit 12
                    set category 8
                next
                edit 13
                    set category 9
                next
                edit 14
                    set category 11
                next
                edit 15
                    set category 13
                next
                edit 16
                    set category 14
                next
                edit 17
                    set category 15
                next
                edit 18
                    set category 16
                next
                edit 19
                    set category 57
                next
                edit 20
                    set category 63
                next
                edit 21
                    set category 64
                next
                edit 22
                    set category 65
                next
                edit 23
                    set category 66
                next
                edit 24
                    set category 67
                next
                edit 25
                    set category 19
                next
                edit 26
                    set category 24
                next
                edit 27
                    set category 25
                next
                edit 28
                    set category 72
                next
                edit 29
                    set category 75
                next
                edit 30
                    set category 76
                next
                edit 31
                    set category 26
                next
                edit 32
                    set category 61
                next
                edit 33
                    set category 86
                next
                edit 34
                    set category 17
                next
                edit 35
                    set category 18
                next
                edit 36
                    set category 20
                next
                edit 37
                    set category 23
                next
                edit 38
                    set category 28
                next
                edit 39
                    set category 29
                next
                edit 40
                    set category 30
                next
                edit 41
                    set category 33
                next
                edit 42
                    set category 34
                next
                edit 43
                    set category 35
                next
                edit 44
                    set category 36
                next
                edit 45
                    set category 37
                next
                edit 46
                    set category 38
                next
                edit 47
                    set category 39
                next
                edit 48
                    set category 40
                next
                edit 49
                    set category 42
                next
                edit 50
                    set category 44
                next
                edit 51
                    set category 46
                next
                edit 52
                    set category 47
                next
                edit 53
                    set category 48
                next
                edit 54
                    set category 54
                next
                edit 55
                    set category 55
                next
                edit 56
                    set category 58
                next
                edit 57
                    set category 68
                next
                edit 58
                    set category 69
                next
                edit 59
                    set category 70
                next
                edit 60
                    set category 71
                next
                edit 61
                    set category 77
                next
                edit 62
                    set category 78
                next
                edit 63
                    set category 79
                next
                edit 64
                    set category 80
                next
                edit 65
                    set category 82
                next
                edit 66
                    set category 85
                next
                edit 67
                    set category 87
                next
                edit 68
                    set category 31
                next
                edit 69
                    set category 41
                next
                edit 70
                    set category 43
                next
                edit 71
                    set category 49
                next
                edit 72
                    set category 50
                next
                edit 73
                    set category 51
                next
                edit 74
                    set category 52
                next
                edit 75
                    set category 53
                next
                edit 76
                    set category 56
                next
                edit 77
                    set category 81
                next
                edit 78
                    set category 84
                next
                edit 79
                next
                edit 80
                    set category 88
                next
                edit 81
                    set category 89
                next
                edit 82
                    set category 90
                next
                edit 83
                    set category 91
                next
                edit 84
                    set category 92
                next
                edit 85
                    set category 93
                next
                edit 86
                    set category 94
                next
                edit 87
                    set category 95
                next
                edit 88
                    set category 96
                next
                edit 89
                    set category 97
                next
                edit 90
                    set category 98
                next
                edit 91
                    set category 99
                next
                edit 92
                    set category 100
                next
                edit 93
                    set category 101
                next
            end
        end
        set log-all-url enable
        set web-content-log disable
        set web-filter-command-block-log disable
        set web-filter-cookie-log disable
        set web-url-log disable
        set web-invalid-domain-log disable
        set web-ftgd-err-log disable
    next
end
config webfilter search-engine
    edit "google"
        set hostname ".*\\.google\\..*"
        set url "^\\/((custom|search|images|videosearch|webhp)\\?)"
        set query "q="
        set safesearch url
        set safesearch-str "&safe=active"
    next
    edit "yahoo"
        set hostname ".*\\.yahoo\\..*"
        set url "^\\/search(\\/video|\\/images){0,1}(\\?|;)"
        set query "p="
        set safesearch url
        set safesearch-str "&vm=r"
    next
    edit "bing"
        set hostname ".*\\.bing\\..*"
        set url "^(\\/images|\\/videos)?(\\/search|\\/async|\\/asyncv2)\\?"
        set query "q="
        set safesearch header
    next
    edit "yandex"
        set hostname "yandex\\..*"
        set url "^\\/((|yand|images\\/|video\\/)(search)|search\\/)\\?"
        set query "text="
        set safesearch url
        set safesearch-str "&family=yes"
    next
    edit "youtube"
        set hostname ".*youtube.*"
        set safesearch header
    next
    edit "baidu"
        set hostname ".*\\.baidu\\.com"
        set url "^\\/s?\\?"
        set query "wd="
    next
    edit "baidu2"
        set hostname ".*\\.baidu\\.com"
        set url "^\\/(ns|q|m|i|v)\\?"
        set query "word="
    next
    edit "baidu3"
        set hostname "tieba\\.baidu\\.com"
        set url "^\\/f\\?"
        set query "kw="
    next
    edit "vimeo"
        set hostname ".*vimeo.*"
        set url "^\\/search\\?"
        set query "q="
        set safesearch header
    next
    edit "yt-scan-1"
        set url "www.youtube.com/user/"
        set safesearch yt-scan
    next
    edit "yt-scan-2"
        set url "www.youtube.com/youtubei/v1/browse"
        set safesearch yt-scan
    next
    edit "yt-scan-3"
        set url "www.youtube.com/youtubei/v1/player"
        set safesearch yt-scan
    next
    edit "yt-scan-4"
        set url "www.youtube.com/youtubei/v1/navigator"
        set safesearch yt-scan
    next
    edit "yt-channel"
        set url "www.youtube.com/channel"
        set safesearch yt-channel
    next
    edit "yt-pattern"
        set url "youtube.com/channel/"
        set safesearch yt-pattern
    next
    edit "twitter"
        set hostname "twitter\\.com"
        set url "^\\/i\\/api\\/graphql\\/.*\\/UserByScreenName"
        set query "variables="
        set safesearch translate
        set safesearch-str "regex::%22screen_name%22:%22([A-Za-z0-9_]{4,15})%22::twitter.com/\\1"
    next
    edit "google-translate-1"
        set hostname "translate\\.google\\..*"
        set url "^\\/translate"
        set query "u="
        set safesearch translate
        set safesearch-str "regex::(?:\\?|&)u=([^&]+)::\\1"
    next
    edit "google-translate-2"
        set hostname ".*\\.translate\\.goog"
        set url "^\\/"
        set safesearch translate
        set safesearch-str "case::google-translate"
    next
end
config emailfilter profile
    edit "default"
        set comment "Malware and phishing URL filtering."
        config imap
        end
        config pop3
        end
        config smtp
        end
    next
    edit "sniffer-profile"
        set comment "Malware and phishing URL monitoring."
        config imap
        end
        config pop3
        end
        config smtp
        end
    next
end
config virtual-patch profile
    edit "default"
    next
end
config report layout
    edit "default"
        set title "FortiGate System Analysis Report"
        set style-theme "default-report"
        set options include-table-of-content view-chart-as-heading
        config page
            set paper letter
            set page-break-before heading1
            config header
                config header-item
                    edit 1
                        set type image
                        set style "header-image"
                        set img-src "fortinet_logo_small.png"
                    next
                end
            end
            config footer
                config footer-item
                    edit 1
                        set style "footer-text"
                        set content "FortiGate ${schedule_type} Security Report - Host Name: ${hostname}"
                    next
                    edit 2
                        set style "footer-pageno"
                    next
                end
            end
        end
        config body-item
            edit 101
                set type image
                set style "report-cover1"
                set img-src "fortigate_log.png"
            next
            edit 103
                set style "report-cover2"
                set content "FortiGate ${schedule_type} Security Report"
            next
            edit 105
                set style "report-cover3"
                set content "Report Date: ${started_time}"
            next
            edit 107
                set style "report-cover3"
                set content "Data Range: ${report_data_range}  (${hostname})"
            next
            edit 109
                set style "report-cover3"
                set content "${vdom}"
            next
            edit 111
                set type image
                set style "report-cover4"
                set img-src "fortinet_logo_small.png"
            next
            edit 121
                set type misc
                set misc-component page-break
            next
            edit 301
                set text-component heading1
                set content "Bandwidth and Applications"
            next
            edit 311
                set type chart
                set chart "traffic.bandwidth.history_c"
            next
            edit 321
                set type chart
                set chart "traffic.sessions.history_c"
            next
            edit 331
                set type chart
                set chart "traffic.statistics"
            next
            edit 411
                set type chart
                set chart "traffic.bandwidth.apps_c"
            next
            edit 421
                set type chart
                set chart "traffic.bandwidth.cats_c"
            next
            edit 511
                set type chart
                set chart "traffic.bandwidth.users_c"
            next
            edit 521
                set type chart
                set chart "traffic.users.history.hour_c"
            next
            edit 611
                set type chart
                set chart "traffic.bandwidth.destinations_tab"
            next
            edit 1001
                set text-component heading1
                set content "Web Usage"
            next
            edit 1011
                set type chart
                set chart "web.allowed-request.sites_c"
            next
            edit 1021
                set type chart
                set chart "web.bandwidth.sites_c"
            next
            edit 1031
                set type chart
                set chart "web.blocked-request.sites_c"
            next
            edit 1041
                set type chart
                set chart "web.blocked-request.users_c"
            next
            edit 1051
                set type chart
                set chart "web.requests.users_c"
            next
            edit 1061
                set type chart
                set chart "web.bandwidth.users_c"
            next
            edit 1071
                set type chart
                set chart "web.bandwidth.stream-sites_c"
            next
            edit 1301
                set text-component heading1
                set content "Emails"
            next
            edit 1311
                set type chart
                set chart "email.request.senders_c"
            next
            edit 1321
                set type chart
                set chart "email.bandwidth.senders_c"
            next
            edit 1331
                set type chart
                set chart "email.request.recipients_c"
            next
            edit 1341
                set type chart
                set chart "email.bandwidth.recipients_c"
            next
            edit 1501
                set text-component heading1
                set content "Threats"
            next
            edit 1511
                set type chart
                set top-n 80
                set chart "virus.count.viruses_c"
            next
            edit 1531
                set type chart
                set top-n 80
                set chart "virus.count.users_c"
            next
            edit 1541
                set type chart
                set top-n 80
                set chart "virus.count.sources_c"
            next
            edit 1551
                set type chart
                set chart "virus.count.history_c"
            next
            edit 1561
                set type chart
                set top-n 80
                set chart "botnet.count_c"
            next
            edit 1571
                set type chart
                set top-n 80
                set chart "botnet.count.users_c"
            next
            edit 1581
                set type chart
                set top-n 80
                set chart "botnet.count.sources_c"
            next
            edit 1591
                set type chart
                set chart "botnet.count.history_c"
            next
            edit 1601
                set type chart
                set top-n 80
                set chart "attack.count.attacks_c"
            next
            edit 1611
                set type chart
                set top-n 80
                set chart "attack.count.victims_c"
            next
            edit 1621
                set type chart
                set top-n 80
                set chart "attack.count.source_bar_c"
            next
            edit 1631
                set type chart
                set chart "attack.count.blocked_attacks_c"
            next
            edit 1641
                set type chart
                set chart "attack.count.severity_c"
            next
            edit 1651
                set type chart
                set chart "attack.count.history_c"
            next
            edit 1701
                set text-component heading1
                set content "VPN Usage"
            next
            edit 1711
                set type chart
                set top-n 80
                set chart "vpn.bandwidth.static-tunnels_c"
            next
            edit 1721
                set type chart
                set top-n 80
                set chart "vpn.bandwidth.dynamic-tunnels_c"
            next
            edit 1731
                set type chart
                set top-n 80
                set chart "vpn.bandwidth.ssl-tunnel.users_c"
            next
            edit 1741
                set type chart
                set top-n 80
                set chart "vpn.bandwidth.ssl-web.users_c"
            next
            edit 1901
                set text-component heading1
                set content "Admin Login and System Events"
            next
            edit 1911
                set type chart
                set top-n 80
                set chart "event.login.summary_c"
            next
            edit 1931
                set type chart
                set top-n 80
                set chart "event.failed.login_c"
            next
            edit 1961
                set type chart
                set top-n 80
                set chart "event.system.group_events_c"
            next
        end
    next
end
config report setting
    set report-source forward-traffic sniffer-traffic
end
config wanopt settings
    set host-id "default-id"
end
config wanopt profile
    edit "default"
        set comments "Default WANopt profile."
    next
end
config log memory setting
    set status disable
end
config log disk setting
    set status enable
end
config log null-device setting
    set status disable
end
config firewall schedule recurring
    edit "always"
        set day sunday monday tuesday wednesday thursday friday saturday
    next
    edit "none"
    next
    edit "default-darrp-optimize"
        set start 01:00
        set end 01:30
        set day sunday monday tuesday wednesday thursday friday saturday
    next
end
config firewall ssh local-key
    edit "Fortinet_SSH_RSA2048"
        set password ENC uV//+0XsTxvC+uJ3fCipCoYqU+kKsdkg7eAS8nZuCee5Rkt7TAbImwRJR4uVks2xWS3rSMJbGeZ8+FnQNyHDl82LTPRsfmCye46QmL97pJoSIYXcFpnozp0rVLg3eK/a4vPHSdJWFBhp4slekNsBjcvStDJMriXl+NNW2NPTdoAFHCncAA4hi1JVmycP9wulqGQawFlmMjY3dkVA
        set private-key "-----BEGIN OPENSSH PRIVATE KEY-----
b3BlbnNzaC1rZXktdjEAAAAACmFlczI1Ni1jdHIAAAAGYmNyeXB0AAAAGAAAABCkZjB7MT
udasaPL8OETxEzAAAAEAAAAAEAAAEXAAAAB3NzaC1yc2EAAAADAQABAAABAQDPqMmbHTFI
9V30utFOum7Zlyj0ziCGELJNDXOAnZDAJlNVdBY1V0m/NbrNLVGU+q/Ai8B2w8/seYTxRJ
La2C3OJvC80wUogKXcZ/2NURx1pt/aZe8cgMnstUwhMobRs6SogYb4XK1ax2W7+cgOUjwk
yzLROBR9PEZtbip3VMQwN7T4eso2tWIrb2fT2kz8iJu9sBNYD9yZI3gsPZTzDja1XOeja6
zWO9xRMQ2pSDk2sNd9eTFfPFwYj8yJKb/qpUmmE4/ltGsDnannlahsGF6KtDkbJitjz+Ha
zIFH9f6Px31W8N2oZ3x8FBGUKJYgyFjGsRu2J6r+MoePvBubBLHRAAADwE1yNVgEkFe1Ib
SfGGR0Fbt7KcCZnYJi07lMutiSV2/42DiNzBU0Pf8gQl/D49GGJ7JLay24nMGmEIT6s0xD
Zl/Ok9LjAXNfFoGxaCra6enK6c53vIAckL5gCD0btUe1TQRofavRq9HKKbHxXpKw4rt4YJ
AuV/aIQVnE/n+NqhRgSNrtNo/5YhDzYsUK/O3ArbzHAEamJ2pEJMmm1m0XB27t0uu7ups9
6ryaVOuPgLw7eiSzz1xquMQ54LbnwPTYSuAjk0hsch1K5tnuwE5ojI9SbFY8fwbJskytpe
+78wImRrRTAOoljWmxM7s2zURdx1MPnWW4b49FQD8SBO3c/KFVQli4touHggEbWwVLLQy5
r9vyOB0CTbuRFKoQlm/VmQDGZmIPC8q0014Dnbs0Gc+9EJ7FXXZ8RckyZ1/E8+7ukfFdM0
AzrpLYYX6UWByTflZuVKYKjlXXnaZD8MqXMSBNjzmDIC3nHgMGiAVK8vSu/Aeo3gmMl6lQ
5kYxbOhk5g/HIvPQBFLhElkX/Dza1npatV9dH3FZnYEODq3xKWrJRTGI9LqlhTri/oZlRG
WqDtPzlBuwNyv0wLikjo0WbhwxLXoGqLMfCr2yGgGU8PmLlgEAkUVlW800ILS60+8aVUwq
Y0RB+Xxxej4SG2/vXnrprlILPHkMje/rHmjF9hZHl0uTbf6yeQz+UV4q+XwVw5BkfFsnda
HfE3Va+4a034bJpPoTKwzA+yPC8kF0v1zVcpvZ6sICJdXCsJgwDbwfpSvXeagae31u9yoQ
24Zo7qzy0MIkB3ZA2q40fYYeCAXi46iT/1Unn/R9FxnOam6LyupjwXyzh3rnzH/BQQd8ni
5mZBDO+iMLQDDNyjltjyeZvJrZ2kVcSsrq7o8ubxQQhuPUAEG63BCjgcM4ZrJfKlQTMjRP
byz+k3Mzr7HJbLp1+yq+Xg9D+G+AMTaM9UDQGQWR2nv29mkClBBIF8onYNyGYfb6PupIzy
P5KcycXj29sgBycEKivjbapxE7OVaqarZWqmHX1RnzV6XqY8PRYjOv/SJQHiOCWvm7oUxC
HaSeX4A/XP+cQeZJ6/dJQzWJaJLl9yho/7zfEILm2x9DQ9es59pecujAflSIdihzkXJjYl
3qUorHtQVAKhMpFRRIlPgg1/DpSJqry4JeL37rfO7wHpWyQIOZOeo0k3Md3m20grbcfs1g
RKr6RcBZd/NmICZLt8LoZxYHDbGcXR28mzSzH2NuoE8hP5zOP9hPEx76aclF89AXhuKy/P
VKRAYj4w==
-----END OPENSSH PRIVATE KEY-----
"
        set public-key "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDPqMmbHTFI9V30utFOum7Zlyj0ziCGELJNDXOAnZDAJlNVdBY1V0m/NbrNLVGU+q/Ai8B2w8/seYTxRJLa2C3OJvC80wUogKXcZ/2NURx1pt/aZe8cgMnstUwhMobRs6SogYb4XK1ax2W7+cgOUjwkyzLROBR9PEZtbip3VMQwN7T4eso2tWIrb2fT2kz8iJu9sBNYD9yZI3gsPZTzDja1XOeja6zWO9xRMQ2pSDk2sNd9eTFfPFwYj8yJKb/qpUmmE4/ltGsDnannlahsGF6KtDkbJitjz+HazIFH9f6Px31W8N2oZ3x8FBGUKJYgyFjGsRu2J6r+MoePvBubBLHR"
        set source built-in
    next
    edit "Fortinet_SSH_DSA1024"
        set password ENC CXxBr+sUkaCraREbs/u/nvmJVBN4FXs8rqT/sWW5HV4WGsPnGHZkAs8upST6mn5U4/yKu3BextwV2+XNF+2E59y7G0SBTYPNthGWVlxEtrndjrBzT+2HhC1asmwPHIAelP2x32hJJiievnb2Y3MDLCt1czzJ6N13K7tZluuF+BEOuihmqin/00q3ESqDvtPDIcSEollmMjY3dkVA
        set private-key "-----BEGIN OPENSSH PRIVATE KEY-----
b3BlbnNzaC1rZXktdjEAAAAACmFlczI1Ni1jdHIAAAAGYmNyeXB0AAAAGAAAABBCn0szLx
oEmwyQD8LmuJtZAAAAEAAAAAEAAAGxAAAAB3NzaC1kc3MAAACBANU+AQjEGvtxLHLWBmMa
rcqIPdGjEAVRTIP3m+pRkJA93S+kaAr283smP2xr5xCvq9e467ggXjx6tVZ0CqVYIFkHtj
dOoDTo6JmVbVr9WyglnpXOA7Repqm+DmuUFRwklY+NYvyLMcZhPAT6ec7Ro1j51ewbWRA/
fI0gh0ZvhGLXAAAAFQDxxLWM0wqSW4e2T+/4Po4sMuddiQAAAIByHkFJ7jB2LPmXqsX7Xv
9A7EG4MgtP9dlEvcTc/woc8lYaE0P8JqYOEV4xUYp6AnurXZdxCOAYBjTg0bfsLX7cKzy8
CMO8H1P0kYhNtsyzXOC9iIbLuBYeo6A/NMCmr+d0vM5WaoNe1GqclFsUwwQizUVY6CQIhm
yIIsXhklDo6AAAAIB0Hxq9mikzQ+Mz/1qE3bztExb4wUuS3gwUxXzeZME5+GEsklFuY1l8
p7EPk7ZVcpXBDa35+ma4exNPRjn0oY1LSxeDVs3mqgE1GprLHqSmH2aOJfw8vnZHk0urGe
+r0xrQhs8xiqYG2lxgRXQU9Slm0pPiYv+teDulWqLwf0J25QAAAeBC+Uz6mtaR8VxUw7W0
/2JSYeVRrxsDI/DO2QDwu20uDr/bSbBZVQwxTcC2Mm2STm7Fje6sjDIzqOGg8yOiTiyWgk
CI27QDh2K5H0cW2BYb3Z7HA1C5ws/cLgCQlnd+Lf9ZMe/2blo0uAFO0IQE3KB+ClVfSBnZ
Me4WQseQu1GKoWuB4s0Yzgd9mfqX1XFREq808X/KPQ/JzLFBuX4t6SHOyIo3pdQpwWaud0
pEMRVbwMaWG9rJxUS8sXyNSRKOmAVyqqeC+yRAFh2ydJi2U8t/1MA9aveOCGj7mfuKXSIb
tKbmWCPVuENkhdPY5EUSw8iMaErd+ASz4ZeZ530cclAW6Pe6uq07xGmcYbNkm04T+xTbC3
q9OWt/fgUoncuiuioOGB1ZYbX4tmjvx3UZsY/o+S0pMWKr2QsNAOrKDQPWT/EhEhm/Eyt1
PkDp8XiNkxbiOHRRhIelgtBDSCzTbT+BItrPvfJw4LmarJANKFNY6gUUbie4+PS+fXqWoZ
Nqx0kGZ9ysga1s4I3Wjlf/797mL3zhnu7tbwlkdrcfRY3v0DM76odAenWiBYBSgHNqZJdR
wh7Zosfy6gQIBGMrGbtuPJxhl8fmPKJ9cWM5QGMmYnIXvLxq1aBnqjgvCxwE/Ks=
-----END OPENSSH PRIVATE KEY-----
"
        set public-key "ssh-dss AAAAB3NzaC1kc3MAAACBANU+AQjEGvtxLHLWBmMarcqIPdGjEAVRTIP3m+pRkJA93S+kaAr283smP2xr5xCvq9e467ggXjx6tVZ0CqVYIFkHtjdOoDTo6JmVbVr9WyglnpXOA7Repqm+DmuUFRwklY+NYvyLMcZhPAT6ec7Ro1j51ewbWRA/fI0gh0ZvhGLXAAAAFQDxxLWM0wqSW4e2T+/4Po4sMuddiQAAAIByHkFJ7jB2LPmXqsX7Xv9A7EG4MgtP9dlEvcTc/woc8lYaE0P8JqYOEV4xUYp6AnurXZdxCOAYBjTg0bfsLX7cKzy8CMO8H1P0kYhNtsyzXOC9iIbLuBYeo6A/NMCmr+d0vM5WaoNe1GqclFsUwwQizUVY6CQIhmyIIsXhklDo6AAAAIB0Hxq9mikzQ+Mz/1qE3bztExb4wUuS3gwUxXzeZME5+GEsklFuY1l8p7EPk7ZVcpXBDa35+ma4exNPRjn0oY1LSxeDVs3mqgE1GprLHqSmH2aOJfw8vnZHk0urGe+r0xrQhs8xiqYG2lxgRXQU9Slm0pPiYv+teDulWqLwf0J25Q=="
        set source built-in
    next
    edit "Fortinet_SSH_ECDSA256"
        set password ENC EMe2UWxzhU81BK2polMv5E51ZPagbVrd8oBRkKf/9TpgHSYK89mc6ca4xw3suZXe/XYBag+AttB2ETACjmhBrtd1el+eZub0siiv5Ij27nr4zImVu0yTJx1MOxhL5WX906zflel0rBo5uFLMePVCRX3G7Ez3TdSVRnbatm42iEIYSEVYXo5FDEnYx/+npquk6D/PHVlmMjY3dkVA
        set private-key "-----BEGIN OPENSSH PRIVATE KEY-----
b3BlbnNzaC1rZXktdjEAAAAACmFlczI1Ni1jdHIAAAAGYmNyeXB0AAAAGAAAABB9VItKCa
pOC4qp8uHgakb/AAAAEAAAAAEAAABoAAAAE2VjZHNhLXNoYTItbmlzdHAyNTYAAAAIbmlz
dHAyNTYAAABBBPleWv5vXa+aDx9OPIirVumgTag6KKV6fIODSAh7qq75v+htQIpuqGBuqL
TNobm3TuLCWd+B7m7koW7WoVxfyeAAAACgxwkbfDeituUlYdeyhmBh1P1jLhX3eD0YqCbq
xxsjivne8u1cLO+VGpaYwjZ2QAxTrO7lUG/w9gvrww2QJXb3AJGNGLbuqOAq3t9e7oDVII
Nmi2Sb9BHjbG0wzhwmeJIl53ntqRy1so0KkuPUFfn/xXyRovJv+r/fhCwuGy5lHsz/rxuD
0lULRIwSC5c4jyyI4vDtytQ7RF6QcRh6iHT35g==
-----END OPENSSH PRIVATE KEY-----
"
        set public-key "ecdsa-sha2-nistp256 AAAAE2VjZHNhLXNoYTItbmlzdHAyNTYAAAAIbmlzdHAyNTYAAABBBPleWv5vXa+aDx9OPIirVumgTag6KKV6fIODSAh7qq75v+htQIpuqGBuqLTNobm3TuLCWd+B7m7koW7WoVxfyeA="
        set source built-in
    next
    edit "Fortinet_SSH_ECDSA384"
        set password ENC CdH3oOOigQEjXmFhVR2yaxR1b6ZxnEnFQAtNUZYkFEeJPEKUH8Ykf3VqpNvcyq6B5oOo7gPtjEwrQxfeHm2Rt2PdbrjQIbueNti9eLFMKsDBEbe8LErbDtLkw9NqQgSA7g26GkKUiQNBpy9UOnpnEqBFN/iGIiWEc1kTzJt68vUaO8tg84ZLP0HyYgJBNzRk8l+gxllmMjY3dkVA
        set private-key "-----BEGIN OPENSSH PRIVATE KEY-----
b3BlbnNzaC1rZXktdjEAAAAACmFlczI1Ni1jdHIAAAAGYmNyeXB0AAAAGAAAABC5LhkFob
SIzcFwyW/6x173AAAAEAAAAAEAAACIAAAAE2VjZHNhLXNoYTItbmlzdHAzODQAAAAIbmlz
dHAzODQAAABhBDnxtCerj1wT/LrxIBjhwyQuKI+SF2qfZutYOLw5ZaJI3OYwjSWCFkOlwJ
Abc8J+6uFRjL3GYps00KiD+a4uXqzmg7JHXoNspdHY2Hv1rd16lcZMjcnpunzGDMzfZWgP
VAAAANBHmc5RwTTfb1HYelgMymnLYtdtFnsOK0FuyW7UvO0lldxA6jBORZTqLloAW7xXUF
EOmj/JDxTW/BMVBj7aERufNtjB0R5rrNaZxZf5K65r647xQiu6+bfe/UTHZRg7XiRUyUob
4T9NJFQjDjdmvfZgwFcUprJsd53lC9kvEEG6fnFVxlC1KNyEoZMzfM8lFWWcTG4rpuerUv
CSbuzw8MvXwNwLm4REouvvxP7Le5XeqlONqHQf5xhb+LE/tKLL5adbW4aZKuf9LzLxaisA
K7P5
-----END OPENSSH PRIVATE KEY-----
"
        set public-key "ecdsa-sha2-nistp384 AAAAE2VjZHNhLXNoYTItbmlzdHAzODQAAAAIbmlzdHAzODQAAABhBDnxtCerj1wT/LrxIBjhwyQuKI+SF2qfZutYOLw5ZaJI3OYwjSWCFkOlwJAbc8J+6uFRjL3GYps00KiD+a4uXqzmg7JHXoNspdHY2Hv1rd16lcZMjcnpunzGDMzfZWgPVA=="
        set source built-in
    next
    edit "Fortinet_SSH_ECDSA521"
        set password ENC Gpcn5sPKPjQYgiJ8RNHIwDAz2+mUUOXj31FXZpAI3FZ/JqjuZv2bH+nBHFhrRVJDjybmQFim/PepySyTiW77gCwDaaheDmVerbRM6PiWPFtyG3RVnrUVTIh2NvVFB4iVHJDLdWgctfUbZYAGW5FStpl59PwqF1Y4m2YKws9zh3zxthR3g6D9ty7kIwmpNwyXoy41AFlmMjY3dkVA
        set private-key "-----BEGIN OPENSSH PRIVATE KEY-----
b3BlbnNzaC1rZXktdjEAAAAACmFlczI1Ni1jdHIAAAAGYmNyeXB0AAAAGAAAABCjPBSTQe
dBCLx3E9UCIMQYAAAAEAAAAAEAAACsAAAAE2VjZHNhLXNoYTItbmlzdHA1MjEAAAAIbmlz
dHA1MjEAAACFBAG0J/mtyfM4scIBCvZRa0bEIxnSKOQQ5fPKaEYaFRipV8X0D+rKxq8zRO
WL5LziFZe1mamYRLUc6NiS54b3L/eDMwBL3Mi64oklyiLhK2LWxCNQ6bzWjnjm+q50i7T2
TY6Izz4JujY63d2XtzGjmIGyrBnK3GArczHh2gd++qPeGt5f7AAAAQArDjramSHLzpfklD
Wa93DboluAj9KQzioh6zkTMju+3ZvxjfAh+NNhuPK6WKtSgdnpnaf/YCKMw2iruC5WffBQ
MdpwreW3WdRXNy/x6t66NqGBjH2yE7yvzlzNQxy4CmFNUmL/5rjh5LyNi381akQ0Jwb7Bc
tQZeFnnyye6ENYMeX9dKW+IfL6dzdCNyRaomYzJkz6gtoACWeeyjJK1v8mnQoWiESPNn8N
zqqu7R1i7PoPHV6dskUW6WyZyMxS4ZD5ndRlqIzBkbUqOdMv91BWWV+m+dBL9JOSjYfaKK
PBYf6cEBMUHGgKsYCdPj3rffz507f6VgjEz8oTqc/xrlz9
-----END OPENSSH PRIVATE KEY-----
"
        set public-key "ecdsa-sha2-nistp521 AAAAE2VjZHNhLXNoYTItbmlzdHA1MjEAAAAIbmlzdHA1MjEAAACFBAG0J/mtyfM4scIBCvZRa0bEIxnSKOQQ5fPKaEYaFRipV8X0D+rKxq8zROWL5LziFZe1mamYRLUc6NiS54b3L/eDMwBL3Mi64oklyiLhK2LWxCNQ6bzWjnjm+q50i7T2TY6Izz4JujY63d2XtzGjmIGyrBnK3GArczHh2gd++qPeGt5f7A=="
        set source built-in
    next
    edit "Fortinet_SSH_ED25519"
        set password ENC yVi5nnbxz1Q30WxCebSf8kRQsiRhYRAROhIRUzXJPYmW0En3GMyaPQg5H1KzbCu7UW0/Ha/Ooi1vD/iqeXmExKJ8q2pi91233sHkKIQd9i2wa1Qdlx63KLGlz5PMk/SdbOf5Tl1hMIUeg1DkjoRZcJEmITVG2/pHd4Ez7bTLDqxY8vrNj4QBGJ373jajN7zX+tLHF1lmMjY3dkVA
        set private-key "-----BEGIN OPENSSH PRIVATE KEY-----
b3BlbnNzaC1rZXktdjEAAAAACmFlczI1Ni1jdHIAAAAGYmNyeXB0AAAAGAAAABC1WpXUN0
jKWLg7KdC6CGieAAAAEAAAAAEAAAAzAAAAC3NzaC1lZDI1NTE5AAAAIPKjMja0wQaZtHaL
A3NbJujgbaJWhbXpdb91Sqbw5AntAAAAkNdu8SuNSYfTk4RaDbGhYqQfiqq6hbs7G4ABW2
o+U96bV5WjAwYJos+xHuYc4EipfVZ0yCk+7G1TbSB7MpLLSd7yE9i5AtGZoc2IjVq6RjYD
+JKW+YPD5K6f4UQhIpDiZ89484C+S8g5UJu9GICLlTcFB7nU1C4eL4n4FUFQ7nzVrMuWv6
RFXwcoufWodM4xaQ==
-----END OPENSSH PRIVATE KEY-----
"
        set public-key "ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIPKjMja0wQaZtHaLA3NbJujgbaJWhbXpdb91Sqbw5Ant"
        set source built-in
    next
end
config firewall ssh local-ca
    edit "Fortinet_SSH_CA"
        set password ENC E2ldS2NrJIk/D31HNicZiRyzddA5sVghdnXX0aB9RLCF9ypU2wiX29DY2KvNOGVqd/s6x/SbaRgllNGxSygoK020OovfwBcuzfmvxGj5rd1wiwJevDQYmp7y59Uf+YvPGPUAxg20bKhy7jGT3er7Mzxwoee/W0XELkb2m+/4lMnPjFfgkOpG4KyFCgg1jQLtho2uyVlmMjY3dkVA
        set private-key "-----BEGIN OPENSSH PRIVATE KEY-----
b3BlbnNzaC1rZXktdjEAAAAACmFlczI1Ni1jdHIAAAAGYmNyeXB0AAAAGAAAABCsnWkyVO
cVapAc5FJVAkmMAAAAEAAAAAEAAAEXAAAAB3NzaC1yc2EAAAADAQABAAABAQCxt/tNRxRL
aPE4PFmxrhEmXkhkEfep3dg2gGnPczG1XG+byNN9LAdix3pkCuKdd4fV1mYB8EJ/Zaw4wu
srb+Q4oZb/haZXeaUp+AgtXoVZ8QsH7Hdey2PtYrLlkukxeWCcsnF3bZf0EBf7gsgJTSSo
iadXspjlL2Aj0Fsi2BytK4o7PudkmCn7oX25WgBkdGA/8ok8nu2w6kwdIHUJazymjkZnBo
dFu5wPA3FY8axvPETib70I/SrmLHdJ7wlQB/6vJQbSy1rxj7MMO6uJXcc+d9kHNh8ag1NX
dwZJG8+q976bsyuSQxNfudUSRnhvlZg76vFIa3tGBecXL3awTrcnAAADwOh2XBuBZNooVp
9a5RrfQWCaOAs/ZUFRErUqcN1SwzGFZL6HNbfitVvnMtNj1eXMczhoIrVVYEVGdXYp2GXb
mN6nxxow+6YolXxRX4Ei8RvWK0DExG+ipkTZhMkuxHo9lN3wWeagaI/j/j6KuaeyKUtXZD
CuAtHUIZSRjimdWzp1NMsis+oUv21K1Gm0dEgzxKcIvidzjVFI0Vi9QY4gyyeieeOmdl9J
2vUJnFHyXOitqE4EFm0RoCsfVHvX3dhex6AhK9nV3Yl9TPrRU92oYzEHYFKu62Sv+Vu120
ABNPgGX9UMP1R9WMDcBbko4vJJwsVOiKULcgQCnjkuHO1FfqAZNb0JX6pejNwBDGyIWITk
D0zRDrXCq3yMm5lLR8HhCqmRoqK0u11PlEumDqP0mddY81Oc3NLvwDsGKcDaN9B2VarWaI
FbgXl6y/1GvvR5Cqiucj8wnx926+Qe3aIn30gdyJVrhjBmHba8SnG2USiP92kDuxY89bFs
3zb3rqDi1gkx2exbqImD8KHdcOWSHX5SO+xK05mSjH30tqbC4QbM+9mLBNpB0Tt5wSvfBK
AWz2yvPoUDJPcRJUA9VcLl0i+u5VEMFo7WFP8AiyY078nhMcE0jF9SL8I2iafV1GLqIaoy
pvfUuhiMy3U97bTeV603cD3mKO86A1rHxqOPRFIkXYgMBVTCFPSSJ7aAVg3Lru3gc5FgPN
3GKHhw9h1Juk81+LWs2r2G4jDT54OM8qodunXD6Bwgz/ecN01ssRZQ4x5qopE8Y81EAmS8
3kZDEOxi0784bIhtXKM8FXn4thOCiLsz+rW6tbtyGreWHqCTh9kPIKsDWFrCjWXYx5VLLJ
/7lvm9u4Yy5PbJG436VrK/MiWMmIHi9/RgwRfFPpWYX37zw55vZuwI0eb9+6kPe5B2eG3L
fkAMgLnCeab3IPgPbwoMoOhhwD0HcW/V3qL7ihuWOweD0JbO3F0p4RxvDI4+iE4CQfWpsC
VYsRnKiOYgrvl/W+B6v0mXEiaCuXRZKLy8VrfCNLTUunkqjGLKWp+MPHQsATLWelpg3zPE
ZAFTWrF7fLWT3Sg1TXsQ1jINY39bXQj6686xRHyj3pCFMA0pVHJ2BctluzQ2FWl15UCJak
SVyPsRhey1Irmk0sWQI0mtkDVtplyJaZ8DJMNkqjmD5V0Affht37MF1lz1zQehetYVmdnD
6koT5CYQB+BmNDyi9sL8Xdq/b34jBm4mzpJhGb15Gm+DScrcQGobD7wty++afMmhw1GB8u
/kRKweqw==
-----END OPENSSH PRIVATE KEY-----
"
        set public-key "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQCxt/tNRxRLaPE4PFmxrhEmXkhkEfep3dg2gGnPczG1XG+byNN9LAdix3pkCuKdd4fV1mYB8EJ/Zaw4wusrb+Q4oZb/haZXeaUp+AgtXoVZ8QsH7Hdey2PtYrLlkukxeWCcsnF3bZf0EBf7gsgJTSSoiadXspjlL2Aj0Fsi2BytK4o7PudkmCn7oX25WgBkdGA/8ok8nu2w6kwdIHUJazymjkZnBodFu5wPA3FY8axvPETib70I/SrmLHdJ7wlQB/6vJQbSy1rxj7MMO6uJXcc+d9kHNh8ag1NXdwZJG8+q976bsyuSQxNfudUSRnhvlZg76vFIa3tGBecXL3awTrcn"
        set source built-in
    next
    edit "Fortinet_SSH_CA_Untrusted"
        set password ENC e52ZC8uCeKEBYJvhtjNJgFIlVT2RXB3eptT5ktxORciFV/wTSPJNn3qnXmbtO1izu1xf6xTnItK061napAIvf5pzfAutxQxl6zuVtjzxMahzZxByTGdKFijjWys9O/7RwKBIrciv/fhfad5sqqnEa6JE/Pm52fGJ5uTIdgi6QlFky5OEKgBwh7TfoM09Lw/XMbHxLVlmMjY3dkVA
        set private-key "-----BEGIN OPENSSH PRIVATE KEY-----
b3BlbnNzaC1rZXktdjEAAAAACmFlczI1Ni1jdHIAAAAGYmNyeXB0AAAAGAAAABAm4HJs0c
oxlU4E6ggnJ+gWAAAAEAAAAAEAAAEXAAAAB3NzaC1yc2EAAAADAQABAAABAQClpRRlw6xv
Z35zJZdylC6Uq6p36X2Gvm/MXJgEh9eLFZpvt0lTT0ndh8vMrJGAct0nO8cH3LOrJpVKEg
JVZvw2LvoqZCEo+K2lGHTkJbAqQIrkF/bHnJLxTbZeBP6HghEqX9YrS0aFiR9eUZIvMmij
2Z35wKjLe2f7bJdvb9xhJ3RYHC6rjhEvYOv213/56p1pwVG+sEP0tSDqUP81X5AC/swKSZ
YMrM19Lv03QfCXywCg3dFF9+IW13htC20/d4HFj4wTUnGYxCeXF0sytbPNVifsQ2FyMMux
GRhfKvTqCI03m3V9Zkt25rXz52gewQ+C2VfCiF9k84xteLzK1YaZAAADwPPM8ZX0kNAY2W
0mE/pBX3KtJpFw697FsidSXygntP4Uq/DwQzEyay4oNadaHohtAHSgdiLZWhehdpBpuAgR
uCjF3vGUfzXrWr2CM8PuKj+lDn6RsRCuw+E5HOxfkfc3TOcndryT0Lz8Y/7Eb2igInhcMj
j8F55zLX9qdqylap5W4ASyAmzO3v20/Gs0fBAC/0dw9g72QEyVDBjMd9Oeq0D+B8Kz0Sd/
GJm2Gr3qTBgTy4e5BR2m5KpyvLtgpj3CsbA7nX/o4VzNSTnDmo8rpcmfrteZZIB3RsOmOZ
s7AvsCaHiAT5Lxd6/C+4jG6UQjITvPTS+/y/6JTK14LfxaM14T+oJfViJVtmvF/XUWX0J9
KkrCptuWWR26z0iyBgS0XWg/UKOPos1at7oCV2wXsvRX+C6EemVEY+Um4Nzce+fwp7xLY2
NPMa5mUHYYEG9kDTATVDknbqkLsDiH0bruPJ0UCjWid8RKLk+naXBm4rX3Nhim25u9fuYK
clQyySl0X6dtOJ1uxfftd5aflOr5L0nifCIBo/9JnRfMfsJuLXelNdCkMpcaL8Zpy2PVYT
l9V2/nTvNOh6GH2izDP7g0zCn98AoW6HivWbBXG3X5a+BGGWK8heRU95ybFbnzwgCZmR/P
fwJgzwTk5CBU9wKRB/QFM/x93bLUKq3FSfEjM9ZJDFiI3E053pos6ScQWEHj7RSiAqg8Fb
l3+HGb/acb7dWN6hLtVBLA/qhol/Maxii4ksGF70eAma6G5MOKFmWTkZr4SuXSPFVnqVKs
8128xIOdbauthSz57j52Im38gJk8swPtsHdIWZe3+AcxKrhTffmysIiGUdq4RTEi/jBkco
Nsv2eMp3UNldMQyEQm+o1qfcj4whWwWdtIJTsq9wp1aaqaVtaIRkKQS+iiAJOOB7EEQuIA
cnquh3rQp9znUh+3XXGSDwxg81EwzEPvXbmnMyPos8O0PFvGtqI/eV9s4kBAmI8rIHeJQs
UpQY2D+wCt81jVj2uGpvZsf6X/+FLqR1OHq4xIuZivr/YBKpUfd/wfLqj5ys4i3eNMC4uj
pbgYLesSuBc7uhgyWu2n/x24vKZ4fyGjfdN1e7Id4heul5Bky+Pq+91yXvAxrkg372h2hB
AzfRgwPPO98ldoBuP5W/LLoTiaSz+RUqEdEu8ALXdFpr3KnhTVAB4pmz56Jt65SnbDVW3W
KxWYOoeP4aS05dWJGEaYGpT44GPp0ZbTp/dMqEIA0etAYs6TvIWgLAHAqKv+Jbar5w6HtB
H4LZeRkw==
-----END OPENSSH PRIVATE KEY-----
"
        set public-key "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQClpRRlw6xvZ35zJZdylC6Uq6p36X2Gvm/MXJgEh9eLFZpvt0lTT0ndh8vMrJGAct0nO8cH3LOrJpVKEgJVZvw2LvoqZCEo+K2lGHTkJbAqQIrkF/bHnJLxTbZeBP6HghEqX9YrS0aFiR9eUZIvMmij2Z35wKjLe2f7bJdvb9xhJ3RYHC6rjhEvYOv213/56p1pwVG+sEP0tSDqUP81X5AC/swKSZYMrM19Lv03QfCXywCg3dFF9+IW13htC20/d4HFj4wTUnGYxCeXF0sytbPNVifsQ2FyMMuxGRhfKvTqCI03m3V9Zkt25rXz52gewQ+C2VfCiF9k84xteLzK1YaZ"
        set source built-in
    next
end
config firewall ssh setting
    set caname "Fortinet_SSH_CA"
    set untrusted-caname "Fortinet_SSH_CA_Untrusted"
    set hostkey-rsa2048 "Fortinet_SSH_RSA2048"
    set hostkey-dsa1024 "Fortinet_SSH_DSA1024"
    set hostkey-ecdsa256 "Fortinet_SSH_ECDSA256"
    set hostkey-ecdsa384 "Fortinet_SSH_ECDSA384"
    set hostkey-ecdsa521 "Fortinet_SSH_ECDSA521"
    set hostkey-ed25519 "Fortinet_SSH_ED25519"
end
config firewall profile-protocol-options
    edit "default"
        set comment "All default services."
        config http
            set ports 80
            unset options
            unset post-lang
        end
        config ftp
            set ports 21
            set options splice
        end
        config imap
            set ports 143
            set options fragmail
        end
        config mapi
            set ports 135
            set options fragmail
        end
        config pop3
            set ports 110
            set options fragmail
        end
        config smtp
            set ports 25
            set options fragmail splice
        end
        config nntp
            set ports 119
            set options splice
        end
        config ssh
            unset options
        end
        config dns
            set ports 53
        end
        config cifs
            set ports 445
            unset options
        end
    next
end
config firewall ssl-ssh-profile
    edit "certificate-inspection"
        set comment "Read-only SSL handshake inspection profile."
        config https
            set ports 443
            set status certificate-inspection
            set quic inspect
        end
        config ftps
            set status disable
        end
        config imaps
            set status disable
        end
        config pop3s
            set status disable
        end
        config smtps
            set status disable
        end
        config ssh
            set ports 22
            set status disable
        end
        config dot
            set status disable
            set quic inspect
        end
    next
    edit "deep-inspection"
        set comment "Read-only deep inspection profile."
        config https
            set ports 443
            set status deep-inspection
            set quic inspect
        end
        config ftps
            set ports 990
            set status deep-inspection
        end
        config imaps
            set ports 993
            set status deep-inspection
        end
        config pop3s
            set ports 995
            set status deep-inspection
        end
        config smtps
            set ports 465
            set status deep-inspection
        end
        config ssh
            set ports 22
            set status disable
        end
        config dot
            set status disable
            set quic inspect
        end
        config ssl-exempt
            edit 1
                set fortiguard-category 31
            next
            edit 2
                set fortiguard-category 33
            next
            edit 3
                set type wildcard-fqdn
                set wildcard-fqdn "adobe"
            next
            edit 4
                set type wildcard-fqdn
                set wildcard-fqdn "Adobe Login"
            next
            edit 5
                set type wildcard-fqdn
                set wildcard-fqdn "android"
            next
            edit 6
                set type wildcard-fqdn
                set wildcard-fqdn "apple"
            next
            edit 7
                set type wildcard-fqdn
                set wildcard-fqdn "appstore"
            next
            edit 8
                set type wildcard-fqdn
                set wildcard-fqdn "auth.gfx.ms"
            next
            edit 9
                set type wildcard-fqdn
                set wildcard-fqdn "citrix"
            next
            edit 10
                set type wildcard-fqdn
                set wildcard-fqdn "dropbox.com"
            next
            edit 11
                set type wildcard-fqdn
                set wildcard-fqdn "eease"
            next
            edit 12
                set type wildcard-fqdn
                set wildcard-fqdn "firefox update server"
            next
            edit 13
                set type wildcard-fqdn
                set wildcard-fqdn "fortinet"
            next
            edit 14
                set type wildcard-fqdn
                set wildcard-fqdn "googleapis.com"
            next
            edit 15
                set type wildcard-fqdn
                set wildcard-fqdn "google-drive"
            next
            edit 16
                set type wildcard-fqdn
                set wildcard-fqdn "google-play2"
            next
            edit 17
                set type wildcard-fqdn
                set wildcard-fqdn "google-play3"
            next
            edit 18
                set type wildcard-fqdn
                set wildcard-fqdn "Gotomeeting"
            next
            edit 19
                set type wildcard-fqdn
                set wildcard-fqdn "icloud"
            next
            edit 20
                set type wildcard-fqdn
                set wildcard-fqdn "itunes"
            next
            edit 21
                set type wildcard-fqdn
                set wildcard-fqdn "microsoft"
            next
            edit 22
                set type wildcard-fqdn
                set wildcard-fqdn "skype"
            next
            edit 23
                set type wildcard-fqdn
                set wildcard-fqdn "softwareupdate.vmware.com"
            next
            edit 24
                set type wildcard-fqdn
                set wildcard-fqdn "verisign"
            next
            edit 25
                set type wildcard-fqdn
                set wildcard-fqdn "Windows update 2"
            next
            edit 26
                set type wildcard-fqdn
                set wildcard-fqdn "live.com"
            next
            edit 27
                set type wildcard-fqdn
                set wildcard-fqdn "google-play"
            next
            edit 28
                set type wildcard-fqdn
                set wildcard-fqdn "update.microsoft.com"
            next
            edit 29
                set type wildcard-fqdn
                set wildcard-fqdn "swscan.apple.com"
            next
            edit 30
                set type wildcard-fqdn
                set wildcard-fqdn "autoupdate.opera.com"
            next
            edit 31
                set type wildcard-fqdn
                set wildcard-fqdn "cdn-apple"
            next
            edit 32
                set type wildcard-fqdn
                set wildcard-fqdn "mzstatic-apple"
            next
        end
    next
    edit "custom-deep-inspection"
        set comment "Customizable deep inspection profile."
        config https
            set ports 443
            set status deep-inspection
            set quic inspect
        end
        config ftps
            set ports 990
            set status deep-inspection
        end
        config imaps
            set ports 993
            set status deep-inspection
        end
        config pop3s
            set ports 995
            set status deep-inspection
        end
        config smtps
            set ports 465
            set status deep-inspection
        end
        config ssh
            set ports 22
            set status disable
        end
        config dot
            set status disable
            set quic inspect
        end
        config ssl-exempt
            edit 1
                set fortiguard-category 31
            next
            edit 2
                set fortiguard-category 33
            next
            edit 3
                set type wildcard-fqdn
                set wildcard-fqdn "adobe"
            next
            edit 4
                set type wildcard-fqdn
                set wildcard-fqdn "Adobe Login"
            next
            edit 5
                set type wildcard-fqdn
                set wildcard-fqdn "android"
            next
            edit 6
                set type wildcard-fqdn
                set wildcard-fqdn "apple"
            next
            edit 7
                set type wildcard-fqdn
                set wildcard-fqdn "appstore"
            next
            edit 8
                set type wildcard-fqdn
                set wildcard-fqdn "auth.gfx.ms"
            next
            edit 9
                set type wildcard-fqdn
                set wildcard-fqdn "citrix"
            next
            edit 10
                set type wildcard-fqdn
                set wildcard-fqdn "dropbox.com"
            next
            edit 11
                set type wildcard-fqdn
                set wildcard-fqdn "eease"
            next
            edit 12
                set type wildcard-fqdn
                set wildcard-fqdn "firefox update server"
            next
            edit 13
                set type wildcard-fqdn
                set wildcard-fqdn "fortinet"
            next
            edit 14
                set type wildcard-fqdn
                set wildcard-fqdn "googleapis.com"
            next
            edit 15
                set type wildcard-fqdn
                set wildcard-fqdn "google-drive"
            next
            edit 16
                set type wildcard-fqdn
                set wildcard-fqdn "google-play2"
            next
            edit 17
                set type wildcard-fqdn
                set wildcard-fqdn "google-play3"
            next
            edit 18
                set type wildcard-fqdn
                set wildcard-fqdn "Gotomeeting"
            next
            edit 19
                set type wildcard-fqdn
                set wildcard-fqdn "icloud"
            next
            edit 20
                set type wildcard-fqdn
                set wildcard-fqdn "itunes"
            next
            edit 21
                set type wildcard-fqdn
                set wildcard-fqdn "microsoft"
            next
            edit 22
                set type wildcard-fqdn
                set wildcard-fqdn "skype"
            next
            edit 23
                set type wildcard-fqdn
                set wildcard-fqdn "softwareupdate.vmware.com"
            next
            edit 24
                set type wildcard-fqdn
                set wildcard-fqdn "verisign"
            next
            edit 25
                set type wildcard-fqdn
                set wildcard-fqdn "Windows update 2"
            next
            edit 26
                set type wildcard-fqdn
                set wildcard-fqdn "live.com"
            next
            edit 27
                set type wildcard-fqdn
                set wildcard-fqdn "google-play"
            next
            edit 28
                set type wildcard-fqdn
                set wildcard-fqdn "update.microsoft.com"
            next
            edit 29
                set type wildcard-fqdn
                set wildcard-fqdn "swscan.apple.com"
            next
            edit 30
                set type wildcard-fqdn
                set wildcard-fqdn "autoupdate.opera.com"
            next
            edit 31
                set type wildcard-fqdn
                set wildcard-fqdn "cdn-apple"
            next
            edit 32
                set type wildcard-fqdn
                set wildcard-fqdn "mzstatic-apple"
            next
        end
    next
    edit "no-inspection"
        set comment "Read-only profile that does no inspection."
        config https
            set status disable
            set quic bypass
        end
        config ftps
            set status disable
        end
        config imaps
            set status disable
        end
        config pop3s
            set status disable
        end
        config smtps
            set status disable
        end
        config ssh
            set ports 22
            set status disable
        end
        config dot
            set status disable
            set quic inspect
        end
    next
end
config waf profile
    edit "default"
        config signature
            config main-class 100000000
                set action block
                set severity high
            end
            config main-class 20000000
            end
            config main-class 30000000
                set status enable
                set action block
                set severity high
            end
            config main-class 40000000
            end
            config main-class 50000000
                set status enable
                set action block
                set severity high
            end
            config main-class 60000000
            end
            config main-class 70000000
                set status enable
                set action block
                set severity high
            end
            config main-class 80000000
                set status enable
                set severity low
            end
            config main-class 110000000
                set status enable
                set severity high
            end
            config main-class 90000000
                set status enable
                set action block
                set severity high
            end
            set disabled-signature 80080005 80200001 60030001 60120001 80080003 90410001 90410002
        end
        config constraint
            config header-length
                set status enable
                set log enable
                set severity low
            end
            config content-length
                set status enable
                set log enable
                set severity low
            end
            config param-length
                set status enable
                set log enable
                set severity low
            end
            config line-length
                set status enable
                set log enable
                set severity low
            end
            config url-param-length
                set status enable
                set log enable
                set severity low
            end
            config version
                set log enable
            end
            config method
                set action block
                set log enable
            end
            config hostname
                set action block
                set log enable
            end
            config malformed
                set log enable
            end
            config max-cookie
                set status enable
                set log enable
                set severity low
            end
            config max-header-line
                set status enable
                set log enable
                set severity low
            end
            config max-url-param
                set status enable
                set log enable
                set severity low
            end
            config max-range-segment
                set status enable
                set log enable
                set severity high
            end
        end
    next
end
config casb saas-application
end
config casb user-activity
end
config casb profile
    edit "default"
    next
end
config firewall policy
    edit 1
        set name "any"
        set uuid 34b1d318-42a9-51f0-2af8-563df4c79390
        set srcintf "port5"
        set dstintf "port3"
        set action accept
        set srcaddr "all"
        set dstaddr "all"
        set schedule "always"
        set service "ALL"
    next
end
config firewall multicast-policy
    edit 1
        set uuid 026786b4-42a9-51f0-f06b-d33307eef9a2
        set srcintf "any"
        set dstintf "any"
        set srcaddr "all"
        set dstaddr "all"
        set logtraffic disable
    next
end
config firewall sniffer
    edit 1
        set uuid 01f0ce48-42a9-51f0-0d24-7d2239e079fc
        set logtraffic all
        set ipv6 enable
        set non-ip enable
        set interface "s1"
        set application-list-status enable
        set application-list "sniffer-profile"
        set ips-sensor-status enable
        set ips-sensor "sniffer-profile"
        set av-profile-status enable
        set av-profile "sniffer-profile"
        set webfilter-profile-status enable
        set webfilter-profile "sniffer-profile"
        set file-filter-profile-status enable
        set file-filter-profile "sniffer-profile"
    next
    edit 2
        set uuid 01f118b2-42a9-51f0-d9ec-a53866479f32
        set logtraffic all
        set ipv6 enable
        set non-ip enable
        set interface "s2"
        set application-list-status enable
        set application-list "sniffer-profile"
        set ips-sensor-status enable
        set ips-sensor "sniffer-profile"
        set av-profile-status enable
        set av-profile "sniffer-profile"
        set webfilter-profile-status enable
        set webfilter-profile "sniffer-profile"
        set file-filter-profile-status enable
        set file-filter-profile "sniffer-profile"
    next
end
config switch-controller initial-config template
    edit "_default"
        set vlanid 1
    next
    edit "quarantine"
        set vlanid 4093
        set dhcp-server enable
    next
    edit "rspan"
        set vlanid 4092
        set dhcp-server enable
    next
    edit "voice"
        set vlanid 4091
    next
    edit "video"
        set vlanid 4090
    next
    edit "onboarding"
        set vlanid 4089
    next
    edit "nac_segment"
        set vlanid 4088
        set dhcp-server enable
    next
end
config switch-controller ptp profile
    edit "default"
    next
end
config switch-controller ptp interface-policy
    edit "default"
    next
end
config switch-controller remote-log
    edit "syslogd"
    next
    edit "syslogd2"
    next
end
config wireless-controller setting
    set darrp-optimize-schedules "default-darrp-optimize"
end
config wireless-controller arrp-profile
    edit "arrp-default"
    next
end
config wireless-controller wids-profile
    edit "default"
        set comment "Default WIDS profile."
        set ap-scan enable
        set wireless-bridge enable
        set deauth-broadcast enable
        set null-ssid-probe-resp enable
        set long-duration-attack enable
        set invalid-mac-oui enable
        set weak-wep-iv enable
        set auth-frame-flood enable
        set assoc-frame-flood enable
        set spoofed-deauth enable
        set asleap-attack enable
        set eapol-start-flood enable
        set eapol-logoff-flood enable
        set eapol-succ-flood enable
        set eapol-fail-flood enable
        set eapol-pre-succ-flood enable
        set eapol-pre-fail-flood enable
    next
    edit "default-wids-apscan-enabled"
        set ap-scan enable
    next
end
config wireless-controller ble-profile
    edit "fortiap-discovery"
        set advertising ibeacon eddystone-uid eddystone-url
        set ibeacon-uuid "wtp-uuid"
    next
end
config router static
    edit 1
        set gateway 10.51.212.1
    next
end
config router static6
    edit 1
    next
end
