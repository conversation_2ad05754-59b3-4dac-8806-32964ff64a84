# FortiGate转换器Makefile

# 国际化相关目标
.PHONY: i18n-check i18n-validate i18n-performance i18n-clean i18n-update

i18n-check:
	@echo "🔍 运行国际化检查..."
	cd engine && python tools/ci_i18n_check.py .

i18n-validate:
	@echo "🔍 运行国际化验证..."
	cd engine && python tools/i18n_validator.py

i18n-performance:
	@echo "🔍 运行国际化性能测试..."
	cd engine && python tools/i18n_performance_test.py

i18n-scan:
	@echo "🔍 扫描硬编码字符串..."
	cd engine && python tools/hardcoded_string_scanner.py

i18n-generate:
	@echo "🔧 生成国际化内容..."
	cd engine && python tools/i18n_content_generator.py

i18n-clean:
	@echo "🧹 清理翻译文件..."
	cd engine && python tools/translation_cleaner.py

i18n-update:
	@echo "🔄 更新国际化系统..."
	$(MAKE) i18n-scan
	$(MAKE) i18n-generate
	$(MAKE) i18n-clean
	$(MAKE) i18n-validate

i18n-install:
	@echo "🔧 安装国际化钩子..."
	cd engine && python tools/install_i18n_hooks.py

i18n-test:
	@echo "🧪 运行国际化测试..."
	cd engine && python -m pytest tests/test_i18n_system.py -v

i18n-all:
	@echo "🚀 运行完整国际化检查..."
	$(MAKE) i18n-check
	$(MAKE) i18n-validate
	$(MAKE) i18n-performance
	$(MAKE) i18n-test
