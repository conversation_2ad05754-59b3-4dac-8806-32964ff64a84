# Docker构建优化说明

## 背景

为了优化Docker构建流程，提高更新应用代码的效率，我们将Docker镜像拆分为两部分：

1. **基础镜像（Base Image）**：包含所有系统依赖、Python环境和常用库，很少更新
2. **应用镜像（App Image）**：基于基础镜像，只包含应用程序代码和配置，频繁更新

这种方式的优势：

- 应用更新时只需重新构建应用镜像，构建速度快（通常只需几秒钟）
- 推送和拉取应用镜像的网络流量小，降低部署时间
- 当系统依赖需要更新时，只需更新基础镜像，所有应用镜像会自动获得更新

## 进程管理

应用镜像使用 Supervisor 进行进程管理，提供以下优势：

- 自动监控并重启崩溃的进程
- 集中管理日志输出
- 提供更好的进程控制和监控机制
- 支持多个进程同时运行，如主应用服务和健康检查监控

Supervisor 配置文件位于 `supervisor.conf`，定义了以下进程：

- `configtrans`: 主应用服务进程
- `monitor`: 定期执行健康检查
- `process_monitor`: 监听进程状态，自动重启失败的进程

## 目录结构

```
├── Dockerfile          # 原始Dockerfile（保留用于兼容）
├── Dockerfile.base     # 基础镜像的Dockerfile
├── Dockerfile.app      # 应用镜像的Dockerfile
├── build.sh            # 构建脚本
├── push-aliyun.sh      # 阿里云专用推送脚本
├── supervisor.conf     # Supervisor配置文件
└── README.docker.md    # 本说明文档
```

## 阿里云仓库

本项目默认使用阿里云容器镜像服务：

- 仓库地址: `registry.cn-hangzhou.aliyuncs.com/secloud`
- 应用镜像: `registry.cn-hangzhou.aliyuncs.com/secloud/config-converter:2.9.1`
- 基础镜像: `registry.cn-hangzhou.aliyuncs.com/secloud/config-converter-base:2.9.1`

### 使用阿里云专用脚本

为了简化阿里云镜像的构建和推送流程，提供了专用脚本：

```bash
chmod +x push-aliyun.sh
./push-aliyun.sh
```

按照提示选择操作模式即可。

### 阿里云镜像登录

在推送镜像前，需要先登录到阿里云容器镜像服务：

```bash
docker login --username=您的阿里云账号 registry.cn-hangzhou.aliyuncs.com
```

然后按提示输入密码（在阿里云容器镜像服务控制台可以找到）。

## 构建镜像

### 设置权限

首先，确保构建脚本有执行权限：

```bash
chmod +x build.sh
```

### 构建基础镜像

基础镜像包含所有系统依赖和环境设置，构建时间较长，但只需偶尔更新：

```bash
./build.sh --base-only --aliyun
```

### 构建应用镜像

应用镜像只包含应用代码和配置，构建速度快，适合频繁更新：

```bash
./build.sh --app-only --aliyun
```

### 同时构建两个镜像

如果需要同时构建基础镜像和应用镜像：

```bash
./build.sh --aliyun
```

### 构建但不推送

如果只想构建而不推送到远程仓库：

```bash
./build.sh --aliyun --no-push
```

## 部署应用

部署时，拉取并运行阿里云仓库中的镜像：

```bash
docker pull registry.cn-hangzhou.aliyuncs.com/secloud/config-converter:2.9.1
docker run -d -p 9005:9005 registry.cn-hangzhou.aliyuncs.com/secloud/config-converter:2.9.1
```

## 常见问题

### 如何查看supervisor管理的进程状态？

使用以下命令进入容器并查看进程状态：

```bash
docker exec -it 容器ID bash
supervisorctl status
```

### 如何重启特定进程？

使用以下命令重启进程：

```bash
docker exec -it 容器ID supervisorctl restart configtrans
```

### 如何查看进程日志？

Supervisor会将日志保存在容器的`/app/logs/`目录下，您可以通过以下方式查看：

```bash
# 查看主程序日志
docker exec -it 容器ID cat /app/logs/configtrans-service-stdout.log

# 查看健康检查日志
docker exec -it 容器ID cat /app/logs/healthcheck.log
```

或者使用supervisorctl的tail命令：

```bash
docker exec -it 容器ID supervisorctl tail configtrans
```

### 如何切换到使用静态配置文件？

设置环境变量 `USE_DEFAULT_CONFIG=true`：

```bash
docker run -d -p 9005:9005 -e USE_DEFAULT_CONFIG=true registry.cn-hangzhou.aliyuncs.com/secloud/config-converter:2.9.1
```

### 如何自定义环境变量？

可以通过 `-e` 参数设置任意环境变量：

```bash
docker run -d -p 9005:9005 \
  -e DB_HOST=my-db-server \
  -e DB_PORT=3306 \
  -e REDIS_HOST=my-redis \
  registry.cn-hangzhou.aliyuncs.com/secloud/config-converter:2.9.1
```

### 如何挂载数据目录？

通过 `-v` 参数挂载数据目录：

```bash
docker run -d -p 9005:9005 \
  -v /path/to/data:/app/data \
  -v /path/to/logs:/app/logs \
  registry.cn-hangzhou.aliyuncs.com/secloud/config-converter:2.9.1
```

## 开发工作流程

1. 开发新功能或修复错误
2. 构建并测试应用镜像：`./build.sh --app-only --aliyun --no-push`
3. 测试通过后，构建并推送应用镜像：`./build.sh --app-only --aliyun` 或使用 `./push-aliyun.sh`
4. 部署新版本的应用镜像

## 注意事项

- 基础镜像的更新频率应该远低于应用镜像
- 每次对系统依赖或基础环境的修改，都需要重新构建基础镜像
- 对应用代码的修改，只需重新构建应用镜像
- 始终使用版本化的标签 (如 `2.9.1`) 而不是 `latest`，以便回滚

## 最新优化说明

### 镜像分层和大小优化

当前优化后的镜像情况：
- 基础镜像大小：约1.15GB
- 应用镜像大小：约1.26GB
- 应用镜像增量：约110MB（实际传输大小）

虽然应用镜像显示为1.26GB，但由于层共享机制，实际上只有110MB是应用镜像特有的层。这意味着推送、拉取和更新应用时，只会涉及到这110MB的传输，大大提高了部署效率。

### Engine目录简化

最新的`Dockerfile.app`中，我们简化了engine目录的复制方式：

```dockerfile
# 直接复制整个engine目录，简化复制过程
COPY engine/ /app/engine/
```

相比之前选择性复制特定文件和子目录的方式，这种方法：
- 简化了Dockerfile配置
- 减少了漏掉重要文件的风险
- 使维护更加容易，尤其是当engine目录结构发生变化时

### 进程管理优化

- 使用supervisor替代了旧版的watchdog.sh脚本，提供更可靠的进程管理
- 保留了健康检查脚本(healthcheck.sh)，用于全面的服务状态监控
- 配置了多层监控机制，确保服务稳定性和自动恢复能力

### 验证镜像层共享

我们提供了`check-image-size.sh`脚本用于验证镜像大小和层共享情况：

```bash
./check-image-size.sh
```

建议在每次构建完成后运行此脚本，确保镜像层正确共享，特别是在更改Dockerfile或构建流程后。 