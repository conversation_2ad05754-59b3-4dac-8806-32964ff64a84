# 配置转换服务国际化指南

本文档介绍了配置转换服务的国际化实现，支持中文和英文界面。

## 一、概述

配置转换服务的国际化支持通过以下几个部分实现：

1. Python引擎层的国际化支持（engine模块）
2. Go服务层的国际化支持（apis模块）
3. Docker容器的国际化配置

## 二、语言支持

目前支持的语言：

- 中文简体 (zh-CN) - 默认语言
- 英文 (en-US)

> **重要说明**: 整个系统现在统一使用连字符格式的语言代码 (如 `zh-CN`, `en-US`)，不再使用下划线格式 (如 `zh_CN`, `en_US`)。Go和Python组件已经完成统一，兼容这一格式标准。

## 三、语言设置方式

客户端可以通过以下几种方式指定语言：

1. **URL参数**：在API请求中添加`?lang=en-US`或`?lang=zh-CN`
2. **Accept-Language头**：设置HTTP请求头`Accept-Language: en-US`或`Accept-Language: zh-CN`
3. **默认语言**：如果未指定语言，默认使用中文(zh-CN)

优先级顺序：URL参数 > Accept-Language头 > 默认语言

## 四、Python引擎国际化

### 4.1 文件结构

```
engine/
  ├── locales/
  │   ├── zh-CN.json  # 中文翻译文件
  │   └── en-US.json  # 英文翻译文件
  ├── utils/
  │   └── i18n.py     # 国际化工具模块
  └── main.py         # 引擎入口
```

### 4.2 国际化模块使用

```python
from utils.i18n import translate

# 使用翻译函数
message = translate("error_invalid_format", "zh-CN")

# 带参数的翻译
error_msg = translate("format_not_supported", "en-US").format(format="XML")
```

### 4.3 命令行参数

引擎支持通过`--lang`参数指定语言：

```bash
python main.py --mode verify --vendor fortigate --cli config.txt --lang en-US
```

## 五、Go服务国际化

### 5.1 中间件

Go服务使用`middleware/i18n.go`中间件处理语言设置，将解析出的语言代码存储在请求上下文中：

```go
// 获取当前请求的语言设置
language := ctx.Values().GetString("language")
```

### 5.2 控制器国际化

控制器根据语言设置返回不同语言的消息：

```go
errMsg := "系统错误"
if language == "en-US" {
    errMsg = "System error"
}
ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, errMsg))
```

### 5.3 与Python引擎交互

Go服务在调用Python引擎时，通过`--lang`参数传递语言设置：

```go
cmd := exec.Command(
    pythonPath,
    scriptPath,
    "--mode", "verify",
    "--vendor", vendor,
    "--cli", configPath,
    "--log-dir", tempLogDir,
    "--lang", language,
)
```

## 六、Docker容器配置

Docker容器通过以下设置支持国际化：

1. 在`Dockerfile.app`中创建语言文件目录：
   ```dockerfile
   # 确保国际化(i18n)目录存在
   RUN mkdir -p /app/engine/locales
   
   # 设置可执行权限和处理国际化文件
   RUN chmod +x /app/configtrans-service /app/start.sh && \
       if [ -f /app/engine/main.py ]; then chmod +x /app/engine/main.py; fi && \
       find /app/engine -name "*.py" -exec chmod +x {} \; && \
       # 确保国际化文件有正确的权限
       if [ -d /app/engine/locales ]; then chmod -R 755 /app/engine/locales; fi
   ```

2. 设置国际化支持的环境变量：
   ```dockerfile
   ENV LANG=C.UTF-8 \
       LC_ALL=C.UTF-8 \
       PYTHONIOENCODING=utf-8
   ```

## 七、扩展支持新语言

要添加新的语言支持，请按照以下步骤操作：

1. 在`engine/locales/`目录中创建新的语言文件，如`fr-FR.json`
2. 更新`engine/utils/i18n.py`中的`SUPPORTED_LANGUAGES`列表
3. 在Go语言的中间件`middleware/i18n.go`中更新`parseAcceptLanguage`和`normalizeLang`函数

## 八、开发注意事项

1. 所有用户可见的文本应使用国际化函数，不要硬编码
2. 保持翻译文件的一致性和完整性
3. 测试不同语言设置的功能正常工作
4. 确保新添加的功能也支持国际化
5. 始终使用连字符格式的语言代码(如 `zh-CN`, `en-US`)

## 九、故障排除

如果国际化功能不正常，请检查：

1. 请求中是否正确设置了语言参数
2. 语言文件是否存在且格式正确
3. Docker容器中的语言环境变量是否正确
4. Python引擎是否正确接收了语言参数

## 十、相关文件

- `/engine/utils/i18n.py` - Python国际化核心模块
- `/engine/locales/` - 语言文件目录
- `/apis/application/middleware/i18n.go` - Go语言国际化中间件
- `Dockerfile.app` - Docker容器国际化配置 