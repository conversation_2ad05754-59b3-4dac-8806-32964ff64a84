 # NTOS ConfigTrans 配置转换服务

[![Version](https://img.shields.io/badge/version-2.9.1-blue.svg)](https://github.com/your-repo/config-converter)
[![Go Version](https://img.shields.io/badge/go-1.22+-00ADD8.svg)](https://golang.org/)
[![Python Version](https://img.shields.io/badge/python-3.8+-3776ab.svg)](https://www.python.org/)
[![Docker](https://img.shields.io/badge/docker-ready-2496ED.svg)](https://www.docker.com/)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)

NTOS ConfigTrans 是一个企业级的网络设备配置转换平台，专门设计用于将各种厂商的网络设备配置文件转换为标准化的NTOS格式。该服务采用现代化的微服务架构，提供高性能、高可用的配置转换能力。

## 🚀 项目特点

### 核心功能
- **多厂商支持**：专业支持FortiGate防火墙配置转换，架构可扩展至其他厂商
- **新架构v2.0**：采用分层架构设计，提供更好的性能和可维护性
- **智能转换**：基于YANG模型的智能配置解析和转换
- **批量处理**：支持大规模配置文件的批量转换操作
- **容量校验**：🆕 智能检测配置是否超出目标设备容量限制，提供优化建议

### 技术架构
- **后端服务**：基于Go 1.22+构建的高性能RESTful API服务
- **转换引擎**：Python 3.8+实现的专业配置解析和转换引擎
- **容器化部署**：优化的Docker镜像分层设计，支持快速部署和扩展
- **进程管理**：基于Supervisor的进程监控和自动恢复机制

### 企业特性
- **高可用性**：内置健康检查和故障自动恢复机制
- **安全性**：支持JWT认证、RBAC权限控制和文件加密
- **监控能力**：完整的日志记录、性能监控和错误追踪
- **国际化**：支持中文和英文多语言环境
- **可扩展性**：模块化设计，支持插件式功能扩展

## 📋 技术栈与系统要求

### 技术栈

| 组件 | 版本 | 说明 |
|------|------|------|
| **后端语言** | Go 1.22+ | 高性能API服务 |
| **转换引擎** | Python 3.8+ | 配置解析和转换 |
| **数据库** | MySQL 8.0+ | 任务和配置数据存储 |
| **缓存** | Redis 7.0+ | 会话和缓存管理 |
| **容器** | Docker 20.10+ | 容器化部署 |
| **编排** | Docker Compose 2.0+ | 多容器管理 |
| **进程管理** | Supervisor 4.0+ | 进程监控和管理 |
| **YANG处理** | libyang 2.0+ | YANG模型验证 |

### 系统要求

#### 最低配置
- **CPU**: 2核心
- **内存**: 4GB RAM
- **存储**: 10GB可用磁盘空间
- **操作系统**: Linux、macOS、Windows 10+

#### 推荐配置（生产环境）
- **CPU**: 4核心或更多
- **内存**: 8GB RAM或更多
- **存储**: 50GB可用磁盘空间（SSD推荐）
- **网络**: 稳定的网络连接

#### 容器环境要求
- Docker 20.10.0+
- Docker Compose 2.0+ (如使用compose方式部署)
- 容器运行时支持（Docker Engine或Podman）

## 🏗️ 系统架构

### 整体架构

本系统采用现代化的微服务架构设计，具有高度的模块化和可扩展性：

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Client    │    │   API Gateway   │    │  Load Balancer  │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Go API 服务   │    │  Python 转换    │    │   Supervisor    │
│                 │    │     引擎        │    │   进程管理      │
│ • RESTful API   │    │ • 配置解析      │    │ • 健康检查      │
│ • 任务管理      │    │ • YANG验证      │    │ • 自动恢复      │
│ • 用户认证      │    │ • XML生成       │    │ • 日志管理      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   MySQL 数据库  │    │   Redis 缓存    │    │   文件存储      │
│                 │    │                 │    │                 │
│ • 任务数据      │    │ • 会话管理      │    │ • 配置文件      │
│ • 用户信息      │    │ • 任务队列      │    │ • 转换结果      │
│ • 审计日志      │    │ • 性能缓存      │    │ • 日志文件      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 核心组件

#### 1. API服务层 (Go)
- **RESTful API**: 提供标准化的HTTP接口
- **认证授权**: JWT + RBAC权限控制
- **任务调度**: 异步任务管理和监控
- **文件处理**: 安全的文件上传和下载
- **健康检查**: 服务状态监控端点

#### 2. 转换引擎 (Python)
- **新架构v2.0**: 分层设计，支持插件化扩展
- **配置解析**: 多厂商配置文件解析器
- **YANG验证**: 基于libyang的模型验证
- **XML生成**: 标准化的NTOS配置生成
- **错误恢复**: 智能错误处理和回退机制

#### 3. 数据存储层
- **MySQL**: 持久化数据存储（任务、用户、审计）
- **Redis**: 高性能缓存和会话管理
- **文件系统**: 配置文件和转换结果存储

### 容器化架构

系统采用优化的多阶段Docker构建策略：

#### 镜像分层设计
1. **基础镜像 (Base Image)**
   - 系统依赖和运行时环境
   - Python环境和常用库
   - libyang和加密库
   - 大小: ~1.15GB，更新频率低

2. **应用镜像 (App Image)**
   - 应用程序代码和配置
   - 业务逻辑和转换规则
   - 增量大小: ~110MB，频繁更新

#### 架构优势
- **快速部署**: 应用更新只需传输110MB增量数据
- **版本管理**: 独立的基础环境和应用版本控制
- **资源优化**: 镜像层共享，节省存储空间
- **扩展性**: 支持水平扩展和负载均衡

## 🔄 支持的转换类型

### 当前支持

| 源厂商 | 设备类型 | 目标格式 | 支持版本 | 状态 |
|--------|----------|----------|----------|------|
| **Fortinet** | FortiGate防火墙 | NTOS XML | 6.0+ | ✅ 完全支持 |

### 转换能力

#### FortiGate → NTOS 转换特性
- **接口配置**: 物理接口、VLAN子接口、PPPoE配置
- **地址对象**: IP地址、IP范围、地址组
- **服务对象**: TCP/UDP服务、服务组、自定义服务
- **安全策略**: 防火墙规则、NAT策略、时间对象
- **路由配置**: 静态路由、默认路由
- **系统配置**: DNS设置、时区配置、管理接口

#### 支持的地址对象类型
- ✅ **ipmask/subnet**: IP地址/掩码格式 (如 `*********** *************`)
- ✅ **iprange**: IP地址范围 (如 `***********-*************`)
- ❌ **fqdn**: 完全限定域名 (计划支持)
- ❌ **geography**: 地理位置 (不支持)
- ❌ **wildcard**: 通配符地址 (不支持)

### 未来规划

系统架构已为扩展做好准备，计划支持：
- **Cisco ASA**: 防火墙配置转换
- **Juniper SRX**: 安全设备配置转换
- **华为USG**: 防火墙配置转换
- **H3C SecPath**: 安全网关配置转换

## 📚 API 概览

### 核心API端点

| 功能分类 | 端点 | 方法 | 描述 |
|----------|------|------|------|
| **配置验证** | `/api/v1/firewallflextrans/validate` | POST | 验证配置文件格式 |
| **信息提取** | `/api/v1/firewallflextrans/interfaces` | POST | 提取接口信息 |
| **转换任务** | `/api/v1/firewallflextrans/convert` | POST | 创建转换任务 |
| **任务管理** | `/api/v1/firewallflextrans/tasks` | GET | 查询任务列表 |
| **任务状态** | `/api/v1/firewallflextrans/status/{job_id}` | GET | 查询任务状态 |
| **文件下载** | `/api/v1/firewallflextrans/download/{job_id}` | GET | 下载转换结果 |
| **日志查看** | `/api/v1/firewallflextrans/view-log/{job_id}` | GET | 在线查看日志 |
| **转换报告** | `/api/v1/firewallflextrans/report/{job_id}` | GET | 获取转换报告 |
| **健康检查** | `/health` | GET | 服务健康状态 |

### 快速API示例

```bash
# 1. 验证配置文件
curl -X POST "http://localhost:9005/api/v1/firewallflextrans/validate" \
  -F "file=@fortigate-config.conf" \
  -F "vendor=fortigate"

# 2. 创建转换任务
curl -X POST "http://localhost:9005/api/v1/firewallflextrans/convert" \
  -F "file=@fortigate-config.conf" \
  -F "vendor=fortigate" \
  -F "model=z5100s" \
  -F "version=R11" \
  -F "mapping_json={\"port1\":\"Ge0/1\",\"port2\":\"Ge0/2\"}"

# 3. 查询任务状态
curl -X GET "http://localhost:9005/api/v1/firewallflextrans/status/{job_id}"

# 4. 下载转换结果
curl -X GET "http://localhost:9005/api/v1/firewallflextrans/download/{job_id}" -o result.xml
```

> 📖 **详细API文档**: 查看 [APIs README.md](apis/README.md) 获取完整的API参考文档

## 🔍 容量校验功能 (新特性)

### 功能概述

容量校验功能是v2.9.1版本新增的重要特性，能够在配置转换前智能检测FortiGate配置是否超出目标NTOS设备的容量限制，帮助用户：

- ✅ **提前发现问题**：在转换前识别可能导致设备性能问题的配置超限
- 📊 **详细分析报告**：提供使用率、风险级别和影响评估
- 💡 **智能优化建议**：基于配置类别提供具体的优化建议
- 🎯 **设备选型指导**：帮助选择满足需求的合适设备型号

### 支持的设备型号

| 设备型号 | 定位 | 安全策略限制 | NAT策略限制 | 地址对象限制 |
|---------|------|-------------|-------------|-------------|
| **Z3200S** | 中端防火墙 | 3,000条 | 250条 | 1,024个 |
| **Z5100S** | 高端防火墙 | 4,000条 | 300条 | 1,024个 |

### 检查的资源类型

- **网络配置**：DNS服务器、子接口、PPPOE会话
- **路由配置**：静态路由条数
- **策略配置**：安全策略、NAT策略数量
- **对象配置**：地址对象、服务对象、时间对象及其组

### 使用示例

#### 命令行方式

```bash
# 验证配置并进行容量校验
python engine/main.py --mode verify --cli config.conf --vendor fortigate --device-model z3200s

# 获取JSON格式的详细结果
python engine/main.py --mode verify --cli config.conf --vendor fortigate --device-model z3200s --json
```

#### API方式

```bash
# 带容量校验的配置验证
curl -X POST "http://localhost:9005/api/v1/firewallflextrans/validate" \
  -F "file=@fortigate-config.conf" \
  -F "vendor=fortigate" \
  -F "device_model=z3200s"
```

#### 输出示例

```json
{
  "valid": true,
  "success": true,
  "warnings": [
    "容量限制警告：DNS服务器数量(5)超出z3200s设备限制(3)。建议配置不超过3个DNS服务器",
    "容量限制警告：安全策略数量(3500)超出z3200s设备限制(3000)。建议优化安全策略配置或升级到Z5100S型号"
  ],
  "capacity_violations": [
    {
      "resource_type": "dns_servers",
      "current_count": 5,
      "max_limit": 3,
      "usage_rate": "166.7%",
      "risk_level": "critical",
      "suggestion": "建议配置不超过3个DNS服务器",
      "optimization_tips": [
        "优化网络配置",
        "检查配置的必要性"
      ]
    }
  ]
}
```

> 📖 **详细文档**: 查看 [容量校验功能指南](docs/capacity_validation_guide.md) 获取完整的使用说明

## 🚀 快速开始

### 方式一：Docker Compose 一键部署（推荐）

这是最简单的部署方式，包含完整的服务栈：

```bash
# 1. 克隆项目
git clone https://github.com/your-username/config-converter.git
cd config-converter

# 2. 一键启动所有服务
docker-compose up -d

# 3. 验证服务状态
curl http://localhost:9005/health
```

服务启动后可通过以下地址访问：
- **API服务**: http://localhost:9005
- **健康检查**: http://localhost:9005/health
- **API文档**: http://localhost:9005/swagger/index.html (如已启用)

### 方式二：单容器快速部署

如果您已有数据库和Redis服务：

```bash
# 1. 拉取预构建镜像
docker pull registry.cn-hangzhou.aliyuncs.com/secloud/config-converter:2.9.1

# 2. 运行容器
docker run -d \
  --name configtrans-service \
  -p 9005:9005 \
  -e DB_HOST=your-mysql-host \
  -e DB_PASSWORD=your-mysql-password \
  -e REDIS_HOST=your-redis-host \
  -e REDIS_PASSWORD=your-redis-password \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/logs:/app/logs \
  registry.cn-hangzhou.aliyuncs.com/secloud/config-converter:2.9.1
```

### 方式三：本地构建部署

```bash
# 1. 构建镜像
docker build -t configtrans-service:latest .

# 2. 运行容器
docker run -d \
  --name configtrans-service \
  -p 9005:9005 \
  --network host \
  configtrans-service:latest
```

### 🎯 使用示例

#### 完整转换流程示例

```bash
# 1. 验证配置文件
curl -X POST "http://localhost:9005/api/v1/firewallflextrans/validate" \
  -F "file=@fortigate-config.conf" \
  -F "vendor=fortigate"

# 2. 提取接口信息（用于创建映射）
curl -X POST "http://localhost:9005/api/v1/firewallflextrans/interfaces" \
  -F "file=@fortigate-config.conf" \
  -F "vendor=fortigate"

# 3. 创建转换任务
curl -X POST "http://localhost:9005/api/v1/firewallflextrans/convert" \
  -F "file=@fortigate-config.conf" \
  -F "vendor=fortigate" \
  -F "model=z5100s" \
  -F "version=R11" \
  -F "mapping_json={\"port1\":\"Ge0/1\",\"port2\":\"Ge0/2\",\"port3\":\"Ge0/3\"}"

# 4. 查询任务状态（返回job_id）
curl -X GET "http://localhost:9005/api/v1/firewallflextrans/status/{job_id}"

# 5. 下载转换结果
curl -X GET "http://localhost:9005/api/v1/firewallflextrans/download/{job_id}" \
  -o converted-config.xml

# 6. 查看转换日志
curl -X GET "http://localhost:9005/api/v1/firewallflextrans/view-log/{job_id}"

# 7. 获取转换报告
curl -X GET "http://localhost:9005/api/v1/firewallflextrans/report/{job_id}"
```

#### Python SDK 示例

```python
import requests
import json
import time

class ConfigTransClient:
    def __init__(self, base_url="http://localhost:9005"):
        self.base_url = base_url

    def convert_config(self, config_file, mapping, model="z5100s", version="R11"):
        """完整的配置转换流程"""

        # 1. 验证配置文件
        with open(config_file, 'rb') as f:
            response = requests.post(
                f"{self.base_url}/api/v1/firewallflextrans/validate",
                files={'file': f},
                data={'vendor': 'fortigate'}
            )

        if not response.json()['data']['valid']:
            raise Exception("配置文件验证失败")

        # 2. 创建转换任务
        with open(config_file, 'rb') as f:
            response = requests.post(
                f"{self.base_url}/api/v1/firewallflextrans/convert",
                files={'file': f},
                data={
                    'vendor': 'fortigate',
                    'model': model,
                    'version': version,
                    'mapping_json': json.dumps(mapping)
                }
            )

        job_id = response.json()['data']['job_id']

        # 3. 等待转换完成
        while True:
            status_response = requests.get(
                f"{self.base_url}/api/v1/firewallflextrans/status/{job_id}"
            )
            status = status_response.json()['data']['status']

            if status == 1:  # 成功
                break
            elif status == 2:  # 失败
                raise Exception("转换失败")

            time.sleep(5)  # 等待5秒后重试

        # 4. 下载结果
        result_response = requests.get(
            f"{self.base_url}/api/v1/firewallflextrans/download/{job_id}"
        )

        return {
            'job_id': job_id,
            'xml_content': result_response.content,
            'status_info': status_response.json()['data']
        }

# 使用示例
client = ConfigTransClient()
result = client.convert_config(
    config_file="fortigate-config.conf",
    mapping={"port1": "Ge0/1", "port2": "Ge0/2"}
)

print(f"转换完成，任务ID: {result['job_id']}")
with open("converted-config.xml", "wb") as f:
    f.write(result['xml_content'])
```

### 🛠️ 高级部署选项

#### 生产环境部署

```bash
# 使用环境变量文件
cat > .env << EOF
DEBUG=false
LOGLEVEL=info
DB_HOST=prod-mysql-host
DB_PASSWORD=secure-password
REDIS_HOST=prod-redis-host
REDIS_PASSWORD=secure-redis-password
MAX_SIZE=51200
EOF

# 启动生产环境
docker-compose --env-file .env up -d
```

#### 高可用部署

```bash
# 使用Docker Swarm进行集群部署
docker swarm init
docker stack deploy -c docker-compose.yml configtrans-stack
```

#### 性能优化配置

```bash
# 高性能配置
docker run -d \
  --name configtrans-service \
  -p 9005:9005 \
  --memory=8g \
  --cpus=4 \
  -e DB_MAX_OPEN_CONNS=200 \
  -e REDIS_POOL_SIZE=20 \
  configtrans-service:latest
```

## 配置选项

服务支持通过环境变量进行配置。主要配置选项包括：

### 基本配置

- `DEBUG`: 是否启用调试模式 (默认: `false`)
- `LOGLEVEL`: 日志级别 (默认: `info`)
- `HOST`: 服务监听地址 (默认: `0.0.0.0`)
- `PORT`: 服务监听端口 (默认: `9005`)
- `READ_TIMEOUT`: 读取超时时间(秒) (默认: `60`)
- `WRITE_TIMEOUT`: 写入超时时间(秒) (默认: `60`)
- `MAX_SIZE`: 文件上传大小限制(KB) (默认: `10240`)
- `USE_DEFAULT_CONFIG`: 是否使用静态配置文件 (默认: `false`)

### 数据库配置

数据库连接提供了两种配置方式：

**方式一：使用完整连接字符串**

```bash
docker run -d -p 9005:9005 \
  -e DB_CONN="root:mypassword@tcp(***********00:3306)/mydb?parseTime=True&loc=Local" \
  configtrans-service
```

**方式二：使用分解的参数**

- `DB_HOST`: MySQL主机地址 (默认: `************`)
- `DB_PORT`: MySQL端口 (默认: `3306`)
- `DB_USER`: MySQL用户名 (默认: `root`)
- `DB_PASSWORD`: MySQL密码 (默认: `secloud@mysql`)
- `DB_NAME`: 数据库名称 (默认: `configtrans`)
- `DB_PARSE_TIME`: 是否解析时间类型 (默认: `True`)
- `DB_LOC`: 数据库时区 (默认: `Local`)
- `DB_MAX_IDLE_CONNS`: 最大空闲连接数 (默认: `10`)
- `DB_MAX_OPEN_CONNS`: 最大打开连接数 (默认: `100`)
- `DB_CONN_MAX_LIFETIME`: 连接最大生命周期(秒) (默认: `3600`)
- `DB_DEBUG`: 是否开启SQL调试 (默认: `false`)
- `DB_LOG_LEVEL`: 数据库日志级别 (默认: `info`)
- `DB_SLOW_THRESHOLD`: 慢查询阈值(毫秒) (默认: `200`)

### Redis配置

- `REDIS_HOST`: Redis主机地址 (默认: `************`)
- `REDIS_PORT`: Redis端口 (默认: `6379`)
- `REDIS_PASSWORD`: Redis密码 (默认: `secloud@Redis`)
- `REDIS_DATABASE`: Redis数据库索引 (默认: `5`)
- `REDIS_DIAL_TIMEOUT`: 连接超时时间(秒) (默认: `10`)
- `REDIS_READ_TIMEOUT`: 读取超时时间(秒) (默认: `6`)
- `REDIS_WRITE_TIMEOUT`: 写入超时时间(秒) (默认: `6`)
- `REDIS_POOL_SIZE`: 连接池大小 (默认: `10`)
- `REDIS_MIN_IDLE_CONNS`: 最小空闲连接数 (默认: `5`)
- `REDIS_MAX_RETRIES`: 最大重试次数 (默认: `3`)
- `REDIS_RETRY_TIMEOUT`: 重试超时时间(毫秒) (默认: `500`)

### 文件存储配置

- `FILESTORAGE_TEMP`: 临时文件目录 (默认: `/app/data/temp/`)
- `FILESTORAGE_UPLOAD`: 上传文件目录 (默认: `/app/data/uploads/`)
- `CONFIGTRANS_UPLOAD`: 配置文件上传目录 (默认: `/app/data/uploads/`)
- `CONFIGTRANS_TEMPDIR`: 配置转换临时目录 (默认: `/app/data/temp/`)
- `CONFIGTRANS_MAPPINGBASEDIR`: 映射文件目录 (默认: `/app/data/mappings/`)

### 引擎配置

- `CONFIGTRANS_PYTHONPATH`: Python解释器路径 (默认: `python`)
- `CONFIGTRANS_ENGINEPATH`: 转换引擎路径 (默认: `/app/engine`)
- `CONFIGTRANS_FORTIGATE_MAPPINGFILE`: 飞塔接口映射文件 (默认: `interface_mapping.json`)

### 国际化配置

- `LANG`: 系统语言环境 (默认: `C.UTF-8`)
- `LC_ALL`: 区域设置 (默认: `C.UTF-8`)
- `PYTHONIOENCODING`: Python I/O编码 (默认: `utf-8`)

## 高级用法

### 配置自定义

除了使用环境变量，还可以通过以下方式配置系统：

#### 方法一：挂载自定义配置文件

您可以创建自定义的配置文件，并在启动容器时挂载使用：

```bash
docker run -d -p 9005:9005 -v /path/to/your/custom-application.yml:/app/application.yml configtrans-service
```

#### 方法二：结合环境变量和配置文件

您可以同时使用环境变量和配置文件，环境变量的设置会覆盖配置文件中的值：

```bash
docker run -d -p 9005:9005 \
  -v /path/to/your/custom-application.yml:/app/application.yml \
  -e DB_PASSWORD=override_password \
  -e REDIS_HOST=override_redis_host \
  configtrans-service
```

#### 方法三：使用静态配置文件

如果您想使用静态配置文件而不进行环境变量替换，可以设置`USE_DEFAULT_CONFIG`环境变量：

```bash
docker run -d -p 9005:9005 -e USE_DEFAULT_CONFIG=true configtrans-service
```

### 数据持久化

为了持久化存储上传和转换的配置文件，建议挂载以下目录：

```bash
docker run -d -p 9005:9005 \
  -v /path/to/data/uploads:/app/data/uploads \
  -v /path/to/data/output:/app/data/output \
  -v /path/to/data/mappings:/app/data/mappings \
  -v /path/to/logs:/app/logs \
  configtrans-service
```

### 使用supervisor管理进程

查看进程状态：

```bash
docker exec -it 容器ID supervisorctl status
```

重启特定进程：

```bash
docker exec -it 容器ID supervisorctl restart configtrans
```

查看进程日志：

```bash
docker exec -it 容器ID supervisorctl tail configtrans
```

## 健康监控与故障恢复

系统内置了全面的健康监控和故障恢复机制，确保服务高可用性：

### 健康检查

服务通过以下方式提供健康状态监控：

1. **Docker健康检查**：每30秒自动检查服务状态
2. **健康检查端点**：`/health`或`/healthz` API端点
3. **健康检查脚本**：`healthcheck.sh`可用于手动或自动化监控
4. **定期监控进程**：通过supervisor管理的`monitor`进程，定期执行健康检查

健康检查内容包括：

- 进程存活状态
- 内存和CPU使用情况
- API响应状态
- 日志更新状态

```bash
# 手动运行健康检查
docker exec configtrans-service /app/healthcheck.sh
```

### 故障自动恢复

系统使用supervisor的进程监控机制实现故障自动恢复：

1. **进程崩溃检测**：监控主进程状态
2. **事件监听器**：当进程退出时自动触发健康检查
3. **智能重启策略**：根据健康检查结果决定是否重启服务
4. **日志记录**：详细记录故障和恢复过程

Supervisor配置：

- 进程自动重启：当configtrans进程意外退出时
- 进程状态监听器：监控PROCESS_STATE_EXITED和PROCESS_STATE_FATAL事件
- 启动重试次数：最多5次
- 启动等待时间：10秒

## 部署到生产环境

### 部署到阿里云容器服务

本项目提供了阿里云容器镜像服务的上传脚本和指南：

- 使用push-aliyun.sh脚本推送镜像到阿里云容器镜像服务

```bash
chmod +x push-aliyun.sh
./push-aliyun.sh
```

### 性能优化

- **内存配置**：对于大型配置文件处理，建议增加容器可用内存

  ```bash
  docker run -d -p 9005:9005 --memory=4g configtrans-service
  ```

- **CPU 限制**：设置 CPU 使用限制

  ```bash
  docker run -d -p 9005:9005 --cpus=2 configtrans-service
  ```

- **多进程模式**：对于高负载场景，使用supervisor配置多worker进程

### 镜像层优化

当前优化后的镜像情况：

- 基础镜像大小：约1.15GB
- 应用镜像大小：约1.26GB
- 应用镜像增量：约110MB（实际传输大小）

虽然应用镜像显示为1.26GB，但由于层共享机制，实际上只有110MB是应用镜像特有的层。这意味着推送、拉取和更新应用时，只会涉及到这110MB的传输，大大提高了部署效率。

## 项目结构

```
.
├── apis/                    # Go API接口代码
│   └── rbac_model.conf      # RBAC权限模型配置
├── bin/                     # 预编译的Go可执行文件
│   └── configtrans-service  # 主服务可执行文件
├── engine/                  # Python转换引擎
│   ├── main.py              # 转换引擎入口
│   ├── convert.py           # 核心转换逻辑
│   ├── extract.py           # 接口信息提取
│   ├── verify.py            # 配置验证功能
│   ├── parsers/             # 各厂商配置解析器
│   ├── generators/          # 输出格式生成器
│   ├── utils/               # 工具函数
│   └── locales/             # 国际化资源文件
├── data/                    # 数据目录（挂载到容器）
│   ├── uploads/             # 上传文件存储
│   ├── temp/                # 临时文件
│   ├── output/              # 输出文件
│   └── mappings/            # 映射配置文件
├── logs/                    # 日志目录
├── Dockerfile               # 兼容模式Dockerfile
├── Dockerfile.base          # 基础镜像构建文件
├── Dockerfile.app           # 应用镜像构建文件
├── libyang.Dockerfile       # libyang库构建文件
├── docker-application.yml   # Docker环境默认配置
├── docker-application.yml.template # 配置模板（含环境变量）
├── docker-compose.yml       # Docker Compose配置
├── requirements.txt         # Python依赖 (lxml, pyyaml)
├── start.sh                 # 容器启动脚本
├── healthcheck.sh           # 健康检查脚本
├── supervisor.conf          # Supervisor配置文件
├── run-simple.sh            # 单容器运行脚本
├── run-docker-compose.sh    # Compose运行脚本
├── build.sh                 # 构建脚本
├── push-aliyun.sh           # 阿里云推送脚本
└── check-image-size.sh      # 镜像大小检查脚本
```

## 故障排除

### 常见问题

1. **无法连接数据库或 Redis**

   检查 host 网络是否可达，以及配置文件中的连接信息是否正确。
   验证环境变量是否正确设置：

   ```bash
   docker inspect <container_id> | grep -A 20 "Env"
   ```

2. **服务启动失败**

   检查日志以获取详细错误信息：

   ```bash
   docker logs <container_id>
   ```

   或者查看supervisor管理的日志：

   ```bash
   docker exec -it <container_id> supervisorctl tail configtrans
   ```

3. **端口冲突**

   确保宿主机上的 9005 端口（或您指定的端口）没有被其他服务占用。
   您可以更改端口：

   ```bash
   docker run -d -p 9006:9005 configtrans-service
   ```

4. **Python引擎崩溃**

   检查Python错误日志并确保依赖库正确安装：

   ```bash
   docker exec <container_id> pip list
   docker exec <container_id> cat /app/logs/engine-error.log
   ```

5. **配置文件解析失败**

   确保上传的配置文件格式正确，检查转换日志：

   ```bash
   docker exec <container_id> cat /app/logs/transform.log
   ```

6. **Supervisor进程管理问题**

   检查supervisor状态和日志：

   ```bash
   docker exec <container_id> supervisorctl status
   docker exec <container_id> cat /app/logs/supervisord.log
   ```

### 日志查看

```bash
# 查看容器日志
docker logs <container_id>

# 持续查看日志
docker logs -f <container_id>

# 查看最后100行日志
docker logs --tail 100 <container_id>

# 查看特定日志文件
docker exec <container_id> cat /app/logs/configtrans-service-stdout.log

# 使用supervisor查看进程日志
docker exec <container_id> supervisorctl tail configtrans
```

### 调试模式

启用调试模式获取更详细的日志：

```bash
docker run -d -p 9005:9005 -e DEBUG=true -e LOGLEVEL=debug configtrans-service
```

### 健康状态检查

检查服务的健康状态：

```bash
# 使用Docker健康检查
docker inspect --format='{{.State.Health.Status}}' <container_id>

# 使用健康检查脚本
docker exec <container_id> /app/healthcheck.sh

# 使用HTTP健康检查端点
curl -i http://localhost:9005/health
```

## 访问API

服务启动后，可通过以下地址访问API：

- API根地址: `http://localhost:9005/api/`
- 健康检查: `http://localhost:9005/health`
- Swagger文档: `http://localhost:9005/swagger/index.html` (如已启用)

### API使用示例

使用curl上传配置文件进行转换：

```bash
curl -X POST "http://localhost:9005/api/v1/convert" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@/path/to/fortinet-config.conf" \
  -F "vendor=fortinet" \
  -F "model=standard"
```

## 本地开发指南

### 环境要求

- Go 1.22+
- Python 3.8+
- MySQL 5.7+
- Redis 6.0+
- libyang (用于YANG模型处理)

### Python开发环境设置

```bash
# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate     # Windows

# 安装依赖
pip install -r requirements.txt
pip install -r engine/requirements.txt
```

### 构建步骤

1. 编译 Go 程序：

```bash
cd /path/to/project/apis
go build -o ../bin/configtrans-service -ldflags "-X 'main.BuildName=NTOS配置转换工具' -X 'main.BuildVersion=1.0.0' -X 'main.BuildTime=$(date)'" main.go
```

2. 构建 Docker 镜像：

方法一：使用单一Dockerfile构建（兼容模式）

```bash
cd /path/to/project
docker build -t configtrans-service .
```

方法二：使用优化的分层构建（推荐）

```bash
# 构建基础镜像
./build.sh --base-only

# 构建应用镜像
./build.sh --app-only
```

### 本地测试

```bash
# 直接运行Go服务
cd /path/to/project
./bin/configtrans-service

# 使用默认配置文件
./bin/configtrans-service -config=./application.yml

# 单独测试Python转换引擎
cd /path/to/project/engine
python main.py --mode=verify --cli=/path/to/config.conf --vendor=fortigate
```

## 安全建议

1. **默认凭证修改**：在生产环境中务必修改默认的数据库和Redis密码
2. **网络隔离**：建议在专用网络中部署，避免直接暴露到公网
3. **文件检查**：实施上传文件的安全扫描和验证机制
4. **API保护**：考虑添加API认证机制，如JWT或API密钥

## Docker镜像优化

为了优化Docker构建流程，提高更新应用代码的效率，我们将Docker镜像拆分为两部分：

1. **基础镜像（Base Image）**：包含所有系统依赖、Python环境和常用库，很少更新
2. **应用镜像（App Image）**：基于基础镜像，只包含应用程序代码和配置，频繁更新

这种优化方式具有以下优势：

- **分层架构**：基础镜像和应用镜像之间通过Docker镜像层共享机制实现高效存储
- **快速构建**：应用镜像构建速度快（通常只需几秒钟），因为只包含应用代码
- **高效部署**：推送和拉取应用镜像时，只传输变化的部分（通常100MB左右），大幅降低网络传输量
- **集中管理**：通过Supervisor进行进程管理，提供自动监控和故障恢复功能

### 镜像大小和层共享

当前优化后的镜像情况：

- 基础镜像大小：约1.15GB
- 应用镜像大小：约1.26GB
- 应用镜像增量：约110MB（实际传输大小）

需要注意：虽然应用镜像显示为1.26GB，但由于层共享机制，实际上只有110MB是应用镜像特有的层。

更多Docker镜像优化的详细信息，请参考[README.docker.md](README.docker.md)文件。

## 国际化支持

该项目支持多语言国际化，目前支持中文和英文界面。相关文件：

- 国际化资源文件：`engine/locales/`目录
- 国际化配置说明：[README.i18n.md](README.i18n.md)

### 切换语言

设置以下环境变量来切换语言：

```bash
# 运行时指定语言
docker run -d -p 9005:9005 -e LANG=en_US.UTF-8 configtrans-service
```

## 许可

[此处添加项目许可信息]
