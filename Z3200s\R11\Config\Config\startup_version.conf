[{"name": "root", "version": 38294, "ts": *************}, {"name": "physical", "version": 38294, "ts": *************}, {"name": "sub_interface", "version": 38294, "ts": *************}, {"name": "routing", "version": 38294, "ts": *************}, {"name": "dhcp", "version": 38294, "ts": *************}, {"name": "security_zone", "version": 38294, "ts": *************}, {"name": "auth", "version": 38294, "ts": *************}, {"name": "devicename", "version": 38294, "ts": *************}, {"name": "discovery", "version": 38294, "ts": *************}, {"name": "timezone", "version": 38294, "ts": *************}, {"name": "security-policy", "version": 38294, "ts": *************}, {"name": "network-obj", "version": 38294, "ts": *************}, {"name": "appid", "version": 38294, "ts": *************}, {"name": "service-obj", "version": 38294, "ts": *************}, {"name": "time-range", "version": 38294, "ts": *************}, {"name": "ips-config", "version": 38294, "ts": *************}, {"name": "anti-virus", "version": 38294, "ts": *************}, {"name": "url-filter", "version": 38294, "ts": *************}, {"name": "url-category", "version": 38294, "ts": *************}, {"name": "security-defend", "version": 38294, "ts": *************}, {"name": "nat", "version": 38294, "ts": *************}, {"name": "threat-intelligence", "version": 38294, "ts": *************}, {"name": "mac-block", "version": 38294, "ts": *************}, {"name": "user-experience", "version": 38294, "ts": *************}, {"name": "mllb", "version": 38294, "ts": *************}]