[{"name": "root", "version": 50897, "ts": *************}, {"name": "physical", "version": 50898, "ts": *************}, {"name": "sub_interface", "version": 50898, "ts": *************}, {"name": "routing", "version": 50897, "ts": *************}, {"name": "dhcp", "version": 50898, "ts": *************}, {"name": "security_zone", "version": 50898, "ts": *************}, {"name": "auth", "version": 50898, "ts": *************}, {"name": "devicename", "version": 50897, "ts": *************}, {"name": "discovery", "version": 50897, "ts": *************}, {"name": "timezone", "version": 50898, "ts": *************}, {"name": "security-policy", "version": 50897, "ts": *************}, {"name": "network-obj", "version": 50897, "ts": *************}, {"name": "appid", "version": 50899, "ts": *************}, {"name": "service-obj", "version": 50898, "ts": *************}, {"name": "time-range", "version": 50898, "ts": *************}, {"name": "ips-config", "version": 50898, "ts": *************}, {"name": "anti-virus", "version": 50897, "ts": *************}, {"name": "url-filter", "version": 50898, "ts": *************}, {"name": "url-category", "version": 50898, "ts": *************}, {"name": "security-defend", "version": 50898, "ts": 1751523924868}, {"name": "threat-intelligence", "version": 50898, "ts": *************}, {"name": "mac-block", "version": 50898, "ts": 1751523924869}, {"name": "user-experience", "version": 50898, "ts": 1751523924888}]