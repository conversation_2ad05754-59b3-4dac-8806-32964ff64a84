module frr-route-types {
  yang-version 1.1;
  namespace "http://frrouting.org/yang/route-types";
  prefix frr-route-types;

  organization
    "Free Range Routing";
  contact
    "FRR Users List:       <mailto:<EMAIL>>
     FRR Development List: <mailto:<EMAIL>>";
  description
    "This module defines typedefs for route types.";

  revision 2018-03-28 {
    description
      "Initial revision.";
  }

  typedef frr-route-types-v4 {
    type enumeration {
      enum kernel {
        value 1;
      }
      enum connected {
        value 2;
      }
      enum static {
        value 3;
      }
      enum rip {
        value 4;
      }
      enum ospf {
        value 6;
      }
      enum isis {
        value 8;
      }
      enum bgp {
        value 9;
      }
      enum eigrp {
        value 11;
      }
      enum nhrp {
        value 12;
      }
      enum table {
        value 15;
      }
      enum vnc {
        value 17;
      }
      enum babel {
        value 22;
      }
      enum sharp {
        value 23;
      }
      enum openfabric {
        value 26;
      }
    }
  }

  typedef frr-route-types-v6 {
    type enumeration {
      enum kernel {
        value 1;
      }
      enum connected {
        value 2;
      }
      enum static {
        value 3;
      }
      enum ripng {
        value 5;
      }
      enum ospf6 {
        value 7;
      }
      enum isis {
        value 8;
      }
      enum bgp {
        value 9;
      }
      enum nhrp {
        value 12;
      }
      enum table {
        value 15;
      }
      enum vnc {
        value 17;
      }
      enum babel {
        value 22;
      }
      enum sharp {
        value 23;
      }
      enum openfabric {
        value 26;
      }
    }
  }
}
