module ntos-anti-virus {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:anti-virus";
  prefix ntos-anti-virus;
  
  import ntos {
    prefix ntos;
  }
  import ntos-system {
    prefix ntos-system;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-api {
    prefix ntos-api;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel: 4008-111-000
    E-mail: <EMAIL>";
  description
    "Ruijie NTOS anti-virus module.";
    
  revision 2023-04-23 {
    description
      "Mandatory node template is now removed.";
    reference
      "";
  }

  typedef scan-mode {
    type enumeration {
      enum deep {
        description
          "Deep scan mode.";
      }
      enum quick {
        description
          "Quick scan mode.";
      }
    }
    description
      "The scan mode of anti-virus.";
  }

  typedef action-type {
    type enumeration {
      enum alert {
        description
          "Alert action.";
      }
      enum block {
        description
          "Block action.";
      }
    }
    description
      "Action type when a virus is detected.";
  }

  typedef flow-direction {
    type enumeration {
      enum to-client {
        description
          "Downstream flow.";
      }
      enum to-server {
        description
          "Upstream flow.";
      }
      enum both {
        description
          "Both upstream and downstream flow.";
      }
    }
    description
      "The direction type of flow.";
  }
  typedef hash-string {
    type string {
      length "32|40";
      pattern
        '[0-9a-fA-F]*';
    }
    description
      "The hash-string type represents a hash string.";
  }
  grouping reference-detail {
    container security-policy {
      description
        "The reference list of the security policy.";
      leaf-list id {
        type uint32;
        description
          "The id of the security policy.";
      }
    }
    container sim-security-policy {
      description
        "The reference list of the simulative security policy.";
      leaf-list id {
        type uint32;
        description
          "The id of the simulative security policy";
      }
    }
  }

  grouping template-detail {
    description
      "The detail of template.";
    leaf name {
      type ntos-types:ntos-obj-name-type;
      description
        "The name of the template.";
    }
    leaf description {
      type ntos-types:ntos-obj-description-type;
      description
        "The description of the template.";
    }
    leaf scan-mode {
      type scan-mode;
      default "quick";
      description
        "The scan mode of anti-virus.";
    }
    container protocols {
      list protocol {
        key "name";
        leaf name {
          type string;
          description
            "The name of the protocol.";
        }
        leaf direction {
          type flow-direction;
          description
            "The direction of the flow.";
        }
      }    
    }
    container app-exception {
      list app {
        description
          "The name of the application.";
        ntos-ext:nc-cli-one-liner;
        key "name";
        leaf name {
          type ntos-types:ntos-obj-name-type;
          ntos-ext:nc-cli-no-name;
        }
      }
    }
    // Choose to use virus exception or hash exception based on scan mode
    container virus-exception {
      description
        "Configuration of virus exceptions.";
      leaf-list virus-id {
        type uint32;
        description
          "The id of the virus.";
        }
    }
    container hash-exception {
      description
        "Configuration of hash exceptions.";
      leaf-list hash {
        type hash-string;
        description
          "Hash value of the virus.";
      }
    }
    container custom-hash {
      description
        "Configuration of custom hash.";
      leaf-list hash {
        type hash-string;
        description
          "Hash value of the virus.";
      }
    }
    container file-type-sets {
      list file-type {
        description
          "The suffix of the file type.";
        ntos-ext:nc-cli-one-liner;
        key "suffix";
        leaf suffix {
          type ntos-types:ntos-obj-name-type;
          ntos-ext:nc-cli-no-name;
        }
      }
    }
    container custom-file-type-sets {
      description
        "The suffix of the custom file type.";
      leaf-list suffix {
        type string;
        description
          "The name of the file type.";
      }
    }
  }
  grouping profile-detail {
    description
      "The detail of the profile.";
    leaf name {
      type string;
      description
        "The name of the profile.";
    }
    leaf template {
      type string;
      //mandatory true;
      description
        "The name of the reference template.";
      status obsolete;
    }
    leaf template-name {
      type string;
      mandatory true;
      description
        "The name of the reference template.";
    }
    leaf action {
      type action-type;
      mandatory true;
      description
        "The action of the profile.";
    }    
  }
  
  grouping anti-virus-file-exception {
    description
      "Indicate the anti-virus file type exception information.";
    leaf enabled {
      type boolean;
      default true;
      description
        "Enable or disable the anti-virus file exception detection.";
    }

    list file-type {
      key "name";
      description
        "Indicate the file type exception";
      leaf name {
        type string;
        description
          "Indicate the name of the file type exception.";
      }
    }
  }

  augment "/ntos:config/ntos-system:system" {
    description
      "System level configuration or global configuration of anti-virus";
    container anti-virus-file-exception {
      description
        "Configuration for anti-virus file exception.";
      uses anti-virus-file-exception;
    }
  }

  augment "/ntos:state/ntos-system:system" {
    description
      "The state of anti-virus.";
    container anti-virus-file-exception {
      description
        "The state of anti-virus file exception.";
      uses anti-virus-file-exception;
    }
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "The configuration of anti-virus.";
    container anti-virus {
      description
        "Configuration of anti-virus.";
      list template{
        description
          "List of template.";
        key "name";
        uses template-detail;
        container reference-list {
          config false;
          description
            "The policy references of the template.";
          uses reference-detail;
        }
      }
      list profile {
        key "name";
          description
            "List of profile.";
          uses profile-detail;
      }
    }
  }
  
  augment "/ntos:state/ntos:vrf" {
    description
      "The state of anti-virus.";
    container anti-virus {
      config false;
      description
        "The state of anti-virus.";
      list template{
        description
          "List of template.";
        key "name";
        uses template-detail;
        container reference-list {
          config false;
          description
            "The policy references of the template.";
          uses reference-detail;
        }
      }
    }
  }

  rpc av-protocol-set-show {
    description
      "Show supported protocols.";
    output {
      list protocol {
        description
          "The list of protocol.";
        leaf name {
          type string;
          description
            "The name of the protocol.";
        }
      }
    }
    ntos-ext:nc-cli-show "av protocol-set";
  }

  rpc av-template-show {
    description
      "Show defined templates.";
    input {
      leaf vrf {
        type string;
        default "main";
        description
          "The name of the virtual routing forwarding.";
      }
      leaf start {
        type uint32;
        description
          "The start offset.";
      }
      leaf end {
        type uint32;
        description
          "The end offset.";
      }
      leaf name {
        type string;
          description
            "Specific template name or fuzzy matching prefix.";
      }
      leaf predefined {
        type boolean;
          default false;
        description
          "Show predefined templates or custom templates.";
      }
      leaf fuzzy-match {
        type boolean;
        default false;
        description
          "Whether to perform fuzzy match.";
      }
    }
    output {
      leaf template-num {
        type uint32;
        description
          "The total number of templates.";
      }
      list template {
        key "name";
        uses template-detail;
        container reference-list {
          description
            "The policy references of the template.";
          uses reference-detail;
        }
      }
    }
    ntos-ext:nc-cli-show "av template";
  }

  rpc av-signature-update {
    description
      "Update anti-virus signature.";
    input {
      leaf type {
        type scan-mode;
        description
          "The scan mode of anti-virus.";
      }
      leaf package-path {
        type string;
        description
          "The location of the upgrade package.";
      }
    }
    output {
      leaf id {
        type uint32;
        description
          "Id of tracing upgrade.";
      }
      leaf progress {
        type uint32;
        description
          "Progress of upgrade.";
      }
      leaf errnum {
        type uint32;
        description
          "Error code.";
      }
    }
    ntos-ext:nc-cli-cmd "av signature-update";
  }

  rpc av-signature-version-show {
    description
      "Show anti-virus signature version.";
    input {
      leaf type {
        type scan-mode;
        description
          "The scan mode of anti-virus.";
      }
    }
    output {
      leaf version {
        type string;
          description
            "The version of the signature.";
      }
    }
    ntos-ext:nc-cli-show "av signature-version";
  }

  rpc av-signature-update-status-get {
    description
      "Show anti-virus signature update status.";
    input {
      leaf type {
        type scan-mode;
        description
          "The scan mode of anti-virus.";
      }
    }

    output {
      leaf errnum {
        type uint32;
        description
          "Error code.";
      }
    }
    ntos-ext:nc-cli-show "av signature-update status";
  }

  rpc av-file-exception-show {
    description
      "The rpc to show all file type exception in system.";
    output {
      list file-type {
        key "name";
        description
          "Indicate the file type exception.";
        leaf name {
          type string;
          description
            "Indicate the name of the file type exception.";
        }
      }
    }
    ntos-ext:nc-cli-show "av file-type-exception";
    ntos-api:internal;
  }

  rpc av-show-profile {
    input {
      leaf vrf {
        type string;
        default "main";
        description
          "The name of the virtual routing forwarding.";
      }
      leaf profile-id {
        type string;
        description
          "Filter by profile id.";
      }
    }

    output {
      list profile {
        leaf id {
          type uint32;
          description
            "The id of the anti-virus profile.";
        }
        leaf name {
          type string;
          description
            "The name of the anti-virus profile.";
        }
        leaf action {
          type action-type;
          description
            "The action of the anti-virus profile.";
        }
        leaf template-name {
          type string;
          description
            "The name of the anti-virus template.";
        }
      }
    }
    ntos-ext:nc-cli-show "av profile";
  }

  rpc av-sdk-thread-status-get {
    description
      "Show anti-virus sdk thread status.";
    output {
      leaf status {
        type uint8;
        description
          "The status of sdk thread.";
      }
    }
    ntos-ext:nc-cli-show "av sdk-thread status";
  }

  rpc av-hash-algorithm-get {
    description
      "Get the hash algorithm of quick scanning mode.";
    output {
      leaf hash-algorithm {
        type uint8;
        description
          "The type of hash algorithm: 1 represents MD5, 2 represents SHA1.";
      }
    }
    ntos-ext:nc-cli-show "av hash-algorithm";
  }

  rpc av-checked-file-types-get {
    description
      "Get the list of supported file types checked by the user.";
    output {
      list file-type-group {
        key "name";
        leaf name {
          type ntos-types:ntos-obj-name-type;
          description
            "The group name of the file type.";
        }
        leaf-list file-type {
          type string;
          description
            "The name of the file type.";
        }
      }
    }
    ntos-ext:nc-cli-show "av checked-file-type";
  }
}