module ntos-appid {
  namespace "urn:ruijie:ntos:params:xml:ns:yang:appid";
  prefix ntos-appid;

  import ntos {
    prefix ntos;
  }

  import ntos-types {
    prefix ntos-types;
  }

 import ntos-inet-types {
    prefix ntos-inet;
  }

  import ntos-extensions {
    prefix ntos-extensions;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS appid module.";

  revision 2022-01-07 {
    description
      "Create.";
    reference "";
  }

  typedef app-ip-address-type {
    type union {
      type ntos-inet:ipv4-address;
      type ntos-inet:ipv4-prefix;
      type ntos-inet:ipv4-range;
      type ntos-inet:ipv4-mask;

      /* add IPv6 Address Type */
      type ntos-inet:ipv6-address;
      type ntos-inet:ipv6-prefix;
      type ntos-inet:ipv6-range;
    }
  }
  grouping appid-config {
    description
      "Configuration data for appid.";

    leaf dpi-enabled {
      type boolean;
      description
        "Set status of dpi-enabled.";
    }

    leaf cache-enabled {
      type boolean;
      description
        "Set status of cache-enabled.";
    }

    leaf dfi-enabled {
      type boolean;
      description
        "Set status of dfi-enabled.";
    }

    leaf app-blacklist-enabled {
      type boolean;
      description
        "Set status of app-blacklist-enabled.";
    }

    leaf mode {
      type enumeration {
        enum all-identify {
          description
            "The mode of identify all.";
        }
        enum dynamic-identify {
          description
            "The mode of identfiy some .";
        }
        enum no-identify {
          description
            "The mode of no identify.";
        }
      }
      default dynamic-identify;
      description
        "The mode of identify.";
    }

    container cache {
      description
        "Set aging time of cache.";

      leaf timeout {
        type uint32 {
          range "1..1440";
        }
        units "minute";
        description
          "The value of aging time.";
      }
    }

    container custom-app {
      description
        "The custom application.";
      list application {
        key "app-name";
        description
          "The list of custom application.";
        leaf app-name {
          type ntos-types:ntos-obj-name-type;
          description
            "The name of application.";
        }
        leaf class-name {
          type ntos-types:ntos-obj-name-type;
          description
            "The name of application class.";
        }
        list rule {
          key "protocol src-ip dst-ip dst-port";
          description
          "The list of custom application rule.";
          leaf protocol {
            type string;
            description
              "The protocol of the custom application.";
          }
          leaf src-ip {
            type app-ip-address-type;
            default "0.0.0.0/0";
            description
              "The source IP of the custom application.";
          }
          leaf dst-ip {
            type app-ip-address-type;
            default "0.0.0.0/0";
            description
              "The destination IP of the custom application.";
          }
          leaf dst-port {
            type string {
              pattern '([0-9]|[1-9]\d|[1-9]\d{2}|[1-9]\d{3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])' +
              '(-([0-9]|[1-9]\d|[1-9]\d{2}|[1-9]\d{3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5]))?' {
                error-message "Invalid port range format. Example: '21' or '1024-2048' in the range 0-65535";
              }
              length 0..128;
            }
            default "0";
            description
              "The destination port of the custom application.";
          }
        }
        list domain-rule {
          key "name";
          description
            "The list of custom domain.";
          leaf name {
            type string {
              length "1..255";
              pattern '(\*\.)?(?!-)([a-zA-Z0-9-]{1,63}(?<!-)(\.[a-zA-Z0-9-]{1,63}(?<!-))*)?|(?!-)[a-zA-Z0-9-]{1,63}(?<!-)';
              ntos-extensions:nc-cli-shortdesc "<domain-name>";
            }
            description
              "The name of the custom domain.";
          }
          leaf track-port {
            type string {
              ntos-extensions:nc-cli-shortdesc "<port-range>";
              pattern '([0-9]|[1-9]\d|[1-9]\d{2}|[1-9]\d{3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])' +
              '(-([0-9]|[1-9]\d|[1-9]\d{2}|[1-9]\d{3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5]))?' +
              '(,([0-9]|[1-9]\d|[1-9]\d{2}|[1-9]\d{3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])' +
              '(-([0-9]|[1-9]\d|[1-9]\d{2}|[1-9]\d{3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5]))?)*' {
                error-message "Invalid port range format. Example: '21,22,1024-2048' in the range 0-65535";
              }
              length 0..256;
            }
            default "0-65535";
            description
              "The port of the custom domain track.";
          }
          leaf dns-track-enabled {
            type boolean;
            default true;
            description
              "Set status of dns track enabled.";
          }
          leaf host-track-enabled {
            type boolean;
            default false;
            description
              "Set status of host track enabled.";
          }
        }
      }
    }

    container custom-group {
      description
        "The custom group of application.";
      list app-group {
        key "group-name";
        description
          "The list of custom group.";

        leaf group-name {
          type ntos-types:ntos-obj-name-type;
          description
            "The name of group.";
        }
        leaf description {
          type string;
          description
            "The description of group.";
        }

        leaf-list app-name {
          type ntos-types:ntos-obj-name-type;
            description
             "The name of application.";
        }
      }
    }

    container app-blacklist {
      description
        "The blacklist of application.";
      leaf-list app-name {
        type ntos-types:ntos-obj-name-type;
        description
          "The name of application.";
      }
    }
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "Appid configuration.";

    container appid {
      description
        "Appid configuration.";
      uses appid-config;
    }
  }

  augment "/ntos:state/ntos:vrf" {
    description
      "State of appid.";

    container appid {
      description
        "Appid configuration.";
      uses appid-config;
    }
  }

  notification appid-signature-version-update {
    description
      "Notify app signature version.";
    leaf version {
      type string;
      description
        "Version of signature.";
    }
  }

  rpc appid-signature-version-show {
    description
      "Show appid signature version.";

    output {
      leaf version {
        type string;
        description
          "Version of signature.";
      }
    }

    ntos-extensions:nc-cli-show "appid signature-version";
  }

  rpc appid-class-show {
    description
      "Show information of application class.";

    output {
      leaf buffer {
        type string;
        description
          "The information of application class.";
      }
    }
  }

  rpc appid-three-layer-app-show {
    description
      "Show application of signature.";

    input {
      leaf class-name {
        type string;
        description
          "The name of application class.";
      }
      leaf sub-class-name {
        type string;
        description
          "The name of application sub class.";
      }
    }

    output {
      leaf buffer {
        type string;
        description
          "The information of application.";
      }
    }
  }

  grouping appid-filter-condition {
    description
      "Configuration data for appid.";

    leaf filter {
      type string;
      description
        "The filter string.";
      ntos-extensions:nc-cli-group "subcommand2";
    }
    leaf start {
      type uint32;
      description
        "The start offset.";
    }
    leaf end {
      type uint32;
      description
        "The end offset.";
    }
  }

  grouping appid-capacity {
    description
      "The capacity of appid.";
    leaf capacity {
      type uint32;
      description
        "The total capacity.";
    }
    leaf used {
      type uint32;
      description
        "The used capacity.";
    }
    leaf remain {
      type uint32;
      description
        "The reamin capacity.";
    }
  }

  rpc appid-app-show {
    description
      "Show information of application or application group.";

    input {
      container app-group {
        description
          "The information of application group.";
        presence "Show appid application group.";

        leaf name {
          type string;
          description
            "The name of application group.";
          ntos-extensions:nc-cli-group "subcommand2";
        }
        container av-exception {
          description
            "The information of av-exception.";
          presence "Show information of av-exception.";
          ntos-extensions:nc-cli-group "subcommand2";
        }
        container pbr-support {
          description
            "The information of pbr-support.";
          presence "Show information of pbr-support";
          ntos-extensions:nc-cli-group "subcommand2";
        }
        container pbr-group {
          description
            "The information of pbr-group.";
          presence "Show information of pbr application group.";
          ntos-extensions:nc-cli-group "subcommand2";
        }
        container normal-group {
          description
            "The information of normal-group.";
          presence "Show information of normal application group.";
          ntos-extensions:nc-cli-group "subcommand2";
        }
        uses appid-filter-condition;
        ntos-extensions:nc-cli-group "subcommand1";
      }
      container app {
        description
          "The information of application.";
        presence "Show information of application.";

        container tree {
          description
            "The class relationship of application.";
          presence "Show the class relationship of application.";
          ntos-extensions:nc-cli-group "subcommand2";
        }
        leaf name {
          type string;
          description
            "The name of application.";
          ntos-extensions:nc-cli-group "subcommand2";
        }
        container file {
          description
            "The file of application info.";
            presence "Show the file name of application info.";
            ntos-extensions:nc-cli-group "subcommand2";
        }
        uses appid-filter-condition;
        ntos-extensions:nc-cli-group "subcommand1";
      }
      container custom-app {
        description
          "The custom application.";
        presence "Show appid custom application.";
        leaf name {
          type string;
          description
            "The name of custom application.";
          ntos-extensions:nc-cli-group "subcommand2";
        }
        uses appid-filter-condition;
        ntos-extensions:nc-cli-group "subcommand1";
      }
    }

    output {
      leaf buffer {
        type string;
        description
          "The information of application or application group.";
      }
      leaf file {
        type string;
        description
          "The file name of record application info.";
      }
    }
    ntos-extensions:nc-cli-show "appid";
  }

  rpc appid-show-glb {
    description
      "Show global information.";

    output {
      leaf result {
        description
          "Result of command.";
        type string;
        ntos-extensions:nc-cli-stdout;
        ntos-extensions:nc-cli-hidden;
      }
    }
    ntos-extensions:nc-cli-show "appid global";
  }

  rpc appid-sig-update-state {
    description
      "Upgrade state of appid signature DB.";

    output {
      leaf errnum {
        type uint32;
        description
          "The state of updating.";
      }
    }
    ntos-extensions:nc-cli-cmd "appid signature-state";
  }

  rpc appid-signature-update {
    description
      "Update appid signature DB.";

    input {
      leaf file-name {
        type string;
        description
          "Name of upgrade file.";
      }
    }

    output {
      leaf id {
        type uint32;
        description
          "Id of tracing upgrade.";
      }
      leaf progress {
        type uint32;
        description
          "Progress of upgrade.";
      }
      leaf errnum {
        type uint32;
        description
          "Error code.";
      }
    }
    ntos-extensions:nc-cli-cmd "appid signature-update";
  }

  rpc appid {
    description
      "Execute appid command in fast path.";

    input {
      leaf run {
        description
          "Command to run.";
        type string;
      }
    }

    output {
      leaf result {
        description
          "Result of command.";
        type string;
        ntos-extensions:nc-cli-stdout;
        ntos-extensions:nc-cli-hidden;
      }
    }
    ntos-extensions:nc-cli-cmd "appid";
  }

  rpc appid-rollback-version {
    output {
      leaf buffer {
        type string;
        description
          "Signature version of rollback.";
      }
    }
  }

  rpc appid-signature-rollback {
    description
      "Rollback appid signature DB.";

    input {
      leaf mod {
        type string;
        description
          "Mod of rollback process.";
      }
      leaf id {
        type uint32;
        description
          "Id of rollback process.";
      }
      leaf rollback-type {
        type string;
        description
          "Type of rollback process.";
      }
    }

    output {
      leaf mod {
        type string;
        description
          "Mod of tracing rollback.";
      }
      leaf res {
        type uint32;
        description
          "Result of tracing rollback.";
      }
      leaf progress {
        type uint32;
        description
          "Progress of rollback.";
      }
      leaf errnum {
        type uint32;
        description
          "Error code.";
      }
    }
    ntos-extensions:nc-cli-cmd "appid signature-rollback";
  }

  rpc appid-running-mode {
    output {
      leaf mode {
        type string;
        description
          "Appid running mode.";
      }
    }
  }

  rpc appid-get-relationship {
    input {
      leaf app-name {
        type string;
        description
          "The name of application.";
      }
    }
    output {
      leaf buffer {
        type string;
        description
          "Application relationship with other policy.";
      }
    }
  }

  rpc appid-change-config {
    input {
      leaf translate-type {
        type enumeration {
          enum zh-to-en {
            description
              "Translate config to Chinese.";
          }
          enum en-to-zh {
            description
              "Translate config to English.";
          }
        }
      }
    }
  }

  rpc appid-get-config-change-status {
    output {
      leaf config-finish-status {
        type boolean;
        description
          "Status of config change.";
      }
    }
  }

  rpc appid-get-app-list {
    output {
      leaf buffer {
        type string;
        description
          "The list of application.";
      }
    }
  }

  rpc appid-get-capacity {
    output {
      container custom-group {
        uses appid-capacity;
        description
          "The capacity of custom group.";
      }
      container custom-app {
        description
          "The capacity of custom application.";
        uses appid-capacity;
        container ip-rule {
          uses appid-capacity;
          description
            "The capacity of custom ip rule.";
        }
        container domain-rule {
          uses appid-capacity;
          description
            "The capacity of custom domain rule.";
        }
      }
    }
  }
}
