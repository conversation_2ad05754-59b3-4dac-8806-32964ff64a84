module ntos-auth {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:system:auth";
  prefix ntos-auth;

  import iana-crypt-hash {
    prefix ianach;
  }
  import ietf-netconf-acm {
    prefix nacm;
  }
  import ntos {
    prefix ntos;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-extensions {
    prefix ntos-extensions;
  }
  import ntos-system {
    prefix ntos-system;
  }
  import ntos-api {
    prefix ntos-api;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS auth management.";

  revision 2022-04-20 {
    description
      "Support user disabling.";
    reference "";
  }

  revision 2021-11-03 {
    description
      "Forbid root user configuration.";
    reference "";
  }
  revision 2021-10-03 {
    description
      "Initial version.";
    reference "";
  }

  grouping auth-config {
    description
      "Local users configuration.";

    list user {
      key "name";
      description
        "List of local users on the system.";

      leaf name {
        type string {
          length 0..31;
          pattern 'root' {
            modifier "invert-match";
            error-message "The system user root is reserved.";
          }
          pattern '[a-zA-Z]+[a-zA-Z0-9_]*';
        }
        description
          "The user name string identifying this entry.";
      }

      leaf alias {
        type string {
          length 0..31;
          pattern 'root' {
            modifier "invert-match";
            error-message "The system user root is reserved.";
          }
          pattern '[a-zA-Z]+[a-zA-Z0-9_]*';
        }
        description
          "The alias of user name.";
      }

      leaf description {
        type ntos-types:ntos-obj-description-type;
        description
          "The description of user.";
      }

      leaf role {
        type enumeration {
          enum viewer {
            description
              "The user can view configuration and state and run standard
               commands. However, he/she cannot edit the configuration,
               read protected config/state nodes (such as passwords) nor
               run privileged commands (such as reboot, poweroff, etc.).";
          }
          enum admin {
            description
              "The user can view all configuration and state, including
               protected nodes (such as password). He/she may edit the
               configuration and run any command including privileged ones
               (such as reboot, poweroff, etc.).";
          }
        }
        mandatory true;
        description
          "The role of the user.";
      }

      leaf lock {
        type boolean;
        description
          "Lock the user.";
      }

      leaf password {
        type string;
        description
          "The user password, supplied as a hashed value
           using the notation described in the definition of the
           crypt-hash type.";
        nacm:default-deny-all;
        ntos-extensions:nc-cli-hashed-password "sha-256";
      }

      leaf-list authorized-key {
        type string {
          pattern '(ssh-dss|ssh-rsa|ssh-ed25519|ecdsa-sha2-.+) .+' {
            error-message "Only ssh-dss, ssh-rsa, ssh-ed25519 and ecdsa-sha2-* algorithms are supported.";
          }
        }
        description
          "A public SSH key for this user in the OpenSSH format.
           This key is allowed for SSH authentication without a password
           to both the NETCONF and SSH servers.

           You may use the ssh-keygen utility to generate a new key-pair and
           paste the contents of the `*.pub` file (the public key) here.";
        ntos-api:pattern-added "(ssh-dss|ssh-rsa|ssh-ed25519|ecdsa-sha2-.+) .+";
      }

      leaf network-password {
        type string;
        description
          "The user password of network";
      }
      ntos-extensions:data-not-sync;
    }
  }

  rpc show-system-auth {
    description
      "Show system auth.";
    input {
      leaf user {
        ntos-extensions:nc-cli-group "subcommand";
        type string {
          length 0..31;
          pattern '[a-zA-Z]+[a-zA-Z0-9_]*';
        }
        description
          "The name of user.";
      }

      leaf original {
        ntos-extensions:nc-cli-group "subcommand";
        type string {
          length 0..31;
          pattern '[a-zA-Z]+[a-zA-Z0-9_]*';
        }
        description
          "The original name of user.";
      }
    }

    output {
      uses auth-config;
    }

    ntos-extensions:nc-cli-show "system auth";
    ntos-api:internal;
  }

  rpc recovery-cmd {
    description
      "recovery rpc cmd.";

    input {
      container mode-type {
        leaf msg {
          description
            " mssessage type(mode, keepalive..).";
          type enumeration {
            enum enter;
            enum exit;
            enum keepalive;
            enum get-status;
            enum reset;
          }
        }
      }
    }

    output {
      leaf status {
        type int32;
      }

      leaf left-time {
        type int32;
      }
    }
  }

  augment "/ntos:config/ntos-system:system" {
    description
      "Authentication configuration.";

    container auth {
      presence "Makes authentication available";
      description
        "Configuration data for local users.";

      container inactive {
        description
          "Configure password inactivation for local users.";
        leaf enabled {
          type boolean;
          description
            "Enable/disable password inactivation after expiration.";
        }
        leaf expiration {
          type int32 {
            range "0..9999";
          }
          description
            "Set password expiration duration in days.";
        }
      }
      uses auth-config;
    }
    container recovery-mode {
      description
         "password recovery mode configuration.";
      leaf version {
        description
          "Version of the reset mode.";
        type enumeration {
          enum "v1.0";
          enum "v2.0";
        }
      }
    }
  }

  augment "/ntos:state/ntos-system:system" {
    description
      "Authentication state.";

    container auth {
      description
        "Operational state data for local users.";

      container inactive {
        description
          "Configure password inactivation for local users.";
        leaf enabled {
          type boolean;
          description
            "Enable/disable password inactivation after expiration.";
        }
        leaf expiration {
          type int32 {
            range "0..9999";
          }
          description
            "Set password expiration duration in days.";
        }
      }
      uses auth-config;
    }
    container recovery-mode {
      description
         "password recovery mode configuration.";
      leaf version {
        description
          "Version of the reset mode.";
        type enumeration {
          enum "v1.0";
          enum "v2.0";
        }
      }
    }
  }
}
