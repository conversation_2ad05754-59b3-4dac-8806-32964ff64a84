module ntos-content-audit {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:content-audit";
  prefix ntos-content-audit;

  import ntos {
    prefix ntos;
  }

  import ntos-types {
    prefix ntos-types;
  }

  import ntos-extensions {
    prefix ntos-ext;
  }

  import ntos-api {
    prefix ntos-api;
  }

  import ntos-user-management {
    prefix ntos-user-management;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS config content-audit module.";

  revision 2023-04-18 {
    description
      "Create.";
    reference
      "";
  }

  typedef audit-direction {
    type enumeration {
      enum upload {
        description
          "Upload.";
      }
      enum download {
        description
          "Download.";
      }
      enum both {
        description
          "Both.";
      }
    }
    description
      "The direction of the file audit.";
  }

  grouping file-audit {
    container file-name {
      leaf direction {
        type audit-direction;
        description
          "The direction of the file name audit.";
      }
      description
        "The configuration of the file name.";
    }

    container file-content {
      leaf direction {
        type audit-direction;
        description
          "The direction of the file content audit.";
      }
      description
        "The configuration of the file content.";
    }
  }

  grouping content-audit-enabled {
    leaf enabled {
      type boolean;
      default "false";
      description
        "Enable or disable audit.";
    }
  }

  grouping blog-or-bbs-audit {
    leaf audit {
      type bits {
        bit attachments {
          description
            "Attachments.";
        }
      }
    }
  }

  grouping content-audit-profile {
    description
      "The grouping of content audit profile.";

    leaf name {
      type ntos-types:ntos-obj-name-type;
      description
        "The name of content audit profile.";
    }

    leaf description {
      type ntos-types:ntos-obj-description-type;
      description
        "The description of content audit profile.";
    }

    container audit-type {
      description
        "The type of content audit.";

      container url-audit {
        description
          "The configuration of url audit.";
        uses content-audit-enabled;
      }

      container im-audit {
        description
          "The configuration of im audit.";
        uses content-audit-enabled;
      }

      container telnet-audit {
        description
          "The configuration of telnet audit.";
        uses content-audit-enabled;
      }

      container web-search-audit {
        description
          "The configuration of web search audit.";
        uses content-audit-enabled;
      }

      container web-mail-audit {
        description
          "The configuration of web mail audit.";
        uses content-audit-enabled;
        uses blog-or-bbs-audit;
      }

      container mail-audit {
        description
          "The configuration of mail audit.";
        uses content-audit-enabled;
        uses blog-or-bbs-audit;
      }

      container bbs-audit {
        description
          "The configuration of bbs audit.";
        uses content-audit-enabled;
        uses blog-or-bbs-audit;
      }

      container microblog-audit {
        description
          "The configuration of microblog audit.";
        uses content-audit-enabled;
        uses blog-or-bbs-audit;
      }

      container ftp-audit {
        description
          "The configuration of ftp audit.";
        uses content-audit-enabled;
        uses file-audit;
      }

      container http-file-audit {
        description
          "The configuration of http file audit.";
        uses content-audit-enabled;
        uses file-audit;
      }
    }
  }

  grouping user-item {
    list user-group {
      key "name";
      description
        "The list of user group.";
      ntos-ext:nc-cli-one-liner;
      leaf name {
        description
          "policy related user group.
           An user group name must carry the authentication domain name.
           For example, /default/group1 indicates group1 in the default authentication domain.";
        type ntos-user-management:user-group-path;
        ntos-ext:nc-cli-no-name;
      }
    }

    list user-name {
      key "name";
      description
          "policy related user obj.
           An user obj name must carry the authentication domain name.
           For example: user1@xxx, if xxx is the default domain,
                                      do not fill it in and remove the '@' character.";
      ntos-ext:nc-cli-one-liner;
      leaf name {
        description "Configure the name of the user.";
        type ntos-types:ntos-obj-name-type;
        ntos-ext:nc-cli-no-name;
      }
    }
  }

  grouping contain-object {
    description
      "Detail about object contained by policy.";

    list source-zone {
      description
        "The name of source zone.";
      ntos-ext:nc-cli-one-liner;
      key "name";
      leaf name {
        type ntos-types:ntos-obj-name-type;
        ntos-ext:nc-cli-no-name;
      }
    }

    list dest-zone {
      description
        "The name of destination zone.";
      ntos-ext:nc-cli-one-liner;
      key "name";
      leaf name {
        type ntos-types:ntos-obj-name-type;
        ntos-ext:nc-cli-no-name;
      }
    }

    list source-network {
      description
        "The name of source network.";
      ntos-ext:nc-cli-one-liner;
      key "name";
      leaf name {
        type ntos-types:ntos-obj-name-type;
        ntos-ext:nc-cli-no-name;
      }
    }

    list dest-network {
      description
        "The name of destination network.";
      ntos-ext:nc-cli-one-liner;
      key "name";
      leaf name {
        type ntos-types:ntos-obj-name-type;
        ntos-ext:nc-cli-no-name;
      }
    }

    list service {
      description
        "The name of service.";
      ntos-ext:nc-cli-one-liner;
      key "name";
      leaf name {
        type ntos-types:ntos-obj-name-type;
        ntos-ext:nc-cli-no-name;
      }
    }

    list app {
      description
        "The name of application.";
      ntos-ext:nc-cli-one-liner;
      key "name";
      leaf name {
        type ntos-types:ntos-obj-name-type;
        ntos-ext:nc-cli-no-name;
      }
      leaf name-i18n {
        type ntos-types:ntos-obj-name-type;
        config false;
      }
    }

    uses user-item;
    leaf time-range {
      description
        "The name of time range.";
      type ntos-types:ntos-obj-name-type;
      default "any";
    }
  }

  grouping content-audit-policy {
    description
      "Configuration detail of content audit policy.";

    leaf name {
      type ntos-types:ntos-obj-name-type;
      description
        "The name of content audit policy.";
    }

    leaf description {
      type ntos-types:ntos-obj-description-type;
      description
        "The description of content audit policy.";
    }

    leaf policy-id {
      config false;
      type uint16;
      description
        "The policy id of content audit policy.";
    }

    leaf position-id {
      config false;
      type uint16;
      description
        "The position id of content audit policy.";
    }

    leaf enabled {
      type boolean;
      default "true";
      description
        "Enable or disable content audit policy.";
    }

    leaf time-range-enabled {
      config false;
      type boolean;
      default "true";
      description
        "Enable or disable content audit policy by time range.";
    }

    container action {
      choice content-audit-action {
        description
          "Indicate the action of content audit.";
        leaf audit-profile {
          description
            "Audit profile.";
          type ntos-types:ntos-obj-name-type;
        }
        leaf audit-all {
          description
            "Aduit all.";
          type empty;
        }

        leaf audit-none {
          description
            "Audit none.";
          type empty;
        }
      }
    }
    uses contain-object;
  }

  grouping white-file-group-item {
    container file-group {
      description
        "The list of file group.";
      list pre-defined {
        key "name";
        description
          "Indicate the Content audit white pre define file group.";

        leaf name {
          type ntos-types:ntos-obj-name-type;
          description
            "The name of pre define file group.";
        }

        leaf name-i18n {
          type string;
          config false;
          description
            "The name-i18n of pre define file group.";
        }
      }

      list user-defined {
        key "name";
        description
          "Indicate the Content audit white user define file group.";

        leaf name {
          type ntos-types:ntos-obj-name-type;
          description
            "The name of user define file group.";
        }
      }
    }
  }

  grouping white-url-category-item {
    container url-category {
      description
        "The list of url category.";
      list pre-defined {
        key "name";
        description
          "Indicate the Content audit white pre define category.";

        leaf name {
          type ntos-types:ntos-obj-name-type;
          description
            "The name of pre define category.";
        }

        leaf name-i18n {
          type string;
          config false;
          description
            "The name-i18n of pre define category.";
        }
      }

      list user-defined {
        key "name";
        description
          "Indicate the Content audit white user define category.";

        leaf name {
          type ntos-types:ntos-obj-name-type;
          description
            "The name of user define category.";
        }
      }
    }
  }

  grouping white-user-item {
    container user {
      description
        "The list of white user info.";

      list user-group {
        key "name";
        leaf name {
          description
            "The name of white user group.";
          type ntos-user-management:user-group-path;
        }
      }

      list user-name {
        key "name";
        leaf name {
          description
            "The name of white user group.";
          type ntos-user-management:user-group-path;
        }
      }
    }
  }

  grouping content-audit-whitelist {
    description
      "The grouping of content audit white list.";

    uses white-file-group-item;
    uses white-url-category-item;
    uses white-user-item;
    container app {
      list app-name {
        key "name";
        description
          "The list of white app.";
        leaf name {
          type string;
          description
            "The name of white app.";
        }
        leaf name-i18n {
          type string;
          config false;
          description
            "The name-i18n of white app.";
        }
      }
    }
  }

  grouping show-rpc-input-arguments {
    description
      "The grouping of input leaf node.";

    leaf vrf {
      type ntos:vrf-name;
      description
        "The specific vrf.";
    }

    leaf name {
      type string;
      description
        "The name string of object.";
      ntos-ext:nc-cli-group "start-end";
    }

    leaf filter {
      type string;
      description
        "The filter string of object.";
      ntos-ext:nc-cli-group "start-end";
    }

    leaf start {
      type uint32;
      description
        "The index of page start.";
    }

    leaf end {
      type uint32;
      must "current() >= ../start" {
          error-message "The end value must be larger than the start value.";
      }
      description "The index of page end.";
    }
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "Content audit configuration.";

    container content-audit {
      description
        "Content audit configuration.";
      list profile {
        key "name";
        description
          "Indicate the Content audit profile.";
        uses content-audit-profile;
      }

      list policy {
        key "name";
        description
          "Indicate the Content audit policy.";
        uses content-audit-policy;
        ordered-by user;
      }

      list default-policy {
        key "name";
        description
          "Indicate the default audit policy.";
        leaf name {
          type ntos-types:ntos-obj-name-type;
          description
            "The name of default audit policy.";
        }
        leaf enabled {
          type boolean;
          description
            "Enable or disable default audit policy.";
        }
      }
      container whitelist {
        description
          "The list of content-audit bypass.";
        uses content-audit-whitelist;
      }
    }
  }

  augment "/ntos:state/ntos:vrf" {
    description
      "State of Content audit.";

    container content-audit {
      description
        "Content audit configuration.";

      list profile {
        key "name";
        description
          "Indicate the Content audit profile.";
        uses content-audit-profile;
      }

      list policy {
        key "name";
        description
          "Indicate the Content audit policy.";
        uses content-audit-policy;
        ordered-by user;
      }

      container whitelist {
        description
          "The list of content-audit bypass.";
        uses content-audit-whitelist;
      }
    }
  }

  rpc content-audit-policy-stat-clear {
    description
      "Clear hit statistics of content audit policy.";

    input {
      leaf vrf {
        type string;
        description
          "Indicate the vrf.";
      }

      leaf policy-id-list {
        type string;
        description
          "Indicate the ID list of policy.";
      }
    }
    ntos-ext:nc-cli-cmd "content-audit policy stat-clear";
    ntos-api:internal;
  }

  rpc content-audit-show {
    input {
      container profile {
        description
          "TThe info of input profile.";
        presence "show content audit profile.";
        uses show-rpc-input-arguments;
        ntos-ext:nc-cli-group "show-audit-command";
      }

      container policy {
        description
          "The info of input policy.";
        presence "show content audit policy.";
        uses show-rpc-input-arguments;
        ntos-ext:nc-cli-group "show-audit-command";
      }

      container whitelist {
        description
          "The white list of content audit.";

        container file-group {
          description
            "The list of file group";
          presence "show content-audit whitelist file-group.";
          container pre-defined {
            description
              "The pre-defined list of file group.";
            presence "show content-audit whitelist file-group pre-defined.";
            ntos-ext:nc-cli-group "subcommand2";
          }
          container user-define {
            description
              "The user-defined list of file group.";
            presence "show content-audit whitelist file-group user-defined.";
            ntos-ext:nc-cli-group "subcommand2";
          }
          ntos-ext:nc-cli-group "subcommand1";
          uses show-rpc-input-arguments;
        }

        container user {
          description
            "The white list of user group.";
          presence "show content audit whitelist user-group.";
          container user-name {
            description
              "The name list of user.";
            presence "show content-audit whitelist user user-name.";
            ntos-ext:nc-cli-group "subcommand2";
          }
          container user-group {
            description
              "The group name list of user.";
            presence "show content-audit whitelist user user-group.";
            ntos-ext:nc-cli-group "subcommand2";
          }
          ntos-ext:nc-cli-group "subcommand1";
          uses show-rpc-input-arguments;
        }

        container app {
          description
            "The white list of applictaion.";
          presence "show content audit whitelist application.";
          uses show-rpc-input-arguments;
          ntos-ext:nc-cli-group "subcommand1";
        }

        container url-category {
          description
            "The white list of url category.";
          presence "show content audit whitelist url-category.";
          container pre-defined {
            description
              "The pre-defined list of url category.";
            presence "show content-audit whitelist url-category pre-defined.";
            ntos-ext:nc-cli-group "subcommand2";
          }
          container user-defined {
            description
              "The user-defined list of url category.";
            presence "show content-audit whitelist url-category user-defined.";
            ntos-ext:nc-cli-group "subcommand2";
          }
          ntos-ext:nc-cli-group "subcommand1";
          uses show-rpc-input-arguments;
        }
        ntos-ext:nc-cli-group "show-audit-command";
      }

      container signature-version {
        description
          "The version of content audit signature.";
        presence "show content audit signature version.";
        ntos-ext:nc-cli-group "show-audit-command";
      }
    }

    output {
      leaf profile-num {
        type uint16;
        description
          "The num of content filter profile.";
      }

      list profile {
        key "name";
        description
          "The list of content filter profile.";
        uses content-audit-profile;
        container content-audit-policy {
          leaf-list policy {
            type string;
            description
              "The policy name of refer profile.";
          }
        }
      }

      leaf policy-num {
        type uint16;
        description
          "The num of content audit policy.";
      }

      list policy {
        key "name";
        description
          "Indicate the content audit policy.";
        uses content-audit-policy;
        leaf hit-cnt {
          type uint64;
          description
            "The count of policy hit.";
        }
      }

      container whitelist {
        description
          "The white list.";
        uses content-audit-whitelist;
      }

      leaf signature-version {
        type string;
        description "The version of signature.";
      }
    }
    ntos-ext:nc-cli-show "content-audit";
  }

  rpc content-audit-update-signature {
    description
      "Update content audit signature.";
    input {
      leaf filepath {
        type string;
        description
          "The path of the update file.";
      }
    }
    output {
      leaf err-num {
        type uint32;
        description "The error num.";
      }
    }
    ntos-ext:nc-cli-cmd "content-audit signature-update";
  }

  rpc content-audit-show-update-state {
    description
      "The state of update signature.";
    output {
      leaf err-num {
        type uint32;
        description
          "The error num.";
      }
    }
  }

  rpc content-audit-name {
    description "Show the name of the content audit policy.";
    input {
      leaf vrf {
        type ntos:vrf-name;
        description "The specific vrf.";
      }

      leaf id-list {
        type string;
        description "Indicate the ID list of policy.";
      }
    }

    output {
      list policy-name {
        description "The policy name list.";

        leaf id {
          type uint32;
          description "The searched id.";
        }

        leaf name {
          type ntos-types:ntos-obj-name-type;
          description "The name of the policy.";
        }
      }
    }
  }

}