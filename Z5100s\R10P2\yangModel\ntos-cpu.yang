module ntos-cpu {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:system:cpu";
  prefix ntos-cpu;
  
  import ntos {
    prefix ntos;
  }
  
  import ntos-dm-types {
    prefix ntos-dm;
  }
  
  import ntos-system {
    prefix ntos-system;
  }
  
  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS system CPU monitoring module.";

  revision 2022-09-03 {
    description
      "Initial version.";
    reference "";
  }

  grouping cpu-state-list {
    description
      "Operational state data for the system CPU(s).";
    
	list cpu {
	  description
	     "The list of CPUs on the system.";
      key "dev-index index";
         
	  leaf dev-index {
		  type uint32;
		  description
			"The index that uniquely represents a device. In stacking environments, there are multiple devices.
             This value will indicate the device index. When in general single device environments, this value is 0.";
	  }
	  
      leaf index {
		 type uint32;
         description
            "The CPU index for multiple CPU system. It doesn't signify a processor core of one CPU. 
			 When in general single CPU system, this value is 0. This node signifies an aggregation
			 of the CPU utilization statistics over all cores of one CPU in the system.";
      }
    
	  leaf cpu-utilization-1min {
	     type uint32 {
	         range "0..100";
	     }
	     config false;
         description
            "The CPU utilization for 1 minutes.";
	  }

	  leaf cpu-utilization-lmin {
	     type uint32 {
	         range "0..100";
	     }
	     config false;
         description
            "The CPU utilization for 1 minutes.";
	  }


	  leaf cpu-utilization-5min {
	     type uint32 {
	        range "0..100";
	     }
         config false;
         description
            "The CPU utilization for 5 minutes.";
	  }
      
      leaf status {
         type ntos-dm:severity-level;
		 description
            "The node indicate whether the CPU utilization is normal or not.";
	  }
	  
      leaf warning-threshold {
        type uint32;
        default "85";
        description
        "The warning threshold of the CPU utilization. 
         When the curent CPU utilization exceeds the threshold, a warning notification will be sent.";
      }
      
      leaf critical-threshold {
        type uint32;
        default "85";
        description
          "The critical threshold of the CPU utilization. 
           When the curent CPU utilization exceeds the threshold, a critical notification will be sent.";
      }
    }
  }


  grouping sys-cpu-config {
    description
      "The grouping for CPU monitoring configuration of the system.";

  list cpu {
     description
       "The list of CPUs on the system.";
     key "dev-index index";
          
     leaf dev-index {
       type uint32;
       //config false;
       description
          "The index that uniquely represents a device. In stacking environments, there are multiple devices.
           This value will indicate the device index. When in general single device environments, this value is 0.";
      }
    
      leaf index {
        type uint32;
        //config false;
        description
           "The CPU index for multiple CPU system. It doesn't signify a processor core of one CPU. 
            When in general single CPU system, this value is 0. This node signifies an aggregation
            of the CPU utilization statistics over all cores of one CPU in the system.";
      }
    
      leaf warning-threshold {
        type uint32 {
            range "15..100";
        }
        default "95";
        description
        "The warning threshold of the CPU utilization. 
         When the curent CPU utilization exceeds the threshold, a warning notification will be sent.";
      }
      
      leaf critical-threshold {
        type uint32 {
            range "15..100";
        }
        default "95";
        description
          "The critical threshold of the CPU utilization. 
           When the curent CPU utilization exceeds the threshold, a critical notification will be sent.";
      }
    }
  }

  grouping sys-cpu-state {
     description
        "The grouping for CPU state of the system.";

 	 leaf total-cpu-number {
	    type uint32;
	 	config false;
	 	description
	 	   "The total CPU number of the system.";
	 }
	 
	 leaf total-utilization-1min {
	     type uint32 {
	         range "0..100";
	     }
	     config false;
         description
            "The total CPU utilization of all CPUs for 1 minutes.";
	 }

	 leaf total-utilization-lmin {
	     type uint32 {
	         range "0..100";
	     }
	     config false;
         description
            "The total CPU utilization of all CPUs for 1 minutes.";
	 }
	 
	 leaf total-utilization-5min {
	     type uint32 {
	         range "0..100";
	     }
         config false;
         description
             "The total CPU utilization of all CPUs for 5 minutes.";
	 }
	 
	 uses cpu-state-list; 
  }

  augment "/ntos:config/ntos-system:system" {
      description
         "The system CPU monitoring configuration.";

      container cpus {
         description
            "Enclosing container for the list of CPUs configuration.";
	     uses sys-cpu-config;
	  }
   }

  augment "/ntos:state/ntos-system:system" {
     description
        "The system CPU monitoring state.";

     container cpus {
        description
           "Enclosing container for the list of CPUs state.";
	    config false;
		
		uses sys-cpu-state;
	 }
  }
  
  notification cpu-alarm {
      description
         "The CPU status chanage notification.";

	  leaf dev-index {
		 type uint32;
		 description
			 "The index that uniquely represents a device. In stacking environments, there are multiple devices.
              This value will indicate the device index. When in general single device environments, this value is 0.";
      }   
   
      leaf index {
		 type uint32;
         description
            "The CPU index for multiple CPU system. It doesn't signify a processor core of one CPU. 
			 When in general single CPU system, this value is 0. This node signifies an aggregation
			 of the CPU utilization statistics over all cores of one CPU in the system.";
	  }
	  
	  leaf status {
         type ntos-dm:severity-level;
		 description
            "The node indicate whether the CPU utilization is normal or not.";
	  }
  }
}
