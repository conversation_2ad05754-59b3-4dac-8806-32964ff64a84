module ntos-dhcp-snooping {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:dhcp-snooping";
  prefix ntos-dhcp-snooping;

  import ntos {
    prefix ntos;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-dhcp {
    prefix ntos-dhcp;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
     E-mail:<EMAIL>";
  description
    "Ruijie NTOS DHCP Snooping function set.";

  revision 2024-03-08 {
    description
      "Add DHCP Snooping.";
    reference "";
  }

  identity dhcp-snooping {
    base ntos-types:SERVICE_LOG_ID;
    description
      "DHCP Snooping service.";
  }

  grouping dhcp-snp-config {
    description
      "Configuration data for DHCP Snooping.";

    leaf enabled {
      type boolean;
      default "false";
      description
        "Enable or disable the DHCP Snooping.";
    }

    leaf mode {
      type enumeration {
        enum normal {
            description
            "DHCP Snooping normal mode.";
        }
        enum monitor {
            description
            "DHCP Snooping monitor mode.";
        }
      }
      default "normal";
      description
        "Set the mode of DHCP Snooping.";
    }

    leaf loose-forward {
      type boolean;
      default "false";
      description
        "The loose-forward function.";
    }

    leaf bootp-bind {
      type boolean;
      default "false";
      description
        "BOOTP user binding function.";
    }

    leaf giaddr-check {
      type boolean;
      default "false";
      description
        "The giaddr field check function.";
    }

    leaf mac-verify {
      type boolean;
      default "false";
      description
        "MAC address verify function.";
    }

    leaf unicast {
      type boolean;
      default "false";
      description
        "ACK packet unicast function.";
    }

    leaf db-write-interval {
      type uint32 {
        range "0..3600";
      }
      default "0";
      description
        "The interval of DB periodic write, 0 is disabled.";
    }
  }

  grouping dhcp-snp-parameters {
    description
      "Parameters for DHCP Snooping, used by ntos-interface.";

    container snooping {
      leaf trust {
        type boolean;
        default "false";
        description
          "Config interface as DHCP Snooping trust interface.";
      }

      leaf suppress {
        type boolean;
        default "false";
        description
          "Config interface as DHCP Snooping suppress interface.";
      }

      description
        "DHCP Snooping parameters for this interface.";
    }
  }

  rpc snp-backup-usr {
    description
      "Write the DHCP Snooping usr-binding to database.";

    input {
      leaf vrf {
        type ntos:vrf-name;
        default "main";
        description
          "vrf name.";
      }

      leaf delay {
        type uint8 {
          range "0..5";
        }
        description
          "Write to DB after delay time.";
      }
    }

    output {
      leaf buffer {
        type string;
        description
          "Command output buffer since the last request.";
        ntos-ext:nc-cli-stdout;
      }
    }

    ntos-ext:nc-cli-hidden;
    ntos-ext:nc-cli-cmd "dhcp-snooping backup-usr";
  }

  rpc snp-recover-usr {
    description
      "Recover the DHCP Snooping usr-binding from database.";

    input {
      leaf vrf {
        type ntos:vrf-name;
        default "main";
        description
          "vrf name.";
      }

      leaf delay {
        type uint8 {
          range "0..5";
        }
        description
          "Recover from DB after delay time.";
      }
    }

    output {
      leaf buffer {
        type string;
        description
          "Command output buffer since the last request.";
        ntos-ext:nc-cli-stdout;
      }
    }

    ntos-ext:nc-cli-hidden;
    ntos-ext:nc-cli-cmd "dhcp-snooping recover-usr";
  }

  rpc snp-get-usr {
    description
      "Copy DHCP Snooping usr-binding to flash by range.";

    input {
      leaf vrf {
        type ntos:vrf-name;
        default "main";
        description
          "vrf name.";
      }

      leaf start {
        type uint32;
        description
          "Range start.";
        ntos-ext:nc-cli-no-name;
      }

      leaf end {
        type uint32;
        description
          "Range end.";
        ntos-ext:nc-cli-no-name;
      }
    }

    output {
      leaf buffer {
        type string;
        description
          "Command output buffer since the last request.";
        ntos-ext:nc-cli-stdout;
      }
    }

    ntos-ext:nc-cli-hidden;
    ntos-ext:nc-cli-cmd "dhcp-snooping get-usr";
  }

  rpc snp-clear-usr {
    description
      "Clear DHCP Snooping usr-binding.";

    input {
      leaf vrf {
        type ntos:vrf-name;
        default "main";
        description
          "vrf name.";
      }

      leaf delay {
        type uint8 {
          range "0..5";
        }
        description
          "Clear DHCP Snooping usr-binding after delay time.";
      }
    }

    output {
      leaf buffer {
        type string;
        description
          "Command output buffer since the last request.";
        ntos-ext:nc-cli-stdout;
      }
    }

    ntos-ext:nc-cli-hidden;
    ntos-ext:nc-cli-cmd "dhcp-snooping clear-usr";
  }

  rpc snp-show-intf {
    description
      "Show DHCP Snooping interface table.";

    input {
      leaf vrf {
        type ntos:vrf-name;
        default "main";
        description
          "vrf name.";
      }

      container content {
        leaf start {
          type uint32;
          description
            "Range start.";
          ntos-ext:nc-cli-no-name;
        }
        leaf end {
          type uint32;
          description
            "Range end.";
          ntos-ext:nc-cli-no-name;
        }
        description
          "content start end.";
        ntos-ext:nc-cli-group "sub1";
      }

      leaf name {
        type ntos-types:ifname;
        description
          "The name of the interface.";
        ntos-ext:nc-cli-group "sub1";
      }
    }

    output {
      list interface {
        leaf name {
          type ntos-types:ifname;
          description
            "The name of the interface.";
        }

        leaf trust {
          type boolean;
          default "false";
          description
            "Config interface as DHCP Snooping trust interface.";
        }

        leaf suppress {
          type boolean;
          default "false";
          description
            "Config interface as DHCP Snooping suppress interface.";
        }

        description
          "DHCP Snooping parameters for this interface.";
      }
    }

    ntos-ext:nc-cli-show "dhcp-snooping interface";
  }

  augment "/ntos:config/ntos:vrf/ntos-dhcp:dhcp" {
    description
      "DHCP snooping configuration.";
    container snooping {
      uses dhcp-snp-config;
      presence "DHCP Snooping configuration";
      description
        "DHCP Snooping configuration.";
    }
  }

  augment "/ntos:state/ntos:vrf/ntos-dhcp:dhcp" {
    description
      "DHCP snooping state.";
    container snooping {
    uses dhcp-snp-config;
      presence "DHCP Snooping state";
      description
        "DHCP Snooping state.";
    }
  }
}
