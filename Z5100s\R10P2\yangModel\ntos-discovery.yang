module ntos-discovery {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:discovery";
  prefix ntos-discovery;

  import ntos {
    prefix ntos;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-api {
    prefix ntos-api;
  }
  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS discovery.";

  revision 2022-04-13 {
    description
      "Initial version.";
    reference "";
  }

  identity discovery {
    base ntos-types:SERVICE_LOG_ID;
    description
     "discovery service.";
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "Network discovery configuration.";

    container discovery {
      leaf enabled {
        type boolean;
        description
          "Set status of discovery-enabled.";
      }

      container network {
        leaf id {
            type string;
            description
              "The id of network.";
        }

        leaf name {
            type string;
            description
              "The name of network.";
        }
      }

      leaf parent-id {
        type string;
        description
            "The parent id of network.";
      }
    }
  }

  rpc discovery {
    description
      "Show state of discovery.";
    input {
      leaf vrf {
        type ntos-types:ntos-obj-name-type;
        description
          "Specify the VRF.";
      }

      leaf type {
        type enumeration {
          enum master;
          enum info;
          enum conflict-master;
          enum esw;
          enum working-mode;
          enum unsupport-master;
        }

        description
          "Show type of discovery.";
      }
    }

    output {
      container master {
        leaf network-id {
          description
            "The id of network on current netwrok master.";
            type string;
        }

        leaf network-name {
          description
            "The name of network on current netwrok master.";
            type string;
        }

        leaf sn {
          description
            "The SN of current network master.";
            type string;
        }

        leaf product-type {
          description
            "The product type of current network master.";
            type string;
        }

        leaf device-type {
          description
            "The device type of current network master.";
            type string;
        }

        leaf ip {
          description
            "The IP of current network master.";
            type string;
        }

        leaf mac {
          description
            "The MAC of current network master.";
            type string;
        }

        leaf software {
          description
            "The software of current network master.";
            type string;
        }

        leaf ifx {
          description
            "The priority of current network master.";
            type string;
        }

        leaf group_name {
          description
            "The group name of current network master.";
            type string;
        }

        leaf group_id {
          description
            "The group id of current network master.";
            type string;
        }

        leaf role {
          description
            "The role of current network master.";
            type string;
        }
      }

      container info {
        leaf network-id {
          description
            "The id of network.";
            type string;
        }

        leaf network-name {
          description
            "The name of network.";
            type string;
        }

        leaf parent-id {
          description
            "The parent id of network.";
            type string;
        }
      }

      leaf working-mode {
        description
          "Current working Mode.";
        type string;
      }

      leaf count {
        description
          "The count of list.";
        type string;
      }

      list conflict-master {
        description
          "The group of conflicting network master.";

        leaf network-id {
          description
            "The id of netwrok on conflicting network master.";
            type string;
        }

        leaf network-name {
          description
            "The name of network on conflicting netwrok master.";
            type string;
        }

        leaf mac {
          description
            "The MAC of conflicting network master.";
            type string;
        }

        leaf ip {
          description
            "The IP of conflicting network master.";
            type string;
        }

        leaf sn {
          description
            "The SN of conflicting network master.";
            type string;
        }

        leaf product-type {
          description
            "The product type of conflicting network master.";
            type string;
        }

        leaf device-type {
          description
            "The device type of conflicting network master.";
            type string;
        }

        leaf software {
          description
            "The software of conflicting network master.";
            type string;
        }

        leaf ifx {
          description
            "The priority of conflicting network master.";
            type string;
        }

        leaf group_name {
          description
            "The group name of conflicting network master.";
            type string;
        }

        leaf group_id {
          description
            "The group id of conflicting network master.";
            type string;
        }

        leaf role {
          description
            "The role of conflicting network master.";
            type string;
        }
      }

      list esw {
        description
          "The group of ESW.";
        leaf network-id {
          description
            "The id of network in current network ESW.";
            type string;
        }

        leaf network-name {
          description
            "The name of network on current network ESW.";
            type string;
        }

        leaf mac {
          description
            "The MAC of current network ESW.";
            type string;
        }

        leaf ip {
          description
            "The IP of current network ESW.";
            type string;
        }

        leaf sn {
          description
            "The SN of current network ESW.";
            type string;
        }

        leaf product-type {
          description
            "The product type of current network ESW.";
            type string;
        }

        leaf device-type {
          description
            "The device type of current network ESW.";
            type string;
        }

        leaf software {
          description
            "The software of current network ESW.";
            type string;
        }

        leaf ifx {
          description
            "The priority of current network ESW.";
            type string;
        }

        leaf group_name {
          description
            "The group name of current network ESW.";
            type string;
        }

        leaf group_id {
          description
            "The group id of current network ESW.";
            type string;
        }

        leaf role {
          description
            "The role of current network ESW.";
            type string;
        }
      }
      list unsupport-master {
        description
          "The group of ESW.";
        leaf network-id {
          description
            "The id of network in unsupport master.";
            type string;
        }

        leaf network-name {
          description
            "The name of network on unsupport master.";
            type string;
        }

        leaf mac {
          description
            "The MAC of unsupport master.";
            type string;
        }

        leaf ip {
          description
            "The IP of unsupport master.";
            type string;
        }

        leaf sn {
          description
            "The SN of unsupport master.";
            type string;
        }

        leaf product-type {
          description
            "The product type of unsupport master.";
            type string;
        }

        leaf device-type {
          description
            "The device type of unsupport master.";
            type string;
        }

        leaf software {
          description
            "The software of unsupport master.";
            type string;
        }

        leaf ifx {
          description
            "The priority of unsupport master.";
            type string;
        }

        leaf group_name {
          description
            "The group name of unsupport master.";
            type string;
        }

        leaf group_id {
          description
            "The group id of unsupport master.";
            type string;
        }

        leaf role {
          description
            "The role of unsupport master.";
            type string;
        }
      }
    }

    ntos-ext:nc-cli-show "discovery";
    ntos-api:internal;
  }
}