module ntos-dm-ext {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:system:dm-ext";
  prefix ntos-dm-ext;

  import ntos {
    prefix ntos;
  }

  import ntos-dm-types {
    prefix ntos-dm;
  }

  import ntos-system {
    prefix ntos-system;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS device management extend module.";

  revision 2022-09-03 {
    description
      "Initial version.";
    reference "";
  }

  grouping sys-power-state {
      description
         "The grouping for device power state.";
	  list power-entry {
	     description
            "The power state list.";
	     key "dev-index index";

		 leaf dev-index {
		    type uint32;
		    description
			   "The index that uniquely represents a device. In stacking environments, there are multiple devices.
                This value will indicate the device index. When in general single device environments, this value is 0.";
		 }

		 leaf index {
		    type uint32;
		    description
			   "The index of power state entry.";
		 }

		 leaf status {
            type ntos-dm:power-status;
			config false;
		    description
               "The node indicate whether the power is normal or not.";
		 }

		 leaf in-voltage {
            //type decimal64 {
                //fraction-digits 2;
            //}
			type int32;
			units "mV";
			config false;
			description
               "The input voltage(Unit: mV) of the power.";
		 }

		 leaf in-current {
		    type int32;
			units "mA";
			config false;
			description
               "The input current(Unit: mA) of the power.";
		 }
	  }
  }

  grouping sys-voltage-state {
      description
         "The grouping for the device output voltage state of powers.";

      list out-voltage {
		  key "dev-index index";
		  leaf dev-index {
		     type uint32;
		     description
			    "The index that uniquely represents a device. In stacking environments, there are multiple devices.
                 This value will indicate the device index. When in general single device environments, this value is 0.";
		  }

          leaf index {
	         type uint32;
	         description
	            "The index of output voltage status entry.";
	      }

	      leaf voltage {
	         type int32;
             units "mV";
             config false;
             description
                "The output voltage(Unit: mV) of current voltage status entry of the power.
                 If the device is no support this information, This node value will be 0.";
	      }

		  leaf status {
		      type ntos-dm:severity-level;
		      description
			     "The severity level of the voltage entry alarm.";
		  }

          description
             "The subtree indicate the output voltage status entry list of powers.";
      }
  }

  grouping sys-voltage-config {
      description
         "The grouping for device voltage configuration.";

      list voltage-entry {
	     description
            "The voltage configuration list.";
         key "dev-index index";

		 leaf dev-index {
		    type uint32;
			//config false;
		    description
			   "The index that uniquely represents a device. In stacking environments, there are multiple devices.
                This value will indicate the device index. When in general single device environments, this value is 0.";
		 }

		 leaf index {
		    type uint32;
			//config false;
		    description
			   "The index of voltage state entry.";
		 }

		 leaf warning-threshold {
		    type uint32;
		    description
			   "The warning threshold of the entry temperateture.
			    When the curent voltage exceeds the threshold, a warning notification will be sent.";
		 }

		 leaf critical-threshold {
		    type uint32;
		    description
			   "The critical threshold of the entry temperateture.
			    When the curent voltage exceeds the threshold, a critical notification will be sent.";
		 }
      }
  }

  grouping sys-fan-state {
      description
         "The grouping for device fan state.";

      list fan-entry {
	     description
            "The fan state list.";
         key "dev-index index";

		 leaf dev-index {
		    type uint32;
		    description
			   "The index that uniquely represents a device. In stacking environments, there are multiple devices.
                This value will indicate the device index. When in general single device environments, this value is 0.";
		 }

		 leaf index {
		    type uint32;
		    description
			   "The index of fan state entry.";
		 }

		 leaf status {
		    type ntos-dm:power-status;
			config false;
		    description
               "The node indicate whether the fan is normal or not.
			    If the fan is not in the corresponding slot, this node value is 0.";
		 }

	     leaf level {
	        type uint32;
		    config false;
		    description
              "The level of the fan speed.";
	     }

        leaf speed {
            type string {
                length "0..255";
            }
            config false;
            description
            "This object displays fan speed.";
        }
      }
  }

  grouping sys-temp-config {
      description
         "The grouping for device temperature configuration.";

      list temp-entry {
	     description
            "The temperature configuration list.";
         key "dev-index index";

		 leaf dev-index {
		    type uint32;
			//config false;
		    description
			   "The index that uniquely represents a device. In stacking environments, there are multiple devices.
                This value will indicate the device index. When in general single device environments, this value is 0.";
		 }

		 leaf index {
		    type uint32;
			//config false;
		    description
			   "The index of temperature state entry.";
		 }

		 leaf warning-threshold {
		    type uint32;
		    mandatory true;
		    description
			   "The warning threshold of the entry temperateture.
			    When the curent temperateture exceeds the threshold, a warning notification will be sent.";
		 }

		 leaf critical-threshold {
		    type uint32;
		    mandatory true;
		    description
			   "The critical threshold of the entry temperateture.
			    When the curent temperateture exceeds the threshold, a critical notification will be sent.";
		 }
      }
  }

  grouping sys-temp-state {
      description
         "The grouping for device temperature state.";

      list temp-entry {
	     description
            "The temperature state list.";
         key "dev-index index";

		 leaf dev-index {
		    type uint32;
		    description
			   "The index that uniquely represents a device. In stacking environments, there are multiple devices.
                This value will indicate the device index. When in general single device environments, this value is 0.";
		 }

		 leaf index {
		    type uint32;
		    description
			   "The index of temperature state entry.";
		 }

		 leaf name {
 	        type string {
		        length "0..255";
		    }
			description
               "A textual name assigned to a multiple temperature chip.";
		 }

		 leaf curr-temp {
		    type int32;
			config false;
			description
               "The node indicates current multiple temperature of the device.
                The temperature display is not supported for the current temperature returns to -255.";
		 }

         leaf status {
	        type ntos-dm:severity-level;
		    description
			   "The severity level of the temperature status alarm.";
	     }
      }
  }

  augment "/ntos:config/ntos-system:system" {
      description
         "Global system extend configuration.";

      container temperatures {
         description
            "The device temperature monitoring configuration data.";
	     uses sys-temp-config;
	  }
   }

   augment "/ntos:config/ntos-system:system" {
      description
         "Global system extend configuration.";

      container voltages {
         description
            "The device voltage monitoring configuration data.";
	     uses sys-voltage-config;
	  }
   }

   augment "/ntos:state/ntos-system:system" {
      description
         "Global system extend state.";

      container powers {
	     config false;
         description
            "The device power state data.";

		 leaf number {
            type uint32;
			config false;
	        description
			   "The total number of powers.";
		 }

		 uses sys-power-state;
	  }

      container out-voltages {
	     config false;
         description
            "The device output voltage state data.";

		 leaf number {
            type uint32;
			config false;
	        description
			   "The total number of output voltages.";
		 }

		 uses sys-voltage-state;
	  }

      container fans {
	     config false;
         description
            "The device fan state data.";

		 leaf number {
            type uint32;
			config false;
	        description
			   "The total number of fans.";
		 }

		 uses sys-fan-state;
	  }

      container temperatures {
	     config false;
         description
            "The device temperature state data.";

		 leaf number {
            type uint32;
			config false;
	        description
			   "The total number of temperature entries.";
		 }

	     uses sys-temp-state;
	  }
   }

   notification temperature-alarm {
      description
         "The temperature status change notification.";

	  leaf dev-index {
		 type uint32;
		 description
			"The index that uniquely represents a device. In stacking environments, there are multiple devices.
             This value will indicate the device index. When in general single device environments, this value is 0.";
      }

      leaf index {
		 type uint32;
		 description
			 "The index of temperature state entry.";
	  }

	  leaf name {
 	      type string {
		    length "0..255";
		  }
		  description
             "A textual name assigned to a multiple temperature chip.";
	  }

	  leaf status {
	     type ntos-dm:severity-level;
		 description
			"The severity level of the temperature status alarm.";
	  }
   }

   notification power-alarm {
      description
         "The power status change notification.";

	  leaf dev-index {
		 type uint32;
		 description
			 "The index that uniquely represents a device. In stacking environments, there are multiple devices.
              This value will indicate the device index. When in general single device environments, this value is 0.";
      }

      leaf index {
		 type uint32;
		 description
			"The index of power state entry.";
	  }

	  leaf status {
         type ntos-dm:power-status;
		 description
            "The node indicate whether the power is normal or not.";
	  }
   }

   notification fan-alarm {
      description
         "The fan status chanage notification.";

	  leaf dev-index {
		 type uint32;
		 description
			 "The index that uniquely represents a device. In stacking environments, there are multiple devices.
              This value will indicate the device index. When in general single device environments, this value is 0.";
      }

      leaf index {
		 type uint32;
		 description
			 "The index of fan state entry.";
	  }

	  leaf status {
         type ntos-dm:power-status;
		 description
            "The node indicate whether the fan is normal or not.";
	  }
   }

   notification voltage-alarm {
      description
         "The voltage status chanage notification.";

      leaf dev-index {
		 type uint32;
		 description
			 "The index that uniquely represents a device. In stacking environments, there are multiple devices.
              This value will indicate the device index. When in general single device environments, this value is 0.";
      }

      leaf index {
	     type uint32;
	     description
	         "The index of output voltage status entry.";
	  }

	  leaf voltage {
	     type int32;
         units "mV";
		 description
		     "The output voltage(Unit: mV) of current voltage status entry of the power.
			  If the device is no support this information, This node value will be 0.";
	  }

	  leaf status {
		 type ntos-dm:severity-level;
		 description
             "The severity level of the voltage entry alarm.";
	  }
   }
}
