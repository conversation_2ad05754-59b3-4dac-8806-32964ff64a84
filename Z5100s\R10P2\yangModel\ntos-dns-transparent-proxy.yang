module ntos-dns-transparent-proxy {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:dns-transparent-proxy";
  prefix ntos-dns-transparent-proxy;

  import ntos {
    prefix ntos;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description "Ruijie NTOS dns transparent proxy module.";

  revision 2023-07-25 {
    description "Initial version.";
    reference "";
  }

  identity dns-transparent-proxy {
    base ntos-types:SERVICE_LOG_ID;
    description "DNS transparent proxy service.";
    ntos-ext:nc-cli-identity-name "dns-transparent-proxy";
  }

  grouping dns-server-config {
    description "Detail configuration for a dns server group.";

    leaf preferred {
      type union {
        type ntos-inet:ipv4-filter-invalid-address;
        type ntos-inet:ipv6-address;
      }
    }
    leaf alternate {
      type union {
        type ntos-inet:ipv4-filter-invalid-address;
        type ntos-inet:ipv6-address;
      }
      must "count(../preferred) > 0" {
        error-message "The preferred must be configured first.";
      }
      must "current() != ../preferred" {
        error-message "The preferred have same configuration.";
      }
    }
  }

  grouping bind-interface-config {
    description "Detail configuration for a dns server bind interface.";

    leaf name {
      type ntos-types:ifname;
    }
    leaf enabled {
      type boolean;
      default "true";
    }
    leaf kind {
      type enumeration {
        enum physical;
        enum bond;
        enum vlan;
        enum ppp;
      }
    }
  }

  grouping dns-track-config {
    description "Detail configuration for a dns server track.";

    leaf enabled {
      type boolean;
      default "false";
    }
    leaf tx-interval {
      type uint32 {
        range "1..604800";
      }
      default "3";
    }
    leaf times {
      type uint16 {
        range "0..30";
      }
      default "3";
    }
    leaf tx-domain {
      type string;
    }
  }

  grouping policy-list-config {
    description "Detail configuration for a dns server proxy policy.";

    list policy {
      key "name";
      ordered-by user;
      leaf name {
        type ntos-types:ntos-obj-name-type {
          length "1..127";
        }
      }
      leaf description {
        type ntos-types:ntos-obj-description-type {
          length "1..255";
        }
      }
      leaf enabled {
        type boolean;
        default "true";
      }
      leaf action {
        type enumeration {
          enum tpdns;
          enum no-tpdns;
        }
        default "no-tpdns";
      }
      list source-network {
        ntos-ext:nc-cli-one-liner;
        max-elements 64;
        key "name";
        leaf name {
          type ntos-types:ntos-obj-name-type;
          ntos-ext:nc-cli-no-name;
        }
      }
      list dest-network {
        ntos-ext:nc-cli-one-liner;
        max-elements 64;
        key "name";
        leaf name {
          type ntos-types:ntos-obj-name-type;
          ntos-ext:nc-cli-no-name;
        }
      }
      list service {
        ntos-ext:nc-cli-one-liner;
        max-elements 64;
        key "name";
        leaf name {
          type ntos-types:ntos-obj-name-type;
          ntos-ext:nc-cli-no-name;
        }
      }
    }
  }

  grouping dns-server-show {
    description "Show configuration for a dns server group.";

    leaf preferred {
      type string;
    }
    leaf alternate {
      type string;
    }
  }

  grouping bind-interface-show {
    description "Show configuration for a dns server bind interface.";

    leaf name {
      type string;
    }
    leaf id {
      type uint32;
    }
    leaf enabled {
      type boolean;
    }
    leaf kind {
      type string;
    }
    leaf hits {
      description "The matching hits for this interface.";
      type uint64;
    }
  }

  grouping dns-track-show {
    description "Show configuration for a dns server track.";

    leaf enabled {
      type boolean;
    }
    leaf tx-interval {
      type uint32;
    }
    leaf times {
      type uint16;
    }
    leaf tx-domain {
      type string;
    }
  }

  grouping policy-list-show {
    list policy {
      key "name";
      ordered-by user;
      leaf name {
        type string;
      }
      leaf description {
        type string;
      }
      leaf id {
        type uint32;
      }
      leaf position-id {
        type uint32;
      }
      leaf enabled {
        type boolean;
      }
      leaf action {
        type string;
      }
      list source-network {
        ntos-ext:nc-cli-one-liner;
        key "name";
        leaf name {
          type string;
        }
      }
      list dest-network {
        ntos-ext:nc-cli-one-liner;
        key "name";
        leaf name {
          type string;
        }
      }
      list service {
        ntos-ext:nc-cli-one-liner;
        key "name";
        leaf name {
          type string;
        }
      }
      leaf hits {
        description "The matching hits for this policy.";
        type uint64;
      }
      leaf _next {
        description "The policy next node.";
        type string;
      }
      leaf _prev {
        description "The policy prev node.";
        type string;
      }
    }
  }

  grouping dns-tpp-detail {
    description "Detail of DNS transparent proxy global configuration.";

    container dns-transparent-proxy {
      description "Configuration of DNS transparent proxy.";
      leaf enabled {
        type boolean;
        default "false";
      }
      leaf mode {
        type enumeration {
          enum mllb;
          enum src-ip-hash;
          enum upload;
          enum download;
          enum both;
          enum up-bandwidth;
          enum down-bandwidth;
          // enum weight;
          // enum priority;
          // enum quality;
          // enum src-dst-ip-hash;
          // enum min-conn;
          // enum min-conn-weight;
        }
        default "mllb";
      }
      list bind-interface {
        key "name";
        description "Configuration wan interface list for DNS transparent proxy.";
        uses bind-interface-config;
        container dns-server {
          uses dns-server-config;
        }
        leaf ref-dns-track {
          type ntos-types:ntos-obj-name-type;
        }
      }
      list exclude {
        key "domain";
        description "Configuration exclude domains of DNS transparent proxy.";
        leaf domain {
          type string {
            length "3..255";
            pattern '((\w[\w\-]{0,62}\.)+(\w[\w\-]{0,62}|\*))|((\*|\w[\w\-]{0,62})\.(\w[\w\-]{0,62}\.)*(\w[\w\-]{0,62}))';
            ntos-ext:nc-cli-shortdesc "<host-name>";
          }
        }
        container dns-server {
          uses dns-server-config;
        }
      }
      uses policy-list-config;
    }
  }

  rpc dns-transparent-proxy {
    description "Show dns-transparent-proxy global configuration.";

    input {
      leaf vrf {
        description "Specify the VRF.";
        type ntos-types:ntos-obj-name-type;
      }
      choice content {
        case mode {
          leaf mode {
            type empty;
          }
        }
        case enabled {
          leaf enabled {
            type empty;
          }
        }
        case interface {
          container interface {
            ntos-ext:nc-cli-group "subcommand";
            leaf start {
              type uint16 {
                range "0..65535";
              }
            }
            leaf end {
              type uint16 {
                range "0..65535";
              }
            }
            leaf name {
              type string;
            }
            leaf filter {
              type string;
            }
            leaf id {
              type uint32;
            }
          }
        }
        case exclude {
          container exclude {
            ntos-ext:nc-cli-group "subcommand";
            leaf start {
              type uint16 {
                range "0..65535";
              }
            }
            leaf end {
              type uint16 {
                range "0..65535";
              }
            }
            leaf domain {
              type string;
            }
            leaf filter {
              type string;
            }
            leaf id {
              type uint32;
            }
          }
        }
        case policy {
          container policy {
            ntos-ext:nc-cli-group "subcommand";
            leaf start {
              type uint16 {
                range "0..65535";
              }
            }
            leaf end {
              type uint16 {
                range "0..65535";
              }
            }
            leaf name {
              type string;
            }
            leaf filter {
              type string;
            }
            leaf id {
              type string;
            }
          }
        }
      }
    }

    output {
      leaf interfaces-total {
        description "Interfaces total number.";
        type uint16;
      }
      leaf exclude-total {
        description "Exclude total number.";
        type uint16;
      }
      leaf policys-total {
        description "Policys total number.";
        type uint16;
      }
      leaf enabled {
        type boolean;
      }
      leaf mode {
        type string;
      }
      list bind-interface {
        key "name";
        uses bind-interface-show;
        container dns-server {
          uses dns-server-show;
        }
        container ref-dns-track {
          leaf name {
            type string;
          }
          leaf config-source {
            type string;
          }
        }
        leaf invalid {
          type uint8;
        }
        leaf invalid-type {
          type string;
        }
        list alarm {
          key "info";
          leaf info {
            type string;
          }
        }
      }
      list exclude {
        key "domain";
        leaf domain {
          type string;
        }
        leaf id {
          type uint32;
        }
        container dns-server {
          uses dns-server-show;
        }
      }
      uses policy-list-show;
    }

    ntos-ext:nc-cli-show "dns-transparent-proxy";
    ntos-api:internal;
  }

  rpc clear-policy-hits {
    description "Clear hit statistics of dns transparent proxy policy.";

    input {
      leaf vrf {
        description "Specify the VRF.";
        type ntos-types:ntos-obj-name-type;
      }
      choice content {
        case id {
          leaf id {
            description "Clear hit statistics of policy by id.";
            type string;
          }
        }
        case id-list {
          leaf id-list {
            description "Clear hit statistics of policy by id list [X,Y,Z...].";
            type string;
          }
        }
        case all {
          leaf all {
            description "Clear hit statistics of all policy.";
            type empty;
          }
        }
      }
    }

    ntos-ext:feature "dns-transparent-proxy";
    ntos-ext:nc-cli-flush "dns-transparent-proxy policy-hits";
  }

  rpc clear-interface-hits {
    description "Clear hit statistics of dns transparent proxy interface.";

    input {
      leaf vrf {
        description "Specify the VRF.";
        type ntos-types:ntos-obj-name-type;
      }
      choice content {
        case id {
          leaf id {
            description "Clear hit statistics of interface by ifid.";
            type string;
          }
        }
        case id-list {
          leaf id-list {
            description "Clear hit statistics of interface by ifid list [X,Y,Z...].";
            type string;
          }
        }
        case all {
          leaf all {
            description "Clear hit statistics of all interface.";
            type empty;
          }
        }
      }
    }

    ntos-ext:feature "dns-transparent-proxy";
    ntos-ext:nc-cli-flush "dns-transparent-proxy interface-hits";
  }

  rpc dnsp-search-info {
    description "Get interface info by interface name.";

    input {
      leaf vrf {
        description "Specify the VRF.";
        type ntos-types:ntos-obj-name-type;
      }
      leaf dns-server-list {
        type enumeration {
          enum all;
          enum valid;
        }
      }
      container interface {
        ntos-ext:nc-cli-group "subcommand";
        leaf name {
          type ntos-types:ifname;
        }
        leaf kind {
          type string;
        }
        leaf link-type {
          type string;
        }
      }
    }

    output {
      container interface {
        leaf name {
          type string;
        }
        leaf next-hop {
          type string;
        }
        leaf preferred {
          type string;
        }
        leaf alternate {
          type string;
        }
      }
      leaf dns-total {
        type uint16;
      }
      list dns-server-list {
        leaf server-ip {
          type string;
        }
      }
    }

    ntos-ext:nc-cli-cmd "dns-transparent-proxy search";
    ntos-api:internal;
  }

  rpc show-yams-config {
    description "Show dns transparent proxy mgmt configuration.";

    ntos-ext:nc-cli-cmd "dns-transparent-proxy show-yams-config";
    ntos-api:internal;
  }

  rpc dnsp-debug-config {
    description "Set/show the debug level of dns-transparent-proxy.";

    input {
      leaf vrf {
        description "Specify the VRF.";
        type ntos-types:ntos-obj-name-type;
      }
      choice content {
        case set {
          leaf enabled {
            type boolean;
            default "true";
          }
          leaf level {
            type enumeration {
              enum critical;
              enum error;
              enum warning;
              enum notice;
              enum info;
              enum debug;
            }
            default "error";
          }
        }
        case show {
          leaf show {
            type empty;
          }
        }
      }
    }

    output {
      leaf enabled {
        type boolean;
      }
      leaf level {
        type string;
      }
    }

    ntos-ext:nc-cli-hidden;
    ntos-ext:nc-cli-cmd "dns-transparent-proxy debug";
    ntos-api:internal;
  }

  augment "/ntos:config/ntos:vrf" {
    description "DNS transparent proxy configuration.";
    uses dns-tpp-detail;
  }

  augment "/ntos:state/ntos:vrf" {
    description "DNS transparent proxy state.";
    uses dns-tpp-detail;
  }
}
