module ntos-fast-path {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:fast-path";
  prefix ntos-fast-path;

  import ntos {
    prefix ntos;
  }
  import ntos-system {
    prefix ntos-system;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-commands {
    prefix ntos-cmd;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-api {
    prefix ntos-api;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS fast path module.";

  revision 2021-12-24 {
    description
      "Add fast path statistics.";
    reference "";
  }
  revision 2020-11-25 {
    description
      "Add netfilter cache for IPv4 and IPv6 in the advanced container.";
    reference "";
  }
  revision 2020-07-10 {
    description
      "Add the reserve-hugepages leaf to the advanced container.";
    reference "";
  }

  revision 2019-07-01 {
    description
      "Add cores for QoS.";
    reference "";
  }

  revision 2018-10-03 {
    description
      "Initial version.";
    reference "";
  }

  identity fastpath {
    base ntos-types:SERVICE_LOG_ID;
    description
      "Fast path service.";
  }

  grouping fast-path-advanced-config {
    description
      "Advanced configuration for fast path.";

    container advanced {
      description
        "Advanced configuration for fast path.";

      leaf nb-mbuf {
        type string {
          pattern '\+?[0-9]+(,\+?[0-9]+)*' {
            error-message "Format for number of mbuf is '[+]<int>(,[+]<int>)*'.";
          }
          ntos-ext:nc-cli-shortdesc "<nb-mbuf>";
        }
        description
          "Number of mbufs (network packet descriptors). The value can be
           an integer representing the total number of mbufs, an integer
           prefixed with '+' representing the number of mbufs to add to
           the automatic value. In case of NUMA, the value can be a
           per-socket list. If unset, nb-mbuf is determined automatically.";
      }

      leaf machine-memory {
        type uint32;
        units "megabytes";
        description
          "Set the memory that will be used by the fast path
           (hugepages, shm, mallocs...) so it can run on a machine with
           this amount of physical memory.";
      }

      leaf mainloop-sleep-delay {
        type uint16;
        units "microseconds";
        description
          "If set, add a sleep time after each idle mainloop turn. This
           will drastically decrease performance.";
      }

      leaf offload {
        type boolean;
        description
          "Enable or disable advanced offload features such as TSO, L4
           checksum offloading, or offload information forwarding from a
           guest to the NIC through a virtual interface. If unset, use
           default product configuration.";
      }

      leaf vlan-strip {
        type boolean;
        description
          "Strip the VLAN header from incoming frames if supported by the
           hardware. By default, vlan stripping feature is disabled.";
      }

      leaf intercore-ring-size {
        type uint16;
        description
          "Set the size of the intercore rings, used by dataplane cores
           to send messages to another dataplane core. The default size
           depends on the product.";
      }

      leaf software-txq {
        type uint16;
        must '. = 64 or . = 128 or . = 256 or . = 512 or . = 1024 or . = 2048 or
            . = 4096 or . = 8192 or . = 16384 or . = 32768' {
          error-message
            "The size of software Tx queue must be a power of 2
             greater or equal to 64.";
        }
        description
          "Set the default size of Tx software queue. This field
           must be a power of 2. Default is 0 (no software queue).";
      }

      leaf nb-rxd {
        type uint16;
        description
          "Set the default number of Rx hardware descriptors for
           Ethernet ports. The value must be accepted by all devices
           on the system. If unset, an automatic value is used.";
      }

      leaf nb-txd {
        type uint16;
        description
          "Set the default number of Tx hardware descriptors for
           Ethernet ports. The value must be accepted by all devices
           on the system. If unset, an automatic value is used.";
      }

      leaf reserve-hugepages {
        type boolean;
        description
          "Enable or disable the automatic huge pages allocation by
           the fast path. When disabled, the user is responsible for
           providing enough huge pages for the fast path to start.";
      }

      leaf ipv4-netfilter-cache {
        type boolean;
        default "true";
        description
          "Enable or disable the IPv4 netfilter cache.";
      }

      leaf ipv6-netfilter-cache {
        type boolean;
        default "true";
        description
          "Enable or disable the IPv6 netfilter cache.";
      }
    }
  }

  grouping fast-path-crypto-config {
    description
      "Fast path crypto configuration.";

    container crypto {
      description
        "Fast path crypto configuration.";

      leaf driver {
        type enumeration {
          enum multibuffer {
            description
              "Intel multibuffer library.";
          }
          enum quickassist {
            description
              "Intel quickassist.";
          }
          enum dpdk-pmd {
            description
              "DPDK crypto PMD.";
          }
          enum octeontxcpt {
            description
              "Marvell Octeon TX.";
          }
          enum octeontx2cpt {
            description
              "Marvell Octeon TX2.";
          }
        }
        description
          "Crypto driver. If unset, select automatically.";
      }

      leaf offload-core-mask {
        type union {
          type ntos-types:coremask;
          type enumeration {
            enum none {
              description
                "Disable crypto offload.";
            }
          }
        }
        description
          "Fast path cores that can do crypto operations for other fast
           path cores. It must be included in fast path mask. The crypto
           offloading is always done on cores in the same NUMA node.";
      }

      leaf nb-session {
        type uint32;
        description
          "Maximum number of cryptographic sessions.";
      }

      leaf nb-buffer {
        type uint32;
        description
          "Maximum number of cryptographic buffers, representing
           the maximum number of in-flight operations, either being
           processed by the asynchronous crypto engine, or waiting in
           crypto device queues.";
      }
    }
  }

  grouping fast-path-limits-max-params {
    description
      "Fast path limits maximum parameters.";

    leaf fp-max-if {
      type uint32 {
        range "256..50000";
      }
      description
        "Maximum number of interfaces. It includes physical ports and
         virtual interfaces like gre, vlan, ...";
      ntos-api:range-added "256..50000";
    }

    leaf fp-max-vrf {
      type uint32 {
        range "1..2176";
      }
      description
        "Maximum number of VRFs.";
      ntos-api:range-added "1..2176";
    }

    leaf ip4-max-addr {
      type uint32 {
        range "16..4000000";
      }
      description
        "Maximum number of IPv4 addresses.";
    }

    leaf ip4-max-route {
      type uint32 {
        range "16..4000000";
      }
      description
        "Maximum number of IPv4 routes.";
    }

    leaf ip4-max-neigh {
      type uint32 {
        range "16..400000";
      }
      description
        "Maximum number of IPv4 neighbors.";
    }

    leaf ip6-max-addr {
      type uint32 {
        range "16..4000000";
      }
      description
        "Maximum number of IPv6 addresses.";
    }

    leaf ip6-max-route {
      type uint32 {
        range "16..4000000";
      }
      description
        "Maximum number of IPv6 routes.";
    }

    leaf ip6-max-neigh {
      type uint32 {
        range "16..400000";
      }
      description
        "Maximum number of IPv6 neighbors.";
    }

    leaf pbr-max-rule {
      type uint32 {
        range "16..400000";
      }
      description
        "Maximum number of PBR rules.";
    }

    leaf filter4-max-rule {
      type uint32 {
        range "16..60000";
      }
      description
        "Maximum number of IPv4 Netfilter rules.";
      ntos-api:range-added "16..60000";
    }

    leaf filter6-max-rule {
      type uint32 {
        range "16..60000";
      }
      description
        "Maximum number of IPv6 Netfilter rules.";
      ntos-api:range-added "16..60000";
    }

    leaf filter4-max-ct {
      type uint32 {
        range "16..1000000";
      }
      description
        "Maximum number of IPv4 Netfilter conntracks.";
    }

    leaf filter6-max-ct {
      type uint32 {
        range "16..1000000";
      }
      description
        "Maximum number of IPv6 Netfilter conntracks.";
    }

    leaf filter-max-ipset {
      type uint32 {
        range "0..1000";
      }
      description
        "Maximum number of ipsets per VRF.";
    }

    leaf filter-max-ipset-entry {
      type uint32 {
        range "0..1000000";
      }
      description
        "Maximum number of entries per ipset.";
      ntos-api:range-added "0..1000000";
    }

    leaf filter-bridge-max-rule {
      type uint32 {
        range "0..40000";
      }
      description
        "Maximum number of bridge filter rules.";
    }

    leaf vxlan-max-port {
      type uint32 {
        range "0..128";
      }
      description
        "Maximum number of (VXLAN destination port, VRF) pairs.";
    }

    leaf vxlan-max-if {
      type uint32 {
        range "0..50000";
      }
      description
        "Maximum number of VXLAN interfaces.";
    }

    leaf vxlan-max-fdb {
      type uint32 {
        range "0..50000";
      }
      description
        "Maximum number of VXLAN forwarding database entries.";
    }

    leaf reass4-max-queue {
      type uint32 {
        range "0..10000000";
      }
      description
        "Maximum number of simultaneous reassembly procedures for IPv4.";
    }

    leaf reass6-max-queue {
      type uint32 {
        range "0..10000000";
      }
      description
        "Maximum number of simultaneous reassembly procedures for IPv6.";
    }

    leaf ipsec-max-sp {
      type uint32 {
        range "0..400000";
      }
      description
        "Maximum number of IPv4 and IPv6 IPsec SPs.";
    }

    leaf ipsec-max-sa {
      type uint32 {
        range "0..400000";
      }
      description
        "Maximum number of IPv4 and IPv6 IPsec SAs.";
    }

    leaf ip-max-8-table {
      type uint32;
      description
        "Maximum number of IPv4 and IPv6 /8 table entries.";
    }

    leaf filter-max-cache {
      type uint32 {
        range "1..100000000";
      }
      description
        "Maximum number of IPv4 flows stored in filter cache.";
    }

    leaf filter6-max-cache {
      type uint32 {
        range "0..100000000";
      }
      description
        "Maximum number of IPv6 flows stored in filter cache.";
    }

    leaf vlan-max-if {
      type uint32 {
        range "16..50000";
      }
      description
        "Maximum number of VLAN interfaces.";
    }

    leaf macvlan-max-if {
      type uint32 {
        range "16..50000";
      }
      description
        "Maximum number of MACVLAN (VRRP) interfaces.";
    }

    leaf gre-max-if {
      type uint32 {
        range "16..50000";
      }
      description
        "Maximum number of GRE interfaces.";
    }

    leaf svti-max-if {
      type uint32 {
        range "16..50000";
      }
      description
        "Maximum number of SVTI interfaces.";
    }
  }

  grouping fast-path-limits {
    description
      "Global runtime limits for fast path.";

    container limits {
      description
        "Global runtime limits for fast path.";
      uses fast-path-limits-max-params;
    }
  }

  grouping fast-path-limits-state {
    description
      "Global runtime limits for fast path.";

    container limits {
      description
        "Global runtime limits for fast path.";

      leaf fp-cur-if {
        type uint32;
        description
          "Current number of interfaces. It includes physical ports and
           virtual interfaces like gre, vlan, ...";
      }

      leaf fp-cur-vrf {
        type uint32;
        description
          "Current number of VRFs.";
      }

      leaf ip4-cur-addr {
        type uint32;
        description
          "Current number of IPv4 addresses.";
      }

      leaf ip4-cur-route {
        type uint32;
        description
          "Current number of IPv4 routes.";
      }

      leaf ip4-cur-neigh {
        type uint32;
        description
          "Current number of IPv4 neighbors.";
      }

      leaf ip6-cur-addr {
        type uint32;
        description
          "Current number of IPv6 addresses.";
      }

      leaf ip6-cur-route {
        type uint32;
        description
          "Current number of IPv6 routes.";
      }

      leaf ip6-cur-neigh {
        type uint32;
        description
          "Current number of IPv6 neighbors.";
      }

      leaf pbr-cur-rule {
        type uint32;
        description
          "Current number of PBR rules.";
      }

      leaf filter4-cur-rule {
        type uint32;
        description
          "Current number of IPv4 Netfilter rules.";
      }

      leaf filter6-cur-rule {
        type uint32;
        description
          "Current number of IPv6 Netfilter rules.";
      }

      leaf filter4-cur-ct {
        type uint32;
        description
          "Current number of IPv4 Netfilter conntracks.";
      }

      leaf filter6-cur-ct {
        type uint32;
        description
          "Current number of IPv6 Netfilter conntracks.";
      }

      leaf filter-cur-ipset {
        type uint32;
        description
          "Current number of ipsets per VRF.";
      }

      leaf vxlan-cur-port {
        type uint32;
        description
          "Current number of (VXLAN destination port, VRF) pairs.";
      }

      leaf vxlan-cur-if {
        type uint32;
        description
          "Current number of VXLAN interfaces.";
      }

      leaf vxlan-cur-fdb {
        type uint32;
        description
          "Current number of VXLAN forwarding database entries.";
      }

      leaf ipsec-cur-sp {
        type uint32;
        description
          "Current number of IPv4 and IPv6 IPsec SPs.";
      }

      leaf ipsec-cur-sa {
        type uint32;
        description
          "Current number of IPv4 and IPv6 IPsec SAs.";
      }

      leaf ip-cur-8-table {
        type uint32;
        description
          "Current number of IPv4 and IPv6 /8 table entries.";
      }

      leaf vlan-cur-if {
        type uint32;
        description
          "Current number of VLAN interfaces.";
      }

      leaf macvlan-cur-if {
        type uint32;
        description
          "Current number of MACVLAN (VRRP) interfaces.";
      }

      leaf gre-cur-if {
        type uint32;
        description
          "Current number of GRE interfaces.";
      }

      leaf svti-cur-if {
        type uint32;
        description
          "Current number of SVTI interfaces.";
      }
      uses fast-path-limits-max-params;
    }
  }

  grouping linux-sync {
    description
      "Advanced tuning for fast path / Linux synchronization.";

    container linux-sync {
      must "count(disable[.='conntrack']) = 0 or count(disable[.='nat']) = 1" {
        error-message "Nat cannot work without conntrack.";
      }
      must "count(disable[.='firewall']) = 0 or count(disable[.='conntrack']) = 1" {
        error-message "Conntrack cannot work without firewall.";
      }
      must "count(disable[.='ipsec']) = 0 or count(disable[.='svti']) = 1" {
        error-message "SVTI cannot work wirout IPsec.";
      }
      description
        "Advanced tuning for fast path / Linux synchronization.";

      leaf fpm-socket-size {
        type uint32 {
          range "4096..268435456";
        }
        units "bytes";
        default "2097152";
        description
          "Buffer size of the socket used to communicate between the
           cache manager and the fast path manager.";
      }

      leaf nl-socket-size {
        type uint32 {
          range "4096..268435456";
        }
        units "bytes";
        default "67108864";
        description
          "Buffer size of the cache manager netlink socket.";
      }

      leaf ipset-dump-delay {
        type uint32;
        units "seconds";
        default "1";
        description
          "Delay period for polling the ipset content.";
      }

      leaf-list disable {
        type enumeration {
          enum bpf {
            description
              "Disable BPF synchronization (used by traffic capture).";
          }
          enum bridge {
            description
              "Disable bridge interface synchronization.";
          }
          enum conntrack {
            description
              "Disable connection tracking synchronization.";
          }
          enum firewall {
            description
              "Disable firewall synchronization.";
          }
          enum gre {
            description
              "Disable GRE interface synchronization.";
          }
          enum ipip {
            description
              "Disable IP in IP interface synchronization.";
          }
          enum ipsec {
            description
              "Disable IPsec synchronization.";
          }
          enum ipset4 {
            description
              "Disable IPv4 ipset synchronization (used by firewall IPv4 address/network groups).";
          }
          enum ipset6 {
            description
              "Disable IPv6 ipset synchronization (used by firewall IPv6 address/network groups).";
          }
          enum ipv6 {
            description
              "Disable IPv6 synchronization.";
          }
          enum lag {
            description
              "Disable LAG interface synchronization.";
          }
          enum macvlan {
            description
              "Disable MACVLAN interface synchronization (used by VRRP).";
          }
          enum mpls {
            description
              "Disable MPLS synchronization.";
          }
          enum nat {
            description
              "Disable NAT synchronization.";
          }
          enum svti {
            description
              "Disable SVTI interface synchronization.";
          }
          enum vlan {
            description
              "Disable VLAN interface synchronization.";
          }
          enum vxlan {
            description
              "Disable VXLAN interface synchronization.";
          }
        }
        description
          "Disable synchronization for specific modules.";
      }
    }
  }

  grouping fast-path-config {
    description
      "Configuration for fast path.";

    leaf-list port {
      type union {
        type ntos-types:pci-port-name;
        type ntos-types:device-tree-port-name;
      }
      description
        "A physical network port managed by the fast path.";
      ntos-api:pattern-added "pci-(d[0-9]+)?(b[0-9]+)(s[0-9]+)(f[0-9]+)?(p[0-9]+)?";
      ntos-api:pattern-added "dt-(.+)";
      ntos-ext:nc-cli-completion-xpath
        "/ntos:state/ntos-network-ports:network-port/ntos-network-ports:name";
    }

    container core-mask {
      description
        "Dedicate cores to fast path or exception path.";

      leaf fast-path {
        type union {
          type enumeration {
            enum max {
              description
                "Dedicate the maximum number of cores to the fast path.";
            }
            enum half {
              description
                "Dedicate half of the cores to the fast path.";
            }
            enum min {
              description
                "Dedicate the minimum number of cores to the fast path.";
            }
          }
          type ntos-types:coremask;
        }
        description
          "List of cores dedicated to fast path.";
      }

      leaf exception {
        type ntos-types:coremask;
        description
          "Control plane cores allocated to exception packets processing. If
           unset, use the first non fast path core.";
      }

      leaf linux-to-fp {
        type ntos-types:coremask;
        description
          "Fast path cores that can receive packets from Linux. It must
           be included in fast path mask. If unset, all fast path cores can
           receive packets from Linux.";
      }

      leaf qos {
        type ntos-types:coremask;
        description
          "Fast path cores dedicated for qos schedulers. These cores do not
           received any packets from the NIC or Linux.";
      }

      leaf port {
        type string {
          pattern 'c[0-9]+=[0-9]+(:[0-9]+)*(/c[0-9]+=[0-9]+(:[0-9]+)*)*' {
            error-message "Invalid core port mapping. Example: 'c1=0:1/c2=2/c3=0:1:2'.";
          }
        }
        description
          "Map fast path cores with network ports, specifying which
             logical cores poll which ports. Example:
             'c1=0:1/c2=2/c3=0:1:2' means the logical core 1 polls the
             port 0 and 1, the core 2 polls the port 2, and the core 3
             polls the ports 0, 1, and 2. If unset, each port is polled
             by all the logical cores of the same socket.";
        ntos-ext:nc-cli-shortdesc "<core-port-map>";
      }
    }
  }

  grouping cp-protection-config {
    description
      "Configuration for control plane protection.";

    container cp-protection {
      description
        "Control plane protection configuration.";

      leaf budget {
        type int16 {
          range "0..100";
        }
        default "10";
        description
          "Maximum CPU usage allowed for Control Plane Protection in percent.";
      }
    }
  }

  rpc show-fast-path-cpu-usage {
    description
      "Show the fast path CPU usage.";
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-ext:feature "product";
    ntos-ext:nc-cli-show "fast-path cpu-usage";
    ntos-api:internal;
  }

  rpc show-fast-path-table-usage {
    description
      "Show the fast path table usage.";
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-ext:feature "product";
    ntos-ext:nc-cli-show "fast-path table-usage";
    ntos-api:internal;
  }

  rpc show-fast-path-statistics {
    description
      "Show the fast path statistics.";
    input {
      must 'count(*) <= 1' {
        error-message "Should not more than one option.";
      }
      leaf all {
        type empty;
        description
          "all statistics";
      }
      leaf global {
        type empty;
        description
          "global statistics";
      }
      leaf iface {
        type empty;
        description
          "iface statistics";
      }
      leaf port {
        type empty;
        description
          "port statistics";
      }
      leaf reset {
        type empty;
        description
          "reset statistics";
      }
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-ext:feature "product";
    ntos-ext:nc-cli-show "fast-path statistics";
    ntos-api:internal;
  }

  augment "/ntos:config/ntos-system:system" {
    description
      "Fast path configuration.";

    container fast-path {
      must "enabled = 'false' or count(port) > 0" {
        error-message "At least one port must be selected.";
      }
      presence "Makes fast-path available";
      description
        "Fast path configuration.";
      ntos-ext:feature "product";

      leaf enabled {
        type boolean;
        default "true";
        description
          "Enable or disable the fast path.";
      }
      uses fast-path-config;
      uses cp-protection-config;
      uses fast-path-crypto-config;
      uses fast-path-advanced-config;
      uses fast-path-limits;
      uses linux-sync;
    }
  }

  augment "/ntos:state/ntos-system:system" {
    description
      "Fast path configuration.";

    container fast-path {
      presence "Makes fast path available";
      description
        "Fast path configuration.";
      ntos-ext:feature "product";

      leaf enabled {
        type union {
          type boolean;
          type enumeration {
            enum failed {
              description
                "Service has failed.";
            }
            enum starting {
              description
                "Service is starting.";
            }
            enum stopping {
              description
                "Service is stopping.";
            }
          }
        }
        description
          "Fast path state.";
      }
      uses fast-path-config;

      list cpu-usage {
        key "cpu";
        description
          "The list of busy percentage per CPU.";

        leaf cpu {
          type string;
          description
            "The CPU number.";
        }

        leaf busy {
          type uint16;
          description
            "The busy percentage.";
        }
      }
      uses cp-protection-config;
      uses fast-path-crypto-config;
      uses fast-path-advanced-config;
      uses fast-path-limits-state;
      uses linux-sync;
    }
  }
}
