module ntos-flow-audit {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:flow-audit";
  prefix ntos-flow-audit;

  import ntos {
    prefix ntos;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-system {
    prefix ntos-system;
  }
  import ntos-user-management {
    prefix ntos-user-management;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS flow audit module.";

  revision 2022-09-07 {
    description
      "create file.";
    reference "revision 2022-09-07.";
  }

  identity flow-audit {
    base ntos-types:SERVICE_LOG_ID;
    description
      "Flow-audit service.";
    ntos-ext:nc-cli-identity-name "flow-audit";
  }

  typedef percent {
    type uint8 {
      range "1..100";
    }
  }

  grouping flow-info {
    description
      "flow info include up/down flow and drop info";

      leaf up-flow {
        description
          "up flow rate";
        type uint64;
      }
      leaf down-flow {
        description
          "down flow rate";
        type uint64;
      }
      leaf up-drop-flow {
        description
          "up drow flow rate";
        type uint64;
      }
      leaf down-drop-flow {
        description
          "down drop flow rate";
        type uint64;
      }
      leaf vup-flow {
        description
          "vpn up flow rate";
        type uint64;
      }
      leaf vdown-flow {
        description
          "vpn down flow rate";
        type uint64;
      }
      leaf vup-drop-flow {
        description
          "vpn up drow flow rate";
        type uint64;
      }
      leaf vdown-drop-flow {
        description
          "vpn down drop flow rate";
        type uint64;
      }
  }

  grouping system-flow-audit-config {
    description
      "Configuration data for flow audit.";
    container flowrate {
      description
        "Configuration for real-time traffic statistics collection.";
      leaf enable {
        type boolean;
        description
          "Enable or disable real-time traffic statistics collection.";
      }
    }

    container session {
      description
        "Configuration for session statistics.";
      leaf enable {
        type boolean;
        description
          "Enable or disable session statistics.";
      }
    }

    container flowtotal {
      description
        "Configuration for flow throughput statistics.";
      leaf enabled {
        type boolean;
        description
          "Enable or disable flow throughput statistics.";
      }
    }

    container flowspeed {
      description
        "Configuration for flow speed statistics.";
      leaf enabled {
        type boolean;
        description
          "Enable or disable flow speed statistics.";
      }
    }

    container real-time-session {
      description
        "Configuration for real-time session statistics.";
      leaf enabled {
        type boolean;
        description
          "Enable or disable real-time session statistics.";
      }
    }
  }


  grouping system-hard-disk-config {
    leaf hard-disk-quota {
      type percent {
        range "15..50";
      }
      default 20;
      description
        "Maximum percentages of the hard disk that flow audit can use.";
    }
  }

  grouping system-top-n-refresh-time {
    leaf refresh-time {
      type uint32;
      default 30;
      description
        "Scheduled refresh time.";
    }
  }

  rpc show-enable-status {
    description
      "Show enable status of flow audit.";
    output {
      container flowrate {
        description
          "Configuration for real-time traffic statistics collection.";
        leaf enable {
          type boolean;
          description
            "Enabling status real-time traffic statistics collection.";
        }
      }
      container session {
        description
          "Configuration of session statistics.";
        leaf enable {
          type boolean;
          description
            "Enabling status of session statistics.";
        }
      }
      container flowtotal {
        description
          "Configuration of flow throughput statistics.";
        leaf enabled {
          type boolean;
          description
            "Enabling status of flow throughput statistics.";
        }
      }
      container flowspeed {
        description
          "Configuration of flow speed statistics.";
        leaf enabled {
          type boolean;
          description
            "Enabling status of flow speed statistics.";
        }
      }
      container log2cloud {
        description
          "Configuration of log upload to cloud.";
        leaf enabled {
          type boolean;
          description
            "Enabling status of log upload to cloud.";
        }
      }
      container real-time-session {
        description
          "Configuration of real-time session.";
        leaf enabled {
          type boolean;
          description
            "Enabling status of real-time session.";
        }
      }
      container flow-audit-service {
        description
          "Configuration of flow audit service.";
        leaf runstatus {
          type boolean;
          description
            "Enabling status of flow audit service.";
        }
      }
    }

    ntos-ext:nc-cli-show "flow audit enable status";
  }

  rpc show-operate-state {
    description
      "Show operate state.";
    output {
      leaf operate-state {
        type string;
        description
          "Flow audit operate state.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }

    ntos-ext:nc-cli-show "flow audit operate state";
  }

  rpc show-ip-real-time-rate {
    description
      "Show ip real-time rate.";

    input {
      leaf ip-list {
        description
          "IP addresses used to query the rate.";
        type string;
      }
    }

    output {
      list ip-info {
        leaf ip {
          type string;
        }
        leaf up {
          type uint64;
        }
        leaf down {
          type uint64;
        }
      }
    }

    ntos-ext:nc-cli-show "flow audit ip real-time rate";
  }

  rpc all-switch-off {
    description "Turn off all flow audit functions.";
    input {
      leaf enabled {
        type boolean;
      }
    }
    ntos-ext:nc-cli-cmd "flow audit all-switch-off";
  }


  rpc inquire-ip-app-snapshoot {
    input {
      leaf vrf {
        description
          "Specify the VRF.";

        type ntos:vrf-name;
        ntos-ext:nc-cli-completion-xpath "/ntos:state/ntos:vrf/ntos:name";
      }
      leaf token {
        description
          "Unique value.";
        type string;
      }
      container filter {
        description
          "Search filter.";

        leaf app-name {
          description
            "The name of application.";

          type ntos-types:ntos-obj-name-type;
        }

        leaf user-name {
          description
            "The user name.";
          type ntos-user-management:user-group-path;
        }

        leaf user-id {
          description
            "The user_id.";
          type uint32;
        }

        leaf ip-address {
          description
            "ip-address.";
          type union {
            type ntos-inet:ipv4-address;
            type ntos-inet:ipv6-address;
          }
        }

        leaf interface-name {
          description
            "Specify interface.";

          type ntos-types:ifname;
        }

        leaf search-type {
          description
            "Match type.";

          type enumeration {
            enum ip;
            enum app;
            enum user;
            enum ip-app;
            enum user-app;
            enum app-ip;
            enum app-user;
            enum app-ip-user;
          }
        }

      }

    }

    output {
      leaf operate-state {
        type string;
        description
          "Flow audit operate state.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }

    ntos-ext:nc-cli-cmd "flow-audit inquire-ip-app-snapshoot";
  }

  rpc inquire-ip-app-snapshoot-status {
    input {
      leaf vrf {
        description
          "Specify the VRF.";

        type ntos:vrf-name;
        ntos-ext:nc-cli-completion-xpath "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf token {
        description
          "Unique value.";
        type string;
      }

      leaf sort-rule {
        description
          "flow info sort rule";
        type enumeration {
          enum up;
          enum down;
          enum total;
        }
      }

      leaf fuzzy-name {
        description
          "The query name in snap entry.";

        type string;
      }


      leaf start {
        description
          "snap start idx";
        type uint32;
        default 0;
      }

      leaf end {
        description
          "snap end idx";
        type uint32;
        default 19;
      }
    }

    output {
      leaf snap-status {
        description
          "Saving the status code of the snapshot";
        type uint32;
      }
      leaf snap-name {
        description
          "snap-name";
        type string;
      }
      leaf timestamp {
        description
          "snap timestamp";
        type string;
      }
      container global-flow-info {
        description
          "snap global flow info";
        leaf snap-count {
          description
            "snap info list count";
          type uint32;
        }
        uses flow-info;
      }
      list snap-info-list {
        leaf user {
          type string;
        }
        leaf ip {
          type string;
        }
        leaf app {
          type string;
        }
        leaf top {
          type uint32;
        }
        uses flow-info;
      }
    }

    ntos-ext:nc-cli-command-no-pager;
    ntos-ext:nc-cli-show "flow audit ip-app-snapshoot-status";
  }


  rpc drop-ip-app-snapshoot {
    input {
      leaf vrf {
        description
          "Specify the VRF.";

        type ntos:vrf-name;
        ntos-ext:nc-cli-completion-xpath "/ntos:state/ntos:vrf/ntos:name";
      }
      leaf token {
        description
          "Unique value.";
        type string;
      }
    }

    output {
      leaf operate-state {
        type string;
        description
          "Flow audit operate state.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }

    ntos-ext:nc-cli-cmd "flow-audit drop-ip-app-snapshoot";
  }

  rpc show-snap-info {
    description
      "Show snap info.";
    output {
      leaf operate-state {
        type string;
        description
          "Flow audit operate state.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }

    ntos-ext:nc-cli-show "flow-audit snap-info";
  }

  augment "/ntos:config/ntos-system:system" {
    description
      "Configuration data for the flow audit.";
    container flow-audit {
      description
        "Flow audit parameter configuration.";
      ntos-ext:feature "product";
      uses system-flow-audit-config;
      uses system-hard-disk-config;
      uses system-top-n-refresh-time;
    }
  }

  augment "/ntos:state/ntos-system:system" {
    description
      "The state of flow audit.";
    container flow-audit {
      description
        "Flow audit parameter configuration.";
      ntos-ext:feature "product";
      uses system-flow-audit-config;
      uses system-hard-disk-config;
      uses system-top-n-refresh-time;
    }
  }
}