module ntos-ipip {
  namespace "urn:ruijie:ntos:params:xml:ns:yang:ipip";
  prefix ntos-ipip;

  import extra-conditions {
    prefix ext-cond;
  }
  import ntos {
    prefix ntos;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-ip {
    prefix ntos-ip;
  }
  import ntos-if-types {
    prefix ntos-if;
  }
  import ntos-interface {
    prefix ntos-interface;
  }
  import ntos-tunnel {
    prefix ntos-tunnel;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-qos {
    prefix ntos-qos;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS ipip tunnel interfaces.";

  revision 2018-10-15 {
    description
      "Initial version.";
    reference "";
  }

  identity ipip {
    base ntos-types:INTERFACE_TYPE;
    description
      "Ipip interface.";
  }

  augment "/ntos:config/ntos:vrf/ntos-interface:interface" {
    description
      "Network ipip configuration.";

    list ipip {
      must 'not(count(tos) and contains(local, ":"))' {
        error-message
          "tos can not be defined for tunnels encapsulates traffic in an explicit IPv6 tunnel";
      }
      key "name";
      description
        "The list of ipip interfaces on the device.";
      ntos-ext:feature "product";
      uses ntos-interface:nonphy-interface-config;
      uses ntos-ip:ntos-ipv4-ptp-config;
      uses ntos-ip:ntos-ipv6-ptp-config;
      uses ntos-ip:ntos-network-stack-parameters;
      uses ntos-tunnel:tunnel-config;
      uses ntos-qos:logical-if-qos-config;
      ext-cond:unique-tuple "local remote link-vrf" {
        error-message "The (local, remote, link-vrf) tuple must be unique.";
      }
      ntos-ext:data-not-sync;
    }
  }

  augment "/ntos:state/ntos:vrf/ntos-interface:interface" {
    description
      "Network ipip operational state data.";

    list ipip {
      key "name";
      description
        "The list of ipip interfaces on the device.";
      ntos-ext:feature "product";
      uses ntos-interface:interface-state;
      uses ntos-ip:ntos-ipv4-ptp-state;
      uses ntos-ip:ntos-ipv6-ptp-state;
      uses ntos-ip:ntos-network-stack-parameters;
      uses ntos-tunnel:tunnel-config;
      uses ntos-if:interface-common-state;
      uses ntos-if:interface-counters-state;
      uses ntos-qos:logical-if-qos-state;
    }
  }
}
