module ntos-isp {
  namespace "urn:ruijie:ntos:params:xml:ns:yang:isp";
  prefix ntos-isp;

  import ntos {
    prefix ntos;
  }

  import ntos-interface {
    prefix ntos-interface;
  }
  import ntos-system {
    prefix ntos-system;
  }

  import ntos-if-types {
    prefix ntos-if-types;
  }

  import ntos-inet-types {
    prefix ntos-inet-types;
  }

  import ntos-types {
    prefix ntos-types;
  }

  import ntos-extensions {
    prefix ntos-ext;
  }

  import ntos-network-obj {
    prefix ntos-network-obj;
  }

  import ntos-security-zone {
    prefix ntos-security-zone;
  }

  import ntos-vlan {
    prefix ntos-vlan;
  }

  import ntos-lag {
    prefix ntos-lag;
  }

  organization
    "Ruijie Networks Co., Ltd";
  contact
    "Tel: 4008-111-000
    E-mail: <EMAIL>";
  description
    "Ruijie NTOS local defend module.";

  revision 2022-02-21 {
    description
      "Create initial version.";
  }

  identity local-defend {
    base ntos-types:SERVICE_LOG_ID;
    description
      "Local denfed service.";
  }

  typedef network-obj {
    type ntos-types:ntos-obj-name-type;
  }

  typedef ipv4-address-type {
    type union {
      type ntos-inet-types:ipv4-address;
      type ntos-inet-types:ipv4-prefix;
      type ntos-inet-types:ipv4-range;
      type ntos-inet-types:ipv4-mask;
    }
  }

  grouping cmd-output-buffer {
    leaf buffer {
      description
        "Command output buffer since last request.";

      type string;
      ntos-ext:nc-cli-stdout;
      ntos-ext:nc-cli-hidden;
    }
  }

  grouping vrf {
    leaf vrf {
      description
        "VRF name.";

      type string;
      default "main";
      ntos-ext:nc-cli-completion-xpath
        "/ntos:state/ntos:vrf/ntos:name";
    }
  }

  grouping library-config {
    leaf name {
      description
        "The name of isp.";
      type ntos-types:ntos-obj-name-type;
    }
    leaf description {
      description
        "description information (1-63 characters).";
      type ntos-types:ntos-obj-description-type;
      default "";
    }
  }

  grouping isp-config {
    container isp-config {
      list isp-name {
        key "name";
        ntos-ext:nc-cli-one-liner;
        leaf name {
          description
            "The name of isp.";
          type ntos-types:ntos-obj-name-type;
        }
      }
      list isp-route {
        key "name";
        leaf name {
          description
            "The route of isp.";
          type ntos-types:ntos-obj-name-type;
        }
      }
    }
  }

  rpc isp-ip-add {
    description
      "Add an user define ip.";

    input {
      uses vrf;
      leaf name {
        description
          "The name of isp";

        type string;
        ntos-ext:nc-cli-no-name;
      }

      list ip-list {
        key "address";
        leaf address {
          description
            "IPv4 address.";

          type ipv4-address-type;
        }
      }
    }

    output {
      uses cmd-output-buffer;
    }
    ntos-ext:nc-cli-command-no-pager;
    ntos-ext:nc-cli-cmd "isp add-ip";
  }

  rpc isp-ip-delete {
    description
      "Delete an user define ip.";

    input {
      uses vrf;
      leaf name {
        description
          "The name of isp.";

        type string;
        ntos-ext:nc-cli-no-name;
      }

      list ip-list {
        key "address";
        leaf address {
          description
            "IPv4 address.";

          type ipv4-address-type;
        }
      }
    }

    output {
      uses cmd-output-buffer;
    }
    ntos-ext:nc-cli-command-no-pager;
    ntos-ext:nc-cli-cmd "isp delete-ip";
  }

  rpc isp-import-file {
    description
      "Import user defined ISP file.";

    input {
      uses vrf;
      leaf name {
        description
          "The name of isp.";

        type string;
        ntos-ext:nc-cli-no-name;
      }

      leaf path {
        description
          "The file path.";

        type string;
      }
    }

    output {
      uses cmd-output-buffer;
    }
    ntos-ext:nc-cli-command-no-pager;
    ntos-ext:nc-cli-cmd "isp import";
  }

  rpc isp-export-file {
    description
      "Export user defined ISP file.";

    input {
      uses vrf;
      leaf name {
        description
          "The name of isp.";

        type string;
        ntos-ext:nc-cli-no-name;
      }
    }

    output {
      uses cmd-output-buffer;
    }
    ntos-ext:nc-cli-command-no-pager;
    ntos-ext:nc-cli-cmd "isp export";
  }

  rpc show-isp-info {
    description
      "Show all ISP library info.";

    input {
      uses vrf;
      leaf format {
        description
          "Format of output buffer.";
        type enumeration {
          enum text;
          enum json;
        }
        default "text";
        ntos-ext:nc-cli-no-name;
      }

      leaf start {
        description
          "Start offset of result.";
        type uint32;
      }

      leaf end {
        description
          "End offset of result.";
        type uint32;
      }
    }

    output {
      uses cmd-output-buffer;
    }
    ntos-ext:nc-cli-command-no-pager;
    ntos-ext:nc-cli-show "isp info";
  }

  rpc show-isp-list {
    description
      "Show ISP library list.";

    input {
      uses vrf;
      leaf format {
        description
          "Format of output buffer.";
        type enumeration {
          enum text;
          enum json;
        }
        default "text";
        ntos-ext:nc-cli-no-name;
      }

      leaf name {
        description
          "The name of isp.";

        type string;
      }

      leaf filter {
        description
          "Filter of isp name.";

        type string;
      }

      leaf start {
        description
          "Start offset of result.";
        type uint32;
      }

      leaf end {
        description
          "End offset of result.";
        type uint32;
      }
    }

    output {
      uses cmd-output-buffer;
    }
    ntos-ext:nc-cli-command-no-pager;
    ntos-ext:nc-cli-show "isp list";
  }

  rpc show-isp-default {
    description
      "Show all default ISP library.";

    input {
      uses vrf;
      leaf format {
        description
          "Format of output buffer.";
        type enumeration {
          enum text;
          enum json;
        }
        default "text";
        ntos-ext:nc-cli-no-name;
      }
    }

    output {
      uses cmd-output-buffer;
    }
    ntos-ext:nc-cli-command-no-pager;
    ntos-ext:nc-cli-show "isp default";
  }

  rpc show-isp-ip-list {
    description
      "Show ip list.";

    input {
      uses vrf;
      leaf name {
        description
          "The name of isp.";

        type string;
        ntos-ext:nc-cli-no-name;
      }

      leaf format {
        description
          "Format of output buffer.";
        type enumeration {
          enum text;
          enum json;
        }
        default "text";
      }

      leaf address {
        description
          "IPv4 address.";
        type ipv4-address-type;
      }

      leaf filter {
        description
          "Filter of ip address.";
        type string;
      }

      leaf start {
        description
          "Start offset of result.";
        type uint32;
      }

      leaf end {
        description
          "End offset of result.";
        type uint32;
      }
    }

    output {
      uses cmd-output-buffer;
    }
    ntos-ext:nc-cli-command-no-pager;
    ntos-ext:nc-cli-show "isp ip-list";
  }

  rpc show-isp-route {
    description
      "Show isp route.";

    input {
      uses vrf;
      leaf format {
        description
          "Format of output buffer.";

        type enumeration {
          enum text;
          enum json;
        }
        default "text";
        ntos-ext:nc-cli-no-name;
      }

      leaf start {
        description
          "Start offset of result.";
        type uint32;
      }

      leaf end {
        description
          "End offset of result.";
        type uint32;
      }

      leaf name {
        description
          "The name of isp.";

        type string;
      }

      leaf interface {
        description
          "The ISP route of the interface.";

        type ntos-types:ifname;
      }

      leaf address {
        description
          "IPv4 address.";

        type ntos-inet-types:ipv4-address;
      }

      leaf fuzzy {
        description
          "Fuzzy query.";

        type string;
      }
    }

    output {
      uses cmd-output-buffer;
    }
    ntos-ext:nc-cli-command-no-pager;
    ntos-ext:nc-cli-show "isp route";
  }

  rpc show-isp-ip-capacity {
    description
      "Show ip capacity.";

    input {
      uses vrf;

      leaf format {
        description
          "Format of output buffer.";
        type enumeration {
          enum text;
          enum json;
        }
        default "text";
        ntos-ext:nc-cli-no-name;
      }
    }

    output {
      uses cmd-output-buffer;
    }
    ntos-ext:nc-cli-command-no-pager;
    ntos-ext:nc-cli-hidden;
    ntos-ext:nc-cli-show "isp ip-capacity";
  }

  rpc show-isp-statistics {
    description
      "Show isp statistics.";

    input {
      uses vrf;
    }

    output {
      uses cmd-output-buffer;
    }
    ntos-ext:nc-cli-command-no-pager;
    ntos-ext:nc-cli-hidden;
    ntos-ext:nc-cli-show "isp statistics";
  }

  rpc show-isp-ip-state {
    description
      "Show ip state.";

    input {
      uses vrf;
      leaf name {
        description
          "The name of isp.";

        type string;
        ntos-ext:nc-cli-no-name;
      }

      leaf start {
        description
          "Start offset of result.";
        type uint32;
      }

      leaf end {
        description
          "End offset of result.";
        type uint32;
      }
    }

    output {
      uses cmd-output-buffer;
    }
    ntos-ext:nc-cli-command-no-pager;
    ntos-ext:nc-cli-hidden;
    ntos-ext:nc-cli-show "isp ip-state";
  }

  rpc isp-update {
    description
      "Update default isp.";
    input {
      leaf id {
        description
          "Id of upgrade process.";
        type uint32;
      }
      leaf path {
        description
          "The path of the file.";
        type string;
      }
    }

    output {
      leaf id {
        description
          "Id of upgrade.";
        type uint32;
      }
      leaf progress {
        description
          "Progress of upgrade.";
        type uint32;
      }
      leaf errnum {
        description
          "Error code.";
        type uint32;
      }
    }

    ntos-ext:nc-cli-hidden;
    ntos-ext:nc-cli-cmd "isp update";
  }

  rpc isp-ip-split {
    description
      "Split ip range.";

    input {
      leaf address {
        description
          "IPv4 address.";

        type ntos-inet-types:ipv4-range;
        ntos-ext:nc-cli-no-name;
      }
    }

    output {
      uses cmd-output-buffer;
    }
    ntos-ext:nc-cli-command-no-pager;
    ntos-ext:nc-cli-hidden;
    ntos-ext:nc-cli-show "isp split-ip";
  }

  augment "/ntos:config/ntos:vrf" {
    container isp {
      leaf distance {
        description
          "Distance value for this route.";
        type uint8 {
          range "1..255";
        }
        default "10";
      }
      list library {
        description
          "The isp library configuration.";
        key "name";
        uses library-config;
      }
      ntos-ext:data-not-sync;
    }
  }

  augment "/ntos:state/ntos:vrf" {
    container isp {
      leaf distance {
        description
          "Distance value for this route.";
        type uint8 {
          range "1..255";
        }
        default "10";
      }
      list library {
        key "name";
        uses library-config;
      }
    }
  }

  augment "/ntos:config/ntos:vrf/ntos-interface:interface/ntos-interface:physical" {
    uses isp-config;
  }

  augment "/ntos:state/ntos:vrf/ntos-interface:interface/ntos-interface:physical" {
    uses isp-config;
  }

  augment "/ntos:config/ntos:vrf/ntos-interface:interface/ntos-interface:physical/ntos-interface:ipv4/ntos-interface:pppoe/ntos-interface:connection" {
    uses isp-config;
  }

  augment "/ntos:state/ntos:vrf/ntos-interface:interface/ntos-interface:physical/ntos-interface:ipv4/ntos-interface:pppoe/ntos-interface:connection" {
    uses isp-config;
  }

  augment "/ntos:config/ntos:vrf/ntos-interface:interface/ntos-vlan:vlan" {
    uses isp-config;
  }

  augment "/ntos:state/ntos:vrf/ntos-interface:interface/ntos-vlan:vlan" {
    uses isp-config;
  }

  augment "/ntos:config/ntos:vrf/ntos-interface:interface/ntos-lag:lag" {
    uses isp-config;
  }

  augment "/ntos:state/ntos:vrf/ntos-interface:interface/ntos-lag:lag" {
    uses isp-config;
  }
}
