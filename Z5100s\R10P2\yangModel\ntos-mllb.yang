module ntos-mllb {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:mllb";
  prefix ntos-mllb;

  import ntos {
    prefix ntos;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description "Ruijie NTOS mllb module.";

  revision 2024-08-21 {
    description "Add algorithms for Improving Quality.";
    reference "";
  }
  revision 2023-11-14 {
    description "Add mllb arithmetic: src-ip/dst-ip-hash, min-conn.";
    reference "";
  }
  revision 2022-05-22 {
    description "Initial version.";
    reference "";
  }

  identity mllb {
    base ntos-types:SERVICE_LOG_ID;
    description "Mllb service.";
    ntos-ext:nc-cli-identity-name "mllb";
  }

  typedef bandwidth-unit {
    description "Bandwidth unit.";

    type enumeration {
      enum mbps {
        description "Megabits per second.";
      }
      enum kbps {
        description "Kilobit per second.";
      }
    }
  }

  grouping interface-bandwidth {
    description "Bandwidth configuration for a wan-interface.";

    container upload-bandwidth {
      description "The upload bandwidth.";
      leaf upload-bandwidth-unit {
        description "Configure the unit of upload bandwidth.";
        type bandwidth-unit;
        default "mbps";
      }
      leaf upload-bandwidth-value {
        description "Configure the bandwidth.";
        type uint32 {
          range "1..100000000";
        }
        must "((count(../upload-bandwidth-unit) = 0 or ../upload-bandwidth-unit = 'mbps') and current() < 1000001) or ../upload-bandwidth-unit = 'kbps'" {
          error-message "Bandwidth range is 1-100000 mbps.";
        }
      }
      leaf upload-bandwidth-threshold {
        description "Configure the threshold value.";
        type uint16 {
          range "1..100";
        }
        default "80";
      }
    }
    container download-bandwidth {
      description "The download bandwidth.";

      leaf download-bandwidth-unit {
        description "Configure the unit of download bandwidth.";
        type bandwidth-unit;
        default "mbps";
      }
      leaf download-bandwidth-value {
        description "Configure the bandwidth.";
        type uint32 {
          range "1..100000000";
        }
        must "((count(../download-bandwidth-unit) = 0 or ../download-bandwidth-unit = 'mbps') and current() < 1000001) or ../download-bandwidth-unit = 'kbps'" {
          error-message "Bandwidth range is 1-100000 mbps.";
        }
      }
      leaf download-bandwidth-threshold {
        description "Configure the threshold value.";
        type uint16 {
          range "1..100";
        }
        default "80";
      }
    }
  }

  grouping mllb-detail {
    description "Detail of mllb global configuration.";

    container mllb {
      description "Configuration of mllb.";

      container arithmetic {
        choice arithmetic-type {
          default "src-ip-hash";

          case src-ip-hash {
            ntos-ext:nc-cli-group "arithmetic-type";
            leaf src-ip-hash {
              description "Sessions with the same source IP address are transmitted through the same egress.";
              type empty;
            }
          }

          case bandwidth {
            ntos-ext:nc-cli-group "arithmetic-type";
            container bandwidth {
              description "Perform load balancing based on line bandwidth";

              leaf bandwidth-type {
                description "The bandwidth type of the global mllb";

                type enumeration {
                    enum upload;
                    enum download;
                    enum both;
                    enum up-bandwidth;
                    enum down-bandwidth;
                }
                default "down-bandwidth";
              }
            }
          }
          case weight {
            ntos-ext:nc-cli-group "arithmetic-type";
            leaf weight {
              description "Select a egress with a higher weight.";
              type empty;
            }
          }
          case priority {
            ntos-ext:nc-cli-group "arithmetic-type";
            leaf priority {
              description "Select a egress with a higher priority.";
              type empty;
            }
          }
          case quality {
            ntos-ext:nc-cli-group "arithmetic-type";
            leaf quality {
              description "Select a egress with lower loss, delay, and jitter.";
              type empty;
            }
          }
          case src-dst-ip-hash {
            ntos-ext:nc-cli-group "arithmetic-type";
            leaf src-dst-ip-hash {
              description "Sessions with the same source and destination IP addresses are transmitted through the same egress.";
              type empty;
            }
          }
          case min-conn {
            ntos-ext:nc-cli-group "arithmetic-type";
            leaf min-conn {
              description "Perform load balancing based on the real-time session number.";
              type empty;
            }
          }
          case min-conn-weight {
            ntos-ext:nc-cli-group "arithmetic-type";
            leaf min-conn-weight {
              description "Perform load balancing based on line bandwidth and the session number.";
              type empty;
            }
          }
          case hybrid-broadband {
            ntos-ext:nc-cli-group "arithmetic-type";
            leaf hybrid-broadband {
              description "Intelligently select an egress among hybrid broadbands.";
              type empty;
            }
          }
        }
      }

      list wan-interface {
        key "name";
        description "Configuration wan interface list for mllb.";

        leaf name {
          description "The name of the interface.";
          type ntos-types:ifname;
        }

        leaf enabled {
          description "Enable/Disable the load-balancing capability of the interface.";
          type boolean;
          default "true";
        }

        leaf kind {
          description "The kind of the interface.";
          type enumeration {
            enum physical;
            enum bond;
            enum vlan;
            enum ppp;
            enum vti;
            enum gre;
          }
        }
        leaf weight {
          description "The weight of interface.";
          type uint32 {
            range "1..10000";
          }
          default "1";
        }
        leaf priority {
          description "The priority of interface.";
          type uint16 {
            range "1..1000";
          }
          default "1";
        }
        leaf max-conn {
          description "The maximum connection limit of interface.";
          type uint32 {
            range "1..100000000";
          }
          default "2000";
        }
        leaf conn-overload-threshold {
          description "The overload threshold of interface.";
          type uint16 {
            range "1..100";
          }
          default "90";
        }
        leaf conn-recover-threshold {
          description "The recover threshold of interface.";
          type uint16 {
            range "1..100";
          }
          must "current() < ../conn-overload-threshold" {
            error-message "The end value must be less than the conn-overload-threshold value";
          }
          default "80";
        }
        uses interface-bandwidth;
      }

      container advanced-options {
        description "Configuration advanced options of mllb.";

        leaf refresh-session {
          description "Established sessions can be refreshed.";
          type boolean;
          default "false";
        }

        leaf cache-timeout {
          description "The timeout seconds of mllb cache.";
          type uint32 {
            range "1..86400";
          }
          default "300";
        }

        leaf cache-once {
          description "The step size for aging the mllb cache each time.";
          type uint32 {
            range "1..4096";
          }
          default "256";
        }

        leaf cache-disable {
          description "Enabled/Disabled mllb cache.";
          type boolean;
          default "false";
        }
        leaf alarm-threshold {
          description "The alarm threshold of the interface sessions.";
          type uint8 {
            range "1..100";
          }
          default "90";
        }
      }
      leaf all-if-switch {
        description "Whether to display all interfaces.";
        type boolean;
        default "false";
      }
    }
  }

  rpc mllb {
    description "Show mllb global configuration.";

    input {
      leaf vrf {
        description "Specify the VRF.";
        type ntos-types:ntos-obj-name-type;
      }

      choice content {
        case arithmetic {
          leaf arithmetic {
            description "Show mllb arithmetic.";
            type empty;
          }
        }

        case all-if-switch {
          leaf all-if-switch {
            description "Show switch of interfaces.";
            type empty;
          }
        }

        case advanced-options {
          leaf advanced-options {
            description "Advanced options of mllb.";
            type empty;
          }
        }

        case wan-interface {
          container wan-interface {
            ntos-ext:nc-cli-group "subcommand";
            description "Show interfaces of mllb.";

            leaf start {
              description "Start interface number.";
              type uint16 {
                range "0..65535";
              }
            }
            leaf end {
              description "End interface number.";
              type uint16 {
                range "0..65535";
              }
              must "current() >= ../start" {
                error-message "The end value must be larger than the start value.";
              }
            }
            leaf name {
              description "Show the interface with the specified name.";
              type string;
            }
            leaf all {
              description "Show all interface.";
              type empty;
            }
            leaf filter {
              description "Show the interface with the specified field in its name.";
              type string;
            }
            leaf kind {
              description "Show the interface with the specified kind.";
              type string;
            }
          }
        }
      }
    }

    output {
      leaf arithmetic {
        type string;
      }
      leaf all-if-switch {
        type boolean;
      }
      leaf bandwidth-type {
        type string;
      }

      leaf interface-total {
        description "Interface total number.";
        type uint16;
      }
      list wan-interface {
        key "name";
        description "Show wan interface list for mllb.";

        leaf name {
          type string;
        }
        leaf id {
          type uint32;
        }
        leaf ifid {
          type uint32;
        }
        leaf enabled {
          type boolean;
        }
        leaf kind {
          type string;
        }
        leaf weight {
          type uint32;
        }
        leaf priority {
          type uint16;
        }
        leaf max-conn {
          type uint32;
        }
        leaf current-conn {
          type uint32;
        }
        leaf conn-overload-threshold {
          type uint16;
        }
        leaf conn-recover-threshold {
          type uint16;
        }
        leaf isp-name{
          type string;
        }
        leaf broadband-type {
          type string;
        }
        container upload-bandwidth {
          leaf upload-bandwidth-value {
            type uint32;
          }
          leaf upload-bandwidth-unit {
            type string;
          }
          leaf upload-bandwidth-threshold {
            type uint16;
          }
          leaf upload-rate {
            type uint32;
          }
        }
        container download-bandwidth {
          leaf download-bandwidth-value {
            type uint32;
          }
          leaf download-bandwidth-unit {
            type string;
          }
          leaf download-bandwidth-threshold {
            type uint16;
          }
          leaf download-rate {
            type uint32;
          }
        }
        leaf group-list {
          type string;
        }
      }

      container advanced-options {
        description "Configuration advanced options of mllb.";

        leaf refresh-session {
          type boolean;
        }

        leaf cache-timeout {
          type uint32;
        }

        leaf cache-once {
          type uint32;
        }

        leaf cache-disable {
          type boolean;
        }
        leaf alarm-threshold {
          type uint8;
        }
      }
    }

    ntos-ext:nc-cli-show "mllb";
    ntos-api:internal;
  }

  rpc mllb-real-time-data {
    description "Show real-time data from the mllb interface, such as rate, current-sessions.";

    input {
      leaf vrf {
        description "Specify the VRF.";
        type ntos-types:ntos-obj-name-type;
      }
      choice content {
        description "Show real-time data from the mllb interfaces.";

        case name {
          leaf name {
            description "Show interface real-time data with the specified name.";
            type string;
          }
        }

        case name-list {
          leaf name-list {
            description "Show interface real-time data with the specified name list X,Y,Z...";
            type string;
          }
        }
        case ifid {
          leaf ifid {
            description "Show interface real-time data with the specified id.";
            type string;
          }
        }

        case ifid-list {
          leaf ifid-list {
            description "Show interface real-time data with the specified id list X,Y,Z...";
            type string;
          }
        }
      }
    }

    output {
      list wan-interface {
        key "ifid";
        description "Show wan interface list for mllb.";

        leaf name {
          type string;
        }
        leaf ifid {
          type uint32;
        }
        leaf current-conn {
          type uint32;
        }
        leaf upload-rate {
          type uint32;
        }
        leaf download-rate {
          type uint32;
        }
      }
    }

    ntos-ext:nc-cli-show "mllb real-time-data";
    ntos-api:internal;
  }

  rpc mllb-session {
    description "Show mllb forward session.";

    input {
      leaf vrf {
        description "Specify the VRF.";
        type ntos-types:ntos-obj-name-type;
      }

      container content {
        ntos-ext:nc-cli-group "subcommand";

        leaf start {
          description "Start session number.";
          type uint16 {
            range "0..65535";
          }
        }

        leaf end {
          description "End session number.";
          type uint16 {
            range "0..65535";
          }
          must "current() >= ../start" {
            error-message "The end value must be larger than the start value.";
          }
        }

        leaf filter {
          description "Show the session with the specified field in its name or IP.";
          type string;
        }
      }
    }

    output {
      leaf session-total {
        description "Mllb session total number.";
        type uint32;
      }

      list session {
        leaf id {
          type uint32;
        }
        leaf hit {
          type uint64;
        }
        leaf route-name {
          type string;
        }
        leaf module-name {
          type string;
        }
        leaf module-name-i18n {
          type string;
        }
        list wan-interface {
          key "name";
          leaf name {
            type ntos-types:ifname;
          }
        }
      }
    }

    ntos-ext:nc-cli-show "mllb-session";
    ntos-api:internal;
  }

  rpc search-cache-table {
    description "Search mllb cache node.";

    input {
      leaf vrf {
        description "Specify the VRF.";
        type ntos-types:ntos-obj-name-type;
      }
      container content {
        ntos-ext:nc-cli-group "subcommand";

        leaf start {
          description "Start cache number.";
          type uint16 {
            range "0..65535";
          }
        }

        leaf end {
          description "End cache number.";
          type uint16 {
            range "0..65535";
          }
          must "current() >= ../start" {
            error-message "The end value must be larger than the start value.";
          }
        }

        container filter {
          description "Show the cache with the specified field in node.";

          leaf sip {
            description "Show the cache with the specified source address.";
            type union {
              type ntos-inet:ipv6-address;
              type ntos-inet:ipv4-address;
            }
          }

          leaf dip {
            description "Show the cache with the specified destination address.";
            type union {
              type ntos-inet:ipv6-address;
              type ntos-inet:ipv4-address;
            }
          }

          leaf appid {
            description "Show the cache with the specified appid.";
            type string;
          }
        }
      }
    }

    output {
      leaf cache-current {
        type uint32;
      }
      leaf search-num {
        type uint32;
      }
      list cache {
        leaf sip {
          type string;
        }

        leaf dip {
          type string;
        }

        leaf appid {
          type string;
        }

        leaf ifname {
          type string;
        }

        leaf nh-gw {
          type string;
        }

        leaf flags {
          type string;
        }

        leaf timeout {
          type uint32;
        }
      }
    }

    ntos-ext:nc-cli-cmd "mllb search-cache-table";
    ntos-api:internal;
  }

  rpc clear-cache-table {
    description "Clear mllb cache table.";

    ntos-ext:feature "mllb";
    ntos-ext:nc-cli-flush "mllb cache-table";
  }

  rpc clear-stat-hits {
    description "Clear hit statistics of mllb.";

    input {
      leaf vrf {
        description "Specify the VRF.";
        type ntos-types:ntos-obj-name-type;
      }

      choice content {
        description "Mllb stat clear type";

        case id {
          leaf id {
            description "Clear hit statistics of mllb stat by id.";
            type string;
          }
        }

        case id-list {
          leaf id-list {
            description "Clear hit statistics of mllb stat by id list [X,Y,Z...].";
            type string;
          }
        }

        case all {
          leaf all {
            description "Clear hit statistics of all mllb stat.";
            type empty;
          }
        }
      }
    }

    ntos-ext:feature "mllb";
    ntos-ext:nc-cli-flush "mllb stat-hits";
  }

  rpc clear-stats {
    description "Clear hit statistics of mllb.";

    input {
      leaf vrf {
        description "Specify the VRF.";
        type ntos-types:ntos-obj-name-type;
      }
    }

    ntos-ext:feature "mllb";
    ntos-ext:nc-cli-flush "mllb stats";
  }

  rpc mllb-timer {
    description "Set mllb fp intfs and cache timer.";

    input {
      leaf vrf {
        description "Specify the VRF.";
        type ntos-types:ntos-obj-name-type;
      }

      choice content {
        description "Mllb timer management.";

        case intfs {
          container intfs {
            description "Set the interfaces update timer of mllb.";

            leaf enabled {
              type boolean;
              default "true";
            }

            leaf interval {
              description "Set the mllb interfaces update timer interval time.";
              type uint16 {
                range "1..3600";
              }
              default "5";
            }
          }
        }
        case cache {
          container cache {
            description "Set the cache timer of mllb.";

            leaf enabled {
              type boolean;
              default "true";
            }

            leaf interval {
              description "Set the mllb cache timer interval time.";
              type uint16{
                range "1..3600";
              }
              default "300";
            }
          }
        }

        case show {
          leaf show {
            description "Show the timer configure of mllb.";
            type empty;
          }
        }
      }
    }

    output {
      container intfs {
        leaf enabled {
          type boolean;
        }
        leaf interval {
          type uint16;
        }
      }
      container cache {
        leaf enabled {
          type boolean;
        }
        leaf interval {
          type uint16;
        }
      }
    }

    // ntos-ext:nc-cli-hidden;
    ntos-ext:nc-cli-cmd "mllb timer";
    ntos-api:internal;
  }

  rpc show-yams-config {
    description "Show mllb mgmt configuration.";

    ntos-ext:nc-cli-cmd "mllb show-yams-config";
    ntos-api:internal;
  }

  augment "/ntos:config/ntos:vrf" {
    description "Mllb configuration.";
    uses mllb-detail;
  }

  augment "/ntos:state/ntos:vrf" {
    description "Mllb state.";
    uses mllb-detail;
  }

  rpc mllb-debug-config {
    description "Set/show the debug level of mllb.";

    input {
      leaf vrf {
        description "Specify the VRF.";
        type ntos-types:ntos-obj-name-type;
      }
      choice content {
        case set {
          leaf enabled {
            type boolean;
            default "true";
          }
          leaf level {
            type enumeration {
              enum critical;
              enum error;
              enum warning;
              enum notice;
              enum info;
              enum debug;
            }
          }
        }
        case show {
          leaf show {
            type empty;
          }
        }
      }
    }

    output {
      leaf enabled {
        type boolean;
      }
      leaf level {
        type string;
      }
    }

    // ntos-ext:nc-cli-hidden;
    ntos-ext:nc-cli-cmd "mllb debug";
    ntos-api:internal;
  }
}
