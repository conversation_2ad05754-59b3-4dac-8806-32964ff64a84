module ntos-ospf {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:ospf";
  prefix ntos-ospf;

  import ntos {
    prefix ntos;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-commands {
    prefix ntos-cmd;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos-extensions {
    prefix ntos-extensions;
  }
  import ntos-routing {
    prefix ntos-rt;
  }
  import ntos-types {
    prefix ntos-types;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "RUIJIE NTOS OSPF.";

  revision 2023-03-26 {
    description
      "Updating for First version of OSPF to release.";
    reference "";
  }
  
  revision 2020-11-10 {
    description
      "Update distribute-list.";
    reference "";
  }
  revision 2020-06-26 {
    description
      "Add flush ospf interface RPC.";
    reference "";
  }
  revision 2019-06-06 {
    description
      "Add logging configuration.";
    reference "";
  }
  revision 2018-10-03 {
    description
      "Initial version.";
    reference "";
  }
  
  identity ospf {
    base ntos-types:SERVICE_LOG_ID;
    description
      "Routing OSPF protocol.";
    ntos-extensions:nc-cli-identity-name "routing ospf";
  }

  identity route-ospf {
    base ntos-types:ROUTE4_FRR_ID;
    description
      "OSPF routes.";
    ntos-extensions:nc-cli-identity-name "ospf";
  }

  grouping ospf-common {
    description
      "OSPF objects common to configuration and state.";

    leaf router-id {
      type ntos-inet:ipv4-address;
      description
        "OSPF router-id in IP address format.";
    }

    leaf abr-type {
      type enumeration {
        enum cisco {
          description
            "Alternative ABR, cisco implementation.";
        }
        enum ibm {
          description
            "Alternative ABR, IBM implementation.";
        }
        enum shortcut {
          description
            "Shortcut ABR.";
        }
        enum standard {
          description
            "Standard behavior (RFC2328).";
        }
      }
      default "cisco";
      description
        "OSPF ABR type.";
    }

    leaf write-multiplier {
      type uint8 {
        range "1..100";
      }
      default "20";
      description
        "Maximum number of interface serviced per write.";
    }

    list area {
      key "area-id";
      description
        "OSPF area parameters.";

      leaf area-id {
        type ntos-inet:ipv4-address;
        description
          "OSPF area ID.";
      }

      container nssa {
        must 'count(../stub) + count(../virtual-link) = 0' {
          error-message "nssa, stub and virtual-link are exclusive.";
        }
        presence "Makes nssa available";
        description
          "Configure OSPF area as nssa.";
        ntos-extensions:nc-cli-one-liner;

        leaf summary {
          type boolean;
          default "true";
          description
            "Inject inter-area routes into nssa.";
        }

        leaf translate {
          type enumeration {
            enum always {
              description
                "Configure NSSA-ABR to always translate.";
            }
            enum candidate {
              description
                "Configure NSSA-ABR for translate election (default).";
            }
            enum never {
              description
                "Configure NSSA-ABR to never translate.";
            }
          }
          default "candidate";
          description
            "NSSA-ABR translate.";
        }
      }

      container stub {
        must 'count(../nssa) + count(../virtual-link) = 0' {
          error-message "nssa, stub and virtual-link are exclusive.";
        }
        presence "Makes stub available";
        description
          "Configure OSPF area as stub.";
        ntos-extensions:nc-cli-one-liner;

        leaf summary {
          type boolean;
          default "true";
          description
            "Inject inter-area routes into stub.";
        }
      }

      list virtual-link {
        must 'count(../nssa) + count(../stub) = 0' {
          error-message "nssa, stub and virtual-link are exclusive.";
        }
        key "remote-id";
        description
          "Virtual links.";

        leaf remote-id {
          type ntos-inet:ipv4-address;
          description
            "Router ID of the remote ABR.";
        }
      }

      leaf default-cost {
        type uint32 {
          range "0..16777215";
        }
        must '../nssa or ../stub' {
          error-message "The area is neither stub, nor NSSA.";
        }
        description
          "Default summary cost of a NSSA or stub area.";
        ntos-api:must-added "../nssa or ../stub";
      }

      leaf export-list {
        type string {
          length "1..max";
        }
        description
          "Set the filter for networks announced to other areas (access-list name).";
      }

      container filter-list {
        description
          "Filter networks between OSPF areas.";

        leaf input {
          type string {
            length "1..max";
          }
          description
            "Filter networks sent to this area (prefix-list name).";
        }

        leaf output {
          type string {
            length "1..max";
          }
          description
            "Filter networks sent from this area (prefix-list name).";
        }
      }

      leaf import-list {
        type string {
          length "1..max";
        }
        description
          "Set the filter for networks from other areas announced to the specified one
           (access-list name).";
      }

      list range {
        key "prefix";
        description
          "Summarize routes matching address/mask (border routers only).";
        ntos-extensions:nc-cli-one-liner;

        leaf prefix {
          type ntos-inet:ipv4-prefix;
          description
            "Area range prefix.";
        }

        leaf action {
          type union {
            type enumeration {
              enum advertise {
                description
                  "Advertise this range (default).";
              }
              enum not-advertise {
                description
                  "Do not advertise this range.";
              }
            }
            type ntos-inet:ipv4-prefix;
          }
          default "advertise";
          description
            "Advertise this range, do not advertise or announce as another prefix.";
        }

        leaf cost {
          type uint32 {
            range "0..16777215";
          }
          must "../action = 'advertise'" {
            error-message "Cost cannot be set if action is not advertise.";
          }
          description
            "User specified metric for this range.";
        }
      }

      leaf shortcut {
        type boolean;
        must "../area-id != '0.0.0.0'" {
          error-message
            "The backbone area with ID 0 or 0.0.0.0 cannot be used
             for shortcutting.";
        }
        must "../../abr-type = 'shortcut'" {
          error-message "The ABR router must be of type 'shortcut'.";
        }
        description
          "Enable/Disable shortcutting through the area.";
      }
    }

    leaf auto-cost {
      type uint32 {
        range "1..4294967";
      }
      default "100000";
      description
        "Calculate OSPF interface cost according to reference bandwidth (Mbits per second).";
    }

    leaf opaque-lsa {
      type boolean;
      default "false";
      description
        "Enable or disable opaque LSA capability.";
    }

    leaf compatible-rfc1583 {
      type boolean;
      default "false";
      description
        "Enable or disable compatibility with RFC 1583.";
    }

    container default-information {
      presence "Enables default information";
      description
        "Control distribution of default information.";

      leaf always {
        type boolean;
        description
          "If true, always advertise default route.";
      }

      leaf metric {
        type uint32 {
          range "0..********";
        }
        description
          "OSPF default metric.";
      }

      leaf metric-type {
        type uint8 {
          range "1..2";
        }
        default "2";
        description
          "OSPF metric type for default routes.";
      }

      leaf route-map {
        type string {
          length "1..max";
        }
        description
          "Route map reference.";
      }
    }

    leaf default-metric {
      type uint32 {
        range "0..********";
      }
      description
        "Set metric of redistributed routes.";
    }

    container distance {
      description
        "OSPF administrative distance.";

      leaf all {
        type uint8 {
          range "1..255";
        }
        description
          "Default OSPF administrative distance.";
      }

      leaf external {
        type uint8 {
          range "1..255";
        }
        description
          "OSPF administrative distance for external routes.";
      }

      leaf inter-area {
        type uint8 {
          range "1..255";
        }
        description
          "OSPF administrative distance for inter-area routes.";
      }

      leaf intra-area {
        type uint8 {
          range "1..255";
        }
        description
          "OSPF administrative distance for intra-area routes.";
      }
    }

    leaf log-adjacency-changes {
      type enumeration {
        enum standard {
          description
            "Standard logs.";
        }
        enum detail {
          description
            "Log all state changes.";
        }
      }
      description
        "Log changes in adjacency state.";
    }

    container max-metric {
      description
        "OSPF maximum / infinite-distance metric.";

      leaf administrative {
        type boolean;
        description
          "If true, mark as administratively applied, for an indefinite period.";
      }

      leaf on-shutdown {
        type uint8 {
          range "5..100";
        }
        description
          "Advertise stub-router prior to full shutdown of OSPF.";
      }

      leaf on-startup {
        type uint32 {
          range "5..86400";
        }
        description
          "Automatically advertise stub Router-LSA on startup of OSPF.";
      }
    }

    list neighbor {
      key "address";
      description
        "Neighbor router.";
      ntos-extensions:nc-cli-one-liner;

      leaf address {
        type ntos-inet:ipv4-address;
        description
          "Neighbor IP address.";
      }

      leaf poll-interval {
        type uint16 {
          range "1..65535";
        }
        description
          "Dead neighbor polling interval (in seconds).";
      }

      leaf priority {
        type uint8 {
          range "0..255";
        }
        description
          "Neighbor priority.";
      }
    }

    list network {
      key "prefix area";
      description
        "Enable routing on an IP network.";
      ntos-extensions:nc-cli-one-liner;

      leaf prefix {
        type ntos-inet:ipv4-prefix;
        description
          "OSPF network prefix.";
      }

      leaf area {
        type ntos-inet:ipv4-address;
        mandatory true;
        description
          "OSPF area ID.";
      }
    }

    list passive-interface {
      key "interface";
      description
        "Suppress routing updates on an interface.";
      ntos-extensions:nc-cli-one-liner;

      leaf interface {
        type ntos-types:ifname;
        description
          "Interface name.";
        ntos-extensions:nc-cli-completion-xpath
          "../../ntos-interface:interface/*/*[local-name()='name']";
      }

      leaf address {
        type ntos-inet:ipv4-address;
        description
          "IPv4 address.";
      }
    }

    leaf refresh-timer {
      type uint16 {
        range
          "10|20|30|40|50|60|70|80|90|100|110|120|130|140|150|160|170|180|190" +
          "|200|210|220|230|240|250|260|270|280|290|300|310|320|330|340|350" +
          "|360|370|380|390|400|410|420|430|440|450|460|470|480|490|500|510" +
          "|520|530|540|550|560|570|580|590|600|610|620|630|640|650|660|670" +
          "|680|690|700|710|720|730|740|750|760|770|780|790|800|810|820|830" +
          "|840|850|860|870|880|890|900|910|920|930|940|950|960|970|980|990" +
          "|1000|1010|1020|1030|1040|1050|1060|1070|1080|1090|1100|1110|1120" +
          "|1130|1140|1150|1160|1170|1180|1190|1200|1210|1220|1230|1240|1250" +
          "|1260|1270|1280|1290|1300|1310|1320|1330|1340|1350|1360|1370|1380" +
          "|1390|1400|1410|1420|1430|1440|1450|1460|1470|1480|1490|1500|1510" +
          "|1520|1530|1540|1550|1560|1570|1580|1590|1600|1610|1620|1630|1640" +
          "|1650|1660|1670|1680|1690|1700|1710|1720|1730|1740|1750|1760|1770" +
          "|1780|1790|1800";
      }
      default "10";
      description
        "LSA refresh interval (in seconds).";
    }

    container timers {
      description
        "Adjust routing timers.";

      container lsa {
        description
          "Throttling link state advertisement delays.";

        leaf min-arrival {
          type uint32 {
            range "0..600000";
          }
          description
            "Minimum delay in receiving new version of a LSA.";
        }
      }

      container throttle {
        description
          "Throttling adaptive timer.";

        leaf lsa {
          type uint16 {
            range "0..5000";
          }
          description
            "LSA delay (msec) between transmissions.";
        }

        container spf {
          presence "Makes OSPF SPF timers available";
          description
            "OSPF SPF timers.";

          leaf delay {
            type uint32 {
              range "0..600000";
            }
            mandatory true;
            description
              "Delay (msec) from first change received till SPF calculation.";
          }

          leaf init-hold-time {
            type uint32 {
              range "0..600000";
            }
            mandatory true;
            description
              "Initial hold time (msec) between consecutive SPF calculations.";
          }

          leaf max-hold-time {
            type uint32 {
              range "0..600000";
            }
            mandatory true;
            description
              "Maximum hold time (msec).";
          }
        }
      }
    }
  }

  grouping ospf-config {
    description
      "OSPF configuration objects.";
    uses ospf-common;

    list distribute-list {
      key "out access-list";
      description
        "Filter networks in routing updates.";
      ntos-extensions:nc-cli-show-key-name;

      leaf access-list {
        type string {
          length "1..max";
        }
        mandatory true;
        description
          "Access list name.";
      }

      leaf out {
        type enumeration {
          enum bgp {
            description
              "Border Gateway Protocol (BGP).";
          }
          enum connected {
            description
              "Connected routes (directly attached subnet or host).";
          }
          enum kernel {
            description
              "Kernel routes (not installed via the zebra RIB).";
          }
          enum rip {
            description
              "Routing Information Protocol (RIP).";
          }
          enum static {
            description
              "Statically configured routes.";
          }
          enum table {
            description
              "Non-main Kernel Routing Table.";
          }
          enum nhrp {
            description
              "Next Hop Resolution Protocol (NHRP).";
          }
        }
        description
          "Filter outgoing routing updates.";
      }
    }

    list redistribute {
      key "protocol";
      description
        "Redistribute information from another routing protocol.";
      ntos-extensions:nc-cli-one-liner;

      leaf protocol {
        type enumeration {
          enum bgp {
            description
              "Border Gateway Protocol (BGP).";
          }
          enum connected {
            description
              "Connected routes (directly attached subnet or host).";
          }
          enum kernel {
            description
              "Kernel routes (not installed via the zebra RIB).";
          }
          enum rip {
            description
              "Routing Information Protocol (RIP).";
          }
          enum static {
            description
              "Statically configured routes.";
          }
          enum table {
            description
              "Non-main Kernel Routing Table.";
          }
        }
        description
          "Routing protocol.";
      }

      leaf metric {
        type uint32 {
          range "0..********";
        }
        description
          "Metric for redistributed routes.";
      }

      leaf metric-type {
        type uint8 {
          range "1..2";
        }
        description
          "OSPF exterior metric type for redistributed routes.";
      }

      leaf route-map {
        type string {
          length "1..max";
        }
        description
          "Route map reference.";
      }

      leaf id {
        when "../protocol = 'table'" {
          description
            "Table ID is available only for table protocol.";
        }
        type uint16;
        description
          "Table ID.";
      }
    }
  }

  grouping ospf-state {
    description
      "OSPF state objects.";
    uses ospf-common;

    list distribute-list {
      key "out";
      description
        "Filter networks in routing updates.";
      ntos-extensions:nc-cli-show-key-name;

      leaf access-list {
        type string;
        description
          "Access list name.";
      }

      leaf out {
        type string;
        description
          "Filter outgoing routing updates.";
      }
    }

    list redistribute {
      key "protocol";
      description
        "Redistribute information from another routing protocol.";
      ntos-extensions:nc-cli-one-liner;

      leaf protocol {
        type string;
        description
          "Routing protocol.";
      }

      leaf metric {
        type uint32 {
          range "0..********";
        }
        description
          "Metric for redistributed routes.";
      }

      leaf metric-type {
        type uint8 {
          range "1..2";
        }
        description
          "OSPF exterior metric type for redistributed routes.";
      }

      leaf route-map {
        type string;
        description
          "Route map reference.";
      }

      leaf id {
        when "../protocol = 'table'" {
          description
            "Table ID is available only for table protocol.";
        }
        type uint16;
        description
          "Table ID.";
      }
    }
  }

  grouping common-interface-config {
    description
      "OSPF common interface configuration.";

    leaf area {
      type ntos-inet:ipv4-address;
      must 'count(/ntos:config/ntos:vrf/ntos-rt:routing/ospf/network) = 0' {
        error-message "Interface area and ospf network are exclusive.";
      }
      description
        "OSPF area ID.";
    }

    leaf authentication {
      type enumeration {
        enum simple {
          description
            "Use simple authentication.";
        }
        enum message-digest {
          description
            "Use message-digest authentication.";
        }
        enum null {
          description
            "Use null authentication.";
        }
      }

      description
        "Enable authentication on this interface.";
    }

    leaf authentication-key {
      type string {
        length "1..100";
      }
      description
        "Authentication key.";
    }

    list message-digest-key {
      key "key-id";
      description
        "Message digest authentication password (key).";
      ntos-extensions:nc-cli-one-liner;

      leaf key-id {
        type uint8 {
          range "1..255";
        }
        description
          "Key ID.";
      }

      leaf md5 {
        type string {
          length "1..100";
        }
        mandatory true;
        description
          "The OSPF password (key).";
      }
    }

    leaf cost {
      type uint16 {
        range "1..65535";
      }
      description
        "Interface cost.";
    }

    container dead-interval {
      must 'count(seconds) + count(minimal) = 1' {
        error-message "You must specify either seconds or minimal hello multiplier.";
      }
      presence "Makes minimal available";
      description
        "Interval time after which a neighbor is declared down.";
      ntos-extensions:nc-cli-one-liner;

      leaf seconds {
        type uint16 {
          range "1..65535";
        }
        description
          "Seconds.";
      }

      container minimal {
        presence "Makes minimal available";
        description
          "Minimal 1s dead-interval with fast sub-second hellos.";

        leaf hello-multiplier {
          type uint8 {
            range "1..10";
          }
          mandatory true;
          description
            "Number of Hellos to send each second.";
        }
      }
    }

    leaf hello-interval {
      type uint16 {
        range "1..65535";
      }
      description
        "Time between HELLO packets (seconds).";
    }

    leaf mtu-ignore {
      type boolean;
      description
        "If true, disable MTU mismatch detection on this interface.";
    }

    leaf passive {
      type boolean;
      description
        "If true, passive interface.If false, not passive interface.";
    }

    leaf priority {
      type uint8 {
        range "0..255";
      }
      description
        "Router priority.";
    }

    leaf retransmit-interval {
      type uint16 {
        range "3..65535";
      }
      description
        "Time between retransmitting lost link state advertisements (seconds).";
    }

    leaf transmit-delay {
      type uint16 {
        range "1..65535";
      }
      description
        "Link state transmit delay (seconds).";
    }
  }

  grouping interface-config {
    description
      "Interface OSPF configuration.";
    uses common-interface-config;

    leaf network {
      type enumeration {
        enum broadcast {
          description
            "Specify OSPF broadcast multi-access network.";
        }
        enum non-broadcast {
          description
            "Specify OSPF NBMA network.";
        }
        enum point-to-multipoint {
          description
            "Specify OSPF point-to-multipoint network.";
        }
        enum point-to-point {
          description
            "Specify OSPF point-to-point network.";
        }
      }
      default "broadcast";
      description
        "Network type.";
    }

    list address {
      key "ip";
      description
        "Specific configuration per IP address.";

      leaf ip {
        type ntos-inet:ipv4-address;
        description
          "Interface address.";
      }
      uses common-interface-config;
    }
  }

  grouping logging-common-config {
    description
      "OSPF logging configuration.";

    leaf enabled {
      type boolean;
      default "false";
      description
        "Enable/disable OSPF logging configuration.";
    }

    leaf events {
      type boolean;
      default "false";
      description
        "Log OSPF event information.";
    }

    leaf-list ism {
      type enumeration {
        enum events {
          description
            "Log ISM Event Information.";
        }
        enum status {
          description
            "Log ISM Status Information.";
        }
        enum timers {
          description
            "Log ISM Timer Information.";
        }
        enum all {
          description
            "Log all ISM Information.";
        }
      }
      must "(../ism = 'all' and count(../ism) = 1) or not(../ism = 'all')" {
        error-message "All ISM information are already logged.";
      }
      description
        "Log OSPF Interface State Machine information.";
    }

    leaf-list lsa {
      type enumeration {
        enum flooding {
          description
            "Log LSA flooding Information.";
        }
        enum generate {
          description
            "Log LSA generate Information.";
        }
        enum install {
          description
            "Log LSA install Information.";
        }
        enum refresh {
          description
            "Log LSA refresh Information.";
        }
        enum all {
          description
            "Log all LSA Information.";
        }
      }
      must "(../lsa = 'all' and count(../lsa) = 1) or not(../lsa = 'all')" {
        error-message "All LSA information are already logged.";
      }
      description
        "Log OSPF Link State Advertisement information.";
    }

    leaf-list nsm {
      type enumeration {
        enum events {
          description
            "Log NSM Event Information.";
        }
        enum status {
          description
            "Log NSM Status Information.";
        }
        enum timers {
          description
            "Log NSM Timer Information.";
        }
        enum all {
          description
            "Log all NSM Information.";
        }
      }
      must "(../nsm = 'all' and count(../nsm) = 1) or not(../nsm = 'all')" {
        error-message "All NSM information are already logged.";
      }
      description
        "Log OSPF Neighbor State Machine information.";
    }

    leaf nssa {
      type boolean;
      default "false";
      description
        "Log OSPF nssa information.";
    }

    list message {
      must "(message-type = 'all' and count(../message) = 1) or message-type != 'all'" {
        error-message "All messages are already logged.";
      }
      key "message-type";
      description
        "Log OSPF message information.";

      leaf message-type {
        type enumeration {
          enum dd {
            description
              "Log Database Description messages.";
          }
          enum hello {
            description
              "Log Hello messages.";
          }
          enum ls-ack {
            description
              "Log Link State Acknowledgment messages.";
          }
          enum ls-request {
            description
              "Log Link State Request messages.";
          }
          enum ls-update {
            description
              "Log Link State Update messages.";
          }
          enum all {
            description
              "Log all messages (whatever its type).";
          }
        }
        description
          "Message type to log.";
      }

      leaf direction {
        type enumeration {
          enum send {
            description
              "Log sent messages.";
          }
          enum receive {
            description
              "Log received messages.";
          }
          enum both {
            description
              "Log all messages.";
          }
        }
        default "both";
        description
          "Direction of messages to log.";
      }

      leaf detail {
        type boolean;
        default "false";
        description
          "Log message details.";
      }
    }

    leaf zebra {
      type enumeration {
        enum interface {
          description
            "Log zebra interface information.";
        }
        enum redistribute {
          description
            "Log zebra redistribute information.";
        }
        enum all {
          description
            "Log zebra interface and redistribute information.";
        }
      }
      must "(../zebra = 'all' and count(../zebra) = 1) or not(../zebra = 'all')" {
        error-message "All zebra information are already logged.";
      }
      description
        "Log zebra information.";
    }
  }

  grouping show-ospf-database-common-parameters {
    description
      "The common parameters of 'show ospf database [params]' command.";

    leaf address {
      type union {
        type ntos-inet:ipv4-address;
        type enumeration {
          enum self-originate {
            description
              "Self-originated link states.";
          }
        }
      }
      description
        "The router address.";
      ntos-extensions:nc-cli-no-name;
      ntos-extensions:nc-cli-group "show-ospf-parameter-subcommand";
    }

    leaf advertising-router {
      type ntos-inet:ipv4-address;
      description
        "The advertising router address.";
      ntos-extensions:nc-cli-group "show-ospf-parameter-subcommand";
    }
  }

  grouping page-filter {
    description
      "Page filter parameters.";

    leaf page-index {
      type uint32{
          range "1..10000000";
        }
      description
        "Page index.";
    }

    leaf page-size {
      type uint32{
          range "1..100";
        }
      description
        "Number entries of one page.";
    }
  }
  
  rpc show-ospf {
    description
      "Show OSPF information.";
    input {
      must 'count(route) + count(database) + count(neighbor) + count(interface) <= 1' {
        error-message "Route, database, neighbor and interface parameters are exclusive.";
      }

      leaf vrf {
        type string;
        description
          "Specify the VRF.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
        ntos-extensions:nc-cli-group "show-ospf-vrfs";
      }

      leaf vrfs {
        type empty;
        description
          "Available VRFs.";
        ntos-extensions:nc-cli-group "show-ospf-vrfs";
        ntos-extensions:nc-cli-group "show-ospf-command";
      }

      uses page-filter;

      leaf route {
        type empty;
        description
          "OSPF routing table.";
        ntos-extensions:nc-cli-group "show-ospf-command";
      }

      container database {
        must 'count(router) + count(default) <= 1' {
          error-message "It is not possible to dump default and router info.";
        }
        presence "Database summary.";
        description
          "Database summary.";
        ntos-extensions:nc-cli-exclusive;
        ntos-extensions:nc-cli-group "show-ospf-command";

        leaf default {
          type empty;
          must 'count(../../page-index) + count(../../page-size) <= 0' {
            error-message "Data pagination is not supported.";
          }
          description
            "Database summary.";
        }

        leaf max-age {
          type empty;
          must 'count(../../page-index) + count(../../page-size) <= 0' {
            error-message "Data pagination is not supported.";
          }
          description
            "Database maximum age.";
        }

        container router {
          presence "Database Router link states.";
          must 'count(../../page-index) + count(../../page-size) <= 0' {
            error-message "Data pagination is not supported.";
          }
          description
            "Database Router link states.";
          uses show-ospf-database-common-parameters;
        }

        container asbr-summary {
          presence "Database ASBR summary link states.";
          must 'count(../../page-index) + count(../../page-size) <= 0' {
            error-message "Data pagination is not supported.";
          }
          description
            "Database ASBR summary link states.";
          uses show-ospf-database-common-parameters;
        }

        container external {
          presence "Database External link states.";
          must 'count(../../page-index) + count(../../page-size) <= 0' {
            error-message "Data pagination is not supported.";
          }
          description
            "Database External link states.";
          uses show-ospf-database-common-parameters;
        }

        container network {
          presence "Database Network link states.";
          must 'count(../../page-index) + count(../../page-size) <= 0' {
            error-message "Data pagination is not supported.";
          }
          description
            "Database Network link states.";
          uses show-ospf-database-common-parameters;
        }

        container nssa-external {
          presence "Database NSSA external link states.";
          must 'count(../../page-index) + count(../../page-size) <= 0' {
            error-message "Data pagination is not supported.";
          }
          description
            "Database NSSA external link states.";
          uses show-ospf-database-common-parameters;
        }

        container opaque-area {
          presence "Database Opaque link state area.";
          must 'count(../../page-index) + count(../../page-size) <= 0' {
            error-message "Data pagination is not supported.";
          }
          description
            "Database Opaque link state area.";
          uses show-ospf-database-common-parameters;
        }

        container opaque-link {
          presence "Database Opaque link states.";
          must 'count(../../page-index) + count(../../page-size) <= 0' {
            error-message "Data pagination is not supported.";
          }
          description
            "Database Opaque link states.";
          uses show-ospf-database-common-parameters;
        }

        container opaque-as {
          presence "Database Opaque AS link states.";
          must 'count(../../page-index) + count(../../page-size) <= 0' {
            error-message "Data pagination is not supported.";
          }
          description
            "Database Opaque AS link states.";
          uses show-ospf-database-common-parameters;
        }

        container summary {
          presence "Database Summary link states.";
          must 'count(../../page-index) + count(../../page-size) <= 0' {
            error-message "Data pagination is not supported.";
          }
          description
            "Database Summary link states.";
          uses show-ospf-database-common-parameters;
        }
      }

      container neighbor {
        presence "Neighbors information.";
        description
          "Neighbors information.";
        ntos-extensions:nc-cli-group "show-ospf-command";

        leaf address {
          type ntos-inet:ipv4-address;
          must 'count(../../page-index) + count(../../page-size) <= 0' {
            error-message "Data pagination is not supported.";
          }
          description
            "Neighbor ID.";
          ntos-extensions:nc-cli-no-name;
          ntos-extensions:nc-cli-group "show-ospf-neigh-detail-subcommand";
          ntos-extensions:nc-cli-group "show-ospf-neigh-ifname-subcommand";
        }

        leaf ifname {
          type ntos-types:ifname {
            pattern 'detail' {
              modifier "invert-match";
            }
          }
          must 'count(../../page-index) + count(../../page-size) <= 0' {
            error-message "Data pagination is not supported.";
          }
          description
            "The interface name.";
          ntos-extensions:nc-cli-no-name;
          ntos-extensions:nc-cli-completion-xpath
            "/ntos:state/ntos:vrf[ntos:name=string(current()/ntos-ospf:vrf)]/ntos-interface:interface/*/*[local-name()='name'] |
             /ntos:state/ntos:vrf[ntos:name='main'][not(current()/ntos-ospf:vrf)]/ntos-interface:interface/*/*[local-name()='name']";
          ntos-extensions:nc-cli-group "show-ospf-neigh-ifname-subcommand";
        }

        leaf detail {
          type empty;
          must 'count(../../page-index) + count(../../page-size) <= 0' {
            error-message "Data pagination is not supported.";
          }
          description
            "Show detailed neighbor's information.";
          ntos-extensions:nc-cli-group "show-ospf-neigh-detail-subcommand";
        }
      }

      container interface {
        presence "Interface information.";
        description
          "Interface information.";
        ntos-extensions:nc-cli-group "show-ospf-command";

        leaf traffic {
          type empty;
          must 'count(../../page-index) + count(../../page-size) <= 0' {
            error-message "Data pagination is not supported.";
          }
          description
            "Interface traffic information.";
        }

        leaf name {
          type ntos-types:ifname {
            pattern 'traffic' {
              modifier "invert-match";
            }
          }
          must 'count(../../page-index) + count(../../page-size) <= 0' {
            error-message "Data pagination is not supported.";
          }
          description
            "The interface name. If not specified, show all interfaces.";
          ntos-extensions:nc-cli-no-name;
          ntos-extensions:nc-cli-completion-xpath
            "/ntos:state/ntos:vrf[ntos:name=string(current()/ntos-ospf:vrf)]/ntos-interface:interface/*/*[local-name()='name'] |
             /ntos:state/ntos:vrf[ntos:name='main'][not(current()/ntos-ospf:vrf)]/ntos-interface:interface/*/*[local-name()='name']";
        }
      }
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-extensions:feature "product";
    ntos-extensions:nc-cli-show "ospf";
    ntos-api:internal;
  }

  rpc flush-ospf {
    description
      "Flush OSPF information.";
    input {

      leaf vrf {
        type string;
        description
          "Specify the VRF.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf interface {
        type string {
          ntos-extensions:nc-cli-shortdesc "<ifname>";
        }
        mandatory true;
        description
          "The name of the network interface to be cleared.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf[ntos:name=string(current()/ntos-system:vrf)]/ntos-interface:interface/*/*[local-name()='name'] |
           /ntos:state/ntos:vrf[ntos:name='main'][not(current()/ntos-system:vrf)]/ntos-interface:interface/*/*[local-name()='name']";
      }
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-extensions:feature "product";
    ntos-extensions:nc-cli-flush "ospf";
    ntos-api:internal;
  }

  augment "/ntos:config/ntos:vrf/ntos-rt:routing" {
    description
      "OSPF configuration.";

    container ospf {
      presence "Makes OSPF available";
      description
        "OSPF configuration.";
      ntos-extensions:feature "product";
      uses ospf-config;
    }
  }

  augment "/ntos:state/ntos:vrf/ntos-rt:routing" {
    description
      "OSPF state.";

    container ospf {
      presence "Makes OSPF available";
      description
        "OSPF operational state data.";
      ntos-extensions:feature "product";
      uses ospf-config;
    }
  }

  augment "/ntos:config/ntos:vrf/ntos-rt:routing/ntos-rt:interface/ntos-rt:ip" {
    description
      "OSPF interface configuration.";

    container ospf {
      presence "Makes OSPF available";
      description
        "OSPF configuration.";
      ntos-extensions:feature "product";
      uses interface-config;
    }
  }

  augment "/ntos:state/ntos:vrf/ntos-rt:routing/ntos-rt:interface/ntos-rt:ip" {
    description
      "OSPF interface state.";

    container ospf {
      presence "Makes OSPF available";
      description
        "OSPF operational state data.";
      ntos-extensions:feature "product";
      uses interface-config;
    }
  }

  augment "/ntos:config/ntos-rt:routing/ntos-rt:logging" {
    description
      "Common OSPF routers logging configuration.";

    container ospf {
      presence "Make OSPF common logging configuration available.";
      description
        "Common OSPF routers logging configuration.";
      ntos-extensions:feature "product";
      uses logging-common-config;
    }
  }

  augment "/ntos:state/ntos-rt:routing/ntos-rt:logging" {
    description
      "Common OSPF routers logging operational state.";

    container ospf {
      presence "Make OSPF common logging state available.";
      description
        "Operational logging state common to all OSPF routers.";
      ntos-extensions:feature "product";
      uses logging-common-config;
    }
  }
}
