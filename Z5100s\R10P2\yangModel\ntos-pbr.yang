module ntos-pbr {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:pbr";
  prefix ntos-pbr;

  import ntos {
    prefix ntos;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos-user-management {
    prefix ntos-user-management;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel: 4008-111-000
    E-mail: <EMAIL>";
  description
    "Ruijie NTOS policy-based routing module.";

  revision 2024-09-29 {
    description "Add exclusive-link mode";
    reference "";
  }
  revision 2024-04-11 {
    description "Add sla-policy configure";
    reference "";
  }
  revision 2023-07-31 {
    description "Add multi-interface configure";
    reference "";
  }
  revision 2023-04-18 {
    description "Modify next-hop check";
    reference "";
  }
  revision 2022-08-25 {
    description "Initial revision";
    reference "";
  }
  
  typedef pbr-ipv4-filter-invalid-address {
    description "Invalid ipv4 address check.";

    type ntos-inet:ipv4-address  {
      pattern '(127\.\d+\.\d+\.\d+).*' {
        modifier invert-match;
        error-message "Invalid IPv4 address.";
      }
      pattern '(0\.0\.0\.0).*' {
        modifier invert-match;
        error-message "Invalid IPv4 address.";
      }
      pattern '((22[4-9]|23\d)\.\d+\.\d+\.\d+).*' {
        modifier invert-match;
        error-message "Invalid IPv4 address.";
      }
      pattern '(255\.255\.255\.255)' {
        modifier invert-match;
        error-message "Invalid IPv4 address.";
      }
      ntos-ext:nc-cli-shortdesc "<A.B.C.D>";
    }
    ntos-api:pattern-stable;
  }

  typedef pbr-ipv6-filter-invalid-address {
    description "Invalid ipv6 address check.";

    type ntos-inet:ipv6-address {
      pattern '(([fF]{2}[0-9a-fA-F]{2}):).*' {
        modifier "invert-match";
        error-message "Invalid IPv6 address.";
      }
      pattern '[fF][eE]80:.*' {
        modifier "invert-match";
        error-message "Invalid IPv6 address.";
      }

      ntos-ext:nc-cli-shortdesc "<X:X::X:X>";
    }
    ntos-api:pattern-stable;
  }

  typedef action-type {
    description "PBR action type.";
    type enumeration {
      enum permit {
        description "PBR forward permit.";
      }
      enum deny {
        description "PBR forward ignore, use other routing.";
      }
      enum other {
        description "PBR forward other vrf.";
      }
    }
  }

  typedef egress-type {
    description "The egress type of policy-based routing .";
    type enumeration {
      enum single {
        description "Configure a single interface.";
      }
      enum multi {
        description "Configure multiple interfaces.";
      }
    }
  }

  typedef config-source-type {
    type enumeration {
      enum auto {
        description "Automatic configuration.";
      }
      enum manual {
        description "Manual configuration.";
      }
    }
  }

  grouping user-item {
    list user-group {
      max-elements 64;
      key "name";
      description "The list of user group.";
      ntos-ext:nc-cli-one-liner;

      leaf name {
        description "Policy related user group.
         An user group name must carry the authentication domain name.
         For example, /default/group1 indicates group1 in the default authentication domain.";
        type ntos-user-management:user-group-path;
        ntos-ext:nc-cli-no-name;
      }
    }

    list user-name {
      max-elements 64;
      key "name";
      description "The list of user name.";
      ntos-ext:nc-cli-one-liner;
      leaf name {
        description
          "policy related user obj.
           An user obj name must carry the authentication domain name.
           For example: user1@xxx, if xxx is the default domain, do not fill it in and remove the '@' character.";
        type ntos-types:ntos-obj-name-type;
        ntos-ext:nc-cli-no-name;
      }
    }
  }

  grouping match-object {
    description "Detail about match object contained by pbr.";

    list source-zone {
      description "The upstream direction security zone name.";
      max-elements 64;
      ntos-ext:nc-cli-one-liner;
      key "name";
      leaf name {
        type ntos-types:ntos-obj-name-type {
          length "1..127";
        }
        ntos-ext:nc-cli-no-name;
      }
    }

    list ingress {
      description "The upstream direction interface name.";
      max-elements 64;
      ntos-ext:nc-cli-one-liner;
      key "name";
      leaf name {
        type ntos-types:ifname {
          length "1..15";
        }
        ntos-ext:nc-cli-completion-xpath
          "/ntos:config/vrf/ntos-interface:interface/*/*[local-name()='name']";
      }
    }

    list source-network {
      description "The name of source network.";
      max-elements 64;
      ntos-ext:nc-cli-one-liner;
      key "name";
      leaf name {
        type ntos-types:ntos-obj-name-type {
          length "1..127";
        }
        ntos-ext:nc-cli-no-name;
      }
    }

    list dest-network {
      description "The name of destination network.";
      max-elements 64;
      ntos-ext:nc-cli-one-liner;
      key "name";
      leaf name {
        type ntos-types:ntos-obj-name-type {
          length "1..127";
        }
        ntos-ext:nc-cli-no-name;
      }
    }

    list service {
      description "The name of service.";
      max-elements 64;
      ntos-ext:nc-cli-one-liner;
      key "name";
      leaf name {
        type ntos-types:ntos-obj-name-type {
          length "1..127";
        }
        ntos-ext:nc-cli-no-name;
      }
    }

    uses user-item;
    list app {
      description "The name of application.";
      max-elements 64;
      ntos-ext:nc-cli-one-liner;
      key "name";
      leaf name {
        type string {
          length "1..127";
        }
        ntos-ext:nc-cli-no-name;
      }
    }

    leaf time-range {
      description "The name of the time range. The rule only works in the time range defined by this time range.";
      type ntos-types:ntos-obj-name-type {
        length "1..127";
      }
      default "any";
    }
  }

  grouping egress-object {
    description "Detail about outbound object contained by pbr.";

    list out-if {
      description "The list of outbound interface.";
      max-elements 16;
      ntos-ext:nc-cli-one-liner;
      key "name";

      leaf name {
        description "The name of outbound interface.";
        type string {
          length "1..127";
        }
        ntos-ext:nc-cli-completion-xpath
          "/ntos:config/vrf/ntos-interface:interface/*/*[local-name()='name'] |
           /ntos:config/vrf/ntos-interface-group:interface-group/*/*[local-name()='name']";
      }

      leaf group-id {
        description "The group-id of interface-group.";
        type uint8 {
          range "1..255";
        }
      }

      leaf weight {
        description "The weight of interface.";
        type uint32 {
          range "1..40000000";
        }
        default "1000000";
      }

      leaf upload-bandwidth-threshold {
        description "The upload bandwidth threshold of interface.";
        type uint8 {
          range "1..100";
        }
        default "80";
      }

      leaf download-bandwidth-threshold {
        description "The download bandwidth threshold of interface.";
        type uint8 {
          range "1..100";
        }
        default "80";
      }

      leaf priority {
        description "The priority of interface.";
        type uint16 {
          range "1..1000";
        }
        default "1";
      }
      leaf max-conn {
        description "The maximum connection limit of interface.";
        type uint32 {
          range "1..100000000";
        }
        default "2000";
      }
      leaf next-hop {
        description "The IPv4 next-hop of interface.";
        type pbr-ipv4-filter-invalid-address;
      }
      leaf next-hop6 {
        description "The IPv6 next-hop of interface.";
        type pbr-ipv6-filter-invalid-address;
      }
      leaf is-master {
        description "The interface is master.";
        type boolean;
        default "false";
      }
      leaf isp-desc {
        description "The Internet Service Provider name of interface.";
        type string {
          length "0..32";
        }
      }
      leaf intf-type {
        description "The type of interface.";
        type enumeration {
          enum interface;
          enum interface-group;
        }
        default "interface";
      }
      leaf list-index {
        description "The list index of interface.";
        type uint8 {
          range "1..16";
        }
      }
    }

    list next-hop-list {
      description "The list of next-hop.";
      max-elements 8;
      ntos-ext:nc-cli-one-liner;
      key "name";

      leaf name {
        description "The address of next-hop.";
        type union {
          type pbr-ipv4-filter-invalid-address;
          type pbr-ipv6-filter-invalid-address;
        }
      }
    }

    leaf mode {
      description "The mode of policy-based routing.";
      type uint8 {
        range "0..17";
      }
      default "0";
    }
    leaf bandwidth-type {
      description "The bandwidth type of the global pbr";

      type enumeration {
        enum upload;
        enum download;
        enum both;
        enum up-bandwidth;
        enum down-bandwidth;
      }
      default "down-bandwidth";
    }
  }

  grouping sla-template-detail {
    description "Configuration detail of pbr sla template.";

    list sla-template {
      description "The detail of sla template.";
      key "name";
      ordered-by user;

      leaf name {
        description "The name of sla template.";
        type ntos-types:ntos-obj-name-type {
          length "1..127";
        }
      }

      leaf description {
        description "The description of sla template.";
        type ntos-types:ntos-obj-description-type {
          length "1..255";
        }
      }

      leaf delay {
        description "Delay time of sla template.";
        type uint32 {
          range "0..180000" {
          error-message
            "The value of up must >= 0 and <= 180000.";
          }
        }
        units "millisecond";
      }

      leaf jitter {
        description "Delay jitter time of sla template.";
        type uint32 {
          range "0..180000" {
          error-message
            "The value of up must >= 0 and <= 180000.";
          }
        }
        units "millisecond";
      }

      leaf loss {
        description "Loss packages of sla template.";
        type uint8 {
          range "0..100" {
          error-message
            "The value of up must >= 0 and <= 100.";
          }
        }
      }
    }
  }

  grouping pbr-detail {
    description "Configuration detail of pbr.";

    leaf vrf {
      description "Specify the VRF.";
      type ntos-types:ntos-obj-name-type;
    }

    list policy {
      description "The detail of policy-based routing.";
      key "name";
      ordered-by user;

      leaf name {
        description "The name of policy-based routing policy.";
        type ntos-types:ntos-obj-name-type {
          length "1..127";
        }
      }

      leaf description {
        description "The description of policy-based routing policy.";
        type ntos-types:ntos-obj-description-type {
          length "1..255";
        }
      }

      leaf group-name {
        description "The group which policy belongs to.";
        type enumeration {
          enum sla-group;
          enum def-group;
        }
        default 'def-group';
      }

      leaf enabled {
        description "Enable or disable this pbr policy. True means enable and false means disable.";
        type boolean;
        default "false";
      }

      leaf type {
        description "The egress type of policy-based routing.";
        type egress-type;
        default "single";
      }

      uses match-object;

      leaf action {
        description "The action of policy-based routing.";
        type action-type;
        default "deny";
      }
      uses egress-object;

      leaf track {
        description "The track of policy-based routing.";
        type ntos-types:ntos-obj-name-type {
          length "1..127";
        }
        ntos-ext:nc-cli-completion-xpath
          "/ntos:config/ntos:vrf/ntos-ip-track:track/ntos-ip-track:rule/ntos-ip-track:name";
      }

      leaf config-source {
        description "Configuration source of policy-based routing.";
        type config-source-type;
        default "manual";
      }

      leaf sla-type {
        description "Sla type of policy-based routing.";
        type config-source-type;
        default "auto";
      }
      leaf sla-template {
        description "The sla name of policy-based routing.";
        type enumeration {
          enum predefine;
          enum user-defined;
        }
        default "predefine";
      }
      leaf delay {
        description "Delay time of policy-based routing egress.";
        type uint32 {
          range "0..180000" {
          error-message
            "The value of up must >= 0 and <= 180000.";
          }
        }
        units "millisecond";
      }
      leaf jitter {
        description "Delay jitter time of policy-based routing egress.";
        type uint32 {
          range "0..180000" {
          error-message
            "The value of up must >= 0 and <= 180000.";
          }
        }
        units "millisecond";
      }
      leaf loss {
        description "Loss packages of policy-based routing egress.";
        type uint8 {
          range "0..100" {
          error-message
            "The value of up must >= 0 and <= 100.";
          }
        }
      }
    }
  }

  augment "/ntos:config/ntos:vrf" {
    description "Top-Level grouping for policy-based routing configuration.";

    container pbr {
      description "Configuration for policy-based routing.";
      uses pbr-detail;
    }
  }

  augment "/ntos:state/ntos:vrf" {
    description "The state of policy-based routing.";

    container pbr {
      config false;
      uses pbr-detail;
    }
  }

  rpc pbr-debug-config {
    description "Set/show the forward switch of policy-based routing.";

    input {
      leaf vrf {
        description "Specify the VRF.";
        type ntos-types:ntos-obj-name-type;
      }
      choice content {
        description "Pbr stat clear type";

        case set {
          leaf enabled {
            type boolean;
            default "true";
          }
          leaf level {
            type uint8 {
              range "3..7";
            }
            default "3";
          }
        }
        case show {
          leaf show {
            type empty;
          }
        }
      }
    }

    output {
      leaf enabled {
        type boolean;
      }
      leaf level {
        type uint8;
      }
    }

    ntos-ext:nc-cli-hidden;
    ntos-ext:nc-cli-cmd "pbr debug";
    ntos-api:internal;
  }

  rpc pbr-forward-switch {
    description "Set/show the forward switch of policy-based routing.";

    input {
      leaf vrf {
        description "Specify the VRF.";
        type ntos-types:ntos-obj-name-type;
      }
      choice content {
        description "Pbr stat clear type";

        case set {
          leaf set {
            description "Set the forward switch of policy-based routing.";
            type enumeration {
              enum on;
              enum off;
            }
          }
        }
        case show {
          leaf show {
            description "Show the forward switch of policy-based routing.";
            type empty;
          }
        }
      }
    }

    output {
      leaf switch {
        description "Enable/Disable pbr forward on this VRF.";
        type string;
      }
    }

    ntos-ext:nc-cli-hidden;
    ntos-ext:nc-cli-cmd "pbr-forward-switch";
    ntos-api:internal;
  }

  rpc pbr-stat-clear {
    description "Clear hit statistics of policy-based routing.";

    input {
      leaf vrf {
        description "Specify the VRF.";
        type ntos-types:ntos-obj-name-type;
      }

      choice content {
        description "Pbr stat clear type";

        case pbr-id {
          leaf pbr-id {
            description "Clear hit statistics of policy-based routing by pbr id.";
            type string;
          }
        }

        case pbr-id-list {
          leaf pbr-id-list {
            description "Clear hit statistics of policy-based routing by pbr id list [X,Y,Z...].";
            type string;
          }
        }

        case all {
          leaf all {
            description "Clear hit statistics of all policy-based routing.";
            type empty;
          }
        }
      }
    }

    ntos-ext:nc-cli-cmd "pbr-stat-clear";
    ntos-api:internal;
  }

  rpc pbr-intf {
    description "Show interface and policy-based routing reference info.";

    input {
      leaf vrf {
        description "Specify the VRF.";
        type ntos-types:ntos-obj-name-type;
      }
      container content {
        ntos-ext:nc-cli-group "subcommand";

        leaf start {
          description "Start interface number.";
          type uint32;
        }

        leaf end {
          description "End interface number.";
          type uint32;
          must "current() >= ../start" {
            error-message "The end value must be larger than the start value.";
          }
        }

        leaf name {
          description "Show the interface with the specified name.";
          type string;
        }

        leaf id {
          description "Show the interface with the specified interface ID.";
          type string;
        }
      }
    }

    output {
      leaf intf-total {
        description "Total number of policy-based routing used interfaces.";
        type uint32;
      }

      list intf {
        description "The detail of policy-based routing interface.";
        key "name";

        leaf name {
          type string;
        }

        leaf id {
          description "The id of policy-based routing interface entry.";
          type uint32;
        }

        leaf if-id {
          description "The id of interface.";
          type uint32;
        }

        leaf if-addr {
          description "The address of interface.";
          type string;
        }

        leaf next-hop {
          description "The next-hop of interface.";
          type string;
        }

        leaf if-status {
          description "The status of interface.";
          type uint32;
        }

        list pbr-id-src {
          key "name";
          leaf name {
            type string;
          }
        }
        list pbr-id-dst {
          key "name";
          leaf name {
            type string;
          }
        }
        leaf pbr-id-src-num {
          description "The number of this interface as an ingress.";
          type uint32;
        }

        leaf pbr-id-dst-num {
          description "The number of this interface as an egress.";
          type uint32;
        }
      }
    }

    ntos-ext:nc-cli-hidden;
    ntos-ext:nc-cli-show "pbr-intf";
    ntos-api:internal;
  }

  rpc pbr {
    description "Show state of policy-based routing.";

    input {
      leaf vrf {
        description "Specify the VRF.";
        type ntos-types:ntos-obj-name-type;
      }

      container content {
        ntos-ext:nc-cli-group "subcommand";

        choice real-content {
          case type {
            leaf type {
              description "The type of policy.";
              type enumeration {
                enum policy;
                enum sla-policy;
              }
            }

            leaf start {
              description "Start policy number.";
              type uint32;
            }

            leaf end {
              description "End policy number.";
              type uint32;
              must "current() >= ../start" {
                error-message "The end value must be larger than the start value.";
              }
            }

            leaf filter {
              description "Show the policy with the specified field in its name or object.";
              type string;
            }

            leaf name {
              description "Show the policy with the specified name.";
              type string;
            }
          }

          case pbr-id {
            leaf pbr-id {
              description "Show the policy with the specified policy ID.";
              type string;
            }
          }

          case permit-all-pbr {
            leaf permit-all-pbr {
              description "Show all permit pbr.";
              type empty;
            }
          }
        }
      }
    }

    output {
      leaf policy-total {
        description "Total number of policy-based routing.";
        type uint32;
      }

      leaf sla-total {
        description "Total number of sla policy.";
        type uint32;
      }

      list policy {
        description "The detail of policy-based routing.";
        key "name";

        leaf name {
          type string;
        }

        leaf alias-en {
          type string;
        }

        leaf alias-zh {
          type string;
        }

        leaf description {
          type string;
        }

        leaf group-name {
          description "Name of the group which current policy belongs to.";
          type ntos-types:ntos-obj-name-type;
        }

        leaf pbr-id {
          description "The id of policy-based routing.";
          type uint64;
        }

        leaf position-id {
          description "The position id of policy-based routing.";
          type uint32;
        }

        leaf enabled {
          description "Enable or disable policy-based routing.";
          type boolean;
        }

        leaf time-range-enabled {
          description "Enable or disable policy-based routing by time range.";
          type boolean;
        }

        leaf track-status {
          description "Enable or disable policy-based routing by track result.";
          type boolean;
        }

        list source-zone {
          key "name";
          leaf name {
            type ntos-types:ntos-obj-name-type;
          }
        }

        list source-network {
          key "name";
          leaf name {
            type ntos-types:ntos-obj-name-type;
          }
        }

        list dest-network {
          key "name";
          leaf name {
            type ntos-types:ntos-obj-name-type;
          }
        }

        list service {
          key "name";
          leaf name {
            type ntos-types:ntos-obj-name-type;
          }
        }

        list user-group {
          key "name";
          leaf name {
            type ntos-user-management:user-group-path;
          }
        }

        list user-name {
          key "name";
          leaf name {
            type ntos-types:ntos-obj-name-type;
          }
        }

        list app {
          key "name";
          leaf name {
            type string;
          }
          leaf name-i18n {
            type ntos-types:ntos-obj-name-type;
            description
              "Indicate the name of application for internationalization.";
          }
        }

        leaf time-range {
          type ntos-types:ntos-obj-name-type;
        }

        list ingress {
          key "name";
          leaf name {
            type ntos-types:ifname;
          }
        }

        leaf max-outif-num {
          type uint8;
        }

        list out-if {
          key "name";
          leaf name {
            type string;
          }
          leaf group-id {
            type uint8;
          }
          leaf upload-bandwidth-threshold {
            type uint8;
          }
          leaf download-bandwidth-threshold {
            type uint8;
          }
          leaf priority {
            type uint16;
          }
          leaf upload-speed {
            type uint32;
          }
          leaf download-speed {
            type uint32;
          }
          leaf sessions {
            type uint32;
          }
          leaf is-master {
            type boolean;
          }
          leaf intf-type {
            type string;
          }
          leaf isp-desc {
            type string;
          }
          leaf list-index {
            type uint8;
          }
          leaf weight {
            type uint32;
          }
          leaf max-conn {
            type uint32;
          }
          leaf next-hop {
            type string;
          }
          leaf next-hop6 {
            type string;
          }
          leaf run-status {
            type string;
          }
        }

        list next-hop-list {
          key "name";
          leaf name {
            type string;
          }
        }

        leaf action {
          type action-type;
        }

        leaf type {
          type string;
        }

        leaf mode {
          type string;
        }
        leaf bandwidth-type {
            type string;
        }

        leaf out-if-name {
          type string;
        }

        leaf out-if-id {
          type uint32;
        }

        leaf group-id {
          type uint8;
        }

        leaf next-hop {
          type string;
        }

        leaf track {
          type string;
        }

        leaf sla-type{
          type string;
        }

        leaf sla-template {
          type string;
        }

        leaf delay {
          type uint32;
        }

        leaf jitter {
          type uint32;
        }

        leaf loss {
          type uint8;
        }
        leaf match-count {
          description "The matching times for this policy.";
          type uint64;
        }

        leaf create-time {
          description "The policy creation time.";
          type string;
        }

        leaf first-match-time {
          description "The first match time of this policy";
          type string;
        }

        leaf last-match-time {
          description "The last match time of this policy.";
          type string;
        }

        leaf invalid {
          description "Check policy-based routing result.";
          type boolean;
        }

        leaf invalid-type {
          description "The invalid type of the current pbr.";
          type string;
        }

        list invalid-info {
          description "The invalid info of the current pbr.";
          key "info";
          leaf info {
            type string;
          }
          ntos-ext:nc-cli-one-liner;
        }

        leaf _next {
          description "The pbr next node.";
          type string;
        }

        leaf _prev {
          description "The pbr prev node.";
          type string;
        }

        leaf config-source {
          type config-source-type;
          description "Source of policy-based routing.";
        }
      }
    }

    ntos-ext:nc-cli-show "pbr";
    ntos-api:internal;
  }
  rpc search-other-info {
    description "Get interface information by name or type.";

    input {
      leaf vrf {
        description "Specify the VRF.";
        type ntos-types:ntos-obj-name-type;
      }
      container interface {
        ntos-ext:nc-cli-group "subcommand";
        leaf name {
          description "Get interface information by specified name.";
          type ntos-types:ifname;
        }
        leaf kind {
          description "Get interface information by specified kind.";
          type string;
        }
        leaf link-type {
          description "Get interface information by specified link type.";
          type string;
        }
        leaf name-set {
          description "Get interface information by specified name set.";
          type string;
        }
      }
    }

    output {
      leaf interface-total {
        type uint32;
      }
      list interface {
        leaf name {
          type string;
        }
        container upload-bandwidth {
          leaf upload-bandwidth-value {
            type uint32;
          }
          leaf upload-bandwidth-unit {
            type string;
          }
          leaf upload-bandwidth-threshold {
            type uint16;
          }
        }
        container download-bandwidth {
          leaf download-bandwidth-value {
            type uint32;
          }
          leaf download-bandwidth-unit {
            type string;
          }
          leaf download-bandwidth-threshold {
            type uint16;
          }
        }
        leaf next-hop {
          type string;
        }
        leaf next-hop6 {
          type string;
        }
        leaf max-conn {
          type uint32;
        }
      }
      leaf buffer {
        type string;
        ntos-ext:nc-cli-stdout;
      }
    }

    ntos-ext:nc-cli-cmd "pbr search";
    ntos-api:internal;
  }

  rpc pbr-search-sla-intf {
    description "Get sla policy info by interface name.";

    input {
      list policy {
        description "The name of sla policy.";
        key "name";
        leaf name {
          type ntos-types:ntos-obj-name-type {
            length "1..127";
          }
        }
        leaf sla-intf {
          type string;
        }
      }
    }

    output {
      leaf if-total {
        description "Total number of sla used interfaces.";
        type uint8;
      }

      list sla-intf {
        leaf policy {
          type string;
        }
        leaf name {
          type string;
        }
        leaf upload-speed {
          type uint32;
        }
        leaf download-speed {
          type uint32;
        }
        leaf sessions {
          type uint16;
        }
        leaf is-master {
          type boolean;
        }
        leaf isp-desc {
          type string;
        }
        leaf list-index {
          type uint8;
        }
      }
    }

    ntos-ext:nc-cli-cmd "pbr search sla-intf";
    ntos-api:internal;
  }

  rpc pbr-search-sla-template {
    description "Get quality template policy info by sla name.";

    input {
      list policy {
        description "The name of sla policy.";
        key "name";
        leaf name {
          type ntos-types:ntos-obj-name-type {
            length "1..127";
          }
        }
      }
    }

    output {
      leaf template-total {
        description "Total number of sla template.";
        type uint8;
      }

      list sla-template {
        leaf name {
          type string;
        }
        leaf delay {
          type uint32;
        }
        leaf jitter {
          type uint32;
        }
        leaf loss {
          type uint8;
        }
      }
    }

    ntos-ext:nc-cli-cmd "pbr search sla-template";
    ntos-api:internal;
  }

  rpc pbr-timer {
    description "Set pbr timer.";

    input {
      leaf vrf {
        description "Specify the VRF.";

        type ntos-types:ntos-obj-name-type;
      }

      choice content {
        description "Pbr timer management.";

        case policy-async {
          container policy-async {
            description "Set the async timer of pbr.";

            leaf interval {
              description "Set the pbr async timer interval time.";

              type uint16 {
                range "1..3600";
              }
              default "30";
            }
          }
        }

        case show {
          leaf show {
            description "Show the timer configure of pbr.";
            type empty;
          }
        }
      }
    }

    output {
      container policy-async {
        leaf interval {
          type uint16;
        }
      }
    }

    // ntos-ext:nc-cli-hidden;
    ntos-ext:nc-cli-cmd "pbr timer";
    ntos-api:internal;
  }
}
