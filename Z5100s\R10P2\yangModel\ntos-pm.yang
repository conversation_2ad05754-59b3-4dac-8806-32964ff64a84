module ntos-pm {
  namespace "urn:ruijie:ntos:params:xml:ns:yang:tracker:icmp";
  prefix ntos-pm;

  import ntos {
    prefix ntos;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-commands {
    prefix ntos-cmd;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos-tracker {
    prefix ntos-tracker;
  }
  import ntos-types {
    prefix ntos-types;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "RUIJIE NTOS tracker.";

  revision 2019-07-29 {
    description
      "Initial version.";
    reference "";
  }

  typedef pm-state {
    type enumeration {
      enum admin-down {
        value 0;
        description
          "Administratively down.";
      }
      enum down {
        value 1;
        description
          "Down.";
      }
      enum init {
        value 2;
        description
          "Initializing.";
      }
      enum up {
        value 3;
        description
          "Up.";
      }
    }
    description
      "Path Monitoring session state.";
  }

  grouping icmp-tracker-config {
    description
      "Configuration data for tracker.";

    leaf name {
      type ntos-tracker:tracker-name;
      must "current() != 'bfd'" {
        error-message "'bfd' cannot be used as tracker name.";
      }
      description
        "The name of the tracker.";
    }

    leaf address {
      type ntos-inet:host;
      description
        "The host to track.";
    }

    leaf vrf {
      type ntos:vrf-name;
      must "string(current()) = 'main' or boolean(/ntos:config/ntos:vrf[ntos:name=string(current())])" {
        error-message "The vrf must exist in configuration.";
      }
      mandatory true;
      description
        "The vrf in which the ping must be sent. Default is the
           current netns.";
      ntos-ext:nc-cli-completion-xpath
        "/ntos:state/ntos:vrf/ntos:name";
    }

    leaf source {
      type ntos-inet:ip-address;
      description
        "Source address in the ping packet.";
    }

    leaf interface {
      type ntos-types:ifname;
      description
        "The interface to bind the tracker to.";
      ntos-ext:nc-cli-completion-xpath
        "/ntos:config/ntos:vrf[name='main'][count(current()/ntos-pm:vrf)=0]/ntos-interface:interface/*/*[local-name()='name'] |
         /ntos:config/ntos:vrf[name=string(current()/ntos-pm:vrf)][count(current()/ntos-pm:vrf)!=0]/ntos-interface:interface/*/*[local-name()='name'] |
         /ntos:state/ntos:vrf[name='main'][count(current()/ntos-pm:vrf)=0]/ntos-interface:interface/*/*[local-name()='name'] |
         /ntos:state/ntos:vrf[name=string(current()/ntos-pm:vrf)][count(current()/ntos-pm:vrf)!=0]/ntos-interface:interface/*/*[local-name()='name']";
    }

    leaf dhcp-interface {
      type ntos-types:ifname;
      description
        "The address, gateway and source will be taken from DHCP on this
         interface unless explicitly specified in the tracker.";
      ntos-ext:nc-cli-completion-xpath
        "/ntos:config/ntos:vrf[name='main'][count(current()/ntos-pm:vrf)=0]/ntos-interface:interface/*/*[local-name()='name'] |
         /ntos:config/ntos:vrf[name=string(current()/ntos-pm:vrf)][count(current()/ntos-pm:vrf)!=0]/ntos-interface:interface/*/*[local-name()='name']";
      }

    leaf gateway {
      type ntos-inet:host;
      description
        "The gateway to use to send the packet.";
    }

    leaf period {
      type uint16 {
        range "100..65535";
      }
      units "milliseconds";
      default "500";
      description
        "Time between each ping.";
    }

    leaf threshold {
      type uint8 {
        range "1..255";
      }
      must '. <= ../total' {
        error-message "threshold must be lower or equal than total";
      }
      default "1";
      description
        "Number of successful pings among <total> to consider peer as
         reachable.";
    }

    leaf total {
      type uint8 {
        range "1..255";
      }
      default "1";
      description
        "Check the threshold among this number of last pings to consider
         peer as reachable.";
    }

    leaf packet-size {
      type uint16 {
        range "1..65535";
      }
      units "bytes";
      default "100";
      description
        "Packet size.";
    }

    leaf packet-tos {
      type uint8 {
        range "1..255";
      }
      default "192";
      description
        "ToS to apply to the packet.";
    }

    leaf timeout {
      type uint16 {
        range "100..65535";
      }
      units "milliseconds";
      must ". <= ../period" {
        error-message "timeout must be lower or equal to period.";
      }
      description
        "Time during which a ping reply is considered as valid. If unset,
         it timeouts after a ping period.";
    }
  }

  grouping icmp-tracker-state {
    description
      "State data for tracker.";

    leaf state {
      type pm-state;
      description
        "Status of the last ping.";
    }

    leaf diagnostic {
      type string;
      description
        "Local session diagnostic.";
    }
  }

  rpc show-path-monitoring {
    description
      "Show path monitoring information.";
    input {

      leaf vrf {
        type ntos:vrf-name;
        description
          "Specify the VRF.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf address {
        type ntos-inet:ip-address;
        description
          "IP address of the peer.";
      }

      leaf operational {
        type empty;
        description
          "Show session operational information.";
      }
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-ext:feature "product";
    ntos-ext:nc-cli-show "path-monitoring";
    ntos-api:internal;
  }

  augment "/ntos:config/ntos-tracker:tracker" {
    description
      "The ICMP tracker configuration.";

    list icmp {
      must "count(../ntos-pm:icmp
               [vrf=string(current()/vrf)]
               [address=string(current()/address) or not(current()/address)]
               [(not(address) and not(current()/address)) or boolean(current()/address)]
               [source=string(current()/source) or not(current()/source)]
               [(not(source) and not(current()/source)) or boolean(current()/source)]
               [interface=string(current()/interface) or not(current()/interface)]
               [(not(interface) and not(current()/interface)) or boolean(current()/interface)]
               [dhcp-interface=string(current()/dhcp-interface) or not(current()/dhcp-interface)]
               [(not(dhcp-interface) and not(current()/dhcp-interface)) or boolean(current()/dhcp-interface)]
            ) = 1" {
        error-message "This session already exists.";
      }
      must 'boolean(current()/address) or boolean(current()/dhcp-interface)' {
        error-message "Incomplete tracker: address or dhcp-interface must be set.";
      }
      key "name";
      description
        "List of tracked addresses using ICMP echo requests.";
      ntos-ext:feature "product";
      ntos-ext:nc-cli-one-liner;
      uses icmp-tracker-config;
    }
  }

  augment "/ntos:state/ntos-tracker:tracker" {
    description
      "The ICMP tracker state.";

    list icmp {
      key "name";
      description
        "List of tracked addresses using ICMP echo requests.";
      ntos-ext:feature "product";
      ntos-ext:nc-cli-one-liner;
      uses icmp-tracker-config;
      uses icmp-tracker-state;
    }
  }
}
