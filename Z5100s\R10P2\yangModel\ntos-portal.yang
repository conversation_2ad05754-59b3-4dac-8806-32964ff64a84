module ntos-portal {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:portal";
  prefix ntos-portal;

  import ntos {
    prefix ntos;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-system {
    prefix ntos-system;
  }
  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS Portal.";

  revision 2023-03-28 {
    description
      "Initial version.";
    reference
      "";
  }

  revision 2024-11-25 {
    description
      "Add Local Portal Authentication No-Perception Configuration.";
    reference
      "";
  }

  identity portal {
    base ntos-types:SERVICE_LOG_ID;
    description
      "Portal service.";
  }

  grouping system-portal-config {
    description
      "Configuration data for system portal configuration.";

    leaf enabled {
      type boolean;
      default "false";
      description
        "Enable or disable the portal.";
    }

    leaf port {
      type ntos-inet:port-number;
      default "8081";
      description
        "The local port of the portal.";
    }

    container customized-page {
      description
        "Configure customized content for the Portal authentication page.";
      leaf modify-password-enabled {
        type boolean;
        description
          "Enable or disable the switch of modify password.";
      }
    }

    leaf ssl-enabled {
      type boolean;
      default "false";
      description
        "Enable or disable the portal ssl.";
    }

    leaf redirection-mode {
      default no-redirection;
      description "Configure the redirection mode for the authenticated users.";
      type enumeration {
          enum "no-redirection" {
              description "No redirection, and stay on authentication page.";
          }
          enum "previous-page" {
              description "Redirects to the last page you visited.";
          }
          enum "customized-url" {
              description "Redirects to the customized URL.";
          }
      }
    }

    leaf customized-url {
      when "../redirection-mode = 'customized-url'";
      description "Configure the customized URL.";
      type ntos-types:http-dual-stack-url;
    }
    container no-perception {
      description
        "No-Perception Configuration.";
      leaf enabled {
        description
          "Enable No-Perception's config.";
        type boolean;
      }
      leaf time-day {
        description
          "Configure the time-day of the No-Perception's user.";
        type uint32;
      }
      leaf mac-limit {
        description
          "Configure the Maximum Number of MAC Addresses per User.";
        type uint32;
      }
    }
  }

  grouping portal-customized-template-config {
    description
      "Portal customized template configuration grouping.";
    
    list customized-template {
      key "name";
      description
        "List of customized authentication templates for authentication function and portal page UI customization.";
      
      leaf name {
        type union {
          type enumeration {
            enum def-template {
              description
                "The default authentication template.";
            }
          }
          type ntos-types:ntos-obj-name-type;
        }
        description
          "Name identifier for the customized authentication template.";
      }
      
      leaf description {
        type string;
        description
          "Description of the customized authentication template.";
      }
      
      container web-page-customization {
        description
          "Portal page UI customization configuration within the authentication template.";
        
        container logo-config {
          description
            "Logo configuration for portal page UI customization.";
          
          leaf display-enabled {
            type boolean;
            default "false";
            description
              "Global switch to enable or disable logo display in portal page UI. When disabled, no logos will be shown regardless of individual logo settings.";
          }
          
          list logo {
            key "name";
            description
              "List of logos for portal page UI customization.";
            
            leaf name {
              type ntos-types:ntos-obj-name-type;
              description
                "Name identifier for the logo in portal page UI.";
            }
            
            leaf description {
              type string;
              description
                "Description of the logo for portal page UI customization.";
            }
            
            leaf width {
              type uint32;
              units "pixels";
              description
                "Width of the logo in portal page UI (pixels).";
            }
            
            leaf enabled {
              type boolean;
              default "true";
              description
                "Enable or disable this individual logo in portal page UI. Only effective when logo-config/display-enabled is true.";
            }
          }
        }
        
        container background-config {
          description
            "Background configuration for portal page UI customization.";
          
          leaf default-background-enabled {
            type boolean;
            default "true";
            description
              "Enable or disable the default system background image. When enabled, the default background image will be used. When disabled, at least one custom background image must be enabled.";
          }
          
          list background-image {
            key "name";
            description
              "List of custom background images for portal page UI customization. Maximum 4 custom background images are allowed.";
            
            leaf name {
              type ntos-types:ntos-obj-name-type;
              description
                "Name identifier for the custom background image in portal page UI.";
            }
            
            leaf description {
              type string;
              description
                "Description of the custom background image for portal page UI customization.";
            }
            
            leaf enabled {
              type boolean;
              default "true";
              description
                "Enable or disable this custom background image in portal page UI. When default-background-enabled is false, at least one custom background image must be enabled.";
            }
          }
        }
      }
    }
  }

  grouping portal-language-list-config {
    description
      "Portal language configuration grouping.";
    list language {
      key "name";
      ordered-by user;
      description
        "List of supported languages.";
      
      leaf name {
        type ntos-types:ntos-obj-name-type;
        description
          "Language identifier (e.g., en_US, zh_CN).";
      }
      
      leaf display-name {
        mandatory true;
        type string;
        description
          "Display name of the language (e.g., English, 简体中文).";
      }
      
      leaf is-default {
        type boolean;
        description
          "Whether this is the default language.";
      }

      leaf is-builtin {
        config false;
        type boolean;
        description
          "Whether this is the builtin language.";
      }
      
      leaf package-name {
        mandatory true;
        type ntos-types:ntos-obj-name-type;
        description
          "Name of the language package file.";
      }
      
      leaf enabled {
        type boolean;
        description
          "Whether the language is enabled.";
      }
      
      leaf description {
        type ntos-types:ntos-obj-description-type;
        description
          "Optional description for this language configuration.";
      }

      leaf prev {
        config false;
        type string;
        description
          "Name of the previous language in the sequence. Used for navigation through the language list.";
      }

      leaf next {
        config false;
        type string;
        description
          "Name of the next language in the sequence. Used for navigation through the language list.";
      }
    }
    
  }

  rpc portal-language-config {
    description
      "Portal language configuration.";
    input {
      leaf vrf {
        type ntos-types:ntos-obj-name-type;
        description
          "Specify the VRF.";
      }

      container content {
        ntos-ext:nc-cli-group "subcommand";

        choice content-type {
          case get-default {
            leaf default {
              type empty;
              description
                "Show default language.";
            }
          }
          case get-language {
            leaf filter {
              type string;
              description
                "Filter.";
            }
            leaf name {
              type string;
              description
                "Show language by name";
            }

            leaf start {
              type uint32;
              description
                "Start offset of result.";
            }

            leaf end {
              type uint32;
              description
                "End offset of result.";
            }
          }
        }
      }
    }
    
    output {
      leaf language-total {
        description
          "Total number of language.";
        type uint32;
      }
      uses portal-language-list-config;
    }
    ntos-ext:nc-cli-show "portal language-config";
    ntos-api:internal;
  }

  rpc portal-customized-template {
    description
      "Portal customized template configuration.";
    input {
      leaf vrf {
        type ntos-types:ntos-obj-name-type;
        description
          "Specify the VRF.";
      }

      container content {
        ntos-ext:nc-cli-group "subcommand";

        leaf name {
          type union {
            type enumeration {
              enum def-template {
                description
                  "The default authentication template.";
              }
            }
            type ntos-types:ntos-obj-name-type;
          }
          description
            "Show template by name";
        }
      }
    }
    
    output {
      leaf template-total {
        description
          "Total number of templates.";
        type uint32;
      }
      uses portal-customized-template-config;
    }
    ntos-ext:nc-cli-show "portal customized-template";
    ntos-api:internal;
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "Portal configuration.";

    container wba-portal {
      description
        "Portal configuration.";
      uses system-portal-config;
    }

    container portal-customized-template {
      description
        "Portal customized template configuration.";
      uses portal-customized-template-config;
    }

    container portal-languages {
      description
        "Portal language configuration.";
      uses portal-language-list-config;
    }
  }

  augment "/ntos:state/ntos:vrf" {
    description
      "Portal state.";

    container wba-portal {
      description
        "Portal state.";
      uses system-portal-config;
    }

    container portal-customized-template {
      description
        "Portal customized template state.";
      uses portal-customized-template-config;
    }

    container portal-languages {
      description
        "Portal language configuration.";
      uses portal-language-list-config;
    }

  }
}
