module ntos-ppp {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:ppp";
  prefix ntos-ppp;
  
  import ntos {
    prefix ntos;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos-types {
    prefix ntos-types;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel: 4008-111-000
    E-mail: <EMAIL>";
  description
    "Ruijie NTOS ppp module.";
    
  revision 2024-10-29 {
    description
      "Initial version.";
    reference
      "";
  }

  grouping vrf {
    leaf vrf {
      description
        "VRF name.";

      type string;
      default "main";
    }
  }

  typedef ppp-work-mode {
    type enumeration {
      enum client {
        description
          "PPP work in client.";
      }
      enum server {
        description
          "PPP work in server.";
      }
    }
  }

  typedef auth-mode {
    type enumeration {
      enum no-auth {
        description
          "No auth.";
      }
      enum pap {
        description
          "PAP mode.";
      }
      enum chap {
        description
          "CHAP mode.";
      }
      enum mschap-v1 {
        description
          "MSCHAPv1 mode.";
      }
      enum mschap-v2 {
        description
          "MSCHAPv1 mode.";
      }
    }
  }

  typedef ip-mode {
    type enumeration {
      enum ipv4 {
        description
          "IPV6.";
      }
      enum ipv6 {
        description
          "IPV6.";
      }
      enum none {
        description
          "NONE.";
      }
    }
  }

  typedef ccp-mode {
    type enumeration {
      enum mppe {
        description
          "MPPE.";
      }
      enum none {
        description
          "NONE.";
      }
    }
  }

  grouping user-unit {
    leaf user-name {
      type string {
        length "1..127";
      }
      description
        "User name.";
    }
    leaf user-ip {
      type ntos-inet:ip-address;
      description
        "User ip.";
    }
    leaf insert-ip {
      type ntos-inet:ip-address;
      description
        "Insert ip.";
    }
    leaf access-ip {
      type ntos-inet:ip-address;
      description
        "Access ip.";
    }
    leaf host-name {
      type string {
        length "1..127";
      }
      description
        "Host name.";
    }
    leaf insert-time {
      type uint64;
      description
        "insert time.";
    }
    leaf online-time {
      type uint64;
      description
        "Online time.";
    }
    leaf interface-name {
      type string {
        length "1..127";
      }
      description
        "Interface name.";
    }
  }

  grouping ppp-session-unit {
    leaf session-id {
      type uint32;
      description
        "Ppp session id.";
    }
    leaf tunnel-status {
      type uint32;
      description
        "Status of tunnel.";
    }
    leaf flow-statistics {
      type uint32;
      description
        "Statistics of flow.";
    }
    uses user-unit;
  }

  grouping auth-unit {
    leaf-list mode {
      type auth-mode;
      description
        "Auth mode.";
    }
    container auth-config {
      description
        "Configuration for client of server.";
      choice what {
        default virtual-template;
        case virtual-template {
          container virtual-template {
            leaf ppp-domain {
              type string {
                length "1..127";
              }
              description
                "PPP domain.";
            }
          }
        }
        case virtual-ppp {
          container virtual-ppp {
            leaf user-name {
              type string {
                length "1..127";
              }
              description
                "User name.";
            }
            leaf password {
              type string {
                length "1..127";
              }
              description
                "Password.";
            }
          }
        }
      }
    }
  }

  grouping ncp-unit {
    leaf ip-mode-support {
      type ip-mode;
      description
        "Type of ip mode.";
    }
    container ncp-config {
      description
        "Configuration for client of server.";
      choice what {
        default virtual-template;
        case virtual-template {
          container virtual-template {
            leaf address-pool {
              type string {
                length "1..127";
              }
              description
                "Address pool.";
            }
            leaf major-dns {
              type ntos-inet:ip-address;
              description
                "Major dns server.";
            }
            leaf minor-dns {
              type ntos-inet:ip-address;
              description
                "Minor dns server.";
            }
            leaf major-wins {
              type ntos-inet:ip-address;
              description
                "Major wins server.";
            }
            leaf minor-wins {
              type ntos-inet:ip-address;
              description
                "Minor wins server.";
            }
            leaf local-ip {
              type ntos-inet:ip-address;
              description
                "Local ip.";
            }
          }
        }
        case virtual-ppp {
          container virtual-ppp {
            leaf accept-dns-enabled {
              type boolean;
              default false;
              description
                "Accept dns enabled.";
            }
            leaf local-ip {
              type ntos-inet:ip-address;
              description
                "Local ip.";
            }
            leaf pppid {
              type int32;
              description
                "ppp port id.";
            }
          }
        }
      }
    }
    leaf mru {
      type uint32;
      default 1400;
      description
        "MRU range.";
    }
  }

  grouping ppp-unit {
    leaf name {
      type string {
        length "1..16";
      }
      description
        "PPP class name.";
    }
    container security-zone {
      description
        "Security zone.";
      leaf-list zone {
        type string;
        description
            "Select security zone to protect.";
      }
    }
    leaf enabled {
      type boolean;
      default true;
      description
        "PPP is enable or not.";
    }
    leaf lcp-echo-interval {
      type uint32;
      description
        "LCP echo interval.";
    }
    leaf lcp-norsp-max {
      type uint32;
      description
        "LCP max no response count.";
    }
    uses auth-unit;
    uses ncp-unit;
    leaf ccp-work-mode {
      type ccp-mode;
      description
        "CCP work mode.";
    }
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "The configuration of ppp.";
    container ppp {
      list ppp-list {
        key "name";
        uses ppp-unit;
      }
    }
  }

  rpc ppp-config-show {
    description
      "Get ppp configuration";
    input {
      uses vrf;
      leaf start {
        type uint32;
      }
      leaf end {
        type uint32;
      }
    }
    output {
      container ppp {
        leaf total {
          type uint32;
        }
        list ppp-list {
          key "name";
          uses ppp-unit;
        }
      }
    }
    ntos-ext:nc-cli-show "ppp";
  }

  rpc ppp-config-get {
    description
      "Get ppp configuration";
    input {
      uses vrf;
      leaf name {
        type string {
          length "1..127";
        }
        description
          "PPP class name.";
      }
    }
    output {
      container ppp {
        uses ppp-unit;
      }
    }
    ntos-ext:nc-cli-cmd "ppp get";
  }

  rpc get-references {
    description
      "Get reference list";
    input {
      uses vrf;
      leaf name {
        type string {
          length "1..127";
        }
        description
          "PPP class name.";
      }
      leaf start {
        type uint32;
      }
      leaf end {
        type uint32;
      }
    }
    output {
      container ppp {
        leaf total {
          type uint32;
        }
        leaf-list reference-list {
            type uint32;
            description
              "Reference module id.";
        }
      }
    }
    ntos-ext:nc-cli-cmd "ppp ref";
  }

  rpc ppp-set-log {
    description
      "Up or down pptp tunnel";
    input {
      uses vrf;
      leaf submodule {
        type string {
          length "1..127";
        }
      }
      leaf level {
        type ntos-types:log-level;
      }
    }
    ntos-ext:nc-cli-cmd "ppp set";
  }

  rpc ppp-get-status {
    description
      "Get configuration status.";
    output {
      container ppp {
        leaf status {
          type uint32;
        }
      }
    }
    ntos-ext:nc-cli-cmd "ppp get status";
  }

  rpc show-ppp-config {
    output {
      leaf buffer {
        description
          "Command output buffer since last request.";

        type string;
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }

    ntos-ext:nc-cli-show "ppp config";
  }

  rpc show-ppp-intf {
    output {
      leaf buffer {
        description
          "Command output buffer since last request.";

        type string;
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }

    ntos-ext:nc-cli-show "ppp intf";
  }

  rpc show-ppp-ippool {
    output {
      leaf buffer {
        description
          "Command output buffer since last request.";

        type string;
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }

    ntos-ext:nc-cli-show "ppp ippool";
  }

  rpc show-ppp-statistics {
    output {
      leaf buffer {
        description
          "Command output buffer since last request.";

        type string;
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }

    ntos-ext:nc-cli-show "ppp statistics";
  }

  rpc clear-ppp-statistics {
    output {
      leaf buffer {
        description
          "Command output buffer since last request.";

        type string;
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-cmd "clear ppp statistics";
  }

  rpc show-ppp-session {
    input {
      leaf vrf {
        type string;
        default "main";
        description
          "Specify the VRF.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf session-id {
        type uint16 {
          range "0..65535";
        }
        description
          "PPP session id.";
      }
    }

    output {
      leaf buffer {
        description
          "Command output buffer since last request.";

        type string;
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "ppp session";
  }

  rpc clear-ppp-session {
    input {
      leaf vrf {
        type string;
        default "main";
        description
          "Specify the VRF.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf session-id {
        type uint16 {
          range "0..65535";
        }
        description
          "PPP session id. Session-id is 0 means clear all PPP session";
      }
    }

    output {
      leaf buffer {
        description
          "Command output buffer since last request.";

        type string;
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-cmd "clear ppp session";
  }

  rpc ppp-security-policy {
    description
      "Set securiy policy for ppp in firewall series product";
    input {
      uses vrf;
      leaf operate {
        type enumeration {
          enum check {
            description
              "Check depends security policy exists";
          }
          enum recover {
            description
              "Recover depends security policy";
          }
          enum create {
            description
              "Create depends security policy";
          }
          enum delete {
            description
              "Delete depends security policy";
          }
        }
      }
      leaf ppp-name {
        type string {
          length "1..127";
        }
      }
    }

    output {
      leaf result {
        type int32;
      }
      leaf message {
        type string {
          length "0..254";
        }
      }
    }
    ntos-ext:nc-cli-cmd "ppp security-policy";
  }
}
