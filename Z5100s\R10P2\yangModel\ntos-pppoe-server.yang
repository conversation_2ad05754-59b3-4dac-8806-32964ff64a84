module ntos-pppoe-server {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:pppoe-server";
  prefix ntos-pppoe-server;

  import ntos {
    prefix ntos;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-extensions {
    prefix ntos-extensions;
  }
  import ntos-if-types {
    prefix ntos-if;
  }
  import ntos-ppp {
    prefix ntos-ppp;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS PPPoE Server module.";

  revision 2024-11-04 {
    description
      "Add PPPoE Server config.";
    reference "";
  }

  identity pppoe-server {
    base ntos-types:SERVICE_LOG_ID;
    description
      "PPPoE Server service.";
  }

  grouping sessions-limit-config {
    description
      "PPPoE Server group sessions limits configuration.";

    leaf local-mac-limit {
      type uint32 {
        range "0..65535";
      }
      default "65535";
      description
        "Set the maximum number of incoming sessions that one MAC address can accept on the local side.";
    }

    leaf max-limit {
      type uint32 {
        range "0..65535";
      }
      default "65535";
      description
        "Set the maximum number of sessions that the current system allows to connect.";
    }

    leaf per-mac-limit {
      type uint32 {
        range "0..500";
      }
      default "100";
      description
        "Set the maximum number of incoming sessions that one MAC address can accept on the peer side.";
    }

  }

  grouping pppoe-server-group-config {
    description
      "PPPoE Server group configuration.";

    list group {
      key "name";
      description
          "The detail of group.";
      ordered-by user;

      leaf name {
        type string {
          length "1..63";
        }
        description
          "Set the name of group.";
      }

      leaf ppp {
        type string;
        ntos-extensions:nc-cli-completion-xpath
                "/ntos:config/ntos:vrf[ntos:name='main']/ntos-ppp:ppp/*[local-name()='ppp-list']/*[local-name()='name']";
        description
          "Reference ppp name.";
      }

      leaf pppoe-only {
        type boolean;
        default "true";

        description
          "Enable or disable force pppoe.";
      }

      leaf ac-cookie {
        type boolean;
        default "false";

        description
          "Enable or disable ac-cookie.";
      }

      list excluded-ip {
        key "ip";
        description
          "The additional ip list.";
        ordered-by user;

        leaf ip {
          type union {
            type ntos-inet:ipv4-address;
            type ntos-inet:ipv6-address;
          }
          description "Additional IPv4/IPv6 address.";
        }

        ntos-extensions:nc-cli-one-liner;
      }

      container sessions {
        description
          "Set the sessions limits.";

        uses sessions-limit-config;
      }
    }
  }

  grouping pppoe-server-config {
    container pppoe-server {
      leaf group-name {
        type string {
          length "1..63";
        }
        ntos-extensions:nc-cli-completion-xpath
                "/ntos:config/ntos:vrf[ntos:name='main']/ntos-pppoe-server:pppoe-server/*[local-name()='group']/*[local-name()='name']";
        description
          "Reference the group of PPPoE Server.";
      }
    }
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "Set PPPoE Server configuration.";

    container pppoe-server {
      description
        "Configuration of PPPoE Server.";

      leaf enabled {
        description
          "Enable or disable PPPoE Server.";

        type boolean;
        default "false";
      }

      uses pppoe-server-group-config;
    }
  }

  rpc show-pppoe-server-session {
    input {
      leaf vrf {
        type string;
        default "main";
        description
          "Specify the VRF.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf session-id {
        type uint16 {
          range "0..65535";
        }
        description
          "PPPoE Server session id.";
      }
    }

    output {

      leaf buffer {
        description
          "Command output buffer since last request.";

        type string;
        ntos-extensions:nc-cli-stdout;
        ntos-extensions:nc-cli-hidden;
      }
    }

    ntos-extensions:nc-cli-show "pppoe-server session";
  }

  rpc clear-pppoe-server-session {
    input {
      leaf vrf {
        type string;
        default "main";
        description
          "Specify the VRF.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf session-id {
        type uint16 {
          range "0..65535";
        }
        description
          "PPPoE Server session id. Session-id is 0 means clear all PPPoE Server session";
      }
    }

    output {

      leaf buffer {
        description
          "Command output buffer since last request.";

        type string;
        ntos-extensions:nc-cli-stdout;
        ntos-extensions:nc-cli-hidden;
      }
    }

    ntos-extensions:nc-cli-cmd "clear pppoe-server session";
  }

  rpc show-pppoe-server-statistics {

    output {
      leaf buffer {
        description
          "Command output buffer since last request.";

        type string;
        ntos-extensions:nc-cli-stdout;
        ntos-extensions:nc-cli-hidden;
      }
    }

    ntos-extensions:nc-cli-show "pppoe-server statistics";
  }

  rpc clear-pppoe-server-statistics {

    output {
      leaf buffer {
        description
          "Command output buffer since last request.";

        type string;
        ntos-extensions:nc-cli-stdout;
        ntos-extensions:nc-cli-hidden;
      }
    }

    ntos-extensions:nc-cli-cmd "clear pppoe-server statistics";
  }

  rpc show-pppoe-server-config {

    output {
      leaf buffer {
        description
          "Command output buffer since last request.";

        type string;
        ntos-extensions:nc-cli-stdout;
        ntos-extensions:nc-cli-hidden;
      }
    }

    ntos-extensions:nc-cli-show "pppoe-server config";
  }

  rpc show-web-pppoe-server-config {

    output {

      leaf name {
        type string;

        description
          "Name of PPPoE Server.";
      }

      leaf ppp-name {
        type string;

        description
          "PPP name of PPPoE Server.";
      }

      leaf enabled {
        type boolean;

        description
          "Enable or disable PPPoE Server.";
      }

      leaf pppoe-only {
        type boolean;

        description
          "Enable or disable force pppoe.";
      }

      list interface {
        key "name";
        description
          "The PPPoE Server interface list.";
        ordered-by user;

        leaf name {
          type string;
        }
      }

      container ppp {
        uses ntos-ppp:ppp-unit;
      }

      leaf is-clear {
        type uint16;
        description
          "clearing.";
      }

    }
    ntos-extensions:nc-cli-show "pppoe-server config web";
  }

  rpc clear-all-pppoe-server-config {

    output {
      leaf buffer {
        description
          "Command output buffer since last request.";

        type string;
        ntos-extensions:nc-cli-stdout;
        ntos-extensions:nc-cli-hidden;
      }
    }

    ntos-extensions:nc-cli-cmd "clear pppoe-server all config";
  }

}