module ntos-radius {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:rdsd";
  prefix ntos-radius;

  import ntos {
    prefix ntos;
  }
  import ntos-inet-types {
    prefix ntos-inet-types;
  }
  import ntos-system {
    prefix ntos-system;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-types {
    prefix ntos-types;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS Radius management.";

  revision 2022-10-14 {
    description
      "Initial version.";
    reference "";
  }

  identity rdsd {
    base ntos-types:SERVICE_LOG_ID;
    description
      "Radius service.";
  }

  grouping server-common {
    leaf ip {
      description
        "Radius server ip";
      type ntos-inet-types:ipv4-address;
    }
    leaf port {
      description
        "Radius server port";
      type ntos-inet-types:port-number;
    }
    leaf type {
      description
        "Radius server type: auth/acct";
      type enumeration {
        enum accounting {
          description
            "An accounting server";
        }
        enum authentication {
          description
            "An authentication server";
        }
      }
    }
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "Radius server configuration.";

    container radius {
      description
       "Radius module";

      container resolve-configs {
        description
          "Radius resolve configs";

        leaf user-flow-control {
          description
            "Radius user-flow-control class";
          type enumeration {
            enum format-32-bytes {
              description
                "The format of the speed limit in the configuration class attribute is 32 bytes";
            }
            enum format-16-bytes {
              description
                "The format of the speed limit in the configuration class attribute is 16 bytes";
            }
            enum unit-bit/s {
              description
                "The format of the speed limit in the configuration class attribute is bit/s";
            }
            enum unit-byte/s {
              description
                "The format of the speed limit in the configuration class attribute is byte/s";
            }
          }
        }

        container vendor-specific {
          description
            "Radius vendor specific ability settings";

          leaf extend {
            description
              "Radius vendor specific extend";
            type boolean;
          }
        }
      }

      container attribute-configs {
        description
          "Radius attribute configs";

        container authentication {
          description
            "Radius authentication attributes";

          list attribute {
            description
              "Radius authentication package attributes";
            key "id";
            leaf id {
              description
                "Attribute id";
              type uint32 {
                range "1..255";
              }
            }
          }
        }

        container account {
          description
            "Account attribute";

          list attribute {
            description
              "Radius accounting package attributes";
            key "id";
            leaf id {
              description
                "Attribute id";
              type uint32 {
                range "1..255";
              }
            }
          }
        }

        leaf nas-port-id-format {
          description
            "Radius nas port id format";
          type enumeration {
            enum mode1 {
              description
                "mode 1";
            }
            enum normal {
              description
                "normal";
            }
            enum port-vid {
              description
                "port-vid";
            }
            enum qinq {
              description
                "qinq";
            }
          }
        }

        container mac-31-format {
          description
            "Radius server mac format attribution";

          choice mac-formats {
            case hyphen {
              leaf hyphen {
                description
                  "Format ex: 00d0-4096-3e4a";
                type empty;
              }
            }
            case ietf {
              leaf ietf {
                description
                  "Format ex: 00-D0-40-96-3E-4A";
                type empty;
              }
            }
            case normal {
              leaf normal {
                description
                  "Format ex: 00d0.4096.3e4a";
                type empty;
              }
            }
            case unformatted {
              leaf unformatted {
                description
                  "Format ex: 00d040963e4a";
                type empty;
              }
            }
            case customize {
              container customize {
                description
                  "Customize format";
                leaf split {
                  type enumeration {
                    enum colon-split {
                      description
                        "Use ':' as separator";
                    }
                    enum dot-split {
                      description
                        "Use '.' as separator";
                    }
                    enum hyphen-split {
                      description
                        "Use '-' as separator";
                    }
                  }
                  mandatory true;
                }
                leaf mode {
                  type enumeration {
                    enum mode1 {
                      description
                        "4 characters in a group";
                    }
                    enum mode2 {
                      description
                        "2 characters in a group";
                    }
                  }
                  mandatory true;
                }
                leaf case {
                  type enumeration {
                    enum lowercase {
                      description
                        "Use lowercase letters";
                    }
                    enum uppercase {
                      description
                        "Use uppercase letters";
                    }
                  }
                  default lowercase;
                }
              }
            }
            default unformatted;
          }
        }
      }

      container options {
        description
          "Radius options";

        leaf support-cui {
          description
            "Support cmcc-dot1x flag";
          type boolean;
        }

        leaf user-name-compatible {
          description
            "Radius user-name compatible";
          type boolean;
        }

        leaf deadtime {
          description
            "Radius server deadtime (0 for always active)";
          type uint32 {
            range "0..1440";
          }
        }

        container dead-criteria {
          description
            "Radius server dead-criteria";
          leaf time {
            description
              "Minutes before dead";
            type uint32;
          }
          leaf tries {
            description
              "Max tries before dead";
            type uint32;
          }
        }

        leaf source-port {
          description
            "Radius send port";
          type ntos-inet-types:port-number;
        }

        leaf source-interface {
          description
            "Radius send src-ifx";
          type ntos-types:ifname;
        }

        leaf nas-ip-address {
          description
            "Radius global nas-ip";
          type ntos-inet-types:ipv4-address;
        }

        container shared-key {
          description
            "Radius global key";
          choice key-type {
            description
              "Key-type config";
            case un {
              leaf unencrypted {
                description
                  "The UNENCRYPTED (clear text) shared key";
                type string;
              }
            }
            case en {
              leaf encrypted {
                description
                  "Hidden key";
                type string;
              }
            }
          }
        }
      }

      list server-group {
        description
          "Radius server group";
        key name;
        max-elements 50;

        leaf enabled {
          description
            "Radius server group switch";

          type boolean;
          default true;
        }

        leaf name {
          description
            "Radius group name, group radius is reserved.";
          type ntos-types:ntos-obj-name-type {
            pattern 'radius|tacacs\+' {
	            modifier invert-match;
	            error-message "group radius/tacacs+ is reserved";
	            description "group radius/tacacs+ is reserved.";
            }
          }
        }

        leaf test-username {
          description
            "Radius test username";

          type ntos-types:ntos-obj-name-type;
        }

        list server {
          key "ip port type";
          description
            "Radius server";
          uses server-common;
          ordered-by user;
          leaf update-flag {
            description
              "True when server list need update";
            type boolean;
          }

          ntos-ext:nc-cli-one-liner;
        }
      }

      list host {
        description
          "Radius server list";

        key "ip port type";
        uses server-common;
        max-elements 200;

        container key {
          description
            "Server's private key config";
          choice key-type {
            description
              "key-type config";
            case un {
              description
                "UNENCRYPTED key";
              leaf unencrypted {
                description
                  "The UNENCRYPTED (clear text) shared key";
                type string;
              }
            }
            case en {
              description
                "ENCRYPTED key";
              leaf encrypted {
                type string;
              }
            }
          }
        }

        leaf source-interface {
          description
            "Radius server send src-ifx";
          type ntos-types:ifname;
        }

        leaf retries {
          description
            "Radius server retries setting";
          type uint32 {
            range "1..5";
          }
          default "3";
        }

        leaf timeout {
          description
            "Radius server timeout setting";
          type uint32 {
            range "1..10";
          }
          default "5";
        }

        leaf data-flow-fmt {
          description
            "Radius server data flow format";
          type enumeration {
            enum byte;
            enum giga-byte;
            enum kilo-byte;
            enum mega-byte;
          }
          default "byte";
        }
      }
    }
  }

  rpc radius-show {
    input {
      leaf statistics {
        type empty;
      }

      leaf server {
        type empty;
      }

      leaf group {
        type empty;
      }

      container group-json {
        leaf start {
          type uint32;
        }
        leaf end {
          type uint32;
        }
        leaf filter {
          type string;
        }
        leaf name {
          type string;
        }
      }

      leaf attribute {
        type empty;
      }

      leaf param {
        type empty;
      }

      leaf something {
        type empty;
      }

      leaf vsa {
        type empty;
      }

      leaf occupied {
        type empty;
      }
    }

    output {
      leaf buffer{
        type string;
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "radius";
  }
}