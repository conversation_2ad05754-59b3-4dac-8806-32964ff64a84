module ntos-sslvpn {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:sslvpn";
  prefix ntos-sslvpn;

  import ntos {
    prefix ntos;
  }

  import ntos-commands {
    prefix ntos-cmd;
  }

  import ntos-types {
    prefix ntos-types;
  }

  import ntos-inet-types {
    prefix ntos-inet;
  }

  import ntos-extensions {
    prefix ntos-ext;
  }

  organization
  "Ruijie Networks Co.,Ltd";
  contact
  "Tel:4008-111-000
  E-mail:<EMAIL>";
  description
  "Ruijie NTOS sslvpn gateway global configure module.";

  revision 2024-10-14 {
    description
      "Support otp configuration";
  }

  revision 2023-12-18 {
    description
      "Sslvpn support short message functions.";
  }

  revision 2023-06-14 {
    description
      "Add sslvpn gateway update domain res command.";
  }

  revision 2023-05-25 {
    description
      "Add sslvpn gateway diag information command.";
  }

  revision 2023-04-15 {
    description
      "add port range limits; add rpc res check; add rpc iface address query.";
  }

  revision 2023-03-28 {
    description
      "online session bytes use uint64; authorize policy add desc filed";
  }

  revision 2023-03-27 {
    description
      "Support ippool net prefix address format.";
  }

  revision 2023-02-25 {
    description
      "Add license numbuer query.";
  }

  revision 2023-02-08 {
    description
      "Show rpc add page filter field.
      Only support filter one gateway, and change rpc output data.";
  }

  revision 2023-02-02 {
    description
      "Add client policy configure.";
  }
  revision 2022-12-16 {
    description
      "Add auto approve public terminal hardid configure.";
  }
  revision 2022-10-12 {
    description "Initial reversion";
  }

  identity sslvpn {
    base ntos-types:SERVICE_LOG_ID;
    description
      "SSLVPN service.";
  }

  typedef hardid-show-type {
    type enumeration {
      enum approved {
        description
          "Show approved harids.";
      }

      enum all {
        description
          "Show all hardids.";
      }

      enum unapproved {
        description
          "Show unapproved hardids.";
      }
    }
  }

  typedef tunnel-mode-type {
    type enumeration {
      enum split {
        description 
          "The route mode is split mode.";
      }
      enum full {
        description 
          "The route mode is full mode.";  
      }
    }
  }

  typedef ipv4-address-type {
    type union {
      type ntos-inet:ipv4-prefix;
      type ntos-inet:ipv4-range;
      type ntos-inet:ipv4-mask;
    }
  }

  typedef mask-type {
    type union {
      type string {
        pattern '(([0-9])|([1-2][0-9])|(3[0-2]))' {
          error-message "Invalid mask address or prefix.";
        }
      }
      type ntos-inet:ipv4-address;
    }
    description
      "An mask prefix: address or mask prefix.";
  }

  grouping short-message-config {
    container short-message {
      description "Short message auth configures.";
      leaf enabled {
        type boolean;
        description
          "Enabled short message auth.";
          default "false";
      }

      leaf sms-server {
        type ntos-types:ntos-obj-name-type;
        ntos-ext:nc-cli-completion-xpath
          "/ntos:config/ntos:vrf/ntos-sms-srv:sms-server/*[local-name()='server']/*[local-name()='name']";
        description
            "Choise short message server.";
      }
      leaf smsg-everyday-peruser-max {
        description
          "The limit of peruser max short-messages everyday, use 0 if not limit.";
        type uint32 {
          range "0..65535";
        }
        default "0";
      }

      leaf smsg-extend-functions {
        type bits {
          bit smsg-bind-hardid {
              description 
                  "Allow user to bind hardid by smsg auth.";
          }
          bit smsg-unbind-hardid {
              description
                  "Allow user to unbind hardid by smsg auth.";
          }
          bit smsg-submit-phone {
              description
                  "Allow user to submit phonenumber by smsg if user telephone not import.";
          }
        }
        description
          "Smsg extend function, like bind/unbind hardid, submit-phone..";
        default "";
      }
      container smsg-code-policy {
        leaf validate-times {
            type uint32 {
              range "1..60";
            }
            default "5";
            ntos-ext:nc-cli-no-name;
            description
              "Smsg verify code validate time, uint minutes.";
        }
        leaf max-retries {
            type uint32 {
              range "1..60";
            }
            default "5";
            ntos-ext:nc-cli-no-name;
            description
              "Smsg verify code max try times.";
        }
        ntos-ext:nc-cli-one-liner;
      }
    }
  }

  grouping otp-auth-config {
    container otp-auth {
      description "otp auth configures.";
      container otp-auth-policy {
        leaf max-retries {
            type uint32 {
              range "1..60";
            }
            default "5";
            ntos-ext:nc-cli-no-name;
            description
              "otp verify code max try times.";
        }
        ntos-ext:nc-cli-one-liner;
      }
    }
  }

  // 2024.11.6 [NTOS1.R10P5] add support IPv6
  grouping ip-type-config {
    leaf ip-type {
      type enumeration {
        enum ipv4 {
          description
            "The ip type is IPv4.";
        }
        enum ipv6 {
          description
            "The ip type is IPv6.";
        }
        enum both {
          description
            "The ip type is both IPv4 and IPv6.";
        }
      }
    }
  }

  grouping gateway-config {
    description
      "Detail about sslvpn gateway global configures.";
    leaf name {
      type ntos-types:ntos-obj-name-type;
      description
        "The name of sslvpn gateway.";
    }

    /* basic configuration */
    leaf type {
      mandatory true;
      type enumeration {
        enum exclusive {
          description
            "The  exclusive gateway type which ip is only used by one domain.";
        }
        enum sharing {
          description
            "The sharing gateway type which ip can be used by many domains";
        }
      }
      description
        "Support gateway type : exclusive or sharing.";
    }

    container address {
      description
        "The gateway adress, include interface ip port. the adress num range is {1..3}";
      list interface {
        key "name";
        description
          "Interface configuration.";
        leaf name {
          type ntos-types:ifname;
          ntos-ext:nc-cli-no-name;
          ntos-ext:nc-cli-completion-xpath
            "/ntos:config/ntos:vrf/ntos-interface:interface/*/*[local-name()='name']";
        }

        leaf port {
          type uint16 {
              range "1..65535" {
                error-message
                  "The port must more than 1 and less than 65535.";
              }
            }
          default 8443;
          description
            "The name of sslvpn gateway port.";
        }

        // 2024.11.6 [NTOS1.R10P5] add support IPv6
        uses ip-type-config;

        max-elements 3;
        ntos-ext:nc-cli-one-liner;
      }

      list mannual-ip {
        key "ipv4";
        description
          "Mannual-ip configuration.";
        leaf ipv4 {
          type ntos-inet:ipv4-address;
          ntos-ext:nc-cli-no-name;
          description
            "The name of sslvpn gateway ip.";
        }

        leaf port {
          type uint16 {
              range "1..65535" {
                error-message
                  "The port must more than 1 and less than 65535.";
              }
            }
          default 8443;
          description
            "The name of sslvpn gateway port.";
        }
        max-elements 3;
        ntos-ext:nc-cli-one-liner;
      }

      // 2024.11.6 [NTOS1.R10P5] add support IPv6
      list mannual-ip6 {
        key "ipv6";
        description
          "Mannual-ip configuration.";
        leaf ipv6 {
          type ntos-inet:ipv6-address;
          ntos-ext:nc-cli-no-name;
          description
            "The name of sslvpn gateway IPv6.";
        }

        leaf port {
          type uint16 {
              range "1..65535" {
                error-message
                  "The port must more than 1 and less than 65535.";
              }
            }
          default 8443;
          description
            "The name of sslvpn gateway port.";
        }
        max-elements 3;
        ntos-ext:nc-cli-one-liner;
      }
    }

    leaf domain {
      type ntos-inet:domain-name;
      description
        "The domain name of sslvpn gateway.";
    }

    uses short-message-config;
    uses otp-auth-config;

    container two-factor-auth {
      description
        "two-factor-auth configuation";

      leaf method {
        type enumeration {
          enum none {
            description
              "diable two factor auth";
          }
          enum short-message {
            description
              "enable sms two factor auth";
          }
          enum otp-auth {
            description
              "enable otp two factor auth";
          }
        }
        default "none";
        description
          "two factor auth method, include none, short-message, otp-auth";
      }
    }

    leaf-list dns {
      type ntos-inet:ipv4-address;
      status deprecated {
        ntos-ext:status-obsoleted-release "2023-05-31";
        ntos-ext:status-deprecated-revision "2023-05-24";
        ntos-ext:status-description
          "Dns leaf-list is not used anymore. Instead, name-server list
           is used by old version dns configuation.";
        ntos-ext:status-replacement "/ntos:config/ntos:vrf/ntos-sslvpn:sslvpn/ntos-gateway:gateway/ntos-gateway:name-server";
      }
      description
        "The name of sslvpn gateway dns.the dns num range is {0..3}";

      max-elements 3;
    }

    list name-server {
      key "seq";
      description
        "Dns info [seq value 1].";

      leaf seq {
        type uint32 {
            range "1..3" {
              error-message
                "The priority must in 1~3.";
            }
          }
          description
            "Dns seq. Example:[seq value][1 *******].";
      }

      leaf value {
        type ntos-inet:ipv4-address;
        description
          "Dns Address max = 3.";
        ntos-ext:nc-cli-no-name;
      }
      max-elements 3;
    }

    leaf dns-order {
      type enumeration {
        enum client-first {
          description
            "The  dns first type is client first.";
        }
        enum gateway-first {
          description
            "The  dns first type is gateway first.";
        }
      }
      default gateway-first;
      description
        "The type of sslvpn gateway dns first, include client-first and dev-first.";
    }

    /* advanced configuration */
    leaf-list protocol {
      type enumeration {
        enum tls1.0 {
          description
            "The tls protocol is tls1.0.";
        }
        enum tls1.1 {
          description
            "The tls protocol is tls1.1.";
        }
        enum tls1.2 {
          description
            "The tls protocol is tls1.2.";
        }
      }
      default "tls1.2";
      description
        "The name of ssl supported protocols, include tls1.0, tls1.1, tls1.2.";
    }

    leaf-list ciphersuit {
        type enumeration {
          enum tls-ecdhe-rsa-with-aes128-cbc-sha256 {
            description 
              "The rego cipher suit is tls-ecdhe-rsa-with-aes128-cbc-sha256.";
          }
          enum tls-ecdhe-rsa-with-aes128-cbc-sha {
            description 
              "The rego cipher suit is tls-ecdhe-rsa-with-aes128-cbc-sha.";
          }
          enum tls-ecdhe-rsa-with-aes256-cbc-sha384 {
            description 
              "The rego cipher suit is tls-ecdhe-rsa-with-aes256-cbc-sha384.";  
          }
          enum tls-rsa-with-aes256-cbc-sha {
            description 
              "The rego cipher suit is tls-rsa-with-aes256-cbc-sha.";  
          }
        }
      description
        "The rego cipher suit of iptunnel and https.";
    }

    leaf local-cert {
      type ntos-types:cert-name;
      default 'default';
      description
        "The name of ssl local certs."; 
    }

    leaf max-concur-users {
      type uint32 {
          range "0..5000" {
            error-message
              "The priority must less then 5000.";
          }
        }
        default 0;
        description
          "The total limits of max concurrent user numbers. 
          0: Indicates that consumes all remaining resources. Example:'0..5000'.";
    }

    /* login-auth */
    leaf auth-zone {
      mandatory true;
      type string;
      description
      "The name of sslvpn gateway authorization zone.";
    }

    container login-limit {
      container by-user {
        description "The explosion proof of user lock about gateway.";
        leaf enabled {
          type boolean;
          default "true";
          description
            "Enable or disable login user limit options.";
        }
        leaf user-max-retries {
          type uint32 {
              range "1..65535" {
                error-message
                  "The user-max-retries must >= 1 and <= 65535.";
              }
          }
          default 5;
          description
            "The max number of consecutive login failures by user. Example:'1..65535'.";
        }
        leaf user-lock-secs {
          type uint32 {
              range "1..10080" {
                error-message
                  "The user-lock-secs must >= 1 and <= 10080.";
              }
          }
          default 5;
          description
            "The locked seconds of users which reached the max number of consecutive login failures.Example:'1..10080'.";
        }   
        ntos-ext:nc-cli-one-liner;
      }
      container by-ip {
        description
          "The explosion proof of ip lock about gateway.";

        leaf enabled {
          type boolean;
          default "true";
          description
            "Enable or disenable login ip limit options.";
        }

        leaf ip-max-retries {
          type uint32 {
              range "1..65535" {
                error-message
                  "The ip-max-retries must >= 1 and <= 65535.";
              }
            }
          default 5;
          description
            "The max number of consecutive login failures by ip. Example:'1..65535'.";
        }

        leaf ip-lock-secs {
          type uint32 {
              range "1..10080" {
                error-message
                  "The ip-lock-secs must >= 1 and <= 10080.";
              }
          }
          default 5;
          description
            "The locked seconds of ips which reached the max number of consecutive login failures. Example:'1..10080'.";
        }
        ntos-ext:nc-cli-one-liner;
      }
    }

    container client-policy {
      description
        "The sslvpn client access policy control.";
      leaf policy {
        type enumeration {
          enum any {
            description "Allow all client access.";
          }
          enum newest {
            description "Use sec-cloud newest client version.";
          }
          enum custom {
            description "Custom access sslvpn client version.";
          }
        }
        default "any";
        description
          "The client access policy.";
      }
      list custom-version {
        key platform;
        leaf platform {
          type enumeration {
            enum "Windows64";
            enum "Windows32";
            enum "Android";
            enum "iOS";
            enum "MacOS";
            enum "Linux64";
            enum "Linux32";
            enum "DebianX86_64";
            enum "UosX86_64";
            enum "UosARM64";
            enum "KylinX86_64";
            enum "KylinARM64";
          }
          description
            "The client os platform.";
        }
        leaf version {
          type string {
            length "0..9";
          }
          description
            "The client specital version.";
          default "any";
        }
      }
    }

    leaf session-timeout {
      type uint32 {
          range "1..1440" {
            error-message
              "The login session timeout must >= 1 and <= 1440 minutes.";
          }
      }

      default 30;
      description
        "The login session timeout must minutes.Example:'1..1440'.";
    }

    leaf soft-keyboard-enabled {
      type boolean;
      default "false";
      description
        "Enable or disable soft keyboard.";
    }

    container img-verify {
      description
        "The web config about image verify.";
      leaf enabled {
        type boolean;
        default "false";
        description
          "Enable or disable img verify";
      }
      leaf sess-retries {
        type uint32 {
            range "0..1024" {
              error-message
                "The sess img verify retry must >= 1 and <= 1024.";
            }
        }
        description
          "The limit of max retry times before enable image verify. Example:'1..1024'.";
      }
      ntos-ext:nc-cli-one-liner;
    }

    container hardid-verify {
      description
        "Details about hardid verify infomations";
      leaf enabled {
        type boolean;
        default "false";
        description
          "Enable or disable hardid verify.";
      }

      leaf max-num {
        type uint32 {
            range "3..64" {
              error-message
                "The max hardid num must >= 3 and <= 64.";
            }
          }

        default 3;
        description
          "The collect max hardid numbers per user. Example:'3..64'.";
      }

      leaf auto-approval {
        type boolean;
        default "true";
        description
          "Enable or disable hardid auto approval.";
      }

          leaf auto-approve-pub-term {
            type boolean;
                default "false";
                description
                  "Enable or disable hardid auto approve public terminal.";
          }
          
          leaf hardid-self-unbind {
            type boolean;
                default "false";
                description
                  "Enable of disable hardid self unbind.";
          }
    }

    /* iptun resources */
    container iptunnel {
      description
        "Details about iptunnel access configure infomation."; 
      leaf timeout-idle {
        type uint32 {
            range "1..1440" {
              error-message
                "The iptun timeout idle must >= 1 and <= 1440 minutes.";
            }
        }
        default 30;
        description
          "The time of iptunnel resource timeout idle minutes.Example:'1..1440'.";
      }

      leaf client-wins {
        // 2024.11.6 [NTOS1.R10P5] add support IPv6 [function not open]
        type ntos-inet:ip-address;
        description
          "The client wins configure of iptunnel access.";
      }

      container keep-alive {
        description
          "The description of keepalive about interval and hold-time.";
        leaf interval {
          type uint32 {
              range "10..60" {
                error-message
                  "The iptun timeout idle must >= 10 and <= 60 seconds.";
              }
          }
          default 30;
          description
            "The configuration of heartbeat interval [seconds].Example:'10..60'.";
        }

        leaf dead-time {
          type uint32 {
              range "30..300" {
                error-message
                  "The iptun timeout idle must >= 30 and <= 300 seconds.";
              }
          }
          default 180;
          description
            "The configuration of sslvpn connect dead time [seconds].Example:'30..300'.";
        }
        ntos-ext:nc-cli-one-liner;
      }

      leaf route-mode {
        type tunnel-mode-type;
        default "split";
        description
          "The route mode about client configuration.";
      }

      leaf sslvpn-line {
        type boolean;
        default "false";
        description
          "The iptunnel special line mode. default: false";
      }

      container ip-pool {
        description
          "The description about ip pools.";

        list net {
          key "net";
          description
            "The description about ip pools.";
          leaf net {
            type ipv4-address-type;
            description
              "The ip/mask format of ip pools.";
            ntos-ext:nc-cli-no-name;
          }
          ntos-ext:nc-cli-one-liner;
          max-elements 16;
        }

        list range {
          key "start-ip end-ip";
          description
            "IPv4 range.";

          leaf start-ip {
            type ntos-inet:ipv4-address;
            description
              "Starting IPv4 Address of a range.";
          }

          leaf end-ip {
            type ntos-inet:ipv4-address;
            description
              "Last IPv4 Address of a range.";
          }

          leaf mask {
            type mask-type;
            description
              "The mask of ip pools' net range.";
          }

          ntos-ext:nc-cli-one-liner;
          max-elements 16;
        }
      }
    }

    container resource {
      description
        "The resource of sslvpn gateway.";
      list iptun-resgrp {
        description
          "Add a iptunnel resource group for sslvpn";
        key "name";
        leaf name {
          type ntos-types:ntos-obj-name-type;
          description "The name of iptunnel resource group.";
          ntos-ext:nc-cli-no-name;
        }

        leaf description {
          type ntos-types:ntos-obj-description-type;
          description
            "The description about iptun resource group.";
        }

        list uri {
          key "name";
          leaf name {
            type ntos-types:ntos-obj-name-type;
            description "The name of iptunnel uri resource name.";
            ntos-ext:nc-cli-no-name;
          }

          leaf value {
            type string {
              length "3..128";
              ntos-ext:nc-cli-shortdesc "<[scheme]://host[:port]>";
            }
            description "The value of uri resource. 
                         for example: https://test.ruijie.com:8888";
          }

          ntos-ext:nc-cli-one-liner;
          max-elements 16;
        }

        list net {
          key "name";
          leaf name {
            type ntos-types:ntos-obj-name-type;
            description "The name of iptunnel net resource name.";
            ntos-ext:nc-cli-no-name;
          }

          leaf value {
            type union {
              type ntos-inet:ipv4-mask;
              type ntos-inet:ipv4-prefix;
            }
            description  
              "THe net address.";
          }

          uses service;
          description "The name of iptunnel net resource.";
          max-elements 16;
          ntos-ext:nc-cli-one-liner;
        }

        list host {
          key "name";
          leaf name {
            type ntos-types:ntos-obj-name-type;
            ntos-ext:nc-cli-no-name;
            description "The name of iptunnel host resource name.";
          }

          leaf value {
            type ntos-inet:ipv4-address;
            description  
              "THe host address. for example: *******";
          }
          uses service;
          description "The name of iptunnel host resource.";
          max-elements 16;
          ntos-ext:nc-cli-one-liner;
        }

        list domain {
          key "name";
          leaf name {
            type ntos-types:ntos-obj-name-type;
            ntos-ext:nc-cli-no-name;
            description "The name of iptunnel domain resource name.";
          }
          leaf value {
            type ntos-inet:domain-name;
            description  
              "THe domain address. for example: www.test.com.cn";
          }
          uses service;

          description "The name of iptunnel domain resource.";
          max-elements 16;
          ntos-ext:nc-cli-one-liner;
        }
      }
    }

    list authorize-policy {
      description "The gateway authorize resoure module.";
      key "name";
      leaf name {
        type ntos-types:ntos-obj-name-type;
        description
            "The authorize policy name.";
        ntos-ext:nc-cli-no-name;
      }

      leaf description {
        type string {
          length "0..255";
        }
        description
            "The authorize policy description.";
      }

      list user {
        key "name";
        leaf name {
          type string;
          description
            "The user name.";
          ntos-ext:nc-cli-no-name;
        }
        ntos-ext:nc-cli-one-liner;
      }

      list group {
        key "name";
        leaf name {
          type string;
          description
            "The user group name.";
          ntos-ext:nc-cli-no-name;
        }
        ntos-ext:nc-cli-one-liner;
      }

      leaf-list iptun-resgrp {
        type ntos-types:ntos-obj-name-type;
        max-elements 128;
        description
          "iptun res name of auth policy.";
        ntos-ext:nc-cli-completion-xpath 
          "../resource/iptun-resgrp/*[local-name()='name']";
      }
    }

    leaf enabled {
      type boolean;
      description
        "Enable or disable iptun service of sslvpn gateway.";
    }
  }

  grouping service {
    leaf proto {
      type enumeration {
        enum any {
          description
            "Support any protos.";
        }

        enum tcp {
          description
            "Support tcp protos.";
        }

        enum udp {
          description
            "Support udp protos.";
        }
      }
      description
        "The communication proto of iptunnel.";
    }

    leaf start-port {
      type ntos-inet:port-number {
        range "0..65535" {
          error-message
            "The start port must >= 0 and <= 65535.";
        }
      }
      description
        "Start port address.";
    }

    leaf end-port {
      type ntos-inet:port-number;
      must "current() >= ../start-port" {
        error-message "The End value must be larger than the start value.";
      }
      description
        "End port address.";
    }
  }

  augment "/ntos:config/ntos:vrf" {
    description "Top-Level grouping for policy based gateway configuration.";
    
    container sslvpn {
      description
        "Configuration for sslvpn gateway. [sslvpn gateway <name>]";
      
      list gateway {
        description
          "Configuration for sslvpn gateway.";
        key "name";
        uses gateway-config;
        max-elements 20;
      }
    }
  }

  augment "/ntos:state/ntos:vrf" {
    description "Top-Level grouping for policy based gateway configuration.";
    
    container sslvpn {
      description
        "Configuration for sslvpn gateway. [sslvpn gateway <name>]";
      
      list gateway {
        description
          "Configuration for sslvpn gateway.";
        key "name";
        uses gateway-config;
      }
    }
  }

  rpc hardid-export {
    description
      "Export hardid to the specified location.";
    input {
      leaf vrf {
        mandatory true;
        type ntos:vrf-name;
        description
          "The name of vrf which will export hardids.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf gateway{
        type ntos-types:ntos-obj-name-type;
        description
          "The name of gateway which will export hardids.";
      }

      leaf filename{
        type string;
        description
          "The name which will export hardids.";
      }
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-ext:nc-cli-cmd "sslvpn hardid export";
    ntos-ext:nc-cli-command-no-pager;
  }

  rpc hardid-import {
    description
      "Import hardid from the specified location.";
    input {
      leaf vrf {
        mandatory true;
        type ntos:vrf-name;
        description
          "The name of vrf which will import hardids.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }
      
      leaf gateway{
        type ntos-types:ntos-obj-name-type;
        description
          "The name of gateway which will export hardids.";
      }

      leaf filename{
        type string;
        description
          "The name which will import hardids.";
      }
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }

    ntos-ext:nc-cli-cmd " sslvpn hardid import";
    ntos-ext:nc-cli-command-no-pager;
  }

  rpc hardid-delete {
    description
      "The rpc to delete a user's hardids.";
    input {
      leaf vrf {
        mandatory true;
        type ntos:vrf-name;
        description
          "The name of vrf which will delete hardids.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf gateway{
        type ntos-types:ntos-obj-name-type;
        description
          "The name of gateway which will export hardids.";
      }
      list hardids {
        key "username hardid";
        leaf username {
          type string;
          description
            "The user's name.";
        }

        leaf hardid {
          type string;
          description
            "The hardid content. Use it when deleting one of per user's hardids.";
        }
      }
    }

    ntos-ext:nc-cli-cmd "sslvpn hardid delete";
    ntos-ext:nc-cli-command-no-pager;
  }

  rpc hardid-approve {
    description
      "The rpc to approve a user's hardids.";

    input {
      leaf vrf {
        mandatory true;
        type ntos:vrf-name;
        description
          "The name of vrf which will approve hardids.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf gateway{
        type ntos-types:ntos-obj-name-type;
        description
          "The name of gateway which will export hardids.";
      }
      list hardids {
        key "username hardid";
        leaf username {
          type string;
          description
            "The user's name.";
        }
        leaf hardid {
          type string;
          description
            "The hardid content. Use it when approving one of per user's hardids.";
        }
      }
    }

    ntos-ext:nc-cli-cmd "sslvpn hardid approve";
    ntos-ext:nc-cli-command-no-pager;
  }
  
  rpc hardid-show {
    description
      "The rpc to approve a user's hardids.";

    input {
      leaf vrf {
        mandatory true;
        type ntos:vrf-name;
        description
          "The name of vrf which will show hardids.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf gateway {
        type ntos-types:ntos-obj-name-type;
        description "Filter by gateway name.";
      }

      leaf username {
        type string;
        description "Filter by username.";
      }

      leaf group {
        type string;
        description "Filter by usergroup.";
      }

      leaf flag {
        type hardid-show-type;
        description "Filter hardid type.";
        default all;
      }

      leaf start {
        type uint32;
        description "Show hardid, start with the offset.";
      }

      leaf end {
        type uint32;
        description "Show hardid, end with the offset.";
      }
    }
    output {
      leaf total {
        type int32;
        description "Total number of hardid.";
      }
      list detail {
        key "gateway username hid";
        leaf gateway {
          type string;
          description "The name of gateway.";
        }
        leaf hid {
          type string;
          description "The hardid information.";
        }

        leaf username {
          type string;
          description "The hardid belond to user.";
        }

        leaf group {
          type string;
          description "The hardid belond to user group.";
        }

        leaf description {
          type string;
          description "Description of hardid entry.";
        }

        leaf approved {
          type boolean;
          description "Status of hardid, true/false(approved/unapproved)";
        } 
      }
    }
    ntos-ext:nc-cli-show "sslvpn hardid";
    ntos-ext:nc-cli-command-no-pager;
  }

  rpc online-session {
    description 
      "show sslvpn gateway online session info.";
    input {
      leaf vrf {
        mandatory true;
        type ntos:vrf-name;
        description
          "The name of vrf which will show online session info.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf gateway {
        type ntos-types:ntos-obj-name-type;
        description "Filter by gateway name.";
      }

      leaf username {
        type string;
        description "Filter by username.";
      }

      leaf start {
        type uint32;
        description "Show online user, start with the offset.";
      }

      leaf end {
        type uint32;
        description "Show online user, end with the offset.";
      }
    }
    output {
      leaf total {
        type uint32;
        description "Total number of online user.";
      }

      list detail {
        key "gateway username";
        leaf gateway {
          type ntos-types:ntos-obj-name-type;
          description "The gateway name.";
        }

        leaf username {
          type string;
          description "The name of online user.";
        }

        leaf access-time {
          type string;
          description "The user online time.";
        }

        leaf access-info {
          type string;
          description "User access gateway ip and port info.";
        }

        leaf access-ip {
          type string;
          description "User access use ip.";
        }

        leaf virtual-ip {
          type string;
          description "Assign to user's virtual ip.";
        }

        leaf online-time {
          type int64;
          description "User online time.";
        }

        leaf up-rate {
          type uint64;
          description "User up rates(Bytes).";
        }

        leaf down-rate {
          type uint64;
          description "User down rates(Bytes).";
        }

        leaf terminal {
          type string;
          description "User login terminal type.";
        }
        leaf idle-timeout {
          type int32;
          description "User remain idle timeout(second).";
        }
      }
    }
    ntos-ext:nc-cli-show "sslvpn online-session";
    ntos-ext:nc-cli-command-no-pager;
  } 
  
  rpc offline-user {
    description
      "The rpc to delete a user's hardids.";
    input {
      leaf vrf {
        mandatory true;
        type ntos:vrf-name;
        description
          "The name of vrf which will show offline user info.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf gateway{
        type ntos-types:ntos-obj-name-type;
        description
          "The name of gateway.";
      }

      leaf-list username {
        type string;
        description
          "The user's name.";
      }
    }

    ntos-ext:nc-cli-cmd "sslvpn offline-user";
    ntos-ext:nc-cli-command-no-pager;
  }

  rpc lock-user-info {
    description 
      "Locked sslvpn gateway lock user info.";
    input {
      leaf vrf {
        mandatory true;
        type ntos:vrf-name;
        description
          "The name of vrf which will show online session info.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }
      leaf gateway {
        description "The filter gateway name.";
        type ntos-types:ntos-obj-name-type;
      }
      leaf username {
        type string;
        description "Filter locked user by username.";
      }

      leaf start {
        type uint32;
        description "Show locked user, start with the offset.";
      }
      leaf end {
        type uint32;
        description "Show locked user, end with the offset.";
      }
    }
    
    output {
      leaf total {
        type int32;
        description "Total number of locked user.";
      }

      list detail {
        key "gateway username";
        leaf gateway {
          type ntos-types:ntos-obj-name-type;
          description "The name of gateway.";
        }

        leaf username {
          type string;
          description "The name of locked user.";
        }

        leaf locked-time {
          type string;
          description "User locked time.";
        }

        leaf access-ip {
          type ntos-inet:ip-address;
          description "User locked access ip address.";
        }

        leaf unlock-time {
          type int32;
          description "Remain time to auto unlock.";
        }
      }
    }
    ntos-ext:nc-cli-show "sslvpn locked-user";
    ntos-ext:nc-cli-command-no-pager;
  }
  
  rpc lock-ip-info {
    description 
      "Locked sslvpn gateway lock ip info.";
    input {
      leaf vrf {
        mandatory true;
        type ntos:vrf-name;
        description
          "The name of vrf which will show online session info.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf gateway {
        type ntos-types:ntos-obj-name-type;
        description "Filter gateway name.";
      }

      leaf ip {
        type ntos-inet:ip-address;
        description "Filter locked by ip address.";
      }

      leaf start {
        type uint32;
        description
          "Show locked ip, start with the offset.";
      }

      leaf end {
        type uint32;
        description
          "Show locked ip, end with the offset.";
      }
    }
    
    output {
      leaf total {
        type uint32;
        description
          "Total number of locked ip.";
      }
      list detail {
        key "gateway ip";
        leaf ip {
          type ntos-inet:ip-address;
          description "Locked ip address.";
        }

        leaf gateway {
          type ntos-types:ntos-obj-name-type;
          description "The name of gateway.";
        }

        leaf locked-time {
          type string;
          description "The time of locked.";
        }

        leaf unlock-time {
          type int64;
          description "The time of auto unlock time.";
        }
      }
    }
    ntos-ext:nc-cli-show "sslvpn locked-ip";
    ntos-ext:nc-cli-command-no-pager;
  }

rpc unlock {
    description 
      "Unlocked sslvpn user or ip.";
    input {
      choice lock-type {
        container user {
          description
            "Unlocked sslvpn user.";

          leaf vrf {
            mandatory true;
            type ntos:vrf-name;
            description
              "The name of vrf.";
            ntos-ext:nc-cli-completion-xpath
              "/ntos:state/ntos:vrf/ntos:name";
          }
  
          leaf gateway {
            type ntos-types:ntos-obj-name-type;
            description "Choice gateway name.";
          }

          leaf-list username {
            type string;
            description "The name of user which unlocked.";
          }
        }
        container ip {
          description
            "Unlocked sslvpn ip.";

          leaf vrf {
            mandatory true;
            type ntos:vrf-name;
            description
              "The name of vrf.";
            ntos-ext:nc-cli-completion-xpath
              "/ntos:state/ntos:vrf/ntos:name";
          }

          leaf gateway {
            type ntos-types:ntos-obj-name-type;
            description "Choice gateway name.";
          }

          leaf-list ip {
            type ntos-inet:ip-address;
            description "The IP address detail.";
          }
        }
      }
    }
    ntos-ext:nc-cli-cmd "sslvpn unlock";
    ntos-ext:nc-cli-command-no-pager;
  }

  rpc get-remain-concurrent {
    description 
      "Get all gateway remain concurrent";
    output {
      leaf num {  
        type int32;
        description "Remain concurrent user number";
      }
    }
    ntos-ext:nc-cli-cmd "sslvpn get-remain-concurrent";
    ntos-ext:nc-cli-command-no-pager;
  }

  rpc domain-match-ip-verify {
    description 
      "Verify domain matched with ip all.";
    input {
      leaf vrf {
        mandatory true;
        type ntos:vrf-name;
        description
          "The name of vrf which will show online session info.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf domain {
        type ntos-inet:domain-name;
        mandatory true;
        description
          "The domain name of sslvpn gateway.";
      }

      list ipaddr {
        description "Check ipaddr list.";
        key "name";

        leaf name {
          // 2024.11.6 [NTOS1.R10P5] add support IPv6
          type ntos-inet:ip-address;
          description "The name of sslvpn gateway ipaddr.";
        }
      }
    }

    output {
      leaf ret {  
        type int32;
        description "Verify domain matched ipaddr result. success: True, failure: False";
      }
      leaf errstr {  
        type string;
        description "Verify domain matched ipaddr errstr.";
      }
    }
    ntos-ext:nc-cli-cmd "sslvpn domain-match-ip-verify";
    ntos-ext:nc-cli-command-no-pager;
    ntos-ext:nc-cli-hidden;
  }

  rpc get-client-version {
    description
      "Get all firewall support client version.";
    output {
      list detail {
        key platform;
        leaf platform {
          description
            "The client os platform.";
          type string;
        }
        list version {
          key "ver";
          leaf ver {
            type string;
            description
              "The version of client.";
          }
          leaf desc {
            type string;
            description
              "This version client's description information.";
          }
        }
      }
    }
    ntos-ext:nc-cli-cmd "sslvpn get-client-version";
    ntos-ext:nc-cli-command-no-pager;
  }

  rpc get-license-info {
    description
      "Get device sslvpn license concurrent user number.";
    output {
      leaf result {
        description "The rpc exec result.";
        type int32;
      }
      container context {
        leaf free_num {
          description "The free sslvpn concurent user number.";
          type int32;
        }
        leaf license_num {
          description "User by the license number of sslvpn concurrent user number.";
          type int32;
        }
      }
    }
    ntos-ext:nc-cli-cmd "sslvpn get-license-info";
    ntos-ext:nc-cli-command-no-pager;
  }

  rpc domain-or-uri-res-resolved {
    description 
      "Domain or uri can be resolved.";
    input {
      leaf vrf {
        mandatory true;
        type ntos:vrf-name;
        description
          "The name of vrf which will check res whether be resolved.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf resource {
        type string;
        mandatory true;
        description
          "The name of sslvpn gateway resource group domain or uri resource.";
      }
    }

    output {
      leaf ret {  
        type boolean;
        description "The result of resolving domain or uri resource. success: True, failure: False";
      }
      leaf errstr {  
        type string;
        description "Resolved domain or uri res error string.";
      }
    }
    ntos-ext:nc-cli-cmd "sslvpn domain-or-uri-res-resolved";
    ntos-ext:nc-cli-command-no-pager;
    ntos-ext:nc-cli-hidden;
  }


  rpc resgrp-is-referenced {
    description 
      "Domain or uri can be resolved.";
    input {
      leaf vrf {
        mandatory true;
        type ntos:vrf-name;
        description
          "The name of vrf which will get resgrp reference info.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf gw-name {
        type string;
        mandatory true;
        description
          "The name of sslvpn gateway name.";
      }

      leaf resgrp-name {
        type string;
        mandatory true;
        description
          "The name of sslvpn gateway resource group name.";
      }
    }

    output {
      leaf ret {  
        type boolean;
        description "The result: True is referenced. False is unrferenced.";
      }
    }
    ntos-ext:nc-cli-cmd "sslvpn resgrp-is-referenced";
    ntos-ext:nc-cli-command-no-pager;
    ntos-ext:nc-cli-hidden;
  }

  rpc get-address-info-from-interface {
    description 
      "Get ipv4 address info from interface.";
    input {
      leaf vrf {
        mandatory true;
        type ntos:vrf-name;
        description
          "The name of vrf which will get gateway address info.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf-list name {
        type ntos-types:ifname;
        ntos-ext:nc-cli-completion-xpath
          "/ntos:config/ntos:vrf/ntos-interface:interface/*/*[local-name()='name']";
      }
    }

    output {
      list infaddr-list {
        key "iface-name";
        leaf iface-name {
          type ntos-types:ifname;
          description
            "The interface name.";
        }

        leaf iface-ipv4 {
          type ntos-inet:ipv4-address;
          description
            "The interface linked IPv4 address.";
        }

        // 2024.1.2 [NTOS1.0R10P5] add support IPv4 second address
        leaf-list iface-ipv4-second {
            type ntos-inet:ipv4-address;
            description "Additional IPv4 addresses for the interface.";
        }

        leaf iface-ipv6 {
          // 2024.11.6 [NTOS1.0R10] add support IPv6
          type ntos-inet:ipv6-address;
          description
            "The interface linked IPv6 address.";
        }
      }
    }
    ntos-ext:nc-cli-cmd "sslvpn get-address-info-from-interface";
    ntos-ext:nc-cli-command-no-pager;
    ntos-ext:nc-cli-hidden;
  }

  rpc get-gateway-info {
    description
      "Get sslvpn gateway infomations.";
    input {
      leaf vrf {
        mandatory true;
        type ntos:vrf-name;
        description
          "The name of vrf which will get gateway info.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf name {
        type ntos-types:ntos-obj-name-type;
        description "The gateway name.";
      }
    }

    output {
      leaf gateway-info {
        type string;
        description
          "The gateway infomations.";
      }
    }

    ntos-ext:nc-cli-cmd "sslvpn get-gateway-info";
    ntos-ext:nc-cli-command-no-pager;
    ntos-ext:nc-cli-hidden;
  }

  rpc update-domain-res {
    description
      "Manually one-click update sslvpn domain and uri resources.";
    input {
      leaf vrf {
        mandatory true;
        type ntos:vrf-name;
        description
          "The name of vrf which sslvpn will update domain and uri resources.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf name {
        type ntos-types:ntos-obj-name-type;
        description "The gateway name. current only support all gateway";
      }
    }

    output {
      leaf result {
        type string;
        description
          "The domain resource update result.";
      }
    }

    ntos-ext:nc-cli-cmd "sslvpn update-domain-res";
    ntos-ext:nc-cli-command-no-pager;
  }

  rpc gateway-diagnosis {
    description
      "Sslvpn gateway diag information.";
    input {
      leaf vrf {
        type ntos-types:ntos-obj-name-type;
        description
          "The name of vrf.";
      }
      leaf gateway {
        type ntos-types:ntos-obj-name-type;
        description
          "The name of gateway.";
      }
      leaf configure {
        type empty;
        description
          "Show gateway configure.";
      }
      leaf statistics {
        type enumeration {
          enum all;
          enum iptunnel;
          enum login;
          enum smsg;
        }
        description
          "The sslvpn statistics information";
      }
      leaf iptunnel {
        type enumeration {
          enum session;
          enum resource;
          enum iptun-io;
        }
        description
          "Show sslvpn iptunnel information";
      }

      leaf license {
        type empty;
        description
          "Show sslvpn license information";
      }

      leaf client-update {
        type empty;
        description
          "Show sslvpn secloud client information";
      }

    }
    output {
      leaf buffer {
        description
          "The command output buffer.";
        type string;
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "sslvpn diagnosis";
  }

  rpc check-deps-policy-exist {
    description
      "Check sslvpn gateway depends policy exists. policy include vinf, vinf-zone, security-policy";
    input {
      leaf vrf {
        mandatory true;
        type ntos:vrf-name;
        description
          "The name of vrf which will check depends policy.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf gw-name {
        type ntos-types:ntos-obj-name-type;
        description "The gateway name.";
      }
    }

    output {
      leaf result {
        type boolean;
        description
          "The result of check depends policy.";
      }

      leaf detail {
        type string;
        description
          "The detail of check depends policy.";
      }

      leaf errmods {
        type string;
        description
          "The modules of depends policy nonexist.";
      }
    }
    ntos-ext:nc-cli-cmd "sslvpn check-deps-policy-exist";
    ntos-ext:nc-cli-command-no-pager;
  }

  rpc recovery-deps-policy {
    description
      "Recovery sslvpn gateway depends policy. policy include vinf, vinf-zone, security-policy";
    input {
      leaf vrf {
        mandatory true;
        type ntos:vrf-name;
        description
          "The name of vrf which will check depends policy.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf gw-name {
        type ntos-types:ntos-obj-name-type;
        description "The gateway name.";
      }

      leaf recovery-mod {
        type enumeration {
          enum all {
            description    "Recovery all depends policy.";
          }
          enum sp {
            description    "Recovery sslvpn depends security policy";
          }
          enum vinf {
            description    "Recovery sslvpn depends virtual tunnuel interface.";
          }
        }
        description "Recovery depends policy modules.";
      }
    }

    output {
      leaf ret {
        type string;
        description
          "The result infomations.";
      }
    }
    ntos-ext:nc-cli-cmd "sslvpn recovery-deps-policy";
    ntos-ext:nc-cli-command-no-pager;
  }

  rpc check-pools-conflict-with-interfaces {
    description "Check if the address pool conflicts with the physical interface network segment.";
    input {
      leaf vrf {
        type ntos:vrf-name;
        description "The name of vrf which will check pools conflict with interfaces.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf-list pools {
        type ipv4-address-type;
        description "ip pool addresses.";
      }

      leaf-list interfaces {
        type ntos-types:ifname;
        ntos-ext:nc-cli-completion-xpath
          "../ntos:config/ntos:vrf/ntos-interface:interface/*/*[local-name()='name']";
      }

      list pools-range {
        key "start-ip end-ip";
        description "IPv4 range.";
        leaf start-ip {
          type ntos-inet:ipv4-address;
          description "Starting IPv4 Address of a range.";
        }
        leaf end-ip {
          type ntos-inet:ipv4-address;
          description "Last IPv4 Address of a range.";
        }

        leaf mask {
          type mask-type;
          description "The mask of ip pools' net range.";
        }
      }
    }

    output {
      leaf buffer {
        description
          "The json buffer of checking pools and interfaces conflict output.";
        type string;
      }
    }

    ntos-ext:nc-cli-cmd "sslvpn check-pools-conflict-with-interfaces";
    ntos-ext:nc-cli-command-no-pager;
  }

}
