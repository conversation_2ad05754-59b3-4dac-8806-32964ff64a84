module ntos-sysha {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:sysha";
  prefix ntos-sysha;

  import ntos {
    prefix ntos;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-commands {
    prefix ntos-cmd;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-system {
    prefix ntos-system;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS System High Availability module.";

  revision 2024-07-30 {
    description
      "Initial version.";
    reference "";
  }

  grouping sysha-monitoring-config {
    description
      "Configuration for SYSHA Monitoring.";

    container monitor {
      description
        "SYSHA Monitoring configuration.";

      leaf enabled {
        type boolean;
        default "true";
        description
          "Enable or disable the SYSHA Monitoring.";
      }
    }
  }

  rpc sysha {
    description
      "Show the sysha state.";
    input {
      must 'count(*) <= 2' {
        error-message "Should not more than two option.";
      }
      leaf all {
        type empty;
        description
          "SYSHA all state";
      }
      leaf cpu {
        type empty;
        description
          "cpu alerting and monitoring state";
      }
      leaf mem {
        type empty;
        description
          "memory alerting and monitoring state";
      }
      leaf fd {
        type empty;
        description
          "file description alerting and monitoring state";
      }
      leaf io {
        type empty;
        description
          "io alerting state";
      }
      uses sysha-monitoring-config;
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-ext:nc-cli-cmd "sysha";
    ntos-api:internal;
  }
}
