module ntos-system {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:system";
  prefix ntos-system;

  import ietf-yang-types {
    prefix ietf-yang;
  }
  import iana-timezones {
    prefix iana-tz;
  }
  import ietf-netconf-acm {
    prefix nacm;
  }
  import ntos {
    prefix ntos;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-commands {
    prefix ntos-cmd;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-ip {
    prefix ntos-ip;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS system management module.";

  revision 2024-01-11 {
    description
      "Add scheduled restart";
    reference "revision 2024-01-11";
  }

  revision 2023-08-25 {
    description
      "Add crashinfo service";
    reference "revision 2023-08-25";
  }
  revision 2022-12-15 {
    description
      "Add show c memory RPC";
    reference "revision 2022-12-15";
  }
  revision 2022-07-20 {
    description
      "Add set-time timestamp";
    reference "revision 2022-07-20";
  }
  revision 2022-06-26 {
    description
      "Add show-version RPC";
    reference "revision 2022-06-26";
  }
  revision 2022-06-07 {
    description
      "Add traffic-capture-pcapview-listbyexpr RPC";
    reference "revision 2022-06-07";
  }
  revision 2022-01-25 {
    description
      "Add show-uptime-history RPCs";
    reference "revision 2022-01-25";
  }
  revision 2020-12-04 {
    description
      "Add traffic capture RPCs.";
    reference "revision 2020-12-04";
  }
  revision 2020-11-12 {
    description
      "Conntrack and neighbor containers are now removed.";
    reference "revision 2020-11-12";
  }
  revision 2019-12-06 {
    description
      "Deprecated conntrack and neighbor containers are now obsolete.";
    reference "revision 2019-05-07";
  }
  revision 2019-07-05 {
    description
      "Add banner rpc.";
    reference "";
  }
  revision 2019-05-07 {
    description
      "Move conntrack and neighbor configuration in the network-stack container.";
    reference "";
  }
  revision 2018-10-03 {
    description
      "Initial version.";
    reference "";
  }

  grouping system-neighbor {
    description
      "Neighbor advanced configuration.";

    container neighbor {
      description
        "Neighbor advanced configuration.";

      leaf ipv4-max-entries {
        type uint32 {
          range "16..400000";
        }
        description
          "Maximum number of IPv4 neighbors.";
      }

      leaf ipv6-max-entries {
        type uint32 {
          range "16..400000";
        }
        description
          "Maximum number of IPv6 neighbors.";
      }
    }
  }

  grouping system-conntrack {
    description
      "Conntrack advanced configuration.";

    container conntrack {
      description
        "Conntrack advanced configuration.";

      leaf max-entries {
        type uint32 {
          range "16..10000000";
        }
        description
          "Maximum number of Netfilter conntracks.";
      }

      leaf tcp-timeout-close {
        type uint32 {
          range "0..8589934";
        }
        description
          "Conntrack TCP timeout close.";
      }

      leaf tcp-timeout-close-wait {
        type uint32 {
          range "0..8589934";
        }
        description
          "Conntrack TCP timeout close wait.";
      }

      leaf tcp-timeout-established {
        type uint32 {
          range "0..8589934";
        }
        description
          "Conntrack TCP timeout established.";
      }

      leaf tcp-timeout-fin-wait {
        type uint32 {
          range "0..8589934";
        }
        description
          "Conntrack TCP timeout fin wait.";
      }

      leaf tcp-timeout-last-ack {
        type uint32 {
          range "0..8589934";
        }
        description
          "Conntrack TCP timeout last ack.";
      }

      leaf tcp-timeout-max-retrans {
        type uint32 {
          range "0..8589934";
        }
        description
          "Conntrack TCP timeout max retrans.";
      }

      leaf tcp-timeout-syn-recv {
        type uint32 {
          range "0..8589934";
        }
        description
          "Conntrack TCP timeout syn recv.";
      }

      leaf tcp-timeout-syn-sent {
        type uint32 {
          range "0..8589934";
        }
        description
          "Conntrack TCP timeout syn sent.";
      }

      leaf tcp-timeout-time-wait {
        type uint32 {
          range "0..8589934";
        }
        description
          "Conntrack TCP timeout time wait.";
      }

      leaf tcp-timeout-unacknowledged {
        type uint32 {
          range "0..8589934";
        }
        description
          "Conntrack TCP timeout unacknowledged.";
      }

      leaf udp-timeout {
        type uint32 {
          range "0..8589934";
        }
        description
          "Conntrack UDP timeout.";
      }

      leaf udp-timeout-stream {
        type uint32 {
          range "0..8589934";
        }
        description
          "Conntrack UDP timeout stream.";
      }
    }
  }

  grouping system-global-config {
    description
      "System-wide configuration parameters.";

    leaf hostname {
      type ntos-inet:domain-name;
      description
        "The hostname of the device -- should be a single domain
         label, without the domain.";
      ntos-ext:data-not-sync;
    }

    leaf devicename {
      type string;
      description
        "The device name.";
      ntos-ext:data-not-sync;
    }

    leaf cp-mask {
      type union {
        type enumeration {
          enum default {
            description
              "Use all cores except fast path ones for control plane.";
          }
        }
        type ntos-types:coremask;
      }
      default "default";
      description
        "Cores on which control plane applications run.";
      ntos-ext:feature "product";
    }

    container nfp {
      description
        "Network forwarding process.";

      container autoperf {
        leaf enabled {
          type boolean;
          default "true";
          description
            "Run perf when cpu-usage is higher than threshold";
        }
      }
    }
  }

  grouping system-network-stack {
    description
      "Global network stack default parameters.";

    container network-stack {
      description
        "Network stack parameters.";

      container bridge {
        description
          "Bridge default parameters.";

        leaf call-ipv4-filtering {
          type boolean;
          description
            "Call IPv4 filtering hooks on bridges.";
        }

        leaf call-ipv6-filtering {
          type boolean;
          description
            "Call IPv6 filtering hooks on bridges.";
        }
      }

      container icmp {
        description
          "ICMP default parameters.";

        leaf ignore-icmp-echo-broadcast {
          type boolean;
          description
            "Ignore all ICMP ECHO and TIMESTAMP requests sent via broadcast or multicast.";
          status obsolete;
        }

        leaf rate-limit-icmp {
          type uint16 {
            range "0..1000";
          }
          units "milliseconds";
          description
            "The minimum time space that separates the sending of two consecutive ICMP packets.
             By default, such space is 1000 ms.";
        }

        leaf rate-mask-icmp {
          type bits {
            bit echo-reply {
              description
                "Echo Reply.";
            }
            bit destination-unreachable {
              description
                "Destination Unreachable.";
            }
            bit source-quench {
              description
                "Source Quench.";
            }
            bit redirect {
              description
                "Redirect.";
            }
            bit echo-request {
              description
                "Echo Request.";
            }
            bit time-exceeded {
              description
                "Time Exceeded.";
            }
            bit parameter-problem {
              description
                "Parameter Problem.";
            }
            bit timestamp-request {
              description
                "Timestamp Request.";
            }
            bit timestamp-reply {
              description
                "Timestamp Reply.";
            }
            bit info-request {
              description
                "Info Request.";
            }
            bit info-reply {
              description
                "Info Reply.";
            }
            bit address-mask-request {
              description
                "Address Mask Request.";
            }
            bit address-mask-reply {
              description
                "Address Mask Reply.";
            }
          }

          description
            "Mask made of ICMP types for which rates are being limited.";
        }
      }

      container ipv4 {
        description
          "IPv4 default parameters.";
        uses ntos-ip:ipv4-parameters;
      }

      container ipv6 {
        description
          "IPv6 default parameters.";
        uses ntos-ip:ipv6-parameters;
      }
      ntos-ext:feature "product";
      uses system-neighbor;
      uses system-conntrack;
    }
  }

  grouping banner-grouping {
    description
      "Login banner grouping.";

    leaf message {
      type string;
      description
        "Message to display.";
    }

    leaf reset {
      type empty;
      description
        "Reset message to factory defaults.";
    }
  }

  grouping poweroff-grouping {
    description
      "Poweroff grouping.";

    leaf delay {
      type uint32;
      units "seconds";
      default "60";
      description
        "The number of seconds to wait before poweroff.
         During that time, it is possible to cancel the poweroff.";
    }

    leaf cancel {
      type empty;
      description
        "If defined, cancel a pending poweroff.";
    }
  }

  grouping reboot-grouping {
    description
      "Reboot grouping.";

    leaf delay {
      type uint32;
      units "seconds";
      default "60";
      description
        "The number of seconds to wait before reboot.
         During that time, it is possible to cancel the reboot.";
    }

    leaf cancel {
      type empty;
      description
        "If defined, cancel a pending reboot.";
    }
  }

  grouping traffic-grouping {
    description
      "Traffic grouping.";

    leaf vrf {
      type string;
      default "main";
      description
        "The VRF in which to capture traffic. This must be the VRF the
         interface belongs to. By default, the interface is assumed to
         be in the 'main' vrf.";
      ntos-ext:nc-cli-completion-xpath
        "/ntos:state/ntos:vrf/ntos:name";
    }

    leaf count {
      type uint16 {
        range "1..1000";
      }
      description
        "Stop after capturing count packets.";
    }

    leaf filter {
      type string {
        ntos-ext:nc-cli-shortdesc "<pcap-expr>";
      }
      description
        "Optional filter expression. This must be a valid PCAP filter.
         See https://www.tcpdump.org/manpages/pcap-filter.7.html for more
         details.";
    }

    leaf interface {
      type string {
        ntos-ext:nc-cli-shortdesc "<ifname>";
      }
      mandatory true;
      description
        "The name of the network interface on which to monitor traffic.";
      ntos-ext:nc-cli-no-name;
      ntos-ext:nc-cli-completion-xpath
        "/ntos:state/ntos:vrf[ntos:name=string(current()/ntos-system:vrf)]/ntos-interface:interface/*/*[local-name()='name'] |
         /ntos:state/ntos:vrf[ntos:name='main'][not(current()/ntos-system:vrf)]/ntos-interface:interface/*/*[local-name()='name']";
    }
  }

  typedef scheduled-restart-weekday {
    type enumeration {
      enum sun {
        value 0;
        description
          "Sunday.";
      }
      enum mon {
        value 1;
        description
          "Monday.";
      }
      enum tue {
        value 2;
        description
          "Tuesday.";
      }
      enum wed {
        value 3;
        description
          "Wednesday.";
      }
      enum thu {
        value 4;
        description
          "Thursday.";
      }
      enum fri {
        value 5;
        description
          "Friday.";
      }
      enum sat {
        value 6;
        description
          "Saturday.";
      }
    }
  }

  grouping system-scheduled-restart-config
  {
    description
      "The configuration of scheduled restart.";

    leaf enabled {
      type boolean;
      default "false";
      description
        "Enable or disable the scheduled restart function.";
    }

    leaf hour {
      type uint32 {
        range "0..23" {
          error-message
            "The index must >= 0 and <= 23.";
        }
      }
      default "3";
      description
        "Config hour of scheduled restart.";
    }

    leaf minute {
      type uint32 {
        range "0..59" {
          error-message
            "The index must >= 0 and <= 59.";
        }
      }
      default "0";
      description
        "Config minute of scheduled restart.";
    }

    list weekday {
      key "key";
      description
        "Day of the week(sun|mon|tue|wed|thu|fri|sat).";
      ntos-ext:nc-cli-one-liner;
      leaf key {
        type scheduled-restart-weekday;
      }
      max-elements 7;
      unique "key";
    }

    leaf once {
      type boolean;
      default "false";
      description
        "Enabled once of scheduled restart.";
    }

  }

  augment "/ntos:config/ntos-system:system" {
    description
      "Scheduled restart configuration.";

    container scheduled-restart {
      description
        "Scheduled restart configuration.";
      uses system-scheduled-restart-config;
    }
  }

  augment "/ntos:state/ntos-system:system" {
    description
      "Scheduled restart status.";

    container scheduled-restart {
      description
        "Scheduled restart status.";
      leaf running {
        type uint32;
        description
          "Scheduled restart running status.";
      }
    }
  }

  rpc set-time {
    description
      "Set system time.";
    input {
      choice name {
        description
          "Choice time type";

        case time{
          leaf time {
            ntos-ext:nc-cli-no-name;
            type string;
            description
              "Time string. As YYYY-MM-DD hh:mm:ss. Between 1970-01-01 23:59:59 to 2200-12-31 23:59:59";
          }
        }

        case timestamp {
          leaf timestamp {
            ntos-ext:nc-cli-no-name;
            type ietf-yang:timestamp;
            description
              "Timestamp";
          }
        }
      }
    }

    ntos-ext:nc-cli-cmd "set-time";
    ntos-api:internal;
  }

  rpc sync-time {
    description
      "Synchronize system time.";
    ntos-ext:nc-cli-cmd "sync-time";
    ntos-api:internal;
  }

  rpc banner {
    description
      "Manage login banner.";
    input {
      ntos-ext:nc-cli-exclusive;

      container pre-login {
        description
          "Manage banner before a user logs in.";
        uses banner-grouping;
      }

      container post-login {
        description
          "Manage banner after a user logs in.";
        uses banner-grouping;
      }
    }
    output {

      leaf buffer {
        type string;
        description
          "The command output buffer.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    nacm:default-deny-all;
    ntos-ext:nc-cli-cmd "banner";
  }

  rpc sysupgrade {
    ntos-ext:nc-cli-cmd "sysupgrade";
    ntos-api:internal;
    description
      "Upgrade the system software.";
    input {
      leaf url {
        type union {
          type ntos-types:ftp-url;
          type ntos-types:tftp-url;
          type ntos-types:http-url;
          type string {
            pattern 'flash:/.*';
            ntos-ext:nc-cli-shortdesc "<flash:/path/to/file>";
          }
          type string {
            pattern 'usb0:/.*';
            ntos-ext:nc-cli-shortdesc "<usb0:/path/to/file>";
          }
          type string {
            pattern 'usb1:/.*';
            ntos-ext:nc-cli-shortdesc "<usb1:/path/to/file>";
          }
        }
        ntos-ext:nc-cli-no-name;
        description
          "A filename begin with this prefix";
      }
      leaf async {
        type boolean;
        default "false";
        description
          "Upgrade the system software in the background.";
      }
      leaf rollback {
        type empty;
        description
          "Rollback software version";
      }
      leaf rollback-rulelib {
        type empty;
        description
          "Rollback rulelib version";
      }
      leaf firmware {
        type empty;
        description
          "Upgrade firmware";
      }
    }
    output {
      leaf status {
        type uint32;
        description
          "The status of system upgrade.";
      }

      leaf errcode {
        type uint32;
        description
          "The error code of system upgrade.";
      }
      leaf buffer {
        type string;
        description
          "The command output buffer.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
  }

  rpc change-security-cloud {
    ntos-ext:nc-cli-cmd "change-security-cloud";
    ntos-api:internal;
    description
      "Change security cloud.";
    input {
      leaf url {
        type string;
        ntos-ext:nc-cli-no-name;
        description
          "URL of cloud server.";
      }
      leaf port {
        type uint32;
        ntos-ext:nc-cli-no-name;
        description
          "Server port.";
      }
    }
    output {
      leaf res {
        type uint32;
        description
          "The error code.";
      }
    }
  }

  rpc show-onlineupgrade-info {
    description
      "Show onlineupgrade infomation.";
    output {
      leaf data {
        type string;
        description
          "The command output buffer.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "onlineupgrade info";
    ntos-api:internal;
  }


  rpc dir {
    description
      "List files on a file system.";
    input {
      leaf info {
        type union {
          type enumeration {
            enum flash: {
              description
                "Device name.";
            }
            enum sata0:{
              description
                "Device name.";
            }
            enum sata1:{
              description
                "Device name.";
            }
            enum usb0:{
              description
                "Device name.";
            }
            enum usb1:{
              description
                "Device name.";
            }
            enum log: {
              description
                "Device log.";
            }
            enum tmp: {
              description
                "Tmp filesystem.";
            }
          }
          type string;
        }
        description
          "File name.";
        ntos-ext:nc-cli-no-name;
      }
    }
    output {
      leaf buffer {
        type string;
        description
          "The command output buffer.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-cmd "dir";
  }

  rpc delete {
    description
      "Delete a file.";
    input {
      leaf info {
        type union {
          type enumeration {
            enum flash: {
              description
                "Device name.";
            }
            enum sata0:{
              description
                "Device name.";
            }
            enum sata1:{
              description
                "Device name.";
            }
          }
          type string;
        }
		mandatory true;
        description
          "File name.";
        ntos-ext:nc-cli-no-name;
      }
    }
    output {
      leaf buffer {
        type string;
        description
          "The command output buffer.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-cmd "delete";
  }

  rpc reboot {
    description
      "Schedule a system reboot after a grace period.";
    input {
      uses reboot-grouping;
    }
    output {

      leaf reboot-time {
        type string;
        description
          "The time at which the system will reboot.";
      }
    }
    nacm:default-deny-all;
  }

  rpc reboot-cli {
    description
      "Schedule a system reboot after a grace period.";
    input {
      uses reboot-grouping;

      leaf force {
        type empty;
        description
          "If defined, force reboot even if startup configuration is different
           than running configuration.";
      }
    }
    output {
      leaf buffer {
        type string;
        description
          "The command output buffer.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    nacm:default-deny-all;
    ntos-ext:nc-cli-cmd "reboot";
    ntos-api:internal;
  }

  rpc poweroff {
    description
      "Schedule a system poweroff after a grace period.";
    input {
      uses poweroff-grouping;
    }
    output {

      leaf poweroff-time {
        type string;
        description
          "The time at which the system will be powered off.";
      }
    }
    nacm:default-deny-all;
  }

  rpc poweroff-cli {
    description
      "Schedule a system poweroff after a grace period.";
    input {
      uses poweroff-grouping;

      leaf force {
        type empty;
        description
          "If defined, force poweroff even if startup configuration is different
           than running configuration.";
      }
    }
    output {
      leaf buffer {
        type string;
        description
          "The command output buffer.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    nacm:default-deny-all;
    ntos-ext:nc-cli-cmd "poweroff";
    ntos-api:internal;
  }

  rpc ping {
    description
      "Send ICMP ECHO_REQUEST messages to network hosts and print their
       responses.";
    input {

      leaf vrf {
        type string;
        default "main";
        description
          "The VRF in which to send the ICMP ECHO_REQUESTs.
           By default, they are sent in the 'main' vrf.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf count {
        type uint16;
        description
          "Stop after sent count ECHO_REQUEST packets.";
      }

      leaf packetsize {
        type uint16{
          range "0..1500";
        }
        description
          "Specifies the number of data bytes to be sent.
           The default is 56, which  translates into 64 ICMP data bytes
           when combined with the 8 bytes of ICMP header data.";
      }

      leaf nodns {
        type empty;
        description
          "Numeric output only.  No attempt will be made to lookup
           symbolic names for host addresses.";
      }

      leaf ipv6 {
        type empty;
        description
          "Force IPv6 operation only. By default, it is detected from the
           destination. If destination is a host name, ipv4 is used by default
           unless this flag is set.";
      }

      leaf source {
        type string;
        description
          "Specify source IP address or source interface name.

           For IPv6, when doing ping to a link-local scope address, link
           specification (by the '%'-notation in destination, or by this
           option) is required.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf[ntos:name=string(current()/ntos-system:vrf)]/ntos-interface:interface/*/*[local-name()='name'] |
           /ntos:state/ntos:vrf[ntos:name='main'][not(current()/ntos-system:vrf)]/ntos-interface:interface/*/*[local-name()='name']";
      }

      leaf rate {
        type uint16 {
          range "1..1000";
        }
        description
          "The number of packets to send per second. By default, 1 packet is
           sent every second.";
      }

      leaf destination {
        type string {
          ntos-ext:nc-cli-shortdesc "<destination>";
        }
        mandatory true;
        description
          "The destination host (name or IP address).";
        ntos-ext:nc-cli-no-name;
      }
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-ext:nc-cli-cmd "ping";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
  }

  rpc traceroute {
    description
      "Display the route (path) that was used to connect to a certain IP
       address or hostname. It also measures the transit delays among hops.";

    input {
      leaf vrf {
        type string;
        default "main";
        description
          "The VRF in which the packets are sent by traceroute.
           By default, they are sent in the 'main' vrf.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf nodns {
        type empty;
        description
          "Do not try to map IP addresses to host names when displaying them.";
      }

      leaf ipv6 {
        type empty;
        description
          "Force IPv6 operation only. By default, it is detected from the
           destination. If destination is a host name, ipv4 is used by default
           unless this flag is set.";
      }

      leaf source {
        type string;
        description
          "Chooses an alternative source address. Note that an address
           of one of the interfaces must be selected. By default, the
           address of the outgoing interface is used.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf[ntos:name=string(current()/ntos-system:vrf)]/ntos-interface:interface/*/*[local-name()='name'] |
           /ntos:state/ntos:vrf[ntos:name='main'][not(current()/ntos-system:vrf)]/ntos-interface:interface/*/*[local-name()='name']";
      }

      leaf host {
        type string {
          ntos-ext:nc-cli-shortdesc "<host>";
        }
        mandatory true;
        description
          "The destination host (name or IP address).";
        ntos-ext:nc-cli-no-name;
      }
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-ext:nc-cli-cmd "traceroute";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
  }

  rpc cmd-autoperf {
    description
      "Display the hot spot functions for specified cpu core.";

    input {
      choice name {
        description
          "Choice autoperf parameters";

        case cpu-high-threshold {
          leaf cpu-high-threshold {
            type uint32 {
              range "0..100";
            }
            units "X%";
            default "95";
            description
            "CPU high threshold.";
          }
        }

        case max-file-num {
          leaf max-file-num {
            type uint32 {
              range "1..10";
            }
            default "5";
            description
              "Max perfdata file number.";
          }
        }

        case sampling-interval {
          leaf sampling-interval {
            type uint32 {
              range "60..600";
            }
            units "seconds";
            default "300";
            description
              "Perf sampling interval time.";
          }
        }

        case sampling-time {
          leaf sampling-time {
            type uint32 {
              range "1..60";
            }
            units "seconds";
            default "5";
            description
              "Perf sampling time.";
          }
        }

        case set-debug {
          leaf set-debug {
            type boolean;
            default "false";
            description
              "Set autoperf debug.";
          }
        }
      }
    }
    ntos-ext:nc-cli-cmd "autoperf";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
  }

  rpc show-autoperf {
    description
      "Show autoperf parameters.";
    output {
      leaf cpu-high-threshold {
        type uint32 {
          range "0..100";
        }
        units "X%";
        description
          "CPU high threshold.";
      }

      leaf max-file-num {
        type uint32 {
          range "1..10";
        }
        description
          "Max perfdata file number.";
      }

      leaf sampling-interval {
        type uint32 {
          range "60..600";
        }
        units "seconds";
        description
          "Perf sampling interval time.";
      }

      leaf sampling-time {
        type uint32 {
          range "1..60";
        }
        units "seconds";
        description
          "Perf sampling time.";
      }

      leaf set-debug {
        type boolean;
        description
          "Set autoperf debug.";
      }
    }
    ntos-ext:nc-cli-show "autoperf";
    ntos-api:internal;
  }

  rpc perf {
    description
      "Display the hot spot functions for specified cpu core.";

    input {
      choice name {
        description
          "Choice perf parameters";

        case record {
          container record {
            description
              "Record cpu's profile into perf.data";

            leaf cpu {
              type uint32 {
                range "0..127";
              }
              description
                "Physical cpu core id";
            }

            leaf event {
              type enumeration {
                enum syscall {
                  description
                    "System call.";
                }
                enum pagefault {
                  description
                    "Page fault.";
                }
                enum kernel {
                  description
                    "Kernel symbol.";
                }
              }
              description
                "Record specified event";
            }

            leaf nostack {
              type empty;
              description
                "Record without function call stack";
            }

            leaf time {
              type uint32 {
                range "1..600";
              }
              description
                "Record time in seconds";
            }
          }
        }

        case stat {
          container stat {
            description
              "Display cpu's stats for specified cpu core.";

            leaf event {
              type enumeration {
                enum syscall {
                  description
                    "System call.";
                }
                enum pagefault {
                  description
                    "Page fault.";
                }
              }
              description
                "Display stats of specified event";
            }

            leaf time {
              type uint32 {
                range "1..600";
              }
              description
                "Stat time in seconds";
            }
          }
        }
      }
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-ext:nc-cli-cmd "perf";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
  }

  rpc show-perf {
    description
      "Show perf report.";
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-ext:nc-cli-show "perf";
    ntos-ext:nc-cli-hidden;
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
  }

  rpc show-product {
    description
      "Show the product infomation.";
    output {
      leaf start-time {
        type string;
        description
          "The system start time.";
      }
      leaf uptime {
        type string;
        description
          "The system uptime.";
      }
      leaf productname {
        type string;
        description
          "The product name.";
      }
      leaf hardwarever {
        type string;
        description
          "The hardware version.";
      }
      leaf serialnum {
        type string;
        description
          "The serialn number.";
      }
      leaf ethaddr {
        type string;
        description
          "The hw ethaddr.";
      }
      leaf softwarever {
        type string;
        description
          "The system software version.";
      }
      leaf softwarenum {
        type string;
        description
          "The system software number.";
      }
      leaf boot-version {
        type string;
        description
          "The system boot version.";
      }
    }
    ntos-ext:nc-cli-show "product";
    ntos-api:internal;
  }

  rpc show-traffic {
    status deprecated {
      ntos-ext:status-obsoleted-release "21q3";
      ntos-ext:status-deprecated-revision "2020-12-07";
      ntos-ext:status-description "This command is replaced by cmd traffic-capture.";
    }
    description
      "Print traffic flowing on a network interface.";
    input {
      uses traffic-grouping;
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-ext:nc-cli-cmd "show-traffic";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
  }

  rpc traffic-capture {
    description
      "Print traffic flowing on a network interface.";
    input {
      uses traffic-grouping;
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-ext:nc-cli-cmd "traffic-capture";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
  }

  rpc traffic-capture-new {
    description
      "Capture traffic flowing on a network interface.";
    input {

      leaf name {
        type string {
          pattern '[-A-Za-z0-9._@]+';
          ntos-ext:nc-cli-shortdesc "<name>";
        }
        description
          "The name of the capture file. If not set a unique name will be
           automatically chosen (in format YYYY-MM-DD_HH-MM-SS.<ifname>.pcap).
           otherwise, if the file already exists it will be overwritten.";
      }
      uses traffic-grouping;
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-ext:nc-cli-cmd "traffic-capture new";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
  }

  rpc traffic-capture-list {
    description
      "List captured traffic file.";
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-ext:nc-cli-cmd "traffic-capture list";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
  }

  rpc traffic-capture-flush {
    description
      "Flush all captured traffic files.";
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-ext:nc-cli-cmd "traffic-capture flush";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
  }

  rpc traffic-capture-read {
    description
      "Read a captured traffic file.";
    input {

      leaf name {
        type string {
          pattern '[-A-Za-z0-9._@]+';
          ntos-ext:nc-cli-shortdesc "<name>";
        }
        mandatory true;
        description
          "The name of capture file.";
        ntos-ext:nc-cli-no-name;
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos-system:system/traffic-capture";
      }
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-ext:nc-cli-cmd "traffic-capture read";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
  }

  rpc traffic-capture-pcapview-listbyexpr {
    description
      "Show packages list of a captured traffic file by the expr.";
    input {
      leaf name {
        type string {
          pattern '[-A-Za-z0-9._@]+';
          ntos-ext:nc-cli-shortdesc "<name>";
        }
        mandatory true;
        description
          "The name of capture file.";
        ntos-ext:nc-cli-no-name;
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos-system:system/traffic-capture";
      }
      leaf expr {
        type string {
        }
        description
          "The expr of filter.";
      }
      leaf maxcount {
        type uint32 {
          range "1..1000";
        }
        description
          "Max count";
      }
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-ext:nc-cli-cmd "traffic-capture pcapview-listbyexpr";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
  }

  rpc traffic-capture-delete {
    description
      "Delete a captured traffic file.";
    input {

      leaf name {
        type string {
          pattern '[-A-Za-z0-9._@]+';
          ntos-ext:nc-cli-shortdesc "<name>";
        }
        mandatory true;
        description
          "The name of capture file.";
        ntos-ext:nc-cli-no-name;
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos-system:system/traffic-capture";
      }
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-ext:nc-cli-cmd "traffic-capture delete";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
  }

  rpc traffic-capture-all-delete {
    description
      "Delete all captured traffic file.";
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-ext:nc-cli-cmd "traffic-capture all-delete";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
  }

  rpc traffic-capture-export {
    description
      "Export a captured traffic file.";
    input {

      leaf vrf {
        type string;
        default "main";
        description
          "The VRF in which remote access is done.
           By default, they are sent in the 'main' vrf.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf url {
        type union {
          type ntos-types:sftp-url;
          type ntos-types:scp-url;
          type ntos-types:smtp-url;
          type ntos-types:ftp-url;
          type ntos-types:tftp-url;
          type ntos-types:http-url;
        }
        mandatory true;
        description
          "The destination URL.";
      }

      leaf name {
        type string {
          pattern '[-A-Za-z0-9._@]+';
          ntos-ext:nc-cli-shortdesc "<name>";
        }
        mandatory true;
        description
          "The name of capture file.";
        ntos-ext:nc-cli-no-name;
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos-system:system/traffic-capture";
      }
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-ext:nc-cli-cmd "traffic-capture export";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
  }

  rpc troubleshooting-report {
    description
      "Manage troubleshooting reports.";
    input {
      must 'count(*) = 1' {
        error-message "One (and only one) action must be specified.";
      }
      ntos-ext:nc-cli-exclusive;

      leaf list {
        type empty;
        description
          "List existing troubleshooting reports.";
      }

      container delete {
        presence "makes delete available";
        description
          "Delete an existing troubleshooting report.";

        leaf name {
          type string {
            ntos-ext:nc-cli-shortdesc "<name>";
          }
          mandatory true;
          description
            "The name of the report to delete.";
          ntos-ext:nc-cli-no-name;
          ntos-ext:nc-cli-completion-xpath
            "/ntos:state/ntos-system:system/troubleshooting-report";
        }
      }

      leaf flush {
        type empty;
        description
          "Delete all existing troubleshooting reports.";
      }

      container new {
        presence "makes new available";
        description
          "Generate a new troubleshooting report.";
        leaf scenario1 {
          type string {
            ntos-ext:nc-cli-shortdesc "<scenario1>";
          }
          mandatory false;
          description "The scenario(level 1).";
          ntos-ext:nc-cli-no-name;
        }
        leaf scenario2 {
          type string {
            ntos-ext:nc-cli-shortdesc "<scenario2>";
          }
          mandatory false;
          description "The scenario(level 2).";
          ntos-ext:nc-cli-no-name;
        }
      }

      container export {
        presence "makes export available";
        description
          "Export an existing troubleshooting report to a remote server
           via SFTP.";

        leaf vrf {
          type string;
          default "main";
          description
            "The VRF in which remote access is done.
             By default, they are sent in the 'main' vrf.";
          ntos-ext:nc-cli-completion-xpath
            "/ntos:state/ntos:vrf/ntos:name";
        }

        leaf url {
          type union {
            type ntos-types:sftp-url;
            type ntos-types:scp-url;
            type ntos-types:smtp-url;
            type ntos-types:ftp-url;
            type ntos-types:tftp-url;
            type ntos-types:http-url;
          }
          mandatory true;
          description
            "The destination URL.";
        }

        leaf name {
          type string {
            ntos-ext:nc-cli-shortdesc "<name>";
          }
          mandatory true;
          description
            "The name of the report to export.";
          ntos-ext:nc-cli-no-name;
          ntos-ext:nc-cli-completion-xpath
            "/ntos:state/ntos-system:system/troubleshooting-report";
        }
      }
    }
    output {
      uses ntos-cmd:long-cmd-output;
      uses ntos-cmd:long-cmd-status;
    }
    ntos-ext:nc-cli-cmd "troubleshooting-report";
    ntos-api:internal;
  }

  grouping process-grouping {
    leaf name {
      type string;
      ntos-ext:nc-cli-no-name;
      description
          "Process name";

    }
  }
  rpc process {
    description
      "Process management commands.";

    input {
      container start {
        description
          "Start a process.";
        ntos-ext:nc-cli-group "subcommand";
        uses process-grouping;
      }
      container restart {
        description
          "Restart a process.";
        ntos-ext:nc-cli-group "subcommand";
        uses process-grouping;
      }
      container stop {
        description
          "Stop a process.";
        ntos-ext:nc-cli-group "subcommand";
        uses process-grouping;
      }
      container status {
        description
          "Show the status of process.";
        ntos-ext:nc-cli-group "subcommand";
        uses process-grouping;
      }
    }
    output {
      leaf data {
        type string;
        description
          "The command output buffer.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-cmd "process";
    ntos-api:internal;
  }

  rpc show-tcp-connect {
    description
      "Show tcp connnect infomation.";
    output {
      leaf data {
        type string;
        description
          "The command output buffer.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "tcp connect";
    ntos-api:internal;
  }

  rpc show-devicename {
    description
      "Show devicenname.";
    output {
      leaf devicename {
        type string;
        description
          "Devicename.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "devicename";
    ntos-api:internal;
  }

  rpc show-ip-udp-detail {
    description
      "Show udp detail.";
    output {
      leaf data {
        type string;
        description
          "The command output buffer.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "ip udp detail";
    ntos-api:internal;
  }

  rpc show-date {
    description
      "Show system time.";
    output {
      leaf date {
        type string;
        description
          "The command output buffer.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "date";
    ntos-api:internal;
  }

  rpc show-uptime {
    description
      "Show system uptime.";
    output {
      leaf uptime {
        type string;
        description
          "The command output buffer.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "uptime";
    ntos-api:internal;
  }

  rpc show-uptime-history {
    description
      "Show system history uptime.";
    output {
      leaf firstboot {
        type string;
        description
          "System first boot time.";
      }
      leaf history-uptime {
        type string;
        description
          "System history uptime.";
      }
    }
    ntos-ext:nc-cli-show "uptime history";
    ntos-api:internal;
  }

  rpc show-sysupgrade {
    description
      "Show sysupgrade rollback status.";
    output {
      leaf isexist {
        type boolean;
        description
          "The rollback software is exist.";
      }
      leaf lastversion {
        type string;
        description
          "The rollback software version.";
      }
    }
    ntos-ext:nc-cli-show "sysupgrade rollback status";
    ntos-api:internal;
  }

  rpc show-task {
    description
      "Show configuration version information.";

    input {
      leaf detail {
        type empty;
        description
          "Show task detail.";
      }
    }

    output {
      leaf data {
        type string;
        description
          "The command output buffer.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "task";
    ntos-api:internal;
  }

  rpc show-version {
    description
      "Show task infomation.";

    input {
      leaf running {
        type empty;
        description
          "The command output buffer.";
      }

      leaf startup {
        type empty;
        description
          "The command output buffer.";
      }
    }

    output {
      leaf data {
        type string;
        description
          "The command output buffer.";
        ntos-ext:nc-cli-stdout;
      }
    }
    ntos-ext:nc-cli-show "version";
    ntos-api:internal;
  }

  rpc show-cpu {
    description
      "Show cpu infomation.";
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-ext:nc-cli-show "cpu";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
  }

  rpc show-exception {
    description
      "Show exception infomation.";
    output {
      leaf data {
        type string;
        description
          "The command output buffer.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "exception";
    ntos-api:internal;
  }

  rpc show-memory {
    description
      "Show memory infomation.";

    input {
      leaf process {
        type string;
        description
          "The process name.";
      }
    }

    output {
      leaf data {
        type string;
        description
          "The command output buffer.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "memory";
    ntos-api:internal;
  }

  rpc coredump {
    description
      "Manage coredump files.";
    input {
      must 'count(*) = 1' {
        error-message "One (and only one) action must be specified.";
      }
      ntos-ext:nc-cli-exclusive;

      leaf list {
        type empty;
        description
          "List existing coredump files.";
      }

      container delete {
        presence "makes delete available";
        description
          "Delete an existing coredump file.";

        leaf name {
          type string {
            ntos-ext:nc-cli-shortdesc "<name>";
          }
          mandatory true;
          description
            "The name of the coredump to delete.";
          ntos-ext:nc-cli-no-name;
          ntos-ext:nc-cli-completion-xpath
            "/ntos:state/ntos-system:system/coredump";
        }
      }

      leaf flush {
        type empty;
        description
          "Delete all existing coredump files.";
      }

      container export {
        presence "makes export available";
        description
          "Export an existing coredump file to a remote server via FTP,TFTP or HTTP.";

        leaf vrf {
          type string;
          default "main";
          description
            "The VRF in which remote access is done.
             By default, they are sent in the 'main' vrf.";
          ntos-ext:nc-cli-completion-xpath
            "/ntos:state/ntos:vrf/ntos:name";
        }

        leaf url {
          type union {
            type ntos-types:ftp-url;
            type ntos-types:tftp-url;
            type ntos-types:http-url;
          }
          mandatory true;
          description
            "The destination URL.";
        }

        leaf name {
          type string {
            ntos-ext:nc-cli-shortdesc "<name>";
          }
          mandatory true;
          description
            "The name of the coredump to export.";
          ntos-ext:nc-cli-no-name;
          ntos-ext:nc-cli-completion-xpath
            "/ntos:state/ntos-system:system/coredump";
        }
      }
    }
    output {
      uses ntos-cmd:long-cmd-output;
      uses ntos-cmd:long-cmd-status;
    }
    ntos-ext:nc-cli-cmd "coredump";
    ntos-api:internal;
  }

  rpc tracemalloc {
    description
      "Tracemalloc debug command.";

    input {
      leaf start {
        type empty;
        description
          "Start snapshot memory.";
      }

      leaf stop {
        type empty;
        description
          "Stop snapshot memory.";
      }

      leaf snapshot {
        type uint16{
          range "1..2";
        }
        description
          "Snapshot memory.";
      }

      leaf stats {
        type uint16{
          range "1..2";
        }
        description
          "Show snapshot memory.";
      }

      leaf compare {
        type uint16{
          range "1..100";
        }
        description
          "Show compare snapshot info.";
      }

      leaf memory-c {
        type empty;
        description
          "Show yams c memory.";
      }
    }

    output {
      leaf data {
        type string;
        description
          "The command output buffer.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-cmd "tracemalloc";
    ntos-api:internal;
  }

  rpc set-ipv4-arp-ignore {
    description
      "Set ipv4's arp-ignore default value.";

    input {
      leaf value-key {
        type enumeration {
          enum "any" {
            description
              "Reply for any local target IP address, configured on any
              interface.";
          }
          enum "check-interface" {
            description
              "Reply only if the target IP address is local address configured on
              the incoming interface.";
          }
          enum "check-interface-and-subnet" {
            description
              "Reply only if the target IP address is local address configured on
              the incoming interface and both with the sender's IP address are
              part from same subnet on this interface.";
          }
          enum "ignore-scope" {
            description
              "Do not reply for local addresses configured with scope host, only
              resolutions for global and link addresses are replied.";
          }
          enum "ignore-all" {
            description
              "Do not reply for all local addresses.";
          }
        }
      }
    }

    output {
      leaf data {
        type string;
        description
          "The command output buffer.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-hidden;
    ntos-ext:nc-cli-cmd "set-ipv4-arp-ignore";
  }

  rpc downgrade-config-rollback {
    description
      "Rollback module compatibility configuration when downgrading the version.";

    input {
      leaf local-version {
        type string;
      }

      leaf target-version {
        type string;
      }

      leaf translate-type {
        description
          "Consistent with the parameters of the application recognition interface,
           used to determine the device's language switch (between Chinese and English).";

        type enumeration {
          enum zh-to-en {
            description
              "Translate config to Chinese.";
          }
          enum en-to-zh {
            description
              "Translate config to English.";
          }
        }
      }
    }

    output {
      leaf rollback-module-number {
        type uint16;
      }
    }
    ntos-api:internal;
  }

  augment "/ntos:config/ntos-system:system/network-stack/neighbor" {
    description
      "Add neighbor stack parameters that are not per vrf.";

    leaf ipv4-base-reachable-time {
      type uint32;
      units "seconds";
      description
        "Time during which an IPv4 neighbor entry stays reachable.";
    }

    leaf ipv6-base-reachable-time {
      type uint32;
      units "seconds";
      description
        "Time during which an IPv6 neighbor entry stays reachable.";
    }
  }

  augment "/ntos:config" {
    description
      "Global system configuration.";

    container system {
      presence "Makes global system configuration available";
      description
        "Global system configuration.";
      uses system-global-config;
      uses system-network-stack {
        refine "network-stack/bridge/call-ipv4-filtering" {
          default "false";
        }
        refine "network-stack/bridge/call-ipv6-filtering" {
          default "false";
        }
        // refine "network-stack/icmp/ignore-icmp-echo-broadcast" {
        //   default "false";
        // }
        refine "network-stack/icmp/rate-limit-icmp" {
          default "1000";
        }
        refine "network-stack/icmp/rate-mask-icmp" {
          default "destination-unreachable
                   source-quench
                   time-exceeded
                   parameter-problem";
        }
        refine "network-stack/ipv4/forwarding" {
          default "true";
        }
        refine "network-stack/ipv4/send-redirects" {
          default "true";
        }
        refine "network-stack/ipv4/accept-redirects" {
          default "false";
        }
        refine "network-stack/ipv4/accept-source-route" {
          default "false";
        }
        refine "network-stack/ipv4/arp-announce" {
          default "any";
        }
        refine "network-stack/ipv4/arp-filter" {
          default "false";
        }
        refine "network-stack/ipv4/arp-ignore" {
          default "any";
        }
        refine "network-stack/ipv4/log-invalid-addresses" {
          default "false";
        }
        refine "network-stack/ipv6/forwarding" {
          default "true";
        }
        refine "network-stack/ipv6/autoconfiguration" {
          default "true";
        }
        refine "network-stack/ipv6/accept-router-advert" {
          default "never";
        }
        refine "network-stack/ipv6/accept-redirects" {
          default "false";
        }
        refine "network-stack/ipv6/accept-source-route" {
          default "false";
        }
        refine "network-stack/ipv6/router-solicitations" {
          default "-1";
        }
        refine "network-stack/ipv6/use-temporary-addresses" {
          default "never";
        }
      }

      leaf timezone {
        type union {
          type enumeration {
            enum UTC {
              description
                "Coordinated Universal Time.";
            }
            enum GMT {
              description
                "Greenwich Mean Time.";
            }
          }
          type iana-tz:iana-timezone;
          type ntos-types:linux-timezone;
        }
        description
          "The timezone of the device.";
      }
    }
  }

  augment "/ntos:state" {
    description
      "Global system state.";

    container system {
      description
        "Global system operational state data.";
      uses system-global-config;
      uses system-network-stack;

      leaf timezone {
        type string;
        description
          "The timezone of the device.";
      }

      leaf date {
        type string;
        description
          "The local time of the device.";
      }

      leaf-list troubleshooting-report {
        type string;
        description
          "The existing troubleshooting reports available on the system.";
      }

      leaf-list traffic-capture {
        type string;
        description
          "The existing traffic captures available on the system.";
      }

      leaf-list coredump {
        type string;
        description
          "The existing coredump file available on the system.";
      }
    }
  }

  augment "/ntos:state/ntos-system:system/network-stack/neighbor" {
    description
      "Add neighbor stack parameters that are not per vrf.";

    leaf ipv4-base-reachable-time {
      type uint32;
      units "seconds";
      description
        "Time during which an IPv4 neighbor entry stays reachable.";
    }

    leaf ipv6-base-reachable-time {
      type uint32;
      units "seconds";
      description
        "Time during which an IPv6 neighbor entry stays reachable.";
    }
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "Vrf system configuration.";
    uses system-network-stack {
      refine "network-stack" {
        description
          "Default network stack parameters for this VRF (overrides global).";
      }
    }
  }

  augment "/ntos:state/ntos:vrf" {
    description
      "Vrf system state.";
    uses system-network-stack {
      refine "network-stack" {
        description
          "Default network stack parameters for this VRF (overrides global).";
      }
    }
  }

  identity onlineupgrade {
    base ntos-types:SERVICE_LOG_ID;
    description
      "Online upgrade service.";
  }

  identity crashinfo {
    base ntos-types:SERVICE_LOG_ID;
    description
      "crashinfo ui service.";
  }
}
