module ntos-terminal-identify {
  namespace "urn:ruijie:ntos:params:xml:ns:yang:terminal-identify";
  prefix ntos-terminal-identify;

  import ntos {
    prefix ntos;
  }

  import ntos-types {
    prefix ntos-types;
  }

 import ntos-inet-types {
    prefix ntos-inet;
  }

  import ntos-extensions {
    prefix ntos-extensions;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS terminal identify module.";

  revision 2024-11-18 {
    description
      "Create.";
    reference "";
  }

  grouping terminal-identify-config {
    description
      "Configuration data for terminal identify.";

    container collect {
      leaf mode {
        type enumeration {
          enum all-collect {
            description
              "The mode of collect all.";
          }
          enum dynamic-collect {
            description
              "The mode of collect some .";
          }
          enum no-collect {
            description
              "The mode of no collect.";
          }
        }
        default dynamic-collect;
        description
          "The mode of collect.";
      }
    }
  }
  augment "/ntos:config/ntos:vrf" {
    description
      "Terminal identify configuration.";

    container terminal-identify {
      description
        "Terminal identify configuration.";
      uses terminal-identify-config;
    }
  }

  augment "/ntos:state/ntos:vrf" {
    description
      "State of appid.";

    container terminal-identify {
      description
        "Terminal identify configuration.";
      uses terminal-identify-config;
    }
  }

  rpc terminal-identify-status-show {
    description
      "Show infomation of terminal identify.";

    input {
      container collect {
        description
          "The collect infomation of terminal identify.";
        presence "Show terminal-identify collect";
      }
    }
    output {
      container collect {
        leaf mode {
          type string;
          description
            "The status of terminal collect.";
        }
      }
    }
    ntos-extensions:nc-cli-show "terminal-identify";
  }
}
