module ntos-threat-intelligence {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:threat-intelligence";
  prefix ntos-threat-intelligence;
  
  import ntos {
    prefix ntos;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel: 4008-111-000
    E-mail: <EMAIL>";
  description
    "Ruijie NTOS threat-intelligence module.";
    
  revision 2022-12-01 {
    description
      "Initial version.";
    reference
      "";
  }

  typedef action-type {
    type enumeration {
      enum block {
        description
          "Action block.";
      }
      enum warning {
        description
          "Action warning.";
      }
    }
  }

  typedef ti-type {
    type enumeration {
      enum custom {
        description
          "Custom of threat intelligence.";
      }
      enum exception {
        description
          "Exception of threat intelligence.";
      }
    }
  }

  typedef scenario-capability {
    type enumeration {
      enum in {
        description
          "In bound.";
      }
      enum out {
        description
          "Out bound.";
      }
      enum in-out {
        description
          "In and out bound both.";
      }
    }
  }

  typedef search-action-type {
    type enumeration {
      enum entry {
        description
          "entry search.";
      }
      enum file {
        description
          "File search.";
      }
    }
  }

  grouping ti-type-action-map {
    list type-action-map {
      description
        "Threat intelligence list.";
      key "name";
      leaf name {
        type string {
          length "1..127";
        }
        description
          "Threat intelligence class name.";
      }
      leaf action {
        type action-type;
        description
          "Action of threat intelligence,when a package is hited.";
      }
      leaf status {
        type boolean;
        default true;
        description
          "Threat intelligence is enable or not.";
      }
    }
  }

  grouping channel {
    list channel-tree {
      description
        "Threat intelligence sdk channel list.";
      key "name";
      leaf name {
        type string {
          length "1..127";
        }
        description
          "Threat intelligence class name.";
      }
      leaf status {
        type boolean;
        description
          "Threat intelligence is enable or not.";
      }
    }
  }

  grouping ti-capabilities {
    list capability {
      key "name";
      leaf name {
        type scenario-capability;
        description
          "Capability of scenario.";
      }
    }
  }

  grouping ti-sources {
    description
      "Information about ti source.";
    list source {
      key "name";
      leaf name {
        type string {
          length "1..31";
        }
        description
          "Threat intelligence source name.";
      }
      leaf enabled {
        type boolean;
        default true;
        description
          "Enable source.";
      }
      leaf status {
        type uint32;
        description
          "Status of source.";
      }
      leaf priority {
        type uint32;
        description
          "Priority of source.";
      }
      leaf source-type {
        type uint32;
        description
          "Type of source.";
      }
      container capabilities {
        description
          "List of capabilities.";
        uses ti-capabilities;
      }
      container channels {
        description
          "List of channels.";
        uses channel;
      }
      container ti-types {
        description
          "List of types.";
        uses ti-type-action-map;
      }
    }
  }

  grouping ti-scenario {
    description
      "The scenario of ti worked.";
    list scenario {
      key "name";
      leaf name {
        type string {
          length "1..127";
        }
      }
      leaf enabled {
        type boolean;
        default true;
      }
    }
  }

  grouping threat-intelligence-management {
    description
      "The threat-intelligence configuration page.";
    leaf enable-status {
      type boolean;
      default false;
      description
        "The status of threat-intelligence enable or not.";
    }
    leaf enable-ai {
      type boolean;
      default false;
      description
        "Enable machine learning enhancement.";
    }
    container scenarios {
      description
        "Threat intelligence work scenarios.";
      uses ti-scenario;
    }
    container security-zone {
      description
        "Security zone selection.";
      choice what {
        default auto;
        case auto {
          leaf auto {
            type boolean;
            default true;
            description
              "Automatically recognition security zone.";
          }
        }
        case select-zone {
          leaf-list zone {
            type string;
            description
              "Select security zone to protect.";
          }
        }
      }
    }
    container ti-source-group {
      description
        "List of source.";
      uses ti-sources;
    }
  }

  grouping ti-detail {
    description
      "The user-defined ti information.";
    list ti-groups {
      key "name";
      leaf name {
        type string {
          length "1..127";
        }
      }
      leaf capability {
        type scenario-capability;
      }
      leaf number {
        type uint32; 
      }
      leaf enable {
        type boolean;
        default true;
      }
    }
  }

  grouping vrf {
    leaf vrf {
      description
        "VRF name.";

      type string;
      default "main";
    }
  }

  grouping ti-srcs-type-map {
    description
      "Mutile source type map.";
    list ti-srcs {
      key "src-name";
      leaf src-name {
        type string {
          length "1..127";
        }
      }
      leaf src-id {
        type uint32;
      }
      list src-type-map {
        key "type-name";
        leaf type-name {
          type string;
        }
        leaf type-id {
          type uint32;
        }
      }
    }
  }

  grouping ti-src-search-req {
    description
      "Search ip or domain in which source.";
    leaf search-type {
      type search-action-type;
      description
        "Search type single search or file search.";
    }
    leaf search-data {
      type string {
        length "1..255";
      }
      description
        "Search data, url,ip or filepath.";
    }
  }

  grouping ti-src-description-list {
    description
      "TI sources description list.";
    list src-description {
      key "src-id";
      leaf src-id {
        type uint32;
      }
      leaf description-str {
        type string;
      }
    }
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "The configuration of threat-intelligence.";
    container threat-intelligence {
      container management {
        uses threat-intelligence-management;
      }
      container custom {
        uses ti-detail;
      }
      container exception {
        uses ti-detail;
      }
    }
  }

  rpc threat-intelligence-management-show {
    description
      "Get ti management configuration";
    input {
      uses vrf;
    }
    output {
      container threat-intelligence {
        uses threat-intelligence-management;
      }
    }
    ntos-ext:nc-cli-show "ti management";
  }

  rpc threat-intelligence-config-show {
    description
      "Get ti configuration.";
    input {
      uses vrf;
      leaf type {
        type ti-type;
      }
      container all {
        leaf start {
          type uint32;
        }
        leaf end {
          type uint32;
        }
      }
    }
    output {
      container threat-intelligence {
        uses ti-detail;
      }
      leaf total {
        type uint32;
      }
      leaf url-total {
        type uint32;
      }
      leaf ip-total {
        type uint32;
      }
    }
    ntos-ext:nc-cli-show "ti config";
  }

  rpc threat-intelligence-config-set {
    description
      "Threat-intelligence, set from Stream.";
    input {
      uses vrf;
      leaf name {
        type string {
          length "1..127";
        }
      }
      leaf rules {
        type string;
      }
      leaf type {
        type ti-type;
      }
      leaf capability {
        type scenario-capability;
      }
      leaf clear {
        type boolean;
      }
      leaf create {
        type boolean;
      }
      leaf session-id {
        type string {
          length "1..127";
        }
      }
    }
    output {
      container threat-intelligence {
        leaf status {
          type uint32;
        }
      }
    }
    ntos-ext:nc-cli-cmd "ti config set";
  }

  rpc threat-intelligence-config-set-result {
    description
      "Threat-intelligence, set rules result.";
    input {
      uses vrf;
      leaf session-id {
        type string {
          length "1..32";
        }
      }
    }
    output {
      container threat-intelligence {
        leaf status {
          type uint32;
        }
      }
    }
    ntos-ext:nc-cli-cmd "ti config set result";
  }

  rpc threat-intelligence-search-ip-url {
    description
      "Threat-intelligence, search from all ti rule group.";
    input {
      uses vrf;
      leaf rule {
        type string;
      }
      leaf type {
        type ti-type;
      }
    }
    output {
      container threat-intelligence {
        list ti-groups {
          key "group";
          leaf group {
            type string;
          }
          leaf enable {
            type boolean;
            default true;
          }
          leaf line {
            type uint32;
          }
          leaf number {
            type uint32;
          }
        }
      }
    }
    ntos-ext:nc-cli-cmd "ti config search";
  }

  rpc threat-intelligence-del-rule {
    description
      "Threat-intelligence, delete one rule from group.";
    input {
      uses vrf;
      leaf group {
        type string;
      }
      leaf rule {
        type string;
      }
      leaf type {
        type ti-type;
      }
    }
    output {
      container threat-intelligence {
        leaf status {
          type uint32;
        }
      }
    }
    ntos-ext:nc-cli-cmd "ti config del";
  }

  rpc threat-intelligence-config-add-exception-custom {
    description
      "Threat-intelligence, add one exception or custom.";
    input {
      uses vrf;
      leaf type {
        type ti-type;
      }
      leaf capability {
        type scenario-capability;
      }
      leaf name {
        type string;
      }
      leaf rule {
        type string;
      }
    }
    output {
      container threat-intelligence {
        leaf status {
          type string;
        }
      }
    }
    ntos-ext:nc-cli-cmd "ti config add";
  }

  rpc threat-intelligence-config-import {
    description
      "Threat-intelligence, import from file.";
    input {
      uses vrf;
      leaf path {
        type string;
      }
      leaf type {
        type ti-type;
      }
      leaf capability {
        type scenario-capability;
      }
      leaf session-id {
        type string {
          length "1..32";
        }
      }
    }
    output {
      container threat-intelligence {
        leaf status {
          type uint32;
        }
      }
    }
    ntos-ext:nc-cli-cmd "ti config import";
  }

  rpc threat-intelligence-config-export {
    description
      "Threat-intelligence, import from file.";
    input {
      uses vrf;
      leaf path {
        type string;
      }
      leaf type {
        type ti-type;
      }
      leaf session-id {
        type string {
          length "1..32";
        }
      }
    }
    output {
      container threat-intelligence {
        leaf status {
          type uint32;
        }
      }
    }
    ntos-ext:nc-cli-cmd "ti config export";
  }

  rpc threat-intelligence-config-import-export-result {
    description
      "Threat-intelligence, import and export result.";
    input {
      uses vrf;
      leaf session-id {
        type string {
          length "1..32";
        }
      }
    }
    output {
      container threat-intelligence {
        leaf status {
          type uint32;
        }
      }
    }
    ntos-ext:nc-cli-cmd "ti config import-export result";
  }

  rpc threat-intelligence-signature-update {
    description
      "Update threat-intelligence signature.";
    input {
      leaf sig-type {
        type string;
        description
          "Type of signature information lib.";
      }
      leaf package-path {
        type string;
        description
          "The location of the upgrade package.";
      }
    }
    output {
      leaf id {
        type uint32;
        description
          "Id of tracing upgrade.";
      }
      leaf progress {
        type uint32;
        description
          "Progress of upgrade.";
      }
      leaf errnum {
        type uint32;
        description
          "Error code.";
      }
    }
    ntos-ext:nc-cli-cmd "ti signature-update";
  }

  rpc threat-intelligence-signature-version-show {
    description
      "Show threat-intelligence signature version.";
    input {
      leaf sig-type {
        type string;
        description
          "Type of signature information lib.";
      }
    }

    output {
      container threat-intelligence {
        leaf version {
          type string;
            description
              "The version of the signature.";
        }
      }
    }
    ntos-ext:nc-cli-show "ti signature-version";
  }

  rpc threat-intelligence-signature-update-status-get {
    description
      "Get threat-intelligence signature update status.";
    input {
      leaf sig-type {
        type string;
        description
          "Type of signature information lib.";
      }
    }

    output {
      leaf errnum {
        type uint32;
        description
          "Error code.";
      }
    }
    ntos-ext:nc-cli-cmd "ti signature-update status";
  }

  rpc threat-intelligence-sdk-status-get {
    description
      "Get threat-intelligence sdk status.";
    input {
      leaf sdk-type {
        type string;
        description
          "The type of sdk.";
      }
    }

    output {
      container threat-intelligence {
        leaf status {
          type uint32;
          description
            "sdk status.";
        }
      }
    }
    ntos-ext:nc-cli-cmd "ti sdk status";
  }

  rpc threat-intelligence-srcs-type-map-get {
    description
      "Get threat-intelligence sources type map.";
    input {
      uses vrf;
    }
    output {
      container threat-intelligence {
        uses ti-srcs-type-map;
      }
    }
    ntos-ext:nc-cli-cmd "ti get-srcs";
  }
  
  rpc threat-intelligence-domain-ip-search {
    description
      "Search ip or domain in which source.";
    input {
      uses ti-src-search-req;
    }
    output {
      container threat-intelligence {
        leaf status {
          type uint32;
        }
      }
    }
    ntos-ext:nc-cli-cmd "ti info-search";
  }

  rpc threat-intelligence-get-search-status {
    description
      "Get search ip or domain  command status.";
    output {
      container threat-intelligence {
        leaf status {
          type uint32;
        }
      }
    }
    ntos-ext:nc-cli-cmd "ti info-search-status";
  }

  rpc threat-intelligence-get-show-info {
    description
      "Get fpcmd show information.";
    output {
      container threat-intelligence {
        leaf result {
          type string;
        }
      }
    }
    ntos-ext:nc-cli-cmd "ti fpcmd show";
  }

  rpc threat-intelligence-get-src-description {
    description
      "Search ip or domain in which source.";
    input {
      uses vrf;
    }
    output {
      container threat-intelligence {
        uses ti-src-description-list;
      }
    }
    ntos-ext:nc-cli-cmd "ti get-description";
  }
}
