module ntos-veth {
  namespace "urn:ruijie:ntos:params:xml:ns:yang:veth";
  prefix ntos-veth;

  import ntos {
    prefix ntos;
  }
  import ntos-if-types {
    prefix ntos-if;
  }
  import ntos-ip {
    prefix ntos-ip;
  }
  import ntos-interface {
    prefix ntos-interface;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-extensions {
    prefix ntos-extensions;
  }
  import ntos-qos {
    prefix ntos-qos;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS veth interfaces.";

  revision 2019-04-05 {
    description
      "Add qos context.";
    reference "";
  }
  revision 2018-12-11 {
    description
      "Initial version.";
    reference "";
  }

  identity veth {
    base ntos-types:INTERFACE_TYPE;
    description
      "Veth interface.";
  }

  grouping veth-config {
    description
      "Veth configuration container.";

    leaf link-interface {
      type leafref {
        path
          "/ntos:config/ntos:vrf/ntos-interface:interface/ntos-veth:veth/ntos-veth:name";
      }
      must '. != ../name or ../link-vrf != ../../../ntos:name' {
        error-message "Cannot bind our own interface";
      }
      mandatory true;
      description
        "The other endpoint of the Veth pair.";
    }

    leaf link-vrf {
      type string;
      mandatory true;
      description
        "The link vrf name.";
      ntos-extensions:nc-cli-completion-xpath
        "/ntos:config/ntos:vrf/ntos:name";
    }
  }

  augment "/ntos:config/ntos:vrf/ntos-interface:interface" {
    description
      "Network veth configuration.";

    list veth {
      must "count(ntos-veth:link-interface) = 0 or
            count(ntos-veth:link-vrf) = 0 or
            count(/ntos:config/ntos:vrf[ntos:name=current()/ntos-veth:link-vrf]/
                  ntos-interface:interface/ntos-veth:veth
                    [ntos-veth:name=current()/ntos-veth:link-interface]) != 0" {
        error-message "Veth endpoint does not exist.";
      }
      must "count(ntos-veth:link-interface) = 0 or
            count(ntos-veth:link-vrf) = 0 or
            count(/ntos:config/ntos:vrf[ntos:name=current()/ntos-veth:link-vrf]/
                  ntos-interface:interface/ntos-veth:veth
                    [ntos-veth:name=current()/ntos-veth:link-interface]
                    [ntos-veth:link-interface=current()/ntos-veth:name]
                    [ntos-veth:link-vrf=current()/../../ntos:name]) != 0" {
        error-message "Veth endpoints should bind each other.";
      }
      key "name";
      description
        "The list of veth interfaces on the device.";
      uses ntos-interface:nonphy-interface-config {
        refine name {
          must 'not(starts-with(string(.),"xvrf"))' {
            error-message "interface name xvrf* are reserved.";
          }
        }
      }
      uses ntos-ip:ntos-ipv4-config;
      uses ntos-ip:ntos-ipv6-config;
      uses ntos-ip:ntos-network-stack-parameters;
      uses ntos-interface:eth-config;
      uses veth-config;
      uses ntos-qos:logical-if-qos-config;
      ntos-extensions:data-not-sync;
    }
  }

  augment "/ntos:state/ntos:vrf/ntos-interface:interface" {
    description
      "Network veth operational state data.";

    list veth {
      key "name";
      description
        "The list of veth interfaces on the device.";
      ntos-extensions:feature "product";
      uses ntos-interface:interface-state;
      uses ntos-ip:ntos-ipv4-state;
      uses ntos-ip:ntos-ipv6-state;
      uses ntos-ip:ntos-network-stack-parameters;
      uses ntos-if:interface-common-state;
      uses ntos-if:interface-counters-state;
      uses ntos-interface:eth-state;
      uses veth-config;
      uses ntos-qos:logical-if-qos-state;
    }
  }
}
