module ntos-vlan {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:vlan";
  prefix ntos-vlan;

  import extra-conditions {
    prefix ext-cond;
  }
  import ntos {
    prefix ntos;
  }
  import ntos-extensions {
    prefix ntos-extensions;
  }
  import ntos-if-types {
    prefix ntos-if;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-ip {
    prefix ntos-ip;
  }
  import ntos-interface {
    prefix ntos-interface;
  }
  import ntos-qos {
    prefix ntos-qos;
  }
  import ntos-routing {
    prefix ntos-routing;
  }
  import ntos-dhcp-snooping {
    prefix ntos-dhcp-snp;
  }
  import ntos-pppoe-server {
    prefix ntos-pppoe-server;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS VLAN interfaces.";

  revision 2021-11-07 {
    description
      "Change vlan id range to [1-4063].";
    reference "";
  }

  revision 2018-10-30 {
    description
      "Initial version.";
    reference "";
  }

  identity vlan {
    base ntos-types:INTERFACE_TYPE;
    description
      "VLAN interface.";
  }

  typedef vlan-id {
    type uint16 {
      range "1..4063";
    }
    description
      "Type definition representing a single-tagged VLAN.";
  }

  typedef protocol {
    type enumeration {
      enum 802.1q {
        description
          "VLAN protocol.";
      }
      enum 802.1ad {
        description
          "QinQ protocol.";
      }
    }
    description
      "The available VLAN protocols.";
  }

  grouping vlan-config {
    description
      "VLAN configuration container.";

    leaf vlan-id {
      type vlan-id;
      mandatory true;
      description
        "Interface VLAN id.";
    }

    leaf link-interface {
      type ntos-types:ifname;
      must '. != ../name or ../link-vrf != ../../../ntos:name' {
        error-message "Cannot bind our own interface";
      }
      mandatory true;
      description
        "Create the VLAN on top of this interface.";
      /* interfaces from config and state in the selected vrf */
      ntos-extensions:nc-cli-completion-xpath
        "/ntos:config/vrf[ntos:name=string(
           /ntos:config/vrf[ntos:name=string(current()/ntos-vlan:link-vrf)]/ntos:name |
           /ntos:config/vrf[ntos:name=string(
             current()/../../*[local-name()='name']
           )][count(current()/ntos-vlan:link-vrf)=0]/ntos:name
         )]/ntos-interface:interface/*[.!=current()]/*[local-name()='ethernet']/../*[local-name()='name'] |
         /ntos:state/vrf[ntos:name=string(
           /ntos:config/vrf[ntos:name=string(current()/ntos-vlan:link-vrf)]/ntos:name |
           /ntos:config/vrf[ntos:name=string(
             current()/../../*[local-name()='name']
           )][count(current()/ntos-vlan:link-vrf)=0]/ntos:name
         )]/ntos-interface:interface/*[.!=current()]/*[local-name()='ethernet']/../*[local-name()='name']";
    }

    leaf protocol {
      type protocol;
      default "802.1q";
      description
        "The VLAN protocol to use.";
    }

    leaf link-vrf {
      type string;
      must '. != ../../../ntos:name' {
        error-message "link-vrf must reference another vrf.";
      }
      description
        "The link vrf name.";
      ntos-extensions:nc-cli-completion-xpath
        "/ntos:config/ntos:vrf/ntos:name";
    }

    uses ntos-interface:ha-group-config;
  }

  augment "/ntos:config/ntos:vrf/ntos-interface:interface" {
    description
      "Network vlan configuration.";

    list vlan {
      key "name";
      description
        "The list of VLAN interfaces on the device.";
      ext-cond:unique-tuple "link-interface vlan-id link-vrf" {
        error-message "Cannot bind the same tuple (link-interface, vlan-id, link-vrf).";
      }
      uses ntos-interface:phy-interface-config;
      uses ntos-interface:eth-config;
      uses ntos-ip:ntos-ipv4-config;
      uses ntos-ip:ntos-ipv6-config;
      uses ntos-ip:ntos-network-stack-parameters;
      uses ntos-routing:reverse-path;
      uses ntos-interface:network-access-attr;
      uses vlan-config;
      uses ntos-qos:logical-if-qos-config;
      uses ntos-dhcp-snp:dhcp-snp-parameters;
      uses ntos-pppoe-server:pppoe-server-config;
    }
  }

  augment "/ntos:state/ntos:vrf/ntos-interface:interface" {
    description
      "Network vlan operational state data.";

    list vlan {
      key "name";
      description
        "The list of VLAN interfaces on the device.";
      uses ntos-interface:interface-state;
      uses ntos-interface:eth-state;
      uses ntos-ip:ntos-ipv4-state;
      uses ntos-ip:ntos-ipv6-state;
      uses ntos-ip:ntos-network-stack-parameters;
      uses vlan-config;
      uses ntos-if:interface-common-state;
      uses ntos-if:interface-counters-state;
      uses ntos-qos:logical-if-qos-state;
      uses ntos-dhcp-snp:dhcp-snp-parameters;
    }
  }
}
