module ntos-vxlan {
  namespace "urn:ruijie:ntos:params:xml:ns:yang:vxlan";
  prefix ntos-vxlan;

  import extra-conditions {
    prefix ext-cond;
  }
  import ntos {
    prefix ntos;
  }
  import ntos-ip {
    prefix ntos-ip;
  }
  import ntos-if-types {
    prefix ntos-if;
  }
  import ntos-inet-types {
    prefix ntos-inet-types;
  }
  import ntos-interface {
    prefix ntos-interface;
  }
  import ntos-tunnel {
    prefix ntos-tunnel;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-extensions {
    prefix ext;
  }
  import ntos-qos {
    prefix ntos-qos;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS VxLAN tunnel interfaces.";

  revision 2018-10-15 {
    description
      "Initial version.";
    reference "";
  }

  identity vxlan {
    base ntos-types:INTERFACE_TYPE;
    description
      "VxLAN interface.";
  }

  typedef vni {
    type uint32 {
      range "0..16777215";
    }
    description
      "Type definition representing VXLAN Segment ID / VXLAN Network Identifier value.";
  }

  grouping vxlan-config {
    description
      "VxLAN configuration container.";

    leaf vni {
      type vni;
      mandatory true;
      description
        "Interface VXLAN Network ID. This ID must be unique.";
    }

    leaf group {
      type ntos-inet-types:ip-multicast-address;
      must 'count(../link-interface) = 1' {
        error-message "link-interface required if group is set.";
      }
      description
        "The group multicast IP address.";
    }
    // Unicast configuration can be set by adding the corresponding fdb rule
    // Direct configuration by setting the remote tunnel information through
    // the cli (configuration of remote and group are exclusive and can be
    // confusing)

    leaf local {
      type ntos-inet-types:ip-address;
      must 'count(../group) = 0 or contains(., ":") = contains(../group, ":")' {
        error-message "Ip version of local and group must be the same";
      }
      description
        "The source address that should be used for the Vxlan tunnel. If none
         is specified an address of the link interface will be used.";
    }
    uses ntos-tunnel:tunnel-common-config;

    leaf learning {
      type boolean;
      default "true";
      description
        "Enable the registration of unknown source link layer addresses
         and IP addresses into the VxLAN forwarding database.";
    }

    leaf gbp {
      type boolean;
      default "false";
      description
        "Enable the Group Policy extension.";
    }

    leaf dst {
      type uint16;
      default "4789";
      description
        "UDP destination port.";
    }

    container src-range {
      must 'min <= max' {
        error-message "src-range value must be ordered.";
      }
      description
        "Range of UDP source ports.";

      leaf min {
        type uint16;
        default "49152";
        description
          "Minimal value of source port range.";
        ext:nc-cli-no-name;
      }

      leaf max {
        type uint16;
        default "65535";
        description
          "Maximal value of source port range.";
        ext:nc-cli-no-name;
      }
    }
  }

  augment "/ntos:config/ntos:vrf/ntos-interface:interface" {
    description
      "Network vxlan configuration.";

    list vxlan {
      key "name";
      description
        "The list of VxLAN interfaces on the device.";
      ext:feature "product";
      ext-cond:unique-tuple "vni link-vrf" {
        error-message "The (vni, link-vrf) tuple must be unique.";
      }
      uses ntos-interface:nonphy-interface-config;
      uses ntos-interface:eth-config;
      uses ntos-ip:ntos-ipv4-config;
      uses ntos-ip:ntos-ipv6-config;
      uses ntos-ip:ntos-network-stack-parameters;
      uses vxlan-config;
      uses ntos-qos:logical-if-qos-config;
      ext:data-not-sync;
    }
  }

  augment "/ntos:state/ntos:vrf/ntos-interface:interface" {
    description
      "Network vxlan operational state data.";

    list vxlan {
      key "name";
      description
        "The list of VxLAN interfaces on the device.";
      ext:feature "product";
      uses ntos-interface:interface-state;
      uses ntos-interface:eth-state;
      uses ntos-ip:ntos-ipv4-state;
      uses ntos-ip:ntos-ipv6-state;
      uses ntos-ip:ntos-network-stack-parameters;
      uses vxlan-config;
      uses ntos-if:interface-common-state;
      uses ntos-if:interface-counters-state;
      uses ntos-qos:logical-if-qos-state;
    }
  }
}
