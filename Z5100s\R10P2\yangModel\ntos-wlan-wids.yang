module ntos-wlan-wids {
  yang-version 1.1;

  namespace "urn:ruijie:ntos:params:xml:ns:yang:wlan-wids";

  prefix ntos-wlan-wids;

  import ntos-types {
    prefix ntos-types;
  }

  import ntos-extensions {
    prefix ntos-ext;
  }

  import ntos-if-types {
    prefix ntos-if;
  }

  import ntos-api {
    prefix ntos-api;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel: 4008-111-000
    E-mail: <EMAIL>";
  description
    "Ruijie NTOS WLAN wids module.";

  revision 2024-11-05 {
    description
      "Initial revision.";
    reference
      "";
  }

  grouping wids-countermeasures {
    container countermeasures {
      leaf enabled {
        type boolean;
        default false;
        description
          "Whether to enable device countermeasures";
      }

      leaf-list mode {
        type enumeration {
          enum adhoc;
          enum config;
          enum rogue;
          enum ssid;
        }
      }

      leaf rssi-min {
        type uint32;
        default 25;
        description
          "Lower limit of counter signal strength";
      }

      leaf channel-match {
        type boolean;
        default false;
        description
          "Whether to enable channel-based countermeasures";
      }
    }
  }

  grouping wids-device {
    container device {
      container attack {
        leaf-list mac-address {
          description
            "Attack mac address.";
          type ntos-if:mac-address;
        }
      }

      container permit {
        leaf-list mac-address {
          description
            "permit mac.";
          type ntos-if:mac-address;
        }

        leaf-list ssid {
          description
            "permit ssid.";
          type string;
        }

        container vendor {
          leaf-list bssid {
            description
              "permit vendor.";
            type ntos-if:mac-address;
          }
        }
      }

      container black-ssid {
        leaf-list ssid {
          description
            "Black ssid.";
          type string;
        }
      }
    }
  }

  grouping wids-user-isolation {
    container user-isolation {
      leaf-list user-type {
        type enumeration {
          enum ap;
          enum ssid-ap;
        }
      }
      leaf-list permit-mac {
        description
          "permit mac.";
        type ntos-if:mac-address;
      }
    }
  }

  grouping wids-configure {
    container wids {
      description
        "wids configure.";

      uses wids-device;
      uses wids-countermeasures;
      uses wids-user-isolation;
    }
  }

  grouping wids-cmd {
    container wids {
      description
        "wids cmd.";
      container reset {
        description
          "reset wids configure.";
        container detected  {
          leaf all {
            type empty;
          }
        }
      }
    }
  }

  grouping wids-ap-config {
    container device {
      leaf mode {
        type enumeration {
          enum hybrid;
          enum monitor;
          enum normal;
        }
      }

      leaf radio {
        when "../mode = 'monitor'";
        type uint32;
        description
          "wids device radio id.";
      }
    }

    list scan-channels {
      key "type";
      leaf type {
        type enumeration {
          enum 802.11a;
          enum 802.11b;
        }
      }
      leaf-list channels {
        type uint32;
        description
          "wids channels num.";
      }
    }
  }

  grouping show-wids-config-input {
    container wids {
      container content {
        leaf type {
          description
            "Show type of wlan.";
          type enumeration {
            enum config;
            enum attack-list;
            enum permitted;
            enum backlist;
            enum whitelist;
            enum user-isolation;
            enum detecte;
          }
        }
        leaf start {
          type uint32;
          description
            "Start offset of result.";
        }

        leaf end {
          type uint32;
          description
            "End offset of result.";
        }

        leaf filter {
          type string;
        }

        leaf permit-mac {
          type empty;
          when "../type = 'user-isolation'";
        }

        leaf permitted-type {
          when "../type = 'permitted'";
          description
            "Show the admissible MAC, admissible SSID, and admissible verdor table entry configurations of WIDS.";
          type enumeration {
            enum mac-address {
              description
                "Indicates the information about allowed MAC address entries.";
            }
            enum vendor {
              description
                "Indicates the allowed verdor table entry information.";
            }
            enum ssid {
              description
                "Indicates the allowed SSID entry information.";
            }
          }
        }

        leaf backlist-type {
          when "../type = 'backlist'";
          description
            "Show the dynamic/static blacklist entries of WIDS.";
          type enumeration {
            enum dynamic {
              description
                "Indicates show information about dynamic blacklist entries.";
            }
            enum static {
              description
                "Indicates show information about static blacklist entries.";
            }
          }
        }

        container detecte-type {
          when "../type = 'detecte'";
          container interfer {
            leaf ap {
              description
                "Indicates that the detected interference ap is displayed.";
              type empty;
            }
          }

          leaf rogue {
            type enumeration {
              enum ap {
                description
                  "Indicates that devices matching the countermeasures mode rogue have been detected.";
              }
              enum ssid-ap {
                description
                  "Indicates the detected devices that match the ssid of the countermeasure mode.";
              }
              enum adhoc-ap {
                description
                  "Indicates that a device matching the countermeasure mode adhoc has been detected.";
              }
              enum config-ap {
                description
                  "Indicates the detected devices that match the countermeasure mode config.";
              }
            }
          }
        }
      }
    }
  }

  grouping show-wids-config-output {
    leaf num {
      type uint32;
    }
    leaf-list attack-list-info {
      type string;
    }
    leaf-list permitted-info {
      type string;
    }
    leaf-list static-backlist {
      type string;
    }
    leaf-list whitelist {
      type string;
    }
    leaf-list user-isolation-permit-mac {
      type string;
    }
    list detecte-config {
      key "bssid";
      leaf ssid {
        type string;
      }
      leaf bssid {
        type string;
      }
      leaf chan {
        type string;
      }
      leaf rate {
        type string;
      }
      leaf sn {
        type string;
      }
    }
    uses wids-countermeasures;
    uses wids-device;
    uses wids-user-isolation;
  }
}