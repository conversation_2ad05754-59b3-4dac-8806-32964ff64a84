module ntos-firewall-modules {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:firewall-modules";
  prefix ntos-firewall-modules;

  import ntos-api {
    prefix ntos-api;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos-firewall-types {
    prefix ntos-fwt;
  }
  import ntos-extensions {
    prefix ntos-extensions;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS firewall modules.";

  revision 2018-10-03 {
    description
      "Initial version.";
    reference "";
  }

  typedef standard-actions {
    type enumeration {
      enum accept {
        description
          "Let the packet through.";
      }
      enum drop {
        description
          "Drop the packet.";
      }
      enum return {
        description
          "Stop traversing this chain and resume at the next rule in the parent chain. For built-ins, go through the policy.";
      }
    }
    description
      "Standard actions.";
  }

  grouping dest6-match {
    description
      "Destination match.";

    container destination {
      presence "makes destination available";
      description
        "Match on destination fields.";

      container address {
        presence "makes address available";
        description
          "Match on destination address.";

        leaf not {
          type empty;
          description
            "Invert the match.";
          ntos-extensions:nc-cli-order "1";
        }

        leaf value {
          type ntos-fwt:address6-types;
          mandatory true;
          description
            "The address to match.";
          ntos-extensions:nc-cli-no-name;
        }
      }

      container port {
        when "../../protocol/value='tcp' or ../../protocol/value='udp' or ../../protocol/value='sctp'";
        presence "makes port available";
        description
          "Match on destination port.";

        leaf not {
          type empty;
          description
            "Invert the match.";
          ntos-extensions:nc-cli-order "1";
        }

        leaf value {
          type ntos-inet:port-number;
          mandatory true;
          description
            "The port to match.";
          ntos-extensions:nc-cli-no-name;
        }
      }

      container port-range {
        when "../../protocol/value='tcp' or ../../protocol/value='udp' or ../../protocol/value='sctp'";
        presence "makes port available";
        description
          "Match on destination port range (syntax: port[,port|,port-port]).";

        leaf not {
          type empty;
          description
            "Invert the match.";
          ntos-extensions:nc-cli-order "1";
        }

        leaf value {
          type ntos-fwt:port-range;
          mandatory true;
          description
            "Port range, syntax is port[,port|,port-port].";
          ntos-extensions:nc-cli-no-name;
        }
      }
      uses set-match;
    }
  }

  grouping source6-match {
    description
      "Source match.";

    container source {
      presence "makes source available";
      description
        "Match on source fields.";

      container address {
        presence "makes address available";
        description
          "Match on source address.";

        leaf not {
          type empty;
          description
            "Invert the match.";
          ntos-extensions:nc-cli-order "1";
        }

        leaf value {
          type ntos-fwt:address6-types;
          mandatory true;
          description
            "The address to match.";
          ntos-extensions:nc-cli-no-name;
        }
      }

      container port {
        when "../../protocol/value='tcp' or ../../protocol/value='udp' or ../../protocol/value='sctp'";
        presence "makes port available";
        description
          "Match on source port.";

        leaf not {
          type empty;
          description
            "Invert the match.";
          ntos-extensions:nc-cli-order "1";
        }

        leaf value {
          type ntos-inet:port-number;
          mandatory true;
          description
            "The port to match.";
          ntos-extensions:nc-cli-no-name;
        }
      }

      container port-range {
        when "../../protocol/value='tcp' or ../../protocol/value='udp' or ../../protocol/value='sctp'";
        presence "makes port available";
        description
          "Match on source port range (syntax: port[,port|,port-port]).";

        leaf not {
          type empty;
          description
            "Invert the match.";
          ntos-extensions:nc-cli-order "1";
        }

        leaf value {
          type ntos-fwt:port-range;
          mandatory true;
          description
            "Port range, syntax is port[,port|,port-port].";
          ntos-extensions:nc-cli-no-name;
        }
      }
      uses set-match;
    }
  }

  grouping icmp6-type-match {
    description
      "ICMP type match.";

    container icmpv6-type {
      when "../protocol/value='ipv6-icmp'";
      presence "makes icmp type available";
      description
        "Match the packet ICMP type.";

      leaf not {
        type empty;
        description
          "Invert the match.";
        ntos-extensions:nc-cli-order "1";
      }

      leaf value {
        type ntos-fwt:icmp6-types;
        mandatory true;
        description
          "The ICMP type to match.";
        ntos-extensions:nc-cli-no-name;
      }
    }
  }

  grouping reject6-action {
    description
      "Reject action.";

    leaf reject {
      type ntos-fwt:reject6-type;
      description
        "Used to send back an error packet in response to the matched packet.";
    }
  }

  grouping protocol-match {
    description
      "Protocol match.";

    container protocol {
      presence "makes protocol available";
      description
        "Match the protocol.";

      leaf not {
        type empty;
        description
          "Invert the match.";
        ntos-extensions:nc-cli-order "1";
      }

      leaf value {
        type ntos-fwt:protocol-types;
        mandatory true;
        description
          "The protocol to match.";
        ntos-extensions:nc-cli-no-name;
      }
    }
  }

  grouping protocol6-match {
    description
      "Protocol match.";

    container protocol {
      presence "makes protocol available";
      description
        "Match the protocol.";

      leaf not {
        type empty;
        description
          "Invert the match.";
        ntos-extensions:nc-cli-order "1";
      }

      leaf value {
        type ntos-fwt:protocol6-types;
        mandatory true;
        description
          "The protocol to match.";
        ntos-extensions:nc-cli-no-name;
      }
    }
  }

  grouping fragment-match {
    description
      "Fragment match.";

    container ipv4 {
      presence "makes ipv4 available";
      description
        "Match the fragment.";

      leaf not {
        type empty;
        description
          "Invert the match.";
        ntos-extensions:nc-cli-order "1";
      }

      leaf fragment {
        type empty;
        mandatory true;
        description
          "Match if the packet is a fragment.";
      }
    }
  }

  grouping outbound-interface-match {
    description
      "Outbound interface match.";

    container outbound-interface {
      presence "makes interface available";
      description
        "Name of an interface via which a packet is going to be sent. Only for forward, output and postrouting.";

      leaf not {
        type empty;
        description
          "Invert the match.";
        ntos-extensions:nc-cli-order "1";
      }

      leaf name {
        type string;
        mandatory true;
        description
          "The interface to match.";
        ntos-extensions:nc-cli-no-name;
        ntos-extensions:nc-cli-completion-xpath
          "../../../ntos-interface:interface/*/*[local-name()='name'] |
           ../../../../../../ntos-interface:interface/*/*[local-name()='name']";
      }
    }
  }

  grouping dest-match {
    description
      "Destination match.";

    container destination {
      presence "makes destination available";
      description
        "Match on destination fields.";

      container address {
        presence "makes address available";
        description
          "Match on destination address.";

        leaf not {
          type empty;
          description
            "Invert the match.";
          ntos-extensions:nc-cli-order "1";
        }

        leaf value {
          type ntos-fwt:address-types;
          mandatory true;
          description
            "The address to match.";
          ntos-extensions:nc-cli-no-name;
        }
      }

      container port {
        when "../../protocol/value='tcp' or ../../protocol/value='udp' or ../../protocol/value='sctp'";
        presence "makes port available";
        description
          "Match on destination port.";

        leaf not {
          type empty;
          description
            "Invert the match.";
          ntos-extensions:nc-cli-order "1";
        }

        leaf value {
          type ntos-inet:port-number;
          mandatory true;
          description
            "The port to match.";
          ntos-extensions:nc-cli-no-name;
        }
      }

      container port-range {
        when "../../protocol/value='tcp' or ../../protocol/value='udp' or ../../protocol/value='sctp'";
        presence "makes port available";
        description
          "Match on destination port range (syntax: port[,port|,port-port]).";

        leaf not {
          type empty;
          description
            "Invert the match.";
          ntos-extensions:nc-cli-order "1";
        }

        leaf value {
          type ntos-fwt:port-range;
          mandatory true;
          description
            "Port range, syntax is port[,port|,port-port].";
          ntos-extensions:nc-cli-no-name;
        }
      }
      uses set-match;
    }
  }

  grouping inbound-interface-match {
    description
      "Inbound interface match.";

    container inbound-interface {
      presence "makes interface available";
      description
        "Name of an interface via which a packet was received. Only for input, forward and prerouting.";

      leaf not {
        type empty;
        description
          "Invert the match.";
        ntos-extensions:nc-cli-order "1";
      }
      // XXX: could be an existing interface
      // 'wildcards'=eth+

      leaf name {
        type string;
        mandatory true;
        description
          "The interface to match.";
        ntos-extensions:nc-cli-no-name;
        ntos-extensions:nc-cli-completion-xpath
          "../../../ntos-interface:interface/*/*[local-name()='name'] |
           ../../../../../../ntos-interface:interface/*/*[local-name()='name']";
      }
    }
  }

  grouping source-match {
    description
      "Source match.";

    container source {
      presence "makes source available";
      description
        "Match on source fields.";

      container address {
        presence "makes address available";
        description
          "Match on source address.";

        leaf not {
          type empty;
          description
            "Invert the match.";
          ntos-extensions:nc-cli-order "1";
        }

        leaf value {
          type ntos-fwt:address-types;
          mandatory true;
          description
            "The address to match.";
          ntos-extensions:nc-cli-no-name;
        }
      }

      container port {
        when "../../protocol/value='tcp' or ../../protocol/value='udp' or ../../protocol/value='sctp'";
        presence "makes port available";
        description
          "Match on source port.";

        leaf not {
          type empty;
          description
            "Invert the match.";
          ntos-extensions:nc-cli-order "1";
        }

        leaf value {
          type ntos-inet:port-number;
          mandatory true;
          description
            "The port to match.";
          ntos-extensions:nc-cli-no-name;
        }
      }

      container port-range {
        when "../../protocol/value='tcp' or ../../protocol/value='udp' or ../../protocol/value='sctp'";
        presence "makes port available";
        description
          "Match on source port range (syntax: port[,port|,port-port]).";

        leaf not {
          type empty;
          description
            "Invert the match.";
          ntos-extensions:nc-cli-order "1";
        }

        leaf value {
          type ntos-fwt:port-range;
          mandatory true;
          description
            "Port range, syntax is port[,port|,port-port].";
          ntos-extensions:nc-cli-no-name;
        }
      }
      uses set-match;
    }
  }

  grouping dscp-action {
    description
      "DSCP action.";

    leaf dscp {
      type ntos-fwt:dscp-type;
      description
        "Alters the value of the DSCP bits within
         the tos header of the IPv4 packet.";
    }
  }

  grouping dscp-match {
    description
      "DSCP match.";

    container dscp {
      presence "makes dscp available";
      description
        "Match the DSCP.";

      leaf not {
        type empty;
        description
          "Invert the match.";
        ntos-extensions:nc-cli-order "1";
      }

      leaf value {
        type ntos-fwt:dscp-type;
        mandatory true;
        description
          "The DSCP value to match.";
        ntos-extensions:nc-cli-no-name;
      }
    }
  }

  grouping tos-action {
    description
      "TOS action.";

    container tos {
      presence "enable tos";
      description
        "Alters the value of the tos header of the IPv4 packet.";

      leaf value {
        type string {
          pattern '0x[0-9a-fA-F]{1,2}';
          ntos-extensions:nc-cli-shortdesc "<0x0-0xff>";
        }
        mandatory true;
        description
          "Bits that should be XORed into the tos.";
        ntos-extensions:nc-cli-no-name;
      }

      leaf mask {
        type string {
          pattern '0x[0-9a-fA-F]{1,2}';
          ntos-extensions:nc-cli-shortdesc "<0x0-0xff>";
        }
        description
          "Zero the bits given by this mask in the tos.";
      }
    }
  }

  grouping tos-match {
    description
      "TOS match.";

    container tos {
      presence "makes tos available";
      description
        "Match the tos.";

      leaf not {
        type empty;
        description
          "Invert the match.";
        ntos-extensions:nc-cli-order "1";
      }

      leaf value {
        type string {
          pattern '0x[0-9a-fA-F]{1,2}';
          ntos-extensions:nc-cli-shortdesc "<0x0-0xff>";
        }
        mandatory true;
        description
          "The tos value. Packets in connections are matched against this value.";
        ntos-extensions:nc-cli-no-name;
      }

      leaf mask {
        type string {
          pattern '0x[0-9a-fA-F]{1,2}';
          ntos-extensions:nc-cli-shortdesc "<0x0-0xff>";
        }
        description
          "Logically ANDed with the tos before the comparison.";
      }
    }
  }

  grouping icmp-type-match {
    description
      "ICMP type match.";

    container icmp-type {
      when "../protocol/value='icmp'";
      presence "makes icmp type available";
      description
        "Match the packet ICMP type.";

      leaf not {
        type empty;
        description
          "Invert the match.";
        ntos-extensions:nc-cli-order "1";
      }

      leaf value {
        type ntos-fwt:icmp-types;
        mandatory true;
        description
          "The ICMP type to match.";
        ntos-extensions:nc-cli-no-name;
      }
    }
  }

  grouping tcp-flags-match {
    description
      "Available TCP flags.";

    container tcp-flags {
      when "../protocol/value='tcp'";
      presence "makes tcp type available";
      description
        "Match the packet TCP flags.";

      leaf not {
        type empty;
        description
          "Invert the match.";
        ntos-extensions:nc-cli-order "1";
      }

      leaf-list set {
        type ntos-fwt:tcp-flags;
        min-elements 1;
        description
          "Set flags.";
      }

      leaf-list examined {
        type ntos-fwt:tcp-flags;
        description
          "Examined flags.";
      }
    }
  }

  grouping conntrack-match {
    description
      "Conntrack match.";

    container conntrack {
      presence "makes conntrack available";
      description
        "Match conntrack information.";

      container status {
        presence "makes status available";
        description
          "Match the connection status.";

        leaf not {
          type empty;
          description
            "Invert the match.";
          ntos-extensions:nc-cli-order "1";
        }

        leaf-list value {
          type ntos-fwt:ctstatus-types;
          min-elements 1;
          description
            "The conntrack status to match.";
          ntos-extensions:nc-cli-no-name;
        }
      }

      container state {
        presence "makes state available";
        description
          "Match the packet state regarding conntrack.";

        leaf not {
          type empty;
          description
            "Invert the match.";
          ntos-extensions:nc-cli-order "1";
        }

        leaf-list value {
          type ntos-fwt:ctstate-types;
          min-elements 1;
          description
            "The packet states to match.";
          ntos-extensions:nc-cli-no-name;
        }
      }
    }
  }

  grouping connmark-action {
    description
      "Connmark action.";

    container connmark {
      must 'count(*) = 1' {
        error-message "Choose between set-xmark, save-mark and restore-mark.";
      }
      presence "makes connmark available";
      description
        "Sets the mark value associated with a connection. The mark is 32 bits wide.";
      ntos-extensions:nc-cli-exclusive;

      container set-xmark {
        presence "makes set-xmark available";
        description
          "Zero out the bits given by mask and XOR value into the ctmark.";

        leaf value {
          type string {
            pattern '0x[0-9a-fA-F]{1,8}';
            ntos-extensions:nc-cli-shortdesc "<0x0-0xffffffff>";
          }
          mandatory true;
          description
            "XOR with this value.";
          ntos-extensions:nc-cli-no-name;
        }

        leaf mask {
          type string {
            pattern '0x[0-9a-fA-F]{1,8}';
            ntos-extensions:nc-cli-shortdesc "<0x0-0xffffffff>";
          }
          description
            "Zero the bits given by this mask.";
        }
      }

      container save-mark {
        presence "makes save-mark available";
        description
          "Copy the packet mark (nfmark) to the connection mark (ctmark) using
           the given masks. The new value is determined as follows:
           ctmark = (ctmark & ~ctmask) ^ (nfmark & nfmask)
           i.e. ctmask defines what bits to clear and nfmask what bits of the
           nfmark to XOR into the ctmark. ctmask and nfmask default to
           0xFFFFFFFF.";

        leaf nfmask {
          type string {
            pattern '0x[0-9a-fA-F]{1,8}';
            ntos-extensions:nc-cli-shortdesc "<0x0-0xffffffff>";
          }
          description
            "Bits that should be XORed into the connection mark.";
        }

        leaf ctmask {
          type string {
            pattern '0x[0-9a-fA-F]{1,8}';
            ntos-extensions:nc-cli-shortdesc "<0x0-0xffffffff>";
          }
          description
            "Bits that should be cleared.";
        }
      }

      container restore-mark {
        presence "makes restore-mark available";
        description
          "Copy the connection mark (ctmark) to the packet mark (nfmark) using
           the given masks. The new ctmark value is determined as follows:
           nfmark = (nfmark & ~nfmask) ^ (ctmark & ctmask)

           i.e. nfmask defines what bits to clear and ctmask what bits of the
           ctmark to XOR into the packet mark. ctmask and nfmask default to
           0xFFFFFFFF.

           restore-mark is only valid in the mangle table.";

        leaf nfmask {
          type string {
            pattern '0x[0-9a-fA-F]{1,8}';
            ntos-extensions:nc-cli-shortdesc "<0x0-0xffffffff>";
          }
          description
            "Bits that should be cleared.";
        }

        leaf ctmask {
          type string {
            pattern '0x[0-9a-fA-F]{1,8}';
            ntos-extensions:nc-cli-shortdesc "<0x0-0xffffffff>";
          }
          description
            "Bits that should be XORed into the packet mark.";
        }
      }
    }
  }

  grouping connmark-match {
    description
      "Connmark match.";

    container connmark {
      presence "makes connmark available";
      description
        "Matches the mark field associated with a connection.";

      leaf not {
        type empty;
        description
          "Invert the match.";
        ntos-extensions:nc-cli-order "1";
      }

      leaf value {
        type string {
          pattern '0x[0-9a-fA-F]{1,8}';
          ntos-extensions:nc-cli-shortdesc "<0x0-0xffffffff>";
        }
        mandatory true;
        description
          "The mark value. Packets in connections are matched against this value.";
        ntos-extensions:nc-cli-no-name;
      }

      leaf mask {
        type string {
          pattern '0x[0-9a-fA-F]{1,8}';
          ntos-extensions:nc-cli-shortdesc "<0x0-0xffffffff>";
        }
        description
          "Logically ANDed with the mark before the comparison.";
      }
    }
  }

  grouping log-action {
    description
      "Log action.";

    container log {
      presence "makes log available";
      description
        "Turn on logging of matching packets.";

      leaf level {
        type ntos-fwt:level-types;
        description
          "Level of logging.";
      }

      leaf prefix {
        type string {
          length "1..29";
        }
        description
          "Prefix log messages with the specified prefix, up to 29 letters long.";
      }

      leaf-list additional-infos {
        type ntos-fwt:additional-infos-types;
        description
          "Append additional informations to the logs.";
      }
    }
  }

  grouping limit-match {
    description
      "Limit match.";

    container limit {
      presence "makes limit available";
      description
        "Matches packets at a limited rate. If not set,
         the rate value is 3/hour and the burst value is 5.";

      container rate {
        presence "makes limit available";
        description
          "Matching rate, default unit is per hour.";

        leaf value {
          type uint32;
          mandatory true;
          description
            "The rate.";
          ntos-extensions:nc-cli-no-name;
        }

        leaf unit {
          type ntos-fwt:unit-types;
          description
            "Unit for rate.";
          ntos-extensions:nc-cli-no-name;
        }
      }

      leaf burst {
        type uint32;
        description
          "Maximum initial number of packets to match.
           This number gets recharged by one every time
           the rate is not reached, up to this number.";
      }
    }
  }

  grouping mark-match {
    description
      "Mark match.";

    container mark {
      presence "makes mark available";
      description
        "Matches the mark field associated with a packet.";

      leaf not {
        type empty;
        description
          "Invert the match.";
        ntos-extensions:nc-cli-order "1";
      }

      leaf value {
        type string {
          pattern '0x[0-9a-fA-F]{1,8}';
          ntos-extensions:nc-cli-shortdesc "<0x0-0xffffffff>";
        }
        mandatory true;
        description
          "The mark value. Packets in connections are matched against this value.";
        ntos-extensions:nc-cli-no-name;
      }

      leaf mask {
        type string {
          pattern '0x[0-9a-fA-F]{1,8}';
          ntos-extensions:nc-cli-shortdesc "<0x0-0xffffffff>";
        }
        description
          "Logically ANDed with the mark before the comparison.";
      }
    }
  }

  grouping rpfilter-match {
    description
      "Reverse path filter test.";

    container rpfilter {
      presence "makes rpfilter available";
      description
        "Performs a reverse path filter test on a packet. If a reply to the
         packet would be sent via the same interface that the packet arrived on,
         the packet will match.";

      leaf invert {
        type boolean;
        default "false";
        description
          "This will invert the sense of the match. Instead of matching packets
           that passed the reverse path filter test, match those that have
           failed it.";
      }
    }
  }

  grouping chunk-types {
    description
      "Available chunk types.";

    container data {
      presence "enable data";
      description
        "DATA chunk.";

      leaf-list examined {
        type ntos-fwt:sctp-data-flags;
        min-elements 1;
        description
          "Examined flags.";
      }

      leaf-list set {
        type ntos-fwt:sctp-data-flags;
        description
          "Set flags.";
      }
    }

    container abort {
      presence "enable abort";
      description
        "ABORT chunk.";

      leaf-list examined {
        type ntos-fwt:sctp-abort-flags;
        min-elements 1;
        description
          "Examined flags.";
      }

      leaf-list set {
        type ntos-fwt:sctp-abort-flags;
        description
          "Set flags.";
      }
    }

    container shutdown-complete {
      presence "enable shutdown_complete";
      description
        "SHUTDOWN COMPLETE chunk.";

      leaf-list examined {
        type ntos-fwt:sctp-abort-flags;
        min-elements 1;
        description
          "Examined flags.";
      }

      leaf-list set {
        type ntos-fwt:sctp-abort-flags;
        description
          "Set flags.";
      }
    }

    leaf init {
      type empty;
      description
        "INIT chunk.";
    }

    leaf init-ack {
      type empty;
      description
        "INIT ACK chunk.";
    }

    leaf sack {
      type empty;
      description
        "SACK chunk.";
    }

    leaf heartbeat {
      type empty;
      description
        "HEARTBEAT chunk.";
    }

    leaf heartbeat-ack {
      type empty;
      description
        "HEARTBEAT ACK chunk.";
    }

    leaf shutdown {
      type empty;
      description
        "SHUTDOWN chunk.";
    }

    leaf shutdown-ack {
      type empty;
      description
        "SHUTDOWN ACK chunk.";
    }

    leaf error {
      type empty;
      description
        "ERROR chunk.";
    }

    leaf cookie-echo {
      type empty;
      description
        "COOKIE ECHO chunk.";
    }

    leaf cookie-ack {
      type empty;
      description
        "COOKIE ACK chunk.";
    }

    leaf ecn-ecne {
      type empty;
      description
        "ECN ECNE chunk.";
    }

    leaf ecn-cwr {
      type empty;
      description
        "ECN CWR chunk.";
    }

    leaf asconf {
      type empty;
      description
        "ASCONF chunk.";
    }

    leaf asconf-ack {
      type empty;
      description
        "ASCONF ACK chunk.";
    }

    leaf forward-tsn {
      type empty;
      description
        "FORWARD TSN chunk.";
    }
  }

  grouping sctp-match {
    description
      "SCTP chunk type match.";

    container sctp-chunk-types {
      when "../protocol/value='sctp'";
      presence "makes chunk-types available";
      description
        "This module matches Stream Control Transmission Protocol headers.";

      leaf not {
        type empty;
        description
          "Invert the match.";
        ntos-extensions:nc-cli-order "1";
      }

      leaf scope {
        type enumeration {
          enum all {
            description
              "Match all chunk types.";
          }
          enum any {
            description
              "Match any chunk type.";
          }
          enum only {
            description
              "Match exactly chunk type.";
          }
        }
        mandatory true;
        description
          "Invert the match.";
        ntos-extensions:nc-cli-no-name;
        ntos-extensions:nc-cli-order "2";
      }
      uses chunk-types;
    }
  }

  grouping mark-action {
    description
      "Mark action.";

    container mark {
      presence "enable mark";
      description
        "Used to set the mark value associated with the packet.";

      leaf value {
        type string {
          pattern '0x[0-9a-fA-F]{1,8}';
          ntos-extensions:nc-cli-shortdesc "<0x0-0xffffffff>";
        }
        mandatory true;
        description
          "Bits that should be XORed into the packet mark.";
        ntos-extensions:nc-cli-no-name;
      }

      leaf mask {
        type string {
          pattern '0x[0-9a-fA-F]{1,8}';
          ntos-extensions:nc-cli-shortdesc "<0x0-0xffffffff>";
        }
        description
          "Zero the bits given by this mask in the packet mark.";
      }
    }
  }

  grouping notrack-action {
    description
      "Notrack action.";

    leaf notrack {
      type empty;
      description
        "Disables connection tracking for this packet.";
    }
  }

  grouping reject-action {
    description
      "Reject action.";

    leaf reject {
      type ntos-fwt:reject-type;
      description
        "Used to send back an error packet in response to the matched packet.";
    }
  }

  grouping tcpmss-action {
    description
      "TCPMSS action.";

    container tcpmss {
      must 'count(set-mss) + count(clamp-mss-to-pmtu) = 1';
      must
        "../../protocol/value='tcp' and ../../tcp-flags/examined[.='syn']
         and ../../tcp-flags/set[.='syn']";
      presence "enable tcpmss";
      description
        "Alters the MSS value of TCP SYN packets,
         to control the maximum size for that connection.";
      ntos-extensions:nc-cli-exclusive;

      leaf set-mss {
        type uint32;
        description
          "Explicitly sets MSS option to specified value.";
      }

      leaf clamp-mss-to-pmtu {
        type empty;
        description
          "Automatically clamp MSS value to (path_MTU - 40 for IPv4, - 60 for IPv6).";
      }
    }
  }

  grouping set-match {
    description
      "Administration tool for IP sets.";

    container group {
      presence "makes not available";
      description
        "Matches a set of addresses or networks.";

      leaf not {
        type empty;
        description
          "Not match-set.";
        ntos-extensions:nc-cli-order "1";
      }

      leaf value {
        type string;
        must '../../../../../../network-group/name = current() or ../../../../../../address-group/name = current()' {
          error-message "Group doesn't exist.";
        }
        mandatory true;
        description
          "The name of the group.";
        ntos-extensions:nc-cli-no-name;
        ntos-extensions:nc-cli-completion-xpath
          "../../../../../ntos-firewall:network-group/ntos-firewall:name |
           ../../../../../ntos-firewall:address-group/ntos-firewall:name |
           ../../../../../ntos-firewall6:network-group/ntos-firewall6:name |
           ../../../../../ntos-firewall6:address-group/ntos-firewall6:name";
        ntos-api:must-added "../../../../../../network-group/name = current() or ../../../../../../address-group/name = current()";
      }
    }
  }
}
