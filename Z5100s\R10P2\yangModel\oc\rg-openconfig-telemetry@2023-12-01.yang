module rg-openconfig-telemetry {
    namespace "urn:rg:params:xml:ns:yang:rg-openconfig-telemetry";
    prefix "rg-telemetry";

    import openconfig-telemetry { prefix "oc-tele"; }
    import openconfig-inet-types { prefix oc-inet; }

    organization
        "Ruijie Networks Co., Ltd";

    contact
        "Tel: 4008-111-000
        E-mail: <EMAIL>";

    description
        "Yang model for ruijie telemetry extend";

    revision 2023-12-01 {
        description "add tls";
    }

    revision 2023-09-14 {
        description "add subscr-source-interface";
    }

    revision 2023-07-26 {
        description "add vrf";
    }

    revision 2023-05-12 {
        description "add standby destination address";
    }

    feature set-tls {
        description
            "This feature indicates that this device supports set tls.";
    }

    feature telemetry-source-interface {
        description
            "This feature indicates that subscr-source-interface revision.";
    }

    grouping destination-group-vrf {
        leaf destination-address {
            type oc-inet:ip-address;
            description
                "Reference to the destination address of the telemetry stream";
        }
        leaf destination-port {
            type uint16;
            description
                "Reference to the port number of the stream destination";
        }
        leaf destination-vrf {
            type string;
            description
                "Reference to the vrf of the stream destination";
        }
    }

    grouping standby-destination-group-vrf {
        leaf standby-destination-address {
            type oc-inet:ip-address;
            description
                "Reference to the destination address of the telemetry stream";
        }
        leaf standby-destination-port {
            type uint16;
            description
                "Reference to the port number of the stream destination";
        }
        leaf standby-destination-vrf {
            type string;
            description
                "Reference to the vrf of the stream destination";
        }
    }

    augment "/oc-tele:telemetry-system/oc-tele:destination-groups/oc-tele:destination-group/oc-tele:destinations" {
        list destination-vrf {
            key "destination-address destination-port destination-vrf";
            description
                "Configure destination address with the vrf.";
            uses destination-group-vrf;
            list standby-destination-vrf {
                key "standby-destination-address standby-destination-port standby-destination-vrf";
                description
                    "Configure standby destination address with the vrf.";
                uses standby-destination-group-vrf;
            }
        }
    }

    augment "/oc-tele:telemetry-system/oc-tele:destination-groups/oc-tele:destination-group/oc-tele:destinations/oc-tele:destination" {
        container standby-destinations {
            description
                "Configure standby destination address with the destination.";
            list standby-destination {
                key "standby-destination-address standby-destination-port";
                description
                    "List of telemetry stream standby destinations";

                leaf standby-destination-address {
                    type oc-inet:ip-address;
                    description
                        "Reference to the standby destination address of the telemetry stream";
                }

                leaf standby-destination-port {
                    type uint16;
                    description
                        "Reference to the port number of the stream standby destination";
                }
            }
        }
    }

    augment "/oc-tele:telemetry-system" {
        container keepalive {
            leaf time {
                type uint32 {
                    range "1..300";
                }
                default 3;
                description "Specify destination address keepalive time";
            }
            leaf interval {
                type uint32 {
                    range "5..3600";
                }
                default 10;
                description "Specify destination address keepalive interval";
            }
        }

        leaf subscr-source-interface {
            if-feature telemetry-source-interface;
            type string;
            description "GRPC subscribe source interface.";
        }
    }

    augment "/oc-tele:telemetry-system/oc-tele:destination-groups/oc-tele:destination-group" {
		leaf tls {
		  if-feature set-tls;
		  type boolean;
		  default false;
		  description
			"protocol grpc tls";
		}
    }
}
