#!/usr/bin/env python3
"""
FortiGate到NTOS转换器access-control模块全面检测和验证
"""

import os
import re
from lxml import etree
from collections import defaultdict

class AccessControlAnalyzer:
    def __init__(self):
        self.fortigate_config = "Pass-Mask-MTU-FW-1_7-2_1639_202506271000.conf"
        self.ntos_xml = "output/fortigate-z5100s-R11-repaired.xml"
        self.yang_models_path = "engine/data/input/yang_models/z5100s/R11"
        
    def analyze_fortigate_access_control(self):
        """分析FortiGate配置中的access-control配置"""
        print("🔍 分析FortiGate配置中的access-control配置")
        print("=" * 60)
        
        if not os.path.exists(self.fortigate_config):
            print(f"❌ FortiGate配置文件不存在: {self.fortigate_config}")
            return None
        
        interfaces_with_access = {}
        current_interface = None
        
        try:
            with open(self.fortigate_config, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            in_interface_section = False
            
            for i, line in enumerate(lines):
                line = line.strip()
                
                # 检测接口配置开始
                if line == "config system interface":
                    in_interface_section = True
                    continue
                
                # 检测接口配置结束
                if in_interface_section and line == "end":
                    in_interface_section = False
                    continue
                
                if in_interface_section:
                    # 检测接口编辑开始
                    if line.startswith('edit "') and line.endswith('"'):
                        current_interface = line[6:-1]  # 提取接口名
                        interfaces_with_access[current_interface] = {
                            'allowaccess': [],
                            'ip': None,
                            'type': None,
                            'role': None,
                            'alias': None,
                            'vlanid': None,
                            'security_mode': None,
                            'line_number': i + 1
                        }
                    
                    # 检测allowaccess配置
                    elif line.startswith('set allowaccess ') and current_interface:
                        access_list = line[16:].split()
                        interfaces_with_access[current_interface]['allowaccess'] = access_list
                    
                    # 检测其他相关配置
                    elif line.startswith('set ip ') and current_interface:
                        interfaces_with_access[current_interface]['ip'] = line[7:]
                    elif line.startswith('set type ') and current_interface:
                        interfaces_with_access[current_interface]['type'] = line[9:]
                    elif line.startswith('set role ') and current_interface:
                        interfaces_with_access[current_interface]['role'] = line[9:]
                    elif line.startswith('set alias ') and current_interface:
                        interfaces_with_access[current_interface]['alias'] = line[11:-1] if line.startswith('set alias "') else line[10:]
                    elif line.startswith('set vlanid ') and current_interface:
                        interfaces_with_access[current_interface]['vlanid'] = line[11:]
                    elif line.startswith('set security-mode ') and current_interface:
                        interfaces_with_access[current_interface]['security_mode'] = line[19:]
                    
                    # 检测接口编辑结束
                    elif line == "next":
                        current_interface = None
            
            print(f"✅ 找到 {len(interfaces_with_access)} 个接口配置")
            
            # 统计access-control配置
            access_types = defaultdict(int)
            interfaces_with_allowaccess = 0
            
            for interface, config in interfaces_with_access.items():
                if config['allowaccess']:
                    interfaces_with_allowaccess += 1
                    for access_type in config['allowaccess']:
                        access_types[access_type] += 1
            
            print(f"📊 access-control统计:")
            print(f"   有allowaccess配置的接口: {interfaces_with_allowaccess}")
            print(f"   访问类型分布:")
            for access_type, count in sorted(access_types.items()):
                print(f"      {access_type}: {count}个接口")
            
            return interfaces_with_access
            
        except Exception as e:
            print(f"❌ 分析FortiGate配置失败: {str(e)}")
            return None
    
    def analyze_ntos_access_control(self):
        """分析NTOS XML中的access-control配置"""
        print("\n🔍 分析NTOS XML中的access-control配置")
        print("=" * 60)
        
        if not os.path.exists(self.ntos_xml):
            print(f"❌ NTOS XML文件不存在: {self.ntos_xml}")
            return None
        
        try:
            tree = etree.parse(self.ntos_xml)
            root = tree.getroot()
            
            # 查找access-control相关元素
            access_control_elements = []
            interface_elements = []
            
            for elem in root.iter():
                tag = elem.tag.split('}')[-1] if '}' in elem.tag else elem.tag
                
                if 'access' in tag.lower() or 'control' in tag.lower():
                    access_control_elements.append({
                        'tag': tag,
                        'full_tag': elem.tag,
                        'text': elem.text,
                        'attributes': dict(elem.attrib)
                    })
                
                if tag == 'interface':
                    interface_elements.append(elem)
            
            print(f"✅ 找到 {len(access_control_elements)} 个access-control相关元素")
            print(f"✅ 找到 {len(interface_elements)} 个interface元素")
            
            # 分析interface元素中的access-control配置
            interfaces_with_access_control = {}

            # 查找所有physical和vlan接口
            physical_interfaces = []
            vlan_interfaces = []

            for elem in root.iter():
                tag = elem.tag.split('}')[-1] if '}' in elem.tag else elem.tag
                if tag == 'physical':
                    physical_interfaces.append(elem)
                elif tag == 'vlan':
                    vlan_interfaces.append(elem)

            all_interfaces = physical_interfaces + vlan_interfaces

            for interface_elem in all_interfaces:
                interface_name = None
                access_control_config = {}

                # 查找接口名称
                for child in interface_elem:
                    child_tag = child.tag.split('}')[-1] if '}' in child.tag else child.tag
                    if child_tag == 'name':
                        interface_name = child.text
                        break

                if interface_name:
                    # 查找access-control配置
                    for child in interface_elem:
                        child_tag = child.tag.split('}')[-1] if '}' in child.tag else child.tag
                        if child_tag == 'access-control':
                            # 解析access-control子元素
                            for service_elem in child:
                                service_tag = service_elem.tag.split('}')[-1] if '}' in service_elem.tag else service_elem.tag
                                access_control_config[service_tag] = service_elem.text

                    if access_control_config:
                        interfaces_with_access_control[interface_name] = access_control_config
            
            print(f"📊 NTOS access-control统计:")
            print(f"   有access-control配置的接口: {len(interfaces_with_access_control)}")
            
            if access_control_elements:
                print(f"   access-control元素类型:")
                element_types = defaultdict(int)
                for elem in access_control_elements:
                    element_types[elem['tag']] += 1
                for elem_type, count in sorted(element_types.items()):
                    print(f"      {elem_type}: {count}个")
            
            return {
                'access_control_elements': access_control_elements,
                'interfaces_with_access_control': interfaces_with_access_control,
                'interface_elements': interface_elements
            }
            
        except Exception as e:
            print(f"❌ 分析NTOS XML失败: {str(e)}")
            return None
    
    def compare_configurations(self, fortigate_data, ntos_data):
        """对比FortiGate和NTOS的access-control配置"""
        print("\n🔍 对比FortiGate和NTOS的access-control配置")
        print("=" * 60)
        
        if not fortigate_data or not ntos_data:
            print("❌ 缺少对比数据")
            return
        
        # 统计FortiGate接口
        fortigate_interfaces = set(fortigate_data.keys())
        fortigate_with_access = set(name for name, config in fortigate_data.items() 
                                  if config['allowaccess'])
        
        # 统计NTOS接口
        ntos_interfaces = set()
        for interface_elem in ntos_data['interface_elements']:
            for child in interface_elem:
                child_tag = child.tag.split('}')[-1] if '}' in child.tag else child.tag
                if child_tag == 'name' and child.text:
                    ntos_interfaces.add(child.text)
                    break
        
        ntos_with_access = set(ntos_data['interfaces_with_access_control'].keys())
        
        print(f"📊 配置对比统计:")
        print(f"   FortiGate接口总数: {len(fortigate_interfaces)}")
        print(f"   FortiGate有allowaccess的接口: {len(fortigate_with_access)}")
        print(f"   NTOS接口总数: {len(ntos_interfaces)}")
        print(f"   NTOS有access-control的接口: {len(ntos_with_access)}")
        
        # 查找缺失的接口
        missing_in_ntos = fortigate_interfaces - ntos_interfaces
        extra_in_ntos = ntos_interfaces - fortigate_interfaces
        
        if missing_in_ntos:
            print(f"\n❌ NTOS中缺失的接口 ({len(missing_in_ntos)}个):")
            for interface in sorted(missing_in_ntos):
                print(f"      - {interface}")
        
        if extra_in_ntos:
            print(f"\n⚠️ NTOS中多余的接口 ({len(extra_in_ntos)}个):")
            for interface in sorted(extra_in_ntos):
                print(f"      + {interface}")
        
        # 查找access-control转换情况
        access_control_missing = fortigate_with_access - ntos_with_access
        
        if access_control_missing:
            print(f"\n❌ access-control转换缺失 ({len(access_control_missing)}个):")
            for interface in sorted(access_control_missing):
                if interface in fortigate_data:
                    access_list = fortigate_data[interface]['allowaccess']
                    print(f"      - {interface}: {access_list}")
        
        # 详细对比有access-control的接口
        common_interfaces = fortigate_interfaces & ntos_interfaces
        print(f"\n📋 详细access-control转换分析:")
        
        conversion_success = 0
        conversion_partial = 0
        conversion_missing = 0
        
        for interface in sorted(common_interfaces):
            fortigate_config = fortigate_data.get(interface, {})
            ntos_config = ntos_data['interfaces_with_access_control'].get(interface, {})
            
            if fortigate_config.get('allowaccess'):
                if ntos_config:
                    conversion_success += 1
                    print(f"   ✅ {interface}: 转换成功")
                else:
                    conversion_missing += 1
                    print(f"   ❌ {interface}: 转换缺失 (FortiGate: {fortigate_config['allowaccess']})")
        
        print(f"\n📊 转换成功率:")
        total_with_access = len(fortigate_with_access)
        if total_with_access > 0:
            success_rate = (conversion_success / total_with_access) * 100
            print(f"   转换成功: {conversion_success}/{total_with_access} ({success_rate:.1f}%)")
            print(f"   转换缺失: {conversion_missing}/{total_with_access}")
        else:
            print("   没有需要转换的access-control配置")

def main():
    """主函数"""
    print("🚀 开始FortiGate到NTOS转换器access-control模块全面检测")
    print("=" * 80)
    
    analyzer = AccessControlAnalyzer()
    
    # 1. 分析FortiGate配置
    fortigate_data = analyzer.analyze_fortigate_access_control()
    
    # 2. 分析NTOS XML配置
    ntos_data = analyzer.analyze_ntos_access_control()
    
    # 3. 对比配置
    analyzer.compare_configurations(fortigate_data, ntos_data)
    
    print(f"\n{'='*80}")
    print("📊 access-control模块检测总结:")
    print(f"{'='*80}")
    
    if fortigate_data and ntos_data:
        fortigate_count = len([name for name, config in fortigate_data.items() 
                             if config['allowaccess']])
        ntos_count = len(ntos_data['interfaces_with_access_control'])
        
        print(f"✅ FortiGate配置解析: 成功")
        print(f"✅ NTOS XML解析: 成功")
        print(f"📊 配置统计:")
        print(f"   FortiGate有allowaccess的接口: {fortigate_count}")
        print(f"   NTOS有access-control的接口: {ntos_count}")
        
        if fortigate_count > 0:
            conversion_rate = (ntos_count / fortigate_count) * 100
            print(f"   转换覆盖率: {conversion_rate:.1f}%")
            
            if conversion_rate >= 90:
                print("🎉 access-control转换质量: 优秀")
            elif conversion_rate >= 70:
                print("✅ access-control转换质量: 良好")
            elif conversion_rate >= 50:
                print("⚠️ access-control转换质量: 一般")
            else:
                print("❌ access-control转换质量: 需要改进")
        else:
            print("ℹ️ 没有需要转换的access-control配置")
    else:
        print("❌ 检测失败，无法完成分析")

if __name__ == "__main__":
    main()
