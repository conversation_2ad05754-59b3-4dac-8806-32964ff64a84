#!/usr/bin/env python3
"""
分析FortiGate到NTOS转换器生成的XML文件中安全策略部分的YANG模型合规性问题
"""

import sys
import os
import re
import xml.etree.ElementTree as ET
from typing import Dict, List, Set, Tuple
from collections import defaultdict

def parse_xml_file(xml_file_path: str) -> ET.Element:
    """解析XML文件"""
    try:
        tree = ET.parse(xml_file_path)
        return tree.getroot()
    except Exception as e:
        print(f"❌ 解析XML文件失败: {e}")
        return None

def extract_network_objects(root: ET.Element) -> Dict[str, Dict]:
    """提取所有网络对象定义"""
    network_objects = {}
    
    # 查找所有网络对象定义
    for network_obj in root.findall(".//network-obj", {"": "urn:ruijie:ntos:params:xml:ns:yang:network-obj"}):
        # 提取address-set
        for address_set in network_obj.findall(".//address-set"):
            name_elem = address_set.find("name")
            if name_elem is not None:
                name = name_elem.text
                ip_sets = []
                for ip_set in address_set.findall(".//ip-set"):
                    ip_addr_elem = ip_set.find("ip-address")
                    if ip_addr_elem is not None:
                        ip_sets.append(ip_addr_elem.text)
                
                network_objects[name] = {
                    "type": "address-set",
                    "ip_addresses": ip_sets
                }
        
        # 提取address-group
        for address_group in network_obj.findall(".//address-group"):
            name_elem = address_group.find("name")
            if name_elem is not None:
                name = name_elem.text
                address_sets = []
                for addr_set in address_group.findall(".//address-set"):
                    addr_name_elem = addr_set.find("name")
                    if addr_name_elem is not None:
                        address_sets.append(addr_name_elem.text)
                
                network_objects[name] = {
                    "type": "address-group",
                    "address_sets": address_sets
                }
    
    return network_objects

def extract_security_policies(root: ET.Element) -> List[Dict]:
    """提取所有安全策略"""
    policies = []
    
    # 查找所有安全策略
    for policy in root.findall(".//policy", {"": "urn:ruijie:ntos:params:xml:ns:yang:security-policy"}):
        policy_data = {}
        
        # 提取策略名称
        name_elem = policy.find("name")
        if name_elem is not None:
            policy_data["name"] = name_elem.text
        
        # 提取源网络对象
        source_networks = []
        for src_net in policy.findall(".//source-network"):
            name_elem = src_net.find("name")
            if name_elem is not None:
                source_networks.append(name_elem.text)
        policy_data["source_networks"] = source_networks
        
        # 提取目标网络对象
        dest_networks = []
        for dest_net in policy.findall(".//dest-network"):
            name_elem = dest_net.find("name")
            if name_elem is not None:
                dest_networks.append(name_elem.text)
        policy_data["dest_networks"] = dest_networks
        
        # 提取源区域
        source_zones = []
        for src_zone in policy.findall(".//source-zone"):
            name_elem = src_zone.find("name")
            if name_elem is not None:
                source_zones.append(name_elem.text)
        policy_data["source_zones"] = source_zones
        
        # 提取目标区域
        dest_zones = []
        for dest_zone in policy.findall(".//dest-zone"):
            name_elem = dest_zone.find("name")
            if name_elem is not None:
                dest_zones.append(name_elem.text)
        policy_data["dest_zones"] = dest_zones
        
        # 提取服务
        services = []
        for service in policy.findall(".//service"):
            name_elem = service.find("name")
            if name_elem is not None:
                services.append(name_elem.text)
        policy_data["services"] = services
        
        policies.append(policy_data)
    
    return policies

def validate_yang_name_compliance(name: str) -> Tuple[bool, str]:
    """验证名称是否符合YANG模型约束"""
    if not name:
        return False, "名称不能为空"
    
    # YANG模型约束: ntos-obj-name-type
    # pattern "[^`~!#$%^&*+/|{};:\"',\\<>?]*"
    forbidden_chars = set('`~!#$%^&*+/|{};:"\'\\<>?,')
    
    invalid_chars = []
    for char in name:
        if char in forbidden_chars:
            invalid_chars.append(char)
    
    if invalid_chars:
        return False, f"包含禁用字符: {', '.join(set(invalid_chars))}"
    
    # 长度检查
    if len(name) > 64:
        return False, f"名称过长: {len(name)} > 64"
    
    return True, ""

def check_reference_integrity(policies: List[Dict], network_objects: Dict[str, Dict]) -> List[Dict]:
    """检查引用完整性"""
    issues = []
    
    for policy in policies:
        policy_name = policy.get("name", "未知策略")
        
        # 检查源网络对象引用
        for src_net in policy.get("source_networks", []):
            if src_net not in network_objects:
                issues.append({
                    "type": "missing_reference",
                    "policy": policy_name,
                    "object_type": "source-network",
                    "object_name": src_net,
                    "description": f"策略 '{policy_name}' 引用的源网络对象 '{src_net}' 不存在"
                })
        
        # 检查目标网络对象引用
        for dest_net in policy.get("dest_networks", []):
            if dest_net not in network_objects:
                issues.append({
                    "type": "missing_reference",
                    "policy": policy_name,
                    "object_type": "dest-network",
                    "object_name": dest_net,
                    "description": f"策略 '{policy_name}' 引用的目标网络对象 '{dest_net}' 不存在"
                })
    
    return issues

def check_yang_compliance(policies: List[Dict], network_objects: Dict[str, Dict]) -> List[Dict]:
    """检查YANG模型合规性"""
    issues = []
    
    # 检查网络对象名称合规性
    for obj_name, obj_data in network_objects.items():
        is_valid, error_msg = validate_yang_name_compliance(obj_name)
        if not is_valid:
            issues.append({
                "type": "yang_violation",
                "object_type": "network_object",
                "object_name": obj_name,
                "description": f"网络对象名称 '{obj_name}' 违反YANG约束: {error_msg}"
            })
    
    # 检查策略名称合规性
    for policy in policies:
        policy_name = policy.get("name", "")
        if policy_name:
            is_valid, error_msg = validate_yang_name_compliance(policy_name)
            if not is_valid:
                issues.append({
                    "type": "yang_violation",
                    "object_type": "policy",
                    "object_name": policy_name,
                    "description": f"策略名称 '{policy_name}' 违反YANG约束: {error_msg}"
                })
    
    return issues

def check_xml_structure_compliance(root: ET.Element) -> List[Dict]:
    """检查XML结构合规性"""
    issues = []
    
    # 检查安全策略结构
    for policy in root.findall(".//policy", {"": "urn:ruijie:ntos:params:xml:ns:yang:security-policy"}):
        policy_name_elem = policy.find("name")
        policy_name = policy_name_elem.text if policy_name_elem is not None else "未知策略"
        
        # 检查必需字段
        required_fields = ["name", "enabled", "action"]
        for field in required_fields:
            if policy.find(field) is None:
                issues.append({
                    "type": "structure_violation",
                    "object_type": "policy",
                    "object_name": policy_name,
                    "description": f"策略 '{policy_name}' 缺少必需字段: {field}"
                })
        
        # 检查source-network和dest-network结构
        for src_net in policy.findall(".//source-network"):
            if src_net.find("name") is None:
                issues.append({
                    "type": "structure_violation",
                    "object_type": "source-network",
                    "object_name": policy_name,
                    "description": f"策略 '{policy_name}' 的source-network缺少name字段"
                })
        
        for dest_net in policy.findall(".//dest-network"):
            if dest_net.find("name") is None:
                issues.append({
                    "type": "structure_violation",
                    "object_type": "dest-network",
                    "object_name": policy_name,
                    "description": f"策略 '{policy_name}' 的dest-network缺少name字段"
                })
    
    return issues

def analyze_specific_case(policies: List[Dict], network_objects: Dict[str, Dict]) -> List[Dict]:
    """分析特定问题案例"""
    issues = []
    
    # 查找包含10_128_0_104的策略
    for policy in policies:
        policy_name = policy.get("name", "")
        source_networks = policy.get("source_networks", [])
        
        if "10_128_0_104" in source_networks:
            # 检查这个网络对象是否存在
            if "10_128_0_104" not in network_objects:
                issues.append({
                    "type": "specific_case",
                    "object_type": "source-network",
                    "object_name": "10_128_0_104",
                    "policy": policy_name,
                    "description": f"策略 '{policy_name}' 引用的网络对象 '10_128_0_104' 不存在，这是一个IP地址格式的名称，可能需要创建对应的address-set定义"
                })
            
            # 检查名称格式
            is_valid, error_msg = validate_yang_name_compliance("10_128_0_104")
            if not is_valid:
                issues.append({
                    "type": "specific_case",
                    "object_type": "network_object_name",
                    "object_name": "10_128_0_104",
                    "policy": policy_name,
                    "description": f"网络对象名称 '10_128_0_104' 违反YANG约束: {error_msg}"
                })
            else:
                issues.append({
                    "type": "specific_case_info",
                    "object_type": "network_object_name",
                    "object_name": "10_128_0_104",
                    "policy": policy_name,
                    "description": f"网络对象名称 '10_128_0_104' 符合YANG约束，但缺少对应的address-set定义"
                })
    
    return issues

def main():
    """主函数"""
    xml_file = "output/fortigate-z5100s-R11_backup_20250804_161148.xml"
    
    if not os.path.exists(xml_file):
        print(f"❌ XML文件不存在: {xml_file}")
        return False
    
    print("FortiGate到NTOS转换器安全策略YANG模型合规性分析")
    print("=" * 80)
    
    # 解析XML文件
    print("📄 解析XML文件...")
    root = parse_xml_file(xml_file)
    if root is None:
        return False
    
    # 提取网络对象和安全策略
    print("🔍 提取网络对象定义...")
    network_objects = extract_network_objects(root)
    print(f"   找到 {len(network_objects)} 个网络对象")
    
    print("🔍 提取安全策略...")
    policies = extract_security_policies(root)
    print(f"   找到 {len(policies)} 个安全策略")
    
    # 执行各种检查
    all_issues = []
    
    print("\n🔍 检查引用完整性...")
    ref_issues = check_reference_integrity(policies, network_objects)
    all_issues.extend(ref_issues)
    print(f"   发现 {len(ref_issues)} 个引用完整性问题")
    
    print("🔍 检查YANG模型合规性...")
    yang_issues = check_yang_compliance(policies, network_objects)
    all_issues.extend(yang_issues)
    print(f"   发现 {len(yang_issues)} 个YANG合规性问题")
    
    print("🔍 检查XML结构合规性...")
    struct_issues = check_xml_structure_compliance(root)
    all_issues.extend(struct_issues)
    print(f"   发现 {len(struct_issues)} 个XML结构问题")
    
    print("🔍 分析特定问题案例...")
    specific_issues = analyze_specific_case(policies, network_objects)
    all_issues.extend(specific_issues)
    print(f"   发现 {len(specific_issues)} 个特定案例问题")
    
    # 生成报告
    print("\n" + "=" * 80)
    print("📊 问题分析报告")
    print("=" * 80)
    
    if not all_issues:
        print("✅ 未发现YANG模型违规问题")
        return True
    
    # 按类型分组显示问题
    issues_by_type = defaultdict(list)
    for issue in all_issues:
        issues_by_type[issue["type"]].append(issue)
    
    for issue_type, issues in issues_by_type.items():
        print(f"\n📋 {issue_type.upper()} ({len(issues)} 个问题):")
        print("-" * 60)
        
        for i, issue in enumerate(issues, 1):
            print(f"{i:2d}. {issue['description']}")
            if 'policy' in issue:
                print(f"     策略: {issue['policy']}")
            if 'object_name' in issue:
                print(f"     对象: {issue['object_name']}")
            print()
    
    # 总结
    print("=" * 80)
    print(f"📈 总计发现 {len(all_issues)} 个问题")
    print(f"   - 引用完整性问题: {len(ref_issues)}")
    print(f"   - YANG合规性问题: {len(yang_issues)}")
    print(f"   - XML结构问题: {len(struct_issues)}")
    print(f"   - 特定案例问题: {len(specific_issues)}")
    
    return len(all_issues) == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
