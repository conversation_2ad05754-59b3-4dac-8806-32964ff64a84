#!/usr/bin/env python3
"""
分析threat-intelligence配置丢失问题
"""

import os
from lxml import etree

def analyze_template_file():
    """分析模板文件中的threat-intelligence结构"""
    print("🔍 分析模板文件中的threat-intelligence结构")
    print("=" * 60)
    
    template_file = "engine/data/input/configData/z5100s/R11/Config/running.xml"
    
    if not os.path.exists(template_file):
        print(f"❌ 模板文件不存在: {template_file}")
        return None
    
    try:
        tree = etree.parse(template_file)
        root = tree.getroot()
        
        # 查找threat-intelligence元素
        threat_intel_elem = None
        for elem in root.iter():
            if elem.tag.endswith('threat-intelligence'):
                threat_intel_elem = elem
                break
        
        if threat_intel_elem is None:
            print("❌ 模板文件中未找到threat-intelligence元素")
            return None
        
        print("✅ 找到threat-intelligence元素")
        print(f"   标签: {threat_intel_elem.tag}")
        print(f"   xmlns: {threat_intel_elem.get('xmlns')}")
        
        # 分析结构
        structure = analyze_element_structure(threat_intel_elem, "")
        print("\n📊 完整结构分析:")
        for line in structure:
            print(f"   {line}")
        
        # 生成XML字符串用于对比
        xml_string = etree.tostring(
            threat_intel_elem,
            encoding='unicode',
            pretty_print=True
        )
        
        return {
            "element": threat_intel_elem,
            "structure": structure,
            "xml": xml_string
        }
        
    except Exception as e:
        print(f"❌ 分析模板文件失败: {str(e)}")
        return None

def analyze_generated_file():
    """分析生成文件中的threat-intelligence结构"""
    print("\n🔍 分析生成文件中的threat-intelligence结构")
    print("=" * 60)
    
    generated_file = "output/fortigate-z5100s-R11-fixed.xml"
    
    if not os.path.exists(generated_file):
        print(f"❌ 生成文件不存在: {generated_file}")
        return None
    
    try:
        tree = etree.parse(generated_file)
        root = tree.getroot()
        
        # 查找threat-intelligence元素
        threat_intel_elem = None
        for elem in root.iter():
            if elem.tag.endswith('threat-intelligence'):
                threat_intel_elem = elem
                break
        
        if threat_intel_elem is None:
            print("❌ 生成文件中未找到threat-intelligence元素")
            return None
        
        print("✅ 找到threat-intelligence元素")
        print(f"   标签: {threat_intel_elem.tag}")
        print(f"   xmlns: {threat_intel_elem.get('xmlns')}")
        
        # 分析结构
        structure = analyze_element_structure(threat_intel_elem, "")
        print("\n📊 完整结构分析:")
        for line in structure:
            print(f"   {line}")
        
        # 生成XML字符串用于对比
        xml_string = etree.tostring(
            threat_intel_elem,
            encoding='unicode',
            pretty_print=True
        )
        
        return {
            "element": threat_intel_elem,
            "structure": structure,
            "xml": xml_string
        }
        
    except Exception as e:
        print(f"❌ 分析生成文件失败: {str(e)}")
        return None

def analyze_element_structure(element, prefix):
    """递归分析元素结构"""
    structure = []
    
    # 当前元素
    tag = element.tag.split('}')[-1] if '}' in element.tag else element.tag
    text = element.text.strip() if element.text else ""
    
    if text:
        structure.append(f"{prefix}{tag}: {text}")
    else:
        structure.append(f"{prefix}{tag}")
    
    # 子元素
    for child in element:
        child_structure = analyze_element_structure(child, prefix + "  ")
        structure.extend(child_structure)
    
    return structure

def compare_structures(template_data, generated_data):
    """对比两个结构的差异"""
    print("\n🔍 对比结构差异")
    print("=" * 60)
    
    if template_data is None or generated_data is None:
        print("❌ 无法对比，缺少数据")
        return
    
    template_structure = set(template_data["structure"])
    generated_structure = set(generated_data["structure"])
    
    # 找出缺失的元素
    missing_in_generated = template_structure - generated_structure
    extra_in_generated = generated_structure - template_structure
    
    print("📊 结构对比结果:")
    print(f"   模板结构元素数: {len(template_structure)}")
    print(f"   生成结构元素数: {len(generated_structure)}")
    
    if missing_in_generated:
        print(f"\n❌ 生成文件中缺失的元素 ({len(missing_in_generated)}个):")
        for item in sorted(missing_in_generated):
            print(f"      - {item}")
    
    if extra_in_generated:
        print(f"\n⚠️ 生成文件中多余的元素 ({len(extra_in_generated)}个):")
        for item in sorted(extra_in_generated):
            print(f"      + {item}")
    
    if not missing_in_generated and not extra_in_generated:
        print("✅ 结构完全一致")
    
    # 显示XML对比
    print(f"\n📄 模板XML:")
    print("   " + "\n   ".join(template_data["xml"].split('\n')))
    
    print(f"\n📄 生成XML:")
    print("   " + "\n   ".join(generated_data["xml"].split('\n')))

def identify_loss_cause():
    """识别丢失原因"""
    print("\n🔍 识别配置丢失原因")
    print("=" * 60)
    
    # 检查可能的原因
    causes = []
    
    # 1. 检查XML模板集成阶段是否有过滤逻辑
    integration_file = "engine/processing/stages/xml_template_integration_stage.py"
    if os.path.exists(integration_file):
        with open(integration_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        if "threat-intelligence" in content:
            causes.append("XML模板集成阶段包含threat-intelligence相关逻辑")
        
        if "_should_skip_element" in content:
            causes.append("存在元素跳过逻辑，可能影响threat-intelligence")
        
        if "security-zone" in content and "skip" in content:
            causes.append("存在security-zone跳过逻辑，可能影响子元素")
    
    # 2. 检查是否有其他处理阶段
    print("🔍 可能的丢失原因:")
    for i, cause in enumerate(causes, 1):
        print(f"   {i}. {cause}")
    
    if not causes:
        print("   未发现明显的处理逻辑")
    
    # 3. 提供修复建议
    print(f"\n💡 修复建议:")
    print("   1. 检查XML模板加载后是否完整保留threat-intelligence结构")
    print("   2. 确认XML合并逻辑不会意外删除子元素")
    print("   3. 验证元素跳过逻辑的过滤条件")
    print("   4. 添加threat-intelligence结构完整性检查")

def main():
    """主函数"""
    print("🚀 开始分析threat-intelligence配置丢失问题")
    
    # 分析模板文件
    template_data = analyze_template_file()
    
    # 分析生成文件
    generated_data = analyze_generated_file()
    
    # 对比结构差异
    compare_structures(template_data, generated_data)
    
    # 识别丢失原因
    identify_loss_cause()
    
    print(f"\n{'='*60}")
    print("📊 分析总结:")
    print(f"{'='*60}")
    
    if template_data and generated_data:
        template_count = len(template_data["structure"])
        generated_count = len(generated_data["structure"])
        
        if template_count > generated_count:
            print(f"❌ 配置丢失确认: 模板有{template_count}个元素，生成只有{generated_count}个")
            print("🎯 需要修复XML处理逻辑以保留完整的threat-intelligence结构")
        elif template_count == generated_count:
            print(f"✅ 元素数量一致: {template_count}个")
            print("🔍 可能是结构层次问题，需要详细检查")
        else:
            print(f"⚠️ 生成文件元素更多: 模板{template_count}个，生成{generated_count}个")
    else:
        print("❌ 分析失败，无法获取完整数据")

if __name__ == "__main__":
    main()
