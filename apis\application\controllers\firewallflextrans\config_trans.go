package firewallflextrans

import (
	"bufio"
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"strings"
	"time"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/i18n"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/firewallflextrans"
	"irisAdminApi/service/cache"
	"irisAdminApi/service/dao/firewallflextrans/dconfigtrans"

	"github.com/kataras/iris/v12"
)

// 支持的厂商列表
var supportedVendors = map[string]bool{
	"fortigate": true,
	// 后续可添加其它厂商
}

// 默认厂商
const defaultVendor = "fortigate"

// 验证厂商是否支持
func isVendorSupported(vendor string) bool {
	return isVendorSupportedUtil(vendor)
}

// 获取厂商参数，如果没有指定则返回默认厂商
func getVendorParam(ctx iris.Context) string {
	return getVendorParamUtil(ctx)
}

// VerifyConfig 通用配置验证API处理函数
func VerifyConfig(ctx iris.Context) {
	// 获取语言参数
	language := getLanguageFromContext(ctx)

	// 获取厂商信息
	vendor := getVendorParam(ctx)

	// 检查厂商是否支持
	if !isVendorSupported(vendor) {
		response.I18nError(ctx, "vendor.not_supported", "vendor", vendor, "supported", "fortigate")
		return
	}

	// 上传文件处理
	f, fh, err := ctx.FormFile("file")
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		response.I18nError(ctx, "system.error")
		return
	}
	defer f.Close()

	// 验证文件扩展名，只允许 .conf 格式（移到保存文件之前）
	fileExt := strings.ToLower(filepath.Ext(fh.Filename))
	if fileExt != ".conf" {
		logging.ErrorLogger.Errorf(getI18nMessage("internal.unsupported_file_format", language), fileExt, fh.Filename)
		response.I18nError(ctx, "file.invalid_extension", "filename", fh.Filename, "allowed", ".conf")
		return
	}

	// 验证文件名长度（移到保存文件之前）
	if len(fh.Filename) > 248 {
		logging.ErrorLogger.Errorf(getI18nMessageWithParams("internal.filename_too_long", language, map[string]interface{}{"filename": fh.Filename, "length": len(fh.Filename)}))
		response.I18nError(ctx, "file.name_too_long", "max_length", "248")
		return
	}

	// 创建临时工作目录
	tempID := libs.GetUniqueID()
	tempDir := filepath.Join(libs.Config.ConfigTrans.TempDir, tempID)
	defer os.RemoveAll(tempDir) // 处理完成后清理临时目录

	os.MkdirAll(tempDir, 0750)

	// 保存上传的文件
	filePath := filepath.Join(tempDir, fh.Filename)
	_, err = ctx.SaveFormFile(fh, filePath)
	if err != nil {
		// 增强错误处理，检查是否是文件名过长错误
		if strings.Contains(err.Error(), "file name too long") {
			logging.ErrorLogger.Errorf(getI18nMessageWithParams("internal.filename_too_long_save_failed", language, map[string]interface{}{"error": err.Error()}))
			response.I18nError(ctx, "file.name_too_long", "max_length", "248")
		} else {
			logging.ErrorLogger.Errorf(getI18nMessageWithParams("internal.save_file_failed", language, map[string]interface{}{"error": err.Error()}))
			response.I18nError(ctx, "file.save_failed", "error", err.Error())
		}
		return
	}

	// 验证文件是否为纯文本文件
	if !isTextFile(filePath) {
		logging.ErrorLogger.Errorf(getI18nMessageWithParams("internal.invalid_config_content", language, map[string]interface{}{"path": filePath}))
		response.I18nError(ctx, "file.invalid_content")
		return
	}

	// 验证文件内容是否符合配置文件格式
	if !isValidConfigFile(filePath) {
		logging.ErrorLogger.Errorf(getI18nMessageWithParams("internal.invalid_config_content", language, map[string]interface{}{"path": filePath}))
		response.I18nError(ctx, "file.invalid_content")
		return
	}

	// 调用验证函数
	result, err := verifyConfig(vendor, filePath, language)
	if err != nil {
		// 只有在真正的系统错误时才返回SystemErr
		logging.ErrorLogger.Errorf(getI18nMessageWithParams("internal.system_error_occurred", language, map[string]interface{}{"error": err}))
		response.I18nError(ctx, "system.error_with_message", "error", err.Error())
		return
	}
	// 检查结果中是否包含业务错误
	valid, hasValid := result["valid"].(bool)

	// 如果验证失败，但有返回结果（业务错误而非系统错误）
	if hasValid && !valid {
		// 提取错误信息
		errorMessage := ""
		if errMsg, ok := result["error"].(string); ok && errMsg != "" {
			errorMessage = errMsg
		} else if errMsg, ok := result["message"].(string); ok && errMsg != "" {
			errorMessage = errMsg
		}

		// 检查是否包含版本错误标记
		versionError := false
		if ve, ok := result["version_error"].(bool); ok {
			versionError = ve
		}

		// 对于版本检测错误，返回特定的错误码和信息
		if versionError || (errorMessage != "" && strings.Contains(errorMessage, "版本") && strings.Contains(errorMessage, "检测")) {
			logging.ErrorLogger.Errorf(getI18nMessageWithParams("internal.verification_failed_version_detection", language, map[string]interface{}{"message": errorMessage}))
			response.I18nResponse(ctx, response.SystemErr.Code, result, "convert.version_detection_failed")
			return
		}

		// 对于其他业务错误，返回验证结果
		logging.ErrorLogger.Errorf(getI18nMessageWithParams("internal.verification_failed_business_error", language, map[string]interface{}{"message": errorMessage}))
		response.I18nResponse(ctx, response.SystemErr.Code, result, "convert.verification_failed")
		return
	}

	// 验证成功，构造符合预期格式的返回结果
	if hasValid && valid {
		// 创建新的结果对象，符合预期的格式
		formattedResult := map[string]interface{}{
			"message": i18n.Translate(language, "convert.verification_success", nil),
			"valid":   true,
		}

		// 如果原始结果中有版本信息，保留它
		// if detectedVersion, ok := result["detected_version"].(string); ok && detectedVersion != "" {
		// 	formattedResult["detected_version"] = detectedVersion
		// }

		language := getLanguageFromContext(ctx)
		logging.DebugLogger.Infof(getI18nMessageWithParams("debug.verification_success_formatted_result", language, map[string]interface{}{"result": formattedResult}))
		response.I18nResponse(ctx, response.NoErr.Code, formattedResult, "convert.verification_success")
		return
	}

	// 验证函数返回的结果中可能包含业务错误（如验证失败），但这不是系统错误
	// 直接返回结果，前端根据valid字段判断验证是否成功
	logging.DebugLogger.Infof(getI18nMessageWithParams("debug.verification_complete_result", language, map[string]interface{}{"result": result}))
	response.I18nResponse(ctx, response.NoErr.Code, result, "convert.verification_success")
}

// ExtractInterfaces 通用接口信息提取API处理函数
func ExtractInterfaces(ctx iris.Context) {
	// 获取语言参数
	language := getLanguageFromContext(ctx)

	// 获取厂商信息
	vendor := getVendorParam(ctx)

	// 检查厂商是否支持
	if !isVendorSupported(vendor) {
		response.I18nError(ctx, "vendor.not_supported", "vendor", vendor, "supported", "fortigate")
		return
	}

	// 上传文件处理
	f, fh, err := ctx.FormFile("file")
	if err != nil {
		logging.ErrorLogger.Errorf(getI18nMessageWithParams("internal.unable_to_get_upload_file", language, map[string]interface{}{"error": err.Error()}))
		response.I18nError(ctx, "file.upload_error", "error", err.Error())
		return
	}
	defer f.Close()

	// 记录请求参数和文件信息
	logging.DebugLogger.Infof(getI18nMessageWithParams("debug.interface_extraction_request", getLanguageFromContext(ctx), map[string]interface{}{"vendor": vendor, "file": fh.Filename, "size": fh.Size}))

	// 验证文件格式和大小
	if fh.Size == 0 {
		logging.ErrorLogger.Error(getI18nMessage("internal.uploaded_file_empty", language))
		response.I18nError(ctx, "file.empty")
		return
	}

	// 限制文件大小 (10MB)
	if fh.Size > 10*1024*1024 {
		logging.ErrorLogger.Error(getI18nMessage("internal.uploaded_file_too_large", language))
		response.I18nError(ctx, "file.too_large", "size", "10")
		return
	}

	// 验证文件扩展名，只允许 .conf 格式（移到保存文件之前）
	fileExt := strings.ToLower(filepath.Ext(fh.Filename))
	if fileExt != ".conf" {
		logging.ErrorLogger.Errorf(getI18nMessageWithParams("internal.unsupported_file_format", language, map[string]interface{}{"format": fileExt, "filename": fh.Filename}))
		response.I18nError(ctx, "file.invalid_extension", "filename", fh.Filename, "allowed", ".conf")
		return
	}

	// 验证文件名长度（移到保存文件之前）
	if len(fh.Filename) > 248 {
		logging.ErrorLogger.Errorf(getI18nMessageWithParams("internal.filename_too_long", language, map[string]interface{}{"filename": fh.Filename, "length": len(fh.Filename)}))
		response.I18nError(ctx, "file.name_too_long", "max_length", "248")
		return
	}

	// 创建临时工作目录
	tempID := libs.GetUniqueID()
	tempDir := filepath.Join(libs.Config.ConfigTrans.TempDir, tempID)
	defer os.RemoveAll(tempDir) // 处理完成后清理临时目录

	err = os.MkdirAll(tempDir, 0750)
	if err != nil {
		logging.ErrorLogger.Error(getI18nMessageWithParams("internal.create_temp_dir_failed", language, map[string]interface{}{"error": err}))
		response.I18nError(ctx, "file.temp_dir_error", "error", err.Error())
		return
	}

	// 保存上传的文件
	filePath := filepath.Join(tempDir, fh.Filename)
	_, err = ctx.SaveFormFile(fh, filePath)
	if err != nil {
		// 增强错误处理，检查是否是文件名过长错误
		if strings.Contains(err.Error(), "file name too long") {
			logging.ErrorLogger.Errorf(getI18nMessageWithParams("internal.filename_too_long_save_failed", language, map[string]interface{}{"error": err.Error()}))
			response.I18nError(ctx, "file.name_too_long", "max_length", "248")
		} else {
			logging.ErrorLogger.Error(getI18nMessageWithParams("internal.save_upload_file_failed", language, map[string]interface{}{"error": err}))
			response.I18nError(ctx, "file.save_failed", "error", err.Error())
		}
		return
	}

	// 验证文件是否为纯文本文件
	if !isTextFile(filePath) {
		logging.ErrorLogger.Errorf(getI18nMessageWithParams("internal.invalid_config_content", language, map[string]interface{}{"path": filePath}))
		response.I18nError(ctx, "file.invalid_content")
		return
	}

	// 验证文件内容是否符合配置文件格式
	if !isValidConfigFile(filePath) {
		logging.ErrorLogger.Errorf(getI18nMessageWithParams("internal.invalid_config_content", language, map[string]interface{}{"path": filePath}))
		response.I18nError(ctx, "file.invalid_content")
		return
	}

	// 调用提取接口函数
	result, err := extractInterfaces(vendor, filePath, language)
	if err != nil {
		// 只有在真正的系统错误时才返回SystemErr
		logging.ErrorLogger.Error(err.Error())
		response.I18nError(ctx, "system.error_with_message", "error", err.Error())
		return
	}

	// 检查结果中是否包含业务错误
	success, hasSuccess := result["success"].(bool)

	// 如果提取失败，但有返回结果（业务错误而非系统错误）
	if hasSuccess && !success {
		// 提取错误信息
		errorMessage := ""
		if errMsg, ok := result["error"].(string); ok && errMsg != "" {
			errorMessage = errMsg
		} else if errMsg, ok := result["message"].(string); ok && errMsg != "" {
			errorMessage = errMsg
		}

		// 检查是否是版本检测失败的错误
		if errorMessage != "" && strings.Contains(errorMessage, "版本") && strings.Contains(errorMessage, "检测") {
			logging.ErrorLogger.Errorf(getI18nMessageWithParams("internal.extract_interface_failed_version_detection", language, map[string]interface{}{"message": errorMessage}))
			response.I18nResponse(ctx, response.SystemErr.Code, result, "convert.version_detection_failed")
			return
		}

		// 对于其他业务错误，返回提取结果
		logging.ErrorLogger.Errorf(getI18nMessageWithParams("internal.extract_interface_failed_business_error", language, map[string]interface{}{"message": errorMessage}))
		response.I18nResponse(ctx, response.SystemErr.Code, result, "convert.extraction_failed")
		return
	}

	// 直接返回提取结果
	logging.DebugLogger.Infof(getI18nMessageWithParams("debug.extraction_complete_result", language, map[string]interface{}{"result": result}))
	response.I18nResponse(ctx, response.NoErr.Code, result, "convert.extract_success")
}

// CreateTransJob 通用创建配置转换作业API处理函数
func CreateTransJob(ctx iris.Context) {
	// 获取厂商信息
	vendor := getVendorParam(ctx)

	// 检查厂商是否支持
	if !isVendorSupported(vendor) {
		response.I18nError(ctx, "vendor.not_supported", "vendor", vendor, "supported", "fortigate")
		return
	}

	// 使用通用的配置转换处理逻辑
	createGenericTransJob(ctx, vendor)
}

// createGenericTransJob 创建通用的配置转换作业
func createGenericTransJob(ctx iris.Context, vendor string) {
	// 获取语言参数
	language := getLanguageFromContext(ctx)

	// 使用Redis任务计数器检查任务数量限制
	taskCounter := dconfigtrans.NewTaskCounter()

	// 检查任务计数器是否正常初始化
	if taskCounter == nil || taskCounter.GetRedisClient() == nil {
		logging.ErrorLogger.Error(getI18nMessage("internal.task_counter_not_initialized", language))
		// 尝试重新连接Redis
		if cache.TryReconnect() {
			logging.InfoLogger.Info(getI18nMessage("info.redis_reconnect_success_retry_task_counter", getLanguageFromContext(ctx)))
			taskCounter = dconfigtrans.NewTaskCounter()
			if taskCounter == nil || taskCounter.GetRedisClient() == nil {
				logging.ErrorLogger.Error(getI18nMessage("internal.redis_task_counter_init_failed", language))
				response.I18nError(ctx, "system.redis_error")
				return
			}
		} else {
			logging.ErrorLogger.Error(getI18nMessage("internal.redis_reconnect_failed", language))
			response.I18nError(ctx, "system.redis_error")
			return
		}
	}

	taskLimit := 10 // 设置任务数量限制为10个

	// 尝试获取任务槽位
	acquired, currentCount, err := taskCounter.TryAcquireTaskSlot(vendor, taskLimit)
	if err != nil {
		logging.ErrorLogger.Errorf(getI18nMessageWithParams("internal.acquire_task_slot_failed", language, map[string]interface{}{"error": err}))
		// 检查是否是Redis连接问题
		if strings.Contains(err.Error(), "Redis客户端未初始化") ||
			strings.Contains(err.Error(), "无法获取Redis连接") ||
			strings.Contains(err.Error(), "get on closed pool") {
			// 尝试重新连接Redis
			if cache.TryReconnect() {
				logging.InfoLogger.Info(getI18nMessage("info.redis_reconnect_retry", getLanguageFromContext(ctx)))
				response.I18nError(ctx, "system.redis_reconnected")
				return
			}
		}
		response.I18nError(ctx, "system.error")
		return
	}

	// 如果无法获取任务槽位，说明已达到限制
	if !acquired {
		logging.DebugLogger.Infof(getI18nMessageWithParams("debug.task_limit_reached", getLanguageFromContext(ctx), map[string]interface{}{"current": currentCount, "limit": taskLimit}))
		response.I18nError(ctx, "task.limit_reached", "limit", taskLimit, "current", currentCount)
		return
	}

	// 标记任务是否已创建，用于决定是否需要释放槽位
	taskCreated := false

	// 延迟函数，在函数退出时处理任务槽位释放
	defer func() {
		// 如果任务未成功创建，立即释放槽位
		// 如果成功创建，槽位将在任务完成时由工作线程释放
		if !taskCreated {
			if err := taskCounter.ReleaseTaskSlot(vendor); err != nil {
				logging.ErrorLogger.Errorf(getI18nMessageWithParams("internal.release_task_slot_failed", language, map[string]interface{}{"error": err}))
			}
		}
	}()

	request := dconfigtrans.Request{}
	if err := ctx.ReadForm(&request); err != nil {
		logging.ErrorLogger.Errorf("create config trans job read form err ", err)
		response.I18nError(ctx, "system.error")
		return
	}

	// 获取工作模式
	mode := request.Mode
	if mode == "" {
		mode = "convert" // 默认为转换模式
	}

	// 验证模式是否有效
	if mode != "convert" {
		response.I18nError(ctx, "param.invalid_mode", "mode", "convert")
		return
	}

	// 转换模式下验证Model和Version字段
	if request.Model == "" || request.Version == "" {
		language := getLanguageFromContext(ctx)
		response.I18nError(ctx, "param.missing_fields", "mode", getI18nMessage("mode.convert", language), "fields", getI18nMessage("fields.model_and_version", language))
		return
	}

	// 使用型号和版本表获取简写名称
	// 1. 查找型号是否存在
	deviceModel, err := dconfigtrans.GetModelByName(request.Model, vendor)
	if err != nil {
		logging.ErrorLogger.Errorf(getI18nMessageWithParams("internal.model_lookup_failed", language, map[string]interface{}{"model": request.Model, "error": err}))
		response.I18nError(ctx, "param.invalid_model", "model", request.Model)
		return
	}

	// 2. 查找版本是否存在
	deviceVersion, err := dconfigtrans.GetVersionByName(request.Version, vendor)
	if err != nil {
		logging.ErrorLogger.Errorf(getI18nMessageWithParams("internal.version_lookup_failed", language, map[string]interface{}{"version": request.Version, "error": err}))
		response.I18nError(ctx, "param.invalid_version", "version", request.Version)
		return
	}

	// 获取型号和版本的简写，用于后续处理
	modelShortName := deviceModel.ShortName
	versionShortName := deviceVersion.ShortName

	// 上传配置文件处理
	configFile, configFileHeader, err := ctx.FormFile("file")
	if err != nil {
		logging.ErrorLogger.Errorf("Error while uploading config file: %s", err.Error())
		response.I18nError(ctx, "file.upload_error", "error", err.Error())
		return
	}
	defer configFile.Close()

	// 检查配置文件大小
	if configFileHeader.Size == 0 {
		response.I18nError(ctx, "file.empty")
		return
	}
	if configFileHeader.Size > 10*1024*1024 {
		response.I18nError(ctx, "file.too_large", "size", "10")
		return
	}

	// 验证文件扩展名，只允许 .conf 格式（移到保存文件之前）
	fileExt := strings.ToLower(filepath.Ext(configFileHeader.Filename))
	if fileExt != ".conf" {
		logging.ErrorLogger.Errorf(getI18nMessageWithParams("internal.unsupported_file_format", language, map[string]interface{}{"format": fileExt, "filename": configFileHeader.Filename}))
		response.I18nError(ctx, "file.invalid_extension", "filename", configFileHeader.Filename, "allowed", ".conf")
		return
	}

	// 检查文件名长度（移到保存文件之前）
	if len(configFileHeader.Filename) > 248 {
		logging.ErrorLogger.Errorf(getI18nMessageWithParams("internal.filename_too_long", language, map[string]interface{}{"filename": configFileHeader.Filename, "length": len(configFileHeader.Filename)}))
		response.I18nError(ctx, "file.name_too_long", "max_length", "248")
		return
	}

	// 获取接口映射JSON字符串（必须提供）
	mappingJSON := request.MappingJSON
	if mappingJSON == "" {
		response.I18nError(ctx, "interface.mapping_required")
		return
	}

	// 生成任务ID
	jobID := libs.GetUUID()

	// 创建上传目录
	uploadDir := filepath.Join(libs.Config.ConfigTrans.Upload, time.Now().Format("20060102"), jobID)
	os.MkdirAll(uploadDir, 0750)

	// 创建映射文件目录
	mappingDir := filepath.Join(uploadDir, "mappings")
	os.MkdirAll(mappingDir, 0750)

	// 生成映射文件名
	mappingFileName := fmt.Sprintf("interface_mapping_%s.json", jobID)
	mappingFilePath := filepath.Join(mappingDir, mappingFileName)

	// 将JSON字符串写入映射文件
	err = os.WriteFile(mappingFilePath, []byte(mappingJSON), 0644)
	if err != nil {
		logging.ErrorLogger.Error(err)
		response.I18nError(ctx, "file.create_failed", "error", err.Error())
		os.RemoveAll(uploadDir)
		return
	}

	// 校验映射文件格式是否正确
	if err := validateMappingFile(mappingFilePath, language); err != nil {
		logging.ErrorLogger.Error(err)
		response.I18nError(ctx, "convert.mapping_error", "error", err.Error())
		os.RemoveAll(uploadDir)
		return
	}

	logging.DebugLogger.Infof(getI18nMessageWithParams("debug.using_auto_mapping_file", getLanguageFromContext(ctx), map[string]interface{}{"file": mappingFilePath}))

	// 保存配置文件
	configUpload := filepath.Join(uploadDir, configFileHeader.Filename)
	_, err = ctx.SaveFormFile(configFileHeader, configUpload)
	if err != nil {
		// 增强错误处理，检查是否是文件名过长错误
		if strings.Contains(err.Error(), "file name too long") {
			logging.ErrorLogger.Errorf(getI18nMessageWithParams("internal.filename_too_long_save_failed", language, map[string]interface{}{"error": err.Error()}))
			response.I18nError(ctx, "file.name_too_long", "max_length", "248")
		} else {
			logging.ErrorLogger.Error(getI18nMessageWithParams("internal.save_file_failed", language, map[string]interface{}{"error": err}))
			response.I18nError(ctx, "file.save_failed", "error", err.Error())
		}
		os.RemoveAll(uploadDir)
		return
	}

	// 验证配置文件内容是否有效
	if !isValidConfigFile(configUpload) {
		logging.ErrorLogger.Errorf(getI18nMessageWithParams("internal.invalid_config_content", language, map[string]interface{}{"path": configUpload}))
		response.I18nError(ctx, "file.invalid_content")
		os.RemoveAll(uploadDir)
		return
	}

	// 计算配置文件MD5
	md5, err := libs.GetFileMd5(configUpload)
	if err != nil {
		logging.ErrorLogger.Errorf(getI18nMessageWithParams("internal.calculate_xml_md5_failed", language, map[string]interface{}{"error": err}))
	}

	// 使用原始文件名，因为数据库字段现在支持最多248个字符
	processedFileName := configFileHeader.Filename

	// 创建任务记录
	jobData := map[string]interface{}{
		"JobID":            jobID,
		"InputFileName":    processedFileName, // 使用处理后的文件名
		"InputMD5":         md5,
		"Status":           0,                // 0-处理中 1-成功 2-失败
		"Mode":             mode,             // 工作模式：convert-转换
		"Model":            request.Model,    // 保存前端传递的型号全称
		"Version":          request.Version,  // 保存前端传递的版本全称
		"ModelShortName":   modelShortName,   // 保存型号简写，用于Python引擎
		"VersionShortName": versionShortName, // 保存版本简写，用于Python引擎
		"Vendor":           vendor,           // 设置厂商标识
		"MappingFilePath":  mappingFilePath,  // 保存接口映射文件路径
		"MappingFileName":  mappingFileName,  // 保存映射文件名
		"OutputFileName":   "",               // 添加默认空值，满足非空约束
		"OutputMD5":        "",               // 添加默认空值，满足非空约束
		"UserID":           0,                // 添加默认用户ID为0，移除用户依赖
		"Language":         language,         // 设置语言参数
		"CreatedAt":        time.Now(),
		"UpdatedAt":        time.Now(),
	}

	job := dconfigtrans.ConfigTrans{}
	if err := job.Create(jobData); err != nil {
		logging.ErrorLogger.Error(err)
		os.RemoveAll(uploadDir)
		response.I18nError(ctx, "system.error_with_message", "error", err.Error())
		return
	}

	// 查找创建的任务
	err = job.FindEx("job_id", jobID)
	if err != nil {
		response.I18nError(ctx, "system.error_with_message", "error", err.Error())
		return
	}

	// 标记任务已成功创建
	taskCreated = true

	// 异步执行转换任务
	if job.ID > 0 {
		go func() {
			// 确保在任务完成后释放任务槽位
			defer func() {
				if err := taskCounter.ReleaseTaskSlot(vendor); err != nil {
					logging.ErrorLogger.Errorf(getI18nMessageWithParams("internal.task_slot_release_failed_after_completion", language, map[string]interface{}{"error": err}))
				}
			}()

			result, err := ConfigTransWorker(&job)
			if err != nil {
				logging.ErrorLogger.Error(err)
				// 保存详细的错误信息
				errMsg := err.Error()
				// 如果错误信息太长，截取前200个字符
				if len(errMsg) > 200 {
					errMsg = errMsg[:200] + getI18nMessage("internal.error_message_too_long_truncated", language)
				}
				err = job.Update(job.ID, map[string]interface{}{
					"Status": 2, // 失败
					"Result": errMsg,
				})
				if err != nil {
					logging.ErrorLogger.Error(err)
				}
			} else {
				err = job.Update(job.ID, map[string]interface{}{
					"Status": 1, // 成功
					"Result": result,
				})
				if err != nil {
					logging.ErrorLogger.Error(err)
				}
			}
		}()
	}

	// 返回任务ID
	// 获取当前语言并翻译消息
	currentLang := response.GetLanguage(ctx)
	translatedMessage := i18n.Translate(currentLang, "task.created_processing", nil)

	response.I18nSuccess(ctx, iris.Map{
		"job_id":  jobID,
		"status":  job.Status, // 添加初始状态信息 (0: 处理中)
		"message": translatedMessage,
	})
}

// ConfigTransWorker 通用配置转换工作执行函数
func ConfigTransWorker(job *dconfigtrans.ConfigTrans) (string, error) {
	// 获取上传文件路径
	uploadDir := filepath.Join(libs.Config.ConfigTrans.Upload, job.CreatedAt.Format("20060102"), job.JobID)
	inputFile := filepath.Join(uploadDir, job.InputFileName)

	// 创建日志目录和文件
	logDir := filepath.Join(uploadDir, "logs")
	os.MkdirAll(logDir, 0750)

	// 获取语言参数，默认使用中文
	jobLanguage := "zh-CN"
	if job.Language != "" {
		jobLanguage = job.Language
	}

	// 创建用户可见的日志文件（只包含必要信息，不包含敏感逻辑）
	userLogFile := filepath.Join(logDir, job.JobID+".log")
	userLogWriter, err := os.Create(userLogFile)
	if err != nil {
		logging.ErrorLogger.Errorf(getI18nMessageWithParams("internal.create_user_log_file_failed", jobLanguage, map[string]interface{}{"error": err}))
		return "", fmt.Errorf(getI18nMessageWithParams("error.create_log_file_failed", "zh-CN", map[string]interface{}{"error": err}))
	}
	defer userLogWriter.Close()

	// 创建系统内部调试日志（包含详细转换逻辑，不对用户公开）
	debugLogFile := filepath.Join(logDir, job.JobID+".debug.log")
	debugLogWriter, err := os.Create(debugLogFile)
	if err != nil {
		logging.ErrorLogger.Errorf(getI18nMessageWithParams("internal.create_debug_log_file_failed", jobLanguage, map[string]interface{}{"error": err}))
		// 调试日志创建失败不影响主流程
	} else {
		defer debugLogWriter.Close()
		// 记录详细的调试信息
		fmt.Fprintf(debugLogWriter, "%s\n", getI18nMessage("log.task_start", jobLanguage))
		fmt.Fprintf(debugLogWriter, "%s\n", getI18nMessageWithParams("log.task_id", jobLanguage, map[string]interface{}{"id": job.JobID}))
		fmt.Fprintf(debugLogWriter, "%s\n", getI18nMessageWithParams("log.vendor", jobLanguage, map[string]interface{}{"vendor": job.Vendor}))
		fmt.Fprintf(debugLogWriter, "%s\n", getI18nMessageWithParams("log.input_file", jobLanguage, map[string]interface{}{"file": job.InputFileName}))
		fmt.Fprintf(debugLogWriter, "%s: %s\n", getI18nMessage("log.input_file_path", jobLanguage), inputFile)
	}

	// 创建完整日志文件（包含所有日志信息，带标记区分用户日志和系统日志）
	fullLogFile := filepath.Join(logDir, job.JobID+".full.log")
	fullLogWriter, err := os.Create(fullLogFile)
	if err != nil {
		logging.ErrorLogger.Errorf(getI18nMessageWithParams("internal.create_full_log_file_failed", jobLanguage, map[string]interface{}{"error": err}))
		// 完整日志创建失败不影响主流程
	} else {
		defer fullLogWriter.Close()
		// 记录完整日志头部信息
		fmt.Fprintf(fullLogWriter, "=== %s ===\n", getI18nMessage("log.full_log_header", jobLanguage))
		fmt.Fprintf(fullLogWriter, "%s: %s\n", getI18nMessage("task.task_id", jobLanguage), job.JobID)
		fmt.Fprintf(fullLogWriter, "%s: %s\n", getI18nMessage("task.vendor", jobLanguage), job.Vendor)
		fmt.Fprintf(fullLogWriter, "%s: %s\n", getI18nMessage("task.model", jobLanguage), job.Model)
		fmt.Fprintf(fullLogWriter, "%s: %s\n", getI18nMessage("task.version", jobLanguage), job.Version)
		fmt.Fprintf(fullLogWriter, "%s: %s\n\n", getI18nMessage("task.start_time", jobLanguage), time.Now().Format("2006-01-02 15:04:05"))
	}

	// 记录开始信息（用户可见）
	startTime := time.Now()
	fmt.Fprintf(userLogWriter, "%s\n", getI18nMessage("log.task_start", jobLanguage))
	fmt.Fprintf(userLogWriter, "%s\n", getI18nMessageWithParams("log.task_id", jobLanguage, map[string]interface{}{"id": job.JobID}))
	fmt.Fprintf(userLogWriter, "%s\n", getI18nMessageWithParams("log.vendor", jobLanguage, map[string]interface{}{"vendor": job.Vendor}))
	fmt.Fprintf(userLogWriter, "%s\n", getI18nMessageWithParams("log.model", jobLanguage, map[string]interface{}{"model": job.Model}))
	fmt.Fprintf(userLogWriter, "%s\n", getI18nMessageWithParams("log.version", jobLanguage, map[string]interface{}{"version": job.Version}))
	fmt.Fprintf(userLogWriter, "%s\n", getI18nMessageWithParams("log.input_file", jobLanguage, map[string]interface{}{"file": job.InputFileName}))
	fmt.Fprintf(userLogWriter, "%s\n\n", getI18nMessageWithParams("log.start_time", jobLanguage, map[string]interface{}{"time": startTime.Format("2006-01-02 15:04:05")}))

	// 查找型号和版本的简写
	deviceModel, err := dconfigtrans.GetModelByName(job.Model, job.Vendor)
	if err != nil {
		errMsg := getI18nMessageWithParams("log.error_model_lookup", jobLanguage, map[string]interface{}{
			"model": job.Model,
			"error": err.Error(),
		})
		logging.ErrorLogger.Error(errMsg)
		fmt.Fprintf(userLogWriter, getI18nMessageWithParams("internal.error_prefix", jobLanguage, map[string]interface{}{"message": errMsg}))
		return "", fmt.Errorf(errMsg)
	}

	deviceVersion, err := dconfigtrans.GetVersionByName(job.Version, job.Vendor)
	if err != nil {
		errMsg := getI18nMessageWithParams("log.error_version_lookup", jobLanguage, map[string]interface{}{
			"version": job.Version,
			"error":   err.Error(),
		})
		logging.ErrorLogger.Error(errMsg)
		fmt.Fprintf(userLogWriter, getI18nMessageWithParams("internal.error_prefix", jobLanguage, map[string]interface{}{"message": errMsg}))
		return "", fmt.Errorf(errMsg)
	}

	// 获取型号和版本的简写
	modelShortName := deviceModel.ShortName
	versionShortName := deviceVersion.ShortName

	// 记录简写信息（仅调试日志可见）
	if debugLogWriter != nil {
		fmt.Fprintf(debugLogWriter, getI18nMessageWithParams("internal.model_short_name_mapping", jobLanguage, map[string]interface{}{"model": job.Model, "short_name": modelShortName})+"\n")
		fmt.Fprintf(debugLogWriter, getI18nMessageWithParams("internal.version_short_name_mapping", jobLanguage, map[string]interface{}{"version": job.Version, "short_name": versionShortName})+"\n")
	}
	if fullLogWriter != nil {
		fmt.Fprintf(fullLogWriter, "[SYS] "+getI18nMessageWithParams("internal.model_short_name_mapping", jobLanguage, map[string]interface{}{"model": job.Model, "short_name": modelShortName})+"\n")
		fmt.Fprintf(fullLogWriter, "[SYS] "+getI18nMessageWithParams("internal.version_short_name_mapping", jobLanguage, map[string]interface{}{"version": job.Version, "short_name": versionShortName})+"\n")
	}

	// 设置输出文件名称
	outputFileBaseName := fmt.Sprintf("%s-%s-%s.xml", job.Vendor, modelShortName, versionShortName)
	outputDir := filepath.Join(uploadDir, "output")
	os.MkdirAll(outputDir, 0750)

	outputFile := filepath.Join(outputDir, outputFileBaseName)
	encryptOutputFile := filepath.Join(outputDir, fmt.Sprintf("backup-startup.tar.gz"))

	// 获取接口映射文件路径
	mappingFile := job.MappingFilePath
	// 验证映射文件是否存在
	if _, err := os.Stat(mappingFile); err != nil {
		var i18nKey string
		if jobLanguage == "en-US" {
			i18nKey = "file.not_exists"
		} else {
			i18nKey = "file.not_exists"
		}
		errMsg := fmt.Sprintf("%s: %s, %v", getI18nMessage(i18nKey, jobLanguage), mappingFile, err)
		logging.ErrorLogger.Error(errMsg)
		return "", fmt.Errorf(errMsg)
	}

	// 验证映射文件格式
	if err := validateMappingFile(mappingFile, jobLanguage); err != nil {
		var i18nKey string
		if jobLanguage == "en-US" {
			i18nKey = "convert.mapping_error"
		} else {
			i18nKey = "convert.mapping_error"
		}
		errMsg := getI18nMessageWithParams(i18nKey, jobLanguage, map[string]interface{}{"error": err.Error()})
		logging.ErrorLogger.Error(errMsg)
		return "", fmt.Errorf(errMsg)
	}

	logging.DebugLogger.Infof(getI18nMessageWithParams("debug.using_mapping_file", "zh-CN", map[string]interface{}{"file": mappingFile}))

	// 记录调试信息（仅内部可见）
	if debugLogWriter != nil {
		fmt.Fprintf(debugLogWriter, "使用接口映射文件: %s\n", mappingFile)
		fmt.Fprintf(debugLogWriter, "输出XML文件: %s\n", outputFile)
		fmt.Fprintf(debugLogWriter, "输出加密文件: %s\n", encryptOutputFile)
	}

	// 记录完整日志信息
	if fullLogWriter != nil {
		fmt.Fprintf(fullLogWriter, "[SYS] 使用接口映射文件: %s\n", mappingFile)
		fmt.Fprintf(fullLogWriter, "[SYS] 输出XML文件: %s\n", outputFile)
		fmt.Fprintf(fullLogWriter, "[SYS] 输出加密文件: %s\n", encryptOutputFile)
		fmt.Fprintf(fullLogWriter, "[USER] 使用接口映射文件: 已加载\n")
	}

	// 用户日志中只记录使用了映射文件（不显示具体路径）
	fmt.Fprintf(userLogWriter, "%s\n", getI18nMessage("log.mapping_file_loaded", jobLanguage))

	// 执行转换
	fmt.Fprintf(userLogWriter, "\n%s\n", getI18nMessage("log.conversion_start", jobLanguage))
	if debugLogWriter != nil {
		fmt.Fprintf(debugLogWriter, "\n%s\n", getI18nMessage("log.conversion_start", jobLanguage))
	}
	if fullLogWriter != nil {
		fmt.Fprintf(fullLogWriter, "\n%s\n", getI18nMessage("log.conversion_start", jobLanguage))
	}

	// 创建转换引擎详细日志文件（仅内部使用）
	engineDebugLog := filepath.Join(logDir, job.JobID+".engine.debug.log")

	// 创建转换引擎用户日志文件（用户可见，只包含必要信息）
	engineUserLog := filepath.Join(outputDir, "conversion_summary.log")

	// 执行转换，传入日志目录而不是具体的日志文件路径
	// Python侧应该根据--log-dir参数在该目录下生成引擎日志
	// 使用型号和版本的简写与Python转换引擎交互
	success, message, err := convertConfig(job.Vendor, inputFile, mappingFile, modelShortName, versionShortName, outputFile, encryptOutputFile, logDir, jobLanguage)

	// 解析转换结果中的日志和总结信息
	var conversionResult map[string]interface{}
	if err == nil && success {
		// 检查是否存在加密错误标记文件
		errorMarker := encryptOutputFile + ".error"
		if _, statErr := os.Stat(errorMarker); statErr == nil {
			// 如果存在错误标记文件，读取错误信息
			errorContent, readErr := os.ReadFile(errorMarker)
			if readErr == nil {
				errorMsg := string(errorContent)
				logging.DebugLogger.Errorf(getI18nMessageWithParams("debug.encryption_failed", "zh-CN", map[string]interface{}{"error": errorMsg}))
				// 将加密失败标记为转换失败
				success = false
				message = getI18nMessageWithParams("internal.encryption_failed_with_message", jobLanguage, map[string]interface{}{"message": errorMsg})
			} else {
				logging.DebugLogger.Errorf(getI18nMessageWithParams("debug.cannot_read_encrypt_error_file", "zh-CN", map[string]interface{}{"error": readErr}))
				success = false
				message = getI18nMessage("internal.encryption_failed_no_details", jobLanguage)
			}
		} else {
			// 检查加密输出文件是否存在且有效
			if fi, statErr := os.Stat(encryptOutputFile); statErr != nil || fi.Size() == 0 {
				logging.DebugLogger.Errorf(getI18nMessageWithParams("debug.encrypt_output_file_missing_or_empty", "zh-CN", map[string]interface{}{"file": encryptOutputFile}))
				success = false
				message = getI18nMessage("internal.encryption_failed_invalid_output", jobLanguage)
			}
		}

		// 尝试读取转换结果文件，该文件应该由Python引擎生成
		resultFile := filepath.Join(logDir, "conversion_result.json")
		if _, statErr := os.Stat(resultFile); statErr == nil {
			resultContent, readErr := os.ReadFile(resultFile)
			if readErr == nil {
				var result map[string]interface{}
				if jsonErr := json.Unmarshal(resultContent, &result); jsonErr == nil {
					conversionResult = result
					logging.DebugLogger.Infof(getI18nMessage("debug.conversion_result_file_read", "zh-CN"))
				}
			}
		}
	}

	// 处理转换结果中的日志和总结信息
	if conversionResult != nil {
		// 处理详细日志
		if logsData, ok := conversionResult["logs"].([]interface{}); ok && len(logsData) > 0 {
			logs := make([]firewallflextrans.LogEntry, 0, len(logsData))

			for _, logItem := range logsData {
				if logMap, ok := logItem.(map[string]interface{}); ok {
					entry := firewallflextrans.LogEntry{}

					if time, ok := logMap["time"].(string); ok {
						entry.Time = time
					}

					if level, ok := logMap["level"].(string); ok {
						entry.Level = level
					}

					if message, ok := logMap["message"].(string); ok {
						entry.Message = message
					}

					if module, ok := logMap["module"].(string); ok {
						entry.Module = module
					}

					logs = append(logs, entry)
				}
			}

			// 将详细日志写入用户日志文件
			fmt.Fprintf(userLogWriter, "\n%s\n", getI18nMessage("log.detailed_logs", jobLanguage))
			WriteDetailedLogs(userLogWriter, logs)
		}

		// 处理转换总结
		var summary *firewallflextrans.ConversionSummary
		if summaryData, ok := conversionResult["summary"].(map[string]interface{}); ok {
			summary = &firewallflextrans.ConversionSummary{}

			// 解析基本计数
			if val, ok := summaryData["total_interfaces"].(float64); ok {
				summary.TotalInterfaces = int(val)
			}

			if val, ok := summaryData["mapped_interfaces"].(float64); ok {
				summary.MappedInterfaces = int(val)
			}

			if val, ok := summaryData["unmapped_interfaces"].(float64); ok {
				summary.UnmappedInterfaces = int(val)
			}

			if val, ok := summaryData["zones"].(float64); ok {
				summary.Zones = int(val)
			}

			if val, ok := summaryData["static_routes"].(float64); ok {
				summary.StaticRoutes = int(val)
			}

			if val, ok := summaryData["address_objects"].(float64); ok {
				summary.AddressObjects = int(val)
			}

			if val, ok := summaryData["service_objects"].(float64); ok {
				summary.ServiceObjects = int(val)
			}

			if val, ok := summaryData["policy_rules"].(float64); ok {
				summary.PolicyRules = int(val)
			}

			// 解析跳过的项目
			if skippedItems, ok := summaryData["skipped_items"].([]interface{}); ok {
				skipped := make([]firewallflextrans.SkippedItem, 0, len(skippedItems))

				for _, item := range skippedItems {
					if itemMap, ok := item.(map[string]interface{}); ok {
						skippedItem := firewallflextrans.SkippedItem{}

						if val, ok := itemMap["type"].(string); ok {
							skippedItem.Type = val
						}

						if val, ok := itemMap["name"].(string); ok {
							skippedItem.Name = val
						}

						if val, ok := itemMap["reason"].(string); ok {
							skippedItem.Reason = val
						}

						if val, ok := itemMap["detail"].(string); ok {
							skippedItem.Detail = val
						}

						skipped = append(skipped, skippedItem)
					}
				}

				summary.SkippedItems = skipped
			}

			// 解析需要手动配置的项目
			if manualItems, ok := summaryData["manual_config_required"].([]interface{}); ok {
				manual := make([]firewallflextrans.ManualConfigItem, 0, len(manualItems))

				for _, item := range manualItems {
					if itemMap, ok := item.(map[string]interface{}); ok {
						manualItem := firewallflextrans.ManualConfigItem{}

						if val, ok := itemMap["type"].(string); ok {
							manualItem.Type = val
						}

						if val, ok := itemMap["name"].(string); ok {
							manualItem.Name = val
						}

						if val, ok := itemMap["reason"].(string); ok {
							manualItem.Reason = val
						}

						if val, ok := itemMap["detail"].(string); ok {
							manualItem.Detail = val
						}

						manual = append(manual, manualItem)
					}
				}

				summary.ManualConfigRequired = manual
			}
		}

		// 将转换总结写入用户日志文件
		WriteConversionSummary(userLogWriter, summary)
	}

	// 转换完成后，尝试将引擎日志合并到对应的日志文件中
	// Python引擎应在logDir中生成引擎调试日志
	engineDebugLog = filepath.Join(logDir, job.JobID+".engine.debug.log") // 期望Python引擎生成的日志路径
	engineUserLog = filepath.Join(logDir, "conversion_summary.log")       // 期望Python引擎生成的用户日志路径

	// 合并引擎调试日志到调试日志
	if _, err := os.Stat(engineDebugLog); err == nil {
		engineDebugContent, readErr := os.ReadFile(engineDebugLog)
		if readErr == nil && len(engineDebugContent) > 0 && debugLogWriter != nil {
			fmt.Fprintf(debugLogWriter, "\n%s\n", getI18nMessage("log.engine_debug_logs", jobLanguage))
			fmt.Fprintf(debugLogWriter, "%s\n", string(engineDebugContent))
			logging.InfoLogger.Infof(getI18nMessage("info.engine_debug_log_merged", "zh-CN"))
		}
	}

	// 合并引擎用户日志到用户日志
	if _, err := os.Stat(engineUserLog); err == nil {
		engineUserContent, readErr := os.ReadFile(engineUserLog)
		if readErr == nil && len(engineUserContent) > 0 {
			fmt.Fprintf(userLogWriter, "\n%s\n", getI18nMessage("log.engine_conversion_details", jobLanguage))
			fmt.Fprintf(userLogWriter, "%s\n", string(engineUserContent))
			logging.InfoLogger.Infof(getI18nMessage("info.engine_user_log_merged", "zh-CN"))
		}
	}

	// 记录转换结果
	if err != nil {
		if debugLogWriter != nil {
			fmt.Fprintf(debugLogWriter, "%s %v\n%s\n", getI18nMessage("log.conversion_failed", jobLanguage), err, message)
		}
		if fullLogWriter != nil {
			fmt.Fprintf(fullLogWriter, "[SYS] %s %v\n%s\n", getI18nMessage("log.conversion_failed", jobLanguage), err, message)
			fmt.Fprintf(fullLogWriter, "[USER] %s %v\n", getI18nMessage("log.conversion_failed", jobLanguage), err)
		}
		fmt.Fprintf(userLogWriter, "%s %v\n", getI18nMessage("log.conversion_failed", jobLanguage), err)

		// 尝试从错误日志中获取更多详细信息
		errorLog := filepath.Join(logDir, "error.log")
		errorDetails := ""
		if _, statErr := os.Stat(errorLog); statErr == nil {
			errorContent, readErr := os.ReadFile(errorLog)
			if readErr == nil && len(errorContent) > 0 {
				errorDetails = string(errorContent)
				// 将错误详情写入用户日志
				fmt.Fprintf(userLogWriter, "\n%s\n%s\n", getI18nMessage("log.error_details", jobLanguage), errorDetails)
				if fullLogWriter != nil {
					fmt.Fprintf(fullLogWriter, "[USER] %s\n%s\n", getI18nMessage("log.error_details", jobLanguage), errorDetails)
				}
			}
		}

		// 如果有Python引擎日志，尝试提取相关错误信息
		engineLog := filepath.Join(logDir, job.JobID+".engine.debug.log")
		if _, statErr := os.Stat(engineLog); statErr == nil {
			engineContent, readErr := os.ReadFile(engineLog)
			if readErr == nil && len(engineContent) > 0 {
				// 提取最后几行可能包含错误信息的日志
				engineLines := extractLastLines(string(engineContent), 10)
				if engineLines != "" {
					fmt.Fprintf(userLogWriter, "\n%s\n%s\n", getI18nMessage("log.python_engine_errors", jobLanguage), engineLines)
					if fullLogWriter != nil {
						fmt.Fprintf(fullLogWriter, "[USER] %s\n%s\n", getI18nMessage("log.python_engine_errors", jobLanguage), engineLines)
					}
					// 将引擎错误信息添加到errorDetails中
					if errorDetails == "" {
						errorDetails = engineLines
					}
				}
			}
		}

		// 构建详细的错误信息
		detailedError := err.Error()
		if message != "" && !strings.Contains(detailedError, message) {
			detailedError = fmt.Sprintf("%s: %s", detailedError, message)
		}
		if errorDetails != "" {
			detailedError = fmt.Sprintf("%s\n%s", detailedError, getI18nMessageWithParams("internal.detailed_error_prefix", jobLanguage, map[string]interface{}{"details": errorDetails}))
		}

		return "", fmt.Errorf("%s", detailedError)
	}

	if !success {
		if debugLogWriter != nil {
			fmt.Fprintf(debugLogWriter, "%s %s\n", getI18nMessage("log.conversion_failed", jobLanguage), message)
		}
		if fullLogWriter != nil {
			fmt.Fprintf(fullLogWriter, "[SYS] %s %s\n", getI18nMessage("log.conversion_failed", jobLanguage), message)
			fmt.Fprintf(fullLogWriter, "[USER] %s %s\n", getI18nMessage("log.conversion_failed", jobLanguage), message) // 修改这里，将详细错误信息展示给用户
		}
		fmt.Fprintf(userLogWriter, "%s %s\n", getI18nMessage("log.conversion_failed", jobLanguage), message) // 修改这里，将详细错误信息展示给用户

		// 尝试从错误日志中获取更多详细信息
		errorLog := filepath.Join(logDir, "error.log")
		errorDetails := ""
		if _, statErr := os.Stat(errorLog); statErr == nil {
			errorContent, readErr := os.ReadFile(errorLog)
			if readErr == nil && len(errorContent) > 0 {
				errorDetails = string(errorContent)
				// 将错误详情写入用户日志
				fmt.Fprintf(userLogWriter, "\n%s\n", getI18nMessage("log.error_details", jobLanguage))
				fmt.Fprintf(userLogWriter, "%s\n", errorDetails)
				if fullLogWriter != nil {
					fmt.Fprintf(fullLogWriter, "[USER] %s\n", getI18nMessage("log.error_details", jobLanguage))
					fmt.Fprintf(fullLogWriter, "%s\n", errorDetails)
				}
			}
		}

		// 如果有Python引擎日志，尝试提取相关错误信息
		engineLog := filepath.Join(logDir, job.JobID+".engine.debug.log")
		if _, statErr := os.Stat(engineLog); statErr == nil {
			engineContent, readErr := os.ReadFile(engineLog)
			if readErr == nil && len(engineContent) > 0 {
				// 提取最后几行可能包含错误信息的日志
				engineLines := extractLastLines(string(engineContent), 10)
				if engineLines != "" {
					fmt.Fprintf(userLogWriter, "\n%s\n", getI18nMessage("log.engine_error_details", jobLanguage))
					fmt.Fprintf(userLogWriter, "%s\n", engineLines)
					if fullLogWriter != nil {
						fmt.Fprintf(fullLogWriter, "[USER] %s\n", getI18nMessage("log.engine_error_details", jobLanguage))
						fmt.Fprintf(fullLogWriter, "%s\n", engineLines)
					}
					// 将引擎错误信息添加到errorDetails中
					if errorDetails == "" {
						errorDetails = engineLines
					}
				}
			}
		}

		// 构建详细的错误信息
		detailedError := message
		// if errorDetails != "" {
		// 	detailedError = fmt.Sprintf("%s\n详细错误: %s", message, errorDetails)
		// }

		return "", fmt.Errorf("%s", detailedError)
	}

	fmt.Fprintf(userLogWriter, "%s\n", getI18nMessage("log.conversion_success", jobLanguage))
	if debugLogWriter != nil {
		fmt.Fprintf(debugLogWriter, "%s: %s\n", getI18nMessage("log.conversion_success", jobLanguage), message)
	}
	if fullLogWriter != nil {
		fmt.Fprintf(fullLogWriter, "[USER] %s\n", getI18nMessage("log.conversion_success", jobLanguage))
		fmt.Fprintf(fullLogWriter, "[SYS] %s: %s\n", getI18nMessage("log.conversion_success", jobLanguage), message)
	}

	// 更新任务信息：XML文件
	xmlMD5, err := libs.GetFileMd5(outputFile)
	if err != nil {
		logging.ErrorLogger.Errorf(getI18nMessageWithParams("internal.calculate_xml_md5_failed", jobLanguage, map[string]interface{}{"error": err}))
		if debugLogWriter != nil {
			fmt.Fprintf(debugLogWriter, getI18nMessageWithParams("internal.calculate_xml_md5_failed", jobLanguage, map[string]interface{}{"error": err})+"\n")
		}
		if fullLogWriter != nil {
			fmt.Fprintf(fullLogWriter, "[SYS] "+getI18nMessageWithParams("internal.calculate_xml_md5_failed", jobLanguage, map[string]interface{}{"error": err})+"\n")
			fmt.Fprintf(fullLogWriter, "[USER] %s\n", getI18nMessage("log.xml_file_no_checksum", jobLanguage))
		}
		fmt.Fprintf(userLogWriter, "%s\n", getI18nMessage("log.xml_file_no_checksum", jobLanguage))
	} else {
		logging.InfoLogger.Infof(getI18nMessageWithParams("internal.xml_file_generated_successfully", jobLanguage, map[string]interface{}{"file": outputFile}))
		if debugLogWriter != nil {
			fmt.Fprintf(debugLogWriter, getI18nMessageWithParams("internal.xml_file_generated_successfully", jobLanguage, map[string]interface{}{"file": outputFile})+" (MD5: %s)\n", xmlMD5)
		}
		if fullLogWriter != nil {
			fmt.Fprintf(fullLogWriter, "[SYS] "+getI18nMessageWithParams("internal.xml_file_generated_successfully", jobLanguage, map[string]interface{}{"file": outputFile})+" (MD5: %s)\n", xmlMD5)
			fmt.Fprintf(fullLogWriter, "[USER] %s\n", getI18nMessageWithParams("log.xml_file_success", jobLanguage, map[string]interface{}{"file": outputFileBaseName}))
		}
		// 用户日志中不显示完整路径，只显示文件名
		fmt.Fprintf(userLogWriter, "%s\n", getI18nMessageWithParams("log.xml_file_success", jobLanguage, map[string]interface{}{"file": outputFileBaseName}))
	}

	// 更新任务信息：加密文件
	encryptMD5 := ""
	if _, err := os.Stat(encryptOutputFile); err == nil {
		encryptMD5, _ = libs.GetFileMd5(encryptOutputFile)
		if encryptMD5 != "" {
			logging.InfoLogger.Infof(getI18nMessageWithParams("info.encrypt_file_generated", "zh-CN", map[string]interface{}{"file": encryptOutputFile}))
			// 记录调试信息
			if debugLogWriter != nil {
				fmt.Fprintf(debugLogWriter, "加密文件生成成功: %s (MD5: %s)\n", encryptOutputFile, encryptMD5)
			}
			if fullLogWriter != nil {
				fmt.Fprintf(fullLogWriter, "[SYS] 加密文件生成成功: %s (MD5: %s)\n", encryptOutputFile, encryptMD5)
				fmt.Fprintf(fullLogWriter, "[USER] %s\n", getI18nMessage("log.encrypt_file_success", jobLanguage))
			}
			fmt.Fprintf(userLogWriter, "%s\n", getI18nMessage("log.encrypt_file_success", jobLanguage))
		}
	}

	// 计算总耗时
	totalTime := time.Since(startTime)
	logging.InfoLogger.Infof(getI18nMessageWithParams("info.conversion_complete_time", "zh-CN", map[string]interface{}{"time": totalTime}))
	fmt.Fprintf(userLogWriter, "\n%s\n", getI18nMessageWithParams("log.conversion_complete", jobLanguage, map[string]interface{}{"time": totalTime}))
	if debugLogWriter != nil {
		fmt.Fprintf(debugLogWriter, "\n%s: %s\n", getI18nMessage("log.conversion_complete", jobLanguage), totalTime)
	}
	if fullLogWriter != nil {
		fmt.Fprintf(fullLogWriter, "\n[SYS] %s: %s\n", getI18nMessage("log.conversion_complete", jobLanguage), totalTime)
		fmt.Fprintf(fullLogWriter, "[USER] %s\n", getI18nMessageWithParams("log.conversion_complete", jobLanguage, map[string]interface{}{"time": totalTime}))
	}

	// 如果配置了删除原始文件，则在转换完成后删除
	if libs.Config.ConfigTrans.DeleteOriginalFile {
		// 删除原始配置文件
		if err := os.Remove(inputFile); err != nil {
			logging.ErrorLogger.Errorf(getI18nMessageWithParams("internal.delete_original_file_failed", jobLanguage, map[string]interface{}{"error": err}))
			if debugLogWriter != nil {
				fmt.Fprintf(debugLogWriter, getI18nMessageWithParams("internal.delete_original_file_failed", jobLanguage, map[string]interface{}{"error": err})+"\n")
			}
			if fullLogWriter != nil {
				fmt.Fprintf(fullLogWriter, "[SYS] "+getI18nMessageWithParams("internal.delete_original_file_failed", jobLanguage, map[string]interface{}{"error": err})+"\n")
			}
		} else {
			logging.InfoLogger.Infof(getI18nMessageWithParams("internal.original_file_deleted_successfully", jobLanguage, map[string]interface{}{"file": inputFile}))
			if debugLogWriter != nil {
				fmt.Fprintf(debugLogWriter, getI18nMessageWithParams("internal.original_file_deleted_successfully", jobLanguage, map[string]interface{}{"file": inputFile})+"\n")
			}
			if fullLogWriter != nil {
				fmt.Fprintf(fullLogWriter, "[SYS] "+getI18nMessageWithParams("internal.original_file_deleted_successfully", jobLanguage, map[string]interface{}{"file": inputFile})+"\n")
				fmt.Fprintf(fullLogWriter, "[USER] %s\n", getI18nMessage("log.original_file_deleted", jobLanguage))
			}
			fmt.Fprintf(userLogWriter, "%s\n", getI18nMessage("log.original_file_deleted", jobLanguage))
		}
	}

	// 使用map批量更新任务信息
	updateData := map[string]interface{}{
		"Status": 1, // 1-成功
	}

	// 保存XML信息
	if xmlMD5 != "" {
		updateData["XmlFileName"] = outputFileBaseName
		updateData["XmlMD5"] = xmlMD5
	}

	// 保存加密信息
	if encryptMD5 != "" {
		updateData["OutputFileName"] = filepath.Base(encryptOutputFile)
		updateData["OutputMD5"] = encryptMD5
	} else if xmlMD5 != "" {
		// 如果没有加密文件，将XML设为主输出
		updateData["OutputFileName"] = outputFileBaseName
		updateData["OutputMD5"] = xmlMD5
	}

	// 保存更新
	if err := job.Update(job.ID, updateData); err != nil {
		logging.ErrorLogger.Errorf(getI18nMessageWithParams("internal.update_task_status_failed", jobLanguage, map[string]interface{}{"error": err}))
	}

	// 返回输出文件路径
	if encryptMD5 != "" {
		return encryptOutputFile, nil
	}
	return outputFile, nil
}

// GetTransJobs 通用转换任务列表API处理函数
func GetTransJobs(ctx iris.Context) {
	// 获取厂商信息
	vendor := getVendorParam(ctx)

	// 检查厂商是否支持
	if !isVendorSupported(vendor) {
		response.I18nError(ctx, "vendor.not_supported", "vendor", vendor, "supported", "fortigate")
		return
	}

	// 使用通用的任务列表获取逻辑
	getGenericTransJobs(ctx, vendor)
}

// getGenericTransJobs 获取通用的配置转换任务列表
func getGenericTransJobs(ctx iris.Context, vendor string) {
	page := ctx.URLParamIntDefault("page", 1)
	pageSize := ctx.URLParamIntDefault("pageSize", 10)
	mode := ctx.URLParamDefault("mode", "")
	status := ctx.URLParamDefault("status", "")
	sort := ctx.URLParamDefault("sort", "desc")
	orderBy := ctx.URLParamDefault("orderBy", "id")
	jobID := ctx.URLParamDefault("job_id", "")
	start := ctx.URLParamDefault("start", "")
	end := ctx.URLParamDefault("end", "")

	job := dconfigtrans.ConfigTrans{}
	list, err := job.All("", jobID, start, end, status, mode, vendor, sort, orderBy, page, pageSize)
	if err != nil {
		response.I18nError(ctx, "system.error_with_message", "error", err.Error())
		return
	}
	response.I18nSuccess(ctx, list)
}

// GetTransJobByJobID 通过job_id获取转换任务详情
func GetTransJobByJobID(ctx iris.Context) {
	// 获取厂商信息
	vendor := getVendorParam(ctx)

	// 检查厂商是否支持
	if !isVendorSupported(vendor) {
		response.I18nError(ctx, "vendor.not_supported", "vendor", vendor, "supported", "fortigate")
		return
	}

	// 使用通用的任务详情获取逻辑
	getGenericTransJobByJobID(ctx, vendor)
}

// getGenericTransJobByJobID 通过job_id获取通用的配置转换任务详情
func getGenericTransJobByJobID(ctx iris.Context, vendor string) {
	jobID := ctx.Params().Get("job_id")
	if jobID == "" {
		response.I18nError(ctx, "param.error")
		return
	}

	job := dconfigtrans.ConfigTrans{}
	err := job.FindEx("job_id", jobID)
	if err != nil {
		response.I18nError(ctx, "system.error_with_message", "error", err.Error())
		return
	}

	// 检查任务是否属于当前厂商
	if job.Vendor != vendor {
		response.I18nError(ctx, "vendor.not_supported", "vendor", job.Vendor, "supported", vendor)
		return
	}

	// 转换成map以便添加字段
	jobMap := map[string]interface{}{
		"job_id":           job.JobID,
		"vendor":           job.Vendor,
		"model":            job.Model,
		"version":          job.Version,
		"status":           job.Status,
		"input_file_name":  job.InputFileName,
		"output_file_name": job.OutputFileName,
		"created_at":       job.CreatedAt,
	}

	// 添加状态文本描述
	language := response.GetLanguage(ctx)
	jobMap["status_text"] = getStatusTextI18n(job.Status, language)

	// 如果任务失败，添加失败原因
	if job.Status == 2 && job.Result != "" {
		jobMap["error_reason"] = job.Result
	}

	// 添加下载链接，使用新的URL格式
	jobMap["download_url"] = fmt.Sprintf("/api/v1/firewallflextrans/download/%s", job.JobID)
	jobMap["log_url"] = fmt.Sprintf("/api/v1/firewallflextrans/log/%s", job.JobID)
	jobMap["view_raw_log_url"] = fmt.Sprintf("/api/v1/firewallflextrans/view-raw-log/%s", job.JobID)

	response.I18nSuccess(ctx, jobMap)
}

// DownloadTransFile 通用下载转换后文件API处理函数
func DownloadTransFile(ctx iris.Context) {
	// 获取厂商信息
	vendor := getVendorParam(ctx)

	// 检查厂商是否支持
	if !isVendorSupported(vendor) {
		response.I18nError(ctx, "vendor.not_supported", "vendor", vendor, "supported", "fortigate")
		return
	}

	// 获取job_id
	jobID := ctx.Params().Get("job_id")
	if jobID == "" {
		response.I18nError(ctx, "param.error")
		return
	}

	job := dconfigtrans.ConfigTrans{}
	err := job.FindEx("job_id", jobID)
	if err != nil {
		response.I18nError(ctx, "system.error_with_message", "error", err.Error())
		return
	}

	// 检查任务是否属于当前厂商
	if job.Vendor != vendor {
		response.I18nError(ctx, "vendor.not_supported", "vendor", job.Vendor, "supported", vendor)
		return
	}

	// 检查转换是否完成
	if job.Status != 1 {
		response.I18nError(ctx, "task.status.failed")
		return
	}

	// 获取文件类型参数，默认为加密文件
	fileType := ctx.URLParamDefault("type", "encrypt")

	uploadDir := filepath.Join(libs.Config.ConfigTrans.Upload, job.CreatedAt.Format("20060102"), job.JobID)
	outputDir := filepath.Join(uploadDir, "output")

	// 根据请求类型选择不同的文件
	if fileType == "xml" && job.XmlFileName != "" {
		// 下载XML文件
		filePath := filepath.Join(outputDir, job.XmlFileName)
		if _, err := os.Stat(filePath); os.IsNotExist(err) {
			response.I18nError(ctx, "file.not_exists")
			return
		}
		ctx.Header("Content-Type", "application/xml")
		ctx.SendFile(filePath, job.XmlFileName)
		return
	} else {
		// 下载加密文件（默认）
		if job.OutputFileName == "" {
			response.I18nError(ctx, "file.not_exists")
			return
		}

		filePath := filepath.Join(outputDir, job.OutputFileName)
		if _, err := os.Stat(filePath); os.IsNotExist(err) {
			response.I18nError(ctx, "file.not_exists")
			return
		}
		ctx.Header("Content-Type", "application/octet-stream")
		ctx.SendFile(filePath, job.OutputFileName)
		return
	}
}

// DownloadTransLog 通用下载转换日志API处理函数
func DownloadTransLog(ctx iris.Context) {
	// 获取厂商信息
	vendor := getVendorParam(ctx)

	// 检查厂商是否支持
	if !isVendorSupported(vendor) {
		response.I18nError(ctx, "vendor.not_supported", "vendor", vendor, "supported", "fortigate")
		return
	}

	// 获取job_id
	jobID := ctx.Params().Get("job_id")
	if jobID == "" {
		response.I18nError(ctx, "param.error")
		return
	}

	job := dconfigtrans.ConfigTrans{}
	err := job.FindEx("job_id", jobID)
	if err != nil {
		response.I18nError(ctx, "system.error_with_message", "error", err.Error())
		return
	}

	if job.JobID == "" {
		response.I18nError(ctx, "file.not_exists", "file", getI18nMessage("file.user_log", getLanguageFromContext(ctx)))
		return
	}

	// 检查任务是否属于当前厂商
	if job.Vendor != vendor {
		response.I18nError(ctx, "vendor.not_supported", "vendor", job.Vendor, "supported", vendor)
		return
	}

	// 设置日志文件路径 - 与ViewRawLog函数使用相同的用户日志文件
	uploadDir := filepath.Join(libs.Config.ConfigTrans.Upload, job.CreatedAt.Format("20060102"), job.JobID)
	logDir := filepath.Join(uploadDir, "logs")

	// 直接使用用户日志文件，与ViewRawLog函数保持一致
	logFile := filepath.Join(logDir, job.JobID+".log")
	logFileName := fmt.Sprintf("%s_log.txt", job.JobID)

	// 检查文件是否存在
	if _, err := os.Stat(logFile); os.IsNotExist(err) {
		response.I18nError(ctx, "file.not_exists", "file", getI18nMessage("file.user_log", getLanguageFromContext(ctx)))
		return
	}

	// 下载文件
	ctx.Header("Content-Type", "application/octet-stream; charset=utf-8")
	ctx.SendFile(logFile, logFileName)
}

// ViewTransLog 查看任务转换日志
func ViewTransLog(ctx iris.Context) {
	// 获取厂商信息
	vendor := getVendorParam(ctx)

	// 检查厂商是否支持
	if !isVendorSupported(vendor) {
		response.I18nError(ctx, "vendor.not_supported", "vendor", vendor, "supported", "fortigate")
		return
	}

	// 获取job_id
	jobID := ctx.Params().Get("job_id")
	if jobID == "" {
		response.I18nError(ctx, "param.job_id_required")
		return
	}

	job := dconfigtrans.ConfigTrans{}
	err := job.FindEx("job_id", jobID)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	if job.JobID == "" {
		response.I18nError(ctx, "file.not_exists")
		return
	}

	// 检查任务是否属于当前厂商
	if job.Vendor != vendor {
		response.I18nError(ctx, "task.vendor_mismatch", "job_id", jobID, "vendor", vendor)
		return
	}

	// 获取当前语言
	language := getLanguageFromContext(ctx)

	// 设置日志文件路径
	uploadDir := filepath.Join(libs.Config.ConfigTrans.Upload, job.CreatedAt.Format("20060102"), job.JobID)
	logDir := filepath.Join(uploadDir, "logs")

	// 只获取用户日志
	userLogFile := filepath.Join(logDir, job.JobID+".log")

	if _, err := os.Stat(userLogFile); os.IsNotExist(err) {
		response.I18nError(ctx, "file.not_exists")
		return
	}

	content, err := os.ReadFile(userLogFile)
	if err != nil {
		response.I18nError(ctx, "file.read_failed", "error", err.Error())
		return
	}

	// 直接使用日志内容
	logContent := string(content)

	// 获取国际化的页面标题
	logTitle := i18n.Translate(language, "convert.log_view", nil)

	// 构建全屏显示的HTML页面
	html := fmt.Sprintf(`
	<!DOCTYPE html>
	<html>
	<head>
		<title>%s - %s</title>
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<style>
			body {
				font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
				margin: 0;
				padding: 0;
				background-color: #f0f2f5;
				color: #333;
				line-height: 1.5;
				height: 100vh;
				display: flex;
				flex-direction: column;
			}
			
			.header {
				background-color: #1890ff;
				color: white;
				padding: 10px 20px;
				display: flex;
				justify-content: space-between;
				align-items: center;
				box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
			}
			
			.header h1 {
				font-size: 18px;
				margin: 0;
			}
			
			.job-info {
				display: flex;
				flex-wrap: wrap;
				gap: 15px;
				font-size: 14px;
			}
			
			.job-info-item {
				display: flex;
				align-items: center;
			}
			
			.job-info-label {
				font-weight: 500;
				margin-right: 5px;
			}
			
			.status-badge {
				display: inline-block;
				padding: 2px 8px;
				border-radius: 10px;
				font-size: 12px;
				font-weight: 500;
			}
			
			.status-processing {
				background-color: #e6f7ff;
				color: #1890ff;
			}
			
			.status-success {
				background-color: #f6ffed;
				color: #52c41a;
			}
			
			.status-failed {
				background-color: #fff2f0;
				color: #f5222d;
			}
			
			.content {
				flex: 1;
				overflow: auto;
				padding: 20px;
			}
			
			.log-content {
				background-color: #fff;
				border-radius: 4px;
				padding: 15px;
				box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
				font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace;
				font-size: 14px;
				white-space: pre-wrap;
				word-wrap: break-word;
				overflow-x: auto;
				line-height: 1.6;
			}
			
			/* 日志级别颜色 */
			.info {
				color: #1890ff;
			}
			
			.warning {
				color: #faad14;
			}
			
			.error {
				color: #f5222d;
			}
			
			.success {
				color: #52c41a;
			}
			
			.footer {
				background-color: #f0f2f5;
				border-top: 1px solid #e8e8e8;
				padding: 10px 20px;
				text-align: center;
				font-size: 12px;
				color: #777;
			}
			
			@media (max-width: 768px) {
				.header {
					flex-direction: column;
					align-items: flex-start;
				}
				
				.job-info {
					margin-top: 10px;
					flex-direction: column;
					gap: 5px;
				}
				
				.content {
					padding: 10px;
				}
				
				.log-content {
					font-size: 13px;
					padding: 10px;
				}
			}
		</style>
	</head>
	<body>
		<div class="header">
			<h1>%s</h1>
			<div class="job-info">
				<div class="job-info-item">
					<div class="job-info-label">%s:</div>
					<div class="job-info-value">%s</div>
				</div>
				<div class="job-info-item">
					<div class="job-info-label">%s:</div>
					<div class="job-info-value">%s</div>
				</div>
				<div class="job-info-item">
					<div class="job-info-label">%s:</div>
					<div class="job-info-value">%s</div>
				</div>
				<div class="job-info-item">
					<div class="job-info-label">%s:</div>
					<div class="job-info-value">%s</div>
				</div>
				<div class="job-info-item">
					<div class="job-info-label">%s:</div>
					<div class="job-info-value"><span class="status-badge status-%s">%s</span></div>
				</div>
			</div>
		</div>
		
		<div class="content">
			<div class="log-content">%s</div>
		</div>
		
		<div class="footer">
			<p>%s: %s</p>
		</div>
		
		<script>
			// 语法高亮处理
			document.addEventListener('DOMContentLoaded', function() {
				const logContent = document.querySelector('.log-content');
				let html = logContent.innerHTML;
				
				// 为INFO级别的日志添加样式
				html = html.replace(/\[INFO\]/g, '<span class="info">[INFO]</span>');
				
				// 为WARNING级别的日志添加样式
				html = html.replace(/\[WARNING\]/g, '<span class="warning">[WARNING]</span>');
				
				// 为ERROR级别的日志添加样式
				html = html.replace(/\[ERROR\]/g, '<span class="error">[ERROR]</span>');
				
				// 为SUCCESS级别的日志添加样式
				html = html.replace(/\[SUCCESS\]/g, '<span class="success">[SUCCESS]</span>');
				
				// 高亮分隔符
				html = html.replace(/(===.*===)/g, '<strong>$1</strong>');
				
				logContent.innerHTML = html;
				
				// 滚动到底部
				logContent.scrollTop = logContent.scrollHeight;
			});
		</script>
	</body>
	</html>
	`,
		logTitle, jobID, // 页面标题
		logTitle, // 标题栏
		i18n.Translate(language, "task.task_id", nil), job.JobID,
		i18n.Translate(language, "task.vendor", nil), job.Vendor,
		i18n.Translate(language, "task.model", nil), job.Model,
		i18n.Translate(language, "task.version", nil), job.Version,
		i18n.Translate(language, "task.status_name", nil), getStatusClass(job.Status), getStatusTextI18n(job.Status, language),
		// 日志内容
		formatSimpleLogContent(logContent),
		// 底部时间
		i18n.Translate(language, "task.view_time", nil), time.Now().Format("2006-01-02 15:04:05"))

	ctx.HTML(html)
}

// formatSimpleLogContent 简单格式化日志内容，添加基本的颜色
func formatSimpleLogContent(content string) string {
	// 转义HTML特殊字符
	content = strings.Replace(content, "&", "&amp;", -1)
	content = strings.Replace(content, "<", "&lt;", -1)
	content = strings.Replace(content, ">", "&gt;", -1)

	// 为不同级别的日志添加颜色
	content = regexp.MustCompile(`(INFO|信息)`).ReplaceAllString(content, `<span class="info">$1</span>`)
	content = regexp.MustCompile(`(WARNING|警告)`).ReplaceAllString(content, `<span class="warning">$1</span>`)
	content = regexp.MustCompile(`(ERROR|错误)`).ReplaceAllString(content, `<span class="error">$1</span>`)
	content = regexp.MustCompile(`(SUCCESS|成功)`).ReplaceAllString(content, `<span class="success">$1</span>`)

	// 高亮显示分隔符
	content = regexp.MustCompile(`(===.*===)`).ReplaceAllString(content, `<strong>$1</strong>`)

	return content
}

// processLogContent 处理日志内容，将其分类并格式化为HTML
func processLogContent(logContent string) string {
	lines := strings.Split(logContent, "\n")

	// 定义日志分类
	processLogs := []string{}       // 处理过程日志
	summaryLogs := []string{}       // 转换总结日志
	statisticsLogs := []string{}    // 统计信息日志
	manualConfigLogs := []string{}  // 需要手动配置的项目
	compatibilityLogs := []string{} // 兼容性问题

	// 当前处理的分类
	currentCategory := "process"

	// 统计各类型日志数量
	infoCount := 0
	warningCount := 0
	errorCount := 0

	// 处理每一行日志
	for _, line := range lines {
		trimmedLine := strings.TrimSpace(line)

		// 跳过空行
		if trimmedLine == "" {
			continue
		}

		// 检查是否是分类标题
		if strings.Contains(trimmedLine, "=== 转换总结 ===") {
			currentCategory = "summary"
			continue
		} else if strings.Contains(trimmedLine, "=== 转换统计 ===") {
			currentCategory = "statistics"
			continue
		} else if strings.Contains(trimmedLine, "=== 需要手动配置的项目 ===") {
			currentCategory = "manual"
			continue
		} else if strings.Contains(trimmedLine, "=== 兼容性问题 ===") {
			currentCategory = "compatibility"
			continue
		} else if strings.HasPrefix(trimmedLine, "===") && strings.HasSuffix(trimmedLine, "===") {
			// 忽略其他分隔符
			continue
		}

		// 解析日志级别和格式化日志条目
		logEntry := formatLogEntry(trimmedLine)

		// 统计日志级别
		if strings.Contains(trimmedLine, " - INFO - ") {
			infoCount++
		} else if strings.Contains(trimmedLine, " - WARNING - ") {
			warningCount++
		} else if strings.Contains(trimmedLine, " - ERROR - ") {
			errorCount++
		}

		// 添加到相应分类
		switch currentCategory {
		case "summary":
			summaryLogs = append(summaryLogs, logEntry)
		case "statistics":
			statisticsLogs = append(statisticsLogs, logEntry)
		case "manual":
			manualConfigLogs = append(manualConfigLogs, logEntry)
		case "compatibility":
			compatibilityLogs = append(compatibilityLogs, logEntry)
		default:
			processLogs = append(processLogs, logEntry)
		}
	}

	// 构建HTML输出
	var html strings.Builder

	// 处理过程日志区域
	html.WriteString(fmt.Sprintf(`
		<div class="log-section">
			<div class="log-section-header">
				<span class="section-title">处理过程</span>
				<div>
					<span class="section-count">%d 条日志</span>
					<span class="toggle-icon">▼</span>
				</div>
			</div>
			<div class="log-section-content" style="display: block;">
				%s
			</div>
		</div>
	`, len(processLogs), strings.Join(processLogs, "")))

	// 统计信息区域
	if len(statisticsLogs) > 0 {
		html.WriteString(fmt.Sprintf(`
			<div class="log-section">
				<div class="log-section-header">
					<span class="section-title">转换统计</span>
					<div>
						<span class="section-count">%d 条</span>
						<span class="toggle-icon">▼</span>
					</div>
				</div>
				<div class="log-section-content" style="display: none;">
					%s
				</div>
			</div>
		`, len(statisticsLogs), strings.Join(statisticsLogs, "")))
	}

	// 转换总结区域
	if len(summaryLogs) > 0 {
		html.WriteString(fmt.Sprintf(`
			<div class="log-section">
				<div class="log-section-header">
					<span class="section-title">转换总结</span>
					<div>
						<span class="section-count">%d 条</span>
						<span class="toggle-icon">▼</span>
					</div>
				</div>
				<div class="log-section-content" style="display: none;">
					%s
				</div>
			</div>
		`, len(summaryLogs), strings.Join(summaryLogs, "")))
	}

	// 需要手动配置的项目区域
	if len(manualConfigLogs) > 0 {
		html.WriteString(fmt.Sprintf(`
			<div class="log-section">
				<div class="log-section-header">
					<span class="section-title">需要手动配置的项目</span>
					<div>
						<span class="section-count">%d 条</span>
						<span class="toggle-icon">▼</span>
					</div>
				</div>
				<div class="log-section-content" style="display: none;">
					%s
				</div>
			</div>
		`, len(manualConfigLogs), strings.Join(manualConfigLogs, "")))
	}

	// 兼容性问题区域
	if len(compatibilityLogs) > 0 {
		html.WriteString(fmt.Sprintf(`
			<div class="log-section">
				<div class="log-section-header">
					<span class="section-title">兼容性问题</span>
					<div>
						<span class="section-count">%d 条</span>
						<span class="toggle-icon">▼</span>
					</div>
				</div>
				<div class="log-section-content" style="display: none;">
					%s
				</div>
			</div>
		`, len(compatibilityLogs), strings.Join(compatibilityLogs, "")))
	}

	// 日志概览区域
	html.WriteString(fmt.Sprintf(`
		<div class="log-section">
			<div class="log-section-header">
				<span class="section-title">日志概览</span>
				<div>
					<span class="toggle-icon">▼</span>
				</div>
			</div>
			<div class="log-section-content" style="display: none;">
				<div class="summary-item">
					<span class="summary-label">信息日志</span>
					<span class="summary-value">
						<span class="summary-count count-total">%d</span>
					</span>
				</div>
				<div class="summary-item">
					<span class="summary-label">警告日志</span>
					<span class="summary-value">
						<span class="summary-count count-warning">%d</span>
					</span>
				</div>
				<div class="summary-item">
					<span class="summary-label">错误日志</span>
					<span class="summary-value">
						<span class="summary-count count-error">%d</span>
					</span>
				</div>
			</div>
		</div>
	`, infoCount, warningCount, errorCount))

	return html.String()
}

// formatLogEntry 格式化单条日志为HTML
func formatLogEntry(line string) string {
	// 检查是否是带时间戳的标准日志格式
	if matches := regexp.MustCompile(`^(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) - (\w+) - (.+)$`).FindStringSubmatch(line); matches != nil {
		timestamp := matches[1]
		level := matches[2]
		message := matches[3]

		// 根据日志级别设置样式
		levelClass := "info"
		entryClass := "info"
		if level == "WARNING" {
			levelClass = "warning"
			entryClass = "warning"
		} else if level == "ERROR" {
			levelClass = "error"
			entryClass = "error"
		} else if level == "SUCCESS" {
			levelClass = "success"
			entryClass = "success"
		}

		return fmt.Sprintf(`
			<div class="log-entry %s">
				<span class="log-timestamp">%s</span>
				<span class="log-level %s">%s</span>
				<span class="log-message">%s</span>
			</div>
		`, entryClass, timestamp, levelClass, level, message)
	}

	// 对于不符合标准格式的行，简单显示
	return fmt.Sprintf(`<div class="log-entry"><span class="log-message">%s</span></div>`, line)
}

// getStatusText 获取任务状态的文本描述
func getStatusText(status uint) string {
	switch status {
	case 0:
		return "处理中"
	case 1:
		return "成功"
	case 2:
		return "失败"
	default:
		return "未知"
	}
}

// getStatusTextI18n 获取任务状态的国际化文本描述
func getStatusTextI18n(status uint, language string) string {
	switch status {
	case 0:
		return i18n.Translate(language, "task.status.processing", nil)
	case 1:
		return i18n.Translate(language, "task.status.success", nil)
	case 2:
		return i18n.Translate(language, "task.status.failed", nil)
	default:
		return i18n.Translate(language, "task.status.unknown", nil)
	}
}

// getStatusClass 获取任务状态对应的CSS类名
func getStatusClass(status uint) string {
	switch status {
	case 0:
		return "processing"
	case 1:
		return "success"
	case 2:
		return "failed"
	default:
		return "processing"
	}
}

// verifyConfig 验证配置文件是否有效
func verifyConfig(vendor, configFilePath string, language string) (map[string]interface{}, error) {
	// 获取引擎路径和Python路径
	enginePath, pythonPath := getEnginePaths(vendor)

	// 创建临时日志目录
	tempLogDir := filepath.Join(os.TempDir(), fmt.Sprintf("verify_%s", time.Now().Format("20060102150405")))
	err := os.MkdirAll(tempLogDir, 0750)
	if err != nil {
		logging.ErrorLogger.Errorf(getI18nMessageWithParams("internal.create_temp_log_dir_failed", language, map[string]interface{}{"error": err.Error()}))
		// 继续执行，即使无法创建日志目录
	} else {
		defer os.RemoveAll(tempLogDir) // 处理完成后清理临时日志目录
	}

	// 使用Python脚本验证配置
	args := []string{
		enginePath,
		"--mode", "verify",
		"--vendor", vendor,
		"--cli", configFilePath,
		"--quiet",          // 静默模式，不输出日志到标准输出
		"--lang", language, // 使用语言参数
	}

	// 如果成功创建了日志目录，添加日志参数
	if err == nil {
		args = append(args, "--log-dir", tempLogDir)
	}

	logging.DebugLogger.Infof("执行验证命令: %s %v", pythonPath, args)

	// 打印完整命令的python路径和参数，方便调试
	fullCommand := fmt.Sprintf("%s %s", pythonPath, strings.Join(args, " "))
	fmt.Println("执行验证命令:", fullCommand)

	// 使用标准库的exec.Command
	cmd := exec.Command(pythonPath, args...)

	// 设置工作目录为engine目录，确保Python脚本能找到相对路径的模板文件
	engineDir := filepath.Dir(enginePath)
	cmd.Dir = engineDir
	logging.DebugLogger.Infof("设置Python命令工作目录: %s", engineDir)
	fmt.Println("设置Python命令工作目录: %s", engineDir)

	// 捕获标准输出和标准错误，以便更好地诊断问题
	output, cmdErr := cmd.CombinedOutput()
	outputStr := string(output)

	// 即使命令执行出错，也尝试解析返回的JSON
	var rawResult map[string]interface{}
	jsonErr := error(nil)

	if len(outputStr) > 0 {
		fmt.Println("outputStr: ", outputStr)
		// 尝试从输出中提取JSON
		rawResult, jsonErr = extractAndParseLastJSON(outputStr, language)

		// 如果成功解析了JSON，检查是否包含业务错误
		if jsonErr == nil {
			// 检查valid字段是否为false
			if valid, ok := rawResult["valid"].(bool); ok && !valid {
				// 提取错误信息
				errorMessage := ""
				if errMsg, ok := rawResult["error"].(string); ok && errMsg != "" {
					errorMessage = errMsg
				} else if errMsg, ok := rawResult["message"].(string); ok && errMsg != "" {
					errorMessage = errMsg
				}

				// 检查是否是版本检测失败的错误
				if strings.Contains(errorMessage, "版本") && strings.Contains(errorMessage, "检测") {
					logging.ErrorLogger.Errorf(getI18nMessageWithParams("internal.verification_failed_version_detection_error", language, map[string]interface{}{"message": errorMessage}))
					return rawResult, nil // 直接返回包含错误信息的结果
				}

				// 其他业务错误
				if errorMessage != "" {
					logging.ErrorLogger.Errorf(getI18nMessageWithParams("internal.verification_business_error", language, map[string]interface{}{"message": errorMessage}))
					return rawResult, nil // 直接返回包含错误信息的结果
				}
			}
		}
	}

	if cmdErr != nil {
		logging.ErrorLogger.Errorf(getI18nMessageWithParams("internal.verification_execution_failed", language, map[string]interface{}{"error": cmdErr, "output": outputStr}))

		// 尝试读取错误日志获取更详细的信息
		errorLog := filepath.Join(tempLogDir, "error.log")
		if _, statErr := os.Stat(errorLog); statErr == nil {
			errorContent, _ := os.ReadFile(errorLog)
			errorContentStr := string(errorContent)
			logging.ErrorLogger.Errorf(getI18nMessageWithParams("internal.python_script_execution_failed", language, map[string]interface{}{"error": cmdErr, "log": errorContentStr}))

			// 检查是否是版本检测失败的错误
			if strings.Contains(errorContentStr, "版本") && strings.Contains(errorContentStr, "检测") {
				versionErrMsg := getI18nMessage("internal.version_detection_failed_error", language)

				// 如果已经解析了JSON结果并包含错误信息，直接返回
				if rawResult != nil && jsonErr == nil {
					return rawResult, nil
				}

				// 否则构造一个错误结果
				return map[string]interface{}{
					"valid":         false,
					"message":       versionErrMsg,
					"error":         versionErrMsg,
					"version_error": true,
				}, nil
			}
		}

		// 如果已经成功解析了JSON并包含错误信息，直接返回
		if rawResult != nil && jsonErr == nil {
			if _, ok := rawResult["valid"].(bool); ok {
				return rawResult, nil
			}
		}

		errMsg := getI18nMessageWithParams("internal.verification_execution_failed_simple", language, map[string]interface{}{"error": cmdErr})
		return nil, fmt.Errorf(errMsg)
	}

	// 如果命令执行成功但JSON解析失败
	if jsonErr != nil {
		logging.ErrorLogger.Errorf(getI18nMessageWithParams("internal.parse_verification_result_failed", language, map[string]interface{}{"error": jsonErr, "output": truncateString(outputStr, 200)}))
		errMsg := getI18nMessageWithParams("internal.parse_verification_result_failed", language, map[string]interface{}{"error": jsonErr, "output": ""})
		return nil, fmt.Errorf(errMsg)
	}

	// 使用新的响应处理函数处理原始数据
	responseData, err := handleVerificationResponse(rawResult, language)
	if err != nil {
		logging.ErrorLogger.Errorf(getI18nMessageWithParams("internal.handle_verification_response_failed", language, map[string]interface{}{"error": err}))
		return rawResult, nil // 出错时返回原始结果
	}

	// 记录处理后的响应
	logging.DebugLogger.Infof(getI18nMessageWithParams("debug.processed_verification_response", "zh-CN", map[string]interface{}{"response": responseData}))

	return responseData, nil
}

// extractInterfaces 提取配置文件中的接口信息
func extractInterfaces(vendor, configFilePath string, language string) (map[string]interface{}, error) {
	// 获取引擎路径和Python路径
	enginePath, pythonPath := getEnginePaths(vendor)

	// 创建临时日志目录
	tempLogDir := filepath.Join(os.TempDir(), fmt.Sprintf("extract_%s", time.Now().Format("20060102150405")))
	err := os.MkdirAll(tempLogDir, 0750)
	if err != nil {
		logging.ErrorLogger.Errorf(getI18nMessageWithParams("internal.create_temp_log_dir_failed", language, map[string]interface{}{"error": err.Error()}))
		// 继续执行，即使无法创建日志目录
	} else {
		defer os.RemoveAll(tempLogDir) // 处理完成后清理临时日志目录
	}

	// 使用Python脚本提取接口信息，添加quiet参数和日志目录
	args := []string{
		enginePath,
		"--mode", "extract",
		"--vendor", vendor,
		"--cli", configFilePath,
		"--quiet",          // 静默模式，不输出日志到标准输出
		"--lang", language, // 添加语言参数
	}

	// 如果成功创建了日志目录，添加日志参数
	if err == nil {
		args = append(args, "--log-dir", tempLogDir)
	}

	logging.DebugLogger.Infof("执行接口提取命令: %s %v", pythonPath, args)

	// 打印完整命令的python路径和参数，方便调试
	fullCommand := fmt.Sprintf("%s %s", pythonPath, strings.Join(args, " "))
	fmt.Println("执行接口提取命令:", fullCommand)

	// 使用标准库的exec.Command
	cmd := exec.Command(pythonPath, args...)

	// 设置工作目录为engine目录，确保Python脚本能找到相对路径的模板文件
	engineDir := filepath.Dir(enginePath)
	cmd.Dir = engineDir
	logging.DebugLogger.Infof("设置Python命令工作目录: %s", engineDir)
	fmt.Println("设置Python命令工作目录: %s", engineDir)

	// 捕获标准输出和标准错误，以便更好地诊断问题
	output, cmdErr := cmd.CombinedOutput()
	outputStr := string(output)

	// 即使命令执行出错，也尝试解析返回的JSON
	var rawResult map[string]interface{}
	jsonErr := error(nil)

	if len(outputStr) > 0 {
		// 尝试从输出中提取JSON
		rawResult, jsonErr = extractAndParseLastJSON(outputStr, language)

		// 如果成功解析了JSON，检查是否包含业务错误
		if jsonErr == nil {
			// 检查success字段是否为false
			if success, ok := rawResult["success"].(bool); ok && !success {
				// 提取错误信息
				errorMessage := ""
				if errMsg, ok := rawResult["error"].(string); ok && errMsg != "" {
					errorMessage = errMsg
				} else if errMsg, ok := rawResult["message"].(string); ok && errMsg != "" {
					errorMessage = errMsg
				}

				// 检查是否是版本检测失败的错误
				if strings.Contains(errorMessage, "版本") && strings.Contains(errorMessage, "检测") {
					logging.ErrorLogger.Errorf("提取接口失败 - 版本检测错误: %s", errorMessage)
					return rawResult, nil // 直接返回包含错误信息的结果
				}

				// 对于其他业务错误，返回提取结果
				logging.ErrorLogger.Errorf("提取接口失败 - 业务错误: %s", errorMessage)
				return rawResult, nil // 直接返回包含错误信息的结果
			}
		}
	}

	// 处理命令执行错误
	if cmdErr != nil {
		// 分析输出，查找有关不支持厂商的信息
		if strings.Contains(outputStr, "不支持的厂商") {
			vendorErrMsg := fmt.Sprintf("错误: 不支持的厂商 '%s'，当前支持的厂商: fortigate", vendor)
			logging.ErrorLogger.Errorf(vendorErrMsg)
			return nil, fmt.Errorf(vendorErrMsg)
		}

		// 如果命令执行失败，尝试读取错误日志
		errorLog := filepath.Join(tempLogDir, "error.log")
		if _, statErr := os.Stat(errorLog); statErr == nil {
			errorContent, _ := os.ReadFile(errorLog)
			errorContentStr := string(errorContent)
			logging.ErrorLogger.Errorf("Python脚本执行失败: %v\n错误日志: %s", cmdErr, errorContentStr)

			// 检查是否是厂商不支持的错误
			if strings.Contains(errorContentStr, "不支持的厂商") {
				vendorErrMsg := fmt.Sprintf("错误: 不支持的厂商 '%s'，当前支持的厂商: fortigate", vendor)
				return nil, fmt.Errorf(vendorErrMsg)
			}

			// 检查是否是版本检测失败的错误
			if strings.Contains(errorContentStr, "版本") && strings.Contains(errorContentStr, "检测") {
				versionErrMsg := getI18nMessage("internal.version_detection_failed_error", language)

				// 如果已经解析了JSON结果并包含错误信息，直接返回
				if rawResult != nil && jsonErr == nil {
					return rawResult, nil
				}

				// 否则构造一个错误结果
				return map[string]interface{}{
					"success":    false,
					"message":    versionErrMsg,
					"error":      versionErrMsg,
					"interfaces": []interface{}{},
					"count":      0,
				}, nil
			}
		}

		errMsg := getI18nMessageWithParams("internal.python_script_execution_failed_generic", language, map[string]interface{}{"error": cmdErr})

		// 如果已经成功解析了JSON并包含错误信息，直接返回
		if rawResult != nil && jsonErr == nil {
			if _, ok := rawResult["success"].(bool); ok {
				return rawResult, nil
			}
		}

		return nil, fmt.Errorf(errMsg)
	}

	// 如果命令执行成功但JSON解析失败
	if jsonErr != nil {
		logging.ErrorLogger.Errorf(getI18nMessageWithParams("internal.parse_extraction_result_failed", language, map[string]interface{}{"error": jsonErr, "length": len(outputStr)}))
		errMsg := getI18nMessageWithParams("internal.parse_extraction_result_failed", language, map[string]interface{}{"error": jsonErr, "length": 0})
		return nil, fmt.Errorf(errMsg)
	}

	// 使用新的响应处理函数处理原始数据
	responseData, err := handleInterfaceExtractionResponse(rawResult)
	if err != nil {
		logging.ErrorLogger.Errorf(getI18nMessageWithParams("internal.handle_interface_extraction_response_failed", language, map[string]interface{}{"error": err}))
		return rawResult, nil // 出错时返回原始结果
	}

	// 记录处理后的响应
	logging.DebugLogger.Infof(getI18nMessageWithParams("debug.processed_interface_extraction_response", "zh-CN", map[string]interface{}{"response": responseData}))

	return responseData, nil
}

// extractAndParseLastJSON 解析Python脚本输出的JSON
func extractAndParseLastJSON(output string, language string) (map[string]interface{}, error) {
	// 确保输出不为空
	if output == "" {
		return nil, fmt.Errorf(getI18nMessage("error.output_empty", language))
	}

	// 清理可能的前导和尾随空白
	output = strings.TrimSpace(output)

	// 先尝试直接解析整个输出
	var result map[string]interface{}
	err := json.Unmarshal([]byte(output), &result)
	if err == nil {
		// 整个输出是有效的JSON对象
		logging.DebugLogger.Info(getI18nMessage("debug.json_parsed_directly", language))
		return result, nil
	}

	// 如果直接解析失败，尝试使用清理函数处理输出
	cleanedOutput := cleanPythonOutput(output)
	err = json.Unmarshal([]byte(cleanedOutput), &result)
	if err == nil {
		// 使用清理函数成功解析
		logging.DebugLogger.Info(getI18nMessage("debug.json_parsed_with_clean", language))
		return result, nil
	}

	// 如果清理后仍然失败，尝试提取最后一个可能的JSON对象
	// 查找最后一个 '{' 字符的位置
	lastOpenBrace := strings.LastIndex(output, "{")
	if lastOpenBrace == -1 {
		// 没有找到JSON开始标记
		logging.ErrorLogger.Errorf(getI18nMessageWithParams("error.no_json_object_found", language, map[string]interface{}{"output": truncateString(output, 200)}))
		return nil, fmt.Errorf(getI18nMessage("error.no_json_object_in_output", language))
	}

	// 从最后一个 '{' 开始提取可能的JSON
	potentialJSON := output[lastOpenBrace:]

	// 尝试解析提取的内容
	err = json.Unmarshal([]byte(potentialJSON), &result)
	if err == nil {
		// 成功解析到JSON对象
		logging.DebugLogger.Infof(getI18nMessageWithParams("debug.json_extracted_from_output", language, map[string]interface{}{"json": truncateString(potentialJSON, 100)}))
		return result, nil
	}

	// 如果直接提取失败，尝试使用深度清理
	cleanedJSON := deepCleanJSON(potentialJSON)
	err = json.Unmarshal([]byte(cleanedJSON), &result)
	if err == nil {
		logging.DebugLogger.Info("使用深度清理成功解析JSON")
		return result, nil
	}

	// 如果深度清理后仍然失败，尝试使用更复杂的方法找到JSON
	logging.DebugLogger.Info("尝试使用深度解析方法提取JSON")
	lastJSON := extractMostLikelyJSON(output)
	if lastJSON != "" {
		err = json.Unmarshal([]byte(lastJSON), &result)
		if err == nil {
			logging.DebugLogger.Info("使用深度解析成功提取JSON")
			return result, nil
		}

		// 最后尝试对提取的JSON进行深度清理
		cleanedLastJSON := deepCleanJSON(lastJSON)
		err = json.Unmarshal([]byte(cleanedLastJSON), &result)
		if err == nil {
			logging.DebugLogger.Info("使用深度解析+深度清理成功提取JSON")
			return result, nil
		}
	}

	// 记录错误并返回
	// 使用传入的语言参数记录日志
	logging.ErrorLogger.Errorf(getI18nMessageWithParams("internal.unable_to_parse_json_from_output", language, map[string]interface{}{"output": truncateString(output, 200), "error": err.Error()}))
	return nil, fmt.Errorf(getI18nMessageWithParams("internal.unable_to_parse_json_from_output", language, map[string]interface{}{"output": "", "error": err.Error()}))
}

// validateMappingFile 验证接口映射文件格式是否正确
func validateMappingFile(filePath string, language string) error {
	// 读取文件内容
	fileContent, err := os.ReadFile(filePath)
	if err != nil {
		return fmt.Errorf(getI18nMessageWithParams("file.read_failed", language, map[string]interface{}{"error": err.Error()}))
	}

	// 检查文件是否为空
	if len(fileContent) == 0 {
		return fmt.Errorf(getI18nMessage("interface.mapping_empty", language))
	}

	// 验证是否为有效的JSON
	var mappingData map[string]interface{}
	if err := json.Unmarshal(fileContent, &mappingData); err != nil {
		return fmt.Errorf(getI18nMessageWithParams("file.invalid_format", language, map[string]interface{}{"error": err.Error()}))
	}

	// 检查是否至少有一个映射
	if len(mappingData) == 0 {
		return fmt.Errorf(getI18nMessage("interface.no_mappings", language))
	}

	// 用于检查目标接口是否重复的映射
	targetInterfaces := make(map[string]string)

	// 检查值是否为字符串类型，并验证一对一关系
	for key, value := range mappingData {
		// 检查源接口名称是否为空
		if key == "" {
			return fmt.Errorf(getI18nMessage("interface.empty_source", language))
		}

		// 检查值是否为字符串类型
		targetValue, ok := value.(string)
		if !ok {
			return fmt.Errorf(getI18nMessageWithParams("interface.invalid_value_type", language, map[string]interface{}{"interface": key}))
		}

		// 检查目标接口名称是否为空
		if targetValue == "" {
			return fmt.Errorf(getI18nMessageWithParams("interface.empty_target", language, map[string]interface{}{"interface": key}))
		}

		// 检查目标接口是否已经被其他源接口使用（验证一对一关系）
		if existingSource, exists := targetInterfaces[targetValue]; exists {
			return fmt.Errorf(getI18nMessageWithParams("interface.duplicate_target", language,
				map[string]interface{}{
					"target":  targetValue,
					"source1": existingSource,
					"source2": key,
				}))
		}

		// 记录目标接口和对应的源接口
		targetInterfaces[targetValue] = key
	}

	// 记录映射数量
	mappingCount := len(mappingData)
	if language == "en-US" {
		logging.DebugLogger.Infof("Interface mapping validation passed: %s, contains %d mappings", filePath, mappingCount)
	} else {
		logging.DebugLogger.Infof("接口映射验证通过: %s, 包含 %d 个映射", filePath, mappingCount)
	}
	return nil
}

// GetConversionReport 获取转换报告API
func GetConversionReport(ctx iris.Context) {
	// 获取厂商信息
	vendor := getVendorParam(ctx)

	// 检查厂商是否支持
	if !isVendorSupported(vendor) {
		response.I18nError(ctx, "vendor.not_supported", "vendor", vendor, "supported", "fortigate")
		return
	}

	// 获取任务ID
	jobID := ctx.Params().Get("job_id")
	if jobID == "" {
		response.I18nError(ctx, "param.error")
		return
	}

	// 查询任务信息
	job := dconfigtrans.ConfigTrans{}
	if err := job.FindEx("job_id", jobID); err != nil {
		response.I18nError(ctx, "data.empty")
		return
	}

	// 检查任务是否属于当前厂商
	if job.Vendor != vendor {
		response.I18nError(ctx, "task.vendor_mismatch", "job_id", jobID, "vendor", vendor)
		return
	}

	// 获取任务目录
	jobDir := job.Dir()
	outputDir := filepath.Join(jobDir, "output")

	// 寻找转换报告文件
	reportPath := filepath.Join(outputDir, "conversion_report.json")
	if _, err := os.Stat(reportPath); os.IsNotExist(err) {
		// 如果报告文件不存在，返回简单的任务状态信息
		response.I18nResponse(ctx, response.NoErr.Code, map[string]interface{}{
			"success": job.Status == 1,
			"message": job.Result,
			"job_info": map[string]interface{}{
				"job_id":     job.JobID,
				"vendor":     job.Vendor,
				"model":      job.Model,
				"version":    job.Version,
				"created_at": job.CreatedAt,
				"status":     job.Status,
			},
		}, "convert.success")
		return
	}

	// 读取报告文件内容
	reportContent, err := os.ReadFile(reportPath)
	if err != nil {
		response.I18nError(ctx, "file.read_failed", "error", err.Error())
		return
	}

	// 解析JSON报告
	var report map[string]interface{}
	if err := json.Unmarshal(reportContent, &report); err != nil {
		response.I18nError(ctx, "file.parse_failed", "error", err.Error())
		return
	}

	// 添加任务基本信息
	report["job_info"] = map[string]interface{}{
		"job_id":     job.JobID,
		"vendor":     job.Vendor,
		"model":      job.Model,
		"version":    job.Version,
		"created_at": job.CreatedAt,
		"status":     job.Status,
	}

	response.I18nResponse(ctx, response.NoErr.Code, report, "convert.success")
}

// GetJobStatus 通过job_id查询任务状态
func GetJobStatus(ctx iris.Context) {
	// 获取厂商信息
	vendor := getVendorParam(ctx)

	// 检查厂商是否支持
	if !isVendorSupported(vendor) {
		response.I18nError(ctx, "vendor.not_supported", "vendor", vendor, "supported", "fortigate")
		return
	}

	// 获取任务ID
	jobID := ctx.Params().Get("job_id")
	if jobID == "" {
		response.I18nError(ctx, "param.error")
		return
	}

	// 查询任务信息
	job := dconfigtrans.ConfigTrans{}
	if err := job.FindEx("job_id", jobID); err != nil {
		response.I18nError(ctx, "data.empty")
		return
	}

	// 检查任务是否属于当前厂商
	if job.Vendor != vendor {
		response.I18nError(ctx, "task.vendor_mismatch", "job_id", jobID, "vendor", vendor)
		return
	}

	// 获取当前语言
	language := getLanguageFromContext(ctx)

	// 状态描述映射
	statusDesc := map[uint]string{
		0: getI18nMessage("status.processing", language),
		1: getI18nMessage("status.success", language),
		2: getI18nMessage("status.failed", language),
	}

	// 构建响应数据
	statusData := map[string]interface{}{
		"job_id":      job.JobID,
		"id":          job.ID,
		"status":      job.Status,
		"status_desc": statusDesc[job.Status],
		"result":      job.Result,
		"created_at":  job.CreatedAt,
		"updated_at":  job.UpdatedAt,
		"file_name":   job.InputFileName,
		"output_file": job.OutputFileName,
		"has_report":  job.ReportPath != "",
		"model":       job.Model,
		"version":     job.Version,
		"vendor":      job.Vendor,
	}

	// 如果转换已完成（成功或失败），添加额外信息
	if job.Status > 0 {
		// 添加下载链接等信息，使用新的URL格式
		statusData["download_url"] = fmt.Sprintf("/api/v1/firewallflextrans/download/%s", job.JobID)
		statusData["log_url"] = fmt.Sprintf("/api/v1/firewallflextrans/log/%s", job.JobID)
		statusData["view_log_url"] = fmt.Sprintf("/api/v1/firewallflextrans/view-log/%s", job.JobID)

		// 如果有转换报告，添加报告链接
		if job.ReportPath != "" {
			statusData["report_url"] = fmt.Sprintf("/api/v1/firewallflextrans/report/%s", job.JobID)
		}
	}

	response.I18nResponse(ctx, response.NoErr.Code, statusData, "convert.success")
}

// convertConfig 转换配置文件
func convertConfig(vendor, inputFile, mappingFile, model, version, outputFile, encryptOutputFile, logDir string, language string) (success bool, message string, err error) {
	// 获取引擎路径和Python路径
	enginePath, pythonPath := getEnginePaths(vendor)

	// 拼接Python命令参数
	args := []string{
		enginePath,
		"--mode", "convert",
		"--vendor", vendor,
		"--cli", inputFile,
		"--mapping", mappingFile,
		"--model", model,
		"--version", version,
		"--output", outputFile,
		"--lang", language, // 添加语言参数
	}

	// 如果需要生成加密文件
	if encryptOutputFile != "" {
		args = append(args, "--encrypt-output", encryptOutputFile)
	}

	// 如果指定了日志目录
	if logDir != "" {
		args = append(args, "--log-dir", logDir)
	}

	logging.DebugLogger.Infof("执行转换命令: %s %v", pythonPath, args)
	//打印完整命令的python路径和参数 和系统执行一样
	fullCommand := fmt.Sprintf("%s %s", pythonPath, strings.Join(args, " "))
	fmt.Println("执行转换命令:", fullCommand)

	// 使用标准库的exec.Command
	cmd := exec.Command(pythonPath, args...)

	// 设置工作目录为engine目录，确保Python脚本能找到相对路径的模板文件
	engineDir := filepath.Dir(enginePath)
	cmd.Dir = engineDir
	logging.DebugLogger.Infof("设置Python命令工作目录: %s", engineDir)
	fmt.Println("设置Python命令工作目录: %s", engineDir)

	// 捕获标准输出和标准错误，以便更好地诊断问题
	output, cmdErr := cmd.CombinedOutput()
	outputStr := string(output)

	if cmdErr != nil {
		// 打印错误输出
		fmt.Println("Python脚本执行失败，错误信息: ", outputStr)
		logging.ErrorLogger.Errorf("Python脚本执行失败，错误输出: %s", outputStr)

		// 尝试读取错误日志获取更详细的信息
		errorLog := filepath.Join(logDir, "error.log")
		errorDetails := ""
		if _, statErr := os.Stat(errorLog); statErr == nil {
			errorContent, readErr := os.ReadFile(errorLog)
			if readErr == nil && len(errorContent) > 0 {
				errorDetails = string(errorContent)
				logging.ErrorLogger.Errorf("Python错误日志内容: %s", errorDetails)
			}
		}

		// 构建详细的错误信息
		detailedError := getI18nMessageWithParams("convert.python_error", language, map[string]interface{}{"error": cmdErr.Error()})
		if outputStr != "" && len(outputStr) < 500 {
			detailedError = fmt.Sprintf("%s\n输出: %s", detailedError, outputStr)
		}
		if errorDetails != "" {
			detailedError = fmt.Sprintf("%s\nError-log: %s", detailedError, errorDetails)
		}

		return false, detailedError, cmdErr
	}

	logging.DebugLogger.Infof("转换命令输出 (长度: %d): %s", len(outputStr), truncateString(outputStr, 200))

	// 解析JSON结果
	rawResult, err := extractAndParseLastJSON(outputStr, language)
	if err != nil {
		// 如果解析失败，尝试读取错误日志获取更多信息
		errorLog := filepath.Join(logDir, "error.log")
		if _, statErr := os.Stat(errorLog); statErr == nil {
			errorContent, _ := os.ReadFile(errorLog)
			errorDetails := string(errorContent)
			logging.ErrorLogger.Errorf("解析JSON失败，错误日志内容: %s", errorDetails)

			// 构建详细的错误信息
			detailedError := getI18nMessage("convert.engine_error", language)
			if len(errorDetails) > 0 {
				detailedError = fmt.Sprintf("%s\nError-log: %s", detailedError, errorDetails)
			}
			return false, detailedError, err
		}

		errMsg := getI18nMessage("convert.engine_error", language)
		return false, errMsg, err
	}

	// 使用新的响应处理函数处理原始数据
	responseData, err := handleConversionResponse(rawResult, language)
	if err != nil {
		logging.ErrorLogger.Errorf(getI18nMessageWithParams("internal.handle_conversion_response_failed", language, map[string]interface{}{"error": err}))

		// 检查原始结果中的错误信息
		errorMsg := getI18nMessage("convert.failed", language)
		if msg, ok := rawResult["error"].(string); ok && msg != "" {
			errorMsg = msg
		} else if msg, ok := rawResult["message"].(string); ok && msg != "" {
			errorMsg = msg
		}

		return false, errorMsg, nil
	}

	// 检查转换是否成功
	convSuccess, ok := responseData["success"].(bool)
	if !ok || !convSuccess {
		errorMsg := getI18nMessage("convert.failed", language)
		if errMsg, ok := responseData["message"].(string); ok && errMsg != "" {
			errorMsg = errMsg
		}

		logging.DebugLogger.Infof(getI18nMessageWithParams("internal.conversion_business_failed", language, map[string]interface{}{"message": errorMsg}))
		return false, errorMsg, nil
	}

	// 记录转换报告路径（如果存在）
	if reportPath, ok := responseData["report_path"].(string); ok && reportPath != "" {
		logging.DebugLogger.Infof("转换报告路径: %s", reportPath)
		// 这里不返回报告路径，因为caller不需要处理报告文件
	}

	successMsg := getI18nMessage("convert.success", language)
	logging.DebugLogger.Infof(successMsg)
	return true, successMsg, nil
}

// extractMostLikelyJSON 从输出中提取最可能是JSON的部分
func extractMostLikelyJSON(output string) string {
	// 尝试找到最完整的JSON对象
	jsonRe := regexp.MustCompile(`(\{.*?\})`)
	matches := jsonRe.FindAllString(output, -1)

	// 如果找到多个匹配，选择最长的一个
	var bestMatch string
	var maxLen int

	for _, match := range matches {
		if len(match) > maxLen {
			// 验证这是有效的JSON结构
			openBraces := strings.Count(match, "{")
			closeBraces := strings.Count(match, "}")

			// 基本的平衡检查
			if openBraces > 0 && closeBraces > 0 &&
				openBraces <= closeBraces &&
				strings.HasPrefix(match, "{") &&
				strings.HasSuffix(match, "}") {
				bestMatch = match
				maxLen = len(match)
			}
		}
	}

	if bestMatch != "" {
		// 尝试修复常见问题
		bestMatch = strings.Replace(bestMatch, ",}", "}", -1)
		bestMatch = strings.Replace(bestMatch, ",]", "]", -1)

		// 确保括号平衡
		openBraces := strings.Count(bestMatch, "{")
		closeBraces := strings.Count(bestMatch, "}")

		if closeBraces > openBraces {
			// 移除多余的闭合括号
			excess := closeBraces - openBraces
			for i := 0; i < excess; i++ {
				lastIndex := strings.LastIndex(bestMatch, "}")
				if lastIndex != -1 {
					bestMatch = bestMatch[:lastIndex] + bestMatch[lastIndex+1:]
				}
			}
		} else if openBraces > closeBraces {
			// 添加缺少的闭合括号
			for i := 0; i < openBraces-closeBraces; i++ {
				bestMatch += "}"
			}
		}

		logging.DebugLogger.Infof("提取的最可能JSON: %s", truncateString(bestMatch, 100))
		return bestMatch
	}

	return ""
}

// cleanPythonOutput 清理Python脚本输出，解决常见的格式问题
func cleanPythonOutput(output string) string {
	// 检查输入是否为空
	if output == "" {
		return "{}" // 返回空对象而不是空字符串
	}

	// 去除UTF-8 BOM和空白
	output = strings.TrimSpace(output)
	if len(output) >= 3 && output[0] == 0xEF && output[1] == 0xBB && output[2] == 0xBF {
		output = output[3:]
	}

	// 查找第一个JSON开始和最后一个JSON结束
	firstBrace := strings.Index(output, "{")
	lastBrace := strings.LastIndex(output, "}")

	// 保护: 确保索引有效
	if firstBrace == -1 || lastBrace == -1 || firstBrace >= lastBrace ||
		firstBrace >= len(output) || lastBrace >= len(output) {
		// 注意：这个工具函数没有语言参数，使用默认语言记录内部调试日志
		logging.DebugLogger.Infof(getI18nMessage("internal.unable_to_find_complete_json", "zh-CN"))
		return output
	}

	// 安全检查：确保索引不会越界
	if firstBrace < 0 {
		firstBrace = 0
	}
	if lastBrace >= len(output) {
		lastBrace = len(output) - 1
	}
	if firstBrace > lastBrace {
		// 如果出现索引问题，返回原始输出
		// 注意：这个工具函数没有语言参数，使用默认语言记录内部调试日志
		logging.DebugLogger.Infof(getI18nMessageWithParams("internal.invalid_json_fragment_indices", "zh-CN", map[string]interface{}{"first": firstBrace, "last": lastBrace}))
		return output
	}

	// 提取可能的JSON部分，确保索引不会越界
	jsonPart := output[firstBrace : lastBrace+1]

	// 检查提取的JSON部分是否有嵌套的完整JSON对象
	// 这可以处理Python脚本输出日志和JSON混合的情况
	innerFirstBrace := -1
	if len(jsonPart) > 1 {
		innerFirst := strings.Index(jsonPart[1:], "{")
		if innerFirst != -1 {
			innerFirstBrace = innerFirst + 1 // 调整偏移量
		}
	}

	innerLastBrace := -1
	if len(jsonPart) > 1 {
		// 从后向前查找，避免匹配到嵌套对象的结束括号
		for i := len(jsonPart) - 2; i >= 0; i-- {
			if jsonPart[i] == '}' {
				innerLastBrace = i
				break
			}
		}
	}

	// 如果找到完整的内部JSON，提取它
	if innerFirstBrace != -1 && innerLastBrace != -1 && innerFirstBrace < innerLastBrace {
		// 索引安全检查
		if innerFirstBrace >= 0 && innerLastBrace < len(jsonPart) {
			logging.DebugLogger.Infof("找到内部嵌套的JSON: %d-%d", innerFirstBrace, innerLastBrace)
			jsonPart = jsonPart[innerFirstBrace : innerLastBrace+1]
		}
	}

	// 尝试修复常见的JSON格式问题
	jsonPart = strings.Replace(jsonPart, ",}", "}", -1) // 移除尾随逗号
	jsonPart = strings.Replace(jsonPart, ",]", "]", -1) // 移除数组尾随逗号

	// 深度清理JSON
	return deepCleanJSON(jsonPart)
}

// deepCleanJSON 执行深度JSON清理，处理常见的格式问题
func deepCleanJSON(jsonStr string) string {
	// 1. 修复未闭合的引号
	re := regexp.MustCompile(`"([^"]*$)`)
	jsonStr = re.ReplaceAllString(jsonStr, `"`)

	// 2. 修复多余的逗号
	jsonStr = strings.Replace(jsonStr, ",}", "}", -1) // 对象末尾逗号
	jsonStr = strings.Replace(jsonStr, ",]", "]", -1) // 数组末尾逗号

	// 3. 修复数组后的错误字符 - 专门针对"invalid character '}' after array element"
	// 正则表达式查找类似 "]}" 的问题，确保在数组和对象之间有正确的逗号
	arrayObjRe := regexp.MustCompile(`\](\s*)([}\]])`)
	jsonStr = arrayObjRe.ReplaceAllString(jsonStr, `],$2`)

	// 4. 修复错误添加的逗号 - 删除 "],}" 之间的逗号
	jsonStr = strings.Replace(jsonStr, "],}", "]}", -1)

	// 5. 确保对象内部属性之间有逗号
	propertyRe := regexp.MustCompile(`"([^"]+)"\s*:\s*([^,\s\]\}]+)(\s*)([^,\]\}])`)
	jsonStr = propertyRe.ReplaceAllString(jsonStr, `"$1": $2$3,$4`)

	// 6. 修复数组内元素后缺少逗号的问题
	arrayElemRe := regexp.MustCompile(`(\}|\]|"[^"]*"|[0-9]+|true|false|null)(\s*)(\{|\[|"[^"]*")`)
	jsonStr = arrayElemRe.ReplaceAllString(jsonStr, `$1,$3`)

	// 7. 移除多余的字符
	jsonStr = strings.TrimSpace(jsonStr)

	// 8. 确保括号平衡
	openBraces := strings.Count(jsonStr, "{")
	closeBraces := strings.Count(jsonStr, "}")
	openBrackets := strings.Count(jsonStr, "[")
	closeBrackets := strings.Count(jsonStr, "]")

	// 添加缺少的右花括号
	for i := 0; i < openBraces-closeBraces; i++ {
		jsonStr += "}"
	}

	// 添加缺少的右方括号
	for i := 0; i < openBrackets-closeBrackets; i++ {
		jsonStr += "]"
	}

	// 移除多余的右花括号
	if closeBraces > openBraces {
		excess := closeBraces - openBraces
		for i := 0; i < excess; i++ {
			lastIndex := strings.LastIndex(jsonStr, "}")
			if lastIndex != -1 {
				jsonStr = jsonStr[:lastIndex] + jsonStr[lastIndex+1:]
			}
		}
	}

	// 移除多余的右方括号
	if closeBrackets > openBrackets {
		excess := closeBrackets - openBrackets
		for i := 0; i < excess; i++ {
			lastIndex := strings.LastIndex(jsonStr, "]")
			if lastIndex != -1 {
				jsonStr = jsonStr[:lastIndex] + jsonStr[lastIndex+1:]
			}
		}
	}

	// 记录清理后的结果
	logging.DebugLogger.Infof("深度清理JSON后: %s", truncateString(jsonStr, 100))

	return jsonStr
}

// getLanguageFromContext 从上下文中获取语言设置
func getLanguageFromContext(ctx iris.Context) string {
	if ctx == nil {
		return "zh-CN" // 默认语言
	}

	language := ctx.Values().GetString("language")
	if language == "" {
		language = "zh-CN" // 默认使用中文
	}
	return language
}

// getI18nMessage 获取指定语言的国际化消息
func getI18nMessage(key string, language string) string {
	// 确保 i18n 包已初始化
	i18n.Init()

	// 使用 i18n 包的 Translate 方法获取国际化消息
	msg := i18n.Translate(language, key, nil)

	// 添加调试日志
	logging.DebugLogger.Debugf("翻译键: %s, 语言: %s, 翻译结果: %s", key, language, msg)

	if msg == key {
		// 如果返回的消息与键相同，说明没有找到翻译
		// 作为备用，使用内部的国际化消息
		messages := map[string]map[string]string{
			"en-US": {
				"file.not_exists":              "File does not exist",
				"file.read_failed":             "Failed to read file",
				"file.invalid_format":          "Invalid file format",
				"convert.mapping_error":        "Interface mapping error",
				"convert.failed":               "Conversion failed",
				"convert.success":              "Conversion successful",
				"convert.python_error":         "Python execution error",
				"convert.engine_error":         "Conversion engine error",
				"interface.mapping_empty":      "Mapping data is empty",
				"interface.no_mappings":        "Mapping data does not contain any interface mappings",
				"interface.empty_source":       "Mapping contains empty source interface name",
				"interface.invalid_value_type": "The mapping value for interface must be a string",
				"interface.empty_target":       "The mapping target for interface is empty",
				"interface.duplicate_target":   "Target interface is used by multiple source interfaces, many-to-one mapping is not allowed",
			},
			"zh-CN": {
				"file.not_exists":              "文件不存在",
				"file.read_failed":             "读取文件失败",
				"file.invalid_format":          "文件格式不正确",
				"convert.mapping_error":        "接口映射错误",
				"convert.failed":               "转换失败",
				"convert.success":              "转换成功",
				"convert.python_error":         "Python执行错误",
				"convert.engine_error":         "转换引擎错误",
				"interface.mapping_empty":      "映射数据为空",
				"interface.no_mappings":        "映射数据不包含任何接口映射",
				"interface.empty_source":       "映射中包含空的源接口名称",
				"interface.invalid_value_type": "接口的映射值必须是字符串类型",
				"interface.empty_target":       "接口的映射目标为空",
				"interface.duplicate_target":   "目标接口被多个源接口使用，不允许多对一映射",
			},
		}

		if message, ok := messages[language][key]; ok {
			return message
		}

		// 默认返回中文消息
		if message, ok := messages["zh-CN"][key]; ok {
			return message
		}
	}

	return msg
}

// getI18nMessageWithParams 获取带参数的国际化消息
func getI18nMessageWithParams(key string, language string, params map[string]interface{}) string {
	// 确保 i18n 包已初始化
	i18n.Init()

	// 使用 i18n 包的 Translate 方法获取带参数的国际化消息
	msg := i18n.Translate(language, key, params)

	// 添加调试日志
	logging.DebugLogger.Debugf("翻译键(带参数): %s, 语言: %s, 参数: %+v, 翻译结果: %s", key, language, params, msg)

	if msg == key {
		// 如果消息为空，使用基本消息并手动替换参数
		baseMsg := getI18nMessage(key, language)

		// 简单的参数替换实现
		for k, v := range params {
			placeholder := "{{." + k + "}}"
			baseMsg = strings.Replace(baseMsg, placeholder, fmt.Sprintf("%v", v), -1)
		}

		return baseMsg
	}

	return msg
}

// testI18nTranslation 测试国际化翻译功能
func testI18nTranslation() {
	logging.InfoLogger.Info("=== 测试国际化翻译功能 ===")

	// 确保 i18n 包已初始化
	i18n.Init()

	// 测试基本翻译
	testKeys := []string{
		"log.task_start",
		"log.task_id",
		"log.vendor",
		"log.model",
		"log.version",
		"log.input_file",
		"log.start_time",
	}

	languages := []string{"zh-CN", "en-US"}

	for _, lang := range languages {
		logging.InfoLogger.Infof("测试语言: %s", lang)
		for _, key := range testKeys {
			result := i18n.Translate(lang, key, nil)
			logging.InfoLogger.Infof("  键: %s -> 结果: %s", key, result)
		}
	}

	// 测试带参数的翻译
	params := map[string]interface{}{
		"id":      "test123",
		"vendor":  "fortigate",
		"model":   "z5100s",
		"version": "R10P2",
		"file":    "test.txt",
		"time":    "2025-01-01 12:00:00",
	}

	logging.InfoLogger.Info("测试带参数的翻译:")
	for _, lang := range languages {
		logging.InfoLogger.Infof("语言: %s", lang)
		for _, key := range testKeys {
			result := i18n.Translate(lang, key, params)
			logging.InfoLogger.Infof("  键: %s -> 结果: %s", key, result)
		}
	}

	logging.InfoLogger.Info("=== 国际化翻译测试完成 ===")
}

// getConfigItemTypeName 获取配置项类型的国际化名称
func getConfigItemTypeName(itemType string, language string) string {
	switch strings.ToLower(itemType) {
	case "interface":
		return i18n.Translate(language, "config.type.interface", nil)
	case "zone":
		return i18n.Translate(language, "config.type.zone", nil)
	case "zone_interface":
		return i18n.Translate(language, "config.type.zone_interface", nil)
	case "route":
		return i18n.Translate(language, "config.type.route", nil)
	case "address":
		return i18n.Translate(language, "config.type.address", nil)
	case "service":
		return i18n.Translate(language, "config.type.service", nil)
	case "policy":
		return i18n.Translate(language, "config.type.policy", nil)
	default:
		return itemType
	}
}

// LogEntry 表示单条日志条目
type LogEntry struct {
	Timestamp string `json:"timestamp"`
	Level     string `json:"level"`
	Message   string `json:"message"`
	Category  string `json:"category"`
}

// LogStats 表示日志统计信息
type LogStats struct {
	InfoCount    int `json:"info_count"`
	WarningCount int `json:"warning_count"`
	ErrorCount   int `json:"error_count"`
	TotalLines   int `json:"total_lines"`
}

// CategoryInfo 表示日志分类信息
type CategoryInfo struct {
	Name  string `json:"name"`
	Count int    `json:"count"`
}

// LogResponse 表示日志响应结构
type LogResponse struct {
	JobInfo struct {
		JobID     string    `json:"job_id"`
		Vendor    string    `json:"vendor"`
		Model     string    `json:"model"`
		Version   string    `json:"version"`
		Status    int       `json:"status"`
		CreatedAt time.Time `json:"created_at"`
	} `json:"job_info"`
	LogStats    LogStats       `json:"log_stats"`
	Categories  []CategoryInfo `json:"categories"`
	Entries     []LogEntry     `json:"entries"`
	LastUpdated time.Time      `json:"last_updated"`
}

// GetLogData 获取日志数据的API
func GetLogData(ctx iris.Context) {
	// 获取厂商信息
	vendor := getVendorParam(ctx)

	// 检查厂商是否支持
	if !isVendorSupported(vendor) {
		response.I18nError(ctx, "vendor.not_supported", "vendor", vendor, "supported", "fortigate")
		return
	}

	// 获取job_id
	jobID := ctx.Params().Get("job_id")
	if jobID == "" {
		response.I18nError(ctx, "param.job_id_required")
		return
	}

	// 获取任务信息
	job := dconfigtrans.ConfigTrans{}
	err := job.FindEx("job_id", jobID)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	// 检查任务是否属于当前厂商
	if job.Vendor != vendor {
		response.I18nError(ctx, "task.vendor_mismatch", "job_id", jobID, "vendor", vendor)
		return
	}

	// 设置日志文件路径
	uploadDir := filepath.Join(libs.Config.ConfigTrans.Upload, job.CreatedAt.Format("20060102"), job.JobID)
	logDir := filepath.Join(uploadDir, "logs")

	// 只获取用户日志
	userLogFile := filepath.Join(logDir, job.JobID+".log")

	if _, err := os.Stat(userLogFile); os.IsNotExist(err) {
		response.I18nError(ctx, "file.log_not_exists")
		return
	}

	content, err := os.ReadFile(userLogFile)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	// 解析日志内容
	logContent := string(content)
	logEntries, categories, stats := parseLogToStructured(logContent)

	// 构建响应
	logResponse := LogResponse{}

	// 填充任务信息
	logResponse.JobInfo.JobID = job.JobID
	logResponse.JobInfo.Vendor = job.Vendor
	logResponse.JobInfo.Model = job.Model
	logResponse.JobInfo.Version = job.Version
	logResponse.JobInfo.Status = int(job.Status)
	logResponse.JobInfo.CreatedAt = job.CreatedAt

	// 填充日志统计信息
	logResponse.LogStats = stats

	// 填充分类信息
	logResponse.Categories = categories

	// 填充日志条目
	logResponse.Entries = logEntries
	logResponse.LastUpdated = time.Now()

	// 返回JSON响应
	response.I18nResponse(ctx, response.NoErr.Code, logResponse, "log.data_retrieved")
}

// parseLogToStructured 解析日志内容为结构化数据
func parseLogToStructured(content string) ([]LogEntry, []CategoryInfo, LogStats) {
	lines := strings.Split(content, "\n")
	entries := make([]LogEntry, 0, len(lines))
	categoriesMap := make(map[string]int)
	stats := LogStats{}

	currentCategory := getI18nMessage("log.category.processing", "zh-CN")
	categoriesMap[currentCategory] = 0

	for _, line := range lines {
		trimmedLine := strings.TrimSpace(line)
		if trimmedLine == "" {
			continue
		}

		// 检查是否是分类标题
		if strings.Contains(trimmedLine, "=== 转换总结 ===") {
			currentCategory = getI18nMessage("log.category.summary", "zh-CN")
			categoriesMap[currentCategory] = 0
			continue
		} else if strings.Contains(trimmedLine, "=== 转换统计 ===") {
			currentCategory = getI18nMessage("log.category.statistics", "zh-CN")
			categoriesMap[currentCategory] = 0
			continue
		} else if strings.Contains(trimmedLine, "=== 需要手动配置的项目 ===") {
			currentCategory = getI18nMessage("log.category.manual_config", "zh-CN")
			categoriesMap[currentCategory] = 0
			continue
		} else if strings.Contains(trimmedLine, "=== 兼容性问题 ===") {
			currentCategory = getI18nMessage("log.category.compatibility", "zh-CN")
			categoriesMap[currentCategory] = 0
			continue
		} else if strings.HasPrefix(trimmedLine, "===") && strings.HasSuffix(trimmedLine, "===") {
			// 其他分类标题
			currentCategory = strings.Trim(trimmedLine, "= ")
			categoriesMap[currentCategory] = 0
			continue
		}

		// 解析日志条目
		entry := LogEntry{
			Message:  trimmedLine,
			Category: currentCategory,
		}

		// 尝试解析带时间戳和级别的日志格式
		re := regexp.MustCompile(`^(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) - (\w+) - (.+)$`)
		matches := re.FindStringSubmatch(trimmedLine)

		if matches != nil && len(matches) >= 4 {
			entry.Timestamp = matches[1]
			entry.Level = matches[2]
			entry.Message = matches[3]

			// 统计不同级别的日志数量
			switch entry.Level {
			case "INFO":
				stats.InfoCount++
			case "WARNING":
				stats.WarningCount++
			case "ERROR":
				stats.ErrorCount++
			}
		}

		entries = append(entries, entry)
		categoriesMap[currentCategory]++
		stats.TotalLines++
	}

	// 将分类映射转换为切片
	categories := make([]CategoryInfo, 0, len(categoriesMap))
	for name, count := range categoriesMap {
		categories = append(categories, CategoryInfo{
			Name:  name,
			Count: count,
		})
	}

	return entries, categories, stats
}

// ViewSimpleLog 提供一个简单的日志查看接口，直接显示日志内容
func ViewSimpleLog(ctx iris.Context) {
	// 获取厂商信息
	vendor := getVendorParam(ctx)

	// 检查厂商是否支持
	if !isVendorSupported(vendor) {
		response.I18nError(ctx, "vendor.not_supported", "vendor", vendor, "supported", "fortigate")
		return
	}

	// 获取job_id
	jobID := ctx.Params().Get("job_id")
	if jobID == "" {
		response.I18nError(ctx, "param.job_id_required")
		return
	}

	job := dconfigtrans.ConfigTrans{}
	err := job.FindEx("job_id", jobID)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	// 检查任务是否属于当前厂商
	if job.Vendor != vendor {
		response.I18nError(ctx, "task.vendor_mismatch", "job_id", jobID, "vendor", vendor)
		return
	}

	// 设置日志文件路径
	uploadDir := filepath.Join(libs.Config.ConfigTrans.Upload, job.CreatedAt.Format("20060102"), job.JobID)
	logDir := filepath.Join(uploadDir, "logs")
	userLogFile := filepath.Join(logDir, job.JobID+".log")

	if _, err := os.Stat(userLogFile); os.IsNotExist(err) {
		response.I18nError(ctx, "file.not_exists")
		return
	}

	content, err := os.ReadFile(userLogFile)
	if err != nil {
		response.I18nError(ctx, "file.read_failed", "error", err.Error())
		return
	}

	// 直接使用日志内容
	logContent := string(content)

	// 获取当前语言
	language := response.GetLanguage(ctx)

	// 获取国际化的页面标题
	logTitle := i18n.Translate(language, "convert.log_view", nil)

	// 构建简单的HTML页面
	html := fmt.Sprintf(`<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>%s - %s</title>
    <style>
        body {
            font-family: 'Courier New', Courier, monospace;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #333;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
            margin-top: 0;
        }
        .job-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .job-info p {
            margin: 5px 0;
        }
        .log-content {
            background-color: #272822;
            color: #f8f8f2;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        .log-content .info {
            color: #a6e22e;
        }
        .log-content .warning {
            color: #f1fa8c;
        }
        .log-content .error {
            color: #f92672;
        }
        .footer {
            margin-top: 20px;
            text-align: right;
            font-size: 12px;
            color: #777;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>%s</h1>
        <div class="job-info">
            <p><strong>%s:</strong> %s</p>
            <p><strong>%s:</strong> %s</p>
            <p><strong>%s:</strong> %s</p>
            <p><strong>%s:</strong> %s</p>
            <p><strong>%s:</strong> %s</p>
        </div>
        <div class="log-content">%s</div>
        <div class="footer">
            <p>%s: %s</p>
        </div>
    </div>
    <script>
        // 简单的语法高亮处理
        document.addEventListener('DOMContentLoaded', function() {
            const logContent = document.querySelector('.log-content');
            let html = logContent.innerHTML;
            
            // 为INFO级别的日志添加样式
            html = html.replace(/\[INFO\]/g, '<span class="info">[INFO]</span>');
            
            // 为WARNING级别的日志添加样式
            html = html.replace(/\[WARNING\]/g, '<span class="warning">[WARNING]</span>');
            
            // 为ERROR级别的日志添加样式
            html = html.replace(/\[ERROR\]/g, '<span class="error">[ERROR]</span>');
            
            logContent.innerHTML = html;
        });
    </script>
</body>
</html>`,
		logTitle, jobID,
		logTitle,
		getI18nMessage("task.task_id", language), jobID,
		getI18nMessage("task.vendor", language), job.Vendor,
		getI18nMessage("task.model", language), job.Model,
		getI18nMessage("task.version", language), job.Version,
		getI18nMessage("task.created_at", language), job.CreatedAt.Format("2006-01-02 15:04:05"),
		formatSimpleLogContentBasic(logContent),
		getI18nMessage("task.view_time", language), time.Now().Format("2006-01-02 15:04:05"))

	// 返回HTML内容
	ctx.HTML(html)
}

// formatSimpleLogContentBasic 格式化日志内容，处理HTML转义和简单的颜色标记
func formatSimpleLogContentBasic(content string) string {
	// HTML转义
	content = strings.ReplaceAll(content, "&", "&amp;")
	content = strings.ReplaceAll(content, "<", "&lt;")
	content = strings.ReplaceAll(content, ">", "&gt;")

	return content
}

// ViewRawLog 提供一个最简单的日志查看接口，直接以纯文本形式输出日志内容
func ViewRawLog(ctx iris.Context) {
	// 获取厂商信息
	vendor := getVendorParam(ctx)

	// 检查厂商是否支持
	if !isVendorSupported(vendor) {
		response.I18nError(ctx, "vendor.not_supported", "vendor", vendor, "supported", "fortigate")
		return
	}

	// 获取job_id
	jobID := ctx.Params().Get("job_id")
	if jobID == "" {
		response.I18nError(ctx, "param.error")
		return
	}

	job := dconfigtrans.ConfigTrans{}
	err := job.FindEx("job_id", jobID)
	if err != nil {
		response.I18nError(ctx, "system.error_with_message", "error", err.Error())
		return
	}

	if job.JobID == "" {
		response.I18nError(ctx, "file.not_exists")
		return
	}

	// 检查任务是否属于当前厂商
	if job.Vendor != vendor {
		response.I18nError(ctx, "vendor.not_supported", "vendor", job.Vendor, "supported", vendor)
		return
	}

	// 设置日志文件路径
	uploadDir := filepath.Join(libs.Config.ConfigTrans.Upload, job.CreatedAt.Format("20060102"), job.JobID)
	logDir := filepath.Join(uploadDir, "logs")
	userLogFile := filepath.Join(logDir, job.JobID+".log")

	if _, err := os.Stat(userLogFile); os.IsNotExist(err) {
		response.I18nError(ctx, "file.not_exists")
		return
	}

	content, err := os.ReadFile(userLogFile)
	if err != nil {
		response.I18nError(ctx, "file.read_failed", "error", err.Error())
		return
	}

	// 构建响应数据
	responseData := map[string]interface{}{
		"job_info": map[string]interface{}{
			"job_id":     job.JobID,
			"vendor":     job.Vendor,
			"model":      job.Model,
			"version":    job.Version,
			"status":     job.Status,
			"created_at": job.CreatedAt.Format("2006-01-02 15:04:05"),
		},
		"log_content": string(content),
	}

	// 返回JSON响应
	response.I18nResponse(ctx, response.NoErr.Code, responseData, "convert.log_view")
}

// isValidConfigFile 验证文件是否是有效的配置文件
func isValidConfigFile(filePath string) bool {
	// 读取文件进行验证
	file, err := os.Open(filePath)
	if err != nil {
		// 注意：这个工具函数没有语言参数，使用默认语言记录内部错误日志
		logging.ErrorLogger.Errorf(getI18nMessageWithParams("internal.open_config_file_failed", "zh-CN", map[string]interface{}{"error": err}))
		return false
	}
	defer file.Close()

	// 首先检查文件是否为纯文本
	if !isTextFile(filePath) {
		logging.ErrorLogger.Errorf("文件不是纯文本文件: %s", filePath)
		return false
	}

	// 基本特征检测
	validPatterns := []string{
		"config", "set", "end", "edit", "next",
	}

	// 结构验证相关变量
	configSectionCount := 0
	hasConfigSystem := false
	hasConfigRouter := false
	hasConfigFirewall := false
	hasConfigVdom := false

	// 读取整个文件进行更全面的验证
	scanner := bufio.NewScanner(file)
	lineCount := 0
	maxLines := 5000 // 限制读取行数，防止过大文件

	// 第一阶段：基本特征检测
	foundBasicPattern := false
	for scanner.Scan() && lineCount < 50 { // 只检查前50行的基本特征
		line := strings.TrimSpace(scanner.Text())
		lineCount++

		// 跳过空行和注释
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		// 检查是否包含配置文件的特征命令
		for _, pattern := range validPatterns {
			if strings.HasPrefix(line, pattern) {
				foundBasicPattern = true
				break
			}
		}

		if foundBasicPattern {
			break
		}
	}

	// 如果前50行没有找到任何特征命令，则认为不是有效的配置文件
	if !foundBasicPattern {
		logging.ErrorLogger.Errorf("文件不包含基本配置特征: %s", filePath)
		return false
	}

	// 重置文件指针，进行第二阶段验证
	file.Seek(0, 0)
	scanner = bufio.NewScanner(file)
	lineCount = 0

	// 第二阶段：结构和内容验证
	for scanner.Scan() && lineCount < maxLines {
		line := strings.TrimSpace(scanner.Text())
		lineCount++

		// 跳过空行和注释
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		// 检查配置段
		if strings.HasPrefix(line, "config ") {
			configSectionCount++

			// 检查常见的配置段
			if strings.Contains(line, "system") {
				hasConfigSystem = true
			} else if strings.Contains(line, "router") {
				hasConfigRouter = true
			} else if strings.Contains(line, "firewall") {
				hasConfigFirewall = true
			} else if strings.Contains(line, "vdom") {
				hasConfigVdom = true
			}
		}
	}

	if err := scanner.Err(); err != nil {
		// 注意：这个工具函数没有语言参数，使用默认语言记录内部错误日志
		logging.ErrorLogger.Errorf(getI18nMessageWithParams("internal.read_config_file_failed", "zh-CN", map[string]interface{}{"error": err}))
		return false
	}

	// 验证配置文件结构
	// 1. 至少应该有一些配置段
	if configSectionCount == 0 {
		logging.ErrorLogger.Errorf("文件不包含任何配置段: %s", filePath)
		return false
	}

	// 2. 检查是否包含至少一个常见的配置段
	if !hasConfigSystem && !hasConfigRouter && !hasConfigFirewall && !hasConfigVdom {
		logging.ErrorLogger.Errorf("文件不包含常见的配置段(system/router/firewall/vdom): %s", filePath)
		return false
	}

	// 3. 文件大小检查 - 有效的配置文件通常不会太小
	fileInfo, err := file.Stat()
	if err == nil && fileInfo.Size() < 100 { // 小于100字节的文件可能不是有效配置
		logging.ErrorLogger.Errorf("文件太小，可能不是有效配置: %s (%d bytes)", filePath, fileInfo.Size())
		return false
	}

	// 通过所有验证，认为是有效的配置文件
	return true
}

// isTextFile 检查文件是否为纯文本文件
func isTextFile(filePath string) bool {
	// 读取文件前8KB进行检查
	file, err := os.Open(filePath)
	if err != nil {
		// 注意：这个工具函数没有语言参数，使用默认语言记录内部错误日志
		logging.ErrorLogger.Errorf(getI18nMessageWithParams("internal.open_file_failed", "zh-CN", map[string]interface{}{"error": err}))
		return false
	}
	defer file.Close()

	// 读取前8KB
	buffer := make([]byte, 8192)
	n, err := file.Read(buffer)
	if err != nil && err != io.EOF {
		// 注意：这个工具函数没有语言参数，使用默认语言记录内部错误日志
		logging.ErrorLogger.Errorf(getI18nMessageWithParams("internal.read_file_failed", "zh-CN", map[string]interface{}{"error": err}))
		return false
	}

	// 如果文件小于8KB，调整buffer大小
	buffer = buffer[:n]

	// 检查是否包含空字节（二进制文件特征）
	if bytes.IndexByte(buffer, 0) != -1 {
		return false
	}

	// 检查是否包含过多的非ASCII字符
	nonAsciiCount := 0
	for _, b := range buffer {
		if b < 32 && !isAllowedControl(b) || b > 126 {
			nonAsciiCount++
		}
	}

	// 如果非ASCII字符超过10%，认为不是纯文本
	if float64(nonAsciiCount)/float64(len(buffer)) > 0.1 {
		return false
	}

	return true
}

// isAllowedControl 检查是否为允许的控制字符
func isAllowedControl(b byte) bool {
	// 允许的控制字符: 制表符、换行符、回车符
	return b == '\t' || b == '\n' || b == '\r'
}

// extractLastLines 提取文本最后几行
func extractLastLines(text string, n int) string {
	lines := strings.Split(text, "\n")
	if len(lines) <= n {
		return text
	}
	return strings.Join(lines[len(lines)-n:], "\n")
}
