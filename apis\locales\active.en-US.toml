[common]
success = "Request successful"

[system]
error = "System error, please contact administrator"
error_with_message = "System error: {{.error}}"
redis_error = "Redis connection error, please try again later"
redis_reconnected = "Redis has been reconnected, please retry operation"

[vendor]
not_supported = "Error: vendor '{{.vendor}}' not supported, currently supported vendors: {{.supported}}"
info = "Currently supported vendors"

[task]
status.processing = "Processing"
status.success = "Success"
status.failed = "Failed"
status.unknown = "Unknown Status"
create_success = "Task created successfully"
created_processing = "Task created, processing"
task_id = "Task ID"
vendor = "Vendor"
model = "Model"
version = "Version"
status_name = "Status"
created_at = "Created At"
limit_reached = "Current processing task count has reached limit: {{.current}}/{{.limit}}"

[file]
not_exists = "File does not exist"
save_failed = "Failed to save file: {{.error}}"
read_failed = "Failed to read file: {{.error}}"
parse_failed = "Failed to parse file: {{.error}}"
invalid_format = "Invalid file format"
upload_error = "Could not get uploaded file: {{.error}}"
empty = "Uploaded file is empty"
too_large = "Uploaded file is too large, exceeding {{.size}}MB limit"
temp_dir_error = "Failed to create temporary directory: {{.error}}"
invalid_extension = "Unsupported file format: {{.filename}}, only {{.allowed}} formats are supported"
invalid_content = "The uploaded file is not a valid configuration file, please check the content"
name_too_long = "File name is too long, exceeding the {{.max_length}} character limit"

[auth]
name_pass_error = "Invalid username or password"
token_invalid = "Invalid token"
token_expired = "Token expired"
permission_error = "Permission error"
token_cache_error = "TOKEN CACHE Error"
access_denied = "Access denied"
account_disabled = "This user has been disabled, please contact administrator"

[data]
empty = "Data is empty"
duplicate = "Duplicate record exists"
not_found = "Record not found"
query_error = "Query error: {{.error}}"

[param]
error = "Parameter error"
invalid_mode = "Invalid mode, must be {{.mode}}"
missing_fields = "Parameters {{.fields}} are required in {{.mode}} mode"
invalid_value = "Invalid parameter value: {{.param}}"
invalid_model = "Unsupported model: {{.model}}"
invalid_version = "Unsupported version: {{.version}}"

[convert]
success = "Conversion successful"
failed = "Conversion failed: {{.error}}"
extract_success = "Interface extraction successful"
extraction_failed = "Interface extraction failed: {{.error}}"
verification_success = "Verification successful"
verification_failed = "Verification failed: {{.error}}"
version_detection_failed = "Could not detect version information from config file: {{.error}}"
processing = "Conversion in progress, please check status later"
download_success = "File downloaded successfully"
log_view = "Conversion Log"
mapping_error = "Interface mapping error: {{.error}}"
engine_error = "Conversion engine error: {{.error}}"
python_error = "Python execution error: {{.error}}"
report_title = "Conversion Report"
report_summary = "Conversion Summary"
report_details = "Conversion Details"
report_stats = "Conversion Statistics"
report_warnings = "Conversion Warnings"
report_errors = "Conversion Errors"
no_warnings = "No warning information"
no_errors = "No error information"
interface_count = "Interface count: {{.count}}"
policy_count = "Policy count: {{.count}}"
route_count = "Route count: {{.count}}"
nat_count = "NAT rule count: {{.count}}"
config_size = "Configuration size: {{.size}}"
execution_time = "Execution time: {{.time}}"
conversion_date = "Conversion date: {{.date}}"
convert.verification_success = "Verification successful"
convert.verification_failed = "Verification failed: {{.error}}"
convert.conversion_success = "Conversion successful"
convert.conversion_failed = "Conversion failed: {{.error}}"

[interface]
extraction_title = "Interface Extraction"
mapping_title = "Interface Mapping"
no_interfaces = "No interfaces found"
interface_count = "Found {{.count}} interfaces"
interface_name = "Interface Name"
interface_type = "Interface Type"
interface_ip = "IP Address"
interface_mask = "Subnet Mask"
interface_desc = "Description"
interface_status = "Status"
status_up = "Enabled"
status_down = "Disabled"
mapping_empty = "Mapping data is empty"
no_mappings = "Mapping data contains no interface mappings"
empty_source = "Mapping contains empty source interface name"
invalid_value_type = "Mapping value for interface '{{.interface}}' must be a string"
empty_target = "Mapping target for interface '{{.interface}}' is empty"
duplicate_target = "Target interface '{{.target}}' is used by multiple source interfaces ('{{.source1}}' and '{{.source2}}'), many-to-one mapping is not allowed"

[language]
current = "Current language"
switch_success = "Language switch successful"
switch_failed = "Language switch failed"
zh_CN = "Chinese"
en_US = "English"

[error]
file_not_exists = "File does not exist"
file_empty = "File is empty"
verification_error = "Error during verification process: {{.error}}"
vendor_not_supported = "Unsupported vendor: {{.vendor}}, currently supported: {{.supported}}"
invalid_fortigate_config = "Invalid FortiGate configuration file"
unable_to_detect_version = "Unable to detect FortiOS version of the configuration file"
unsupported_fortigate_version = "Unsupported FortiOS version: {{.version}}, minimum required version: {{.min_version}}"
extraction_failed = "Interface extraction failed: {{.error}}"
missing_interface_config = "Configuration file is missing system interface configuration section"

[log]
task_start = "=== Configuration Conversion Task Started ==="
task_id = "Task ID: {{.id}}"
vendor = "Vendor: {{.vendor}}"
model = "Model: {{.model}}"
version = "Version: {{.version}}"
input_file = "Input File: {{.file}}"
start_time = "Start Time: {{.time}}"
mapping_file_loaded = "Using interface mapping file: Loaded"
conversion_start = "=== Starting Conversion ==="
detailed_logs = "=== Detailed Conversion Logs ==="
conversion_success = "Conversion successful: Configuration was successfully converted"
xml_file_success = "XML file generation successful: {{.file}}"
xml_file_no_checksum = "XML file generated, but could not calculate checksum"
encrypt_file_success = "Encrypted file generation successful"
conversion_complete = "Conversion complete, total time: {{.time}}"
original_file_deleted = "Original configuration file deleted"
error_model_lookup = "Could not lookup model abbreviation: {{.model}}, error: {{.error}}"
error_version_lookup = "Could not lookup version abbreviation: {{.version}}, error: {{.error}}"
engine_debug_logs = "=== Engine Debug Logs ==="
conversion_failed = "Conversion failed: {{.error}}"
conversion_unsuccessful = "Conversion was not successful: {{.message}}"
error_details = "=== Error Details ==="
python_engine_errors = "=== Python Engine Error Messages ==="
engine_conversion_details = "=== Engine Conversion Details ==="
engine_error_details = "=== Engine Error Details ==="

[redis]
dial_error = "Redis connection error: {{.error}}"
invalid_connection = "Invalid connection from Redis connection pool: {{.error}}"
client_not_initialized_for_prewarm = "Redis client not initialized, cannot prewarm connection pool"
client_initialization_incomplete = "Redis client initialization incomplete, cannot prewarm connection pool"
prewarm_start = "Starting to prewarm Redis connection pool, connection count: {{.count}}"
prewarm_get_connection_failed = "Failed to get connection while prewarming pool: {{.error}}"
prewarm_ping_failed = "PING command failed while prewarming pool: {{.error}}"
prewarm_completed = "Redis connection pool prewarming completed, successful connections: {{.count}}"
empty_address_list = "Redis address list is empty, cannot initialize Redis client"
trying_single_mode_connection = "Trying to connect to Redis in single instance mode: {{.addr}}"
single_mode_connection_success = "Successfully connected to Redis in single instance mode"
single_mode_ping_failed = "Single instance mode PING failed, trying cluster mode: {{.error}}"
create_single_pool_failed = "Failed to create single instance connection pool: {{.error}}"
trying_cluster_mode_connection = "Trying to connect to Redis in cluster mode: {{.addrs}}"
cluster_mode_not_supported = "Redis does not support cluster mode, falling back to single instance mode"
single_mode_connection_failed = "Failed to connect to Redis in single instance mode: {{.error}}"
fallback_ping_failed = "Fallback to single instance mode but PING failed: {{.error}}"
refresh_cluster_info_failed = "Failed to refresh Redis cluster info: {{.error}}"
cluster_ping_failed = "Cluster connection PING failed: {{.error}}"
cluster_connection_success = "Successfully connected to Redis in cluster mode"
init_failed_all_modes = "Redis initialization failed, all connection methods failed"
client_nil = "Error: Redis client object is nil"
client_not_initialized_error = "Error: Redis client not successfully initialized"
single_pool_nil = "Error: Redis single instance connection pool is nil"
get_connection_from_single_pool_failed = "Error: Failed to get connection from single instance pool: {{.error}}"
cluster_client_nil = "Error: Redis cluster client is nil"
get_connection_from_cluster_failed = "Error: Failed to get connection from cluster client: {{.error}}"
client_not_initialized = "Redis client is not initialized"
cannot_get_connection = "Cannot get Redis connection"
network_error = "Redis network error, command: {{.command}}, error: {{.error}}"
command_execution_error = "Redis command execution error, command: {{.command}}, error: {{.error}}"
cannot_reconnect_no_saved_info = "Cannot reconnect: Global Redis client not initialized and no saved connection info"
reconnect_success = "Redis reconnection successful"
reconnect_failed = "Redis reconnection failed"
connection_closed = "Redis connection closed"
client_object_nil = "Redis client object is nil"
client_not_successfully_initialized = "Redis client was not successfully initialized"
ping_failed = "Redis PING failed: {{.error}}"
global_client_not_initialized = "Global Redis client not initialized"
invalid_expire_seconds = "Invalid Redis expire seconds: {{.seconds}}"

[internal]
# Internal log messages - for system internal logging
unsupported_file_format = "Unsupported file format: {{.format}}, filename: {{.filename}}"
filename_too_long = "Filename too long: {{.filename}}, length: {{.length}}"
filename_too_long_save_failed = "Failed to save due to filename too long: {{.error}}"
save_file_failed = "Failed to save file: {{.error}}"
invalid_config_content = "Invalid configuration file content: {{.path}}"
system_error_occurred = "System error: {{.error}}"
verification_failed_version_detection = "Verification failed - version detection error: {{.message}}"
verification_failed_business_error = "Verification failed - business error: {{.message}}"
config_file_format_invalid = "Configuration file format validation failed: file does not contain valid FortiGate configuration content"
syntax_errors_detected_enhanced = "Configuration file contains syntax errors. Possibly incorrect format of multiline content (such as HTML templates, certificates, etc.), please check the syntax format of the configuration file."
verification_syntax_error = "Syntax error detected during verification: {{.message}}"
unable_to_get_upload_file = "Unable to get uploaded file: {{.error}}"
create_temp_dir_failed = "Failed to create temporary directory: {{.error}}"
save_upload_file_failed = "Failed to save uploaded file: {{.error}}"
extract_interface_failed_version_detection = "Interface extraction failed - version detection error: {{.message}}"
extract_interface_failed_business_error = "Interface extraction failed - business error: {{.message}}"
redis_task_counter_init_failed = "Still unable to initialize task counter even after reconnection"
redis_reconnect_failed = "Redis reconnection failed, unable to create task"
acquire_task_slot_failed = "Failed to acquire task slot: {{.error}}"
release_task_slot_failed = "Failed to release task slot: {{.error}}"
model_lookup_failed = "Failed to lookup model: {{.model}}, error: {{.error}}"
version_lookup_failed = "Failed to lookup version: {{.version}}, error: {{.error}}"
task_slot_release_failed_after_completion = "Failed to release task slot after completion: {{.error}}"
error_message_too_long_truncated = "...(error message too long, truncated)"
create_user_log_file_failed = "Failed to create user log file: {{.error}}"
create_debug_log_file_failed = "Failed to create debug log file: {{.error}}"
create_full_log_file_failed = "Failed to create full log file: {{.error}}"
error_prefix = "Error: {{.message}}"
model_short_name_mapping = "Model short name: {{.model}} -> {{.short_name}}"
version_short_name_mapping = "Version short name: {{.version}} -> {{.short_name}}"
encryption_failed_with_message = "Encryption failed: {{.message}}"
encryption_failed_no_details = "Encryption failed, unable to get detailed error information"
encryption_failed_invalid_output = "Encryption failed, output file is invalid or empty"
detailed_error_prefix = "Detailed error: {{.details}}"
calculate_xml_md5_failed = "Failed to calculate XML file MD5: {{.error}}"
xml_file_generated_successfully = "XML file generated successfully: {{.file}}"
delete_original_file_failed = "Failed to delete original file: {{.error}}"
original_file_deleted_successfully = "Original file deleted successfully: {{.file}}"
update_task_status_failed = "Failed to update task status: {{.error}}"
create_temp_log_dir_failed = "Failed to create temporary log directory: {{.error}}"
verification_failed_version_detection_error = "Verification failed - version detection error: {{.message}}"
verification_business_error = "Verification business error: {{.message}}"
verification_execution_failed = "Configuration verification execution failed: {{.error}}, output: {{.output}}"
python_script_execution_failed = "Python script execution failed: {{.error}}\nError log: {{.log}}"
verification_execution_failed_simple = "Configuration verification execution failed: {{.error}}"
parse_verification_result_failed = "Failed to parse verification result: {{.error}}, raw output: {{.output}}"
handle_verification_response_failed = "Failed to handle verification response: {{.error}}"
extract_interface_failed_version_detection_error = "Interface extraction failed - version detection error: {{.message}}"
extract_interface_failed_business_error_simple = "Interface extraction failed - business error: {{.message}}"
unsupported_vendor_error = "Error: unsupported vendor '{{.vendor}}', currently supported vendors: fortigate"
python_script_execution_failed_simple = "Python script execution failed: {{.error}}\nError log: {{.log}}"
version_detection_failed_error = "Error: unable to detect version information from configuration file"
python_script_execution_failed_generic = "Failed to execute Python script: {{.error}}"
parse_extraction_result_failed = "Failed to parse extraction result: {{.error}}, output length: {{.length}}"
handle_interface_extraction_response_failed = "Failed to handle interface extraction response: {{.error}}"
unable_to_parse_json_from_output = "Unable to parse JSON from output: {{.output}}, error: {{.error}}"
handle_conversion_response_failed = "Failed to handle conversion response: {{.error}}"
conversion_business_failed = "Conversion business failed: {{.message}}"
unable_to_find_complete_json = "Unable to find complete JSON, returning raw output"
invalid_json_fragment_indices = "Found JSON fragment indices are invalid: firstBrace={{.first}}, lastBrace={{.last}}"
open_config_file_failed = "Failed to open configuration file: {{.error}}"
read_config_file_failed = "Failed to read configuration file: {{.error}}"
open_file_failed = "Failed to open file: {{.error}}"
read_file_failed = "Failed to read file: {{.error}}"
uploaded_file_empty = "Uploaded file is empty"
uploaded_file_too_large = "Uploaded file is too large, exceeds 10MB limit"