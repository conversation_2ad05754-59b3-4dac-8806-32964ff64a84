[common]
success = "请求成功"

[system]
error = "系统错误，请联系管理员"
error_with_message = "系统错误: {{.error}}"
redis_error = "Redis连接错误，请稍后重试"
redis_reconnected = "Redis已重新连接，请重试操作"

[vendor]
not_supported = "错误: 不支持的厂商 '{{.vendor}}'，当前支持的厂商: {{.supported}}"
info = "当前支持的厂商"

[task]
status.processing = "处理中"
status.success = "成功"
status.failed = "失败"
status.unknown = "未知状态"
create_success = "任务创建成功"
created_processing = "任务已创建，正在处理中"
task_id = "任务ID"
vendor = "厂商"
model = "型号"
version = "版本"
status_name = "状态"
created_at = "创建时间"
limit_reached = "当前处理中的任务数量已达到上限: {{.current}}/{{.limit}}"

[file]
not_exists = "文件不存在"
save_failed = "保存文件失败: {{.error}}"
read_failed = "读取文件失败: {{.error}}"
parse_failed = "解析文件失败: {{.error}}"
invalid_format = "文件格式不正确"
upload_error = "无法获取上传文件: {{.error}}"
empty = "上传的文件为空"
too_large = "上传的文件过大，超过{{.size}}MB限制"
temp_dir_error = "创建临时目录失败: {{.error}}"
invalid_extension = "不支持的文件格式: {{.filename}}，仅支持{{.allowed}}格式"
invalid_content = "上传的文件不是有效的配置文件，请检查文件内容"
name_too_long = "文件名过长，超过{{.max_length}}个字符的限制"

[auth]
name_pass_error = "用户名密码错误"
token_invalid = "Token 无效"
token_expired = "Token 过期"
permission_error = "权限错误"
token_cache_error = "TOKEN CACHE 错误"
access_denied = "禁止访问"
account_disabled = "该用户已禁用，请联系管理员"

[data]
empty = "数据为空"
duplicate = "存在重复记录"
not_found = "记录不存在"
query_error = "查询错误: {{.error}}"

[param]
error = "参数错误"
invalid_mode = "无效的工作模式，必须是{{.mode}}"
missing_fields = "{{.mode}}模式下必须提供{{.fields}}参数"
invalid_value = "参数值无效: {{.param}}"
invalid_model = "不支持的型号: {{.model}}"
invalid_version = "不支持的版本: {{.version}}"

[convert]
success = "转换成功"
failed = "转换失败: {{.error}}"
extract_success = "接口提取成功"
extraction_failed = "接口提取失败: {{.error}}"
verification_success = "验证成功"
verification_failed = "验证失败: {{.error}}"
version_detection_failed = "无法检测配置文件的版本信息: {{.error}}"
processing = "转换处理中，请稍后查看状态"
download_success = "文件下载成功"
log_view = "转换日志"
mapping_error = "接口映射错误: {{.error}}"
engine_error = "转换引擎错误: {{.error}}"
python_error = "Python执行错误: {{.error}}"
report_title = "转换报告"
report_summary = "转换概要"
report_details = "转换详情"
report_stats = "转换统计"
report_warnings = "转换警告"
report_errors = "转换错误"
no_warnings = "无警告信息"
no_errors = "无错误信息"
interface_count = "接口数量: {{.count}}"
policy_count = "策略数量: {{.count}}"
route_count = "路由数量: {{.count}}"
nat_count = "NAT规则数量: {{.count}}"
config_size = "配置大小: {{.size}}"
execution_time = "执行时间: {{.time}}"
conversion_date = "转换日期: {{.date}}"
convert.verification_success = "验证成功"
convert.verification_failed = "验证失败: {{.error}}"
convert.conversion_success = "转换成功"
convert.conversion_failed = "转换失败: {{.error}}"

[interface]
extraction_title = "接口提取"
mapping_title = "接口映射"
no_interfaces = "未找到接口"
interface_count = "找到{{.count}}个接口"
interface_name = "接口名称"
interface_type = "接口类型"
interface_ip = "IP地址"
interface_mask = "子网掩码"
interface_desc = "描述"
interface_status = "状态"
status_up = "启用"
status_down = "禁用"
mapping_empty = "映射数据为空"
no_mappings = "映射数据不包含任何接口映射"
empty_source = "映射中包含空的源接口名称"
invalid_value_type = "接口 '{{.interface}}' 的映射值必须是字符串类型"
empty_target = "接口 '{{.interface}}' 的映射目标为空"
duplicate_target = "目标接口 '{{.target}}' 被多个源接口使用 ('{{.source1}}' 和 '{{.source2}}')，不允许多对一映射"

[language]
current = "当前语言"
switch_success = "语言切换成功"
switch_failed = "语言切换失败"
zh_CN = "中文"
en_US = "英文"

[error]
file_not_exists = "文件不存在"
file_empty = "文件为空"
verification_error = "验证过程中发生错误: {{.error}}"
vendor_not_supported = "不支持的厂商: {{.vendor}}，当前支持: {{.supported}}"
invalid_fortigate_config = "无效的飞塔配置文件"
unable_to_detect_version = "无法检测配置文件的FortiOS版本"
unsupported_fortigate_version = "不支持的FortiOS版本: {{.version}}，最低要求版本: {{.min_version}}"
extraction_failed = "接口提取失败: {{.error}}"
missing_interface_config = "配置文件缺少system interface配置段"

[log]
task_start = "=== 配置转换任务开始 ==="
task_id = "任务ID: {{.id}}"
vendor = "厂商: {{.vendor}}"
model = "型号: {{.model}}"
version = "版本: {{.version}}"
input_file = "输入文件: {{.file}}"
start_time = "开始时间: {{.time}}"
mapping_file_loaded = "使用接口映射文件: 已加载"
conversion_start = "=== 开始执行转换 ==="
detailed_logs = "=== 详细转换日志 ==="
conversion_success = "转换成功: 配置已成功转换"
xml_file_success = "XML文件生成成功: {{.file}}"
xml_file_no_checksum = "XML文件生成，但无法计算校验和"
encrypt_file_success = "加密文件生成成功"
conversion_complete = "转换完成，总耗时: {{.time}}"
original_file_deleted = "已删除原始配置文件"
error_model_lookup = "无法查找型号简写: {{.model}}, 错误: {{.error}}"
error_version_lookup = "无法查找版本简写: {{.version}}, 错误: {{.error}}"
engine_debug_logs = "=== 引擎调试日志 ==="
conversion_failed = "转换失败: {{.error}}"
conversion_unsuccessful = "转换未成功: {{.message}}"
error_details = "=== 错误详情 ==="
python_engine_errors = "=== Python引擎错误信息 ==="
engine_conversion_details = "=== 引擎转换详情 ==="
engine_error_details = "=== 引擎错误详情 ==="

[redis]
dial_error = "Redis连接错误：{{.error}}"
invalid_connection = "从Redis连接池取出连接无效：{{.error}}"
client_not_initialized_for_prewarm = "Redis客户端未初始化，无法预热连接池"
client_initialization_incomplete = "Redis客户端初始化未完成，无法预热连接池"
prewarm_start = "开始预热Redis连接池，预热连接数：{{.count}}"
prewarm_get_connection_failed = "预热连接池时获取连接失败：{{.error}}"
prewarm_ping_failed = "预热连接池时PING命令失败：{{.error}}"
prewarm_completed = "Redis连接池预热完成，成功预热连接数：{{.count}}"
empty_address_list = "Redis地址列表为空，无法初始化Redis客户端"
trying_single_mode_connection = "尝试以单实例模式连接到Redis：{{.addr}}"
single_mode_connection_success = "成功以单实例模式连接到Redis"
single_mode_ping_failed = "单实例模式PING失败，尝试集群模式：{{.error}}"
create_single_pool_failed = "无法创建单实例连接池：{{.error}}"
trying_cluster_mode_connection = "尝试以集群模式连接到Redis：{{.addrs}}"
cluster_mode_not_supported = "Redis不支持集群模式，回退到单实例模式"
single_mode_connection_failed = "无法以单实例模式连接到Redis：{{.error}}"
fallback_ping_failed = "回退到单实例模式但PING失败：{{.error}}"
refresh_cluster_info_failed = "刷新Redis集群信息失败：{{.error}}"
cluster_ping_failed = "集群连接PING失败：{{.error}}"
cluster_connection_success = "成功以集群模式连接到Redis"
init_failed_all_modes = "Redis初始化失败，所有连接方式都无法建立"
client_nil = "错误：Redis客户端对象为nil"
client_not_initialized_error = "错误：Redis客户端未成功初始化"
single_pool_nil = "错误：Redis单实例连接池为nil"
get_connection_from_single_pool_failed = "错误：从单实例连接池获取连接失败：{{.error}}"
cluster_client_nil = "错误：Redis集群客户端为nil"
get_connection_from_cluster_failed = "错误：从集群客户端获取连接失败：{{.error}}"
client_not_initialized = "Redis客户端未初始化"
cannot_get_connection = "无法获取Redis连接"
network_error = "Redis网络错误，命令：{{.command}}，错误：{{.error}}"
command_execution_error = "Redis命令执行错误，命令：{{.command}}，错误：{{.error}}"
cannot_reconnect_no_saved_info = "无法重连：全局Redis客户端未初始化且没有保存的连接信息"
reconnect_success = "Redis重连成功"
reconnect_failed = "Redis重连失败"
connection_closed = "Redis连接已关闭"
client_object_nil = "Redis客户端对象为nil"
client_not_successfully_initialized = "Redis客户端未成功初始化"
ping_failed = "Redis PING失败：{{.error}}"
global_client_not_initialized = "全局Redis客户端未初始化"
invalid_expire_seconds = "Redis过期时间无效：{{.seconds}}"

[internal]
# 内部日志消息 - 用于系统内部日志记录
unsupported_file_format = "不支持的文件格式: {{.format}}, 文件名: {{.filename}}"
filename_too_long = "文件名过长: {{.filename}}, 长度: {{.length}}"
filename_too_long_save_failed = "文件名过长导致保存失败: {{.error}}"
save_file_failed = "保存文件失败: {{.error}}"
invalid_config_content = "无效的配置文件内容: {{.path}}"
system_error_occurred = "系统错误: {{.error}}"
verification_failed_version_detection = "验证失败 - 版本检测错误: {{.message}}"
verification_failed_business_error = "验证失败 - 业务错误: {{.message}}"
config_file_format_invalid = "配置文件格式验证失败：文件不包含有效的FortiGate配置内容"
syntax_errors_detected_enhanced = "配置文件包含语法错误。可能是多行内容（如HTML模板、证书等）格式不正确，请检查配置文件的语法格式。"
verification_syntax_error = "验证过程中发现语法错误: {{.message}}"
unable_to_get_upload_file = "无法获取上传文件: {{.error}}"
create_temp_dir_failed = "创建临时目录失败: {{.error}}"
save_upload_file_failed = "保存上传文件失败: {{.error}}"
extract_interface_failed_version_detection = "提取接口失败 - 版本检测错误: {{.message}}"
extract_interface_failed_business_error = "提取接口失败 - 业务错误: {{.message}}"
redis_task_counter_init_failed = "即使在重连后仍无法初始化任务计数器"
redis_reconnect_failed = "Redis重连失败，无法创建任务"
acquire_task_slot_failed = "获取任务槽位失败: {{.error}}"
release_task_slot_failed = "释放任务槽位失败: {{.error}}"
model_lookup_failed = "查找型号失败: {{.model}}, 错误: {{.error}}"
version_lookup_failed = "查找版本失败: {{.version}}, 错误: {{.error}}"
task_slot_release_failed_after_completion = "任务完成后释放槽位失败: {{.error}}"
error_message_too_long_truncated = "...(错误信息过长，已截断)"
create_user_log_file_failed = "创建用户日志文件错误: {{.error}}"
create_debug_log_file_failed = "创建调试日志文件错误: {{.error}}"
create_full_log_file_failed = "创建完整日志文件错误: {{.error}}"
error_prefix = "错误: {{.message}}"
model_short_name_mapping = "型号简写: {{.model}} -> {{.short_name}}"
version_short_name_mapping = "版本简写: {{.version}} -> {{.short_name}}"
encryption_failed_with_message = "加密失败: {{.message}}"
encryption_failed_no_details = "加密失败，无法获取详细错误信息"
encryption_failed_invalid_output = "加密失败，输出文件无效或为空"
detailed_error_prefix = "详细错误: {{.details}}"
calculate_xml_md5_failed = "计算XML文件MD5失败: {{.error}}"
xml_file_generated_successfully = "XML文件生成成功: {{.file}}"
delete_original_file_failed = "删除原始配置文件失败: {{.error}}"
original_file_deleted_successfully = "已删除原始配置文件: {{.file}}"
update_task_status_failed = "更新任务状态失败: {{.error}}"
create_temp_log_dir_failed = "创建临时日志目录失败: {{.error}}"
verification_failed_version_detection_error = "验证失败 - 版本检测错误: {{.message}}"
verification_business_error = "验证业务错误: {{.message}}"
verification_execution_failed = "验证配置文件执行失败: {{.error}}, 输出: {{.output}}"
python_script_execution_failed = "Python脚本执行失败: {{.error}}\n错误日志: {{.log}}"
verification_execution_failed_simple = "验证配置文件执行失败: {{.error}}"
parse_verification_result_failed = "解析验证结果失败: {{.error}}, 原始输出: {{.output}}"
handle_verification_response_failed = "处理验证响应失败: {{.error}}"
extract_interface_failed_version_detection_error = "提取接口失败 - 版本检测错误: {{.message}}"
extract_interface_failed_business_error_simple = "提取接口失败 - 业务错误: {{.message}}"
unsupported_vendor_error = "错误: 不支持的厂商 '{{.vendor}}'，当前支持的厂商: fortigate"
python_script_execution_failed_simple = "Python脚本执行失败: {{.error}}\n错误日志: {{.log}}"
version_detection_failed_error = "错误: 无法检测配置文件的版本信息"
python_script_execution_failed_generic = "执行Python脚本失败: {{.error}}"
parse_extraction_result_failed = "解析提取结果失败: {{.error}}, 输出长度: {{.length}}"
handle_interface_extraction_response_failed = "处理接口提取响应失败: {{.error}}"
unable_to_parse_json_from_output = "无法从输出中解析JSON: {{.output}}, 错误: {{.error}}"
handle_conversion_response_failed = "处理转换响应失败: {{.error}}"
conversion_business_failed = "转换业务失败: {{.message}}"
unable_to_find_complete_json = "无法找到完整的JSON，返回原始输出"
invalid_json_fragment_indices = "找到的JSON片段索引无效：firstBrace={{.first}}, lastBrace={{.last}}"
open_config_file_failed = "打开配置文件失败: {{.error}}"
read_config_file_failed = "读取配置文件失败: {{.error}}"
open_file_failed = "打开文件失败: {{.error}}"
read_file_failed = "读取文件失败: {{.error}}"
uploaded_file_empty = "上传的文件为空"
uploaded_file_too_large = "上传的文件过大，超过10MB限制"