#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
四层优化策略性能基准测试
测量实际的优化比例和处理时间差异
"""

import sys
import os
import time
import json
import subprocess
import re
from typing import Dict, List, Tuple
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def run_conversion_benchmark(config_file: str, iterations: int = 3) -> Dict:
    """运行转换性能基准测试"""
    print(f"🔄 运行转换性能基准测试")
    print(f"📁 配置文件: {config_file}")
    print(f"🔄 测试次数: {iterations}")
    print("=" * 60)
    
    results = {
        'config_file': config_file,
        'iterations': iterations,
        'execution_times': [],
        'optimization_metrics': [],
        'average_time': 0,
        'optimization_ratio': 0,
        'performance_improvement': 0,
        'detailed_stats': []
    }
    
    for i in range(iterations):
        print(f"🧪 执行第 {i+1}/{iterations} 次测试...")
        
        start_time = time.time()
        
        # 执行转换
        try:
            result = subprocess.run([
                'python', 'engine/main.py',
                '--mode', 'convert',
                '--cli', config_file,
                '--output', f'output/benchmark_result_{i}.xml',
                '--model', 'z5100s',
                '--version', 'R11',
                '--mapping', 'mappings/interface_mapping_correct.json'
            ], capture_output=True, text=True, timeout=300)
            
            execution_time = time.time() - start_time
            results['execution_times'].append(execution_time)
            
            print(f"   ⏱️ 执行时间: {execution_time:.2f}秒")
            
            # 提取优化指标
            if result.returncode == 0:
                optimization_metrics = extract_optimization_metrics_from_log()
                results['optimization_metrics'].append(optimization_metrics)
                results['detailed_stats'].append({
                    'iteration': i + 1,
                    'execution_time': execution_time,
                    'return_code': result.returncode,
                    'optimization_metrics': optimization_metrics
                })
                print(f"   ✅ 转换成功")
                if optimization_metrics.get('optimization_ratio', 0) > 0:
                    print(f"   🎯 优化比例: {optimization_metrics['optimization_ratio']:.1%}")
            else:
                print(f"   ❌ 转换失败: {result.stderr[:200]}...")
                results['detailed_stats'].append({
                    'iteration': i + 1,
                    'execution_time': execution_time,
                    'return_code': result.returncode,
                    'error': result.stderr[:500]
                })
                
        except subprocess.TimeoutExpired:
            execution_time = time.time() - start_time
            print(f"   ⏰ 执行超时: {execution_time:.2f}秒")
            results['execution_times'].append(execution_time)
            results['detailed_stats'].append({
                'iteration': i + 1,
                'execution_time': execution_time,
                'timeout': True
            })
        except Exception as e:
            execution_time = time.time() - start_time
            print(f"   ❌ 执行异常: {str(e)}")
            results['execution_times'].append(execution_time)
            results['detailed_stats'].append({
                'iteration': i + 1,
                'execution_time': execution_time,
                'exception': str(e)
            })
    
    # 计算平均值和统计
    if results['execution_times']:
        results['average_time'] = sum(results['execution_times']) / len(results['execution_times'])
    
    if results['optimization_metrics']:
        valid_metrics = [m for m in results['optimization_metrics'] if m.get('optimization_ratio', 0) > 0]
        if valid_metrics:
            avg_optimization = sum(m.get('optimization_ratio', 0) for m in valid_metrics) / len(valid_metrics)
            results['optimization_ratio'] = avg_optimization
            
            # 计算平均统计
            results['avg_total_sections'] = sum(m.get('total_sections', 0) for m in valid_metrics) / len(valid_metrics)
            results['avg_sections_skipped'] = sum(m.get('sections_skipped', 0) for m in valid_metrics) / len(valid_metrics)
            results['avg_sections_simplified'] = sum(m.get('sections_simplified', 0) for m in valid_metrics) / len(valid_metrics)
            results['avg_sections_full_processed'] = sum(m.get('sections_full_processed', 0) for m in valid_metrics) / len(valid_metrics)
    
    return results

def extract_optimization_metrics_from_log() -> Dict:
    """从日志中提取优化指标"""
    log_file = "output/info.log"
    metrics = {
        'optimization_ratio': 0,
        'sections_skipped': 0,
        'sections_simplified': 0,
        'sections_full_processed': 0,
        'total_sections': 0,
        'optimization_executed': False
    }
    
    if os.path.exists(log_file):
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查优化是否执行
            if "简化四层优化策略开始执行" in content or "simple_four_tier_optimization" in content:
                metrics['optimization_executed'] = True
            
            # 解析优化统计信息
            # 查找优化统计行
            pattern = r'优化统计 - 总计: (\d+), 跳过: (\d+), 简化: (\d+), 完整: (\d+)'
            match = re.search(pattern, content)
            if match:
                metrics['total_sections'] = int(match.group(1))
                metrics['sections_skipped'] = int(match.group(2))
                metrics['sections_simplified'] = int(match.group(3))
                metrics['sections_full_processed'] = int(match.group(4))
                
                if metrics['total_sections'] > 0:
                    metrics['optimization_ratio'] = (metrics['sections_skipped'] + metrics['sections_simplified']) / metrics['total_sections']
            
            # 查找优化比例
            ratio_pattern = r'优化比例: ([\d.]+)%'
            ratio_match = re.search(ratio_pattern, content)
            if ratio_match:
                metrics['optimization_ratio'] = float(ratio_match.group(1)) / 100
                
        except Exception as e:
            print(f"⚠️ 日志解析失败: {str(e)}")
    
    return metrics

def generate_performance_report(results: Dict):
    """生成性能报告"""
    print("\n📊 性能基准测试报告")
    print("=" * 60)
    
    print(f"📁 配置文件: {results['config_file']}")
    print(f"🔄 测试次数: {results['iterations']}")
    print(f"⏱️ 平均执行时间: {results['average_time']:.2f}秒")
    
    if results['optimization_ratio'] > 0:
        print(f"🎯 平均优化比例: {results['optimization_ratio']:.1%}")
        
        # 详细优化统计
        if 'avg_total_sections' in results:
            print(f"\n📋 平均优化统计:")
            print(f"   总段落数: {results['avg_total_sections']:.0f}")
            print(f"   跳过段落: {results['avg_sections_skipped']:.0f}")
            print(f"   简化段落: {results['avg_sections_simplified']:.0f}")
            print(f"   完整段落: {results['avg_sections_full_processed']:.0f}")
            
            # 计算各类型比例
            if results['avg_total_sections'] > 0:
                skip_ratio = results['avg_sections_skipped'] / results['avg_total_sections']
                simplify_ratio = results['avg_sections_simplified'] / results['avg_total_sections']
                full_ratio = results['avg_sections_full_processed'] / results['avg_total_sections']
                
                print(f"\n📈 段落处理比例:")
                print(f"   跳过比例: {skip_ratio:.1%}")
                print(f"   简化比例: {simplify_ratio:.1%}")
                print(f"   完整比例: {full_ratio:.1%}")
    else:
        print("⚠️ 未检测到优化效果")
    
    # 执行时间统计
    if len(results['execution_times']) > 1:
        min_time = min(results['execution_times'])
        max_time = max(results['execution_times'])
        print(f"\n⏱️ 执行时间统计:")
        print(f"   最短时间: {min_time:.2f}秒")
        print(f"   最长时间: {max_time:.2f}秒")
        print(f"   时间差异: {max_time - min_time:.2f}秒")
    
    # 成功率统计
    successful_runs = sum(1 for stat in results['detailed_stats'] if stat.get('return_code') == 0)
    success_rate = successful_runs / len(results['detailed_stats']) if results['detailed_stats'] else 0
    print(f"\n✅ 执行成功率: {success_rate:.1%} ({successful_runs}/{len(results['detailed_stats'])})")
    
    # 保存详细报告
    timestamp = int(time.time())
    report_file = f"performance_benchmark_report_{timestamp}.json"
    
    # 添加时间戳和元数据
    results['report_generated'] = datetime.now().isoformat()
    results['report_file'] = report_file
    
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        print(f"\n💾 详细报告已保存: {report_file}")
    except Exception as e:
        print(f"⚠️ 报告保存失败: {str(e)}")
    
    return results

def analyze_optimization_effectiveness(results: Dict):
    """分析优化效果"""
    print("\n🔍 优化效果分析")
    print("=" * 60)
    
    # 目标指标
    target_optimization_ratio = 0.35  # 35%
    target_skip_ratio = 0.25  # 25%
    target_simplify_ratio = 0.20  # 20%
    
    actual_optimization = results.get('optimization_ratio', 0)
    
    print(f"🎯 目标优化比例: {target_optimization_ratio:.1%}")
    print(f"📊 实际优化比例: {actual_optimization:.1%}")
    
    if actual_optimization >= target_optimization_ratio:
        print("✅ 优化比例达到目标")
        effectiveness = "优秀"
    elif actual_optimization >= target_optimization_ratio * 0.8:
        print("⚡ 优化比例接近目标")
        effectiveness = "良好"
    elif actual_optimization > 0:
        print("⚠️ 优化比例低于目标")
        effectiveness = "需要改进"
    else:
        print("❌ 未检测到优化效果")
        effectiveness = "失败"
    
    # 分析各类型处理比例
    if 'avg_total_sections' in results and results['avg_total_sections'] > 0:
        skip_ratio = results['avg_sections_skipped'] / results['avg_total_sections']
        simplify_ratio = results['avg_sections_simplified'] / results['avg_total_sections']
        
        print(f"\n📋 处理类型分析:")
        print(f"   跳过比例: {skip_ratio:.1%} (目标: {target_skip_ratio:.1%})")
        print(f"   简化比例: {simplify_ratio:.1%} (目标: {target_simplify_ratio:.1%})")
        
        skip_status = "✅" if skip_ratio >= target_skip_ratio * 0.8 else "⚠️" if skip_ratio > 0 else "❌"
        simplify_status = "✅" if simplify_ratio >= target_simplify_ratio * 0.8 else "⚠️" if simplify_ratio > 0 else "❌"
        
        print(f"   跳过效果: {skip_status}")
        print(f"   简化效果: {simplify_status}")
    
    print(f"\n🏆 总体评价: {effectiveness}")
    
    return effectiveness

def main():
    """主测试函数"""
    print("⚡ 四层优化策略性能基准测试工具")
    print("=" * 60)
    
    config_file = "FortiGate-100F_7-6_3510_202505161613.conf"
    
    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        return False
    
    # 运行基准测试
    results = run_conversion_benchmark(config_file, iterations=3)
    
    # 生成报告
    generate_performance_report(results)
    
    # 分析优化效果
    effectiveness = analyze_optimization_effectiveness(results)
    
    # 总结
    print("\n" + "=" * 60)
    print("🎯 基准测试总结")
    print("=" * 60)
    
    if results['optimization_ratio'] > 0:
        print("✅ 四层优化策略正常执行")
        print(f"📊 优化比例: {results['optimization_ratio']:.1%}")
        print(f"⏱️ 平均执行时间: {results['average_time']:.2f}秒")
        print(f"🏆 优化效果: {effectiveness}")
        
        # 建议
        if effectiveness == "需要改进":
            print("\n💡 改进建议:")
            print("1. 检查跳过模式是否覆盖足够的配置段落")
            print("2. 优化简化处理模式的匹配规则")
            print("3. 考虑增加更多的优化策略")
        elif effectiveness == "失败":
            print("\n🔧 修复建议:")
            print("1. 检查四层优化策略是否正确执行")
            print("2. 验证配置段落提取和分类逻辑")
            print("3. 确认优化统计日志是否正确生成")
        
        return True
    else:
        print("❌ 四层优化策略未执行或无效果")
        print("\n🔧 需要检查:")
        print("1. 四层优化策略是否正确集成到管道")
        print("2. 优化决策逻辑是否正常工作")
        print("3. 日志记录是否完整")
        
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
