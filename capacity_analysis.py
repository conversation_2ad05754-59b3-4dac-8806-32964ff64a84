#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
容量验证分析脚本
"""

import sys
import os
import json

# 添加engine目录到Python路径
sys.path.insert(0, os.path.join(os.getcwd(), 'engine'))

from verify import count_resources, perform_capacity_validation
import json

def load_capacity_limits_fixed():
    """修复版本的容量限制加载函数"""
    try:
        config_file = os.path.join('engine', 'data', 'capacity_limits.json')
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            print(f"错误：容量配置文件未找到: {config_file}")
            return None
    except Exception as e:
        print(f"错误：加载容量配置失败: {str(e)}")
        return None

def analyze_capacity():
    """分析配置文件的容量使用情况"""

    # 读取配置文件
    config_file = 'KHU-FGT-1_7-0_0682_202507311406.conf'
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config_content = f.read()
    except FileNotFoundError:
        print(f"错误：找不到配置文件 {config_file}")
        return

    print('=== 资源统计分析 ===')
    resource_counts = count_resources(config_content)

    print('\n各类资源统计结果:')
    for resource_type, count in resource_counts.items():
        print(f'{resource_type}: {count}')

    print('\n=== 容量限制配置 ===')
    capacity_limits = load_capacity_limits_fixed()
    
    if not capacity_limits:
        print("错误：无法加载容量限制配置")
        return
        
    if 'z3200s' not in capacity_limits.get('device_models', {}):
        print("错误：找不到z3200s设备配置")
        return
    
    z3200s_limits = capacity_limits['device_models']['z3200s']['limits']
    print('\nZ3200S设备限制对比:')
    violations_found = []
    
    for resource_type, limit_info in z3200s_limits.items():
        max_limit = limit_info['max']
        current_count = resource_counts.get(resource_type, 0)
        status = '超限' if current_count > max_limit else '正常'
        print(f'{resource_type}: {current_count}/{max_limit} ({status})')
        
        if current_count > max_limit:
            violations_found.append({
                'resource_type': resource_type,
                'current_count': current_count,
                'max_limit': max_limit,
                'violation_count': current_count - max_limit
            })
    
    print('\n=== 容量验证结果 ===')
    violations = perform_capacity_validation(config_content, 'z3200s')
    print(f'容量验证函数发现 {len(violations)} 个容量违规:')
    
    if violations:
        for violation in violations:
            print(f'- {violation.resource_type}: {violation.current_count}/{violation.max_limit} (超出 {violation.violation_count})')
            print(f'  严重级别: {violation.severity}')
            print(f'  建议: {violation.suggestion}')
            print()
    else:
        print("容量验证函数未发现任何违规")
    
    print('\n=== 问题诊断 ===')
    if violations_found and not violations:
        print("发现问题：手动计算发现容量超限，但容量验证函数未检测到违规")
        print("可能的原因：")
        print("1. 容量验证函数存在bug")
        print("2. 资源统计函数计算错误")
        print("3. 容量配置文件加载问题")
        
        print(f"\n手动发现的违规项目 ({len(violations_found)} 个):")
        for violation in violations_found:
            print(f"- {violation['resource_type']}: {violation['current_count']}/{violation['max_limit']} (超出 {violation['violation_count']})")
    
    elif violations_found and violations:
        print("容量验证功能正常工作")
        if len(violations_found) != len(violations):
            print(f"警告：手动计算发现 {len(violations_found)} 个违规，但验证函数只发现 {len(violations)} 个")
    
    elif not violations_found and not violations:
        print("配置文件未超出z3200s设备的容量限制")
    
    print('\n=== 详细资源分析 ===')
    print("重点关注的资源类型:")
    key_resources = ['security_policies', 'nat44_policies', 'address_objects', 'service_objects', 'sub_interfaces', 'static_routes_v4']
    
    for resource_type in key_resources:
        if resource_type in resource_counts and resource_type in z3200s_limits:
            current = resource_counts[resource_type]
            limit = z3200s_limits[resource_type]['max']
            usage_percent = (current / limit) * 100
            print(f"{resource_type}: {current}/{limit} ({usage_percent:.1f}%)")

if __name__ == "__main__":
    analyze_capacity()
