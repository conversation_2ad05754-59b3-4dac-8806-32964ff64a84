#!/usr/bin/env python3
"""
检查NAT重复情况的脚本
"""

import os
import xml.etree.ElementTree as ET
from collections import defaultdict

def analyze_nat_duplication(xml_file):
    """分析XML文件中的NAT重复情况"""
    print(f"🔍 分析文件: {xml_file}")
    
    if not os.path.exists(xml_file):
        print(f"❌ 文件不存在: {xml_file}")
        return False
    
    try:
        # 解析XML文件
        tree = ET.parse(xml_file)
        root = tree.getroot()
        
        # 查找所有NAT元素
        nat_elements = []
        
        # 使用不同的命名空间查找方式
        for elem in root.iter():
            if elem.tag.endswith('nat') or elem.tag == 'nat':
                nat_elements.append(elem)
        
        print(f"📊 找到 {len(nat_elements)} 个NAT元素")
        
        total_pools = 0
        total_rules = 0
        pool_names = defaultdict(list)
        rule_names = defaultdict(list)
        
        for i, nat_elem in enumerate(nat_elements):
            print(f"\n📋 NAT元素 {i+1}:")
            
            pools_in_this_nat = 0
            rules_in_this_nat = 0
            
            for child in nat_elem:
                if child.tag.endswith('pool') or child.tag == 'pool':
                    pools_in_this_nat += 1
                    total_pools += 1
                    
                    # 查找pool名称
                    name_elem = child.find('name')
                    if name_elem is None:
                        # 尝试不同的查找方式
                        for subchild in child:
                            if subchild.tag.endswith('name') or subchild.tag == 'name':
                                name_elem = subchild
                                break
                    
                    if name_elem is not None and name_elem.text:
                        pool_names[name_elem.text].append(f"NAT{i+1}")
                
                elif child.tag.endswith('rule') or child.tag == 'rule':
                    rules_in_this_nat += 1
                    total_rules += 1
                    
                    # 查找rule名称
                    name_elem = child.find('name')
                    if name_elem is None:
                        for subchild in child:
                            if subchild.tag.endswith('name') or subchild.tag == 'name':
                                name_elem = subchild
                                break
                    
                    if name_elem is not None and name_elem.text:
                        rule_names[name_elem.text].append(f"NAT{i+1}")
            
            print(f"   Pools: {pools_in_this_nat}")
            print(f"   Rules: {rules_in_this_nat}")
        
        print(f"\n📊 总计统计:")
        print(f"   总Pool数: {total_pools}")
        print(f"   总Rule数: {total_rules}")
        print(f"   唯一Pool名称数: {len(pool_names)}")
        print(f"   唯一Rule名称数: {len(rule_names)}")
        
        # 检查重复
        duplicate_pools = {name: locations for name, locations in pool_names.items() if len(locations) > 1}
        duplicate_rules = {name: locations for name, locations in rule_names.items() if len(locations) > 1}
        
        print(f"\n🔍 重复检查:")
        print(f"   重复Pool数: {len(duplicate_pools)}")
        print(f"   重复Rule数: {len(duplicate_rules)}")
        
        if duplicate_pools:
            print(f"\n❌ 重复的Pools:")
            for name, locations in list(duplicate_pools.items())[:5]:  # 只显示前5个
                print(f"   {name}: 出现在 {locations}")
        
        if duplicate_rules:
            print(f"\n❌ 重复的Rules:")
            for name, locations in list(duplicate_rules.items())[:5]:  # 只显示前5个
                print(f"   {name}: 出现在 {locations}")
        
        # 计算重复率
        if pool_names:
            pool_duplication_rate = len(duplicate_pools) / len(pool_names) * 100
            print(f"\n📈 Pool重复率: {pool_duplication_rate:.1f}%")
        
        if rule_names:
            rule_duplication_rate = len(duplicate_rules) / len(rule_names) * 100
            print(f"📈 Rule重复率: {rule_duplication_rate:.1f}%")
        
        # 判断是否有重复问题
        has_duplication = len(duplicate_pools) > 0 or len(duplicate_rules) > 0
        
        if has_duplication:
            print(f"\n⚠️ 发现重复问题！")
            return False
        else:
            print(f"\n✅ 没有发现重复问题")
            return True
        
    except Exception as e:
        print(f"❌ 分析失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 开始检查NAT重复情况")
    print("=" * 60)
    
    # 检查多个文件
    files_to_check = [
        "output/fortigate-z5100s-R11_backup_20250801_194353.xml",  # 最新备份
        "output/fortigate-z5100s-R11-fixed.xml",                   # 修复后的文件
        "output/fortigate-z5100s-R11-original.xml"                 # 原始文件
    ]
    
    results = {}
    
    for xml_file in files_to_check:
        print(f"\n{'='*60}")
        result = analyze_nat_duplication(xml_file)
        results[xml_file] = result
    
    print(f"\n{'='*60}")
    print("📊 总结:")
    
    for xml_file, result in results.items():
        filename = os.path.basename(xml_file)
        status = "✅ 无重复" if result else "❌ 有重复"
        print(f"   {filename}: {status}")
    
    # 检查是否有改进
    backup_file = "output/fortigate-z5100s-R11_backup_20250801_194353.xml"
    fixed_file = "output/fortigate-z5100s-R11-fixed.xml"
    
    if backup_file in results and fixed_file in results:
        if not results[backup_file] and results[fixed_file]:
            print(f"\n🎉 修复成功！从有重复变为无重复")
        elif not results[backup_file] and not results[fixed_file]:
            print(f"\n⚠️ 两个文件都有重复问题")
        elif results[backup_file] and results[fixed_file]:
            print(f"\n✅ 两个文件都没有重复问题")
    
    return any(results.values())

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
