#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
全面修复log()函数参数冲突问题

系统性地查找并修复所有可能导致"got multiple values for argument 'message'"错误的log()调用
"""

import os
import sys
import re
from pathlib import Path

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def find_all_problematic_log_calls():
    """查找所有有问题的log()函数调用"""
    
    print("🔍 全面搜索有问题的log()函数调用...")
    
    # 需要检查的目录
    directories_to_check = [
        "engine/business",
        "engine/processing", 
        "engine/infrastructure",
        "engine/utils"
    ]
    
    problematic_calls = []
    
    for directory in directories_to_check:
        if not os.path.exists(directory):
            print(f"   ⚠️ 目录不存在: {directory}")
            continue
        
        # 递归搜索Python文件
        for root, dirs, files in os.walk(directory):
            for file in files:
                if not file.endswith('.py'):
                    continue
                
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                    
                    for i, line in enumerate(lines):
                        # 查找形如 log(_("key"), "level", param=value) 的调用
                        if 'log(' in line and '_(' in line:
                            # 检查是否有额外的参数
                            if line.count(',') >= 3:  # log, _(), level, 额外参数
                                problematic_calls.append({
                                    'file': file_path,
                                    'line_number': i + 1,
                                    'line_content': line.strip(),
                                    'issue': 'potential_parameter_conflict'
                                })
                                print(f"   ❌ {file_path}:{i+1} - {line.strip()}")
                
                except Exception as e:
                    print(f"   ⚠️ 读取文件失败 {file_path}: {e}")
    
    return problematic_calls

def analyze_log_call_pattern(line):
    """分析log()调用模式"""
    
    # 匹配 log(_("key"), "level", param=value) 模式
    pattern = r'log\s*\(\s*_\s*\(\s*["\']([^"\']+)["\']([^)]*)\)\s*,\s*["\']([^"\']+)["\'](.*)?\)'
    match = re.search(pattern, line)
    
    if match:
        key = match.group(1)
        key_params = match.group(2)
        level = match.group(3)
        extra_params = match.group(4)
        
        return {
            'key': key,
            'key_params': key_params.strip(),
            'level': level,
            'extra_params': extra_params.strip() if extra_params else '',
            'has_conflict': bool(extra_params and extra_params.strip())
        }
    
    return None

def fix_log_call(line, analysis):
    """修复log()调用"""
    
    if not analysis or not analysis['has_conflict']:
        return line
    
    # 提取额外参数
    extra_params = analysis['extra_params']
    if extra_params.startswith(','):
        extra_params = extra_params[1:].strip()
    
    # 构建修复后的调用
    if analysis['key_params']:
        # 已有参数，添加额外参数
        fixed_call = f'log(_("{analysis["key"]}", {analysis["key_params"]}, {extra_params}), "{analysis["level"]}")'
    else:
        # 没有参数，添加额外参数
        fixed_call = f'log(_("{analysis["key"]}", {extra_params}), "{analysis["level"]}")'
    
    # 替换原始调用
    original_pattern = r'log\s*\(\s*_\s*\(\s*["\']' + re.escape(analysis['key']) + r'["\']([^)]*)\)\s*,\s*["\']' + re.escape(analysis['level']) + r'["\'].*?\)'
    
    return re.sub(original_pattern, fixed_call, line)

def fix_file_log_calls(file_path, problematic_calls):
    """修复文件中的log()调用"""
    
    file_calls = [call for call in problematic_calls if call['file'] == file_path]
    if not file_calls:
        return False
    
    print(f"\n🔧 修复文件: {file_path}")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        modified = False
        for call in file_calls:
            line_index = call['line_number'] - 1
            original_line = lines[line_index]
            
            # 分析调用模式
            analysis = analyze_log_call_pattern(original_line)
            if analysis and analysis['has_conflict']:
                # 修复调用
                fixed_line = fix_log_call(original_line, analysis)
                if fixed_line != original_line:
                    lines[line_index] = fixed_line
                    modified = True
                    print(f"   ✅ 第{call['line_number']}行已修复")
                    print(f"      原始: {original_line.strip()}")
                    print(f"      修复: {fixed_line.strip()}")
        
        if modified:
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.writelines(lines)
            print(f"   ✅ 文件已保存: {file_path}")
            return True
        
    except Exception as e:
        print(f"   ❌ 修复文件失败: {e}")
        return False
    
    return False

def test_yang_validation_after_fix():
    """修复后测试YANG验证"""
    
    print("\n🧪 测试修复后的YANG验证...")
    
    try:
        # 导入相关模块测试
        from engine.infrastructure.yang.yang_manager import YangManager
        from engine.utils.logger import log
        from engine.utils.i18n import _
        
        # 测试log()调用
        print("   测试log()调用...")
        
        # 测试修复后的调用方式
        test_msg = _("yang_manager.schema_cache_hit", cache_key="test_key")
        log(test_msg, "debug")
        print("   ✅ log()调用测试通过")
        
        # 测试YANG管理器创建
        yang_manager = YangManager()
        print("   ✅ YANG管理器创建成功")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def run_converter_test():
    """运行转换器测试"""
    
    print("\n🚀 运行转换器测试验证修复效果...")
    
    try:
        # 检查配置文件
        config_file = "Pass-Mask-MTU-FW-1_7-2_1639_202506271000.conf"
        if not os.path.exists(config_file):
            print(f"   ⚠️ 配置文件不存在: {config_file}")
            return False
        
        print("   ✅ 配置文件存在，建议运行完整转换测试")
        return True
        
    except Exception as e:
        print(f"   ❌ 转换器测试准备失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 全面修复log()函数参数冲突问题")
    print("=" * 60)
    
    # 1. 查找所有有问题的log()调用
    problematic_calls = find_all_problematic_log_calls()
    
    if not problematic_calls:
        print("\n✅ 没有发现log()函数参数冲突问题")
    else:
        print(f"\n⚠️ 发现 {len(problematic_calls)} 个潜在问题调用")
        
        # 2. 按文件分组修复
        files_to_fix = set(call['file'] for call in problematic_calls)
        fixed_files = 0
        
        for file_path in files_to_fix:
            if fix_file_log_calls(file_path, problematic_calls):
                fixed_files += 1
        
        print(f"\n✅ 已修复 {fixed_files} 个文件")
    
    # 3. 测试修复效果
    yang_test_ok = test_yang_validation_after_fix()
    converter_test_ok = run_converter_test()
    
    print("\n" + "=" * 60)
    print("🎉 log()函数参数冲突修复完成!")
    
    print("\n📋 修复结果:")
    print(f"   ✅ 发现问题调用: {len(problematic_calls)} 个")
    print(f"   ✅ 修复文件数量: {len(set(call['file'] for call in problematic_calls))} 个")
    print(f"   ✅ YANG验证测试: {'通过' if yang_test_ok else '失败'}")
    print(f"   ✅ 转换器测试准备: {'通过' if converter_test_ok else '失败'}")
    
    print("\n🔧 后续步骤:")
    print("1. 重新运行转换器测试")
    print("2. 验证YANG验证阶段不再出现log()参数冲突错误")
    print("3. 检查转换器是否能完整运行所有阶段")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
