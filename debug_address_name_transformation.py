#!/usr/bin/env python3
"""
调试地址名称转换过程
追踪IP地址格式的地址名称在整个转换过程中的变化
"""

import sys
import os
import json
sys.path.append('.')
sys.path.append('./engine')

def debug_fortigate_parsing():
    """调试FortiGate解析过程"""
    print("=== 调试FortiGate解析过程 ===")
    
    # 创建一个简单的测试配置
    test_config = """
config firewall address
    edit "************"
        set type ipmask
        set subnet ************ ***************
    next
    edit "************"
        set type ipmask
        set subnet ************ ***************
    next
end

config firewall addrgrp
    edit "test_group"
        set member "************" "************"
    next
end

config firewall policy
    edit 1
        set srcintf "port1"
        set dstintf "port2"
        set srcaddr "************"
        set dstaddr "************"
        set action accept
        set service "ALL"
    next
end
"""
    
    try:
        from engine.parsers.fortigate_parser import parse_fortigate_config
        
        print("解析测试配置...")
        result = parse_fortigate_config(test_config.split('\n'))
        
        print("\n地址对象解析结果:")
        for addr in result.get("address_objects", []):
            print(f"  名称: {addr.get('name')} | 类型: {addr.get('type')} | 子网: {addr.get('subnet')}")
        
        print("\n地址组解析结果:")
        for group in result.get("address_groups", []):
            print(f"  组名: {group.get('name')} | 成员: {group.get('members', [])}")
        
        print("\n策略解析结果:")
        for policy in result.get("policies", []):
            print(f"  策略ID: {policy.get('policyid')}")
            print(f"    源地址: {policy.get('srcaddr', [])}")
            print(f"    目标地址: {policy.get('dstaddr', [])}")
        
        return result
        
    except Exception as e:
        print(f"解析失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def debug_address_processing(parsed_data):
    """调试地址处理过程"""
    print("\n=== 调试地址处理过程 ===")
    
    if not parsed_data:
        print("没有解析数据")
        return None
    
    try:
        from engine.processing.stages.address_processing_stage import AddressObjectProcessor
        
        processor = AddressObjectProcessor()
        address_objects = {addr['name']: addr for addr in parsed_data.get("address_objects", [])}
        
        print(f"处理前的地址对象: {list(address_objects.keys())}")
        
        result = processor.process_address_objects(address_objects)
        
        print(f"处理后的地址对象: {list(result.get('converted', {}).keys())}")
        
        print("\n详细转换结果:")
        for name, addr_config in result.get('converted', {}).items():
            print(f"  {name}: {addr_config}")
        
        return result
        
    except Exception as e:
        print(f"地址处理失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def debug_policy_processing(parsed_data):
    """调试策略处理过程"""
    print("\n=== 调试策略处理过程 ===")
    
    if not parsed_data:
        print("没有解析数据")
        return None
    
    try:
        from engine.processors.policy_processor import PolicyProcessor
        
        processor = PolicyProcessor()
        policies = parsed_data.get("policies", [])
        
        print(f"处理前的策略数量: {len(policies)}")
        
        for policy in policies:
            print(f"\n策略 {policy.get('policyid')}:")
            print(f"  原始源地址: {policy.get('srcaddr', [])}")
            print(f"  原始目标地址: {policy.get('dstaddr', [])}")
            
            # 模拟策略处理过程中的地址名称清理
            from engine.utils.name_validator import clean_ntos_name
            
            srcaddr_list = policy.get("srcaddr", [])
            if isinstance(srcaddr_list, str):
                srcaddr_list = [srcaddr_list]
            
            dstaddr_list = policy.get("dstaddr", [])
            if isinstance(dstaddr_list, str):
                dstaddr_list = [dstaddr_list]
            
            print(f"  清理后源地址:")
            for addr in srcaddr_list:
                if addr and addr.upper() not in ["ALL", "NONE"]:
                    clean_addr_name = clean_ntos_name(addr, 64)
                    print(f"    {addr} → {clean_addr_name}")
            
            print(f"  清理后目标地址:")
            for addr in dstaddr_list:
                if addr and addr.upper() not in ["ALL", "NONE"]:
                    clean_addr_name = clean_ntos_name(addr, 64)
                    print(f"    {addr} → {clean_addr_name}")
        
        return True
        
    except Exception as e:
        print(f"策略处理失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def debug_name_validator_functions():
    """调试名称验证函数"""
    print("\n=== 调试名称验证函数 ===")
    
    try:
        from engine.utils.name_validator import clean_ntos_name, clean_ip_address_name, _looks_like_ip_address
        
        test_addresses = [
            "************",
            "************", 
            "************",
            "************",
            "************",
            "************",
            "************"
        ]
        
        print("名称验证函数测试:")
        for addr in test_addresses:
            is_ip_like = _looks_like_ip_address(addr)
            clean_name = clean_ntos_name(addr, 64)
            ip_clean_name = clean_ip_address_name(addr)
            
            print(f"  {addr}:")
            print(f"    IP检测: {is_ip_like}")
            print(f"    clean_ntos_name: {clean_name}")
            print(f"    clean_ip_address_name: {ip_clean_name}")
            print(f"    是否一致: {clean_name == addr}")
        
        return True
        
    except Exception as e:
        print(f"名称验证测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_for_name_mapping():
    """检查是否有名称映射逻辑"""
    print("\n=== 检查名称映射逻辑 ===")
    
    try:
        # 检查是否有名称映射管理器
        try:
            from engine.utils.name_mapping_manager import NameMappingManager
            print("发现名称映射管理器")
            
            # 创建实例并测试
            manager = NameMappingManager()
            test_name = "************"

            # 测试注册地址映射
            mapped_name = manager.register_name_mapping("addresses", test_name, context="测试地址")
            print(f"  注册地址映射: {test_name} → {mapped_name}")

            # 测试获取映射
            retrieved_name = manager.get_mapped_name("addresses", test_name)
            print(f"  获取地址映射: {test_name} → {retrieved_name}")

            # 测试策略中的地址引用
            policy_addr = "************"
            policy_mapped = manager.get_mapped_name("addresses", policy_addr)
            print(f"  策略地址引用: {policy_addr} → {policy_mapped}")

            # 检查映射统计
            stats = manager.get_mapping_stats()
            print(f"  映射统计: {stats}")

            # 检查地址映射表
            address_mappings = manager.name_mappings.get("addresses", {})
            print(f"  地址映射表: {address_mappings}")
            
        except ImportError:
            print("未发现名称映射管理器")
        
        # 检查是否有其他名称转换逻辑
        import os
        import glob
        
        print("\n搜索可能的名称转换代码...")
        search_patterns = [
            "*.py",
        ]
        
        found_files = []
        for pattern in search_patterns:
            for root, dirs, files in os.walk("engine"):
                for file in files:
                    if file.endswith(".py"):
                        found_files.append(os.path.join(root, file))
        
        # 搜索可能包含地址名称转换的文件
        suspicious_patterns = [
            "replace('.',",
            "replace('.', '_')",
            "\.replace\('\.\', '_'\)",
            "address.*name.*mapping",
            "name.*transform"
        ]
        
        print(f"搜索 {len(found_files)} 个Python文件...")
        for file_path in found_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    for pattern in suspicious_patterns:
                        if pattern in content:
                            print(f"  发现可疑模式 '{pattern}' 在文件: {file_path}")
            except:
                continue
        
        return True
        
    except Exception as e:
        print(f"检查失败: {e}")
        return False

def main():
    """主函数"""
    print("地址名称转换过程调试")
    print("=" * 60)
    
    # 1. 调试名称验证函数
    debug_name_validator_functions()
    
    # 2. 调试FortiGate解析
    parsed_data = debug_fortigate_parsing()
    
    # 3. 调试地址处理
    if parsed_data:
        debug_address_processing(parsed_data)
    
    # 4. 调试策略处理
    if parsed_data:
        debug_policy_processing(parsed_data)
    
    # 5. 检查名称映射
    check_for_name_mapping()
    
    print("\n" + "=" * 60)
    print("调试完成")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
