#!/usr/bin/env python3
"""
调试NAT片段内容
"""

import os
import sys
import xml.etree.ElementTree as ET
from collections import defaultdict

def analyze_xml_file(xml_file):
    """分析XML文件中的NAT结构"""
    print(f"🔍 分析文件: {xml_file}")
    
    if not os.path.exists(xml_file):
        print(f"❌ 文件不存在: {xml_file}")
        return False
    
    try:
        # 解析XML文件
        tree = ET.parse(xml_file)
        root = tree.getroot()
        
        # 查找所有NAT元素
        nat_elements = []
        
        # 使用不同的命名空间查找方式
        for elem in root.iter():
            if elem.tag.endswith('nat') or elem.tag == 'nat':
                nat_elements.append(elem)
        
        print(f"📊 找到 {len(nat_elements)} 个NAT元素")
        
        for i, nat_elem in enumerate(nat_elements):
            print(f"\n📋 NAT元素 {i+1}:")
            print(f"   标签: {nat_elem.tag}")
            print(f"   属性: {nat_elem.attrib}")
            print(f"   子元素数量: {len(nat_elem)}")
            
            # 分析子元素
            child_types = defaultdict(int)
            pool_names = []
            rule_names = []
            
            for child in nat_elem:
                child_tag = child.tag.split('}')[-1] if '}' in child.tag else child.tag
                child_types[child_tag] += 1
                
                if child_tag == 'pool':
                    name_elem = child.find('name')
                    if name_elem is None:
                        for subchild in child:
                            if subchild.tag.endswith('name') or subchild.tag == 'name':
                                name_elem = subchild
                                break
                    
                    if name_elem is not None and name_elem.text:
                        pool_names.append(name_elem.text)
                
                elif child_tag == 'rule':
                    name_elem = child.find('name')
                    if name_elem is None:
                        for subchild in child:
                            if subchild.tag.endswith('name') or subchild.tag == 'name':
                                name_elem = subchild
                                break
                    
                    if name_elem is not None and name_elem.text:
                        rule_names.append(name_elem.text)
            
            print(f"   子元素类型: {dict(child_types)}")
            print(f"   Pool名称: {pool_names[:5]}{'...' if len(pool_names) > 5 else ''}")
            print(f"   Rule名称: {rule_names[:5]}{'...' if len(rule_names) > 5 else ''}")
            
            # 检查重复
            pool_duplicates = len(pool_names) - len(set(pool_names))
            rule_duplicates = len(rule_names) - len(set(rule_names))
            
            if pool_duplicates > 0:
                print(f"   ⚠️ 发现 {pool_duplicates} 个重复pool")
            if rule_duplicates > 0:
                print(f"   ⚠️ 发现 {rule_duplicates} 个重复rule")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def compare_files():
    """对比新旧文件"""
    print("\n📊 对比新旧文件...")
    
    files = [
        ("备份文件", "output/fortigate-z5100s-R11_backup_20250801_194353.xml"),
        ("测试文件", "output/fortigate-z5100s-R11-test-fixed.xml")
    ]
    
    results = {}
    
    for name, file_path in files:
        print(f"\n{'='*60}")
        print(f"分析 {name}")
        print(f"{'='*60}")
        results[name] = analyze_xml_file(file_path)
    
    return results

def check_nat_fragment_structure():
    """检查NAT片段结构"""
    print("\n🔍 检查NAT片段结构...")
    
    # 模拟NAT片段内容
    sample_nat_fragment = '''<nat xmlns="urn:ruijie:ntos:params:xml:ns:yang:nat">
    <pool>
        <name>95.183.134.1</name>
        <type>static-snat44</type>
    </pool>
    <rule>
        <name>test_rule</name>
        <type>static-snat44</type>
    </rule>
</nat>'''
    
    try:
        fragment_root = ET.fromstring(sample_nat_fragment)
        print(f"✅ 样本NAT片段解析成功")
        print(f"   根元素: {fragment_root.tag}")
        print(f"   子元素数量: {len(fragment_root)}")
        
        for child in fragment_root:
            child_tag = child.tag.split('}')[-1] if '}' in child.tag else child.tag
            print(f"   子元素: {child_tag}")
            
            name_elem = child.find('name')
            if name_elem is not None:
                print(f"     名称: {name_elem.text}")
        
        return True
        
    except Exception as e:
        print(f"❌ NAT片段结构检查失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 开始调试NAT片段内容")
    print("=" * 60)
    
    # 检查NAT片段结构
    fragment_ok = check_nat_fragment_structure()
    
    # 对比文件
    file_results = compare_files()
    
    print(f"\n📊 调试总结:")
    print(f"   NAT片段结构: {'✅ 正常' if fragment_ok else '❌ 异常'}")
    
    for name, result in file_results.items():
        status = "✅ 成功" if result else "❌ 失败"
        print(f"   {name}: {status}")
    
    # 分析可能的问题
    print(f"\n🔍 可能的问题:")
    print(f"   1. XML命名空间不匹配")
    print(f"   2. fragment_root为空或结构不正确")
    print(f"   3. existing_nat查找失败")
    print(f"   4. 安全合并方法的逻辑问题")
    
    return all(file_results.values()) and fragment_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
