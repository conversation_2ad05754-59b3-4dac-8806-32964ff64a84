#!/usr/bin/env python3
"""
调试XML模板加载过程
"""

import os
import sys
sys.path.append('.')

from engine.infrastructure.templates.template_manager import TemplateManager
from engine.infrastructure.config.config_manager import ConfigManager
from lxml import etree

def debug_template_loading():
    """调试XML模板加载过程"""
    print("🔍 调试XML模板加载过程")
    print("=" * 60)
    
    try:
        # 创建配置管理器和模板管理器
        config_manager = ConfigManager()
        template_manager = TemplateManager(config_manager)
        
        # 加载模板
        model = "z5100s"
        version = "R11"
        
        print(f"📥 加载模板: {model}/{version}")
        template_root = template_manager.get_template(model, version)
        
        if template_root is None:
            print("❌ 模板加载失败")
            return False
        
        print("✅ 模板加载成功")
        print(f"   根元素: {template_root.tag}")
        print(f"   总元素数: {len(list(template_root.iter()))}")
        
        # 查找threat-intelligence元素
        threat_intel_elem = None
        for elem in template_root.iter():
            if elem.tag.endswith('threat-intelligence'):
                threat_intel_elem = elem
                break
        
        if threat_intel_elem is None:
            print("❌ 加载后的模板中未找到threat-intelligence元素")
            return False
        
        print("✅ 找到threat-intelligence元素")
        
        # 分析结构
        structure = analyze_element_structure(threat_intel_elem, "")
        print("\n📊 加载后的threat-intelligence结构:")
        for line in structure:
            print(f"   {line}")
        
        # 生成XML字符串
        xml_string = etree.tostring(
            threat_intel_elem,
            encoding='unicode',
            pretty_print=True
        )
        
        print(f"\n📄 加载后的XML:")
        print("   " + "\n   ".join(xml_string.split('\n')))
        
        return True
        
    except Exception as e:
        print(f"❌ 模板加载调试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def analyze_element_structure(element, prefix):
    """递归分析元素结构"""
    structure = []
    
    # 当前元素
    tag = element.tag.split('}')[-1] if '}' in element.tag else element.tag
    text = element.text.strip() if element.text else ""
    
    if text:
        structure.append(f"{prefix}{tag}: {text}")
    else:
        structure.append(f"{prefix}{tag}")
    
    # 子元素
    for child in element:
        child_structure = analyze_element_structure(child, prefix + "  ")
        structure.extend(child_structure)
    
    return structure

def debug_template_manager():
    """调试模板管理器的实现"""
    print("\n🔍 调试模板管理器实现")
    print("=" * 60)
    
    try:
        # 直接读取模板文件
        template_file = "engine/data/input/configData/z5100s/R11/Config/running.xml"
        
        if not os.path.exists(template_file):
            print(f"❌ 模板文件不存在: {template_file}")
            return False
        
        print(f"📁 直接读取模板文件: {template_file}")
        
        # 解析文件
        tree = etree.parse(template_file)
        root = tree.getroot()
        
        print("✅ 直接解析成功")
        print(f"   根元素: {root.tag}")
        print(f"   总元素数: {len(list(root.iter()))}")
        
        # 查找threat-intelligence元素
        threat_intel_elem = None
        for elem in root.iter():
            if elem.tag.endswith('threat-intelligence'):
                threat_intel_elem = elem
                break
        
        if threat_intel_elem is None:
            print("❌ 直接解析的文件中未找到threat-intelligence元素")
            return False
        
        print("✅ 找到threat-intelligence元素")
        
        # 分析结构
        structure = analyze_element_structure(threat_intel_elem, "")
        print("\n📊 直接解析的threat-intelligence结构:")
        for line in structure:
            print(f"   {line}")
        
        return True
        
    except Exception as e:
        print(f"❌ 直接解析调试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 开始调试XML模板加载过程")
    
    # 调试直接文件解析
    direct_ok = debug_template_manager()
    
    # 调试模板管理器加载
    manager_ok = debug_template_loading()
    
    print(f"\n{'='*60}")
    print("📊 调试总结:")
    print(f"{'='*60}")
    
    if direct_ok:
        print("✅ 直接文件解析: threat-intelligence结构完整")
    else:
        print("❌ 直接文件解析: threat-intelligence结构有问题")
    
    if manager_ok:
        print("✅ 模板管理器加载: threat-intelligence结构完整")
    else:
        print("❌ 模板管理器加载: threat-intelligence结构有问题")
    
    if direct_ok and not manager_ok:
        print("\n🎯 问题定位: 模板管理器在加载过程中丢失了结构")
    elif direct_ok and manager_ok:
        print("\n🎯 问题定位: 模板加载正常，问题在后续处理阶段")
    else:
        print("\n🎯 问题定位: 需要进一步调查")

if __name__ == "__main__":
    main()
