#!/usr/bin/env python3
"""
调试XML模板结构
"""

import os
from lxml import etree

def debug_template_structure():
    """调试XML模板结构"""
    print("🔍 调试XML模板结构")
    print("=" * 60)
    
    template_file = "engine/data/input/configData/z5100s/R11/Config/running.xml"
    
    if not os.path.exists(template_file):
        print(f"❌ 模板文件不存在: {template_file}")
        return
    
    try:
        # 解析模板文件
        tree = etree.parse(template_file)
        root = tree.getroot()
        
        print(f"✅ 模板文件解析成功")
        print(f"   根元素: {root.tag}")
        print(f"   总元素数: {len(list(root.iter()))}")
        
        # 查找threat-intelligence元素
        threat_intel_elements = []
        for elem in root.iter():
            if elem.tag.endswith('threat-intelligence'):
                threat_intel_elements.append(elem)
        
        print(f"\n🔍 threat-intelligence元素分析:")
        print(f"   找到 {len(threat_intel_elements)} 个threat-intelligence元素")
        
        for i, ti_elem in enumerate(threat_intel_elements):
            print(f"\n   threat-intelligence元素 {i+1}:")
            print(f"      标签: {ti_elem.tag}")
            print(f"      命名空间: {ti_elem.get('xmlns')}")
            
            # 检查management子元素
            management = ti_elem.find('management')
            if management is not None:
                print(f"      ✅ 有management子元素")
                
                # 检查management的子元素
                for child in management:
                    child_tag = child.tag.split('}')[-1] if '}' in child.tag else child.tag
                    print(f"         - {child_tag}: {child.text}")
                
                # 检查security-zone子元素
                security_zone = management.find('security-zone')
                if security_zone is not None:
                    print(f"      ✅ 有security-zone子元素")
                    
                    # 检查security-zone的子元素
                    for child in security_zone:
                        child_tag = child.tag.split('}')[-1] if '}' in child.tag else child.tag
                        print(f"         - {child_tag}: {child.text}")
                    
                    # 检查auto子元素
                    auto = security_zone.find('auto')
                    if auto is not None:
                        print(f"      ✅ 有auto子元素: {auto.text}")
                    else:
                        print(f"      ❌ 缺少auto子元素")
                else:
                    print(f"      ❌ 缺少security-zone子元素")
            else:
                print(f"      ❌ 缺少management子元素")
        
        # 生成完整的threat-intelligence XML结构
        print(f"\n📄 完整的threat-intelligence XML结构:")
        for ti_elem in threat_intel_elements:
            xml_string = etree.tostring(
                ti_elem,
                encoding='unicode',
                pretty_print=True
            )
            print(xml_string)
        
        return True
        
    except Exception as e:
        print(f"❌ 模板结构调试失败: {str(e)}")
        return False

def debug_generated_structure():
    """调试生成的XML结构"""
    print("\n🔍 调试生成的XML结构")
    print("=" * 60)
    
    generated_file = "output/fortigate-z5100s-R11-fixed.xml"
    
    if not os.path.exists(generated_file):
        print(f"❌ 生成文件不存在: {generated_file}")
        return
    
    try:
        # 解析生成文件
        tree = etree.parse(generated_file)
        root = tree.getroot()
        
        print(f"✅ 生成文件解析成功")
        print(f"   根元素: {root.tag}")
        print(f"   总元素数: {len(list(root.iter()))}")
        
        # 查找threat-intelligence元素
        threat_intel_elements = []
        for elem in root.iter():
            if elem.tag.endswith('threat-intelligence'):
                threat_intel_elements.append(elem)
        
        print(f"\n🔍 threat-intelligence元素分析:")
        print(f"   找到 {len(threat_intel_elements)} 个threat-intelligence元素")
        
        for i, ti_elem in enumerate(threat_intel_elements):
            print(f"\n   threat-intelligence元素 {i+1}:")
            print(f"      标签: {ti_elem.tag}")
            print(f"      命名空间: {ti_elem.get('xmlns')}")
            
            # 生成完整的threat-intelligence XML结构
            xml_string = etree.tostring(
                ti_elem,
                encoding='unicode',
                pretty_print=True
            )
            print(f"      XML结构:")
            print("      " + "\n      ".join(xml_string.split('\n')))
        
        return True
        
    except Exception as e:
        print(f"❌ 生成结构调试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 开始XML结构调试")
    
    # 调试模板结构
    template_ok = debug_template_structure()
    
    # 调试生成结构
    generated_ok = debug_generated_structure()
    
    print(f"\n{'='*60}")
    print("📊 调试总结:")
    print(f"{'='*60}")
    
    if template_ok:
        print("✅ 模板结构调试成功")
    else:
        print("❌ 模板结构调试失败")
    
    if generated_ok:
        print("✅ 生成结构调试成功")
    else:
        print("❌ 生成结构调试失败")
    
    if template_ok and generated_ok:
        print("\n🎯 可以对比模板和生成的结构差异")
    else:
        print("\n⚠️ 需要修复调试问题")

if __name__ == "__main__":
    main()
