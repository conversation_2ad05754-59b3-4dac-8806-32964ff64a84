#!/usr/bin/env python3
"""
调试threat-intelligence处理过程
"""

import os
import sys
sys.path.append('.')

from lxml import etree

def analyze_threat_intelligence_in_file(file_path, file_description):
    """分析文件中的threat-intelligence结构"""
    print(f"\n🔍 分析{file_description}: {file_path}")
    print("-" * 60)
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在")
        return None
    
    try:
        tree = etree.parse(file_path)
        root = tree.getroot()
        
        # 查找threat-intelligence元素
        threat_intel_elem = None
        for elem in root.iter():
            if elem.tag.endswith('threat-intelligence'):
                threat_intel_elem = elem
                break
        
        if threat_intel_elem is None:
            print("❌ 未找到threat-intelligence元素")
            return None
        
        print("✅ 找到threat-intelligence元素")
        
        # 分析结构
        structure = analyze_element_structure(threat_intel_elem, "")
        print("📊 结构分析:")
        for line in structure:
            print(f"   {line}")
        
        # 检查特定元素
        management_elem = None
        for child in threat_intel_elem:
            child_tag = child.tag.split('}')[-1] if '}' in child.tag else child.tag
            if child_tag == 'management':
                management_elem = child
                break
        
        if management_elem is not None:
            print("✅ 有management元素")
            
            security_zone_elem = None
            for child in management_elem:
                child_tag = child.tag.split('}')[-1] if '}' in child.tag else child.tag
                if child_tag == 'security-zone':
                    security_zone_elem = child
                    break
            
            if security_zone_elem is not None:
                print("✅ 有security-zone元素")
                
                auto_elem = None
                for child in security_zone_elem:
                    child_tag = child.tag.split('}')[-1] if '}' in child.tag else child.tag
                    if child_tag == 'auto':
                        auto_elem = child
                        break
                
                if auto_elem is not None:
                    print(f"✅ 有auto元素: {auto_elem.text}")
                else:
                    print("❌ 缺少auto元素")
            else:
                print("❌ 缺少security-zone元素")
        else:
            print("❌ 缺少management元素")
        
        return {
            "structure": structure,
            "has_management": management_elem is not None,
            "has_security_zone": security_zone_elem is not None if management_elem else False,
            "has_auto": auto_elem is not None if security_zone_elem else False
        }
        
    except Exception as e:
        print(f"❌ 分析失败: {str(e)}")
        return None

def analyze_element_structure(element, prefix):
    """递归分析元素结构"""
    structure = []
    
    # 当前元素
    tag = element.tag.split('}')[-1] if '}' in element.tag else element.tag
    text = element.text.strip() if element.text else ""
    
    if text:
        structure.append(f"{prefix}{tag}: {text}")
    else:
        structure.append(f"{prefix}{tag}")
    
    # 子元素
    for child in element:
        child_structure = analyze_element_structure(child, prefix + "  ")
        structure.extend(child_structure)
    
    return structure

def check_xml_processing_stages():
    """检查XML处理各个阶段的threat-intelligence结构"""
    print("🚀 检查XML处理各个阶段的threat-intelligence结构")
    print("=" * 80)
    
    # 检查的文件列表
    files_to_check = [
        ("engine/data/input/configData/z5100s/R11/Config/running.xml", "原始模板文件"),
        ("output/fortigate-z5100s-R11-repaired.xml", "最终生成文件"),
        ("output/fortigate-z5100s-R11-fixed.xml", "之前的修复文件"),
        ("output/fortigate-z5100s-R11-rollback.xml", "回滚文件")
    ]
    
    results = {}
    
    for file_path, description in files_to_check:
        result = analyze_threat_intelligence_in_file(file_path, description)
        results[description] = result
    
    # 对比结果
    print(f"\n{'='*80}")
    print("📊 对比分析结果:")
    print(f"{'='*80}")
    
    for description, result in results.items():
        if result:
            print(f"\n{description}:")
            print(f"   结构元素数: {len(result['structure'])}")
            print(f"   有management: {'✅' if result['has_management'] else '❌'}")
            print(f"   有security-zone: {'✅' if result['has_security_zone'] else '❌'}")
            print(f"   有auto: {'✅' if result['has_auto'] else '❌'}")
        else:
            print(f"\n{description}: ❌ 分析失败")
    
    # 找出问题
    template_result = results.get("原始模板文件")
    generated_result = results.get("最终生成文件")
    
    if template_result and generated_result:
        print(f"\n🎯 问题分析:")
        
        if template_result['has_security_zone'] and not generated_result['has_security_zone']:
            print("❌ security-zone元素在处理过程中丢失")
        
        if template_result['has_auto'] and not generated_result['has_auto']:
            print("❌ auto元素在处理过程中丢失")
        
        template_count = len(template_result['structure'])
        generated_count = len(generated_result['structure'])
        
        if template_count > generated_count:
            missing_count = template_count - generated_count
            print(f"❌ 总共丢失了 {missing_count} 个结构元素")
        
        print(f"\n💡 修复建议:")
        print("1. 检查XML模板集成过程中是否有逻辑删除了子元素")
        print("2. 验证XML深拷贝过程是否完整")
        print("3. 检查是否有其他处理阶段修改了threat-intelligence")
        print("4. 确认元素跳过逻辑没有被错误应用")

def check_logs_for_clues():
    """检查日志文件中的线索"""
    print(f"\n🔍 检查日志文件中的线索")
    print("-" * 60)
    
    log_files = [
        "output/logs/debug.log",
        "output/logs/info.log",
        "output/logs/warning.log"
    ]
    
    keywords = [
        "threat-intelligence",
        "management",
        "security-zone",
        "auto",
        "skip",
        "remove",
        "delete",
        "missing"
    ]
    
    for log_file in log_files:
        if os.path.exists(log_file):
            print(f"\n📄 检查 {log_file}:")
            
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                relevant_lines = []
                for i, line in enumerate(lines):
                    for keyword in keywords:
                        if keyword.lower() in line.lower():
                            relevant_lines.append(f"   行{i+1}: {line.strip()}")
                            break
                
                if relevant_lines:
                    print(f"   找到 {len(relevant_lines)} 条相关日志:")
                    for line in relevant_lines[:10]:  # 只显示前10条
                        print(line)
                    if len(relevant_lines) > 10:
                        print(f"   ... 还有 {len(relevant_lines) - 10} 条")
                else:
                    print("   未找到相关日志")
                    
            except Exception as e:
                print(f"   ❌ 读取失败: {str(e)}")
        else:
            print(f"\n📄 {log_file}: 文件不存在")

def main():
    """主函数"""
    print("🚀 开始调试threat-intelligence处理过程")
    
    # 检查各个阶段的文件
    check_xml_processing_stages()
    
    # 检查日志文件
    check_logs_for_clues()
    
    print(f"\n{'='*80}")
    print("📊 调试总结:")
    print(f"{'='*80}")
    print("1. 对比了原始模板和生成文件的threat-intelligence结构")
    print("2. 检查了日志文件中的相关信息")
    print("3. 提供了问题分析和修复建议")
    print("\n🎯 下一步: 根据分析结果修复XML处理逻辑")

if __name__ == "__main__":
    main()
