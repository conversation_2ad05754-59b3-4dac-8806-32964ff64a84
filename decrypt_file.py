#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简化版配置文件解密工具
"""

import os
import sys
import ctypes
import tarfile
import shutil
import tempfile
import subprocess

def simple_log(message, level="info"):
    """简单的日志函数"""
    print(f"[{level.upper()}] {message}")

class SimpleCompressionAPI:
    def __init__(self, lib_path=None):
        """初始化压缩API接口
        
        Args:
            lib_path: libconf_compress.so库文件的路径
        """
        if lib_path is None:
            # 尝试在几个常见位置查找库文件
            possible_paths = [
                "./libconf_compress.so",
                "./engine/lib/crypto/libconf_compress.so",
                "./engine/lib/x86/libconf_compress.so",
                "/media/sf_code/config-converter/engine/lib/crypto/libconf_compress.so",
                "/media/sf_code/config-converter/engine/lib/x86/libconf_compress.so"
            ]
            
            # 检查环境变量
            crypto_libs_path = os.environ.get('CRYPTO_LIBS_PATH')
            if crypto_libs_path:
                possible_paths.insert(0, os.path.join(crypto_libs_path, 'libconf_compress.so'))
            
            # 尝试所有可能的路径
            for path in possible_paths:
                if os.path.exists(path):
                    lib_path = path
                    simple_log(f"找到库文件: {lib_path}")
                    break
        
        if not lib_path or not os.path.exists(lib_path):
            raise RuntimeError("找不到库文件 libconf_compress.so，请指定正确的路径")
        
        self.lib_path = lib_path
        self.lib_dir = os.path.dirname(lib_path)
        
        # 设置库路径环境变量
        os.environ['LD_LIBRARY_PATH'] = f"{self.lib_dir}:{os.environ.get('LD_LIBRARY_PATH', '')}"
        
        # 加载库
        try:
            if hasattr(ctypes, 'RTLD_GLOBAL'):
                self.CONF_COMPRESS_API = ctypes.CDLL(lib_path, mode=ctypes.RTLD_GLOBAL)
            else:
                self.CONF_COMPRESS_API = ctypes.CDLL(lib_path)
            simple_log("成功加载库文件")
        except Exception as e:
            raise RuntimeError(f"加载库文件失败: {str(e)}")
    
    def cm_file_decrypt(self, infile, outfile, ver_file, enflag=0):
        """解密文件
        
        Args:
            infile: 输入文件路径
            outfile: 输出文件路径
            ver_file: 版本信息输入文件路径
            enflag: 加密标志，0表示解密
            
        Returns:
            返回解密操作的状态码，0表示成功
        """
        # 检查输入文件和版本信息文件是否存在
        if not os.path.exists(infile):
            simple_log(f"输入文件不存在: {infile}", "error")
            return -1
            
        if not os.path.exists(ver_file):
            simple_log(f"版本文件不存在: {ver_file}", "error")
            return -1
        
        try:
            # 配置函数原型
            func = self.CONF_COMPRESS_API.cm_file_encrypt
            func.argtypes = [ctypes.c_char_p, ctypes.c_char_p, ctypes.c_char_p, ctypes.c_int]
            func.restype = ctypes.c_int
            
            # 调用函数
            result = func(infile.encode("utf-8"), 
                        outfile.encode("utf-8"), 
                        ver_file.encode("utf-8"), 
                        enflag)
            
            return result
        except Exception as e:
            simple_log(f"调用解密API失败: {str(e)}", "error")
            return -1

def process_tar_gz_file(tar_gz_path):
    """处理.tar.gz文件，解压并找到所需的文件
    
    Args:
        tar_gz_path: .tar.gz文件的路径
        
    Returns:
        tuple: 包含临时目录、版本文件和加密文件路径的元组
    """
    if not os.path.exists(tar_gz_path):
        simple_log(f"文件不存在: {tar_gz_path}", "error")
        return None, None, None
        
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    
    try:
        # 解压文件
        simple_log(f"解压文件: {tar_gz_path}")
        with tarfile.open(tar_gz_path, 'r') as tar:
            tar.extractall(path=temp_dir)
        
        # 寻找版本文件和加密文件
        ver_file = None
        encrypted_file = None
        
        for file in os.listdir(temp_dir):
            if file.endswith('.ver'):
                ver_file = os.path.join(temp_dir, file)
            elif file.endswith('.en'):
                encrypted_file = os.path.join(temp_dir, file)
        
        if not ver_file:
            simple_log("找不到版本文件(.ver)", "error")
            return None, None, None
            
        if not encrypted_file:
            simple_log("找不到加密文件(.en)", "error")
            return None, None, None
            
        simple_log(f"找到版本文件: {ver_file}")
        simple_log(f"找到加密文件: {encrypted_file}")
        
        return temp_dir, ver_file, encrypted_file
        
    except Exception as e:
        simple_log(f"处理文件失败: {str(e)}", "error")
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
        return None, None, None

def decrypt_file(input_file, output_dir, lib_path=None):
    """解密文件主函数
    
    Args:
        input_file: 输入文件路径
        output_dir: 输出目录
        lib_path: 可选，库文件路径
    """
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 处理输入文件
    simple_log(f"开始处理输入文件: {input_file}")
    temp_dir, ver_file, encrypted_file = process_tar_gz_file(input_file)
    
    if not all([temp_dir, ver_file, encrypted_file]):
        simple_log("处理输入文件失败", "error")
        return False
    
    try:
        # 初始化解密API
        simple_log("初始化解密API")
        api = SimpleCompressionAPI(lib_path)
        
        # 解密文件
        decrypted_file = os.path.join(output_dir, "Config.tar.gz")
        simple_log(f"开始解密文件到: {decrypted_file}")
        
        result = api.cm_file_decrypt(encrypted_file, decrypted_file, ver_file)
        
        if result != 0:
            simple_log(f"解密失败，错误代码: {result}", "error")
            return False
        
        simple_log(f"解密成功，文件保存在: {decrypted_file}")
        
        # 提示用户如何进一步解压Config.tar.gz
        simple_log("您可以使用以下命令解压Config.tar.gz获取配置文件:")
        simple_log(f"  tar -xzf {decrypted_file} -C {output_dir}")
        
        return True
    
    except Exception as e:
        simple_log(f"解密过程中发生错误: {str(e)}", "error")
        return False
    
    finally:
        # 清理临时目录
        if temp_dir and os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
            simple_log("已清理临时文件")

def main():
    # 检查命令行参数
    if len(sys.argv) < 3:
        print("用法: python decrypt_file.py 输入文件.tar.gz 输出目录 [库文件路径]")
        print("示例: python decrypt_file.py export-startup.tar.gz ./output")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_dir = sys.argv[2]
    lib_path = sys.argv[3] if len(sys.argv) > 3 else None
    
    if decrypt_file(input_file, output_dir, lib_path):
        print("文件解密成功!")
    else:
        print("文件解密失败。")
        sys.exit(1)

if __name__ == "__main__":
    main()