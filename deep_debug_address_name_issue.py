#!/usr/bin/env python3
"""
深度调试地址名称引用不一致问题
通过Hook关键函数来追踪地址名称的转换过程
"""

import sys
import os
sys.path.append('.')
sys.path.append('./engine')

def hook_name_functions():
    """Hook名称处理函数来追踪转换过程"""
    print("=== Hook名称处理函数 ===")
    
    try:
        from engine.utils import name_validator
        
        # 保存原始函数
        original_clean_ntos_name = name_validator.clean_ntos_name
        original_sanitize_address_name = name_validator.sanitize_address_name
        
        # 创建Hook函数
        def hooked_clean_ntos_name(name, max_length=64):
            result = original_clean_ntos_name(name, max_length)
            if "***********" in str(name) or "10_10_11_15" in str(name):
                print(f"🔍 clean_ntos_name: {name} → {result}")
            return result
        
        def hooked_sanitize_address_name(name):
            result = original_sanitize_address_name(name)
            if "***********" in str(name) or "10_10_11_15" in str(name):
                print(f"🔍 sanitize_address_name: {name} → {result}")
            return result
        
        # 替换函数
        name_validator.clean_ntos_name = hooked_clean_ntos_name
        name_validator.sanitize_address_name = hooked_sanitize_address_name
        
        print("✅ 成功Hook名称处理函数")
        return True
        
    except Exception as e:
        print(f"❌ Hook失败: {e}")
        return False

def hook_policy_processor():
    """Hook策略处理器"""
    print("=== Hook策略处理器 ===")
    
    try:
        from engine.processors import policy_processor
        
        # 保存原始函数
        original_process_policy = policy_processor.PolicyProcessor.process_policy
        
        def hooked_process_policy(self, policy):
            # 检查策略中的地址
            srcaddr = policy.get("srcaddr", [])
            dstaddr = policy.get("dstaddr", [])
            
            if isinstance(srcaddr, str):
                srcaddr = [srcaddr]
            if isinstance(dstaddr, str):
                dstaddr = [dstaddr]
            
            for addr in srcaddr + dstaddr:
                if "***********" in str(addr):
                    print(f"🔍 策略处理器输入地址: {addr}")
            
            result = original_process_policy(self, policy)
            
            # 检查处理后的结果
            if "source-network" in result:
                for src_net in result["source-network"]:
                    if "10_10_11_15" in str(src_net.get("name", "")):
                        print(f"🔍 策略处理器输出源地址: {src_net}")
            
            if "dest-network" in result:
                for dest_net in result["dest-network"]:
                    if "10_10_11_15" in str(dest_net.get("name", "")):
                        print(f"🔍 策略处理器输出目标地址: {dest_net}")
            
            return result
        
        # 替换函数
        policy_processor.PolicyProcessor.process_policy = hooked_process_policy
        
        print("✅ 成功Hook策略处理器")
        return True
        
    except Exception as e:
        print(f"❌ Hook策略处理器失败: {e}")
        return False

def hook_xml_generation():
    """Hook XML生成过程"""
    print("=== Hook XML生成过程 ===")
    
    try:
        # Hook XML元素创建
        from lxml import etree
        
        original_SubElement = etree.SubElement
        
        def hooked_SubElement(parent, tag, **kwargs):
            element = original_SubElement(parent, tag, **kwargs)
            
            # 检查是否是name元素且包含我们关注的地址
            if tag.endswith('name') and hasattr(element, 'text'):
                if element.text and ("10_10_11_15" in element.text or "***********" in element.text):
                    print(f"🔍 XML生成name元素: {element.text}")
                    import traceback
                    print("调用栈:")
                    for line in traceback.format_stack()[-5:-1]:
                        print(f"  {line.strip()}")
            
            return element
        
        # 替换函数
        etree.SubElement = hooked_SubElement
        
        print("✅ 成功Hook XML生成")
        return True
        
    except Exception as e:
        print(f"❌ Hook XML生成失败: {e}")
        return False

def run_conversion_with_hooks():
    """运行带Hook的转换过程"""
    print("=== 运行带Hook的转换过程 ===")
    
    try:
        # 设置Hook
        hook_name_functions()
        hook_policy_processor()
        hook_xml_generation()
        
        # 运行转换器
        from main import main as converter_main
        
        # 备份原始的sys.argv
        original_argv = sys.argv.copy()
        
        # 设置转换参数
        sys.argv = [
            'main.py',
            '--cli', 'FortiGate-100F_7-6_3510_202505161613.conf',
            '--output', 'debug_output.xml',
            '--verbose'
        ]
        
        print("开始转换...")
        try:
            result = converter_main()
            print(f"转换结果: {result}")
        except SystemExit as e:
            print(f"转换完成，退出码: {e.code}")
        
        # 恢复原始的sys.argv
        sys.argv = original_argv
        
        return True
        
    except Exception as e:
        print(f"转换失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_debug_output():
    """分析调试输出"""
    print("=== 分析调试输出 ===")
    
    if os.path.exists('debug_output.xml'):
        print("✅ 生成了调试XML文件")
        
        # 检查XML文件中的地址引用
        try:
            with open('debug_output.xml', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 搜索相关的地址引用
            import re
            
            # 查找地址对象定义
            address_defs = re.findall(r'<address-set>.*?<name>(10[._]10[._]11[._]15\d)</name>.*?</address-set>', content, re.DOTALL)
            print(f"找到地址对象定义: {address_defs}")
            
            # 查找策略中的地址引用
            policy_refs = re.findall(r'<(?:source|dest)-network>.*?<name>(10[._]10[._]11[._]15\d)</name>.*?</(?:source|dest)-network>', content, re.DOTALL)
            print(f"找到策略地址引用: {policy_refs}")
            
            return True
            
        except Exception as e:
            print(f"分析XML文件失败: {e}")
            return False
    else:
        print("❌ 未生成调试XML文件")
        return False

def test_name_transformation_directly():
    """直接测试名称转换"""
    print("=== 直接测试名称转换 ===")
    
    try:
        from engine.utils.name_validator import clean_ntos_name, sanitize_address_name
        
        test_addresses = [
            "***********0",
            "***********1",
            "***********2",
            "***********3",
            "***********4",
            "***********5"
        ]
        
        print("直接测试名称转换函数:")
        for addr in test_addresses:
            clean_result = clean_ntos_name(addr, 64)
            sanitize_result = sanitize_address_name(addr)
            
            print(f"  {addr}:")
            print(f"    clean_ntos_name: {clean_result}")
            print(f"    sanitize_address_name: {sanitize_result}")
            
            if clean_result != addr:
                print(f"    ❌ clean_ntos_name修改了地址")
            if sanitize_result != addr:
                print(f"    ❌ sanitize_address_name修改了地址")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False

def main():
    """主函数"""
    print("深度调试地址名称引用不一致问题")
    print("=" * 60)
    
    # 1. 直接测试名称转换
    test_name_transformation_directly()
    
    # 2. 运行带Hook的转换过程
    run_conversion_with_hooks()
    
    # 3. 分析调试输出
    analyze_debug_output()
    
    print("\n" + "=" * 60)
    print("深度调试完成")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
