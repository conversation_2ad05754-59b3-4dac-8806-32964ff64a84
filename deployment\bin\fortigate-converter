#!/bin/bash
# FortiGate到NTOS转换器服务启动脚本
# 版本: 2.0.0
# 创建时间: 2025-08-01T22:47:59.801756

set -e

# 服务配置
SERVICE_NAME="fortigate-to-ntos-converter"
SERVICE_ROOT="/opt/fortigate-converter"
CONFIG_DIR="/etc/fortigate-converter"
LOG_DIR="/var/log/fortigate-converter"
DATA_DIR="/var/lib/fortigate-converter"
TEMP_DIR="/tmp/fortigate-converter"

# Python环境
PYTHON_BIN="$SERVICE_ROOT/venv/bin/python"
MAIN_SCRIPT="$SERVICE_ROOT/engine/main.py"

# 日志配置
LOG_FILE="$LOG_DIR/converter.log"
ERROR_LOG="$LOG_DIR/error.log"

# 函数定义
log_info() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] INFO: $1" | tee -a "$LOG_FILE"
}

log_error() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1" | tee -a "$ERROR_LOG"
}

# 检查环境
check_environment() {
    log_info "检查运行环境..."
    
    # 检查Python环境
    if [ ! -f "$PYTHON_BIN" ]; then
        log_error "Python虚拟环境不存在: $PYTHON_BIN"
        exit 1
    fi
    
    # 检查主程序
    if [ ! -f "$MAIN_SCRIPT" ]; then
        log_error "主程序不存在: $MAIN_SCRIPT"
        exit 1
    fi
    
    # 检查目录权限
    for dir in "$LOG_DIR" "$DATA_DIR" "$TEMP_DIR"; do
        if [ ! -w "$dir" ]; then
            log_error "目录不可写: $dir"
            exit 1
        fi
    done
    
    log_info "环境检查通过"
}

# 启动服务
start_service() {
    log_info "启动FortiGate转换器服务..."
    
    check_environment
    
    # 切换到服务目录
    cd "$SERVICE_ROOT"
    
    # 启动服务
    exec "$PYTHON_BIN" "$MAIN_SCRIPT" \
        --mode server \
        --config "$CONFIG_DIR/converter.yaml" \
        --log-dir "$LOG_DIR" \
        --data-dir "$DATA_DIR" \
        --temp-dir "$TEMP_DIR" \
        "$@"
}

# 转换单个文件
convert_file() {
    local config_file="$1"
    local mapping_file="$2"
    local output_file="$3"
    local model="$4"
    local version="$5"
    
    log_info "开始转换: $config_file"
    
    check_environment
    
    cd "$SERVICE_ROOT"
    
    "$PYTHON_BIN" "$MAIN_SCRIPT" \
        --mode convert \
        --vendor fortigate \
        --cli "$config_file" \
        --mapping "$mapping_file" \
        --model "$model" \
        --version "$version" \
        --output "$output_file" \
        --lang zh-CN \
        --log-dir "$LOG_DIR"
}

# 主函数
main() {
    case "$1" in
        start)
            start_service "${@:2}"
            ;;
        convert)
            if [ $# -lt 6 ]; then
                echo "用法: $0 convert <config_file> <mapping_file> <output_file> <model> <version>"
                exit 1
            fi
            convert_file "$2" "$3" "$4" "$5" "$6"
            ;;
        *)
            echo "用法: $0 {start|convert} [参数...]"
            echo "  start                    - 启动服务模式"
            echo "  convert <args>           - 转换单个文件"
            exit 1
            ;;
    esac
}

main "$@"
