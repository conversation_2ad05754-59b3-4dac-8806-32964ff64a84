conversion:
  default_language: zh-CN
  default_model: z5100s
  default_version: R11
  enable_encryption: false
  enable_yang_validation: true
  max_concurrent_jobs: 10
logging:
  backup_count: 5
  file: /var/log/fortigate-converter/converter.log
  format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
  level: INFO
  max_size: 10MB
  rotation: daily
monitoring:
  enable_alerts: true
  enable_metrics: true
  health_check_interval: 30
  metrics_port: 9090
service:
  bind_host: 0.0.0.0
  bind_port: 8080
  max_file_size: 100MB
  name: fortigate-to-ntos-converter
  timeout: 300
  version: 2.0.0
  workers: 4
storage:
  cleanup_interval: 3600
  data_dir: /var/lib/fortigate-converter
  max_temp_age: 86400
  temp_dir: /tmp/fortigate-converter
