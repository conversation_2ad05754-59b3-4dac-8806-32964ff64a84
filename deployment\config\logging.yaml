disable_existing_loggers: false
formatters:
  detailed:
    format: '%(asctime)s - %(name)s - %(levelname)s - %(module)s - %(funcName)s -
      %(message)s'
  standard:
    format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
handlers:
  console:
    class: logging.StreamHandler
    formatter: standard
    level: INFO
    stream: ext://sys.stdout
  error_file:
    backupCount: 5
    class: logging.handlers.RotatingFileHandler
    filename: /var/log/fortigate-converter/error.log
    formatter: detailed
    level: ERROR
    maxBytes: 10485760
  file:
    backupCount: 5
    class: logging.handlers.RotatingFileHandler
    filename: /var/log/fortigate-converter/converter.log
    formatter: detailed
    level: DEBUG
    maxBytes: 10485760
loggers:
  fortigate_converter:
    handlers:
    - console
    - file
    - error_file
    level: DEBUG
    propagate: false
root:
  handlers:
  - console
  - file
  level: INFO
version: 1
