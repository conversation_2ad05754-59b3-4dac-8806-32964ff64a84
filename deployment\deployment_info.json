{"package_version": "2.0.0", "created_at": "2025-08-01T22:47:59.801756", "python_requirements": ["lxml>=4.6.0", "pyyaml>=5.4.0", "jsonschema>=3.2.0", "click>=7.0"], "deployment_paths": {"service_root": "/opt/fortigate-converter", "config_dir": "/etc/fortigate-converter", "log_dir": "/var/log/fortigate-converter", "data_dir": "/var/lib/fortigate-converter", "temp_dir": "/tmp/fortigate-converter"}, "files_included": ["bin/fortigate-converter", "config/converter.yaml", "config/logging.yaml", "systemd/fortigate-converter.service", "monitoring/prometheus.yml", "monitoring/converter_rules.yml", "docs/DEPLOYMENT_GUIDE.md"]}