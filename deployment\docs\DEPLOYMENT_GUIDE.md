# FortiGate到NTOS转换器生产环境部署指南

## 版本信息
- 服务版本: 2.0.0
- Python版本: 3.8+
- 部署日期: 2025-08-01T22:47:59.801756

## 系统要求
- 操作系统: Ubuntu 20.04+ / CentOS 8+ / RHEL 8+
- Python: 3.8+
- 内存: 最小2GB，推荐4GB+
- 磁盘: 最小10GB可用空间
- 网络: 支持HTTP/HTTPS访问

## 安装步骤

### 1. 创建服务用户
```bash
sudo useradd -r -s /bin/false converter
sudo mkdir -p /opt/fortigate-converter
sudo mkdir -p /etc/fortigate-converter
sudo mkdir -p /var/log/fortigate-converter
sudo mkdir -p /var/lib/fortigate-converter
sudo mkdir -p /tmp/fortigate-converter
sudo chown -R converter:converter /opt/fortigate-converter
sudo chown -R converter:converter /var/log/fortigate-converter
sudo chown -R converter:converter /var/lib/fortigate-converter
sudo chown -R converter:converter /tmp/fortigate-converter
```

### 2. 部署应用代码
```bash
# 复制应用代码
sudo cp -r engine/ /opt/fortigate-converter/
sudo cp -r mappings/ /opt/fortigate-converter/
sudo cp deployment/bin/* /opt/fortigate-converter/bin/
sudo cp deployment/config/* /etc/fortigate-converter/

# 设置权限
sudo chown -R converter:converter /opt/fortigate-converter
sudo chmod +x /opt/fortigate-converter/bin/*
```

### 3. 安装Python依赖
```bash
# 创建虚拟环境
sudo -u converter python3 -m venv /opt/fortigate-converter/venv

# 安装依赖
sudo -u converter /opt/fortigate-converter/venv/bin/pip install \
    lxml>=4.6.0 pyyaml>=5.4.0 jsonschema>=3.2.0 click>=7.0
```

### 4. 配置systemd服务
```bash
sudo cp deployment/systemd/fortigate-converter.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable fortigate-converter
```

### 5. 启动服务
```bash
sudo systemctl start fortigate-converter
sudo systemctl status fortigate-converter
```

## 配置说明

### 主配置文件
配置文件位置: `/etc/fortigate-converter/converter.yaml`

主要配置项:
- `service.bind_port`: 服务监听端口（默认8080）
- `service.workers`: 工作进程数（默认4）
- `conversion.default_model`: 默认设备型号
- `logging.level`: 日志级别

### 日志配置
- 应用日志: `/var/log/fortigate-converter/converter.log`
- 错误日志: `/var/log/fortigate-converter/error.log`
- 系统日志: `journalctl -u fortigate-converter`

## 使用方法

### 命令行转换
```bash
/opt/fortigate-converter/bin/fortigate-converter convert \
    /path/to/fortigate.conf \
    /path/to/mapping.json \
    /path/to/output.xml \
    z5100s \
    R11
```

### API调用
```bash
curl -X POST http://localhost:8080/api/convert \
    -F "config=@fortigate.conf" \
    -F "mapping=@mapping.json" \
    -F "model=z5100s" \
    -F "version=R11"
```

## 监控和维护

### 健康检查
```bash
curl http://localhost:8080/health
```

### 查看指标
```bash
curl http://localhost:9090/metrics
```

### 日志轮转
日志文件会自动轮转，保留最近5个文件。

### 备份建议
- 定期备份配置文件
- 定期备份接口映射文件
- 监控磁盘空间使用

## 故障排除

### 常见问题
1. **服务启动失败**
   - 检查Python环境: `/opt/fortigate-converter/venv/bin/python --version`
   - 检查权限: `ls -la /opt/fortigate-converter`
   - 查看日志: `journalctl -u fortigate-converter -f`

2. **转换失败**
   - 检查接口映射文件格式
   - 查看错误日志: `tail -f /var/log/fortigate-converter/error.log`
   - 验证FortiGate配置文件格式

3. **性能问题**
   - 调整worker数量
   - 增加内存限制
   - 监控CPU和内存使用

### 联系支持
- 邮箱: <EMAIL>
- 文档: https://docs.your-company.com/fortigate-converter
- 问题反馈: https://github.com/your-org/fortigate-converter/issues
