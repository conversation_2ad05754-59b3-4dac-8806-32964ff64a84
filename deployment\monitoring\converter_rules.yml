
groups:
  - name: fortigate_converter_alerts
    rules:
      - alert: ConverterServiceDown
        expr: up{job="fortigate-converter"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "FortiGate转换器服务停止"
          description: "FortiGate转换器服务已停止超过1分钟"
      
      - alert: HighConversionFailureRate
        expr: rate(converter_failures_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "转换失败率过高"
          description: "转换失败率超过10%，持续2分钟"
      
      - alert: HighMemoryUsage
        expr: process_resident_memory_bytes / 1024 / 1024 > 1000
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "内存使用率过高"
          description: "内存使用超过1GB，持续5分钟"
