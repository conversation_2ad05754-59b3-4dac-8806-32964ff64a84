[Unit]
Description=FortiGate到NTOS转换器服务
Documentation=https://github.com/your-org/fortigate-converter
After=network.target
Wants=network.target

[Service]
Type=simple
User=converter
Group=converter
WorkingDirectory=/opt/fortigate-converter
ExecStart=/opt/fortigate-converter/bin/fortigate-converter start
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=fortigate-converter

# 环境变量
Environment=PYTHONPATH=/opt/fortigate-converter
Environment=CONVERTER_CONFIG=/etc/fortigate-converter/converter.yaml
Environment=CONVERTER_LOG_DIR=/var/log/fortigate-converter

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/log/fortigate-converter /var/lib/fortigate-converter /tmp/fortigate-converter

[Install]
WantedBy=multi-user.target
