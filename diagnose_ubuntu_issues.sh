#!/bin/bash
# Ubuntu环境问题诊断脚本

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Ubuntu环境Docker构建问题诊断脚本 ===${NC}"
echo ""

# 1. 系统环境检查
echo -e "${YELLOW}1. 系统环境检查${NC}"
echo "系统信息: $(uname -a)"
echo "当前用户: $(whoami)"
echo "当前工作目录: $(pwd)"
echo "PATH: $PATH"
echo ""

# 2. Git仓库状态检查
echo -e "${YELLOW}2. Git仓库状态检查${NC}"
if [ -d ".git" ]; then
    echo -e "${GREEN}✓ .git目录存在${NC}"
    
    if git rev-parse --git-dir >/dev/null 2>&1; then
        echo -e "${GREEN}✓ 这是一个有效的Git仓库${NC}"
        
        # 测试Git命令
        echo "Git版本: $(git --version)"
        echo "Git配置检查:"
        git config --list | head -5
        
        echo ""
        echo "Git状态测试:"
        git_hash_result=$(git rev-parse --short=8 HEAD 2>&1)
        git_exit_code=$?
        echo "git rev-parse --short=8 HEAD 结果: '$git_hash_result'"
        echo "退出代码: $git_exit_code"
        
        if [ $git_exit_code -eq 0 ]; then
            echo -e "${GREEN}✓ Git哈希获取成功${NC}"
        else
            echo -e "${RED}✗ Git哈希获取失败${NC}"
            echo "可能的原因:"
            echo "  - 没有任何提交记录"
            echo "  - Git仓库损坏"
            echo "  - 权限问题"
        fi
        
        # 检查提交历史
        echo ""
        echo "提交历史检查:"
        commit_count=$(git rev-list --count HEAD 2>/dev/null || echo "0")
        echo "提交数量: $commit_count"
        
        if [ "$commit_count" -gt 0 ]; then
            echo "最近的提交:"
            git log --oneline -3 2>/dev/null || echo "无法获取提交历史"
        else
            echo -e "${RED}✗ 没有任何提交记录${NC}"
            echo "解决方案: 执行 git add . && git commit -m 'Initial commit'"
        fi
        
    else
        echo -e "${RED}✗ 不是有效的Git仓库${NC}"
    fi
else
    echo -e "${RED}✗ .git目录不存在${NC}"
    echo "解决方案: 执行 git init 初始化Git仓库"
fi
echo ""

# 3. Docker环境检查
echo -e "${YELLOW}3. Docker环境检查${NC}"
if command -v docker >/dev/null 2>&1; then
    echo -e "${GREEN}✓ Docker已安装${NC}"
    echo "Docker版本: $(docker --version)"
    
    # 测试Docker权限
    if docker ps >/dev/null 2>&1; then
        echo -e "${GREEN}✓ Docker权限正常${NC}"
    else
        echo -e "${RED}✗ Docker权限问题${NC}"
        echo "解决方案: sudo usermod -aG docker \$USER && newgrp docker"
    fi
    
    # 检查Docker守护进程
    if docker info >/dev/null 2>&1; then
        echo -e "${GREEN}✓ Docker守护进程运行正常${NC}"
    else
        echo -e "${RED}✗ Docker守护进程问题${NC}"
        echo "解决方案: sudo systemctl start docker"
    fi
else
    echo -e "${RED}✗ Docker未安装${NC}"
    echo "解决方案: 安装Docker"
fi
echo ""

# 4. 文件权限检查
echo -e "${YELLOW}4. 文件权限检查${NC}"
for file in "push-aliyun.sh" "build.sh"; do
    if [ -f "$file" ]; then
        perms=$(ls -la "$file" | awk '{print $1}')
        echo "$file: $perms"
        if [ -x "$file" ]; then
            echo -e "${GREEN}✓ $file 可执行${NC}"
        else
            echo -e "${RED}✗ $file 不可执行${NC}"
            echo "解决方案: chmod +x $file"
        fi
    else
        echo -e "${RED}✗ $file 不存在${NC}"
    fi
done
echo ""

# 5. 配置文件检查
echo -e "${YELLOW}5. 配置文件检查${NC}"
config_files=(
    "engine/infrastructure/config/settings.py"
    "config/production_config.py"
    "deployment/deployment_info.json"
    "Dockerfile.base"
    "Dockerfile.app"
)

for file in "${config_files[@]}"; do
    if [ -f "$file" ]; then
        echo -e "${GREEN}✓ $file 存在${NC}"
        
        # 特殊检查
        case "$file" in
            "engine/infrastructure/config/settings.py")
                engine_version=$(grep "ENGINE_VERSION" "$file" 2>/dev/null | head -1)
                echo "  ENGINE_VERSION: $engine_version"
                ;;
            "config/production_config.py")
                prod_version=$(grep "VERSION.*=" "$file" 2>/dev/null | head -1)
                echo "  VERSION: $prod_version"
                ;;
            "deployment/deployment_info.json")
                if command -v python3 >/dev/null 2>&1; then
                    json_version=$(python3 -c "import json; print(json.load(open('$file'))['package_version'])" 2>/dev/null || echo "解析失败")
                    echo "  package_version: $json_version"
                else
                    echo "  python3不可用，无法解析JSON"
                fi
                ;;
        esac
    else
        echo -e "${RED}✗ $file 不存在${NC}"
    fi
done
echo ""

# 6. 环境变量检查
echo -e "${YELLOW}6. 环境变量检查${NC}"
env_vars=("TAG" "REGISTRY" "BASE_IMAGE_NAME" "APP_IMAGE_NAME")
for var in "${env_vars[@]}"; do
    value="${!var}"
    if [ -n "$value" ]; then
        echo "$var: $value"
    else
        echo "$var: 未设置"
    fi
done
echo ""

# 7. 测试标签生成
echo -e "${YELLOW}7. 测试标签生成${NC}"
if [ -f "push-aliyun.sh" ]; then
    echo "测试push-aliyun.sh中的generate_tag函数..."
    
    # 提取并测试generate_tag函数
    source_result=$(bash -c '
        source push-aliyun.sh 2>/dev/null
        if declare -f generate_tag >/dev/null 2>&1; then
            echo "函数加载成功"
            base_tag=$(generate_tag "base" 2>&1)
            app_tag=$(generate_tag "app" 2>&1)
            echo "基础镜像标签: $base_tag"
            echo "应用镜像标签: $app_tag"
        else
            echo "函数加载失败"
        fi
    ' 2>&1)
    
    echo "$source_result"
else
    echo "push-aliyun.sh不存在，无法测试"
fi
echo ""

echo -e "${BLUE}=== 诊断完成 ===${NC}"
echo ""
echo -e "${YELLOW}建议的修复步骤:${NC}"
echo "1. 如果Git哈希获取失败，确保有提交记录: git add . && git commit -m 'Initial commit'"
echo "2. 如果Docker权限问题，添加用户到docker组: sudo usermod -aG docker \$USER"
echo "3. 如果脚本不可执行，添加执行权限: chmod +x *.sh"
echo "4. 重新运行 ./push-aliyun.sh 并观察调试输出"
