debug: false
loglevel: info
host: 0.0.0.0
port: 9005
nginx:
  host: localhost
  port: 9527
  path: /api
readtimeout: 60
writetimeout: 60
maxsize: 10240
pprof: true

casbin:
  prefix: ""
  path: /app/rbac_model.conf

db:
  adapter: mysql
  conn: "root:abc.123@tcp(**************:3306)/test?parseTime=True&loc=Local"
  max_idle_conns: 10
  max_open_conns: 100
  conn_max_lifetime: 3600
  prefix: ""
  encrypt: false
  debug: false
  log_level: info
  slow_threshold: 200

redis:
  host: ***********
  port: 6379
  password: "password"
  database: 5
  dial_timeout: 10
  read_timeout: 6
  write_timeout: 6
  pool_size: 10
  min_idle_conns: 5
  max_retries: 3
  retry_timeout: 500

cache:
  driver: redis

filestorage:
  temp: /app/data/temp/
  upload: /app/data/uploads/

configtrans:
  upload: /app/data/uploads/
  tempdir: /app/data/temp/
  pythonpath: python
  enginepath: /app/engine
  mappingbasedir: /app/data/mappings/
  
  vendors:
    fortigate:
      mappingfile: interface_mapping.json
      modes:
        verify: "--mode verify --cli {file}"
        extract: "--mode extract --cli {file} --output-json {json}"
        convert: "--mode convert --cli {file} --mapping {mapping} --model {model} --version {version} --output {output} --encrypt-output {encrypt}"
