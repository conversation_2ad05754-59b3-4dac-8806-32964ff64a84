debug: ${DEBUG}
loglevel: ${LOGLEVEL}
host: ${HOST}
port: ${PORT}
nginx:
  host: ${NGINX_HOST}
  port: ${NGINX_PORT}
  path: ${NGINX_PATH}
readtimeout: ${READ_TIMEOUT}
writetimeout: ${WRITE_TIMEOUT}
maxsize: ${MAX_SIZE}
pprof: ${PPROF_ENABLED}

casbin:
  prefix: "${CASBIN_PREFIX}"
  path: ${CASBIN_PATH}

db:
  adapter: ${DB_ADAPTER}
  conn: "${DB_CONN}"
  max_idle_conns: ${DB_MAX_IDLE_CONNS}
  max_open_conns: ${DB_MAX_OPEN_CONNS}
  conn_max_lifetime: ${DB_CONN_MAX_LIFETIME}
  prefix: "${DB_PREFIX}"
  encrypt: ${DB_ENABLED}
  debug: ${DB_DEBUG}
  log_level: ${DB_LOG_LEVEL}
  slow_threshold: ${DB_SLOW_THRESHOLD}

redis:
  host: ${REDIS_HOST}
  port: ${REDIS_PORT}
  password: "${REDIS_PASSWORD}"
  database: ${REDIS_DATABASE}
  dial_timeout: ${REDIS_DIAL_TIMEOUT}
  read_timeout: ${REDIS_READ_TIMEOUT}
  write_timeout: ${REDIS_WRITE_TIMEOUT}
  pool_size: ${REDIS_POOL_SIZE}
  min_idle_conns: ${REDIS_MIN_IDLE_CONNS}
  max_retries: ${REDIS_MAX_RETRIES}
  retry_timeout: ${REDIS_RETRY_TIMEOUT}

cache:
  driver: ${CACHE_DRIVER}

filestorage:
  temp: ${FILESTORAGE_TEMP}
  upload: ${FILESTORAGE_UPLOAD}

configtrans:
  upload: ${CONFIGTRANS_UPLOAD}
  tempdir: ${CONFIGTRANS_TEMPDIR}
  pythonpath: ${CONFIGTRANS_PYTHONPATH}
  enginepath: ${CONFIGTRANS_ENGINEPATH}
  mappingbasedir: ${CONFIGTRANS_MAPPINGBASEDIR}
  
  vendors:
    fortigate:
      mappingfile: ${CONFIGTRANS_FORTIGATE_MAPPINGFILE}
      modes:
        verify: "${CONFIGTRANS_FORTIGATE_VERIFY}"
        extract: "${CONFIGTRANS_FORTIGATE_EXTRACT}"
        convert: "${CONFIGTRANS_FORTIGATE_CONVERT}"
