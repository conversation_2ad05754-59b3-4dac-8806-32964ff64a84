version: '3'

services:
  configtrans-service:
    build: .
    image: configtrans-service:latest
    container_name: configtrans-service
    ports:
      - "9005:9005"
    environment:
      - DEBUG=false
      - LOGLEVEL=info
      - HOST=0.0.0.0
      - PORT=9005
      # 数据库配置
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USER=root
      - DB_PASSWORD=secloud@mysql
      - DB_NAME=configtrans
      # Redis配置
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=secloud@Redis
      - REDIS_DATABASE=5
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    depends_on:
      - mysql
      - redis
    networks:
      - app-network

  mysql:
    image: mysql:8.0
    container_name: mysql
    restart: always
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=secloud@mysql
      - MYSQL_DATABASE=configtrans
    volumes:
      - mysql-data:/var/lib/mysql
    networks:
      - app-network

  redis:
    image: redis:7.0
    container_name: redis
    restart: always
    ports:
      - "6379:6379"
    command: redis-server --requirepass secloud@Redis
    volumes:
      - redis-data:/data
    networks:
      - app-network

volumes:
  mysql-data:
  redis-data:

networks:
  app-network:
    driver: bridge