# 配置转换工具国际化（i18n）实现

本文档描述了配置转换工具的国际化（i18n）实现，包括对 `convert.py` 文件的国际化改造工作。

## 国际化架构

我们采用基于键值对的国际化方案，主要组件包括：

1. **翻译文件**：
   - `engine/locales/zh-CN.json`：中文翻译文件
   - `engine/locales/en-US.json`：英文翻译文件

> **重要更新**: 所有语言代码现已统一为连字符格式(如 `zh-CN`, `en-US`)，不再使用下划线格式(如 `zh_CN`, `en_US`)。

2. **国际化工具类**：
   - `engine/utils/i18n.py`：提供翻译功能的工具类

3. **国际化使用方式**：
   - 使用 `_()` 函数进行翻译，如 `_("error.file_not_exists", file=path)`
   - 在日志中使用 `i18n:` 前缀，如 `log("i18n:info.start_conversion", "info")`

## 翻译键命名规范

翻译键按照以下规范命名：

- `error.xxx`：错误消息
- `warning.xxx`：警告消息
- `info.xxx`：信息消息
- `status.xxx`：状态消息
- `report.xxx`：报告相关消息
- `manual_config.xxx`：手动配置相关消息
- `compatibility.xxx`：兼容性相关消息
- `ui.xxx`：用户界面相关消息

## 国际化实现步骤

1. **创建翻译文件**：
   - 创建中文翻译文件 `zh-CN.json`
   - 创建英文翻译文件 `en-US.json`

2. **修改代码**：
   - 将硬编码的中文字符串替换为国际化键
   - 使用 `_()` 函数进行翻译
   - 在日志中使用 `i18n:` 前缀

3. **测试国际化功能**：
   - 创建测试脚本 `test_i18n_convert_full.py`
   - 测试不同语言环境下的翻译功能
   - 验证回退机制

## 国际化使用示例

### 在代码中使用

```python
# 直接翻译
message = _("error.file_not_exists", file=path)

# 在日志中使用
log("i18n:info.start_conversion", "info")
user_log("i18n:info.conversion_succeeded")

# 带参数的翻译
error_msg = _("error.parse_config_failed", error=str(e))
```

### 切换语言

```python
# 初始化为中文
init_i18n("zh-CN")

# 切换到英文
init_i18n("en-US")
```

## 测试结果

国际化功能测试成功，包括：

1. 中文翻译测试
2. 英文翻译测试
3. 回退翻译测试（当使用不支持的语言时，回退到英文）
4. convert 模块功能测试

## 未来扩展

1. 支持更多语言：
   - 只需添加对应的翻译文件，如 `ja-JP.json` 用于日语
   - 无需修改代码逻辑

2. 动态语言切换：
   - 当前实现支持在运行时切换语言
   - 可以根据用户设置或环境变量自动选择语言

3. 翻译文件管理：
   - 可以考虑使用工具自动提取需要翻译的字符串
   - 建立翻译管理平台，方便翻译人员工作 