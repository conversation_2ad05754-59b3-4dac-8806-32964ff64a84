"""
FortiGate twice-nat44转换数据模型

本模块定义了FortiGate复合NAT转换为NTOS twice-nat44配置所需的数据结构。
基于已完成的XML模板集成重构成果，提供标准化的数据模型。
"""

from dataclasses import dataclass, field
from typing import Optional, Dict, Any, List
from enum import Enum
import re
from engine.utils.logger import log
from engine.utils.i18n import _
# 避免循环导入，错误处理装饰器将在运行时动态导入


class TwiceNat44AddressType(Enum):
    """twice-nat44地址类型枚举"""
    INTERFACE = "interface"
    IP = "ip"
    POOL = "pool"


class TwiceNat44ConfigError(Exception):
    """twice-nat44配置错误"""
    pass


class TwiceNat44ValidationError(Exception):
    """twice-nat44验证错误"""
    pass


@dataclass
class TwiceNat44MatchConditions:
    """
    twice-nat44匹配条件数据结构
    
    定义了twice-nat44规则的匹配条件，包括源网络、目标网络、服务和时间范围。
    """
    dest_network: str
    source_network: Optional[str] = None
    service: str = "any"
    time_range: str = "any"
    
    def __post_init__(self):
        """初始化后验证"""
        if not self.dest_network:
            raise TwiceNat44ConfigError("dest_network cannot be empty")
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式，用于XML生成
        
        Returns:
            Dict[str, Any]: 匹配条件字典
        """
        result = {
            "dest-network": {"name": self.dest_network},
            "service": {"name": self.service},
            "time-range": {"value": self.time_range}
        }
        
        if self.source_network:
            result["source-network"] = {"name": self.source_network}
        
        return result
    
    def validate_network_objects(self, available_objects: List[str]) -> bool:
        """
        验证网络对象是否存在
        
        Args:
            available_objects: 可用的网络对象列表
            
        Returns:
            bool: 验证是否通过
        """
        if self.dest_network not in available_objects:
            log(_("twice_nat44.dest_network_not_found", network=self.dest_network), "warning")
            return False
        
        if self.source_network and self.source_network not in available_objects:
            log(_("twice_nat44.source_network_not_found", network=self.source_network), "warning")
            return False
        
        return True


@dataclass
class TwiceNat44SnatConfig:
    """
    twice-nat44 SNAT配置数据结构
    
    定义了twice-nat44规则的源地址转换配置。
    """
    address_type: TwiceNat44AddressType
    address_value: Optional[str] = None
    no_pat: bool = False
    try_no_pat: bool = True
    
    def __post_init__(self):
        """初始化后验证"""
        if self.address_type in [TwiceNat44AddressType.IP, TwiceNat44AddressType.POOL]:
            if not self.address_value:
                raise TwiceNat44ConfigError(f"address_value is required for {self.address_type.value}")
        
        if self.address_type == TwiceNat44AddressType.IP:
            if not self._is_valid_ipv4(self.address_value):
                raise TwiceNat44ConfigError(f"Invalid IPv4 address: {self.address_value}")
    
    def _is_valid_ipv4(self, ip: str) -> bool:
        """验证IPv4地址格式"""
        if not ip:
            return False
        pattern = r'^(\d{1,3}\.){3}\d{1,3}$'
        if not re.match(pattern, ip):
            return False
        parts = ip.split('.')
        return all(0 <= int(part) <= 255 for part in parts)
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式，用于XML生成
        
        Returns:
            Dict[str, Any]: SNAT配置字典
        """
        result = {
            "no-pat": self.no_pat,
            "try-no-pat": self.try_no_pat
        }
        
        if self.address_type == TwiceNat44AddressType.INTERFACE:
            result["output-address"] = {}
        elif self.address_type == TwiceNat44AddressType.IP:
            result["ipv4-address"] = self.address_value
        elif self.address_type == TwiceNat44AddressType.POOL:
            result["pool-name"] = self.address_value
        
        return result


@dataclass
class TwiceNat44DnatConfig:
    """
    twice-nat44 DNAT配置数据结构
    
    定义了twice-nat44规则的目标地址转换配置。
    """
    ipv4_address: str
    port: Optional[int] = None
    
    def __post_init__(self):
        """初始化后验证"""
        if not self.ipv4_address:
            raise TwiceNat44ConfigError("ipv4_address cannot be empty")
        
        if not self._is_valid_ipv4(self.ipv4_address):
            raise TwiceNat44ConfigError(f"Invalid IPv4 address: {self.ipv4_address}")
        
        if self.port is not None:
            if not (1 <= self.port <= 65535):
                raise TwiceNat44ConfigError(f"Invalid port number: {self.port}")
    
    def _is_valid_ipv4(self, ip: str) -> bool:
        """验证IPv4地址格式"""
        if not ip:
            return False
        pattern = r'^(\d{1,3}\.){3}\d{1,3}$'
        if not re.match(pattern, ip):
            return False
        parts = ip.split('.')
        return all(0 <= int(part) <= 255 for part in parts)
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式，用于XML生成
        
        Returns:
            Dict[str, Any]: DNAT配置字典
        """
        result = {"ipv4-address": self.ipv4_address}
        if self.port:
            result["port"] = self.port
        return result


@dataclass
class TwiceNat44Rule:
    """
    twice-nat44规则完整数据结构
    
    这是twice-nat44规则的主要数据模型，包含了完整的规则配置信息。
    基于已重构的XML处理框架设计，确保与现有系统的完美兼容。
    """
    name: str
    enabled: bool
    description: str
    match_conditions: TwiceNat44MatchConditions
    snat_config: TwiceNat44SnatConfig
    dnat_config: TwiceNat44DnatConfig
    
    def __post_init__(self):
        """初始化后验证"""
        if not self.name:
            raise TwiceNat44ConfigError("Rule name cannot be empty")
        
        if not self.description:
            self.description = f"twice-nat44 rule {self.name}"
    
    @classmethod
    def from_fortigate_policy(cls, policy: Dict[str, Any], vip_config: Dict[str, Any]) -> 'TwiceNat44Rule':
        """
        从FortiGate策略创建twice-nat44规则
        
        Args:
            policy: FortiGate策略配置
            vip_config: VIP对象配置
            
        Returns:
            TwiceNat44Rule: 创建的规则对象
            
        Raises:
            TwiceNat44ConfigError: 当配置无效时
            KeyError: 当必需的配置项缺失时
        """
        try:
            # 构建规则名称
            policy_name = policy.get("name", "unknown")
            vip_name = vip_config.get("name", "unknown")
            rule_name = f"{policy_name}_{vip_name}_twice_nat"
            
            # 构建匹配条件
            match_conditions = TwiceNat44MatchConditions(
                dest_network=vip_name,
                service=policy.get("service", ["any"])[0] if isinstance(policy.get("service"), list) else policy.get("service", "any"),
                time_range=policy.get("schedule", "any")
            )
            
            # 构建SNAT配置
            snat_config = TwiceNat44SnatConfig(
                address_type=TwiceNat44AddressType.INTERFACE,
                no_pat=policy.get("fixedport", "disable") == "enable",
                try_no_pat=policy.get("fixedport", "disable") != "enable"
            )
            
            # 构建DNAT配置
            mapped_ip = vip_config.get("mappedip")
            if not mapped_ip:
                raise TwiceNat44ConfigError(f"VIP {vip_name} missing mappedip")
            
            mapped_port = None
            if "mappedport" in vip_config:
                try:
                    mapped_port = int(vip_config["mappedport"])
                except (ValueError, TypeError):
                    log(_("twice_nat44.invalid_mapped_port", port=vip_config["mappedport"]), "warning")
            
            dnat_config = TwiceNat44DnatConfig(
                ipv4_address=mapped_ip,
                port=mapped_port
            )
            
            # 创建规则
            rule = cls(
                name=rule_name,
                enabled=policy.get("status", "enable") == "enable",
                description=f"FortiGate复合NAT策略 {policy_name}",
                match_conditions=match_conditions,
                snat_config=snat_config,
                dnat_config=dnat_config
            )
            
            log(_("twice_nat44.rule_created", name=rule_name), "info")
            return rule
            
        except Exception as e:
            log(_("twice_nat44.rule_creation_failed", policy=policy.get("name"), error=str(e)), "error")
            raise TwiceNat44ConfigError(f"Failed to create twice-nat44 rule: {str(e)}")
    
    def to_nat_rule_dict(self) -> Dict[str, Any]:
        """
        转换为NAT规则字典格式，用于XML生成
        
        Returns:
            Dict[str, Any]: NAT规则字典
        """
        return {
            "name": self.name,
            "type": "twice-nat44",
            "rule_en": self.enabled,
            "desc": self.description,
            "twice-nat44": {
                "match": self.match_conditions.to_dict(),
                "snat": self.snat_config.to_dict(),
                "dnat": self.dnat_config.to_dict()
            }
        }
    
    def validate(self) -> bool:
        """
        验证规则配置的有效性
        
        Returns:
            bool: 配置是否有效
        """
        try:
            # 验证基本字段
            if not self.name or not self.description:
                return False
            
            # 验证匹配条件
            if not self.match_conditions.dest_network:
                return False
            
            # 验证SNAT配置
            if self.snat_config.address_type in [TwiceNat44AddressType.IP, TwiceNat44AddressType.POOL]:
                if not self.snat_config.address_value:
                    return False
            
            # 验证DNAT配置
            if not self.dnat_config.ipv4_address:
                return False
            
            return True
            
        except Exception as e:
            log(_("twice_nat44.validation_failed", name=self.name, error=str(e)), "error")
            return False
    
    def get_xml_namespace(self) -> str:
        """
        获取XML命名空间
        
        Returns:
            str: XML命名空间URI
        """
        return "urn:ruijie:ntos:params:xml:ns:yang:nat"
