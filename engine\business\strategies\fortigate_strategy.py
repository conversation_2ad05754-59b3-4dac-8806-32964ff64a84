
import gc
import psutil
import os
from engine.utils.i18n import _

class MemoryOptimizer:
    """内存优化器"""
    
    def __init__(self):
        self.process = psutil.Process(os.getpid())
        self.initial_memory = self.get_memory_usage()
    
    def get_memory_usage(self):
        """获取当前内存使用量（MB）"""
        return self.process.memory_info().rss / 1024 / 1024
    
    def optimize_memory(self, stage_name="unknown"):
        """优化内存使用"""
        try:
            # 强制垃圾回收
            gc.collect()
            
            current_memory = self.get_memory_usage()
            print(_("fortigate_strategy.memory.optimization", stage=stage_name, memory=current_memory))

            # 如果内存使用超过500MB，进行深度清理
            if current_memory > 500:
                print(_("fortigate_strategy.memory.high_usage", memory=current_memory))

                # 多次垃圾回收
                for _ in range(3):
                    gc.collect()

                after_memory = self.get_memory_usage()
                saved_memory = current_memory - after_memory
                print(_("fortigate_strategy.memory.cleanup_complete", saved=saved_memory))

            return current_memory

        except Exception as e:
            print(_("fortigate_strategy.memory.optimization_failed", error=e))
            return 0

def with_memory_optimization(stage_name="unknown"):
    """内存优化装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            optimizer = MemoryOptimizer()
            
            try:
                # 执行前优化
                optimizer.optimize_memory(f"{stage_name}_start")
                
                # 执行函数
                result = func(*args, **kwargs)
                
                # 执行后优化
                optimizer.optimize_memory(f"{stage_name}_end")
                
                return result
                
            except Exception as e:
                # 异常时也进行内存清理
                optimizer.optimize_memory(f"{stage_name}_error")
                raise e
        
        return wrapper
    return decorator

class ChunkedConfigProcessor:
    """分块配置处理器"""
    
    def __init__(self, chunk_size=1000):
        self.chunk_size = chunk_size
        self.optimizer = MemoryOptimizer()
    
    def process_config_in_chunks(self, config_lines):
        """分块处理配置"""
        print(_("fortigate_strategy.chunk.processing_config", total=len(config_lines)))

        chunks = []
        for i in range(0, len(config_lines), self.chunk_size):
            chunk = config_lines[i:i + self.chunk_size]
            chunks.append(chunk)

        print(_("fortigate_strategy.chunk.divided_into_chunks", chunks=len(chunks), size=self.chunk_size))

        processed_results = []

        for i, chunk in enumerate(chunks):
            print(_("fortigate_strategy.chunk.processing_chunk", current=i+1, total=len(chunks)))

            try:
                # 处理当前块
                chunk_result = self.process_single_chunk(chunk, i+1)
                processed_results.append(chunk_result)

                # 每处理5个块进行一次内存优化
                if (i + 1) % 5 == 0:
                    self.optimizer.optimize_memory(f"chunk_{i+1}")

            except Exception as e:
                print(_("fortigate_strategy.chunk.processing_failed", chunk=i+1, error=e))
                # 继续处理下一块
                processed_results.append({"status": "failed", "error": str(e)})

        return processed_results
    
    def process_single_chunk(self, chunk, chunk_number):
        """处理单个块"""
        # 这里实现具体的块处理逻辑
        return {
            "status": "success",
            "chunk_number": chunk_number,
            "lines_processed": len(chunk)
        }

# MEMORY_OPTIMIZATION_APPLIED
# -*- coding: utf-8 -*-
"""
Fortigate转换策略 - 专门处理Fortigate配置转换的策略
特别关注策略(firewall policy)转换的可靠性和XML模板/YANG模型集成
"""

from typing import Dict, Any, List, Optional, Tuple
from engine.utils.logger import log
from engine.utils.i18n import _

# 定义安全翻译函数
def safe_translate(message, **kwargs):
    """安全的翻译函数，确保即使原始_函数不可用也能正常工作"""
    try:
        return _(message, **kwargs)
    except Exception:
        # 如果翻译失败，使用简单的字符串替换
        result = message
        for k, v in kwargs.items():
            result = result.replace(f"{{{k}}}", str(v))
        return result

from engine.processing.pipeline.data_flow import DataContext
from .conversion_strategy import ConversionStrategy


class FortigateConversionStrategy(ConversionStrategy):
    """
    Fortigate转换策略
    专门处理Fortigate配置转换，确保策略转换的可靠性
    """

    def __init__(self, config_manager, template_manager, yang_manager):
        """
        初始化Fortigate转换策略
        
        Args:
            config_manager: 配置管理器
            template_manager: 模板管理器
            yang_manager: YANG管理器
        """
        super().__init__("fortigate", config_manager, template_manager, yang_manager)
        self.vendor = "fortigate"
        
    def validate_input(self, context: DataContext) -> bool:
        """
        验证Fortigate配置输入 - 适配新的11阶段管道数据结构

        Args:
            context: 数据上下文

        Returns:
            bool: 验证是否通过
        """
        # 适配新的11阶段管道数据结构
        config_data = context.get_data("config_data")

        # 如果没有config_data，尝试旧的parsed_config（向后兼容）
        if not config_data:
            config_data = context.get_data("parsed_config")

        if not config_data:
            context.add_error(_("fortigate_strategy.no_parsed_config"))
            return False
        
        # 验证必要的配置段
        required_sections = ["interfaces", "policies", "address_objects", "service_objects"]
        for section in required_sections:
            if section not in config_data:
                context.add_warning(_("fortigate_strategy.missing_section", section=section))
        
        # 验证策略配置的完整性
        policies = config_data.get("policies", [])
        if policies:
            valid_policies = self._validate_policies(policies, context)
            context.set_data("valid_policies_count", valid_policies)
            log(_("fortigate_strategy.policies_validated"), "info", 
                total=len(policies), valid=valid_policies)
        
        return True
    
    def _validate_policies(self, policies: List[Dict], context: DataContext) -> int:
        """
        验证策略配置的完整性
        
        Args:
            policies: 策略列表
            context: 数据上下文
            
        Returns:
            int: 有效策略数量
        """
        valid_count = 0
        
        for i, policy in enumerate(policies):
            policy_id = policy.get("policyid", f"policy_{i}")
            
            # 检查必要字段
            required_fields = ["srcintf", "dstintf", "srcaddr", "dstaddr", "service", "action"]
            missing_fields = []
            
            for field in required_fields:
                if field not in policy or not policy[field]:
                    missing_fields.append(field)
            
            if missing_fields:
                context.add_warning(_("fortigate_strategy.policy_missing_fields", 
                                    policy_id=policy_id, fields=", ".join(missing_fields)))
            else:
                valid_count += 1
            
            # 检查策略类型和NAT配置
            self._validate_policy_nat_config(policy, policy_id, context)
        
        return valid_count
    
    def _validate_policy_nat_config(self, policy: Dict, policy_id: str, context: DataContext):
        """
        验证策略的NAT配置
        
        Args:
            policy: 策略配置
            policy_id: 策略ID
            context: 数据上下文
        """
        # 检查NAT相关配置
        nat_enabled = policy.get("nat", "disable") == "enable"
        ippool_enabled = policy.get("ippool", "disable") == "enable"
        poolname = policy.get("poolname", [])
        
        if nat_enabled:
            if ippool_enabled and not poolname:
                context.add_warning(_("fortigate_strategy.nat_ippool_no_pool", policy_id=policy_id))
        
        # 检查VIP相关配置
        dstaddr_list = policy.get("dstaddr", [])
        for addr in dstaddr_list:
            if addr and addr != "all":
                # 这里可以进一步验证VIP对象的存在性
                pass
        
        # 检查不支持的特性
        unsupported_features = []
        if policy.get("users"):
            unsupported_features.append("users")
        if policy.get("groups"):
            unsupported_features.append("groups")
        if policy.get("fixedport") == "enable":
            unsupported_features.append("fixedport")
        
        if unsupported_features:
            context.add_warning(_("fortigate_strategy.unsupported_features", 
                                policy_id=policy_id, features=", ".join(unsupported_features)))
    
    def prepare_conversion_data(self, context: DataContext) -> bool:
        """
        准备Fortigate转换数据 - 适配新的11阶段管道数据结构

        Args:
            context: 数据上下文

        Returns:
            bool: 准备是否成功
        """
        try:
            # 适配新的11阶段管道数据结构
            config_data = context.get_data("config_data", {})

            # 如果没有config_data，尝试旧的parsed_config（向后兼容）
            if not config_data:
                config_data = context.get_data("parsed_config", {})

            # 获取NTOS版本信息（从命令行参数或上下文）
            ntos_version = context.get_data("ntos_version")
            if not ntos_version:
                # 尝试从其他地方获取版本信息
                ntos_version = context.get_data("version", "R11")  # 默认R11

            # 设置版本信息到上下文
            context.set_data("ntos_version", ntos_version)
            log(safe_translate("fortigate_strategy.ntos_version", version=ntos_version), "info")

            # 准备策略转换数据
            policies = config_data.get("policies", [])
            security_policies = []
            nat_rules = []

            # 详细调试信息
            log(f"DEBUG: config_data keys: {list(config_data.keys())}", "info")
            log(f"DEBUG: policies type: {type(policies)}, count: {len(policies)}", "info")
            if policies:
                log(f"DEBUG: first policy keys: {list(policies[0].keys()) if policies else 'N/A'}", "info")
                log(f"DEBUG: first policy id: {policies[0].get('policyid', 'N/A') if policies else 'N/A'}", "info")

            log(_("fortigate_strategy.policy.data_preparation", count=len(policies)), "info")
            
            # 分类和转换策略 - 传递完整的上下文以访问处理结果
            policies_requiring_nat = 0
            for policy in policies:
                policy_id = policy.get("policyid", "unknown")
                log(safe_translate("fortigate_strategy.policy.classification_start", policy_id=policy_id), "info")
                policy_classification = self._classify_policy(policy, context)
                log(safe_translate("fortigate_strategy.policy.classification_result",
                                 policy_id=policy_id, result=str(policy_classification)), "info")

                # 记录策略分类结果
                if policy_classification.get("needs_nat", False):
                    policies_requiring_nat += 1
                    log(safe_translate("fortigate_strategy.policy.nat_required",
                                     policy_id=policy_id, nat_type=policy_classification.get('nat_type')), "info")

                # 生成安全策略
                if policy_classification["needs_security_policy"]:
                    security_policy = self._generate_security_policy(policy, policy_classification, context)
                    if security_policy:
                        security_policies.append(security_policy)

                # 生成NAT规则（支持多个规则）
                if policy_classification["needs_nat"]:
                    log(safe_translate("fortigate_strategy.nat.generation_start", policy_id=policy_id), "info")
                    generated_nat_rules = self._generate_nat_rule(policy, policy_classification, context)
                    if generated_nat_rules:
                        if isinstance(generated_nat_rules, list):
                            nat_rules.extend(generated_nat_rules)
                            log(safe_translate("fortigate_strategy.nat.rules_generated",
                                             policy_id=policy_id, count=len(generated_nat_rules)), "info")
                        else:
                            nat_rules.append(generated_nat_rules)
                            log(safe_translate("fortigate_strategy.nat.rule_generated", policy_id=policy_id), "info")
                    else:
                        log(safe_translate("fortigate_strategy.nat.generation_failed", policy_id=policy_id), "warning")
            
            # 存储转换结果
            context.set_data("security_policies", security_policies)
            log(safe_translate("fortigate_strategy.nat.rules_stored", count=len(nat_rules)), "info")
            log(safe_translate("fortigate_strategy.policy.processing_summary",
                             total=len(policies), nat_policies=policies_requiring_nat), "info")
            if nat_rules:
                rule_types = [rule.get('type', 'unknown') for rule in nat_rules[:5]]
                log(safe_translate("fortigate_strategy.nat.rule_types", types=str(rule_types)), "info")
            context.set_data("nat_rules", nat_rules)
            context.set_data("conversion_stats", {
                "total_policies": len(policies),
                "security_policies": len(security_policies),
                "nat_rules": len(nat_rules)
            })
            
            log(_("fortigate_strategy.conversion_data_prepared"), "info",
                total=len(policies), security=len(security_policies), nat=len(nat_rules))
            
            return True
            
        except Exception as e:
            error_msg = _("fortigate_strategy.prepare_data_failed", error=str(e))
            context.add_error(error_msg)
            log(error_msg, "error")
            return False
    
    def _classify_policy(self, policy: Dict, context: DataContext) -> Dict[str, Any]:
        """
        分类策略类型 - 适配新的11阶段管道数据结构，支持版本兼容性

        Args:
            policy: 策略配置
            context: 数据上下文（包含所有处理结果）

        Returns: Dict[str, Any]: 分类结果
        """
        classification = {
            "needs_security_policy": True,  # 所有策略都需要安全策略
            "needs_nat": False,
            "nat_type": None,
            "is_dnat": False,
            "is_snat": False,
            "is_compound_nat": False,  # 新增：复合NAT（同时有SNAT和DNAT）
            "version_compatible": True,  # 新增：版本兼容性
            "warnings": []  # 新增：警告信息
        }

        # 获取目标NTOS版本信息
        ntos_version = context.get_data("ntos_version", "R11")  # 默认R11
        policy_id = policy.get("policyid", "unknown")

        # 调试：输出策略的完整内容（仅对策略1）
        if policy_id == "1":
            log(f"DEBUG: Policy {policy_id} - Full policy data: {policy}", "info")

        # 检查版本兼容性
        if ntos_version == "R10P2":
            classification["version_compatible"] = False
            classification["warnings"].append(
                f"策略 {policy_id}: R10P2版本不支持NAT规则转换，仅转换安全策略"
            )
            log(f"WARNING: Policy {policy_id} - R10P2 version does not support NAT rules, skipping NAT processing", "warning")
            return classification

        # 检查SNAT需求
        nat_enabled = policy.get("nat", "disable") == "enable"

        # 处理ippool字段（可能是字符串或列表）
        ippool_value = policy.get("ippool", "disable")
        if isinstance(ippool_value, list):
            ippool_enabled = len(ippool_value) > 0 and ippool_value[0] == "enable"
        else:
            ippool_enabled = ippool_value == "enable"

        # 处理poolname字段（可能是字符串或列表）
        poolname_list = policy.get("poolname", [])
        if isinstance(poolname_list, str):
            poolname_list = [poolname_list]
        elif not isinstance(poolname_list, list):
            poolname_list = []

        # 调试日志
        log(f"DEBUG: Policy {policy_id} - NAT analysis: nat={policy.get('nat')}, ippool={policy.get('ippool')}, poolname={policy.get('poolname')}", "info")
        log(f"DEBUG: Policy {policy_id} - Processed: nat_enabled={nat_enabled}, ippool_enabled={ippool_enabled}, poolname_list={poolname_list}", "info")

        # 检查DNAT需求 (VIP) - 从地址对象处理结果中获取VIP信息
        dstaddr_list = policy.get("dstaddr", [])
        if isinstance(dstaddr_list, str):
            dstaddr_list = [dstaddr_list]

        # 获取地址对象处理结果
        address_result = context.get_data("address_processing_result", {})
        address_objects = address_result.get("address_objects", {}).get("converted", {})

        # 检查是否有VIP类型的地址对象
        vip_names = {name for name, addr_obj in address_objects.items()
                    if addr_obj.get("type") == "vip"}

        has_vip = any(addr in vip_names for addr in dstaddr_list if addr and addr.upper() != "ALL")


        # 根据补充信息优化NAT分类逻辑
        if has_vip and nat_enabled:
            # 情况3：既有VIP又有nat enable - 复合NAT（源目的同时转换）
            classification["needs_nat"] = True
            classification["nat_type"] = "compound_nat"
            classification["is_dnat"] = True
            classification["is_snat"] = True
            classification["is_compound_nat"] = True
        elif has_vip:
            # 情况1：只有VIP - 纯DNAT
            classification["needs_nat"] = True
            classification["nat_type"] = "dnat"
            classification["is_dnat"] = True
        elif nat_enabled:
            # 情况2：只有nat enable - 纯SNAT
            classification["needs_nat"] = True
            # 检查是否使用IP池：需要同时满足ippool enable和有poolname
            if ippool_enabled and poolname_list:
                classification["nat_type"] = "dynamic_snat"
            else:
                classification["nat_type"] = "static_snat"
            classification["is_snat"] = True

        return classification
    
    def _generate_security_policy(self, policy: Dict, classification: Dict, context: DataContext) -> Optional[Dict]:
        """
        生成安全策略配置 - 基于现有policy_processor逻辑，适配新的11阶段管道

        Args:
            policy: 原始策略
            classification: 策略分类
            context: 数据上下文（包含所有处理结果）

        Returns:
            Optional[Dict]: 安全策略配置
        """
        # 🚨 CRITICAL: 新架构严格验证标记 - 如果看到这条日志说明代码被正确加载
        policy_id = policy.get("policyid", "unknown")
        log(_("fortigate_strategy.policy.generation_start", policy_id=policy_id), "info")

        try:
            # 从数据上下文获取接口映射信息 - 增强获取逻辑
            interface_mapping = {}

            # 方法1：从interface_processing_result获取
            interface_result = context.get_data("interface_processing_result")
            if interface_result:
                final_mapping = interface_result.get("final_mapping", {})
                if final_mapping:
                    interface_mapping = final_mapping
                    log(_("fortigate_strategy.policy.interface_mapping_from_result", count=len(interface_mapping)), "info")
                    log(_("fortigate_strategy.policy.interface_mapping_content", mapping=interface_mapping), "info")
                else:
                    log(_("fortigate_strategy.policy.interface_mapping_empty"), "warning")

            # 方法2：如果方法1失败，尝试从其他数据源获取
            if not interface_mapping:
                # 尝试从interface_mappings获取
                interface_mappings = context.get_data("interface_mappings", {})
                if interface_mappings:
                    interface_mapping = interface_mappings
                    log(_("fortigate_strategy.policy.interface_mapping_from_mappings", count=len(interface_mapping)), "info")
                    log(_("fortigate_strategy.policy.interface_mapping_content", mapping=interface_mapping), "info")

                # 尝试从原始映射文件获取
                if not interface_mapping:
                    mapping_file = context.get_data("mapping_file")
                    if mapping_file:
                        try:
                            import json
                            with open(mapping_file, 'r', encoding='utf-8') as f:
                                file_mapping = json.load(f)

                            # 处理嵌套格式
                            if "interface_mappings" in file_mapping:
                                interface_mapping = file_mapping["interface_mappings"]
                            else:
                                # 扁平格式，过滤非接口映射的键
                                interface_mapping = {k: v for k, v in file_mapping.items()
                                                   if k not in ["default_mapping"] and isinstance(v, str)}

                            if interface_mapping:
                                log(_("fortigate_strategy.policy.interface_mapping_from_file", count=len(interface_mapping)), "info")
                                log(_("fortigate_strategy.policy.interface_mapping_content", mapping=interface_mapping), "info")
                        except Exception as e:
                            error_msg = _("fortigate_strategy.policy.mapping_file_read_failed", error=str(e))
                            log(error_msg, "warning")

            # 如果仍然没有获取到接口映射，记录警告
            if not interface_mapping:
                log(_("fortigate_strategy.policy.no_interface_mapping"), "warning")

            # 🔒 严格验证接口映射（与旧架构保持一致）
            policy_id = policy.get("policyid", "unknown")
            policy_name = policy.get("name", f"policy_{policy_id}")
            log(_("fortigate_strategy.policy.validation_start", policy_id=policy_id, name=policy_name), "info")

            try:
                validation_result = self._validate_interface_mapping_strict(policy, interface_mapping, context)
                if not validation_result:
                    log(_("fortigate_strategy.policy.validation_failed", policy_id=policy_id, name=policy_name), "warning")
                    return None
                else:
                    log(_("fortigate_strategy.policy.validation_passed", policy_id=policy_id, name=policy_name), "info")
            except Exception as e:
                error_msg = _("fortigate_strategy.policy.validation_exception", policy_id=policy_id, name=policy_name, error=str(e))
                log(error_msg, "error")
                return None

            # 委托给现有的policy_processor，但传递接口映射
            from engine.processors.policy_processor import PolicyProcessor

            # 创建临时处理器实例
            processor = PolicyProcessor()

            # 使用现有的生成逻辑，但传递接口映射和上下文
            security_policy = processor._generate_security_policy_with_mapping(policy, classification, interface_mapping, context)

            # 添加安全功能映射（支持版本兼容性检测）
            self._add_security_features(policy, security_policy, context)

            # 添加时间范围映射（确保完整性）
            self._add_time_range_mapping(policy, security_policy)

            return security_policy

        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            print(_("fortigate_strategy.policy.generation_error", error=str(e)))
            print(_("fortigate_strategy.policy.error_stack", stack=error_details))
            log(_("fortigate_strategy.security_policy_generation_failed"), "error",
                policy_id=policy.get("policyid", "unknown"), error=str(e))
            return None

    def _resolve_zone_to_interfaces(self, zone_name: str, interface_mapping: Dict[str, str], context: DataContext) -> List[str]:
        """
        将区域名称解析为接口列表（结合YANG模型验证）

        Args:
            zone_name: 区域名称
            interface_mapping: 接口映射表
            context: 数据上下文

        Returns: List[str]: 该区域包含的NTOS接口名称列表
        """
        try:
            if not context:
                log(_("fortigate_strategy.zone.no_context", zone=zone_name), "warning")
                return []

            # 获取解析的区域配置
            config_data = context.get_data("config_data")
            if not config_data:
                # 尝试从parsed_config获取（向后兼容）
                config_data = context.get_data("parsed_config")

            if not config_data or "zones" not in config_data:
                log(_("fortigate_strategy.zone.no_config", zone=zone_name), "warning")
                return []

            zones = config_data["zones"]

            # 查找指定区域
            target_zone = None
            for zone in zones:
                if zone.get("name") == zone_name:
                    target_zone = zone
                    break

            if not target_zone:
                log(_("fortigate_strategy.zone.not_found", zone=zone_name), "debug")
                return []

            # 获取区域中的接口列表
            zone_interfaces = target_zone.get("interfaces", [])
            if not zone_interfaces:
                log(_("fortigate_strategy.zone.no_interfaces", zone=zone_name), "debug")
                return []

            # 将FortiGate接口名称映射为NTOS接口名称
            mapped_interfaces = []
            for intf in zone_interfaces:
                clean_intf = intf.strip('"').strip("'")
                if clean_intf in interface_mapping:
                    ntos_intf = interface_mapping[clean_intf]

                    # 验证接口名称是否符合YANG模型的ifname类型
                    if self._validate_interface_name_with_yang(ntos_intf, context):
                        mapped_interfaces.append(ntos_intf)
                        log(_("fortigate_strategy.zone.interface_mapping", **{"from": clean_intf, "to": ntos_intf}), "debug")
                    else:
                        log(_("fortigate_strategy.zone.interface_yang_invalid", interface=ntos_intf), "warning")
                        mapped_interfaces.append(ntos_intf)  # 仍然添加，但记录警告
                else:
                    log(_("fortigate_strategy.zone.interface_no_mapping", zone=zone_name, interface=clean_intf), "warning")

            log(_("fortigate_strategy.zone.resolved_interfaces", zone=zone_name, interfaces=mapped_interfaces), "info")
            return mapped_interfaces

        except Exception as e:
            log(_("fortigate_strategy.zone.resolution_exception", zone=zone_name, error=str(e)), "error")
            return []

    def _validate_zone_with_yang(self, zone_name: str, zone_interfaces: List[str], context: DataContext) -> bool:
        """
        使用YANG模型验证区域配置

        Args:
            zone_name: 区域名称
            zone_interfaces: 区域包含的接口列表
            context: 数据上下文

        Returns:
            bool: 验证是否通过
        """
        try:
            # 基本的区域名称验证（根据NTOS标准）
            # 标准区域名称通常为trust、untrust等
            standard_zones = ["trust", "untrust", "dmz", "mgmt", "internal", "external"]

            if zone_name.lower() in [z.lower() for z in standard_zones]:
                log(_("fortigate_strategy.zone.standard_zone", zone=zone_name), "debug")
                return True

            # 自定义区域名称验证（长度、字符等）
            if len(zone_name) > 32:  # NTOS通常限制区域名称长度
                log(_("fortigate_strategy.zone.name_too_long", zone=zone_name), "warning")
                return False

            # 检查是否包含非法字符
            import re
            if not re.match(r'^[a-zA-Z0-9_-]+$', zone_name):
                log(_("fortigate_strategy.zone.invalid_characters", zone=zone_name), "warning")
                return False

            log(_("fortigate_strategy.zone.yang_validation_passed", zone=zone_name), "debug")
            return True

        except Exception as e:
            log(_("fortigate_strategy.zone.validation_exception", zone=zone_name, error=str(e)), "warning")
            return True  # 验证异常时，允许通过

    def _validate_interface_name_with_yang(self, interface_name: str, context: DataContext) -> bool:
        """
        使用YANG模型验证接口名称

        Args:
            interface_name: NTOS接口名称
            context: 数据上下文

        Returns:
            bool: 验证是否通过
        """
        try:
            # 基本的NTOS接口名称格式验证
            # 格式通常为: Ge0/0, Ge0/1.100, Lo0等
            import re

            # 物理接口格式: Ge0/0, Ge0/1等
            if re.match(r'^Ge\d+/\d+$', interface_name):
                return True

            # TenGe物理接口格式: TenGe0/0, TenGe0/1等
            if re.match(r'^TenGe\d+/\d+$', interface_name):
                return True

            # VLAN子接口格式: Ge0/0.100等
            if re.match(r'^Ge\d+/\d+\.\d+$', interface_name):
                return True

            # TenGe VLAN子接口格式: TenGe0/0.100等
            if re.match(r'^TenGe\d+/\d+\.\d+$', interface_name):
                return True

            # 环回接口格式: Lo0, Lo1等
            if re.match(r'^Lo\d+$', interface_name):
                return True

            # 其他可能的接口格式
            if re.match(r'^(Eth|FastEthernet|GigabitEthernet|TenGigabitEthernet)\d+/\d+(\.\d+)?$', interface_name):
                return True

            log(_("fortigate_strategy.interface.yang_invalid_format", interface=interface_name), "debug")
            return False

        except Exception as e:
            log(_("fortigate_strategy.interface.validation_exception", interface=interface_name, error=str(e)), "warning")
            return True  # 验证异常时，允许通过

    def _validate_interface_mapping_strict(self, policy: Dict, interface_mapping: Dict, context: DataContext) -> bool:
        """
        严格验证策略中的接口映射（支持区域名称解析，结合YANG模型）

        Args:
            policy: FortiGate策略配置
            interface_mapping: 接口映射表
            context: 数据上下文

        Returns:
            bool: 验证是否通过
        """
        policy_id = policy.get("policyid", "unknown")

        log(_("fortigate_strategy.validation.strict_start", policy_id=policy_id), "info")
        log(_("fortigate_strategy.validation.available_mappings", mappings=interface_mapping), "info")

        if not interface_mapping:
            log(_("fortigate_strategy.validation.empty_mapping", policy_id=policy_id), "warning")
            return False

        # 注意：context参数已经通过方法参数传入，无需重新获取

        # 检查源接口映射
        srcintf_list = policy.get("srcintf", [])
        if isinstance(srcintf_list, str):
            srcintf_list = [srcintf_list]

        log(_("fortigate_strategy.validation.check_source", interfaces=srcintf_list), "info")

        for intf in srcintf_list:
            if intf and intf.strip():
                clean_intf = intf.strip('"').strip("'")
                log(_("fortigate_strategy.validation.validate_source", interface=clean_intf), "info")

                # 特殊处理"any"接口
                if clean_intf.lower() == "any":
                    continue

                # 首先检查是否为具体接口名称
                if clean_intf in interface_mapping:
                    log(_("fortigate_strategy.validation.source_found", interface=clean_intf), "info")
                    continue

                # 检查是否为区域名称
                zone_interfaces = self._resolve_zone_to_interfaces(clean_intf, interface_mapping, context)
                if zone_interfaces:
                    log(_("fortigate_strategy.validation.source_zone_resolved", zone=clean_intf, interfaces=zone_interfaces), "info")
                    continue

                # 既不是接口也不是区域
                log(_("fortigate_strategy.validation.source_not_found", policy_id=policy_id, interface=clean_intf), "warning")
                log(_("fortigate_strategy.validation.mapping_keys", keys=list(interface_mapping.keys())), "info")
                return False

        # 检查目标接口映射
        dstintf_list = policy.get("dstintf", [])
        if isinstance(dstintf_list, str):
            dstintf_list = [dstintf_list]

        log(_("fortigate_strategy.validation.check_destination", interfaces=dstintf_list), "info")

        for intf in dstintf_list:
            if intf and intf.strip():
                clean_intf = intf.strip('"').strip("'")
                log(_("fortigate_strategy.validation.validate_destination", interface=clean_intf), "info")

                # 特殊处理"any"接口
                if clean_intf.lower() == "any":
                    log(f"DEBUG: Policy {policy_id} - 目标接口 'any' 被接受（表示所有接口）", "info")
                    continue

                # 首先检查是否为具体接口名称
                if clean_intf in interface_mapping:
                    log(_("fortigate_strategy.validation.destination_found", interface=clean_intf), "info")
                    continue

                # 检查是否为区域名称
                zone_interfaces = self._resolve_zone_to_interfaces(clean_intf, interface_mapping, context)
                if zone_interfaces:
                    log(_("fortigate_strategy.validation.destination_zone_resolved", zone=clean_intf, interfaces=zone_interfaces), "info")
                    continue

                # 既不是接口也不是区域
                log(_("fortigate_strategy.validation.destination_not_found", policy_id=policy_id, interface=clean_intf), "warning")
                log(_("fortigate_strategy.validation.mapping_keys", keys=list(interface_mapping.keys())), "info")
                return False

        log(_("fortigate_strategy.validation.all_passed", policy_id=policy_id), "info")
        return True

    def _add_security_features(self, policy: Dict, security_policy: Dict, context: DataContext = None):
        """
        添加安全功能映射 - 基于用户记忆中的映射规则和NTOS YANG模型要求，支持版本兼容性检测

        Args:
            policy: 原始策略
            security_policy: 安全策略配置
            context: 数据上下文（用于获取版本信息）
        """
        # 获取NTOS版本信息进行兼容性检测
        ntos_version = self._get_ntos_version(context)

        # 根据用户记忆：任何av-profile都映射到default-alert
        if policy.get("av_profile") or policy.get("av-profile"):
            security_policy["av"] = "default-alert"

        # 根据用户记忆：任何ips-sensor都映射到default-use-signature-action
        if policy.get("ips_sensor") or policy.get("ips-sensor"):
            security_policy["ips"] = "default-use-signature-action"

        # Web过滤配置文件映射到websec模板
        if policy.get("webfilter_profile") or policy.get("webfilter-profile"):
            security_policy["websec"] = "default-websec-template"

        # SSL/SSH配置文件映射到content-filter模板
        if policy.get("ssl_ssh_profile") or policy.get("ssl-ssh-profile"):
            security_policy["content-filter"] = "default-content-filter-template"

        # 文件过滤配置文件映射到file-filter模板（仅R11+版本支持）
        if policy.get("file_filter_profile") or policy.get("file-filter-profile"):
            if self._is_version_supported(ntos_version, "file-filter"):
                security_policy["file-filter"] = "default-file-filter-template"
            else:
                log(_("fortigate_strategy.security_features.file_filter_not_supported", version=ntos_version), "warning")

        # 协议选项配置文件也可以映射到content-filter模板
        if policy.get("profile_protocol_options") or policy.get("profile-protocol-options"):
            if "content-filter" not in security_policy:  # 避免覆盖SSL/SSH映射
                security_policy["content-filter"] = "default-protocol-options-template"

    def _add_time_range_mapping(self, policy: Dict, security_policy: Dict):
        """
        添加时间范围映射 - 将FortiGate schedule字段映射到NTOS time-range字段

        Args:
            policy: 原始策略
            security_policy: 安全策略配置
        """
        schedule = policy.get("schedule", "always")

        # 处理特殊值和引号
        if schedule:
            # 移除可能存在的引号
            if schedule.startswith('"') and schedule.endswith('"'):
                schedule = schedule[1:-1]

            # 处理特殊值
            if schedule.lower() in ["all", ""]:
                schedule = "always"
        else:
            schedule = "always"

        # 只有当schedule不是"always"时才添加time-range字段
        if schedule != "always":
            security_policy["time-range"] = schedule

    def _get_ntos_version(self, context: DataContext = None) -> str:
        """
        获取NTOS目标版本信息 - 注意：这是NTOS的版本，不是FortiGate的版本

        Args:
            context: 数据上下文

        Returns:
            str: NTOS版本（如R10P2、R11等）
        """
        if context:
            # 方法1：从CLI参数中获取NTOS版本信息
            ntos_version = context.get_data("ntos_version") or context.get_data("version")
            if ntos_version:
                # 标准化版本格式
                version_str = str(ntos_version).upper()
                if "R11" in version_str:
                    return "R11"
                elif "R10P2" in version_str:
                    return "R10P2"
                elif "R10" in version_str:
                    return "R10P2"  # R10默认为R10P2

            # 方法2：从设备模型推断NTOS版本（如果CLI参数中包含版本信息）
            model = context.get_data("model")
            if model:
                model_str = str(model).upper()
                # 注意：这里检查的是目标NTOS设备的版本，不是FortiGate版本
                if "R11" in model_str:
                    return "R11"
                elif "R10P2" in model_str or "R10" in model_str:
                    return "R10P2"

        # 默认返回R11（最新NTOS版本，功能最完整）
        return "R11"

    def _is_version_supported(self, version: str, feature: str) -> bool:
        """
        检查指定NTOS版本是否支持特定功能 - 基于YANG模型验证结果

        Args:
            version: NTOS版本（如R10P2、R11）
            feature: 功能名称

        Returns:
            bool: 是否支持该功能
        """
        # NTOS版本功能兼容性映射表 - 基于YANG模型分析结果
        version_features = {
            "R10P2": {
                "av": True,           # R10P2支持av字段
                "ips": True,          # R10P2支持ips字段
                "websec": True,       # R10P2支持websec字段
                "content-filter": True, # R10P2支持content-filter字段
                "file-filter": False, # R10P2 YANG模型中不存在file-filter字段
                "nat": True,          # R10P2支持NAT功能（static-snat44、dynamic-snat44、static-dnat44）
            },
            "R11": {
                "av": True,           # R11支持av字段
                "ips": True,          # R11支持ips字段
                "websec": True,       # R11支持websec字段
                "content-filter": True, # R11支持content-filter字段
                "file-filter": True,  # R11 YANG模型中包含file-filter字段
                "nat": True,          # R11支持NAT功能（static-snat44、dynamic-snat44、static-dnat44）
            }
        }

        # 获取版本支持的功能
        supported_features = version_features.get(version, version_features["R11"])
        return supported_features.get(feature, True)  # 默认支持

    def _get_interface_mapping_from_context(self, context: DataContext) -> Dict[str, str]:
        """
        从数据上下文获取接口映射信息

        Args:
            context: 数据上下文

        Returns: Dict[str, str]: 接口映射字典
        """
        interface_mapping = {}

        try:
            # 方法1：从interface_processing_result获取
            interface_result = context.get_data("interface_processing_result")
            if interface_result:
                final_mapping = interface_result.get("final_mapping", {})
                if final_mapping:
                    interface_mapping = final_mapping
                    return interface_mapping

            # 方法2：从interface_mappings获取
            interface_mappings = context.get_data("interface_mappings", {})
            if interface_mappings:
                interface_mapping = interface_mappings
                return interface_mapping

            # 方法3：从原始映射文件获取
            mapping_file = context.get_data("mapping_file")
            if mapping_file:
                try:
                    import json
                    with open(mapping_file, 'r', encoding='utf-8') as f:
                        file_mapping = json.load(f)

                    # 处理嵌套格式
                    if "interface_mappings" in file_mapping:
                        interface_mapping = file_mapping["interface_mappings"]
                    else:
                        # 扁平格式，过滤非接口映射的键
                        interface_mapping = {k: v for k, v in file_mapping.items()
                                           if k not in ["default_mapping"] and isinstance(v, str)}

                    if interface_mapping:
                        return interface_mapping

                except Exception as e:
                    log(f"Failed to load interface mapping from file: {e}", "warning")

            log("No interface mapping found in context", "warning")
            return {}

        except Exception as e:
            log(f"Error getting interface mapping from context: {e}", "error")
            return {}

    def _generate_nat_rule(self, policy: Dict, classification: Dict, context: DataContext) -> Optional[List[Dict]]:
        """
        生成NAT规则配置 - 支持复合NAT和版本兼容性

        Args:
            policy: 原始策略
            classification: 策略分类
            context: 数据上下文（包含所有处理结果）

        Returns:
            Optional[List[Dict]]: NAT规则配置列表（支持多个规则）
        """
        policy_id = policy.get("policyid", "unknown")

        # 检查版本兼容性
        if not classification.get("version_compatible", True):
            return None

        if not classification.get("needs_nat", False):
            return None

        try:
            # 委托给现有的nat_processor
            from engine.processors.nat_processor import NATProcessor

            # 创建临时处理器实例并传递接口映射
            processor = NATProcessor()

            # 从context获取接口映射并传递给NAT处理器
            interface_mapping = self._get_interface_mapping_from_context(context)
            if interface_mapping:
                processor.interface_mapping = interface_mapping
                processor._interface_mapping = interface_mapping  # 同时设置缓存

            # 获取配置数据以保持兼容性
            config_data = context.get_data("config_data", {})

            # 从nat_rules中提取VIP和IP池配置
            nat_rules_data = config_data.get("nat_rules", [])
            ippools = config_data.get("ippools", {})

            # 构建VIP字典（从nat_rules中提取）
            vips = {}
            for rule in nat_rules_data:
                if rule.get("type") == "vip":
                    vip_name = rule.get("name")
                    if vip_name:
                        vips[vip_name] = rule

            log(safe_translate("fortigate_strategy.nat.vip_pool_count",
                             policy_id=policy_id, vip_count=len(vips), pool_count=len(ippools)), "info")

            # 根据NAT类型生成不同的规则
            nat_type = classification.get("nat_type")
            generated_rules = []

            if nat_type == "compound_nat":
                # 复合NAT：既有VIP又有nat enable - 生成源目的同时转换的NAT规则
                compound_rules = self._generate_compound_nat_rules(policy, vips, ippools, processor)
                if compound_rules:
                    generated_rules.extend(compound_rules)
            elif nat_type == "dnat":
                # 纯DNAT：只有VIP对象
                dnat_rules = self._generate_dnat_rules(policy, vips, processor)
                if dnat_rules:
                    generated_rules.extend(dnat_rules)
            elif nat_type in ["static_snat", "dynamic_snat"]:
                # 纯SNAT：只有nat enable
                snat_rules = self._generate_snat_rules(policy, ippools, processor, nat_type)
                if snat_rules:
                    generated_rules.extend(snat_rules)

            return generated_rules if generated_rules else None

        except Exception as e:
            log(f"Policy {policy_id} NAT rule generation failed: {str(e)}", "error")
            return None
    
    def validate_xml_output(self, context: DataContext) -> bool:
        """
        验证XML输出是否符合YANG模型
        
        Args:
            context: 数据上下文
            
        Returns:
            bool: 验证是否通过
        """
        xml_content = context.get_data("generated_xml")
        if not xml_content:
            context.add_error(_("fortigate_strategy.no_xml_content"))
            return False
        
        # 验证security-policy节点
        if "<security-policy" in xml_content:
            if not self._validate_security_policy_xml(xml_content, context):
                return False
        
        # 验证NAT节点
        if "<nat" in xml_content:
            if not self._validate_nat_xml(xml_content, context):
                return False
        
        return True
    
    def _validate_security_policy_xml(self, xml_content: str, context: DataContext) -> bool:
        """
        验证security-policy XML结构
        
        Args:
            xml_content: XML内容
            context: 数据上下文
            
        Returns:
            bool: 验证是否通过
        """
        # 检查必要的XML结构
        required_elements = [
            'xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy"',
            "<policy>",
            "<name>",
            "<action>",
            "<config-source>"
        ]
        
        for element in required_elements:
            if element not in xml_content:
                context.add_error(_("fortigate_strategy.missing_xml_element", element=element))
                return False
        
        return True
    
    def _validate_nat_xml(self, xml_content: str, context: DataContext) -> bool:
        """
        验证NAT XML结构
        
        Args:
            xml_content: XML内容
            context: 数据上下文
            
        Returns:
            bool: 验证是否通过
        """
        # 检查NAT相关的XML结构
        nat_elements = ["static-dnat44", "static-snat44", "dynamic-snat44"]
        has_nat_rule = any(element in xml_content for element in nat_elements)
        
        if has_nat_rule:
            # 检查NAT命名空间
            if 'xmlns="urn:ruijie:ntos:params:xml:ns:yang:nat"' not in xml_content:
                context.add_error(_("fortigate_strategy.missing_nat_namespace"))
                return False
        
        return True

    def _supports_twice_nat44(self, policy: Dict, vips: Dict) -> bool:
        """
        检查策略是否支持twice-nat44转换

        Args:
            policy: FortiGate策略配置
            vips: VIP对象字典

        Returns:
            bool: 是否支持twice-nat44转换

        Note:
            检查条件包括：
            1. 策略包含VIP对象
            2. 策略启用了NAT
            3. 不使用IP池（暂不支持）
            4. VIP配置完整
        """
        try:
            # 检查基本条件
            has_vip = any(addr in vips for addr in policy.get("dstaddr", []))
            nat_enabled = policy.get("nat", "disable") == "enable"

            if not (has_vip and nat_enabled):
                return False

            # 检查复杂场景支持
            # 暂不支持IP池场景
            if policy.get("ippool", "disable") == "enable":
                log(_("fortigate_strategy.twice_nat44_ippool_not_supported", policy=policy.get("name")), "debug")
                return False

            # 检查VIP配置完整性
            for addr in policy.get("dstaddr", []):
                if addr in vips:
                    vip = vips[addr]
                    if not all(key in vip for key in ["extip", "mappedip"]):
                        log(_("fortigate_strategy.twice_nat44_incomplete_vip", vip=addr), "debug")
                        return False

            return True

        except Exception as e:
            log(_("fortigate_strategy.twice_nat44_support_check_failed", error=str(e)), "error")
            return False

    def _generate_twice_nat44_rule(self, policy: Dict, vip_config: Dict) -> Dict:
        """
        生成twice-nat44规则

        Args:
            policy: FortiGate策略配置
            vip_config: VIP对象配置

        Returns:
            Dict: twice-nat44规则配置

        Raises:
            Exception: 当配置无效时
        """
        try:
            from engine.business.models.twice_nat44_models import TwiceNat44Rule

            # 使用数据模型创建规则
            rule = TwiceNat44Rule.from_fortigate_policy(policy, vip_config)

            # 转换为NAT规则字典格式
            rule_dict = rule.to_nat_rule_dict()

            log(_("fortigate_strategy.twice_nat44_rule_generated",
                 policy=policy.get('name'), vip=vip_config.get('name')), "info")

            return rule_dict

        except Exception as e:
            log(_("fortigate_strategy.twice_nat44_rule_generation_failed",
                 policy=policy.get('name'), error=str(e)), "error")
            raise

    def _generate_compound_nat_rules(self, policy: Dict, vips: Dict, ippools: Dict, processor) -> List[Dict]:
        """
        生成复合NAT规则（支持twice-nat44和回退机制）

        Args:
            policy: 策略配置
            vips: VIP对象字典
            ippools: IP池字典
            processor: NAT处理器

        Returns:
            List[Dict]: NAT规则列表

        Note:
            优先使用twice-nat44，失败时回退到原有逻辑
        """
        # 检查是否启用twice-nat44
        use_twice_nat44 = processor.context.get_config("nat.use_twice_nat44", True)

        if use_twice_nat44 and self._supports_twice_nat44(policy, vips):
            try:
                # 使用twice-nat44方案
                rules = []
                vip_addresses = policy.get("dstaddr", [])

                for vip_name in vip_addresses:
                    if vip_name in vips:
                        twice_nat_rule = self._generate_twice_nat44_rule(policy, vips[vip_name])
                        if twice_nat_rule:
                            rules.append(twice_nat_rule)
                            log(_("fortigate_strategy.twice_nat44_generated",
                                 policy=policy.get('name'), vip=vip_name), "info")

                return rules

            except Exception as e:
                log(_("fortigate_strategy.twice_nat44_failed",
                     policy=policy.get('name'), error=str(e)), "warning")

                # 回退到原有逻辑
                fallback_enabled = processor.context.get_config("nat.twice_nat44_fallback", True)
                if fallback_enabled:
                    log(_("fortigate_strategy.fallback_to_legacy"), "info")
                    return self._generate_compound_nat_rules_legacy(policy, vips, ippools, processor)
                else:
                    raise

        # 使用原有逻辑
        return self._generate_compound_nat_rules_legacy(policy, vips, ippools, processor)

    def _generate_compound_nat_rules_legacy(self, policy: Dict, vips: Dict, ippools: Dict, processor) -> List[Dict]:
        """
        原有的复合NAT规则生成逻辑（重命名保留）

        Args:
            policy: 策略配置
            vips: VIP对象字典
            ippools: IP池字典
            processor: NAT处理器

        Returns: List[Dict]: 复合NAT规则列表
        """
        # 保持原有实现不变
        rules = []

        # 生成DNAT规则
        dnat_rules = self._generate_dnat_rules(policy, vips, processor)
        if dnat_rules:
            rules.extend(dnat_rules)

        # 生成SNAT规则
        nat_type = "dynamic_snat" if policy.get("ippool", "disable") == "enable" else "static_snat"
        snat_rules = self._generate_snat_rules(policy, ippools, processor, nat_type)
        if snat_rules:
            rules.extend(snat_rules)

        return rules

    def _generate_dnat_rules(self, policy: Dict, vips: Dict, processor) -> List[Dict]:
        """
        生成DNAT规则（基于VIP对象）

        Args:
            policy: 策略配置
            vips: VIP对象字典
            processor: NAT处理器

        Returns: List[Dict]: DNAT规则列表
        """
        policy_id = policy.get("policyid", "unknown")
        log(safe_translate("fortigate_strategy.dnat.generation_start",
                         policy_id=policy_id, vip_count=len(vips)), "info")

        # 使用现有的NAT处理器逻辑
        nat_rules = processor._extract_nat_rules_from_policy(policy, vips, {})
        log(safe_translate("fortigate_strategy.dnat.processor_returned",
                         policy_id=policy_id, count=len(nat_rules)), "info")

        if nat_rules:
            rule_types = [rule.get('type') for rule in nat_rules]
            log(safe_translate("fortigate_strategy.dnat.rule_types",
                             policy_id=policy_id, types=str(rule_types)), "info")

        # NAT处理器返回的类型是"static-dnat44"，不是"dnat"
        dnat_rules = [rule for rule in nat_rules if rule.get("type") in ["dnat", "static-dnat44"]]
        log(safe_translate("fortigate_strategy.dnat.filtered_count",
                         policy_id=policy_id, count=len(dnat_rules)), "info")
        return dnat_rules

    def _generate_snat_rules(self, policy: Dict, ippools: Dict, processor, nat_type: str) -> List[Dict]:
        """
        生成SNAT规则（基于nat enable）

        Args:
            policy: 策略配置
            ippools: IP池字典
            processor: NAT处理器
            nat_type: SNAT类型（static_snat或dynamic_snat）

        Returns: List[Dict]: SNAT规则列表
        """
        policy_id = policy.get("policyid", "unknown")

        # 根据nat_type直接生成对应的SNAT规则
        if nat_type == "dynamic_snat":
            # 生成动态SNAT规则 - 需要提取poolname列表
            poolname_list = policy.get("poolname", [])
            if isinstance(poolname_list, str):
                poolname_list = [poolname_list]
            snat_rule = processor._create_dynamic_snat_rule(policy, poolname_list, ippools)
        else:
            # 生成静态SNAT规则
            snat_rule = processor._create_snat_rule(policy, ippools)

        nat_rules = [snat_rule] if snat_rule else []

        if nat_rules:
            for i, rule in enumerate(nat_rules):
                log(f"DEBUG: Policy {policy_id} - NAT rule {i}: type={rule.get('type')}, keys={list(rule.keys())}", "info")

        # 修复：NAT处理器返回的类型是"static-snat44"或"dynamic-snat44"，不是"snat"
        snat_rules = [rule for rule in nat_rules if rule.get("type") in ["snat", "static-snat44", "dynamic-snat44"]]
        return snat_rules
