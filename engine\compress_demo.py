#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
配置压缩示例
"""

import ctypes
import os
import sys
import platform
import tarfile
import shutil
import tempfile
import datetime
import subprocess
import glob
import json
import zlib
import base64
import time
from engine.utils.logger import log
from engine.utils.i18n import _  # 导入国际化函数

class CompressionAPI:
    def __init__(self, lib_path=None):
        """初始化压缩API接口
        
        Args:
            lib_path: libconf_compress.so库文件的路径，如果为None则使用x86目录下的库
        """
        # 全局清理库文件
        self._global_library_cleanup()
        
        # 配置库路径
        if lib_path is None:
            base_dir = os.path.dirname(os.path.abspath(__file__))
            
            # 优先检查环境变量中的加密库路径
            crypto_libs_path = os.environ.get('CRYPTO_LIBS_PATH')
            if crypto_libs_path and os.path.exists(os.path.join(crypto_libs_path, 'libconf_compress.so')):
                lib_path = os.path.join(crypto_libs_path, 'libconf_compress.so')
                log(_("compress.using_crypto_libs_path"), path=crypto_libs_path)
            # 其次尝试专用加密库目录
            elif os.path.exists(os.path.join(base_dir, "lib", "crypto", "libconf_compress.so")):
                lib_path = os.path.join(base_dir, "lib", "crypto", "libconf_compress.so")
                log(_("compress.using_crypto_dir"), path=lib_path)
            # 最后尝试原始x86目录
            else:
                lib_path = os.path.join(base_dir, "lib", "x86", "libconf_compress.so")
            
        # 显示当前信息
        current_dir = os.getcwd()
        log(_("compress.current_working_dir"), dir=current_dir)
        log(_("compress.trying_to_load_lib"), path=os.path.abspath(lib_path))
        
        # 保存库路径和其所在目录
        self.lib_path = lib_path
        self.lib_dir = os.path.dirname(lib_path)
        
        # 检查路径和执行权限
        log(_("compress.checking_lib_file"), path=lib_path)
        if not os.path.exists(lib_path):
            log(_("compress.error.lib_not_exists"), "error", path=lib_path)
            raise RuntimeError(f"库文件不存在: {lib_path}")
        
        # 检查库文件是否真的是文件
        if not os.path.isfile(lib_path):
            log(_("compress.error.lib_not_file"), "error", path=lib_path)
            raise RuntimeError(f"指定的库路径不是文件: {lib_path}")
        
        # 检查库文件的权限
        try:
            file_stat = os.stat(lib_path)
            file_mode = file_stat.st_mode
            can_read = bool(file_mode & 0o400)
            can_execute = bool(file_mode & 0o100)
            log(_("compress.lib_file_permissions"), path=lib_path, mode=oct(file_mode), 
                can_read=can_read, can_execute=can_execute)
        except Exception as e:
            error_msg = _("compress.error.checking_permissions", error=str(e))
            log(error_msg, "warning")
        
        # 初始化库引用存储和环境检测
        self._loaded_libs = {}
        self._temp_dir = None
        is_container = self._is_container_environment()
        
        log(_("compress.init_env"), container=is_container, lib_dir=self.lib_dir)
        
        # 根据环境选择初始化策略
        if is_container:
            # 容器环境：直接使用原始库目录，避免不必要的文件复制
            pass  # 保持lib_dir不变
        else:
            # 非容器环境：仅在必要时创建临时目录
            self._setup_temp_library_dir()
        
        # 快速设置库环境和加载主库
        self._quick_setup_and_load(is_container)
    
    def _try_container_load_main_lib(self, lib_name):
        """针对容器环境的库加载方法"""
        try:
            import ctypes.util
            
            # 确保依赖库已经在进程中加载
            for dep_lib in ["libttylog.so", "libcrypto.so.1.1", "libssl.so.1.1"]:
                if dep_lib in self._loaded_libs:
                    log(_("compress.dependency_already_loaded"), lib=dep_lib)
            
            abs_lib_path = os.path.join(os.getcwd(), lib_name)
            
            # 使用RPATH和ldconfig方法加载库
            try:
                log(_("compress.trying_container_method1_rpath"))
                
                # 强制刷新动态链接器缓存
                import subprocess
                try:
                    subprocess.run(['ldconfig'], check=False, capture_output=True)
                    log(_("compress.ldconfig_executed"))
                except:
                    pass
                
                # 通过LD_LIBRARY_PATH和LD_PRELOAD强制依赖加载
                current_env = os.environ.copy()
                abs_lib_dir = os.path.dirname(abs_lib_path)
                
                # 简化库目录，只包含必要的路径
                lib_paths = [
                    abs_lib_dir,
                    "/usr/lib",
                    "/usr/local/lib"
                ]
                current_env['LD_LIBRARY_PATH'] = ':'.join(lib_paths)
                
                # 强制预加载所有依赖
                preload_libs = []
                for dep in ["libttylog.so", "libcrypto.so.1.1", "libssl.so.1.1"]:
                    # 尝试多个可能的路径
                    for lib_dir in lib_paths:
                        dep_path = os.path.join(lib_dir, dep)
                        if os.path.exists(dep_path):
                            preload_libs.append(dep_path)
                            log(_("compress.found_dependency"), path=dep_path)
                            break
                
                if preload_libs:
                    current_env['LD_PRELOAD'] = ':'.join(preload_libs)
                    log(_("compress.setting_ld_preload"), libs=':'.join(preload_libs))
                
                # 更新当前进程的环境变量
                os.environ.update(current_env)
                
                # 检查平台支持的加载模式
                load_mode = None
                if hasattr(ctypes, 'RTLD_NOW') and hasattr(ctypes, 'RTLD_GLOBAL'):
                    load_mode = ctypes.RTLD_NOW | ctypes.RTLD_GLOBAL
                elif hasattr(ctypes, 'RTLD_GLOBAL'):
                    load_mode = ctypes.RTLD_GLOBAL
                
                # 加载主库
                if load_mode is not None:
                    self.CONF_COMPRESS_API = ctypes.CDLL(abs_lib_path, mode=load_mode)
                else:
                    self.CONF_COMPRESS_API = ctypes.CDLL(abs_lib_path)
                    
                log(_("compress.container_load_success_method1"))
                log(_("compress.main_lib_loaded_successfully"))
                return True
                    
            except Exception as e:
                error_msg = _("compress.method1_failed", error=str(e))
                log(error_msg, "warning")
                return False
            
        except Exception as e:
            error_msg = _("compress.container_load_failed", error=str(e))
            log(error_msg, "warning")
            return False
    
    def _try_traditional_load_main_lib(self, lib_name, lib_path):
        """传统的库加载方法"""
        # 尝试多次加载主库，有时第一次可能会失败
        max_attempts = 3
        last_error = None
        
        for attempt in range(max_attempts):
            try:
                # 尝试使用相对路径加载
                log(_("compress.trying_relative_path"), attempt=attempt+1, name=lib_name)
                # 检查平台支持的加载模式
                load_mode = None
                if hasattr(ctypes, 'RTLD_GLOBAL'):
                    load_mode = ctypes.RTLD_GLOBAL
                
                if load_mode is not None:
                    self.CONF_COMPRESS_API = ctypes.CDLL(f"./{lib_name}", mode=load_mode)
                else:
                    self.CONF_COMPRESS_API = ctypes.CDLL(f"./{lib_name}")
                log(_("compress.load_main_lib_success"), path=f"./{lib_name}")
                log(_("compress.main_lib_loaded_successfully"))
                return True
            except Exception as e:
                last_error = e
                error_msg = _("compress.load_with_relative_path_failed", error=str(e))
                log(error_msg, "warning")
                
                # 尝试使用绝对路径加载
                try:
                    log(_("compress.loading_with_absolute_path"), attempt=attempt+1, path=lib_path)
                    if load_mode is not None:
                        self.CONF_COMPRESS_API = ctypes.CDLL(lib_path, mode=load_mode)
                    else:
                        self.CONF_COMPRESS_API = ctypes.CDLL(lib_path)
                    log(_("compress.load_main_lib_success"), path=lib_path)
                    log(_("compress.main_lib_loaded_successfully"))
                    return True
                except Exception as e2:
                    last_error = e2
                    if attempt < max_attempts - 1:
                        error_msg = _("compress.retry_load_main_lib", attempt=attempt+1, error=str(e2))
                        log(error_msg, "warning")
                        time.sleep(0.5)  # 短暂延迟后重试
                    else:
                        log(_("compress.all_attempts_failed"), "error")
                        return False
        
        # 如果所有尝试都失败了
        error_msg = _("compress.traditional_load_completely_failed", error=str(last_error))
        log(error_msg, "error")
        return False
    
    def _is_container_environment(self):
        """优化的容器环境检测
        
        Returns:
            bool: 如果在容器环境中返回True，否则返回False
        """
        try:
            # 1. 快速检测 - 检查明显的容器标志
            if (os.path.exists("/.dockerenv") or 
                os.environ.get("container") == "docker" or
                os.environ.get("DOCKER_CONTAINER") == "1"):
                return True
            
            # 2. 检查工作目录是否在容器典型目录
            if os.getcwd().startswith("/app") or os.path.exists("/app"):
                return True
            
            # 3. 检查其他容器环境变量
            container_env_vars = ['KUBERNETES_SERVICE_HOST', 'K8S_NODE_NAME']
            if any(os.environ.get(var) for var in container_env_vars):
                return True
            
            # 4. 最后检查 cgroup（较慢的检测）
            try:
                with open('/proc/1/cgroup', 'r') as f:
                    content = f.read(1024)  # 只读前1KB，提高效率
                    return any(pattern in content for pattern in ['/docker/', '/kubepods/', '/lxc/'])
            except (FileNotFoundError, PermissionError):
                pass
                
            return False
        except Exception as e:
            error_msg = _("compress.error.container_detection_failed", error=str(e))
            log(error_msg, "warning")
            return False
    
    def _prepare_container_library_loading(self):
        """在容器环境中准备库加载的特殊处理"""
        try:
            log(_("compress.preparing_container_loading"))
            
            # 确保所有依赖库都已经在内存中
            for lib_name in ["libttylog.so", "libcrypto.so.1.1", "libssl.so.1.1"]:
                if lib_name in self._loaded_libs and self._loaded_libs[lib_name]:
                    log(_("compress.dependency_already_in_memory"), lib=lib_name)
            
            # 刷新动态链接器缓存（如果可能）
            try:
                subprocess.run(['ldconfig'], check=False, capture_output=True, timeout=5)
                log(_("compress.ldconfig_refreshed"))
            except:
                pass  # 在容器中可能没有权限运行ldconfig
                
        except Exception as e:
            error_msg = _("compress.error.preparing_container_loading", error=str(e))
            log(error_msg, "warning")
    
    def __del__(self):
        """析构函数，清理临时目录"""
        # 只在非容器环境下清理临时目录
        if (hasattr(self, '_temp_dir') and self._temp_dir and 
            os.path.exists(self._temp_dir) and 
            not (os.getcwd().startswith("/app") or os.path.exists("/app"))):
            try:
                shutil.rmtree(self._temp_dir)
                log(_("compress.cleaned_temp_lib_dir"), dir=self._temp_dir)
            except Exception as e:
                error_msg = _("compress.error.cleanup_temp_dir", error=str(e))
                log(error_msg, "warning")

    def _global_library_cleanup(self):
        """全局库文件清理，特别是删除任何可能干扰的libc.so文件"""
        try:
            # 搜索包含engine的路径中的libc.so文件
            paths_to_check = glob.glob("/app/engine/**/*", recursive=True)
            paths_to_check.extend(glob.glob("/usr/local/lib/engine*/**/*", recursive=True))
            
            for path in paths_to_check:
                # 如果是目录，检查其中的文件
                if os.path.isdir(path):
                    for file in os.listdir(path):
                        if file.startswith("libc.so"):
                            full_path = os.path.join(path, file)
                            try:
                                os.remove(full_path)
                                log(_("compress.deleted_file"), path=full_path)
                            except Exception as e:
                                error_msg = _("compress.cannot_delete_file", path=full_path, error=str(e))
                                log(error_msg, "warning")
                # 如果是文件且是libc.so
                elif os.path.basename(path).startswith("libc.so"):
                    try:
                        os.remove(path)
                        log(_("compress.deleted_file"), path=path)
                    except Exception as e:
                        error_msg = _("compress.cannot_delete_file", path=path, error=str(e))
                        log(error_msg, "warning")
        
            log(_("compress.global_cleanup_complete"))
        except Exception as e:
            error_msg = _("compress.error.global_cleanup_failed", error=str(e))
            log(error_msg, "error")
    
    def _prepare_library_files(self):
        """准备库文件，确保动态链接器能够找到它们
        
        在容器环境中，可能需要特殊处理才能使库文件被正确加载。
        此方法会尝试使用多种方法确保库文件可用。
        """
        try:
            # 获取库文件目录的绝对路径
            abs_lib_dir = os.path.abspath(self.lib_dir)
            log(_("compress.preparing_library_files"), dir=abs_lib_dir)
            
            # 检查库文件是否存在
            critical_libs = ["libttylog.so", "libcrypto.so.1.1", "libssl.so.1.1", "libconf_compress.so"]
            # 排除系统基础库
            system_libs = ["libc.so.6", "libpthread.so.0", "libdl.so.2", "librt.so.1", "ld-linux-x86-64.so.2"]
            missing_libs = []
            
            for lib in critical_libs:
                if lib in system_libs:
                    continue  # 跳过系统基础库
                    
                lib_path = os.path.join(abs_lib_dir, lib)
                if not os.path.exists(lib_path):
                    missing_libs.append(lib)
                    log(_("compress.critical_lib_missing"), "warning", lib=lib)
                else:
                    # 检查权限
                    try:
                        mode = os.stat(lib_path).st_mode
                        is_exec = bool(mode & 0o100)
                        if not is_exec:
                            log(_("compress.fixing_lib_permissions"), lib=lib)
                            os.chmod(lib_path, mode | 0o755)
                    except Exception as e:
                        error_msg = _("compress.error.checking_lib_permissions", lib=lib, error=str(e))
                        log(error_msg, "warning")
            
            if missing_libs:
                log(_("compress.error.missing_critical_libs"), "error", libs=", ".join(missing_libs))
                return False
                
            # 检测容器环境
            is_container = self._is_container_environment()
            
            # 容器环境下的特殊处理
            if is_container:
                log(_("compress.container_special_handling"))
                
                # 1. 创建系统库目录（如果不存在）
                engine_lib_dir = "/usr/lib/engine"
                try:
                    if not os.path.exists(engine_lib_dir):
                        os.makedirs(engine_lib_dir)
                        log(_("compress.created_system_lib_dir"), dir=engine_lib_dir)
                except Exception as e:
                    error_msg = _("compress.error.creating_system_lib_dir", error=str(e))
                    log(error_msg, "warning")
                
                # 2. 创建符号链接到系统库目录
                try:
                    for lib in critical_libs:
                        if lib in system_libs:
                            continue  # 跳过系统基础库
                            
                        src_path = os.path.join(abs_lib_dir, lib)
                        dst_path = os.path.join(engine_lib_dir, lib)
                        
                        # 如果源文件存在，创建符号链接（如果目标不存在或不是指向正确位置的符号链接）
                        if os.path.exists(src_path):
                            if os.path.exists(dst_path):
                                # 如果已存在，检查是否是正确的符号链接
                                if os.path.islink(dst_path):
                                    existing_target = os.readlink(dst_path)
                                    if existing_target == src_path:
                                        log(_("compress.symlink_already_exists"), src=src_path, dst=dst_path)
                                        continue
                                    else:
                                        # 更新到正确的目标
                                        os.unlink(dst_path)
                                else:
                                    # 如果是常规文件，备份并替换
                                    backup_path = f"{dst_path}.bak"
                                    shutil.move(dst_path, backup_path)
                                    log(_("compress.backed_up_existing_file"), src=dst_path, dst=backup_path)
                            
                            # 创建符号链接
                            os.symlink(src_path, dst_path)
                            log(_("compress.created_system_symlink"), src=src_path, dst=dst_path)
                except Exception as e:
                    error_msg = _("compress.error.creating_symlinks", error=str(e))
                    log(error_msg, "warning")
                
                # 3. 强制刷新动态链接器缓存
                try:
                    subprocess.run(['ldconfig'], check=False, capture_output=True, timeout=5)
                    log(_("compress.ran_ldconfig"))
                except Exception as e:
                    error_msg = _("compress.error.running_ldconfig", error=str(e))
                    log(error_msg, "warning")
                
                # 不再复制库文件到系统目录，只使用符号链接方式
            
            return True
            
        except Exception as e:
            error_msg = _("compress.error.preparing_library_files", error=str(e))
            log(error_msg, "error")
            return False

    def _setup_library_path(self):
        """设置库搜索路径 - 使用最小化策略避免系统库冲突"""
        try:
            # 确保 lib_dir 是绝对路径
            abs_lib_dir = os.path.abspath(self.lib_dir)
            
            # 检测容器环境
            is_container = self._is_container_environment()
            
            # 准备库文件（符号链接、权限等）
            self._prepare_library_files()
            
            if is_container:
                # 容器环境：只使用我们的库目录，避免系统库冲突
                # 保存原始系统路径以备后续需要
                original_ld_path = os.environ.get('LD_LIBRARY_PATH', '')
                
                # 容器环境中设置完整的库搜索路径
                search_paths = [
                    abs_lib_dir,
                    "/usr/lib/engine",  # 我们创建的系统目录
                    "/usr/local/lib"    # 系统基本库
                ]
                
                # 设置库搜索路径
                os.environ['LD_LIBRARY_PATH'] = ':'.join(search_paths)
                
                # 只预加载我们的依赖库，不依赖系统库
                our_libs = []
                for lib_name in ["libttylog.so", "libcrypto.so.1.1", "libssl.so.1.1"]:
                    lib_path = os.path.join(abs_lib_dir, lib_name)
                    if os.path.exists(lib_path):
                        our_libs.append(lib_path)
                
                if our_libs:
                    os.environ['LD_PRELOAD'] = ':'.join(our_libs)
                    log(_("compress.container_minimal_preload"), libs=':'.join(our_libs))
                
                log(_("compress.container_library_path"), path=os.environ['LD_LIBRARY_PATH'])
                
                # 存储原始路径，以便必要时恢复
                self._original_ld_path = original_ld_path
                
            else:
                # 非容器环境：保持现有逻辑
                current_ld_path = os.environ.get('LD_LIBRARY_PATH', '')
                if current_ld_path:
                    new_ld_path = f"{abs_lib_dir}:{current_ld_path}"
                else:
                    new_ld_path = abs_lib_dir
                
                os.environ['LD_LIBRARY_PATH'] = new_ld_path
                
                # 同时设置 LD_PRELOAD 来强制预加载关键库
                critical_libs = [
                    os.path.join(abs_lib_dir, "libttylog.so"),
                    os.path.join(abs_lib_dir, "libcrypto.so.1.1"),
                    os.path.join(abs_lib_dir, "libssl.so.1.1")
                ]
                
                # 过滤存在的库文件
                existing_libs = [lib for lib in critical_libs if os.path.exists(lib)]
                if existing_libs:
                    os.environ['LD_PRELOAD'] = ':'.join(existing_libs)
                    log(_("compress.ld_preload_set"), libs=':'.join(existing_libs))
                
                log(_("compress.ld_library_path_set"), path=new_ld_path)
                
        except Exception as e:
            error_msg = _("compress.error.setting_ld_library_path", error=str(e))
            log(error_msg, "error")
    
    def _load_dependent_libraries(self):
        """预加载依赖库，确保正确的加载顺序
        
        Returns:
            bool: 是否成功加载所有关键依赖库
        """
        try:
            # 在容器环境中，我们需要特殊的库加载策略
            is_container = self._is_container_environment()
            
            def _load_local_lib(lib_name):
                """从当前目录或专用加密库目录加载库"""
                # 首先尝试从专用加密库目录加载
                crypto_libs_path = os.environ.get('CRYPTO_LIBS_PATH')
                if crypto_libs_path and os.path.exists(os.path.join(crypto_libs_path, lib_name)):
                    full_path = os.path.join(crypto_libs_path, lib_name)
                    log(_("compress.using_crypto_lib"), lib=lib_name, path=full_path)
                # 其次尝试从lib_dir加载
                elif os.path.exists(os.path.join(self.lib_dir, lib_name)):
                    full_path = os.path.join(self.lib_dir, lib_name)
                    log(_("compress.using_lib_dir"), lib=lib_name, path=full_path)
                else:
                    log(_("compress.lib_not_found"), "warning", lib=lib_name)
                    return None
                
                # 容器环境特殊处理
                if is_container:
                    return self._container_load_library(full_path, lib_name)
                else:
                    return self._standard_load_library(full_path, lib_name)
            
            # 库加载顺序很重要
            core_libs = [
                "libttylog.so",        # 首先加载 ttylog 库
                "libcrypto.so.1.1",    # 加密库
                "libssl.so.1.1"        # SSL库
            ]
            
            # 确保当前工作目录是库目录
            original_dir = os.getcwd()
            os.chdir(self.lib_dir)
            log(_("compress.chdir_for_preload"), dir=self.lib_dir)
            
            try:
                # 预加载所有核心依赖库
                all_critical_loaded = True
                for lib_name in core_libs:
                    lib = _load_local_lib(lib_name)
                    if lib:
                        self._loaded_libs[lib_name] = lib
                        # 保持库引用，防止被垃圾回收
                        setattr(self, f"_lib_{lib_name.replace('.', '_')}", lib)
                        log(_("compress.dependency_loaded_successfully"), lib=lib_name)
                    else:
                        all_critical_loaded = False
                        log(_("compress.error.critical_lib_missing"), "error", lib=lib_name)
                
                # 容器环境额外处理
                if is_container and all_critical_loaded:
                    # 强制刷新符号表
                    try:
                        import subprocess
                        subprocess.run(['ldconfig'], check=False, capture_output=True, timeout=5)
                        log(_("compress.ldconfig_executed_after_preload"))
                    except:
                        pass
                
                return all_critical_loaded
            finally:
                # 恢复原始工作目录
                os.chdir(original_dir)
                log(_("compress.restore_dir_after_preload"), dir=original_dir)
                
        except Exception as e:
            error_msg = _("compress.error.preload_libs_failed", error=str(e))
            log(error_msg, "error")
            return False
    
    def _container_load_library(self, full_path, lib_name):
        """容器环境下的库加载方法"""
        try:
            # 方法1: 使用dlopen系统调用
            if platform.system() == "Linux":
                try:
                    libc = ctypes.CDLL("libc.so.6")
                    libc.dlopen.argtypes = [ctypes.c_char_p, ctypes.c_int]
                    libc.dlopen.restype = ctypes.c_void_p
                    libc.dlerror.restype = ctypes.c_char_p
                    
                    # 使用最强的加载标志
                    RTLD_NOW = 2
                    RTLD_GLOBAL = 256
                    RTLD_NODELETE = 4096
                    RTLD_DEEPBIND = 8  # Linux特有，强制优先使用库自身的符号
                    
                    flags = RTLD_NOW | RTLD_GLOBAL | RTLD_NODELETE | RTLD_DEEPBIND
                    
                    handle = libc.dlopen(full_path.encode(), flags)
                    if handle:
                        # 创建Python CDLL对象包装handle
                        lib = ctypes.CDLL(full_path)
                        log(_("compress.container_lib_loaded_via_dlopen"), lib=lib_name)
                        return lib
                    else:
                        error_msg = libc.dlerror()
                        if error_msg:
                            log(_("compress.container_dlopen_error"), "warning", 
                                lib=lib_name, error=error_msg.decode())
                except Exception as e:
                    error_msg = _("compress.container_dlopen_failed", lib=lib_name, error=str(e))
                    log(error_msg, "warning")
            
            # 方法2: 回退到标准方法
            return self._standard_load_library(full_path, lib_name)
            
        except Exception as e:
            error_msg = _("compress.container_load_library_failed", lib=lib_name, error=str(e))
            log(error_msg, "warning")
            return None
    
    def _standard_load_library(self, full_path, lib_name):
        """标准库加载方法"""
        load_modes = []
        
        # 检查平台并设置相应的加载模式
        if hasattr(ctypes, 'RTLD_GLOBAL'):
            load_modes.append(("RTLD_GLOBAL", ctypes.RTLD_GLOBAL))
        
        if hasattr(ctypes, 'RTLD_NOW') and hasattr(ctypes, 'RTLD_GLOBAL'):
            load_modes.append(("RTLD_NOW_GLOBAL", ctypes.RTLD_NOW | ctypes.RTLD_GLOBAL))
        
        # 如果没有这些常量（Windows），使用默认模式
        if not load_modes:
            load_modes.append(("DEFAULT", None))
        
        # 使用多种方法尝试加载库
        for mode_name, mode in load_modes:
            try:
                if mode is not None:
                    lib = ctypes.CDLL(full_path, mode=mode)
                else:
                    lib = ctypes.CDLL(full_path)
                log(_("compress.preloaded_lib"), lib=lib_name, mode=mode_name)
                return lib
            except Exception as e:
                log(_("compress.error.load_lib_mode_failed"), "warning", 
                    lib=lib_name, mode=mode_name, error=str(e))
                continue
        
        log(_("compress.error.load_lib_failed"), "error", lib=lib_name)
        return None
    
 
    def cm_file_encrypt(self, infile, outfile, ver_file, enflag=1):
        """加密文件
        
        Args:
            infile: 输入文件路径
            outfile: 输出文件路径
            ver_file: 版本信息输出文件路径
            enflag: 加密标志，1表示加密
            
        Returns:
            返回加密操作的状态码，0表示成功
        """
        # 检查输入文件是否存在
        if not os.path.exists(infile):
            log(_("compress.error.input_file_not_exists"), "error", file=infile)
            return -1
        
        # 使用真实API
        try:
            # 配置函数原型
            func = self.CONF_COMPRESS_API.cm_file_encrypt
            func.argtypes = [ctypes.c_char_p, ctypes.c_char_p, ctypes.c_char_p, ctypes.c_int]
            func.restype = ctypes.c_int
            
            # 调用函数
            result = func(infile.encode("utf-8"), 
                        outfile.encode("utf-8"), 
                        ver_file.encode("utf-8"), 
                        enflag)
            
            return result
        except Exception as e:
            error_msg = _("compress.error.call_real_api_failed", error=str(e))
            log(error_msg, "error")
            return -1

    def cm_file_decrypt(self, infile, outfile, ver_file, enflag=0):
        """解密文件
        
        Args:
            infile: 输入文件路径
            outfile: 输出文件路径
            ver_file: 版本信息输入文件路径
            enflag: 加密标志，0表示解密
            
        Returns:
            返回解密操作的状态码，0表示成功
        """
        # 检查输入文件和版本信息文件是否存在
        if not os.path.exists(infile):
            log(_("compress.error.input_file_not_exists"), "error", file=infile)
            return -1
            
        if not os.path.exists(ver_file):
            log(_("compress.error.version_file_not_exists"), "error", file=ver_file)
            return -1
            
        # 直接调用encrypt方法，使用enflag=0表示解密
        return self.cm_file_encrypt(infile, outfile, ver_file, enflag)

    def _setup_temp_library_dir(self):
        """设置临时库目录（仅非容器环境）"""
        self._temp_dir = tempfile.mkdtemp()
        
        try:
            # 只复制必要的库文件
            essential_libs = ['libconf_compress.so', 'libttylog.so', 'libcrypto.so.1.1', 'libssl.so.1.1']
            
            for lib_file in essential_libs:
                src = os.path.join(self.lib_dir, lib_file)
                if os.path.exists(src):
                    dst = os.path.join(self._temp_dir, lib_file)
                    shutil.copy2(src, dst)
                    os.chmod(dst, 0o755)
            
            # 更新库路径
            self.lib_path = os.path.join(self._temp_dir, os.path.basename(self.lib_path))
            self.lib_dir = self._temp_dir
            
        except Exception as e:
            if self._temp_dir and os.path.exists(self._temp_dir):
                shutil.rmtree(self._temp_dir)
                self._temp_dir = None
            raise RuntimeError(f"设置临时库目录失败: {str(e)}")
    
    def _quick_setup_and_load(self, is_container):
        """快速设置环境并加载库"""
        try:
            # 快速设置环境变量
            self._quick_setup_library_path(is_container)
            
            # 预加载关键依赖库
            if not self._quick_load_dependencies(is_container):
                raise RuntimeError("预加载依赖库失败")
            
            # 加载主库
            if not self._quick_load_main_library(is_container):
                raise RuntimeError("加载主库失败")
                
            log(_("compress.init_complete"))
            
        except Exception as e:
            self._cleanup_resources()
            raise
    
    def _quick_setup_library_path(self, is_container):
        """快速设置库路径环境变量"""
        abs_lib_dir = os.path.abspath(self.lib_dir)
        current_ld_path = os.environ.get('LD_LIBRARY_PATH', '')
        
        if is_container:
            # 容器环境：使用系统库路径
            search_paths = [abs_lib_dir, "/usr/lib", "/usr/local/lib"]
        else:
            # 非容器环境：简单添加库目录
            search_paths = [abs_lib_dir]
            if current_ld_path:
                search_paths.append(current_ld_path)
        
        os.environ['LD_LIBRARY_PATH'] = ':'.join(search_paths)
    
    def _quick_load_dependencies(self, is_container):
        """快速加载依赖库"""
        dependencies = ["libttylog.so", "libcrypto.so.1.1", "libssl.so.1.1"]
        
        for dep in dependencies:
            dep_path = os.path.join(self.lib_dir, dep)
            if os.path.exists(dep_path):
                try:
                    if is_container:
                        lib = self._load_library_with_flags(dep_path)
                    else:
                        lib = ctypes.CDLL(dep_path)
                    
                    if lib:
                        self._loaded_libs[dep] = lib
                except Exception:
                    # 依赖库加载失败不一定是致命错误
                    continue
        
        return True  # 即使某些依赖库加载失败，也尝试继续
    
    def _quick_load_main_library(self, is_container):
        """快速加载主库"""
        try:
            log(_("compress.loading_main_library"), path=self.lib_path, container=is_container)
            
            # 检查库文件是否存在
            if not os.path.exists(self.lib_path):
                log(_("compress.error.main_lib_not_exists"), "error", path=self.lib_path)
                return False
            
            # 尝试多种方式加载库
            load_methods = []
            
            if is_container:
                load_methods.append(("container_flags", lambda: self._load_library_with_flags(self.lib_path)))
            
            load_methods.extend([
                ("ctypes_default", lambda: ctypes.CDLL(self.lib_path)),
                ("ctypes_global", lambda: ctypes.CDLL(self.lib_path, mode=ctypes.RTLD_GLOBAL) if hasattr(ctypes, 'RTLD_GLOBAL') else None),
                ("ctypes_now_global", lambda: ctypes.CDLL(self.lib_path, mode=ctypes.RTLD_NOW | ctypes.RTLD_GLOBAL) if hasattr(ctypes, 'RTLD_NOW') and hasattr(ctypes, 'RTLD_GLOBAL') else None)
            ])
            
            last_error = None
            for method_name, load_func in load_methods:
                try:
                    log(_("compress.trying_load_method"), method=method_name)
                    lib = load_func()
                    if lib is not None:
                        self.CONF_COMPRESS_API = lib
                        log(_("compress.lib_loaded_successfully"), method=method_name)
                        break
                except Exception as e:
                    last_error = e
                    error_msg = _("compress.load_method_failed", method=method_name, error=str(e))
                    log(error_msg, "warning")
                    continue
            else:
                # 所有方法都失败了
                error_msg = _("compress.error.all_load_methods_failed", error=str(last_error))
                log(error_msg, "error")
                return False
            
            # 验证主要函数
            if hasattr(self.CONF_COMPRESS_API, 'cm_file_encrypt'):
                log(_("compress.main_function_verified"))
                return True
            else:
                log(_("compress.error.missing_main_function"), "error")
                # 尝试列出库中的符号
                try:
                    log(_("compress.listing_available_symbols"))
                    # 在Linux下使用objdump或nm查看符号
                    result = subprocess.run(['nm', '-D', self.lib_path], capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        symbols = [line.split()[-1] for line in result.stdout.split('\n') if 'T ' in line]
                        log(_("compress.available_symbols"), symbols=str(symbols[:10]))  # 只显示前10个
                except Exception as e:
                    error_msg = _("compress.error.list_symbols_failed", error=str(e))
                    log(error_msg, "warning")
                return False
                
        except Exception as e:
            error_msg = _("compress.error.load_main_library_failed", error=str(e))
            log(error_msg, "error")
            return False
    
    def _load_library_with_flags(self, lib_path):
        """使用适当的标志加载库"""
        try:
            if hasattr(ctypes, 'RTLD_NOW') and hasattr(ctypes, 'RTLD_GLOBAL'):
                return ctypes.CDLL(lib_path, mode=ctypes.RTLD_NOW | ctypes.RTLD_GLOBAL)
            elif hasattr(ctypes, 'RTLD_GLOBAL'):
                return ctypes.CDLL(lib_path, mode=ctypes.RTLD_GLOBAL)
            else:
                return ctypes.CDLL(lib_path)
        except Exception:
            return ctypes.CDLL(lib_path)  # 回退到默认加载
    
    def _cleanup_resources(self):
        """清理资源"""
        if hasattr(self, '_temp_dir') and self._temp_dir and os.path.exists(self._temp_dir):
            try:
                shutil.rmtree(self._temp_dir)
            except Exception:
                pass

def process_tar_gz_file(tar_gz_path):
    """处理.tar.gz文件，解压并找到所需的文件
    
    Args:
        tar_gz_path: .tar.gz文件的路径
        
    Returns:
        tuple: 包含临时目录、版本文件和加密文件路径的元组
    """
    if not os.path.exists(tar_gz_path):
        log(_("compress.error.tar_gz_not_exists"), "error", file=tar_gz_path)
        return None, None, None
        
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    
    try:
        # 解压文件
        with tarfile.open(tar_gz_path, 'r:gz') as tar:
            tar.extractall(path=temp_dir)
        
        # 寻找版本文件和加密文件
        ver_file = None
        encrypted_file = None
        
        for file in os.listdir(temp_dir):
            if file.endswith('.ver'):
                ver_file = os.path.join(temp_dir, file)
            elif file.endswith('.en'):
                encrypted_file = os.path.join(temp_dir, file)
        
        if not ver_file:
            log(_("compress.error.version_file_not_found"), "error")
            return None, None, None
            
        if not encrypted_file:
            log(_("compress.error.encrypted_file_not_found"), "error")
            return None, None, None
            
        log(_("compress.tar_gz_processed"), ver_file=ver_file, encrypted_file=encrypted_file)
        
        return temp_dir, ver_file, encrypted_file
        
    except Exception as e:
        error_msg = _("compress.error.process_tar_gz_failed", error=str(e))
        log(error_msg, "error")
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
        return None, None, None

def create_config_tar_gz(input_dir, output_file):
    """创建Config.tar.gz文件
    
    Args:
        input_dir: 输入目录路径
        output_file: 输出文件路径
        
    Returns:
        bool: 操作是否成功
    """
    if not os.path.exists(input_dir):
        log(_("compress.error.input_dir_not_exists"), "error", dir=input_dir)
        return False
        
    temp_dir = None
    try:
        # 创建临时目录
        temp_dir = tempfile.mkdtemp()
        config_dir = os.path.join(temp_dir, "Config")
        os.makedirs(config_dir)
        
        # 复制输入目录内容到Config目录
        for item in os.listdir(input_dir):
            src = os.path.join(input_dir, item)
            dst = os.path.join(config_dir, item)
            if os.path.isdir(src):
                shutil.copytree(src, dst, symlinks=True)  # 保持符号链接
            else:
                shutil.copy2(src, dst)  # 保持元数据
        
        # 使用tar命令创建Config.tar.gz
        try:
            # 切换到临时目录
            current_dir = os.getcwd()
            os.chdir(temp_dir)
            
            # 执行tar命令，使用-9参数指定最高压缩级别
            cmd = f"tar -czf {output_file} Config"
            result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True)
            
            # 切回原目录
            os.chdir(current_dir)
            
            return True
            
        except subprocess.CalledProcessError as e:
            error_msg = _("compress.error.tar_command_failed", error=str(e))
            log(error_msg, "error")
            return False
            
    except Exception as e:
        error_msg = _("compress.error.create_config_tar_gz", error=str(e))
        log(error_msg, "error")
        return False
    finally:
        # 清理临时目录
        if temp_dir and os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)

def encrypt_directory(input_dir, output_file):
    """加密目录并生成最终的backup-startup文件
    
    Args:
        input_dir: 输入目录路径
        output_file: 输出文件路径
        
    Returns:
        bool: 操作是否成功
    """
    # 检查输出文件是否存在，如果存在则删除
    if os.path.exists(output_file):
        try:
            os.remove(output_file)
            log(_("compress.deleted_existing_output"), file=output_file)
        except Exception as e:
            error_msg = _("compress.error.cannot_delete_output", error=str(e))
            log(error_msg, "error")
            return False
            
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    try:
        # 创建Config.tar.gz
        config_tar_gz = os.path.join(temp_dir, "Config.tar.gz")
        if not create_config_tar_gz(input_dir, config_tar_gz):
            return False
            
        # 初始化API
        api = CompressionAPI()
        
        # 设置加密输出文件路径
        ver_file = os.path.join(temp_dir, "Config_backup.ver")
        encrypted_file = os.path.join(temp_dir, "Config.tar.gz.en")
        
        # 执行加密操作
        result = api.cm_file_encrypt(config_tar_gz, encrypted_file, ver_file, 1)
        
        if result != 0:
            log(_("compress.error.encryption_failed"), "error", code=result)
            log(_("compress.error.encryption_abort"), "error")
            return False
            
        # 检查加密后的文件是否存在且有效
        if not os.path.exists(encrypted_file) or os.path.getsize(encrypted_file) == 0:
            log(_("compress.error.encrypted_file_invalid"), "error")
            return False
            
        if not os.path.exists(ver_file) or os.path.getsize(ver_file) == 0:
            log(_("compress.error.version_file_invalid"), "error")
            return False
            
        # 直接使用tarfile模块创建tar文件
        try:
            with tarfile.open(output_file, "w") as tar:
                tar.add(ver_file, arcname=os.path.basename(ver_file))
                tar.add(encrypted_file, arcname=os.path.basename(encrypted_file))
                
            # 验证生成的文件是否有效
            if not os.path.exists(output_file) or os.path.getsize(output_file) == 0:
                log(_("compress.error.output_file_invalid"), "error")
                return False
                
            log(_("compress.encryption_success"), file=output_file)
            return True
            
        except Exception as e:
            error_msg = _("compress.error.create_tar_file", error=str(e))
            log(error_msg, "error")
            return False
        
    except Exception as e:
        error_msg = _("compress.error.encryption_process", error=str(e))
        log(error_msg, "error")
        return False
    finally:
        # 清理临时目录
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
            log(_("compress.cleanup_temp_dir"), dir=temp_dir)

def main():
    # 检查命令行参数
    if len(sys.argv) < 3:
        print(_("compress.usage.title"))
        print(_("compress.usage.encrypt"))
        print(_("compress.usage.decrypt"))
        print(_("compress.usage.examples"))
        print(_("compress.usage.encrypt_example"))
        print(_("compress.usage.decrypt_example"))
        sys.exit(1)
    
    # 解析命令行参数
    action = sys.argv[1].lower()
    
    if action == "encrypt":
        input_dir = sys.argv[2]
        # 生成带日期和时间的输出文件名
        date_str = datetime.datetime.now().strftime("%Y%m%d-%H%M")
        output_file = sys.argv[3] if len(sys.argv) > 3 else f"backup-startup-{date_str}.tar.gz"
        
        if not encrypt_directory(input_dir, output_file):
            log(_("compress.error.encryption_failed"), "error")
            sys.exit(1)
        log(_("compress.backup_startup_created"), file=output_file)
            
    elif action == "decrypt":
        tar_gz_path = sys.argv[2]
        output_dir = sys.argv[3] if len(sys.argv) > 3 else "output"
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 处理.tar.gz文件
        temp_dir, ver_file, encrypted_file = process_tar_gz_file(tar_gz_path)
        if not all([temp_dir, ver_file, encrypted_file]):
            sys.exit(1)
        
        # 初始化API
        api = CompressionAPI()
        
        # 设置输出文件路径
        decrypted_file = os.path.join(output_dir, "Config.tar.gz")
        
        # 执行解密操作
        try:
            log(_("compress.decrypting_file"), src=encrypted_file, dst=decrypted_file)
            log(_("compress.using_version_file"), file=ver_file)
            result = api.cm_file_decrypt(encrypted_file, decrypted_file, ver_file, 0)
            
            # 检查结果
            if result == 0:
                log(_("compress.decryption_complete"))
                log(_("compress.decrypted_file_saved"), file=decrypted_file)
            else:
                log(_("compress.error.decryption_failed"), "error", code=result)
        
        except Exception as e:
            error_msg = _("compress.error.during_decryption", error=str(e))
            log(error_msg, "error")
        finally:
            # 清理临时目录
            shutil.rmtree(temp_dir)
    else:
        log(_("compress.error.unknown_action"), "error", action=action)
        sys.exit(1)

if __name__ == "__main__":
    main()