#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import json
import re
import sys
from engine.utils.logger import log, user_log, flush_all_logs
from engine.utils.yang_validator import validate_gracefully
from engine.utils.interface_validator import validate_interface
from engine.parsers.fortigate_parser import FortigateParser
from engine.generators.yang_generator import generate_xml
from engine.generators.interface_handler import process_fortinet_interfaces
from engine.generators.ntos_generator import NTOSGenerator
import datetime
from engine.utils.i18n import _, translate, init_i18n, set_debug_mode  # 导入国际化函数
from engine.utils.encoding_fix import setup_encoding

# 新架构组件导入
try:
    from engine.application.conversion_service import ConversionService
    NEW_ARCHITECTURE_AVAILABLE = True
except ImportError:
    NEW_ARCHITECTURE_AVAILABLE = False

# 设置正确的编码
setup_encoding()

# 支持的厂商解析器
SUPPORTED_VENDORS = {
    "fortigate": FortigateParser,
    # 未来可以添加更多厂商解析器，取消注释或添加新条目:
    # "cisco": None,  # 示例，需要实现对应的解析器
}








def setup_file_logging(log_file):
    """
    设置文件日志
    
    Args:
        log_file (str): 日志文件路径
    """
    from engine.utils.logger import setup_file_logging
    setup_file_logging(log_file)
    log(_("info.log_file_created"), file=log_file)

def setup_user_logging(log_file):
    """
    设置用户日志
    
    Args:
        log_file (str): 用户日志文件路径
    """
    from engine.utils.logger import setup_user_logging
    setup_user_logging(log_file)
    log(_("info.user_log_file_created"), file=log_file)

def get_template_path(model, version):
    """
    根据设备型号和版本返回对应的模板路径
    
    Args:
        model (str): 设备型号，如 'z5100s'
        version (str): 设备版本，如 'R10P2'
    
    Returns:
        str: 模板文件的路径
        
    Raises:
        FileNotFoundError: 当模板文件不存在时抛出异常
    """
    # 构建模板路径 - 使用绝对路径
    # 先获取当前脚本目录作为基准点（engine目录）
    current_file_dir = os.path.dirname(os.path.abspath(__file__))
      # 检查是否在容器环境中运行（通过检查是否存在 /app/engine 目录）
    if os.path.exists('/app/engine') and current_file_dir.startswith('/app'):
        # 容器环境：使用固定的 /app/engine 路径
        engine_dir = '/app/engine'
        log(_("info.detected_container_environment"), dir=engine_dir)
    else:
        # 开发环境：当前脚本在engine目录下，直接使用
        # current_file_dir 已经是 engine 目录
        engine_dir = current_file_dir
        log(_("info.detected_dev_environment"), dir=engine_dir)
    
    template_path = os.path.join(engine_dir, "data", "input", "configData", model, version, "Config", "running.xml")
    
    # 打印诊断信息
    log(_("info.current_working_dir"), dir=engine_dir)
    log(_("info.template_absolute_path"), path=template_path)
    
    # 检查目录是否存在
    template_dir = os.path.dirname(template_path)
    if not os.path.exists(template_dir):
        log(_("warning.template_dir_not_exists"), dir=template_dir)
        # 尝试创建目录
        try:
            os.makedirs(template_dir, exist_ok=True)
            log(_("info.template_dir_created"), dir=template_dir)
        except Exception as e:
            log(_("error.create_dir_failed"), dir=template_dir, error=str(e))
    
    # 检查模板是否存在
    if not os.path.exists(template_path):
        log(_("error.template_file_not_exists"), "error", path=template_path)
        # 尝试在相邻位置寻找同名文件
        parent_dir = os.path.dirname(os.path.dirname(template_path))
        if os.path.exists(parent_dir):
            log(_("info.checking_parent_dir"), dir=parent_dir)
            for root, dirs, files in os.walk(parent_dir):
                for file in files:
                    if file == "running.xml":
                        log(_("info.possible_template_found"), path=os.path.join(root, file))
        
        # 查找整个数据目录
        data_dir = os.path.join(engine_dir, "data")
        if os.path.exists(data_dir):
            log(_("info.searching_data_dir"), dir=data_dir)
            found_files = []
            for root, dirs, files in os.walk(data_dir):
                for file in files:
                    if file == "running.xml":
                        found_files.append(os.path.join(root, file))
            if found_files:
                log(_("info.found_template_files"), count=len(found_files))
                for f in found_files:
                    log(_("info.found_template_file"), path=f)                # 使用第一个找到的文件作为替代
                alt_path = found_files[0]
                log(_("info.using_alternative_template"), path=alt_path)
                return alt_path
        
        raise FileNotFoundError(_("error.template_file_not_exists", path=template_path))
    
    log(_("info.using_template"), file=template_path)
    return template_path

# 添加一个辅助函数来创建国际化的转换统计数据结构
def create_i18n_stats_structure():
    """
    创建使用国际化键的统计数据结构
    
    Returns:
        dict: 包含国际化键的统计数据结构
    """
    # 定义统计字段的键名
    total_key = _("stats.total")
    success_key = _("stats.success")
    failed_key = _("stats.failed")
    skipped_key = _("stats.skipped")
    details_key = _("stats.details")
    
    # 创建统计数据结构
    return {
        "interfaces": {
            total_key: 0,
            success_key: 0,
            failed_key: 0,
            skipped_key: 0,
            details_key: []
        },
        "zones": {
            total_key: 0,
            success_key: 0,
            failed_key: 0,
            skipped_key: 0,
            details_key: []
        },
        "policies": {
            total_key: 0,
            success_key: 0,
            failed_key: 0,
            skipped_key: 0,
            details_key: []
        },
        "addresses": {
            total_key: 0,
            success_key: 0,
            failed_key: 0,
            skipped_key: 0,
            details_key: []
        },
        "address_groups": {
            total_key: 0,
            success_key: 0,
            failed_key: 0,
            skipped_key: 0,
            details_key: []
        },
        "services": {
            total_key: 0,
            success_key: 0,
            failed_key: 0,
            skipped_key: 0,
            details_key: []
        }
    }

def convert_config(
    cli_file, mapping_file="data/mappings/interface_mapping.json",
    model="z5100s", version="R10P2", output_file="data/output/result.xml",
    vendor="fortigate", encrypt_output=None, engine_debug_log=None, engine_user_log=None, verbose=True, language=None,
    use_new_architecture=None
):
    """
    将厂商配置转换为NTOS格式

    Args:
        cli_file (str): 配置文件路径
        mapping_file (str): 接口映射文件路径
        model (str): 设备型号
        version (str): 设备版本
        output_file (str): 输出文件路径
        vendor (str): 厂商标识，当前支持: {', '.join(SUPPORTED_VENDORS.keys())}
        encrypt_output (str, optional): 加密输出文件路径
        engine_debug_log (str, optional): 引擎调试日志文件路径
        engine_user_log (str, optional): 引擎用户日志文件路径
        verbose (bool, optional): 是否打印详细日志
        language (str, optional): 国际化语言设置
        use_new_architecture (bool, optional): 是否使用新架构，None表示自动选择

    Returns:
        dict: 转换结果，包含success, error, message
    """

    # 新架构选择逻辑 - 基于架构配置文件
    if use_new_architecture is None:
        # 读取架构配置文件
        try:
            # json和os模块已在文件顶部导入，无需重复导入
            config_path = os.path.join(os.path.dirname(__file__), "config", "architecture_config.json")
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    arch_config = json.load(f)

                new_arch_config = arch_config.get("new_architecture", {})

                # 检查是否强制使用传统架构
                if new_arch_config.get("force_legacy", False):
                    use_new_architecture = False
                    log(_("info.force_legacy_architecture"), "info", reason=new_arch_config.get("disable_reason", "Configuration setting"))
                # 检查新架构是否启用
                elif not new_arch_config.get("enabled", True):
                    use_new_architecture = False
                    log(_("info.new_architecture_disabled"), "info", reason=new_arch_config.get("disable_reason", "Configuration setting"))
                else:
                    # 新架构启用，检查可用性
                    use_new_architecture = NEW_ARCHITECTURE_AVAILABLE
                    if not NEW_ARCHITECTURE_AVAILABLE:
                        log(_("warning.new_architecture_not_available"), "warning")
            else:
                # 配置文件不存在，使用默认逻辑
                use_new_architecture = NEW_ARCHITECTURE_AVAILABLE
                if not NEW_ARCHITECTURE_AVAILABLE:
                    log(_("warning.new_architecture_not_available"), "warning")
        except Exception as e:
            error_msg = _("warning.architecture_config_error", error=str(e))
            log(error_msg, "warning")
            # 配置读取失败，使用默认逻辑
            use_new_architecture = NEW_ARCHITECTURE_AVAILABLE
            if not NEW_ARCHITECTURE_AVAILABLE:
                log(_("warning.new_architecture_not_available"), "warning")

    if use_new_architecture and NEW_ARCHITECTURE_AVAILABLE:
        log(_("info.using_new_architecture"), "info")
        try:
            # 使用新架构进行转换
            conversion_service = ConversionService()
            return conversion_service.convert(
                cli_file=cli_file,
                mapping_file=mapping_file,
                model=model,
                version=version,
                output_file=output_file,
                vendor=vendor,
                encrypt_output=encrypt_output,
                engine_debug_log=engine_debug_log,
                engine_user_log=engine_user_log,
                verbose=verbose,
                language=language
            )
        except Exception as e:
            import traceback
            error_msg = _("warning.new_architecture_failed", error=str(e))
            log(error_msg, "error")

            # 记录详细的错误堆栈信息
            stack_trace = traceback.format_exc()
            log(f"新架构失败详细信息:\n{stack_trace}", "error")

            # 记录新架构失败的具体阶段
            log(f"新架构失败时的参数: cli_file={cli_file}, model={model}, version={version}", "error")

            log(_("info.fallback_to_legacy"), "info")
            # 如果新架构失败，回退到传统架构

    # 使用传统架构（原有逻辑）
    log(_("info.using_legacy_architecture"), "info")
    try:
        # 设置日志文件
        if not engine_debug_log:
            # 确保日志目录存在
            log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'logs')
            os.makedirs(log_dir, exist_ok=True)
            
            # 创建日志文件名
            timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
            engine_debug_log = os.path.join(log_dir, f'debug_{timestamp}.log')
        
        # 设置调试日志
        setup_file_logging(engine_debug_log)
        log(_("info.engine_debug_log_created"), file=engine_debug_log)
        
        # 设置用户日志文件
        if not engine_user_log:
            log_dir = os.path.dirname(engine_debug_log)
            engine_user_log = os.path.join(log_dir, f'conversion_summary_{datetime.datetime.now().strftime("%Y%m%d%H%M%S")}.log')
        
        # 设置用户日志
        setup_user_logging(engine_user_log)
        log(_("info.engine_user_log_created"), file=engine_user_log)
        
        # 初始化用户日志格式化器
        from engine.utils.user_log_formatter import get_user_formatter
        user_formatter = get_user_formatter()
        user_formatter.start_conversion(vendor, model, version)
        
        # 初始化国际化设置
        if language:
            init_i18n(language)
            log(_("info.language_set"), language=language)
        else:
            # 默认使用中文
            init_i18n('zh-CN')
            log(_("info.using_default_language"), language='zh-CN')
        
        # 设置调试模式
        set_debug_mode(verbose)
        
        # 验证输出文件路径
        if not output_file or output_file.strip() == "":
            error_msg = _("error.invalid_output_file_path")
            log(error_msg, "error")
            user_log(error_msg, "error")
            return {
                "success": False,
                "error": error_msg,
                "message": error_msg
            }
        
        # 修复常见的路径错误
        cli_file = fix_common_path_errors(cli_file)
        mapping_file = fix_common_path_errors(mapping_file)
        output_file = fix_common_path_errors(output_file)
        
        # 初始化转换统计数据
        conversion_stats = create_i18n_stats_structure()
        
        # 收集需要人工配置的项目
        manual_config_items = []
        
        # 兼容性问题列表
        compatibility_issues = []
        
        # 记录日志设置
        if engine_debug_log:
            if not os.path.exists(os.path.dirname(engine_debug_log)):
                os.makedirs(os.path.dirname(engine_debug_log), exist_ok=True)
            setup_file_logging(engine_debug_log)
            log(_("info.engine_debug_log_created"), file=engine_debug_log)
        
        if engine_user_log:
            if not os.path.exists(os.path.dirname(engine_user_log)):
                os.makedirs(os.path.dirname(engine_user_log), exist_ok=True)
            setup_user_logging(engine_user_log)
            log(_("info.engine_user_log_created"), file=engine_user_log)
        
        # 读取配置文件
        try:
            with open(cli_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            log(_("info.config_file_read_success"), size=len(content))
            user_log(_("user.info.config_file_read_success"))
        except Exception as e:
            error_msg = _("error.config_file_read_failed", error=str(e))
            log(error_msg, "error")
            user_log(_("user.error.file_not_exists"), "error", file=cli_file)
            return {
                "success": False,
                "error": _("error.config_file_read_failed", error=str(e)),
                "message": _("error.cannot_read_config")
            }
        
        # 加载接口映射文件
        try:
            with open(mapping_file, 'r', encoding='utf-8') as f:
                mapping_data = json.load(f)
            log(_("info.mapping_file_loaded"), count=len(mapping_data))
            user_log(_("user.info.mapping_file_loaded"))
        except Exception as e:
            error_msg = _("error.mapping_file_load_failed", error=str(e))
            log(error_msg, "error")
            user_log(_("user.error.file_not_exists"), "error", file=mapping_file)
            return {
                "success": False,
                "error": _("error.mapping_file_load_failed", error=str(e)),
                "message": _("error.cannot_load_mapping_file")
            }
        
        # 检查厂商是否支持
        if vendor.lower() not in SUPPORTED_VENDORS:
            supported = list(SUPPORTED_VENDORS.keys())
            error_msg = _("error.vendor_not_supported", vendor=vendor, supported=", ".join(supported))
            log(_("error.vendor_not_supported"), "error", vendor=vendor, supported=", ".join(supported))
            user_log(_("user.error.vendor_not_supported"), "error", vendor=vendor)
            return {
                "success": False,
                "error": error_msg,
                "message": _("error.unsupported_vendor")
            }
        
        # 获取对应的解析器类
        parser_class = SUPPORTED_VENDORS[vendor]
        log(_("info.using_parser"), vendor=vendor.capitalize())
        user_log(_("user.info.start_parsing"), vendor=vendor.capitalize())
        
        # 创建解析器
        parser = parser_class()
        
        # 解析配置
        try:
            log(_("info.parsing_config_start"))
            user_log(_("user.info.parsing_config_start"))
            config_data = parser.parse(cli_file)
            log(_("info.parsing_config_complete"))
            user_log(_("user.info.parsing_config_complete"))
        except Exception as e:
            error_msg = _("error.parsing_config_failed", error=str(e))
            log(error_msg, "error")
            user_log(_("user.error.parsing_config_failed"), "error")
            return {
                "success": False,
                "error": _("error.parsing_config_failed", error=str(e)),
                "message": _("error.cannot_parse_config")
            }
        
        # 检查是否为透明模式并进行相应处理
        transparent_mode_result = None
        system_settings = config_data.get("system_settings", {})
        if system_settings:
            log(_("info.processing_transparent_mode"))
            try:
                from engine.processors.transparent_mode_processor import create_transparent_mode_processor
                transparent_processor = create_transparent_mode_processor(model, mapping_data)
                transparent_mode_result = transparent_processor.process_transparent_mode(system_settings)

                if transparent_mode_result:
                    log(_("info.transparent_mode_detected"))
                    user_log(_("user.info.transparent_mode_detected"))

                    # 记录透明模式配置信息
                    mgmt_interface = transparent_mode_result.get("mgmt_interface")
                    bridge_count = len(transparent_mode_result.get("interface_classification", {}).get("bridge_interfaces", []))
                    log(_("info.transparent_mode_config"), mgmt=mgmt_interface, bridge_count=bridge_count)
                    user_log(_("user.info.transparent_mode_config"), mgmt=mgmt_interface, bridge_count=bridge_count)
                else:
                    log(_("info.nat_mode_detected"))
                    user_log(_("user.info.nat_mode_detected"))

            except Exception as e:
                error_msg = _("error.transparent_mode_processing_failed", error=str(e))
                log(error_msg, "error")
                user_log(_("user.error.transparent_mode_processing_failed"), "error")
                # 继续使用NAT模式处理
                transparent_mode_result = None

        # 检查是否为透明模式并进行相应处理
        transparent_mode_result = None
        system_settings = config_data.get("system_settings", {})
        if system_settings:
            log(_("info.processing_transparent_mode"))
            try:
                from engine.processors.transparent_mode_processor import create_transparent_mode_processor
                transparent_processor = create_transparent_mode_processor(model, mapping_data)
                transparent_mode_result = transparent_processor.process_transparent_mode(system_settings)

                if transparent_mode_result:
                    log(_("info.transparent_mode_detected"))
                    user_log(_("user.info.transparent_mode_detected"))

                    # 记录透明模式配置信息
                    mgmt_interface = transparent_mode_result.get("mgmt_interface")
                    bridge_count = len(transparent_mode_result.get("interface_classification", {}).get("bridge_interfaces", []))
                    log(_("info.transparent_mode_config"), mgmt=mgmt_interface, bridge_count=bridge_count)
                    user_log(_("user.info.transparent_mode_config"), mgmt=mgmt_interface, bridge_count=bridge_count)
                else:
                    log(_("info.nat_mode_detected"))
                    user_log(_("user.info.nat_mode_detected"))

            except Exception as e:
                error_msg = _("error.transparent_mode_processing_failed", error=str(e))
                log(error_msg, "error")
                user_log(_("user.error.transparent_mode_processing_failed"), "error")
                # 继续使用NAT模式处理
                transparent_mode_result = None
        else:
            # 没有system_settings配置，默认为NAT模式
            log(_("info.no_system_settings_nat_mode"))
            user_log(_("user.info.no_system_settings_nat_mode"))

        # 检查配置与目标设备的兼容性
        log(_("info.checking_compatibility"))
        compatibility_issues = check_compatibility(config_data, vendor, model, version)
        
        # 提取和处理接口配置
        interfaces = config_data.get("interfaces", [])
        log(_("info.interfaces_extracted"), count=len(interfaces))
        user_log(_("user.info.interfaces_extracted"), count=len(interfaces))

        # 验证接口映射
        log(_("info.validating_interface_mapping"))
        is_valid_mapping, invalid_mappings, warnings = validate_interface_mapping(interfaces, mapping_data, model)
        if not is_valid_mapping:
            log(_("error.interface_mapping_validation_failed"), "error", count=len(invalid_mappings))
            user_log(_("user.error.interface_mapping_validation_failed"), "error", count=len(invalid_mappings))
            
            # 如果是设备型号验证失败，添加更具体的错误信息
            model_validation_failed = any("不在设备型号" in mapping["reason"] for mapping in invalid_mappings)
            if model_validation_failed:
                log(_("error.interface_model_validation_failed"), "error", count=len(invalid_mappings))
                user_log(_("user.error.interface_model_validation_failed"), "error", count=len(invalid_mappings))
            
            # 返回错误信息
            return {
                "success": False,
                "error": _("error.interface_mapping_validation_failed", count=len(invalid_mappings)),
                "message": _("error.check_interface_mapping"),
                "invalid_mappings": invalid_mappings
            }

        # 处理接口映射
        log(_("info.processing_interface_mapping"))
        interface_mapping, all_physical_unmapped = process_interface_mapping(interfaces, mapping_data)
        log(_("info.interface_mapping_complete"), count=len(interface_mapping))

        # 设置当前输出文件路径，供其他模块使用
        from engine.utils.file_utils import set_current_output_file_path, get_task_data_directory
        set_current_output_file_path(output_file)

        # 保存最终的接口映射文件，供策略和NAT模块使用
        from engine.utils.interface_mapper import save_final_interface_mapping

        # 获取任务数据目录
        task_data_dir = get_task_data_directory(output_file)
        final_mapping_file = save_final_interface_mapping(interface_mapping, task_data_dir)
        if final_mapping_file:
            log(_("info.final_interface_mapping_file_saved"), file=final_mapping_file)
        
        # 提取和处理区域配置
        zones = config_data.get("zones", [])
        log(_("info.zones_extracted"), count=len(zones))
        user_log(_("user.info.zones_extracted"), count=len(zones))
        
        # 映射区域中的接口名称
        zones = process_zone_interface_mapping(zones, interface_mapping)
        log(_("info.zone_interface_mapping_complete"))
        
        # 处理策略规则
        policy_result = process_policy_rules(config_data, interface_mapping, version)
        log(_("info.policy_rules_processed"), success=len(policy_result['converted']), failed=len(policy_result['failed']))
        user_log(_("user.info.policy_rules_processed"), success=len(policy_result['converted']))
        
        # 处理静态路由
        routes_result = process_static_routes(config_data, interface_mapping)
        log(_("info.static_routes_processed"), success=len(routes_result['converted']), failed=len(routes_result['failed']))
        user_log(_("user.info.static_routes_processed"), success=len(routes_result['converted']))
        
        # 处理地址对象
        address_result = process_address_objects(config_data)
        log(_("info.address_objects_processed"), 
            success=len(address_result['converted']), 
            failed=len(address_result['failed']), 
            skipped=len(address_result['skipped']),
            total=len(address_result['converted']) + len(address_result['failed']) + len(address_result['skipped']))
        user_log(_("user.info.address_objects_processed"), 
                success=len(address_result['converted']), 
                failed=len(address_result['failed']), 
                skipped=len(address_result['skipped']),
                total=len(address_result['converted']) + len(address_result['failed']) + len(address_result['skipped']))
        
        # 处理地址组对象
        address_group_result = process_address_groups(config_data)
        log(_("info.address_groups_processed"), 
            success=len(address_group_result['converted']), 
            failed=len(address_group_result['failed']), 
            skipped=len(address_group_result['skipped']),
            total=len(address_group_result['converted']) + len(address_group_result['failed']) + len(address_group_result['skipped']))
        user_log(_("user.info.address_groups_processed"), 
                success=len(address_group_result['converted']), 
                failed=len(address_group_result['failed']), 
                skipped=len(address_group_result['skipped']),
                count=len(address_group_result['converted']),
                total=len(address_group_result['converted']) + len(address_group_result['failed']) + len(address_group_result['skipped']))
        
        # 处理服务对象
        service_result = process_service_objects(config_data)

        # 添加详细的调试日志
        log(_("debug.service_objects_processing_details"), "debug",
            converted=len(service_result['converted']),
            failed=len(service_result['failed']),
            skipped=len(service_result.get('skipped', [])))

        log(_("info.service_objects_processed"), success=len(service_result['converted']), failed=len(service_result['failed']))
        user_log(_("user.info.service_objects_processed"), success=len(service_result['converted']))
        
        # 处理服务组对象，传入服务对象处理结果以支持预定义服务映射
        service_group_result = process_service_groups(config_data, service_result)
        log(_("info.service_groups_processed"), success=len(service_group_result['converted']), failed=len(service_group_result['failed']))
        user_log(_("user.info.service_groups_processed"), count=len(service_group_result['converted']))
        
        # 处理时间对象
        time_range_result = process_time_ranges(config_data)
        log(_("info.time_ranges_processed"), success=len(time_range_result['converted']), failed=len(time_range_result['failed']))
        user_log(_("user.info.time_ranges_processed"), count=len(time_range_result['converted']))
        
        # 检查兼容性问题
        compatibility_issues = check_compatibility(config_data, vendor, model, version)
        if compatibility_issues:
            log(_("warning.compatibility_issues_detected"), count=len(compatibility_issues))
            user_log(_("user.warning.compatibility_issues_detected"), "warning", count=len(compatibility_issues))
        
        # 生成需要人工配置的项目列表
        manual_config_items = []
        if policy_result["failed"]:
            manual_config_items.append({
                "类型": _("config_type.policy_rules"),
                "数量": len(policy_result["failed"]),
                "原因": _("reason.unsupported_rule_type"),
                "建议": _("suggestion.check_policy_type")
            })
        
        if routes_result["failed"]:
            manual_config_items.append({
                "类型": _("config_type.static_routes"),
                "数量": len(routes_result["failed"]),
                "原因": _("reason.unsupported_route_type"),
                "建议": _("suggestion.check_route_type")
            })
        
        # 检查是否需要生成NTOS配置
        if all_physical_unmapped:
            log(_("warning.all_physical_interfaces_unmapped"), "warning")
            user_log(_("user.warning.all_physical_interfaces_unmapped"), "warning")
            
            # 生成报告
            report_file = os.path.join(os.path.dirname(output_file), "conversion_report.json")
            report_data = {
                _("report.timestamp"): datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                _("report.source_file"): os.path.basename(cli_file),
                _("report.vendor"): vendor,
                _("report.target_device"): f"{model} {version}",
                _("report.conversion_stats"): conversion_stats,
                _("report.compatibility_issues"): compatibility_issues,
                _("report.manual_config_items"): manual_config_items
            }
            
            # 保存报告文件
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, ensure_ascii=False, indent=2)
            log(_("info.report_saved"), file=report_file)
            
            # 在函数结束前刷新所有日志
            flush_all_logs()
            
            return {
                "success": False,
                "error": _("error.all_physical_interfaces_unmapped"),
                "message": _("error.check_interface_config"),
                "report_file": report_file
            }
        
        # 处理NTOS转换
        try:
            # 创建配置模板路径
            log(_("info.getting_template_path"), model=model, version=version)
            template_path = get_template_path(model, version)
            if not template_path:
                error_msg = _("error.template_not_found", model=model, version=version)
                log(_("error.template_not_found"), "error", model=model, version=version)
                user_log(_("user.error.template_not_found"), "error", model=model, version=version)
                
                return {
                    "success": False,
                    "error": error_msg,
                    "message": _("error.cannot_get_template")
                }
            
            log(_("info.using_template"), file=template_path)
            
            # 检查是否有DNS配置，如果有则使用扩展生成器
            has_dns_config = "dns_config" in config_data and config_data["dns_config"]

            if has_dns_config:
                # 导入扩展生成器
                from engine.generators.ntos_generator_extensions import ExtendedNTOSGenerator
                ntos_generator = ExtendedNTOSGenerator(model, version, custom_template_path=None, transparent_mode_result=transparent_mode_result)
                log(_("info.using_extended_generator_for_dns"))
            else:
                # 使用普通生成器
                ntos_generator = NTOSGenerator(model, version, transparent_mode_result=transparent_mode_result)
                log(_("info.using_standard_generator"))
            
            # 创建简单的名称映射字典用于接口名称映射
            simple_interface_mapping = {}
            for original_name, interface_config in interface_mapping.items():
                if isinstance(interface_config, dict) and "name" in interface_config:
                    simple_interface_mapping[original_name] = interface_config["name"]
                else:
                    simple_interface_mapping[original_name] = interface_config

            # 为所有接口设置正确的工作模式
            interface_list = list(interface_mapping.values())
            for interface_config in interface_list:
                if isinstance(interface_config, dict):
                    # 根据透明模式结果设置工作模式
                    if transparent_mode_result:
                        # 透明模式：使用透明模式处理器确定工作模式
                        interface_name = interface_config.get("name", "")
                        working_mode = transparent_processor.get_interface_working_mode(interface_name, transparent_mode_result)
                        interface_config["working_mode"] = working_mode
                        log("透明模式 - 接口 {interface_name} 工作模式设置为: {working_mode}", "debug")
                    else:
                        # 路由模式：所有接口都使用路由模式
                        interface_config["working_mode"] = "route"
                        interface_name = interface_config.get("name", "")
                        log("路由模式 - 接口 {interface_name} 工作模式设置为: route", "debug")

            # 直接使用原始数据，不再进行字段过滤
            ntos_generator.add_interfaces(interface_list, simple_interface_mapping)
            ntos_generator.add_address_objects(address_result["converted"])
            ntos_generator.add_address_groups(address_group_result["converted"])
            ntos_generator.add_service_objects(service_result["converted"])
            ntos_generator.add_service_groups(service_group_result["converted"])
            ntos_generator.add_policy_rules(policy_result["converted"])

            # 传递服务映射关系到NTOS生成器
            if "mapping_relationships" in service_result:
                ntos_generator.set_service_mapping_relationships(service_result["mapping_relationships"])

            ntos_generator.add_zones(zones)
            
            # 添加时间对象
            if "add_time_ranges" in dir(ntos_generator) and callable(getattr(ntos_generator, "add_time_ranges")):
                log(_("info.adding_time_ranges"), count=len(time_range_result['converted']))
                ntos_generator.add_time_ranges(time_range_result["converted"])
            elif hasattr(ntos_generator, "set_time_ranges") and callable(getattr(ntos_generator, "set_time_ranges")):
                log(_("info.setting_time_ranges_legacy"), count=len(time_range_result['converted']))
                ntos_generator.set_time_ranges(time_range_result["converted"])
            else:
                log(_("warning.time_ranges_not_supported"), "warning")
                user_log(_("warning.time_ranges_not_supported"), "warning")

            # 添加NAT规则（如果有）
            enhanced_nat_rules = config_data.get("enhanced_nat_rules", [])
            enhanced_nat_pools = config_data.get("enhanced_nat_pools", [])

            # 合并NAT规则和NAT池
            all_nat_items = enhanced_nat_rules + enhanced_nat_pools

            if all_nat_items:
                if hasattr(ntos_generator, "set_nat_rules") and callable(getattr(ntos_generator, "set_nat_rules")):
                    log(_("info.setting_nat_rules"), count=len(all_nat_items))
                    ntos_generator.set_nat_rules(all_nat_items)
                    user_log(_("user.info.nat_rules_added"), count=len(enhanced_nat_rules), pools=len(enhanced_nat_pools))
                else:
                    log(_("warning.nat_rules_not_supported"), "warning")
                    user_log(_("warning.nat_rules_not_supported"), "warning")
            else:
                log(_("info.no_nat_rules_found"))
                user_log(_("user.info.no_nat_rules_found"))
            
            # 添加静态路由
            if "add_static_routes" in dir(ntos_generator) and callable(getattr(ntos_generator, "add_static_routes")):
                log(_("info.adding_static_routes"), count=len(routes_result['converted']))
                ntos_generator.add_static_routes(routes_result["converted"], routes_result.get("converted_ipv6", []))
            else:
                log(_("warning.static_routes_not_supported"), "warning")
                user_log(_("warning.static_routes_not_supported"), "warning")

            # 添加DNS配置（如果使用扩展生成器且有DNS配置）
            if has_dns_config and hasattr(ntos_generator, "add_dns_config"):
                log(_("info.adding_dns_config"))
                ntos_generator.add_dns_config(config_data["dns_config"])
                user_log(_("user.info.dns_config_added"))
            
            # 生成XML内容 - 接收三元组 (XML内容, 统计信息, 验证报告)
            generate_result = ntos_generator.generate()
            
            # 处理不同的返回格式以确保向后兼容性
            if len(generate_result) == 3:
                # 新格式：(xml_content, conversion_stats, validation_report)
                xml_content, conversion_stats, validation_report = generate_result
                log(_("info.received_enhanced_generator_output"))
            elif len(generate_result) == 2:
                # 旧格式：(xml_content, conversion_stats)
                xml_content, conversion_stats = generate_result
                validation_report = {
                    "errors": 0,
                    "warnings": 0,
                    "is_valid": True,
                    "mode": "legacy_compatibility"
                }
                log(_("info.received_legacy_generator_output"))
            else:
                # 异常情况
                raise ValueError(f"生成器返回了意外的结果格式: {len(generate_result)} 个元素")
            
            # 更新转换统计信息
            interfaces_stats = conversion_stats.get("interfaces", {})
            zones_stats = conversion_stats.get("zones", {})
            
            # 获取跳过的区域接口信息
            skipped_zone_interfaces = zones_stats.get("skipped_interfaces", [])
            
            # 预处理skipped_zone_interfaces，确保所有元素都是字典类型
            processed_skipped_interfaces = []
            for intf in skipped_zone_interfaces:
                if isinstance(intf, dict):
                    processed_skipped_interfaces.append(intf)
                elif isinstance(intf, str):
                    processed_skipped_interfaces.append({
                        "zone": "未知",
                        "interface": intf,
                        "reason": "未找到接口映射"
                    })
                # 忽略其他类型的元素
            
            # 替换原始列表
            skipped_zone_interfaces = processed_skipped_interfaces
            
            # 记录无效接口详情
            invalid_interfaces = interfaces_stats.get("invalid_interfaces", [])

            # 预处理invalid_interfaces，确保所有元素都是字典类型
            processed_invalid_interfaces = []
            for intf in invalid_interfaces:
                if isinstance(intf, dict):
                    processed_invalid_interfaces.append(intf)
                elif isinstance(intf, str):
                    processed_invalid_interfaces.append({
                        "name": intf,
                        "reason": "未知原因"
                    })
                # 忽略其他类型的元素

            # 替换原始列表
            invalid_interfaces = processed_invalid_interfaces
            
            # 定义统计字段的键名
            total_key = _("stats.total")
            success_key = _("stats.success")
            failed_key = _("stats.failed")
            skipped_key = _("stats.skipped")
            details_key = _("stats.details")

            # 添加服务对象统计的详细调试信息
            log(_("debug.preparing_service_stats_calculation"), "debug", keys=str(list(service_result.keys())))
            log(_("debug.service_converted_count"), "debug", count=len(service_result['converted']))
            log(_("debug.service_failed_count"), "debug", count=len(service_result['failed']))
            log(_("debug.service_skipped_count"), "debug", count=len(service_result.get('skipped', [])))

            conversion_stats = {
                "interfaces": {
                    total_key: interfaces_stats.get("total", 0),
                    success_key: interfaces_stats.get("processed", 0),
                    failed_key: interfaces_stats.get("skipped", 0),
                    skipped_key: 0,
                    details_key: []
                },
                "zones": {
                    total_key: zones_stats.get("total", 0),
                    success_key: zones_stats.get("processed", 0),
                    failed_key: zones_stats.get("total", 0) - zones_stats.get("processed", 0),
                    skipped_key: zones_stats.get("skipped_interfaces_count", 0),
                    details_key: []
                },
                "static_routes": {
                    total_key: len(routes_result["converted"]) + len(routes_result["failed"]) + len(routes_result["skipped"]),
                    success_key: len(routes_result["converted"]),
                    failed_key: len(routes_result["failed"]),
                    skipped_key: len(routes_result["skipped"]),
                    details_key: routes_result["details"] if "details" in routes_result else []
                },
                "time_ranges": {
                    total_key: len(time_range_result["converted"]) + len(time_range_result["failed"]) + len(time_range_result["skipped"]),
                    success_key: len(time_range_result["converted"]),
                    failed_key: len(time_range_result["failed"]),
                    skipped_key: len(time_range_result["skipped"]),
                    details_key: time_range_result["details"]
                },
                "policies": {
                    total_key: len(policy_result["converted"]) + len(policy_result["failed"]),
                    success_key: len(policy_result["converted"]),
                    failed_key: len(policy_result["failed"]),
                    skipped_key: 0,
                    details_key: []
                },
                "addresses": {
                    total_key: len(address_result["converted"]) + len(address_result["failed"]) + len(address_result["skipped"]),
                    success_key: len(address_result["converted"]),
                    failed_key: len(address_result["failed"]),
                    skipped_key: len(address_result["skipped"]),
                    details_key: []
                },
                "address_groups": {
                    total_key: len(address_group_result["converted"]) + len(address_group_result["failed"]) + len(address_group_result["skipped"]),
                    success_key: len(address_group_result["converted"]),
                    failed_key: len(address_group_result["failed"]),
                    skipped_key: len(address_group_result["skipped"]),
                    details_key: []
                },
                "service_groups": {
                    total_key: len(service_group_result["converted"]) + len(service_group_result["failed"]),
                    success_key: len(service_group_result["converted"]),
                    failed_key: len(service_group_result["failed"]),
                    skipped_key: 0,
                    details_key: []
                },
                "services": {
                    total_key: len(service_result["converted"]) + len(service_result["failed"]) + len(service_result.get("skipped", [])),
                    success_key: len(service_result["converted"]),
                    failed_key: len(service_result["failed"]),
                    skipped_key: len(service_result.get("skipped", [])),
                    details_key: []
                }
            }

            # 添加NAT统计信息（如果存在）
            nat_rules = config_data.get("enhanced_nat_rules", [])
            nat_pools = config_data.get("enhanced_nat_pools", [])

            if nat_rules or nat_pools:
                # 计算NAT规则统计
                dnat_count = 0
                snat_count = 0
                for rule in nat_rules:
                    if "static-dnat44" in rule:
                        dnat_count += 1
                    elif "static-snat44" in rule or "dynamic-snat44" in rule:
                        snat_count += 1

                conversion_stats["nat_rules"] = {
                    total_key: len(nat_rules),
                    success_key: len(nat_rules),  # 假设所有NAT规则都成功转换
                    failed_key: 0,
                    skipped_key: 0,
                    details_key: []
                }

                # 记录详细的NAT统计信息
                log(_("nat.statistics_summary"),
                    nat_rules=len(nat_rules),
                    dnat_rules=dnat_count,
                    snat_rules=snat_count,
                    nat_pools=len(nat_pools))

            # 添加验证信息
            conversion_stats["validation"] = {
                "yang_validation": validation_report.get("is_valid", True),
                "errors": validation_report.get("error_count", 0),
                "warnings": validation_report.get("warning_count", 0),
                "mode": validation_report.get("mode", "unknown"),
                "details": validation_report.get("details", [])
            }

            # 验证服务对象统计计算结果
            services_stats = conversion_stats["services"]
            log(_("debug.service_objects_stats_calculated"), "debug",
                total=services_stats[total_key],
                success=services_stats[success_key],
                failed=services_stats[failed_key],
                skipped=services_stats[skipped_key])

            # 记录无效接口详情
            for intf in invalid_interfaces:
                # 确保intf是一个字典
                if not isinstance(intf, dict):
                    # 如果是字符串，则创建一个字典
                    intf = {
                        "name": intf,
                        "reason": "未知原因"
                    }
                conversion_stats["interfaces"][details_key].append({
                    _("detail.name"): intf.get("name", str(_("common.unknown"))),
                    _("detail.status"): _("status.failed"),
                    _("detail.reason"): intf.get("reason", str(_("common.unknown_reason")))
                })
            
            # 记录跳过的区域接口详情
            for intf in skipped_zone_interfaces:
                # 确保intf是一个字典
                if not isinstance(intf, dict):
                    # 如果是字符串，则创建一个字典
                    intf = {
                        "zone": "未知",
                        "interface": intf,
                        "reason": "未找到接口映射"
                    }
                # 获取reason并确保它已被翻译
                reason = intf.get("reason", "未找到接口映射")
                # 如果reason是一个翻译键，则需要翻译它
                if isinstance(reason, str) and reason.startswith("report."):
                    reason = _(reason)
                
                conversion_stats["zones"][details_key].append({
                    _("detail.zone"): intf.get("zone", _("common.unknown")),
                    _("detail.interface"): intf.get("interface", _("common.unknown")),
                    _("detail.status"): _("status.skipped"),
                    _("detail.reason"): reason
                })
            
            # 如果有无效接口，添加到兼容性问题列表
            if invalid_interfaces:
                compatibility_issues.append({
                    "类型": _("issue.interface_compatibility"),
                    "严重程度": _("severity.warning"),
                    "描述": _("description.interfaces_skipped_due_to_invalid_config", count=len(invalid_interfaces)),
                    "建议": _("suggestion.check_logs_and_fix_interface_config")
                })
                
                # 添加无效接口到需要人工配置的项目
                for intf in invalid_interfaces:
                    manual_config_items.append({
                        "类型": _("config_type.interface"),
                        "名称": intf.get("name", _("common.unknown")),
                        "原因": intf.get("reason", _("reason.invalid_vlan_id_or_compatibility_issue")),
                        "建议": _("suggestion.check_vlan_id_range")
                    })
            
            # 如果有未映射的区域接口，添加到兼容性问题列表
            if skipped_zone_interfaces:
                compatibility_issues.append({
                    "类型": _("issue.zone_interface_mapping"),
                    "严重程度": _("severity.warning"),
                    "描述": _("description.zone_interfaces_skipped_due_to_missing_mapping", count=len(skipped_zone_interfaces)),
                    "建议": _("suggestion.check_zone_interface_names")
                })
                
                # 添加未映射的区域接口到需要人工配置的项目
                # 安全地获取唯一区域名称集合
                unique_zones = set()
                for intf in skipped_zone_interfaces:
                    if isinstance(intf, dict):
                        unique_zones.add(intf.get("zone", "未知"))
                    elif isinstance(intf, str):
                        # 如果是字符串，使用默认区域名
                        unique_zones.add("未知")
                
                for zone_name in unique_zones:
                    # 收集该区域的所有未映射接口
                    zone_intfs = []
                    for intf in skipped_zone_interfaces:
                        if isinstance(intf, dict) and intf.get("zone") == zone_name:
                            zone_intfs.append(intf.get("interface", "未知"))
                        elif isinstance(intf, str) and zone_name == "未知":
                            # 如果接口是字符串，且当前处理"未知"区域
                            zone_intfs.append(intf)
                    suggestion = str(_("suggestion.check_interface_name_or_add_mapping"))
                    reason = str(_("reason.interface_not_found_in_mapping"))
                    type = str(_("config_type.zone_interface"))
                    manual_config_items.append({
                        "类型": type,
                        "区域": zone_name,
                        "接口列表": zone_intfs,
                        "原因": reason,
                        "建议": suggestion
                    })
            
            # 创建输出目录（如果不存在）
            output_dir = os.path.dirname(output_file)
            
            # 如果输出目录为空（相对路径文件名），使用当前工作目录
            if not output_dir or output_dir.strip() == "":
                output_dir = os.getcwd()
                log(_("info.using_current_directory"), dir=output_dir)
                # 更新output_file为绝对路径
                output_file = os.path.join(output_dir, os.path.basename(output_file))
            
            if not os.path.exists(output_dir):
                try:
                    os.makedirs(output_dir, exist_ok=True)
                    log(_("info.output_dir_created"), dir=output_dir)
                except OSError as e:
                    error_msg = _("error.create_output_dir_failed", dir=output_dir, error=str(e))
                    log(error_msg, "error")
                    user_log(error_msg, "error")
                    return {
                        "success": False,
                        "error": error_msg,
                        "message": error_msg
                    }
            
            # 写入输出文件
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(xml_content)
            log(_("info.xml_file_written"), file=output_file)
            user_log(_("user.info.config_file_generated"))
            
            # 验证生成的XML
            validation_result, validation_message = validate_xml(output_file, model=model, version=version)
            if not validation_result:
                # 记录验证失败信息
                log(_("error.xml_validation_failed"), "error", details=validation_message)
                user_log(_("user.error.yang_validation_failed"), "error")
                
                # 生成验证失败的报告
                report_file = os.path.join(output_dir, "conversion_report.json")
                report_data = {
                    _("report.timestamp"): datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    _("report.source_file"): os.path.basename(cli_file),
                    _("report.vendor"): vendor,
                    _("report.target_device"): f"{model} {version}",
                    _("report.conversion_stats"): conversion_stats,
                    _("report.compatibility_issues"): compatibility_issues,
                    _("report.manual_config_items"): manual_config_items,
                    _("report.validation_failed"): {
                        _("report.error_message"): validation_message,
                        _("report.suggestion"): _("suggestion.fix_xml_errors")
                    }
                }
                
                with open(report_file, 'w', encoding='utf-8') as f:
                    json.dump(report_data, f, ensure_ascii=False, indent=2)
                log(_("info.validation_report_saved"), file=report_file)
                
                # 结束转换计时
                user_formatter.end_conversion()
                
                # 在函数结束前刷新所有日志
                flush_all_logs()
                
                return {
                    "success": False,
                    "error": _("error.yang_validation_failed") + ": " + validation_message,
                    "message": _("error.yang_validation_failed"),
                    "report_file": report_file
                }
            
            log(_("info.xml_validation_passed"))
            user_log(_("user.info.yang_validation_passed"))
            
            # 如果指定了加密输出文件路径，则加密输出文件
            if encrypt_output:
                from engine.encrypt import encrypt_config
                # 获取模板路径
                template_path = get_template_path(model, version)
                # 修复函数调用，确保传递所有必需的参数并处理返回值
                try:
                    encrypt_success, encrypt_message = encrypt_config(output_file, template_path, encrypt_output)
                    if encrypt_success:
                        log(_("info.encrypted_file_generated"), file=encrypt_output)
                        user_log(_("user.info.encrypted_file_generated"), file=encrypt_output)
                    else:
                        log(_("error.encryption_failed",
                              message=encrypt_message), "error")
                        user_log(_("user.error.encryption_failed",
                                  message=encrypt_message))

                        # 结束转换计时
                        user_formatter.end_conversion()

                        # 在函数结束前刷新所有日志
                        flush_all_logs()

                        return {
                            "success": False,
                            "error": _("error.encryption_failed") + ": " + encrypt_message,
                            "message": _("error.encryption_failed"),
                            "report_file": report_file
                        }
                except Exception as e:
                    error_msg = f"加密过程发生异常: {str(e)}"
                    error_msg = _("error.encryption_error", error=str(e))
                    log(error_msg, "error")
                    user_log(_("user.error.encryption_error"), error=str(e))

                    # 结束转换计时
                    user_formatter.end_conversion()

                    # 在函数结束前刷新所有日志
                    flush_all_logs()

                    return {
                        "success": False,
                        "error": error_msg,
                        "message": _("error.encryption_error"),
                        "report_file": report_file
                    }
            
            # 保存转换报告
            report_file = os.path.join(output_dir, "conversion_report.json")
            report_data = {
                _("report.timestamp"): datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                _("report.source_file"): os.path.basename(cli_file),
                _("report.vendor"): vendor,
                _("report.target_device"): f"{model} {version}",
                _("report.conversion_stats"): conversion_stats,
                _("report.compatibility_issues"): compatibility_issues,
                _("report.manual_config_items"): manual_config_items
            }
            
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, ensure_ascii=False, indent=2)
            
            # 结束转换计时
            user_formatter.end_conversion()
            
            # 添加转换总结信息到用户日志
            user_log(_("user.summary.conversion_complete"), summary=True)
            user_log("", summary=True)  # 空行
            
            user_log(_("user.summary.conversion_stats"), summary=True)
            user_log("", summary=True)  # 空行
            
            # 定义统计字段的键名
            total_key = _("stats.total")
            success_key = _("stats.success")
            failed_key = _("stats.failed")
            skipped_key = _("stats.skipped")
            
            # 添加接口统计
            user_log(_("user.summary.interfaces_processed"), 
                    total=conversion_stats["interfaces"][total_key],
                    success=conversion_stats["interfaces"][success_key],
                    failed=conversion_stats["interfaces"][failed_key],
                    skipped=conversion_stats["interfaces"][skipped_key])
            
            # 添加区域统计
            user_log(_("user.summary.zones_processed"), 
                    total=conversion_stats["zones"][total_key],
                    success=conversion_stats["zones"][success_key],
                    failed=conversion_stats["zones"][failed_key],
                    skipped=conversion_stats["zones"][skipped_key])
            
            # 添加策略统计
            user_log(_("user.summary.policies_processed"), 
                    total=conversion_stats["policies"][total_key],
                    success=conversion_stats["policies"][success_key],
                    failed=conversion_stats["policies"][failed_key],
                    skipped=conversion_stats["policies"][skipped_key])
            
            # 添加静态路由统计
            if "static_routes" in conversion_stats:
                user_log(_("user.summary.routes_processed"), 
                        total=conversion_stats["static_routes"][total_key],
                        success=conversion_stats["static_routes"][success_key],
                        failed=conversion_stats["static_routes"][failed_key],
                        skipped=conversion_stats["static_routes"][skipped_key])
            
            # 添加地址对象统计
            user_log(_("user.summary.addresses_processed"), 
                    total=conversion_stats["addresses"][total_key],
                    success=conversion_stats["addresses"][success_key],
                    failed=conversion_stats["addresses"][failed_key],
                    skipped=conversion_stats["addresses"][skipped_key])
            
            # 添加地址组统计
            user_log(_("user.summary.address_groups_processed"), 
                    total=conversion_stats["address_groups"][total_key],
                    success=conversion_stats["address_groups"][success_key],
                    failed=conversion_stats["address_groups"][failed_key],
                    skipped=conversion_stats["address_groups"][skipped_key])
            
            # 添加服务对象统计
            user_log(_("user.summary.services_processed"), 
                    total=conversion_stats["services"][total_key],
                    success=conversion_stats["services"][success_key],
                    failed=conversion_stats["services"][failed_key],
                    skipped=conversion_stats["services"][skipped_key])
            
            # 添加服务组统计
            if "service_groups" in conversion_stats:
                user_log(_("user.summary.service_groups_processed"), 
                        total=conversion_stats["service_groups"][total_key],
                        success=conversion_stats["service_groups"][success_key],
                        failed=conversion_stats["service_groups"][failed_key],
                        skipped=conversion_stats["service_groups"][skipped_key])
            
            # 添加时间对象统计
            if "time_ranges" in conversion_stats:
                user_log(_("user.summary.time_ranges_processed"),
                        total=conversion_stats["time_ranges"][total_key],
                        success=conversion_stats["time_ranges"][success_key],
                        failed=conversion_stats["time_ranges"][failed_key],
                        skipped=conversion_stats["time_ranges"][skipped_key])

            # 添加NAT统计
            if "nat_rules" in conversion_stats:
                user_log(_("user.summary.nat_rules_processed"),
                        total=conversion_stats["nat_rules"][total_key],
                        success=conversion_stats["nat_rules"][success_key],
                        failed=conversion_stats["nat_rules"][failed_key],
                        skipped=conversion_stats["nat_rules"][skipped_key])

            # 添加总体转换统计
            user_log("", summary=True)  # 空行
            user_log(_("user.summary.conversion_summary"), summary=True)
            user_log("", summary=True)  # 空行
            
            # 计算总体统计数据
            total_items = 0
            total_success = 0
            total_failed = 0
            total_skipped = 0
            
            # 定义统计字段的键名
            total_key = _("stats.total")
            success_key = _("stats.success")
            failed_key = _("stats.failed")
            skipped_key = _("stats.skipped")
            
            for category in conversion_stats:
                if isinstance(conversion_stats[category], dict) and total_key in conversion_stats[category]:
                    total_items += conversion_stats[category][total_key]
                    total_success += conversion_stats[category][success_key]
                    total_failed += conversion_stats[category][failed_key]
                    total_skipped += conversion_stats[category][skipped_key]
            
            user_log(_("user.summary.total_items_processed"), 
                    total=total_items,
                    success=total_success,
                    failed=total_failed,
                    skipped=total_skipped)
            
            # 收集所有未转换的项目（包括失败和跳过的）
            unconverted_items = []
            
            # 从各个处理结果中收集失败和跳过的项目
            if "interfaces_result" in locals() and isinstance(interfaces_result, dict):
                for item in interfaces_result.get("failed", []):
                    unconverted_items.append({
                        "type": _("config_type.interface"),
                        "name": item.get("name", _("common.unknown")),
                        "reason": item.get("reason", _("common.unknown_reason"))
                    })
                for item in interfaces_result.get("skipped", []):
                    unconverted_items.append({
                        "type": _("config_type.interface"),
                        "name": item.get("name", _("common.unknown")),
                        "reason": item.get("reason", _("common.unknown_reason"))
                    })
            
            if "policies_result" in locals() and isinstance(policies_result, dict):
                for item in policies_result.get("failed", []):
                    unconverted_items.append({
                        "type": _("config_type.policy_rule"),
                        "name": item.get("name", _("common.unknown")),
                        "reason": item.get("reason", _("common.unknown_reason"))
                    })
                for item in policies_result.get("skipped", []):
                    unconverted_items.append({
                        "type": _("config_type.policy_rule"),
                        "name": item.get("name", _("common.unknown")),
                        "reason": item.get("reason", _("common.unknown_reason"))
                    })
            
            if "routes_result" in locals() and isinstance(routes_result, dict):
                for item in routes_result.get("failed", []):
                    unconverted_items.append({
                        "type": _("config_type.static_route"),
                        "name": item.get("destination", _("common.unknown")),
                        "reason": item.get("reason", _("common.unknown_reason"))
                    })
                for item in routes_result.get("skipped", []):
                    unconverted_items.append({
                        "type": _("config_type.static_route"),
                        "name": item.get("destination", _("common.unknown")),
                        "reason": item.get("reason", _("common.unknown_reason"))
                    })
            
            if "addresses_result" in locals() and isinstance(addresses_result, dict):
                for item in addresses_result.get("failed", []):
                    unconverted_items.append({
                        "type": _("config_type.address_object"),
                        "name": item.get("name", _("common.unknown")),
                        "reason": item.get("reason", _("common.unknown_reason"))
                    })
                for item in addresses_result.get("skipped", []):
                    unconverted_items.append({
                        "type": _("config_type.address_object"),
                        "name": item.get("name", _("common.unknown")),
                        "reason": item.get("reason", _("common.unknown_reason"))
                    })
            
            if "address_groups_result" in locals() and isinstance(address_groups_result, dict):
                for item in address_groups_result.get("failed", []):
                    unconverted_items.append({
                        "type": _("config_type.address_group"),
                        "name": item.get("name", _("common.unknown")),
                        "reason": item.get("reason", _("common.unknown_reason"))
                    })
                for item in address_groups_result.get("skipped", []):
                    unconverted_items.append({
                        "type": _("config_type.address_group"),
                        "name": item.get("name", _("common.unknown")),
                        "reason": item.get("reason", _("common.unknown_reason"))
                    })
            
            if "services_result" in locals() and isinstance(services_result, dict):
                for item in services_result.get("failed", []):
                    unconverted_items.append({
                        "type": _("config_type.service_object"),
                        "name": item.get("name", _("common.unknown")),
                        "reason": item.get("reason", _("common.unknown_reason"))
                    })
                for item in services_result.get("skipped", []):
                    unconverted_items.append({
                        "type": _("config_type.service_object"),
                        "name": item.get("name", _("common.unknown")),
                        "reason": item.get("reason", _("common.unknown_reason"))
                    })
            
            if "service_groups_result" in locals() and isinstance(service_groups_result, dict):
                for item in service_groups_result.get("failed", []):
                    unconverted_items.append({
                        "type": _("config_type.service_group"),
                        "name": item.get("name", _("common.unknown")),
                        "reason": item.get("reason", _("common.unknown_reason"))
                    })
                for item in service_groups_result.get("skipped", []):
                    unconverted_items.append({
                        "type": _("config_type.service_group"),
                        "name": item.get("name", _("common.unknown")),
                        "reason": item.get("reason", _("common.unknown_reason"))
                    })
            
            if "time_range_result" in locals() and isinstance(time_range_result, dict):
                for item in time_range_result.get("failed", []):
                    unconverted_items.append({
                        "type": _("config_type.time_range"),
                        "name": item.get("name", _("common.unknown")),
                        "reason": item.get("reason", _("common.unknown_reason"))
                    })
                for item in time_range_result.get("skipped", []):
                    unconverted_items.append({
                        "type": _("config_type.time_range"),
                        "name": item.get("name", _("common.unknown")),
                        "reason": item.get("reason", _("common.unknown_reason"))
                    })
            
            # 添加未转换项目的总结
            if unconverted_items:
                user_log("", summary=True)  # 空行
                user_log(_("user.summary.unconverted_items"), summary=True)
                user_log("", summary=True)  # 空行
                
                for item in unconverted_items:
                    user_log(_("user.summary.unconverted_item"), 
                            type=item.get("type", _("common.unknown")),
                            name=item.get("name", _("common.unknown")),
                            reason=item.get("reason", _("common.unknown_reason")))
            else:
                user_log("", summary=True)  # 空行
                user_log(_("user.summary.no_unconverted_items"), summary=True)
            
            # 添加失败项目的详细信息
            failed_items = [item for item in unconverted_items if any(result.get("failed", []) for result in [
                locals().get("interfaces_result", {}), 
                locals().get("policies_result", {}),
                locals().get("routes_result", {}),
                locals().get("addresses_result", {}),
                locals().get("address_groups_result", {}),
                locals().get("services_result", {}),
                locals().get("service_groups_result", {}),
                locals().get("time_range_result", {})
            ])]
            
            if failed_items:
                user_log("", summary=True)  # 空行
                user_log(_("user.summary.failed_items_detail"), summary=True)
                user_log("", summary=True)  # 空行
                
                for item in failed_items:
                    user_log(_("user.summary.failed_item_detail"), 
                            type=item.get("type", _("common.unknown")),
                            name=item.get("name", _("common.unknown")),
                            reason=item.get("reason", _("common.unknown_reason")))
            
            # 添加跳过项目的详细信息
            skipped_items = [item for item in unconverted_items if any(result.get("skipped", []) for result in [
                locals().get("interfaces_result", {}), 
                locals().get("policies_result", {}),
                locals().get("routes_result", {}),
                locals().get("addresses_result", {}),
                locals().get("address_groups_result", {}),
                locals().get("services_result", {}),
                locals().get("service_groups_result", {}),
                locals().get("time_range_result", {})
            ])]
            
            if skipped_items:
                user_log("", summary=True)  # 空行
                user_log(_("user.summary.skipped_items_detail"), summary=True)
                user_log("", summary=True)  # 空行
                
                for item in skipped_items:
                    user_log(_("user.summary.skipped_item_detail"), 
                            type=item.get("type", _("common.unknown")),
                            name=item.get("name", _("common.unknown")),
                            reason=item.get("reason", _("common.unknown_reason")))
            
            # 添加需要手动配置的项目
            if manual_config_items:
                user_log("", summary=True)  # 空行
                user_log(_("user.summary.manual_config_needed"), summary=True)
                user_log("", summary=True)  # 空行
                
                for item in manual_config_items:
                    user_log(_("user.summary.manual_config_item"), 
                            type=item.get("类型", _("common.unknown")),
                            reason=item.get("原因", _("common.unknown_reason")),
                            suggestion=item.get("建议", _("common.no_suggestion")))
            else:
                user_log("", summary=True)  # 空行
                user_log(_("user.summary.no_manual_config_needed"), summary=True)
            
            # 添加兼容性问题
            if compatibility_issues:
                user_log("", summary=True)  # 空行
                user_log(_("user.summary.compatibility_issues"), summary=True)
                user_log("", summary=True)  # 空行
                
                for issue in compatibility_issues:
                    compatibility_issue_name = issue.get("name", str(_("common.unknown")))
                    compatibility_issue_detail = issue.get("detail", str(_("common.no_details")))
                    compatibility_issue_impact = issue.get("impact", str(_("common.no_impact")))
                    user_log(_("user.summary.compatibility_issue"), 
                            issue=compatibility_issue_name,
                            detail=compatibility_issue_detail,
                            impact=compatibility_issue_impact)
            else:
                user_log("", summary=True)  # 空行
                user_log(_("user.summary.no_compatibility_issues"), summary=True)
            
            
            # 添加转换耗时信息
            duration = user_formatter.get_conversion_duration()
            user_log(_("user.summary.conversion_time"), time=duration, summary=True)
            
            # 在函数结束前刷新所有日志
            flush_all_logs()
            
            return {
                "success": True,
                "message": str(_("info.conversion_successful")),
                "report_file": report_file
            }
        except Exception as e:
            error_msg = _("error.ntos_generation_failed", error=str(e))
            log(error_msg, "error")
            user_log(_("user.error.target_config_generation_failed"), "error")
            # 确保report_file变量已定义
            report_file = os.path.join(os.path.dirname(output_file), "conversion_report.json")
            # 在函数结束前刷新所有日志
            flush_all_logs()
            return {
                "success": False,
                "error": _("error.ntos_generation_failed", error=str(e)),
                "message": _("error.cannot_generate_target_config"),
                "report_file": report_file
            }
        
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        error_msg = _("error.unknown_error", error=str(e))
        log(error_msg, "error")
        log(error_details, "error")
        user_log(_("user.error.conversion_failed"), "error")
        
        # 创建错误报告
        try:
            report_dir = os.path.dirname(output_file)
            if not os.path.exists(report_dir):
                os.makedirs(report_dir, exist_ok=True)
            
            report_file = os.path.join(report_dir, "error_report.json")
            error_report = {
                _("report.timestamp"): datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                _("report.source_file"): os.path.basename(cli_file) if cli_file else _("report.unknown"),
                _("report.vendor"): vendor,
                _("report.error"): str(e),
                _("report.details"): error_details
            }
            
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(error_report, f, ensure_ascii=False, indent=2)
            log(_("info.error_report_saved"), file=report_file)
        except:
            # 如果无法创建错误报告，忽略这个异常
            pass
        
        # 在异常处理后也刷新所有日志
        flush_all_logs()
        
        return {
            "success": False,
            "error": _("error.unknown_error", error=str(e)),
            "message": _("error.conversion_failed"),
            "report_file": report_file if 'report_file' in locals() else ""
        }

def validate_interface_mapping(interfaces, mapping_data, model=None):
    """
    验证接口映射是否有效

    Args:
        interfaces (list): 接口列表
        mapping_data (dict): 映射数据
        model (str, optional): 设备型号，如果提供，将验证目标接口是否在该型号上有效

    Returns:
        tuple: (是否有效, 无效映射列表, 警告列表)
    """
    from engine.utils.interface_validator import validate_interface
    from engine.utils.logger import log
    from engine.utils.i18n import _

    # 创建接口名称到接口对象的映射
    interface_dict = {}
    for intf in interfaces:
        if isinstance(intf, dict):
            raw_name = intf.get("raw_name")
            if raw_name:
                interface_dict[raw_name] = intf

    # 验证结果
    is_valid = True
    invalid_mappings = []
    warnings = []

    # 如果没有接口或映射，认为是有效的
    if not interfaces or not mapping_data:
        log(_("info.no_interfaces_or_mappings_to_validate"))
        return True, [], []

    # 提取实际的接口映射数据
    # 支持两种格式：
    # 1. 嵌套格式: {"interface_mappings": {"port1": "Ge0/0"}, "default_mapping": {...}}
    # 2. 扁平格式: {"port1": "Ge0/0", "port2": "Ge0/1"}
    actual_mappings = {}

    if "interface_mappings" in mapping_data:
        # 嵌套格式，提取interface_mappings部分
        actual_mappings = mapping_data["interface_mappings"]
        log(_("info.using_nested_interface_mapping_format"), "debug")
    else:
        # 扁平格式，直接使用
        # 过滤掉非接口映射的键（如default_mapping等配置项）
        for key, value in mapping_data.items():
            if key not in ["default_mapping"] and isinstance(value, (str, dict)):
                actual_mappings[key] = value
        log(_("info.using_flat_interface_mapping_format"), "debug")

    if not actual_mappings:
        log(_("warning.no_actual_interface_mappings_found"), "warning")
        return True, [], []

    # 验证每个映射
    for source_interface, target_interface in actual_mappings.items():
        # 检查源接口是否存在
        if source_interface not in interface_dict:
            invalid_mappings.append({
                "source": source_interface,
                "target": target_interface,
                "reason": _("error.source_interface_not_exists", interface=source_interface)
            })
            is_valid = False
            continue
        
        # 检查源接口是否为物理接口或子接口
        interface_data = interface_dict[source_interface]
        is_physical = interface_data.get("type") == "physical" or not interface_data.get("is_subinterface", False)
        is_subinterface = interface_data.get("is_subinterface", False)
        
        if not is_physical:
            # 如果是子接口，检查其父接口是否存在且为物理接口
            if is_subinterface:
                # 尝试从子接口名称中提取父接口名称
                parent_interface = None
                
                # 从接口数据中获取父接口信息
                if "parent_interface" in interface_data:
                    parent_interface = interface_data["parent_interface"]
                elif "interface" in interface_data:  # 某些解析器可能使用"interface"字段
                    parent_interface = interface_data["interface"]
                else:
                    # 尝试从名称中解析父接口
                    # 常见格式: port1.10, eth0.20, vlan3-100 等
                    import re
                    dot_match = re.match(r'^([^.]+)\.', source_interface)
                    dash_match = re.match(r'^([^-]+)-', source_interface)
                    
                    if dot_match:
                        parent_interface = dot_match.group(1)
                    elif dash_match:
                        parent_interface = dash_match.group(1)
                
                # 检查父接口是否存在且为物理接口
                if parent_interface and parent_interface in interface_dict:
                    parent_data = interface_dict[parent_interface]
                    parent_is_physical = parent_data.get("type") == "physical" or not parent_data.get("is_subinterface", False)
                    
                    if parent_is_physical:
                        # 父接口是物理接口，子接口验证通过
                        log(_("info.subinterface_parent_validated"), 
                            subinterface=source_interface, parent=parent_interface)
                        continue
                    else:
                        # 父接口不是物理接口
                        invalid_mappings.append({
                            "source": source_interface,
                            "target": target_interface,
                            "reason": _("error.parent_interface_not_physical", 
                                        interface=source_interface, parent=parent_interface)
                        })
                        is_valid = False
                        continue
                else:
                    # 找不到父接口
                    invalid_mappings.append({
                        "source": source_interface,
                        "target": target_interface,
                        "reason": _("error.parent_interface_not_found", 
                                    interface=source_interface, parent=parent_interface or "unknown")
                    })
                    is_valid = False
                    continue
            else:
                # 不是子接口也不是物理接口
                invalid_mappings.append({
                    "source": source_interface,
                    "target": target_interface,
                    "reason": _("error.source_interface_not_physical", interface=source_interface)
                })
                is_valid = False
                continue
        
        # 如果提供了设备型号，验证目标接口是否在该型号上有效
        if model:
            # 提取目标接口名称
            target_name = target_interface
            if isinstance(target_interface, dict):
                target_name = target_interface.get("name", "")
            
            # 验证目标接口
            is_valid_interface, reason = validate_interface(target_name, model)
            if not is_valid_interface:
                invalid_mappings.append({
                    "source": source_interface,
                    "target": target_name,
                    "reason": reason
                })
                is_valid = False
    
    # 记录验证结果
    if is_valid:
        log(_("info.interface_mapping_validation_passed"))
    else:
        log(_("error.interface_mapping_validation_failed"), "error", count=len(invalid_mappings))
        for mapping in invalid_mappings:
            log(_("error.invalid_interface_mapping"), "error", 
                source=mapping["source"], target=mapping["target"], reason=mapping["reason"])
    
    return is_valid, invalid_mappings, warnings

def _check_version_supports_nat(target_version):
    """
    检查目标版本是否支持NAT/SNAT规则

    Args:
        target_version (str): 目标NTOS版本，如'R10P2'或'R11'

    Returns:
        bool: 是否支持NAT/SNAT规则
    """
    if not target_version:
        # 如果没有指定版本，默认支持NAT（向后兼容）
        return True

    # 提取版本号
    version_upper = target_version.upper()

    # R10P2版本不支持NAT/SNAT规则
    if version_upper == "R10P2":
        return False

    # R11及以后版本支持NAT/SNAT规则
    if version_upper.startswith("R11") or version_upper.startswith("R12") or version_upper.startswith("R13"):
        return True

    # 对于其他版本，尝试解析版本号
    import re
    version_match = re.match(r'R(\d+)', version_upper)
    if version_match:
        major_version = int(version_match.group(1))
        # R11及以后版本支持NAT
        return major_version >= 11

    # 无法识别的版本，默认支持（向后兼容）
    return True

def _generate_subinterface_ntos_name(interface_name, parent_interface=None, vlan_id=None):
    """
    为子接口生成合适的NTOS名称

    Args:
        interface_name (str): 原始接口名称
        parent_interface (str): 父接口名称
        vlan_id (str): VLAN ID

    Returns:
        str: NTOS格式的接口名称
    """
    import re

    if not interface_name:
        return interface_name

    # 如果有父接口和VLAN ID信息，优先使用（这是VLAN子接口的标准情况）
    if parent_interface and vlan_id:
        # 这种情况下，接口名称应该是 父接口.VLAN_ID 格式
        # 但这里我们只返回原始名称，让调用方处理映射
        return interface_name

    # 传统的VLAN子接口：port1.10 -> port1.10 (保持原样，因为没有父接口映射)
    if "." in interface_name:
        return interface_name

    # 其他情况保持原名
    return interface_name

def process_interface_mapping(interfaces, mapping_data):
    """
    处理接口映射

    Args:
        interfaces (list): 解析出的接口列表
        mapping_data (dict): 映射配置数据

    Returns:
        dict: 处理后的接口映射，键为原始接口名称，值为映射后的接口数据
        或者 (dict, bool): 处理后的接口映射和一个布尔值，表示是否所有物理接口都没有映射关系
    """
    log(_("info.processing_interface_mapping"))

    if not interfaces:
        log(_("warning.no_interfaces_to_process"), "warning")
        return {}, True  # 没有接口，视为所有物理接口都没有映射关系

    # 提取实际的接口映射数据
    # 支持两种格式：
    # 1. 嵌套格式: {"interface_mappings": {"port1": "Ge0/0"}, "default_mapping": {...}}
    # 2. 扁平格式: {"port1": "Ge0/0", "port2": "Ge0/1"}
    actual_mappings = {}

    if "interface_mappings" in mapping_data:
        # 嵌套格式，提取interface_mappings部分
        actual_mappings = mapping_data["interface_mappings"]
        log(_("info.using_nested_interface_mapping_format"), "debug")
    else:
        # 扁平格式，直接使用
        # 过滤掉非接口映射的键（如default_mapping等配置项）
        for key, value in mapping_data.items():
            if key not in ["default_mapping"] and isinstance(value, (str, dict)):
                actual_mappings[key] = value
        log(_("info.using_flat_interface_mapping_format"), "debug")

    # 创建结果字典，键为原始接口名称，值为映射后的接口数据
    result = {}

    # 统计物理接口和映射成功的物理接口数量
    physical_interfaces_count = 0
    mapped_physical_interfaces_count = 0
    
    # 先处理物理接口，以便后续子接口可以引用它们的映射
    for intf in interfaces:
        # 获取原始接口名称
        raw_name = intf.get("raw_name")
        if not raw_name:
            log(_("warning.interface_missing_raw_name"), "warning", interface=str(intf))
            continue
        
        # 检查是否是物理接口
        is_physical = intf.get("type") == "physical" or not intf.get("is_subinterface", False)
        if not is_physical:
            # 跳过子接口，稍后处理
            continue
            
        physical_interfaces_count += 1
        
        # 查找映射
        mapped_name = actual_mappings.get(raw_name)
        if not mapped_name:
            log(_("warning.interface_mapping_not_found", interface=raw_name), "warning")
            # 物理接口没有找到映射关系时，不进行转换操作
            log(_("info.skipping_unmapped_interface", interface=raw_name))
            continue
        
        # 物理接口有映射，增加计数
        mapped_physical_interfaces_count += 1
        
        # 创建一个新的接口配置字典
        mapped_intf = intf.copy()
        mapped_intf["name"] = mapped_name
        
        # 添加到结果
        result[raw_name] = mapped_intf
        # 确保mapped_name是字符串格式用于日志显示
        display_mapped_name = mapped_name if isinstance(mapped_name, str) else mapped_name.get("name", str(mapped_name))
        log(_("info.interface_mapped", raw_name=raw_name, mapped_name=display_mapped_name))
    
    # 现在处理子接口
    for intf in interfaces:
        raw_name = intf.get("raw_name")
        if not raw_name:
            continue
            
        # 检查是否是子接口
        is_subinterface = intf.get("is_subinterface", False)
        if not is_subinterface:
            # 跳过物理接口，已经处理过了
            continue
            
        # 查找子接口映射
        mapped_name = actual_mappings.get(raw_name)
        
        if mapped_name:
            # 子接口有直接映射，使用它
            mapped_intf = intf.copy()
            mapped_intf["name"] = mapped_name
            result[raw_name] = mapped_intf
            log(_("info.subinterface_direct_mapped", raw_name=raw_name, mapped_name=mapped_name))
        else:
            # 子接口没有直接映射，尝试基于父接口生成映射
            parent_interface = intf.get("parent_interface") or intf.get("interface")
            vlanid = intf.get("vlanid")
            
            if parent_interface and vlanid:
                # 查找父接口的映射
                parent_mapped = False
                mapped_parent = None
                
                # 检查父接口是否已经映射
                for orig_name, mapped_intf in result.items():
                    if isinstance(mapped_intf, dict) and mapped_intf.get("raw_name") == parent_interface:
                        mapped_parent = mapped_intf.get("name")
                        parent_mapped = True
                        break
                
                # 如果父接口没有在result中找到，尝试在映射表中查找
                if not parent_mapped and parent_interface in actual_mappings:
                    mapped_parent = actual_mappings[parent_interface]
                    parent_mapped = True
                
                if parent_mapped and mapped_parent:
                    # 基于父接口的映射和VLAN ID生成子接口的映射名称
                    if isinstance(mapped_parent, dict) and "name" in mapped_parent:
                        mapped_parent_name = mapped_parent["name"]
                    else:
                        mapped_parent_name = str(mapped_parent)
                    
                    # 生成子接口名称
                    generated_name = f"{mapped_parent_name}.{vlanid}"
                    
                    # 创建映射
                    mapped_intf = intf.copy()
                    mapped_intf["name"] = generated_name
                    
                    # 添加到结果
                    result[raw_name] = mapped_intf
                    log(_("info.subinterface_generated_mapping", raw_name=raw_name,
                        mapped_name=generated_name, parent=parent_interface,
                        mapped_parent=mapped_parent_name, vlanid=vlanid))
                else:
                    # 父接口没有映射，跳过此子接口，不添加到映射文件中
                    log(_("warning.subinterface_parent_not_mapped_skipped"), "warning",
                        raw_name=raw_name, parent=parent_interface)
                    # 不添加到result中，直接跳过
            else:
                # 子接口缺少父接口或VLAN ID信息，跳过此子接口
                log(_("warning.subinterface_missing_parent_or_vlanid_skipped"), "warning",
                    raw_name=raw_name, has_parent=bool(parent_interface), has_vlanid=bool(vlanid))
                # 不添加到result中，直接跳过
    
    log(_("info.interface_mapping_complete"), count=len(result))
    
    # 检查是否所有物理接口都没有映射关系
    all_physical_unmapped = physical_interfaces_count > 0 and mapped_physical_interfaces_count == 0
    if all_physical_unmapped:
        log(_("error.all_physical_interfaces_unmapped"), "error", count=physical_interfaces_count)
    
    # 处理飞塔子接口映射
    log(_("info.processing_fortinet_subinterfaces"))
    
    # 创建一个临时配置数据结构，包含接口列表
    temp_config = {"interfaces": list(result.values())}
    
    # 创建简单的名称映射用于子接口处理
    simple_mapping_for_subinterfaces = {}
    for original_name, interface_config in result.items():
        if isinstance(interface_config, dict) and "name" in interface_config:
            simple_mapping_for_subinterfaces[original_name] = interface_config["name"]
        else:
            simple_mapping_for_subinterfaces[original_name] = interface_config
    
    # 处理飞塔子接口，使用简单映射
    processed_config = process_fortinet_interfaces(temp_config, simple_mapping_for_subinterfaces)
    
    # 用处理后的接口列表更新result字典
    updated_result = {}
    for intf in processed_config["interfaces"]:
        # 保存raw_name到结果映射中
        raw_name = intf.get("raw_name")
        if raw_name:
            updated_result[raw_name] = intf
            
        # 如果子接口有新名称，使用新名称作为键
        if intf.get("is_subinterface") and "name" in intf and "parent_interface" in intf:
            # 检查父接口是否有映射
            parent_interface = intf.get("parent_interface")
            parent_raw_name = None
            
            # 查找父接口的原始名称
            for orig_name, mapped_intf in result.items():
                if isinstance(mapped_intf, dict) and mapped_intf.get("name") == parent_interface:
                    parent_raw_name = orig_name
                    break
            
            # 只有当父接口有映射时，才添加子接口
            if parent_raw_name:
                # 记录子接口的新旧映射
                log(_("info.subinterface_mapped", raw_name=raw_name, mapped_name=intf['name'], parent=parent_interface))
                
                # 为子接口创建一个唯一的键，确保不会覆盖现有接口
                if "." in raw_name:
                    # 如果原始名称已经是子接口格式，直接使用
                    subintf_key = raw_name
                else:
                    # 否则，创建一个基于父接口和VLAN ID的键
                    vlanid = intf.get("vlanid", "unknown")
                    subintf_key = f"{parent_raw_name}.{vlanid}"
                
                updated_result[subintf_key] = intf
            else:
                log(_("warning.subinterface_parent_not_mapped",
                    raw_name=raw_name, parent=parent_interface), "warning")
    
    # 合并新的结果
    if updated_result:
        result.update(updated_result)
        log(_("info.fortinet_subinterfaces_processed", count=len(result)))
    
    return result, all_physical_unmapped

def process_policy_rules(config_data, interface_mapping, target_version=None):
    """
    处理策略规则 - 增强版本，支持新的策略转换功能

    Args:
        config_data (dict): 解析后的配置数据
        interface_mapping (dict): 接口映射数据
        target_version (str, optional): 目标NTOS版本，如'R10P2'或'R11'

    Returns:
        dict: 处理结果，包含converted(成功), failed(失败), skipped(跳过)和details(详情)
    """
    log(_("info.processing_policy_rules"))

    converted_rules = []
    failed_rules = []
    skipped_rules = []
    details = []

    # 检查目标版本是否支持NAT/SNAT规则
    supports_nat = _check_version_supports_nat(target_version)

    # 新增：检查是否有新格式的策略数据（来自增强的解析器）
    has_new_policies = "policies" in config_data and config_data["policies"]
    has_vips = "vips" in config_data and config_data["vips"]
    has_ippools = "ippools" in config_data and config_data["ippools"]

    # 如果有新格式的策略数据，使用新的策略处理器
    if has_new_policies:
        log(_("info.using_enhanced_policy_processor"))
        try:
            from engine.processors.policy_processor import PolicyProcessor
            from engine.processing.pipeline.data_flow import DataContext
            policy_processor = PolicyProcessor()

            # 提取相关数据
            policies = config_data["policies"]
            vips = config_data.get("vips", {})
            ippools = config_data.get("ippools", {})

            # 创建数据上下文
            context = DataContext()
            context.set_data("ntos_version", target_version)
            context.set_data("version", target_version)
            context.set_data("config_data", config_data)
            context.set_data("interface_mapping", interface_mapping)

            # 处理策略
            policy_result = policy_processor.process_policies(policies, vips, ippools, context)

            # 调试：检查policy_result的结构
            log("policy_result keys: {list(policy_result.keys())}", "debug")
            log("security_policies type: {type(policy_result['security_policies'])}", "debug")
            log("security_policies length: {len(policy_result['security_policies'])}", "debug")
            if policy_result["security_policies"]:
                first_policy = policy_result['security_policies'][0]
                log("first security_policy type: {type(first_policy)}", "debug")
                if isinstance(first_policy, dict):
                    log("first security_policy keys: {list(first_policy.keys())}", "debug")
                    # 检查关键字段的类型
                    for key in ["source-zone", "destination-zone", "source-address", "destination-address", "service"]:
                        if key in first_policy:
                            log("{key} type: {type(first_policy[key])}, value: {first_policy[key]}", "debug")
                else:
                    log("first security_policy is not a dict: {first_policy}", "debug")

            # 将新格式的结果转换为兼容的格式
            for security_policy in policy_result["security_policies"]:
                # 安全地提取字段值，处理不同的数据结构
                def safe_extract_name(field_value, default="unknown"):
                    if isinstance(field_value, dict):
                        return field_value.get("name", default)
                    elif isinstance(field_value, list) and field_value:
                        first_item = field_value[0]
                        if isinstance(first_item, dict):
                            return first_item.get("name", default)
                        else:
                            return str(first_item)
                    elif isinstance(field_value, str):
                        return field_value
                    else:
                        return default

                converted_rule = {
                    "name": security_policy.get("name", "unknown"),
                    "src_zone": safe_extract_name(security_policy.get("source-zone"), "unknown"),
                    "dst_zone": safe_extract_name(security_policy.get("destination-zone"), "unknown"),
                    "src_addr": safe_extract_name(security_policy.get("source-address"), "any"),
                    "dst_addr": safe_extract_name(security_policy.get("destination-address"), "any"),
                    "service": safe_extract_name(security_policy.get("service"), "any"),
                    "action": security_policy.get("action", "permit"),
                    "enabled": True,
                    "description": "",
                    "enhanced_policy": True  # 标记为增强策略
                }
                converted_rules.append(converted_rule)
                details.append({
                    "name": converted_rule["name"],
                    "status": _("status.success"),
                    "reason": _("reason.enhanced_policy_processor"),
                    "original": security_policy,
                    "converted": converted_rule
                })

            # 记录NAT规则信息（用于后续处理）
            if policy_result["nat_rules"]:
                config_data["enhanced_nat_rules"] = policy_result["nat_rules"]
            if policy_result["nat_pools"]:
                config_data["enhanced_nat_pools"] = policy_result["nat_pools"]

            # 记录警告
            for warning in policy_result["warnings"]:
                log(warning, "warning")

            log(_("info.enhanced_policy_processing_complete"),
                security_policies=len(policy_result["security_policies"]),
                nat_rules=len(policy_result["nat_rules"]))

        except Exception as e:
            error_msg = f"Enhanced policy processing failed: {str(e)}"
            log(error_msg, "error")
            # 如果增强处理失败，继续使用原有逻辑
            has_new_policies = False

    # 如果使用了增强策略处理器且成功，直接返回结果
    if has_new_policies:
        log(_("info.policy_rules_processing_complete"),
            total=len(converted_rules) + len(failed_rules) + len(skipped_rules),
            converted=len(converted_rules),
            failed=len(failed_rules),
            skipped=len(skipped_rules))

        return {
            "converted": converted_rules,
            "failed": failed_rules,
            "skipped": skipped_rules,
            "details": details
        }

    # 如果没有使用增强策略处理器，或者增强处理失败，继续使用原有逻辑
    # 检查是否有服务映射关系
    service_mapping = config_data.get("service_mapping_relationships", {})
    have_service_mapping = bool(service_mapping)
    if have_service_mapping:
        log(_("info.using_service_mapping_for_policy_rules"), count=len(service_mapping))
        log(_("debug.using_service_mapping_for_policies"), "debug", count=len(service_mapping))

    # 确保配置数据包含策略规则 - 检查多种可能的键名
    policy_key = None
    if "policy_rules" in config_data and config_data["policy_rules"]:
        policy_key = "policy_rules"
    elif "policy" in config_data and config_data["policy"]:
        policy_key = "policy"

    if not policy_key:
        log(_("info.no_policy_rules_found"), "info")
        return {
            "converted": converted_rules,
            "failed": failed_rules,
            "skipped": skipped_rules,
            "details": details
        }

    # 创建接口名称到区域的映射
    interface_to_zone = {}
    for raw_name, intf_data in interface_mapping.items():
        if "role" in intf_data:
            zone = intf_data["role"]  # 通常role字段表示接口所属区域
            interface_to_zone[raw_name] = zone
            interface_to_zone[intf_data.get("name", raw_name)] = zone

    for i, rule in enumerate(config_data[policy_key]):
        try:
            rule_name = rule.get("name", f"rule_{i+1}")
            log(_("info.processing_policy_rule"), name=rule_name)

            # 检查规则是否包含必要的字段
            required_fields = ["srcintf", "dstintf", "srcaddr", "dstaddr", "service", "action"]
            missing_fields = [field for field in required_fields if field not in rule or not rule[field]]

            if missing_fields:
                log(_("warning.skipping_policy_rule_missing_fields"), "warning",
                    name=rule_name, fields=", ".join(missing_fields))
                skipped_rules.append({
                    "name": rule_name,
                    "reason": _("reason.missing_required_fields", fields=", ".join(missing_fields))
                })
                details.append({
                    "name": rule_name,
                    "status": _("status.skipped"),
                    "reason": _("reason.missing_required_fields", fields=", ".join(missing_fields)),
                    "original": rule
                })
                continue

            # 检查源接口和目标接口是否有映射
            src_intf_mapped = rule["srcintf"] in interface_mapping
            dst_intf_mapped = rule["dstintf"] in interface_mapping

            if not src_intf_mapped or not dst_intf_mapped:
                unmapped = []
                if not src_intf_mapped:
                    unmapped.append(_("interface.source", name=rule['srcintf']))
                if not dst_intf_mapped:
                    unmapped.append(_("interface.destination", name=rule['dstintf']))

                log(_("warning.skipping_policy_rule_unmapped_interfaces"), "warning",
                    name=rule_name, interfaces=", ".join(unmapped))
                skipped_rules.append({
                    "name": rule_name,
                    "reason": _("reason.unmapped_interfaces", interfaces=", ".join(unmapped))
                })
                details.append({
                    "name": rule_name,
                    "status": _("status.skipped"),
                    "reason": _("reason.unmapped_interfaces", interfaces=", ".join(unmapped)),
                    "original": rule
                })
                continue

            # 处理服务名称映射
            service_name = rule["service"]
            mapped_service = service_name

            # 如果有服务映射关系，尝试应用它
            if have_service_mapping:
                # 处理服务列表的情况
                if isinstance(service_name, list):
                    mapped_services = []
                    for svc in service_name:
                        if svc in service_mapping:
                            mapped_svc = service_mapping[svc]
                            mapped_services.append(mapped_svc)
                            log(_("debug.policy_service_mapping_applied"), "debug", rule=rule_name, original=svc, mapped=mapped_svc)
                        else:
                            mapped_services.append(svc)
                    mapped_service = mapped_services
                # 处理单个服务的情况
                elif service_name in service_mapping:
                    mapped_service = service_mapping[service_name]
                    log(_("debug.policy_service_mapping_applied"), "debug", rule=rule_name, original=service_name, mapped=mapped_service)

            # 转换策略规则
            converted_rule = {
                "name": rule_name,
                "src_zone": interface_to_zone.get(rule["srcintf"], _("zone.unknown")),
                "dst_zone": interface_to_zone.get(rule["dstintf"], _("zone.unknown")),
                "src_addr": rule["srcaddr"],
                "dst_addr": rule["dstaddr"],
                "service": mapped_service,  # 使用映射后的服务名称
                "action": "permit" if rule["action"].lower() == "accept" else "deny",
                "enabled": rule.get("status", "enable").lower() == "enable",
                "description": rule.get("comments", "")
            }

            # 处理NAT设置 - 根据版本决定是否处理
            if "nat" in rule and rule["nat"].lower() == "enable":
                if supports_nat:
                    # R11及以后版本支持NAT/SNAT规则
                    converted_rule["nat_enabled"] = True
                    if "ippool" in rule and rule["ippool"].lower() == "enable":
                        converted_rule["nat_type"] = "ippool"
                        converted_rule["nat_ippool"] = rule.get("ippool_name", "")
                    else:
                        converted_rule["nat_type"] = "dynamic"
                else:
                    # R10P2版本不支持NAT/SNAT规则，记录告警
                    converted_rule["nat_enabled"] = False
                    user_log(_("user.warning.nat_not_supported_in_version"), "warning",
                            rule_name=rule_name, version=target_version or "R10P2")
                    log(_("warning.nat_rule_skipped_version_not_supported"), "warning",
                        rule_name=rule_name, version=target_version or "R10P2")
            else:
                converted_rule["nat_enabled"] = False

            # 添加转换后的规则
            converted_rules.append(converted_rule)
            log(_("info.policy_rule_converted_success"), name=rule_name)
            details.append({
                "name": rule_name,
                "status": _("status.success"),
                "reason": "",
                "original": rule,
                "converted": converted_rule
            })

        except Exception as e:
            rule_name = rule.get("name", f"rule_{i+1}")
            error_msg = str(e)
            error_msg = _("error.policy_rule_conversion_failed", name=rule_name, error=error_msg)
            log(error_msg, "error")
            failed_rules.append({
                "name": rule_name,
                "reason": error_msg
            })
            details.append({
                "name": rule_name,
                "status": _("status.failed"),
                "reason": error_msg,
                "original": rule
            })

    log(_("info.policy_rules_processing_complete"),
        total=len(converted_rules) + len(failed_rules) + len(skipped_rules),
        converted=len(converted_rules),
        failed=len(failed_rules),
        skipped=len(skipped_rules))

    return {
        "converted": converted_rules,
        "failed": failed_rules,
        "skipped": skipped_rules,
        "details": details
    }

def process_static_routes(config_data, interface_mapping):
    """
    处理静态路由
    
    Args:
        config_data (dict): 解析后的配置数据
        interface_mapping (dict): 接口映射数据
    
    Returns:
        dict: 处理结果，包含converted(成功), failed(失败), skipped(跳过)和details(详情)
    """
    log(_("info.processing_static_routes"))
    
    converted_routes = []
    converted_routes_ipv6 = []  # 添加IPv6路由列表
    failed_routes = []
    skipped_routes = []
    details = []
    
    # 确保配置数据包含静态路由
    has_ipv4_routes = "static_routes" in config_data and config_data["static_routes"]
    has_ipv6_routes = "static_routes_ipv6" in config_data and config_data["static_routes_ipv6"]
    
    if not has_ipv4_routes and not has_ipv6_routes:
        log(_("info.no_static_routes_found"), "info")
        return {
            "converted": converted_routes,
            "converted_ipv6": converted_routes_ipv6,
            "failed": failed_routes,
            "skipped": skipped_routes,
            "details": details
        }
    
    # 处理IPv4静态路由
    if has_ipv4_routes:
        log(_("info.processing_ipv4_static_routes"), count=len(config_data['static_routes']))
        for idx, route in enumerate(config_data["static_routes"]):
            try:
                # 创建新的静态路由对象 - 直接使用原始路由数据
                new_route = route.copy()
                
                # 确保路由有ID
                if "id" not in new_route:
                    new_route["id"] = idx + 1
                    
                # 检查目的网络字段 - 飞塔可能使用dst而不是destination
                if "dst" in new_route and "destination" not in new_route:
                    # 如果dst字段是完整的CIDR格式，直接使用
                    if "/" in new_route["dst"]:
                        new_route["destination"] = new_route["dst"]
                    else:
                        # 否则需要从dst和mask字段组合成CIDR格式
                        network = new_route["dst"]
                        mask = new_route.get("mask", "0.0.0.0")
                        
                        # 计算掩码对应的前缀长度
                        prefix_len = 0
                        parts = mask.split('.')
                        if len(parts) == 4:  # 确保是有效的IPv4掩码
                            for part in parts:
                                bin_repr = bin(int(part))[2:].zfill(8)  # 转换为二进制并填充到8位
                                prefix_len += bin_repr.count('1')  # 计算1的个数
                        
                        # 特殊情况处理
                        if mask == "0.0.0.0" and network == "0.0.0.0":
                            new_route["destination"] = "0.0.0.0/0"  # 默认路由
                        else:
                            new_route["destination"] = f"{network}/{prefix_len}"
                    
                    log(_("info.route_destination_converted"), src=new_route['dst'], dst=new_route['destination'])
                
                # 如果没有destination字段，但是有黑洞路由标记
                if "destination" not in new_route and "blackhole" in new_route and new_route["blackhole"] == "enable":
                    log(_("info.blackhole_route_default_destination"), id=new_route['id'])
                    new_route["destination"] = "0.0.0.0/0"
                
                # 如果转换后仍然没有destination字段，记录警告并跳过此路由
                if "destination" not in new_route:
                    reason = _("reason.route_missing_destination", id=new_route['id'])
                    log(_("warning.route_missing_destination"), "warning", id=new_route['id'])
                    skipped_routes.append({
                        "id": new_route['id'],
                        "reason": reason
                    })
                    details.append({
                        "id": new_route['id'],
                        "status": _("status.skipped"),
                        "reason": reason,
                        "original": route
                    })
                    continue
                
                # 如果设备字段存在，确保接口名称正确映射
                if "device" in new_route:
                    device_name = new_route["device"]
                    # 移除可能的引号
                    if device_name.startswith('"') and device_name.endswith('"'):
                        device_name = device_name[1:-1]
                        new_route["device"] = device_name
                        log(_("info.interface_name_quotes_removed"), name=device_name)
                    
                    # 使用interface_mapping查找映射后的接口名称
                    if device_name in interface_mapping:
                        mapped_name = interface_mapping[device_name].get("name")
                        if mapped_name:
                            log(_("info.interface_mapping_applied"), original=device_name, mapped=mapped_name)
                            new_route["mapped_device"] = mapped_name
                    else:
                        # 记录未映射的接口信息
                        reason = _("reason.interface_not_mapped", interface=device_name)
                        log(_("warning.route_interface_not_mapped"), "warning", interface=device_name)
                        user_log(_("user.warning.route_interface_not_mapped_check"), "warning", interface=device_name)
                        new_route["unmapped_interface"] = True
                        
                        # 添加到失败路由列表，但不阻止处理
                        failed_routes.append({
                            "id": new_route['id'],
                            "reason": reason
                        })
                        details.append({
                            "id": new_route['id'],
                            "status": _("status.failed"),
                            "reason": reason,
                            "original": route,
                            "converted": new_route
                        })
                        continue
                
                # 添加到转换成功路由列表
                converted_routes.append(new_route)
                
                # 日志记录
                dest = new_route.get("destination", _("route.unknown_destination"))
                gateway = new_route.get("gateway", _("route.unknown_gateway"))
                log(_("info.processing_static_route"), id=new_route['id'], dest=dest, gateway=gateway)
                
                details.append({
                    "id": new_route['id'],
                    "status": _("status.success"),
                    "reason": "",
                    "original": route,
                    "converted": new_route
                })
                
            except Exception as e:
                error_msg = str(e)
                error_msg = _("error.static_route_processing_error", error=error_msg)
                log(error_msg, "error")
                
                # 添加到失败路由列表
                route_id = route.get("id", f"route_{idx+1}")
                failed_routes.append({
                    "id": route_id,
                    "reason": error_msg
                })
                details.append({
                    "id": route_id,
                    "status": _("status.failed"),
                    "reason": error_msg,
                    "original": route
                })
    
    # 处理IPv6静态路由
    if has_ipv6_routes:
        log(_("info.processing_ipv6_static_routes"), count=len(config_data['static_routes_ipv6']))
        for idx, route in enumerate(config_data["static_routes_ipv6"]):
            try:
                # 创建新的IPv6静态路由对象
                new_route = route.copy()
                
                # 确保路由有ID
                if "id" not in new_route:
                    new_route["id"] = f"ipv6_{idx+1}"
                
                # 确保标记为IPv6路由
                new_route["is_ipv6"] = True
                
                # 检查路由是否有必要的配置（网关或设备）
                has_gateway = "gateway" in new_route and new_route["gateway"]
                has_device = "device" in new_route and new_route["device"]
                has_blackhole = "blackhole" in new_route and new_route["blackhole"] == "enable"
                
                # 如果路由没有必要的配置，跳过处理
                if not (has_gateway or has_device or has_blackhole):
                    reason = _("reason.ipv6_route_no_necessary_config", id=new_route['id'])
                    log(_("warning.ipv6_route_no_necessary_config"), "warning", id=new_route['id'])
                    skipped_routes.append({
                        "id": new_route['id'],
                        "reason": reason,
                        "is_ipv6": True
                    })
                    details.append({
                        "id": new_route['id'],
                        "status": _("status.skipped"),
                        "reason": reason,
                        "original": route,
                        "is_ipv6": True
                    })
                    continue
                
                # 如果没有destination字段，但是有黑洞路由标记
                if "destination" not in new_route and "blackhole" in new_route and new_route["blackhole"] == "enable":
                    log(_("info.blackhole_route_default_destination"), id=new_route['id'])
                    new_route["destination"] = "::/0"  # IPv6默认路由
                
                # 如果转换后仍然没有destination字段，记录警告并跳过此路由
                if "destination" not in new_route:
                    reason = _("reason.route_missing_destination", id=new_route['id'])
                    log(_("warning.ipv6_route_missing_destination"), "warning", id=new_route['id'])
                    skipped_routes.append({
                        "id": new_route['id'],
                        "reason": reason,
                        "is_ipv6": True
                    })
                    details.append({
                        "id": new_route['id'],
                        "status": _("status.skipped"),
                        "reason": reason,
                        "original": route,
                        "is_ipv6": True
                    })
                    continue
                
                # 如果设备字段存在，确保接口名称正确映射
                if "device" in new_route:
                    device_name = new_route["device"]
                    # 移除可能的引号
                    if device_name.startswith('"') and device_name.endswith('"'):
                        device_name = device_name[1:-1]
                        new_route["device"] = device_name
                        log(_("info.interface_name_quotes_removed"), name=device_name)
                    
                    # 使用interface_mapping查找映射后的接口名称
                    if device_name in interface_mapping:
                        mapped_name = interface_mapping[device_name].get("name")
                        if mapped_name:
                            log(_("info.interface_mapping_applied"), original=device_name, mapped=mapped_name)
                            new_route["mapped_device"] = mapped_name
                    else:
                        # 记录未映射的接口信息
                        reason = _("reason.interface_not_mapped", interface=device_name)
                        log(_("warning.route_interface_not_mapped"), "warning", interface=device_name)
                        user_log(_("user.warning.route_interface_not_mapped_check"), "warning", interface=device_name)
                        new_route["unmapped_interface"] = True
                        
                        # 添加到失败路由列表，但不阻止处理
                        failed_routes.append({
                            "id": new_route['id'],
                            "reason": reason,
                            "is_ipv6": True
                        })
                        details.append({
                            "id": new_route['id'],
                            "status": _("status.failed"),
                            "reason": reason,
                            "original": route,
                            "converted": new_route,
                            "is_ipv6": True
                        })
                        continue
                
                # 添加到转换成功的IPv6路由列表
                converted_routes_ipv6.append(new_route)
                
                # 日志记录
                dest = new_route.get("destination", _("route.unknown_destination"))
                gateway = new_route.get("gateway", _("route.unknown_gateway"))
                log(_("info.processing_ipv6_static_route"), id=new_route['id'], dest=dest, gateway=gateway)
                
                details.append({
                    "id": new_route['id'],
                    "status": _("status.success"),
                    "reason": "",
                    "original": route,
                    "converted": new_route,
                    "is_ipv6": True
                })
                
            except Exception as e:
                error_msg = str(e)
                error_msg = _("error.ipv6_static_route_processing_error", error=error_msg)
                log(error_msg, "error")
                
                # 添加到失败路由列表
                route_id = route.get("id", f"ipv6_route_{idx+1}")
                failed_routes.append({
                    "id": route_id,
                    "reason": error_msg,
                    "is_ipv6": True
                })
                details.append({
                    "id": route_id,
                    "status": _("status.failed"),
                    "reason": error_msg,
                    "original": route,
                    "is_ipv6": True
                })
    
    # 统计处理结果
    total_ipv4 = len(config_data.get('static_routes', []))
    total_ipv6 = len(config_data.get('static_routes_ipv6', []))
    success_ipv4 = len(converted_routes)
    success_ipv6 = len(converted_routes_ipv6)
    
    log(_("info.static_routes_processing_complete"), 
        total=total_ipv4 + total_ipv6, 
        success=success_ipv4 + success_ipv6, 
        failed=len(failed_routes), 
        skipped=len(skipped_routes),
        ipv4_total=total_ipv4,
        ipv4_success=success_ipv4,
        ipv6_total=total_ipv6,
        ipv6_success=success_ipv6)
    
    return {
        "converted": converted_routes,
        "converted_ipv6": converted_routes_ipv6,
        "failed": failed_routes,
        "skipped": skipped_routes,
        "details": details
    }

def process_address_objects(config_data):
    """
    处理地址对象，将飞塔（FortiGate）地址对象转换为NTOS格式
    
    Args:
        config_data (dict): 解析后的配置数据
    
    Returns:
        dict: 处理结果，包含converted(成功), failed(失败), skipped(跳过)和details(详情)
    """
    log(_("info.processing_address_objects"))
    
    converted_addresses = []
    failed_addresses = []
    skipped_addresses = []
    details = []
    
    # 不支持的地址对象类型列表
    unsupported_types = ["fqdn", "geography", "wildcard", "dynamic", 
                        "interface-subnet", "mac", "route-tag"]
    
    # 向用户显示支持和不支持的地址对象类型信息
    user_log(_("fortigate.address_type_support"))
    user_log(_("fortigate.address_type_unsupported"))
    user_log(_("fortigate.address_interface_unsupported"))
    
    # 确保配置数据包含地址对象
    if "address_objects" not in config_data or not config_data["address_objects"]:
        log(_("info.no_address_objects_found"), "info")
        return {
            "converted": converted_addresses,
            "failed": failed_addresses,
            "skipped": skipped_addresses,
            "details": details
        }
    
    # 添加调试日志，显示所有地址对象
    log(_("debug.processing_address_objects_count"), "debug", count=len(config_data['address_objects']))
    for idx, addr in enumerate(config_data["address_objects"]):
        # 安全地格式化地址对象数据，避免花括号冲突
        addr_name = addr.get('name', 'unknown')
        addr_type = addr.get('type', 'unknown')
        log(_("debug.address_object_detail"), "debug", index=idx+1, name=addr_name, type=addr_type)
    
    for addr in config_data["address_objects"]:
        try:
            # 确保地址对象有名称
            if "name" not in addr or not addr["name"]:
                addr_name = _("address.unnamed")
                reason = _("reason.address_missing_name")
                log(_("warning.skipping_address_missing_name"), "warning", name=addr_name)
                skipped_addresses.append({
                    "name": addr_name,
                    "reason": reason
                })
                details.append({
                    "name": addr_name,
                    "status": _("status.skipped"),
                    "reason": reason,
                    "original": addr
                })
                continue
            
            addr_name = addr["name"]
            
            # 检查是否含有接口关联配置，如果有则跳过
            if "interface" in addr or "associated-interface" in addr:
                reason = _("reason.interface_associated_address_not_supported")
                log(_("warning.skipping_interface_associated_address"), "warning", name=addr_name)
                skipped_addresses.append({
                    "name": addr_name,
                    "reason": reason
                })
                details.append({
                    "name": addr_name,
                    "status": _("status.skipped"),
                    "reason": reason,
                    "original": addr
                })
                continue
            
            # 增强类型检测 - 根据属性推断类型
            addr_type = None
            if "type" in addr:
                addr_type = addr["type"].lower()
                log(_("info.address_type_specified"), name=addr_name, type=addr_type)
            elif "start-ip" in addr and "end-ip" in addr:
                addr_type = "iprange"
                log(_("info.address_type_detected_from_attributes"), name=addr_name, type=addr_type)
            elif "subnet" in addr:
                addr_type = "subnet"
                log(_("info.address_type_detected_from_attributes"), name=addr_name, type=addr_type)
            else:
                addr_type = "ipmask"  # 默认类型
                log(_("info.address_type_not_specified"), name=addr_name, default=addr_type)
            
            # 检查是否为不支持的类型
            if addr_type in unsupported_types:
                reason = _("reason.address_type_not_supported", type=addr_type)
                log(_("warning.skipping_address_unsupported_type"), "warning", name=addr_name, type=addr_type)
                skipped_addresses.append({
                    "name": addr_name,
                    "reason": reason
                })
                details.append({
                    "name": addr_name,
                    "status": _("status.skipped"),
                    "reason": reason,
                    "original": addr
                })
                continue
            
            # 根据类型处理地址对象
            if addr_type == "ipmask" or addr_type == "subnet":
                # 确保有subnet字段
                if "subnet" not in addr:
                    reason = _("reason.subnet_info_missing")
                    log(_("warning.skipping_address_missing_subnet"), "warning", name=addr_name)
                    skipped_addresses.append({
                        "name": addr_name,
                        "reason": reason
                    })
                    details.append({
                        "name": addr_name,
                        "status": _("status.skipped"),
                        "reason": reason,
                        "original": addr
                    })
                    continue
                
                subnet = addr["subnet"]
                
                # 处理不同格式的子网
                if ' ' in subnet:  # 飞塔格式: "ip netmask"
                    subnet_parts = subnet.split()
                    if len(subnet_parts) != 2:
                        reason = _("reason.invalid_subnet_format", subnet=subnet)
                        log(_("warning.skipping_address_invalid_subnet"), "warning", name=addr_name, subnet=subnet)
                        skipped_addresses.append({
                            "name": addr_name,
                            "reason": reason
                        })
                        details.append({
                            "name": addr_name,
                            "status": _("status.skipped"),
                            "reason": reason,
                            "original": addr
                        })
                        continue
                    
                    ip, mask = subnet_parts
                    ip_address = f"{ip}/{mask}"  # NTOS格式
                elif '/' in subnet:  # CIDR格式: "ip/prefix" 或 "ip/mask"
                    ip_address = subnet  # 已经是NTOS支持的格式
                else:  # 单个IP地址
                    ip_address = f"{subnet}/32"  # 默认作为主机地址
                
                # 创建NTOS格式的地址对象
                converted_addr = {
                    "name": addr_name,
                    "type": "subnet",
                    "ip_address": ip_address,
                    "description": addr.get("comment", "")
                }
                
            elif addr_type == "iprange":
                # 检查是否有IP范围信息
                if "start-ip" in addr and "end-ip" in addr:
                    # 创建NTOS格式的IP范围地址对象
                    converted_addr = {
                        "name": addr_name,
                        "type": "range",
                        "ip_address": f"{addr['start-ip']}-{addr['end-ip']}",  # NTOS格式
                        "description": addr.get("comment", "")
                    }
                else:
                    # 尝试从名称中提取IP范围 (例如: ***********-*************)
                    import re
                    range_match = re.match(r'^(\d+\.\d+\.\d+\.\d+)-(\d+\.\d+\.\d+\.\d+)$', addr_name)
                    if range_match:
                        start_ip, end_ip = range_match.groups()
                        converted_addr = {
                            "name": addr_name,
                            "type": "range",
                            "ip_address": f"{start_ip}-{end_ip}",  # NTOS格式
                            "description": addr.get("comment", "")
                        }
                    else:
                        reason = _("reason.ip_range_missing_endpoints")
                        log(_("warning.skipping_address_missing_ip_range"), "warning", name=addr_name)
                        skipped_addresses.append({
                            "name": addr_name,
                            "reason": reason
                        })
                        details.append({
                            "name": addr_name,
                            "status": _("status.skipped"),
                            "reason": reason,
                            "original": addr
                        })
                        continue
                
            else:
                # 其他未明确支持的类型
                reason = _("reason.address_type_not_explicitly_supported", type=addr_type)
                log(_("warning.skipping_address_type_not_explicitly_supported"), "warning", name=addr_name, type=addr_type)
                skipped_addresses.append({
                    "name": addr_name,
                    "reason": reason
                })
                details.append({
                    "name": addr_name,
                    "status": _("status.skipped"),
                    "reason": reason,
                    "original": addr
                })
                continue
            
            # 添加转换后的地址对象
            converted_addresses.append(converted_addr)
            log(_("info.address_object_converted"), name=addr_name, type=addr_type)
            details.append({
                "name": addr_name,
                "status": _("status.success"),
                "reason": "",
                "original": addr,
                "converted": converted_addr
            })
            
        except Exception as e:
            addr_name = addr.get("name", _("address.unnamed"))
            error_msg = str(e)
            error_msg = _("error.address_object_conversion_failed", name=addr_name, error=error_msg)
            log(error_msg, "error")
            failed_addresses.append({
                "name": addr_name,
                "reason": error_msg
            })
            details.append({
                "name": addr_name,
                "status": _("status.failed"),
                "reason": error_msg,
                "original": addr
            })
    
    log(_("info.address_objects_processing_complete"), 
        total=len(config_data['address_objects']), 
        success=len(converted_addresses), 
        failed=len(failed_addresses), 
        skipped=len(skipped_addresses))
    
    return {
        "converted": converted_addresses,
        "failed": failed_addresses,
        "skipped": skipped_addresses,
        "details": details
    }

def process_service_objects(config_data):
    """
    处理服务对象
    
    Args:
        config_data (dict): 解析后的配置数据
    
    Returns:
        dict: 处理结果，包含converted(成功), failed(失败), skipped(跳过)和details(详情)
    """
    log(_("info.processing_service_objects"))
    
    # 尝试加载和使用新的服务处理器（带预定义服务映射）
    try:
        from engine.processors.service_processor import ServiceProcessor
        service_processor = ServiceProcessor()
        log(_("info.using_service_processor_with_mapping"))
        
        # 修改策略：对于预定义服务，只保存映射关系，不进行实际转换
        service_objects = config_data.get("service_objects", [])
        
        # 检查所有服务对象是否都是预定义服务
        all_predefined = True
        predefined_count = 0
        service_mapping_relationships = {}
        
        for service_obj in service_objects:
            if not service_obj or "name" not in service_obj:
                all_predefined = False
                continue
                
            service_name = service_obj.get("name", "")
            if service_processor.is_predefined_service(service_name):
                predefined_count += 1
                # 获取映射关系并保存
                mapping = service_processor.get_predefined_service_mapping(service_name)
                if mapping and "ntos_service" in mapping:
                    service_mapping_relationships[service_name] = mapping["ntos_service"]
                    log(_("debug.predefined_service_mapping_found"), "debug", service=service_name, ntos_service=mapping['ntos_service'])
            else:
                all_predefined = False
        
        # 如果全部是预定义服务，只保存映射关系，不生成服务对象
        if all_predefined and predefined_count > 0:
            log(_("info.all_predefined_services_detected"), count=predefined_count)
            user_log(_("user.info.predefined_services_detected"), count=predefined_count)
            
            # 保存映射关系到配置数据中
            config_data["service_mapping_relationships"] = service_mapping_relationships
            log(_("info.service_mapping_saved"), count=len(service_mapping_relationships))
            
            # 返回空的转换结果，但包含映射关系
            return {
                "converted": [],  # 预定义服务不需要转换
                "failed": [],
                "skipped": [{"name": svc.get("name", ""), "reason": "predefined_service"} for svc in service_objects],
                "details": [{"name": svc.get("name", ""), "status": "skipped", "reason": "predefined_service"} for svc in service_objects],
                "mapping_relationships": service_mapping_relationships
            }
        
        # 如果不是全部预定义服务，则正常处理
        result = service_processor.process_service_objects(service_objects)
        
        # 兼容处理：将 ServiceProcessor 返回的 "successful" 键适配为 "converted" 键
        if "successful" in result and "converted" not in result:
            log(_("info.adapting_service_processor_result_format"))
            result["converted"] = result["successful"]
            
        # 确保所有必要的键都存在
        for key in ["converted", "failed", "skipped", "details"]:
            if key not in result:
                if key == "details":
                    result[key] = []
                elif key not in ["converted", "failed", "skipped"]:
                    result[key] = []
        
        # 处理映射关系，如果存在
        if "mapping_relationships" in result and result["mapping_relationships"]:
            # 保存到配置数据中，以便后续处理策略时使用
            config_data["service_mapping_relationships"] = result["mapping_relationships"]
            log(_("debug.service_mapping_relationships_saved"), "debug", count=len(result['mapping_relationships']))
            log(_("info.service_mapping_saved"), count=len(result["mapping_relationships"]))
                
        return result
    except Exception as e:
        error_msg = _("warning.service_processor_failed", error=str(e))
        log(error_msg, "warning")
        log(_("info.falling_back_to_legacy_service_processing"))
        # 如果加载或使用新处理器失败，回退到传统处理方式
    
    converted_services = []
    failed_services = []
    skipped_services = []
    details = []
    
    # 用于跟踪已处理的服务对象名称，检测重复
    processed_service_names = set()
    
    # 定义NTOS不支持的协议类型列表
    unsupported_protocols = ["udp-lite", "sctp", "icmp6"]
    
    # 确保配置数据包含服务对象
    if "service_objects" not in config_data or not config_data["service_objects"]:
        log(_("info.no_service_objects_found"), "info")
        return {
            "converted": converted_services,
            "failed": failed_services,
            "skipped": skipped_services,
            "details": details
        }
    
    # 创建服务映射关系字典
    service_mapping_relationships = {}
    
    # 添加调试日志，显示所有服务对象
    log(_("debug.processing_service_objects_count"), "debug", count=len(config_data['service_objects']))
    
    for svc in config_data["service_objects"]:
        try:
            # 检查是否存在TCP或UDP端口配置
            has_tcp_ports = "tcp-portrange" in svc and svc["tcp-portrange"]
            has_udp_ports = "udp-portrange" in svc and svc["udp-portrange"]
            
            # 确保服务对象有名称
            if "name" not in svc or not svc["name"]:
                # 尝试生成一个有意义的标识符
                svc_name = _("service.unnamed")
                if "protocol" in svc:
                    protocol = svc["protocol"].lower()
                    if protocol == "tcp" and has_tcp_ports:
                        svc_name = f"tcp_{svc['tcp-portrange']}"
                    elif protocol == "udp" and has_udp_ports:
                        svc_name = f"udp_{svc['udp-portrange']}"
                    elif protocol == "icmp" and "icmptype" in svc:
                        svc_name = f"icmp_type{svc['icmptype']}"
                    else:
                        svc_name = f"service_{protocol}_{id(svc)}"
                
                reason = _("reason.service_missing_name")
                log(_("warning.skipping_service_missing_name"), "warning", name=svc_name)
                skipped_services.append({
                    "name": svc_name,
                    "reason": reason
                })
                details.append({
                    "name": svc_name,
                    "status": _("status.skipped"),
                    "reason": reason,
                    "original": svc
                })
                continue
            
            # 获取服务名称
            svc_name = svc["name"]
            
            # 检查是否存在重复的服务对象名称
            if svc_name in processed_service_names:
                # 发现重复的服务对象名称，直接报错
                error_msg = _("error.duplicate_service_object", name=svc_name)
                log(_("error.duplicate_service_object", name=svc_name), "error")
                user_log(_("user.error.duplicate_service_object", name=svc_name), "error")
                
                # 添加到失败列表
                failed_services.append({
                    "name": svc_name,
                    "reason": error_msg
                })
                details.append({
                    "name": svc_name,
                    "status": _("status.failed"),
                    "reason": error_msg,
                    "original": svc
                })
                continue
            
            # 检查服务对象类型
            protocol = None
            if "protocol" in svc:
                protocol = svc["protocol"].lower()
                log(_("info.service_protocol_specified"), name=svc_name, protocol=protocol)
                
                # 检查是否是不支持的协议类型
                if protocol in unsupported_protocols:
                    reason = _("reason.unsupported_protocol_ntos", protocol=protocol)
                    log(_("warning.skipping_unsupported_protocol_ntos"), "warning", name=svc_name, protocol=protocol)
                    user_log(_("user.warning.unsupported_protocol_ntos"), "warning", name=svc_name, protocol=protocol)
                    skipped_services.append({
                        "name": svc_name,
                        "reason": reason
                    })
                    details.append({
                        "name": svc_name,
                        "status": _("status.skipped"),
                        "reason": reason,
                        "original": svc
                    })
                    continue
                
                # 飞塔特殊规则：根据端口范围调整协议类型
                if has_tcp_ports and has_udp_ports and protocol != "tcp_udp":
                    log(_("debug.service_protocol_auto_corrected_tcp_udp"), "debug", name=svc_name, original=protocol)
                    protocol = "tcp_udp"
                elif has_tcp_ports and protocol != "tcp" and protocol != "tcp_udp":
                    log(_("debug.service_protocol_auto_corrected_tcp"), "debug", name=svc_name, original=protocol)
                    protocol = "tcp"
                elif has_udp_ports and protocol != "udp" and protocol != "tcp_udp":
                    log(_("debug.service_protocol_auto_corrected_udp"), "debug", name=svc_name, original=protocol)
                    protocol = "udp"
            else:
                # 根据端口范围自动推断协议类型
                if has_tcp_ports and has_udp_ports:
                    protocol = "tcp_udp"
                    log(_("info.service_protocol_auto_detected"), name=svc_name, protocol=protocol, reason="both_tcp_udp_ports")
                elif has_tcp_ports:
                    protocol = "tcp"
                    log(_("info.service_protocol_auto_detected"), name=svc_name, protocol=protocol, reason="tcp_ports")
                elif has_udp_ports:
                    protocol = "udp"
                    log(_("info.service_protocol_auto_detected"), name=svc_name, protocol=protocol, reason="udp_ports")
                else:
                    # 既没有指定协议，也没有端口范围
                    reason = _("reason.service_missing_protocol_and_ports")
                    log(_("warning.skipping_service_missing_protocol_and_ports"), "warning", name=svc_name)
                    skipped_services.append({
                        "name": svc_name,
                        "reason": reason
                    })
                    details.append({
                        "name": svc_name,
                        "status": _("status.skipped"),
                        "reason": reason,
                        "original": svc
                    })
                    continue
            
            # 处理不同协议类型的服务对象
            if protocol in ["tcp", "udp", "tcp_udp"]:
                # 检查端口信息 - 根据协议类型验证必要的端口范围
                if (protocol == "tcp" and not has_tcp_ports) or \
                   (protocol == "udp" and not has_udp_ports) or \
                   (protocol == "tcp_udp" and not (has_tcp_ports or has_udp_ports)):
                    reason = _("reason.service_missing_port_range")
                    log(_("warning.skipping_service_missing_port_range"), "warning", name=svc_name)
                    skipped_services.append({
                        "name": svc_name,
                        "reason": reason
                    })
                    details.append({
                        "name": svc_name,
                        "status": _("status.skipped"),
                        "reason": reason,
                        "original": svc
                    })
                    continue
                
                # 创建TCP/UDP服务对象
                converted_svc = {
                    "name": svc_name,
                    "protocol": protocol,
                    "description": svc.get("comment", "")
                }
                
                # 处理TCP端口
                if has_tcp_ports:
                    # 规范化端口范围
                    tcp_ports = svc["tcp-portrange"]
                    # 如果是简单数字，保持原样
                    if tcp_ports.isdigit():
                        converted_svc["tcp_ports"] = tcp_ports
                    # 处理简单范围 (如 "80-90")
                    elif "-" in tcp_ports and ":" not in tcp_ports and " " not in tcp_ports:
                        try:
                            start, end = map(int, tcp_ports.split("-"))
                            if 1 <= start <= end <= 65535:
                                converted_svc["tcp_ports"] = tcp_ports
                            elif start > end:  # 处理范围顺序错误
                                converted_svc["tcp_ports"] = f"{end}-{start}"
                            elif start == end:  # 单一端口
                                converted_svc["tcp_ports"] = str(start)
                            else:  # 无效范围
                                log(_("warning.tcp_port_range_invalid"), "warning", ports=tcp_ports)
                                converted_svc["tcp_ports"] = tcp_ports
                        except ValueError:
                            # 如果解析失败，使用原始值
                            converted_svc["tcp_ports"] = tcp_ports
                    # 处理复杂格式
                    else:
                        # 处理带空格的多个端口/范围
                        if " " in tcp_ports and ":" not in tcp_ports:
                            ports = tcp_ports.split()
                            # 如果只有一个端口，直接使用
                            if len(ports) == 1:
                                converted_svc["tcp_ports"] = ports[0]
                            # 否则使用第一个范围（简化处理）
                            elif ports:
                                converted_svc["tcp_ports"] = ports[0]
                                log(_("debug.multiple_tcp_ports_using_first"), "debug", original=tcp_ports, selected=ports[0])
                            else:
                                converted_svc["tcp_ports"] = tcp_ports
                        # 处理源目端口格式 (如 "80-90:1024-2048")
                        elif ":" in tcp_ports:
                            parts = tcp_ports.split(":")
                            if len(parts) >= 2:
                                # 通常 "目标端口:源端口"，我们使用目标端口
                                dst_port = parts[0].strip()
                                if dst_port:
                                    converted_svc["tcp_ports"] = dst_port
                                    log(_("debug.complex_tcp_ports_using_destination"), "debug", original=tcp_ports, destination=dst_port)
                                else:
                                    converted_svc["tcp_ports"] = tcp_ports
                            else:
                                converted_svc["tcp_ports"] = tcp_ports
                        else:
                            converted_svc["tcp_ports"] = tcp_ports
                
                # 处理UDP端口
                if has_udp_ports:
                    # 规范化端口范围
                    udp_ports = svc["udp-portrange"]
                    # 如果是简单数字，保持原样
                    if udp_ports.isdigit():
                        converted_svc["udp_ports"] = udp_ports
                    # 处理简单范围 (如 "80-90")
                    elif "-" in udp_ports and ":" not in udp_ports and " " not in udp_ports:
                        try:
                            start, end = map(int, udp_ports.split("-"))
                            if 1 <= start <= end <= 65535:
                                converted_svc["udp_ports"] = udp_ports
                            elif start > end:  # 处理范围顺序错误
                                converted_svc["udp_ports"] = f"{end}-{start}"
                            elif start == end:  # 单一端口
                                converted_svc["udp_ports"] = str(start)
                            else:  # 无效范围
                                log(_("warning.udp_port_range_invalid"), "warning", ports=udp_ports)
                                converted_svc["udp_ports"] = udp_ports
                        except ValueError:
                            # 如果解析失败，使用原始值
                            converted_svc["udp_ports"] = udp_ports
                    # 处理复杂格式
                    else:
                        # 处理带空格的多个端口/范围
                        if " " in udp_ports and ":" not in udp_ports:
                            ports = udp_ports.split()
                            # 如果只有一个端口，直接使用
                            if len(ports) == 1:
                                converted_svc["udp_ports"] = ports[0]
                            # 否则使用第一个范围（简化处理）
                            elif ports:
                                converted_svc["udp_ports"] = ports[0]
                                log(_("debug.multiple_udp_ports_using_first"), "debug", original=udp_ports, selected=ports[0])
                            else:
                                converted_svc["udp_ports"] = udp_ports
                        # 处理源目端口格式 (如 "80-90:1024-2048")
                        elif ":" in udp_ports:
                            parts = udp_ports.split(":")
                            if len(parts) >= 2:
                                # 通常 "目标端口:源端口"，我们使用目标端口
                                dst_port = parts[0].strip()
                                if dst_port:
                                    converted_svc["udp_ports"] = dst_port
                                    log(_("debug.complex_udp_ports_using_destination"), "debug", original=udp_ports, destination=dst_port)
                                else:
                                    converted_svc["udp_ports"] = udp_ports
                            else:
                                converted_svc["udp_ports"] = udp_ports
                        else:
                            converted_svc["udp_ports"] = udp_ports
                
            elif protocol == "icmp":
                # 创建ICMP服务对象
                converted_svc = {
                    "name": svc_name,
                    "protocol": "icmp",
                    "description": svc.get("comment", "")
                }
                
                # 添加ICMP类型和代码
                if "icmptype" in svc:
                    converted_svc["icmp_type"] = svc["icmptype"]
                if "icmpcode" in svc:
                    converted_svc["icmp_code"] = svc["icmpcode"]
                
            elif protocol == "ip":
                # 创建IP协议对象
                converted_svc = {
                    "name": svc_name,
                    "protocol": "ip",
                    "description": svc.get("comment", "")
                }
                
                # 处理IP协议号
                if "protocol-number" in svc:
                    converted_svc["protocol_number"] = svc["protocol-number"]
                
            else:
                # 不支持的协议类型
                reason = _("reason.unsupported_protocol", protocol=protocol)
                log(_("warning.skipping_service_unsupported_protocol"), "warning", name=svc_name, protocol=protocol)
                skipped_services.append({
                    "name": svc_name,
                    "reason": reason
                })
                details.append({
                    "name": svc_name,
                    "status": _("status.skipped"),
                    "reason": reason,
                    "original": svc
                })
                continue
            
            # 记录此服务对象名称为已处理
            processed_service_names.add(svc_name)
            
            # 添加转换后的服务对象
            converted_services.append(converted_svc)
            
            # 添加到服务映射关系
            service_mapping_relationships[svc_name] = svc_name
            
            log(_("info.service_object_converted"), name=svc_name)
            details.append({
                "name": svc_name,
                "status": _("status.success"),
                "reason": "",
                "original": svc,
                "converted": converted_svc
            })
            
        except Exception as e:
            svc_name = svc.get("name", _("service.unnamed"))
            
            # 尝试使用其他字段获取对象标识
            if svc_name == _("service.unnamed"):
                # 尝试使用不同字段作为标识
                protocol = svc.get("protocol", _("protocol.unknown"))
                if protocol == "tcp" and "tcp-portrange" in svc:
                    svc_name = f"tcp_{svc['tcp-portrange']}"
                elif protocol == "udp" and "udp-portrange" in svc:
                    svc_name = f"udp_{svc['udp-portrange']}"
                elif protocol == "icmp" and "icmptype" in svc:
                    svc_name = f"icmp_type{svc['icmptype']}"
                else:
                    svc_name = f"service_{protocol}_{id(svc)}"
            
            error_msg = str(e)
            error_msg = _("error.service_object_conversion_failed", name=svc_name, error=error_msg)
            log(error_msg, "error")
            failed_services.append({
                "name": svc_name,
                "reason": error_msg
            })
            details.append({
                "name": svc_name,
                "status": _("status.failed"),
                "reason": error_msg,
                "original": svc
            })
    
    # 保存服务映射关系到config_data中
    if service_mapping_relationships:
        config_data["service_mapping_relationships"] = service_mapping_relationships
        log(_("debug.service_mapping_relationships_saved"), "debug", count=len(service_mapping_relationships))
        log(_("info.service_mapping_saved"), count=len(service_mapping_relationships))
    
    log(_("info.service_objects_processing_complete"), 
        total=len(config_data['service_objects']), 
        success=len(converted_services), 
        failed=len(failed_services), 
        skipped=len(skipped_services))
    
    return {
        "converted": converted_services,
        "failed": failed_services,
        "skipped": skipped_services,
        "details": details,
        "mapping_relationships": service_mapping_relationships
    }

def validate_xml(xml_file, model=None, version=None):
    """
    验证生成的XML是否符合YANG模型
    
    Args:
        xml_file (str): XML文件路径
        model (str, optional): 设备型号
        version (str, optional): 设备版本
        
    Returns:
        tuple: (是否通过验证, 错误信息)
    """
    log(_("info.validating_xml"), file=xml_file)
    
    try:
        return validate_gracefully(xml_file, model=model, version=version)
    except Exception as e:
        error_msg = _("error.xml_validation_error", error=str(e))
        log(error_msg, "error")
        return False, _("error.validation_error", error=str(e))

def format_conversion_result(success, message):
    """格式化转换结果为JSON字符串"""
    result = {
        "success": success,
        "message": message
    }
    
    # 移除可能包含敏感信息的字段
    if "internal_details" in result:
        del result["internal_details"]
    if "debug_info" in result:
        del result["debug_info"]
    
    return json.dumps(result, ensure_ascii=False, indent=2)

# 添加新的函数用于修复常见的路径错误
def fix_common_path_errors(path):
    """
    修复常见的路径错误
    
    Args:
        path (str): 输入路径
        
    Returns:
        str: 修正后的路径
    """
    if not path:
        return path
        
    # 修复configtranuploads -> configtrans_uploads错误
    if "/tmp/configtranuploads/" in path:
        corrected_path = path.replace("/tmp/configtranuploads/", "/tmp/configtrans_uploads/")
        log(_("warning.path_corrected"), "warning", original=path, corrected=corrected_path)
        return corrected_path
        
    return path

def check_compatibility(config_data, vendor, model, version):
    """检查配置与目标设备的兼容性
    
    Args:
        config_data (dict): 解析后的配置数据
        vendor (str): 厂商标识
        model (str): 目标设备型号
        version (str): 目标设备版本
        
    Returns:
        list: 兼容性问题列表，每项包含issue, detail和impact
    """
    compatibility_issues = []
    
    # 检查不支持的特性
    unsupported_features = []
    
    if vendor.lower() == "fortigate":
        # 检查FortiGate特定的不兼容项
        if "vpn" in config_data and config_data["vpn"]:
            unsupported_features.append({
                "name": _("feature.advanced_vpn"),
                "detail": _("feature.advanced_vpn"),
                "impact": _("impact.vpn_ignored")
            })
        
        if "vdom" in config_data and config_data["vdom"]:
            unsupported_features.append({
                "name": _("feature.vdom"),
                "detail": _("feature.vdom"),
                "impact": _("impact.vdom_ignored")
            })
        
        # 检查高级路由功能
        if "routing" in config_data and config_data["routing"]:
            if any(protocol in config_data["routing"] for protocol in ["bgp", "ospf", "isis", "rip"]):
                unsupported_features.append({
                    "name": _("feature.dynamic_routing"),
                    "detail": _("feature.dynamic_routing"),
                    "impact": _("impact.dynamic_routing_ignored")
                })
    
    # 根据目标设备型号和版本检查兼容性
    if model.lower().startswith("z5"):
        # Z5系列设备的特定兼容性检查
        if "interfaces" in config_data:
            # 检查接口数量是否超过限制
            if len(config_data["interfaces"]) > 50:  # 假设限制为50个接口
                compatibility_issues.append({
                    "name": _("issue.interface_count_exceeded"),
                    "detail": _("impact.interface_count_exceeded", count=len(config_data['interfaces'])),
                    "impact": _("impact.interface_count_exceeded", count=len(config_data['interfaces']))
                })
    
    # 将不支持的特性添加到兼容性问题列表
    compatibility_issues.extend(unsupported_features)
    
    # 记录到日志
    if compatibility_issues:
        log(_("warning.compatibility_issues_found"), "warning", count=len(compatibility_issues))
        for issue in compatibility_issues:
            log(_("warning.compatibility_issue_detail"), "warning", name=issue['name'], impact=issue['impact'])
    else:
        log(_("info.no_compatibility_issues"))
    
    return compatibility_issues

# 添加区域处理函数
def process_zone_interface_mapping(zones, interface_mapping):
    """
    处理区域中的接口映射，将原始接口名称替换为映射后的接口名称
    
    Args:
        zones (list): 区域列表
        interface_mapping (dict): 接口映射字典，键为原始接口名称，值为映射后的NTOS接口配置
        
    Returns:
        list: 处理后的区域列表，以及未映射接口的统计信息
    """
    log(_("info.processing_zone_interface_mapping"))
    
    # 确保zones是列表类型
    if not isinstance(zones, list):
        log(_("warning.zones_not_list"), "warning", type=str(type(zones)))
        if isinstance(zones, dict):
            log(_("info.converting_zones_dict_to_list"))
            zones = list(zones.values())
        else:
            log(_("warning.cannot_process_zones"))
            return []
    
    # 创建接口映射字典（原始名称到NTOS名称）
    ntos_interface_names = {}
    for raw_name, intf_data in interface_mapping.items():
        if "name" in intf_data:
            ntos_interface_names[raw_name] = intf_data["name"]
    
    log(_("info.interface_mapping_table"), mapping=str(ntos_interface_names))
    
    # 统计信息
    unmapped_interfaces_count = 0
    unmapped_details = []
    
    # 处理每个区域
    for zone in zones:
        # 确保zone是字典类型
        if not isinstance(zone, dict):
            log(_("warning.skipping_non_dict_zone"), "warning", zone=str(zone))
            continue
            
        zone_name = zone.get("name", "unknown")
        log(_("info.processing_zone_interfaces"), zone=zone_name)
        
        # 获取原始接口列表
        raw_interfaces = zone.get("interfaces", [])
        
        # 确保raw_interfaces是列表类型
        if not isinstance(raw_interfaces, list):
            log(_("warning.zone_interfaces_not_list"), "warning", zone=zone_name, type=str(type(raw_interfaces)))
            if isinstance(raw_interfaces, str):
                # 如果是单个接口名称的字符串，转换为列表
                raw_interfaces = [raw_interfaces]
                log(_("info.converting_single_interface_to_list"), interfaces=str(raw_interfaces))
            else:
                log(_("warning.cannot_process_zone_interfaces"), zone=zone_name)
                raw_interfaces = []
        
        zone_unmapped_count = 0
        
        # 创建新的映射后的接口列表
        mapped_interfaces = []
        
        # 检查每个接口的映射状态
        for raw_intf in raw_interfaces:
            # 确保raw_intf是字符串类型
            if not isinstance(raw_intf, str):
                log(_("warning.interface_not_string"), "warning", zone=zone_name, interface=str(raw_intf))
                continue
                
            if raw_intf in ntos_interface_names:
                mapped_intf = ntos_interface_names[raw_intf]
                mapped_interfaces.append(mapped_intf)
                log(_("info.zone_interface_mapped"), zone=zone_name, interface=raw_intf, mapped=mapped_intf)
            else:
                log(_("warning.zone_interface_not_mapped"), "warning", zone=zone_name, interface=raw_intf)
                log(_("info.skipping_unmapped_zone_interface"), zone=zone_name, interface=raw_intf)
                unmapped_interfaces_count += 1
                zone_unmapped_count += 1
                unmapped_details.append({
                    "zone": zone_name,
                    "interface": raw_intf
                })
        
        # 用映射后的接口列表替换原始接口列表
        zone["interfaces"] = mapped_interfaces
        
        # 记录本区域映射情况
        if zone_unmapped_count > 0:
            log(_("warning.zone_unmapped_interfaces"), "warning", zone=zone_name, count=zone_unmapped_count)
            user_log(_("user.warning.zone_unmapped_interfaces"), "warning", zone=zone_name, count=zone_unmapped_count)
    
    # 总体统计
    if unmapped_interfaces_count > 0:
        log(_("warning.total_unmapped_zone_interfaces"), "warning", count=unmapped_interfaces_count)
        user_log(_("user.warning.total_unmapped_zone_interfaces"), "warning", count=unmapped_interfaces_count)
    else:
        log(_("info.all_zone_interfaces_mapped"))
    
    return zones

def process_address_groups(config_data):
    """
    处理地址组
    
    Args:
        config_data (dict): 解析后的配置数据
    
    Returns:
        dict: 处理结果，包含converted(成功), failed(失败), skipped(跳过)和details(详情)
    """
    log(_("info.processing_address_groups"))
    
    converted_groups = []
    failed_groups = []
    skipped_groups = []
    details = []
    
    # 确保配置数据包含地址组
    if "address_groups" not in config_data or not config_data["address_groups"]:
        log(_("info.no_address_groups_found"), "info")
        return {
            "converted": converted_groups,
            "failed": failed_groups,
            "skipped": skipped_groups,
            "details": details
        }

    
    for group in config_data["address_groups"]:
        try:
            # 确保地址组有名称
            if "name" not in group or not group["name"]:
                group_name = _("address_group.unnamed")
                reason = _("reason.address_group_missing_name")
                log(_("warning.skipping_address_group_missing_name"), "warning", name=group_name)
                skipped_groups.append({
                    "name": group_name,
                    "reason": reason
                })
                details.append({
                    "name": group_name,
                    "status": _("status.skipped"),
                    "reason": reason,
                    "original": group
                })
                continue
            
            group_name = group["name"]
            
            # 确保地址组有成员
            if "members" not in group or not group["members"]:
                reason = _("reason.address_group_empty_members")
                log(_("warning.skipping_address_group_empty_members"), "warning", name=group_name)
                skipped_groups.append({
                    "name": group_name,
                    "reason": reason
                })
                details.append({
                    "name": group_name,
                    "status": _("status.skipped"),
                    "reason": reason,
                    "original": group
                })
                continue
            
            # 创建转换后的地址组对象
            converted_group = {
                "name": group_name,
                "type": "group",
                "members": group["members"],
                "description": group.get("comment", "")
            }
            
            # 添加转换后的地址组
            converted_groups.append(converted_group)
            log(_("info.address_group_converted"), name=group_name, members=len(group["members"]))
            details.append({
                "name": group_name,
                "status": _("status.success"),
                "reason": "",
                "original": group,
                "converted": converted_group
            })
            
        except Exception as e:
            group_name = group.get("name", _("address_group.unnamed"))
            error_msg = str(e)
            error_msg = _("error.address_group_conversion_failed", name=group_name, error=error_msg)
            log(error_msg, "error")
            failed_groups.append({
                "name": group_name,
                "reason": error_msg
            })
            details.append({
                "name": group_name,
                "status": _("status.failed"),
                "reason": error_msg,
                "original": group
            })
    
    log(_("info.address_groups_processing_complete"), 
        total=len(config_data['address_groups']), 
        success=len(converted_groups), 
        failed=len(failed_groups), 
        skipped=len(skipped_groups))
    
    return {
        "converted": converted_groups,
        "failed": failed_groups,
        "skipped": skipped_groups,
        "details": details
    }

def process_service_groups(config_data, service_result=None):
    """
    处理服务组，将飞塔（FortiGate）服务组转换为NTOS格式

    Args:
        config_data (dict): 解析后的配置数据
        service_result (dict, optional): 服务对象处理结果，包含映射关系信息

    Returns:
        dict: 处理结果，包含converted(成功), failed(失败), skipped(跳过)和details(详情)
    """
    log(_("info.processing_service_groups"))
    
    converted_groups = []
    failed_groups = []
    skipped_groups = []
    details = []
    
    # 确保配置数据包含服务组
    log(_("debug.checking_service_groups_data"), "debug", exists="service_groups" in config_data)
    if "service_groups" in config_data:
        log(_("debug.service_groups_data_length"), "debug", count=len(config_data['service_groups']))
        if len(config_data['service_groups']) > 0:
            log(_("debug.service_groups_data_sample"), "debug",
                sample=str(config_data['service_groups'][:3]))
        else:
            log(_("debug.service_groups_data_empty"), "debug")

    if "service_groups" not in config_data or not config_data["service_groups"]:
        log(_("info.no_service_groups_found"), "info")
        return {
            "converted": converted_groups,
            "failed": failed_groups,
            "skipped": skipped_groups,
            "details": details
        }
    
    # 检查服务对象是否存在，用于验证成员引用
    # 优先使用转换后的服务对象列表，以支持预定义服务映射
    service_objects = {}
    service_mapping_relationships = {}

    if service_result and "converted" in service_result:
        # 使用转换后的服务对象列表
        for service in service_result["converted"]:
            if "name" in service:
                service_objects[service["name"]] = service

        # 获取服务映射关系
        service_mapping_relationships = service_result.get("mapping_relationships", {})
        log(_("debug.using_converted_service_objects_for_validation"), "debug", count=len(service_objects))
        if service_mapping_relationships:
            log(_("debug.service_mapping_relationships_found"), "debug", count=len(service_mapping_relationships))
    elif "service_objects" in config_data:
        # 回退到原始服务对象列表
        for service in config_data["service_objects"]:
            if "name" in service:
                service_objects[service["name"]] = service
        log(_("debug.using_original_service_objects_for_validation"), "debug", count=len(service_objects))
    
    # 处理每个服务组
    for idx, group in enumerate(config_data["service_groups"]):
        try:
            # 创建新的服务组对象
            new_group = group.copy()
            
            # 确保服务组有名称
            if "name" not in new_group or not new_group["name"]:
                reason = _("reason.service_group_missing_name")
                log(_("warning.service_group_missing_name"), "warning", id=idx+1)
                skipped_groups.append({
                    "id": idx + 1,
                    "reason": reason
                })
                details.append({
                    "id": idx + 1,
                    "status": _("status.skipped"),
                    "reason": reason,
                    "original": group
                })
                continue
            
            # 验证成员列表 - 检查解析器返回的字段名称
            members_field = None
            if "members" in new_group and new_group["members"]:
                members_field = "members"
            elif "member" in new_group and new_group["member"]:
                members_field = "member"

            if not members_field:
                reason = _("reason.service_group_no_members", name=new_group["name"])
                log(_("warning.service_group_no_members"), "warning", name=new_group["name"])
                skipped_groups.append({
                    "name": new_group["name"],
                    "reason": reason
                })
                details.append({
                    "name": new_group["name"],
                    "status": _("status.skipped"),
                    "reason": reason,
                    "original": group
                })
                continue

            # 标准化成员列表
            if members_field == "member":
                if isinstance(new_group["member"], str):
                    new_group["members"] = [new_group["member"]]
                elif isinstance(new_group["member"], list):
                    new_group["members"] = new_group["member"]
                else:
                    new_group["members"] = []
            # 如果已经是members字段，则保持不变
            
            # 验证每个成员是否存在，支持预定义服务映射
            valid_members = []
            invalid_members = []
            for member_name in new_group["members"]:
                # 移除可能的引号
                if member_name.startswith('"') and member_name.endswith('"'):
                    member_name = member_name[1:-1]

                member_found = False

                # 首先检查成员是否直接存在于转换后的服务对象中
                if member_name in service_objects:
                    valid_members.append(member_name)
                    member_found = True
                    log(_("debug.service_group_member_found_directly"), "debug", member=member_name)
                # 然后检查是否存在映射关系（原始名称 -> 映射后名称）
                elif member_name in service_mapping_relationships:
                    mapped_name = service_mapping_relationships[member_name]
                    # 对于预定义服务映射，不需要检查映射后的名称是否在service_objects中
                    # 因为预定义服务不会出现在转换后的服务对象列表中
                    valid_members.append(mapped_name)
                    member_found = True
                    log(_("debug.service_group_member_found_via_mapping"), "debug",
                        original=member_name, mapped=mapped_name)
                    log(_("info.service_group_member_mapped"),
                        original=member_name, mapped=mapped_name, group=new_group["name"])

                if not member_found:
                    invalid_members.append(member_name)
                    log(_("warning.service_group_member_not_found"), "warning",
                        group=new_group["name"], member=member_name)
            
            # 更新成员列表
            new_group["members"] = valid_members
            
            # 如果没有有效成员，则跳过此服务组
            if not valid_members:
                reason = _("reason.service_group_no_valid_members", name=new_group["name"])
                log(_("warning.service_group_no_valid_members"), "warning", name=new_group["name"])
                skipped_groups.append({
                    "name": new_group["name"],
                    "reason": reason
                })
                details.append({
                    "name": new_group["name"],
                    "status": _("status.skipped"),
                    "reason": reason,
                    "original": group,
                    "invalid_members": invalid_members
                })
                continue
            
            # 添加到转换成功列表
            converted_groups.append(new_group)
            
            # 日志记录
            log(_("info.processing_service_group"), name=new_group["name"], 
                valid=len(valid_members), invalid=len(invalid_members))
            
            details.append({
                "name": new_group["name"],
                "status": _("status.success"),
                "reason": "",
                "original": group,
                "converted": new_group,
                "valid_members": valid_members,
                "invalid_members": invalid_members
            })
            
        except Exception as e:
            error_msg = str(e)
            group_name = group.get("name", f"group_{idx+1}")
            error_msg = _("error.service_group_processing_error", name=group_name, error=error_msg)
            log(error_msg, "error")
            
            # 添加到失败列表
            failed_groups.append({
                "name": group_name,
                "reason": error_msg
            })
            details.append({
                "name": group_name,
                "status": _("status.failed"),
                "reason": error_msg,
                "original": group
            })
    
    log(_("info.service_groups_processing_complete"), 
        total=len(config_data['service_groups']), 
        success=len(converted_groups), 
        failed=len(failed_groups), 
        skipped=len(skipped_groups))
    
    return {
        "converted": converted_groups,
        "failed": failed_groups,
        "skipped": skipped_groups,
        "details": details
    }

def process_time_ranges(config_data):
    """
    处理时间对象，将飞塔（FortiGate）时间对象转换为NTOS格式
    
    Args:
        config_data (dict): 解析后的配置数据
    
    Returns:
        dict: 处理结果，包含converted(成功), failed(失败), skipped(跳过)和details(详情)
    """
    log(_("info.processing_time_ranges"))
    
    converted_time_ranges = []
    failed_time_ranges = []
    skipped_time_ranges = []
    details = []
    
    # 确保配置数据包含时间对象
    if "time_ranges" not in config_data or not config_data["time_ranges"]:
        log(_("info.no_time_ranges_found"), "info")
        return {
            "converted": converted_time_ranges,
            "failed": failed_time_ranges,
            "skipped": skipped_time_ranges,
            "details": details
        }
    
    # 使用新的转换模块处理时间对象
    try:
        from engine.processors.schedule_converter import convert_fortigate_schedule
        converted_time_ranges = convert_fortigate_schedule(config_data["time_ranges"])
        
        # 记录转换成功的时间对象
        for time_range in converted_time_ranges:
            time_range_name = time_range.get("name", "")
            log(_("info.time_range_converted"), name=time_range_name)
            details.append({
                "name": time_range_name,
                "status": _("status.success"),
                "reason": "",
                "original": next((t for t in config_data["time_ranges"] if t.get("name") == time_range_name), {}),
                "converted": time_range
            })
    except Exception as e:
        error_msg = str(e)
        error_msg = _("error.time_range_conversion_failed", error=error_msg)
        log(error_msg, "error")
        # 将所有时间对象标记为失败
        for time_range in config_data["time_ranges"]:
            time_range_name = time_range.get("name", _("time_range.unnamed"))
            failed_time_ranges.append({
                "name": time_range_name,
                "reason": error_msg
            })
            details.append({
                "name": time_range_name,
                "status": _("status.failed"),
                "reason": error_msg,
                "original": time_range
            })
    
    log(_("info.time_ranges_processing_complete"), 
        total=len(config_data['time_ranges']), 
        success=len(converted_time_ranges), 
        failed=len(failed_time_ranges), 
        skipped=len(skipped_time_ranges))
    
    return {
        "converted": converted_time_ranges,
        "failed": failed_time_ranges,
        "skipped": skipped_time_ranges,
        "details": details
    }
