{"version": "1.0.0", "description_key": "capacity.config.description", "last_updated": "2025-01-01", "device_models": {"z3200s": {"name": "Z3200S", "description_key": "capacity.device.z3200s.description", "vendor": "rui<PERSON>e", "category": "mid-range", "limits": {"dns_servers": {"max": 3, "description_key": "capacity.resource.dns_servers.description", "severity": "error", "suggestion_key": "capacity.resource.dns_servers.suggestion", "category": "network"}, "sub_interfaces": {"max": 128, "description_key": "capacity.resource.sub_interfaces.description", "severity": "error", "suggestion_key": "capacity.resource.sub_interfaces.suggestion.z3200s", "category": "interface"}, "pppoe_sessions": {"max": 16, "description_key": "capacity.resource.pppoe_sessions.description", "severity": "error", "suggestion_key": "capacity.resource.pppoe_sessions.suggestion", "category": "interface"}, "static_routes_v4": {"max": 1000, "description_key": "capacity.resource.static_routes_v4.description", "severity": "error", "suggestion_key": "capacity.resource.static_routes_v4.suggestion", "category": "routing"}, "nat44_policies": {"max": 250, "description_key": "capacity.resource.nat44_policies.description", "severity": "error", "suggestion_key": "capacity.resource.nat44_policies.suggestion.z3200s", "category": "policy"}, "security_policies": {"max": 3000, "description_key": "capacity.resource.security_policies.description", "severity": "error", "suggestion_key": "capacity.resource.security_policies.suggestion.z3200s", "category": "policy"}, "address_objects": {"max": 1024, "description_key": "capacity.resource.address_objects.description", "severity": "error", "suggestion_key": "capacity.resource.address_objects.suggestion", "category": "object"}, "address_groups": {"max": 512, "description_key": "capacity.resource.address_groups.description", "severity": "error", "suggestion_key": "capacity.resource.address_groups.suggestion", "category": "object"}, "service_objects": {"max": 1024, "description_key": "capacity.resource.service_objects.description", "severity": "error", "suggestion_key": "capacity.resource.service_objects.suggestion", "category": "object"}, "service_groups": {"max": 512, "description_key": "capacity.resource.service_groups.description", "severity": "error", "suggestion_key": "capacity.resource.service_groups.suggestion", "category": "object"}, "time_objects": {"max": 256, "description_key": "capacity.resource.time_objects.description", "severity": "error", "suggestion_key": "capacity.resource.time_objects.suggestion", "category": "object"}}}, "z5100s": {"name": "Z5100S", "description_key": "capacity.device.z5100s.description", "vendor": "rui<PERSON>e", "category": "high-end", "limits": {"dns_servers": {"max": 3, "description_key": "capacity.resource.dns_servers.description", "severity": "error", "suggestion_key": "capacity.resource.dns_servers.suggestion", "category": "network"}, "sub_interfaces": {"max": 128, "description_key": "capacity.resource.sub_interfaces.description", "severity": "error", "suggestion_key": "capacity.resource.sub_interfaces.suggestion.z5100s", "category": "interface"}, "pppoe_sessions": {"max": 16, "description_key": "capacity.resource.pppoe_sessions.description", "severity": "error", "suggestion_key": "capacity.resource.pppoe_sessions.suggestion", "category": "interface"}, "static_routes_v4": {"max": 1000, "description_key": "capacity.resource.static_routes_v4.description", "severity": "error", "suggestion_key": "capacity.resource.static_routes_v4.suggestion", "category": "routing"}, "nat44_policies": {"max": 300, "description_key": "capacity.resource.nat44_policies.description", "severity": "error", "suggestion_key": "capacity.resource.nat44_policies.suggestion.z5100s", "category": "policy"}, "security_policies": {"max": 4000, "description_key": "capacity.resource.security_policies.description", "severity": "error", "suggestion_key": "capacity.resource.security_policies.suggestion.z5100s", "category": "policy"}, "address_objects": {"max": 1024, "description_key": "capacity.resource.address_objects.description", "severity": "error", "suggestion_key": "capacity.resource.address_objects.suggestion", "category": "object"}, "address_groups": {"max": 512, "description_key": "capacity.resource.address_groups.description", "severity": "error", "suggestion_key": "capacity.resource.address_groups.suggestion", "category": "object"}, "service_objects": {"max": 1024, "description_key": "capacity.resource.service_objects.description", "severity": "error", "suggestion_key": "capacity.resource.service_objects.suggestion", "category": "object"}, "service_groups": {"max": 512, "description_key": "capacity.resource.service_groups.description", "severity": "error", "suggestion_key": "capacity.resource.service_groups.suggestion", "category": "object"}, "time_objects": {"max": 256, "description_key": "capacity.resource.time_objects.description", "severity": "error", "suggestion_key": "capacity.resource.time_objects.suggestion", "category": "object"}}}}, "validation_rules": {"enabled": true, "default_severity": "error", "skip_on_conversion_mode": false, "report_format": "detailed", "usage_thresholds": {"warning": 0.8, "critical": 0.95}}, "metadata": {"schema_version": "1.0", "supported_vendors": ["fortigate"], "supported_targets": ["rui<PERSON>e"], "created_by": "FortiGate to NTOS Converter", "notes": "配置文件基于设备厂商提供的技术规格文档"}}