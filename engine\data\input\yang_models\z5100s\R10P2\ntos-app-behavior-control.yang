module ntos-app-behavior-control {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:app-behavior-control";
  prefix ntos-app-behavior-control;

  import ntos {
    prefix ntos;
  }

  import ntos-types {
    prefix ntos-types;
  }

  import ntos-extensions {
    prefix ntos-ext;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS config app-behavior-control module.";

  revision 2024-01-15 {
    description
      "Create.";
    reference
      "";
  }

  typedef qq-control-mode {
    type enumeration {
      enum blacklist {
        description
          "Indicate the mode of blacklist.";
      }
      enum whitelist {
        description
          "Indicate the mode of whitelist.";
      }
    }
    description
      "The control mode of the im qq.";
  }

  grouping qq-control-obj {
    description
      "The configuration of im qq control.";

    leaf control-mode {
      type qq-control-mode;
      default blacklist;
      description
        "Mode of the qq control.";
    }

    list whitelist {
      key "id";
      description
        "The whitelist of the qq account.";
      leaf id {
        type string;
        description
          "Id of the qq account.";
      }
    }

    list blacklist {
      key "id";
      description
        "The blacklist of the qq account.";
      leaf id {
        type string;
        description
          "Id of the qq account.";
      }
    }
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "app-behavior-control configuration.";
    container app-behavior-control {
      description
        "app-behavior-control configuration.";

      container qq-control {
        description
          "The configuration of im qq control.";

        uses qq-control-obj;
      }
    }
  }

  rpc show-qq-control {
    description
      "Show qq control information.";

    input {
      leaf vrf {
        type string;
        default "main";
        description
          "The name of VRF.";
      }

      leaf start {
        type uint32;
        description
          "The start offset.";
      }

      leaf end {
        type uint32;
        description
          "The end offset.";
      }

      leaf control-mode {
        type qq-control-mode;
        default blacklist;
        description
          "Mode of the qq control.";
      }

      leaf id {
        type string;
        description
          "Id of the qq account.";
      }
    }

    output {
      leaf control-mode {
        type qq-control-mode;
        description
          "Mode of the qq control.";
      }

      leaf account-num {
        type uint32;
        description
          "The total number of account.";
      }

      list account-list {
        key "id";
        description
          "The list of the qq account.";
        leaf id {
          type string;
          description
            "Id of the qq account.";
        }
      }
    }
    ntos-ext:nc-cli-show "app-behavior-control qq-control";
  }
}