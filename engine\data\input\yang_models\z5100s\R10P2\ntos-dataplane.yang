module ntos-dataplane {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:dataplane-dsa";
  prefix ntos-dataplane;

  import ntos {
    prefix ntos;
  }
  import ntos-system {
    prefix ntos-system;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-commands {
    prefix ntos-cmd;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-api {
    prefix ntos-api;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS dataplane-hash module.";

  revision 2023-12-08 {
    description
      "Initial version.";
    reference "";
  }

  rpc show-switch-interface {
    input {
      leaf name {
        type ntos-types:ifname;
        description
          "Show interface by this name.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos-interface:interface/*/*[local-name()='name']";
      }

      leaf detail {
        type empty;
        description
          "Show detailed information of switch panel port";
      }

      leaf counters {
        type empty;
        description
          "Show statistical information of switch panel port";
      }
    }
    output {
      leaf buffer {
        type string;

        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "switch interface";
  }

  rpc show-switch-vlan {
    input {
      leaf vid {
        type uint16;
      }

      leaf detail {
        type empty;
      }
    }
    output {
      leaf buffer {
        type string;

        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "switch vlan";
  }

  rpc show-switch-fdb {
    input {
      leaf detail {
        type empty;
      }
    }
    output {
      leaf buffer {
        type string;

        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "switch fdb";
  }

  rpc show-switch-info {
    input {
      leaf detail {
        type empty;
      }
    }
    output {
      leaf buffer {
        type string;

        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "switch info";
  }

  rpc show-switch-trunk {
    input {
      leaf detail {
        type empty;
      }
    }
    output {
      leaf buffer {
        type string;

        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "switch trunk";
  }

  rpc show-switch-table {
    input {
      leaf detail {
        type empty;
      }
    }
    output {
      leaf buffer {
        type string;

        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "switch table";
  }

  rpc clear-interface-counters {
    input {
      leaf name {
        type ntos-types:ifname;
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos-interface:interface/*/*[local-name()='name']";
      }

      leaf counters {
        type empty;
      }
    }
    output {
      leaf buffer {
        type string;

        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-cmd "clear switch interface";
  }

  rpc show-dataplane-hash {
    description
      "Show the hash type of dataplane.";
    
    input {
      leaf valid-ports {
        type empty;
      }
    }

    output {
      leaf buffer {
        description
          "Hash type.";
        type string;
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }

    ntos-ext:nc-cli-show "dataplane hash";
  }

  rpc set-dataplane-hash {
    input {
      leaf type {
        type enumeration {
          enum hash-default {
            description
              "Default hash type.";
          }

          enum hash-bind {
            description
              "Bind front-panel port with designated cpu port.";
          }

          enum hash-adaptive {
            description
              "Bind front-panel port with cpu port adaptively. Note that in this mode, 
              packets sent to cpu ports do not have dsa tag.";
          }
        }

        description
          "Hash type for choosing cpu port";
      }

      leaf bind {
        type string;
        description
          "Usage: cpu_port0=port1,port2,...:cpu_port1=port3,port4,...\n
           \tFor which ports are valid to bind, please refer to command: show dataplane hash valid-ports\n
           \tNote: this option is only effective in hash-bind mode!";
      }
    }

    ntos-ext:nc-cli-cmd "set dataplane hash";
  }

  rpc set-dataplane-perf-mode {
    input {
      leaf enabled {
        type boolean;
        description
          "Set whether to enable perf-mode for performance test.";
      }
    }

    ntos-ext:nc-cli-cmd "set dataplane perf-mode";
  }

  rpc set-dataplane-port-tpid {
    description
      "Set cpu port default tpid for vlan tag offload.\nUsed for load-balance of packets which carry non-8021q 
      vlan tpid (0x8100) or qinq tpid (0x88a8).";
    
    input {
      leaf name {
        type string;
        description
          "Cpu port name";
      }

      leaf cvlan {
        type string;
        description
          "Specify cvlan tpid. e.g., if the packet carries a 0x9100 tpid, then type 9100.";
      }

      leaf svlan {
        type string;
        description
          "Specify svlan tpid. e.g., if the packet carries a 0x9100 tpid, then type 9100.";
      }
    }

    output {
      leaf buffer {
        description
          "Cmd result.";
        type string;
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }

    ntos-ext:nc-cli-cmd "set dataplane port-tpid";
  }

  grouping hash-type-config {
    container hash {
      leaf type {
        type enumeration {
          enum hash-default {
            description
              "Default hash type";
          }

          enum hash-bind {
            description
              "Bind front-panel port with designated cpu port";
          }

          enum hash-adaptive {
            description
              "Bind front-panel port with cpu port adaptively. Note that in this mode, 
              packets sent to cpu ports do not have dsa tag";
          }
        }

        default hash-default;
        description
          "Hash type for choosing cpu port";
      }

      leaf bind {
        type string;
        description
          "Usage: cpu_port0=port1,port2,...:cpu_port1=port3,port4,...\n
           \tFor which ports are valid to bind, please refer to command: show dataplane hash valid-ports\n
           \tNote: this option is only effective in hash-bind mode!";
        
        default "none";
      }
    }
  }

  grouping port-tpid-config {
    description
      "Cpu port tpid config.";

    container port-tpid {
      list port {
        key "name";
        leaf name {
          type string;
          description
            "Cpu port name";
        }

        leaf cvlan {
          type string;
          description
            "Specify cvlan tpid. e.g., if the packet carries a 0x9100 tpid, then type 9100.";
        }

        leaf svlan {
          type string;
          description
            "Specify svlan tpid. e.g., if the packet carries a 0x9100 tpid, then type 9100.";
        }
      }
    }
  }

  augment "/ntos:config/ntos-system:system" {
    description
      "Dataplane configuration.";

    container dataplane {
      uses hash-type-config;
      uses port-tpid-config;
    }
  }

  augment "/ntos:state/ntos-system:system" {
    description
      "Dataplane configuration state.";

    container dataplane {
      uses hash-type-config;
      uses port-tpid-config;
    }
  }
}
