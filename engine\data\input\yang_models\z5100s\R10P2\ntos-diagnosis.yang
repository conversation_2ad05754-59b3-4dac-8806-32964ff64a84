module ntos-diagnosis {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:diagnosis";
  prefix ntos-diagnosis;

  import ntos-extensions {
    prefix ntos-ext;
  }

  import ntos-api {
    prefix ntos-api;
  }

  import ntos-types {
    prefix ntos-types;
  }

  import ntos-inet-types {
    prefix ntos-inet;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS diagnosis module.";

  revision 2022-08-28 {
    description
      "Show services start duration.";
    reference
      "";
  }

  revision 2022-02-18 {
    description
      "Update trace filter command.";
    reference
      "";
  }

  revision 2022-01-07 {
    description
      "Show yang version info.";
    reference
      "";
  }

  revision 2021-12-31 {
    description
      "Update trace command.";
    reference
      "";
  }

  revision 2021-12-24 {
    description
      "Add trace set command.";
    reference
      "";
  }

  revision 2021-12-07 {
    description
      "Fix  get level emergency error.";
    reference
      "";
  }

  revision 2021-06-08 {
    description
      "Initial version.";
    reference
      "";
  }

  typedef submodule-name {
    type union {
      type enumeration {
        enum all {
          description
            "All submodule.";
        }
      }

      type string;
    }
  }

  typedef service-name {
    type union {
      type identityref {
        base ntos-types:SERVICE_LOG_ID;
      }

      type enumeration {
        enum yams;
      }
      type string;
    }
  }

  rpc debug-enabled {
    ntos-api:internal;
    ntos-ext:nc-cli-cmd "debug";
    description
      "Enable or disable debug function.";

    input {
      leaf service {
        type union {
          type service-name;
          type enumeration {
            enum all {
              description
                "All service.";
            }
          }
        }

        mandatory true;
        description
          "Name of service.";
      }

      leaf enabled {
        type boolean;
        mandatory true;
        description
          "Enable or disable debug function.";
      }
    }
  }

  rpc debug-set {
    ntos-api:internal;
    ntos-ext:nc-cli-cmd "debug-set";
    description
      "Set debug log level.";
    input {
      leaf service {
        type union {
          type service-name;
          type enumeration {
            enum all {
              description
                "All service.";
            }
          }
        }

        mandatory true;
        description
          "Name of service.";
      }
      leaf submodule {
        type submodule-name;
        must "(../service='all' and current()='all') or not(../service = 'all')";
        default "all";
        description
          "Name of submodule.";
      }
      leaf level {
        type ntos-types:log-level;

        default "warning";
        description
          "Lowest debug level.";
       }
    }
  }

  rpc debug-get {
    ntos-api:internal;
    ntos-ext:nc-cli-cmd "debug-get";
    ntos-ext:nc-cli-show "debug-level";
    description
      "Get debug log level.";
    input {
      leaf service {
        type service-name;
        mandatory true;
        description
          "Name of service.";
      }
      leaf submodule {
        type submodule-name;
        default "all";
        description
          "Name of submodule.";
      }
    }

    output {
      leaf service {
        type string;
        description
          "Name of service.";
      }

      leaf enabled {
        type string;
        description
          "Enable or disable debug function.";
      }

      list submodule {
        key name;
        leaf name {
          type submodule-name;
          description
            "Name of submodule.";
        }
        leaf level {
          type ntos-types:log-level;
          description
            "Lowest debug level.";
        }
      }
    }
  }

  rpc debug-statistics {
    ntos-api:internal;
    ntos-ext:nc-cli-show "debug-statistics";
    description
      "Get debug statistics.";

    output {
      leaf version-info {
        type string;
        description
          "Yang version information.";
      }
      container start-duration {
        description
        "Startup duration of service.";
        list service {
          key name;
          leaf name {
            type string;
            description
              "Name of service.";
          }
          leaf duration {
            type string;
            ntos-ext:nc-cli-no-name;
            description
              "Startup duration (second).";
          }
        }
      }

    }
  }

  rpc services-start-duration {
    ntos-api:internal;
    ntos-ext:nc-cli-show "services-start-duration";
    description
      "Get services start duration statistics.";

    output {
      leaf version-info {
        type string;
        description
          "Yang version information.";
      }
      container start-total-duration {
        description
          "Total start duration of all services.";
        leaf load-total-duration {
            type string;
            description
              "Total load duration of all services (second).";
        }
        leaf init-total-duration {
          type string;
          description
            "Total init duration of all services (second).";
        }
        leaf connection-duration {
          type string;
          description
            "Total connection duration (second).";
        }
        leaf startup-total-duration {
          type string;
          description
            "Total startup duration of all services (second).";
        }
      }
      container start-duration {
        description
          "Start duration of service.";
        list service {
          key name;
          leaf name {
            type string;
            description
              "Name of service.";
          }
          leaf load-duration {
            type string;
            description
              "Load duration of service (second).";
          }
          leaf init-duration {
            type string;
            description
              "Init duration of service (second).";
          }
          leaf startup-duration {
            type string;
            description
              "Startup duration of service (second).";
          }
          leaf start-apply-duration {
            type string;
            description
              "Start apply duration of service (second).";
          }
          leaf validate-duration {
            type string;
            description
              "Validate duration of service (second).";
          }
          leaf update-duration {
            type string;
            description
              "Update duration of service (second).";
          }
          leaf apply-duration {
            type string;
            description
              "Apply duration of service (second).";
          }
        }
      }
    }
  }

  rpc services-start-mem {
    ntos-api:internal;
    ntos-ext:nc-cli-show "services-start-mem";
    description
      "Get services start used memory statistics.";

    output {
      container start-mem {
        description
          "Start used memory of service.";
        list service {
          key name;
          leaf name {
            type string;
            description
              "Name of service.";
          }
          leaf load-mem {
            type string;
            description
              "Load memory of service (M).";
          }
          leaf init-mem {
            type string;
            description
              "Init memory of service (M).";
          }
        }
      }
    }
  }

  rpc trace {
    ntos-api:internal;
    ntos-ext:nc-cli-cmd "trace";

    description
      "Set trace parameters.";

    input {
      leaf level {
        type enumeration {
          enum EMERG;
          enum ALERT;
          enum CRIT;
          enum ERR;
          enum WARNING;
          enum NOTICE;
          enum INFO;
          enum DEBUG;
        }
        description
          "trace level";
      }
      leaf max-number {
        type uint16 {
          range "0..65535";
        }
        default "5000";
        description
          "maxmum trace number";
      }
      leaf timeout {
        type uint16 {
          range "0..65535";
        }
        default "600";
        description
          "trace tmeout value";
      }
      leaf type-on {
        type string;
        description
          "trace type enabled";
      }
      leaf type-off {
        type string;
        description
          "trace type disabled";
      }
    }
  }

  rpc trace-filter {
    ntos-api:internal;
    ntos-ext:nc-cli-cmd "trace-filter";

    description
      "Set trace filter parameters.";

    input {
      leaf enabled {
        type boolean;
        description
          "enable or disable trace filter";
      }
      leaf proto {
        type uint8 {
          range "0..255";
        }
      }
      leaf saddr {
        type ntos-inet:ipv4-address;
      }
      leaf sport {
        type uint16 {
          range "0..65535";
        }
      }
      leaf daddr {
        type ntos-inet:ipv4-address;
      }
      leaf dport {
        type uint16 {
          range "0..65535";
        }
      }
      leaf ifid1 {
        type uint16 {
          range "0..65535";
        }
      }
      leaf ifid2 {
        type uint16 {
          range "0..65535";
        }
      }
    }
  }

  rpc trace-set {
    ntos-api:internal;
    ntos-ext:nc-cli-cmd "trace-set";

    description
      "Set packet trace parameters";
    input {
      leaf level {
        type enumeration {
          enum EMERG;
          enum ALERT;
          enum CRIT;
          enum ERROR;
          enum WARNING;
          enum NOTICE;
          enum INFO;
          enum DEBUG;
        }
        description
          "Packet trace log level";
      }

      leaf timeout {
        type uint16 {
          range "0..3600";
        }
        description
          "Packet trace timeout value";
      }

      leaf max-log-num {
        type uint64 {
          range "0..2000000";
        }
        description
          "Packet trace maximum log number";
      }

      leaf max-pkt-num {
        type uint64 {
          range "0..100000";
        }
        description
          "Packet trace maximum packet number";
      }

      leaf proto {
        type string;
        description
          "Set proto by name";
      }

      leaf ifname {
        type string;
        description
          "Set entry interface name";
      }

      leaf saddr {
        type union {
          type ntos-inet:ipv4-address;
          type ntos-inet:ipv6-address;
          type ntos-inet:domain-name;
        }
        description
          "Set source IP address";
      }

      leaf sport {
        type uint16 {
          range "0..65535";
        }
        description
          "Set source port";
      }

      leaf daddr {
        type union {
          type ntos-inet:ipv4-address;
          type ntos-inet:ipv6-address;
          type ntos-inet:domain-name;
        }
        description
          "Set destination IP address";
      }

      leaf dport {
        type uint16 {
          range "0..65535";
        }
        description
          "Set destintion port";
      }

      leaf smac {
        type string;
        description
          "Set source mac address.The address is separated by ':'";
      }

      leaf dmac {
        type string;
        description
          "Set destination mac address.The address is separated by ':'";
      }

      leaf output-mode {
        type string;
        description
          "output-mode file or terminal";
      }

      leaf type-on {
        type string;
        description
          "trace type enabled";
      }

      leaf type-off {
        type string;
        description
          "trace type disabled";
      }

      leaf pcap-filter {
        type string {
          length '0..255';
        }
        description
          "Optional filter expression. This must be a valid PCAP filter.
           See https://www.tcpdump.org/manpages/pcap-filter.7.html for more
           details.";
      }
    }

    output {
      leaf result {
        type string;
        description
          "The result of setting packet trace paramter";
        ntos-ext:nc-cli-stdout;
      }
    }
  }

  rpc trace-get {
    ntos-api:internal;
    ntos-ext:nc-cli-cmd "trace-get";

    description
      "Get packet trace paramter";

    output {
      leaf result{
        type string;
        description
          "The result of getting packet trace paramter";
        ntos-ext:nc-cli-stdout;
      }
    }
  }

  rpc trace-reset {
    ntos-api:internal;
    ntos-ext:nc-cli-cmd "trace-reset";

    description
      "reset packet trace parameter";
  }

  rpc trace-stop {
    ntos-api:internal;
    ntos-ext:nc-cli-cmd "trace-stop";

    description
      "Stop packet trace task";
  }

  rpc fp-debug-support {
    input {
      leaf debugger {
        mandatory true;
        type enumeration {
          enum fp;
          enum npf;
          enum bsp;
          enum cmd;
          enum ike;
          enum edge;
        }
        ntos-ext:nc-cli-no-name;
      }

      leaf exec {
        type string {
          length '0..255';
        }
        default "";
      }
    }

    output {
      leaf buffer {
        type string;
        description
          "Command output buffer since last request.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-command-no-pager;
    ntos-ext:nc-cli-cmd "debug-support";
  }
}
