module ntos-dm-types {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:dm-types";
  prefix ntos-dm-types;
  
  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS device management types defition module.";

  revision 2022-09-03 {
    description
      "Initial version.";
    reference "";
  }
  
  typedef power-status {
	type enumeration {
		enum normal {
		    description "The power is noraml.";	
		}
		
		enum unknown {
		    description "The status except for mentioned above.";
		}
		
	    enum no-exist {
		    description "The power is not in the corresponding slot.";
        }
		
        enum no-power {
		    description "The power is in the corresponding slot, but not powered.";
		}
		
		enum ready-power {
		    description "The power is in ready.";	
		}
		
		enum abnormal {
		    description "The power is abnoraml.";
		}
		
		enum line-fail {
		    description "line fail.";
		}
	}
  }
  
  typedef severity-level {
    description 
		"The severity level of the module status alarm.";

	type enumeration {
		enum normal {
			description
			   "Indicate that the module status has already changed to normal.";
		}
			
		enum warning {
			description
			   "Indicate that the module status has already changed to warning.";
		}
			
		enum critical {
			description
			    "Indicate that the module status has already changed to critical.";
		}

		enum emergent {
			description
			    "Indicate that the module status has already changed to emergent.";
		}
	}
  }
}