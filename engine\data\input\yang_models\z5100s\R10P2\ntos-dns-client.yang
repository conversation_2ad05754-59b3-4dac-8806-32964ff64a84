module ntos-dns-client {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:dns-client";
  prefix ntos-dns-client;

  import ntos {
    prefix ntos;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos-types {
    prefix ntos-types;
  }
  
  organization 
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel: 4008-111-000
    E-mail: <EMAIL>";
  description
    "Ruijie NTOS DNS client module.";

  revision 2022-04-07 {
    description
      "Initial version.";
  }

  identity dns-client {
    base ntos-types:SERVICE_LOG_ID;
    description
      "DNS client service.";
  }
  
  typedef dns-types {
    type enumeration {
      enum static {
        description
          "Static DNS.";
      }
	  
      enum dynamic {
		description
          "Dynamic DNS.";
      }
    }
    description
      "The type of DNS, can be either static or dynamic.";
  }
  
  typedef domain-name {
    type string {
      length "1..253";
      pattern '(([a-zA-Z0-9]([a-zA-Z0-9\-]){0,61})?[a-zA-Z0-9]\.)+(([a-zA-Z0-9]([a-zA-Z0-9\-]){0,61})?[a-zA-Z0-9]\.?)+';
      ntos-ext:nc-cli-shortdesc "<domain-name>";
    }
    description
      "The domain-name type represents a DNS domain name.
       Fully quallified left to the models which utilize this type.

       Internet domain names are only loosely specified.  Section
       3.5 of RFC 1034 recommends a syntax (modified in Section
       2.1 of RFC 1123).  The pattern above is intended to allow
       for current practice in domain name use, and some possible
       future expansion.  It is designed to hold various types of
       domain names, including names used for A or AAAA records
       (host names) and other records, such as SRV records.  Note
       that Internet host names have a stricter syntax (described
       in RFC 952) than the DNS recommendations in RFCs 1034 and
       1123, and that systems that want to store host names in
       schema nodes using the domain-name type are recommended to
       adhere to this stricter standard to ensure interoperability.

       The encoding of DNS names in the DNS protocol is limited
       to 255 characters.  Since the encoding consists of labels
       prefixed by a length bytes and there is a trailing NULL
       byte, only 253 characters can appear in the textual dotted
       notation.

       Domain-name values use the US-ASCII encoding.  Their canonical
       format uses lowercase US-ASCII characters.  Internationalized
       domain names MUST be encoded in punycode as described in RFC
       3492.";
  }
  
  grouping dns-client-config {
    description
      "Configuration data for DNS client.";
  
    container ip-domain-lookup {
      description
        "Enable the DNS for domain name resolution 
      or enable the DNS for domain name resolution and specify the DNS source interface and address.";
      
      leaf enabled {
      type boolean;
      default "true";
      description
        "Enable or disable DNS for domain name resolution.";
      }
      
      leaf source-interface {
        type ntos-types:ifname;
      description
          "NTOS interface name.";
      }
      
      leaf source-address {
      type union {
        type ntos-inet:ipv4-filter-invalid-address;
        type ntos-inet:ipv6-address;
      }
      description
        "Ipv4 address or Ipv6 address.";
      }
    }
	
    list ip-host {
      description
      "Configures of static domain names.";
      
      max-elements 100;
      
      key "host-name";
      
      leaf host-name {
        type domain-name;
      description
          "Domain name.";
      }
        
      leaf ip-address {
        type ntos-inet:ip-address;
      description
          "The IPv4 address of the domain name.";
      }
      ntos-ext:nc-cli-one-liner;
    }
	  
    list ipv6-host {
      description
        "Configures of static ipv6 domain names.";

      max-elements 100;
      
      key "host-name";

      leaf host-name {
        type domain-name;
        description
          "Domain name.";
      }

      leaf ipv6-address {
        type ntos-inet:ipv6-address;
        description
        "The IPv6 address of the domain name.";
      }
      ntos-ext:nc-cli-one-liner;
    }
  }

  grouping dns-info {
    description
      "dns information.";
    
    list hosts-list {
      leaf host-name {
        type domain-name;
        description
          "Domain name.";
      }
        
      leaf dns-type {
        type dns-types;
        description
          "DNS type, can be either static or dynamic.";
      }
      
      leaf ip-address {
        type union {
          type ntos-inet:ipv4-filter-invalid-address;
          type ntos-inet:ipv6-address;
        }
        description
          "The address of the domain ip address, can be either IPv4 or IPv6.";
      }
      
      leaf TTL {
        type string{
          length "1..12";
        }
        description
          "Dynamic DNS TTL.";
      }
    }
  }
  
  augment "/ntos:config/ntos:vrf" {
    description
      "DNS client configuration.";

    container dns-client {
      description
        "DNS client configuration.";
      uses dns-client-config;
    }
  }

  augment "/ntos:state/ntos:vrf" {
    description
      "DNS client state.";

    container dns-client {
      description
        "DNS client state.";
      uses dns-client-config;
    }
  }
  
  rpc show-dns-client-hosts {
    description
      "Show DNS client hosts information.";
	  
    output {
	    container dns-client-info {
        description
          "Show DNS client hosts information.";
      
        uses dns-info;
      }
    }
    ntos-ext:nc-cli-show "dns-client-hosts";
    ntos-api:internal;
  }
  
  rpc flush-dns-client-host {
    description
      "Flush DNS client host.";
    input {
      leaf host {
        type string{
          length "1..253";
        }
        description
          "The host name records that are dynamically learned from DNS.";
      }
    }
    ntos-ext:nc-cli-flush "dns-client-host";
  }
  
  rpc dns-client-lookup {
    description
      "DNS client lookup domain name address.";
    input {
      leaf host-name {
        type domain-name;
        description
          "The host name records that are dynamically learned from DNS.";
      }
    }
    
    output {
      container dns-lookup-info {
        description
          "Show DNS client lookup information.";
      
        uses dns-info;
      }
    }
    ntos-ext:nc-cli-cmd "dns-client-lookup";
  }

  rpc print-dns-client-nameserver {
    description
      "Print dns-client nameserver to log.";
    ntos-ext:nc-cli-cmd "print-dns-client-nameserver";
  }

  rpc print-dns-client-agent-info {
    description
      "Print dns-client agent info to log.";
    ntos-ext:nc-cli-cmd "print-dns-client-agent-info";
  }

  rpc print-dns-client-cache-info {
    description
      "Print dns-client cache info to log.";
    ntos-ext:nc-cli-cmd "print-dns-client-cache-info";
    ntos-ext:nc-cli-hidden;
  }
}