module ntos-dns {
  namespace "urn:ruijie:ntos:params:xml:ns:yang:dns";
  prefix ntos-dns;

  import ietf-netconf-acm {
    prefix nacm;
  }
  import ntos {
    prefix ntos;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-extensions {
    prefix ntos-extensions;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-dns-client {
    prefix ntos-dns-client;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS DNS module.";

  revision 2018-10-03 {
    description
      "Initial version.";
    reference "";
  }

  identity dns {
    base ntos-types:SERVICE_LOG_ID;
    description
      "DNS resolver service.";
  }

  grouping system-dns-config {
    description
      "DNS / resolver related configuration data.";

    leaf-list search {
      type ntos-inet:domain-name;
      ordered-by user;
      description
        "An ordered list of domains to search when resolving
         a host name.";
    }
  }

  grouping system-dns-servers-config {
    description
      "Configuration data for DNS resolvers.";
    //RFC 7317 includes a single-value choice statement to for
    //TCP and UDP transport.  This has been removed since it the
    //transport protocol is not generally available as an options
    //on target devices.  It may be added back if and when needed.

    leaf address {
      type union {
        type ntos-inet:ipv4-filter-invalid-address;
        type ntos-inet:ipv6-address;
      }
      description
        "The address of the DNS server, can be either IPv4
         or IPv6.";
    }
    //RFC 7317 includes resolver timeout and attempts options. These
    //have been omitted as they are not available on many targets. If
    //and when they are required, they may be added back in.
  }

  grouping domain-param {
    description
      "the param of domain.";

    leaf domain-name {
      type ntos-dns-client:domain-name;
    }
    leaf ttl {
      type uint32 {
        range "10..31536000";
      }
      units "seconds";
      default "3600";
      description
        "The domain name's time to live.";
    }
    leaf-list address {
      max-elements 8;
      type ntos-inet:ip-address;
    }
  }

  grouping dns-proxy-config-one {
    leaf-list interface {
        type ntos-types:ifname;
        description
          "Interface on which the DNS proxy should listen. If not
          specified, DNS proxy listens to all networks.";
        ntos-extensions:nc-cli-completion-xpath
          "../../../ntos-interface:interface/*/*[local-name()='name']";
      }

    container forward {
      description
        "Configure name servers to forward the DNS requests to. If not
        specified, requests are forwarded to local DNS servers (configured
        statically or obtained through DHCP).";

      list server {
        key "seq";
        max-elements 3;
        ntos-extensions:nc-cli-one-liner;
        description
          "The address of the DNS servers, can be either IPv4 or IPv6.";

        leaf seq {
          type uint32 {
            range "1..3" {
              error-message
                "The priority must in 1~3.";
            }
          }
          description
            "Dns server address seq.";
        }

        leaf addr {
          mandatory true;
          type ntos-inet:ip-address;
          description
            "Dns server address max = 3.";
          ntos-extensions:nc-cli-no-name;
        }
      }

      leaf local {
        type empty;
        description
          "Forward DNS requests to local DNS servers (configured statically or
          obtained through DHCP).";
      }

      leaf priority {
        type enumeration {
          enum server {
            description
              "Forward DNS requests to configured server preferentially.";
          }
          enum local {
            description
              "Forward DNS requests to local DNS servers preferentially.";
          }
        }
        description
          "Forward DNS requests to configured DNS servers or
          local DNS servers preferentially.";
      }
    }

    leaf-list allow-query {
      type union {
        type ntos-inet:ip-prefix;
        type ntos-inet:ip-address;
      }
      description
        "Specifies which hosts (an IP address list) are allowed to send queries to this resolver.";
    }
  }

  grouping dns-proxy-config {
    description
      "Configuration data for DNS proxy.";

    leaf enabled {
      type boolean;
      default "false";
      description
        "Enable or disable DNS proxy. By default, DNS proxy listens to
         requests on all networks and forwards them to local DNS servers
         (configured statically or obtained through DHCP).";
    }

    choice query-mode {
      case route {
        container query-route {
          description
            "DNS proxy send querys to the server through route.";
          uses dns-proxy-config-one;
        }
      }
      case assign-interface {
        container query-assign-interface {
          description
            "DNS proxy send querys to the server through assign interface.";
          list view {
            max-elements 5;
            key "out-interface";
            description
              "The config of DNS proxy view.";

            leaf out-interface {
              type ntos-types:ifname;
              description
                "Interface on which the DNS proxy request should out.";
              ntos-extensions:nc-cli-completion-xpath
                "../../../ntos-interface:interface/*/*[local-name()='name']";
            }
            uses dns-proxy-config-one;
          }
        }
      }
    }

    leaf-list blackhole {
      type union {
        type ntos-inet:ip-prefix;
        type ntos-inet:ip-address;
      }
      description
        "Specifies which hosts (an IP address list) the server does not accept
        queries from or use to resolve a query. Queries from these addresses
        are not responded to. The default is none.";
    }

    list static-dns {
      description
        "Configure the mapping between domain names and ip addresses.";
      key "domain-name";

      must "count(address) > 0" {
        error-message "the number of address must not be less than one.";
      }
      uses domain-param;
    }

    // list dns64 {
    //   key "prefix";
    //   description
    //     "DNS64 configuration.";

    //   leaf prefix {
    //     type ntos-inet:ipv6-prefix;
    //     description
    //       "Network prefix.";
    //     ntos-extensions:nc-cli-completion-xpath
    //       "../../../ntos-nat:nat/ntos-nat:rule/ntos-nat:translate-to/ntos-nat:destination-prefix";
    //   }

    //   leaf-list client {
    //     type ntos-inet:ipv6-prefix;
    //     min-elements 1;
    //     description
    //       "Clients IPv6 addresses.";
    //     ntos-extensions:nc-cli-completion-xpath
    //       "../../../ntos-nat:nat/ntos-nat:rule/ntos-nat:match/ntos-nat:source/ntos-nat:ipv6-address";
    //   }

    //   leaf-list exclude {
    //     type ntos-inet:ipv6-prefix;
    //     description
    //       "IPv6 addresses to exclude.";
    //     ntos-extensions:nc-cli-completion-xpath
    //       "../../../ntos-nat:nat/ntos-nat:rule/ntos-nat:translate-to/ntos-nat:destination-prefix";
    //   }

    //   container mapped {
    //     presence "Enable IPv4 mapped selection.";
    //     description
    //       "IPv4 prefixes to be map in the corresponding A resource record set. If not defined it defaults to any.";
    //     ntos-extensions:nc-cli-one-liner;

    //     leaf not {
    //       type empty;
    //       description
    //         "Do not map the following set of IPv4 prefixes.";
    //       ntos-extensions:nc-cli-order "1";
    //     }

    //     leaf-list prefix {
    //       type ntos-inet:ipv4-prefix;
    //       description
    //         "IPv4 prefixes to be mapped or not.";
    //       ntos-extensions:nc-cli-order "2";
    //       ntos-extensions:nc-cli-no-name;
    //     }
    //   }
    // }
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "Top-level grouping for DNS / resolver config and operational
       state data.";

    container dns {
      presence "Makes dns available";
      description
        "Enclosing container for DNS resolver data.";
      uses system-dns-config;

      list server {
        key "address";
        max-elements 3;
        ordered-by user;
        description
          "List of the DNS servers that the resolver should query.

           When the resolver is invoked by a calling application, it
           sends the query to the first name server in this list.  If
           no response has been received within 'timeout' seconds,
           the resolver continues with the next server in the list.
           If no response is received from any server, the resolver
           continues with the first server again.  When the resolver
           has traversed the list 'attempts' times without receiving
           any response, it gives up and returns an error to the
           calling application.

           Implementations MAY limit the number of entries in this
           list.";
        ntos-extensions:nc-cli-one-liner;
        uses system-dns-servers-config;
      }

      container proxy {
        presence "Makes proxy available";
        description
          "DNS proxy configuration.";
        uses dns-proxy-config;
      }
    }
  }

  augment "/ntos:state/ntos:vrf" {
    description
      "Top-level grouping for DNS / resolver config and operational
       state data.";

    container dns {
      description
        "Enclosing container for DNS resolver data.";
      uses system-dns-config;

      list server {
        key "address";
        ordered-by user;
        description
          "List of the DNS servers that the resolver should query.

           When the resolver is invoked by a calling application, it
           sends the query to the first name server in this list.  If
           no response has been received within 'timeout' seconds,
           the resolver continues with the next server in the list.
           If no response is received from any server, the resolver
           continues with the first server again.  When the resolver
           has traversed the list 'attempts' times without receiving
           any response, it gives up and returns an error to the
           calling application.

           Implementations MAY limit the number of entries in this
           list.";
        ntos-extensions:nc-cli-one-liner;
        uses system-dns-servers-config;
      }

      container proxy {
        description
          "DNS proxy operational state data.";
        uses dns-proxy-config;
      }
    }
  }

  rpc clear-dns-proxy-cache {
    description
      "Clear DNS proxy cache.";
    input {

      leaf vrf {
        type string;
        default "main";
        description
          "Specify the VRF.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }
    }
    nacm:default-deny-all;
    ntos-extensions:nc-cli-flush "dns-proxy cache";
  }

  rpc show-dns-proxy-cache {
    description
      "Show DNS proxy cache.";
    input {

      leaf vrf {
        type string;
        default "main";
        description
          "Specify the VRF.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }
    }

    output {
      leaf result {
        type string;
        description
          "The command output buffer.";
        ntos-extensions:nc-cli-stdout;
      }
    }
    ntos-extensions:nc-cli-hidden;
    ntos-extensions:nc-cli-show "dns-proxy cache";
  }

  rpc show-dns-proxy-static-domain-name {
    description
      "Show DNS proxy static domain name.";
    input {
      leaf vrf {
        type string;
        default "main";
        description
          "Specify the VRF.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }
      leaf page {
        type uint32{
          range "0..10000";
        }
        default "0";
        description
          "Page number.";
      }
      leaf num {
        type uint32{
          range "1..100";
        }
        default "10";
        description
          "Max number in one page.";
      }
      leaf search {
        type string {
          length "1..64";
        }
        description
          "search domain name with this key.";
      }
      leaf output-type {
        type enumeration {
          enum node-tree {
            description
              "Output format to yang module data.";
          }
          enum json-string {
            description
              "Output format to json.";
          }
        }
        default "node-tree";
        description
          "Output format to yang module data or json string.";
      }
    }
    output {
      choice output-type {
        case node-tree {
          leaf number {
            type int32;
            description
              "total domain name number.";
          }
          list domain-name {
            key "domain-name";
            uses domain-param;
          }
        }
        case json-string {
          leaf data {
            type string;
            description
              "The command output buffer.";
            ntos-extensions:nc-cli-stdout;
            ntos-extensions:nc-cli-hidden;
          }
        }
      }
    }
    ntos-extensions:feature "product";
    ntos-extensions:nc-cli-show "dns-proxy static-domain-name";
    ntos-api:internal;
  }

  rpc show-dns-proxy-statistics {
    description
      "Show DNS proxy statistics.";
    input {

      leaf vrf {
        type string;
        default "main";
        description
          "Specify the VRF.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }
    }

    output {
      leaf result {
        type string;
        description
          "The command output buffer.";
        ntos-extensions:nc-cli-stdout;
      }
    }
    ntos-extensions:nc-cli-hidden;
    ntos-extensions:nc-cli-show "dns-proxy statistics";
  }
}
