module ntos-evpn {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:evpn";
  prefix ntos-evpn;

  import ntos {
    prefix ntos;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-extensions {
    prefix ntos-extensions;
  }
  import ntos-commands {
    prefix ntos-cmd;
  }
  import ntos-if-types {
    prefix ntos-if;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos-routing {
    prefix ntos-rt;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-vlan {
    prefix ntos-vlan;
  }
  import ntos-vxlan {
    prefix ntos-vxlan;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "RUIJIE NTOS routing EVPN.";

  revision 2020-01-16 {
    description
      "Initial version.";
    reference "";
  }

  grouping evpn-arp-mac-common {
    description
      "Common info between ARP/neighbor discovery and MAC lists.";

    leaf type {
      type string;
      description
        "ARP/Neighbor discovery entry type.";
    }

    leaf remote-vtep-ip {
      type ntos-inet:ip-address;
      description
        "Remote virtual termination end point IP address.";
    }
  }

  grouping common-evpn-state {
    description
      "Operational EVPN state.";

    list vni {
      key "vni";
      description
        "Operational EVPN state for a specific VNI.";

      leaf vni {
        type ntos-vxlan:vni;
        description
          "VXLAN Network Identifier.";
      }

      leaf type {
        type string;
        description
          "VNI type (L2/L3).";
      }

      leaf vxlan {
        type ntos-types:ifname;
        description
          "VXLAN name.";
      }

      leaf num-mac {
        type uint32;
        description
          "Number of mac entries.";
      }

      leaf num-arp-nd {
        type uint32;
        description
          "Number of ARP and neighbor discovery entries.";
      }

      leaf local-vtep-ip {
        type ntos-inet:ip-address;
        description
          "Local virtual termination end point IP address.";
      }

      leaf advertise-gateway-mac-ip {
        type boolean;
        description
          "Advertise gateway MAC IP.";
      }

      leaf state {
        type string;
        description
          "VNI state.";
      }

      leaf router-mac {
        type union {
          type ntos-if:mac-address;
          type enumeration {
            enum None {
              description
                "No router.";
            }
          }
        }
        description
          "Router MAC address.";
      }

      leaf-list l2-vni {
        type ntos-vxlan:vni;
        description
          "List of L2 VNIs linked.";
      }

      list arp-neighbor-discovery {
        key "address";
        description
          "List of ARP/neighbor discovery entries.";

        leaf address {
          type ntos-inet:ip-address;
          description
            "ARP/Neighbor discovery entry address.";
        }

        leaf state {
          type string;
          description
            "ARP/Neighbor discovery entry state.";
        }

        leaf default-gateway {
          type boolean;
          description
            "Default gateway.";
        }

        leaf mac {
          type ntos-if:mac-address;
          description
            "Entry mac address.";
        }
        uses evpn-arp-mac-common;
      }

      list mac {
        key "address";
        description
          "List of MAC entries.";

        leaf address {
          type ntos-if:mac-address;
          description
            "MAC address.";
        }

        leaf vlan-id {
          type ntos-vlan:vlan-id;
          description
            "VLAN identifier.";
        }
        uses evpn-arp-mac-common;
      }
    }
  }

  rpc show-evpn {
    description
      "Show EVPN information.";
    input {

      leaf arp-cache {
        type empty;
        description
          "Show ARP and ND cache information.";
        ntos-extensions:nc-cli-group "subcommand";
        ntos-extensions:nc-cli-exclusive;
      }

      leaf mac {
        type empty;
        description
          "Show MAC addresses information.";
        ntos-extensions:nc-cli-group "subcommand";
        ntos-extensions:nc-cli-exclusive;
      }

      leaf vni {
        type union {
          type enumeration {
            enum all {
              description
                "Show all VNIs.";
            }
          }
          type ntos-vxlan:vni;
        }
        mandatory true;
        description
          "Show EVPN information about a specific VNI or all.";
      }

      leaf detail {
        type empty;
        must '../vni = "all"' {
          error-message "cannot be used in a specific VNI.";
        }
        description
          "Detail information on each VNI.";
      }
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-extensions:feature "product";
    ntos-extensions:nc-cli-show "evpn";
    ntos-api:internal;
  }

  augment "/ntos:state/ntos:vrf/ntos-rt:routing" {
    description
      "Operational EVPN state.";

    container evpn {
      presence "Make EVPN state available";
      description
        "Operational EVPN state.";
      ntos-extensions:feature "product";
      uses common-evpn-state;
    }
  }
}
