module ntos-extensions {
  namespace "urn:ruijie:ntos:params:xml:ns:yang:extensions";
  prefix ntos-extensions;

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS extensions.";

  revision 2020-02-10 {
    description
      "Add extension nc-cli-sort-by.";
    reference "";
  }
  revision 2019-01-14 {
    description
      "Add extension nc-cli-int-multiplier.";
    reference "";
  }
  revision 2018-10-03 {
    description
      "Initial version.";
    reference "";
  }

  extension status-obsoleted-release {
    argument release;
    description
      "Describes in which version a given node will become obsolete.
       Only to be used as a substatement of status.";
  }

  extension status-description {
    argument description;
    description
      "Describes why a given node has been deprecated or made obsolete.
       Only to be used as a substatement of status.";
  }

  extension status-replacement {
    argument replacement;
    description
      "Describes what replaced this node. Only to be used as a substatement of status.";
  }

  extension status-deprecated-revision {
    argument revision;
    description
      "Describes in which module revision a given node has been deprecated or made obsolete.
       Only to be used as a substatement of status.";
  }

  extension nc-cli-shortdesc {
    argument shortdesc;
    description
      "Extend a type to specify what should be displayed as short
       description in nc-cli. The description should be short (~20
       characters max), and surrounded by <> if it is a pattern
       descriptor.";
  }

  extension nc-cli-show-key-name {
    description
      "Extend a list to specify that the key names should
       be specified by the user when entering in the list.";
  }

  extension nc-cli-no-name {
    description
      "Extend a leaf or a leaf-list so that it is set with its
       value only, instead of <name> <value>. This option should
       only be used with defined values (like enums) to avoid
       defining an ambiguous grammar. The name of the leaf is
       still used for deletion.";
  }

  extension nc-cli-completion-xpath {
    argument xpath;
    description
      "Add nodes matching the xpath in completions list. The xpath can
       reference config nodes, state nodes, and relative rpc nodes.
       All node names must be prefixed by the module name (not the module
       prefix). The 'current()' xpath function points to the parent of
       the node we are trying to complete. In case of a list key, current()
       points to the parent of the list node.";
  }

  extension nc-cli-hashed-password {
    argument algorithm;
    description
      "Extend a leaf to specify that the stored value is a hashed
       password. In the cli, the password can be entered as a hashed
       value, or interactively by typing the password. The argument
       contains the algorithm to use: md5, sha-256 or sha-512.";
  }

  extension nc-cli-one-liner {
    description
      "Extend a container or a list to make it configurable using
       one line only. User cannot enter into the container or list
       context. All the content is available on one line.";
  }

  extension status-rpc {
    description
      "Extend a leaf to specify that it contains xml to be sent as rpc input
       to get further status of a background command.";
  }

  extension refresh-rpc {
    description
      "Extend a leaf to specify that it contains xml to be sent as rpc input
       to keep a background command running.";
  }

  extension stop-rpc {
    description
      "Extend a leaf to specify that it contains xml to be sent as rpc input
       to stop a background command.";
  }

  extension nc-cli-cmd {
    argument name;
    description
      "Extend a rpc to specify that it should be used as a cli command.";
  }

  extension nc-cli-show {
    argument name;
    description
      "Extend a rpc to specify that it should be used as a cli show.";
  }

  extension nc-cli-flush {
    argument name;
    description
      "Extend a rpc to specify that it should be used as a cli flush.";
  }

  extension nc-cli-hidden {
    description
      "Extend a leaf/container to specify that it should be hidden from the user.";
  }

  extension nc-cli-stdout {
    description
      "Specify that a rpc output leaf contains data to be printed as-is
       to stdout.";
  }

  extension nc-cli-command-stopped {
    description
      "Specify that a rpc output leaf (when present) means that the related
       command is terminated.";
  }

  extension nc-cli-exclusive {
    description
      "This extension can be added on a list or a container to
       ensure that only one child node can be parsed at a time on
       the a command line. This is useful to implement a choice-like
       behavior in oneliner commands.";
  }

  extension nc-cli-group {
    argument name;
    description
      "This extension can be added on nodes inside the same container
       or list to ensure that only one node that is member of this
       group can be parsed at a time on the command line. This is useful
       to implement a sort of choice in oneliner commands. The argument
       is a string identifying the group. Several group can be used
       simultaneously in the same container.";
  }

  extension nc-cli-order {
    argument order;
    description
      "This extension can be added on a leaf/container that is part of a
       oneliner group to enforce its parsing order on the command line. If other
       siblings leaves/containers are already parsed with a higher order, this
       one will not be accepted.

       Only strictly positive values are valid.";
  }

  extension nc-cli-command-no-pager {
    description
      "Never use pager for this command. This extension must be added to the
       rpc node.";
  }

  extension nc-cli-int-multiplier {
    description
      "Extend an integer type to specify that it may be typed with an optional
       multiplier (K/M/G/T) and will be displayed with the most compact form,
       using multipliers if applicable.";
  }

  extension nc-cli-identity-name {
    argument name;
    description
      "This extension can be added on an identity to override its name. This
       name can containe spaces.";
  }

  extension feature {
    argument name;
    description
      "The node is considered enabled only if the specified feature
       is enabled. The list of enabled features is retrieved using a
       custom RPC.

       Standard YANG features have some limitations. For instance,
       they cannot be enabled/disabled dynamically when a transaction
       (edit-config for example) is in progress. This extension achieves
       the same purpose than standard YANG features without this
       limitation.";
  }

  extension nc-cli-sort-by {
    argument name;
    description
      "Sort a list according to a leaf value specified with the extension
       argument. This extension can be set only once per list. If the leaf is
       not set, the node will be put at the top of the list.";
  }

  extension nc-cli-message {
    argument message;
    description
      "Display a message when the node is modified/created (leaf), or when
       we go into it (container, list).";
  }

  extension data-not-sync {
    description
      "Indicates that a node not synchronization.";
  }
}
