module ntos-grpc {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:system:grpc";
  prefix ntos-grpc;

  import ntos {
    prefix ntos;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-system {
    prefix ntos-system;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos-extensions {
    prefix ntos-extensions;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS GRPC module.";

  revision 2024-03-18 {
    description
      "Initial version.";
    reference "";
  }

  identity grpc {
    base ntos-types:SERVICE_LOG_ID;
    description
      "GRPC resolver service.";
  }

  identity STREAM_PROTOCOL {
    description "Base identity for a telemetry stream protocol";
  }

  identity DATA_ENCODING_METHOD {
    description
      "Base identity for supported encoding for configuration and
      operational state data";
  }

  grouping greeter-config {
    container greeter {
      description
        "The configuration of a greeter service";
      leaf enabled {
        type boolean;
        description
          "Enable or disable greeter service.";
      }

      leaf port {
        type uint32;
        description
          "GRPC service listening port.";
      }
    }
  }

  grouping dail-out-config {
    container dail-out {
      description
        "The configuration of a dail-out service";
      leaf enabled {
        type boolean;
        default "false";
        description
          "Enable or disable dail-out service.";
      }

      uses telemetry-config;
    }
  }

  grouping telemetry-config {
    container telemetry {
      description
        "The configuration of telemetry";
      
      list sensor-group {
        key "sensor-group-id";
        max-elements 25;
        description
          "List of telemetry sensory groups on the local
           system, where a sensor grouping represents a resuable
           grouping of multiple paths and exclude filters.";

        leaf sensor-group-id {
          type string {
            length "1..255";
          }
          description
            "Name or identifier for the sensor group itself.
             Will be referenced by other configuration specifying a
             sensor group";
        }

        list sensor-path {
          key "path";
          max-elements 64;
          description
            "List of paths in the model which together
             comprise a sensor grouping. Filters for each path
             to exclude items are also provided.";

          leaf path {
            type string {
              length "1..450";
            }
            description
              "Path to a section of operational state of interest
               (the sensor).";
          }

          //leaf exclude-filter {
          //  type string;
          //  description
          //    "Filter to exclude certain values out of the state
          //     values";
          //    //May not be necessary. Could remove.
          //}
        }
      }

      list destination-group {
        key "group-id";
        max-elements 30;
        description
          "List of destination-groups. Destination groups allow the
           reuse of common telemetry destinations across the
           telemetry configuration. An operator references a
           set of destinations via the configurable
           destination-group-identifier.

           A destination group may contain one or more telemetry
           destinations";

        leaf group-id {
          type string {
            length "1..255";
          }
          description
            "Unique identifier for the destination group";
        }

        list destination {
          key "destination-address destination-port";
          max-elements 30;
          description
            "List of telemetry stream destinations";

          leaf destination-address {
            type ntos-inet:ip-address;
            description
              "IP address of the telemetry stream destination";
          }
          leaf destination-port {
            type uint16;
            description
              "Protocol (udp or tcp) port number for the telemetry
               stream destination";
          }

          //list standby-destination {
          //  key "standby-destination-address standby-destination-port";
          //  description
          //    "List of telemetry stream standby destinations";
          //
          //  leaf standby-destination-address {
          //    type ntos-inet:ip-address;
          //    description
          //      "Reference to the standby destination address of the telemetry stream";
          //  }
          //
          //  leaf standby-destination-port {
          //    type uint16;
          //    description
          //      "Reference to the port number of the stream standby destination";
          //  }
          //}
        }

        leaf tls {
		      type boolean;
		      default false;
		      description
			      "protocol grpc tls";
		    }

        //list destination-vrf {
        //  key "destination-address destination-port destination-vrf";
        //  description
        //    "Configure destination address with the vrf.";
        //
        //  leaf destination-address {
        //    type ntos-inet:ip-address;
        //    description
        //      "Reference to the destination address of the telemetry stream";
        //  }
        //  leaf destination-port {
        //    type uint16;
        //    description
        //      "Reference to the port number of the stream destination";
        //  }
        //  leaf destination-vrf {
        //    type string;
        //    description
        //      "Reference to the vrf of the stream destination";
        //  }
        //
        //  list standby-destination-vrf {
        //    key "standby-destination-address standby-destination-port standby-destination-vrf";
        //      description
        //        "Configure standby destination address with the vrf.";
        //    
        //    leaf standby-destination-address {
        //      type ntos-inet:ip-address;
        //      description
        //        "Reference to the destination address of the telemetry stream";
        //    }
        //    leaf standby-destination-port {
        //      type uint16;
        //      description
        //        "Reference to the port number of the stream destination";
        //    }
        //    leaf standby-destination-vrf {
        //      type string;
        //      description
        //        "Reference to the vrf of the stream destination";
        //    }
        //  }
        //}
      }

      list subscription {
        key "subscription-name";
        max-elements 30;
        description
          "List of telemetry subscriptions. A telemetry
          subscription consists of a set of collection
          destinations, stream attributes, and associated paths to
          state information in the model (sensor data)";

        leaf subscription-name {
          type string {
            length "1..255";
          }
          description
            "User configured identifier of the telemetry
             subscription. This value is used primarily for
             subscriptions configured locally on the network
             element.";
        }

        // TODO: Make this a reference to an interface.
        //leaf local-source-address {
        //  type ntos-inet:ip-address;
        //  description
        //    "The IP address which will be the source of packets from
        //     the device to a telemetry collector destination.";
        //}

        //leaf originated-qos-marking {
        //  type ntos-inet:dscp;
        //  description
        //    "DSCP marking of packets generated by the telemetry
        //     subsystem on the network device.";
        //}

        //leaf protocol {
        //  type identityref {
        //    base STREAM_PROTOCOL;
        //  }
        //  description
        //    "Selection of the transport protocol for the telemetry
        //     stream.";
        //}

        //leaf encoding {
        //  type identityref {
        //    base DATA_ENCODING_METHOD;
        //  }
        //  description
        //    "Selection of the specific encoding or RPC framework
        //     for telemetry messages to and from the network element.";
        //}

        list sensor-profile {
          key "sensor-group";
          max-elements 5;
          description
            "List of telemetry sensor groups used
            in the subscription";

          leaf sensor-group {
            type leafref {
              path "../../../sensor-group/sensor-group-id";
            }
            description
              "Reference to the sensor group which is used in the profile";
          }
              
          leaf sample-interval {
            type uint64 {
              range '0 | 1000..604800000'; //604800000ms = 1 week
            }
            default 0;
            description
              "Time in milliseconds between the device's sample of a
               telemetry data source. For example, setting this to 100
               would require the local device to collect the telemetry
               data every 100 milliseconds. There can be latency or jitter
               in transmitting the data, but the sample must occur at
               the specified interval.

               The timestamp must reflect the actual time when the data
               was sampled, not simply the previous sample timestamp +
               sample-interval.

               If sample-interval is set to 0, the telemetry sensor
               becomes event based. The sensor must then emit data upon
               every change of the underlying data source.";
          }

          //leaf heartbeat-interval {
          //  type uint64;
          //  description
          //    "Maximum time interval in seconds that may pass
          //     between updates from a device to a telemetry collector.
          //     If this interval expires, but there is no updated data to
          //     send (such as if suppress_updates has been configured), the
          //     device must send a telemetry message to the collector.";
          //}

          //leaf suppress-redundant {
          //  type boolean;
          //  description
          //    "Boolean flag to control suppression of redundant
          //     telemetry updates to the collector platform. If this flag is
          //     set to TRUE, then the collector will only send an update at
          //     the configured interval if a subscribed data value has
          //     changed. Otherwise, the device will not send an update to
          //     the collector until expiration of the heartbeat interval.";
          //}
        }

        list destination-group {
          key "group-id";
          max-elements 30;
          description
            "Identifier of the previously defined destination
             group";

          leaf group-id {
            type leafref {
              path "../../../destination-group/group-id";
            }
            description
              "The destination group id references a reusable
               group of destination addresses and ports for
               the telemetry stream.";
          }
        }
      }

      //container keepalive {
      //  leaf time {
      //    type uint32 {
      //      range "1..300";
      //    }
      //    default 3;
      //      description "Specify destination address keepalive time";
      //  }
      //  leaf interval {
      //    type uint32 {
      //      range "5..3600";
      //    }
      //    default 10;
      //    description "Specify destination address keepalive interval";
      //  }
      //}
    }
  }

  augment "/ntos:config/ntos-system:system" {
    description
      "Top-level grouping for GRPC / resolver config and operational
       state data.";

    container grpc {
      presence "Makes grpc available";
      description
        "Enclosing container for grpc resolver data.";

      leaf enabled {
        type boolean;
        default "false";
        description
          "Enable or disable GRPC Service.";
      }

      uses greeter-config;

      uses dail-out-config;
    }
  }

  augment "/ntos:state/ntos-system:system" {
    description
      "Top-level grouping for GRPC / resolver config and operational
       state data.";

    container grpc {
      description
        "Enclosing container for grpc resolver data.";

      leaf state {
        type string;
        description
          "The state of grpc server.";
      }
    }
  }

  typedef grpc-encoding {
    type enumeration {
      enum json {
        value 0;
      }
      enum bytes {
        value 1;
      }
      enum proto {
        value 2;
      }
      enum ascii {
        value 3;
      }
      enum json-ietf {
        value 4;
      }
    }
  }

  rpc grpc-connection {
    description
      "Display gRPC connection state information.";

    output {
      leaf state {
        type string;
        description
          "The connection of grpc server.";
      }
    }
    ntos-extensions:nc-cli-show "grpc connection";
  }

  rpc grpc-configuration {
    description
      "Display gRPC configuration stats information.";

    output {
      leaf state {
        type string;
        description
          "The connection of grpc server.";
      }
    }
    ntos-extensions:nc-cli-show "grpc configuration";
  }

  rpc grpc-gnmi-capabilities{
    description
      "Capabilities allows the client to retrieve the set of capabilities that 
      is supported by the target. This allows the target to validate the 
      service version that is implemented and retrieve the set of models that 
      the target supports. The models can then be specified in subsequent RPCs 
      to restrict the set of data that is utilized.";
    output {
      list supported_models {
        leaf name {
          type string;
          description
            "Name of the model.";
        }

        leaf organization {
          type string;
          description
            "Organization publishing the model.";
        }

        leaf version {
          type string;
          description
            "Semantic version of the model.";
        }
      }

      leaf-list supported_encodings {
        type grpc-encoding;
          description
            "Supported encodings.
            { JSON = 0; BYTES = 1; PROTO = 2; ASCII = 3;  JSON_IETF = 4; }";
      }

      leaf gNMI_version {
        type string;
        description
          "Supported gNMI version.";
      }
    }
    ntos-extensions:nc-cli-show "grpc-gnmi-capabilities";
  }
}