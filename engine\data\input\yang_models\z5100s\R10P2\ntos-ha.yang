module ntos-ha {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:ha";
  prefix ntos-ha;

  import ntos {
    prefix ntos;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-ip-track {
    prefix ntos-ip-track;
  }
  import ntos-api {
    prefix ntos-api;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:gao<PERSON><PERSON><PERSON>@ruijie.com.cn";
  description
    "Ruijie NTOS HA.";

  revision 2022-10-17 {
    description
      "Add ha config and state.";
    reference "";
  }

  identity ha {
    base ntos-types:SERVICE_LOG_ID;
    description
      "HA service.";
  }

  grouping ha-config {
    description
      "Configuration data for ha configuration.";
    
    list group {
      key "id";
      description
        "The detail of ha group.";
      ordered-by user;
      leaf id {
        type uint32 {
          range "0..1";
        }
        description
          "The id of group.";
      }

      leaf description {
        type string {
          length "0..64";
          pattern "[^`~!#$%^&*+/|{};:\"',\\\\<>? ]*" {
            error-message 'cannot include character: `~!#%^&*+\|{};:",/<>? ';
          }
        }
        default "";
        description
          "The description of ha group, length 0..64.";
        ntos-ext:data-not-sync;
      }

      leaf priority {
        type uint16 {
          range "1..255";
        }
        default "100";
        description
          "The priority of group.";
        ntos-ext:data-not-sync;
      }

      list monitor {
        description
          "Configuration of group monitor.";
        key "monitor-name";
        ordered-by user;
        leaf monitor-name {
          type ntos-types:ntos-obj-name-type {
            length "1..127";
          }
          description
            "The monitor name of group.";
          ntos-ext:nc-cli-completion-xpath
            "/ntos:config/ntos:vrf/ntos-ip-track:track/ntos-ip-track:rule/ntos-ip-track:name";
        }
        max-elements 2;
        ntos-ext:nc-cli-one-liner;
      }
    }
    
    list link {
      description
        "The heart interface of ha.";

      key "link";  
      ordered-by user;

      leaf link {
        type ntos-types:ifname;
        description
          "The link of heart interface.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:config/ntos:vrf[ntos:name='main']/ntos-interface:interface/physical/*[local-name()='name'] |
           /ntos:config/ntos:vrf[ntos:name='main']/ntos-interface:interface/ntos-lag:lag/*[local-name()='name']";
      }

      leaf local-addr {
        type ntos-inet:ipv4-address;
        mandatory true;
        description
          "The local address of heart interface.";
      }

      leaf peer-addr {
        type ntos-inet:ipv4-address;
        mandatory true;
        description
          "The peer address of heart interface.";
      }
      max-elements 1;
      ntos-ext:nc-cli-one-liner;
      ntos-ext:data-not-sync;
    }

    list data-link {
      description
        "The data interface of ha.";

      key "link";  
      ordered-by user;
      leaf link {
        type ntos-types:ifname;
        description
          "The link of data interface.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:config/ntos:vrf[ntos:name='main']/ntos-interface:interface/physical/*[local-name()='name'] |
           /ntos:config/ntos:vrf[ntos:name='main']/ntos-interface:interface/ntos-lag:lag/*[local-name()='name']";
      }

      leaf local-addr {
        type ntos-inet:ipv4-address;
        mandatory true;
        description
          "The local address of data interface.";
      }

      leaf peer-addr {
        type ntos-inet:ipv4-address;
        mandatory true;
        description
          "The peer address of data interface.";
      }

      ntos-ext:nc-cli-one-liner;
      max-elements 1;
      ntos-ext:data-not-sync;
    }

    list mgmt-link {
      description
        "The management interface of ha.";

      key "link";  
      ordered-by user;
      leaf link {
        type ntos-types:ifname;
        description
          "The link of management interface.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:config/ntos:vrf[ntos:name='main']/ntos-interface:interface/physical/*[local-name()='name']";
      }

      ntos-ext:nc-cli-one-liner;
    }

    leaf mode {
      type enumeration {
        enum A-P {
          description
            "Active-Passive";
        }
        enum A-A {
          description
            "Active-Active";
        }
      }
      default "A-P";
      description
        "The mode of ha, A-P or A-A.";
    }

    leaf heart-interval {
      type uint16 {
        range "100..10000";
      }
      units ms;
      default "1000";
      description
        "The heart interval of ha.";
    }

    leaf heart-alert-count {
      type uint16 {
        range "3..255";
      }
      default "3"; 
      description
        "The heart alert count of ha.";
    }

    leaf gra-arp-count {
      type uint16 {
        range "5..100";
      }
      default "5"; 
      description
        "The count of arp to be sent after switchover.";
    }

    leaf vmac-prefix {
      type string {
        pattern '[0-9a-fA-F]{2}(:[0-9a-fA-F]{2}){2}';
      }
      default "00:00:5e";
      description
        "The virtual mac address prefix of ha. pattern:[0-9a-fA-F]{2}(:[0-9a-fA-F]{2}){2}.";
    }
    
    leaf preempt {
      type boolean; 
      default "false";
      description
        "The enabled for preemtion.";
    }
      
    leaf preempt-delay {
      type uint64 {
        range "1..1800";
      }
      units second;
      default "60";
      description
        "The preemtion switchover delay time, unit:second.";
    }

    leaf session-sync {
      type boolean; 
      default "true";
      description
        "The enabled for session synchronization.";
    }

    leaf neigh-sync {
      type boolean; 
      default "true";
      description
        "The enabled for neighbor synchronization.";
    }

    leaf switch-link-time {
      type uint16 {
        range "1..10";
      }
      default "1";
      description
        "The interface DOWN-UP time.";
    }

    leaf auth-type {
      type enumeration {
        enum none;
        enum auth;
      } 
      default "none";
      description
        "The type of HA authentication.";
      ntos-ext:data-not-sync;
    }

    leaf auth-key {
      type string;
      description
        "The key of HA authentication, length 8..32.";
      ntos-ext:data-not-sync;
    }
  }

  grouping log-level {
    leaf group {
      type enumeration {
        enum on;
        enum off;
      } 
      default "on";
      description
        "Group messages.";
    }
    leaf adv {
      type enumeration {
        enum on;
        enum off;
      } 
      default "off";
      description
        "Adv messages.";
    }
    leaf dbus {
      type enumeration {
        enum on;
        enum off;
      }  
      default "on";
      description
        "Dbus messages.";
    }
    leaf monitor {
      type enumeration {
        enum on;
        enum off;
      }  
      default "off";
      description
        "Monitor messages.";
    }
    ntos-ext:data-not-sync;
  }

  grouping group-info {
    container local-info {
      leaf group-id{
        type uint32 {
          range "0..1";
        }
        description
          "The id of group.";
      }

      leaf description {
        type string;
        description
          "The description of ha group.";
      }

      leaf status {
        type string;
        description
          "The status of ha.";
      }

      leaf flag {
        type string;
        description
          "The flag of ha.";
      }

      leaf priority {
        type uint16 {
          range "1..255";
        }
        description
          "The priority of group.";
      }

      leaf switch-reason {
        type string;
        description
          "The switch-reason of group.";
      }

      leaf tx-count {
        type uint32;
        description
          "The num of tx.";
      }

      leaf rx-count {
        type uint32;
        description
          "The num of rx.";
      }

      list interface {
        description
          "Configuration of virtual ip.";
        key "link";
        leaf link {
          type string;
          description
            "The link of interface.";
        }

        leaf virtual-ip {
          type ntos-inet:masked-ipv4-address;
          description
            "The virtual-ip of interface.";
        }

        leaf virtual-ip6 {
          type ntos-inet:masked-ipv6-address;
          description
            "The virtual-ip6 of link.";
        }
      }

      list monitor {
        key "monitor-name";
        leaf monitor-name {
          type string;
          description
            "The monitor name of group.";
        }
      }
    }

    container peer-info {
      leaf peer-status {
        type string;
        description
          "The peer-status of ha.";
      }

      leaf peer-flag {
        type string;
        description
          "The peer-flag of ha.";
      }
    }

    container second-local-info {
      leaf group-id{
        type uint32 {
          range "0..1";
        }
        description
          "The id of group.";
      }

      leaf description {
        type string;
        description
          "The description of ha group.";
      }

      leaf status {
        type string;
        description
          "The status of ha.";
      }

      leaf flag {
        type string;
        description
          "The flag of ha.";
      }

      leaf priority {
        type uint16 {
          range "1..255";
        }
        description
          "The priority of group.";
      }

      leaf switch-reason {
        type string;
        description
          "The switch-reason of ha.";
      }

      leaf tx-count {
        type uint32;
        description
          "The num of tx.";
      }

      leaf rx-count {
        type uint32;
        description
          "The num of rx.";
      }

      list interface {
        description
          "Configuration of virtual ip.";
        key "link";
        leaf link {
          type string;
          description
            "The interface link of group.";
        }

        leaf virtual-ip {
          type ntos-inet:masked-ipv4-address;
          description
            "The virtual-ip of link.";
        }

        leaf virtual-ip6 {
          type ntos-inet:masked-ipv6-address;
          description
            "The virtual-ip6 of link.";
        }
      }

      list monitor {
        key "monitor-name";
        leaf monitor-name {
          type string;
          description
            "The monitor name of group.";
        }
      }
    }

    container second-peer-info {
      leaf peer-status {
        type string;
        description
          "The peer-status of ha.";
      }

      leaf peer-flag {
        type string;
        description
          "The peer-flag of ha.";
      }
    }
  }

  grouping global-info {
    container global-info {
      leaf mode {
        type string;
        description
          "The mode of ha, AP or AA.";
      }

      leaf heart-interval {
        type uint16;
        description
          "The heart interval of ha, unit:ms.";
      }

      leaf heart-alert-count {
        type uint16 {
          range "3..255";
        }
        description
          "The heart alert count of ha.";
      }

      leaf gra-arp-count {
        type uint16;
        description
          "The count of arp to be sent after switchover.";
      }

      leaf vmac-prefix {
        type string;
        description
          "The virtual mac address prefix of ha.";
      }

      leaf preempt {
        type string;
        description
          "The preempt enabled of ha.";
      }

      leaf preempt-delay {
        type uint64 {
          range "1..1800";
        }
        description
          "The preemtion switchover delay time, unit:second.";
      }

      list link {
        description
          "The heart connect of ha.";

        key "link";
        leaf link {
          type string;
          description
            "The link of heart interface.";
        }

        leaf local-addr {
          type ntos-inet:ipv4-address;
          description
            "The local address of heart interface.";
        }

        leaf peer-addr {
          type ntos-inet:ipv4-address;
          description
            "The peer address of heart interface.";
        }
      }

      list data-link {
        description
          "The data interface of ha.";

        key "link";
        leaf link {
          type string;
          description
            "The link of data interface.";
        }

        leaf local-addr {
          type ntos-inet:ipv4-address;
          description
            "The local address of data interface.";
        }

        leaf peer-addr {
          type ntos-inet:ipv4-address;
          description
            "The peer address of data interface.";
        }
      }

      list mgmt-link {
        description
          "The management interface of ha.";

        key "link";
        leaf link {
          type string;
          description
            "The link of management interface.";
        }
      }

      leaf enabled {
        type string;
        description
          "The enabled of ha.";
      }

      leaf session-sync {
        type string;
        description
          "The enabled for session synchronization.";
      }

      leaf neigh-sync {
        type string;
        description
          "The enabled for neighbor synchronization.";
      }

      leaf switch-link-time {
        type uint16;
        description
          "The interface DOWN-UP time.";
      }

      leaf cfg-hot-sync {
        type string;
        description
          "The configuration hot synchronization state.";
      }

      leaf cfg-hot-sync-fail-reason {
        type string;
        description
          "The configuration hot synchronization fail reason.";
      }

      leaf running-time {
        type string;
        description
          "The running time of HA.";
      }

      leaf auth-type {
        type string;
        description
          "The key of HA authentication.";
      }

      leaf auth-key {
        type string;
        description
          "The key of HA authentication.";
      }

      leaf auth-state {
        type string;
        description
          "The HA authentication state.";
      }
    }
  }

  grouping log{
    container log {
      description
        "The log state of ha.";

      leaf group {
        type string;
        description
          "Set log-level group.";
      }
      leaf adv {
        type string;
        description
          "Set log-level adv.";
      }
      leaf dbus {
        type string;
        description
          "Set log-level dbus.";
      }
      leaf monitor {
        type string;
        description
          "Set log-level monitor.";
      }
    }
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "Set ha configuration.";
    
    container ha {
      description
        "Configuration of ha.";
      
      uses ha-config;

      leaf enabled {
        type boolean; 
        default "false";
        description
          "Ha enabled.";
        ntos-ext:data-not-sync;
      }

      container log-level {
        description
          "Set log-level on/off.";
        uses log-level;
        ntos-ext:data-not-sync;
      }
    }
  }

  rpc ha-switch {
    description
      "The manual switch command.";
    input {
      leaf group {
        type uint32 {
          range "0..1";
        }
        description
          "The id of group.";
        ntos-ext:nc-cli-no-name;
      }
    }

    output {
      leaf buffer {
        type string;
        description
          "Command output buffer since last request.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-cmd "ha switch backup group";
  }

  rpc ha-manual-sync {
    description
      "The manual synchronization command.";

    output {
      leaf buffer {
        type string;
        description
          "Command output buffer since last request.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-cmd "ha manual sync";
  }

  rpc show-ha-state-group {
    description
      "Display state of ha group.";
    output {
      uses group-info;
    }
    ntos-ext:nc-cli-show "ha state group";
  }

  rpc show-ha-state-global {
    description
      "Display state of ha global.";
    output {
      uses global-info;
    }
    ntos-ext:nc-cli-show "ha state global";
  }

  rpc show-ha-state-virtual-if {
    description
      "Display virtual interface of ha.";
    input {
      leaf json {
        type empty;
      }
    }

    output {
      leaf buffer {
        type string;
        description
          "Command output buffer since last request.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "ha state virtual interface";
  }

  rpc show-ha-state-sync {
    description
      "Display state of ha synchronization.";
    input {
      leaf json {
        type empty;
      }
    }

    output {
      leaf buffer {
        type string;
        description
          "Command output buffer since last request.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "ha state sync";
  }

  rpc show-ha-state-log {
    description
      "Display state of ha log.";
    output {
      uses log;
    }
    ntos-ext:nc-cli-show "ha state log";
  }

  rpc ha-sysupgrade {
    ntos-ext:nc-cli-cmd "ha sysupgrade";
    ntos-api:internal;
    description
      "Upgrade the system software.";
    input {
      leaf url {
        type union {
          type ntos-types:ftp-url;
          type ntos-types:tftp-url;
          type ntos-types:http-url;
          type string {
            pattern 'flash:/.*';
            ntos-ext:nc-cli-shortdesc "<flash:/path/to/file>";
          }
        }
        ntos-ext:nc-cli-no-name;
        description
          "A filename begin with this prefix";
      }
      leaf async {
        type boolean;
        default "false";
        description
          "Upgrade the system software in the background.";
      }
      leaf rollback {
        type empty;
        description
          "Rollback software version";
      }
    }

    output {
      leaf status {
        type uint32;
        description
          "The status of system upgrade.";
      }
      leaf errcode {
        type uint32;
        description
          "The error code of system upgrade.";
      }
      leaf buffer {
        type string;
        description
          "The command output buffer.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
  }
}
