module ntos-interface-group {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:interface-group";
  prefix ntos-interface-group;

  import ntos {
    prefix ntos;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS interface-group module.";

  revision 2024-04-07 {
    description
      "Initial version.";
    reference "";
  }
  
  grouping vrf {
    leaf vrf {
      description
        "VRF name.";

      type string;
      default "main";
    }
  }

  grouping references {
    description
      "The references of interface group.";
    list reference {
      key "policy-id";
      leaf policy-id {
        description
          "The id of referenced policy.";
        type string {
          length 1..127;
        }
      }
      leaf ref {
        description
          "The direction of referenced policy.";
        type uint32;
      }
      leaf module-id {
        description
          "The module of referenced policy.";
        type uint32;
      }
    }
  }

  grouping link-interface-param {
    description
      "Slave interface of this Interface-group.";
      
    list link-interface {
      key "slave";
      description
        "Slave interface of this Interface-group.";
      ntos-ext:nc-cli-one-liner;
      
      leaf slave {
        type ntos-types:ifname;
        description
          "Slave interface name.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:config/vrf[ntos:name=string(current()/../../*[local-name()='name'])]/
             ntos-interface:interface/*[.!=current()]/*[local-name()='ethernet']/../*[local-name()='name'] |
           /ntos:state/vrf[ntos:name=string(current()/../../*[local-name()='name'])]/
             ntos-interface:interface/*[.!=current()]/*[local-name()='ethernet']/../*[local-name()='name']";
      }
      leaf slave-id {
        type uint32;
        description
          "Slave interface id.";
      }
      leaf priority {
        description
          "Configure the priority value.";
        type uint16 {
          range "1..1000";
        }
        default "1";
      }

      leaf weight {
        description
          "Configure the weight value.";
        type uint32 {
          range "1..40000000";
        }
        default "1000000";
      }
    }
  }
  
  grouping interface-group-detail {
    description
      "Detail of interface-group global configuration.";

    list group {
      description "The detail of interface-group.";
      key "name";

      leaf name {
        description "The name of interface-group.";
        type ntos-types:ntos-obj-name-type {
          length "1..127";
        }
      }

      leaf group-id {
        description "The group id of interface-group.";
        type uint32;
      }

      leaf desc {
        description "The description of interface-group.";
        type ntos-types:ntos-obj-description-type {
          length "0..255";
        }
      }

      leaf algorithm {
        type enumeration {
          enum src-ip-hash;
          enum upload;
          enum download;
          enum both;
          enum up-bandwidth;
          enum down-bandwidth;
          enum weight;
          enum priority;
          enum quality;
          enum src-dst-ip-hash;
          enum min-conn;
          enum min-conn-weight;
        }
        default "src-dst-ip-hash";
      }
      container slave-list {
        uses link-interface-param;
      }
      uses references;
    }
  }
  
  augment "/ntos:config/ntos:vrf" {
    description
      "Interface-group configuration.";

    container interface-group {
      uses interface-group-detail;
    }
  }

  augment "/ntos:state/ntos:vrf" {
    description
      "Interface-group state.";

    container interface-group {
      uses interface-group-detail;
    }
  }
  
  rpc show-interface-group {
    description
      "Display configuration of the interface-group.";
  
    input {
      uses vrf;
      leaf start {
        type uint16;

        description
          "The index of page start.";
      }

      leaf end {
        type uint16;

        description
          "The index of page end.";
      }
    }

    output {
      container interface-group {
        leaf total {
          type uint32;

          description
            "The total count of interface group.";
        }
        uses interface-group-detail;
      }
    }
    ntos-ext:nc-cli-show "interface-group";
  }
    
  rpc cmd-search-interface-group {
    description
      "Display configuration of the interface-group.";
  
    input {
      uses vrf;
      leaf start {
        type uint16;

        description
          "The index of page start.";
      }

      leaf end {
        type uint16;

        description
          "The index of page end.";
      }
      leaf name {
        description
          "The name of interface-group";
        type string {
          length "1..127";
        }
      }
    }

    output {
      container interface-group {
        leaf total {
          type uint32;

          description
            "The total count of interface group.";
        }
        uses interface-group-detail;
      }
    }
    ntos-ext:nc-cli-cmd "interface-group search";
  }

  rpc cmd-get-interface-group-reference {
    description
      "Get configuration of interface-group.";

    input {
      uses vrf;
      leaf group-id {
        description
          "The group id of interface-group";
        type uint32;
      }
    }

    output {
      container interface-group {
        uses references;
      }
    }
    ntos-ext:nc-cli-cmd "interface-group get-reference";
  }
}