module ntos-interface {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:interface";
  prefix ntos-interface;

  import extra-conditions {
    prefix ext-cond;
  }
  import ntos {
    prefix ntos;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-commands {
    prefix ntos-cmd;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-if-types {
    prefix ntos-if;
  }
  import ntos-extensions {
    prefix ntos-extensions;
  }
  import ntos-ip {
    prefix ntos-ip;
  }
  import ntos-system {
    prefix ntos-system;
  }
  import ntos-fast-path {
    prefix ntos-fast-path;
  }
  import ntos-qos {
    prefix ntos-qos;
  }
  import ntos-routing {
    prefix ntos-routing;
  }
  import ntos-dhcp-snooping {
    prefix ntos-dhcp-snp;
  }
  import ntos-pppoe-server {
    prefix ntos-pppoe-server;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS network interfaces module.";

  revision 2023-04-25 {
    description
      "Add new features for the show-interface rpc.";
    reference "";
  }

  revision 2022-09-08 {
    description
      "Add a new monitor working-mode for physical interface.";
    reference "";
  }

  revision 2021-12-20 {
    description
      "Add new features for interface module.";
    reference "";
  }
  revision 2020-12-10 {
    description
      "Add the dhcp-client-renew-lease rpc.";
    reference "";
  }
  revision 2019-06-25 {
    description
      "More reserved interface names are now forbidden.";
    reference "";
  }
  revision 2018-10-03 {
    description
      "Initial version.";
    reference "";
  }

  identity physical {
    base ntos-types:INTERFACE_TYPE;
    description
      "Physical interface.";
  }

  typedef bandwidth-unit {
    type enumeration {
      enum kbps {
        description
          "Kilobit per second.";
      }
    }
    description
      "Bandwidth unit.";
  }

  grouping interface-config {
    description
      "Configuration data for all interfaces.";

    leaf name {
      type ntos-types:ifname;
      must ". != 'lo' and . != 'gre0' and . != 'gretap0' and
            . != 'ip6gre0' and . != 'ip6gretap0' and . != 'tunl0' and
            . != 'sit0' and . != 'ip6tnl0' and . != 'dummy0' and
            . != 'erspan0' and . != 'ip6_vti0'" {
        error-message "This interface name is reserved.";
      }
      description
        "The name of the interface.";
    }

    leaf mtu {
      type uint32;
      description
        "Set the max transmission unit size in octets.";
    }

    leaf promiscuous {
      type boolean;
      description
        "Set promiscuous mode.";
    }

    uses ntos-if:interface-common-config;
  }

  grouping nonphy-interface-config {
    description
      "Configuration data for non-physical interfaces.";

    uses interface-config {
      refine name {
        must 'not(contains(string(.), "/"))' {
          error-message "The character '/' is not allowed in interface name.";
        }
        must 'not(contains(string(.), "_"))' {
          error-message "The character '_' is not allowed in interface name.";
        }
      }
    }
  }

  grouping phy-interface-config {
    description
      "Configuration data for physical interfaces.";

    uses interface-config {
      refine name {
        must 'not(contains(string(.), "_"))' {
          error-message "The character '_' is not allowed in interface name.";
        }
      }
      refine mtu {
        must 'current() >= 68 and current() <= 1600' {
          error-message "MTU must be >= 68 and <= 1600.";
        }
        description
          "Set the max transmission unit size in octets, range: 68..1600.";
      }
    }
  }

  grouping cp-protection-config {
    description
      "Configuration for control plane protection.";

    leaf rx-cp-protection {
      type boolean;
      must "../../../../ntos-system:system/ntos-fast-path:fast-path/ntos-fast-path:enabled = 'true'" {
        error-message "Fast path must be started";
      }
      must ". = 'false' or count(../ntos-interface:port) = 0 or
              count(../../../../ntos-system:system/ntos-fast-path:fast-path/ntos-fast-path:port[.=string(current()/../ntos-interface:port)]) = 1" {
        error-message "Port must be managed by fast path";
      }
      description
        "Enable Rx Control Plane Protection.";
    }

    leaf tx-cp-protection {
      type boolean;
      must "../../../../ntos-system:system/ntos-fast-path:fast-path/ntos-fast-path:enabled = 'true'" {
        error-message "Fast path must be started";
      }
      must ". = 'false' or count(../ntos-interface:port) = 0 or
              count(../../../../ntos-system:system/ntos-fast-path:fast-path/ntos-fast-path:port[.=string(current()/../ntos-interface:port)]) = 1" {
        error-message "Port must be managed by fast path";
      }
      description
        "Enable Tx Control Plane Protection.";
    }
  }

  grouping interface-state {
    description
      "State variables for all interfaces.";
    uses interface-config;
  }

  grouping eth-config {
    description
      "Configuration data for Ethernet interfaces.";

    container ethernet {
      description
        "Ethernet configuration.";
      uses ntos-if:ethernet-address-config;
    }
  }

  grouping eth-state {
    description
      "State variables for Ethernet interfaces.";

    container ethernet {
      description
        "Ethernet state.";
      uses ntos-if:ethernet-address-state;
    }
  }

  grouping interface-bandwidth {
    description
      "Bandwidth configuration for a physical interface.";

    container upload-bandwidth {
      description "The upload bandwidth.";

      leaf upload-bandwidth-value {
        type uint32 {
          range "1..100000000";
        }
        description "Configure the bandwidth.";
      }
      leaf upload-bandwidth-unit {
        type bandwidth-unit;
        description "Configure the unit of upload bandwidth.";
      }
    }
    container download-bandwidth {
      description "The download bandwidth.";

      leaf download-bandwidth-value {
        type uint32 {
          range "1..100000000";
        }
        description "Configure the bandwidth.";
      }
      leaf download-bandwidth-unit {
        type bandwidth-unit;
        description "Configure the unit of download bandwidth.";
      }
    }
  }

  grouping physical-wanlan {
    description
      "wan/lan configuration for physical interfaces.";

    leaf wanlan {
      type enumeration {
        enum wan {
          description
            "WAN Ethernet.";
        }
        enum lan {
          description
            "LAN Ethernet.";
        }
      }
      description
        "The type of the physical interface, wan or lan.";
    }
  }

  grouping physical-working-mode {
    description
      "Working mode configuration for physical interfaces.";

    leaf working-mode {
      type enumeration {
        enum route {
          description
            "Route mode.";
        }
        enum bridge {
          description
            "Bridge mode.";
        }
        enum monitor {
          description
            "Monitor mode.";
        }
        enum vswitch {
          description
            "Vswitch mode.";
        }
      }
      default "route";
      description
        "The working mode of the physical interface, route/bridge/monitor.";
    }
  }

  grouping ha-group-config {
    leaf ha-group {
      type uint32 {
        range "0..1";
      }
      description "The HA group this interface belongs to.";
    }
  }

  grouping physical-config {
    description
      "Configuration data for physical interfaces.";

    uses physical-wanlan;
    uses physical-working-mode;
    uses ha-group-config;
  }

  grouping physical-state {
    description
      "State variables for physical interfaces.";

    leaf port {
      type string;
      description
        "Reference to a physical network port.";
    }

    uses physical-wanlan;
    uses physical-working-mode;
    uses ha-group-config;
  }

  grouping physical-port-attr {
    description
      "Physical port attributes defined in devcap file.";
    container port-attr {
      description
        "Port attributes.";
      leaf is-pci {
        type boolean;
        description
          "Is this a PCI port?";
      }
      leaf is-expansion-slot {
        type boolean;
        description
          "Is this a expansion slot?";
      }
      leaf slot {
        type uint8;
        description
          "Slot number.";
      }
      leaf port {
        type uint8;
        description
          "Port index in this slot.";
      }
      leaf panel-row {
        type uint8;
        description
          "The row number on panel of this port.";
      }
      leaf panel-col {
        type uint8;
        description
          "The column number on panel of this port.";
      }
      leaf ether-type {
        type string;
        description
          "The ethernet type of this port, GIGABIT,10GIGABIT,40GIGABIT etc.";
      }
      leaf medium-type {
        type string;
        description
          "The medium of this port, COPPER, FIBER.";
      }
      leaf is-mgmt {
        type boolean;
        description
          "Is this a management port?";
      }
      leaf wanlan-change {
        type string;
        description
          "The physinterface wanlan attributes.";
      }
      leaf box-name {
        type string;
        description
          "The physinterface box name.";
      }
      leaf panel-name {
        type string;
        description
          "The physinterface panel name.";
      }
    }
  }

  typedef broadband-type-enum {
    description
      "The broadband type of interface";
    type enumeration {
      enum home {
        description
          "Home broadband line.";
      }
      enum enterprise {
        description
          "Enterprise leased line.";
      }
    }
  }

  grouping network-access-attr {
    description
      "The ability and means to connect to the Internet.";
    leaf broadband-type {
      type broadband-type-enum;
    }
  }

  rpc show-throughput {
    description
      "Show interface throughput every second.";
    input {

      leaf vrf {
        type string;
        default "main";
        description
          "VRF to look into.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf type {
        type identityref {
          base ntos-types:INTERFACE_TYPE;
        }
        description
          "Select all interfaces of this type.";
      }

      leaf-list name {
        type string;
        description
          "Select this specific interface (may be specified multiple times).";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos-interface:interface/*/*[local-name()='name'] |
           /ntos:state/ntos:vrf/ntos-interface:interface/*/*[local-name()='ipv4']/*[local-name()='pppoe']/*[local-name()='connection']/*[local-name()='tunnel-interface']";
      }

      leaf count {
        type uint16 {
          range "1..65535";
        }
        description
          "Stop after the given number of seconds. By default, the throughput
           is displayed every second until interrupted by `ctrl-c`.";
      }

      leaf raw {
        type empty;
        description
          "Show the exact number of packets/bits received/transmitted every
           second instead of human readable values.";
      }
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-extensions:nc-cli-show "interface throughput";
    ntos-extensions:nc-cli-command-no-pager;
    ntos-api:internal;
  }

  rpc show-interface {
    description
      "Show interface information.";
    input {

      leaf vrf {
        type string;
        default "main";
        description
          "VRF to look into.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf type {
        type identityref {
          base ntos-types:INTERFACE_TYPE;
        }
        description
          "Interface type.";
      }

      leaf level {
        type enumeration {
          enum statistics {
            description
              "Show statistics.";
          }
          enum details {
            description
              "Show more details.";
          }
          enum up {
            description
              "Show up interfaces.";
          }
          enum hardware-statistics {
            description
              "Show hardware statistics of physical interfaces.";
          }
          enum hardware-features {
            description
              "Show hardware features of physical interfaces.";
          }
          enum hardware-information {
            description
              "Show hardware information of physical interfaces.";
          }
          enum hardware-driver-information {
            description
              "Show hardware driver information of physical interfaces.";
          }
          enum json {
            description
              "Show interface information in json array format.";
          }
        }
        description
          "The level of information requested.";
        ntos-extensions:nc-cli-no-name;
      }

      leaf name {
        type ntos-types:ifname;
        description
          "Show interface by this name.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos-interface:interface/*/*[local-name()='name'] |
           /ntos:state/ntos:vrf/ntos-interface:interface/*/*[local-name()='ipv4']/*[local-name()='pppoe']/*[local-name()='connection']/*[local-name()='tunnel-interface']";
      }
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-extensions:nc-cli-show "interface";
    ntos-api:internal;
  }

  rpc show-interface-list {
    description
      "Show interface list.";
    input {

      leaf vrf {
        type string;
        default "main";
        description
          "VRF to look into.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf type {
        type identityref {
          base ntos-types:INTERFACE_TYPE;
        }
        description
          "Interface type.";
      }

      leaf level {
        type enumeration {
          enum wan {
            description
              "Show wan interfaces.";
          }
          enum lan {
            description
              "Show lan interfaces.";
          }
        }
        description
          "The level of information requested.";
        ntos-extensions:nc-cli-no-name;
      }
    }
    output {
      list interface {
        description
          "Output for interface list";

        leaf name {
          type string;
          description
            "Interface name.";
        }

        list link-interface {
          description
            "Linked interfaces name list.";

          leaf name {
            type string;
            description
              "Interface name.";
          }
        }
      }
    }
    ntos-extensions:nc-cli-show "interface list";
    ntos-api:internal;
  }

  rpc show-physical-interface-state {
    ntos-extensions:nc-cli-show "interface port state";
    ntos-api:internal;
    description
      "Show interface state.";
    input {
      leaf vrf {
        ntos-extensions:nc-cli-completion-xpath "/ntos:state/ntos:vrf/ntos:name";
        type string;
        default "main";
        description
          "VRF to look into.";
      }
      leaf name {
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos-interface:interface/*/*[local-name()='name'] |
           /ntos:state/ntos:vrf/ntos-interface:interface/*/*[local-name()='ipv4']/*[local-name()='pppoe']/*[local-name()='connection']/*[local-name()='tunnel-interface']";
        type ntos-types:ifname;
        description
          "Show interface by this name.";
      }
      leaf type {
        type identityref {
          base ntos-types:INTERFACE_TYPE;
        }
        description
          "Interface type.";
      }
      leaf start {
        type uint16 {
          range "1..65535";
        }
        description
          "Start interface number.";
      }
      leaf end {
        type uint16 {
          range "1..65535";
        }
        description
          "End interface number.";
      }
      leaf search-name {
        type string {
          length "1..15";
        }
        description
          "Search device with name.";
      }
    }

    output {
      leaf interface-total {
        type uint16 {
          range "0..65535";
        }
        description
          "Interface total number.";
      }
      list interface {
        description
          "Output for interface list";
        uses interface-state;
        uses physical-state;
        uses ntos-if:interface-common-state;
        uses ntos-ip:ntos-ipv4-state;
        uses ntos-ip:ntos-ipv6-state;
        uses interface-bandwidth;
        uses eth-state {
          augment "ethernet" {
            description
              "Physical ethernet parameters.";
            uses ntos-if:ethernet-transmission-config;
          }
        }
        uses physical-port-attr;
        leaf host-br {
          type ntos-types:ifname;
          ntos-extensions:nc-cli-one-liner;
          description
            "The bridge interface which the physical interface belongs to.";
        }
        leaf host-vswitch {
          type ntos-types:ifname;
          ntos-extensions:nc-cli-one-liner;
          description
            "The vswitch interface which the physical interface belongs to.";
        }
        leaf ip-mac-enabled {
          type boolean;
          description
            "The interface IP-MAC binding state.";
        }
        leaf port-speed-status {
          type uint16;
          description
            "The interface speed status.";
        }
      }
    }
  }

  rpc show-port-panel-info {
    ntos-extensions:nc-cli-show "port panel-info";
    ntos-api:internal;
    description
      "Show port panel information.";
    input {
      leaf name {
        ntos-extensions:nc-cli-completion-xpath "/ntos:state/ntos:vrf/ntos-interface:interface/physical/*[local-name()='name']";
        type ntos-types:ifname;
        description
          "Show interface by this name.";
      }
    }
    output {
      list ports {
        description
          "Output for port list";
        uses interface-state;
        uses physical-state;
        uses ntos-if:interface-common-state;
        uses ntos-ip:ntos-ipv4-state;
        uses ntos-ip:ntos-ipv6-state;
        uses eth-state;
        uses physical-port-attr;
      }
    }
  }

  rpc show-port-factory-mac-addr {
    ntos-extensions:nc-cli-show "port factory-mac-addr";
    ntos-extensions:nc-cli-command-no-pager;
    ntos-api:internal;
    description
      "Show factory MAC address.";
    input {
      leaf vrf {
        ntos-extensions:nc-cli-completion-xpath "/ntos:state/ntos:vrf/ntos:name";
        type string;
        default "main";
        description
          "Specify the VRF.";
      }

      leaf name {
        ntos-extensions:nc-cli-completion-xpath "/ntos:state/ntos:vrf/ntos-interface:interface/physical/*[local-name()='name']";
        type ntos-types:ifname;
        mandatory true;
        description
          "The interface name.";
      }
    }

    output {
      leaf mac-address {
          type ntos-if:mac-address;
          description
            "The factory MAC address.";
      }
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
  }

  rpc clear-interface-counters {
    description
      "Clear physical interfaces counters.";
    input {
      leaf name {
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos-interface:interface/*/*[local-name()='name']";
        type string;
        description
          "The interface name.";
      }
    }

    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-extensions:nc-cli-cmd "clear interface counters";
    ntos-extensions:nc-cli-command-no-pager;
    ntos-api:internal;
  }

  rpc dhcp-client-renew-lease {
    description
      "Renew DHCP client lease period.";
    input {

      leaf vrf {
        type string;
        default "main";
        description
          "Specify the VRF.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf ifname {
        type ntos-types:ifname;
        mandatory true;
        description
          "The interface name.";
        ntos-extensions:nc-cli-no-name;
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos-interface:interface/*/*[local-name()='name']";
      }
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-extensions:nc-cli-cmd "dhcp-client renew-lease";
    ntos-extensions:nc-cli-command-no-pager;
    ntos-api:internal;
  }

  grouping monitor-setting {
    description
        "The grouping data for interface monitor setting.";
      leaf if-usage-compute-interval {
        type int32 {
          range "5..300";
        }                                                   
        units "seconds";
        default "30";
        description
            "The time between rate computations. 
             When the value of if-usage-compute-interval is changed,
             the interval in-progress proceeds as though the value
             had not changed. The change will apply to the length
             of subsequent intervals.";
       }

       leaf if-global-notify-enable {
         type boolean;
         default "true";
         description
             "This object specifies whether the system produces the
              notification on this interface.
              A false value prevents such notifications from
              being generated by this system.";              
         }
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "Network interfaces configuration.";

    container interface {
      description
        "Interface configuration.";
      ext-cond:unique-values "*/*[local-name()='name']" {
        error-message "The interface name must be unique in a vrf.";
      }

      container snmp {                                                
        description                                                           
          "snmp interface monitor setting.";                                       
        uses monitor-setting;                                                 
      }  

      list physical {
        key "name";
        description
          "The list of physical interfaces on the device.";

        leaf port {
          type union {
            type ntos-types:pci-port-name;
            type ntos-types:device-tree-port-name;
          }
          must 'count(/ntos:config/ntos:vrf/ntos-interface:interface/ntos-interface:physical/ntos-interface:port[current()=.]) <= 1' {
            error-message "A PCI port can only be bound once.";
          }
          description
            "Reference to a physical network port.";
          ntos-api:pattern-added "pci-(d[0-9]+)?(b[0-9]+)(s[0-9]+)(f[0-9]+)?(p[0-9]+)?";
          ntos-api:pattern-added "dt-(.+)";
          ntos-extensions:nc-cli-completion-xpath
            "/ntos:state/ntos-network-ports:network-port/ntos-network-ports:name";
        }
        uses phy-interface-config;
        uses physical-config;
        uses cp-protection-config;
        uses ntos-ip:ntos-ipv4-config;
        uses ntos-ip:ntos-ipv6-config;
        uses ntos-ip:ntos-network-stack-parameters;
        uses ntos-routing:reverse-path;
        uses network-access-attr;
        uses interface-bandwidth;
        uses eth-config {

          augment "ethernet" {
            description
              "Physical ethernet parameters.";
            uses ntos-if:ethernet-transmission-config;
          }
        }
        uses ntos-qos:physical-if-qos-config;
        uses ntos-dhcp-snp:dhcp-snp-parameters;
        uses ntos-pppoe-server:pppoe-server-config;
      }
    }
  }

  augment "/ntos:state/ntos:vrf" {
    description
      "Network interfaces operational state data.";

    container interface {
      config false;
      description
        "Interface state.";

      list physical {
        key "name";
        description
          "The list of physical interfaces on the device.";
        uses interface-state;
        uses physical-state;
        uses cp-protection-config;
        uses ntos-if:interface-common-state;
        uses ntos-if:interface-counters-state;
        uses ntos-ip:ntos-ipv4-state;
        uses ntos-ip:ntos-ipv6-state;
        uses ntos-ip:ntos-network-stack-parameters;
        uses interface-bandwidth;
        uses eth-state {

          augment "ethernet" {
            description
              "Physical ethernet parameters.";
            uses ntos-if:ethernet-transmission-config;
          }
        }
        uses ntos-qos:physical-if-qos-state;
        uses ntos-dhcp-snp:dhcp-snp-parameters;
      }
    }
  }

  augment "/ntos:config/ntos:vrf/ntos-interface:interface/ntos-interface:physical/ntos-interface:ipv4/ntos-interface:pppoe/ntos-interface:connection" {
    uses ntos-routing:reverse-path;
    uses network-access-attr;
    uses interface-bandwidth;
    uses eth-config;
  }

  augment "/ntos:state/ntos:vrf/ntos-interface:interface/ntos-interface:physical/ntos-interface:ipv4/ntos-interface:pppoe/ntos-interface:connection" {
    uses ntos-routing:reverse-path;
    uses network-access-attr;
    uses interface-bandwidth;
    uses eth-state;
  }
}
