module iana-timezones {
  namespace "urn:ietf:params:xml:ns:yang:iana-timezones";
  prefix ianatz;

  organization "IANA";
  contact
    "        Internet Assigned Numbers Authority

     Postal: ICANN
             4676 Admiralty Way, Suite 330
             Marina del Rey, CA 90292

     Tel:    ****** 823 9358
     E-Mail: iana&iana.org";
  description
    "This YANG module defines the iana-timezone typedef, which
     contains YANG definitions for IANA-registered timezones.

     This YANG module is maintained by IANA, and reflects the
     IANA Time Zone Database.
     (http://www.iana.org/time-zones)

     The latest revision of this YANG module can be obtained from
     the IANA web site.

     Copyright (c) 2011 IETF Trust and the persons identified as
     authors of the code.  All rights reserved.

     Redistribution and use in source and binary forms, with or
     without modification, is permitted pursuant to, and subject
     to the license terms contained in, the Simplified BSD License
     set forth in Section 4.c of the IETF Trust's Legal Provisions
     Relating to IETF Documents
     (http://trustee.ietf.org/license-info).

     This version of this YANG module is part of RFC XXXX; see
     the RFC itself for full legal notices.";

  revision 2012-07-09 {
    description
      "Initial revision. Using IANA Time Zone Data v. 2012c
       (Released 2012-03-27)";
    reference "RFC XXXX: TITLE";
  }
  typedef iana-timezone {
    type enumeration {
      enum "Europe/Andorra" {
        value 0;
      }
      enum "Asia/Dubai" {
        value 1;
      }
      enum "Asia/Kabul" {
        value 2;
      }
      enum "America/Antigua" {
        value 3;
      }
      enum "America/Anguilla" {
        value 4;
      }
      enum "Europe/Tirane" {
        value 5;
      }
      enum "Asia/Yerevan" {
        value 6;
      }
      enum "Africa/Luanda" {
        value 7;
      }
      enum "Antarctica/McMurdo" {
        value 8;
        description
          "McMurdo Station, Ross Island";
      }
      enum "Antarctica/South_Pole" {
        value 9;
        description
          "Amundsen-Scott Station, South Pole";
      }
      enum "Antarctica/Rothera" {
        value 10;
        description
          "Rothera Station, Adelaide Island";
      }
      enum "Antarctica/Palmer" {
        value 11;
        description
          "Palmer Station, Anvers Island";
      }
      enum "Antarctica/Mawson" {
        value 12;
        description
          "Mawson Station, Holme Bay";
      }
      enum "Antarctica/Davis" {
        value 13;
        description
          "Davis Station, Vestfold Hills";
      }
      enum "Antarctica/Casey" {
        value 14;
        description
          "Casey Station, Bailey Peninsula";
      }
      enum "Antarctica/Vostok" {
        value 15;
        description
          "Vostok Station, Lake Vostok";
      }
      enum "Antarctica/DumontDUrville" {
        value 16;
        description
          "Dumont-d'Urville Station, Terre Adelie";
      }
      enum "Antarctica/Syowa" {
        value 17;
        description
          "Syowa Station, E Ongul I";
      }
      enum "Antarctica/Macquarie" {
        value 18;
        description
          "Macquarie Island Station, Macquarie Island";
      }
      enum "America/Argentina/Buenos_Aires" {
        value 19;
        description
          "Buenos Aires (BA, CF)";
      }
      enum "America/Argentina/Cordoba" {
        value 20;
        description
          "most locations (CB, CC, CN, ER, FM, MN, SE, SF)";
      }
      enum "America/Argentina/Salta" {
        value 21;
        description
          "(SA, LP, NQ, RN)";
      }
      enum "America/Argentina/Jujuy" {
        value 22;
        description
          "Jujuy (JY)";
      }
      enum "America/Argentina/Tucuman" {
        value 23;
        description
          "Tucuman (TM)";
      }
      enum "America/Argentina/Catamarca" {
        value 24;
        description
          "Catamarca (CT), Chubut (CH)";
      }
      enum "America/Argentina/La_Rioja" {
        value 25;
        description
          "La Rioja (LR)";
      }
      enum "America/Argentina/San_Juan" {
        value 26;
        description
          "San Juan (SJ)";
      }
      enum "America/Argentina/Mendoza" {
        value 27;
        description
          "Mendoza (MZ)";
      }
      enum "America/Argentina/San_Luis" {
        value 28;
        description
          "San Luis (SL)";
      }
      enum "America/Argentina/Rio_Gallegos" {
        value 29;
        description
          "Santa Cruz (SC)";
      }
      enum "America/Argentina/Ushuaia" {
        value 30;
        description
          "Tierra del Fuego (TF)";
      }
      enum "Pacific/Pago_Pago" {
        value 31;
      }
      enum "Europe/Vienna" {
        value 32;
      }
      enum "Australia/Lord_Howe" {
        value 33;
        description
          "Lord Howe Island";
      }
      enum "Australia/Hobart" {
        value 34;
        description
          "Tasmania - most locations";
      }
      enum "Australia/Currie" {
        value 35;
        description
          "Tasmania - King Island";
      }
      enum "Australia/Melbourne" {
        value 36;
        description
          "Victoria";
      }
      enum "Australia/Sydney" {
        value 37;
        description
          "New South Wales - most locations";
      }
      enum "Australia/Broken_Hill" {
        value 38;
        description
          "New South Wales - Yancowinna";
      }
      enum "Australia/Brisbane" {
        value 39;
        description
          "Queensland - most locations";
      }
      enum "Australia/Lindeman" {
        value 40;
        description
          "Queensland - Holiday Islands";
      }
      enum "Australia/Adelaide" {
        value 41;
        description
          "South Australia";
      }
      enum "Australia/Darwin" {
        value 42;
        description
          "Northern Territory";
      }
      enum "Australia/Perth" {
        value 43;
        description
          "Western Australia - most locations";
      }
      enum "Australia/Eucla" {
        value 44;
        description
          "Western Australia - Eucla area";
      }
      enum "America/Aruba" {
        value 45;
      }
      enum "Europe/Mariehamn" {
        value 46;
      }
      enum "Asia/Baku" {
        value 47;
      }
      enum "Europe/Sarajevo" {
        value 48;
      }
      enum "America/Barbados" {
        value 49;
      }
      enum "Asia/Dhaka" {
        value 50;
      }
      enum "Europe/Brussels" {
        value 51;
      }
      enum "Africa/Ouagadougou" {
        value 52;
      }
      enum "Europe/Sofia" {
        value 53;
      }
      enum "Asia/Bahrain" {
        value 54;
      }
      enum "Africa/Bujumbura" {
        value 55;
      }
      enum "Africa/Porto-Novo" {
        value 56;
      }
      enum "America/St_Barthelemy" {
        value 57;
      }
      enum "Atlantic/Bermuda" {
        value 58;
      }
      enum "Asia/Brunei" {
        value 59;
      }
      enum "America/La_Paz" {
        value 60;
      }
      enum "America/Kralendijk" {
        value 61;
      }
      enum "America/Noronha" {
        value 62;
        description
          "Atlantic islands";
      }
      enum "America/Belem" {
        value 63;
        description
          "Amapa, E Para";
      }
      enum "America/Fortaleza" {
        value 64;
        description
          "NE Brazil (MA, PI, CE, RN, PB)";
      }
      enum "America/Recife" {
        value 65;
        description
          "Pernambuco";
      }
      enum "America/Araguaina" {
        value 66;
        description
          "Tocantins";
      }
      enum "America/Maceio" {
        value 67;
        description
          "Alagoas, Sergipe";
      }
      enum "America/Bahia" {
        value 68;
        description
          "Bahia";
      }
      enum "America/Sao_Paulo" {
        value 69;
        description
          "S & SE Brazil (GO, DF, MG, ES, RJ, SP, PR, SC, RS)";
      }
      enum "America/Campo_Grande" {
        value 70;
        description
          "Mato Grosso do Sul";
      }
      enum "America/Cuiaba" {
        value 71;
        description
          "Mato Grosso";
      }
      enum "America/Santarem" {
        value 72;
        description
          "W Para";
      }
      enum "America/Porto_Velho" {
        value 73;
        description
          "Rondonia";
      }
      enum "America/Boa_Vista" {
        value 74;
        description
          "Roraima";
      }
      enum "America/Manaus" {
        value 75;
        description
          "E Amazonas";
      }
      enum "America/Eirunepe" {
        value 76;
        description
          "W Amazonas";
      }
      enum "America/Rio_Branco" {
        value 77;
        description
          "Acre";
      }
      enum "America/Nassau" {
        value 78;
      }
      enum "Asia/Thimphu" {
        value 79;
      }
      enum "Africa/Gaborone" {
        value 80;
      }
      enum "Europe/Minsk" {
        value 81;
      }
      enum "America/Belize" {
        value 82;
      }
      enum "America/St_Johns" {
        value 83;
        description
          "Newfoundland Time, including SE Labrador";
      }
      enum "America/Halifax" {
        value 84;
        description
          "Atlantic Time - Nova Scotia (most places), PEI";
      }
      enum "America/Glace_Bay" {
        value 85;
        description
          "Atlantic Time - Nova Scotia - places that did not observe
           DST 1966-1971";
      }
      enum "America/Moncton" {
        value 86;
        description
          "Atlantic Time - New Brunswick";
      }
      enum "America/Goose_Bay" {
        value 87;
        description
          "Atlantic Time - Labrador - most locations";
      }
      enum "America/Blanc-Sablon" {
        value 88;
        description
          "Atlantic Standard Time - Quebec - Lower North Shore";
      }
      enum "America/Montreal" {
        value 89;
        description
          "Eastern Time - Quebec - most locations";
      }
      enum "America/Toronto" {
        value 90;
        description
          "Eastern Time - Ontario - most locations";
      }
      enum "America/Nipigon" {
        value 91;
        description
          "Eastern Time - Ontario & Quebec - places that did not
           observe DST 1967-1973";
      }
      enum "America/Thunder_Bay" {
        value 92;
        description
          "Eastern Time - Thunder Bay, Ontario";
      }
      enum "America/Iqaluit" {
        value 93;
        description
          "Eastern Time - east Nunavut - most locations";
      }
      enum "America/Pangnirtung" {
        value 94;
        description
          "Eastern Time - Pangnirtung, Nunavut";
      }
      enum "America/Resolute" {
        value 95;
        description
          "Central Standard Time - Resolute, Nunavut";
      }
      enum "America/Atikokan" {
        value 96;
        description
          "Eastern Standard Time - Atikokan, Ontario and Southampton I,
           Nunavut";
      }
      enum "America/Rankin_Inlet" {
        value 97;
        description
          "Central Time - central Nunavut";
      }
      enum "America/Winnipeg" {
        value 98;
        description
          "Central Time - Manitoba & west Ontario";
      }
      enum "America/Rainy_River" {
        value 99;
        description
          "Central Time - Rainy River & Fort Frances, Ontario";
      }
      enum "America/Regina" {
        value 100;
        description
          "Central Standard Time - Saskatchewan - most locations";
      }
      enum "America/Swift_Current" {
        value 101;
        description
          "Central Standard Time - Saskatchewan - midwest";
      }
      enum "America/Edmonton" {
        value 102;
        description
          "Mountain Time - Alberta, east British Columbia & west
           Saskatchewan";
      }
      enum "America/Cambridge_Bay" {
        value 103;
        description
          "Mountain Time - west Nunavut";
      }
      enum "America/Yellowknife" {
        value 104;
        description
          "Mountain Time - central Northwest Territories";
      }
      enum "America/Inuvik" {
        value 105;
        description
          "Mountain Time - west Northwest Territories";
      }
      enum "America/Creston" {
        value 106;
        description
          "Mountain Standard Time - Creston, British Columbia";
      }
      enum "America/Dawson_Creek" {
        value 107;
        description
          "Mountain Standard Time - Dawson Creek & Fort Saint John,
           British Columbia";
      }
      enum "America/Vancouver" {
        value 108;
        description
          "Pacific Time - west British Columbia";
      }
      enum "America/Whitehorse" {
        value 109;
        description
          "Pacific Time - south Yukon";
      }
      enum "America/Dawson" {
        value 110;
        description
          "Pacific Time - north Yukon";
      }
      enum "Indian/Cocos" {
        value 111;
      }
      enum "Africa/Kinshasa" {
        value 112;
        description
          "west Dem. Rep. of Congo";
      }
      enum "Africa/Lubumbashi" {
        value 113;
        description
          "east Dem. Rep. of Congo";
      }
      enum "Africa/Bangui" {
        value 114;
      }
      enum "Africa/Brazzaville" {
        value 115;
      }
      enum "Europe/Zurich" {
        value 116;
      }
      enum "Africa/Abidjan" {
        value 117;
      }
      enum "Pacific/Rarotonga" {
        value 118;
      }
      enum "America/Santiago" {
        value 119;
        description
          "most locations";
      }
      enum "Pacific/Easter" {
        value 120;
        description
          "Easter Island & Sala y Gomez";
      }
      enum "Africa/Douala" {
        value 121;
      }
      enum "Asia/Shanghai" {
        value 122;
        description
          "east China - Beijing, Guangdong, Shanghai, etc.";
      }
      enum "Asia/Harbin" {
        value 123;
        description
          "Heilongjiang (except Mohe), Jilin";
      }
      enum "Asia/Chongqing" {
        value 124;
        description
          "central China - Sichuan, Yunnan, Guangxi, Shaanxi, Guizhou,
           etc.";
      }
      enum "Asia/Urumqi" {
        value 125;
        description
          "most of Tibet & Xinjiang";
      }
      enum "Asia/Kashgar" {
        value 126;
        description
          "west Tibet & Xinjiang";
      }
      enum "America/Bogota" {
        value 127;
      }
      enum "America/Costa_Rica" {
        value 128;
      }
      enum "America/Havana" {
        value 129;
      }
      enum "Atlantic/Cape_Verde" {
        value 130;
      }
      enum "America/Curacao" {
        value 131;
      }
      enum "Indian/Christmas" {
        value 132;
      }
      enum "Asia/Nicosia" {
        value 133;
      }
      enum "Europe/Prague" {
        value 134;
      }
      enum "Europe/Berlin" {
        value 135;
      }
      enum "Africa/Djibouti" {
        value 136;
      }
      enum "Europe/Copenhagen" {
        value 137;
      }
      enum "America/Dominica" {
        value 138;
      }
      enum "America/Santo_Domingo" {
        value 139;
      }
      enum "Africa/Algiers" {
        value 140;
      }
      enum "America/Guayaquil" {
        value 141;
        description
          "mainland";
      }
      enum "Pacific/Galapagos" {
        value 142;
        description
          "Galapagos Islands";
      }
      enum "Europe/Tallinn" {
        value 143;
      }
      enum "Africa/Cairo" {
        value 144;
      }
      enum "Africa/El_Aaiun" {
        value 145;
      }
      enum "Africa/Asmara" {
        value 146;
      }
      enum "Europe/Madrid" {
        value 147;
        description
          "mainland";
      }
      enum "Africa/Ceuta" {
        value 148;
        description
          "Ceuta & Melilla";
      }
      enum "Atlantic/Canary" {
        value 149;
        description
          "Canary Islands";
      }
      enum "Africa/Addis_Ababa" {
        value 150;
      }
      enum "Europe/Helsinki" {
        value 151;
      }
      enum "Pacific/Fiji" {
        value 152;
      }
      enum "Atlantic/Stanley" {
        value 153;
      }
      enum "Pacific/Chuuk" {
        value 154;
        description
          "Chuuk (Truk) and Yap";
      }
      enum "Pacific/Pohnpei" {
        value 155;
        description
          "Pohnpei (Ponape)";
      }
      enum "Pacific/Kosrae" {
        value 156;
        description
          "Kosrae";
      }
      enum "Atlantic/Faroe" {
        value 157;
      }
      enum "Europe/Paris" {
        value 158;
      }
      enum "Africa/Libreville" {
        value 159;
      }
      enum "Europe/London" {
        value 160;
      }
      enum "America/Grenada" {
        value 161;
      }
      enum "Asia/Tbilisi" {
        value 162;
      }
      enum "America/Cayenne" {
        value 163;
      }
      enum "Europe/Guernsey" {
        value 164;
      }
      enum "Africa/Accra" {
        value 165;
      }
      enum "Europe/Gibraltar" {
        value 166;
      }
      enum "America/Godthab" {
        value 167;
        description
          "most locations";
      }
      enum "America/Danmarkshavn" {
        value 168;
        description
          "east coast, north of Scoresbysund";
      }
      enum "America/Scoresbysund" {
        value 169;
        description
          "Scoresbysund / Ittoqqortoormiit";
      }
      enum "America/Thule" {
        value 170;
        description
          "Thule / Pituffik";
      }
      enum "Africa/Banjul" {
        value 171;
      }
      enum "Africa/Conakry" {
        value 172;
      }
      enum "America/Guadeloupe" {
        value 173;
      }
      enum "Africa/Malabo" {
        value 174;
      }
      enum "Europe/Athens" {
        value 175;
      }
      enum "Atlantic/South_Georgia" {
        value 176;
      }
      enum "America/Guatemala" {
        value 177;
      }
      enum "Pacific/Guam" {
        value 178;
      }
      enum "Africa/Bissau" {
        value 179;
      }
      enum "America/Guyana" {
        value 180;
      }
      enum "Asia/Hong_Kong" {
        value 181;
      }
      enum "America/Tegucigalpa" {
        value 182;
      }
      enum "Europe/Zagreb" {
        value 183;
      }
      enum "America/Port-au-Prince" {
        value 184;
      }
      enum "Europe/Budapest" {
        value 185;
      }
      enum "Asia/Jakarta" {
        value 186;
        description
          "Java & Sumatra";
      }
      enum "Asia/Pontianak" {
        value 187;
        description
          "west & central Borneo";
      }
      enum "Asia/Makassar" {
        value 188;
        description
          "east & south Borneo, Sulawesi (Celebes), Bali, Nusa
           Tengarra, west Timor";
      }
      enum "Asia/Jayapura" {
        value 189;
        description
          "west New Guinea (Irian Jaya) & Malukus (Moluccas)";
      }
      /* Disabled temporarily.
       * enum "Europe/Dublin" {
       *   value 190;
       * }
       */
      enum "Asia/Jerusalem" {
        value 191;
      }
      enum "Europe/Isle_of_Man" {
        value 192;
      }
      enum "Asia/Kolkata" {
        value 193;
      }
      enum "Indian/Chagos" {
        value 194;
      }
      enum "Asia/Baghdad" {
        value 195;
      }
      enum "Asia/Tehran" {
        value 196;
      }
      enum "Atlantic/Reykjavik" {
        value 197;
      }
      enum "Europe/Rome" {
        value 198;
      }
      enum "Europe/Jersey" {
        value 199;
      }
      enum "America/Jamaica" {
        value 200;
      }
      enum "Asia/Amman" {
        value 201;
      }
      enum "Asia/Tokyo" {
        value 202;
      }
      enum "Africa/Nairobi" {
        value 203;
      }
      enum "Asia/Bishkek" {
        value 204;
      }
      enum "Asia/Phnom_Penh" {
        value 205;
      }
      enum "Pacific/Tarawa" {
        value 206;
        description
          "Gilbert Islands";
      }
      enum "Pacific/Enderbury" {
        value 207;
        description
          "Phoenix Islands";
      }
      enum "Pacific/Kiritimati" {
        value 208;
        description
          "Line Islands";
      }
      enum "Indian/Comoro" {
        value 209;
      }
      enum "America/St_Kitts" {
        value 210;
      }
      enum "Asia/Pyongyang" {
        value 211;
      }
      enum "Asia/Seoul" {
        value 212;
      }
      enum "Asia/Kuwait" {
        value 213;
      }
      enum "America/Cayman" {
        value 214;
      }
      enum "Asia/Almaty" {
        value 215;
        description
          "most locations";
      }
      enum "Asia/Qyzylorda" {
        value 216;
        description
          "Qyzylorda (Kyzylorda, Kzyl-Orda)";
      }
      enum "Asia/Aqtobe" {
        value 217;
        description
          "Aqtobe (Aktobe)";
      }
      enum "Asia/Aqtau" {
        value 218;
        description
          "Atyrau (Atirau, Gur'yev), Mangghystau (Mankistau)";
      }
      enum "Asia/Oral" {
        value 219;
        description
          "West Kazakhstan";
      }
      enum "Asia/Vientiane" {
        value 220;
      }
      enum "Asia/Beirut" {
        value 221;
      }
      enum "America/St_Lucia" {
        value 222;
      }
      enum "Europe/Vaduz" {
        value 223;
      }
      enum "Asia/Colombo" {
        value 224;
      }
      enum "Africa/Monrovia" {
        value 225;
      }
      enum "Africa/Maseru" {
        value 226;
      }
      enum "Europe/Vilnius" {
        value 227;
      }
      enum "Europe/Luxembourg" {
        value 228;
      }
      enum "Europe/Riga" {
        value 229;
      }
      enum "Africa/Tripoli" {
        value 230;
      }
      enum "Africa/Casablanca" {
        value 231;
      }
      enum "Europe/Monaco" {
        value 232;
      }
      enum "Europe/Chisinau" {
        value 233;
      }
      enum "Europe/Podgorica" {
        value 234;
      }
      enum "America/Marigot" {
        value 235;
      }
      enum "Indian/Antananarivo" {
        value 236;
      }
      enum "Pacific/Majuro" {
        value 237;
        description
          "most locations";
      }
      enum "Pacific/Kwajalein" {
        value 238;
        description
          "Kwajalein";
      }
      enum "Europe/Skopje" {
        value 239;
      }
      enum "Africa/Bamako" {
        value 240;
      }
      enum "Asia/Rangoon" {
        value 241;
      }
      enum "Asia/Ulaanbaatar" {
        value 242;
        description
          "most locations";
      }
      enum "Asia/Hovd" {
        value 243;
        description
          "Bayan-Olgiy, Govi-Altai, Hovd, Uvs, Zavkhan";
      }
      enum "Asia/Choibalsan" {
        value 244;
        description
          "Dornod, Sukhbaatar";
      }
      enum "Asia/Macau" {
        value 245;
      }
      enum "Pacific/Saipan" {
        value 246;
      }
      enum "America/Martinique" {
        value 247;
      }
      enum "Africa/Nouakchott" {
        value 248;
      }
      enum "America/Montserrat" {
        value 249;
      }
      enum "Europe/Malta" {
        value 250;
      }
      enum "Indian/Mauritius" {
        value 251;
      }
      enum "Indian/Maldives" {
        value 252;
      }
      enum "Africa/Blantyre" {
        value 253;
      }
      enum "America/Mexico_City" {
        value 254;
        description
          "Central Time - most locations";
      }
      enum "America/Cancun" {
        value 255;
        description
          "Central Time - Quintana Roo";
      }
      enum "America/Merida" {
        value 256;
        description
          "Central Time - Campeche, Yucatan";
      }
      enum "America/Monterrey" {
        value 257;
        description
          "Mexican Central Time - Coahuila, Durango, Nuevo Leon,
           Tamaulipas away from US border";
      }
      enum "America/Matamoros" {
        value 258;
        description
          "US Central Time - Coahuila, Durango, Nuevo Leon, Tamaulipas
           near US border";
      }
      enum "America/Mazatlan" {
        value 259;
        description
          "Mountain Time - S Baja, Nayarit, Sinaloa";
      }
      enum "America/Chihuahua" {
        value 260;
        description
          "Mexican Mountain Time - Chihuahua away from US border";
      }
      enum "America/Ojinaga" {
        value 261;
        description
          "US Mountain Time - Chihuahua near US border";
      }
      enum "America/Hermosillo" {
        value 262;
        description
          "Mountain Standard Time - Sonora";
      }
      enum "America/Tijuana" {
        value 263;
        description
          "US Pacific Time - Baja California near US border";
      }
      enum "America/Santa_Isabel" {
        value 264;
        description
          "Mexican Pacific Time - Baja California away from US border";
      }
      enum "America/Bahia_Banderas" {
        value 265;
        description
          "Mexican Central Time - Bahia de Banderas";
      }
      enum "Asia/Kuala_Lumpur" {
        value 266;
        description
          "peninsular Malaysia";
      }
      enum "Asia/Kuching" {
        value 267;
        description
          "Sabah & Sarawak";
      }
      enum "Africa/Maputo" {
        value 268;
      }
      /* Disabled temporarily.
       * enum "Africa/Windhoek" {
       *   value 269;
       * }
       */
      enum "Pacific/Noumea" {
        value 270;
      }
      enum "Africa/Niamey" {
        value 271;
      }
      enum "Pacific/Norfolk" {
        value 272;
      }
      enum "Africa/Lagos" {
        value 273;
      }
      enum "America/Managua" {
        value 274;
      }
      enum "Europe/Amsterdam" {
        value 275;
      }
      enum "Europe/Oslo" {
        value 276;
      }
      enum "Asia/Kathmandu" {
        value 277;
      }
      enum "Pacific/Nauru" {
        value 278;
      }
      enum "Pacific/Niue" {
        value 279;
      }
      enum "Pacific/Auckland" {
        value 280;
        description
          "most locations";
      }
      enum "Pacific/Chatham" {
        value 281;
        description
          "Chatham Islands";
      }
      enum "Asia/Muscat" {
        value 282;
      }
      enum "America/Panama" {
        value 283;
      }
      enum "America/Lima" {
        value 284;
      }
      enum "Pacific/Tahiti" {
        value 285;
        description
          "Society Islands";
      }
      enum "Pacific/Marquesas" {
        value 286;
        description
          "Marquesas Islands";
      }
      enum "Pacific/Gambier" {
        value 287;
        description
          "Gambier Islands";
      }
      enum "Pacific/Port_Moresby" {
        value 288;
      }
      enum "Asia/Manila" {
        value 289;
      }
      enum "Asia/Karachi" {
        value 290;
      }
      enum "Europe/Warsaw" {
        value 291;
      }
      enum "America/Miquelon" {
        value 292;
      }
      enum "Pacific/Pitcairn" {
        value 293;
      }
      enum "America/Puerto_Rico" {
        value 294;
      }
      enum "Asia/Gaza" {
        value 295;
        description
          "Gaza Strip";
      }
      enum "Asia/Hebron" {
        value 296;
        description
          "West Bank";
      }
      enum "Europe/Lisbon" {
        value 297;
        description
          "mainland";
      }
      enum "Atlantic/Madeira" {
        value 298;
        description
          "Madeira Islands";
      }
      enum "Atlantic/Azores" {
        value 299;
        description
          "Azores";
      }
      enum "Pacific/Palau" {
        value 300;
      }
      enum "America/Asuncion" {
        value 301;
      }
      enum "Asia/Qatar" {
        value 302;
      }
      enum "Indian/Reunion" {
        value 303;
      }
      enum "Europe/Bucharest" {
        value 304;
      }
      enum "Europe/Belgrade" {
        value 305;
      }
      enum "Europe/Kaliningrad" {
        value 306;
        description
          "Moscow-01 - Kaliningrad";
      }
      enum "Europe/Moscow" {
        value 307;
        description
          "Moscow+00 - west Russia";
      }
      enum "Europe/Volgograd" {
        value 308;
        description
          "Moscow+00 - Caspian Sea";
      }
      enum "Europe/Samara" {
        value 309;
        description
          "Moscow+00 - Samara, Udmurtia";
      }
      enum "Asia/Yekaterinburg" {
        value 310;
        description
          "Moscow+02 - Urals";
      }
      enum "Asia/Omsk" {
        value 311;
        description
          "Moscow+03 - west Siberia";
      }
      enum "Asia/Novosibirsk" {
        value 312;
        description
          "Moscow+03 - Novosibirsk";
      }
      enum "Asia/Novokuznetsk" {
        value 313;
        description
          "Moscow+03 - Novokuznetsk";
      }
      enum "Asia/Krasnoyarsk" {
        value 314;
        description
          "Moscow+04 - Yenisei River";
      }
      enum "Asia/Irkutsk" {
        value 315;
        description
          "Moscow+05 - Lake Baikal";
      }
      enum "Asia/Yakutsk" {
        value 316;
        description
          "Moscow+06 - Lena River";
      }
      enum "Asia/Vladivostok" {
        value 317;
        description
          "Moscow+07 - Amur River";
      }
      enum "Asia/Sakhalin" {
        value 318;
        description
          "Moscow+07 - Sakhalin Island";
      }
      enum "Asia/Magadan" {
        value 319;
        description
          "Moscow+08 - Magadan";
      }
      enum "Asia/Kamchatka" {
        value 320;
        description
          "Moscow+08 - Kamchatka";
      }
      enum "Asia/Anadyr" {
        value 321;
        description
          "Moscow+08 - Bering Sea";
      }
      enum "Africa/Kigali" {
        value 322;
      }
      enum "Asia/Riyadh" {
        value 323;
      }
      enum "Pacific/Guadalcanal" {
        value 324;
      }
      enum "Indian/Mahe" {
        value 325;
      }
      enum "Africa/Khartoum" {
        value 326;
      }
      enum "Europe/Stockholm" {
        value 327;
      }
      enum "Asia/Singapore" {
        value 328;
      }
      enum "Atlantic/St_Helena" {
        value 329;
      }
      enum "Europe/Ljubljana" {
        value 330;
      }
      enum "Arctic/Longyearbyen" {
        value 331;
      }
      enum "Europe/Bratislava" {
        value 332;
      }
      enum "Africa/Freetown" {
        value 333;
      }
      enum "Europe/San_Marino" {
        value 334;
      }
      enum "Africa/Dakar" {
        value 335;
      }
      enum "Africa/Mogadishu" {
        value 336;
      }
      enum "America/Paramaribo" {
        value 337;
      }
      enum "Africa/Juba" {
        value 338;
      }
      enum "Africa/Sao_Tome" {
        value 339;
      }
      enum "America/El_Salvador" {
        value 340;
      }
      enum "America/Lower_Princes" {
        value 341;
      }
      enum "Asia/Damascus" {
        value 342;
      }
      enum "Africa/Mbabane" {
        value 343;
      }
      enum "America/Grand_Turk" {
        value 344;
      }
      enum "Africa/Ndjamena" {
        value 345;
      }
      enum "Indian/Kerguelen" {
        value 346;
      }
      enum "Africa/Lome" {
        value 347;
      }
      enum "Asia/Bangkok" {
        value 348;
      }
      enum "Asia/Dushanbe" {
        value 349;
      }
      enum "Pacific/Fakaofo" {
        value 350;
      }
      enum "Asia/Dili" {
        value 351;
      }
      enum "Asia/Ashgabat" {
        value 352;
      }
      enum "Africa/Tunis" {
        value 353;
      }
      enum "Pacific/Tongatapu" {
        value 354;
      }
      enum "Europe/Istanbul" {
        value 355;
      }
      enum "America/Port_of_Spain" {
        value 356;
      }
      enum "Pacific/Funafuti" {
        value 357;
      }
      enum "Asia/Taipei" {
        value 358;
      }
      enum "Africa/Dar_es_Salaam" {
        value 359;
      }
      enum "Europe/Kiev" {
        value 360;
        description
          "most locations";
      }
      enum "Europe/Uzhgorod" {
        value 361;
        description
          "Ruthenia";
      }
      enum "Europe/Zaporozhye" {
        value 362;
        description
          "Zaporozh'ye, E Lugansk / Zaporizhia, E Luhansk";
      }
      enum "Europe/Simferopol" {
        value 363;
        description
          "central Crimea";
      }
      enum "Africa/Kampala" {
        value 364;
      }
      enum "Pacific/Johnston" {
        value 365;
        description
          "Johnston Atoll";
      }
      enum "Pacific/Midway" {
        value 366;
        description
          "Midway Islands";
      }
      enum "Pacific/Wake" {
        value 367;
        description
          "Wake Island";
      }
      enum "America/New_York" {
        value 368;
        description
          "Eastern Time";
      }
      enum "America/Detroit" {
        value 369;
        description
          "Eastern Time - Michigan - most locations";
      }
      enum "America/Kentucky/Louisville" {
        value 370;
        description
          "Eastern Time - Kentucky - Louisville area";
      }
      enum "America/Kentucky/Monticello" {
        value 371;
        description
          "Eastern Time - Kentucky - Wayne County";
      }
      enum "America/Indiana/Indianapolis" {
        value 372;
        description
          "Eastern Time - Indiana - most locations";
      }
      enum "America/Indiana/Vincennes" {
        value 373;
        description
          "Eastern Time - Indiana - Daviess, Dubois, Knox & Martin
           Counties";
      }
      enum "America/Indiana/Winamac" {
        value 374;
        description
          "Eastern Time - Indiana - Pulaski County";
      }
      enum "America/Indiana/Marengo" {
        value 375;
        description
          "Eastern Time - Indiana - Crawford County";
      }
      enum "America/Indiana/Petersburg" {
        value 376;
        description
          "Eastern Time - Indiana - Pike County";
      }
      enum "America/Indiana/Vevay" {
        value 377;
        description
          "Eastern Time - Indiana - Switzerland County";
      }
      enum "America/Chicago" {
        value 378;
        description
          "Central Time";
      }
      enum "America/Indiana/Tell_City" {
        value 379;
        description
          "Central Time - Indiana - Perry County";
      }
      enum "America/Indiana/Knox" {
        value 380;
        description
          "Central Time - Indiana - Starke County";
      }
      enum "America/Menominee" {
        value 381;
        description
          "Central Time - Michigan - Dickinson, Gogebic, Iron &
           Menominee Counties";
      }
      enum "America/North_Dakota/Center" {
        value 382;
        description
          "Central Time - North Dakota - Oliver County";
      }
      enum "America/North_Dakota/New_Salem" {
        value 383;
        description
          "Central Time - North Dakota - Morton County (except Mandan
           area)";
      }
      enum "America/North_Dakota/Beulah" {
        value 384;
        description
          "Central Time - North Dakota - Mercer County";
      }
      enum "America/Denver" {
        value 385;
        description
          "Mountain Time";
      }
      enum "America/Boise" {
        value 386;
        description
          "Mountain Time - south Idaho & east Oregon";
      }
      enum "America/Shiprock" {
        value 387;
        description
          "Mountain Time - Navajo";
      }
      enum "America/Phoenix" {
        value 388;
        description
          "Mountain Standard Time - Arizona";
      }
      enum "America/Los_Angeles" {
        value 389;
        description
          "Pacific Time";
      }
      enum "America/Anchorage" {
        value 390;
        description
          "Alaska Time";
      }
      enum "America/Juneau" {
        value 391;
        description
          "Alaska Time - Alaska panhandle";
      }
      enum "America/Sitka" {
        value 392;
        description
          "Alaska Time - southeast Alaska panhandle";
      }
      enum "America/Yakutat" {
        value 393;
        description
          "Alaska Time - Alaska panhandle neck";
      }
      enum "America/Nome" {
        value 394;
        description
          "Alaska Time - west Alaska";
      }
      enum "America/Adak" {
        value 395;
        description
          "Aleutian Islands";
      }
      enum "America/Metlakatla" {
        value 396;
        description
          "Metlakatla Time - Annette Island";
      }
      enum "Pacific/Honolulu" {
        value 397;
        description
          "Hawaii";
      }
      enum "America/Montevideo" {
        value 398;
      }
      enum "Asia/Samarkand" {
        value 399;
        description
          "west Uzbekistan";
      }
      enum "Asia/Tashkent" {
        value 400;
        description
          "east Uzbekistan";
      }
      enum "Europe/Vatican" {
        value 401;
      }
      enum "America/St_Vincent" {
        value 402;
      }
      enum "America/Caracas" {
        value 403;
      }
      enum "America/Tortola" {
        value 404;
      }
      enum "America/St_Thomas" {
        value 405;
      }
      enum "Asia/Ho_Chi_Minh" {
        value 406;
      }
      enum "Pacific/Efate" {
        value 407;
      }
      enum "Pacific/Wallis" {
        value 408;
      }
      enum "Pacific/Apia" {
        value 409;
      }
      enum "Asia/Aden" {
        value 410;
      }
      enum "Indian/Mayotte" {
        value 411;
      }
      enum "Africa/Johannesburg" {
        value 412;
      }
      enum "Africa/Lusaka" {
        value 413;
      }
      enum "Africa/Harare" {
        value 414;
      }
    }
    description
      "A timezone location as defined by the IANA timezone
       database (http://www.iana.org/time-zones)";
  }
}
