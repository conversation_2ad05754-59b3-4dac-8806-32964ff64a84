module ietf-yang-structure-ext {
  yang-version 1.1;
  namespace "urn:ietf:params:xml:ns:yang:ietf-yang-structure-ext";
  prefix sx;

  organization
    "IETF NETMOD (NETCONF Data Modeling Language) Working Group";
  contact
    "WG Web:   <https://datatracker.ietf.org/wg/netmod/>
     WG List:  <mailto:<EMAIL>>

     Author:   <PERSON>
               <mailto:<EMAIL>>

     Author:   <PERSON>
               <mailto:<EMAIL>>

     Author:   <PERSON>
               <mailto:<EMAIL>>";
  description
    "This module contains conceptual YANG specifications for defining
     abstract data structures.

     The key words 'MUST', 'MUST NOT', 'REQUIRED', 'SHALL', 'SHALL
     NOT', 'SHOULD', 'SHOULD NOT', 'RECOMMENDED', 'NOT RECOMMENDED',
     'MAY', and 'OPTIONAL' in this document are to be interpreted as
     described in BCP 14 (RFC 2119) (RFC 8174) when, and only when,
     they appear in all capitals, as shown here.

     Copyright (c) 2020 IETF Trust and the persons identified as
     authors of the code.  All rights reserved.

     Redistribution and use in source and binary forms, with or
     without modification, is permitted pursuant to, and subject to
     the license terms contained in, the Simplified BSD License set
     forth in Section 4.c of the IETF Trust's Legal Provisions
     Relating to IETF Documents
     (http://trustee.ietf.org/license-info).

     This version of this YANG module is part of RFC 8791
     (https://www.rfc-editor.org/info/rfc8791); see the RFC itself
     for full legal notices.";

  revision 2020-06-17 {
    description
      "Initial revision.";
    reference
      "RFC 8791: YANG Data Structure Extensions.";
  }

  extension structure {
    argument name {
      yin-element true;
    }
    description
      "This extension is used to specify a YANG data structure that
       represents conceptual data defined in YANG.  It is intended to
       describe hierarchical data independent of protocol context or
       specific message encoding format.  Data definition statements
       within a 'structure' extension statement specify the generic
       syntax for the specific YANG data structure, whose name is the
       argument of the 'structure' extension statement.

       Note that this extension does not define a media type.  A
       specification using this extension MUST specify the message
       encoding rules, including the content media type, if
       applicable.

       The mandatory 'name' parameter value identifies the YANG data
       structure that is being defined.

       This extension is only valid as a top-level statement, i.e.,
       given as a substatement to 'module' or 'submodule'.

       The substatements of this extension MUST follow the ABNF
       rules below, where the rules are defined in RFC 7950:

           *must-stmt
           [status-stmt]
           [description-stmt]
           [reference-stmt]
           *(typedef-stmt / grouping-stmt)
           *data-def-stmt

       A YANG data structure defined with this extension statement is
       encoded in the same way as an 'anydata' node.  This means
       that the name of the structure is encoded as a 'container',
       with the instantiated child statements encoded as child nodes
       to this node.

       The module name and namespace value for the YANG module using
       the extension statement are assigned to each of the data
       definition statements resulting from the YANG data structure.

       The XPath document element is the extension statement itself,
       such that the child nodes of the document element are
       represented by the data-def-stmt substatements within this
       extension.  This conceptual document is the context for the
       following YANG statements:

           - must-stmt
           - when-stmt
           - path-stmt
           - min-elements-stmt
           - max-elements-stmt
           - mandatory-stmt
           - unique-stmt
           - ordered-by
           - instance-identifier data type

       The following data-def-stmt substatements are constrained
       when used within a 'structure' extension statement.

          - The list-stmt is not required to have a key-stmt defined.
          - The config-stmt is ignored if present.
       ";
  }

  extension augment-structure {
    argument path {
      yin-element true;
    }
    description
      "This extension is used to specify an augmentation to a YANG
       data structure defined with the 'structure' statement.  It is
       intended to describe hierarchical data independent of protocol
       context or specific message encoding format.

       This statement has almost the same structure as the
       'augment-stmt'.  Data definition statements within this
       statement specify the semantics and generic syntax for the
       additional data to be added to the specific YANG data
       structure, identified by the 'path' argument.

       The mandatory 'path' parameter value identifies the YANG
       conceptual data node that is being augmented and is
       represented as an absolute-schema-nodeid string, where the
       first node in the absolute-schema-nodeid string identifies the
       YANG data structure to augment, and the rest of the nodes in
       the string identifies the node within the YANG structure to
       augment.

       This extension is only valid as a top-level statement, i.e.,
       given as a substatement to 'module' or 'submodule'.

       The substatements of this extension MUST follow the ABNF
       rules below, where the rules are defined in RFC 7950:

           [status-stmt]
           [description-stmt]
           [reference-stmt]
           1*(data-def-stmt / case-stmt)

       The module name and namespace value for the YANG module using
       the extension statement are assigned to instance document data
       conforming to the data definition statements within this
       extension.

       The XPath document element is the augmented extension
       statement itself, such that the child nodes of the document
       element are represented by the data-def-stmt substatements
       within the augmented 'structure' statement.

       The context node of the 'augment-structure' statement is
       derived in the same way as the 'augment' statement, as defined
       in Section 6.4.1 of [RFC7950]. This conceptual node is
       considered the context node for the following YANG statements:

           - must-stmt
           - when-stmt
           - path-stmt
           - min-elements-stmt
           - max-elements-stmt
           - mandatory-stmt
           - unique-stmt
           - ordered-by
           - instance-identifier data type

       The following data-def-stmt substatements are constrained
       when used within an 'augment-structure' extension statement.

           - The list-stmt is not required to have a key-stmt defined.
           - The config-stmt is ignored if present.

       Example:

           module foo {
               import ietf-yang-structure-ext { prefix sx; }

               sx:structure foo-data {
               container foo-con { }
               }
           }

           module bar {
               import ietf-yang-structure-ext { prefix sx; }
               import foo { prefix foo; }

               sx:augment-structure /foo:foo-data/foo:foo-con {
               leaf add-leaf1 { type int32; }
               leaf add-leaf2 { type string; }
               }
           }
       ";
  }
}
