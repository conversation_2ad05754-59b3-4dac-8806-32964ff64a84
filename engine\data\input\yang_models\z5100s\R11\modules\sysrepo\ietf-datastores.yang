module ietf-datastores {
  yang-version 1.1;
  namespace "urn:ietf:params:xml:ns:yang:ietf-datastores";
  prefix ds;

  organization
    "IETF Network Modeling (NETMOD) Working Group";

  contact
    "WG Web:   <https://datatracker.ietf.org/wg/netmod/>

     WG List:  <mailto:<EMAIL>>

     Author:   <PERSON>
               <mailto:<EMAIL>>

     Author:   <PERSON><PERSON><PERSON>
               <mailto:j.schoen<PERSON><EMAIL>>

     Author:   <PERSON>
               <mailto:<EMAIL>>

     Author:   <PERSON>
               <mailto:<EMAIL>>

     Author:   <PERSON>
               <<EMAIL>>";

  description
    "This YANG module defines a set of identities for identifying
     datastores.

     Copyright (c) 2018 IETF Trust and the persons identified as
     authors of the code.  All rights reserved.

     Redistribution and use in source and binary forms, with or
     without modification, is permitted pursuant to, and subject to
     the license terms contained in, the Simplified BSD License set
     forth in Section 4.c of the IETF Trust's Legal Provisions
     Relating to IETF Documents
     (https://trustee.ietf.org/license-info).

     This version of this YANG module is part of RFC 8342
     (https://www.rfc-editor.org/info/rfc8342); see the RFC itself
     for full legal notices.";

  revision 2018-02-14 {
    description
      "Initial revision.";
    reference
      "RFC 8342: Network Management Datastore Architecture (NMDA)";
  }

  /*
   * Identities
   */

  identity datastore {
    description
      "Abstract base identity for datastore identities.";
  }

  identity conventional {
    base datastore;
    description
      "Abstract base identity for conventional configuration
       datastores.";
  }

  identity running {
    base conventional;
    description
      "The running configuration datastore.";
  }

  identity candidate {
    base conventional;
    description
      "The candidate configuration datastore.";
  }

  identity startup {
    base conventional;
    description
      "The startup configuration datastore.";
  }

  identity intended {
    base conventional;
    description
      "The intended configuration datastore.";
  }

  identity dynamic {
    base datastore;
    description
      "Abstract base identity for dynamic configuration datastores.";
  }

  identity operational {
    base datastore;
    description
      "The operational state datastore.";
  }

  /*
   * Type definitions
   */

  typedef datastore-ref {
    type identityref {
      base datastore;
    }
    description
      "A datastore identity reference.";
  }
}
