module ietf-netconf-notifications {
   namespace
     "urn:ietf:params:xml:ns:yang:ietf-netconf-notifications";
   prefix ncn;
   import ietf-inet-types { prefix inet; }
   import ietf-netconf { prefix nc; }
   organization
     "IETF NETCONF (Network Configuration Protocol) Working Group";
   contact
     "WG Web:   <http://tools.ietf.org/wg/netconf/>
      WG List:  <mailto:<EMAIL>>
      WG Chair: <PERSON>
                <mailto:<EMAIL>>
      WG Chair: Mehmet Ersue
                <mailto:<EMAIL>>
      Editor:   <PERSON>
                <mailto:<EMAIL>>";
   description
     "This module defines a YANG data model for use with the
      NETCONF protocol that allows the NETCONF client to
      receive common NETCONF base event notifications.
      Copyright (c) 2012 IETF Trust and the persons identified as
      the document authors.  All rights reserved.
      Redistribution and use in source and binary forms, with or
      without modification, is permitted pursuant to, and subject
      to the license terms contained in, the Simplified BSD License
      set forth in Section 4.c of the IETF Trust's Legal Provisions
      Relating to IETF Documents
      (http://trustee.ietf.org/license-info).
      This version of this YANG module is part of RFC 6470; see
      the RFC itself for full legal notices.";
   revision "2012-02-06" {
     description
       "Initial version.";
     reference
       "RFC 6470: NETCONF Base Notifications";
   }
  grouping common-session-parms {
    description
      "Common session parameters to identify a
       management session.";
    leaf username {
      type string;
      mandatory true;
      description
        "Name of the user for the session.";
    }
    leaf session-id {
      type nc:session-id-or-zero-type;
      mandatory true;
      description
        "Identifier of the session.
         A NETCONF session MUST be identified by a non-zero value.
         A non-NETCONF session MAY be identified by the value zero.";
    }
    leaf source-host {
      type inet:ip-address;
      description
        "Address of the remote host for the session.";
    }
  }
   grouping changed-by-parms {
    description
      "Common parameters to identify the source
       of a change event, such as a configuration
       or capability change.";
    container changed-by {
      description
        "Indicates the source of the change.
         If caused by internal action, then the
         empty leaf 'server' will be present.
         If caused by a management session, then
         the name, remote host address, and session ID
         of the session that made the change will be reported.";
      choice server-or-user {
        mandatory true;
        leaf server {
          type empty;
          description
            "If present, the change was caused
             by the server.";
        }
        case by-user {
          uses common-session-parms;
        }
      } // choice server-or-user
    } // container changed-by-parms
  }
  notification netconf-config-change {
    description
      "Generated when the NETCONF server detects that the
       <running> or <startup> configuration datastore
       has been changed by a management session.
       The notification summarizes the edits that
       have been detected.
       The server MAY choose to also generate this
       notification while loading a datastore during the
       boot process for the device.";
    uses changed-by-parms;
    leaf datastore {
      type enumeration {
        enum running {
          description "The <running> datastore has changed.";
        }
        enum startup {
          description "The <startup> datastore has changed";
        }
      }
      default "running";
      description
        "Indicates which configuration datastore has changed.";
    }
    list edit {
      description
        "An edit record SHOULD be present for each distinct
         edit operation that the server has detected on
         the target datastore.  This list MAY be omitted
         if the detailed edit operations are not known.
         The server MAY report entries in this list for
         changes not made by a NETCONF session (e.g., CLI).";
      leaf target {
        type instance-identifier;
        description
          "Topmost node associated with the configuration change.
           A server SHOULD set this object to the node within
           the datastore that is being altered.  A server MAY
           set this object to one of the ancestors of the actual
           node that was changed, or omit this object, if the
           exact node is not known.";
      }
      leaf operation {
        type nc:edit-operation-type;
        description
          "Type of edit operation performed.
           A server MUST set this object to the NETCONF edit
           operation performed on the target datastore.";
      }
    } // list edit
  } // notification netconf-config-change
  notification netconf-capability-change {
    description
      "Generated when the NETCONF server detects that
       the server capabilities have changed.
       Indicates which capabilities have been added, deleted,
       and/or modified.  The manner in which a server
       capability is changed is outside the scope of this
       document.";
    uses changed-by-parms;
    leaf-list added-capability {
      type inet:uri;
      description
        "List of capabilities that have just been added.";
    }
    leaf-list deleted-capability {
      type inet:uri;
      description
        "List of capabilities that have just been deleted.";
    }
    leaf-list modified-capability {
      type inet:uri;
      description
        "List of capabilities that have just been modified.
         A capability is considered to be modified if the
         base URI for the capability has not changed, but
         one or more of the parameters encoded at the end of
         the capability URI have changed.
         The new modified value of the complete URI is returned.";
    }
  } // notification netconf-capability-change
  notification netconf-session-start {
    description
      "Generated when a NETCONF server detects that a
       NETCONF session has started.  A server MAY generate
       this event for non-NETCONF management sessions.
       Indicates the identity of the user that started
       the session.";
    uses common-session-parms;
  } // notification netconf-session-start
  notification netconf-session-end {
    description
      "Generated when a NETCONF server detects that a
       NETCONF session has terminated.
       A server MAY optionally generate this event for
       non-NETCONF management sessions.  Indicates the
       identity of the user that owned the session,
       and why the session was terminated.";
    uses common-session-parms;
    leaf killed-by {
      when "../termination-reason = 'killed'";
      type nc:session-id-type;
      description
        "The ID of the session that directly caused this session
         to be abnormally terminated.  If this session was abnormally
         terminated by a non-NETCONF session unknown to the server,
         then this leaf will not be present.";
    }
    leaf termination-reason {
      type enumeration {
        enum "closed" {
          description
            "The session was terminated by the client in normal
             fashion, e.g., by the NETCONF <close-session>
             protocol operation.";
        }
        enum "killed" {
          description
            "The session was terminated in abnormal
             fashion, e.g., by the NETCONF <kill-session>
             protocol operation.";
        }
        enum "dropped" {
          description
            "The session was terminated because the transport layer
             connection was unexpectedly closed.";
        }
        enum "timeout" {
          description
            "The session was terminated because of inactivity,
             e.g., waiting for the <hello> message or <rpc>
             messages.";
        }
        enum "bad-hello" {
          description
            "The client's <hello> message was invalid.";
        }
        enum "other" {
          description
            "The session was terminated for some other reason.";
        }
      }
      mandatory true;
      description
        "Reason the session was terminated.";
    }
  } // notification netconf-session-end
  notification netconf-confirmed-commit {
    description
      "Generated when a NETCONF server detects that a
       confirmed-commit event has occurred.  Indicates the event
       and the current state of the confirmed-commit procedure
       in progress.";
    reference
      "RFC 6241, Section 8.4";
    uses common-session-parms {
      when "confirm-event != 'timeout'";
    }
    leaf confirm-event {
      type enumeration {
        enum "start" {
          description
            "The confirmed-commit procedure has started.";
        }
        enum "cancel" {
          description
            "The confirmed-commit procedure has been canceled,
             e.g., due to the session being terminated, or an
             explicit <cancel-commit> operation.";
        }
        enum "timeout" {
          description
            "The confirmed-commit procedure has been canceled
             due to the confirm-timeout interval expiring.
             The common session parameters will not be present
             in this sub-mode.";
        }
        enum "extend" {
          description
            "The confirmed-commit timeout has been extended,
             e.g., by a new <confirmed-commit> operation.";
        }
        enum "complete" {
          description
            "The confirmed-commit procedure has been completed.";
        }
      }
      mandatory true;
      description
        "Indicates the event that caused the notification.";
    }
    leaf timeout {
      when
        "../confirm-event = 'start' or ../confirm-event = 'extend'";
      type uint32;
      units "seconds";
      description
        "The configured timeout value if the event type
         is 'start' or 'extend'.  This value represents
         the approximate number of seconds from the event
         time when the 'timeout' event might occur.";
    }
  } // notification netconf-confirmed-commit
}
