module ntos-aaa {
  namespace "urn:ruijie:ntos:params:xml:ns:yang:system:aaa";
  prefix ntos-aaa;

  import ietf-netconf-acm {
    prefix nacm;
  }
  import ntos {
    prefix ntos;
  }
  import ntos-inet-types {
    prefix ntos-inet-types;
  }
  import ntos-system {
    prefix ntos-system;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS Authentication, Authorization and Accounting management.";

  revision 2018-11-19 {
    description
      "Initial version.";
    reference "";
  }

  grouping aaa-config {
    description
      "AAA servers configuration.";

    list tacacs {
      key "order";
      description
        "List of tacacs servers on the system.";

      leaf order {
        type uint32;
        must "count(../../*[local-name()='tacacs']/*[local-name()='order']) < 9" {
          error-message "Cannot specify more than 8 TACACS+ servers.";
        }
        description
          "Order for TACACS+ servers. They will be reached by increasing order value.";
      }

      leaf address {
        type ntos-inet-types:ip-address;
        mandatory true;
        description
          "TACACS+ server IPv4 or IPv6 address. It has to be accessible from vrf 'main'.";
      }

      leaf port {
        type uint16;
        default "49";
        description
          "Port number to reach the TACACS server.";
      }

      leaf secret {
        type string;
        mandatory true;
        description
          "TACACS+ client/server shared secret.";
      }

      leaf timeout {
        type uint8 {
          range "1..90";
        }
        default "3";
        description
          "Timeout before trying to reach another TACACS+ server.";
      }
      nacm:default-deny-all;
    }
  }

  augment "/ntos:config/ntos-system:system" {
    description
      "Authentication, Authorization and Accounting configuration.";

    container aaa {
      presence "Makes aaa available";
      description
        "Configuration data for aaa servers.";
      ntos-ext:feature "product";
      uses aaa-config;
    }
  }

  augment "/ntos:state/ntos-system:system" {
    description
      "Authentication, Authorization and Accounting state.";

    container aaa {
      description
        "Operational state data for aaa servers.";
      ntos-ext:feature "product";
      uses aaa-config;
    }
  }
}
