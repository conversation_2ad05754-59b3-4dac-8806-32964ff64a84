module ntos-app-parse-mgmt {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:app-parse-mgmt";
  prefix ntos-app-parse-mgmt;

  import ntos {
    prefix ntos;
  }

  import ntos-extensions {
    prefix ntos-ext;
  }

  import ntos-types {
    prefix ntos-types;
  }

  import ntos-commands {
    prefix ntos-cmd;
  }

  import ietf-yang-types {
    prefix ietf-yang;
  }

  organization
    "Ruijie Networks Co., Ltd";
  contact
    "Tel: 4008-111-000
    E-mail: <EMAIL>";
  description
    "Ruijie NTOS app-parse-mgmt module.";

  revision 2024-03-14 {
    description
      "Add by-policy option in overload action.";
    reference
      "";
  }

  revision 2023-11-20 {
    description
      "Create initial version.";
    reference
      "";
  }

  identity app-parse-mgmt {
    base ntos-types:SERVICE_LOG_ID;
    description
      "Application parse management service.";
  }

  grouping http-parse-mgmt-group {
    description
      "The grouping of HTTP parse management.";

    leaf decompress-length {
      description
        "Set HTTP decompression length.";
      type uint32;
      default 2048;
    }
  }

  grouping app-parse-mgmt-group {
    description
      "The grouping of application parse management.";

    container app-parse-mgmt {
      description
        "Application parse management configuration.";

      container overload-protection {
        description
          "Set overload protection.";

        leaf enabled {
          type boolean;
          default "false";
          description
            "Enable or disable the overload protection.";
        }

        leaf action {
          description
            "Set action of traffic handling when overload.";
          type enumeration {
            enum bypass {
              description
                "Set bypass action.";
            }
            enum block {
              description
                "Set block action.";
            }
            enum by-policy {
              description
                "Set self-adaption action by policy.";
            }
          }
          default bypass;
        }

        leaf policy {
          type ntos-types:ntos-obj-name-type;
          must "../action = 'by-policy'";
          description
            "Set self-adaption policy.";
          ntos-ext:nc-cli-completion-xpath
            "../../policy/*[local-name()='name']";
        }
      }

      list policy {
        key "name";
        description
          "Self-adaption policy configuration.";

        leaf name {
          type ntos-types:ntos-obj-name-type;
          description
            "The name of self-adaption policy.";
        }

        leaf description {
          type ntos-types:ntos-obj-description-type;
          description
            "The description of self-adaption policy.";
        }

        leaf reputation-bypass {
          type boolean;
          default "false";
          description
            "Enable or disable bypass based on application reputation.";
        }

        container cpu-threshold {
          description
            "Set threshold of dataplane cpu.";

          ntos-ext:nc-cli-one-liner;
          leaf high {
            type uint8 {
              range "1..99";
            }
            default 90;
            description
              "Set high threshold.";
          }

          leaf low {
            type uint8 {
              range "1..99";
            }
            must "../high and ../high >= ../low" {
              error-message "High threshold must greater than low threshold.";
            }
            default 80;
            description
              "Set low threshold.";
          }
        }

        list priority {
          key "module";
          description
            "The priority of security modules.";
          ordered-by user;

          leaf module {
            description
              "The identity of security module.";

            type enumeration {
              enum ips {
                description
                  "Set intrusion preversion system priority.";
              }
              enum ti {
                description
                  "Set threat intelligence priority.";
              }
              enum av {
                description
                  "Set anti-virus priority.";
              }
            }
          }

          leaf enabled {
            type boolean;
            default "true";
            description
              "Enable or disable this module in overload protection.";
          }
        }
      }

      container http {
        description
          "HTTP parse management configuration.";

        uses http-parse-mgmt-group;
      }
    }
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "Configuration of application parse management.";
    uses app-parse-mgmt-group;
  }

  augment "/ntos:state/ntos:vrf" {
    description
      "State of application parse management.";
    uses app-parse-mgmt-group;
  }

  rpc show-app-parse-mgmt-info {
    description
      "Show application parse management configuration.";

    input {
      leaf vrf {
        type ntos:vrf-name;
        default "main";
        description
          "Vrf name.";
      }
    }

    output {
      container overload-protection {
        description
          "Show overload protection configuration.";

        leaf enabled {
          type boolean;
          description
            "Enabled of overload protection.";
        }

        leaf action {
          type enumeration {
            enum bypass {
              description
                "Show bypass action.";
            }
            enum block {
              description
                "Show block action.";
            }
            enum by-policy {
              description
                "Show self-adaption action by policy.";
            }
          }
          description
            "Action of traffic handling when overload.";
        }

        leaf policy {
          type ntos-types:ntos-obj-name-type;
          description
            "Show self-adaption policy.";
        }
      }

      list policy {
        key "name";
        description
          "Self-adaption policy configuration.";

        leaf name {
          type ntos-types:ntos-obj-name-type;
          description
            "The name of self-adaption policy.";
        }

        leaf description {
          type ntos-types:ntos-obj-description-type;
          description
            "The description of self-adaption policy.";
        }

        leaf reputation-bypass {
          type boolean;
          description
            "Enable or disable bypass based on application reputation.";
        }

        container cpu-threshold {
          description
            "Set threshold of dataplane cpu.";

          leaf high {
            type uint8 {
              range "1..99";
            }
            description
              "Set high threshold.";
          }

          leaf low {
            type uint8 {
              range "1..99";
            }
            description
              "Set low threshold.";
          }
        }

        list priority {
          key "module";
          description
            "Show priority of security modules.";

          leaf module {
            description
              "Show security module.";

            type enumeration {
              enum ips {
                description
                  "Set intrusion preversion system priority.";
              }
              enum ti {
                description
                  "Set threat intelligence priority.";
              }
              enum av {
                description
                  "Set anti-virus priority.";
              }
            }
          }

          leaf enabled {
            type boolean;
            description
              "Enable or disable this module in overload protection.";
          }
        }
      }

      container http {
        description
          "Show HTTP parse management configuration.";

        leaf decompress-length {
          type uint32;
          description
            "HTTP decompression length.";
        }
      }
      uses ntos-cmd:long-cmd-output;
    }

    ntos-ext:nc-cli-show "app-parse-mgmt";
  }

  rpc show-overload-protection-status {
    description
      "Show system overload protection status.";

    input {
      leaf vrf {
        type ntos:vrf-name;
        default "main";
        description
          "Vrf name.";
      }
    }

    output {
      leaf action {
        description
          "Show action of traffic handling when overload.";
        type enumeration {
          enum bypass {
            description
              "Show bypass action.";
          }
          enum block {
            description
              "Show block action.";
          }
          enum by-policy {
            description
              "Show self-adaption action by policy.";
          }
        }
      }

      leaf status {
        description
          "Show status of this action.";
        type enumeration {
          enum normal {
            description
              "Check all flows.";
          }
          enum bypass {
            description
              "Bypass all flows.";
          }
          enum block {
            description
              "Block All flows.";
          }
          enum disabled {
            description
              "Function is disabled.";
          }
        }
      }

      leaf policy {
        type ntos-types:ntos-obj-name-type;
        description
          "Show self-adaption policy name.";
      }

      leaf reputation-bypass {
        type boolean;
        description
          "Enabled of application reputation bypass.";
      }

      list module-status {
        key "module";
        description
          "Show status of security module.";
        leaf module {
          description
            "Show priority of security module.";

          type enumeration {
            enum ips {
              description
                "Intrusion preversion system status.";
            }
            enum ti {
              description
                "Threat intelligence status.";
            }
            enum av {
              description
                "Anti-virus status.";
            }
          }
        }

        leaf status {
          description
            "Show status of this module.";
          type enumeration {
            enum normal {
              description
                "This module is normal.";
            }
            enum bypass {
              description
                "This module has been bypass.";
            }
            enum disabled {
              description
                "This module is disabled.";
            }
          }
        }

        leaf bypass-time {
          description
            "The time when the module entered bypass.";
          type string {
            length "0..32";
          }
          must "../status = 'bypass'";
        }

        leaf bypass-flows {
          description
            "Number of flows bypassed.";
          type uint32;
          must "../status = 'bypass'";
        }
      }
      uses ntos-cmd:long-cmd-output;
    }
    ntos-ext:nc-cli-show "app-parse-mgmt overload-protection-status";
  }
}
