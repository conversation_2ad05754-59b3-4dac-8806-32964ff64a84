module ntos-bgp-rpki {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:rpki";
  prefix ntos-bgp-rpki;

  import ntos {
    prefix ntos;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-bgp {
    prefix ntos-bgp;
  }
  import ntos-commands {
    prefix ntos-cmd;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos-routing {
    prefix ntos-rt;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "RUIJIE NTOS routing RPKI.";

  revision 2020-03-19 {
    description
      "Initial version.";
    reference "";
  }

  grouping rpki-cache-server-conf {
    description
      "Cache server configuration.";

    leaf preference {
      type uint8 {
        range "1..255";
      }
      description
        "Preference of the cache server.";
    }

    leaf address {
      type union {
        type ntos-inet:ipv4-address;
        type ntos-inet:domain-name {
          ntos-ext:nc-cli-shortdesc "<fqdn>";
        }
      }
      mandatory true;
      description
        "IP address or hostname of the cache server.";
    }

    container ssh {
      presence "Configure SSH cache server.";
      description
        "Use SSH protocol for this cache server.";

      leaf port {
        type uint16 {
          range "1..65535";
        }
        mandatory true;
        description
          "Port number.";
      }

      leaf user-name {
        type string;
        mandatory true;
        description
          "SSH user name.";
      }

      leaf key {
        type string;
        mandatory true;
        description
          "SSH key pair name.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos-routing:routing/ntos-bgp:bgp/ntos-bgp-rpki:rpki-ssh-key";
      }
    }

    container tcp {
      presence "Configure TCP cache server.";
      description
        "Use TCP protocol for this cache server.";

      leaf port {
        type uint16 {
          range "1..65535";
        }
        mandatory true;
        description
          "Port number.";
      }
    }
  }

  grouping rpki-timer-conf {
    description
      "Timers configuration.";

    leaf expire-interval {
      type uint32 {
        range "600..172800";
      }
      default "7200";
      description
        "Set expire interval in seconds.";
    }

    leaf polling-period {
      type uint32 {
        range "1..86400";
      }
      default "3600";
      description
        "Set polling period in seconds.";
    }

    leaf retry-interval {
      type uint16 {
        range "1..7200";
      }
      default "600";
      description
        "Set retry interval in seconds.";
    }
  }

  grouping rpki-conf {
    description
      "BGP RPKI configuration.";

    leaf enabled {
      type boolean;
      default "true";
      description
        "Enable/Disable BGP RPKI configuration.";
    }

    list cache-server {
      must '(ssh or tcp) and not(ssh and tcp)' {
        error-message "SSH or TCP protocol must be configured but not both.";
      }
      key "preference";
      description
        "Configure a cache server.";
      ntos-ext:nc-cli-one-liner;
      uses rpki-cache-server-conf;
    }
    uses rpki-timer-conf;
  }

  grouping rpki-state {
    description
      "BGP RPKI state.";

    leaf enabled {
      type boolean;
      default "true";
      description
        "BGP RPKI status.";
    }

    list cache-server {
      key "preference";
      description
        "Cache server operational state.";
      ntos-ext:nc-cli-one-liner;

      leaf status {
        type enumeration {
          enum up {
            description
              "The cache server is up.";
          }
          enum down {
            description
              "The cache server is down.";
          }
        }
        description
          "Cache server status.";
      }
      uses rpki-cache-server-conf;
    }
    uses rpki-timer-conf;
  }

  grouping rpki-rmap {
    description
      "RPKI route-map configuration and state.";

    leaf rpki {
      type enumeration {
        enum valid {
          description
            "Valid prefix.";
        }
        enum invalid {
          description
            "Invalid prefix.";
        }
        enum notfound {
          description
            "Prerfix not found.";
        }
      }
      description
        "RPKI specific settings.";
    }
  }

  rpc show-bgp-rpki-cache-connection {
    description
      "Show which RPKI cache servers have a connection.";
    input {

      leaf vrf {
        type ntos:vrf-name;
        description
          "Specify the VRF.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-ext:feature "product";
    ntos-ext:nc-cli-show "bgp rpki cache-connection";
    ntos-api:internal;
  }

  rpc show-bgp-rpki-cache-server {
    description
      "Show RPKI configured cache server.";
    input {

      leaf vrf {
        type ntos:vrf-name;
        description
          "Specify the VRF.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-ext:feature "product";
    ntos-ext:nc-cli-show "bgp rpki cache-server";
    ntos-api:internal;
  }

  rpc show-bgp-rpki-prefix-table {
    description
      "Show validated prefixes which were received from RPKI Cache.";
    input {

      leaf vrf {
        type ntos:vrf-name;
        description
          "Specify the VRF.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf prefix {
        type ntos-inet:ip-prefix;
        description
          "Lookup by IPv4/IPv6 prefix.";
        ntos-ext:nc-cli-no-name;
      }

      leaf as {
        type ntos-inet:as-number {
          range "1..4294967295";
        }
        description
          "Lookup by AS number.";
      }
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-ext:feature "product";
    ntos-ext:nc-cli-show "bgp rpki prefix-table";
    ntos-api:internal;
  }

  rpc bgp-ssh-key-create {
    description
      "Create SSH keys.";
    input {

      leaf type {
        type enumeration {
          enum rsa-1024 {
            description
              "RSA in 1024 bits.";
          }
          enum rsa-2048 {
            description
              "RSA in 2048 bits.";
          }
          enum rsa-4096 {
            description
              "RSA in 4096 bits.";
          }
          enum ecdsa-256 {
            description
              "ECDSA in 256 bits.";
          }
          enum ecdsa-384 {
            description
              "ECDSA in 384 bits.";
          }
          enum ecdsa-521 {
            description
              "ECDSA in 521 bits.";
          }
          enum ed25519 {
            description
              "EDDSA in 25519 bits.";
          }
        }
        mandatory true;
        description
          "SSH key type.";
      }

      leaf name {
        type string;
        mandatory true;
        description
          "Name of the new key pair.";
      }
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-ext:feature "product";
    ntos-ext:nc-cli-cmd "bgp rpki ssh-key create";
    ntos-api:internal;
  }

  rpc bgp-ssh-key-delete {
    description
      "Delete SSH keys.";
    input {

      leaf name {
        type string;
        mandatory true;
        description
          "Delete an existing key pair.";
        ntos-ext:nc-cli-no-name;
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos-routing:routing/ntos-bgp:bgp/ntos-bgp-rpki:rpki-ssh-key";
      }
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-ext:feature "product";
    ntos-ext:nc-cli-cmd "bgp rpki ssh-key delete";
    ntos-api:internal;
  }

  rpc bgp-ssh-key-list {
    description
      "List SSH keys.";
    input {

      leaf detail {
        type empty;
        description
          "Show public key.";
      }
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-ext:feature "product";
    ntos-ext:nc-cli-cmd "bgp rpki ssh-key list";
    ntos-api:internal;
  }

  rpc bgp-ssh-host-add {
    description
      "Add host to routing known hosts.";
    input {

      leaf host {
        type ntos-inet:domain-name;
        mandatory true;
        description
          "Host name to add to known hosts.";
        ntos-ext:nc-cli-no-name;
      }

      leaf port {
        type ntos-inet:port-number;
        description
          "Use a specific port to join the remote host.";
      }

      leaf vrf {
        type ntos:vrf-name;
        description
          "Specify the VRF.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }
    }
    ntos-ext:feature "product";
    ntos-ext:nc-cli-cmd "bgp rpki ssh-host add";
    ntos-api:internal;
  }

  rpc bgp-ssh-host-delete {
    description
      "Delete host from routing known hosts.";
    input {

      leaf host-name {
        type ntos-inet:domain-name;
        mandatory true;
        description
          "Host name to remove from known hosts.";
        ntos-ext:nc-cli-no-name;
      }
    }
    ntos-ext:feature "product";
    ntos-ext:nc-cli-cmd "bgp rpki ssh-host delete";
    ntos-api:internal;
  }

  augment "/ntos:config/ntos:vrf/ntos-rt:routing/ntos-bgp:bgp" {
    description
      "BGP RPKI configuration.";

    container rpki {
      must 'expire-interval >= polling-period' {
        error-message "Expiry interval must be polling period or larger";
      }
      presence "Make BGP RPKI available";
      description
        "BGP RPKI configuration.";
      uses rpki-conf;
    }
  }

  augment "/ntos:state/ntos:vrf/ntos-rt:routing/ntos-bgp:bgp" {
    description
      "Operational BGP RPKI state.";

    container rpki {
      presence "Make BGP RPKI available";
      description
        "Operational BGP RPKI state.";
      uses rpki-state;
    }
  }

  augment "/ntos:state/ntos-rt:routing/ntos-bgp:bgp" {
    description
      "BGP RPKI available key pairs for SSH cache server.";

    leaf-list rpki-ssh-key {
      type string;
      description
        "Available SSH key pairs.";
    }
  }

  augment "/ntos:config/ntos-rt:routing/ntos-rt:route-map/ntos-rt:seq/ntos-rt:match" {
    description
      "BGP RPKI route-map configuration.";
    uses rpki-rmap;
  }

  augment "/ntos:state/ntos-rt:routing/ntos-rt:route-map/ntos-rt:seq/ntos-rt:match" {
    description
      "BGP RPKI route-map operational state.";
    uses rpki-rmap;
  }

  augment "/ntos:config/ntos-rt:routing/ntos-rt:logging/ntos-bgp:bgp" {
    description
      "Common BGP RPKI logging configuration.";

    leaf rpki {
      type boolean;
      default "false";
      description
        "Enable RPKI logging.";
    }
  }

  augment "/ntos:state/ntos-rt:routing/ntos-rt:logging/ntos-bgp:bgp" {
    description
      "Common BGP RPKI logging operational state.";

    leaf rpki {
      type boolean;
      default "false";
      description
        "RPKI logging state.";
    }
  }
}
