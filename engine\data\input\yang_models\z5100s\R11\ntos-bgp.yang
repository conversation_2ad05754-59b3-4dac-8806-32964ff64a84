module ntos-bgp {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:bgp";
  prefix ntos-bgp;

  import ntos {
    prefix ntos;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-commands {
    prefix ntos-cmd;
  }
  import ntos-extensions {
    prefix ntos-extensions;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos-interface {
    prefix ntos-iface;
  }
  import ntos-routing {
    prefix ntos-rt;
  }
  import ntos-routing-types {
    prefix ntos-rt-types;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-tracker {
    prefix ntos-tracker;
  }
  import ntos-vxlan {
    prefix ntos-vxlan;
  }
  import extra-conditions {
    prefix ext-cond;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "RUIJIE NTOS routing BGP.";

  revision 2021-02-15 {
    description
      "Extend community-list and community-list-expanded typedef blocks.";
    reference "";
  }
  revision 2020-12-07 {
    description
      "Fix the naming issue with (ext)community lists.";
    reference "";
  }
  revision 2020-12-04 {
    description
      "Add route-distinguisher leaf to evpn container.";
    reference "";
  }
  revision 2020-11-30 {
    description
      "Add show bgp afi safi neighbor command.";
    reference "";
  }
  revision 2020-11-27 {
    description
      "Add sender-as-path-loop-detection leaf to neighbor list.
       Add reject-as-sets leaf to bgp container.";
    reference "";
  }
  revision 2020-11-26 {
    description
      "Add route-map leaf to aggregate-address list present in some
       address-families containers.";
    reference "";
  }
  revision 2020-11-12 {
    description
      "Obsolete ipv4-community-list, ipv4-extcommunity-list and
       ipv4-as-path-access-list are now removed. Deprecated route-target in
       ipv6-unicast family is now obsolete.";
    reference "revision 2020-11-12";
  }
  revision 2020-10-08 {
    description
      "Deprecate dampening container and leaf. Add route-flap-dampening
       container that could be used by certain address-families containers.";
    reference "";
  }
  revision 2020-09-22 {
    description
      "Add path leaf to all address-families containers.";
    reference "";
  }

  revision 2020-06-26 {
    description
      "Add some leafs in address-families containers.";
    reference "";
  }
  revision 2020-01-16 {
    description
      "Move show evpn RPC in another yang model.";
    reference "";
  }
  revision 2020-01-09 {
    description
      "Add L3VPN support.";
    reference "";
  }
  revision 2019-12-18 {
    description
      "Add L2VPN support.";
    reference "";
  }
  revision 2019-11-29 {
    description
      "Add ipv6 flowspec.";
    reference "";
  }
  revision 2019-11-28 {
    description
      "Add ipv6-route-target.";
    reference "";
  }
  revision 2019-06-06 {
    description
      "Add logging configuration.";
    reference "";
  }
  revision 2019-02-06 {
    description
      "Add flush commands completion.";
    reference "";
  }
  revision 2019-01-30 {
    description
      "Add flush commands.";
    reference "";
  }
  revision 2019-01-23 {
    description
      "Add L3VPN support.";
    reference "";
  }
  revision 2018-11-29 {
    description
      "Rework show bgp.";
    reference "";
  }
  revision 2018-10-03 {
    description
      "Initial version.";
    reference "";
  }

  identity bgp {
    base ntos-types:SERVICE_LOG_ID;
    description
      "Routing BGP protocol.";
    ntos-extensions:nc-cli-identity-name "routing bgp";
  }

  identity route-bgp {
    base ntos-types:ROUTE4_FRR_ID;
    base ntos-types:ROUTE6_FRR_ID;
    description
      "BGP routes.";
    ntos-extensions:nc-cli-identity-name "bgp";
  }

  typedef route-distinguisher {
    type union {
      type string {
        // Type 1: 2-octet global and 4-octet local
        //         (AS number)        (Integer)
        pattern '(6553[0-5]|655[0-2][0-9]|654[0-9]{2}|65[0-4][0-9]{2}|' +
                '6[0-4][0-9]{3}|[1-5][0-9]{4}|[1-9][0-9]{1,3}|' +
                '[0-9]):(*********[0-5]|********[0-8][0-9]|4294967[0-1][0-9]{2}|' +
                '429496[0-6][0-9]{3}|42949[0-5][0-9]{4}|4294[0-8][0-9]{5}|' +
                '429[0-3][0-9]{6}|4[0-1][0-9]{7}|[1-3][0-9]{9}|[1-9][0-9]{1,8}|' +
                '[0-9])';
      }
      type string {
        // Type 2: 4-octet global and 2-octet local
        //         (ipv4-address)     (integer)
        pattern '(([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\.){3}([0-9]|' +
                '[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5]):(6553[0-5]|' +
                '655[0-2][0-9]|654[0-9]{2}|65[0-4][0-9]{2}|6[0-4][0-9]{3}|' +
                '[1-5][0-9]{4}|[1-9][0-9]{1,3}|[0-9])';
      }
    }
    description
      "Type definition for extended community attributes. In the case that
       common communities are utilised, they are represented as a string
       of the form:
        - <2b AS>:<4b value> per RFC4360 section 3.1
        - <4b IPv4>:<2b value> per RFC4360 section 3.2.";
    reference "RFC 4360 - BGP Extended Communities Attribute";
  }

  typedef route-target {
    type union {
      type string {
        // Type 1: 2-octet global and 4-octet local
        //         (AS number)        (Integer)
        pattern '(6553[0-5]|655[0-2][0-9]|654[0-9]{2}|65[0-4][0-9]{2}|' +
                '6[0-4][0-9]{3}|[1-5][0-9]{4}|[1-9][0-9]{1,3}|' +
                '[0-9]):(*********[0-5]|********[0-8][0-9]|4294967[0-1][0-9]{2}|' +
                '429496[0-6][0-9]{3}|42949[0-5][0-9]{4}|4294[0-8][0-9]{5}|' +
                '429[0-3][0-9]{6}|4[0-1][0-9]{7}|[1-3][0-9]{9}|[1-9][0-9]{1,8}|' +
                '[0-9])';
      }
      type string {
        // Type 2: 4-octet global and 2-octet local
        //         (ipv4-address)     (integer)
        pattern '(([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\.){3}([0-9]|' +
                '[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5]):(6553[0-5]|' +
                '655[0-2][0-9]|654[0-9]{2}|65[0-4][0-9]{2}|6[0-4][0-9]{3}|' +
                '[1-5][0-9]{4}|[1-9][0-9]{1,3}|[0-9])';
      }
      type string {
        // RFC5668: 4-octet global and 2-octet local
        //            (AS number)        (integer)
        pattern '(*********[0-5]|********[0-8][0-9]|4294967[0-1][0-9]{2}|' +
                '429496[0-6][0-9]{3}|42949[0-5][0-9]{4}|4294[0-8][0-9]{5}|' +
                '429[0-3][0-9]{6}|4[0-1][0-9]{7}|[1-3][0-9]{9}|[1-9][0-9]{1,8}|' +
                '[0-9]):(6553[0-5]|655[0-2][0-9]|654[0-9]{2}|65[0-4][0-9]{2}|' +
                '6[0-4][0-9]{3}|[1-5][0-9]{4}|[1-9][0-9]{1,3}|[0-9])';
      }
    }
    description
      "Extended communities or route-target attribute.";
    reference
      "RFC 4360 - BGP Extended Communities Attribute.
       RFC 5668 - 4-Octet AS Specific BGP Extended Community
       draft-ietf-idr-segment-routing-te-policy";
  }

  typedef ipv6-route-target {
    type string {
      pattern
          '((:|[0-9a-fA-F]{0,4}):)([0-9a-fA-F]{0,4}:){0,5}' +
          '((([0-9a-fA-F]{0,4}:)?(:|[0-9a-fA-F]{0,4}))|' +
          '(((25[0-5]|2[0-4][0-9]|[01]?[0-9]?[0-9])\.){3}' +
          '(25[0-5]|2[0-4][0-9]|[01]?[0-9]?[0-9])))' +
          ':' +
          '(6553[0-5]|655[0-2][0-9]|65[0-4][0-9]{2}|' +
          '6[0-4][0-9]{3}|' +
          '[0-5]?[0-9]{0,3}[0-9])';
      pattern '((([^:]+:){6}(([^:]+:[^:]+)|(.*\..*)))|' +
          '((([^:]+:)*[^:]+)?::(([^:]+:)*[^:]+)?))' +
          ':' +
          '(6553[0-5]|655[0-2][0-9]|65[0-4][0-9]{2}|' +
          '6[0-4][0-9]{3}|' +
          '[0-5]?[0-9]{0,3}[0-9])';
    }
    description
      "An IPv6 route target is a 20-octet BGP IPv6 address
       specific extended community serving the same function
       as a standard 8-octet route target only allowing for
       an IPv6 address as the global administrator. The format
       is <ipv6-address:2-octet-number>.

       Some valid examples are: 2001:DB8::1:6544 and
       2001:DB8::5eb1:791:6b37:17958.";
    reference
      "RFC5701: IPv6 Address Specific BGP Extended Community
                Attribute";
  }

  typedef community-list {
    type union {
      type enumeration {
        enum local-AS {
          description
            "Local AS.";
        }
        enum no-advertise {
          description
            "Do not advertise.";
        }
        enum no-export {
          description
            "Do not export.";
        }
        enum internet {
          description
            "Internet.";
        }
        enum additive {
          description
            "Additive.";
        }
        enum graceful-shutdown {
          description
            "Graceful-shutdown.";
        }
        enum accept-own {
          description
            "Accept-own.";
        }
        enum route-filter-translated-v4 {
          description
            "Route-filter-translated-v4.";
        }
        enum route-filter-v4 {
          description
            "Route-filter-v4.";
        }
        enum route-filter-translated-v6 {
          description
            "Route-filter-translated-v6.";
        }
        enum route-filter-v6 {
          description
            "Route-filter-v6.";
        }
        enum llgr-stale {
          description
            "Llgr-stale.";
        }
        enum no-llgr {
          description
            "No-llgr.";
        }
        enum accept-own-nexthop {
          description
            "Accept-own-nexthop.";
        }
        enum blackhole {
          description
            "Blackhole.";
        }
        enum no-peer {
          description
            "No-peer.";
        }
      }
      // 4-octet value:
      //  <as number> 2 octets
      //  <community value> 2 octets
      type string {
        pattern '(6553[0-5]|655[0-2][0-9]|65[0-4][0-9]{2}|6[0-4][0-9]{3}|' +
                '[1-5][0-9]{4}|[1-9][0-9]{1,3}|[0-9]):(6553[0-5]|655[0-2][0-9]|' +
                '65[0-4][0-9]{2}|6[0-4][0-9]{3}|[1-5][0-9]{4}|[1-9][0-9]{1,3}|' +
                '[0-9])';
      }
    }
    description
      "Community attribute.";
    reference "RFC 1997 - BGP Communities Attribute";
  }

  typedef community-list-expanded {
    type union {
      type enumeration {
        enum local-AS {
          description
            "Local AS.";
        }
        enum no-advertise {
          description
            "Do not advertise.";
        }
        enum no-export {
          description
            "Do not export.";
        }
        enum internet {
          description
            "Internet.";
        }
        enum additive {
          description
            "Additive.";
        }
        enum graceful-shutdown {
          description
            "Graceful-shutdown.";
        }
        enum accept-own {
          description
            "Accept-own.";
        }
        enum route-filter-translated-v4 {
          description
            "Route-filter-translated-v4.";
        }
        enum route-filter-v4 {
          description
            "Route-filter-v4.";
        }
        enum route-filter-translated-v6 {
          description
            "Route-filter-translated-v6.";
        }
        enum route-filter-v6 {
          description
            "Route-filter-v6.";
        }
        enum llgr-stale {
          description
            "Llgr-stale.";
        }
        enum no-llgr {
          description
            "No-llgr.";
        }
        enum accept-own-nexthop {
          description
            "Accept-own-nexthop.";
        }
        enum blackhole {
          description
            "Blackhole.";
        }
        enum no-peer {
          description
            "No-peer.";
        }
      }
      // community attribute string with regular expression
      type string;
    }
    description
      "Community attribute.";
    reference "RFC 1997 - BGP Communities Attribute";
  }

  // --- BGP global configuration -- \\

  grouping route-flap-dampening {
    description
      "Dampening common configuration.";

    container route-flap-dampening {
      presence "enable dampening";
      description
        "Enable route-flap dampening.";

      leaf enabled {
        type boolean;
        default "false";
        description
          "Enable route flap dampening.";
      }

      leaf reach-decay {
        type uint8 {
          range "1..45";
        }
        units "seconds";
        default "15";
        description
          "This value specifies the time desired for the instability
           metric value to reach one-half of its current value when
           the route is reachable. This half-life value determines
           the rate at which the metric value is decayed. A smaller
           half-life value makes a suppressed route reusable sooner
           than a larger value. The accumulated penalty will be reduced
           to half after this duration.";
      }

      leaf reuse-above {
        type uint16 {
          range "1..20000";
        }
        default "750";
        description
          "This is the value of the instability metric at which a
           suppressed route becomes unsuppressed if it is reachable
           but currently suppressed. The value assigned to
           reuse-below must be less than suppress-above.";
      }

      leaf suppress-above {
        type uint16 {
          range "1..20000";
        }
        default "2000";
        description
          "This is the value of the instability metric at which
           route suppression takes place. A route is not installed
           in the forwarding information base (FIB), or announced
           even if it is reachable during the period that it is
           suppressed.";
      }

      leaf unreach-decay {
        type uint8 {
          range "1..255";
        }
        units "seconds";
        default "60";
        description
          "This value acts the same as reach-decay except that it
           specifies the rate at which the instability metric is
           decayed when a route is unreachable. It should have a
           value greater than or equal to reach-decay.";
      }
    }
  }

  grouping global-distance-common-conf {
    description
      "Distance common configuration.";

    leaf distance {
      type uint8 {
        range "1..255";
      }
      mandatory true;
      description
        "Administrative distance.";
    }
    // Augmented with access-list leaf.
  }

  grouping global-bgp-distance-conf {
    description
      "Configure BGP distance.";

    container bgp-distance {
      description
        "Configure BGP distance.";

      leaf external-routes {
        type uint8 {
          range "1..255";
        }
        default "20";
        description
          "Distance for routes external to the AS.";
      }

      leaf internal-routes {
        type uint8 {
          range "1..255";
        }
        default "200";
        description
          "Distance for routes internal to the AS.";
      }

      leaf local-routes {
        type uint8 {
          range "1..255";
        }
        default "200";
        description
          "Distance for local routes.";
      }
    }
  }

  grouping global-network-af-ipv4-conf {
    description
      "Network address family IPv4 configuration.";

    leaf ip-prefix {
      type ntos-inet:ipv4-prefix;
      description
        "IPv4 prefix.";
    }

    leaf backdoor {
      type boolean;
      default "false";
      description
        "If true, specify a BGP backdoor route.";
    }

    leaf label-index {
      type uint32 {
        range "1..1048560";
      }
      description
        "Label index to associate with the prefix.";
    }
  }

  grouping global-common-af-ipv4-conf {
    description
      "Common global configuration options for IPv4 unicast and multicast
       address families.";

    list aggregate-address {
      key "ip-prefix";
      description
        "Configure BGP aggregate entries.";
      ntos-extensions:nc-cli-one-liner;

      leaf ip-prefix {
        type ntos-inet:ipv4-prefix;
        description
          "Aggregate prefix.";
      }

      leaf as-set {
        type boolean;
        default "false";
        description
          "If true, generate AS set path information.";
      }

      leaf summary-only {
        type boolean;
        default "false";
        description
          "If true, filter more specific routes from updates.";
      }

      leaf route-map {
        type ntos-rt-types:route-map-name;
        description
          "Apply route-map to aggregate network.";
      }
    }

    list administrative-distance {
      key "ip-prefix";
      description
        "Define administrative distance.";
      ntos-extensions:nc-cli-one-liner;

      leaf ip-prefix {
        type ntos-inet:ipv4-prefix;
        description
          "IP source prefix.";
      }
      uses global-distance-common-conf;
    }
    uses global-bgp-distance-conf;

    leaf dampening {
      type boolean;
      must "current() = 'false' or ../../../dampening" {
        error-message "BGP dampening must be configured in BGP root level.";
      }
      status deprecated {
        ntos-extensions:status-obsoleted-release "21q1";
        ntos-extensions:status-deprecated-revision "2020-10-08";
        ntos-extensions:status-description
          "Dampening leaf is not used anymore. Instead, route-flap-dampening
           container is used by certain address-family containers to suppress
           the flapping routes.";
      }
      description
        "Enable/Disable route-flap dampening in this address family.";
    }
  }

  grouping global-maximum-paths-conf {
    description
      "Forward packets over multiple paths.";

    container maximum-path {
      description
        "Forward packets over multiple paths.";

      leaf ebgp {
        type uint8 {
          range "1..16";
        }
        default "16";
        description
          "Ebgp number of paths.";
      }

      leaf ibgp {
        type uint8 {
          range "1..16";
        }
        default "16";
        description
          "Ibgp number of paths.";
      }

      leaf equal-cluster-length {
        type boolean;
        default "false";
        description
          "If true, match the cluster length.";
      }
    }
  }

  grouping global-common-redistribute-conf {
    description
      "Common option of redistribute commands.";

    leaf metric {
      type uint32;
      description
        "Metric for redistributed routes.";
    }
    // Augmented with route-map leaf.
  }

  grouping global-common-af-ip-ucast-conf {
    description
      "Common global configuration options for IPv4/IPv6 unicast address
       families.";

    container l3vpn {
      must "/ntos:config/ntos:vrf[ntos:name='main']/ntos-rt:routing/ntos-bgp:bgp/ntos-bgp:as" {
        error-message "The BGP core instance (vrf main) must be configured.";
      }
      presence "Enables L3VPN BGP configuration.";
      description
        "Specify route-target and route-distinguisher between this address
         family and VPN.";

      container export {
        presence
          "Make available to specify route-target and route-distinguisher
           for routes leaked from this address-family to VPN.";
        description
          "For routes leaked from this address-family to VPN.";

        leaf vpn {
          type boolean;
          default "false";
          description
            "Export routes from this address-family to default instance VPN
             RIB.";
        }

        leaf label {
          type union {
            type uint32 {
              range "0..1048575";
            }
            type enumeration {
              enum auto {
                description
                  "Automatically assign a label.";
              }
            }
          }
          description
            "Label value (use auto to automatically assign a label).";
        }

        leaf-list route-target {
          type route-target;
          description
            "Specify route target list.";
        }

        leaf route-distinguisher {
          type route-distinguisher;
          description
            "Specify route distinguisher.";
        }

        leaf nexthop {
          type ntos-inet:ip-address;
          description
            "Specify next hop to use for VRF advertised prefixes between the
             current address-family and VPN.";
        }

        leaf route-map {
          type ntos-rt-types:route-map-name;
          description
            "Specify route map between the current address-family and VPN.";
        }
      }

      container import {
        presence
          "Make available to specify route-target for route leaked from VPN to
           this address-family.";
        description
          "For routes leaked from VPN to this address-family.";

        leaf vpn {
          type boolean;
          default "false";
          description
            "Import routes to this address-family from default instance VPN
             RIB.";
        }

        leaf-list route-target {
          type route-target;
          min-elements 1;
          description
            "Specify route target list.";
        }

        leaf route-map {
          type ntos-rt-types:route-map-name;
          description
            "Specify route map between the current address-family and VPN.";
        }
      }
    }
    uses global-maximum-paths-conf;
  }

  grouping global-network-af-ipv6-conf {
    description
      "Network address family IPv6 configuration.";

    leaf ip-prefix {
      type ntos-inet:ipv6-prefix;
      description
        "IPv6 prefix.";
    }

    leaf label-index {
      type uint32 {
        range "0..1048560";
      }
      description
        "Label index to associate with the prefix.";
    }
  }

  grouping global-rd-af-vpn-conf {
    description
      "Route distinguisher address family IPv4/IPv6 VPN configuration.";

    leaf rd {
      type route-distinguisher;
      description
        "VPN route distinguisher.";
    }

    leaf label {
      type uint32 {
        range "0..1048575";
      }
      description
        "VPN NLRI label.";
    }

    leaf route-map {
      type ntos-rt-types:route-map-name;
      description
        "Route map name.";
    }
  }

  grouping global-common-af-ipv6-conf {
    description
      "Common global configuration options for IPv6 unicast and multicast
       address families.";

    list administrative-distance {
      key "ip-prefix";
      description
        "Define administrative distance.";
      ntos-extensions:nc-cli-one-liner;

      leaf ip-prefix {
        type ntos-inet:ipv6-prefix;
        description
          "IP source prefix.";
      }
      uses global-distance-common-conf;
    }
  }

  grouping global-ipv4-unicast-conf {
    description
      "IPv4 unicast configuration.";

    leaf enabled {
      type boolean;
      default "true";
      description
        "Enable or disable IPv4 unicast addres family.";
    }

    list redistribute {
      key "protocol";
      description
        "Redistribute information from another routing protocol.";
      ntos-extensions:nc-cli-one-liner;

      leaf protocol {
        type enumeration {
          enum connected {
            description
              "Connected routes (directly attached subnet or host).";
          }
          enum kernel {
            description
              "Kernel routes (not installed via the zebra RIB).";
          }
          enum ospf {
            description
              "Open Shortest Path First (OSPFv2).";
          }
          enum rip {
            description
              "Routing Information Protocol (RIP).";
          }
          enum static {
            description
              "Statically configured routes.";
          }
          enum table {
            description
              "Non-main Kernel Routing Table.";
          }
        }
        description
          "Routing protocol.";
      }

      leaf id {
        when "../protocol = 'ospf' or ../protocol = 'table'" {
          description
            "Instance/table ID is available only for OSPF or table
             protocols.";
        }
        type uint32;
        description
          "Instance or table ID.";
      }
      uses global-common-redistribute-conf;
    }

    container route-target {
      presence "Enable route-target redirect import";
      description
        "Route target list.";

      leaf-list redirect-import {
        type route-target;
        description
          "Flow-spec redirect type route target, Import routes to this
           address-family.";
      }
    }
    uses global-common-af-ip-ucast-conf;
    uses global-common-af-ipv4-conf;
  }

  grouping global-ipv4-multicast-conf {
    description
      "IPv4 multicast configuration.";

    leaf enabled {
      type boolean;
      default "true";
      description
        "Enable or disable IPv4 multicast Address Family.";
    }
    uses global-common-af-ipv4-conf;
  }

  grouping global-ipv4-labeled-unicast-conf {
    description
      "IPv4 labeled unicast configuration.";

    leaf enabled {
      type boolean;
      default "true";
      description
        "Enable or disable IPv4 labeled unicast Address Family.";
    }
  }

  grouping global-flowspec-conf {
    description
      "Flowspec configuration.";

    leaf enabled {
      type boolean;
      default "true";
      description
        "Enable or disable Flowspec Address Family.";
    }

    leaf-list local-install {
      type ntos-types:ifname;
      description
        "Interface name.";
      ntos-extensions:nc-cli-completion-xpath
        "../../../../ntos-interface:interface/*/*[local-name()='name']";
    }
  }

  grouping global-ipv6-unicast-conf {
    description
      "IPv6 unicast configuration.";

    leaf enabled {
      type boolean;
      default "true";
      description
        "Enable or disable IPv6 unicast Address Family.";
    }

    list aggregate-address {
      key "ip-prefix";
      description
        "Configure BGP aggregate entries.";
      ntos-extensions:nc-cli-one-liner;

      leaf ip-prefix {
        type ntos-inet:ipv6-prefix;
        description
          "Aggregate prefix.";
      }

      leaf as-set {
        type boolean;
        default "false";
        description
          "If true, generate AS set path information.";
      }

      leaf summary-only {
        type boolean;
        default "false";
        description
          "If true, filter more specific routes from updates.";
      }

      leaf route-map {
        type ntos-rt-types:route-map-name;
        description
          "Apply route-map to aggregate network.";
      }
    }

    list redistribute {
      key "protocol";
      description
        "Redistribute information from another routing protocol.";
      ntos-extensions:nc-cli-one-liner;

      leaf protocol {
        type enumeration {
          enum connected {
            description
              "Connected routes (directly attached subnet or host).";
          }
          enum kernel {
            description
              "Kernel routes (not installed via the zebra RIB).";
          }
          enum ospf6 {
            description
              "Open Shortest Path First IPv6 (OSPFv3).";
          }
          enum ripng {
            description
              "Routing Information Protocol next-generation (IPv6) (RIPng).";
          }
          enum static {
            description
              "Statically configured routes.";
          }
          enum table {
            description
              "Non-main Kernel Routing Table.";
          }
        }
        description
          "Routing protocol.";
      }
      uses global-common-redistribute-conf;
    }

    container ipv6-route-target {
      presence "Enable route-target redirect import";
      description
        "Route target list.";

      leaf-list redirect-import {
        type ipv6-route-target;
        description
          "Flow-spec redirect ipv6 type route target, Import routes to this
           address-family.";
      }
    }

    container route-target {
      presence "Enable route-target redirect import";
      status obsolete {
        ntos-extensions:status-obsoleted-release "20q3";
        ntos-extensions:status-deprecated-revision "2019-11-28";
        ntos-extensions:status-description
          "route-target and ipv6-route-target are respective community lists for
           ipv4 and ipv6. For that, route-target is replaced by ipv6-route-target
           in ipv6 unicast address-family. The new 'ipv6-route-target' fixes this.";
        ntos-extensions:status-replacement "/ntos:config/ntos:vrf/ntos-routing:routing/ntos-bgp:bgp/ntos-bgp:address-family/ntos-bgp:ipv6-unicast/ntos-bgp:ipv6-route-target";
      }
      description
        "Route target list.";

      leaf-list redirect-import {
        type route-target;
        status obsolete;
        description
          "Flow-spec redirect type route target, Import routes to this
           address-family.";
      }
    }

    uses global-common-af-ipv6-conf;
    uses global-common-af-ip-ucast-conf;
    uses global-bgp-distance-conf;
  }

  grouping global-ipv6-multicast-conf {
    description
      "IPv6 multicast configuration.";

    leaf enabled {
      type boolean;
      default "true";
      description
        "Enable or disable IPv6 multicast Address Family.";
    }
    uses global-common-af-ipv6-conf;
    uses global-bgp-distance-conf;
  }

  grouping global-ipv6-labeled-unicast-conf {
    description
      "IPv6 labeled multicast configuration.";

    leaf enabled {
      type boolean;
      default "true";
      description
        "Enable or disable IPv6 labeled unicast Address Family.";
    }
    uses global-maximum-paths-conf;
  }

  grouping global-l2vpn-evpn-common-conf {
    description
      "Global L2VPN EVPN configuation common to root and vni contexts.";

    container export {
      presence "Make available to specify route-target.";
      description
        "Configure route-target to export.";

      leaf-list route-target {
        type route-target;
        max-elements 1;
        description
          "Specify the route target.";
      }

      leaf route-distinguisher {
        type route-distinguisher;
        description
          "Specify route distinguisher.";
      }
    }

    container import {
      presence "Make available to specify route-target.";
      description
        "Configure route-target to import.";

      leaf-list route-target {
        type route-target;
        max-elements 1;
        description
          "Specify the route target.";
      }
    }
  }

  grouping global-l2vpn-evpn-conf {
    description
      "L2VPN EVPN configuration.";

    leaf enabled {
      type boolean;
      default "true";
      description
        "Enable or disable L2VPN EVPN Address Family configuration.";
    }

    leaf-list advertise {
      type enumeration {
        enum ipv4-unicast {
          description
            "Advertise IPv4 unicast routes.";
        }
        enum ipv6-unicast {
          description
            "Advertise IPv6 unicast routes.";
        }
      }
      description
        "Configure advertisement.";
    }

    leaf advertise-all-vni {
      type boolean;
      default "false";
      description
        "Advertise All local VNIs.";
    }

    leaf auto-route-target {
      type enumeration {
        enum disabled {
          description
            "Do not auto-derivate route targets.";
        }
        enum rfc8365 {
          description
            "Auto-derivation of route targets using RFC8365.";
        }
      }
      default "disabled";
      description
        "Configure auto-derivation of route-target.";
    }

    leaf default-originate {
      type enumeration {
        enum ipv4 {
          description
            "IPv4 address family.";
        }
        enum ipv6 {
          description
            "IPv6 address family.";
        }
        enum both {
          description
            "IPv4 and IPv6 address families.";
        }
      }
      description
        "Originate a default route.";
    }

    leaf flooding {
      type enumeration {
        enum disabled {
          description
            "Do not flood any BUM packets.";
        }
        enum head-end-replication {
          description
            "Flood BUM packets using head-end replication.";
        }
      }
      default "head-end-replication";
      description
        "Specify handling for BUM packets.";
    }

    list vni {
      must "export/route-distinguisher and ../advertise-all-vni = 'true' or not(export/route-distinguisher)" {
        error-message "Route distinguisher can be set only if all VNI are advertised.";
      }
      must "import/route-target and ../advertise-all-vni = 'true' or not(import/route-target)" {
        error-message "Route target can be set only if all VNI are advertised.";
      }
      must "export/route-target and ../advertise-all-vni = 'true' or not(export/route-target)" {
        error-message "Route target can be set only if all VNI are advertised.";
      }
      must "enabled[text()='true'] and ../advertise-all-vni = 'true' or ../advertise-all-vni = 'false'" {
        error-message
          "Advertisement cannot be disabled on a specific VNI if all of them are
           advertised (advertise all-vni is set).";
      }
      key "vni";
      description
        "Configure L2VPN for a specific VXLAN Network Identifier.";

      leaf vni {
        type ntos-vxlan:vni;
        description
          "VXLAN Network Identifier.";
      }

      leaf enabled {
        type boolean;
        default "true";
        description
          "Enable/Disable advertisement for this VNI.";
      }

      leaf advertise-default-gw {
        type boolean;
        default "false";
        description
          "Advertise all default g/w mac-ip routes in EVPN.";
      }
      uses global-l2vpn-evpn-common-conf;
    }
    uses global-l2vpn-evpn-common-conf;
  }

  grouping global-address-family-conf {
    description
      "Address family configuration.";

    container address-family {
      description
        "Address-families associated with the BGP configuration.";

      container ipv4-unicast {
        presence "Configure IPv4 unicast address family global options";
        description
          "Configure IPv4 unicast address family.";

        list network {
          key "ip-prefix";
          description
            "Specify networks to announce via BGP.";

          leaf route-map {
            type ntos-rt-types:route-map-name;
            description
              "Route-map name.";
          }
          uses global-network-af-ipv4-conf;
        }

        leaf table-map {
          type ntos-rt-types:route-map-name;
          description
            "BGP table to RIB route download filter.";
        }
        uses global-ipv4-unicast-conf {

          augment "administrative-distance" {
            description
              "Add access-list option to administrative-distance list.";

            leaf access-list {
              type ntos-rt-types:v4-access-list-name;
              description
                "Access list name.";
            }
          }

          augment "redistribute" {
            description
              "Add route-map option to redistribute list.";

            leaf route-map {
              type ntos-rt-types:route-map-name;
              description
                "Route-map name.";
            }
          }
        }
        uses route-flap-dampening;
      }

      container ipv4-multicast {
        presence "Configure IPv4 multicast Address Family global options";
        description
          "Configure IPv4 multicast address family.";

        list network {
          key "ip-prefix";
          description
            "Specify networks to announce via BGP.";

          leaf route-map {
            type ntos-rt-types:route-map-name;
            description
              "Route-map name.";
          }
          uses global-network-af-ipv4-conf;
        }

        leaf table-map {
          type ntos-rt-types:route-map-name;
          description
            "BGP table to RIB route download filter.";
        }
        uses global-ipv4-multicast-conf {

          augment "administrative-distance" {
            description
              "Add access-list option to administrative-distance list.";

            leaf access-list {
              type ntos-rt-types:v4-access-list-name;
              description
                "Access list name.";
            }
          }
        }
        uses route-flap-dampening;
      }

      container ipv4-flowspec {
        presence "Configure IPv4 Flowspec Address Family global options";
        description
          "Configure IPv4 Flowspec address family.";
        uses global-flowspec-conf;
      }

      container ipv4-labeled-unicast {
        presence "Configure IPv4 labeled unicast Address Family global options";
        description
          "Configure IPv4 labeled unicast address family.";
        uses global-ipv4-labeled-unicast-conf;
        uses route-flap-dampening;
      }

      container ipv4-vpn {
        presence "Configure IPv4 VPN Address Family global options";
        description
          "Configure IPv4 VPN address family.";

        leaf enabled {
          type boolean;
          default "true";
          description
            "Enable or disable IPv4 VPN Address Family.";
        }

        list route-distinguisher {
          key "rd ip-prefix";
          description
            "Specify a network to announce via BGP.";

          leaf ip-prefix {
            type ntos-inet:ipv4-prefix;
            description
              "IPv4 prefix.";
          }
          uses global-rd-af-vpn-conf;
        }
      }

      container ipv6-unicast {
        presence "Configure IPv6 unicast Address Family global options";
        description
          "Configure IPv6 unicast address family.";

        list network {
          key "ip-prefix";
          description
            "Specify a network to announce via BGP.";

          leaf route-map {
            type ntos-rt-types:route-map-name;
            description
              "Route-map name.";
          }
          uses global-network-af-ipv6-conf;
        }

        leaf table-map {
          type ntos-rt-types:route-map-name;
          description
            "BGP table to RIB route download filter.";
        }
        uses global-ipv6-unicast-conf {

          augment "administrative-distance" {
            description
              "Add access-list option to administrative-distance list.";

            leaf access-list {
              type ntos-rt-types:v6-access-list-name;
              description
                "Access list name.";
            }
          }

          augment "redistribute" {
            description
              "Add route-map option to redistribute list.";

            leaf route-map {
              type ntos-rt-types:route-map-name;
              description
                "Route-map name.";
            }
          }
        }
        uses route-flap-dampening;
      }

      container ipv6-flowspec {
        presence "Configure IPv6 Flowspec Address Family global options";
        description
          "Configure IPv6 Flowspec address family.";
        uses global-flowspec-conf;
      }

      container ipv6-multicast {
        presence "Configure IPv6 multicast Address Family global options";
        description
          "Configure IPv6 multicast address family.";

        list network {
          key "ip-prefix";
          description
            "Specify a network to announce via BGP.";

          leaf route-map {
            type ntos-rt-types:route-map-name;
            description
              "Route-map name.";
          }
          uses global-network-af-ipv6-conf;
        }
        uses global-ipv6-multicast-conf {

          augment "administrative-distance" {
            description
              "Add access-list option to administrative-distance list.";

            leaf access-list {
              type ntos-rt-types:v6-access-list-name;
              description
                "Access list name.";
            }
          }
        }
        uses route-flap-dampening;
      }

      container ipv6-labeled-unicast {
        presence "Configure IPv6 labeled unicast Address Family global options";
        description
          "Configure IPv6 labeled unicast address family.";
        uses global-ipv6-labeled-unicast-conf;
        uses route-flap-dampening;
      }

      container ipv6-vpn {
        presence "Configure IPv6 VPN Address Family global options";
        description
          "Configure IPv6 VPN address family.";

        leaf enabled {
          type boolean;
          default "true";
          description
            "Enable or disable IPv6 VPN Address Family.";
        }

        list route-distinguisher {
          key "rd ip-prefix";
          description
            "Specify a network to announce via BGP.";

          leaf ip-prefix {
            type ntos-inet:ipv6-prefix;
            description
              "IPv6 prefix.";
          }
          uses global-rd-af-vpn-conf;
        }
      }

      container l2vpn-evpn {
        must
          "count(/ntos:config/ntos:vrf[ntos:name!='main']/ntos-rt:routing/bgp/address-family/l2vpn-evpn[
                   enabled='true' and advertise-all-vni='true']) = 0" {
          error-message "advertise all-vni can be set only in the main VRF.";
        }
        must "/ntos:config/ntos:vrf[ntos:name='main']/ntos-rt:routing/ntos-bgp:bgp/ntos-bgp:as" {
          error-message "The BGP core instance (vrf main) must be configured.";
        }
        presence "Configure L2VPN EVPN address family global options";
        description
          "Configure L2VPN EVPN address family.";
        uses global-l2vpn-evpn-conf;
        ntos-api:must-added
          "/ntos:config/ntos:vrf[ntos:name='main']/ntos-routing:routing/ntos-bgp:bgp/ntos-bgp:as";
      }
    }
  }

  grouping global-conf {
    description
      "Global BGP parameters.";

    leaf always-compare-med {
      type boolean;
      default "false";
      description
        "If true, allow comparing MED from different neighbors.";
    }

    container bestpath {
      description
        "Change the default bestpath selection.";

      container as-path {
        description
          "AS-path attribute.";

        leaf confederation {
          type boolean;
          default "false";
          description
            "If true, compare path lengths including confederation sets and
             sequences in selecting a route.";
        }

        leaf ignore {
          type boolean;
          default "false";
          description
            "If true, ignore as-path length in selecting a route.";
        }

        leaf multipath-relax {
          type enumeration {
            enum as-set {
              description
                "Generate an AS_SET.";
            }
            enum no-as-set {
              description
                "Do not generate an AS_SET.";
            }
          }
          description
            "Allow load sharing across routes that have different AS paths
             (but same length).";
        }
      }

      leaf compare-routerid {
        type boolean;
        default "false";
        description
          "If true, compare router-id for identical EBGP paths.";
      }

      leaf-list med {
        type enumeration {
          enum confederation {
            description
              "Compare MED among confederation paths.";
          }
          enum missing-as-worst {
            description
              "Treat missing MED as the least preferred one.";
          }
        }
        description
          "MED attribute.";
      }
    }

    container client-to-client {
      description
        "BGP client to client route reflection.";

      leaf reflection {
        type boolean;
        default "true";
        description
          "Enable or disable BGP client to client route reflection.";
      }
    }

    leaf cluster-id {
      type union {
        type ntos-inet:ipv4-address;
        type uint32 {
          range "1..**********";
        }
      }
      description
        "Configure Route-Reflector Cluster-id.";
    }

    leaf coalesce-time {
      type uint32 {
        range "1..**********";
        // 0 = disabled
      }
      description
        "Subgroup coalesce timer (in ms).";
    }

    container confederation {
      description
        "Parameters indicating whether the local system acts as part of a BGP
         confederation.";

      leaf identifier {
        type uint32 {
          range "1..**********";
        }
        description
          "Confederation AS number. Setting the AS indicates that the local-AS
           is part of a BGP confederation.";
      }

      leaf-list peers {
        type uint32 {
          range "1..**********";
        }
        must '../../as[text() != current()]' {
          error-message "The local AS is not allowed!";
        }
        description
          "Peer AS that are to be treated as part of the local
           confederation.";
      }
    }

    container dampening {
      presence "make route-flap dampening available";
      status deprecated {
        ntos-extensions:status-obsoleted-release "21q1";
        ntos-extensions:status-deprecated-revision "2020-10-08";
        ntos-extensions:status-description
          "Dampening container is used to suppress flapping routes. It will be
           replaced by route-flap-dampening that will do the same job, but for
           a specified address family.";
      }
      description
        "Enable route-flap dampening.";

      leaf half-life {
        type uint8 {
          range "1..45";
        }
        description
          "Half-life time for the penalty (minutes).";
      }

      leaf reuse {
        type uint16 {
          range "1..20000";
        }
        description
          "Value to start reusing a route.";
      }

      leaf suppress {
        type uint16 {
          range "1..20000";
        }
        description
          "Value to start suppressing a route.";
      }

      leaf max-suppress-time {
        type uint8 {
          range "1..255";
        }
        description
          "Maximum duration to suppress a stable route (minutes).";
      }
    }

    leaf deterministic-med {
      type boolean;
      default "false";
      description
        "If true, Pick the best-MED path among paths advertised from the
         neighboring AS.";
    }

    leaf ebgp-connected-route-check {
      type boolean;
      default "true";
      description
        "Enable or disable checking if nexthop is connected on ebgp sessions.";
    }

    leaf fast-external-failover {
      type boolean;
      default "true";
      description
        "If true, immediately reset session if a link to a directly connected external
         peer goes down.";
    }

    container graceful-restart {
      presence "Enable graceful restart capability parameters";
      description
        "Configure graceful restart capability parameters.";

      leaf preserve-fw-state {
        type boolean;
        default "false";
        description
          "If true, sets F-bit indication that fib is preserved while doing
           Graceful Restart.";
      }

      leaf restart-time {
        type uint16 {
          range "1..3600";
        }
        default "120";
        description
          "Set the time to wait to delete stale routes before a BGP open
           message is received.";
      }

      leaf stalepath-time {
        type uint16 {
          range "1..3600";
        }
        default "360";
        description
          "Set the max time to hold onto restarting peer's stale paths.";
      }
    }

    leaf graceful-shutdown {
      type boolean;
      default "false";
      description
        "Enable or disable graceful shutdown. When enabled, EBGP route
         attributes are sent with the GRACEFUL_SHUTDOWN (see RFC8326) community
         and preference set to 0.";
    }

    container listen {
      description
        "Configure BGP listen options.";

      leaf limit {
        type uint16 {
          range "1..5000";
        }
        default "100";
        description
          "Maximum number of BGP Dynamic neighbors that can be created.";
      }

      list neighbor-range {
        key "address";
        description
          "Configure BGP dynamic neighbors listen range.";
        ntos-extensions:nc-cli-one-liner;

        leaf address {
          type ntos-inet:ip-prefix;
          description
            "Neighbor address.";
        }
      }
      // XXX neighbor-group
    }

    leaf log-neighbor-changes {
      type boolean;
      default "false";
      description
        "If true, log neighbor up/down and reset reason.";
    }

    container max-med {
      must 'administrative or on-startup' {
        error-message "At least administrative or on-startup must be configured.";
      }
      presence "Enable advertise routes with max-med";
      description
        "Advertise routes with max-med.";

      leaf administrative {
        type uint32;
        description
          "Administratively applied, for an indefinite period.";
      }

      container on-startup {
        presence "make max-med effective on startup";
        description
          "Effective on a startup.";

        leaf period {
          type uint32 {
            range "5..86400";
          }
          mandatory true;
          description
            "Time (seconds) period for max-med.";
        }

        leaf max-med {
          type uint32;
          default "**********";
          description
            "Max MED value to be used.";
        }
      }
    }

    leaf network-import-check {
      type boolean;
      default "false";
      description
        "If true, check BGP network route exists in IGP.";
    }

    leaf route-reflector-allow-outbound-policy {
      type boolean;
      default "false";
      description
        "If true, allow modifications made by out route-map on IBGP neighbors.";
    }

    leaf reject-as-sets {
      type boolean;
      default "false";
      description
        "Some BGP routers may perform route aggregation, and because of that,
         those routers may use AS_SET and AS_CONFED_SETS attributes that contain
         an unordered list of ASes that contributing prefixes in the aggregate
         have traversed. Using those attributes may cause operational issues,
         because they blur the semantic of origin AS.";
    }

    container packet-rw-quantum {
      description
        "Number of packets to read/write from peer socket per I/O cycle.";

      leaf read {
        type uint8 {
          range "1..10";
        }
        default "10";
        description
          "Number of packets to read from peer socket per I/O cycle.";
      }

      leaf write {
        type uint8 {
          range "1..64";
        }
        ntos-api:range-added "1..64";
        default "64";
        description
          "Number of packets to write from peer socket per I/O cycle.";
      }
    }

    leaf router-id {
      type ntos-inet:ipv4-address;
      description
        "Router id of the router.";
      reference "RFC4271 - A Border Gateway Protocol 4 (BGP-4), Section 4.2";
    }

    container update-delay {
      presence "Enable to force initial delay for best-path and updates";
      description
        "Force initial delay for best-path and updates.";

      leaf delay {
        type uint16 {
          range "0..3600";
        }
        default "0";
        description
          "Force initial delay for best-path and updates.";
      }

      leaf established-wait {
        type uint16 {
          range "1..3600";
        }
        must '../delay > current()' {
          error-message "The delay must be greater or equal to the establish-wait.";
        }
        description
          "Wait for peers to be established.";
      }
    }

    leaf ebgp-requires-policy {
      type boolean;
      default "false";
      description
        "If true, require in and out policy for eBGP peers (RFC8212).";
    }
  }

  // --- BGP neighbors configuration --- \\

  grouping neigh-af-common-ip-ucast-mcast-lbl-conf {
    description
      "Common configuration parameters for IPv4/IPv6 unicast, multicast and
       labeled-unicast address families.";

    leaf capability-orf-prefix-list {
      type enumeration {
        enum both {
          description
            "Capability to SEND and RECEIVE the ORF to/from this neighbor.";
        }
        enum send {
          description
            "Capability to SEND the ORF to this neighbor.";
        }
        enum receive {
          description
            "Capability to RECEIVE the ORF from this neighbor.";
        }
      }
      description
        "Advertise prefixlist ORF capability to this neighbor.";
    }

    container default-originate {
      presence "Originate default route to this neighbor";
      description
        "Originate default route to this neighbor.";
      // Augmented with route-map leaf.
    }
  }

  grouping neigh-af-common-all-conf {
    description
      "Common configuration parameters to all address families.";

    list route-map {
      key "route-direction";
      description
        "Apply route map to this neighbor.";
      ntos-extensions:nc-cli-one-liner;

      leaf route-direction {
        type enumeration {
          enum in {
            description
              "Apply map to incoming routes.";
          }
          enum out {
            description
              "Apply map to outbound routes.";
          }
        }
        description
          "Route direction.";
      }
      // Augmented with route-map-name leaf.
    }

    leaf route-reflector-client {
      // XXX Only IGP (remote-as == as). Case remote-as set in neighbor-group
      // attached to the current neighbor.
      type boolean;
      default "false";
      description
        "If true, configure a neighbor as Route Reflector client.
         This only applies to internal neighbors (IGP).";
    }

    leaf route-server-client {
      type boolean;
      default "false";
      description
        "If true, configure a neighbor as Route Server client.";
    }

    leaf soft-reconfiguration-inbound {
      type boolean;
      default "false";
      description
        "If true, allow inbound soft reconfiguration for this neighbor.";
    }
  }

  grouping neigh-af-common-ip-ucast-mcast-lbl-vpn-flowspec-conf {
    description
      "Common configuration parameters for IPv4/IPv6 unicast, multicast,
       labeled-unicast, VPN and Flowspec address families.";

    list filter-list {
      key "update-direction";
      description
        "Establish BGP filters.";
      ntos-extensions:nc-cli-one-liner;

      leaf update-direction {
        type enumeration {
          enum in {
            description
              "Filter incoming updates.";
          }
          enum out {
            description
              "Filter outgoing updates.";
          }
        }
        description
          "Updates direction.";
      }
      // Augmented with access-list leaf.
    }

    list prefix-list {
      key "update-direction";
      description
        "Filter updates to/from this neighbor.";
      ntos-extensions:nc-cli-one-liner;

      leaf update-direction {
        type enumeration {
          enum in {
            description
              "Filter incoming updates.";
          }
          enum out {
            description
              "Filter outgoing updates.";
          }
        }
        description
          "Updates direction.";
      }
      // Augmented with prefix-list-name leaf.
    }
  }

  grouping neigh-af-common-ip-ucast-mcast-lbl-vpn-l2vpn-conf {
    description
      "Common configuration parameters for IPv4/IPv6 unicast, multicast,
       labeled-unicast, VPN and l2vpn evpn address families.";

    leaf allowas-in {
      type union {
        type uint8 {
          range "1..10";
        }
        type enumeration {
          enum origin {
            description
              "Only accept my AS in the as-path if the route was originated in
               my AS.";
          }
        }
      }
      description
        "Accept as-path with my AS present in it.";
    }

    leaf-list attribute-unchanged {
      type enumeration {
        enum as-path {
          description
            "As-path attribute.";
        }
        enum med {
          description
            "Med attribute.";
        }
        enum nexthop {
          description
            "Nexthop attribute.";
        }
      }
      description
        "BGP attribute is propagated unchanged to this neighbor.";
    }
  }

  grouping neigh-af-common-ip-ucast-mcast-lbl-vpn-conf {
    description
      "Common configuration parameters for IPv4/IPv6 unicast, multicast,
       labeled-unicast and VPN address families.";

    container addpath {
      must "not(tx-all-paths = 'true' and tx-best-path-per-AS = 'true')" {
        error-message "tx-all-path and tx-best-path-per-AS cannot be true at the same time.";
      }
      description
        "Configure addpath.";

      leaf tx-all-paths {
        type boolean;
        default "false";
        description
          "If true, use addpath to advertise all paths to a neighbor.";
      }

      leaf tx-best-path-per-AS {
        type boolean;
        default "false";
        description
          "If true, use addpath to advertise the bestpath per each neighboring AS.";
      }
    }

    leaf as-override {
      // XXX Only EGP (remote-as != as). Case remote-as set in neighbor-group
      // attached to the current neighbor.
      type boolean;
      default "false";
      description
        "If true, disable checking if nexthop is connected on ebgp sessions.";
    }

    list distribute-list {
      // XXX case prefix-list/distribute-list also set in a neighbor group
      // member
      must 'not(. and ../prefix-list)' {
        error-message "Prefix/distribute list cannot co-exist.";
      }
      key "update-direction";
      description
        "Filter updates to/from this neighbor.";
      ntos-extensions:nc-cli-one-liner;

      leaf update-direction {
        type enumeration {
          enum in {
            description
              "Filter incoming updates.";
          }
          enum out {
            description
              "Filter outgoing updates.";
          }
        }
        description
          "Updates direction.";
      }
      // Augmented with access-list leaf.
    }

    container maximum-prefix {
      must "count(warning-only[text() = 'true']) + count(restart) <= 1" {
        error-message
          "warning-only and restart options cannot be configured at the same
           time!";
      }
      presence "Configure maximum number of prefixes to accept from this peer";
      description
        "Maximum number of prefixes to accept from this peer.";

      leaf maximum {
        type uint32 {
          range "1..**********";
        }
        mandatory true;
        description
          "Maximum number of prefix limit.";
      }

      leaf threshold {
        type uint8 {
          range "1..100";
        }
        default "75";
        description
          "Threshold value (%) at which to generate a warning msg.";
      }

      leaf restart {
        type uint16 {
          range "1..65535";
        }
        description
          "Restart interval in minutes.";
      }

      leaf warning-only {
        type boolean;
        default "false";
        description
          "If true, only give warning message when limit is exceeded.";
      }
    }

    leaf maximum-prefix-out {
      type uint32 {
        range "1..**********";
      }
      description
        "Sets a maximum number of prefixes we can send to a given peer.";
    }

    container nexthop-self {
      presence "Disable the next hop calculation for this neighbor";
      description
        "Disable the next hop calculation for this neighbor.";

      leaf force {
        type boolean;
        default "false";
        description
          "If true, set the next hop to self for reflected routes.";
      }
    }

    container as-outbound-update {
      presence "Remove private AS numbers in outbound updates";
      description
        "Remove or replace ASNs in outbound updates.";

      leaf action {
        type enumeration {
          enum replace {
            description
              "Replace ASNs in outbound updates by our ASN.";
          }
          enum remove {
            description
              "Remove ASN in outbound updates.";
          }
        }
        default "remove";
        description
          "Action to apply for ASNs in outbound updates.";
      }

      leaf as-type {
        type enumeration {
          enum private {
            description
              "Apply to private AS numbers only.";
          }
          enum all {
            description
              "Apply to all AS numbers.";
          }
        }
        default "private";
        description
          "Apply to private AS numbers or all.";
      }
    }

    leaf send-community {
      type enumeration {
        enum all {
          description
            "Send Standard and Extended Community attributes.";
        }
        enum extended {
          description
            "Send Extended Community attributes.";
        }
        enum standard {
          description
            "Send Standard Community attributes.";
        }
        enum none {
          description
            "Do not send Standard or Extended Community attributes.";
        }
      }
      default "all";
      description
        "Send Community attribute to this neighbor.";
    }

    leaf weight {
      type uint16;
      description
        "Set default weight for routes from this neighbor.";
    }
    uses neigh-af-common-ip-ucast-mcast-lbl-vpn-l2vpn-conf;
    uses neigh-af-common-ip-ucast-mcast-lbl-vpn-flowspec-conf;
    uses neigh-af-common-all-conf;
  }

  grouping neigh-ip-ucast-mcast-lbl {
    description
      "IPv4/IPv6 unicast/multicast address-family associated with the BGP
       neighbor common to config and state.";
    uses neigh-af-common-ip-ucast-mcast-lbl-vpn-conf;
    uses neigh-af-common-ip-ucast-mcast-lbl-conf;
  }

  grouping neigh-ip-ucast-mcast-lbl-conf {
    description
      "IPv4 unicast/multicast address-family config associated with the BGP
       neighbor.";
    uses neigh-ip-ucast-mcast-lbl {

      augment "default-originate" {
        description
          "Add route-map option to default-originate containter.";

        leaf route-map {
          type ntos-rt-types:route-map-name;
          description
            "Route-map to specify criteria to originate default.";
        }
      }

      augment "route-map" {
        description
          "Add route-map-name option to route-map list.";

        leaf route-map-name {
          type ntos-rt-types:route-map-name;
          mandatory true;
          description
            "Route-map name.";
        }
      }

      augment "filter-list" {
        description
          "Add access-list option to filter list.";

        leaf access-list {
          type leafref {
            path
              "/ntos:config/ntos-rt:routing/ntos-bgp:bgp/ntos-bgp:as-path-access-list/ntos-bgp:name";
            require-instance false;
            ntos-extensions:nc-cli-shortdesc "<as-path-access-list>";
          }
          mandatory true;
          description
            "Access list name.";
        }
      }

      augment "prefix-list" {
        description
          "Add prefix-list-name option to prefix-list.";

        leaf prefix-list-name {
          type ntos-rt-types:v4-prefix-list-name;
          mandatory true;
          description
            "Name of the prefix list.";
        }
      }

      augment "distribute-list" {
        description
          "Add access-list option to distribute list.";

        leaf access-list {
          type ntos-rt-types:v4-access-list-name;
          mandatory true;
          description
            "Access list name.";
        }
      }
    }

    leaf unsuppress-map {
      type ntos-rt-types:route-map-name;
      description
        "Route-map to selectively unsuppress suppressed routes.";
    }
  }

  grouping neigh-ip6-ucast-mcast-lbl-conf {
    description
      "IPv6 unicast/multicast address-family config associated with the BGP
       neighbor.";
    uses neigh-ip-ucast-mcast-lbl {

      augment "default-originate" {
        description
          "Add route-map option to default-originate containter.";

        leaf route-map {
          type ntos-rt-types:route-map-name;
          description
            "Route-map to specify criteria to originate default.";
        }
      }

      augment "route-map" {
        description
          "Add route-map-name option to route-map list.";

        leaf route-map-name {
          type ntos-rt-types:route-map-name;
          mandatory true;
          description
            "Route-map name.";
        }
      }

      augment "filter-list" {
        description
          "Add access-list option to filter list.";

        leaf access-list {
          type leafref {
            path
              "/ntos:config/ntos-rt:routing/ntos-bgp:bgp/ntos-bgp:as-path-access-list/ntos-bgp:name";
            require-instance false;
            ntos-extensions:nc-cli-shortdesc "<as-path-access-list>";
          }
          mandatory true;
          description
            "Access list name.";
        }
      }

      augment "prefix-list" {
        description
          "Add prefix-list-name option to prefix-list.";

        leaf prefix-list-name {
          type ntos-rt-types:v6-prefix-list-name;
          mandatory true;
          description
            "Name of the prefix list.";
        }
      }

      augment "distribute-list" {
        description
          "Add access-list option to distribute list.";

        leaf access-list {
          type ntos-rt-types:v6-access-list-name;
          mandatory true;
          description
            "Access list name.";
        }
      }
    }

    leaf unsuppress-map {
      type ntos-rt-types:route-map-name;
      description
        "Route-map to selectively unsuppress suppressed routes.";
    }
  }

  grouping neigh-ip-flowspec {
    description
      "IPv4/IPv6 Flowspec address-family associated with the BGP neighbor common
       to config and state.";
    uses neigh-af-common-ip-ucast-mcast-lbl-vpn-flowspec-conf;
    uses neigh-af-common-all-conf;
  }

  grouping neigh-ip-flowspec-conf {
    description
      "IPv4 Flowspec address-family config associated with the BGP
       neighbor.";
    uses neigh-ip-flowspec {

      augment "route-map" {
        description
          "Add route-map-name option to route-map list.";

        leaf route-map-name {
          type ntos-rt-types:route-map-name;
          mandatory true;
          description
            "Route-map name.";
        }
      }

      augment "filter-list" {
        description
          "Add access-list option to filter list.";

        leaf access-list {
          type leafref {
            path
              "/ntos:config/ntos-rt:routing/ntos-bgp:bgp/ntos-bgp:as-path-access-list/ntos-bgp:name";
            require-instance false;
            ntos-extensions:nc-cli-shortdesc "<as-path-access-list>";
          }
          mandatory true;
          description
            "Access list name.";
        }
      }

      augment "prefix-list" {
        description
          "Add prefix-list-name option to prefix list.";

        leaf prefix-list-name {
          type ntos-rt-types:v4-prefix-list-name;
          mandatory true;
          description
            "Name of the prefix list.";
        }
      }
    }
  }

  grouping neigh-ip6-flowspec-conf {
    description
      "IPv6 Flowspec address-family config associated with the BGP
       neighbor.";
    uses neigh-ip-flowspec {

      augment "route-map" {
        description
          "Add route-map-name option to route-map list.";

        leaf route-map-name {
          type ntos-rt-types:route-map-name;
          mandatory true;
          description
            "Route-map name.";
        }
      }

      augment "filter-list" {
        description
          "Add access-list option to filter list.";

        leaf access-list {
          type leafref {
            path
              "/ntos:config/ntos-rt:routing/ntos-bgp:bgp/ntos-bgp:as-path-access-list/ntos-bgp:name";
            require-instance false;
            ntos-extensions:nc-cli-shortdesc "<as-path-access-list>";
          }
          mandatory true;
          description
            "Access list name.";
        }
      }

      augment "prefix-list" {
        description
          "Add prefix-list-name option to prefix list.";

        leaf prefix-list-name {
          type ntos-rt-types:v6-prefix-list-name;
          mandatory true;
          description
            "Name of the prefix list.";
        }
      }
    }
  }

  grouping neigh-ip-vpn-conf {
    description
      "IPv4/IPv6 VPN address-family configuration associated with the BGP
       neighbor.";

    leaf unsuppress-map {
      type ntos-rt-types:route-map-name;
      description
        "Route-map to selectively unsuppress suppressed routes.";
    }
    uses neigh-af-common-ip-ucast-mcast-lbl-vpn-conf;
  }

  grouping neigh-l2vpn-evpn-conf {
    description
      "L2VPN EVPN address-family configuration associated with the BGP
       neighbor.";

    leaf nexthop-self {
      type boolean;
      default "false";
      description
        "Disable the next hop calculation for this neighbor.";
    }
    uses neigh-af-common-all-conf;
    uses neigh-af-common-ip-ucast-mcast-lbl-vpn-l2vpn-conf;
  }

  grouping neigh-address-family-conf {
    description
      "Address-families associated with the BGP neighbor.";

    container address-family {
      description
        "Address-families associated with the BGP neighbor.";

      container ipv4-unicast {
        must "not(../ipv4-labeled-unicast/enabled = 'true' and enabled = 'true')" {
          error-message "Cannot activate peer for both 'ipv4 unicast' and 'ipv4 labeled-unicast'.";
        }
        description
          "IPv4 unicast address-family associated with the BGP neighbor.";

        leaf enabled {
          type boolean;
          default "true";
          description
            "Enable or disable IPv4 unicast Address Family for this neighbor.";
        }
        uses neigh-ip-ucast-mcast-lbl-conf;
      }

      container ipv4-multicast {
        presence "Enable IPv4 multicast Address Family for this neighbor";
        description
          "IPv4 multicast address-family associated with the BGP neighbor.";

        leaf enabled {
          type boolean;
          default "true";
          description
            "Enable or disable IPv4 multicast Address Family for this neighbor.";
        }
        uses neigh-ip-ucast-mcast-lbl-conf;
      }

      container ipv4-labeled-unicast {
        presence "Enable IPv4 labeled unicast Address Family for this neighbor";
        description
          "IPv4 labeled unicast address-family associated with the BGP
           neighbor.";

        leaf enabled {
          type boolean;
          default "true";
          description
            "Enable or disable IPv4 labeled unicast Address Family for this neighbor.";
        }
        uses neigh-ip-ucast-mcast-lbl-conf;
      }

      container ipv4-flowspec {
        presence "Enable IPv4 Flowspec Address Family for this neighbor";
        description
          "IPv4 Flowspec address-family associated with the BGP neighbor.";

        leaf enabled {
          type boolean;
          default "true";
          description
            "Enable or disable IPv4 Flowspec Address Family for this neighbor.";
        }
        uses neigh-ip-flowspec-conf;
      }

      container ipv4-vpn {
        presence "Configure IPv4 VPN Address Family options";
        description
          "Configure IPv4 VPN address family.";

        leaf enabled {
          type boolean;
          default "true";
          description
            "Enable or disable IPv4 VPN Address Family for this neighbor.";
        }
        uses neigh-ip-vpn-conf;
      }

      container ipv6-unicast {
        must "not(../ipv6-labeled-unicast/enabled = 'true' and enabled = 'true')" {
          error-message "Cannot activate peer for both 'ipv6 unicast' and 'ipv6 labeled-unicast'.";
        }
        presence "Enable IPv6 unicast Address Family for this neighbor";
        description
          "IPv6 unicast address-family associated with the BGP neighbor.";

        leaf enabled {
          type boolean;
          default "true";
          description
            "Enable or disable IPv6 unicast Address Family for this neighbor.";
        }

        leaf nexthop-local-unchanged {
          type boolean;
          default "false";
          description
            "If true, leave link-local nexthop unchanged for this peer.";
        }
        uses neigh-ip6-ucast-mcast-lbl-conf;
      }

      container ipv6-multicast {
        presence "Enable IPv6 multicast Address Family for this neighbor";
        description
          "IPv6 multicast address-family associated with the BGP neighbor.";

        leaf enabled {
          type boolean;
          default "true";
          description
            "Enable or disable IPv6 multicast Address Family for this
             neighbor.";
        }
        uses neigh-ip6-ucast-mcast-lbl-conf;
      }

      container ipv6-labeled-unicast {
        presence "Enable IPv6 labeled unicast Address Family for this neighbor";
        description
          "IPv6 labeled unicast address-family associated with the BGP
           neighbor.";

        leaf enabled {
          type boolean;
          default "true";
          description
            "Enable or disable IPv6 labeled unicast Address Family for this
             neighbor.";
        }
        uses neigh-ip6-ucast-mcast-lbl-conf;
      }

      container ipv6-flowspec {
        presence "Enable IPv6 Flowspec Address Family for this neighbor";
        description
          "IPv6 Flowspec address-family associated with the BGP neighbor.";

        leaf enabled {
          type boolean;
          default "true";
          description
            "Enable or disable IPv6 Flowspec Address Family for this neighbor.";
        }
        uses neigh-ip6-flowspec-conf;
      }

      container ipv6-vpn {
        presence "Configure IPv6 VPN Address Family options";
        description
          "Configure IPv6 VPN address family.";

        leaf enabled {
          type boolean;
          default "true";
          description
            "Enable or disable IPv6 VPN Address Family for this neighbor.";
        }
        uses neigh-ip-vpn-conf;
      }

      container l2vpn-evpn {
        presence "Configure L2VPN EVPN address family options";
        description
          "Configure L2VPN EVPN address family.";

        leaf enabled {
          type boolean;
          default "true";
          description
            "Enable or disable L2VPN EVPN Address Family for this neighbor.";
        }
        uses neigh-l2vpn-evpn-conf {

          augment "route-map" {
            description
              "Add route-map-name option to route-map list.";

            leaf route-map-name {
              type ntos-rt-types:route-map-name;
              mandatory true;
              description
                "Route-map name.";
            }
          }
        }
      }
    }
  }

  grouping neigh-common-conf {
    description
      "Configuration parameters relating to timers used for the BGP neighbor.";

    leaf remote-as {
      type union {
        type uint32 {
          range "1..**********";
        }
        type enumeration {
          enum external {
            description
              "External BGP peer.";
          }
          enum internal {
            description
              "Internal BGP peer.";
          }
        }
      }
      description
        "Remote AS number.";
      ntos-api:must-added "count(../neighbor-group) = 0 or count(../../neighbor-group[name=current()/../neighbor-group]/remote-as) = 0";
    }

    leaf-list capability {
      type enumeration {
        enum dynamic {
          description
            "Advertise dynamic capability to this neighbor.";
        }
        enum extended-nexthop {
          description
            "Advertise extended nexthop capability to the peer.";
        }
      }
      description
        "Advertise capability to the peer.";
    }

    leaf capability-negotiate {
      type boolean;
      default "true";
      description
        "If true, perform capability negotiation.";
    }

    leaf ebgp-multihop {
      type uint8 {
        range "1..255";
      }
      // XXX case ebgp-multihop/ttl-security-hops also set in a neighbor group
      // member
      must 'not(. and ../ttl-security-hops)' {
        error-message "ebgp-multihop and ttl-security-hops cannot be configured together.";
      }
      description
        "Allow EBGP neighbors not on directly connected networks.";
    }

    leaf enforce-first-as {
      type boolean;
      default "false";
      description
        "If true, enforce the first AS for EBGP routes.";
    }

    leaf enforce-multihop {
      // Alias of disable-connected-check
      type boolean;
      default "false";
      description
        "If true, enforce EBGP neighbors perform multihop.";
    }

    container local-as {
      // XXX Only EGP (remote-as != as). Case remote-as set in neighbor-group
      // attached to the current neighbor.
      presence "Enable to specify a local-as number for this neighbor";
      description
        "Specify a local-as number.";

      leaf as-number {
        type uint32 {
          range "1..**********";
        }
        mandatory true;
        description
          "AS number used as local AS.";
      }

      leaf no-prepend {
        type boolean;
        default "false";
        description
          "If true, do not prepend local-as to updates from ebgp peers.";
      }

      leaf replace-as {
        when "../no-prepend = 'true'" {
          description
            "No-prepend must be set first.";
        }
        type boolean;
        default "false";
        description
          "If true, do not prepend local-as to updates from ibgp peers.";
      }
    }

    leaf neighbor-description {
      type string {
        length "1..80";
      }
      description
        "Neighbor specific description: up to 80 characters describing this
         neighbor.";
      ntos-api:length-added "1..80";
    }

    leaf override-capability {
      type boolean;
      default "false";
      description
        "If true, override capability negotiation result.";
    }

    leaf passive {
      type boolean;
      default "false";
      description
        "If true, don't send open messages to this neighbor.";
    }

    leaf password {
      type string;
      description
        "Set a password.";
    }

    container shutdown {
      presence "Administratively shut down this neighbor";
      description
        "Administratively shut down this neighbor.";

      leaf message {
        type string;
        description
          "Shutdown message.";
      }
    }

    leaf solo {
      type boolean;
      default "false";
      description
        "If true, solo peer - part of its own update group.";
    }

    leaf strict-capability-match {
      type boolean;
      must "current() = 'false' or current() != ../override-capability" {
        error-message
          "override-capability and strict-capability-match cannot be set at the
           same time.";
      }
      default "false";
      description
        "Enable or disable strict capability negotiation match.";
    }

    leaf track {
      type union {
        type ntos-tracker:tracker-name;
        type identityref {
          base ntos-types:RTG_AUTO_TRACKER;
        }
      }
      description
        "A tracker name defined in the tracker context, or the BGP internal BFD
         tracker (bfd keyword). If a tracker name is used, when the tracked
         address is reachable, the neighbor or neighbor group is considered as
         valid, else it is disabled. If the BGP internal BFD tracker is used, it
         works the same way, but this neighbor address is automatically
         tracked. The check-control-plane-failure option is available only when
         the BGP internal BFD tracker is used.";
      ntos-extensions:nc-cli-completion-xpath
        "/ntos:config/ntos-tracker:tracker/ntos-pm:icmp/ntos-tracker:name |
         /ntos:config/ntos-tracker:tracker/ntos-bfd:bfd/ntos-bfd:name";
    }

    leaf check-control-plane-failure {
      when "../track = 'bfd'" {
        description
          "This option is available only if the BGP internal BFD tracker is
           selected, i.e the 'track bfd' option is set.";
      }
      type boolean;
      description
        "Link data-plane status with BGP control-plane. This option is available
         only if the BGP internal BFD tracker is selected, i.e the 'track bfd'
         option is set.";
    }

    leaf ttl-security-hops {
      type uint8 {
        range "1..254";
      }
      description
        "Specify the maximum number of hops to the BGP peer.";
    }

    leaf update-source {
      type union {
        type ntos-inet:ip-address;
        type ntos-types:ifname;
      }
      description
        "Source of routing updates.";
      ntos-extensions:nc-cli-completion-xpath
        "../../../ntos-interface:interface/*/*[local-name()='name']";
    }

    container timers {
      presence "Configure neighbor timers";
      description
        "Config parameters related to timers associated with the BGP
         peer.";

      leaf advertisement-interval {
        type uint16 {
          range "1..600";
        }
        description
          "Minimum time which must elapse between subsequent UPDATE messages
           relating to a common set of NLRI being transmitted to a peer. This
           timer is referred to as MinRouteAdvertisementIntervalTimer by RFC 4721
           and serves to reduce the number of UPDATE messages transmitted when a
           particular set of NLRI exhibit instability. A change of this value will
           be taken into account for new sessions.";
        reference "RFC 4271 - A Border Gateway Protocol 4, Sec *******";
      }

      leaf connect-retry {
        type uint16 {
          range "1..65535";
        }
        description
          "Time interval in seconds between attempts to establish a
           session with the peer.";
      }

      leaf keepalive-interval {
        type uint16;
        must '../hold-time' {
          error-message
            "hold-time must be set and greater or equal to keepalive-interval *
             3.";
        }
        description
          "Time interval in seconds between transmission of keepalive messages
           to the neighbor. Typically set to 1/3 the hold-time. A change of this
           value will be taken into account for new sessions.";
      }

      leaf hold-time {
        type uint16;
        must '../keepalive-interval and ../keepalive-interval * 3 <= current()' {
          error-message
            "keepalive-interval must be set and hold-time must be greater or
             equal to keepalive-interval * 3.";
        }
        description
          "Time interval in seconds that a BGP session will be considered active
           in the absence of keepalive or other messages from the peer. The
           hold-time is typically set to 3x the keepalive-interval.";
        reference "RFC 4271 - A Border Gateway Protocol 4, Sec. 10";
      }
    }

    leaf sender-as-path-loop-detection {
      type boolean;
      default "false";
      description
        "Detect the sender side AS path loops and filter the bad routes before
         they are sent.";
    }
  }

  grouping neigh-group-base-conf {
    description
      "Parameters related to a BGP neighbor group.";

    leaf name {
      type string;
      description
        "Reference to the name of the BGP neighbor-group used as a key in the
         neighbor-group list.";
    }
    uses neigh-common-conf;
  }

  grouping neigh-base-conf {
    description
      "Parameters related to a BGP neighbor.";

    leaf neighbor-address {
      type ntos-inet:ip-address;
      description
        "IPv4 or IPv6 address of the BGP neighbor.";
    }

    leaf interface {
      type ntos-types:ifname;
      description
        "Name of the interface.";
      ntos-extensions:nc-cli-completion-xpath
        "../../../ntos-interface:interface/*/*[local-name()='name']";
    }

    leaf port {
      type ntos-inet:port-number;
      description
        "TCP port number.";
    }
    uses neigh-common-conf;
  }

  grouping bgp-conf {
    description
      "BGP router instance.";

    leaf enabled {
      type boolean;
      default "true";
      description
        "Enable or disable BGP router.";
    }

    leaf as {
      type ntos-inet:as-number {
        range "1..**********";
      }
      mandatory true;
      description
        "BGP AS number.";
    }
    uses global-conf {

      augment "listen/neighbor-range" {
        description
          "Add neighbor-group to lister neighbor-range list.";

        leaf neighbor-group {
          type leafref {
            path
              "/ntos:config/ntos:vrf/ntos-rt:routing/ntos-bgp:bgp/ntos-bgp:neighbor-group/ntos-bgp:name";
            ntos-extensions:nc-cli-shortdesc "<neighbor-group>";
          }
          must 'count(../../../neighbor-group[name = current()]/remote-as) > 0' {
            error-message "The remote-as must be specified for this neighbor group!";
          }
          mandatory true;
          description
            "Neighbor group name.";
        }
      }
    }
    uses global-address-family-conf;

    list neighbor-group {
      must
        "remote-as or (count(../neighbor[neighbor-group=current()/name and remote-as='internal']/remote-as)
         + count(../neighbor[neighbor-group=current()/name and remote-as=../as]/remote-as))
         = count(../neighbor[neighbor-group=current()/name]/remote-as) or
         (count(../neighbor[neighbor-group=current()/name and remote-as='external']/remote-as)
         + count(../neighbor[neighbor-group=current()/name and remote-as!=../as and remote-as!='internal' and remote-as!='external']/remote-as))
         = count(../neighbor[neighbor-group=current()/name]/remote-as)" {
        error-message "neighbor-group members must be all internal or all external.";
      }
      key "name";
      description
        "List of BGP peer-groups configured on the local system - uniquely
         identified by peer-group name.";
      uses neigh-group-base-conf;
      uses neigh-address-family-conf;
      ntos-api:must-added
        "remote-as or (count(../neighbor[neighbor-group=current()/name and remote-as='internal']/remote-as)
         + count(../neighbor[neighbor-group=current()/name and remote-as=../as]/remote-as))
         = count(../neighbor[neighbor-group=current()/name]/remote-as) or
         (count(../neighbor[neighbor-group=current()/name and remote-as='external']/remote-as)
         + count(../neighbor[neighbor-group=current()/name and remote-as!=../as and remote-as!='internal' and remote-as!='external']/remote-as))
         = count(../neighbor[neighbor-group=current()/name]/remote-as)";
    }

    list neighbor {
      must 'count(remote-as) + count(neighbor-group) >= 1' {
        error-message "The remote-as or neighbor-group must be specified!";
      }
      key "neighbor-address";
      description
        "List of BGP neighbors configured on the local system,
         uniquely identified by peer IPv[46] address.";
      // XXX if neighbor group member: must remote-as OR neighbor-group
      // remote-as. Not both!

      leaf neighbor-group {
        type leafref {
          path
            "/ntos:config/ntos:vrf/ntos-rt:routing/ntos-bgp:bgp/ntos-bgp:neighbor-group/ntos-bgp:name";
          ntos-extensions:nc-cli-shortdesc "<neighbor-group>";
        }
        description
          "Peer group name.";
      }
      uses neigh-base-conf {
        refine "remote-as" {
          must 'count(../neighbor-group) = 0 or count(../../neighbor-group[name=current()/../neighbor-group]/remote-as) = 0' {
            error-message "neighbor-group member cannot override remote-as of neighbor-group";
          }
        }
      }
      uses neigh-address-family-conf;
    }
  }

  // --- BGP state --- \\

  grouping global-route-af-state {
    description
      "Route address family state.";

    leaf peer-id {
      type string;
      description
        "Route state identifier.";
    }

    leaf status {
      type enumeration {
        enum removed {
          description
            "Entry is removed.";
        }
        enum suppressed {
          description
            "Entry is being removed.";
        }
        enum used {
          description
            "Entry is used.";
        }
      }
      description
        "Route state.";
    }

    leaf stale {
      type boolean;
      description
        "Entry declared stale because of restarting remote entity.";
    }

    leaf suppressed {
      type boolean;
      description
        "BGP path suppressed.";
    }

    leaf history {
      type boolean;
      description
        "This entry has been recorded for historical information.";
    }

    leaf damped {
      type boolean;
      description
        "Flapping events occured on that entry.";
    }

    leaf bestpath {
      type boolean;
      description
        "Selected route considered as the preferred one in the local routing
         context.";
    }

    leaf multipath {
      type boolean;
      description
        "Entry is selected and is not alone.";
    }

    leaf validity {
      type boolean;
      description
        "Route validity.";
    }

    leaf route-type {
      type enumeration {
        enum internal {
          description
            "Internal route.";
        }
        enum external {
          description
            "External route.";
        }
      }
      description
        "Internal or external route.";
    }

    leaf prefix {
      type ntos-inet:ip-address;
      description
        "Route prefix.";
    }

    leaf prefix-length {
      type uint8;
      description
        "Route prefix length.";
    }

    leaf med {
      type uint32;
      status obsolete {
        ntos-extensions:status-obsoleted-release "20q3";
        ntos-extensions:status-deprecated-revision "2020-01-23";
        ntos-extensions:status-description "med option is deprecated in FRR. It has been replaced by the metric one.";
        ntos-extensions:status-replacement "../metric";
      }
      description
        "Multi Exit Discriminator.";
    }

    leaf metric {
      type uint32;
      description
        "Route metric.";
    }

    leaf origin {
      type string;
      description
        "Route origin.";
    }

    leaf network {
      type ntos-inet:ip-prefix;
      description
        "Path network.";
    }

    leaf local-preference {
      type uint32;
      description
        "Local preference.";
    }

    leaf weight {
      type uint32;
      description
        "Weight.";
    }

    leaf packet-length {
      type string;
      description
        "Packet length.";
    }

    leaf path {
      type string;
      description
        "AS path.";
    }

    list nexthop {
      key "address";
      description
        "Route nexthop.";
      ntos-extensions:nc-cli-one-liner;

      leaf address {
        type union {
          type ntos-inet:ip-address;
          type ntos-inet:domain-name;
        }
        ntos-api:length-added "1..253";
        ntos-api:pattern-added
          '((([a-zA-Z0-9_]([a-zA-Z0-9\-_]){0,61})?[a-zA-Z0-9]\.)*([a-zA-Z0-9_]([a-zA-Z0-9\-_]){0,61})?[a-zA-Z0-9]\.?)|\.';
        description
          "Nexthop address or domain name.";
      }

      leaf address-family {
        type enumeration {
          enum ipv4 {
            description
              "IPv4 nexthop.";
          }
          enum ipv6 {
            description
              "IPv6 nexthop.";
          }
          enum fqdn {
            description
              "Domain name nexthop.";
          }
        }
        description
          "Nexthop address family.";
      }

      leaf used {
        type boolean;
        description
          "Nexthop used.";
      }
    }
  }

  grouping global-route-af-ucast-mcast-vpn-state {
    description
      "Route address family unicast/multicast state.";

    list route {
      key "peer-id";
      description
        "Route operational state.";
      uses global-route-af-state;
    }
  }

  grouping global-route-af-flowspec-state {
    description
      "Route address family Flowspec state.";

    list route {
      key "id";
      description
        "Route operational state.";

      leaf id {
        type uint32;
        description
          "Route identifier.";
      }

      leaf to {
        type ntos-inet:ip-prefix;
        description
          "Route destination prefix.";
      }

      leaf from {
        type ntos-inet:ip-prefix;
        description
          "Route source prefix.";
      }
      uses global-route-af-state;
    }
  }

  grouping neigh-state {
    description
      "Neighbor state.";

    leaf remote-neighbor-group {
      type string;
      description
        "Remote neighbor group.";
    }

    leaf remote-router-id {
      type ntos-inet:ip-address;
      description
        "Remote router identifier.";
    }

    leaf state {
      type string;
      description
        "BGP router status.";
    }

    leaf min-time-btwn-advertisement {
      type uint32;
      description
        "Minimum time between advertisement runs in miliseconds.";
    }

    container connections {
      description
        "Established/dropped connections statistics.";

      leaf established {
        type uint32;
        description
          "Number of established connections.";
      }

      leaf dropped {
        type uint32;
        description
          "Number of dropped connections.";
      }
    }

    leaf last-reset {
      type string;
      description
        "Last reset.";
    }

    container local-host {
      description
        "Local host data.";

      leaf name {
        type string;
        description
          "Local host name.";
      }

      leaf port {
        type uint16;
        description
          "Local host port.";
      }
    }

    container remote-host {
      description
        "Remote host data.";

      leaf name {
        type string;
        description
          "Remote host name.";
      }

      leaf port {
        type uint16;
        description
          "Remote host port.";
      }
    }

    container nexthop {
      description
        "Nexthop data.";

      leaf address {
        type ntos-inet:ip-address;
        description
          "Nexthop IP address.";
      }

      leaf global-address {
        type ntos-inet:ip-address;
        description
          "Nexthop global IP address.";
      }

      leaf local-address {
        type ntos-inet:ip-address;
        description
          "Nexthop local IP address.";
      }
    }

    leaf bgp-connection {
      type string;
      description
        "BGP connection type.";
    }

    leaf connect-retry-timer {
      type uint32;
      description
        "BGP connect retry timer in seconds.";
    }

    leaf estimated-rount-trip-time {
      type uint32;
      description
        "Estimated round trip time in miliseconds.";
    }

    container thread {
      description
        "Read/Write thread.";

      leaf read-enabled {
        type boolean;
        description
          "Read thread status.";
      }

      leaf write-enabled {
        type boolean;
        description
          "Write thread status.";
      }
    }

    container message-statistics {
      description
        "Neighbor messages statistics.";

      leaf packet-wait-process {
        type uint32;
        description
          "Number of packets waiting to be processed.";
      }

      leaf packet-wait-written {
        type uint32;
        description
          "Number of packets waiting to be written.";
      }

      leaf open-sent {
        type uint32;
        description
          "BGP open messages sent.";
      }

      leaf opens-received {
        type uint32;
        description
          "BGP open messages received.";
      }

      leaf notifications-sent {
        type uint32;
        description
          "Notifications messages sent.";
      }

      leaf notifications-received {
        type uint32;
        description
          "Notification messages received.";
      }

      leaf updates-sent {
        type uint32;
        description
          "Update messages sent.";
      }

      leaf updates-received {
        type uint32;
        description
          "Update messages received.";
      }

      leaf keepalives-sent {
        type uint32;
        description
          "Keepalive messages sent.";
      }

      leaf keepalives-received {
        type uint32;
        description
          "Keepalive messages received.";
      }

      leaf route-refresh-sent {
        type uint32;
        description
          "Route refresh messages sent.";
      }

      leaf route-refresh-received {
        type uint32;
        description
          "Route refresh messages received.";
      }

      leaf capability-sent {
        type uint32;
        description
          "Capability messages sent.";
      }

      leaf capability-received {
        type uint32;
        description
          "Capability messages received.";
      }

      leaf total-sent {
        type uint32;
        description
          "Total messages sent.";
      }

      leaf total-received {
        type uint32;
        description
          "Total messages received.";
      }
    }
  }

  grouping neigh-ip-ucast-mcast-lbl-state {
    description
      "IPv4/IPv6 unicast/multicast address-family config state associated with
       the BGP neighbor.";
    uses neigh-ip-ucast-mcast-lbl {

      augment "default-originate" {
        description
          "Add route-map option to default-originate containter.";

        leaf route-map {
          type string;
          description
            "Route-map to specify criteria to originate default.";
        }
      }

      augment "route-map" {
        description
          "Add route-map-name option to route-map list.";

        leaf route-map-name {
          type string;
          description
            "Route-map name.";
        }
      }

      augment "filter-list" {
        description
          "Add access-list option to filter list.";

        leaf access-list {
          type string;
          description
            "Access list name.";
        }
      }

      augment "prefix-list" {
        description
          "Add prefix-list-name option to prefix-list.";

        leaf prefix-list-name {
          type string;
          description
            "Name of the prefix list.";
        }
      }

      augment "distribute-list" {
        description
          "Add access-list option to distribute list.";

        leaf access-list {
          type string;
          description
            "Access list name.";
        }
      }
    }

    leaf unsuppress-map {
      type string;
      description
        "Route-map to selectively unsuppress suppressed routes.";
    }
  }

  grouping neigh-ip-flowspec-state {
    description
      "IPv4/IPv6 Flowspec address-family config state associated with the BGP
       neighbor.";
    uses neigh-ip-flowspec {

      augment "route-map" {
        description
          "Add route-map-name option to route-map list.";

        leaf route-map-name {
          type string;
          description
            "Route-map name.";
        }
      }

      augment "filter-list" {
        description
          "Add access-list option to filter list.";

        leaf access-list {
          type string;
          description
            "Access list name.";
        }
      }

      augment "prefix-list" {
        description
          "Add prefix-list-name option to prefix list.";

        leaf prefix-list-name {
          type string;
          description
            "Name of the prefix list.";
        }
      }
    }
  }

  grouping neigh-afi-safi-state {
    description
      "Neighbor state per address-family.";

    leaf update-group-id {
      type uint32;
      description
        "Update group identifier.";
    }

    leaf sub-group-id {
      type uint32;
      description
        "Sub-group identifier.";
    }

    leaf packet-queue-length {
      type uint32;
      description
        "Packet queue length.";
    }

    leaf accepted-prefix {
      type uint32;
      description
        "Accepted prefix counter.";
    }

    leaf inbound-ebgp-requires-policy {
      type string;
      description
        "The presence of this comment informs the user that incoming BGP updates are discarded, as per RFC8212 behaviour.";
    }

    leaf outbound-ebgp-requires-policy {
      type string;
      description
        "The presence of this comment informs the user that outgoing BGP updates are discarded, as per RFC8212 behaviour.";
    }
  }

  grouping neigh-address-family-state {
    description
      "Neighbor address family state.";

    container address-family {
      description
        "Address-families associated with the BGP neighbor.";

      container ipv4-unicast {
        description
          "IPv4 unicast address-family associated with the BGP neighbor.";

        leaf enabled {
          type boolean;
          description
            "Enable or disable IPv4 unicast Address Family for this neighbor.";
        }
        uses neigh-ip-ucast-mcast-lbl-state;
        uses neigh-afi-safi-state;
      }

      container ipv4-multicast {
        presence "Enable IPv4 multicast Address Family for this neighbor";
        description
          "IPv4 multicast address-family associated with the BGP neighbor.";

        leaf enabled {
          type boolean;
          description
            "Enable or disable IPv4 multicast Address Family for this neighbor.";
        }
        uses neigh-ip-ucast-mcast-lbl-state;
        uses neigh-afi-safi-state;
      }

      container ipv4-labeled-unicast {
        presence "Enable IPv4 labeled unicast Address Family for this neighbor";
        description
          "IPv4 labeled unicast address-family associated with the BGP
           neighbor.";

        leaf enabled {
          type boolean;
          description
            "Enable or disable IPv4 labeled unicast Address Family for this neighbor.";
        }
        uses neigh-ip-ucast-mcast-lbl-state;
        uses neigh-afi-safi-state;
      }

      container ipv4-flowspec {
        presence "Enable IPv4 Flowspec Address Family for this neighbor";
        description
          "IPv4 Flowspec address-family associated with the BGP neighbor.";

        leaf enabled {
          type boolean;
          description
            "Enable or disable IPv4 Flowspec Address Family for this neighbor.";
        }
        uses neigh-ip-flowspec-state;
        uses neigh-afi-safi-state;
      }

      container ipv4-vpn {
        presence "Enable IPv4 VPN Address Family for this neighbor";
        description
          "IPv4 VPN address-family associated with the BGP neighbor.";

        leaf enabled {
          type boolean;
          default "true";
          description
            "Enable or disable IPv4 VPN Address Family for this neighbor.";
        }
        uses neigh-ip-vpn-conf;
        uses neigh-afi-safi-state;
      }

      container ipv6-unicast {
        presence "Enable IPv6 unicast Address Family for this neighbor";
        description
          "IPv6 unicast address-family associated with the BGP neighbor.";

        leaf enabled {
          type boolean;
          description
            "Enable or disable IPv6 unicast Address Family for this neighbor.";
        }

        leaf nexthop-local-unchanged {
          type boolean;
          default "false";
          description
            "If true, leave link-local nexthop unchanged for this peer.";
        }
        uses neigh-ip-ucast-mcast-lbl-state;
        uses neigh-afi-safi-state;
      }

      container ipv6-multicast {
        presence "Enable IPv6 multicast Address Family for this neighbor";
        description
          "IPv6 multicast address-family associated with the BGP neighbor.";

        leaf enabled {
          type boolean;
          default "true";
          description
            "Enable or disable IPv6 multicast Address Family for this
             neighbor.";
        }
        uses neigh-ip-ucast-mcast-lbl-state;
        uses neigh-afi-safi-state;
      }

      container ipv6-labeled-unicast {
        presence "Enable IPv6 labeled unicast Address Family for this neighbor";
        description
          "IPv6 labeled unicast address-family associated with the BGP
           neighbor.";

        leaf enabled {
          type boolean;
          description
            "Enable or disable IPv6 labeled unicast Address Family for this
             neighbor.";
        }
        uses neigh-ip-ucast-mcast-lbl-state;
        uses neigh-afi-safi-state;
      }

      container ipv6-flowspec {
        presence "Enable IPv6 Flowspec Address Family for this neighbor";
        description
          "IPv6 Flowspec address-family associated with the BGP neighbor.";

        leaf enabled {
          type boolean;
          default "true";
          description
            "Enable or disable IPv6 Flowspec Address Family for this neighbor.";
        }
        uses neigh-ip-flowspec-state;
        uses neigh-afi-safi-state;
      }

      container ipv6-vpn {
        presence "Enable IPv6 VPN Address Family for this neighbor";
        description
          "IPv6 VPN address-family associated with the BGP neighbor.";

        leaf enabled {
          type boolean;
          default "true";
          description
            "Enable or disable IPv6 VPN Address Family for this neighbor.";
        }
        uses neigh-ip-vpn-conf;
        uses neigh-afi-safi-state;
      }

      container l2vpn-evpn {
        presence "Enable L2VPN EVPN address family for this neighbor";
        description
          "L2VPN EVPN address family associated with the BGP neighbor.";

        leaf enabled {
          type boolean;
          default "true";
          description
            "Enable or disable L2VPN EVPN Address Family for this neighbor.";
        }
        uses neigh-l2vpn-evpn-conf {

          augment "route-map" {
            description
              "Add route-map-name option to route-map list.";

            leaf route-map-name {
              type string;
              description
                "Route-map name.";
            }
          }
        }
        uses neigh-afi-safi-state;
      }
    }
  }

  grouping global-afi-safi-state {
    description
      "AFI-SAFI state.";

    leaf rib-count {
      type uint32;
      description
        "Routing information base table count.";
    }

    leaf neighbor-count {
      type uint32;
      description
        "Number of neighbors for this address family.";
    }

    leaf dynamic-neighbor-count {
      type uint32;
      description
        "Number of dynamic neighbors for this address family.";
    }
  }

  grouping global-l2vpn-evpn-state {
    description
      "Global L2VPN EVPN AF state.";

    list route-distinguisher {
      key "rd";
      description
        "Route distinguisher operational state.";

      leaf rd {
        type route-distinguisher;
        description
          "Route distinguisher identifier.";
      }

      list prefix {
        key "prefix";
        description
          "Route distinguisher prefix list.";

        leaf prefix {
          type string;
          description
            "EVPN prefixes:
             EVPN type-2 prefix: [2]:[EthTag]:[MAClen]:[MAC]
                                 [2]:[EthTag]:[MAClen]:[MAC]:[IPLen]:[Ip]
             EVPN type-3 prefix: [3]:[EthTag]:[IPlen]:[OrigIP]
             EVPN type-5 prefix: [5]:[EthTag]:[IPlen]:[IP].";
        }

        leaf prefix-length {
          type uint16;
          description
            "Route prefix length.";
        }

        list path {
          key "id";
          description
            "Path list.";

          leaf id {
            type uint32 {
              range "1..**********";
            }
            description
              "Path internal identifier.";
          }

          uses global-route-af-state;
        }
      }
    }
  }

  grouping global-address-family-state {
    description
      "Address family state.";

    container address-family {
      description
        "Address-families associated with the BGP configuration.";

      container ipv4-unicast {
        presence "Configure IPv4 unicast address family global options";
        description
          "Configure IPv4 unicast address family.";

        list network {
          key "ip-prefix";
          description
            "Specify networks to announce via BGP.";

          leaf route-map {
            type string;
            description
              "Route-map name.";
          }
          uses global-network-af-ipv4-conf;
          uses global-route-af-ucast-mcast-vpn-state;
        }

        leaf table-map {
          type string;
          description
            "BGP table to RIB route download filter.";
        }
        uses global-ipv4-unicast-conf {

          augment "administrative-distance" {
            description
              "Add access-list option to administrative-distance list.";

            leaf access-list {
              type string;
              description
                "Access list name.";
            }
          }

          augment "redistribute" {
            description
              "Add route-map option to redistribute list.";

            leaf route-map {
              type string;
              description
                "Route-map name.";
            }
          }
        }
        uses global-afi-safi-state;
        uses route-flap-dampening;
      }

      container ipv4-multicast {
        presence "Configure IPv4 multicast Address Family global options";
        description
          "Configure IPv4 multicast address family.";

        list network {
          key "ip-prefix";
          description
            "Specify networks to announce via BGP.";

          leaf route-map {
            type string;
            description
              "Route-map name.";
          }
          uses global-network-af-ipv4-conf;
          uses global-route-af-ucast-mcast-vpn-state;
        }

        leaf table-map {
          type string;
          description
            "BGP table to RIB route download filter.";
        }
        uses global-ipv4-multicast-conf {

          augment "administrative-distance" {
            description
              "Add access-list option to administrative-distance list.";

            leaf access-list {
              type string;
              description
                "Access list name.";
            }
          }
        }
        uses global-afi-safi-state;
        uses route-flap-dampening;
      }

      container ipv4-labeled-unicast {
        presence "Configure IPv4 labeled unicast Address Family global options";
        description
          "Configure IPv4 labeled unicast address family.";
        uses global-ipv4-labeled-unicast-conf;
        uses global-afi-safi-state;
        uses route-flap-dampening;
      }

      container ipv4-flowspec {
        presence "Configure IPv4 Flowspec Address Family global options";
        description
          "Configure IPv4 Flowspec address family.";
        uses global-flowspec-conf;
        uses global-afi-safi-state;
        uses global-route-af-flowspec-state;
      }

      container ipv4-vpn {
        presence "Configure IPv4 VPN Address Family global options";
        description
          "Configure IPv4 VPN address family.";

        leaf enabled {
          type boolean;
          default "true";
          description
            "Enable or disable IPv4 VPN Address Family.";
        }

        list route-distinguisher {
          key "rd ip-prefix";
          description
            "Specify a network to announce via BGP.";

          leaf ip-prefix {
            type ntos-inet:ipv4-prefix;
            description
              "IPv4 prefix.";
          }
          uses global-rd-af-vpn-conf;
          uses global-route-af-ucast-mcast-vpn-state;
        }
        uses global-afi-safi-state;
      }

      container ipv6-unicast {
        presence "Configure IPv6 unicast Address Family global options";
        description
          "Configure IPv6 unicast address family.";

        list network {
          key "ip-prefix";
          description
            "Specify a network to announce via BGP.";

          leaf route-map {
            type string;
            description
              "Route-map name.";
          }
          uses global-network-af-ipv6-conf;
          uses global-route-af-ucast-mcast-vpn-state;
        }

        leaf table-map {
          type string;
          description
            "BGP table to RIB route download filter.";
        }
        uses global-ipv6-unicast-conf {

          augment "administrative-distance" {
            description
              "Add access-list option to administrative-distance list.";

            leaf access-list {
              type string;
              description
                "Access list name.";
            }
          }

          augment "redistribute" {
            description
              "Add route-map option to redistribute list.";

            leaf route-map {
              type string;
              description
                "Route-map name.";
            }
          }
        }
        uses global-afi-safi-state;
        uses route-flap-dampening;
      }

      container ipv6-multicast {
        presence "Configure IPv6 multicast Address Family global options";
        description
          "Configure IPv6 multicast address family.";

        list network {
          key "ip-prefix";
          description
            "Specify a network to announce via BGP.";

          leaf route-map {
            type string;
            description
              "Route-map name.";
          }
          uses global-network-af-ipv6-conf;
          uses global-route-af-ucast-mcast-vpn-state;
        }
        uses global-ipv6-multicast-conf {

          augment "administrative-distance" {
            description
              "Add access-list to administrative-distance list.";

            leaf access-list {
              type string;
              description
                "Access list name.";
            }
          }
        }
        uses global-afi-safi-state;
        uses route-flap-dampening;
      }

      container ipv6-labeled-unicast {
        presence "Configure IPv6 labeled unicast Address Family global options";
        description
          "Configure IPv6 labeled unicast address family.";
        uses global-ipv6-labeled-unicast-conf;
        uses global-afi-safi-state;
        uses route-flap-dampening;
      }

      container ipv6-flowspec {
        presence "Configure IPv6 Flowspec Address Family global options";
        description
          "Configure IPv6 Flowspec address family.";
        uses global-flowspec-conf;
        uses global-afi-safi-state;
        uses global-route-af-flowspec-state;
      }

      container ipv6-vpn {
        presence "Configure IPv6 VPN Address Family global options";
        description
          "Configure IPv6 VPN address family.";

        leaf enabled {
          type boolean;
          default "true";
          description
            "Enable or disable IPv6 VPN Address Family.";
        }

        list route-distinguisher {
          key "rd ip-prefix";
          description
            "Specify a network to announce via BGP.";

          leaf ip-prefix {
            type ntos-inet:ipv6-prefix;
            description
              "IPv6 prefix.";
          }
          uses global-rd-af-vpn-conf;
          uses global-route-af-ucast-mcast-vpn-state;
        }
        uses global-afi-safi-state;
      }

      container l2vpn-evpn {
        presence "Configure L2VPN EVPN address family global options";
        description
          "Configure L2VPN EVPN address family.";
        uses global-l2vpn-evpn-conf;
        uses global-l2vpn-evpn-state;
        uses global-afi-safi-state;
      }
    }
  }

  grouping bgp-state {
    description
      "BGP router instance state.";

    leaf enabled {
      type boolean;
      default "true";
      description
        "BGP router state.";
    }

    leaf as {
      type ntos-inet:as-number {
        range "1..**********";
      }
      mandatory true;
      description
        "BGP AS number.";
    }

    leaf neighbor-total-count {
      type uint32;
      description
        "Total number of neighbors.";
    }
    uses global-conf {

      augment "listen/neighbor-range" {
        description
          "Add neighbor-group to lister neighbor-range list.";

        leaf neighbor-group {
          type string;
          description
            "Neighbor group name.";
        }
      }
    }
    uses global-address-family-state;

    list neighbor-group {
      key "name";
      description
        "List of BGP peer-groups configured on the local system - uniquely
         identified by peer-group name.";
      uses neigh-group-base-conf;
      uses neigh-state;
      uses neigh-address-family-state;
    }

    list neighbor {
      key "neighbor-address";
      description
        "List of BGP neighbors configured on the local system,
         uniquely identified by peer IPv[46] address.";

      leaf neighbor-group {
        type string;
        description
          "Peer group name.";
      }
      uses neigh-base-conf;
      uses neigh-state;
      uses neigh-address-family-state;
    }
  }

  grouping rmap-bgp-match-conf {
    description
      "Route map BGP match configuration.";

    container community {
      presence "Makes community available";
      description
        "Match BGP community list.";
      ntos-extensions:nc-cli-one-liner;

      leaf id {
        type leafref {
          path
            "/ntos:config/ntos-rt:routing/ntos-bgp:bgp/ntos-bgp:community-list/ntos-bgp:name";
          require-instance false;
        }
        mandatory true;
        description
          "Community-list number or name.";
      }

      leaf exact-match {
        type boolean;
        description
          "If true, do exact matching of communities.";
      }
    }

    leaf extcommunity {
      type leafref {
        path
          "/ntos:config/ntos-rt:routing/ntos-bgp:bgp/ntos-bgp:extcommunity-list/ntos-bgp:name";
        require-instance false;
      }
      description
        "Match BGP/VPN extended community list.";
    }
  }

  grouping rmap-bgp-set-conf {
    description
      "Route map BGP set configuration.";

    leaf comm-list-delete {
      type leafref {
        path
          "/ntos:config/ntos-rt:routing/ntos-bgp:bgp/ntos-bgp:community-list/ntos-bgp:name";
        require-instance false;
      }
      description
        "Set BGP community list (for deletion).";
    }

    container community {
      // For backward compatibility, none and attribute leaf can be unset.
      must 'not(none and attribute)' {
        error-message "none or attribute must be set, not both.";
      }
      presence "Enable community.";
      description
        "BGP community attribute.";
      ntos-api:must-added "not(none and attribute)";

      leaf none {
        type empty;
        description
          "Set community to none.";
        ntos-extensions:nc-cli-group "community";
      }

      leaf-list attribute {
        type community-list;
        description
          "BGP community attribute.";
        ntos-extensions:nc-cli-group "community";
      }
    }

    container extcommunity {
      description
        "BGP extended community attribute.";

      leaf-list rt {
        type route-target;
        description
          "Route Target extended community.";
      }

      leaf-list soo {
        type route-target;
        description
          "Site-of-Origin extended community.";
      }
    }
  }

  // --- Common BGP routers configuration --- \\

  grouping common-bgp-conf {
    description
      "Common BGP routers configuration.";

    leaf route-map-delay {
      type uint16 {
        range "0..600";
      }
      default "5";
      description
        "Time in secs to wait before processing route-map changes.";
    }

    list community-list {
      key "name";
      description
        "Add a community list entry.";

      leaf name {
        type union {
          type uint8 {
            range "1..99";
          }
          type string;
        }
        description
          "List name.";
      }

      list policy {
        key "priority";
        min-elements 1;
        description
          "Specify communities to reject or accept.";
        ntos-extensions:nc-cli-one-liner;

        leaf priority {
          type uint16;
          description
            "Priority of the policy. Lesser is the value, greater is the priority.";
        }

        leaf policy {
          type enumeration {
            enum deny {
              description
                "Specified communities will be rejected.";
            }
            enum permit {
              description
                "Specified communities will be accepted.";
            }
          }
          mandatory true;
          description
            "Policy to apply to the specified communities.";
          ntos-extensions:nc-cli-no-name;
        }

        leaf-list community {
          type community-list;
          min-elements 1;
          description
            "Communities on which the policy should be applied.";
          ntos-extensions:nc-cli-no-name;
        }
      }
    }

    list community-list-expanded {
      key "name";
      description
        "Add an expanded community list entry.";

      leaf name {
        type union {
          type uint16 {
            range "100..500";
          }
          type string;
        }
        description
          "List name.";
      }

      list policy {
        key "priority";
        min-elements 1;
        description
          "Specify communities to reject or accept.";
        ntos-extensions:nc-cli-one-liner;

        leaf priority {
          type uint16;
          description
            "Priority of the policy. Lesser is the value, greater is the priority.";
        }

        leaf policy {
          type enumeration {
            enum deny {
              description
                "Specified communities will be rejected.";
            }
            enum permit {
              description
                "Specified communities will be accepted.";
            }
          }
          mandatory true;
          description
            "Policy to apply to the specified communities.";
          ntos-extensions:nc-cli-no-name;
        }

        leaf-list community {
          type community-list-expanded;
          min-elements 1;
          description
            "Communities on which the policy should be applied.";
          ntos-extensions:nc-cli-no-name;
        }
      }
    }

    list extcommunity-list {
      key "name";
      description
        "Add an extended community list entry.";

      leaf name {
        type union {
          type uint8 {
            range "1..99";
          }
          type string;
        }
        description
          "List name.";
      }

      list policy {
        must 'rt or soo' {
          error-message "At lest one rt or soo element must be set.";
        }
        key "priority";
        min-elements 1;
        description
          "Specify extended communities to reject or accept.";
        ntos-extensions:nc-cli-one-liner;

        leaf priority {
          type uint16;
          description
            "Priority of the policy. Lesser is the value, greater is the
             priority.";
        }

        leaf policy {
          type enumeration {
            enum deny {
              description
                "Specified extcommunities will be reject.";
            }
            enum permit {
              description
                "Specified extcommunities will be accept.";
            }
          }
          mandatory true;
          description
            "Policy to apply to the specified extcommunities.";
          ntos-extensions:nc-cli-no-name;
        }

        leaf-list rt {
          type route-target;
          description
            "Extended community route target to reject.";
        }

        leaf-list soo {
          type route-target;
          description
            "Extended community site of origin to reject.";
        }
      }
    }

    list extcommunity-list-expanded {
      key "name";
      description
        "Add an expanded extended community list entry.";

      leaf name {
        type union {
          type uint16 {
            range "100..500";
          }
          type string;
        }
        description
          "List name.";
      }

      list policy {
        key "priority";
        min-elements 1;
        description
          "Specify extended communities to reject or accept.";
        ntos-extensions:nc-cli-one-liner;

        leaf priority {
          type uint16;
          description
            "Priority of the policy. Lesser is the value, greater is the
             priority.";
        }

        leaf policy {
          type enumeration {
            enum deny {
              description
                "Specified extcommunities will be reject.";
            }
            enum permit {
              description
                "Specified extcommunities will be accept.";
            }
          }
          mandatory true;
          description
            "Policy to apply to the specified extcommunities.";
          ntos-extensions:nc-cli-no-name;
        }

        leaf-list extcommunity {
          type string;
          min-elements 1;
          description
            "Communities on which the policy should be applied.";
          ntos-extensions:nc-cli-no-name;
        }
      }
    }

    list as-path-access-list {
      key "name";
      description
        "BGP autonomous system path filter.";

      leaf name {
        type string;
        description
          "Access list name.";
      }

      list policy {
        key "priority";
        min-elements 1;
        description
          "Specify AS path access list to reject or accept.";
        ntos-extensions:nc-cli-one-liner;

        leaf priority {
          type uint16;
          description
            "Priority of the policy. Lesser is the value, greater is the priority.";
        }

        leaf policy {
          type enumeration {
            enum deny {
              description
                "Specified access list will be rejected.";
            }
            enum permit {
              description
                "Specified access list will be accepted.";
            }
          }
          mandatory true;
          description
            "Policy to apply to the specified regular expression that match AS
             paths.";
          ntos-extensions:nc-cli-no-name;
        }

        leaf-list access-list {
          type string;
          min-elements 1;
          description
            "Regular expression to match the BGP AS paths on which the policy
             should be applied.";
          ntos-extensions:nc-cli-no-name;
        }
      }
    }
  }

  grouping common-bgp-logging-conf {
    description
      "BGP logging configuration.";

    leaf enabled {
      type boolean;
      default "true";
      description
        "Enable/disable BGP logging configuration.";
    }

    leaf allow-martians {
      type boolean;
      default "false";
      description
        "Allow martian next hops.";
    }

    leaf as-4bytes {
      type boolean;
      default "false";
      description
        "Log AS > 65535 actions.";
    }

    leaf as-4bytes-segment {
      type boolean;
      default "false";
      description
        "Log AS > 65535 aspath segment handling.";
    }

    leaf-list bestpath {
      type ntos-inet:ip-prefix;
      description
        "Log BGP bestpath info.";
    }

    leaf nexthop-tracking {
      type boolean;
      default "false";
      description
        "Log BGP nexthop tracking.";
    }

    leaf flowspec {
      type boolean;
      default "false";
      description
        "Enable flowspec debugging entries.";
    }

    leaf-list keepalives {
      type union {
        type ntos-inet:ip-address;
        type enumeration {
          enum all {
            description
              "Log all keepalive messages.";
          }
        }
      }
      must "(current() = 'all' and count(../keepalives) = 1) or not(current() = 'all')" {
        error-message "Messages from all neighbors are already logged.";
      }
      description
        "Log keepalive messages to/from a specific neighbor or all.";
      ntos-extensions:nc-cli-completion-xpath
        "/ntos:config/ntos:vrf/ntos-routing:routing/ntos-bgp:bgp/neighbor/neighbor-address";
    }

    leaf-list neighbor-events {
      type union {
        type ntos-inet:ip-address;
        type enumeration {
          enum all {
            description
              "Log all neighbor event messages.";
          }
        }
      }
      must "(current() = 'all' and count(../neighbor-events) = 1) or not(current() = 'all')" {
        error-message "Messages from all neighbors are already logged.";
      }
      description
        "Log neighbor event messages to/from a specific neighbor or all.";
      ntos-extensions:nc-cli-completion-xpath
        "/ntos:config/ntos:vrf/ntos-routing:routing/ntos-bgp:bgp/neighbor/neighbor-address";
    }

    container pbr {
      presence "Log policy base routing info.";
      description
        "Log policy base routing info.";

      leaf detail {
        type boolean;
        default "false";
        description
          "Log policy base routing info with more details.";
      }
    }

    leaf update-groups {
      type boolean;
      default "false";
      description
        "Log update messages (only when BGP is configured as a server).";
    }

    container updates {
      must "(in = 'all' and count(in) = 1) or not(in = 'all')" {
        error-message "Update messages from all neighbors are already logged.";
      }
      must "(out = 'all' and count(out) = 1) or not(out = 'all')" {
        error-message "Update messages to all neighbors are already logged.";
      }
      presence "Make update messages configurable.";
      description
        "Log inbound and outbound update messages.";

      leaf enabled {
        type boolean;
        default "true";
        description
          "Enable/Disable log about inbound and outbound update messages.";
      }

      leaf-list in {
        type union {
          type ntos-inet:ip-address;
          type enumeration {
            enum all {
              description
                "Log inbound update messages from all neighbors.";
            }
          }
        }
        default "all";
        description
          "Log inbound update messages from a specific neighbor or all.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:config/ntos:vrf/ntos-routing:routing/ntos-bgp:bgp/neighbor/neighbor-address";
      }

      leaf-list out {
        type union {
          type ntos-inet:ip-address;
          type enumeration {
            enum all {
              description
                "Log outbound update messages from all neighbors.";
            }
          }
        }
        default "all";
        description
          "Log outbound update messages from a specific neighbor or all.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:config/ntos:vrf/ntos-routing:routing/ntos-bgp:bgp/neighbor/neighbor-address";
      }

      leaf-list prefix {
        type ntos-inet:ip-prefix;
        description
          "Log update messages to/from a specific network.";
      }
    }

    container vpn {
      description
        "Log VPN routes.";

      leaf label {
        type boolean;
        default "false";
        description
          "Log VPN label.";
      }

      leaf leak-vrf {
        type enumeration {
          enum to {
            description
              "Log leak to VRF from VPN.";
          }
          enum from {
            description
              "Log leak from VRF to VPN.";
          }
          enum both {
            description
              "Log all leaks.";
          }
        }
        description
          "Log leaks.";
      }

      leaf route-map-event {
        type boolean;
        default "false";
        description
          "Log VPN route-map updates.";
      }
    }

    leaf-list zebra {
      type union {
        type ntos-inet:ip-prefix;
        type enumeration {
          enum all {
            description
              "Log all messages between Zebra and BGP.";
          }
        }
      }
      must "(../zebra = 'all' and count(../zebra) = 1) or not(../zebra = 'all')" {
        error-message "All zebra messages are already logged.";
      }
      description
        "Log zebra/BGP messages for a specific prefix or all.";
    }
  }

  // -- BGP RPC -- \\

  grouping show-bgp-ipv4-ip {
    description
      "Show bgp ipv4 for an IP address.";

    list ip {
      key "value";
      max-elements 1;
      description
        "Display this address in the BGP routing table.";
      ntos-extensions:nc-cli-group "ipv4-subcommand";

      leaf value {
        type ntos-inet:ipv4-address;
        description
          "Display this address in the BGP routing table.";
        ntos-extensions:nc-cli-no-name;
      }

      leaf bestpath {
        type empty;
        description
          "Display only the best path.";
        ntos-extensions:nc-cli-group "ipv4-ip-subcommand";
      }

      leaf multipath {
        type empty;
        description
          "Display only multipaths.";
        ntos-extensions:nc-cli-group "ipv4-ip-subcommand";
      }
    }
  }

  grouping show-bgp-ipv4-prefix {
    description
      "Show bgp ipv4 for an IP prefix.";

    list prefix {
      key "value";
      max-elements 1;
      description
        "Display this prefix in the BGP routing table.";
      ntos-extensions:nc-cli-group "ipv4-subcommand";

      leaf value {
        type ntos-inet:ipv4-prefix;
        description
          "Display this prefix in the BGP routing table.";
      }

      leaf bestpath {
        type empty;
        description
          "Display only the best path.";
        ntos-extensions:nc-cli-group "ipv4-prefix-subcommand";
      }

      leaf multipath {
        type empty;
        description
          "Display only multipaths.";
        ntos-extensions:nc-cli-group "ipv4-prefix-subcommand";
      }

      leaf longer-prefixes {
        type empty;
        description
          "Display route and more specific routes.";
        ntos-extensions:nc-cli-group "ipv4-prefix-subcommand";
      }
    }
  }

  grouping show-bgp-ipv6-ip {
    description
      "Show bgp ipv6 for an IP address.";

    list ip {
      key "value";
      max-elements 1;
      description
        "Display this address in the BGP routing table.";
      ntos-extensions:nc-cli-group "ipv6-subcommand";

      leaf value {
        type ntos-inet:ipv6-address;
        description
          "Display this address in the BGP routing table.";
      }

      leaf bestpath {
        type empty;
        description
          "Display only the best path.";
        ntos-extensions:nc-cli-group "ipv6-ip-subcommand";
      }

      leaf multipath {
        type empty;
        description
          "Display only multipaths.";
        ntos-extensions:nc-cli-group "ipv6-ip-subcommand";
      }
    }
  }

  grouping show-bgp-ipv6-prefix {
    description
      "Show bgp ipv6 for an IP prefix.";

    list prefix {
      key "value";
      max-elements 1;
      description
        "Display this prefix in the BGP routing table.";
      ntos-extensions:nc-cli-group "ipv6-subcommand";

      leaf value {
        type ntos-inet:ipv6-prefix;
        description
          "Display this prefix in the BGP routing table.";
        ntos-extensions:nc-cli-no-name;
      }

      leaf bestpath {
        type empty;
        description
          "Display only the best path.";
        ntos-extensions:nc-cli-group "ipv6-prefix-subcommand";
      }

      leaf multipath {
        type empty;
        description
          "Display only multipaths.";
        ntos-extensions:nc-cli-group "ipv6-prefix-subcommand";
      }

      leaf longer-prefixes {
        type empty;
        description
          "Display route and more specific routes.";
        ntos-extensions:nc-cli-group "ipv6-prefix-subcommand";
      }
    }
  }

  grouping show-bgp-ip-flowspec {
    description
      "Show bgp ipv* flowspec.";

    leaf detail {
      type empty;
      description
        "Display detailed information on flowspec entries.";
      ntos-extensions:nc-cli-group "ipv4-subcommand";
      ntos-extensions:nc-cli-group "ipv6-subcommand";
    }
  }

  grouping show-bgp-ip-neighbor {
    description
      "Detailed information on BGP neighbor.";

    leaf prefix-counts {
      type empty;
      description
        "Display detailed prefix count information.";
      ntos-extensions:nc-cli-group "neighbor-filter";
    }

    container received {
      description
        "Display information received from a BGP neighbor.";
      ntos-extensions:nc-cli-group "neighbor-filter";

      leaf prefix-filter {
        type empty;
        description
          "Display the prefixlist filter.";
      }
    }
    uses show-bgp-ip-neighbor-common;
  }

  grouping show-bgp-ip-neighbor-common {
    description
      "Detailed information on BGP neighbor.";

    leaf advertised-routes {
      type empty;
      description
        "Display the routes advertised to a BGP neighbor.";
      ntos-extensions:nc-cli-group "neighbor-filter";
    }

    leaf dampened-routes {
      type empty;
      description
        "Display the dampened routes received from neighbor.";
      ntos-extensions:nc-cli-group "neighbor-filter";
    }

    leaf filtered-routes {
      type empty;
      description
        "Display the filtered routes received from neighbor.";
      ntos-extensions:nc-cli-group "neighbor-filter";
    }

    leaf flap-statistics {
      type empty;
      description
        "Display the flap statistics of the routes learned from neighbor.";
      ntos-extensions:nc-cli-group "neighbor-filter";
    }

    leaf received-routes {
      type empty;
      description
        "Display the received routes from neighbor.";
      ntos-extensions:nc-cli-group "neighbor-filter";
    }

    leaf routes {
      type empty;
      description
        "Display routes learned from neighbor.";
      ntos-extensions:nc-cli-group "neighbor-filter";
    }
  }

  grouping show-bgp-ip-mcast-vpn-neighbor {
    description
      "Detailed information on BGP neighbor.";

    leaf prefix-counts {
      type empty;
      description
        "Display detailed prefix count information.";
      ntos-extensions:nc-cli-group "neighbor-filter";
    }
    uses show-bgp-ip-neighbor-common;
  }

  grouping show-bgp-route-map {
    description
      "Show bgp route-map.";

    leaf route-map {
      type string;
      description
        "Display information about this route map.";
      ntos-extensions:nc-cli-group "subcommand";
      ntos-extensions:nc-cli-group "ipv4-subcommand";
      ntos-extensions:nc-cli-group "ipv6-subcommand";
      ntos-extensions:nc-cli-completion-xpath
        "/ntos:state/ntos-routing:routing/ntos-routing:route-map/name";
    }
  }

  grouping show-bgp-ip-common {
    description
      "Show bgp ipv*.";

    leaf cidr-only {
      type empty;
      description
        "Display only routes with non-natural netmask.";
      ntos-extensions:nc-cli-group "ipv4-subcommand";
      ntos-extensions:nc-cli-group "ipv6-subcommand";
    }

    leaf statistics {
      type empty;
      description
        "Display BGP RIB advertisement statistics.";
      ntos-extensions:nc-cli-group "ipv4-subcommand";
      ntos-extensions:nc-cli-group "ipv6-subcommand";
    }

    leaf summary {
      type empty;
      description
        "Display summary of BGP neighbor status.";
      ntos-extensions:nc-cli-group "ipv4-subcommand";
      ntos-extensions:nc-cli-group "ipv6-subcommand";
    }
    uses show-bgp-route-map;
  }

  grouping flush-bgp-common {
    description
      "Flush bgp ipv4.";

    leaf as {
      type ntos-inet:as-number {
        range "1..**********";
      }
      description
        "Flush neighbors with the AS number.";
      ntos-extensions:nc-cli-group "ip-subcommand";
      // First xpath: select neighbors remote-as that have the current afi/safi
      // enabled.
      // Second xpath: select neighbors remote-as which are part of a neighbor
      // group and with the current afi/safi not disabled and enabled in their
      // neighbor group.
      // Third xpath: select neighbor groups remote-as with the current afi/safi
      // enabled.
      // Fourth xpath: select neighbor groups remote-as with a neighbor with the
      // current afi/safi enabled.
      ntos-extensions:nc-cli-completion-xpath
        "/ntos:state/ntos:vrf[ntos:name=string(current()/../../ntos-bgp:vrf) or (
           string(current()/../../ntos-bgp:vrf)='' and ntos:name='main'
         )]/ntos-routing:routing/ntos-bgp:bgp/ntos-bgp:neighbor[
           ntos-bgp:address-family/*[local-name()=concat(
             local-name(current()/..), '-', local-name(current())
           )]/ntos-bgp:enabled='true'
         ]/ntos-bgp:remote-as |
         /ntos:state/ntos:vrf[ntos:name=string(current()/../../ntos-bgp:vrf) or (
           string(current()/../../ntos-bgp:vrf)='' and ntos:name='main'
         )]/ntos-routing:routing/ntos-bgp:bgp/ntos-bgp:neighbor[
           ntos-bgp:neighbor-group=/ntos:state/ntos:vrf[
             ntos:name=string(current()/../../ntos-bgp:vrf) or
             (string(current()/../../ntos-bgp:vrf)='' and ntos:name='main')
           ]/ntos-routing:routing/ntos-bgp:bgp/ntos-bgp:neighbor-group[
             ntos-bgp:address-family/*[local-name()=concat(
               local-name(current()/..), '-', local-name(current())
             )]/ntos-bgp:enabled='true'
           ]/ntos-bgp:name and (ntos-bgp:address-family/*[local-name()=concat(
             local-name(current()/..), '-', local-name(current())
           )]/ntos-bgp:enabled!='false' or not(ntos-bgp:address-family/*[
             local-name()=concat(local-name(current()/..), '-', local-name(current()))
           ]/ntos-bgp:enabled))
         ]/ntos-bgp:remote-as |
         /ntos:state/ntos:vrf[ntos:name=string(current()/../../ntos-bgp:vrf) or (
           string(current()/../../ntos-bgp:vrf)='' and ntos:name='main'
         )]/ntos-routing:routing/ntos-bgp:bgp/ntos-bgp:neighbor-group[
           ntos-bgp:address-family/*[local-name()=concat(
             local-name(current()/..), '-', local-name(current())
           )]/ntos-bgp:enabled='true'
         ]/ntos-bgp:remote-as |
         /ntos:state/ntos:vrf[ntos:name=string(current()/../../ntos-bgp:vrf) or (
           string(current()/../../ntos-bgp:vrf)='' and ntos:name='main'
         )]/ntos-routing:routing/ntos-bgp:bgp/ntos-bgp:neighbor-group[
           name=/ntos:state/ntos:vrf[
             ntos:name=string(current()/../../ntos-bgp:vrf) or
             (string(current()/../../ntos-bgp:vrf)='' and ntos:name='main')
           ]/ntos-routing:routing/ntos-bgp:bgp/ntos-bgp:neighbor[
             ntos-bgp:address-family/*[local-name()=concat(
               local-name(current()/..), '-', local-name(current())
             )]/ntos-bgp:enabled='true'
           ]/neighbor-group
         ]/ntos-bgp:remote-as";
    }

    leaf all {
      type empty;
      description
        "Flush all neighbors.";
      ntos-extensions:nc-cli-group "ip-subcommand";
    }

    leaf neighbor {
      type ntos-inet:ip-address;
      description
        "BGP neighbor address to flush.";
      ntos-extensions:nc-cli-group "ip-subcommand";
      // First xpath: select neighbors that have the current afi/safi enabled.
      // Second xpath: select neighbors which are part of a neighbor group and
      // with the current afi/safi not disabled and enabled in their neighbor
      // group.
      ntos-extensions:nc-cli-completion-xpath
        "/ntos:state/ntos:vrf[ntos:name=string(current()/../../ntos-bgp:vrf) or (
           string(current()/../../ntos-bgp:vrf)='' and ntos:name='main'
         )]/ntos-routing:routing/ntos-bgp:bgp/ntos-bgp:neighbor[
           ntos-bgp:address-family/*[local-name()=concat(
             local-name(current()/..), '-', local-name(current())
           )]/ntos-bgp:enabled='true'
         ]/ntos-bgp:neighbor-address |
         /ntos:state/ntos:vrf[ntos:name=string(current()/../../ntos-bgp:vrf) or (
           string(current()/../../ntos-bgp:vrf)='' and ntos:name='main'
         )]/ntos-routing:routing/ntos-bgp:bgp/ntos-bgp:neighbor[
           ntos-bgp:neighbor-group=/ntos:state/ntos:vrf[
             ntos:name=string(current()/../../ntos-bgp:vrf) or
             (string(current()/../../ntos-bgp:vrf)='' and ntos:name='main')
           ]/ntos-routing:routing/ntos-bgp:bgp/ntos-bgp:neighbor-group[
             ntos-bgp:address-family/*[local-name()=concat(
               local-name(current()/..), '-', local-name(current())
             )]/ntos-bgp:enabled='true'
           ]/ntos-bgp:name and (ntos-bgp:address-family/*[
             local-name()=concat(local-name(current()/..), '-', local-name(current()))
           ]/ntos-bgp:enabled!='false' or not(ntos-bgp:address-family/*[
             local-name()=concat(local-name(current()/..), '-', local-name(current()))
           ]/ntos-bgp:enabled))
         ]/ntos-bgp:neighbor-address";
    }

    leaf external {
      type empty;
      description
        "Flush all external neighbors.";
      ntos-extensions:nc-cli-group "ip-subcommand";
    }

    leaf neighbor-group {
      type string;
      description
        "Flush all members of the neighbor group.";
      ntos-extensions:nc-cli-group "ip-subcommand";
      ntos-extensions:nc-cli-completion-xpath
        "/ntos:state/ntos:vrf[ntos:name=string(current()/../../ntos-bgp:vrf) or (
           string(current()/../../ntos-bgp:vrf)='' and ntos:name='main'
         )]/ntos-routing:routing/ntos-bgp:bgp/ntos-bgp:neighbor-group[
           ntos-bgp:address-family/*[local-name()=concat(
             local-name(current()/..), '-', local-name(current())
           )]/ntos-bgp:enabled='true'
         ]/ntos-bgp:name";
    }

    leaf soft {
      type enumeration {
        enum in {
          description
            "Send route-refresh unless using 'soft-reconfiguration inbound'.";
        }
        enum out {
          description
            "Resend all outbound updates.";
        }
        enum both {
          description
            "Soft reconfigure inbound and outbound updates.";
        }
      }
      description
        "Soft reconfigure inbound and/or outbound updates.";
    }
  }

  grouping flush-bgp-safi {
    description
      "BPG flush IPv4/IPv6 common SAFI options.";

    container unicast {
      presence "Enable unicast.";
      description
        "Flush information for unicast address family.";
      uses flush-bgp-common;
    }

    container multicast {
      presence "Enable multicast.";
      description
        "Flush information for multicast address family.";
      uses flush-bgp-common;
    }

    container labeled-unicast {
      presence "Enable labeled-unicast.";
      description
        "Flush information for labeled unicast address family.";
      uses flush-bgp-common;
    }

    container flowspec {
      presence "Enable flowspec.";
      description
        "Flush information for flowspec address family.";
      uses flush-bgp-common;
    }

    container vpn {
      presence "Enable vpn.";
      description
        "Flush information for VPN address family.";
      uses flush-bgp-common;
    }
  }

  rpc show-bgp {
    description
      "Show BGP information.";
    input {
      // PBR

      container pbr {
        description
          "Display information about PBR configured by BGP.";
        ntos-extensions:nc-cli-group "subcommand";

        container ipset {
          presence "Enable ipset.";
          description
            "Display information about PBR IPSETs configured by BGP.";
          ntos-extensions:nc-cli-group "pbr-subcommand";

          leaf set {
            type string;
            description
              "Display information about this set.";
          }
        }

        container iptable {
          presence "Enable iptable.";
          description
            "Display information about PBR IPTables chainsa configured by BGP.";
          ntos-extensions:nc-cli-group "pbr-subcommand";

          leaf chain {
            type string;
            description
              "Display information about this chain.";
          }
        }
      }
      // BGP

      leaf vrf {
        type string;
        description
          "Specify the VRF.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
        ntos-extensions:nc-cli-group "vrf-command";
      }

      leaf vrfs {
        type empty;
        description
          "Show BGP VRFs.";
        ntos-extensions:nc-cli-group "vrf-command";
        ntos-extensions:nc-cli-group "subcommand";
      }

      leaf summary {
        type empty;
        description
          "Summary of BGP neighbor status.";
        ntos-extensions:nc-cli-group "subcommand";
      }

      leaf neighbors {
        type empty;
        description
          "Display information about all BGP neighbors.";
        ntos-extensions:nc-cli-group "subcommand";
      }

      list neighbor {
        /* XXX: We need to remove this and accept "neighbor" */
        key "id";
        max-elements 1;
        description
          "Display information about one BGP neighbor.";
        ntos-extensions:nc-cli-group "subcommand";

        leaf id {
          type union {
            type ntos-inet:ip-address;
            type string;
          }
          description
            "Display information about one BGP neighbor.";
          ntos-extensions:nc-cli-completion-xpath
            "/ntos:state/ntos:vrf[ntos:name=string(current()/ntos-bgp:vrf) or (
               string(current()/ntos-bgp:vrf)='' and ntos:name='main'
             )]/ntos-routing:routing/ntos-bgp:bgp/ntos-bgp:neighbor/ntos-bgp:neighbor-address";
        }
      }

      container community-list {
        presence "Enable community-list.";
        description
          "Display information about BGP community lists (standard and expanded).";
        ntos-extensions:nc-cli-group "subcommand";
        ntos-extensions:nc-cli-group "vrf-command";
      }

      container extcommunity-list {
        presence "Enable extcommunity-list.";
        description
          "Display information about BGP extcommunity lists (standard and expanded).";
        ntos-extensions:nc-cli-group "subcommand";
        ntos-extensions:nc-cli-group "vrf-command";
      }

      container as-path-access-list {
        presence "Enable as-path-access-list.";
        description
          "Display information about AS-path access lists.";
        ntos-extensions:nc-cli-group "subcommand";
        ntos-extensions:nc-cli-group "vrf-command";

        leaf name {
          type string;
          description
            "Display information about a certain AS-Path access list.";
          ntos-extensions:nc-cli-completion-xpath
            "/ntos:state/ntos-routing:routing/ntos-bgp:bgp/ntos-bgp:as-path-access-list/ntos-bgp:name";
        }
      }
      uses show-bgp-route-map;

      container ipv4 {
        presence "Enable ipv4.";
        description
          "Display information about BGP IPv4.";
        ntos-extensions:nc-cli-group "subcommand";
        ntos-extensions:nc-cli-exclusive;
        uses show-bgp-ipv4-ip;
        uses show-bgp-ipv4-prefix;
        uses show-bgp-ip-common;

        container flowspec {
          presence "Enable flowspec.";
          description
            "Display information for flowspec address family.";
          uses show-bgp-ipv4-ip;
          uses show-bgp-ipv4-prefix;
          uses show-bgp-ip-flowspec;
          uses show-bgp-ip-common;
        }

        container unicast {
          presence "Enable unicast.";
          description
            "Display information for unicast address family.";

          list neighbor {
            key "id";
            max-elements 1;
            description
              "Display information about one BGP neighbor.";
            ntos-extensions:nc-cli-group "ipv4-subcommand";

            leaf id {
              type union {
                type ntos-inet:ipv4-address;
                type string;
              }
              description
                "Display information about one BGP neighbor.";
              ntos-extensions:nc-cli-completion-xpath
                "/ntos:state/ntos:vrf[ntos:name=string(current()/../ntos-bgp:vrf) or (
                   string(current()/../ntos-bgp:vrf)='' and ntos:name='main'
                 )]/ntos-routing:routing/ntos-bgp:bgp/ntos-bgp:neighbor[
                   ntos-bgp:address-family/ntos-bgp:ipv4-unicast/ntos-bgp:enabled='true'
                 ]/ntos-bgp:neighbor-address";
            }
            uses show-bgp-ip-neighbor;
          }
          uses show-bgp-ipv4-ip;
          uses show-bgp-ipv4-prefix;
          uses show-bgp-ip-common;
        }

        container multicast {
          presence "Enable multicast.";
          description
            "Display information for multicast address family.";

          list neighbor {
            key "id";
            max-elements 1;
            description
              "Display information about one BGP neighbor.";
            ntos-extensions:nc-cli-group "ipv4-subcommand";

            leaf id {
              type union {
                type ntos-inet:ipv4-address;
                type string;
              }
              description
                "Display information about one BGP neighbor.";
              ntos-extensions:nc-cli-completion-xpath
                "/ntos:state/ntos:vrf[ntos:name=string(current()/../ntos-bgp:vrf) or (
                   string(current()/../ntos-bgp:vrf)='' and ntos:name='main'
                 )]/ntos-routing:routing/ntos-bgp:bgp/ntos-bgp:neighbor[
                   ntos-bgp:address-family/ntos-bgp:ipv4-multicast/ntos-bgp:enabled='true'
                 ]/ntos-bgp:neighbor-address";
            }
            uses show-bgp-ip-mcast-vpn-neighbor;
          }
          uses show-bgp-ipv4-ip;
          uses show-bgp-ipv4-prefix;
          uses show-bgp-ip-common;
        }

        container labeled-unicast {
          presence "Enable labeled-unicast.";
          description
            "Display information for labeled unicast address family.";

          list neighbor {
            key "id";
            max-elements 1;
            description
              "Display information about one BGP neighbor.";
            ntos-extensions:nc-cli-group "ipv4-subcommand";

            leaf id {
              type union {
                type ntos-inet:ipv4-address;
                type string;
              }
              description
                "Display information about one BGP neighbor.";
              ntos-extensions:nc-cli-completion-xpath
                "/ntos:state/ntos:vrf[ntos:name=string(current()/../ntos-bgp:vrf) or (
                   string(current()/../ntos-bgp:vrf)='' and ntos:name='main'
                 )]/ntos-routing:routing/ntos-bgp:bgp/ntos-bgp:neighbor[
                   ntos-bgp:address-family/ntos-bgp:ipv4-labeled-unicast/ntos-bgp:enabled='true'
                 ]/ntos-bgp:neighbor-address";
            }
            uses show-bgp-ip-neighbor-common;
          }
          uses show-bgp-ipv4-ip;
          uses show-bgp-ipv4-prefix;
          uses show-bgp-ip-common;
        }

        container vpn {
          presence "Enable VPN.";
          description
            "Display information for VPN address family.";

          leaf route-distinguisher {
            type route-distinguisher;
            description
              "Display information for a route distinguisher.";
            ntos-extensions:nc-cli-group "ipv4-ip-subcommand";
            ntos-extensions:nc-cli-group "ipv4-neighbor-subcommand";
          }

          list neighbor {
            key "id";
            max-elements 1;
            description
              "Display information about one BGP neighbor.";
            ntos-extensions:nc-cli-group "ipv4-subcommand";
            ntos-extensions:nc-cli-group "ipv4-neighbor-subcommand";

            leaf id {
              type union {
                type ntos-inet:ipv4-address;
                type string;
              }
              description
                "Display information about one BGP neighbor.";
              ntos-extensions:nc-cli-completion-xpath
                "/ntos:state/ntos:vrf[ntos:name=string(current()/../ntos-bgp:vrf) or (
                   string(current()/../ntos-bgp:vrf)='' and ntos:name='main'
                 )]/ntos-routing:routing/ntos-bgp:bgp/ntos-bgp:neighbor[
                   ntos-bgp:address-family/ntos-bgp:ipv4-vpn/ntos-bgp:enabled='true'
                 ]/ntos-bgp:neighbor-address";
            }
            uses show-bgp-ip-mcast-vpn-neighbor;
          }
          uses show-bgp-ipv4-ip;
          uses show-bgp-ipv4-prefix;
          uses show-bgp-ip-common;
        }

        list neighbor {
          key "id";
          max-elements 1;
          description
            "Display information about one BGP neighbor.";

          leaf id {
            type union {
              type ntos-inet:ipv4-address;
              type string;
            }
            description
              "Display information about one BGP neighbor.";
            ntos-extensions:nc-cli-completion-xpath
              "/ntos:state/ntos:vrf[ntos:name=string(current()/../ntos-bgp:vrf) or (
                 string(current()/../ntos-bgp:vrf)='' and ntos:name='main'
               )]/ntos-routing:routing/ntos-bgp:bgp/ntos-bgp:neighbor[
                 ntos-bgp:address-family/*[starts-with(local-name(), 'ipv4-')]/ntos-bgp:enabled='true'
               ]/ntos-bgp:neighbor-address";
          }
          uses show-bgp-ip-neighbor;
        }

        leaf neighbors {
          type empty;
          description
            "Display information about all BGP neighbors.";
        }
      }

      container ipv6 {
        presence "Enable ipv6.";
        description
          "Display information about BGP IPv6.";
        ntos-extensions:nc-cli-group "subcommand";
        ntos-extensions:nc-cli-exclusive;
        uses show-bgp-ipv6-ip;
        uses show-bgp-ipv6-prefix;
        uses show-bgp-ip-common;

        container flowspec {
          presence "Enable flowspec.";
          description
            "Display information for flowspec address family.";
          uses show-bgp-ipv6-ip;
          uses show-bgp-ipv6-prefix;
          uses show-bgp-ip-flowspec;
          uses show-bgp-ip-common;
          ntos-extensions:nc-cli-group "safi";
        }

        container unicast {
          presence "Enable unicast.";
          description
            "Display information for unicast address family.";

          list neighbor {
            key "id";
            max-elements 1;
            description
              "Display information about one BGP neighbor.";
            ntos-extensions:nc-cli-group "ipv6-subcommand";

            leaf id {
              type union {
                type ntos-inet:ipv6-address;
                type string;
              }
              description
                "Display information about one BGP neighbor.";
              ntos-extensions:nc-cli-completion-xpath
                "/ntos:state/ntos:vrf[ntos:name=string(current()/../ntos-bgp:vrf) or (
                   string(current()/../ntos-bgp:vrf)='' and ntos:name='main'
                 )]/ntos-routing:routing/ntos-bgp:bgp/ntos-bgp:neighbor[
                   ntos-bgp:address-family/ntos-bgp:ipv6-unicast/ntos-bgp:enabled='true'
                 ]/ntos-bgp:neighbor-address";
            }
            uses show-bgp-ip-neighbor;
          }
          uses show-bgp-ipv6-ip;
          uses show-bgp-ipv6-prefix;
          uses show-bgp-ip-common;
          ntos-extensions:nc-cli-group "safi";
        }

        container multicast {
          presence "Enable multicast.";
          description
            "Display information for multicast address family.";

          list neighbor {
            key "id";
            max-elements 1;
            description
              "Display information about one BGP neighbor.";
            ntos-extensions:nc-cli-group "ipv6-subcommand";

            leaf id {
              type union {
                type ntos-inet:ipv6-address;
                type string;
              }
              description
                "Display information about one BGP neighbor.";
              ntos-extensions:nc-cli-completion-xpath
                "/ntos:state/ntos:vrf[ntos:name=string(current()/../ntos-bgp:vrf) or (
                   string(current()/../ntos-bgp:vrf)='' and ntos:name='main'
                 )]/ntos-routing:routing/ntos-bgp:bgp/ntos-bgp:neighbor[
                   ntos-bgp:address-family/ntos-bgp:ipv6-multicast/ntos-bgp:enabled='true'
                 ]/ntos-bgp:neighbor-address";
            }
            uses show-bgp-ip-mcast-vpn-neighbor;
          }
          uses show-bgp-ipv6-ip;
          uses show-bgp-ipv6-prefix;
          uses show-bgp-ip-common;
          ntos-extensions:nc-cli-group "safi";
        }

        container labeled-unicast {
          presence "Enable labeled-unicast.";
          description
            "Display information for labeled unicast address family.";

          list neighbor {
            key "id";
            max-elements 1;
            description
              "Display information about one BGP neighbor.";
            ntos-extensions:nc-cli-group "ipv6-subcommand";

            leaf id {
              type union {
                type ntos-inet:ipv6-address;
                type string;
              }
              description
                "Display information about one BGP neighbor.";
              ntos-extensions:nc-cli-completion-xpath
                "/ntos:state/ntos:vrf[ntos:name=string(current()/../ntos-bgp:vrf) or (
                   string(current()/../ntos-bgp:vrf)='' and ntos:name='main'
                 )]/ntos-routing:routing/ntos-bgp:bgp/ntos-bgp:neighbor[
                   ntos-bgp:address-family/ntos-bgp:ipv6-labeled-unicast/ntos-bgp:enabled='true'
                 ]/ntos-bgp:neighbor-address";
            }
            uses show-bgp-ip-neighbor-common;
          }
          uses show-bgp-ipv6-ip;
          uses show-bgp-ipv6-prefix;
          uses show-bgp-ip-common;
          ntos-extensions:nc-cli-group "safi";
        }

        container vpn {
          presence "Enable VPN.";
          description
            "Display information for VPN address family.";

          list neighbor {
            key "id";
            max-elements 1;
            description
              "Display information about one BGP neighbor.";
            ntos-extensions:nc-cli-group "ipv6-subcommand";

            leaf id {
              type union {
                type ntos-inet:ipv6-address;
                type string;
              }
              description
                "Display information about one BGP neighbor.";
              ntos-extensions:nc-cli-completion-xpath
                "/ntos:state/ntos:vrf[ntos:name=string(current()/../ntos-bgp:vrf) or (
                   string(current()/../ntos-bgp:vrf)='' and ntos:name='main'
                 )]/ntos-routing:routing/ntos-bgp:bgp/ntos-bgp:neighbor[
                   ntos-bgp:address-family/ntos-bgp:ipv6-vpn/ntos-bgp:enabled='true'
                 ]/ntos-bgp:neighbor-address";
            }
            uses show-bgp-ip-mcast-vpn-neighbor;
          }
          uses show-bgp-ipv6-ip;
          uses show-bgp-ipv6-prefix;
          uses show-bgp-ip-common;
          ntos-extensions:nc-cli-group "safi";
        }

        list neighbor {
          key "id";
          max-elements 1;
          description
            "Display information about one BGP neighbor.";
          ntos-extensions:nc-cli-group "ipv6-subcommand";

          leaf id {
            type union {
              type ntos-inet:ipv6-address;
              type string;
            }
            description
              "Display information about one BGP neighbor.";
            ntos-extensions:nc-cli-completion-xpath
              "/ntos:state/ntos:vrf[ntos:name=string(current()/../ntos-bgp:vrf) or (
                 string(current()/../ntos-bgp:vrf)='' and ntos:name='main'
               )]/ntos-routing:routing/ntos-bgp:bgp/ntos-bgp:neighbor[
                 ntos-bgp:address-family/*[starts-with(local-name(), 'ipv6-')]/ntos-bgp:enabled='true'
               ]/ntos-bgp:neighbor-address";
          }
          uses show-bgp-ip-neighbor;
        }

        leaf neighbors {
          type empty;
          description
            "Display information about all BGP neighbors.";
          ntos-extensions:nc-cli-group "ipv6-subcommand";
        }
      }

      container l2vpn {
        must 'evpn/summary or ../vrf = "main" or not(../vrf)' {
        error-message
          "Except for the summary option, this command must be executed only in
           the main VRF.";
        }
        presence "Enable L2VPN.";
        description
          "Display Layer 2 Virtual Private Network information.";
        ntos-extensions:nc-cli-group "subcommand";

        container evpn {
          presence "Enable L2VPN EVPN.";
          description
            "Display Ethernet Virtual Private Network information.";

          leaf vni {
            type ntos-vxlan:vni;
            description
              "Display VNI information.";
            ntos-extensions:nc-cli-group "l2vpn-rdcommand";
            ntos-extensions:nc-cli-group "l2vpn-neighcommand";
          }

          leaf net {
            type union {
              type ntos-inet:ipv4-address;
              type ntos-inet:ipv4-prefix;
            }
            description
              "Network in the BGP routing table to display.";
            ntos-extensions:nc-cli-no-name;
            ntos-extensions:nc-cli-group "l2vpn-rdcommand";
            ntos-extensions:nc-cli-group "l2vpn-neighcommand";
          }

          leaf summary {
            type empty;
            description
              "Summary of BGP neighbor status.";
            ntos-extensions:nc-cli-group "l2vpn-neighcommand";
            ntos-extensions:nc-cli-group "l2vpn-rdcommand";
          }

          leaf overlay {
            type empty;
            description
              "Display BGP Overlay Information for prefixes.";
            ntos-extensions:nc-cli-group "l2vpn-neighcommand";
          }

          leaf tags {
            type empty;
            description
              "Display BGP tags for prefixes.";
            ntos-extensions:nc-cli-group "l2vpn-neighcommand";
          }

          container neighbors {
            must 'advertised-routes or routes' {
              error-message "advertised-routes or routes is missing.";
            }
            presence "Enable L2VPN EVPN neighbor filter.";
            description
              "Detailed information on TCP and BGP neighbor connections.";
            ntos-extensions:nc-cli-group "l2vpn-neighcommand";

            leaf neighbor {
              type ntos-inet:ipv4-address;
              mandatory true;
              description
                "Neighbor to display information about.";
              ntos-extensions:nc-cli-no-name;
            }

            leaf advertised-routes {
              type empty;
              description
                "Display the routes advertised to a BGP neighbor.";
              ntos-extensions:nc-cli-group "l2vpn-neigh-subcommand";
            }

            leaf routes {
              type empty;
              description
                "Display routes learned from neighbor.";
              ntos-extensions:nc-cli-group "l2vpn-neigh-subcommand";
            }
          }

          leaf route-distinguisher {
            type route-distinguisher;
            description
              "Display information for a route distinguisher.";
            ntos-extensions:nc-cli-group "l2vpn-rdcommand";
          }

          container route {
            presence "Enable L2VPN EVPN route filter.";
            description
              "Detailed information BPG L2VPN EVPN routes.";
            ntos-extensions:nc-cli-group "l2vpn-neighcommand";
            ntos-extensions:nc-cli-group "l2vpn-rdcommand";

            leaf type {
              type enumeration {
                enum macip {
                  description
                    "MAC-IP (Type-2) route.";
                }
                enum multicast {
                  description
                    "Multicast (Type-3) route.";
                }
                enum prefix {
                  description
                    "Prefix (Type-5) route.";
                }
              }
              description
                "Specify route type.";
            }

            leaf detail {
              type empty;
              description
                "Display detail information.";
            }
          }
        }
      }
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-extensions:feature "product";
    ntos-extensions:nc-cli-show "bgp";
    ntos-api:internal;
  }

  rpc flush-bgp {
    description
      "Flush BGP information.";
    input {

      leaf vrf {
        type string;
        description
          "Specify the VRF.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      container ipv4 {
        must '*/as or */all or */neighbor or */external or */neighbor-group' {
          error-message "No neighbors selected.";
        }
        presence "Enable ipv4.";
        description
          "Flush information about BGP IPv4.";
        ntos-extensions:nc-cli-group "subcommand";
        ntos-extensions:nc-cli-exclusive;
        uses flush-bgp-safi;
      }

      container ipv6 {
        must '*/as or */all or */neighbor or */external or */neighbor-group' {
          error-message "No neighbors selected.";
        }
        presence "Enable ipv6.";
        description
          "Flush information about BGP IPv6.";
        ntos-extensions:nc-cli-group "subcommand";
        ntos-extensions:nc-cli-exclusive;
        uses flush-bgp-safi;
      }
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-extensions:feature "product";
    ntos-extensions:nc-cli-flush "bgp";
    ntos-api:internal;
  }

  augment "/ntos:config/ntos:vrf/ntos-rt:routing" {
    description
      "BGP router configuration.";

    leaf l3-vni {
      type ntos-vxlan:vni;
      must '../../ntos-iface:interface/ntos-vxlan:vxlan/ntos-vxlan:vni = current()' {
        error-message "Unknown VNI.";
      }
      must 'not(../bgp/address-family/l2vpn-evpn/vni[enabled="true"])' {
        error-message "AF l2VPN EVPN vni option cannot be configured when l3-vni is set.";
      }
      description
        "VNI corresponding to tenant VRF.";
      ntos-extensions:feature "product";
    }

    container bgp {
      presence "Make BGP router available";
      description
        "BGP router configuration.";
      ntos-extensions:feature "product";
      uses bgp-conf;
    }
  }

  augment "/ntos:state/ntos:vrf/ntos-rt:routing" {
    description
      "BGP router operational state data.";

    leaf l3-vni {
      type ntos-vxlan:vni;
      description
        "VNI corresponding to tenant VRF.";
    }

    container bgp {
      presence "Make BGP router available";
      description
        "BGP router operational state data.";
      ntos-extensions:feature "product";
      uses bgp-state;
    }
  }

  augment "/ntos:config/ntos-rt:routing" {
    description
      "Common BGP routers configuration.";

    container bgp {
      presence "Make BGP common configuration available";
      description
        "Common BGP routers configuration.";
      ntos-extensions:feature "product";
      uses common-bgp-conf;
      ext-cond:unique-values "community-list/name | community-list-expanded/name" {
        error-message "Community list names must be unique among standard and expanded lists.";
      }
      ext-cond:unique-values "extcommunity-list/name | extcommunity-list-expanded/name" {
        error-message "Extcommunity list names must be unique among standard and expanded lists.";
      }
      ntos-api:extension-added "extra-conditions:unique-values 'community-list/name | community-list-expanded/name'";
      ntos-api:extension-added "extra-conditions:unique-values 'extcommunity-list/name | extcommunity-list-expanded/name'";
    }
  }

  augment "/ntos:state/ntos-rt:routing" {
    description
      "Common BGP routers operational state.";

    container bgp {
      presence "Make BGP common configuration available";
      description
        "Operational state common to all BGP routers.";
      ntos-extensions:feature "product";
      uses common-bgp-conf;
    }
  }

  augment "/ntos:config/ntos-rt:routing/ntos-rt:route-map/ntos-rt:seq/ntos-rt:match" {
    description
      "BGP route map match configuration.";
    uses rmap-bgp-match-conf;
  }

  augment "/ntos:state/ntos-rt:routing/ntos-rt:route-map/ntos-rt:seq/ntos-rt:match" {
    description
      "BGP route map match state.";
    uses rmap-bgp-match-conf;
  }

  augment "/ntos:config/ntos-rt:routing/ntos-rt:route-map/ntos-rt:seq/ntos-rt:match/ntos-rt:evpn" {
    description
      "BGP route map match EVPN configuration.";

    leaf route-distinguisher {
      type route-distinguisher;
      description
        "Route distinguisher.";
    }
  }

  augment "/ntos:state/ntos-rt:routing/ntos-rt:route-map/ntos-rt:seq/ntos-rt:match/ntos-rt:evpn" {
    description
      "BGP route map match EVPN configuration.";

    leaf route-distinguisher {
      type route-distinguisher;
      description
        "Route distinguisher.";
    }
  }

  augment "/ntos:config/ntos-rt:routing/ntos-rt:route-map/ntos-rt:seq/ntos-rt:set" {
    description
      "BGP route map set configuration.";
    uses rmap-bgp-set-conf;
  }

  augment "/ntos:state/ntos-rt:routing/ntos-rt:route-map/ntos-rt:seq/ntos-rt:set" {
    description
      "BGP route map set state.";
    uses rmap-bgp-set-conf;
  }

  augment "/ntos:config/ntos-rt:routing/ntos-rt:logging" {
    description
      "Common BGP routers logging configuration.";

    container bgp {
      presence "Make BGP common logging configuration available.";
      description
        "Common BGP routers logging configuration.";
      ntos-extensions:feature "product";
      uses common-bgp-logging-conf;
    }
  }

  augment "/ntos:state/ntos-rt:routing/ntos-rt:logging" {
    description
      "Common BGP routers logging operational state.";

    container bgp {
      presence "Make BGP common logging state available.";
      description
        "Operational logging state common to all BGP routers.";
      ntos-extensions:feature "product";
      uses common-bgp-logging-conf;
    }
  }
}
