module ntos-certificate {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:certificate";
  prefix ntos-certificate;

  import ietf-netconf-acm {
    prefix nacm;
  }
  import ntos {
    prefix ntos;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-commands {
    prefix ntos-cmd;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS X509 certificate management.";

  revision 2018-10-03 {
    description
      "Initial version.";
    reference "";
  }

  rpc certificate-import {
    description
      "Import a X509 certificate from network, in PEM format.";
    input {

      leaf name {
        type ntos-types:cert-name;
        description
          "The name to assign of the certificate.";
      }

      leaf url {
        type union {
          type ntos-types:http-url;
          type ntos-types:sftp-url;
          type ntos-types:scp-url;
          type ntos-types:ftp-url;
          type ntos-types:tftp-url;
        }
        mandatory true;
        description
          "The URL from which to download the certificate in PEM format.";
      }

      leaf private-key-url {
        type union {
          type ntos-types:http-url;
          type ntos-types:sftp-url;
          type ntos-types:scp-url;
          type ntos-types:ftp-url;
          type ntos-types:tftp-url;
        }
        description
          "The URL from which to download the certificate private key in
           PEM format.";
      }
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }

    nacm:default-deny-all;
    ntos-ext:feature "product";
    ntos-ext:nc-cli-cmd "certificate import";
    ntos-api:internal;
  }

  rpc certificate-export {
    description
      "Export a X509 certificate in PEM format.";
    input {

      leaf name {
        type ntos-types:cert-name;
        description
          "The name of the certificate.";
        ntos-ext:nc-cli-completion-xpath
            "/ntos:state/ntos-certificate:certificate/ntos-certificate:name";
      }

      leaf url {
        type union {
          type ntos-types:http-url;
          type ntos-types:sftp-url;
          type ntos-types:scp-url;
          type ntos-types:ftp-url;
          type ntos-types:tftp-url;
        }
        mandatory true;
        description
          "The URL where the certificate is updloaded.";
      }
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }

    nacm:default-deny-all;
    ntos-ext:feature "product";
    ntos-ext:nc-cli-cmd "certificate export";
    ntos-api:internal;
  }

  rpc certificate-add {
    description
      "Add a X509 certificate in PEM format.";
    input {

      leaf name {
        type ntos-types:cert-name;
        description
          "The name to assign to the certificate.";
      }

      leaf data {
        type string;
        mandatory true;
        description
          "PEM-encoded X509 certificate.";
      }

      leaf private-key {
        type string;
        description
          "PEM-encoded X509 private key.";
      }
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }

    nacm:default-deny-all;
    ntos-ext:feature "product";
    ntos-ext:nc-cli-cmd "certificate import";
    ntos-api:internal;
  }

  rpc certificate-delete {
    description
      "Delete a X509 certificate.";
    input {

      leaf name {
        type ntos-types:cert-name;
        mandatory true;
        description
          "The name of the certificate.";
        ntos-ext:nc-cli-completion-xpath
            "/ntos:state/ntos-certificate:certificate/ntos-certificate:name";
      }
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }

    nacm:default-deny-all;
    ntos-ext:feature "product";
    ntos-ext:nc-cli-cmd "certificate delete";
    ntos-api:internal;
  }

  rpc certificate-show {
    description
      "Show X509 certificates list.";
    output {
      leaf buffer {
        type string;
        description
          "State output for the command.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }

    nacm:default-deny-all;
    ntos-ext:feature "product";
    ntos-ext:nc-cli-show "certificate list";
    ntos-api:internal;
  }

  rpc certificate-show-key {
    description
      "Show X509 certificate private key.";
    input {

      leaf name {
        type ntos-types:cert-name;
        mandatory true;
        description
          "The name of the certificate.";
        ntos-ext:nc-cli-completion-xpath
            "/ntos:state/ntos-certificate:certificate[ntos-certificate:has-private-key='true']/ntos-certificate:name";
      }
    }
    output {
      leaf buffer {
        type string;
        description
          "State output for the command.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }

    nacm:default-deny-all;
    ntos-ext:feature "product";
    ntos-ext:nc-cli-show "certificate key";
    ntos-api:internal;
  }

  rpc certificate-show-detail {
    description
      "Show X509 certificate details.";
    input {

      leaf name {
        type ntos-types:cert-name;
        mandatory true;
        description
          "The name of the certificate.";
        ntos-ext:nc-cli-completion-xpath
            "/ntos:state/ntos-certificate:certificate/ntos-certificate:name";
      }

      leaf base64 {
        type empty;
        description
          "Show the certificate in base64 format.";
      }
    }
    output {
      leaf buffer {
        type string;
        description
          "State output for the command.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }

    nacm:default-deny-all;
    ntos-ext:nc-cli-show "certificate";
    ntos-api:internal;
  }

  // XXX add CA certificate revocation list

  augment "/ntos:state" {
    description
      "Global X509 certificate state.";

    list certificate {
      key "name";

      description
        "List of X509 Certificates.";
      ntos-ext:feature "product";

      leaf name {
        type string;
        description
          "The name of the certificate.";
      }

      leaf subject {
        type string;
        description
          "The subject of the certificate.";
      }

      leaf issuer {
        type string;
        description
          "The issuer of the certificate.";
      }

      leaf validity-not-before {
        type string;
        description
          "The validity the certificate: not before this date.";
      }

      leaf validity-not-after {
        type string;
        description
          "The validity the certificate: not after this date.";
      }

      leaf has-private-key {
        type boolean;
        description
          "There is a private key associated to this certificate.";
      }
    }
  }
}
