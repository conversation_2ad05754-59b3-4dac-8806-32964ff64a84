module ntos-certmgt {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:certmgt";
  prefix ntos-certmgt;
  import ietf-netconf-acm {
    prefix nacm;
  }
  import ntos {
    prefix ntos;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-commands {
    prefix ntos-cmd;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS X509 certificate management.";

  revision 2022-10-12 {
    description
      "Initial version.";
    reference "";
  }

  identity certmgt {
    base ntos-types:SERVICE_LOG_ID;
    description
      "Cert management service.";
  }

  typedef cert-format-type {
    type enumeration {
      enum pfx {
        description
          "The pfx file format, must include key.";
      }
    }
    description
      "The enumeration of certificate file format type.";
  }

  rpc certmgt-localcert-import {
    description
      "The rpc of import a local certificate.";
    input {
      leaf vrf {
        mandatory true;
        type ntos:vrf-name;
        description
          "Specify the VRF.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf name {
        mandatory true;
        type ntos-types:cert-name;
        description
          "Name of imported certificate.";
      }

      leaf location {
        type union {
          type ntos-types:http-url;
          type ntos-types:ftp-url;
          type ntos-types:scp-url;
        }
        mandatory true;
        description
          "The URL from which to download the certificate.";
      }

      leaf cert-format{
        mandatory true;
        type cert-format-type;
        description
          "Format type of certificate.";
      }

      leaf password {
        type string {
          length "1..32";
        }
        description
          "Certificate file's password. Example: length must '1..32'.";
      }
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-ext:feature "product";
    ntos-ext:nc-cli-cmd "certmgt local import";
    nacm:default-deny-all;
  }

  rpc certmgt-localcert-import-from-web {
    description
      "The rpc of import a local certificate.";
    input {
      leaf vrf {
        mandatory true;
        type ntos:vrf-name;
        description
          "Specify the VRF.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf name {
        mandatory true;
        type ntos-types:cert-name;
        description
          "Name of imported certificate.";
      }

      leaf cert-format{
        mandatory true;
        type cert-format-type;
        description
          "Format type of certificate.";
      }

      leaf password {
        type string {
          length "1..32";
        }
        description
          "Certificate file's password. Example: length must '1..32'.";
      }
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-ext:feature "product";
    ntos-ext:nc-cli-cmd "certmgt local web import";
    nacm:default-deny-all;
    ntos-ext:nc-cli-hidden;
  }

  rpc certmgt-localcert-export {
    description
      "The rpc of export a local certificate.";
    input {
      leaf vrf {
        mandatory true;
        type ntos:vrf-name;
        description
          "Specify the VRF.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf name {
        mandatory true;
        type ntos-types:cert-name;
        description
          "Name of exported certificate.";
      }

      leaf location {
        type union {
          type ntos-types:http-url;
          type ntos-types:ftp-url;
          type ntos-types:scp-url;
        }
        mandatory true;
        description
          "The URL where the certificate is updloaded.";
      }
    }
    ntos-ext:feature "product";
    ntos-ext:nc-cli-cmd "certmgt local export";
    nacm:default-deny-all;
  }

  rpc certmgt-localcert-export-from-web {
    description
      "The rpc of export a local certificate.";
    input {
      leaf vrf {
        mandatory true;
        type ntos:vrf-name;
        description
          "Specify the VRF.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf name {
        mandatory true;
        type ntos-types:cert-name;
        description
          "Name of exported certificate.";
      }
    }
    ntos-ext:feature "product";
    ntos-ext:nc-cli-cmd "certmgt local web export";
    nacm:default-deny-all;
    ntos-ext:nc-cli-hidden;
  }

  rpc certmgt-localcert-delete {
    description
      "The rpc of delete a local cert.";
    input {
      leaf vrf {
        mandatory true;
        type ntos:vrf-name;
        description
          "Specify the VRF.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf-list name {
        type ntos-types:cert-name;
        description
          "The cert name will be deleted from localcert.";
      }
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-ext:feature "product";
    ntos-ext:nc-cli-cmd "certmgt local delete";
    nacm:default-deny-all;
  }

 rpc certmgt-localcert-show {
    description
      "The rpc to show all certificates.";
    input {
      leaf vrf {
        mandatory true;
        type ntos:vrf-name;
        description
          "Specify the VRF.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf name {
        type ntos-types:cert-name;
        description
          "The certificate name.";
      }
    }

    output {
      leaf cert-sum {
          type uint32;
          description
            "The total number of certs.";
      }
      list cert {
        key "name";
        description
          "The certificate list.";
        leaf name {
          type ntos-types:ntos-obj-name-type;
          description
            "The certificate name.";
        }

        leaf issuer {
          type string;
          description
            "The issuer of the certificate.";
        }

        leaf subject {
          type string;
          description
            "The subject of the certificate.";
        }

        leaf not-before {
          type string;
          description
            "The not-before day of the certificate.";
        }

        leaf not-after {
          type string;
          description
            "The not-after day of the certificate.";
        }

        leaf version {
          type string;
          description
            "The version of the certificate.";
        }

        leaf serial-number {
          type string;
          description
            "The serial number of the certificate.";
        }

        leaf extensions {
          type string;
          description
            "The extensions of the certificate.";
          ntos-ext:nc-cli-stdout;
        }

        leaf ref-num {
          type int32;
          description
            "The cert be referenced numbers.";
        }

        list ref-list {
          key "ref-obj-name";
          leaf ref-obj-name {
            type ntos-types:ntos-obj-name-type;
            description
              "The certificate reference object name.";
          }

          leaf ref-module-name {
            type ntos-types:ntos-obj-name-type;
            description
              "The certificate reference module name.";
          }
        }
      }
    }
    ntos-ext:feature "product";
    ntos-ext:nc-cli-show "certmgt local";
    nacm:default-deny-all;
  }
}
