module ntos-content-filter {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:content-filter";
  prefix ntos-content-filter;

  import ntos {
    prefix ntos;
  }

  import ntos-types {
    prefix ntos-types;
  }

  import ntos-extensions {
    prefix ntos-ext;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS config content-filter module.";

  revision 2023-03-06 {
    description
      "Create.";
    reference
      "";
  }

  typedef content-filter-action-type {
    type enumeration {
      enum alert {
        description
          "Indicate the type of alert.";
      }
      enum block {
        description
          "Indicate the type of block.";
      }
    }
    description
      "The action type of the content filter profile.";
  }

  typedef content-filter-direction-type {
    type enumeration {
      enum upload {
        description
          "Upload.";
      }
      enum download {
        description
          "Download.";
      }
      enum both {
        description
          "Both.";
      }
    }
    description
      "The direction of the file audit.";
  }

  grouping keyword-group-obj {
    description
      "The grouping of keyword-group.";

    leaf name {
      type ntos-types:ntos-obj-name-type;
      description
        "The name of the keyword group.";
    }

    leaf description {
      type ntos-types:ntos-obj-description-type;
      description
        "The description of the keyword group.";
    }

    leaf-list keyword {
      type string {
        pattern "[^\"\\\\]*" {
          error-message 'cannot include character: \"';
        }
      }
      description
        "The string of the keyword.";
    }
  }

  grouping rule-filter {
    description
      "The grouping of keyword group filter.";

    leaf name {
      type ntos-types:ntos-obj-name-type;
      description
        "The name of the keyword group filter.";
    }

    leaf rule-id {
      type uint32;
      config false;
      description
        "Rule id.";
    }

    leaf protocols {
      type union {
        type enumeration {
          enum any {
            description
              "All protocol.";
          }
        }
        type bits {
          bit http {
            description
              "HTTP protocol.";
          }
          bit ftp {
            description
              "FTP protocol.";
          }
        }
      }
      description
        "The protocols of content filter.";
      default any;
    }

    leaf direction {
      type content-filter-direction-type;
      description
        "The direction of content filter.";
      default both;
    }

    leaf action {
      type content-filter-action-type;
      description
        "The action of content filter rule.";
      default alert;
    }

    leaf-list keyword-group {
      type ntos-types:ntos-obj-name-type;
      description
        "The name of keyword group.";
    }
  }

  grouping content-filter-profile {
    description
      "The grouping of content filter.";

    leaf name {
      type ntos-types:ntos-obj-name-type;
      description
        "The name of the content filter profile.";
    }

    leaf description {
      type ntos-types:ntos-obj-description-type;
      description
        "The description of content filter profile.";
    }

    list rule {
      key "name";
      description
        "The name of the rule filter.";
      uses rule-filter;
    }
  }

  grouping show-rpc-input-arguments {
    description
      "The grouping of input leaf node.";

    leaf vrf {
      type ntos:vrf-name;
      description
        "The specific vrf.";
    }

    leaf name {
      type string;
      description
        "The name string of the object.";
      ntos-ext:nc-cli-group "subcommand1";
    }

    leaf filter {
      type string;
      description
        "The filter string of the object.";
      ntos-ext:nc-cli-group "subcommand1";
    }

    leaf start {
      type uint32;
      description
        "The index of page start.";
    }

    leaf end {
      type uint32;
      must "current() >= ../start" {
          error-message "The end value must be larger than the start value.";
      }
      description "The index of page end.";
    }
  }

  grouping policy-id {
    description
      "The grouping of policy id.";

    leaf id {
      type uint32;
      description
        "The id of policy.";
    }
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "Content-filter configuration.";

    container keyword-group {
      description
        "Keyword group configuration.";

      list group {
        key "name";
        description
          "Indicate the Keyword group.";
        uses keyword-group-obj;
      }
    }

    container content-filter {
      description
        "Content filter configuration.";
      list profile {
        key "name";
        description
          "Indicate the Content filter profile.";
        uses content-filter-profile;
      }
    }
  }

  augment "/ntos:state/ntos:vrf" {
    description
      "State of Content filter.";

    container keyword-group {
      description
        "Keyword group configuration.";

      list group {
        key "name";
        description
          "Indicate the Keyword group.";
        uses keyword-group-obj;
      }
    }

    container content-filter {
      description
        "Content filter configuration.";

      list profile {
        key "name";
        description
          "Indicate the Content filter profile.";
        uses content-filter-profile;
      }
    }
  }

  rpc keyword-group-show {
    input {
      uses show-rpc-input-arguments;
    }

    output {
      leaf keyword-group-num {
        type uint32;
        description
          "The total number of the keyword group.";
      }

      list keyword-group {
        key "name";
        description
          "Indicate the Keyword group.";
        uses keyword-group-obj;
        container content-filter {
          leaf-list profile {
            type string;
            description
              "The profile name of refer keyword group.";
          }
        }
      }
    }

    ntos-ext:nc-cli-show "keyword-group";
  }

  rpc content-filter-show {
    input {
      container profile {
        description
          "The container of profile.";
        presence "show content filter profile.";
        uses show-rpc-input-arguments;

        container rule {
          description
            "The container of profile rule.";
          presence "show profile rule.";
          uses show-rpc-input-arguments;
        }
      }
    }

    output {
      leaf profile-num {
        type uint32;
        description
          "The num of content filter profile.";
      }

      list profile {
        key "name";
        description
          "The list of content filter profile.";
        uses content-filter-profile;
        container security-policy {
          leaf-list id {
            type uint32;
            description
              "The policy name of refer profile.";
          }
        }
        container sim-security-policy {
          leaf-list id {
            type uint32;
            description
              "The policy name of refer profile.";
          }
        }
      }

      leaf rule-num {
        type uint32;
        description
          "The num of profile rule.";
      }
      list rule {
        key "name";
        description
          "The list of content filter rule.";
        uses rule-filter;
      }
    }
    ntos-ext:nc-cli-show "content-filter";
  }

}