module ntos-ddns {
  namespace "urn:ruijie:ntos:params:xml:ns:yang:ddns";
  prefix ntos-ddns;

  import ietf-netconf-acm {
    prefix nacm;
  }
  import ntos {
    prefix ntos;
  }
  import ntos-api {
    prefix ntos-api;
  }

  import ntos-extensions {
    prefix ntos-extensions;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos-types {
    prefix ntos-types;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS DDNS module.";

  revision 2022-02-19 {
    description
      "Initial version.";
    reference "";
  }

  identity ddns {
    base ntos-types:SERVICE_LOG_ID;
    description
      "DDNS resolver service.";
  }

  grouping ddns-server-default-config {
    description
      "DDNS / resolver related configuration data.";

    leaf-list search {
      type ntos-inet:domain-name;
      ordered-by user;
      description
        "An ordered list of domains to search when resolving
         a host name.";
    }
  }

   grouping ddns-common-config {
    description
      "Configuration data for common ddns.";
      
    leaf policy-name {
      type string {
        pattern "[^`~!#$%^&*+/|{};:\"',\\\\<>? ]*" {
          error-message 'cannot include character: `~!#%^&*+\|{};:",/<>? ';
        }
      }
      description
        "policy-name";
    }       
    leaf username {
      type string;
      description
        "username";
    }
    leaf password {
      type string;
      description
        "user password";
    }
  
    leaf domain {
        type string {
          length "3..255";
          pattern '((([a-zA-Z0-9_]([a-zA-Z0-9\-_]){0,61})?[a-zA-Z0-9\-]\.)*' +
                  '([a-zA-Z0-9_]([a-zA-Z0-9\-_]){0,61})?[a-zA-Z0-9\-]\.?)|\.';
          ntos-extensions:nc-cli-shortdesc "<host-name>";
        }
        description
          "DDNS Domain what you need to analyze";
    }
    leaf interface {
        type ntos-types:ifname;
        description
          "Show interface by this name.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos-interface:interface/*/*[local-name()='name']";
    }
    
    leaf server-host {
        type string;
        description
        "The DDNSAPI host which was supported by DDNS Service Provider ";
    }    
  } 
  grouping system-ddns-servers-config {
    description
      "Configuration data for DDNS resolvers.";
      
    leaf server-name {
      type string;
      description
        "DDNS server name configuration(Currently supports oray|pubyun|dyndns|no-ip|other).";
    } 
    list policy{
        description
          "DDNS policy name configuration";
        key "policy-name";
        uses ddns-common-config;
    }         
}

  augment "/ntos:config/ntos:vrf" {
    description
      "Top-level grouping for DDNS / resolver config and operational
       state data.";

    container ddns {
      presence "Makes ddns available";
      description
        "Enclosing container for DDNS resolver data.";
//      uses system-ddns-config;

      list server {
//        presence "DDNS server available";
        description
          "DDNS server configuration.";
        key "server-name";
        uses system-ddns-servers-config;
      }
    }
  }
    rpc show-ddns-status{
    description
      "Show DDNS status.";
    input {

     leaf server {
        type string;
        default "all";
        description
          "DDNS Service Provider";
      }
     leaf domain {
        type string;
        default "all";
        description
          "ddns domain.";
      }
      
    }
    output {

      leaf data {
        type string;
        description
          "The command output buffer.";
        ntos-extensions:nc-cli-stdout;
        ntos-extensions:nc-cli-hidden;
      }
    }
    ntos-extensions:nc-cli-show "ddns-status";
  }  
  
  augment "/ntos:state/ntos:vrf" {
    description
      "Top-level grouping for DDNS / resolver config and operational
       state data.";

    container ddns {
      presence "Makes ddns available";
      description
        "Enclosing container for DDNS resolver data.";
//      uses system-ddns-config;

      list server {
//        presence "Makes server available";
        key "server-name";
        description
          "DDNS proxy configuration.";
        uses system-ddns-servers-config;
      }
    }
  }

}
