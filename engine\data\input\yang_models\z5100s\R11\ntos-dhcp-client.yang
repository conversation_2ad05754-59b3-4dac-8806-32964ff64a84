module ntos-dhcp-client {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:dhclient";
  prefix ntos-dhclient;

  import ntos {
    prefix ntos;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-extensions {
    prefix ntos-extensions;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos-dhcp {
    prefix ntos-dhcp;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS DHCP client.";

  revision 2022-02-28 {
    description
      "Initial version.";
    reference "";
  }

  identity dhcp-client {
    base ntos-types:SERVICE_LOG_ID;
    description
      "DHCP Client service.";
  }

  grouping dhcp-lease6-time-info {
    leaf start {
      type uint64;
          description
            "Lease start time.";
    }
    leaf preferred-life {
      type uint32;
          description
            "The IPv6 preferred lifetime associated with this address, in seconds.";
    }
    leaf max-life {
      type uint32;
          description
            "The valid lifetime associated with this address, in seconds.";
    }
  }

  grouping dhcp-client-lease-info {
    description
      "The information for DHCP client lease.";

    list dhclient-lease-info {
      key "interface";
      description
        "The information for DHCP client lease with the interface.";

      leaf interface {
        type ntos-types:ifname;
        description
          "The interface that is the DHCP client start on.";
      }

      container lease4-info {
        presence "dhcpv4 lease info";
        leaf addr {
          type ntos-inet:ipv4-address;
          description
            "The IPv4 address of the DHCP client get from DHCP server.";
        }
        leaf mask {
          type ntos-inet:ipv4-address;
          description
            "The mask of the IPv4 address.";
        }
        leaf lease-time {
          type uint32;
          description
            "The network address lease time assigned to DHCP clients (inseconds).";
        }
        leaf timestamp {
          type uint64;
              description
                "Lease update time.";
        }
        leaf state {
            type ntos-dhcp:binding-state;
            description
                "The state of the address.";
        }
        leaf routers {
          type ntos-inet:ipv4-address;
          description
            "The mask of the IPv4 address.";
        }
        leaf server-identifier {
          type string {
            length "1..128";
          }
          description
            "The server identifier.";
        }
        leaf-list domain-name-servers {
          type ntos-inet:ipv4-address;
          description
            "The domain name server address get from DHCP server.";
        }
      }
      container lease6-info {
        presence "dhcpv6 lease info";
        list ia-info {
            key "ia-id";
            leaf ia-id {
                type string;
                description
                    "The id of Identity Association of the client.";
            }
            container ia-na {
                presence "dhcpv6 na lease";
                list iaaddr {
                    key "addr";
                    leaf addr {
                    type ntos-inet:ipv6-address;
                    description
                        "The IPv6 address of the DHCP client get from DHCP server.";
                    }
                    leaf state {
                        type ntos-dhcp:binding-state;
                        description
                            "The state of the address.";
                    }
                    uses dhcp-lease6-time-info;
                }
            }
            container ia-pd {
            presence "dhcpv6 pd lease";
                list iaprefix {
                    key "prefix";
                    leaf prefix {
                    type ntos-inet:ipv6-prefix;
                    description
                        "The IPv6 prefix of the DHCP client get from DHCP server.";
                    }
                    leaf pd-pool-id {
                    type string {
                        length "1..63";
                    }
                    description
                        "The IPv6 prefix pool identify that is provided for RA.";
                    }
                    leaf state {
                        type ntos-dhcp:binding-state;
                        description
                            "The state of the address.";
                    }
                    uses dhcp-lease6-time-info;
                }
            }
        }

        leaf server-identifier {
          type string {
            length "1..128";
          }
          description
            "The server identifier.";
        }
        leaf-list domain-name-servers {
          type ntos-inet:ipv6-address;
          description
            "The domain name server address get from DHCP server.";
        }
        leaf-list domain-list {
          type string;
          description
            "The domain search list the client is to use when resolving hostnames with DNS.";
        }
      }
    }
  }

  grouping dhcp-client-cmd {
    list client-list {
        key "interface ip-type";

        leaf interface {
            type ntos-types:ifname;
            description
            "The interface that is the DHCP client start on.";
        }
        leaf ip-type {
            type enumeration {
                enum 4 {
                    description
                    "IPv4.";
                }
                enum 6 {
                    description
                    "IPv6.";
                }
            }
            description 
                "The DHCP client work in IPv4 or IPv6.";
        }
        leaf log-level {
          type enumeration {
            enum error {
              description
                "Error level.";
            }
            enum warning {
              description
                "Warning level.";
            }
            enum notice {
              description
                "Notice level.";
            }
            enum info {
              description
                "Info level.";
            }
            enum debug {
              description
                "Debug level.";
            }
          }
          description
            "Logging level message levels.";
        }
        leaf command {
            type enumeration {
                enum clear-stats-pkt {
                    description
                    "The command is used to clear the statistics of package.";
                }
            }
            description 
                "The DHCP client work in IPv4 or IPv6.";
        }
        leaf command-param {
            type string {
                length "1..128";
            }
            description 
                "The command params.";
        }
        leaf response {
            type string;
            description 
                "The command response.";
        }
    }
  }

  augment "/ntos:state/ntos:vrf/ntos-dhcp:dhcp" {
    description
      "DHCP Client state.";
    container client {
      description
        "DHCP Client state.";
      presence "DHCP Client state";

      container lease-info {
        presence "DHCP Client lease infomation";
        uses dhcp-client-lease-info;
      }
      container command-info {
        presence "DHCP Client command infomation";
        uses dhcp-client-cmd;
      }
      container stats-info {
        presence "DHCP Client statistics infomation";
        list client {
          key "interface ip-type";

          leaf interface {
            type ntos-types:ifname;
            description
              "The interface that is the DHCP client start on.";
          }
          leaf ip-type {
            type enumeration {
              enum 4 {
                description
                  "IPv4.";
              }
              enum 6 {
                description
                  "IPv6.";
              }
            }
            description 
              "The DHCP client work in IPv4 or IPv6.";
          }
          leaf stats-pkt {
            type string;
            description 
              "The statistics data of the client.";
          }
        }
      }
    }
  }

  rpc show-dhcp-client-pd-pool-id {
    description
      "Show dhcpv6 pd client prefix pool id.";
    input {
      leaf vrf {
        type string;
        default "main";
        description
          "Specify the VRF.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }
      leaf search {
        type string {
          length "1..64";
        }
        description
          "search server config with this key.";
      }
    }
    output {
      leaf data {
        type string;
        description
          "The command output buffer.";
        ntos-extensions:nc-cli-stdout;
        ntos-extensions:nc-cli-hidden;
      }
    }
    ntos-extensions:feature "product";
    ntos-extensions:nc-cli-show "dhcp-client pd-pool-id";
    ntos-api:internal;
  }

  rpc show-dhcp-client-lease {
    description
      "Show dhcp client lease information.";
    input {
      leaf vrf {
        type string;
        default "main";
        description
          "Specify the VRF.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }
      leaf interface {
          type ntos-types:ifname;
          description
          "The interface that is the DHCP client start on.";
      }
      leaf lease-type {
        type enumeration {
          enum 4 {
            description
              "IPv4.";
          }
          enum 6 {
            description
              "IPv6.";
          }
        }
        description
          "dhcpv4 lease or dhcpv6 lease.";
      }
      leaf search {
        type string {
          length "1..64";
        }
        description
          "search lease with this key.";
      }
      leaf output-type {
        type enumeration {
          enum node-tree {
            description
              "Output format to yang module data.";
          }
          enum json-string {
            description
              "Output format to json.";
          }
        }
        default "node-tree";
        description
          "Output format to yang module data or json string.";
      }
    }
    output {
      choice output-type {
        case node-tree {
          uses dhcp-client-lease-info;
        }
        case json-string {
          leaf data {
            type string;
            description
              "The command output buffer.";
            ntos-extensions:nc-cli-stdout;
            ntos-extensions:nc-cli-hidden;
          }
        }
      }
    }
    ntos-extensions:feature "product";
    ntos-extensions:nc-cli-show "dhcp-client lease";
    ntos-api:internal;
  }

  rpc show-dhcp-client-stats {
    description
      "Show dhcp client statistics information.";
    input {
      leaf vrf {
        type string;
        default "main";
        description
          "Specify the VRF.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }
      leaf interface {
          type ntos-types:ifname;
          description
          "The interface that is the DHCP client start on.";
          ntos-extensions:nc-cli-no-name;
      }
      leaf client-type {
        type enumeration {
          enum 4 {
            description
              "IPv4.";
          }
          enum 6 {
            description
              "IPv6.";
          }
        }
        default "4";
        description
          "dhcpv4 client or dhcpv6 client.";
        ntos-extensions:nc-cli-no-name;
      }
    }
    output {
      leaf data {
        type string;
        description
          "The command output buffer.";
        ntos-extensions:nc-cli-stdout;
        ntos-extensions:nc-cli-hidden;
      }
    }
    ntos-extensions:feature "product";
    ntos-extensions:nc-cli-show "dhcp-client stats";
    ntos-api:internal;
  }
}
