module ntos-dmc {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:dmc";
  prefix ntos-dmc;

  import ntos {
    prefix ntos;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-commands {
    prefix ntos-cmd;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-system {
    prefix ntos-system;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS device maintainable center module.";
  revision 2024-04-23 {
    description
      "add DAE RPCs";
    reference "revision 2024-04-23";
  }
  revision 2021-12-01 {
    description
      "Device maintainable center RPCs";
    reference "revision 2023-09-20";
  }

  grouping dmc-dae {
    container tasks {
        description
          "DAE tasks.";
      leaf info {
        type empty;
        description
          "show DAE task status.";
      }
    }
    container objects {
      description
          "DAE monitor objects.";
      leaf info {
        type empty;
        description
          "show DAE monitor objects(process name) info.";
      }
      container add {
        description
              "add DAE monitor objects(process name).";
        leaf name {
          type string;
            description
              "DAE monitor objects(process name).";
        }
      }
      container delete {
        description
          "delete DAE monitor objects(process name).";
        leaf name {
          type string;
          description
            "DAE monitor objects(process name).";
        }
      }
    }
    container params {
      description
          "DAE parameters.";
      leaf info {
        type empty;
        description
          "show DAE parameters.";
      }
      container set {
        description
          "set DAE parameters.";
        leaf name {
          type string;
          description
            "DAE monitor objects(process name).";
        }
        leaf key {
          type string;
            description
              "key of DAE algorithm parameter.";
        }
        leaf value {
          type string;
            description
              "value of DAE algorithm parameter.";
        }
      }
    }
  }
  rpc dmc {
    description
      "DMC cli.";

    input {
      container plugin {
       description
         "plugin";
        leaf status {
          type empty;
          description
            "The status of the dmc plugin.";
        }
      }

      container oneclick {
       description
         "oneclick collector for diagnostic";
        leaf rule-stats {
          type empty;
          description
            "Output stats of oneclick rule.";
        }
      }

      container dae {
       description
         "DAE(Data Analysis Engine)";
        uses dmc-dae;
      }
    }
    output {
       uses ntos-cmd:long-cmd-output;
       uses ntos-cmd:long-cmd-status;
    }
    ntos-ext:nc-cli-cmd "dmc";
    ntos-api:internal;
  }
}
