module ntos-dns-security {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:dns-security";
  prefix ntos-dns-security;
  
  import ntos {
    prefix ntos;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel: 4008-111-000
    E-mail: <EMAIL>";
  description
    "Ruijie NTOS dns-security module.";
    
  revision 2024-03-25 {
    description
      "Initial version.";
    reference
      "";
  }

  typedef work-mode-type {
    type enumeration {
      enum black-white {
        description
          "Black-white mode.";
      }
      enum white {
        description
          "White mode.";
      }
      enum none {
        description
          "none mode.";
      }
    }
  }

  typedef filter-action-type {
    type enumeration {
      enum allow {
        description
          "Allow.";
      }
      enum warning {
        description
          "Warning.";
      }
      enum block {
        description
          "Block.";
      }
    }
  }

  typedef defense-action-type {
    type enumeration {
      enum warning {
        description
          "Warning.";
      }
      enum block {
        description
          "Block.";
      }
      enum redirect {
        description
          "Redirect.";
      }
    }
  }

  grouping vrf {
    leaf vrf {
      description
        "VRF name.";

      type string;
      default "main";
    }
  }

  grouping reference-detail {
    container security-policy {
      description
        "The reference list of the security policy.";
      leaf-list id {
        type uint32;
        description
          "The id of the security policy.";
      }
    }
    container sim-security-policy {
      description
        "The reference list of the simulative security policy.";
      leaf-list id {
        type uint32;
        description
          "The id of the simulative security policy";
      }
    }
  }

  grouping dns-filter-level-group {
    description
      "Dns filter block level.";
    list filter-type {
      key "type-id";
      leaf type-id {
        type uint32;
        description
          "The id of dns filter type";
      }
      leaf type-name {
        type string {
          length "1..127";
        }
        description
          "The name of dns filter type";
      }
      leaf action {
        type filter-action-type;
      }
    }
  }

  grouping enc-dns-group {
    description
      "Encryption dns block.";
    leaf dot-status {
      type boolean;
      default true;
      description
        "The status of dns over tls.";
    }
    leaf doh-status {
      type boolean;
      default true;
      description
        "The status of dns over https.";
    }
  }

  grouping redirect-group {
    description
      "Redirect work.";
    leaf enabled-status {
      type boolean;
      default false;
      description
        "The status of enabled.";
    }
    leaf ip {
      type string {
        length "1..256";
      }
    }
  }

  grouping record-type-filter-group {
    description
      "Record type filter.";
    leaf svcb-block-status {
      type boolean;
      default true;
      description
        "Type SVCB(64) block status.";
    }
    leaf https-block-status {
      type boolean;
      default true;
      description
        "Type HTTPS(65) block status.";
    }
  }

  grouping dns-filter-item {
    description
      "Single dns filter configuration item.";
    leaf name {
      type string {
        length "1..127";
      }
      description
        "The name of dns filter template.";
    }
    container reference-list {
      description
      "The policy references of the template.";
      uses reference-detail;
    }
    leaf mode {
      type work-mode-type;
      description
        "The work mode of dns filter.";
    }
    leaf log-status {
      type boolean;
      default false;
      description
        "The status of white log.";
    }
    container black-list {
      leaf-list url {
        type string {
          length "1..256";
        }
      }
    }
    container white-list {
      leaf-list url {
        type string {
          length "1..256";
        }
      }
    }
    container dns-filter-level {
      uses dns-filter-level-group;
    }
    container enc-dns {
      uses enc-dns-group;
    }
    container redirect {
      uses redirect-group;
    }
    container record-type-filter {
      uses record-type-filter-group;
    }
  }

  grouping dns-filter-group {
    description
      "Dns filter configuration.";
    leaf dns-status {
      type boolean;
      default false;
      description
        "The status of dns security enable or not.";
    }
    container security-zone {
      description
        "Security zone selection.";
      leaf-list zone {
        type string;
        description
          "Select security zone to protect.";
      }
    }
    container filter-list {
      list filter-template {
        key "name";
        uses dns-filter-item;
      }
    }
  }
  
  grouping protect-group {
    description
      "Protect configuration.";
    leaf ip {
      type string {
        length "1..256";
      }
    }
    leaf-list zone {
      type string;
      description
        "Select security zone of attack.";
    }
  }

  grouping attack-defense-group {
    description
      "Attack defense configuration.";
    leaf protocol {
      type boolean;
      default true;
    }
    leaf security-vul {
      type boolean;
      default true;
    }
    leaf acction {
      type defense-action-type;
    }
    leaf redirect-url {
      type string {
        length "1..256";
      }
    }
  }

  grouping flood-defense-group {
    description
      "Flood defense configuration.";
    leaf enable {
      type boolean;
      default true;
    }
    leaf pkt-drop {
      type uint32;
    }
    leaf src-lock {
      type uint32;
    }
    leaf lock-time {
      type uint32;
    }
  }

  grouping dns-attack-defense-group {
    description
      "Dns attack defense module.";
    container protect {
      uses protect-group;
    }
    container attack-defense {
      uses attack-defense-group;
    }
    container flood-defense {
      uses flood-defense-group;
    }
  }
  
  grouping dns-security-cloud-group {
    description
      "Security cloud linkage configuration.";
    leaf pdns {
      type boolean;
      default true;
    }
    leaf online-protect {
      type boolean;
      default true;
    }
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "The configuration of dns-security.";
    container dns-security {
      container dns-filter {
        uses dns-filter-group;
      }
      container dns-attack-defense {
        uses dns-attack-defense-group;
      }
      container dns-security-cloud {
        uses dns-security-cloud-group;
      }
    }
  }
  
  rpc show-dns-security {
    description
      "Get dns security configuration";
    input {
      uses vrf;
    }
    output {
      container dns-security {
        container dns-filter {
          uses dns-filter-group;
        }
        container dns-security-cloud {
          uses dns-security-cloud-group;
        }
      }
    }
    ntos-ext:nc-cli-show "dns-security config";
  }

  rpc show-dns-intercept-addr {
    description
      "show dns intercept ip addr";
    output {
      container dns-intercept {
        leaf ip-addr {
          type string;
          description
            "DNS intercept ip addr.";
        }
      }
    }
    ntos-ext:nc-cli-show "dns-intercept addr";
  }

  rpc cmd-dns-security-get-apply-status {
    description
      "Get dns security apply status";
    output {
      container dns-security {
        leaf status {
          type uint32;
        }
      }
    }
    ntos-ext:nc-cli-cmd "dns-security get-apply-status";
  }
}
