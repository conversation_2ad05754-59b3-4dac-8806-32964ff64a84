module ntos-gre {
  namespace "urn:ruijie:ntos:params:xml:ns:yang:gre";
  prefix ntos-gre;

  import ntos {
    prefix ntos;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-ip {
    prefix ntos-ip;
  }
  import ntos-if-types {
    prefix ntos-if;
  }
  import ntos-inet-types {
    prefix ntos-inet-types;
  }
  import ntos-interface {
    prefix ntos-interface;
  }
  import ntos-tunnel {
    prefix ntos-tunnel;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-qos {
    prefix ntos-qos;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import extra-conditions {
    prefix ext-cond;
  }
  import ntos-dhcp-snooping {
    prefix ntos-dhcp-snp;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS GRE tunnel interfaces.";

  revision 2019-08-13 {
    description
      "Remove unicity condition on GRE parameters. The rules are actually more
       complex and should include the input key.
       Fix online help for key commands.";
    reference "";
  }
  revision 2018-10-03 {
    description
      "Initial version.";
    reference "";
  }

  identity gre {
    base ntos-types:INTERFACE_TYPE;
    description
      "GRE interface.";
  }

  typedef type-gre-key {
    type union {
      type uint32;
      type ntos-inet-types:ipv4-address;
    }
    description
      "GRE key type.";
  }

  grouping gre-checksum {
    description
      "GRE checksum.";

    leaf checksum {
      type enumeration {
        enum input {
          description
            "Verify checksum for all input packets.";
        }
        enum output {
          description
            "Calculate checksum for outgoing packets.";
        }
        enum both {
          description
            "Calculate checksum for outgoing packets, and verify
             it for all input packets.";
        }
      }
      description
        "Enable checksum features for this tunnel.";
    }
  }

  grouping gre-sequence-number-config {
    description
      "Configuration for GRE sequence number.";

    leaf sequence-number {
      type enumeration {
        enum input {
          description
            "All input packet must be serialized.";
        }
        enum output {
          description
            "Enable sequencing of outgoing packets.";
        }
        enum both {
          description
            "Enable sequencing of outgoing packet and check serialization of all
             input packets.";
        }
      }
      description
        "Enable sequence number for this tunnel.";
    }
  }

  grouping gre-key {
    description
      "GRE key as defined in RFC2890.";

    container key {
      description
        "Required field. Set the value of the GRE key for this interface.";

      leaf input {
        type type-gre-key;
        description
          "GRE key of incoming packets (overrides the value specified in both).";
      }

      leaf output {
        type type-gre-key;
        description
          "GRE key for outgoing packets (overrides the value specified in both).";
      }

      leaf both {
        type type-gre-key;
        description
          "GRE key for incoming and outgoing packets.";
      }
    }
  }

  grouping gre-tunnel {
    description
      "GRE tunnel information, multipooint if remote is not set.";

    leaf type {
      type enumeration {
        enum gre {
          description
            "The  interface of gre.";
        }
        /*enum gretap {
          description
            "The  interface of gretap or ip6gretap.";
        }*/
        enum erspan {
          description
            "The  interface of erspan.";
        }
      }
      description
        "Required field. Support interface type : gre|erspan.";
    }

    /*leaf nums {
      type uint16 {
          range "0..10" {
            error-message
              "The gre tunnel nested limit number must range in 0-10.";
          }
      }
      default 4;
      description
        "The number of tunnel nested-limit number.";
    }*/

    leaf local {
      type ntos-inet-types:ipv4-filter-invalid-address;
      mandatory true;
      description
        "Required field. The source address that should be used for the
         tunnel.";
    }

    leaf remote {
      type ntos-inet-types:ipv4-filter-invalid-address;
      must 'contains(., ":") = contains(../local, ":")' {
        error-message "Ip version of local and remote must be the same";
      }
      description
        "The destination address that should be used for the
         tunnel.";
      ntos-api:must-added "contains(., \":\") = contains(../local, \":\")";
    }
  }

  grouping gre-keepalive {
    description
      "GRE tunnel keepalive information, include interval and retry times.";

      container keepalive {
        description
          "The description of keepalive about interval and retries.";

        leaf enabled {
          type boolean;
          default "false";
          description
            "Enable or disable keepalive options.";
        }

        leaf interval {
          type uint16 {
              range "1..32767" {
                error-message
                  "The gre tunnel keepalive interval must range in 1-32767 seconds.";
              }
          }
          default 10;
          description
            "Time interval in seconds between keepalive messages.";
        }

        leaf retries {
          type uint16 {
              range "1..255" {
                error-message
                  "The gre tunnel keepalive retries number must range in 1-255.";
              }
          }
          default 3;
          description
            "The number of retries for keepalive messages.";
        }
      }
  }

  grouping gre-fragmentation-mtu {
    description
      "GRE tunnel fragmentation information follows local interface.";
      leaf frag-mtu {
        type uint16;
        must 'current() >= 68 and current() <= 1600' {
          error-message "Fragmentation MTU must be >= 68 and <= 1600.";
        }
        default 1500;
        description
          "GRE tunnel fragmentation MTU. Must be in 68..1600";
      }
  }

  /*grouping gretap-eth-config {
    description
      "Configuration data for Gretap ethernet interfaces.";

    container ethernet {
      description
        "Ethernet configuration.";
      uses ntos-if:ethernet-address-config;
    }
  }

  grouping gretap-eth-state {
    description
      "State variables for Ethernet interfaces.";

    container ethernet {
      description
        "Ethernet state.";
      uses ntos-if:ethernet-address-state;
    }
  }*/

  grouping gre-config {
    description
      "Configuration for GRE.";
    uses gre-tunnel;
    uses gre-keepalive;
    uses gre-fragmentation-mtu;
    uses gre-checksum;
    uses gre-sequence-number-config;
    uses gre-key;
  }

  augment "/ntos:config/ntos:vrf/ntos-interface:interface" {
    description
      "Network GRE configuration.";

    list gre {
      max-elements 128;
      key "name";
      description
        "The list of GRE interfaces on the device.";
      ntos-ext:feature "product";
      uses ntos-interface:nonphy-interface-config {
        refine mtu {
          must 'current() >= 68 and current() <= 1600' {
            error-message "MTU must be >= 68 and <= 1600.";
          }
          default 1500;
          description
            "Set the max transmission unit size in octets, range: 68..1600.";
        }
        refine promiscuous {
          config false;
        }
      }
      uses ntos-ip:ntos-ipv4-ptm-config;
      uses ntos-ip:ntos-ipv6-ptm-config;
      uses ntos-ip:ntos-network-stack-parameters;
      uses ntos-tunnel:tunnel-common-config;
      uses gre-config;
      /*uses gretap-eth-config {
        augment "ethernet" {
          description
            "gretap ethernet parameters.";
          uses ntos-if:ethernet-transmission-config;
        }
      }*/
      uses ntos-qos:logical-if-qos-config;
      uses ntos-dhcp-snp:dhcp-snp-parameters;
      ext-cond:unique-tuple "local remote link-vrf key/input key/both" {
        error-message "The (local, remote, link-vrf, key/input, key/both) tuple must be unique.";
      }
      ntos-api:extension-added "extra-conditions:unique-tuple 'local remote link-vrf key/input key/both'";
    }
  }

  augment "/ntos:state/ntos:vrf/ntos-interface:interface" {
    description
      "Network GRE operational state data.";

    list gre {
      key "name";
      description
        "The list of GRE interfaces on the device.";
      ntos-ext:feature "product";
      uses ntos-interface:interface-state;
      uses ntos-ip:ntos-ipv4-ptm-state;
      uses ntos-ip:ntos-ipv6-ptm-state;
      uses ntos-ip:ntos-network-stack-parameters;
      uses ntos-tunnel:tunnel-common-config;
      uses gre-config;
      /*uses gretap-eth-state {
        augment "ethernet" {
          description
            "Physical ethernet parameters.";
          uses ntos-if:ethernet-transmission-config;
        }
      }*/
      uses ntos-if:interface-common-state;
      uses ntos-if:interface-counters-state;
      uses ntos-qos:logical-if-qos-state;
      uses ntos-dhcp-snp:dhcp-snp-parameters;
    }
  }
}
