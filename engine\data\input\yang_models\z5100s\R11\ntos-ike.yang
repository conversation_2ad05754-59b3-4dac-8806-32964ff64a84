module ntos-ike {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:ike";
  prefix ntos-ike;

  import ietf-yang-types {
    prefix ietf-yang;
  }
  import ntos {
    prefix ntos;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-commands {
    prefix ntos-cmd;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:gao<PERSON><PERSON><PERSON>@ruijie.com.cn";
  description
    "Ruijie NTOS ike module.";

  revision 2023-08-02 {
    description
      "Add ike profile configure.";
  }

  revision 2022-12-23 {
    description
      "Add ike config and state.";
    reference "";
  }

  identity ike {
    base ntos-types:SERVICE_LOG_ID;
    description
      "ike service.";
  }

  grouping ike-key-config {
    description
      "Configuration data for ike key.";

    list key {
      key "type";
      ordered-by user;
      description
        "Configuration of ike key.";

      leaf type  {
        type enumeration {
          enum pre-share {
            description
              "Preshared-key type.";
          }
        }
        description
          "Set the type of ike key.";
      }

      list ipv4-address {
        key "ipv4-address";
        ordered-by user;
        description
          "Address configuration of key.";

        leaf ipv4-address {
          type ntos-inet:ipv4-address {
            ntos-ext:nc-cli-shortdesc "<ipv4-address>";
          }
          description
            "Set the ipv4 address.";
        }

        leaf key {
          type string {
            length "3..64";
          }
          mandatory true;
          description
            "Set the key of ipv4 address length 3..64.";
        }

        ntos-ext:nc-cli-one-liner;
      }

      list ipv6-address {
        key "ipv6-address";
        ordered-by user;
        description
          "Address configuration of key.";

        leaf ipv6-address {
          type ntos-inet:ipv6-address {
            ntos-ext:nc-cli-shortdesc "<ipv6-address>";
          }
          description
            "Set the ipv6 address.";
        }

        leaf key {
          type string {
            length "3..64";
          }
          description
            "Set the key of ipv6 address length 3..64.";
        }

        ntos-ext:nc-cli-one-liner;
      }

      list fqdn {
        key "fqdn";
        ordered-by user;
        description
          "fqdn configuration of preshare key.";

        leaf fqdn {
          type string {
            length "1..253";
          }
          description
            "Set the fqdn length 1..253.";
        }

        leaf key {
          type string {
            length "3..64";
          }
          description
            "Set the key of fqdn length 3..64.";
        }

        ntos-ext:nc-cli-one-liner;
      }

      list user-fqdn {
        key "user-fqdn";
        ordered-by user;
        description
          "user-fqdn configuration of preshare key.";

        leaf user-fqdn {
          type string {
            length "1..255";
          }
          description
            "Set the user-fqdn length 1..255.";
        }

        leaf key {
          type string {
            length "3..64";
          }
          description
            "Set the key of user-fqdn length 3..64.";
        }

        ntos-ext:nc-cli-one-liner;
      }

      list key-id {
        key "key-id";
        ordered-by user;
        description
          "key-id configuration of preshare key.";

        leaf key-id {
          type string {
            length "1..255";
          }
          description
            "Set the key-id length 1..255.";
        }

        leaf key {
          type string {
            length "3..64";
          }
          description
            "Set the key of key-id length 3..64.";
        }

        ntos-ext:nc-cli-one-liner;
      }

      max-elements 1000;
    }
  }

  grouping identity-config {
    description
      "Configuration data for identity.";
    container local-identity {
      choice local-identity {
        case ip {
          leaf address {
            type empty;
            description
                "Set the local ip (profile local address).";
          }
        }
        case fqdn {
          leaf fqdn {
            type string {
              length "1..253";
            }
            description
                "Set the value of fully qualified domain name, length 1..253.";
          }
        }
        case user-fqdn {
          leaf user-fqdn {
            type string {
              length "1..255";
            }
            description
                "Set the value of user fully qualified domain name, length 1..255.";
          }
        }
      }
    }
  }

  grouping ike-profile-config {
    description
      "Configuration data for ike profile.";
    list profile {
      key "name";
      ordered-by user;
      description
        "Configuration of ike profile.";
      leaf name {
        type string {
          length "1..64";
          pattern "[^`~!#$%^&*+/|{};:\"',\\\\<>?]*" {
            error-message 'cannot include character: `~!#%^&*+\|{};:",/<>?';
          }
        }
        description
          "Set the name of profile.";
      }
      leaf default-psk {
        type string {
          length "3..64";
        }
        description
          "The default pre-share key of ike.";
      }

      container peer-id {
        description
          "Configuration of peer(remote) identify.";
        must 'count(ipv4-address) + count(ipv6-address) + count(fqdn) + count(user-fqdn) + count(key-id) <= 1000' {
          error-message 'peer identity out of limit.';
        }

        list ipv4-address {
          key "ipv4-address";
          ordered-by user;
          description
            "Address configuration of key.";

          leaf ipv4-address {
            type ntos-inet:ipv4-address {
              ntos-ext:nc-cli-shortdesc "<ipv4-address>";
            }
            description
              "Set the ip address.";
          }

          leaf key {
            type string {
              length "3..64";
            }
            description
              "Set the key of ip address.";
          }

          ntos-ext:nc-cli-one-liner;
        }

        list ipv6-address {
          key "ipv6-address";
          ordered-by user;
          description
            "Address configuration of key.";

          leaf ipv6-address {
            type ntos-inet:ipv6-address {
              ntos-ext:nc-cli-shortdesc "<ipv6-address>";
            }
            description
              "Set the ip address.";
          }

          leaf key {
            type string {
              length "3..64";
            }
            description
              "Set the key of ip address.";
          }

          ntos-ext:nc-cli-one-liner;
        }

        list fqdn {
          key "fqdn";
          ordered-by user;
          description
            "fqdn configuration of preshare key.";

          leaf fqdn {
            type string {
              length "1..253";
            }
            description
              "Set the fqdn.";
          }

          leaf key {
            type string {
              length "3..64";
            }
            description
              "Set the key of hostname.";
          }

          ntos-ext:nc-cli-one-liner;
        }

        list user-fqdn {
          key "user-fqdn";
          ordered-by user;
          description
            "user-fqdn configuration of preshare key.";

          leaf user-fqdn {
            type string {
              length "1..255";
            }
            description
              "Set the user-fqdn.";
          }

          leaf key {
            type string {
              length "3..64";
            }
            description
              "Set the key of hostname.";
          }

          ntos-ext:nc-cli-one-liner;
        }

        list key-id {
          key "key-id";
          ordered-by user;
          description
            "key-id configuration of preshare key.";

          leaf key-id {
            type string {
              length "1..255";
            }
            description
              "Set the key-id.";
          }

          leaf key {
            type string {
              length "3..64";
            }
            description
              "Set the key of hostname.";
          }
          ntos-ext:nc-cli-one-liner;
        }
      }
    }
  }

  grouping ike-proposal-config {
    description
      "Configuration data for proposal.";

    list proposal {
      key "name";
      ordered-by user;

      description
        "Configuration of ike proposal.";

      leaf name {
        type string {
          length "1..64";
          pattern "[^`~!#$%^&*+/|{};:\"',\\\\<>?]*" {
            error-message 'cannot include character: `~!#%^&*+\|{};:",/<>?';
          }
        }
        description
          "Set the name of proposal.";
      }

      leaf prf {
        type bits {
          bit md5 {
            description
              "Pseudo-random function md5.";
          }
          bit sha {
            description
              "Pseudo-random function sha1.";
          }
          bit sha-256 {
            description
              "Pseudo-random function sha-256.";
          }
          bit sha-384 {
            description
              "Pseudo-random function sha2-384.";
          }
          bit sha-512 {
            description
              "Pseudo-random function sha2-512.";
          }
        }
        default sha-256;
        description
          "Set the Pseudo-random function algorithm.";
      }

      leaf life-seconds {
        type uint32 {
          range "120..604800";
        }
        units second;
        default "86400";
        description
          "Set the life-seconds of proposal.";
      }

      leaf encrypt-alg {
        type bits {
          bit des {
            description
              "Encryption algorithm des.";
          }
          bit des3 {
            description
              "Encryption algorithm 3des.";
          }
          bit aes-128 {
            description
              "Encryption algorithm aes-128.";
          }
          bit aes-192 {
            description
              "Encryption algorithm aes-192.";
          }
          bit aes-256 {
            description
              "Encryption algorithm aes-256.";
          }
        }
        mandatory true;
        description
          "Set the encryption algorithm of proposal.";
      }

      leaf hash-alg {
        type bits {
          bit md5 {
            description
              "Auth algorithm md5.";
          }
          bit sha {
            description
              "Auth algorithm sha.";
          }
          bit sha-256 {
            description
              "Auth algorithm md5.";
          }
          bit sha-384 {
            description
              "Auth algorithm sha.";
          }
          bit sha-512 {
            description
              "Auth algorithm sha.";
          }
        }
        mandatory true;
        description
          "Set the hash algorithm of proposal.";
      }

      leaf dh-group {
        type bits {
          bit group1 {
            description
              "D-H Group1 (MODP768).";
          }
          bit group2 {
            description
              "D-H Group2 (MODP1024).";
          }
          bit group5 {
            description
              "D-H Group5 (MODP1536).";
          }
          bit group14 {
            description
              "D-H Group14 (MODP2048).";
          }
          bit group15 {
            description
              "D-H Group15 (MODP3072).";
          }
          bit group16 {
            description
              "D-H Group16 (MODP4096).";
          }
        }
        mandatory true;
        description
           "Set the groups of proposal.";
      }

      leaf auth-mode  {
        type enumeration {
          enum preshared-key {
            description
              "preshared-key.";
          }
        }
        default "preshared-key";
        description
          "Set the authentication of proposal.";
      }
      max-elements 1001;
    }
  }

  grouping global-config {
    container dpd {
      leaf interval {
        type uint32 {
          range "10..3600";
        }
        units second;
        default 30;
        ntos-ext:nc-cli-no-name;
        description
          "Set the DPD R-U-THERE interval.";
      }

      leaf retry-interval {
        type uint32 {
          range "2..10";
        }
        units second;
        default 5;
        ntos-ext:nc-cli-no-name;
        description
          "Set the DPD Retry Interval.";
      }

      leaf type {
        type enumeration {
          enum periodic {
            description
              "Periodic send DPD queries at regular intervals.";
          }
          enum idle {
            description
              "idle mode.";
          }
        }
        ntos-ext:nc-cli-no-name;
        description
          "Set the type of DPD.";
      }
      ntos-ext:nc-cli-one-liner;
    }

    container nat-traversal {
      leaf enabled {
        type boolean;
        default "true";
        description
          "Enable or disable nat traversal.";
      }
    }

    container nat {
      leaf keepalive {
        type int32 {
          range "5..3600";
        }
        default "20";
        description
          "NAT keepalive interval in seconds.";
      }
    }
  }

  rpc ipsec-tunnelcfg-show {
    input {
      leaf vrf {
        type string;
        default "main";
        description
          "vrf name.";
      }

      leaf start {
        type uint32;
        description
          "Start offset.";
      }

      leaf end {
        type uint32;
        description
          "End offset.";
      }

      leaf filter-type {
        description
          "Filter-type.";
        type enumeration {
          enum peer-address {
            description
              "Display tunnel config filter by peer-address.";
          }
          enum name {
            description
              "Display tunnel config filter by name.";
          }
          enum type {
            description
              "Display tunnel config filter by type (static/dynamic).";
          }
        }
      }

      leaf filter {
        type string;
        description
          "Filter content (fuzzy query).";
      }

      leaf name {
        type string;
        description
          "Filter content (exact query).";
      }
    }

    output {

      leaf ipsec-tunnelcfg-sum {
        type uint32;
      }

      leaf config-status {
        type uint16;
        description
          "Configuration status: 0-configuring, 1-successfully, 2-failed.";
      }

      leaf error {
        type string;
        description
          "Error message when config failed.";
      }

      list config {
        key "name";

        leaf name {
          type string;
          description
            "Display the name of profile.";
        }

        leaf is-template {
          type boolean;
          description
            "Used to distinguish whether to deliver from the web page of the wizard configuration.";
        }

        leaf wizard-type {
          type enumeration {
            enum l2tp-over-ipsec;
          }
          description
            "Display IPsec scenario type.";
        }

        leaf description {
          type string;
          description
            "Display the description of profile.";
        }

        leaf create-time {
          description
          "Ipsec Profile creation time.";
          type ietf-yang:timestamp;
        }

        leaf version {
          type string;
          description
            "The ike protocol version.";
        }

        leaf enabled {
          type boolean;
          description
            "Enable or disable profile.";
        }

        leaf type {
          type enumeration {
            enum static {
              description
                "Display static type tunnel.";
            }
            enum dynamic {
              description
                "Display dynamic type tunnel.";
            }
          }
          description
            "Display the type of ipsec profile.";
        }

        leaf exchange-mode {
          type enumeration {
            enum main {
              description
                "Display main mode.";
            }
            enum aggressive {
              description
                "Display aggressive mode.";
            }
            enum auto {
              description
                "Display auto mode.";
            }
          }
          description
            "Display the exchange-mode of ipsec profile.";
        }

        leaf autoup {
          type boolean;
          description
            "Display the autoup of ipsec profile.";
        }

        leaf local {
          type string;
        }

        leaf peer-address {
          type string;
        }

        leaf fragmentation-mtu {
          type uint32 {
            range "512..1500";
          }
          description
            "Display the mtu size.";
        }

        leaf tunnel-interface {
          type string;
        }

        leaf life-seconds {
          type uint32 {
            range "60..604800";
          }
          description
            "Display the life-seconds of ipsec proposal.";
        }

        leaf pfs {
          description
            "Display the groups of ipsec proposal.";

          type enumeration {
            enum group1 {
              description
                "D-H Group1 (MODP768).";
            }
            enum group2 {
              description
                "D-H Group2 (MODP1024).";
            }
            enum group5 {
              description
                "D-H Group5 (MODP1536).";
            }
            enum group14 {
              description
                "D-H Group14 (MODP2048).";
            }
            enum group15 {
              description
                "D-H Group15 (MODP3072).";
            }
            enum group16 {
              description
                "D-H Group16 (MODP4096).";
            }
          }
        }

        container local-identity {
          choice local-identity {
            case ipv4-address {
              leaf ipv4-address {
                type union {
                  type ntos-inet:ipv4-address;
                  type empty;
                }
                description
                    "Set the local ipv4-address (profile local address).";
              }
            }
            case ipv6-address {
              leaf ipv6-address {
                type union {
                  type ntos-inet:ipv6-address;
                  type empty;
                }
                description
                    "Set the local ipv6-address (profile local address).";
              }
            }
            case fqdn {
              leaf fqdn {
                type string {
                  length "1..253";
                }
                description
                    "Set the value of fully qualified domain name, length 1..253.";
              }
            }
            case user-fqdn {
              leaf user-fqdn {
                type string {
                  length "1..255";
                }
                description
                    "Set the value of user fully qualified domain name, length 1..255.";
              }
            }
            case key-id {
              leaf key-id {
                type string {
                  length "1..255";
                }
                description
                    "Set the value of key id, length 1..255.";
              }
            }
          }
        }

        leaf check-id {
          type boolean;
          description
            "Enable or disable peer-identity.";
        }

        container peer-identity {
          description
            "Set peer-identity type and value.";

          list ipv4-address {
            key "value";
              description
                "Set the peer ipv4-addresss.";
            leaf value {
              type string;
              description
                "The value of peer ipv4-address";
            }
            ordered-by user;
            ntos-ext:nc-cli-one-liner;
          }

          list ipv6-address {
            key "value";
              description
                "Set the peer ipv6-address.";
            leaf value {
              type string;
              description
                "The value of peer ipv6-address";
            }
            ordered-by user;
            ntos-ext:nc-cli-one-liner;
          }

          list fqdn {
            key "value";
              description
                "Set the peer fqdn.";
            leaf value {
              type string;
              description
                "The value of fully qualified domain name, length 1..253";
            }
            ordered-by user;
            ntos-ext:nc-cli-one-liner;
          }

          list user-fqdn {
            key "value";
              description
                "Set the peer user-fqdn.";
            leaf value {
              type string;
              description
                "The value of user fully qualified domain name, length 1..255";
            }
            ordered-by user;
            ntos-ext:nc-cli-one-liner;
          }

          list key-id {
            key "value";
              description
                "Set the peer key-id.";
            leaf value {
              type string;
              description
                "The value of key id, length 1..255";
            }
            ordered-by user;
            ntos-ext:nc-cli-one-liner;
          }
        }

        container reverse-route {
          leaf enabled {
            type boolean;
            description
              "Display Enable or disable reverse-route.";
          }

          leaf distance {
            type uint32 {
              range "1..255";
            }
            description
              "Display the reverse-route distance.";
          }

          leaf peer {
            type union {
              type ntos-inet:ipv4-address;
              type ntos-inet:ipv6-address;
            }
            description
              "Display the reverse-route peer.";
          }
        }

        container ike-proposal {
          leaf name {
            type string {
              length "1..64";
            }
            description
              "Display the name of proposal.";
          }

          leaf life-seconds {
            type uint32 {
              range "120..604800";
            }
            description
              "Display the life-seconds of proposal.";
          }

          leaf encrypt-alg {
            type string;
            description
              "Display the encryption algorithm of proposal.";
          }

          leaf hash-alg {
            type string;
            description
              "Display the hash algorithm of proposal.";
          }

          leaf prf {
            type string;
            description
              "Display the prf of proposal.";
          }

          leaf dh-group {
            type string;
            description
              "Display the groups of proposal.";
          }

          leaf auth-mode  {
            type string;
            description
              "Display the authentication of proposal.";
          }
        }

        container ike-profile {
          leaf name {
            type string {
              length "1..64";
            }
            description
              "Display the name of ike profile.";
          }
          leaf default-psk {
            type string {
              length "3..64";
            }
            description
              "The default pre-share key of ike.";
          }
          container peer-id {
            description
              "Configuration of peer(remote) identify.";

            list ipv4-address {
              key "ipv4-address";
              ordered-by user;
              description
                "Address configuration of key.";

              leaf ipv4-address {
                type ntos-inet:ipv4-address {
                  ntos-ext:nc-cli-shortdesc "<ipv4-address>";
                }
                description
                  "Set the ip address.";
              }

              leaf key {
                type string {
                  length "3..64";
                }
                description
                  "Set the key of ip address.";
              }

              ntos-ext:nc-cli-one-liner;
            }

            list ipv6-address {
              key "ipv6-address";
              ordered-by user;
              description
                "Address configuration of key.";

              leaf ipv6-address {
                type ntos-inet:ipv6-address {
                  ntos-ext:nc-cli-shortdesc "<ipv6-address>";
                }
                description
                  "Set the ip address.";
              }

              leaf key {
                type string {
                  length "3..64";
                }
                description
                  "Set the key of ip address.";
              }

              ntos-ext:nc-cli-one-liner;
            }

            list fqdn {
              key "fqdn";
              ordered-by user;
              description
                "fqdn configuration of preshare key.";

              leaf fqdn {
                type string {
                  length "1..253";
                }
                description
                  "Set the fqdn.";
              }

              leaf key {
                type string {
                  length "3..64";
                }
                description
                  "Set the key of hostname.";
              }

              ntos-ext:nc-cli-one-liner;
            }

            list user-fqdn {
              key "user-fqdn";
              ordered-by user;
              description
                "user-fqdn configuration of preshare key.";

              leaf user-fqdn {
                type string {
                  length "1..255";
                }
                description
                  "Set the user-fqdn.";
              }

              leaf key {
                type string {
                  length "3..64";
                }
                description
                  "Set the key of hostname.";
              }

              ntos-ext:nc-cli-one-liner;
            }

            list key-id {
              key "key-id";
              ordered-by user;
              description
                "key-id configuration of preshare key.";

              leaf key-id {
                type string {
                  length "1..255";
                }
                description
                  "Set the key-id.";
              }

              leaf key {
                type string {
                  length "3..64";
                }
                description
                  "Set the key of hostname.";
              }
              ntos-ext:nc-cli-one-liner;
            }
          }
        }

        container ipsec-proposal {
          leaf name {
            type string {
                length "1..64";
            }
            description
              "Display the name of ipsec proposal.";
          }

          leaf protocol {
            type string;
            description
              "Display the protocol of ipsec profile.";
          }

          leaf encap-mode {
            type enumeration {
              enum tunnel {
                description
                    "Set the tunnel mode.";
              }
              enum transport {
                description
                    "Set the transport mode.";
              }
            }
            description
              "Display the encap-mode of ipsec profile.";
          }

          leaf esp-encrypt-alg {
            type string;
            description
              "Display the encryption algorithm of ipsec proposal.";
          }

          leaf esp-auth-alg {
            type string;
            description
              "Display the hash algorithm of proposal.";
          }
        }

        container dpd {
          leaf interval {
            type uint32;
            description
              "Display the DPD R-U-THERE interval.";
          }

          leaf retry-interval {
            type uint32;
            description
              "Display the DPD Retry Interval.";
          }

          leaf type {
            type enumeration {
              enum periodic {
                description
                  "Periodic send DPD queries at regular intervals.";
              }
              enum idle {
                description
                  "Idle send DPD queries at regular intervals.";
              }
            }
            description
              "Display the type of DPD.";
          }
        }

        leaf key {
          type string;
          description
            "Display the key.";
        }

        list rule {
          description
            "Display the ip address of proxyid.";

          key "local remote";
          ordered-by user;

          leaf local {
            type string;
            description
              "Display the local address of proxyid.";
          }

          leaf remote {
            type string;
            description
              "Display the remote address of proxyid.";
          }
        }
      }
    }
    ntos-ext:nc-cli-show "ipsec profiles";
  }

  rpc ipsec-globalcfg-show {
    output {
      container global-cfg {

        container nat-traversal {
          leaf enabled {
              type boolean;
              default "true";
              description
                  "Enable or disable nat traversal.";
          }
        }

        container nat {
          leaf keepalive {
            type int32 {
              range "5..3600";
            }
            description
              "NAT keepalive interval in seconds.";
          }
        }

        container anti-replay {
          leaf check {
            type boolean;
            default true;
            description
              "Enable ipsec anti-replay checking.";
          }

          leaf window-size {
            type int32;
            description
              "Set the size of ipsec anti-replay window.";
          }
        }

        leaf df-bit {
          type string;
          description
            "Set the df-bit of ipsec.";
        }

        leaf inbound-sp {
          type boolean;
            description
              "Enable ipsec inbound-sp.";
        }

        leaf prefrag {
          type boolean;
            description
              "Enable ipsec prefrag.";
        }

        leaf spd-hash-bits-src {
          type uint32;
          description
            "Set the ipsec spd-hash-bits src-bits.";
        }

        leaf spd-hash-bits-dst {
          type uint32;
          description
            "Set the ipsec spd-hash-bits dst-bits.";
        }
      }
    }
    ntos-ext:nc-cli-show "ipsec global";
  }

  rpc ike-load-config {
    ntos-ext:nc-cli-cmd "ike load config";
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "Set ike configuration.";

    container ike {
      description
        "Configuration of ike.";

      uses ike-proposal-config;
      uses ike-key-config;
      uses ike-profile-config;
      uses identity-config;
      uses global-config;
    }
  }

  rpc get-ipsec-policies {
    description
      "Get ipsec policies";    
    input {
      leaf-list ipsec-prof-name {
        type string;
        description
          "Name of ipsec profile.";
      }
      leaf-list ike-prop-name {
        type string;
        description
          "Name of ike proposal.";
      } 
      leaf-list ipsec-prop-name {
        type string;
        description
          "Name of ipsec proposal.";
      }
      leaf-list proxyid-name {
        type string;
        description
          "Name of ipsec proxyid.";
      }

      leaf-list ipv4-address-psk {
          type string;
          description
            "IPv4 address id value.";
      }
      leaf-list ipv6-address-psk {
          type string;
          description
            "IPv6 address id value.";
      }
      leaf-list fqdn-id-psk {
          type string;
          description
            "FQDN id value.";
      }
      leaf-list user-fqdn-id-psk {
          type string;
          description
            "USER-FQDN id value.";
      }
      leaf-list keyid-id-psk {
          type string;
          description
            "KEYID id value.";
      }
    }

    output {
      uses ntos-cmd:long-cmd-output;
    }

    ntos-ext:nc-cli-show "ipsec-policy";
  }

  rpc get-security-policy-deps {
    description
      "Get security-policy dependences";
    input {
      leaf-list security-zone-name {
        type string;
        description
          "Name of security-zone.";
      }
      leaf-list network-obj-name {
        type string;
        description
          "Name of network-obj.";
      }
      leaf-list security-policy-name {
        type string;
        description
          "Name of security-policy.";
      }
    }

    output {
      uses ntos-cmd:long-cmd-output;
    }
  }

  rpc check-ip-prefix-has-multipath {
    ntos-ext:nc-cli-cmd "ipsec check-ip-prefix-has-multipath";

    description
      "Check if an prefix has multiple nexthop with VTI.";    
    
    input {
      leaf ipsec-prof-name {
        type string;
        description
          "Name of ipsec profile.";
      }
      leaf vti-name {
        type string;
        description
          "Name of vti name.";
      }
      leaf remote-net {
        type ntos-inet:ip-prefix;
        description
          "An IPv4 or IPv6 prefix: address and CIDR mask.";
      }
    }

    output {
        leaf result {
          type string;
          description
            "The result of RPC method.";
          ntos-ext:nc-cli-stdout;
      }
    }
  }

}
