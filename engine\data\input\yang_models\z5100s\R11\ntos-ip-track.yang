module ntos-ip-track {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:ip-track";
  prefix ntos-ip-track;

  import ntos {
    prefix ntos;
  }

  import ntos-types {
    prefix ntos-types;
  }

  import ntos-inet-types {
    prefix ntos-inet;
  }

  import ntos-extensions {
    prefix ntos-ext;
  }
  organization
    "Ruijie Networks Co.,Ltd";

  contact
    "Tel: 4008-111-000
    E-mail: <EMAIL>";

  description
    "Ruijie NTOS Reliable Network Service.";

  revision 2022-09-29 {
    description
      "Modifed track config of delay";
    reference "";
  }
  revision 2023-12-01 {
    description
      "Add support DNS protocol detection";
    reference "";
  }
  revision 2024-04-11 {
    description
      "Adapt to HA";
    reference "";
  }
  revision 2024-08-21 {
    description
      "Support TCP/HTTP/HTTPS protocol detection";
    reference "";
  }

  identity ip-track {
    base ntos-types:SERVICE_LOG_ID;
    description
      "Reliable network service.";
  }

  typedef config-source-type {
    description "Config source type.";
    type enumeration {
      enum TRACK {
        description "Config source is track.";
      }
      enum DNS-PROXY {
        description "Config source is dns-proxy.";
      }
      enum PBR {
        description "Config source is pbr.";
      }
      enum INTF {
        description "Config source is interface.";
      }
      enum STATIC-ROUTE {
        description "Config source is static routing.";
      }
      enum HA {
        description "Config source is ha.";
      }
    }
  }

  grouping rns-detect-url {
    leaf ip-address {
      type string {
        pattern "[^`!@$(){};,'\\\\<>|#]*" {
          error-message "Can not include character: `^$!@(){};,'<>\\|#";
        }
      }
      description "The specific URL.";
      ntos-ext:nc-cli-no-name;
    }
  }

  grouping rns-detect-ip {
    leaf ip-address {
      type ntos-inet:ip-address;
      
      description
        "The destination ip address which will be detected if it is reachable.";
      ntos-ext:nc-cli-no-name;
      ntos-ext:nc-cli-group "destination";
    }
  }

  grouping rns-detect-param {
    description
      "The grouping of RNS detecting parameters.";

/*will support
    leaf hostname {
      type string;
      description
        "The destination host which will be detected if it is reachable.";

      ntos-ext:nc-cli-group "destination";
    }
    leaf name-server {
      type ntos-inet:ip-address;
      description
        "The DNS name server used to resolve destination host name.";
    }
*/

    leaf source-ipaddr {
      type ntos-inet:ip-address;
      description
      "The source ip address used to do detect.";
    }

    leaf out-interface {
      type ntos-types:ifname;
      ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos-interface:interface/*/*[local-name()='name']";
      description
        "The name of the out interface.";
    }

    leaf next-hop {
      type ntos-inet:ip-address;
      description
        "The ip address of the next hop.";
    }
  }

  grouping rns-time-param {
    leaf enabled {
      type boolean;
      default "true";

      description
        "Enable/Disable rns entry.";
    }
    leaf frequency {
      description
        "The interval times between two detection.";
      type uint32 {
        range "1000..604800000" {
          error-message
            "The frequency must >= 1000 and <= 604800000.";
        }
      }
      must "count(../timeout) = 0 or . >= ../timeout" {
        error-message "The frequency must >= timeout";
      }

      default "6000";
      units millisecond;
    }

    leaf timeout {
      description
        "The timeout times of one detection.";
      type uint32 {
        range "1000..604800000" {
          error-message
            "The timeout must >= 1000 and <= 604800000.";
        }
      }
      must "count(../threshold) = 0 or . >= ../threshold" {
        error-message "The timeout must >= threshold";
      }

      units millisecond;
    }

    leaf threshold {
      description
        "The threshold times of one detection.";
      type uint32 {
        range "0..60000" {
          error-message
            "The threshold must >= 0 and <= 60000.";
        }
      }

      units millisecond;
    }

    leaf retry-times {
      description
        "Indicate maximum number of failures.";
      type uint32 {
        range "1 .. 3600";
      }

      default "4";
    }

    leaf resume-times {
      description
        "Indicate maximum number of recovery.";
      type uint8 {
        range "1 .. 200";
      }

      default "3";
    }
  }

  grouping rns-schedule-start-time {
    container start-time {
      description
      "RNS start time configuration.";
/*will support late
      container exact-time {
        description
          "The exact time RNS will start.";
        ntos-ext:nc-cli-group "start-time";

        leaf time {
          description "Time.";
          type string {
                pattern '\d{2}:\d{2}:\d{2}';
                ntos-ext:nc-cli-shortdesc "[HH:MM:SS](HH:MM:SS)";
          }

          ntos-ext:nc-cli-no-name;
        }

        leaf day {
          description "Day.";
          type uint16 {
            range "1..31" {
              error-message
                "The date must >= 1 and <= 31.";
            }
          }
          ntos-ext:nc-cli-no-name;
        }

        leaf month {
          description "Month.";

          type uint16 {
            range "1..12" {
              error-message
                "The date must >= 1 and <= 12.";
            }
          }
          ntos-ext:nc-cli-no-name;
        }
      }
*/
      leaf pending {
        description "Start time not defined.";
        type empty;
        ntos-ext:nc-cli-group "start-time";
      }

      leaf now {
        description "Start now.";
        type empty;
        ntos-ext:nc-cli-group "start-time";
      }
/*will support late
      leaf after {
        description "Start after some times.";
        type string {
              pattern '\d{2}:\d{2}:\d{2}';
              ntos-ext:nc-cli-shortdesc "[HH:MM:SS](HH:MM:SS)";
        }
        ntos-ext:nc-cli-group "start-time";
      }

      leaf recurring {
        description "Start at the same time every day.";
        type empty;
      }
*/
    }
  }

  grouping rns-config {
    description
      "The grouping of reliable network service parameters.";

    list rns {
      key "name";
      must "count(icmp-echo | dns | tcp-connect | http | https) = 1";
      description
        "Log collection configuration.";

      leaf name {
        type ntos-types:ntos-obj-name-type;
        description
          "The name of reliable netw service object.";

        ntos-ext:nc-cli-no-name;
      }
      leaf rns-weight {
        description
          "Indicate the importance of RNS instances in measuring link quality.";
        type uint8 {
          range "1 .. 255" {
            error-message
                "The rns weight must >= 1 and <= 255.";
          }
        }
        default "1";
      }

      choice rns-choice {
        case icmp-echo {
          container icmp-echo {
            description
              "Use icmp-echo to do detect.";
            uses rns-detect-param;
            uses rns-detect-ip;
            leaf name-server {
              type ntos-inet:ip-address;
              description
                "The DNS name server used to resolve destination host name.";
            }
          }
        }

        case tcp-connect {
          container tcp-connect {
            uses rns-detect-param;
            uses rns-detect-ip;
            leaf port {
              type uint16 {
                range "1..65535" {
                  error-message
                    "The port must >= 1 and <= 65535.";
                }
              }
              description
                "The TCP port used to connect.";
            }
            leaf name-server {
              type ntos-inet:ip-address;
              description
                "The DNS name server used to resolve destination host name.";
            }
          }
        }

        case dns {
          container dns {
            description
              "Use dns to do detect.";
            uses rns-detect-param;
            uses rns-detect-ip;
            leaf domain-name {
              type string;
              description
                "The domain-name which will be detected.";
              ntos-ext:nc-cli-group "destination";
              default "secloud1.ruijie.com.cn";
            }
            leaf port {
              type uint16 {
                range "1..65535" {
                  error-message
                    "The port must >= 1 and <= 65535.";
                }
              }
              default "53";
              description
                "The dns port used to connect.";
            }
          }
        }
        case http {
          container http {
            description
              "Use http protocol to do detect.";
            uses rns-detect-param;
            uses rns-detect-url;
            leaf name-server {
              type ntos-inet:ip-address;
              description
                "The DNS name server used to resolve destination host name.";
            }
            leaf redirect-num {
              type uint8 {
                range "0..5" {
                  error-message
                    "The port must >= 0 and <= 5.";
                }
              }
              default "0";
              description
                "The number of redirects.";
            }
          }
        }
        case https {
          container https {
            description
              "Use https protocol to do detect.";
            uses rns-detect-param;
            uses rns-detect-url;
            leaf name-server {
              type ntos-inet:ip-address;
              description
                "The DNS name server used to resolve destination host name.";
            }
            leaf redirect-num {
              type uint8 {
                range "0..5" {
                  error-message
                    "The port must >= 0 and <= 5.";
                }
              }
              default "0";
              description
                "The number of redirects.";
            }
          }
        }
      }

      uses rns-time-param;
/*
      container schedule {
        container life {
          leaf forever {
            description
              "The RNS object will work forever until this schedu be deleted.";
            type empty;
            ntos-ext:nc-cli-group "schedule-life";
          }

          container secondes {
            leaf time{
              type uint32;
              description
                "Time of seconds the RNS object will work for.";

              units second;
              ntos-ext:nc-cli-no-name;
            }

            ntos-ext:nc-cli-group "schedule-life";
          }
        }

        uses rns-schedule-start-time;
      }
*/
    }
  }

  grouping track-object-configuration {
    list object {
      key "name";
      leaf name {
        type ntos-types:ntos-obj-name-type;
        description
          "The name of the track object.";

        ntos-ext:nc-cli-no-name;
      }

      leaf not {
        type empty;
        description
              "Expect state of this track object is down";
      }
    }
  }

  grouping track-config {
    description
      "The grouping of track parameters.";

    container track {
      leaf enabled {
        type boolean;
        default "false";

        description
          "Enable/Disable tracks on this VRF except those created by HA.";
      }

      list rule {
        key "name";

        leaf name {
          type ntos-types:ntos-obj-name-type;
          description
            "The name of the track object.";

          ntos-ext:nc-cli-no-name;
        }

        /*
        container list-boolean {
          ntos-ext:nc-cli-group "track-type";
          container and {
            ntos-ext:nc-cli-group "boolean-type";
            uses track-object-configuration;
          }

          container or {
            ntos-ext:nc-cli-group "boolean-type";
            uses track-object-configuration;
          }
        }
*/
        leaf enabled {
          status deprecated;
          ntos-ext:nc-cli-hidden;
          type boolean;
          default "true";
        }

        leaf interface {
          type ntos-types:ifname;
          ntos-ext:nc-cli-completion-xpath
            "/ntos:config/ntos:vrf/ntos-interface:interface/*[local-name()='physical']/*[local-name()='name'] |
            /ntos:config/ntos:vrf/ntos-interface:interface/*[local-name()='lag']/*[local-name()='name']";

          description
            "The Interface which track object is effected.";
        }

        leaf list-boolean {
          status deprecated;
          ntos-ext:nc-cli-hidden;
          type enumeration{
            enum and {
              description
                "and";
            }

            enum or {
              description
                "or";
            }
          }
        }

        uses rns-config;

        container delay {
          status deprecated;
          ntos-ext:nc-cli-hidden;
          leaf up {
            type uint32 {
              range "0..180000" {
                error-message
                  "The value of up must >= 0 and <= 180000.";
              }
            }

            default "0";
            units "millisecond";
            description
              "Delay time that track state change from down to up.";
          }

          leaf down {
            type uint32 {
              range "0..180000" {
                error-message
                  "The value of down must >= 0 and <= 180000.";
              }
            }

            default "0";
            units "millisecond";
            description
              "Delay time that track state change from up to down.";
          }
        }
        leaf least-activenum {
          description
            "The least numbers of activing detection instances.";
          type uint8 {
            range "1..16" {
            error-message
              "The activenum must >= 1 and <= 16.";
            }
          }
          default "1";
        }

        leaf track-threshold {
          description
            "Track unavailable when total weight of abnormal RNS instances exceeds the threshold.";
          type uint8 {
            range "1..255" {
            error-message
              "The track threshold must >= 1 and <= 255.";
            }
          }
          default "1";
        }

        leaf config-source {
          type config-source-type;
          default "TRACK";
          description
            "Track config source.";
        }

        leaf state-detection {
          type enumeration {
            enum "LEAST-ACTIVENUM";
            enum "WEIGHT-THRESHOLD";
          }
          default "LEAST-ACTIVENUM";
          description
            "Track state detection criteria.";
        }

        leaf type {
          type enumeration {
            enum interface {
              description
                "interface type.";
            }

            enum rns {
              description
                "rns type.";
            }
          }
          default rns;
        }

        leaf thread-weight {
          type uint32 {
            range "1..255";
          }
          default "255";
          description
            "thread-weight.";
        }

        list track-interface {
          key "ifname";
          ordered-by user;

          leaf ifname {
            type ntos-types:ifname;
            description
              "track-interface.";
            ntos-ext:nc-cli-completion-xpath
              "/ntos:config/ntos:vrf/ntos-interface:interface/*[local-name()='physical']/*[local-name()='name'] |
              /ntos:config/ntos:vrf/ntos-interface:interface/*[local-name()='lag']/*[local-name()='name']";
          }
          ntos-ext:nc-cli-one-liner;
        }
      }
    }
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "Configuration data for the reliable network service.";
    uses track-config;
  }

  augment "/ntos:state/ntos:vrf" {
    description
      "Operational state for the reliable network service.";
    uses track-config;
  }

  rpc show-track {
    description
      "Display configuration of the track object.";

      input {
        leaf name {
          type ntos-types:ntos-obj-name-type;
          description
            "The name of the track object.";
        }
        leaf page {
          type uint16;
          description
            "The page of the track object.";
        }
        leaf type {
          type enumeration {
            enum rns {
              description
                  "for track.";
            }
            enum interface {
              description
                  "for ha.";
            }
          }
          description
            "Type : rns or interface.";
        }
        leaf reference {
          description
            "The reference information of track by policy";
          type ntos-types:ntos-obj-name-type;
        }
        leaf config-source {
          type config-source-type;
          description
            "Track config source.";
        }
      }

    output {
      leaf buffer {
        type string;
        description
          "Configuration of the track object.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "track";
  }

  rpc get-track-config {
    description
      "Get configuration of the track object.";

      input {
        leaf name {
          type ntos-types:ntos-obj-name-type;
          description
            "The name of the track object.";
        }

        leaf start {
          type uint16;

          description
            "The index of page start.";
        }

        leaf end {
          type uint16;

          description
            "The index of page end.";
        }

        leaf config-source {
          type config-source-type;
          description
            "Track config source.";
        }

        leaf filter {
          type string;
          description
            "The content of search.";
        }

        leaf enabled {
          type empty;
          description
            "Track running status.";
        }

        leaf type {
          type enumeration {
            enum rns {
              description
                  "track for web.";
            }
            enum interface {
              description
                  "ha for web.";
            }
          }
          description
            "Type for web.";
        }

        leaf reference {
          description
            "The reference information of track by policy.";
          type ntos-types:ntos-obj-name-type;
        }
      }

    output {
      leaf buffer {
        type string;
        description
          "Configuration of the track object.";
      }
    }
  }
  rpc get-track-config-reference {
    description
      "Get configuration of the track object.";

      input {
        leaf reference {
          description
            "The reference information of track by policy";
          type ntos-types:ntos-obj-name-type;
        }
      }

    output {
      leaf buffer {
        type string;
        description
          "Configuration of the track ref object.";
      }
    }
  }
  
/*
  rpc show-track-client {
    description
      "Display configuration of the track object.";

      input {
        leaf name {
          type ntos-types:ntos-obj-name-type;
          description
            "The name of the track object.";

          ntos-ext:nc-cli-no-name;
        }
      }

    output {
      leaf buffer {
          type string;
        description
          "Configuration of the track object.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "track client";
  }
  */
  rpc show-rns-collect-statis {
    description
      "Display collect statistics of the rns object.";

      input {
        leaf name {
          type ntos-types:ntos-obj-name-type;
          description
            "The name of the rns object.";

          ntos-ext:nc-cli-no-name;
        }
      }

    output {
      leaf buffer {
        type string;
        description
          "Collect statistics of the rns object.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "track rns-collect-statis";
  }

  rpc show-rns-operate-state {
    description
      "Display operate state of the rns object.";

      input {
        leaf name {
          type ntos-types:ntos-obj-name-type;
          description
            "The name of the rns object.";

          ntos-ext:nc-cli-no-name;
        }
      }

    output {
      leaf buffer {
        type string;
        description
          "Operate state of the rns object.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "track rns-operate-state";
  }

  rpc set-debug-level {
    input {
      leaf module {
        type enumeration {
          enum "track";
          enum "rns";
        }
      }

      leaf level {
        type enumeration {
          enum "FATAL";
          enum "ERROR";
          enum "WARN";
          enum "INFO";
          enum "DEBUG";
          enum "TRACE";
          enum "DISABL";
        }
      }
    }

    output {
      leaf buffer {
        type string;

        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-cmd "track set-debug";
  }
}