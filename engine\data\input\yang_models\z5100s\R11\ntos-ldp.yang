module ntos-ldp {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:ldp";
  prefix ntos-ldp;

  import ntos {
    prefix ntos;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-commands {
    prefix ntos-cmd;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-routing {
    prefix ntos-rt;
  }
  import ntos-routing-types {
    prefix ntos-rt-types;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-interface {
    prefix ntos-interface;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "RUIJIE NTOS LDP.";

  revision 2019-06-12 {
    description
      "Add logging configuration.";
    reference "";
  }
  revision 2019-01-14 {
    description
      "Initial version.";
    reference "";
  }

  identity ldp {
    base ntos-types:SERVICE_LOG_ID;
    description
      "Routing LDP protocol.";
    ntos-ext:nc-cli-identity-name "routing mpls ldp";
  }

  grouping hello-config {
    description
      "LDP Link Hellos.";

    container hello {
      description
        "LDP Link Hellos.";

      leaf holdtime {
        type uint16 {
          range "1..65535";
        }
        default "15";
        description
          "Hello holdtime in seconds.";
      }

      leaf interval {
        type uint16 {
          range "1..65535";
        }
        default "5";
        description
          "Hello interval in seconds.";
      }
    }
  }

  grouping interface-config {
    description
      "Enable LDP on an interface.";

    list interface {
      key "name";
      description
        "Enable LDP on an interface.";

      leaf name {
        type ntos-types:ifname;
        must "count(/ntos:config/ntos:vrf[ntos:name='main']/ntos-interface:interface/*/*[local-name()='name'][text()=current()]) = 1" {
          error-message "The name must reference a configured interface in the main vrf.";
        }
        description
          "Interface name.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:config/ntos:vrf[ntos:name='main']/ntos-interface:interface/*/*[local-name()='name']";
      }
    }
  }

  grouping label-ipv4-config {
    description
      "Configure label control and policies.";

    container label {
      description
        "Configure label control and policies.";

      container local {
        description
          "Local label control and policies.";

        container advertise {
          description
            "Configure outbound label advertisement control.";

          leaf for {
            type ntos-rt-types:v4-access-list-name;
            description
              "IP access-list for destination prefixes.";
          }

          leaf to {
            type ntos-rt-types:v4-access-list-name;
            description
              "IP Access-list specifying controls on LDP Peers.";
          }

          container explicit-null {
            presence "Configure explicit-null advertisement.";
            description
              "Configure explicit-null advertisement.";

            leaf for {
              type ntos-rt-types:v4-access-list-name;
              description
                "IP access-list for destination prefixes.";
            }
          }
        }

        container allocate {
          must 'count(for) + count(host-routes) <= 1' {
            error-message "for and host-routes are exclusive.";
          }
          description
            "Configure label allocation control.";
          ntos-ext:nc-cli-exclusive;

          leaf for {
            type ntos-rt-types:v4-access-list-name;
            description
              "IP access-list.";
          }

          leaf host-routes {
            type empty;
            description
              "Allocate local label for host routes only.";
          }
        }
      }

      container remote {
        description
          "Remote/peer label control and policies.";

        container accept {
          description
            "Configure inbound label acceptance control.";

          leaf for {
            type ntos-rt-types:v4-access-list-name;
            description
              "IP access-list for destination prefixes.";
          }

          leaf from {
            type ntos-rt-types:v4-access-list-name;
            description
              "Neighbor from whom to accept label advertisement.";
          }
        }
      }
    }
  }

  grouping label-ipv6-config {
    description
      "Configure label control and policies.";

    container label {
      description
        "Configure label control and policies.";

      container local {
        description
          "Local label control and policies.";

        container advertise {
          description
            "Configure outbound label advertisement control.";

          leaf for {
            type ntos-rt-types:v6-access-list-name;
            description
              "IP access-list for destination prefixes.";
          }

          leaf to {
            type ntos-rt-types:v6-access-list-name;
            description
              "IP Access-list specifying controls on LDP Peers.";
          }

          container explicit-null {
            presence "Configure explicit-null advertisement.";
            description
              "Configure explicit-null advertisement.";

            leaf for {
              type ntos-rt-types:v6-access-list-name;
              description
                "IP access-list for destination prefixes.";
            }
          }
        }

        container allocate {
          must 'count(for) + count(host-routes) <= 1' {
            error-message "for and host-routes are exclusive.";
          }
          description
            "Configure label allocation control.";
          ntos-ext:nc-cli-exclusive;

          leaf for {
            type ntos-rt-types:v6-access-list-name;
            description
              "IP access-list.";
          }

          leaf host-routes {
            type empty;
            description
              "Allocate local label for host routes only.";
          }
        }
      }

      container remote {
        description
          "Remote/peer label control and policies.";

        container accept {
          description
            "Configure inbound label acceptance control.";

          leaf for {
            type ntos-rt-types:v6-access-list-name;
            description
              "IP access-list for destination prefixes.";
          }

          leaf from {
            type ntos-rt-types:v6-access-list-name;
            description
              "Neighbor from whom to accept label advertisement.";
          }
        }
      }
    }
  }

  grouping session-config {
    description
      "Configure session parameters.";

    leaf session-holdtime {
      type uint16 {
        range "15..65535";
      }
      default "180";
      description
        "Session holdtime in seconds.";
    }
  }

  grouping ldp-config {
    description
      "LDP configuration.";

    leaf enabled {
      type boolean;
      default "true";
      description
        "Enable or disable LDP.";
    }

    leaf router-id {
      type ntos-inet:ipv4-address;
      description
        "LSR Id in IPv4 address format.";
    }

    container discovery {
      description
        "Discovery parameters.";
      uses hello-config;
    }

    container dual-stack {
      description
        "Configure dual stack parameters.";

      leaf cisco-interop {
        type boolean;
        default "false";
        description
          "Use Cisco non-compliant format to send and interpret the Dual-Stack
           capability TLV.";
      }

      leaf transport-preference {
        type enumeration {
          enum ipv4 {
            description
              "IPv4.";
          }
          enum ipv6 {
            description
              "IPv6.";
          }
        }
        default "ipv6";
        description
          "Configure preferred address family for TCP transport connection with
           neighbor.";
      }
    }

    list neighbor {
      key "neighbor-id";
      description
        "Configure neighbor parameters.";

      leaf neighbor-id {
        type ntos-inet:ipv4-address;
        description
          "LDP Id of neighbor.";
      }

      leaf password {
        type string {
          length "1..max";
        }
        description
          "The password.";
      }

      container ttl-security {
        description
          "LDP ttl security check.";
        ntos-ext:nc-cli-exclusive;

        leaf hops {
          type uint8 {
            range "1..255";
          }
          must "../disable = 'false'" {
            error-message "hops cannot be set when disable is set.";
          }
          description
            "Maximum number of IP hops.";
        }

        leaf disable {
          type boolean;
          default "false";
          description
            "Disable ttl security.";
        }
      }
    }

    container address-family {
      description
        "Configure Address Family and its parameters.";

      container ipv4 {
        presence "Makes IPv4 available.";
        description
          "IPv4.";

        container discovery {
          description
            "Discovery parameters.";
          uses hello-config;

          leaf transport-address {
            type ntos-inet:ipv4-address;
            mandatory true;
            description
              "Transport address for TCP connection.";
          }
        }
        uses interface-config;
        uses label-ipv4-config;
        uses session-config;
      }

      container ipv6 {
        presence "Makes IPv6 available.";
        description
          "IPv6.";

        container discovery {
          description
            "Discovery parameters.";
          uses hello-config;

          leaf transport-address {
            type ntos-inet:ipv6-address;
            mandatory true;
            description
              "Transport address for TCP connection.";
          }
        }
        uses interface-config;
        uses label-ipv6-config;
        uses session-config;
      }
    }
  }

  grouping ldp-logging-config {
    description
      "OSPF logging configuration.";

    leaf enabled {
      type boolean;
      default "true";
      description
        "Enable/disable MPLS LDP logging configuration.";
    }

    leaf discovery-hello {
      type enumeration {
        enum send {
          description
            "Log sent messages.";
        }
        enum receive {
          description
            "Log received messages.";
        }
        enum both {
          description
            "Log all messages.";
        }
      }
      description
        "Direction of discovery messages to log.";
    }

    leaf errors {
      type boolean;
      default "true";
      description
        "Log errors.";
    }

    leaf events {
      type boolean;
      default "false";
      description
        "Log event information.";
    }

    leaf labels {
      type boolean;
      default "false";
      description
        "Log label allocation information.";
    }

    container message {
      presence "Enable configuring LDP message information to log.";
      description
        "Log LDP message information.";

      leaf direction {
        type enumeration {
          enum send {
            description
              "Log sent messages.";
          }
          enum receive {
            description
              "Log received messages.";
          }
          enum both {
            description
              "Log all messages.";
          }
        }
        default "both";
        description
          "Direction of messages to log.";
      }

      leaf detail {
        type boolean;
        default "false";
        description
          "Log message including periodic Keep Alives.";
      }
    }

    leaf zebra {
      type boolean;
      default "false";
      description
        "Log zebra information.";
    }
  }

  rpc show-mpls-ldp {
    description
      "Show MPLS LDP information.";
    input {
      must 'count(*) = 1' {
        error-message "Command takes one (and only one) parameter.";
      }

      container discovery {
        presence "Discovery.";
        description
          "Discovery Hello Information.";
        ntos-ext:nc-cli-group "ldp-subcommand";

        leaf detail {
          type empty;
          description
            "Show detailed information.";
        }
      }

      leaf interface {
        type empty;
        description
          "Interface information.";
        ntos-ext:nc-cli-group "ldp-subcommand";
      }

      leaf capabilities {
        type empty;
        description
          "Display neighbor capability information.";
        ntos-ext:nc-cli-group "ldp-subcommand";
      }

      container neighbor {
        must 'count(capabilities) + count(detail) <= 1' {
          error-message "'capabilities' and 'detail' are exclusive.";
        }
        presence "Neighbor.";
        description
          "Neighbor information.";
        ntos-ext:nc-cli-group "ldp-subcommand";

        leaf lsr-id {
          type ntos-inet:ipv4-address;
          description
            "OSPF routing table.";
          ntos-ext:nc-cli-no-name;
        }

        leaf capabilities {
          type empty;
          description
            "Display neighbor capability information.";
        }

        leaf detail {
          type empty;
          description
            "Show detailed information.";
        }
      }

      container binding {
        must 'count(ipv4) + count(ipv6) <= 1' {
          error-message "'ipv4' and 'ipv6' are exclusive.";
        }
        presence "Binding.";
        description
          "Label Information Base (LIB) information.";
        ntos-ext:nc-cli-group "ldp-subcommand";

        leaf prefix {
          type union {
            type ntos-inet:ipv4-prefix;
            type ntos-inet:ipv6-prefix;
          }
          description
            "Destination prefix.";
          ntos-ext:nc-cli-no-name;
        }

        leaf longer-prefixes {
          type empty;
          must '../prefix' {
            error-message "Define a prefix to use 'longer-prefixes'";
          }
          description
            "Include longer matches.";
        }

        leaf local-label {
          type uint32 {
            range "0..1048575";
          }
          description
            "Locally assigned label value.";
        }

        leaf remote-label {
          type uint32 {
            range "0..1048575";
          }
          description
            "Match remotely assigned label values.";
        }

        leaf neighbor {
          type ntos-inet:ipv4-address;
          description
            "Display labels from LDP neighbor.";
        }

        leaf detail {
          type empty;
          description
            "Show detailed information.";
        }

        leaf ipv4 {
          type empty;
          description
            "IPv4 Address Family.";
        }

        leaf ipv6 {
          type empty;
          description
            "IPv6 Address Family.";
        }
      }
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-ext:feature "product";
    ntos-ext:nc-cli-show "mpls ldp";
    ntos-api:internal;
  }

  augment "/ntos:config/ntos:vrf/ntos-rt:routing/ntos-rt:mpls" {
    description
      "MPLS configuration.";

    container ldp {
      must "../../../../ntos:vrf/ntos:name = 'main'" {
        error-message "LDP is only supported in main vrf.";
      }
      presence "Makes LDP available.";
      description
        "LDP configuration.";
      ntos-ext:feature "product";
      uses ldp-config;
    }
  }

  augment "/ntos:state/ntos:vrf/ntos-rt:routing/ntos-rt:mpls" {
    description
      "MPLS state.";

    container ldp {
      presence "Makes LDP available.";
      description
        "LDP operational state data.";
      ntos-ext:feature "product";
      uses ldp-config;
    }
  }

  augment "/ntos:config/ntos-rt:routing/ntos-rt:logging/ntos-rt:mpls" {
    description
      "Common LDP routers logging configuration.";

    container ldp {
      presence "Make LDP common logging configuration available.";
      description
        "Common LDP routers logging configuration.";
      uses ldp-logging-config;
    }
  }

  augment "/ntos:state/ntos-rt:routing/ntos-rt:logging/ntos-rt:mpls" {
    description
      "Common LDP routers logging operational state.";

    container ldp {
      presence "Make LDP common logging state available.";
      description
        "Operational logging state common to all LDP routers.";
      uses ldp-logging-config;
    }
  }
}
