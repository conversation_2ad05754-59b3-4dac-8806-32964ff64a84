module ntos-mem {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:system:mem";
  prefix ntos-mem;
  
  import ntos {
    prefix ntos;
  }
  
  import ntos-dm-types {
    prefix ntos-dm;
  }

  import ntos-system {
    prefix ntos-system;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS system memory monitoring module.";

  revision 2022-09-03 {
    description
      "Initial version.";
    reference "";
  }

  grouping sys-mem-config {
    description
      "The grouping for memory monitoring configuration of the system.";

  leaf dev-index {
      type uint32;
      config false;
      description
      "The index that uniquely represents a device. In stacking environments, there are multiple devices.
          This value will indicate the device index. When in general single device environments, this value is 0.";
  }

    leaf warning-threshold {
      type uint32 {
          range "15..100";
      }
      default "95";
      description
      "The warning threshold of the memory utilization. 
        When the curent memory utilization exceeds the threshold, a warning notification will be sent.";
  }
    
  leaf critical-threshold {
      type uint32 {
          range "15..100";
      }
      default "95";
      description
      "The critical threshold of the memory utilization. 
        When the curent memory utilization exceeds the threshold, a critical notification will be sent.";
  }
  }

  grouping sys-mem-state {
    description
      "Operational state data for the system memory.";
	
    leaf dev-index {
      type uint32;
      config false;
      description
      "The index that uniquely represents a device. In stacking environments, there are multiple devices.
            This value will indicate the device index. When in general single device environments, this value is 0.";
    }	
    
    leaf memory-size {
      type uint64;
      units "bytes";
      config false;
      description
          "The total physical memory size on the system.";
    }
      
    leaf memory-free {
      type uint64;
      units "bytes";
      config false;
      description
          "The free memory size not be used on the system.";
    }

    leaf memory-usage {
      type uint32 {
        range "0..100";
      }
      config false;
      description
          "System memory usage rate.";
    }

    leaf status {
        type ntos-dm:severity-level;
        description
            "The node indicate whether the memory utilization is normal or not.";
    }

    leaf warning-threshold {
        type uint32;
        units "bytes";
        description
            "The warning threshold of the memory utilization. 
            When the curent memory utilization exceeds the threshold, a warning notification will be sent.";
    }

    leaf critical-threshold {
        type uint32;
        units "bytes";
        description
            "The critical threshold of the memory utilization. 
            When the curent memory utilization exceeds the threshold, a critical notification will be sent.";
    }
  }
  
  augment "/ntos:config/ntos-system:system" {
      description
         "The system memory monitoring configuration.";

      container memory {
         description
            "Enclosing container for the memory monitoring configuration.";
	     uses sys-mem-config;
	  }
   }

  augment "/ntos:state/ntos-system:system" {
     description
        "The system memory monitoring state.";

     container memory {
        description
           "Enclosing container for the memory monitoring state.";
	    config false;
		
		uses sys-mem-state;
	 }
  }
  
  notification memory-alarm {
      description
         "The memory status chanage notification.";

	  leaf dev-index {
		 type uint32;
		 description
			 "The index that uniquely represents a device. In stacking environments, there are multiple devices.
              This value will indicate the device index. When in general single device environments, this value is 0.";
      }
	  
	  leaf status {
         type ntos-dm:severity-level;
		 description
            "The node indicate whether the memory utilization is normal or not.";
	  }
  }
}
