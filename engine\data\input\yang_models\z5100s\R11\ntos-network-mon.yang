module ntos-network-mon {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:network-mon";
  prefix ntos-network-mon;

  import ntos {
    prefix ntos;
  }

  import ntos-types {
    prefix ntos-types;
  }

  /*
    import ntos-inet-types {
      prefix ntos-inet;
    }
  */

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS network monitoring module.";

  revision 2022-09-03 {
    description
      "Initial version.";
    reference "";
  }

  /*
    grouping delay-threshold {
      description
          "The grouping data for network delay test. You can get time delay
      of network paths by using network diagnostic tool(eg. ping).
      When the test result gotten, it need to check whether the time-delay(RTT)
      exceeds the threshold value configured in this group.
      If the time-delay exceeds the threshold value, a notification needs to be send.";

      leaf min-threshold {
        type uint32;
        units "milliseconds";
      config false;
      description
        "The minimum round-trip-time (RTT) threshold.";
    }

      leaf avg-threshold {
        type uint32;
        units "milliseconds";
      config false;
      description
        "The average round-trip-time (RTT) threshold.";
    }

    leaf max-threshold {
        type uint32;
        units "milliseconds";
      config false;
      description
        "The maximum round-trip-time (RTT) threshold.";
    }

    }
  */

  grouping flow-statistics {
    description
      "The grouping for flow statistics.";

     leaf current-number {
      type ntos-types:counter32;
      config false;
      description
        "The number of current concurrent connections alive.";
  }

     leaf new-crt-avg1sec {
      type string;
      config false;
      description
        "The new created connections per second.";
   }

     leaf new-crt-avg10secs {
    type string;
      config false;
      description
        "The new created connections per second.
         It is average value of total count of new created connections in 10 seconds.";
   }

   leaf new-crt-avg30secs {
      type string;
      config false;
      description
        "The new created connections per second.
         It is average value of total count of new created connections in 30 seconds.";
   }

     leaf new-crt-avg60secs {
      type string;
      config false;
      description
        "The new created connections per second.
         It is average value of total count of new created connections in 60 seconds.";
   }
  }

/*
  augment "/ntos:config" {
    description
      "Network monitoring configuration data.";

    container network-monitor {
      description
        "Network monitoring configuration.";
      uses delay-threshold;
    }
  }

  augment "/ntos:state" {
    description
      "Network monitoring operational state data.";

    container network-monitor {
      config false;
      description
        "Network monitoring state.";
      uses delay-threshold;
  }
  }
*/

  augment "/ntos:state/ntos:vrf" {
    description
      "Network interfaces operational state data.";

    container flow-monitor {
      config false;
      description
        "Network flow monitoring data.";
      uses flow-statistics;
    }
  }

/*
  notification network-delay-alarm {
     description
        "This notification is generated when one of the following conditions is met:
         a)the minimum round-trip-time (RTT) exceeds the min-threshold.
     b)the average round-trip-time (RTT) exceeds the avg-threshold.
     c)the maximum round-trip-time (RTT) exceeds the max-threshold.";

    leaf target-address {
        type ntos-inet:ip-address;
    description "The destination address for which the network time delay test.";
    }

  leaf time-type {
      type enumeration {
        enum min-delay {
         description "It means that the minimum network time-delay last test is abnormal.";
      }

      enum avg-delay {
         description "It means that the average network time-delay last test is abnormal.";
      }

      enum max-delay {
         description "It means that the maximum network time-delay last test is abnormal.";
      }
    }
  }

    leaf time-delay {
      type uint32;
      units "milliseconds";
    description
      "The round-trip-time (RTT) value which is abnormal.
       When time-type is min-delay, the value is minimum network time-delay value.
       When time-type is avg-delay, the value is average network time-delay value.
       When time-type is max-delay, the value is maximum network time-delay value.";
    }
  }
*/
}