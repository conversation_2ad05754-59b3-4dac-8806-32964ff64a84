module ntos-network-monitor {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:network-monitor";
  prefix ntos-network-monitor;

  import ntos {
    prefix ntos;
  }
  import ntos-commands {
    prefix ntos-cmd;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-if-types{
    prefix ntos-if;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos-system {
    prefix ntos-system;
  }
  import ntos-types {
    prefix ntos-types;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel: 4008-111-000
    E-mail: <EMAIL>";
  description
    "Ruijie NTOS network monitor module.";

  revision 2024-11-11 {
    description
      "Initial version.";
    reference "";
  }

  identity network-monitor {
    base ntos-types:SERVICE_LOG_ID;
    description
      "Network monitor service.";
  }

  grouping data-collection-config {
    description
      "Set data collection information.";

    container data-collection {
      description
        "Set data collection information.";

      leaf org-name {
        description
          "Set the organization name for data collection.";
        type ntos-types:ntos-obj-name-type {
          length "1..70";
        }
      }
      leaf org-code {
        description
          "Set the organization code for data collection.";
        type string {
          length "9";
        }
      }
      leaf org-address {
        description
          "Set the organization address for data collection.";
        type string {
          length "1..255";
        }
      }
      leaf source-wacode {
        description
          "Set source sign for data collection.";
        type string {
          length "6";
        }
      }
      leaf destination-wacode {
        description
          "Set destination sign for data collection.";
        type string {
          length "6";
        }
      }
    }
  }

  grouping send-param-config {
    description
      "Set send param of log server.";

    container send-param {
      description
        "Set send param information.";
      leaf ip-address {
        description
          "Set log server IP address.";
        type union {
          type ntos-inet:ipv4-address;
          type ntos-inet:ipv6-address;
        }
      }
      container source {
        description
          "Source interface of sending to log server.";
        ntos-ext:nc-cli-one-liner;

        choice source {
          description
            "Choice of source.";

          default "auto";
          case auto {
            leaf auto {
              description
                "Auto source.";
              type empty;
            }
          }
          case interface {
            leaf interface {
              description
                "Interface source.";
              type ntos-types:ifname;
              ntos-ext:nc-cli-no-name;
            }
          }
        }
      }

      leaf port {
        description
          "Set log server port.";
        type ntos-inet:port-number;
      }
      leaf username {
        description
          "Set log server username.";
        type string {
          length "1..63";
          pattern "[^`~!#$%^&*+/|\"'\\\\<>?]*" {
            error-message "cannot include character: `~!#$%^&*+|\"'\\/<>?";
          }
        }
      }
      leaf password {
        description
          "Set log server password.";
        type string;
      }
    }
  }

  grouping ap-config {
    description
      "Set ap information.";

    list ap {
      description
        "Set ap information.";
      key mac;
      ntos-ext:nc-cli-show-key-name;

      leaf mac {
        description
          "Set MAC address of ap.";
        type ntos-if:mac-address;
      }
      leaf server-name {
        description
          "Set log server name.";
        type ntos-types:ntos-obj-name-type;
        ntos-ext:nc-cli-completion-xpath
          "/ntos:config/ntos-system:system/ntos-network-monitor:network-monitor/log-server/*[local-name()='server-name']";
      }
      leaf service-code {
        description
          "Set netsite service code.";
        type ntos-types:ntos-obj-name-type;
        ntos-ext:nc-cli-completion-xpath
          "/ntos:config/ntos-system:system/ntos-network-monitor:network-monitor/netsite/*[local-name()='service-code']";
      }

      leaf ap-name {
        description
          "Set name of ap.";
        type string {
          length "1..127";
          pattern "[^`~!#$%^&*+/|\"',\\\\<>?]*" {
            error-message "cannot include character: `~!#$%^&*+|\"',\\/<>?";
          }
        }
      }
      leaf ap-type {
        description
          "Set type of ap.";
        type union {
          type enumeration {
            enum "1" {
              description
                "Fixed AP.";
            }
            enum "2" {
              description
                "Mobile AP.";
            }
            enum "10" {
              description
                "Fixed fence.";
            }
            enum "11" {
              description
                "Mobile fence.";
            }
            enum "12" {
              description
                "Individual equipment fence.";
            }
            enum "20" {
              description
                "H series.";
            }
            enum "22" {
              description
                "RT series.";
            }
          }
          type ntos-types:ntos-obj-name-type {
            length "1..63";
          }
        }
      }
      leaf ap-create-time {
        description
          "Set create time of ap.";
        type string {
          pattern "(([1-9][0-9]{3})-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1]))";
        }
      }
    }
  }

  grouping netsite-config {
    description
      "Set netsite information.";

    list netsite {
      description
        "Set netsite information.";
      key service-code;
      ntos-ext:nc-cli-show-key-name;

      leaf service-code {
        description
          "Set service code.";
        type string {
          length "14";
        }
      }

      leaf server-name {
        description
          "Set log server name.";
        type ntos-types:ntos-obj-name-type;
        ntos-ext:nc-cli-completion-xpath
          "/ntos:config/ntos-system:system/ntos-network-monitor:network-monitor/log-server/*[local-name()='server-name']";
      }

      leaf service-name {
        description
          "Set service name.";
        type string {
          length "1..255";
          pattern "[^`~!#$%^&*+/|\"',\\\\<>?]*" {
            error-message "cannot include character: `~!#$%^&*+|\"',\\/<>?";
          }
        }
      }
      leaf principal-name {
        description
          "Set principal name.";
        type string {
          length "1..50";
          pattern "[^`~!#$%^&*+/|\"',\\\\<>?]*" {
            error-message "cannot include character: `~!#$%^&*+|\"',\\/<>?";
          }
        }
      }
      leaf principal-cert-type {
        description
          "Set type of valid identification.";
        type union {
          type enumeration {
            enum "111" {
              description
                "ID Card.";
            }
            enum "112" {
              description
                "Temporary ID Card.";
            }
            enum "113" {
              description
                "Residence Booklet.";
            }
            enum "114" {
              description
                "PLA officer Certificate.";
            }
            enum "115" {
              description
                "Police Officer Certificate of the Chinese People's Armed Police Force.";
            }
            enum "116" {
              description
                "Temporary Residence Permit.";
            }
            enum "121" {
              description
                "Judge's Certificate.";
            }
            enum "123" {
              description
                "Police Officer Certificate.";
            }
            enum "125" {
              description
                "Prosecutor's Testimony.";
            }
            enum "127" {
              description
                "Lawyer's Certificate.";
            }
            enum "129" {
              description
                "Journalist ID.";
            }
            enum "131" {
              description
                "Employee's Card.";
            }
            enum "133" {
              description
                "Student ID Card.";
            }
            enum "155" {
              description
                "Accommodation Certificate.";
            }
            enum "159" {
              description
                "Social Security Card.";
            }
            enum "233" {
              description
                "Soldier Certificate.";
            }
            enum "335" {
              description
                "Driver's License.";
            }
            enum "411" {
              description
                "Diplomatic Passport.";
            }
            enum "412" {
              description
                "Official Passport.";
            }
            enum "413" {
              description
                "Official Ordinary Passport.";
            }
            enum "414" {
              description
                "Ordinary Passport.";
            }
            enum "415" {
              description
                "Travel Documents.";
            }
            enum "416" {
              description
                "Exit Entry Permit.";
            }
            enum "417" {
              description
                "Alien Exit Entry Permit.";
            }
            enum "418" {
              description
                "Foreigner Travel Permit.";
            }
            enum "420" {
              description
                "Hong Kong Special Administrative Region Passport.";
            }
            enum "421" {
              description
                "Macao Special Administrative Region Passport.";
            }
            enum "511" {
              description
                "Taiwan Compatriot Certificate.";
            }
            enum "513" {
              description
                "Pass For Traveling Between Inland Areas And Hong Kong And Macao.";
            }
            enum "515" {
              description
                "One Way Pass To Hong Kong And Macao.";
            }
            enum "516" {
              description
                "Notification Of Hong Kong And Macau Homecoming Permit.";
            }
            enum "517" {
              description
                "Mainland Residents' Travel Permit To Taiwan.";
            }
            enum "518" {
              description
                "Business Trips To Hong Kong And Macao Special Administrative Regions.";
            }
            enum "554" {
              description
                "Alien Residence Permit.";
            }
            enum "555" {
              description
                "Temporary Residence Permit For Foreigners.";
            }
            enum "711" {
              description
                "Border Management Zone Pass.";
            }
            enum "2" {
              description
                "Cell-phone Number.";
            }
            enum "990" {
              description
                "Other.";
            }
          }
          type ntos-types:ntos-obj-name-type {
            length "1..63";
          }
        }
      }
      leaf principal-cert-code {
        description
          "Set code of valid identification.";
        type string {
          length "1..50";
          pattern "[^`~!#$%^&*+/|\"',\\\\<>?]*" {
            error-message "cannot include character: `~!#$%^&*+|\"',\\/<>?";
          }
        }
      }
      leaf person-phone {
        description
          "Set phone number of manager.";
        type string {
          pattern "1[0-9]{10}";
        }
      }
      leaf service-type {
        description
          "Set type of service.";
        type union {
          type enumeration {
            enum "1" {
              description
                "Hotels and guesthouses (accommodation service facilities).";
            }
            enum "2" {
              description
                "Library Reading Room.";
            }
            enum "3" {
              description
                "Computer training center category.";
            }
            enum "4" {
              description
                "Entertainment venues.";
            }
            enum "5" {
              description
                "Transportation hub.";
            }
            enum "6" {
              description
                "Public transportation.";
            }
            enum "7" {
              description
                "Catering service venues.";
            }
            enum "8" {
              description
                "Financial service venues.";
            }
            enum "A" {
              description
                "Shopping places..";
            }
            enum "B" {
              description
                "Public service places.";
            }
            enum "C" {
              description
                "Cultural service venues.";
            }
            enum "D" {
              description
                "Public leisure places.";
            }
            enum "9" {
              description
                "Other.";
            }
          }
          type ntos-types:ntos-obj-name-type {
            length "1..63";
          }
        }
      }
      leaf business-nature {
        description
          "Set business nature.";
        type union {
          type enumeration {
            enum "1" {
              description
                "Operating.";
            }
            enum "2" {
              description
                "Non operating.";
            }
            enum "3" {
              description
                "Fence collection.";
            }
            enum "9" {
              description
                "Other.";
            }
          }
          type ntos-types:ntos-obj-name-type {
            length "1..63";
          }
        }
      }
      leaf business-status {
        description
          "Set business status.";
        type union {
          type enumeration {
            enum "1" {
              description
                "Installation and opening online.";
            }
            enum "2" {
              description
                "Installation and opening offline";
            }
            enum "6" {
              description
                "Order to suspend business.";
            }
            enum "12" {
              description
                "Suspension of business.";
            }
            enum "14" {
              description
                "Appointment installation.";
            }
            enum "10" {
              description
                "Other.";
            }
          }
          type ntos-types:ntos-obj-name-type {
            length "1..63";
          }
        }
      }
      leaf address {
        description
          "Set address.";
        type string {
          length "1..255";
          pattern "[^`~!$%^&*+/|\"',\\\\<>?]*" {
            error-message "cannot include character: `~!$%^&*+|\"',\\/<>?";
          }
        }
      }
      leaf create-time {
        description
          "Set create time.";
        type string {
          pattern "(([1-9][0-9]{3})-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1]))";
        }
      }
      leaf province-code {
        description
          "Set province code.";
        type string {
          length "6";
        }
      }
      leaf city-code {
        description
          "Set city code.";
        type string {
          length "6";
        }
      }
      leaf area-code {
        description
          "Set area code.";
        type string {
          length "6";
        }
      }
      leaf longitude {
        description
          "Set longitude.";
        type string {
          pattern "(-?)(((0[0-9]{2}).([0-9]{6}))|((1[0-7][0-9]).([0-9]{6}))|180.000000)";
        }
      }
      leaf latitude {
        description
          "Set latitude.";
        type string {
          pattern "(-?)(((0[0-8][0-9]).([0-9]{6}))|090.000000)";
        }
      }
      leaf start-time {
        description
          "Set business start time.";
        type string {
          pattern "(0[0-9]|1[0-9]|2[0-3]):([0-5][0-9])";
        }
      }
      leaf end-time {
        description
          "Set business closing time.";
        type string {
          pattern "(0[0-9]|1[0-9]|2[0-3]):([0-5][0-9])";
        }
      }
      leaf producer-code {
        description
          "Set producer code.";
        type union {
          type enumeration {
            enum "1" {
              description
                "China Telecom.";
            }
            enum "2" {
              description
                "China Netcom.";
            }
            enum "3" {
              description
                "China Unicom.";
            }
            enum "4" {
              description
                "China Great Wall Broadband.";
            }
            enum "5" {
              description
                "China Tietong.";
            }
            enum "6" {
              description
                "China Mobile.";
            }
            enum "8" {
              description
                "Education Department.";
            }
            enum "9" {
              description
                "Chinese Academy of Sciences.";
            }
            enum "11" {
              description
                "Radio and Television Department.";
            }
            enum "99" {
              description
                "Other.";
            }
          }
          type ntos-types:ntos-obj-name-type {
            length "1..63";
          }
        }
      }
      leaf producer-type {
        description
          "Set producer type.";
        type union {
          type enumeration {
            enum "1" {
              description
                "Private network, real IP address.";
            }
            enum "2" {
              description
                "Dedicated line.";
            }
            enum "3" {
              description
                "ADSL dial-up.";
            }
            enum "4" {
              description
                "ISDN.";
            }
            enum "5" {
              description
                "General dial-up.";
            }
            enum "6" {
              description
                "Cable modem dial-up.";
            }
            enum "7" {
              description
                "Power line.";
            }
            enum "8" {
              description
                "Wireless internet.";
            }
            enum "99" {
              description
                "Other.";
            }
          }
          type ntos-types:ntos-obj-name-type {
            length "1..63";
          }
        }
      }
    }
  }

  grouping log-server-config {
    description
      "Set log server information.";

    list log-server {
      description
        "Set log server information.";
      key server-name;
      ntos-ext:nc-cli-show-key-name;
      max-elements 3;

      leaf server-name {
        description
          "Set log server name.";
        type string {
          length "1..63";
          pattern "[^`~!#$%^&*+/|\"',\\\\<>?]*" {
            error-message "cannot include character: `~!#$%^&*+|\"',\\/<>?";
          }
        }
      }

      leaf platform {
        description
          "Set log server platform.";
        type union {
          type enumeration {
            enum surfilter-v2-new;
          }
          type ntos-types:ntos-obj-name-type {
            length "1..63";
          }
        }
      }

      uses send-param-config;
      uses data-collection-config;
    }
  }

  augment "/ntos:config/ntos-system:system" {
    description
      "Network monitor configuration.";

    container network-monitor {
      description
        "Network monitor service.";

      leaf enabled {
        description
          "Enable or disable network monitor service.";
        type boolean;
        default false;
      }

      leaf wireless-gather-type {
        description
          "Set wireless data gather type.";
        type enumeration {
          enum snmp {
            description
              "Set SNMP data gather type.";
          }
          enum syslog {
            description
              "Set SYSLOG data gather type.";
          }
        }
        default snmp;
      }

      uses log-server-config;
      uses netsite-config;
      uses ap-config;
    }
  }

  rpc set-debug {
    description
      "Set debug level.";

    input {
      leaf type {
        description
          "Debug type.";
        type enumeration {
          enum process;
          enum config;
          enum analysis;
          enum sender;
          enum database;
          enum ds;
          enum ds-log;
        }
        default process;
      }
      leaf level {
        description
          "Debug level.";
        type enumeration {
          enum emergency;
          enum alert;
          enum critical;
          enum error;
          enum warning;
          enum notice;
          enum info;
          enum debug;
        }
        default error;
      }
    }
    ntos-ext:nc-cli-cmd "network-monitor set-debug";
  }

  rpc show-debug {
    description
      "Display debug level.";

    output {
      leaf data {
        description
          "Debug level.";
        type string;
      }
    }
    ntos-ext:nc-cli-show "network-monitor debug";
  }

  rpc show-license-status {
    description
      "Display license status.";

    output {
      leaf data {
        description
          "License status.";
        type string;
      }
    }
    ntos-ext:nc-cli-show "network-monitor license-status";
  }

  rpc check-ap-exist {
    description
      "Get configuration of ap.";

    input {
      leaf mac {
        description
          "MAC address of ap.";
        type ntos-if:mac-address;
      }
    }
    output {
      leaf result {
        description
          "Result of check AP exist.";
        type string;
      }
    }
  }

  rpc get-ap-config {
    description
      "Get configuration of ap.";

    input {
      leaf log-server-name {
        description
          "Log server name.";
        type string {
          length "1..63";
        }
      }
      leaf netsite-name {
        description
          "Netsite name.";
        type ntos-types:ntos-obj-name-type {
          length "1..255";
        }
      }
      leaf start {
        type uint16 {
          range "0..65535";
        }
        description
          "The start offset.";
      }
      leaf end {
        type uint16 {
          range "0..65535";
        }
        description
          "The end offset.";
        must "current() >= ../start" {
          error-message "The end value must be larger than the start value.";
        }
      }
    }
    output {
      leaf data {
        description
          "Configuration of AP.";
        type string;
      }
    }
  }

  rpc get-log-server-platform {
    description
      "Get platform of log server.";

    output {
      leaf data {
        description
          "Platform of log-server.";
        type string;
      }
    }
  }

  rpc get-log-server-template {
    description
      "Get template of log server.";

    input {
      leaf log-server-platform {
        description
          "Log server platform.";
        type string {
          length "1..63";
        }
      }
    }
    output {
      leaf data {
        description
          "Template of log-server.";
        type string;
      }
    }
  }

  rpc get-netsite-template {
    description
      "Get template of netsite.";

    input {
      leaf log-server-name {
        description
          "Log server name.";
        type string {
          length "1..63";
        }
      }
    }
    output {
      leaf data {
        description
          "Template of netsite.";
        type string;
      }
    }
  }

  rpc get-ap-template {
    description
      "Get template of ap.";

    input {
      leaf log-server-name {
        description
          "Log server name.";
        type string {
          length "1..63";
        }
      }
    }
    output {
      leaf data {
        description
          "Template of ap.";
        type string;
      }
    }
  }

  rpc get-csv-template {
    description
      "Get csv template.";

    input {
      leaf log-server-name {
        description
          "Log server name.";
        type string;
      }
      leaf file-name {
        description
          "File name.";
        type string {
          length "1..255";
        }
      }
      leaf type {
        description
          "Type name.";
        type enumeration {
          enum netsite {
            description
              "Get netsite csv template.";
          }
          enum ap {
            description
              "Get AP csv template.";
          }
        }
      }
    }
    output {
      leaf result {
        description
          "Result of get csv template.";
        type string;
      }
    }
  }

  rpc import-netsite {
    description
      "Import netsite.";

    input {
      leaf log-server-name {
        description
          "Log server name.";
        type string;
      }
      leaf file-name {
        description
          "Import file name.";
        type string {
          length "1..255";
        }
      }
    }
    output {
      leaf result {
        description
          "Result of import netsite.";
        type string;
      }
    }
    ntos-ext:nc-cli-cmd "network-monitor import-netsite";
  }

  rpc export-netsite {
    description
      "Export netsite.";

    input {
      leaf log-server-name {
        description
          "Log server name.";
        type string;
      }
      leaf file-name {
        description
          "Export file name.";
        type string {
          length "1..255";
        }
      }
    }
    output {
      leaf result {
        description
          "Result of export netsite.";
        type string;
      }
    }
    ntos-ext:nc-cli-cmd "network-monitor export-netsite";
  }

  rpc import-ap {
    description
      "Import ap.";

    input {
      leaf log-server-name {
        description
          "Log server name.";
        type string;
      }
      leaf file-name {
        description
          "Import file name.";
        type string {
          length "1..255";
        }
      }
    }
    output {
      leaf result {
        description
          "Result of import ap.";
        type string;
      }
    }
    ntos-ext:nc-cli-cmd "network-monitor import-ap";
  }

  rpc export-ap {
    description
      "Export ap.";

    input {
      leaf log-server-name {
        description
          "Log server name.";
        type string;
      }
      leaf netsite-service-name {
        description
          "netsite service name.";
        type string;
      }
      leaf file-name {
        description
          "Export file name.";
        type string {
          length "1..255";
        }
      }
    }
    output {
      leaf result {
        description
          "Result of export ap.";
        type string;
      }
    }
    ntos-ext:nc-cli-cmd "network-monitor export-ap";
  }

  rpc upgrade-signature {
    description
      "Upgrade signature.";

    input {
      leaf file-path {
        description
          "Upgrade file path.";
        type string;
      }
    }
    output {
      leaf result {
        description
          "Result of upgrade signature.";
        type string;
      }
    }
    ntos-ext:nc-cli-cmd "network-monitor upgrade-signature";
  }

  rpc show-signature-upgrade-status {
    description
      "Display signature update status.";

    output {
      leaf data {
        description
          "Upgrade signature status.";
        type string;
      }
    }
    ntos-ext:nc-cli-show "network-monitor signature-status";
  }

  rpc show-signature-version {
    description
      "Display signature version.";

    output {
      leaf data {
        description
          "Signature version.";
        type string;
      }
    }
    ntos-ext:nc-cli-show "network-monitor signature-version";
  }

  rpc show-network-monitor-stats {
    description
      "Display network monitor stats.";

    input {
      leaf log-server-name {
        description
          "Log server name.";
        type string;
      }
    }
    output {
      leaf data {
        description
          "Stats of network monitor.";
        type string;
      }
      uses ntos-cmd:long-cmd-output;
    }
    ntos-ext:nc-cli-show "network-monitor stats";
  }

  rpc clear-network-monitor-stats {
    description
      "Clear network monitor stats.";

    input {
      leaf log-server-name {
        description
          "Log server name.";
        type string;
      }
    }
    ntos-ext:nc-cli-cmd "clear network-monitor stats";
  }

  rpc show-network-monitor-log-detail {
    description
      "Display network monitor log detail.";

    input {
      leaf log-server-name {
        description
          "Log server name.";
        type string;
      }
    }
    output {
      leaf data {
        description
          "Log detail information of network monitor.";
        type string;
      }
      uses ntos-cmd:long-cmd-output;
    }
    ntos-ext:nc-cli-show "network-monitor log-detail";
  }
}