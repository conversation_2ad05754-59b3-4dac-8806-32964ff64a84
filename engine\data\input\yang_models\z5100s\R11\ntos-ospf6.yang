module ntos-ospf6 {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:ospf6";
  prefix ntos-ospf6;

  import ntos {
    prefix ntos;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-commands {
    prefix ntos-cmd;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos-extensions {
    prefix ntos-extensions;
  }
  import ntos-routing {
    prefix ntos-rt;
  }
  import ntos-types {
    prefix ntos-types;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "RUIJIE NTOS OSPFv3.";

  revision 2020-06-26 {
    description
      "Add flush ospf6 interface RPC.";
    reference "";
  }
  revision 2019-06-06 {
    description
      "Add logging configuration.";
    reference "";
  }
  revision 2018-10-03 {
    description
      "Initial version.";
    reference "";
  }

  identity ospf6 {
    base ntos-types:SERVICE_LOG_ID;
    description
      "Routing OSPF6 protocol.";
    ntos-extensions:nc-cli-identity-name "routing ospf6";
  }

  identity route-ospf6 {
    base ntos-types:ROUTE6_FRR_ID;
    description
      "OSPF6 routes.";
    ntos-extensions:nc-cli-identity-name "ospf6";
  }

  grouping ospf6-config {
    description
      "OSPFv3 configuration.";

    leaf enabled {
      type boolean;
      default "true";
      description
        "Enable or disable OSPFv3.";
    }

    leaf router-id {
      type ntos-inet:ipv4-address;
      description
        "OSPFv3 router-id in IP address format.";
    }

    list area {
      key "area-id";
      description
        "OSPFv3 area parameters.";

      leaf area-id {
        type union {
          type uint32;
          type ntos-inet:ipv4-address;
        }
        description
          "OSPF area ID.";
      }

      container stub {
        presence "Makes stub available";
        description
          "Configure area as stub.";

        leaf summary {
          type boolean;
          default "true";
          description
            "Inject inter-area routes into stub.";
        }
      }

      leaf export-list {
        type string {
          length "1..max";
        }
        description
          "Set the filter for networks announced to other areas (access-list name).";
      }

      container filter-list {
        description
          "Filter networks between areas.";

        leaf input {
          type string {
            length "1..max";
          }
          description
            "Filter networks sent to this area (prefix-list name).";
        }

        leaf output {
          type string {
            length "1..max";
          }
          description
            "Filter networks sent from this area (prefix-list name).";
        }
      }

      leaf import-list {
        type string {
          length "1..max";
        }
        description
          "Set the filter for networks from other areas announced to the specified one
           (access-list name).";
      }

      list range {
        key "prefix";
        description
          "Summarize routes matching address/mask (border routers only).";
        ntos-extensions:nc-cli-one-liner;

        leaf prefix {
          type ntos-inet:ipv6-prefix;
          description
            "Area range prefix.";
        }

        leaf advertise {
          type boolean;
          default "true";
          description
            "Advertise this range.";
        }

        leaf cost {
          type uint32 {
            range "0..16777215";
          }
          must "../advertise = 'true'" {
            error-message "Cost cannot be set if action is not advertise.";
          }
          description
            "User specified metric for this range.";
        }
      }
    }

    leaf auto-cost {
      type uint32 {
        range "1..4294967";
      }
      default "100000";
      description
        "Calculate OSPF interface cost according to reference bandwidth (Mbits per second).";
    }

    container distance {
      description
        "OSPF administrative distance.";

      leaf all {
        type uint8 {
          range "1..255";
        }
        description
          "Default OSPF administrative distance.";
      }

      leaf external {
        type uint8 {
          range "1..255";
        }
        description
          "OSPF administrative distance for external routes.";
      }

      leaf inter-area {
        type uint8 {
          range "1..255";
        }
        description
          "OSPF administrative distance for inter-area routes.";
      }

      leaf intra-area {
        type uint8 {
          range "1..255";
        }
        description
          "OSPF administrative distance for intra-area routes.";
      }
    }

    list interface {
      key "name";
      description
        "Enable routing on an IPv6 interface.";
      ntos-extensions:nc-cli-one-liner;

      leaf name {
        type ntos-types:ifname;
        description
          "Interface name.";
        ntos-extensions:nc-cli-completion-xpath
          "../../ntos-interface:interface/*/*[local-name()='name']";
      }

      leaf area {
        type ntos-inet:ipv4-address;
        mandatory true;
        description
          "OSPF6 area ID.";
      }
    }

    leaf log-adjacency-changes {
      type enumeration {
        enum standard {
          description
            "Standard logs.";
        }
        enum detail {
          description
            "Log all state changes.";
        }
      }
      description
        "Log changes in adjacency state.";
    }

    list redistribute {
      key "protocol";
      description
        "Redistribute information from another routing protocol.";
      ntos-extensions:nc-cli-one-liner;

      leaf protocol {
        type enumeration {
          enum babel {
            description
              "Babel routing protocol (Babel).";
          }
          enum bgp {
            description
              "Border Gateway Protocol (BGP).";
          }
          enum connected {
            description
              "Connected routes (directly attached subnet or host).";
          }
          enum kernel {
            description
              "Kernel routes (not installed via the zebra RIB).";
          }
          enum ripng {
            description
              "Routing Information Protocol next-generation (IPv6) (RIPng).";
          }
          enum static {
            description
              "Statically configured routes.";
          }
          enum table {
            description
              "Non-main Kernel Routing Table.";
          }
        }
        description
          "Routing protocol.";
      }

      leaf route-map {
        type string {
          length "1..max";
        }
        description
          "Route map reference.";
      }
    }

    container timers {
      description
        "Adjust routing timers.";

      container lsa {
        description
          "Throttling link state advertisement delays.";

        leaf min-arrival {
          type uint32 {
            range "0..600000";
          }
          description
            "Minimum delay in receiving new version of a LSA.";
        }
      }

      container throttle {
        description
          "Throttling adaptive timer.";

        leaf lsa {
          type uint16 {
            range "0..5000";
          }
          description
            "LSA delay (msec) between transmissions.";
        }

        container spf {
          presence "Makes OSPF SPF timers available";
          description
            "OSPF SPF timers.";

          leaf delay {
            type uint32 {
              range "0..600000";
            }
            mandatory true;
            description
              "Delay (msec) from first change received till SPF calculation.";
          }

          leaf init-hold-time {
            type uint32 {
              range "0..600000";
            }
            mandatory true;
            description
              "Initial hold time (msec) between consecutive SPF calculations.";
          }

          leaf max-hold-time {
            type uint32 {
              range "0..600000";
            }
            mandatory true;
            description
              "Maximum hold time (msec).";
          }
        }
      }
    }
  }

  grouping ospf6-interface-config {
    description
      "Interface OSPFv3 configuration.";

    container advertise {
      presence "Makes advertise available.";
      description
        "Advertising options.";
      ntos-extensions:nc-cli-one-liner;

      leaf prefix-list {
        type string {
          length "1..max";
        }
        mandatory true;
        description
          "Filter prefix using prefix-list.";
      }
    }

    leaf cost {
      type uint16 {
        range "1..65535";
      }
      description
        "Outgoing metric of this interface.";
    }

    leaf dead-interval {
      type uint16 {
        range "1..65535";
      }
      description
        "Interval time (in seconds) after which a neighbor is declared down.";
    }

    leaf hello-interval {
      type uint16 {
        range "1..65535";
      }
      description
        "Time between HELLO packets (seconds).";
    }

    leaf ifmtu {
      type uint16 {
        range "1..65535";
      }
      description
        "OSPFv3 Interface MTU.";
    }

    leaf instance-id {
      type uint8 {
        range "0..255";
      }
      description
        "Instance ID for this interface.";
    }

    leaf mtu-ignore {
      type boolean;
      description
        "Disable MTU mismatch detection on this interface.";
    }

    leaf network {
      type enumeration {
        enum broadcast {
          description
            "Specify OSPF6 broadcast network.";
        }
        enum point-to-point {
          description
            "Specify OSPF6 point-to-point network.";
        }
      }
      default "broadcast";
      description
        "Network type.";
    }

    leaf passive {
      type boolean;
      description
        "Passive interface; no adjacency will be formed on this interface.";
    }

    leaf priority {
      type uint8 {
        range "0..255";
      }
      description
        "Router priority.";
    }

    leaf retransmit-interval {
      type uint16 {
        range "1..65535";
      }
      description
        "Time between retransmitting lost link state advertisements (in seconds).";
    }

    leaf transmit-delay {
      type uint16 {
        range "1..3600";
      }
      description
        "Link state transmit delay (in seconds).";
    }
  }

  grouping logging-common-config {
    description
      "OSPF6 logging configuration.";

    leaf enabled {
      type boolean;
      default "true";
      description
        "Enable/Disable OSPF6 logging configuration.";
    }

    leaf abr {
      type boolean;
      default "false";
      description
        "Log ABR information.";
    }

    leaf asbr {
      type boolean;
      default "false";
      description
        "Log ASBR information.";
    }

    container border-routers {
      description
        "Log border routers information.";

      leaf summary {
        type boolean;
        default "false";
        description
          "Log border router information in a specific area.";
      }

      leaf area-id {
        type ntos-inet:ipv4-address;
        description
          "Log border router information in a specific area.";
      }

      leaf router-id {
        type ntos-inet:ipv4-address;
        description
          "Log information from a specific border router.";
      }
    }

    leaf events {
      type boolean;
      default "false";
      description
        "Log events.";
    }

    leaf flooding {
      type boolean;
      default "false";
      description
        "Log flooding information.";
    }

    leaf interface {
      type boolean;
      default "false";
      description
        "Log interface information.";
    }

    list lsa {
      must "(lsa = 'all' and count(../lsa) = 1) or lsa != 'all'" {
        error-message "All LSA messages are already logged.";
      }
      must "(level = 'all' and count(level) = 1) or not(level = 'all')" {
        error-message "All LSA levels are already logged.";
      }
      key "lsa";
      description
        "Configure Link State Advertisements logging information.";
      ntos-extensions:nc-cli-one-liner;

      leaf lsa {
        type enumeration {
          enum as-external {
            description
              "Log as-external LSAs.";
          }
          enum inter-prefix {
            description
              "Log inter area prefix LSAs.";
          }
          enum inter-router {
            description
              "LOG inter router LSAs.";
          }
          enum intra-prefix {
            description
              "LOG intra area prefix LSAs.";
          }
          enum link {
            description
              "LOG link LSAs.";
          }
          enum network {
            description
              "LOG network LSAs.";
          }
          enum router {
            description
              "LOG router LSAs.";
          }
          enum all {
            description
              "LOG all LSA information.";
          }
        }
        description
          "LSA feature to log.";
      }

      leaf-list level {
        type enumeration {
          enum examine {
            description
              "Dump LSAs.";
          }
          enum flooding {
            description
              "Log LSA's internal information.";
          }
          enum originate {
            description
              "Log details of LSAs.";
          }
          enum all {
            description
              "Log all information about LSAs.";
          }
        }
        default "all";
        description
          "LSA log level.";
      }
    }

    list message {
      must "(message-type = 'all' and count(../message) = 1) or message-type != 'all'" {
        error-message "All messages are already logged.";
      }
      key "message-type";
      description
        "Log OSPF message information.";
      ntos-extensions:nc-cli-one-liner;

      leaf message-type {
        type enumeration {
          enum dd {
            description
              "Log Database Description messages.";
          }
          enum hello {
            description
              "Log Hello messages.";
          }
          enum ls-ack {
            description
              "Log Link State Acknowledgment messages.";
          }
          enum ls-request {
            description
              "Log Link State Request messages.";
          }
          enum ls-update {
            description
              "Log Link State Update messages.";
          }
          enum all {
            description
              "Log all messages.";
          }
        }
        description
          "Message type to log.";
      }

      leaf direction {
        type enumeration {
          enum send {
            description
              "Log sent messages.";
          }
          enum receive {
            description
              "Log received messages.";
          }
          enum both {
            description
              "Log all messages.";
          }
        }
        default "both";
        description
          "Direction of messages to log.";
      }
    }

    leaf neighbor {
      type enumeration {
        enum events {
          description
            "Log neighbor event information.";
        }
        enum state {
          description
            "Log neighbor state information.";
        }
        enum all {
          description
            "Log all neighbor information.";
        }
      }
      description
        "Log neighbor information.";
    }

    leaf-list route {
      type enumeration {
        enum inter-area {
          description
            "Log inter area route calculation.";
        }
        enum intra-area {
          description
            "Log intra area route calculation.";
        }
        enum memory {
          description
            "Log route memory use..";
        }
        enum table {
          description
            "Log route table calculation.";
        }
        enum all {
          description
            "Log all route information.";
        }
      }
      must "(../route = 'all' and count(../route) = 1) or not(../route = 'all')" {
        error-message "All route messages are already logged.";
      }
      description
        "Log route information.";
    }

    leaf-list spf {
      type enumeration {
        enum database {
          description
            "Log number of LSAs at SPF calculation time.";
        }
        enum process {
          description
            "Log detailed SPF process.";
        }
        enum time {
          description
            "Measure time taken by SPF calculation.";
        }
        enum all {
          description
            "Log all SPF messages.";
        }
      }
      must "(../spf = 'all' and count(../spf) = 1) or not(../spf = 'all')" {
        error-message "All SPF messages are already logged.";
      }
      description
        "Log SPF calculation.";
    }

    leaf zebra {
      type enumeration {
        enum send {
          description
            "Log messages sent to zebra.";
        }
        enum receive {
          description
            "Log messages received from zebra.";
        }
        enum both {
          description
            "Log messages to/from zebra.";
        }
      }
      description
        "Log messages between OSPF router and zebra.";
    }
  }

  rpc show-ospf6 {
    description
      "Show OSPFv3 information.";
    input {
      must 'count(route) + count(database) + count(neighbor) + count(interface) <= 1' {
        error-message "Route, database, neighbor and interface parameters are exclusive.";
      }

      leaf vrf {
        type string;
        default "main";
        description
          "Specify the VRF.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      container route {
        presence "OSPFv3 routing table.";
        description
          "OSPFv3 routing table.";
        ntos-extensions:nc-cli-group "show-ospf6-command";

        leaf destination {
          type union {
            type ntos-inet:ipv6-address;
            type ntos-inet:ipv6-prefix;
            type enumeration {
              enum detail {
                description
                  "Detailed information.";
              }
              enum external-1 {
                description
                  "Display Type-1 External routes.";
              }
              enum external-2 {
                description
                  "Display Type-2 External routes.";
              }
              enum inter-area {
                description
                  "Display Inter-Area routes.";
              }
              enum intra-area {
                description
                  "Display Intra-Area routes.";
              }
              enum summary {
                description
                  "Route table summary.";
              }
            }
          }
          description
            "The route destination.";
          ntos-extensions:nc-cli-no-name;
        }
      }

      container database {
        must 'count(router) + count(default) <= 1' {
          error-message "It is not possible to dump default and router info.";
        }
        presence "Database summary.";
        description
          "Database summary.";
        ntos-extensions:nc-cli-exclusive;
        ntos-extensions:nc-cli-group "show-ospf6-command";

        leaf default {
          type empty;
          description
            "Database summary.";
        }

        leaf router {
          type empty;
          description
            "Database Router link states.";
        }
      }

      leaf neighbor {
        type empty;
        description
          "Neighbor list.";
        ntos-extensions:nc-cli-group "show-ospf6-command";
      }

      container interface {
        presence "Interface information.";
        description
          "Interface information.";
        ntos-extensions:nc-cli-group "show-ospf6-command";

        leaf name {
          type ntos-types:ifname;
          description
            "The interface name. If not specified, show all interfaces.";
          ntos-extensions:nc-cli-no-name;
          ntos-extensions:nc-cli-completion-xpath
            "/ntos:state/ntos:vrf[ntos:name=string(current()/ntos-ospf6:vrf)]/ntos-interface:interface/*/*[local-name()='name'] |
             /ntos:state/ntos:vrf[ntos:name='main'][not(current()/ntos-ospf6:vrf)]/ntos-interface:interface/*/*[local-name()='name']";
                                                                                                                               }
      }
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-extensions:feature "product";
    ntos-extensions:nc-cli-show "ospf6";
    ntos-api:internal;
  }

  rpc flush-ospf6 {
    description
      "Flush OSPFv3 information.";
    input {

      leaf interface {
        type string {
          ntos-extensions:nc-cli-shortdesc "<ifname>";
        }
        mandatory true;
        description
          "The name of the network interface to be cleared.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf[ntos:name=string(current()/ntos-system:vrf)]/ntos-interface:interface/*/*[local-name()='name'] |
           /ntos:state/ntos:vrf[ntos:name='main'][not(current()/ntos-system:vrf)]/ntos-interface:interface/*/*[local-name()='name']";
      }
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-extensions:feature "product";
    ntos-extensions:nc-cli-flush "ospf6";
    ntos-api:internal;
  }

  augment "/ntos:config/ntos:vrf/ntos-rt:routing" {
    description
      "OSPFv3 configuration.";

    container ospf6 {
      must 'count(/ntos:config/ntos:vrf/ntos-rt:routing/ospf6[enabled="true"]) <= 1' {
        error-message "Only one OSPF6 instance can be enabled.";
      }
      presence "Makes OSPFv3 available";
      description
        "OSPFv3 configuration.";
      ntos-extensions:feature "product";
      uses ospf6-config;
    }
  }

  augment "/ntos:state/ntos:vrf/ntos-rt:routing" {
    description
      "OSPFv3 state.";

    container ospf6 {
      presence "Makes OSPFv3 available";
      description
        "OSPFv3 operational state data.";
      ntos-extensions:feature "product";
      uses ospf6-config;
    }
  }

  augment "/ntos:config/ntos:vrf/ntos-rt:routing/ntos-rt:interface/ntos-rt:ipv6" {
    description
      "OSPF interface configuration.";

    container ospf6 {
      presence "Makes interface OSPFv3 available";
      description
        "Interface OSPFv3 configuration.";
      ntos-extensions:feature "product";
      uses ospf6-interface-config;
    }
  }

  augment "/ntos:state/ntos:vrf/ntos-rt:routing/ntos-rt:interface/ntos-rt:ipv6" {
    description
      "OSPF interface state.";

    container ospf6 {
      presence "Makes interface OSPFv3 available";
      description
        "Interface OSPFv3 operational state data.";
      ntos-extensions:feature "product";
      uses ospf6-interface-config;
    }
  }

  augment "/ntos:config/ntos-rt:routing/ntos-rt:logging" {
    description
      "Common OSPF6 routers logging configuration.";

    container ospf6 {
      presence "Make OSPF6 common logging configuration available.";
      description
        "Common OSPF6 routers logging configuration.";
      ntos-extensions:feature "product";
      uses logging-common-config;
    }
  }

  augment "/ntos:state/ntos-rt:routing/ntos-rt:logging" {
    description
      "Common OSPF6 routers logging operational state.";

    container ospf6 {
      presence "Make OSPF6 common logging state available.";
      description
        "Operational logging state common to all OSPF6 routers.";
      ntos-extensions:feature "product";
      uses logging-common-config;
    }
  }
}
