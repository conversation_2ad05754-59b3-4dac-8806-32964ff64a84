module ntos-reputation-center {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:reputation-center";
  prefix ntos-reputation-center;

  import ntos {
    prefix ntos;
  }

  import ntos-extensions {
    prefix ntos-ext;
  }

  import ntos-types {
    prefix ntos-types;
  }

  import ntos-commands {
    prefix ntos-cmd;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS reputation-center module.";

  revision 2024-05-13 {
    description
      "Add rpc for show statsistics.";
    reference "";
  }

  revision 2023-03-30 {
    description
      "Add rpc for getting license status.";
    reference "";
  }

  revision 2023-02-01 {
    description
      "Initial version.";
    reference "";
  }

  identity reputation-center {
    base ntos-types:SERVICE_LOG_ID;
    description
      "Reputation-center service.";
  }

  grouping reputation-center-group {
    description
      "Configuration of reputation-center.";

    leaf enabled {
      type boolean;
      default "false";
      description
        "Enable or disable the reputation-center.";
    }
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "Configuration of reputation-center.";

    container reputation-center {
      description
        "Reputation-center configuration.";
      uses reputation-center-group;
    }
  }

  augment "/ntos:state/ntos:vrf" {
    description
      "State of reputation-center.";

    container reputation-center {
      description
        "Reputation-center configuration.";
      uses reputation-center-group;
    }
  }

  rpc reputation-center-show {
    description
      "Show reputation-center configuration.";

    input {
      leaf vrf {
        type ntos:vrf-name;
        default "main";
        description
          "Vrf name.";
      }
    }

    output {
      leaf enabled {
        type boolean;
        description
          "Reputation-center status.";
      }

      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }

    ntos-ext:nc-cli-show "reputation-center";
  }

  rpc reputation-center-get-license-status {
    description
      "Get reputation-center license status.";

    input {
      leaf vrf {
        type ntos:vrf-name;
        default "main";
        description
          "Vrf name.";
      }
    }

    output {
      leaf activated {
        type boolean;
        description
          "Reputation-center license status.";
      }

      uses ntos-cmd:long-cmd-output;
    }

    ntos-ext:nc-cli-show "reputation-center license-status";
  }

  rpc reputation-center-show-app-stats {
    description
      "Show statistics of applications.";

    output {
      leaf buffer {
        type string;
        description
          "The statistics of applications.";
      }
    }
  }

  rpc reputation-center-show-flow-stats {
    description
      "Show statistics of reputation flows.";

    output {
      leaf buffer {
        type string;
        description
          "The statistics of reputation flows.";
      }
    }
  }
}
