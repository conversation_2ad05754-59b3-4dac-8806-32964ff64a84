module ntos-routing {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:routing";
  prefix ntos-routing;

  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos {
    prefix ntos;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-commands {
    prefix ntos-cmd;
  }
  import ntos-extensions {
    prefix ntos-extensions;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-ip-track {
    prefix ntos-ip-track;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS routing module.";

  revision 2022-01-06 {
    description
      "Add new features for NTOS.";
    reference "";
  }
  revision 2020-12-11 {
    description
      "Obsolete prepend as-number is now removed. Deprecated table leaf in
       next-hop is obsolete. Deprecated deny/permit in ipv4 and ipv6 access list
       are now obsolete.";
    reference "revision 2020-12-11";
  }
  revision 2020-09-23 {
    description
      "Add new 'rib route-count nhrp'.";
    reference "";
  }
  revision 2020-02-19 {
    description
      "Add new 'table' list to allow configuring the same static route in several tables.
       Deprecate static routes 'table' parameter.";
    reference "";
  }
  revision 2019-12-06 {
    description
      "Deprecated prepend as-number is now obsolete.";
    reference "revision 2019-01-29";
  }
  revision 2019-06-06 {
    description
      "Add logging configuration.";
    reference "";
  }
  revision 2019-01-29 {
    description
      "Allow to prepend the same AS number several times in a route-map.";
    reference "";
  }
  revision 2018-10-03 {
    description
      "Initial version.";
    reference "";
  }

  identity routing {
    base ntos-types:SERVICE_LOG_ID;
    description
      "Routing service.";
  }

  identity route-connected {
    base ntos-types:ROUTE4_FRR_ID;
    base ntos-types:ROUTE6_FRR_ID;
    description
      "Connected routes.";
    ntos-extensions:nc-cli-identity-name "connected";
  }

  identity route-kernel {
    base ntos-types:ROUTE4_FRR_ID;
    base ntos-types:ROUTE6_FRR_ID;
    description
      "Kernel routes.";
    ntos-extensions:nc-cli-identity-name "kernel";
  }

  identity route-static {
    base ntos-types:ROUTE4_FRR_ID;
    base ntos-types:ROUTE6_FRR_ID;
    description
      "Static routes.";
    ntos-extensions:nc-cli-identity-name "static";
  }

  typedef ipv4-address-and-ifname {
    type string {
      pattern '(([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\.){3}([0-9]|' +
              '[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])%[-A-Za-z0-9/:._@]+';
      ntos-extensions:nc-cli-shortdesc "<A.B.C.D>%<ifname>";
    }
    description
      "An IPv4 address followed by an interface name.";
  }

  typedef route-map-name {
    type leafref {
      path
        "/ntos:config/ntos-routing:routing/route-map/name";
    }
    description
      "Route map name.";
    ntos-extensions:nc-cli-shortdesc "<route-map>";
  }

  typedef ipv6-address-and-ifname {
    type string {
      pattern '(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:' +
              '|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}' +
              ':){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}' +
              '(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}' +
              '(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}' +
              '(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}' +
              '){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:))%[-A-Za-z0-9/:._@]+';
      ntos-extensions:nc-cli-shortdesc "<X:X::X:X>%<ifname>";
    }
    description
      "An IPv6 address followed by an interface name.";
  }

  grouping static-route-ipv4-config {
    description
      "Configuration for IPv4 static route.";

    leaf destination {
      type ntos-inet:ipv4-prefix {
        ntos-extensions:nc-cli-shortdesc "<A.B.C.D/M>";
      }
      description
        "Route destination.";
    }
  }

  grouping static-route-ipv6-config {
    description
      "Configuration for IPv6 static route.";

    leaf destination {
      type ntos-inet:ipv6-prefix {
        ntos-extensions:nc-cli-shortdesc "<X:X::X:X/M>";
      }
      description
        "Route destination.";
    }
  }

  grouping routing-table-config {
    description
      "Routing table configuration.";

    leaf table-id {
      type uint32 {
        range "1..**********";
      }
      description
        "Table number.";
    }
  }

  grouping static-route-next-hop-common-config-with-table {
    description
      "Configuration for common route next hop.";

    leaf distance {
      type uint8 {
        range "1..255";
      }
      default "5";
      description
        "Distance value for this route.";
    }

    leaf label {
      type string {
        length "1..max";
      }
      description
        "Specify label(s) for this route. One or more labels in the range (16-1048575)
         separated by '/'.";
    }

    leaf nexthop-vrf {
      type ntos:vrf-name;
      description
        "Nexthop VRF.";
    }

    leaf table {
      type uint32 {
        range "1..**********";
      }
      status obsolete {
        ntos-extensions:status-obsoleted-release "20q3";
        ntos-extensions:status-deprecated-revision "2020-02-19";
        ntos-extensions:status-description "The table option has been replaced by a table context to support
          the same nexthops in different tables.";
        ntos-extensions:status-replacement
          "/ntos:config/ntos:vrf/ntos-routing:routing/static/table/ipv*/next-hop";
      }
      description
        "Table to configure.";
    }

    leaf tag {
      type uint32 {
        range "1..**********";
      }
      description
        "Route tag.";
    }

    leaf track {
      type ntos-types:ntos-obj-name-type;
      description
        "A tracker name. If the tracked address is reachable, the next-hop is
         considered as valid, else it is disabled.";
      ntos-extensions:nc-cli-completion-xpath
        "/ntos:config/ntos:vrf/ntos-ip-track:track/ntos-ip-track:rule/ntos-ip-track:name";
    }
  }

  grouping static-route-next-hop-common-config {
    description
      "Configuration for common route next hop.";

    leaf distance {
      type uint8 {
        range "1..255";
      }
      description
        "Distance value for this route.";
    }

    leaf label {
      type string {
        length "1..max";
      }
      description
        "Specify label(s) for this route. One or more labels in the range (16-1048575)
         separated by '/'.";
    }

    leaf nexthop-vrf {
      type ntos:vrf-name;
      description
        "Nexthop VRF.";
    }

    leaf tag {
      type uint32 {
        range "1..**********";
      }
      description
        "Route tag.";
    }

    leaf track {
      type ntos-types:ntos-obj-name-type;
      description
        "A tracker name. If the tracked address is reachable, the next-hop is
         considered as valid, else it is disabled.";
      ntos-extensions:nc-cli-completion-xpath
        "/ntos:config/ntos:vrf/ntos-ip-track:track/ntos-ip-track:rule/ntos-ip-track:name";
    }
  }

  grouping static-route-next-hop-ipv4-config-with-table {
    description
      "Configuration for IPv4 route next hop.";

    leaf next-hop {
      type union {
        type ntos-inet:ipv4-address {
          ntos-extensions:nc-cli-shortdesc "<A.B.C.D>";
        }
        type ntos-types:ifname;
        type ipv4-address-and-ifname;
        type enumeration {
          enum blackhole {
            description
              "Silently discard packets when matched.";
          }
          enum dhcp-gateway {
            description
              "Use the gateway acquired by DHCP on the port specified in dhcp-port leaf.";
          }
        }
      }
      must "current() != 'dhcp-gateway' or current() = 'dhcp-gateway' and count(../dhcp-port) = 1" {
        error-message "dhcp-gateway needs dhcp-port.";
      }
      description
        "Route next-hop.";
      ntos-extensions:nc-cli-completion-xpath
        "../../../ntos-interface:interface/*[local-name()='physical']/*[local-name()='name'] |
         ../../../ntos-interface:interface/*[local-name()='vlan']/*[local-name()='name'] |
         ../../../ntos-interface:interface/*[local-name()='bridge']/*[local-name()='name'] |
         ../../../ntos-interface:interface/*[local-name()='vti']/*[local-name()='name'] |
         ../../../ntos-interface:interface/*[local-name()='gre']/*[local-name()='name'] |
         /ntos:state/ntos:vrf/ntos-interface:interface/*[local-name()='physical']/*[local-name()='ipv4']/
         *[local-name()='pppoe']/*[local-name()='connection']/*[local-name()='tunnel-interface'] |
         /ntos:state/ntos:vrf/ntos-interface:interface/*[local-name()='vlan']/*[local-name()='ipv4']/
         *[local-name()='pppoe']/*[local-name()='connection']/*[local-name()='tunnel-interface']";
    }

    leaf dhcp-port {
      type ntos-types:ifname;
      must "../next-hop = 'dhcp-gateway'" {
        error-message "dhcp-port is only for dhcp-gateway next-hops.";
      }
      description
        "The dhcp port, for dhcp-gateway type next-hops.";
      ntos-extensions:nc-cli-completion-xpath
        "../../../../ntos-interface:interface/*/*[local-name()='name']";
    }
    uses static-route-next-hop-common-config-with-table;
  }

  grouping static-route-next-hop-ipv4-config {
    description
      "Configuration for IPv4 route next hop.";

    leaf next-hop {
      type union {
        type ntos-inet:ipv4-address {
          ntos-extensions:nc-cli-shortdesc "<A.B.C.D>";
        }
        type ntos-types:ifname;
        type ipv4-address-and-ifname;
        type enumeration {
          enum blackhole {
            description
              "Silently discard packets when matched.";
          }
          enum reject {
            description
              "Emit an ICMP unreachable when matched.";
          }
          enum dhcp-gateway {
            description
              "Use the gateway acquired by DHCP on the port specified in dhcp-port leaf.";
          }
        }
      }
      must "current() != 'dhcp-gateway' or current() = 'dhcp-gateway' and count(../dhcp-port) = 1" {
        error-message "dhcp-gateway needs dhcp-port.";
      }
      description
        "Route next-hop.";
      ntos-extensions:nc-cli-completion-xpath
        "../../../ntos-interface:interface/*/*[local-name()='name']";
    }

    leaf dhcp-port {
      type ntos-types:ifname;
      must "../next-hop = 'dhcp-gateway'" {
        error-message "dhcp-port is only for dhcp-gateway next-hops.";
      }
      description
        "The dhcp port, for dhcp-gateway type next-hops.";
      ntos-extensions:nc-cli-completion-xpath
        "../../../../ntos-interface:interface/*/*[local-name()='name']";
    }
    uses static-route-next-hop-common-config;
  }

  grouping static-route-next-hop-ipv6-config-with-table {
    description
      "Configuration for IPv6 route next hop.";

    leaf next-hop {
      type union {
        type ntos-inet:ipv6-address {
          ntos-extensions:nc-cli-shortdesc "<X:X::X:X>";
        }
        type ntos-types:ifname;
        type ipv6-address-and-ifname;
        type enumeration {
          enum blackhole {
            description
              "Silently discard packets when matched.";
          }
          enum reject {
            description
              "Emit an ICMP unreachable when matched.";
          }
        }
      }
      description
        "Route next-hop.";
      ntos-extensions:nc-cli-completion-xpath
        "../../../ntos-interface:interface/*/*[local-name()='name']";
    }
    uses static-route-next-hop-common-config-with-table;
  }

  grouping static-route-next-hop-ipv6-config {
    description
      "Configuration for IPv6 route next hop.";

    leaf next-hop {
      type union {
        type ntos-inet:ipv6-address {
          ntos-extensions:nc-cli-shortdesc "<X:X::X:X>";
        }
        type ntos-types:ifname;
        type ipv6-address-and-ifname;
        type enumeration {
          enum blackhole {
            description
              "Silently discard packets when matched.";
          }
          enum reject {
            description
              "Emit an ICMP unreachable when matched.";
          }
        }
      }
      description
        "Route next-hop.";
      ntos-extensions:nc-cli-completion-xpath
        "../../../ntos-interface:interface/*/*[local-name()='name']";
    }
    uses static-route-next-hop-common-config;
  }

  grouping ipv4-access-list-rule {
    description
      "Configuration for IPv4 access list rule.";

    leaf address {
      type union {
        type ntos-inet:ipv4-prefix {
          ntos-extensions:nc-cli-shortdesc "<A.B.C.D/M>";
        }
        type enumeration {
          enum any {
            description
              "Any prefix.";
          }
        }
      }
      description
        "Prefix to match.";
    }

    leaf exact-match {
      type boolean;
      must "../address[text() != 'any']" {
        error-message "Cannot set exact match if address is set to any.";
      }
      description
        "Enable or disable exact match of the prefixes.";
    }
  }

  grouping ipv4-access-list-rule-obsolete {
    status obsolete;
    description
      "Configuration for IPv4 access list rule.";

    leaf address {
      type union {
        type ntos-inet:ipv4-prefix {
          ntos-extensions:nc-cli-shortdesc "<A.B.C.D/M>";
        }
        type enumeration {
          enum any {
            description
              "Any prefix.";
          }
        }
      }
      status obsolete;
      description
        "Prefix to match.";
    }

    leaf exact-match {
      type boolean;
      must "../address[text() != 'any']" {
        error-message "Cannot set exact match if address is set to any.";
      }
      status obsolete;
      description
        "Enable or disable exact match of the prefixes.";
    }
  }

  grouping ipv4-access-list-config {
    description
      "Configuration for IPv4 access list.";

    list ipv4-access-list {
      key "name";
      description
        "IPv4 access list.";

      leaf name {
        type string {
          length "1..max";
        }
        description
          "Access list name.";
      }

      leaf remark {
        type string {
          length "1..100";
        }
        description
          "Access list entry comment.";
      }

      list seq {
        key "num";
        description
          "Specify access list to reject or accept.";
        ntos-extensions:nc-cli-one-liner;

        leaf num {
          type uint16;
          description
            "List sequence.";
        }

        list permit {
          key "address";
          description
            "IPv4 access list deny rules.";
          ntos-extensions:nc-cli-one-liner;
          uses ipv4-access-list-rule;
        }

        list deny {
          key "address";
          description
            "IPv4 access list deny rules.";
          ntos-extensions:nc-cli-one-liner;
          uses ipv4-access-list-rule;
        }
      }

      list deny {
        key "address";
        status obsolete {
          ntos-extensions:status-obsoleted-release "20q3";
          ntos-extensions:status-deprecated-revision "2019-10-16";
          ntos-extensions:status-description "The configuration result of mixed deny/permit nodes was not deterministic, and is replaced by an ordered list";
          ntos-extensions:status-replacement "/ntos:config/ntos-routing:routing/ipv4-access-list-config/ipv4-access-list/seq/deny";
        }
        description
          "IPv4 access list deny rules (obsolete).";
        ntos-extensions:nc-cli-one-liner;
        uses ipv4-access-list-rule-obsolete {
          status obsolete;
        }
      }

      list permit {
        key "address";
        status obsolete {
          ntos-extensions:status-obsoleted-release "20q3";
          ntos-extensions:status-deprecated-revision "2019-10-16";
          ntos-extensions:status-description "The configuration result of mixed deny/permit nodes was not deterministic, and is replaced by an ordered list";
          ntos-extensions:status-replacement "/ntos:config/ntos-routing:routing/ipv4-access-list-config/ipv4-access-list/seq/permit";
        }
        description
          "IPv4 access list permit rules (obsolete).";
        ntos-extensions:nc-cli-one-liner;
        uses ipv4-access-list-rule-obsolete {
          status obsolete;
        }
      }
    }
  }

  grouping ipv6-access-list-rule {
    description
      "Configuration for IPv6 access list rule.";

    leaf address {
      type union {
        type ntos-inet:ipv6-prefix {
          ntos-extensions:nc-cli-shortdesc "<X:X::X:X/M>";
        }
        type enumeration {
          enum any {
            description
              "Any prefix.";
          }
        }
      }
      description
        "Prefix to match.";
    }

    leaf exact-match {
      type boolean;
      must "../address[text() != 'any']" {
        error-message "Cannot set exact match if address is set to any.";
      }
      description
        "Enable or disable exact match of the prefixes.";
    }
  }

  grouping ipv6-access-list-rule-obsolete {
    status obsolete;
    description
      "Configuration for IPv6 access list rule.";

    leaf address {
      type union {
        type ntos-inet:ipv6-prefix {
          ntos-extensions:nc-cli-shortdesc "<X:X::X:X/M>";
        }
        type enumeration {
          enum any {
            description
              "Any prefix.";
          }
        }
      }
      status obsolete;
      description
        "Prefix to match.";
    }

    leaf exact-match {
      type boolean;
      must "../address[text() != 'any']" {
        error-message "Cannot set exact match if address is set to any.";
      }
      status obsolete;
      description
        "Enable or disable exact match of the prefixes.";
    }
  }

  grouping ipv6-access-list-config {
    description
      "Configuration for IPv6 access list.";

    list ipv6-access-list {
      key "name";
      description
        "IPv6 access list.";

      leaf name {
        type string {
          length "1..max";
        }
        description
          "Access list name.";
      }

      leaf remark {
        type string {
          length "1..100";
        }
        description
          "Access list entry comment.";
      }

      list seq {
        key "num";
        description
          "Specify access list to reject or accept.";
        ntos-extensions:nc-cli-one-liner;

        leaf num {
          type uint16;
          description
            "Access list sequence.";
        }

        list permit {
          key "address";
          description
            "IPv6 access list deny rules.";
          ntos-extensions:nc-cli-one-liner;
          uses ipv6-access-list-rule;
        }

        list deny {
          key "address";
          description
            "IPv6 access list deny rules.";
          ntos-extensions:nc-cli-one-liner;
          uses ipv6-access-list-rule;
        }
      }

      list deny {
        key "address";
        status obsolete {
          ntos-extensions:status-obsoleted-release "20q3";
          ntos-extensions:status-deprecated-revision "2019-10-16";
          ntos-extensions:status-description "The configuration result of mixed deny/permit nodes was not deterministic, and is replaced by an ordered list";
          ntos-extensions:status-replacement "/ntos:config/ntos-routing:routing/ipv6-access-list-config/ipv6-access-list/seq/deny";
        }
        description
          "IPv6 access list deny rules (obsolete).";
        ntos-extensions:nc-cli-one-liner;
        uses ipv6-access-list-rule-obsolete {
          status obsolete;
        }
      }

      list permit {
        key "address";
        status obsolete {
          ntos-extensions:status-obsoleted-release "20q3";
          ntos-extensions:status-deprecated-revision "2019-10-16";
          ntos-extensions:status-description "The configuration result of mixed deny/permit nodes was not deterministic, and is replaced by an ordered list";
          ntos-extensions:status-replacement "/ntos:config/ntos-routing:routing/ipv6-access-list-config/ipv6-access-list/seq/permit";
        }
        description
          "IPv6 access list permit rules (obsolete).";
        ntos-extensions:nc-cli-one-liner;
        uses ipv6-access-list-rule-obsolete {
          status obsolete;
        }
      }
    }
  }

  grouping prefix-list-seq-common-config {
    description
      "Configuration for common prefix list sequence number.";

    leaf num {
      type uint32 {
        range "1..**********";
      }
      description
        "Sequence number.";
    }

    leaf policy {
      type enumeration {
        enum deny {
          description
            "Specify packets to reject.";
        }
        enum permit {
          description
            "Specify packets to forward.";
        }
      }
      mandatory true;
      description
        "Prefix list policy.";
    }

    leaf ge {
      type uint8 {
        range "0..128";
      }
      must 'count(../address) = 1' {
        error-message "Minimum prefix length is not applicable when address is any";
      }
      description
        "Minimum prefix length to be matched.";
    }

    leaf le {
      type uint8 {
        range "0..128";
      }
      must 'count(../address) = 1' {
        error-message "Maximum prefix length is not applicable when address is any";
      }
      must 'count(../ge) = 0 or . >= ../ge' {
        error-message "Maximum prefix must be >= minimum prefix";
      }
      description
        "Maximum prefix length to be matched.";
    }
  }

  grouping prefix-list-seq-ipv4-config {
    description
      "Configuration for IPv4 prefix list sequence number.";

    leaf address {
      type ntos-inet:ipv4-prefix {
        ntos-extensions:nc-cli-shortdesc "<A.B.C.D/M>";
      }
      description
        "Prefix to match (any if not set).";
    }
    uses prefix-list-seq-common-config;
  }

  grouping prefix-list-seq-ipv6-config {
    description
      "Configuration for IPv6 prefix list sequence number.";

    leaf address {
      type ntos-inet:ipv6-prefix {
        ntos-extensions:nc-cli-shortdesc "<X:X::X:X/M>";
      }
      description
        "Prefix to match (any if not set).";
    }
    uses prefix-list-seq-common-config;
  }

  grouping route-map-seq-config {
    description
      "Configuration for route map sequence number.";

    leaf num {
      type uint16 {
        range "1..65535";
      }
      description
        "Sequence number.";
    }

    leaf policy {
      type enumeration {
        enum deny {
          description
            "Route map denies set operations.";
        }
        enum permit {
          description
            "Route map permits set operations.";
        }
      }
      mandatory true;
      description
        "Matching policy.";
    }

    leaf description {
      type string;
      description
        "Route-map description.";
    }

    container match {
      description
        "Match values from routing table.";

      leaf as-path {
        type string {
          length "1..max";
        }
        description
          "Match BGP AS path list.";
      }

      container evpn {
        description
          "Ethernet Virtual Private Network.";

        leaf default-route {
          type boolean;
          description
            "If true, mark as default EVPN type-5 route.";
        }

        leaf route-type {
          type enumeration {
            enum macip {
              description
                "Mac-ip route.";
            }
            enum multicast {
              description
                "IMET route.";
            }
            enum prefix {
              description
                "Prefix route.";
            }
          }
          description
            "Match route type.";
        }

        leaf vni {
          type uint32 {
            range "1..********";
          }
          description
            "VNI ID.";
        }
      }

      leaf interface {
        type ntos-types:ifname;
        description
          "Match first hop interface of route.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:config/vrf/ntos-interface:interface/*/*[local-name()='name']";
      }

      container ip {
        description
          "IP information.";

        container address {
          description
            "Match address of route.";

          leaf access-list {
            type union {
              type uint16 {
                range "1..199|1300..2699";
              }
              type string {
                length "1..max";
              }
            }
            description
              "Matches the specified access list.";
          }

          leaf prefix-list {
            type string {
              length "1..max";
            }
            description
              "Matches the specified prefix list.";
          }

          leaf prefix-len {
            type uint8 {
              range "0..32";
            }
            description
              "Matches the specified prefix length.";
          }
        }

        container next-hop {
          description
            "Match next-hop address of route.";

          leaf access-list {
            type union {
              type uint16 {
                range "1..199|1300..2699";
              }
              type string {
                length "1..max";
              }
            }
            description
              "Matches the specified access list.";
          }

          leaf prefix-list {
            type string {
              length "1..max";
            }
            description
              "Matches the specified prefix list.";
          }

          leaf prefix-len {
            type uint8 {
              range "0..32";
            }
            description
              "Matches the specified prefix length.";
          }
        }

        container route-source {
          description
            "Match advertising source address of route.";

          leaf access-list {
            type union {
              type uint16 {
                range "1..199|1300..2699";
              }
              type string {
                length "1..max";
              }
            }
            description
              "Matches the specified access list.";
          }

          leaf prefix-list {
            type string {
              length "1..max";
            }
            description
              "Matches the specified prefix list.";
          }
        }
      }

      container ipv6 {
        description
          "IPv6 information.";

        container address {
          description
            "Match IPv6 address of route.";

          leaf access-list {
            type string {
              length "1..max";
            }
            description
              "Matches the specified access list.";
          }

          leaf prefix-list {
            type string {
              length "1..max";
            }
            description
              "Matches the specified prefix list.";
          }

          leaf prefix-len {
            type uint8 {
              range "0..128";
            }
            description
              "Matches the specified prefix length.";
          }
        }

        container next-hop {
          description
            "Match IPv6 next-hop address of route.";

          leaf address {
            type ntos-inet:ipv6-address {
              ntos-extensions:nc-cli-shortdesc "<X:X::X:X>";
            }
            description
              "IPv6 address of next hop.";
          }
        }
      }

      leaf local-preference {
        type uint32;
        description
          "Match local-preference metric value.";
      }

      leaf mac-address {
        type string {
          length "1..max";
        }
        description
          "Match MAC Access-list name.";
      }

      leaf metric {
        type uint32;
        description
          "Match metric value.";
      }

      leaf origin {
        type enumeration {
          enum egp {
            description
              "Remote EGP.";
          }
          enum igp {
            description
              "Local IGP.";
          }
          enum incomplete {
            description
              "Unknown heritage.";
          }
        }
        description
          "BGP origin code.";
      }

      leaf peer {
        type union {
          type enumeration {
            enum local {
              description
                "Static or redistributed routes.";
            }
          }
          type ntos-inet:ipv4-address {
            ntos-extensions:nc-cli-shortdesc "<A.B.C.D>";
          }
          type ntos-inet:ipv6-address {
            ntos-extensions:nc-cli-shortdesc "<X:X::X:X>";
          }
          type ntos-types:ifname;
        }
        description
          "Match peer address.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:config/vrf/ntos-interface:interface/*/*[local-name()='name']";
      }

      leaf probability {
        type uint8 {
          range "0..100";
        }
        description
          "Match portion of routes defined by percentage value.";
      }

      leaf source-instance {
        type uint8;
        description
          "Match the protocol's instance number.";
      }

      leaf source-protocol {
        type enumeration {
          enum babel {
            description
              "BABEL protocol.";
          }
          enum bgp {
            description
              "BGP protocol.";
          }
          enum connected {
            description
              "Routes from directly connected peer.";
          }
          enum eigrp {
            description
              "EIGRP protocol.";
          }
          enum isis {
            description
              "ISIS protocol.";
          }
          enum kernel {
            description
              "Routes from kernel.";
          }
          enum nhrp {
            description
              "NHRP protocol.";
          }
          enum ospf {
            description
              "OSPF protocol.";
          }
          enum ospf6 {
            description
              "OSPF6 protocol.";
          }
          enum pim {
            description
              "PIM protocol.";
          }
          enum rip {
            description
              "RIP protocol.";
          }
          enum ripng {
            description
              "RIPNG protocol.";
          }
          enum sharp {
            description
              "SHARP process.";
          }
          enum static {
            description
              "Statically configured routes.";
          }
          enum system {
            description
              "Routes from system configuration.";
          }
        }
        description
          "Match protocol via which the route was learnt.";
      }

      leaf tag {
        type uint32;
        description
          "Match tag of route.";
      }
    }

    container set {
      description
        "Set values in destination routing protocol.";

      container aggregator {
        presence "Makes aggregator available";
        description
          "BGP aggregator attribute.";
        ntos-extensions:nc-cli-one-liner;

        leaf as {
          type uint32;
          mandatory true;
          description
            "AS number of BGP aggregator.";
        }

        leaf address {
          type ntos-inet:ipv4-address {
            ntos-extensions:nc-cli-shortdesc "<A.B.C.D>";
          }
          mandatory true;
          description
            "IP address of aggregator.";
        }
      }

      container as-path {
        description
          "Transform BGP AS-path attribute.";

        leaf-list exclude {
          type uint32;
          description
            "AS numbers to exclude from the as-path.";
        }

        container prepend {
          description
            "Prepend to the as-path.";
          ntos-extensions:nc-cli-exclusive;

          list asn {
            key "id";
            description
              "AS number to prepend to the as-path list.";

            leaf id {
              type uint8;
              description
                "Order of the AS number.";
            }

            leaf value {
              type uint32;
              mandatory true;
              description
                "AS number.";
              ntos-extensions:nc-cli-no-name;
            }
          }

          leaf last-as {
            when 'count(../asn) = 0';
            type uint8 {
              range "1..10";
            }
            description
              "Use the peer's AS-number; number of times to insert.";
          }
        }
      }

      leaf table {
        type uint32 {
          range "1..**********";
        }
        description
          "Export route to non-main table.";
      }

      leaf atomic-aggregate {
        type boolean;
        description
          "Enable or disable BGP atomic aggregate attribute.";
      }

      container ip {
        description
          "IP information.";

        leaf next-hop {
          type union {
            type ntos-inet:ipv4-address {
              ntos-extensions:nc-cli-shortdesc "<A.B.C.D>";
            }
            type enumeration {
              enum peer-address {
                description
                  "Use peer address (for BGP only).";
              }
              enum unchanged {
                description
                  "Don't modify existing Next hop address.";
              }
            }
          }
          description
            "Next hop address.";
        }
      }

      container ipv4 {
        description
          "IPv4 information.";

        container vpn {
          description
            "VPN information.";

          leaf next-hop {
            type ntos-inet:ipv4-address {
              ntos-extensions:nc-cli-shortdesc "<A.B.C.D>";
            }
            description
              "VPN next-hop address.";
          }
        }
      }

      container ipv6 {
        description
          "IPv6 information.";

        container next-hop {
          description
            "IPv6 next hop address.";

          leaf global {
            type ntos-inet:ipv6-address {
              pattern '[fF][eE]80:.*' {
                modifier invert-match;
              }
              pattern '(([fF]{2}[0-9a-fA-F]{2}):).*' {
                modifier invert-match;
              }
              pattern ':.*' {
                modifier invert-match;
              }
              ntos-extensions:nc-cli-shortdesc "<X:X::X:X>";
            }
            description
              "IPv6 global address.";
          }

          leaf local {
            type ntos-inet:ipv6-link-local-address {
              ntos-extensions:nc-cli-shortdesc "<X:X::X:X>";
            }
            description
              "IPv6 local address.";
          }

          leaf peer-address {
            type boolean;
            description
              "If true, use peer address (for BGP only).";
          }

          leaf prefer-global {
            type boolean;
            description
              "If true, prefer global over link-local if both exist.";
          }
        }

        container vpn {
          description
            "VPN information.";

          leaf next-hop {
            type ntos-inet:ipv6-address {
              ntos-extensions:nc-cli-shortdesc "<X:X::X:X>";
            }
            description
              "VPN next-hop address.";
          }
        }
      }

      leaf label-index {
        type uint32 {
          range "0..1048560";
        }
        description
          "Label index value.";
      }

      leaf local-preference {
        type uint32;
        description
          "BGP local preference path attribute.";
      }

      leaf metric {
        type union {
          type uint32;
          type enumeration {
            enum add-metric {
              description
                "Add metric.";
            }
            enum add-rtt {
              description
                "Add round trip time.";
            }
            enum substract-metric {
              description
                "Subtract metric.";
            }
            enum substract-rtt {
              description
                "Subtract round trip time.";
            }
            enum rtt {
              description
                "Assign round trip time.";
            }
          }
        }
        description
          "Metric value for destination routing protocol.";
      }

      leaf metric-type {
        type enumeration {
          enum type-1 {
            description
              "OSPF6 external type 1 metric.";
          }
          enum type-2 {
            description
              "OSPF6 external type 2 metric.";
          }
        }
        description
          "Type of metric.";
      }

      leaf origin {
        type enumeration {
          enum egp {
            description
              "Remote EGP.";
          }
          enum igp {
            description
              "Local IGP.";
          }
          enum incomplete {
            description
              "Unknown heritage.";
          }
        }
        description
          "BGP origin code.";
      }

      leaf originator-id {
        type ntos-inet:ipv4-address {
          ntos-extensions:nc-cli-shortdesc "<A.B.C.D>";
        }
        description
          "BGP originator ID attribute.";
      }

      leaf src {
        type union {
          type ntos-inet:ipv4-address {
            ntos-extensions:nc-cli-shortdesc "<A.B.C.D>";
          }
          type ntos-inet:ipv6-address {
            ntos-extensions:nc-cli-shortdesc "<X:X::X:X>";
          }
        }
        description
          "Src address for route.";
      }

      leaf tag {
        type uint32 {
          range "1..**********";
        }
        description
          "Tag value for routing protocol.";
      }

      leaf weight {
        type uint32;
        description
          "BGP weight for routing table.";
      }
    }

    leaf call {
      type string {
        length "1..max";
      }
      description
        "Jump to another Route-Map after match+set.";
    }

    leaf on-match {
      type union {
        type uint16 {
          range "1..65535";
        }
        type enumeration {
          enum next {
            description
              "Next clause.";
          }
        }
      }
      must "../policy = 'permit'" {
        error-message "on-match not supported under route-map deny";
      }
      description
        "Exit policy on matches.";
    }
  }

  grouping logging-config {
    description
      "Logs configuration.";

    container logging {
      description
        "Logs configuration.";

      leaf enabled {
        type boolean;
        default "true";
        description
          "Enable/Disable routing logs.";
      }

      leaf level {
        type ntos-types:log-level;
        default "error";
        description
          "Set minimal logging level.";
      }

      container mpls {
        presence "Makes MPLS available.";
        description
          "MPLS logging configuration.";
      }
    }
  }

  grouping rib-next-hop-group {
    description
      "Configuration for rib next-hop.";

    leaf protocol {
      type enumeration {
        enum babel {
          description
            "BABEL protocol.";
        }
        enum bgp {
          description
            "BGP protocol.";
        }
        enum connected {
          description
            "Routes from directly connected peer.";
        }
        enum eigrp {
          description
            "EIGRP protocol.";
        }
        enum isis {
          description
            "ISIS protocol.";
        }
        enum kernel {
          description
            "Routes from kernel.";
        }
        enum nhrp {
          description
            "NHRP protocol.";
        }
        enum ospf {
          description
            "OSPF protocol.";
        }
        enum ospf6 {
          description
            "OSPF6 protocol.";
        }
        enum pim {
          description
            "PIM protocol.";
        }
        enum rip {
          description
            "RIP protocol.";
        }
        enum ripng {
          description
            "RIPNG protocol.";
        }
        enum sharp {
          description
            "SHARP process.";
        }
        enum static {
          description
            "Statically configured routes.";
        }
        enum system {
          description
            "Routes from system configuration.";
        }
      }
      description
        "Route protocol.";
    }

    leaf distance {
      type uint8 {
        range "1..255";
      }
      description
        "Distance value for this route.";
    }

    leaf metric {
      type uint32;
      description
        "Route metric.";
    }

    leaf interface {
      type ntos-types:ifname;
      description
        "Output interface.";
    }

    leaf selected {
      type boolean;
      description
        "If true, route is selected.";
    }

    leaf fib {
      type boolean;
      description
        "If true, route is in Forwarding Information Base.";
    }

    leaf directly-connected {
      type boolean;
      description
        "If true, route is directly connected.";
    }

    leaf duplicate {
      type boolean;
      description
        "If true, route is duplicate.";
    }

    leaf active {
      type boolean;
      description
        "If true, route is active.";
    }

    leaf on-link {
      type boolean;
      description
        "If true, on link is set.";
    }

    leaf recursive {
      type boolean;
      description
        "If true, recursive is set.";
    }

    leaf uptime {
      type string;
      description
        "Route uptime.";
    }
  }

  grouping routes-count {
    description
      "Routes count.";

    leaf routes {
      type uint32;
      description
        "Number of routes in RIB.";
    }

    leaf installed-routes {
      type uint32;
      description
        "Number of routes in FIB.";
    }
  }

  grouping ipv4-proto-rtmap {
    description
      "IPv4 protocol route map grouping.";

    list ip {
      key "protocol name";
      description
        "IP route map.";
      ntos-extensions:nc-cli-one-liner;

      leaf protocol {
        type enumeration {
          enum any {
            description
              "Any of the above protocols.";
          }
          enum bgp {
            description
              "Border Gateway Protocol (BGP).";
          }
          enum connected {
            description
              "Connected routes (directly attached subnet or host).";
          }
          enum ospf {
            description
              "Open Shortest Path First (OSPFv2).";
          }
          enum rip {
            description
              "Routing Information Protocol (RIP).";
          }
          enum static {
            description
              "Statically configured routes.";
          }
        }
        description
          "The protocol on which the route map will be applied.";
      }

      leaf name {
        type route-map-name;
        description
          "The route map that will be applied.";
      }
    }
  }

  grouping ipv6-proto-rtmap {
    description
      "IPv6 protocol route map grouping.";

    list ipv6 {
      key "protocol name";
      description
        "IPv6 route map.";
      ntos-extensions:nc-cli-one-liner;

      leaf protocol {
        type enumeration {
          enum any {
            description
              "Any of the above protocols.";
          }
          enum bgp {
            description
              "Border Gateway Protocol (BGP).";
          }
          enum connected {
            description
              "Connected routes (directly attached subnet or host).";
          }
          enum ospf6 {
            description
              "Open Shortest Path First (IPv6) (OSPFv3).";
          }
          enum ripng {
            description
              "Routing Information Protocol next-generation (IPv6) (RIPng).";
          }
          enum static {
            description
              "Statically configured routes.";
          }
        }
        description
          "The protocol on which the route map will be applied.";
      }

      leaf name {
        type route-map-name;
        description
          "The route map that will be applied.";
      }
    }
  }

  grouping rib-stats {
    description
      "RIB statistics.";

    container kernel {
      description
        "Kernel routes count.";
      ntos-extensions:nc-cli-one-liner;
      uses routes-count;
    }

    container connected {
      description
        "Connected routes count.";
      ntos-extensions:nc-cli-one-liner;
      uses routes-count;
    }

    container static {
      description
        "Static routes count.";
      ntos-extensions:nc-cli-one-liner;
      uses routes-count;
    }

    container ospf {
      description
        "OSPF routes count.";
      ntos-extensions:nc-cli-one-liner;
      uses routes-count;
    }

    container ebgp {
      description
        "EBGP routes count.";
      ntos-extensions:nc-cli-one-liner;
      uses routes-count;
    }

    container ibgp {
      description
        "IBGP routes count.";
      ntos-extensions:nc-cli-one-liner;
      uses routes-count;
    }

    container nhrp {
      description
        "NHRP routes count.";
      ntos-extensions:nc-cli-one-liner;
      uses routes-count;
    }

    container total {
      description
        "Total routes count.";
      ntos-extensions:nc-cli-one-liner;
      uses routes-count;
    }
  }

  grouping reverse-path {
    description
      "Configure reverse path limite.";

    leaf reverse-path {
      type boolean;
      default "true";
      description
        "Enable reverse path limite.";
    }
  }

  rpc show-ipv4-routes {
    description
      "Show IPv4 routing table.";
    input {
      must 'not(to and table)' {
        error-message "to is not compatible with table.";
      }
      must 'not(to and protocol)' {
        error-message "to is not compatible with protocol.";
      }

      leaf vrf {
        type string;
        default "main";
        description
          "Specify the VRF.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf protocol {
        type identityref {
          base ntos-types:ROUTE4_FRR_ID;
        }
        description
          "Filter routes by protocol.";
        ntos-extensions:nc-cli-group "subcommand1";
      }

      leaf table {
        type uint32 {
          range "1..**********";
        }
        description
          "Non-main Kernel Routing Table.";
        ntos-extensions:nc-cli-group "subcommand2";
      }

      leaf to {
        type union {
          type ntos-inet:ipv4-address {
            ntos-extensions:nc-cli-shortdesc "<A.B.C.D>";
          }
          type ntos-inet:ipv4-prefix {
            ntos-extensions:nc-cli-shortdesc "<A.B.C.D/M>";
          }
        }
        description
          "Find the route entry used to reach an IP address or an exact network.";
        ntos-extensions:nc-cli-group "subcommand1";
        ntos-extensions:nc-cli-group "subcommand2";
      }
    }
    output {
      leaf buffer {
        type string;
        description
          "The command output buffer.";
        ntos-extensions:nc-cli-stdout;
        ntos-extensions:nc-cli-hidden;
      }
    }
    ntos-extensions:nc-cli-show "ipv4-routes";
    ntos-api:internal;
  }

  rpc show-ipv4-routes-json {
    description
      "Show IPv4 routing table in json.";
    input {
      leaf vrf {
        type string;
        default "main";
        description
          "Specify the VRF.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf match {
        type string {
          length "1..32";
        }
        description
          "fuzzy matching (1-32 characters).";
      }

      leaf start {
        type int16 {
          range "1..32767";
        }
        description
          "Iterate start position.";
      }

      leaf count {
        type int16 {
          range "1..50";
        }
        description
          "Iterate counts.";
      }
    }
    output {
      leaf buffer {
        type string;
        description
          "The command output buffer.";
        ntos-extensions:nc-cli-stdout;
        ntos-extensions:nc-cli-hidden;
      }
    }
  }

  rpc show-ipv4-routes-isp {
    description
      "Show all IPv4 isp routing table.";
    input {
      leaf vrf {
        type string;
        default "main";
        description
          "Specify the VRF.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf to {
        type union {
          type ntos-inet:ipv4-address {
            ntos-extensions:nc-cli-shortdesc "<A.B.C.D>";
          }
          type ntos-inet:ipv4-prefix {
            ntos-extensions:nc-cli-shortdesc "<A.B.C.D/M>";
          }
        }
        description
          "Find the route entry used to reach an IP address or an exact network.";
      }
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-extensions:nc-cli-hidden;
    ntos-extensions:nc-cli-show "ipv4-routes-isp";
    ntos-api:internal;
  }

  rpc show-static-config-json {
    description
      "Static routing information.";
    input {
      leaf vrf {
        type string;
        default "main";
        description
          "Specify the VRF.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      uses static-route-ipv4-config;

      leaf next-hop {
        type union {
          type ntos-inet:ipv4-address {
            ntos-extensions:nc-cli-shortdesc "<A.B.C.D>";
          }
          type ntos-types:ifname;
          type ipv4-address-and-ifname;
          type enumeration {
            enum blackhole {
              description
                "Silently discard packets when matched.";
            }
          }
        }
        must 'count(../destination) > 0' {
          error-message "next-hop must be set with destination.";
        }
      }

      leaf match {
        type string {
          length "1..32";
        }
        description
          "Fuzzy matching.";
        must 'count(../destination) = 0' {
          error-message "match must not be set with destination.";
        }
      }
    }
    output {
      leaf buffer {
        type string;
        description
          "The command output buffer.";
        ntos-extensions:nc-cli-stdout;
        ntos-extensions:nc-cli-hidden;
      }
    }
  }

  rpc show-ipv6-routes {
    description
      "Show IPv6 routing table.";
    input {
      must 'not(to and table)' {
        error-message "to is not compatible with table.";
      }
      must 'not(to and protocol)' {
        error-message "to is not compatible with protocol.";
      }

      leaf vrf {
        type string;
        default "main";
        description
          "Specify the VRF.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf protocol {
        type identityref {
          base ntos-types:ROUTE6_FRR_ID;
        }
        description
          "Filter routes by protocol.";
        ntos-extensions:nc-cli-group "subcommand1";
      }

      leaf table {
        type uint32 {
          range "1..**********";
        }
        description
          "Non-main Kernel Routing Table.";
        ntos-extensions:nc-cli-group "subcommand2";
      }

      leaf to {
        type union {
          type ntos-inet:ipv6-address {
            ntos-extensions:nc-cli-shortdesc "<X:X::X:X>";
          }
          type ntos-inet:ipv6-prefix {
            ntos-extensions:nc-cli-shortdesc "<X:X::X:X/M>";
          }
        }
        description
          "Find the route entry used to reach an IPv6 address or an exact network.";
        ntos-extensions:nc-cli-group "subcommand1";
        ntos-extensions:nc-cli-group "subcommand2";
      }
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-extensions:nc-cli-show "ipv6-routes";
    ntos-api:internal;
  }

  rpc show-ipv6-routes-json {
    description
      "Show IPv6 routing table in json.";
    input {
      leaf vrf {
        type string;
        default "main";
        description
          "Specify the VRF.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf match {
        type string {
          length "1..32";
        }
        description
          "fuzzy matching (1-32 characters).";
      }

      leaf start {
        type int16 {
          range "1..32767";
        }
        description
          "Iterate start position.";
      }

      leaf count {
        type int16 {
          range "1..50";
        }
        description
          "Iterate counts.";
      }
    }
    output {
      leaf buffer {
        type string;
        description
          "The command output buffer.";
        ntos-extensions:nc-cli-stdout;
      }
    }
    ntos-extensions:nc-cli-show "ipv6-routes json";
    ntos-api:internal;
  }

  rpc show-static-ipv6-config-json {
    description
      "Static routing information.";
    input {
      leaf vrf {
        type string;
        default "main";
        description
          "Specify the VRF.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      uses static-route-ipv6-config;

      leaf next-hop {
        type union {
          type ntos-inet:ipv6-address {
            ntos-extensions:nc-cli-shortdesc "<X:X::X:X>";
          }
          type ntos-types:ifname;
          type ipv6-address-and-ifname;
          type enumeration {
            enum blackhole {
              description
                "Silently discard packets when matched.";
            }
          }
        }
        must 'count(../destination) > 0' {
          error-message "next-hop must be set with destination.";
        }
      }

      leaf match {
        type string {
          length "1..32";
        }
        description
          "Fuzzy matching.";
        must 'count(../destination) = 0' {
          error-message "match must not be set with destination.";
        }
      }
    }
    output {
      leaf buffer {
        type string;
        description
          "The command output buffer.";
        ntos-extensions:nc-cli-stdout;
      }
    }
    ntos-extensions:nc-cli-show "ipv6-config json";
    ntos-api:internal;
  }

  rpc show-mpls-table {
    description
      "Show MPLS table information.";
    input {

      leaf lsp {
        type uint32 {
          range "16..1048575";
        }
        description
          "LSP to display information about.";
        ntos-extensions:nc-cli-no-name;
      }
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-extensions:feature "product";
    ntos-extensions:nc-cli-show "mpls table";
    ntos-api:internal;
  }

  rpc frr-exec {
    description
      "FRRouting exec.";
    input {
      leaf config {
        type boolean;
        default false;
        description
          "config mode.";
      }

      leaf command {
        type string;
        description
          "exec command";
      }
    }
    output {
      leaf buffer {
        type string;
        description
          "Command output buffer.";
        ntos-extensions:nc-cli-stdout;
        ntos-extensions:nc-cli-hidden;
      }
    }
    ntos-extensions:nc-cli-cmd "frr exec";
    ntos-extensions:nc-cli-command-no-pager;
    ntos-extensions:nc-cli-hidden;
    ntos-api:internal;
  }

  augment "/ntos:config" {
    description
      "Routing global configuration.";

    container routing {
      presence "Makes routing available";
      description
        "Routing global configuration.";
      ntos-extensions:feature "product";
      uses ipv4-access-list-config;
      uses ipv6-access-list-config;
      uses logging-config;

      list ipv4-prefix-list {
        key "name";
        description
          "IPv4 prefix list.";

        leaf name {
          type string {
            length "1..max";
          }
          description
            "Prefix list name.";
        }

        list seq {
          key "num";
          min-elements 1;
          description
            "Prefix list sequence.";
          ntos-extensions:nc-cli-one-liner;
          ntos-extensions:nc-cli-sort-by "num";
          uses prefix-list-seq-ipv4-config;
        }
      }

      list ipv6-prefix-list {
        key "name";
        description
          "IPv6 prefix list.";

        leaf name {
          type string {
            length "1..max";
          }
          description
            "Prefix list name.";
        }

        list seq {
          key "num";
          min-elements 1;
          description
            "Prefix list sequence.";
          ntos-extensions:nc-cli-one-liner;
          ntos-extensions:nc-cli-sort-by "num";
          uses prefix-list-seq-ipv6-config;
        }
      }

      list route-map {
        key "name";
        description
          "Route map list.";

        leaf name {
          type string {
            length "1..max";
          }
          description
            "Route map name.";
        }

        list seq {
          key "num";
          min-elements 1;
          description
            "Route map sequence.";
          ntos-extensions:nc-cli-sort-by "num";
          uses route-map-seq-config;
        }
      }
    }
  }

  augment "/ntos:state" {
    description
      "Routing global operational state data.";

    container routing {
      description
        "Routing global operational state data.";
      ntos-extensions:feature "product";
      uses ipv4-access-list-config;
      uses ipv6-access-list-config;
      uses logging-config;

      list ipv4-prefix-list {
        key "name";
        description
          "IPv4 prefix list.";

        leaf name {
          type string {
            length "1..max";
          }
          description
            "Prefix list name.";
        }

        list seq {
          key "num";
          min-elements 1;
          description
            "Prefix list sequence.";
          ntos-extensions:nc-cli-one-liner;
          ntos-extensions:nc-cli-sort-by "num";
          uses prefix-list-seq-ipv4-config;
        }
      }

      list ipv6-prefix-list {
        key "name";
        description
          "IPv6 prefix list.";

        leaf name {
          type string {
            length "1..max";
          }
          description
            "Prefix list name.";
        }

        list seq {
          key "num";
          min-elements 1;
          description
            "Prefix list sequence.";
          ntos-extensions:nc-cli-one-liner;
          ntos-extensions:nc-cli-sort-by "num";
          uses prefix-list-seq-ipv6-config;
        }
      }

      list route-map {
        key "name";
        description
          "Route map list.";

        leaf name {
          type string {
            length "1..max";
          }
          description
            "Route map name.";
        }

        list seq {
          key "num";
          min-elements 1;
          description
            "Route map sequence.";
          ntos-extensions:nc-cli-sort-by "num";
          uses route-map-seq-config;
        }
      }
    }
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "Routing configuration.";

    container routing {
      description
        "Routing configuration.";

      container static {
        description
          "Static routes.";

        list ipv4-route {
          key "destination";
          description
            "List of IPv4 static routes.";
          uses static-route-ipv4-config;

          list next-hop {
            key "next-hop";
            min-elements 1;
            max-elements 256;
            description
              "Route next-hops.";
            ntos-extensions:nc-cli-one-liner;
            uses static-route-next-hop-ipv4-config-with-table;

            leaf enable {
              type boolean;
              default "true";
              description
                "Enable or disable static route configuration.";
            }

            leaf descr {
              must "../next-hop!='dhcp-gateway'" {
                error-message "Description can not be set when the next-hop is dhcp-gateway.";
              }
              type ntos-types:ntos-obj-description-type {
                  length "1..63";
              }
              description
                "description information (1-63 characters).";
            }
          }
        }

        list ipv6-route {
          key "destination";
          description
            "List of IPv6 static routes.";
          uses static-route-ipv6-config;
		  
          must 'count(./next-hop) < 9' {                     
            error-message "Too many ipv6 \"next-hop\" elements";
          }
		  
          list next-hop {
            key "next-hop";
            min-elements 1;
            description
              "Route next-hops.";
            ntos-extensions:nc-cli-one-liner;
            uses static-route-next-hop-ipv6-config-with-table;

            leaf description {
              must "../next-hop!='dhcp-gateway'" {
                error-message "Description can not be set when the next-hop is dhcp-gateway.";
              }
              type ntos-types:ntos-obj-description-type {
                  length "1..63";
              }
              description
                "You can briefly describe the purpose of this route or other hints here.
                 String length is limited to 1..63 characters.";
            }
          }
        }

        list table {
          key "table-id";
          description
            "List of routing tables.";
          uses routing-table-config;

          list ipv4-route {
            key "destination";
            description
              "List of IPv4 static routes.";
            uses static-route-ipv4-config;

            list next-hop {
              key "next-hop";
              min-elements 1;
              description
                "Route next-hops.";
              ntos-extensions:nc-cli-one-liner;
              uses static-route-next-hop-ipv4-config;
            }
          }

          list ipv6-route {
            key "destination";
            description
              "List of IPv6 static routes.";
            uses static-route-ipv6-config;

            list next-hop {
              key "next-hop";
              min-elements 1;
              description
                "Route next-hops.";
              ntos-extensions:nc-cli-one-liner;
              uses static-route-next-hop-ipv6-config;
            }
          }
        }
      }

      list interface {
        key "name";
        description
          "Interface-specific routing configuration.";
        ntos-extensions:feature "product";

        leaf name {
          type ntos-types:ifname;
          description
            "Interface name.";
          ntos-extensions:nc-cli-completion-xpath
            "../ntos-interface:interface/*/*[local-name()='name']";
        }

        leaf bandwidth {
          type uint32 {
            range "1..1000000";
          }
          units "megabits/sec";
          description
            "Set bandwidth value of the interface in megabits/sec. This is for
             calculating OSPFv2 and OSPFv3 cost. This command does not affect the
             actual device configuration. It is overridden by setting the cost
             with ip ospf cost.";
        }

        container ip {
          description
            "IP information.";
        }

        container ipv6 {
          description
            "IPv6 information.";
        }
      }

      container mpls {
        presence "Makes MPLS available.";
        description
          "MPLS configuration.";
        ntos-extensions:feature "product";
      }

      container route-map {
        presence "Makes route map available.";
        description
          "Configure a route map that will be applied to the routes learnt from a protocol.";
        ntos-extensions:feature "product";
        uses ipv4-proto-rtmap;
        uses ipv6-proto-rtmap;
      }
    }
  }

  augment "/ntos:state/ntos:vrf" {
    description
      "Routing operational state data.";

    container routing {
      description
        "Routing operational state data.";

      container rib {
        description
          "Routing information base.";

        container ipv4-count {
          description
            "IPv4 routes statistics.";
          uses rib-stats;
        }

        container ipv6-count {
          description
            "IPv6 routes statistics.";
          uses rib-stats;
        }

        list ipv4-route {
          key "destination";
          description
            "IPv4 routes in RIB.";

          leaf destination {
            type ntos-inet:ipv4-prefix {
              ntos-extensions:nc-cli-shortdesc "<A.B.C.D/M>";
            }
            description
              "Route destination.";
          }

          list next-hop {
            key "next-hop";
            description
              "Route next-hops.";

            leaf next-hop {
              type union {
                type ntos-inet:ipv4-address {
                  ntos-extensions:nc-cli-shortdesc "<A.B.C.D>";
                }
                type ntos-types:ifname;
                type enumeration {
                  enum blackhole {
                    description
                      "Silently discard packets when matched.";
                  }
                  enum reject {
                    description
                      "Emit an ICMP unreachable when matched.";
                  }
                  enum admin-prohibited {
                    description
                      "Emit an ICMP admin-prohibited when matched.";
                  }
                }
              }
              description
                "Route next-hop.";
            }
            uses rib-next-hop-group;
          }
        }

        list ipv6-route {
          key "destination";
          description
            "IPv6 routes in RIB.";

          leaf destination {
            type ntos-inet:ipv6-prefix {
              ntos-extensions:nc-cli-shortdesc "<X:X::X:X/M>";
            }
            description
              "Route destination.";
          }

          list next-hop {
            key "next-hop";
            description
              "Route next-hops.";

            leaf next-hop {
              type union {
                type ntos-inet:ipv6-address {
                  ntos-extensions:nc-cli-shortdesc "<X:X::X:X>";
                }
                type ntos-types:ifname;
                type enumeration {
                  enum blackhole {
                    description
                      "Silently discard packets when matched.";
                  }
                  enum reject {
                    description
                      "Emit an ICMP unreachable when matched.";
                  }
                  enum admin-prohibited {
                    description
                      "Emit an ICMP admin-prohibited when matched.";
                  }
                }
              }
              description
                "Route next-hop.";
            }
            uses rib-next-hop-group;
          }
        }
      }

      container static {
        description
          "Static routes.";

        leaf ipv4-count {
          type uint32;
          description
            "IPv4 static routes statistics.";
        }

        leaf ipv6-count {
          type uint32;
          description
            "IPv6 static routes statistics.";
        }

        list ipv4-route {
          key "destination";
          description
            "List of IPv4 static routes.";
          uses static-route-ipv4-config;

          list next-hop {
            key "next-hop";
            min-elements 1;
            description
              "Route next-hops.";
            ntos-extensions:nc-cli-one-liner;
            uses static-route-next-hop-ipv4-config-with-table;
          }
        }

        list ipv6-route {
          key "destination";
          description
            "List of IPv6 static routes.";
          uses static-route-ipv6-config;

          list next-hop {
            key "next-hop";
            min-elements 1;
            description
              "Route next-hops.";
            ntos-extensions:nc-cli-one-liner;
            uses static-route-next-hop-ipv6-config-with-table;
          }
        }

        list table {
          key "table-id";
          description
            "List of routing tables.";
          uses routing-table-config;

          list ipv4-route {
            key "destination";
            description
              "List of IPv4 static routes.";
            uses static-route-ipv4-config;

            list next-hop {
              key "next-hop";
              min-elements 1;
              description
                "Route next-hops.";
              ntos-extensions:nc-cli-one-liner;
              uses static-route-next-hop-ipv4-config;
            }
          }

          list ipv6-route {
            key "destination";
            description
              "List of IPv6 static routes.";
            uses static-route-ipv6-config;

            list next-hop {
              key "next-hop";
              min-elements 1;
              description
                "Route next-hops.";
              ntos-extensions:nc-cli-one-liner;
              uses static-route-next-hop-ipv6-config;
            }
          }
        }
      }

      list interface {
        key "name";
        description
          "Interface-specific routing operational state data.";
        ntos-extensions:feature "product";

        leaf name {
          type ntos-types:ifname;
          description
            "Interface name.";
        }

        leaf bandwidth {
          type uint32;
          units "megabits/sec";
          description
            "Set bandwidth value of the interface in megabits/sec. This is for
             calculating OSPFv2 and OSPFv3 cost. This command does not affect the
             actual device configuration. It is overridden by setting the cost
             with ip ospf cost.";
        }

        container ip {
          description
            "IP information.";
        }

        container ipv6 {
          description
            "IPv6 information.";
        }
      }

      container mpls {
        description
          "MPLS state.";
        ntos-extensions:feature "product";
      }

      container route-map {
        presence "Makes route map available.";
        description
          "Route map that will be applied to the routes learnt from a protocol.";
        ntos-extensions:feature "product";
        uses ipv4-proto-rtmap;
        uses ipv6-proto-rtmap;
      }
    }
  }
}
