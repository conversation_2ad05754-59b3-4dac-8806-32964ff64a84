module ntos-security-defend {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:security-defend";
  prefix ntos-security-defend;

  import ntos {
    prefix ntos;
  }

  import ntos-inet-types {
    prefix ntos-inet;
  }

  import ntos-types {
    prefix ntos-types;
  }

  import ntos-extensions {
    prefix ntos-ext;
  }

  import ntos-network-obj {
    prefix ntos-network-obj;
  }

  import ntos-security-zone {
    prefix ntos-security-zone;
  }

  organization
    "Ruijie Networks Co., Ltd";
  contact
    "Tel: 4008-111-000
    E-mail: <EMAIL>";
  description
    "This module contains the YANG definition for security defend.";

  revision 2024-12-05 {
    description
      "add ip white/blacklist's capacity/usage statistics query for unc.";
  }

  revision 2024-08-20 {
    description
      "1,Add import/export for mac-block; 2,Add block mode for mac-block.";
  }

  revision 2022-12-06 {
    description
      "Add IPv6 address filter.";
  }

  revision 2022-09-20 {
    description
      "Add IPv6.";
  }

  revision 2022-02-21 {
    description
      "Definition for security defend status.";
  }

  typedef ipv4-address {
    type union {
      type ntos-inet:ipv4-address {
        ntos-ext:nc-cli-shortdesc "<ipv4-address>";
      }
      type ntos-inet:ipv4-range {
        ntos-ext:nc-cli-shortdesc "<ipv4-range>";
      }
      type string {
        pattern
            '(([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\.){3}'
          +  '([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])([/][1-9]{1,2})';
          ntos-ext:nc-cli-shortdesc "<ipv4/mask>";
      }
    }
    description
      "An IPv4 address or addresses range.";
  }

  typedef ipv6-address {
    type union {
      type ntos-inet:ipv6-address {
        ntos-ext:nc-cli-shortdesc "<ipv6-address>";
      }
      type ntos-inet:ipv6-range {
        ntos-ext:nc-cli-shortdesc "<ipv6-range>";
      }
      type ntos-inet:masked-ipv6-address {
        ntos-ext:nc-cli-shortdesc "<ipv6/mask>";
      }
    }
    description
      "An IPv6 address or addresses range.";
  }

  typedef network-obj {
    type ntos-types:ntos-obj-name-type;
  }

  grouping vrf {
    leaf vrf {
      description
        "VRF name.";

      type string;
      default "main";
      ntos-ext:nc-cli-completion-xpath
        "/ntos:state/ntos:vrf/ntos:name";
    }
  }

  grouping traffic-monitor {
    container traffic-monitor {
      description
        "Traffic-monitor configuration.";

      leaf tcp {
        description
          "Enable or disable TCP traffic-monitor.";

        type boolean;
        default "false";
      }

      leaf udp {
        description
          "Enable or disable UDP traffic-monitor.";

        type boolean;
        default "false";
      }

      leaf icmp {
        description
          "Enable or disable ICMP traffic-monitor.";

        type boolean;
        default "false";
      }

      leaf icmpv6 {
        description
          "Enable or disable ICMPv6 traffic-monitor.";

        type boolean;
        default "false";
      }

      leaf ip {
        description
          "Enable or disable IP traffic-monitor.";

        type boolean;
        default "false";
      }

      leaf http {
        description
          "Enable or disable HTTP traffic-monitor.";

        type boolean;
        default "false";
      }
    }
  }

  grouping proto-attack {
    container proto-attack {
      description
          "Proto-attack defend configuration.";

      leaf teardrop {
        description
          "Enable or disable teardrop proto-attack defend.";

        type boolean;
        default "false";
      }

      leaf src-route {
        description
          "Enable or disable src-route proto-attack defend.";

        type boolean;
        default "false";
      }

      leaf route-record {
        description
          "Enable or disable route-record proto-attack defend.";

        type boolean;
        default "false";
      }

      leaf smurf {
        description
          "Enable or disable smurf proto-attack defend.";

        type boolean;
        default "false";
      }

      leaf redirect {
        description
          "Enable or disable redirect proto-attack defend.";

        type boolean;
        default "false";
      }

      leaf icmp-unreach {
        description
          "Enable or disable icmp-unreach proto-attack defend.";

        type boolean;
        default "false";
      }

      leaf land {
        description
          "Enable or disable land proto-attack defend.";

        type boolean;
        default "false";
      }

      leaf winnuke {
        description
          "Enable or disable winnuke proto-attack defend.";

        type boolean;
        default "false";
      }

      leaf fraggle {
        description
          "Enable or disable fraggle proto-attack defend.";

        type boolean;
        default "false";
      }

      container large-icmp {
        description
          "Large-icmp proto-attack defend configuration.";

        leaf enabled {
          description
            "Enable or disable large-icmp proto-attack defend.";

          type boolean;
          default "false";
        }
        leaf len {
          description
            "Length definition of large-icmp.";

          type uint16;
          default "1500";
        }
      }

      container ipv6-extend-header {
        description
          "ipv6 extend header defend configuration.";

        leaf ah {
          description
            "Enable or disable ipv6 AH extend header defend.";

          type boolean;
          default "false";
        }

        leaf destination {
          description
            "Enable or disable ipv6 destination extend header defend.";

          type boolean;
          default "false";
        }

        leaf esp {
          description
            "Enable or disable ipv6 ESP extend header defend.";

          type boolean;
          default "false";
        }

        leaf fragment {
          description
            "Enable or disable ipv6 fragment extend header defend.";

          type boolean;
          default "false";
        }

        leaf hop-by-hop {
          description
            "Enable or disable ipv6 hop-by-hop extend header defend.";

          type boolean;
          default "false";
        }

        leaf none {
          description
            "Enable or disable ipv6 none extend header defend.";

          type boolean;
          default "false";
        }

        leaf route {
          description
            "Enable or disable ipv6 route extend header defend.";

          type boolean;
          default "false";
        }
      }
    }
  }

  grouping plcy-parms {
    leaf enabled {
      description
        "Enable or disable defend-policy.";

      type boolean;
      default "false";
    }

    leaf threshold {
      description
        "After the policy was triggered, the threshold is the value of block.";

      type uint32 {
        range '1..999999999';
      }
      default "2000";
    }

    leaf timeout {
      description
        "Duration of blocking the host.";

      type uint32 {
        range '1..3600';
      }
      default "300";
    }
  }

  grouping plcy-parms-scan {
    leaf enabled {
      description
        "Enable or disable defend-policy.";

      type boolean;
      default "false";
    }

    leaf threshold {
      description
        "After the policy was triggered, the threshold is the value of block.";

      type uint32 {
        range '1..5000';
      }
      default "100";
    }

    leaf timeout {
      description
        "Duration of blocking the host.";

      type uint32 {
        range '1..3600';
      }
      default "300";
    }
  }

  grouping plcy-parms-limit {
    leaf enabled {
      description
        "Enable or disable defend-policy.";

      type boolean;
      default "false";
    }

    leaf threshold {
      description
        "After the policy was triggered, the threshold is the value of limite.";

      type uint32 {
        range '1..999999999';
      }
      default "10000";
    }

    leaf timeout {
      description
        "Duration of limiting the host.";

      type uint32 {
        range '1..3600';
      }
      default "300";
    }
  }

  grouping defend-policy {
    container tcp {
      description
        "TCP source defend configuration.";

      container syns-in {
        container host {
          uses plcy-parms;
        }
      }

      container dst-syns-in {
        container host {
          uses plcy-parms-limit;
        }
      }
    }

    container udp {
      description
        "UDP source defend configuration.";

      container src-in {
        container host {
          uses plcy-parms;
        }
      }

      container dst-in {
        container host {
          uses plcy-parms-limit;
        }
      }
    }

    container icmp {
      description
        "ICMP source defend configuration.";

      container src-in {
        container host {
          uses plcy-parms;
        }
      }

      container dst-in {
        container host {
          uses plcy-parms-limit;
        }
      }
    }

    container icmpv6 {
      description
        "ICMPv6 source defend configuration.";

      container src-in {
        container host {
          uses plcy-parms;
        }
      }

      container dst-in {
        container host {
          uses plcy-parms-limit;
        }
      }
    }

    container other-protocol {
      description
        "Other-protocol source defend configuration.";

      container src-in {
        container host {
          uses plcy-parms;
        }
      }

      container dst-in {
        container host {
          uses plcy-parms-limit;
        }
      }
    }

    container scan-ip {
      description
        "IP scan defend configuration.";

      uses plcy-parms-scan;
    }

    container scan-port {
      description
        "Port scan defend configuration.";

      uses plcy-parms-scan;
    }
  }

  grouping dst-defend-policy {
    container tcp {
      description
        "TCP destination defend configuration.";

      container dst-syns-in {
        container host {
          uses plcy-parms-limit;
        }
      }
    }

    container udp {
      description
        "UDP destination defend configuration.";

      container dst-in {
        container host {
          uses plcy-parms-limit;
        }
      }
    }

    container icmp {
      description
        "ICMP destination defend configuration.";

      container dst-in {
        container host {
          uses plcy-parms-limit;
        }
      }
    }

    container icmpv6 {
      description
        "ICMPv6 destination defend configuration.";

      container dst-in {
        container host {
          uses plcy-parms-limit;
        }
      }
    }

    container other-protocol {
      description
        "Other-protocol destination defend configuration.";

      container dst-in {
        container host {
          uses plcy-parms-limit;
        }
      }
    }
  }

  grouping policy-action {
    container policy-action {
      description
        "The action of policy.";

      leaf notify {
        description
          "Record.";

        type boolean;
        default "false";
      }

      leaf block {
        description
          "Block the host.";

        type boolean;
        default "false";
      }

      leaf limit {
        description
          "Limit the host.";

        type boolean;
        default "false";
      }
    }
  }

  grouping security-defend-defend-zone {
    leaf name {
      description
        "The name of policy.";

      type ntos-types:ntos-obj-name-type;
    }

    leaf enabled {
      description
        "Enable or disable policy.";

      type boolean;
      default "true";
    }

    leaf description {
      description
        "The description of policy.";

      type ntos-types:ntos-obj-description-type;
      default "";
    }

    list source-zone {
      description
        "The attack source zone of the policy.";

      key "name";
      leaf name {
        description
          "The name of source zone.";

        type ntos-types:ntos-obj-name-type;
        ntos-ext:nc-cli-no-name;
      }

      ntos-ext:nc-cli-one-liner;
    }

    list source-network {
      description
        "The source network objects to the policy.";

      key "name";

      leaf name {
        description
          "The name of source network.";

        type network-obj;
        ntos-ext:nc-cli-no-name;
      }

      ntos-ext:nc-cli-one-liner;
    }

    list dest-network {
      description
        "The destination network objects to the policy.";

      key "name";

      leaf name {
        description
          "The name of destination network.";

        type network-obj;
        ntos-ext:nc-cli-no-name;
      }

      ntos-ext:nc-cli-one-liner;
    }

    uses defend-policy;
    uses policy-action;
    uses proto-attack;
    uses traffic-monitor;
  }

  grouping security-defend-dst-defend-zone {
    leaf name {
      description
        "The name of policy.";

      type ntos-types:ntos-obj-name-type;
    }

    leaf enabled {
      description
        "Enable or disable policy.";

      type boolean;
      default "true";
    }

    leaf description {
      description
        "The description of policy.";

      type ntos-types:ntos-obj-description-type;
      default "";
    }

    list source-zone {
      description
        "The attack source zone of the policy.";

      key "name";
      leaf name {
        description
          "The name of source zone.";

        type ntos-types:ntos-obj-name-type;
        ntos-ext:nc-cli-no-name;
      }

      ntos-ext:nc-cli-one-liner;
    }

    list source-network {
      description
        "The source network objects to the policy.";

      key "name";

      leaf name {
        description
          "The name of source network.";

        type network-obj;
        ntos-ext:nc-cli-no-name;
      }

      ntos-ext:nc-cli-one-liner;
    }

    list dest-network {
      description
        "The destination network objects to the policy.";

      key "name";

      leaf name {
        description
          "The name of destination network.";

        type network-obj;
        ntos-ext:nc-cli-no-name;
      }

      ntos-ext:nc-cli-one-liner;
    }

    uses dst-defend-policy;
    uses policy-action;
    uses proto-attack;
    uses traffic-monitor;
  }

  typedef src-type-def {
    type enumeration {
      enum manual {
        description
          "From manual configuration.";
      }
      enum bds {
        description
          "From BDS server configuration.";
      }
    }
    description
      "The source type of the list.";
  }

  grouping white-black-member {
    leaf object {
      type ipv4-address;
    }
    leaf way {
      type enumeration {
        enum dstip;
        enum srcip;
      }
    }
    leaf src-type {
      type src-type-def;
      default "manual";
      description
        "The source type of the list.";
    }
    leaf description {
      type ntos-types:ntos-obj-description-type;
      default "";
    }
    leaf enabled {
      type boolean;
      default "true";
    }
  }

  grouping white-black-member-v6 {
    leaf object {
      type ipv6-address;
    }
    leaf way {
      type enumeration {
        enum dstip;
        enum srcip;
      }
    }
    leaf src-type {
      type src-type-def;
      default "manual";
      description
        "The source type of the list.";
    }
    leaf description {
      type ntos-types:ntos-ipv6-obj-description-type;
      default "";
    }
    leaf enabled {
      type boolean;
      default "true";
    }
  }

  grouping temp-blocktime {
    leaf minute {
      type uint32 {
        range '3..21600';
      }
    }
    leaf hour {
      type uint32 {
        range '1..360';
      }
    }
    leaf day {
      type uint32 {
        range '1..15';
      }
    }
  }

  grouping temp-black-member {
    leaf object {
      type ipv4-address;
    }
    leaf way {
      type enumeration {
        enum dstip;
        enum srcip;
      }
    }
    leaf src-type {
      type src-type-def;
      default "manual";
      description
        "The source type of the list.";
    }
    leaf timeout-minute {
      type uint32 {
        range '3..21600';
      }
    }
    leaf timeout-hour {
      type uint32 {
        range '1..360';
      }
    }
    leaf timeout-day {
      type uint32 {
        range '1..15';
      }
    }
    leaf description {
      type ntos-types:ntos-obj-description-type;
      default "";
    }
  }

  grouping temp-black-member-v6 {
    leaf object {
      type ipv6-address;
    }
    leaf way {
      type enumeration {
        enum dstip;
        enum srcip;
      }
    }
    leaf src-type {
      type src-type-def;
      default "manual";
      description
        "The source type of the list.";
    }
    leaf timeout-minute {
      type uint32 {
        range '3..21600';
      }
    }
    leaf timeout-hour {
      type uint32 {
        range '1..360';
      }
    }
    leaf timeout-day {
      type uint32 {
        range '1..15';
      }
    }
    leaf description {
      type ntos-types:ntos-ipv6-obj-description-type;
      default "";
    }
  }

  grouping mac-block-member {
    leaf mac {
      type string {
        pattern '([0-9a-f]{2}(:[0-9a-f]{2}){5})|([0-9a-f]{4}(:[0-9a-f]{4}){2})';
        ntos-ext:nc-cli-shortdesc "<mac-address>";
      }
      description
        "Mac address.";
    }
    leaf enabled {
      type boolean;
      default "true";
    }
    leaf type {
      type enumeration {
        enum permanent;
        enum temporary;
      }
      default "permanent";
    }
    leaf block-mode {
      type enumeration {
        enum native;
        enum identity;
      }
      default "native";
    }
    leaf source {
      type string {
        length "1..32";
      }
      default "manual";
      description
        "The source type of the mac.";
    }
    leaf notify {
      type boolean;
      default "false";
    }
    choice timeout {
      case minute {
        leaf timeout-minute {
          type uint32 {
            range '3..21600';
          }
        }
      }
      case hour {
        leaf timeout-hour {
          type uint32 {
            range '1..360';
          }
        }
      }
      case day {
        leaf timeout-day {
          type uint32 {
            range '1..15';
          }
        }
      }
    }
    leaf description {
      type ntos-types:ntos-obj-description-type;
      default "";
    }
    leaf detail {
      type string {
        length "0..255";
      }
    }
  }

  rpc show-security-defend-mac-block {
    description
      "Show mac block json format.";

    input {
      uses vrf;
      leaf mac {
        description
          "Mac address.";
        type string {
          pattern '([0-9a-fA-F]{2}(:[0-9a-fA-F]{2}){5})|([0-9a-fA-F]{4}(:[0-9a-fA-F]{4}){2})';
          ntos-ext:nc-cli-shortdesc "<mac-address>";
        }
      }
      leaf filter {
        description
          "The content of search.";

        type string;
      }

      leaf start {
        description
          "The index of page start.";

        type uint16;
      }

      leaf end {
        description
          "The index of page end.";

        type uint16;
        must "current() >= ../start" {
          error-message "The End value must be larger than the start value.";
        }
      }
    }

    output {
      leaf buffer {
        description
          "The command output buffer since last request.";

        type string;

        ntos-ext:nc-cli-stdout;
      }
    }

    ntos-ext:nc-cli-show "security-defend mac-block json";
    ntos-ext:nc-cli-hidden;
  }

  rpc show-security-defend-mac-block-config {
    description
      "Show mac block config.";

    input {
      uses vrf;
      leaf mac {
        description
          "Mac address.";
        type string {
          pattern '([0-9a-fA-F]{2}(:[0-9a-fA-F]{2}){5})|([0-9a-fA-F]{4}(:[0-9a-fA-F]{4}){2})';
          ntos-ext:nc-cli-shortdesc "<mac-address>";
        }
      }
      leaf filter {
        description
          "The content of search.";

        type string;
      }

      leaf start {
        description
          "The index of page start.";

        type uint16;
      }

      leaf end {
        description
          "The index of page end.";

        type uint16;
        must "current() >= ../start" {
          error-message "The End value must be larger than the start value.";
        }
      }
    }

    output {
      leaf buffer {
        description
          "The command output buffer since last request.";

        type string;

        ntos-ext:nc-cli-stdout;
      }
    }

    ntos-ext:nc-cli-show "security-defend mac-block";
  }

  rpc show-security-defend-blacklist {
    description
      "Show blacklist.";

    input {
      uses vrf;
      leaf filter {
        description
          "The content of search.";

        type string;
        default "";
      }

      leaf start {
        description
          "The index of page start.";

        type uint16;
      }

      leaf end {
        description
          "The index of page end.";

        type uint16;
        must "current() >= ../start" {
          error-message "The End value must be larger than the start value.";
        }
      }
    }

    output {
      leaf buffer {
        description
          "The command output buffer since last request.";

        type string;

        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }

    ntos-ext:nc-cli-show "security-defend blacklist";
  }

  rpc show-security-defend-ipv6-blacklist {
    description
      "Show IPv6 blacklist.";

    input {
      uses vrf;
      leaf filter {
        description
          "The content of search.";

        type string;
        default "";
      }

      leaf start {
        description
          "The index of page start.";

        type uint16;
      }

      leaf end {
        description
          "The index of page end.";

        type uint16;
        must "current() >= ../start" {
          error-message "The End value must be larger than the start value.";
        }
      }
    }

    output {
      leaf buffer {
        description
          "The command output buffer since last request.";

        type string;

        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }

    ntos-ext:nc-cli-show "security-defend ipv6-blacklist";
  }

  rpc show-security-defend-whitelist {
    description
      "Show whitelist.";

    input {
      uses vrf;
      leaf filter {
         description
          "The content of search.";

        type string;
        default "";
      }

      leaf start {
        description
          "The index of page start.";

        type uint16;
      }

      leaf end {
        description
          "The index of page end.";

        type uint16;
        must "current() >= ../start" {
          error-message "The End value must be larger than the start value.";
        }
      }
    }

    output {
      leaf buffer {
        description
          "The command output buffer since last request.";

        type string;

        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }

    ntos-ext:nc-cli-show "security-defend whitelist";
  }

  rpc show-security-defend-ipv6-whitelist {
    description
      "Show IPv6 whitelist.";

    input {
      uses vrf;
      leaf filter {
         description
          "The content of search.";

        type string;
        default "";
      }

      leaf start {
        description
          "The index of page start.";

        type uint16;
      }

      leaf end {
        description
          "The index of page end.";

        type uint16;
        must "current() >= ../start" {
          error-message "The End value must be larger than the start value.";
        }
      }
    }

    output {
      leaf buffer {
        description
          "The command output buffer since last request.";

        type string;

        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }

    ntos-ext:nc-cli-show "security-defend ipv6-whitelist";
  }

  rpc show-security-defend-zone {
    description
      "Show the security defend policy or policy-list.";

    input {
      uses vrf;
      leaf filter {
        description
          "The content of search.";

        type string;
        default "";
      }

      leaf start {
        description
          "The index of page start.";

        type uint16;
      }

      leaf end {
        description
          "The index of page end.";

        type uint16;
      }
    }

    output {
      leaf buffer {
        description
          "The command output buffer since last request.";

        type string;

        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "security-defend policy";
  }

  rpc show-security-defend-zone-detail {
    description
      "Show the details of the security defend policy.";

    input {
      uses vrf;
      leaf defend-zone {
        description
          "The name of defend zone.";

        type ntos-types:ntos-obj-name-type;
      }
    }

    output {
      leaf buffer {
        description
          "The command output buffer since last request.";

        type string;

        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "security-defend detail";
  }

  rpc show-security-dst-defend-zone-detail {
    description
      "Show the details of the security destination defend policy.";

    input {
      uses vrf;
      leaf dst-defend-zone {
        description
          "The name of destination defend zone.";

        type ntos-types:ntos-obj-name-type;
      }
    }

    output {
      leaf buffer {
        description
          "The command output buffer since last request.";

        type string;

        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "security-defend detail";
  }

  rpc get-security-defend-policy-name {
    description
      "Show the details of security defend policy.";

    input {
      uses vrf;
      leaf policy-id {
        description
          "The id of defend policy.";

        type string;
      }
    }

    output {
      leaf buffer {
        description
          "The command output buffer since last request.";

        type string;

        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
  }

  rpc security-defend-import-list {
    input {
      uses vrf;
      leaf list-type {
        description
          "List type.";

        type enumeration {
          enum blacklist;
          enum whitelist;
          enum ipv6-blacklist;
          enum ipv6-whitelist;
          enum mac-block;
        }
      }

      leaf file-path {
        description
          "CSV file compelete path.";

        type string {
          length "1..255";
        }
      }

      leaf session-id {
        description
          "Session id of task.";

        type string {
          length "32";
        }
      }

      leaf action {
        description
          "Operation of task.";

        type enumeration {
          enum start;
          enum continue;
        }
      }
    }

    output {
      leaf buffer {
        description
          "The command output buffer since last request.";

        type string;

        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
  }

  rpc security-defend-export-list {
    input {
      uses vrf;
      leaf list-type {
        description
          "List type.";

        type enumeration {
          enum blacklist;
          enum whitelist;
          enum ipv6-blacklist;
          enum ipv6-whitelist;
          enum mac-block;
        }
      }

      leaf file-path {
        description
          "CSV file compelete path.";

        type string {
          length "1..255";
        }
      }

      leaf session-id {
        description
          "Session id of task.";

        type string {
          length "32";
        }
      }
    }

    output {
      leaf buffer {
        description
          "The command output buffer since last request.";

        type string;

        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
  }

  rpc security-defend-result {
    description
      "Get the import/export result by session-id.";
    input {
      uses vrf;
      leaf session-id {
        description
          "Session id of task.";

        type string {
          length "32";
        }
      }
      leaf list-type {
        description
          "List type.";

        type enumeration {
          enum blacklist;
          enum whitelist;
          enum ipv6-blacklist;
          enum ipv6-whitelist;
          enum mac-block;
        }
        default "blacklist";
      }
    }

    output {
      leaf buffer {
        description
          "The command output buffer since last request.";

        type string;

        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
  }

  rpc security-defend-subcommand {
    input {
      leaf command {
        description
          "Subcommand.";

        type enumeration {
          enum global;
          enum all-config;
          enum srchost-table;
          enum dsthost-table;
          enum dynfilter-table;
          enum srcfilter-table;
          enum dstfilter-table;
          enum proto-status;
          enum blacklist;
          enum ipv6-blacklist;
          enum hw-blacklist;
          enum whitelist;
          enum ipv6-whitelist;
          enum traffic;
          enum scanning;
          enum mac-block;
        }

        ntos-ext:nc-cli-hidden;
        ntos-ext:nc-cli-no-name;
      }
    }

    output {
      leaf buffer {
        description
          "The command output buffer since last request.";

        type string;

        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }

    ntos-ext:nc-cli-hidden;
    ntos-ext:nc-cli-show "security-defend-subcommand";
  }

  rpc security-defend-del-temp-blacklist {
    input {
      list item {
        key "object way";
        ordered-by user;

        leaf object {
          type ipv4-address;
        }
        leaf way {
          type enumeration {
            enum dstip;
            enum srcip;
          }
        }
        leaf mdl {
          type enumeration {
            enum ips;
            enum web_sec;
            enum user_blist;
          }
        }
      }
    }

    output {
      leaf buffer {
        description
          "The command output buffer since last request.";

        type string;

        ntos-ext:nc-cli-stdout;
      }
    }

    ntos-ext:nc-cli-hidden;
  }

  rpc show-security-defend-basic-control {
    description
      "Show the flag of security defend basic protocol control.";

    input {
      uses vrf;
    }

    output {
      leaf buffer {
        description
          "The flag of basic protocol control by ddos policy.";

        type string;

        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }

    ntos-ext:nc-cli-show "security-defend basic-control";
  }

  rpc security-defend-del-ipv6-temp-blacklist {
    input {
      list item {
        key "object way";
        ordered-by user;

        leaf object {
          type ipv6-address;
        }
        leaf way {
          type enumeration {
            enum dstip;
            enum srcip;
          }
        }
        leaf mdl {
          type enumeration {
            enum ips;
            enum web_sec;
            enum user_blist;
          }
        }
      }
    }

    output {
      leaf buffer {
        description
          "The command output buffer since last request.";

        type string;

        ntos-ext:nc-cli-stdout;
      }
    }

    ntos-ext:nc-cli-hidden;
  }

  augment "/ntos:config/ntos:vrf" {
    container security-defend {
      description
        "The top-level grouping of security-defend configurations.";

      leaf basic-protocol-control-enabled {
        description
          "Enable or disable basic protocol control by ddos policy";

        type boolean;
        default "false";
      }

      list defend-zone {
        description
          "Security source defend zone configuration.";

        key "name";
        ordered-by user;

        uses security-defend-defend-zone;
      }

      list dst-defend-zone {
        description
          "Security destination defend zone configuration.";

        key "name";
        ordered-by user;

        uses security-defend-dst-defend-zone;
      }

      list blacklist {
        description
          "Security defend IPv4 blacklist configuration.";

        key "object way";
        ordered-by user;

        uses white-black-member;
      }

      list ipv6-blacklist {
        description
          "Security defend IPv6 blacklist configuration.";

        key "object way";
        ordered-by user;

        uses white-black-member-v6;
      }

      container temp-blacklist {
        description
          "Security defend temporary blacklist configuration.";

        list item {
        description
          "IPv4 temporary blacklist configuration.";

          key "object way";
          ordered-by user;

          uses temp-black-member;
        }

        list ipv6-item {
        description
          "IPv6 temporary blacklist configuration.";

          key "object way";
          ordered-by user;

          uses temp-black-member-v6;
        }

        container timeout {
          description
            "Global temporary blacklist block time.";

          uses temp-blocktime;
        }
      }

      list whitelist {
        description
          "Security defend IPv4 whitelist configuration.";

        key "object way";
        ordered-by user;

        uses white-black-member;
      }

      list ipv6-whitelist {
        description
          "Security defend IPv6 whitelist configuration.";

        key "object way";
        ordered-by user;

        uses white-black-member-v6;
      }

      container mac-block {
        list source-mac {
          description
            "Security defend mac block configuration.";

          key "mac";
          ordered-by user;

          uses mac-block-member;
        }
      }

      leaf enabled {
        description
          "Enable the security defend.";

        type boolean;
        default "false";
      }
    }
  }

  augment "/ntos:state/ntos:vrf" {
    container security-defend {
      description
        "The top-level grouping of security-defend configurations.";

      list defend-zone {
        description
          "Security source defend zone configuration.";

        key "name";

        uses security-defend-defend-zone;
      }

      list dst-defend-zone {
        description
          "Security destination defend zone configuration.";

        key "name";

        uses security-defend-dst-defend-zone;
      }

      list blacklist {
        description
          "Security defend blacklist configuration.";

        key "object way";

        uses white-black-member;
      }

      list whitelist {
        description
          "Security defend whitelist configuration.";

        key "object way";

        uses white-black-member;
      }

      list mac-block {
        description
          "Security defend mac block configuration.";

        key "mac";
        ordered-by user;

        uses mac-block-member;
      }

      leaf enabled {
        description
          "Enable the security defend.";

        type boolean;
      }

      container block-white-list-statistics {
        description
          "The statistics output for block/white list.";
        leaf statistics {
          type string {
            length "0..1024";
          }
          description
            "The capacity of the list.";
        }
      }
    }
  }
}