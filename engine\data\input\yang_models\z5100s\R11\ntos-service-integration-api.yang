module ntos-service-integration-api {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:service-integration-api";
  prefix ntos-service-integration-api;

  import ntos {
    prefix ntos;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-commands {
    prefix ntos-cmd;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-types {
    prefix ntos-types;
  }
  
  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "RUIJIE NTOS service integration API.";

  revision 2024-02-05 {
    description
      "First version of service integration API.";
    reference "";
  }
  
  identity service-integration-api {
    base ntos-types:SERVICE_LOG_ID;
    description
      "Service integration API.";
  }
  
  grouping interface-filter {
    description
      "interface filter param.";

    leaf type {
      type enumeration {
        enum physical {
          description
            "Physical interfaces.";
        }
        enum vlan {
          description
            "VLAN interfaces.";
        }
        enum bridge {
          description
            "Bridge interfaces.";
        }
        enum lag {
          description
            "LAG interfaces.";
        }
        enum vti {
          description
            "VTI interfaces.";
        }
        enum gre {
          description
            "GRE interfaces.";
        }
        enum erspan {
          description
            "ERSPAN interfaces.";
        }
        enum vswitch {
          description
            "Switch interfaces.";
        }
      }
    }

    leaf ifname {
      type union {
        type ntos-types:ifname;
      }
      description
        "Filter by inbound interface.";
      ntos-ext:nc-cli-completion-xpath
        "/ntos:state/ntos:vrf/ntos-interface:interface/*/*[local-name()='name']|
        /ntos:state/ntos:vrf/ntos-interface:interface/*[local-name()='physical']/*[local-name()='ipv4']/
        *[local-name()='pppoe']/*[local-name()='connection']/*[local-name()='tunnel-interface'] |
        /ntos:state/ntos:vrf/ntos-interface:interface/*[local-name()='vlan']/*[local-name()='ipv4']/
        *[local-name()='pppoe']/*[local-name()='connection']/*[local-name()='tunnel-interface']";
      ntos-ext:nc-cli-group "command";
    }
    leaf detail {
      type empty;
      description
        "Interface list include detail information.";
    }
  }

  grouping page-filter {
    description
      "page filter param.";

    leaf page-index {
      type uint32{
          range "1..10000000";
        }
      description
        "Page index.";
    }

    leaf page-size {
      type uint32{
          range "1..100";
        }
      description
        "Number entries of one page.";
    }
  }

  grouping search-filter {
    description
      "search filter param.";

    list exact-params {
      key field;
      leaf field {
        type string;
      }

      leaf exact-filter {
        type string;
      }
    }
  }

  rpc show-service-integration-interface-list {
    description
      "Show interface integration list information.";
    input {
      must 'count(page-size) + count(page-index) + count(type) = 3' {
          error-message "Must input page-index, page-size and type.";
      }
      uses interface-filter;
      uses page-filter;
      uses search-filter;
    }
    
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    
    ntos-ext:feature "service-integration-api";
    ntos-ext:nc-cli-show "service integration interface list";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
  }
  
  rpc show-service-integration-interface-detail {
    description
      "Show interface integration detail information.";
    input {
      must 'count(ifname) + count(type) = 2' {
          error-message "Must input interface type and name.";
      }
      uses interface-filter;
    }
    
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    
    ntos-ext:feature "service-integration-api";
    ntos-ext:nc-cli-show "service integration interface detail";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
  }
}
