module ntos-sim-security-policy {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:sim-security-policy";
  prefix ntos-sim-security-policy;

  import ntos {
    prefix ntos;
  }
  import ntos-security-policy {
    prefix ntos-security-policy;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ietf-netconf-acm {
    prefix nacm;
    reference
      "RFC 8341: Network Configuration Access Control Model";
  }
  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel: 4008-111-000
    E-mail: <EMAIL>";
  description
    "Ruijie NTOS simulative security policy module.";

  revision 2021-11-05 {
    description
      "create";
    reference "";
  }

  typedef switch-types {
    type union {
      type enumeration {
        enum on {
          description
            "The simulative security policy is working.";
        }
        enum off {
          description
            "The simulative security policy is not working.";
        }
      }
    }
    description
      "The status of the simulative security policy.";
  }

  typedef sim-status-types {
    type union {
      type enumeration {
        enum free {
          description
            "The simulative policy space is idle.";
        }
        enum copying-real-to-sim {
          description
            "Copy from real policy to simulative policy.";
        }
        enum copying-sim-to-real {
          description
            "Copy from simulative policy to real policy.";
        }
      }
    }
    description
      "Current operating status of the simulative security policy.";
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "Configuration of simulative security policy.";

    container sim-status {
      description
        "Configuration of simulative security policy status.";

      leaf switch {
        type switch-types;
      }

      leaf action {
        type sim-status-types;
      }

      leaf set-run-time {
        type uint32;
      }
    }

    container sim-security-policy {
      description
        "Configuration of simulative security policy.";

      list group {
        description
          "The group of simulative security policy.";
        key "name";

        ordered-by user;
        ntos-ext:nc-cli-one-liner;
        leaf name {
          description
            "The name of simulative security policy group.";
          type string;
          ntos-ext:nc-cli-no-name;
        }

        leaf rename {
          type ntos-types:ntos-obj-name-type;
          description
            "The new name of security policy group.";
        }
      }
      uses ntos-security-policy:policy-detail;
    }
  }

  rpc sim-policy-run {
    description
      "Set simulative security policy status.";
    input {
      leaf switch {
        type switch-types;
      }
      leaf clear-result {
        type empty;
      }
    }
    output {
      leaf info {
        description
          "The result message of this RPC execution.";
        type string;
      }
    }
    nacm:default-deny-all;
    ntos-ext:nc-cli-cmd "sim-security-policy";
    ntos-api:internal;
  }
}