module ntos-trusted-host {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:trusted-host";
  prefix ntos-trusted-host;

  import ntos {
    prefix ntos;
  }
  import ntos-system {
    prefix ntos-system;
  }
  import ntos-inet-types {
    prefix ntos-inet-types;
  }
  import ntos-auth {
    prefix ntos-auth;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-if-types {
    prefix ntos-if;
  }
  import ntos-extensions {
    prefix ntos-extensions;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS trusted host management.";

  revision 2023-12-18 {
    description
      "add mac and ip-mac.";
    reference "";
  }

  revision 2023-03-02 {
    description
      "add SERVICE_LOG_ID.";
    reference "";
  }

  revision 2023-01-03 {
    description
      "Initial version.";
    reference "";
  }

  identity trusted-host {
    base ntos-types:SERVICE_LOG_ID;
    description
      "Trusted-host service.";
  }

  grouping trusted-host-list {
    description
      "Trusted hosts configuration.";

    list host {
      key "username";
      description
        "List of trusted hosts.";

      leaf username {
        type leafref {
          path
            "/ntos:config/ntos-system:system/ntos-auth:auth/ntos-auth:user/ntos-auth:name";
          require-instance true;
        }
        description
          "Trusted host username.";
      }

      leaf-list ipv4 {
        type ntos-inet-types:ipv4-prefix;
        max-elements 10;
        description
          "Trusted host ipv4 address.";
      }

      leaf-list ipv6 {
        type ntos-inet-types:ipv6-prefix;
        max-elements 10;
        description
          "Trusted host ipv6 address.";
      }

      leaf-list mac {
        type ntos-if:mac-address {
            ntos-extensions:nc-cli-shortdesc "<mac-address>";
        }
        description
          "Trusted host mac address.";
      }

      list ip-mac {
        key "ip mac";
        description
          "Trusted host ip-mac address.";
        ntos-extensions:nc-cli-one-liner;

        leaf ip {
          type ntos-inet-types:ip-prefix;
          description
            "Enter IP address.";
        }

        leaf mac {
          type ntos-if:mac-address {
            ntos-extensions:nc-cli-shortdesc "<mac-address>";
          }
          description
            "Enter MAC address of the IP.";
        }
      }

      leaf enabled {
        type boolean;
        mandatory true;
        description
          "Enable trusted host authentication.";
      }
      ntos-extensions:data-not-sync;
    }
  }

  augment "/ntos:config/ntos-system:system" {
    description
      "Trusted-host configuration.";
      
    container trusted-host {
      description
        "Configuration data for trusted hosts.";
      uses trusted-host-list;
    }
  }

  augment "/ntos:state/ntos-system:system" {
    description
      "Trusted-host state.";

    container trusted-host {
      description
        "Configuration state data for trusted hosts.";
      uses trusted-host-list;
    }
  }
}