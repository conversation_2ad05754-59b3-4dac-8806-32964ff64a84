module ntos-user-experience-plan {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:user-experience-plan";
  prefix ntos-user-exp-plan;

  import ntos {
    prefix ntos;
  }
  import ntos-system {
    prefix ntos-system;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS user experience plan module.";

  revision 2023-04-07 {
    description
      "Initial version.";
    reference "";
  }

  grouping user-experience-plan {
    description
      "User experience plan.";

    leaf no-prompt {
      type boolean;
      default "false";
      description
        "If the user experience plan is not enabled whether to stop pop-up prompts";
    }
    leaf enabled {
      type boolean;
      default "false";
      description
        "Enable or disable the user experience plan";
    }
    container log-category {
      description
        "Log categories forwarded to the cloud platform.";
      leaf security-log {
        type boolean;
        description
          "Whether to forward security logs to the cloud platform.";
      }
      leaf device-log {
        type boolean;
        description
          "Whether to forward device logs to the cloud platform.";
      }
      leaf engine-log {
        type boolean;
        description
          "Whether to forward engine logs to the cloud platform.";
      }
      leaf pdns-log {
        type boolean;
        description
          "Whether to forward passive dns logs to the cloud platform.";
      }
      leaf diagnostic-log {
        type boolean;
        description
          "Whether to forward diagnostic logs to the cloud platform.";
      }
    }
  }

  augment "/ntos:config/ntos-system:system" {
    description
      "Configuration of User Experience Plan";
    container usr-exp-plan {
      description
        "Configuration of User Experience Plan";
      uses user-experience-plan;
    }
  }

  augment "/ntos:state/ntos-system:system" {
    description
      "The state of User Experience Plan";
    container usr-exp-plan {
      description
        "Configuration of User Experience Plan";
      uses user-experience-plan;
    }
  }

  rpc show-user-experience-plan-info {
    description
      "Show user experience plan information.";
    output {
      uses user-experience-plan;
    }
    ntos-ext:nc-cli-show "user-experience-plan info";
    ntos-api:internal;
  }

  rpc set-ux-no-prompt {
    description
      "If the user experience plan is not enabled,wether the web landing page should be reminded by pop-up window.";
    input {
      leaf no-prompt {
        type boolean;
        description
          "True indicates do not display pop-up window again,otherwise display.";
      }
    }
    ntos-ext:nc-cli-cmd "user-experience-plan set";
    ntos-ext:nc-cli-hidden;
    ntos-api:internal;
  }
}