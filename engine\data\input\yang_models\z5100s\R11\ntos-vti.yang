module ntos-vti {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:vti";
  prefix ntos-vti;

  import extra-conditions {
    prefix ext-cond;
  }
  import ntos {
    prefix ntos;
  }
  import ntos-extensions {
    prefix ntos-extensions;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-if-types {
    prefix ntos-if;
  }
  import ntos-ip {
    prefix ntos-ip;
  }
  import ntos-interface {
    prefix ntos-interface;
  }
  import ntos-qos {
    prefix ntos-qos;
  }
  import ntos-routing {
    prefix ntos-routing;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS VTI interfaces.";

  revision 2023-03-23 {
    description
      "Initial version.";
    reference "";
  }

  identity vti {
    base ntos-types:INTERFACE_TYPE;
    description
      "vti interface.";
  }

  feature supported {
    description
      "Support vti interface.";
  }

  grouping vti-config {
    description
      "Vti configuration container.";

    leaf local {
      type ntos-inet:ip-address;
      description
        "Local address is deprecated since revision 2024-01-10.";
      status deprecated;
    }

    leaf remote {
      type ntos-inet:ip-address;
      mandatory false;
      description
        "Remote address is deprecated since revision 2024-01-10.";
      status deprecated;
    }

    leaf link-vrf {
      type string;
      must '. != ../../../ntos:name' {
        error-message "link-vrf must reference another vrf.";
      }
      description
        "The link vrf name.";
      ntos-extensions:nc-cli-completion-xpath
        "/ntos:config/ntos:vrf/ntos:name";
    }

    leaf is-template {
      type boolean;
      default "false";
      description
        "Used to distinguish whether to deliver from the web page of the wizard configuration.";
    }
  }

  grouping vti-state {
    description
      "Vti state container.";

    leaf vti-key {
      type uint32 {
        range "1..4294967295";
      }
      mandatory false;
      description
        "The key of vti interface.";
    }

    uses vti-config;
  }

  augment "/ntos:config/ntos:vrf/ntos-interface:interface" {
    description
      "Network vti configuration.";

    list vti {
      if-feature supported;
      key "name";
      description
        "The list of vti interfaces on the device.";

      uses ntos-interface:nonphy-interface-config {
        refine enabled {
          default "false";
        }
        refine mtu {
          must 'current() >= 512 and current() <= 1600' {
            error-message "MTU must be >= 512 and <= 1600.";
          }
          description
            "Set the max transmission unit size in octets, range: 512..1600.";
        }
        refine promiscuous {
          config false;
        }
      }
      uses ntos-ip:ntos-ipv4-ptp-config;
      uses ntos-ip:ntos-ipv6-ptp-config {
        refine "ipv6/enabled" {
          default "true";
        }
      }
      uses ntos-routing:reverse-path;
      uses vti-config{
        refine local {
          must "not(. = '0.0.0.0')" {
            error-message "interface vti local cannot be 0.0.0.0.";
          }
        }
      }
    }
  }

  augment "/ntos:state/ntos:vrf/ntos-interface:interface" {
    description
      "Network vti operational state data.";

    list vti {
      if-feature supported;
      key "name";
      description
        "The list of vti interfaces on the device.";
      uses ntos-interface:interface-state;
      uses ntos-ip:ntos-ipv4-ptp-state;
      uses ntos-ip:ntos-ipv6-ptp-state;
      uses ntos-ip:ntos-network-stack-parameters;
      uses vti-state;
      uses ntos-if:interface-common-state;
      uses ntos-if:interface-counters-state;
      uses ntos-qos:logical-if-qos-state;
    }
  }

  rpc show-tunnel-interface-state {
    ntos-extensions:nc-cli-show "vti state";
    description
      "Show interface state.";
    input {
      leaf vrf {
        ntos-extensions:nc-cli-completion-xpath "/ntos:state/ntos:vrf/ntos:name";
        type string;
        default "main";
        description
          "VRF to look into.";
      }
      leaf start {
        type uint16 {
          range "1..65535";
        }
        description
          "Start interface number.";
      }
      leaf end {
        type uint16 {
          range "1..65535";
        }
        description
          "End interface number.";
      }
      leaf name {
        type string;
        description
          "Show interface by this name (exact query).";
      }
      leaf filter {
        type string;
        description
          "Show interface by this name (fuzzy query).";
      }
    }

    output {
      leaf interface-total {
        type uint16 {
          range "0..65535";
        }
        description
          "Interface total number.";
      }
      list interface {
        description
          "Output for interface list";
        uses ntos-interface:interface-state;
        uses ntos-if:interface-common-state;
        uses ntos-ip:ntos-ipv4-state;
        uses ntos-ip:ntos-ipv6-state;

        leaf local {
          type ntos-inet:ip-address;
          description
            "Local address.";
        }

        leaf remote {
          type ntos-inet:ip-address;
          description
            "Remote address.";
        }

        leaf link-vrf {
          type string;
          description
            "The link vrf name.";
          ntos-extensions:nc-cli-completion-xpath
            "/ntos:config/ntos:vrf/ntos:name";
        }

        leaf is-template {
          type boolean;
          default "false";
          description
            "Used to distinguish whether to deliver from the web page of the wizard configuration.";
        }

        leaf ipsec-profile {
          type string;
          description
            "Used to indicate the ipsec profile referenced by VTI interface.";
        }

        container access-control {

          leaf https {
            type boolean;
            default "false";
          }

          leaf ping {
            type boolean;
            default "false";
          }

          leaf ssh {
            type boolean;
            default "false";
          }
        }
      }
    }
  }

}
