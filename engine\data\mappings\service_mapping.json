{"ping": {"protocol": "ICMP", "type": 8, "code": 0, "description": "PING服务", "aliases": ["echo-request"], "ntos_service": "ping"}, "ftp": {"protocol": "TCP", "ports": "21", "description": "文件传输协议", "aliases": ["file-transfer", "file_transfer", "FTP"], "ntos_service": "ftp"}, "ssh": {"protocol": "TCP", "ports": "22", "description": "安全Shell协议", "aliases": ["secure-shell", "secure_shell", "SSH"], "ntos_service": "ssh"}, "telnet": {"protocol": "TCP", "ports": "23", "description": "Telnet远程登录服务", "aliases": ["terminal", "TELNET"], "ntos_service": "telnet"}, "smtp": {"protocol": "TCP", "ports": "25", "description": "简单邮件传输协议", "aliases": ["mail", "email", "SMTP"], "ntos_service": "smtp"}, "dns-t": {"protocol": "TCP", "ports": "53", "description": "域名系统(TCP)", "aliases": ["domain-tcp", "dns-tcp", "domain_tcp"], "ntos_service": "dns-t"}, "dns-u": {"protocol": "UDP", "ports": "53", "description": "域名系统(UDP)", "aliases": ["domain-udp", "dns-udp", "domain_udp"], "ntos_service": "dns-u"}, "dns": {"protocol": "TCP_UDP", "ports": "53", "description": "域名系统(通用)", "aliases": ["domain", "DNS"], "ntos_service": "dns"}, "sql_net": {"protocol": "UDP", "ports": "66", "description": "Oracle SQL*NET", "aliases": ["sqlnet", "oracle-sql"], "ntos_service": "sql_net"}, "tftp": {"protocol": "UDP", "ports": "69", "description": "简单文件传输协议", "aliases": ["trivial-ftp"], "ntos_service": "tftp"}, "http": {"protocol": "TCP", "ports": "80", "description": "超文本传输协议", "aliases": ["www", "web", "HTTP"], "ntos_service": "http"}, "pop3": {"protocol": "TCP", "ports": "110", "description": "邮局协议版本3", "aliases": ["POP3", "pop"], "ntos_service": "pop3"}, "ntp_t": {"protocol": "TCP", "ports": "123", "ntos_service": "ntp_t"}, "ntp_u": {"protocol": "UDP", "ports": "123", "ntos_service": "ntp_u"}, "ntp": {"protocol": "UDP", "ports": "123", "description": "网络时间协议", "aliases": ["time", "NTP"], "ntos_service": "ntp"}, "snmp": {"protocol": "UDP", "ports": "161", "description": "简单网络管理协议", "aliases": ["SNMP", "snmp-agent"], "ntos_service": "snmp"}, "snmp_trap": {"protocol": "UDP", "ports": "162", "ntos_service": "snmp_trap"}, "bgp": {"protocol": "TCP", "ports": "179", "ntos_service": "bgp"}, "irc": {"protocol": "TCP", "ports": "194", "ntos_service": "irc"}, "netbios-ns": {"protocol": "UDP", "ports": "137", "ntos_service": "netbios-ns"}, "ldap": {"protocol": "TCP", "ports": "389", "ntos_service": "ldap"}, "https": {"protocol": "TCP", "ports": "443", "description": "安全超文本传输协议", "aliases": ["ssl", "tls", "HTTPS"], "ntos_service": "https"}, "rip": {"protocol": "UDP", "ports": "520", "ntos_service": "rip"}, "rtsp": {"protocol": "TCP", "ports": "554,7070,8554", "ntos_service": "rtsp"}, "rtsp-udp": {"protocol": "UDP", "ports": "554", "ntos_service": "rtsp"}, "ms-sql-s": {"protocol": "TCP", "ports": "1433", "ntos_service": "ms-sql-s"}, "ms-sql-m": {"protocol": "TCP", "ports": "1434", "ntos_service": "ms-sql-m"}, "ms-sql-r": {"protocol": "UDP", "ports": "1434", "ntos_service": "ms-sql-r"}, "nfs": {"protocol": "TCP", "ports": "2049,111", "ntos_service": "nfs"}, "nfs-udp": {"protocol": "UDP", "ports": "2049,111", "ntos_service": "nfs"}, "netmeeting": {"protocol": "TCP", "ports": "1503,1720,1731", "ntos_service": "netmeeting"}, "l2tp": {"protocol": "TCP", "ports": "1701", "ntos_service": "l2tp"}, "l2tp-udp": {"protocol": "UDP", "ports": "1701", "ntos_service": "l2tp"}, "pptp": {"protocol": "TCP", "ports": "1723", "ntos_service": "pptp"}, "mysql": {"protocol": "TCP", "ports": "3306", "ntos_service": "mysql"}, "squid": {"protocol": "TCP", "ports": "3128", "ntos_service": "squid"}, "cluster": {"protocol": "UDP", "ports": "3343", "ntos_service": "cluster"}, "sip_t": {"protocol": "TCP", "ports": "5060", "ntos_service": "sip_t"}, "sip_u": {"protocol": "UDP", "ports": "5060", "ntos_service": "sip_u"}, "rdp": {"protocol": "TCP", "ports": "3389", "ntos_service": "rdp"}, "ping6": {"protocol": "ICMPV6", "type": 128, "code": 0, "ntos_service": "ping6"}, "icmpv6": {"protocol": "ICMPV6", "type_range": "0-255", "code_range": "0-255", "ntos_service": "icmpv6"}, "h323": {"protocol": "UDP", "ports": "1719", "ntos_service": "h323"}, "h225": {"protocol": "TCP", "ports": "1720", "ntos_service": "h225"}, "ah": {"protocol": "IP", "protocol_number": 51, "ntos_service": "ah"}, "dhcp": {"protocol": "UDP", "ports": "67,68", "description": "动态主机配置协议", "aliases": ["DHCP", "dhcp-server", "dhcp-client"], "ntos_service": "dhcp"}, "dhcp6": {"protocol": "UDP", "ports": "546,547", "ntos_service": "dhcp6"}, "esp": {"protocol": "IP", "protocol_number": 50, "ntos_service": "esp"}, "gre": {"protocol": "IP", "protocol_number": 47, "ntos_service": "gre"}, "icmp": {"protocol": "ICMP", "type_range": "0-255", "code_range": "0-255", "ntos_service": "icmp"}, "ike": {"protocol": "UDP", "ports": "500,4500", "ntos_service": "ike"}, "imap": {"protocol": "TCP", "ports": "143", "description": "Internet邮件访问协议", "aliases": ["IMAP", "imap4"], "ntos_service": "imap"}, "imaps": {"protocol": "TCP", "ports": "993", "ntos_service": "imaps"}, "kerberos": {"protocol": "TCP", "ports": "88", "ntos_service": "kerber<PERSON>"}, "kerberos-udp": {"protocol": "UDP", "ports": "88", "ntos_service": "kerber<PERSON>"}, "mms": {"protocol": "TCP", "ports": "1755", "ntos_service": "mms"}, "nntp": {"protocol": "TCP", "ports": "119", "ntos_service": "nntp"}, "ospf": {"protocol": "IP", "protocol_number": 89, "ntos_service": "ospf"}, "pop3s": {"protocol": "TCP", "ports": "995", "ntos_service": "pop3s"}, "radius": {"protocol": "UDP", "ports": "1812,1813", "ntos_service": "radius"}, "radius_old": {"protocol": "UDP", "ports": "1645,1646", "ntos_service": "radius_old"}, "radius_coa": {"protocol": "UDP", "ports": "3799", "ntos_service": "radius_coa"}, "rlogin": {"protocol": "TCP", "ports": "513", "ntos_service": "rlogin"}, "samba": {"protocol": "TCP", "ports": "139", "ntos_service": "samba"}, "sip-msnmessenger": {"protocol": "TCP", "ports": "1863", "ntos_service": "sip-msnmessenger"}, "smtps": {"protocol": "TCP", "ports": "465", "ntos_service": "smtps"}, "socks": {"protocol": "TCP", "ports": "1080", "ntos_service": "socks"}, "socks-udp": {"protocol": "UDP", "ports": "1080", "ntos_service": "socks"}, "syslog": {"protocol": "UDP", "ports": "514", "ntos_service": "syslog"}, "tcp-all": {"protocol": "TCP", "ports": "0-65535", "ntos_service": "tcp"}, "udp-all": {"protocol": "UDP", "ports": "0-65535", "ntos_service": "udp"}, "all": {"protocol": "ANY", "ports": "any", "description": "所有服务", "aliases": ["ALL", "any", "ANY"], "ntos_service": "any"}, "vnc": {"protocol": "TCP", "ports": "5900", "ntos_service": "vnc"}, "wins": {"protocol": "TCP", "ports": "1512", "ntos_service": "wins"}, "wins-udp": {"protocol": "UDP", "ports": "1512", "ntos_service": "wins"}, "other-ip": {"protocol": "IP", "protocol_number_range": "2-5,7-16,18-57,59-255", "ntos_service": "其他"}, "local_service": {"protocol": "TCP", "ports": "22,830,443,80,20099", "ntos_service": "本地服务"}, "local_service_udp": {"protocol": "UDP", "ports": "53,67,68", "ntos_service": "本地服务"}, "local_https": {"protocol": "TCP", "ports": "443", "ntos_service": "local_https"}, "local_ssh": {"protocol": "TCP", "ports": "22", "ntos_service": "local_ssh"}, "local_snmp": {"protocol": "UDP", "ports": "161,162", "ntos_service": "local_snmp"}, "sslvpn": {"protocol": "TCP", "ports": "8443", "ntos_service": "sslvpn"}, "sslvpn-udp": {"protocol": "UDP", "ports": "8443", "ntos_service": "sslvpn"}, "ha": {"protocol": "TCP", "ports": "111,2049,6488,50000-50002", "ntos_service": "ha"}, "ha-udp": {"protocol": "UDP", "ports": "16200-16299", "ntos_service": "ha"}, "local_portal": {"protocol": "TCP", "ports": "8081", "ntos_service": "local_portal"}, "local_wifidog": {"protocol": "TCP", "ports": "2060", "ntos_service": "local_wifidog"}, "n2n": {"protocol": "UDP", "ports": "51211", "ntos_service": "n2n"}, "nat-detect": {"protocol": "UDP", "ports": "53478,53479", "ntos_service": "nat-detect"}, "MS-SQL": {"protocol": "TCP", "ports": "1433", "description": "Microsoft SQL Server", "aliases": ["mssql", "sql-server", "sqlserver"], "ntos_service": "ms-sql-s"}, "Oracle-DB": {"protocol": "TCP", "ports": "1521", "description": "Oracle Database", "aliases": ["oracle", "oracle-database", "oracledb"]}, "WebLogic": {"protocol": "TCP", "ports": "7001", "description": "Oracle WebLogic Server", "aliases": ["weblogic-server"]}, "WebSphere": {"protocol": "TCP", "ports": "9060,9080", "description": "IBM WebSphere Application Server", "aliases": ["websphere-server"]}, "Tomcat": {"protocol": "TCP", "ports": "8080", "description": "Apache Tomcat Server", "aliases": ["tomcat-server", "apache-tomcat"]}, "MongoDB": {"protocol": "TCP", "ports": "27017", "description": "MongoDB Database", "aliases": ["mongo", "mongo-db"]}, "Redis": {"protocol": "TCP", "ports": "6379", "description": "Redis Database", "aliases": ["redis-db", "redis-server"]}, "Elasticsearch": {"protocol": "TCP", "ports": "9200,9300", "description": "Elasticsearch", "aliases": ["elastic", "es"]}, "Kafka": {"protocol": "TCP", "ports": "9092", "description": "Apache Kafka", "aliases": ["kafka-broker"]}, "Zookeeper": {"protocol": "TCP", "ports": "2181", "description": "Apache Zookeeper", "aliases": ["zk"]}, "Jenkins": {"protocol": "TCP", "ports": "8080", "description": "Jenkins CI/CD", "aliases": ["<PERSON><PERSON><PERSON>-server", "ci-cd"]}, "Docker": {"protocol": "TCP", "ports": "2375,2376", "description": "Docker API", "aliases": ["docker-api", "docker-daemon"]}, "Kubernetes-API": {"protocol": "TCP", "ports": "6443", "description": "Kubernetes API Server", "aliases": ["k8s-api", "kube-api"]}, "Prometheus": {"protocol": "TCP", "ports": "9090", "description": "Prometheus Monitoring", "aliases": ["prometheus-server"]}, "Grafana": {"protocol": "TCP", "ports": "3000", "description": "Grafana Dashboard", "aliases": ["grafana-server"]}}