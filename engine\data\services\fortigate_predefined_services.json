{"ALL": {"protocol": "IP", "description": "所有协议和端口", "category": "General"}, "ALL_TCP": {"protocol": "TCP", "ports": "1-65535", "description": "所有TCP端口", "category": "General"}, "ALL_UDP": {"protocol": "UDP", "ports": "1-65535", "description": "所有UDP端口", "category": "General"}, "ALL_ICMP": {"protocol": "ICMP", "description": "所有ICMP类型", "category": "General"}, "HTTP": {"protocol": "TCP", "ports": "80", "description": "超文本传输协议", "category": "Web Access"}, "HTTPS": {"protocol": "TCP", "ports": "443", "description": "安全超文本传输协议", "category": "Web Access"}, "SSH": {"protocol": "TCP", "ports": "22", "description": "安全Shell协议", "category": "Remote Access"}, "TELNET": {"protocol": "TCP", "ports": "23", "description": "Telnet远程登录", "category": "Remote Access"}, "FTP": {"protocol": "TCP", "ports": "21", "description": "文件传输协议", "category": "File Access"}, "FTP_GET": {"protocol": "TCP", "ports": "21", "description": "FTP下载", "category": "File Access"}, "FTP_PUT": {"protocol": "TCP", "ports": "21", "description": "FTP上传", "category": "File Access"}, "SMTP": {"protocol": "TCP", "ports": "25", "description": "简单邮件传输协议", "category": "Email"}, "SMTPS": {"protocol": "TCP", "ports": "465", "description": "安全SMTP", "category": "Email"}, "POP3": {"protocol": "TCP", "ports": "110", "description": "邮局协议版本3", "category": "Email"}, "POP3S": {"protocol": "TCP", "ports": "995", "description": "安全POP3", "category": "Email"}, "IMAP": {"protocol": "TCP", "ports": "143", "description": "互联网消息访问协议", "category": "Email"}, "IMAPS": {"protocol": "TCP", "ports": "993", "description": "安全IMAP", "category": "Email"}, "DNS": {"protocol": "TCP,UDP", "ports": "53", "description": "域名系统", "category": "Network Services"}, "DHCP": {"protocol": "UDP", "ports": "67-68", "description": "动态主机配置协议", "category": "Network Services"}, "DHCP6": {"protocol": "UDP", "ports": "546-547", "description": "DHCPv6", "category": "Network Services"}, "NTP": {"protocol": "TCP,UDP", "ports": "123", "description": "网络时间协议", "category": "Network Services"}, "SNMP": {"protocol": "TCP,UDP", "ports": "161-162", "description": "简单网络管理协议", "category": "Network Services"}, "SYSLOG": {"protocol": "UDP", "ports": "514", "description": "系统日志协议", "category": "Network Services"}, "TFTP": {"protocol": "UDP", "ports": "69", "description": "简单文件传输协议", "category": "File Access"}, "SAMBA": {"protocol": "TCP", "ports": "139", "description": "NetBIOS会话服务", "category": "File Access"}, "SMB": {"protocol": "TCP", "ports": "445", "description": "服务器消息块协议", "category": "File Access"}, "NFS": {"protocol": "TCP,UDP", "ports": "111,2049", "description": "网络文件系统", "category": "File Access"}, "MYSQL": {"protocol": "TCP", "ports": "3306", "description": "MySQL数据库", "category": "Database"}, "MS-SQL": {"protocol": "TCP", "ports": "1433,1434", "description": "Microsoft SQL Server", "category": "Database"}, "RDP": {"protocol": "TCP", "ports": "3389", "description": "远程桌面协议", "category": "Remote Access"}, "VNC": {"protocol": "TCP", "ports": "5900", "description": "虚拟网络计算", "category": "Remote Access"}, "LDAP": {"protocol": "TCP", "ports": "389", "description": "轻量级目录访问协议", "category": "Authentication"}, "LDAP_UDP": {"protocol": "UDP", "ports": "389", "description": "LDAP over UDP", "category": "Authentication"}, "KERBEROS": {"protocol": "TCP,UDP", "ports": "88,464", "description": "Kerberos认证协议", "category": "Authentication"}, "RADIUS": {"protocol": "UDP", "ports": "1812,1813", "description": "远程认证拨入用户服务", "category": "Authentication"}, "RADIUS-OLD": {"protocol": "UDP", "ports": "1645,1646", "description": "旧版RADIUS", "category": "Authentication"}, "PING": {"protocol": "ICMP", "icmp_type": "8", "description": "ICMP回显请求", "category": "Network Services"}, "PING6": {"protocol": "ICMP6", "icmp_type": "128", "description": "ICMPv6回显请求", "category": "Network Services"}, "GRE": {"protocol": "IP", "protocol_number": "47", "description": "通用路由封装", "category": "Tunneling"}, "ESP": {"protocol": "IP", "protocol_number": "50", "description": "封装安全载荷", "category": "Tunneling"}, "AH": {"protocol": "IP", "protocol_number": "51", "description": "认证头", "category": "Tunneling"}, "IKE": {"protocol": "UDP", "ports": "500,4500", "description": "互联网密钥交换", "category": "Tunneling"}, "L2TP": {"protocol": "TCP,UDP", "ports": "1701", "description": "第二层隧道协议", "category": "Tunneling"}, "PPTP": {"protocol": "TCP", "ports": "1723", "description": "点对点隧道协议", "category": "Tunneling"}, "SIP": {"protocol": "TCP,UDP", "ports": "5060", "description": "会话初始协议", "category": "VoIP, Messaging & Other Applications"}, "H323": {"protocol": "TCP,UDP", "ports": "1720,1503,1719", "description": "H.323视频会议", "category": "VoIP, Messaging & Other Applications"}, "RTSP": {"protocol": "TCP,UDP", "ports": "554,7070,8554", "description": "实时流协议", "category": "VoIP, Messaging & Other Applications"}, "BGP": {"protocol": "TCP", "ports": "179", "description": "边界网关协议", "category": "Network Services"}, "RIP": {"protocol": "UDP", "ports": "520", "description": "路由信息协议", "category": "Network Services"}, "OSPF": {"protocol": "IP", "protocol_number": "89", "description": "开放最短路径优先", "category": "Network Services"}, "WINS": {"protocol": "TCP,UDP", "ports": "1512", "description": "Windows互联网名称服务", "category": "Remote Access"}, "SQUID": {"protocol": "TCP", "ports": "3128", "description": "Squid代理服务器", "category": "Tunneling"}, "SOCKS": {"protocol": "TCP,UDP", "ports": "1080", "description": "SOCKS代理协议", "category": "Tunneling"}}