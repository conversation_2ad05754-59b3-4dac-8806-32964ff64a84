{"ping": {"protocol": "ICMP", "type": 8, "code": 0, "description": "service.builtin.ping.description"}, "ftp": {"protocol": "TCP", "ports": "21", "description": "service.builtin.ftp.description"}, "ssh": {"protocol": "TCP", "ports": "22", "description": "service.builtin.ssh.description"}, "telnet": {"protocol": "TCP", "ports": "23", "description": "service.builtin.telnet.description"}, "smtp": {"protocol": "TCP", "ports": "25", "description": "service.builtin.smtp.description"}, "dns-t": {"protocol": "TCP", "ports": "53", "description": "service.builtin.dns_t.description"}, "dns-u": {"protocol": "UDP", "ports": "53", "description": "service.builtin.dns_u.description"}, "sql_net": {"protocol": "UDP", "ports": "66", "description": "service.builtin.sql_net.description"}, "tftp": {"protocol": "UDP", "ports": "69", "description": "service.builtin.tftp.description"}, "http": {"protocol": "TCP", "ports": "80", "description": "service.builtin.http.description"}, "pop3": {"protocol": "TCP", "ports": "110", "description": "service.builtin.pop3.description"}, "ntp_t": {"protocol": "TCP", "ports": "123", "description": "service.builtin.ntp_t.description"}, "ntp_u": {"protocol": "UDP", "ports": "123", "description": "service.builtin.ntp_u.description"}, "snmp": {"protocol": "UDP", "ports": "161", "description": "service.builtin.snmp.description"}, "snmp_trap": {"protocol": "UDP", "ports": "162", "description": "service.builtin.snmp_trap.description"}, "bgp": {"protocol": "TCP", "ports": "179", "description": "service.builtin.bgp.description"}, "irc": {"protocol": "TCP", "ports": "194", "description": "service.builtin.irc.description"}, "netbios-ns": {"protocol": "UDP", "ports": "137", "description": "service.builtin.netbios_ns.description"}, "ldap": {"protocol": "TCP", "ports": "389", "description": "service.builtin.ldap.description"}, "https": {"protocol": "TCP", "ports": "443", "description": "service.builtin.https.description"}, "rip": {"protocol": "UDP", "ports": "520", "description": "service.builtin.rip.description"}, "rtsp": {"protocol": "TCP", "ports": "554,7070,8554", "description": "service.builtin.rtsp.description"}, "rtsp-udp": {"protocol": "UDP", "ports": "554", "description": "service.builtin.rtsp_udp.description"}, "ms-sql-s": {"protocol": "TCP", "ports": "1433", "description": "service.builtin.ms_sql_s.description"}, "ms-sql-m": {"protocol": "TCP", "ports": "1434", "description": "service.builtin.ms_sql_m.description"}, "ms-sql-r": {"protocol": "UDP", "ports": "1434", "description": "service.builtin.ms_sql_r.description"}, "nfs": {"protocol": "TCP", "ports": "2049,111", "description": "service.builtin.nfs.description"}, "nfs-udp": {"protocol": "UDP", "ports": "2049,111", "description": "service.builtin.nfs_udp.description"}, "netmeeting": {"protocol": "TCP", "ports": "1503,1720,1731", "description": "service.builtin.netmeeting.description"}, "l2tp": {"protocol": "TCP", "ports": "1701", "description": "service.builtin.l2tp.description"}, "l2tp-udp": {"protocol": "UDP", "ports": "1701", "description": "service.builtin.l2tp_udp.description"}, "pptp": {"protocol": "TCP", "ports": "1723", "description": "service.builtin.pptp.description"}, "mysql": {"protocol": "TCP", "ports": "3306", "description": "service.builtin.mysql.description"}, "squid": {"protocol": "TCP", "ports": "3128", "description": "service.builtin.squid.description"}, "cluster": {"protocol": "UDP", "ports": "3343", "description": "service.builtin.cluster.description"}, "sip_t": {"protocol": "TCP", "ports": "5060", "description": "service.builtin.sip_t.description"}, "sip_u": {"protocol": "UDP", "ports": "5060", "description": "service.builtin.sip_u.description"}, "rdp": {"protocol": "TCP", "ports": "3389", "description": "service.builtin.rdp.description"}, "ping6": {"protocol": "ICMPV6", "type": 128, "code": 0, "description": "service.builtin.ping6.description"}, "icmpv6": {"protocol": "ICMPV6", "type_range": "0-255", "code_range": "0-255", "description": "service.builtin.icmpv6.description"}, "h323": {"protocol": "UDP", "ports": "1719", "description": "service.builtin.h323.description"}, "h225": {"protocol": "TCP", "ports": "1720", "description": "service.builtin.h225.description"}, "ah": {"protocol": "IP", "protocol_number": 51, "description": "service.builtin.ah.description"}, "dhcp": {"protocol": "UDP", "ports": "67,68", "description": "service.builtin.dhcp.description"}, "dhcp6": {"protocol": "UDP", "ports": "546,547", "description": "service.builtin.dhcp6.description"}, "esp": {"protocol": "IP", "protocol_number": 50, "description": "service.builtin.esp.description"}, "gre": {"protocol": "IP", "protocol_number": 47, "description": "service.builtin.gre.description"}, "icmp": {"protocol": "ICMP", "type_range": "0-255", "code_range": "0-255", "description": "service.builtin.icmp.description"}, "ike": {"protocol": "UDP", "ports": "500,4500", "description": "service.builtin.ike.description"}, "imap": {"protocol": "TCP", "ports": "143", "description": "service.builtin.imap.description"}, "imaps": {"protocol": "TCP", "ports": "993", "description": "service.builtin.imaps.description"}, "kerberos": {"protocol": "TCP", "ports": "88", "description": "service.builtin.kerberos.description"}, "kerberos-udp": {"protocol": "UDP", "ports": "88", "description": "service.builtin.kerberos_udp.description"}, "mms": {"protocol": "TCP", "ports": "1755", "description": "service.builtin.mms.description"}, "nntp": {"protocol": "TCP", "ports": "119", "description": "service.builtin.nntp.description"}, "ospf": {"protocol": "IP", "protocol_number": 89, "description": "service.builtin.ospf.description"}, "pop3s": {"protocol": "TCP", "ports": "995", "description": "service.builtin.pop3s.description"}, "radius": {"protocol": "UDP", "ports": "1812,1813", "description": "service.builtin.radius.description"}, "radius_old": {"protocol": "UDP", "ports": "1645,1646", "description": "service.builtin.radius_old.description"}, "radius_coa": {"protocol": "UDP", "ports": "3799", "description": "service.builtin.radius_coa.description"}, "rlogin": {"protocol": "TCP", "ports": "513", "description": "service.builtin.rlogin.description"}, "samba": {"protocol": "TCP", "ports": "139", "description": "service.builtin.samba.description"}, "sip-msnmessenger": {"protocol": "TCP", "ports": "1863", "description": "service.builtin.sip_msnmessenger.description"}, "smtps": {"protocol": "TCP", "ports": "465", "description": "service.builtin.smtps.description"}, "socks": {"protocol": "TCP", "ports": "1080", "description": "service.builtin.socks.description"}, "socks-udp": {"protocol": "UDP", "ports": "1080", "description": "service.builtin.socks_udp.description"}, "syslog": {"protocol": "UDP", "ports": "514", "description": "service.builtin.syslog.description"}, "tcp": {"protocol": "TCP", "ports": "0-65535", "description": "service.builtin.tcp.description"}, "udp": {"protocol": "UDP", "ports": "0-65535", "description": "service.builtin.udp.description"}, "vnc": {"protocol": "TCP", "ports": "5900", "description": "service.builtin.vnc.description"}, "wins": {"protocol": "TCP", "ports": "1512", "description": "service.builtin.wins.description"}, "wins-udp": {"protocol": "UDP", "ports": "1512", "description": "service.builtin.wins_udp.description"}, "其他": {"protocol": "IP", "protocol_number_range": "2-5,7-16,18-57,59-255", "description": "service.builtin.other.description"}, "本地服务": {"protocol": "TCP", "ports": "22,830,443,80,20099", "description": "service.builtin.local_service.description"}, "本地服务-udp": {"protocol": "UDP", "ports": "53,67,68", "description": "service.builtin.local_service_udp.description"}, "local_https": {"protocol": "TCP", "ports": "443", "description": "service.builtin.local_https.description"}, "local_ssh": {"protocol": "TCP", "ports": "22", "description": "service.builtin.local_ssh.description"}, "local_snmp": {"protocol": "UDP", "ports": "161,162", "description": "service.builtin.local_snmp.description"}, "sslvpn": {"protocol": "TCP", "ports": "8443", "description": "service.builtin.sslvpn.description"}, "sslvpn-udp": {"protocol": "UDP", "ports": "8443", "description": "service.builtin.sslvpn_udp.description"}, "ha": {"protocol": "TCP", "ports": "111,2049,6488,50000-50002", "description": "service.builtin.ha.description"}, "ha-udp": {"protocol": "UDP", "ports": "16200-16299", "description": "service.builtin.ha_udp.description"}}