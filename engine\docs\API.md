# Python转换引擎 API 文档

## 概述

本文档描述了Python转换引擎v2.0的主要API接口。新架构提供了清晰的分层API，支持多种使用场景。

## 快速开始

### 基本转换示例

```python
from engine.application.conversion_service import ConversionService

# 创建转换服务
service = ConversionService()

# 执行转换
result = service.convert(
    cli_file="fortigate_config.conf",
    vendor="fortigate",
    model="z5100s",
    version="R10P2",
    output_file="output.xml"
)

# 检查结果
if result['success']:
    print(f"转换成功: {result['output_file']}")
    print(f"转换方法: {result['conversion_method']}")
    if 'performance_metrics' in result:
        perf = result['performance_metrics']
        print(f"性能: {perf['duration']:.3f}秒, {perf['memory_used_mb']:.2f}MB")
else:
    print(f"转换失败: {result['error']}")
```

## 应用服务层 API

### ConversionService

主要的配置转换服务接口。

#### 构造函数

```python
ConversionService(config_manager: ConfigManager = None)
```

**参数**:
- `config_manager`: 配置管理器实例，可选

#### convert() 方法

```python
def convert(self, **kwargs) -> Dict[str, Any]
```

**参数**:
- `cli_file` (str): 输入配置文件路径
- `vendor` (str): 厂商名称 ("fortigate")
- `model` (str): 设备型号 ("z5100s")
- `version` (str): 版本号 ("R10P2", "R11")
- `output_file` (str, 可选): 输出文件路径
- `verbose` (bool, 可选): 详细输出模式，默认False
- `interface_mapping_file` (str, 可选): 接口映射文件路径

**返回值**:
```python
{
    'success': bool,                    # 转换是否成功
    'workflow_version': str,            # 工作流版本
    'conversion_method': str,           # 转换方法 ('pipeline' 或 'legacy')
    'output_file': str,                 # 输出文件路径
    'performance_metrics': {            # 性能指标 (可选)
        'duration': float,              # 执行时间(秒)
        'memory_used_mb': float,        # 内存使用(MB)
        'cpu_percent': float            # CPU使用率(%)
    },
    'conversion_stats': {               # 转换统计 (可选)
        'policies_converted': int,      # 转换的策略数
        'nat_rules_generated': int,     # 生成的NAT规则数
        'warnings': List[str]           # 警告信息
    },
    'pipeline_info': {                  # 管道信息 (pipeline方法)
        'stages_executed': List[str],   # 执行的阶段
        'total_stages': int             # 总阶段数
    },
    'yang_validation': {                # YANG验证结果 (可选)
        'performed': bool,              # 是否执行验证
        'passed': bool,                 # 验证是否通过
        'errors': List[str]             # 验证错误
    },
    'error': str                        # 错误信息 (失败时)
}
```

### ValidationService

配置验证服务接口。

#### 构造函数

```python
ValidationService(config_manager: ConfigManager)
```

#### validate_config() 方法

```python
def validate_config(self, config_file: str, model: str, version: str) -> Dict[str, Any]
```

**参数**:
- `config_file`: 配置文件路径
- `model`: 设备型号
- `version`: 版本号

**返回值**:
```python
{
    'valid': bool,                      # 验证是否通过
    'yang_validation': {                # YANG验证结果
        'performed': bool,
        'passed': bool,
        'errors': List[str]
    },
    'format_validation': {              # 格式验证结果
        'passed': bool,
        'errors': List[str]
    },
    'syntax_validation': {              # 语法验证结果
        'passed': bool,
        'errors': List[str]
    }
}
```

### ExtractionService

配置信息提取服务接口。

#### 构造函数

```python
ExtractionService(config_manager: ConfigManager)
```

#### extract_interfaces() 方法

```python
def extract_interfaces(self, config_file: str, vendor: str) -> Dict[str, Any]
```

**参数**:
- `config_file`: 配置文件路径
- `vendor`: 厂商名称

**返回值**:
```python
{
    'success': bool,
    'interfaces': List[Dict[str, Any]],  # 接口信息列表
    'interface_count': int,              # 接口数量
    'supported_vendors': List[str]       # 支持的厂商
}
```

## 业务逻辑层 API

### ConversionWorkflow

转换工作流管理器。

#### 构造函数

```python
ConversionWorkflow(
    config_manager: ConfigManager,
    template_manager: TemplateManager,
    yang_manager: YangManager
)
```

#### execute_conversion() 方法

```python
def execute_conversion(self, conversion_params: Dict[str, Any]) -> Dict[str, Any]
```

**参数**:
- `conversion_params`: 转换参数字典

**返回值**: 与ConversionService.convert()相同的结构

### FortigateConversionStrategy

Fortigate专用转换策略。

#### 构造函数

```python
FortigateConversionStrategy(
    config_manager: ConfigManager,
    template_manager: TemplateManager,
    yang_manager: YangManager
)
```

#### execute_strategy() 方法

```python
def execute_strategy(self, context: DataContext) -> bool
```

**参数**:
- `context`: 数据上下文对象

**返回值**: 执行是否成功

## 数据处理层 API

### PipelineManager

处理管道管理器。

#### 构造函数

```python
PipelineManager(pipeline_id: str, description: str = "")
```

#### add_stage() 方法

```python
def add_stage(self, stage: PipelineStage) -> bool
```

**参数**:
- `stage`: 管道阶段实例

#### execute() 方法

```python
def execute(self, initial_data: Dict[str, Any]) -> Dict[str, Any]
```

**参数**:
- `initial_data`: 初始数据

**返回值**:
```python
{
    'success': bool,
    'stages_executed': List[str],
    'execution_time': float,
    'final_data': Dict[str, Any],
    'stage_results': List[Dict[str, Any]]
}
```

### ParserRegistry

解析器注册表。

#### get_parser() 方法

```python
def get_parser(self, vendor: str) -> Optional[ParserPlugin]
```

**参数**:
- `vendor`: 厂商名称

**返回值**: 解析器实例或None

#### get_supported_vendors() 方法

```python
def get_supported_vendors() -> List[str]
```

**返回值**: 支持的厂商列表

### GeneratorRegistry

生成器注册表。

#### get_generator() 方法

```python
def get_generator(self, generator_type: str) -> Optional[GeneratorInterface]
```

**参数**:
- `generator_type`: 生成器类型

**返回值**: 生成器实例或None

#### get_supported_generators() 方法

```python
def get_supported_generators() -> List[str]
```

**返回值**: 支持的生成器类型列表

## 基础设施层 API

### ConfigManager

配置管理器。

#### get_engine_dir() 方法

```python
def get_engine_dir() -> str
```

**返回值**: 引擎目录路径

#### is_vendor_supported() 方法

```python
def is_vendor_supported(self, vendor: str) -> bool
```

**参数**:
- `vendor`: 厂商名称

**返回值**: 是否支持该厂商

### TemplateManager

模板管理器。

#### load_template() 方法

```python
def load_template(self, template_name: str, model: str, version: str) -> Optional[str]
```

**参数**:
- `template_name`: 模板名称
- `model`: 设备型号
- `version`: 版本号

**返回值**: 模板内容或None

### YangManager

YANG模型管理器。

#### validate_xml() 方法

```python
def validate_xml(self, xml_content: str, model: str, version: str) -> Dict[str, Any]
```

**参数**:
- `xml_content`: XML内容
- `model`: 设备型号
- `version`: 版本号

**返回值**:
```python
{
    'valid': bool,
    'errors': List[str],
    'warnings': List[str]
}
```

## 性能监控 API

### PerformanceMonitor

性能监控器。

#### start_operation() 方法

```python
def start_operation(self, operation_name: str) -> Dict[str, Any]
```

**参数**:
- `operation_name`: 操作名称

**返回值**: 操作上下文

#### end_operation() 方法

```python
def end_operation(self, context: Dict[str, Any], success: bool = True) -> PerformanceMetrics
```

**参数**:
- `context`: 操作上下文
- `success`: 操作是否成功

**返回值**: 性能指标对象

#### get_performance_summary() 方法

```python
def get_performance_summary() -> Dict[str, Any]
```

**返回值**: 性能摘要信息

## 错误处理 API

### ErrorHandler

错误处理器。

#### handle_error() 方法

```python
def handle_error(
    self, 
    exception: Exception, 
    context: Optional[Dict[str, Any]] = None,
    attempt_recovery: bool = True
) -> Dict[str, Any]
```

**参数**:
- `exception`: 异常实例
- `context`: 错误上下文
- `attempt_recovery`: 是否尝试恢复

**返回值**:
```python
{
    'handled': bool,
    'error_record': ErrorRecord,
    'recovery_result': Dict[str, Any],
    'user_message': str,
    'severity': ErrorSeverity
}
```

## 缓存管理 API

### CacheManager

缓存管理器。

#### create_cache() 方法

```python
def create_cache(
    self, 
    name: str, 
    max_size: int = 128, 
    default_ttl: Optional[float] = None
) -> LRUCache
```

**参数**:
- `name`: 缓存名称
- `max_size`: 最大缓存大小
- `default_ttl`: 默认TTL

**返回值**: LRU缓存实例

### LRUCache

LRU缓存实现。

#### get() 方法

```python
def get(self, key: str) -> Optional[Any]
```

#### put() 方法

```python
def put(self, key: str, value: Any, ttl: Optional[float] = None)
```

#### get_stats() 方法

```python
def get_stats() -> Dict[str, Any]
```

**返回值**:
```python
{
    'size': int,
    'max_size': int,
    'hit_rate': float,
    'hits': int,
    'misses': int,
    'evictions': int,
    'expirations': int
}
```

## 装饰器 API

### 性能监控装饰器

```python
@performance_monitor("operation_name")
def my_function():
    # 函数实现
    pass
```

### 缓存装饰器

```python
@cached("cache_name", ttl=3600)
def expensive_function(param1, param2):
    # 昂贵的计算
    return result
```

## 错误码参考

### 转换错误
- `CONVERSION_001`: 配置文件不存在
- `CONVERSION_002`: 不支持的厂商
- `CONVERSION_003`: 模板文件未找到
- `CONVERSION_004`: YANG验证失败

### 系统错误
- `SYSTEM_001`: 内存不足
- `SYSTEM_002`: 磁盘空间不足
- `SYSTEM_003`: 权限不足

### 配置错误
- `CONFIG_001`: 配置文件格式错误
- `CONFIG_002`: 必需参数缺失
- `CONFIG_003`: 参数值无效

## 最佳实践

### 1. 错误处理

```python
try:
    result = service.convert(**params)
    if not result['success']:
        logger.error(f"转换失败: {result['error']}")
        # 处理失败情况
except Exception as e:
    logger.exception("转换过程中发生异常")
    # 处理异常情况
```

### 2. 性能监控

```python
monitor = PerformanceMonitor()
context = monitor.start_operation("batch_conversion")

try:
    # 执行批量转换
    for config_file in config_files:
        result = service.convert(cli_file=config_file, ...)
finally:
    metrics = monitor.end_operation(context)
    logger.info(f"批量转换完成: {metrics.duration:.3f}秒")
```

### 3. 缓存使用

```python
cache_manager = CacheManager()
template_cache = cache_manager.create_cache("templates", max_size=50, default_ttl=3600)

# 使用缓存
cached_template = template_cache.get("template_key")
if cached_template is None:
    template = load_template_from_disk()
    template_cache.put("template_key", template)
else:
    template = cached_template
```
