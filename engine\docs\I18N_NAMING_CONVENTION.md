# FortiGate转换器国际化键命名规范

## 1. 总体原则

### 1.1 命名格式
```
{模块名}.{功能名}.{具体内容}
```

### 1.2 命名规则
- 使用小写字母和下划线
- 模块名使用单数形式
- 功能名描述具体操作或状态
- 具体内容描述详细信息类型

## 2. 模块分类

### 2.1 核心处理模块
- `parser` - 配置解析器
- `converter` - 转换器核心
- `generator` - XML生成器
- `validator` - 验证器
- `processor` - 数据处理器

### 2.2 功能模块
- `interface` - 接口处理
- `address` - 地址对象
- `service` - 服务对象
- `policy` - 安全策略
- `nat` - NAT规则
- `dns` - DNS配置
- `route` - 路由配置
- `zone` - 安全区域

### 2.3 系统模块
- `workflow` - 工作流程
- `pipeline` - 处理管道
- `template` - 模板管理
- `config` - 配置管理
- `cache` - 缓存管理
- `monitor` - 性能监控

## 3. 功能名分类

### 3.1 操作类型
- `start` - 开始操作
- `complete` - 完成操作
- `process` - 处理中
- `convert` - 转换操作
- `generate` - 生成操作
- `validate` - 验证操作
- `load` - 加载操作
- `save` - 保存操作

### 3.2 状态类型
- `success` - 成功状态
- `failed` - 失败状态
- `error` - 错误状态
- `warning` - 警告状态
- `info` - 信息状态
- `debug` - 调试信息

### 3.3 配置类型
- `config` - 配置信息
- `setting` - 设置信息
- `option` - 选项信息
- `param` - 参数信息

## 4. 具体内容分类

### 4.1 消息类型
- `message` - 一般消息
- `description` - 描述信息
- `detail` - 详细信息
- `summary` - 摘要信息
- `report` - 报告信息

### 4.2 错误类型
- `not_found` - 未找到
- `invalid` - 无效
- `missing` - 缺失
- `duplicate` - 重复
- `timeout` - 超时
- `permission` - 权限问题

### 4.3 数据类型
- `count` - 数量
- `size` - 大小
- `path` - 路径
- `name` - 名称
- `type` - 类型
- `value` - 值

## 5. 命名示例

### 5.1 正确示例
```
parser.fortigate.start_message = "开始解析FortiGate配置文件"
converter.interface.convert_success = "接口转换成功: {name}"
generator.xml.generate_complete = "XML生成完成，文件大小: {size}"
validator.yang.validate_failed = "YANG验证失败: {error}"
processor.address.process_count = "处理地址对象数量: {count}"
```

### 5.2 错误示例
```
❌ FortigateParserStartMessage
❌ interface_convert_success
❌ XML_Generate_Complete
❌ yangValidateFailed
❌ addressProcessCount
```

## 6. 特殊规则

### 6.1 参数占位符
- 使用 `{参数名}` 格式
- 参数名使用小写和下划线
- 常用参数：`{name}`, `{count}`, `{size}`, `{error}`, `{path}`

### 6.2 层级结构
对于复杂功能，可以使用更深层级：
```
processor.address.group.member.not_found = "地址组成员未找到: {member}"
generator.xml.template.integration.failed = "XML模板集成失败: {error}"
```

### 6.3 状态组合
```
{模块}.{功能}.{操作}_{状态}
例如：converter.policy.convert_success
```

## 7. 分类标准

### 7.1 按严重程度
- `error.*` - 错误信息（红色）
- `warning.*` - 警告信息（黄色）
- `info.*` - 一般信息（蓝色）
- `debug.*` - 调试信息（灰色）
- `success.*` - 成功信息（绿色）

### 7.2 按功能模块
- `conversion.*` - 转换相关
- `validation.*` - 验证相关
- `generation.*` - 生成相关
- `processing.*` - 处理相关
- `management.*` - 管理相关

### 7.3 按用户类型
- `user.*` - 面向用户的消息
- `admin.*` - 面向管理员的消息
- `developer.*` - 面向开发者的消息
- `system.*` - 系统内部消息

## 8. 实施指南

### 8.1 迁移策略
1. 创建新的键名映射表
2. 逐步替换旧的键名
3. 保留向后兼容性
4. 最终清理旧键名

### 8.2 验证工具
- 自动检查键名格式
- 验证参数占位符
- 检测重复键名
- 生成覆盖率报告

### 8.3 维护规范
- 新增键必须遵循规范
- 定期审查和清理
- 文档同步更新
- 团队培训和指导
