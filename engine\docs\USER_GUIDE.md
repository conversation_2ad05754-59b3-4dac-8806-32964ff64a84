# Python转换引擎用户指南

## 概述

Python转换引擎v2.0是一个强大的配置转换工具，专门用于将厂商特定的网络设备配置转换为NTOS格式。本指南将帮助您快速上手并充分利用新架构的功能。

## 安装和配置

### 系统要求

- Python 3.7+
- Windows/Linux操作系统
- 至少2GB可用内存
- 500MB磁盘空间

### 依赖包

```bash
pip install lxml psutil
```

### 可选依赖

- `yanglint`: 用于YANG模型验证（推荐）

## 快速开始

### 基本转换

最简单的转换操作只需要几行代码：

```python
from engine.application.conversion_service import ConversionService

# 创建转换服务
service = ConversionService()

# 执行转换
result = service.convert(
    cli_file="fortigate_config.conf",
    vendor="fortigate",
    model="z5100s",
    version="R10P2"
)

# 检查结果
if result['success']:
    print("转换成功！")
else:
    print(f"转换失败: {result['error']}")
```

### 带接口映射的转换

对于需要接口映射的场景：

```python
result = service.convert(
    cli_file="fortigate_config.conf",
    vendor="fortigate",
    model="z5100s",
    version="R10P2",
    interface_mapping_file="interface_mapping.json",
    output_file="converted_config.xml"
)
```

## 支持的功能

### Fortigate配置转换

#### 支持的配置项

1. **防火墙策略 (Firewall Policy)**
   - 基本策略转换
   - 源/目标接口映射
   - 地址对象引用
   - 服务对象引用
   - 动作映射 (accept/deny)

2. **NAT规则生成**
   - SNAT规则 (nat enable)
   - DNAT规则 (VIP对象)
   - 端口映射
   - 地址转换

3. **安全功能映射**
   - 防病毒配置 (av-profile → default-alert)
   - 入侵防护 (ips-sensor → default-use-signature-action)

4. **接口配置**
   - 物理接口
   - 子接口 (VLAN)
   - PPPoE接口
   - 接口映射

5. **地址对象**
   - 网络地址
   - 主机地址
   - VIP对象
   - 地址组

6. **服务对象**
   - TCP/UDP服务
   - 自定义服务
   - 服务组

#### 版本支持

- **R10P2**: 仅安全策略转换
- **R11及以上**: 完整功能支持（包括NAT/SNAT）

### 不支持的功能

以下功能会生成警告信息：

- 用户和用户组 (users/groups)
- 固定端口 (fixedport enable)
- 某些高级安全功能

## 配置文件格式

### 接口映射文件

接口映射文件使用JSON格式：

```json
{
  "port1": "Ge0/1",
  "port2": "Ge0/2",
  "port1.100": "Ge0/1.100",
  "port3": "Ge0/3"
}
```

### 输出XML格式

转换后的XML遵循NTOS YANG模型：

```xml
<security-policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
  <policy>
    <name>Allow_Internal_to_Internet</name>
    <enabled>true</enabled>
    <group-name>default</group-name>
    <source-zone>trust</source-zone>
    <destination-zone>untrust</destination-zone>
    <service>
      <name>HTTP</name>
    </service>
    <action>permit</action>
    <config-source>fortigate</config-source>
  </policy>
</security-policy>
```

## 高级功能

### 性能监控

新架构内置了性能监控功能：

```python
# 转换结果包含性能指标
if 'performance_metrics' in result:
    perf = result['performance_metrics']
    print(f"转换耗时: {perf['duration']:.3f}秒")
    print(f"内存使用: {perf['memory_used_mb']:.2f}MB")
    print(f"CPU使用率: {perf['cpu_percent']:.1f}%")
```

### 批量转换

处理多个配置文件：

```python
import os
from engine.application.conversion_service import ConversionService

service = ConversionService()
config_dir = "configs"
output_dir = "outputs"

for filename in os.listdir(config_dir):
    if filename.endswith('.conf'):
        config_path = os.path.join(config_dir, filename)
        output_path = os.path.join(output_dir, filename.replace('.conf', '.xml'))
        
        result = service.convert(
            cli_file=config_path,
            vendor="fortigate",
            model="z5100s",
            version="R11",
            output_file=output_path
        )
        
        if result['success']:
            print(f"✅ {filename} 转换成功")
        else:
            print(f"❌ {filename} 转换失败: {result['error']}")
```

### 配置验证

在转换前验证配置：

```python
from engine.application.validation_service import ValidationService

validator = ValidationService()
validation_result = validator.validate_config(
    config_file="config.xml",
    model="z5100s",
    version="R11"
)

if validation_result['valid']:
    print("配置验证通过")
else:
    print("配置验证失败:")
    for error in validation_result.get('yang_validation', {}).get('errors', []):
        print(f"  - {error}")
```

### 信息提取

提取配置中的特定信息：

```python
from engine.application.extraction_service import ExtractionService

extractor = ExtractionService()
interfaces = extractor.extract_interfaces(
    config_file="fortigate_config.conf",
    vendor="fortigate"
)

if interfaces['success']:
    print(f"发现 {interfaces['interface_count']} 个接口:")
    for interface in interfaces['interfaces']:
        print(f"  - {interface['name']}: {interface.get('ip', 'N/A')}")
```

## 错误处理和故障排除

### 常见错误

#### 1. 文件不存在
```
错误: FileNotFoundError: [Errno 2] No such file or directory: 'config.conf'
解决: 检查文件路径是否正确
```

#### 2. 模板未找到
```
错误: conversion_service.template_not_found
解决: 确保模板文件存在于正确的目录中
```

#### 3. 不支持的厂商
```
错误: 不支持的厂商: cisco
解决: 当前版本仅支持fortigate厂商
```

#### 4. YANG验证失败
```
错误: yang_validation_failed
解决: 检查yanglint工具安装，或禁用YANG验证
```

### 调试技巧

#### 启用详细输出

```python
result = service.convert(
    cli_file="config.conf",
    vendor="fortigate",
    model="z5100s",
    version="R10P2",
    verbose=True  # 启用详细输出
)
```

#### 查看转换统计

```python
if 'conversion_stats' in result:
    stats = result['conversion_stats']
    print(f"转换的策略数: {stats.get('policies_converted', 0)}")
    print(f"生成的NAT规则: {stats.get('nat_rules_generated', 0)}")
    
    # 查看警告信息
    for warning in stats.get('warnings', []):
        print(f"警告: {warning}")
```

#### 检查管道执行

```python
if result.get('conversion_method') == 'pipeline':
    pipeline_info = result.get('pipeline_info', {})
    print(f"执行的阶段: {pipeline_info.get('stages_executed', [])}")
    print(f"总阶段数: {pipeline_info.get('total_stages', 0)}")
```

## 性能优化

### 内存管理

对于大型配置文件，可以启用内存优化：

```python
from engine.infrastructure.common.performance import MemoryManager

memory_manager = MemoryManager(memory_threshold_mb=200.0)

# 在处理前检查内存
memory_status = memory_manager.check_memory_usage()
if memory_status['threshold_exceeded']:
    memory_manager.optimize_memory()

# 执行转换
result = service.convert(...)
```

### 缓存优化

利用缓存提高重复操作的性能：

```python
from engine.infrastructure.common.caching import CacheManager

cache_manager = CacheManager()
template_cache = cache_manager.create_cache("templates", max_size=20)

# 缓存会自动用于模板加载
```

### 并发处理

对于批量转换，可以使用多线程：

```python
import threading
import queue

def worker_conversion(config_queue, result_queue):
    service = ConversionService()
    
    while True:
        try:
            config_file = config_queue.get(timeout=1)
            result = service.convert(
                cli_file=config_file,
                vendor="fortigate",
                model="z5100s",
                version="R11"
            )
            result_queue.put((config_file, result))
            config_queue.task_done()
        except queue.Empty:
            break

# 创建队列
config_queue = queue.Queue()
result_queue = queue.Queue()

# 添加配置文件到队列
for config_file in config_files:
    config_queue.put(config_file)

# 启动工作线程
threads = []
for i in range(4):  # 4个并发线程
    thread = threading.Thread(target=worker_conversion, args=(config_queue, result_queue))
    thread.start()
    threads.append(thread)

# 等待完成
config_queue.join()

# 收集结果
results = []
while not result_queue.empty():
    results.append(result_queue.get())
```

## 最佳实践

### 1. 错误处理

始终检查转换结果：

```python
result = service.convert(...)

if result['success']:
    # 处理成功情况
    print(f"输出文件: {result['output_file']}")
else:
    # 处理失败情况
    print(f"转换失败: {result['error']}")
    # 记录日志或采取其他措施
```

### 2. 资源管理

对于长时间运行的应用，定期清理资源：

```python
# 定期清理缓存
cache_manager.clear_all_caches()

# 强制垃圾回收
memory_manager.optimize_memory(force=True)
```

### 3. 配置验证

在转换前验证输入：

```python
# 检查文件存在性
if not os.path.exists(config_file):
    print("配置文件不存在")
    return

# 检查厂商支持
if not service.is_vendor_supported(vendor):
    print(f"不支持的厂商: {vendor}")
    return

# 执行转换
result = service.convert(...)
```

### 4. 日志记录

建议启用适当的日志记录：

```python
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# 转换引擎会自动记录日志
result = service.convert(...)
```

## 迁移指南

### 从v1.x迁移

新架构完全向后兼容，现有代码无需修改即可运行。但建议逐步迁移到新API：

#### 旧方式 (仍然支持)
```python
# 直接调用现有函数
from engine.legacy import convert_fortigate_config
result = convert_fortigate_config(config_file, output_file)
```

#### 新方式 (推荐)
```python
# 使用新的服务接口
from engine.application.conversion_service import ConversionService
service = ConversionService()
result = service.convert(cli_file=config_file, vendor="fortigate", ...)
```

### 新功能优势

- **性能监控**: 自动收集性能指标
- **错误处理**: 更好的错误分类和恢复
- **缓存优化**: 提高重复操作性能
- **模块化设计**: 更容易扩展和维护

## 支持和反馈

如果您遇到问题或有改进建议，请：

1. 检查本文档的故障排除部分
2. 查看系统日志获取详细错误信息
3. 联系技术支持团队

## 版本更新

### v2.0.0 新功能
- 完整架构重构
- 性能监控集成
- 智能缓存系统
- 统一错误处理
- 模块化设计
- 向后兼容保证
