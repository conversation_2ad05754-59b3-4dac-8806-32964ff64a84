import os
import shutil
import uuid
import zlib
from engine.utils.logger import log
from engine.utils.i18n import init_i18n, _

def copy_config_directory(template_path, output_xml_path, output_dir):
    """
    复制模板所在的Config目录，并用生成的XML替换running.xml和startup.xml
    
    Args:
        template_path (str): 模板文件路径
        output_xml_path (str): 生成的XML文件路径
        output_dir (str): 输出目录路径
    
    Returns:
        str: 复制后的Config目录路径
    """
    log(_("info.encrypt_start"))

    # 获取模板所在的目录结构
    template_dir = os.path.dirname(template_path)
    log(_("info.template_dir"), dir=template_dir)
    
    # 找到Config目录的根路径（可能是/data/input/model/version/Config或类似结构）
    config_root = template_dir
    parent_dirs = []
    
    # 向上查找Config目录
    while os.path.dirname(config_root) != "":
        dirname = os.path.basename(config_root)
        if dirname == "Config":
            break
        parent_dirs.append(dirname)
        config_root = os.path.dirname(config_root)
    
    # 判断是否找到Config目录
    if os.path.basename(config_root) != "Config":
        # 如果模板文件路径包含"Config"
        if "Config" in template_path:
            # 尝试构建Config目录路径
            path_parts = template_path.split(os.path.sep)
            config_index = path_parts.index("Config") if "Config" in path_parts else -1
            
            if config_index >= 0:
                config_root = os.path.sep.join(path_parts[:config_index+1])
                log(_("info.config_dir_found"), path=config_root)
            else:
                log(_("warning.config_dir_not_found"), "warning")
                config_root = template_dir
        else:
            log(_("warning.config_dir_not_found"), "warning")
            config_root = template_dir
    
    log(_("info.using_config_root"), path=config_root)

    # 创建输出目录（如果不存在）
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        log(_("info.create_output_dir"), dir=output_dir)

    # 复制Config目录结构
    output_config_dir = os.path.join(output_dir, "Config")
    if os.path.exists(output_config_dir):
        shutil.rmtree(output_config_dir)
        log(_("info.delete_existing_output"), dir=output_config_dir)
    
    # 复制整个Config目录
    if os.path.basename(config_root) == "Config":
        shutil.copytree(config_root, output_config_dir)
        log(_("info.copy_config_dir"), src=config_root, dst=output_config_dir)
    else:
        # 如果没有找到Config目录，则创建一个新的
        os.makedirs(output_config_dir)
        log(_("info.create_new_config_dir"), dir=output_config_dir)

    # 确保目标目录中存在running.xml和startup.xml的父目录
    rel_template_path = os.path.relpath(template_path, config_root) if os.path.basename(config_root) == "Config" else "running.xml"
    running_xml_dir = os.path.dirname(os.path.join(output_config_dir, rel_template_path))
    
    if not os.path.exists(running_xml_dir):
        os.makedirs(running_xml_dir)
        log(_("info.create_directory"), dir=running_xml_dir)

    # 用生成的XML替换running.xml和startup.xml
    if os.path.basename(config_root) == "Config":
        running_xml_path = os.path.join(output_config_dir, rel_template_path)
    else:
        running_xml_path = os.path.join(output_config_dir, "running.xml")
    
    startup_xml_path = os.path.join(output_config_dir, "startup.xml")
    
    # 复制生成的XML到running.xml和startup.xml
    shutil.copy2(output_xml_path, running_xml_path)
    shutil.copy2(output_xml_path, startup_xml_path)
    log(_("info.copy_xml_to_running"), path=running_xml_path)
    log(_("info.copy_xml_to_startup"), path=startup_xml_path)

    return output_config_dir

def encrypt_config_directory(config_dir, output_file):
    """
    加密Config目录为tar.gz文件
    
    Args:
        config_dir (str): Config目录路径
        output_file (str): 输出加密文件路径
    
    Returns:
        bool: 加密是否成功
    """
    try:
        # 导入compress_demo模块
        from engine.compress_demo import encrypt_directory
        
        log(_("info.start_encryption"), dir=config_dir)
        result = encrypt_directory(config_dir, output_file)
        
        if result:
            # 验证生成的文件是否有效
            if os.path.exists(output_file) and os.path.getsize(output_file) > 0:
                log(_("info.encryption_success"), file=output_file)
                # 删除任何可能存在的错误标记文件
                error_marker = f"{output_file}.error"
                if os.path.exists(error_marker):
                    os.remove(error_marker)
                return True
            else:
                log(_("error.output_file_invalid"), "error")
                # 创建错误标记文件
                with open(f"{output_file}.error", "w") as f:
                    f.write(_("encrypt.error.output_invalid"))
                return False
        else:
            log(_("error.encryption_failed"), "error")
            # 删除可能生成的空文件
            if os.path.exists(output_file):
                try:
                    os.remove(output_file)
                    log(_("info.removed_invalid_output"), file=output_file)
                except:
                    pass
                    
            # 创建错误标记文件
            with open(f"{output_file}.error", "w") as f:
                f.write(_("encrypt.error.library_failed"))
            return False
    except Exception as e:
        error_msg = _("error.encryption_error", error=str(e))
        log(error_msg, "error")
        import traceback
        log(traceback.format_exc(), "error")
        
        # 删除可能生成的空文件
        if os.path.exists(output_file):
            try:
                os.remove(output_file)
                log(_("info.removed_invalid_output"), file=output_file)
            except:
                pass
                
        # 创建错误标记文件
        with open(f"{output_file}.error", "w") as f:
            f.write(_("encrypt.error.process_failed", error=str(e)))
        return False

def encrypt_config(xml_file, template_path, output_file):
    """
    将XML配置文件加密为tar.gz文件
    
    Args:
        xml_file (str): XML配置文件路径
        template_path (str): 模板文件路径
        output_file (str): 输出加密文件路径
    
    Returns:
        bool: 加密是否成功
        str: 结果消息或错误信息
    """
    try:
        # 创建临时目录
        temp_dir = os.path.join(os.path.dirname(output_file), f"temp_{uuid.uuid4()}")
        
        # 复制Config目录
        config_dir = copy_config_directory(template_path, xml_file, temp_dir)
        
        # 确保输出目录存在
        output_dir = os.path.dirname(output_file)
        if not os.path.exists(output_dir) and output_dir:
            os.makedirs(output_dir)
        
        # 加密Config目录
        success = encrypt_config_directory(config_dir, output_file)
        
        # 清理临时目录
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
            log(_("info.cleanup_temp_dir"), dir=temp_dir)
        
        if success:
            return True, f"加密成功，结果保存至: {output_file}"
        else:
            return False, "加密失败"
            
    except Exception as e:
        error_msg = _("error.encrypt_error", error=str(e))
        log(error_msg, "error")
        import traceback
        log(traceback.format_exc(), "error")
        return False, f"加密出错: {str(e)}"

if __name__ == "__main__":
    # 直接运行时的简单命令行处理
    import argparse
    import sys
    import datetime
    parser = argparse.ArgumentParser(description="配置加密工具")
    parser.add_argument("--xml", required=True, help="XML配置文件路径")
    parser.add_argument("--template", required=True, help="模板文件路径")
    parser.add_argument("--output", help="输出加密文件路径")
    parser.add_argument("--language", default="zh-CN", help="指定使用的语言，默认为zh-CN")
    args = parser.parse_args()
    
    # 初始化国际化系统
    init_i18n(args.language)
    
    # 如果未指定输出文件，创建一个默认的输出文件
    if not args.output:
        date_str = datetime.datetime.now().strftime("%Y%m%d-%H%M")
        args.output = f"backup-startup-{date_str}.tar.gz"
    
    # 加密配置
    success, message = encrypt_config(args.xml, args.template, args.output)
    
    print(message)
    
    if success:
        sys.exit(0)
    else:
        sys.exit(1) 