#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import json
import re
import sys
import logging

# 添加父目录到Python路径，确保可以正确导入engine模块
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from engine.utils.logger import log, user_log
from engine.utils.i18n import _
from engine.verify import detect_fortigate_version, verify_fortigate_config
from engine.utils.interface_utils import (
    is_physical_interface,
    validate_pppoe_interface,
    mask_interface_passwords,
    process_interfaces_with_pppoe_filtering_and_masking
)

# 支持的厂商列表，目前仅支持FortiGate
SUPPORTED_VENDORS = {
    "fortigate": "extract_fortigate_interfaces",
    # 未来可以添加更多厂商支持
}

# 物理接口类型列表 - 根据用户提供的信息更新
PHYSICAL_INTERFACE_TYPES = [
    "physical"  # 只有physical是物理接口
]

# 物理接口名称模式列表
PHYSICAL_INTERFACE_PATTERNS = [
    r"^port\d+$",    # port1, port2, ...
    r"^wan\d*$",     # wan, wan1, wan2, ...
    r"^lan\d*$",     # lan, lan1, lan2, ...
    r"^dmz\d*$",     # dmz, dmz1, dmz2, ...
    r"^internal\d*$", # internal, internal1, ...
    r"^external\d*$", # external, external1, ...
    r"^mgmt\d*$",    # mgmt, mgmt1, ...
    r"^modem\d*$",   # modem, modem1, ...
    r"^wwan\d*$",    # wwan, wwan1, ...
]

# 非物理接口类型列表 - 根据用户提供的信息更新
NON_PHYSICAL_INTERFACE_TYPES = [
    "port",        # 非物理接口
    "vlan",        # vlan子接口
    "aggregate",   # 汇聚接口
    "redundant",   # 冗余接口
    "tunnel",      # 隧道接口
    "vdom-link",   # vdom链路
    "loopback",    # 回环接口
    "switch",      # 软件交换口
    "hard-switch", # 硬件交换口
    "vap-switch",  # vap接口
    "wl-mesh",     # WLAN mesh接口
    "fext-wan",    # FortiExtender接口
    "vxlan",       # vxlan接口
    "hdlc",        # T1/E1接口
    "switch-vlan", # 交换VLAN接口
    "emac-vlan"    # EMAC VLAN接口
]

# 接口类型的国际化映射
INTERFACE_TYPE_I18N = {
    "physical": "interface.type.physical",
    "port": "interface.type.port",
    "vlan": "interface.type.vlan",
    "aggregate": "interface.type.aggregate",
    "redundant": "interface.type.redundant",
    "tunnel": "interface.type.tunnel",
    "vdom-link": "interface.type.vdom_link",
    "loopback": "interface.type.loopback",
    "switch": "interface.type.switch",
    "hard-switch": "interface.type.hard_switch",
    "vap-switch": "interface.type.vap_switch",
    "wl-mesh": "interface.type.wl_mesh",
    "fext-wan": "interface.type.fext_wan",
    "vxlan": "interface.type.vxlan",
    "hdlc": "interface.type.hdlc",
    "switch-vlan": "interface.type.switch_vlan",
    "emac-vlan": "interface.type.emac_vlan",
    "wan": "interface.type.wan",
    "lan": "interface.type.lan",
    "dmz": "interface.type.dmz",
    "pppoe": "interface.type.pppoe",
    "unknown": "interface.type.unknown",
    "subinterface": "interface.type.subinterface"  # 子接口类型
}

def mask_to_prefix(mask):
    """将子网掩码转换为前缀长度"""
    try:
        if '.' in mask:  # 确保是点分十进制格式
            # 将掩码转换为二进制字符串，计算1的个数
            binary = ''.join([bin(int(x))[2:].zfill(8) for x in mask.split('.')])
            return binary.count('1')
        else:
            return int(mask)  # 如果已经是数字，直接返回
    except Exception as e:
        error_msg = _("error.mask_conversion_failed", mask=mask, error=str(e))
        log(error_msg, "error")
        # 对于测试用例，保证抛出异常
        raise ValueError(_("error.invalid_subnet_mask_format", mask=mask))

def get_vlan_from_name(interface_name):
    """从接口名中提取VLAN ID (例如从"vlan10"提取10)"""
    try:
        # 查找常见的VLAN接口命名模式
        vlan_patterns = [
            r"vlan(\d+)",  # vlan10
            r"VLAN(\d+)",  # VLAN10
            r"Vlan(\d+)",  # Vlan10
            r"\.(\d+)$",   # eth0.10
        ]
        
        for pattern in vlan_patterns:
            match = re.search(pattern, interface_name)
            if match:
                return int(match.group(1))
        return None
    except Exception as e:
        error_msg = _("error.vlan_extraction_failed", interface=interface_name, error=str(e))
        log(error_msg, "error")
        return None

def is_physical_interface_name(interface_name):
    """
    根据接口名称判断是否为物理接口
    
    Args:
        interface_name (str): 接口名称
        
    Returns:
        bool: 是否可能为物理接口
    """
    # 如果是子接口，则不是物理接口
    if is_subinterface(interface_name):
        return False
    
    # 检查是否匹配物理接口名称模式
    for pattern in PHYSICAL_INTERFACE_PATTERNS:
        if re.match(pattern, interface_name.lower()):
            return True
    
    return False



def is_subinterface(interface_name, interface_config=None):
    """
    判断是否为子接口
    
    Args:
        interface_name (str): 接口名称
        interface_config (dict, optional): 接口配置，如果提供
        
    Returns:
        bool: 是否为子接口
    """
    # 检查是否包含点号（常见的子接口命名方式，如port1.10）
    if '.' in interface_name:
        return True
    
    # 检查是否为VLAN接口（另一种常见的子接口形式）
    if interface_name.lower().startswith('vlan'):
        return True
    
    # 检查是否包含特定子接口关键字
    if 'wan_pppoe' in interface_name.lower():
        return True
    
    # 检查是否包含 VLAN ID 格式（如 vlan-100, port2-30 等）
    if re.search(r'-\d+$', interface_name):
        return True
    
    # 检查是否为PPPoE子接口（基于配置信息）
    if interface_config:
        # 如果接口配置中有mode=pppoe，并且指定了物理接口和vlanid，则为子接口
        if (interface_config.get('mode') == 'pppoe' and 
            'interface' in interface_config and 
            'vlanid' in interface_config):
            return True
    
    return False

def get_interface_type_name(interface_type):
    """
    获取接口类型的国际化名称
    
    Args:
        interface_type (str): 接口类型
        
    Returns:
        str: 接口类型的国际化名称
    """
    # 获取接口类型的国际化映射键
    i18n_key = INTERFACE_TYPE_I18N.get(interface_type.lower(), "interface.type.unknown")
    
    # 返回国际化的接口类型名称
    return _(i18n_key)

def extract_interface_info(cli_file, output_json=None, vendor="fortigate", language="zh-CN"):
    """
    从配置文件中提取接口信息，只提取区域、接口名称、IP、子网掩码
    
    Args:
        cli_file (str): 配置文件路径
        output_json (str): 输出JSON文件路径，如果为None则不输出到文件
        vendor (str): 厂商标识，当前仅支持fortigate
        language (str): 国际化语言，默认为中文
        
    Returns:
        dict: 接口信息字典
    """
    try:
        # 初始化国际化
        from engine.utils.i18n import init_i18n
        init_i18n(language)
        
        # 检查厂商是否支持
        vendor = vendor.lower()
        if vendor != "fortigate":
            supported_vendors = ", ".join(SUPPORTED_VENDORS.keys())
            log(_("error.vendor_not_supported"), "error", vendor=vendor, supported=supported_vendors)
            user_log(_("error.vendor_not_supported"), "error", vendor=vendor, supported=supported_vendors)
            print(_("error.vendor_not_supported", vendor=vendor, supported=supported_vendors), file=sys.stderr)
            return {
                "success": False,
                "message": _("error.vendor_not_supported", vendor=vendor, supported=supported_vendors),
                "error": _("error.vendor_not_supported", vendor=vendor, supported=supported_vendors),
                "interfaces": [],
                "count": 0
            }
            
        # 检查配置文件是否存在
        if not os.path.isfile(cli_file):
            log(_("error.file_not_exists"), "error", file=cli_file)
            user_log(_("error.file_not_exists"), "error", file=cli_file)
            return {
                "success": False,
                "message": _("error.file_not_exists", file=cli_file),
                "error": _("error.file_not_exists", file=cli_file),
                "interfaces": [],
                "count": 0
            }
            
        log(_("info.extraction_start"), "info")
        user_log(_("info.extraction_start"), "info")
        
        # 读取文件内容，用于版本检测
        with open(cli_file, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # 检测飞塔版本
        version_detected, version_str, major, minor, patch = detect_fortigate_version(content)
        
        # 如果没有检测到版本，直接返回错误
        if not version_detected:
            error_msg = _("error.unable_to_detect_version")
            log(_("error.unable_to_detect_version"), "error")
            user_log(_("error.unable_to_detect_version"), "error")
            return {
                "success": False,
                "message": error_msg,
                "error": error_msg,
                "interfaces": [],
                "count": 0
            }
        
        log(_("info.detected_fortigate_version"), "info", version=version_str)
        user_log(_("info.detected_fortigate_version"), "info", version=version_str)
        
        # 基础配置验证，确保配置文件格式正确
        warnings = []
        try:
            # 进行基础语法检查
            if "config system" not in content and "config firewall" not in content and "config interface" not in content:
                error_msg = _("error.invalid_fortigate_config")
                log(_("error.invalid_fortigate_config"), "error")
                user_log(_("error.invalid_fortigate_config"), "error")
                return {
                    "success": False,
                    "message": error_msg,
                    "error": error_msg,
                    "interfaces": [],
                    "count": 0
                }
        except Exception as e:
            error_msg = _("error.verification_failed", error=str(e))
            error_msg = _("error.verification_failed", error=str(e))
            log(error_msg, "error")
            user_log(_("error.verification_failed"), "error")
            return {
                "success": False,
                "message": error_msg,
                "error": error_msg,
                "interfaces": [],
                "count": 0
            }
        
        # 提取接口信息
        result = extract_fortigate_interfaces(cli_file)
        
        # 添加版本信息和警告到结果
        if result:
            result["version"] = version_str
            if "warnings" not in result:
                result["warnings"] = []
            result["warnings"].extend(warnings)
        
        # 如果提取成功，输出到文件（如果指定了输出文件）
        if result and result.get("success") and output_json:
            try:
                with open(output_json, 'w', encoding='utf-8') as f:
                    json.dump(result, f, ensure_ascii=False, indent=2)
                log(_("info.interface_info_written"), "info", file=output_json)
            except Exception as e:
                error_msg = _("error.write_interface_info_failed", error=str(e))
                log(error_msg, "error")
        
        return result
    except Exception as e:
        error_msg = _("error.extraction_failed", error=str(e))
        log(error_msg, "error")
        user_log(_("error.extraction_failed"), "error")
        return {
            "success": False,
            "message": _("error.extraction_failed"),
            "error": str(e),
            "interfaces": [],
            "count": 0
        }

def extract_fortigate_interfaces(cli_file):
    """从飞塔配置中提取接口信息，只提取区域、接口名称、IP、子网掩码"""
    try:
        log(_("info.extracting_interfaces_from_config"), file=cli_file)
        
        interfaces = []
        current_interface = None
        zones = []
        current_zone = None
        filtered_pppoe_count = 0  # 记录被过滤的PPPoE接口数量
        
        with open(cli_file, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
            
            # 增强的基础语法检查 - 确保配置文件是有效的飞塔格式
            # 配置文件至少应包含system interface配置段
            if "config system interface" not in content:
                error_msg = _("error.missing_interface_config")
                log(_("error.missing_interface_config"), "error")
                user_log(_("error.missing_interface_config"), "error")
                return {
                    "success": False,
                    "message": error_msg,
                    "error": error_msg,
                    "interfaces": [],
                    "count": 0
                }
            
            # 更严格的格式检查 - 确保包含必要的配置结构
            if "config system" not in content or "end" not in content:
                error_msg = _("error.invalid_fortigate_config_structure")
                log(_("error.invalid_fortigate_config_structure"), "error")
                user_log(_("error.invalid_fortigate_config_structure"), "error")
                return {
                    "success": False,
                    "message": error_msg,
                    "error": error_msg,
                    "interfaces": [],
                    "count": 0
                }
            
            # 从内容中提取区域信息
            zone_section = re.search(r'config system zone(.*?)end', content, re.DOTALL)
            if zone_section:
                log(_("info.extracting_zones"))
                zone_content = zone_section.group(1)
                zone_blocks = re.finditer(r'edit\s+"([^"]+)"(.*?)next', zone_content, re.DOTALL)
                
                for match in zone_blocks:
                    zone_name = match.group(1)
                    zone_block = match.group(2)
                    
                    # 提取区域中的接口
                    interfaces_match = re.search(r'set interface\s+(.*)', zone_block)
                    if interfaces_match:
                        interfaces_str = interfaces_match.group(1)
                        # 提取引号中的接口名称
                        zone_interfaces = re.findall(r'"([^"]+)"', interfaces_str)
                        zones.append({
                            "name": zone_name,
                            "interfaces": zone_interfaces
                        })
                        log(_("info.zone_extracted"), zone=zone_name, interfaces=len(zone_interfaces))
            
            log(_("info.zones_found"), count=len(zones))
            
            # 处理接口配置
            in_interface_block = False
            in_nested_block = False
            
            for line in content.split('\n'):
                line = line.strip()
                
                # 检测接口配置块开始
                if line.startswith('config system interface'):
                    in_interface_block = True
                    continue
                
                # 检测接口配置块结束
                if in_interface_block and line == 'end':
                    # 检查是否处于嵌套块中
                    if in_nested_block:
                        in_nested_block = False
                    else:
                        in_interface_block = False
                        # 确保最后一个接口被添加
                        if current_interface:
                            # 分析接口类型并设置必填属性
                            analyze_and_set_interface_type(current_interface)

                            # 过滤Modem接口（逻辑接口，不需要转换）
                            interface_name = current_interface.get('name', '').lower()
                            if interface_name == 'modem':
                                log(_("info.modem_interface_filtered"), "info", interface=current_interface.get('name', ''))
                                current_interface = None
                                continue

                            # 验证PPPoE接口完整性，只有验证通过的接口才添加到结果中
                            if validate_pppoe_interface(current_interface):
                                interfaces.append(current_interface)
                            else:
                                # 如果是被过滤的PPPoE接口，增加计数
                                if (current_interface.get('mode') == 'pppoe' and
                                    is_physical_interface(current_interface.get('name', ''),
                                                        current_interface.get('type', ''),
                                                        current_interface)):
                                    filtered_pppoe_count += 1
                            current_interface = None
                    continue
                
                # 只处理接口配置块内的内容
                if in_interface_block:
                    # 处理嵌套配置块
                    if line.startswith('config '):
                        in_nested_block = True
                        continue
                    
                    # 跳过嵌套配置块内的所有行
                    if in_nested_block:
                        continue
                    
                    # 新接口开始
                    if line.startswith('edit '):
                        if current_interface:
                            # 分析接口类型并设置必填属性
                            analyze_and_set_interface_type(current_interface)

                            # 过滤Modem接口（逻辑接口，不需要转换）
                            interface_name = current_interface.get('name', '').lower()
                            if interface_name == 'modem':
                                log(_("info.modem_interface_filtered"), "info", interface=current_interface.get('name', ''))
                            else:
                                # 验证PPPoE接口完整性，只有验证通过的接口才添加到结果中
                                if validate_pppoe_interface(current_interface):
                                    interfaces.append(current_interface)
                                else:
                                    # 如果是被过滤的PPPoE接口，增加计数
                                    if (current_interface.get('mode') == 'pppoe' and
                                        is_physical_interface(current_interface.get('name', ''),
                                                            current_interface.get('type', ''),
                                                            current_interface)):
                                        filtered_pppoe_count += 1
                        
                        interface_name = line[5:].strip('"\'')
                        current_interface = {
                            'name': interface_name,
                            'zone': 'NA',  # 默认区域为NA
                            'ip': 'NA',    # 默认IP为NA
                            'mask': 'NA',   # 默认掩码为NA
                            # 初始化时只根据名称判断是否为子接口，后续会根据配置更新
                            'is_subinterface': is_subinterface(interface_name)
                        }
                        
                    # IP地址和掩码提取
                    elif line.startswith('set ip ') and current_interface:
                        ip_line = line[7:].strip()
                        ip_parts = ip_line.split()
                        
                        if ip_parts:
                            ip = ip_parts[0]
                            current_interface['ip'] = ip
                            
                            # 如果同时提供了掩码
                            if len(ip_parts) > 1:
                                mask = ip_parts[1]
                                current_interface['mask'] = mask
                                
                                # 计算前缀长度
                                prefix = mask_to_prefix(mask)
                                if prefix is not None:
                                    current_interface['prefix'] = prefix
                    
                    # 状态信息
                    elif line.startswith('set status ') and current_interface:
                        status = line[11:].strip()
                        current_interface['status'] = status
                    
                    # vdom信息(区域)
                    elif line.startswith('set vdom ') and current_interface:
                        vdom = line[9:].strip().strip('"\'')
                        current_interface['zone'] = vdom
                    
                    # 访问控制设置
                    elif line.startswith('set allowaccess ') and current_interface:
                        allowaccess = line[15:].strip()
                        current_interface['allowaccess'] = allowaccess
                        
                    # 接口类型
                    elif line.startswith('set type ') and current_interface:
                        interface_type = line[9:].strip()
                        current_interface['type'] = interface_type
                        
                    # PPPoE模式
                    elif line.startswith('set mode pppoe') and current_interface:
                        current_interface['mode'] = 'pppoe'

                    # PPPoE用户名
                    elif line.startswith('set username ') and current_interface:
                        username = line[13:].strip().strip('"\'')
                        current_interface['username'] = username
                        current_interface['pppoe_username'] = username  # 兼容现有代码

                    # PPPoE密码
                    elif line.startswith('set password ') and current_interface:
                        password = line[13:].strip()
                        # 处理加密密码格式
                        if password.startswith('ENC '):
                            password = password[4:]
                        current_interface['password'] = password
                        current_interface['pppoe_password'] = password  # 兼容现有代码

                    # 物理接口（对于PPPoE子接口）
                    elif line.startswith('set interface ') and current_interface:
                        parent_interface = line[13:].strip().strip('"\'')
                        current_interface['interface'] = parent_interface

                    # VLAN ID（对于PPPoE子接口）
                    elif line.startswith('set vlanid ') and current_interface:
                        vlanid = line[11:].strip()
                        current_interface['vlanid'] = vlanid
                    
                    # 处理next结束当前接口编辑
                    elif line == 'next' and current_interface:
                        # 分析接口类型并设置必填属性
                        analyze_and_set_interface_type(current_interface)

                        # 过滤Modem接口（逻辑接口，不需要转换）
                        interface_name = current_interface.get('name', '').lower()
                        if interface_name == 'modem':
                            log(_("info.modem_interface_filtered"), "info", interface=current_interface.get('name', ''))
                        else:
                            # 验证PPPoE接口完整性，只有验证通过的接口才添加到结果中
                            if validate_pppoe_interface(current_interface):
                                interfaces.append(current_interface)
                            else:
                                # 如果是被过滤的PPPoE接口，增加计数
                                if (current_interface.get('mode') == 'pppoe' and
                                    is_physical_interface(current_interface.get('name', ''),
                                                        current_interface.get('type', ''),
                                                        current_interface)):
                                    filtered_pppoe_count += 1
                        current_interface = None

        # 添加最后一个接口（如果有）
        if current_interface:
            # 分析接口类型并设置必填属性
            analyze_and_set_interface_type(current_interface)

            # 过滤Modem接口（逻辑接口，不需要转换）
            interface_name = current_interface.get('name', '').lower()
            if interface_name == 'modem':
                log(_("info.modem_interface_filtered"), "info", interface=current_interface.get('name', ''))
            else:
                # 验证PPPoE接口完整性，只有验证通过的接口才添加到结果中
                if validate_pppoe_interface(current_interface):
                    interfaces.append(current_interface)
                else:
                    # 如果是被过滤的PPPoE接口，增加计数
                    if (current_interface.get('mode') == 'pppoe' and
                        is_physical_interface(current_interface.get('name', ''),
                                            current_interface.get('type', ''),
                                            current_interface)):
                        filtered_pppoe_count += 1
        
        # 将区域信息应用到接口
        if zones:
            # 创建接口名称到接口的映射
            interface_map = {intf['name']: intf for intf in interfaces}
            
            # 应用区域信息
            for zone in zones:
                zone_name = zone['name']
                for intf_name in zone['interfaces']:
                    if intf_name in interface_map:
                        interface_map[intf_name]['zone'] = zone_name
                        log(_("info.interface_zone_assigned"), interface=intf_name, zone=zone_name)
            
            log(_("info.zones_processing_complete"), processed=len(zones))
        
        # 创建结果字典
        interfaces_count = len(interfaces)
        log(_("info.extraction_success"), "info", count=interfaces_count)
        user_log(_("info.extraction_success"), "info", count=interfaces_count)

        # 如果有被过滤的PPPoE接口，记录相关信息
        if filtered_pppoe_count > 0:
            log(_("info.pppoe_interfaces_filtered"), "info", count=filtered_pppoe_count)
            user_log(_("info.pppoe_interfaces_filtered"), "info", count=filtered_pppoe_count)

        # 对接口数据进行密码掩码处理，保护敏感信息
        masked_interfaces = []
        for interface in interfaces:
            masked_interface = mask_interface_passwords(interface)
            masked_interfaces.append(masked_interface)

        result = {
            "success": True,
            "message": _("info.extraction_success", count=interfaces_count),
            "error": "",
            "interfaces": masked_interfaces,  # 使用掩码处理后的接口数据
            "count": interfaces_count,
            "zones_count": len(zones),
            "zones": zones,
            "filtered_pppoe_count": filtered_pppoe_count  # 添加过滤统计信息
        }
        
        return result
    except Exception as e:
        error_msg = _("error.extraction_failed", error=str(e))
        log(error_msg, "error")
        user_log(_("error.extraction_failed"), "error")
        # 返回统一的错误结构
        return {
            'success': False,
            'message': _("error.extraction_failed"),
            'error': str(e),
            'interfaces': [],
            'count': 0,
            'zones': [],
            'zones_count': 0
        }



def analyze_and_set_interface_type(interface):
    """
    分析接口配置并设置正确的接口类型和必填属性

    Args:
        interface (dict): 接口配置字典
    """
    interface_name = interface.get('name', '')
    
    # 根据完整配置重新判断是否为子接口
    is_sub = is_subinterface(interface.get('name', ''), interface)
    interface['is_subinterface'] = is_sub
    
    # 如果是子接口，设置相应的类型和属性
    if is_sub:
        # 如果是PPPoE子接口，设置类型为pppoe
        if interface.get('mode') == 'pppoe':
            interface['type'] = 'pppoe'
            # 设置父接口信息
            if 'interface' in interface:
                interface['parent_interface'] = interface['interface']
        else:
            # 其他子接口默认设置为vlan类型
            interface['type'] = 'vlan'
        
        interface['required'] = False
        interface['type_name'] = _("interface.type.subinterface")
        return
    
    # 使用配置中的type字段（如果有）
    if 'type' in interface:
        interface_type = interface['type']
    else:
        # 否则根据名称和配置内容推断类型
        if is_physical_interface(interface_name, None, interface):
            interface_type = 'physical'
        elif interface_name.lower().startswith('port'):
            interface_type = 'port'
        elif interface_name.lower().startswith('loopback'):
            interface_type = 'loopback'
        elif interface_name.lower().startswith('tunnel'):
            interface_type = 'tunnel'
        elif 'pppoe' in interface_name.lower() or interface.get('mode') == 'pppoe':
            interface_type = 'pppoe'
        else:
            interface_type = 'unknown'
        
        interface['type'] = interface_type
    
    # 判断是否是物理接口，并设置必填属性
    is_physical = is_physical_interface(interface_name, interface_type, interface)
    
    # 特殊处理：如果接口名称以dmz开头或角色为dmz，设置为必填项
    # NTOS系统只支持lan、wan，不支持dmz、undefined，但我们会将dmz转换为lan
    if interface_name.lower().startswith('dmz'):
        log(_("info.dmz_interface_set_required"), "info", interface=interface_name)
        # 添加警告信息
        log(_("fortigate.warning.unsupported_role"), "warning", interface=interface_name, role="dmz")
    elif interface.get('role', '').lower() == 'dmz':
        log(_("info.dmz_interface_set_required"), "info", interface=interface_name)
        # 添加警告信息
        log(_("fortigate.warning.unsupported_role"), "warning", interface=interface_name, role="dmz")
    elif interface.get('zone', '').lower() == 'undefined':
        log(_("info.undefined_zone_interface_set_required"), "info", interface=interface_name)
        # 添加警告信息
        log(_("fortigate.warning.unsupported_role"), "warning", interface=interface_name, role="undefined")
    elif interface.get('role', '').lower() == 'undefined':
        log(_("info.undefined_zone_interface_set_required"), "info", interface=interface_name)
        # 添加警告信息
        log(_("fortigate.warning.unsupported_role"), "warning", interface=interface_name, role="undefined")
    
    interface['required'] = is_physical
    
    # 设置接口类型名称（国际化）
    interface['type_name'] = get_interface_type_name(interface_type)

def format_extraction_result(result):
    """格式化提取结果为JSON字符串"""
    if result is None:
        # 提取失败
        result = {
            "success": False,
            "message": _("error.extraction_failed"),
            "error": _("error.extraction_failed"),
            "interfaces": [],
            "interfaces_count": 0,
            "count": 0,
            "zones": [],
            "zones_count": 0
        }
    else:
        # 确保接口是一个列表类型
        interfaces = result.get("interfaces", [])
        if not isinstance(interfaces, list):
            log(_("warning.interface_data_not_list"), "warning")
            interfaces = []
            result["interfaces"] = interfaces
        
        # 确保区域是一个列表类型
        zones = result.get("zones", [])
        if not isinstance(zones, list):
            log(_("warning.zone_data_not_list"), "warning")
            zones = []
            result["zones"] = zones
        
        # 确保结果包含所有必要字段
        if "message" not in result:
            result["message"] = _("info.extraction_success", count=len(interfaces))
        
        # 确保interfaces_count和count字段存在
        result["interfaces_count"] = len(interfaces)
        result["count"] = len(interfaces)
        
        # 确保zones_count字段存在
        result["zones_count"] = len(zones)
        
        # 确保接口信息完整性，补充可能缺失的字段
        for interface in interfaces:
            # 跳过非字典类型的接口对象
            if not isinstance(interface, dict):
                continue
                
            # 确保基本字段存在
            if "has_ip" not in interface:
                interface["has_ip"] = bool(interface.get("ip", "")) and interface.get("ip", "") != "NA"
            
            # 确保required字段存在
            if "required" not in interface:
                # 判断是否是物理接口
                interface["required"] = is_physical_interface(interface.get('name', ''), interface.get('type'), interface)
            
            # 确保is_subinterface字段存在
            if "is_subinterface" not in interface:
                interface["is_subinterface"] = is_subinterface(interface.get('name', ''))
            
            # 确保type_name字段存在
            if "type_name" not in interface:
                interface_type = interface.get('type', 'unknown')
                if interface.get('is_subinterface', False):
                    interface["type_name"] = _("interface.type.subinterface")
                else:
                    interface["type_name"] = get_interface_type_name(interface_type)
            
            # 确保status字段存在
            if "status" not in interface:
                interface["status"] = "unknown"
            
            # 确保所有值都是有效的JSON类型
            for key, value in list(interface.items()):
                if value is None:
                    interface[key] = ""
                elif not isinstance(value, (str, int, float, bool, list, dict)):
                    interface[key] = str(value)
        
        # 确保警告字段存在
        if "warnings" not in result:
            result["warnings"] = []
    
    # 移除可能包含敏感信息的字段
    if "internal_details" in result:
        del result["internal_details"]
    if "debug_info" in result:
        del result["debug_info"]
    
    return json.dumps(result, ensure_ascii=False, indent=2)

def safe_print_json(data):
    """安全地打印JSON数据，避免编码问题"""
    try:
        if data is None:
            # 如果数据为空，输出一个错误结果
            error_json = {
                "success": False,
                "message": _("error.extraction_failed"),
                "error": _("error.no_data"),
                "interfaces": [],
                "interfaces_count": 0,
                "count": 0
            }
            print(json.dumps(error_json, ensure_ascii=False, indent=2))
        elif isinstance(data, str):
            # 如果已经是字符串，尝试解析为JSON
            try:
                parsed = json.loads(data)
                print(json.dumps(parsed, ensure_ascii=False, indent=2))
            except:
                # 如果不是有效的JSON字符串，直接打印
                print(data)
        else:
            # 确保结果包含必要字段
            if isinstance(data, dict):
                # 确保接口是一个列表类型
                interfaces = data.get("interfaces", [])
                if not isinstance(interfaces, list):
                    interfaces = []
                
                # 确保包含所有必要字段
                result = {
                    "success": data.get("success", True),
                    "message": data.get("message", _("info.extraction_success", count=len(interfaces))),
                    "error": data.get("error", ""),
                    "interfaces": interfaces,
                    "interfaces_count": len(interfaces),
                    "count": len(interfaces)
                }
                print(json.dumps(result, ensure_ascii=False, indent=2))
            else:
                # 其他类型直接转换为JSON
                print(json.dumps(data, ensure_ascii=False, indent=2))
    except Exception as e:
        error_msg = _("error.json_print_failed", error=str(e))
        log(error_msg, "error")
        # 输出一个简单的错误JSON
        error_json = {
            "success": False,
            "message": _("error.json_print_failed", error=str(e)),
            "error": _("error.json_print_failed", error=str(e)),
            "interfaces": [],
            "interfaces_count": 0,
            "count": 0
        }
        print(json.dumps(error_json, ensure_ascii=False, indent=2))

def extract_config_interfaces(cli_file, vendor="fortigate", language="zh-CN", quiet=False):
    """提取配置文件中的接口信息
    
    Args:
        cli_file (str): 配置文件路径
        vendor (str): 厂商标识，当前支持: fortigate
        language (str): 国际化语言，默认为中文
        quiet (bool): 是否静默模式
        
    Returns:
        dict: 包含接口信息的字典
    """
    try:
        # 初始化国际化
        from engine.utils.i18n import init_i18n
        init_i18n(language)
        
        log(_("info.extract_interfaces_from_config"), file=cli_file)
        user_log(_("info.extract_interfaces_from_config"), file=cli_file, quiet=quiet)
        
        # 检查厂商是否支持
        vendor = vendor.lower()
        if vendor not in SUPPORTED_VENDORS:
            supported = list(SUPPORTED_VENDORS.keys())
            log(_("error.vendor_not_supported"), "error", vendor=vendor, supported=", ".join(supported))
            user_log(_("error.vendor_not_supported"), "error", quiet=quiet)
            return {
                "success": False,
                "error": _("error.vendor_not_supported", vendor=vendor, supported=", ".join(supported)),
                "interfaces": [],
                "count": 0
            }
            
        # 检查文件是否存在
        if not os.path.exists(cli_file):
            log(_("error.file_not_exists"), "error", file=cli_file)
            user_log(_("error.file_not_exists"), "error", quiet=quiet)
            return {
                "success": False,
                "error": _("error.file_not_exists", file=cli_file),
                "interfaces": [],
                "count": 0
            }
        
        # 检查文件是否为空
        if os.path.getsize(cli_file) == 0:
            log(_("error.file_empty"), "error")
            user_log(_("error.file_empty"), "error", quiet=quiet)
            return {
                "success": False,
                "error": _("error.file_empty"),
                "interfaces": [],
                "count": 0
            }
        
        # 如果是FortiGate，检查配置文件内容中的版本信息
        if vendor == "fortigate":
            with open(cli_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # 使用verify.py中的detect_fortigate_version函数检测版本
            from engine.verify import detect_fortigate_version
            version_detected, version_str, major, minor, patch = detect_fortigate_version(content)
            
            # 默认启用严格版本检测，如果无法检测到版本，直接返回错误
            if not version_detected:
                log(_("error.unable_to_detect_version"), "error")
                user_log(_("error.unable_to_detect_version"), "error", quiet=quiet)
                return {
                    "success": False,
                    "error": _("error.unable_to_detect_version"),
                    "interfaces": [],
                    "count": 0,
                    "version_error": True
                }
        
        # 根据厂商选择对应的提取函数
        extract_func_name = SUPPORTED_VENDORS[vendor]
        
        # 调用对应的提取函数
        result = globals()[extract_func_name](cli_file)
        
        # 返回结果
        return result
    
    except Exception as e:
        error_msg = _("error.extract_interfaces_failed", error=str(e))
        log(error_msg, "error")
        user_log(_("error.extract_interfaces_failed"), "error", quiet=quiet)
        
        return {
            "success": False,
            "error": _("error.extract_interfaces_failed", error=str(e)),
            "interfaces": [],
            "count": 0
        }

def init_loggers(log_dir, quiet=False):
    """初始化日志配置
    
    Args:
        log_dir (str): 日志目录路径
        quiet (bool): 是否静默模式
    """
    if not log_dir:
        return
    
    # 确保日志目录存在
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 设置日志
    from engine.utils.logger import setup_logging
    setup_logging(log_dir, True, logging.DEBUG, quiet)

# 命令行入口
if __name__ == "__main__":
    import argparse
    import sys
    
    # 获取支持的厂商列表
    supported_vendors = list(SUPPORTED_VENDORS.keys())
    
    # 初始化国际化
    from engine.utils.i18n import init_i18n
    init_i18n('zh-CN')  # 默认使用中文，后续会根据参数更新
    
    parser = argparse.ArgumentParser(description=_("description.interface_extraction_tool"))
    parser.add_argument('--cli', required=True, help=_("help.cli_file"))
    parser.add_argument('--output', help=_("help.output_file"))
    parser.add_argument('--vendor', default="fortigate", help=_("help.supported_vendors", vendors=", ".join(supported_vendors)))
    parser.add_argument('--lang', default='zh-CN', help=_("help.language"))
    parser.add_argument('--quiet', action='store_true', help=_("help.quiet_mode"))
    parser.add_argument('--print', action='store_true', help=_("help.print_result"))
    parser.add_argument('--log-dir', help=_("help.log_directory"))
    
    args = parser.parse_args()
    
    # 更新国际化设置
    init_i18n(args.lang)
    
    # 注意：默认启用严格版本检测，无需额外参数
    
    # 检查日志目录参数
    if args.log_dir:
        init_loggers(args.log_dir, args.quiet)
    
    # 检查厂商是否支持
    if args.vendor.lower() not in SUPPORTED_VENDORS:
        supported = list(SUPPORTED_VENDORS.keys())
        error_json = {
            "success": False,
            "error": _("error.vendor_not_supported", vendor=args.vendor, supported=", ".join(supported)),
            "interfaces": [],
            "count": 0
        }
        
        if args.print:
            print(json.dumps(error_json, ensure_ascii=False, indent=2))
        sys.exit(0)  # 使用0状态码，但返回错误信息
    
    # 提取接口信息
    result = extract_config_interfaces(args.cli, args.vendor, args.lang, args.quiet)
    
    # 将结果写入输出文件（如果指定了输出文件）
    if result.get("success") and args.output:
        try:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            log(_("info.interface_info_written"), "info", file=args.output)
            if not args.quiet:
                user_log(_("info.interface_info_written"), "info", file=args.output)
        except Exception as e:
            error_msg = _("error.write_interface_info_failed", error=str(e))
            log(error_msg, "error")
            if not args.quiet:
                user_error_msg = _("error.write_interface_info_failed", error=str(e))
                user_log(user_error_msg, "error")
    
    # 输出结果到标准输出
    if args.print:
        print(json.dumps(result, ensure_ascii=False, indent=2))
    
    # 返回成功与否的状态码
    sys.exit(0)  # 即使执行失败，也返回0，错误信息已经在JSON中