#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
桥接接口生成器
负责生成NTOS桥接接口XML配置
"""

from lxml import etree
from engine.utils.logger import log, _
from engine.utils.i18n import _

# 命名空间常量
NS_BRIDGE = "urn:ruijie:ntos:params:xml:ns:yang:bridge"
NS_INTERFACE = "urn:ruijie:ntos:params:xml:ns:yang:interface"
NS_MONITOR = "urn:ruijie:ntos:params:xml:ns:yang:if-mon"
NS_ACCESS = "urn:ruijie:ntos:params:xml:ns:yang:local-defend"


class BridgeInterfaceGenerator:
    """桥接接口生成器类"""
    
    def __init__(self):
        """初始化桥接接口生成器"""
        log(_("bridge_generator.initialized"))
    
    def create_bridge_interface(self, interface_elem, bridge_config):
        """
        创建桥接接口XML配置
        
        Args:
            interface_elem: 父接口元素
            bridge_config (dict): 桥接配置数据
                {
                    "name": "br0",
                    "enabled": True,
                    "slave_interfaces": ["Ge0/1", "Ge0/2", ...],
                    "mgmt_ip": "*************/*************",  # 可选
                    "access_control": {  # 可选
                        "https": True,
                        "ping": True,
                        "ssh": False
                    }
                }
                
        Returns:
            Element: 创建的桥接接口元素
        """
        bridge_name = bridge_config.get("name", "br0")
        slave_interfaces = bridge_config.get("slave_interfaces", [])
        
        log(_("bridge_generator.creating_bridge", 
              name=bridge_name, slave_count=len(slave_interfaces)))
        
        # 创建bridge节点
        bridge = etree.SubElement(interface_elem, "bridge")
        bridge.set("xmlns", NS_BRIDGE)
        
        # 设置桥接接口名称
        etree.SubElement(bridge, "name").text = bridge_name
        
        # 设置启用状态
        enabled = bridge_config.get("enabled", True)
        etree.SubElement(bridge, "enabled").text = "true" if enabled else "false"
        
        # 配置IPv4
        self._configure_bridge_ipv4(bridge, bridge_config)
        
        # 配置IPv6
        self._configure_bridge_ipv6(bridge, bridge_config)
        
        # 配置网络栈参数
        self._configure_network_stack(bridge)
        
        # 添加slave接口
        self._add_slave_interfaces(bridge, slave_interfaces)
        
        # 配置snooping
        self._configure_snooping(bridge)
        
        # 配置session源检查
        self._configure_session_source_check(bridge)
        
        # 配置监控
        self._configure_monitor(bridge)
        
        # 配置访问控制（如果指定）
        access_control = bridge_config.get("access_control")
        if access_control:
            self._configure_access_control(bridge, access_control)
        
        log(_("bridge_generator.bridge_created", name=bridge_name))
        return bridge
    
    def _configure_bridge_ipv4(self, bridge_elem, bridge_config):
        """
        配置桥接接口的IPv4设置
        """
        ipv4 = etree.SubElement(bridge_elem, "ipv4")
        etree.SubElement(ipv4, "enabled").text = "true"
        
        # 检查是否有管理IP配置
        mgmt_ip = bridge_config.get("mgmt_ip")
        if mgmt_ip:
            # 如果有管理IP，配置静态地址
            address = etree.SubElement(ipv4, "address")
            etree.SubElement(address, "ip").text = mgmt_ip
            log(_("bridge_generator.mgmt_ip_configured", ip=mgmt_ip))
        else:
            # 默认配置DHCP
            dhcp = etree.SubElement(ipv4, "dhcp")
            etree.SubElement(dhcp, "enabled").text = "true"
            etree.SubElement(dhcp, "timeout").text = "60"
            etree.SubElement(dhcp, "retry").text = "30"
            etree.SubElement(dhcp, "select-timeout").text = "0"
            etree.SubElement(dhcp, "reboot").text = "10"
            etree.SubElement(dhcp, "initial-interval").text = "10"
            etree.SubElement(dhcp, "dhcp-lease-time").text = "7200"
            
            # DHCP请求选项
            dhcp_requests = [
                "subnet-mask", "broadcast-address", "time-offset", "routers",
                "domain-name", "domain-search", "domain-name-servers", 
                "host-name", "nis-domain"
            ]
            for request in dhcp_requests:
                etree.SubElement(dhcp, "request").text = request
    
    def _configure_bridge_ipv6(self, bridge_elem, bridge_config):
        """
        配置桥接接口的IPv6设置
        """
        ipv6 = etree.SubElement(bridge_elem, "ipv6")
        etree.SubElement(ipv6, "enabled").text = "true"
    
    def _configure_network_stack(self, bridge_elem):
        """
        配置网络栈参数
        """
        network_stack = etree.SubElement(bridge_elem, "network-stack")
        ipv4_ns = etree.SubElement(network_stack, "ipv4")
        etree.SubElement(ipv4_ns, "arp-ignore").text = "check-interface-and-subnet"
    
    def _add_slave_interfaces(self, bridge_elem, slave_interfaces):
        """
        添加slave接口到桥接接口
        
        Args:
            bridge_elem: 桥接接口元素
            slave_interfaces (list): slave接口名称列表
        """
        for slave_name in slave_interfaces:
            link_interface = etree.SubElement(bridge_elem, "link-interface")
            etree.SubElement(link_interface, "slave").text = slave_name
            log(_("bridge_generator.slave_added", slave=slave_name))
    
    def _configure_snooping(self, bridge_elem):
        """
        配置snooping设置
        """
        snooping = etree.SubElement(bridge_elem, "snooping")
        etree.SubElement(snooping, "trust").text = "false"
        etree.SubElement(snooping, "suppress").text = "false"
    
    def _configure_session_source_check(self, bridge_elem):
        """
        配置session源检查
        """
        etree.SubElement(bridge_elem, "session-source-check").text = "dont-check"
    
    def _configure_monitor(self, bridge_elem):
        """
        配置监控设置
        """
        monitor = etree.SubElement(bridge_elem, "monitor")
        monitor.set("xmlns", NS_MONITOR)
        
        etree.SubElement(monitor, "notify-up-drop-rate-threshold").text = "1000"
        etree.SubElement(monitor, "notify-down-drop-rate-threshold").text = "1000"
        etree.SubElement(monitor, "if-notify-enable").text = "false"
        etree.SubElement(monitor, "notify-up-usage-threshold").text = "100"
        etree.SubElement(monitor, "notify-down-usage-threshold").text = "100"
        etree.SubElement(monitor, "notify-up-speed-threshold").text = "100000000"
        etree.SubElement(monitor, "notify-down-speed-threshold").text = "100000000"
    
    def _configure_access_control(self, bridge_elem, access_control):
        """
        配置访问控制设置
        
        Args:
            bridge_elem: 桥接接口元素
            access_control (dict): 访问控制配置
        """
        access_elem = etree.SubElement(bridge_elem, "access-control")
        access_elem.set("xmlns", NS_ACCESS)
        
        # 设置各种访问控制选项
        https_enabled = access_control.get("https", True)
        ping_enabled = access_control.get("ping", True)
        ssh_enabled = access_control.get("ssh", False)
        
        etree.SubElement(access_elem, "https").text = "true" if https_enabled else "false"
        etree.SubElement(access_elem, "ping").text = "true" if ping_enabled else "false"
        etree.SubElement(access_elem, "ssh").text = "true" if ssh_enabled else "false"
        
        log(_("bridge_generator.access_control_configured", 
              https=https_enabled, ping=ping_enabled, ssh=ssh_enabled))
    
    def create_bridge_from_transparent_result(self, interface_elem, transparent_result):
        """
        根据透明模式处理结果创建桥接接口
        
        Args:
            interface_elem: 父接口元素
            transparent_result (dict): 透明模式处理结果
            
        Returns:
            Element: 创建的桥接接口元素，如果没有桥接接口则返回None
        """
        if not transparent_result:
            return None
        
        bridge_config_data = transparent_result.get("bridge_config", {})
        if not bridge_config_data:
            return None
        
        # 构建桥接配置
        bridge_config = {
            "name": bridge_config_data.get("name", "br0"),
            "enabled": bridge_config_data.get("enabled", True),
            "slave_interfaces": bridge_config_data.get("slave_interfaces", []),
            "access_control": {
                "https": True,
                "ping": True,
                "ssh": False
            }
        }
        
        # 如果有管理IP，不在桥接接口上配置（管理IP配置在MGMT物理接口上）
        # 桥接接口使用DHCP
        
        return self.create_bridge_interface(interface_elem, bridge_config)


def create_bridge_interface_generator():
    """
    工厂函数：创建桥接接口生成器实例
    
    Returns:
        BridgeInterfaceGenerator: 桥接接口生成器实例
    """
    return BridgeInterfaceGenerator()


# 测试函数
def test_bridge_interface_generator():
    """测试桥接接口生成器功能"""
    print("=== 桥接接口生成器测试 ===")
    
    # 创建生成器
    generator = BridgeInterfaceGenerator()
    
    # 创建测试用的interface元素
    root = etree.Element("interface")
    root.set("xmlns", NS_INTERFACE)
    
    # 测试桥接配置
    bridge_config = {
        "name": "br0",
        "enabled": True,
        "slave_interfaces": ["Ge0/1", "Ge0/2", "Ge0/3", "Ge0/4"],
        "access_control": {
            "https": True,
            "ping": True,
            "ssh": False
        }
    }
    
    # 创建桥接接口
    bridge_elem = generator.create_bridge_interface(root, bridge_config)
    
    # 输出XML
    xml_str = etree.tostring(root, pretty_print=True, encoding='unicode')
    print("生成的桥接接口XML:")
    print(xml_str)


if __name__ == "__main__":
    test_bridge_interface_generator()
