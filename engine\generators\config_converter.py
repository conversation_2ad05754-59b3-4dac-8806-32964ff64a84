from lxml import etree
import os
import json
import copy
from engine.utils.logger import log
from engine.utils.i18n import _  # 导入国际化函数

# 导入所有模块
from engine.generators.yang_generator import generate_xml
from engine.generators.yang_generator_extensions import (
    generate_advanced_xml, validate_config, merge_configs, compare_configs
)
from engine.generators.ntos_generator import NTOSGenerator
from engine.generators.ntos_generator_extensions import (
    ExtendedNTOSGenerator, create_ntos_generator
)

class ConfigConverter:
    """配置转换器，整合所有功能"""
    
    def __init__(self, model=None, version=None):
        """
        初始化配置转换器
        
        Args:
            model (str, optional): 设备型号
            version (str, optional): 设备版本
        """
        self.model = model
        self.version = version
        self.config_data = {}
        self.template_path = None
        self.output_path = None
        log(_("config_converter.init", model=model, version=version))
    
    def set_model_version(self, model, version):
        """
        设置设备型号和版本
        
        Args:
            model (str): 设备型号
            version (str): 设备版本
        """
        self.model = model
        self.version = version
        log(_("config_converter.set_model_version", model=model, version=version))
    
    def set_template(self, template_path):
        """
        设置模板路径
        
        Args:
            template_path (str): 模板文件路径
        """
        self.template_path = template_path
        log(_("config_converter.set_template", path=template_path))
    
    def set_output(self, output_path):
        """
        设置输出路径
        
        Args:
            output_path (str): 输出文件路径
        """
        self.output_path = output_path
        log(_("config_converter.set_output", path=output_path))
    
    def load_config(self, config_path=None, config_data=None):
        """
        加载配置数据
        
        Args:
            config_path (str, optional): 配置文件路径
            config_data (dict, optional): 配置数据字典
            
        Returns:
            bool: 是否成功加载
        """
        if config_data:
            self.config_data = copy.deepcopy(config_data)
            log(_("config_converter.load_config_from_dict"))
            return True
        
        if config_path:
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    self.config_data = json.load(f)
                log(_("config_converter.load_config_from_file", path=config_path))
                return True
            except Exception as e:
                error_msg = _("config_converter.error.load_config_failed", error=str(e))
                log(error_msg, "error")
                return False
        
        log(_("config_converter.error.no_config_provided"), "error")
        return False
    
    def validate(self):
        """
        验证配置数据
        
        Returns:
            tuple: (是否有效, 错误信息列表)
        """
        return validate_config(self.config_data)
    
    def convert_to_xml(self, use_advanced=False):
        """
        将配置数据转换为XML
        
        Args:
            use_advanced (bool): 是否使用高级XML生成
            
        Returns:
            str: 生成的XML字符串
        """
        if not self.config_data:
            log(_("config_converter.error.no_config_data"), "error")
            return None
        
        # 验证配置
        is_valid, errors = self.validate()
        if not is_valid:
            log(_("config_converter.error.invalid_config"), "error")
            for error in errors:
                log(error, "error")
            return None
        
        # 生成XML
        try:
            if use_advanced:
                xml_string = generate_advanced_xml(self.config_data, self.template_path)
            else:
                xml_string = generate_xml(self.config_data, self.template_path)
            
            log(_("config_converter.xml_generation_success"))
            return xml_string
        except Exception as e:
            error_msg = _("config_converter.error.xml_generation_failed", error=str(e))
            log(error_msg, "error")
            return None
    
    def convert_to_ntos(self, use_extended=False, custom_template=None):
        """
        将配置数据转换为NTOS格式
        
        Args:
            use_extended (bool): 是否使用扩展NTOS生成器
            custom_template (str, optional): 自定义模板路径
            
        Returns:
            tuple: (XML配置内容, 转换统计信息)
        """
        if not self.model or not self.version:
            log(_("config_converter.error.model_version_not_set"), "error")
            return None, {}
        
        if not self.config_data:
            log(_("config_converter.error.no_config_data"), "error")
            return None, {}
        
        # 创建NTOS生成器
        generator = create_ntos_generator(
            self.model, 
            self.version, 
            use_extended=use_extended, 
            custom_template=custom_template or self.template_path
        )
        
        # 添加接口配置
        if "interfaces" in self.config_data:
            generator.add_interfaces(self.config_data["interfaces"])
        
        # 添加地址对象
        if "address_objects" in self.config_data:
            generator.add_address_objects(self.config_data["address_objects"])
        
        # 添加服务对象
        if "service_objects" in self.config_data:
            generator.add_service_objects(self.config_data["service_objects"])
        
        # 添加策略规则
        if "policy_rules" in self.config_data:
            generator.add_policy_rules(self.config_data["policy_rules"])
        
        # 添加区域配置
        if "security_zones" in self.config_data:
            generator.add_zones(self.config_data["security_zones"])
        
        # 添加静态路由配置
        if "static_routes" in self.config_data:
            generator.add_static_routes(self.config_data["static_routes"])
        
        # 添加地址组
        if "address_groups" in self.config_data:
            generator.add_address_groups(self.config_data["address_groups"])
        
        # 添加服务组
        if "service_groups" in self.config_data:
            generator.add_service_groups(self.config_data["service_groups"])
        
        # 如果是扩展生成器，添加扩展配置
        if use_extended and isinstance(generator, ExtendedNTOSGenerator):
            # 添加NAT规则
            if "nat_rules" in self.config_data:
                generator.add_nat_rules(self.config_data["nat_rules"])

            # 添加VPN隧道
            if "vpn_tunnels" in self.config_data:
                generator.add_vpn_tunnels(self.config_data["vpn_tunnels"])

            # 添加DHCP服务器
            if "dhcp_servers" in self.config_data:
                generator.add_dhcp_servers(self.config_data["dhcp_servers"])

            # 添加DNS配置
            if "dns_config" in self.config_data and self.config_data["dns_config"]:
                generator.add_dns_config(self.config_data["dns_config"])
                log(_("config_converter.added_dns_config"))
        
        # 生成配置
        xml_content, stats = generator.generate()
        
        # 保存配置（如果指定了输出路径）
        if self.output_path and xml_content:
            try:
                with open(self.output_path, 'w', encoding='utf-8') as f:
                    f.write(xml_content)
                log(_("config_converter.save_xml_success", path=self.output_path))
            except Exception as e:
                error_msg = _("config_converter.error.save_xml_failed", error=str(e))
                log(error_msg, "error")
        
        return xml_content, stats
    
    def merge_with(self, other_config):
        """
        与另一个配置合并
        
        Args:
            other_config (dict): 要合并的配置数据
            
        Returns:
            ConfigConverter: 合并后的配置转换器
        """
        merged_config = merge_configs(self.config_data, other_config)
        
        # 创建新的转换器
        merged_converter = ConfigConverter(self.model, self.version)
        merged_converter.load_config(config_data=merged_config)
        merged_converter.template_path = self.template_path
        merged_converter.output_path = self.output_path
        
        return merged_converter
    
    def compare_with(self, other_config):
        """
        与另一个配置比较差异
        
        Args:
            other_config (dict): 要比较的配置数据
            
        Returns:
            dict: 差异信息
        """
        return compare_configs(self.config_data, other_config)
    
    def save_config(self, output_path=None):
        """
        保存配置数据到JSON文件
        
        Args:
            output_path (str, optional): 输出文件路径
            
        Returns:
            bool: 是否成功保存
        """
        path = output_path or self.output_path
        if not path:
            log(_("config_converter.error.no_output_path"), "error")
            return False
        
        try:
            with open(path, 'w', encoding='utf-8') as f:
                json.dump(self.config_data, f, indent=2, ensure_ascii=False)
            log(_("config_converter.save_config_success", path=path))
            return True
        except Exception as e:
            error_msg = _("config_converter.error.save_config_failed", error=str(e))
            log(error_msg, "error")
            return False

# 工厂函数 - 创建配置转换器
def create_converter(model=None, version=None, config_path=None, template_path=None, output_path=None):
    """
    创建配置转换器
    
    Args:
        model (str, optional): 设备型号
        version (str, optional): 设备版本
        config_path (str, optional): 配置文件路径
        template_path (str, optional): 模板文件路径
        output_path (str, optional): 输出文件路径
        
    Returns:
        ConfigConverter: 配置转换器实例
    """
    converter = ConfigConverter(model, version)
    
    if template_path:
        converter.set_template(template_path)
    
    if output_path:
        converter.set_output(output_path)
    
    if config_path:
        converter.load_config(config_path)
    
    return converter

# 如果直接运行此文件，执行测试
if __name__ == "__main__":
    # 创建测试配置
    test_config = {
        "interfaces": [
            {
                "name": "Ge0/0",
                "status": "up",
                "ip": "***********/24",
                "role": "lan"
            }
        ],
        "static_routes": [
            {
                "destination": "0.0.0.0/0",
                "gateway": "*************"
            }
        ],
        "security_zones": [
            {
                "name": "trust",
                "description": "Trust Zone",
                "priority": 85,
                "interfaces": ["Ge0/0"]
            }
        ]
    }
    
    # 创建配置转换器
    converter = ConfigConverter("z5100s", "R10P2")
    converter.load_config(config_data=test_config)
    
    # 验证配置
    is_valid, errors = converter.validate()
    print(f"配置有效性: {is_valid}")
    if not is_valid:
        for error in errors:
            print(f"错误: {error}")
    
    # 生成XML
    xml_output = converter.convert_to_xml()
    if xml_output:
        print("生成的XML:")
        print(xml_output)
    
    # 生成NTOS配置
    ntos_xml, stats = converter.convert_to_ntos(use_extended=True)
    if ntos_xml:
        print("\nNTOS配置:")
        print(ntos_xml)
        
        print("\n转换统计信息:")
        for category, info in stats.items():
            print(f"{category}: {info}") 