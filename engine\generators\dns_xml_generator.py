#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
DNS XML生成器模块
专门处理飞塔DNS配置转换为NTOS DNS XML格式
"""

from lxml import etree
from engine.utils.logger import log
from engine.utils.i18n import _


class DNSXMLGenerator:
    """DNS XML生成器类"""
    
    def __init__(self):
        """初始化DNS XML生成器"""
        self.dns_namespace = "urn:ruijie:ntos:params:xml:ns:yang:dns"
        log(_("dns_generator.init_success"))
    
    def generate_dns_xml(self, dns_config):
        """
        生成DNS配置的XML
        
        Args:
            dns_config (dict): DNS配置字典，包含primary、secondary、dns_over_tls等字段
            
        Returns:
            etree.Element: DNS XML元素
        """
        if not dns_config:
            log(_("dns_generator.warning.empty_config"), "warning")
            return None
        
        # 创建DNS根元素
        dns_elem = etree.Element("dns")
        dns_elem.set("xmlns", self.dns_namespace)
        
        # 收集DNS服务器地址
        dns_servers = []
        if dns_config.get("primary"):
            dns_servers.append(dns_config["primary"])
        if dns_config.get("secondary"):
            dns_servers.append(dns_config["secondary"])
        if dns_config.get("tertiary"):
            dns_servers.append(dns_config["tertiary"])
        
        # 限制最多3个DNS服务器（NTOS限制）
        if len(dns_servers) > 3:
            log(_("dns_generator.warning.servers_exceed_limit", 
                  count=len(dns_servers), limit=3), "warning")
            dns_servers = dns_servers[:3]
        
        # 生成server节点
        for server_addr in dns_servers:
            if server_addr and server_addr.strip():
                server_elem = etree.SubElement(dns_elem, "server")
                address_elem = etree.SubElement(server_elem, "address")
                address_elem.text = server_addr.strip()
                log(_("dns_generator.added_server", address=server_addr.strip()))
        
        # 生成proxy节点（基于dns-over-tls设置）
        proxy_elem = etree.SubElement(dns_elem, "proxy")
        enabled_elem = etree.SubElement(proxy_elem, "enabled")
        
        # 判断dns-over-tls设置
        dns_over_tls = dns_config.get("dns_over_tls", "disable").lower()
        protocol = dns_config.get("protocol", "").lower()
        
        # 如果设置了dns-over-tls enable或protocol为dot，则启用proxy
        if dns_over_tls == "enable" or protocol == "dot":
            enabled_elem.text = "true"
            log(_("dns_generator.proxy_enabled"))
        else:
            enabled_elem.text = "false"
            log(_("dns_generator.proxy_disabled"))
        
        log(_("dns_generator.xml_generated", servers=len(dns_servers)))
        return dns_elem
    
    def integrate_dns_to_xml(self, root, dns_config):
        """
        将DNS配置集成到现有的XML根节点中
        
        Args:
            root (etree.Element): XML根节点
            dns_config (dict): DNS配置字典
            
        Returns:
            bool: 是否成功集成
        """
        try:
            # 查找VRF节点，尝试多种方式
            vrf = root.find(".//vrf")
            if vrf is None:
                # 尝试查找带命名空间的VRF节点
                vrf = root.find(".//{urn:ruijie:ntos}vrf")
            if vrf is None:
                # 如果还是找不到，直接使用根节点
                log(_("dns_generator.warning.vrf_not_found_using_root"), "warning")
                vrf = root

            # 查找现有的DNS节点，尝试多种方式
            existing_dns = vrf.find(".//dns[@xmlns='{}']".format(self.dns_namespace))
            if existing_dns is None:
                # 尝试直接查找DNS节点
                existing_dns = vrf.find("./dns[@xmlns='{}']".format(self.dns_namespace))
            if existing_dns is None:
                # 尝试不带命名空间的查找
                existing_dns = vrf.find(".//dns")
            if existing_dns is None:
                # 尝试使用完整的命名空间标签查找
                dns_with_ns = vrf.find("./{{{}}}dns".format(self.dns_namespace))
                if dns_with_ns is not None:
                    existing_dns = dns_with_ns
                else:
                    # 尝试直接遍历子节点查找
                    for child in vrf:
                        if child.tag.endswith('}dns') and child.get('xmlns') == self.dns_namespace:
                            existing_dns = child
                            break
            
            if existing_dns is not None:
                # 如果DNS节点已存在，更新其内容
                log(_("dns_generator.updating_existing_dns"))

                # 收集DNS服务器地址
                dns_servers = []
                if dns_config.get("primary"):
                    dns_servers.append(dns_config["primary"])
                if dns_config.get("secondary"):
                    dns_servers.append(dns_config["secondary"])
                if dns_config.get("tertiary"):
                    dns_servers.append(dns_config["tertiary"])

                # 限制最多3个DNS服务器（NTOS限制）
                if len(dns_servers) > 3:
                    log(_("dns_generator.warning.servers_exceed_limit",
                          count=len(dns_servers), limit=3), "warning")
                    dns_servers = dns_servers[:3]

                # 更新server节点：先移除所有现有server，再添加新的
                for server in existing_dns.findall("./server"):
                    existing_dns.remove(server)
                # 移除所有可能的server节点（包括带命名空间的）
                for child in list(existing_dns):
                    if child.tag.endswith('server') or child.tag == 'server':
                        existing_dns.remove(child)

                # 添加新的server节点
                for server_addr in dns_servers:
                    if server_addr and server_addr.strip():
                        server_elem = etree.SubElement(existing_dns, "server")
                        address_elem = etree.SubElement(server_elem, "address")
                        address_elem.text = server_addr.strip()
                        log(_("dns_generator.added_server", address=server_addr.strip()))

                # 更新proxy节点：查找现有proxy节点并更新其enabled值
                proxy_elem = existing_dns.find("./proxy")
                if proxy_elem is None:
                    # 查找所有可能的proxy节点（包括带命名空间的）
                    for child in existing_dns:
                        if child.tag.endswith('proxy') or child.tag == 'proxy':
                            proxy_elem = child
                            break

                if proxy_elem is not None:
                    # 找到现有proxy节点，更新enabled值
                    enabled_elem = proxy_elem.find("./enabled")
                    if enabled_elem is None:
                        # 查找所有可能的enabled节点（包括带命名空间的）
                        for child in proxy_elem:
                            if child.tag.endswith('enabled') or child.tag == 'enabled':
                                enabled_elem = child
                                break

                    # 保持proxy节点的默认值，不做修改
                    # 注释：由于NTOS系统和飞塔在DNS over TLS功能上存在较大差异，
                    # 暂时保持NTOS模板的默认proxy设置，后续版本将重新梳理调整
                    log(_("dns_generator.proxy_kept_default"))
                else:
                    # 如果没有proxy节点，不创建新的proxy节点
                    # 注释：由于NTOS系统和飞塔在DNS over TLS功能上存在较大差异，
                    # 暂时不处理proxy设置，后续版本将重新梳理调整
                    log(_("dns_generator.proxy_not_found"))

                log(_("dns_generator.dns_updated"))
            else:
                # 如果DNS节点不存在，创建新的DNS节点
                log(_("dns_generator.creating_new_dns"))
                new_dns_elem = self.generate_dns_xml(dns_config)
                if new_dns_elem is not None:
                    vrf.append(new_dns_elem)
                    log(_("dns_generator.dns_created"))
            
            return True
            
        except Exception as e:
            log(_("dns_generator.error.integration_failed", error=str(e)), "error")
            return False
    
    def validate_dns_config(self, dns_config):
        """
        验证DNS配置的有效性
        
        Args:
            dns_config (dict): DNS配置字典
            
        Returns:
            tuple: (是否有效, 错误信息列表)
        """
        errors = []
        
        if not dns_config:
            errors.append(_("dns_generator.validation.empty_config"))
            return False, errors
        
        # 检查是否至少有一个DNS服务器
        has_server = False
        for key in ["primary", "secondary", "tertiary"]:
            if dns_config.get(key) and dns_config[key].strip():
                has_server = True
                break
        
        if not has_server:
            errors.append(_("dns_generator.validation.no_servers"))
        
        # 验证DNS服务器地址格式（简单验证）
        import re
        ip_pattern = r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$'
        
        for key in ["primary", "secondary", "tertiary"]:
            server_addr = dns_config.get(key)
            if server_addr and server_addr.strip():
                if not re.match(ip_pattern, server_addr.strip()):
                    errors.append(_("dns_generator.validation.invalid_ip", 
                                   field=key, ip=server_addr.strip()))
        
        # 验证dns_over_tls字段
        dns_over_tls = dns_config.get("dns_over_tls")
        if dns_over_tls and dns_over_tls.lower() not in ["enable", "disable"]:
            errors.append(_("dns_generator.validation.invalid_dns_over_tls", 
                           value=dns_over_tls))
        
        return len(errors) == 0, errors


def create_dns_xml_generator():
    """
    工厂函数：创建DNS XML生成器实例
    
    Returns:
        DNSXMLGenerator: DNS XML生成器实例
    """
    return DNSXMLGenerator()


# 便捷函数
def generate_dns_xml_from_config(dns_config):
    """
    从DNS配置生成XML元素的便捷函数
    
    Args:
        dns_config (dict): DNS配置字典
        
    Returns:
        etree.Element: DNS XML元素
    """
    generator = create_dns_xml_generator()
    return generator.generate_dns_xml(dns_config)


def integrate_dns_config_to_xml(root, dns_config):
    """
    将DNS配置集成到XML根节点的便捷函数
    
    Args:
        root (etree.Element): XML根节点
        dns_config (dict): DNS配置字典
        
    Returns:
        bool: 是否成功集成
    """
    generator = create_dns_xml_generator()
    return generator.integrate_dns_to_xml(root, dns_config)


# 如果直接运行此文件，执行测试
if __name__ == "__main__":
    # 测试DNS XML生成
    test_dns_config = {
        "primary": "223.5.5.5",
        "secondary": "114.114.114.114",
        "dns_over_tls": "disable",
        "protocol": ""
    }
    
    generator = create_dns_xml_generator()
    
    # 验证配置
    is_valid, errors = generator.validate_dns_config(test_dns_config)
    print(f"配置验证结果: {is_valid}")
    if not is_valid:
        for error in errors:
            print(f"错误: {error}")
    
    # 生成XML
    dns_xml = generator.generate_dns_xml(test_dns_config)
    if dns_xml is not None:
        xml_str = etree.tostring(dns_xml, encoding='utf-8', pretty_print=True).decode('utf-8')
        print("生成的DNS XML:")
        print(xml_str)
    else:
        print("DNS XML生成失败")
