#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import re
from lxml import etree
from typing import Dict, Any, Optional, Tuple
from engine.utils.logger import log
from engine.utils.i18n import _
from engine.utils.yang_model_parser import Yang<PERSON><PERSON>lPars<PERSON>, YangValidationContext

class EnhancedYangGenerator:
    """
    增强的YANG生成器，集成实时验证和YANG模型驱动的XML生成
    
    核心原则：
    1. 大体上尽可能的使用模板上的信息
    2. 如果节点已存在 → 修改字段  
    3. 如果节点不存在 → 插入新的完整块
    4. 基于YANG模型进行实时验证
    5. 动态命名空间管理
    """
    
    def __init__(self, model: str, version: str):
        """
        初始化增强的YANG生成器
        
        Args:
            model (str): 设备型号，如 'z5100s'
            version (str): 设备版本，如 'R10P2'
        """
        self.model = model
        self.version = version
        
        # 初始化YANG模型解析器
        self.yang_parser = YangModelParser(model, version)
        self.yang_schema = self.yang_parser.parse_all_models()
        
        # 初始化验证上下文
        self.validation_context = YangValidationContext(self.yang_parser)
        
        # 缓存命名空间映射
        self.namespace_map = self.yang_parser.get_required_namespaces()
        
        log(_("enhanced_yang_generator.init", model=model, version=version))
    
    def generate_xml(self, config_data: Dict[str, Any], 
                    template_path: Optional[str] = None) -> Tuple[str, Dict[str, Any]]:
        """
        生成XML配置（与现有接口兼容）
        
        Args:
            config_data (Dict[str, Any]): 配置数据
            template_path (Optional[str]): 模板路径（可选）
            
        Returns: Tuple[str, Dict[str, Any]]: XML内容和验证报告
        """
        return self.generate_xml_with_validation(config_data, template_path)
    
    def generate_xml_with_validation(self, config_data: Dict[str, Any], 
                                   template_path: Optional[str] = None) -> Tuple[str, Dict[str, Any], str]:
        """
        生成带验证的XML配置 - 实现11步骤流程
        
        Args:
            config_data (Dict[str, Any]): 配置数据
            template_path (Optional[str]): 模板文件路径
            
        Returns: Tuple[str, Dict[str, Any], str]: (XML字符串, 验证报告, 验证状态)
        """
        log(_("enhanced_yang_generator.start_generation"))
        
        # 步骤1-3: 清除之前的验证结果，加载模板，处理配置数据
        self.validation_context.clear_validation_results()
        root = self._load_or_create_base_xml(template_path)
        self._process_config_data(root, config_data)
        
        # 步骤4-5: 生成XML并进行内部验证
        xml_string = self._generate_final_xml(root)
        validation_report = self.validation_context.get_validation_summary()
        
        # 步骤6: 集成yanglint验证（如果可用）
        yanglint_status = "not_available"
        try:
            from engine.utils.yang_validator import validate_with_yanglint, check_yanglint_available
            
            if check_yanglint_available():
                # 创建临时文件进行验证
                import tempfile
                with tempfile.NamedTemporaryFile(mode='w', suffix='.xml', delete=False, encoding='utf-8') as tmp_file:
                    tmp_file.write(xml_string)
                    tmp_file_path = tmp_file.name
                
                try:
                    # 执行yanglint验证
                    yanglint_result, yanglint_message = validate_with_yanglint(
                        tmp_file_path, model=self.model, version=self.version)
                    
                    if yanglint_result:
                        yanglint_status = "passed"
                        log(_("enhanced_yang_generator.yanglint_validation_passed"))
                    else:
                        yanglint_status = "failed"
                        validation_report["yanglint_errors"] = yanglint_message
                        log(_("enhanced_yang_generator.yanglint_validation_failed",
                              message=yanglint_message), "warning")
                        
                finally:
                    # 清理临时文件
                    if os.path.exists(tmp_file_path):
                        os.unlink(tmp_file_path)
            else:
                log(_("enhanced_yang_generator.yanglint_not_available"), "warning")
                
        except Exception as e:
            log(_("enhanced_yang_generator.yanglint_error", error=str(e)), "warning")
        
        # 更新验证报告
        validation_report["yanglint_status"] = yanglint_status
        
        log(_("enhanced_yang_generator.generation_complete", 
             errors=validation_report["error_count"],
             warnings=validation_report["warning_count"],
             yanglint_status=yanglint_status))
        
        return xml_string, validation_report, yanglint_status
    
    def generate_complete_config_package(self, config_data: Dict[str, Any], 
                                       template_path: Optional[str] = None,
                                       output_xml_path: Optional[str] = None,
                                       encrypt_output_path: Optional[str] = None) -> Tuple[str, Dict[str, Any]]:
        """
        生成完整的配置包 - 实现步骤7-11的完整流程
        
        Args:
            config_data (Dict[str, Any]): 配置数据
            template_path (Optional[str]): 模板文件路径  
            output_xml_path (Optional[str]): XML输出路径
            encrypt_output_path (Optional[str]): 加密文件输出路径
            
        Returns: Tuple[str, Dict[str, Any]]: (最终文件路径, 完整报告)
        """
        import tempfile
        import datetime
        
        # 步骤1-6: 生成并验证XML
        xml_string, validation_report, yanglint_status = self.generate_xml_with_validation(config_data, template_path)
        
        # 如果验证失败且有严重错误，记录警告但继续处理
        if validation_report["error_count"] > 0 and yanglint_status == "failed":
            log(_("enhanced_yang_generator.validation_failed_continuing"), "warning")
            # 不再停止处理，继续保存XML文件
        
        # 步骤7: 保存XML文件
        if output_xml_path:
            try:
                # 确保输出目录存在
                output_dir = os.path.dirname(output_xml_path)
                if output_dir and not os.path.exists(output_dir):
                    os.makedirs(output_dir, exist_ok=True)
                
                with open(output_xml_path, 'w', encoding='utf-8') as f:
                    f.write(xml_string)
                
                validation_report["xml_file_path"] = output_xml_path
                log(_("enhanced_yang_generator.xml_saved", path=output_xml_path))
                
            except Exception as e:
                log(_("enhanced_yang_generator.xml_save_error", error=str(e)), "error")
                validation_report["xml_save_error"] = str(e)
                return "", validation_report
        
        # 步骤8-11: 如果需要加密输出，执行加密流程
        if encrypt_output_path and output_xml_path:
            try:
                encryption_result = self._generate_encrypted_config_package(
                    output_xml_path, template_path, encrypt_output_path)
                
                validation_report.update(encryption_result)
                
                if encryption_result.get("encryption_success", False):
                    final_output_path = encrypt_output_path
                    log(_("enhanced_yang_generator.encryption_completed", path=encrypt_output_path))
                else:
                    final_output_path = output_xml_path
                    log(_("enhanced_yang_generator.encryption_failed_fallback"), "warning")
                    
            except Exception as e:
                log(_("enhanced_yang_generator.encryption_error", error=str(e)), "error")
                validation_report["encryption_error"] = str(e)
                final_output_path = output_xml_path
        else:
            final_output_path = output_xml_path or ""
        
        # 添加处理摘要
        validation_report["processing_timestamp"] = datetime.datetime.now().isoformat()
        validation_report["final_output_path"] = final_output_path
        validation_report["template_used"] = template_path is not None
        
        return final_output_path, validation_report
    
    def _generate_encrypted_config_package(self, xml_file_path: str, template_path: Optional[str], 
                                         encrypt_output_path: str) -> Dict[str, Any]:
        """
        生成加密配置包 - 实现步骤9-11
        
        Args:
            xml_file_path (str): XML文件路径
            template_path (Optional[str]): 模板路径
            encrypt_output_path (str): 加密输出路径
            
        Returns: Dict[str, Any]: 加密处理结果
        """
        encryption_result = {
            "encryption_success": False,
            "encryption_started": True
        }
        
        try:
            # 步骤9: 导入加密模块
            from engine.encrypt import encrypt_config
            
            # 步骤10: 获取模板路径（如果没有提供）
            if not template_path:
                template_path = self._get_default_template_path()
                log(_("enhanced_yang_generator.using_default_template", path=template_path))
            
            # 步骤11: 执行加密操作
            log(_("enhanced_yang_generator.starting_encryption", 
                 xml_file=xml_file_path, template=template_path, output=encrypt_output_path))
            
            success, message = encrypt_config(xml_file_path, template_path, encrypt_output_path)
            
            encryption_result["encryption_success"] = success
            encryption_result["encryption_message"] = message
            
            if success:
                # 验证加密文件是否存在
                if os.path.exists(encrypt_output_path):
                    file_size = os.path.getsize(encrypt_output_path)
                    encryption_result["encrypted_file_size"] = file_size
                    log(_("enhanced_yang_generator.encryption_success", 
                         path=encrypt_output_path, size=file_size))
                else:
                    encryption_result["encryption_success"] = False
                    encryption_result["encryption_error"] = "加密文件未生成"
            else:
                log(_("enhanced_yang_generator.encryption_failed",
                      message=message), "error")
                
        except ImportError as e:
            encryption_result["encryption_error"] = f"无法导入加密模块: {str(e)}"
            log(_("enhanced_yang_generator.encryption_import_error", error=str(e)), "error")
        except Exception as e:
            encryption_result["encryption_error"] = str(e)
            log(_("enhanced_yang_generator.encryption_exception", error=str(e)), "error")
        
        return encryption_result
    
    def _get_default_template_path(self) -> str:
        """获取默认模板路径"""
        # 构建默认模板路径
        base_dir = os.path.dirname(os.path.dirname(__file__))  # engine目录
        template_path = os.path.join(base_dir, "data", "input", "configData", 
                                   self.model, self.version, "Config", "running.xml")
        
        if os.path.exists(template_path):
            return template_path
        
        # 尝试其他可能的路径
        alternative_paths = [
            os.path.join(base_dir, "data", "templates", self.model, self.version, "running.xml"),
            os.path.join(base_dir, "data", "input", self.model, self.version, "Config", "running.xml")
        ]
        
        for alt_path in alternative_paths:
            if os.path.exists(alt_path):
                return alt_path
        
        log(_("enhanced_yang_generator.no_template_found", model=self.model, version=self.version), "warning")
        return template_path  # 返回默认路径，即使不存在
    
    def _load_or_create_base_xml(self, template_path: Optional[str]) -> etree.Element:
        """加载模板或创建基础XML结构 - 优先使用模板信息（设计原则1）"""
        if template_path and os.path.exists(template_path):
            try:
                tree = etree.parse(template_path)
                root = tree.getroot()
                log(_("enhanced_yang_generator.template_loaded", path=template_path))
                
                # 验证模板根节点
                if root.tag != "config":
                    log(_("enhanced_yang_generator.invalid_template_root", tag=root.tag), "warning")
                
                # 确保根节点有正确的命名空间声明
                if root.get("xmlns") != "urn:ruijie:ntos":
                    root.set("xmlns", "urn:ruijie:ntos")
                    log(_("enhanced_yang_generator.fixed_root_namespace"))
                
                # 清理其他节点的冗余xmlns声明（保持设计原则2）
                self._cleanup_redundant_namespaces(root)
                
                return root
            except Exception as e:
                log(_("enhanced_yang_generator.template_load_error", error=str(e)), "error")
                log(_("enhanced_yang_generator.fallback_to_default"), "info")
        
        # 创建默认XML结构
        return self._create_default_xml_structure()
    
    def _create_default_xml_structure(self) -> etree.Element:
        """创建默认的XML结构，基于YANG模型，符合命名空间要求"""
        # 创建根节点，只设置默认命名空间（符合设计原则2）
        main_namespace = "urn:ruijie:ntos"
        root = etree.Element("ntos")
        root.set("xmlns", main_namespace)  # 根节点只有一个默认命名空间声明
        
        # 验证根节点创建
        try:
            self.validation_context.validate_element_creation("", "ntos", main_namespace)
        except Exception as e:
            log(_("enhanced_yang_generator.validation_skipped", error=str(e)), "warning")
        
        # 创建基础VRF结构（不添加额外的xmlns）
        vrf_element = self._create_element_without_namespace(root, "vrf", "vrf")
        
        # 添加main VRF
        main_vrf = self._create_element_without_namespace(vrf_element, "main", "vrf/main")
        
        log(_("enhanced_yang_generator.default_structure_created"))
        return root
    
    def _process_config_data(self, root: etree.Element, config_data: Dict[str, Any]):
        """处理配置数据，基于YANG模型验证和生成XML"""
        # 获取或创建VRF节点
        vrf_element = root.find(".//vrf")
        if vrf_element is None:
            vrf_element = self._create_or_get_element(root, "vrf", "vrf")
        
        main_vrf = vrf_element.find(".//main")
        if main_vrf is None:
            main_vrf = self._create_or_get_element(vrf_element, "main", "vrf/main")
        
        # 处理接口配置
        if "interfaces" in config_data and config_data["interfaces"]:
            self._process_interfaces(main_vrf, config_data["interfaces"])
        
        # 处理安全区域
        if "zones" in config_data and config_data["zones"]:
            self._process_security_zones(main_vrf, config_data["zones"])
        
        # 处理路由配置
        if "static_routes" in config_data and config_data["static_routes"]:
            self._process_routing(main_vrf, config_data["static_routes"])
        
        # 处理策略规则
        if "policy_rules" in config_data and config_data["policy_rules"]:
            self._process_security_policies(main_vrf, config_data["policy_rules"])
        
        # 处理地址对象
        if "address_objects" in config_data and config_data["address_objects"]:
            self._process_address_objects(main_vrf, config_data["address_objects"])
        
        # 处理服务对象
        if "service_objects" in config_data and config_data["service_objects"]:
            self._process_service_objects(main_vrf, config_data["service_objects"])
    
    def _process_interfaces(self, parent: etree.Element, interfaces: Dict[str, Any]):
        """处理接口配置 - 优先使用模板信息（设计原则1）"""
        # 获取或创建接口容器，使用XPath查找现有结构
        interface_container = self._find_or_create_element_by_path(parent, ".//interface")
        
        # 确保接口容器有正确的命名空间
        if interface_container.get("xmlns") != "urn:ruijie:ntos:params:xml:ns:yang:interface":
            interface_container.set("xmlns", "urn:ruijie:ntos:params:xml:ns:yang:interface")
        
        # 检查并添加SNMP配置（如果模板中不存在）
        snmp_elem = interface_container.find(".//snmp")
        if snmp_elem is None:
            snmp_elem = self._create_element_without_namespace(interface_container, "snmp", "vrf/main/interface/snmp")
            self._update_or_create_element_text(snmp_elem, "if-usage-compute-interval", "30",
                                              "vrf/main/interface/snmp/if-usage-compute-interval")
            self._update_or_create_element_text(snmp_elem, "if-global-notify-enable", "false",
                                              "vrf/main/interface/snmp/if-global-notify-enable")
            log(_("enhanced_yang_generator.created_snmp_config"))
        else:
            log(_("enhanced_yang_generator.found_existing_snmp"))
        
        # 处理每个接口配置
        for interface_name, interface_config in interfaces.items():
            self._process_single_interface_with_template(interface_container, interface_name, interface_config)
    
    def _process_single_interface_with_template(self, parent: etree.Element, name: str, config: Dict[str, Any]):
        """处理单个接口配置 - 实现设计原则1的核心逻辑"""
        # 标准化接口名称
        sanitized_name = self._sanitize_interface_name(name)
        
        # 查找模板中是否已存在该接口
        existing_interface = parent.find(f".//{sanitized_name}")
        
        if existing_interface is not None:
            # 接口已存在 → 修改字段（设计原则1）
            log(_("enhanced_yang_generator.modify_existing_interface", name=sanitized_name))
            self._update_existing_interface_config(existing_interface, config, sanitized_name)
        else:
            # 接口不存在 → 插入新的完整块（设计原则1）
            log(_("enhanced_yang_generator.create_new_interface", name=sanitized_name))
            self._create_complete_interface_block(parent, sanitized_name, config)
    
    def _update_existing_interface_config(self, interface_elem: etree.Element, config: Dict[str, Any], name: str):
        """更新现有接口配置 - 保留模板信息，只修改必要字段"""
        interface_path = f"vrf/main/interface/{name}"
        
        # 更新IP地址配置（如果提供）
        if "ip_address" in config:
            # 查找现有IP配置路径
            ip_elem = interface_elem.find(".//ip")
            if ip_elem is None:
                ip_elem = self._create_element_without_namespace(interface_elem, "ip", f"{interface_path}/ip")
            
            addr_elem = ip_elem.find(".//address") 
            if addr_elem is None:
                addr_elem = self._create_element_without_namespace(ip_elem, "address", f"{interface_path}/ip/address")
            
            primary_elem = addr_elem.find(".//primary")
            if primary_elem is None:
                primary_elem = self._create_element_without_namespace(addr_elem, "primary", f"{interface_path}/ip/address/primary")
            
            # 修改IP地址字段
            self._update_or_create_element_text(primary_elem, "ip", config["ip_address"], 
                                              f"{interface_path}/ip/address/primary/ip")
            
            # 修改子网掩码字段（如果提供）
            if "netmask" in config:
                self._update_or_create_element_text(primary_elem, "mask", config["netmask"],
                                                  f"{interface_path}/ip/address/primary/mask")
        
        # 更新安全区域（如果提供）
        if "zone" in config:
            self._update_or_create_element_text(interface_elem, "security-zone", config["zone"],
                                              f"{interface_path}/security-zone")
        
        # 更新接口状态（如果提供）
        if "enabled" in config:
            if config["enabled"]:
                # 确保no shutdown存在
                self._update_or_create_element_text(interface_elem, "no", "shutdown", f"{interface_path}/no")
                # 移除shutdown配置（如果存在）
                shutdown_elem = interface_elem.find(".//shutdown")
                if shutdown_elem is not None:
                    interface_elem.remove(shutdown_elem)
            else:
                # 添加shutdown配置
                self._update_or_create_element_text(interface_elem, "shutdown", "", f"{interface_path}/shutdown")
                # 移除no shutdown配置（如果存在）
                no_elem = interface_elem.find(".//no")
                if no_elem is not None and no_elem.text == "shutdown":
                    interface_elem.remove(no_elem)
        
        log(_("enhanced_yang_generator.interface_config_updated", name=name))
    
    def _create_complete_interface_block(self, parent: etree.Element, name: str, config: Dict[str, Any]):
        """创建新的完整接口配置块"""
        interface_path = f"vrf/main/interface/{name}"
        interface_elem = self._create_element_without_namespace(parent, name, interface_path)
        
        # 创建完整的IP配置块（如果提供）
        if "ip_address" in config:
            ip_elem = self._create_element_without_namespace(interface_elem, "ip", f"{interface_path}/ip")
            addr_elem = self._create_element_without_namespace(ip_elem, "address", f"{interface_path}/ip/address")
            primary_elem = self._create_element_without_namespace(addr_elem, "primary", f"{interface_path}/ip/address/primary")
            
            # 添加IP地址
            self._update_or_create_element_text(primary_elem, "ip", config["ip_address"],
                                              f"{interface_path}/ip/address/primary/ip")
            
            # 添加子网掩码（如果提供）
            if "netmask" in config:
                self._update_or_create_element_text(primary_elem, "mask", config["netmask"],
                                                  f"{interface_path}/ip/address/primary/mask")
        
        # 添加安全区域（如果提供）
        if "zone" in config:
            self._update_or_create_element_text(interface_elem, "security-zone", config["zone"],
                                              f"{interface_path}/security-zone")
        
        # 添加接口启用状态
        if config.get("enabled", True):
            self._update_or_create_element_text(interface_elem, "no", "shutdown", f"{interface_path}/no")
        else:
            self._update_or_create_element_text(interface_elem, "shutdown", "", f"{interface_path}/shutdown")
        
        log(_("enhanced_yang_generator.interface_block_created", name=name))
    
    def _create_new_interface(self, parent: etree.Element, name: str, config: Dict[str, Any]):
        """创建新接口配置块"""
        interface_path = f"vrf/main/interface/{name}"
        interface_elem = self._create_or_get_element(parent, name, interface_path)
        
        # 添加基础配置
        if "ip_address" in config:
            ip_elem = self._create_or_get_element(interface_elem, "ip", f"{interface_path}/ip")
            addr_elem = self._create_or_get_element(ip_elem, "address", f"{interface_path}/ip/address")
            
            # 创建IP地址配置
            primary_elem = self._create_or_get_element(addr_elem, "primary", 
                                                     f"{interface_path}/ip/address/primary")
            self._create_leaf_element(primary_elem, "ip", config["ip_address"],
                                    f"{interface_path}/ip/address/primary/ip")
            
            if "netmask" in config:
                self._create_leaf_element(primary_elem, "mask", config["netmask"],
                                        f"{interface_path}/ip/address/primary/mask")
        
        # 添加安全区域
        if "zone" in config:
            self._create_leaf_element(interface_elem, "security-zone", config["zone"],
                                    f"{interface_path}/security-zone")
        
        # 添加启用状态
        if config.get("enabled", True):
            self._create_leaf_element(interface_elem, "no", "shutdown",
                                    f"{interface_path}/no")
    
    def _update_interface_config(self, interface_elem: etree.Element, config: Dict[str, Any]):
        """更新现有接口配置"""
        # 更新IP地址（如果提供）
        if "ip_address" in config:
            ip_elem = interface_elem.find(".//ip")
            if ip_elem is None:
                ip_elem = etree.SubElement(interface_elem, "ip")
            
            addr_elem = ip_elem.find(".//address")
            if addr_elem is None:
                addr_elem = etree.SubElement(ip_elem, "address")
            
            primary_elem = addr_elem.find(".//primary")
            if primary_elem is None:
                primary_elem = etree.SubElement(addr_elem, "primary")
            
            # 更新或创建IP地址
            ip_leaf = primary_elem.find(".//ip")
            if ip_leaf is not None:
                ip_leaf.text = config["ip_address"]
            else:
                etree.SubElement(primary_elem, "ip").text = config["ip_address"]
            
            # 更新或创建子网掩码
            if "netmask" in config:
                mask_leaf = primary_elem.find(".//mask")
                if mask_leaf is not None:
                    mask_leaf.text = config["netmask"]
                else:
                    etree.SubElement(primary_elem, "mask").text = config["netmask"]
        
        # 更新安全区域
        if "zone" in config:
            zone_elem = interface_elem.find(".//security-zone")
            if zone_elem is not None:
                zone_elem.text = config["zone"]
            else:
                etree.SubElement(interface_elem, "security-zone").text = config["zone"]
    
    def _process_security_zones(self, parent: etree.Element, zones: list):
        """处理安全区域配置"""
        security_zone_ns = self.namespace_map.get("ntos-security-zone",
                                                "urn:ruijie:ntos:params:xml:ns:yang:security-zone")
        
        # 获取或创建安全区域容器
        security_zone_container = self._get_or_create_validated_element(
            parent, "security-zone", "vrf/main/security-zone", security_zone_ns)
        
        for zone_config in zones:
            zone_name = zone_config.get("name")
            if not zone_name:
                continue
            
            # 检查区域是否已存在
            existing_zone = security_zone_container.find(f".//{zone_name}")
            
            if existing_zone is not None:
                # 区域已存在，修改字段
                log(_("enhanced_yang_generator.modify_existing_zone", name=zone_name))
                self._update_zone_config(existing_zone, zone_config)
            else:
                # 区域不存在，插入新的完整块
                log(_("enhanced_yang_generator.create_new_zone", name=zone_name))
                self._create_new_zone(security_zone_container, zone_name, zone_config)
    
    def _create_new_zone(self, parent: etree.Element, name: str, config: Dict[str, Any]):
        """创建新安全区域配置块"""
        zone_path = f"vrf/main/security-zone/{name}"
        zone_elem = self._create_or_get_element(parent, name, zone_path)
        
        # 添加描述
        if "description" in config:
            self._create_leaf_element(zone_elem, "description", config["description"],
                                    f"{zone_path}/description")
        
        # 添加接口成员
        if "interfaces" in config and config["interfaces"]:
            interface_elem = self._create_or_get_element(zone_elem, "interface", 
                                                       f"{zone_path}/interface")
            for interface_name in config["interfaces"]:
                sanitized_name = self._sanitize_interface_name(interface_name)
                self._create_leaf_element(interface_elem, "member", sanitized_name,
                                        f"{zone_path}/interface/member")
    
    def _update_zone_config(self, zone_elem: etree.Element, config: Dict[str, Any]):
        """更新现有安全区域配置"""
        # 更新描述
        if "description" in config:
            desc_elem = zone_elem.find(".//description")
            if desc_elem is not None:
                desc_elem.text = config["description"]
            else:
                etree.SubElement(zone_elem, "description").text = config["description"]
    
    def _process_routing(self, parent: etree.Element, routes: list):
        """处理路由配置"""
        routing_ns = self.namespace_map.get("ntos-routing",
                                          "urn:ruijie:ntos:params:xml:ns:yang:routing")
        
        # 获取或创建路由容器
        routing_container = self._get_or_create_validated_element(
            parent, "routing", "vrf/main/routing", routing_ns)
        
        # 获取或创建静态路由容器
        static_routes_container = self._get_or_create_validated_element(
            routing_container, "static-routes", "vrf/main/routing/static-routes")
        
        for route_config in routes:
            self._create_static_route(static_routes_container, route_config)
    
    def _create_static_route(self, parent: etree.Element, config: Dict[str, Any]):
        """创建静态路由配置"""
        if "destination" not in config or "next_hop" not in config:
            return
        
        route_path = f"vrf/main/routing/static-routes/route"
        route_elem = self._create_or_get_element(parent, "route", route_path)
        
        # 设置目标网络
        self._create_leaf_element(route_elem, "destination", config["destination"],
                                f"{route_path}/destination")
        
        # 设置下一跳
        self._create_leaf_element(route_elem, "next-hop", config["next_hop"],
                                f"{route_path}/next-hop")
        
        # 设置度量值（可选）
        if "metric" in config:
            self._create_leaf_element(route_elem, "metric", str(config["metric"]),
                                    f"{route_path}/metric")
    
    def _process_security_policies(self, parent: etree.Element, policies: list):
        """处理安全策略配置 - 基于模板优先原则"""
        if not policies:
            return
            
        # 获取或创建安全策略容器
        policy_container = self._find_or_create_element_by_path(parent, ".//security-policy")
        
        if policy_container.get("xmlns") != "urn:ruijie:ntos:params:xml:ns:yang:security-policy":
            policy_container.set("xmlns", "urn:ruijie:ntos:params:xml:ns:yang:security-policy")
        
        for policy_config in policies:
            policy_id = policy_config.get("id") or policy_config.get("name")
            if not policy_id:
                continue
                
            # 查找现有策略
            existing_policy = policy_container.find(f".//rule[id='{policy_id}']")
            
            if existing_policy is not None:
                # 策略已存在 → 修改字段
                log(_("enhanced_yang_generator.modify_existing_policy", id=policy_id))
                self._update_existing_policy_config(existing_policy, policy_config, policy_id)
            else:
                # 策略不存在 → 插入新的完整块
                log(_("enhanced_yang_generator.create_new_policy", id=policy_id))
                self._create_complete_policy_block(policy_container, policy_id, policy_config)
    
    def _update_existing_policy_config(self, policy_elem: etree.Element, config: Dict[str, Any], policy_id: str):
        """更新现有安全策略配置 - 保留模板信息，只修改必要字段"""
        policy_path = f"vrf/main/security-policy/rule[id='{policy_id}']"
        
        # 更新源区域
        if "src_zone" in config:
            self._update_or_create_element_text(policy_elem, "src-zone", config["src_zone"],
                                              f"{policy_path}/src-zone")
        
        # 更新目标区域
        if "dst_zone" in config:
            self._update_or_create_element_text(policy_elem, "dst-zone", config["dst_zone"],
                                              f"{policy_path}/dst-zone")
        
        # 更新源地址
        if "src_addr" in config:
            self._update_or_create_element_text(policy_elem, "src-addr", config["src_addr"],
                                              f"{policy_path}/src-addr")
        
        # 更新目标地址
        if "dst_addr" in config:
            self._update_or_create_element_text(policy_elem, "dst-addr", config["dst_addr"],
                                              f"{policy_path}/dst-addr")
        
        # 更新服务
        if "service" in config:
            self._update_or_create_element_text(policy_elem, "service", config["service"],
                                              f"{policy_path}/service")
        
        # 更新动作
        if "action" in config:
            self._update_or_create_element_text(policy_elem, "action", config["action"],
                                              f"{policy_path}/action")
    
    def _create_complete_policy_block(self, parent: etree.Element, policy_id: str, config: Dict[str, Any]):
        """创建新的完整安全策略块"""
        policy_path = f"vrf/main/security-policy/rule"
        rule_elem = self._create_element_without_namespace(parent, "rule", policy_path)
        
        # 添加策略ID
        self._update_or_create_element_text(rule_elem, "id", str(policy_id), f"{policy_path}/id")
        
        # 添加源区域
        if "src_zone" in config:
            self._update_or_create_element_text(rule_elem, "src-zone", config["src_zone"],
                                              f"{policy_path}/src-zone")
        
        # 添加目标区域
        if "dst_zone" in config:
            self._update_or_create_element_text(rule_elem, "dst-zone", config["dst_zone"],
                                              f"{policy_path}/dst-zone")
        
        # 添加源地址
        if "src_addr" in config:
            self._update_or_create_element_text(rule_elem, "src-addr", config["src_addr"],
                                              f"{policy_path}/src-addr")
        
        # 添加目标地址
        if "dst_addr" in config:
            self._update_or_create_element_text(rule_elem, "dst-addr", config["dst_addr"],
                                              f"{policy_path}/dst-addr")
        
        # 添加服务
        if "service" in config:
            self._update_or_create_element_text(rule_elem, "service", config["service"],
                                              f"{policy_path}/service")
        
        # 添加动作
        action = config.get("action", "permit")
        self._update_or_create_element_text(rule_elem, "action", action, f"{policy_path}/action")
        
        log(_("enhanced_yang_generator.policy_block_created", id=policy_id))
    
    def _process_address_objects(self, parent: etree.Element, address_objects: list):
        """处理地址对象配置 - 基于模板优先原则"""
        if not address_objects:
            return
            
        # 获取或创建地址对象容器
        addr_container = self._find_or_create_element_by_path(parent, ".//address-object")
        
        for addr_config in address_objects:
            addr_name = addr_config.get("name")
            if not addr_name:
                continue
                
            # 查找现有地址对象
            existing_addr = addr_container.find(f".//{addr_name}")
            
            if existing_addr is not None:
                # 地址对象已存在 → 修改字段
                log(_("enhanced_yang_generator.modify_existing_address", name=addr_name))
                self._update_existing_address_config(existing_addr, addr_config, addr_name)
            else:
                # 地址对象不存在 → 插入新的完整块
                log(_("enhanced_yang_generator.create_new_address", name=addr_name))
                self._create_complete_address_block(addr_container, addr_name, addr_config)
    
    def _update_existing_address_config(self, addr_elem: etree.Element, config: Dict[str, Any], addr_name: str):
        """更新现有地址对象配置"""
        addr_path = f"vrf/main/address-object/{addr_name}"
        
        if "ip" in config:
            self._update_or_create_element_text(addr_elem, "ip", config["ip"], f"{addr_path}/ip")
        
        if "mask" in config:
            self._update_or_create_element_text(addr_elem, "mask", config["mask"], f"{addr_path}/mask")
        
        if "description" in config:
            self._update_or_create_element_text(addr_elem, "description", config["description"],
                                              f"{addr_path}/description")
    
    def _create_complete_address_block(self, parent: etree.Element, addr_name: str, config: Dict[str, Any]):
        """创建新的完整地址对象块"""
        addr_path = f"vrf/main/address-object/{addr_name}"
        addr_elem = self._create_element_without_namespace(parent, addr_name, addr_path)
        
        if "ip" in config:
            self._update_or_create_element_text(addr_elem, "ip", config["ip"], f"{addr_path}/ip")
        
        if "mask" in config:
            self._update_or_create_element_text(addr_elem, "mask", config["mask"], f"{addr_path}/mask")
        
        if "description" in config:
            self._update_or_create_element_text(addr_elem, "description", config["description"],
                                              f"{addr_path}/description")
        
        log(_("enhanced_yang_generator.address_block_created", name=addr_name))
    
    def _process_service_objects(self, parent: etree.Element, service_objects: list):
        """处理服务对象配置 - 基于模板优先原则"""
        if not service_objects:
            return
            
        # 获取或创建服务对象容器
        service_container = self._find_or_create_element_by_path(parent, ".//service-object")
        
        for service_config in service_objects:
            service_name = service_config.get("name")
            if not service_name:
                continue
                
            # 查找现有服务对象
            existing_service = service_container.find(f".//{service_name}")
            
            if existing_service is not None:
                # 服务对象已存在 → 修改字段
                log(_("enhanced_yang_generator.modify_existing_service", name=service_name))
                self._update_existing_service_config(existing_service, service_config, service_name)
            else:
                # 服务对象不存在 → 插入新的完整块
                log(_("enhanced_yang_generator.create_new_service", name=service_name))
                self._create_complete_service_block(service_container, service_name, service_config)
    
    def _update_existing_service_config(self, service_elem: etree.Element, config: Dict[str, Any], service_name: str):
        """更新现有服务对象配置"""
        service_path = f"vrf/main/service-object/{service_name}"
        
        if "protocol" in config:
            self._update_or_create_element_text(service_elem, "protocol", config["protocol"],
                                              f"{service_path}/protocol")
        
        if "port" in config:
            self._update_or_create_element_text(service_elem, "port", str(config["port"]),
                                              f"{service_path}/port")
        
        if "description" in config:
            self._update_or_create_element_text(service_elem, "description", config["description"],
                                              f"{service_path}/description")
    
    def _create_complete_service_block(self, parent: etree.Element, service_name: str, config: Dict[str, Any]):
        """创建新的完整服务对象块"""
        service_path = f"vrf/main/service-object/{service_name}"
        service_elem = self._create_element_without_namespace(parent, service_name, service_path)
        
        if "protocol" in config:
            self._update_or_create_element_text(service_elem, "protocol", config["protocol"],
                                              f"{service_path}/protocol")
        
        if "port" in config:
            self._update_or_create_element_text(service_elem, "port", str(config["port"]),
                                              f"{service_path}/port")
        
        if "description" in config:
            self._update_or_create_element_text(service_elem, "description", config["description"],
                                              f"{service_path}/description")
        
        log(_("enhanced_yang_generator.service_block_created", name=service_name))
    
    def _get_or_create_validated_element(self, parent: etree.Element, tag: str, 
                                       path: str, namespace: str = None) -> etree.Element:
        """获取或创建带验证的元素"""
        # 先尝试查找现有元素
        element = parent.find(f".//{tag}")
        
        if element is None:
            # 元素不存在，创建新元素
            element = self._create_or_get_element(parent, tag, path, namespace)
        
        return element
    
    def _create_or_get_element(self, parent: etree.Element, tag: str, path: str, 
                             namespace: str = None) -> etree.Element:
        """创建或获取元素，带YANG验证"""
        # 验证元素创建（捕获验证错误避免中断流程）
        try:
            parent_path = "/".join(path.split("/")[:-1]) if "/" in path else ""
            self.validation_context.validate_element_creation(parent_path, tag, namespace)
        except Exception as e:
            log(_("enhanced_yang_generator.validation_error", error=str(e)), "warning")
        
        # 创建元素
        element = etree.SubElement(parent, tag)
        
        # 设置命名空间（如果提供）
        if namespace:
            try:
                element.set("xmlns", namespace)
            except Exception as e:
                log(_("enhanced_yang_generator.namespace_error", error=str(e)), "warning")
        
        return element
    
    def _create_leaf_element(self, parent: etree.Element, tag: str, value: str, path: str):
        """创建叶子元素，带值验证"""
        # 验证值
        self.validation_context.validate_element_value(path, value)
        
        # 创建元素
        element = etree.SubElement(parent, tag)
        element.text = value
        
        return element
    
    def _sanitize_interface_name(self, name: str) -> str:
        """标准化接口名称"""
        # 移除特殊字符，转换为符合YANG规范的格式
        sanitized = re.sub(r'[^\w\-_.]', '_', name)
        return sanitized
    
    def _generate_final_xml(self, root: etree.Element) -> str:
        """生成最终的XML字符串"""
        # 清理命名空间声明
        self._cleanup_namespaces(root)
        
        # 生成格式化的XML
        xml_str = etree.tostring(root, encoding='unicode', pretty_print=True)
        
        # 修复重复的xmlns属性
        from engine.generators.xml_utils import fix_duplicate_xmlns_in_string
        xml_str = fix_duplicate_xmlns_in_string(xml_str)
        
        return xml_str
    
    def _cleanup_namespaces(self, root: etree.Element):
        """清理和优化命名空间声明"""
        # 移除重复的命名空间声明
        # 这里可以实现更复杂的命名空间优化逻辑
        pass
    
    def get_validation_report(self) -> Dict[str, Any]:
        """获取详细的验证报告"""
        return self.validation_context.get_validation_summary()
    
    def export_yang_schema(self, output_path: str):
        """导出YANG架构信息，用于调试"""
        self.yang_parser.export_schema_json(output_path)
        log(_("enhanced_yang_generator.schema_exported", path=output_path))
    
    def _create_element_without_namespace(self, parent: etree.Element, tag: str, path: str) -> etree.Element:
        """创建元素但不设置xmlns（符合设计原则2）"""
        try:
            parent_path = "/".join(path.split("/")[:-1]) if "/" in path else ""
            self.validation_context.validate_element_creation(parent_path, tag, None)
        except Exception as e:
            log(_("enhanced_yang_generator.validation_error", error=str(e)), "warning")
        
        # 创建元素但不设置xmlns属性
        element = etree.SubElement(parent, tag)
        return element
    
    def _cleanup_redundant_namespaces(self, root: etree.Element):
        """清理冗余的xmlns声明，保持设计原则2的要求"""
        
        # 定义始终需要保留命名空间的元素（顶级元素）
        always_required_namespace_elements = {
            "interface": "urn:ruijie:ntos:params:xml:ns:yang:interface",
            "security-zone": "urn:ruijie:ntos:params:xml:ns:yang:security-zone", 
            "routing": "urn:ruijie:ntos:params:xml:ns:yang:routing",
            "vlan": "urn:ruijie:ntos:params:xml:ns:yang:vlan"
        }
        
        # 定义只有在interface标签内才需要保留命名空间的元素
        interface_specific_namespace_elements = {
            "flow-control": "urn:ruijie:ntos:params:xml:ns:yang:flow-control",
            "monitor": "urn:ruijie:ntos:params:xml:ns:yang:if-mon",
            "ip-mac-bind": "urn:ruijie:ntos:params:xml:ns:yang:ip-mac",
            "access-control": "urn:ruijie:ntos:params:xml:ns:yang:local-defend"
        }
        
        def is_inside_interface(elem):
            """检查元素是否在interface标签内"""
            parent = elem.getparent()
            while parent is not None:
                if not isinstance(parent.tag, str):
                    parent = parent.getparent()
                    continue
                parent_tag = parent.tag.split('}')[-1] if '}' in parent.tag else parent.tag
                if parent_tag == "interface":
                    return True
                parent = parent.getparent()
            return False
        
        for elem in root.iter():
            # 移除非根节点的xmlns声明，除非是特定命名空间
            if elem != root and elem.get("xmlns"):
                # 确保tag是字符串类型
                if not isinstance(elem.tag, str):
                    continue
                tag_name = elem.tag.split('}')[-1] if '}' in elem.tag else elem.tag
                
                # 始终保留的命名空间元素
                if tag_name in always_required_namespace_elements:
                    expected_ns = always_required_namespace_elements[tag_name]
                    if elem.get("xmlns") != expected_ns:
                        elem.set("xmlns", expected_ns)
                
                # 只有在interface内才保留命名空间的元素
                elif tag_name in interface_specific_namespace_elements:
                    if is_inside_interface(elem):
                        expected_ns = interface_specific_namespace_elements[tag_name]
                        if elem.get("xmlns") != expected_ns:
                            elem.set("xmlns", expected_ns)
                    else:
                        # 不在interface内，移除xmlns声明
                        elem.attrib.pop("xmlns", None)
                
                # 其他元素移除xmlns声明
                else:
                    elem.attrib.pop("xmlns", None)
    
    def _find_or_create_element_by_path(self, root: etree.Element, xpath: str, create_if_missing: bool = True) -> Optional[etree.Element]:
        """根据XPath查找元素，如果不存在则创建（实现设计原则1）"""
        # 先尝试查找现有元素
        element = root.find(xpath)
        
        if element is not None:
            log(_("enhanced_yang_generator.found_existing_element", path=xpath))
            return element
        
        if not create_if_missing:
            return None
        
        # 元素不存在，需要创建完整路径
        log(_("enhanced_yang_generator.creating_element_path", path=xpath))
        
        # 分析路径并逐级创建
        path_parts = xpath.strip("./").split("/")
        current_element = root
        current_path = ""
        
        for i, part in enumerate(path_parts):
            current_path = "/".join(path_parts[:i+1])
            
            # 查找当前级别的元素
            if part.startswith(".") or part == "":
                continue
                
            child = current_element.find(f"./{part}")
            if child is None:
                # 创建缺失的元素
                child = self._create_element_without_namespace(current_element, part, current_path)
                
                # 为特定元素设置命名空间
                if part in ["interface", "security-zone", "routing"]:
                    namespace_map = {
                        "interface": "urn:ruijie:ntos:params:xml:ns:yang:interface",
                        "security-zone": "urn:ruijie:ntos:params:xml:ns:yang:security-zone",
                        "routing": "urn:ruijie:ntos:params:xml:ns:yang:routing"
                    }
                    if part in namespace_map:
                        child.set("xmlns", namespace_map[part])
                
                log(_("enhanced_yang_generator.created_element", tag=part, path=current_path))
            
            current_element = child
        
        return current_element
    
    def _update_or_create_element_text(self, parent: etree.Element, tag: str, value: str, path: str):
        """更新现有元素文本或创建新元素（实现设计原则1的修改逻辑）"""
        element = parent.find(f"./{tag}")
        
        if element is not None:
            # 元素已存在，修改字段
            old_value = element.text or ""
            element.text = value
            log(_("enhanced_yang_generator.updated_element_text", tag=tag, old_value=old_value, new_value=value))
        else:
            # 元素不存在，插入新的完整块
            element = self._create_element_without_namespace(parent, tag, path)
            element.text = value
            log(_("enhanced_yang_generator.created_element_with_text", tag=tag, value=value))
        
        # 验证元素值
        try:
            self.validation_context.validate_element_value(path, value)
        except Exception as e:
            log(_("enhanced_yang_generator.validation_error", error=str(e)), "warning")
        
        return element
