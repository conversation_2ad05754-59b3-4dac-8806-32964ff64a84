#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
修复版本的LegacyGenerator
专注于修复服务对象转换问题
"""

from engine.generators.legacy_generator import LegacyGenerator
from lxml import etree
from engine.utils.logger import log
from engine.utils.i18n import _

class FixedLegacyGenerator(LegacyGenerator):
    """修复版本的LegacyGenerator，解决服务对象转换问题"""
    
    def _add_service_objects_to_xml(self, vrf, nsmap):
        """
        添加服务对象到XML文档 - 修复版本
        
        Args:
            vrf: VRF元素
            nsmap: 命名空间映射
            
        Returns:
            int: 处理的服务对象数量
        """
        if not self.service_objects:
            log(_("info.no_service_objects_to_process"))
            return 0
            
        processed = 0
        skipped = 0
        
        # 记录服务对象的数量和内容，用于调试
        log(_("debug.fixed_legacy_generator_processing_service_objects"), "debug", count=len(self.service_objects))
        for i, svc in enumerate(self.service_objects):
            service_name = svc.get("name", "unknown")
            service_type = svc.get("type", "unknown")
            service_protocol = svc.get("protocol", "unknown")
            log(_("debug.fixed_legacy_generator_service_object_detail"), "debug",
                index=i+1, name=service_name, type=service_type, protocol=service_protocol)
        
        # 服务对象命名空间
        service_ns = self.NS_SERVICE_OBJ
        
        # 查找或创建服务对象容器
        service_obj_xpath = ".//*[local-name()='service-obj']"
        service_obj_nodes = vrf.xpath(service_obj_xpath)
        
        if service_obj_nodes:
            service_obj = service_obj_nodes[0]
            log(_("info.found_existing_service_obj_node"))
        else:
            service_obj = etree.SubElement(vrf, "service-obj")
            service_obj.set("xmlns", service_ns)
            log(_("info.created_service_obj_node"))
        
        # 收集现有服务集合，避免重复
        existing_services = {}
        for service_set in service_obj.xpath(".//*[local-name()='service-set']"):
            name_elem = service_set.xpath(".//*[local-name()='name']")
            if name_elem and len(name_elem) > 0:
                existing_services[name_elem[0].text] = service_set
        
        # 不支持的协议列表
        unsupported_protocols = ["udp-lite", "sctp", "icmp6"]
        
        # 处理每个服务对象
        for service in self.service_objects:
            try:
                # 获取服务名称
                service_name = service.get("name")
                if not service_name:
                    log(_("warning.service_missing_name"), "warning")
                    skipped += 1
                    continue
                
                log(_("debug.fixed_legacy_generator_start_processing_service"), "debug", name=service_name)
                
                # 检查是否存在TCP或UDP端口配置
                has_tcp = "tcp-portrange" in service and service["tcp-portrange"]
                has_udp = "udp-portrange" in service and service["udp-portrange"]
                
                log(_("debug.fixed_legacy_generator_service_port_config"), "debug",
                    name=service_name, has_tcp=has_tcp, has_udp=has_udp)
                
                # 检查协议类型，如果没有指定，则根据端口范围自动推断
                protocol = None
                if "protocol" in service:
                    protocol = service.get("protocol", "").lower()
                    log(_("info.service_protocol_specified", name=service_name, protocol=protocol))
                else:
                    # 根据端口范围自动推断协议类型
                    if has_tcp and has_udp:
                        protocol = "tcp_udp"
                        log(_("info.service_protocol_auto_detected", name=service_name, protocol=protocol, reason="both_tcp_udp_ports"))
                    elif has_tcp:
                        protocol = "tcp"
                        log(_("info.service_protocol_auto_detected", name=service_name, protocol=protocol, reason="tcp_ports"))
                    elif has_udp:
                        protocol = "udp"
                        log(_("info.service_protocol_auto_detected", name=service_name, protocol=protocol, reason="udp_ports"))
                    else:
                        # 既没有指定协议，也没有端口范围
                        log(_("warning.service_missing_protocol_and_ports", name=service_name), "warning")
                        skipped += 1
                        continue
                
                log(_("debug.fixed_legacy_generator_service_protocol"), "debug",
                    name=service_name, protocol=protocol)
                
                # 检查协议支持情况
                if protocol in unsupported_protocols:
                    log(_("warning.unsupported_protocol", name=service_name, protocol=protocol), "warning")
                    skipped += 1
                    continue
                
                # 处理FQDN地址（不支持）
                if "fqdn" in service:
                    log(_("warning.unsupported_fqdn", name=service_name, fqdn=service["fqdn"]), "warning")
                    skipped += 1
                    continue
                
                # 检查是否需要端口配置（TCP/UDP协议需要端口配置，IP和ICMP不需要）
                if not has_tcp and not has_udp and protocol not in ["ip", "icmp"]:
                    log(_("warning.service_missing_ports", name=service_name), "warning")
                    log(_("debug.fixed_legacy_generator_service_missing_ports"), "debug",
                        name=service_name, protocol=protocol)
                    skipped += 1
                    continue
                
                # 检查服务是否已存在
                if service_name in existing_services:
                    service_set = existing_services[service_name]
                    log(_("info.service_exists", name=service_name))
                    
                    # 清除现有协议配置
                    for protocol_elem in service_set.xpath(".//*[local-name()='tcp' or local-name()='udp' or local-name()='icmp' or local-name()='ip']"):
                        service_set.remove(protocol_elem)
                else:
                    # 创建新的service-set元素
                    service_set = etree.SubElement(service_obj, "service-set")
                    # 使用html.escape转义XML特殊字符
                    import html
                    etree.SubElement(service_set, "name").text = html.escape(service_name)
                
                # 处理TCP端口
                if has_tcp:
                    tcp_ports = self._convert_port_range(service["tcp-portrange"])
                    tcp_elem = etree.SubElement(service_set, "tcp")
                    etree.SubElement(tcp_elem, "dest-port").text = tcp_ports
                    log(_("debug.fixed_legacy_generator_added_tcp_ports"), "debug",
                        name=service_name, ports=tcp_ports)
                
                # 处理UDP端口
                if has_udp:
                    udp_ports = self._convert_port_range(service["udp-portrange"])
                    udp_elem = etree.SubElement(service_set, "udp")
                    etree.SubElement(udp_elem, "dest-port").text = udp_ports
                    log(_("debug.fixed_legacy_generator_added_udp_ports"), "debug",
                        name=service_name, ports=udp_ports)
                
                # 处理IP协议号
                if protocol == "ip" and "protocol-number" in service:
                    ip_elem = etree.SubElement(service_set, "protocol-id")
                    ip_elem.text = service["protocol-number"]
                    log(_("debug.fixed_legacy_generator_added_ip_protocol"), "debug",
                        name=service_name, protocol_number=service['protocol-number'])
                
                # 处理ICMP类型和代码
                if protocol == "icmp":
                    # 根据yang模型，icmp是一个列表，每个列表项需要同时有type和code作为键
                    icmp_type = service.get("icmptype", "0")
                    icmp_code = service.get("icmpcode", "0")
                    
                    # 创建icmp列表项
                    icmp_item = etree.SubElement(service_set, "icmp")
                    type_elem = etree.SubElement(icmp_item, "type")
                    type_elem.text = icmp_type
                    code_elem = etree.SubElement(icmp_item, "code")
                    code_elem.text = icmp_code
                    log(_("debug.fixed_legacy_generator_added_icmp"), "debug",
                        name=service_name, icmp_type=icmp_type, icmp_code=icmp_code)
                
                # 添加注释（如果有）
                if "comment" in service and service["comment"]:
                    # 同样转义注释中的XML特殊字符
                    import html
                    etree.SubElement(service_set, "description").text = html.escape(service["comment"])
                
                log(_("info.added_service", name=service_name))
                log(_("debug.fixed_legacy_generator_service_added_success"), "debug", name=service_name)
                processed += 1
                
            except Exception as e:
                log(_("error.service_conversion_failed", name=service.get("name", "unknown"), error=str(e)), "error")
                log(_("debug.fixed_legacy_generator_service_processing_failed"), "debug",
                    name=service.get('name', 'unknown'), error=str(e))
                skipped += 1
        
        # 检查是否有服务对象被添加到XML中
        service_sets = service_obj.xpath(".//*[local-name()='service-set']")
        log(_("debug.fixed_legacy_generator_xml_service_sets_count"), "debug", count=len(service_sets))
        for i, service_set in enumerate(service_sets):
            name_elem = service_set.xpath(".//*[local-name()='name']")
            if name_elem and len(name_elem) > 0:
                log(_("debug.fixed_legacy_generator_xml_service_set"), "debug",
                    index=i+1, name=name_elem[0].text)
        
        # 检查服务对象节点是否为空
        if len(service_sets) == 0:
            log("警告: FixedLegacyGenerator服务对象节点为空，没有添加任何服务集合", "warning")
        
        # 清理命名空间
        etree.cleanup_namespaces(service_obj)
        
        log(_("info.service_objects_processing_complete", 
              total=len(self.service_objects), 
              success=processed, 
              failed=skipped, 
              skipped=skipped))
        return processed 

    def _generate_legacy_xml(self):
        """
        生成NTOS XML配置 - 修复版本
        
        Returns:
            tuple: (XML字符串, 统计信息)
        """
        # 调用父类的_generate_legacy_xml方法
        xml_str, stats = super()._generate_legacy_xml()
        
        # 解析生成的XML
        try:
            root = etree.fromstring(xml_str.encode('utf-8'))
            
            # 查找VRF元素
            vrf_nodes = root.xpath(".//*[local-name()='vrf']")
            if vrf_nodes:
                vrf = vrf_nodes[0]
                
                # 查找服务对象节点
                service_obj_nodes = vrf.xpath(".//*[local-name()='service-obj']")
                if service_obj_nodes:
                    service_obj = service_obj_nodes[0]
                    service_sets = service_obj.xpath(".//*[local-name()='service-set']")
                    log(_("debug.fixed_legacy_generator_final_xml_service_sets_count"), "debug", count=len(service_sets))
                    
                    # 如果服务对象节点为空，则移除它
                    if len(service_sets) == 0:
                        log("警告: FixedLegacyGenerator中的服务对象节点为空，将移除该节点", "warning")
                        vrf.remove(service_obj)
                        log("已移除空的服务对象节点", "debug")
                        
                        # 重新序列化XML
                        xml_str = etree.tostring(root, pretty_print=True, encoding='utf-8').decode('utf-8')
                        log("已重新序列化XML", "debug")
                
                # 检查是否成功移除了空的服务对象节点
                service_obj_nodes = vrf.xpath(".//*[local-name()='service-obj']")
                if not service_obj_nodes:
                    log("确认: 空的服务对象节点已成功移除", "debug")
                else:
                    service_obj = service_obj_nodes[0]
                    service_sets = service_obj.xpath(".//*[local-name()='service-set']")
                    if len(service_sets) == 0:
                        log("警告: 空的服务对象节点移除失败", "warning")
                    else:
                        log(_("debug.fixed_legacy_generator_keeping_service_obj_node"), "debug", count=len(service_sets))
        
        except Exception as e:
            log(f"FixedLegacyGenerator处理XML时出错: {str(e)}", "error")
        
        return xml_str, stats 