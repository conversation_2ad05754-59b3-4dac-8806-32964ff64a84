#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""飞塔物理接口配置转换优化器"""

from lxml import etree
from engine.utils.logger import log
from engine.utils.i18n import _
from engine.generators.xml_utils import NS_INTERFACE, NS_FLOW, NS_MONITOR, NS_IPMAC, NS_ACCESS
from engine.generators.interface_common import InterfaceCommon

class FortinetPhysicalInterfaceHandler:
    """
    飞塔物理接口处理类，专门针对飞塔配置进行优化
    """
    
    def __init__(self, interface_common=None, transparent_mode_result=None):
        """
        初始化飞塔物理接口处理器

        Args:
            interface_common: 接口公共处理器实例
            transparent_mode_result: 透明模式处理结果，用于确定接口工作模式
        """
        self.interface_common = interface_common if interface_common else InterfaceCommon()
        self.transparent_mode_result = transparent_mode_result
    
    def _normalize_interface_role(self, role, interface_name, mode=""):
        """
        标准化接口角色，将不支持的角色转换为支持的角色
        
        Args:
            role: 原始角色名称
            interface_name: 接口名称（用于日志）
            mode: 接口模式（可选，用于特殊情况）
            
        Returns:
            str: 标准化后的角色名称（"lan"或"wan"）
        """
        if not role:
            # 如果没有指定角色，则根据模式判断
            if mode.lower() in ["pppoe", "dhcp"]:
                return "wan"
            else:
                return "lan"
        
        # 转换为小写并去除空白
        normalized_role = role.lower().strip()
        
        # 检查是否为支持的角色
        if normalized_role in ["lan", "wan"]:
            return normalized_role
        
        # 处理不支持的角色
        if normalized_role in ["dmz", "undefined"]:
            # 记录警告信息
            log(_("interface_handler.warning.unsupported_role", 
                interface=interface_name, role=normalized_role), "warning")
            log(_("fortigate.warning.unsupported_role", 
                interface=interface_name, role=normalized_role), "warning")
            log(_("interface_handler.role_defaulted_to_lan", name=interface_name))
            
            # 统一转换为lan
            return "lan"
        
        # 其他未知角色，记录警告并根据模式判断
        log(_("interface_handler.warning.unknown_role", 
            interface=interface_name, role=normalized_role), "warning")
        
        # 根据模式判断默认角色
        if mode.lower() in ["pppoe", "dhcp"]:
            log(_("interface_handler.role_defaulted_to_wan", name=interface_name))
            return "wan"
        else:
            log(_("interface_handler.role_defaulted_to_lan", name=interface_name))
            return "lan"

    def _get_interface_working_mode(self, interface_name, fortinet_config):
        """
        获取接口的工作模式（支持透明模式）

        Args:
            interface_name (str): 接口名称
            fortinet_config (dict): 飞塔配置数据

        Returns:
            str: 工作模式 ('route' 或 'bridge')
        """
        # 如果有透明模式处理结果，使用透明模式逻辑
        if self.transparent_mode_result:
            classification = self.transparent_mode_result.get("interface_classification", {})

            # 添加调试日志（只在透明模式时输出）

            # 检查是否为MGMT接口（保持路由模式）- 使用映射后的接口名称
            if interface_name in classification.get("mgmt_interfaces_mapped", []):
                log(_("interface_handler.working_mode_route_mgmt", interface=interface_name))
                return "route"

            # 检查是否为桥接接口 - 使用映射后的接口名称
            elif interface_name in classification.get("bridge_interfaces_mapped", []):
                log(_("interface_handler.working_mode_bridge_transparent", interface=interface_name))
                return "bridge"

            # 接口不在设备接口列表中，默认路由模式
            else:
                log(_("interface_handler.working_mode_route_default", interface=interface_name))
                return "route"

        # 非透明模式，默认使用路由模式
        log(_("interface_handler.working_mode_route_nat", interface=interface_name))
        return "route"

    def _get_mgmt_ip_for_interface(self, interface_name):
        """
        获取指定接口的管理IP（仅适用于透明模式下的MGMT接口）

        Args:
            interface_name (str): 接口名称

        Returns:
            str: 管理IP地址（CIDR格式），如果不是MGMT接口或没有管理IP则返回None
        """
        if not self.transparent_mode_result:
            return None

        # 检查是否为MGMT接口（同时检查原始名称和映射后名称）
        classification = self.transparent_mode_result.get("interface_classification", {})

        # 方法1：检查原始名称列表
        is_mgmt_original = interface_name in classification.get("mgmt_interfaces", [])

        # 方法2：检查映射后名称列表
        is_mgmt_mapped = interface_name in classification.get("mgmt_interfaces_mapped", [])

        # 方法3：直接检查是否为管理接口的映射后名称
        mgmt_interface_mapped = classification.get("mgmt_interface_mapped")
        is_mgmt_direct = interface_name == mgmt_interface_mapped

        log(f"DEBUG: 管理接口检查 - 接口: {interface_name}, 原始列表: {is_mgmt_original}, 映射列表: {is_mgmt_mapped}, 直接匹配: {is_mgmt_direct}", "info")

        if not (is_mgmt_original or is_mgmt_mapped or is_mgmt_direct):
            return None

        # 获取管理IP
        mgmt_ip = self.transparent_mode_result.get("mgmt_ip")
        if mgmt_ip:
            # 转换FortiGate的IP/掩码格式为CIDR格式
            mgmt_ip_cidr = self._convert_fortigate_ip_to_cidr(mgmt_ip)
            log(_("interface_handler.mgmt_ip_found",
                  interface=interface_name, ip=mgmt_ip_cidr))
            return mgmt_ip_cidr

        return None

    def _convert_fortigate_ip_to_cidr(self, ip_config):
        """
        将FortiGate的IP配置转换为CIDR格式

        Args:
            ip_config (str): FortiGate IP配置，如 "*************/*************"

        Returns:
            str: CIDR格式的IP地址
        """
        if not ip_config:
            return None

        # 如果已经是CIDR格式，直接返回
        if "/" in ip_config and not "255." in ip_config:
            return ip_config

        # 处理IP/掩码格式
        if "/" in ip_config:
            parts = ip_config.split("/")
            if len(parts) == 2:
                ip_addr = parts[0]
                netmask = parts[1]

                # 如果掩码是点分十进制格式，转换为前缀长度
                if "." in netmask:
                    prefix_length = self._netmask_to_prefix(netmask)
                    return f"{ip_addr}/{prefix_length}"
                else:
                    # 已经是前缀长度格式
                    return ip_config

        # 默认假设是/24网络
        return f"{ip_config}/24"

    def _is_mgmt_interface_in_transparent_mode(self, interface_name):
        """
        检查指定接口是否为透明模式下的MGMT接口

        Args:
            interface_name (str): 接口名称

        Returns:
            bool: 是否为透明模式下的MGMT接口
        """
        if not self.transparent_mode_result:
            return False

        classification = self.transparent_mode_result.get("interface_classification", {})
        return interface_name in classification.get("mgmt_interfaces", [])

    def parse_fortinet_ip_config(self, ip_config, netmask=None):
        """
        解析飞塔IP配置格式
        
        Args:
            ip_config: IP配置，可能是"***********"或"***********/24"
            netmask: 子网掩码，如"*************"
            
        Returns:
            str: CIDR格式的IP地址
        """
        if not ip_config:
            return None
        
        # 如果已经是CIDR格式，直接返回
        if "/" in str(ip_config):
            return str(ip_config)
        
        # 如果提供了子网掩码，转换为CIDR格式
        if netmask:
            try:
                # 将子网掩码转换为前缀长度
                prefix_length = self._netmask_to_prefix(netmask)
                return f"{ip_config}/{prefix_length}"
            except Exception as e:
                log(_("interface_handler.error.netmask_conversion", error=str(e)), "error")
                return str(ip_config)
        
        # 默认情况下，假设是/24网络
        return f"{ip_config}/24"
    
    def _netmask_to_prefix(self, netmask):
        """
        将子网掩码转换为前缀长度
        
        Args:
            netmask: 子网掩码字符串，如"*************"
            
        Returns:
            int: 前缀长度
        """
        # 常见的子网掩码映射
        netmask_map = {
            "***************": 32,
            "***************": 31,
            "***************": 30,
            "***************": 29,
            "***************": 28,
            "***************": 27,
            "***************": 26,
            "***************": 25,
            "*************": 24,
            "*************": 23,
            "*************": 22,
            "*************": 21,
            "*************": 20,
            "*************": 19,
            "*************": 18,
            "*************": 17,
            "***********": 16,
            "***********": 15,
            "***********": 14,
            "255.248.0.0": 13,
            "255.240.0.0": 12,
            "255.224.0.0": 11,
            "255.192.0.0": 10,
            "255.128.0.0": 9,
            "*********": 8,
            "254.0.0.0": 7,
            "252.0.0.0": 6,
            "248.0.0.0": 5,
            "240.0.0.0": 4,
            "224.0.0.0": 3,
            "192.0.0.0": 2,
            "128.0.0.0": 1,
            "0.0.0.0": 0
        }
        
        if netmask in netmask_map:
            return netmask_map[netmask]
        
        # 如果不在映射表中，尝试计算
        try:
            octets = netmask.split('.')
            binary = ''.join([bin(int(octet))[2:].zfill(8) for octet in octets])
            return binary.count('1')
        except:
            # 默认返回24
            return 24
    
    def parse_fortinet_allowaccess(self, allowaccess_config):
        """
        解析飞塔allowaccess配置
        
        Args:
            allowaccess_config: 可能是字符串"ping https ssh"或列表["ping", "https", "ssh"]
            
        Returns:
            list: 访问控制列表
        """
        if not allowaccess_config:
            return []
        
        if isinstance(allowaccess_config, str):
            # 分割字符串，处理多种分隔符
            return allowaccess_config.replace(",", " ").split()
        elif isinstance(allowaccess_config, list):
            return allowaccess_config
        else:
            return []
    
    def create_fortinet_interface(self, interface_elem, fortinet_config):
        """
        根据飞塔配置创建NTOS接口
        
        Args:
            interface_elem: 父接口元素
            fortinet_config: 飞塔配置数据
        """
        log(_("interface_handler.create_fortinet_interface", name=fortinet_config.get("name", "unknown")))
        
        # 检查接口是否被标记为无效
        if fortinet_config.get("invalid", False):
            reason = fortinet_config.get("invalid_reason", "未知原因")
            log(_("interface_handler.skipping_invalid_interface", 
                  name=fortinet_config.get("name", "unknown"), 
                  reason=reason), "warning")
            return None
            
        # 检查VDOM设置
        vdom = fortinet_config.get("vdom", "")
        if vdom and vdom.lower() != "root":
            log(_("interface_handler.warning.non_root_vdom", 
                  interface=fortinet_config.get("name", "unknown"), 
                  vdom=vdom), "warning")
            return None
        
        # 创建physical节点
        physical = etree.SubElement(interface_elem, "physical")
        # 注意：physical节点应该使用默认命名空间（与模板保持一致），不设置xmlns属性
        
        # 设置接口名称 - 优先使用alias，其次使用name
        interface_name = fortinet_config.get("alias", fortinet_config.get("name", ""))
        etree.SubElement(physical, "name").text = interface_name
        
        # 设置MTU
        mtu = fortinet_config.get("mtu", "1500")
        etree.SubElement(physical, "mtu").text = str(mtu)
        
        # 设置混杂模式
        etree.SubElement(physical, "promiscuous").text = "false"
        
        # 设置描述
        description = fortinet_config.get("description", "")
        etree.SubElement(physical, "description").text = description
        
        # 设置接口启用状态 - 优先使用解析器提供的enabled字段
        if "enabled" in fortinet_config:
            enabled = fortinet_config["enabled"]
        else:
            # 兼容旧逻辑：从status字段转换
            status = fortinet_config.get("status", "up")
            enabled = status.lower() in ["up", "enable", "enabled", "true"]
        etree.SubElement(physical, "enabled").text = "true" if enabled else "false"
        
        # 设置接口角色 - 使用标准化方法处理
        original_role = fortinet_config.get("role", "")
        mode = fortinet_config.get("mode", "")
        normalized_role = self._normalize_interface_role(original_role, interface_name, mode)
        etree.SubElement(physical, "wanlan").text = normalized_role
        
        # 设置工作模式 - 支持透明模式
        working_mode = self._get_interface_working_mode(interface_name, fortinet_config)

        # 添加详细调试日志
        log(f"🔍 DEBUG: - transparent_mode_result: {self.transparent_mode_result}", "info")

        etree.SubElement(physical, "working-mode").text = working_mode
        
        # 处理defaultgw设置
        defaultgw = fortinet_config.get("defaultgw", "")
        if defaultgw.lower() == "disable":
            log(_("interface_handler.warning.defaultgw_disable_not_supported", 
                  interface=interface_name), "warning")
            # NTOS不支持disable，但我们可以在日志中记录这个警告
        
        # 配置IPv4 - 支持透明模式下的管理IP
        self._configure_ipv4(physical, fortinet_config)
        
        # 配置IPv6（如果存在）
        self._configure_ipv6(physical, fortinet_config)
        
        # 添加默认接口配置
        self._add_enhanced_default_config(physical)
        
        # 配置带宽
        self._configure_bandwidth(physical, fortinet_config)
        
        # 配置访问控制 - 支持透明模式下的管理协议
        self._configure_access_control(physical, fortinet_config)
        
        # 配置飞塔特有的设置
        self._configure_fortinet_specific(physical, fortinet_config)
        
        log(_("interface_handler.fortinet_interface_created", name=interface_name))
        return physical
    
    def _add_enhanced_default_config(self, physical_elem):
        """
        添加增强的默认接口配置
        """
        # 网络栈节点
        network_stack = etree.SubElement(physical_elem, "network-stack")
        ipv4_ns = etree.SubElement(network_stack, "ipv4")
        etree.SubElement(ipv4_ns, "arp-ignore").text = "check-interface-and-subnet"
        
        # 反向路径
        etree.SubElement(physical_elem, "reverse-path").text = "true"
        
        # snooping 节点
        snooping = etree.SubElement(physical_elem, "snooping")
        etree.SubElement(snooping, "trust").text = "false"
        etree.SubElement(snooping, "suppress").text = "false"
        
        # flow-control 节点
        flow_control = etree.SubElement(physical_elem, "flow-control")
        flow_control.set("xmlns", NS_FLOW)
        etree.SubElement(flow_control, "enabled").text = "false"
        
        # monitor 节点
        monitor = etree.SubElement(physical_elem, "monitor")
        monitor.set("xmlns", NS_MONITOR)
        etree.SubElement(monitor, "notify-up-drop-rate-threshold").text = "1000"
        etree.SubElement(monitor, "notify-down-drop-rate-threshold").text = "1000"
        etree.SubElement(monitor, "if-notify-enable").text = "false"
        etree.SubElement(monitor, "notify-up-usage-threshold").text = "100"
        etree.SubElement(monitor, "notify-down-usage-threshold").text = "100"
        etree.SubElement(monitor, "notify-up-speed-threshold").text = "100000000"
        etree.SubElement(monitor, "notify-down-speed-threshold").text = "100000000"
        
        # ip-mac-bind 节点
        ip_mac_bind = etree.SubElement(physical_elem, "ip-mac-bind")
        ip_mac_bind.set("xmlns", NS_IPMAC)
        ipv4_bind = etree.SubElement(ip_mac_bind, "ipv4")
        etree.SubElement(ipv4_bind, "enabled").text = "false"
        ipv6_bind = etree.SubElement(ip_mac_bind, "ipv6")
        etree.SubElement(ipv6_bind, "enabled").text = "false"
    
    def _configure_ipv4(self, physical_elem, fortinet_config):
        """
        配置IPv4设置（支持透明模式下的管理IP）
        """
        mode = fortinet_config.get("mode", "").lower()
        interface_name = fortinet_config.get("alias", fortinet_config.get("name", ""))

        # 创建ipv4节点
        ipv4 = etree.SubElement(physical_elem, "ipv4")

        # 检查是否为透明模式下的MGMT接口
        mgmt_ip = self._get_mgmt_ip_for_interface(interface_name)

        if mgmt_ip:
            # 透明模式下的MGMT接口，使用管理IP
            etree.SubElement(ipv4, "enabled").text = "true"
            address = etree.SubElement(ipv4, "address")
            etree.SubElement(address, "ip").text = mgmt_ip
            log(_("interface_handler.configured_mgmt_ip",
                  name=interface_name, ip=mgmt_ip))

        elif mode == "dhcp" or fortinet_config.get("dhcp_enabled"):
            # DHCP模式 - 支持原始mode字段和新架构dhcp_enabled字段
            etree.SubElement(ipv4, "enabled").text = "true"
            self.interface_common.create_dhcp_config(ipv4)
            log(_("interface_handler.configured_dhcp_mode", name=interface_name))

        elif mode == "pppoe" or fortinet_config.get("pppoe_enabled"):
            # PPPoE模式 - 支持原始mode字段和新架构pppoe_enabled字段
            self._configure_pppoe_optimized(ipv4, fortinet_config)

        else:
            # 静态IP模式或默认配置
            # 支持新架构的ip_address字段和旧架构的ip/netmask字段
            ip_config = fortinet_config.get("ip")
            netmask = fortinet_config.get("netmask")
            ip_address = fortinet_config.get("ip_address")  # 新架构字段


            # 优先使用新架构的ip_address字段
            final_ip_cidr = None
            if ip_address:
                # 新架构：直接使用ip_address字段（已经是CIDR格式）
                final_ip_cidr = ip_address
            elif ip_config:
                # 旧架构：解析ip和netmask字段
                final_ip_cidr = self.parse_fortinet_ip_config(ip_config, netmask)

            if final_ip_cidr:
                etree.SubElement(ipv4, "enabled").text = "true"
                address = etree.SubElement(ipv4, "address")
                # 使用YANG兼容的CIDR格式
                etree.SubElement(address, "ip").text = final_ip_cidr
                log(f"✅ DEBUG: 成功创建address节点，IP: {final_ip_cidr}", "debug")
                log(_("interface_handler.configured_static_ip",
                      name=interface_name, ip=final_ip_cidr))
            else:
                # 默认禁用IPv4
                etree.SubElement(ipv4, "enabled").text = "false"
    
    def _configure_pppoe_optimized(self, ipv4_elem, fortinet_config):
        """
        配置优化的PPPoE设置（避免命名空间问题）
        """
        log(_("interface_handler.configuring_pppoe_optimized", 
              name=fortinet_config.get("name", "unknown")))
        
        # 启用IPv4
        etree.SubElement(ipv4_elem, "enabled").text = "true"
        
        # 创建PPPoE配置
        pppoe = etree.SubElement(ipv4_elem, "pppoe")
        connection = etree.SubElement(pppoe, "connection")
        
        # 分配隧道ID
        tunnel_id = self.interface_common.get_next_tunnel_id()
        etree.SubElement(connection, "tunnel").text = str(tunnel_id)
        etree.SubElement(connection, "enabled").text = "true"
        
        # 用户名和密码 - 支持更多字段名格式
        username = (fortinet_config.get("username") or 
                   fortinet_config.get("pppoe-username") or 
                   fortinet_config.get("pppoe_username") or
                   fortinet_config.get("user", ""))
        # 优先使用解析器提供的固定密码，确保与解析器策略一致
        password = (fortinet_config.get("pppoe_password") or  # 解析器提供的固定密码
                   fortinet_config.get("password") or
                   fortinet_config.get("pppoe-password") or
                   fortinet_config.get("pass", "123456"))  # 默认使用固定密码

        if username:
            etree.SubElement(connection, "user").text = username
            log(_("interface_handler.pppoe_username_configured", username=username))

        if password:
            # 使用解析器提供的固定密码策略，避免加密密码的兼容性问题
            etree.SubElement(connection, "password").text = password
            log(_("interface_handler.pppoe_password_configured"))
        
        # 设置连接参数
        etree.SubElement(connection, "gateway").text = "true"
        etree.SubElement(connection, "timeout").text = "5"
        etree.SubElement(connection, "retries").text = "3"
        
        # PPP参数
        ppp = etree.SubElement(connection, "ppp")
        etree.SubElement(ppp, "negotiation-timeout").text = "3"
        etree.SubElement(ppp, "lcp-echo-interval").text = "10"
        etree.SubElement(ppp, "lcp-echo-retries").text = "10"
        
        # MTU和反向路径
        etree.SubElement(connection, "ppp-mtu").text = "1492"
        etree.SubElement(connection, "reverse-path").text = "true"
        
        # 流量控制（仅在连接内部配置）
        flow_control = etree.SubElement(connection, "flow-control")
        flow_control.set("xmlns", NS_FLOW)
        etree.SubElement(flow_control, "enabled").text = "false"
        
        # PPPoE连接级别的访问控制（设为false，实际访问控制在物理接口级别）
        access_control = etree.SubElement(connection, "access-control")
        access_control.set("xmlns", NS_ACCESS)
        etree.SubElement(access_control, "https").text = "false"
        etree.SubElement(access_control, "ping").text = "false"
        etree.SubElement(access_control, "ssh").text = "false"
        
        log(_("interface_handler.pppoe_configured", tunnel_id=tunnel_id))
    
    def _configure_ipv6(self, physical_elem, fortinet_config):
        """
        配置IPv6设置
        """
        ipv6_config = fortinet_config.get("ipv6", {})
        
        # 检查是否有IPv6配置
        has_ipv6_address = ipv6_config and (ipv6_config.get("ip6-address") or ipv6_config.get("address"))
        has_ipv6_dhcp = ipv6_config and ipv6_config.get("ip6-mode") == "dhcp"
        has_ipv6_config = has_ipv6_address or has_ipv6_dhcp
        
        # 对于PPPoE接口，即使没有明确的IPv6配置，也要创建IPv6节点
        mode = fortinet_config.get("mode", "").lower()
        
        if has_ipv6_config or mode == "pppoe":
            # 创建ipv6节点
            ipv6 = etree.SubElement(physical_elem, "ipv6")
            
            # 对于PPPoE模式，特殊处理：只有静态IPv6地址才启用IPv6，DHCP模式下禁用
            if mode == "pppoe":
                if has_ipv6_address:
                    # PPPoE模式下只有静态IPv6地址才启用
                    etree.SubElement(ipv6, "enabled").text = "true"
                    
                    # 配置邻居发现协议
                    self._configure_ipv6_nd(ipv6)
                    
                    # IPv6地址配置（静态）
                    ipv6_address = ipv6_config.get("ip6-address") or ipv6_config.get("address")
                    if ipv6_address:
                        address = etree.SubElement(ipv6, "address")
                        # 使用YANG兼容的CIDR格式
                        etree.SubElement(address, "ip").text = ipv6_address
                        log(_("interface_handler.configured_ipv6_static", 
                              name=fortinet_config.get("name", "unknown"), ipv6=ipv6_address))
                else:
                    # PPPoE模式下没有静态IPv6地址或只有DHCP配置，则禁用IPv6
                    etree.SubElement(ipv6, "enabled").text = "false"
                    log(_("interface_handler.ipv6_disabled_for_pppoe", 
                          name=fortinet_config.get("name", "unknown")))
            else:
                # 非PPPoE模式，按原逻辑处理
                if has_ipv6_config:
                    etree.SubElement(ipv6, "enabled").text = "true"
                    
                    # 配置邻居发现协议
                    self._configure_ipv6_nd(ipv6)
                    
                    # IPv6地址配置（静态）
                    if has_ipv6_address:
                        ipv6_address = ipv6_config.get("ip6-address") or ipv6_config.get("address")
                        if ipv6_address:
                            address = etree.SubElement(ipv6, "address")
                            # 使用YANG兼容的CIDR格式
                            etree.SubElement(address, "ip").text = ipv6_address
                            log(_("interface_handler.configured_ipv6_static", 
                                  name=fortinet_config.get("name", "unknown"), ipv6=ipv6_address))
                    
                    # IPv6 DHCP配置
                    if has_ipv6_dhcp:
                        self._configure_ipv6_dhcp(ipv6)
                        log(_("interface_handler.configured_ipv6_dhcp", 
                              name=fortinet_config.get("name", "unknown")))
                else:
                    # 没有IPv6配置，禁用IPv6
                    etree.SubElement(ipv6, "enabled").text = "false"
    
    def _configure_ipv6_nd(self, ipv6_elem):
        """
        配置IPv6邻居发现协议
        """
        nd = etree.SubElement(ipv6_elem, "nd")
        etree.SubElement(nd, "dad-detect").text = "1"
        etree.SubElement(nd, "ns-interval").text = "1000"
        etree.SubElement(nd, "suppress-ra").text = "true"
        etree.SubElement(nd, "rs-interval").text = "3600"
        etree.SubElement(nd, "ra-interval").text = "600"
        etree.SubElement(nd, "ra-lifetime").text = "1800"
    
    def _configure_ipv6_dhcp(self, ipv6_elem):
        """
        配置IPv6 DHCP客户端
        """
        dhcp = etree.SubElement(ipv6_elem, "dhcp")
        etree.SubElement(dhcp, "enabled").text = "true"
        etree.SubElement(dhcp, "dad-wait-time").text = "1"
        etree.SubElement(dhcp, "prefix-request").text = "false"
        etree.SubElement(dhcp, "information-request").text = "false"
    
    def _configure_access_control(self, physical_elem, fortinet_config):
        """
        配置访问控制（支持透明模式下的管理协议）
        """
        interface_name = fortinet_config.get("alias", fortinet_config.get("name", ""))


        # 获取IPv4访问控制 - 优先使用处理后的access_control数据
        access_control_data = fortinet_config.get("access_control")
        if access_control_data:
            # 使用接口处理阶段处理后的访问控制数据
            # 将字典格式转换为列表格式
            allowaccess = []
            for service, enabled in access_control_data.items():
                if enabled:
                    allowaccess.append(service)
        else:
            # 回退到原始allowaccess数据
            allowaccess = fortinet_config.get("allowaccess", [])
            if isinstance(allowaccess, str):
                allowaccess = self.parse_fortinet_allowaccess(allowaccess)

        # 获取IPv6访问控制（NTOS中IPv4和IPv6共用访问控制开关）
        ipv6_config = fortinet_config.get("ipv6", {})
        ipv6_allowaccess = ipv6_config.get("ip6-allowaccess", [])
        if isinstance(ipv6_allowaccess, str):
            ipv6_allowaccess = self.parse_fortinet_allowaccess(ipv6_allowaccess)

        # 合并IPv4和IPv6的访问控制（取并集）
        combined_access = list(set(allowaccess + ipv6_allowaccess))

        # 检查是否为透明模式下的MGMT接口，如果是则添加管理协议
        if self._is_mgmt_interface_in_transparent_mode(interface_name):
            # 透明模式下的MGMT接口需要开放管理协议
            mgmt_protocols = ["ping", "https", "ssh"]
            combined_access = list(set(combined_access + mgmt_protocols))
            log(_("interface_handler.mgmt_protocols_added",
                  interface=interface_name, protocols=mgmt_protocols))
        
        # 创建访问控制节点
        access_control = etree.SubElement(physical_elem, "access-control")
        access_control.set("xmlns", NS_ACCESS)
        
        # 检查不支持的访问控制方式
        supported_access = ["ping", "https", "ssh"]
        unsupported_access = [acc for acc in combined_access if acc.lower() not in supported_access]
        if unsupported_access:
            log(_("interface_handler.warning.unsupported_allowaccess", 
                  interface=fortinet_config.get("name", "unknown"), 
                  access=", ".join(unsupported_access)), "warning")
            # 只保留支持的访问方式
            combined_access = [acc for acc in combined_access if acc.lower() in supported_access]
        
        # 设置各种访问方式（NTOS注释说明v4与v6共用此开关）
        # 将http映射到https
        if "http" in combined_access and "https" not in combined_access:
            combined_access.append("https")
        
        # DEBUG: 最终的combined_access
        log(f"DEBUG: 最终的combined_access: {combined_access}", "info")

        https_value = "true" if ("https" in combined_access or "http" in combined_access) else "false"
        ping_value = "true" if "ping" in combined_access else "false"
        ssh_value = "true" if "ssh" in combined_access else "false"

        log(f"DEBUG: 最终XML值 - https: {https_value}, ping: {ping_value}, ssh: {ssh_value}", "info")

        etree.SubElement(access_control, "https").text = https_value
        etree.SubElement(access_control, "ping").text = ping_value
        etree.SubElement(access_control, "ssh").text = ssh_value

        log(_("interface_handler.configured_access_control",
              name=fortinet_config.get("name", "unknown"), access=combined_access))
    
    def _configure_fortinet_specific(self, physical_elem, fortinet_config):
        """
        配置飞塔特有的设置
        """
        # 处理设备识别
        device_identification = fortinet_config.get("device_identification", 
                                                   fortinet_config.get("device-identification"))
        if device_identification:
            # 在NTOS中，设备识别可能对应于某些监控功能
            log(_("interface_handler.device_identification_noted", 
                  name=fortinet_config.get("name", "unknown")))
        
        # 处理安全模式
        security_mode = fortinet_config.get("security_mode", 
                                          fortinet_config.get("security-mode"))
        if security_mode:
            # 在NTOS中，安全模式可能需要特殊处理
            log(_("interface_handler.security_mode_noted", 
                  name=fortinet_config.get("name", "unknown"), mode=security_mode))
        
        # 处理其他飞塔特有配置
        fortinet_raw_config = fortinet_config.get("fortinet_config", {})
        if fortinet_raw_config:
            log(_("interface_handler.fortinet_raw_config_processed", 
                  name=fortinet_config.get("name", "unknown"), 
                  count=len(fortinet_raw_config)))

    def _configure_bandwidth(self, physical_elem, fortinet_config):
        """
        配置上行和下行带宽
        支持新架构处理后的带宽字段格式和原始FortiGate字段格式
        """
        # 上行带宽配置 - 优先使用新架构处理后的格式
        upload_bandwidth_config = fortinet_config.get("upload_bandwidth")
        if upload_bandwidth_config and isinstance(upload_bandwidth_config, dict):
            # 新架构格式: {'value': 500000, 'unit': 'kbps'}
            bandwidth_value = upload_bandwidth_config.get("value", 0)
            bandwidth_unit = upload_bandwidth_config.get("unit", "kbps")

            upload_bandwidth = etree.SubElement(physical_elem, "upload-bandwidth")
            etree.SubElement(upload_bandwidth, "upload-bandwidth-value").text = str(bandwidth_value)
            etree.SubElement(upload_bandwidth, "upload-bandwidth-unit").text = bandwidth_unit
            log(_("interface_handler.configured_upload_bandwidth",
                  name=fortinet_config.get("name", "unknown"), bandwidth=bandwidth_value))
        else:
            # 兼容原始FortiGate字段格式
            upstream_bandwidth = fortinet_config.get("estimated-upstream-bandwidth",
                                                    fortinet_config.get("estimated_upstream_bandwidth"))
            if upstream_bandwidth:
                upload_bandwidth = etree.SubElement(physical_elem, "upload-bandwidth")
                # 转换为kbps单位（飞塔的带宽可能以不同单位存储）
                bandwidth_value = int(upstream_bandwidth) * 1000 if upstream_bandwidth else 0
                etree.SubElement(upload_bandwidth, "upload-bandwidth-value").text = str(bandwidth_value)
                etree.SubElement(upload_bandwidth, "upload-bandwidth-unit").text = "kbps"
                log(_("interface_handler.configured_upload_bandwidth",
                      name=fortinet_config.get("name", "unknown"), bandwidth=bandwidth_value))

        # 下行带宽配置 - 优先使用新架构处理后的格式
        download_bandwidth_config = fortinet_config.get("download_bandwidth")
        if download_bandwidth_config and isinstance(download_bandwidth_config, dict):
            # 新架构格式: {'value': 600000, 'unit': 'kbps'}
            bandwidth_value = download_bandwidth_config.get("value", 0)
            bandwidth_unit = download_bandwidth_config.get("unit", "kbps")

            download_bandwidth = etree.SubElement(physical_elem, "download-bandwidth")
            etree.SubElement(download_bandwidth, "download-bandwidth-value").text = str(bandwidth_value)
            etree.SubElement(download_bandwidth, "download-bandwidth-unit").text = bandwidth_unit
            log(_("interface_handler.configured_download_bandwidth",
                  name=fortinet_config.get("name", "unknown"), bandwidth=bandwidth_value))
        else:
            # 兼容原始FortiGate字段格式
            downstream_bandwidth = fortinet_config.get("estimated-downstream-bandwidth",
                                                      fortinet_config.get("estimated_downstream_bandwidth"))
            if downstream_bandwidth:
                download_bandwidth = etree.SubElement(physical_elem, "download-bandwidth")
                # 转换为kbps单位
                bandwidth_value = int(downstream_bandwidth) * 1000 if downstream_bandwidth else 0
                etree.SubElement(download_bandwidth, "download-bandwidth-value").text = str(bandwidth_value)
                etree.SubElement(download_bandwidth, "download-bandwidth-unit").text = "kbps"
                log(_("interface_handler.configured_download_bandwidth",
                      name=fortinet_config.get("name", "unknown"), bandwidth=bandwidth_value))

# 测试函数
def test_fortinet_optimization():
    """测试飞塔配置转换优化"""
    print("=== 飞塔配置转换优化测试 ===")
    
    # 创建处理器
    handler = FortinetPhysicalInterfaceHandler()
    
    # 测试IP地址解析
    print("\n1. 测试IP地址解析:")
    test_cases = [
        ("***********", "*************"),
        ("********", "*********"), 
        ("**********", "***********"),
        ("***********/24", None)
    ]
    
    for ip, mask in test_cases:
        result = handler.parse_fortinet_ip_config(ip, mask)
        print(f"  {ip} + {mask} -> {result}")
    
    # 测试allowaccess解析
    print("\n2. 测试allowaccess解析:")
    access_tests = [
        "ping https ssh",
        ["ping", "https", "ssh"],
        "ping,https,ssh",
        ""
    ]
    
    for access in access_tests:
        result = handler.parse_fortinet_allowaccess(access)
        print(f"  '{access}' -> {result}")
    
    print("\n测试完成！")

if __name__ == "__main__":
    test_fortinet_optimization()
