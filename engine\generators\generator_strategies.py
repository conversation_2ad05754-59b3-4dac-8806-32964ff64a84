#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
from lxml import etree
from engine.utils.logger import log, user_log
from engine.utils.i18n import _
from engine.generators.xml_handlers import XMLNamespaceHandler
from engine.processors.processor_manager import ProcessorManager
import re


class GeneratorStrategy:
    """配置生成策略基类"""
    
    def __init__(self, model=None, version=None):
        """
        初始化生成策略
        
        Args:
            model (str, optional): 设备型号
            version (str, optional): 设备版本
        """
        self.model = model
        self.version = version
    
    def generate(self, config_data, template_path):
        """
        生成配置的抽象方法
        
        Args:
            config_data (dict): 配置数据
            template_path (str): 模板文件路径
            
        Returns:
            tuple: (XML配置内容, 验证报告)
        """
        raise NotImplementedError("子类必须实现generate方法")


class YangGeneratorStrategy:
    """基于YANG模型的生成策略"""
    
    def __init__(self, model=None, version=None):
        """
        初始化YANG生成策略
        
        Args:
            model (str, optional): 设备型号
            version (str, optional): 设备版本
        """
        self.model = model
        self.version = version
    
    def generate(self, config_data, template_path):
        """
        基于YANG模型生成配置
        
        Args:
            config_data (dict): 配置数据
            template_path (str): 模板文件路径
            
        Returns:
            tuple: (XML配置内容, 验证报告)
        """
        # 记录生成开始
        log(_("info.start_yang_generation"))
        user_log(_("info.generating_using_yang_model"))
        
        # 加载模板XML
        try:
            tree = etree.parse(template_path)
            root = tree.getroot()
        except Exception as e:
            error_msg = _("error.template_parsing_failed", error=str(e))
            log(error_msg, "error")
            user_log(_("error.template_parsing_failed_user"), "error")
            raise ValueError(error_msg)
        
        try:
            # 确保接口数据格式正确
            if "interfaces" in config_data and isinstance(config_data["interfaces"], list):
                interfaces_dict = {}
                for intf in config_data["interfaces"]:
                    if isinstance(intf, dict) and "name" in intf:
                        interfaces_dict[intf["name"]] = intf
                    elif isinstance(intf, dict) and "raw_name" in intf:
                        interfaces_dict[intf["raw_name"]] = intf
                config_data["interfaces"] = interfaces_dict
            
            # 创建处理器管理器
            processor_manager = ProcessorManager()
            
            # 使用处理器管理器处理所有配置
            stats = processor_manager.process_all(root, config_data)
            
            # 创建验证报告
            validation_report = self._create_validation_report(stats)
            
            # 格式化并返回XML
            xml_string = etree.tostring(root, pretty_print=True, encoding='utf-8').decode('utf-8')
            
            # 记录生成完成
            log(_("info.yang_generation_complete"))
            user_log(_("info.generation_complete"))
            
            return xml_string, validation_report
            
        except Exception as e:
            error_msg = _("error.generation_failed", error=str(e))
            log(error_msg, "error")
            # 生成一个最小的有效结构作为应急措施
            log(_("info.yang_generation_fallback"))
            minimal_xml = '<?xml version="1.0" encoding="UTF-8"?>\n<config xmlns="urn:ruijie:ntos"><vrf><name>main</name></vrf></config>'
            
            # 构建错误验证报告
            error_report = {
                "error_count": 1,
                "warning_count": 0,
                "is_valid": False,
                "mode": "yang",
                "errors": [{"message": str(e), "line": 0, "column": 0}]
            }
            
            return minimal_xml, error_report
    
    def _create_validation_report(self, stats):
        """
        创建验证报告
        
        Args:
            stats (dict): 处理统计信息
            
        Returns:
            dict: 验证报告
        """
        # 统计错误和警告
        error_count = 0
        warning_count = 0
        
        # 检查接口处理结果
        if "interfaces" in stats:
            intf_stats = stats["interfaces"]
            if intf_stats["skipped"] > 0:
                warning_count += 1
            if intf_stats["invalid_interfaces"]:
                error_count += len(intf_stats["invalid_interfaces"])
        
        # 检查地址对象处理结果
        if "address_objects" in stats:
            addr_stats = stats["address_objects"]
            if addr_stats["skipped"] > 0:
                warning_count += 1
        
        # 检查服务对象处理结果
        if "service_objects" in stats:
            svc_stats = stats["service_objects"]
            if svc_stats["skipped"] > 0:
                warning_count += 1
        
        # 检查策略规则处理结果
        if "policy_rules" in stats:
            policy_stats = stats["policy_rules"]
            if policy_stats["skipped"] > 0:
                warning_count += 1
            if policy_stats["invalid_rules"]:
                error_count += len(policy_stats["invalid_rules"])
        
        # 检查区域处理结果
        if "zones" in stats:
            zone_stats = stats["zones"]
            if zone_stats["skipped"] > 0:
                warning_count += 1
            if zone_stats["invalid_zones"]:
                error_count += len(zone_stats["invalid_zones"])
        
        # 检查静态路由处理结果
        if "static_routes" in stats:
            route_stats = stats["static_routes"]
            if route_stats["skipped"] > 0:
                warning_count += 1
            if route_stats["invalid_routes"]:
                error_count += len(route_stats["invalid_routes"])
        
        # 创建验证报告
        validation_report = {
            "is_valid": error_count == 0,
            "error_count": error_count,
            "warning_count": warning_count,
            "stats": stats
        }
        
        return validation_report


class LegacyGeneratorStrategy(GeneratorStrategy):
    """传统XML生成策略"""
    
    def generate(self, config_data, template_path):
        """
        使用改进版传统方式生成XML配置
        
        Args:
            config_data (dict): 配置数据
            template_path (str): 模板文件路径
            
        Returns:
            tuple: (XML配置内容, 验证报告)
        """
        log(_("info.using_legacy_generation_strategy"))
        
        # 导入ImprovedLegacyGenerator
        from engine.generators.improved_legacy_generator import ImprovedLegacyGenerator
        
        # 检查模板文件
        if not template_path or not os.path.exists(template_path):
            error_msg = _("error.template_not_exists")
            log(error_msg, "error")
            user_log(_("error.cannot_load_template"), "error")
            raise FileNotFoundError(error_msg)
        
        try:
            # 创建ImprovedLegacyGenerator实例
            generator = ImprovedLegacyGenerator("default", "default", template_path)
            
            # 设置接口
            if "interfaces" in config_data:
                interface_mapping = config_data.get("interface_mapping", {})
                generator.set_interfaces(config_data["interfaces"], interface_mapping)
            
            # 设置地址对象
            if "address_objects" in config_data:
                generator.set_address_objects(config_data["address_objects"])
            
            # 设置服务对象
            if "service_objects" in config_data:
                generator.set_service_objects(config_data["service_objects"])
            
            # 设置策略规则
            if "policy_rules" in config_data:
                generator.set_policy_rules(config_data["policy_rules"])
            
            # 设置区域
            if "zones" in config_data:
                generator.set_zones(config_data["zones"])
            
            # 设置静态路由
            if "static_routes" in config_data:
                generator.set_static_routes(config_data["static_routes"], config_data.get("static_routes_ipv6", []))
            
            # 设置地址组
            if "address_groups" in config_data:
                generator.set_address_groups(config_data["address_groups"])
            
            # 设置服务组
            if "service_groups" in config_data:
                generator.set_service_groups(config_data["service_groups"])
            
            # 生成XML
            xml_content, stats = generator.generate()
            
            # 构建验证报告
            validation_report = {
                "error_count": 0,
                "warning_count": 0,
                "is_valid": True,
                "mode": "improved_legacy"  # 更改模式标识
            }
            
            log(_("info.legacy_xml_generation_complete"))
            return xml_content, validation_report
            
        except Exception as e:
            error_msg = _("error.legacy_generation_failed", error=str(e))
            log(error_msg, "error")
            # 生成一个最小的有效结构作为应急措施
            log(_("info.legacy_generation_fallback"))
            minimal_xml = '<?xml version="1.0" encoding="UTF-8"?>\n<config xmlns="urn:ruijie:ntos"><vrf><name>main</name></vrf></config>'
            
            # 构建错误验证报告
            error_report = {
                "error_count": 1,
                "warning_count": 0,
                "is_valid": False,
                "mode": "improved_legacy",  # 更改模式标识
                "errors": [{"message": str(e), "line": 0, "column": 0}]
            }
            
            log(_("info.legacy_generation_complete"))
            return minimal_xml, error_report


class BatchGeneratorStrategy(GeneratorStrategy):
    """批处理生成策略，适用于大型配置"""
    
    def __init__(self, model=None, version=None, batch_size=100):
        """
        初始化批处理生成策略
        
        Args:
            model (str, optional): 设备型号
            version (str, optional): 设备版本
            batch_size (int): 每批处理的项目数量
        """
        super().__init__(model, version)
        self.batch_size = batch_size
        self.base_strategy = YangGeneratorStrategy(model, version)
    
    def generate(self, config_data, template_path):
        """
        使用批处理方式生成XML配置
        
        Args:
            config_data (dict): 配置数据
            template_path (str): 模板文件路径
            
        Returns:
            tuple: (XML配置内容, 验证报告)
        """
        log(_("info.using_batch_generation_strategy", batch_size=self.batch_size))
        
        # 将配置数据分批处理
        batched_data = self._split_into_batches(config_data)
        
        # 合并所有批次的结果
        try:
            final_xml = None
            combined_report = {
                "error_count": 0,
                "warning_count": 0,
                "is_valid": True,
                "batches": []
            }
            
            for batch_index, batch in enumerate(batched_data):
                log(_("info.processing_batch", index=batch_index+1, total=len(batched_data)))
                
                # 使用基础策略处理每个批次
                xml_content, validation_report = self.base_strategy.generate(batch, template_path)
                
                # 第一个批次的结果作为基础
                if final_xml is None:
                    final_xml = xml_content
                else:
                    # 合并XML（需要实现合并逻辑）
                    final_xml = self._merge_xml(final_xml, xml_content)
                
                # 更新合并的验证报告
                combined_report["error_count"] += validation_report.get("error_count", 0)
                combined_report["warning_count"] += validation_report.get("warning_count", 0)
                combined_report["is_valid"] = combined_report["is_valid"] and validation_report.get("is_valid", True)
                combined_report["batches"].append({
                    "index": batch_index + 1,
                    "error_count": validation_report.get("error_count", 0),
                    "warning_count": validation_report.get("warning_count", 0),
                    "is_valid": validation_report.get("is_valid", True)
                })
            
            log(_("info.batch_processing_complete", batches=len(batched_data)))
            return final_xml, combined_report
            
        except Exception as e:
            log(_("error.batch_processing_failed", error=str(e)), "error")
            # 退回到非批处理策略
            log(_("info.fallback_to_non_batch_strategy"))
            return self.base_strategy.generate(config_data, template_path)
    
    def _split_into_batches(self, config_data):
        """
        将配置数据分割为多个批次
        
        Args:
            config_data (dict): 配置数据
            
        Returns:
            list: 批次列表
        """
        batches = []
        
        # 创建基础批次模板
        batch_template = {k: [] if isinstance(v, list) else {} for k, v in config_data.items()}
        
        # 分割地址对象
        if "address_objects" in config_data and isinstance(config_data["address_objects"], list):
            address_objects = config_data["address_objects"]
            for i in range(0, len(address_objects), self.batch_size):
                # 创建新批次
                new_batch = batch_template.copy()
                # 添加当前批次的地址对象
                new_batch["address_objects"] = address_objects[i:i+self.batch_size]
                batches.append(new_batch)
        
        # TODO: 实现其他配置项的批处理分割
        
        # 如果没有创建任何批次，创建一个包含全部数据的批次
        if not batches:
            batches = [config_data]
        
        return batches
    
    def _merge_xml(self, base_xml, new_xml):
        """
        合并两个XML字符串
        
        Args:
            base_xml (str): 基础XML
            new_xml (str): 新XML
            
        Returns:
            str: 合并后的XML
        """
        # TODO: 实现XML合并逻辑
        # 这需要解析XML，找到相应的节点，合并内容，然后重新序列化
        # 简单实现：使用第一个批次的XML
        return base_xml