#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
改进版本的LegacyGenerator
专注于更精确地处理服务对象
"""

import json
import os
from engine.generators.legacy_generator import LegacyGenerator
from lxml import etree
from engine.utils.logger import log
from engine.utils.i18n import _

class ImprovedLegacyGenerator(LegacyGenerator):
    """改进版本的LegacyGe            if not os.path.exists(mapping_file):
                log(_("warning.service_mapping_config_file_not_found", file=mapping_file), "warning")
                return {}
            
            with open(mapping_file, 'r', encoding='utf-8') as f:
                mapping = json.load(f)
                log(_("info.service_mapping_config_loaded", count=len(mapping)), "info")
                return mapping
        except Exception as e:
            log(_("error.load_service_mapping_config_failed", error=str(e)), "error")确地处理服务对象"""
    
    def __init__(self, model=None, version=None, template_path=None, transparent_mode_result=None):
        """
        初始化改进版配置生成器

        Args:
            model (str, optional): 设备型号，如 'z5100s'
            version (str, optional): 设备版本，如 'R10P2'
            template_path (str, optional): 模板文件路径
            transparent_mode_result (dict, optional): 透明模式处理结果，用于桥接接口生成
        """
        # 如果没有提供参数，使用默认值
        model = model or "default"
        version = version or "default"
        template_path = template_path or ""

        # 调用父类初始化，传递透明模式结果
        super().__init__(model, version, template_path, transparent_mode_result)
        
        # 加载服务映射配置
        self.service_mapping = self._load_service_mapping()
        # 存储NTOS预定义服务映射关系（不生成XML）
        self.ntos_predefined_services = {}
        
        log(_("info.init_improved_legacy_generator", model=model, version=version))
    
    def set_time_ranges(self, time_ranges):
        """设置时间对象"""
        super().set_time_ranges(time_ranges)
        log(_("info.improved_set_time_ranges", count=len(time_ranges)))
    
    def _add_service_objects_to_xml(self, vrf, nsmap):
        """
        添加服务对象到XML，正确处理FortiGate服务对象格式
        
        Args:
            vrf: VRF XML元素节点
            nsmap: 命名空间映射（为了兼容父类）
            
        Returns:
            int: 处理的服务对象数量
        """
        processed = 0
        skipped = 0
        
        log(_("info.improved_legacy_generator_processing_services", count=len(self.service_objects)), "debug")
        
        # 首先进行服务对象去重，确保输入数据的唯一性
        unique_services = []
        seen_service_names = set()
        
        for service in self.service_objects:
            service_name = service.get("name")
            if not service_name:
                log(_("warning.improved_legacy_generator_skip_unnamed_service", service=service), "warning")
                skipped += 1
                continue
                
            if service_name in seen_service_names:
                log(_("warning.improved_legacy_generator_skip_duplicate_service", name=service_name), "warning")
                skipped += 1
                continue
                
            seen_service_names.add(service_name)
            unique_services.append(service)
            log(_("debug.improved_legacy_generator_add_unique_service", name=service_name), "debug")
        
        log(_("debug.improved_legacy_generator_dedup_result", unique=len(unique_services), total=len(self.service_objects)), "debug")
        
        # 更新service_objects为去重后的版本
        self.service_objects = unique_services
        
        # 服务对象命名空间
        service_ns = "urn:ruijie:ntos:params:xml:ns:yang:service-obj"
        
        # 查找或创建服务对象容器
        service_obj_xpath = ".//*[local-name()='service-obj']"
        service_obj_nodes = vrf.xpath(service_obj_xpath)
        
        if service_obj_nodes:
            service_obj = service_obj_nodes[0]
            log(_("info.found_existing_service_obj_node"))
        else:
            service_obj = etree.SubElement(vrf, "service-obj")
            service_obj.set("xmlns", service_ns)
            log(_("info.created_service_obj_node"))
        
        # 收集现有服务集合，避免重复
        existing_services = {}
        for service_set in service_obj.xpath(".//*[local-name()='service-set']"):
            name_elem = service_set.xpath(".//*[local-name()='name']")
            if name_elem and len(name_elem) > 0:
                existing_services[name_elem[0].text] = service_set
                
        log(_("debug.improved_legacy_generator_existing_services", count=len(existing_services)), "debug")
        
        # 不支持的协议列表
        unsupported_protocols = ["udp-lite", "sctp", "icmp6"]
        
        # 处理每个服务对象
        for service in self.service_objects:
            try:
                # 获取服务名称
                service_name = service.get("name")
                if not service_name:
                    log("跳过无名称的服务对象", "warning")
                    skipped += 1
                    continue
                
                log(_("debug.improved_legacy_generator_start_processing_service", name=service_name, service=service), "debug")
                
                # 检查是否为NTOS预定义服务
                ntos_mapping = self._get_ntos_service_mapping(service_name)
                if ntos_mapping and "ntos_service" in ntos_mapping:
                    # 这是NTOS预定义服务，只保存映射关系，不生成XML
                    original_description = service.get("comment", "")  # 使用飞塔配置中的描述
                    self.ntos_predefined_services[service_name] = {
                        "ntos_service": ntos_mapping["ntos_service"],
                        "original_description": original_description,
                        "fortigate_service": service
                    }
                    log(_("info.ntos_predefined_service_mapped", 
                          fortigate_name=service_name, 
                          ntos_name=ntos_mapping["ntos_service"],
                          description=original_description), "info")
                    processed += 1
                    continue
                
                # 检查协议支持情况
                protocol = service.get("protocol", "").lower()
                
                # 检查是否有FortiGate格式的端口配置
                has_tcp_portrange = "tcp-portrange" in service and service["tcp-portrange"]
                has_udp_portrange = "udp-portrange" in service and service["udp-portrange"] 
                has_tcp_ports = "tcp_ports" in service and service["tcp_ports"]
                has_udp_ports = "udp_ports" in service and service["udp_ports"]
                has_port = "port" in service and service["port"]
                
                log(_("debug.improved_legacy_generator_port_config", 
                      name=service_name, 
                      tcp_portrange=has_tcp_portrange, 
                      udp_portrange=has_udp_portrange, 
                      tcp_ports=has_tcp_ports, 
                      udp_ports=has_udp_ports, 
                      port=has_port), "debug")
                
                # 处理FortiGate的ALL协议 
                if protocol == "all":
                    # ALL协议根据端口配置和服务类型推断具体协议
                    if (has_tcp_portrange or has_tcp_ports) and (has_udp_portrange or has_udp_ports):
                        protocol = "tcp_udp"
                        service["protocol"] = protocol
                        log(_("debug.improved_legacy_generator_inferred_protocol_from_all", name=service_name, protocol=protocol), "debug")
                    elif has_tcp_portrange or has_tcp_ports:
                        # 对于只有TCP端口的ALL协议，检查服务类型
                        # Web代理等服务只使用TCP协议
                        if service_name.lower() in ["webproxy", "web-proxy", "http-proxy", "https-proxy"]:
                            protocol = "tcp"
                            service["protocol"] = protocol
                            log(_("debug.improved_legacy_generator_inferred_tcp_only_from_all", name=service_name, protocol=protocol), "debug")
                        else:
                            # 其他服务默认推断为TCP
                            protocol = "tcp"
                            service["protocol"] = protocol
                            log(_("debug.improved_legacy_generator_inferred_protocol_from_all", name=service_name, protocol=protocol), "debug")
                    elif has_udp_portrange or has_udp_ports:
                        protocol = "udp"
                        service["protocol"] = protocol
                        log(_("debug.improved_legacy_generator_inferred_protocol_from_all", name=service_name, protocol=protocol), "debug")
                    else:
                        # ALL协议默认为TCP（大多数应用服务）
                        protocol = "tcp"
                        service["protocol"] = protocol
                        log(_("debug.improved_legacy_generator_default_protocol_from_all", name=service_name, protocol=protocol), "debug")
                elif not protocol:
                    # 如果没有指定协议，根据端口范围自动推断
                    if (has_tcp_portrange or has_tcp_ports) and (has_udp_portrange or has_udp_ports):
                        protocol = "tcp_udp"
                        service["protocol"] = protocol
                        log(_("debug.improved_legacy_generator_inferred_protocol", name=service_name, protocol=protocol), "debug")
                    elif has_tcp_portrange or has_tcp_ports:
                        protocol = "tcp"
                        service["protocol"] = protocol
                        log(_("debug.improved_legacy_generator_inferred_protocol", name=service_name, protocol=protocol), "debug")
                    elif has_udp_portrange or has_udp_ports:
                        protocol = "udp"
                        service["protocol"] = protocol
                        log(_("debug.improved_legacy_generator_inferred_protocol", name=service_name, protocol=protocol), "debug")
                    else:
                        # 默认协议类型
                        protocol = "tcp"
                        service["protocol"] = protocol
                        log(_("debug.improved_legacy_generator_default_protocol", name=service_name, protocol=protocol), "debug")
                
                if protocol in unsupported_protocols:
                    log(_("warning.improved_legacy_generator_unsupported_protocol", name=service_name, protocol=protocol), "warning")
                    skipped += 1
                    continue
                
                # 处理FQDN地址（不支持）
                if "fqdn" in service:
                    log(_("warning.improved_legacy_generator_fqdn_not_supported", name=service_name, fqdn=service['fqdn']), "warning")
                    skipped += 1
                    continue
                
                # 处理IP范围（转换为address对象）
                if "iprange" in service:
                    log(_("warning.improved_legacy_generator_iprange_not_service", name=service_name, iprange=service['iprange']), "warning")
                    skipped += 1
                    continue
                
                # 检查是否需要端口配置（TCP/UDP协议需要端口配置，IP和ICMP不需要）
                if (not has_tcp_portrange and not has_udp_portrange and not has_tcp_ports and not has_udp_ports and not has_port) and protocol in ["tcp", "udp", "tcp_udp"]:
                    log(_("warning.improved_legacy_generator_missing_port_config", name=service_name, protocol=protocol), "warning")
                    skipped += 1
                    continue
                
                # 检查服务是否已存在
                if service_name in existing_services:
                    service_set = existing_services[service_name]
                    log(_("debug.improved_legacy_generator_service_exists", name=service_name), "debug")
                    
                    # 清除现有协议配置
                    for protocol_elem in service_set.xpath(".//*[local-name()='tcp' or local-name()='udp' or local-name()='icmp' or local-name()='protocol-id']"):
                        service_set.remove(protocol_elem)
                else:
                    # 创建新的service-set元素
                    service_set = etree.SubElement(service_obj, "service-set")
                    etree.SubElement(service_set, "name").text = service_name
                
                # 处理TCP端口 - 优先使用FortiGate格式
                if has_tcp_portrange:
                    try:
                        tcp_port_info = self._convert_fortigate_port_range(service["tcp-portrange"])
                        if tcp_port_info:
                            tcp_elem = etree.SubElement(service_set, "tcp")
                            if "source_port" in tcp_port_info:
                                etree.SubElement(tcp_elem, "source-port").text = tcp_port_info["source_port"]
                            if "dest_port" in tcp_port_info:
                                etree.SubElement(tcp_elem, "dest-port").text = tcp_port_info["dest_port"]
                            log(_("debug.improved_legacy_generator_tcp_ports_fortigate", name=service_name, ports=str(tcp_port_info)), "debug")
                    except Exception as e:
                        log(_("error.improved_legacy_generator_tcp_port_processing_failed", name=service_name, error=str(e)), "error")
                        # 继续处理其他端口，不让单个端口错误影响整个服务对象
                elif has_tcp_ports:
                    tcp_ports = self._convert_port_range(service["tcp_ports"])
                    tcp_elem = etree.SubElement(service_set, "tcp")
                    etree.SubElement(tcp_elem, "dest-port").text = tcp_ports
                    log(_("debug.improved_legacy_generator_tcp_ports", name=service_name, ports=tcp_ports), "debug")
                elif has_port and protocol in ["tcp", "tcp_udp"]:
                    # 使用通用端口字段
                    tcp_ports = self._convert_port_range(service["port"])
                    tcp_elem = etree.SubElement(service_set, "tcp")
                    etree.SubElement(tcp_elem, "dest-port").text = tcp_ports
                    log(_("debug.improved_legacy_generator_tcp_ports_generic", name=service_name, ports=tcp_ports), "debug")
                
                # 处理UDP端口 - 优先使用FortiGate格式
                if has_udp_portrange:
                    try:
                        udp_port_info = self._convert_fortigate_port_range(service["udp-portrange"])
                        if udp_port_info:
                            udp_elem = etree.SubElement(service_set, "udp")
                            if "source_port" in udp_port_info:
                                etree.SubElement(udp_elem, "source-port").text = udp_port_info["source_port"]
                            if "dest_port" in udp_port_info:
                                etree.SubElement(udp_elem, "dest-port").text = udp_port_info["dest_port"]
                            log(_("debug.improved_legacy_generator_udp_ports_fortigate", name=service_name, ports=str(udp_port_info)), "debug")
                    except Exception as e:
                        log(_("error.improved_legacy_generator_udp_port_processing_failed", name=service_name, error=str(e)), "error")
                        # 继续处理其他端口，不让单个端口错误影响整个服务对象
                elif has_udp_ports:
                    udp_ports = self._convert_port_range(service["udp_ports"])
                    udp_elem = etree.SubElement(service_set, "udp")
                    etree.SubElement(udp_elem, "dest-port").text = udp_ports
                    log(_("debug.improved_legacy_generator_udp_ports", name=service_name, ports=udp_ports), "debug")
                elif has_port and protocol in ["udp", "tcp_udp"] and not (has_tcp_portrange or has_tcp_ports):
                    # 使用通用端口字段，但要避免重复
                    udp_ports = self._convert_port_range(service["port"])
                    udp_elem = etree.SubElement(service_set, "udp")
                    etree.SubElement(udp_elem, "dest-port").text = udp_ports
                    log(_("debug.improved_legacy_generator_udp_ports_generic", name=service_name, ports=udp_ports), "debug")
                elif protocol == "tcp_udp" and has_tcp_portrange and not has_udp_portrange:
                    # 对于tcp_udp协议但只有tcp-portrange的情况，UDP使用相同的端口范围
                    tcp_port_info = self._convert_fortigate_port_range(service["tcp-portrange"])
                    if tcp_port_info:
                        udp_elem = etree.SubElement(service_set, "udp")
                        if "source_port" in tcp_port_info:
                            etree.SubElement(udp_elem, "source-port").text = tcp_port_info["source_port"]
                        if "dest_port" in tcp_port_info:
                            etree.SubElement(udp_elem, "dest-port").text = tcp_port_info["dest_port"]
                        log(_("debug.improved_legacy_generator_udp_ports_from_tcp", name=service_name, ports=str(tcp_port_info)), "debug")
                
                # 处理IP协议号
                if protocol == "ip" and "protocol-number" in service:
                    protocol_id_elem = etree.SubElement(service_set, "protocol-id")
                    protocol_id_elem.text = service["protocol-number"]
                    log(_("debug.improved_legacy_generator_added_ip_protocol"), "debug",
                        name=service_name, protocol_number=service['protocol-number'])
                
                # 处理ICMP类型和代码
                if protocol == "icmp":
                    icmp_type = service.get("icmp_type", "0")
                    icmp_code = service.get("icmp_code", "0")
                    
                    icmp_item = etree.SubElement(service_set, "icmp")
                    type_elem = etree.SubElement(icmp_item, "type")
                    type_elem.text = icmp_type
                    code_elem = etree.SubElement(icmp_item, "code")
                    code_elem.text = icmp_code
                    log(_("debug.improved_legacy_generator_added_icmp"), "debug",
                        name=service_name, icmp_type=icmp_type, icmp_code=icmp_code)
                
                # 添加注释（如果有）
                if "comment" in service and service["comment"]:
                    desc_elem = etree.SubElement(service_set, "description")
                    desc_elem.text = service["comment"].strip('"')
                    log(_("debug.improved_legacy_generator_added_description"), "debug",
                        name=service_name, description=service['comment'])
                elif "description" in service and service["description"]:
                    desc_elem = etree.SubElement(service_set, "description")
                    desc_elem.text = service["description"]
                    log(_("debug.improved_legacy_generator_added_description"), "debug",
                        name=service_name, description=service['description'])
                
                log(_("debug.improved_legacy_generator_service_processed_success"), "debug", name=service_name)
                processed += 1
                
            except Exception as e:
                log(f"ImprovedLegacyGenerator服务对象 {service.get('name', 'unknown')} 处理失败，错误: {str(e)}", "error")
                skipped += 1
        
        log(_("info.improved_legacy_generator_processing_complete", success=processed, skipped=skipped))
        
        # 检查是否有服务对象被添加到XML中
        service_sets = service_obj.xpath(".//*[local-name()='service-set']")
        log(_("debug.improved_legacy_generator_xml_service_sets_count"), "debug", count=len(service_sets))
        for i, service_set in enumerate(service_sets):
            name_elem = service_set.xpath(".//*[local-name()='name']")
            if name_elem and len(name_elem) > 0:
                log(_("debug.improved_legacy_generator_xml_service_set"), "debug",
                    index=i+1, name=name_elem[0].text)
        
        # 检查服务对象节点状态
        service_obj_nodes = vrf.xpath(".//*[local-name()='service-obj']")
        if not service_obj_nodes:
            log(_("warning.generator_no_service_object_node"), "warning")
        else:
            service_obj = service_obj_nodes[0]
            service_sets = service_obj.xpath(".//*[local-name()='service-set']")
            if len(service_sets) == 0:
                log(_("warning.generator_service_object_node_empty"), "warning")
                vrf.remove(service_obj)
                log("已移除空的服务对象节点", "debug")
            else:
                log(_("debug.improved_legacy_generator_keeping_service_obj_node"), "debug", count=len(service_sets))
        
        # 清理命名空间
        if service_obj_nodes:
            etree.cleanup_namespaces(service_obj_nodes[0])
        
        log(_("info.service_objects_processing_complete", 
              total=len(self.service_objects), 
              success=processed, 
              failed=skipped, 
              skipped=skipped))
        return processed  # 返回处理的数量，与父类保持一致
    
    def _load_service_mapping(self):
        """
        加载服务映射配置文件
        
        Returns:
            dict: 服务映射配置
        """
        try:
            # 获取当前文件的目录
            current_dir = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
            mapping_file = os.path.join(current_dir, "engine", "data", "mappings", "service_mapping.json")
            
            if not os.path.exists(mapping_file):
                log(_("warning.service_mapping_file_not_found", file=mapping_file), "warning")
                return {}
            
            with open(mapping_file, 'r', encoding='utf-8') as f:
                mapping = json.load(f)
                log(_("info.service_mapping_loaded", count=len(mapping)), "info")
                return mapping
        except Exception as e:
            log(_("error.load_service_mapping_failed", error=str(e)), "error")
            return {}
    
    def _get_ntos_service_mapping(self, fortigate_service_name):
        """
        获取FortiGate服务到NTOS服务的映射
        
        Args:
            fortigate_service_name (str): FortiGate服务名称
            
        Returns:
            dict: 映射信息，包含ntos_service字段表示是否为NTOS预定义服务
        """
        # 转换为小写进行匹配
        service_name_lower = fortigate_service_name.lower()
        
        # 直接查找服务名
        if service_name_lower in self.service_mapping:
            return self.service_mapping[service_name_lower]
        
        # 查找别名
        for service_key, service_config in self.service_mapping.items():
            if "aliases" in service_config:
                for alias in service_config["aliases"]:
                    if alias.lower() == service_name_lower:
                        return service_config
        
        return None

    def _convert_fortigate_port_range(self, port_range_input):
        """
        转换FortiGate格式的端口范围到NTOS格式

        FortiGate格式示例：
        - "80" -> {"dest_port": "80"}
        - "80 443" -> {"dest_port": "80,443"}
        - "80:1024" -> {"source_port": "80", "dest_port": "1024"}
        - "0-65535:0-65535" -> {"source_port": "0-65535", "dest_port": "0-65535"}
        - "80-87:1-1024 110:1-2048" -> {"dest_port": "80-87:1-1024,110:1-2048"} (复杂格式保持原样)
        - "88 464" -> {"dest_port": "88,464"}

        Args:
            port_range_input (str|dict): FortiGate格式的端口范围字符串或字典

        Returns:
            dict: 包含source_port和/或dest_port的字典
        """
        if not port_range_input:
            return {}

        # 处理字典格式的输入（如 {'tcp': '5631', 'udp': '5632'}）
        if isinstance(port_range_input, dict):
            log(f"检测到字典格式的端口范围输入: {port_range_input}", "debug")
            # 如果是字典，直接返回转换后的格式
            result = {}
            if 'tcp' in port_range_input:
                result['dest_port'] = str(port_range_input['tcp'])
            elif 'udp' in port_range_input:
                result['dest_port'] = str(port_range_input['udp'])
            elif 'port' in port_range_input:
                result['dest_port'] = str(port_range_input['port'])
            log(f"字典格式端口范围转换结果: {result}", "debug")
            return result

        # 确保输入是字符串
        if not isinstance(port_range_input, str):
            log(f"端口范围输入类型错误，期望字符串或字典，得到: {type(port_range_input)}", "warning")
            return {}

        port_range_str = port_range_input
        
        # 移除引号
        port_range_str = port_range_str.strip('"')
        
        # 按空格分割端口或端口范围
        parts = port_range_str.split()
        
        # 检查是否所有部分都是简单的源端口:目标端口格式
        all_simple_colon_format = True
        parsed_parts = []
        
        for part in parts:
            if ':' in part:
                # 检查是否是简单的源端口:目标端口格式
                colon_parts = part.split(':')
                if len(colon_parts) == 2:
                    # 验证两个部分都是有效的端口范围格式
                    source_part, dest_part = colon_parts
                    if self._is_valid_port_range(source_part) and self._is_valid_port_range(dest_part):
                        parsed_parts.append((source_part, dest_part))
                    else:
                        all_simple_colon_format = False
                        break
                else:
                    all_simple_colon_format = False
                    break
            else:
                # 没有冒号，只是目标端口
                if self._is_valid_port_range(part):
                    parsed_parts.append((None, part))
                else:
                    all_simple_colon_format = False
                    break
        
        if all_simple_colon_format and parsed_parts:
            # 所有部分都是简单格式，可以正确解析
            source_ports = []
            dest_ports = []
            
            for source_part, dest_part in parsed_parts:
                if source_part:
                    source_ports.append(source_part)
                dest_ports.append(dest_part)
            
            result = {}
            if source_ports:
                result["source_port"] = ",".join(source_ports)
            if dest_ports:
                result["dest_port"] = ",".join(dest_ports)
            
            log(_("debug.improved_legacy_generator_port_range_conversion"), "debug",
                input=port_range_str, output=str(result))
            return result
        else:
            # 复杂格式，保持原样作为dest_port
            ntos_format = ",".join(parts)
            result = {"dest_port": ntos_format}
            log(_("debug.improved_legacy_generator_complex_port_range_conversion"), "debug",
                input=port_range_str, output=str(result))
            return result
    
    def _is_valid_port_range(self, port_str):
        """
        验证端口范围字符串是否有效

        Args:
            port_str (str): 端口范围字符串

        Returns:
            bool: 是否有效
        """
        # 类型检查
        if not isinstance(port_str, str):
            log(f"端口范围验证输入类型错误，期望字符串，得到: {type(port_str)}", "debug")
            return False

        import re
        # 匹配单个端口或端口范围，如：80, 80-90, 0-65535
        pattern = r'^([0-9]|[1-9]\d|[1-9]\d{2}|[1-9]\d{3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])(-([0-9]|[1-9]\d|[1-9]\d{2}|[1-9]\d{3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5]))?$'
        return bool(re.match(pattern, port_str.strip()))
    
    def _convert_port_range(self, port_range_str):
        """
        转换端口范围字符串到NTOS格式

        Args:
            port_range_str (str|dict): 端口范围字符串或字典

        Returns:
            str: NTOS格式的端口范围字符串
        """
        if not port_range_str:
            return ""

        # 处理字典格式的输入（如 {'tcp': '5631', 'udp': '5632'}）
        if isinstance(port_range_str, dict):
            log(f"_convert_port_range检测到字典格式的端口范围输入: {port_range_str}", "debug")
            # 如果是字典，提取端口值并转换为字符串
            if 'tcp' in port_range_str:
                port_range_str = str(port_range_str['tcp'])
            elif 'udp' in port_range_str:
                port_range_str = str(port_range_str['udp'])
            elif 'port' in port_range_str:
                port_range_str = str(port_range_str['port'])
            else:
                log(f"_convert_port_range无法从字典中提取端口信息: {port_range_str}", "warning")
                return ""
            log(f"_convert_port_range字典转换结果: {port_range_str}", "debug")

        # 确保输入是字符串
        if not isinstance(port_range_str, str):
            log(f"_convert_port_range端口范围输入类型错误，期望字符串或字典，得到: {type(port_range_str)}", "warning")
            return ""

        # 移除引号
        port_range_str = port_range_str.strip('"')
        
        # 如果已经是逗号分隔的格式，直接返回
        if ',' in port_range_str:
            return port_range_str
        
        # 如果是空格分隔的格式，转换为逗号分隔
        if ' ' in port_range_str:
            parts = port_range_str.split()
            ntos_format = ",".join(parts)
            log(_("debug.improved_legacy_generator_port_range_normalization"), "debug",
                input=port_range_str, output=ntos_format)
            return ntos_format
        
        # 单个端口或端口范围，直接返回
        return port_range_str 

    def _generate_legacy_xml(self):
        """
        生成NTOS XML配置 - 改进版本
        
        Returns:
            tuple: (XML字符串, 统计信息)
        """
        # 调用父类的_generate_legacy_xml方法
        xml_str, stats = super()._generate_legacy_xml()
        
        # 解析生成的XML
        try:
            root = etree.fromstring(xml_str.encode('utf-8'))
            
            # 查找VRF元素
            vrf_nodes = root.xpath(".//*[local-name()='vrf']")
            if vrf_nodes:
                vrf = vrf_nodes[0]
                
                # 查找服务对象节点
                service_obj_nodes = vrf.xpath(".//*[local-name()='service-obj']")
                if service_obj_nodes:
                    service_obj = service_obj_nodes[0]
                    service_sets = service_obj.xpath(".//*[local-name()='service-set']")
                    log(_("debug.improved_legacy_generator_final_xml_service_sets_count"), "debug", count=len(service_sets))
                    
                    # 如果服务对象节点为空，则移除它
                    if len(service_sets) == 0:
                        log(_("warning.generator_service_object_node_empty"), "warning")
                        vrf.remove(service_obj)
                        log("已移除空的服务对象节点", "debug")
                
                # 检查服务对象节点状态
                service_obj_nodes = vrf.xpath(".//*[local-name()='service-obj']")
                if not service_obj_nodes:
                    log("确认: 空的服务对象节点已成功移除", "debug")
                else:
                    service_obj = service_obj_nodes[0]
                    service_sets = service_obj.xpath(".//*[local-name()='service-set']")
                    if len(service_sets) == 0:
                        log(_("warning.generator_empty_service_node_removal_failed"), "warning")
                    else:
                        log(_("debug.improved_legacy_generator_keeping_service_obj_node"), "debug", count=len(service_sets))
        
        except Exception as e:
            log(f"ImprovedLegacyGenerator处理XML时出错: {str(e)}", "error")
        
        return xml_str, stats