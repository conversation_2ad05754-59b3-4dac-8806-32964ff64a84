from lxml import etree
import copy
import random
import re
from typing import Dict, Any, List, Optional
from engine.utils.logger import log
from engine.utils.i18n import _  # 导入国际化函数
from engine.generators.xml_utils import (
    NS_INTERFACE, NS_FLOW, NS_MONITOR, NS_IPMAC, NS_ACCESS, NS_VLAN,
    NAMESPACE_MAPPING, NamespaceManager
)

# 添加一个全局变量，用于跟踪已分配的隧道ID（保留向后兼容性）
used_pppoe_tunnel_ids = set()

class InterfaceCommon:
    """
    接口处理共享类，包含所有接口类型共享的功能
    """
    
    def __init__(self, ns_manager=None):
        """
        初始化接口处理器
        
        Args:
            ns_manager: 命名空间管理器实例，如果为None则创建一个新实例
        """
        self._used_pppoe_tunnel_ids = set()
        self.ns_manager = ns_manager if ns_manager else NamespaceManager()
    
    def get_next_tunnel_id(self):
        """
        获取下一个可用的隧道ID
        
        Returns:
            int: 可用的隧道ID
        """
        # 开始尝试分配ID
        for i in range(128):  # 0-127范围
            if i not in self._used_pppoe_tunnel_ids:
                self._used_pppoe_tunnel_ids.add(i)
                log(_("interface_handler.assigned_pppoe_tunnel_id", id=i))
                return i
        # 如果所有ID都已分配，则返回错误
        log(_("interface_handler.error.all_tunnel_ids_used"), "error")
        return 0
    
    def reset_tunnel_ids(self):
        """重置隧道ID分配记录"""
        self._used_pppoe_tunnel_ids = set()
        log(_("interface_handler.reset_tunnel_ids"))
    
    def create_dhcp_config(self, parent):
        """
        创建DHCP配置
        
        Args:
            parent: 父节点
        """
        dhcp = etree.SubElement(parent, "dhcp")
        etree.SubElement(dhcp, "enabled").text = "true"
        etree.SubElement(dhcp, "timeout").text = "60"
        etree.SubElement(dhcp, "retry").text = "30"
        etree.SubElement(dhcp, "select-timeout").text = "0"
        etree.SubElement(dhcp, "reboot").text = "10"
        etree.SubElement(dhcp, "initial-interval").text = "10"
        etree.SubElement(dhcp, "dhcp-lease-time").text = "3600"
        
        # 添加DHCP请求选项
        dhcp_options = [
            "subnet-mask", "broadcast-address", "time-offset", "routers",
            "domain-name", "domain-search", "domain-name-servers", "host-name",
            "nis-domain", "nis-servers", "ntp-servers", "interface-mtu"
        ]
        for option in dhcp_options:
            etree.SubElement(dhcp, "request").text = option
    
    def create_pppoe_config(self, ipv4_elem, intf_data, nsmap=None, is_physical_interface=True):
        """
        创建PPPoE配置
        
        Args:
            ipv4_elem: IPv4元素
            intf_data: 接口数据
            nsmap: 命名空间映射
            is_physical_interface: 是否为物理接口，决定是否添加reverse-path标签
        """
        log(_("interface_handler.info.create_pppoe_config", interface=intf_data.get('name', 'unknown')))
        
        # 如果没有提供nsmap，使用默认命名空间
        if nsmap is None:
            nsmap = {
                'if': NS_INTERFACE,
                'flow': NS_FLOW,
                'acc': NS_ACCESS
            }
        
        # 先清除ipv4_elem下的所有子元素
        for child in list(ipv4_elem):
            ipv4_elem.remove(child)
        
        # 添加enabled元素 - 使用带或不带命名空间的方式
        if 'if' in nsmap:
            etree.SubElement(ipv4_elem, "{%s}enabled" % nsmap['if']).text = "true"
        else:
            etree.SubElement(ipv4_elem, "enabled").text = "true"
        
        # 创建pppoe元素
        if 'if' in nsmap:
            pppoe = etree.SubElement(ipv4_elem, "{%s}pppoe" % nsmap['if'])
        else:
            pppoe = etree.SubElement(ipv4_elem, "pppoe")
        
        # 创建connection元素
        if 'if' in nsmap:
            connection = etree.SubElement(pppoe, "{%s}connection" % nsmap['if'])
        else:
            connection = etree.SubElement(pppoe, "connection")
        
        # 分配一个新的隧道ID
        tunnel_id = self.get_next_tunnel_id()
        
        # 设置隧道ID
        if 'if' in nsmap:
            etree.SubElement(connection, "{%s}tunnel" % nsmap['if']).text = str(tunnel_id)
            etree.SubElement(connection, "{%s}enabled" % nsmap['if']).text = "true"
        else:
            etree.SubElement(connection, "tunnel").text = str(tunnel_id)
            etree.SubElement(connection, "enabled").text = "true"
        
        # 设置用户名和密码，支持多种字段名格式
        username = (intf_data.get("username") or 
                   intf_data.get("pppoe_username") or 
                   intf_data.get("pppoe-username") or
                   intf_data.get("user", ""))
        
        password = (intf_data.get("password") or 
                   intf_data.get("pppoe_password") or 
                   intf_data.get("pppoe-password") or
                   intf_data.get("pass", ""))
        
        # 确保PPPoE连接始终有用户名和密码（YANG模型要求）
        if not username:
            username = "default_user"  # 提供默认用户名
            log(_("interface_handler.warning.using_default_pppoe_username"), "warning")

        if not password:
            password = "default_pass"  # 提供默认密码
            log(_("interface_handler.warning.using_default_pppoe_password"), "warning")

        # 设置用户名（必需）
        if 'if' in nsmap:
            etree.SubElement(connection, "{%s}user" % nsmap['if']).text = username
        else:
            etree.SubElement(connection, "user").text = username

        # 设置密码（必需）- 使用加密形式的密码
        if 'if' in nsmap:
            etree.SubElement(connection, "{%s}password" % nsmap['if']).text = "=*-#!$_2g+awtqJWZWf+C6zl4RLQ=="
        else:
            etree.SubElement(connection, "password").text = "=*-#!$_2g+awtqJWZWf+C6zl4RLQ=="
        
        # 设置网关和超时参数
        if 'if' in nsmap:
            etree.SubElement(connection, "{%s}gateway" % nsmap['if']).text = "true"
            etree.SubElement(connection, "{%s}timeout" % nsmap['if']).text = "5"
            etree.SubElement(connection, "{%s}retries" % nsmap['if']).text = "3"
        else:
            etree.SubElement(connection, "gateway").text = "true"
            etree.SubElement(connection, "timeout").text = "5"
            etree.SubElement(connection, "retries").text = "3"
        
        # 设置PPP参数
        if 'if' in nsmap:
            ppp = etree.SubElement(connection, "{%s}ppp" % nsmap['if'])
            etree.SubElement(ppp, "{%s}negotiation-timeout" % nsmap['if']).text = "3"
            etree.SubElement(ppp, "{%s}lcp-echo-interval" % nsmap['if']).text = "10"
            etree.SubElement(ppp, "{%s}lcp-echo-retries" % nsmap['if']).text = "10"
        else:
            ppp = etree.SubElement(connection, "ppp")
            etree.SubElement(ppp, "negotiation-timeout").text = "3"
            etree.SubElement(ppp, "lcp-echo-interval").text = "10"
            etree.SubElement(ppp, "lcp-echo-retries").text = "10"
        
        # 设置MTU和反向路径
        if 'if' in nsmap:
            etree.SubElement(connection, "{%s}ppp-mtu" % nsmap['if']).text = "1492"
            # 只有物理接口才添加reverse-path标签
            if is_physical_interface:
                etree.SubElement(connection, "{%s}reverse-path" % nsmap['if']).text = "true"
        else:
            etree.SubElement(connection, "ppp-mtu").text = "1492"
            # 只有物理接口才添加reverse-path标签
            if is_physical_interface:
                etree.SubElement(connection, "reverse-path").text = "true"
        
        # 设置流量控制 - 只有物理接口才添加flow-control标签
        if is_physical_interface:
            # 创建flow-control标签，使用正确的命名空间
            flow_control = etree.SubElement(connection, "flow-control")
            flow_control.set("xmlns", "urn:ruijie:ntos:params:xml:ns:yang:flow-control")
            etree.SubElement(flow_control, "enabled").text = "false"

    def create_ipv6_dhcp_config(self, parent):
        """
        创建IPv6 DHCP配置
        
        Args:
            parent: 父节点
        """
        dhcp = etree.SubElement(parent, "dhcp")
        etree.SubElement(dhcp, "enabled").text = "true"
    
    def create_ipv4_node(self, parent, ip_cidr):
        """
        创建IPv4节点
        
        Args:
            parent: 父节点
            ip_cidr: CIDR格式的IP地址
        """
        ipv4 = etree.SubElement(parent, "ipv4")
        etree.SubElement(ipv4, "enabled").text = "true"
        address = etree.SubElement(ipv4, "address")
        
        # 使用YANG模型兼容的masked-ipv4-address格式 (例如: "***********/24")
        # 而不是分离的ip和prefix-length元素
        etree.SubElement(address, "ip").text = ip_cidr
    
    def create_ipv6_node(self, parent, ipv6_info):
        """
        创建IPv6节点
        
        Args:
            parent: 父节点
            ipv6_info: IPv6配置信息
        """
        ipv6 = etree.SubElement(parent, "ipv6")
        etree.SubElement(ipv6, "enabled").text = "true"
        if isinstance(ipv6_info, str) and "/" in ipv6_info:
            address = etree.SubElement(ipv6, "address")
            # 使用YANG模型兼容的masked-ipv6-address格式 (例如: "2001:db8::1/64")
            # 而不是分离的ip和prefix-length元素
            etree.SubElement(address, "ip").text = ipv6_info
    
    def create_access_control(self, parent, allowaccess):
        """
        创建或更新访问控制节点，避免重复元素

        Args:
            parent: 父节点
            allowaccess: 允许的访问方式列表
        """

        # 定义访问控制命名空间
        ns_access = "urn:ruijie:ntos:params:xml:ns:yang:local-defend"
        
        # 使用多种方式查找现有的access-control节点，考虑命名空间
        existing_access_control = None
        
        # 方法1: 使用命名空间查找
        try:
            existing_access_control = parent.find(".//{%s}access-control" % ns_access)
        except:
            pass
            
        # 方法2: 直接查找（无命名空间）
        if existing_access_control is None:
            existing_access_control = parent.find(".//access-control")
            
        # 方法3: 遍历子元素查找
        if existing_access_control is None:
            for child in parent:
                if child.tag.endswith("}access-control") or child.tag == "access-control":
                    existing_access_control = child
                    break
        
        if existing_access_control is not None:
            # 如果已存在，使用现有节点
            access_control = existing_access_control
            log(_("interface_handler.found_existing_access_control"))
        else:
            # 创建新的access-control节点
            access_control = etree.SubElement(parent, "access-control")
            access_control.set("xmlns", ns_access)
            log(_("interface_handler.created_new_access_control"))
        
        # 增强：设置各种访问方式 - 支持更多服务类型
        service_states = self._parse_enhanced_access_services(allowaccess)

        # 查找或创建各访问方式节点，支持命名空间
        def find_or_create_access_elem(access_control, elem_name, enabled):
            """查找或创建访问控制子元素"""
            # 先尝试无命名空间查找
            elem = access_control.find(elem_name)

            # 如果没找到，尝试带命名空间查找
            if elem is None:
                try:
                    elem = access_control.find(".//{%s}%s" % (ns_access, elem_name))
                except:
                    pass

            # 如果还没找到，遍历查找
            if elem is None:
                for child in access_control:
                    if child.tag.endswith("}%s" % elem_name) or child.tag == elem_name:
                        elem = child
                        break

            # 如果还是没找到，创建新的
            if elem is None:
                elem = etree.SubElement(access_control, elem_name)

            # 设置值
            elem.text = "true" if enabled else "false"
            return elem

        # 增强：更新所有支持的访问方式
        for service_name, enabled in service_states.items():
            find_or_create_access_elem(access_control, service_name, enabled)

    def _parse_enhanced_access_services(self, allowaccess) -> Dict[str, bool]:
        """
        增强的访问服务解析 - 支持更多服务类型

        Args:
            allowaccess: 访问控制配置（字符串、列表或字典）

        Returns: Dict[str, bool]: 服务名称到启用状态的映射
        """
        # NTOS支持的访问控制服务（基于local-defend YANG模型）
        # 根据ntos-local-defend.yang，只支持https、ping、ssh三个服务
        supported_services = {
            "https", "ping", "ssh"
        }

        # 初始化所有服务为禁用状态
        service_states = {service: False for service in supported_services}

        if not allowaccess:
            return service_states

        # 处理字典格式（来自接口处理阶段的增强处理）
        if isinstance(allowaccess, dict):
            for service, enabled in allowaccess.items():
                if service in supported_services:
                    service_states[service] = bool(enabled)
            return service_states

        # 处理字符串格式
        if isinstance(allowaccess, str):
            services = allowaccess.lower().split()
            for service in services:
                service = service.strip()
                if service == "http":
                    service_states["https"] = True  # HTTP映射到HTTPS
                elif service in supported_services:
                    service_states[service] = True

        # 处理列表格式
        elif isinstance(allowaccess, list):
            for service in allowaccess:
                service = str(service).lower().strip()
                if service == "http":
                    service_states["https"] = True  # HTTP映射到HTTPS
                elif service in supported_services:
                    service_states[service] = True

        return service_states

    def sanitize_interface_names(self, config_data):
        """
        清理和标准化接口名称
        
        Args:
            config_data: 配置数据
            
        Returns:
            dict: 处理后的配置数据
        """
        if not config_data:
            return config_data
        
        # 创建配置数据的副本，避免修改原始数据
        result = copy.deepcopy(config_data)
        
        # 处理接口数据
        if "interfaces" in result:
            # 检查接口数据是否为字典类型
            if isinstance(result["interfaces"], dict):
                # 接口数据为字典类型，需要转换为列表
                interfaces_dict = result["interfaces"]
                interfaces_list = []
                
                for raw_name, intf_data in interfaces_dict.items():
                    # 确保intf_data是字典
                    if isinstance(intf_data, dict):
                        # 如果接口数据中没有name字段，则使用raw_name作为name
                        if "name" not in intf_data:
                            intf_data["name"] = raw_name
                        
                        # 记录原始名称
                        intf_data["raw_name"] = raw_name
                        
                        # 添加到列表
                        interfaces_list.append(intf_data)
                    else:
                        log(_("interface_handler.warning.invalid_interface_data", name=raw_name), "warning")
                
                # 替换原始接口数据
                result["interfaces"] = interfaces_list
            
            # 确保接口列表中的每个接口都有name字段
            for intf in result["interfaces"]:
                if isinstance(intf, dict) and "name" not in intf:
                    # 如果没有name字段，尝试使用其他字段作为name
                    if "interface" in intf:
                        intf["name"] = intf["interface"]
                    elif "raw_name" in intf:
                        intf["name"] = intf["raw_name"]
                    else:
                        # 如果无法确定名称，生成一个随机名称
                        intf["name"] = f"Interface_{random.randint(1000, 9999)}"
                        log(_("interface_handler.warning.generated_random_name", name=intf["name"]), "warning")
        
        return result
    
    def is_physical_interface(self, interface_name, interface_data=None):
        """
        判断接口是否为物理接口
        
        Args:
            interface_name (str): 接口名称
            interface_data (dict, optional): 接口配置数据
            
        Returns:
            bool: 是否为物理接口
        """
        # 如果接口数据中明确指定了类型，优先使用
        if interface_data:
            # 如果明确指定为physical类型，则是物理接口
            if interface_data.get("type") == "physical":
                return True
            # 如果明确标记为子接口，则不是物理接口
            if interface_data.get("is_subinterface", False):
                return False
        
        # 如果接口名称中包含"."，通常表示这是一个子接口
        if interface_name and "." in interface_name:
            return False
        
        # 默认假设为物理接口
        return True
    
    def process_fortinet_interfaces(self, config_data, interface_mapping):
        """
        处理飞塔接口，转换子接口格式
        
        Args:
            config_data: 配置数据
            interface_mapping: 接口映射
            
        Returns:
            dict: 处理后的配置数据
        """
        if not config_data or "interfaces" not in config_data:
            return config_data
        
        # 创建配置数据的副本，避免修改原始数据
        result = copy.deepcopy(config_data)
        
        # 创建接口名称到接口对象的映射，用于验证父接口
        interface_dict = {}
        for intf in result["interfaces"]:
            if isinstance(intf, dict):
                intf_name = intf.get("name", "") or intf.get("raw_name", "")
                # 确保接口名称是字符串类型
                if intf_name and isinstance(intf_name, str):
                    interface_dict[intf_name] = intf
        
        # 标记需要跳过的子接口索引
        skip_indices = set()
        
        # 处理每个接口
        for i, intf in enumerate(result["interfaces"]):
            if not isinstance(intf, dict):
                continue
            
            # 检查是否是子接口 - 支持两种格式：
            # 1. 名称中包含"."的格式（如port1.10）
            # 2. 已标记为is_subinterface的格式（如vlan3-100）
            interface_name = intf.get("name", "") or intf.get("raw_name", "")
            is_subinterface = intf.get("is_subinterface", False)
            
            if (interface_name and "." in interface_name) or is_subinterface:
                # 是子接口，尝试解析或映射
                if is_subinterface and "parent_interface" in intf and "vlanid" in intf:
                    # 对于已解析的子接口（如vlan3-100），直接进行名称映射
                    parent_interface = intf.get("parent_interface")
                    vlan_id = intf.get("vlanid")
                    
                    # 验证父接口是否存在
                    if parent_interface not in interface_dict:
                        # 检查父接口是否在映射表中
                        parent_found_in_mapping = False
                        mapped_parent = None
                        
                        # 检查是否有任何接口映射到了这个父接口名称
                        for orig_name, mapped_name in interface_mapping.items():
                            if isinstance(mapped_name, dict) and mapped_name.get("name") == parent_interface:
                                parent_found_in_mapping = True
                                mapped_parent = mapped_name
                                break
                            elif isinstance(mapped_name, str) and mapped_name == parent_interface:
                                parent_found_in_mapping = True
                                mapped_parent = mapped_name
                                break
                        
                        if not parent_found_in_mapping:
                            log(_("interface_handler.parent_interface_not_found", 
                                subinterface=interface_name, parent=parent_interface), "warning")
                            skip_indices.add(i)
                            continue
                        else:
                            # 父接口在映射表中但不在接口列表中，这是正常的
                            log(_("interface_handler.parent_interface_in_mapping_only", 
                                subinterface=interface_name, parent=parent_interface))
                    
                    # 验证父接口是否为物理接口
                    parent_data = interface_dict.get(parent_interface)
                    if parent_data and not self.is_physical_interface(parent_interface, parent_data):
                        log(_("interface_handler.parent_interface_not_physical", 
                            subinterface=interface_name, parent=parent_interface), "error")
                        skip_indices.add(i)
                        continue
                    
                    # 映射父接口名称
                    if interface_mapping:
                        # 检查父接口是否有映射
                        parent_mapped = False
                        mapped_parent = None
                        
                        # 直接检查父接口是否在映射表中
                        if parent_interface in interface_mapping:
                            mapped_parent = interface_mapping[parent_interface]
                            parent_mapped = True
                        else:
                            # 检查是否有任何接口映射到了这个父接口名称
                            for orig_name, mapped_name in interface_mapping.items():
                                if isinstance(mapped_name, dict) and mapped_name.get("name") == parent_interface:
                                    parent_mapped = True
                                    mapped_parent = parent_interface  # 已经是映射后的名称
                                    break
                                elif isinstance(mapped_name, str) and mapped_name == parent_interface:
                                    parent_mapped = True
                                    mapped_parent = parent_interface  # 已经是映射后的名称
                                    break
                        
                        if parent_mapped:
                            # 使用映射后的父接口名称
                            if isinstance(mapped_parent, dict) and "name" in mapped_parent:
                                mapped_parent_name = mapped_parent["name"]
                            else:
                                mapped_parent_name = mapped_parent
                            
                            # 生成NTOS格式的子接口名称
                            mapped_name = f"{mapped_parent_name}.{vlan_id}"
                            intf["name"] = mapped_name
                            log(_("interface_handler.subinterface_mapped",
                                raw_name=intf.get("raw_name"), mapped_name=mapped_name, parent=mapped_parent_name))
                        else:
                            log(_("interface_handler.parent_interface_not_mapped", 
                                subinterface=interface_name, parent=parent_interface), "warning")
                            skip_indices.add(i)
                            continue
                    else:
                        log(_("interface_handler.no_mapping_provided", 
                            subinterface=interface_name), "warning")
                        skip_indices.add(i)
                        continue
                elif interface_name and "." in interface_name:
                    # 对于传统格式的子接口（如port1.10），使用原有解析逻辑
                    # 提取父接口名称
                    match = re.match(r'([^.]+)\.(\d+)', interface_name)
                    if match:
                        parent_name = match.group(1)
                        vlan_id = match.group(2)
                        
                        # 验证父接口是否存在
                        parent_found = False
                        
                        # 检查父接口是否在接口列表中
                        if parent_name in interface_dict:
                            parent_found = True
                            parent_data = interface_dict[parent_name]
                            
                            # 验证父接口是否为物理接口
                            if not self.is_physical_interface(parent_name, parent_data):
                                log(_("interface_handler.parent_interface_not_physical", 
                                    subinterface=interface_name, parent=parent_name), "error")
                                skip_indices.add(i)
                                continue
                        
                        # 如果父接口不在接口列表中，检查是否在映射表中
                        if not parent_found and interface_mapping:
                            for orig_name, mapped_name in interface_mapping.items():
                                if orig_name == parent_name:
                                    parent_found = True
                                    break
                                elif isinstance(mapped_name, dict) and mapped_name.get("name") == parent_name:
                                    parent_found = True
                                    break
                                elif isinstance(mapped_name, str) and mapped_name == parent_name:
                                    parent_found = True
                                    break
                        
                        if not parent_found:
                            log(_("interface_handler.parent_interface_not_found", 
                                subinterface=interface_name, parent=parent_name), "warning")
                            skip_indices.add(i)
                            continue
                    
                    # 解析子接口
                    parsed_data = self.parse_fortinet_subinterface(interface_name, intf, interface_mapping)
                    if parsed_data:
                        # 更新接口数据
                        result["interfaces"][i] = parsed_data
                    else:
                        # 如果解析失败，标记为跳过
                        log(_("interface_handler.subinterface_parse_failed", 
                            subinterface=interface_name), "warning")
                        skip_indices.add(i)
                else:
                    # 处理非传统命名的子接口（如wan_pppoe）
                    # 检查是否有interface和vlanid属性
                    if "interface" in intf and "vlanid" in intf:
                        log(_("interface_handler.processing_nonstandard_subinterface", 
                            name=interface_name, interface=intf.get("interface"), vlanid=intf.get("vlanid")))
                        parent_interface = intf.get("interface")
                        vlan_id = intf.get("vlanid")
                        
                        # 验证父接口是否存在
                        parent_found = False
                        
                        # 检查父接口是否在接口列表中
                        if parent_interface in interface_dict:
                            parent_found = True
                            parent_data = interface_dict[parent_interface]
                            log(_("interface_handler.nonstandard_parent_found_in_interfaces", 
                                subinterface=interface_name, parent=parent_interface))
                            
                            # 验证父接口是否为物理接口
                            if not self.is_physical_interface(parent_interface, parent_data):
                                log(_("interface_handler.parent_interface_not_physical", 
                                    subinterface=interface_name, parent=parent_interface), "error")
                                skip_indices.add(i)
                                continue
                        
                        # 如果父接口不在接口列表中，检查是否在映射表中
                        if not parent_found and interface_mapping:
                            for orig_name, mapped_name in interface_mapping.items():
                                if orig_name == parent_interface:
                                    parent_found = True
                                    log(_("interface_handler.nonstandard_parent_found_in_mapping", 
                                        subinterface=interface_name, parent=parent_interface, mapped=mapped_name))
                                    break
                                elif isinstance(mapped_name, dict) and mapped_name.get("name") == parent_interface:
                                    parent_found = True
                                    log(_("interface_handler.nonstandard_parent_found_in_mapping_dict", 
                                        subinterface=interface_name, parent=parent_interface))
                                    break
                                elif isinstance(mapped_name, str) and mapped_name == parent_interface:
                                    parent_found = True
                                    log(_("interface_handler.nonstandard_parent_found_in_mapping_str", 
                                        subinterface=interface_name, parent=parent_interface))
                                    break
                        
                        if not parent_found:
                            log(_("interface_handler.parent_interface_not_found", 
                                subinterface=interface_name, parent=parent_interface), "warning")
                            skip_indices.add(i)
                            continue
                        
                        # 映射父接口名称
                        if interface_mapping:
                            # 检查父接口是否有映射
                            parent_mapped = False
                            mapped_parent = None
                            
                            # 直接检查父接口是否在映射表中
                            if parent_interface in interface_mapping:
                                mapped_parent = interface_mapping[parent_interface]
                                parent_mapped = True
                                log(_("interface_handler.nonstandard_parent_direct_mapping", 
                                    subinterface=interface_name, parent=parent_interface, mapped=mapped_parent))
                            else:
                                # 检查是否有任何接口映射到了这个父接口名称
                                for orig_name, mapped_name in interface_mapping.items():
                                    if orig_name == parent_interface:
                                        mapped_parent = mapped_name
                                        parent_mapped = True
                                        log(_("interface_handler.nonstandard_parent_indirect_mapping", 
                                            subinterface=interface_name, parent=parent_interface, mapped=mapped_name))
                                        break
                            
                            if parent_mapped:
                                # 使用映射后的父接口名称
                                if isinstance(mapped_parent, dict) and "name" in mapped_parent:
                                    mapped_parent_name = mapped_parent["name"]
                                    log(_("interface_handler.nonstandard_parent_mapped_dict", 
                                        subinterface=interface_name, parent=parent_interface, mapped=mapped_parent_name))
                                else:
                                    mapped_parent_name = mapped_parent
                                    log(_("interface_handler.nonstandard_parent_mapped_str", 
                                        subinterface=interface_name, parent=parent_interface, mapped=mapped_parent_name))
                                
                                # 生成NTOS格式的子接口名称
                                mapped_name = f"{mapped_parent_name}.{vlan_id}"
                                intf["name"] = mapped_name
                                # 设置子接口属性
                                intf["is_subinterface"] = True
                                intf["parent_interface"] = mapped_parent_name
                                log(_("interface_handler.nonstandard_subinterface_mapped", 
                                    raw_name=intf.get("raw_name"), mapped_name=mapped_name, 
                                    parent=mapped_parent_name, vlanid=vlan_id))
                                
                                # 检查是否为PPPoE模式
                                if intf.get("mode", "").lower() == "pppoe":
                                    log(_("interface_handler.nonstandard_subinterface_pppoe", 
                                        name=mapped_name, username=intf.get("username", "unknown")))
                            else:
                                log(_("interface_handler.parent_interface_not_mapped", 
                                    subinterface=interface_name, parent=parent_interface), "warning")
                                skip_indices.add(i)
                                continue
                        else:
                            log(_("interface_handler.no_mapping_provided", 
                                subinterface=interface_name), "warning")
                            skip_indices.add(i)
                            continue
                    else:
                        # 缺少必要的子接口属性
                        log(_("interface_handler.subinterface_missing_attributes", 
                            subinterface=interface_name, has_interface="interface" in intf,
                            has_vlanid="vlanid" in intf), "warning")
                        skip_indices.add(i)
        
        # 移除标记为跳过的子接口
        if skip_indices:
            result["interfaces"] = [intf for i, intf in enumerate(result["interfaces"]) if i not in skip_indices]
            log(_("interface_handler.skipped_subinterfaces", count=len(skip_indices)))
        
        return result
    
    def get_ntos_interface_name(self, fortinet_interface_name, interface_mapping):
        """
        获取NTOS接口名称，支持FortiGate接口名称映射
        
        Args:
            fortinet_interface_name: FortiGate接口名称（如port1）
            interface_mapping: 接口映射表
            
        Returns:
            str: NTOS接口名称
        """
        # 确保输入是字符串类型
        if isinstance(fortinet_interface_name, dict):
            # 如果输入是字典，尝试提取接口名称
            if "name" in fortinet_interface_name:
                fortinet_interface_name = fortinet_interface_name["name"]
            elif "interface" in fortinet_interface_name:
                fortinet_interface_name = fortinet_interface_name["interface"]
            else:
                # 取第一个值作为接口名称
                fortinet_interface_name = list(fortinet_interface_name.values())[0] if fortinet_interface_name else "unknown"
        
        # 确保是字符串
        fortinet_interface_name = str(fortinet_interface_name)
        
        # 检查接口映射
        if interface_mapping and fortinet_interface_name in interface_mapping:
            mapping_data = interface_mapping[fortinet_interface_name]
            # 如果映射数据是字典，提取name字段
            if isinstance(mapping_data, dict):
                return mapping_data.get("name", fortinet_interface_name)
            else:
                # 如果是字符串，直接返回
                return mapping_data
        
        # 如果没有映射，尝试使用正则表达式匹配
        # 匹配模式: port1 -> Ge0/0, port2 -> Ge0/1, ...
        match = re.match(r'port(\d+)', fortinet_interface_name)
        if match:
            port_num = int(match.group(1))
            # 减1是因为飞塔从port1开始，而NTOS从Ge0/0开始
            return f"Ge0/{port_num - 1}"
        
        # 如果无法匹配，返回原始名称
        return fortinet_interface_name
    
    def parse_fortinet_subinterface(self, interface_name, interface_data, interface_mapping):
        """
        解析飞塔子接口
        
        Args:
            interface_name: 接口名称
            interface_data: 接口数据
            interface_mapping: 接口映射
            
        Returns:
            dict: 解析后的接口数据，如果解析失败则返回None
        """
        # 检查是否是飞塔子接口格式（如port1.100）
        match = re.match(r'([^.]+)\.(\d+)', interface_name)
        if not match:
            log(_("interface_handler.not_a_subinterface", name=interface_name), "debug")
            return None
        
        # 提取父接口名称和VLAN ID
        parent_name = match.group(1)
        vlan_id = match.group(2)
        
        # 检查父接口是否有映射
        parent_mapped = False
        mapped_parent = None
        
        # 直接检查父接口是否在映射表中
        if interface_mapping and parent_name in interface_mapping:
            mapped_parent = interface_mapping[parent_name]
            parent_mapped = True
        elif interface_mapping:
            # 检查是否有任何接口映射到了这个父接口名称
            for orig_name, mapped_name in interface_mapping.items():
                if orig_name == parent_name:
                    mapped_parent = mapped_name
                    parent_mapped = True
                    break
        
        if not parent_mapped:
            log(_("interface_handler.parent_interface_not_mapped_in_parse", 
                  subinterface=interface_name, parent=parent_name), "warning")
            return None
        
        # 获取父接口的NTOS名称
        if isinstance(mapped_parent, dict) and "name" in mapped_parent:
            parent_ntos_name = mapped_parent["name"]
        else:
            parent_ntos_name = mapped_parent
        
        # 创建子接口数据的副本
        result = copy.deepcopy(interface_data)
        
        # 设置子接口属性
        result["is_subinterface"] = True
        result["parent_interface"] = parent_ntos_name
        result["vlanid"] = vlan_id
        
        # 构造NTOS格式的子接口名称
        ntos_subinterface_name = f"{parent_ntos_name}.{vlan_id}"
        result["name"] = ntos_subinterface_name
        
        log(_("interface_handler.parsed_fortinet_subinterface", 
              name=interface_name, 
              parent=parent_ntos_name, 
              vlan=vlan_id, 
              ntos_name=ntos_subinterface_name))
        
        return result
    
    def add_interfaces_to_xml(self, interface_elem, interfaces_data, interface_mapping=None):
        """
        将接口配置添加到XML中，确保符合YANG模型规范
        
        Args:
            interface_elem: 接口元素
            interfaces_data: 接口数据列表
            interface_mapping: 接口映射表，用于将FortiGate接口名称映射为NTOS名称
        """
        # 延迟导入，避免循环导入问题
        import engine.generators.physical_interface_handler as phy_handler
        import engine.generators.vlan_interface_handler as vlan_handler
        
        log(_("interface_handler.adding_interfaces_to_xml", count=len(interfaces_data)))
        
        # 收集现有接口信息
        existing_physical_interfaces = {}
        existing_vlan_interfaces = {}
        
        # 查找现有的物理接口
        for phy in interface_elem.findall(".//physical"):
            name_elem = phy.find("./name")
            if name_elem is not None and name_elem.text:
                existing_physical_interfaces[name_elem.text] = phy
        
        # 查找现有的VLAN接口
        for vlan in interface_elem.findall(".//{%s}vlan" % NS_VLAN):
            name_elem = vlan.find(".//{%s}name" % NS_VLAN) or vlan.find(".//name")
            if name_elem is not None and name_elem.text:
                existing_vlan_interfaces[name_elem.text] = vlan
        
        # 处理每个接口
        for interface in interfaces_data:
            interface_name = interface.get("name", "")
            if not interface_name:
                log(_("interface_handler.skip_interface_without_name"), "warning")
                continue
            
            # 判断接口类型
            if interface.get("is_subinterface", False) and "vlanid" in interface:
                # VLAN子接口
                if interface_name in existing_vlan_interfaces:
                    # 更新现有VLAN接口
                    log(_("interface_handler.update_existing_vlan", name=interface_name))
                    vlan_handler.update_vlan_interface(existing_vlan_interfaces[interface_name], interface, interface_mapping)
                else:
                    # 创建新的VLAN接口
                    log(_("interface_handler.create_new_vlan", name=interface_name))
                    vlan_handler.create_new_vlan_interface(interface_elem, interface, interface_mapping)
            else:
                # 普通物理接口
                if interface_name in existing_physical_interfaces:
                    # 更新现有物理接口
                    log(_("interface_handler.update_existing_physical", name=interface_name))
                    phy_handler.update_physical_interface(existing_physical_interfaces[interface_name], interface)
                else:
                    # 创建新的物理接口
                    log(_("interface_handler.create_new_physical", name=interface_name))
                    phy_handler.create_new_interface(interface_elem, interface, interface_mapping)
        
        log(_("interface_handler.interfaces_added_to_xml"))

# 为了保持向后兼容性，创建一个默认的接口公共处理器实例
_default_interface_common = InterfaceCommon()

# 向后兼容的函数，调用默认接口公共处理器的方法
def get_next_tunnel_id():
    """向后兼容的获取下一个隧道ID函数"""
    return _default_interface_common.get_next_tunnel_id()

def reset_tunnel_ids():
    """向后兼容的重置隧道ID函数"""
    return _default_interface_common.reset_tunnel_ids()

def create_dhcp_config(parent):
    """向后兼容的创建DHCP配置函数"""
    return _default_interface_common.create_dhcp_config(parent)

def create_pppoe_config(ipv4_elem, intf_data, nsmap=None, is_physical_interface=True):
    """向后兼容的创建PPPoE配置函数"""
    return _default_interface_common.create_pppoe_config(ipv4_elem, intf_data, nsmap, is_physical_interface)

def create_ipv6_dhcp_config(parent):
    """向后兼容的创建IPv6 DHCP配置函数"""
    return _default_interface_common.create_ipv6_dhcp_config(parent)

def create_ipv4_node(parent, ip_cidr):
    """向后兼容的创建IPv4节点函数"""
    return _default_interface_common.create_ipv4_node(parent, ip_cidr)

def create_ipv6_node(parent, ipv6_info):
    """向后兼容的创建IPv6节点函数"""
    return _default_interface_common.create_ipv6_node(parent, ipv6_info)

def create_access_control(parent, allowaccess):
    """向后兼容的创建访问控制节点函数"""
    return _default_interface_common.create_access_control(parent, allowaccess)

def sanitize_interface_names(config_data):
    """向后兼容的清理和标准化接口名称函数"""
    return _default_interface_common.sanitize_interface_names(config_data)

def process_fortinet_interfaces(config_data, interface_mapping):
    """向后兼容的处理飞塔接口函数"""
    return _default_interface_common.process_fortinet_interfaces(config_data, interface_mapping)

def get_ntos_interface_name(fortinet_interface_name, interface_mapping):
    """向后兼容的获取NTOS接口名称函数"""
    return _default_interface_common.get_ntos_interface_name(fortinet_interface_name, interface_mapping)

def parse_fortinet_subinterface(interface_name, interface_data, interface_mapping):
    """向后兼容的解析飞塔子接口函数"""
    return _default_interface_common.parse_fortinet_subinterface(interface_name, interface_data, interface_mapping)

def add_interfaces_to_xml(interface_elem, interfaces_data, interface_mapping=None):
    """向后兼容的添加接口到XML函数"""
    return _default_interface_common.add_interfaces_to_xml(interface_elem, interfaces_data, interface_mapping) 