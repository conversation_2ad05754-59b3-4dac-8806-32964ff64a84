"""
接口处理模块 - 包装器
此模块为保持向后兼容性而存在，实际功能已被拆分到以下模块：
- interface_common.py: 接口公共功能
- physical_interface_handler.py: 物理接口处理
- vlan_interface_handler.py: VLAN接口处理
"""

# 导入所有必要的模块
from engine.generators.interface_common import (
    InterfaceCommon,
    get_next_tunnel_id,
    reset_tunnel_ids,
    create_dhcp_config,
    create_pppoe_config,
    create_ipv6_dhcp_config,
    create_ipv4_node,
    create_ipv6_node,
    create_access_control,
    sanitize_interface_names,
    process_fortinet_interfaces,
    get_ntos_interface_name,
    parse_fortinet_subinterface,
    add_interfaces_to_xml,
)

from engine.generators.physical_interface_handler import (
    PhysicalInterfaceHandler,
    append_default_interface_config,
    create_new_interface,
    update_existing_interface,
    update_physical_interface,
)

from engine.generators.vlan_interface_handler import (
    VlanInterfaceHandler,
    create_new_vlan_interface,
    update_vlan_interface,
)

from engine.generators.xml_utils import NamespaceManager

# 保持向后兼容性的全局变量
from engine.generators.interface_common import used_pppoe_tunnel_ids
from engine.utils.i18n import _

# 创建一个完整的接口处理器类，集成所有功能
class InterfaceHandler:
    """
    接口处理类，集成物理接口和VLAN接口处理功能
    此类为保持向后兼容性而存在，实际功能已被拆分到多个模块
    """
    
    def __init__(self, ns_manager=None):
        """
        初始化接口处理器
        
        Args:
            ns_manager: 命名空间管理器实例，如果为None则创建一个新实例
        """
        self.ns_manager = ns_manager if ns_manager else NamespaceManager()
        self.common = InterfaceCommon(self.ns_manager)
        self.physical = PhysicalInterfaceHandler(self.common)
        self.vlan = VlanInterfaceHandler(self.common)
    
    # 从InterfaceCommon继承的方法
    def get_next_tunnel_id(self):
        return self.common.get_next_tunnel_id()
    
    def reset_tunnel_ids(self):
        return self.common.reset_tunnel_ids()
    
    def create_dhcp_config(self, parent):
        return self.common.create_dhcp_config(parent)
    
    def create_pppoe_config(self, ipv4_elem, intf_data, nsmap=None):
        return self.common.create_pppoe_config(ipv4_elem, intf_data, nsmap)
    
    def create_ipv6_dhcp_config(self, parent):
        return self.common.create_ipv6_dhcp_config(parent)
    
    def create_ipv4_node(self, parent, ip_cidr):
        return self.common.create_ipv4_node(parent, ip_cidr)
    
    def create_ipv6_node(self, parent, ipv6_info):
        return self.common.create_ipv6_node(parent, ipv6_info)
    
    def create_access_control(self, parent, allowaccess):
        return self.common.create_access_control(parent, allowaccess)
    
    def sanitize_interface_names(self, config_data):
        return self.common.sanitize_interface_names(config_data)
    
    def process_fortinet_interfaces(self, config_data, interface_mapping):
        return self.common.process_fortinet_interfaces(config_data, interface_mapping)
    
    def get_ntos_interface_name(self, fortinet_interface_name, interface_mapping):
        return self.common.get_ntos_interface_name(fortinet_interface_name, interface_mapping)
    
    def parse_fortinet_subinterface(self, interface_name, interface_data, interface_mapping):
        return self.common.parse_fortinet_subinterface(interface_name, interface_data, interface_mapping)
    
    def add_interfaces_to_xml(self, interface_elem, interfaces_data, interface_mapping=None):
        return self.common.add_interfaces_to_xml(interface_elem, interfaces_data, interface_mapping)
    
    # 从PhysicalInterfaceHandler继承的方法
    def append_default_interface_config(self, physical_elem):
        return self.physical.append_default_interface_config(physical_elem)
    
    def create_new_interface(self, interface_elem, intf_data, interface_mapping=None):
        return self.physical.create_new_interface(interface_elem, intf_data, interface_mapping)
    
    def update_existing_interface(self, phy_elem, intf_data):
        return self.physical.update_existing_interface(phy_elem, intf_data)
    
    def update_physical_interface(self, interface_elem, intf_data):
        return self.physical.update_physical_interface(interface_elem, intf_data)
    
    # 从VlanInterfaceHandler继承的方法
    def create_new_vlan_interface(self, interface_elem, intf_data, interface_mapping=None):
        return self.vlan.create_new_vlan_interface(interface_elem, intf_data, interface_mapping)
    
    def update_vlan_interface(self, interface_elem, intf_data, interface_mapping=None):
        return self.vlan.update_vlan_interface(interface_elem, intf_data, interface_mapping)

# 创建一个默认的接口处理器实例，用于向后兼容的函数调用
_default_handler = InterfaceHandler()

# 以下是保持向后兼容性的函数，它们调用默认实例的对应方法
def get_next_tunnel_id():
    """向后兼容的获取下一个隧道ID函数"""
    return _default_handler.get_next_tunnel_id()

def reset_tunnel_ids():
    """向后兼容的重置隧道ID函数"""
    return _default_handler.reset_tunnel_ids()

def append_default_interface_config(physical_elem):
    """向后兼容的添加默认接口配置函数"""
    return _default_handler.append_default_interface_config(physical_elem)

def create_dhcp_config(parent):
    """向后兼容的创建DHCP配置函数"""
    return _default_handler.create_dhcp_config(parent)

def create_pppoe_config(ipv4_elem, intf_data, nsmap=None):
    """向后兼容的创建PPPoE配置函数"""
    return _default_handler.create_pppoe_config(ipv4_elem, intf_data, nsmap)

def create_ipv6_dhcp_config(parent):
    """向后兼容的创建IPv6 DHCP配置函数"""
    return _default_handler.create_ipv6_dhcp_config(parent)

def create_ipv4_node(parent, ip_cidr):
    """向后兼容的创建IPv4节点函数"""
    return _default_handler.create_ipv4_node(parent, ip_cidr)

def create_ipv6_node(parent, ipv6_info):
    """向后兼容的创建IPv6节点函数"""
    return _default_handler.create_ipv6_node(parent, ipv6_info)

def create_access_control(parent, allowaccess):
    """向后兼容的创建访问控制节点函数"""
    return _default_handler.create_access_control(parent, allowaccess)

def create_new_interface(interface_elem, intf_data, interface_mapping=None):
    """向后兼容的创建新接口函数"""
    return _default_handler.create_new_interface(interface_elem, intf_data, interface_mapping)

def sanitize_interface_names(config_data):
    """向后兼容的清理和标准化接口名称函数"""
    return _default_handler.sanitize_interface_names(config_data)

def process_fortinet_interfaces(config_data, interface_mapping):
    """向后兼容的处理飞塔接口函数"""
    return _default_handler.process_fortinet_interfaces(config_data, interface_mapping)

def get_ntos_interface_name(fortinet_interface_name, interface_mapping):
    """向后兼容的获取NTOS接口名称函数"""
    return _default_handler.get_ntos_interface_name(fortinet_interface_name, interface_mapping)

def parse_fortinet_subinterface(interface_name, interface_data, interface_mapping):
    """向后兼容的解析飞塔子接口函数"""
    return _default_handler.parse_fortinet_subinterface(interface_name, interface_data, interface_mapping)

def add_interfaces_to_xml(interface_elem, interfaces_data, interface_mapping=None):
    """向后兼容的添加接口到XML函数"""
    return _default_handler.add_interfaces_to_xml(interface_elem, interfaces_data, interface_mapping)

def create_new_vlan_interface(interface_elem, intf_data, interface_mapping=None):
    """向后兼容的创建新VLAN子接口函数"""
    return _default_handler.create_new_vlan_interface(interface_elem, intf_data, interface_mapping)

def update_vlan_interface(interface_elem, intf_data, interface_mapping=None):
    """向后兼容的更新VLAN子接口函数"""
    return _default_handler.update_vlan_interface(interface_elem, intf_data, interface_mapping)

def update_existing_interface(phy_elem, intf_data):
    """向后兼容的更新现有接口函数"""
    return _default_handler.update_existing_interface(phy_elem, intf_data)

def update_physical_interface(interface_elem, intf_data):
    """向后兼容的更新物理接口函数"""
    return _default_handler.update_physical_interface(interface_elem, intf_data) 