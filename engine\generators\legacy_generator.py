#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import json
import xml.dom.minidom as minidom
from xml.dom.minidom import parseString
from datetime import datetime
from engine.utils.logger import log, user_log
from lxml import etree
from engine.generators.interface_handler import (
    reset_tunnel_ids, create_new_interface, create_new_vlan_interface, 
    update_existing_interface, update_vlan_interface
)
from engine.utils.i18n import _
import re
from engine.generators.interface_common import used_pppoe_tunnel_ids
import html
from copy import deepcopy

class LegacyGenerator:
    """传统XML配置生成器（回退方案）"""
    
    # 添加命名空间常量
    NS_SERVICE_OBJ = "urn:ruijie:ntos:params:xml:ns:yang:service-obj"
    
    def __init__(self, model, version, template_path, transparent_mode_result=None):
        """
        初始化传统配置生成器

        Args:
            model (str): 设备型号，如 'z5100s'
            version (str): 设备版本，如 'R10P2'
            template_path (str): 模板文件路径
            transparent_mode_result (dict, optional): 透明模式处理结果，用于桥接接口生成
        """
        self.model = model
        self.version = version
        self.template_path = template_path
        self.transparent_mode_result = transparent_mode_result
        self.interfaces = {}
        self.interface_mapping = {}
        self.address_objects = []
        self.service_objects = []
        self.policy_rules = []
        self.zones = []
        self.static_routes = []
        self.static_routes_ipv6 = []
        self.address_groups = []
        self.service_groups = []
        self.time_ranges = []
        self.nat_rules = []  # NAT规则
        self.service_mapping_relationships = {}  # 服务映射关系

        log(_("info.init_legacy_generator", model=model, version=version))
    
    def set_interfaces(self, interfaces, interface_mapping=None):
        """设置接口信息"""
        self.interfaces = interfaces
        if interface_mapping:
            self.interface_mapping = interface_mapping
    
    def set_address_objects(self, address_objects):
        """设置地址对象"""
        self.address_objects = address_objects
    
    def set_service_objects(self, service_objects):
        """设置服务对象"""
        self.service_objects = service_objects
    
    def set_policy_rules(self, policy_rules):
        """设置策略规则"""
        self.policy_rules = policy_rules
    
    def set_zones(self, zones):
        """设置安全区域"""
        self.zones = zones
    
    def set_static_routes(self, static_routes, static_routes_ipv6=None):
        """设置静态路由"""
        self.static_routes = static_routes
        self.static_routes_ipv6 = static_routes_ipv6 or []
    
    def set_address_groups(self, address_groups):
        """设置地址组"""
        self.address_groups = address_groups
    
    def set_service_groups(self, service_groups):
        """设置服务组"""
        self.service_groups = service_groups

    def set_service_mapping_relationships(self, service_mapping_relationships):
        """设置服务映射关系"""
        self.service_mapping_relationships = service_mapping_relationships or {}
        log(_("debug.legacy_generator_set_service_mappings"), "debug", count=len(self.service_mapping_relationships))

    def set_time_ranges(self, time_ranges):
        """设置时间对象"""
        self.time_ranges = time_ranges
        log(_("info.legacy_set_time_ranges", count=len(time_ranges)))

    def set_nat_rules(self, nat_rules):
        """设置NAT规则"""
        self.nat_rules = nat_rules or []
        log(_("info.legacy_set_nat_rules", count=len(self.nat_rules)))
    
    def generate(self):
        """
        传统生成方式（回退方案）

        Returns:
            tuple: (XML内容, 统计信息)
        """
        log(_("info.using_legacy_generation"))

        # 定义命名空间映射 - 简化版本，只包含根命名空间用于查询
        # 实际的命名空间声明将直接在需要的元素上添加
        nsmap = {
            None: "urn:ruijie:ntos",  # 根命名空间
            'ntos': "urn:ruijie:ntos",  # 用于XPath查询
            'ntos-security-zone': "urn:ruijie:ntos:params:xml:ns:yang:security-zone",  # 安全区域命名空间
            'ntos-network-obj': "urn:ruijie:ntos:params:xml:ns:yang:network-obj",  # 网络对象命名空间
            'ntos-routing': "urn:ruijie:ntos:params:xml:ns:yang:routing"  # 路由命名空间
        }
        
        # 转换统计信息（传统模式）
        stats = {
            "interfaces": {
                "total": len(self.interfaces),
                "processed": 0,
                "skipped": 0,
                "invalid_interfaces": [],
                "duplicates_removed": 0
            },
            "address_objects": {
                "total": len(self.address_objects),
                "processed": 0
            },
            "service_objects": {
                "total": len(self.service_objects),
                "processed": 0
            },
            "policy_rules": {
                "total": len(self.policy_rules),
                "processed": 0
            },
            "zones": {
                "total": len(self.zones),
                "processed": 0,
                "skipped_interfaces": [],
                "skipped_interfaces_count": 0
            },
            "static_routes": {
                "total": len(self.static_routes),
                "processed": 0
            },
            "time_ranges": {
                "total": len(self.time_ranges),
                "processed": 0
            },
            "validation": {
                "errors": 0,
                "warnings": 0,
                "is_valid": True,
                "mode": "legacy"
            }
        }
        
        try:
            # 加载模板文件
            if not self.template_path or not os.path.exists(self.template_path):
                error_msg = _("error.template_not_exists")
                log(error_msg, "error")
                user_log(_("error.cannot_load_template"), "error")
                raise FileNotFoundError(error_msg)
            
            # 使用传统的XML生成逻辑
            xml_content, legacy_stats = self._generate_legacy_xml()
            
            # 更新统计信息
            stats.update(legacy_stats)
            
            log(_("info.legacy_generation_complete"))
            return xml_content, stats
            
        except Exception as e:
            error_msg = _("error.xml_generation_failed", error=str(e))
            log(error_msg, "error")
            user_log(_("error.generation_failed"), "error")
            raise
    
    def _generate_legacy_xml(self):
        """
        生成符合NTOS格式的XML配置
        
        Returns:
            str: 生成的XML字符串
            dict: 统计信息
        """
        try:
            tree = etree.parse(self.template_path)
            log(_("info.template_loaded_success", path=self.template_path))
        except Exception as e:
            error_msg = _("error.template_parse_failed", error=str(e))
            log(error_msg, "error")
            user_log(_("error.template_format_invalid"), "error")
            raise ValueError(error_msg)
        
        # 获取根元素
        root = tree.getroot()
        log(_("info.root_element_tag", tag=root.tag))
        
        # 确保根元素有正确的命名空间声明，只保留默认命名空间
        if root.tag.endswith('config'):
            log(_("info.root_config_element_found"))
            
            # 创建新的根元素，只包含默认命名空间
            simple_nsmap = {None: "urn:ruijie:ntos"}
            new_root = etree.Element("config", nsmap=simple_nsmap)
            
            # 将原根元素的所有子元素复制到新根元素
            for child in root:
                new_root.append(child)
            
            # 替换根元素
            root = new_root
            tree._setroot(root)
            
            log(_("info.root_namespace_simplified"))
        
        # 定义命名空间映射用于查询
        nsmap = {
            None: "urn:ruijie:ntos",  # 根命名空间
            'ntos': "urn:ruijie:ntos",  # 用于XPath查询
            'ntos-security-zone': "urn:ruijie:ntos:params:xml:ns:yang:security-zone",  # 安全区域命名空间
            'ntos-network-obj': "urn:ruijie:ntos:params:xml:ns:yang:network-obj"  # 网络对象命名空间
        }
        
        # 找到vrf节点 - 使用多种方式查找
        vrf = None
        
        # 方法1：使用命名空间前缀查找
        vrf = root.find(".//ntos:vrf", namespaces=nsmap)
        
        # 方法2：不使用命名空间前缀查找
        if vrf is None:
            vrf = root.find(".//vrf")
            if vrf is not None:
                log(_("info.vrf_node_found_without_namespace"))
        
        # 方法3：使用local-name()函数查找
        if vrf is None:
            vrf = root.xpath(".//*[local-name()='vrf']")
            if vrf and len(vrf) > 0:
                vrf = vrf[0]
                log(_("info.vrf_node_found_with_local_name"))
        
        # 方法4：使用完整URI查找
        if vrf is None:
            vrf = root.find(".//{%s}vrf" % nsmap['ntos'])
            if vrf is not None:
                log(_("info.vrf_node_found_with_full_uri"))
        
        if vrf is None:
            # 只有在完全找不到vrf节点时才创建新的vrf节点
            log(_("info.vrf_node_not_found_creating_new"), "warning")
            user_log(_("info.creating_vrf_node"), "info")
            
            # 创建VRF子元素 - 使用QName避免命名空间前缀
            from lxml.etree import QName
            qname = QName(nsmap['ntos'], "vrf")
            vrf = etree.SubElement(root, qname)
            # 使用name标签（符合YANG模型）
            has_name_tag = True  # 默认使用name标签
            etree.SubElement(vrf, QName(nsmap['ntos'], "name")).text = "main"
            log(_("info.vrf_node_created"))
        else:
            # 找到了vrf节点，现在检查是否有name子节点
            log(_("info.vrf_node_found"))
            
            # 检查是否有name或n元素，如果有检查值是否为"main"
            name_elem = vrf.find(".//ntos:name", namespaces=nsmap)
            if name_elem is None:
                name_elem = vrf.find(".//name")
            n_elem = vrf.find(".//ntos:n", namespaces=nsmap)
            if n_elem is None:
                n_elem = vrf.find(".//n")
            
            # 检查模板使用的是n标签还是name标签，优先使用name标签
            has_name_tag = name_elem is not None
            
            if has_name_tag:
                # 如果存在name元素
                if name_elem is not None:
                    current_name = name_elem.text
                    if current_name != 'main':
                        log(_("info.vrf_name_not_main", current=current_name), "warning")
                        name_elem.text = 'main'
                        log(_("info.corrected_vrf_name_to_main"))
                else:
                    # 如果不存在name元素，创建一个
                    from lxml.etree import QName
                    etree.SubElement(vrf, QName(nsmap['ntos'], "name")).text = "main"
                    log(_("info.added_missing_name_element"))
            else:
                # 如果存在n元素，将其替换为name元素
                if n_elem is not None:
                    current_name = n_elem.text
                    # 移除n元素
                    n_elem.getparent().remove(n_elem)
                    # 创建name元素
                    from lxml.etree import QName
                    etree.SubElement(vrf, QName(nsmap['ntos'], "name")).text = current_name if current_name == 'main' else 'main'
                    log(_("info.replaced_n_with_name_element"))
                else:
                    # 如果不存在n元素，创建name元素
                    from lxml.etree import QName
                    etree.SubElement(vrf, QName(nsmap['ntos'], "name")).text = "main"
                    log(_("info.added_missing_name_element"))
        
        # 初始化统计信息
        stats = {
            "interfaces": {
                "total": len(self.interfaces),
                "processed": 0,
                "skipped": 0,
                "invalid_interfaces": [],
                "duplicates_removed": 0
            },
            "address_objects": {
                "total": len(self.address_objects),
                "processed": 0
            },
            "service_objects": {
                "total": len(self.service_objects),
                "processed": 0
            },
            "policy_rules": {
                "total": len(self.policy_rules),
                "processed": 0
            },
            "zones": {
                "total": len(self.zones),
                "processed": 0,
                "skipped_interfaces": [],
                "skipped_interfaces_count": 0
            },
            "static_routes": {
                "total": len(self.static_routes),
                "processed": 0
            },
            "time_ranges": {
                "total": len(self.time_ranges),
                "processed": 0
            },
            "validation": {
                "errors": 0,
                "warnings": 0,
                "is_valid": True,
                "mode": "legacy"
            }
        }
        
        # 添加接口配置
        if self.interfaces:
            interface_stats = self._add_interfaces_to_xml(vrf, nsmap)
            stats["interfaces"]["processed"] = interface_stats["processed"]
            stats["interfaces"]["skipped"] = interface_stats["skipped"]
            stats["interfaces"]["invalid_interfaces"] = interface_stats["invalid_interfaces"]
            stats["interfaces"]["duplicates_removed"] = interface_stats["duplicates_removed"]
        
        # 添加区域配置
        if self.zones:
            zone_stats = self._add_zones_to_xml(vrf, nsmap)
            # 修正：zone_stats是一个元组(processed_zones, skipped_interfaces)，而不是字典
            processed_zones, skipped_interfaces = zone_stats
            stats["zones"]["processed"] = processed_zones
            stats["zones"]["skipped"] = 0  # 这里没有直接对应的值，设置为0
            stats["zones"]["skipped_interfaces"] = skipped_interfaces
            stats["zones"]["skipped_interfaces_count"] = len(skipped_interfaces)
        
        # 添加地址对象
        if self.address_objects:
            stats["address_objects"]["processed"] = self._add_address_objects_to_xml(vrf, nsmap)
        
        # 添加地址组
        if self.address_groups:
            stats["address_groups"] = {
                "total": len(self.address_groups),
                "processed": 0
            }
            stats["address_groups"]["processed"] = self._add_address_groups_to_xml(vrf, nsmap)
        
        # 添加服务对象
        if self.service_objects:
            log(_("debug.start_adding_service_objects_to_xml"), "debug", count=len(self.service_objects))
            stats["service_objects"]["processed"] = self._add_service_objects_to_xml(vrf, nsmap)
            
            # 检查服务对象是否成功添加到XML中
            service_obj_nodes = vrf.xpath(".//*[local-name()='service-obj']")
            if service_obj_nodes:
                service_obj = service_obj_nodes[0]
                service_sets = service_obj.xpath(".//*[local-name()='service-set']")
                log(_("debug.service_sets_count_after_adding"), "debug", count=len(service_sets))
                for i, service_set in enumerate(service_sets):
                    name_elem = service_set.xpath(".//*[local-name()='name']")
                    if name_elem and len(name_elem) > 0:
                        log(_("debug.service_set_in_xml"), "debug", index=i+1, name=name_elem[0].text)
                
                # 如果服务对象节点为空，则移除它
                if len(service_sets) == 0:
                    log("警告: XML中的服务对象节点为空，将移除该节点", "warning")
                    vrf.remove(service_obj)
                    log("已移除空的服务对象节点", "debug")
            else:
                log("警告: XML中没有找到服务对象节点", "warning")
        
        # 添加服务组
        if self.service_groups:
            stats["service_groups"] = {
                "total": len(self.service_groups),
                "processed": 0
            }
            stats["service_groups"]["processed"] = self._add_service_groups_to_xml(vrf, nsmap, self.service_objects)
        
        # 添加策略规则
        if self.policy_rules:
            stats["policy_rules"]["processed"] = self._add_policy_rules_to_xml(vrf, nsmap)

        # 添加NAT规则
        if self.nat_rules:
            log(_("info.start_adding_nat_rules_to_xml"), "info", count=len(self.nat_rules))
            stats["nat_rules"] = {"total": len(self.nat_rules), "processed": self._add_nat_rules_to_xml(vrf, nsmap)}

        # 添加静态路由
        if self.static_routes:
            stats["static_routes"]["processed"] = self._add_static_routes_to_xml(vrf, nsmap)
        
        # 确保有默认的"always"时间对象（用于NAT规则）
        self._ensure_default_time_objects()

        # 添加时间对象
        if hasattr(self, 'time_ranges') and self.time_ranges:
            stats["time_ranges"] = {
                "total": len(self.time_ranges),
                "processed": 0
            }
            stats["time_ranges"]["processed"] = self._add_time_ranges_to_xml(vrf, nsmap)
        
        # 确保所有vrf下的直接子元素使用正确的命名空间
        self._ensure_correct_namespaces(root, nsmap)
        
        # 检查最终XML中的服务对象节点
        service_obj_nodes = vrf.xpath(".//*[local-name()='service-obj']")
        if service_obj_nodes:
            service_obj = service_obj_nodes[0]
            service_sets = service_obj.xpath(".//*[local-name()='service-set']")
            log(_("debug.final_service_sets_count"), "debug", count=len(service_sets))
            for i, service_set in enumerate(service_sets):
                name_elem = service_set.xpath(".//*[local-name()='name']")
                if name_elem and len(name_elem) > 0:
                    log(_("debug.final_service_set_in_xml"), "debug", index=i+1, name=name_elem[0].text)
        else:
            log(_("warning.no_service_obj_node_in_final_xml"), "warning")
        
        # 生成最终XML字符串（命名空间已在_ensure_correct_namespaces中清理）
        
        # 在生成XML字符串前，清理所有空的time-range节点
        self._cleanup_empty_time_range_nodes(root)
        
        xml_str = etree.tostring(root, pretty_print=True, encoding='utf-8').decode('utf-8')
        
        # 记录XML初始大小
        initial_xml_size = len(xml_str)
        log(_("info.initial_xml_size", size=initial_xml_size))
        
        # 记录XML根元素子元素数量
        child_count = len(root)
        log(_("info.root_child_count", count=child_count))
        
        # 安全检查：确保XML字符串包含有效内容
        if len(xml_str) < 50 or '<config/>' in xml_str or '<config xmlns' not in xml_str:
            log(_("error.xml_generation_produced_invalid_output"), "error")
            # 使用xml_utils创建一个基本的XML结构
            from engine.generators.xml_utils import create_default_xml_structure
            fallback_root = create_default_xml_structure()
            
            # 添加统计信息作为注释
            comment = etree.Comment(f" 配置生成失败，生成了默认结构。统计信息: {stats} ")
            fallback_root.insert(0, comment)
            
            # 重新序列化
            xml_str = etree.tostring(fallback_root, pretty_print=True, encoding='utf-8').decode('utf-8')
            log(_("info.using_fallback_xml_structure"))
        
        # 在最终XML字符串上修复重复的xmlns属性
        log(f"调用_fix_duplicate_xmlns_in_string修复重复xmlns属性", "debug")
        xml_str_before = xml_str  # 保存修复前的XML用于比较
        xml_str = self._fix_duplicate_xmlns_in_string(xml_str)
        log(f"xmlns修复完成，修复前大小: {len(xml_str_before)}，修复后大小: {len(xml_str)}", "debug")
        
        # 修复错误的命名空间前缀
        log(f"调用_fix_namespace_prefixes修复命名空间前缀问题", "debug")
        xml_str = self._fix_namespace_prefixes(xml_str)
        log(f"命名空间前缀修复完成", "debug")
        
        # 检查修复是否导致了严重内容丢失
        if len(xml_str) < len(xml_str_before) * 0.8:  # 丢失超过20%的内容
            log(_("error.xmlns_repair_lost_significant_content"), "error")
            xml_str = xml_str_before  # 使用修复前的XML
            log(_("info.reverted_to_pre_repair_xml"))
        
        # 最终安全检查：确保修复后的XML仍然有效
        if len(xml_str) < 50 or '<config/>' in xml_str:
            log(_("error.xml_repair_produced_invalid_output"), "error")
            # 生成一个最小的有效结构
            xml_str = '<?xml version="1.0" encoding="UTF-8"?>\n<config xmlns="urn:ruijie:ntos"><vrf><n>main</n></vrf></config>'
            log(_("info.using_minimal_valid_xml"))
        
        # 检查最终XML字符串中是否包含服务对象节点
        if '<service-obj' in xml_str:
            log("最终XML字符串中包含服务对象节点", "debug")
            # 尝试提取服务对象节点的内容
            import re
            service_obj_match = re.search(r'<service-obj[^>]*>(.*?)</service-obj>', xml_str, re.DOTALL)
            if service_obj_match:
                service_obj_content = service_obj_match.group(1).strip()
                log(f"服务对象节点内容: {service_obj_content[:200]}{'...' if len(service_obj_content) > 200 else ''}", "debug")
                if not service_obj_content:
                    log("警告: 服务对象节点内容为空", "warning")
            else:
                log("警告: 无法提取服务对象节点内容", "warning")
        else:
            log("警告: 最终XML字符串中没有找到服务对象节点", "warning")
        
        # 修复所有接口的工作模式（路由模式下所有接口都应该是route模式）
        if not self.transparent_mode_result:  # 只在路由模式下执行
            xml_str = self._fix_all_interface_working_modes(xml_str)

        # 返回生成的XML和统计信息
        log(_("info.xml_generation_complete"))
        return xml_str, stats

    def _ensure_default_time_objects(self):
        """
        确保有默认的时间对象，特别是"always"时间对象用于NAT规则
        """
        if not hasattr(self, 'time_ranges'):
            self.time_ranges = []

        # 检查是否已经有"always"时间对象
        has_always = False
        for time_range in self.time_ranges:
            if time_range.get("name") == "always":
                has_always = True
                break

        # 如果没有"always"时间对象，创建一个
        if not has_always:
            always_time_object = {
                "name": "always",
                "type": "periodic",
                "time_start": "00:00:00",
                "time_end": "23:59:59",
                "weekdays": ["sun", "mon", "tue", "wed", "thu", "fri", "sat"],
                "desc": "默认时间对象，表示全天候有效"
            }
            self.time_ranges.append(always_time_object)
            log(_("info.created_default_always_time_object"))
    
    def _cleanup_empty_time_range_nodes(self, root):
        """
        清理所有空的time-range节点，并合并重复节点
        
        Args:
            root: XML根节点
        """
        from copy import deepcopy
        
        # 查找所有vrf节点
        for vrf in root.xpath(".//vrf"):
            # 使用XPath查找所有time-range节点
            time_range_elems = vrf.xpath(".//*[local-name()='time-range']")
            
            # 如果找到多个time-range节点，则合并它们
            if len(time_range_elems) > 1:
                log(_("info.found_multiple_time_range_nodes", count=len(time_range_elems)), "warning")
                
                # 使用第一个节点作为基准
                primary_node = time_range_elems[0]
                NS_TIME_RANGE = "urn:ruijie:ntos:params:xml:ns:yang:time-range"
                
                # 确保主节点有正确的命名空间
                primary_node.set("xmlns", NS_TIME_RANGE)
                
                # 合并其他节点的子元素到主节点
                for node in time_range_elems[1:]:
                    # 将当前节点的所有子元素复制到主节点
                    for child in list(node):  # 使用list()创建副本，因为我们将修改集合
                        # 检查是否已经存在同名的range元素
                        if child.tag.endswith('range') or child.tag == 'range':
                            child_name = None
                            # 查找name子元素
                            for name_elem in child.findall(".//*[local-name()='name']"):
                                child_name = name_elem.text
                                break
                            # 如果没有找到name，查找n子元素（兼容）
                            if child_name is None:
                                for n_elem in child.findall(".//*[local-name()='n']"):
                                    child_name = n_elem.text
                                    break
                            
                            # 如果找到了名称，检查主节点中是否已存在同名元素
                            if child_name is not None:
                                existing = False
                                for primary_child in primary_node.findall(".//*[local-name()='range']"):
                                    primary_name = None
                                    # 查找主节点中range的name
                                    for name_elem in primary_child.findall(".//*[local-name()='name']"):
                                        primary_name = name_elem.text
                                        break
                                    # 查找主节点中range的n
                                    if primary_name is None:
                                        for n_elem in primary_child.findall(".//*[local-name()='n']"):
                                            primary_name = n_elem.text
                                            break
                                    
                                    # 如果名称匹配，则跳过
                                    if primary_name == child_name:
                                        existing = True
                                        log(_("info.skipped_duplicate_range", name=child_name), "info")
                                        break
                                
                                # 如果不存在同名元素，则添加
                                if not existing:
                                    primary_node.append(deepcopy(child))
                                    log(_("info.merged_range_from_duplicate", name=child_name), "info")
                            else:
                                # 如果没有名称，直接添加
                                primary_node.append(deepcopy(child))
                                log(_("info.merged_unnamed_range"), "info")
                        else:
                            # 对于非range元素，直接添加
                            primary_node.append(deepcopy(child))
                    
                    # 从父节点中移除当前节点
                    parent = node.getparent()
                    if parent is not None:
                        log(_("info.removing_duplicate_time_range_node"), "info")
                        parent.remove(node)
            
            # 清理空的time-range节点
            for time_range_elem in vrf.xpath(".//*[local-name()='time-range']"):
                # 检查是否为空（没有range子元素）
                range_elements = time_range_elem.findall(".//*[local-name()='range']")
                if len(range_elements) == 0:
                    # 如果是空的，从父节点中移除
                    parent = time_range_elem.getparent()
                    if parent is not None:
                        log(_("info.removing_empty_time_range_node_final"), "info")
                        parent.remove(time_range_elem)
    
    # 这里需要包含原ntos_generator.py中的其他辅助方法，例如：
    # _add_interfaces_to_xml, _add_zones_to_xml, _add_address_objects_to_xml 等
    
    def _add_interfaces_to_xml(self, vrf, nsmap):
        """
        将接口添加到XML文档
        
        Args:
            vrf: VRF元素
            nsmap: 命名空间映射
        
        Returns:
            dict: 处理统计信息
        """
        stats = {
            "total": len(self.interfaces),
            "processed": 0,
            "skipped": 0,
            "invalid_interfaces": [],
            "duplicates_removed": 0
        }
        
        log(_("info.adding_interfaces_to_xml"))
        
        # 先查找interface节点 - 修复命名空间查找问题
        interface_elem = None
        
        # 尝试不同的方式查找interface元素
        interface_methods = [
            # 检查是否有完整命名空间的interface元素
            ("{urn:ruijie:ntos:params:xml:ns:yang:interface}interface", "完整命名空间"),
            # 检查标签名以interface结尾的元素
            ("endswith_interface", "标签结尾匹配"),
            # 检查简单的interface标签
            ("interface", "简单标签")
        ]
        
        for xpath, desc in interface_methods:
            if xpath == "endswith_interface":
                # 遍历查找
                for node in vrf:
                    if node.tag.endswith('}interface') or node.tag == 'interface':
                        interface_elem = node
                        log(_("info.found_existing_interface_node_with_method", method=desc))
                        break
            elif xpath == "interface":
                # 简单查找
                interface_elem = vrf.find('interface')
                if interface_elem is not None:
                    log(_("info.found_existing_interface_node_with_method", method=desc))
            else:
                # 完整命名空间查找
                interface_elem = vrf.find(xpath)
                if interface_elem is not None:
                    log(_("info.found_existing_interface_node_with_method", method=desc))
            
            if interface_elem is not None:
                break
        
        # 如果找不到interface节点，需要创建，并设置正确的命名空间
        if interface_elem is None:
            # 使用interface特定的命名空间创建interface节点
            interface_ns = "urn:ruijie:ntos:params:xml:ns:yang:interface"
            interface_elem = etree.SubElement(vrf, "interface")
            # 直接设置xmlns属性，不使用带命名空间的形式
            interface_elem.set("xmlns", interface_ns)
            log(_("info.created_new_interface_node"))
        
        # 处理之前先检查是否有重复接口
        duplicates_removed = self._remove_duplicate_interfaces(interface_elem)
        stats["duplicates_removed"] = duplicates_removed
        
        # 收集现有接口信息
        existing_interfaces = {}
        existing_vlans = {}
        self._collect_existing_interfaces(interface_elem, existing_interfaces, existing_vlans)
        
        # 为每个接口创建或更新配置
        # 检查self.interfaces的类型，并相应地处理
        if isinstance(self.interfaces, dict):
            # 如果是字典，按照键值对进行迭代
            for raw_name, intf_data in self.interfaces.items():
                try:
                    # 检查intf_data是否是字符串
                    if isinstance(intf_data, str):
                        log(_("warning.interface_data_is_string", name=raw_name), "warning")
                        stats["skipped"] += 1
                        stats["invalid_interfaces"].append(raw_name)
                        continue
                    
                    if not intf_data.get("name"):
                        log(_("warning.interface_missing_name_data", data=str(intf_data)), "warning")
                        stats["skipped"] += 1
                        stats["invalid_interfaces"].append(intf_data.get("raw_name", "未知"))
                        continue
                    
                    interface_name = intf_data["name"]
                    
                    # 检查是否是VLAN子接口
                    if intf_data.get("is_subinterface", False) and "vlanid" in intf_data:
                        # 处理VLAN子接口
                        if interface_name in existing_vlans:
                            # 更新现有VLAN子接口
                            log(_("info.updating_existing_vlan", name=interface_name))
                            update_vlan_interface(existing_vlans[interface_name], intf_data, self.interface_mapping)
                        else:
                            # 创建新的VLAN子接口
                            log(_("info.creating_new_vlan", name=interface_name))
                            # 确保使用正确的命名空间创建VLAN子接口
                            # 使用从yang_generator导入的函数
                            create_new_vlan_interface(interface_elem, intf_data, self.interface_mapping)
                    else:
                        # 处理物理接口
                        if interface_name in existing_interfaces:
                            # 更新现有接口
                            log(_("info.updating_existing_interface", name=interface_name))
                            update_existing_interface(existing_interfaces[interface_name], intf_data)
                        else:
                            # 创建新接口
                            log(_("info.creating_new_interface", name=interface_name))
                            create_new_interface(interface_elem, intf_data)
                    
                    stats["processed"] += 1
                except Exception as e:
                    # 安全地获取接口名称
                    if isinstance(intf_data, dict):
                        intf_name = intf_data.get("name", raw_name)
                    else:
                        intf_name = str(raw_name)
                    
                    log(_("error.failed_to_process_interface", name=intf_name, error=str(e)), "error")
                    stats["skipped"] += 1
                    stats["invalid_interfaces"].append(intf_name)
                    import traceback
                    log(traceback.format_exc(), "error")
        else:
            # 如果是列表或其他类型，按照原来的方式处理
            for intf_data in self.interfaces:
                try:
                    # 检查intf_data是否是字符串
                    if isinstance(intf_data, str):
                        log(_("warning.interface_data_is_string", name=intf_data), "warning")
                        stats["skipped"] += 1
                        stats["invalid_interfaces"].append(intf_data)
                        continue
                    
                    if not intf_data.get("name"):
                        log(_("warning.interface_missing_name_data", data=str(intf_data)), "warning")
                        stats["skipped"] += 1
                        stats["invalid_interfaces"].append(intf_data.get("raw_name", "未知"))
                        continue
                    
                    # 确保interface_name是字符串类型
                    interface_name = intf_data["name"]
                    if isinstance(interface_name, dict):
                        # 如果name是字典，尝试提取实际的接口名称
                        interface_name = interface_name.get("name", str(interface_name))
                    interface_name = str(interface_name)
                    
                    # 检查是否是VLAN子接口
                    if intf_data.get("is_subinterface", False) and "vlanid" in intf_data:
                        # 处理VLAN子接口
                        if interface_name in existing_vlans:
                            # 更新现有VLAN子接口
                            log(_("info.updating_existing_vlan", name=interface_name))
                            update_vlan_interface(existing_vlans[interface_name], intf_data, self.interface_mapping)
                        else:
                            # 创建新的VLAN子接口
                            log(_("info.creating_new_vlan", name=interface_name))
                            # 确保使用正确的命名空间创建VLAN子接口
                            # 使用从yang_generator导入的函数
                            create_new_vlan_interface(interface_elem, intf_data, self.interface_mapping)
                    else:
                        # 处理物理接口
                        if interface_name in existing_interfaces:
                            # 更新现有接口
                            log(_("info.updating_existing_interface", name=interface_name))
                            update_existing_interface(existing_interfaces[interface_name], intf_data)
                        else:
                            # 创建新接口
                            log(_("info.creating_new_interface", name=interface_name))
                            create_new_interface(interface_elem, intf_data)
                    
                    stats["processed"] += 1
                except Exception as e:
                    # 安全地获取接口名称
                    if isinstance(intf_data, dict):
                        intf_name = intf_data.get("name", "未知")
                    else:
                        intf_name = str(intf_data)
                    
                    log(_("error.failed_to_process_interface", name=intf_name, error=str(e)), "error")
                    stats["skipped"] += 1
                    stats["invalid_interfaces"].append(intf_name)
                    import traceback
                    log(traceback.format_exc(), "error")
        
        # 如果是透明模式，生成桥接接口
        if self.transparent_mode_result:
            try:
                log(_("info.generating_bridge_interface_for_transparent_mode"))
                from engine.generators.bridge_interface_generator import create_bridge_interface_generator
                bridge_generator = create_bridge_interface_generator()
                bridge_elem = bridge_generator.create_bridge_from_transparent_result(interface_elem, self.transparent_mode_result)
                if bridge_elem is not None:
                    log(_("info.bridge_interface_generated_successfully"))
                else:
                    log(_("warning.bridge_interface_generation_skipped"), "warning")
            except Exception as e:
                log(_("error.bridge_interface_generation_failed", error=str(e)), "error")

        log(_("info.interfaces_processing_complete", processed=stats["processed"], skipped=stats["skipped"]))
        return stats
    
    def _add_zones_to_xml(self, vrf, nsmap):
        """
        将区域配置添加到XML中，确保严格遵循YANG模型结构
        
        Args:
            vrf (Element): VRF元素
            nsmap (dict): 命名空间映射
            
        Returns:
            tuple: (处理的区域数, 跳过的接口列表)
        """
        if not self.zones:
            log(_("info.no_zones_to_process"))
            return 0, []
        
        # 验证区域列表的类型
        if not isinstance(self.zones, list):
            log(_("error.zone_data_not_list", type=type(self.zones)), "error")
            return 0, []
        
        # 根据YANG模型，结构应该是: 
        # <security-zone xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-zone">
        #   <zone>
        #     <name>zone_name</name>
        #     <interface>
        #       <name>interface_name</name>
        #     </interface>
        #   </zone>
        # </security-zone>
        
        # 使用准确的命名空间，添加安全检查
        security_zone_ns = nsmap.get('ntos-security-zone', "urn:ruijie:ntos:params:xml:ns:yang:security-zone")
        
        # 查找security-zone节点
        xpath = ".//*[local-name()='security-zone']"
        security_zone_nodes = vrf.xpath(xpath)
        
        if security_zone_nodes:
            security_zone_container = security_zone_nodes[0]
            # 确保现有的security-zone容器有正确的xmlns属性
            if not security_zone_container.get("xmlns"):
                security_zone_container.set("xmlns", security_zone_ns)
            log(_("info.found_existing_security_zone_container"))
        else:
            # 创建security-zone容器，并设置命名空间
            security_zone_ns = "urn:ruijie:ntos:params:xml:ns:yang:security-zone"
            # 创建普通的security-zone元素，然后设置xmlns属性让子元素继承
            security_zone_container = etree.SubElement(vrf, "security-zone")
            # 设置默认命名空间，让子元素自动继承
            security_zone_container.set("xmlns", security_zone_ns)
            log(_("info.created_security_zone_container"))
        
        # 获取现有区域配置，用于更新
        existing_zones = {}
        # 修复XPath查询语法，使用标准的查找方法而不是复杂的XPath表达式
        for zone_elem in security_zone_container.xpath(".//*[local-name()='zone']"):
            name_elem = zone_elem.xpath(".//*[local-name()='name']")
            if name_elem and len(name_elem) > 0:
                zone_name = name_elem[0].text
                existing_zones[zone_name] = zone_elem
                log(_("info.found_existing_zone", name=zone_name))
        
        # 接口名称映射，用于解析原始接口名称到NTOS接口名称的映射
        interface_mapping = {}
        if isinstance(self.interfaces, dict):
            for raw_name, intf_data in self.interfaces.items():
                if isinstance(intf_data, dict) and "name" in intf_data:
                    interface_mapping[raw_name] = intf_data["name"]
        
        # 处理每个区域
        processed_zones = 0
        skipped_interfaces = []
        
        for zone_index, zone_data in enumerate(self.zones):
            try:
                # 验证区域数据的类型
                if not isinstance(zone_data, dict):
                    log(_("error.zone_data_invalid_type", index=zone_index, type=zone_data), "error")
                    continue
                
                zone_name = zone_data.get("name")
                if not zone_name:
                    log(_("error.zone_missing_name"), "error")
                    continue
                
                log(_("info.processing_zone", name=zone_name))
                
                # 检查区域是否已存在
                if zone_name in existing_zones:
                    # 更新现有区域
                    zone_elem = existing_zones[zone_name]
                    log(_("info.updating_zone", name=zone_name))
                    
                    # 清除现有的接口配置，修复XPath查询
                    for intf_elem in zone_elem.xpath(".//*[local-name()='interface']"):
                        zone_elem.remove(intf_elem)
                else:
                    # 创建新区域 - 确保zone元素继承security-zone的命名空间
                    # 由于security_zone_container已经设置了xmlns，子元素会自动继承
                    zone_elem = etree.SubElement(security_zone_container, "zone")
                    etree.SubElement(zone_elem, "name").text = zone_name
                    log(_("info.created_zone", name=zone_name))
                
                # 添加描述（如果存在），修复XPath查询
                if "description" in zone_data:
                    desc_elem = None
                    desc_elems = zone_elem.xpath(".//*[local-name()='description']")
                    if desc_elems:
                        desc_elem = desc_elems[0]
                    if desc_elem is None:
                        desc_elem = etree.SubElement(zone_elem, "description")
                    desc_elem.text = zone_data.get("description", "")
                
                # 获取接口列表
                interfaces_data = zone_data.get("interfaces", [])
                
                # 验证接口列表的类型
                if not isinstance(interfaces_data, list):
                    log(_("error.interfaces_data_not_list", zone=zone_name, type=type(interfaces_data)), "error")
                    # 尝试将非列表转换为列表
                    if isinstance(interfaces_data, str):
                        interfaces_data = [interfaces_data]
                        log(_("info.convert_string_to_list", data=interfaces_data))
                    else:
                        log(_("error.cannot_process_interfaces_data"))
                        interfaces_data = []
                
                # 添加接口列表
                valid_interfaces_count = 0
                for intf_index, raw_intf_name in enumerate(interfaces_data):
                    try:
                        # 验证接口名称的类型
                        if not isinstance(raw_intf_name, str):
                            log(_("error.interface_name_invalid_type", zone=zone_name, index=intf_index, type=type(raw_intf_name)), "error")
                            skipped_interfaces.append({
                                "zone": zone_name,
                                "interface": str(raw_intf_name),
                                "reason": _("error.interface_name_not_string", type=type(raw_intf_name))
                            })
                            continue
                        
                        # 应用严格的接口映射处理
                        if raw_intf_name in interface_mapping:
                            ntos_intf_name = interface_mapping[raw_intf_name]
                            
                            # 创建接口节点，确保正确的层次结构
                            intf_elem = etree.SubElement(zone_elem, "interface")
                            etree.SubElement(intf_elem, "name").text = ntos_intf_name
                            
                            log(_("info.added_mapped_interface_to_zone", interface=raw_intf_name, zone=zone_name, mapped=ntos_intf_name))
                            valid_interfaces_count += 1
                        else:
                            log(_("warning.interface_not_found_in_mapping", interface=raw_intf_name, zone=zone_name), "warning")
                            skipped_interfaces.append({
                                "zone": zone_name,
                                "interface": raw_intf_name,
                                "reason": _("report.no_mapping")
                            })
                    except Exception as e:
                        # 捕获处理单个接口时的异常
                        log(_("error.interface_processing_in_zone_failed", zone=zone_name, index=intf_index, error=str(e)), "error")
                        skipped_interfaces.append({
                            "zone": zone_name,
                            "interface": str(raw_intf_name) if isinstance(raw_intf_name, (str, int, float)) else f"接口 #{intf_index}",
                            "reason": _("error.interface_processing_failed", error=str(e))
                        })
                
                # 记录有效接口数量
                log(_("info.zone_interfaces_summary", zone=zone_name, valid=valid_interfaces_count, skipped=len(interfaces_data) - valid_interfaces_count))
                processed_zones += 1
                
            except Exception as e:
                # 捕获处理单个区域时的异常
                zone_id = zone_data.get("name", f"区域 #{zone_index}") if isinstance(zone_data, dict) else f"区域 #{zone_index}"
                log(_("error.zone_processing_failed", name=zone_id, error=str(e)), "error")
        
        # 如果有被跳过的接口，记录汇总信息
        if skipped_interfaces:
            log(_("warning.skipped_zone_interfaces_summary", count=len(skipped_interfaces)), "warning")
        
        # 清理命名空间 - 确保生成的XML不包含冗余命名空间声明
        etree.cleanup_namespaces(security_zone_container)
        log(_("info.cleaned_security_zone_namespaces"))
        
        log(_("info.zones_processing_complete", processed=processed_zones))
        return processed_zones, skipped_interfaces
    
    def _add_address_objects_to_xml(self, vrf, nsmap):
        """
        添加地址对象到XML文档，确保严格遵循YANG模型结构
        
        Args:
            vrf: VRF元素
            nsmap: 命名空间映射
            
        Returns:
            int: 处理的地址对象数量
        """
        if not self.address_objects:
            log(_("info.no_address_objects_to_process"))
            return 0
            
        processed = 0
        
        # 根据YANG模型，结构应该是: 
        # <network-obj xmlns="urn:ruijie:ntos:params:xml:ns:yang:network-obj">
        #   <address-set>
        #     <name>address_name</name>
        #     <ip-set>
        #       <ip-address>***********/24</ip-address>
        #     </ip-set>
        #   </address-set>
        # </network-obj>
        
        # 使用正确的命名空间
        network_obj_ns = nsmap['ntos-network-obj']
        
        # 查找network-obj节点，确保使用正确的命名空间
        xpath = ".//*[local-name()='network-obj']"
        network_obj_nodes = vrf.xpath(xpath)
        
        if network_obj_nodes:
            network_obj = network_obj_nodes[0]
            log(_("info.found_existing_network_obj"))
        else:
            # 创建network-obj节点
            network_obj = etree.SubElement(vrf, "network-obj")
            # 直接设置xmlns属性，不使用带命名空间的形式
            network_obj.set("xmlns", network_obj_ns)
            log(_("info.created_network_obj_node"))
        
        # 查找现有的地址对象，避免重复
        existing_addresses = {}
        
        # 查找已存在的address-set节点，修复XPath查询
        for address_elem in network_obj.xpath(".//*[local-name()='address-set']"):
            name_elem = address_elem.xpath(".//*[local-name()='name']")
            if name_elem and len(name_elem) > 0:
                existing_addresses[name_elem[0].text] = address_elem
        
        # 遍历并添加地址对象
        for addr_obj in self.address_objects:
            # 跳过无效对象
            if not addr_obj.get('name'):
                log(_("warning.address_object_no_name"), "warning")
                continue
            
            name = addr_obj['name']
            
            # 检查是否已存在
            if name in existing_addresses:
                log(_("info.address_object_exists", name=name))
                continue
            
            # 获取对象类型和IP地址值
            obj_type = addr_obj.get('type', 'subnet')
            ip_value = addr_obj.get('ip_address', '')
            
            # 创建address-set元素
            addr_elem = etree.SubElement(network_obj, "address-set")
            
            # 添加名称
            etree.SubElement(addr_elem, "name").text = name
            
            # 根据对象类型添加相应的元素
            if obj_type == 'subnet' or obj_type == 'host':
                # 处理子网或主机地址对象
                if ip_value:
                    # 创建ip-set元素
                    ip_set = etree.SubElement(addr_elem, "ip-set")
                    # 添加ip-address元素
                    etree.SubElement(ip_set, "ip-address").text = ip_value
                else:
                    log(_("warning.missing_ip_address", name=name), "warning")
                    continue
            
            elif obj_type == 'range':
                # 处理IP范围对象
                if '-' in ip_value:
                    start_ip, end_ip = ip_value.split('-', 1)
                    
                    # 创建ip-set元素
                    ip_set = etree.SubElement(addr_elem, "ip-set")
                    # 添加范围格式的IP地址
                    range_text = f"{start_ip.strip()}-{end_ip.strip()}"
                    etree.SubElement(ip_set, "ip-address").text = range_text
                else:
                    log(_("warning.invalid_range_format", name=name, value=ip_value), "warning")
                    continue
            
            elif obj_type == 'fqdn':
                # 处理FQDN对象
                if ip_value:
                    # 创建ip-set元素
                    ip_set = etree.SubElement(addr_elem, "ip-set")
                    # 添加FQDN
                    etree.SubElement(ip_set, "ip-address").text = ip_value
                else:
                    log(_("warning.missing_fqdn", name=name), "warning")
                    continue
            
            else:
                log(_("warning.unknown_address_type", name=name, type=obj_type), "warning")
                continue
            
            # 添加描述（如果有）
            description = addr_obj.get('description', '')
            if description:
                etree.SubElement(addr_elem, "description").text = description
            
            log(_("info.added_address_object", name=name, type=obj_type))
            processed += 1
        
        # 清理命名空间
        etree.cleanup_namespaces(network_obj)
        
        log(_("info.address_objects_processing_complete", total=processed, success=processed, failed=0, skipped=0))
        return processed
    
    def _add_address_groups_to_xml(self, vrf, nsmap):
        """
        添加地址组到XML文档，确保严格遵循YANG模型结构
        
        Args:
            vrf: VRF元素
            nsmap: 命名空间映射
            
        Returns:
            int: 处理的地址组数量
        """
        if not hasattr(self, 'address_groups') or not self.address_groups:
            log(_("info.no_address_groups_to_process"))
            return 0
            
        processed = 0
        
        # 使用正确的命名空间
        network_obj_ns = nsmap['ntos-network-obj']
        
        # 查找network-obj节点，确保使用正确的命名空间
        xpath = ".//*[local-name()='network-obj']"
        network_obj_nodes = vrf.xpath(xpath)
        
        if network_obj_nodes:
            network_obj = network_obj_nodes[0]
            log(_("info.found_existing_network_obj"))
        else:
            # 创建network-obj节点
            network_obj = etree.SubElement(vrf, "network-obj")
            # 直接设置xmlns属性，不使用带命名空间的形式
            network_obj.set("xmlns", network_obj_ns)
            log(_("info.created_network_obj_node"))
        
        # 获取现有地址组，避免重复
        existing_groups = {}
        
        # 查找已存在的address-group节点，修复XPath查询
        for group_elem in network_obj.xpath(".//*[local-name()='address-group']"):
            name_elem = group_elem.xpath(".//*[local-name()='name']")
            if name_elem and len(name_elem) > 0:
                existing_groups[name_elem[0].text] = group_elem
        
        # 遍历并添加地址组
        for group in self.address_groups:
            # 跳过无效组
            if 'name' not in group or 'members' not in group:
                log(_("warning.address_group_missing_fields"), "warning")
                continue
            
            group_name = group['name']
            
            # 检查是否已存在
            if group_name in existing_groups:
                group_elem = existing_groups[group_name]
                log(_("info.address_group_exists", name=group_name))
                
                # 清除现有成员，修复XPath查询
                for address_set in group_elem.xpath(".//*[local-name()='address-set']"):
                    group_elem.remove(address_set)
            else:
                # 创建新的address-group元素
                group_elem = etree.SubElement(network_obj, "address-group")
                
                # 添加名称 - 使用name标签而不是n标签
                etree.SubElement(group_elem, "name").text = group_name
                
                # 添加描述(如果有)
                if 'comment' in group and group['comment']:
                    etree.SubElement(group_elem, "description").text = group['comment']
            
            # 添加成员
            for member_name in group['members']:
                # 创建address-set元素
                address_set = etree.SubElement(group_elem, "address-set")
                # 添加成员名称 - 使用name标签而不是n标签
                etree.SubElement(address_set, "name").text = member_name
            
            log(_("info.added_address_group", name=group_name, members=len(group['members'])))
            processed += 1
        
        # 清理命名空间
        etree.cleanup_namespaces(network_obj)
        
        log(_("info.address_groups_processed", success=processed))
        return processed
    
    def _add_service_objects_to_xml(self, vrf, nsmap):
        """
        添加服务对象到XML文档
        
        Args:
            vrf: VRF元素
            nsmap: 命名空间映射
            
        Returns:
            int: 处理的服务对象数量
        """
        if not self.service_objects:
            log(_("info.no_service_objects_to_process"))
            return 0
            
        processed = 0
        skipped = 0
        
        # 记录服务对象的数量和内容，用于调试
        log(_("debug.legacy_generator_processing_service_objects"), "debug", count=len(self.service_objects))
        for i, svc in enumerate(self.service_objects):
            service_name = svc.get("name", "unknown")
            service_type = svc.get("type", "unknown")
            service_protocol = svc.get("protocol", "unknown")
            log(_("debug.legacy_generator_service_object_detail"), "debug",
                index=i+1, name=service_name, type=service_type, protocol=service_protocol)
        
        # 服务对象命名空间
        service_ns = self.NS_SERVICE_OBJ
        
        # 查找或创建服务对象容器
        service_obj_xpath = ".//*[local-name()='service-obj']"
        service_obj_nodes = vrf.xpath(service_obj_xpath)
        
        if service_obj_nodes:
            service_obj = service_obj_nodes[0]
            log(_("info.found_existing_service_obj_node"))
        else:
            service_obj = etree.SubElement(vrf, "service-obj")
            service_obj.set("xmlns", service_ns)
            log(_("info.created_service_obj_node"))
            
            # 验证服务对象节点是否正确创建
            service_obj_nodes = vrf.xpath(service_obj_xpath)
            if not service_obj_nodes:
                log("错误: 无法创建服务对象节点", "error")
                return 0
            else:
                log(f"服务对象节点已创建，标签名: {service_obj.tag}", "debug")
        
        # 收集现有服务集合，避免重复
        existing_services = {}
        for service_set in service_obj.xpath(".//*[local-name()='service-set']"):
            name_elem = service_set.xpath(".//*[local-name()='name']")
            if name_elem and len(name_elem) > 0:
                existing_services[name_elem[0].text] = service_set
        
        # 不支持的协议列表
        unsupported_protocols = ["udp-lite", "sctp", "icmp6"]
        
        # 处理每个服务对象
        for service in self.service_objects:
            try:
                # 获取服务名称
                service_name = service.get("name", "unknown")
                if not service_name or service_name == "unknown":
                    log(_("warning.skipping_service_missing_name"), "warning")
                    skipped += 1
                    continue
                
                # 检查是否已存在
                if service_name in existing_services:
                    log(_("info.service_already_exists", name=service_name))
                    continue
                
                # 获取协议类型
                protocol = service.get("protocol", "").lower()
                
                # 检查是否是不支持的协议类型
                if protocol in unsupported_protocols:
                    log(_("warning.skipping_unsupported_protocol_ntos", name=service_name, protocol=protocol), "warning")
                    skipped += 1
                    continue
                
                # 检查是否存在TCP或UDP端口配置
                has_tcp_ports = "tcp_ports" in service and service["tcp_ports"]
                has_udp_ports = "udp_ports" in service and service["udp_ports"]
                
                # 根据端口范围自动推断或调整协议类型
                if has_tcp_ports and has_udp_ports and protocol != "tcp_udp":
                    log(f"服务对象 {service_name} 同时有TCP和UDP端口但协议类型为 {protocol}，自动修正为 tcp_udp", "debug")
                    protocol = "tcp_udp"
                elif has_tcp_ports and protocol != "tcp" and protocol != "tcp_udp":
                    log(f"服务对象 {service_name} 有TCP端口但协议类型为 {protocol}，自动修正为 tcp", "debug")
                    protocol = "tcp"
                elif has_udp_ports and protocol != "udp" and protocol != "tcp_udp":
                    log(f"服务对象 {service_name} 有UDP端口但协议类型为 {protocol}，自动修正为 udp", "debug")
                    protocol = "udp"
                elif not protocol:
                    if has_tcp_ports and has_udp_ports:
                        protocol = "tcp_udp"
                        log(f"服务对象 {service_name} 未指定协议类型，根据端口自动推断为 tcp_udp", "debug")
                    elif has_tcp_ports:
                        protocol = "tcp"
                        log(f"服务对象 {service_name} 未指定协议类型，根据端口自动推断为 tcp", "debug")
                    elif has_udp_ports:
                        protocol = "udp"
                        log(f"服务对象 {service_name} 未指定协议类型，根据端口自动推断为 udp", "debug")
                    else:
                        log(_("warning.service_missing_ports", name=service_name), "warning")
                        skipped += 1
                        continue
                
                # 创建服务集合元素
                service_set = etree.SubElement(service_obj, "service-set")
                
                # 添加名称
                name_elem = etree.SubElement(service_set, "name")
                name_elem.text = service_name
                
                # 添加描述
                if "description" in service and service["description"]:
                    desc_elem = etree.SubElement(service_set, "description")
                    desc_elem.text = service["description"]
                
                # 添加协议类型和端口
                if protocol in ["tcp", "udp", "tcp_udp"]:
                    # 添加TCP端口
                    if protocol in ["tcp", "tcp_udp"] and has_tcp_ports:
                        tcp_elem = etree.SubElement(service_set, "tcp")
                        port_elem = etree.SubElement(tcp_elem, "port")
                        port_elem.text = service["tcp_ports"]
                    
                    # 添加UDP端口
                    if protocol in ["udp", "tcp_udp"] and has_udp_ports:
                        udp_elem = etree.SubElement(service_set, "udp")
                        port_elem = etree.SubElement(udp_elem, "port")
                        port_elem.text = service["udp_ports"]
                
                elif protocol == "icmp":
                    # 添加ICMP类型
                    icmp_elem = etree.SubElement(service_set, "icmp")
                    
                    if "icmp_type" in service:
                        type_elem = etree.SubElement(icmp_elem, "icmp-type")
                        type_elem.text = str(service["icmp_type"])
                    
                    if "icmp_code" in service:
                        code_elem = etree.SubElement(icmp_elem, "icmp-code")
                        code_elem.text = str(service["icmp_code"])
                
                elif protocol == "ip":
                    # 添加IP协议
                    ip_elem = etree.SubElement(service_set, "ip")
                    
                    if "protocol_number" in service:
                        proto_elem = etree.SubElement(ip_elem, "protocol-number")
                        proto_elem.text = str(service["protocol_number"])
                
                # 验证服务集合是否正确添加
                service_set_xpath = f".//*[local-name()='service-set']/*[local-name()='name' and text()='{service_name}']/.."
                added_service_sets = service_obj.xpath(service_set_xpath)
                if not added_service_sets:
                    log(f"警告: 服务集合 {service_name} 可能未正确添加到XML", "warning")
                    # 尝试再次添加
                    service_obj.append(service_set)
                    # 再次验证
                    added_service_sets = service_obj.xpath(service_set_xpath)
                    if added_service_sets:
                        log(f"服务集合 {service_name} 在第二次尝试后成功添加", "debug")
                    else:
                        log(f"错误: 无法添加服务集合 {service_name}", "error")
                        skipped += 1
                        continue
                
                log(_("info.added_service", name=service_name))
                log(f"服务对象 {service_name} 添加成功", "debug")
                processed += 1
                
            except Exception as e:
                log(_("error.service_conversion_failed", name=service.get("name", "unknown"), error=str(e)), "error")
                log(f"服务对象 {service.get('name', 'unknown')} 处理失败，错误: {str(e)}", "debug")
                skipped += 1
        
        # 检查是否有服务对象被添加到XML中
        service_sets = service_obj.xpath(".//*[local-name()='service-set']")
        log(f"XML中的服务集合数量: {len(service_sets)}", "debug")
        for i, service_set in enumerate(service_sets):
            name_elem = service_set.xpath(".//*[local-name()='name']")
            if name_elem and len(name_elem) > 0:
                log(f"XML中的服务集合 #{i+1}: {name_elem[0].text}", "debug")
        
        # 检查服务对象节点是否为空
        if len(service_sets) == 0:
            log("警告: 服务对象节点为空，没有添加任何服务集合", "warning")
        
        # 清理命名空间
        etree.cleanup_namespaces(service_obj)
        
        log(_("info.service_objects_processing_complete", 
              total=len(self.service_objects), 
              success=processed, 
              failed=skipped, 
              skipped=skipped))
        return processed
    
    def _add_service_groups_to_xml(self, vrf, nsmap, service_objects=None):
        """
        添加服务组到XML文档
        
        Args:
            vrf: VRF元素
            nsmap: 命名空间映射
            service_objects (list, optional): 服务对象列表，用于验证成员
            
        Returns:
            int: 处理的服务组数量
        """
        if not hasattr(self, 'service_groups') or not self.service_groups:
            log(_("info.no_service_groups_to_process"))
            return 0
            
        # 导入服务组处理器
        from engine.processors.service_group_processor import service_group_processor

        # 使用服务组处理器处理服务组，传递映射关系
        stats = service_group_processor.process(
            vrf,
            self.service_groups,
            service_objects or self.service_objects,
            service_mapping_relationships=getattr(self, 'service_mapping_relationships', {})
        )
        
        log(_("info.service_groups_processed", success=stats["processed"]))
        return stats["processed"]
    
    def _add_policy_rules_to_xml(self, vrf, nsmap):
        """
        添加策略规则到XML文档

        Args:
            vrf: VRF元素
            nsmap: 命名空间映射

        Returns:
            int: 处理的策略规则数量
        """
        if not self.policy_rules:
            log(_("info.no_policy_rules_to_process"))
            return 0

        log(_("info.start_adding_policy_rules_to_xml"), "info", count=len(self.policy_rules))

        # 导入安全策略生成器
        from engine.generators.security_policy_generator import SecurityPolicyGenerator

        # 创建安全策略生成器实例
        security_policy_generator = SecurityPolicyGenerator()

        # 转换策略规则格式为安全策略生成器所需的格式
        security_policies = []
        processed = 0
        failed = 0

        for policy_rule in self.policy_rules:
            try:
                # 转换策略规则格式
                security_policy = self._convert_policy_rule_to_security_policy(policy_rule)
                if security_policy:
                    security_policies.append(security_policy)
                    processed += 1
                else:
                    failed += 1
                    log(_("warning.failed_to_convert_policy_rule"), "warning",
                        name=policy_rule.get("name", "unknown"))
            except Exception as e:
                failed += 1
                log(_("error.policy_rule_conversion_error"), "error",
                    name=policy_rule.get("name", "unknown"), error=str(e))

        # 如果有转换成功的安全策略，则添加到XML中
        if security_policies:
            try:
                # 使用安全策略生成器将策略添加到VRF中
                security_policy_container = security_policy_generator._integrate_with_existing_xml(
                    security_policies, vrf.getroottree().getroot()
                )

                if security_policy_container is not None:
                    log(_("info.security_policies_added_to_xml"), "info", count=len(security_policies))
                else:
                    log(_("warning.failed_to_add_security_policies_to_xml"), "warning")

            except Exception as e:
                error_msg = _("error.failed_to_integrate_security_policies", error=str(e))
                log(error_msg, "error")
                failed += len(security_policies)
                processed = 0

        log(_("info.legacy_policy_rules_processing_complete"), "info",
            total=len(self.policy_rules), success=processed, failed=failed, skipped=0)
        return processed

    def _convert_policy_rule_to_security_policy(self, policy_rule):
        """
        将策略规则转换为安全策略生成器所需的格式

        Args:
            policy_rule: 策略规则字典

        Returns:
            dict: 安全策略字典，如果转换失败则返回None
        """
        try:
            # 检查必需字段
            if not policy_rule.get("name"):
                log(_("warning.legacy_policy_rule_missing_name"), "warning")
                return None

            # 创建安全策略字典，包含所有必需字段
            security_policy = {
                "name": policy_rule["name"],
                "enabled": True,  # 默认启用
                "group-name": "def-group",  # 默认组名
                "time-range": "any",  # 默认时间范围
                "session-timeout": 0  # 默认会话超时
            }

            # 转换源区域
            if "src_zone" in policy_rule:
                security_policy["source-zone"] = {
                    "name": policy_rule["src_zone"]
                }

            # 转换目标区域（使用正确的YANG字段名dest-zone）
            if "dst_zone" in policy_rule:
                security_policy["dest-zone"] = {
                    "name": policy_rule["dst_zone"]
                }

            # 转换源网络（使用正确的YANG字段名source-network）
            if "src_addr" in policy_rule:
                security_policy["source-network"] = {
                    "name": policy_rule["src_addr"]
                }

            # 转换目标网络（使用正确的YANG字段名dest-network）
            if "dst_addr" in policy_rule:
                security_policy["dest-network"] = {
                    "name": policy_rule["dst_addr"]
                }

            # 转换服务
            if "service" in policy_rule:
                security_policy["service"] = {
                    "name": policy_rule["service"]
                }

            # 转换时间范围
            if "time_range" in policy_rule:
                security_policy["time-range"] = policy_rule["time_range"]

            # 转换动作
            if "action" in policy_rule:
                # 标准化动作值
                action = policy_rule["action"].lower()
                if action in ["allow", "accept", "permit"]:
                    security_policy["action"] = "permit"
                elif action in ["deny", "drop", "reject"]:
                    security_policy["action"] = "deny"
                else:
                    security_policy["action"] = action
            else:
                # 默认动作为permit
                security_policy["action"] = "permit"

            # 添加配置源标识（使用有效的枚举值）
            security_policy["config-source"] = "manual"  # 使用manual而不是fortigate-converted

            # 添加可选的安全功能字段（如果在原始策略中存在）
            if "description" in policy_rule:
                security_policy["description"] = policy_rule["description"]

            # 检查是否有安全功能配置
            # 处理IPS配置：检查ips_sensor字段（来自Fortigate的"set ips-sensor"）
            if "ips_sensor" in policy_rule and policy_rule["ips_sensor"]:
                # 只要存在ips_sensor字段（不管值是什么），都启用IPS功能
                security_policy["ips"] = "default-use-signature-action"
                log(_("debug.fortigate_ips_sensor_detected"), "debug",
                    policy=policy_rule["name"], sensor=policy_rule["ips_sensor"])
            elif "ips" in policy_rule:
                # 直接指定的IPS配置
                security_policy["ips"] = policy_rule["ips"]
            elif "ips_enabled" in policy_rule and policy_rule["ips_enabled"]:
                # 布尔值启用的IPS配置
                security_policy["ips"] = "default-use-signature-action"

            # 处理反病毒配置：检查av_profile字段（来自Fortigate的"set av-profile"）
            if "av_profile" in policy_rule and policy_rule["av_profile"]:
                # 只要存在av_profile字段（不管值是什么），都启用反病毒功能
                security_policy["av"] = "default-alert"
                log(_("debug.fortigate_av_profile_detected"), "debug",
                    policy=policy_rule["name"], profile=policy_rule["av_profile"])
            elif "av" in policy_rule:
                # 直接指定的反病毒配置
                security_policy["av"] = policy_rule["av"]
            elif "av_enabled" in policy_rule and policy_rule["av_enabled"]:
                # 布尔值启用的反病毒配置
                security_policy["av"] = "default-alert"

            # 处理不支持的配置项并生成告警
            self._handle_unsupported_policy_features(policy_rule)

            log(_("debug.legacy_policy_rule_converted_successfully"), "debug",
                name=policy_rule["name"], action=security_policy["action"])

            return security_policy

        except Exception as e:
            log(_("error.legacy_policy_rule_conversion_failed"), "error",
                name=policy_rule.get("name", "unknown"), error=str(e))
            return None

    def _handle_unsupported_policy_features(self, policy_rule):
        """
        处理不支持的策略功能并生成告警

        Args:
            policy_rule: 策略规则字典
        """
        policy_name = policy_rule.get("name", "unknown")

        # 处理fixedport配置
        if "fixedport" in policy_rule:
            fixedport = policy_rule["fixedport"].lower()
            if fixedport == "enable":
                warning_msg = f"策略 '{policy_name}' 的 fixedport enable 配置不被NTOS系统支持"
                log(_("fortigate.warning.fixedport_not_supported"), "warning", policy=policy_name)
                if hasattr(self, 'warnings'):
                    self.warnings.append(warning_msg)

        # 处理users配置
        if "users" in policy_rule and policy_rule["users"]:
            users_str = ", ".join(policy_rule["users"])
            warning_msg = f"策略 '{policy_name}' 的用户配置 '{users_str}' 不被NTOS系统支持"
            log(_("fortigate.warning.users_not_supported"), "warning",
                policy=policy_name, users=users_str)
            if hasattr(self, 'warnings'):
                self.warnings.append(warning_msg)

        # 处理groups配置
        if "groups" in policy_rule and policy_rule["groups"]:
            groups_str = ", ".join(policy_rule["groups"])
            warning_msg = f"策略 '{policy_name}' 的用户组配置 '{groups_str}' 不被NTOS系统支持"
            log(_("fortigate.warning.groups_not_supported"), "warning",
                policy=policy_name, groups=groups_str)
            if hasattr(self, 'warnings'):
                self.warnings.append(warning_msg)

        # 处理port-preserve配置（这个需要在NAT规则中处理，这里只记录）
        if "port_preserve" in policy_rule:
            port_preserve = policy_rule["port_preserve"].lower()
            log(_("debug.port_preserve_detected"), "debug",
                policy=policy_name, port_preserve=port_preserve)

    def _add_nat_rules_to_xml(self, vrf, nsmap):
        """
        添加NAT规则到XML文档

        Args:
            vrf: VRF元素
            nsmap: 命名空间映射

        Returns:
            int: 处理的NAT规则数量
        """
        if not self.nat_rules:
            log(_("info.no_nat_rules_to_process"), "info")
            return 0

        log(_("info.start_processing_nat_rules"), "info", count=len(self.nat_rules))

        # 导入NAT生成器
        from engine.generators.nat_generator import NATGenerator

        # 创建NAT生成器实例
        nat_generator = NATGenerator()

        # 分离NAT规则和NAT池
        nat_rules = []
        nat_pools = []

        for nat_item in self.nat_rules:
            if isinstance(nat_item, dict):
                # 检查是否是NAT池
                if "pool" in nat_item or "address" in nat_item:
                    nat_pools.append(nat_item)
                else:
                    nat_rules.append(nat_item)

        processed = 0
        failed = 0

        try:
            # 使用NAT生成器将NAT配置添加到VRF中
            nat_container = nat_generator._integrate_with_existing_xml(
                nat_rules, nat_pools, vrf.getroottree().getroot()
            )

            if nat_container is not None:
                processed = len(nat_rules) + len(nat_pools)
                log(_("info.nat_rules_added_to_xml"), "info",
                    rules=len(nat_rules), pools=len(nat_pools))
            else:
                log(_("warning.failed_to_add_nat_rules_to_xml"), "warning")
                failed = len(self.nat_rules)

        except Exception as e:
            error_msg = _("error.failed_to_integrate_nat_rules", error=str(e))
            log(error_msg, "error")
            failed = len(self.nat_rules)
            processed = 0

        log(_("info.nat_rules_processing_complete"), "info",
            total=len(self.nat_rules), success=processed, failed=failed)
        return processed

    def _add_static_routes_to_xml(self, vrf, nsmap):
        """
        添加静态路由到XML文档，确保严格遵循YANG模型结构
        所有路由应该放在同一个static标签内，每个路由使用独立的ipv4-route元素
        
        Args:
            vrf: VRF元素
            nsmap: 命名空间映射
            
        Returns:
            int: 处理的静态路由数量
        """
        processed_ipv4 = 0
        processed_ipv6 = 0
        
        # 检查是否有路由需要处理
        has_ipv4_routes = bool(self.static_routes)
        has_ipv6_routes = hasattr(self, 'static_routes_ipv6') and bool(self.static_routes_ipv6)
        
        if not has_ipv4_routes and not has_ipv6_routes:
            log(_("info.no_static_routes_to_process"))
            return 0
            
        # 根据YANG模型，静态路由应该在routing节点下的单个static标签内
        routing_ns = "urn:ruijie:ntos:params:xml:ns:yang:routing"
        
        # 查找routing节点
        xpath = ".//*[local-name()='routing']"
        routing_nodes = vrf.xpath(xpath)
        
        if routing_nodes:
            routing = routing_nodes[0]
            log(_("info.found_existing_routing_node"))
        else:
            # 创建routing节点
            routing = etree.SubElement(vrf, "routing")
            routing.set("xmlns", routing_ns)
            log(_("info.created_routing_node"))
        
        # 查找或创建static节点 - 所有路由都放在同一个static标签内
        static_xpath = ".//*[local-name()='static']"
        static_nodes = routing.xpath(static_xpath)
        
        if static_nodes:
            static = static_nodes[0]
            log(_("info.found_existing_static_node"))
        else:
            # 创建static节点
            static = etree.SubElement(routing, "static")
            log(_("info.created_static_node"))
        
        # 处理IPv4静态路由
        if has_ipv4_routes:
            log(_("info.processing_ipv4_static_routes", count=len(self.static_routes)))

            # 按目的网段分组路由，确保相同目的网段的路由合并到同一个ipv4-route节点
            grouped_routes = self._group_routes_by_destination(self.static_routes)
            log(_("info.grouped_ipv4_routes", groups=len(grouped_routes), total=len(self.static_routes)))

            for destination, route_group in grouped_routes.items():
                try:
                    log(_("info.processing_route_group", destination=destination, count=len(route_group)))

                    # 创建ipv4-route节点（每个目的网段只创建一个）
                    ipv4_route = etree.SubElement(static, "ipv4-route")
                    etree.SubElement(ipv4_route, "destination").text = destination

                    next_hop_count = 0  # 跟踪已添加的next-hop节点数量

                    # 处理该目的网段的所有路由，每个路由创建一个next-hop节点
                    for route in route_group:
                        gateway = route.get('gateway', '')
                        interface = route.get('interface', '')
                        mapped_device = route.get('mapped_device', '')  # 使用映射后的接口名称
                        distance = route.get('distance', route.get('metric', '5'))
                        description = route.get('description', route.get('comment', ''))
                        blackhole = route.get('blackhole', False)  # 检查是否是黑洞路由

                        # 检查路由是否有必要的配置（网关或设备）
                        has_gateway = gateway and gateway.strip()
                        has_device = (interface and interface.strip()) or (mapped_device and mapped_device.strip())
                        has_blackhole = blackhole in ["enable", True] or gateway == "blackhole"

                        # 如果路由没有必要的配置，则跳过这个next-hop
                        if not (has_gateway or has_device or has_blackhole):
                            log(_("warning.skipping_route_without_nexthop", destination=destination), "warning")
                            continue

                        # 特殊情况：如果路由的gateway直接就是"blackhole"
                        if gateway == "blackhole":
                            blackhole_node = etree.SubElement(ipv4_route, "next-hop")
                            etree.SubElement(blackhole_node, "next-hop").text = "blackhole"

                            # 使用提供的距离值
                            etree.SubElement(blackhole_node, "distance").text = str(distance)

                            # 设置启用状态
                            etree.SubElement(blackhole_node, "enable").text = "true"

                            # 添加描述（如果有）
                            if description:
                                etree.SubElement(blackhole_node, "descr").text = description

                            next_hop_count += 1
                        # 处理正常网关/接口的next-hop
                        elif gateway or interface or mapped_device:
                            next_hop_node = etree.SubElement(ipv4_route, "next-hop")

                            # 设置next-hop键值
                            next_hop_text = gateway
                            if mapped_device:
                                # 优先使用映射后的接口名称
                                if gateway:
                                    next_hop_text = f"{gateway}%{mapped_device}"
                                else:
                                    next_hop_text = mapped_device
                            elif interface:
                                # 如果没有映射后的接口名称，使用原始接口名称
                                if gateway:
                                    next_hop_text = f"{gateway}%{interface}"
                                else:
                                    next_hop_text = interface

                            etree.SubElement(next_hop_node, "next-hop").text = next_hop_text

                            # 设置优先级（如果有）
                            if distance:
                                etree.SubElement(next_hop_node, "distance").text = str(distance)

                            # 设置启用状态
                            etree.SubElement(next_hop_node, "enable").text = "true"

                            # 添加描述（如果有）
                            if description:
                                etree.SubElement(next_hop_node, "descr").text = description

                            next_hop_count += 1

                            # 如果指定了黑洞路由（额外添加blackhole next-hop），且尚未添加blackhole
                            if blackhole and gateway != "blackhole":
                                blackhole_node = etree.SubElement(ipv4_route, "next-hop")
                                etree.SubElement(blackhole_node, "next-hop").text = "blackhole"

                                # 黑洞路由通常使用高优先级（如254）
                                blackhole_distance = route.get('blackhole_distance', '254')
                                etree.SubElement(blackhole_node, "distance").text = str(blackhole_distance)

                                # 设置启用状态
                                etree.SubElement(blackhole_node, "enable").text = "true"

                                # 添加描述（如果有）
                                if description:
                                    etree.SubElement(blackhole_node, "descr").text = description

                                next_hop_count += 1

                    # 确保至少有一个next-hop节点（YANG模型要求）
                    if next_hop_count == 0:
                        # 如果没有添加任何next-hop，添加一个默认的blackhole next-hop
                        blackhole_node = etree.SubElement(ipv4_route, "next-hop")
                        etree.SubElement(blackhole_node, "next-hop").text = "blackhole"
                        etree.SubElement(blackhole_node, "distance").text = "254"  # 使用高优先级
                        etree.SubElement(blackhole_node, "enable").text = "true"

                        log(_("warning.adding_default_blackhole_next_hop", route=destination), "warning")
                        next_hop_count += 1

                    log(_("info.added_static_route", destination=destination, next_hops=next_hop_count))
                    processed_ipv4 += 1
                    
                except Exception as e:
                    log(_("error.failed_to_process_static_route", route=str(route.get('destination', '未知')), error=str(e)), "error")
        
        # 处理IPv6静态路由
        if has_ipv6_routes:
            log(_("info.processing_ipv6_static_routes", count=len(self.static_routes_ipv6)))
            
            # 过滤掉没有必要配置的IPv6路由
            valid_ipv6_routes = []
            for route in self.static_routes_ipv6:
                # 检查路由是否有必要的配置（网关或设备）
                has_gateway = "gateway" in route and route["gateway"]
                has_device = "interface" in route or "mapped_device" in route
                has_blackhole = "blackhole" in route and route["blackhole"] in ["enable", True]
                
                if has_gateway or has_device or has_blackhole:
                    valid_ipv6_routes.append(route)
                else:
                    log(_("warning.skipping_empty_ipv6_route", id=route.get('id', 'unknown')), "warning")
            
            # 只有当有有效的IPv6路由时，才处理它们
            if valid_ipv6_routes:
                for route in valid_ipv6_routes:
                    try:
                        # 获取路由信息
                        destination = route.get('destination', '::/0')
                        gateway = route.get('gateway', '')
                        interface = route.get('interface', '')
                        mapped_device = route.get('mapped_device', '')  # 使用映射后的接口名称
                        distance = route.get('distance', route.get('metric', '5'))
                        description = route.get('description', route.get('comment', ''))
                        blackhole = route.get('blackhole', False)  # 检查是否是黑洞路由
                        next_hop_count = 0  # 跟踪已添加的next-hop节点数量
                        
                        # 检查路由是否有必要的配置（网关或设备）
                        has_gateway = gateway and gateway.strip()
                        has_device = (interface and interface.strip()) or (mapped_device and mapped_device.strip())
                        has_blackhole = blackhole in ["enable", True] or gateway == "blackhole"
                        
                        # 如果路由没有必要的配置，则跳过
                        if not (has_gateway or has_device or has_blackhole):
                            log(_("warning.skipping_ipv6_route_without_nexthop", destination=destination), "warning")
                            continue
                        
                        # 创建ipv6-route节点
                        ipv6_route = etree.SubElement(static, "ipv6-route")
                        etree.SubElement(ipv6_route, "destination").text = destination
                        
                        # 特殊情况：如果路由的gateway直接就是"blackhole"
                        if gateway == "blackhole":
                            blackhole_node = etree.SubElement(ipv6_route, "next-hop")
                            etree.SubElement(blackhole_node, "next-hop").text = "blackhole"
                            
                            # 使用提供的距离值
                            etree.SubElement(blackhole_node, "distance").text = str(distance)
                            
                            # 设置启用状态
                            etree.SubElement(blackhole_node, "enable").text = "true"
                            
                            # 添加描述（如果有）
                            if description:
                                etree.SubElement(blackhole_node, "descr").text = description
                            
                            next_hop_count += 1
                        # 处理正常网关/接口的next-hop
                        elif gateway or interface or mapped_device:
                            next_hop_node = etree.SubElement(ipv6_route, "next-hop")
                            
                            # 设置next-hop键值
                            next_hop_text = gateway
                            if mapped_device:
                                # 优先使用映射后的接口名称
                                if gateway:
                                    next_hop_text = f"{gateway}%{mapped_device}"
                                else:
                                    next_hop_text = mapped_device
                            elif interface:
                                # 如果没有映射后的接口名称，使用原始接口名称
                                if gateway:
                                    next_hop_text = f"{gateway}%{interface}"
                                else:
                                    next_hop_text = interface
                            
                            etree.SubElement(next_hop_node, "next-hop").text = next_hop_text
                            
                            # 设置优先级（如果有）
                            if distance:
                                etree.SubElement(next_hop_node, "distance").text = str(distance)
                            
                            # 设置启用状态
                            etree.SubElement(next_hop_node, "enable").text = "true"
                            
                            # 添加描述（如果有）
                            if description:
                                etree.SubElement(next_hop_node, "descr").text = description
                            
                            next_hop_count += 1
                        
                        # 如果指定了黑洞路由（额外添加blackhole next-hop），且尚未添加blackhole
                        if blackhole and gateway != "blackhole":
                            blackhole_node = etree.SubElement(ipv6_route, "next-hop")
                            etree.SubElement(blackhole_node, "next-hop").text = "blackhole"
                            
                            # 黑洞路由通常使用高优先级（如254）
                            blackhole_distance = route.get('blackhole_distance', '254')
                            etree.SubElement(blackhole_node, "distance").text = str(blackhole_distance)
                            
                            # 设置启用状态
                            etree.SubElement(blackhole_node, "enable").text = "true"
                            
                            # 添加描述（如果有）
                            if description:
                                etree.SubElement(blackhole_node, "descr").text = description
                            
                            next_hop_count += 1
                        
                        # 确保至少有一个next-hop节点（YANG模型要求）
                        if next_hop_count == 0:
                            # 如果没有添加任何next-hop，添加一个默认的blackhole next-hop
                            blackhole_node = etree.SubElement(ipv6_route, "next-hop")
                            etree.SubElement(blackhole_node, "next-hop").text = "blackhole"
                            etree.SubElement(blackhole_node, "distance").text = "254"  # 使用高优先级
                            etree.SubElement(blackhole_node, "enable").text = "true"
                            
                            log(_("warning.adding_default_blackhole_next_hop", route=destination), "warning")
                            next_hop_count += 1
                        
                        log(_("info.added_ipv6_static_route", destination=destination, next_hops=next_hop_count))
                        processed_ipv6 += 1
                        
                    except Exception as e:
                        log(_("error.failed_to_process_ipv6_static_route", route=str(route.get('destination', '未知')), error=str(e)), "error")
            else:
                log(_("info.no_valid_ipv6_static_routes"), "info")
        
        # 清理命名空间
        etree.cleanup_namespaces(routing)
        
        total_processed = processed_ipv4 + processed_ipv6
        ipv4_total = len(self.static_routes)
        ipv6_total = len(self.static_routes_ipv6) if hasattr(self, 'static_routes_ipv6') else 0
        
        log(_("info.static_routes_processing_complete", 
              total=ipv4_total + ipv6_total, 
              success=total_processed, 
              failed=(ipv4_total + ipv6_total) - total_processed, 
              skipped=0,
              ipv4_total=ipv4_total,
              ipv4_success=processed_ipv4,
              ipv6_total=ipv6_total,
              ipv6_success=processed_ipv6))
        
        return total_processed
    
    def _add_time_ranges_to_xml(self, vrf, nsmap):
        """
        将时间对象添加到XML中
        
        Args:
            vrf: VRF XML元素
            nsmap: 命名空间映射
        
        Returns:
            int: 处理的时间对象数量
        """
        if not self.time_ranges:
            log(_("info.no_time_ranges_to_process"))
            return 0
            
        log(_("info.adding_time_ranges_to_xml", count=len(self.time_ranges)))
        
        # 添加数据结构适配步骤
        adapted_time_ranges = []
        for time_range in self.time_ranges:
            adapted = dict(time_range)  # 复制原始对象
            
            # 处理period结构
            if "period" in adapted:
                if "start" in adapted["period"]:
                    adapted["time_start"] = adapted["period"]["start"]
                if "end" in adapted["period"]:
                    adapted["time_end"] = adapted["period"]["end"]
                if "weekday" in adapted["period"] and adapted["period"]["weekday"]:
                    adapted["weekdays"] = [item["key"] for item in adapted["period"]["weekday"] if "key" in item]
                
                # 如果没有type字段，但有period，则设为periodic
                if "type" not in adapted:
                    adapted["type"] = "periodic"
            
            # 处理once结构
            elif "once" in adapted:
                if "start" in adapted["once"]:
                    adapted["datetime_start"] = adapted["once"]["start"]
                if "end" in adapted["once"]:
                    adapted["datetime_end"] = adapted["once"]["end"]
                
                # 如果没有type字段，但有once，则设为onetime
                if "type" not in adapted:
                    adapted["type"] = "onetime"
            
            # 输出调试信息
            log(_("debug.time_range_adapted", 
                 name=adapted.get("name", "unknown"),
                 has_time_start="time_start" in adapted,
                 has_time_end="time_end" in adapted,
                 has_weekdays="weekdays" in adapted and bool(adapted.get("weekdays")),
                 has_datetime_start="datetime_start" in adapted,
                 has_datetime_end="datetime_end" in adapted), "debug")
            
            adapted_time_ranges.append(adapted)
        
        # 使用适配后的时间对象
        time_ranges_to_process = adapted_time_ranges
        
        # 时间对象的命名空间
        NS_TIME_RANGE = "urn:ruijie:ntos:params:xml:ns:yang:time-range"
        
        # 获取或创建time-range节点 - 修复命名空间查找问题
        time_range_elem = None

        # 方法1：使用命名空间前缀查找
        nsmap_tr = {'tr': NS_TIME_RANGE}
        time_range_elem = vrf.find(".//tr:time-range", namespaces=nsmap_tr)
        if time_range_elem is not None:
            log(_("info.found_existing_time_range_node_with_method", method="命名空间前缀"))

        # 方法2：使用local-name()函数查找
        if time_range_elem is None:
            time_range_elems = vrf.xpath(".//*[local-name()='time-range']")
            if time_range_elems and len(time_range_elems) > 0:
                time_range_elem = time_range_elems[0]
                log(_("info.found_existing_time_range_node_with_method", method="local-name()"))

        # 方法3：使用完整URI查找
        if time_range_elem is None:
            time_range_elem = vrf.find(".//{%s}time-range" % NS_TIME_RANGE)
            if time_range_elem is not None:
                log(_("info.found_existing_time_range_node_with_method", method="完整URI"))

        # 方法4：简单查找（兼容旧方法）
        if time_range_elem is None:
            time_range_elem = vrf.find(".//time-range")
            if time_range_elem is not None:
                log(_("info.found_existing_time_range_node_with_method", method="简单查找"))

        # 如果仍然找不到，则创建新的节点
        if time_range_elem is None:
            time_range_elem = etree.SubElement(vrf, "time-range")
            time_range_elem.set("xmlns", NS_TIME_RANGE)
            log(_("info.create_time_range_node"))
        else:
            log(_("info.found_existing_time_range_node"))
        
        processed_count = 0
        
        # 处理每个时间对象
        for time_range in time_ranges_to_process:
            # 检查必要的字段
            if "name" not in time_range:
                log(_("warning.time_range_missing_name"), "warning")
                continue
            
            name = time_range["name"]
            log(_("info.processing_time_range", name=name))
            
            # 检查时间对象是否有内容（时间或星期几）
            has_time = ("time_start" in time_range and time_range["time_start"]) or \
                      ("time_end" in time_range and time_range["time_end"]) or \
                      ("datetime_start" in time_range and time_range["datetime_start"]) or \
                      ("datetime_end" in time_range and time_range["datetime_end"])
            
            has_weekdays = "weekdays" in time_range and time_range["weekdays"] and len(time_range["weekdays"]) > 0
            
            # 输出详细的调试信息
            log(_("debug.time_range_check_details", 
                 name=name,
                 has_time=has_time, 
                 has_weekdays=has_weekdays,
                 time_start=time_range.get("time_start", "None"),
                 time_end=time_range.get("time_end", "None"),
                 weekdays=str(time_range.get("weekdays", []))), "debug")
            
            # 如果时间对象没有时间设置也没有星期几设置，则跳过
            if not has_time and not has_weekdays:
                log(_("info.time_range_empty_skipped", name=name), "info")
                continue
            
            # 检查是否为自动填充的默认值对象（只有UUID的对象被自动填充了默认值）
            default_start_times = ["00:00:00", "00:00"]
            default_end_times = ["23:59:59", "23:59"]
            all_weekdays = ["sun", "mon", "tue", "wed", "thu", "fri", "sat"]
            
            is_default_time = (time_range.get("time_start") in default_start_times and 
                              time_range.get("time_end") in default_end_times)
            is_all_weekdays = set(time_range.get("weekdays", [])) == set(all_weekdays)
            
            # 如果同时满足默认时间和所有星期几，检查是否有其他非默认属性
            if is_default_time and is_all_weekdays:
                # 检查是否只有基本属性和UUID
                basic_attrs = {"name", "type", "time_start", "time_end", "weekdays", "uuid"}
                non_default_attrs = [attr for attr in time_range.keys() if attr not in basic_attrs]
                
                if not non_default_attrs:
                    log(_("info.time_range_default_values_skipped", name=name), "info")
                    continue
            
            # 查找是否已存在同名时间对象
            existing_range = None
            for range_elem in time_range_elem.findall("./range"):
                # 获取名称元素 - 使用多种方法查找name
                name_elem = range_elem.find("./name")
                if name_elem is None:
                    name_elem = range_elem.find(".//name")
                if name_elem is None:
                    continue
                
                # 比较名称
                if name_elem.text == name:
                    existing_range = range_elem
                    log(_("info.found_existing_time_range", name=name))
                    break

            # 如果不存在同名时间对象，创建新的
            if existing_range is None:
                # 创建新的range元素
                range_elem = etree.SubElement(time_range_elem, "range")
                
                # 添加名称
                name_elem = etree.SubElement(range_elem, "name")
                name_elem.text = name
                
                log(_("info.created_time_range", name=name))
            else:
                range_elem = existing_range
                log(_("info.using_existing_time_range", name=name))
            
            # 移除可能存在的type标签（不符合YANG schema）
            type_elem = range_elem.find("./type")
            if type_elem is not None:
                range_elem.remove(type_elem)
                log(_("debug.removed_type_tag", name=name), "debug")
            
            # 处理周期性时间对象（period标签内的内容）
            if "weekdays" in time_range or "time_start" in time_range or "time_end" in time_range:
                # 先删除可能存在的直接子级的start和end标签
                for direct_child in ["start", "end"]:
                    direct_elem = range_elem.find(f"./{direct_child}")
                    if direct_elem is not None:
                        range_elem.remove(direct_elem)
                
                # 创建或获取period标签
                period_elem = range_elem.find("./period")
                if period_elem is None:
                    period_elem = etree.SubElement(range_elem, "period")
                
                # 添加时间信息到period标签内
                if "time_start" in time_range and time_range["time_start"]:
                    start_elem = period_elem.find("./start")
                    if start_elem is None:
                        start_elem = etree.SubElement(period_elem, "start")
                    start_elem.text = time_range["time_start"]
                
                if "time_end" in time_range and time_range["time_end"]:
                    end_elem = period_elem.find("./end")
                    if end_elem is None:
                        end_elem = etree.SubElement(period_elem, "end")
                    end_elem.text = time_range["time_end"]
                
                # 添加星期几信息
                if "weekdays" in time_range and time_range["weekdays"]:
                    # 清除现有的weekday元素
                    for weekday_elem in period_elem.findall("./weekday"):
                        period_elem.remove(weekday_elem)
                    
                    # 添加新的weekday元素
                    for day in time_range["weekdays"]:
                        weekday_elem = etree.SubElement(period_elem, "weekday")
                        key_elem = etree.SubElement(weekday_elem, "key")
                        key_elem.text = day
            
            # 处理一次性时间对象（once标签）
            elif "datetime_start" in time_range or "datetime_end" in time_range:
                # 创建或获取once标签
                once_elem = range_elem.find("./once")
                if once_elem is None:
                    once_elem = etree.SubElement(range_elem, "once")
                
                # 添加时间信息到once标签内
                if "datetime_start" in time_range and time_range["datetime_start"]:
                    start_elem = once_elem.find("./start")
                    if start_elem is None:
                        start_elem = etree.SubElement(once_elem, "start")
                    start_elem.text = time_range["datetime_start"]
                
                if "datetime_end" in time_range and time_range["datetime_end"]:
                    end_elem = once_elem.find("./end")
                    if end_elem is None:
                        end_elem = etree.SubElement(once_elem, "end")
                    end_elem.text = time_range["datetime_end"]
            
            processed_count += 1
        
        # 返回处理的时间对象数量
        return processed_count
    
    def _fix_duplicate_xmlns_in_string(self, xml_str):
        """
        在XML字符串级别修复重复的xmlns属性
        这是最后的安全措施，确保即使在XML序列化过程中产生的重复xmlns也能被清理
        
        Args:
            xml_str (str): XML字符串
            
        Returns:
            str: 修复后的XML字符串
        """
        import re
        
        # 检查原始XML字符串
        if not xml_str or len(xml_str) < 20:
            log(_("error.xml_string_too_short", length=len(xml_str)), "error")
            # 如果XML字符串太短，可能已经损坏，返回基本结构 - 使用name标签而不是n标签
            return '<?xml version="1.0" encoding="UTF-8"?>\n<config xmlns="urn:ruijie:ntos"><vrf><name>main</name></vrf></config>'
        
        # 保存原始XML字符串用于安全检查
        original_xml = xml_str
        
        # 多种重复xmlns模式的正则表达式
        patterns = [
            # 修复重复的相同xmlns属性: xmlns="uri" xmlns="uri"
            r'xmlns="([^"]+)"\s+xmlns="\1"',
            # 修复更复杂的重复: xmlns="uri" 其他属性 xmlns="uri"
            r'xmlns="([^"]+)"([^>]*?)xmlns="\1"',
            # 修复多个重复
            r'(xmlns="[^"]+")(\s+\1)+',
        ]
        
        fixed_count = 0
        
        for pattern in patterns:
            matches = re.findall(pattern, xml_str)
            if matches:
                print(f"[DEBUG] 发现重复xmlns模式: {pattern}, 匹配数: {len(matches)}")
                if pattern == r'xmlns="([^"]+)"\s+xmlns="\1"':
                    # 简单重复: xmlns="uri" xmlns="uri" -> xmlns="uri"
                    xml_str = re.sub(pattern, r'xmlns="\1"', xml_str)
                elif pattern == r'xmlns="([^"]+)"([^>]*?)xmlns="\1"':
                    # 复杂重复: xmlns="uri" 其他属性 xmlns="uri" -> xmlns="uri" 其他属性
                    xml_str = re.sub(pattern, r'xmlns="\1"\2', xml_str)
                elif pattern == r'(xmlns="[^"]+")(\s+\1)+':
                    # 多个重复: xmlns="uri" xmlns="uri" xmlns="uri" -> xmlns="uri"
                    xml_str = re.sub(pattern, r'\1', xml_str)
                fixed_count += len(matches)
        
        if fixed_count > 0:
            print(f"[DEBUG] 在字符串级别修复了 {fixed_count} 个重复的xmlns属性")
        
        # 安全检查：确保没有破坏XML结构
        if '<config' not in xml_str or '</config>' not in xml_str:
            log(_("error.xml_structure_damaged_during_fixing"), "error")
            # 尝试恢复基本结构
            if '<config' in original_xml and '</config>' in original_xml:
                # 如果原始XML有完整的config标签，使用原始XML
                return original_xml
            else:
                # 否则返回最小的有效结构 - 使用name标签而不是n标签
                return '<?xml version="1.0" encoding="UTF-8"?>\n<config xmlns="urn:ruijie:ntos"><vrf><name>main</name></vrf></config>'
        
        # 检查是否只剩下空的config标签
        if re.match(r'\s*<config\s*/>.*', xml_str):
            log(_("error.empty_config_tag_detected"), "error")
            # 返回最小的有效结构 - 使用name标签而不是n标签
            return '<?xml version="1.0" encoding="UTF-8"?>\n<config xmlns="urn:ruijie:ntos"><vrf><name>main</name></vrf></config>'
        
        # 修复<n>标签为<name>标签
        xml_str = re.sub(r'<n>([^<]*)</n>', r'<name>\1</name>', xml_str)
        
        return xml_str
    
    def _fix_namespace_prefixes(self, xml_str):
        """
        修复错误的命名空间前缀，比如xmlns:ns14="http://www\.w3\.org/2000/xmlns/" ns14:xmlns="..."
        
        Args:
            xml_str (str): XML字符串
            
        Returns:
            str: 修复后的XML字符串
        """
        import re
        
        # 修复 xmlns:ns14="http://www\.w3\.org/2000/xmlns/" ns14:xmlns="..." 的问题
        pattern = r'xmlns:ns\d+="http://www\.w3\.org/2000/xmlns/" ns\d+:xmlns="([^"]+)"'
        xml_str = re.sub(pattern, r'xmlns="\1"', xml_str)
        
        # 修复其他潜在的错误形式
        pattern = r'xmlns:([a-zA-Z0-9]+)="http://www\.w3\.org/2000/xmlns/" \1:xmlns="([^"]+)"'
        xml_str = re.sub(pattern, r'xmlns="\2"', xml_str)
        
        # 修复重复的xmlns前缀声明
        pattern = r'(xmlns:[a-zA-Z0-9]+="[^"]+")(\s+\1)+'
        xml_str = re.sub(pattern, r'\1', xml_str)
        
        return xml_str
    
    def _remove_duplicate_interfaces(self, interface_elem):
        """
        从XML中移除重复的接口
        
        Args:
            interface_elem: 接口元素
            
        Returns:
            int: 移除的接口数量
        """
        # 定义命名空间
        nsmap = {
            'if': "urn:ruijie:ntos:params:xml:ns:yang:interface",
            'vlan': "urn:ruijie:ntos:params:xml:ns:yang:vlan"
        }
        
        # 跟踪已处理的接口名称
        processed_names = {}
        removed_count = 0
        
        # 收集所有接口（物理接口和VLAN接口）
        all_interfaces = []
        
        # 1. 物理接口 - 使用改进的命名空间查询方式
        # 1.1 带if命名空间的查询（最有效的方式）
        try:
            phy_with_ns = interface_elem.findall("if:physical", namespaces=nsmap)
            all_interfaces.extend([(elem, "physical") for elem in phy_with_ns])
        except Exception as e:
            log(_("debug.physical_interface_namespace_query_failed", error=str(e)), "debug")
        
        # 1.2 使用完整URI的查询
        try:
            phy_with_uri = interface_elem.findall("{%s}physical" % nsmap['if'])
            # 避免重复添加
            for elem in phy_with_uri:
                if (elem, "physical") not in all_interfaces:
                    all_interfaces.append((elem, "physical"))
        except Exception as e:
            log(_("debug.physical_interface_uri_query_failed", error=str(e)), "debug")
        
        # 1.3 不带命名空间的查询（作为备选）
        try:
            phy_no_ns = interface_elem.findall("physical")
            for elem in phy_no_ns:
                if (elem, "physical") not in all_interfaces:
                    all_interfaces.append((elem, "physical"))
        except Exception as e:
            log(_("debug.physical_interface_no_namespace_query_failed", error=str(e)), "debug")
        
        # 2. VLAN子接口 - 使用改进的命名空间查询方式
        # 2.1 带vlan命名空间的查询
        try:
            vlan_with_ns = interface_elem.findall("vlan:vlan", namespaces=nsmap)
            all_interfaces.extend([(elem, "vlan") for elem in vlan_with_ns])
        except Exception as e:
            log(_("debug.vlan_interface_namespace_query_failed", error=str(e)), "debug")
        
        # 2.2 使用完整URI的查询
        try:
            vlan_with_uri = interface_elem.findall("{%s}vlan" % nsmap['vlan'])
            for elem in vlan_with_uri:
                if (elem, "vlan") not in all_interfaces:
                    all_interfaces.append((elem, "vlan"))
        except Exception as e:
            log(_("debug.vlan_interface_uri_query_failed", error=str(e)), "debug")
        
        # 2.3 不带命名空间的查询
        try:
            vlan_no_ns = interface_elem.findall("vlan")
            for elem in vlan_no_ns:
                if (elem, "vlan") not in all_interfaces:
                    all_interfaces.append((elem, "vlan"))
        except Exception as e:
            log(_("debug.vlan_interface_no_namespace_query_failed", error=str(e)), "debug")
        
        # 只有当存在接口时才记录日志
        if all_interfaces:
            log(_("info.removing_duplicate_interfaces"))
        
        # 遍历所有接口并检查重复
        for intf, intf_type in all_interfaces:
            # 尝试不同的方式查找name元素，使用改进的方法
            name_elem = None
            name_methods = [
                ("if:name", lambda: intf.find("if:name", namespaces=nsmap)),
                ("{uri}name", lambda: intf.find("{%s}name" % nsmap['if'])),
                ("name", lambda: intf.find("name")),
            ]
            
            for method_name, method_func in name_methods:
                try:
                    found_elem = method_func()
                    if found_elem is not None and found_elem.text:
                        name_elem = found_elem
                        break
                except Exception as e:
                    log(_("debug.name_query_method_failed", method=method_name, error=str(e)), "debug")
            
            if name_elem is not None and name_elem.text:
                interface_name = name_elem.text
                interface_key = f"{intf_type}:{interface_name}"
                
                if interface_key in processed_names:
                    # 如果遇到重复的接口，将其从父元素中移除
                    log(_("info.removing_duplicate_interface", type=intf_type, name=interface_name))
                    interface_elem.remove(intf)
                    removed_count += 1
                else:
                    # 记录此接口为已处理
                    processed_names[interface_key] = intf
        
        # 只有在移除了接口时才记录日志
        if removed_count > 0:
            log(_("info.removed_duplicate_interfaces", count=removed_count))
        
        return removed_count
    
    def _collect_existing_interfaces(self, interface_elem, existing_interfaces, existing_vlans):
        """
        收集现有的物理接口和VLAN接口
        
        Args:
            interface_elem: 接口元素
            existing_interfaces: 用于存储物理接口的字典
            existing_vlans: 用于存储VLAN接口的字典
        """
        log(_("info.collecting_existing_interfaces"))
        
        # 定义命名空间
        nsmap = {
            'if': "urn:ruijie:ntos:params:xml:ns:yang:interface",
            'vlan': "urn:ruijie:ntos:params:xml:ns:yang:vlan"
        }
        
        # 使用不同的查找方式来确保捕获所有接口类型
        
        # 1. 物理接口 - 重点修复查找方式
        physical_interfaces = []
        
        # 1.1 带if命名空间的查询（最有效的方式）
        try:
            phy_with_ns = interface_elem.findall("if:physical", namespaces=nsmap)
            physical_interfaces.extend(phy_with_ns)
            log(_("info.found_physical_interfaces_with_namespace", count=len(phy_with_ns)))
        except Exception as e:
            log(_("warning.physical_interface_namespace_query_failed", error=str(e)), "warning")
        
        # 1.2 使用完整URI的查询
        try:
            phy_with_uri = interface_elem.findall("{%s}physical" % nsmap['if'])
            # 避免重复添加
            for phy in phy_with_uri:
                if phy not in physical_interfaces:
                    physical_interfaces.append(phy)
            log(_("info.found_physical_interfaces_with_uri", count=len(phy_with_uri)))
        except Exception as e:
            log(_("warning.physical_interface_uri_query_failed", error=str(e)), "warning")
        
        # 1.3 不带命名空间的查询（作为备选）
        try:
            phy_no_ns = interface_elem.findall("physical")
            for phy in phy_no_ns:
                if phy not in physical_interfaces:
                    physical_interfaces.append(phy)
            log(_("info.found_physical_interfaces_no_namespace", count=len(phy_no_ns)))
        except Exception as e:
            log(_("warning.physical_interface_no_namespace_query_failed", error=str(e)), "warning")
        
        # 处理物理接口
        for phy in physical_interfaces:
            # 尝试不同的方式查找name元素，优先使用命名空间查询
            name_elem = None
            name_methods = [
                ("if:name", lambda: phy.find("if:name", namespaces=nsmap)),
                ("{uri}name", lambda: phy.find("{%s}name" % nsmap['if'])),
                ("name", lambda: phy.find("name")),
            ]
            
            for method_name, method_func in name_methods:
                try:
                    found_elem = method_func()
                    if found_elem is not None and found_elem.text:
                        name_elem = found_elem
                        log(_("info.found_physical_interface_name_with_method", name=found_elem.text, method=method_name))
                        break
                except Exception as e:
                    log(_("debug.name_query_method_failed", method=method_name, error=str(e)), "debug")
            
            if name_elem is not None and name_elem.text:
                existing_interfaces[name_elem.text] = phy
            else:
                log(_("warning.physical_interface_without_name"), "warning")
                
            # 检查PPPoE配置并记录已使用的隧道ID
            self._check_pppoe_tunnel(phy)
        
        # 2. VLAN子接口 - 重点修复查找方式
        vlan_interfaces = []
        
        # 2.1 带vlan命名空间的查询
        try:
            vlan_with_ns = interface_elem.findall("vlan:vlan", namespaces=nsmap)
            vlan_interfaces.extend(vlan_with_ns)
            log(_("info.found_vlan_interfaces_with_namespace", count=len(vlan_with_ns)))
        except Exception as e:
            log(_("warning.vlan_interface_namespace_query_failed", error=str(e)), "warning")
        
        # 2.2 使用完整URI的查询
        try:
            vlan_with_uri = interface_elem.findall("{%s}vlan" % nsmap['vlan'])
            for vlan in vlan_with_uri:
                if vlan not in vlan_interfaces:
                    vlan_interfaces.append(vlan)
            log(_("info.found_vlan_interfaces_with_uri", count=len(vlan_with_uri)))
        except Exception as e:
            log(_("warning.vlan_interface_uri_query_failed", error=str(e)), "warning")
        
        # 2.3 不带命名空间的查询
        try:
            vlan_no_ns = interface_elem.findall("vlan")
            for vlan in vlan_no_ns:
                if vlan not in vlan_interfaces:
                    vlan_interfaces.append(vlan)
            log(_("info.found_vlan_interfaces_no_namespace", count=len(vlan_no_ns)))
        except Exception as e:
            log(_("warning.vlan_interface_no_namespace_query_failed", error=str(e)), "warning")
        
        # 处理VLAN接口
        for vlan in vlan_interfaces:
            # 尝试不同的方式查找name元素，优先使用命名空间查询
            name_elem = None
            name_methods = [
                ("if:name", lambda: vlan.find("if:name", namespaces=nsmap)),
                ("vlan:name", lambda: vlan.find("vlan:name", namespaces=nsmap)),
                ("{if-uri}name", lambda: vlan.find("{%s}name" % nsmap['if'])),
                ("{vlan-uri}name", lambda: vlan.find("{%s}name" % nsmap['vlan'])),
                ("name", lambda: vlan.find("name")),
            ]
            
            for method_name, method_func in name_methods:
                try:
                    found_elem = method_func()
                    if found_elem is not None and found_elem.text:
                        name_elem = found_elem
                        log(_("info.found_vlan_interface_name_with_method", name=found_elem.text, method=method_name))
                        break
                except Exception as e:
                    log(_("debug.vlan_name_query_method_failed", method=method_name, error=str(e)), "debug")
            
            if name_elem is not None and name_elem.text:
                existing_vlans[name_elem.text] = vlan
            else:
                log(_("warning.vlan_interface_without_name"), "warning")
                
            # 检查PPPoE配置并记录已使用的隧道ID
            self._check_pppoe_tunnel(vlan)
        
        log(_("info.collected_interfaces", 
              physical_count=len(existing_interfaces), 
              vlan_count=len(existing_vlans)))
    
    def _check_pppoe_tunnel(self, interface_elem):
        """
        检查接口中的PPPoE配置，并记录已使用的隧道ID
        
        Args:
            interface_elem: 接口元素
        """
        # 定义命名空间
        nsmap = {
            'if': "urn:ruijie:ntos:params:xml:ns:yang:interface",
            'pppoe': "urn:ruijie:ntos:params:xml:ns:yang:pppoe"
        }
        
        # 查找PPPoE配置
        pppoe_elems = []
        
        # 不带命名空间的查询
        pppoe_elems.extend(interface_elem.findall(".//pppoe"))
        
        # 带命名空间的查询
        pppoe_elems.extend(interface_elem.findall(".//pppoe:pppoe", namespaces=nsmap))
        
        # 使用完整URI的查询
        pppoe_elems.extend(interface_elem.findall(".//{%s}pppoe" % nsmap['pppoe']))
        
        for pppoe in pppoe_elems:
            # 查找隧道ID
            tunnel_id_elems = []
            tunnel_id_elems.extend(pppoe.findall(".//tunnel-id"))
            tunnel_id_elems.extend(pppoe.findall(".//pppoe:tunnel-id", namespaces=nsmap))
            tunnel_id_elems.extend(pppoe.findall(".//{%s}tunnel-id" % nsmap['pppoe']))
            
            for tunnel_id_elem in tunnel_id_elems:
                if tunnel_id_elem is not None and tunnel_id_elem.text:
                    try:
                        tunnel_id = int(tunnel_id_elem.text)
                        used_pppoe_tunnel_ids.add(tunnel_id)
                        log(_("info.registered_existing_tunnel_id", id=tunnel_id))
                    except (ValueError, TypeError) as e:
                        log(_("warning.invalid_tunnel_id", id=tunnel_id_elem.text, error=str(e)), "warning")
                    break

    def _ensure_correct_namespaces(self, root, nsmap):
        """
        确保所有VRF下的直接子元素使用正确的命名空间
        
        Args:
            root: XML根节点  
            nsmap: 命名空间映射
        """
        from engine.generators.xml_utils import NAMESPACE_MAPPING
        from engine.utils.i18n import _
        
        log(_("info.checking_and_fixing_namespaces"))
        
        # 记录原始子元素数量
        original_root_child_count = len(root)
        original_child_total = sum(1 for elem in root.iter())
        
        # 保存清理前的XML字符串作为备份
        import copy 
        root_backup = copy.deepcopy(root)
        
        # 确保根节点有正确的命名空间
        if None not in root.nsmap or root.nsmap[None] != "urn:ruijie:ntos":
            # 创建新的根节点并迁移子元素
            log(_("info.fix_root_namespace"))
            nsmap = {None: "urn:ruijie:ntos"}
            new_root = etree.Element("config", nsmap=nsmap)
            for child in list(root):  # 使用list复制避免修改时的问题
                new_root.append(copy.deepcopy(child))
            root = new_root
        
        # 查找所有VRF节点
        vrf_nodes = root.xpath(".//vrf")
        
        # 如果没有找到VRF节点，添加一个
        if not vrf_nodes:
            log(_("info.no_vrf_nodes_found_creating"), "warning")
            # 从utils导入而不是直接创建
            from engine.generators.xml_utils import find_or_create_vrf
            vrf = find_or_create_vrf(root)
            vrf_nodes = [vrf]
        
        for vrf in vrf_nodes:
            # 遍历VRF的直接子元素
            for child in list(vrf):
                tag_name = child.tag
                
                # 去除命名空间前缀，获取基本标签名
                if '}' in tag_name:
                    base_tag = tag_name.split('}')[1]
                else:
                    base_tag = tag_name
                
                # 检查是否需要设置特定命名空间
                if base_tag in NAMESPACE_MAPPING:
                    expected_ns = NAMESPACE_MAPPING[base_tag]
                    current_xmlns = child.get('xmlns')
                    
                    # 如果当前没有xmlns或者xmlns不正确，设置正确的命名空间
                    if current_xmlns != expected_ns:
                        child.set('xmlns', expected_ns)
                        log(_("info.fixed_namespace_for_element", 
                             element=base_tag, 
                             old_tag=current_xmlns or "None", 
                             new_tag=expected_ns))
        
        # 修复节点名称问题 - 将 'n' 标签改为 'name'
        for elem in list(root.iter()):
            if not isinstance(elem.tag, str):
                continue
            if elem.tag.endswith('n') and elem.tag.split('}')[-1] == 'n':
                # 获取标签的命名空间前缀
                ns_prefix = ''
                if '}' in elem.tag:
                    ns_prefix = elem.tag.split('}')[0] + '}'
                
                # 创建新的 'name' 元素并复制内容
                new_elem = etree.Element(ns_prefix + 'name')
                new_elem.text = elem.text
                for attr_name, attr_value in elem.attrib.items():
                    new_elem.set(attr_name, attr_value)
                
                # 复制所有子元素
                for child in list(elem):
                    elem.remove(child)
                    new_elem.append(child)
                
                # 替换旧元素
                parent = elem.getparent()
                if parent is not None:
                    index = parent.index(elem)
                    parent.remove(elem)
                    parent.insert(index, new_elem)
                    log(_("info.fixed_node_name", old="n", new="name"))
        
        # 使用自定义方法清理命名空间，避免使用可能不安全的lxml内置方法
        cleaned_root = self._custom_namespace_cleanup(root)
        
        # 安全检查：确保清理后的XML不是空的
        if len(cleaned_root) == 0 and original_root_child_count > 0:
            log(_("error.namespace_cleanup_lost_all_content"), "error")
            # 使用备份
            return root_backup
        
        # 确保我们没有丢失太多元素
        cleaned_child_total = sum(1 for elem in cleaned_root.iter())
        if cleaned_child_total < original_child_total * 0.9:  # 如果丢失超过10%的元素
            log(_("warning.namespace_cleanup_lost_many_elements", 
                  original=original_child_total, 
                  remaining=cleaned_child_total), "warning")
            # 使用备份
            return root_backup
        
        log(_("info.namespaces_fixed"))
        return cleaned_root
    
    def _custom_namespace_cleanup(self, root):
        """
        自定义命名空间清理，避免使用lxml的cleanup_namespaces方法
        
        Args:
            root: XML根节点
            
        Returns:
            Element: 处理后的XML根节点
        """
        # 保存根节点默认命名空间
        root_ns = root.nsmap.get(None)
        if not root_ns:
            root_ns = "urn:ruijie:ntos"
        
        # 创建新的根节点，保留默认命名空间
        new_root = etree.Element(root.tag, nsmap={None: root_ns})
        
        # 复制根节点的属性和文本
        new_root.text = root.text
        new_root.tail = root.tail
        for attr_name, attr_value in root.attrib.items():
            if attr_name != 'xmlns':  # 避免重复的xmlns属性
                new_root.set(attr_name, attr_value)
        
        # 深度复制所有子元素，避免命名空间问题
        self._deep_copy_elements(root, new_root)
        
        # 检查结果，确保根节点有子元素
        if len(new_root) == 0 and len(root) > 0:
            log(_("error.custom_cleanup_lost_all_children"), "error")
            return root  # 返回原始根节点
            
        return new_root
    
    def _deep_copy_elements(self, source_elem, target_elem):
        """
        深度复制元素及其所有子元素，保留必要的命名空间
        
        Args:
            source_elem: 源元素
            target_elem: 目标元素
        """
        from engine.generators.xml_utils import NAMESPACE_MAPPING
        
        # 遍历源元素的所有子元素
        for child in source_elem:
            # 获取子元素的标签名，去除命名空间前缀
            if not isinstance(child.tag, str):
                continue
                
            if '}' in child.tag:
                tag_name = child.tag.split('}')[1]
            else:
                tag_name = child.tag
            
            # 创建新的子元素
            new_child = etree.SubElement(target_elem, tag_name)
            
            # 复制文本和属性
            new_child.text = child.text
            new_child.tail = child.tail
            
            # 复制除xmlns外的所有属性
            for attr_name, attr_value in child.attrib.items():
                if attr_name != 'xmlns':
                    new_child.set(attr_name, attr_value)
            
            # 对于特定标签，添加正确的命名空间
            if tag_name in NAMESPACE_MAPPING:
                new_child.set('xmlns', NAMESPACE_MAPPING[tag_name])
            
            # 递归处理子元素
            self._deep_copy_elements(child, new_child)
    
    def _convert_port_range(self, port_range_str):
        """
        转换端口范围字符串为NTOS格式
        
        Args:
            port_range_str (str): 端口范围字符串，如 "1521" 或 "88 464" 或 "67 68"
            
        Returns:
            str: NTOS格式的端口范围
        """
        if not port_range_str:
            return ""
        
        # 处理复杂端口格式，如 "80-87:1-1024 110:1-2048 25:1-65000"
        if ":" in port_range_str:
            # 分割成多个部分，每部分是一个端口范围
            parts = port_range_str.split()
            result_ports = []
            
            for part in parts:
                # 如果包含冒号，取冒号前面的部分作为目标端口
                if ":" in part:
                    dest_port = part.split(":")[0]
                    result_ports.append(dest_port)
                else:
                    result_ports.append(part)
            
            # 使用逗号连接不同的端口或端口范围
            return ",".join(result_ports)
        
        # 处理简单端口格式
        parts = port_range_str.split()
        
        # 处理单个端口
        if len(parts) == 1:
            return parts[0]
        
        # 处理两个端口 - 可能是范围或两个独立端口
        if len(parts) == 2:
            try:
                port1 = int(parts[0])
                port2 = int(parts[1])
                
                # 如果第二个端口大于第一个端口，认为是范围
                if port2 > port1:
                    return f"{port1}-{port2}"
                # 否则认为是两个独立端口
                else:
                    return f"{port1},{port2}"
            except ValueError:
                # 非整数端口，直接用逗号连接
                return ",".join(parts)
        
        # 处理多个端口 - 用逗号连接
        return ",".join(parts)

    def _group_routes_by_destination(self, routes):
        """
        按目的网段对路由进行分组

        根据YANG模型要求，相同目的网段的路由应该在同一个ipv4-route节点下
        使用多个next-hop子节点，而不是创建多个ipv4-route节点

        Args:
            routes (list): 路由列表

        Returns:
            dict: 按目的网段分组的路由字典 {destination: [route1, route2, ...]}
        """
        grouped = {}

        for route in routes:
            destination = route.get('destination', '0.0.0.0/0')

            if destination not in grouped:
                grouped[destination] = []

            grouped[destination].append(route)

        return grouped

    def _fix_all_interface_working_modes(self, xml_str):
        """
        修复所有接口的工作模式为route模式（路由模式下）

        Args:
            xml_str (str): XML字符串

        Returns:
            str: 修复后的XML字符串
        """
        try:
            log("开始修复所有接口的工作模式为route模式", "info")

            # 使用正则表达式替换所有的bridge模式为route模式
            import re

            # 统计修复前的数量
            bridge_count_before = len(re.findall(r'<working-mode>bridge</working-mode>', xml_str))
            route_count_before = len(re.findall(r'<working-mode>route</working-mode>', xml_str))

            log(f"修复前统计: Bridge模式={bridge_count_before}, Route模式={route_count_before}", "info")

            # 将所有bridge模式替换为route模式
            xml_str = re.sub(r'<working-mode>bridge</working-mode>', '<working-mode>route</working-mode>', xml_str)

            # 统计修复后的数量
            bridge_count_after = len(re.findall(r'<working-mode>bridge</working-mode>', xml_str))
            route_count_after = len(re.findall(r'<working-mode>route</working-mode>', xml_str))

            log(f"修复后统计: Bridge模式={bridge_count_after}, Route模式={route_count_after}", "info")
            log(f"成功修复了 {bridge_count_before} 个接口的工作模式", "info")

            return xml_str

        except Exception as e:
            log(f"修复接口工作模式时出错: {str(e)}", "error")
            return xml_str