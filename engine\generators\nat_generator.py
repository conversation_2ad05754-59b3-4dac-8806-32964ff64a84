#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
NAT配置XML生成器模块

生成符合NTOS YANG模型的NAT配置XML
支持以下功能：
1. NAT规则生成 (DNAT/SNAT)
2. NAT池配置生成
3. XML模板集成
4. YANG模型验证
"""

from typing import Dict, List, Any, Optional
from lxml import etree
from engine.utils.logger import log
from engine.utils.i18n import _
from engine.generators.xml_utils import NamespaceManager
from engine.infrastructure.error_handling import (
    twice_nat44_error_handler, twice_nat44_performance_monitor, TwiceNat44ErrorContext,
    twice_nat44_performance_optimized, twice_nat44_memory_optimized
)


class NATGenerator:
    """NAT配置XML生成器类"""
    
    def __init__(self, name_mapping_manager=None):
        """
        初始化生成器

        Args:
            name_mapping_manager: 名称映射管理器实例
        """
        self.name = "nat_generator"
        self.description = _("nat.generator_description")

        # NAT命名空间
        self.nat_namespace = "urn:ruijie:ntos:params:xml:ns:yang:nat"

        # 初始化命名空间管理器
        self.ns_manager = NamespaceManager()

        # 名称映射管理器
        self.name_mapping_manager = name_mapping_manager
        if self.name_mapping_manager is None:
            from engine.utils.name_mapping_manager import NameMappingManager
            self.name_mapping_manager = NameMappingManager()
    
    @twice_nat44_error_handler(
        operation="generate_nat_xml",
        max_retries=2,
        handle_exceptions=(etree.XMLSyntaxError, AttributeError, KeyError),
        log_errors=True,
        attempt_recovery=True
    )
    @twice_nat44_performance_monitor(
        operation="generate_nat_xml",
        log_performance=True,
        performance_threshold=2.0
    )
    @twice_nat44_performance_optimized(
        use_cache=True,
        use_object_pool=True,
        batch_size=50
    )
    @twice_nat44_memory_optimized(
        gc_threshold=100,
        memory_limit=512.0
    )
    def generate_nat_xml(self, nat_rules: List[Dict], nat_pools: List[Dict] = None, root: etree.Element = None) -> etree.Element:
        """
        生成NAT配置XML
        
        Args:
            nat_rules: NAT规则配置列表
            nat_pools: NAT池配置列表
            root: 可选的根XML元素，如果提供则在其中查找VRF节点
            
        Returns:
            etree.Element: NAT配置XML元素
        """
        nat_pools = nat_pools or []
        
        if not nat_rules and not nat_pools:
            log(_("nat.no_config_to_generate"))
            return None
        
        log(_("nat.start_generation", rules=len(nat_rules), pools=len(nat_pools)))
        
        # 如果提供了根元素，在其中查找或创建NAT节点
        if root is not None:
            return self._integrate_with_existing_xml(nat_rules, nat_pools, root)
        else:
            # 创建独立的NAT XML
            return self._create_standalone_nat_xml(nat_rules, nat_pools)
    
    def _integrate_with_existing_xml(self, nat_rules: List[Dict], nat_pools: List[Dict], root: etree.Element) -> etree.Element:
        """
        将NAT配置集成到现有XML中
        
        Args:
            nat_rules: NAT规则配置列表
            nat_pools: NAT池配置列表
            root: 根XML元素
            
        Returns:
            etree.Element: NAT容器元素
        """
        # 查找或创建VRF节点
        vrf = self.ns_manager.find_or_create_vrf(root)
        
        # 查找或创建NAT容器（不使用命名空间前缀）
        nat_container = self._find_or_create_nat_container(vrf)
        
        # 确保NAT已启用
        self._ensure_nat_enabled(nat_container)
        
        # 添加NAT池
        for pool in nat_pools:
            pool_element = self._create_nat_pool_element(pool)
            if pool_element is not None:
                nat_container.append(pool_element)
        
        # 添加NAT规则
        for rule in nat_rules:
            rule_element = self._create_nat_rule_element(rule)
            if rule_element is not None:
                nat_container.append(rule_element)

        # 添加ALG配置（如果不存在）
        self._ensure_alg_config(nat_container)

        # 添加SIP端口检查配置（如果不存在）
        self._ensure_sip_port_check_config(nat_container)

        # 移除可能的重复元素
        self._remove_duplicate_elements(nat_container)

        log(_("nat.integration_complete", rules=len(nat_rules), pools=len(nat_pools)))
        return nat_container
    
    def _create_standalone_nat_xml(self, nat_rules: List[Dict], nat_pools: List[Dict]) -> etree.Element:
        """
        创建独立的NAT XML
        
        Args:
            nat_rules: NAT规则配置列表
            nat_pools: NAT池配置列表
            
        Returns:
            etree.Element: NAT XML元素
        """
        # 创建NAT容器
        nat_container = etree.Element(
            "nat",
            nsmap={None: self.nat_namespace}
        )
        
        # 启用NAT
        self._ensure_nat_enabled(nat_container)
        
        # 添加NAT池
        for pool in nat_pools:
            pool_element = self._create_nat_pool_element(pool)
            if pool_element is not None:
                nat_container.append(pool_element)

        # 添加NAT规则
        for rule in nat_rules:
            rule_element = self._create_nat_rule_element(rule)
            if rule_element is not None:
                nat_container.append(rule_element)

        # 添加ALG配置（如果不存在）
        self._ensure_alg_config(nat_container)

        # 添加SIP端口检查配置（如果不存在）
        self._ensure_sip_port_check_config(nat_container)

        # 移除可能的重复元素
        self._remove_duplicate_elements(nat_container)

        log(_("nat.standalone_creation_complete", rules=len(nat_rules), pools=len(nat_pools)))
        return nat_container
    
    def _find_or_create_nat_container(self, vrf: etree.Element) -> etree.Element:
        """
        在VRF中查找或创建NAT容器
        
        Args:
            vrf: VRF元素
            
        Returns:
            etree.Element: NAT容器元素
        """
        # 尝试查找现有的NAT容器（支持带或不带命名空间前缀）
        nat_container = vrf.find(".//nat")
        if nat_container is None:
            # 尝试查找带命名空间的版本
            nat_container = vrf.find(f".//{{{self.nat_namespace}}}nat")

        if nat_container is None:
            # 创建新的NAT容器（不使用命名空间前缀）
            nat_container = etree.SubElement(vrf, "nat")
            nat_container.set("xmlns", self.nat_namespace)
            log(_("nat.container_created"))
        else:
            log(_("nat.container_found"))
        
        return nat_container
    
    def _ensure_nat_enabled(self, nat_container: etree.Element):
        """
        确保NAT已启用

        Args:
            nat_container: NAT容器元素
        """
        # 查找enabled元素，考虑可能的命名空间
        enabled_element = nat_container.find("enabled")
        if enabled_element is None:
            # 尝试使用命名空间查找
            enabled_element = nat_container.find("{%s}enabled" % self.nat_namespace)

        if enabled_element is None:
            enabled_element = etree.SubElement(nat_container, "enabled")
            enabled_element.text = "true"
            log(_("nat.enabled_added"))
        elif enabled_element.text != "true":
            enabled_element.text = "true"
            log(_("nat.enabled_updated"))
        else:
            log(_("nat.enabled_already_exists"))
    
    def _create_nat_pool_element(self, pool: Dict) -> Optional[etree.Element]:
        """
        创建NAT池元素（增强版本，集成YANG验证和名称清理）

        Args:
            pool: NAT池配置字典

        Returns:
            etree.Element: NAT池XML元素
        """
        try:
            # 执行YANG预验证
            validated_pool = self._validate_pool_with_yang(pool)
            if not validated_pool:
                return None

            original_pool_name = validated_pool.get("name")
            if not original_pool_name:
                log(_("nat.missing_pool_name"), "warning")
                return None

            # 使用名称映射管理器清理和注册池名称
            clean_pool_name = self.name_mapping_manager.register_name_mapping(
                'nat_pools',
                original_pool_name,
                context="NAT池"
            )

            # 创建池元素
            pool_element = etree.Element("pool")

            # 添加池名称（必需的键）
            name_element = etree.SubElement(pool_element, "name")
            name_element.text = clean_pool_name

            # 添加描述（如果存在），并清理描述中的非法字符
            if "desc" in validated_pool and validated_pool["desc"]:
                from engine.utils.name_validator import clean_ntos_description
                original_desc = validated_pool["desc"]
                clean_desc = clean_ntos_description(original_desc, 255)

                if clean_desc:  # 只有清理后的描述不为空才添加
                    desc_element = etree.SubElement(pool_element, "desc")
                    desc_element.text = clean_desc

                    # 如果描述被修改，记录日志
                    if original_desc != clean_desc:
                        log(_("nat.pool_description_cleaned",
                              pool_name=clean_pool_name,
                              original=original_desc,
                              cleaned=clean_desc), "info")

            # 添加地址范围（符合YANG模型的列表结构）
            if "address" in validated_pool:
                address_config = validated_pool["address"]

                # 如果是单个地址配置，转换为列表格式
                if isinstance(address_config, dict) and "value" in address_config:
                    # 兼容旧格式：{"value": "ip-range"}
                    address_element = etree.SubElement(pool_element, "address")
                    value_element = etree.SubElement(address_element, "value")
                    value_element.text = address_config["value"]
                elif isinstance(address_config, list):
                    # 新格式：[{"value": "ip1"}, {"value": "ip2"}]
                    for addr_item in address_config:
                        address_element = etree.SubElement(pool_element, "address")
                        value_element = etree.SubElement(address_element, "value")
                        if isinstance(addr_item, dict) and "value" in addr_item:
                            value_element.text = addr_item["value"]
                        else:
                            value_element.text = str(addr_item)
                elif isinstance(address_config, str):
                    # 简单字符串格式
                    address_element = etree.SubElement(pool_element, "address")
                    value_element = etree.SubElement(address_element, "value")
                    value_element.text = address_config

            log(_("nat.pool_element_created", name=clean_pool_name))
            return pool_element

        except Exception as e:
            pool_name = pool.get("name", "unknown") if pool else "unknown"
            log(_("nat.pool_creation_error",
                  name=pool_name,
                  error=str(e)), "error")

            # 尝试创建一个最小的有效pool元素作为fallback
            try:
                if pool and "name" in pool:
                    from engine.utils.name_validator import clean_ntos_name
                    fallback_element = etree.Element("pool")
                    name_element = etree.SubElement(fallback_element, "name")
                    name_element.text = clean_ntos_name(pool["name"], 64)

                    # 添加默认地址（如果原始配置中没有有效地址）
                    if "address" not in pool or not pool["address"]:
                        address_element = etree.SubElement(fallback_element, "address")
                        value_element = etree.SubElement(address_element, "value")
                        value_element.text = "0.0.0.0-0.0.0.0"  # 占位符地址
                        log(_("nat.pool_fallback_address_used", name=pool["name"]), "warning")

                    log(_("nat.pool_fallback_created", name=pool["name"]), "info")
                    return fallback_element
            except Exception as fallback_error:
                log(_("nat.pool_fallback_failed",
                      name=pool_name,
                      error=str(fallback_error)), "error")

            return None

    def _validate_pool_with_yang(self, pool: Dict) -> Optional[Dict]:
        """
        使用YANG模型验证单个地址池配置

        Args:
            pool: 原始池配置

        Returns:
            Optional[Dict]: 验证后的池配置，如果验证失败则返回None
        """
        try:
            from engine.validators.yang_ippool_validator import YangIPPoolValidator

            validator = YangIPPoolValidator()
            validation_result = validator.validate_pool_configuration(pool)

            pool_name = pool.get("name", "unknown")

            if validation_result.is_valid:
                # 记录警告信息
                for warning in validation_result.warnings:
                    log(_("nat_generator.pool_yang_validation_warning",
                         pool_name=pool_name,
                         field=warning.field_path,
                         message=warning.message), "warning")

                return pool
            else:
                # 记录验证错误
                log(_("nat_generator.pool_yang_validation_failed", pool_name=pool_name), "error")

                for error in validation_result.errors:
                    log(_("nat_generator.pool_yang_validation_error",
                         pool_name=pool_name,
                         field=error.field_path,
                         error_type=error.error_type,
                         message=error.message), "error")

                # 尝试修复常见问题
                fixed_pool = self._attempt_pool_auto_fix(pool, validation_result)
                if fixed_pool:
                    # 重新验证修复后的池
                    retry_result = validator.validate_pool_configuration(fixed_pool)
                    if retry_result.is_valid:
                        log(_("nat_generator.pool_auto_fixed", pool_name=pool_name), "info")
                        return fixed_pool
                    else:
                        log(_("nat_generator.pool_fix_failed", pool_name=pool_name), "warning")
                        return None
                else:
                    log(_("nat_generator.pool_skipped", pool_name=pool_name), "warning")
                    return None

        except ImportError:
            log(_("nat_generator.yang_validator_unavailable"), "debug")
            # 如果YANG验证器不可用，返回原始池
            return pool
        except Exception as e:
            log(_("nat_generator.yang_validation_failed",
                 pool_name=pool.get("name", "unknown"), error=str(e)), "warning")
            # 验证过程出错，返回原始池
            return pool

    def _attempt_pool_auto_fix(self, pool: Dict, validation_result) -> Optional[Dict]:
        """
        尝试自动修复地址池配置问题

        Args:
            pool: 原始池配置
            validation_result: 验证结果

        Returns:
            Optional[Dict]: 修复后的池配置，如果无法修复则返回None
        """
        fixed_pool = pool.copy()
        fix_applied = False

        for error in validation_result.errors:
            field_path = error.field_path
            error_type = error.error_type

            # 修复池名称问题
            if field_path == "name" and error_type in ["invalid_characters", "forbidden_characters"]:
                try:
                    from engine.utils.name_validator import clean_ntos_name
                    original_name = fixed_pool.get("name", "")
                    clean_name = clean_ntos_name(original_name, 64)
                    if clean_name != original_name:
                        fixed_pool["name"] = clean_name
                        fix_applied = True
                        log(_("nat_generator.pool_name_auto_cleaned",
                             original=original_name, cleaned=clean_name), "info")
                except ImportError:
                    pass

            # 修复描述长度问题
            elif field_path == "desc" and error_type == "length_exceeded":
                desc = fixed_pool.get("desc", "")
                if len(desc) > 255:
                    fixed_pool["desc"] = desc[:252] + "..."
                    fix_applied = True
                    log(_("nat_generator.pool_desc_truncated", pool_name=fixed_pool.get("name")), "info")

        return fixed_pool if fix_applied else None
    
    @twice_nat44_error_handler(
        operation="create_nat_rule_element",
        max_retries=1,
        handle_exceptions=(etree.XMLSyntaxError, KeyError, AttributeError),
        log_errors=True,
        attempt_recovery=True
    )
    def _create_nat_rule_element(self, rule: Dict) -> Optional[etree.Element]:
        """
        创建NAT规则元素 - 严格按照YANG模型字段顺序

        Args:
            rule: NAT规则配置字典

        Returns:
            etree.Element: NAT规则XML元素
        """
        try:
            original_rule_name = rule.get("name")
            if not original_rule_name:
                log(_("nat.missing_rule_name"), "warning")
                return None

            # 使用名称映射管理器清理和注册规则名称
            clean_rule_name = self.name_mapping_manager.register_name_mapping(
                'nat_rules',
                original_rule_name,
                context="NAT规则"
            )

            # 创建规则元素
            rule_element = etree.Element("rule")

            # 按照YANG模型字段顺序：name → rule_en → desc → NAT类型配置

            # 1. 添加规则名称（必需字段）
            name_element = etree.SubElement(rule_element, "name")
            name_element.text = clean_rule_name

            # 2. 添加规则启用状态
            rule_en = rule.get("rule_en", True)
            rule_en_element = etree.SubElement(rule_element, "rule_en")
            rule_en_element.text = "true" if rule_en else "false"

            # 3. 添加描述（按照YANG模型，desc是可选字段）
            desc = rule.get("desc")
            if desc:
                desc_element = etree.SubElement(rule_element, "desc")
                desc_element.text = str(desc)[:255]  # YANG约束：长度0-255
            
            # 根据规则类型添加相应配置
            log(_("nat.processing_rule_type_check",
                  rule_name=clean_rule_name,
                  static_dnat44=('static-dnat44' in rule),
                  static_snat44=('static-snat44' in rule),
                  dynamic_snat44=('dynamic-snat44' in rule),
                  twice_nat44=('twice-nat44' in rule)), "debug")

            if "static-dnat44" in rule:
                self._add_static_dnat44_config(rule_element, rule["static-dnat44"])
            elif "static-snat44" in rule:
                self._add_static_snat44_config(rule_element, rule["static-snat44"])
            elif "dynamic-snat44" in rule:
                self._add_dynamic_snat44_config(rule_element, rule["dynamic-snat44"])
            elif "twice-nat44" in rule:
                self._add_twice_nat44_config(rule_element, rule["twice-nat44"])
            else:
                log(_("nat.unknown_rule_type", name=clean_rule_name), "warning")
                return None

            log(_("nat.rule_element_created", name=clean_rule_name))
            return rule_element
            
        except Exception as e:
            log(_("nat.rule_creation_error", 
                  name=rule.get("name", "unknown"), 
                  error=str(e)), "error")
            return None
    
    def _add_static_dnat44_config(self, rule_element: etree.Element, dnat_config: Dict):
        """
        添加静态DNAT44配置
        
        Args:
            rule_element: 规则元素
            dnat_config: DNAT配置
        """
        dnat_element = etree.SubElement(rule_element, "static-dnat44")
        
        # 添加匹配条件
        if "match" in dnat_config:
            match_element = etree.SubElement(dnat_element, "match")
            self._add_match_conditions(match_element, dnat_config["match"])
        
        # 添加转换配置
        if "translate-to" in dnat_config:
            translate_element = etree.SubElement(dnat_element, "translate-to")
            self._add_dnat_translation(translate_element, dnat_config["translate-to"])
    
    def _add_static_snat44_config(self, rule_element: etree.Element, snat_config: Dict):
        """
        添加静态SNAT44配置
        
        Args:
            rule_element: 规则元素
            snat_config: SNAT配置
        """
        snat_element = etree.SubElement(rule_element, "static-snat44")
        
        # 添加匹配条件
        if "match" in snat_config:
            match_element = etree.SubElement(snat_element, "match")
            self._add_match_conditions(match_element, snat_config["match"])
        
        # 添加转换配置
        if "translate-to" in snat_config:
            translate_element = etree.SubElement(snat_element, "translate-to")
            self._add_snat_translation(translate_element, snat_config["translate-to"])
    
    def _add_dynamic_snat44_config(self, rule_element: etree.Element, snat_config: Dict):
        """
        添加动态SNAT44配置

        Args:
            rule_element: 规则元素
            snat_config: SNAT配置
        """
        snat_element = etree.SubElement(rule_element, "dynamic-snat44")

        # 添加匹配条件
        if "match" in snat_config:
            match_element = etree.SubElement(snat_element, "match")
            self._add_match_conditions(match_element, snat_config["match"])

        # 添加转换配置
        if "translate-to" in snat_config:
            translate_element = etree.SubElement(snat_element, "translate-to")
            self._add_dynamic_snat_translation(translate_element, snat_config["translate-to"])
    
    def _add_match_conditions(self, match_element: etree.Element, match_config: Dict):
        """
        添加匹配条件

        Args:
            match_element: 匹配元素
            match_config: 匹配配置
        """
        # 添加源接口（支持多个接口）
        if "source-interface" in match_config:
            source_interfaces = match_config["source-interface"]
            # 确保是列表格式
            if isinstance(source_interfaces, dict):
                source_interfaces = [source_interfaces]
            elif isinstance(source_interfaces, str):
                source_interfaces = [{"name": source_interfaces}]

            # 为每个源接口创建source-interface元素
            for intf in source_interfaces:
                source_if_element = etree.SubElement(match_element, "source-interface")
                source_if_name_element = etree.SubElement(source_if_element, "name")
                if isinstance(intf, dict):
                    source_if_name_element.text = intf["name"]
                else:
                    source_if_name_element.text = str(intf)
                log(_("nat.added_source_interface", interface=source_if_name_element.text), "debug")

        # 添加源网络（支持多个网络）
        if "source-network" in match_config:
            source_networks = match_config["source-network"]
            # 确保是列表格式
            if isinstance(source_networks, dict):
                source_networks = [source_networks]
            elif isinstance(source_networks, str):
                source_networks = [{"name": source_networks}]

            # 为每个源网络创建source-network元素
            for net in source_networks:
                source_net_element = etree.SubElement(match_element, "source-network")
                source_name_element = etree.SubElement(source_net_element, "name")
                if isinstance(net, dict):
                    source_name_element.text = net["name"]
                else:
                    source_name_element.text = str(net)
                log(_("nat.added_source_network", network=source_name_element.text), "debug")

        # 添加目标网络（支持多个网络）
        if "dest-network" in match_config:
            dest_networks = match_config["dest-network"]
            # 确保是列表格式
            if isinstance(dest_networks, dict):
                dest_networks = [dest_networks]
            elif isinstance(dest_networks, str):
                dest_networks = [{"name": dest_networks}]

            # 为每个目标网络创建dest-network元素
            for net in dest_networks:
                dest_net_element = etree.SubElement(match_element, "dest-network")
                dest_name_element = etree.SubElement(dest_net_element, "name")
                if isinstance(net, dict):
                    dest_name_element.text = net["name"]
                else:
                    dest_name_element.text = str(net)
                log(_("nat.added_dest_network", network=dest_name_element.text), "debug")

        # 添加目标接口
        if "dest-if" in match_config:
            dest_if_element = etree.SubElement(match_element, "dest-if")
            dest_if_element.text = match_config["dest-if"]
            log(_("nat.added_dest_interface", interface=match_config["dest-if"]), "debug")

        # 添加服务（支持多个服务）
        if "service" in match_config:
            services = match_config["service"]
            # 如果是单个服务，转换为列表
            if isinstance(services, dict):
                services = [services]
            elif isinstance(services, str):
                services = [{"name": services}]

            # 为每个服务创建service元素
            for service in services:
                service_element = etree.SubElement(match_element, "service")
                service_name_element = etree.SubElement(service_element, "name")
                if isinstance(service, dict):
                    service_name_element.text = service["name"]
                else:
                    service_name_element.text = str(service)
        
        # 添加时间范围
        if "time-range" in match_config:
            time_range_value = match_config["time-range"]["value"]

            # 移除可能存在的引号
            if time_range_value.startswith('"') and time_range_value.endswith('"'):
                time_range_value = time_range_value[1:-1]

            # 处理空值情况，使用默认的"always"
            if not time_range_value or time_range_value.strip() == "":
                time_range_value = "always"

            # 只有当时间范围值有效时才添加时间范围元素
            if time_range_value and time_range_value != "none":
                time_range_element = etree.SubElement(match_element, "time-range")
                time_value_element = etree.SubElement(time_range_element, "value")
                time_value_element.text = time_range_value
                log(_("nat.added_time_range", value=time_range_value))
    
    def _add_dnat_translation(self, translate_element: etree.Element, translate_config: Dict):
        """
        添加DNAT转换配置
        
        Args:
            translate_element: 转换元素
            translate_config: 转换配置
        """
        # 添加IPv4地址
        if "ipv4-address" in translate_config:
            ipv4_element = etree.SubElement(translate_element, "ipv4-address")
            ipv4_element.text = translate_config["ipv4-address"]
        
        # 添加端口
        if "port" in translate_config:
            port_element = etree.SubElement(translate_element, "port")
            port_element.text = str(translate_config["port"])
    
    def _add_snat_translation(self, translate_element: etree.Element, translate_config: Dict):
        """
        添加静态SNAT转换配置 - 严格按照YANG模型规范

        Args:
            translate_element: 转换元素
            translate_config: 转换配置
        """
        # 按照YANG模型，static-snat44的translate-to包含：
        # choice address-type (ip | interface) + no-pat + try-no-pat

        # 添加地址类型配置
        if "ipv4-address" in translate_config:
            # 使用具体IP地址
            ipv4_element = etree.SubElement(translate_element, "ipv4-address")
            ipv4_element.text = translate_config["ipv4-address"]
        elif "output-address" in translate_config:
            # 使用出接口地址
            output_addr_element = etree.SubElement(translate_element, "output-address")

        # 按照YANG模型，no-pat和try-no-pat都是必需字段，有默认值
        # YANG规范：no-pat默认false，try-no-pat默认true

        # 添加no-pat设置（YANG默认值：false）
        no_pat_value = translate_config.get("no-pat", False)
        no_pat_element = etree.SubElement(translate_element, "no-pat")
        no_pat_element.text = "true" if no_pat_value else "false"

        # 添加try-no-pat设置（YANG默认值：true）
        try_no_pat_value = translate_config.get("try-no-pat", True)
        try_no_pat_element = etree.SubElement(translate_element, "try-no-pat")
        try_no_pat_element.text = "true" if try_no_pat_value else "false"
    
    def _add_dynamic_snat_translation(self, translate_element: etree.Element, translate_config: Dict):
        """
        添加动态SNAT转换配置

        Args:
            translate_element: 转换元素
            translate_config: 转换配置
        """

        # 添加池名称
        if "pool-name" in translate_config:
            pool_name_element = etree.SubElement(translate_element, "pool-name")
            pool_name_element.text = translate_config["pool-name"]
        else:
            log(f"WARNING: 转换配置中缺少pool-name: {translate_config}", "warning")

    def _ensure_alg_config(self, nat_container: etree.Element):
        """
        确保NAT容器包含ALG配置

        Args:
            nat_container: NAT容器元素
        """
        # 查找alg元素，考虑可能的命名空间
        alg_element = nat_container.find("alg")
        if alg_element is None:
            # 尝试使用命名空间查找
            alg_element = nat_container.find("{%s}alg" % self.nat_namespace)

        if alg_element is None:
            # 添加默认ALG配置
            alg_element = etree.SubElement(nat_container, "alg")
            alg_element.text = "ftp sip-tcp sip-udp tftp dns-udp"
            log(_("nat.alg_config_added"))
        else:
            log(_("nat.alg_config_already_exists"))

    def _ensure_sip_port_check_config(self, nat_container: etree.Element):
        """
        确保NAT容器包含SIP端口检查配置

        Args:
            nat_container: NAT容器元素
        """
        # 查找sip-port-check元素，考虑可能的命名空间
        sip_port_check_element = nat_container.find("sip-port-check")
        if sip_port_check_element is None:
            # 尝试使用命名空间查找
            sip_port_check_element = nat_container.find("{%s}sip-port-check" % self.nat_namespace)

        if sip_port_check_element is None:
            # 添加默认SIP端口检查配置
            sip_port_check_element = etree.SubElement(nat_container, "sip-port-check")
            enabled_element = etree.SubElement(sip_port_check_element, "enabled")
            enabled_element.text = "true"
            log(_("nat.sip_port_check_config_added"))
        else:
            log(_("nat.sip_port_check_config_already_exists"))

    def convert_nat_rule_to_xml_dict(self, nat_rule: Dict) -> Dict:
        """
        将NAT规则转换为XML字典格式

        Args:
            nat_rule: NAT规则配置字典

        Returns:
            Dict: XML字典格式的NAT规则
        """
        try:
            # 创建NAT规则元素
            rule_element = self._create_nat_rule_element(nat_rule)
            if rule_element is None:
                return None

            # 将XML元素转换为字典格式
            rule_dict = self._xml_element_to_dict(rule_element)

            log(_("nat.rule_converted_to_dict", name=nat_rule.get("name", "unknown")))
            return rule_dict

        except Exception as e:
            log(_("nat.rule_conversion_to_dict_failed",
                  name=nat_rule.get("name", "unknown"), error=str(e)), "error")
            return None

    def _xml_element_to_dict(self, element: etree.Element) -> Dict:
        """
        将XML元素转换为字典格式

        Args:
            element: XML元素

        Returns:
            Dict: 字典格式的数据
        """
        result = {}

        # 添加元素标签
        result['tag'] = element.tag

        # 添加属性
        if element.attrib:
            result['attributes'] = dict(element.attrib)

        # 添加文本内容
        if element.text and element.text.strip():
            result['text'] = element.text.strip()

        # 添加子元素
        children = []
        for child in element:
            child_dict = self._xml_element_to_dict(child)
            children.append(child_dict)

        if children:
            result['children'] = children

        return result

    def _remove_duplicate_elements(self, nat_container: etree.Element):
        """
        移除NAT容器中的重复元素

        Args:
            nat_container: NAT容器元素
        """
        # 需要检查的单一元素（不应该重复）
        single_elements = ["enabled", "alg", "sip-port-check"]

        for element_name in single_elements:
            # 查找所有同名元素（包括命名空间）
            elements = nat_container.findall(element_name)
            elements.extend(nat_container.findall("{%s}%s" % (self.nat_namespace, element_name)))

            if len(elements) > 1:
                log(_("nat.duplicate_elements_found", element=element_name, count=len(elements)), "warning")
                # 保留第一个，移除其余的
                for element in elements[1:]:
                    nat_container.remove(element)

    def _add_twice_nat44_config(self, rule_element: etree.Element, twice_nat_config: Dict):
        """
        添加twice-nat44配置到XML元素

        Args:
            rule_element: 规则XML元素
            twice_nat_config: twice-nat44配置字典

        Raises:
            ValueError: 当配置格式无效时
            etree.XMLSyntaxError: 当XML生成失败时
        """
        try:
            twice_nat_element = etree.SubElement(rule_element, "twice-nat44")

            # 添加匹配条件
            if "match" in twice_nat_config:
                match_element = etree.SubElement(twice_nat_element, "match")
                self._add_match_conditions(match_element, twice_nat_config["match"])

            # 添加SNAT配置
            if "snat" in twice_nat_config:
                snat_element = etree.SubElement(twice_nat_element, "snat")
                self._add_twice_nat_snat_config(snat_element, twice_nat_config["snat"])

            # 添加DNAT配置
            if "dnat" in twice_nat_config:
                dnat_element = etree.SubElement(twice_nat_element, "dnat")
                self._add_twice_nat_dnat_config(dnat_element, twice_nat_config["dnat"])

            log(_("nat.twice_nat44_config_added"), "debug")

        except Exception as e:
            log(_("nat.twice_nat44_config_error", error=str(e)), "error")
            raise

    def _add_twice_nat_snat_config(self, snat_element: etree.Element, snat_config: Dict):
        """
        添加twice-nat44 SNAT配置

        Args:
            snat_element: SNAT XML元素
            snat_config: SNAT配置字典

        Note:
            支持的地址类型：
            - interface: 使用出接口地址
            - ip: 使用指定IP地址
            - pool: 使用IP地址池
        """
        try:
            # 处理地址类型
            if "output-address" in snat_config:
                etree.SubElement(snat_element, "output-address")
            elif "ipv4-address" in snat_config:
                ip_elem = etree.SubElement(snat_element, "ipv4-address")
                ip_elem.text = snat_config["ipv4-address"]
            elif "pool-name" in snat_config:
                pool_elem = etree.SubElement(snat_element, "pool-name")
                pool_elem.text = snat_config["pool-name"]

            # 处理PAT配置
            if "no-pat" in snat_config:
                no_pat_elem = etree.SubElement(snat_element, "no-pat")
                no_pat_elem.text = "true" if snat_config["no-pat"] else "false"

            if "try-no-pat" in snat_config:
                try_no_pat_elem = etree.SubElement(snat_element, "try-no-pat")
                try_no_pat_elem.text = "true" if snat_config["try-no-pat"] else "false"

            log(_("nat.twice_nat44_snat_config_added"), "debug")

        except Exception as e:
            log(_("nat.twice_nat44_snat_config_error", error=str(e)), "error")
            raise

    def _add_twice_nat_dnat_config(self, dnat_element: etree.Element, dnat_config: Dict):
        """
        添加twice-nat44 DNAT配置

        Args:
            dnat_element: DNAT XML元素
            dnat_config: DNAT配置字典
        """
        try:
            if "ipv4-address" in dnat_config:
                ip_elem = etree.SubElement(dnat_element, "ipv4-address")
                ip_elem.text = dnat_config["ipv4-address"]

            if "port" in dnat_config:
                port_elem = etree.SubElement(dnat_element, "port")
                port_elem.text = str(dnat_config["port"])

            log(_("nat.twice_nat44_dnat_config_added"), "debug")

        except Exception as e:
            log(_("nat.twice_nat44_dnat_config_error", error=str(e)), "error")
            raise

    def _validate_twice_nat44_xml(self, rule_element: etree.Element) -> bool:
        """
        验证twice-nat44 XML结构的正确性

        Args:
            rule_element: 规则XML元素

        Returns:
            bool: XML结构是否正确
        """
        try:
            twice_nat_elem = rule_element.find("twice-nat44")
            if twice_nat_elem is None:
                return False

            # 检查必需的子元素
            match_elem = twice_nat_elem.find("match")
            snat_elem = twice_nat_elem.find("snat")
            dnat_elem = twice_nat_elem.find("dnat")

            if match_elem is None or snat_elem is None or dnat_elem is None:
                log(_("nat.twice_nat44_missing_required_elements"), "warning")
                return False

            # 检查DNAT配置
            dnat_ip = dnat_elem.find("ipv4-address")
            if dnat_ip is None or not dnat_ip.text:
                log(_("nat.twice_nat44_missing_dnat_ip"), "warning")
                return False

            return True

        except Exception as e:
            log(_("nat.twice_nat44_validation_error", error=str(e)), "error")
            return False
