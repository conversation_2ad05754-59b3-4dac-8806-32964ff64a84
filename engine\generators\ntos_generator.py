#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import json
import xml.dom.minidom as minidom
from xml.dom.minidom import parseString
from datetime import datetime
from engine.utils.logger import log, user_log
from lxml import etree
from engine.generators.yang_generator import generate_xml
from engine.generators.interface_handler import (
    reset_tunnel_ids, create_new_interface, create_new_vlan_interface, 
    update_existing_interface, update_vlan_interface
)
from engine.utils.i18n import _
import re

class NTOSGenerator:
    """NTOS XML配置生成器"""
    
    def __init__(self, model, version, transparent_mode_result=None):
        """
        初始化NTOS配置生成器

        Args:
            model (str): 设备型号，如 'z5100s'
            version (str): 设备版本，如 'R10P2'
            transparent_mode_result (dict): 透明模式处理结果，用于桥接接口生成
        """
        self.model = model
        self.version = version
        self.transparent_mode_result = transparent_mode_result
        self.interfaces = {}
        self.interface_mapping = {}  # 添加接口映射支持
        self.address_objects = []
        self.service_objects = []
        self.policy_rules = []
        self.zones = []  # 添加区域列表
        self.static_routes = []  # 静态路由列表
        self.static_routes_ipv6 = []  # IPv6静态路由列表
        self.address_groups = []  # 地址组列表
        self.service_groups = []  # 服务组列表
        self.time_ranges = []  # 时间对象列表
        self.nat_rules = []  # NAT规则列表

        # 加载模板文件
        self.template_path = self._get_template_path()
        log(_("info.init_ntos_generator", model=model, version=version))
    
    def _get_template_path(self):
        """获取模板文件路径"""
        # 使用相对于当前文件的路径
        current_file_dir = os.path.dirname(os.path.abspath(__file__))
        engine_dir = os.path.dirname(current_file_dir)  # 回到 engine 目录
        
        # 确保我们在正确的engine目录中
        if not os.path.basename(engine_dir) == 'engine':
            # 查找包含 engine 目录的路径
            parts = current_file_dir.split(os.sep)
            if 'engine' in parts:
                engine_index = parts.index('engine')
                engine_dir = os.sep.join(parts[:engine_index+1])
        
        template_path = os.path.join(engine_dir, "data", "input", "configData", self.model, self.version, "Config", "running.xml")
        
        # 检查模板是否存在
        if not os.path.exists(template_path):
            error_msg = _("error.template_not_exists", path=template_path)
            log(error_msg, "error")
            user_log(_("error.no_template_for_model_version"), "error")
            raise FileNotFoundError(error_msg)
        
        log(_("info.using_template_file", path=template_path))
        return template_path
    
    def add_interfaces(self, interfaces, interface_mapping=None):
        """
        添加接口配置
        
        Args:
            interfaces (dict): 接口配置字典
            interface_mapping (dict, optional): 接口映射表，用于将FortiGate接口名称映射为NTOS名称
        """
        self.interfaces = interfaces
        if interface_mapping:
            self.interface_mapping = interface_mapping
        log(_("info.added_interfaces", count=len(interfaces)))
    
    def add_address_objects(self, address_objects):
        """
        添加地址对象
        
        Args:
            address_objects (list): 地址对象列表
        """
        self.address_objects = address_objects
        log(_("info.added_address_objects", count=len(address_objects)))
    
    def add_service_objects(self, service_objects):
        """
        添加服务对象
        
        Args:
            service_objects (list): 服务对象列表
        """
        # 记录服务对象的数量和内容，用于调试
        log(_("debug.adding_service_objects_to_ntos_generator"), "debug", count=len(service_objects))
        for i, svc in enumerate(service_objects):
            service_name = svc.get("name", "unknown")
            service_type = svc.get("type", "unknown")
            log(_("debug.service_object_details"), "debug",
                index=i+1, name=service_name, type=service_type)
        
        # 检查服务对象格式是否正确
        valid_service_objects = []
        for svc in service_objects:
            if not isinstance(svc, dict):
                log(_("warning.skip_non_dict_service_object"), "warning", object_type=type(svc).__name__)
                continue

            if "name" not in svc or not svc["name"]:
                log(_("warning.skip_service_object_missing_name"), "warning")
                continue
            
            # 检查服务对象是否包含必要的字段
            has_tcp = "tcp-portrange" in svc and svc["tcp-portrange"]
            has_udp = "udp-portrange" in svc and svc["udp-portrange"]
            has_protocol = "protocol" in svc and svc["protocol"]
            
            # 如果没有协议字段，根据端口范围自动推断
            if not has_protocol:
                if has_tcp and has_udp:
                    svc["protocol"] = "tcp_udp"
                    log(_("debug.service_auto_infer_protocol"), "debug", name=svc['name'], protocol="tcp_udp")
                elif has_tcp:
                    svc["protocol"] = "tcp"
                    log(_("debug.service_auto_infer_protocol"), "debug", name=svc['name'], protocol="tcp")
                elif has_udp:
                    svc["protocol"] = "udp"
                    log(_("debug.service_auto_infer_protocol"), "debug", name=svc['name'], protocol="udp")
                else:
                    log(_("warning.service_missing_protocol_and_ports"), "warning", name=svc['name'])
                    svc["protocol"] = "tcp"  # 设置默认协议类型
            # 如果有协议字段但需要根据端口范围调整
            elif has_protocol:
                # 飞塔特殊规则：如果配置了tcp-portrange，协议类型应为TCP或TCP_UDP
                if has_tcp and svc["protocol"].lower() not in ["tcp", "tcp_udp"]:
                    log(_("debug.service_protocol_correction"), "debug",
                        name=svc['name'], old_protocol=svc['protocol'], new_protocol="tcp", reason="has_tcp_ports")
                    svc["protocol"] = "tcp"

                # 飞塔特殊规则：如果配置了udp-portrange，协议类型应为UDP或TCP_UDP
                if has_udp and svc["protocol"].lower() not in ["udp", "tcp_udp"]:
                    log(_("debug.service_protocol_correction"), "debug",
                        name=svc['name'], old_protocol=svc['protocol'], new_protocol="udp", reason="has_udp_ports")
                    svc["protocol"] = "udp"

                # 飞塔特殊规则：如果同时配置了tcp-portrange和udp-portrange，协议类型应为TCP_UDP
                if has_tcp and has_udp and svc["protocol"].lower() != "tcp_udp":
                    log(_("debug.service_protocol_correction"), "debug",
                        name=svc['name'], old_protocol=svc['protocol'], new_protocol="tcp_udp", reason="has_both_ports")
                    svc["protocol"] = "tcp_udp"
            
            # 添加到有效服务对象列表
            valid_service_objects.append(svc)
            log(_("debug.added_valid_service_object"), "debug", name=svc['name'])

        self.service_objects = valid_service_objects
        log(_("info.added_service_objects", count=len(valid_service_objects)))
        log(_("debug.valid_service_objects_count"), "debug",
            valid=len(valid_service_objects), total=len(service_objects))
    
    def add_policy_rules(self, policy_rules):
        """
        添加策略规则
        
        Args:
            policy_rules (list): 策略规则列表
        """
        self.policy_rules = policy_rules
        log(_("info.added_policy_rules", count=len(policy_rules)))
    
    def add_zones(self, zones):
        """
        添加区域配置
        
        Args:
            zones (list): 区域配置列表
        """
        self.zones = zones
        log(_("info.added_zones", count=len(zones)))
    
    def add_static_routes(self, static_routes, static_routes_ipv6=None):
        """
        添加静态路由
        
        Args:
            static_routes (list): 静态路由列表
            static_routes_ipv6 (list, optional): IPv6静态路由列表
        """
        from engine.utils.logger import log
        from engine.utils.i18n import _
        
        self.static_routes = static_routes
        
        # 过滤掉没有必要配置的IPv6路由
        if static_routes_ipv6:
            valid_ipv6_routes = []
            for route in static_routes_ipv6:
                # 检查路由是否有必要的配置（网关或设备）
                has_gateway = "gateway" in route and route["gateway"]
                has_device = "device" in route or "mapped_device" in route
                has_blackhole = "blackhole" in route and route["blackhole"] in ["enable", True]
                
                if has_gateway or has_device or has_blackhole:
                    valid_ipv6_routes.append(route)
                else:
                    log(_("warning.skipping_empty_ipv6_route", id=route.get('id', 'unknown')), "warning")
            
            self.static_routes_ipv6 = valid_ipv6_routes
        else:
            self.static_routes_ipv6 = []
        
        log(_("info.added_static_routes", count=len(static_routes)))
        if static_routes_ipv6:
            log(_("info.added_ipv6_static_routes", count=len(self.static_routes_ipv6)))
    
    def add_address_groups(self, address_groups):
        """
        添加地址组对象
        
        Args:
            address_groups (list): 地址组对象列表
        """
        self.address_groups = address_groups
        log(_("info.added_address_groups", count=len(address_groups)))
    
    def add_service_groups(self, service_groups):
        """
        添加服务组对象
        
        Args:
            service_groups (list): 服务组对象列表
        """
        self.service_groups = service_groups
        log(_("info.added_service_groups", count=len(service_groups)))
    
    def add_time_ranges(self, time_ranges):
        """
        添加时间对象

        Args:
            time_ranges (list): 时间对象列表
        """
        self.time_ranges = time_ranges
        log(_("info.added_time_ranges", count=len(time_ranges)))

    def set_nat_rules(self, nat_rules):
        """
        设置NAT规则

        Args:
            nat_rules (list): NAT规则列表
        """
        self.nat_rules = nat_rules or []
        log(_("info.set_nat_rules", count=len(self.nat_rules)))

    def set_service_mapping_relationships(self, service_mapping_relationships):
        """
        设置服务映射关系

        Args:
            service_mapping_relationships (dict): 服务映射关系字典
        """
        self.service_mapping_relationships = service_mapping_relationships or {}
        log(_("debug.ntos_generator_set_service_mappings"), "debug", count=len(self.service_mapping_relationships))
    
    def generate(self):
        """
        生成NTOS格式的XML配置，集成YANG模型验证
        
        Returns:
            tuple: (XML配置内容, 转换统计信息, 验证报告)
        """
        log(_("info.starting_ntos_xml_generation"))
        user_log(_("info.starting_xml_generation"))
        
        # 暂时关闭增强YANG生成器，直接使用传统方式
        log(_("info.using_legacy_generator"), "warning")
        return self._fallback_to_legacy_generation()
        
        # 以下代码被暂时注释掉，增强YANG生成器功能暂时不使用
        """
        # 导入增强的YANG生成器
        from engine.generators.enhanced_yang_generator import EnhancedYangGenerator
        
        # 创建增强的YANG生成器实例
        try:
            enhanced_generator = EnhancedYangGenerator(self.model, self.version)
            log(_("info.yang_generator_initialized"))
        except Exception as e:
            log(_("warning.yang_generator_init_failed", error=str(e)), "warning")
            # 如果YANG生成器初始化失败，回退到传统方式
            return self._fallback_to_legacy_generation()
        
        # 重置隧道ID分配
        reset_tunnel_ids()
        
        # 准备配置数据
        config_data = self._prepare_config_data()
        
        # 使用增强的YANG生成器生成XML
        try:
            xml_content, validation_report, yanglint_status = enhanced_generator.generate_xml_with_validation(
                config_data, self.template_path)
            
            # 转换统计信息
            stats = self._calculate_stats(config_data, validation_report)
            
            log(_("info.xml_generation_complete_with_validation", 
                 errors=validation_report.get("error_count", 0),
                 warnings=validation_report.get("warning_count", 0)))
            
            return xml_content, stats, validation_report
            
        except Exception as e:
            log(_("error.yang_generation_failed", error=str(e)), "error")
            # 如果YANG生成失败，回退到传统方式
            log(_("info.fallback_to_legacy_generation"), "warning")
            return self._fallback_to_legacy_generation()
        """
    
    def _prepare_config_data(self):
        """准备配置数据供YANG生成器使用"""
        return {
            "interfaces": self.interfaces,
            "interface_mapping": self.interface_mapping,
            "address_objects": self.address_objects,
            "service_objects": self.service_objects,
            "policy_rules": self.policy_rules,
            "zones": self.zones,
            "static_routes": getattr(self, "static_routes", []),
            "address_groups": getattr(self, "address_groups", []),
            "service_groups": getattr(self, "service_groups", []),
            "time_ranges": getattr(self, "time_ranges", [])  # 添加时间对象
        }
    
    def _calculate_stats(self, config_data, validation_report):
        """计算转换统计信息"""
        stats = {
            "interfaces": {
                "total": len(config_data.get("interfaces", {})),
                "processed": len([i for i in config_data.get("interfaces", {}).values() if i]),
                "skipped": 0,
                "invalid_interfaces": [],
                "duplicates_removed": 0
            },
            "address_objects": {
                "total": len(config_data.get("address_objects", [])),
                "processed": len(config_data.get("address_objects", []))
            },
            "service_objects": {
                "total": len(config_data.get("service_objects", [])),
                "processed": len(config_data.get("service_objects", []))
            },
            "policy_rules": {
                "total": len(config_data.get("policy_rules", [])),
                "processed": len(config_data.get("policy_rules", []))
            },
            "zones": {
                "total": len(config_data.get("zones", [])),
                "processed": len(config_data.get("zones", [])),
                "skipped_interfaces": [],
                "skipped_interfaces_count": 0
            },
            "static_routes": {
                "total": len(config_data.get("static_routes", [])),
                "processed": len(config_data.get("static_routes", []))
            },
            "validation": {
                "errors": validation_report.get("error_count", 0),
                "warnings": validation_report.get("warning_count", 0),
                "is_valid": validation_report.get("is_valid", True)
            }
        }
        return stats
    
    def _fallback_to_legacy_generation(self):
        """
        回退到传统生成方式，使用改进版生成器
        
        Returns:
            tuple: (XML内容, 统计信息, 验证报告)
        """
        # 导入ImprovedLegacyGenerator
        from engine.generators.improved_legacy_generator import ImprovedLegacyGenerator
        
        # 创建ImprovedLegacyGenerator实例，传递透明模式结果
        legacy_generator = ImprovedLegacyGenerator(self.model, self.version, self.template_path,
                                                 transparent_mode_result=self.transparent_mode_result)
        
        # 记录服务对象的数量和内容，用于调试
        log(_("debug.setting_service_objects_to_legacy_generator"), "debug", count=len(self.service_objects))
        for i, svc in enumerate(self.service_objects):
            service_name = svc.get("name", "unknown")
            service_type = svc.get("type", "unknown")
            log(_("debug.legacy_generator_service_object_details"), "debug",
                index=i+1, name=service_name, type=service_type)
        
        # 设置配置数据
        legacy_generator.set_interfaces(self.interfaces, self.interface_mapping)
        legacy_generator.set_address_objects(self.address_objects)
        legacy_generator.set_service_objects(self.service_objects)
        legacy_generator.set_policy_rules(self.policy_rules)
        legacy_generator.set_zones(self.zones)
        legacy_generator.set_static_routes(self.static_routes, self.static_routes_ipv6)
        
        if hasattr(self, 'address_groups'):
            legacy_generator.set_address_groups(self.address_groups)
        
        if hasattr(self, 'service_groups'):
            legacy_generator.set_service_groups(self.service_groups)

        # 传递服务映射关系
        if hasattr(self, 'service_mapping_relationships'):
            legacy_generator.set_service_mapping_relationships(self.service_mapping_relationships)

        # 添加时间对象传递
        if hasattr(self, 'time_ranges'):
            log(_("info.setting_time_ranges_to_legacy_generator", count=len(self.time_ranges)), "debug")
            legacy_generator.set_time_ranges(self.time_ranges)

        # 添加NAT规则传递
        if hasattr(self, 'nat_rules') and self.nat_rules:
            log(_("info.setting_nat_rules_to_legacy_generator", count=len(self.nat_rules)), "debug")
            legacy_generator.set_nat_rules(self.nat_rules)

        # 定义type_key变量，确保在生成XML之前初始化
        # 这个变量在处理服务组时可能会被使用
        type_key = _("issue.type")
        
        # 检查LegacyGenerator中的服务对象
        log(_("debug.legacy_generator_service_objects_count"), "debug", count=len(legacy_generator.service_objects))
        for i, svc in enumerate(legacy_generator.service_objects):
            service_name = svc.get("name", "unknown")
            service_type = svc.get("type", "unknown")
            log(_("debug.legacy_generator_service_object_check"), "debug",
                index=i+1, name=service_name, type=service_type)
        
        # 生成XML
        xml_content, stats = legacy_generator.generate()
        
        # 添加验证报告占位符
        validation_report = {
            "is_valid": True,
            "error_count": 0,
            "warning_count": 0,
            "mode": "improved_legacy"  # 更改模式标识
        }
        
        return xml_content, stats, validation_report
    
    def _collect_existing_interfaces(self, interface_elem, existing_interfaces, existing_vlans):
        """
        收集现有的物理接口和VLAN接口
        
        Args:
            interface_elem: 接口元素
            existing_interfaces: 用于存储物理接口的字典
            existing_vlans: 用于存储VLAN接口的字典
        """
        log(_("info.collecting_existing_interfaces"))
        
        # 定义命名空间
        nsmap = {
            'if': "urn:ruijie:ntos:params:xml:ns:yang:interface",
            'vlan': "urn:ruijie:ntos:params:xml:ns:yang:vlan"
        }
        
        # 使用不同的查找方式来确保捕获所有接口类型
        
        # 1. 物理接口 - 使用不同的命名空间查询方式
        physical_interfaces = []
        
        # 1.1 不带命名空间的查询
        physical_interfaces.extend(interface_elem.findall("physical"))
        
        # 1.2 带if命名空间的查询
        physical_interfaces.extend(interface_elem.findall("if:physical", namespaces=nsmap))
        
        # 1.3 使用完整URI的查询
        physical_interfaces.extend(interface_elem.findall("{%s}physical" % nsmap['if']))
        
        # 处理物理接口
        for phy in physical_interfaces:
            # 尝试不同的方式查找name元素
            name_elems = []
            name_elems.extend(phy.findall("name"))
            name_elems.extend(phy.findall("if:name", namespaces=nsmap))
            name_elems.extend(phy.findall("{%s}name" % nsmap['if']))
            
            for name_elem in name_elems:
                if name_elem is not None and name_elem.text:
                    log(_("info.found_existing_physical_interface", name=name_elem.text))
                    existing_interfaces[name_elem.text] = phy
                    break
                
            # 检查PPPoE配置并记录已使用的隧道ID
            self._check_pppoe_tunnel(phy)
        
        # 2. VLAN子接口 - 使用不同的命名空间查询方式
        vlan_interfaces = []
        
        # 2.1 不带命名空间的查询
        vlan_interfaces.extend(interface_elem.findall("vlan"))
        
        # 2.2 带vlan命名空间的查询
        vlan_interfaces.extend(interface_elem.findall("vlan:vlan", namespaces=nsmap))
        
        # 2.3 使用完整URI的查询
        vlan_interfaces.extend(interface_elem.findall("{%s}vlan" % nsmap['vlan']))
        
        # 处理VLAN接口
        for vlan in vlan_interfaces:
            # 尝试不同的方式查找name元素
            name_elems = []
            name_elems.extend(vlan.findall("name"))
            name_elems.extend(vlan.findall("if:name", namespaces=nsmap))
            name_elems.extend(vlan.findall("{%s}name" % nsmap['if']))
            
            for name_elem in name_elems:
                if name_elem is not None and name_elem.text:
                    log(_("info.found_existing_vlan_interface", name=name_elem.text))
                    existing_vlans[name_elem.text] = vlan
                    break
                
            # 检查PPPoE配置并记录已使用的隧道ID
            self._check_pppoe_tunnel(vlan)
        
        log(_("info.collected_interfaces", 
              physical_count=len(existing_interfaces), 
              vlan_count=len(existing_vlans)))