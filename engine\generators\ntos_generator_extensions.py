from lxml import etree
from engine.utils.logger import log
from engine.utils.i18n import _  # 导入国际化函数
import os
import copy
import re

# 导入基础模块
from engine.generators.ntos_generator import NTOSGenerator

class ExtendedNTOSGenerator(NTOSGenerator):
    """扩展的NTOS配置生成器，提供更多功能"""
    
    def __init__(self, model, version, custom_template_path=None, transparent_mode_result=None):
        """
        初始化扩展的NTOS配置生成器

        Args:
            model (str): 设备型号，如 'z5100s'
            version (str): 设备版本，如 'R10P2'
            custom_template_path (str, optional): 自定义模板路径
            transparent_mode_result (dict, optional): 透明模式处理结果，用于桥接接口生成
        """
        # 先设置custom_template_path，因为父类初始化会调用_get_template_path
        self.custom_template_path = custom_template_path
        super().__init__(model, version, transparent_mode_result)
        self.nat_rules = []  # NAT规则列表
        self.vpn_tunnels = []  # VPN隧道列表
        self.dhcp_servers = []  # DHCP服务器列表
        self.dns_config = {}  # DNS配置字典
        log(_("ntos_extensions.init_extended_generator", model=model, version=version))
    
    def _get_template_path(self):
        """获取模板文件路径，优先使用自定义模板"""
        if self.custom_template_path and os.path.exists(self.custom_template_path):
            log(_("ntos_extensions.using_custom_template", path=self.custom_template_path))
            return self.custom_template_path
        
        # 使用父类方法获取默认模板
        return super()._get_template_path()
    
    def add_nat_rules(self, nat_rules):
        """
        添加NAT规则配置
        
        Args:
            nat_rules (list): NAT规则列表
        """
        self.nat_rules = nat_rules
        log(_("ntos_extensions.added_nat_rules", count=len(nat_rules)))
    
    def add_vpn_tunnels(self, vpn_tunnels):
        """
        添加VPN隧道配置
        
        Args:
            vpn_tunnels (list): VPN隧道列表
        """
        self.vpn_tunnels = vpn_tunnels
        log(_("ntos_extensions.added_vpn_tunnels", count=len(vpn_tunnels)))
    
    def add_dhcp_servers(self, dhcp_servers):
        """
        添加DHCP服务器配置

        Args:
            dhcp_servers (list): DHCP服务器列表
        """
        self.dhcp_servers = dhcp_servers
        log(_("ntos_extensions.added_dhcp_servers", count=len(dhcp_servers)))

    def add_dns_config(self, dns_config):
        """
        添加DNS配置

        Args:
            dns_config (dict): DNS配置字典，包含primary、secondary、dns_over_tls等字段
        """
        self.dns_config = dns_config
        # 统计DNS服务器数量
        dns_server_count = 0
        if dns_config.get("primary"):
            dns_server_count += 1
        if dns_config.get("secondary"):
            dns_server_count += 1
        if dns_config.get("tertiary"):
            dns_server_count += 1

        log(_("info.added_dns_config", servers=dns_server_count,
              dns_over_tls=dns_config.get("dns_over_tls", "未设置")))
    
    def generate(self):
        """
        生成NTOS格式的XML配置，包括扩展功能

        Returns:
            tuple: (XML配置内容, 转换统计信息)
        """
        # 调用父类的generate方法生成基本配置
        # 父类返回3个值：(xml_content, stats, validation_report)
        result = super().generate()
        if len(result) == 3:
            xml_content, stats, validation_report = result
        else:
            xml_content, stats = result
        
        # 解析生成的XML
        try:
            root = etree.fromstring(xml_content)
            
            # 添加扩展配置
            if self.nat_rules:
                self._add_nat_rules_to_xml(root)
                stats["nat_rules"] = {
                    "total": len(self.nat_rules),
                    "processed": len(self.nat_rules)
                }
            
            if self.vpn_tunnels:
                self._add_vpn_tunnels_to_xml(root)
                stats["vpn_tunnels"] = {
                    "total": len(self.vpn_tunnels),
                    "processed": len(self.vpn_tunnels)
                }
            
            if self.dhcp_servers:
                self._add_dhcp_servers_to_xml(root)
                stats["dhcp_servers"] = {
                    "total": len(self.dhcp_servers),
                    "processed": len(self.dhcp_servers)
                }

            if self.dns_config:
                self._add_dns_config_to_xml(root)
                # 统计DNS服务器数量
                dns_server_count = 0
                if self.dns_config.get("primary"):
                    dns_server_count += 1
                if self.dns_config.get("secondary"):
                    dns_server_count += 1
                if self.dns_config.get("tertiary"):
                    dns_server_count += 1

                stats["dns_config"] = {
                    "servers": dns_server_count,
                    "dns_over_tls": self.dns_config.get("dns_over_tls", "未设置"),
                    "processed": 1
                }

            # 转换回XML字符串
            xml_content = etree.tostring(root, encoding='utf-8', pretty_print=True).decode('utf-8')
            
        except Exception as e:
            error_msg = _("ntos_extensions.error.extending_xml_failed", error=str(e))
            log(error_msg, "error")
        
        return xml_content, stats
    
    def _add_nat_rules_to_xml(self, root):
        """
        将NAT规则添加到XML中
        
        Args:
            root: XML根节点
        """
        log(_("ntos_extensions.adding_nat_rules"))
        
        # 查找VRF节点
        vrf = root.find(".//vrf")
        if vrf is None:
            log(_("ntos_extensions.error.vrf_not_found"), "error")
            return
        
        # 查找或创建NAT节点
        nat_elem = vrf.find(".//nat")
        if nat_elem is None:
            nat_elem = etree.SubElement(vrf, "nat")
            nat_elem.set("xmlns", "urn:ruijie:ntos:params:xml:ns:yang:nat")
            log(_("ntos_extensions.create_nat_node"))
        
        # 处理每条NAT规则
        for rule in self.nat_rules:
            # 创建NAT规则节点
            rule_elem = etree.SubElement(nat_elem, "rule")
            
            # 设置规则ID
            if "id" in rule:
                etree.SubElement(rule_elem, "id").text = str(rule["id"])
            else:
                # 生成唯一ID
                rule_id = len(nat_elem.findall("./rule")) + 1
                etree.SubElement(rule_elem, "id").text = str(rule_id)
            
            # 设置规则类型
            if "type" in rule:
                etree.SubElement(rule_elem, "type").text = rule["type"]
            
            # 设置源地址
            if "src_addr" in rule:
                src_addr = etree.SubElement(rule_elem, "src-addr")
                etree.SubElement(src_addr, "addr").text = rule["src_addr"]
            
            # 设置目标地址
            if "dst_addr" in rule:
                dst_addr = etree.SubElement(rule_elem, "dst-addr")
                etree.SubElement(dst_addr, "addr").text = rule["dst_addr"]
            
            # 设置转换后的地址
            if "translated_addr" in rule:
                trans_addr = etree.SubElement(rule_elem, "translated-addr")
                etree.SubElement(trans_addr, "addr").text = rule["translated_addr"]
            
            # 设置服务
            if "service" in rule:
                service = etree.SubElement(rule_elem, "service")
                etree.SubElement(service, "name").text = rule["service"]
            
            # 设置状态
            etree.SubElement(rule_elem, "enabled").text = "true" if rule.get("enabled", True) else "false"
        
        log(_("ntos_extensions.nat_rules_added"))
    
    def _add_vpn_tunnels_to_xml(self, root):
        """
        将VPN隧道添加到XML中
        
        Args:
            root: XML根节点
        """
        log(_("ntos_extensions.adding_vpn_tunnels"))
        
        # 查找VRF节点
        vrf = root.find(".//vrf")
        if vrf is None:
            log(_("ntos_extensions.error.vrf_not_found"), "error")
            return
        
        # 查找或创建VPN节点
        vpn_elem = vrf.find(".//vpn")
        if vpn_elem is None:
            vpn_elem = etree.SubElement(vrf, "vpn")
            vpn_elem.set("xmlns", "urn:ruijie:ntos:params:xml:ns:yang:vpn")
            log(_("ntos_extensions.create_vpn_node"))
        
        # 查找或创建IPsec节点
        ipsec_elem = vpn_elem.find("./ipsec")
        if ipsec_elem is None:
            ipsec_elem = etree.SubElement(vpn_elem, "ipsec")
            log(_("ntos_extensions.create_ipsec_node"))
        
        # 处理每个VPN隧道
        for tunnel in self.vpn_tunnels:
            # 创建隧道节点
            tunnel_elem = etree.SubElement(ipsec_elem, "tunnel")
            
            # 设置隧道名称
            if "name" in tunnel:
                etree.SubElement(tunnel_elem, "name").text = tunnel["name"]
            
            # 设置本地网关
            if "local_gateway" in tunnel:
                local_gw = etree.SubElement(tunnel_elem, "local-gateway")
                etree.SubElement(local_gw, "address").text = tunnel["local_gateway"]
            
            # 设置远程网关
            if "remote_gateway" in tunnel:
                remote_gw = etree.SubElement(tunnel_elem, "remote-gateway")
                etree.SubElement(remote_gw, "address").text = tunnel["remote_gateway"]
            
            # 设置认证方式
            if "auth_method" in tunnel:
                auth = etree.SubElement(tunnel_elem, "authentication")
                etree.SubElement(auth, "method").text = tunnel["auth_method"]
                
                # 如果是预共享密钥
                if tunnel["auth_method"] == "psk" and "psk" in tunnel:
                    etree.SubElement(auth, "psk").text = tunnel["psk"]
            
            # 设置加密算法
            if "encryption" in tunnel:
                etree.SubElement(tunnel_elem, "encryption").text = tunnel["encryption"]
            
            # 设置哈希算法
            if "hash" in tunnel:
                etree.SubElement(tunnel_elem, "hash").text = tunnel["hash"]
            
            # 设置DH组
            if "dh_group" in tunnel:
                etree.SubElement(tunnel_elem, "dh-group").text = str(tunnel["dh_group"])
            
            # 设置状态
            etree.SubElement(tunnel_elem, "enabled").text = "true" if tunnel.get("enabled", True) else "false"
        
        log(_("ntos_extensions.vpn_tunnels_added"))
    
    def _add_dhcp_servers_to_xml(self, root):
        """
        将DHCP服务器添加到XML中
        
        Args:
            root: XML根节点
        """
        log(_("ntos_extensions.adding_dhcp_servers"))
        
        # 查找VRF节点
        vrf = root.find(".//vrf")
        if vrf is None:
            log(_("ntos_extensions.error.vrf_not_found"), "error")
            return
        
        # 查找或创建DHCP服务器节点
        dhcp_server_elem = vrf.find(".//dhcp-server")
        if dhcp_server_elem is None:
            dhcp_server_elem = etree.SubElement(vrf, "dhcp-server")
            dhcp_server_elem.set("xmlns", "urn:ruijie:ntos:params:xml:ns:yang:dhcp-server")
            log(_("ntos_extensions.create_dhcp_server_node"))
        
        # 处理每个DHCP服务器
        for server in self.dhcp_servers:
            # 创建服务器节点
            server_elem = etree.SubElement(dhcp_server_elem, "server")
            
            # 设置接口名称
            if "interface" in server:
                etree.SubElement(server_elem, "interface").text = server["interface"]
            
            # 设置状态
            etree.SubElement(server_elem, "enabled").text = "true" if server.get("enabled", True) else "false"
            
            # 设置地址池
            if "pool" in server:
                pool_elem = etree.SubElement(server_elem, "pool")
                
                # 设置起始地址
                if "start_ip" in server["pool"]:
                    etree.SubElement(pool_elem, "start-ip").text = server["pool"]["start_ip"]
                
                # 设置结束地址
                if "end_ip" in server["pool"]:
                    etree.SubElement(pool_elem, "end-ip").text = server["pool"]["end_ip"]
                
                # 设置子网掩码
                if "netmask" in server["pool"]:
                    etree.SubElement(pool_elem, "netmask").text = server["pool"]["netmask"]
            
            # 设置租约时间
            if "lease_time" in server:
                etree.SubElement(server_elem, "lease-time").text = str(server["lease_time"])
            
            # 设置网关
            if "gateway" in server:
                etree.SubElement(server_elem, "gateway").text = server["gateway"]
            
            # 设置DNS服务器
            if "dns_servers" in server and server["dns_servers"]:
                for dns in server["dns_servers"]:
                    dns_elem = etree.SubElement(server_elem, "dns-server")
                    etree.SubElement(dns_elem, "address").text = dns
        
        log(_("ntos_extensions.dhcp_servers_added"))

    def _add_dns_config_to_xml(self, root):
        """
        将DNS配置添加到XML中

        Args:
            root: XML根节点
        """
        log(_("ntos_extensions.adding_dns_config"))

        # 导入DNS XML生成器
        from engine.generators.dns_xml_generator import integrate_dns_config_to_xml

        # 使用DNS XML生成器集成DNS配置
        success = integrate_dns_config_to_xml(root, self.dns_config)

        if success:
            # 统计DNS服务器数量
            dns_server_count = 0
            if self.dns_config.get("primary"):
                dns_server_count += 1
            if self.dns_config.get("secondary"):
                dns_server_count += 1
            if self.dns_config.get("tertiary"):
                dns_server_count += 1

            log(_("ntos_extensions.dns_config_added",
                  servers=dns_server_count,
                  dns_over_tls=self.dns_config.get("dns_over_tls", "未设置")))
        else:
            log(_("ntos_extensions.error.dns_config_failed"), "error")

# 工厂函数 - 创建NTOS生成器实例
def create_ntos_generator(model, version, use_extended=False, custom_template=None, transparent_mode_result=None):
    """
    创建NTOS生成器实例

    Args:
        model (str): 设备型号
        version (str): 设备版本
        use_extended (bool): 是否使用扩展生成器
        custom_template (str, optional): 自定义模板路径
        transparent_mode_result (dict, optional): 透明模式处理结果，用于桥接接口生成

    Returns:
        NTOSGenerator or ExtendedNTOSGenerator: NTOS生成器实例
    """
    if use_extended:
        return ExtendedNTOSGenerator(model, version, custom_template, transparent_mode_result)
    else:
        return NTOSGenerator(model, version, transparent_mode_result)

# 如果直接运行此文件，执行测试
if __name__ == "__main__":
    # 创建扩展的NTOS生成器
    generator = ExtendedNTOSGenerator("z5100s", "R10P2")
    
    # 添加接口
    interfaces = [
        {
            "name": "Ge0/0",
            "status": "up",
            "ip": "***********/24",
            "role": "lan"
        }
    ]
    generator.add_interfaces(interfaces)
    
    # 添加NAT规则
    nat_rules = [
        {
            "id": 1,
            "type": "snat",
            "src_addr": "***********/24",
            "translated_addr": "********"
        }
    ]
    generator.add_nat_rules(nat_rules)
    
    # 生成配置
    xml_content, stats = generator.generate()
    
    # 打印生成的XML
    print("生成的XML:")
    print(xml_content)
    
    # 打印统计信息
    print("\n转换统计信息:")
    for category, info in stats.items():
        print(f"{category}: {info}") 