from lxml import etree
from engine.utils.logger import log
from engine.utils.i18n import _  # 导入国际化函数
from engine.generators.xml_utils import NS_INTERFACE, NS_FLOW, NS_MONITOR, NS_IPMAC, NS_ACCESS
from engine.generators.interface_common import InterfaceCommon
from engine.generators.fortinet_interface_optimizer import FortinetPhysicalInterfaceHandler

class PhysicalInterfaceHandler:
    """
    物理接口处理类，用于管理物理接口配置
    """
    
    def __init__(self, interface_common=None, operation_mode_result=None):
        """
        初始化物理接口处理器

        Args:
            interface_common: 接口公共处理器实例，如果为None则创建一个新实例
            operation_mode_result: 操作模式处理结果，包含透明模式信息（如果适用）
        """
        self.interface_common = interface_common if interface_common else InterfaceCommon()

        # 提取透明模式信息（如果是透明模式）
        transparent_mode_result = None

        # 添加调试日志
        from engine.utils.logger import log

        if operation_mode_result:
            mode = operation_mode_result.get("mode")

            if mode == "transparent":
                transparent_mode_result = operation_mode_result
            else:
                log(_("physical_interface_handler.initialized_non_transparent"), "info")
        else:
            log(_("physical_interface_handler.initialized_empty_operation_mode"), "info")

        # 初始化飞塔优化器，传递透明模式信息
        self.fortinet_optimizer = FortinetPhysicalInterfaceHandler(self.interface_common, transparent_mode_result)

    def _should_filter_interface(self, interface_name: str, intf_data: dict, interface_mapping: dict = None) -> bool:
        """
        检查接口是否应该被过滤（借鉴旧架构逻辑）

        Args:
            interface_name: 接口名称
            intf_data: 接口数据
            interface_mapping: 接口映射

        Returns:
            bool: 是否应该过滤
        """
        # 首先检查特殊的逻辑接口（优先级最高）
        # 借鉴旧架构：过滤modem接口（逻辑接口，不需要转换）
        if interface_name.lower() == 'modem':
            log(_("physical_interface_handler.filter_modem_interface", interface_name=interface_name), "info")
            return True

        # 然后检查type字段：如果明确标记为physical，则不过滤
        interface_type = intf_data.get('type')
        if interface_type == 'physical':
            log(_("physical_interface_handler.keep_physical_interface", interface_name=interface_name), "debug")
            return False

        # 过滤tunnel接口
        if interface_name.lower().startswith('tunnel') or intf_data.get('type') == 'tunnel':
            log(_("physical_interface_handler.filter_tunnel_interface", interface_name=interface_name), "info")
            return True

        # 精确匹配已知逻辑接口模式
        interface_name_lower = interface_name.lower()

        # NAF隧道接口
        if interface_name_lower.startswith('naf.'):
            log(_("physical_interface_handler.filter_logical_interface", interface_name=interface_name), "info")
            return True

        # L2TP隧道接口
        if interface_name_lower.startswith('l2t.'):
            log(_("physical_interface_handler.filter_logical_interface", interface_name=interface_name), "info")
            return True

        # SSL VPN接口
        if interface_name_lower.startswith('ssl.'):
            log(_("physical_interface_handler.filter_logical_interface", interface_name=interface_name), "info")
            return True

        # FortiLink聚合接口（精确匹配）
        if interface_name_lower == 'fortilink':
            log(_("physical_interface_handler.filter_logical_interface", interface_name=interface_name), "info")
            return True

        # 检查接口是否有映射关系，只处理有映射关系的接口
        if interface_mapping and not self._has_interface_mapping(interface_name, interface_mapping, intf_data):
            log(_("physical_interface_handler.filter_unmapped_interface", interface_name=interface_name), "info")
            log(_("physical_interface_handler.mapping_check_failed", interface_name=interface_name, interface_mapping=interface_mapping), "info")
            return True

        return False

    def _has_interface_mapping(self, interface_name: str, interface_mapping: dict, intf_data: dict = None) -> bool:
        """
        检查接口是否有映射关系

        Args:
            interface_name: 接口名称（可能是原始名称或映射后名称）
            interface_mapping: 接口映射字典
            intf_data: 接口配置（可选）

        Returns:
            bool: 是否有映射关系
        """
        if not interface_mapping:
            return False

        # 处理嵌套的映射字典结构
        actual_mapping = interface_mapping.get('interface_mappings', interface_mapping)

        # 直接检查接口是否在映射中（原始名称）
        if interface_name in actual_mapping:
            return True

        # 检查是否为映射后的名称（反向查找）
        if interface_name in actual_mapping.values():
            return True

        # 检查是否为子接口，如果父接口有映射关系，则子接口也有
        if '.' in interface_name:
            parent_interface = interface_name.split('.')[0]
            if parent_interface in actual_mapping or parent_interface in actual_mapping.values():
                return True

        # 检查是否有vlanid字段且interface字段指向有映射的父接口
        if intf_data and 'vlanid' in intf_data and 'interface' in intf_data:
            parent_interface = intf_data['interface']
            if parent_interface in actual_mapping or parent_interface in actual_mapping.values():
                return True

        # 检查接口配置中是否有原始名称信息
        if intf_data:
            original_name = intf_data.get('original_name') or intf_data.get('raw_name')
            if original_name and original_name in actual_mapping:
                return True

        return False

    def _normalize_interface_role(self, role, interface_name, mode=""):
        """
        标准化接口角色，将不支持的角色转换为支持的角色
        
        Args:
            role: 原始角色名称
            interface_name: 接口名称（用于日志）
            mode: 接口模式（可选，用于特殊情况）
            
        Returns:
            str: 标准化后的角色名称（"lan"或"wan"）
        """
        if not role:
            # 如果没有指定角色，则根据模式判断
            if mode.lower() in ["pppoe", "dhcp"]:
                return "wan"
            else:
                return "lan"
        
        # 转换为小写并去除空白
        normalized_role = role.lower().strip()
        
        # 检查是否为支持的角色
        if normalized_role in ["lan", "wan"]:
            return normalized_role
        
        # 处理不支持的角色
        if normalized_role in ["dmz", "undefined"]:
            # 记录警告信息
            log(_("interface_handler.warning.unsupported_role", 
                interface=interface_name, role=normalized_role), "warning")
            log(_("interface_handler.role_defaulted_to_lan", name=interface_name))
            
            # 统一转换为lan
            return "lan"
        
        # 其他未知角色，记录警告并根据模式判断
        log(_("interface_handler.warning.unknown_role", 
            interface=interface_name, role=normalized_role), "warning")
        
        # 根据模式判断默认角色
        if mode.lower() in ["pppoe", "dhcp"]:
            log(_("interface_handler.role_defaulted_to_wan", name=interface_name))
            return "wan"
        else:
            log(_("interface_handler.role_defaulted_to_lan", name=interface_name))
            return "lan"
    
    def _is_fortinet_config(self, intf_data):
        """
        检测是否为飞塔配置格式
        
        Args:
            intf_data: 接口数据
            
        Returns:
            bool: 是否为飞塔配置
        """
        # 检查是否包含飞塔特有的配置字段
        fortinet_indicators = [
            "allowaccess",  # 飞塔访问控制字段
            "ip",          # 飞塔IP配置方式
            "netmask",     # 子网掩码配置
            "pppoe-username",  # PPPoE用户名配置
            "pppoe_username",  # PPPoE用户名配置（下划线格式）
            "pppoe-password",  # PPPoE密码配置
            "pppoe_password",  # PPPoE密码配置（下划线格式）
            "ipv6",        # IPv6配置节点
            "device-identification",  # 设备探测
            "device_identification",  # 设备探测（下划线格式）
            "alias",       # 接口别名
            "description", # 接口描述
            "estimated-upstream-bandwidth",   # 上行带宽
            "estimated_upstream_bandwidth",   # 上行带宽（下划线格式）
            "estimated-downstream-bandwidth", # 下行带宽
            "estimated_downstream_bandwidth", # 下行带宽（下划线格式）
            "snmp-index",  # SNMP索引
            "snmp_index",  # SNMP索引（下划线格式）
            "type",        # 接口类型
            "vdom"         # 虚拟域
        ]
        
        # 如果包含任何飞塔特有字段，认为是飞塔配置
        for indicator in fortinet_indicators:
            if indicator in intf_data:
                return True
        
        # 检查IPv6配置中的飞塔特有字段
        ipv6_config = intf_data.get("ipv6", {})
        if ipv6_config:
            ipv6_indicators = ["ip6-mode", "ip6-address", "ip6-allowaccess"]
            for indicator in ipv6_indicators:
                if indicator in ipv6_config:
                    return True
        
        # 检查IP配置格式（飞塔通常是单独的ip和netmask字段）
        if "ip" in intf_data and "netmask" in intf_data:
            return True
        
        # 检查是否已经是CIDR格式但应该走标准流程
        if "ip" in intf_data and "/" in str(intf_data["ip"]) and len([x for x in fortinet_indicators if x in intf_data]) == 0:
            return False
            
        return False
    
    def append_default_interface_config(self, physical_elem):
        """
        将默认段落插入到接口物理层节点下
        
        Args:
            physical_elem: 物理接口元素
        """
        # 网络栈节点
        network_stack = etree.SubElement(physical_elem, "network-stack")
        ipv4_ns = etree.SubElement(network_stack, "ipv4")
        etree.SubElement(ipv4_ns, "arp-ignore").text = "check-interface-and-subnet"
        
        # 反向路径
        etree.SubElement(physical_elem, "reverse-path").text = "true"
        
        # snooping 节点 - 确保标签名称正确
        snooping = etree.SubElement(physical_elem, "snooping")
        # 直接创建子元素，避免使用变量
        etree.SubElement(snooping, "trust").text = "false"
        etree.SubElement(snooping, "suppress").text = "false"
        
        # flow-control 节点 - 使用独立命名空间
        flow_control = etree.SubElement(physical_elem, "flow-control")
        flow_control.set("xmlns", "urn:ruijie:ntos:params:xml:ns:yang:flow-control")
        etree.SubElement(flow_control, "enabled").text = "false"
        
        # monitor 节点 - 使用独立命名空间
        monitor = etree.SubElement(physical_elem, "monitor")
        monitor.set("xmlns", NS_MONITOR)
        etree.SubElement(monitor, "notify-up-drop-rate-threshold").text = "1000"
        etree.SubElement(monitor, "notify-down-drop-rate-threshold").text = "1000"
        etree.SubElement(monitor, "if-notify-enable").text = "false"
        etree.SubElement(monitor, "notify-up-usage-threshold").text = "100"
        etree.SubElement(monitor, "notify-down-usage-threshold").text = "100"
        etree.SubElement(monitor, "notify-up-speed-threshold").text = "100000000"
        etree.SubElement(monitor, "notify-down-speed-threshold").text = "100000000"
        
        # ip-mac-bind 节点 - 使用独立命名空间
        ip_mac_bind = etree.SubElement(physical_elem, "ip-mac-bind")
        ip_mac_bind.set("xmlns", NS_IPMAC)
        ipv4_bind = etree.SubElement(ip_mac_bind, "ipv4")
        etree.SubElement(ipv4_bind, "enabled").text = "false"
        ipv6_bind = etree.SubElement(ip_mac_bind, "ipv6")
        etree.SubElement(ipv6_bind, "enabled").text = "false"
        
        # 检查是否已存在access-control节点，避免重复
        existing_access_control = None

        # 使用多种方式查找现有的access-control节点，考虑命名空间
        try:
            existing_access_control = physical_elem.find(".//{%s}access-control" % NS_ACCESS)
        except:
            pass

        if existing_access_control is None:
            existing_access_control = physical_elem.find(".//access-control")

        if existing_access_control is None:
            for child in physical_elem:
                if child.tag.endswith("}access-control") or child.tag == "access-control":
                    existing_access_control = child
                    break

        if existing_access_control is not None:
            # 如果已存在，则不再添加
            log(_("interface_handler.access_control_already_exists"))
        else:
            # 添加access-control节点 - 使用独立命名空间
            access_control = etree.SubElement(physical_elem, "access-control")
            access_control.set("xmlns", NS_ACCESS)
            etree.SubElement(access_control, "https").text = "false"
            etree.SubElement(access_control, "ping").text = "false"
            etree.SubElement(access_control, "ssh").text = "false"
    
    def create_new_interface(self, interface_elem, intf_data, interface_mapping=None):
        """
        创建新的物理接口

        Args:
            interface_elem: 父接口元素
            intf_data: 接口数据
            interface_mapping: 接口名称映射
        """
        # 借鉴旧架构：首先检查是否应该过滤接口
        interface_name = intf_data.get("name", "unknown")

        # DEBUG: 确认方法被调用
        log("PhysicalInterfaceHandler.create_new_interface 被调用，接口: {interface_name}", "debug")
        log("接口数据键: {list(intf_data.keys())}", "debug")
        if self._should_filter_interface(interface_name, intf_data, interface_mapping):
            log(_("physical_interface_handler.skip_filtered_interface", interface_name=interface_name), "info")
            return None

        log(_("interface_handler.create_new_interface", name=interface_name))

        # 检查是否为飞塔配置，如果是则使用专门的优化器
        if self._is_fortinet_config(intf_data):
            log(_("interface_handler.using_fortinet_optimizer", name=interface_name))
            log(_("physical_interface_handler.debug_call_fortinet_optimizer", interface_name=interface_name), "debug")
            log(_("physical_interface_handler.debug_intf_data_keys", keys=list(intf_data.keys())), "debug")
            log(_("physical_interface_handler.debug_intf_data_ip", ip=intf_data.get('ip'), netmask=intf_data.get('netmask')), "debug")
            return self.fortinet_optimizer.create_fortinet_interface(interface_elem, intf_data)
        
        # 创建physical节点
        physical = etree.SubElement(interface_elem, "physical")
        # 注意：physical节点应该使用默认命名空间（与模板保持一致），不设置xmlns属性
        
        # 设置接口名称 - 使用name标签而不是n标签
        etree.SubElement(physical, "name").text = intf_data["name"]
        
        # 注释掉多余的配置，以匹配原版输出结构
        # 原版输出中物理接口没有enabled和working-mode配置
        # enabled = intf_data.get("status", "up") == "up"
        # etree.SubElement(physical, "enabled").text = "true" if enabled else "false"

        # working_mode = intf_data.get("working_mode", "route")
        # etree.SubElement(physical, "working-mode").text = working_mode
        # log(_("physical_interface_handler.debug_set_working_mode", interface_name=intf_data['name'], working_mode=working_mode), "debug")

        # 添加默认接口配置
        self.append_default_interface_config(physical)
        
        # 设置接口角色
        if "role" in intf_data:
            # 使用_normalize_interface_role方法标准化角色
            normalized_role = self._normalize_interface_role(intf_data["role"], intf_data["name"], intf_data.get("mode", ""))
            etree.SubElement(physical, "wanlan").text = normalized_role
        
        # 检查接口模式
        is_dhcp = intf_data.get("mode", "") == "dhcp"
        is_pppoe = intf_data.get("mode", "") == "pppoe"
        
        # 创建ipv4节点
        ipv4 = etree.SubElement(physical, "ipv4")
        
        # 根据接口模式设置配置
        if is_dhcp:
            # DHCP模式
            etree.SubElement(ipv4, "enabled").text = "true"
            self.interface_common.create_dhcp_config(ipv4)
            log(_("interface_handler.interface_mode_dhcp", name=intf_data["name"]))
        elif is_pppoe:
            # PPPoE模式
            self.interface_common.create_pppoe_config(ipv4, intf_data)
            log(_("interface_handler.interface_mode_pppoe", name=intf_data["name"]))
        elif "ip" in intf_data or "ip_address" in intf_data:
            # 静态IP模式 - 支持ip和ip_address字段
            etree.SubElement(ipv4, "enabled").text = "true"

            # 获取IP地址（优先使用ip_address字段）
            ip_addr = intf_data.get("ip_address") or intf_data.get("ip")

            # 解析IP地址和前缀长度
            if ip_addr and "/" in ip_addr and len(ip_addr.split("/")) == 2:
                address = etree.SubElement(ipv4, "address")
                # 使用YANG模型兼容的masked-ipv4-address格式 (例如: "***********/24")
                # 而不是分离的ip和prefix-length元素
                etree.SubElement(address, "ip").text = ip_addr
                log(_("interface_handler.interface_mode_static", name=intf_data["name"], ip=ip_addr))
        else:
            # 默认禁用IPv4
            etree.SubElement(ipv4, "enabled").text = "false"
            log(_("interface_handler.interface_mode_disabled", name=intf_data["name"]))
        
        # 创建ipv6节点（默认禁用）
        ipv6 = etree.SubElement(physical, "ipv6")
        etree.SubElement(ipv6, "enabled").text = "false"
        
        # 设置接口描述
        if "description" in intf_data:
            etree.SubElement(physical, "description").text = intf_data["description"]
        
        # 设置接口带宽
        if "bandwidth" in intf_data:
            etree.SubElement(physical, "bandwidth").text = str(intf_data["bandwidth"])
        
        # 设置访问控制 - 优先使用处理后的access_control数据
        access_control_data = intf_data.get("access_control")
        allowaccess = intf_data.get("allowaccess", "")

        if access_control_data:
            # 将字典格式转换为列表格式
            access_list = []
            for service, enabled in access_control_data.items():
                if enabled:
                    access_list.append(service)

            self.interface_common.create_access_control(physical, access_list)
        elif allowaccess:
            # 回退到原始allowaccess数据
            log(_("interface_handler.using_raw_allowaccess"))
            # 检查 allowaccess 的数据类型
            if isinstance(allowaccess, list):
                # 如果已经是列表，直接使用
                access_list = allowaccess
            elif isinstance(allowaccess, str):
                # 如果是字符串，进行分割
                access_list = allowaccess.split()
            else:
                # 其他类型，转换为字符串后分割
                access_list = str(allowaccess).split()

            self.interface_common.create_access_control(physical, access_list)
        
        log(_("interface_handler.interface_created", name=intf_data["name"]))
        return physical
    
    def update_physical_interface(self, interface_elem, intf_data):
        """
        更新物理接口配置 - 包装函数，调用update_existing_interface
        
        Args:
            interface_elem: 接口元素
            intf_data: 接口数据
        """
        log(_("interface_handler.update_physical_interface", name=intf_data.get("name", "unknown")))
        
        # 直接调用update_existing_interface函数
        self.update_existing_interface(interface_elem, intf_data)
        
        log(_("interface_handler.update_physical_interface.complete", name=intf_data.get("name", "unknown")))
    
    def update_existing_interface(self, phy_elem, intf_data):
        """
        更新现有接口配置，正确识别和更新NTOS接口节点
        
        Args:
            phy_elem: 物理接口元素
            intf_data: 接口数据
        """
        log(_("interface_handler.update_existing_interface", name=intf_data.get("name", "unknown")))
        
        # 更新启用状态
        enabled_elem = self._find_child_element(phy_elem, "enabled")
        if enabled_elem is not None:
            enabled_elem.text = "true" if intf_data.get("status", "up") == "up" else "false"

        # 更新工作模式（支持透明模式）
        if "working_mode" in intf_data:
            working_mode = intf_data["working_mode"]
            working_mode_elem = self._find_child_element(phy_elem, "working-mode")
            if working_mode_elem is not None:
                working_mode_elem.text = working_mode
            else:
                etree.SubElement(phy_elem, "working-mode").text = working_mode
            log(_("physical_interface_handler.debug_update_working_mode", interface_name=intf_data.get('name', 'unknown'), working_mode=working_mode), "debug")

        # 更新接口角色
        if "role" in intf_data:
            # 使用_normalize_interface_role方法标准化角色
            normalized_role = self._normalize_interface_role(intf_data["role"], intf_data.get("name", "unknown"), intf_data.get("mode", ""))
            
            wanlan_elem = self._find_child_element(phy_elem, "wanlan")
            if wanlan_elem is not None:
                wanlan_elem.text = normalized_role
            else:
                etree.SubElement(phy_elem, "wanlan").text = normalized_role
        
        # 更新接口描述
        if "description" in intf_data:
            desc_elem = self._find_child_element(phy_elem, "description")
            if desc_elem is not None:
                desc_elem.text = intf_data["description"]
            else:
                etree.SubElement(phy_elem, "description").text = intf_data["description"]
        
        # 更新接口带宽配置
        self._update_bandwidth_config(phy_elem, intf_data)
        
        # 检查接口模式
        is_dhcp = intf_data.get("mode", "") == "dhcp"
        is_pppoe = intf_data.get("mode", "") == "pppoe"
        
        # 智能更新IPv4配置
        self._update_ipv4_config(phy_elem, intf_data, is_dhcp, is_pppoe)
        
        # 智能更新IPv6配置
        self._update_ipv6_config(phy_elem, intf_data, is_dhcp)
        
        # 更新访问控制 - 优先使用处理后的access_control数据
        access_control_data = intf_data.get("access_control")
        if access_control_data:
            # 使用接口处理阶段处理后的访问控制数据
            log(_("interface_handler.updating_processed_access_control"))
            # 将字典格式转换为列表格式
            access_list = []
            for service, enabled in access_control_data.items():
                if enabled:
                    access_list.append(service)
            self.interface_common.create_access_control(phy_elem, access_list)
        elif "allowaccess" in intf_data:
            # 回退到原始allowaccess数据
            log(_("interface_handler.updating_raw_allowaccess"))
            allowaccess = intf_data["allowaccess"]
            if isinstance(allowaccess, str):
                allowaccess = allowaccess.split()
            self.interface_common.create_access_control(phy_elem, allowaccess)
        
        log(_("interface_handler.interface_updated", name=intf_data.get("name", "unknown")))
    
    def _update_bandwidth_config(self, phy_elem, intf_data):
        """更新带宽配置 - 支持新架构和原始FortiGate格式"""
        # 处理上行带宽 - 优先使用新架构格式
        upload_bandwidth_config = intf_data.get("upload_bandwidth")
        if upload_bandwidth_config and isinstance(upload_bandwidth_config, dict):
            # 新架构格式：{'value': 500000, 'unit': 'kbps'}
            upload_elem = self._find_child_element(phy_elem, "upload-bandwidth")
            if upload_elem is None:
                upload_elem = etree.SubElement(phy_elem, "upload-bandwidth")

            # 更新或创建子元素
            value_elem = self._find_child_element(upload_elem, "upload-bandwidth-value")
            if value_elem is None:
                value_elem = etree.SubElement(upload_elem, "upload-bandwidth-value")
            value_elem.text = str(upload_bandwidth_config["value"])

            unit_elem = self._find_child_element(upload_elem, "upload-bandwidth-unit")
            if unit_elem is None:
                unit_elem = etree.SubElement(upload_elem, "upload-bandwidth-unit")
            unit_elem.text = upload_bandwidth_config["unit"]

            log(_("interface_handler.configured_upload_bandwidth"), "info")

        elif "estimated-upstream-bandwidth" in intf_data:
            # 原始FortiGate格式兼容性
            upload_bw = int(intf_data["estimated-upstream-bandwidth"]) * 1000  # 转换为kbps
            upload_elem = self._find_child_element(phy_elem, "upload-bandwidth")
            if upload_elem is None:
                upload_elem = etree.SubElement(phy_elem, "upload-bandwidth")

            # 更新或创建子元素
            value_elem = self._find_child_element(upload_elem, "upload-bandwidth-value")
            if value_elem is None:
                value_elem = etree.SubElement(upload_elem, "upload-bandwidth-value")
            value_elem.text = str(upload_bw)

            unit_elem = self._find_child_element(upload_elem, "upload-bandwidth-unit")
            if unit_elem is None:
                unit_elem = etree.SubElement(upload_elem, "upload-bandwidth-unit")
            unit_elem.text = "kbps"

        # 处理下行带宽 - 优先使用新架构格式
        download_bandwidth_config = intf_data.get("download_bandwidth")
        if download_bandwidth_config and isinstance(download_bandwidth_config, dict):
            # 新架构格式：{'value': 600000, 'unit': 'kbps'}
            download_elem = self._find_child_element(phy_elem, "download-bandwidth")
            if download_elem is None:
                download_elem = etree.SubElement(phy_elem, "download-bandwidth")

            # 更新或创建子元素
            value_elem = self._find_child_element(download_elem, "download-bandwidth-value")
            if value_elem is None:
                value_elem = etree.SubElement(download_elem, "download-bandwidth-value")
            value_elem.text = str(download_bandwidth_config["value"])

            unit_elem = self._find_child_element(download_elem, "download-bandwidth-unit")
            if unit_elem is None:
                unit_elem = etree.SubElement(download_elem, "download-bandwidth-unit")
            unit_elem.text = download_bandwidth_config["unit"]

            log(_("interface_handler.configured_download_bandwidth"), "info")

        elif "estimated-downstream-bandwidth" in intf_data:
            # 原始FortiGate格式兼容性
            download_bw = int(intf_data["estimated-downstream-bandwidth"]) * 1000  # 转换为kbps
            download_elem = self._find_child_element(phy_elem, "download-bandwidth")
            if download_elem is None:
                download_elem = etree.SubElement(phy_elem, "download-bandwidth")

            # 更新或创建子元素
            value_elem = self._find_child_element(download_elem, "download-bandwidth-value")
            if value_elem is None:
                value_elem = etree.SubElement(download_elem, "download-bandwidth-value")
            value_elem.text = str(download_bw)

            unit_elem = self._find_child_element(download_elem, "download-bandwidth-unit")
            if unit_elem is None:
                unit_elem = etree.SubElement(download_elem, "download-bandwidth-unit")
            unit_elem.text = "kbps"
    
    def _update_ipv4_config(self, phy_elem, intf_data, is_dhcp, is_pppoe):
        """智能更新IPv4配置，避免破坏现有结构，确保不会创建重复节点"""
        # 安全检查：确保phy_elem是有效的Element
        if phy_elem is None or not hasattr(phy_elem, 'find'):
            log(_("physical_interface_handler.invalid_parent_element"), "error")
            return
        
        # 严格检查现有的IPv4节点，避免重复创建
        ipv4_elem = self._find_or_create_safe_ipv4(phy_elem)
        
        if is_dhcp:
            # DHCP模式：保留现有DHCP配置结构，只更新必要的部分
            self._update_enabled_status(ipv4_elem, True)
            
            # 如果没有DHCP配置，添加DHCP配置
            dhcp_elem = self._find_child_element(ipv4_elem, "dhcp")
            if dhcp_elem is None:
                # 清除静态IP和PPPoE配置
                self._clear_config_types(ipv4_elem, ["address", "pppoe"])
                # 添加DHCP配置
                self.interface_common.create_dhcp_config(ipv4_elem)
                
        elif is_pppoe:
            # PPPoE模式：保留现有PPPoE配置结构
            self._update_enabled_status(ipv4_elem, True)
            
            # 如果没有PPPoE配置，添加PPPoE配置
            pppoe_elem = self._find_child_element(ipv4_elem, "pppoe")
            if pppoe_elem is None:
                # 清除静态IP和DHCP配置
                self._clear_config_types(ipv4_elem, ["address", "dhcp"])
                # 添加PPPoE配置
                self.interface_common.create_pppoe_config(ipv4_elem, intf_data)
            else:
                # 更新现有PPPoE配置的用户名和密码
                self._update_pppoe_credentials(pppoe_elem, intf_data)
                
        elif "ip" in intf_data or "ip_address" in intf_data or "ip_addresses" in intf_data:
            # 静态IP模式：智能更新，保留现有结构 - 支持多个IP地址
            self._update_enabled_status(ipv4_elem, True)

            # 首先清除DHCP和PPPoE配置
            self._clear_config_types(ipv4_elem, ["dhcp", "pppoe"])

            # 处理多个IP地址的情况
            self._handle_multiple_ip_addresses(ipv4_elem, intf_data)
                
        else:
            # 如果没有IP配置，只更新enabled状态
            self._update_enabled_status(ipv4_elem, False)

    def _handle_multiple_ip_addresses(self, ipv4_elem, intf_data):
        """
        处理接口的多个IP地址，解决网络重叠问题

        Args:
            ipv4_elem: IPv4元素
            intf_data: 接口数据
        """
        # 收集真实的IP地址（优先使用实际配置数据）
        real_ip_addresses = []

        # 从实际配置数据收集IP地址
        if "ip_addresses" in intf_data and isinstance(intf_data["ip_addresses"], list):
            real_ip_addresses.extend(intf_data["ip_addresses"])
        elif "ip_address" in intf_data:
            real_ip_addresses.append(intf_data["ip_address"])
        elif "ip" in intf_data:
            # 处理FortiGate格式的IP配置
            ip_value = intf_data["ip"]
            netmask = intf_data.get("netmask", "*************")

            # 转换为CIDR格式
            if "/" not in str(ip_value):
                try:
                    import ipaddress
                    network = ipaddress.IPv4Network(f"{ip_value}/{netmask}", strict=False)
                    cidr_ip = f"{ip_value}/{network.prefixlen}"
                    real_ip_addresses.append(cidr_ip)
                except:
                    real_ip_addresses.append(str(ip_value))
            else:
                real_ip_addresses.append(str(ip_value))

        # 如果没有真实的IP配置，检查XML中的非模板IP
        if not real_ip_addresses:
            existing_addresses = ipv4_elem.findall("address")
            for addr_elem in existing_addresses:
                ip_elem = addr_elem.find("ip")
                if ip_elem is not None and ip_elem.text:
                    ip_text = ip_elem.text
                    # 过滤模板默认IP
                    if not self._is_template_default_ip(ip_text):
                        real_ip_addresses.append(ip_text)

        # 总是清除现有的address节点，无论是否有真实IP
        existing_addresses = ipv4_elem.findall("address")
        for addr_elem in existing_addresses:
            ipv4_elem.remove(addr_elem)

        # 如果没有真实IP地址，不创建任何address节点
        if not real_ip_addresses:
            log("No real IP addresses found, cleared template default IPs", "debug")
            return

        # 如果只有一个IP地址，直接使用，不需要secondary处理
        if len(real_ip_addresses) == 1:
            # 创建单个address节点
            address_elem = etree.SubElement(ipv4_elem, "address")
            etree.SubElement(address_elem, "ip").text = real_ip_addresses[0]
            log(f"Configured single IP address: {real_ip_addresses[0]}", "debug")
            return

        # 多个IP地址时，使用网络冲突解决工具
        try:
            from engine.utils.name_validator import resolve_interface_ip_conflicts
            resolved_ips = resolve_interface_ip_conflicts(real_ip_addresses)
        except ImportError:
            log("Failed to import network conflict resolver, using fallback", "warning")
            resolved_ips = [(ip, False) for ip in real_ip_addresses]

        # 创建新的address节点
        for ip_addr, is_secondary in resolved_ips:
            address_elem = etree.SubElement(ipv4_elem, "address")
            etree.SubElement(address_elem, "ip").text = ip_addr

            # 如果是辅助IP，添加secondary标志
            if is_secondary:
                etree.SubElement(address_elem, "flags").text = "secondary"

        log(f"Processed {len(resolved_ips)} IP addresses for interface, {sum(1 for _, is_sec in resolved_ips if is_sec)} secondary", "debug")

    def _is_template_default_ip(self, ip_address: str) -> bool:
        """
        检查IP地址是否为模板默认IP

        Args:
            ip_address: IP地址字符串

        Returns:
            bool: 是否为模板默认IP
        """
        if not ip_address:
            return False

        # 常见的模板默认IP地址
        template_default_ips = [
            "*************/24",
            "*************",
            "********/24",
            "********",
            "**********/24",
            "**********"
        ]

        # 检查是否为模板默认IP
        for default_ip in template_default_ips:
            if ip_address == default_ip:
                return True

            # 检查不带掩码的情况
            if "/" in ip_address and ip_address.split("/")[0] == default_ip.split("/")[0]:
                if default_ip in template_default_ips:
                    return True

        return False

    def _find_or_create_safe_ipv4(self, phy_elem):
        """安全地查找或创建IPv4节点，避免重复创建"""
        # 查找现有的IPv4节点（使用多种方法确保找到）
        ipv4_elem = phy_elem.find("./ipv4")
        
        # 如果没有找到，检查是否有namespace的IPv4节点
        if ipv4_elem is None:
            try:
                # 尝试查找带命名空间的IPv4节点
                for child in phy_elem:
                    if child.tag.endswith("ipv4") or child.tag == "ipv4":
                        # 确保这是直接的子节点，不是network-stack下的IPv4
                        parent_tag = phy_elem.tag
                        if parent_tag.endswith("physical") or parent_tag == "physical":
                            # 检查这不是network-stack下的IPv4
                            parent_of_child = child.getparent()
                            if parent_of_child is not None and (parent_of_child.tag.endswith("physical") or parent_of_child.tag == "physical"):
                                ipv4_elem = child
                                break
            except Exception as e:
                log(_("physical_interface_handler.find_ipv4_error", error=str(e)), "warning")
        
        # 如果仍然没有找到，创建新的IPv4节点
        if ipv4_elem is None:
            # 验证父节点有效性
            if phy_elem.tag and hasattr(phy_elem, 'append'):
                ipv4_elem = etree.SubElement(phy_elem, "ipv4")
                log(_("physical_interface_handler.created_new_ipv4"))
            else:
                log(_("physical_interface_handler.invalid_parent_for_ipv4"), "error")
                raise ValueError("Invalid parent element for IPv4 creation")
        
        return ipv4_elem
    
    def _find_child_element(self, parent, tag_name):
        """安全地查找子元素，处理命名空间问题"""
        # 首先尝试相对路径查找
        child = parent.find(f"./{tag_name}")
        if child is not None:
            return child
        
        # 如果失败，迭代查找（处理命名空间）
        for child in parent:
            if child.tag.endswith(tag_name) or child.tag == tag_name:
                return child
        
        return None

    def _update_enabled_status(self, ipv4_elem, enabled):
        """更新IPv4启用状态"""
        enabled_elem = self._find_child_element(ipv4_elem, "enabled")
        if enabled_elem is not None:
            enabled_elem.text = "true" if enabled else "false"
        else:
            etree.SubElement(ipv4_elem, "enabled").text = "true" if enabled else "false"
    
    def _clear_config_types(self, ipv4_elem, config_types):
        """清除指定类型的配置，支持命名空间"""
        for child in list(ipv4_elem):
            # 获取标签的本地名称（不包含命名空间）
            local_name = child.tag.split('}')[-1] if '}' in child.tag else child.tag
            if local_name in config_types:
                ipv4_elem.remove(child)
    
    def _update_pppoe_credentials(self, pppoe_elem, intf_data):
        """更新PPPoE认证信息"""
        connection_elem = pppoe_elem.find("./connection")
        if connection_elem is not None:
            # 支持多种用户名字段格式
            username = (intf_data.get("username") or 
                       intf_data.get("pppoe_username") or 
                       intf_data.get("pppoe-username") or
                       intf_data.get("user", ""))
            password = (intf_data.get("password") or 
                       intf_data.get("pppoe_password") or 
                       intf_data.get("pppoe-password") or
                       intf_data.get("pass", ""))
            
            if username:
                user_elem = connection_elem.find("./user")
                if user_elem is not None:
                    user_elem.text = username
            if password:
                pass_elem = connection_elem.find("./password")
                if pass_elem is not None:
                    pass_elem.text = "=*-#!$_2g+awtqJWZWf+C6zl4RLQ=="  # 加密格式
    
    def _update_ipv6_config(self, phy_elem, intf_data, is_dhcp):
        """智能更新IPv6配置"""
        # 检查IPv6配置
        ipv6_config = intf_data.get("ipv6", {})
        has_ipv6_address = "ip6-address" in ipv6_config
        ipv6_mode = ipv6_config.get("ip6-mode", "")
        
        # 查找现有IPv6节点 - 使用命名空间感知查找
        ipv6_elem = self._find_child_element(phy_elem, "ipv6")
        
        if has_ipv6_address or ipv6_mode == "dhcp" or is_dhcp:
            if ipv6_elem is not None:
                # 更新现有IPv6配置
                enabled_elem = self._find_child_element(ipv6_elem, "enabled")
                if enabled_elem is not None:
                    enabled_elem.text = "true"
                else:
                    etree.SubElement(ipv6_elem, "enabled").text = "true"
                
                # 根据模式配置IPv6
                if ipv6_mode == "dhcp" or is_dhcp:
                    # 确保有DHCP配置
                    dhcp_elem = self._find_child_element(ipv6_elem, "dhcp")
                    if dhcp_elem is None:
                        self.interface_common.create_ipv6_dhcp_config(ipv6_elem)
                
                if has_ipv6_address:
                    # 静态IPv6地址配置
                    address_elem = self._find_child_element(ipv6_elem, "address")
                    if address_elem is not None:
                        ip_elem = self._find_child_element(address_elem, "ip")
                        if ip_elem is not None:
                            ip_elem.text = ipv6_config["ip6-address"]
                        else:
                            etree.SubElement(address_elem, "ip").text = ipv6_config["ip6-address"]
                    else:
                        address_elem = etree.SubElement(ipv6_elem, "address")
                        etree.SubElement(address_elem, "ip").text = ipv6_config["ip6-address"]
            else:
                # 创建新的IPv6配置
                ipv6_elem = etree.SubElement(phy_elem, "ipv6")
                etree.SubElement(ipv6_elem, "enabled").text = "true"
                
                if ipv6_mode == "dhcp" or is_dhcp:
                    self.interface_common.create_ipv6_dhcp_config(ipv6_elem)
                    
                if has_ipv6_address:
                    address_elem = etree.SubElement(ipv6_elem, "address")
                    etree.SubElement(address_elem, "ip").text = ipv6_config["ip6-address"]
        elif ipv6_elem is not None:
            # 如果没有IPv6配置，禁用IPv6
            enabled_elem = self._find_child_element(ipv6_elem, "enabled")
            if enabled_elem is not None:
                enabled_elem.text = "false"

# 为了保持向后兼容性，创建一个默认的物理接口处理器实例
_default_physical_interface_handler = PhysicalInterfaceHandler()

# 向后兼容的函数，调用默认物理接口处理器的方法
def append_default_interface_config(physical_elem):
    """向后兼容的添加默认接口配置函数"""
    return _default_physical_interface_handler.append_default_interface_config(physical_elem)

def create_new_interface(interface_elem, intf_data, interface_mapping=None):
    """向后兼容的创建新接口函数"""
    return _default_physical_interface_handler.create_new_interface(interface_elem, intf_data, interface_mapping)

def update_physical_interface(interface_elem, intf_data):
    """向后兼容的更新物理接口函数"""
    return _default_physical_interface_handler.update_physical_interface(interface_elem, intf_data)

def update_existing_interface(phy_elem, intf_data):
    """向后兼容的更新现有接口函数"""
    return _default_physical_interface_handler.update_existing_interface(phy_elem, intf_data)