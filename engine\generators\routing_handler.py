from lxml import etree
from engine.utils.logger import log
from engine.utils.i18n import _  # 导入国际化函数
from engine.generators.xml_utils import NS_ROUTING, find_or_create_child_with_ns, NamespaceManager

class RoutingHandler:
    """
    路由处理类，用于管理路由配置
    """
    
    def __init__(self, ns_manager=None):
        """
        初始化路由处理器
        
        Args:
            ns_manager: 命名空间管理器实例，如果为None则创建一个新实例
        """
        self.ns_manager = ns_manager if ns_manager else NamespaceManager()
    
    def add_routes_to_xml(self, routing_elem, routes_data):
        """
        将路由配置添加到XML中，确保符合YANG模型规范

        优化原则：
        1. 相同目的网段的路由合并到同一个ipv4-route节点
        2. 每个不同的next-hop创建独立的next-hop子节点
        3. 避免重复的next-hop节点
        4. 确保符合YANG模型的min-elements要求

        Args:
            routing_elem: 路由元素
            routes_data: 路由数据列表或字典
        """
        # 处理不同的输入格式
        if isinstance(routes_data, dict):
            # 如果是字典格式，转换为列表
            routes_list = list(routes_data.values())
        else:
            routes_list = routes_data

        log(f"routing_handler: adding {len(routes_list)} routes to xml")

        # 获取或创建static节点
        static_elem = routing_elem.find(".//static")
        if static_elem is None:
            static_elem = etree.SubElement(routing_elem, "static")
            log("routing handler: create static node")

        # 收集现有路由配置的信息，使用目标网络作为键
        existing_routes = {}
        for route_elem in static_elem.findall(".//ipv4-route"):
            dest_elem = route_elem.find("./destination")
            if dest_elem is not None and dest_elem.text:
                existing_routes[dest_elem.text] = route_elem

        # 按目的网段分组路由数据
        grouped_routes = self._group_routes_by_destination(routes_list)

        # 处理每个目的网段的路由组
        for destination, route_group in grouped_routes.items():
            log(f"routing_handler: processing route group - destination: {destination}, count: {len(route_group)}")

            # 检查是否已存在此目标网络的路由
            if destination in existing_routes:
                # 更新现有路由，添加新的next-hop节点
                route_elem = existing_routes[destination]
                for route in route_group:
                    self.update_existing_route(route_elem, route)
            else:
                # 创建新路由，包含第一个next-hop
                first_route = route_group[0]
                self.create_new_route(static_elem, first_route)

                # 如果有多个路由，添加额外的next-hop节点
                if len(route_group) > 1:
                    # 获取刚创建的路由元素
                    new_route_elem = None
                    for route_elem in static_elem.findall(".//ipv4-route"):
                        dest_elem = route_elem.find("./destination")
                        if dest_elem is not None and dest_elem.text == destination:
                            new_route_elem = route_elem
                            break

                    if new_route_elem is not None:
                        for additional_route in route_group[1:]:
                            self.update_existing_route(new_route_elem, additional_route)

        log("routing_handler: routes added to xml")

    def _group_routes_by_destination(self, routes_list):
        """
        按目的网段对路由进行分组

        Args:
            routes_list: 路由数据列表

        Returns:
            dict: 按目的网段分组的路由字典
        """
        grouped = {}

        for route in routes_list:
            destination = route.get("destination", "0.0.0.0/0")

            if destination not in grouped:
                grouped[destination] = []

            grouped[destination].append(route)

        log(f"routing_handler: grouped {len(routes_list)} routes into {len(grouped)} groups")
        return grouped

    def update_existing_route(self, route_elem, route_data):
        """
        更新现有路由配置，支持添加新的next-hop节点

        根据YANG模型，相同目的网段的路由应该在同一个ipv4-route节点下
        使用多个next-hop子节点，而不是创建多个ipv4-route节点

        Args:
            route_elem: 路由元素
            route_data: 路由数据
        """
        log("routing_handler: updating existing route", "debug")

        # 构建新的next-hop标识符
        new_next_hop_key = self._build_next_hop_key(route_data)
        if not new_next_hop_key:
            log("routing_handler: invalid next-hop data", "warning")
            return

        # 检查是否已存在相同的next-hop
        existing_next_hops = route_elem.findall("./next-hop")
        for existing_next_hop in existing_next_hops:
            existing_key_elem = existing_next_hop.find("./next-hop")
            if existing_key_elem is not None and existing_key_elem.text == new_next_hop_key:
                log(f"routing_handler: next-hop already exists: {new_next_hop_key}", "info")
                # 更新现有next-hop的属性
                self._update_next_hop_attributes(existing_next_hop, route_data)
                return

        # 创建新的next-hop节点
        log(f"routing_handler: adding new next-hop: {new_next_hop_key}", "info")
        self._create_next_hop_node(route_elem, route_data, new_next_hop_key)

    def _build_next_hop_key(self, route_data):
        """
        构建next-hop的键值

        Args:
            route_data: 路由数据

        Returns:
            str: next-hop键值，如果无效则返回None
        """
        if route_data.get("blackhole") == "enable" or route_data.get("gateway") == "blackhole":
            return "blackhole"

        gateway = route_data.get("gateway", "")
        interface = route_data.get("interface", "")

        if gateway and interface:
            # 网关+接口格式：gateway%interface
            return f"{gateway}%{interface}"
        elif gateway:
            # 仅网关
            return gateway
        elif interface:
            # 仅接口
            return interface

        return None

    def _update_next_hop_attributes(self, next_hop_elem, route_data):
        """
        更新next-hop节点的属性

        Args:
            next_hop_elem: next-hop元素
            route_data: 路由数据
        """
        # 更新管理距离
        if "distance" in route_data:
            distance_elem = next_hop_elem.find("./distance")
            if distance_elem is not None:
                distance_elem.text = str(route_data["distance"])
            else:
                etree.SubElement(next_hop_elem, "distance").text = str(route_data["distance"])

        # 更新启用状态
        enable_elem = next_hop_elem.find("./enable")
        if enable_elem is not None:
            enable_elem.text = "true"
        else:
            etree.SubElement(next_hop_elem, "enable").text = "true"

        # 更新描述（如果有）
        description = route_data.get("description") or route_data.get("comment")
        if description:
            descr_elem = next_hop_elem.find("./descr")
            if descr_elem is not None:
                descr_elem.text = description
            else:
                etree.SubElement(next_hop_elem, "descr").text = description

    def _create_next_hop_node(self, route_elem, route_data, next_hop_key):
        """
        创建新的next-hop节点

        Args:
            route_elem: 路由元素
            route_data: 路由数据
            next_hop_key: next-hop键值
        """
        # 创建next-hop节点
        next_hop = etree.SubElement(route_elem, "next-hop")

        # 设置next-hop键值
        etree.SubElement(next_hop, "next-hop").text = next_hop_key

        # 设置管理距离
        distance = route_data.get("distance", route_data.get("priority", "1"))
        etree.SubElement(next_hop, "distance").text = str(distance)

        # 启用路由
        etree.SubElement(next_hop, "enable").text = "true"

        # 添加描述（如果有）
        description = route_data.get("description") or route_data.get("comment")
        if description:
            etree.SubElement(next_hop, "descr").text = description

    def create_new_route(self, static_elem, route_data):
        """
        创建新的路由配置

        Args:
            static_elem: 静态路由节点
            route_data: 路由数据
        """
        # 创建IPv4路由节点
        ipv4_route = etree.SubElement(static_elem, "ipv4-route")

        # 设置目标网络
        destination = route_data.get("destination", "0.0.0.0/0")  # 默认为默认路由
        etree.SubElement(ipv4_route, "destination").text = destination

        # 构建next-hop键值
        next_hop_key = self._build_next_hop_key(route_data)
        if not next_hop_key:
            log("routing_handler: invalid next-hop data for new route", "warning")
            # 创建默认的blackhole next-hop以满足YANG模型要求
            next_hop_key = "blackhole"

        # 创建next-hop节点
        self._create_next_hop_node(ipv4_route, route_data, next_hop_key)

        log(f"routing_handler: created new route - destination: {destination}, next-hop: {next_hop_key}")
    
    def create_static_route_config(self, vrf_elem, route_data, interface_mapping=None):
        """
        创建静态路由配置
        
        Args:
            vrf_elem: VRF XML元素
            route_data: 解析的路由数据
            interface_mapping: 接口映射信息
        
        Returns:
            bool: 路由创建是否成功
        """
        # 获取或创建routing节点
        routing_elem = self.ns_manager.find_or_create_child_with_ns(vrf_elem, "routing", NS_ROUTING)
        
        # 获取或创建static节点
        static_elem = self.ns_manager.find_or_create_child_with_ns(routing_elem, "static")
        
        # 直接调用创建新路由的函数
        self.create_new_route(static_elem, route_data)
        
        return True

# 为了保持向后兼容性，创建一个默认的路由处理器实例
_default_routing_handler = RoutingHandler()

# 向后兼容的函数，调用默认路由处理器的方法
def add_routes_to_xml(routing_elem, routes_data):
    """向后兼容的添加路由到XML函数"""
    return _default_routing_handler.add_routes_to_xml(routing_elem, routes_data)

def update_existing_route(route_elem, route_data):
    """向后兼容的更新现有路由函数"""
    return _default_routing_handler.update_existing_route(route_elem, route_data)

def create_new_route(static_elem, route_data):
    """向后兼容的创建新路由函数"""
    return _default_routing_handler.create_new_route(static_elem, route_data)

def create_static_route_config(vrf_elem, route_data, interface_mapping=None):
    """向后兼容的创建静态路由配置函数"""
    return _default_routing_handler.create_static_route_config(vrf_elem, route_data, interface_mapping) 