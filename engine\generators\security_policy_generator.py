#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
安全策略XML生成器模块

生成符合NTOS YANG模型的安全策略XML配置
支持以下功能：
1. 安全策略规则生成
2. 命名空间管理
3. XML模板集成
4. YANG模型验证
"""

from typing import Dict, List, Any, Optional
from lxml import etree
from engine.utils.logger import log
from engine.utils.i18n import _
from engine.generators.xml_utils import NamespaceManager


class SecurityPolicyGenerator:
    """安全策略XML生成器类"""
    
    def __init__(self, name_mapping_manager=None):
        """
        初始化生成器

        Args:
            name_mapping_manager: 名称映射管理器实例
        """
        self.name = "security_policy_generator"
        self.description = _("security_policy.generator_description")

        # 安全策略命名空间
        self.security_policy_namespace = "urn:ruijie:ntos:params:xml:ns:yang:security-policy"

        # 初始化命名空间管理器
        self.ns_manager = NamespaceManager()

        # 名称映射管理器
        self.name_mapping_manager = name_mapping_manager
        if self.name_mapping_manager is None:
            from engine.utils.name_mapping_manager import NameMappingManager
            self.name_mapping_manager = NameMappingManager()
    
    def generate_security_policy_xml(self, security_policies: List[Dict], root: etree.Element = None) -> etree.Element:
        """
        生成安全策略XML配置
        
        Args:
            security_policies: 安全策略配置列表
            root: 可选的根XML元素，如果提供则在其中查找VRF节点
            
        Returns:
            etree.Element: 安全策略XML元素
        """
        if not security_policies:
            log(_("security_policy.no_policies_to_generate"))
            return None
        
        log(_("security_policy.start_generation", count=len(security_policies)))
        
        # 如果提供了根元素，在其中查找或创建安全策略节点
        if root is not None:
            return self._integrate_with_existing_xml(security_policies, root)
        else:
            # 创建独立的安全策略XML
            return self._create_standalone_security_policy_xml(security_policies)
    
    def _integrate_with_existing_xml(self, security_policies: List[Dict], root: etree.Element) -> etree.Element:
        """
        将安全策略集成到现有XML中
        
        Args:
            security_policies: 安全策略配置列表
            root: 根XML元素
            
        Returns:
            etree.Element: 安全策略容器元素
        """
        # 查找或创建VRF节点
        vrf = self.ns_manager.find_or_create_vrf(root)
        
        # 查找或创建安全策略容器
        security_policy_container = self._find_or_create_security_policy_container(vrf)
        
        # 添加策略规则
        for policy in security_policies:
            policy_element = self._create_policy_element(policy)
            if policy_element is not None:
                security_policy_container.append(policy_element)
        
        log(_("security_policy.integration_complete", count=len(security_policies)))
        return security_policy_container
    
    def _create_standalone_security_policy_xml(self, security_policies: List[Dict]) -> etree.Element:
        """
        创建独立的安全策略XML
        
        Args:
            security_policies: 安全策略配置列表
            
        Returns:
            etree.Element: 安全策略XML元素
        """
        # 创建安全策略容器
        security_policy_container = etree.Element(
            "security-policy",
            nsmap={None: self.security_policy_namespace}
        )
        
        # 添加策略规则
        for policy in security_policies:
            policy_element = self._create_policy_element(policy)
            if policy_element is not None:
                security_policy_container.append(policy_element)
        
        log(_("security_policy.standalone_creation_complete", count=len(security_policies)))
        return security_policy_container
    
    def _find_or_create_security_policy_container(self, vrf: etree.Element) -> etree.Element:
        """
        在VRF中查找或创建安全策略容器
        
        Args:
            vrf: VRF元素
            
        Returns:
            etree.Element: 安全策略容器元素
        """
        # 尝试查找现有的安全策略容器（支持带或不带命名空间前缀）
        security_policy_container = vrf.find(".//security-policy")
        if security_policy_container is None:
            # 尝试查找带命名空间的版本
            security_policy_container = vrf.find(f".//{{{self.security_policy_namespace}}}security-policy")
        
        if security_policy_container is None:
            # 创建新的安全策略容器（不使用命名空间前缀）
            security_policy_container = etree.SubElement(vrf, "security-policy")
            security_policy_container.set("xmlns", self.security_policy_namespace)
            log(_("security_policy.container_created"))
        else:
            log(_("security_policy.container_found"))
        
        return security_policy_container
    
    def _create_policy_element(self, policy: Dict) -> Optional[etree.Element]:
        """
        创建单个策略元素

        Args:
            policy: 策略配置字典

        Returns:
            etree.Element: 策略XML元素
        """
        try:
            original_policy_name = policy.get("name")
            if not original_policy_name:
                log(_("security_policy.missing_policy_name"), "warning")
                return None

            # 使用名称映射管理器清理和注册策略名称
            clean_policy_name = self.name_mapping_manager.register_name_mapping(
                'policies',
                original_policy_name,
                context="安全策略"
            )

            log(f"SecurityPolicyGenerator: Starting policy element creation {clean_policy_name}", "info")
            log(f"SecurityPolicyGenerator: Policy data = {policy}", "info")

            # 创建策略元素
            policy_element = etree.Element("policy")
            policy_element.text = "\n    "  # 策略元素内部的缩进

            # 1. 添加策略名称（必需的键）
            self._add_formatted_element(policy_element, "name", clean_policy_name)

            # 2. 添加启用状态（YANG默认值：true）
            enabled = policy.get("enabled", True)
            self._add_formatted_element(policy_element, "enabled", "true" if enabled else "false")

            # 3. 添加描述（可选），并清理描述中的非法字符
            if "description" in policy and policy["description"]:
                from engine.utils.name_validator import clean_ntos_description
                original_desc = policy["description"]
                clean_desc = clean_ntos_description(original_desc, 255)

                if clean_desc:  # 只有清理后的描述不为空才添加
                    self._add_formatted_element(policy_element, "description", clean_desc)

                    # 如果描述被修改，记录日志
                    if original_desc != clean_desc:
                        log(_("security_policy.policy_description_cleaned",
                              policy_name=clean_policy_name,
                              original=original_desc,
                              cleaned=clean_desc), "info")

            # 4. 添加组名（YANG默认值：def-group）
            group_name = policy.get("group-name", "def-group")
            self._add_formatted_element(policy_element, "group-name", group_name)

            # 添加源区域 - 按照YANG模型要求生成list结构
            if "source-zone" in policy:
                log(f"SecurityPolicyGenerator: 添加源区域 = {policy['source-zone']}", "info")
                self._add_zone_element(policy_element, "source-zone", policy["source-zone"])
            else:
                log(f"SecurityPolicyGenerator: 策略中没有source-zone字段", "info")

            # 添加目标区域 - 使用YANG模型要求的dest-zone字段名，按照list结构生成
            if "dest-zone" in policy:
                log(f"SecurityPolicyGenerator: 添加目标区域 = {policy['dest-zone']}", "info")
                self._add_zone_element(policy_element, "dest-zone", policy["dest-zone"])
            elif "destination-zone" in policy:
                # 向后兼容旧字段名
                log(f"SecurityPolicyGenerator: 添加目标区域(兼容) = {policy['destination-zone']}", "info")
                self._add_zone_element(policy_element, "dest-zone", policy["destination-zone"])
            else:
                log(f"SecurityPolicyGenerator: 策略中没有dest-zone字段", "info")

            # 添加源接口 - 按照YANG模型要求的list结构生成
            if "source-interface" in policy:
                log(f"SecurityPolicyGenerator: 添加源接口 = {policy['source-interface']}", "info")
                self._add_interface_element(policy_element, "source-interface", policy["source-interface"])
            else:
                log(f"SecurityPolicyGenerator: 策略中没有source-interface字段", "info")

            # 添加目标接口 - 按照YANG模型要求的list结构生成
            if "dest-interface" in policy:
                log(f"SecurityPolicyGenerator: 添加目标接口 = {policy['dest-interface']}", "info")
                self._add_interface_element(policy_element, "dest-interface", policy["dest-interface"])
            else:
                log(f"SecurityPolicyGenerator: 策略中没有dest-interface字段", "info")

            # 添加源地址
            if "source-address" in policy:
                self._add_address_element(policy_element, "source-address", policy["source-address"])

            # 添加目标地址
            if "destination-address" in policy:
                self._add_address_element(policy_element, "destination-address", policy["destination-address"])

            # 添加源网络（支持列表格式和单个对象格式）
            if "source-network" in policy:
                self._add_address_element(policy_element, "source-network", policy["source-network"])

            # 添加目标网络（支持列表格式和单个对象格式）
            if "dest-network" in policy:
                self._add_address_element(policy_element, "dest-network", policy["dest-network"])

            # 添加服务
            if "service" in policy:
                self._add_address_element(policy_element, "service", policy["service"])

            # 添加时间范围（YANG默认值：any）
            time_range = policy.get("time-range", "any")
            self._add_formatted_element(policy_element, "time-range", time_range)

            # 按照YANG顺序：action → config-source → session-timeout

            # 添加动作（YANG默认值：permit）
            action = policy.get("action", "permit")
            log(f"SecurityPolicyGenerator: 添加动作 = {action}", "info")
            self._add_formatted_element(policy_element, "action", action)

            # 添加配置源（YANG默认值：manual）
            config_source = policy.get("config-source", "manual")
            self._add_formatted_element(policy_element, "config-source", config_source)

            # 添加会话超时（YANG默认值：0）
            session_timeout = policy.get("session-timeout", 0)
            self._add_formatted_element(policy_element, "session-timeout", str(session_timeout))

            # 添加可选的安全功能字段
            # 注意：description字段已经在前面按照YANG顺序添加了

            if "ips" in policy:
                self._add_formatted_element(policy_element, "ips", policy["ips"])

            if "av" in policy:
                self._add_formatted_element(policy_element, "av", policy["av"])

            if "url" in policy:
                self._add_formatted_element(policy_element, "url", policy["url"])

            if "websec" in policy:
                self._add_formatted_element(policy_element, "websec", policy["websec"])

            # 设置策略元素的结束格式
            policy_element.tail = "\n  "

            log(_("security_policy.policy_element_created", name=policy_name))
            return policy_element

        except Exception as e:
            log(_("security_policy.policy_creation_error",
                  name=policy.get("name", "unknown"),
                  error=str(e)), "error")
            return None

    def _add_formatted_element(self, parent: etree.Element, tag: str, text: str = None, tail: str = "\n    ") -> etree.Element:
        """
        添加格式化的XML元素

        Args:
            parent: 父元素
            tag: 标签名
            text: 元素文本内容
            tail: 元素后的格式化字符串

        Returns:
            etree.Element: 创建的元素
        """
        element = etree.SubElement(parent, tag)
        if text is not None:
            element.text = str(text)
        element.tail = tail
        return element

    def _add_zone_element(self, parent: etree.Element, tag: str, zone_data) -> None:
        """
        添加格式化的区域元素

        Args:
            parent: 父元素
            tag: 标签名（source-zone 或 dest-zone）
            zone_data: 区域数据
        """
        if isinstance(zone_data, dict):
            # 旧格式：{"name": "trust"}
            zone_element = self._add_formatted_element(parent, tag)
            zone_element.text = "\n      "  # 区域元素内部缩进
            name_element = self._add_formatted_element(zone_element, "name", zone_data.get("name", ""), "\n    ")
        elif isinstance(zone_data, list):
            # 新格式：[{"name": "trust"}, {"name": "untrust"}] 或 ["trust", "untrust"]
            for zone in zone_data:
                zone_element = self._add_formatted_element(parent, tag)
                zone_element.text = "\n      "  # 区域元素内部缩进
                if isinstance(zone, dict):
                    name_element = self._add_formatted_element(zone_element, "name", zone.get("name", ""), "\n    ")
                else:
                    name_element = self._add_formatted_element(zone_element, "name", str(zone), "\n    ")
        elif isinstance(zone_data, str):
            # 简单字符串格式："trust"
            zone_element = self._add_formatted_element(parent, tag)
            zone_element.text = "\n      "  # 区域元素内部缩进
            name_element = self._add_formatted_element(zone_element, "name", zone_data, "\n    ")

    def _add_interface_element(self, parent: etree.Element, tag: str, interface_data) -> None:
        """
        添加格式化的接口元素

        Args:
            parent: 父元素
            tag: 标签名（source-interface 或 dest-interface）
            interface_data: 接口数据
        """
        if isinstance(interface_data, list):
            # 多个接口，每个接口都是一个独立的interface元素
            for intf in interface_data:
                intf_element = self._add_formatted_element(parent, tag)
                intf_element.text = "\n      "  # 接口元素内部缩进
                if isinstance(intf, dict):
                    name_element = self._add_formatted_element(intf_element, "name", intf.get("name", ""), "\n    ")
                else:
                    name_element = self._add_formatted_element(intf_element, "name", str(intf), "\n    ")
        elif isinstance(interface_data, dict):
            # 单个接口，也需要按照list结构生成
            intf_element = self._add_formatted_element(parent, tag)
            intf_element.text = "\n      "  # 接口元素内部缩进
            name_element = self._add_formatted_element(intf_element, "name", interface_data.get("name", ""), "\n    ")
        elif isinstance(interface_data, str):
            # 简单字符串格式
            intf_element = self._add_formatted_element(parent, tag)
            intf_element.text = "\n      "  # 接口元素内部缩进
            name_element = self._add_formatted_element(intf_element, "name", interface_data, "\n    ")

    def _add_address_element(self, parent: etree.Element, tag: str, address_data) -> None:
        """
        添加格式化的地址元素

        Args:
            parent: 父元素
            tag: 标签名（source-address, dest-address, source-network, dest-network）
            address_data: 地址数据
        """
        if isinstance(address_data, list):
            # 多个地址
            for addr in address_data:
                addr_element = self._add_formatted_element(parent, tag)
                addr_element.text = "\n      "  # 地址元素内部缩进
                if isinstance(addr, dict):
                    name_element = self._add_formatted_element(addr_element, "name", addr.get("name", ""), "\n    ")
                else:
                    name_element = self._add_formatted_element(addr_element, "name", str(addr), "\n    ")
        elif isinstance(address_data, dict):
            # 单个地址（字典格式）
            addr_element = self._add_formatted_element(parent, tag)
            addr_element.text = "\n      "  # 地址元素内部缩进
            name_element = self._add_formatted_element(addr_element, "name", address_data.get("name", ""), "\n    ")
        elif isinstance(address_data, str):
            # 简单字符串格式
            addr_element = self._add_formatted_element(parent, tag)
            addr_element.text = "\n      "  # 地址元素内部缩进
            name_element = self._add_formatted_element(addr_element, "name", address_data, "\n    ")

    def _add_zone_element(self, parent: etree.Element, tag: str, zone_data) -> None:
        """
        添加格式化的区域元素

        Args:
            parent: 父元素
            tag: 标签名（source-zone 或 dest-zone）
            zone_data: 区域数据
        """
        if isinstance(zone_data, dict):
            # 旧格式：{"name": "trust"}
            zone_element = self._add_formatted_element(parent, tag)
            zone_element.text = "\n      "  # 区域元素内部缩进
            name_element = self._add_formatted_element(zone_element, "name", zone_data.get("name", ""), "\n    ")
        elif isinstance(zone_data, list):
            # 新格式：[{"name": "trust"}, {"name": "untrust"}] 或 ["trust", "untrust"]
            for zone in zone_data:
                zone_element = self._add_formatted_element(parent, tag)
                zone_element.text = "\n      "  # 区域元素内部缩进
                if isinstance(zone, dict):
                    name_element = self._add_formatted_element(zone_element, "name", zone.get("name", ""), "\n    ")
                else:
                    name_element = self._add_formatted_element(zone_element, "name", str(zone), "\n    ")
        elif isinstance(zone_data, str):
            # 简单字符串格式："trust"
            zone_element = self._add_formatted_element(parent, tag)
            zone_element.text = "\n      "  # 区域元素内部缩进
            name_element = self._add_formatted_element(zone_element, "name", zone_data, "\n    ")

    def _add_interface_element(self, parent: etree.Element, tag: str, interface_data) -> None:
        """
        添加格式化的接口元素

        Args:
            parent: 父元素
            tag: 标签名（source-interface 或 dest-interface）
            interface_data: 接口数据
        """
        if isinstance(interface_data, list):
            # 多个接口，每个接口都是一个独立的interface元素
            for intf in interface_data:
                intf_element = self._add_formatted_element(parent, tag)
                intf_element.text = "\n      "  # 接口元素内部缩进
                if isinstance(intf, dict):
                    name_element = self._add_formatted_element(intf_element, "name", intf.get("name", ""), "\n    ")
                else:
                    name_element = self._add_formatted_element(intf_element, "name", str(intf), "\n    ")
        elif isinstance(interface_data, dict):
            # 单个接口，也需要按照list结构生成
            intf_element = self._add_formatted_element(parent, tag)
            intf_element.text = "\n      "  # 接口元素内部缩进
            name_element = self._add_formatted_element(intf_element, "name", interface_data.get("name", ""), "\n    ")
        elif isinstance(interface_data, str):
            # 简单字符串格式
            intf_element = self._add_formatted_element(parent, tag)
            intf_element.text = "\n      "  # 接口元素内部缩进
            name_element = self._add_formatted_element(intf_element, "name", interface_data, "\n    ")

    def _add_address_element(self, parent: etree.Element, tag: str, address_data) -> None:
        """
        添加格式化的地址元素

        Args:
            parent: 父元素
            tag: 标签名（source-address, dest-address, source-network, dest-network）
            address_data: 地址数据
        """
        if isinstance(address_data, list):
            # 多个地址
            for addr in address_data:
                addr_element = self._add_formatted_element(parent, tag)
                addr_element.text = "\n      "  # 地址元素内部缩进
                if isinstance(addr, dict):
                    name_element = self._add_formatted_element(addr_element, "name", addr.get("name", ""), "\n    ")
                else:
                    name_element = self._add_formatted_element(addr_element, "name", str(addr), "\n    ")
        elif isinstance(address_data, dict):
            # 单个地址（字典格式）
            addr_element = self._add_formatted_element(parent, tag)
            addr_element.text = "\n      "  # 地址元素内部缩进
            name_element = self._add_formatted_element(addr_element, "name", address_data.get("name", ""), "\n    ")
        elif isinstance(address_data, str):
            # 简单字符串格式
            addr_element = self._add_formatted_element(parent, tag)
            addr_element.text = "\n      "  # 地址元素内部缩进
            name_element = self._add_formatted_element(addr_element, "name", address_data, "\n    ")
    
    def validate_security_policy_xml(self, security_policy_element: etree.Element) -> Dict[str, Any]:
        """
        验证安全策略XML的有效性
        
        Args:
            security_policy_element: 安全策略XML元素
            
        Returns:
            dict: 验证结果
                {
                    "valid": bool,
                    "errors": [错误列表],
                    "warnings": [警告列表]
                }
        """
        validation_result = {
            "valid": True,
            "errors": [],
            "warnings": []
        }
        
        try:
            # 检查命名空间
            if security_policy_element.get("xmlns") != self.security_policy_namespace:
                validation_result["warnings"].append(
                    _("security_policy.validation.missing_namespace")
                )
            
            # 检查策略元素
            policies = security_policy_element.findall(".//policy")
            if not policies:
                validation_result["warnings"].append(
                    _("security_policy.validation.no_policies")
                )
            
            # 验证每个策略
            for policy in policies:
                policy_validation = self._validate_single_policy(policy)
                validation_result["errors"].extend(policy_validation["errors"])
                validation_result["warnings"].extend(policy_validation["warnings"])
            
            # 如果有错误，标记为无效
            if validation_result["errors"]:
                validation_result["valid"] = False
            
        except Exception as e:
            validation_result["valid"] = False
            validation_result["errors"].append(
                _("security_policy.validation.exception", error=str(e))
            )
        
        return validation_result
    
    def _validate_single_policy(self, policy_element: etree.Element) -> Dict[str, Any]:
        """
        验证单个策略元素
        
        Args:
            policy_element: 策略XML元素
            
        Returns:
            dict: 验证结果
        """
        result = {"errors": [], "warnings": []}
        
        # 检查必需字段
        required_fields = ["name", "action"]
        for field in required_fields:
            field_element = policy_element.find(field)
            if field_element is None or not field_element.text:
                result["errors"].append(
                    _("security_policy.validation.missing_required_field", field=field)
                )
        
        # 检查动作值
        action_element = policy_element.find("action")
        if action_element is not None:
            action_value = action_element.text
            if action_value not in ["permit", "deny"]:
                result["errors"].append(
                    _("security_policy.validation.invalid_action", action=action_value)
                )
        
        return result
    
    def convert_policy_to_xml_dict(self, policy: Dict) -> Dict:
        """
        将策略配置转换为XML字典格式

        Args:
            policy: 策略配置字典

        Returns:
            Dict: XML字典格式的策略
        """
        try:
            # 创建策略元素
            policy_element = self._create_policy_element(policy)
            if policy_element is None:
                return None

            # 将XML元素转换为字典格式
            policy_dict = self._xml_element_to_dict(policy_element)

            log(_("security_policy.policy_converted_to_dict", name=policy.get("name", "unknown")))
            return policy_dict

        except Exception as e:
            log(_("security_policy.policy_conversion_to_dict_failed",
                  name=policy.get("name", "unknown"), error=str(e)), "error")
            return None

    def _xml_element_to_dict(self, element: etree.Element) -> Dict:
        """
        将XML元素转换为字典格式

        Args:
            element: XML元素

        Returns:
            Dict: 字典格式的数据
        """
        result = {}

        # 添加元素标签
        result['tag'] = element.tag

        # 添加属性
        if element.attrib:
            result['attributes'] = dict(element.attrib)

        # 添加文本内容
        if element.text and element.text.strip():
            result['text'] = element.text.strip()

        # 添加子元素
        children = []
        for child in element:
            child_dict = self._xml_element_to_dict(child)
            children.append(child_dict)

        if children:
            result['children'] = children

        return result

    def get_security_policy_statistics(self, security_policy_element: etree.Element) -> Dict[str, int]:
        """
        获取安全策略统计信息

        Args:
            security_policy_element: 安全策略XML元素
            
        Returns:
            dict: 统计信息
        """
        stats = {
            "total_policies": 0,
            "permit_policies": 0,
            "deny_policies": 0
        }
        
        policies = security_policy_element.findall(".//policy")
        stats["total_policies"] = len(policies)
        
        for policy in policies:
            action_element = policy.find("action")
            if action_element is not None:
                action = action_element.text
                if action == "permit":
                    stats["permit_policies"] += 1
                elif action == "deny":
                    stats["deny_policies"] += 1
        
        return stats
