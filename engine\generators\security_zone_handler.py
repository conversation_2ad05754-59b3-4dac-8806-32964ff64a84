from lxml import etree
from engine.utils.logger import log
from engine.utils.i18n import _  # 导入国际化函数
from engine.generators.xml_utils import NS_SECURITY_ZONE, find_or_create_child_with_ns, NamespaceManager

class SecurityZoneHandler:
    """
    安全区域处理类，用于管理安全区域配置
    """
    
    def __init__(self, ns_manager=None):
        """
        初始化安全区域处理器
        
        Args:
            ns_manager: 命名空间管理器实例，如果为None则创建一个新实例
        """
        self.ns_manager = ns_manager if ns_manager else NamespaceManager()
    
    def add_zones_to_xml(self, security_zone_elem, zones_data):
        """
        将安全区域配置添加到XML中，确保符合YANG模型规范
        
        原则：
        1. 如果安全区域节点已存在，更新现有节点
        2. 如果安全区域节点不存在，创建新的安全区域节点
        3. 确保子节点使用正确的命名空间
        
        Args:
            security_zone_elem: 安全区域元素
            zones_data: 区域数据列表
        """
        log(_("security_zone_handler.adding_zones_to_xml", count=len(zones_data)))
        
        # 收集现有安全区域配置的信息，使用区域名称作为键
        existing_zones = {}
        for zone_elem in security_zone_elem.findall(".//zone"):
            name_elem = zone_elem.find("./name")
            if name_elem is not None and name_elem.text:
                existing_zones[name_elem.text] = zone_elem
                log(_("security_zone_handler.found_existing_zone", name=name_elem.text))
        
        # 处理每个安全区域
        for zone in zones_data:
            # 确保有区域名称
            if "name" not in zone:
                log(_("security_zone_handler.skip_zone_without_name"), "warning")
                continue
            
            # 检查是否已存在此名称的安全区域
            if zone["name"] in existing_zones:
                # 更新现有安全区域
                log(_("security_zone_handler.update_existing_zone", name=zone["name"]))
                self.update_existing_zone(existing_zones[zone["name"]], zone)
            else:
                # 创建新的安全区域
                log(_("security_zone_handler.create_new_zone", name=zone["name"]))
                self.create_new_zone(security_zone_elem, zone)
        
        log(_("security_zone_handler.zones_added_to_xml"))

    def update_existing_zone(self, zone_elem, zone_data):
        """
        更新现有安全区域配置
        
        Args:
            zone_elem: 安全区域元素
            zone_data: 安全区域数据
        """
        log(_("security_zone_handler.updating_zone", name=zone_data["name"]))
        
        # 更新区域名称（确保一致）
        name_elem = zone_elem.find("./name")
        if name_elem is not None:
            name_elem.text = zone_data["name"]
        else:
            etree.SubElement(zone_elem, "name").text = zone_data["name"]
        
        # 更新描述
        if "description" in zone_data:
            desc_elem = zone_elem.find("./description")
            if desc_elem is not None:
                desc_elem.text = zone_data["description"]
            else:
                etree.SubElement(zone_elem, "description").text = zone_data["description"]
        
        # 更新优先级
        if "priority" in zone_data:
            priority_elem = zone_elem.find("./priority")
            if priority_elem is not None:
                priority_elem.text = str(zone_data["priority"])
            else:
                etree.SubElement(zone_elem, "priority").text = str(zone_data["priority"])
        
        # 更新接口配置（如果有）
        if "interfaces" in zone_data and zone_data["interfaces"]:
            # 收集现有接口
            existing_interfaces = set()
            for intf_elem in zone_elem.findall("./interface"):
                intf_name_elem = intf_elem.find("./name")
                if intf_name_elem is not None and intf_name_elem.text:
                    existing_interfaces.add(intf_name_elem.text)
            
            # 添加新接口（不存在的）
            for intf_name in zone_data["interfaces"]:
                if intf_name not in existing_interfaces:
                    # 创建interface节点
                    interface_elem = etree.SubElement(zone_elem, "interface")
                    # 设置接口节点的命名空间 - 确保正确设置
                    interface_elem.set("xmlns", NS_SECURITY_ZONE)
                    etree.SubElement(interface_elem, "name").text = intf_name
                    log(_("security_zone_handler.add_interface_to_zone", interface=intf_name, zone=zone_data["name"]))
        
        log(_("security_zone_handler.zone_update_complete", name=zone_data["name"]))

    def create_new_zone(self, security_zone_elem, zone_data):
        """
        创建新的安全区域配置
        
        Args:
            security_zone_elem: 安全区域父元素
            zone_data: 安全区域数据
        """
        # 创建zone节点
        zone_elem = etree.SubElement(security_zone_elem, "zone")
        
        # 设置区域名称 - 使用name标签而不是n标签
        etree.SubElement(zone_elem, "name").text = zone_data["name"]
        
        # 设置描述（可选）
        if "description" in zone_data:
            etree.SubElement(zone_elem, "description").text = zone_data["description"]
        
        # 设置优先级（可选，默认为50）
        priority = zone_data.get("priority", "50")
        etree.SubElement(zone_elem, "priority").text = str(priority)
        
        # 添加接口配置（如果有）
        if "interfaces" in zone_data and zone_data["interfaces"]:
            for intf_name in zone_data["interfaces"]:
                # 创建interface节点
                interface_elem = etree.SubElement(zone_elem, "interface")
                # 设置接口节点的命名空间 - 确保正确设置
                interface_elem.set("xmlns", NS_SECURITY_ZONE)
                etree.SubElement(interface_elem, "name").text = intf_name
                log(_("security_zone_handler.add_interface_to_zone", interface=intf_name, zone=zone_data["name"]))
        
        log(_("security_zone_handler.zone_creation_success", name=zone_data["name"]))
        return zone_elem

    def create_security_zone_config(self, vrf_elem, zone_data):
        """
        创建安全区域配置
        
        Args:
            vrf_elem: VRF元素
            zone_data: 区域数据
            
        Returns:
            bool: 是否成功创建区域
        """
        if not isinstance(zone_data, dict) or "name" not in zone_data:
            log(_("security_zone_handler.error.invalid_zone_data_format", data=zone_data), "error")
            return False
        
        # 检查security-zone节点是否存在
        security_zone_elem = self.ns_manager.find_or_create_child_with_ns(vrf_elem, "security-zone", NS_SECURITY_ZONE)
        
        # 检查是否已存在同名区域
        for zone_elem in security_zone_elem.findall(".//zone"):
            name_elem = zone_elem.find("./name")
            if name_elem is not None and name_elem.text == zone_data["name"]:
                log(_("security_zone_handler.zone_already_exists", name=zone_data["name"]))
                # 更新现有区域
                self.update_existing_zone(zone_elem, zone_data)
                return True
        
        # 创建新区域
        self.create_new_zone(security_zone_elem, zone_data)
        return True

# 为了保持向后兼容性，创建一个默认的安全区域处理器实例
_default_security_zone_handler = SecurityZoneHandler()

# 向后兼容的函数，调用默认安全区域处理器的方法
def add_zones_to_xml(security_zone_elem, zones_data):
    """向后兼容的添加安全区域到XML函数"""
    return _default_security_zone_handler.add_zones_to_xml(security_zone_elem, zones_data)

def update_existing_zone(zone_elem, zone_data):
    """向后兼容的更新现有安全区域函数"""
    return _default_security_zone_handler.update_existing_zone(zone_elem, zone_data)

def create_new_zone(security_zone_elem, zone_data):
    """向后兼容的创建新安全区域函数"""
    return _default_security_zone_handler.create_new_zone(security_zone_elem, zone_data)

def create_security_zone_config(vrf_elem, zone_data):
    """向后兼容的创建安全区域配置函数"""
    return _default_security_zone_handler.create_security_zone_config(vrf_elem, zone_data) 