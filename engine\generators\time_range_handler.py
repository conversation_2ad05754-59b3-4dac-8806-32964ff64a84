#!/usr/bin/env python
# -*- coding: utf-8 -*-

from lxml import etree
from engine.utils.logger import log
from engine.utils.i18n import _

# 时间对象的命名空间
NS_TIME_RANGE = "urn:ruijie:ntos:params:xml:ns:yang:time-range"

class TimeRangeHandler:
    """
    时间对象处理类，用于将时间对象添加到XML中
    """
    
    def __init__(self, ns_manager=None):
        """初始化时间对象处理器"""
        self.ns_manager = ns_manager
    
    def add_time_ranges_to_xml(self, vrf_elem, time_ranges):
        """
        将时间对象添加到XML中
        
        Args:
            vrf_elem: VRF XML元素
            time_ranges: 时间对象列表
        """
        if not time_ranges:
            log(_("time_range.no_time_ranges_to_add"))
            return
        
        log(_("time_range.adding_time_ranges", count=len(time_ranges)))
        
        # 获取或创建time-range节点
        time_range_elem = vrf_elem.find(".//time-range")
        if time_range_elem is None:
            time_range_elem = etree.SubElement(vrf_elem, "time-range")
            time_range_elem.set("xmlns", NS_TIME_RANGE)
            log(_("time_range.create_time_range_node"))
        
        # 处理VRF中的<n>标签问题
        self._fix_vrf_name_tag(vrf_elem)
        
        # 处理每个时间对象
        for time_range in time_ranges:
            self._add_single_time_range(time_range_elem, time_range)
    
    def _fix_vrf_name_tag(self, vrf_elem):
        """
        修复VRF元素中的<n>标签问题，替换为<name>
        
        Args:
            vrf_elem: VRF XML元素
        """
        # 检查VRF中是否存在<n>标签
        n_elem = vrf_elem.find("./n")
        if n_elem is not None:
            # 获取值
            value = n_elem.text
            # 删除<n>标签
            vrf_elem.remove(n_elem)
            # 创建<name>标签
            name_elem = etree.SubElement(vrf_elem, "name")
            name_elem.text = value
            log(_("time_range.fixed_vrf_name_tag"))
    
    def _add_single_time_range(self, time_range_elem, time_range):
        """
        添加单个时间对象
        
        Args:
            time_range_elem: 时间对象XML父节点
            time_range: 时间对象数据
        """
        # 检查必要的字段
        if "name" not in time_range:
            log(_("time_range.missing_name"), "warning")
            return
        
        name = time_range["name"]
        log(_("time_range.processing", name=name))
        
        # 查找是否已存在同名时间对象
        existing_range = None
        for range_elem in time_range_elem.findall("./range"):
            name_elem = range_elem.find("./name")
            # 同时检查 name 和 n 标签（兼容旧版本）
            if name_elem is None:
                name_elem = range_elem.find("./n")
            
            if name_elem is not None and name_elem.text == name:
                existing_range = range_elem
                log(_("time_range.found_existing", name=name))
                break
        
        # 如果不存在，创建新的时间对象
        if existing_range is None:
            range_elem = etree.SubElement(time_range_elem, "range")
            name_elem = etree.SubElement(range_elem, "name")
            name_elem.text = name
            log(_("time_range.create_new", name=name))
        else:
            range_elem = existing_range
            
            # 确保使用正确的 name 标签
            name_elem = range_elem.find("./name")
            if name_elem is None:
                # 如果只有 n 标签，删除它并创建 name 标签
                n_elem = range_elem.find("./n")
                if n_elem is not None:
                    range_elem.remove(n_elem)
                name_elem = etree.SubElement(range_elem, "name")
                name_elem.text = name
            
            log(_("time_range.update_existing", name=name))
        
        # 添加描述（如果有）
        if "description" in time_range:
            desc_elem = range_elem.find("./description")
            if desc_elem is None:
                desc_elem = etree.SubElement(range_elem, "description")
            desc_elem.text = time_range["description"]
        
        # 删除可能存在的<type>元素，YANG schema不需要这个元素
        type_elem = range_elem.find("./type")
        if type_elem is not None:
            range_elem.remove(type_elem)
        
        # 处理单次计划
        if "once" in time_range:
            # 删除可能存在的period节点
            for period_elem in range_elem.findall("./period"):
                range_elem.remove(period_elem)
            
            # 删除可能同级的start和end元素
            for elem_name in ["start", "end"]:
                for elem in range_elem.findall(f"./{elem_name}"):
                    range_elem.remove(elem)
            
            # 创建或更新once节点
            once_elem = range_elem.find("./once")
            if once_elem is None:
                once_elem = etree.SubElement(range_elem, "once")
            
            # 添加起止时间
            if "start" in time_range["once"]:
                start_elem = once_elem.find("./start")
                if start_elem is None:
                    start_elem = etree.SubElement(once_elem, "start")
                start_elem.text = time_range["once"]["start"]
            
            if "end" in time_range["once"]:
                end_elem = once_elem.find("./end")
                if end_elem is None:
                    end_elem = etree.SubElement(once_elem, "end")
                end_elem.text = time_range["once"]["end"]
        
        # 处理周期性计划
        elif "period" in time_range:
            # 删除可能存在的once节点
            for once_elem in range_elem.findall("./once"):
                range_elem.remove(once_elem)
            
            # 删除可能同级的start和end元素
            for elem_name in ["start", "end"]:
                for elem in range_elem.findall(f"./{elem_name}"):
                    range_elem.remove(elem)
            
            # 创建或更新period节点
            period_elem = range_elem.find("./period")
            if period_elem is None:
                period_elem = etree.SubElement(range_elem, "period")
            
            # 添加起止时间
            if "start" in time_range["period"]:
                start_elem = period_elem.find("./start")
                if start_elem is None:
                    start_elem = etree.SubElement(period_elem, "start")
                start_elem.text = time_range["period"]["start"]
            
            if "end" in time_range["period"]:
                end_elem = period_elem.find("./end")
                if end_elem is None:
                    end_elem = etree.SubElement(period_elem, "end")
                end_elem.text = time_range["period"]["end"]
            
            # 处理星期几
            if "weekday" in time_range["period"]:
                # 删除所有现有的weekday节点
                for weekday_elem in period_elem.findall("./weekday"):
                    period_elem.remove(weekday_elem)
                
                # 添加新的weekday节点
                for weekday in time_range["period"]["weekday"]:
                    weekday_elem = etree.SubElement(period_elem, "weekday")
                    key_elem = etree.SubElement(weekday_elem, "key")
                    key_elem.text = weekday["key"]
        
        # 处理全局起止日期，确保它们被移动到正确的位置
        # 先移除这些可能存在的同级元素
        for global_date_elem in range_elem.findall("./start"):
            range_elem.remove(global_date_elem)
        for global_date_elem in range_elem.findall("./end"):
            range_elem.remove(global_date_elem)
        
        # 处理禁用状态（对于 "none" 类型的时间对象）
        if "disabled" in time_range and time_range["disabled"]:
            range_elem.set("disabled", "true")
            log(_("time_range.set_disabled", name=name))
        elif range_elem.get("disabled") == "true":
            # 移除可能存在的 disabled 属性
            del range_elem.attrib["disabled"]

# 为了便于使用，提供一个独立的函数
def add_time_ranges_to_xml(vrf_elem, time_ranges):
    """
    将时间对象添加到XML中
    
    Args:
        vrf_elem: VRF XML元素
        time_ranges: 时间对象列表
    """
    handler = TimeRangeHandler()
    handler.add_time_ranges_to_xml(vrf_elem, time_ranges) 