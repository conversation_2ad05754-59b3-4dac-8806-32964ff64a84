#!/usr/bin/env python
# -*- coding: utf-8 -*-

from engine.utils.logger import log
from engine.utils.i18n import _


class ConfigDataValidator:
    """配置数据验证器"""
    
    @staticmethod
    def validate_interfaces(interfaces):
        """
        验证接口配置数据
        
        Args:
            interfaces (dict|list): 接口配置数据
            
        Returns:
            tuple: (是否有效, 错误消息)
        """
        if not interfaces:
            return True, ""
        
        # 如果是列表，检查每个元素
        if isinstance(interfaces, list):
            for i, intf in enumerate(interfaces):
                if not isinstance(intf, dict):
                    return False, f"接口 #{i+1} 不是字典类型"
                if "name" not in intf:
                    return False, f"接口 #{i+1} 缺少name字段"
            return True, ""
        
        # 如果是字典，检查每个值
        if isinstance(interfaces, dict):
            for name, intf in interfaces.items():
                if intf is not None and not isinstance(intf, dict):
                    return False, f"接口 {name} 的值不是字典类型"
            return True, ""
        
        return False, "接口数据既不是列表也不是字典类型"
    
    @staticmethod
    def validate_zones(zones):
        """
        验证区域数据
        
        Args:
            zones (list): 区域列表
            
        Returns:
            tuple: (是否有效, 错误消息)
        """
        if not zones:
            return True, ""
        
        if not isinstance(zones, list):
            return False, "区域数据不是列表类型"
        
        for i, zone in enumerate(zones):
            if not isinstance(zone, dict):
                return False, f"区域 #{i+1} 不是字典类型"
            if "name" not in zone:
                return False, f"区域 #{i+1} 缺少name字段"
        
        return True, ""
    
    @staticmethod
    def validate_address_objects(address_objects):
        """
        验证地址对象数据
        
        Args:
            address_objects (list): 地址对象列表
            
        Returns:
            tuple: (是否有效, 错误消息)
        """
        if not address_objects:
            return True, ""
        
        if not isinstance(address_objects, list):
            return False, "地址对象数据不是列表类型"
        
        for i, obj in enumerate(address_objects):
            if not isinstance(obj, dict):
                return False, f"地址对象 #{i+1} 不是字典类型"
            if "name" not in obj:
                return False, f"地址对象 #{i+1} 缺少name字段"
        
        return True, ""
    
    @staticmethod
    def validate_service_objects(service_objects):
        """
        验证服务对象数据
        
        Args:
            service_objects (list): 服务对象列表
            
        Returns:
            tuple: (是否有效, 错误消息)
        """
        if not service_objects:
            return True, ""
        
        if not isinstance(service_objects, list):
            return False, "服务对象数据不是列表类型"
        
        for i, obj in enumerate(service_objects):
            if not isinstance(obj, dict):
                return False, f"服务对象 #{i+1} 不是字典类型"
            if "name" not in obj:
                return False, f"服务对象 #{i+1} 缺少name字段"
        
        return True, ""
    
    @staticmethod
    def validate_policy_rules(policy_rules):
        """
        验证策略规则数据
        
        Args:
            policy_rules (list): 策略规则列表
            
        Returns:
            tuple: (是否有效, 错误消息)
        """
        if not policy_rules:
            return True, ""
        
        if not isinstance(policy_rules, list):
            return False, "策略规则数据不是列表类型"
        
        for i, rule in enumerate(policy_rules):
            if not isinstance(rule, dict):
                return False, f"策略规则 #{i+1} 不是字典类型"
            if "name" not in rule:
                return False, f"策略规则 #{i+1} 缺少name字段"
        
        return True, ""
    
    @staticmethod
    def validate_static_routes(static_routes):
        """
        验证静态路由数据
        
        Args:
            static_routes (list): 静态路由列表
            
        Returns:
            tuple: (是否有效, 错误消息)
        """
        if not static_routes:
            return True, ""
        
        if not isinstance(static_routes, list):
            return False, "静态路由数据不是列表类型"
        
        for i, route in enumerate(static_routes):
            if not isinstance(route, dict):
                return False, f"静态路由 #{i+1} 不是字典类型"
        
        return True, ""
    
    @staticmethod
    def validate_address_groups(address_groups):
        """
        验证地址组数据
        
        Args:
            address_groups (list): 地址组列表
            
        Returns:
            tuple: (是否有效, 错误消息)
        """
        if not address_groups:
            return True, ""
        
        if not isinstance(address_groups, list):
            return False, "地址组数据不是列表类型"
        
        for i, group in enumerate(address_groups):
            if not isinstance(group, dict):
                return False, f"地址组 #{i+1} 不是字典类型"
            if "name" not in group:
                return False, f"地址组 #{i+1} 缺少name字段"
        
        return True, ""
    
    @staticmethod
    def validate_service_groups(service_groups):
        """
        验证服务组数据
        
        Args:
            service_groups (list): 服务组列表
            
        Returns:
            tuple: (是否有效, 错误消息)
        """
        if not service_groups:
            return True, ""
        
        if not isinstance(service_groups, list):
            return False, "服务组数据不是列表类型"
        
        for i, group in enumerate(service_groups):
            if not isinstance(group, dict):
                return False, f"服务组 #{i+1} 不是字典类型"
            if "name" not in group:
                return False, f"服务组 #{i+1} 缺少name字段"
        
        return True, ""