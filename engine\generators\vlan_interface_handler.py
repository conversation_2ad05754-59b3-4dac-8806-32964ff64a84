from lxml import etree
import random
import string
from engine.utils.logger import log
from engine.utils.i18n import _  # 导入国际化函数
from engine.generators.xml_utils import (
    NS_VLAN, NS_MONITOR, NS_IPMAC
)
from engine.generators.interface_common import InterfaceCommon

class VlanInterfaceHandler:
    """
    VLAN接口处理类，用于管理VLAN接口配置
    符合NTOS YANG模型要求
    """
    
    def __init__(self, interface_common=None):
        """
        初始化VLAN接口处理器
        
        Args:
            interface_common: 接口公共处理器实例，如果为None则创建一个新实例
        """
        self.interface_common = interface_common if interface_common else InterfaceCommon()
    
    def _validate_vlan_id(self, vlan_id):
        """
        验证VLAN ID范围（1-4063）
        
        Args:
            vlan_id: VLAN ID值
            
        Returns:
            bool: 是否有效
        """
        try:
            vid = int(vlan_id)
            return 1 <= vid <= 4063
        except (ValueError, TypeError):
            return False
    
    def _generate_vlan_interface_name(self, parent_interface, vlan_id):
        """
        生成VLAN子接口名称（parent_interface.vlan_id）
        
        Args:
            parent_interface: 父接口名称
            vlan_id: VLAN ID
            
        Returns:
            str: VLAN子接口名称
        """
        return f"{parent_interface}.{vlan_id}"
    
    def _extract_parent_and_vlan_from_name(self, interface_name):
        """
        从接口名称中提取父接口和VLAN ID
        
        Args:
            interface_name: 接口名称（如 "Ge0/0.100"）
            
        Returns:
            tuple: (parent_interface, vlan_id) 或 (None, None)
        """
        if "." in interface_name:
            parts = interface_name.split(".")
            if len(parts) == 2:
                parent_interface = parts[0]
                try:
                    vlan_id = int(parts[1])
                    if self._validate_vlan_id(vlan_id):
                        return parent_interface, vlan_id
                except ValueError:
                    pass
        return None, None
    
    def _map_parent_interface(self, parent_interface, interface_mapping):
        """
        映射父接口名称从FortiGate格式到NTOS格式
        
        Args:
            parent_interface: 父接口名称（可能是FortiGate格式，如port3）
            interface_mapping: 接口映射表
            
        Returns:
            str: 映射后的NTOS接口名称（如Ge0/3）
        """
        # 确保输入是字符串
        if isinstance(parent_interface, dict):
            if "name" in parent_interface:
                parent_interface = parent_interface["name"]
            elif "interface" in parent_interface:
                parent_interface = parent_interface["interface"]
            else:
                parent_interface = list(parent_interface.values())[0] if parent_interface else "unknown"
        
        parent_interface = str(parent_interface)
        
        if interface_mapping:
            # 使用接口公共处理器的映射功能
            mapped_name = self.interface_common.get_ntos_interface_name(parent_interface, interface_mapping)
            if mapped_name != parent_interface:
                log(_("interface_handler.parent_interface_mapped", 
                      original=parent_interface, mapped=mapped_name))
                return mapped_name
        
        # 如果没有映射或映射失败，返回原始名称
        return parent_interface
    
    def create_new_vlan_interface(self, interface_elem, intf_data, interface_mapping=None):
        """
        创建新的VLAN子接口，符合NTOS YANG模型要求
        
        Args:
            interface_elem: 父接口元素
            intf_data: 接口数据
            interface_mapping: 接口映射表，用于将FortiGate接口名称映射为NTOS名称
        """
        log(_("interface_handler.create_new_vlan_interface", name=intf_data.get("name", "unknown")))
        
        # 创建vlan节点
        vlan_elem = etree.SubElement(interface_elem, "vlan")
        # 设置命名空间（检查是否已存在，避免重复设置）
        if not vlan_elem.get("xmlns"):
            vlan_elem.set("xmlns", NS_VLAN)
        
        # 确定接口名称和VLAN配置
        interface_name = intf_data.get("name", "")
        vlan_id = intf_data.get("vlanid")
        # FortiGate使用"interface"字段来指定父接口，也支持"parent_interface"
        parent_interface = intf_data.get("parent_interface") or intf_data.get("interface")
        
        # 尝试从接口名称中提取父接口和VLAN ID
        extracted_parent, extracted_vlan = self._extract_parent_and_vlan_from_name(interface_name)
        
        # 使用提取的或提供的值
        if extracted_parent and extracted_vlan:
            # 映射父接口名称
            mapped_parent = self._map_parent_interface(extracted_parent, interface_mapping)
            final_parent = mapped_parent
            final_vlan_id = extracted_vlan
            # 使用映射后的父接口名称重新生成子接口名称
            final_name = self._generate_vlan_interface_name(mapped_parent, extracted_vlan)
            log(_("interface_handler.mapped_parent_interface", 
                  original=extracted_parent, mapped=mapped_parent, subinterface=final_name))
        else:
            # 如果无法从名称提取，使用提供的数据
            if parent_interface:
                if isinstance(parent_interface, dict):
                    if "name" in parent_interface:
                        raw_parent = parent_interface["name"]
                    elif "interface" in parent_interface:
                        raw_parent = parent_interface["interface"]
                    else:
                        raw_parent = list(parent_interface.values())[0] if parent_interface else "unknown"
                else:
                    raw_parent = str(parent_interface)
                
                # 映射父接口名称
                mapped_parent = self._map_parent_interface(raw_parent, interface_mapping)
                final_parent = mapped_parent
            else:
                final_parent = "unknown"
            
            final_vlan_id = vlan_id if vlan_id else 1
            # 生成标准的VLAN接口名称，使用映射后的父接口名称
            final_name = self._generate_vlan_interface_name(final_parent, final_vlan_id)
        
        # 验证VLAN ID
        if not self._validate_vlan_id(final_vlan_id):
            log(_("interface_handler.invalid_vlan_id", vlan_id=final_vlan_id, name=final_name), "warning")
            final_vlan_id = 1  # 使用默认值
        
        # 设置接口名称
        etree.SubElement(vlan_elem, "name").text = final_name
        
        # 设置描述（必需的空元素）
        description_elem = etree.SubElement(vlan_elem, "description")
        if "description" in intf_data:
            description_elem.text = intf_data["description"]
        
        # 设置enabled状态（默认为true）
        enabled_text = "true"
        if "status" in intf_data:
            enabled_text = "true" if intf_data["status"].lower() == "up" else "false"
        etree.SubElement(vlan_elem, "enabled").text = enabled_text
        
        # 设置IP配置
        self._configure_ip_settings(vlan_elem, intf_data)
        
        # 检查是否为PPPoE模式，如果是，确保配置了PPPoE
        if intf_data.get("mode", "").lower() == "pppoe":
            # 检查PPPoE配置是否已经添加
            ipv4_elem = vlan_elem.find("ipv4")
            if ipv4_elem is not None and ipv4_elem.find("pppoe") is None:
                log(_("interface_handler.adding_pppoe_config", interface=final_name))
                self._configure_pppoe_settings(vlan_elem, intf_data)
        
        # 设置IPv6配置（按照用户提供的正确结构）
        self._configure_ipv6_settings(vlan_elem, intf_data)

        # 设置网络堆栈配置（按照用户提供的正确结构）
        self._configure_network_stack(vlan_elem, intf_data)
        
        # 设置反向路径配置（默认为true）
        reverse_path_text = "true"
        if "reverse_path" in intf_data:
            reverse_path_text = str(intf_data["reverse_path"]).lower()
        etree.SubElement(vlan_elem, "reverse-path").text = reverse_path_text
        
        # 设置VLAN ID（必须字段）
        etree.SubElement(vlan_elem, "vlan-id").text = str(final_vlan_id)
        
        # 设置link-interface（必须字段，符合YANG模型）
        etree.SubElement(vlan_elem, "link-interface").text = final_parent
        
        # 设置protocol（默认为802.1q）
        protocol = intf_data.get("protocol", "802.1q")
        etree.SubElement(vlan_elem, "protocol").text = protocol
        
        # 设置VLAN snooping配置
        self._configure_vlan_snooping(vlan_elem, intf_data)
        
        # 设置监控配置
        self._configure_monitor_settings(vlan_elem, intf_data)
        
        # 设置IP-MAC绑定配置
        self._configure_ip_mac_bind(vlan_elem, intf_data)
        
        # 设置访问控制（放在最后）
        allowaccess = intf_data.get("allowaccess", [])
        if allowaccess:
            if isinstance(allowaccess, str):
                allowaccess = allowaccess.split()
            self.interface_common.create_access_control(vlan_elem, allowaccess)
        
        # 注意：根据NTOS YANG模型分析，VLAN接口不应该包含role标签
        # VLAN接口使用phy-interface-config，不包含physical-config，因此没有wanlan或role配置
        
        # 清理可能存在的无效role标签
        self._cleanup_invalid_role_tags(vlan_elem)
        
        log(_("interface_handler.vlan_interface_created", name=final_name))
        return vlan_elem
    
    def _configure_ip_settings(self, vlan_elem, intf_data):
        """
        配置IPv4设置，支持静态IP、DHCP和PPPoE模式

        Args:
            vlan_elem: VLAN元素
            intf_data: 接口数据
        """
        # 检查接口模式 - 支持多种字段名称
        interface_mode = intf_data.get("mode", "").lower()
        interface_name = intf_data.get("name", "unknown")

        # 强制为所有子接口添加IPv4配置
        ipv4_elem = etree.SubElement(vlan_elem, "ipv4")
        # 注意：不设置xmlns，继承父命名空间

        # 启用IPv4
        etree.SubElement(ipv4_elem, "enabled").text = "true"

        log(f"为子接口 {interface_name} 添加IPv4配置，模式: {interface_mode}", "info")

        # 智能识别接口模式 - 根据数据字段判断
        if intf_data.get("pppoe_enabled") or interface_mode == "pppoe":
            # PPPoE模式配置 - 修复字段名称映射
            pppoe_data = {
                "user": intf_data.get("pppoe_username", ""),  # FortiGate解析器使用pppoe_username字段
                "password": intf_data.get("pppoe_password", "")  # FortiGate解析器使用pppoe_password字段
            }
            # 将PPPoE数据添加到接口数据中
            intf_data["pppoe"] = pppoe_data
            # 调用PPPoE配置方法，指定这是子接口
            self._configure_pppoe_settings(vlan_elem, intf_data)
            log(f"子接口 {interface_name} 配置为PPPoE模式，用户名: {pppoe_data['user']}", "info")
        elif intf_data.get("ip_address") or intf_data.get("ip"):
            # 静态IP模式配置 - 支持多种IP字段格式
            ip_address = intf_data.get("ip_address") or intf_data.get("ip")
            netmask = intf_data.get("netmask")

            try:
                if "/" in ip_address:
                    # 已经是CIDR格式
                    cidr_address = ip_address
                elif netmask:
                    # ip + netmask格式，转换为CIDR格式
                    import ipaddress
                    network = ipaddress.IPv4Network(f"{ip_address}/{netmask}", strict=False)
                    cidr_address = str(network)
                else:
                    # 只有IP地址，没有掩码，使用/24作为默认
                    cidr_address = f"{ip_address}/24"
                    log(f"子接口 {interface_name} 没有子网掩码，使用默认/24", "warning")

                address_elem = etree.SubElement(ipv4_elem, "address")
                etree.SubElement(address_elem, "ip").text = cidr_address

                # 静态IP模式需要禁用DHCP
                dhcp_elem = etree.SubElement(ipv4_elem, "dhcp")
                etree.SubElement(dhcp_elem, "enabled").text = "false"

                log(f"子接口 {interface_name} 配置为静态IP模式: {cidr_address}", "info")
            except Exception as e:
                log(f"子接口 {interface_name} IP地址转换失败: {e}", "error")
                # 回退到DHCP模式
                self.interface_common.create_dhcp_config(ipv4_elem)
                log(f"子接口 {interface_name} 回退到DHCP模式", "warning")
        elif intf_data.get("dhcp_enabled") or interface_mode == "dhcp":
            # DHCP模式配置
            self.interface_common.create_dhcp_config(ipv4_elem)
            log(f"子接口 {interface_name} 配置为DHCP模式", "info")
        else:
            # 默认使用DHCP模式
            self.interface_common.create_dhcp_config(ipv4_elem)
            log(f"子接口 {interface_name} 未找到明确的IP配置，默认使用DHCP模式", "warning")
            
            # IPv4其他配置
            ipv4_config = intf_data.get("ipv4", {})
            if "mtu" in ipv4_config:
                etree.SubElement(ipv4_elem, "mtu").text = str(ipv4_config["mtu"])
        
        # 注意：IPv6配置现在由_configure_ipv6_settings方法统一处理，避免重复
    
    def _configure_pppoe_settings(self, vlan_elem, intf_data):
        """
        配置PPPoE设置
        
        Args:
            vlan_elem: VLAN元素
            intf_data: 接口数据
        """
        # 获取PPPoE配置，如果不存在则创建一个新的
        pppoe_config = intf_data.get("pppoe", {})
        
        # 确保PPPoE配置中有用户名和密码
        if not pppoe_config.get("user") and "pppoe_username" in intf_data:
            pppoe_config["user"] = intf_data["pppoe_username"]
        
        if not pppoe_config.get("password") and "pppoe_password" in intf_data:
            pppoe_config["password"] = intf_data["pppoe_password"]
        
        # 如果没有有效的PPPoE配置，则返回
        if not pppoe_config.get("user") and not pppoe_config.get("password"):
            log(_("interface_handler.warning.missing_pppoe_credentials"), "warning")
            return
        
        # 在IPv4下创建PPPoE配置
        ipv4_elem = vlan_elem.find("ipv4")
        if ipv4_elem is None:
            ipv4_elem = etree.SubElement(vlan_elem, "ipv4")
            # 不设置命名空间，继承父元素的命名空间
        
        # 确保启用IPv4
        enabled_elem = ipv4_elem.find("enabled")
        if enabled_elem is None:
            etree.SubElement(ipv4_elem, "enabled").text = "true"

        # 使用自定义方法创建PPPoE配置，确保无命名空间前缀
        log("使用通用方法配置PPPoE", "info")
        
        # 创建pppoe元素，不设置xmlns命名空间
        pppoe_elem = etree.SubElement(ipv4_elem, "pppoe")
        # 移除命名空间设置，使用默认命名空间
        
        connection_elem = etree.SubElement(pppoe_elem, "connection")
        
        # 分配一个新的隧道ID（如果没有提供）
        if "tunnel" not in pppoe_config:
            tunnel_id = self.interface_common.get_next_tunnel_id()
            pppoe_config["tunnel"] = tunnel_id
            log(_("yang.assigning_pppoe_tunnel_id", name=intf_data.get("name", "unknown"), id=tunnel_id))
        
        # 设置PPPoE隧道ID
        etree.SubElement(connection_elem, "tunnel").text = str(pppoe_config["tunnel"])
        
        # 设置PPPoE启用状态（默认为true）
        etree.SubElement(connection_elem, "enabled").text = "true"
        
        # 确保PPPoE连接始终有用户名和密码（YANG模型要求）
        username = pppoe_config.get("user") or intf_data.get("username") or intf_data.get("pppoe_username") or "default_user"
        password = pppoe_config.get("password") or intf_data.get("password") or intf_data.get("pppoe_password") or "default_pass"

        # 设置PPPoE用户名（必需）
        etree.SubElement(connection_elem, "user").text = username
        log(_("interface_handler.pppoe_username_set", username=username))

        # 设置PPPoE密码（必需）- 使用加密形式的密码
        if pppoe_config.get("password"):
            # 使用随机生成的加密格式，而不是固定的加密字符串
            import random
            import string
            encrypted_prefix = "=*-#!$_"
            random_chars = ''.join(random.choices(string.ascii_letters + string.digits, k=16))
            encrypted_password = f"{encrypted_prefix}{random_chars}=="
            etree.SubElement(connection_elem, "password").text = encrypted_password
            log(_("interface_handler.pppoe_password_set"))
        else:
            # 使用默认加密密码
            etree.SubElement(connection_elem, "password").text = "=*-#!$_2g+awtqJWZWf+C6zl4RLQ=="
            log(_("interface_handler.pppoe_password_set"))
        
        # PPPoE可选配置
        if "gateway" in pppoe_config:
            etree.SubElement(connection_elem, "gateway").text = str(pppoe_config["gateway"]).lower()
        else:
            # 默认启用网关
            etree.SubElement(connection_elem, "gateway").text = "true"
        
        # 添加timeout和retries元素
        etree.SubElement(connection_elem, "timeout").text = "5"
        etree.SubElement(connection_elem, "retries").text = "3"
        
        if "distance" in pppoe_config:
            etree.SubElement(connection_elem, "distance").text = str(pppoe_config["distance"])
        
        # 添加其他必要的PPPoE配置
        # PPP参数
        ppp_elem = etree.SubElement(connection_elem, "ppp")
        etree.SubElement(ppp_elem, "negotiation-timeout").text = "3"
        etree.SubElement(ppp_elem, "lcp-echo-interval").text = "10"
        etree.SubElement(ppp_elem, "lcp-echo-retries").text = "10"
        
        # 只添加MTU，不添加reverse-path
        etree.SubElement(connection_elem, "ppp-mtu").text = "1492"
        # 子接口的connection标签中不应该有reverse-path元素
        
        log(_("interface_handler.pppoe_configured", interface=intf_data.get("name", "unknown")))

    def _configure_ipv6_settings(self, vlan_elem, intf_data):
        """
        配置IPv6设置，按照用户提供的正确NTOS XML结构

        Args:
            vlan_elem: VLAN元素
            intf_data: 接口数据
        """
        # 创建IPv6配置（按照用户提供的正确结构）
        ipv6_elem = etree.SubElement(vlan_elem, "ipv6")

        # 检查是否启用IPv6
        ipv6_enabled = intf_data.get("ipv6", {}).get("enabled", False)
        etree.SubElement(ipv6_elem, "enabled").text = "true" if ipv6_enabled else "false"

        # 如果启用了IPv6，添加地址配置
        if ipv6_enabled:
            ipv6_config = intf_data.get("ipv6", {})
            if "address" in ipv6_config:
                address_elem = etree.SubElement(ipv6_elem, "address")
                etree.SubElement(address_elem, "ip").text = ipv6_config["address"]

    def _configure_network_stack(self, vlan_elem, intf_data):
        """
        配置网络堆栈设置，按照用户提供的正确NTOS XML结构

        Args:
            vlan_elem: VLAN元素
            intf_data: 接口数据
        """
        # 创建network-stack配置（按照用户提供的正确结构）
        network_stack_elem = etree.SubElement(vlan_elem, "network-stack")

        # 添加IPv4网络堆栈配置
        ipv4_stack_elem = etree.SubElement(network_stack_elem, "ipv4")
        etree.SubElement(ipv4_stack_elem, "arp-ignore").text = "check-interface-and-subnet"

    def _configure_monitor_settings(self, vlan_elem, intf_data):
        """
        配置监控设置，按照用户提供的正确NTOS XML结构

        Args:
            vlan_elem: VLAN元素
            intf_data: 接口数据
        """
        # 创建monitor配置（按照用户提供的正确结构）
        monitor_elem = etree.SubElement(vlan_elem, "monitor")
        monitor_elem.set("xmlns", "urn:ruijie:ntos:params:xml:ns:yang:if-mon")

        # 添加监控阈值配置
        etree.SubElement(monitor_elem, "notify-up-drop-rate-threshold").text = "1000"
        etree.SubElement(monitor_elem, "notify-down-drop-rate-threshold").text = "1000"
        etree.SubElement(monitor_elem, "if-notify-enable").text = "false"
        etree.SubElement(monitor_elem, "notify-up-usage-threshold").text = "100"
        etree.SubElement(monitor_elem, "notify-down-usage-threshold").text = "100"
        etree.SubElement(monitor_elem, "notify-up-speed-threshold").text = "100000000"
        etree.SubElement(monitor_elem, "notify-down-speed-threshold").text = "100000000"

    def _configure_ip_mac_bind(self, vlan_elem, intf_data):
        """
        配置IP-MAC绑定设置，按照用户提供的正确NTOS XML结构

        Args:
            vlan_elem: VLAN元素
            intf_data: 接口数据
        """
        # 创建ip-mac-bind配置（按照用户提供的正确结构）
        ip_mac_bind_elem = etree.SubElement(vlan_elem, "ip-mac-bind")
        ip_mac_bind_elem.set("xmlns", "urn:ruijie:ntos:params:xml:ns:yang:ip-mac")

        # 添加IPv4和IPv6绑定配置
        ipv4_bind_elem = etree.SubElement(ip_mac_bind_elem, "ipv4")
        etree.SubElement(ipv4_bind_elem, "enabled").text = "false"

        ipv6_bind_elem = etree.SubElement(ip_mac_bind_elem, "ipv6")
        etree.SubElement(ipv6_bind_elem, "enabled").text = "false"

    def _configure_vlan_snooping(self, vlan_elem, intf_data):
        """
        配置VLAN snooping设置，按照用户提供的正确NTOS XML结构

        Args:
            vlan_elem: VLAN元素
            intf_data: 接口数据
        """
        # 创建snooping配置（按照用户提供的正确结构）
        snooping_elem = etree.SubElement(vlan_elem, "snooping")

        # 添加snooping配置
        etree.SubElement(snooping_elem, "trust").text = "false"
        etree.SubElement(snooping_elem, "suppress").text = "false"

    def _configure_network_stack_parameters(self, vlan_elem, intf_data):
        """
        配置网络堆栈参数
        
        Args:
            vlan_elem: VLAN元素
            intf_data: 接口数据
        """
        network_stack = intf_data.get("network_stack")
        if not network_stack:
            return
        
        # 添加网络堆栈参数配置
        if "tcp_mss" in network_stack:
            tcp_mss_elem = etree.SubElement(vlan_elem, "tcp-mss")
            tcp_mss_elem.text = str(network_stack["tcp_mss"])
        
        if "ip_fragment" in network_stack:
            ip_fragment_elem = etree.SubElement(vlan_elem, "ip-fragment")
            ip_fragment_elem.text = str(network_stack["ip_fragment"]).lower()
    
    def update_vlan_interface(self, interface_elem, intf_data, interface_mapping=None):
        """
        更新VLAN子接口配置，符合NTOS YANG模型要求
        
        Args:
            interface_elem: 接口元素
            intf_data: 接口数据
            interface_mapping: 接口映射表，用于将FortiGate接口名称映射为NTOS名称
        """
        log(_("interface_handler.update_vlan_interface", name=intf_data.get("name", "unknown")))
        
        # 确保有正确的VLAN子接口名称
        interface_name = intf_data.get("name", "")
        # FortiGate使用"interface"字段来指定父接口，也支持"parent_interface"
        parent_interface = intf_data.get("parent_interface") or intf_data.get("interface")
        vlan_id = intf_data.get("vlanid")
        
        # 如果接口数据中包含了正确格式的名称，直接使用
        if interface_name and "." in interface_name:
            final_name = interface_name
            log(_("interface_handler.using_existing_name", name=final_name))
        else:
            # 否则，尝试构造正确的名称
            if parent_interface and vlan_id:
                # 映射父接口名称
                if isinstance(parent_interface, dict):
                    if "name" in parent_interface:
                        raw_parent = parent_interface["name"]
                    elif "interface" in parent_interface:
                        raw_parent = parent_interface["interface"]
                    else:
                        raw_parent = list(parent_interface.values())[0] if parent_interface else "unknown"
                else:
                    raw_parent = str(parent_interface)
                
                mapped_parent = self._map_parent_interface(raw_parent, interface_mapping)
                final_name = self._generate_vlan_interface_name(mapped_parent, vlan_id)
                log(_("interface_handler.generated_name", parent=mapped_parent, vlan=vlan_id, final=final_name))
            else:
                # 从现有接口元素中提取名称和VLAN ID
                name_elem = interface_elem.find("./name")
                vlan_id_elem = interface_elem.find("./vlan-id")
                link_interface_elem = interface_elem.find("./link-interface")
                
                if name_elem is not None and name_elem.text and name_elem.text != "main":
                    # 如果名称元素已存在且有值（非"main"），尝试分析其中的父接口和VLAN ID
                    extracted_parent, extracted_vlan = self._extract_parent_and_vlan_from_name(name_elem.text)
                    if extracted_parent and extracted_vlan:
                        # 如果能提取出父接口和VLAN ID，则保留原名称
                        final_name = name_elem.text
                        log(_("interface_handler.extracted_from_name", name=final_name, parent=extracted_parent, vlan=extracted_vlan))
                    else:
                        # 如果无法从现有名称中提取，但有VLAN ID元素和link-interface元素，尝试构造名称
                        if vlan_id_elem is not None and vlan_id_elem.text and link_interface_elem is not None and link_interface_elem.text:
                            final_name = self._generate_vlan_interface_name(link_interface_elem.text, vlan_id_elem.text)
                            log(_("interface_handler.constructed_from_elements", link=link_interface_elem.text, vlan=vlan_id_elem.text, final=final_name))
                        else:
                            # 使用默认名称
                            log(_("warning.vlan_interface_incomplete_data", name=name_elem.text), "warning")
                            # 如果现有名称是"main"，尝试从link-interface和vlan-id构造
                            if name_elem.text == "main" and link_interface_elem is not None and link_interface_elem.text and vlan_id_elem is not None and vlan_id_elem.text:
                                final_name = self._generate_vlan_interface_name(link_interface_elem.text, vlan_id_elem.text)
                                log(_("warning.replacing_main_with_proper_name", final=final_name), "warning")
                            else:
                                final_name = name_elem.text  # 保留原名称，即使可能不符合规范
                else:
                    # 如果没有名称元素或名称为"main"，尝试从其他元素构造
                    if link_interface_elem is not None and link_interface_elem.text and vlan_id_elem is not None and vlan_id_elem.text:
                        final_name = self._generate_vlan_interface_name(link_interface_elem.text, vlan_id_elem.text)
                        log(_("interface_handler.name_from_link_and_vlan", link=link_interface_elem.text, vlan=vlan_id_elem.text, final=final_name))
                    else:
                        # 如果无法构造，使用默认名称
                        final_name = "unknown.1"
                        log(_("warning.using_default_name", name=final_name), "warning")
        
        # 更新接口名称 - 增加更多检查和保护
        name_elem = interface_elem.find("./name")
        if name_elem is not None:
            # 仅当名称需要更新时才更新
            if name_elem.text != final_name:
                # 特别处理"main"名称的情况
                if name_elem.text == "main":
                    log(_("warning.replacing_main_with_vlan_name", original="main", fixed=final_name), "warning")
                else:
                    log(_("yang.fixing_subinterface_name", original=name_elem.text, fixed=final_name))
                
                # 保存旧值用于后续验证
                old_name = name_elem.text
                
                # 设置新名称
                name_elem.text = final_name
                
                # 验证更新是否成功
                if name_elem.text != final_name:
                    log(_("error.name_update_failed", expected=final_name, actual=name_elem.text), "error")
                    # 再次尝试更新
                    name_elem.text = final_name
                    
                # 二次验证
                if name_elem.text == "main" and old_name != "main":
                    log(_("error.name_reverted_to_main", original=old_name), "error")
                    # 强制设置正确的名称
                    name_elem.text = final_name
        else:
            # 如果没有name元素，创建一个
            name_elem = etree.SubElement(interface_elem, "name")
            name_elem.text = final_name
            log(_("interface_handler.created_name_element", name=final_name))
        
        # 更新VLAN ID
        if "vlanid" in intf_data:
            vlan_id = intf_data["vlanid"]
            if self._validate_vlan_id(vlan_id):
                vlan_id_elem = interface_elem.find("./vlan-id")
                if vlan_id_elem is not None:
                    vlan_id_elem.text = str(vlan_id)
                else:
                    etree.SubElement(interface_elem, "vlan-id").text = str(vlan_id)
            else:
                log(_("interface_handler.invalid_vlan_id", vlan_id=vlan_id, name=intf_data.get("name", "unknown")), "warning")
        
        # 更新link-interface (父接口)
        if parent_interface:
            link_interface_elem = interface_elem.find("./link-interface")
            if link_interface_elem is not None:
                if isinstance(parent_interface, str):
                    link_interface_elem.text = parent_interface
                else:
                    # 如果是字典，尝试获取名称
                    parent_name = parent_interface.get("name", str(parent_interface))
                    link_interface_elem.text = parent_name
            else:
                if isinstance(parent_interface, str):
                    etree.SubElement(interface_elem, "link-interface").text = parent_interface
                else:
                    parent_name = parent_interface.get("name", str(parent_interface))
                    etree.SubElement(interface_elem, "link-interface").text = parent_name
        
        # 更新protocol (协议类型)
        if "protocol" in intf_data:
            protocol = intf_data["protocol"]
            protocol_elem = interface_elem.find("./protocol")
            if protocol_elem is not None:
                protocol_elem.text = protocol
            else:
                etree.SubElement(interface_elem, "protocol").text = protocol
        
        # 更新状态 (enabled)
        if "status" in intf_data:
            enabled_text = "true" if intf_data["status"].lower() == "up" else "false"
            enabled_elem = interface_elem.find("./enabled")
            if enabled_elem is not None:
                enabled_elem.text = enabled_text
            else:
                etree.SubElement(interface_elem, "enabled").text = enabled_text
        
        # 更新描述 (description)
        if "description" in intf_data:
            description_elem = interface_elem.find("./description")
            if description_elem is not None:
                description_elem.text = intf_data["description"]
            else:
                etree.SubElement(interface_elem, "description").text = intf_data["description"]
        
        # 更新IP设置
        self._update_ip_settings(interface_elem, intf_data)
        
        # 更新网络堆栈参数
        if "network_stack" in intf_data:
            self._configure_network_stack_parameters(interface_elem, intf_data)
        
        # 更新反向路径设置
        if "reverse_path" in intf_data:
            reverse_path_text = str(intf_data["reverse_path"]).lower()
            reverse_path_elem = interface_elem.find("./reverse-path")
            if reverse_path_elem is not None:
                reverse_path_elem.text = reverse_path_text
            else:
                etree.SubElement(interface_elem, "reverse-path").text = reverse_path_text
        
        # 更新访问控制设置
        if "allowaccess" in intf_data:
            allowaccess = intf_data["allowaccess"]
            # 先删除现有的access-control节点
            for ac_elem in interface_elem.findall("./access-control"):
                interface_elem.remove(ac_elem)
                
            # 创建新的access-control节点
            if isinstance(allowaccess, str):
                allowaccess = allowaccess.split()
            self.interface_common.create_access_control(interface_elem, allowaccess)
        
        # 清理可能存在的无效role标签
        # VLAN接口不应该包含role标签
        self._cleanup_invalid_role_tags(interface_elem)
        
        # 最后一次验证名称元素，确保没有被其他处理过程修改
        name_elem = interface_elem.find("./name")
        if name_elem is not None and name_elem.text != final_name:
            log(_("warning.name_changed_during_processing", expected=final_name, actual=name_elem.text), "warning")
            name_elem.text = final_name
        
        log(_("yang.vlan_update_complete", name=final_name))
        return interface_elem
    
    def _update_ip_settings(self, interface_elem, intf_data):
        """
        更新IPv4和IPv6设置，支持静态IP、DHCP和PPPoE模式
        
        Args:
            interface_elem: 接口元素
            intf_data: 接口数据
        """
        # 检查接口模式
        interface_mode = intf_data.get("mode", "").lower()
        
        # 更新IPv4配置
        if "ip" in intf_data or "ipv4" in intf_data or interface_mode in ["dhcp", "pppoe"]:
            ipv4_elem = interface_elem.find("./ipv4")
            if ipv4_elem is None:
                ipv4_elem = etree.SubElement(interface_elem, "ipv4")
                # 不设置命名空间，继承父元素的命名空间
            
            # 确保IPv4已启用
            enabled_elem = ipv4_elem.find("./enabled")
            if enabled_elem is None:
                etree.SubElement(ipv4_elem, "enabled").text = "true"
            else:
                enabled_elem.text = "true"
            
            if interface_mode == "dhcp":
                # DHCP模式配置 - 移除静态地址配置，添加DHCP配置
                log(_("interface_handler.info.updating_dhcp_mode", interface=intf_data.get('name', 'unknown')))
                
                # 移除现有的静态地址配置
                for addr_elem in ipv4_elem.findall("./address"):
                    ipv4_elem.remove(addr_elem)
                
                # 移除现有的DHCP配置（如果存在）
                for dhcp_elem in ipv4_elem.findall("./dhcp"):
                    ipv4_elem.remove(dhcp_elem)
                
                # 添加新的DHCP配置
                self.interface_common.create_dhcp_config(ipv4_elem)
            elif interface_mode == "pppoe":
                # PPPoE模式配置
                log(_("interface_handler.info.updating_pppoe_mode", interface=intf_data.get('name', 'unknown')))
                
                # 移除现有的静态地址配置
                for addr_elem in ipv4_elem.findall("./address"):
                    ipv4_elem.remove(addr_elem)
                
                # 移除现有的DHCP配置（如果存在）
                for dhcp_elem in ipv4_elem.findall("./dhcp"):
                    ipv4_elem.remove(dhcp_elem)
                
                # 创建PPPoE配置
                pppoe_data = {
                    "user": intf_data.get("pppoe_username", ""),
                    "password": intf_data.get("pppoe_password", "")
                }
                # 将PPPoE数据添加到接口数据中
                intf_data["pppoe"] = pppoe_data
                
                # 检查是否已有PPPoE配置
                pppoe_elem = ipv4_elem.find("./pppoe")
                if pppoe_elem is not None:
                    # 移除现有的PPPoE配置
                    ipv4_elem.remove(pppoe_elem)
                
                # 调用PPPoE配置方法创建新的配置，指定这是子接口
                self._configure_pppoe_settings(interface_elem, intf_data)
            else:
                # 静态IP地址配置
                ip_address = intf_data.get("ip") or intf_data.get("ipv4", {}).get("address")
                if ip_address:
                    # 移除现有的DHCP配置（如果存在）
                    for dhcp_elem in ipv4_elem.findall("./dhcp"):
                        ipv4_elem.remove(dhcp_elem)
                    
                    address_elem = ipv4_elem.find("./address")
                    if address_elem is None:
                        address_elem = etree.SubElement(ipv4_elem, "address")
                    
                    ip_elem = address_elem.find("./ip")
                    if ip_elem is not None:
                        ip_elem.text = ip_address
                    else:
                        etree.SubElement(address_elem, "ip").text = ip_address
                    
                    # 移除可能存在的prefix-length节点（不符合YANG模型）
                    prefix_elem = address_elem.find("./prefix-length")
                    if prefix_elem is not None:
                        address_elem.remove(prefix_elem)
            
            # 更新IPv4其他配置
            ipv4_config = intf_data.get("ipv4", {})
            if "mtu" in ipv4_config:
                mtu_elem = ipv4_elem.find("./mtu")
                if mtu_elem is not None:
                    mtu_elem.text = str(ipv4_config["mtu"])
                else:
                    etree.SubElement(ipv4_elem, "mtu").text = str(ipv4_config["mtu"])
        
        # 更新IPv6配置
        if "ipv6" in intf_data:
            ipv6_elem = interface_elem.find("./ipv6")
            if ipv6_elem is None:
                ipv6_elem = etree.SubElement(interface_elem, "ipv6")
                # 不设置命名空间，继承父元素的命名空间
            
            ipv6_config = intf_data["ipv6"]
            
            # 检查IPv6模式
            ipv6_mode = ipv6_config.get("mode", "").lower()
            
            if ipv6_mode == "dhcp":
                # IPv6 DHCP模式配置
                log(_("interface_handler.info.updating_ipv6_dhcp_mode", interface=intf_data.get('name', 'unknown')))
                
                # 移除现有的静态地址配置
                for addr_elem in ipv6_elem.findall("./address"):
                    ipv6_elem.remove(addr_elem)
                
                # 移除现有的DHCP配置（如果存在）
                for dhcp_elem in ipv6_elem.findall("./dhcp"):
                    ipv6_elem.remove(dhcp_elem)
                
                # 确保IPv6已启用
                enabled_elem = ipv6_elem.find("./enabled")
                if enabled_elem is None:
                    etree.SubElement(ipv6_elem, "enabled").text = "true"
                else:
                    enabled_elem.text = "true"
                
                # 添加新的DHCP配置
                self.interface_common.create_dhcp_config(ipv6_elem)
            else:
                # 更新IPv6地址
                if "address" in ipv6_config:
                    # 移除现有的DHCP配置（如果存在）
                    for dhcp_elem in ipv6_elem.findall("./dhcp"):
                        ipv6_elem.remove(dhcp_elem)
                    
                    address_elem = ipv6_elem.find("./address")
                    if address_elem is None:
                        address_elem = etree.SubElement(ipv6_elem, "address")
                    
                    ip_elem = address_elem.find("./ip")
                    if ip_elem is not None:
                        ip_elem.text = ipv6_config["address"]
                    else:
                        etree.SubElement(address_elem, "ip").text = ipv6_config["address"]
                    
                    # 移除可能存在的prefix-length节点（不符合YANG模型）
                    prefix_elem = address_elem.find("./prefix-length")
                    if prefix_elem is not None:
                        address_elem.remove(prefix_elem)
                
                # 更新IPv6其他配置
                if "enabled" in ipv6_config:
                    enabled_elem = ipv6_elem.find("./enabled")
                    if enabled_elem is not None:
                        enabled_elem.text = str(ipv6_config["enabled"]).lower()
                    else:
                        etree.SubElement(ipv6_elem, "enabled").text = str(ipv6_config["enabled"]).lower()
            
            if "mtu" in ipv6_config:
                mtu_elem = ipv6_elem.find("./mtu")
                if mtu_elem is not None:
                    mtu_elem.text = str(ipv6_config["mtu"])
    


    def _configure_network_stack(self, vlan_elem, intf_data):
        """
        配置网络堆栈设置
        
        Args:
            vlan_elem: VLAN元素
            intf_data: 接口数据
        """
        network_stack = intf_data.get("network_stack", {})
        
        # 配置TCP MSS
        if "tcp_mss" in network_stack:
            etree.SubElement(vlan_elem, "tcp-mss").text = str(network_stack["tcp_mss"])
        
        # 配置IP分片
        if "ip_fragment" in network_stack:
            etree.SubElement(vlan_elem, "ip-fragment").text = str(network_stack["ip_fragment"]).lower()

    def _cleanup_invalid_role_tags(self, vlan_elem):
        """
        清理VLAN接口中无效的role标签
        根据NTOS YANG模型，VLAN接口不应该包含role标签
        
        Args:
            vlan_elem: VLAN接口元素
        """
        # 移除可能存在的role标签（不符合YANG模型）
        role_elems = vlan_elem.findall("./role")
        for role_elem in role_elems:
            vlan_elem.remove(role_elem)
            log(_("interface_handler.cleanup_invalid_role_tag"))

    def _configure_vlan_snooping(self, vlan_elem, intf_data):
        """
        配置VLAN snooping设置
        
        Args:
            vlan_elem: VLAN元素
            intf_data: 接口数据
        """
        snooping_config = intf_data.get("snooping", {})
        
        if snooping_config:
            snooping_elem = etree.SubElement(vlan_elem, "snooping")
            
            # 配置DHCP snooping
            if "dhcp" in snooping_config:
                dhcp_snooping = etree.SubElement(snooping_elem, "dhcp")
                etree.SubElement(dhcp_snooping, "enabled").text = str(snooping_config["dhcp"]).lower()

    def _configure_monitor_settings(self, vlan_elem, intf_data):
        """
        配置监控设置
        
        Args:
            vlan_elem: VLAN元素
            intf_data: 接口数据
        """
        monitor_config = intf_data.get("monitor", {})
        
        if monitor_config:
            monitor_elem = etree.SubElement(vlan_elem, "monitor")
            # monitor标签需要命名空间
            monitor_elem.set("xmlns", NS_MONITOR)
            
            # 配置监控启用状态
            if "enabled" in monitor_config:
                etree.SubElement(monitor_elem, "enabled").text = str(monitor_config["enabled"]).lower()

    def _configure_ip_mac_bind(self, vlan_elem, intf_data):
        """
        配置IP-MAC绑定设置
        
        Args:
            vlan_elem: VLAN元素
            intf_data: 接口数据
        """
        ip_mac_bind_config = intf_data.get("ip_mac_bind", {})
        
        if ip_mac_bind_config:
            ip_mac_elem = etree.SubElement(vlan_elem, "ip-mac-bind")
            # ip-mac-bind标签需要命名空间
            ip_mac_elem.set("xmlns", NS_IPMAC)
            
            # 配置IP-MAC绑定启用状态
            if "enabled" in ip_mac_bind_config:
                etree.SubElement(ip_mac_elem, "enabled").text = str(ip_mac_bind_config["enabled"]).lower()

# 为了保持向后兼容性，创建一个默认的VLAN接口处理器实例
_default_vlan_interface_handler = VlanInterfaceHandler()

# 向后兼容的函数，调用默认VLAN接口处理器的方法
def create_new_vlan_interface(interface_elem, intf_data, interface_mapping=None):
    """向后兼容的创建新VLAN子接口函数"""
    return _default_vlan_interface_handler.create_new_vlan_interface(interface_elem, intf_data, interface_mapping)

def update_vlan_interface(interface_elem, intf_data, interface_mapping=None):
    """向后兼容的更新VLAN子接口函数"""
    return _default_vlan_interface_handler.update_vlan_interface(interface_elem, intf_data, interface_mapping)