#!/usr/bin/env python
# -*- coding: utf-8 -*-

from lxml import etree
import re
import copy
from engine.utils.logger import log
from engine.utils.i18n import _

# 定义标准命名空间映射
NAMESPACE_MAPPING = {
    "security-zone": "urn:ruijie:ntos:params:xml:ns:yang:security-zone",
    "network-obj": "urn:ruijie:ntos:params:xml:ns:yang:network-obj",
    "routing": "urn:ruijie:ntos:params:xml:ns:yang:routing",
    "interface": "urn:ruijie:ntos:params:xml:ns:yang:interface",
    "vlan": "urn:ruijie:ntos:params:xml:ns:yang:vlan",
    "policy": "urn:ruijie:ntos:params:xml:ns:yang:policy",
    "time-range": "urn:ruijie:ntos:params:xml:ns:yang:time-range",
    "service": "urn:ruijie:ntos:params:xml:ns:yang:service",
    "address": "urn:ruijie:ntos:params:xml:ns:yang:address",
    "nat": "urn:ruijie:ntos:params:xml:ns:yang:nat",
    "dns": "urn:ruijie:ntos:params:xml:ns:yang:dns",
    "system": "urn:ruijie:ntos:params:xml:ns:yang:system"
}

# 默认命名空间常量
DEFAULT_NAMESPACE = "urn:ruijie:ntos"


class XMLNamespaceHandler:
    """XML命名空间处理器，处理命名空间相关操作"""
    
    def __init__(self, root, nsmap=None):
        """
        初始化XML命名空间处理器
        
        Args:
            root: XML根节点
            nsmap: 命名空间映射
        """
        self.root = root
        self.nsmap = nsmap or {None: DEFAULT_NAMESPACE}
    
    def simplify_root_namespace(self):
        """
        简化根元素命名空间，确保只有一个默认命名空间
        
        Returns:
            Element: 处理后的根元素
        """
        log(_("info.simplifying_root_namespace"))
        
        # 确保根元素有正确的命名空间声明，只保留默认命名空间
        if self.root.tag.endswith('config'):
            log(_("info.root_config_element_found"))
            
            # 创建新的根元素，只包含默认命名空间
            simple_nsmap = {None: DEFAULT_NAMESPACE}
            new_root = etree.Element("config", nsmap=simple_nsmap)
            
            # 将原根元素的所有子元素复制到新根元素
            for child in self.root:
                new_root.append(child)
            
            # 更新根元素
            self.root = new_root
            log(_("info.root_namespace_simplified"))
        
        return self.root
    
    def find_or_create_vrf(self):
        """
        查找或创建VRF节点
        
        Returns:
            Element: VRF节点
        """
        log(_("info.finding_vrf_node"))
        
        # 多种查找方式
        vrf = None
        
        # 方法1：使用命名空间前缀查找
        vrf = self.root.find(".//ntos:vrf", namespaces=self.nsmap)
        
        # 方法2：不使用命名空间前缀查找
        if vrf is None:
            vrf = self.root.find(".//vrf")
            if vrf is not None:
                log(_("info.vrf_node_found_without_namespace"))
        
        # 方法3：使用local-name()函数查找
        if vrf is None:
            vrf = self.root.xpath(".//*[local-name()='vrf']")
            if vrf and len(vrf) > 0:
                vrf = vrf[0]
                log(_("info.vrf_node_found_with_local_name"))
        
        # 方法4：使用完整URI查找
        if vrf is None:
            vrf = self.root.find(".//{%s}vrf" % self.nsmap['ntos'])
            if vrf is not None:
                log(_("info.vrf_node_found_with_full_uri"))
        
        # 如果没有找到VRF节点，创建一个
        if vrf is None:
            log(_("info.vrf_node_not_found_creating_new"), "warning")
            
            # 创建VRF子元素
            from lxml.etree import QName
            qname = QName(self.nsmap['ntos'], "vrf")
            vrf = etree.SubElement(self.root, qname)
            
            # 添加name子元素
            etree.SubElement(vrf, QName(self.nsmap['ntos'], "name")).text = "main"
            log(_("info.vrf_node_created"))
        else:
            # 找到了vrf节点，检查是否有name或n子节点
            log(_("info.vrf_node_found"))
            
            name_elem = vrf.find(".//ntos:name", namespaces=self.nsmap)
            if name_elem is None:
                name_elem = vrf.find(".//name")
            
            n_elem = vrf.find(".//ntos:n", namespaces=self.nsmap)
            if n_elem is None:
                n_elem = vrf.find(".//n")
            
            # 如果存在name元素，检查值是否为"main"
            if name_elem is not None:
                current_name = name_elem.text
                if current_name != 'main':
                    log(_("info.vrf_name_not_main", current=current_name), "warning")
                    name_elem.text = 'main'
                    log(_("info.corrected_vrf_name_to_main"))
            elif n_elem is not None:
                # 如果存在n元素，将其替换为name元素
                current_name = n_elem.text
                # 移除n元素
                n_elem.getparent().remove(n_elem)
                # 创建name元素
                from lxml.etree import QName
                etree.SubElement(vrf, QName(self.nsmap['ntos'], "name")).text = current_name if current_name == 'main' else 'main'
                log(_("info.replaced_n_with_name_element"))
            else:
                # 如果不存在name和n元素，创建name元素
                from lxml.etree import QName
                etree.SubElement(vrf, QName(self.nsmap['ntos'], "name")).text = "main"
                log(_("info.added_missing_name_element"))
        
        return vrf
    
    def find_element(self, parent, tag_name, create_if_missing=True, namespace=None):
        """
        查找或创建元素
        
        Args:
            parent: 父元素
            tag_name: 标签名
            create_if_missing: 如果不存在是否创建
            namespace: 命名空间
            
        Returns:
            Element: 找到或创建的元素
        """
        # 确保parent不是列表
        if isinstance(parent, list):
            if len(parent) > 0:
                parent = parent[0]
            else:
                log(_("error.parent_element_is_empty_list"), "error")
                return None
        
        # 安全检查父元素
        if parent is None:
            log(_("error.parent_element_is_none"), "error", tag=tag_name)
            return None
            
        # 检查父元素是否有正确的类型
        if not hasattr(parent, 'find') or not hasattr(parent, 'xpath'):
            log(_("error.parent_not_element_object"), "error", type=str(type(parent)))
            return None
            
        # 确定命名空间
        if namespace is None and tag_name in NAMESPACE_MAPPING:
            namespace = NAMESPACE_MAPPING[tag_name]
            log(_("info.namespace_mapped", tag=tag_name, namespace=namespace), "debug")
        
        # 多种查找方式
        element = None
        
        # 1. 直接查找
        element = parent.find(".//" + tag_name)
        
        # 2. 使用local-name()函数查找
        if element is None:
            element = parent.xpath(".//*[local-name()='" + tag_name + "']")
            if element and len(element) > 0:
                element = element[0]
        
        # 3. 使用命名空间查找
        if element is None and namespace:
            for prefix, uri in self.nsmap.items():
                if uri == namespace and prefix:
                    element = parent.find(".//" + prefix + ":" + tag_name, namespaces=self.nsmap)
                    if element is not None:
                        break
        
        # 4. 使用完整URI查找
        if element is None and namespace:
            element = parent.find(".//{%s}%s" % (namespace, tag_name))
        
        # 如果未找到且需要创建
        if element is None and create_if_missing:
            try:
                if namespace:
                    element = etree.SubElement(parent, tag_name)
                    element.set("xmlns", namespace)
                else:
                    element = etree.SubElement(parent, tag_name)
                
                log(_("info.created_element", name=tag_name))
            except Exception as e:
                log(_("error.create_element_failed", tag=tag_name, error=str(e)), "error")
                return None
        
        return element
    
    def cleanup_namespaces(self, element):
        """
        清理元素的命名空间，避免冗余命名空间声明
        
        Args:
            element: 要清理的元素
            
        Returns:
            Element: 清理后的元素
        """
        log(_("info.cleaning_namespaces"))
        
        # 记录原始元素数量
        original_count = sum(1 for _ in element.iter())
        
        # 保存清理前的元素作为备份
        element_backup = copy.deepcopy(element)
        
        try:
            # 使用lxml的cleanup_namespaces方法清理命名空间
            # 此方法可能在某些情况下有问题，所以我们添加了备份恢复机制
            etree.cleanup_namespaces(element)
            
            # 检查清理后的元素数量
            cleaned_count = sum(1 for _ in element.iter())
            
            # 如果清理导致元素丢失超过10%，恢复备份
            if cleaned_count < original_count * 0.9:
                log(_("warning.namespace_cleanup_lost_elements", 
                       original=original_count, 
                       remaining=cleaned_count), "warning")
                return element_backup
            
            log(_("info.namespaces_cleaned_successfully"))
            return element
            
        except Exception as e:
            log(_("error.namespace_cleanup_failed", error=str(e)), "error")
            return element_backup
    
    def fix_duplicate_xmlns(self, xml_str):
        """
        修复XML字符串中重复的xmlns属性
        
        Args:
            xml_str: XML字符串
            
        Returns:
            str: 修复后的XML字符串
        """
        log(_("info.fixing_duplicate_xmlns"))
        
        # 检查原始XML字符串
        if not xml_str or len(xml_str) < 20:
            return xml_str
            
        # 使用正则表达式查找和替换重复的xmlns属性
        try:
            # 匹配元素上重复的xmlns属性
            duplicate_xmlns = re.compile(r'(<[\w\-:]+\s+[^>]*?)xmlns\s*=\s*["\']([^"\']+)["\']([^>]*?)xmlns\s*=\s*["\'](\2)["\']', re.DOTALL)
            
            # 计数重复
            count = 0
            
            # 循环替换所有重复属性
            while True:
                # 查找重复属性
                match = duplicate_xmlns.search(xml_str)
                if not match:
                    break
                    
                # 替换重复属性
                xml_str = xml_str[:match.start()] + \
                         match.group(1) + \
                         'xmlns="' + match.group(2) + '"' + \
                         match.group(3) + \
                         xml_str[match.end():]
                count += 1
                
            # 匹配并替换子元素上不必要的xmlns属性
            namespace_uri = "urn:ruijie:ntos"
            already_declared = re.compile(r'(<[\w\-:]+\s+[^>]*?xmlns\s*=\s*["\']' + re.escape(namespace_uri) + '["\'][^>]*?>)([^<]*?)(<[\w\-:]+\s+[^>]*?)xmlns\s*=\s*["\']' + re.escape(namespace_uri) + '["\']', re.DOTALL)
            
            # 循环替换所有不必要的属性
            while True:
                # 查找不必要的属性
                match = already_declared.search(xml_str)
                if not match:
                    break
                    
                # 替换不必要的属性
                xml_str = xml_str[:match.start()] + \
                         match.group(1) + \
                         match.group(2) + \
                         match.group(3) + \
                         xml_str[match.end():]
                count += 1
                
            # 记录结果
            log(_("xml_utils.fix_duplicate_xmlns", count=count))
            
        except Exception as e:
            log(_("error.fix_xmlns_failed", error=str(e)), "warning")
            
        return xml_str
        
    def fix_namespace_prefixes(self, xml_str):
        """
        修复命名空间前缀问题
        
        Args:
            xml_str: XML字符串
            
        Returns:
            str: 修复后的XML字符串
        """
        # 修复 xmlns:ns14="http://www.w3.org/2000/xmlns/" ns14:xmlns="..." 的问题
        pattern = r'xmlns:ns\d+="http://www\.w3\.org/2000/xmlns/" ns\d+:xmlns="([^"]+)"'
        xml_str = re.sub(pattern, r'xmlns="\1"', xml_str)
        
        # 修复其他潜在的错误形式
        pattern = r'xmlns:([a-zA-Z0-9]+)="http://www\.w3\.org/2000/xmlns/" \1:xmlns="([^"]+)"'
        xml_str = re.sub(pattern, r'xmlns="\2"', xml_str)
        
        # 修复重复的xmlns前缀声明
        pattern = r'(xmlns:[a-zA-Z0-9]+="[^"]+")(\s+\1)+'
        xml_str = re.sub(pattern, r'\1', xml_str)
        
        return xml_str


class XMLElementBuilder:
    """XML元素构建器，简化XML元素的创建和更新"""
    
    def __init__(self, nsmap=None):
        """
        初始化XML元素构建器
        
        Args:
            nsmap (dict, optional): 命名空间映射
        """
        from lxml import etree
        self.etree = etree
        self.nsmap = nsmap or {None: DEFAULT_NAMESPACE}
    
    def create_element(self, parent, tag_name, text=None, namespace=None, attributes=None):
        """
        创建XML元素
        
        Args:
            parent (Element): 父元素
            tag_name (str): 标签名
            text (str, optional): 元素文本
            namespace (str, optional): 命名空间
            attributes (dict, optional): 属性字典
            
        Returns:
            Element: 创建的元素
        """
        # 确保parent不是列表
        if isinstance(parent, list):
            if len(parent) > 0:
                parent = parent[0]
            else:
                log(_("error.parent_element_is_empty_list"), "error")
                # 创建一个新的根元素作为替代
                parent = self.etree.Element("config", nsmap=self.nsmap)
        
        # 安全检查父元素
        if parent is None:
            log(_("error.parent_element_is_none", tag=tag_name), "error")
            # 创建一个新的根元素作为替代
            parent = self.etree.Element("config", nsmap=self.nsmap)
        
        # 检查父元素是否有正确的类型
        if not hasattr(parent, 'append'):
            log(_("error.parent_not_element_object"), "error", type=str(type(parent)))
            # 创建一个新的根元素作为替代
            parent = self.etree.Element("config", nsmap=self.nsmap)
        
        try:
            # 确定命名空间
            if namespace is None and tag_name in NAMESPACE_MAPPING:
                namespace = NAMESPACE_MAPPING[tag_name]
            
            # 创建元素
            if namespace:
                # 创建带命名空间的元素
                from lxml.etree import QName
                qname = QName(namespace, tag_name)
                element = self.etree.SubElement(parent, qname)
            else:
                # 创建不带命名空间的元素
                element = self.etree.SubElement(parent, tag_name)
            
            # 设置元素文本
            if text is not None:
                element.text = str(text)
            
            # 设置属性
            if attributes:
                for name, value in attributes.items():
                    element.set(name, str(value))
            
            return element
            
        except Exception as e:
            log(_("error.create_element_failed", tag=tag_name, error=str(e)), "error")
            # 创建一个简单元素作为替代
            try:
                element = self.etree.SubElement(parent, tag_name)
                if text is not None:
                    element.text = str(text)
                return element
            except:
                # 最后的尝试，如果仍然失败，返回父元素
                return parent
    
    def create_address_object(self, parent, address_data):
        """
        创建地址对象元素
        
        Args:
            parent (Element): 父元素
            address_data (dict): 地址对象数据
            
        Returns:
            Element: 创建的地址对象元素
        """
        try:
            # 检查必要字段
            if "name" not in address_data:
                log(_("error.address_missing_name"), "error")
                return None
                
            # 创建地址对象元素
            address = self.create_element(parent, "address")
            
            # 添加名称
            self.create_element(address, "name", text=address_data["name"])
            
            # 添加描述(如果有)
            if "description" in address_data and address_data["description"]:
                self.create_element(address, "description", text=address_data["description"])
                
            # 根据类型添加不同属性
            if "type" in address_data:
                addr_type = address_data["type"].lower()
                
                if addr_type == "host":
                    # 主机类型
                    if "host" in address_data:
                        self.create_element(address, "type", text="host")
                        self.create_element(address, "ip", text=address_data["host"])
                elif addr_type == "subnet":
                    # 子网类型
                    if "subnet" in address_data:
                        self.create_element(address, "type", text="subnet")
                        self.create_element(address, "subnet", text=address_data["subnet"])
                elif addr_type == "range":
                    # IP范围类型
                    if "start_ip" in address_data and "end_ip" in address_data:
                        self.create_element(address, "type", text="range")
                        self.create_element(address, "start-ip", text=address_data["start_ip"])
                        self.create_element(address, "end-ip", text=address_data["end_ip"])
                elif addr_type == "fqdn":
                    # FQDN类型
                    if "fqdn" in address_data:
                        self.create_element(address, "type", text="fqdn")
                        self.create_element(address, "fqdn", text=address_data["fqdn"])
                else:
                    # 未知类型
                    log(_("warning.unknown_address_type", type=addr_type), "warning")
                    self.create_element(address, "type", text="unknown")
                    
            return address
            
        except Exception as e:
            log(_("error.create_address_object_failed", 
                 name=address_data.get("name", "unknown"), 
                 error=str(e)), "error")
            return None
    
    def create_interface(self, parent, interface_data, is_vlan=False):
        """
        创建接口元素
        
        Args:
            parent (Element): 父元素
            interface_data (dict): 接口数据
            is_vlan (bool): 是否是VLAN接口
            
        Returns:
            Element: 创建的接口元素
        """
        try:
            # 确定接口类型
            interface_type = "vlan" if is_vlan else "physical"
            
            # 创建接口元素
            interface = self.create_element(parent, interface_type)
            
            # 添加名称
            if "name" in interface_data:
                self.create_element(interface, "name", text=interface_data["name"])
            
            # 添加描述（如果有）
            if "description" in interface_data and interface_data["description"]:
                self.create_element(interface, "description", text=interface_data["description"])
            
            # 添加状态
            status = interface_data.get("status", "up").lower()
            if status in ["up", "down"]:
                self.create_element(interface, "status", text=status)
            
            # 添加IPv4配置
            if "ip" in interface_data or "ipv4" in interface_data:
                ip_data = interface_data.get("ip", interface_data.get("ipv4", {}))
                if isinstance(ip_data, dict) and "address" in ip_data:
                    ipv4_elem = self.create_element(interface, "ipv4")
                    address_elem = self.create_element(ipv4_elem, "address")
                    self.create_element(address_elem, "address", text=ip_data["address"])
            
            return interface
            
        except Exception as e:
            log(_("error.create_interface_failed", 
                 name=interface_data.get("name", "未知"), 
                 error=str(e)), "error")
            # 创建一个简单的接口元素作为替代
            interface = self.create_element(parent, "physical" if not is_vlan else "vlan")
            if "name" in interface_data:
                self.create_element(interface, "name", text=interface_data["name"])
            return interface
    
    def create_zone(self, parent, zone_data, interface_mapping=None):
        """
        创建区域元素
        
        Args:
            parent: 父元素
            zone_data: 区域数据
            interface_mapping: 接口映射
            
        Returns:
            Element: 创建的区域元素
        """
        if not zone_data.get('name'):
            log(_("warning.zone_missing_name"), "warning")
            return None
        
        zone_name = zone_data['name']
        
        # 创建zone元素
        zone_elem = self.create_element(parent, "zone", namespace=NAMESPACE_MAPPING["security-zone"])
        
        # 添加名称
        self.create_element(zone_elem, "name", text=zone_name)
        
        # 添加描述（如果有）
        description = zone_data.get('description', '')
        if description:
            self.create_element(zone_elem, "description", text=description)
        
        # 添加接口
        interfaces = zone_data.get('interfaces', [])
        if isinstance(interfaces, list):
            for intf_name in interfaces:
                # 检查接口映射
                if interface_mapping and intf_name in interface_mapping:
                    mapped_name = interface_mapping[intf_name]
                    intf_elem = self.create_element(zone_elem, "interface")
                    self.create_element(intf_elem, "name", text=mapped_name)
                elif interface_mapping is None:
                    # 没有映射时直接使用接口名
                    intf_elem = self.create_element(zone_elem, "interface")
                    self.create_element(intf_elem, "name", text=intf_name)
        
        return zone_elem
    
    def create_route(self, parent, route_data):
        """
        创建路由元素
        
        Args:
            parent: 父元素
            route_data: 路由数据
            
        Returns:
            Element: 创建的路由元素
        """
        # 获取路由信息
        destination = route_data.get('destination', '0.0.0.0/0')
        gateway = route_data.get('gateway', '')
        interface = route_data.get('interface', '')
        distance = route_data.get('distance', route_data.get('metric', '5'))
        description = route_data.get('description', route_data.get('comment', ''))
        blackhole = route_data.get('blackhole', False)
        
        # 创建ipv4-route元素
        ipv4_route = self.create_element(parent, "ipv4-route")
        self.create_element(ipv4_route, "destination", text=destination)
        
        # 特殊情况：如果路由的gateway直接就是"blackhole"
        if gateway == "blackhole":
            blackhole_node = self.create_element(ipv4_route, "next-hop")
            self.create_element(blackhole_node, "next-hop", text="blackhole")
            self.create_element(blackhole_node, "distance", text=str(distance))
            self.create_element(blackhole_node, "enable", text="true")
            if description:
                self.create_element(blackhole_node, "descr", text=description)
        
        # 处理正常网关/接口的next-hop
        elif gateway or interface:
            next_hop_node = self.create_element(ipv4_route, "next-hop")
            
            # 设置next-hop键值
            next_hop_text = gateway
            if interface:
                # 如果同时有网关和接口，使用格式"网关%接口"
                if gateway:
                    next_hop_text = f"{gateway}%{interface}"
                else:
                    next_hop_text = interface
            
            self.create_element(next_hop_node, "next-hop", text=next_hop_text)
            self.create_element(next_hop_node, "distance", text=str(distance))
            self.create_element(next_hop_node, "enable", text="true")
            if description:
                self.create_element(next_hop_node, "descr", text=description)
        
        # 如果指定了黑洞路由（额外添加blackhole next-hop），且尚未添加blackhole
        if blackhole and gateway != "blackhole":
            blackhole_node = self.create_element(ipv4_route, "next-hop")
            self.create_element(blackhole_node, "next-hop", text="blackhole")
            
            # 黑洞路由通常使用高优先级（如254）
            blackhole_distance = route_data.get('blackhole_distance', '254')
            self.create_element(blackhole_node, "distance", text=str(blackhole_distance))
            self.create_element(blackhole_node, "enable", text="true")
            if description:
                self.create_element(blackhole_node, "descr", text=description)
        
        return ipv4_route
    
    def create_service_object(self, parent, service_data):
        """
        创建服务对象元素
        
        Args:
            parent (Element): 父元素
            service_data (dict): 服务对象数据
            
        Returns:
            Element: 创建的服务对象元素
        """
        try:
            # 检查必要字段
            if "name" not in service_data:
                log(_("error.service_missing_name"), "error")
                return None
                
            # 创建服务对象元素
            service = self.create_element(parent, "service")
            
            # 添加名称
            self.create_element(service, "name", text=service_data["name"])
            
            # 添加描述(如果有)
            if "description" in service_data and service_data["description"]:
                self.create_element(service, "description", text=service_data["description"])
                
            # 添加协议和端口
            if "protocol" in service_data:
                protocol = service_data["protocol"].lower()
                self.create_element(service, "protocol", text=protocol)
                
                # 添加端口(如果是TCP或UDP)
                if protocol in ["tcp", "udp"]:
                    if "port" in service_data:
                        port_element = self.create_element(service, "port")
                        self.create_element(port_element, "value", text=service_data["port"])
                    elif "start_port" in service_data and "end_port" in service_data:
                        port_element = self.create_element(service, "port")
                        self.create_element(port_element, "start", text=service_data["start_port"])
                        self.create_element(port_element, "end", text=service_data["end_port"])
                
                # 添加ICMP类型(如果是ICMP)
                elif protocol == "icmp" and "icmp_type" in service_data:
                    self.create_element(service, "icmp-type", text=service_data["icmp_type"])
                    
            return service
            
        except Exception as e:
            log(_("error.create_service_object_failed", 
                 name=service_data.get("name", "unknown"), 
                 error=str(e)), "error")
            return None