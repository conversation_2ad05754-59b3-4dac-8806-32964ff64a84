#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
XML集成器模块
提供XML模板集成功能，将转换后的策略和规则集成到XML模板中
"""

from typing import List, Dict, Optional, Any
from lxml import etree
from engine.utils.logger import log, _

# 命名空间映射表（借鉴旧架构的逻辑）
NAMESPACE_MAPPING = {
    "security-policy": "urn:ruijie:ntos:params:xml:ns:yang:security-policy",
    "nat": "urn:ruijie:ntos:params:xml:ns:yang:nat",
    "interface": "urn:ruijie:ntos:params:xml:ns:yang:interface",
    "routing": "urn:ruijie:ntos:params:xml:ns:yang:routing",
    "security-zone": "urn:ruijie:ntos:params:xml:ns:yang:security-zone",
    "network-obj": "urn:ruijie:ntos:params:xml:ns:yang:network-obj",
    "vlan": "urn:ruijie:ntos:params:xml:ns:yang:vlan",
    "system": "urn:ruijie:ntos:params:xml:ns:yang:system",
    "bridge": "urn:ruijie:ntos:params:xml:ns:yang:bridge",
    "if-mon": "urn:ruijie:ntos:params:xml:ns:yang:if-mon",
    "local-defend": "urn:ruijie:ntos:params:xml:ns:yang:local-defend"
}


class XMLIntegrator:
    """
    XML集成器
    负责将转换后的安全策略和NAT规则集成到XML模板中
    """
    
    def __init__(self, name_mapping_manager=None):
        """
        初始化XML集成器

        Args:
            name_mapping_manager: 名称映射管理器（可选）
        """
        self.security_policy_generator = None
        self.nat_generator = None

        # 名称映射管理器
        self.name_mapping_manager = name_mapping_manager
        if self.name_mapping_manager is None:
            try:
                from engine.utils.name_mapping_manager import NameMappingManager
                self.name_mapping_manager = NameMappingManager()
            except ImportError:
                # 如果名称映射管理器不可用，设置为None
                self.name_mapping_manager = None

    def _clean_name_for_yang(self, name: str, obj_type: str = "generic", context: str = "") -> str:
        """
        清理名称以符合YANG模型约束

        Args:
            name: 原始名称
            obj_type: 对象类型
            context: 上下文信息

        Returns:
            str: 清理后的名称
        """
        if not name:
            return "unnamed"

        # 如果有名称映射管理器，使用它来清理名称
        if self.name_mapping_manager:
            try:
                return self.name_mapping_manager.register_name_mapping(
                    obj_type, name, context=context
                )
            except Exception as e:
                log(_("xml_integrator.name_mapping_failed",
                      name=name, error=str(e)), "warning")

        # 如果没有名称映射管理器，使用简单的清理逻辑
        return self._simple_clean_name(name)

    def _simple_clean_name(self, name: str) -> str:
        """
        简单的名称清理（不依赖外部模块）

        Args:
            name: 原始名称

        Returns:
            str: 清理后的名称
        """
        import re
        if not name:
            return "unnamed"

        # 移除YANG模型不允许的字符
        cleaned = re.sub(r'[`~!@#$%^&*+|{};:"\'\\/<>?,]', '_', str(name))

        # 替换连续的下划线为单个下划线
        cleaned = re.sub(r'_+', '_', cleaned)

        # 移除开头和结尾的下划线
        cleaned = cleaned.strip('_')

        # 如果清理后为空，使用默认名称
        if not cleaned.strip():
            cleaned = "unnamed_object"

        # 限制长度
        if len(cleaned) > 64:
            cleaned = cleaned[:64].rstrip('_')

        return cleaned

    def _clean_description_for_yang(self, description: str) -> str:
        """
        清理描述字段以符合YANG模型约束

        Args:
            description: 原始描述

        Returns:
            str: 清理后的描述
        """
        import re
        if not description:
            return ""

        # 移除YANG模型不允许的字符，但保留更多可读字符
        cleaned = re.sub(r'[`~!#$%^&*+|{};:"\'\\/<>?]', ' ', str(description))

        # 清理多余的空格
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()

        # 限制长度
        if len(cleaned) > 255:
            cleaned = cleaned[:255].rstrip()

        return cleaned

    def _set_namespace_if_needed(self, element: etree.Element, tag_name: str):
        """
        为元素设置命名空间（如果需要的话）

        Args:
            element: XML元素
            tag_name: 标签名
        """
        if tag_name in NAMESPACE_MAPPING:
            namespace_uri = NAMESPACE_MAPPING[tag_name]
            # 检查是否已经有命名空间声明
            if element.get("xmlns") != namespace_uri:
                element.set("xmlns", namespace_uri)
                log(_("xml_integrator.namespace_set"), "debug",
                    tag=tag_name, namespace=namespace_uri)
    
    def integrate_security_policies(self, template_root: etree.Element, 
                                   security_policies: List[Dict]) -> bool:
        """
        将安全策略集成到XML模板中
        
        Args:
            template_root: XML模板根元素
            security_policies: 安全策略列表
            
        Returns:
            bool: 集成是否成功
        """
        try:
            if not security_policies:
                log(_("xml_integrator.no_security_policies_to_integrate"), "info")
                return True
            
            # 使用现有的SecurityPolicyGenerator进行集成
            from engine.generators.security_policy_generator import SecurityPolicyGenerator
            
            if self.security_policy_generator is None:
                self.security_policy_generator = SecurityPolicyGenerator()
            
            # 将策略字典转换为XML元素并集成
            successful_integrations = 0
            failed_integrations = 0

            for policy_dict in security_policies:
                try:
                    # 检查策略数据格式并进行适当转换
                    if isinstance(policy_dict, dict):
                        # 如果是XML字典格式（包含tag、children等字段），转换为策略配置格式
                        if 'tag' in policy_dict and 'children' in policy_dict:
                            policy_config = self._convert_xml_dict_to_policy_config(policy_dict)
                        else:
                            # 如果已经是策略配置格式，直接使用
                            policy_config = policy_dict
                            log(_("xml_integrator.using_policy_config_format"), "debug",
                                name=policy_dict.get('name', 'unknown'))
                    else:
                        failed_integrations += 1
                        log(_("xml_integrator.invalid_policy_format"), "warning",
                            format=str(type(policy_dict)))
                        continue

                    # 检查转换是否成功
                    if policy_config is None:
                        failed_integrations += 1
                        policy_name = policy_dict.get('name', 'unknown')
                        log(_("xml_integrator.policy_conversion_failed"), "warning",
                            name=policy_name)
                        continue

                    # 添加调试日志
                    log(f"XMLIntegrator接收到的策略配置: {policy_config}", "info")

                    # 创建策略元素
                    policy_element = self.security_policy_generator._create_policy_element(policy_config)
                    if policy_element is not None:
                        # 找到或创建安全策略容器
                        security_policy_container = self._find_or_create_security_policy_container(template_root)
                        security_policy_container.append(policy_element)
                        successful_integrations += 1
                        log(_("xml_integrator.policy_integrated_successfully"), "debug",
                            name=policy_config.get('name', 'unknown'))
                    else:
                        failed_integrations += 1
                        log(_("xml_integrator.policy_integration_failed"), "warning",
                            name=policy_config.get('name', 'unknown'))

                except Exception as e:
                    failed_integrations += 1
                    policy_name = policy_dict.get('name', 'unknown')
                    log(_("xml_integrator.policy_integration_exception"), "error",
                        name=policy_name, error=str(e))

            # 记录集成结果
            log(_("xml_integrator.security_policies_integrated"), "info",
                count=len(security_policies), successful=successful_integrations, failed=failed_integrations)

            # 如果有失败的集成，返回False
            if failed_integrations > 0:
                log(_("xml_integrator.some_policies_failed"), "warning",
                    failed=failed_integrations, total=len(security_policies))
                return False

            return True
            
        except Exception as e:
            log(_("xml_integrator.security_policy_integration_failed"), "error", 
                error=str(e))
            return False
    
    def integrate_nat_rules(self, template_root: etree.Element, 
                           nat_rules: List[Dict]) -> bool:
        """
        将NAT规则集成到XML模板中
        
        Args:
            template_root: XML模板根元素
            nat_rules: NAT规则列表
            
        Returns:
            bool: 集成是否成功
        """
        try:
            if not nat_rules:
                log(_("xml_integrator.no_nat_rules_to_integrate"), "info")
                return True
            
            # 使用现有的NATGenerator进行集成
            from engine.generators.nat_generator import NATGenerator
            
            if self.nat_generator is None:
                self.nat_generator = NATGenerator()
            
            # 将NAT规则字典转换为XML元素并集成
            for i, nat_rule_dict in enumerate(nat_rules):
                # 如果nat_rule_dict是XML字典格式，需要转换为NAT规则配置格式
                nat_rule_config = self._convert_xml_dict_to_nat_rule_config(nat_rule_dict)
                # 创建NAT规则元素
                nat_rule_element = self.nat_generator._create_nat_rule_element(nat_rule_config)
                if nat_rule_element is not None:
                    # 找到或创建NAT容器
                    nat_container = self._find_or_create_nat_container(template_root)
                    nat_container.append(nat_rule_element)
            
            log(_("xml_integrator.nat_rules_integrated"), "info", 
                count=len(nat_rules))
            return True
            
        except Exception as e:
            log(_("xml_integrator.nat_rule_integration_failed"), "error", 
                error=str(e))
            return False
    
    def _convert_xml_dict_to_policy_config(self, xml_dict: Dict) -> Dict:
        """
        将XML字典格式转换为策略配置格式

        Args:
            xml_dict: XML字典格式的策略

        Returns:
            Dict: 策略配置格式
        """
        # 如果已经是策略配置格式，直接返回
        if 'name' in xml_dict and 'action' in xml_dict and 'tag' not in xml_dict:
            return xml_dict

        # 如果是XML字典格式（包含tag、children等字段），需要解析
        if 'tag' in xml_dict and xml_dict['tag'] == 'policy':
            policy_config = {}

            # 解析子元素
            children = xml_dict.get('children', [])
            for child in children:
                if 'tag' in child:
                    tag_name = child['tag']
                    tag_value = child.get('text', '')

                    # 处理基本字段
                    if tag_name in ['name', 'action', 'config-source', 'enabled', 'group-name',
                                   'time-range', 'session-timeout', 'description']:
                        policy_config[tag_name] = tag_value

                    # 处理区域字段 - 修正为YANG模型中的正确字段名
                    elif tag_name in ['source-zone', 'dest-zone']:
                        # 这些字段可能有子元素
                        if 'children' in child:
                            zone_config = {}
                            for zone_child in child['children']:
                                if zone_child.get('tag') == 'name':
                                    zone_config['name'] = zone_child.get('text', '')
                            policy_config[tag_name] = zone_config
                        else:
                            policy_config[tag_name] = {'name': tag_value}

                    # 兼容旧的destination-zone字段名，转换为dest-zone
                    elif tag_name == 'destination-zone':
                        if 'children' in child:
                            zone_config = {}
                            for zone_child in child['children']:
                                if zone_child.get('tag') == 'name':
                                    zone_config['name'] = zone_child.get('text', '')
                            policy_config['dest-zone'] = zone_config
                        else:
                            policy_config['dest-zone'] = {'name': tag_value}

                    # 处理接口字段
                    elif tag_name in ['source-interface', 'dest-interface']:
                        if 'children' in child:
                            interface_config = {}
                            for interface_child in child['children']:
                                if interface_child.get('tag') == 'name':
                                    interface_config['name'] = interface_child.get('text', '')
                            policy_config[tag_name] = interface_config
                        else:
                            policy_config[tag_name] = {'name': tag_value}

                    # 处理地址字段
                    elif tag_name in ['source-address', 'destination-address']:
                        if 'children' in child:
                            address_list = []
                            for address_child in child['children']:
                                if address_child.get('tag') == 'name':
                                    address_list.append(address_child.get('text', ''))
                            policy_config[tag_name] = address_list
                        else:
                            policy_config[tag_name] = [tag_value] if tag_value else []

                    # 处理服务字段
                    elif tag_name == 'service':
                        if 'children' in child:
                            service_list = []
                            for service_child in child['children']:
                                if service_child.get('tag') == 'name':
                                    service_list.append(service_child.get('text', ''))
                            policy_config[tag_name] = service_list
                        else:
                            policy_config[tag_name] = [tag_value] if tag_value else []



            # 验证必需字段
            if 'name' not in policy_config or not policy_config['name']:
                # 如果没有有效的name字段，这是一个无效策略
                log(_("xml_integrator.invalid_policy_no_name"), "warning")
                return None

            # 设置默认值
            if 'action' not in policy_config:
                policy_config['action'] = 'permit'
            if 'config-source' not in policy_config:
                policy_config['config-source'] = 'manual'

            return policy_config

        # 如果格式不识别，返回默认配置
        log(_("xml_integrator.unknown_policy_format"), "warning", format=str(xml_dict))
        return {
            'name': 'unknown',
            'action': 'permit',
            'config-source': 'manual'
        }
    
    def _convert_xml_dict_to_nat_rule_config(self, xml_dict: Dict) -> Dict:
        """
        将XML字典格式转换为NAT规则配置格式

        Args:
            xml_dict: XML字典格式的NAT规则

        Returns:
            Dict: NAT规则配置格式
        """
        # 如果已经是NAT规则配置格式（包含static-dnat44、static-snat44或dynamic-snat44键），直接返回
        nat_rule_types = ['static-dnat44', 'static-snat44', 'dynamic-snat44']
        if any(rule_type in xml_dict for rule_type in nat_rule_types):
            return xml_dict

        # 如果是简化格式，需要转换为标准格式
        if 'type' in xml_dict:
            rule_type = xml_dict.get('type', 'static-dnat44')
            nat_rule_config = {
                'name': xml_dict.get('name', 'unknown'),
                'rule_en': xml_dict.get('rule_en', True),
                rule_type: {
                    'match': xml_dict.get('match', {}),
                    'translate-to': xml_dict.get('translate-to', {})
                }
            }
            return nat_rule_config

        # 如果格式不识别，返回默认配置
        log(_("xml_integrator.unknown_nat_rule_format"), "warning", format=str(xml_dict))
        return xml_dict
    
    def _find_or_create_security_policy_container(self, root: etree.Element) -> etree.Element:
        """
        查找或创建安全策略容器

        Args:
            root: XML根元素

        Returns:
            etree.Element: 安全策略容器元素
        """
        # 查找VRF元素（考虑命名空间）
        vrf = root.find('.//vrf')
        if vrf is None:
            # 尝试使用默认命名空间查找
            vrf = root.find('.//{urn:ruijie:ntos}vrf')
            if vrf is not None:
                log(_("xml_integrator.vrf_found_with_namespace"), "debug")
        else:
            log(_("xml_integrator.vrf_found_without_namespace"), "debug")

        if vrf is None:
            # 创建VRF元素
            vrf = etree.SubElement(root, 'vrf')
            # 创建name子元素而不是name属性（符合YANG模型）
            name_elem = etree.SubElement(vrf, 'name')
            name_elem.text = 'main'
        else:
            # 检查VRF是否有正确的name子元素（考虑命名空间）
            name_elem = vrf.find('./name')
            if name_elem is None:
                # 尝试使用默认命名空间查找
                name_elem = vrf.find('./{urn:ruijie:ntos}name')

            if name_elem is None:
                # 如果没有name子元素，创建一个（插入到VRF开头）
                name_elem = etree.Element('name')
                name_elem.text = 'main'
                vrf.insert(0, name_elem)
                log(_("xml_integrator.vrf_name_created"), "debug")
            elif name_elem.text != 'main':
                # 如果name值不是main，修正为main
                name_elem.text = 'main'
                log(_("xml_integrator.vrf_name_updated"), "debug")
            else:
                log(_("xml_integrator.vrf_name_found"), "debug")

        # 查找安全策略容器
        security_policy_container = vrf.find('.//security-policy')
        if security_policy_container is None:
            # 创建安全策略容器（使用统一的命名空间设置方法）
            security_policy_container = etree.SubElement(vrf, 'security-policy')
            self._set_namespace_if_needed(security_policy_container, 'security-policy')
        else:
            # 如果容器已存在，确保它有正确的命名空间
            self._set_namespace_if_needed(security_policy_container, 'security-policy')

        return security_policy_container
    
    def _find_or_create_nat_container(self, root: etree.Element) -> etree.Element:
        """
        查找或创建NAT容器

        Args:
            root: XML根元素

        Returns:
            etree.Element: NAT容器元素
        """
        # 查找VRF元素（考虑命名空间）
        vrf = root.find('.//vrf')
        if vrf is None:
            # 尝试使用默认命名空间查找
            vrf = root.find('.//{urn:ruijie:ntos}vrf')
            if vrf is not None:
                log(_("xml_integrator.vrf_found_with_namespace"), "debug")
        else:
            log(_("xml_integrator.vrf_found_without_namespace"), "debug")

        if vrf is None:
            # 创建VRF元素
            vrf = etree.SubElement(root, 'vrf')
            # 创建name子元素而不是name属性（符合YANG模型）
            name_elem = etree.SubElement(vrf, 'name')
            name_elem.text = 'main'
        else:
            # 检查VRF是否有正确的name子元素（考虑命名空间）
            name_elem = vrf.find('./name')
            if name_elem is None:
                # 尝试使用默认命名空间查找
                name_elem = vrf.find('./{urn:ruijie:ntos}name')

            if name_elem is None:
                # 如果没有name子元素，创建一个（插入到VRF开头）
                name_elem = etree.Element('name')
                name_elem.text = 'main'
                vrf.insert(0, name_elem)
                log(_("xml_integrator.vrf_name_created"), "debug")
            elif name_elem.text != 'main':
                # 如果name值不是main，修正为main
                name_elem.text = 'main'
                log(_("xml_integrator.vrf_name_updated"), "debug")
            else:
                log(_("xml_integrator.vrf_name_found"), "debug")

        # 查找NAT容器
        nat_container = vrf.find('.//nat')
        if nat_container is None:
            # 创建NAT容器（使用统一的命名空间设置方法）
            nat_container = etree.SubElement(vrf, 'nat')
            self._set_namespace_if_needed(nat_container, 'nat')
        else:
            # 如果容器已存在，确保它有正确的命名空间
            self._set_namespace_if_needed(nat_container, 'nat')

        return nat_container

    def integrate_bridge_interfaces(self, template_root: etree.Element,
                                   operation_mode_result: Dict) -> bool:
        """
        将桥接接口配置集成到XML模板中（透明模式）

        Args:
            template_root: XML模板根元素
            operation_mode_result: 操作模式处理结果

        Returns:
            bool: 集成是否成功
        """
        try:
            # 检查是否为透明模式
            if operation_mode_result.get("mode") != "transparent":
                log(_("xml_integrator.not_transparent_mode"), "debug")
                return True

            # 获取桥接配置
            bridge_config = operation_mode_result.get("bridge_config", {})
            if not bridge_config or not bridge_config.get("enabled"):
                log(_("xml_integrator.no_bridge_config"), "info")
                return True

            # 创建桥接接口配置
            bridge_name = bridge_config.get("name", "br0")
            slave_interfaces = bridge_config.get("slave_interfaces", [])

            if not slave_interfaces:
                log(_("xml_integrator.no_slave_interfaces"), "warning")
                return True

            # 查找或创建桥接容器
            bridge_container = self._find_or_create_bridge_container(template_root)

            # 创建桥接接口元素
            bridge_element = etree.SubElement(bridge_container, "bridge-interface")

            # 设置桥接接口名称
            name_element = etree.SubElement(bridge_element, "name")
            # 清理桥接接口名称以符合YANG约束
            clean_bridge_name = self._clean_name_for_yang(
                bridge_name, 'bridge_interfaces', "桥接接口"
            )
            name_element.text = clean_bridge_name

            # 设置启用状态
            enabled_element = etree.SubElement(bridge_element, "enabled")
            enabled_element.text = "true"

            # 添加slave接口
            for slave_interface in slave_interfaces:
                slave_element = etree.SubElement(bridge_element, "slave")
                slave_element.text = slave_interface

            log(_("xml_integrator.bridge_interface_integrated"), "info",
                bridge_name=bridge_name, slave_count=len(slave_interfaces))

            return True

        except Exception as e:
            log(_("xml_integrator.bridge_integration_failed"), "error",
                error=str(e))
            return False

    def integrate_interface_working_modes(self, template_root: etree.Element,
                                        interface_processing_result: Dict) -> bool:
        """
        将接口配置集成到XML模板中（包括工作模式、IP配置等）

        Args:
            template_root: XML模板根元素
            interface_processing_result: 接口处理结果

        Returns:
            bool: 集成是否成功
        """
        try:
            converted_interfaces = interface_processing_result.get("converted", {})
            if not converted_interfaces:
                log(_("xml_integrator.no_interfaces_to_integrate"), "info")
                return True

            # 设置接口映射上下文，供VLAN接口使用
            self._current_interface_mapping = self._extract_interface_mapping(converted_interfaces)

            # 查找或创建接口容器 - 在VRF节点下
            interface_container = self._find_or_create_interface_container_in_vrf(template_root)

            # 跳过所有接口的传统处理，现在完全依赖接口处理阶段的XML片段
            log("跳过传统接口处理，使用接口处理阶段的XML片段", "debug")

            # 注意：所有接口（物理接口和VLAN接口）现在都由接口处理阶段的XML片段处理
            # 这避免了重复的接口生成，确保XML结构的一致性

            log(_("xml_integrator.interface_working_modes_integrated"), "info",
                count=len(converted_interfaces))

            return True

        except Exception as e:
            log(_("xml_integrator.interface_working_mode_integration_failed"), "error",
                error=str(e))
            return False

    def _find_or_create_bridge_container(self, root: etree.Element) -> etree.Element:
        """
        查找或创建桥接容器

        Args:
            root: XML根元素

        Returns:
            etree.Element: 桥接容器元素
        """
        # 查找桥接容器
        bridge_container = root.find('.//bridge')
        if bridge_container is None:
            # 创建桥接容器
            bridge_container = etree.SubElement(root, 'bridge')
            self._set_namespace_if_needed(bridge_container, 'bridge')
        else:
            # 如果容器已存在，确保它有正确的命名空间
            self._set_namespace_if_needed(bridge_container, 'bridge')

        return bridge_container

    def _find_or_create_interface_container(self, root: etree.Element) -> etree.Element:
        """
        查找或创建接口容器

        Args:
            root: XML根元素

        Returns:
            etree.Element: 接口容器元素
        """
        # 查找接口容器
        interface_container = root.find('.//interface')
        if interface_container is None:
            # 创建接口容器
            interface_container = etree.SubElement(root, 'interface')
            self._set_namespace_if_needed(interface_container, 'interface')
        else:
            # 如果容器已存在，确保它有正确的命名空间
            self._set_namespace_if_needed(interface_container, 'interface')

        return interface_container

    def _find_or_create_interface_element(self, container: etree.Element, interface_name: str, interface_config: Dict = None) -> etree.Element:
        """
        查找或创建接口元素

        Args:
            container: 接口容器
            interface_name: 接口名称
            interface_config: 接口配置（用于判断接口类型）

        Returns:
            etree.Element: 接口元素
        """
        # 确定接口类型
        interface_type = self._determine_interface_xml_type(interface_config)

        # 查找现有接口元素
        for interface_elem in container.findall(f'.//{interface_type}'):
            name_elem = interface_elem.find('name')
            if name_elem is not None and name_elem.text == interface_name:
                return interface_elem

        # 创建新的接口元素
        interface_element = etree.SubElement(container, interface_type)
        name_element = etree.SubElement(interface_element, 'name')
        # 清理接口名称以符合YANG约束
        clean_interface_name = self._clean_name_for_yang(
            interface_name, 'interfaces', f"{interface_type}接口"
        )
        name_element.text = clean_interface_name

        return interface_element

    def _determine_interface_xml_type(self, interface_config: Dict = None) -> str:
        """
        确定接口的XML类型 - 根据NTOS YANG模型使用正确的元素名称

        Args:
            interface_config: 接口配置

        Returns:
            str: XML接口类型
        """
        if not interface_config:
            return 'physical'  # 默认为物理接口

        interface_type = interface_config.get('type', 'physical')
        working_mode = interface_config.get('working_mode', 'route')
        is_subinterface = interface_config.get('is_subinterface', False)

        # VLAN子接口判断
        if interface_type == 'vlan' or is_subinterface or interface_config.get('vlan_id'):
            return 'vlan'  # VLAN接口使用vlan元素
        elif interface_type == 'loopback':
            return 'loopback'  # 环回接口
        elif working_mode == 'bridge' and interface_config.get('name', '').startswith('br'):
            return 'bridge'  # 桥接接口
        else:
            return 'physical'  # 物理接口

    def _integrate_access_control(self, interface_element: etree.Element, access_control: Dict):
        """
        集成访问控制配置

        Args:
            interface_element: 接口元素
            access_control: 访问控制配置
        """
        # 查找或创建访问控制容器
        access_control_element = interface_element.find('access-control')
        if access_control_element is None:
            access_control_element = etree.SubElement(interface_element, 'access-control')

        # 设置各种访问控制选项
        for service, enabled in access_control.items():
            service_element = access_control_element.find(service)
            if service_element is None:
                service_element = etree.SubElement(access_control_element, service)
            service_element.text = "true" if enabled else "false"

    def _integrate_basic_interface_config(self, interface_element: etree.Element, interface_config: Dict):
        """
        集成基本接口配置

        Args:
            interface_element: 接口元素
            interface_config: 接口配置
        """
        # 设置启用状态
        enabled_element = interface_element.find("enabled")
        if enabled_element is None:
            enabled_element = etree.SubElement(interface_element, "enabled")
        enabled_element.text = "true" if interface_config.get("enabled", True) else "false"

        # 设置描述
        description = interface_config.get("description")
        if description:
            desc_element = interface_element.find("description")
            if desc_element is None:
                desc_element = etree.SubElement(interface_element, "description")
            # 清理描述字段以符合YANG约束
            clean_description = self._clean_description_for_yang(description)
            if clean_description:  # 只有清理后不为空才设置
                desc_element.text = clean_description

        # 设置MTU
        mtu = interface_config.get("mtu")
        if mtu and mtu != 1500:  # 只有非默认值才设置
            mtu_element = interface_element.find("mtu")
            if mtu_element is None:
                mtu_element = etree.SubElement(interface_element, "mtu")
            mtu_element.text = str(mtu)

        # 添加默认的网络栈配置
        self._add_default_network_stack(interface_element)

        # 添加默认的监控配置
        self._add_default_monitor_config(interface_element)

        # 添加默认的流控配置
        self._add_default_flow_control(interface_element)

        # 添加默认的IP-MAC绑定配置
        self._add_default_ip_mac_bind(interface_element)

        # 添加默认的反向路径检查
        self._add_default_reverse_path(interface_element)

        # 添加默认的snooping配置
        self._add_default_snooping(interface_element)

    def _integrate_ip_configuration(self, interface_element: etree.Element, interface_config: Dict):
        """
        集成IP配置

        Args:
            interface_element: 接口元素
            interface_config: 接口配置
        """
        # 查找或创建IPv4元素
        ipv4_element = interface_element.find("ipv4")
        if ipv4_element is None:
            ipv4_element = etree.SubElement(interface_element, "ipv4")

        # 设置IPv4启用状态
        ip_address = interface_config.get("ip_address")
        dhcp_enabled = interface_config.get("dhcp_enabled", False)
        pppoe_enabled = interface_config.get("pppoe_enabled", False)

        enabled_element = ipv4_element.find("enabled")
        if enabled_element is None:
            enabled_element = etree.SubElement(ipv4_element, "enabled")

        if ip_address or dhcp_enabled or pppoe_enabled:
            enabled_element.text = "true"

            if dhcp_enabled:
                # DHCP配置
                dhcp_element = ipv4_element.find("dhcp")
                if dhcp_element is None:
                    dhcp_element = etree.SubElement(ipv4_element, "dhcp")
                    etree.SubElement(dhcp_element, "enabled").text = "true"

            elif pppoe_enabled:
                # PPPoE配置 - 确保有tunnel key（符合YANG模型要求）
                pppoe_element = ipv4_element.find("pppoe")
                if pppoe_element is None:
                    pppoe_element = etree.SubElement(ipv4_element, "pppoe")
                    connection_element = etree.SubElement(pppoe_element, "connection")

                    # 添加tunnel key（YANG模型要求）
                    tunnel_id = interface_config.get("tunnel_id", 1)  # 默认使用1
                    etree.SubElement(connection_element, "tunnel").text = str(tunnel_id)

                    # 启用连接
                    etree.SubElement(connection_element, "enabled").text = "true"

                    username = interface_config.get("pppoe_username", "")
                    password = interface_config.get("pppoe_password", "")

                    if username:
                        etree.SubElement(connection_element, "user").text = username
                    if password:
                        etree.SubElement(connection_element, "password").text = "=*-#!$_2g+awtqJWZWf+C6zl4RLQ=="  # 加密格式

            elif ip_address:
                # 静态IP配置
                address_element = ipv4_element.find("address")
                if address_element is None:
                    address_element = etree.SubElement(ipv4_element, "address")

                ip_element = address_element.find("ip")
                if ip_element is None:
                    ip_element = etree.SubElement(address_element, "ip")
                ip_element.text = ip_address
        else:
            enabled_element.text = "false"

    def _integrate_ipv6_configuration(self, interface_element: etree.Element, interface_config: Dict):
        """
        集成IPv6配置

        Args:
            interface_element: 接口元素
            interface_config: 接口配置
        """
        ipv6_config = interface_config.get("ipv6", {})

        # 查找或创建IPv6元素
        ipv6_element = interface_element.find("ipv6")
        if ipv6_element is None:
            ipv6_element = etree.SubElement(interface_element, "ipv6")

        # 设置IPv6启用状态
        enabled_element = ipv6_element.find("enabled")
        if enabled_element is None:
            enabled_element = etree.SubElement(ipv6_element, "enabled")

        ipv6_enabled = ipv6_config.get("enabled", False)
        enabled_element.text = "true" if ipv6_enabled else "false"

        if ipv6_enabled:
            # IPv6地址配置
            ipv6_address = ipv6_config.get("address")
            if ipv6_address:
                address_element = ipv6_element.find("address")
                if address_element is None:
                    address_element = etree.SubElement(ipv6_element, "address")

                ip_element = address_element.find("ip")
                if ip_element is None:
                    ip_element = etree.SubElement(address_element, "ip")
                ip_element.text = ipv6_address

            # IPv6 DHCP配置
            if ipv6_config.get("dhcp_enabled", False):
                dhcp_element = ipv6_element.find("dhcp")
                if dhcp_element is None:
                    dhcp_element = etree.SubElement(ipv6_element, "dhcp")
                    etree.SubElement(dhcp_element, "enabled").text = "true"

    def _integrate_bandwidth_configuration(self, interface_element: etree.Element, interface_config: Dict):
        """
        集成带宽配置

        Args:
            interface_element: 接口元素
            interface_config: 接口配置
        """
        # 上行带宽
        upload_bandwidth = interface_config.get("upload_bandwidth")
        if upload_bandwidth:
            upload_element = interface_element.find("upload-bandwidth")
            if upload_element is None:
                upload_element = etree.SubElement(interface_element, "upload-bandwidth")

            value_element = upload_element.find("upload-bandwidth-value")
            if value_element is None:
                value_element = etree.SubElement(upload_element, "upload-bandwidth-value")
            value_element.text = str(upload_bandwidth["value"])

            unit_element = upload_element.find("upload-bandwidth-unit")
            if unit_element is None:
                unit_element = etree.SubElement(upload_element, "upload-bandwidth-unit")
            unit_element.text = upload_bandwidth["unit"]

        # 下行带宽
        download_bandwidth = interface_config.get("download_bandwidth")
        if download_bandwidth:
            download_element = interface_element.find("download-bandwidth")
            if download_element is None:
                download_element = etree.SubElement(interface_element, "download-bandwidth")

            value_element = download_element.find("download-bandwidth-value")
            if value_element is None:
                value_element = etree.SubElement(download_element, "download-bandwidth-value")
            value_element.text = str(download_bandwidth["value"])

            unit_element = download_element.find("download-bandwidth-unit")
            if unit_element is None:
                unit_element = etree.SubElement(download_element, "download-bandwidth-unit")
            unit_element.text = download_bandwidth["unit"]

    def _add_default_network_stack(self, interface_element: etree.Element):
        """添加默认的网络栈配置"""
        network_stack = interface_element.find("network-stack")
        if network_stack is None:
            network_stack = etree.SubElement(interface_element, "network-stack")

            # IPv4网络栈配置
            ipv4_stack = etree.SubElement(network_stack, "ipv4")
            etree.SubElement(ipv4_stack, "arp-ignore").text = "check-interface-and-subnet"

    def _add_default_monitor_config(self, interface_element: etree.Element):
        """添加默认的监控配置"""
        monitor = interface_element.find("monitor")
        if monitor is None:
            monitor = etree.SubElement(interface_element, "monitor")
            monitor.set("xmlns", "urn:ruijie:ntos:params:xml:ns:yang:if-mon")

            # 监控配置
            etree.SubElement(monitor, "notify-up-drop-rate-threshold").text = "1000"
            etree.SubElement(monitor, "notify-down-drop-rate-threshold").text = "1000"
            etree.SubElement(monitor, "if-notify-enable").text = "false"
            etree.SubElement(monitor, "notify-up-usage-threshold").text = "100"
            etree.SubElement(monitor, "notify-down-usage-threshold").text = "100"
            etree.SubElement(monitor, "notify-up-speed-threshold").text = "100000000"
            etree.SubElement(monitor, "notify-down-speed-threshold").text = "100000000"

    def _add_default_flow_control(self, interface_element: etree.Element):
        """添加默认的流控配置"""
        flow_control = interface_element.find("flow-control")
        if flow_control is None:
            flow_control = etree.SubElement(interface_element, "flow-control")
            flow_control.set("xmlns", "urn:ruijie:ntos:params:xml:ns:yang:flow-control")
            etree.SubElement(flow_control, "enabled").text = "false"

    def _add_default_ip_mac_bind(self, interface_element: etree.Element):
        """添加默认的IP-MAC绑定配置"""
        ip_mac_bind = interface_element.find("ip-mac-bind")
        if ip_mac_bind is None:
            ip_mac_bind = etree.SubElement(interface_element, "ip-mac-bind")
            ip_mac_bind.set("xmlns", "urn:ruijie:ntos:params:xml:ns:yang:ip-mac")

            # IPv4绑定配置
            ipv4_bind = etree.SubElement(ip_mac_bind, "ipv4")
            etree.SubElement(ipv4_bind, "enabled").text = "false"

            # IPv6绑定配置
            ipv6_bind = etree.SubElement(ip_mac_bind, "ipv6")
            etree.SubElement(ipv6_bind, "enabled").text = "false"

    def _add_default_reverse_path(self, interface_element: etree.Element):
        """添加默认的反向路径检查"""
        reverse_path = interface_element.find("reverse-path")
        if reverse_path is None:
            etree.SubElement(interface_element, "reverse-path").text = "true"

    def _add_default_snooping(self, interface_element: etree.Element):
        """添加默认的snooping配置"""
        snooping = interface_element.find("snooping")
        if snooping is None:
            snooping = etree.SubElement(interface_element, "snooping")
            etree.SubElement(snooping, "trust").text = "false"
            etree.SubElement(snooping, "suppress").text = "false"

    def _find_or_create_interface_container_in_vrf(self, root: etree.Element) -> etree.Element:
        """
        在VRF节点下查找或创建接口容器

        Args:
            root: XML根元素

        Returns:
            etree.Element: 接口容器元素
        """
        # 查找VRF元素
        vrf = root.find('.//vrf')
        if vrf is None:
            # 尝试使用默认命名空间查找
            vrf = root.find('.//{urn:ruijie:ntos}vrf')
            if vrf is None:
                log(_("xml_integrator.vrf_not_found"), "error")
                return None

        # 查找接口容器
        interface_container = vrf.find('.//interface')
        if interface_container is None:
            # 尝试使用接口命名空间查找
            interface_container = vrf.find('.//{urn:ruijie:ntos:params:xml:ns:yang:interface}interface')

        if interface_container is None:
            # 创建接口容器
            interface_container = etree.SubElement(vrf, 'interface')
            interface_container.set('xmlns', 'urn:ruijie:ntos:params:xml:ns:yang:interface')
            log(_("xml_integrator.interface_container_created"), "debug")
        else:
            log(_("xml_integrator.interface_container_found"), "debug")

        return interface_container

    def _integrate_vlan_config(self, interface_element: etree.Element, interface_config: Dict):
        """
        集成VLAN接口配置 - 根据NTOS YANG模型使用正确的字段名

        Args:
            interface_element: 接口元素
            interface_config: 接口配置
        """
        # 设置VLAN ID - 使用YANG模型要求的字段名
        vlan_id = interface_config.get("vlan_id")
        if vlan_id:
            vlan_id_element = interface_element.find("vlan-id")
            if vlan_id_element is None:
                vlan_id_element = etree.SubElement(interface_element, "vlan-id")
            vlan_id_element.text = str(vlan_id)

        # 设置链接接口 - 使用YANG模型要求的link-interface字段
        link_interface = (interface_config.get("link_interface") or
                         interface_config.get("parent_interface"))
        if link_interface:
            # 需要映射到实际的接口名称
            mapped_link_interface = self._get_mapped_interface_name(link_interface, interface_config)

            link_element = interface_element.find("link-interface")
            if link_element is None:
                link_element = etree.SubElement(interface_element, "link-interface")
            link_element.text = mapped_link_interface


        # 设置协议类型（默认802.1q）
        protocol = interface_config.get("protocol", "802.1q")
        if protocol:
            protocol_element = interface_element.find("protocol")
            if protocol_element is None:
                protocol_element = etree.SubElement(interface_element, "protocol")
            protocol_element.text = protocol

    def _get_mapped_interface_name(self, original_name: str, interface_config: Dict) -> str:
        """
        获取映射后的接口名称

        Args:
            original_name: 原始接口名称
            interface_config: 接口配置

        Returns:
            str: 映射后的接口名称
        """
        # 首先尝试从当前上下文获取映射
        if hasattr(self, '_current_interface_mapping') and self._current_interface_mapping:
            mapped_name = self._current_interface_mapping.get(original_name)
            if mapped_name:
                return mapped_name

        # 如果没有找到映射，返回原始名称
        log(f"WARNING: 未找到接口 {original_name} 的映射，使用原始名称", "warning")
        return original_name

    def _extract_interface_mapping(self, converted_interfaces: Dict[str, Any]) -> Dict[str, str]:
        """
        从转换后的接口中提取接口映射

        Args:
            converted_interfaces: 转换后的接口字典

        Returns: Dict[str, str]: 接口映射字典
        """
        mapping = {}

        for original_name, interface_config in converted_interfaces.items():
            mapped_name = interface_config.get("name")
            if mapped_name:
                mapping[original_name] = mapped_name

                # 如果是VLAN接口，也添加父接口的映射
                if interface_config.get("type") == "vlan":
                    parent_interface = interface_config.get("parent_interface")
                    if parent_interface and parent_interface not in mapping:
                        # 查找父接口的映射
                        for parent_orig, parent_config in converted_interfaces.items():
                            if (parent_config.get("type") == "physical" and
                                parent_orig == parent_interface):
                                parent_mapped = parent_config.get("name")
                                if parent_mapped:
                                    mapping[parent_interface] = parent_mapped
                                break

        return mapping
