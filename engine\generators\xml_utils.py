from lxml import etree
from engine.utils.logger import log
from engine.utils.i18n import _  # 导入国际化函数
import html  # 导入 html 模块用于 XML 特殊字符转义

# 保留原始命名空间常量以保持向后兼容性
NSMAP_GLOBAL = {None: "urn:ruijie:ntos"}
NS_INTERFACE = "urn:ruijie:ntos:params:xml:ns:yang:interface"
NS_FLOW = "urn:ruijie:ntos:params:xml:ns:yang:flow-control"
NS_MONITOR = "urn:ruijie:ntos:params:xml:ns:yang:if-mon"
NS_IPMAC = "urn:ruijie:ntos:params:xml:ns:yang:ip-mac"
NS_ACCESS = "urn:ruijie:ntos:params:xml:ns:yang:local-defend"
NS_ROUTING = "urn:ruijie:ntos:params:xml:ns:yang:routing"
NS_VLAN = "urn:ruijie:ntos:params:xml:ns:yang:vlan"
NS_SECURITY_ZONE = "urn:ruijie:ntos:params:xml:ns:yang:security-zone"
NS_IP = "urn:ruijie:ntos:params:xml:ns:yang:ip"
NS_PPPOE = "urn:ruijie:ntos:params:xml:ns:yang:pppoe"

# 命名空间映射表
NAMESPACE_MAPPING = {
    "interface": NS_INTERFACE,
    "routing": NS_ROUTING,
    "security-zone": NS_SECURITY_ZONE,
    "vlan": NS_VLAN,
    "flow-control": NS_FLOW,
    "monitor": NS_MONITOR,
    "ip-mac-bind": NS_IPMAC,
    "access-control": NS_ACCESS,
    "ip": NS_IP,
    "pppoe": NS_PPPOE
}

class NamespaceManager:
    """
    命名空间管理类，用于处理XML命名空间相关操作
    """
    
    def __init__(self):
        """初始化命名空间管理器"""
        # 命名空间定义
        self.NSMAP_GLOBAL = {None: "urn:ruijie:ntos"}
        self.NS_INTERFACE = "urn:ruijie:ntos:params:xml:ns:yang:interface"
        self.NS_FLOW = "urn:ruijie:ntos:params:xml:ns:yang:flow-control"
        self.NS_MONITOR = "urn:ruijie:ntos:params:xml:ns:yang:if-mon"
        self.NS_IPMAC = "urn:ruijie:ntos:params:xml:ns:yang:ip-mac"
        self.NS_ACCESS = "urn:ruijie:ntos:params:xml:ns:yang:local-defend"
        self.NS_ROUTING = "urn:ruijie:ntos:params:xml:ns:yang:routing"
        self.NS_VLAN = "urn:ruijie:ntos:params:xml:ns:yang:vlan"
        self.NS_SECURITY_ZONE = "urn:ruijie:ntos:params:xml:ns:yang:security-zone"
        self.NS_IP = "urn:ruijie:ntos:params:xml:ns:yang:ip"
        self.NS_PPPOE = "urn:ruijie:ntos:params:xml:ns:yang:pppoe"
        
        # 命名空间映射表
        self.namespace_mapping = {
            "interface": self.NS_INTERFACE,
            "routing": self.NS_ROUTING,
            "security-zone": self.NS_SECURITY_ZONE,
            "vlan": self.NS_VLAN,
            "flow-control": self.NS_FLOW,
            "monitor": self.NS_MONITOR,
            "ip-mac-bind": self.NS_IPMAC,
            "access-control": self.NS_ACCESS,
            "ip": self.NS_IP,
            "pppoe": self.NS_PPPOE
        }
    
    def create_default_xml_structure(self):
        """
        创建默认的XML结构
        
        Returns:
            Element: XML根节点
        """
        # 创建根节点，设置默认命名空间
        nsmap = {None: "urn:ruijie:ntos"}
        root = etree.Element("config", nsmap=nsmap)
        
        # 使用find_or_create_vrf而不是直接创建
        # 由于这是新创建的根节点，这里会创建vrf节点
        self.find_or_create_vrf(root)
        
        log(_("xml_utils.create_default_structure.success"))
        return root
    
    def cleanup_namespaces(self, root):
        """
        清理XML命名空间，确保符合YANG模型规范
        
        Args:
            root: XML根节点
            
        Returns:
            Element: 处理后的XML根节点
        """
        # 首先保存一个完整的备份
        import copy
        root_backup = copy.deepcopy(root)
        
        # 记录原始子元素数量，用于后续验证
        original_child_count = len(root)
        log(_("xml_utils.start_namespace_cleanup", child_count=original_child_count))
        
        # 确保根节点有正确的命名空间
        if None not in root.nsmap or root.nsmap[None] != "urn:ruijie:ntos":
            # 创建新的根节点并迁移子元素
            log(_("xml_utils.fix_root_namespace"))
            nsmap = {None: "urn:ruijie:ntos"}
            new_root = etree.Element("config", nsmap=nsmap)
            for child in list(root):  # 使用list复制避免修改时的问题
                root.remove(child)
                new_root.append(child)
            root = new_root
        
        # 设置一个类变量来记录是否已经记录过深层xmlns移除的日志
        if not hasattr(NamespaceManager, 'logged_deep_xmlns_removal'):
            NamespaceManager.logged_deep_xmlns_removal = False
        
        # 递归处理所有子元素，确保保留必需的命名空间声明
        # 定义始终需要保留命名空间的元素（顶级元素）
        always_required_namespace_elements = {
            "interface": "urn:ruijie:ntos:params:xml:ns:yang:interface",
            "routing": "urn:ruijie:ntos:params:xml:ns:yang:routing",
            "vlan": "urn:ruijie:ntos:params:xml:ns:yang:vlan"
        }
        
        # 定义只有在interface标签内才需要保留命名空间的元素
        interface_specific_namespace_elements = {
            "flow-control": "urn:ruijie:ntos:params:xml:ns:yang:flow-control",
            "monitor": "urn:ruijie:ntos:params:xml:ns:yang:if-mon", 
            "ip-mac-bind": "urn:ruijie:ntos:params:xml:ns:yang:ip-mac",
            "access-control": "urn:ruijie:ntos:params:xml:ns:yang:local-defend"
        }
        
        # 定义只有在vrf标签内才需要保留命名空间的元素
        vrf_specific_namespace_elements = {
            "security-zone": "urn:ruijie:ntos:params:xml:ns:yang:security-zone"
        }
        
        def is_inside_interface(elem):
            """检查元素是否在interface标签内"""
            parent = elem.getparent()
            while parent is not None:
                if not isinstance(parent.tag, str):
                    parent = parent.getparent()
                    continue
                parent_tag = parent.tag.split('}')[-1] if '}' in parent.tag else parent.tag
                if parent_tag == "interface":
                    return True
                parent = parent.getparent()
            return False
        
        def is_inside_vrf(elem):
            """检查元素是否在vrf标签内"""
            parent = elem.getparent()
            while parent is not None:
                if not isinstance(parent.tag, str):
                    parent = parent.getparent()
                    continue
                parent_tag = parent.tag.split('}')[-1] if '}' in parent.tag else parent.tag
                if parent_tag == "vrf":
                    return True
                parent = parent.getparent()
            return False
        
        # 首先清除所有不必要的xmlns属性和命名空间
        elements_to_process = []
        for elem in root.iter():
            # 确保tag是字符串类型
            if not isinstance(elem.tag, str):
                continue
            tag_name = elem.tag.split('}')[-1] if '}' in elem.tag else elem.tag
            
            # 收集需要处理的元素信息
            elements_to_process.append((elem, tag_name))
            
            # 清除所有现有的xmlns属性，稍后重新设置需要的
            if elem.get('xmlns'):
                elem.attrib.pop('xmlns', None)
        
        # 创建新的无命名空间元素来替换有命名空间的元素
        def create_clean_element(original_elem, tag_name, should_have_namespace=False, namespace_uri=None):
            """创建清理后的元素"""
            if should_have_namespace and namespace_uri:
                # 需要保留命名空间的元素
                new_elem = etree.Element(tag_name)
                new_elem.set('xmlns', namespace_uri)
            else:
                # 不需要命名空间的元素
                new_elem = etree.Element(tag_name)
            
            # 复制文本内容
            new_elem.text = original_elem.text
            new_elem.tail = original_elem.tail
            
            # 复制属性（除了xmlns）
            for attr_name, attr_value in original_elem.attrib.items():
                if attr_name != 'xmlns':
                    new_elem.set(attr_name, attr_value)
            
            return new_elem
        
        # 从叶子节点开始处理，避免父子关系问题
        elements_to_process.reverse()
        
        for original_elem, tag_name in elements_to_process:
            # 跳过根节点
            if original_elem == root:
                continue
                
            should_have_namespace = False
            namespace_uri = None
            
            # 始终保留的命名空间元素
            if tag_name in always_required_namespace_elements:
                should_have_namespace = True
                namespace_uri = always_required_namespace_elements[tag_name]
            
            # 只有在interface内才保留命名空间的元素
            elif tag_name in interface_specific_namespace_elements:
                if is_inside_interface(original_elem):
                    should_have_namespace = True
                    namespace_uri = interface_specific_namespace_elements[tag_name]
            
            # 只有在vrf内才保留命名空间的元素
            elif tag_name in vrf_specific_namespace_elements:
                if is_inside_vrf(original_elem):
                    should_have_namespace = True
                    namespace_uri = vrf_specific_namespace_elements[tag_name]
            
            # 如果元素当前有命名空间但不应该有，或者应该有不同的命名空间，则替换
            current_has_namespace = '}' in original_elem.tag
            if current_has_namespace != should_have_namespace or (should_have_namespace and namespace_uri and namespace_uri not in original_elem.tag):
                # 创建新元素
                new_elem = create_clean_element(original_elem, tag_name, should_have_namespace, namespace_uri)
                
                # 复制所有子元素
                for child in list(original_elem):
                    original_elem.remove(child)
                    new_elem.append(child)
                
                # 替换原元素
                parent = original_elem.getparent()
                if parent is not None:
                    index = parent.index(original_elem)
                    parent.remove(original_elem)
                    parent.insert(index, new_elem)
        
        # 修复节点名称问题 - 将 'n' 标签改为 'name'
        for elem in list(root.iter()):
            if not isinstance(elem.tag, str):
                continue
            if elem.tag.endswith('n') and elem.tag.split('}')[-1] == 'n':
                # 获取标签的命名空间前缀
                ns_prefix = ''
                if '}' in elem.tag:
                    ns_prefix = elem.tag.split('}')[0] + '}'
                
                # 创建新的 'name' 元素并复制内容
                new_elem = etree.Element(ns_prefix + 'name')
                new_elem.text = elem.text
                for attr_name, attr_value in elem.attrib.items():
                    new_elem.set(attr_name, attr_value)
                
                # 复制所有子元素
                for child in list(elem):
                    elem.remove(child)
                    new_elem.append(child)
                
                # 替换旧元素
                parent = elem.getparent()
                if parent is not None:
                    index = parent.index(elem)
                    parent.remove(elem)
                    parent.insert(index, new_elem)
                    log(_("xml_utils.fix_node_name", old="n", new="name"))
        
        # 保存根节点的默认命名空间
        root_default_ns = root.nsmap.get(None)
        
        # 使用lxml的cleanup_namespaces函数清理所有前缀
        # 先保存一份子元素的数量
        pre_cleanup_child_count = len(root)
        
        # 谨慎地使用lxml的cleanup_namespaces函数
        try:
            etree.cleanup_namespaces(root)
            
            # 验证子元素是否丢失
            post_cleanup_child_count = len(root)
            if post_cleanup_child_count < pre_cleanup_child_count:
                log(_("xml_utils.warning.children_lost_during_cleanup", 
                      pre=pre_cleanup_child_count, 
                      post=post_cleanup_child_count), "warning")
                # 如果丢失太多子元素（超过10%），使用备份
                if post_cleanup_child_count < pre_cleanup_child_count * 0.9:
                    log(_("xml_utils.warning.significant_children_loss_using_backup"), "warning")
                    return root_backup
        except Exception as e:
            log(_("xml_utils.error.cleanup_namespaces_failed", error=str(e)), "error")
            # 出现异常时使用备份
            return root_backup
        
        # 如果根节点的默认命名空间被移除，重新设置
        if root_default_ns and (None not in root.nsmap or root.nsmap.get(None) != root_default_ns):
            # 创建新的根节点并保留命名空间
            nsmap = {None: root_default_ns}
            # 保留其他可能的命名空间前缀
            for prefix, uri in root.nsmap.items():
                if prefix is not None:
                    nsmap[prefix] = uri
            
            new_root = etree.Element(root.tag, nsmap=nsmap)
            new_root.text = root.text
            new_root.tail = root.tail
            
            # 复制属性
            for attr_name, attr_value in root.attrib.items():
                new_root.set(attr_name, attr_value)
            
            # 移动所有子元素，确保不丢失元素
            child_count_before = len(root)
            for child in list(root):
                root.remove(child)
                new_root.append(child)
            
            # 检查是否所有子元素都成功迁移
            if len(new_root) < child_count_before:
                log(_("xml_utils.warning.children_lost_during_root_replacement", 
                      before=child_count_before, 
                      after=len(new_root)), "warning")
                # 如果丢失子元素，使用备份
                return root_backup
            
            root = new_root
            log(_("xml_utils.restore_root_namespace"))
        
        # 验证命名空间清理结果
        root_ns = root.nsmap.get(None)
        if root_ns == "urn:ruijie:ntos":
            log(_("xml_utils.namespace_cleanup_success"))
        else:
            log(_("xml_utils.namespace_cleanup_warning"), "warning")
        
        # 最终检查：确保根节点下有子元素
        if len(root) == 0 and original_child_count > 0:
            log(_("xml_utils.error.all_children_lost_using_backup"), "error")
            return root_backup
        
        return root
    
    def fix_duplicate_xmlns_in_string(self, xml_str):
        """
        在XML字符串级别修复重复的xmlns属性和policy元素的多余xmlns属性
        这是最后的安全措施，确保即使在XML序列化过程中产生的重复xmlns也能被清理

        Args:
            xml_str (str): XML字符串

        Returns:
            str: 修复后的XML字符串
        """
        import re

        original_xml = xml_str
        fixed_count = 0

        # 第一步：修复重复的xmlns属性
        duplicate_patterns = [
            # 修复重复的相同xmlns属性: xmlns="uri" xmlns="uri"
            r'xmlns="([^"]+)"\s+xmlns="\1"',
            # 修复更复杂的重复: xmlns="uri" 其他属性 xmlns="uri"
            r'xmlns="([^"]+)"([^>]*?)xmlns="\1"',
            # 修复多个重复
            r'(xmlns="[^"]+")(\s+\1)+',
        ]

        for pattern in duplicate_patterns:
            matches = re.findall(pattern, xml_str)
            if matches:
                if pattern == r'xmlns="([^"]+)"\s+xmlns="\1"':
                    # 简单重复: xmlns="uri" xmlns="uri" -> xmlns="uri"
                    xml_str = re.sub(pattern, r'xmlns="\1"', xml_str)
                elif pattern == r'xmlns="([^"]+)"([^>]*?)xmlns="\1"':
                    # 复杂重复: xmlns="uri" 其他属性 xmlns="uri" -> xmlns="uri" 其他属性
                    xml_str = re.sub(pattern, r'xmlns="\1"\2', xml_str)
                elif pattern == r'(xmlns="[^"]+")(\s+\1)+':
                    # 多个重复: xmlns="uri" xmlns="uri" xmlns="uri" -> xmlns="uri"
                    xml_str = re.sub(pattern, r'\1', xml_str)
                fixed_count += len(matches)

        # 第二步：清理policy元素的xmlns属性（符合NTOS YANG模型）
        # 根据NTOS YANG模型，security-policy下的policy元素应该是纯容器元素，不应该包含xmlns属性
        policy_pattern = r'<policy\s+xmlns="[^"]*"([^>]*)>'
        policy_matches = re.findall(policy_pattern, xml_str)
        if policy_matches:
            xml_str = re.sub(policy_pattern, r'<policy\1>', xml_str)
            policy_fixed_count = len(policy_matches)
            fixed_count += policy_fixed_count
            log(f"🔧 清理了{policy_fixed_count}个policy元素的xmlns属性", "debug")

        if fixed_count > 0:
            log(_("xml_utils.fix_duplicate_xmlns", count=fixed_count))

        return xml_str
    
    def find_or_create_vrf(self, root):
        """
        查找或创建VRF节点
        
        Args:
            root: XML根节点
            
        Returns:
            Element: VRF节点
        """
        # 使用多种方式查找vrf节点，确保能找到任何存在的vrf
        vrf = None
        
        # 1. 尝试直接查找
        vrf_nodes = root.findall(".//vrf")
        if vrf_nodes:
            # 使用第一个找到的vrf节点
            vrf = vrf_nodes[0]
        
        # 2. 如果没找到，再尝试其他方式
        if vrf is None:
            # 尝试使用XPath查找带命名空间的vrf节点
            vrf_nodes = root.xpath(".//*[local-name()='vrf']")
            if vrf_nodes:
                vrf = vrf_nodes[0]
        
        # 如果没有找到任何vrf节点，创建一个新的
        if vrf is None:
            log(_("xml_utils.creating_vrf_node"))
            vrf = etree.SubElement(root, "vrf")
            etree.SubElement(vrf, "name").text = "main"
        else:
            # 检查是否有name元素，如果没有则添加
            name_elem = None
            
            # 查找name元素（只查找VRF的直接子元素）
            name_elems = vrf.findall("./name")
            if not name_elems:
                name_elems = vrf.xpath("./*[local-name()='name']")
            
            if name_elems:
                name_elem = name_elems[0]
            
            # 查找旧格式的n元素（只查找VRF的直接子元素）
            if name_elem is None:
                n_elems = vrf.findall("./n")
                if not n_elems:
                    n_elems = vrf.xpath("./*[local-name()='n']")
                
                if n_elems:
                    n_elem = n_elems[0]
                    # 转换n元素为name元素
                    parent = n_elem.getparent()
                    if parent is not None:
                        name_elem = etree.Element("name")
                        name_elem.text = n_elem.text
                        parent.replace(n_elem, name_elem)
                        log(_("xml_utils.n_converted_to_name"))
            
            # 如果没有name或n元素，添加一个name元素
            if name_elem is None:
                name_elem = etree.SubElement(vrf, "name")
                name_elem.text = "main"
                log(_("xml_utils.name_element_added"))
            # 确保name元素值为main
            elif name_elem.text != "main":
                log(_("xml_utils.vrf_name_not_main", current=name_elem.text))
                name_elem.text = "main"
        
        return vrf
    
    def find_or_create_child_with_ns(self, parent, tag_name, namespace=None):
        """
        查找或创建带命名空间的子节点
        
        Args:
            parent: 父节点
            tag_name: 标签名
            namespace: 命名空间
            
        Returns:
            Element: 子节点
        """
        # 先尝试查找带命名空间的节点
        if namespace:
            child = parent.find(".//{%s}%s" % (namespace, tag_name))
        else:
            child = parent.find(".//%s" % tag_name)
            
        # 如果找不到，尝试不带命名空间查找
        if child is None:
            child = parent.find(".//%s" % tag_name)
            
        # 如果仍然找不到，创建新节点
        if child is None:
            if namespace:
                child = etree.SubElement(parent, "{%s}%s" % (namespace, tag_name))
                # 如果是需要命名空间声明的顶级节点，添加xmlns属性
                if tag_name in self.namespace_mapping:
                    child.set("xmlns", namespace)
            else:
                child = etree.SubElement(parent, tag_name)
                
            log(_("xml_utils.create_node", tag=tag_name))
            
        return child
    
    def _remove_duplicate_xmlns_attributes(self, root):
        """
        移除重复的xmlns属性
        
        Args:
            root: XML根节点
        """
        for elem in root.iter():
            if not isinstance(elem.tag, str):
                continue
                
            # 检查是否有重复的xmlns属性
            attrib_keys = list(elem.attrib.keys())
            xmlns_keys = [key for key in attrib_keys if key == 'xmlns']
            
            if len(xmlns_keys) > 1:
                # 保留第一个xmlns属性，移除其他的
                xmlns_value = elem.get('xmlns')
                # 清除所有xmlns属性
                for key in xmlns_keys:
                    elem.attrib.pop(key, None)
                # 重新设置一个xmlns属性
                if xmlns_value:
                    elem.set('xmlns', xmlns_value)
                    
# 为了保持向后兼容性，创建一个默认的命名空间管理器实例
_default_ns_manager = NamespaceManager()

# 向后兼容的函数，调用默认命名空间管理器的方法
def create_default_xml_structure():
    """向后兼容的创建默认XML结构函数"""
    return _default_ns_manager.create_default_xml_structure()

def cleanup_namespaces(root):
    """向后兼容的命名空间清理函数"""
    return _default_ns_manager.cleanup_namespaces(root)

def find_or_create_vrf(root):
    """向后兼容的查找或创建VRF节点函数"""
    return _default_ns_manager.find_or_create_vrf(root)

def find_or_create_child_with_ns(parent, tag_name, namespace=None):
    """向后兼容的查找或创建带命名空间的子节点函数"""
    return _default_ns_manager.find_or_create_child_with_ns(parent, tag_name, namespace)

def fix_duplicate_xmlns_in_string(xml_str):
    """向后兼容的修复重复xmlns属性函数"""
    return _default_ns_manager.fix_duplicate_xmlns_in_string(xml_str)

def escape_xml_text(text):
    """
    转义 XML 特殊字符，确保生成的 XML 是有效的。
    
    Args:
        text (str): 需要转义的文本
        
    Returns:
        str: 转义后的文本
    """
    if not text:
        return text
    
    # 使用 html.escape 函数转义 XML 特殊字符
    # 这将转义 &, <, >, ", ' 等特殊字符
    return html.escape(str(text))