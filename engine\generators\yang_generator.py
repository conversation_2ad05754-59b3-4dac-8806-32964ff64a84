from lxml import etree
from engine.utils.logger import log
from engine.utils.i18n import _  # 导入国际化函数
import copy
import random
import re

# 导入重构后的模块
from engine.generators.xml_utils import (
    NSMAP_GLOBAL, NS_INTERFACE, NS_FLOW, NS_MONITOR, NS_IPMAC, 
    NS_ACCESS, NS_ROUTING, NS_VLAN, NS_SECURITY_ZONE,
    NamespaceManager, create_default_xml_structure, cleanup_namespaces, find_or_create_vrf
)
from engine.generators.interface_handler import (
    InterfaceHandler, sanitize_interface_names, process_fortinet_interfaces,
    add_interfaces_to_xml, reset_tunnel_ids
)
from engine.generators.routing_handler import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, add_routes_to_xml, create_static_route_config
)
from engine.generators.security_zone_handler import (
    SecurityZoneHandler, add_zones_to_xml, create_security_zone_config
)
from engine.generators.time_range_handler import (
    TimeRangeHandler, add_time_ranges_to_xml, NS_TIME_RANGE
)
# 导入策略处理相关模块
from engine.processors.policy_processor import PolicyProcessor
from engine.generators.security_policy_generator import SecurityPolicyGenerator
from engine.generators.nat_generator import NATGenerator

class YangGenerator:
    """
    YANG模型生成器类，用于生成符合YANG模型规范的XML配置
    """
    
    def __init__(self):
        """初始化YANG模型生成器"""
        self.ns_manager = NamespaceManager()
        self.interface_handler = InterfaceHandler(self.ns_manager)
        self.routing_handler = RoutingHandler(self.ns_manager)
        self.security_zone_handler = SecurityZoneHandler(self.ns_manager)
        self.time_range_handler = TimeRangeHandler(self.ns_manager)

        # 初始化策略处理相关组件
        self.policy_processor = PolicyProcessor()
        self.security_policy_generator = SecurityPolicyGenerator()
        self.nat_generator = NATGenerator()
    
    def generate_xml(self, config_data, template_path=None):
        """
        生成基于模板的NTOS XML配置，确保符合YANG模型规范
        
        原则：
        1. 尽可能使用模板上的信息
        2. 如果节点已存在，则修改字段
        3. 如果节点不存在，则插入新的完整块
        4. XML不包含命名空间前缀
        5. 根节点使用默认命名空间声明xmlns="urn:ruijie:ntos"
        6. 子节点使用xmlns属性声明自己的命名空间
        
        Args:
            config_data: 配置数据
            template_path: 模板文件路径（可选）
            
        Returns:
            str: 生成的XML字符串
        """
        log(_("yang.start_xml_generation"))
        
        # 重置隧道ID
        self.interface_handler.reset_tunnel_ids()
        
        # 清理和标准化接口名称
        config_data = self.interface_handler.sanitize_interface_names(config_data)
        
        # 如果提供了模板路径，使用模板作为基础
        if template_path:
            try:
                tree = etree.parse(template_path)
                root = tree.getroot()
                log(_("yang.using_template", path=template_path))
            except Exception as e:
                log(_("yang.template_loading_error", error=str(e)), "error")
                # 如果无法加载模板，则使用默认结构
                root = self.ns_manager.create_default_xml_structure()
        else:
            # 创建默认的XML结构
            root = self.ns_manager.create_default_xml_structure()
        
        # 获取或创建VRF节点
        vrf = self.ns_manager.find_or_create_vrf(root)
        
        # 添加接口配置
        if "interfaces" in config_data and config_data["interfaces"]:
            # 获取或创建接口节点
            interface_elem = vrf.find(".//interface")
            if interface_elem is None:
                interface_elem = etree.SubElement(vrf, "interface")
                interface_elem.set("xmlns", NS_INTERFACE)
                log(_("yang.create_interface_node"))
                
                # 添加SNMP配置
                snmp_elem = etree.SubElement(interface_elem, "snmp")
                etree.SubElement(snmp_elem, "if-usage-compute-interval").text = "30"
                etree.SubElement(snmp_elem, "if-global-notify-enable").text = "false"
            
            # 处理飞塔接口，转换子接口格式
            if "interface_mapping" in config_data:
                config_data = self.interface_handler.process_fortinet_interfaces(config_data, config_data["interface_mapping"])
            
            # 使用接口处理模块添加接口配置
            self.interface_handler.add_interfaces_to_xml(interface_elem, config_data["interfaces"])
        
        # 添加静态路由配置
        if "static_routes" in config_data and config_data["static_routes"]:
            # 获取或创建routing节点
            routing_elem = vrf.find(".//routing")
            if routing_elem is None:
                routing_elem = etree.SubElement(vrf, "routing")
                routing_elem.set("xmlns", NS_ROUTING)
                log(_("yang.create_routing_node"))
            
            # 使用路由处理模块添加路由配置
            self.routing_handler.add_routes_to_xml(routing_elem, config_data["static_routes"])
        
        # 添加安全区域配置
        if "security_zones" in config_data and config_data["security_zones"]:
            # 获取或创建security-zone节点
            security_zone_elem = vrf.find(".//security-zone")
            if security_zone_elem is None:
                security_zone_elem = etree.SubElement(vrf, "security-zone")
                security_zone_elem.set("xmlns", NS_SECURITY_ZONE)
                log(_("yang.create_security_zone_node"))
            
            # 使用安全区域处理模块添加安全区域配置
            self.security_zone_handler.add_zones_to_xml(security_zone_elem, config_data["security_zones"])
        
        # 添加时间对象配置
        if "time_ranges" in config_data and config_data["time_ranges"]:
            # 使用时间对象处理模块添加时间对象配置
            self.time_range_handler.add_time_ranges_to_xml(vrf, config_data["time_ranges"])

        # 添加策略配置（安全策略和NAT规则）
        if "policies" in config_data and config_data["policies"]:
            self._process_firewall_policies(vrf, config_data)

        # 处理命名空间 - 确保XML符合YANG模型规范
        root = self.ns_manager.cleanup_namespaces(root)
        
        # 确保根节点有默认命名空间
        if None not in root.nsmap or root.nsmap[None] != "urn:ruijie:ntos":
            # 手动设置根节点的命名空间
            nsmap = {None: "urn:ruijie:ntos"}
            new_root = etree.Element("config", nsmap=nsmap)
            for child in root:
                new_root.append(child)
            root = new_root
        
        # 修复节点名称问题 - 将所有 'n' 标签改为 'name'
        # 使用递归函数处理所有节点
        def fix_n_tags(elem):
            # 处理当前元素的所有子元素
            for child in list(elem):
                # 检查是否是 'n' 标签
                if child.tag.endswith('n') and child.tag.split('}')[-1] == 'n':
                    # 获取标签的命名空间前缀
                    ns_prefix = ''
                    if '}' in child.tag:
                        ns_prefix = child.tag.split('}')[0] + '}'
                    
                    # 创建新的 'name' 元素
                    new_elem = etree.Element(ns_prefix + 'name')
                    new_elem.text = child.text
                    
                    # 复制属性
                    for attr_name, attr_value in child.attrib.items():
                        new_elem.set(attr_name, attr_value)
                    
                    # 复制子元素
                    for grandchild in child:
                        new_elem.append(grandchild)
                      # 替换旧元素
                    index = elem.index(child)
                    elem.remove(child)
                    elem.insert(index, new_elem)
                    log(_("yang.fix_node_name", old="n", new="name"))
                else:
                    # 递归处理子元素
                    fix_n_tags(child)
        
        # 从根节点开始递归处理
        fix_n_tags(root)
        
        # 生成XML字符串（不启用XML声明）
        xml_string = etree.tostring(root, encoding='utf-8', pretty_print=True, xml_declaration=False).decode('utf-8')

        # 修复任何潜在的重复xmlns声明（安全措施）
        from engine.generators.xml_utils import fix_duplicate_xmlns_in_string
        xml_string = fix_duplicate_xmlns_in_string(xml_string)

        log(_("yang.xml_generation_complete"))
        return xml_string

    def _process_firewall_policies(self, vrf, config_data):
        """
        处理防火墙策略配置，生成安全策略和NAT规则

        遵循原则：
        1. 如果节点已存在 → 修改字段
        2. 如果节点不存在 → 插入新的完整块

        Args:
            vrf: VRF XML元素
            config_data: 配置数据
        """
        try:
            # 提取策略相关数据
            policies = config_data.get("policies", [])
            vips = self._extract_vips_from_nat_rules(config_data.get("nat_rules", []))
            ippools = self._extract_ippools_from_nat_rules(config_data.get("nat_rules", []))

            # 使用策略处理器处理策略
            policy_result = self.policy_processor.process_policies(policies, vips, ippools)

            # 处理安全策略
            if policy_result["security_policies"]:
                self._add_security_policies_to_xml(vrf, policy_result["security_policies"])

            # 处理NAT配置
            if policy_result["nat_rules"] or policy_result["nat_pools"]:
                self._add_nat_configuration_to_xml(vrf, policy_result["nat_rules"], policy_result["nat_pools"])

            # 记录警告信息
            for warning in policy_result["warnings"]:
                log(warning, "warning")

            # 记录统计信息
            stats = policy_result["statistics"]
            log(_("yang.policy_processing_stats",
                  total=stats["total_policies"],
                  security_policies=stats["security_policies_created"],
                  nat_rules=stats["nat_rules_created"]))

        except Exception as e:
            log(_("yang.policy_processing_error", error=str(e)), "error")

    def _add_security_policies_to_xml(self, vrf, security_policies):
        """
        添加安全策略到XML中

        Args:
            vrf: VRF XML元素
            security_policies: 安全策略列表
        """
        try:
            # 查找现有的安全策略节点
            security_policy_elem = vrf.find(".//security-policy")

            if security_policy_elem is not None:
                # 节点已存在 → 修改字段（添加新策略）
                log(_("yang.security_policy_node_exists"))
                for policy in security_policies:
                    policy_element = self.security_policy_generator._create_policy_element(policy)
                    if policy_element is not None:
                        security_policy_elem.append(policy_element)
            else:
                # 节点不存在 → 插入新的完整块
                log(_("yang.creating_security_policy_node"))
                security_policy_elem = self.security_policy_generator.generate_security_policy_xml(
                    security_policies, vrf.getroottree().getroot()
                )

        except Exception as e:
            log(_("yang.security_policy_error", error=str(e)), "error")

    def _add_nat_configuration_to_xml(self, vrf, nat_rules, nat_pools):
        """
        添加NAT配置到XML中

        Args:
            vrf: VRF XML元素
            nat_rules: NAT规则列表
            nat_pools: NAT池列表
        """
        try:
            # 查找现有的NAT节点
            nat_elem = vrf.find(".//nat")

            if nat_elem is not None:
                # 节点已存在 → 修改字段（添加新规则和池）
                log(_("yang.nat_node_exists"))

                # 添加NAT池
                for pool in nat_pools:
                    pool_element = self.nat_generator._create_nat_pool_element(pool)
                    if pool_element is not None:
                        nat_elem.append(pool_element)

                # 添加NAT规则
                for rule in nat_rules:
                    rule_element = self.nat_generator._create_nat_rule_element(rule)
                    if rule_element is not None:
                        nat_elem.append(rule_element)
            else:
                # 节点不存在 → 插入新的完整块
                log(_("yang.creating_nat_node"))
                nat_elem = self.nat_generator.generate_nat_xml(
                    nat_rules, nat_pools, vrf.getroottree().getroot()
                )

        except Exception as e:
            log(_("yang.nat_configuration_error", error=str(e)), "error")

    def _extract_vips_from_nat_rules(self, nat_rules):
        """
        从NAT规则中提取VIP配置

        Args:
            nat_rules: NAT规则列表

        Returns:
            dict: VIP配置字典
        """
        vips = {}
        for rule in nat_rules:
            if rule.get("type") == "vip":
                vip_name = rule.get("name")
                if vip_name:
                    vips[vip_name] = rule
        return vips

    def _extract_ippools_from_nat_rules(self, nat_rules):
        """
        从NAT规则中提取IP池配置

        Args:
            nat_rules: NAT规则列表

        Returns:
            dict: IP池配置字典
        """
        ippools = {}
        for rule in nat_rules:
            if rule.get("type") == "ippool":
                pool_name = rule.get("name")
                if pool_name:
                    ippools[pool_name] = rule
        return ippools

# 为了保持向后兼容性，创建一个默认的YANG生成器实例
_default_yang_generator = YangGenerator()

# 已废弃的函数，保留以维持向后兼容性
def create_static_route(vrf_elem, route_data, interface_mapping=None):
    """
    [已废弃] 请使用add_routes_to_xml函数替代。此函数仅为保持向后兼容性。
    """
    # 将调用转发到新的函数
    log(_("yang.using_deprecated_function.create_static_route"))
    return create_static_route_config(vrf_elem, route_data, interface_mapping)

# 向后兼容的函数，调用默认YANG生成器的方法
def generate_xml(config_data, template_path=None):
    """向后兼容的生成XML函数"""
    return _default_yang_generator.generate_xml(config_data, template_path)

# 如果直接运行此文件，执行测试
if __name__ == "__main__":
    # 创建一个简单的测试配置
    test_config = {
        "interfaces": [
            {
                "name": "Ge0/0",
                "status": "up",
                "ip": "***********/24",
                "role": "lan"
            }
        ],
        "static_routes": [
            {
                "destination": "0.0.0.0/0",
                "gateway": "*************"
            }
        ],
        "security_zones": [
            {
                "name": "trust",
                "description": "Trust Zone",
                "priority": 85,
                "interfaces": ["Ge0/0"]
            }
        ]
    }
    
    # 生成XML
    xml_output = generate_xml(test_config)
    
    # 打印生成的XML
    print("生成的XML:")
    print(xml_output)
    
    # 验证XML是否符合NTOS规范
    import re
    
    # 检查是否没有命名空间前缀
    has_prefix = re.search(r'<[a-zA-Z0-9_-]+:', xml_output)
    if has_prefix:
        print(f"错误: XML包含命名空间前缀: {has_prefix.group(0)}")
    else:
        print("通过: XML不包含命名空间前缀")
    
    # 检查是否只有根节点包含xmlns声明
    xmlns_count = xml_output.count('xmlns=')
    if xmlns_count != 1:
        print(f"错误: XML包含{xmlns_count}个xmlns声明，应该只包含一个")
    else:
        print("通过: XML只包含一个xmlns声明")
    
    # 检查是否没有XML声明
    if xml_output.startswith('<?xml'):
        print("错误: XML包含XML声明")
    else:
        print("通过: XML不包含XML声明")
