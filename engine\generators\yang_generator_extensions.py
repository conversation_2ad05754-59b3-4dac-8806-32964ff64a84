from lxml import etree
from engine.utils.logger import log
from engine.utils.i18n import _  # 导入国际化函数
import copy
import random
import re

# 导入基础模块
from engine.generators.xml_utils import (
    NSMAP_GLOBAL, NS_INTERFACE, NS_FLOW, NS_MONITOR, NS_IPMAC, 
    NS_ACCESS, NS_ROUTING, NS_VLAN, NS_SECURITY_ZONE,
    create_default_xml_structure, cleanup_namespaces, find_or_create_vrf,
    find_or_create_child_with_ns
)

# 扩展功能 - 高级XML生成与处理
def generate_advanced_xml(config_data, template_path=None):
    """
    生成高级的NTOS XML配置，支持更多的功能和更复杂的配置
    
    Args:
        config_data: 配置数据
        template_path: 模板文件路径（可选）
        
    Returns:
        str: 生成的XML字符串
    """
    log(_("yang_extensions.start_advanced_xml_generation"))
    
    # 使用基础的XML生成函数
    from engine.generators.yang_generator import generate_xml
    xml_string = generate_xml(config_data, template_path)
    
    # 在这里可以添加额外的处理逻辑
    # 例如：添加高级配置、验证XML结构等
    
    log(_("yang_extensions.advanced_xml_generation_complete"))
    return xml_string

# 扩展功能 - 配置验证
def validate_config(config_data):
    """
    验证配置数据的有效性
    
    Args:
        config_data: 配置数据
        
    Returns:
        tuple: (是否有效, 错误信息列表)
    """
    errors = []
    
    # 验证接口配置
    if "interfaces" in config_data:
        for interface in config_data["interfaces"]:
            # 检查必要字段
            if "name" not in interface:
                errors.append(_("yang_extensions.error.interface_missing_name"))
            
            # 检查IP地址格式
            if "ip" in interface:
                ip_parts = interface["ip"].split("/")
                if len(ip_parts) != 2:
                    errors.append(_("yang_extensions.error.invalid_ip_format", ip=interface["ip"]))
                else:
                    # 验证IP地址
                    ip = ip_parts[0]
                    if not re.match(r'^(\d{1,3}\.){3}\d{1,3}$', ip):
                        errors.append(_("yang_extensions.error.invalid_ip_address", ip=ip))
                    
                    # 验证前缀长度
                    try:
                        prefix = int(ip_parts[1])
                        if prefix < 0 or prefix > 32:
                            errors.append(_("yang_extensions.error.invalid_prefix_length", prefix=prefix))
                    except ValueError:
                        errors.append(_("yang_extensions.error.invalid_prefix_format", prefix=ip_parts[1]))
    
    # 验证路由配置
    if "static_routes" in config_data:
        for route in config_data["static_routes"]:
            # 检查目标网络
            if "destination" not in route:
                errors.append(_("yang_extensions.error.route_missing_destination"))
            
            # 检查下一跳
            if "gateway" not in route and route.get("blackhole") != "enable":
                errors.append(_("yang_extensions.error.route_missing_gateway"))
    
    # 验证安全区域配置
    if "security_zones" in config_data:
        for zone in config_data["security_zones"]:
            # 检查区域名称
            if "name" not in zone:
                errors.append(_("yang_extensions.error.zone_missing_name"))
    
    # 返回验证结果
    is_valid = len(errors) == 0
    return is_valid, errors

# 扩展功能 - 配置合并
def merge_configs(base_config, overlay_config):
    """
    合并两个配置数据
    
    Args:
        base_config: 基础配置数据
        overlay_config: 要合并的配置数据
        
    Returns:
        dict: 合并后的配置数据
    """
    result = copy.deepcopy(base_config)
    
    # 合并接口配置
    if "interfaces" in overlay_config:
        if "interfaces" not in result:
            result["interfaces"] = []
        
        # 创建现有接口的映射
        existing_interfaces = {intf["name"]: i for i, intf in enumerate(result["interfaces"]) if "name" in intf}
        
        # 添加或更新接口
        for interface in overlay_config["interfaces"]:
            if "name" in interface and interface["name"] in existing_interfaces:
                # 更新现有接口
                idx = existing_interfaces[interface["name"]]
                result["interfaces"][idx].update(interface)
            else:
                # 添加新接口
                result["interfaces"].append(interface)
    
    # 合并静态路由配置
    if "static_routes" in overlay_config:
        if "static_routes" not in result:
            result["static_routes"] = []
        
        # 创建现有路由的映射
        existing_routes = {route.get("destination"): i for i, route in enumerate(result["static_routes"]) if "destination" in route}
        
        # 添加或更新路由
        for route in overlay_config["static_routes"]:
            if "destination" in route and route["destination"] in existing_routes:
                # 更新现有路由
                idx = existing_routes[route["destination"]]
                result["static_routes"][idx].update(route)
            else:
                # 添加新路由
                result["static_routes"].append(route)
    
    # 合并安全区域配置
    if "security_zones" in overlay_config:
        if "security_zones" not in result:
            result["security_zones"] = []
        
        # 创建现有区域的映射
        existing_zones = {zone["name"]: i for i, zone in enumerate(result["security_zones"]) if "name" in zone}
        
        # 添加或更新区域
        for zone in overlay_config["security_zones"]:
            if "name" in zone and zone["name"] in existing_zones:
                # 更新现有区域
                idx = existing_zones[zone["name"]]
                
                # 特殊处理接口列表
                if "interfaces" in zone:
                    if "interfaces" not in result["security_zones"][idx]:
                        result["security_zones"][idx]["interfaces"] = []
                    
                    # 添加不存在的接口
                    for intf in zone["interfaces"]:
                        if intf not in result["security_zones"][idx]["interfaces"]:
                            result["security_zones"][idx]["interfaces"].append(intf)
                
                # 更新其他属性
                for key, value in zone.items():
                    if key != "interfaces":
                        result["security_zones"][idx][key] = value
            else:
                # 添加新区域
                result["security_zones"].append(zone)
    
    return result

# 扩展功能 - 配置差异比较
def compare_configs(config1, config2):
    """
    比较两个配置数据的差异
    
    Args:
        config1: 第一个配置数据
        config2: 第二个配置数据
        
    Returns:
        dict: 差异信息
    """
    differences = {
        "interfaces": {
            "added": [],
            "removed": [],
            "modified": []
        },
        "static_routes": {
            "added": [],
            "removed": [],
            "modified": []
        },
        "security_zones": {
            "added": [],
            "removed": [],
            "modified": []
        }
    }
    
    # 比较接口配置
    if "interfaces" in config1 or "interfaces" in config2:
        interfaces1 = {intf["name"]: intf for intf in config1.get("interfaces", []) if "name" in intf}
        interfaces2 = {intf["name"]: intf for intf in config2.get("interfaces", []) if "name" in intf}
        
        # 查找添加的接口
        for name in interfaces2:
            if name not in interfaces1:
                differences["interfaces"]["added"].append(interfaces2[name])
        
        # 查找删除的接口
        for name in interfaces1:
            if name not in interfaces2:
                differences["interfaces"]["removed"].append(interfaces1[name])
        
        # 查找修改的接口
        for name in interfaces1:
            if name in interfaces2 and interfaces1[name] != interfaces2[name]:
                differences["interfaces"]["modified"].append({
                    "name": name,
                    "old": interfaces1[name],
                    "new": interfaces2[name]
                })
    
    # 比较静态路由配置
    if "static_routes" in config1 or "static_routes" in config2:
        routes1 = {route.get("destination"): route for route in config1.get("static_routes", []) if "destination" in route}
        routes2 = {route.get("destination"): route for route in config2.get("static_routes", []) if "destination" in route}
        
        # 查找添加的路由
        for dest in routes2:
            if dest not in routes1:
                differences["static_routes"]["added"].append(routes2[dest])
        
        # 查找删除的路由
        for dest in routes1:
            if dest not in routes2:
                differences["static_routes"]["removed"].append(routes1[dest])
        
        # 查找修改的路由
        for dest in routes1:
            if dest in routes2 and routes1[dest] != routes2[dest]:
                differences["static_routes"]["modified"].append({
                    "destination": dest,
                    "old": routes1[dest],
                    "new": routes2[dest]
                })
    
    # 比较安全区域配置
    if "security_zones" in config1 or "security_zones" in config2:
        zones1 = {zone["name"]: zone for zone in config1.get("security_zones", []) if "name" in zone}
        zones2 = {zone["name"]: zone for zone in config2.get("security_zones", []) if "name" in zone}
        
        # 查找添加的区域
        for name in zones2:
            if name not in zones1:
                differences["security_zones"]["added"].append(zones2[name])
        
        # 查找删除的区域
        for name in zones1:
            if name not in zones2:
                differences["security_zones"]["removed"].append(zones1[name])
        
        # 查找修改的区域
        for name in zones1:
            if name in zones2:
                # 比较区域属性
                zone1 = zones1[name]
                zone2 = zones2[name]
                
                # 检查基本属性
                basic_attrs_changed = False
                for attr in ["description", "priority"]:
                    if attr in zone1 and attr in zone2:
                        if zone1[attr] != zone2[attr]:
                            basic_attrs_changed = True
                            break
                    elif attr in zone1 or attr in zone2:
                        basic_attrs_changed = True
                        break
                
                # 比较接口列表
                interfaces_changed = False
                interfaces1 = set(zone1.get("interfaces", []))
                interfaces2 = set(zone2.get("interfaces", []))
                if interfaces1 != interfaces2:
                    interfaces_changed = True
                
                # 如果有任何变化，添加到修改列表
                if basic_attrs_changed or interfaces_changed:
                    differences["security_zones"]["modified"].append({
                        "name": name,
                        "old": zone1,
                        "new": zone2
                    })
    
    return differences

# 如果直接运行此文件，执行测试
if __name__ == "__main__":
    # 创建一个简单的测试配置
    test_config = {
        "interfaces": [
            {
                "name": "Ge0/0",
                "status": "up",
                "ip": "***********/24",
                "role": "lan"
            }
        ],
        "static_routes": [
            {
                "destination": "0.0.0.0/0",
                "gateway": "*************"
            }
        ],
        "security_zones": [
            {
                "name": "trust",
                "description": "Trust Zone",
                "priority": 85,
                "interfaces": ["Ge0/0"]
            }
        ]
    }
    
    # 验证配置
    is_valid, errors = validate_config(test_config)
    print(f"配置有效性: {is_valid}")
    if not is_valid:
        for error in errors:
            print(f"错误: {error}")
    
    # 生成XML
    from engine.generators.yang_generator import generate_xml
    xml_output = generate_xml(test_config)
    
    # 打印生成的XML
    print("生成的XML:")
    print(xml_output) 