# -*- coding: utf-8 -*-
"""
缓存管理 - 提供智能缓存和LRU缓存实现
"""

import time
import threading
from typing import Any, Optional, Dict, List, Callable
from collections import OrderedDict
from dataclasses import dataclass
from engine.utils.logger import log
from engine.utils.i18n import _


@dataclass
class CacheEntry:
    """缓存条目数据类"""
    value: Any
    created_at: float
    last_accessed: float
    access_count: int
    ttl: Optional[float] = None


class LRUCache:
    """
    LRU（最近最少使用）缓存实现
    线程安全的LRU缓存，支持TTL和统计信息
    """
    
    def __init__(self, max_size: int = 128, default_ttl: Optional[float] = None):
        """
        初始化LRU缓存
        
        Args:
            max_size: 最大缓存大小
            default_ttl: 默认TTL（秒），None表示永不过期
        """
        self.max_size = max_size
        self.default_ttl = default_ttl
        self._cache: OrderedDict[str, CacheEntry] = OrderedDict()
        self._lock = threading.RLock()
        self._stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'expirations': 0
        }
        
        log(_("lru_cache.initialized", max_size=max_size, ttl=default_ttl), "debug")
    
    def get(self, key: str) -> Optional[Any]:
        """
        获取缓存值
        
        Args:
            key: 缓存键
            
        Returns:
            Optional[Any]: 缓存值，如果不存在或过期则返回None
        """
        with self._lock:
            if key not in self._cache:
                self._stats['misses'] += 1
                return None
            
            entry = self._cache[key]
            
            # 检查是否过期
            if self._is_expired(entry):
                del self._cache[key]
                self._stats['expirations'] += 1
                self._stats['misses'] += 1
                return None
            
            # 更新访问信息
            entry.last_accessed = time.time()
            entry.access_count += 1
            
            # 移动到末尾（最近使用）
            self._cache.move_to_end(key)
            
            self._stats['hits'] += 1
            return entry.value
    
    def put(self, key: str, value: Any, ttl: Optional[float] = None):
        """
        存储缓存值
        
        Args:
            key: 缓存键
            value: 缓存值
            ttl: TTL（秒），None使用默认TTL
        """
        with self._lock:
            current_time = time.time()
            
            # 创建缓存条目
            entry = CacheEntry(
                value=value,
                created_at=current_time,
                last_accessed=current_time,
                access_count=0,
                ttl=ttl if ttl is not None else self.default_ttl
            )
            
            # 如果键已存在，更新值
            if key in self._cache:
                self._cache[key] = entry
                self._cache.move_to_end(key)
            else:
                # 检查是否需要驱逐
                if len(self._cache) >= self.max_size:
                    self._evict_lru()
                
                self._cache[key] = entry
    
    def delete(self, key: str) -> bool:
        """
        删除缓存条目
        
        Args:
            key: 缓存键
            
        Returns:
            bool: 是否成功删除
        """
        with self._lock:
            if key in self._cache:
                del self._cache[key]
                return True
            return False
    
    def clear(self):
        """清空缓存"""
        with self._lock:
            self._cache.clear()
            log(_("lru_cache.cleared"), "debug")
    
    def _is_expired(self, entry: CacheEntry) -> bool:
        """检查缓存条目是否过期"""
        if entry.ttl is None:
            return False
        return time.time() - entry.created_at > entry.ttl
    
    def _evict_lru(self):
        """驱逐最近最少使用的条目"""
        if self._cache:
            evicted_key, evicted_entry = self._cache.popitem(last=False)
            self._stats['evictions'] += 1
            log(_("lru_cache.evicted"), "debug", key=evicted_key)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self._lock:
            total_requests = self._stats['hits'] + self._stats['misses']
            hit_rate = self._stats['hits'] / total_requests if total_requests > 0 else 0.0
            
            return {
                'size': len(self._cache),
                'max_size': self.max_size,
                'hit_rate': hit_rate,
                'hits': self._stats['hits'],
                'misses': self._stats['misses'],
                'evictions': self._stats['evictions'],
                'expirations': self._stats['expirations']
            }
    
    def get_keys(self) -> List[str]:
        """获取所有缓存键"""
        with self._lock:
            return list(self._cache.keys())


class CacheManager:
    """
    缓存管理器
    管理多个命名缓存实例
    """
    
    def __init__(self):
        """初始化缓存管理器"""
        self._caches: Dict[str, LRUCache] = {}
        self._lock = threading.Lock()
        
        log(_("cache_manager.initialized"), "info")
    
    def create_cache(self, name: str, max_size: int = 128, 
                    default_ttl: Optional[float] = None) -> LRUCache:
        """
        创建命名缓存
        
        Args:
            name: 缓存名称
            max_size: 最大缓存大小
            default_ttl: 默认TTL
            
        Returns:
            LRUCache: 缓存实例
        """
        with self._lock:
            if name in self._caches:
                log(_("cache_manager.cache_exists"), "warning", name=name)
                return self._caches[name]
            
            cache = LRUCache(max_size, default_ttl)
            self._caches[name] = cache
            
            log(_("cache_manager.cache_created"), "info", 
                name=name, max_size=max_size, ttl=default_ttl)
            
            return cache
    
    def get_cache(self, name: str) -> Optional[LRUCache]:
        """
        获取命名缓存
        
        Args:
            name: 缓存名称
            
        Returns:
            Optional[LRUCache]: 缓存实例
        """
        with self._lock:
            return self._caches.get(name)
    
    def delete_cache(self, name: str) -> bool:
        """
        删除命名缓存
        
        Args:
            name: 缓存名称
            
        Returns:
            bool: 是否成功删除
        """
        with self._lock:
            if name in self._caches:
                del self._caches[name]
                log(_("cache_manager.cache_deleted"), "info", name=name)
                return True
            return False
    
    def clear_all_caches(self):
        """清空所有缓存"""
        with self._lock:
            for cache in self._caches.values():
                cache.clear()
            log(_("cache_manager.all_caches_cleared"), "info")
    
    def get_all_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取所有缓存的统计信息"""
        with self._lock:
            return {name: cache.get_stats() for name, cache in self._caches.items()}
    
    def get_cache_names(self) -> List[str]:
        """获取所有缓存名称"""
        with self._lock:
            return list(self._caches.keys())


def cached(cache_name: str, key_func: Optional[Callable] = None, ttl: Optional[float] = None):
    """
    缓存装饰器
    
    Args:
        cache_name: 缓存名称
        key_func: 键生成函数
        ttl: TTL（秒）
    """
    def decorator(func: Callable) -> Callable:
        def wrapper(*args, **kwargs):
            # 生成缓存键
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                cache_key = f"{func.__name__}_{hash(str(args) + str(sorted(kwargs.items())))}"
            
            # 获取缓存管理器
            cache_manager = CacheManager()
            cache = cache_manager.get_cache(cache_name)
            
            if cache is None:
                # 创建缓存
                cache = cache_manager.create_cache(cache_name)
            
            # 尝试从缓存获取
            cached_result = cache.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            cache.put(cache_key, result, ttl)
            
            return result
        
        return wrapper
    return decorator
