# -*- coding: utf-8 -*-
"""
错误处理和异常管理 - 提供统一的错误处理和恢复机制
"""

import traceback
import threading
from typing import Dict, Any, Optional, List, Callable, Type
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from engine.utils.logger import log
from engine.utils.i18n import _


class ErrorSeverity(Enum):
    """错误严重程度"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class ErrorRecord:
    """错误记录数据类"""
    timestamp: datetime
    error_type: str
    error_message: str
    severity: ErrorSeverity
    context: Dict[str, Any]
    stack_trace: str
    recovery_attempted: bool
    recovery_successful: bool


class ExceptionRegistry:
    """
    异常注册表
    管理已知异常类型和处理策略
    """
    
    def __init__(self):
        """初始化异常注册表"""
        self._exception_handlers: Dict[Type[Exception], Callable] = {}
        self._error_patterns: Dict[str, Dict[str, Any]] = {}
        self._lock = threading.Lock()
        
        # 注册默认异常处理器
        self._register_default_handlers()
        
        log(_("exception_registry.initialized"))
    
    def _register_default_handlers(self):
        """注册默认异常处理器"""
        # 文件相关异常
        self.register_handler(FileNotFoundError, self._handle_file_not_found)
        self.register_handler(PermissionError, self._handle_permission_error)
        
        # 网络相关异常
        self.register_handler(ConnectionError, self._handle_connection_error)
        self.register_handler(TimeoutError, self._handle_timeout_error)
        
        # 数据相关异常
        self.register_handler(ValueError, self._handle_value_error)
        self.register_handler(KeyError, self._handle_key_error)
        
        # 内存相关异常
        self.register_handler(MemoryError, self._handle_memory_error)
    
    def register_handler(self, exception_type: Type[Exception], handler: Callable):
        """
        注册异常处理器
        
        Args:
            exception_type: 异常类型
            handler: 处理器函数
        """
        with self._lock:
            self._exception_handlers[exception_type] = handler
            log(_("exception_registry.handler_registered"), "debug", 
                exception=exception_type.__name__)
    
    def get_handler(self, exception: Exception) -> Optional[Callable]:
        """
        获取异常处理器
        
        Args:
            exception: 异常实例
            
        Returns:
            Optional[Callable]: 处理器函数
        """
        with self._lock:
            # 精确匹配
            if type(exception) in self._exception_handlers:
                return self._exception_handlers[type(exception)]
            
            # 继承匹配
            for exc_type, handler in self._exception_handlers.items():
                if isinstance(exception, exc_type):
                    return handler
            
            return None
    
    def _handle_file_not_found(self, exception: FileNotFoundError, context: Dict[str, Any]) -> Dict[str, Any]:
        """处理文件未找到异常"""
        return {
            'recovery_strategy': 'create_default_file',
            'severity': ErrorSeverity.MEDIUM,
            'user_message': _("error_handler.file_not_found_recovery"),
            'technical_details': str(exception)
        }
    
    def _handle_permission_error(self, exception: PermissionError, context: Dict[str, Any]) -> Dict[str, Any]:
        """处理权限错误异常"""
        return {
            'recovery_strategy': 'request_elevated_permissions',
            'severity': ErrorSeverity.HIGH,
            'user_message': _("error_handler.permission_error_recovery"),
            'technical_details': str(exception)
        }
    
    def _handle_connection_error(self, exception: ConnectionError, context: Dict[str, Any]) -> Dict[str, Any]:
        """处理连接错误异常"""
        return {
            'recovery_strategy': 'retry_with_backoff',
            'severity': ErrorSeverity.MEDIUM,
            'user_message': _("error_handler.connection_error_recovery"),
            'technical_details': str(exception)
        }
    
    def _handle_timeout_error(self, exception: TimeoutError, context: Dict[str, Any]) -> Dict[str, Any]:
        """处理超时错误异常"""
        return {
            'recovery_strategy': 'increase_timeout_and_retry',
            'severity': ErrorSeverity.MEDIUM,
            'user_message': _("error_handler.timeout_error_recovery"),
            'technical_details': str(exception)
        }
    
    def _handle_value_error(self, exception: ValueError, context: Dict[str, Any]) -> Dict[str, Any]:
        """处理值错误异常"""
        return {
            'recovery_strategy': 'validate_and_sanitize_input',
            'severity': ErrorSeverity.LOW,
            'user_message': _("error_handler.value_error_recovery"),
            'technical_details': str(exception)
        }
    
    def _handle_key_error(self, exception: KeyError, context: Dict[str, Any]) -> Dict[str, Any]:
        """处理键错误异常"""
        return {
            'recovery_strategy': 'provide_default_value',
            'severity': ErrorSeverity.LOW,
            'user_message': _("error_handler.key_error_recovery"),
            'technical_details': str(exception)
        }
    
    def _handle_memory_error(self, exception: MemoryError, context: Dict[str, Any]) -> Dict[str, Any]:
        """处理内存错误异常"""
        return {
            'recovery_strategy': 'free_memory_and_retry',
            'severity': ErrorSeverity.CRITICAL,
            'user_message': _("error_handler.memory_error_recovery"),
            'technical_details': str(exception)
        }


class ErrorHandler:
    """
    错误处理器
    提供统一的错误处理和恢复机制
    """
    
    def __init__(self, max_error_history: int = 500):
        """
        初始化错误处理器
        
        Args:
            max_error_history: 最大错误历史记录数
        """
        self.max_error_history = max_error_history
        self.exception_registry = ExceptionRegistry()
        self._error_history: List[ErrorRecord] = []
        self._error_stats: Dict[str, int] = {}
        self._lock = threading.Lock()
        
        log(_("error_handler.initialized"))
    
    def handle_error(self, exception: Exception, context: Optional[Dict[str, Any]] = None,
                    attempt_recovery: bool = True) -> Dict[str, Any]:
        """
        处理错误
        
        Args:
            exception: 异常实例
            context: 错误上下文
            attempt_recovery: 是否尝试恢复
            
        Returns: Dict[str, Any]: 处理结果
        """
        context = context or {}
        
        # 获取异常处理器
        handler = self.exception_registry.get_handler(exception)
        
        # 确定错误严重程度
        if handler:
            handler_result = handler(exception, context)
            severity = handler_result.get('severity', ErrorSeverity.MEDIUM)
            recovery_strategy = handler_result.get('recovery_strategy')
            user_message = handler_result.get('user_message')
        else:
            severity = ErrorSeverity.MEDIUM
            recovery_strategy = 'log_and_continue'
            user_message = _("error_handler.unknown_error")
        
        # 记录错误
        error_record = ErrorRecord(
            timestamp=datetime.now(),
            error_type=type(exception).__name__,
            error_message=str(exception),
            severity=severity,
            context=context,
            stack_trace=traceback.format_exc(),
            recovery_attempted=attempt_recovery,
            recovery_successful=False
        )
        
        # 尝试恢复
        recovery_result = None
        if attempt_recovery and recovery_strategy:
            recovery_result = self._attempt_recovery(exception, recovery_strategy, context)
            error_record.recovery_successful = recovery_result.get('success', False)
        
        # 记录错误历史
        self._record_error(error_record)
        
        # 记录日志
        log_level = self._get_log_level(severity)
        log(_("error_handler.error_handled"))
        
        return {
            'error_record': error_record,
            'recovery_result': recovery_result,
            'user_message': user_message,
            'severity': severity,
            'handled': True
        }
    
    def _attempt_recovery(self, exception: Exception, strategy: str, 
                         context: Dict[str, Any]) -> Dict[str, Any]:
        """
        尝试错误恢复
        
        Args:
            exception: 异常实例
            strategy: 恢复策略
            context: 错误上下文
            
        Returns: Dict[str, Any]: 恢复结果
        """
        try:
            if strategy == 'create_default_file':
                return self._create_default_file(context)
            elif strategy == 'retry_with_backoff':
                return self._retry_with_backoff(context)
            elif strategy == 'free_memory_and_retry':
                return self._free_memory_and_retry(context)
            elif strategy == 'provide_default_value':
                return self._provide_default_value(context)
            elif strategy == 'validate_and_sanitize_input':
                return self._validate_and_sanitize_input(context)
            else:
                return {'success': False, 'reason': 'unknown_strategy'}
                
        except Exception as recovery_exception:
            log(_("error_handler.recovery_failed"), "error",
                strategy=strategy, error=str(recovery_exception))
            return {'success': False, 'reason': 'recovery_exception', 'error': str(recovery_exception)}
    
    def _create_default_file(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """创建默认文件恢复策略"""
        # 这里可以实现创建默认文件的逻辑
        return {'success': False, 'reason': 'not_implemented'}
    
    def _retry_with_backoff(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """重试恢复策略"""
        # 这里可以实现重试逻辑
        return {'success': False, 'reason': 'not_implemented'}
    
    def _free_memory_and_retry(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """释放内存并重试恢复策略"""
        try:
            from .performance import MemoryManager
            memory_manager = MemoryManager()
            optimization_result = memory_manager.optimize_memory(force=True)
            
            return {
                'success': optimization_result.get('optimized', False),
                'memory_freed_mb': optimization_result.get('memory_freed_mb', 0)
            }
        except Exception:
            return {'success': False, 'reason': 'memory_optimization_failed'}
    
    def _provide_default_value(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """提供默认值恢复策略"""
        # 这里可以实现提供默认值的逻辑
        return {'success': False, 'reason': 'not_implemented'}
    
    def _validate_and_sanitize_input(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """验证和清理输入恢复策略"""
        # 这里可以实现输入验证和清理的逻辑
        return {'success': False, 'reason': 'not_implemented'}
    
    def _record_error(self, error_record: ErrorRecord):
        """记录错误历史"""
        with self._lock:
            self._error_history.append(error_record)
            
            # 保持历史记录在限制范围内
            if len(self._error_history) > self.max_error_history:
                self._error_history.pop(0)
            
            # 更新错误统计
            error_type = error_record.error_type
            self._error_stats[error_type] = self._error_stats.get(error_type, 0) + 1
    
    def _get_log_level(self, severity: ErrorSeverity) -> str:
        """根据严重程度获取日志级别"""
        level_map = {
            ErrorSeverity.LOW: "warning",
            ErrorSeverity.MEDIUM: "error",
            ErrorSeverity.HIGH: "error",
            ErrorSeverity.CRITICAL: "critical"
        }
        return level_map.get(severity, "error")
    
    def get_error_stats(self) -> Dict[str, Any]:
        """获取错误统计信息"""
        with self._lock:
            return {
                'total_errors': len(self._error_history),
                'error_types': self._error_stats.copy(),
                'recent_errors': len([e for e in self._error_history[-10:] if e]),
                'recovery_success_rate': self._calculate_recovery_success_rate()
            }
    
    def _calculate_recovery_success_rate(self) -> float:
        """计算恢复成功率"""
        if not self._error_history:
            return 0.0
        
        recovery_attempts = [e for e in self._error_history if e.recovery_attempted]
        if not recovery_attempts:
            return 0.0
        
        successful_recoveries = [e for e in recovery_attempts if e.recovery_successful]
        return len(successful_recoveries) / len(recovery_attempts)
    
    def get_recent_errors(self, count: int = 10) -> List[ErrorRecord]:
        """获取最近的错误记录"""
        with self._lock:
            return self._error_history[-count:] if self._error_history else []
