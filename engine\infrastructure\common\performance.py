# -*- coding: utf-8 -*-
"""
性能监控和内存管理 - 提供系统性能监控和内存优化功能
"""

import time
import psutil
import gc
import threading
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass
from engine.utils.logger import log
from engine.utils.i18n import _


@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    timestamp: float
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    memory_available_mb: float
    operation_name: str
    duration: float
    success: bool


class PerformanceMonitor:
    """
    性能监控器
    监控系统资源使用情况和操作性能
    """
    
    def __init__(self, max_history: int = 1000):
        """
        初始化性能监控器
        
        Args:
            max_history: 最大历史记录数
        """
        self.max_history = max_history
        self._metrics_history: List[PerformanceMetrics] = []
        self._operation_stats: Dict[str, Dict[str, Any]] = {}
        self._lock = threading.Lock()
        
        log(_("performance_monitor.initialized"), "info", max_history=max_history)
    
    def start_operation(self, operation_name: str) -> Dict[str, Any]:
        """
        开始监控操作
        
        Args:
            operation_name: 操作名称
            
        Returns: Dict[str, Any]: 操作上下文
        """
        context = {
            'operation_name': operation_name,
            'start_time': time.time(),
            'start_memory': self._get_memory_usage(),
            'start_cpu': psutil.cpu_percent()
        }
        
        log(_("performance_monitor.operation_started"), "debug", operation=operation_name)
        return context
    
    def end_operation(self, context: Dict[str, Any], success: bool = True) -> PerformanceMetrics:
        """
        结束监控操作
        
        Args:
            context: 操作上下文
            success: 操作是否成功
            
        Returns:
            PerformanceMetrics: 性能指标
        """
        end_time = time.time()
        duration = end_time - context['start_time']
        
        # 收集性能指标
        metrics = PerformanceMetrics(
            timestamp=end_time,
            cpu_percent=psutil.cpu_percent(),
            memory_percent=psutil.virtual_memory().percent,
            memory_used_mb=self._get_memory_usage(),
            memory_available_mb=psutil.virtual_memory().available / 1024 / 1024,
            operation_name=context['operation_name'],
            duration=duration,
            success=success
        )
        
        # 记录指标
        self._record_metrics(metrics)
        
        # 更新操作统计
        self._update_operation_stats(context['operation_name'], duration, success)
        
        log(_("performance_monitor.operation_completed"), "debug",
            operation=context['operation_name'], duration=duration, success=success)
        
        return metrics
    
    def _get_memory_usage(self) -> float:
        """获取当前内存使用量（MB）"""
        process = psutil.Process()
        return process.memory_info().rss / 1024 / 1024
    
    def _record_metrics(self, metrics: PerformanceMetrics):
        """记录性能指标"""
        with self._lock:
            self._metrics_history.append(metrics)
            
            # 保持历史记录在限制范围内
            if len(self._metrics_history) > self.max_history:
                self._metrics_history.pop(0)
    
    def _update_operation_stats(self, operation_name: str, duration: float, success: bool):
        """更新操作统计"""
        with self._lock:
            if operation_name not in self._operation_stats:
                self._operation_stats[operation_name] = {
                    'total_count': 0,
                    'success_count': 0,
                    'total_duration': 0.0,
                    'min_duration': float('inf'),
                    'max_duration': 0.0,
                    'avg_duration': 0.0
                }
            
            stats = self._operation_stats[operation_name]
            stats['total_count'] += 1
            if success:
                stats['success_count'] += 1
            
            stats['total_duration'] += duration
            stats['min_duration'] = min(stats['min_duration'], duration)
            stats['max_duration'] = max(stats['max_duration'], duration)
            stats['avg_duration'] = stats['total_duration'] / stats['total_count']
    
    def get_current_metrics(self) -> Dict[str, Any]:
        """获取当前系统指标"""
        return {
            'cpu_percent': psutil.cpu_percent(),
            'memory_percent': psutil.virtual_memory().percent,
            'memory_used_mb': self._get_memory_usage(),
            'memory_available_mb': psutil.virtual_memory().available / 1024 / 1024,
            'timestamp': time.time()
        }
    
    def get_operation_stats(self, operation_name: Optional[str] = None) -> Dict[str, Any]:
        """
        获取操作统计信息
        
        Args:
            operation_name: 操作名称，None表示获取所有操作
            
        Returns: Dict[str, Any]: 统计信息
        """
        with self._lock:
            if operation_name:
                return self._operation_stats.get(operation_name, {})
            else:
                return self._operation_stats.copy()
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        with self._lock:
            if not self._metrics_history:
                return {}
            
            recent_metrics = self._metrics_history[-10:]  # 最近10次操作
            
            return {
                'total_operations': len(self._metrics_history),
                'recent_avg_duration': sum(m.duration for m in recent_metrics) / len(recent_metrics),
                'recent_success_rate': sum(1 for m in recent_metrics if m.success) / len(recent_metrics),
                'current_memory_mb': self._get_memory_usage(),
                'peak_memory_mb': max(m.memory_used_mb for m in self._metrics_history),
                'operation_types': len(self._operation_stats)
            }


class MemoryManager:
    """
    内存管理器
    提供内存优化和垃圾回收功能
    """
    
    def __init__(self, memory_threshold_mb: float = 500.0):
        """
        初始化内存管理器
        
        Args:
            memory_threshold_mb: 内存阈值（MB）
        """
        self.memory_threshold_mb = memory_threshold_mb
        self._gc_stats = {
            'manual_collections': 0,
            'auto_collections': 0,
            'memory_freed_mb': 0.0
        }
        
        log(_("memory_manager.initialized"), "info", threshold=memory_threshold_mb)
    
    def check_memory_usage(self) -> Dict[str, Any]:
        """检查内存使用情况"""
        process = psutil.Process()
        memory_info = process.memory_info()
        
        memory_usage = {
            'rss_mb': memory_info.rss / 1024 / 1024,
            'vms_mb': memory_info.vms / 1024 / 1024,
            'percent': psutil.virtual_memory().percent,
            'available_mb': psutil.virtual_memory().available / 1024 / 1024,
            'threshold_exceeded': memory_info.rss / 1024 / 1024 > self.memory_threshold_mb
        }
        
        return memory_usage
    
    def optimize_memory(self, force: bool = False) -> Dict[str, Any]:
        """
        优化内存使用
        
        Args:
            force: 是否强制执行优化
            
        Returns: Dict[str, Any]: 优化结果
        """
        memory_before = self.check_memory_usage()
        
        if force or memory_before['threshold_exceeded']:
            log(_("memory_manager.starting_optimization"), "info",
                memory_mb=memory_before['rss_mb'])
            
            # 执行垃圾回收
            collected = gc.collect()
            self._gc_stats['manual_collections'] += 1
            
            # 检查优化后的内存使用
            memory_after = self.check_memory_usage()
            memory_freed = memory_before['rss_mb'] - memory_after['rss_mb']
            self._gc_stats['memory_freed_mb'] += memory_freed
            
            result = {
                'optimized': True,
                'memory_before_mb': memory_before['rss_mb'],
                'memory_after_mb': memory_after['rss_mb'],
                'memory_freed_mb': memory_freed,
                'objects_collected': collected
            }
            
            log(_("memory_manager.optimization_completed"), "info",
                freed_mb=memory_freed, objects=collected)
            
            return result
        else:
            return {
                'optimized': False,
                'reason': 'threshold_not_exceeded',
                'current_memory_mb': memory_before['rss_mb']
            }
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """获取内存统计信息"""
        current_usage = self.check_memory_usage()
        
        return {
            'current_usage': current_usage,
            'threshold_mb': self.memory_threshold_mb,
            'gc_stats': self._gc_stats.copy(),
            'gc_counts': gc.get_count(),
            'gc_thresholds': gc.get_threshold()
        }
    
    def set_memory_threshold(self, threshold_mb: float):
        """设置内存阈值"""
        self.memory_threshold_mb = threshold_mb
        log(_("memory_manager.threshold_updated"), "info", threshold=threshold_mb)


def performance_monitor(operation_name: str):
    """
    性能监控装饰器
    
    Args:
        operation_name: 操作名称
    """
    def decorator(func: Callable) -> Callable:
        def wrapper(*args, **kwargs):
            monitor = PerformanceMonitor()
            context = monitor.start_operation(operation_name)
            
            try:
                result = func(*args, **kwargs)
                monitor.end_operation(context, success=True)
                return result
            except Exception as e:
                monitor.end_operation(context, success=False)
                raise
        
        return wrapper
    return decorator
