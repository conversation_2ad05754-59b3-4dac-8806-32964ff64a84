# -*- coding: utf-8 -*-
"""
配置管理器 - 统一管理引擎配置和资源路径
保持与现有逻辑的完全兼容性
"""

import os
from typing import Dict, Any, Optional
from engine.utils.logger import log
from engine.utils.i18n import _


class ConfigManager:
    """
    统一的配置管理器
    负责管理XML模板路径、YANG模型路径、接口映射等配置信息
    """

    def __init__(self):
        """初始化配置管理器"""
        self._config = {}
        self._engine_dir = self._detect_engine_directory()
        self._initialize_default_config()
        log(_("config_manager.initialized"), "info")

    def _detect_engine_directory(self) -> str:
        """
        检测引擎目录路径 - 保持与现有逻辑一致
        复用convert.py中的环境检测逻辑
        """
        # Current file is at engine/infrastructure/config/config_manager.py
        # Need to go up 3 levels to reach engine directory
        current_file_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

        # Check if running in container environment
        if os.path.exists('/app/engine') and current_file_dir.startswith('/app'):
            engine_dir = '/app/engine'
            log(_("config_manager.detected_container_environment"), "info", dir=engine_dir)
        else:
            # Development environment
            engine_dir = current_file_dir
            log(_("config_manager.detected_dev_environment"), "info", dir=engine_dir)

        return engine_dir

    def _initialize_default_config(self):
        """初始化默认配置"""
        self._config = {
            'engine_dir': self._engine_dir,
            'template_base_dir': os.path.join(self._engine_dir, 'data', 'input', 'configData'),
            'yang_base_dir': os.path.join(self._engine_dir, 'data', 'input', 'yang_models'),
            'mapping_base_dir': os.path.join(self._engine_dir, 'data', 'mappings'),
            'output_base_dir': os.path.join(self._engine_dir, 'data', 'output'),
            'temp_base_dir': os.path.join(self._engine_dir, 'data', 'temp'),
            'supported_vendors': {
                'fortigate': {
                    'parser_class': 'engine.parsers.fortigate_parser.FortigateParser',
                    'supported_versions': ['6.0', '6.2', '6.4', '7.0', '7.2']
                }
            },
            'supported_models': {
                'z5100s': {
                    'supported_versions': ['R10P2', 'R11', 'R8P1']
                },
                'z3200s': {
                    'supported_versions': ['R10P2', 'R11', 'R8P1']
                }
            },
            # twice-nat44相关配置
            'nat': {
                'use_twice_nat44': True,           # 启用twice-nat44转换
                'twice_nat44_fallback': True,      # 失败时回退到原有逻辑
                'validate_twice_nat44': True,      # 启用twice-nat44验证
                'twice_nat44_debug': False         # 调试模式
            }
        }
    
    def get_template_path(self, model: str, version: str, template_type: str = "running") -> str:
        """
        获取XML模板路径 - 保持与现有get_template_path函数完全一致的逻辑
        
        Args:
            model: 设备型号，如 'z5100s'
            version: 设备版本，如 'R10P2'  
            template_type: 模板类型，默认为 'running'
            
        Returns:
            str: 模板文件路径
            
        Raises:
            FileNotFoundError: 当模板文件不存在时
        """
        # Build main template path
        template_path = os.path.join(
            self._config['template_base_dir'],
            model,
            version,
            "Config",
            f"{template_type}.xml"
        )

        if os.path.exists(template_path):
            log(_("config_manager.template_found_new"), "info", path=template_path)
            return template_path
        
        # 尝试备用路径
        alternative_paths = [
            os.path.join(self._engine_dir, model, version, "Config", f"{template_type}.xml"),
            os.path.join(self._engine_dir, f"{version}_dec", f"Config-{model}", f"{template_type}.xml"),
            os.path.join(self._engine_dir, "data", "templates", model, version, f"{template_type}.xml")
        ]

        for alt_path in alternative_paths:
            if os.path.exists(alt_path):
                log(_("config_manager.template_found_alternative"), "info", path=alt_path)
                return alt_path
        
        # 如果都不存在，抛出异常
        error_msg = _("config_manager.template_not_found", model=model, version=version, type=template_type)
        log(error_msg, "error")
        raise FileNotFoundError(error_msg)
    
    def get_yang_model_dir(self, model: str, version: str) -> str:
        """
        获取YANG模型目录路径
        
        Args:
            model: 设备型号
            version: 设备版本
            
        Returns:
            str: YANG模型目录路径
            
        Raises:
            FileNotFoundError: 当YANG模型目录不存在时
        """
        yang_dir = os.path.join(self._config['yang_base_dir'], model, version)

        if not os.path.exists(yang_dir):
            error_msg = _("config_manager.yang_dir_not_found", model=model, version=version, dir=yang_dir)
            log(error_msg, "error")
            raise FileNotFoundError(error_msg)

        return yang_dir

    def get_config(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        return self._config.get(key, default)

    def set_config(self, key: str, value: Any):
        """设置配置值"""
        self._config[key] = value
        log(_("config_manager.config_updated"), "debug", key=key)

    def is_vendor_supported(self, vendor: str) -> bool:
        """检查厂商是否支持"""
        return vendor in self._config['supported_vendors']

    def is_model_version_supported(self, model: str, version: str) -> bool:
        """检查设备型号和版本是否支持"""
        if model not in self._config['supported_models']:
            return False
        return version in self._config['supported_models'][model]['supported_versions']

    def get_engine_dir(self) -> str:
        """获取引擎根目录"""
        return self._engine_dir

    def get_twice_nat44_config(self, key: str, default: Any = None) -> Any:
        """
        获取twice-nat44相关配置

        Args:
            key: 配置键名（如"use_twice_nat44"）
            default: 默认值

        Returns:
            Any: 配置值
        """
        nat_config = self._config.get('nat', {})
        return nat_config.get(key, default)

    def set_twice_nat44_config(self, key: str, value: Any):
        """
        设置twice-nat44相关配置

        Args:
            key: 配置键名
            value: 配置值
        """
        if 'nat' not in self._config:
            self._config['nat'] = {}

        self._config['nat'][key] = value
        log(_("config_manager.twice_nat44_config_updated", key=key, value=value), "debug")

    def validate_twice_nat44_config(self) -> bool:
        """
        验证twice-nat44配置的有效性

        Returns:
            bool: 配置是否有效
        """
        try:
            nat_config = self._config.get('nat', {})

            # 检查必需的配置项
            required_keys = ['use_twice_nat44', 'twice_nat44_fallback', 'validate_twice_nat44']
            for key in required_keys:
                if key not in nat_config:
                    log(_("config_manager.twice_nat44_missing_config", key=key), "warning")
                    return False

            # 检查配置值类型
            boolean_keys = ['use_twice_nat44', 'twice_nat44_fallback', 'validate_twice_nat44', 'twice_nat44_debug']
            for key in boolean_keys:
                if key in nat_config and not isinstance(nat_config[key], bool):
                    log(_("config_manager.twice_nat44_invalid_type", key=key), "warning")
                    return False

            return True

        except Exception as e:
            log(_("config_manager.twice_nat44_validation_failed", error=str(e)), "error")
            return False

    def is_twice_nat44_enabled(self) -> bool:
        """
        检查是否启用twice-nat44

        Returns:
            bool: 是否启用twice-nat44
        """
        return self.get_twice_nat44_config('use_twice_nat44', True)

    def is_twice_nat44_fallback_enabled(self) -> bool:
        """
        检查是否启用twice-nat44回退机制

        Returns:
            bool: 是否启用回退机制
        """
        return self.get_twice_nat44_config('twice_nat44_fallback', True)
