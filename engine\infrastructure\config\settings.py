# -*- coding: utf-8 -*-
"""
引擎设置 - 定义引擎的默认配置和常量
"""

import os
from engine.utils.i18n import _

# 引擎版本信息
ENGINE_VERSION = "2.0.0"
ENGINE_NAME = "NTOS Config Converter"
ARCHITECTURE_VERSION = "layered"

# 支持的厂商配置
SUPPORTED_VENDORS = {
    'fortigate': {
        'parser_class': 'engine.parsers.fortigate_parser.FortigateParser',
        'supported_versions': ['6.0', '6.2', '6.4', '7.0', '7.2'],
        'description': 'Fortinet FortiGate Firewall'
    }
}

# 支持的目标设备型号
SUPPORTED_MODELS = {
    'z5100s': {
        'supported_versions': ['R10P2', 'R11', 'R8P1'],
        'description': 'Ruijie Z5100S Series'
    },
    'z3200s': {
        'supported_versions': ['R10P2', 'R11', 'R8P1'],
        'description': 'Ruijie Z3200S Series'
    }
}

# 默认路径配置
DEFAULT_PATHS = {
    'template_base_dir': 'data/input/configData',
    'yang_base_dir': 'data/input/yang_models',
    'mapping_base_dir': 'data/mappings',
    'output_base_dir': 'data/output',
    'temp_base_dir': 'data/temp',
    'log_base_dir': 'logs'
}

# 缓存配置
CACHE_SETTINGS = {
    'template_cache': {
        'max_size': 50,
        'ttl': 3600  # 1小时
    },
    'yang_cache': {
        'max_size': 20,
        'ttl': 7200  # 2小时
    }
}

# 验证配置
VALIDATION_SETTINGS = {
    'yanglint_timeout': 30,  # 秒
    'xml_validation_enabled': True,
    'interface_validation_enabled': True,
    'semantic_validation_enabled': False  # 默认禁用语义验证，避免不准确的警告
}

# 日志配置
LOGGING_SETTINGS = {
    'default_level': 'INFO',  # 生产环境使用INFO级别，减少DEBUG日志干扰
    'debug_mode': False,      # 调试模式控制，可通过命令行参数启用
    'max_file_size': 10 * 1024 * 1024,  # 10MB
    'backup_count': 5,
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
}

# 国际化配置
I18N_SETTINGS = {
    'default_language': 'zh-CN',
    'supported_languages': ['zh-CN', 'en-US'],
    'locale_dir': 'locales'
}

# 性能配置
PERFORMANCE_SETTINGS = {
    'max_file_size': 100 * 1024 * 1024,  # 100MB
    'chunk_size': 8192,
    'parallel_processing': False,  # 暂时禁用并行处理
    'memory_limit': 512 * 1024 * 1024  # 512MB
}

# 安全配置
SECURITY_SETTINGS = {
    'max_path_depth': 10,
    'allowed_file_extensions': ['.conf', '.cfg', '.txt', '.xml', '.json'],
    'sanitize_logs': True
}

# NAT转换配置
NAT_SETTINGS = {
    'use_twice_nat44': True,           # 启用twice-nat44转换
    'twice_nat44_fallback': True,      # 失败时回退到原有逻辑
    'validate_twice_nat44': True,      # 启用twice-nat44验证
    'twice_nat44_debug': False,        # 调试模式
    'twice_nat44_whitelist': [],       # 白名单场景（用于渐进部署）
    'twice_nat44_blacklist': []        # 黑名单场景（强制使用原有逻辑）
}


class Settings:
    """
    设置类 - 提供配置访问接口
    """
    
    def __init__(self):
        """初始化设置"""
        self._settings = {
            'engine': {
                'version': ENGINE_VERSION,
                'name': ENGINE_NAME,
                'architecture': ARCHITECTURE_VERSION
            },
            'vendors': SUPPORTED_VENDORS,
            'models': SUPPORTED_MODELS,
            'paths': DEFAULT_PATHS,
            'cache': CACHE_SETTINGS,
            'validation': VALIDATION_SETTINGS,
            'logging': LOGGING_SETTINGS,
            'i18n': I18N_SETTINGS,
            'performance': PERFORMANCE_SETTINGS,
            'security': SECURITY_SETTINGS,
            'nat': NAT_SETTINGS
        }
    
    def get(self, key: str, default=None):
        """
        获取设置值
        
        Args:
            key: 设置键，支持点号分隔的嵌套键
            default: 默认值
            
        Returns:
            设置值
        """
        keys = key.split('.')
        value = self._settings
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value):
        """
        设置配置值
        
        Args:
            key: 设置键，支持点号分隔的嵌套键
            value: 设置值
        """
        keys = key.split('.')
        target = self._settings
        
        for k in keys[:-1]:
            if k not in target:
                target[k] = {}
            target = target[k]
        
        target[keys[-1]] = value
    
    def get_all(self) -> dict:
        """获取所有设置"""
        return self._settings.copy()
    
    def is_vendor_supported(self, vendor: str) -> bool:
        """检查厂商是否支持"""
        return vendor in self._settings['vendors']
    
    def is_model_supported(self, model: str) -> bool:
        """检查设备型号是否支持"""
        return model in self._settings['models']
    
    def get_supported_vendors(self) -> list:
        """获取支持的厂商列表"""
        return list(self._settings['vendors'].keys())
    
    def get_supported_models(self) -> list:
        """获取支持的设备型号列表"""
        return list(self._settings['models'].keys())
