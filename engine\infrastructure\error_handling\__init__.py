# -*- coding: utf-8 -*-
"""
twice-nat44错误处理模块

提供企业级的错误处理、日志记录和错误恢复机制，专门针对twice-nat44转换过程。

主要组件：
- TwiceNat44ErrorHandler: 核心错误处理器
- TwiceNat44ErrorContext: 错误上下文管理
- 错误处理装饰器: 便捷的错误处理装饰器
- 错误恢复策略: 自动错误恢复机制
"""

from .twice_nat44_error_handler import (
    TwiceNat44ErrorType,
    TwiceNat44ErrorSeverity,
    TwiceNat44ErrorContext,
    TwiceNat44ErrorRecord,
    TwiceNat44ErrorHandler,
    get_twice_nat44_error_handler,
    handle_twice_nat44_error
)

from .decorators import (
    twice_nat44_error_handler,
    twice_nat44_performance_monitor,
    twice_nat44_validation_required,
    twice_nat44_thread_safe,
    twice_nat44_cache_result,
    twice_nat44_operation_context,
    twice_nat44_performance_optimized,
    twice_nat44_memory_optimized
)

__all__ = [
    # 错误处理核心类
    'TwiceNat44ErrorType',
    'TwiceNat44ErrorSeverity', 
    'TwiceNat44ErrorContext',
    'TwiceNat44ErrorRecord',
    'TwiceNat44ErrorHandler',
    
    # 便捷函数
    'get_twice_nat44_error_handler',
    'handle_twice_nat44_error',
    
    # 装饰器
    'twice_nat44_error_handler',
    'twice_nat44_performance_monitor',
    'twice_nat44_validation_required',
    'twice_nat44_thread_safe',
    'twice_nat44_cache_result',
    'twice_nat44_operation_context',
    'twice_nat44_performance_optimized',
    'twice_nat44_memory_optimized'
]

# 版本信息
__version__ = "1.0.0"
__author__ = "FortiGate twice-nat44 Conversion Team"
