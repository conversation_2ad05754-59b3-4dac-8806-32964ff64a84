# -*- coding: utf-8 -*-
"""
twice-nat44错误处理装饰器

提供便捷的装饰器来自动处理twice-nat44相关的错误，包括重试机制、性能监控和错误恢复。
"""

import functools
import time
import threading
from typing import Any, Callable, Dict, Optional, Type, Union, List
from contextlib import contextmanager

from engine.utils.logger import log
from engine.utils.i18n import translate as _
from engine.infrastructure.error_handling.twice_nat44_error_handler import (
    TwiceNat44ErrorHandler, TwiceNat44ErrorContext, TwiceNat44ErrorType,
    get_twice_nat44_error_handler
)


def twice_nat44_error_handler(operation: str = None,
                            max_retries: int = 3,
                            retry_delay: float = 1.0,
                            retry_backoff: float = 2.0,
                            handle_exceptions: tuple = None,
                            ignore_exceptions: tuple = None,
                            fallback_value: Any = None,
                            log_errors: bool = True,
                            attempt_recovery: bool = True):
    """
    twice-nat44错误处理装饰器
    
    Args:
        operation: 操作名称
        max_retries: 最大重试次数
        retry_delay: 重试延迟（秒）
        retry_backoff: 重试退避倍数
        handle_exceptions: 要处理的异常类型元组
        ignore_exceptions: 要忽略的异常类型元组
        fallback_value: 失败时的回退值
        log_errors: 是否记录错误日志
        attempt_recovery: 是否尝试错误恢复
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 确定操作名称
            op_name = operation or f"{func.__module__}.{func.__name__}"
            
            # 创建错误上下文
            context = TwiceNat44ErrorContext(
                operation=op_name,
                max_retries=max_retries
            )
            
            # 从kwargs中提取上下文信息
            if 'policy_name' in kwargs:
                context.policy_name = kwargs['policy_name']
            if 'vip_name' in kwargs:
                context.vip_name = kwargs['vip_name']
            if 'rule_name' in kwargs:
                context.rule_name = kwargs['rule_name']
            
            handler = get_twice_nat44_error_handler()
            last_exception = None
            
            for attempt in range(max_retries + 1):
                context.retry_count = attempt
                
                try:
                    # 记录性能指标
                    start_time = time.time()
                    result = func(*args, **kwargs)
                    end_time = time.time()
                    
                    context.performance_metrics['execution_time'] = end_time - start_time
                    
                    # 如果有重试，记录成功恢复
                    if attempt > 0 and log_errors:
                        log(_("twice_nat44_decorator.retry_success"), "info",
                            operation=op_name, attempt=attempt + 1)
                    
                    return result
                    
                except Exception as e:
                    last_exception = e
                    
                    # 检查是否应该忽略此异常
                    if ignore_exceptions and isinstance(e, ignore_exceptions):
                        raise
                    
                    # 检查是否应该处理此异常
                    if handle_exceptions and not isinstance(e, handle_exceptions):
                        raise
                    
                    # 如果是最后一次尝试，进行完整的错误处理
                    if attempt == max_retries:
                        if log_errors:
                            error_result = handler.handle_twice_nat44_error(
                                e, context, attempt_recovery
                            )
                            
                            # 如果恢复成功，返回恢复结果
                            if error_result.get('recoverable', False):
                                recovery_result = error_result.get('recovery_result', {})
                                if recovery_result.get('success', False):
                                    return recovery_result.get('details', fallback_value)
                        
                        # 如果有回退值，返回回退值
                        if fallback_value is not None:
                            if log_errors:
                                log(_("twice_nat44_decorator.using_fallback"), "warning",
                                    operation=op_name, fallback=str(fallback_value))
                            return fallback_value
                        
                        # 否则重新抛出异常
                        raise
                    
                    # 计算重试延迟
                    delay = retry_delay * (retry_backoff ** attempt)
                    
                    if log_errors:
                        log(_("twice_nat44_decorator.retry_attempt"), "warning",
                            operation=op_name, attempt=attempt + 1, 
                            max_retries=max_retries, delay=delay, error=str(e))
                    
                    time.sleep(delay)
            
            # 理论上不应该到达这里
            if last_exception:
                raise last_exception
                
        return wrapper
    return decorator


def twice_nat44_performance_monitor(operation: str = None,
                                  log_performance: bool = True,
                                  performance_threshold: float = 5.0):
    """
    twice-nat44性能监控装饰器
    
    Args:
        operation: 操作名称
        log_performance: 是否记录性能日志
        performance_threshold: 性能阈值（秒），超过此值将记录警告
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            op_name = operation or f"{func.__module__}.{func.__name__}"
            
            start_time = time.time()
            start_memory = _get_memory_usage()
            
            try:
                result = func(*args, **kwargs)
                
                end_time = time.time()
                end_memory = _get_memory_usage()
                
                execution_time = end_time - start_time
                memory_delta = end_memory - start_memory
                
                if log_performance:
                    log_level = "warning" if execution_time > performance_threshold else "info"
                    log(_("twice_nat44_performance.execution_completed"), log_level,
                        operation=op_name,
                        execution_time=f"{execution_time:.3f}s",
                        memory_delta=f"{memory_delta:.1f}MB")
                
                return result
                
            except Exception as e:
                end_time = time.time()
                execution_time = end_time - start_time
                
                if log_performance:
                    log(_("twice_nat44_performance.execution_failed"), "error",
                        operation=op_name,
                        execution_time=f"{execution_time:.3f}s",
                        error=str(e))
                
                raise
                
        return wrapper
    return decorator


def twice_nat44_validation_required(validate_input: bool = True,
                                  validate_output: bool = False,
                                  input_validator: Callable = None,
                                  output_validator: Callable = None):
    """
    twice-nat44验证装饰器
    
    Args:
        validate_input: 是否验证输入
        validate_output: 是否验证输出
        input_validator: 自定义输入验证器
        output_validator: 自定义输出验证器
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 输入验证
            if validate_input:
                if input_validator:
                    validation_result = input_validator(*args, **kwargs)
                    if not validation_result.get('valid', True):
                        raise ValueError(f"Input validation failed: {validation_result.get('error')}")
                else:
                    # 默认输入验证
                    _default_input_validation(*args, **kwargs)
            
            # 执行函数
            result = func(*args, **kwargs)
            
            # 输出验证
            if validate_output:
                if output_validator:
                    validation_result = output_validator(result)
                    if not validation_result.get('valid', True):
                        raise ValueError(f"Output validation failed: {validation_result.get('error')}")
                else:
                    # 默认输出验证
                    _default_output_validation(result)
            
            return result
            
        return wrapper
    return decorator


def twice_nat44_thread_safe(lock: threading.Lock = None):
    """
    twice-nat44线程安全装饰器
    
    Args:
        lock: 自定义锁对象，如果不提供则创建新锁
    """
    if lock is None:
        lock = threading.Lock()
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            with lock:
                return func(*args, **kwargs)
        return wrapper
    return decorator


def twice_nat44_cache_result(cache_key_func: Callable = None,
                           cache_ttl: int = 300,
                           max_cache_size: int = 100):
    """
    twice-nat44结果缓存装饰器
    
    Args:
        cache_key_func: 缓存键生成函数
        cache_ttl: 缓存生存时间（秒）
        max_cache_size: 最大缓存大小
    """
    cache = {}
    cache_times = {}
    cache_lock = threading.Lock()
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            if cache_key_func:
                cache_key = cache_key_func(*args, **kwargs)
            else:
                cache_key = _generate_default_cache_key(func, *args, **kwargs)
            
            current_time = time.time()
            
            with cache_lock:
                # 检查缓存是否存在且未过期
                if (cache_key in cache and 
                    cache_key in cache_times and
                    current_time - cache_times[cache_key] < cache_ttl):
                    
                    log(_("twice_nat44_cache.cache_hit"), "debug",
                        cache_key=cache_key, function=func.__name__)
                    return cache[cache_key]
                
                # 清理过期缓存
                _cleanup_expired_cache(cache, cache_times, current_time, cache_ttl)
                
                # 限制缓存大小
                if len(cache) >= max_cache_size:
                    _evict_oldest_cache_entry(cache, cache_times)
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            
            with cache_lock:
                cache[cache_key] = result
                cache_times[cache_key] = current_time
                
                log(_("twice_nat44_cache.cache_stored"), "debug",
                    cache_key=cache_key, function=func.__name__)
            
            return result
            
        return wrapper
    return decorator


@contextmanager
def twice_nat44_operation_context(operation: str, **context_kwargs):
    """
    twice-nat44操作上下文管理器
    
    Args:
        operation: 操作名称
        **context_kwargs: 上下文参数
    """
    context = TwiceNat44ErrorContext(operation=operation, **context_kwargs)
    handler = get_twice_nat44_error_handler()
    
    start_time = time.time()
    
    try:
        log(_("twice_nat44_context.operation_started"), "info", operation=operation)
        yield context
        
        end_time = time.time()
        context.performance_metrics['total_time'] = end_time - start_time
        
        log(_("twice_nat44_context.operation_completed"), "info",
            operation=operation, duration=f"{end_time - start_time:.3f}s")
            
    except Exception as e:
        end_time = time.time()
        context.performance_metrics['total_time'] = end_time - start_time
        
        # 自动处理错误
        error_result = handler.handle_twice_nat44_error(e, context)
        
        log(_("twice_nat44_context.operation_failed"), "error",
            operation=operation, duration=f"{end_time - start_time:.3f}s",
            error_id=error_result.get('error_id'))
        
        # 如果不可恢复，重新抛出异常
        if not error_result.get('recoverable', False):
            raise


def _get_memory_usage() -> float:
    """获取当前内存使用量（MB）"""
    try:
        import psutil
        process = psutil.Process()
        return process.memory_info().rss / 1024 / 1024
    except ImportError:
        return 0.0


def _default_input_validation(*args, **kwargs):
    """默认输入验证"""
    # 检查None值
    for i, arg in enumerate(args):
        if arg is None:
            raise ValueError(f"Argument {i} cannot be None")
    
    for key, value in kwargs.items():
        if value is None and key in ['policy_name', 'vip_name', 'rule_name']:
            raise ValueError(f"Parameter {key} cannot be None")


def _default_output_validation(result):
    """默认输出验证"""
    if result is None:
        raise ValueError("Function result cannot be None")


def _generate_default_cache_key(func: Callable, *args, **kwargs) -> str:
    """生成默认缓存键"""
    import hashlib
    
    # 创建键字符串
    key_parts = [func.__name__]
    key_parts.extend(str(arg) for arg in args)
    key_parts.extend(f"{k}={v}" for k, v in sorted(kwargs.items()))
    
    key_string = "|".join(key_parts)
    
    # 生成哈希
    return hashlib.md5(key_string.encode()).hexdigest()


def _cleanup_expired_cache(cache: dict, cache_times: dict, current_time: float, ttl: int):
    """清理过期缓存"""
    expired_keys = [
        key for key, timestamp in cache_times.items()
        if current_time - timestamp >= ttl
    ]
    
    for key in expired_keys:
        cache.pop(key, None)
        cache_times.pop(key, None)


def _evict_oldest_cache_entry(cache: dict, cache_times: dict):
    """驱逐最旧的缓存条目"""
    if not cache_times:
        return

    oldest_key = min(cache_times.keys(), key=lambda k: cache_times[k])
    cache.pop(oldest_key, None)
    cache_times.pop(oldest_key, None)


def twice_nat44_performance_optimized(use_cache: bool = True,
                                    use_object_pool: bool = True,
                                    batch_size: int = 100,
                                    enable_gc: bool = True):
    """
    twice-nat44性能优化装饰器

    Args:
        use_cache: 是否使用缓存
        use_object_pool: 是否使用对象池
        batch_size: 批处理大小
        enable_gc: 是否启用垃圾回收
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            from engine.infrastructure.performance import get_twice_nat44_optimizer

            optimizer = get_twice_nat44_optimizer()

            # 如果是批量处理，使用优化器
            if 'rules' in kwargs and isinstance(kwargs['rules'], list):
                rules = kwargs['rules']

                def processor(rule):
                    # 创建单规则参数
                    single_args = args
                    single_kwargs = {k: v for k, v in kwargs.items() if k != 'rules'}
                    single_kwargs['rule'] = rule
                    return func(*single_args, **single_kwargs)

                results, metrics = optimizer.optimize_batch_processing(rules, processor)
                return results
            else:
                # 单规则处理
                return func(*args, **kwargs)

        return wrapper
    return decorator


def twice_nat44_memory_optimized(gc_threshold: int = 100,
                               memory_limit: float = 1024.0):
    """
    twice-nat44内存优化装饰器

    Args:
        gc_threshold: 垃圾回收阈值
        memory_limit: 内存限制（MB）
    """
    call_count = 0

    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            nonlocal call_count
            call_count += 1

            # 检查内存使用
            try:
                import psutil
                process = psutil.Process()
                memory_usage = process.memory_info().rss / 1024 / 1024

                if memory_usage > memory_limit:
                    log(_("twice_nat44_memory.limit_exceeded"), "warning",
                        current=memory_usage, limit=memory_limit)

                    # 执行内存优化
                    from engine.infrastructure.performance import get_twice_nat44_optimizer
                    optimizer = get_twice_nat44_optimizer()
                    optimizer.optimize_memory_usage()

            except ImportError:
                pass

            # 执行函数
            result = func(*args, **kwargs)

            # 定期执行垃圾回收
            if call_count % gc_threshold == 0:
                import gc
                collected = gc.collect()
                log(_("twice_nat44_memory.gc_executed"), "debug",
                    call_count=call_count, collected=collected)

            return result

        return wrapper
    return decorator
