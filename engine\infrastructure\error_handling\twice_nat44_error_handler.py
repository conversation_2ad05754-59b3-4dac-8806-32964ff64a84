# -*- coding: utf-8 -*-
"""
FortiGate twice-nat44转换专用错误处理框架

提供企业级的错误处理、日志记录和错误恢复机制，专门针对twice-nat44转换过程中的各种异常情况。
"""

import traceback
import threading
import time
import gc
from typing import Dict, Any, Optional, List, Callable, Type, Union
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from contextlib import contextmanager

from engine.utils.logger import log
from engine.utils.i18n import translate as _
# 避免循环导入，异常类将在运行时动态导入
from engine.infrastructure.common.error_handling import ErrorHandler, ErrorSeverity, ErrorRecord


class TwiceNat44ErrorType(Enum):
    """twice-nat44错误类型"""
    CONFIG_ERROR = "config_error"
    VALIDATION_ERROR = "validation_error"
    CONVERSION_ERROR = "conversion_error"
    XML_GENERATION_ERROR = "xml_generation_error"
    TEMPLATE_INTEGRATION_ERROR = "template_integration_error"
    PERFORMANCE_ERROR = "performance_error"
    RESOURCE_ERROR = "resource_error"
    NETWORK_ERROR = "network_error"
    UNKNOWN_ERROR = "unknown_error"


class TwiceNat44ErrorSeverity(Enum):
    """twice-nat44错误严重程度"""
    INFO = "info"           # 信息性错误，不影响功能
    WARNING = "warning"     # 警告，可能影响部分功能
    ERROR = "error"         # 错误，影响当前操作
    CRITICAL = "critical"   # 严重错误，影响整个转换过程
    FATAL = "fatal"         # 致命错误，需要立即停止


@dataclass
class TwiceNat44ErrorContext:
    """twice-nat44错误上下文"""
    operation: str                              # 当前操作
    policy_name: Optional[str] = None          # FortiGate策略名称
    vip_name: Optional[str] = None             # VIP对象名称
    rule_name: Optional[str] = None            # twice-nat44规则名称
    stage: Optional[str] = None                # 处理阶段
    input_data: Optional[Dict[str, Any]] = None # 输入数据
    partial_result: Optional[Dict[str, Any]] = None # 部分结果
    performance_metrics: Dict[str, float] = field(default_factory=dict) # 性能指标
    retry_count: int = 0                       # 重试次数
    max_retries: int = 3                       # 最大重试次数


@dataclass
class TwiceNat44ErrorRecord:
    """twice-nat44错误记录"""
    timestamp: datetime
    error_id: str
    error_type: TwiceNat44ErrorType
    error_severity: TwiceNat44ErrorSeverity
    error_message: str
    technical_details: str
    context: TwiceNat44ErrorContext
    stack_trace: str
    recovery_attempted: bool
    recovery_successful: bool
    recovery_strategy: Optional[str] = None
    recovery_details: Optional[Dict[str, Any]] = None
    user_impact: Optional[str] = None
    suggested_action: Optional[str] = None


class TwiceNat44ErrorHandler:
    """twice-nat44专用错误处理器"""
    
    def __init__(self, max_error_history: int = 1000):
        """
        初始化twice-nat44错误处理器
        
        Args:
            max_error_history: 最大错误历史记录数
        """
        self.max_error_history = max_error_history
        self._error_history: List[TwiceNat44ErrorRecord] = []
        self._error_stats: Dict[str, int] = {}
        self._recovery_strategies: Dict[TwiceNat44ErrorType, Callable] = {}
        self._lock = threading.Lock()
        
        # 注册默认恢复策略
        self._register_default_recovery_strategies()
        
        log(_("twice_nat44_error_handler.initialized"), "info", 
            max_history=max_error_history)
    
    def _register_default_recovery_strategies(self):
        """注册默认恢复策略"""
        self._recovery_strategies.update({
            TwiceNat44ErrorType.CONFIG_ERROR: self._recover_config_error,
            TwiceNat44ErrorType.VALIDATION_ERROR: self._recover_validation_error,
            TwiceNat44ErrorType.CONVERSION_ERROR: self._recover_conversion_error,
            TwiceNat44ErrorType.XML_GENERATION_ERROR: self._recover_xml_generation_error,
            TwiceNat44ErrorType.TEMPLATE_INTEGRATION_ERROR: self._recover_template_integration_error,
            TwiceNat44ErrorType.PERFORMANCE_ERROR: self._recover_performance_error,
            TwiceNat44ErrorType.RESOURCE_ERROR: self._recover_resource_error,
            TwiceNat44ErrorType.NETWORK_ERROR: self._recover_network_error,
        })
    
    def handle_twice_nat44_error(self, 
                                exception: Exception,
                                context: TwiceNat44ErrorContext,
                                attempt_recovery: bool = True) -> Dict[str, Any]:
        """
        处理twice-nat44相关错误
        
        Args:
            exception: 异常实例
            context: 错误上下文
            attempt_recovery: 是否尝试恢复
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        # 分类错误类型
        error_type = self._classify_error(exception)
        error_severity = self._determine_severity(exception, error_type, context)
        
        # 生成错误ID
        error_id = self._generate_error_id(error_type, context)
        
        # 创建错误记录
        error_record = TwiceNat44ErrorRecord(
            timestamp=datetime.now(),
            error_id=error_id,
            error_type=error_type,
            error_severity=error_severity,
            error_message=str(exception),
            technical_details=self._extract_technical_details(exception),
            context=context,
            stack_trace=traceback.format_exc(),
            recovery_attempted=attempt_recovery,
            recovery_successful=False,
            user_impact=self._assess_user_impact(error_type, error_severity, context),
            suggested_action=self._suggest_action(error_type, error_severity, context)
        )
        
        # 尝试恢复
        recovery_result = None
        if attempt_recovery and error_type in self._recovery_strategies:
            recovery_result = self._attempt_recovery(error_record)
            error_record.recovery_successful = recovery_result.get('success', False)
            error_record.recovery_strategy = recovery_result.get('strategy')
            error_record.recovery_details = recovery_result.get('details')
        
        # 记录错误历史
        self._record_error(error_record)
        
        # 记录日志
        self._log_error(error_record)
        
        # 更新统计信息
        self._update_error_stats(error_record)
        
        return {
            'error_record': error_record,
            'recovery_result': recovery_result,
            'error_id': error_id,
            'error_type': error_type.value,
            'severity': error_severity.value,
            'user_message': self._generate_user_message(error_record),
            'technical_message': error_record.technical_details,
            'suggested_action': error_record.suggested_action,
            'recoverable': recovery_result is not None and recovery_result.get('success', False),
            'handled': True
        }
    
    def _classify_error(self, exception: Exception) -> TwiceNat44ErrorType:
        """分类错误类型"""
        # 动态导入异常类以避免循环导入
        try:
            from engine.business.models.twice_nat44_models import TwiceNat44ConfigError, TwiceNat44ValidationError
            if isinstance(exception, TwiceNat44ConfigError):
                return TwiceNat44ErrorType.CONFIG_ERROR
            elif isinstance(exception, TwiceNat44ValidationError):
                return TwiceNat44ErrorType.VALIDATION_ERROR
        except ImportError:
            # 如果导入失败，使用异常名称进行分类
            exception_name = type(exception).__name__
            if "Config" in exception_name:
                return TwiceNat44ErrorType.CONFIG_ERROR
            elif "Validation" in exception_name:
                return TwiceNat44ErrorType.VALIDATION_ERROR

        if isinstance(exception, ValueError):
            return TwiceNat44ErrorType.CONVERSION_ERROR
        elif isinstance(exception, (AttributeError, KeyError)):
            return TwiceNat44ErrorType.XML_GENERATION_ERROR
        elif isinstance(exception, MemoryError):
            return TwiceNat44ErrorType.RESOURCE_ERROR
        elif isinstance(exception, (ConnectionError, TimeoutError)):
            return TwiceNat44ErrorType.NETWORK_ERROR
        else:
            return TwiceNat44ErrorType.UNKNOWN_ERROR
    
    def _determine_severity(self, exception: Exception, 
                          error_type: TwiceNat44ErrorType,
                          context: TwiceNat44ErrorContext) -> TwiceNat44ErrorSeverity:
        """确定错误严重程度"""
        # 基于异常类型的基础严重程度
        base_severity_map = {
            TwiceNat44ErrorType.CONFIG_ERROR: TwiceNat44ErrorSeverity.ERROR,
            TwiceNat44ErrorType.VALIDATION_ERROR: TwiceNat44ErrorSeverity.WARNING,
            TwiceNat44ErrorType.CONVERSION_ERROR: TwiceNat44ErrorSeverity.ERROR,
            TwiceNat44ErrorType.XML_GENERATION_ERROR: TwiceNat44ErrorSeverity.ERROR,
            TwiceNat44ErrorType.TEMPLATE_INTEGRATION_ERROR: TwiceNat44ErrorSeverity.ERROR,
            TwiceNat44ErrorType.PERFORMANCE_ERROR: TwiceNat44ErrorSeverity.WARNING,
            TwiceNat44ErrorType.RESOURCE_ERROR: TwiceNat44ErrorSeverity.CRITICAL,
            TwiceNat44ErrorType.NETWORK_ERROR: TwiceNat44ErrorSeverity.ERROR,
            TwiceNat44ErrorType.UNKNOWN_ERROR: TwiceNat44ErrorSeverity.ERROR,
        }
        
        base_severity = base_severity_map.get(error_type, TwiceNat44ErrorSeverity.ERROR)
        
        # 基于上下文调整严重程度
        if context.retry_count >= context.max_retries:
            # 重试次数达到上限，提升严重程度
            if base_severity == TwiceNat44ErrorSeverity.WARNING:
                return TwiceNat44ErrorSeverity.ERROR
            elif base_severity == TwiceNat44ErrorSeverity.ERROR:
                return TwiceNat44ErrorSeverity.CRITICAL
        
        # 基于操作类型调整
        critical_operations = ['batch_conversion', 'production_deployment']
        if context.operation in critical_operations:
            if base_severity in [TwiceNat44ErrorSeverity.WARNING, TwiceNat44ErrorSeverity.ERROR]:
                return TwiceNat44ErrorSeverity.CRITICAL
        
        return base_severity
    
    def _generate_error_id(self, error_type: TwiceNat44ErrorType, 
                          context: TwiceNat44ErrorContext) -> str:
        """生成错误ID"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
        operation = context.operation.replace(" ", "_").lower()
        return f"TN44_{error_type.value.upper()}_{operation}_{timestamp}"
    
    def _extract_technical_details(self, exception: Exception) -> str:
        """提取技术详情"""
        details = [f"Exception Type: {type(exception).__name__}"]
        details.append(f"Exception Message: {str(exception)}")
        
        # 添加异常特定的详情
        if hasattr(exception, '__dict__'):
            for key, value in exception.__dict__.items():
                if not key.startswith('_'):
                    details.append(f"{key}: {value}")
        
        return " | ".join(details)
    
    def _assess_user_impact(self, error_type: TwiceNat44ErrorType,
                           severity: TwiceNat44ErrorSeverity,
                           context: TwiceNat44ErrorContext) -> str:
        """评估用户影响"""
        impact_map = {
            (TwiceNat44ErrorType.CONFIG_ERROR, TwiceNat44ErrorSeverity.ERROR): 
                _("twice_nat44_error.impact.config_error"),
            (TwiceNat44ErrorType.VALIDATION_ERROR, TwiceNat44ErrorSeverity.WARNING):
                _("twice_nat44_error.impact.validation_warning"),
            (TwiceNat44ErrorType.CONVERSION_ERROR, TwiceNat44ErrorSeverity.ERROR):
                _("twice_nat44_error.impact.conversion_error"),
            (TwiceNat44ErrorType.RESOURCE_ERROR, TwiceNat44ErrorSeverity.CRITICAL):
                _("twice_nat44_error.impact.resource_critical"),
        }
        
        return impact_map.get((error_type, severity), 
                             _("twice_nat44_error.impact.general"))
    
    def _suggest_action(self, error_type: TwiceNat44ErrorType,
                       severity: TwiceNat44ErrorSeverity,
                       context: TwiceNat44ErrorContext) -> str:
        """建议处理动作"""
        action_map = {
            TwiceNat44ErrorType.CONFIG_ERROR: _("twice_nat44_error.action.check_config"),
            TwiceNat44ErrorType.VALIDATION_ERROR: _("twice_nat44_error.action.validate_input"),
            TwiceNat44ErrorType.CONVERSION_ERROR: _("twice_nat44_error.action.check_data_format"),
            TwiceNat44ErrorType.XML_GENERATION_ERROR: _("twice_nat44_error.action.check_template"),
            TwiceNat44ErrorType.RESOURCE_ERROR: _("twice_nat44_error.action.free_resources"),
            TwiceNat44ErrorType.NETWORK_ERROR: _("twice_nat44_error.action.check_network"),
        }
        
        return action_map.get(error_type, _("twice_nat44_error.action.contact_support"))
    
    def _attempt_recovery(self, error_record: TwiceNat44ErrorRecord) -> Dict[str, Any]:
        """尝试错误恢复"""
        recovery_strategy = self._recovery_strategies.get(error_record.error_type)
        
        if not recovery_strategy:
            return {'success': False, 'reason': 'no_recovery_strategy'}
        
        try:
            return recovery_strategy(error_record)
        except Exception as e:
            log(_("twice_nat44_error_handler.recovery_failed"), "error",
                error_id=error_record.error_id, recovery_error=str(e))
            return {'success': False, 'reason': 'recovery_exception', 'error': str(e)}
    
    def _recover_config_error(self, error_record: TwiceNat44ErrorRecord) -> Dict[str, Any]:
        """恢复配置错误"""
        context = error_record.context
        
        # 尝试使用默认配置
        if context.policy_name and not context.vip_name:
            return {
                'success': True,
                'strategy': 'use_default_vip',
                'details': {'default_vip': f"{context.policy_name}_DEFAULT_VIP"}
            }
        
        # 尝试修复无效IP地址
        if "Invalid IPv4 address" in error_record.error_message:
            return {
                'success': True,
                'strategy': 'use_default_ip',
                'details': {'default_ip': '*************'}
            }
        
        return {'success': False, 'reason': 'no_applicable_recovery'}
    
    def _recover_validation_error(self, error_record: TwiceNat44ErrorRecord) -> Dict[str, Any]:
        """恢复验证错误"""
        # 验证错误通常可以通过数据清理来恢复
        return {
            'success': True,
            'strategy': 'sanitize_and_retry',
            'details': {'action': 'remove_invalid_fields'}
        }
    
    def _recover_conversion_error(self, error_record: TwiceNat44ErrorRecord) -> Dict[str, Any]:
        """恢复转换错误"""
        context = error_record.context
        
        # 如果有部分结果，尝试使用部分结果
        if context.partial_result:
            return {
                'success': True,
                'strategy': 'use_partial_result',
                'details': {'partial_data': context.partial_result}
            }
        
        return {'success': False, 'reason': 'no_partial_result'}
    
    def _recover_xml_generation_error(self, error_record: TwiceNat44ErrorRecord) -> Dict[str, Any]:
        """恢复XML生成错误"""
        return {
            'success': True,
            'strategy': 'use_minimal_template',
            'details': {'template': 'minimal_twice_nat44_template'}
        }
    
    def _recover_template_integration_error(self, error_record: TwiceNat44ErrorRecord) -> Dict[str, Any]:
        """恢复模板集成错误"""
        return {
            'success': True,
            'strategy': 'skip_template_integration',
            'details': {'fallback': 'direct_xml_generation'}
        }
    
    def _recover_performance_error(self, error_record: TwiceNat44ErrorRecord) -> Dict[str, Any]:
        """恢复性能错误"""
        # 释放内存并重试
        gc.collect()
        return {
            'success': True,
            'strategy': 'free_memory_and_retry',
            'details': {'memory_freed': True}
        }
    
    def _recover_resource_error(self, error_record: TwiceNat44ErrorRecord) -> Dict[str, Any]:
        """恢复资源错误"""
        # 强制垃圾回收
        gc.collect()
        time.sleep(0.1)  # 短暂等待
        
        return {
            'success': True,
            'strategy': 'resource_cleanup_and_retry',
            'details': {'cleanup_performed': True}
        }
    
    def _recover_network_error(self, error_record: TwiceNat44ErrorRecord) -> Dict[str, Any]:
        """恢复网络错误"""
        context = error_record.context
        
        # 指数退避重试
        wait_time = min(2 ** context.retry_count, 30)  # 最大等待30秒
        
        return {
            'success': True,
            'strategy': 'exponential_backoff_retry',
            'details': {'wait_time': wait_time, 'retry_count': context.retry_count}
        }
    
    def _record_error(self, error_record: TwiceNat44ErrorRecord):
        """记录错误历史"""
        with self._lock:
            self._error_history.append(error_record)
            
            # 保持历史记录在限制范围内
            if len(self._error_history) > self.max_error_history:
                self._error_history = self._error_history[-self.max_error_history:]
    
    def _log_error(self, error_record: TwiceNat44ErrorRecord):
        """记录错误日志"""
        log_level_map = {
            TwiceNat44ErrorSeverity.INFO: "info",
            TwiceNat44ErrorSeverity.WARNING: "warning",
            TwiceNat44ErrorSeverity.ERROR: "error",
            TwiceNat44ErrorSeverity.CRITICAL: "error",
            TwiceNat44ErrorSeverity.FATAL: "error"
        }
        
        log_level = log_level_map.get(error_record.error_severity, "error")
        
        log(_("twice_nat44_error_handler.error_logged"), log_level,
            error_id=error_record.error_id,
            error_type=error_record.error_type.value,
            severity=error_record.error_severity.value,
            operation=error_record.context.operation,
            policy_name=error_record.context.policy_name,
            recovery_attempted=error_record.recovery_attempted,
            recovery_successful=error_record.recovery_successful)
    
    def _update_error_stats(self, error_record: TwiceNat44ErrorRecord):
        """更新错误统计"""
        with self._lock:
            error_key = f"{error_record.error_type.value}_{error_record.error_severity.value}"
            self._error_stats[error_key] = self._error_stats.get(error_key, 0) + 1
    
    def _generate_user_message(self, error_record: TwiceNat44ErrorRecord) -> str:
        """生成用户友好的错误消息"""
        message_key = f"twice_nat44_error.user_message.{error_record.error_type.value}"
        return _(message_key, 
                policy_name=error_record.context.policy_name,
                vip_name=error_record.context.vip_name,
                rule_name=error_record.context.rule_name)
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """获取错误统计信息"""
        with self._lock:
            total_errors = len(self._error_history)
            recent_errors = [e for e in self._error_history 
                           if (datetime.now() - e.timestamp).seconds < 3600]  # 最近1小时
            
            return {
                'total_errors': total_errors,
                'recent_errors': len(recent_errors),
                'error_types': dict(self._error_stats),
                'recovery_success_rate': self._calculate_recovery_success_rate(),
                'most_common_errors': self._get_most_common_errors()
            }
    
    def _calculate_recovery_success_rate(self) -> float:
        """计算恢复成功率"""
        if not self._error_history:
            return 0.0
        
        recovery_attempts = [e for e in self._error_history if e.recovery_attempted]
        if not recovery_attempts:
            return 0.0
        
        successful_recoveries = [e for e in recovery_attempts if e.recovery_successful]
        return len(successful_recoveries) / len(recovery_attempts) * 100
    
    def _get_most_common_errors(self, top_n: int = 5) -> List[Dict[str, Any]]:
        """获取最常见的错误"""
        sorted_errors = sorted(self._error_stats.items(), key=lambda x: x[1], reverse=True)
        return [{'error_type': k, 'count': v} for k, v in sorted_errors[:top_n]]
    
    @contextmanager
    def error_context(self, operation: str, **kwargs):
        """错误上下文管理器"""
        context = TwiceNat44ErrorContext(operation=operation, **kwargs)
        
        try:
            yield context
        except Exception as e:
            # 自动处理上下文中的错误
            result = self.handle_twice_nat44_error(e, context)
            
            # 如果恢复失败，重新抛出异常
            if not result.get('recoverable', False):
                raise
            
            # 如果恢复成功，记录恢复信息
            log(_("twice_nat44_error_handler.auto_recovery_success"), "info",
                error_id=result['error_id'], operation=operation)


# 全局twice-nat44错误处理器实例
_twice_nat44_error_handler = None


def get_twice_nat44_error_handler() -> TwiceNat44ErrorHandler:
    """获取全局twice-nat44错误处理器实例"""
    global _twice_nat44_error_handler
    if _twice_nat44_error_handler is None:
        _twice_nat44_error_handler = TwiceNat44ErrorHandler()
    return _twice_nat44_error_handler


def handle_twice_nat44_error(exception: Exception, 
                           context: TwiceNat44ErrorContext,
                           attempt_recovery: bool = True) -> Dict[str, Any]:
    """便捷的twice-nat44错误处理函数"""
    handler = get_twice_nat44_error_handler()
    return handler.handle_twice_nat44_error(exception, context, attempt_recovery)
