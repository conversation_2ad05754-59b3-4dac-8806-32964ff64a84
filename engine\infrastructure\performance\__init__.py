# -*- coding: utf-8 -*-
"""
twice-nat44性能优化模块

提供企业级的性能优化功能，包括：
- 批量处理优化
- 内存使用优化
- 缓存机制
- 对象池管理
- 性能监控
"""

from .twice_nat44_optimizer import (
    PerformanceMetrics,
    TwiceNat44ObjectPool,
    TwiceNat44Cache,
    TwiceNat44PerformanceOptimizer,
    get_twice_nat44_optimizer
)

__all__ = [
    'PerformanceMetrics',
    'TwiceNat44ObjectPool',
    'TwiceNat44Cache',
    'TwiceNat44PerformanceOptimizer',
    'get_twice_nat44_optimizer'
]

# 版本信息
__version__ = "1.0.0"
__author__ = "FortiGate twice-nat44 Conversion Team"
