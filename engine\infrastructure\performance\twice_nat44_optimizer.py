# -*- coding: utf-8 -*-
"""
FortiGate twice-nat44性能优化器

提供企业级的性能优化功能，包括：
- 内存使用优化
- 处理速度优化
- 批量处理优化
- 缓存机制优化
- 资源池管理
"""

import gc
import time
import threading
import weakref
from typing import Dict, List, Any, Optional, Callable, Tuple
from dataclasses import dataclass, field
from collections import defaultdict, deque
from concurrent.futures import ThreadPoolExecutor, as_completed
import psutil

from engine.utils.logger import log
from engine.utils.i18n import translate as _


@dataclass
class PerformanceMetrics:
    """性能指标数据结构"""
    operation: str
    start_time: float
    end_time: float
    memory_before: float
    memory_after: float
    cpu_usage: float
    rule_count: int
    success_count: int
    error_count: int
    
    @property
    def execution_time(self) -> float:
        """执行时间（秒）"""
        return self.end_time - self.start_time
    
    @property
    def memory_delta(self) -> float:
        """内存变化（MB）"""
        return self.memory_after - self.memory_before
    
    @property
    def throughput(self) -> float:
        """吞吐量（规则/秒）"""
        if self.execution_time > 0:
            return self.rule_count / self.execution_time
        return 0.0
    
    @property
    def success_rate(self) -> float:
        """成功率（%）"""
        if self.rule_count > 0:
            return (self.success_count / self.rule_count) * 100
        return 0.0


class TwiceNat44ObjectPool:
    """twice-nat44对象池"""
    
    def __init__(self, max_size: int = 1000):
        """
        初始化对象池
        
        Args:
            max_size: 对象池最大大小
        """
        self.max_size = max_size
        self._pools = defaultdict(deque)
        self._lock = threading.Lock()
        self._stats = defaultdict(int)
    
    def get_object(self, object_type: str, factory: Callable = None) -> Any:
        """
        从对象池获取对象
        
        Args:
            object_type: 对象类型
            factory: 对象工厂函数
            
        Returns:
            Any: 对象实例
        """
        with self._lock:
            pool = self._pools[object_type]
            
            if pool:
                obj = pool.popleft()
                self._stats[f"{object_type}_reused"] += 1
                return obj
            
            # 如果池中没有对象，创建新对象
            if factory:
                obj = factory()
                self._stats[f"{object_type}_created"] += 1
                return obj
            
            return None
    
    def return_object(self, object_type: str, obj: Any):
        """
        将对象返回到对象池
        
        Args:
            object_type: 对象类型
            obj: 对象实例
        """
        with self._lock:
            pool = self._pools[object_type]
            
            if len(pool) < self.max_size:
                # 清理对象状态
                if hasattr(obj, 'reset'):
                    obj.reset()
                
                pool.append(obj)
                self._stats[f"{object_type}_returned"] += 1
            else:
                self._stats[f"{object_type}_discarded"] += 1
    
    def get_stats(self) -> Dict[str, int]:
        """获取对象池统计信息"""
        with self._lock:
            return dict(self._stats)
    
    def clear(self):
        """清空对象池"""
        with self._lock:
            self._pools.clear()
            self._stats.clear()


class TwiceNat44Cache:
    """twice-nat44缓存管理器"""
    
    def __init__(self, max_size: int = 10000, ttl: int = 3600):
        """
        初始化缓存管理器
        
        Args:
            max_size: 最大缓存大小
            ttl: 缓存生存时间（秒）
        """
        self.max_size = max_size
        self.ttl = ttl
        self._cache = {}
        self._timestamps = {}
        self._access_times = {}
        self._lock = threading.Lock()
        self._stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'expired': 0
        }
    
    def get(self, key: str) -> Optional[Any]:
        """
        从缓存获取值
        
        Args:
            key: 缓存键
            
        Returns:
            Optional[Any]: 缓存值或None
        """
        with self._lock:
            current_time = time.time()
            
            if key in self._cache:
                # 检查是否过期
                if current_time - self._timestamps[key] > self.ttl:
                    self._remove_key(key)
                    self._stats['expired'] += 1
                    self._stats['misses'] += 1
                    return None
                
                # 更新访问时间
                self._access_times[key] = current_time
                self._stats['hits'] += 1
                return self._cache[key]
            
            self._stats['misses'] += 1
            return None
    
    def put(self, key: str, value: Any):
        """
        将值放入缓存
        
        Args:
            key: 缓存键
            value: 缓存值
        """
        with self._lock:
            current_time = time.time()
            
            # 如果缓存已满，执行LRU驱逐
            if len(self._cache) >= self.max_size and key not in self._cache:
                self._evict_lru()
            
            self._cache[key] = value
            self._timestamps[key] = current_time
            self._access_times[key] = current_time
    
    def _remove_key(self, key: str):
        """移除缓存键"""
        self._cache.pop(key, None)
        self._timestamps.pop(key, None)
        self._access_times.pop(key, None)
    
    def _evict_lru(self):
        """驱逐最近最少使用的缓存项"""
        if not self._access_times:
            return

        # 找到最久未访问的键
        lru_key = min(self._access_times.keys(), key=lambda k: self._access_times[k])
        self._remove_key(lru_key)
        self._stats['evictions'] += 1
    
    def clear(self):
        """清空缓存"""
        with self._lock:
            self._cache.clear()
            self._timestamps.clear()
            self._access_times.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self._lock:
            total_requests = self._stats['hits'] + self._stats['misses']
            hit_rate = (self._stats['hits'] / total_requests * 100) if total_requests > 0 else 0
            
            return {
                **self._stats,
                'cache_size': len(self._cache),
                'hit_rate': hit_rate,
                'total_requests': total_requests
            }


class TwiceNat44PerformanceOptimizer:
    """twice-nat44性能优化器"""
    
    def __init__(self):
        """初始化性能优化器"""
        self.object_pool = TwiceNat44ObjectPool()
        self.cache = TwiceNat44Cache()
        self.metrics_history = deque(maxlen=1000)
        self._lock = threading.Lock()
        
        # 性能配置
        self.batch_size = 100
        self.max_workers = min(4, psutil.cpu_count())
        self.memory_threshold = 1024  # MB
        self.gc_interval = 100  # 处理多少规则后执行GC
        
        log(_("twice_nat44_optimizer.initialized"), "info",
            batch_size=self.batch_size, max_workers=self.max_workers)
    
    def optimize_batch_processing(self, rules: List[Dict[str, Any]], 
                                processor: Callable) -> Tuple[List[Any], PerformanceMetrics]:
        """
        优化批量处理
        
        Args:
            rules: 规则列表
            processor: 处理函数
            
        Returns:
            Tuple[List[Any], PerformanceMetrics]: 处理结果和性能指标
        """
        start_time = time.time()
        memory_before = self._get_memory_usage()
        cpu_before = psutil.cpu_percent()
        
        results = []
        success_count = 0
        error_count = 0
        
        try:
            # 分批处理
            for i in range(0, len(rules), self.batch_size):
                batch = rules[i:i + self.batch_size]
                
                # 并行处理批次
                batch_results = self._process_batch_parallel(batch, processor)
                
                for result in batch_results:
                    if result.get('success', False):
                        success_count += 1
                        results.append(result['data'])
                    else:
                        error_count += 1
                
                # 定期执行垃圾回收
                if (i // self.batch_size + 1) % (self.gc_interval // self.batch_size) == 0:
                    self._perform_gc()
                
                # 检查内存使用
                current_memory = self._get_memory_usage()
                if current_memory > self.memory_threshold:
                    log(_("twice_nat44_optimizer.memory_warning"), "warning",
                        current=current_memory, threshold=self.memory_threshold)
                    self._perform_gc()
            
            end_time = time.time()
            memory_after = self._get_memory_usage()
            cpu_after = psutil.cpu_percent()
            
            # 创建性能指标
            metrics = PerformanceMetrics(
                operation="batch_processing",
                start_time=start_time,
                end_time=end_time,
                memory_before=memory_before,
                memory_after=memory_after,
                cpu_usage=(cpu_before + cpu_after) / 2,
                rule_count=len(rules),
                success_count=success_count,
                error_count=error_count
            )
            
            self._record_metrics(metrics)
            
            log(_("twice_nat44_optimizer.batch_completed"), "info",
                rule_count=len(rules), success_count=success_count,
                error_count=error_count, execution_time=f"{metrics.execution_time:.3f}s",
                throughput=f"{metrics.throughput:.1f}rules/s")
            
            return results, metrics
            
        except Exception as e:
            log(_("twice_nat44_optimizer.batch_error"), "error", error=str(e))
            raise
    
    def _process_batch_parallel(self, batch: List[Dict[str, Any]], 
                               processor: Callable) -> List[Dict[str, Any]]:
        """
        并行处理批次
        
        Args:
            batch: 批次数据
            processor: 处理函数
            
        Returns:
            List[Dict[str, Any]]: 处理结果
        """
        results = []
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交任务
            future_to_rule = {
                executor.submit(self._safe_process_rule, rule, processor): rule 
                for rule in batch
            }
            
            # 收集结果
            for future in as_completed(future_to_rule):
                rule = future_to_rule[future]
                try:
                    result = future.result()
                    results.append(result)
                except Exception as e:
                    results.append({
                        'success': False,
                        'error': str(e),
                        'rule': rule
                    })
        
        return results
    
    def _safe_process_rule(self, rule: Dict[str, Any], processor: Callable) -> Dict[str, Any]:
        """
        安全处理单个规则
        
        Args:
            rule: 规则数据
            processor: 处理函数
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            # 检查缓存
            cache_key = self._generate_cache_key(rule)
            cached_result = self.cache.get(cache_key)
            
            if cached_result is not None:
                return {'success': True, 'data': cached_result, 'cached': True}
            
            # 处理规则
            result = processor(rule)
            
            # 缓存结果
            self.cache.put(cache_key, result)
            
            return {'success': True, 'data': result, 'cached': False}
            
        except Exception as e:
            return {'success': False, 'error': str(e), 'rule': rule}
    
    def _generate_cache_key(self, rule: Dict[str, Any]) -> str:
        """
        生成缓存键
        
        Args:
            rule: 规则数据
            
        Returns:
            str: 缓存键
        """
        import hashlib
        
        # 创建规则的哈希键
        rule_str = str(sorted(rule.items()))
        return hashlib.md5(rule_str.encode()).hexdigest()
    
    def _get_memory_usage(self) -> float:
        """获取当前内存使用量（MB）"""
        try:
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except:
            return 0.0
    
    def _perform_gc(self):
        """执行垃圾回收"""
        collected = gc.collect()
        log(_("twice_nat44_optimizer.gc_performed"), "debug", collected=collected)
    
    def _record_metrics(self, metrics: PerformanceMetrics):
        """记录性能指标"""
        with self._lock:
            self.metrics_history.append(metrics)
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        with self._lock:
            if not self.metrics_history:
                return {}
            
            # 计算统计信息
            total_rules = sum(m.rule_count for m in self.metrics_history)
            total_time = sum(m.execution_time for m in self.metrics_history)
            avg_throughput = total_rules / total_time if total_time > 0 else 0
            
            avg_memory_delta = sum(m.memory_delta for m in self.metrics_history) / len(self.metrics_history)
            avg_success_rate = sum(m.success_rate for m in self.metrics_history) / len(self.metrics_history)
            
            return {
                'total_operations': len(self.metrics_history),
                'total_rules_processed': total_rules,
                'total_processing_time': total_time,
                'average_throughput': avg_throughput,
                'average_memory_delta': avg_memory_delta,
                'average_success_rate': avg_success_rate,
                'cache_stats': self.cache.get_stats(),
                'object_pool_stats': self.object_pool.get_stats()
            }
    
    def optimize_memory_usage(self):
        """优化内存使用"""
        # 清理缓存中的过期项
        current_time = time.time()
        expired_keys = []
        
        with self.cache._lock:
            for key, timestamp in self.cache._timestamps.items():
                if current_time - timestamp > self.cache.ttl:
                    expired_keys.append(key)
        
        for key in expired_keys:
            self.cache._remove_key(key)
        
        # 清理对象池
        self.object_pool.clear()
        
        # 执行垃圾回收
        self._perform_gc()
        
        log(_("twice_nat44_optimizer.memory_optimized"), "info",
            expired_cache_items=len(expired_keys))
    
    def reset_stats(self):
        """重置统计信息"""
        with self._lock:
            self.metrics_history.clear()
        
        self.cache.clear()
        self.object_pool.clear()
        
        log(_("twice_nat44_optimizer.stats_reset"), "info")


# 全局性能优化器实例
_twice_nat44_optimizer = None


def get_twice_nat44_optimizer() -> TwiceNat44PerformanceOptimizer:
    """获取全局twice-nat44性能优化器实例"""
    global _twice_nat44_optimizer
    if _twice_nat44_optimizer is None:
        _twice_nat44_optimizer = TwiceNat44PerformanceOptimizer()
    return _twice_nat44_optimizer
