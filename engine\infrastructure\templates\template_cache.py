# -*- coding: utf-8 -*-
"""
XML模板缓存 - 提供模板的内存缓存功能
"""

import time
from typing import Optional, Dict, Any
from lxml import etree
from engine.utils.logger import log
from engine.utils.i18n import _


class TemplateCache:
    """
    XML模板缓存
    提供模板的内存缓存功能，提高模板加载性能
    """
    
    def __init__(self, max_size: int = 50, ttl: int = 3600):
        """
        初始化模板缓存
        
        Args:
            max_size: 最大缓存条目数
            ttl: 缓存生存时间（秒）
        """
        self.max_size = max_size
        self.ttl = ttl
        self._cache = {}
        self._access_times = {}
        self._creation_times = {}
        
        log(_("template_cache.initialized"), "debug", 
            max_size=max_size, ttl=ttl)
    
    def get(self, key: str) -> Optional[etree.Element]:
        """
        从缓存获取模板
        
        Args:
            key: 缓存键
            
        Returns:
            Optional[etree.Element]: 模板根元素，如果不存在或过期则返回None
        """
        if key not in self._cache:
            return None
        
        # 检查是否过期
        if self._is_expired(key):
            self.remove(key)
            return None
        
        # 更新访问时间
        self._access_times[key] = time.time()
        
        log(_("template_cache.cache_hit"), "debug", key=key)
        return self._cache[key]
    
    def set(self, key: str, template: etree.Element):
        """
        将模板存入缓存
        
        Args:
            key: 缓存键
            template: 模板根元素
        """
        # 如果缓存已满，移除最久未访问的条目
        if len(self._cache) >= self.max_size and key not in self._cache:
            self._evict_lru()
        
        # 存储模板的深拷贝
        self._cache[key] = etree.fromstring(etree.tostring(template))
        self._access_times[key] = time.time()
        self._creation_times[key] = time.time()
        
        log(_("template_cache.template_cached"), "debug", key=key)
    
    def remove(self, key: str):
        """
        从缓存移除模板
        
        Args:
            key: 缓存键
        """
        if key in self._cache:
            del self._cache[key]
            del self._access_times[key]
            del self._creation_times[key]
            log(_("template_cache.template_removed"), "debug", key=key)
    
    def clear(self):
        """清空缓存"""
        self._cache.clear()
        self._access_times.clear()
        self._creation_times.clear()
        log(_("template_cache.cache_cleared"), "info")
    
    def _is_expired(self, key: str) -> bool:
        """
        检查缓存条目是否过期
        
        Args:
            key: 缓存键
            
        Returns:
            bool: 是否过期
        """
        if key not in self._creation_times:
            return True
        
        return time.time() - self._creation_times[key] > self.ttl
    
    def _evict_lru(self):
        """移除最久未访问的缓存条目"""
        if not self._access_times:
            return
        
        # 找到最久未访问的键
        lru_key = min(self._access_times.keys(), key=lambda k: self._access_times[k])
        self.remove(lru_key)
        log(_("template_cache.lru_evicted"), "debug", key=lru_key)
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息
        
        Returns: Dict[str, Any]: 缓存统计信息
        """
        return {
            'size': len(self._cache),
            'max_size': self.max_size,
            'ttl': self.ttl,
            'keys': list(self._cache.keys())
        }
