# -*- coding: utf-8 -*-
"""
XML模板加载器 - 负责从文件系统加载XML模板
"""

import os
from typing import Optional
from lxml import etree
from engine.utils.logger import log
from engine.utils.i18n import _
from engine.utils.i18n import _


class TemplateLoader:
    """
    XML模板加载器
    负责从文件系统加载XML模板文件，处理编码和格式问题
    """
    
    def __init__(self, config_manager):
        """
        初始化模板加载器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config_manager = config_manager
    
    def load_template(self, template_path: str) -> etree.Element:
        """
        从文件加载XML模板
        
        Args:
            template_path: 模板文件路径
            
        Returns:
            etree.Element: 模板的根元素
            
        Raises:
            FileNotFoundError: 当模板文件不存在时
            etree.XMLSyntaxError: 当XML格式错误时
        """
        if not os.path.exists(template_path):
            error_msg = _("template_loader.file_not_found", path=template_path)
            log(error_msg, "error")
            raise FileNotFoundError(error_msg)
        
        try:
            log(_("template_loader.loading_template", path=template_path), "debug")

            # 检查文件大小
            file_size = os.path.getsize(template_path)
            log(_("template_loader.file_size", size=file_size), "debug")

            # 使用lxml解析XML文件（增强解析器配置）
            parser = etree.XMLParser(
                strip_cdata=False,
                recover=True,
                remove_blank_text=False,
                resolve_entities=False
            )

            tree = etree.parse(template_path, parser)
            root = tree.getroot()

            # 详细的加载结果日志
            root_tag = root.tag
            local_tag = root_tag.split('}')[-1] if '}' in root_tag else root_tag
            namespace = root.get("xmlns")
            child_count = len(list(root))

            log(_("template_loader.load_success", path=template_path), "info")
            log(_("template_loader.root_element", full=root_tag, local=local_tag), "debug")
            log(_("template_loader.namespace", namespace=namespace), "debug")
            log(_("template_loader.child_count", count=child_count), "debug")

            return root
            
        except etree.XMLSyntaxError as e:
            error_msg = _("template_loader.xml_syntax_error", path=template_path, error=str(e))
            log(error_msg, "error")
            raise
        except Exception as e:
            error_msg = _("template_loader.unexpected_error", path=template_path, error=str(e))
            log(error_msg, "error")
            raise
    
    def validate_template_file(self, template_path: str) -> bool:
        """
        验证模板文件是否可以正常加载
        
        Args:
            template_path: 模板文件路径
            
        Returns:
            bool: 文件是否有效
        """
        try:
            self.load_template(template_path)
            return True
        except Exception as e:
            log(_("template_loader.validation_failed"), "warning", 
                path=template_path, error=str(e))
            return False
