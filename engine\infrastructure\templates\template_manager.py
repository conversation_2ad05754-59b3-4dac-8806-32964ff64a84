# -*- coding: utf-8 -*-
"""
XML模板管理器 - 统一管理XML模板的加载、缓存和验证
保持与现有模板处理逻辑的完全兼容性
"""

import os
from typing import Optional, Dict, Any
from lxml import etree
from engine.utils.logger import log
from engine.utils.i18n import _
from engine.infrastructure.common.caching import CacheManager
from engine.infrastructure.common.performance import PerformanceMonitor
from .template_loader import TemplateLoader
from .template_cache import TemplateCache


class TemplateManager:
    """
    XML模板管理器
    负责统一管理XML模板的加载、缓存、验证和更新
    与现有的模板处理逻辑保持完全兼容
    """

    def __init__(self, config_manager):
        """
        初始化模板管理器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config_manager = config_manager
        self.template_loader = TemplateLoader(config_manager)
        self.template_cache = TemplateCache()
        self._initialized = False

        # Initialize advanced caching and performance monitoring
        self.cache_manager = CacheManager()
        self.performance_monitor = PerformanceMonitor()
        self.advanced_cache = self.cache_manager.create_cache(
            "template_advanced", max_size=50, default_ttl=3600)

        log(_("template_manager.initialized"), "info")
    
    def get_template(self, model: str, version: str, template_type: str = "running") -> etree.Element:
        """
        获取XML模板根元素 - 保持与现有逻辑兼容
        
        Args:
            model: 设备型号，如 'z5100s'
            version: 设备版本，如 'R10P2'
            template_type: 模板类型，默认为 'running'
            
        Returns:
            etree.Element: 模板的根元素
            
        Raises:
            FileNotFoundError: 当模板文件不存在时
            etree.XMLSyntaxError: 当模板XML格式错误时
        """
        cache_key = f"{model}_{version}_{template_type}"
        
        # 尝试从缓存获取
        cached_template = self.template_cache.get(cache_key)
        if cached_template is not None:
            log(_("template_manager.cache_hit"), "debug", key=cache_key)
            # 返回深拷贝以避免修改缓存的模板，保持命名空间信息
            import copy
            return copy.deepcopy(cached_template)
        
        # 从文件加载模板（增强日志）
        try:
            template_path = self.config_manager.get_template_path(model, version, template_type)
            log(f"Template path obtained: {template_path}", "debug")

            template_root = self.template_loader.load_template(template_path)
            log(f"Template loaded successfully: root='{template_root.tag}', children={len(list(template_root))}", "debug")

             # 验证模板结构
            validation_result = self._validate_template_structure(template_root, model, version)
            log(f"Template structure validation result: {validation_result}", "debug")

        except Exception as e:
            log(f"Template loading failed: model={model}, version={version}, type={template_type}, error={str(e)}", "error")
            raise
        
        # 缓存模板
        self.template_cache.set(cache_key, template_root)

        log(_("template_manager.template_loaded_new"), "info",
            model=model, version=version, type=template_type)
        
         # 返回深拷贝，保持命名空间信息
        import copy
        return copy.deepcopy(template_root)
    
    def get_template_path(self, model: str, version: str, template_type: str = "running") -> str:
        """
        获取模板文件路径 - 委托给配置管理器
        
        Args:
            model: 设备型号
            version: 设备版本  
            template_type: 模板类型
            
        Returns:
            str: 模板文件路径
        """
        return self.config_manager.get_template_path(model, version, template_type)

    def validate_template(self, template_path: str) -> bool:
        """
        验证模板文件的有效性
        
        Args:
            template_path: 模板文件路径
            
        Returns:
            bool: 模板是否有效
        """
        try:
            template_root = self.template_loader.load_template(template_path)
            return self._validate_template_structure(template_root)
        except Exception as e:
            log(_("template_manager.validation_failed_new"), "error",
                path=template_path, error=str(e))
            return False
    
    def _validate_template_structure(self, template_root: etree.Element,
                                   model: str = None, version: str = None) -> bool:
        """
        验证模板结构的有效性（修复版本 - 借鉴旧架构逻辑）

        Args:
            template_root: 模板根元素
            model: 设备型号（可选）
            version: 设备版本（可选）

        Returns:
            bool: 结构是否有效
        """
        if template_root is None:
            log("Template root element is empty", "error")
            return False

        # 获取根元素标签（处理命名空间）
        root_tag = template_root.tag
        local_tag = root_tag.split('}')[-1] if '}' in root_tag else root_tag

        log(f"Template root element validation: full_tag='{root_tag}', local_tag='{local_tag}'", "debug")

        # 检查根元素（更宽容的验证逻辑）
        if local_tag != "config":
            log(f"Template root element is not 'config': actual='{local_tag}', expected='config'", "warning")
            # 不返回False，只记录警告（借鉴旧架构的宽容策略）
        else:
            log(f"Template root element validation passed: '{local_tag}'", "debug")

         # 检查命名空间（只验证，不修改 - 借鉴旧架构策略）
        expected_namespace = "urn:ruijie:ntos"
        root_namespace = template_root.get("xmlns")

        log(f"Namespace validation: actual='{root_namespace}', expected='{expected_namespace}'", "debug")

        if root_namespace != expected_namespace:
            log(_("template_manager.namespace_mismatch_new", actual=root_namespace, expected=expected_namespace), "warning")
           # 不修改模板文件的命名空间（借鉴旧架构 - 尊重模板原始结构）
            log(_("template_manager.namespace_preserved_new"), "info")
        else:
            log("Namespace validation passed", "debug")

        # 检查基本结构元素（更详细的日志，考虑命名空间）
        vrf_element = template_root.find(".//vrf")
        if vrf_element is None:
            # 尝试使用默认命名空间查找
            vrf_element = template_root.find(".//{urn:ruijie:ntos}vrf")
            if vrf_element is None:
                log("Warning: VRF element not found in template (tried both no namespace and default namespace)", "warning")
            else:
                log("Found VRF element (using default namespace), template structure is basically correct", "debug")
        else:
            log("Found VRF element (no namespace), template structure is basically correct", "debug")

        # 添加更多结构检查（借鉴旧架构）
        config_elements = len(list(template_root))
        log(f"Template root element contains {config_elements} child elements", "debug")

        if config_elements == 0:
            log("Warning: Template root element has no child elements", "warning")

        log("Template structure validation completed", "debug")
        return True  # 总是返回True，采用宽容策略
    
    def reload_template(self, model: str, version: str, template_type: str = "running"):
        """
        重新加载指定模板
        
        Args:
            model: 设备型号
            version: 设备版本
            template_type: 模板类型
        """
        cache_key = f"{model}_{version}_{template_type}"
        self.template_cache.remove(cache_key)
        log(f"Template reloaded: {cache_key}", "info")

    def clear_cache(self):
        """清空模板缓存"""
        self.template_cache.clear()
        log("Template cache cleared", "info")

    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return self.template_cache.get_stats()
