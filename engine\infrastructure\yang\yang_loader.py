# -*- coding: utf-8 -*-
"""
YANG模型加载器 - 负责加载和解析YANG模型文件
保持与现有YANG处理逻辑的完全兼容性
"""

import os
import re
from typing import Dict, List, Any, Optional
from engine.utils.logger import log
from engine.utils.i18n import _


class YangLoader:
    """
    YANG模型加载器
    负责从文件系统加载YANG模型文件，提取命名空间和架构信息
    与现有的YangModelParser保持兼容
    """
    
    def __init__(self, config_manager):
        """
        初始化YANG加载器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config_manager = config_manager
    
    def load_yang_schema(self, yang_dir: str) -> Dict[str, Any]:
        """
        加载YANG模型架构信息 - 基于现有YangModelParser的逻辑
        
        Args:
            yang_dir: YANG模型目录路径
            
        Returns: Dict[str, Any]: YANG模型架构信息
        """
        if not os.path.exists(yang_dir):
            raise FileNotFoundError(_("yang_loader.dir_not_found", dir=yang_dir))
        
        # 获取所有YANG文件
        yang_files = [f for f in os.listdir(yang_dir) if f.endswith('.yang')]
        if not yang_files:
            log(_("yang_loader.no_yang_files"), "warning", dir=yang_dir)
            return {}
        
        log(_("yang_loader.found_yang_files"), "info", 
            dir=yang_dir, count=len(yang_files))
        
        schema = {
            'namespaces': {},
            'modules': {},
            'nodes': {},
            'typedefs': {}
        }
        
        # 首先解析命名空间和模块信息
        for yang_file in yang_files:
            file_path = os.path.join(yang_dir, yang_file)
            module_info = self._parse_module_header(file_path)
            if module_info:
                schema['modules'][module_info['name']] = module_info
                if module_info.get('namespace'):
                    schema['namespaces'][module_info['name']] = module_info['namespace']
        
        # 然后解析节点结构
        for yang_file in yang_files:
            file_path = os.path.join(yang_dir, yang_file)
            nodes = self._parse_module_structure(file_path)
            if nodes:
                module_name = os.path.splitext(yang_file)[0]
                schema['nodes'][module_name] = nodes
        
        log(_("yang_loader.schema_loaded"), "info",
            modules=len(schema['modules']),
            namespaces=len(schema['namespaces']))
        
        return schema
    
    def extract_namespaces(self, yang_dir: str) -> Dict[str, str]:
        """
        提取YANG模型的命名空间映射
        
        Args:
            yang_dir: YANG模型目录路径
            
        Returns: Dict[str, str]: 命名空间映射 {模块名: 命名空间URI}
        """
        namespaces = {}
        
        if not os.path.exists(yang_dir):
            log(_("yang_loader.dir_not_found"), "error", dir=yang_dir)
            return namespaces
        
        yang_files = [f for f in os.listdir(yang_dir) if f.endswith('.yang')]
        
        for yang_file in yang_files:
            file_path = os.path.join(yang_dir, yang_file)
            module_info = self._parse_module_header(file_path)
            if module_info and module_info.get('namespace'):
                namespaces[module_info['name']] = module_info['namespace']
        
        return namespaces
    
    def _parse_module_header(self, file_path: str) -> Optional[Dict[str, str]]:
        """
        解析YANG模块头部信息 - 基于现有YangModelParser的逻辑
        
        Args:
            file_path: YANG文件路径
            
        Returns:
            Optional[Dict[str, str]]: 模块信息，包含name, namespace, prefix等
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            module_info = {}
            
            # 提取模块名
            module_match = re.search(r'module\s+([^\s{]+)', content)
            if module_match:
                module_info['name'] = module_match.group(1)
            else:
                return None
            
            # 提取命名空间
            namespace_match = re.search(r'namespace\s+"([^"]+)"', content)
            if namespace_match:
                module_info['namespace'] = namespace_match.group(1)
            
            # 提取前缀
            prefix_match = re.search(r'prefix\s+([^\s;]+)', content)
            if prefix_match:
                module_info['prefix'] = prefix_match.group(1)
            
            # 提取版本信息
            revision_match = re.search(r'revision\s+"([^"]+)"', content)
            if revision_match:
                module_info['revision'] = revision_match.group(1)
            
            return module_info

        except Exception as e:
            log(_("yang_loader.parse_header_error"), "warning",
                file=file_path, error=str(e))
            return None

    def _parse_module_structure(self, file_path: str) -> Optional[Dict[str, Any]]:
        """
        解析YANG模块结构信息 - 基于现有YangModelParser的逻辑

        Args:
            file_path: YANG文件路径

        Returns:
            Optional[Dict[str, Any]]: 节点结构信息
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            nodes = {}

            # 简化的节点解析 - 提取容器和叶子节点
            container_matches = re.finditer(r'container\s+([^\s{]+)', content)
            for match in container_matches:
                container_name = match.group(1)
                nodes[container_name] = {'type': 'container'}

            leaf_matches = re.finditer(r'leaf\s+([^\s{]+)', content)
            for match in leaf_matches:
                leaf_name = match.group(1)
                nodes[leaf_name] = {'type': 'leaf'}

            list_matches = re.finditer(r'list\s+([^\s{]+)', content)
            for match in list_matches:
                list_name = match.group(1)
                nodes[list_name] = {'type': 'list'}

            return nodes if nodes else None

        except Exception as e:
            log(_("yang_loader.parse_structure_error"), "warning",
                file=file_path, error=str(e))
            return None
