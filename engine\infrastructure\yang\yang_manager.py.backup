# YANGLINT_DISABLED_FOR_TESTING
# -*- coding: utf-8 -*-
"""
YANG模型管理器 - 统一管理YANG模型的加载、缓存和验证
保持与现有YANG处理逻辑的完全兼容性
"""

import os
from typing import Dict, Any, Optional, Tuple
from engine.utils.logger import log
from engine.utils.i18n import _
from .yang_loader import <PERSON><PERSON>oader
from .yang_validator import YangValidator


class YangManager:
    """
    YANG模型管理器
    负责统一管理YANG模型的加载、缓存、验证
    与现有的YANG处理逻辑保持完全兼容
    """

    def __init__(self, config_manager):
        """
        初始化YANG管理器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config_manager = config_manager
        self.yang_loader = YangLoader(config_manager)
        self.yang_validator = YangValidator(config_manager)
        self._schema_cache = {}
        self._namespace_cache = {}

        log(_("yang_manager.initialized"), "info")
    
    def get_yang_model_dir(self, model: str, version: str) -> str:
        """
        获取YANG模型目录路径 - 委托给配置管理器
        
        Args:
            model: 设备型号，如 'z5100s'
            version: 设备版本，如 'R10P2'
            
        Returns:
            str: YANG模型目录路径
            
        Raises:
            FileNotFoundError: 当YANG模型目录不存在时
        """
        return self.config_manager.get_yang_model_dir(model, version)

    def get_yang_schema(self, model: str, version: str) -> Optional[Dict[str, Any]]:
        """
        获取YANG模型架构信息
        
        Args:
            model: 设备型号
            version: 设备版本
            
        Returns:
            Optional[Dict[str, Any]]: YANG模型架构信息，如果加载失败则返回None
        """
        cache_key = f"{model}_{version}"
        
        # 尝试从缓存获取
        if cache_key in self._schema_cache:
            log(_("yang_manager.schema_cache_hit"), "debug", key=cache_key)
            return self._schema_cache[cache_key]

        try:
            # 获取YANG模型目录
            yang_dir = self.get_yang_model_dir(model, version)
            
             # 加载YANG模型架构
            schema = self.yang_loader.load_yang_schema(yang_dir)
            
            # 缓存架构信息
            self._schema_cache[cache_key] = schema

            log(_("yang_manager.schema_loaded"), "info",
                model=model, version=version)

            return schema

        except Exception as e:
            log(_("yang_manager.schema_load_failed"), "error",
                model=model, version=version, error=str(e))
            return None
    
    def get_namespace_mappings(self, model: str, version: str) -> Dict[str, str]:
        """
        获取YANG模型的命名空间映射
        
        Args:
            model: 设备型号
            version: 设备版本
            
        Returns: Dict[str, str]: 命名空间映射字典
        """
        cache_key = f"{model}_{version}_ns"
        
        # 尝试从缓存获取
        if cache_key in self._namespace_cache:
            return self._namespace_cache[cache_key]

        try:
            yang_dir = self.get_yang_model_dir(model, version)
            namespaces = self.yang_loader.extract_namespaces(yang_dir)
            
           # 缓存命名空间映射
            self._namespace_cache[cache_key] = namespaces

            log(_("yang_manager.namespaces_loaded"), "info",
                model=model, version=version, count=len(namespaces))

            return namespaces

        except Exception as e:
            log(_("yang_manager.namespaces_load_failed"), "error",
                model=model, version=version, error=str(e))
            return {}

    def validate_xml_against_yang(self, xml_file: str, model: str, version: str) -> Tuple[bool, str]:
        """
        使用YANG模型验证XML配置 - 保持与现有验证逻辑兼容
        
        Args:
            xml_file: XML文件路径
            model: 设备型号
            version: 设备版本
            
        Returns: Tuple[bool, str]: (验证是否通过, 验证消息)
        """
        try:
            yang_dir = self.get_yang_model_dir(model, version)
            return self.yang_validator.validate_xml(xml_file, yang_dir, model, version)

        except Exception as e:
            error_msg = f"YANG验证错误: {str(e)}"
            log(error_msg, "error")
            return False, error_msg

    def is_yanglint_available(self) -> bool:
        """
        检查yanglint工具是否可用 - 委托给验证器
        
        Returns:
            bool: yanglint是否可用
        """
        return self.yang_validator.is_yanglint_available()

    def clear_cache(self):
        """清空所有缓存"""
        self._schema_cache.clear()
        self._namespace_cache.clear()
        log(_("yang_manager.cache_cleared"), "info")

    def get_cache_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息
        
        Returns: Dict[str, Any]: 缓存统计信息
        """
        return {
            'schema_cache_size': len(self._schema_cache),
            'namespace_cache_size': len(self._namespace_cache),
            'schema_keys': list(self._schema_cache.keys()),
            'namespace_keys': list(self._namespace_cache.keys())
        }
