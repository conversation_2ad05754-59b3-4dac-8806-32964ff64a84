# -*- coding: utf-8 -*-
"""
YANG验证器 - 使用yanglint工具验证XML配置
保持与现有YANG验证逻辑的完全兼容性
"""

import os
import subprocess
import glob
from typing import Tuple
from engine.utils.logger import log
from engine.utils.i18n import _


class YangValidator:
    """
    YANG验证器
    使用yanglint工具验证XML配置是否符合YANG模型规范
    与现有的yang_validator.py保持完全兼容
    """
    
    def __init__(self, config_manager):
        """
        初始化YANG验证器

        Args:
            config_manager: 配置管理器实例
        """
        self.config_manager = config_manager
        self._log_directory = None
    
    def validate_xml(self, xml_file: str, yang_dir: str, model: str, version: str) -> Tuple[bool, str]:
        """
        验证XML文件是否符合YANG模型规范 - 基于现有validate_gracefully逻辑
        
        Args:
            xml_file: XML文件路径
            yang_dir: YANG模型目录路径
            model: 设备型号
            version: 设备版本
            
        Returns: Tuple[bool, str]: (验证是否通过, 验证消息)
        """
        # 检查yanglint是否可用
        if not self.is_yanglint_available():
            error_msg = _("error.yanglint_tool_not_available")
            log(error_msg, "error")
            return False, error_msg
        
        # 检查XML文件是否存在
        if not os.path.exists(xml_file):
            error_msg = _("error.xml_file_not_found", file=xml_file)
            log(error_msg, "error")
            return False, error_msg
        
        # 检查YANG模型目录是否存在
        if not os.path.exists(yang_dir):
            error_msg = _("error.yang_model_dir_not_exists", dir=yang_dir)
            log(error_msg, "error")
            return False, error_msg
        
        # 检查是否存在YANG模型文件
        yang_files = [f for f in os.listdir(yang_dir) if f.endswith(('.yang', '.yin'))]
        if not yang_files:
            error_msg = _("error.no_yang_files_found_cannot_validate")
            log(error_msg, "error")
            return False, error_msg
        
        # 执行yanglint验证 - 使用现有的validate_with_yanglint逻辑
        return self._validate_with_yanglint(xml_file, yang_dir, model, version)
    
    def _validate_with_yanglint(self, xml_file: str, yang_model_dir: str, 
                               model: str, version: str) -> Tuple[bool, str]:
        """
        使用yanglint工具验证XML - 基于现有validate_with_yanglint函数的逻辑
        
        Args:
            xml_file: 要验证的XML文件路径
            yang_model_dir: YANG模型目录路径
            model: 设备型号
            version: 设备版本
            
        Returns: Tuple[bool, str]: (验证是否通过, 验证消息)
        """
        try:
            log(_("validator.found_yang_files"), "info", 
                dir=yang_model_dir, count=len(os.listdir(yang_model_dir)))
            
            # 使用shell命令的方式验证XML - 保持与现有逻辑一致
            shell_command = f"yanglint -p {yang_model_dir} -i {os.path.join(yang_model_dir, 'ntos*.yang')} {xml_file}"
            log(_("validator.execute_yanglint_command", cmd=shell_command), "info")
            
            try:
                # 在Linux/Unix系统上使用shell=True可以保持通配符不被展开
                if os.name != 'nt':  # 非Windows系统
                    result = subprocess.run(shell_command, shell=True, capture_output=True, 
                                          text=True, check=True)
                    log(_("validator.validation_passed"), "info")
                    return True, _("validator.validation_passed")
                else:
                    # Windows系统不支持通配符，需要列出所有文件
                    # 获取所有ntos*.yang文件
                    ntos_files = glob.glob(os.path.join(yang_model_dir, "ntos*.yang"))
                    if not ntos_files:
                        log(_("warning.no_ntos_yang_files"), "warning")
                    
                    # 构建Windows兼容的命令
                    cmd_parts = ["yanglint", "-p", yang_model_dir]
                    for ntos_file in ntos_files:
                        cmd_parts.extend(["-i", ntos_file])
                    cmd_parts.append(xml_file)
                    
                    cmd_str = " ".join(cmd_parts)
                    log(_("validator.execute_windows_command", cmd=cmd_str), "info")
                    
                    result = subprocess.run(cmd_str, shell=True, capture_output=True, 
                                          text=True, check=True)
                    log(_("validator.validation_passed"), "info")
                    return True, _("validator.validation_passed")
                    
            except subprocess.CalledProcessError as e:
                error_message = e.stderr if e.stderr else e.stdout

                # 保存详细的YANG验证错误信息
                self._save_yang_validation_error(xml_file, error_message, model, version)

                error_msg = _("error.yanglint_validation_failed", message=error_message)
                log(error_msg, "error")
                return False, _("error.validation_failed", message=error_message)
            except FileNotFoundError:
                log(_("error.yanglint_not_found"), "error")
                return False, _("error.yanglint_not_available")
                
        except Exception as e:
            error_msg = _("error.yanglint_execution_error", error=str(e))
            log(error_msg, "error")
            return False, _("error.validation_process_error", error=str(e))
    
    def is_yanglint_available(self) -> bool:
        """
        检查yanglint工具是否可用 - 改进版本，解决环境变量和路径问题

        Returns:
            bool: yanglint是否可用
        """
        # 尝试多种方法检测yanglint
        detection_methods = [
            self._check_yanglint_direct,
            self._check_yanglint_with_path,
            self._check_yanglint_absolute_path,
            self._check_yanglint_with_env
        ]

        for method in detection_methods:
            try:
                if method():
                    return True
            except Exception as e:
                log(_("validator.yanglint_check_method_failed",
                      method=method.__name__, error=str(e)), "debug")
                continue

        # 所有方法都失败了
        log(_("validator.yanglint_not_available"), "warning")
        return False

    def _check_yanglint_direct(self) -> bool:
        """直接检查yanglint命令"""
        try:
            result = subprocess.run(['yanglint', '--version'],
                                  capture_output=True, text=True, check=True, timeout=10)
            version = result.stdout.strip()
            log(_("validator.yanglint_available", version=version), "debug")
            return True
        except (subprocess.CalledProcessError, FileNotFoundError, subprocess.TimeoutExpired):
            return False

    def _check_yanglint_with_path(self) -> bool:
        """使用shutil.which查找yanglint路径"""
        import shutil
        yanglint_path = shutil.which('yanglint')
        if not yanglint_path:
            return False

        try:
            result = subprocess.run([yanglint_path, '--version'],
                                  capture_output=True, text=True, check=True, timeout=10)
            version = result.stdout.strip()
            log(_("validator.yanglint_available", version=version), "debug")
            return True
        except (subprocess.CalledProcessError, FileNotFoundError, subprocess.TimeoutExpired):
            return False

    def _check_yanglint_absolute_path(self) -> bool:
        """检查常见的yanglint安装路径"""
        common_paths = [
            '/usr/local/bin/yanglint',
            '/usr/bin/yanglint',
            '/opt/venv/bin/yanglint',
            '/usr/sbin/yanglint',
            '/usr/local/sbin/yanglint'
        ]

        for path in common_paths:
            if os.path.exists(path) and os.access(path, os.X_OK):
                try:
                    result = subprocess.run([path, '--version'],
                                          capture_output=True, text=True, check=True, timeout=10)
                    version = result.stdout.strip()
                    log(_("validator.yanglint_available", version=version), "debug")
                    return True
                except (subprocess.CalledProcessError, subprocess.TimeoutExpired):
                    continue
        return False

    def _check_yanglint_with_env(self) -> bool:
        """使用完整环境变量检查yanglint"""
        try:
            # 构建完整的环境变量
            env = os.environ.copy()

            # 确保PATH包含常见的bin目录
            path_additions = ['/usr/local/bin', '/usr/bin', '/opt/venv/bin']
            current_path = env.get('PATH', '')

            for path_add in path_additions:
                if path_add not in current_path:
                    current_path = f"{path_add}:{current_path}"

            env['PATH'] = current_path

            # 设置LD_LIBRARY_PATH
            ld_path_additions = ['/usr/local/lib', '/usr/lib']
            current_ld_path = env.get('LD_LIBRARY_PATH', '')

            for ld_add in ld_path_additions:
                if ld_add not in current_ld_path:
                    current_ld_path = f"{ld_add}:{current_ld_path}" if current_ld_path else ld_add

            env['LD_LIBRARY_PATH'] = current_ld_path

            result = subprocess.run(['yanglint', '--version'],
                                  capture_output=True, text=True, check=True,
                                  timeout=10, env=env)
            version = result.stdout.strip()
            log(_("validator.yanglint_available", version=version), "debug")
            return True
        except (subprocess.CalledProcessError, FileNotFoundError, subprocess.TimeoutExpired):
            return False

    def _save_yang_validation_error(self, xml_file: str, error_message: str,
                                   model: str, version: str):
        """
        保存YANG验证错误信息到文件，用于分析错误原因

        Args:
            xml_file: 验证失败的XML文件路径
            error_message: yanglint的错误输出
            model: 设备型号
            version: 设备版本
        """
        try:
            import datetime
            import os

            # 获取统一的日志目录
            log_dir = self._get_log_directory()

            # 在日志目录下创建YANG验证错误子目录
            error_dir = os.path.join(log_dir, "yang_validation_errors")
            os.makedirs(error_dir, exist_ok=True)

            # 生成错误日志文件名
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            xml_basename = os.path.basename(xml_file).replace('.xml', '')
            error_file = os.path.join(error_dir, f"yang_error_{xml_basename}_{timestamp}.log")

            # 保存详细错误信息
            with open(error_file, 'w', encoding='utf-8') as f:
                f.write("=" * 80 + "\n")
                f.write("YANG验证错误详细信息\n")
                f.write("=" * 80 + "\n")
                f.write(f"时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"XML文件: {xml_file}\n")
                f.write(f"设备型号: {model}\n")
                f.write(f"设备版本: {version}\n")
                f.write("-" * 80 + "\n")
                f.write("yanglint错误输出:\n")
                f.write("-" * 80 + "\n")
                f.write(error_message)
                f.write("\n" + "=" * 80 + "\n")

            info_msg = _("validator.yang_error_saved", file=error_file)
            log(info_msg, "info")

        except Exception as e:
            warning_msg = _("validator.yang_error_save_failed", error=str(e))
            log(warning_msg, "warning")

    def _get_log_directory(self) -> str:
        """
        获取统一的日志目录路径

        Returns:
            str: 日志目录路径
        """
        if self._log_directory is not None:
            return self._log_directory

        # 尝试从多个来源获取日志目录
        log_dir = None

        # 1. 从环境变量获取
        log_dir = os.environ.get('LOG_DIR')

        # 2. 从配置管理器获取
        if not log_dir and self.config_manager:
            log_dir = self.config_manager.get_config('log_dir')

        # 3. 从全局日志系统获取
        if not log_dir:
            try:
                from engine.utils.logger import get_current_log_dir
                log_dir = get_current_log_dir()
            except (ImportError, AttributeError):
                pass

        # 4. 使用默认日志目录
        if not log_dir:
            # 获取引擎根目录
            engine_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            log_dir = os.path.join(engine_dir, 'logs')

        # 确保日志目录存在
        os.makedirs(log_dir, exist_ok=True)

        # 缓存结果
        self._log_directory = log_dir
        return log_dir

    def set_log_directory(self, log_dir: str):
        """
        设置日志目录路径

        Args:
            log_dir: 日志目录路径
        """
        self._log_directory = log_dir
        os.makedirs(log_dir, exist_ok=True)
