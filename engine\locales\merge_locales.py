#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
语言资源文件合并工具
用于确保所有语言文件包含相同的键
"""

import json
import os
import sys
from typing import Dict, Set

# 添加调试信息
print("脚本开始执行")
print(f"当前工作目录: {os.getcwd()}")

# 定义语言文件目录和文件
LOCALE_DIR = os.path.dirname(os.path.abspath(__file__))
print(f"语言文件目录: {LOCALE_DIR}")

LOCALE_FILES = {
    "zh-CN": os.path.join(LOCALE_DIR, "zh-CN.json"),
    "en-US": os.path.join(LOCALE_DIR, "en-US.json")
}

# 检查主语言文件是否存在
for lang, file_path in LOCALE_FILES.items():
    if os.path.exists(file_path):
        print(f"语言文件存在: {file_path}")
    else:
        print(f"语言文件不存在: {file_path}")

# 定义需要合并的额外文件
ADDITIONAL_FILES = [
    os.path.join(LOCALE_DIR, "new_keys.json"),
    os.path.join(LOCALE_DIR, "additional_keys.json"),
    os.path.join(LOCALE_DIR, "en-US-new.json"),
    os.path.join(LOCALE_DIR, "additional_keys_en.json")
]

# 检查额外文件是否存在
for file_path in ADDITIONAL_FILES:
    if os.path.exists(file_path):
        print(f"额外文件存在: {file_path}")
    else:
        print(f"额外文件不存在: {file_path}")

def load_json_file(file_path: str) -> Dict:
    """加载JSON文件"""
    try:
        if not os.path.exists(file_path):
            print(f"文件不存在: {file_path}")
            return {}
            
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"加载文件成功: {file_path}, 包含 {len(data)} 个键")
        return data
    except Exception as e:
        print(f"加载文件失败: {file_path}, 错误: {str(e)}")
        return {}

def save_json_file(file_path: str, data: Dict) -> bool:
    """保存JSON文件"""
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"保存文件成功: {file_path}")
        return True
    except Exception as e:
        print(f"保存文件失败: {file_path}, 错误: {str(e)}")
        return False

def get_all_keys(locale_files: Dict[str, str], additional_files: list) -> Set[str]:
    """获取所有语言文件中的所有键"""
    all_keys = set()
    
    # 从主语言文件获取键
    for lang, file_path in locale_files.items():
        if os.path.exists(file_path):
            data = load_json_file(file_path)
            keys = set(data.keys())
            all_keys.update(keys)
            print(f"从 {lang} 文件中获取了 {len(keys)} 个键")
    
    # 从额外文件获取键
    for file_path in additional_files:
        if os.path.exists(file_path):
            data = load_json_file(file_path)
            keys = set(data.keys())
            all_keys.update(keys)
            print(f"从 {os.path.basename(file_path)} 中获取了 {len(keys)} 个键")
    
    return all_keys

def merge_locales():
    """合并所有语言文件，确保所有文件包含相同的键"""
    print("开始合并语言文件...")
    
    # 加载所有语言文件
    locale_data = {}
    for lang, file_path in LOCALE_FILES.items():
        if os.path.exists(file_path):
            locale_data[lang] = load_json_file(file_path)
        else:
            locale_data[lang] = {}
            print(f"创建新的语言文件: {file_path}")

    # 加载额外的键文件
    additional_data = {}
    for file_path in ADDITIONAL_FILES:
        if os.path.exists(file_path):
            file_data = load_json_file(file_path)
            if file_data:
                # 根据文件名判断语言
                if "en" in os.path.basename(file_path).lower():
                    lang = "en-US"
                else:
                    lang = "zh-CN"
                
                if lang not in additional_data:
                    additional_data[lang] = {}
                
                # 合并到对应语言的额外数据中
                additional_data[lang].update(file_data)
                print(f"将 {os.path.basename(file_path)} 中的 {len(file_data)} 个键添加到 {lang} 额外数据中")

    # 获取所有键
    all_keys = get_all_keys(LOCALE_FILES, ADDITIONAL_FILES)
    print(f"所有文件共有 {len(all_keys)} 个不同的键")

    # 将额外数据合并到主语言文件
    for lang, data in additional_data.items():
        if lang in locale_data:
            print(f"合并额外键到 {lang}, 共 {len(data)} 个键")
            locale_data[lang].update(data)

    # 检查每个文件中缺失的键
    missing_keys = {}
    for lang, data in locale_data.items():
        keys = set(data.keys())
        missing = all_keys - keys
        if missing:
            missing_keys[lang] = missing
            print(f"{lang} 缺少 {len(missing)} 个键")
            if len(missing) <= 20:
                print(f"  缺少的键: {sorted(list(missing))}")

    # 填充缺失的键
    for lang, missing in missing_keys.items():
        data = locale_data[lang]
        
        # 对于中文文件，从英文文件中复制翻译
        if lang.startswith("zh"):
            for key in missing:
                # 从en-US获取英文
                if key in locale_data.get("en-US", {}):
                    en_value = locale_data["en-US"][key]
                    data[key] = f"[需翻译] {en_value}"
                    print(f"  添加键到 {lang}: {key} = [需翻译] {en_value}")
        
        # 对于英文文件，从中文文件中复制翻译
        else:
            for key in missing:
                # 从zh-CN获取中文
                if key in locale_data.get("zh-CN", {}):
                    zh_value = locale_data["zh-CN"][key]
                    data[key] = f"[Needs Translation] {zh_value}"
                    print(f"  添加键到 {lang}: {key} = [Needs Translation] {zh_value}")

    # 保存更新后的文件
    for lang, data in locale_data.items():
        save_json_file(LOCALE_FILES[lang], data)

if __name__ == "__main__":
    print("开始合并语言资源文件...")
    merge_locales()
    print("合并完成!") 