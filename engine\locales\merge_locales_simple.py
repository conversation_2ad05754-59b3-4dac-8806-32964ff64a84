#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化的语言资源文件合并工具
只处理中英文翻译文件，确保两个文件包含相同的键
"""

import json
import os
import glob
from typing import Dict, Set

# 定义语言文件目录
LOCALE_DIR = os.path.dirname(os.path.abspath(__file__))

def discover_locale_files():
    """自动发现所有语言文件"""
    json_files = glob.glob(os.path.join(LOCALE_DIR, "*.json"))
    # 排除备份文件
    json_files = [f for f in json_files if not f.endswith('.backup') and not f.endswith('.pre_manual.json')]
    
    locale_files = {}
    for file_path in json_files:
        filename = os.path.basename(file_path)
        if filename.endswith('.json'):
            lang_code = filename[:-5]  # 移除.json后缀
            # 确保是语言代码格式 (包含连字符或下划线)
            if '-' in lang_code or '_' in lang_code or lang_code in ['zh', 'en']:
                locale_files[lang_code] = file_path
    
    return locale_files

def load_json_file(file_path: str) -> Dict:
    """加载JSON文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"加载文件成功: {os.path.basename(file_path)}, 包含 {len(data)} 个键")
        return data
    except Exception as e:
        print(f"加载文件失败: {os.path.basename(file_path)}, 错误: {str(e)}")
        return {}

def save_json_file(file_path: str, data: Dict) -> bool:
    """保存JSON文件"""
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"保存文件成功: {os.path.basename(file_path)}")
        return True
    except Exception as e:
        print(f"保存文件失败: {os.path.basename(file_path)}, 错误: {str(e)}")
        return False

def get_all_keys(locale_files: Dict[str, str]) -> Set[str]:
    """获取所有语言文件中的所有键"""
    all_keys = set()
    
    for lang, file_path in locale_files.items():
        if os.path.exists(file_path):
            data = load_json_file(file_path)
            keys = set(data.keys())
            all_keys.update(keys)
            print(f"从 {lang} 文件中获取了 {len(keys)} 个键")
    
    return all_keys

def merge_locales():
    """合并所有语言文件，确保所有文件包含相同的键"""
    print("=== 语言文件合并工具 ===\n")
    
    # 发现所有语言文件
    locale_files = discover_locale_files()
    print(f"发现的语言文件: {list(locale_files.keys())}\n")
    
    if not locale_files:
        print("未发现任何语言文件！")
        return
    
    # 加载所有语言文件
    locale_data = {}
    for lang, file_path in locale_files.items():
        locale_data[lang] = load_json_file(file_path)
    
    # 获取所有键
    all_keys = get_all_keys(locale_files)
    print(f"\n所有文件共有 {len(all_keys)} 个不同的键")
    
    # 检查每个文件中缺失的键
    missing_keys = {}
    total_missing = 0
    
    print(f"\n=== 键完整性检查 ===")
    for lang, data in locale_data.items():
        keys = set(data.keys())
        missing = all_keys - keys
        if missing:
            missing_keys[lang] = missing
            print(f"{lang}: 缺少 {len(missing)} 个键")
            total_missing += len(missing)
        else:
            print(f"{lang}: ✓ 包含所有键")
    
    if total_missing == 0:
        print(f"\n✓ 所有语言文件都包含完整的键集合！")
        return
    
    print(f"\n=== 开始填充缺失的键 ===")
    
    # 填充缺失的键
    for lang, missing in missing_keys.items():
        data = locale_data[lang]
        print(f"\n为 {lang} 添加 {len(missing)} 个缺失的键:")
        
        # 优先级：中文 -> 英文 -> 占位符
        for key in sorted(missing):
            value_found = False
            
            # 首先尝试从中文获取
            if "zh-CN" in locale_data and key in locale_data["zh-CN"]:
                zh_value = locale_data["zh-CN"][key]
                if lang.startswith("zh"):
                    data[key] = zh_value  # 中文文件直接使用
                else:
                    data[key] = f"[需翻译至{lang}] {zh_value}"
                value_found = True
            
            # 然后尝试从英文获取
            elif "en-US" in locale_data and key in locale_data["en-US"]:
                en_value = locale_data["en-US"][key]
                if lang.startswith("en"):
                    data[key] = en_value  # 英文文件直接使用
                else:
                    data[key] = f"[需翻译至{lang}] {en_value}"
                value_found = True
            
            # 最后使用占位符
            if not value_found:
                data[key] = f"[需要{lang}翻译]"
            
            # 只显示前5个键的详情
            if len([k for k in sorted(missing) if sorted(missing).index(k) < 5]) >= sorted(missing).index(key):
                print(f"  ✓ {key}")
        
        if len(missing) > 5:
            print(f"  ... 还有 {len(missing) - 5} 个键")
    
    # 保存更新后的文件
    print(f"\n=== 保存更新后的文件 ===")
    success_count = 0
    for lang, data in locale_data.items():
        if save_json_file(locale_files[lang], data):
            print(f"  ✓ {lang}: {len(data)} 个键")
            success_count += 1
        else:
            print(f"  ✗ {lang}: 保存失败")
    
    # 最终验证
    print(f"\n=== 最终验证 ===")
    for lang, file_path in locale_files.items():
        data = load_json_file(file_path)
        print(f"  {lang}: {len(data)} 个键")
    
    print(f"\n✓ 合并完成！成功更新了 {success_count} 个语言文件。")

if __name__ == "__main__":
    merge_locales()
