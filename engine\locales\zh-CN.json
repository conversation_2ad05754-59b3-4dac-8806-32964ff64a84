{"Advanced Covid AI Ready": "Advanced Covid AI Ready", "Cython.Compiler.Main": "Cython: Compiler: Main", "Found 10,000,000,000 copies of Covid32.exe": "Found 10,000,000,000 copies of Covid32: exe", "Hello, World!": "Hello, <PERSON>!", "Importing advanced AI": "Importing advanced AI", "Mapping interface names...": "Mapping interface names: : : ", "Updating interface nodes in running XML...": "Updating interface nodes in running XML: : : ", "[on blue]hello": "[on blue]hello", "_CallbackFileWrapper__fp": " CallbackFileWrapper  fp", "_compress.so": "compress.so", "_distutils_hack": " distutils hack", "_report.json": "report.json", "_temp_for_encryption.xml": "temp_for_encryption.xml", "about_service": "关于服务", "address.processor_description": "address: processor description", "address.unnamed": "未命名地址对象", "address_group.unnamed": "address group: unnamed", "address_group_processing_stage.completed": "地址对象组处理完成，总计: {total}，转换: {converted}，跳过: {skipped}", "address_group_processing_stage.description": "地址对象组处理阶段", "address_group_processing_stage.empty_group": "地址对象组为空", "address_group_processing_stage.group_member_not_found": "地址对象组 {group} 的成员 {member} 未找到", "address_group_processing_stage.group_processing_error": "地址对象组处理错误: {error}", "address_group_processing_stage.group_skipped": "地址对象组 {group} 已跳过: {reason}", "address_group_processing_stage.no_address_groups": "没有地址对象组需要处理", "address_group_processing_stage.no_address_result": "缺少地址对象处理结果", "address_group_processing_stage.no_valid_members": "地址对象组没有有效成员", "address_group_processing_stage.processing_failed": "地址对象组处理失败", "address_group_processing_stage.starting": "开始处理地址对象组", "address_group_processor.conversion_success": "地址对象组转换成功: {group} ({valid}/{total} 成员有效)", "address_group_processor.data_conversion_complete": "地址对象组数据转换完成，原始类型: {original_type}, 转换后: {converted_type}, 组数量: {count}", "address_group_processor.detailed_error_info": "详细错误信息: {error}", "address_group_processor.group_no_members_yang_violation": "警告：地址组 {group} 没有成员，但这违反了YANG约束", "address_group_processor.group_xml_generated": "生成地址对象组XML: {group} (包含 {count} 个成员)", "address_group_processor.member_not_found": "地址组成员不存在于已转换地址对象中: {member}", "address_group_processor.member_validation_success": "地址组成员验证成功: {member}", "address_group_processor.no_converted_addresses": "没有已转换的地址对象，跳过地址对象组处理", "address_group_processor.no_converted_groups": "没有转换成功的地址对象组，返回空XML片段", "address_group_processor.processing_complete": "地址对象组处理完成: 转换成功 {success}/{total}", "address_group_processor.skip_empty_group": "跳过空地址组: {group}", "address_group_processor.skip_no_valid_members": "跳过无有效成员的地址组: {group}", "address_group_processor.xml_fragment_failed": "生成地址对象组XML片段失败: {error}", "address_group_processor.xml_fragment_success": "地址对象组XML片段生成成功，包含 {count} 个地址组", "address_processing.conversion_failed": "[待翻译] address_processing.conversion_failed", "address_processing_stage.address_failed": "地址对象 {name} 处理失败: {reason}", "address_processing_stage.address_missing_subnet_using_default": "地址对象 {name} 缺少subnet字段，使用默认值", "address_processing_stage.address_skipped": "跳过地址对象 {name}: {reason}", "address_processing_stage.completed": "地址对象处理完成，转换: {converted}，总计: {total}", "address_processing_stage.conversion_failed": "地址对象转换失败", "address_processing_stage.description": "地址对象处理阶段", "address_processing_stage.empty_group": "地址组为空", "address_processing_stage.failed": "地址对象处理失败", "address_processing_stage.group_member_not_found": "地址组 {group} 的成员 {member} 未找到", "address_processing_stage.group_processing_error": "地址组处理错误: {error}", "address_processing_stage.group_skipped": "跳过地址组 {name}: {reason}", "address_processing_stage.interface_association_not_supported": "不支持带接口关联的地址对象", "address_processing_stage.item_type": "地址对象", "address_processing_stage.legacy_processing_completed": "旧架构地址处理完成，转换: {converted}，跳过: {skipped}，失败: {failed}", "address_processing_stage.legacy_processing_failed": "旧架构地址处理失败: {error}，回退到新架构处理", "address_processing_stage.legacy_processor_not_available": "旧架构地址处理器不可用，使用新架构处理", "address_processing_stage.no_address_objects": "没有地址对象需要处理", "address_processing_stage.no_valid_members": "地址组没有有效成员", "address_processing_stage.processing_error": "地址对象处理错误: {error}", "address_processing_stage.starting": "开始处理地址对象", "address_processing_stage.subnet_conversion_failed": "地址对象 {name} 的subnet转换失败: {subnet}, 错误: {error}", "address_processing_stage.unsupported_type": "不支持的地址类型: {type}", "address_processor.conversion_success": "地址对象转换成功: {name}", "address_processor.found_objects": "找到 {count} 个地址对象", "address_processor.invalid_cidr_format": "地址对象 {name} 的CIDR格式无效: {subnet}", "address_processor.invalid_ip_address": "地址对象 {name} 的IP地址无效: {ip}", "address_processor.invalid_ip_range": "地址对象 {name} 的IP范围无效: {start_ip}-{end_ip}", "address_processor.invalid_netmask": "地址对象 {name} 的子网掩码无效: {mask}", "address_processor.invalid_prefix_format": "地址对象 {name} 的前缀格式无效: {prefix}", "address_processor.invalid_prefix_length": "地址对象 {name} 的前缀长度无效: {prefix}", "address_processor.invalid_subnet_format": "地址对象 {name} 的subnet格式无效: {subnet}", "address_processor.iprange.generated_description": "从FortiGate服务对象 {source_service} 的iprange字段转换", "address_processor.iprange.generation_failed": "从iprange生成地址对象失败: {error}", "address_processor.iprange.generation_success": "成功生成地址对象: {name} ({range}) 来源服务: {source_service}", "address_processor.iprange.invalid_range": "无效的IP范围 {iprange}: {error}", "address_processor.missing_ip_range_info": "地址对象 {name} 缺少IP范围信息", "address_processor.missing_subnet_info": "地址对象 {name} 缺少subnet信息", "address_processor.no_converted_objects": "没有转换成功的地址对象，返回空XML片段", "address_processor.processing_complete": "地址对象处理完成: 转换成功 {success}/{total}", "address_processor.skip_interface_associated": "跳过接口关联地址对象: {name}", "address_processor.skip_not_explicitly_supported": "跳过未明确支持的地址类型: {name} ({type})", "address_processor.skip_unsupported_type": "跳过不支持的地址类型: {name} ({type})", "address_processor.validation.invalid_format_string": "IP范围格式无效: 必须是字符串", "address_processor.xml_fragment_failed": "生成地址对象XML片段失败: {error}", "address_processor.xml_fragment_success": "地址对象XML片段生成成功，包含 {count} 个地址对象", "address_processor.xml_generated": "生成地址对象XML: {name} ({type}: {ip_address})", "api_documentation": "API文档", "app.description": "将不同厂商的网络设备配置转换为标准格式", "app.title": "配置转换器", "app.version": "版本", "app_name": "配置转换服务", "bridge_generator.initialized": "桥接生成器已初始化", "cache_manager.all_caches_cleared": "缓存管理器：所有缓存已清除", "cache_manager.cache_created": "缓存管理器：缓存已创建", "cache_manager.cache_deleted": "缓存管理器：缓存已删除", "cache_manager.cache_exists": "缓存管理器：缓存已存在", "cache_manager.initialized": "缓存管理器已初始化", "category.address_issues": "地址对象引用问题", "category.dns_issues": "DNS配置问题", "category.interface_issues": "接口映射问题", "category.other_issues": "其他问题", "category.policy_issues": "策略配置问题", "category.service_issues": "服务对象引用问题", "certifi": "certifi", "common.no_details": "无详情", "common.no_impact": "无影响", "common.no_suggestion": "无建议", "common.unknown": "未知", "common.unknown_reason": "未知原因", "compatibility.address": "地址对象", "compatibility.address_limit": "地址对象数量超过限制", "compatibility.address_limit_detail": "地址对象数量 {count} 超过了最大限制 20000", "compatibility.address_suggestion": "请减少地址对象数量或拆分配置", "compatibility.interface": "接口", "compatibility.interface_compat": "接口兼容性", "compatibility.interface_limit": "接口数量超过限制", "compatibility.interface_limit_detail": "接口数量 {count} 超过了最大限制 1024", "compatibility.interface_suggestion": "请减少接口数量或拆分配置", "compatibility.invalid_interfaces_desc": "有 {count} 个接口因配置无效而被跳过", "compatibility.invalid_interfaces_suggestion": "请查看详细日志，根据兼容性问题修改接口配置", "compatibility.issue": "兼容性问题", "compatibility.policy": "策略规则", "compatibility.policy_limit": "策略规则数量超过限制", "compatibility.policy_limit_detail": "策略规则数量 {count} 超过了最大限制 10000", "compatibility.policy_suggestion": "请减少策略规则数量或拆分配置", "compatibility.severity_warning": "警告", "compatibility.unmapped_zone_interfaces_desc": "有 {count} 个区域接口因未找到映射而被跳过", "compatibility.unmapped_zone_interfaces_suggestion": "请检查区域接口名称是否正确，或添加相应的接口映射", "compatibility.zone_interface_mapping": "区域接口映射", "compress.all_attempts_failed": "所有加载尝试都失败了", "compress.all_libs_copied_to_temp": "所有库文件已复制到临时目录", "compress.available_symbols": "可用符号: {symbols}", "compress.backed_up_existing_file": "已备份现有文件: {file}", "compress.backup_startup_created": "成功创建backup-startup.tar.gz文件: {file}", "compress.cannot_delete_file": "无法删除 {path}: {error}", "compress.cannot_find_main_lib": "无法找到主库文件", "compress.changed_to_lib_dir": "切换到库目录: {dir}", "compress.chdir_for_preload": "为预加载切换到库目录: {dir}", "compress.checking_lib_file": "正在检查库文件: {path}", "compress.cleaned_temp_lib_dir": "已清理临时库目录: {dir}", "compress.cleanup_temp_dir": "已清理临时目录: {dir}", "compress.container_clearing_system_paths": "容器环境清除系统库路径", "compress.container_current_full_path": "容器环境当前完整路径: {path}", "compress.container_dep_not_found_anywhere": "容器加载 - 在系统路径中未找到依赖库: {dep}", "compress.container_dep_symlink_created": "容器加载 - 已创建依赖库符号链接: {dep} ({src} -> {dst})", "compress.container_deps_located": "容器加载 - 已定位依赖库: {found}", "compress.container_detection": "容器环境检测: is_container={is_container}, dockerenv_exists={dockerenv_exists}, container_env={container_env}, docker_container_env={docker_container_env}", "compress.container_direct_main_process_failed": "主进程直接加载失败: {error}", "compress.container_direct_system_exception": "直接系统加载异常: {error}", "compress.container_direct_system_failed": "直接系统加载失败: {error}", "compress.container_direct_system_success": "直接系统加载成功: {lib}", "compress.container_dlopen_error": "容器环境dlopen错误: {error}", "compress.container_dlopen_failed": "容器环境dlopen失败: {error}", "compress.container_enforcing_path_isolation": "容器环境强制路径隔离", "compress.container_force_reload_attempt_failed": "容器加载 - 重载尝试失败: {dep} (模式: {mode}), 错误: {error}", "compress.container_force_reload_deps": "容器加载 - 强制重新加载依赖库", "compress.container_force_reload_exception": "容器加载 - 强制重载异常: {error}", "compress.container_force_reload_failed": "容器加载 - 强制重载失败: {dep}, 错误: {error}", "compress.container_force_reload_success": "容器加载 - 强制重载成功: {dep} (模式: {mode})", "compress.container_isolated_lib_path": "容器环境隔离库路径: {path}", "compress.container_isolated_path_override": "容器环境隔离路径覆盖: {path}", "compress.container_ldconfig_refresh": "容器环境刷新动态链接器缓存", "compress.container_ldconfig_warning": "容器环境ldconfig警告: {error}", "compress.container_ldd_all_deps_found": "ldd确认所有依赖都找到", "compress.container_ldd_exception": "ldd检查异常: {error}", "compress.container_ldd_failed": "ldd检查失败: {error}", "compress.container_ldd_missing_deps": "ldd发现缺失依赖: {output}", "compress.container_ldd_output": "ldd输出: {output}", "compress.container_lib_loaded_via_dlopen": "容器环境通过dlopen成功加载库: {lib}", "compress.container_library_path": "容器环境库路径: {path}", "compress.container_load_absolute_failed": "容器加载 - 绝对路径失败: {error}", "compress.container_load_debug_cwd": "容器加载调试 - 当前目录: {dir}", "compress.container_load_debug_deps": "容器加载调试 - 依赖状态: {deps}", "compress.container_load_debug_env": "容器加载调试 - 环境变量: LD_LIBRARY_PATH={ld_library_path}, LD_PRELOAD={ld_preload}", "compress.container_load_debug_paths": "容器加载调试 - 路径: 相对={relative}, 绝对={absolute}", "compress.container_load_debug_permissions": "容器加载调试 - 权限: {path}, 可读={can_read}, 可执行={can_exec}", "compress.container_load_debug_so_files": "容器加载调试 - .so文件列表: {files}", "compress.container_load_dlopen_exception": "容器加载 - dlopen异常: {error}", "compress.container_load_dlopen_failed": "容器加载 - dlopen失败: {error}", "compress.container_load_failed": "容器环境加载失败: {error}", "compress.container_load_lib_not_found": "容器加载 - 库文件不存在: {path}", "compress.container_load_library_failed": "容器环境加载库失败: {error}", "compress.container_load_permission_issue": "容器加载 - 权限问题: {path}, 可读={can_read}, 可执行={can_exec}", "compress.container_load_strategy_failed": "容器加载 - 策略失败: {strategy}, 错误: {error}", "compress.container_load_success_method1": "容器环境方法1加载成功", "compress.container_load_trying_absolute_path": "容器加载 - 尝试绝对路径", "compress.container_load_trying_dlopen": "容器加载 - 尝试dlopen系统调用", "compress.container_load_trying_strategies": "容器加载 - 尝试 {count} 种策略: {strategies}", "compress.container_load_trying_strategy": "容器加载 - 尝试策略: {strategy}", "compress.container_locate_deps_exception": "容器加载 - 定位依赖库时发生异常: {error}", "compress.container_locating_missing_deps": "容器加载 - 正在定位缺失的依赖库: {deps}", "compress.container_minimal_library_path_set": "容器环境设置最小化库路径: {path}", "compress.container_minimal_library_path_strategy": "容器环境使用最小化库路径策略", "compress.container_minimal_preload": "容器环境最小化预加载: {libs}", "compress.container_no_deps_located": "容器加载 - 未能定位任何缺失的依赖库", "compress.container_original_env": "容器环境原始变量: LD_LIBRARY_PATH={ld_path}, LD_PRELOAD={ld_preload}", "compress.container_rebuilt_preload": "容器环境重建预加载: {libs}", "compress.container_simple_load_all_failed": "容器环境所有加载策略都失败: {lib}", "compress.container_simple_load_exception": "容器环境简化加载异常: {lib}, 错误: {error}", "compress.container_simple_load_failed": "容器环境简化加载失败: {lib}, 错误: {error}", "compress.container_simple_load_success": "容器环境简化加载成功: {lib} (策略: {strategy})", "compress.container_special_handling": "容器环境特殊处理: {method}", "compress.container_strace_success": "strace追踪成功", "compress.container_strace_trace": "strace追踪输出: {trace}", "compress.container_strace_unavailable": "strace不可用: {error}", "compress.container_subprocess_exception": "子进程库加载测试异常: {error}", "compress.container_subprocess_test_failed": "子进程库加载测试失败: stdout={stdout}, stderr={stderr}", "compress.container_subprocess_test_success": "子进程库加载测试成功", "compress.container_subprocess_timeout": "子进程库加载测试超时", "compress.container_symlink_failed": "容器加载 - 创建符号链接失败: {dep}, 错误: {error}", "compress.container_trying_direct_system_loading": "容器环境尝试直接系统加载", "compress.copied_lib_to_temp": "已复制库文件: {src} -> {dst}", "compress.created_system_lib_dir": "已创建系统库目录: {dir}", "compress.created_system_symlink": "已创建系统符号链接: {link}", "compress.created_temp_lib_dir": "已创建临时库目录: {dir}", "compress.critical_lib_missing": "关键库缺失: {lib}", "compress.current_working_dir": "当前工作目录: {dir}", "compress.decrypted_file_saved": "解密后的文件保存在: {file}", "compress.decrypting_file": "正在解密文件: {src} -> {dst}", "compress.decryption_complete": "解密成功完成!", "compress.deleted_existing_output": "已删除已存在的输出文件: {file}", "compress.deleted_file": "已删除: {path}", "compress.dependency_already_in_memory": "依赖库已在内存中: {lib}", "compress.dependency_already_loaded": "依赖库已加载: {lib}", "compress.dependency_loaded_successfully": "依赖库加载成功: {lib}", "compress.detected_container_env": "检测到容器环境", "compress.detected_non_container_env": "检测到非容器环境", "compress.encryption_success": "加密成功，已保存到: {file}", "compress.error.all_load_methods_failed": "所有加载方法都失败了: {error}", "compress.error.call_real_api_failed": "调用真实API失败: {error}", "compress.error.cannot_delete_output": "无法删除已存在的输出文件: {error}", "compress.error.checking_lib_permissions": "检查库权限时出错: {error}", "compress.error.checking_permissions": "检查权限时出错: {error}", "compress.error.cleanup_temp_dir": "清理临时目录失败: {error}", "compress.error.container_detection_failed": "容器环境检测失败: {error}", "compress.error.copy_libs_failed": "复制库文件到临时目录失败: {error}", "compress.error.create_config_tar_gz": "创建Config.tar.gz文件时发生错误: {error}", "compress.error.create_tar_file": "创建tar文件时发生错误: {error}", "compress.error.creating_symlinks": "创建符号链接失败: {error}", "compress.error.creating_system_lib_dir": "创建系统库目录失败: {error}", "compress.error.critical_lib_load_failed": "加载关键依赖库失败: {lib}", "compress.error.critical_lib_missing": "关键依赖库缺失: {lib}", "compress.error.decryption_failed": "解密失败，返回代码: {code}", "compress.error.during_decryption": "解密过程中发生错误: {error}", "compress.error.encrypted_file_invalid": "错误: 加密后的文件无效或不存在", "compress.error.encrypted_file_not_found": "错误: 在压缩包中未找到加密文件", "compress.error.encryption_abort": "错误: 加密过程中断，操作失败", "compress.error.encryption_failed": "加密失败，返回代码: {code}", "compress.error.encryption_process": "加密过程中发生错误: {error}", "compress.error.forced_real_api_in_mock_mode": "错误: 强制使用真实API但处于模拟模式，无法继续", "compress.error.global_cleanup_failed": "全局库文件清理过程中出错: {error}", "compress.error.init_failed": "初始化过程中出错: {error}", "compress.error.input_dir_not_exists": "错误: 输入目录 '{dir}' 不存在", "compress.error.input_file_not_exists": "错误: 输入文件 '{file}' 不存在", "compress.error.lib_not_exists": "错误: 库文件不存在: {path}", "compress.error.lib_not_file": "错误: 指定的库路径不是文件: {path}", "compress.error.list_symbols_failed": "列出符号失败: {error}", "compress.error.listing_lib_dir": "列出库目录内容时出错: {error}", "compress.error.load_lib_failed": "加载库文件失败: {lib}, 错误: {error}", "compress.error.load_lib_mode_failed": "使用模式 {mode} 加载库 {lib} 失败: {error}", "compress.error.load_lib_on_demand_failed": "按需加载库文件失败: {error}", "compress.error.load_main_lib_failed": "加载主库文件失败: {error}", "compress.error.load_main_library_failed": "加载主库失败: {error}", "compress.error.main_lib_not_exists": "主库文件不存在: {path}", "compress.error.missing_critical_libs": "缺少关键库: {libs}", "compress.error.missing_main_function": "缺少主要函数", "compress.error.mock_api_with_force_real": "错误: 模拟API模式下不能执行强制真实API的操作", "compress.error.mock_copy_failed": "模拟API: 复制文件失败 - {error}", "compress.error.mock_encryption_failed": "模拟API: 简单加密失败 - {error}", "compress.error.mock_encryption_operation": "模拟API: 加密操作失败 - {error}", "compress.error.output_file_invalid": "错误: 输出文件无效或不存在", "compress.error.preload_lib_failed": "预加载依赖库失败: {lib}, 错误: {error}", "compress.error.preload_libs_failed": "预加载依赖库过程中出错: {error}", "compress.error.preparing_container_loading": "准备容器加载失败: {error}", "compress.error.preparing_library_files": "准备库文件失败: {error}", "compress.error.process_tar_gz_failed": "处理压缩包时发生错误: {error}", "compress.error.reload_dependencies_failed": "重新加载依赖库过程失败: {error}", "compress.error.reload_dependency_failed": "重新加载依赖库失败: {lib}, 错误: {error}", "compress.error.running_ldconfig": "运行ldconfig失败: {error}", "compress.error.setting_ld_library_path": "设置LD_LIBRARY_PATH失败: {error}", "compress.error.tar_command_failed": "执行tar命令失败: {error}, 错误输出: {stderr}", "compress.error.tar_gz_not_exists": "错误: 文件 '{file}' 不存在", "compress.error.unknown_action": "错误: 未知操作 '{action}'，请使用 'encrypt' 或 'decrypt'", "compress.error.version_file_invalid": "错误: 版本文件无效或不存在", "compress.error.version_file_not_exists": "错误: 版本信息文件 '{file}' 不存在", "compress.error.version_file_not_found": "错误: 在压缩包中未找到版本信息文件", "compress.fallback_to_mock_api": "尝试降级到模拟API...", "compress.fixing_lib_permissions": "正在修复库权限: {path}", "compress.force_load_container_attempt": "尝试容器强制加载", "compress.force_load_success": "容器强制加载成功", "compress.found_dependency": "找到依赖库: {path}", "compress.global_cleanup_complete": "全局库文件清理完成", "compress.init_complete": "初始化完成", "compress.init_env": "初始化环境: 容器={container}, 库目录={lib_dir}", "compress.ld_library_path_set": "已设置LD_LIBRARY_PATH={path}", "compress.ld_preload_set": "已设置LD_PRELOAD={libs}", "compress.ldconfig_executed": "已执行ldconfig", "compress.ldconfig_executed_after_preload": "预加载后已执行ldconfig", "compress.ldconfig_refreshed": "已刷新ldconfig缓存", "compress.lib_basename": "库文件名: {name}", "compress.lib_dir_contents": "库目录内容: {files}", "compress.lib_file_permissions": "库文件权限: {path}, 模式={mode}, 可读={can_read}, 可执行={can_execute}", "compress.lib_loaded_successfully": "库加载成功: {method}", "compress.lib_not_found": "未找到库文件: {lib}", "compress.lib_permissions": "库文件权限: {path}, 可读={can_read}, 可执行={can_execute}", "compress.listing_available_symbols": "正在列出可用符号", "compress.load_lib_on_demand_success": "按需加载库文件成功: {path}", "compress.load_main_lib_success": "成功加载主库文件: {path}", "compress.load_method_failed": "加载方法失败: {method}, 错误: {error}", "compress.load_with_absolute_path_failed": "使用绝对路径加载失败: {error}", "compress.load_with_relative_path_failed": "使用相对路径加载失败: {error}", "compress.loading_main_library": "正在加载主库: {path}, 容器模式={container}", "compress.loading_with_absolute_path": "尝试 {attempt}/3 使用绝对路径加载: {path}", "compress.main_function_verified": "主要函数已验证", "compress.main_lib_loaded_container_method": "使用容器方法成功加载主库", "compress.main_lib_loaded_successfully": "主库加载成功", "compress.method1_failed": "方法1失败: {error}", "compress.mock_api_called": "模拟API: cm_file_encrypt 被调用 ({infile}, {outfile}, {ver_file}, {enflag})", "compress.mock_api_initialized": "模拟API初始化完成", "compress.mock_copy_file": "模拟API: 复制文件 {src} -> {dst} (模拟{operation})", "compress.mock_simple_encryption_applied": "模拟API: 已对文件应用简单加密", "compress.preloaded_lib": "预加载依赖库: {lib}", "compress.preparing_container_loading": "准备容器库加载", "compress.preparing_library_files": "正在准备库文件: {dir}", "compress.ran_ldconfig": "已运行ldconfig", "compress.reloaded_dependency": "已重新加载依赖库: {lib}", "compress.reloading_dependencies": "重新加载关键依赖库", "compress.restore_dir_after_preload": "预加载后恢复目录: {dir}", "compress.restored_original_dir": "已恢复原始目录: {dir}", "compress.retry_load_main_lib": "重试加载主库文件 (尝试 {attempt}/3): {error}", "compress.retry_preload_lib": "重试预加载库文件 {lib} (尝试 {attempt}/3): {error}", "compress.setting_ld_preload": "设置LD_PRELOAD: {libs}", "compress.symlink_already_exists": "符号链接已存在: {link}", "compress.tar_gz_processed": "找到版本文件: {ver_file}, 加密文件: {encrypted_file}", "compress.traditional_load_completely_failed": "传统加载完全失败: {error}", "compress.trying_container_method1_rpath": "尝试容器方法1 (rpath)", "compress.trying_load_method": "尝试加载方法: {method}", "compress.trying_relative_path": "尝试 {attempt}/3 使用相对路径加载: {name}", "compress.trying_to_load_lib": "尝试加载的库文件绝对路径: {path}", "compress.usage.decrypt": "解密: python compress_demo.py decrypt <输入.tar.gz文件> <输出目录>", "compress.usage.decrypt_example": "解密: python compress_demo.py decrypt 'backup-startup.tar.gz' output_dir", "compress.usage.encrypt": "加密: python compress_demo.py encrypt <输入目录> [<输出文件>]", "compress.usage.encrypt_example": "加密: python compress_demo.py encrypt input_dir", "compress.usage.examples": "示例:", "compress.usage.title": "用法:", "compress.using_crypto_dir": "使用加密库目录: {path}", "compress.using_crypto_lib": "使用加密库: {path}", "compress.using_crypto_libs_path": "使用环境变量加密库路径: {path}", "compress.using_lib_dir": "使用库目录: {dir}", "compress.using_original_lib_dir": "使用原始库目录: {dir}", "compress.using_version_file": "使用版本信息文件: {file}", "compress.warning.using_mock_api": "警告: 使用模拟API - 某些功能可能不可用", "config_converter.added_dns_config": "已添加DNS配置到转换器", "config_converter.error.invalid_config": "config converter: error: invalid config", "config_converter.error.model_version_not_set": "config converter: error: model version not set", "config_converter.error.no_config_data": "config converter: error: no config data", "config_converter.error.no_config_provided": "config converter: error: no config provided", "config_converter.error.no_output_path": "config converter: error: no output path", "config_converter.error.save_xml_failed": "保存XML配置文件失败: {error}", "config_converter.load_config_from_dict": "config converter: load config from dict", "config_converter.xml_generation_success": "config converter: xml generation success", "config_manager.config_updated": "配置管理器：配置已更新", "config_manager.container_environment_detected_new": "检测到容器环境", "config_manager.detected_container_environment": "配置管理器：检测到容器环境", "config_manager.detected_dev_environment": "配置管理器：检测到开发环境", "config_manager.detected_prod_environment": "配置管理器：检测到生产环境", "config_manager.initialized": "配置管理器已初始化", "config_manager.template_found": "配置管理器：找到模板", "config_manager.template_found_alternative": "配置管理器：找到备用模板", "config_manager.template_found_new": "找到模板文件: {path}", "config_manager.template_not_found_new": "模板文件不存在: 型号={model}, 版本={version}, 类型={type}", "config_parsing.file_size": "📄 配置文件大小: {size:.1f}MB", "config_parsing.file_size_new": "配置文件大小: {size:.2f}MB", "config_parsing.read_file_failed": "❌ 读取配置文件失败: {error}", "config_parsing.using_stream_read": "🔄 使用流式读取处理大文件", "config_parsing.using_stream_read_new": "使用流式读取大文件", "config_type.address_group": "config type: address group", "config_type.address_object": "config type: address object", "config_type.interface": "接口", "config_type.policy_rule": "config type: policy rule", "config_type.policy_rules": "策略规则", "config_type.service_group": "config type: service group", "config_type.service_object": "config type: service object", "config_type.static_route": "config type: static route", "config_type.static_routes": "静态路由", "config_type.time_range": "时间对象", "config_type.zone_interface": "区域接口", "conversion_from_to": "从 {from_format} 转换到 {to_format}", "conversion_report.json": "[待翻译] conversion_report.json", "conversion_service.conversion_completed": "转换已完成", "conversion_service.initialized": "转换服务已初始化", "conversion_service.mapping_file_not_found": "转换服务：映射文件未找到", "conversion_service.prepared_conversion_params": "转换服务：转换参数已准备", "conversion_service.start_conversion": "开始转换", "conversion_service.template_found": "模板已找到", "conversion_service.using_interface_mapping_file": "转换服务：使用接口映射文件", "conversion_service.yang_models_found": "YANG模型已找到", "conversion_service.yang_models_not_found": "转换服务：YANG模型未找到", "conversion_service.yang_validation_completed": "转换服务：YANG验证完成", "conversion_service.yang_validation_completed_successfully": "YANG验证已完成（通过）", "conversion_service.yang_validation_completed_with_errors": "YANG验证已完成（有错误）", "conversion_service.yang_validation_debug_info": "YANG验证调试信息: 键={yang_validation_keys}, 数据={yang_validation_data}", "conversion_service.yang_validation_error": "转换服务：YANG验证错误", "conversion_service.yang_validation_not_configured": "YANG验证未配置", "conversion_service.yang_validation_skipped": "YANG验证已跳过（原因: {reason}）", "conversion_service.yang_validation_skipped_already_performed": "YANG验证已跳过（已执行过）", "conversion_service.yang_validation_skipped_not_available": "YANG验证已跳过（yanglint不可用）", "conversion_service.yang_validation_status_unclear": "YANG验证状态不明确，可用键: {available_keys}", "conversion_service.yang_validation_status_unknown": "YANG验证状态未知", "conversion_strategy.initialized": "转换策略已初始化", "conversion_strategy.invalid_xml_format": "conversion strategy: invalid xml format", "conversion_strategy.no_xml_content": "conversion strategy: no xml content", "conversion_strategy.template_path_failed": "conversion strategy: template path failed", "conversion_strategy.yang_schema_failed": "conversion strategy: yang schema failed", "conversion_successful": "配置转换成功", "conversion_workflow.attempting_xml_save": "正在尝试将XML保存到文件", "conversion_workflow.completed_successfully": "转换工作流程已成功完成", "conversion_workflow.config_parsing_failed": "转换工作流程：配置文件解析失败", "conversion_workflow.detailed_exception_info": "详细异常信息: {error_details}", "conversion_workflow.encryption_not_requested": "未请求加密处理", "conversion_workflow.fallback_to_legacy": "转换工作流程：回退到传统架构", "conversion_workflow.fallback_to_legacy_conversion": "回退到传统转换逻辑", "conversion_workflow.file_write_diagnosis": "文件写入诊断 - 问题: {issues}, 建议: {recommendations}", "conversion_workflow.file_write_diagnosis_failed": "文件写入诊断失败: {file}, 问题: {issues}", "conversion_workflow.file_write_recommendations": "文件写入建议: {recommendations}", "conversion_workflow.fortigate_pipeline_failed": "转换工作流程：Fortigate管道处理失败", "conversion_workflow.generate_user_log_report_failed": "生成用户日志报告失败: {error}", "conversion_workflow.generate_user_suggestions_failed": "生成用户建议失败: {error}", "conversion_workflow.independent_encryption_completed": "独立加密处理完成", "conversion_workflow.independent_encryption_error": "独立加密处理执行错误: {error}", "conversion_workflow.independent_encryption_failed": "独立加密处理失败", "conversion_workflow.independent_encryption_start": "开始独立加密处理", "conversion_workflow.independent_yang_validation_completed": "独立YANG验证完成", "conversion_workflow.independent_yang_validation_error": "独立YANG验证执行错误: {error}", "conversion_workflow.independent_yang_validation_failed": "独立YANG验证失败", "conversion_workflow.independent_yang_validation_failed_details": "独立YANG验证失败: {message}", "conversion_workflow.independent_yang_validation_passed": "独立YANG验证通过", "conversion_workflow.independent_yang_validation_skipped": "独立YANG验证跳过: {reason}", "conversion_workflow.independent_yang_validation_start": "开始独立YANG验证", "conversion_workflow.initialized": "转换工作流程已初始化", "conversion_workflow.interface_mapping_validation_passed": "接口映射验证通过", "conversion_workflow.legacy_architecture_success": "传统架构转换成功", "conversion_workflow.loaded_interface_mapping": "加载的接口映射: {interface_mapping}", "conversion_workflow.memory_threshold_exceeded": "转换工作流程：内存阈值超限", "conversion_workflow.namespace_fix_applied": "命名空间修复已应用", "conversion_workflow.output_write_failed": "转换工作流程：输出文件写入失败", "conversion_workflow.output_write_successful": "输出文件写入成功: {file}, 大小: {size} 字节", "conversion_workflow.pipeline_conversion_failed": "转换工作流程：管道转换失败", "conversion_workflow.pipeline_conversion_successful": "转换工作流程：管道转换成功", "conversion_workflow.pipeline_result_analysis": "转换工作流程：管道结果分析", "conversion_workflow.post_pipeline_processing_failed": "管道后处理失败: {error}", "conversion_workflow.post_processing_failed": "转换工作流程：后处理失败", "conversion_workflow.report_generation_failed": "转换工作流程：报告生成失败", "conversion_workflow.set_current_output_file_path": "设置当前输出文件路径: {output_file}", "conversion_workflow.template_preparation_complete": "模板准备完成: {template_path}", "conversion_workflow.template_preparation_failed": "转换工作流程：模板准备失败", "conversion_workflow.template_prepared": "转换工作流程：模板已准备就绪", "conversion_workflow.using_new_architecture": "使用新架构进行转换", "conversion_workflow.using_temp_output": "使用临时输出路径: {original} -> {temp}", "conversion_workflow.yang_model_preparation_complete": "YANG模型准备完成: {yang_dir}", "conversion_workflow.yang_model_preparation_failed": "YANG模型准备失败: {error}", "conversion_workflow.yang_preparation_failed": "转换工作流程：YANG模型准备失败", "conversion_workflow.yang_prepared": "转换工作流程：YANG模型已准备就绪", "conversion_workflow.yang_validation_completed": "转换工作流程：YANG验证已完成", "conversion_workflow.yang_validation_debug_info": "YANG验证调试信息: XML文件={xml_file}, 模型={model}, 版本={version}", "conversion_workflow.yang_validation_failed": "转换工作流程：YANG验证失败", "conversion_workflow.yang_validation_failed_error": "转换工作流程：YANG验证失败错误", "conversion_workflow.yang_validation_failed_skip_encryption": "YANG验证失败，跳过加密处理", "conversion_workflow.yang_validation_not_performed": "未执行YANG验证", "conversion_workflow.yang_validation_result_debug": "YANG验证结果调试: performed={performed}, passed={passed}, skipped={skipped}, keys={available_keys}", "conversion_workflow.yang_validation_skipped": "YANG验证已跳过: {reason}", "convert_button": "转换", "ctypes.wintypes": "ctypes: wintypes", "data_context.address_object_added": "添加动态生成的地址对象: {name}", "data_context.error_added": "数据上下文：错误已添加", "data_context.error_added_with_stage": "数据上下文添加错误: {error_message} (阶段: {stage})", "data_context.service_iprange_conversion_recorded": "记录服务iprange转换: {service_name} -> {address_name}", "data_context.state_changed": "数据上下文：状态已更改", "data_context.state_changed_with_details": "数据上下文状态变更: {old_state} -> {new_state}", "data_context.warning_added": "数据上下文：警告已添加", "debug.added_service_group_member": "已添加服务组成员: {member}", "debug.added_valid_service_object": "添加有效服务对象: {name}", "debug.adding_service_objects_to_ntos_generator": "添加服务对象到NTOSGenerator，共 {count} 个", "debug.address_object_detail": "地址对象 #{index}: {name} (类型: {type})", "debug.checking_service_groups_data": "检查服务组数据", "debug.complex_tcp_ports_using_destination": "复杂TCP端口 '{original}'，使用目标端口：{destination}", "debug.complex_udp_ports_using_destination": "复杂UDP端口 '{original}'，使用目标端口：{destination}", "debug.detected_full_port_range": "检测到全端口范围：{port_range}，标准化为 1-65535", "debug.final_service_set_in_xml": "最终XML中的服务集合 #{index}: {name}", "debug.final_service_sets_count": "最终XML中的服务集合数量: {count}", "debug.fixed_legacy_generator_added_icmp": "FixedLegacyGenerator服务对象 {name} 添加了ICMP类型和代码: type={icmp_type}, code={icmp_code}", "debug.fixed_legacy_generator_added_ip_protocol": "FixedLegacyGenerator服务对象 {name} 添加了IP协议号: {protocol_number}", "debug.fixed_legacy_generator_added_tcp_ports": "FixedLegacyGenerator服务对象 {name} 添加了TCP端口: {ports}", "debug.fixed_legacy_generator_added_udp_ports": "FixedLegacyGenerator服务对象 {name} 添加了UDP端口: {ports}", "debug.fixed_legacy_generator_final_xml_service_sets_count": "FixedLegacyGenerator最终XML中的服务集合数量: {count}", "debug.fixed_legacy_generator_keeping_service_obj_node": "服务对象节点包含 {count} 个服务集合，保留节点", "debug.fixed_legacy_generator_processing_service_objects": "FixedLegacyGenerator处理服务对象，共 {count} 个", "debug.fixed_legacy_generator_service_added_success": "FixedLegacyGenerator服务对象 {name} 添加成功", "debug.fixed_legacy_generator_service_missing_ports": "FixedLegacyGenerator服务对象 {name} 缺少端口配置，协议类型为 {protocol}", "debug.fixed_legacy_generator_service_object_detail": "FixedLegacyGenerator服务对象 #{index}: 名称={name}, 类型={type}, 协议={protocol}", "debug.fixed_legacy_generator_service_port_config": "FixedLegacyGenerator服务对象 {name} 的端口配置: TCP={has_tcp}, UDP={has_udp}", "debug.fixed_legacy_generator_service_processing_failed": "FixedLegacyGenerator服务对象 {name} 处理失败，错误: {error}", "debug.fixed_legacy_generator_service_protocol": "FixedLegacyGenerator服务对象 {name} 的协议类型: {protocol}", "debug.fixed_legacy_generator_start_processing_service": "FixedLegacyGenerator开始处理服务对象: {name}", "debug.fixed_legacy_generator_xml_service_set": "FixedLegacyGenerator XML中的服务集合 #{index}: {name}", "debug.fixed_legacy_generator_xml_service_sets_count": "FixedLegacyGenerator XML中的服务集合数量: {count}", "debug.fortigate_av_profile_detected": "检测到Fortigate 反病毒配置文件: 策略={policy}, 配置文件={profile}", "debug.fortigate_ips_sensor_detected": "检测到Fortigate IPS传感器配置: 策略={policy}, 传感器={sensor}", "debug.improved_legacy_generator_add_unique_service": "添加唯一服务对象: {name}", "debug.improved_legacy_generator_added_description": "ImprovedLegacyGenerator服务对象 {name} 添加了描述: {description}", "debug.improved_legacy_generator_added_icmp": "ImprovedLegacyGenerator服务对象 {name} 添加了ICMP类型和代码: type={icmp_type}, code={icmp_code}", "debug.improved_legacy_generator_added_ip_protocol": "ImprovedLegacyGenerator服务对象 {name} 添加了IP协议号: {protocol_number}", "debug.improved_legacy_generator_complex_port_range_conversion": "FortiGate端口范围转换(复杂格式): '{input}' -> {output}", "debug.improved_legacy_generator_dedup_result": "去重后的服务对象: {unique}/{total}", "debug.improved_legacy_generator_default_protocol": "ImprovedLegacyGenerator服务对象 {name} 没有端口配置，设置默认协议类型为: {protocol}", "debug.improved_legacy_generator_default_protocol_from_all": "改进版传统生成器：从'all'协议设置默认协议", "debug.improved_legacy_generator_existing_services": "XML中已存在 {count} 个服务对象", "debug.improved_legacy_generator_final_xml_service_sets_count": "ImprovedLegacyGenerator最终XML中的服务集合数量: {count}", "debug.improved_legacy_generator_inferred_protocol": "ImprovedLegacyGenerator服务对象 {name} 自动推断协议类型为: {protocol}", "debug.improved_legacy_generator_inferred_protocol_from_all": "改进版传统生成器：从'all'协议推断协议类型", "debug.improved_legacy_generator_keeping_service_obj_node": "服务对象节点包含 {count} 个服务集合，保留节点", "debug.improved_legacy_generator_port_config": "ImprovedLegacyGenerator服务对象 {name} 的端口配置: tcp-portrange={tcp_portrange}, udp-portrange={udp_portrange}, tcp_ports={tcp_ports}, udp_ports={udp_ports}, port={port}", "debug.improved_legacy_generator_port_range_conversion": "FortiGate端口范围转换: '{input}' -> {output}", "debug.improved_legacy_generator_port_range_normalization": "端口范围转换: '{input}' -> '{output}'", "debug.improved_legacy_generator_service_exists": "服务对象 {name} 已存在，更新配置", "debug.improved_legacy_generator_service_processed_success": "ImprovedLegacyGenerator服务对象 {name} 处理成功", "debug.improved_legacy_generator_start_processing_service": "ImprovedLegacyGenerator开始处理服务对象: {name}, 内容: {service}", "debug.improved_legacy_generator_tcp_ports": "ImprovedLegacyGenerator服务对象 {name} 添加了TCP端口: {ports}", "debug.improved_legacy_generator_tcp_ports_fortigate": "ImprovedLegacyGenerator服务对象 {name} 添加了TCP端口(FortiGate格式): {ports}", "debug.improved_legacy_generator_tcp_ports_generic": "ImprovedLegacyGenerator服务对象 {name} 使用通用端口添加TCP端口: {ports}", "debug.improved_legacy_generator_udp_ports": "ImprovedLegacyGenerator服务对象 {name} 添加了UDP端口: {ports}", "debug.improved_legacy_generator_udp_ports_fortigate": "ImprovedLegacyGenerator服务对象 {name} 添加了UDP端口(FortiGate格式): {ports}", "debug.improved_legacy_generator_udp_ports_generic": "ImprovedLegacyGenerator服务对象 {name} 使用通用端口添加UDP端口: {ports}", "debug.improved_legacy_generator_xml_service_set": "ImprovedLegacyGenerator XML中的服务集合 #{index}: {name}", "debug.improved_legacy_generator_xml_service_sets_count": "ImprovedLegacyGenerator XML中的服务集合数量: {count}", "debug.ippools_data_retrieved": "从config_data获取到的ippools数据: {count} 个池", "debug.ippools_keys_list": "ippools键列表: {keys}", "debug.legacy_generator_processing_service_objects": "LegacyGenerator处理服务对象，共 {count} 个", "debug.legacy_generator_service_object_check": "LegacyGenerator中的服务对象 #{index}: 名称={name}, 类型={type}", "debug.legacy_generator_service_object_detail": "LegacyGenerator服务对象 #{index}: 名称={name}, 类型={type}, 协议={protocol}", "debug.legacy_generator_service_object_details": "服务对象 #{index}: 名称={name}, 类型={type}", "debug.legacy_generator_service_objects_count": "LegacyGenerator中的服务对象数量: {count}", "debug.legacy_generator_set_service_mappings": "传统生成器服务映射已设置", "debug.legacy_policy_rule_converted_successfully": "策略规则 {name} 转换成功，动作: {action}", "debug.loaded_fortigate_service_mapping": "已加载飞塔服务映射，共 {count} 个", "debug.loaded_ntos_builtin_services": "已加载NTOS内置服务数据，共 {count} 个", "debug.loaded_predefined_service_mapping": "已加载预定义服务映射，共 {mappings} 个，别名 {aliases} 个", "debug.multiple_tcp_ports_using_first": "多TCP端口 '{original}'，使用第一个：{selected}", "debug.multiple_udp_ports_using_first": "多UDP端口 '{original}'，使用第一个：{selected}", "debug.nat_pool_created": "创建NAT池 '{pool_name}' 地址范围 {start_ip}-{end_ip}", "debug.nat_pools_generated": "成功生成 {count} 个NAT池配置", "debug.ntos_generator_set_service_mappings": "NTOS生成器服务映射已设置", "debug.policy_service_mapping_applied": "策略规则 '{rule}' 应用服务映射: {original} -> {mapped}", "debug.port_preserve_detected": "检测到端口保持配置: 策略={policy}, 配置={port_preserve}", "debug.predefined_service_mapping_found": "找到预定义服务映射: {service} -> {ntos_service}", "debug.preparing_service_stats_calculation": "准备计算服务对象统计: service_result keys={keys}", "debug.processing_address_objects_count": "处理地址对象，共 {count} 个", "debug.processing_ippools": "开始处理 {count} 个地址池配置", "debug.processing_service_group_member": "处理服务组成员: {member}", "debug.processing_service_objects_count": "处理服务对象，共 {count} 个", "debug.removed_type_tag": "从时间对象 {name} 中移除了无效的type标签", "debug.schedule_no_time_fields": "时间表无时间字段: {name}", "debug.schedule_processing": "调试: schedule processing", "debug.service_auto_infer_protocol": "服务对象 {name} 自动推断协议类型为: {protocol}", "debug.service_converted_count": "service_result['converted']长度: {count}", "debug.service_failed_count": "service_result['failed']长度: {count}", "debug.service_group_member_found_directly": "直接找到服务组成员", "debug.service_group_member_found_via_mapping": "通过映射找到服务组成员", "debug.service_group_processor_received_mappings": "服务组处理器接收到映射: {count}", "debug.service_groups_data_length": "服务组数据长度: {length}", "debug.service_groups_data_sample": "服务组数据示例: {sample}", "debug.service_mapping_relationship": "服务映射关系: {original} -> {mapped}", "debug.service_mapping_relationships_found": "发现服务映射关系: {count}", "debug.service_mapping_relationships_saved": "已保存服务映射关系，共 {count} 个映射", "debug.service_object_details": "服务对象 #{index}: 名称={name}, 类型={type}", "debug.service_objects_processing_details": "服务对象处理结果详情: converted={converted}, failed={failed}, skipped={skipped}", "debug.service_objects_stats_calculated": "服务对象统计计算完成: 总计={total}, 成功={success}, 失败={failed}, 跳过={skipped}", "debug.service_protocol_auto_corrected_tcp": "服务对象 {name} 有TCP端口但协议类型为 {original}，自动修正为 tcp", "debug.service_protocol_auto_corrected_tcp_udp": "服务对象 {name} 同时有TCP和UDP端口但协议类型为 {original}，自动修正为 tcp_udp", "debug.service_protocol_auto_corrected_udp": "服务对象 {name} 有UDP端口但协议类型为 {original}，自动修正为 udp", "debug.service_protocol_correction": "服务对象 {name} 协议类型修正: {old_protocol} -> {new_protocol} (原因: {reason})", "debug.service_set_in_xml": "XML中的服务集合 #{index}: {name}", "debug.service_sets_count_after_adding": "添加服务对象后，XML中的服务集合数量: {count}", "debug.service_skipped_count": "service_result['skipped']长度: {count}", "debug.set_translator": "设置翻译器: {translator}", "debug.setting_service_objects_to_legacy_generator": "设置服务对象到LegacyGenerator，共 {count} 个", "debug.start_adding_service_objects_to_xml": "开始添加服务对象到XML，共 {count} 个", "debug.time_range_adapted": "时间对象 {name} 适配后: 有time_start={has_time_start}, 有time_end={has_time_end}, 有weekdays={has_weekdays}, 有datetime_start={has_datetime_start}, 有datetime_end={has_datetime_end}", "debug.time_range_check_details": "时间对象 {name} 检查: has_time={has_time}, has_weekdays={has_weekdays}, time_start={time_start}, time_end={time_end}, weekdays={weekdays}", "debug.using_converted_service_objects_for_validation": "使用转换后的服务对象进行验证", "debug.using_service_mapping_for_policies": "使用服务映射关系处理策略规则，共 {count} 个映射", "debug.valid_service_group_member": "有效的服务组成员: {member}", "debug.valid_service_objects_count": "有效服务对象数量: {valid}/{total}", "description.config_verification_tool": "配置文件验证工具", "description.interface_extraction_tool": "接口提取工具", "description.interfaces_skipped_due_to_invalid_config": "有 {count} 个接口因配置无效而被跳过", "description.ntos_converter": "NTOS配置转换工具", "description.zone_interfaces_skipped_due_to_missing_mapping": "由于缺少接口映射，{count} 个区域接口被跳过", "detail.interface": "接口", "detail.name": "名称", "detail.reason": "原因", "detail.status": "状态", "detail.zone": "区域", "device.z3200s.description": "Z3200s设备支持的接口列表", "device.z5100s.description": "Z5100s设备支持的接口列表", "dns_generator.added_server": "添加DNS服务器: {address}", "dns_generator.creating_new_dns": "创建新的DNS配置", "dns_generator.dns_created": "DNS配置创建完成", "dns_generator.dns_updated": "DNS配置更新完成", "dns_generator.error.integration_failed": "DNS配置集成失败: {error}", "dns_generator.error.vrf_not_found": "未找到VRF节点", "dns_generator.init_success": "DNS XML生成器初始化成功", "dns_generator.proxy_disabled": "DNS代理已禁用", "dns_generator.proxy_enabled": "DNS代理已启用", "dns_generator.proxy_kept_default": "DNS代理保持模板默认值", "dns_generator.proxy_not_found": "dns generator: proxy not found", "dns_generator.updating_existing_dns": "更新现有DNS配置", "dns_generator.validation.empty_config": "DNS配置为空", "dns_generator.validation.invalid_dns_over_tls": "无效的DNS over TLS值: {value}", "dns_generator.validation.invalid_ip": "无效的IP地址 {field}: {ip}", "dns_generator.validation.no_servers": "未配置DNS服务器", "dns_generator.warning.empty_config": "DNS配置为空", "dns_generator.warning.servers_exceed_limit": "DNS服务器数量({count})超过限制({limit}个)", "dns_generator.warning.vrf_not_found_using_root": "未找到VRF节点，使用根节点", "dns_generator.xml_generated": "DNS XML生成完成，服务器数量: {servers}", "dns_processing_stage.completed": "DNS处理完成，主DNS: {primary_dns}，服务器总数: {total_servers}", "dns_processing_stage.description": "DNS处理阶段", "dns_processing_stage.dns_configuration_processed": "DNS配置处理完成，配置服务器: {configured}，有效服务器: {valid}，主DNS: {primary}", "dns_processing_stage.dns_servers_limited": "DNS服务器数量限制，原始: {original}，限制后: {limited}", "dns_processing_stage.no_config_data": "没有配置数据", "dns_processing_stage.processing_failed": "DNS处理失败: {error}", "dns_processing_stage.starting": "开始处理DNS配置", "dns_processing_stage.using_default_dns": "使用默认DNS服务器", "dns_processor.added_dns_server": "添加DNS服务器: seq={seq}, addr={addr}", "dns_processor.dns_client_config": "DNS客户端配置: IPv4域名 {ipv4} 个，IPv6域名 {ipv6} 个", "dns_processor.dns_config_processing_complete": "DNS配置处理完成: DNS服务器 {servers} 个，静态域名 {hosts} 个", "dns_processor.dns_processing_complete": "DNS服务器处理完成: 配置 {configured} 个，有效 {valid} 个", "dns_processor.dns_processing_stage_complete": "DNS处理完成: DNS服务器 {servers} 个，静态域名 {hosts} 个", "dns_processor.dns_proxy_config": "DNS代理配置: {status}", "dns_processor.dns_servers_limit_exceeded": "DNS服务器数量超过限制，从 {original} 个限制为 {limit} 个", "dns_processor.dns_xml_generation_failed": "生成DNS配置XML片段失败: {error}", "dns_processor.found_dns_config": "找到DNS配置信息", "dns_processor.found_system_settings": "找到系统设置配置", "dns_processor.generate_dns_xml_fragment": "生成DNS配置XML片段", "dns_processor.no_dns_servers_configured": "没有配置DNS服务器", "dns_processor.no_dns_servers_use_default": "没有配置DNS服务器，使用默认DNS服务器", "dns_processor.no_system_settings_found": "没有找到系统设置配置", "dns_processor.no_system_settings_use_default": "没有系统设置配置，使用默认DNS配置", "dns_processor.start_dns_processing": "开始DNS处理", "dns_processor.static_hosts_processing_complete": "静态域名处理完成: 总计 {total} 个，有效 {valid} 个", "dns_processor.using_configured_dns": "使用已配置的DNS服务器: {count} 个", "dropbox.com": "[待翻译] dropbox.com", "encrypt.error.compression_failed": "压缩目录 '{dir}' 失败: {error}", "encrypt.error.library_failed": "加密失败: 无法加载加密库或加密操作失败", "encrypt.error.output_invalid": "加密失败: 输出文件无效或为空", "encrypt.error.process_failed": "加密过程中出错: {error}", "encryption_stage.completed": "加密处理完成: {output}", "encryption_stage.description": "加密处理阶段", "encryption_stage.encryption_failed": "加密失败: {message}", "encryption_stage.encryption_success": "加密成功: {path}, 大小={size}字节", "encryption_stage.executing_encryption": "执行加密: XML={xml_file}, 模板={template}, 输出={output}", "encryption_stage.execution_error": "加密执行失败: {error}", "encryption_stage.failed": "加密处理失败", "encryption_stage.import_error": "加密模块导入失败: {error}", "encryption_stage.no_encryption_requested": "未请求加密处理", "encryption_stage.no_xml_content": "无法找到XML内容进行加密: {details}", "encryption_stage.starting": "开始加密处理", "encryption_stage.starting_encryption": "开始加密: XML文件={xml_file}, 输出={output}", "encryption_stage.temp_xml_cleaned": "临时XML文件已清理: {path}", "encryption_stage.temp_xml_cleanup_failed": "临时XML文件清理失败: {path}, 错误: {error}", "encryption_stage.temp_xml_created": "临时XML文件已创建: {path}", "encryption_stage.temp_xml_creation_failed": "临时XML文件创建失败: {error}", "encryption_stage.template_path_error": "获取模板路径失败: {error}", "encryption_stage.using_default_template": "使用默认模板: {path}", "encryption_stage.using_existing_xml_file": "使用现有XML文件进行加密: {file}", "encryption_stage.using_fallback_template": "使用备用模板: {path}", "encryption_stage.using_generated_xml": "使用生成的XML内容进行加密，临时文件: {temp_file}", "encryption_stage.xml_file_not_found": "XML文件不存在: {file}", "encryption_stage.yang_validation_failed_skip_encryption": "YANG验证失败，跳过加密处理: {message}", "encryption_stage.yang_validation_not_performed_proceed": "未执行YANG验证，继续加密处理", "encryption_stage.yang_validation_passed_proceed": "YANG验证通过，继续加密处理", "enhanced_yang_generator.address_block_created": "enhanced yang generator: address block created", "enhanced_yang_generator.create_new_address": "enhanced yang generator: create new address", "enhanced_yang_generator.create_new_interface": "enhanced yang generator: create new interface", "enhanced_yang_generator.create_new_policy": "enhanced yang generator: create new policy", "enhanced_yang_generator.create_new_service": "enhanced yang generator: create new service", "enhanced_yang_generator.create_new_zone": "enhanced yang generator: create new zone", "enhanced_yang_generator.created_element": "已创建元素", "enhanced_yang_generator.created_element_with_text": "已创建带文本的元素", "enhanced_yang_generator.created_snmp_config": "已创建SNMP配置", "enhanced_yang_generator.creating_element_path": "正在创建元素路径", "enhanced_yang_generator.default_structure_created": "成功创建默认XML结构", "enhanced_yang_generator.encryption_completed": "加密配置包已生成: {path}", "enhanced_yang_generator.encryption_error": "加密过程中发生错误: {error}", "enhanced_yang_generator.encryption_exception": "加密过程中发生异常: {error}", "enhanced_yang_generator.encryption_failed": "加密配置包生成失败: {message}", "enhanced_yang_generator.encryption_failed_fallback": "加密配置包生成失败，将返回非加密版本", "enhanced_yang_generator.encryption_import_error": "导入加密模块失败: {error}", "enhanced_yang_generator.encryption_success": "加密配置包生成成功: {path}, 大小: {size} 字节", "enhanced_yang_generator.fallback_to_default": "enhanced yang generator: fallback to default", "enhanced_yang_generator.fixed_root_namespace": "已修复根节点命名空间为urn:ruijie:ntos", "enhanced_yang_generator.found_existing_element": "enhanced yang generator: found existing element", "enhanced_yang_generator.found_existing_snmp": "enhanced yang generator: found existing snmp", "enhanced_yang_generator.generation_complete": "XML生成完成，验证状态: {validation_status}, yanglint状态: {yanglint_status}", "enhanced_yang_generator.init": "初始化增强YANG生成器，模型: {model}, 版本: {version}", "enhanced_yang_generator.interface_block_created": "enhanced yang generator: interface block created", "enhanced_yang_generator.interface_config_updated": "enhanced yang generator: interface config updated", "enhanced_yang_generator.invalid_template_root": "警告: 模板根节点不是'config': {tag}", "enhanced_yang_generator.modify_existing_address": "enhanced yang generator: modify existing address", "enhanced_yang_generator.modify_existing_interface": "enhanced yang generator: modify existing interface", "enhanced_yang_generator.modify_existing_policy": "enhanced yang generator: modify existing policy", "enhanced_yang_generator.modify_existing_service": "enhanced yang generator: modify existing service", "enhanced_yang_generator.modify_existing_zone": "enhanced yang generator: modify existing zone", "enhanced_yang_generator.namespace_error": "命名空间设置错误: {error}", "enhanced_yang_generator.no_template_found": "未找到模型 {model} 版本 {version} 的模板文件", "enhanced_yang_generator.policy_block_created": "enhanced yang generator: policy block created", "enhanced_yang_generator.schema_exported": "enhanced yang generator: schema exported", "enhanced_yang_generator.service_block_created": "enhanced yang generator: service block created", "enhanced_yang_generator.start_generation": "开始增强YANG XML生成", "enhanced_yang_generator.starting_encryption": "开始生成加密配置包，XML文件: {xml_file}, 模板: {template}, 输出: {output}", "enhanced_yang_generator.template_load_error": "加载模板文件失败: {error}", "enhanced_yang_generator.template_loaded": "成功加载模板文件: {path}", "enhanced_yang_generator.updated_element_text": "enhanced yang generator: updated element text", "enhanced_yang_generator.using_default_template": "使用默认模板路径: {path}", "enhanced_yang_generator.validation_error": "验证错误: {error}", "enhanced_yang_generator.validation_failed_continuing": "enhanced yang generator: validation failed continuing", "enhanced_yang_generator.validation_failed_stopping": "验证失败，停止处理", "enhanced_yang_generator.validation_skipped": "跳过验证以避免错误: {error}", "enhanced_yang_generator.xml_save_error": "保存XML文件失败: {error}", "enhanced_yang_generator.xml_saved": "XML文件已保存到: {path}", "enhanced_yang_generator.yanglint_error": "yang<PERSON>验证过程中发生错误: {error}", "enhanced_yang_generator.yanglint_not_available": "yang<PERSON>不可用，跳过外部验证", "enhanced_yang_generator.yanglint_validation_failed": "yanglint验证失败: {message}", "enhanced_yang_generator.yanglint_validation_passed": "yang<PERSON>验证通过", "error.address_group_conversion_failed": "错误: address group conversion failed", "error.address_group_processing_failed": "处理地址组 {name} 失败: {error}", "error.address_missing_name": "地址对象缺少name字段", "error.address_object_conversion_failed": "地址对象转换失败: {name}，错误: {error}", "error.address_object_creation_failed": "创建地址对象 {name} 失败: {error}", "error.address_object_missing_fields": "错误: 地址对象缺少必要字段", "error.address_object_processing_failed": "处理地址对象 {name} 失败: {error}", "error.all_generation_strategies_failed": "所有生成策略都失败了: {error}", "error.all_physical_interfaces_unmapped": "错误: all physical interfaces unmapped", "error.batch_processing_failed": "批处理失败: {error}", "error.bridge_interface_generation_failed": "桥接接口生成失败: {error}", "error.cannot_convert_to_string": "错误: 无法将{value}转换为字符串，使用默认值", "error.cannot_find_template": "错误: cannot find template", "error.cannot_generate_target_config": "无法生成目标配置", "error.cannot_get_template": "无法获取配置模板", "error.cannot_load_mapping": "无法加载接口映射文件", "error.cannot_load_mapping_file": "无法加载接口映射文件", "error.cannot_load_template": "错误: 无法加载配置模板", "error.cannot_parse_config": "无法解析配置文件", "error.cannot_process_interfaces_data": "无法处理非列表类型的接口数据，设置为空列表", "error.cannot_read_config": "无法读取配置文件", "error.check_interface_config": "请检查接口配置和映射文件", "error.check_interface_mapping": "请检查接口映射配置", "error.check_interfaces": "请检查接口配置和映射文件", "error.config_file_not_exists": "错误: 配置文件不存在: {file}", "error.config_file_not_found": "错误: config file not found", "error.config_file_read_failed": "读取配置文件失败: {error}", "error.conversion_error": "转换过程中发生错误", "error.conversion_failed": "转换过程中发生错误", "error.conversion_process_failed": "错误: conversion process failed", "error.convert_mode_requires_params": "转换模式需要提供映射、型号、版本和输出参数", "error.copy_file_failed": "复制文件失败: {error}", "error.create_address_object_failed": "创建地址对象失败: {name}, 错误: {error}", "error.create_dir_failed": "创建目录失败: {error}", "error.create_element_failed": "创建元素失败: {tag}，错误: {error}", "error.create_interface_failed": "创建接口失败: {name}, 错误: {error}", "error.create_output_dir_failed": "创建输出目录失败: {dir}, 错误: {error}", "error.create_service_object_failed": "错误: create service object failed", "error.create_user_log_dir_failed": "创建用户日志目录失败: {log_dir}，错误: {error}", "error.custom_cleanup_lost_all_children": "错误: custom cleanup lost all children", "error.duplicate_service_object": "发现重复的服务对象名称: {name}", "error.empty_config_tag_detected": "错误: empty config tag detected", "error.encrypt_error": "加密过程中出错: {error}", "error.encrypt_file_failed": "加密文件失败: {error}", "error.encryption_error": "加密过程中发生错误: {error}", "error.encryption_failed": "加密失败", "error.extraction_failed": "提取接口信息失败", "error.extraction_result_format_failed": "接口提取结果格式化失败", "error.failed_to_integrate_nat_rules": "集成NAT规则失败: {error}", "error.failed_to_integrate_security_policies": "集成安全策略失败: {error}", "error.failed_to_load_xml": "加载XML失败: {error}", "error.failed_to_process_interface": "错误: failed to process interface", "error.failed_to_process_service_groups": "处理服务组失败: {error}", "error.failed_to_process_static_route": "错误: failed to process static route", "error.file_empty": "配置文件为空", "error.file_not_exists": "配置文件不存在: {file}", "error.file_not_exists_path": "文件不存在: {file_path}", "error.file_not_found": "找不到文件: {filename}", "error.file_read_failed": "读取文件失败: {error}", "error.file_too_large": "File Too Large", "error.file_too_many_lines": "配置文件行数过多（{lines}行），超过限制", "fortigate.warning.large_config_file": "检测到大型配置文件（{lines}行），将使用优化解析模式", "fortigate.warning.large_config_processing": "正在处理大型配置文件，可能需要较长时间，请耐心等待...", "error.find_or_create_address_container_failed": "查找或创建地址对象容器失败: {error}", "error.find_or_create_physical_interfaces_failed": "查找或创建物理接口容器失败: {error}", "error.find_or_create_vrf_failed": "查找或创建VRF节点失败: {error}", "error.fix_xml_namespaces_failed": "修复XML命名空间失败: {error}", "error.fix_xmlns_failed": "修复xmlns失败: {error}", "error.flush_log_handler_failed": "刷新日志处理器失败: {error}", "error.general": "发生错误", "error.generate_ntos_failed": "生成NTOS配置失败: {error}", "error.generate_target_failed": "无法生成目标配置", "error.generation_failed": "配置生成失败", "error.i18n_log_processing_error": "国际化日志处理错误: {error}", "error.init_translator_failed": "初始化翻译器失败: {error}", "error.interface_mapping_invalid": "接口映射验证失败，发现 {count} 个无效映射", "error.interface_mapping_load_failed": "接口映射加载失败: {file}, 错误: {error}", "error.interface_mapping_not_exists": "错误: 接口映射文件不存在: {file}", "error.interface_mapping_save_failed": "接口映射保存失败: {file}, 错误: {error}", "error.interface_mapping_source_not_found": "错误: 源接口 {interface} 在配置中不存在，目标接口: {target}", "error.interface_mapping_source_not_physical": "错误: 源接口 {interface} 不是物理接口，目标接口: {target}", "error.interface_mapping_target_not_supported": "错误: 目标接口 {interface} 不被设备型号 {model} 支持", "error.interface_mapping_validation_failed": "接口映射验证失败：发现 {count} 个无效映射", "error.interface_missing_name": "接口缺少名称", "error.interface_model_validation_failed": "接口型号验证失败：发现 {count} 个无效映射", "error.interface_name_invalid_type": "错误: 区域 {zone} 中的接口 #{index} 不是字符串类型，而是 {type}", "error.interface_name_not_string": "接口名称不是字符串类型，而是 {type}", "error.interface_not_mapped": "接口未映射: {interfaces}", "error.interface_processing_failed": "处理接口 {name} 时出错: {error}", "error.interface_processing_in_zone_failed": "处理区域 {zone} 的接口 #{index} 时出错: {error}", "error.interfaces_data_not_list": "错误: 区域 {zone} 的接口数据不是列表类型，而是 {type}", "error.invalid_cidr_prefix": "无效的CIDR前缀: {prefix}", "error.invalid_command": "无效的命令", "error.invalid_config": "无效的配置文件", "error.invalid_format": "无效的配置格式", "error.invalid_fortigate_config": "无效的飞塔配置文件", "error.invalid_fortigate_config_structure": "无效的飞塔配置文件结构", "error.invalid_interface_mapping": "无效的接口映射：源接口 {source}，目标接口 {target}，原因：{reason}", "error.invalid_mapping": "接口映射文件格式不正确: {error}", "error.invalid_output_directory_path": "输出目录路径无效或为空", "error.invalid_output_file_path": "输出文件路径无效或为空", "error.invalid_route_data_format": "路由数据格式不正确，无法创建静态路由: {data}", "error.invalid_subnet_mask_format": "无效的子网掩码格式: {mask}", "error.invalid_vlanid_format": "错误: 无效的VLAN ID格式 {vlanid}，使用默认值1", "error.ippool_processing_failed": "处理IP池 '{pool_name}' 失败: {error}", "error.ipv6_static_route_processing_error": "IPv6静态路由处理错误: {error}", "error.json_print_failed": "JSON打印失败: {error}", "error.json_serialization_failed": "JSON序列化失败: {error}", "error.legacy_generation_failed": "传统生成策略失败: {error}", "error.legacy_policy_rule_conversion_failed": "策略规则 {name} 转换失败: {error}", "error.load_mapping_failed": "加载接口映射文件失败: {error}", "error.load_service_mapping_config_failed": "加载服务映射配置失败: {error}", "error.mapping_file_load_failed": "加载接口映射文件失败: {error}", "error.mask_conversion_failed": "掩码转换为前缀长度失败: {mask}, 错误: {error}", "error.missing_interface_config": "错误: missing interface config", "error.missing_interface_name": "错误: 缺少接口名称，无法创建子接口", "error.missing_name_field": "缺少name字段", "error.missing_parameters": "缺少必要参数", "error.missing_parent_interface": "错误: 缺少父接口信息，无法创建子接口", "error.missing_required_fields": "缺少必要字段: {fields}", "error.missing_vlanid": "错误: 缺少VLAN ID，无法创建子接口", "error.model_version_not_provided": "错误: 未提供型号或版本参数，无法进行XML验证", "error.model_version_not_supported": "错误: model version not supported", "error.name_reverted_to_main": "错误: 名称恢复为'main'，原始值={original}", "error.name_update_failed": "错误: 名称更新失败: 期望值={expected}, 实际值={actual}", "error.namespace_cleanup_failed": "命名空间清理失败: {error}", "error.namespace_cleanup_lost_all_content": "错误: namespace cleanup lost all content", "error.no_data": "无数据", "error.no_template": "无法找到模板文件: {model}/{version}", "error.no_template_for_model_version": "错误: 找不到对应型号和版本的配置模板", "error.no_valid_interfaces": "没有有效的接口需要处理", "error.no_yang_files_found": "在YANG模型目录中没有找到.yang或.yin文件", "error.no_yang_files_found_cannot_validate": "错误: 在YANG模型目录中没有找到.yang或.yin文件，无法进行XML验证", "error.no_yang_model_files": "没有找到YANG模型文件，无法验证", "error.ntos_generation_failed": "生成NTOS配置失败: {error}", "error.ntos_services_load_failed": "NTOS服务加载失败: {error}", "error.parent_element_is_empty_list": "错误: parent element is empty list", "error.parent_element_is_none": "父元素为空，无法创建元素: {tag}", "error.parent_interface_mapping_not_found": "父接口映射未找到: {name}", "error.parent_not_element_object": "错误: parent not element object", "error.parse_config_failed": "解析配置文件失败: {error}", "error.parsed_data_not_dict": "错误: parsed_data不是字典类型: {data_type}", "error.parsing_config_failed": "解析配置文件失败: {error}", "error.parsing_failed": "解析失败: {file}, 原因: {reason}", "error.parsing_failed_with_error": "解析失败: {error}", "error.parsing_loop_detected": "Parsing Loop Detected", "error.policy_rule_conversion_error": "策略规则 {name} 转换时出错: {error}", "error.policy_rule_conversion_failed": "转换策略规则 {name} 失败: {error}", "error.policy_rule_creation_failed": "创建策略规则 {name} 失败: {error}", "error.policy_rule_missing_name": "错误: 策略规则缺少名称字段", "error.policy_rule_processing_failed": "处理策略规则 {name} 失败: {error}", "error.possible_unbalanced_nesting": "可能存在不平衡的嵌套结构", "error.process_interface_failed": "处理接口失败: {name}, 错误: {error}", "error.processing_address_object_failed": "处理地址对象失败: {name}, 错误: {error}", "error.processing_all_address_objects_failed": "处理所有地址对象失败: {error}", "error.processing_all_config_failed": "处理配置数据失败: {error}", "error.processing_all_interfaces_failed": "处理所有接口失败: {error}", "error.processing_interface_failed": "处理接口失败: {name}, 错误: {error}", "error.read_config_failed": "读取配置文件失败: {error}", "error.read_json_file_failed": "读取JSON文件失败: {error}", "error.read_text_file_failed": "读取文本文件失败: {error}", "error.report_generation_failed": "报告生成失败: {error}", "error.root_element_is_empty_list": "XML根元素是空列表", "error.root_element_is_none": "XML根元素为空", "error.route_missing_destination": "静态路由 {id} 缺少目的网络信息，无法处理", "error.route_processing_failed": "处理路由 {name} 失败: {error}", "error.save_final_interface_mapping_failed": "保存最终接口映射失败: {error}", "error.schedule_conversion_error": "错误: schedule conversion error", "error.service_group_processing_error": "错误: service group processing error", "error.service_group_processing_failed": "处理服务组 {name} 失败: {error}", "error.service_mapping_load_failed": "服务映射加载失败: {error}", "error.service_missing_name": "错误: service missing name", "error.service_object_conversion_failed": "转换服务对象 {name} 失败: {error}", "error.service_object_creation_failed": "创建服务对象 {name} 失败: {error}", "error.service_object_missing_fields": "错误: 服务对象缺少必要字段", "error.service_object_processing_failed": "处理服务对象 {name} 失败: {error}", "error.source_interface_not_exists": "源接口 {interface} 不存在", "error.source_interface_not_physical": "源接口 {interface} 不是物理接口", "error.static_route_creation_failed": "创建静态路由 {id} 失败: {error}", "error.static_route_creation_failed_with_error": "创建静态路由失败: {error}", "error.static_route_processing_error": "处理静态路由时出错: {error}", "error.subinterface_missing_parent": "子接口缺少父接口: {name}", "error.subinterface_missing_vlanid": "子接口缺少VLAN ID: {name}", "error.target_config_generation_failed": "生成目标配置失败", "error.template_file_not_exists": "模板文件不存在: {path}", "error.template_format_incorrect": "错误: 模板格式不符合要求", "error.template_format_invalid": "错误: 模板文件格式无效", "error.template_invalid": "错误: 模板文件不存在或无效", "error.template_not_exists": "模板文件不存在: {path}", "error.template_not_found": "无法找到模板文件: {model}/{version}", "error.template_parse_failed": "错误: 无法解析模板文件: {error}", "error.template_parsing_failed": "解析模板失败: {error}", "error.template_parsing_failed_user": "无法解析配置模板，请检查文件格式", "error.time_range_conversion_failed": "时间对象 {name} 转换失败: {error}", "error.transparent_mode_processing_failed": "透明模式处理失败: {error}", "error.unable_to_detect_version": "无法检测配置文件的FortiOS版本", "error.unbalanced_config": "配置块不平衡，有 {count} 个未闭合的配置块", "error.unknown_error": "转换过程中发生未知错误: {error}", "error.unknown_reason": "未知原因", "error.unsupported_fortigate_version": "不支持的FortiOS版本: {version}，最低要求版本: {min_version}", "error.unsupported_vendor": "不支持的厂商", "error.validation_error": "验证错误: {error}", "error.validation_exception": "验证过程中出现异常: {error}", "error.validation_failed": "验证失败: {message}", "error.validation_process_error": "验证过程出错: {error}", "error.vendor_mapping_load_failed": "厂商映射加载失败: {error}", "error.vendor_not_supported": "错误: 不支持的厂商 '{vendor}'，当前支持的厂商: {supported}", "error.verification_error": "验证过程中发生错误: {error}", "error.verification_failed": "验证失败: {error}", "error.vlan_extraction_failed": "从接口名提取VLAN ID失败: {interface}, 错误: {error}", "error.vrf_node_not_found": "错误: 模板中没有找到vrf节点", "error.write_interface_info_failed": "写入接口信息文件失败: {error}", "error.write_json_file_failed": "写入JSON文件失败: {error}", "error.write_text_file_failed": "写入文本文件失败: {error}", "error.xml_generation_failed": "XML生成失败: {error}", "error.xml_generation_produced_invalid_output": "错误: xml generation produced invalid output", "error.xml_repair_produced_invalid_output": "错误: xml repair produced invalid output", "error.xml_string_too_short": "错误: xml string too short", "error.xml_structure_damaged_during_fixing": "错误: xml structure damaged during fixing", "error.xml_validation_error": "XML验证过程中发生错误: {error}", "error.xml_validation_failed": "XML验证失败: {details}", "error.xml_validation_failed_message": "生成的配置文件未通过YANG模型验证，无法继续处理", "error.xmlns_repair_lost_significant_content": "错误: xmlns repair lost significant content", "error.yang_generation_failed": "YANG生成策略失败: {error}", "error.yang_model_dir_not_exists": "YANG模型目录不存在: {dir}", "error.yang_model_dir_not_exists_cannot_validate": "错误: YANG模型目录不存在: {dir}，无法进行XML验证", "error.yang_validation_failed": "错误: yang validation failed", "error.yanglint_check_error": "yanglint检查错误: {error}", "error.yanglint_execution_error": "yanglint执行错误: {error}", "error.yanglint_not_available": "yanglint命令不可用，请安装libyang-tools", "error.yanglint_not_found": "无法找到yanglint命令，请确保已安装libyang-tools", "error.yanglint_tool_not_available": "错误: yang<PERSON>工具不可用，无法进行XML验证", "error.yanglint_validation_failed": "yanglint验证失败: {message}", "error.zone_data_invalid_type": "错误: 区域数据 #{index} 不是字典类型，而是 {type}", "error.zone_data_not_list": "错误: 区域数据不是列表类型，而是 {type}", "error.zone_missing_name": "错误: 区域缺少名称字段，跳过", "error.zone_processing_failed": "处理区域 {name} 时出错: {error}", "error_handler.connection_error_recovery": "错误处理: connection error recovery", "error_handler.error_handled": "错误处理: error handled", "error_handler.file_not_found_recovery": "错误处理: file not found recovery", "error_handler.initialized": "错误处理器已初始化", "error_handler.key_error_recovery": "错误处理: key error recovery", "error_handler.memory_error_recovery": "错误处理: memory error recovery", "error_handler.permission_error_recovery": "错误处理: permission error recovery", "error_handler.recovery_failed": "错误处理: recovery failed", "error_handler.timeout_error_recovery": "错误处理: timeout error recovery", "error_handler.unknown_error": "错误处理: unknown error", "error_handler.value_error_recovery": "错误处理: value error recovery", "exception_registry.handler_registered": "exception registry: handler registered", "exception_registry.initialized": "异常注册表已初始化", "extraction_service.extraction_completed": "extraction service: extraction completed", "extraction_service.initialized": "提取服务已初始化", "feature.advanced_vpn": "高级VPN配置", "feature.dynamic_routing": "动态路由协议", "feature.vdom": "虚拟域(VDOM)配置", "file_path_utils.directory_created": "目录已创建: {directory}", "file_path_utils.disk_space_check_failed": "磁盘空间检查失败: {path}, 错误: {error}", "file_path_utils.disk_space_checked": "磁盘空间检查: {path}, 可用: {available}, 需要: {required}, 充足: {sufficient}", "file_path_utils.ensure_directory_failed": "确保目录失败: {path}, 错误: {error}", "file_path_utils.file_backed_up": "文件已备份: {original} -> {backup}", "file_path_utils.file_restored_from_backup": "文件已从备份恢复: {file}", "file_path_utils.file_write_failed": "文件写入失败: {file}, 错误: {error}", "file_path_utils.file_written_successfully": "文件写入成功: {file}, 大小: {size} 字节", "file_path_utils.output_directory_ready": "输出目录准备就绪: {directory}", "file_path_utils.path_normalized": "路径已标准化: {original} -> {normalized}", "file_path_utils.permission_check_failed": "权限检查失败: {directory}, 错误: {error}", "file_path_utils.temp_path_created": "临时路径已创建: {path}", "fix.xml": "[待翻译] fix.xml", "format.data_object_placeholder": "[数据对象]", "format.interface_placeholder": "接口: {name}", "format.json_data_placeholder": "[JSON数据]", "format.missing_param_fallback": "[缺少参数: {param}]", "format.missing_param_placeholder": "[缺失:{param}]", "format.missing_parameter": "[缺失:{param}]", "format.object_placeholder": "对象: {name}", "format.unknown_error": "未知错误", "format.unknown_file": "未知文件", "format.unknown_interface": "未知接口", "format_not_supported": "不支持的配置格式: {format}", "fortigate.address_group_from_service_group": "从服务组 {name} 创建地址组，包含 {count} 个成员", "fortigate.address_interface_unsupported": "不支持带接口关联的地址对象，如: set interface \"xxx\", set associated-interface \"xxx\"", "fortigate.address_parsing_complete": "完成地址对象 {name} 解析", "fortigate.address_type_support": "支持的地址对象类型: ipmask, subnet, iprange", "fortigate.address_type_unsupported": "不支持的地址对象类型: fqdn, geography, wildcard, dynamic, interface-subnet, mac, route-tag", "fortigate.addrgrp_parsing_complete": "完成地址组 {name} 解析，包含 {count} 个成员", "fortigate.auto_adding_ip_address": "自动添加IP地址：{name}", "fortigate.auto_adding_range_address": "自动添加范围地址：{name}", "fortigate.cannot_parse_vlanid": "无法解析VLAN ID: {vlanid}", "fortigate.debug.adding_address": "添加地址对象: {name}, 属性: {attrs}", "fortigate.debug.adding_addrgrp": "添加地址组: {name}, 成员: {members}", "fortigate.debug.adding_default_datetime_end": "时间对象 {name} 未设置结束日期时间，添加默认值: {datetime}", "fortigate.debug.adding_default_datetime_start": "时间对象 {name} 未设置开始日期时间，添加默认值: {datetime}", "fortigate.debug.adding_default_time_end": "时间对象 {name} 未设置结束时间，添加默认值: {time}", "fortigate.debug.adding_default_time_start": "时间对象 {name} 未设置开始时间，添加默认值: {time}", "fortigate.debug.adding_default_weekdays": "时间对象 {name} 未设置星期几，添加默认所有日期", "fortigate.debug.adding_dhcp_server": "Adding Dhcp Server", "fortigate.debug.adding_interface": "添加接口: {name}, 属性: {attrs}", "fortigate.debug.adding_ippool": "Adding Ippool", "fortigate.debug.adding_ipsec_phase1": "Adding Ipsec Phase1", "fortigate.debug.adding_ipsec_phase2": "Adding Ipsec Phase2", "fortigate.debug.adding_local_user": "Adding Local User", "fortigate.debug.adding_policy": "Adding Policy", "fortigate.debug.adding_schedule": "添加时间对象: {name}, 属性: {attrs}", "fortigate.debug.adding_service_custom": "添加自定义服务: {name}", "fortigate.debug.adding_service_group": "添加服务组: {name}, 成员: {members}", "fortigate.debug.adding_ssl_vpn_portal": "Adding Ssl Vpn Portal", "fortigate.debug.adding_user_group": "Adding User Group", "fortigate.debug.adding_vip": "Adding Vip", "fortigate.debug.content_read_success": "从传入的文件路径成功读取内容: {file}", "fortigate.debug.converting_to_address_group": "debug.converting to address group", "fortigate.debug.first_line": "文件首行内容: {content}", "fortigate.debug.interface_section_end": "接口配置部分结束", "fortigate.debug.interface_section_start": "开始接口配置部分", "fortigate.debug.parsing_interface": "解析接口: {name}", "fortigate.debug.processing_line": "行号{line_num}: {content} | 当前section: {section}", "fortigate.debug.second_line": "文件第二行内容: {content}", "fortigate.debug.setting_address_color": "设置地址对象 {name} 的颜色: {color}", "fortigate.debug.setting_address_comment": "设置地址对象 {name} 的备注: {comment}", "fortigate.debug.setting_address_country": "设置地址对象 {name} 的国家: {country}", "fortigate.debug.setting_address_end_ip": "设置地址对象 {name} 的结束IP: {end_ip}", "fortigate.debug.setting_address_fqdn": "设置地址对象 {name} 的FQDN: {fqdn}", "fortigate.debug.setting_address_interface": "设置地址对象 {name} 的关联接口: {interface}", "fortigate.debug.setting_address_start_ip": "设置地址对象 {name} 的起始IP: {start_ip}", "fortigate.debug.setting_address_subnet": "设置地址对象 {name} 的子网: {subnet}", "fortigate.debug.setting_address_type": "设置地址对象 {name} 的类型: {type}", "fortigate.debug.setting_address_uuid": "设置地址对象 {name} 的UUID: {uuid}", "fortigate.debug.setting_address_wildcard": "设置地址对象 {name} 的通配符: {wildcard}", "fortigate.debug.setting_schedule_comment": "设置时间对象注释: {name} -> {comment}", "fortigate.debug.setting_schedule_datetime_end": "设置时间对象 {name} 结束日期时间为 {datetime}", "fortigate.debug.setting_schedule_datetime_start": "设置时间对象 {name} 开始日期时间为 {datetime}", "fortigate.debug.setting_schedule_end_date": "设置时间对象 {name} 结束日期为 {date}", "fortigate.debug.setting_schedule_members": "设置时间对象组成员: {name} -> {members}", "fortigate.debug.setting_schedule_start_date": "设置时间对象 {name} 开始日期为 {date}", "fortigate.debug.setting_schedule_time_end": "设置时间对象 {name} 结束时间为 {time}", "fortigate.debug.setting_schedule_time_start": "设置时间对象 {name} 开始时间为 {time}", "fortigate.debug.setting_schedule_type": "设置时间对象 {name} 类型为 {type}", "fortigate.debug.setting_schedule_weekdays": "设置时间对象 {name} 星期为 {weekdays}", "fortigate.debug.special_schedule_always": "特殊处理'always'时间对象 {name}，设置为全时段", "fortigate.debug.special_schedule_none": "特殊处理'none'时间对象 {name}，设置为空时段", "fortigate.debug.trying_line_by_line": "尝试按行读取文件: {file}", "fortigate.detected_config_command": "检测到配置命令: {command_line}", "fortigate.detected_config_version": "检测到FortiGate配置版本行: {version_line}", "fortigate.dhcp_server_parsing_complete": "Dhcp Server Parsing Complete", "fortigate.dns_config_processed": "DNS配置处理完成，服务器数量: {servers}，DNS over TLS: {dns_over_tls}", "fortigate.error.invalid_format": "错误：配置文件格式无效，首行: {first_line}", "fortigate.error_vlanid_range": "错误: {error}", "fortigate.first_interface": "第一个接口: {name}", "fortigate.fortianalyzer_parsing_complete": "FortiAnalyzer {id} 解析完成", "fortigate.info.interface_type_converted": "信息: 接口 '{interface}' 类型从 '{original_type}' 转换为 '{converted_type}' 以兼容NTOS", "fortigate.info.role_will_be_converted_to_lan": "接口 '{interface}' 的角色 '{role}' 将被转换为 'lan'", "fortigate.interface_attribute_error": "解析接口属性时出错: {error}", "fortigate.interface_parsing_complete": "完成接口 {name} 解析", "fortigate.interface_processing_complete": "FortiGate接口处理完成", "fortigate.interface_section_end": "接口配置部分结束", "fortigate.interface_skipped": "接口 {name} 已跳过转换", "fortigate.invalid_bfd_format": "无法解析BFD设置: {line}", "fortigate.invalid_blackhole_format": "无法解析黑洞路由设置: {line}", "fortigate.invalid_config": "文件不包含有效的飞塔配置", "fortigate.invalid_destination_format": "无法解析目标网络，格式不正确: {line}", "fortigate.invalid_device_format": "无法解析出接口: {line}", "fortigate.invalid_distance_format": "无法解析管理距离: {line}", "fortigate.invalid_gateway_format": "无法解析网关地址: {line}", "fortigate.invalid_mtu_format": "无效的MTU格式: {line}", "fortigate.invalid_vlanid": "警告: 无法解析VLAN ID: {vlanid}", "fortigate.ippool_parsing_complete": "Ippool Parsing Complete", "fortigate.ipsec_phase1_parsing_complete": "Ipsec Phase1 Parsing Complete", "fortigate.ipsec_phase2_parsing_complete": "Ipsec Phase2 Parsing Complete", "fortigate.local_user_parsing_complete": "Local User Parsing Complete", "fortigate.log_filter_parsing_complete": "日志过滤器 {filter} 解析完成", "fortigate.no_interfaces_found": "警告: 没有找到任何接口配置!", "fortigate.parser_description": "FortiGate防火墙配置解析器", "fortigate.parsing_address": "正在解析地址对象: {name}", "fortigate.parsing_addrgrp": "解析地址组: {name}", "fortigate.parsing_cli_file": "解析CLI文件: {file}", "fortigate.parsing_complete": "解析完成，找到 {interfaces} 个接口, {routes} 个静态路由, {zones} 个区域, {addresses} 个地址对象, {addrgroups} 个地址组, {services} 个服务对象, {servicegroups} 个服务组", "fortigate.parsing_complete_user": "解析完成，找到 {interfaces} 个接口, {routes} 个静态路由, {zones} 个区域, {addresses} 个地址对象, {addrgroups} 个地址组, {services} 个服务对象, {servicegroups} 个服务组", "fortigate.parsing_dhcp_ip_range": "Parsing Dhcp Ip Range", "fortigate.parsing_dhcp_option": "Parsing Dhcp Option", "fortigate.parsing_dhcp_server": "Parsing Dhcp Server", "fortigate.parsing_error": "解析配置文件时发生错误: {error}", "fortigate.parsing_error_user": "解析配置文件时发生错误", "fortigate.parsing_exception": "解析异常: {error}", "fortigate.parsing_interface": "解析接口: {name}", "fortigate.parsing_ippool": "Parsing Ippool", "fortigate.parsing_ipsec_phase1": "Parsing Ipsec Phase1", "fortigate.parsing_ipsec_phase2": "Parsing Ipsec Phase2", "fortigate.parsing_ipv6_block": "解析IPv6配置块", "fortigate.parsing_local_user": "Parsing Local User", "fortigate.parsing_policy": "Parsing Policy", "fortigate.parsing_result_summary": "解析结果：接口：{interfaces}个, 路由：{routes}个, 区域：{zones}个, 地址对象：{addresses}个, 地址组：{addrgroups}个, 服务对象：{services}个, 服务组：{servicegroups}个", "fortigate.parsing_schedule": "解析时间对象 {name}", "fortigate.parsing_secondaryip_block": "解析辅助IP配置块", "fortigate.parsing_service_custom": "解析自定义服务: {name}", "fortigate.parsing_service_group": "解析服务组: {name}", "fortigate.parsing_ssl_vpn_portal": "Parsing Ssl Vpn Portal", "fortigate.parsing_user_group": "Parsing User Group", "fortigate.parsing_vip": "Parsing Vip", "fortigate.parsing_zone": "解析区域: {name}", "fortigate.policy_parsing_complete": "Policy Parsing Complete", "fortigate.pppoe_password_manual_change_required": "注意：PPPoE密码已设置为固定值'123456'，请在部署后手动修改为实际密码", "fortigate.read_file_success": "成功读取文件，共 {lines} 行", "fortigate.read_file_success_user": "配置文件读取成功，共 {lines} 行", "fortigate.reason.unknown": "未知原因", "fortigate.reason.unsupported_role": "不支持的接口角色: {role}", "fortigate.route_bfd": "BFD启用: {status}", "fortigate.route_blackhole": "黑洞路由: {status}", "fortigate.route_destination": "静态路由目标网络: {dest}", "fortigate.route_device": "静态路由出接口: {device}", "fortigate.route_distance": "静态路由管理距离: {distance}", "fortigate.route_gateway": "静态路由网关: {gateway}", "fortigate.route_missing_destination": "静态路由 {id} 缺少目标网络，将使用默认值: 0.0.0.0/0", "fortigate.schedule_parsing_complete": "时间对象 {name} 解析完成", "fortigate.secondary_ip_detected": "警告: 检测到secondary-IP enable，将使用'config secondaryip'块", "fortigate.section_end": "{section} 配置部分结束", "fortigate.service_custom_parsing_complete": "完成自定义服务 {name} 解析", "fortigate.service_group_parsing_complete": "完成服务组 {name} 解析，包含 {count} 个成员", "fortigate.set_address_associated_interface": "设置地址对象关联接口: {interface}", "fortigate.set_address_comment": "设置地址对象备注: {comment}", "fortigate.set_address_end_ip": "设置地址对象结束IP: {ip}", "fortigate.set_address_fqdn": "设置地址对象FQDN: {fqdn}", "fortigate.set_address_interface": "设置地址对象接口: {interface}", "fortigate.set_address_start_ip": "设置地址对象起始IP: {ip}", "fortigate.set_address_subnet": "设置地址对象子网: {subnet}", "fortigate.set_address_type": "设置地址对象类型: {type}", "fortigate.set_addrgrp_comment": "设置地址组备注: {comment}", "fortigate.set_addrgrp_members": "设置地址组成员数: {count}", "fortigate.set_auth_cert": "<PERSON> Auth Cert", "fortigate.set_auth_http_basic": "Set Auth Http Basic", "fortigate.set_auth_invalid_max": "<PERSON> Auth Invalid Max", "fortigate.set_auth_lockout_duration": "Set Auth Lockout Duration", "fortigate.set_auth_lockout_threshold": "Set Auth Lockout Threshold", "fortigate.set_auth_timeout": "Set Auth Timeout", "fortigate.set_defaultgw": "设置默认网关: {value}", "fortigate.set_dhcp_default_gateway": "Set Dhcp Default Gateway", "fortigate.set_dhcp_dns_server1": "Set Dhcp Dns Server1", "fortigate.set_dhcp_dns_server2": "Set Dhcp Dns Server2", "fortigate.set_dhcp_domain": "Set Dhcp Domain", "fortigate.set_dhcp_end_ip": "Set Dhcp End Ip", "fortigate.set_dhcp_interface": "Set Dhcp Interface", "fortigate.set_dhcp_lease_time": "Set Dhcp Lease Time", "fortigate.set_dhcp_netmask": "Set Dhcp Netmask", "fortigate.set_dhcp_option_code": "Set Dhcp Option Code", "fortigate.set_dhcp_option_type": "Set Dhcp Option Type", "fortigate.set_dhcp_option_value": "Set Dhcp Option Value", "fortigate.set_dhcp_start_ip": "Set Dhcp Start Ip", "fortigate.set_dns_domains": "设置DNS域: {domains}", "fortigate.set_dns_over_tls": "设置DNS over TLS: {value}", "fortigate.set_dns_primary": "设置主DNS服务器: {server}", "fortigate.set_dns_protocol": "设置DNS协议: {protocol}", "fortigate.set_dns_secondary": "设置辅DNS服务器: {server}", "fortigate.set_dns_source_ip": "Set Dns Source Ip", "fortigate.set_downstream_bandwidth": "设置下行带宽: {bw}", "fortigate.set_fortianalyzer_filter": "设置FortiAnalyzer过滤器: {filter}", "fortigate.set_fortianalyzer_reliable": "设置FortiAnalyzer可靠性: {reliable}", "fortigate.set_fortianalyzer_server": "设置FortiAnalyzer服务器: {server}", "fortigate.set_fortianalyzer_status": "设置FortiAnalyzer {id} 状态: {status}", "fortigate.set_fortianalyzer_upload_option": "设置FortiAnalyzer上传选项: {option}", "fortigate.set_fwpolicy_implicit_log": "Set Fwpolicy Implicit Log", "fortigate.set_interface_allowaccess": "设置接口访问控制: {access}", "fortigate.set_interface_description": "设置接口描述: {desc}", "fortigate.set_interface_enabled": "设置接口启用状态: {enabled}", "fortigate.set_interface_ip_cidr": "设置接口IP (CIDR): {ip}", "fortigate.set_interface_ip_mask": "设置接口IP (掩码): {ip}", "fortigate.set_interface_mode": "设置接口模式: {mode}", "fortigate.set_interface_mtu": "设置接口MTU: {mtu}", "fortigate.set_interface_role": "设置接口角色: {role}", "fortigate.set_interface_status": "设置接口状态: {status}", "fortigate.set_interface_type": "设置接口类型: {type}", "fortigate.set_interface_vdom": "设置接口虚拟域: {vdom}", "fortigate.set_intrazone": "设置区域 {zone} 的内部通信策略: {policy}", "fortigate.set_ippool_comment": "Set Ippool Comment", "fortigate.set_ippool_endip": "Set Ippool <PERSON>ip", "fortigate.set_ippool_source_endip": "Set Ippool Source Endip", "fortigate.set_ippool_source_startip": "Set Ippool Source Startip", "fortigate.set_ippool_startip": "Set Ippool Startip", "fortigate.set_ippool_type": "Set Ippool Type", "fortigate.set_ipsec_comments": "Set Ipsec Comments", "fortigate.set_ipsec_dhgrp": "Set Ipsec Dhgrp", "fortigate.set_ipsec_dst_addr_type": "Set Ipsec Dst Addr Type", "fortigate.set_ipsec_dst_subnet": "Set Ipsec Dst Subnet", "fortigate.set_ipsec_interface": "Set Ipsec Interface", "fortigate.set_ipsec_keylife": "Set Ipsec Keylife", "fortigate.set_ipsec_mode": "Set Ipsec Mode", "fortigate.set_ipsec_pfs": "Set Ipsec Pfs", "fortigate.set_ipsec_phase1name": "Set Ipsec Phase1Name", "fortigate.set_ipsec_phase2_dhgrp": "Set Ipsec Phase2 Dhgrp", "fortigate.set_ipsec_phase2_keylife": "Set Ipsec Phase2 Keylife", "fortigate.set_ipsec_phase2_proposal": "Set Ipsec Phase2 Proposal", "fortigate.set_ipsec_proposal": "Set Ipsec Proposal", "fortigate.set_ipsec_psksecret": "Set Ipsec Psksecret", "fortigate.set_ipsec_remote_gw": "Set Ipsec Remote Gw", "fortigate.set_ipsec_src_addr_type": "Set Ipsec Src Addr Type", "fortigate.set_ipsec_src_subnet": "Set Ipsec Src Subnet", "fortigate.set_ipv6_allowaccess": "设置IPv6访问控制: {access}", "fortigate.set_ipv6_mode": "设置IPv6模式: {mode}", "fortigate.set_log_filter_anomaly": "设置日志过滤器异常: {enabled}", "fortigate.set_log_filter_dlp_archive": "设置日志过滤器DLP归档: {enabled}", "fortigate.set_log_filter_event": "设置日志过滤器事件: {enabled}", "fortigate.set_log_filter_forward_traffic": "设置日志过滤器转发流量: {enabled}", "fortigate.set_log_filter_local_traffic": "设置日志过滤器本地流量: {enabled}", "fortigate.set_log_filter_multicast_traffic": "设置日志过滤器组播流量: {enabled}", "fortigate.set_log_filter_severity": "设置日志过滤器 {filter} 严重级别: {severity}", "fortigate.set_log_filter_sniffer_traffic": "设置日志过滤器嗅探器流量: {enabled}", "fortigate.set_log_filter_voip": "设置日志过滤器VoIP: {enabled}", "fortigate.set_log_invalid_packet": "Set Log Invalid Packet", "fortigate.set_log_resolve_ip": "Set Log Resolve Ip", "fortigate.set_log_status": "设置日志状态: {status}", "fortigate.set_log_user_in_upper": "Set Log User In Upper", "fortigate.set_parent_interface": "设置子接口所属的物理接口: {parent}", "fortigate.set_policy_action": "Set Policy Action", "fortigate.set_policy_application_list": "Set Policy Application List", "fortigate.set_policy_av_profile": "Set Policy Av Profile", "fortigate.set_policy_comments": "Set Policy Comments", "fortigate.set_policy_dstaddr": "Set Policy Dstaddr", "fortigate.set_policy_dstintf": "Set Policy Dstintf", "fortigate.set_policy_fixedport": "设置策略固定端口: {fixedport}", "fortigate.set_policy_groups": "设置策略用户组: {groups}", "fortigate.set_policy_ippool": "Set Policy Ippool", "fortigate.set_policy_ips_sensor": "Set Policy Ips Sensor", "fortigate.set_policy_logtraffic": "Set Policy Logtra<PERSON>ic", "fortigate.set_policy_nat": "Set Policy Nat", "fortigate.set_policy_port_preserve": "设置策略端口保持: {port_preserve}", "fortigate.set_policy_profile_protocol_options": "Set Policy Profile Protocol Options", "fortigate.set_policy_profile_type": "Set Policy Profile Type", "fortigate.set_policy_schedule": "Set Policy Schedule", "fortigate.set_policy_service": "Set Policy Service", "fortigate.set_policy_srcaddr": "Set Policy Srcaddr", "fortigate.set_policy_srcintf": "Set Policy Srcintf", "fortigate.set_policy_ssl_ssh_profile": "Set Policy Ssl Ssh Profile", "fortigate.set_policy_status": "设置策略状态: {status}", "fortigate.set_policy_users": "设置策略用户: {users}", "fortigate.set_policy_utm_status": "设置策略UTM状态: {status}", "fortigate.set_policy_webfilter_profile": "Set Policy Webfilter Profile", "fortigate.set_pppoe_password": "设置PPPoE密码 (加密)", "fortigate.set_pppoe_password_fixed": "设置PPPoE固定密码 (原密码已替换为固定值)", "fortigate.set_pppoe_username": "设置PPPoE用户名: {username}", "fortigate.set_security_mode": "设置安全模式: {mode}", "fortigate.set_service_comment": "设置服务备注: {comment}", "fortigate.set_service_group_comment": "设置服务组备注: {comment}", "fortigate.set_service_group_members": "设置服务组成员数: {count}", "fortigate.set_service_iprange": "设置服务IP范围: {iprange}", "fortigate.set_service_tcp_portrange": "设置服务TCP端口范围: {portrange}", "fortigate.set_service_udp_portrange": "设置服务UDP端口范围: {portrange}", "fortigate.set_snmp_index": "设置SNMP索引: {index}", "fortigate.set_ssl_vpn_dns_server1": "Set Ssl Vpn Dns Server1", "fortigate.set_ssl_vpn_dns_server2": "Set Ssl Vpn Dns Server2", "fortigate.set_ssl_vpn_ip_pools": "Set Ssl Vpn Ip Pools", "fortigate.set_ssl_vpn_port": "Set Ssl Vpn Port", "fortigate.set_ssl_vpn_portal_dns_server1": "Set Ssl Vpn Portal Dns Server1", "fortigate.set_ssl_vpn_portal_dns_server2": "Set Ssl Vpn Portal Dns Server2", "fortigate.set_ssl_vpn_servercert": "Set Ssl Vpn Servercert", "fortigate.set_ssl_vpn_source_interface": "Set Ssl Vpn Source Interface", "fortigate.set_ssl_vpn_split_tunneling": "Set Ssl Vpn Split Tunneling", "fortigate.set_ssl_vpn_split_tunneling_routes": "Set Ssl Vpn Split Tunneling Routes", "fortigate.set_ssl_vpn_status": "设置SSL VPN状态: {status}", "fortigate.set_ssl_vpn_tunnel_mode": "Set Ssl Vpn Tunnel Mode", "fortigate.set_ssl_vpn_web_mode": "Set Ssl Vpn Web Mode", "fortigate.set_syslogd_facility": "设置系统日志设施: {facility}", "fortigate.set_syslogd_filter": "设置系统日志过滤器: {filter}", "fortigate.set_syslogd_format": "设置系统日志格式: {format}", "fortigate.set_syslogd_mode": "设置系统日志模式: {mode}", "fortigate.set_syslogd_port": "设置系统日志端口: {port}", "fortigate.set_syslogd_server": "设置系统日志服务器: {server}", "fortigate.set_syslogd_status": "设置系统日志 {id} 状态: {status}", "fortigate.set_upstream_bandwidth": "设置上行带宽: {bw}", "fortigate.set_user_auth_type": "Set User Auth Type", "fortigate.set_user_email": "Set User Email", "fortigate.set_user_group_auth_timeout": "Set User Group Auth Timeout", "fortigate.set_user_group_members": "Set User Group Members", "fortigate.set_user_group_type": "Set User Group Type", "fortigate.set_user_password": "Set User Password", "fortigate.set_user_sms_phone": "Set User Sms Phone", "fortigate.set_user_status": "设置用户状态: {status}", "fortigate.set_user_two_factor": "Set User Two Factor", "fortigate.set_vip_comment": "Set Vip Comment", "fortigate.set_vip_extintf": "Set Vip Extintf", "fortigate.set_vip_extip": "Set Vip Extip", "fortigate.set_vip_extport": "Set Vip Extport", "fortigate.set_vip_mappedip": "Set Vip Mappedip", "fortigate.set_vip_mappedport": "Set Vip Mappedport", "fortigate.set_vip_portforward": "Set Vip Portforward", "fortigate.set_vip_protocol": "Set Vip Protocol", "fortigate.set_vlanid": "设置子接口VLAN ID: {vlanid}", "fortigate.set_zone_description": "设置区域 {zone} 的描述: {desc}", "fortigate.set_zone_interfaces": "设置区域 {zone} 的接口: {interfaces}", "fortigate.skipping_empty_ipv6_route": "Skipping Empty Ipv6 Route", "fortigate.skipping_empty_route": "Skipping Empty Route", "fortigate.ssl_vpn_portal_parsing_complete": "Ssl Vpn Portal Parsing Complete", "fortigate.start_parsing_address": "开始解析地址对象配置部分", "fortigate.start_parsing_addrgrp": "开始解析地址组配置部分", "fortigate.start_parsing_config": "开始解析配置文件", "fortigate.start_parsing_dhcp_server": "Start Parsing Dhcp Server", "fortigate.start_parsing_dns": "开始解析DNS配置部分", "fortigate.start_parsing_interface": "开始解析接口配置部分", "fortigate.start_parsing_ippool": "Start Parsing Ippool", "fortigate.start_parsing_ipsec_phase1": "Start Parsing Ipsec Phase1", "fortigate.start_parsing_ipsec_phase2": "Start Parsing Ipsec Phase2", "fortigate.start_parsing_ipv6_route": "Start Parsing Ipv6 Route", "fortigate.start_parsing_local_users": "Start Parsing Local Users", "fortigate.start_parsing_log_filter": "开始解析日志过滤器配置部分", "fortigate.start_parsing_log_fortianalyzer": "开始解析FortiAnalyzer日志配置部分", "fortigate.start_parsing_log_settings": "Start Parsing Log Settings", "fortigate.start_parsing_log_syslogd": "开始解析系统日志配置部分", "fortigate.start_parsing_policy": "Start Parsing Policy", "fortigate.start_parsing_route": "开始解析静态路由配置部分", "fortigate.start_parsing_schedule": "开始解析时间对象配置", "fortigate.start_parsing_schedule_group": "开始解析时间对象组配置部分", "fortigate.start_parsing_schedule_onetime": "开始解析一次性时间对象配置部分", "fortigate.start_parsing_schedule_recurring": "开始解析周期性时间对象配置部分", "fortigate.start_parsing_service_custom": "开始解析自定义服务配置部分", "fortigate.start_parsing_service_group": "开始解析服务组配置部分", "fortigate.start_parsing_ssl_vpn_portal": "Start Parsing Ssl Vpn Portal", "fortigate.start_parsing_ssl_vpn_settings": "Start Parsing Ssl Vpn Settings", "fortigate.start_parsing_system_global": "开始解析系统全局配置部分", "fortigate.start_parsing_system_settings": "开始解析系统设置配置部分", "fortigate.start_parsing_user_groups": "Start Parsing User Groups", "fortigate.start_parsing_user_settings": "Start Parsing User Settings", "fortigate.start_parsing_vip": "Start Parsing Vip", "fortigate.start_parsing_zone": "开始解析区域配置部分", "fortigate.syslogd_parsing_complete": "系统日志 {id} 解析完成", "fortigate.unknown_section": "未知", "fortigate.unsupported_alias": "警告: NTOS不支持'alias'属性，跳过", "fortigate.unsupported_ip_format": "警告: 不支持的IP格式: {line}", "fortigate.user_group_parsing_complete": "User Group Parsing Complete", "fortigate.valid_config": "配置文件格式正确", "fortigate.verify_error": "验证配置文件时出错: {error}", "fortigate.vip_parsing_complete": "Vip Parsing Complete", "fortigate.vlanid_out_of_range": "VLAN ID {vlanid} 超出NTOS支持的范围(1-4063)", "fortigate.warning.address_name_suspicious": "警告: 地址对象名称 '{name}' 包含可疑字符，已被过滤", "fortigate.warning.address_name_too_long": "警告: 地址对象名称 '{name}...' 过长，已截断", "fortigate.warning.addrgrp_member_suspicious": "Addrgrp Member Suspicious", "fortigate.warning.addrgrp_members_too_many": "Addrgrp Members Too Many", "fortigate.warning.addrgrp_name_suspicious": "Addrgrp Name Suspicious", "fortigate.warning.addrgrp_name_too_long": "Addrgrp Name Too Long", "fortigate.warning.addrgrp_no_members": "Addrgrp No Members", "fortigate.warning.comment_too_long": "Comment <PERSON>", "fortigate.warning.defaultgw_disable_not_supported": "警告: 接口 '{interface}' 设置了不支持的defaultgw disable选项", "fortigate.warning.description_too_long": "警告: 接口 '{name}' 的描述过长，已截断", "fortigate.warning.dns_servers_exceed_limit": "DNS服务器数量({count})超过NTOS限制({limit}个)，只转换前{limit}个", "fortigate.warning.dstaddr_list_too_large": "Dstaddr List Too Large", "fortigate.warning.dstaddr_suspicious": "Dstaddr Suspicious", "fortigate.warning.dstintf_suspicious": "Dstintf Suspicious", "fortigate.warning.fixedport_not_supported": "策略 '{policy}' 的 fixedport enable 配置不被NTOS系统支持", "fortigate.warning.groups_not_supported": "策略 '{policy}' 的用户组配置 '{groups}' 不被NTOS系统支持", "fortigate.warning.interface_name_suspicious": "警告: 接口名称 '{name}' 包含可疑字符，已被过滤", "fortigate.warning.interface_name_too_long": "警告: 接口名称 '{name}' 过长，已截断", "fortigate.warning.invalid_end_ip": "警告: 地址对象 '{name}' 的结束IP '{ip}' 格式不正确", "fortigate.warning.invalid_fqdn_format": "警告: 地址对象 '{name}' 的FQDN '{fqdn}' 格式可能不正确", "fortigate.warning.invalid_ip_format": "警告: 接口 '{name}' 的IP地址 '{ip}' 格式不正确", "fortigate.warning.invalid_policy_id": "Invalid Policy Id", "fortigate.warning.invalid_start_ip": "警告: 地址对象 '{name}' 的起始IP '{ip}' 格式不正确", "fortigate.warning.invalid_subnet_ip": "警告: 地址对象 '{name}' 的IP地址 '{ip}' 格式不正确", "fortigate.warning.line_too_long": "警告: 行 {line_num} 过长 ({length} 字符)，已截断", "fortigate.warning.no_items_parsed": "警告: 未解析到任何配置项", "fortigate.warning.non_root_vdom_detected": "警告: 检测到非root虚拟域，接口 '{interface}' 使用了不支持的虚拟域 '{vdom}'", "fortigate.warning.non_root_vdoms_summary": "警告: 检测到 {count} 个不支持的非root虚拟域: {vdoms}", "fortigate.warning.policy_comment_too_long": "Policy Comment Too Long", "fortigate.warning.policy_missing_fields": "Policy Missing Fields", "fortigate.warning.potential_command_injection": "警告: 行 {line_num} 包含可疑字符，可能存在命令注入风险: {content}", "fortigate.warning.schedule_empty": "警告: 时间对象 '{name}' 没有时间设置和星期几设置", "fortigate.warning.schedule_empty_msg": "时间对象 '{name}' 没有时间设置和星期几设置", "fortigate.warning.schedule_missing_time": "时间对象 '{name}' 缺少必要的起止时间", "fortigate.warning.schedule_missing_time_msg": "时间对象 '{name}' 缺少必要的起止时间", "fortigate.warning.schedule_name_suspicious": "时间对象名称 '{name}' 包含可疑字符，已被过滤", "fortigate.warning.schedule_name_suspicious_msg": "时间对象名称 '{name}' 包含可疑字符，已被过滤", "fortigate.warning.schedule_name_too_long": "时间对象名称 '{name}' 过长，已截断", "fortigate.warning.schedule_name_too_long_msg": "时间对象名称 '{name}' 过长，已截断", "fortigate.warning.schedule_only_uuid": "警告: 时间对象 '{name}' 只有UUID标识符，没有其他配置，将跳过默认值填充", "fortigate.warning.schedule_only_uuid_msg": "时间对象 '{name}' 只有UUID标识符，没有其他配置，将跳过默认值填充", "fortigate.warning.schedule_only_uuid_skipped": "警告: 时间对象 '{name}' 只有UUID标识符而没有其他配置，处理已跳过", "fortigate.warning.section_repeated": "警告: 配置部分 '{section}' 重复出现 {count} 次，可能存在问题", "fortigate.warning.sensitive_command_detected": "Sensitive Command Detected", "fortigate.warning.service_list_too_large": "Service List Too Large", "fortigate.warning.service_suspicious": "Service Suspicious", "fortigate.warning.skip_interface_conversion": "跳过接口 '{name}' 的转换: {reason}", "fortigate.warning.srcaddr_list_too_large": "Srcaddr List Too Large", "fortigate.warning.srcaddr_suspicious": "Srcaddr Suspicious", "fortigate.warning.srcintf_suspicious": "Srcintf Suspicious", "fortigate.warning.suspicious_file_extension": "Suspicious File Extension", "fortigate.warning.too_many_items": "警告: 解析到的配置项数量异常 ({count}个)", "fortigate.warning.unbalanced_config": "Unbalanced Config", "fortigate.warning.unbalanced_edit": "Unbalanced Edit", "fortigate.warning.unknown_action": "Unknown Action", "fortigate.warning.unsupported_allowaccess": "警告: 接口 '{interface}' 使用了不支持的访问控制方式 '{access}'，NTOS只支持ping、https和ssh", "fortigate.warning.unsupported_interface_type": "警告: 接口 '{interface}' 类型 '{type}' 不受支持，NTOS只支持物理接口和VLAN子接口", "fortigate.warning.unsupported_role": "警告: 接口 '{interface}' 使用了不支持的角色 '{role}'，NTOS只支持lan和wan角色", "fortigate.warning.users_not_supported": "策略 '{policy}' 的用户配置 '{users}' 不被NTOS系统支持", "fortigate.zone_parsing_complete": "完成区域 {name} 解析，包含 {count} 个接口", "fortigate_parser_adapter.config_file_not_found": "配置文件不存在: {file}", "fortigate_parser_adapter.config_parsed": "配置文件解析完成: {file}, 节数: {sections}", "fortigate_parser_adapter.format_validation": "fortigate parser adapter: format validation", "fortigate_parser_adapter.format_validation_failed": "fortigate parser adapter: format validation failed", "fortigate_parser_adapter.interface_extraction_failed": "fortigate parser adapter: interface extraction failed", "fortigate_parser_adapter.interfaces_extracted": "fortigate parser adapter: interfaces extracted", "fortigate_parser_adapter.legacy_parser_import_failed": "现有解析器导入失败: {error}", "fortigate_parser_adapter.legacy_parser_loaded": "现有解析器加载完成", "fortigate_parser_adapter.parsing_failed": "配置文件解析失败: {file}, 错误: {error}", "fortigate_parser_adapter.policies_extracted": "fortigate parser adapter: policies extracted", "fortigate_parser_adapter.policy_extraction_failed": "fortigate parser adapter: policy extraction failed", "fortigate_policy_stage.conversion_completed": "Fortigate策略阶段：转换已完成", "fortigate_policy_stage.conversion_exception": "Fortigate策略阶段：转换异常", "fortigate_policy_stage.custom_services_needed": "策略 {policy} 需要创建自定义服务: {services}", "fortigate_policy_stage.duplicate_service_removed": "策略 {policy} 中移除重复服务: {service}", "fortigate_policy_stage.initialized": "Fortigate策略阶段已初始化", "fortigate_policy_stage.nat_rule_conversion_failed": "Fortigate策略阶段：NAT规则转换失败", "fortigate_policy_stage.no_parsed_config": "Fortigate策略阶段：缺少解析的配置", "fortigate_policy_stage.policy_conversion_failed": "Fortigate策略阶段：策略转换失败", "fortigate_policy_stage.prepare_data_failed": "Fortigate策略阶段：数据准备失败", "fortigate_policy_stage.processing_completed": "Fortigate策略阶段：处理已完成", "fortigate_policy_stage.security_policy_conversion_failed": "Fortigate策略阶段：安全策略转换失败", "fortigate_policy_stage.service_mapping_failed": "策略 {policy} 服务映射失败: {error}", "fortigate_policy_stage.services_deduplicated": "策略 {policy} 服务去重: {original} -> {unique} (移除 {duplicates} 个重复)", "fortigate_policy_stage.start_processing": "Fortigate策略阶段：开始处理", "fortigate_policy_stage.validation_exception": "Fortigate策略阶段：验证异常", "fortigate_policy_stage.validation_failed": "Fortigate策略阶段：验证失败", "fortigate_policy_stage.xml_integration_data_prepared": "Fortigate策略阶段：XML集成数据已准备", "fortigate_policy_stage.xml_integration_preparation_failed": "Fortigate策略阶段：XML集成准备失败", "fortigate_policy_stage.xml_preparation_failed": "Fortigate策略阶段：XML准备失败", "fortigate_service_mapper.custom_service_created": "自定义服务已创建: {service}，配置: {config}", "fortigate_service_mapper.custom_services_reset": "自定义服务记录已重置", "fortigate_service_mapper.empty_service_name": "服务名称为空，使用默认服务 'any'", "fortigate_service_mapper.service_mapped": "FortiGate服务映射成功: {fortigate} -> {ntos}", "fortigate_service_mapper.service_needs_custom_creation": "FortiGate服务 {service} 无预定义映射，需要创建自定义服务", "fortigate_strategy.chunk.divided_into_chunks": "分为 {chunks} 个块，每块最多 {size} 行", "fortigate_strategy.chunk.processing_chunk": "处理第 {current}/{total} 块", "fortigate_strategy.chunk.processing_config": "分块处理配置，总行数: {total}", "fortigate_strategy.chunk.processing_failed": "处理第 {chunk} 块失败: {error}", "fortigate_strategy.conversion_data_prepared": "fortigate strategy: conversion data prepared", "fortigate_strategy.interface.validation_exception": "验证接口名称 '{interface}' 时发生异常: {error}", "fortigate_strategy.interface.yang_invalid_format": "接口名称 '{interface}' 格式不符合NTOS标准", "fortigate_strategy.interface_type_converted": "策略验证: 接口 '{interface}' 类型已转换 {original} -> {converted}", "fortigate_strategy.memory.cleanup_complete": "内存清理完成，节省 {saved:.1f}MB", "fortigate_strategy.memory.high_usage": "内存使用过高 ({memory:.1f}MB)，进行深度清理", "fortigate_strategy.memory.optimization": "内存优化 [{stage}]: {memory:.1f}MB", "fortigate_strategy.memory.optimization_failed": "内存优化失败: {error}", "fortigate_strategy.missing_nat_namespace": "fortigate strategy: missing nat namespace", "fortigate_strategy.nat_rule_generation_failed": "FortiGate策略NAT规则生成失败: 策略ID={policy_id}, 错误={error}", "fortigate_strategy.no_parsed_config": "fortigate strategy: no parsed config", "fortigate_strategy.no_xml_content": "fortigate strategy: no xml content", "fortigate_strategy.policies_validated": "fortigate strategy: policies validated", "fortigate_strategy.policy.data_preparation": "策略转换数据准备：找到 {count} 个策略", "fortigate_strategy.policy.error_stack": "错误堆栈: {stack}", "fortigate_strategy.policy.generation_error": "策略生成详细错误: {error}", "fortigate_strategy.policy.generation_start": "新架构策略处理开始 - 策略ID: {policy_id}", "fortigate_strategy.policy.interface_mapping_content": "接口映射内容: {mapping}", "fortigate_strategy.policy.interface_mapping_empty": "interface_processing_result中final_mapping为空", "fortigate_strategy.policy.interface_mapping_from_file": "从映射文件获取接口映射: {count} 个映射", "fortigate_strategy.policy.interface_mapping_from_mappings": "从interface_mappings获取接口映射: {count} 个映射", "fortigate_strategy.policy.interface_mapping_from_result": "从interface_processing_result获取接口映射: {count} 个映射", "fortigate_strategy.policy.mapping_file_read_failed": "从映射文件读取接口映射失败: {error}", "fortigate_strategy.policy.no_interface_mapping": "警告：无法获取接口映射，策略中的接口名称将不会被映射", "fortigate_strategy.policy.validation_exception": "策略 {policy_id} ({name}): 接口映射验证异常: {error}", "fortigate_strategy.policy.validation_failed": "策略 {policy_id} ({name}): 接口映射验证失败，跳过处理", "fortigate_strategy.policy.validation_passed": "策略 {policy_id} ({name}): 接口映射验证通过", "fortigate_strategy.policy.validation_start": "开始验证策略 {policy_id} ({name}) 的接口映射", "fortigate_strategy.security_policy_generation_failed": "fortigate strategy: security policy generation failed", "fortigate_strategy.validation.all_passed": "策略 {policy_id} 所有接口/区域映射验证通过", "fortigate_strategy.validation.available_mappings": "可用的接口映射: {mappings}", "fortigate_strategy.validation.check_destination": "检查目标接口: {interfaces}", "fortigate_strategy.validation.check_source": "检查源接口: {interfaces}", "fortigate_strategy.validation.destination_found": "目标接口 '{interface}' 在接口映射中找到", "fortigate_strategy.validation.destination_not_found": "策略 {policy_id}: 目标接口/区域 '{interface}' 在映射表中未找到且无法解析为区域", "fortigate_strategy.validation.destination_zone_resolved": "目标区域 '{zone}' 解析为接口: {interfaces}", "fortigate_strategy.validation.empty_mapping": "策略 {policy_id}: 接口映射表为空", "fortigate_strategy.validation.mapping_keys": "可用的接口映射键: {keys}", "fortigate_strategy.validation.source_found": "源接口 '{interface}' 在接口映射中找到", "fortigate_strategy.validation.source_not_found": "策略 {policy_id}: 源接口/区域 '{interface}' 在映射表中未找到且无法解析为区域", "fortigate_strategy.validation.source_zone_resolved": "源区域 '{zone}' 解析为接口: {interfaces}", "fortigate_strategy.validation.strict_start": "FortigateStrategy严格验证策略 {policy_id} 的接口映射（支持区域解析）", "fortigate_strategy.validation.validate_destination": "验证目标接口/区域: '{interface}'", "fortigate_strategy.validation.validate_source": "验证源接口/区域: '{interface}'", "fortigate_strategy.zone.interface_mapping": "区域接口映射: {from} -> {to} (YANG验证通过)", "fortigate_strategy.zone.interface_no_mapping": "区域 '{zone}' 中的接口 '{interface}' 未找到映射", "fortigate_strategy.zone.interface_yang_invalid": "接口名称 '{interface}' 不符合YANG模型规范", "fortigate_strategy.zone.invalid_characters": "区域名称 '{zone}' 包含非法字符", "fortigate_strategy.zone.name_too_long": "区域名称 '{zone}' 过长（超过32字符）", "fortigate_strategy.zone.no_config": "无法获取区域配置，无法解析区域 '{zone}'", "fortigate_strategy.zone.no_context": "无法获取数据上下文，无法解析区域 '{zone}'", "fortigate_strategy.zone.no_interfaces": "区域 '{zone}' 不包含任何接口", "fortigate_strategy.zone.not_found": "未找到区域 '{zone}' 的配置", "fortigate_strategy.zone.resolution_exception": "解析区域 '{zone}' 时发生异常: {error}", "fortigate_strategy.zone.resolved_interfaces": "区域 '{zone}' 解析为接口: {interfaces}", "fortigate_strategy.zone.standard_zone": "区域 '{zone}' 是标准区域名称", "fortigate_strategy.zone.validation_exception": "验证区域 '{zone}' 时发生异常: {error}", "fortigate_strategy.zone.yang_validation_passed": "区域 '{zone}' YANG验证通过", "generator_interface.initialized": "生成器接口已初始化", "generator_interface.invalid_data_type": "generator interface: invalid data type", "generator_registry.builtin_registration_failed": "生成器注册表: builtin registration failed", "generator_registry.cache_cleared": "生成器注册表: cache cleared", "generator_registry.generator_created": "生成器注册表: generator created", "generator_registry.generator_not_found": "生成器注册表: generator not found", "generator_registry.generator_registered": "生成器注册表: generator registered", "generator_registry.initialized": "生成器注册表已初始化", "gmail.com": "[待翻译] gmail.com", "google.com": "[待翻译] google.com", "help.cli_file": "配置文件路径", "help.debug_log": "调试日志文件路径", "help.encrypt_output": "加密输出文件的路径", "help.language": "指定使用的语言，默认为zh-CN", "help.log_dir": "help: log dir", "help.log_directory": "日志目录，如果提供，将在此目录下创建日志文件", "help.mapping_file": "接口映射文件路径", "help.model": "设备型号", "help.output_file": "输出文件路径", "help.output_json_file": "输出JSON文件路径", "help.print_result": "打印转换结果", "help.quiet_mode": "help: quiet mode", "help.supported_vendors": "厂商标识，当前支持: {vendors}", "help.user_log": "用户日志文件路径", "help.version": "设备版本", "i18n:compress.all_attempts_failed": "compress: all attempts failed", "i18n:compress.available_symbols": "compress: available symbols", "i18n:compress.backed_up_existing_file": "compress: backed up existing file", "i18n:compress.backup_startup_created": "compress: backup startup created", "i18n:compress.cannot_delete_file": "compress: cannot delete file", "i18n:compress.chdir_for_preload": "compress: chdir for preload", "i18n:compress.checking_lib_file": "compress: checking lib file", "i18n:compress.cleaned_temp_lib_dir": "compress: cleaned temp lib dir", "i18n:compress.cleanup_temp_dir": "compress: cleanup temp dir", "i18n:compress.container_dlopen_error": "compress: container dlopen error", "i18n:compress.container_dlopen_failed": "compress: container dlopen failed", "i18n:compress.container_lib_loaded_via_dlopen": "compress: container lib loaded via dlopen", "i18n:compress.container_library_path": "compress: container library path", "i18n:compress.container_load_failed": "compress: container load failed", "i18n:compress.container_load_library_failed": "compress: container load library failed", "i18n:compress.container_load_success_method1": "compress: container load success method1", "i18n:compress.container_minimal_preload": "compress: container minimal preload", "i18n:compress.container_special_handling": "compress: container special handling", "i18n:compress.created_system_lib_dir": "compress: created system lib dir", "i18n:compress.created_system_symlink": "compress: created system symlink", "i18n:compress.critical_lib_missing": "compress: critical lib missing", "i18n:compress.current_working_dir": "compress: current working dir", "i18n:compress.decrypted_file_saved": "compress: decrypted file saved", "i18n:compress.decrypting_file": "compress: decrypting file", "i18n:compress.decryption_complete": "compress: decryption complete", "i18n:compress.deleted_existing_output": "compress: deleted existing output", "i18n:compress.deleted_file": "compress: deleted file", "i18n:compress.dependency_already_in_memory": "compress: dependency already in memory", "i18n:compress.dependency_already_loaded": "compress: dependency already loaded", "i18n:compress.dependency_loaded_successfully": "compress: dependency loaded successfully", "i18n:compress.encryption_success": "compress: encryption success", "i18n:compress.error.all_load_methods_failed": "compress: error: all load methods failed", "i18n:compress.error.call_real_api_failed": "compress: error: call real api failed", "i18n:compress.error.cannot_delete_output": "compress: error: cannot delete output", "i18n:compress.error.checking_lib_permissions": "compress: error: checking lib permissions", "i18n:compress.error.checking_permissions": "compress: error: checking permissions", "i18n:compress.error.cleanup_temp_dir": "compress: error: cleanup temp dir", "i18n:compress.error.container_detection_failed": "compress: error: container detection failed", "i18n:compress.error.create_config_tar_gz": "compress: error: create config tar gz", "i18n:compress.error.create_tar_file": "compress: error: create tar file", "i18n:compress.error.creating_symlinks": "compress: error: creating symlinks", "i18n:compress.error.creating_system_lib_dir": "compress: error: creating system lib dir", "i18n:compress.error.critical_lib_missing": "compress: error: critical lib missing", "i18n:compress.error.decryption_failed": "compress: error: decryption failed", "i18n:compress.error.during_decryption": "compress: error: during decryption", "i18n:compress.error.encrypted_file_invalid": "compress: error: encrypted file invalid", "i18n:compress.error.encrypted_file_not_found": "compress: error: encrypted file not found", "i18n:compress.error.encryption_abort": "compress: error: encryption abort", "i18n:compress.error.encryption_failed": "compress: error: encryption failed", "i18n:compress.error.encryption_process": "compress: error: encryption process", "i18n:compress.error.global_cleanup_failed": "compress: error: global cleanup failed", "i18n:compress.error.input_dir_not_exists": "compress: error: input dir not exists", "i18n:compress.error.input_file_not_exists": "compress: error: input file not exists", "i18n:compress.error.lib_not_exists": "compress: error: lib not exists", "i18n:compress.error.lib_not_file": "compress: error: lib not file", "i18n:compress.error.list_symbols_failed": "compress: error: list symbols failed", "i18n:compress.error.load_lib_failed": "compress: error: load lib failed", "i18n:compress.error.load_lib_mode_failed": "compress: error: load lib mode failed", "i18n:compress.error.load_main_library_failed": "compress: error: load main library failed", "i18n:compress.error.main_lib_not_exists": "compress: error: main lib not exists", "i18n:compress.error.missing_critical_libs": "compress: error: missing critical libs", "i18n:compress.error.missing_main_function": "compress: error: missing main function", "i18n:compress.error.output_file_invalid": "compress: error: output file invalid", "i18n:compress.error.preload_libs_failed": "compress: error: preload libs failed", "i18n:compress.error.preparing_container_loading": "compress: error: preparing container loading", "i18n:compress.error.preparing_library_files": "compress: error: preparing library files", "i18n:compress.error.process_tar_gz_failed": "compress: error: process tar gz failed", "i18n:compress.error.running_ldconfig": "compress: error: running ldconfig", "i18n:compress.error.setting_ld_library_path": "compress: error: setting ld library path", "i18n:compress.error.tar_command_failed": "compress: error: tar command failed", "i18n:compress.error.tar_gz_not_exists": "compress: error: tar gz not exists", "i18n:compress.error.unknown_action": "compress: error: unknown action", "i18n:compress.error.version_file_invalid": "compress: error: version file invalid", "i18n:compress.error.version_file_not_exists": "compress: error: version file not exists", "i18n:compress.error.version_file_not_found": "compress: error: version file not found", "i18n:compress.fixing_lib_permissions": "compress: fixing lib permissions", "i18n:compress.found_dependency": "compress: found dependency", "i18n:compress.global_cleanup_complete": "compress: global cleanup complete", "i18n:compress.init_complete": "compress: init complete", "i18n:compress.init_env": "compress: init env", "i18n:compress.ld_library_path_set": "compress: ld library path set", "i18n:compress.ld_preload_set": "compress: ld preload set", "i18n:compress.ldconfig_executed": "compress: ldconfig executed", "i18n:compress.ldconfig_executed_after_preload": "compress: ldconfig executed after preload", "i18n:compress.ldconfig_refreshed": "compress: ldconfig refreshed", "i18n:compress.lib_file_permissions": "compress: lib file permissions", "i18n:compress.lib_loaded_successfully": "compress: lib loaded successfully", "i18n:compress.lib_not_found": "compress: lib not found", "i18n:compress.listing_available_symbols": "compress: listing available symbols", "i18n:compress.load_main_lib_success": "compress: load main lib success", "i18n:compress.load_method_failed": "compress: load method failed", "i18n:compress.load_with_relative_path_failed": "compress: load with relative path failed", "i18n:compress.loading_main_library": "compress: loading main library", "i18n:compress.loading_with_absolute_path": "compress: loading with absolute path", "i18n:compress.main_function_verified": "compress: main function verified", "i18n:compress.main_lib_loaded_successfully": "compress: main lib loaded successfully", "i18n:compress.method1_failed": "compress: method1 failed", "i18n:compress.preloaded_lib": "compress: preloaded lib", "i18n:compress.preparing_container_loading": "compress: preparing container loading", "i18n:compress.preparing_library_files": "compress: preparing library files", "i18n:compress.ran_ldconfig": "compress: ran ldconfig", "i18n:compress.restore_dir_after_preload": "compress: restore dir after preload", "i18n:compress.retry_load_main_lib": "compress: retry load main lib", "i18n:compress.setting_ld_preload": "compress: setting ld preload", "i18n:compress.symlink_already_exists": "compress: symlink already exists", "i18n:compress.tar_gz_processed": "compress: tar gz processed", "i18n:compress.traditional_load_completely_failed": "compress: traditional load completely failed", "i18n:compress.trying_container_method1_rpath": "compress: trying container method1 rpath", "i18n:compress.trying_load_method": "compress: trying load method", "i18n:compress.trying_relative_path": "compress: trying relative path", "i18n:compress.trying_to_load_lib": "compress: trying to load lib", "i18n:compress.using_crypto_dir": "compress: using crypto dir", "i18n:compress.using_crypto_lib": "compress: using crypto lib", "i18n:compress.using_crypto_libs_path": "compress: using crypto libs path", "i18n:compress.using_lib_dir": "compress: using lib dir", "i18n:compress.using_version_file": "compress: using version file", "i18n:debug.added_valid_service_object": "调试: added valid service object", "i18n:debug.adding_service_objects_to_ntos_generator": "调试: adding service objects to ntos generator", "i18n:debug.address_object_detail": "调试: address object detail", "i18n:debug.checking_service_groups_data": "调试: checking service groups data", "i18n:debug.complex_tcp_ports_using_destination": "调试: complex tcp ports using destination", "i18n:debug.complex_udp_ports_using_destination": "调试: complex udp ports using destination", "i18n:debug.detected_full_port_range": "调试: detected full port range", "i18n:debug.final_service_set_in_xml": "调试: final service set in xml", "i18n:debug.final_service_sets_count": "调试: final service sets count", "i18n:debug.fixed_legacy_generator_added_icmp": "调试: fixed legacy generator added icmp", "i18n:debug.fixed_legacy_generator_added_ip_protocol": "调试: fixed legacy generator added ip protocol", "i18n:debug.fixed_legacy_generator_added_tcp_ports": "调试: fixed legacy generator added tcp ports", "i18n:debug.fixed_legacy_generator_added_udp_ports": "调试: fixed legacy generator added udp ports", "i18n:debug.fixed_legacy_generator_final_xml_service_sets_count": "调试: fixed legacy generator final xml service sets count", "i18n:debug.fixed_legacy_generator_keeping_service_obj_node": "调试: fixed legacy generator keeping service obj node", "i18n:debug.fixed_legacy_generator_processing_service_objects": "调试: fixed legacy generator processing service objects", "i18n:debug.fixed_legacy_generator_service_added_success": "调试: fixed legacy generator service added success", "i18n:debug.fixed_legacy_generator_service_missing_ports": "调试: fixed legacy generator service missing ports", "i18n:debug.fixed_legacy_generator_service_object_detail": "调试: fixed legacy generator service object detail", "i18n:debug.fixed_legacy_generator_service_port_config": "调试: fixed legacy generator service port config", "i18n:debug.fixed_legacy_generator_service_processing_failed": "调试: fixed legacy generator service processing failed", "i18n:debug.fixed_legacy_generator_service_protocol": "调试: fixed legacy generator service protocol", "i18n:debug.fixed_legacy_generator_start_processing_service": "调试: fixed legacy generator start processing service", "i18n:debug.fixed_legacy_generator_xml_service_set": "调试: fixed legacy generator xml service set", "i18n:debug.fixed_legacy_generator_xml_service_sets_count": "调试: fixed legacy generator xml service sets count", "i18n:debug.fortigate_av_profile_detected": "调试: fortigate av profile detected", "i18n:debug.fortigate_ips_sensor_detected": "调试: fortigate ips sensor detected", "i18n:debug.improved_legacy_generator_added_description": "调试: improved legacy generator added description", "i18n:debug.improved_legacy_generator_added_icmp": "调试: improved legacy generator added icmp", "i18n:debug.improved_legacy_generator_added_ip_protocol": "调试: improved legacy generator added ip protocol", "i18n:debug.improved_legacy_generator_complex_port_range_conversion": "调试: improved legacy generator complex port range conversion", "i18n:debug.improved_legacy_generator_final_xml_service_sets_count": "调试: improved legacy generator final xml service sets count", "i18n:debug.improved_legacy_generator_keeping_service_obj_node": "调试: improved legacy generator keeping service obj node", "i18n:debug.improved_legacy_generator_port_range_conversion": "调试: improved legacy generator port range conversion", "i18n:debug.improved_legacy_generator_port_range_normalization": "调试: improved legacy generator port range normalization", "i18n:debug.improved_legacy_generator_service_processed_success": "调试: improved legacy generator service processed success", "i18n:debug.improved_legacy_generator_xml_service_set": "调试: improved legacy generator xml service set", "i18n:debug.improved_legacy_generator_xml_service_sets_count": "调试: improved legacy generator xml service sets count", "i18n:debug.legacy_generator_processing_service_objects": "调试: legacy generator processing service objects", "i18n:debug.legacy_generator_service_object_check": "调试: legacy generator service object check", "i18n:debug.legacy_generator_service_object_detail": "调试: legacy generator service object detail", "i18n:debug.legacy_generator_service_object_details": "调试: legacy generator service object details", "i18n:debug.legacy_generator_service_objects_count": "调试: legacy generator service objects count", "i18n:debug.legacy_generator_set_service_mappings": "调试: legacy generator set service mappings", "i18n:debug.legacy_policy_rule_converted_successfully": "调试: legacy policy rule converted successfully", "i18n:debug.loaded_fortigate_service_mapping": "调试: loaded fortigate service mapping", "i18n:debug.loaded_ntos_builtin_services": "调试: loaded ntos builtin services", "i18n:debug.loaded_predefined_service_mapping": "调试: loaded predefined service mapping", "i18n:debug.multiline_content_end": "调试: 检测到多行内容结束 - 类型: {type}, 行号: {line}", "i18n:debug.multiline_content_start": "调试: 检测到多行内容开始 - 类型: {type}, 行号: {line}", "i18n:debug.multiple_tcp_ports_using_first": "调试: multiple tcp ports using first", "i18n:debug.multiple_udp_ports_using_first": "调试: multiple udp ports using first", "i18n:debug.ntos_generator_set_service_mappings": "调试: ntos generator set service mappings", "i18n:debug.policy_service_mapping_applied": "调试: policy service mapping applied", "i18n:debug.port_preserve_detected": "调试: port preserve detected", "i18n:debug.predefined_service_mapping_found": "调试: predefined service mapping found", "i18n:debug.preparing_service_stats_calculation": "调试: preparing service stats calculation", "i18n:debug.processing_address_objects_count": "调试: processing address objects count", "i18n:debug.processing_service_objects_count": "调试: processing service objects count", "i18n:debug.service_auto_infer_protocol": "调试: service auto infer protocol", "i18n:debug.service_converted_count": "调试: service converted count", "i18n:debug.service_failed_count": "调试: service failed count", "i18n:debug.service_group_member_found_directly": "调试: service group member found directly", "i18n:debug.service_group_member_found_via_mapping": "调试: service group member found via mapping", "i18n:debug.service_group_member_using_mapped_name": "调试: service group member using mapped name", "i18n:debug.service_group_member_validated_via_mapping": "调试: service group member validated via mapping", "i18n:debug.service_group_processor_no_mappings": "调试: service group processor no mappings", "i18n:debug.service_group_processor_received_mappings": "调试: service group processor received mappings", "i18n:debug.service_groups_data_empty": "调试: service groups data empty", "i18n:debug.service_groups_data_length": "调试: service groups data length", "i18n:debug.service_groups_data_sample": "调试: service groups data sample", "i18n:debug.service_mapping_relationship": "调试: service mapping relationship", "i18n:debug.service_mapping_relationships_found": "调试: service mapping relationships found", "i18n:debug.service_mapping_relationships_saved": "调试: service mapping relationships saved", "i18n:debug.service_object_details": "调试: service object details", "i18n:debug.service_objects_processing_details": "调试: service objects processing details", "i18n:debug.service_objects_stats_calculated": "调试: service objects stats calculated", "i18n:debug.service_protocol_auto_corrected_tcp": "调试: service protocol auto corrected tcp", "i18n:debug.service_protocol_auto_corrected_tcp_udp": "调试: service protocol auto corrected tcp udp", "i18n:debug.service_protocol_auto_corrected_udp": "调试: service protocol auto corrected udp", "i18n:debug.service_protocol_correction": "调试: service protocol correction", "i18n:debug.service_set_in_xml": "调试: service set in xml", "i18n:debug.service_sets_count_after_adding": "调试: service sets count after adding", "i18n:debug.service_skipped_count": "调试: service skipped count", "i18n:debug.set_translator": "调试: set translator", "i18n:debug.setting_service_objects_to_legacy_generator": "调试: setting service objects to legacy generator", "i18n:debug.start_adding_service_objects_to_xml": "调试: start adding service objects to xml", "i18n:debug.using_converted_service_objects_for_validation": "调试: using converted service objects for validation", "i18n:debug.using_original_service_objects_for_validation": "调试: using original service objects for validation", "i18n:debug.using_service_mapping_for_policies": "调试: using service mapping for policies", "i18n:debug.valid_service_objects_count": "调试: valid service objects count", "i18n:error.address_group_conversion_failed": "错误: address group conversion failed", "i18n:error.address_object_conversion_failed": "错误: address object conversion failed", "i18n:error.all_physical_interfaces_unmapped": "错误: all physical interfaces unmapped", "i18n:error.config_file_read_failed": "错误: config file read failed", "i18n:error.conversion_failed": "错误: conversion failed", "i18n:error.copy_file_failed": "错误: copy file failed", "i18n:error.create_dir_failed": "错误: create dir failed", "i18n:error.create_user_log_dir_failed": "错误: create user log dir failed", "i18n:error.duplicate_policy_id": "错误: duplicate policy id", "i18n:error.duplicate_service_object": "错误: duplicate service object", "i18n:error.edit_without_config": "错误: edit without config", "i18n:error.encrypt_error": "错误: encrypt error", "i18n:error.encryption_error": "错误: encryption error", "i18n:error.encryption_failed": "错误: encryption failed", "i18n:error.end_without_config": "错误: end without config", "i18n:error.enhanced_policy_processing_failed": "增强策略处理失败", "i18n:error.extract_interfaces_failed": "错误: extract interfaces failed", "i18n:error.extraction_failed": "错误: extraction failed", "i18n:error.failed_to_integrate_nat_rules": "错误: failed to integrate nat rules", "i18n:error.failed_to_integrate_security_policies": "错误: failed to integrate security policies", "i18n:error.failed_to_load_xml": "错误: failed to load xml", "i18n:error.file_empty": "错误: file empty", "i18n:error.file_not_exists": "错误: file not exists", "i18n:error.file_not_exists_path": "错误: file not exists path", "i18n:error.file_read_failed": "错误: file read failed", "i18n:error.fix_xml_namespaces_failed": "错误: fix xml namespaces failed", "i18n:error.flush_log_handler_failed": "错误: flush log handler failed", "i18n:error.init_translator_failed": "错误: init translator failed", "i18n:error.interface_mapping_validation_failed": "错误: interface mapping validation failed", "i18n:error.interface_model_validation_failed": "错误: interface model validation failed", "i18n:error.invalid_fortigate_config": "错误: invalid fortigate config", "i18n:error.invalid_fortigate_config_structure": "错误: invalid fortigate config structure", "i18n:error.invalid_interface_mapping": "错误: invalid interface mapping", "i18n:error.invalid_ip_address": "错误: invalid ip address", "i18n:error.invalid_subnet_mask": "错误: invalid subnet mask", "i18n:error.ipv6_static_route_processing_error": "错误: ipv6 static route processing error", "i18n:error.json_print_failed": "错误: json print failed", "i18n:error.legacy_policy_rule_conversion_failed": "错误: legacy policy rule conversion failed", "i18n:error.mapping_file_load_failed": "错误: mapping file load failed", "i18n:error.mask_conversion_failed": "错误: mask conversion failed", "i18n:error.missing_config_version": "错误: missing config version", "i18n:error.missing_interface_config": "错误: missing interface config", "i18n:error.missing_parameters": "错误: missing parameters", "i18n:error.next_without_edit": "错误: next without edit", "i18n:error.ntos_generation_failed": "错误: ntos generation failed", "i18n:error.output_file_invalid": "错误: output file invalid", "i18n:error.parsed_data_not_dict": "错误: parsed data not dict", "i18n:error.parsing_config_failed": "错误: parsing config failed", "i18n:error.parsing_failed_with_error": "错误: parsing failed with error", "i18n:error.policy_rule_conversion_error": "错误: policy rule conversion error", "i18n:error.policy_rule_conversion_failed": "错误: policy rule conversion failed", "i18n:error.possible_unbalanced_nesting": "错误: possible unbalanced nesting", "i18n:error.read_json_file_failed": "错误: read json file failed", "i18n:error.read_text_file_failed": "错误: read text file failed", "i18n:error.semantic_verification_failed": "错误: semantic verification failed", "i18n:error.service_group_processing_error": "错误: service group processing error", "i18n:error.service_object_conversion_failed": "错误: service object conversion failed", "i18n:error.static_route_processing_error": "错误: static route processing error", "i18n:error.syntax_errors_detected": "错误: syntax errors detected", "i18n:error.template_file_not_exists": "错误: template file not exists", "i18n:error.template_not_found": "错误: template not found", "i18n:error.time_range_conversion_failed": "错误: time range conversion failed", "i18n:error.transparent_mode_processing_failed": "错误: transparent mode processing failed", "i18n:error.unable_to_detect_version": "错误: unable to detect version", "i18n:error.unclosed_config_blocks": "错误: unclosed config blocks", "i18n:error.unclosed_edit_blocks": "错误: unclosed edit blocks", "i18n:error.unhandled_exception": "错误: unhandled exception", "i18n:error.unknown_error": "错误: unknown error", "i18n:error.unsupported_fortigate_version": "错误: unsupported fortigate version", "i18n:error.vendor_not_supported": "错误: vendor not supported", "i18n:error.verification_failed": "错误: verification failed", "i18n:error.vlan_extraction_failed": "错误: vlan extraction failed", "i18n:error.write_interface_info_failed": "错误: write interface info failed", "i18n:error.write_json_file_failed": "错误: write json file failed", "i18n:error.write_text_file_failed": "错误: write text file failed", "i18n:error.xml_validation_error": "错误: xml validation error", "i18n:error.xml_validation_failed": "错误: xml validation failed", "i18n:format.data_object_placeholder": "format: data object placeholder", "i18n:format.interface_placeholder": "format: interface placeholder", "i18n:format.json_data_placeholder": "format: json data placeholder", "i18n:format.object_placeholder": "format: object placeholder", "i18n:fortigate.address_interface_unsupported": "fortigate: address interface unsupported", "i18n:fortigate.address_type_support": "fortigate: address type support", "i18n:fortigate.address_type_unsupported": "fortigate: address type unsupported", "i18n:fortigate.warning.fixedport_not_supported": "fortigate: warning: fixedport not supported", "i18n:fortigate.warning.groups_not_supported": "fortigate: warning: groups not supported", "i18n:fortigate.warning.unsupported_role": "fortigate: warning: unsupported role", "i18n:fortigate.warning.users_not_supported": "fortigate: warning: users not supported", "i18n:info.adapting_service_processor_result_format": "信息: adapting service processor result format", "i18n:info.add_interface_with_json": "信息: add interface with json", "i18n:info.adding_dns_config": "信息: adding dns config", "i18n:info.adding_static_routes": "信息: adding static routes", "i18n:info.adding_time_ranges": "信息: adding time ranges", "i18n:info.address_group_converted": "信息: address group converted", "i18n:info.address_groups_processed": "信息: address groups processed", "i18n:info.address_groups_processing_complete": "信息: address groups processing complete", "i18n:info.address_object_converted": "信息: address object converted", "i18n:info.address_objects_processed": "信息: address objects processed", "i18n:info.address_objects_processing_complete": "信息: address objects processing complete", "i18n:info.address_type_detected_from_attributes": "信息: address type detected from attributes", "i18n:info.address_type_not_specified": "信息: address type not specified", "i18n:info.address_type_specified": "信息: address type specified", "i18n:info.all_predefined_services_detected": "信息: all predefined services detected", "i18n:info.all_zone_interfaces_mapped": "信息: all zone interfaces mapped", "i18n:info.blackhole_route_default_destination": "信息: blackhole route default destination", "i18n:info.checking_compatibility": "信息: checking compatibility", "i18n:info.checking_parent_dir": "信息: checking parent dir", "i18n:info.cleanup_temp_dir": "信息: cleanup temp dir", "i18n:info.config_dir_found": "信息: config dir found", "i18n:info.config_file_read_success": "信息: config file read success", "i18n:info.conversion_succeeded": "信息: conversion succeeded", "i18n:info.converting_single_interface_to_list": "信息: converting single interface to list", "i18n:info.converting_zones_dict_to_list": "信息: converting zones dict to list", "i18n:info.copy_config_dir": "信息: copy config dir", "i18n:info.copy_xml_to_running": "信息: copy xml to running", "i18n:info.copy_xml_to_startup": "信息: copy xml to startup", "i18n:info.create_directory": "信息: create directory", "i18n:info.create_new_config_dir": "信息: create new config dir", "i18n:info.create_output_dir": "信息: create output dir", "i18n:info.create_user_log_dir": "信息: create user log dir", "i18n:info.current_working_dir": "信息: current working dir", "i18n:info.delete_existing_output": "信息: delete existing output", "i18n:info.detected_container_environment": "信息: detected container environment", "i18n:info.detected_dev_environment": "信息: detected dev environment", "i18n:info.detected_fortigate_version": "信息: detected fortigate version", "i18n:info.directory_created": "信息: directory created", "i18n:info.dmz_interface_set_required": "信息: dmz interface set required", "i18n:info.encrypt_start": "信息: encrypt start", "i18n:info.encrypted_file_generated": "信息: encrypted file generated", "i18n:info.encryption_success": "信息: encryption success", "i18n:info.engine_debug_log_created": "信息: engine debug log created", "i18n:info.engine_user_log_created": "信息: engine user log created", "i18n:info.enhanced_policy_processing_complete": "增强策略处理完成，安全策略: {security_policies}, NAT规则: {nat_rules}", "i18n:info.error_report_saved": "信息: error report saved", "i18n:info.extract_interfaces_from_config": "信息: extract interfaces from config", "i18n:info.extracting_interfaces_from_config": "信息: extracting interfaces from config", "i18n:info.extracting_zones": "信息: extracting zones", "i18n:info.extraction_start": "信息: extraction start", "i18n:info.extraction_success": "信息: extraction success", "i18n:info.fallback_to_legacy": "信息：回退到传统架构", "i18n:info.falling_back_to_legacy_service_processing": "信息: falling back to legacy service processing", "i18n:info.file_copied": "信息: file copied", "i18n:info.final_interface_mapping_file_saved": "信息: final interface mapping file saved", "i18n:info.fixing_xml_namespaces": "信息: fixing xml namespaces", "i18n:info.fortinet_subinterfaces_processed": "信息: fortinet subinterfaces processed", "i18n:info.found_template_file": "信息: found template file", "i18n:info.found_template_files": "信息: found template files", "i18n:info.getting_template_path": "信息: getting template path", "i18n:info.input_file": "信息: input file", "i18n:info.interface_info_written": "信息: interface info written", "i18n:info.interface_mapped": "信息: interface mapped", "i18n:info.interface_mapping_applied": "信息: interface mapping applied", "i18n:info.interface_mapping_complete": "信息: interface mapping complete", "i18n:info.interface_mapping_table": "信息: interface mapping table", "i18n:info.interface_mapping_validation_passed": "信息: interface mapping validation passed", "i18n:info.interface_name_quotes_removed": "信息: interface name quotes removed", "i18n:info.interface_zone_assigned": "信息: interface zone assigned", "i18n:info.interfaces_extracted": "信息: interfaces extracted", "i18n:info.json_file_saved": "信息: json file saved", "i18n:info.language_set": "信息: language set", "i18n:info.legacy_policy_rules_processing_complete": "信息: legacy policy rules processing complete", "i18n:info.min_version_requirement": "信息: min version requirement", "i18n:info.nat_mode_detected": "信息: nat mode detected", "i18n:info.nat_rules_added_to_xml": "信息: nat rules added to xml", "i18n:info.nat_rules_processing_complete": "信息: nat rules processing complete", "i18n:info.no_address_groups_found": "信息: no address groups found", "i18n:info.no_address_objects_found": "信息: no address objects found", "i18n:info.no_compatibility_issues": "信息: no compatibility issues", "i18n:info.no_interfaces_or_mappings_to_validate": "信息: no interfaces or mappings to validate", "i18n:info.no_nat_rules_found": "信息: no nat rules found", "i18n:info.no_nat_rules_to_process": "信息: no nat rules to process", "i18n:info.no_policy_rules_found": "信息: no policy rules found", "i18n:info.no_service_groups_found": "信息: no service groups found", "i18n:info.no_service_objects_found": "信息: no service objects found", "i18n:info.no_static_routes_found": "信息: no static routes found", "i18n:info.no_system_settings_nat_mode": "信息: no system settings nat mode", "i18n:info.no_time_ranges_found": "信息: no time ranges found", "i18n:info.output_dir_created": "信息: output dir created", "i18n:info.output_file": "信息: output file", "i18n:info.parsing_config_complete": "信息: parsing config complete", "i18n:info.parsing_config_start": "信息: parsing config start", "i18n:info.policy_rule_converted_success": "信息: policy rule converted success", "i18n:info.policy_rules_processed": "信息: policy rules processed", "i18n:info.policy_rules_processing_complete": "信息: policy rules processing complete", "i18n:info.possible_template_found": "信息: possible template found", "i18n:info.pppoe_interfaces_filtered": "信息: pppoe interfaces filtered", "i18n:info.processing_address_groups": "信息: processing address groups", "i18n:info.processing_address_objects": "信息: processing address objects", "i18n:info.processing_fortinet_subinterfaces": "信息: processing fortinet subinterfaces", "i18n:info.processing_interface_mapping": "信息: processing interface mapping", "i18n:info.processing_ipv4_static_routes": "信息: processing ipv4 static routes", "i18n:info.processing_ipv6_static_route": "信息: processing ipv6 static route", "i18n:info.processing_ipv6_static_routes": "信息: processing ipv6 static routes", "i18n:info.processing_policy_rule": "信息: processing policy rule", "i18n:info.processing_policy_rules": "信息: processing policy rules", "i18n:info.processing_service_group": "信息: processing service group", "i18n:info.processing_service_groups": "信息: processing service groups", "i18n:info.processing_service_objects": "信息: processing service objects", "i18n:info.processing_static_route": "信息: processing static route", "i18n:info.processing_static_routes": "信息: processing static routes", "i18n:info.processing_time_ranges": "信息: processing time ranges", "i18n:info.processing_transparent_mode": "信息: processing transparent mode", "i18n:info.processing_zone_interface_mapping": "信息: processing zone interface mapping", "i18n:info.processing_zone_interfaces": "信息: processing zone interfaces", "i18n:info.received_enhanced_generator_output": "信息: received enhanced generator output", "i18n:info.received_legacy_generator_output": "信息: received legacy generator output", "i18n:info.removed_invalid_output": "信息: removed invalid output", "i18n:info.report_path": "信息: report path", "i18n:info.report_saved": "信息: report saved", "i18n:info.route_destination_converted": "信息: route destination converted", "i18n:info.saving_xml_config": "信息: saving xml config", "i18n:info.searching_data_dir": "信息: searching data dir", "i18n:info.security_policies_added_to_xml": "信息: security policies added to xml", "i18n:info.service_group_member_mapped": "信息: service group member mapped", "i18n:info.service_groups_processed": "信息: service groups processed", "i18n:info.service_groups_processing_complete": "信息: service groups processing complete", "i18n:info.service_mapping_saved": "信息: service mapping saved", "i18n:info.service_object_converted": "信息: service object converted", "i18n:info.service_objects_processed": "信息: service objects processed", "i18n:info.service_objects_processing_complete": "信息: service objects processing complete", "i18n:info.service_protocol_auto_detected": "信息: service protocol auto detected", "i18n:info.service_protocol_specified": "信息: service protocol specified", "i18n:info.setting_nat_rules": "信息: setting nat rules", "i18n:info.setting_time_ranges_legacy": "信息: setting time ranges legacy", "i18n:info.skipping_unmapped_interface": "信息: skipping unmapped interface", "i18n:info.skipping_unmapped_zone_interface": "信息: skipping unmapped zone interface", "i18n:info.start_adding_nat_rules_to_xml": "信息: start adding nat rules to xml", "i18n:info.start_adding_policy_rules_to_xml": "信息: start adding policy rules to xml", "i18n:info.start_conversion": "信息: start conversion", "i18n:info.start_encryption": "信息: start encryption", "i18n:info.start_processing_nat_rules": "信息: start processing nat rules", "i18n:info.start_verification": "信息: start verification", "i18n:info.start_verify_vendor_config": "信息: start verify vendor config", "i18n:info.static_routes_processed": "信息: static routes processed", "i18n:info.static_routes_processing_complete": "信息: static routes processing complete", "i18n:info.subinterface_direct_mapped": "信息: subinterface direct mapped", "i18n:info.subinterface_generated_mapping": "信息: subinterface generated mapping", "i18n:info.subinterface_mapped": "信息: subinterface mapped", "i18n:info.subinterface_parent_validated": "信息: subinterface parent validated", "i18n:info.target_model": "信息: target model", "i18n:info.target_version": "信息: target version", "i18n:info.temp_directory_created": "信息: temp directory created", "i18n:info.temp_directory_deleted": "信息: temp directory deleted", "i18n:info.template_absolute_path": "信息: template absolute path", "i18n:info.template_dir": "信息: template dir", "i18n:info.template_dir_created": "信息: template dir created", "i18n:info.text_file_saved": "信息: text file saved", "i18n:info.time_range_converted": "信息: time range converted", "i18n:info.time_ranges_processed": "信息: time ranges processed", "i18n:info.time_ranges_processing_complete": "信息: time ranges processing complete", "i18n:info.tool_started": "信息: tool started", "i18n:info.transparent_mode_config": "信息: transparent mode config", "i18n:info.transparent_mode_detected": "信息: transparent mode detected", "i18n:info.undefined_zone_interface_set_required": "信息: undefined zone interface set required", "i18n:info.user_interrupted": "信息: user interrupted", "i18n:info.user_log_file_created": "信息: user log file created", "i18n:info.using_alternative_template": "信息: using alternative template", "i18n:info.using_config_root": "信息: using config root", "i18n:info.using_current_directory": "信息: using current directory", "i18n:info.using_default_language": "信息: using default language", "i18n:info.using_enhanced_policy_processor": "使用增强策略处理器", "i18n:info.using_extended_generator_for_dns": "信息: using extended generator for dns", "i18n:info.using_legacy_architecture": "信息：使用传统架构", "i18n:info.using_new_architecture": "信息：使用新架构", "i18n:info.using_parser": "信息: using parser", "i18n:info.using_service_mapping_for_policy_rules": "信息: using service mapping for policy rules", "i18n:info.using_service_processor_with_mapping": "信息: using service processor with mapping", "i18n:info.using_standard_generator": "信息: using standard generator", "i18n:info.using_template": "信息: using template", "i18n:info.using_unfixed_xml_file": "信息: using unfixed xml file", "i18n:info.validating_interface_mapping": "信息: validating interface mapping", "i18n:info.validating_xml": "信息: validating xml", "i18n:info.validation_report_saved": "信息: validation report saved", "i18n:info.verification_passed": "信息: verification passed", "i18n:info.verification_success": "信息: verification success", "i18n:info.verify_fortigate_config": "信息: verify fortigate config", "i18n:info.verifying_config_before_conversion": "信息: verifying config before conversion", "i18n:info.xml_config_saved_with_fixed_namespaces": "信息: xml config saved with fixed namespaces", "i18n:info.xml_file_written": "信息: xml file written", "i18n:info.xml_validation_passed": "信息: xml validation passed", "i18n:info.zone_extracted": "信息: zone extracted", "i18n:info.zone_interface_mapped": "信息: zone interface mapped", "i18n:info.zone_interface_mapping_complete": "信息: zone interface mapping complete", "i18n:info.zones_extracted": "信息: zones extracted", "i18n:info.zones_found": "信息: zones found", "i18n:info.zones_processing_complete": "信息: zones processing complete", "i18n:key": "key", "i18n:multiline.buffer_content": "HTML模板内容", "i18n:multiline.certificate_content": "证书内容", "i18n:multiline.private_key_content": "私钥内容", "i18n:nat.statistics_summary": "nat: statistics summary", "i18n:user.error.conversion_failed": "user: error: conversion failed", "i18n:user.error.duplicate_service_object": "user: error: duplicate service object", "i18n:user.error.file_not_exists": "user: error: file not exists", "i18n:user.error.interface_mapping_validation_failed": "user: error: interface mapping validation failed", "i18n:user.error.interface_model_validation_failed": "user: error: interface model validation failed", "i18n:user.error.parsing_config_failed": "user: error: parsing config failed", "i18n:user.error.target_config_generation_failed": "user: error: target config generation failed", "i18n:user.error.template_not_found": "user: error: template not found", "i18n:user.error.transparent_mode_processing_failed": "user: error: transparent mode processing failed", "i18n:user.error.vendor_not_supported": "user: error: vendor not supported", "i18n:user.error.yang_validation_failed": "user: error: yang validation failed", "i18n:user.info.address_groups_processed": "user: info: address groups processed", "i18n:user.info.address_objects_processed": "user: info: address objects processed", "i18n:user.info.config_file_generated": "user: info: config file generated", "i18n:user.info.config_file_read_success": "user: info: config file read success", "i18n:user.info.dns_config_added": "user: info: dns config added", "i18n:user.info.encrypted_file_generated": "user: info: encrypted file generated", "i18n:user.info.interfaces_extracted": "user: info: interfaces extracted", "i18n:user.info.mapping_file_loaded": "user: info: mapping file loaded", "i18n:user.info.nat_mode_detected": "user: info: nat mode detected", "i18n:user.info.nat_rules_added": "user: info: nat rules added", "i18n:user.info.no_nat_rules_found": "user: info: no nat rules found", "i18n:user.info.no_system_settings_nat_mode": "user: info: no system settings nat mode", "i18n:user.info.parsing_config_complete": "user: info: parsing config complete", "i18n:user.info.parsing_config_start": "user: info: parsing config start", "i18n:user.info.policy_rules_processed": "user: info: policy rules processed", "i18n:user.info.predefined_services_detected": "user: info: predefined services detected", "i18n:user.info.service_groups_processed": "user: info: service groups processed", "i18n:user.info.service_objects_processed": "user: info: service objects processed", "i18n:user.info.start_parsing": "user: info: start parsing", "i18n:user.info.static_routes_processed": "user: info: static routes processed", "i18n:user.info.time_ranges_processed": "user: info: time ranges processed", "i18n:user.info.transparent_mode_config": "user: info: transparent mode config", "i18n:user.info.transparent_mode_detected": "user: info: transparent mode detected", "i18n:user.info.yang_validation_passed": "user: info: yang validation passed", "i18n:user.info.zones_extracted": "user: info: zones extracted", "i18n:user.summary.address_groups_processed": "user: summary: address groups processed", "i18n:user.summary.addresses_processed": "user: summary: addresses processed", "i18n:user.summary.compatibility_issue": "user: summary: compatibility issue", "i18n:user.summary.compatibility_issues": "user: summary: compatibility issues", "i18n:user.summary.conversion_complete": "user: summary: conversion complete", "i18n:user.summary.conversion_stats": "user: summary: conversion stats", "i18n:user.summary.conversion_summary": "user: summary: conversion summary", "i18n:user.summary.conversion_time": "user: summary: conversion time", "i18n:user.summary.failed_item_detail": "user: summary: failed item detail", "i18n:user.summary.failed_items_detail": "user: summary: failed items detail", "i18n:user.summary.interfaces_processed": "user: summary: interfaces processed", "i18n:user.summary.manual_config_item": "user: summary: manual config item", "i18n:user.summary.manual_config_needed": "user: summary: manual config needed", "i18n:user.summary.nat_rules_processed": "user: summary: nat rules processed", "i18n:user.summary.no_compatibility_issues": "user: summary: no compatibility issues", "i18n:user.summary.no_manual_config_needed": "user: summary: no manual config needed", "i18n:user.summary.no_unconverted_items": "user: summary: no unconverted items", "i18n:user.summary.policies_processed": "user: summary: policies processed", "i18n:user.summary.routes_processed": "user: summary: routes processed", "i18n:user.summary.service_groups_processed": "user: summary: service groups processed", "i18n:user.summary.services_processed": "user: summary: services processed", "i18n:user.summary.skipped_item_detail": "user: summary: skipped item detail", "i18n:user.summary.skipped_items_detail": "user: summary: skipped items detail", "i18n:user.summary.time_ranges_processed": "user: summary: time ranges processed", "i18n:user.summary.total_items_processed": "user: summary: total items processed", "i18n:user.summary.unconverted_item": "user: summary: unconverted item", "i18n:user.summary.unconverted_items": "user: summary: unconverted items", "i18n:user.summary.zones_processed": "user: summary: zones processed", "i18n:user.warning.all_physical_interfaces_unmapped": "user: warning: all physical interfaces unmapped", "i18n:user.warning.compatibility_issues_detected": "user: warning: compatibility issues detected", "i18n:user.warning.nat_not_supported_in_version": "user: warning: nat not supported in version", "i18n:user.warning.route_interface_not_mapped_check": "user: warning: route interface not mapped check", "i18n:user.warning.total_unmapped_zone_interfaces": "user: warning: total unmapped zone interfaces", "i18n:user.warning.unsupported_protocol_ntos": "user: warning: unsupported protocol ntos", "i18n:user.warning.zone_unmapped_interfaces": "user: warning: zone unmapped interfaces", "i18n:warning.all_physical_interfaces_unmapped": "警告: all physical interfaces unmapped", "i18n:warning.cannot_parse_port_range": "警告: cannot parse port range", "i18n:warning.cannot_process_zone_interfaces": "警告: cannot process zone interfaces", "i18n:warning.cannot_process_zones": "警告: cannot process zones", "i18n:warning.compatibility_issue_detail": "警告: compatibility issue detail", "i18n:warning.compatibility_issues_detected": "警告: compatibility issues detected", "i18n:warning.compatibility_issues_found": "警告: compatibility issues found", "i18n:warning.config_dir_not_found": "警告: config dir not found", "i18n:warning.failed_to_add_nat_rules_to_xml": "警告: failed to add nat rules to xml", "i18n:warning.failed_to_add_security_policies_to_xml": "警告: failed to add security policies to xml", "i18n:warning.failed_to_convert_policy_rule": "警告: failed to convert policy rule", "i18n:warning.format_error": "警告: format error", "i18n:warning.generator_empty_service_node_removal_failed": "警告: generator empty service node removal failed", "i18n:warning.generator_no_service_object_node": "警告: generator no service object node", "i18n:warning.generator_service_object_node_empty": "警告: generator service object node empty", "i18n:warning.interface_data_not_list": "警告: interface data not list", "i18n:warning.interface_mapping_not_found": "警告: interface mapping not found", "i18n:warning.interface_missing_raw_name": "警告: interface missing raw name", "i18n:warning.interface_missing_raw_name_field": "警告: interface missing raw name field", "i18n:warning.interface_not_string": "警告: interface not string", "i18n:warning.invalid_port_range": "警告: invalid port range", "i18n:warning.ipv6_route_missing_destination": "警告: ipv6 route missing destination", "i18n:warning.ipv6_route_no_necessary_config": "警告: ipv6 route no necessary config", "i18n:warning.legacy_policy_rule_missing_name": "警告: legacy policy rule missing name", "i18n:warning.missing_param": "警告: missing param", "i18n:warning.missing_params": "警告: missing params", "i18n:warning.nat_rule_skipped_version_not_supported": "警告: nat rule skipped version not supported", "i18n:warning.nat_rules_not_supported": "警告: nat rules not supported", "i18n:warning.new_architecture_failed": "警告: new architecture failed", "i18n:warning.no_interfaces_to_process": "警告: no interfaces to process", "i18n:warning.no_service_obj_node_in_final_xml": "警告: no service obj node in final xml", "i18n:warning.path_corrected": "警告: path corrected", "i18n:warning.port_out_of_range": "警告: port out of range", "i18n:warning.pppoe_interface_filtered": "警告: pppoe interface filtered", "i18n:warning.pppoe_missing_credentials": "警告: pppoe missing credentials", "i18n:warning.route_interface_not_mapped": "警告: route interface not mapped", "i18n:warning.route_missing_destination": "警告: route missing destination", "i18n:warning.semantic_verification": "警告: semantic verification", "i18n:warning.service_group_member_not_found": "警告: service group member not found", "i18n:warning.service_group_missing_name": "警告: service group missing name", "i18n:warning.service_group_no_members": "警告: service group no members", "i18n:warning.service_group_no_valid_members": "警告: service group no valid members", "i18n:warning.service_missing_protocol_and_ports": "警告: service missing protocol and ports", "i18n:warning.service_processor_failed": "警告: service processor failed", "i18n:warning.skip_non_dict_service_object": "警告: skip non dict service object", "i18n:warning.skip_service_object_missing_name": "警告: skip service object missing name", "i18n:warning.skipping_address_group_empty_members": "警告: skipping address group empty members", "i18n:warning.skipping_address_group_missing_name": "警告: skipping address group missing name", "i18n:warning.skipping_address_invalid_subnet": "警告: skipping address invalid subnet", "i18n:warning.skipping_address_missing_ip_range": "警告: skipping address missing ip range", "i18n:warning.skipping_address_missing_name": "警告: skipping address missing name", "i18n:warning.skipping_address_missing_subnet": "警告: skipping address missing subnet", "i18n:warning.skipping_address_type_not_explicitly_supported": "警告: skipping address type not explicitly supported", "i18n:warning.skipping_address_unsupported_type": "警告: skipping address unsupported type", "i18n:warning.skipping_interface_associated_address": "警告: skipping interface associated address", "i18n:warning.skipping_invalid_interface_with_reason": "警告: skipping invalid interface with reason", "i18n:warning.skipping_non_dict_zone": "警告: skipping non dict zone", "i18n:warning.skipping_policy_rule_missing_fields": "警告: skipping policy rule missing fields", "i18n:warning.skipping_policy_rule_unmapped_interfaces": "警告: skipping policy rule unmapped interfaces", "i18n:warning.skipping_service_missing_name": "警告: skipping service missing name", "i18n:warning.skipping_service_missing_port_range": "警告: skipping service missing port range", "i18n:warning.skipping_service_missing_protocol_and_ports": "警告: skipping service missing protocol and ports", "i18n:warning.skipping_service_unsupported_protocol": "警告: skipping service unsupported protocol", "i18n:warning.skipping_unsupported_protocol_ntos": "警告: skipping unsupported protocol ntos", "i18n:warning.static_routes_not_supported": "警告: static routes not supported", "i18n:warning.subinterface_missing_parent_or_vlanid_skipped": "警告: subinterface missing parent or vlanid skipped", "i18n:warning.subinterface_parent_not_mapped": "警告: subinterface parent not mapped", "i18n:warning.subinterface_parent_not_mapped_skipped": "警告: subinterface parent not mapped skipped", "i18n:warning.tcp_port_range_invalid": "警告: tcp port range invalid", "i18n:warning.template_dir_not_exists": "警告: template dir not exists", "i18n:warning.time_ranges_not_supported": "警告: time ranges not supported", "i18n:warning.total_unmapped_zone_interfaces": "警告: total unmapped zone interfaces", "i18n:warning.translation_format_error": "警告: translation format error", "i18n:warning.translation_missing_param": "警告: translation missing param", "i18n:warning.translation_missing_params": "警告: translation missing params", "i18n:warning.udp_port_range_invalid": "警告: udp port range invalid", "i18n:warning.zone_data_not_list": "警告: zone data not list", "i18n:warning.zone_interface_not_mapped": "警告: zone interface not mapped", "i18n:warning.zone_interfaces_not_list": "警告: zone interfaces not list", "i18n:warning.zone_unmapped_interfaces": "警告: zone unmapped interfaces", "i18n:warning.zones_not_list": "警告: zones not list", "impact.dynamic_routing_ignored": "动态路由配置将被忽略，需要手动配置", "impact.interface_count_exceeded": "当前配置包含 {count} 个接口，超过Z5系列设备支持的上限", "impact.vdom_ignored": "虚拟域配置将被忽略，转换后需要手动配置虚拟域结构", "impact.vpn_ignored": "VPN配置将被忽略，需要手动配置", "importlib.machinery": "importlib: machinery", "importlib.util": "importlib: util", "info.adapting_service_processor_result_format": "调整服务处理器结果格式", "info.add_interface_with_json": "添加接口: {interface_name} (包含详细属性)", "info.add_static_routes": "添加 {count} 条静态路由到NTOS生成器", "info.added_address_group": "添加地址组: {name}, 成员数: {members}", "info.added_address_groups": "已添加地址组 {count} 个", "info.added_address_object": "添加地址对象: {name}, 类型: {type}", "info.added_address_objects": "已添加地址对象 {count} 个", "info.added_dns_config": "已添加DNS配置，服务器数量: {servers}，DNS over TLS: {dns_over_tls}", "info.added_interface_to_zone": "为区域 {zone} 添加接口: {interface}", "info.added_interfaces": "已添加接口 {count} 个", "info.added_ip_next_hop": "已添加IP下一跳: {next_hop}", "info.added_mapped_interface_to_zone": "为区域 {zone} 添加接口: {interface} -> {mapped}", "info.added_missing_name_element": "已添加缺失的name元素", "info.added_namespace": "添加命名空间: {prefix} -> {uri}", "info.added_policy_rules": "已添加策略规则 {count} 条", "info.added_service_group": "已添加服务组: {name}，成员数: {members}", "info.added_service_groups": "已添加服务组 {count} 个", "info.added_service_objects": "已添加服务对象 {count} 个", "info.added_static_route": "信息: added static route", "info.added_static_routes": "已添加静态路由 {count} 条", "info.added_time_range": "添加时间对象 {name}", "info.added_time_ranges": "已添加时间范围: {count} 个", "info.added_zones": "已添加区域 {count} 个", "info.adding_dns_config": "正在添加DNS配置", "info.adding_interfaces_to_xml": "添加 {count} 个接口配置到XML", "info.adding_static_routes": "添加 {count} 条静态路由到NTOS生成器", "info.adding_time_ranges": "正在添加 {count} 个时间对象", "info.adding_time_ranges_to_xml": "正在将时间范围添加到XML", "info.adding_zone_interfaces": "添加区域接口", "info.address_group_converted": "地址组已转换: {name}，成员数: {members}", "info.address_group_exists": "地址组已存在: {name}", "info.address_groups_processed": "地址组处理结果: {success} 个成功，{failed} 个失败", "info.address_groups_processing_complete": "地址组处理完成: 共 {total} 个，成功 {success} 个，失败 {failed} 个，跳过 {skipped} 个", "info.address_object_converted": "地址对象已转换: {name} ({type})", "info.address_object_exists": "地址对象已存在: {name}", "info.address_object_success": "地址对象 {name} 转换成功", "info.address_objects_complete": "地址对象处理完成: 共 {total} 个，成功 {success} 个，失败 {failed} 个，跳过 {skipped} 个", "info.address_objects_processed": "地址对象处理结果: {success} 成功, {failed} 失败", "info.address_objects_processing_complete": "地址对象处理完成: 共 {total} 个，成功 {success} 个，失败 {failed} 个，跳过 {skipped} 个", "info.address_objects_processing_complete_user": "信息: address objects processing complete user", "info.address_processed": "提取并处理了 {count} 个地址对象", "info.address_result": "地址对象处理结果: {success} 成功, {failed} 失败", "info.address_type_detected_from_attributes": "从属性检测到地址类型: {type}", "info.address_type_not_specified": "地址类型未指定: {name}，使用默认: {default}", "info.address_type_specified": "指定地址类型: {type}", "info.all_predefined_services_detected": "所有 {count} 个服务都是预定义服务，仅保存映射关系供策略转换使用", "info.all_predefined_services_found": "找到所有预定义服务: {count} 个", "info.all_zone_interfaces_mapped": "所有区域接口均已成功映射", "info.apply_interface_mapping": "应用接口映射: {original} -> {mapped}", "info.batch_processing_complete": "批处理完成，共 {batches} 个批次", "info.blackhole_route_created_success": "黑洞路由创建成功", "info.blackhole_route_default_destination": "黑洞路由 {id} 缺少目的网络，使用默认值 0.0.0.0/0", "info.bridge_interface_generated_successfully": "桥接接口生成成功", "info.calling_processor": "调用处理器: {type} -> {name}", "info.check_compatibility": "检查配置与目标设备的兼容性...", "info.checking_and_fixing_namespaces": "检查并修复命名空间", "info.checking_compatibility": "检查配置与目标设备的兼容性...", "info.checking_parent_dir": "检查父目录: {dir}", "info.clean_interface_mapping": "清理接口映射: {k}={v}", "info.clean_interface_name": "清理接口名称引号: {name}", "info.cleaned_security_zone_namespaces": "已清理区域配置的命名空间", "info.cleaning_namespaces": "正在清理命名空间", "info.cleanup_temp_dir": "已清理临时目录: {dir}", "info.collected_interfaces": "已收集接口", "info.collecting_existing_interfaces": "正在收集现有接口", "info.compatibility_issues": "检测到 {count} 个兼容性问题", "info.config_dir_found": "找到Config目录路径: {path}", "info.config_file_generated": "成功生成目标配置文件", "info.config_file_read_success": "成功读取配置文件，大小: {size} 字节", "info.conversion_report_saved": "转换报告已保存到: {file}", "info.conversion_succeeded": "转换成功完成！", "info.conversion_success": "转换成功完成！", "info.conversion_successful": "配置转换成功完成！", "info.convert_string_to_list": "将字符串转换为列表: {data}", "info.convert_to_string": "将{value}转换为字符串", "info.converting_single_interface_to_list": "将单个接口名称字符串转换为列表: {interfaces}", "info.converting_static_routes": "转换静态路由", "info.converting_zones_dict_to_list": "尝试将zones字典转换为列表", "info.copy_config_dir": "复制Config目录: {src} -> {dst}", "info.copy_xml_to_running": "已复制生成的XML到运行配置: {path}", "info.copy_xml_to_startup": "已复制生成的XML到启动配置: {path}", "info.corrected_vrf_name_to_main": "已将VRF名称更正为'main'", "info.create_dir_success": "成功创建目录: {dir}", "info.create_directory": "创建目录: {dir}", "info.create_interface_mapping_for_routes": "创建接口映射表用于静态路由，共 {count} 个映射", "info.create_new_config_dir": "创建新的Config目录: {dir}", "info.create_new_vlan_interface": "创建新VLAN子接口配置 - 原始数据: {data}", "info.create_output_dir": "创建输出目录: {dir}", "info.create_routing_node": "创建新的routing节点", "info.create_time_range_node": "信息: create time range node", "info.create_user_log_dir": "创建用户日志目录: {log_dir}", "info.create_xml_generator": "创建XML生成器", "info.created_address_group_node": "创建地址组节点", "info.created_address_object": "创建地址对象: {name}, 类型: {type}", "info.created_default_always_time_object": "信息: created default always time object", "info.created_element": "已创建元素: {name}", "info.created_interface": "创建接口: {name}", "info.created_network_obj_node": "创建网络对象节点", "info.created_new_interface_node": "信息: created new interface node", "info.created_new_xml_element": "创建新的XML元素: {name}", "info.created_policy_rule": "创建策略规则: {name}", "info.created_routing_node": "创建路由节点", "info.created_security_zone_container": "信息: created security zone container", "info.created_service_obj_node": "创建服务对象节点", "info.created_service_object": "创建服务对象: {name}, 协议: {protocol}", "info.created_static_node": "创建静态路由节点", "info.created_static_route": "创建静态路由: {id}", "info.created_time_range": "创建了新的时间对象: {name}", "info.created_time_range_node": "创建时间对象节点", "info.created_vrf_node": "信息: created vrf node", "info.created_zone": "创建区域: {name}", "info.creating_interface_node": "在vrf节点下创建interface节点", "info.creating_interface_xml": "创建接口XML", "info.creating_new_address": "创建新地址对象: {name}", "info.creating_new_address_group": "创建新地址组: {name}", "info.creating_new_interface": "创建新接口: {name}", "info.creating_new_service": "创建新服务对象: {name}", "info.creating_new_service_group": "创建新服务组: {name}", "info.creating_new_subinterface": "创建新子接口: {name}", "info.creating_new_vlan": "创建新VLAN: {name}", "info.creating_new_zone": "创建新区域: {name}", "info.creating_static_route_xml": "创建静态路由XML", "info.creating_vlan_interface": "创建VLAN接口", "info.creating_vrf_node": "正在创建vrf节点", "info.cross_day_schedule_split": "跨天时间表已拆分: {name}，数量: {count}", "info.current_working_dir": "当前工作目录: {dir}", "info.delete_existing_output": "删除已存在的输出Config目录: {dir}", "info.detected_always_schedule": "检测到始终时间表: {name}", "info.detected_container_environment": "检测到容器环境，使用路径: {dir}", "info.detected_cross_day_schedule": "检测到跨天时间表: {name}", "info.detected_dev_environment": "检测到开发环境，使用路径: {dir}", "info.detected_fortigate_version": "检测到FortiOS版本: {version}", "info.detected_none_schedule": "检测到无时间表: {name}", "info.directory_created": "创建目录: {directory}", "info.dmz_interface_set_non_required": "DMZ接口 {interface} 被设置为非必需，NTOS系统不支持DMZ接口角色", "info.dmz_interface_set_required": "DMZ接口 {interface} 被设置为必需，将被转换为LAN接口角色", "info.enable_bfd": "启用BFD: {value}", "info.enable_route": "启用路由: {value}", "info.encrypt_file_success": "成功生成加密配置文件", "info.encrypt_start": "开始复制和准备Config目录", "info.encrypt_success": "成功生成加密配置文件: {file}", "info.encrypted_file_generated": "成功生成加密配置文件", "info.encryption_success": "加密成功，加密文件保存在: {file}", "info.engine_debug_log_created": "引擎调试日志将写入: {file}", "info.engine_user_log_created": "引擎用户日志将写入: {file}", "info.enhanced_policy_processing_complete": "增强策略处理完成", "info.error_report_saved": "错误报告已保存到: {file}", "info.existing_interfaces_in_template": "模板中现有接口:", "info.existing_service_group_name": "现有服务组名称: {name}", "info.existing_service_groups_count": "现有服务组数量: {count}", "info.extract_interfaces_from_config": "开始从配置文件提取接口信息: {file}", "info.extract_interfaces_from_config_user": "开始从配置文件提取接口信息", "info.extracting_interfaces_from_config": "从配置中提取接口信息: {file}", "info.extracting_zones": "从配置中提取区域信息", "info.extraction_start": "开始提取接口信息", "info.extraction_success": "成功提取 {count} 个接口", "info.fallback_to_legacy_generation": "回退到传统生成策略", "info.fallback_to_non_batch_strategy": "回退到非批处理策略", "info.falling_back_to_legacy_service_processing": "回退到传统服务对象处理方式", "info.file_copied": "已复制文件: {src} -> {dst}", "info.final_interface_mapping_created": "最终接口映射已创建", "info.final_interface_mapping_file_saved": "最终接口映射文件已保存", "info.final_interface_mapping_saved": "最终接口映射已保存", "info.finding_vrf_node": "查找VRF节点", "info.finish_conversion": "转换完成，耗时: {time}", "info.finished_processing_all_config": "完成处理所有配置数据", "info.fix_root_namespace": "信息: fix root namespace", "info.fixed_namespace_for_element": "修复元素 {element} 的命名空间: {old_tag} -> {new_tag}", "info.fixed_node_name": "信息: fixed node name", "info.fixing_duplicate_xmlns": "修复重复的xmlns属性", "info.fixing_namespace_for_elements": "修复元素 {element} 的命名空间 (数量: {count}): {namespace}", "info.fixing_xml_namespaces": "修复XML命名空间问题...", "info.fortinet_subinterfaces_processed": "飞塔子接口处理完成，现在共有 {count} 个接口", "info.found_existing_address_group": "找到现有地址组节点", "info.found_existing_interface_node": "找到现有接口节点", "info.found_existing_interface_node_with_method": "信息: found existing interface node with method", "info.found_existing_network_obj": "找到现有网络对象节点", "info.found_existing_object": "找到现有对象: {name}", "info.found_existing_physical_interface": "找到现有物理接口: {name}", "info.found_existing_pppoe_tunnel": "  找到现有PPPoE隧道ID: {id}", "info.found_existing_routing_node": "找到现有路由节点", "info.found_existing_security_zone_container": "找到现有安全区域容器: {zone_name}", "info.found_existing_service": "找到现有服务: {name}", "info.found_existing_service_group": "找到现有服务组: {name}", "info.found_existing_service_obj": "找到现有的服务对象节点", "info.found_existing_service_obj_node": "信息: found existing service obj node", "info.found_existing_static_node": "找到现有静态路由节点", "info.found_existing_time_range": "找到已存在的时间对象: {name}", "info.found_existing_time_range_node": "找到已存在的时间对象节点", "info.found_existing_time_range_node_with_method": "通过{method}方法找到已存在的time-range节点", "info.found_existing_vlan_interface": "找到现有VLAN接口: {name}", "info.found_existing_vlan_pppoe_tunnel": "  找到现有VLAN子接口PPPoE隧道ID: {id}", "info.found_existing_xml_element": "找到现有的XML元素: {name}，使用方法: {method}", "info.found_existing_zone": "找到现有区域: {name}", "info.found_interface": "  - {name}", "info.found_multiple_time_range_nodes": "发现多个time-range节点（共{count}个），将进行合并", "info.found_parent_interface_mapping": "找到父接口映射: {original} -> {mapped}", "info.found_physical_interface_name_with_method": "信息: found physical interface name with method", "info.found_physical_interfaces_no_namespace": "信息: found physical interfaces no namespace", "info.found_physical_interfaces_with_namespace": "发现带命名空间的物理接口", "info.found_physical_interfaces_with_uri": "信息: found physical interfaces with uri", "info.found_service_obj_in_vrf": "在VRF节点中找到service-obj节点", "info.found_template_file": "  - {path}", "info.found_template_files": "找到 {count} 个可能的模板文件", "info.found_vlan_interface": "  - VLAN: {name}", "info.found_vlan_interface_name_with_method": "信息: found vlan interface name with method", "info.found_vlan_interfaces_no_namespace": "信息: found vlan interfaces no namespace", "info.found_vlan_interfaces_with_namespace": "信息: found vlan interfaces with namespace", "info.found_vlan_interfaces_with_uri": "信息: found vlan interfaces with uri", "info.found_vrf_node_by_tag": "信息: found vrf node by tag", "info.found_vrf_node_by_xpath": "信息: found vrf node by xpath", "info.generate_target_success": "成功生成目标配置文件", "info.generating_bridge_interface_for_transparent_mode": "正在为透明模式生成桥接接口...", "info.generating_using_yang_model": "正在使用YANG模型生成配置", "info.generation_complete": "配置生成完成", "info.get_template_path": "获取配置模板路径: {model} {version}", "info.getting_template_path": "获取配置模板路径: {model} {version}", "info.improved_legacy_generator_processing_complete": "ImprovedLegacyGenerator服务对象处理完成: 成功={success}, 跳过={skipped}", "info.improved_legacy_generator_processing_services": "ImprovedLegacyGenerator处理服务对象，共 {count} 个", "info.improved_set_time_ranges": "设置 {count} 个时间对象到改进版生成器", "info.init_improved_legacy_generator": "初始化改进版生成器，型号：{model}，版本：{version}", "info.init_legacy_generator": "信息: init legacy generator", "info.init_ntos_generator": "初始化NTOS生成器，型号: {model}，版本: {version}", "info.initial_xml_size": "信息: initial xml size", "info.input_file": "输入文件: {file}", "info.interface_found_using_mapped_name": "接口在映射中找到，使用映射名称: {original} -> {mapped}", "info.interface_info_written": "接口信息已写入: {file}", "info.interface_info_written_user": "接口信息已成功写入文件", "info.interface_mapped": "接口映射: {raw_name} -> {mapped_name}", "info.interface_mapped_successfully": "接口映射成功: {original} -> {mapped}", "info.interface_mapping": "接口映射: {raw_name} -> {mapped_name}", "info.interface_mapping_applied": "应用接口映射: {original} -> {mapped}", "info.interface_mapping_complete": "接口映射处理完成，共 {count} 个接口", "info.interface_mapping_created": "创建接口映射: {original} -> {mapped}", "info.interface_mapping_from_dict": "使用映射表映射父接口(从字典): {original} -> {mapped}", "info.interface_mapping_init": "处理接口映射...", "info.interface_mapping_loaded": "接口映射已加载", "info.interface_mapping_processing_complete": "接口映射处理完成，共 {count} 个接口", "info.interface_mapping_saved": "接口映射已保存", "info.interface_mapping_table": "接口映射表: {mapping}", "info.interface_mapping_validation_passed": "接口映射验证通过", "info.interface_name_mapping": "接口名称映射: {original} -> {mapped}", "info.interface_name_quotes_removed": "清理接口名称引号: {name}", "info.interface_not_found_using_zone": "接口不在映射中，使用区域名称: {original} -> {zone}", "info.interface_zone_assigned": "接口 {interface} 已分配到区域 {zone}", "info.interfaces_extracted": "从解析数据中提取到 {count} 个接口配置", "info.interfaces_found": "从配置文件中提取到 {count} 个接口", "info.interfaces_processing_complete": "接口处理完成: {processed} 个处理成功, {skipped} 个跳过", "info.json_file_saved": "JSON文件已保存至: {file_path}", "info.keep_interface_mapping": "保持接口映射: {k}={v}", "info.language_set": "已设置语言为: {language}", "info.legacy_generation_complete": "传统生成方式完成", "info.legacy_generation_fallback": "传统生成失败，使用最小化XML结构", "info.legacy_policy_rules_processing_complete": "策略规则处理完成: 总计={total}, 成功={success}, 失败={failed}, 跳过={skipped}", "info.legacy_set_time_ranges": "设置 {count} 个时间对象到传统生成器", "info.legacy_xml_generation_complete": "传统XML生成完成", "info.load_config": "加载配置文件", "info.load_mapping": "加载接口映射", "info.load_mapping_succeeded": "成功加载接口映射", "info.load_mapping_success": "成功加载接口映射文件: {count} 条映射", "info.loading_interface_template": "加载接口模板", "info.loading_xml_config": "正在加载XML配置文件: {file_path}", "info.log_file_created": "日志将写入文件: {file}", "info.log_initialization_complete": "日志初始化完成", "info.log_to_file": "日志将写入文件: {file}", "info.map_parent_interface": "通过辅助函数映射父接口: {original} -> {mapped}", "info.mapping_file": "映射文件: {file}", "info.mapping_file_loaded": "成功加载接口映射文件: {count} 条映射", "info.merged_duplicate_element": "合并重复的元素: {name}，ID: {id}", "info.merged_duplicate_time_range_nodes": "合并了 {count} 个重复的time-range节点", "info.merged_duplicate_xml_element": "信息: merged duplicate xml element", "info.merged_range_from_duplicate": "从重复节点合并时间范围：{name}", "info.merged_time_range_from_duplicate": "信息: merged time range from duplicate", "info.merged_unnamed_range": "合并未命名的时间范围", "info.min_version_requirement": "最低版本要求", "info.modem_interface_filtered": "Modem接口已过滤（逻辑接口）: {interface}", "info.namespaces_cleaned_successfully": "命名空间清理成功", "info.namespaces_fixed": "命名空间修复完成", "info.namespaces_set_successfully": "命名空间设置成功", "info.nat_mode_detected": "检测到NAT模式配置", "info.nat_rules_added_to_xml": "成功添加NAT配置到XML: {rules} 个规则, {pools} 个池", "info.nat_rules_processing_complete": "NAT规则处理完成: 总计={total}, 成功={success}, 失败={failed}", "info.no_address_groups_found": "没有找到地址组", "info.no_address_groups_to_process": "没有地址组需要处理", "info.no_address_objects": "没有找到地址对象", "info.no_address_objects_found": "没有找到地址对象", "info.no_address_objects_to_process": "没有地址对象需要处理", "info.no_compatibility_issues": "未发现兼容性问题", "info.no_interfaces_or_mappings_to_validate": "没有接口或映射需要验证", "info.no_interfaces_to_process": "没有接口需要处理", "info.no_nat_rules_found": "未发现NAT规则", "info.no_nat_rules_to_process": "没有NAT规则需要处理", "info.no_policy_rules": "没有找到策略规则", "info.no_policy_rules_found": "没有找到策略规则", "info.no_policy_rules_to_process": "没有策略规则需要处理", "info.no_routes_to_process": "没有路由需要处理", "info.no_service_groups_found": "没有找到服务组", "info.no_service_groups_to_process": "没有服务组需要处理", "info.no_service_objects": "没有找到服务对象", "info.no_service_objects_found": "没有找到服务对象", "info.no_service_objects_to_process": "没有服务对象需要处理", "info.no_static_routes": "没有找到静态路由", "info.no_static_routes_found": "没有找到静态路由", "info.no_static_routes_to_process": "没有静态路由需要处理", "info.no_system_settings_nat_mode": "未发现系统设置配置，默认使用NAT模式", "info.no_time_ranges_to_process": "没有时间对象需要处理", "info.no_valid_ipv6_static_routes": "信息: no valid ipv6 static routes", "info.no_vrf_nodes_found_creating": "信息: no vrf nodes found creating", "info.no_zones_found": "未发现区域", "info.no_zones_to_process": "没有区域需要处理", "info.none_schedule_detected_by_content": "通过内容结构检测到时间对象 '{name}' 为'none'类型，属性: {keys}", "info.ntos_builtin_services_loaded": "NTOS内置服务已加载: {count} 个", "info.ntos_predefined_service_mapped": "NTOS预定义服务映射: {fortigate_name} -> {ntos_name}, 描述: {description}", "info.ntos_subinterface_name": "NTOS子接口名称: {name}", "info.ntos_xml_generation_complete": "NTOS XML配置生成完成", "info.output_dir_created": "创建输出目录: {dir}", "info.output_file": "输出文件: {file}", "info.parent_interface_raw_name": "父接口原始名称: {name}", "info.parsing_complete": "配置解析完成", "info.parsing_config": "开始解析配置文件...", "info.parsing_config_complete": "配置解析完成", "info.parsing_config_start": "开始解析配置文件...", "info.parsing_fortinet_subinterface": "解析飞塔子接口: {name}, 数据: {data}", "info.parsing_succeeded": "配置文件解析完成", "info.policy_processed": "提取并处理了 {count} 条策略规则", "info.policy_result": "策略规则处理结果: {success} 成功, {failed} 失败", "info.policy_rule_converted_success": "策略规则 {name} 转换成功", "info.policy_rule_exists": "策略规则已存在: {name}", "info.policy_rule_success": "策略规则 {name} 转换成功", "info.policy_rules_complete": "策略规则处理完成: 共 {total} 条，成功 {success} 条，失败 {failed} 条，跳过 {skipped} 条", "info.policy_rules_processed": "策略规则处理结果: {success} 成功, {failed} 失败", "info.policy_rules_processing_complete": "策略规则处理完成: 共 {total} 条，成功 {converted} 条，失败 {failed} 条，跳过 {skipped} 条", "info.possible_template_found": "找到可能的模板文件: {path}", "info.proceeding_with_warnings": "尽管有 {count} 个警告，仍继续进行转换", "info.process_address_objects": "处理地址对象...", "info.process_fortinet_subinterfaces": "处理飞塔子接口特殊映射...", "info.process_interface_mapping": "使用接口映射处理接口配置...", "info.process_policy_rules": "处理策略规则...", "info.process_service_objects": "处理服务对象...", "info.process_static_routes": "处理静态路由...", "info.processing_address_groups": "处理地址组...", "info.processing_address_objects": "处理地址对象...", "info.processing_address_objects_user": "信息: processing address objects user", "info.processing_batch": "处理批次 {index}/{total}", "info.processing_complete": "处理完成，类型: {type}，总计: {total}，已处理: {processed}，跳过: {skipped}", "info.processing_fortinet_subinterfaces": "处理飞塔子接口特殊映射...", "info.processing_interface": "\n处理接口: {raw_name} -> {name}", "info.processing_interface_mapping": "处理接口映射...", "info.processing_interface_mappings": "处理接口映射", "info.processing_ipv4_static_routes": "正在处理IPv4静态路由", "info.processing_onetime_schedule": "处理一次性时间表: {name}", "info.processing_parsing_result": "处理解析结果", "info.processing_periodic_schedule": "处理周期性时间表: {name}", "info.processing_policy_rule": "处理策略规则: {name}", "info.processing_policy_rules": "处理策略规则...", "info.processing_service_group": "处理服务组: {name} (原始: {original})", "info.processing_service_groups": "处理服务组，数量: {count}", "info.processing_service_objects": "处理服务对象...", "info.processing_service_objects_with_mapping": "使用预定义服务映射处理服务对象", "info.processing_static_route": "处理静态路由 {id}: {dest} -> {gateway}", "info.processing_static_routes": "处理静态路由...", "info.processing_time_range": "正在处理时间对象 {name}", "info.processing_time_ranges": "正在处理时间对象", "info.processing_transparent_mode": "正在处理透明模式配置...", "info.processing_zone": "处理区域: {name}", "info.processing_zone_interface_mapping": "处理区域中的接口映射...", "info.processing_zone_interfaces": "处理区域 {zone} 的接口映射", "info.read_config_succeeded": "成功读取配置文件", "info.read_config_success": "成功读取配置文件，大小: {size} 字节", "info.received_enhanced_generator_output": "信息: received enhanced generator output", "info.received_legacy_generator_output": "已接收传统生成器输出", "info.registered_existing_tunnel_id": "信息: registered existing tunnel id", "info.registered_processor": "已注册处理器: {type} -> {name}", "info.remove_old_namespace_tags": "移除旧的命名空间标签", "info.removed_duplicate_element": "移除重复的元素: {name}，值: {value}", "info.removed_duplicate_interfaces": "删除 {count} 个重复接口", "info.removed_duplicate_objects": "删除 {count} 个重复对象", "info.removed_duplicate_services": "删除 {count} 个重复服务", "info.removed_duplicate_zones": "删除 {count} 个重复区域", "info.removed_existing_routes": "删除 {count} 条现有路由", "info.removed_existing_rules": "删除 {count} 条现有规则", "info.removed_invalid_output": "已移除无效的输出文件: {file}", "info.removing_duplicate_interface": "删除重复的接口: {type}:{name}", "info.removing_duplicate_interfaces": "正在移除重复接口", "info.removing_duplicate_object": "删除重复的对象: {name}", "info.removing_duplicate_service": "删除重复的服务: {name}", "info.removing_duplicate_time_range_node": "移除重复的time-range节点", "info.removing_duplicate_zone": "删除重复的区域: {name}", "info.removing_empty_time_range_node": "移除空的time-range节点", "info.removing_empty_time_range_node_final": "最终清理：移除空的time-range节点", "info.removing_empty_xml_element": "信息: removing empty xml element", "info.replaced_n_with_name_element": "已将n元素替换为name元素", "info.report_path": "转换报告将保存到: {file}", "info.report_saved": "保存转换报告: {file}", "info.reverted_to_pre_repair_xml": "信息: reverted to pre repair xml", "info.root_child_count": "信息: root child count", "info.root_config_element_found": "找到根配置元素", "info.root_element_namespace_set": "根元素命名空间已设置", "info.root_element_tag": "根元素标签: {tag}", "info.root_namespace_simplified": "根命名空间已简化", "info.route_default_destination": "黑洞路由 {id} 缺少目的网络，使用默认值 0.0.0.0/0", "info.route_destination_conversion": "转换路由目标: {original} -> {converted}", "info.route_destination_converted": "转换路由目标: {src} -> {dst}", "info.route_processing": "处理静态路由 {id}: {dest} -> {gateway}", "info.routes_processed": "提取并处理了 {count} 条静态路由", "info.routes_result": "静态路由处理结果: {success} 成功, {failed} 失败", "info.routing_node_added": "已添加routing节点，XML: {xml}", "info.save_report": "转换报告已保存到: {file}", "info.saving_xml_config": "保存XML配置到: {file_path}", "info.schedule_conversion_complete": "信息: schedule conversion complete", "info.schedule_conversion_start": "信息: schedule conversion start", "info.schedule_converted_successfully": "时间表转换成功: {name}", "info.searching_data_dir": "在整个数据目录中查找: {dir}", "info.searching_for_interfaces": "正在搜索接口", "info.security_policies_added_to_xml": "成功添加 {count} 个安全策略到XML", "info.semantic_validation_disabled": "语义验证已禁用，跳过语义检查", "info.service_aliases_loaded": "服务别名已加载: {count} 个", "info.service_group_exists": "服务组已存在: {name}", "info.service_group_member_mapped": "服务组成员映射: {original} -> {mapped} (组: {group})", "info.service_group_name_sanitized": "服务组名称已清理: {original} -> {sanitized}", "info.service_groups_processed": "服务组对象处理结果: {success} 个成功", "info.service_groups_processing_complete": "服务组处理完成: 共 {total} 个，成功 {success} 个，失败 {failed} 个，跳过 {skipped} 个", "info.service_mapping_config_loaded": "已加载服务映射配置，共 {count} 个服务", "info.service_mapping_loaded": "服务映射已加载: {count} 个", "info.service_mapping_saved": "服务映射已保存", "info.service_merge_applied": "服务合并已应用: {count} 个服务已合并", "info.service_objects_processed": "服务对象处理结果: {success} 成功, {failed} 失败", "info.service_objects_processing_complete": "服务对象处理完成: 共 {total} 个，成功 {success} 个，失败 {failed} 个，跳过 {skipped} 个", "info.service_objects_processing_complete_with_mapping": "服务对象处理完成，共 {total} 个，成功 {success} 个，失败 {failed} 个，跳过 {skipped} 个", "info.service_processed": "提取并处理了 {count} 个服务对象", "info.service_ranges_not_supported": "警告：当前生成器不支持服务范围", "info.service_result": "服务对象处理结果: {success} 成功, {failed} 失败", "info.set_admin_distance": "设置管理距离: {distance}", "info.set_blackhole_route": "设置黑洞路由", "info.set_gateway": "设置网关: {gateway}", "info.set_next_hop": "设置下一跳: {hop}", "info.set_route_destination": "设置路由目标网络: {destination}", "info.setting_service_groups": "设置 {count} 个服务组到生成器", "info.setting_time_ranges_legacy": "设置 {count} 个时间对象到传统生成器", "info.setting_time_ranges_to_legacy_generator": "设置 {count} 个时间对象到传统生成器", "info.simplifying_root_namespace": "简化根元素命名空间", "info.skipped_duplicate_range": "跳过重复的时间范围：{name}", "info.skipped_interface_detail": "  - {name}: {reason}", "info.skipped_interfaces_list": "以下接口被跳过:", "info.skipping_none_time_range": "跳过名为 'none' 的特殊时间对象 {name}", "info.skipping_unmapped_interface": "跳过未映射的接口: {interface}", "info.start_adding_nat_rules_to_xml": "开始添加NAT规则到XML，共 {count} 个", "info.start_adding_policy_rules_to_xml": "开始添加策略规则到XML，共 {count} 个", "info.start_conversion": "开始转换配置文件", "info.start_encryption": "开始加密Config目录: {dir}", "info.start_generate_ntos_xml": "开始生成NTOS XML配置", "info.start_generate_target_xml": "开始生成目标XML配置...", "info.start_generate_xml": "开始生成XML配置", "info.start_parsing": "开始解析 {vendor} 配置", "info.start_processing_all_config": "开始处理所有配置数据", "info.start_processing_interfaces": "开始处理接口配置", "info.start_processing_nat_rules": "开始处理NAT规则，共 {count} 个", "info.start_vendor_conversion": "开始从 {vendor} 配置转换到 {target}", "info.start_verification": "开始验证配置文件", "info.start_verify_vendor_config": "开始验证{vendor}配置文件: {file}", "info.start_yang_generation": "开始使用YANG模型生成配置", "info.starting_ntos_xml_generation": "开始生成NTOS XML配置", "info.starting_xml_generation": "开始生成XML配置", "info.static_node_not_found": "未找到static节点，创建一个新的", "info.static_route_created_success": "静态路由创建成功: {destination}", "info.static_route_exists": "静态路由已存在: {destination}", "info.static_routes_complete": "静态路由处理完成: 共 {total} 条，成功 {success} 条，失败 {failed} 条，跳过 {skipped} 条", "info.static_routes_processed": "静态路由处理结果: {success} 成功, {failed} 失败", "info.static_routes_processing_complete": "静态路由处理完成，共 {total} 条，成功 {success} 条，失败 {failed} 条，跳过 {skipped} 条", "info.static_tag_added": "添加static标签到routing节点", "info.subinterface_mapped": "子接口映射: {raw_name} -> {mapped_name}", "info.subinterface_mapping": "子接口映射: {raw_name} -> {name}", "info.subinterface_mapping_complete": "飞塔子接口处理完成，现在共有 {count} 个接口", "info.subinterface_parsing_result": "子接口解析结果: {result}", "info.subinterface_smart_mapped": "智能子接口映射: {original} -> {mapped} (类型: {type})", "info.target_model": "目标型号: {model}", "info.target_version": "目标版本: {version}", "info.temp_directory_created": "创建临时目录: {directory}", "info.temp_directory_deleted": "删除临时目录: {directory}", "info.template_absolute_path": "模板文件绝对路径: {path}", "info.template_dir": "模板文件目录: {dir}", "info.template_dir_created": "已创建模板目录: {dir}", "info.template_loaded_success": "成功加载模板文件: {path}", "info.template_loaded_successfully": "成功加载模板文件: {path}", "info.text_file_saved": "文本文件已保存至: {file_path}", "info.time_range_converted": "时间对象 {name} 已转换", "info.time_range_empty_skipped": "时间对象 {name} 没有有效的时间或星期几设置，已跳过", "info.time_range_exists": "时间对象 {name} 已存在", "info.time_range_no_type_using_periodic": "时间对象 {name} 未指定类型，使用默认周期性类型", "info.time_ranges_processed": "已处理时间对象，成功: {success}，失败: {failed}", "info.time_ranges_processing_complete": "时间对象处理完成，总计: {total}，成功: {success}，失败: {failed}，跳过: {skipped}", "info.tool_started": "多厂商配置转换工具已启动，模式: {mode}, 厂商: {vendor}, 语言: {language}", "info.transparent_mode_config": "透明模式配置 - 管理接口: {mgmt}, 桥接接口数量: {bridge_count}", "info.transparent_mode_detected": "检测到透明模式配置", "info.undefined_zone_interface_set_non_required": "未定义区域的接口 {interface} 被设置为非必需，NTOS系统不支持undefined接口角色", "info.undefined_zone_interface_set_required": "接口 {interface} 使用了未定义区域，被设置为必需，将被转换为LAN接口角色", "info.update_interface_config": "更新接口配置: {data}", "info.update_link_interface": "更新link-interface: {old} -> {new}", "info.update_parent_interface": "更新父接口名称: {original} -> {updated}", "info.update_subinterface_name": "更新子接口名称: {old} -> {new}", "info.update_vlan_subinterface_config": "更新VLAN子接口配置: {name}", "info.updated_interface": "更新接口: {name}", "info.updated_service_group": "已更新服务组: {name}，成员数: {members}", "info.updating_existing_address": "更新现有地址对象: {name}", "info.updating_existing_address_group": "更新现有地址组: {name}", "info.updating_existing_interface": "更新现有接口: {name}", "info.updating_existing_service": "更新现有服务对象: {name}", "info.updating_existing_service_group": "更新现有服务组: {name}", "info.updating_existing_subinterface": "更新现有子接口: {name}", "info.updating_existing_vlan": "更新现有VLAN接口: {name}", "info.updating_existing_zone": "更新现有区域: {name}", "info.updating_zone": "更新区域: {name}", "info.user_log_file_created": "用户日志将写入文件: {file}", "info.user_log_to_file": "用户日志将写入文件: {file}", "info.using_alternative_template": "使用替代模板文件: {path}", "info.using_batch_generation_strategy": "使用批处理生成策略，批次大小: {batch_size}", "info.using_config_root": "使用的Config根目录: {path}", "info.using_default_language": "信息: using default language", "info.using_enhanced_policy_processor": "使用增强策略处理器", "info.using_existing_time_range": "使用已存在的时间对象: {name}", "info.using_extended_generator_for_dns": "检测到DNS配置，使用扩展生成器", "info.using_fallback_xml_structure": "信息: using fallback xml structure", "info.using_legacy_generation": "使用传统生成方式", "info.using_legacy_generation_strategy": "使用传统生成策略", "info.using_legacy_generator": "使用传统生成器", "info.using_minimal_valid_xml": "信息: using minimal valid xml", "info.using_namespace_mapping": "使用命名空间映射", "info.using_new_architecture": "使用新架构进行转换", "info.using_parser": "使用 {vendor} 解析器", "info.using_provided_service_obj_node": "使用传入的service-obj节点", "info.using_provided_vrf_node": "使用传入的VRF节点", "info.using_service_processor_with_mapping": "使用预定义服务映射的服务处理器", "info.using_standard_generator": "使用标准生成器", "info.using_template": "使用模板文件: {file}", "info.using_template_file": "使用模板文件: {path}", "info.using_unfixed_xml_file": "使用未修复的XML文件: {temp_file}", "info.validating_interface_mapping": "正在验证接口映射...", "info.validating_interface_model": "正在验证接口与设备型号 {model} 的兼容性", "info.validating_xml": "验证XML文件: {file}", "info.validation_report_saved": "验证失败报告已保存到: {file}", "info.vendor_mapping_loaded": "厂商映射已加载: {vendor}，数量: {count}", "info.verification_passed": "验证通过", "info.verification_success": "配置文件验证通过", "info.verify_fortigate_config": "验证飞塔配置文件: {file}", "info.verifying_config_before_conversion": "转换前验证配置", "info.vlan_subinterface_created": "VLAN子接口创建完成，最终名称: {name}", "info.vlan_subinterface_names": "VLAN子接口: 原始名称={raw}, NTOS名称={ntos}", "info.vrf_name_not_main": "VRF名称不是'main'，当前值: {current}", "info.vrf_node_created": "VRF节点已创建", "info.vrf_node_found": "找到VRF节点", "info.vrf_node_found_with_full_uri": "使用完整URI找到VRF节点", "info.vrf_node_found_with_local_name": "使用local-name()函数找到VRF节点", "info.vrf_node_found_without_namespace": "找到不带命名空间的VRF节点", "info.vrf_node_not_found_creating_new": "未找到VRF节点，创建新节点", "info.warnings_report_saved": "保存警告报告: {file}", "info.write_xml_success": "成功写入XML配置文件: {file}", "info.xml_config_saved_with_fixed_namespaces": "命名空间已修复，最终XML配置已保存到: {file_path}", "info.xml_file_written": "成功写入XML配置文件: {file}", "info.xml_generation_complete": "XML生成完成", "info.xml_generation_complete_with_validation": "XML生成完成，错误: {errors}，警告: {warnings}", "info.xml_validation_passed": "XML验证通过", "info.xml_validation_success": "XML验证通过", "info.yang_generation_complete": "YANG模型生成配置完成", "info.yang_generation_fallback": "YANG生成失败，使用最小化XML结构", "info.yang_generator_initialized": "初始化增强YANG生成器，模型: {model}, 版本: {version}", "info.yang_validation_passed": "配置文件已通过YANG模型验证", "info.zone_extracted": "提取到区域 {zone}，包含 {interfaces} 个接口", "info.zone_interface_mapped": "区域 {zone} 的接口 {interface} 将映射为 {mapped}", "info.zone_interface_mapping_complete": "区域接口映射完成", "info.zone_interfaces_summary": "区域 {zone} 添加了 {valid} 个有效接口，跳过了 {skipped} 个接口", "info.zone_mapping_complete": "区域接口映射完成", "info.zone_skipped_interfaces_count": "区域处理跳过了 {count} 个接口", "info.zones_extracted": "从配置文件中提取了 {count} 个区域", "info.zones_found": "在配置文件中找到 {count} 个区域", "info.zones_processing_complete": "区域处理完成: {processed} 个区域处理成功", "interface.destination": "目标接口 {name}", "interface.ip": "IP地址", "interface.mask": "子网掩码", "interface.name": "接口名称", "interface.source": "源接口 {name}", "interface.status": "接口状态", "interface.type": "接口类型", "interface.type.aggregate": "汇聚接口", "interface.type.dmz": "DMZ接口", "interface.type.emac_vlan": "EMAC VLAN接口", "interface.type.fext_wan": "FortiExtender接口", "interface.type.hard_switch": "硬件交换口", "interface.type.hdlc": "T1/E1接口", "interface.type.lan": "LAN接口", "interface.type.loopback": "回环接口", "interface.type.physical": "物理接口", "interface.type.port": "端口", "interface.type.pppoe": "PPPoE接口", "interface.type.redundant": "冗余接口", "interface.type.subinterface": "子接口", "interface.type.switch": "软件交换口", "interface.type.switch_vlan": "交换VLAN接口", "interface.type.tunnel": "隧道接口", "interface.type.unknown": "未知接口", "interface.type.vap_switch": "VAP接口", "interface.type.vdom_link": "VDOM链路", "interface.type.vlan": "VLAN接口", "interface.type.vxlan": "VXLAN接口", "interface.type.wan": "WAN接口", "interface.type.wl_mesh": "WLAN Mesh接口", "interface_handler.access_control_already_exists": "interface handler: access control already exists", "interface_handler.adding_interfaces_to_xml": "interface handler: adding interfaces to xml", "interface_handler.adding_pppoe_config": "添加PPPoE配置: {interface}", "interface_handler.assigned_pppoe_tunnel_id": "interface handler: assigned pppoe tunnel id", "interface_handler.cleanup_invalid_role_tag": "interface handler: cleanup invalid role tag", "interface_handler.configured_access_control": "interface handler: configured access control", "interface_handler.configured_dhcp_mode": "interface handler: configured dhcp mode", "interface_handler.configured_download_bandwidth": "interface handler: configured download bandwidth", "interface_handler.configured_ipv6_dhcp": "interface handler: configured ipv6 dhcp", "interface_handler.configured_ipv6_static": "interface handler: configured ipv6 static", "interface_handler.configured_static_ip": "interface handler: configured static ip", "interface_handler.configured_upload_bandwidth": "interface handler: configured upload bandwidth", "interface_handler.configuring_pppoe_optimized": "interface handler: configuring pppoe optimized", "interface_handler.constructed_from_elements": "从元素构建名称: link={link}, VLAN={vlan}, 最终名称={final}", "interface_handler.create_fortinet_interface": "interface handler: create fortinet interface", "interface_handler.create_new_interface": "interface handler: create new interface", "interface_handler.create_new_physical": "interface handler: create new physical", "interface_handler.create_new_vlan": "interface handler: create new vlan", "interface_handler.create_new_vlan_interface": "创建新VLAN接口: {name}", "interface_handler.created_name_element": "已创建名称元素: {name}", "interface_handler.created_new_access_control": "创建新的访问控制节点", "interface_handler.device_identification_noted": "interface handler: device identification noted", "interface_handler.error.all_tunnel_ids_used": "interface handler: error: all tunnel ids used", "interface_handler.error.failed_to_use_common_pppoe": "使用通用PPPoE失败: {error}", "interface_handler.error.netmask_conversion": "interface handler: error: netmask conversion", "interface_handler.extracted_from_name": "从名称提取: {name} (父接口: {parent}, VLAN: {vlan})", "interface_handler.fortinet_interface_created": "interface handler: fortinet interface created", "interface_handler.fortinet_raw_config_processed": "interface handler: fortinet raw config processed", "interface_handler.found_existing_access_control": "找到现有的访问控制节点", "interface_handler.generated_name": "生成名称: 父接口={parent}, VLAN={vlan}, 最终名称={final}", "interface_handler.info.configuring_dhcp_mode": "interface handler: info: configuring dhcp mode", "interface_handler.info.configuring_ipv6_dhcp_mode": "interface handler: info: configuring ipv6 dhcp mode", "interface_handler.info.configuring_pppoe_mode": "配置PPPoE模式: {mode}", "interface_handler.info.create_pppoe_config": "interface handler: info: create pppoe config", "interface_handler.info.updating_dhcp_mode": "interface handler: info: updating dhcp mode", "interface_handler.info.updating_ipv6_dhcp_mode": "interface handler: info: updating ipv6 dhcp mode", "interface_handler.interface_created": "interface handler: interface created", "interface_handler.interface_mode_dhcp": "interface handler: interface mode dhcp", "interface_handler.interface_mode_disabled": "interface handler: interface mode disabled", "interface_handler.interface_mode_pppoe": "interface handler: interface mode pppoe", "interface_handler.interface_mode_static": "interface handler: interface mode static", "interface_handler.interface_updated": "interface handler: interface updated", "interface_handler.interfaces_added_to_xml": "interface handler: interfaces added to xml", "interface_handler.invalid_vlan_id": "无效的VLAN ID: {vlan_id}，接口: {name}", "interface_handler.ipv6_disabled_for_pppoe": "interface handler: ipv6 disabled for pppoe", "interface_handler.mapped_parent_interface": "interface handler: mapped parent interface", "interface_handler.missing_parent_interface": "interface handler: missing parent interface", "interface_handler.name_from_link_and_vlan": "从链路和VLAN生成名称: link={link}, VLAN={vlan}, 最终名称={final}", "interface_handler.no_mapping_found": "接口 {name} 没有找到对应的映射，使用原名", "interface_handler.no_mapping_provided": "处理子接口 {subinterface} 时未提供接口映射表", "interface_handler.not_a_subinterface": "接口 {name} 不是子接口格式", "interface_handler.parent_interface_from_name": "interface handler: parent interface from name", "interface_handler.parent_interface_in_mapping_only": "父接口 {parent} 仅在映射表中存在，子接口 {subinterface} 处理继续", "interface_handler.parent_interface_mapped": "interface handler: parent interface mapped", "interface_handler.parent_interface_not_found": "警告: 子接口 {subinterface} 的父接口 {parent} 未找到", "interface_handler.parent_interface_not_mapped": "子接口 {subinterface} 的父接口 {parent} 没有映射关系", "interface_handler.parent_interface_not_mapped_in_parse": "解析子接口 {subinterface} 时，父接口 {parent} 没有映射关系", "interface_handler.parent_interface_not_physical": "错误: 子接口 {subinterface} 的父接口 {parent} 不是物理接口", "interface_handler.parsed_fortinet_subinterface": "已解析飞塔子接口: {name}", "interface_handler.pppoe_configured": "interface handler: pppoe configured", "interface_handler.pppoe_configured_using_common": "使用通用方法配置PPPoE", "interface_handler.pppoe_password_configured": "interface handler: pppoe password configured", "interface_handler.pppoe_password_set": "interface handler: pppoe password set", "interface_handler.pppoe_role_set_to_wan": "interface handler: pppoe role set to wan", "interface_handler.pppoe_username_configured": "interface handler: pppoe username configured", "interface_handler.pppoe_username_set": "PPPoE用户名已设置: {username}", "interface_handler.reset_tunnel_ids": "interface handler: reset tunnel ids", "interface_handler.role_defaulted_to_lan": "接口 {name} 的角色设置为默认值 'lan'", "interface_handler.role_defaulted_to_wan": "接口 {name} 的角色设置为默认值 'wan'", "interface_handler.security_mode_noted": "interface handler: security mode noted", "interface_handler.skip_interface_without_name": "interface handler: skip interface without name", "interface_handler.skipped_subinterfaces": "跳过了 {count} 个子接口", "interface_handler.skipping_invalid_interface": "跳过无效接口 {name}: {reason}", "interface_handler.subinterface_mapped": "子接口 {raw_name} 映射为 {mapped_name}，父接口为 {parent}", "interface_handler.subinterface_parse_failed": "子接口 {subinterface} 解析失败", "interface_handler.update_existing_interface": "interface handler: update existing interface", "interface_handler.update_existing_physical": "interface handler: update existing physical", "interface_handler.update_existing_vlan": "interface handler: update existing vlan", "interface_handler.update_physical_interface": "interface handler: update physical interface", "interface_handler.update_physical_interface.complete": "interface handler: update physical interface: complete", "interface_handler.update_vlan_interface": "更新VLAN接口: {name}", "interface_handler.updating_processed_access_control": "更新处理后的访问控制数据", "interface_handler.updating_raw_allowaccess": "更新原始allowaccess数据", "interface_handler.using_existing_name": "使用现有名称: {name}", "interface_handler.using_fortinet_optimizer": "interface handler: using fortinet optimizer", "interface_handler.using_processed_access_control": "使用处理后的访问控制数据", "interface_handler.using_raw_allowaccess": "使用原始allowaccess数据", "interface_handler.vlan_interface_created": "VLAN接口已创建: {name}", "interface_handler.vlan_interface_updated": "interface handler: vlan interface updated", "interface_handler.warning.defaultgw_disable_not_supported": "警告: 接口 {interface} 的默认网关设置为 'disable'，此设置在NTOS中不支持", "interface_handler.warning.generated_random_name": "警告: 接口缺少名称，已生成随机名称: {name}", "interface_handler.warning.invalid_interface_data": "警告: 接口 {name} 的数据格式无效", "interface_handler.warning.missing_pppoe_credentials": "警告: 缺少PPPoE凭据", "interface_handler.warning.non_root_vdom": "警告: 接口 {interface} 使用了非root虚拟域 '{vdom}'，跳过此接口的转换", "interface_handler.warning.unknown_role": "警告: 接口 {interface} 使用了未知的角色 '{role}'，只支持 'lan' 和 'wan'", "interface_handler.warning.unsupported_allowaccess": "警告: 接口 {interface} 使用了不支持的访问方式 '{access}'，只支持 ping、https 和 ssh", "interface_handler.warning.unsupported_interface_type": "警告: 接口 {interface} 使用了不支持的接口类型 '{type}'，只支持物理接口和VLAN子接口", "interface_handler.warning.unsupported_role": "警告: 接口 {interface} 使用了不支持的角色 '{role}'，只支持 'lan' 和 'wan'", "interface_handler.working_mode_route_default": "[待翻译] interface_handler.working_mode_route_default", "interface_handler.working_mode_route_nat": "[待翻译] interface_handler.working_mode_route_nat", "interface_integrator.added_ipv4_config": "添加IPv4配置: {interface}", "interface_integrator.added_new_interface": "添加新接口: {name}", "interface_integrator.added_new_zone": "添加新区域: {name}", "interface_integrator.added_zone_interface_member": "添加区域接口成员: 区域={zone}, 接口={interface}", "interface_integrator.configured_access_control": "配置访问控制: {protocols}", "interface_integrator.configured_bandwidth": "配置带宽: 上行={upstream}, 下行={downstream}", "interface_integrator.configured_dhcp": "配置DHCP", "interface_integrator.configured_ipv6": "配置IPv6: {address}", "interface_integrator.configured_pppoe": "配置PPPoE: {username}", "interface_integrator.configured_static_ip": "配置静态IP: {ip}/{netmask}", "interface_integrator.created_interface_container": "创建接口容器", "interface_integrator.created_security_zone_container": "创建安全区域容器", "interface_integrator.found_new_field": "发现新字段: {field} = {value}", "interface_integrator.integrating_single_interface": "集成单个接口: {name}", "interface_integrator.integrating_single_zone": "集成单个区域: {name}", "interface_integrator.interface_element_update_failed": "接口元素更新失败: {error}", "interface_integrator.interface_fragment_inserted": "接口片段已插入，数量: {count}", "interface_integrator.interface_fragment_integration_failed": "接口片段集成失败: {error}", "interface_integrator.interface_fragment_merge_failed": "接口片段合并失败: {error}", "interface_integrator.interface_fragment_parse_failed": "接口片段解析失败: {error}", "interface_integrator.interface_integration_completed": "接口集成完成，接口数量: {interface_count}，区域数量: {zone_count}", "interface_integrator.interface_integration_exception": "接口集成异常: {error}", "interface_integrator.interface_mapped": "接口映射: {original} -> {mapped}", "interface_integrator.interface_mapping_failed": "接口映射失败: {interface}, 错误: {error}", "interface_integrator.interface_mapping_loaded": "接口映射已加载，数量: {count}", "interface_integrator.interface_update_completed": "接口更新完成: {name}", "interface_integrator.interface_update_failed": "接口更新失败: {name}, 错误: {error}", "interface_integrator.merged_zone_interface_member": "合并区域接口成员: 区域={zone}, 接口={interface}", "interface_integrator.modified_ipv4_config": "修改IPv4配置: {interface}", "interface_integrator.no_interface_data": "没有接口数据", "interface_integrator.no_interface_data_to_integrate": "没有需要集成的接口数据", "interface_integrator.physical_interface_creation_failed": "物理接口创建失败: {name}, 错误: {error}", "interface_integrator.single_interface_integration_failed": "单个接口集成失败: {name}, 错误: {error}", "interface_integrator.single_zone_integration_failed": "单个区域集成失败: {name}, 错误: {error}", "interface_integrator.start_integrating_interface_fragment": "开始集成接口片段", "interface_integrator.start_updating_existing_interface": "开始更新现有接口: {name}", "interface_integrator.updated_access_control": "更新访问控制: {interface}", "interface_integrator.updated_bandwidth": "更新带宽: {interface}, 类型: {type}", "interface_integrator.updated_existing_interface": "更新现有接口: {name}", "interface_integrator.updated_existing_zone": "更新现有区域: {name}", "interface_integrator.updated_field": "更新字段: 接口={interface}, 字段={field}, 值={value}", "interface_integrator.vlan_interface_creation_failed": "VLAN接口创建失败: {name}, 错误: {error}", "interface_integrator.zone_element_creation_failed": "区域元素创建失败: {name}, 错误: {error}", "interface_integrator.zone_update_failed": "区域更新失败: {error}", "interface_mapping.json": "[待翻译] interface_mapping.json", "interface_processing_stage.add_mapping_suggestion": "请在映射文件中添加该接口的映射关系", "interface_processing_stage.aggregate_interface": "聚合接口", "interface_processing_stage.bridge_mode": "桥接模式", "interface_processing_stage.completed": "接口处理完成，转换: {converted}，安全区域: {zones}", "interface_processing_stage.conversion_failed": "接口转换失败", "interface_processing_stage.converted_interfaces": "成功转换: {count}个", "interface_processing_stage.debug_interface_count": "调试: 处理接口数量 {count}", "interface_processing_stage.debug_mapping_check": "调试: 检查接口映射 {interface} -> {result}", "interface_processing_stage.debug_working_mode": "调试: 接口工作模式 {interface} -> {mode}", "interface_processing_stage.description": "接口处理阶段", "interface_processing_stage.dhcp_enabled": "DHCP启用", "interface_processing_stage.dmz_zone": "DMZ区域", "interface_processing_stage.failed": "接口处理失败", "interface_processing_stage.failed_interfaces": "转换失败: {count}个", "interface_processing_stage.final_mapping_saved": "[待翻译] interface_processing_stage.final_mapping_saved", "interface_processing_stage.interface_failed": "接口 {name} 处理失败: {reason}", "interface_processing_stage.interface_type": "接口", "interface_processing_stage.invalid_interface_format": "无效的接口格式: {interface}", "interface_processing_stage.invalid_vlan_id": "无效的VLAN ID: {vlan_id}", "interface_processing_stage.lan_role": "LAN接口", "interface_processing_stage.logical_interface_skip": "接口 {name} 是逻辑接口，不需要转换", "interface_processing_stage.loopback_interface": "环回接口", "interface_processing_stage.mapping_applied": "应用接口映射: {source} -> {target}", "interface_processing_stage.mapping_file_saved": "接口映射文件已保存: {file}", "interface_processing_stage.mapping_file_verified": "接口映射文件验证成功: {file}", "interface_processing_stage.mgmt_role": "管理接口", "interface_processing_stage.missing_interface_mapping": "缺少接口映射", "interface_processing_stage.no_interfaces": "没有接口需要处理", "interface_processing_stage.no_mapping_found": "未找到接口映射: {interface}", "interface_processing_stage.no_mapping_message": "接口 {name} 没有映射关系", "interface_processing_stage.parent_interface_not_found": "未找到父接口: {parent}", "interface_processing_stage.physical_interface": "物理接口", "interface_processing_stage.pppoe_enabled": "PPPoE启用", "interface_processing_stage.processing_error": "接口处理错误: {error}", "interface_processing_stage.processing_failed": "接口处理失败: {error}", "interface_processing_stage.route_mode": "路由模式", "interface_processing_stage.skipped_interfaces": "跳过转换: {count}个", "interface_processing_stage.starting": "开始处理接口配置", "interface_processing_stage.static_ip": "静态IP", "interface_processing_stage.subinterface_mapping_generated": "生成子接口映射: {parent}.{vlan} -> {target}", "interface_processing_stage.total_interfaces": "总接口数: {count}", "interface_processing_stage.trust_zone": "信任区域", "interface_processing_stage.tunnel_interface": "隧道接口", "interface_processing_stage.untrust_zone": "非信任区域", "interface_processing_stage.vlan_interface": "VLAN接口", "interface_processing_stage.wan_role": "WAN接口", "interface_processing_stage.xml_fragment_length": "XML片段长度: {length}", "interface_processing_stage.xml_generation_completed": "接口XML生成完成", "interface_processing_stage.xml_generation_started": "开始生成接口XML", "interface_processing_stage.zones_created": "创建安全区域: {zones}个", "interface_processor.aggregate_type_converted": "聚合接口 '{interface}' 转换为物理接口处理", "interface_processor.debug_add_valid_mapping": "DEBUG: 添加有效接口映射: {original} -> {mapped} (类型: {type})", "interface_processor.debug_bridge_interface_mode": "DEBUG: 接口 {name}({mapped}) 被识别为桥接接口，设置为桥接模式", "interface_processor.debug_converted_interfaces_count": "DEBUG: converted_interfaces数量: {count}", "interface_processor.debug_converted_interfaces_keys": "DEBUG: converted_interfaces键: {keys}", "interface_processor.debug_default_route_mode": "DEBUG: 接口 {name}({mapped}) 未在分类中找到，默认设置为路由模式", "interface_processor.debug_extract_parent_interface": "DEBUG: 从接口名称提取父接口: {name} -> {parent}", "interface_processor.debug_extract_vlan_id": "DEBUG: 从接口名称提取VLAN ID: {name} -> {vlan_id}", "interface_processor.debug_file_exists_verification": "DEBUG: 验证成功，文件确实存在: {file}", "interface_processor.debug_file_not_exists": "DEBUG: 验证失败，文件不存在: {file}", "interface_processor.debug_final_mapping": "DEBUG: 生成最终接口映射: {mapping}", "interface_processor.debug_final_mapping_content": "DEBUG: 最终映射内容: {mapping}", "interface_processor.debug_final_mapping_save_failed": "DEBUG: 最终映射文件保存失败", "interface_processor.debug_final_mapping_saved": "DEBUG: 最终映射文件已保存到: {file}", "interface_processor.debug_generate_subinterface_mapping": "DEBUG: 生成子接口映射: {original} -> {mapped} (父接口: {parent} -> {parent_mapped})", "interface_processor.debug_generate_xml_for_interface": "DEBUG: 为接口 {name} 生成XML", "interface_processor.debug_interface_mapping_check": "DEBUG: 接口 {name} 映射检查结果: {result}", "interface_processor.debug_interface_mapping_detail": "DEBUG: 接口映射 {index}: {original} -> {mapped} (类型: {type})", "interface_processor.debug_interface_mapping_param": "DEBUG: interface_mapping参数: {mapping}", "interface_processor.debug_interface_xml_complete": "DEBUG: 接口 {name} XML生成完成", "interface_processor.debug_internal_vlan_mapping": "DEBUG: 使用内部映射生成VLAN接口名称: {original} -> {mapped}", "interface_processor.debug_mapping_generation_complete": "DEBUG: 接口映射生成完成，共 {count} 个有效映射", "interface_processor.debug_mgmt_access_control": "DEBUG: 为管理接口 {name} 设置访问控制: ping=True, https=True, ssh=True", "interface_processor.debug_mgmt_interface_route_mode": "DEBUG: 接口 {name}({mapped}) 被识别为管理接口，设置为路由模式", "interface_processor.debug_mgmt_ip_cidr_format": "DEBUG: 为管理接口 {name} 设置管理IP (CIDR格式): {ip}", "interface_processor.debug_mgmt_ip_mask_format": "DEBUG: 为管理接口 {name} 设置管理IP (IP/掩码格式): {original} -> {converted}", "interface_processor.debug_mgmt_ip_space_format": "DEBUG: 为管理接口 {name} 设置管理IP (IP 掩码格式): {original} -> {converted}", "interface_processor.debug_no_converted_interfaces": "DEBUG: 没有转换的接口，返回空XML片段", "interface_processor.debug_output_file_path": "DEBUG: 输出文件路径: {path}", "interface_processor.debug_parent_mapping_method_deprecated": "DEBUG: _get_parent_interface_mapped_name方法被调用，但建议使用参数传递映射", "interface_processor.debug_parent_no_mapping": "DEBUG: 父接口 {parent} 没有有效映射，子接口 {original} 将在后续处理中生成映射", "interface_processor.debug_parent_no_mapping_use_original": "DEBUG: 父接口 {parent} 未找到映射，使用原始名称", "interface_processor.debug_physical_interfaces_keys": "DEBUG: physical_interfaces键: {keys}", "interface_processor.debug_process_physical_interface": "DEBUG: 处理物理接口 {name}, is_subinterface: {is_sub}", "interface_processor.debug_processing_stage_info": "DEBUG: 接口处理阶段调试信息", "interface_processor.debug_saved_mapping_content": "DEBUG: 保存的映射内容: {content}", "interface_processor.debug_skip_subinterface": "DEBUG: 跳过子接口 {name}", "interface_processor.debug_skip_subinterface_no_parent": "DEBUG: 跳过子接口 {original}，父接口 {parent} 没有映射关系", "interface_processor.debug_start_interface_mapping": "DEBUG: 开始生成接口映射，共 {count} 个接口", "interface_processor.debug_start_physical_processing": "DEBUG: 开始处理物理接口，数量: {count}", "interface_processor.debug_start_xml_generation": "DEBUG: 开始生成接口XML片段", "interface_processor.debug_subinterface_detail": "DEBUG: 子接口详细信息 - 原始名称: {original}, 父接口: {parent}, VLAN ID: {vlan_id}, 接口配置: {config}", "interface_processor.debug_task_data_dir": "DEBUG: 任务数据目录: {dir}", "interface_processor.debug_total_interfaces": "DEBUG: 总接口数量: {count}", "interface_processor.debug_vlan_interface_info": "DEBUG: VLAN接口 {original} 的父接口: {parent}, VLAN ID: {vlan_id}, 映射名称: {mapped}", "interface_processor.debug_vlan_interface_mapping": "DEBUG: 使用传入映射生成VLAN接口名称: {original} -> {mapped} (父接口: {parent} -> {mapped_parent})", "interface_processor.debug_vlan_interfaces_keys": "DEBUG: vlan_interfaces键: {keys}", "interface_processor.debug_xml_fragment_length": "DEBUG: XML片段生成结果 - 长度: {length}", "interface_processor.debug_xml_fragment_preview": "DEBUG: XML片段前100字符: {preview}", "interface_processor.debug_xml_generation_complete": "DEBUG: 接口XML片段生成完成", "interface_processor.debug_xml_generation_stats": "DEBUG: XML片段生成 - converted_interfaces: {converted}, vlan_interfaces: {vlan}, physical_interfaces: {physical}", "interface_processor.direct_mapping_used": "使用直接映射: {source} -> {target}", "interface_processor.empty_mapping_config": "接口映射配置为空，跳过接口: {name}", "interface_processor.empty_mapping_config_use_original": "接口映射配置为空，使用原始名称: {name}", "interface_processor.empty_mapping_skip": "接口映射表为空，跳过接口: {name}", "interface_processor.empty_mapping_use_original": "接口映射表为空，使用原始名称: {name}", "interface_processor.filter_logical_interface": "过滤逻辑接口: {name}", "interface_processor.filter_modem_interface": "过滤modem接口: {name}", "interface_processor.filter_tunnel_interface": "过滤tunnel接口: {name}", "interface_processor.filter_virtual_interface": "过滤虚拟接口: {name}", "interface_processor.flat_mapping_check": "使用扁平格式接口映射检查: {count} 个映射", "interface_processor.flat_mapping_use": "使用扁平格式接口映射: {count} 个映射", "interface_processor.loopback_type_converted": "环回接口 '{interface}' 转换为物理接口处理", "interface_processor.mapping_validation_passed": "接口映射验证通过: {source} -> {target}", "interface_processor.nested_mapping_check": "使用嵌套格式接口映射检查: {count} 个映射", "interface_processor.nested_mapping_use": "使用嵌套格式接口映射: {count} 个映射", "interface_processor.no_mapping_found": "接口 {name} 没有找到映射关系", "interface_processor.no_valid_mapping_use_original": "没有找到有效映射，使用原始名称: {name}", "interface_processor.skip_logical_interface": "跳过逻辑接口: {name}", "interface_processor.skip_no_mapping_interface": "跳过没有映射关系的接口: {name}", "interface_processor.source_interface_exists": "源接口 {interface} 存在（已有配置）", "interface_processor.source_interface_validation_default": "源接口 {interface} 存在性验证通过（处理阶段默认存在）", "interface_processor.source_interface_validation_error": "源接口 {interface} 存在性验证失败: {error}", "interface_processor.source_interface_validation_failed": "源接口验证失败: {interface} 不在FortiGate配置中", "interface_processor.subinterface_mapping_used": "使用子接口映射: {source} -> {target}", "interface_processor.switch_type_converted": "交换接口 '{interface}' 转换为物理接口处理", "interface_processor.target_interface_empty": "目标接口名称为空", "interface_processor.target_interface_format_invalid": "目标接口 {interface} 格式不符合NTOS规范", "interface_processor.target_interface_format_valid": "目标接口 {interface} 格式验证通过", "interface_processor.target_interface_validation_failed": "目标接口验证失败: {interface} 格式不符合NTOS规范", "interface_processor.tunnel_type_converted": "隧道接口 '{interface}' 转换为物理接口处理", "interface_processor.type_conversion_applied": "接口 '{interface}' 类型转换: {original} -> {converted}", "interface_processor.using_converted_type": "接口 '{interface}' 使用转换后的类型: {original} -> {converted}", "interface_processor.vlan_mapping_used": "使用VLAN接口映射: {source} -> {target}", "interface_processor.warning_extract_vlan_id_failed": "WARNING: 无法从接口名称提取VLAN ID: {name}", "interface_processor.warning_vlan_id_out_of_range": "WARNING: VLAN ID {vlan_id} 超出NTOS支持范围(1-4063)，接口: {name}", "interface_processor.warning_vlan_missing_info": "WARNING: VLAN接口 {original} 缺少父接口或VLAN ID信息", "interface_processor.xml_fragment_generated": "生成接口XML片段，包含 {vlan} 个VLAN接口和 {physical} 个物理接口", "interface_processor.xml_fragment_generation_failed": "生成接口XML片段失败: {error}", "internal_error": "内部服务错误", "invalid_configuration": "无效的配置内容", "invalid_ini": "无效的INI格式", "invalid_json": "无效的JSON格式", "invalid_properties": "无效的Properties格式", "invalid_toml": "无效的TOML格式", "invalid_xml": "无效的XML格式", "invalid_yaml": "无效的YAML格式", "issue.description": "描述", "issue.interface_compatibility": "接口兼容性", "issue.interface_count_exceeded": "接口数量超限", "issue.invalid_interfaces_desc": "有 {count} 个接口因配置无效而被跳过", "issue.invalid_interfaces_suggestion": "请查看详细日志，根据兼容性问题修改接口配置", "issue.severity": "严重程度", "issue.suggestion": "建议", "issue.type": "类型", "issue.unmapped_zone_interfaces_desc": "有 {count} 个区域接口因未找到映射而被跳过", "issue.unmapped_zone_interfaces_suggestion": "请检查区域接口名称是否正确，或添加相应的接口映射", "issue.warning": "警告", "issue.zone_interface_mapping": "区域接口映射", "key": "key", "libcrypto.so": "[待翻译] libcrypto.so", "libssl.so": "[待翻译] libssl.so", "log.critical_message": "[严重] {module}: 严重错误信息", "log.debug_message": "[调试] {module}: 调试信息", "log.error_message": "[错误] {module}: 错误信息", "log.info_message": "[信息] {module}: 信息", "log.warning_message": "[警告] {module}: 警告信息", "logger.all_logs_flushed": "所有日志已刷新", "logger.create_user_log_dir": "创建用户日志目录: {dir}", "logger.default_translator_initialized": "默认翻译器已初始化", "logger.translator_set": "翻译器已设置: {translator}", "login.microsoft.com": "[待翻译] login.microsoft.com", "login.microsoftonline.com": "[待翻译] login.microsoftonline.com", "login.windows.net": "[待翻译] login.windows.net", "lru_cache.cleared": "LRU缓存: cleared", "lru_cache.evicted": "LRU缓存: evicted", "lru_cache.initialized": "LRU缓存已初始化", "main.conversion_start": "开始转换配置文件", "main.encoding_settings_new": "编码设置: PYTHONIOENCODING={pythonioencoding}", "main.input_file": "输入文件: {file}", "main.locale_settings_new": "区域设置: LANG={lang}, LC_ALL={lc_all}", "main.mapping_file": "映射文件: {file}", "main.output_file": "输出文件: {file}", "main.program_execution_error_new": "程序执行出错: {error}", "main.target_model": "目标型号: {model}", "main.target_version": "目标版本: {version}", "main.tool_started_new": "工具启动，模式: {mode}, 厂商: {vendor}, 语言: {language}", "manual_config.interface.reason": "VLAN ID无效或其他兼容性问题", "manual_config.interface.suggestion": "请检查VLAN ID是否在有效范围(1-4063)内，或其他接口配置是否有效", "manual_config.interface.type": "接口", "manual_config.interface_zone.reason": "接口未在映射表中找到", "manual_config.interface_zone.suggestion": "请检查接口名称是否正确，或添加相应的接口映射", "manual_config.interface_zone.type": "区域接口", "manual_config.policy_rules.reason": "包含不支持的规则类型", "manual_config.policy_rules.suggestion": "检查策略类型并手动配置", "manual_config.policy_rules.type": "策略规则", "manual_config.reason.invalid_vlan": "VLAN ID无效或其他兼容性问题", "manual_config.reason.unmapped_interface": "接口未在映射表中找到", "manual_config.static_routes.reason": "包含不支持的路由类型", "manual_config.static_routes.suggestion": "检查路由类型并手动配置", "manual_config.static_routes.type": "静态路由", "manual_config.suggestion.check_mapping": "请检查接口名称是否正确，或添加相应的接口映射", "manual_config.suggestion.check_vlan": "请检查VLAN ID是否在有效范围(1-4063)内，或其他接口配置是否有效", "manual_config.type.interface": "接口", "manual_config.type.zone_interface": "区域接口", "memory_manager.initialized": "内存管理器已初始化", "memory_manager.optimization_completed": "内存管理: optimization completed", "memory_manager.starting_optimization": "内存管理: starting optimization", "memory_manager.threshold_updated": "内存管理: threshold updated", "missing_required_field": "缺少必填字段: {field}", "monitor.performance_monitoring_started": "性能监控已启动", "naf.root": "[待翻译] naf.root", "name_manager.conflict_resolved": "名称冲突已解决: {original} → {final}", "name_manager.invalid_name": "无效名称 {name}: {error}", "name_manager.name_assigned": "名称分配: {namespace} - {original} → {final}", "name_validator.ip_address_cleaned": "IP地址名称已清理: {original} → {cleaned}", "nat.added_dest_interface": "NAT规则添加目标接口: {interface}", "nat.added_dest_network": "NAT规则添加目标网络: {network}", "nat.added_source_interface": "NAT规则添加源接口: {interface}", "nat.added_source_network": "NAT规则添加源网络: {network}", "nat.added_time_range": "已添加时间范围: {value}", "nat.alg_config_added": "已添加默认ALG配置", "nat.alg_config_already_exists": "ALG配置已存在", "nat.container_created": "nat: container created", "nat.container_found": "nat: container found", "nat.dnat_dest_interface_info": "DNAT规则目标接口信息: {policy_name} - {dest_interfaces}", "nat.dnat_dest_interface_recorded": "DNAT目标接口记录: {policy_name} - {interfaces} ({reason})", "nat.dnat_match_conditions_built": "DNAT匹配条件构建完成: {policy_name} - 条件: {conditions}", "nat.dnat_naming_with_vip": "DNAT规则命名包含VIP: {policy_name}_{vip_name}", "nat.dnat_rules_created": "创建了 {dnat_rules_created} 个DNAT规则", "nat.dnat_service_added": "DNAT服务添加: {policy_name} - {service}", "nat.dnat_service_all_skipped": "DNAT服务跳过'ALL': {policy_name}", "nat.dnat_source_interface_added": "DNAT规则添加源接口匹配: {policy_name} - {interfaces}", "nat.dnat_source_interface_error": "DNAT源接口处理错误: {policy_name} - {interface}: {error}", "nat.dnat_source_interface_mapped": "DNAT源接口映射: {policy_name} - {original} → {mapped}", "nat.dnat_source_interface_mapping_failed": "DNAT源接口映射失败: {policy_name} - {interface}", "nat.dnat_source_interface_to_zone": "DNAT源接口映射到区域: {policy_name} - {original} → {zone}", "nat.dnat_source_interfaces_added": "DNAT规则添加源接口匹配: {policy_name} - {count}个接口: {interfaces}", "nat.dnat_source_network_added": "DNAT源网络添加: {policy_name} - {network}", "nat.dnat_source_network_all_skipped": "DNAT源网络跳过'all': {policy_name}", "nat.dnat_target_vip_set": "DNAT目标VIP设置: {policy_name} - {vip}", "nat.dnat_time_range_default": "DNAT时间范围使用默认: {policy_name}", "nat.dnat_time_range_set": "DNAT时间范围设置: {policy_name} - {schedule}", "nat.duplicate_element_removed": "已移除重复元素: {element}", "nat.duplicate_elements_found": "发现重复元素: {element}，数量: {count}", "nat.enabled_added": "已添加NAT启用配置", "nat.enabled_already_exists": "NAT启用配置已存在", "nat.enabled_updated": "已更新NAT启用配置", "nat.generator_description": "NAT配置XML生成器", "nat.interface_fallback_to_trust": "接口回退到trust区域: {interface}", "nat.interface_fallback_to_untrust": "接口回退到untrust区域: {interface}", "nat.interface_mapped_successfully": "接口映射成功: {original} → {mapped}", "nat.interface_mapped_to_zone": "接口映射到区域: {original} → {zone}", "nat.interface_mapping_error": "接口映射错误: {interface} - {error}", "nat.interface_mapping_failed": "接口映射失败，使用默认区域: 接口={interface}, 错误={error}", "nat.interface_mapping_file_error": "接口映射文件加载错误: {error}", "nat.interface_mapping_loaded_from_file": "接口映射从文件加载: {file}", "nat.interface_mapping_loaded_from_processor": "接口映射从处理器状态加载", "nat.interface_mapping_not_available": "接口映射不可用: {interface}", "nat.interface_mapping_success": "接口映射成功: {original} → {mapped}", "nat.interface_name_mapping_failed": "接口名称映射失败，使用原始名称: 接口={interface}, 错误={error}", "nat.ippool_missing_field": "IP池 {pool_name} 缺少字段: {field}", "nat.missing_pool_name": "NAT池缺少名称", "nat.missing_rule_name": "NAT规则缺少名称", "nat.naming_without_suffix": "NAT规则使用原始策略名称: {policy_name}", "nat.network_name_cleaned": "NAT规则网络名称已清理 [{context}]: {original} → {cleaned} (原因: {reason})", "nat.no_config_to_generate": "没有NAT配置需要生成", "nat.no_valid_ippool": "策略 {policy_name} 没有有效的IP池: {pools}", "nat.pool_creation_error": "创建NAT池 {name} 时出错: {error}", "nat.pool_element_created": "NAT池元素已创建: {name}", "nat.pools_created": "创建了 {nat_pools_created} 个NAT池", "nat.processing_complete": "NAT处理完成，规则: {nat_rules}, 池: {nat_pools}", "nat.processor_description": "nat: processor description", "nat.rule_creation_error": "创建NAT规则 {name} 时出错: {error}", "nat.rule_element_created": "NAT规则元素已创建: {name}", "nat.processing_rule_type_check": "处理NAT规则 {rule_name}, 规则类型检查: static-dnat44={static_dnat44}, static-snat44={static_snat44}, dynamic-snat44={dynamic_snat44}, twice-nat44={twice_nat44}", "nat.sip_port_check_config_added": "已添加默认SIP端口检查配置", "nat.sip_port_check_config_already_exists": "SIP端口检查配置已存在", "nat.snat_rules_created": "创建了 {snat_rules_created} 个SNAT规则", "nat.start_generation": "开始生成NAT配置，规则: {rules}, 池: {pools}", "nat.start_processing": "开始处理NAT配置，策略: {policies}, VIP: {vips}, IP池: {ippools}", "nat.statistics_summary": "NAT统计: 总规则={nat_rules}, DNAT={dnat_rules}, SNAT={snat_rules}, 池={nat_pools}", "nat.unsupported_fixedport": "策略 {policy} 使用了不支持的fixedport功能，将被忽略", "nat.unsupported_groups": "策略 {policy} 使用了不支持的组基础NAT，将被忽略", "nat.unsupported_users": "策略 {policy} 使用了不支持的用户基础NAT，将被忽略", "nat.vip_missing_field": "VIP {vip_name} 缺少字段: {field}", "nat_generator_adapter.invalid_input_data": "nat generator adapter: invalid input data", "network_object_integrator.added_address_group_member": "添加地址组成员", "network_object_integrator.added_new_address_group": "添加新地址组", "network_object_integrator.added_new_address_object": "添加新地址对象", "network_object_integrator.address_group_creation_failed": "[待翻译] network_object_integrator.address_group_creation_failed", "network_object_integrator.address_group_xml_fragment_integrated": "[待翻译] network_object_integrator.address_group_xml_fragment_integrated", "network_object_integrator.address_group_xml_fragment_integration_failed": "[待翻译] network_object_integrator.address_group_xml_fragment_integration_failed", "network_object_integrator.address_group_xml_fragment_parse_failed": "[待翻译] network_object_integrator.address_group_xml_fragment_parse_failed", "network_object_integrator.address_groups_integration_failed": "[待翻译] network_object_integrator.address_groups_integration_failed", "network_object_integrator.address_objects_integration_failed": "[待翻译] network_object_integrator.address_objects_integration_failed", "network_object_integrator.address_set_creation_failed": "[待翻译] network_object_integrator.address_set_creation_failed", "network_object_integrator.address_xml_fragment_integrated": "[待翻译] network_object_integrator.address_xml_fragment_integrated", "network_object_integrator.address_xml_fragment_integration_failed": "[待翻译] network_object_integrator.address_xml_fragment_integration_failed", "network_object_integrator.address_xml_fragment_parse_failed": "[待翻译] network_object_integrator.address_xml_fragment_parse_failed", "network_object_integrator.created_network_obj_container": "创建网络对象容器", "network_object_integrator.integrating_single_address_group": "集成单个地址组", "network_object_integrator.integrating_single_address_object": "集成单个地址对象", "network_object_integrator.merged_existing_address_group": "[待翻译] network_object_integrator.merged_existing_address_group", "network_object_integrator.merged_existing_address_object": "[待翻译] network_object_integrator.merged_existing_address_object", "network_object_integrator.network_object_integration_completed": "网络对象集成完成", "network_object_integrator.network_object_integration_exception": "[待翻译] network_object_integrator.network_object_integration_exception", "network_object_integrator.no_address_data": "[待翻译] network_object_integrator.no_address_data", "network_object_integrator.no_address_data_to_integrate": "[待翻译] network_object_integrator.no_address_data_to_integrate", "network_object_integrator.no_address_groups_to_integrate": "[待翻译] network_object_integrator.no_address_groups_to_integrate", "network_object_integrator.no_address_objects_to_integrate": "[待翻译] network_object_integrator.no_address_objects_to_integrate", "network_object_integrator.replaced_existing_address_group": "[待翻译] network_object_integrator.replaced_existing_address_group", "network_object_integrator.replaced_existing_address_object": "[待翻译] network_object_integrator.replaced_existing_address_object", "network_object_integrator.set_default_address": "[待翻译] network_object_integrator.set_default_address", "network_object_integrator.set_range_address": "[待翻译] network_object_integrator.set_range_address", "network_object_integrator.set_single_ip_address": "[待翻译] network_object_integrator.set_single_ip_address", "network_object_integrator.set_subnet_address": "设置子网地址", "network_object_integrator.single_address_group_integration_failed": "[待翻译] network_object_integrator.single_address_group_integration_failed", "network_object_integrator.single_address_object_integration_failed": "[待翻译] network_object_integrator.single_address_object_integration_failed", "notifications.com": "[待翻译] notifications.com", "ntos_extensions.added_dhcp_servers": "已添加DHCP服务器: {count} 个", "ntos_extensions.added_nat_rules": "已添加NAT规则: {count} 条", "ntos_extensions.added_vpn_tunnels": "已添加VPN隧道: {count} 个", "ntos_extensions.adding_dhcp_servers": "正在添加DHCP服务器到XML", "ntos_extensions.adding_dns_config": "正在添加DNS配置到XML", "ntos_extensions.adding_nat_rules": "正在添加NAT规则到XML", "ntos_extensions.adding_vpn_tunnels": "正在添加VPN隧道到XML", "ntos_extensions.create_dhcp_server_node": "创建DHCP服务器节点", "ntos_extensions.create_ipsec_node": "创建IPsec节点", "ntos_extensions.create_nat_node": "创建NAT节点", "ntos_extensions.create_vpn_node": "创建VPN节点", "ntos_extensions.dhcp_servers_added": "DHCP服务器添加完成", "ntos_extensions.dns_config_added": "DNS配置添加完成，服务器数量: {servers}，DNS over TLS: {dns_over_tls}", "ntos_extensions.error.dns_config_failed": "DNS配置添加失败", "ntos_extensions.error.extending_xml_failed": "扩展XML配置失败: {error}", "ntos_extensions.error.vrf_not_found": "未找到VRF节点", "ntos_extensions.init_extended_generator": "扩展NTOS生成器初始化完成，型号: {model}，版本: {version}", "ntos_extensions.nat_rules_added": "NAT规则添加完成", "ntos_extensions.using_custom_template": "使用自定义模板: {path}", "ntos_extensions.vpn_tunnels_added": "VPN隧道添加完成", "ntos_name_validator.initialized": "NTOS名称验证器已初始化", "ntos_name_validator.invalid_characters_removed": "移除无效字符: '{original}' -> '{cleaned}'", "ntos_name_validator.name_validation_stats": "名称验证统计: 处理={processed}, 规范化={sanitized}, 冲突={conflicts}", "ntos_name_validator.service_group_name_conflict_resolved": "服务组名称冲突已解决: '{original}' -> '{resolved}'", "ntos_name_validator.service_group_name_sanitized": "服务组名称已规范化: '{original}' -> '{sanitized}'", "ntos_name_validator.service_group_names_batch_processed": "批量处理服务组名称: 总数={total}, 需要规范化={sanitized_count}, 冲突解决={conflict_count}", "operation_mode_processor.device_interface_bridge_mode": "DEBUG: 设备接口 {interface} 设为桥接模式", "operation_mode_processor.device_interfaces_load_failed": "WARNING: 设备接口信息加载失败: {error}，使用默认配置", "operation_mode_processor.device_interfaces_loaded": "DEBUG: 设备接口信息加载成功，支持的设备: {devices}", "operation_mode_processor.mgmt_interface_mapping_found": "DEBUG: 找到管理接口映射: {original} -> {mapped}", "operation_mode_processor.transparent_mode_classification": "DEBUG: 透明模式接口分类 - 管理接口: {mgmt}, 桥接接口数量: {count}", "operation_mode_processor.transparent_mode_classification_complete": "DEBUG: 透明模式接口分类完成 - 管理接口: {mgmt}, 桥接接口: {bridge}", "operation_mode_stage.completed": "操作模式检测完成，模式: {mode}，主机名: {hostname}", "operation_mode_stage.description": "操作模式检测阶段", "operation_mode_stage.interface_classification": "接口分类完成，管理接口: {mgmt_count}，桥接接口: {bridge_count}，总计: {total}", "operation_mode_stage.no_config_data": "没有配置数据", "operation_mode_stage.no_system_settings": "没有系统设置配置", "operation_mode_stage.processing_failed": "操作模式处理失败: {error}", "operation_mode_stage.processing_route_mode": "处理路由模式配置", "operation_mode_stage.processing_transparent_mode": "处理透明模式配置", "operation_mode_stage.route_mode_detected": "检测到路由模式: {opmode}", "operation_mode_stage.route_mode_processed": "路由模式处理完成，主机名: {hostname}", "operation_mode_stage.starting": "开始检测操作模式", "operation_mode_stage.transparent_mode_detected": "检测到透明模式", "operation_mode_stage.transparent_mode_processed": "透明模式处理完成，主机名: {hostname}，管理接口: {mgmt}，桥接接口数: {bridge_count}", "parser_plugin.address_extraction_failed": "parser plugin: address extraction failed", "parser_plugin.initialized": "解析器插件已初始化", "parser_plugin.interface_extraction_failed": "parser plugin: interface extraction failed", "parser_plugin.policy_extraction_failed": "parser plugin: policy extraction failed", "parser_plugin.service_extraction_failed": "parser plugin: service extraction failed", "parser_registry.builtin_registration_failed": "解析器注册表: builtin registration failed", "parser_registry.cache_cleared": "解析器注册表: cache cleared", "parser_registry.initialized": "解析器注册表已初始化", "parser_registry.parser_created": "解析器注册表: parser created", "parser_registry.parser_not_found": "解析器注册表: parser not found", "parser_registry.parser_registered": "解析器注册表: parser registered", "performance_monitor.initialized": "性能监控器已初始化", "performance_monitor.operation_completed": "性能监控器：操作已完成", "performance_monitor.operation_started": "性能监控器：操作已开始", "physical_interface_handler.created_new_ipv4": "physical interface handler: created new ipv4", "physical_interface_handler.debug_call_fortinet_optimizer": "🔍 DEBUG: 调用FortinetOptimizer - 接口: {interface_name}", "physical_interface_handler.debug_intf_data_ip": "🔍 DEBUG: intf_data IP相关: ip={ip}, netmask={netmask}", "physical_interface_handler.debug_intf_data_keys": "🔍 DEBUG: intf_data keys: {keys}", "physical_interface_handler.debug_set_working_mode": "DEBUG: 设置接口 {interface_name} 工作模式: {working_mode}", "physical_interface_handler.debug_update_working_mode": "DEBUG: 更新接口 {interface_name} 工作模式: {working_mode}", "physical_interface_handler.filter_logical_interface": "过滤逻辑接口: {interface_name}", "physical_interface_handler.filter_modem_interface": "过滤modem接口: {interface_name}", "physical_interface_handler.filter_tunnel_interface": "过滤tunnel接口: {interface_name}", "physical_interface_handler.filter_unmapped_interface": "过滤没有映射关系的接口: {interface_name}", "physical_interface_handler.initialized_empty_operation_mode": "PhysicalInterfaceHandler初始化 - operation_mode_result为空", "physical_interface_handler.initialized_non_transparent": "PhysicalInterfaceHandler初始化 - 非透明模式，transparent_mode_result保持None", "physical_interface_handler.invalid_parent_element": "physical interface handler: invalid parent element", "physical_interface_handler.invalid_parent_for_ipv4": "IPv4配置的父接口无效", "physical_interface_handler.mapping_check_failed": "映射检查失败 - 接口名: {interface_name}, 映射字典: {interface_mapping}", "physical_interface_handler.skip_filtered_interface": "跳过接口: {interface_name} (已过滤)", "pipeline_manager.error_handler_set": "管道管理器：错误处理器已设置", "pipeline_manager.executing_stage": "管道管理器：执行阶段", "pipeline_manager.initialized": "管道管理器：已初始化", "pipeline_manager.no_stages": "管道管理器：无阶段", "pipeline_manager.pipeline_completed": "管道管理器：管道完成", "pipeline_manager.pipeline_disabled": "管道管理器：管道已禁用", "pipeline_manager.pipeline_enabled": "管道管理器：管道已启用", "pipeline_manager.pipeline_finished": "管道管理器：管道结束", "pipeline_manager.pipeline_starting": "管道管理器：管道开始", "pipeline_manager.pipeline_stopped_on_error": "管道管理器: pipeline stopped on error", "pipeline_manager.stage_added": "管道管理器: stage added", "pipeline_manager.stage_failed_continuing": "管道管理器: stage failed continuing", "pipeline_manager.stage_missing_name": "管道管理器: stage missing name", "pipeline_manager.stage_not_found": "管道管理器: stage not found", "pipeline_manager.stage_not_found_for_error_handler": "管道管理器: stage not found for error handler", "pipeline_manager.stage_removed": "管道管理器: stage removed", "pipeline_manager.stop_on_error_set": "管道管理器: stop on error set", "pipeline_stage.condition_false": "管道阶段：条件为假", "pipeline_stage.condition_false_no_action": "管道阶段：条件为假无操作", "pipeline_stage.condition_true": "管道阶段：条件为真", "pipeline_stage.error_handler_failed": "管道阶段：错误处理器失败", "pipeline_stage.input_validation_failed_new": "阶段 {stage} 输入验证失败", "pipeline_stage.parallel_completed": "管道阶段：并行完成", "pipeline_stage.parallel_fail_fast": "管道阶段：并行快速失败", "pipeline_stage.parallel_stage_error": "管道阶段：并行阶段错误", "pipeline_stage.stage_completed": "管道阶段：阶段完成", "pipeline_stage.stage_completed_new": "阶段完成: {stage}, 耗时: {duration:.2f}秒", "pipeline_stage.stage_disabled": "管道阶段：阶段已禁用", "pipeline_stage.stage_disabled_new": "阶段已禁用: {stage}", "pipeline_stage.stage_enabled": "管道阶段：阶段已启用", "pipeline_stage.stage_failed_new": "阶段失败: {stage}", "pipeline_stage.stage_starting": "管道阶段：阶段开始", "pipeline_stage.stage_starting_new": "阶段开始: {stage}", "pkg_resources": "pkg resources", "pkg_resources.extern.packaging.markers": "pkg resources: extern: packaging: markers", "pkg_resources.extern.packaging.requirements": "pkg resources: extern: packaging: requirements", "pkg_resources.extern.packaging.specifiers": "pkg resources: extern: packaging: specifiers", "pkg_resources.extern.packaging.version": "包资源外部打包版本", "policy.av_profile_mapped": "AV配置文件 {profile} 已映射到 {mapped}", "policy.both_fields_added": "策略 {policy} 同时添加了接口和区域字段（兼容模式）", "policy.conversion_mode_applied": "策略 {policy} 应用转换模式: {mode}", "policy.conversion_mode_applied_with_mapping": "策略 {policy} 应用转换模式（带映射）: {mode}", "policy.dest_interface_mapped": "策略 {policy} 目标接口映射: {original} -> {mapped}", "policy.dest_interface_mapped_with_mapping": "策略 {policy} 目标接口映射（带映射）: {original} -> {mapped}", "policy.dest_zone_mapped": "策略 {policy} 目标区域映射: 接口 {interface} -> 区域 {zone}", "policy.dest_zone_mapped_with_mapping": "策略 {policy} 目标区域映射（带映射）: 接口 {interface} -> 区域 {zone}", "policy.detailed_statistics": "详细统计 - 按类型: {by_type}, 质量指标: {quality_metrics}", "policy.diagnostic_suggestions": "策略 {policy_id} 诊断建议: {suggestions}", "policy.incomplete_ippool": "IP池不完整: {pool_name}", "policy.interface_mapping_file_not_found": "策略 {policy_id}: 接口映射文件未找到: {file}", "policy.interface_mapping_not_found": "策略 {policy_id}: {type} '{interface}' 未找到映射关系", "policy.interface_mapping_validation_error": "策略 {policy_id}: 接口映射验证异常: {error}", "policy.invalid_vip_port": "VIP端口无效: {vip_name}，端口: {port}", "policy.ippool_not_found": "IP池未找到: {pool_name}", "policy.ips_sensor_mapped": "IPS传感器 {sensor} 已映射到 {mapped}", "policy.missing_ippool": "策略缺少IP池: {policy_name}", "policy.nat_rules_generation_failed": "策略 {policy_id} NAT规则生成失败: {error}", "policy.no_interface_mapping_file": "策略 {policy_id}: 无有效的接口映射配置", "policy.no_policies_to_process": "没有策略需要处理", "policy.partial_validation": "策略 {policy_id} 部分验证通过，级别: {level}", "policy.policy_creation_error": "创建策略 {name} 时出错: {error}", "policy.policy_element_created": "策略元素已创建: {name}", "policy.processing_complete": "策略处理完成: 总数{total}, 成功{successful}, 成功率{success_rate:.1f}%, 安全策略{security_policies}个, NAT规则{nat_rules}个", "policy.processing_error": "处理策略 {policy_id} 时出错: {error}", "policy.processing_failed": "策略 {policy_id} 处理失败: {error}", "policy.processor_description": "Fortigate策略转换处理器", "policy.security_policy_generation_failed": "策略 {policy_id} 安全策略生成失败: {error}", "policy.service_mapping_error": "服务映射错误: {service}，错误: {error}", "policy.source_interface_mapped": "策略 {policy} 源接口映射: {original} -> {mapped}", "policy.source_interface_mapped_with_mapping": "策略 {policy} 源接口映射（带映射）: {original} -> {mapped}", "policy.source_zone_mapped": "策略 {policy} 源区域映射: 接口 {interface} -> 区域 {zone}", "policy.source_zone_mapped_with_mapping": "策略 {policy} 源区域映射（带映射）: 接口 {interface} -> 区域 {zone}", "policy.special_interface_used": "策略 {policy_id}: 使用特殊接口 '{interface}' 作为{type}", "policy.start_processing": "开始策略处理，共 {count} 个策略", "policy.unknown_conversion_mode": "策略 {policy} 使用了未知的转换模式 {mode}，使用默认的纯接口模式", "policy.unknown_conversion_mode_with_mapping": "策略 {policy} 使用了未知的转换模式（带映射）{mode}，使用默认的纯接口模式", "policy.unsupported_fixedport": "策略 {policy_id} 使用了不支持的fixedport enable特性", "policy.unsupported_groups_auth": "策略 {policy_id} 使用了不支持的用户组认证特性，用户组: {groups}", "policy.unsupported_users_auth": "策略 {policy_id} 使用了不支持的用户认证特性，用户: {users}", "policy.validation.invalid_action": "无效的策略动作: {action}", "policy.validation.missing_policy_id": "缺少策略ID", "policy.validation.missing_policy_name": "缺少策略名称", "policy.validation.missing_required_fields": "缺少必需字段: {fields}", "policy.validation.missing_services": "缺少服务配置", "policy.validation_failed": "策略 {policy_id} 验证失败: {issues}", "policy.vip_incomplete": "VIP不完整: {vip_name}", "policy_processor.add_nat_rule": "添加NAT规则: {name} ({rule_type})", "policy_processor.add_security_policy": "添加安全策略: {name}", "policy_processor.address_name_cleaned": "🧹 地址名称已清理: {original} -> {cleaned}", "policy_processor.available_interface_mapping": "📋可用的接口映射: {interface_mapping}", "policy_processor.available_interface_mapping_keys": "📋 可用的接口映射键: {keys}", "policy_processor.cannot_get_data_context_for_zone": "无法获取区域 '{zone}' 的数据上下文", "policy_processor.cannot_get_zone_config": "无法获取区域 '{zone}' 的配置", "policy_processor.check_destination_interfaces": "🔍检查目标接口: {dstintf_list}", "policy_processor.check_source_interfaces": "🔍检查源接口: {srcintf_list}", "policy_processor.comment_mapped_to_description": "策略注释已映射到描述字段: {policy_name} - {comment}", "policy_processor.comment_truncated": "策略注释过长已截断: {policy_name} - 原长度: {original_length}, 截断后: {truncated_length}", "policy_processor.create_dnat_rule": "创建DNAT规则: {name}", "policy_processor.create_snat_rule": "创建SNAT规则: {name}", "policy_processor.critical_new_architecture_policy_processing": "🚨 CRITICAL: 新架构主要路径策略处理 - 策略ID: {policy_id}", "policy_processor.destination_fortigate_logical_zone_accepted": "目标区域 '{clean_intf}' 被识别为FortiGate逻辑区域", "policy_processor.destination_interface_found_in_mapping": "✅ 目标接口 '{clean_intf}' 在接口映射中找到", "policy_processor.destination_interface_mapping_found": "✅ 目标接口映射找到: '{clean_intf}' -> '{mapped_intf}'", "policy_processor.destination_interface_not_found_and_not_zone": "❌ 策略 {policy_id}: 目标接口/区域 '{clean_intf}' 在映射表中未找到且无法解析为区域", "policy_processor.destination_interface_not_in_mapping": "❌ 目标接口 '{clean_intf}' 不在接口映射表中", "policy_processor.destination_zone_identified": "目标区域识别: {clean_intf}", "policy_processor.destination_zone_resolved_to_interfaces": "目标区域 '{clean_intf}' 解析为接口: {zone_interfaces}", "policy_processor.detected_target_version": "检测到目标版本: {target_version}", "policy_processor.fortigate_logical_zone_mapped": "FortiGate逻辑区域 '{fortigate_zone}' 映射为NTOS区域 '{ntos_zone}'", "policy_processor.found_policies_count": "找到 {count} 个策略", "policy_processor.get_zone_names_exception": "获取区域名称列表时发生异常: {error}", "policy_processor.interface_format_invalid": "接口名称 '{interface}' 格式不符合NTOS标准", "policy_processor.interface_identified": "识别为接口名称: {name}", "policy_processor.interface_identified_as_dest": "策略 {policy_id} 识别目标接口: {clean_intf} -> {mapped_intf}", "policy_processor.interface_identified_as_source": "策略 {policy_id} 识别源接口: {clean_intf} -> {mapped_intf}", "policy_processor.interface_validation_exception": "验证接口名称 '{interface}' 时发生异常: {error}", "policy_processor.interface_yang_validation_failed": "接口名称 '{interface}' 不符合YANG模型规范", "policy_processor.missing_config_data": "缺少配置数据", "policy_processor.nat_rule_conversion_complete": "NAT规则转换完成: {policy_name}, 生成 {count} 个规则", "policy_processor.nat_xml_failed": "生成NAT XML片段失败: {error}", "policy_processor.nat_xml_generated": "生成NAT XML片段，包含 {count} 个规则", "policy_processor.nat_xml_generation_failed": "生成NAT XML片段失败: {error}", "policy_processor.no_policies_to_process": "没有策略需要处理", "policy_processor.policy_all_interface_zone_mapping_passed": "✅ 策略 {policy_id} 所有接口/区域映射验证通过", "policy_processor.policy_classification_complete": "策略分类完成: {policy_id}, DNAT: {needs_dnat}, SNAT: {needs_snat}", "policy_processor.policy_contains_nat_but_version_not_support": "策略 {policy_name} 包含NAT配置，但目标版本 {target_version} 不支持NAT功能，已跳过", "policy_processor.policy_contains_nat_version_warning": "警告：策略 '{policy_name}' 包含NAT/SNAT配置，但目标版本 '{target_version}' 不支持。NAT/SNAT仅支持R11及以后的版本", "policy_processor.policy_conversion_failed": "策略转换失败: {policy_id}, 错误: {error}", "policy_processor.policy_conversion_success": "策略转换成功: {policy_id}", "policy_processor.policy_destination_interface_mapping": "策略 {policy_id}: 目标接口映射 {clean_intf} -> {mapped_intf}", "policy_processor.policy_destination_interface_mapping_missing_after_validation": "策略 {policy_id}: 警告 - 目标接口 {clean_intf} 映射缺失（验证后）", "policy_processor.policy_interface_mapping_empty": "❌ 策略 {policy_id}: 接口映射表为空", "policy_processor.policy_interface_mapping_validation_exception": "💥 策略 {policy_id} ({policy_name}): 接口映射验证异常: {error}", "policy_processor.policy_interface_mapping_validation_failed_skip": "❌ 策略 {policy_id} ({policy_name}): 接口映射验证失败，跳过处理", "policy_processor.policy_interface_mapping_validation_passed_alt": "✅ 策略 {policy_id} ({policy_name}): 接口映射验证通过", "policy_processor.policy_interface_validation_failed": "❌ 策略 {policy_id}: 接口映射验证失败，跳过此策略", "policy_processor.policy_interface_validation_passed": "✅ 策略 {policy_id}: 接口映射验证通过", "policy_processor.policy_processing_complete": "策略处理完成: 成功 {success}/{total}", "policy_processor.policy_processing_complete_detailed": "策略处理完成: 转换成功 {converted_count} 个，跳过 {skipped_count} 个，失败 {failed_count} 个", "policy_processor.policy_processing_complete_with_nat_version": "策略处理完成: 转换成功 {converted_count}/{total}, 安全策略 {security_policies} 个, NAT规则 {nat_rules} 个, NAT跳过 {nat_skipped} 个 (版本: {target_version})", "policy_processor.policy_processing_complete_without_nat_version": "策略处理完成: 转换成功 {converted_count}/{total}, 安全策略 {security_policies} 个, NAT规则 {nat_rules} 个", "policy_processor.policy_processing_failed": "策略 {policy_id} 处理失败: {error}", "policy_processor.policy_skipped_conversion_error": "策略 {policy_id} 跳过: 转换错误", "policy_processor.policy_skipped_interface_mapping_validation_failed": "策略 {policy_name} 因接口映射验证失败被跳过", "policy_processor.policy_skipped_missing_interface": "策略 {policy_id} 跳过: 缺少必需的接口映射", "policy_processor.policy_skipped_validation_failed": "策略 {policy_id} 跳过: 验证失败", "policy_processor.policy_source_interface_mapping": "策略 {policy_id}: 源接口映射 {clean_intf} -> {mapped_intf}", "policy_processor.policy_source_interface_mapping_missing_after_validation": "策略 {policy_id}: 警告 - 源接口 {clean_intf} 映射缺失（验证后）", "policy_processor.processing_failed": "策略处理失败: {error}", "policy_processor.security_policy_conversion_success": "安全策略转换成功: {policy_name}", "policy_processor.security_policy_xml_failed": "生成安全策略XML片段失败: {error}", "policy_processor.security_policy_xml_generated": "生成安全策略XML片段，包含 {count} 个策略", "policy_processor.source_fortigate_logical_zone_accepted": "源区域 '{clean_intf}' 被识别为FortiGate逻辑区域", "policy_processor.source_interface_found_in_mapping": "✅ 源接口 '{clean_intf}' 在接口映射中找到", "policy_processor.source_interface_mapping_found": "✅ 源接口映射找到: '{clean_intf}' -> '{mapped_intf}'", "policy_processor.source_interface_not_found_and_not_zone": "❌ 策略 {policy_id}: 源接口/区域 '{clean_intf}' 在映射表中未找到且无法解析为区域", "policy_processor.source_interface_not_in_mapping": "❌ 源接口 '{clean_intf}' 不在接口映射表中", "policy_processor.source_zone_identified": "源区域识别: {clean_intf}", "policy_processor.source_zone_resolved_to_interfaces": "✅ 源区域 '{clean_intf}' 解析为接口: {zone_interfaces}", "policy_processor.start_processing": "开始策略处理", "policy_processor.strict_validation_policy_interface_mapping": "🔍新架构主要路径严格验证策略 {policy_id}的接口映射", "policy_processor.target_version_not_specified": "未指定目标版本，默认支持所有功能", "policy_processor.target_version_not_support_nat_config_skipped": "注意：目标版本 {target_version} 不支持NAT/SNAT功能，NAT相关配置将被跳过", "policy_processor.target_version_not_support_nat_skip_generation": "目标版本 {target_version} 不支持NAT功能，将跳过NAT规则生成", "policy_processor.target_version_not_support_nat_skip_xml": "目标版本 {target_version} 不支持NAT功能，跳过NAT XML生成", "policy_processor.verify_destination_interface_zone": "🔍验证目标接口区域: '{clean_intf}'", "policy_processor.verify_destination_interface_zone_alt": "🔍 验证目标接口/区域: '{clean_intf}'", "policy_processor.verify_source_interface_zone": "🔍验证源接口区域: '{clean_intf}'", "policy_processor.verify_source_interface_zone_alt": "🔍 验证源接口/区域: '{clean_intf}'", "policy_processor.zone_check_exception": "检查区域名称 {name} 时发生异常: {error}", "policy_processor.zone_config_not_found": "未找到区域 '{zone}' 的配置", "policy_processor.zone_identified": "识别为区域名称: {name}", "policy_processor.zone_identified_as_dest": "策略 {policy_id} 识别目标区域: {zone_name}", "policy_processor.zone_identified_as_source": "策略 {policy_id} 识别源区域: {zone_name}", "policy_processor.zone_interface_mapping": "区域接口映射: {original} -> {mapped} (YANG验证通过)", "policy_processor.zone_interface_mapping_not_found": "区域 '{zone}' 中的接口 '{interface}' 未找到映射", "policy_processor.zone_is_standard_name": "区域 '{zone}' 是标准区域名称", "policy_processor.zone_name_invalid_chars": "区域名称 '{zone}' 包含非法字符", "policy_processor.zone_name_too_long": "区域名称 '{zone}' 过长（超过32字符）", "policy_processor.zone_no_interfaces": "区域 '{zone}' 没有配置接口", "policy_processor.zone_parsing_exception": "解析区域 '{zone}' 时发生异常: {error}", "policy_processor.zone_resolved_to_interfaces": "区域 '{zone}' 解析为接口: {interfaces}", "policy_processor.zone_validation_exception": "验证区域 '{zone}' 时发生异常: {error}", "policy_processor.zone_yang_validation_failed": "区域 '{zone}' 不符合YANG模型规范", "policy_processor.zone_yang_validation_passed": "区域 '{zone}' YANG验证通过", "protocol.unknown": "未知协议", "pydoc": "pydoc", "reason.address_group_empty_members": "原因: address group empty members", "reason.address_group_missing_name": "原因: address group missing name", "reason.address_missing_name": "地址对象缺少名称", "reason.address_missing_type": "地址对象缺少类型", "reason.address_type_not_explicitly_supported": "原因: address type not explicitly supported", "reason.address_type_not_supported": "原因: address type not supported", "reason.device_model_not_found": "在接口定义中未找到设备型号", "reason.enhanced_policy_processor": "增强策略处理器", "reason.fqdn_missing": "缺少FQDN信息", "reason.interface_associated_address_not_supported": "原因: interface associated address not supported", "reason.interface_not_found_in_mapping": "接口未在映射表中找到", "reason.interface_not_mapped": "接口 '{interface}' 未在映射表中找到，可能导致配置不正确", "reason.invalid_subnet_format": "子网格式不正确: {subnet}", "reason.invalid_vlan_id_or_compatibility_issue": "VLAN ID无效或其他兼容性问题", "reason.ip_range_missing_endpoints": "缺少起始IP或结束IP", "reason.missing_required_fields": "缺少必要字段: {fields}", "reason.route_missing_destination": "静态路由 {id} 缺少目的网络信息，无法处理", "reason.service_group_missing_name": "原因: service group missing name", "reason.service_group_no_members": "原因: service group no members", "reason.service_group_no_valid_members": "原因: service group no valid members", "reason.service_missing_name": "服务对象缺少名称", "reason.service_missing_port_range": "服务对象缺少端口范围信息", "reason.service_missing_protocol": "服务对象缺少协议类型", "reason.service_missing_protocol_and_ports": "reason: service missing protocol and ports", "reason.service_not_mapped": "服务对象未找到映射", "reason.source_interface_not_found": "源接口在配置中不存在", "reason.source_interface_not_physical": "源接口不是物理接口", "reason.subnet_info_missing": "缺少子网信息", "reason.target_interface_not_supported": "目标接口不受指定设备型号支持", "reason.time_range_empty": "时间对象没有有效的时间或星期几设置", "reason.time_range_invalid_datetime_format": "时间对象 {name} 的日期时间格式无效: {error}", "reason.time_range_missing_datetime": "时间对象 {name} 缺少必要的日期时间", "reason.time_range_missing_name": "时间对象缺少名称", "reason.unmapped_interfaces": "接口未映射: {interfaces}", "reason.unsupported_address_type": "不支持的地址对象类型: {type}", "reason.unsupported_protocol": "不支持的协议类型: {protocol}", "reason.unsupported_protocol_ntos": "NTOS不支持的协议", "reason.unsupported_route_type": "包含不支持的路由类型", "reason.unsupported_rule_type": "包含不支持的规则类型", "report.affected_policies": "受影响策略", "report.affected_policies_count": "{count}个策略", "report.category_issues_count": "{count}个问题", "report.compatibility_issues": "兼容性问题", "report.config_file": "配置文件", "report.conversion_stats": "转换统计", "report.detail": "详情", "report.details": "详细信息", "report.error": "错误", "report.error_message": "错误消息", "report.failed": "失败", "report.generated_time": "生成时间", "report.ignored": "忽略", "report.interface": "接口", "report.interfaces.failed": "转换失败接口数", "report.interfaces.ignored": "忽略接口数", "report.interfaces.success": "转换成功接口数", "report.interfaces.total": "接口总数", "report.issue_description": "问题描述", "report.manual_config_items": "需要人工配置项目", "report.missing_objects": "缺失对象", "report.name": "名称", "report.needs_immediate_attention": "需要立即处理", "report.no_mapping": "未找到接口映射", "report.optional_optimization": "可选优化", "report.policies.failed": "转换失败策略数", "report.policies.ignored": "忽略策略数", "report.policies.success": "转换成功策略数", "report.policies.total": "策略总数", "report.policy_ids": "策略ID", "report.policy_list": "策略列表", "report.priority_1": "优先级1", "report.priority_2": "优先级2", "report.priority_3": "优先级3", "report.problem_categories": "问题分类", "report.problem_categories_count": "{count}大类", "report.reason": "原因", "report.recommendation_1": "更新接口映射文件，添加缺失接口的映射关系", "report.recommendation_2": "检查FortiGate配置，确认缺失的地址/服务对象是否需要创建", "report.recommendation_3": "考虑为重要策略添加日志设置和优化DNS配置", "report.recommendations": "处理建议", "report.recommended_to_fix": "建议处理", "report.source_file": "源文件", "report.statistics": "问题统计", "report.status": "状态", "report.success": "成功", "report.suggestion": "建议", "report.target_device": "目标设备", "report.timestamp": "时间", "report.title": "FortiGate配置语义验证报告", "report.total": "总数", "report.total_warnings": "总警告数", "report.unknown": "未知", "report.unknown_reason": "未知原因", "report.validation_failed": "验证失败", "report.vendor": "厂商", "report.zone": "区域", "request_error": "请求错误: {error}", "reset_button": "重置", "route.unknown_destination": "未知目的网络", "route.unknown_gateway": "未知网关", "routing_handler.create_static_node": "routing handler: create static node", "routing_handler.routes_added_to_xml": "routing handler: routes added to xml", "rule_registry.initialized": "规则注册表已初始化", "rule_registry.rule_registered": "rule registry: rule registered", "running.xml": "[待翻译] running.xml", "security_policy.container_created": "安全策略容器已创建", "security_policy.container_found": "找到现有安全策略容器", "security_policy.generator_description": "安全策略XML生成器", "security_policy.integration_complete": "安全策略集成完成，数量: {count}", "security_policy.missing_policy_name": "安全策略缺少名称", "security_policy.no_policies_to_generate": "没有安全策略需要生成", "security_policy.policy_converted_to_dict": "安全策略已转换为字典格式", "security_policy.policy_creation_error": "创建安全策略 {name} 时出错: {error}", "security_policy.policy_element_created": "安全策略元素已创建: {name}", "security_policy.standalone_creation_complete": "独立安全策略创建完成，数量: {count}", "security_policy.start_generation": "开始生成安全策略，数量: {count}", "security_policy.validation.exception": "验证过程中发生异常: {error}", "security_policy.validation.invalid_action": "无效的动作值: {action}", "security_policy.validation.missing_namespace": "缺少安全策略命名空间", "security_policy.validation.missing_required_field": "缺少必要字段: {field}", "security_policy.validation.no_policies": "没有找到策略", "security_policy_generator_adapter.invalid_action": "security policy generator adapter: invalid action", "security_policy_generator_adapter.invalid_data_format": "security policy generator adapter: invalid data format", "security_policy_generator_adapter.invalid_input_data": "security policy generator adapter: invalid input data", "security_policy_generator_adapter.legacy_generator_loaded": "security policy generator adapter: legacy generator loaded", "security_policy_generator_adapter.missing_required_field": "security policy generator adapter: missing required field", "security_policy_generator_adapter.no_legacy_generator": "security policy generator adapter: no legacy generator", "security_policy_integrator.added_nat_rule_from_fragment": "[待翻译] security_policy_integrator.added_nat_rule_from_fragment", "security_policy_integrator.added_new_nat_rule": "[待翻译] security_policy_integrator.added_new_nat_rule", "security_policy_integrator.added_new_security_policy": "[待翻译] security_policy_integrator.added_new_security_policy", "security_policy_integrator.added_security_policy_from_fragment": "[待翻译] security_policy_integrator.added_security_policy_from_fragment", "security_policy_integrator.created_nat_container": "创建NAT容器", "security_policy_integrator.created_security_policy_container": "创建安全策略容器", "security_policy_integrator.integrating_single_nat_rule": "集成单个NAT规则", "security_policy_integrator.integrating_single_security_policy": "集成单个安全策略", "security_policy_integrator.nat_rule_creation_failed": "[待翻译] security_policy_integrator.nat_rule_creation_failed", "security_policy_integrator.nat_rules_integration_failed": "[待翻译] security_policy_integrator.nat_rules_integration_failed", "security_policy_integrator.nat_xml_fragment_integrated": "[待翻译] security_policy_integrator.nat_xml_fragment_integrated", "security_policy_integrator.nat_xml_fragment_integration_failed": "[待翻译] security_policy_integrator.nat_xml_fragment_integration_failed", "security_policy_integrator.nat_xml_fragment_parse_failed": "[待翻译] security_policy_integrator.nat_xml_fragment_parse_failed", "security_policy_integrator.no_nat_rules_to_integrate": "[待翻译] security_policy_integrator.no_nat_rules_to_integrate", "security_policy_integrator.no_policy_data": "[待翻译] security_policy_integrator.no_policy_data", "security_policy_integrator.no_policy_data_to_integrate": "[待翻译] security_policy_integrator.no_policy_data_to_integrate", "security_policy_integrator.no_security_policies_to_integrate": "[待翻译] security_policy_integrator.no_security_policies_to_integrate", "security_policy_integrator.policy_reference_validation_failed": "策略引用验证失败", "security_policy_integrator.policy_references_validated": "策略引用验证通过", "security_policy_integrator.reference_cache_build_failed": "引用缓存构建失败", "security_policy_integrator.reference_cache_built": "引用缓存已构建", "security_policy_integrator.replaced_existing_nat_rule": "[待翻译] security_policy_integrator.replaced_existing_nat_rule", "security_policy_integrator.replaced_existing_security_policy": "[待翻译] security_policy_integrator.replaced_existing_security_policy", "security_policy_integrator.security_policies_integration_failed": "[待翻译] security_policy_integrator.security_policies_integration_failed", "security_policy_integrator.security_policy_creation_failed": "[待翻译] security_policy_integrator.security_policy_creation_failed", "security_policy_integrator.security_policy_integration_completed": "安全策略集成完成", "security_policy_integrator.security_policy_integration_exception": "[待翻译] security_policy_integrator.security_policy_integration_exception", "security_policy_integrator.security_policy_xml_fragment_integrated": "[待翻译] security_policy_integrator.security_policy_xml_fragment_integrated", "security_policy_integrator.security_policy_xml_fragment_integration_failed": "[待翻译] security_policy_integrator.security_policy_xml_fragment_integration_failed", "security_policy_integrator.security_policy_xml_fragment_parse_failed": "[待翻译] security_policy_integrator.security_policy_xml_fragment_parse_failed", "security_policy_integrator.single_nat_rule_integration_failed": "[待翻译] security_policy_integrator.single_nat_rule_integration_failed", "security_policy_integrator.single_security_policy_integration_failed": "[待翻译] security_policy_integrator.single_security_policy_integration_failed", "security_policy_integrator.unsupported_nat_type": "[待翻译] security_policy_integrator.unsupported_nat_type", "security_policy_integrator.validating_policy_references": "验证策略引用", "security_zone_handler.adding_zones_to_xml": "正在添加区域到XML，数量: {count}", "security_zone_handler.create_new_zone": "创建新区域: {name}", "security_zone_handler.found_existing_zone": "找到现有区域: {name}", "security_zone_handler.skip_zone_without_name": "security zone handler: skip zone without name", "security_zone_handler.update_existing_zone": "更新现有区域: {name}", "security_zone_handler.updating_zone": "正在更新区域: {name}", "security_zone_handler.zone_already_exists": "区域已存在: {name}", "security_zone_handler.zone_creation_success": "区域创建成功: {name}", "security_zone_handler.zone_update_complete": "区域更新完成: {name}", "security_zone_handler.zones_added_to_xml": "security zone handler: zones added to xml", "semantic.report_generated": "详细语义验证报告已生成: {path}", "semantic.summary.critical_issues_with_policies": "({count}个): {categories} - {affected_policies}个策略受影响", "semantic.summary.info_issues_count": "({count}个): {categories}", "semantic.summary.warning_issues_count": "({count}个): {categories}", "semantic.validation_completed_with_issues": "语义验证完成 - 发现问题需要关注", "semantic.validation_passed": "语义验证通过，未发现问题", "service.alias_match": "通过别名匹配到预定义服务: {service} -> {target}", "service.builtin.ah.description": "认证头协议", "service.builtin.bgp.description": "边界网关协议", "service.builtin.cluster.description": "集群服务", "service.builtin.dhcp.description": "动态主机配置协议", "service.builtin.dhcp6.description": "IPv6动态主机配置协议", "service.builtin.dns_t.description": "域名系统(TCP)", "service.builtin.dns_u.description": "域名系统(UDP)", "service.builtin.esp.description": "封装安全载荷", "service.builtin.ftp.description": "文件传输协议", "service.builtin.gre.description": "通用路由封装", "service.builtin.h225.description": "H.225协议", "service.builtin.h323.description": "H.323协议", "service.builtin.ha.description": "高可用性(TCP)", "service.builtin.ha_udp.description": "高可用性(UDP)", "service.builtin.http.description": "超文本传输协议", "service.builtin.https.description": "安全超文本传输协议", "service.builtin.icmp.description": "ICMP协议", "service.builtin.icmpv6.description": "ICMPv6协议", "service.builtin.ike.description": "Internet密钥交换", "service.builtin.imap.description": "互联网邮件访问协议", "service.builtin.imaps.description": "安全互联网邮件访问协议", "service.builtin.irc.description": "互联网中继聊天", "service.builtin.kerberos.description": "Kerberos认证(TCP)", "service.builtin.kerberos_udp.description": "Kerberos认证(UDP)", "service.builtin.l2tp.description": "第二层隧道协议(TCP)", "service.builtin.l2tp_udp.description": "第二层隧道协议(UDP)", "service.builtin.ldap.description": "轻量级目录访问协议", "service.builtin.local_https.description": "本地HTTPS服务", "service.builtin.local_service.description": "本地服务(TCP)", "service.builtin.local_service_udp.description": "本地服务(UDP)", "service.builtin.local_snmp.description": "本地SNMP服务", "service.builtin.local_ssh.description": "本地SSH服务", "service.builtin.mms.description": "微软媒体服务", "service.builtin.ms_sql_m.description": "Microsoft SQL Server监控", "service.builtin.ms_sql_r.description": "Microsoft SQL Server解析", "service.builtin.ms_sql_s.description": "Microsoft SQL Server", "service.builtin.mysql.description": "MySQL数据库", "service.builtin.netbios_ns.description": "NetBIOS名称服务", "service.builtin.netmeeting.description": "网络会议", "service.builtin.nfs.description": "网络文件系统(TCP)", "service.builtin.nfs_udp.description": "网络文件系统(UDP)", "service.builtin.nntp.description": "网络新闻传输协议", "service.builtin.ntp_t.description": "网络时间协议(TCP)", "service.builtin.ntp_u.description": "网络时间协议(UDP)", "service.builtin.ospf.description": "开放最短路径优先", "service.builtin.other.description": "其他IP协议", "service.builtin.ping.description": "PING服务", "service.builtin.ping6.description": "IPv6 PING", "service.builtin.pop3.description": "POP3邮件协议", "service.builtin.pop3s.description": "安全POP3邮件协议", "service.builtin.pptp.description": "点对点隧道协议", "service.builtin.radius.description": "远程认证拨入用户服务", "service.builtin.radius_coa.description": "RADIUS变更授权", "service.builtin.radius_old.description": "旧版远程认证拨入用户服务", "service.builtin.rdp.description": "远程桌面协议", "service.builtin.rip.description": "路由信息协议", "service.builtin.rlogin.description": "远程登录", "service.builtin.rtsp.description": "实时流协议(TCP)", "service.builtin.rtsp_udp.description": "实时流协议(UDP)", "service.builtin.samba.description": "Samba服务", "service.builtin.sip_msnmessenger.description": "MSN Messenger", "service.builtin.sip_t.description": "会话初始协议(TCP)", "service.builtin.sip_u.description": "会话初始协议(UDP)", "service.builtin.smtp.description": "简单邮件传输协议", "service.builtin.smtps.description": "安全SMTP", "service.builtin.snmp.description": "简单网络管理协议", "service.builtin.snmp_trap.description": "SNMP陷阱", "service.builtin.socks.description": "SOCKS代理(TCP)", "service.builtin.socks_udp.description": "SOCKS代理(UDP)", "service.builtin.sql_net.description": "Oracle SQL*NET", "service.builtin.squid.description": "Squid代理", "service.builtin.ssh.description": "安全Shell协议", "service.builtin.sslvpn.description": "SSL VPN(TCP)", "service.builtin.sslvpn_udp.description": "SSL VPN(UDP)", "service.builtin.syslog.description": "系统日志", "service.builtin.tcp.description": "所有TCP端口", "service.builtin.telnet.description": "Telnet远程登录服务", "service.builtin.tftp.description": "简单文件传输协议", "service.builtin.udp.description": "所有UDP端口", "service.builtin.vnc.description": "虚拟网络计算", "service.builtin.wins.description": "Windows Internet名称服务(TCP)", "service.builtin.wins_udp.description": "Windows Internet名称服务(UDP)", "service.fuzzy_match": "通过模糊匹配到预定义服务: {service} -> {target}", "service.merged": "服务 {source} 已合并到预定义服务 {target}", "service.port_range_normalized": "端口范围已标准化: {original} -> {normalized}", "service.processor_description": "服务对象处理器", "service.unnamed": "未命名服务对象", "service_group.processor_description": "service group: processor description", "service_group_processing_stage.completed": "服务对象组处理完成，总计: {total}，转换: {converted}，跳过: {skipped}", "service_group_processing_stage.description": "服务对象组处理阶段", "service_group_processing_stage.empty_group": "服务对象组为空", "service_group_processing_stage.group_member_not_found": "服务对象组 {group} 的成员 {member} 未找到", "service_group_processing_stage.group_processing_error": "服务对象组处理错误: {error}", "service_group_processing_stage.group_skipped": "服务对象组 {group} 已跳过: {reason}", "service_group_processing_stage.no_service_groups": "没有服务对象组需要处理", "service_group_processing_stage.no_service_result": "缺少服务对象处理结果", "service_group_processing_stage.no_valid_members": "服务对象组没有有效成员", "service_group_processing_stage.processing_failed": "服务对象组处理失败", "service_group_processing_stage.starting": "开始处理服务对象组", "service_group_processor.alias_case_insensitive_match_predefined": "通过别名忽略大小写匹配到预定义服务: {service} -> {predefined}", "service_group_processor.alias_exact_match_predefined": "通过别名精确匹配到预定义服务: {service} -> {predefined}", "service_group_processor.case_insensitive_match_predefined": "忽略大小写匹配到预定义服务: {service} -> {predefined}", "service_group_processor.conversion_success": "服务对象组转换成功: {group} ({valid}/{total} 成员有效)", "service_group_processor.data_conversion_complete": "服务对象组数据转换完成，原始类型: {original_type}, 转换后: {converted_type}, 组数量: {count}", "service_group_processor.detailed_error_info": "详细错误信息: {error}", "service_group_processor.exact_match_predefined": "精确匹配到预定义服务: {service}", "service_group_processor.found_groups": "找到 {count} 个服务对象组", "service_group_processor.group_no_members_warning": "警告：服务组 {group} 没有成员", "service_group_processor.group_xml_generated": "生成服务对象组XML: {group} (包含 {count} 个成员)", "service_group_processor.member_not_found": "服务组成员不存在于已转换服务对象中: {member}", "service_group_processor.member_not_found_in_converted_or_predefined": "服务组成员不存在于已转换服务对象或预定义服务中: {member}", "service_group_processor.member_validation_success": "服务组成员验证成功: {member}", "service_group_processor.member_validation_success_converted": "服务组成员验证成功（转换）: {member}", "service_group_processor.member_validation_success_mapped": "服务组成员验证成功（映射）: {member}", "service_group_processor.member_validation_success_predefined": "服务组成员验证成功（预定义）: {member} -> {ntos_service}", "service_group_processor.member_validation_success_predefined_no_mapping": "服务组成员验证成功（预定义，无映射）: {member}", "service_group_processor.no_converted_groups": "没有转换成功的服务对象组，返回空XML片段", "service_group_processor.no_converted_services": "没有已转换的服务对象，跳过服务对象组处理", "service_group_processor.predefined_mapping_file_not_exist": "预定义服务映射文件不存在: {file}", "service_group_processor.predefined_mapping_load_failed": "加载预定义服务映射失败: {error}", "service_group_processor.predefined_mapping_loaded": "加载预定义服务映射成功，共 {count} 个服务", "service_group_processor.processing_complete": "服务对象组处理完成: 转换成功 {success}/{total}", "service_group_processor.skip_empty_group": "跳过空服务组: {group}", "service_group_processor.skip_no_valid_members": "跳过无有效成员的服务组: {group}", "service_group_processor.xml_fragment_failed": "生成服务对象组XML片段失败: {error}", "service_group_processor.xml_fragment_success": "服务对象组XML片段生成成功，包含 {count} 个服务组", "service_object_integrator.added_new_service_group": "添加新服务组", "service_object_integrator.added_new_service_object": "添加新服务对象", "service_object_integrator.added_service_group_member": "添加服务组成员", "service_object_integrator.created_service_obj_container": "创建服务对象容器", "service_object_integrator.integrating_single_service_group": "集成单个服务组", "service_object_integrator.integrating_single_service_object": "集成单个服务对象", "service_object_integrator.merged_existing_service_group": "[待翻译] service_object_integrator.merged_existing_service_group", "service_object_integrator.merged_existing_service_object": "[待翻译] service_object_integrator.merged_existing_service_object", "service_object_integrator.no_service_data": "[待翻译] service_object_integrator.no_service_data", "service_object_integrator.no_service_data_to_integrate": "[待翻译] service_object_integrator.no_service_data_to_integrate", "service_object_integrator.no_service_groups_to_integrate": "[待翻译] service_object_integrator.no_service_groups_to_integrate", "service_object_integrator.no_service_objects_to_integrate": "[待翻译] service_object_integrator.no_service_objects_to_integrate", "service_object_integrator.replaced_existing_service_group": "[待翻译] service_object_integrator.replaced_existing_service_group", "service_object_integrator.replaced_existing_service_object": "[待翻译] service_object_integrator.replaced_existing_service_object", "service_object_integrator.service_group_creation_failed": "[待翻译] service_object_integrator.service_group_creation_failed", "service_object_integrator.service_group_xml_fragment_integrated": "[待翻译] service_object_integrator.service_group_xml_fragment_integrated", "service_object_integrator.service_group_xml_fragment_integration_failed": "[待翻译] service_object_integrator.service_group_xml_fragment_integration_failed", "service_object_integrator.service_group_xml_fragment_parse_failed": "[待翻译] service_object_integrator.service_group_xml_fragment_parse_failed", "service_object_integrator.service_groups_integration_failed": "[待翻译] service_object_integrator.service_groups_integration_failed", "service_object_integrator.service_object_integration_completed": "服务对象集成完成", "service_object_integrator.service_object_integration_exception": "[待翻译] service_object_integrator.service_object_integration_exception", "service_object_integrator.service_objects_integration_failed": "[待翻译] service_object_integrator.service_objects_integration_failed", "service_object_integrator.service_set_creation_failed": "[待翻译] service_object_integrator.service_set_creation_failed", "service_object_integrator.service_xml_fragment_integrated": "[待翻译] service_object_integrator.service_xml_fragment_integrated", "service_object_integrator.service_xml_fragment_integration_failed": "[待翻译] service_object_integrator.service_xml_fragment_integration_failed", "service_object_integrator.service_xml_fragment_parse_failed": "[待翻译] service_object_integrator.service_xml_fragment_parse_failed", "service_object_integrator.set_default_tcp_service": "[待翻译] service_object_integrator.set_default_tcp_service", "service_object_integrator.set_icmp_service": "[待翻译] service_object_integrator.set_icmp_service", "service_object_integrator.set_tcp_service": "设置TCP服务", "service_object_integrator.set_udp_service": "[待翻译] service_object_integrator.set_udp_service", "service_object_integrator.single_service_group_integration_failed": "[待翻译] service_object_integrator.single_service_group_integration_failed", "service_object_integrator.single_service_object_integration_failed": "[待翻译] service_object_integrator.single_service_object_integration_failed", "service_processing.custom_conversion_success": "自定义服务转换成功：{service}", "service_processing.load_mapping_failed": "加载服务映射文件失败：{error}", "service_processing.load_mapping_success": "加载服务映射文件成功：{count} 个映射", "service_processing.no_converted_services": "没有转换成功的服务对象，返回空XML片段", "service_processing.object_processing_failed": "服务对象处理失败：{name} - {reason}", "service_processing.predefined_mapping_success": "预定义服务映射成功：{service}", "service_processing.skip_duplicate_mapped_service": "跳过重复的映射服务对象：{service} -> {mapped}", "service_processing.skip_duplicate_service": "跳过重复的服务对象：{service}", "service_processing.unknown_service_type": "未知的服务类型：{service}", "service_processing.using_empty_mapping": "使用空的服务映射配置", "service_processing_stage.completed": "服务对象处理完成：转换成功 {converted}/{total}", "service_processing_stage.config_data_type": "配置数据类型：{type}", "service_processing_stage.description": "服务对象处理阶段", "service_processing_stage.detailed_error": "详细错误信息：{error}", "service_processing_stage.empty_group": "服务组为空", "service_processing_stage.group_member_not_found": "服务组 {group} 的成员 {member} 未找到", "service_processing_stage.group_processing_error": "服务组处理错误: {error}", "service_processing_stage.group_skipped": "跳过服务组 {name}: {reason}", "service_processing_stage.mapping_load_failed": "服务映射配置加载失败: {error}", "service_processing_stage.no_service_objects": "没有找到服务对象配置", "service_processing_stage.no_valid_members": "服务组没有有效成员", "service_processing_stage.processing_error": "服务对象处理错误: {error}", "service_processing_stage.processing_failed": "服务对象处理失败：{error}", "service_processing_stage.service_data_type": "服务对象数据类型：{type}", "service_processing_stage.service_failed": "服务对象 {name} 处理失败: {reason}", "service_processing_stage.starting": "开始服务对象处理", "service_processor.invalid_port_format": "无效的端口格式: {part}", "service_processor.invalid_port_range_format": "无效的端口范围格式: {part}", "service_processor.invalid_port_range_format_2": "无效的端口范围格式: {range}", "service_processor.invalid_protocol_id": "无效的协议ID: {id}，跳过protocol-id字段", "service_processor.invalid_protocol_id_format": "无效的协议ID格式: {part}", "service_processor.invalid_protocol_id_range_format": "无效的协议ID范围格式: {id}", "service_processor.invalid_tcp_port_range": "无效的TCP端口范围: {range}，跳过dest-port字段", "service_processor.invalid_udp_port_range": "无效的UDP端口范围: {range}，跳过dest-port字段", "service_processor.mapped_xml_creation_failed": "创建映射服务对象XML失败: {name} - {error}", "service_processor.mapped_xml_generated": "生成映射服务对象XML: {original} -> {mapped}", "service_processor.port_out_of_range": "端口超出范围(0-65535): {range}", "service_processor.port_range_out_of_limit": "端口范围超出限制(0-65535): {part}", "service_processor.port_range_out_of_limit_2": "端口范围超出限制(0-65535): {range}", "service_processor.protocol_id_out_of_range": "协议ID超出范围(0-255): {id}", "service_processor.protocol_id_range_out_of_limit": "协议ID范围超出限制(0-255): {id}", "service_processor.unrecognized_port_range_format": "无法识别的端口范围格式: {range}", "service_processor.unrecognized_protocol_id_format": "无法识别的协议ID格式: {id}", "service_processor.unsupported_protocol": "不支持的协议类型: {protocol}", "service_processor.xml_creation_failed": "创建服务对象XML失败: {name} - {error}", "service_processor.xml_fragment_failed": "生成服务对象XML片段失败: {error}", "service_processor.xml_fragment_success": "服务对象XML片段生成成功，包含 {count} 个服务对象", "service_processor.xml_generated": "生成服务对象XML: {name} ({protocol})", "setuptools": "setuptools", "setuptools.extern.packaging.specifiers": "setuptools: extern: packaging: specifiers", "setuptools.extern.packaging.version": "安装工具外部打包版本", "severity.critical": "关键问题", "severity.critical_issues": "关键问题", "severity.info": "优化建议", "severity.info_issues": "优化建议", "severity.warning": "警告", "severity.warning_issues": "配置问题", "source_content": "源内容", "source_format": "源格式", "ssl.root": "[待翻译] ssl.root", "startup.tar.gz": "[待翻译] startup.tar.gz", "startup.xml": "[待翻译] startup.xml", "static_route_processing_stage.completed": "静态路由处理完成，总计: {total}，转换: {converted}", "static_route_processing_stage.description": "静态路由处理阶段", "static_route_processing_stage.destination_parse_error": "目标地址解析错误 {dst}: {error}", "static_route_processing_stage.invalid_default_route": "静态路由 {id} 默认路由配置无效: {dst}", "static_route_processing_stage.invalid_destination": "静态路由 {id} 目标地址无效: {dst}", "static_route_processing_stage.missing_destination": "静态路由 {id} 缺少目标地址", "static_route_processing_stage.no_config_data": "没有配置数据", "static_route_processing_stage.no_gateway_or_interface": "静态路由 {id} 没有网关或接口", "static_route_processing_stage.no_static_routes": "没有静态路由需要处理", "static_route_processing_stage.processing_failed": "静态路由处理失败: {error}", "static_route_processing_stage.route_conversion_failed": "静态路由 {id} 转换失败: {error}", "static_route_processing_stage.starting": "开始处理静态路由", "static_route_processor.add_description_field": "Static route: 添加描述字段 '{descr}'", "static_route_processor.add_nexthop": "添加next-hop: 类型={type}, 下一跳={nexthop}", "static_route_processor.converted_routes_found": "转换后找到 {count} 个静态路由", "static_route_processor.default_description": "静态路由 {route_id}", "static_route_processor.destination_parse_error": "目标地址解析错误: {dst}, 错误: {error}", "static_route_processor.detailed_error_info": "详细错误信息: {error}", "static_route_processor.dict_format_detected": "检测到字典格式的静态路由数据，直接使用", "static_route_processor.duplicate_route_merged": "合并重复路由: {destination}, 网关数量: {count}", "static_route_processor.found_routes": "找到 {count} 条静态路由", "static_route_processor.invalid_destination": "无效的目标网络: {destination}", "static_route_processor.invalid_distance": "无效的管理距离: {distance}", "static_route_processor.invalid_gateway": "无效的网关地址: {gateway}", "static_route_processor.list_format_detected": "检测到列表格式的静态路由数据，转换为字典格式", "static_route_processor.no_data_to_process": "没有静态路由数据需要处理", "static_route_processor.no_routes_to_process": "没有静态路由需要处理", "static_route_processor.original_data_content": "原始静态路由数据内容: {content}", "static_route_processor.original_data_type": "原始静态路由数据类型: {type}", "static_route_processor.processing_complete": "静态路由处理完成: 转换成功 {success}/{total}", "static_route_processor.processing_complete_detailed": "静态路由处理完成: 转换成功 {converted} 个，跳过 {skipped} 个，失败 {failed} 个", "static_route_processor.processing_complete_with_groups": "静态路由处理完成: 转换成功 {converted}/{total}, 分组 {groups} 个", "static_route_processor.route_config_format_error": "路由 {route_id} 配置格式错误: 期望dict，实际{type}", "static_route_processor.route_conversion_index": "转换路由: 索引{index} -> 键{route_id}", "static_route_processor.route_conversion_success": "静态路由转换成功: {destination} via {gateway}", "static_route_processor.route_conversion_success_detailed": "静态路由转换成功: {route_id}, 目标: {destination}, 下一跳: {nexthop}", "static_route_processor.route_grouping_complete": "路由分组完成: {routes} 个路由分为 {groups} 个目的网段", "static_route_processor.route_interface_no_mapping": "路由 {route_id}: 接口 {device} 未配置映射关系", "static_route_processor.route_invalid_destination": "路由 {route_id}: 无效的目标地址 {dst}", "static_route_processor.route_missing_destination": "路由 {route_id}: 缺少目标地址", "static_route_processor.route_missing_nexthop": "路由 {route_id}: 缺少有效的下一跳配置", "static_route_processor.route_processing_failed": "路由 {route_id} 处理失败: {error}", "static_route_processor.route_skipped": "跳过静态路由: {destination} - {reason}", "static_route_processor.route_xml_creation_failed": "创建静态路由XML失败: {destination} - {error}", "static_route_processor.route_xml_generated": "生成静态路由XML: {destination} via {gateway} (距离: {distance})", "static_route_processor.skip_invalid_route_data": "跳过无效的路由数据: 索引{index}, 类型{type}", "static_route_processor.start_processing": "开始静态路由处理", "static_route_processor.static_routes_format_error": "静态路由数据格式错误: 期望dict，实际{type}", "static_route_processor.unsupported_data_format": "不支持的静态路由数据格式: {type}", "static_route_processor.xml_fragment_failed": "生成静态路由XML片段失败: {error}", "static_route_processor.xml_fragment_success": "生成静态路由XML片段，包含 {count} 条路由", "static_route_processor.xml_fragment_with_groups": "生成静态路由XML片段，包含 {count} 个路由组", "stats.details": "详情", "stats.failed": "失败", "stats.skipped": "忽略", "stats.success": "成功", "stats.total": "总计", "status.failed": "失败", "status.processing": "处理中", "status.skipped": "跳过", "status.success": "成功", "strategy_factory.cache_cleared": "strategy factory: cache cleared", "strategy_factory.initialized": "策略工厂已初始化", "strategy_factory.strategy_cache_hit": "strategy factory: strategy cache hit", "strategy_factory.strategy_created": "strategy factory: strategy created", "strategy_factory.strategy_registered": "strategy factory: strategy registered", "strategy_factory.unsupported_vendor": "strategy factory: unsupported vendor", "success": "转换成功", "success.conversion_complete": "转换完成", "suggestion.address.convert_fqdn": "转换 {count} 个FQDN地址对象", "suggestion.address.dynamic_desc": "以下动态地址对象不支持：{addresses}", "suggestion.address.dynamic_step1": "确定动态地址的实际IP范围", "suggestion.address.dynamic_step2": "创建静态地址对象替代", "suggestion.address.fqdn_desc": "以下FQDN地址对象需要手动转换：{addresses}", "suggestion.address.fqdn_step1": "解析FQDN为具体IP地址", "suggestion.address.fqdn_step2": "创建对应的IP地址对象", "suggestion.address.fqdn_step3": "更新相关策略规则中的引用", "suggestion.address.replace_dynamic": "替换 {count} 个动态地址对象", "suggestion.check_interface_name_or_add_mapping": "请检查接口名称是否正确，或添加相应的接口映射", "suggestion.check_logs_and_fix_interface_config": "请查看详细日志，根据兼容性问题修改接口配置", "suggestion.check_policy_type": "检查策略类型并手动配置", "suggestion.check_route_type": "检查路由类型并手动配置", "suggestion.check_vlan_id_range": "请检查VLAN ID是否在有效范围(1-4063)内，或其他接口配置是否有效", "suggestion.check_zone_interface_names": "请检查区域接口名称是否正确，或添加相应的接口映射", "suggestion.fix_xml_errors": "请修正XML中的错误或检查YANG模型是否正确", "suggestion.general.backup_config": "备份原始配置", "suggestion.general.backup_desc": "在部署新配置前备份原始配置", "suggestion.general.backup_step1": "导出当前设备配置", "suggestion.general.backup_step2": "保存配置文件到安全位置", "suggestion.general.verify_config": "验证转换后的配置", "suggestion.general.verify_desc": "建议在部署前验证配置的正确性", "suggestion.general.verify_step1": "检查XML配置文件的语法", "suggestion.general.verify_step2": "在测试环境中验证策略规则", "suggestion.general.verify_step3": "确认所有功能正常工作", "suggestion.interface.add_mapping": "添加接口 {interface} 的映射关系", "suggestion.interface.batch_mapping": "批量添加 {count} 个接口的映射关系", "suggestion.interface.batch_mapping_desc": "以下接口缺少映射关系：{interfaces}", "suggestion.interface.batch_step1": "检查接口映射文件的完整性", "suggestion.interface.batch_step2": "批量添加缺失的接口映射", "suggestion.interface.batch_step3": "验证映射关系的正确性", "suggestion.interface.manual_config": "手动配置接口 {interface}", "suggestion.interface.manual_step1": "在目标设备上手动创建对应接口", "suggestion.interface.manual_step2": "配置接口 {interface} 的IP地址和相关参数", "suggestion.interface.mapping_missing_desc": "接口 {interface} 缺少映射关系，无法自动转换", "suggestion.interface.mapping_step1": "打开接口映射文件 interface_mapping.json", "suggestion.interface.mapping_step2": "添加映射条目：\"{interface}\": \"目标接口名\"", "suggestion.interface.mapping_step3": "重新运行转换工具", "suggestion.interface.type_unsupported_desc": "接口 {interface} 的类型不支持自动转换", "supported_formats": "支持的格式", "t.root": "[待翻译] t.root", "target_content": "目标内容", "target_format": "目标格式", "template_cache.cache_cleared": "template cache: cache cleared", "template_cache.cache_hit": "template cache: cache hit", "template_cache.initialized": "模板缓存已初始化", "template_cache.lru_evicted": "template cache: l<PERSON> evicted", "template_cache.template_cached": "template cache: template cached", "template_cache.template_removed": "template cache: template removed", "template_loader.child_count": "子元素数量: {count}", "template_loader.file_size": "模板文件大小: {size} 字节", "template_loader.load_success": "模板加载成功: 文件={path}", "template_loader.loading_template": "开始加载模板文件: {path}", "template_loader.namespace": "命名空间: '{namespace}'", "template_loader.root_element": "根元素: 完整='{full}', 本地='{local}'", "template_loader.template_loaded_successfully": "template loader: template loaded successfully", "template_loader.validation_failed": "template loader: validation failed", "template_manager.cache_cleared": "模板管理器：缓存已清理", "template_manager.cache_hit": "模板管理器：缓存命中", "template_manager.cache_hit_new": "模板缓存命中: {key}", "template_manager.initialized": "模板管理器已初始化", "template_manager.invalid_namespace": "模板管理器：无效的命名空间", "template_manager.invalid_root_element": "模板管理器：无效的根元素", "template_manager.missing_vrf_element": "模板管理器：缺少VRF元素", "template_manager.namespace_fixed": "模板管理器：命名空间已修复", "template_manager.namespace_mismatch_new": "命名空间不匹配: 实际='{actual}', 期望='{expected}'", "template_manager.namespace_preserved_new": "模板文件命名空间保持原样，不进行修改", "template_manager.namespaces_loaded_new": "命名空间已加载", "template_manager.schema_loaded_new": "模式已加载", "template_manager.structure_validation_passed": "模板管理器：结构验证通过", "template_manager.template_loaded": "模板管理器：模板已加载", "template_manager.template_loaded_new": "模板加载成功: 型号={model}, 版本={version}, 类型={type}", "template_manager.template_loaded_success_new": "模板已加载", "template_manager.template_prepared_new": "模板准备完成: {path}", "template_manager.template_reloaded": "模板管理器：模板已重新加载", "template_manager.validation_failed": "模板管理器：验证失败", "template_manager.validation_failed_new": "模板验证失败: 路径={path}, 错误={error}", "time_range.adding_time_ranges": "正在添加时间范围，数量: {count}", "time_range.create_new": "创建新时间范围: {name}", "time_range.create_time_range_node": "创建时间对象节点", "time_range.fixed_vrf_name_tag": "修复VRF名称标签", "time_range.found_existing": "找到现有时间范围: {name}", "time_range.missing_name": "时间对象缺少名称", "time_range.no_time_ranges_to_add": "没有时间对象需要添加", "time_range.processing": "正在处理时间范围: {name}", "time_range.set_disabled": "设置时间范围为禁用: {name}", "time_range.unnamed": "未命名时间对象", "time_range.update_existing": "更新现有时间范围: {name}", "time_range_processing_stage.completed": "时间对象处理完成，总计: {total}，转换: {converted}", "time_range_processing_stage.description": "时间对象处理阶段", "time_range_processing_stage.no_config_data": "没有配置数据", "time_range_processing_stage.no_schedule_objects": "没有时间对象需要处理", "time_range_processing_stage.processing_failed": "时间对象处理失败: {error}", "time_range_processing_stage.schedule_conversion_failed": "时间对象 {name} 转换失败: {error}", "time_range_processing_stage.starting": "开始处理时间对象", "time_range_processing_stage.unsupported_schedule_type": "不支持的时间对象类型 {name}: {type}", "time_range_processor.add_period": "添加period {index}: {start}-{end}, 星期: {weekdays}", "time_range_processor.always_schedule_detected": "检测到always时间对象: {name}", "time_range_processor.conversion_success": "时间对象转换成功: {name} (类型: {type})", "time_range_processor.cross_day_time_detected": "检测到跨天时间对象: {name}, {start}-{end}", "time_range_processor.cross_day_time_split": "跨天时间拆分: {start}-{end} → 两个period", "time_range_processor.detailed_error_info": "详细错误信息: {error}", "time_range_processor.found_objects": "找到 {count} 个时间对象", "time_range_processor.invalid_weekday_name": "无效的星期名称: {weekday}", "time_range_processor.no_objects_to_process": "没有时间对象需要处理", "time_range_processor.onetime_conversion": "一次性时间对象转换: {name}, 开始: {start}, 结束: {end}", "time_range_processor.periodic_time_conversion": "周期性时间对象转换: {name}, 星期: {weekdays}, 时间: {start}-{end}, periods: {count}", "time_range_processor.processing_complete": "时间对象处理完成: 转换成功 {success}/{total}", "time_range_processor.time_format_non_standard": "时间格式不标准: {time}", "time_range_processor.time_format_parse_failed": "时间格式解析失败: {start} - {end}", "time_range_processor.time_group_conversion": "时间对象组转换: {name}, 成员: {members}", "time_range_processor.time_group_xml_not_supported": "时间对象组 {name} 暂不支持XML生成", "time_range_processor.xml_fragment_failed": "生成时间对象XML片段失败: {error}", "time_range_processor.xml_fragment_success": "生成时间对象XML片段，包含 {count} 个对象", "timeout_error": "请求超时", "tmpgdwacvox.xml": "[待翻译] tmpgdwacvox.xml", "transparent_mode_processor.device_file_load_error": "设备配置文件加载错误: {error}", "transparent_mode_processor.device_file_loaded": "设备配置文件已加载: {device}", "transparent_mode_processor.device_file_not_found": "设备配置文件未找到: {file}", "transparent_mode_processor.device_interfaces": "设备接口总数: {count}", "transparent_mode_processor.initialized": "透明模式处理器初始化完成", "transparent_mode_processor.interface_classification": "接口分类完成 - 管理接口: {mgmt_count}, 桥接接口: {bridge_count}", "transparent_mode_processor.interface_not_in_device": "接口 {interface} 不在设备接口列表中", "transparent_mode_processor.invalid_mgmt_interface": "管理接口配置无效，将使用默认设置", "transparent_mode_processor.mgmt_found": "发现管理IP配置: {mgmt_ip}", "transparent_mode_processor.mgmt_not_found": "transparent mode processor: mgmt not found", "transparent_mode_processor.mode_detection": "模式检测: opmode={opmode}", "transparent_mode_processor.nat_mode_fallback": "回退到NAT模式处理", "transparent_mode_processor.processing_transparent_mode": "开始处理透明模式配置", "transparent_mode_processor.transparent_mode_processed": "透明模式处理完成", "type.address_group": "地址组", "type.address_object": "地址对象", "type.interface": "接口", "type.policy_rule": "策略规则", "type.service_group": "服务组", "type.service_object": "服务对象", "type.static_route": "静态路由", "type.time_object": "时间对象", "ui.check_parent_dir": "检查父目录: {dir}", "ui.current_working_dir": "当前工作目录: {dir}", "ui.dir_creation_failed": "创建目录失败: {error}", "ui.found_template": "找到可能的模板文件: {path}", "ui.found_templates_count": "找到 {count} 个可能的模板文件:", "ui.search_data_dir": "在整个数据目录中查找: {dir}", "ui.template_dir_created": "已创建模板目录: {dir}", "ui.template_dir_not_exists": "模板目录不存在: {dir}", "ui.template_path": "模板文件绝对路径: {path}", "ui.using_alt_template": "使用替代模板文件: {path}", "user.common.unknown": "未知", "user.common.unknown_issue": "未知问题", "user.common.unknown_reason": "未知原因", "user.config.applied": "已应用配置更改", "user.config.apply_failed": "应用配置更改失败: {reason}", "user.config.backup_created": "已创建配置备份: {file}", "user.config.conversion_complete": "配置转换完成: {source_format} -> {target_format}", "user.config.conversion_start": "开始转换配置: {source_format} -> {target_format}", "user.config.exported": "已导出配置: {file}", "user.config.imported": "已导入配置: {file}", "user.config.restored": "已恢复配置: {file}", "user.config.validation_complete": "配置验证完成", "user.config.validation_failed": "配置验证失败: {reason}", "user.config.validation_start": "开始验证配置", "user.detail.address_conversion": "地址对象转换", "user.detail.address_group_conversion": "地址对象组转换", "user.detail.and_more": "以及其他{count}个", "user.detail.conversion_statistics": "详细转换统计", "user.detail.detected_mode": "检测到{mode}模式", "user.detail.dns_conversion": "DNS转换", "user.detail.encryption_failed": "加密失败", "user.detail.encryption_success": "加密成功", "user.detail.failed_converted": "转换失败", "user.detail.interface_conversion": "接口转换", "user.detail.items_unit": "个", "user.detail.no_processing_needed": "无需处理", "user.detail.no_statistics_available": "无统计信息可用", "user.detail.operation_mode_detection": "操作模式检测", "user.detail.partial_converted": "部分转换", "user.detail.policy_conversion": "策略规则转换", "user.detail.processing_summary": "成功转换 {success}/{total}", "user.detail.service_conversion": "服务对象转换", "user.detail.service_group_conversion": "服务对象组转换", "user.detail.skipped_converted": "跳过转换", "user.detail.static_route_conversion": "静态路由转换", "user.detail.success_converted": "成功转换", "user.detail.time_range_conversion": "时间对象转换", "user.detail.validation_failed": "验证失败", "user.detail.validation_passed": "验证通过", "user.detail.xml_integration": "XML模板集成", "user.detail.zone_conversion": "区域转换", "user.error.config_parse_failed": "错误: 配置解析失败: {reason}", "user.error.conversion_failed": "配置转换失败", "user.error.duplicate_service_object": "错误: 发现重复的服务对象名称: {name}，请修改配置确保服务对象名称唯一", "user.error.encryption_error": "加密过程中发生错误: {error}", "user.error.encryption_failed": "加密失败: {message}", "user.error.file_not_exists": "文件不存在: {file}", "user.error.interface_mapping_invalid": "接口映射验证失败，发现 {count} 个无效映射，请检查映射配置", "user.error.interface_mapping_validation_failed": "接口映射验证失败：发现 {count} 个无效映射", "user.error.interface_model_validation_failed": "接口型号验证失败：发现 {count} 个无效映射", "user.error.mapping_parse_failed": "错误: 映射文件解析失败: {reason}", "user.error.parsing_config_failed": "配置解析失败", "user.error.target_config_generation_failed": "生成目标配置失败", "user.error.template_not_found": "找不到模板文件: {model}/{version}", "user.error.transparent_mode_processing_failed": "透明模式处理失败，将使用NAT模式继续转换", "user.error.unknown": "未知错误", "user.error.validation_failed": "验证失败", "user.error.yang_validation_failed": "YANG验证失败", "user.file.backup_created": "已创建文件备份: {file} -> {backup}", "user.file.deleted": "文件已删除: {file}", "user.file.not_found": "文件不存在: {file}", "user.file.permission_denied": "文件权限不足: {file}", "user.file.read_complete": "文件读取完成: {file}, 大小: {size} 字节", "user.file.read_failed": "文件读取失败: {file}, 原因: {reason}", "user.file.read_start": "开始读取文件: {file}", "user.file.write_complete": "文件写入完成: {file}, 大小: {size} 字节", "user.file.write_failed": "文件写入失败: {file}, 原因: {reason}", "user.file.write_start": "开始写入文件: {file}", "user.flow.conversion_process": "配置转换流程", "user.flow.no_stages_recorded": "未记录任何转换阶段", "user.flow.stage.address_group_processing": "地址对象组处理", "user.flow.stage.address_processing": "地址对象处理", "user.flow.stage.dns_processing": "DNS处理", "user.flow.stage.encryption": "配置加密", "user.flow.stage.interface_processing": "接口处理", "user.flow.stage.operation_mode": "操作模式检测", "user.flow.stage.policy_processing": "策略规则处理", "user.flow.stage.service_group_processing": "服务对象组处理", "user.flow.stage.service_processing": "服务对象处理", "user.flow.stage.static_route_processing": "静态路由处理", "user.flow.stage.time_range_processing": "时间对象处理", "user.flow.stage.xml_generation": "XML配置生成", "user.flow.stage.xml_template_integration": "XML模板集成", "user.flow.stage.yang_validation": "YANG模型验证", "user.flow.stage.zone_processing": "区域处理", "user.info.address_groups_processed": "地址组处理完成，共 {total} 个，成功 {success} 个，失败 {failed} 个，跳过 {skipped} 个", "user.info.address_objects_processed": "地址对象处理完成，共 {total} 个，成功 {success} 个，失败 {failed} 个，跳过 {skipped} 个", "user.info.config_file_generated": "配置文件生成成功", "user.info.config_file_read_success": "配置文件读取成功", "user.info.dns_config_added": "DNS配置已添加", "user.info.encrypted_file_generated": "加密配置文件生成成功", "user.info.interfaces_extracted": "已提取 {count} 个接口", "user.info.mapping_file_loaded": "映射文件加载成功", "user.info.mapping_file_read_success": "映射文件读取成功", "user.info.nat_mode_detected": "检测到NAT模式配置", "user.info.no_nat_rules_found": "未发现NAT规则", "user.info.no_system_settings_nat_mode": "未发现系统设置配置，默认使用NAT模式", "user.info.parsing_config_complete": "配置解析完成", "user.info.parsing_config_start": "开始解析配置文件", "user.info.policy_rules_processed": "策略规则处理完成，共 {total} 条，成功 {success} 条，失败 {failed} 条，跳过 {skipped} 条", "user.info.predefined_services_detected": "检测到 {count} 个预定义服务，将使用NTOS内置服务，无需转换", "user.info.processing_interface": "正在处理接口: {name}", "user.info.processing_policies": "正在处理策略规则", "user.info.processing_static_routes": "正在处理静态路由", "user.info.service_groups_processed": "服务组处理完成，共 {total} 个，成功 {success} 个，失败 {failed} 个，跳过 {skipped} 个", "user.info.service_objects_processed": "服务对象处理完成，共 {total} 个，成功 {success} 个，失败 {failed} 个，跳过 {skipped} 个", "user.info.start_parsing": "开始解析配置文件", "user.info.static_routes_processed": "静态路由处理完成，共 {total} 条，成功 {success} 条，失败 {failed} 条，跳过 {skipped} 条", "user.info.time_ranges_processed": "已处理 {count} 个时间对象", "user.info.transparent_mode_config": "透明模式配置 - 管理接口: {mgmt}, 桥接接口数量: {bridge_count}", "user.info.transparent_mode_detected": "检测到透明模式配置", "user.info.yang_validation_passed": "配置文件已通过YANG模型验证", "user.info.zones_extracted": "已提取 {count} 个区域", "user.item.failed": "{type} '{name}' 转换失败：{reason}", "user.item.impact": "影响：{impact}", "user.item.success": "{type} '{name}' 转换成功", "user.item.success_with_mapping": "{type} '{original_name}' → '{converted_name}' 转换成功", "user.item.suggestion": "建议：{suggestion}", "user.item.warning": "{type} '{name}' 警告：{warning}", "user.log.detailed_conversion_statistics": "详细转换统计", "user.log.no_statistics_available": "无统计信息可用", "user.log.post_migration_steps": "配置迁移后续步骤", "user.network.connected": "已连接到服务器: {server}", "user.network.connecting": "正在连接到服务器: {server}", "user.network.connection_failed": "连接服务器失败: {server}, 原因: {reason}", "user.network.disconnected": "已断开与服务器的连接: {server}", "user.network.download_complete": "文件下载完成: {url}, 保存为: {file}", "user.network.download_failed": "文件下载失败: {url}, 原因: {reason}", "user.network.download_progress": "下载进度: {percent}%, 速度: {speed}/s", "user.network.download_start": "开始下载文件: {url}", "user.network.request_failed": "网络请求失败: {url}, 状态码: {status_code}", "user.network.timeout": "网络请求超时: {url}", "user.operation.cancelled": "操作已取消: {operation}", "user.operation.complete": "操作完成: {operation}", "user.operation.failed": "操作失败: {operation}, 原因: {reason}", "user.operation.progress": "操作进度: {percent}%", "user.operation.retry": "重试操作: {operation}, 尝试次数: {attempt}/{max_attempts}", "user.operation.start": "开始操作: {operation}", "user.operation.timeout": "操作超时: {operation}", "user.report.comprehensive_conversion_report": "综合转换报告", "user.report.conversion_time": "转换耗时", "user.report.report_end": "报告结束", "user.report.target_model": "目标型号", "user.report.target_version": "目标版本", "user.report.vendor": "源厂商", "user.security.certificate_expired": "证书已过期: {certificate}", "user.security.certificate_invalid": "证书无效: {certificate}, 原因: {reason}", "user.security.decryption_complete": "数据解密完成", "user.security.decryption_failed": "数据解密失败: {reason}", "user.security.decryption_start": "开始解密数据", "user.security.encryption_complete": "数据加密完成", "user.security.encryption_failed": "数据加密失败: {reason}", "user.security.encryption_start": "开始加密数据", "user.security.login_failed": "登录失败: {username}, 原因: {reason}", "user.security.login_success": "登录成功: {username}", "user.security.logout": "用户已登出: {username}", "user.security.password_changed": "密码已更改: {username}", "user.security.permission_denied": "权限不足: {action}", "user.stage.address_config_suggestion": "请检查地址对象配置，确保IP地址格式正确", "user.stage.address_group_processing": "地址对象组转换", "user.stage.address_processing": "地址对象转换", "user.stage.and_others": "以及其他{count}个", "user.stage.complete": "{stage}完成，耗时{duration}", "user.stage.complete_with_stats": "{stage}完成：成功转换{success}/{total}，耗时{duration}", "user.stage.dns_processing": "DNS转换", "user.stage.failed": "{stage}失败：{error}，耗时{duration}", "user.stage.failed_converted": "转换失败: {count}个", "user.stage.failed_details": "失败详情", "user.stage.failed_items": "{stage}：{failed}个项目转换失败", "user.stage.fortigate_policy_conversion": "安全策略转换", "user.stage.fqdn_not_supported_suggestion": "FQDN和动态地址类型暂不支持，请手动配置或使用静态IP地址", "user.stage.interface_mapping_suggestion": "请在接口映射文件中添加这些接口的映射配置", "user.stage.interface_processing": "接口转换", "user.stage.interface_type_suggestion": "这些接口类型暂不支持自动转换，请手动配置", "user.stage.interface_type_unsupported": "接口类型不支持", "user.stage.mapping_config_suggestion": "请在接口映射文件中配置相应的接口映射关系", "user.stage.missing_interface_mapping": "缺少接口映射", "user.stage.mode_detected": "检测到{mode}模式", "user.stage.name.address_group_processing": "地址对象组处理", "user.stage.name.address_processing": "地址对象处理", "user.stage.name.dns_processing": "DNS处理", "user.stage.name.encryption": "配置加密", "user.stage.name.interface_processing": "接口处理", "user.stage.name.operation_mode": "操作模式检测", "user.stage.name.policy_processing": "策略规则处理", "user.stage.name.service_group_processing": "服务对象组处理", "user.stage.name.service_processing": "服务对象处理", "user.stage.name.static_route_processing": "静态路由处理", "user.stage.name.time_range_processing": "时间对象处理", "user.stage.name.xml_generation": "XML配置生成", "user.stage.name.yang_validation": "YANG模型验证", "user.stage.name.zone_processing": "区域处理", "user.stage.no_processing_needed": "无需处理", "user.stage.operation_mode": "操作模式检测", "user.stage.policy_conversion": "策略规则转换", "user.stage.processing_completed": "处理完成", "user.stage.processing_failed": "处理失败", "user.stage.progress": "{stage}进度：{processed}/{total} ({percent}%)", "user.stage.service_group_processing": "服务对象组转换", "user.stage.service_processing": "服务对象转换", "user.stage.skipped_converted": "跳过转换: {count}个", "user.stage.skipped_details": "跳过详情", "user.stage.skipped_items": "{stage}：{skipped}个项目被跳过", "user.stage.start": "开始{stage}", "user.stage.start_with_count": "开始{stage}，共{count}个项目", "user.stage.static_route_processing": "静态路由转换", "user.stage.success_converted": "成功转换: {count}个", "user.stage.suggestion": "建议", "user.stage.time_range_processing": "时间对象转换", "user.stage.unknown": "未知阶段", "user.stage.unknown_reason": "未知原因", "user.stage.xml_integration": "XML模板集成", "user.stage.xml_template_integration": "XML模板集成", "user.stage.zone_processing": "区域转换", "user.status.failed": "失败", "user.status.skipped": "跳过", "user.status.success": "成功", "user.status.unknown": "未知", "user.suggestion.action_steps": "操作步骤", "user.suggestion.no_suggestions": "无需额外操作", "user.suggestion.optional_improvements": "可选的改进：", "user.suggestion.post_migration_steps": "配置迁移后续步骤", "user.suggestion.reason": "原因", "user.suggestion.recommended_optimizations": "建议完成的优化：", "user.suggestion.required_configurations": "必须完成的配置：", "user.summary.address_groups_detail": "地址组: 总计 {total}, 成功 {success}, 失败 {failed}, 忽略 {skipped}", "user.summary.address_groups_processed": "地址组处理: 总计 {total}, 成功 {success}, 失败 {failed}, 跳过 {skipped}", "user.summary.addresses_detail": "地址对象: 总计 {total}, 成功 {success}, 失败 {failed}, 忽略 {skipped}", "user.summary.addresses_processed": "地址对象处理: 总计 {total}, 成功 {success}, 失败 {failed}, 跳过 {skipped}", "user.summary.compatibility_issue": "- {issue}: {detail} - {impact}", "user.summary.compatibility_issues": "兼容性问题", "user.summary.conversion_complete": "转换完成", "user.summary.conversion_stats": "转换统计", "user.summary.conversion_summary": "转换总结", "user.summary.conversion_time": "转换耗时: {time}", "user.summary.failed_item_detail": "- {type} '{name}': {reason}", "user.summary.failed_items_detail": "转换失败的项目详情", "user.summary.interfaces_detail": "接口: 总计 {total}, 成功 {success}, 失败 {failed}, 忽略 {skipped}", "user.summary.interfaces_processed": "接口处理: 总计 {total}, 成功 {success}, 失败 {failed}, 跳过 {skipped}", "user.summary.manual_config_item": "- {type}: {reason} - {suggestion}", "user.summary.manual_config_items": "需要人工配置的项目", "user.summary.manual_config_needed": "需要手动配置的项目", "user.summary.nat_rules_processed": "NAT规则处理: 总计 {total}, 成功 {success}, 失败 {failed}, 跳过 {skipped}", "user.summary.no_compatibility_issues": "无兼容性问题", "user.summary.no_manual_config_items": "无需人工配置的项目", "user.summary.no_manual_config_needed": "无需手动配置的项目", "user.summary.no_unconverted_items": "所有项目均已成功转换", "user.summary.policies_detail": "策略规则: 总计 {total}, 成功 {success}, 失败 {failed}, 忽略 {skipped}", "user.summary.policies_processed": "策略规则处理: 总计 {total}, 成功 {success}, 失败 {failed}, 跳过 {skipped}", "user.summary.routes_processed": "静态路由处理: 总计 {total}, 成功 {success}, 失败 {failed}, 跳过 {skipped}", "user.summary.service_groups_detail": "服务组: 总计 {total}, 成功 {success}, 失败 {failed}, 忽略 {skipped}", "user.summary.service_groups_processed": "服务组处理: 总计 {total}, 成功 {success}, 失败 {failed}, 跳过 {skipped}", "user.summary.services_detail": "服务对象: 总计 {total}, 成功 {success}, 失败 {failed}, 忽略 {skipped}", "user.summary.services_processed": "服务对象处理: 总计 {total}, 成功 {success}, 失败 {failed}, 跳过 {skipped}", "user.summary.skipped_item_detail": "- {type} '{name}': {reason}", "user.summary.skipped_items_detail": "跳过的项目详情", "user.summary.static_routes_detail": "静态路由: 总计 {total}, 成功 {success}, 失败 {failed}, 忽略 {skipped}", "user.summary.time_ranges_processed": "时间对象处理: 总计 {total}, 成功 {success}, 失败 {failed}, 跳过 {skipped}", "user.summary.total_items_processed": "共处理 {total} 个项目，成功 {success} 个，失败 {failed} 个，跳过 {skipped} 个", "user.summary.unconverted_item": "- {type}: {name}, 原因: {reason}", "user.summary.unconverted_items": "未转换的项目", "user.summary.zones_detail": "区域: 总计 {total}, 成功 {success}, 失败 {failed}, 忽略 {skipped}", "user.summary.zones_processed": "区域处理: 总计 {total}, 成功 {success}, 失败 {failed}, 跳过 {skipped}", "user.test.count": "当前计数: {count}", "user.test.hello": "你好，世界!", "user.test.welcome": "欢迎使用 {app}", "user.time.hours": "{hours} 小时", "user.time.minutes": "{minutes} 分钟", "user.time.seconds": "{seconds} 秒", "user.time.unknown": "未知", "user.warning.interface_mapping_missing": "警告: 接口映射缺失: {name}", "user.warning.interface_mapping_warnings": "警告：接口映射有 {count} 个警告。请查看日志了解详情。", "user.warning.interface_not_found": "警告: 未找到接口: {name}", "user.warning.nat_not_supported_in_version": "警告: 策略规则 '{rule_name}' 包含NAT/SNAT配置，但目标版本 '{version}' 不支持。NAT/SNAT仅支持R11及以后的版本", "user.warning.no_valid_interfaces": "警告: 没有有效的接口可处理", "user.warning.total_unmapped_zone_interfaces": "共有 {count} 个区域接口没有找到对应的映射", "user.warning.zone_unmapped_interfaces": "区域 {zone} 中有 {count} 个接口没有找到映射", "validation_failed": "配置验证失败: {reason}", "validation_service.initialized": "验证服务已初始化", "validation_service.initialized_new": "验证服务已初始化", "validation_service.mapping_valid": "validation service: mapping valid", "validation_workflow.initialized": "验证工作流已初始化", "validation_workflow.validation_completed": "validation workflow: validation completed", "validator.execute_windows_command": "Windows环境执行命令: {cmd}", "validator.execute_yanglint_command": "执行yanglint命令: {cmd}", "validator.found_yang_files": "找到YANG文件: {dir}，数量: {count}", "validator.interface_error_load_failed": "加载设备接口定义文件失败: {error}", "validator.interface_error_model_not_found": "未找到设备型号 {model} 的接口定义", "validator.interface_error_not_supported": "接口 {interface} 不在设备型号 {model} 支持的接口列表中", "validator.interface_error_parent_not_supported": "子接口 {interface} 的父接口 {parent_interface} 不在设备型号 {model} 支持的接口列表中", "validator.interface_error_subinterface_format": "子接口 {interface} 格式无效，应为 '接口名.VLAN_ID'", "validator.interface_error_vlan_id_range": "子接口 {interface} 的VLAN ID {vlan_id} 不在有效范围内（1-4063）", "validator.main_errors": "主要错误:", "validator.more_errors_not_shown": "...还有{count}个错误未显示", "validator.use_yang_model_dir": "使用YANG模型目录: {dir}", "validator.validate_xml": "使用yanglint验证XML: {file}", "validator.validation_failed_with_count": "YANG验证失败: 发现{count}个错误", "validator.validation_passed": "yang<PERSON>验证通过", "validator.yang_error_save_failed": "保存YANG验证错误信息失败: {error}", "validator.yang_error_saved": "YANG验证错误信息已保存到: {file}", "validator.yang_model_dir": "YANG模型目录: {dir}", "validator.yanglint_available": "yang<PERSON>可用，版本: {version}", "validator.yanglint_check_error": "validator: yang<PERSON> check error", "validator.yanglint_not_available": "validator: yanglint not available", "validator.yanglint_check_method_failed": "yanglint检测方法失败: {method}, 错误: {error}", "version_info": "版本信息: {version}", "vlan.access_control.create": "创建access-control标签，使用命名空间", "vlan.access_control.namespace.error": "错误: access-control标签命名空间设置失败，当前命名空间", "vlan.access_control.namespace.success": "access-control标签命名空间设置成功", "vlan.create_vlan_tag": "创建VLAN标签，使用命名空间: {namespace}", "vlan.error.invalid_name": "错误: VLAN接口名称无效或不是字符串", "vlan.interface_name": "XML中使用的VLAN接口名称", "vlan.ip_mac_bind.create": "创建ip-mac-bind标签，使用命名空间", "vlan.ip_mac_bind.namespace.error": "错误: ip-mac-bind标签命名空间设置失败，当前命名空间", "vlan.ip_mac_bind.namespace.success": "ip-mac-bind标签命名空间设置成功", "vlan.link_interface.default": "使用默认link-interface", "vlan.link_interface.error.missing": "错误: 无法设置link-interface，缺少parent_interface", "vlan.link_interface.error.not_string": "错误: parent_interface不是字符串", "vlan.link_interface.set": "设置link-interface", "vlan.mac_address.random": "为接口 {interface} 生成随机单播MAC地址", "vlan.monitor.create": "创建monitor标签，使用命名空间", "vlan.monitor.namespace.error": "错误: monitor标签命名空间设置失败，当前命名空间", "vlan.monitor.namespace.success": "monitor标签命名空间设置成功", "vlan.namespace.error": "错误: VLAN标签命名空间设置失败，当前命名空间: {namespace}", "vlan.namespace.success": "VLAN标签命名空间设置成功", "warning.address_group_missing_fields": "地址组缺少必要字段(名称或成员)", "warning.address_group_missing_name": "地址组缺少name字段", "warning.address_object_missing_name": "警告: 地址对象缺少name字段", "warning.address_object_no_name": "警告: address object no name", "warning.address_object_not_dict": "警告: 地址对象不是字典类型: {data}", "warning.address_objects_not_list": "警告: 地址对象不是列表类型，而是 {type}", "warning.bridge_interface_generation_skipped": "桥接接口生成被跳过", "warning.cannot_map_interface_for_route": "无法映射接口 {device}，只设置网关: {gateway}", "warning.cannot_map_parent_interface": "警告: 无法映射父接口 {parent}，将使用原名称", "warning.cannot_parse_port_range": "无法解析端口范围：{port_range}，忽略此端口", "warning.cannot_parse_subinterface_tunnel_id": "  警告: 无法解析子接口隧道ID: {id}", "warning.cannot_parse_tunnel_id": "  警告: 无法解析隧道ID: {id}", "warning.cannot_process_zone_interfaces": "无法处理区域 {zone} 的非列表类型接口，设置为空列表", "warning.cannot_process_zones": "无法处理非列表类型的zones参数，返回空列表", "warning.check_interfaces": "没有有效的接口需要处理，请检查接口配置和映射文件", "warning.compatibility_issue_detail": "兼容性问题: {name} - {impact}", "warning.compatibility_issues_detected": "检测到 {count} 个兼容性问题", "warning.compatibility_issues_found": "发现 {count} 项兼容性问题", "warning.config_dir_not_found": "未找到Config目录，将创建新的Config目录", "warning.default_translator_import_failed": "导入默认翻译器失败", "warning.device_may_not_support_routes": "警告: 目标设备或版本可能不支持静态路由配置", "warning.dns_missing_primary_server": "DNS配置段存在但缺少primary服务器设置", "warning.dns_using_server_instead_of_primary": "DNS配置使用server而非primary设置", "warning.empty_static_route_table": "警告: empty static route table", "warning.failed_to_add_nat_rules_to_xml": "添加NAT规则到XML失败", "warning.failed_to_add_security_policies_to_xml": "添加安全策略到XML失败", "warning.failed_to_convert_policy_rule": "策略规则转换失败: {name}", "warning.file_empty": "警告: 文件为空", "warning.format_error": "格式错误: {error}", "warning.format_key_failed": "格式化键 '{key}' 也失败: {error}", "warning.format_message_error": "格式化消息出错: {error}", "warning.generator_empty_service_node_removal_failed": "警告: 空的服务对象节点移除失败", "warning.generator_no_service_object_node": "警告: 最终XML中没有找到服务对象节点", "warning.generator_service_object_node_empty": "警告: 服务对象节点为空，将移除该节点", "warning.i18n_import_failed": "无法导入i18n模块，使用简单格式化: {key}", "warning.i18n_import_failed_with_key": "无法导入i18n模块，使用原始键: {key}, 错误: {error}", "warning.improved_legacy_generator_fqdn_not_supported": "警告: 服务对象 {name} 包含FQDN配置 {fqdn}，NTOS系统不支持，跳过处理", "warning.improved_legacy_generator_iprange_not_service": "警告: 服务对象 {name} 包含IP范围配置 {iprange}，应归类为地址对象，跳过处理", "warning.improved_legacy_generator_missing_port_config": "警告: 服务对象 {name} 协议为 {protocol} 但缺少端口配置，跳过处理", "warning.improved_legacy_generator_skip_duplicate_service": "跳过重复的服务对象: {name}", "warning.improved_legacy_generator_skip_unnamed_service": "跳过无名称的服务对象: {service}", "warning.improved_legacy_generator_unsupported_protocol": "警告: 服务对象 {name} 使用不支持的协议 {protocol}，跳过处理", "warning.interface_data_invalid_type": "接口 {name} 的数据类型无效", "warning.interface_data_is_string": "警告: 接口 {name} 的数据是字符串类型，无法处理", "warning.interface_data_not_list": "警告: 接口数据不是列表类型，进行修正", "warning.interface_mapping_empty": "警告: 接口映射表为空", "warning.interface_mapping_file_not_found": "警告: 接口映射文件未找到: {file}", "warning.interface_mapping_not_found": "警告: 接口 {interface} 没有找到对应的映射，使用原名", "warning.interface_mapping_target_not_supported": "目标接口 {interface} 不受设备型号 {model} 支持", "warning.interface_mapping_warning_details": "警告：源接口 {source} 映射到 {target}：{reason}", "warning.interface_mapping_warnings": "接口映射有 {count} 个警告", "warning.interface_missing_name": "警告: 接口 {name} 缺少name字段，跳过", "warning.interface_missing_name_data": "警告: 接口数据缺少name字段，跳过: {data}", "warning.interface_missing_name_field": "接口 {name} 缺少name字段", "warning.interface_missing_raw_name": "警告: 接口缺少raw_name字段，跳过: {interface}", "warning.interface_missing_raw_name_field": "警告: 接口缺少raw_name字段: {interface}", "warning.interface_no_mapping": "警告: 接口 {interface} 没有找到对应的映射，使用原名", "warning.interface_not_found_in_config": "警告: 区域 {zone} 中的接口 {interface} 未在配置中找到", "warning.interface_not_found_in_mapping": "警告: 跳过区域 {zone} 中未映射的接口 {interface}", "warning.interface_not_found_using_default_zone": "警告: 接口 '{interface}' 未找到映射，使用默认区域: {zone}", "warning.interface_not_in_mapping": "警告: 接口 '{interface}' 不在 interface_mapping 中，保留原名", "warning.interface_not_mapped": "警告: 路由使用的接口 '{interface}' 没有映射关系，请检查接口映射", "warning.interface_not_string": "警告: 区域 {zone} 中的接口不是字符串类型: {interface}", "warning.invalid_address_group_type": "无效的地址组类型: {type}", "warning.invalid_address_object_type": "无效的地址对象类型: {type}", "warning.invalid_fortigate_config": "配置文件缺少飞塔基本配置块", "warning.invalid_interfaces_count": "警告: {count} 个接口因配置无效而被跳过", "warning.invalid_min_version_format": "无效的最低版本格式: {min_version}", "warning.invalid_policy_rule_type": "规则 #{index} 的类型无效: {type}", "warning.invalid_port_range": "无效的端口范围：{port_range}，忽略此端口", "warning.invalid_range_format": "警告: invalid range format", "warning.invalid_route_data_type": "路由 #{index} 的类型无效: {type}", "warning.invalid_route_destination": "无效的路由目的地: {data}", "warning.invalid_route_destination_type": "无效的路由目的地类型: {type}", "warning.invalid_route_gateway": "无效的路由网关: {data}", "warning.invalid_schedule_skipped": "跳过无效时间表: {name}", "warning.invalid_service_group_type": "无效的服务组类型: {type}", "warning.invalid_service_object": "无效的服务对象", "warning.invalid_service_object_type": "无效的服务对象类型: {type}", "warning.invalid_tunnel_id": "警告: invalid tunnel id", "warning.invalid_zone_data_type": "无效的区域数据类型: {type}", "warning.invalid_zone_interface": "区域 {zone} 中的接口 {interface} 无效", "warning.ippool_invalid_ip_format": "IP池 '{pool_name}' IP地址格式无效: {error}", "warning.ippool_invalid_range": "IP池 '{pool_name}' 地址范围无效: 起始IP {start_ip} > 结束IP {end_ip}", "warning.ippool_missing_field": "IP池 '{pool_name}' 缺少必需字段 '{field}'", "warning.legacy_policy_rule_missing_name": "策略规则缺少名称，跳过处理", "warning.mapping_source_not_in_config": "映射源接口 {source} 在配置中未找到，目标：{target}", "warning.minimum_fortigate_version": "配置文件版本({version})刚好满足最低要求", "warning.missing_config_version_tag": "警告: missing config version tag", "warning.missing_dns_config_section": "缺少DNS配置段 (config system dns)", "warning.missing_dns_settings": "警告: missing dns settings", "warning.missing_end_datetime": "缺少结束日期时间: {name}", "warning.missing_fqdn": "警告: missing fqdn", "warning.missing_ip_address": "警告: missing ip address", "warning.missing_param": "消息中缺少参数: {param}", "warning.missing_param_continue": "警告: 消息中缺少参数 {param}，但继续处理", "warning.missing_param_fallback": "消息中缺少参数: {param}", "warning.missing_params": "消息中缺少参数: {missing_params}", "warning.missing_root_vdom": "警告: missing root vdom", "warning.missing_routing_namespace": "nsmap中缺少路由命名空间，添加默认值", "warning.missing_start_datetime": "缺少开始日期时间: {name}", "warning.name_changed_during_processing": "警告: 处理过程中名称发生变化: 期望值={expected}, 实际值={actual}", "warning.namespace_cleanup_lost_elements": "警告: 命名空间清理导致元素丢失，原始: {original}，剩余: {remaining}", "warning.namespace_cleanup_lost_many_elements": "警告: namespace cleanup lost many elements", "warning.nat_rule_skipped_version_not_supported": "警告: 策略规则 '{rule_name}' 包含NAT配置，但目标版本 '{version}' 不支持NAT/SNAT规则，已跳过NAT配置", "warning.no_interface_mapping_to_save": "警告: 没有接口映射需要保存", "warning.no_interfaces_to_process": "警告: 没有接口需要处理", "warning.no_ntos_yang_files": "警告: 未找到ntos*.yang文件", "warning.no_service_obj_node_in_final_xml": "警告: 最终XML中没有找到服务对象节点", "warning.no_static_routes_support": "警告: NTOS生成器不支持添加静态路由，已跳过", "warning.no_valid_interfaces": "没有有效的接口需要处理，跳过配置生成", "warning.ntos_services_file_not_found": "找不到NTOS服务文件: {path}", "warning.path_corrected": "路径修正: {original} -> {corrected}", "warning.physical_interface_namespace_query_failed": "警告: physical interface namespace query failed", "warning.physical_interface_no_namespace_query_failed": "警告: physical interface no namespace query failed", "warning.physical_interface_uri_query_failed": "警告: physical interface uri query failed", "warning.physical_interface_without_name": "警告: physical interface without name", "warning.policies_missing_log_setting_detailed": "发现{count}个策略缺少日志设置 (策略ID: {policies})", "warning.policies_missing_log_setting_summary": "发现{count}个策略缺少日志设置 (示例策略ID: {sample_policies}...)", "warning.policy_missing_log_setting": "警告: 策略缺少日志设置", "warning.port_out_of_range": "端口号超出有效范围：{port_range}，忽略此端口", "warning.redundant_namespace_prefixes": "冗余的命名空间前缀: {prefixes}, 元素: {element}", "warning.reference_to_undefined_address": "警告: 引用了未定义的地址对象", "warning.reference_to_undefined_address_detailed": "策略{policy_id}中{addr_type}addr引用了未定义的地址对象: {address}", "warning.reference_to_undefined_interface_detailed": "策略{policy_id}中{intf_type}intf引用了未定义的接口: {interface}", "warning.reference_to_undefined_service_detailed": "策略{policy_id}中引用了未定义的服务对象: {service}", "warning.replacing_main_with_proper_name": "警告: 将'main'替换为正确名称: {final}", "warning.replacing_main_with_vlan_name": "警告: 将'main'替换为VLAN名称: {original} -> {fixed}", "warning.route_interface_not_mapped": "警告: 接口 '{interface}' 未在映射表中找到，可能导致配置不正确", "warning.route_interface_not_mapped_check": "警告: 路由使用的接口 '{interface}' 没有映射关系，请检查接口映射", "warning.route_missing_destination": "警告: 静态路由 {id} 缺少目的网络信息，无法处理", "warning.route_missing_next_hop": "警告: 路由缺少下一跳信息", "warning.schedule_conversion_failed": "时间表转换失败: {name}", "warning.schedule_only_uuid_content_skipped": "跳过仅有UUID内容的时间表: {name}", "warning.schedule_only_uuid_direct_skipped": "直接跳过仅有UUID的时间表: {name}", "warning.schedule_only_uuid_skipped": "跳过仅有UUID的时间表: {name}", "warning.semantic_verification": "语义验证警告: {warning}", "warning.service_group_member_not_found": "服务组成员未找到: {name}", "warning.service_group_missing_fields": "服务组缺少必要字段(名称或成员)", "warning.service_group_missing_name": "服务组缺少name字段", "warning.service_group_no_members": "服务组无成员: {name}", "warning.service_group_no_valid_members": "服务组无有效成员: {name}", "warning.service_mapping_config_file_not_found": "服务映射文件未找到: {file}", "warning.service_mapping_file_not_found": "未找到服务映射文件: {path}", "warning.service_merge_conflict": "服务合并冲突: {service}, 保留原始定义", "warning.service_missing_name": "警告: service missing name", "warning.service_missing_port_config": "服务缺少端口配置: {name} (协议: {protocol})", "warning.service_missing_protocol_and_ports": "警告: 服务对象 {name} 缺少协议类型和端口范围，将使用默认协议类型 tcp", "warning.service_object_missing_name": "服务对象缺少name字段", "warning.service_object_missing_port_config": "警告：服务对象 {name} 协议为 {protocol} 但缺少端口配置，跳过此服务", "warning.service_processor_failed": "服务处理器初始化失败: {error}", "warning.skip_non_dict_service_object": "警告: 跳过非字典类型的服务对象: {object_type}", "warning.skip_object": "跳过{type} {name}: {reason}", "warning.skip_service_object_missing_name": "警告: 跳过缺少名称的服务对象", "warning.skipped_interfaces": "警告: {count} 个接口配置因无效而被跳过，请查看日志获取详情", "warning.skipped_zone_interface_detail": "  - 区域 '{zone}' 中的接口 '{interface}': {reason}", "warning.skipped_zone_interfaces": "警告: {count} 个区域接口因未找到映射而被跳过，请查看日志获取详情", "warning.skipped_zone_interfaces_count": "警告: {count} 个区域接口因未找到映射而被跳过", "warning.skipped_zone_interfaces_summary": "警告: 共有 {count} 个接口因未找到映射而被跳过", "warning.skipping_address_group_empty_members": "警告: skipping address group empty members", "warning.skipping_address_group_missing_name": "警告: skipping address group missing name", "warning.skipping_address_invalid_subnet": "跳过无效子网的地址: {name} ({subnet})", "warning.skipping_address_missing_fqdn": "跳过地址对象 {name}: 缺少FQDN信息", "warning.skipping_address_missing_ip_range": "跳过缺少IP范围的地址: {name}", "warning.skipping_address_missing_name": "跳过缺少名称的地址: {name}", "warning.skipping_address_missing_subnet": "跳过缺少子网的地址: {name}", "warning.skipping_address_missing_type": "跳过地址对象 {name}: 缺少类型", "warning.skipping_address_type_not_explicitly_supported": "跳过未明确支持的地址类型: {name} ({type})", "warning.skipping_address_unsupported_type": "跳过不支持的地址类型: {name} ({type})", "warning.skipping_empty_ipv6_route": "跳过空的IPv6路由", "warning.skipping_interface_associated_address": "跳过接口关联地址: {name}", "warning.skipping_invalid_interface": "警告: 跳过无效接口 {raw_name} -> {name}: {reason}", "warning.skipping_invalid_interface_with_reason": "跳过无效接口 {interface_name}: {reason}", "warning.skipping_non_dict_zone": "警告: 跳过非字典类型的区域: {zone}", "warning.skipping_policy_rule_missing_fields": "跳过策略规则 {name}: 缺少必要字段: {fields}", "warning.skipping_policy_rule_unmapped_interfaces": "跳过策略规则 {name}: 接口未映射: {interfaces}", "warning.skipping_service_missing_name": "跳过服务对象 {name}: 缺少名称", "warning.skipping_service_missing_port_range": "跳过服务对象 {name}: 缺少端口范围信息", "warning.skipping_service_missing_protocol": "跳过服务对象 {name}: 缺少协议类型", "warning.skipping_service_unsupported_protocol": "跳过服务对象 {name}: 不支持的协议类型: {protocol}", "warning.static_routes_not_supported": "警告: NTOS生成器不支持添加静态路由，已跳过", "warning.subinterface_mapping_failed": "警告: 子接口 '{interface}' 智能映射失败 (类型: {type})", "warning.subinterface_missing_parent_or_vlanid_skipped": "警告: 子接口 '{raw_name}' 缺少父接口或VLAN ID信息，跳过此子接口 (父接口: {has_parent}, VLAN ID: {has_vlanid})", "warning.subinterface_parent_not_mapped": "子接口 {raw_name} 的父接口 {parent} 没有映射关系，跳过该子接口", "warning.subinterface_parent_not_mapped_skipped": "警告: 子接口 '{raw_name}' 的父接口 '{parent}' 没有映射关系，跳过此子接口", "warning.tcp_port_range_invalid": "TCP端口范围无效：{ports}，使用原始值", "warning.template_dir_not_exists": "模板目录不存在: {dir}", "warning.time_range_missing_name": "警告: time range missing name", "warning.time_ranges_not_supported": "目标设备不支持时间对象", "warning.total_unmapped_zone_interfaces": "警告: 共有 {count} 个区域接口没有找到对应的映射", "warning.total_unmapped_zone_interfaces_skipped": "警告: 共有 {count} 个区域接口没有找到对应的映射，这些接口将被跳过", "warning.translation_error": "翻译出错: {key}, {error}", "warning.translation_format_error": "翻译格式错误: {translation_key}，错误: {error}", "warning.translation_format_error_with_message": "翻译和格式化出错: {message}, {error}", "warning.translation_key_failed": "翻译键 '{key}' 处理失败: {error}", "warning.translation_missing_param": "翻译 '{key}' 中缺少参数: {param}", "warning.translation_missing_params": "翻译 '{key}' 中缺少参数: {missing_params}", "warning.udp_port_range_invalid": "UDP端口范围无效：{ports}，使用原始值", "warning.unable_to_detect_version": "无法检测配置文件的FortiOS版本", "warning.unknown_address_type": "未知的地址对象类型: {type}", "warning.unsupported_feature": "警告: 不支持的功能: {feature}", "warning.unsupported_interface_roles": "发现 {count} 个接口使用了不支持的角色，NTOS系统只支持lan和wan角色", "warning.unsupported_role": "不支持的接口角色: {role}", "warning.using_default_name": "使用默认名称: {name}", "warning.vendor_mapping_file_not_found": "找不到 {vendor} 服务映射文件: {path}", "warning.verification_failed": "配置验证失败: {error}", "warning.vlan_id_not_in_range": "VLAN ID不在有效范围内: {vlanid}", "warning.vlan_interface_incomplete_data": "VLAN接口数据不完整: {name}", "warning.vlan_interface_namespace_query_failed": "警告: vlan interface namespace query failed", "warning.vlan_interface_no_namespace_query_failed": "警告: vlan interface no namespace query failed", "warning.vlan_interface_uri_query_failed": "警告: vlan interface uri query failed", "warning.vlan_interface_without_name": "警告: vlan interface without name", "warning.vlanid_out_of_range": "警告: VLAN ID {vlanid} 超出有效范围 (1-4063)，将限制在范围内", "warning.warning_message_failed": "警告消息处理失败: {error}", "warning.xml_validation_skipped": "跳过XML验证: {message}", "warning.yang_generator_init_failed": "警告: yang generator init failed", "warning.yang_validation_skipped": "跳过YANG模型验证，XML文件已成功生成", "warning.yanglint_command_error": "yanglint命令返回错误", "warning.yanglint_command_not_found": "未找到yanglint命令", "warning.yanglint_not_available_skipping_validation": "yanglint工具不可用，跳过YANG模型验证（XML文件已成功生成）", "warning.zone_data_not_list": "警告: 区域数据不是列表类型，而是 {type}", "warning.zone_interface_not_mapped": "警告: 区域 {zone} 中的接口 {interface} 没有找到对应的映射", "warning.zone_interfaces_not_list": "警告: 区域 {zone} 的接口不是列表类型，而是 {type}", "warning.zone_missing_name": "警告: zone missing name", "warning.zone_unmapped_interfaces": "警告: 区域 {zone} 中有 {count} 个接口没有找到映射", "warning.zone_unmapped_interfaces_skipped": "警告: 区域 {zone} 中有 {count} 个接口没有找到映射，这些接口将被跳过", "warning.zones_not_list": "警告: zones参数不是列表类型，而是 {type}", "welcome": "欢迎使用配置转换服务", "wildcard.dropbox.com": "[待翻译] wildcard.dropbox.com", "wildcard.google.com": "[待翻译] wildcard.google.com", "xml_generator_adapter.invalid_input_data": "xml generator adapter: invalid input data", "xml_integrator.bridge_integration_failed": "XML集成器：桥接集成失败: {error}", "xml_integrator.interface_working_modes_integrated": "XML集成器：接口工作模式集成完成，接口数量: {count}", "xml_integrator.invalid_policy_no_name": "XML集成器：无效策略，缺少名称字段", "xml_integrator.namespace_set": "XML集成器：为标签 {tag} 设置命名空间: {namespace}", "xml_integrator.nat_rule_integration_failed": "XML集成器：NAT规则集成失败", "xml_integrator.nat_rules_integrated": "XML集成器：NAT规则集成完成", "xml_integrator.no_interfaces_to_integrate": "XML集成器：没有接口需要集成", "xml_integrator.no_nat_rules_to_integrate": "XML集成器：没有NAT规则需要集成", "xml_integrator.no_security_policies_to_integrate": "XML集成器：没有安全策略需要集成", "xml_integrator.policy_conversion_failed": "XML集成器：策略转换失败", "xml_integrator.policy_integrated_successfully": "XML集成器：策略集成成功", "xml_integrator.policy_integration_exception": "XML集成器：策略集成异常", "xml_integrator.policy_integration_failed": "XML集成器：策略集成失败", "xml_integrator.security_policies_integrated": "XML集成器：安全策略集成完成", "xml_integrator.security_policy_integration_failed": "XML集成器：安全策略集成失败", "xml_integrator.some_policies_failed": "XML集成器：部分策略集成失败", "xml_integrator.unknown_policy_format": "XML集成器：未知的策略格式", "xml_template_integration.add_default_source_port_to_protocol": "为{protocol}协议添加默认源端口", "xml_template_integration.add_default_tcp_source_port": "为TCP服务{service}添加默认源端口", "xml_template_integration.add_default_tcp_source_port_mixed": "为混合TCP服务{service}添加默认源端口", "xml_template_integration.add_default_udp_source_port": "为UDP服务{service}添加默认源端口", "xml_template_integration.add_default_udp_source_port_mixed": "为混合UDP服务{service}添加默认源端口", "xml_template_integration.add_description_to_existing_address_group": "添加描述到现有address-group", "xml_template_integration.add_description_to_existing_address_set": "DEBUG: 添加描述到现有address-set", "xml_template_integration.add_description_to_existing_security_zone": "添加描述到现有安全区域", "xml_template_integration.add_description_to_existing_service_group": "添加描述到现有service-group", "xml_template_integration.add_description_to_existing_service_set": "添加描述到现有service-set", "xml_template_integration.add_dest_port_to_existing_protocol": "为现有{protocol}协议添加目的端口: {port}", "xml_template_integration.add_dns_client_config": "添加DNS客户端配置", "xml_template_integration.add_dns_proxy_config": "添加DNS代理配置", "xml_template_integration.add_flags_field": "➕ DEBUG: 添加flags字段: {flags}", "xml_template_integration.add_ip_address": "➕ DEBUG: 添加IP地址: {ip}", "xml_template_integration.add_ipv4_field": "➕ DEBUG: 添加IPv4字段 {field} = {value}", "xml_template_integration.add_nat_to_vrf": "添加NAT配置到VRF", "xml_template_integration.add_new_address_set": "添加新的address-set: {name}", "xml_template_integration.add_new_address_to_existing_ipv4": "➕ DEBUG: 添加新地址配置到现有IPv4", "xml_template_integration.add_new_interface_to_security_zone": "添加新的接口到安全区域: {name}", "xml_template_integration.add_new_ip_set": "添加新的ip-set: {identifier}", "xml_template_integration.add_new_protocol_definition": "添加新的协议定义: {protocol}", "xml_template_integration.add_new_route_destination": "添加新路由目的网段: {destination}", "xml_template_integration.add_new_service_set": "添加新的service-set: {name}", "xml_template_integration.add_new_time_object": "添加新时间对象: {name}", "xml_template_integration.add_priority_to_existing_security_zone": "添加优先级到现有安全区域", "xml_template_integration.add_security_policy_to_vrf": "添加安全策略配置到VRF", "xml_template_integration.add_source_port_to_existing_protocol": "为现有{protocol}协议添加源端口: {port}", "xml_template_integration.add_static_route_to_existing_routing": "添加静态路由配置到现有routing", "xml_template_integration.add_static_route_to_vrf": "添加静态路由配置到VRF", "xml_template_integration.add_time_object_container_to_vrf": "添加时间对象容器到VRF", "xml_template_integration.added_ip_address": "➕ DEBUG: 已添加IP地址: {ip}", "xml_template_integration.added_new_download_bandwidth": "添加新的下行带宽配置", "xml_template_integration.added_new_upload_bandwidth": "添加新的上行带宽配置", "xml_template_integration.address_group_integration_complete": "地址对象组集成完成，集成了 {count} 个地址组", "xml_template_integration.address_group_integration_data": "地址对象组集成数据: 组数量={group_count}, 有XML片段={has_fragment}", "xml_template_integration.address_group_parse_failed": "地址对象组XML片段解析失败: {error}", "xml_template_integration.address_group_parse_success": "地址对象组XML片段解析成功", "xml_template_integration.address_object_integration_complete": "地址对象集成完成，集成了 {count} 个地址对象", "xml_template_integration.address_object_parse_failed": "地址对象XML片段解析失败: {error}", "xml_template_integration.address_object_parse_success": "地址对象XML片段解析成功", "xml_template_integration.cannot_find_or_create_vrf_skip_filtering": "无法找到或创建VRF节点，跳过模板接口过滤", "xml_template_integration.cannot_get_vrf_for_dns": "无法获取VRF元素，无法集成DNS配置", "xml_template_integration.cannot_get_vrf_for_nat": "无法获取VRF元素，无法集成NAT规则", "xml_template_integration.cannot_get_vrf_for_security": "无法获取VRF元素，无法集成安全策略", "xml_template_integration.cannot_get_vrf_for_static_routes": "无法获取VRF元素，无法集成静态路由", "xml_template_integration.cannot_get_vrf_for_time_objects": "无法获取VRF元素，无法集成时间对象", "xml_template_integration.collected_existing_physical_interfaces": "DEBUG: 收集到 {count} 个现有物理接口", "xml_template_integration.delete_existing_access_control": "DEBUG: 删除现有访问控制配置", "xml_template_integration.delete_existing_direct_ipv4": "🗑️ DEBUG: 删除现有的直接IPv4配置", "xml_template_integration.delete_existing_field": "DEBUG: 删除现有字段: {field} = {value}", "xml_template_integration.detailed_error_info": "详细错误信息: {error}", "xml_template_integration.dns_integration_complete": "DNS配置集成完成", "xml_template_integration.dns_integration_complete_detailed": "DNS配置集成完成: DNS服务器 {dns_servers} 个，静态域名 {static_hosts} 个", "xml_template_integration.dns_parse_failed": "DNS配置XML片段解析失败: {error}", "xml_template_integration.dns_parse_success": "DNS配置XML片段解析成功", "xml_template_integration.duplicate_container_nodes_found": "发现 {count} 个重复的 {type} 节点，保留第一个", "xml_template_integration.existing_address_search_result": "🔍 DEBUG: 现有地址元素查找结果: {result}", "xml_template_integration.existing_ip_search_result": "🔍 DEBUG: 现有IP元素查找结果: {result}", "xml_template_integration.existing_ipv4_direct_result": "🔍 DEBUG: 现有IPv4直接配置查找结果: {result}", "xml_template_integration.field_verification_failed": "DEBUG: 字段验证失败: {field} 未找到", "xml_template_integration.field_verification_success": "DEBUG: 字段验证成功: {field} = {value}", "xml_template_integration.final_xml_generation": "生成最终XML配置", "xml_template_integration.final_xml_generation_failed": "生成最终XML配置失败: {error}", "xml_template_integration.find_existing_interface_node_result": "DEBUG: 查找现有interface节点结果: {found}", "xml_template_integration.find_ipv4_address_config_start": "🔍 DEBUG: 开始查找IPv4地址配置", "xml_template_integration.found_address_config_method1": "🔍 DEBUG: 方法1找到地址配置", "xml_template_integration.found_address_config_method2": "🔍 DEBUG: 方法2找到地址配置", "xml_template_integration.found_address_config_method3": "🔍 DEBUG: 方法3找到地址配置", "xml_template_integration.found_direct_ipv4_config_method1": "🔍 DEBUG: 方法1找到直接IPv4配置", "xml_template_integration.found_direct_ipv4_config_method2": "🔍 DEBUG: 方法2找到直接IPv4配置", "xml_template_integration.found_direct_ipv4_config_method3": "🔍 DEBUG: 方法3找到直接IPv4配置", "xml_template_integration.found_existing_dns_start_merge": "找到现有的DNS节点，开始合并", "xml_template_integration.found_existing_interface_node_start_merge": "找到现有的interface节点，开始合并", "xml_template_integration.found_existing_ipv4_modify_strategy": "🔄 DEBUG: 发现现有IPv4配置，执行修改策略", "xml_template_integration.found_existing_network_obj_start_merge": "找到现有的network-obj节点，开始合并", "xml_template_integration.found_existing_network_obj_start_merge_groups": "找到现有的network-obj节点，开始合并地址组", "xml_template_integration.found_existing_physical_interface": "DEBUG: 找到现有物理接口: {name}", "xml_template_integration.found_existing_security_zone_merge": "找到现有security-zone节点，开始合并", "xml_template_integration.found_existing_security_zone_start_merge": "找到现有的security-zone节点，开始合并", "xml_template_integration.found_existing_service_group_start_merge": "找到现有的service-group节点，开始合并", "xml_template_integration.found_existing_service_obj_merge_groups": "发现已存在的service-obj节点，直接合并服务组内容", "xml_template_integration.found_existing_service_obj_start_merge": "找到现有的service-obj节点，开始合并", "xml_template_integration.found_existing_service_obj_start_merge_objects": "找到现有的service-obj节点，开始合并服务对象", "xml_template_integration.found_existing_static_route_start_merge": "找到现有的static-route节点，开始合并", "xml_template_integration.found_existing_time_obj_start_merge": "找到现有的time-obj节点，开始合并", "xml_template_integration.found_existing_time_range_container": "发现现有时间对象容器，将合并新的时间对象", "xml_template_integration.found_interface_node_details": "DEBUG: 找到interface节点，标签: {tag}, 命名空间: {xmlns}", "xml_template_integration.found_interface_without_name": "DEBUG: 发现没有名称的接口，直接追加", "xml_template_integration.found_new_direct_ipv4_config": "🔍 DEBUG: 找到新的直接IPv4配置", "xml_template_integration.found_new_download_bandwidth": "找到新的下行带宽配置", "xml_template_integration.found_new_field": "DEBUG: 找到新字段: {field} = {value}", "xml_template_integration.found_new_ipv4_address_config": "✅ DEBUG: 找到新IPv4地址配置，开始检查-修改-创建", "xml_template_integration.found_new_upload_bandwidth": "找到新的上行带宽配置", "xml_template_integration.found_security_zone_container_method1": "方法1找到security-zone容器", "xml_template_integration.found_security_zone_container_method2": "方法2找到security-zone容器", "xml_template_integration.found_security_zone_container_method3": "方法3找到security-zone容器", "xml_template_integration.found_security_zone_container_method4": "方法4找到security-zone容器", "xml_template_integration.found_security_zone_container_method5": "方法5找到security-zone容器", "xml_template_integration.found_service_obj_container_method1": "找到service-obj容器（方法1：直接查找）", "xml_template_integration.found_service_obj_container_method2": "找到service-obj容器（方法2：命名空间查找）", "xml_template_integration.found_service_obj_container_method3": "找到service-obj容器（方法3：遍历查找）", "xml_template_integration.found_vrf_element": "找到VRF元素，XML结构基本正确", "xml_template_integration.fragment_interface_names": "DEBUG: XML片段中的接口名称: {names}", "xml_template_integration.fragment_physical_interfaces_count": "DEBUG: XML片段中物理接口数量: {count}", "xml_template_integration.insert_new_address_group": "插入新地址组: {name}", "xml_template_integration.insert_new_address_object": "插入新地址对象: {name}", "xml_template_integration.insert_new_dns_node": "插入新的DNS节点", "xml_template_integration.insert_new_interface_node": "插入新的interface节点，包含 {count} 个接口", "xml_template_integration.insert_new_network_obj_node": "插入新的network-obj节点，包含 {count} 个地址对象", "xml_template_integration.insert_new_network_obj_node_groups": "插入新的network-obj节点，包含 {count} 个地址组", "xml_template_integration.insert_new_physical_interface": "插入新物理接口: {name}", "xml_template_integration.insert_new_security_zone": "插入新区域: {name}", "xml_template_integration.insert_new_security_zone_node": "插入新的security-zone节点，包含 {count} 个安全区域", "xml_template_integration.insert_new_security_zone_node_zones": "插入新的security-zone节点，包含{count}个区域", "xml_template_integration.insert_new_service_group": "插入新服务组: {name}", "xml_template_integration.insert_new_service_group_node": "插入新的service-group节点，包含 {count} 个服务组", "xml_template_integration.insert_new_service_obj_node": "插入新的service-obj节点，包含 {count} 个服务对象", "xml_template_integration.insert_new_service_obj_node_groups": "插入新的service-obj节点，包含 {count} 个服务组", "xml_template_integration.insert_new_service_object": "插入新服务对象: {name}", "xml_template_integration.insert_new_static_route": "插入新静态路由: {destination}", "xml_template_integration.insert_new_static_route_node": "插入新的static-route节点，包含 {count} 条静态路由", "xml_template_integration.insert_new_time_obj_node": "插入新的time-obj节点，包含 {count} 个时间对象", "xml_template_integration.insert_new_time_object": "插入新时间对象: {name}", "xml_template_integration.interface_fragment_content": "DEBUG: XML片段内容: {content}", "xml_template_integration.interface_fragment_integration_exception": "接口XML片段集成异常: {error}", "xml_template_integration.interface_fragment_parse_failed": "接口XML片段解析失败: {error}", "xml_template_integration.interface_fragment_parse_success": "接口XML片段解析成功", "xml_template_integration.interface_integration_data": "接口集成数据: 接口数量={interface_count}, 区域数量={zone_count}, 有XML片段={has_fragment}", "xml_template_integration.interface_mode_fix_error": "修复接口工作模式时出错: {error}", "xml_template_integration.interface_mode_fix_statistics_after": "修复后统计: Bridge模式={bridge}, Route模式={route}", "xml_template_integration.interface_mode_fix_statistics_before": "修复前统计: Bridge模式={bridge}, Route模式={route}", "xml_template_integration.interface_mode_fix_success": "成功修复了 {count} 个接口的工作模式", "xml_template_integration.interface_processing_generated_interfaces": "接口处理阶段已生成的接口: {interfaces}", "xml_template_integration.ipv": "[待翻译] xml_template_integration.ipv", "xml_template_integration.ipv4_config_processing_complete": "✅ DEBUG: IPv4配置处理完成，IP地址: {ip}", "xml_template_integration.keep_template_interface": "保留模板接口: {interface} (未在接口处理阶段生成)", "xml_template_integration.merge_address_group_failed": "合并address-group失败: {error}", "xml_template_integration.merge_address_set_failed": "ERROR: 合并address-set失败: {error}", "xml_template_integration.merge_complete_integrated_interfaces": "合并完成，共集成 {count} 个接口", "xml_template_integration.merge_container_content_failed": "合并容器内容失败: {type}, 错误: {error}", "xml_template_integration.merge_dns_client_config": "合并DNS客户端配置", "xml_template_integration.merge_existing_nat": "合并现有NAT配置", "xml_template_integration.merge_existing_security_policy": "合并现有安全策略配置", "xml_template_integration.merge_existing_static_route_config": "合并现有静态路由配置", "xml_template_integration.merge_protocol_ports_failed": "合并{protocol}协议端口失败: {error}", "xml_template_integration.merge_route_destination": "合并路由目的网段: {destination}", "xml_template_integration.merge_security_zone": "合并安全区域: {name}", "xml_template_integration.merge_security_zone_failed": "合并安全区域失败: {error}", "xml_template_integration.merge_service_group": "合并服务组: {name}", "xml_template_integration.merge_service_group_failed": "合并service-group失败: {error}", "xml_template_integration.merge_service_object": "合并服务对象: {name}", "xml_template_integration.merge_service_set_failed": "合并service-set失败: {error}", "xml_template_integration.merge_time_range": "合并时间对象: {name}", "xml_template_integration.method1_failed": "方法1失败: {error}", "xml_template_integration.method2_failed": "方法2失败: {error}", "xml_template_integration.method3_failed": "方法3失败: {error}", "xml_template_integration.method4_failed": "方法4失败: {error}", "xml_template_integration.method5_failed": "方法5失败: {error}", "xml_template_integration.modify_existing_ipv4_config_failed": "❌ 修改现有IPv4配置失败: {error}", "xml_template_integration.modify_existing_ipv4_config_start": "🔧 DEBUG: 开始修改现有IPv4配置", "xml_template_integration.nat_integration_complete": "NAT规则集成完成: {count} 个规则", "xml_template_integration.nat_parse_failed": "NAT XML片段解析失败: {error}", "xml_template_integration.duplicate_pool_skipped": "跳过重复的NAT池: {name}", "xml_template_integration.duplicate_rule_skipped": "跳过重复的NAT规则: {name}", "xml_template_integration.nat_merge_summary": "NAT合并完成 - 添加池:{added_pools}, 跳过池:{skipped_pools}, 添加规则:{added_rules}, 跳过规则:{skipped_rules}", "xml_template_integration.nat_merge_error": "NAT合并出错: {error}", "xml_template_integration.skip_invalid_element_in_security_zone": "跳过security-zone中的无效元素: {element}", "xml_template_integration.skip_threat_intelligence_element_in_security_zone": "跳过security-zone中的threat-intelligence元素: {element}", "xml_template_integration.skip_auto_element_wrong_namespace": "跳过错误命名空间的auto元素: {namespace}", "xml_template_integration.element_skip_check_failed": "元素跳过检查失败: {error}", "xml_template_integration.namespace_conflict_warning": "命名空间冲突警告: 已存在 {existing}, 新的 {new}", "xml_template_integration.checking_threat_intelligence_integrity": "检查threat-intelligence结构完整性", "xml_template_integration.threat_intelligence_not_found": "未找到threat-intelligence元素", "xml_template_integration.threat_intelligence_missing_management": "threat-intelligence缺少management子元素", "xml_template_integration.threat_intelligence_missing_security_zone": "threat-intelligence缺少security-zone子元素", "xml_template_integration.threat_intelligence_missing_auto": "threat-intelligence缺少auto子元素", "xml_template_integration.threat_intelligence_auto_repaired": "threat-intelligence auto元素已修复", "xml_template_integration.threat_intelligence_integrity_verified": "threat-intelligence结构完整性验证通过", "xml_template_integration.threat_intelligence_integrity_check_failed": "threat-intelligence完整性检查失败: {error}", "xml_template_integration.repairing_threat_intelligence_structure": "正在修复threat-intelligence结构", "xml_template_integration.threat_intelligence_structure_repaired": "threat-intelligence结构已修复", "xml_template_integration.threat_intelligence_repair_failed": "threat-intelligence结构修复失败: {error}", "xml_template_integration.threat_intelligence_integrity_failed": "threat-intelligence结构完整性检查失败", "xml_template_integration.new_address_result": "🔍 DEBUG: 新地址配置查找结果: {result}", "xml_template_integration.new_field_not_exist": "DEBUG: 新字段不存在: {field}", "xml_template_integration.new_interface_no_direct_ipv4": "ℹ️ DEBUG: 新接口没有直接IPv4配置", "xml_template_integration.new_ip_search_result": "🔍 DEBUG: 新IP元素查找结果: {result}", "xml_template_integration.new_ipv": "[待翻译] xml_template_integration.new_ipv", "xml_template_integration.new_ipv4_direct_result": "🔍 DEBUG: 新IPv4直接配置查找结果: {result}", "xml_template_integration.new_ipv4_no_address_info_skip_modify": "⚠️ DEBUG: 新IPv4配置没有地址信息，跳过修改", "xml_template_integration.new_ipv4_no_address_skip": "⚠️ DEBUG: 新IPv4配置没有IP地址，跳过更新", "xml_template_integration.no_address_config_found": "❌ DEBUG: 未找到地址配置", "xml_template_integration.no_address_group_fragment": "没有地址对象组XML片段", "xml_template_integration.no_address_group_result": "没有地址对象组处理结果", "xml_template_integration.no_address_object_result": "没有地址对象处理结果", "xml_template_integration.no_converted_interfaces_skip_filtering": "没有转换的接口，跳过模板接口过滤", "xml_template_integration.no_direct_ipv4_config_found": "❌ DEBUG: 未找到直接IPv4配置", "xml_template_integration.no_dns_fragment": "没有DNS XML片段需要集成", "xml_template_integration.no_dns_result": "没有DNS处理结果", "xml_template_integration.no_existing_ipv4_create_strategy": "➕ DEBUG: 未发现现有IPv4配置，执行创建策略", "xml_template_integration.no_interface_node_in_template": "模板中没有找到接口节点，跳过过滤", "xml_template_integration.no_interface_result_skip_filtering": "没有接口处理结果，跳过模板接口过滤", "xml_template_integration.no_nat_fragment": "没有NAT XML片段需要集成", "xml_template_integration.no_policy_result": "没有策略处理结果", "xml_template_integration.no_security_policy_fragment": "没有安全策略XML片段需要集成", "xml_template_integration.no_security_zone_fragment": "没有安全区域XML片段", "xml_template_integration.no_service_group_fragment": "没有服务对象组XML片段", "xml_template_integration.no_service_group_result": "没有服务对象组处理结果", "xml_template_integration.no_service_object_fragment": "没有服务对象XML片段", "xml_template_integration.no_service_object_result": "没有服务对象处理结果", "xml_template_integration.no_static_route_fragment": "没有静态路由XML片段需要集成", "xml_template_integration.no_static_route_result": "没有静态路由处理结果", "xml_template_integration.no_time_object_fragment": "没有时间对象XML片段需要集成", "xml_template_integration.no_time_object_result": "没有时间对象处理结果", "xml_template_integration.no_zone_result": "没有区域处理结果", "xml_template_integration.physical_interface_update_complete": "DEBUG: 物理接口节点更新完成: {name}", "xml_template_integration.processing_complete": "XML模板集成处理完成", "xml_template_integration.processing_failed": "XML模板集成处理失败: {error}", "xml_template_integration.processing_interface_fragment": "DEBUG: 处理接口片段: {name}", "xml_template_integration.remove_duplicate_container_node": "移除重复的 {type} 节点", "xml_template_integration.remove_duplicate_interface_from_template": "从模板中移除重复接口: {interface}", "xml_template_integration.removed_existing_address": "🗑️ DEBUG: 已移除现有地址: {ip}", "xml_template_integration.removed_existing_download_bandwidth": "删除现有的下行带宽配置", "xml_template_integration.removed_existing_upload_bandwidth": "删除现有的上行带宽配置", "xml_template_integration.removed_template_flags": "🗑️ DEBUG: 已移除模板flags字段", "xml_template_integration.replace_existing_dns_proxy": "替换现有DNS代理配置", "xml_template_integration.replace_existing_time_object": "替换现有时间对象: {name}", "xml_template_integration.replace_time_range": "替换时间对象: {name}", "xml_template_integration.security_policy_integration_complete": "安全策略集成完成: {count} 个策略", "xml_template_integration.security_policy_parse_failed": "安全策略XML片段解析失败: {error}", "xml_template_integration.security_zone_container_not_found": "未找到security-zone容器", "xml_template_integration.security_zone_integration_data": "安全区域集成数据: 区域数量={zone_count}, 有XML片段={has_fragment}", "xml_template_integration.service_group_integration_complete": "服务对象组集成完成，集成了 {count} 个服务组", "xml_template_integration.service_group_integration_data": "服务对象组集成数据: 组数量={group_count}, 有XML片段={has_fragment}", "xml_template_integration.service_group_parse_failed": "服务对象组XML片段解析失败: {error}", "xml_template_integration.service_group_parse_success": "服务对象组XML片段解析成功", "xml_template_integration.service_obj_container_not_found": "未找到service-obj容器", "xml_template_integration.service_object_integration_complete": "服务对象集成完成，集成了 {count} 个服务对象", "xml_template_integration.service_object_integration_data": "服务对象集成数据: 对象数量={object_count}, 有XML片段={has_fragment}", "xml_template_integration.service_object_parse_failed": "服务对象XML片段解析失败: {error}", "xml_template_integration.service_object_parse_success": "服务对象XML片段解析成功", "xml_template_integration.skip_template_interface_filtering": "跳过模板接口过滤，保留已更新的接口", "xml_template_integration.skip_vlan_interface": "跳过VLAN接口 {interface}，由接口处理阶段的XML片段处理", "xml_template_integration.start_fixing_interface_work_mode": "开始修复所有接口的工作模式为route模式", "xml_template_integration.start_integrating_address_groups": "开始集成地址对象组", "xml_template_integration.start_integrating_address_objects": "开始集成地址对象", "xml_template_integration.start_integrating_dns_config": "开始集成DNS配置", "xml_template_integration.start_integrating_interface_fragment": "DEBUG: 开始集成接口XML片段", "xml_template_integration.start_integrating_security_zones": "开始集成安全区域", "xml_template_integration.start_integrating_service_groups": "开始集成服务对象组", "xml_template_integration.start_integrating_service_objects": "开始集成服务对象", "xml_template_integration.start_integrating_static_routes": "开始集成静态路由", "xml_template_integration.start_integrating_time_objects": "开始集成时间对象", "xml_template_integration.start_loading_template": "开始加载XML模板: model={model}, version={version}", "xml_template_integration.start_security_zone_search": "开始查找security-zone容器", "xml_template_integration.start_template_interface_conflict_detection": "开始模板接口冲突检测和过滤", "xml_template_integration.start_updating_complex_fields": "DEBUG: 开始更新复杂字段: {name}", "xml_template_integration.start_updating_existing_interface": "DEBUG: 开始更新现有接口: {name}", "xml_template_integration.start_vrf_lookup": "开始VRF查找: 根元素={root}, 命名空间={xmlns}", "xml_template_integration.static_route_integration_complete": "静态路由集成完成，集成了 {count} 条静态路由", "xml_template_integration.static_route_integration_complete_detailed": "静态路由集成完成: {routes} 个路由，{groups} 个路由组", "xml_template_integration.static_route_parse_failed": "静态路由XML片段解析失败: {error}", "xml_template_integration.static_route_parse_success": "静态路由XML片段解析成功", "xml_template_integration.template_child_count": "子元素数量: {count}", "xml_template_integration.template_child_elements": "模板子元素: {elements}", "xml_template_integration.template_existing_physical_interfaces_count": "DEBUG: 模板中现有物理接口数量: {count}", "xml_template_integration.template_flags_field_updated": "🔄 DEBUG: 模板flags字段已更新: {flags}", "xml_template_integration.template_found_physical_interfaces": "模板中找到 {count} 个物理接口", "xml_template_integration.template_interface_conflict_filtering_complete": "模板接口冲突过滤完成，移除了 {count} 个重复接口", "xml_template_integration.template_interface_filtering_failed": "模板接口过滤失败: {error}", "xml_template_integration.template_ip_field_updated": "🔄 DEBUG: 模板IP字段已更新: {old_ip} -> {new_ip}", "xml_template_integration.template_loaded_success": "XML模板加载成功: model={model}, version={version}", "xml_template_integration.template_namespace": "命名空间: '{namespace}'", "xml_template_integration.template_namespace_alt": "命名空间: '{namespace}'", "xml_template_integration.template_root_element": "根元素: 完整='{full}', 本地='{local}'", "xml_template_integration.template_root_element_alt": "根元素: 完整='{full}', 本地='{local}'", "xml_template_integration.time_object_integration_complete": "时间对象集成完成，集成了 {count} 个时间对象", "xml_template_integration.time_object_integration_complete_count": "时间对象集成完成: {count} 个对象", "xml_template_integration.time_object_parse_failed": "时间对象XML片段解析失败: {error}", "xml_template_integration.time_object_parse_success": "时间对象XML片段解析成功", "xml_template_integration.time_range_merge_completed": "时间对象合并完成", "xml_template_integration.update_access_control_config": "DEBUG: 更新访问控制配置", "xml_template_integration.update_bandwidth_configuration_complete": "带宽配置更新完成", "xml_template_integration.update_bandwidth_configuration_failed": "带宽配置更新失败: {error}", "xml_template_integration.update_bandwidth_configuration_start": "开始更新带宽配置", "xml_template_integration.update_existing_address_config": "🔄 DEBUG: 更新现有地址配置", "xml_template_integration.update_existing_address_group": "更新现有地址组: {name}", "xml_template_integration.update_existing_address_object": "更新现有地址对象: {name}", "xml_template_integration.update_existing_physical_interface": "更新现有物理接口: {name}", "xml_template_integration.update_existing_security_zone": "更新现有区域: {name}", "xml_template_integration.update_existing_service_group": "更新现有服务组: {name}", "xml_template_integration.update_existing_service_object": "更新现有服务对象: {name}", "xml_template_integration.update_existing_static_route": "更新现有静态路由: {destination}", "xml_template_integration.update_existing_time_object": "更新现有时间对象: {name}", "xml_template_integration.update_field_complete": "DEBUG: 更新字段完成: {field} = {value}, 标签: {tag}", "xml_template_integration.update_ip_address": "🔄 DEBUG: 更新IP地址为: {ip}", "xml_template_integration.update_ipv4_config_failed": "❌ 更新IPv4配置失败: {error}", "xml_template_integration.update_ipv4_configuration_start": "🔧 DEBUG: 开始更新IPv4配置", "xml_template_integration.update_ipv4_field": "🔄 DEBUG: 更新IPv4字段 {field} = {value}", "xml_template_integration.update_physical_interface_failed": "更新物理接口节点失败: {error}", "xml_template_integration.vrf_child_element_lookup_failed": "VRF子元素查找失败: {error}", "xml_template_integration.vrf_child_element_lookup_no_result": "VRF子元素查找无结果", "xml_template_integration.vrf_child_element_lookup_success": "VRF子元素查找成功: 标签={tag}, 命名空间={xmlns}", "xml_template_integration.vrf_child_elements": "VRF子元素: {elements}", "xml_template_integration.vrf_creation_complete": "VRF节点创建完成", "xml_template_integration.vrf_direct_lookup_failed": "VRF直接查找失败: {error}", "xml_template_integration.vrf_direct_lookup_no_result": "VRF直接查找无结果", "xml_template_integration.vrf_direct_lookup_success": "VRF直接查找成功: 标签={tag}, 命名空间={xmlns}", "xml_template_integration.vrf_element_info": "VRF元素信息: 标签={tag}, 子元素数={children}", "xml_template_integration.vrf_full_uri_lookup_failed": "VRF完整URI查找失败: {error}", "xml_template_integration.vrf_full_uri_lookup_no_result": "VRF完整URI查找无结果", "xml_template_integration.vrf_full_uri_lookup_success": "VRF完整URI查找成功: 标签={tag}, 命名空间={xmlns}", "xml_template_integration.vrf_local_name_lookup_failed": "VRF local-name查找失败: {error}", "xml_template_integration.vrf_local_name_lookup_no_result": "VRF local-name查找无结果", "xml_template_integration.vrf_local_name_lookup_success": "VRF local-name查找成功: 标签={tag}, 命名空间={xmlns}", "xml_template_integration.vrf_lookup_success": "VRF节点查找成功", "xml_template_integration.vrf_namespace_lookup_failed": "VRF命名空间查找失败: {error}", "xml_template_integration.vrf_namespace_lookup_no_result": "VRF命名空间查找无结果", "xml_template_integration.vrf_namespace_lookup_success": "VRF命名空间查找成功: 标签={tag}, 命名空间={xmlns}", "xml_template_integration.vrf_not_found_creating": "未找到VRF节点，正在创建", "xml_template_integration.xml_integration_complete": "XML模板集成完成", "xml_template_integration.xml_integration_failed": "XML模板集成失败: {error}", "xml_template_integration.xml_namespace_validation": "XML命名空间验证: 实际='{actual}', 期望='{expected}'", "xml_template_integration.xml_namespace_validation_alt": "XML命名空间验证: 实际='{actual}', 期望='{expected}'", "xml_template_integration.xml_namespace_validation_passed": "XML命名空间验证通过", "xml_template_integration.xml_optimization_complete": "XML优化完成", "xml_template_integration.xml_optimization_failed": "XML优化失败: {error}", "xml_template_integration.xml_optimization_removed_duplicates": "XML优化：移除了 {count} 个重复的容器节点", "xml_template_integration.xml_root_validation_passed": "XML根元素验证通过: '{local}'", "xml_template_integration.xml_root_validation_passed_alt": "XML根元素验证通过: '{local}'", "xml_template_integration.xml_structure_validation": "XML结构验证: 完整标签='{full}', 本地标签='{local}'", "xml_template_integration.xml_structure_validation_alt": "XML结构验证: 完整标签='{full}', 本地标签='{local}'", "xml_template_integration.xml_structure_validation_complete": "XML结构验证完成", "xml_template_integration.xml_validation_complete": "XML验证完成", "xml_template_integration.xml_validation_failed": "XML验证失败: {error}", "xml_template_integration.zone_integration_complete": "区域集成完成，集成了 {count} 个安全区域", "xml_template_integration.zone_parse_failed": "区域XML片段解析失败: {error}", "xml_template_integration.zone_parse_success": "区域XML片段解析成功", "xml_template_integration_stage.address_group_created": "创建新的address-group: {name}", "xml_template_integration_stage.address_group_found": "找到现有address-group: {name}", "xml_template_integration_stage.address_group_integrated": "地址组已集成: {name}", "xml_template_integration_stage.address_group_integration_failed": "地址组集成失败: {name}, 错误: {error}", "xml_template_integration_stage.address_group_invalid_name": "地址组名称无效: 原始={original}, 清理后={cleaned}, 错误={error}", "xml_template_integration_stage.address_group_member_invalid_name": "地址组成员名称无效: 组={group}, 原始={original}, 清理后={cleaned}, 错误={error}", "xml_template_integration_stage.address_group_member_name_cleaned": "地址组成员名称已清理: 组={group}, 原始={original} -> 清理后={cleaned}", "xml_template_integration_stage.address_group_name_cleaned": "地址组名称已清理: 原始={original} -> 清理后={cleaned}", "xml_template_integration_stage.address_integration_data": "地址集成数据: 对象数={objects_count}, 组数={groups_count}", "xml_template_integration_stage.address_integration_start": "开始地址对象集成", "xml_template_integration_stage.address_name_cleaned": "地址对象名称已清理: 原始={original} -> 清理后={cleaned}", "xml_template_integration_stage.address_object_integrated": "地址对象已集成: {name}", "xml_template_integration_stage.address_object_integrated_failed": "地址对象集成失败: {name}", "xml_template_integration_stage.address_object_integrated_success": "地址对象集成成功: {name}", "xml_template_integration_stage.address_object_integration_failed": "地址对象集成失败: {name}, 错误: {error}", "xml_template_integration_stage.address_object_invalid_ip": "地址对象IP地址无效，跳过: {name}", "xml_template_integration_stage.address_object_invalid_name": "地址对象名称无效: 原始={original}, 清理后={cleaned}, 错误={error}", "xml_template_integration_stage.address_objects_integrated": "地址对象集成完成，对象数: {object_count}，组数: {group_count}", "xml_template_integration_stage.address_objects_integration_exception": "地址对象集成异常: {error}", "xml_template_integration_stage.address_set_created": "创建新的address-set: {name}", "xml_template_integration_stage.address_set_found": "找到现有address-set: {name}", "xml_template_integration_stage.backup_xml_saved": "[待翻译] xml_template_integration_stage.backup_xml_saved", "xml_template_integration_stage.bridge_clear_before_rebuild_failed": "重建前清空bridge失败", "xml_template_integration_stage.bridge_configuration_integrated": "桥接配置集成完成", "xml_template_integration_stage.bridge_configuration_integration_error": "桥接配置集成错误: {error}", "xml_template_integration_stage.bridge_configuration_integration_failed": "桥接配置集成失败", "xml_template_integration_stage.bridge_link_interface_clear_error": "bridge link-interface清空错误: {error}", "xml_template_integration_stage.bridge_link_interface_processing_error": "bridge link-interface处理错误: {error}", "xml_template_integration_stage.bridge_link_interface_rebuild_error": "bridge link-interface重建错误: {error}", "xml_template_integration_stage.bridge_link_interfaces_cleared": "bridge link-interface已清空，数量: {count}", "xml_template_integration_stage.bridge_link_interfaces_rebuilt": "bridge link-interface已重建，数量: {count}", "xml_template_integration_stage.bridge_node_processing_failed": "bridge节点处理失败", "xml_template_integration_stage.bridge_node_search_error": "bridge节点查找错误: {error}", "xml_template_integration_stage.bridge_nodes_found": "bridge节点查找完成，数量: {count}", "xml_template_integration_stage.bridge_slave_interface_add_error": "bridge slave接口添加错误: {interface}, {error}", "xml_template_integration_stage.bridge_slave_interface_added": "bridge slave接口已添加: {interface}", "xml_template_integration_stage.debug_address_data": "地址数据: 对象数={objects_count}, 组数={groups_count}", "xml_template_integration_stage.debug_address_object": "地址对象{index}: {name} = {config}", "xml_template_integration_stage.debug_data_context_end": "完成调试DataContext数据", "xml_template_integration_stage.debug_data_context_start": "开始调试DataContext数据", "xml_template_integration_stage.debug_interface_data": "接口数据: 接口数={interfaces_count}", "xml_template_integration_stage.debug_no_address_data": "没有地址数据", "xml_template_integration_stage.debug_no_interface_data": "没有接口数据", "xml_template_integration_stage.debug_no_policy_data": "没有策略数据", "xml_template_integration_stage.debug_no_service_data": "没有服务数据", "xml_template_integration_stage.debug_policy": "策略{index}: {name} = {config}", "xml_template_integration_stage.debug_policy_data": "策略数据: 策略数={policies_count}", "xml_template_integration_stage.debug_service_data": "服务数据: 对象数={objects_count}, 组数={groups_count}", "xml_template_integration_stage.debug_service_object": "服务对象{index}: {name} = {config}", "xml_template_integration_stage.dns_configuration_integrated": "DNS配置集成完成，服务器数: {server_count}，主DNS: {primary_dns}", "xml_template_integration_stage.dns_configuration_integration_exception": "DNS配置集成异常: {error}", "xml_template_integration_stage.duplicate_nodes_found": "[待翻译] xml_template_integration_stage.duplicate_nodes_found", "xml_template_integration_stage.duplicate_nodes_warning": "XML模板集成阶段：重复节点警告", "xml_template_integration_stage.found_bridge_node": "找到bridge节点", "xml_template_integration_stage.found_existing_bridge_nodes": "找到现有bridge节点，数量: {count}", "xml_template_integration_stage.initialized": "XML模板集成阶段已初始化", "xml_template_integration_stage.integrating_address_object": "正在集成地址对象: {name}, 配置: {config}", "xml_template_integration_stage.integrating_address_objects": "正在集成地址对象，数量: {count}", "xml_template_integration_stage.integrating_security_policies": "正在集成安全策略，数量: {count}", "xml_template_integration_stage.integrating_security_policy": "正在集成安全策略: {name}, 配置: {config}", "xml_template_integration_stage.integrating_service_groups": "正在集成服务组，数量: {count}", "xml_template_integration_stage.integrating_service_object": "正在集成服务对象: {name}, 配置: {config}", "xml_template_integration_stage.integrating_service_objects": "正在集成服务对象，数量: {count}", "xml_template_integration_stage.interface_configuration_integrated": "接口配置集成完成，接口数: {interface_count}，区域数: {zone_count}", "xml_template_integration_stage.interface_configuration_integration_exception": "接口配置集成异常: {error}", "xml_template_integration_stage.interface_integrated": "接口已集成: {name}", "xml_template_integration_stage.interface_integration_failed": "接口集成失败: {name}, 错误: {error}", "xml_template_integration_stage.interface_working_mode_fix_failed": "接口工作模式修复失败", "xml_template_integration_stage.ip_set_created": "创建ip-set: {name}, IP: {ip}", "xml_template_integration_stage.missing_model_version": "xml template integration stage: missing model version", "xml_template_integration_stage.namespace_warning": "XML模板集成阶段：命名空间警告", "xml_template_integration_stage.nat_rule_integration_failed": "xml template integration stage: nat rule integration failed", "xml_template_integration_stage.nat_rules_integrated": "xml template integration stage: nat rules integrated", "xml_template_integration_stage.network_obj_created": "创建network-obj容器节点", "xml_template_integration_stage.network_obj_created_for_groups": "为地址组创建network-obj容器节点", "xml_template_integration_stage.network_obj_found": "找到现有network-obj容器节点", "xml_template_integration_stage.no_address_data_to_integrate": "没有地址对象数据需要集成", "xml_template_integration_stage.no_address_result": "没有地址对象处理结果", "xml_template_integration_stage.no_dns_result": "没有DNS处理结果", "xml_template_integration_stage.no_existing_bridge_nodes": "模板中没有现有的bridge节点", "xml_template_integration_stage.no_integration_data": "xml template integration stage: no integration data", "xml_template_integration_stage.no_interface_data_to_integrate": "没有接口数据需要集成", "xml_template_integration_stage.no_interface_result": "没有接口处理结果", "xml_template_integration_stage.no_nat_rules": "xml template integration stage: no nat rules", "xml_template_integration_stage.no_operation_mode_result": "没有操作模式处理结果", "xml_template_integration_stage.no_operation_mode_result_for_bridge": "桥接处理没有操作模式结果", "xml_template_integration_stage.no_security_policies": "xml template integration stage: no security policies", "xml_template_integration_stage.no_service_data_to_integrate": "没有服务数据需要集成", "xml_template_integration_stage.no_service_result": "没有服务对象处理结果", "xml_template_integration_stage.no_slave_interfaces_to_add": "没有slave接口需要添加", "xml_template_integration_stage.no_static_route_result": "没有静态路由处理结果", "xml_template_integration_stage.no_time_range_result": "没有时间对象处理结果", "xml_template_integration_stage.no_zone_result": "没有安全区域处理结果", "xml_template_integration_stage.original_xml_saved": "[待翻译] xml_template_integration_stage.original_xml_saved", "xml_template_integration_stage.processing_completed": "xml template integration stage: processing completed", "xml_template_integration_stage.root_element_valid": "XML模板集成阶段：根元素有效", "xml_template_integration_stage.root_element_warning": "XML模板集成阶段：根元素警告", "xml_template_integration_stage.security_policies_integrated": "xml template integration stage: security policies integrated", "xml_template_integration_stage.security_policy_container_created": "创建security-policy容器", "xml_template_integration_stage.security_policy_container_found": "找到现有security-policy容器", "xml_template_integration_stage.security_policy_integrated": "安全策略集成完成: {name}", "xml_template_integration_stage.security_policy_integrated_failed": "安全策略集成失败: {name}", "xml_template_integration_stage.security_policy_integrated_success": "安全策略集成成功: {name}", "xml_template_integration_stage.security_policy_integration_data": "安全策略集成数据: 策略数={policies_count}", "xml_template_integration_stage.security_policy_integration_failed": "xml template integration stage: security policy integration failed", "xml_template_integration_stage.security_policy_integration_failed_zh": "安全策略规则集成失败：{name} - {error}", "xml_template_integration_stage.security_policy_integration_start": "开始安全策略集成", "xml_template_integration_stage.security_policy_rule_created": "创建新的安全策略规则: {name}", "xml_template_integration_stage.security_policy_rule_found": "找到现有安全策略规则: {name}", "xml_template_integration_stage.security_zones_integrated": "安全区域集成完成，区域数: {zone_count}", "xml_template_integration_stage.security_zones_integration_exception": "安全区域集成异常: {error}", "xml_template_integration_stage.service_group_created": "创建新的service-group: {name}", "xml_template_integration_stage.service_group_found": "找到现有service-group: {name}", "xml_template_integration_stage.service_group_integrated": "服务组集成完成: {name}", "xml_template_integration_stage.service_group_integration_failed": "服务组集成失败: {name}, 错误: {error}", "xml_template_integration_stage.service_groups_integration_todo": "服务组XML集成逻辑待实现", "xml_template_integration_stage.service_integration_data": "服务集成数据: 对象数={objects_count}, 组数={groups_count}", "xml_template_integration_stage.service_integration_start": "开始服务对象集成", "xml_template_integration_stage.service_obj_created": "创建新的service-obj节点", "xml_template_integration_stage.service_obj_created_for_groups": "为服务组创建service-obj节点", "xml_template_integration_stage.service_obj_found": "找到现有service-obj节点", "xml_template_integration_stage.service_object_integrated": "服务对象集成完成: {name}", "xml_template_integration_stage.service_object_integrated_failed": "服务对象集成失败: {name}", "xml_template_integration_stage.service_object_integrated_success": "服务对象集成成功: {name}", "xml_template_integration_stage.service_object_integration_failed": "服务对象集成失败: {name}, 错误: {error}", "xml_template_integration_stage.service_objects_integrated": "服务对象集成完成，对象数: {object_count}，组数: {group_count}", "xml_template_integration_stage.service_objects_integration_exception": "服务对象集成异常: {error}", "xml_template_integration_stage.service_objects_integration_todo": "服务对象XML集成逻辑待实现", "xml_template_integration_stage.service_set_created": "创建新的service-set: {name}", "xml_template_integration_stage.service_set_found": "找到现有service-set: {name}", "xml_template_integration_stage.start_processing": "xml template integration stage: start processing", "xml_template_integration_stage.static_routes_integrated": "静态路由集成完成，路由数: {route_count}", "xml_template_integration_stage.static_routes_integration_exception": "静态路由集成异常: {error}", "xml_template_integration_stage.system_configuration_integrated": "系统配置集成完成，主机名: {hostname}，模式: {mode}", "xml_template_integration_stage.system_configuration_integration_exception": "系统配置集成异常: {error}", "xml_template_integration_stage.template_children": "模板子元素: {children}", "xml_template_integration_stage.template_loaded": "xml template integration stage: template loaded", "xml_template_integration_stage.time_ranges_integrated": "时间对象集成完成，时间范围数: {time_range_count}", "xml_template_integration_stage.time_ranges_integration_exception": "时间对象集成异常: {error}", "xml_template_integration_stage.unexpected_namespace": "[待翻译] xml_template_integration_stage.unexpected_namespace", "xml_template_integration_stage.unexpected_root_element": "XML模板集成阶段：意外的根元素", "xml_template_integration_stage.unknown_operation_mode": "未知操作模式: {mode}", "xml_template_integration_stage.vrf_created": "VRF节点已创建", "xml_template_integration_stage.vrf_found": "找到现有VRF节点", "xml_template_integration_stage.vrf_found_direct": "直接查找找到VRF: {vrf_tag}, 命名空间: {vrf_namespace}", "xml_template_integration_stage.vrf_found_with_full_uri": "找到VRF节点（使用完整URI）", "xml_template_integration_stage.vrf_found_with_local_name": "找到VRF节点（使用local-name）", "xml_template_integration_stage.vrf_found_with_namespace": "使用命名空间找到VRF: {vrf_tag}, 命名空间: {vrf_namespace}", "xml_template_integration_stage.vrf_found_without_namespace": "找到VRF节点（无命名空间）", "xml_template_integration_stage.vrf_not_found_creating": "未找到VRF节点，正在创建", "xml_template_integration_stage.vrf_search_method1_failed": "VRF查找方法1失败: {error}", "xml_template_integration_stage.vrf_search_method2_failed": "VRF查找方法2失败: {error}", "xml_template_integration_stage.vrf_search_method3_failed": "VRF查找方法3失败: {error}", "xml_template_integration_stage.vrf_search_method4_failed": "VRF查找方法4失败: {error}", "xml_template_integration_stage.vrf_search_start": "开始查找VRF节点，根元素: {root_tag}, 命名空间: {root_namespace}", "xml_template_integration_stage.xml_basic_generation_failed": "XML模板集成阶段：基本XML生成失败", "xml_template_integration_stage.xml_basic_generation_success": "XML模板集成阶段：基本XML生成成功", "xml_template_integration_stage.xml_fallback_generation_success": "XML模板集成阶段：回退XML生成成功", "xml_template_integration_stage.xml_formatted_generation_failed": "XML模板集成阶段：格式化XML生成失败", "xml_template_integration_stage.xml_formatted_generation_success": "XML模板集成阶段：格式化XML生成成功", "xml_template_integration_stage.xml_generated": "xml template integration stage: xml generated", "xml_template_integration_stage.xml_generation_debug": "XML模板集成阶段：XML生成调试信息", "xml_template_integration_stage.xml_generation_null_root": "XML模板集成阶段：根元素为空", "xml_template_integration_stage.xml_generation_starting": "XML模板集成阶段：开始生成XML", "xml_template_integration_stage.xml_generation_stats": "XML模板集成阶段：XML生成统计", "xml_template_integration_stage.xml_validated_optimized": "xml template integration stage: xml validated optimized", "xml_template_integration_stage.zone_integrated": "安全区域已集成: {name}", "xml_template_integration_stage.zone_integration_failed": "安全区域集成失败: {name}, 错误: {error}", "xml_utils.create_default_structure.success": "xml utils: create default structure: success", "xml_utils.creating_vrf_node": "xml utils: creating vrf node", "xml_utils.ensure_interface_specific_xmlns": "确保接口内元素 {element} 的命名空间: {namespace}", "xml_utils.ensure_vrf_specific_xmlns": "确保VRF内元素 {element} 的命名空间: {namespace}", "xml_utils.error.all_children_lost_using_backup": "xml utils: error: all children lost using backup", "xml_utils.error.cleanup_namespaces_failed": "清理命名空间失败: {error}", "xml_utils.fix_duplicate_xmlns": "修复重复xmlns，数量: {count}", "xml_utils.fix_root_namespace": "xml utils: fix root namespace", "xml_utils.n_converted_to_name": "xml utils: n converted to name", "xml_utils.name_element_added": "xml utils: name element added", "xml_utils.namespace_cleanup_success": "xml utils: namespace cleanup success", "xml_utils.namespace_cleanup_warning": "xml utils: namespace cleanup warning", "xml_utils.restore_root_namespace": "xml utils: restore root namespace", "xml_utils.start_namespace_cleanup": "开始命名空间清理，子元素数量: {child_count}", "xml_utils.warning.service_object_already_exists": "警告: 服务对象 '{name}' 已经存在，将使用现有对象", "xml_utils.warning.significant_children_loss_using_backup": "xml utils: warning: significant children loss using backup", "xml_utils.warning.zone_interface_not_found": "警告: 区域 '{zone}' 中的接口 '{interface}' 未找到", "xml_utils.warning.zone_not_found": "警告: 区域 '{zone}' 未找到", "yang.access_control_namespace_success": "access-control标签命名空间设置成功", "yang.adding_ipv6_config": "为VLAN子接口 {name} 添加IPv6配置", "yang.adding_static_ip": "添加静态IP配置: {ip}", "yang.assigning_pppoe_tunnel_id": "分配PPPoE隧道ID: {name} -> {id}", "yang.configuring_ipv6_dhcp": "为接口 {name} 配置IPv6 DHCP", "yang.configuring_pppoe": "为接口 {name} 配置PPPoE", "yang.configuring_static_ipv6": "为接口 {name} 配置静态IPv6: {address}", "yang.configuring_vlan_dhcp": "为VLAN子接口 {name} 配置DHCP", "yang.configuring_vlan_pppoe": "为VLAN子接口 {name} 配置PPPoE", "yang.configuring_vlan_static_ip": "为VLAN子接口 {name} 配置静态IP: {ip}", "yang.convert_ip_mask_to_cidr": "将IP和掩码 {ip} 转换为CIDR格式: {cidr}", "yang.create_interface_node": "YANG模型: create interface node", "yang.create_new_interface": "创建新接口配置 - 原始数据: {data}", "yang.create_routing_node": "创建新的routing节点", "yang.create_security_zone_node": "YANG模型: create security zone node", "yang.creating_nat_node": "正在创建NAT节点", "yang.creating_pppoe_config": "创建PPPoE配置 - 接口: {name}", "yang.creating_security_policy_node": "正在创建安全策略节点", "yang.detected_vlan_subinterface": "检测到VLAN子接口: {name}", "yang.enable_bfd": "启用BFD: {value}", "yang.enable_route": "启用路由: {value}", "yang.error.access_control_namespace_failed": "错误: access-control标签命名空间设置失败", "yang.error.access_control_namespace_failed_detail": "错误: access-control标签命名空间设置失败，当前命名空间: {namespace}", "yang.error.all_tunnel_ids_used": "错误: 所有PPPoE隧道ID都已分配，无法创建新的PPPoE连接", "yang.error.interface_in_zone_missing_required_name_field": "错误: 区域中的接口缺少必需的name字段", "yang.error.invalid_route_data_format": "路由数据格式不正确，无法创建静态路由: {data}", "yang.error.invalid_vlanid_format": "错误: 无效的VLAN ID格式 {id}", "yang.error.ipmac_namespace_failed": "错误: ip-mac-bind标签命名空间设置失败", "yang.error.ipmac_namespace_failed_detail": "错误: ip-mac-bind标签命名空间设置失败，当前命名空间: {namespace}", "yang.error.monitor_namespace_failed": "错误: monitor标签命名空间设置失败", "yang.error.static_route_creation_failed": "创建静态路由失败: {error}", "yang.error.update_vlan_missing_name": "错误: 更新VLAN子接口时缺少name字段", "yang.fix_node_name": "YANG模型: fix node name", "yang.fixing_interface_name": "修复接口名称: {original} -> {fixed}", "yang.fixing_interface_underscore": "修复接口名称中的下划线: {original} -> {fixed}", "yang.fixing_parent_interface": "修复子接口父接口名称: {original} -> {fixed}", "yang.fixing_subinterface_name": "修复子接口名称: {original} -> {fixed}", "yang.generating_xml": "使用YANG生成器生成XML配置", "yang.info.removed_interface_without_name": "已移除没有name字段的接口", "yang.interface_cleanup_complete": "接口名称清理完成，共处理 {count} 个接口", "yang.interface_missing_name": "接口缺少name字段，使用raw_name: {raw_name}", "yang.interface_mode_detection": "接口 {name} 模式检测: DHCP={dhcp}, PPPoE={pppoe}", "yang.interface_name_mapping": "接口名称映射: {raw_name} -> {name}", "yang.ipmac_namespace_success": "ip-mac-bind标签命名空间设置成功", "yang.ipv6_config_data": "IPv6配置数据: {data}", "yang.ipv6_dhcp_mode_detection": "接口 {name} 是否为IPv6 DHCP模式: {is_dhcp}", "yang.ipv6_mode_value": "ipv6 mode值: {mode}", "yang.mode_value": "mode值: {mode}", "yang.monitor_namespace_success": "monitor标签命名空间设置成功", "yang.nat_configuration_error": "NAT配置错误: {error}", "yang.nat_node_exists": "NAT节点已存在", "yang.policy_processing_error": "策略处理错误: {error}", "yang.policy_processing_stats": "策略处理统计: 总数={total}, 安全策略={security_policies}, NAT规则={nat_rules}", "yang.reset_tunnel_ids": "重置PPPoE隧道ID分配", "yang.routing_node_added": "已添加routing节点，XML: {xml}", "yang.security_policy_error": "安全策略错误: {error}", "yang.security_policy_node_exists": "安全策略节点已存在", "yang.set_admin_distance": "设置管理距离: {distance}", "yang.set_blackhole_route": "设置黑洞路由", "yang.set_gateway": "设置网关: {gateway}", "yang.set_next_hop": "设置下一跳: {hop}", "yang.set_route_destination": "设置路由目标网络: {destination}", "yang.start_sanitize_interface_names": "开始清理和规范化接口名称", "yang.start_xml_generation": "YANG模型: start xml generation", "yang.static_node_not_found": "未找到static节点，创建一个新的", "yang.static_route_created_success": "成功创建静态路由: {destination}", "yang.static_tag_added": "添加static标签到routing节点", "yang.template_loading_error": "模板加载错误: {error}", "yang.update_access_control_namespace": "更新access-control标签，使用命名空间: {namespace}", "yang.update_interface_config": "更新接口配置 - 原始数据: {data}", "yang.update_ipmac_namespace": "更新ip-mac-bind标签，使用命名空间: {namespace}", "yang.update_static_ip": "更新静态IP配置: {ip}", "yang.update_vlan_data": "更新VLAN子接口 - 原始数据: {data}", "yang.update_vlan_subinterface": "更新VLAN子接口配置: {name}", "yang.using_deprecated_function.create_static_route": "YANG模型: using deprecated function.create static route", "yang.using_template": "YANG模型: using template", "yang.vlan_update_complete": "VLAN更新完成: {name}", "yang.warning.cannot_map_interface_for_route": "无法映射接口 {device}，只设置网关: {gateway}", "yang.warning.cannot_parse_netmask": "警告: 无法解析子网掩码: {mask}", "yang.warning.interface_name_underscore": "警告: 接口名称 {name} 包含下划线，这在NTOS中是不允许的", "yang.warning.interface_no_name": "警告: 接口既没有name也没有raw_name字段，跳过", "yang.warning.ip_no_prefix": "警告: IP地址没有指定前缀长度，默认使用/24: {ip}", "yang.warning.route_missing_next_hop": "路由缺少下一跳信息", "yang.warning.vlanid_out_of_range": "警告: VLAN ID {id} 超出有效范围 (1-4063)，将限制在有效范围内", "yang.xml_generation_complete": "YANG模型: xml generation complete", "yang_extensions.advanced_xml_generation_complete": "yang extensions: advanced xml generation complete", "yang_extensions.error.interface_missing_name": "yang extensions: error: interface missing name", "yang_extensions.error.route_missing_destination": "yang extensions: error: route missing destination", "yang_extensions.error.route_missing_gateway": "yang extensions: error: route missing gateway", "yang_extensions.error.zone_missing_name": "yang extensions: error: zone missing name", "yang_extensions.start_advanced_xml_generation": "yang extensions: start advanced xml generation", "yang_loader.dir_not_found": "yang loader: dir not found", "yang_loader.found_yang_files": "找到YANG文件", "yang_loader.no_yang_files": "yang loader: no yang files", "yang_loader.parse_header_error": "yang loader: parse header error", "yang_loader.parse_structure_error": "yang loader: parse structure error", "yang_loader.schema_loaded": "模式已加载", "yang_manager.cache_cleared": "YANG管理器：缓存已清理", "yang_manager.initialized": "YANG管理器已初始化", "yang_manager.namespaces_load_failed": "YANG管理器：命名空间加载失败", "yang_manager.namespaces_loaded": "YANG管理器：命名空间已加载", "yang_manager.schema_cache_hit": "YANG管理器：模式缓存命中", "yang_manager.schema_load_failed": "YANG管理器：模式加载失败", "yang_manager.schema_loaded": "YANG管理器：模式已加载", "yang_parser.constraint_violation": "YANG解析器: constraint violation", "yang_parser.init": "初始化YANG解析器", "yang_parser.loading_model": "加载YANG模型", "yang_parser.model_loaded": "YANG模型已加载", "yang_parser.parse_header_error": "解析头部错误: {file}，错误: {error}", "yang_parser.parse_structure_error": "解析结构错误: {file}，错误: {error}", "yang_parser.parsing_complete": "YANG解析完成", "yang_parser.parsing_error": "YANG解析错误", "yang_parser.schema_exported": "YANG解析器: schema exported", "yang_parser.start_parsing": "开始解析YANG模型", "yang_parser.validation_error": "YANG验证错误", "yang_validation_stage.initialized": "YANG验证阶段已初始化", "yang_validation_stage.missing_model_version": "缺少模型或版本信息", "yang_validation_stage.no_generated_xml": "没有生成的XML配置", "yang_validation_stage.processing_completed": "YANG验证处理完成", "yang_validation_stage.processing_exception": "YANG验证处理异常: {error}", "yang_validation_stage.recommendation_constraint": "建议检查约束条件配置", "yang_validation_stage.recommendation_data_type": "建议检查数据类型格式", "yang_validation_stage.recommendation_general": "建议检查XML配置语法", "yang_validation_stage.recommendation_mandatory": "建议检查必填字段配置", "yang_validation_stage.recommendation_namespace": "建议检查命名空间配置", "yang_validation_stage.recommendation_structure": "建议检查XML结构配置", "yang_validation_stage.skipping_validation": "跳过YANG验证", "yang_validation_stage.start_processing": "开始YANG验证处理", "yang_validation_stage.temp_file_created": "临时验证文件已创建", "yang_validation_stage.temp_file_creation_failed": "临时验证文件创建失败: {error}", "yang_validation_stage.validation_executed": "YANG验证已执行", "yang_validation_stage.validation_execution_failed": "YANG验证执行失败: {error}", "yang_validation_stage.validation_failed": "YANG验证失败: {message}", "yang_validation_stage.validation_failed_warning": "YANG验证失败警告: {message}", "yang_validation_stage.validation_passed": "YANG验证通过", "yang_validation_stage.validation_report_failure": "YANG验证报告生成失败", "yang_validation_stage.validation_report_generated": "YANG验证报告已生成", "yang_validation_stage.validation_report_success": "YANG验证报告生成成功", "yang_validation_stage.yanglint_not_available": "yanglint工具不可用", "yang_validator.undefined_node": "YANG验证器发现未定义节点", "zone.unknown": "未知区域", "zone_priority_manager.allocated_custom_priority": "为区域 {zone} 分配自定义优先级: {priority} (安全级别: {level})", "zone_priority_manager.allocated_fallback_priority": "为区域 {zone} 分配回退优先级: {priority}", "zone_priority_manager.priority_exhausted": "区域 {zone} 优先级资源耗尽", "zone_priority_manager.using_predefined_priority": "区域 {zone} 使用预定义优先级: {priority}", "zone_processing_stage.completed": "安全区域处理完成，总区域: {total_zones}，创建区域: {created_zones}", "zone_processing_stage.description": "安全区域处理阶段", "zone_processing_stage.invalid_interface_zones_format": "接口区域数据格式无效，类型: {type}", "zone_processing_stage.no_interface_result": "没有接口处理结果", "zone_processing_stage.priority_validation_failed": "区域优先级验证失败，存在重复优先级", "zone_processing_stage.processing_failed": "安全区域处理失败: {error}", "zone_processing_stage.starting": "开始处理安全区域", "zone_processing_stage.zones_processed": "区域处理完成，总计: {total}，默认: {default}，自定义: {custom}", "zone_processor.creating_custom_zone": "创建自定义区域配置: {zone}, 优先级: {priority}", "zone_processor.custom_zone_desc": "自定义区域 {zone}", "zone_processor.detailed_error_info": "详细错误信息: {error}", "zone_processor.dmz_zone_desc": "DMZ区域", "zone_processor.duplicate_priority_detected": "检测到重复的区域优先级 {priority}: {zones}", "zone_processor.interface_mapping_empty": "接口映射为空，无法映射接口名称", "zone_processor.interface_mapping_warning": "⚠️ 接口映射警告: {message}", "zone_processor.interface_not_mapped": "接口 {interface} 未配置映射关系", "zone_processor.no_converted_zones": "没有转换成功的区域，返回空XML片段", "zone_processor.priority_validation_passed": "区域优先级验证通过，共 {count} 个区域", "zone_processor.required_zones": "需要创建的区域: {zones}", "zone_processor.security_warning": "⚠️ 安全警告: {message}", "zone_processor.trust_zone_desc": "信任区域", "zone_processor.untrust_zone_desc": "非信任区域", "zone_processor.zone_config_created": "区域配置创建完成: {zone} 包含 {count} 个接口", "zone_processor.zone_config_creation_complete": "区域 {zone} 配置创建完成: {config}", "zone_processor.zone_conversion_success": "区域转换成功: {zone} (优先级: {priority}, 接口: {interface_count}, {intrazone_info})", "zone_processor.zone_data_conversion_complete": "区域数据转换完成，原始类型: {original_type}, 转换后: {converted_type}, 区域数量: {count}", "zone_processor.zone_fortigate_interfaces": "区域 {zone} 的FortiGate接口: {interfaces}", "zone_processor.zone_interface_found": "找到属于区域 {zone} 的接口: {interface}", "zone_processor.zone_interface_mapping_skip": "区域 {zone}: 接口 {interface} 未配置映射关系，跳过", "zone_processor.zone_interface_mapping_start": "开始处理区域 {zone} 的接口区域映射", "zone_processor.zone_interface_mapping_success": "接口映射成功: {original} -> {mapped}", "zone_processor.zone_interfaces_before_merge": "区域 {zone} 合并前的接口列表: {interfaces}", "zone_processor.zone_interfaces_final": "区域 {zone} 最终接口列表: {interfaces}", "zone_processor.zone_interfaces_from_mapping": "区域 {zone} 从接口区域映射获得的接口: {interfaces}", "zone_processor.zone_interfaces_skipped": "区域 {zone}: {count} 个接口因未配置映射关系被跳过", "zone_processor.zone_mapped_interfaces": "区域 {zone} 映射后的接口: {interfaces}", "zone_processor.zone_no_interface_field": "区域 {zone} 的FortiGate配置中没有interface/interfaces字段", "zone_processor.zone_processing_complete": "区域处理完成: 转换成功 {success}/{total}", "zone_processor.zone_processing_complete_with_warnings": "区域处理完成: 转换成功 {success}/{total}, 警告 {warnings} 个 (安全: {security}, 接口映射: {interface})", "zone_processor.zone_skipped_no_interfaces": "区域的所有接口都未配置映射关系或区域没有配置接口", "zone_processor.zone_xml_fragment_success": "区域XML片段生成成功，包含 {count} 个区域", "zone_processor.zone_xml_generated": "生成区域XML: {zone} (优先级: {priority}, 接口: {count})", "zone_processor.zones_found": "找到 {count} 个区域配置，操作模式: {mode}", "refactored_wrapper.initialized": "重构包装器已初始化", "refactored_wrapper.execution_started": "重构包装器执行开始", "refactored_wrapper.context_validation_failed": "上下文验证失败", "refactored_wrapper.missing_required_data": "缺少必需数据: {key}", "refactored_wrapper.no_processing_results": "没有处理结果", "refactored_wrapper.compatibility_data_prepared": "兼容性数据已准备", "refactored_wrapper.default_template_provided": "提供默认模板", "refactored_wrapper.compatibility_data_preparation_failed": "兼容性数据准备失败: {error}", "refactored_wrapper.template_loading_failed": "XML模板加载失败", "refactored_wrapper.loading_template": "正在加载XML模板: model={model}, version={version}", "refactored_wrapper.template_loaded_success": "XML模板加载成功: model={model}, version={version}", "refactored_wrapper.no_template_provided": "未提供模板", "refactored_wrapper.executing_integrator": "执行集成器: {name}", "refactored_wrapper.integrator_validation_failed": "集成器验证失败: {name}", "refactored_wrapper.integrator_completed": "集成器完成: {name}", "refactored_wrapper.integrator_failed": "集成器失败: {name}", "refactored_wrapper.integration_completed": "集成完成，警告: {warnings}，错误: {errors}", "refactored_wrapper.output_generation_failed": "输出生成失败", "refactored_wrapper.direct_execution_failed": "直接执行失败: {error}", "refactored_wrapper.no_output_file_specified": "未指定输出文件", "refactored_wrapper.xml_file_generated": "XML文件已生成: {file}", "refactored_wrapper.xml_generation_failed": "XML生成失败: {error}", "refactored_wrapper.execution_completed": "重构包装器执行完成", "refactored_wrapper.execution_failed": "重构包装器执行失败", "refactored_wrapper.execution_exception": "重构包装器执行异常: {error}", "refactored_wrapper.output_compatibility_ensured": "输出兼容性已确保", "refactored_wrapper.no_xml_output_detected": "未检测到XML输出", "refactored_wrapper.output_compatibility_failed": "输出兼容性失败: {error}", "refactored_wrapper.performance_metrics_failed": "性能指标失败: {error}", "refactored_wrapper.interface_mapping_found": "找到接口映射数据，数量: {count}", "refactored_wrapper.no_interface_mapping": "未找到接口映射数据", "refactored_xml_integration.process_start": "重构版本XML集成开始", "refactored_xml_integration.template_load_failed": "重构版本模板加载失败", "refactored_xml_integration.integrator_not_found": "集成器未找到: {name}", "refactored_xml_integration.input_validation_failed": "输入验证失败: {name}", "refactored_xml_integration.executing_integrator": "执行集成器: {name}", "refactored_xml_integration.integrator_failed": "集成器失败: {name}, 错误数: {errors}", "refactored_xml_integration.integrator_error": "集成器错误 {name}: {error}", "refactored_xml_integration.integrator_warning": "集成器警告 {name}: {warning}", "refactored_xml_integration.optimization_start": "开始XML优化", "refactored_xml_integration.optimization_failed": "XML优化失败: {error}", "refactored_xml_integration.xml_generation_start": "开始生成最终XML", "refactored_xml_integration.xml_generation_failed": "XML生成失败", "refactored_xml_integration.process_completed": "重构版本XML集成完成，集成器数: {integrators}, 警告数: {warnings}, 错误数: {errors}", "template_loader.missing_model_version": "缺少模型或版本信息", "template_loader.using_cached_template": "使用缓存模板: model={model}, version={version}", "template_loader.template_not_found": "模板未找到: model={model}, version={version}", "template_loader.template_load_success": "模板加载成功: {path}", "template_loader.template_load_exception": "模板加载异常: path={path}, error={error}", "template_loader.searching_paths": "搜索模板路径，engine目录: {engine_dir}", "template_loader.checking_path": "检查路径 {index}: {path}, 存在: {exists}", "template_loader.template_validation_failed": "模板验证失败: {error}", "template_loader.template_cached": "模板已缓存: {cache_key}", "refactored_xml_integration.optimizing_xml": "开始优化XML结构", "refactored_xml_integration.xml_optimization_failed": "XML优化失败", "refactored_xml_integration.generating_final_xml": "开始生成最终XML", "refactored_xml_integration.process_exception": "重构版本XML集成异常: {error}", "twice_nat44_error_handler.initialized": "twice-nat44错误处理器已初始化，最大历史记录: {max_history}", "twice_nat44_error_handler.error_logged": "twice-nat44错误已记录: {error_id}, 类型: {error_type}, 严重程度: {severity}, 操作: {operation}, 策略: {policy_name}, 恢复尝试: {recovery_attempted}, 恢复成功: {recovery_successful}", "twice_nat44_error_handler.recovery_failed": "twice-nat44错误恢复失败: {error_id}, 恢复错误: {recovery_error}", "twice_nat44_error_handler.auto_recovery_success": "twice-nat44自动恢复成功: {error_id}, 操作: {operation}", "twice_nat44_error.impact.config_error": "配置错误将导致规则创建失败，请检查FortiGate策略和VIP配置", "twice_nat44_error.impact.validation_warning": "验证警告可能影响规则的正确性，建议检查配置参数", "twice_nat44_error.impact.conversion_error": "转换错误将导致规则无法生成，请检查输入数据格式", "twice_nat44_error.impact.resource_critical": "资源严重不足，可能导致系统不稳定，建议释放内存或重启服务", "twice_nat44_error.impact.general": "发生未知错误，可能影响系统正常运行", "twice_nat44_error.action.check_config": "请检查FortiGate策略和VIP配置的完整性和正确性", "twice_nat44_error.action.validate_input": "请验证输入数据的格式和有效性", "twice_nat44_error.action.check_data_format": "请检查数据格式是否符合要求", "twice_nat44_error.action.check_template": "请检查XML模板配置是否正确", "twice_nat44_error.action.free_resources": "请释放系统资源或增加可用内存", "twice_nat44_error.action.check_network": "请检查网络连接状态", "twice_nat44_error.action.contact_support": "请联系技术支持获取帮助", "twice_nat44_error.user_message.config_error": "配置错误：策略 {policy_name} 的配置不完整或无效", "twice_nat44_error.user_message.validation_error": "验证错误：规则 {rule_name} 验证失败", "twice_nat44_error.user_message.conversion_error": "转换错误：无法将策略 {policy_name} 转换为twice-nat44规则", "twice_nat44_error.user_message.xml_generation_error": "XML生成错误：规则 {rule_name} 的XML生成失败", "twice_nat44_error.user_message.template_integration_error": "模板集成错误：无法将规则集成到XML模板", "twice_nat44_error.user_message.performance_error": "性能错误：处理过程中出现性能问题", "twice_nat44_error.user_message.resource_error": "资源错误：系统资源不足", "twice_nat44_error.user_message.network_error": "网络错误：网络连接异常", "twice_nat44_error.user_message.unknown_error": "未知错误：发生了未预期的错误", "twice_nat44_decorator.retry_success": "操作 {operation} 在第 {attempt} 次尝试后成功", "twice_nat44_decorator.using_fallback": "操作 {operation} 使用回退值: {fallback}", "twice_nat44_decorator.retry_attempt": "操作 {operation} 第 {attempt}/{max_retries} 次重试，延迟 {delay}秒，错误: {error}", "twice_nat44_performance.execution_completed": "操作 {operation} 执行完成，耗时: {execution_time}，内存变化: {memory_delta}", "twice_nat44_performance.execution_failed": "操作 {operation} 执行失败，耗时: {execution_time}，错误: {error}", "twice_nat44_cache.cache_hit": "缓存命中: {cache_key}, 函数: {function}", "twice_nat44_cache.cache_stored": "缓存已存储: {cache_key}, 函数: {function}", "twice_nat44_context.operation_started": "操作开始: {operation}", "twice_nat44_context.operation_completed": "操作完成: {operation}, 耗时: {duration}", "twice_nat44_context.operation_failed": "操作失败: {operation}, 耗时: {duration}, 错误ID: {error_id}", "twice_nat44_optimizer.initialized": "twice-nat44性能优化器已初始化，批处理大小: {batch_size}, 最大工作线程: {max_workers}", "twice_nat44_optimizer.memory_warning": "内存使用超过阈值: 当前{current}MB, 阈值{threshold}MB", "twice_nat44_optimizer.batch_completed": "批量处理完成: 规则数{rule_count}, 成功{success_count}, 失败{error_count}, 耗时{execution_time}, 吞吐量{throughput}", "twice_nat44_optimizer.batch_error": "批量处理错误: {error}", "twice_nat44_optimizer.gc_performed": "垃圾回收执行完成，回收对象数: {collected}", "twice_nat44_optimizer.memory_optimized": "内存优化完成，清理过期缓存项: {expired_cache_items}", "twice_nat44_optimizer.stats_reset": "性能统计信息已重置", "twice_nat44_memory.limit_exceeded": "内存使用超过限制: 当前{current}MB, 限制{limit}MB", "twice_nat44_memory.gc_executed": "垃圾回收执行: 调用次数{call_count}, 回收对象数{collected}", "capacity.violation_message": "{resource}数量超出限制：当前{current}个，限制{limit}个", "capacity.violation_warning": "容量限制警告：{resource}数量({current})超出{device_model}设备限制({limit})。{suggestion}", "capacity.count_dns_servers": "统计DNS服务器数量: {count}", "capacity.count_sub_interfaces": "统计子接口数量: {count}", "capacity.count_pppoe_sessions": "统计PPPOE会话数量: {count}", "capacity.count_static_routes": "统计静态路由数量: {count}", "capacity.count_nat_policies": "统计NAT策略数量: {count}", "capacity.count_security_policies": "统计安全策略数量: {count}", "info.capacity_validation_completed": "容量校验完成，发现{violations}个违规项，设备型号: {device_model}", "info.capacity_validation_passed": "容量校验通过，设备型号: {device_model}", "warning.capacity_validation_failed": "容量校验失败: {error}", "warning.unsupported_device_model": "不支持的设备型号: {device_model}", "warning.capacity_config_not_found": "容量配置文件未找到: {file}", "error.capacity_validation_error": "容量校验错误: {error}", "error.load_capacity_config": "加载容量配置失败: {error}", "error.count_resources": "统计资源数量失败: {error}", "warning.capacity_violation": "容量违规警告 - {resource}: 当前{current}，限制{limit}", "info.capacity_validation_detailed_report": "容量校验详细报告生成完成 - 设备型号: {device_model}, 违规项: {total_violations}, 合规率: {compliance_rate}", "warning.capacity_violation_detailed": "容量违规详情 - {resource}: 当前{current}/{limit} (使用率{usage_rate}, 风险级别{risk_level}, 类别{category})", "capacity.report.title": "设备容量校验报告 - {device_model}", "capacity.report.total_resources": "检查资源数量: {count}", "capacity.report.total_violations": "发现违规项目: {count}", "capacity.report.critical_violations": "严重违规: {count}", "capacity.report.warning_violations": "警告违规: {count}", "capacity.report.compliance_rate": "合规率: {rate}", "capacity.report.violation_details": "违规详情:", "capacity.report.current_count": "当前数量: {count}", "capacity.report.limit_count": "限制数量: {count}", "capacity.report.excess_count": "超出数量: {count}", "capacity.report.usage_rate": "使用率: {rate}", "capacity.report.severity_level": "严重级别: {level}", "capacity.report.suggestion": "建议: {suggestion}", "capacity.tips.policy.merge_similar": "合并相似的策略规则", "capacity.tips.policy.remove_unused": "删除不再使用的策略", "capacity.tips.policy.use_groups": "使用地址组和服务组简化策略", "capacity.tips.object.cleanup_unused": "清理未使用的对象", "capacity.tips.object.merge_duplicates": "合并重复的对象定义", "capacity.tips.object.use_groups": "使用组对象减少单个对象数量", "capacity.tips.interface.check_unused": "检查是否有未使用的接口配置", "capacity.tips.interface.vlan_aggregation": "考虑使用VLAN聚合", "capacity.tips.interface.optimize_structure": "优化接口配置结构", "capacity.tips.network.optimize_config": "优化网络配置", "capacity.tips.network.check_necessity": "检查配置的必要性", "capacity.tips.network.efficient_methods": "考虑使用更高效的配置方式", "capacity.impact.critical": "可能导致设备性能严重下降或功能异常", "capacity.impact.high": "可能影响设备性能和稳定性", "capacity.impact.medium": "可能在高负载时影响性能", "capacity.impact.low": "当前影响较小，但需要关注", "capacity.config.description": "设备容量限制配置文件 - 定义不同设备型号的资源容量限制", "capacity.device.z3200s.description": "锐捷Z3200S系列防火墙", "capacity.device.z5100s.description": "锐捷Z5100S系列防火墙", "capacity.resource.dns_servers.description": "DNS服务器最大数量", "capacity.resource.dns_servers.suggestion": "建议配置不超过3个DNS服务器", "capacity.resource.sub_interfaces.description": "子接口最大数量", "capacity.resource.sub_interfaces.suggestion.z3200s": "建议减少子接口数量或升级到更高型号", "capacity.resource.sub_interfaces.suggestion.z5100s": "建议减少子接口数量", "capacity.resource.pppoe_sessions.description": "PPPOE拨号整机最大数量", "capacity.resource.pppoe_sessions.suggestion": "建议减少PPPOE拨号会话数量", "capacity.resource.static_routes_v4.description": "静态路由V4最大条数", "capacity.resource.static_routes_v4.suggestion": "建议优化路由配置，减少静态路由条数", "capacity.resource.nat44_policies.description": "NAT44策略最大数量", "capacity.resource.nat44_policies.suggestion.z3200s": "建议优化NAT策略配置或升级到Z5100S型号", "capacity.resource.nat44_policies.suggestion.z5100s": "建议优化NAT策略配置", "capacity.resource.security_policies.description": "安全策略最大数量", "capacity.resource.security_policies.suggestion.z3200s": "建议优化安全策略配置或升级到Z5100S型号", "capacity.resource.security_policies.suggestion.z5100s": "建议优化安全策略配置", "capacity.resource.address_objects.description": "地址对象最大数量", "capacity.resource.address_objects.suggestion": "建议使用地址组来优化地址对象管理", "capacity.resource.address_groups.description": "地址对象组最大数量", "capacity.resource.address_groups.suggestion": "建议优化地址组配置", "capacity.resource.service_objects.description": "服务对象最大数量", "capacity.resource.service_objects.suggestion": "建议使用服务组来优化服务对象管理", "capacity.resource.service_groups.description": "服务对象组最大数量", "capacity.resource.service_groups.suggestion": "建议优化服务组配置", "capacity.resource.time_objects.description": "时间对象最大数量", "capacity.resource.time_objects.suggestion": "建议优化时间对象配置"}