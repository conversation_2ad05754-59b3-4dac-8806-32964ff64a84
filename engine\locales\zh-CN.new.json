{"compress.message.compress_error": "[需要翻译] compress.error.lib_not_exists", "compress.error.file": "库文件不存在: {lib_path}", "compress.message.compress_error_1": "[需要翻译] compress.error.lib_not_file", "compress.error.file_1": "指定的库路径不是文件: {lib_path}", "compress.message.compress_error_2": "[需要翻译] compress.error.checking_permissions", "compress.message.load": "针对容器环境的库加载方法", "compress.message.load_1": "传统的库加载方法", "compress.message.compress_error_3": "[需要翻译] compress.error.container_detection_failed", "compress.message.process": "在容器环境中准备库加载的特殊处理", "compress.message.compress_error_4": "[需要翻译] compress.error.preparing_container_loading", "compress.message.dir": "析构函数，清理临时目录", "compress.message.compress_error_5": "[需要翻译] compress.error.cleanup_temp_dir", "compress.message.file_remove_file": "全局库文件清理，特别是删除任何可能干扰的libc.so文件", "compress.message.compress_error_6": "[需要翻译] compress.error.global_cleanup_failed", "compress.message.compress_error_7": "[需要翻译] compress.error.checking_lib_permissions", "compress.message.compress_error_8": "[需要翻译] compress.error.missing_critical_libs", "compress.message.compress_error_9": "[需要翻译] compress.error.creating_system_lib_dir", "compress.message.compress_error_10": "[需要翻译] compress.error.creating_symlinks", "compress.message.compress_error_11": "[需要翻译] compress.error.running_ldconfig", "compress.message.compress_error_12": "[需要翻译] compress.error.preparing_library_files", "compress.message.config": "设置库搜索路径 - 使用最小化策略避免系统库冲突", "compress.message.compress_error_13": "[需要翻译] compress.error.setting_ld_library_path", "compress.message.dir_1": "从当前目录或专用加密库目录加载库", "compress.message.compress_error_14": "[需要翻译] compress.error.critical_lib_missing", "compress.message.compress_error_15": "[需要翻译] compress.error.preload_libs_failed", "compress.message.load_2": "容器环境下的库加载方法", "compress.message.load_3": "标准库加载方法", "compress.message.compress_error_16": "[需要翻译] compress.error.load_lib_mode_failed", "compress.message.compress_error_17": "[需要翻译] compress.error.load_lib_failed", "compress.message.compress_error_18": "[需要翻译] compress.error.input_file_not_exists", "compress.message.compress_error_19": "[需要翻译] compress.error.call_real_api_failed", "compress.message.compress_error_20": "[需要翻译] compress.error.version_file_not_exists", "compress.message.dir_2": "设置临时库目录（仅非容器环境）", "compress.error.dir": "设置临时库目录失败: {str(e)}", "compress.message.load_4": "快速设置环境并加载库", "compress.error.failed": "预加载依赖库失败", "compress.error.failed_1": "加载主库失败", "compress.message.config_1": "快速设置库路径环境变量", "compress.message.load_5": "快速加载依赖库", "compress.message.load_6": "快速加载主库", "compress.message.compress_error_21": "[需要翻译] compress.error.main_lib_not_exists", "compress.message.compress_error_22": "[需要翻译] compress.error.all_load_methods_failed", "compress.message.compress_error_23": "[需要翻译] compress.error.missing_main_function", "compress.message.compress_error_24": "[需要翻译] compress.error.list_symbols_failed", "compress.message.compress_error_25": "[需要翻译] compress.error.load_main_library_failed", "compress.message.load_7": "使用适当的标志加载库", "compress.message.content_625f6b53": "清理资源", "compress.message.compress_error_26": "[需要翻译] compress.error.tar_gz_not_exists", "compress.message.compress_error_27": "[需要翻译] compress.error.version_file_not_found", "compress.message.compress_error_28": "[需要翻译] compress.error.encrypted_file_not_found", "compress.message.compress_error_29": "[需要翻译] compress.error.process_tar_gz_failed", "compress.message.compress_error_30": "[需要翻译] compress.error.input_dir_not_exists", "compress.message.tar_czf_config": "[需要翻译] tar -czf {output_file} Config", "compress.message.compress_error_31": "[需要翻译] compress.error.tar_command_failed", "compress.message.compress_error_32": "[需要翻译] compress.error.create_config_tar_gz", "compress.message.compress_error_33": "[需要翻译] compress.error.cannot_delete_output", "compress.message.config_tar": "[需要翻译] Config.tar.gz", "compress.message.config_tar_1": "[需要翻译] Config.tar.gz.en", "compress.message.compress_error_34": "[需要翻译] compress.error.encryption_failed", "compress.message.compress_error_35": "[需要翻译] compress.error.encryption_abort", "compress.message.compress_error_36": "[需要翻译] compress.error.encrypted_file_invalid", "compress.message.compress_error_37": "[需要翻译] compress.error.version_file_invalid", "compress.message.compress_error_38": "[需要翻译] compress.error.output_file_invalid", "compress.message.compress_error_39": "[需要翻译] compress.error.create_tar_file", "compress.message.compress_error_40": "[需要翻译] compress.error.encryption_process", "compress.message.compress_error_41": "[需要翻译] compress.error.decryption_failed", "compress.message.compress_error_42": "[需要翻译] compress.error.during_decryption", "compress.message.compress_error_43": "[需要翻译] compress.error.unknown_action", "converter.message.configuration_setting": "[需要翻译] Configuration setting", "converter.message.failed": "新架构失败详细信息:\\n{stack_trace}", "converter.message.failed_model_version": "新架构失败时的参数: cli_file={cli_file}, model={model}, version={version}", "converter.message.user_error": "[需要翻译] user.error.file_not_exists", "converter.message.user_error_1": "[需要翻译] user.error.vendor_not_supported", "converter.message.user_error_2": "[需要翻译] user.error.parsing_config_failed", "converter.message.user_error_3": "[需要翻译] user.error.transparent_mode_processing_failed", "converter.message.user_error_4": "[需要翻译] user.error.interface_mapping_validation_failed", "converter.message.content_2d0f8707": "不在设备型号", "converter.message.user_error_5": "[需要翻译] user.error.interface_model_validation_failed", "converter.message.user_warning": "[需要翻译] user.warning.compatibility_issues_detected", "converter.message.user_warning_1": "[需要翻译] user.warning.all_physical_interfaces_unmapped", "converter.message.user_error_6": "[需要翻译] user.error.template_not_found", "converter.message.config": "透明模式 - 接口 {interface_name} 工作模式设置为: {working_mode}", "converter.message.config_route": "路由模式 - 接口 {interface_name} 工作模式设置为: route", "converter.error.create": "生成器返回了意外的结果格式: {len(generate_result)} 个元素", "converter.message.content_c455b042": "未找到接口映射", "converter.message.content_f91d273b": "未知原因", "converter.message.content_08fe5024": "严重程度", "converter.message.content_7d3a5003": "接口列表", "converter.message.user_error_7": "[需要翻译] user.error.yang_validation_failed", "converter.message.user_error_8": "[需要翻译] user.error.encryption_failed", "converter.message.error": "加密过程发生异常: {str(e)}", "converter.message.user_error_9": "[需要翻译] user.error.encryption_error", "converter.message.user_error_10": "[需要翻译] user.error.target_config_generation_failed", "converter.message.user_error_11": "[需要翻译] user.error.conversion_failed", "converter.message.enhanced_policy_processing": "[需要翻译] Enhanced policy processing failed: {str(e)}", "converter.message.user_warning_2": "[需要翻译] user.warning.nat_not_supported_in_version", "converter.message.user_warning_3": "[需要翻译] user.warning.route_interface_not_mapped_check", "converter.message.user_error_12": "[需要翻译] user.error.duplicate_service_object", "converter.message.user_warning_4": "[需要翻译] user.warning.unsupported_protocol_ntos", "converter.message.content_a7aa7a43": "目标端口:源端口", "converter.message.convert": "格式化转换结果为JSON字符串", "converter.message.user_warning_5": "[需要翻译] user.warning.zone_unmapped_interfaces", "converter.message.user_warning_6": "[需要翻译] user.warning.total_unmapped_zone_interfaces", "misc.message.error": "[需要翻译] {output_file}.error", "misc.message.encrypt_error": "[需要翻译] encrypt.error.output_invalid", "misc.message.encrypt_error_1": "[需要翻译] encrypt.error.library_failed", "misc.message.encrypt_error_2": "[需要翻译] encrypt.error.process_failed", "misc.message.success_save": "加密成功，结果保存至: {output_file}", "misc.message.failed": "加密失败", "misc.message.content_a248e06e": "加密出错: {str(e)}", "misc.message.config": "配置加密工具", "misc.message.file": "XML配置文件路径", "misc.message.file_1": "模板文件路径", "misc.message.file_2": "输出加密文件路径", "misc.message.content_6a6130dc": "指定使用的语言，默认为zh-CN", "misc.message.convert": "将子网掩码转换为前缀长度", "misc.message.content_ec4ab8d8": "从接口名中提取VLAN ID (例如从", "misc.message.content_e5d56f90": "提取10)", "misc.message.config_system": "[需要翻译] config system", "misc.message.config_firewall": "[需要翻译] config firewall", "misc.message.config_interface": "[需要翻译] config interface", "misc.message.config_1": "从飞塔配置中提取接口信息，只提取区域、接口名称、IP、子网掩码", "misc.message.config_system_interface": "[需要翻译] config system interface", "misc.message.config_system_zone": "[需要翻译] config system zone(.*?)end", "misc.message.fortigate_warning": "[需要翻译] fortigate.warning.unsupported_role", "misc.message.content_db4e4452": "格式化提取结果为JSON字符串", "misc.message.content_83de32e1": "安全地打印JSON数据，避免编码问题", "misc.message.multi_vendor_configuration": "[需要翻译] Multi-vendor configuration conversion tool main entry", "misc.message.file_3": "验证配置文件失败: {str(e)}", "misc.message.error_1": "转换过程发生系统错误: {error_message}", "misc.message.config_2": "多厂商配置转换工具", "misc.message.config_verify_extract": "工作模式: verify=验证配置, extract=提取接口, convert=转换配置", "misc.message.fortigate": "设备厂商 (默认: fortigate)", "misc.message.file_4": "输入配置文件路径", "misc.message.check_check": "以JSON格式输出验证结果 (仅验证模式有效)", "misc.message.check": "目标设备型号，用于容量限制校验 (验证模式可选，如z3200s, z5100s)", "misc.message.file_convert": "接口映射文件路径 (仅转换模式需要)", "misc.message.convert_1": "目标设备型号 (仅转换模式需要)", "misc.message.convert_2": "目标设备版本 (仅转换模式需要)", "misc.message.file_convert_1": "输出文件路径 (仅转换模式需要)", "misc.message.file_convert_2": "加密输出文件路径 (仅转换模式需要)", "misc.message.file_5": "接口信息输出JSON文件路径 (仅提取模式可用)", "misc.message.file_6": "日志文件路径", "misc.message.file_7": "用户日志文件路径", "misc.message.dir_save": "日志目录路径，将按级别分类保存日志", "misc.message.content_65d7708a": "静默模式，不输出日志到标准输出", "misc.message.content_ba62853b": "启用DEBUG级别日志，显示详细的调试信息", "misc.message.debug": "启用详细模式，等同于--debug", "misc.message.debug_info_warning": "日志级别 (10=DEBUG, 20=INFO, 30=WARNING, 40=ERROR, 50=CRITICAL)", "misc.message.content_85e0c86e": "语言 (默认: zh-CN)", "misc.message.convert_3": "强制使用新架构进行转换", "misc.message.convert_4": "强制使用传统架构进行转换", "misc.message.content_3ceaf1c3": "启用DEBUG模式，将显示详细的调试信息", "misc.message.config_system_vdom": "[需要翻译] config\\s+system\\s+vdom(.*?)end", "misc.message.config_system_interface_1": "[需要翻译] config\\s+system\\s+interface(.*?)end", "misc.message.config_firewall_policy": "[需要翻译] config\\s+firewall\\s+policy(.*?)end", "misc.message.config_firewall_address": "[需要翻译] config\\s+firewall\\s+address(.*?)end", "misc.message.config_firewall_addrgrp": "[需要翻译] config\\s+firewall\\s+addrgrp(.*?)end", "misc.message.config_firewall_service": "[需要翻译] config\\s+firewall\\s+service\\s+custom(.*?)end", "misc.message.config_firewall_service_1": "[需要翻译] config\\s+firewall\\s+service\\s+group(.*?)end", "misc.message.config_router_static": "[需要翻译] config\\s+router\\s+static(.*?)end", "misc.message.config_system_dns": "[需要翻译] config\\s+system\\s+dns(.*?)end", "misc.message.config_version": "[需要翻译] #config-version=\\w+-(\\d+\\.\\d+\\.\\d+)", "misc.message.config_system_global": "[需要翻译] config system global(.*?)end", "misc.message.config_system_auto": "[需要翻译] config system auto-script", "misc.message.config_firewall_policy_1": "[需要翻译] config firewall policy-ddns", "misc.message.config_user_vendor": "[需要翻译] config user vendor-important-device", "misc.message.config_system_fabric": "[需要翻译] config system fabric-vpn", "misc.message.config_system_dns_1": "[需要翻译] config system dns-database", "misc.message.config_user_radius": "[需要翻译] config user radius dynamic-mapping", "misc.message.config_system_sdwan": "[需要翻译] config system sdwan", "misc.message.config_system_password": "[需要翻译] config system password-policy", "misc.message.config_vpn_ssl": "[需要翻译] config vpn ssl settings", "misc.message.config_system_virtual": "[需要翻译] config system virtual-wan-link", "misc.message.config_router": "[需要翻译] config router", "misc.message.config_vpn": "[需要翻译] config vpn", "misc.message.config_version_1": "[需要翻译] #config-version=([^-]+)-", "misc.message.config_3": "[需要翻译] ^config\\s*$", "misc.message.failed_1": "验证配置失败: {str(e)}", "misc.message.load": "加载设备容量限制配置", "misc.message.config_4": "统计FortiGate配置中的各类资源数量", "misc.message.content_405e3987": "容量违规信息类 - 增强版本，支持详细报告和分析", "misc.message.content_31afdfb0": "计算风险级别", "misc.message.create": "生成详细分析", "misc.message.content_af73817c": "获取推荐操作", "misc.message.content_256df2d2": "获取影响评估", "misc.message.content_6f79ec35": "获取优化建议", "misc.message.convert_5": "转换为详细的字典格式", "misc.message.convert_6": "转换为简化的字典格式", "misc.message.create_1": "容量校验报告生成器", "misc.message.load_1": "加载设备限制信息", "misc.message.create_2": "生成摘要报告", "misc.message.create_3": "生成详细报告", "misc.message.create_4": "生成资源使用情况报告", "misc.message.create_5": "生成优化建议", "misc.message.content_81c1cada": "获取设备信息", "misc.message.content_edce2d03": "未知设备型号", "misc.message.content_651f2642": "格式化控制台输出", "misc.message.content_d4694f83": "统计DNS服务器数量", "misc.message.content_631becfb": "统计子接口数量", "misc.message.content_3f74f9c3": "统计PPPOE拨号会话数量", "misc.message.content_091c1b07": "统计静态路由V4条数", "misc.message.content_1038f313": "统计NAT44策略数量", "misc.message.content_fba223f0": "统计安全策略数量", "misc.message.content_36e1b486": "统计地址对象数量", "misc.message.content_94717b40": "统计地址对象组数量", "misc.message.content_6efefb15": "统计服务对象数量", "misc.message.content_0245cbbe": "统计服务对象组数量", "misc.message.content_4d6e170a": "统计时间对象数量", "misc.message.config_firewall_schedule": "[需要翻译] config\\s+firewall\\s+schedule\\s+recurring(.*?)end", "misc.message.config_firewall_schedule_1": "[需要翻译] config\\s+firewall\\s+schedule\\s+onetime(.*?)end", "misc.message.invalid_element_found": "[需要翻译] Invalid element found in XInclude namespace (%r)", "misc.message.contains_generated_elementtree": "[需要翻译] Contains the generated ElementTree after parsing is finished.", "misc.error.prefix_found_prefix": "[需要翻译] prefix %r not found in prefix map", "misc.error.invalid_descendant": "[需要翻译] invalid descendant", "misc.error.invalid_predicate": "[需要翻译] invalid predicate", "misc.error.invalid_path": "[需要翻译] invalid path", "misc.message.error_message": "[需要翻译] error-message", "misc.message.error_block": "[需要翻译] error-block", "misc.debug.error_must_give": "[需要翻译] Error: you must give two files", "misc.message.descendant_self_option": "[需要翻译] descendant-or-self::option|descendant-or-self::x:option", "misc.error.invalid_class_name": "[需要翻译] Invalid class name: %r", "misc.message.there_option_value": "[需要翻译] There is no option with the value of %r", "misc.message.there_option_value_1": "[需要翻译] There is no option with the value %r", "misc.message.option_currently_selected": "[需要翻译] The option %r is not currently selected", "misc.message.there_option_value_2": "[需要翻译] There is not option with the value %r", "misc.message.svrl_failed_assert": "[需要翻译] //svrl:failed-assert", "misc.error.validating_iso_schematron": "[需要翻译] Validating the ISO schematron requires iso-schematron.rng", "misc.error.none_allowed_stylesheet": "[需要翻译] None not allowed as a stylesheet parameter", "misc.message.svrl_failed_assert_1": "[需要翻译] //svrl:failed-assert | //svrl:successful-report", "misc.message.invalid_schematron_schema": "[需要翻译] invalid schematron schema: %s", "misc.message.warn_script_location": "[需要翻译] --no-warn-script-location", "misc.message.ignoring_invalid_cache": "[需要翻译] Ignoring invalid cache entry origin file %s for %s (%s)", "misc.message.got_invalid_value": "[需要翻译] Got invalid value for load_only - should be one of {}", "misc.message.loads_configuration_configurat": "[需要翻译] Loads configuration from configuration files and environment", "misc.message.returns_file_highest": "[需要翻译] Returns the file with highest priority in configuration", "misc.message.value_configuration": "[需要翻译] Get a value from the configuration.", "misc.message.modify_value_configuration": "[需要翻译] Modify a value in the configuration.", "misc.message.unset_value_configuration": "[需要翻译] Unset a value in the configuration.", "misc.message.fatal_internal_error": "[需要翻译] Fatal Internal error [id=1]. Please report as a bug.", "misc.message.error_occurred_while": "[需要翻译] An error occurred while writing to the configuration file ", "misc.message.content_6208cddb": "[需要翻译] {fname}: {error}", "misc.message.dictionary_representing_loaded": "[需要翻译] A dictionary representing the loaded configuration.", "misc.message.loads_configuration_configurat_1": "[需要翻译] Loads configuration from configuration files", "misc.message.skipping_loading_configuration": "[需要翻译] Skipping loading configuration files due to ", "misc.info.variant_try_loading": "[需要翻译] For variant '%s', will try loading '%s'", "misc.message.contains_invalid_characters": "[需要翻译] contains invalid {locale_encoding} characters", "misc.message.loads_configuration_environmen": "[需要翻译] Loads configuration from environment variables", "misc.message.values_present_config": "[需要翻译] Get values present in a config file", "misc.message.fatal_internal_error_1": "[需要翻译] Fatal Internal error [id=2]. Please report as a bug.", "misc.message.base_pip_error": "[需要翻译] The base pip error.", "misc.message.literal_error_warning": "[需要翻译] Literal[\"error\", \"warning\"]", "misc.error.error_reference_provided": "[需要翻译] error reference not provided!", "misc.error.error_reference_must": "[需要翻译] error reference must be kebab-case!", "misc.message.general_exception_configuratio": "[需要翻译] General exception in configuration", "misc.message.general_exception_during": "[需要翻译] General exception during installation", "misc.message.missing_pyproject_build": "[需要翻译] missing-pyproject-build-system-requires", "misc.message.package_invalid_pyproject": "[需要翻译] This package has an invalid pyproject.toml file.\\n", "misc.message.build_system_table": "[需要翻译] The [build-system] table is missing the mandatory `requires` key.", "misc.message.raised_when_pyproject": "[需要翻译] Raised when pyproject.toml an invalid `build-system.requires`.", "misc.message.invalid_pyproject_build": "[需要翻译] invalid-pyproject-build-system-requires", "misc.message.package_invalid_build": "[需要翻译] This package has an invalid `build-system.requires` key in ", "misc.message.raised_when_general": "[需要翻译] Raised when a general error occurs parsing a requirements file line.", "misc.message.raised_when_virtualenv": "[需要翻译] Raised when virtualenv or a command is not found", "misc.message.raised_when_there": "[需要翻译] Raised when there is an error in command-line arguments", "misc.message.http_connection_error": "[需要翻译] HTTP connection error", "misc.message.invalid_wheel_filename": "[需要翻译] Invalid wheel filename.", "misc.message.invalid_corrupt_wheel": "[需要翻译] Invalid (e.g. corrupt) wheel.", "misc.message.wheel_located_invalid": "[需要翻译] Wheel '{self.name}' located at {self.location} is invalid.", "misc.message.metadata_invalid": "[需要翻译] <PERSON><PERSON><PERSON> is invalid.", "misc.message.requested_invalid_metadata": "[需要翻译] Requested {self.ireq} has invalid metadata: {self.error}", "misc.message.subprocess_call_failed": "[需要翻译] A subprocess call failed.", "misc.message.subprocess_exited_error": "[需要翻译] subprocess-exited-with-error", "misc.message.error_originates_subprocess": "[需要翻译] This error originates from a subprocess, and is likely not a ", "misc.message.metadata_generation_failed": "[需要翻译] metadata-generation-failed", "misc.message.encountered_error_while": "[需要翻译] Encountered error while generating package metadata.", "misc.message.metadata_generation_failed_1": "[需要翻译] metadata generation failed", "misc.message.missing_some_requirements": "[需要翻译] missing from some requirements. Here is a list of those ", "misc.message.when_there_errors": "[需要翻译] When there are errors while loading a configuration file", "misc.message.content_c1802a94": "[需要翻译] .\\n{self.error}\\n", "misc.message.configuration_file": "[需要翻译] Configuration file {self.reason}{message_part}", "misc.message.error_2": "[需要翻译] Error-{lang}", "misc.message.error_3": "[需要翻译] Error-{before}", "misc.info.failed_read": "[需要翻译] Failed to read %s", "misc.message.invalid_installed_package": "[需要翻译] invalid-installed-package", "misc.message.because_invalid": "[需要翻译] because it has an invalid {invalid_type}:\\n{invalid_exc.args[0]}", "misc.message.starting_pip_packages": "[需要翻译] Starting with pip 24.1, packages with invalid ", "misc.message.download_failed_because": "[需要翻译] Download failed {retry_status}because not enough bytes ", "misc.message.disabling_pep_processing": "[需要翻译] Disabling PEP 517 processing is invalid: ", "misc.message.contains_invalid_requirement": "[需要翻译] It contains an invalid requirement: {requirement!r}", "misc.message.bold_reset_blue": "[需要翻译] [bold][[reset][blue]notice[reset][bold]][reset]", "misc.message.release_pip_available": "[需要翻译] {notice} A new release of pip is available: ", "misc.message.update_run": "[需要翻译] {notice} To update, run: ", "misc.error.missing_metadata_version": "[需要翻译] Missing Metadata-Version", "misc.message.invalid_metadata_version": "[需要翻译] Invalid Metadata-Version: {metadata_version_value}", "misc.message.building_failed": "[需要翻译] Building %s for %s failed: %s", "misc.info.built_invalid": "[需要翻译] Built %s for %s is invalid: %s", "misc.message.ignoring_global_option": "[需要翻译] Ignoring --global-option when building %s using PEP 517", "misc.message.ignoring_build_option": "[需要翻译] Ignoring --build-option when building %s using PEP 517", "misc.message.building_wheel_failed": "[需要翻译] Building wheel for %s failed: %s", "misc.info.failed_cleaning_build": "[需要翻译] Failed cleaning build dir for %s", "misc.message.failed_build": "[需要翻译] Failed to build %s", "misc.info.exception_information": "[需要翻译] Exception information:", "misc.debug.error_pipe_stdout": "[需要翻译] ERROR: Pipe to stdout was broken", "misc.info.exception": "[需要翻译] Exception:", "misc.message.python_option_must": "[需要翻译] The --python option must be placed before the pip subcommand name", "misc.message.error_4": "[需要翻译] {option} error: {msg}", "misc.message.configuration": "[需要翻译] configuration.", "misc.message.exit_error_otherwise": "[需要翻译] exit with an error otherwise.", "misc.message.give_more_output": "[需要翻译] Give more output. Option is additive, and can be used up to 3 times.", "misc.message.give_less_output": "[需要翻译] Give less output. Option is additive, and can be used up to 3", "misc.message.times_corresponding_warning": "[需要翻译]  times (corresponding to WARNING, ERROR, and CRITICAL logging", "misc.message.timeout": "[需要翻译] --timeout", "misc.message.default_timeout": "[需要翻译] --default-timeout", "misc.message.set_socket_timeout": "[需要翻译] Set the socket timeout (default %default seconds).", "misc.message.option_used_multiple": "[需要翻译] This option can be used multiple times.", "misc.message.disable_binary_packages": "[需要翻译] disable all binary packages, \":none:\" to empty the set (notice ", "misc.message.may_fail_install": "[需要翻译] and may fail to install when this option is used on them.", "misc.message.option_used_them": "[需要翻译] option is used on them.", "misc.message.platform_running_system": "[需要翻译] platform of the running system. Use this option multiple times to ", "misc.message.invalid_python_version": "[需要翻译] invalid --python-version value: {value!r}: {error_msg}", "misc.message.option_multiple_times": "[需要翻译] Use this option multiple times to specify multiple abis supported ", "misc.message.option": "[需要翻译] option.", "misc.message.option_used": "[需要翻译] if this option is used.", "misc.message.config_settings": "[需要翻译] --config-settings", "misc.message.configuration_settings_passed": "[需要翻译] Configuration settings to be passed to the PEP 517 build backend. ", "misc.message.settings_take_form": "[需要翻译] Settings take the form KEY=VALUE. Use multiple --config-settings options ", "misc.message.build_option": "[需要翻译] --build-option", "misc.message.global_option": "[需要翻译] --global-option", "misc.message.action_pip_run": "[需要翻译] Action if pip is run as a root user [warn, ignore] (default: warn)", "misc.message.repeatable_installs_option": "[需要翻译] repeatable installs. This option is implied when any package in a ", "misc.message.requirements_file_hash": "[需要翻译] requirements file has a --hash option.", "misc.message.python_version_warning": "[需要翻译] --no-python-version-warning", "misc.info.disabling_truststore_since": "[需要翻译] Disabling truststore since ssl support is missing", "misc.info.there_error_checking": "[需要翻译] There was an error checking the latest version of pip.", "misc.info.below_error": "[需要翻译] See below for error", "misc.message.error_5": "[需要翻译] ERROR: {exc}", "misc.info.ignoring_error_when": "[需要翻译] Ignoring error %s when setting locale", "misc.message.timeout_1": "[需要翻译] --timeout=5", "misc.message.timeout_2": "[需要翻译] --timeout==5", "misc.error.failed_run_pip": "[需要翻译] Failed to run pip under {interpreter}: {exc}", "misc.message.base_option_parser": "[需要翻译] Base option parser setup", "misc.message.list_options_including": "[需要翻译] Get a list of all options, including those in option groups.", "misc.debug.error_occurred_during": "[需要翻译] An error occurred during configuration: {exc}", "misc.message.ignoring_configuration_key": "[需要翻译] Ignoring configuration key '%s' as it's value is empty.", "misc.message.valid_value_option": "[需要翻译] {val} is not a valid value for {key} option, ", "misc.message.finished_status": "[需要翻译] finished with status '{final_status}'", "misc.message.error_must_pass": "[需要翻译] ERROR: You must pass {}\\n", "misc.message.pip_config_set": "[需要翻译] pip config set global.index-url https://example.org/", "misc.message.pip_config_set_1": "[需要翻译] pip config set download.timeout 10", "misc.message.system_wide_configuration": "[需要翻译] Use the system-wide configuration file only", "misc.message.user_configuration_file": "[需要翻译] Use the user configuration file only", "misc.message.current_environment_configurat": "[需要翻译] Use the current environment configuration file only", "misc.message.list_config_key": "[需要翻译] List config key-value pairs across different config files", "misc.message.config_5": "[需要翻译] {get_prog()} config {example}", "misc.message.example_config": "[需要翻译] (example: \"{get_prog()} config {example}\")", "misc.message.unable_save_configuration": "[需要翻译] Unable to save configuration. Please report this as a bug.", "misc.error.internal_error": "[需要翻译] Internal Error.", "misc.message.change_without_notice": "[需要翻译] change without notice.", "misc.message.cert_config_value": "[需要翻译] 'cert' config value", "misc.message.comments_when_generating": "[需要翻译] comments when generating output. This option can be ", "misc.message.also_python_option": "[需要翻译] See also the ``--python`` option if the intention is to ", "misc.message.warn_when_installing": "[需要翻译] Do not warn when installing scripts outside PATH", "misc.message.warn_conflicts": "[需要翻译] --no-warn-conflicts", "misc.message.warn_about_broken": "[需要翻译] Do not warn about broken dependencies", "misc.message.when_writing_stdout": "[需要翻译] When writing to stdout, please combine with the --quiet option ", "misc.message.failed_build_installable": "[需要翻译] Failed to build installable wheels for some ", "misc.message.error_while_checking": "[需要翻译] Error while checking for conflicts. Please file an issue on ", "misc.info.non_user_install": "[需要翻译] Non-user install due to --prefix or --target option", "misc.message.consider_using_user": "[需要翻译] Consider using the `--user` option", "misc.message.pip_config_debug": "[需要翻译] pip config debug", "misc.message.consider_checking_your": "[需要翻译] Consider checking your local proxy configuration with \"pip config debug\"", "misc.message.hint_error_might": "[需要翻译] HINT: This error might have occurred since ", "misc.message.freeze_format_cannot": "[需要翻译] The 'freeze' format cannot be used with the --outdated option.", "misc.message.list_format_freeze": "[需要翻译] List format 'freeze' cannot be used with the --outdated option.", "misc.message.without_prior_warning": "[需要翻译] without prior warning.", "misc.error.missing_required_argument": "[需要翻译] Missing required argument (search query).", "misc.message.xmlrpc_request_failed": "[需要翻译] XMLRPC request failed [code: {fault.faultCode}]\\n{fault.faultString}", "misc.info.error_please_provide": "[需要翻译] ERROR: Please provide a package name or names.", "misc.info.package_found": "[需要翻译] Package(s) not found: %s", "misc.message.file_option_used": "[需要翻译] file.  This option can be used multiple times.", "misc.message.invalid_requirement_ignored": "[需要翻译] Invalid requirement: %r ignored -", "misc.error.failed_build_more": "[需要翻译] Failed to build one or more wheels", "misc.message.pip_commands_configuration": "[需要翻译] pip._internal.commands.configuration", "misc.message.manage_local_global": "[需要翻译] Manage local and global configuration.", "misc.message.missing_build_requirements": "[需要翻译] Missing build requirements in pyproject.toml for %s.", "misc.message.some_build_dependencies": "[需要翻译] Some build dependencies for {requirement} are missing: {missing}.", "misc.message.connection_error": "[需要翻译] connection error: {exc}", "misc.message.ignoring_invalid_requires": "[需要翻译] Ignoring invalid Requires-Python (%r) for link: %s", "misc.message.ignoring_failed_requires": "[需要翻译] Ignoring failed Requires-Python check (%s not in: %r) for link: %s", "misc.message.invalid_wheel_filename_1": "[需要翻译] invalid wheel filename", "misc.message.missing_project_version": "[需要翻译] Missing project version for {self.project_name}", "misc.message.configuring_installation_schem": "[需要翻译] Configuring installation scheme with distutils config files ", "misc.message.error_parsing": "[需要翻译] Error parsing %s for %s: %s", "misc.message.package_invalid_requires": "[需要翻译] Package %r has an invalid Requires-Python: %s", "misc.message.ignoring_invalid_distribution": "[需要翻译] Ignoring invalid distribution %s (%s)", "misc.message.error_decoding_metadata": "[需要翻译] Error decoding metadata for {self._wheel_name}: {e} in {name} file", "misc.error.invalid_wheel": "[需要翻译] {name} has an invalid wheel, {e}", "misc.error.invalid_metadata_entry": "[需要翻译] invalid metadata entry 'name'", "misc.message.error_decoding_metadata_1": "[需要翻译] Error decoding metadata for {wheel}: {e} in {filename} file", "misc.message.missing": "[需要翻译] missing one of archive_info, dir_info, vcs_info", "misc.message.invalid_hash_format": "[需要翻译] invalid archive_info.hash format: {value!r}", "misc.message.binary_only_binary": "[需要翻译] --no-binary / --only-binary option requires 1 argument.", "misc.message.index_returned_invalid": "[需要翻译] Index returned invalid data-dist-info-metadata value: %s", "misc.message.tls_enabled_then": "[需要翻译] t have TLS enabled, then WARN if anyplace we", "misc.message.index_url_seems": "[需要翻译] The index url \"%s\" seems invalid, please provide a scheme.", "misc.message.future_versions_pip": "[需要翻译] Future versions of pip will raise the following error:\\n", "misc.message.installed_copy_keyring": "[需要翻译] Installed copy of keyring fails with exception %s", "misc.info.keyring_skipped_due": "[需要翻译] Keyring is skipped due to an exception", "misc.message.keyring_skipped_due": "[需要翻译] Keyring is skipped due to an exception: %s", "misc.message.response_callback_warn": "[需要翻译] Response callback to warn about incorrect credentials.", "misc.message.error_credentials_correct": "[需要翻译] 401 Error, Credentials not correct for %s", "misc.message.response_callback_save": "[需要翻译] Response callback to save credentials on success.", "misc.info.failed_save_credentials": "[需要翻译] Failed to save credentials", "misc.info.http_error_while": "[需要翻译] HTTP error %s while getting %s", "misc.message.warning_allow_anyway": "[需要翻译] this warning and allow it anyway with '--trusted-host %s'.", "misc.message.client_error_url": "[需要翻译] {resp.status_code} Client Error: {reason} for url: {resp.url}", "misc.message.server_error_url": "[需要翻译] {resp.status_code} Server Error: {reason} for url: {resp.url}", "misc.info.error_parsing_dependencies": "[需要翻译] Error parsing dependencies of %s: %s", "misc.message.warning": "[需要翻译]  this warning)", "misc.message.local_remote_invalid": "[需要翻译] local remote or invalid URI:", "misc.message.command_found_path": "[需要翻译] (%s command not found in path)", "misc.info.error_when_trying": "[需要翻译] Error when trying to get requirement for VCS system %s", "misc.message.processing": "[需要翻译] Processing %s", "misc.message.error_url": "[需要翻译] error {exc} for URL {link}", "misc.info.failed_building_wheel": "[需要翻译] Failed building wheel for %s", "misc.info.failed_building_editable": "[需要翻译] Failed building editable for %s", "misc.message.setting_option": "[需要翻译] setting the `--use-pep517` option, ", "misc.message.suppress_warning_warn": "[需要翻译] to suppress this warning, use --no-warn-script-location.", "misc.message.invalid_script_entry": "[需要翻译] Invalid script entry point: {entry_point} - A callable ", "misc.message.regex_match_requirement": "[需要翻译] regex match on requirement {req} failed, this should never happen", "misc.message.regex_group_selection": "[需要翻译] regex group selection for requirement {req} failed, this should never happen", "misc.error.invalid_requirement": "[需要翻译] Invalid requirement: {name!r}: {exc}", "misc.message.invalid_requirement": "[需要翻译] Invalid requirement: {req_as_string!r}: {exc}", "misc.error.invalid_requirement_1": "[需要翻译] Invalid requirement: {req_string!r}: {exc}", "misc.message.dependency_groups_resolution": "[需要翻译] [dependency-groups] resolution failed for '{groupname}' ", "misc.message.dependency_groups_table": "[需要翻译] [dependency-groups] table was missing from '{path}'. ", "misc.message.cannot_resolve_group": "[需要翻译] Cannot resolve '--group' option.", "misc.error.found_cannot_resolve": "[需要翻译] {path} not found. Cannot resolve '--group' option.", "misc.error.error_parsing": "[需要翻译] Error parsing {path}: {e}", "misc.error.error_reading": "[需要翻译] Error reading {path}: {e}", "misc.message.invalid_requirement_1": "[需要翻译] Invalid requirement: {line}\\n{e.msg}", "misc.message.because_config_settings": "[需要翻译] because --config-settings are specified.", "misc.message.set_requirement_after": "[需要翻译] Set requirement after generating metadata.", "misc.message.generating_metadata_package": "[需要翻译] Generating metadata for package %s ", "misc.message.backend_missing_hook": "[需要翻译] backend is missing the 'build_editable' hook. Since it does not ", "misc.message.due_previous_installation": "[需要翻译] due to a previous installation that failed . pip is ", "misc.message.try_using_config": "[需要翻译] try using --config-settings editable_mode=compat. ", "misc.message.config_settings_ignored": "[需要翻译] --config-settings ignored for legacy editable install of %s. ", "misc.message.build_option_global": "[需要翻译] --build-option and --global-option are deprecated.", "misc.message.config_settings_1": "[需要翻译] to use --config-settings", "misc.message.build_option_global_1": "[需要翻译] --build-option / --global-option. ", "misc.info.failed_restore": "[需要翻译] Failed to restore %s", "misc.info.exception_1": "[需要翻译] Exception: %s", "misc.error.invalid_installed_requirement": "[需要翻译] invalid to-be-installed requirement: {req}", "misc.message.ignoring_failed_requires_1": "[需要翻译] Ignoring failed Requires-Python check for package %r: %s not in %r", "misc.message.setting_extras": "[需要翻译] Setting %s extras to: %s", "misc.error.requires_python_error": "[需要翻译] Requires-Python error reported with no cause", "misc.error.installation_error_reported": "[需要翻译] Installation error reported with no cause", "misc.message.ignoring_version_since": "[需要翻译] Ignoring version %s of %s since it has invalid metadata:\\n", "misc.message.config_6": "[需要翻译] ~/.config/", "misc.message.warning_pip_being": "[需要翻译] WARNING: pip is being invoked by an old script wrapper. This will ", "misc.message.warning_deprecation": "[需要翻译] WARNING: DEPRECATION: ....", "misc.message.warning_1": "[需要翻译] WARNING: ", "misc.message.error_6": "[需要翻译] ERROR: ", "misc.error.stderr_rich_console": "[需要翻译] stderr rich console is missing!", "misc.error.stdout_rich_console": "[需要翻译] stdout rich console is missing!", "misc.message.raise_error_input": "[需要翻译] Raise an error if no input is allowed.", "misc.error.invalid_truth_value": "[需要翻译] invalid truth value {val!r}", "misc.message.root_user_action": "[需要翻译] Use the --root-user-action option if you know what you are doing and ", "misc.message.want_suppress_warning": "[需要翻译] want to suppress this warning.", "misc.message.error_execute_setup": "[需要翻译] ERROR: Can not execute `setup.py` since setuptools failed to import in ", "misc.message.build_environment_exception": "[需要翻译] the build environment with exception:", "misc.message.literal_raise_warn": "[需要翻译] Literal[\"raise\", \"warn\", \"ignore\"]", "misc.message.error_while_executing": "[需要翻译] Error %s while executing command %s", "misc.message.command_error_code": "[需要翻译] Command \"%s\" had error code %s in %s", "misc.error.invalid_value": "[需要翻译] Invalid value: on_returncode={on_returncode!r}", "misc.message.log_warning_rmtree": "[需要翻译] Log a warning for a `rmtree` error and continue", "misc.message.failed_remove_temporary": "[需要翻译] Failed to remove a temporary file '%s' due to %s.\\n", "misc.info.failed": "[需要翻译] %s failed with %s.", "misc.message.failed_remove_contents": "[需要翻译] Failed to remove contents in a temporary directory '%s'.\\n", "misc.message.invalid_member_tar": "[需要翻译] Invalid member in the tar file {}: {}", "misc.message.tar_file_member": "[需要翻译] In the tar file %s the member %s is invalid: %s", "misc.error.dist_info_directory": "[需要翻译] .dist-info directory not found", "misc.error.error_decoding": "[需要翻译] error decoding {path!r}: {e!r}", "misc.error.wheel_missing_wheel": "[需要翻译] WHEEL is missing Wheel-Version", "misc.error.invalid_wheel_version": "[需要翻译] invalid Wheel-Version: {version!r}", "misc.error.cannot_find_command": "[需要翻译] Cannot find command {cls.name!r} - invalid PATH", "misc.message.failed_determine_whether": "[需要翻译] Failed to determine whether protocol member {attr!r} ", "misc.message.failing_pass_value": "[需要翻译] Failing to pass a value for the 'fields' parameter", "misc.message.passing_none_fields": "[需要翻译] Passing `None` as the 'fields' parameter", "misc.message.parameter_specification": "[需要翻译] Parameter specification.", "misc.message.converting_none_type": "[需要翻译] For converting None to type(None), and strings to ForwardRef.", "misc.error.last_parameter_concatenate": "[需要翻译] The last parameter to Concatenate should be a ", "misc.message.more_than_typevartuple": "[需要翻译] More than one TypeVarTuple parameter in {alias}", "misc.error.type_parameter_default": "[需要翻译] Type parameter with a default", "misc.error.type_parameter_without": "[需要翻译] Type parameter {t!r} without a default", "misc.message.follows_type_parameter": "[需要翻译]  follows type parameter with a default", "misc.message.error_calling": "[需要翻译] Error calling __set_name__ on {type(val).__name__!r} ", "misc.message.cannot_pass_none": "[需要翻译] Cannot pass `None` as the 'fields' parameter ", "misc.error.non_default_type": "[需要翻译] non-default type parameter '{type_param!r}'", "misc.message.follows_default_type": "[需要翻译]  follows default type parameter", "misc.message.requests_packages_util": "[需要翻译] requests.packages.urllib3.util.timeout", "misc.message.missing_value_cache": "[需要翻译] Missing value for cache-control ", "misc.message.invalid_value_cache": "[需要翻译] Invalid value for cache-control directive ", "misc.info.cache_entry_deserialization": "[需要翻译] Cache entry deserialization failed, entry ignored", "misc.error.dependency_group_found": "[需要翻译] Dependency group '{group}' not found", "misc.error.invalid_dependency_group": "[需要翻译] Invalid dependency group item: {item}", "misc.message.invalid_dependency_group": "[需要翻译] Invalid dependency group item after parse: {item}", "misc.message.usage_error_dependency": "[需要翻译] Usage error: dependency-groups CLI requires tomli or Python 3.11+", "misc.message.invalid_missing_encoding": "[需要翻译] invalid or missing encoding declaration", "misc.message.key_found_first": "[需要翻译] Key not found in the first mapping: {!r}", "misc.message.converting_dictionary_wrapper": "[需要翻译] A converting dictionary wrapper.", "misc.message.converting_list_wrapper": "[需要翻译] A converting list wrapper.", "misc.message.converting_tuple_wrapper": "[需要翻译] A converting tuple wrapper.", "misc.error.invalid_name_version": "[需要翻译] invalid name or version: %r, %r", "misc.error.invalid_path_dist": "[需要翻译] invalid path for a dist-info file: ", "misc.info.missing": "[需要翻译] %s missing %r", "misc.error.invalid_repository": "[需要翻译] invalid repository: %s", "misc.error.sign_command_failed": "[需要翻译] sign command failed with error ", "misc.error.found": "[需要翻译] not found: %s", "misc.error.found_1": "[需要翻译] not found: %r", "misc.error.verify_command_failed": "[需要翻译] verify command failed with error code %s", "misc.info.invalid_path_wheel": "[需要翻译] invalid path for wheel: %s", "misc.info.error_matching": "[需要翻译] error matching %s with %r", "misc.info.json_fetch_failed": "[需要翻译] JSON fetch failed: %s", "misc.info.fetch_failed": "[需要翻译] <PERSON><PERSON> failed: %s: %s", "misc.info.find_done": "[需要翻译] find done for %s", "misc.message.invalid_action": "[需要翻译] invalid action %r", "misc.error.invalid_comparison": "[需要翻译] invalid comparison: %s %s %s", "misc.message.required_metadata_missing": "[需要翻译] A required metadata is missing", "misc.message.metadata_value_invalid": "[需要翻译] A metadata value is invalid", "misc.message.missing_required_metadata": "[需要翻译] missing required metadata: %s", "misc.error.invalid_value_1": "[需要翻译] '%s' is an invalid value for ", "misc.message.missing_metadata_items": "[需要翻译] Missing metadata items: %s", "misc.info.metadata_missing_warnings": "[需要翻译] Metadata: missing: %s, warnings: %s", "misc.info.failed_1": "[需要翻译] _find failed: %r %r", "misc.info.failed_open": "[需要翻译] Failed to open %s", "misc.message.oserror_errno_exec": "[需要翻译] OSError 8 [Errno 8] Exec format error", "misc.info.failed_write_executable": "[需要翻译] Failed to write executable - trying to ", "misc.error.invalid_expression": "[需要翻译] invalid expression: %s", "misc.error.error_string_literal": "[需要翻译] error in string literal: %s", "misc.error.invalid_uri": "[需要翻译] invalid URI: %s", "misc.error.invalid_url": "[需要翻译] Invalid URL: %s", "misc.error.invalid_version": "[需要翻译] invalid version: %s", "misc.error.invalid_constraint": "[需要翻译] invalid constraint: %s", "misc.error.invalid_requirement_2": "[需要翻译] invalid requirement: %s", "misc.error.invalid_specification": "[需要翻译] Invalid specification ", "misc.info.failed_external_data": "[需要翻译] Failed to get external data for %s: %s", "misc.info.exception_during_event": "[需要翻译] Exception during event publication", "misc.message.invalid_glob_recursive": "[需要翻译] invalid glob %r: recursive glob ", "misc.message.invalid_glob_mismatching": "[需要翻译] invalid glob %r: mismatching set marker '{' or '}'", "misc.message.certificate_verify_failed": "[需要翻译] certificate verify failed", "misc.message.done": "[需要翻译] done.", "misc.message.done_1": "[需要翻译] done.\\n", "misc.error.invalid_name": "[需要翻译] Invalid name or ", "misc.error.invalid_wheel_because": "[需要翻译] Invalid wheel, because metadata is ", "misc.message.missing_looked": "[需要翻译] missing: looked in %s", "misc.error.dist_info_directory_1": "[需要翻译] .dist-info directory expected, not found", "misc.info.byte_compilation_failed": "[需要翻译] Byte-compilation failed", "misc.info.installation_failed": "[需要翻译] installation failed.", "misc.error.invalid_entry": "[需要翻译] invalid entry in ", "misc.error.unsupported_error_handling": "[需要翻译] Unsupported error handling \"{}\"", "misc.message.base_exception_idna": "[需要翻译] Base exception for all IDNA-encoding related problems", "misc.message.exception_when_bidirectional": "[需要翻译] Exception when bidirectional requirements are not satisfied", "misc.message.exception_when_disallowed": "[需要翻译] Exception when a disallowed or unallocated codepoint is used", "misc.message.exception_when_codepoint": "[需要翻译] Exception when the codepoint is not valid in the context it is used", "misc.error.invalid_direction_codepoint": "[需要翻译] Invalid direction for codepoint at position {} in a right-to-left label", "misc.error.invalid_direction_codepoint_1": "[需要翻译] Invalid direction for codepoint at position {} in a left-to-right label", "misc.error.invalid_label": "[需要翻译] Invalid A-label", "misc.message.map_characters_string": "[需要翻译] Re-map the characters in the string according to UTS46 processing.", "misc.error.invalid_ascii_label": "[需要翻译] Invalid ASCII in A-label", "misc.message.content_8a3872f7": "(一)", "misc.message.content_114151a0": "(二)", "misc.message.content_9bd3ab73": "(三)", "misc.message.content_a2ad69eb": "(四)", "misc.message.content_ac320c36": "(五)", "misc.message.content_e9396efe": "(六)", "misc.message.content_237ab59c": "(七)", "misc.message.content_9e0a69a5": "(八)", "misc.message.content_d181f578": "(九)", "misc.message.content_371326cc": "(十)", "misc.message.content_2fbf7527": "(月)", "misc.message.content_32ee8028": "(火)", "misc.message.content_c013ba7b": "(水)", "misc.message.content_c1a1b32d": "(木)", "misc.message.content_ee40611b": "(金)", "misc.message.content_4c85f31b": "(土)", "misc.message.content_58460266": "(日)", "misc.message.content_ee365730": "(株)", "misc.message.content_bfd48f8c": "(有)", "misc.message.content_efd640ce": "(社)", "misc.message.content_45d5ca11": "(名)", "misc.message.content_fd204428": "(特)", "misc.message.content_d41f6ed4": "(財)", "misc.message.content_b5fc6390": "(祝)", "misc.message.content_95d04567": "(労)", "misc.message.content_dce6706c": "(代)", "misc.message.content_a3d2b82e": "(呼)", "misc.message.content_c33bbd49": "(学)", "misc.message.content_e2e5ec27": "(監)", "misc.message.content_873e119f": "(企)", "misc.message.content_b3e26932": "(資)", "misc.message.content_fbc07b0d": "(協)", "misc.message.content_6bff9c73": "(祭)", "misc.message.content_8b4355ca": "(休)", "misc.message.content_9129b97d": "(自)", "misc.message.content_19ef7eb2": "(至)", "misc.message.content_f544c399": "10月", "misc.message.content_57c44755": "11月", "misc.message.content_c0615eb3": "12月", "misc.message.content_93f3fc6a": "10点", "misc.message.content_1dc940ce": "11点", "misc.message.content_45f501f0": "12点", "misc.message.content_0930af2f": "13点", "misc.message.content_a847f2f1": "14点", "misc.message.content_74bfcf44": "15点", "misc.message.content_fc34bf16": "16点", "misc.message.content_99364b53": "17点", "misc.message.content_cb0b1f64": "18点", "misc.message.content_7b9ba9a7": "19点", "misc.message.content_08f30cfa": "20点", "misc.message.content_6b991e64": "21点", "misc.message.content_1cd7036d": "22点", "misc.message.content_55498996": "23点", "misc.message.content_89c64bb2": "24点", "misc.message.content_684097d0": "株式会社", "misc.message.content_3cf73caf": "10日", "misc.message.content_64c292bb": "11日", "misc.message.content_9a9ef9e3": "12日", "misc.message.content_3f82b314": "13日", "misc.message.content_47232f86": "14日", "misc.message.content_31614668": "15日", "misc.message.content_2d804839": "16日", "misc.message.content_0a29cee5": "17日", "misc.message.content_23755cca": "18日", "misc.message.content_beb70a3e": "19日", "misc.message.content_63644de4": "20日", "misc.message.content_3e7c882f": "21日", "misc.message.content_4cf727d6": "22日", "misc.message.content_aac4f1a5": "23日", "misc.message.content_b1a1717b": "24日", "misc.message.content_0ab4aed8": "25日", "misc.message.content_bf5d2dd6": "26日", "misc.message.content_67a1d703": "27日", "misc.message.content_d7226c7b": "28日", "misc.message.content_de426d0b": "29日", "misc.message.content_76b04464": "30日", "misc.message.content_d52a3de1": "31日", "misc.message.content_f3b9cd0b": "〔本〕", "misc.message.content_e09bcae2": "〔三〕", "misc.message.content_255ee1e8": "〔二〕", "misc.message.content_97f0f4b4": "〔安〕", "misc.message.content_b37164d7": "〔点〕", "misc.message.content_fbfc2a4d": "〔打〕", "misc.message.content_0a6a7e10": "〔盗〕", "misc.message.content_d2548d10": "〔勝〕", "misc.message.content_e4691779": "〔敗〕", "misc.message.invalid_msgpack_format": "[需要翻译] Invalid msgpack format", "misc.error.unpack_failed_incomplete": "[需要翻译] Unpack failed: incomplete input", "misc.message.metadata_field_contains": "[需要翻译] A metadata field contains invalid data.", "misc.message.name_field_contains": "[需要翻译] The name of the field that contains invalid data.", "misc.error.payload_invalid_encoding": "[需要翻译] payload in an invalid encoding", "misc.message.done_managed_parse": "[需要翻译] ve done has managed to parse this, so it", "misc.message.invalid": "[需要翻译] {value!r} is invalid for {{field}}", "misc.message.invalid_1": "[需要翻译] {name!r} is invalid for {{field}}", "misc.message.invalid_2": "[需要翻译] {req!r} is invalid for {{field}}", "misc.message.invalid_3": "[需要翻译] {path!r} is invalid for {{field}}, ", "misc.message.invalid_paths_must": "[需要翻译] {path!r} is invalid for {{field}}, paths must be resolved", "misc.message.invalid_paths_must_1": "[需要翻译] {path!r} is invalid for {{field}}, paths must be relative", "misc.message.invalid_paths_must_2": "[需要翻译] {path!r} is invalid for {{field}}, paths must use '/' delimiter", "misc.error.invalid_metadata": "[需要翻译] invalid metadata", "misc.message.invalid_data": "[需要翻译] {unparsed_key!r} has invalid data", "misc.message.invalid_unparsed_metadata": "[需要翻译] invalid or unparsed metadata", "misc.error.invalid_specifier": "[需要翻译] Invalid specifier: {spec!r}", "misc.message.config_variable_unset": "[需要翻译] Config variable '%s' is unset, Python ABI tag may be incorrect", "misc.error.invalid_sysconfig": "[需要翻译] invalid sysconfig.get_config_var('EXT_SUFFIX')", "misc.error.name_invalid": "[需要翻译] name is invalid: {name!r}", "misc.message.invalid_wheel_filename_2": "[需要翻译] Invalid wheel filename (extension must be '.whl'): {filename!r}", "misc.message.invalid_wheel_filename_3": "[需要翻译] Invalid wheel filename (wrong number of parts): {filename!r}", "misc.error.invalid_project_name": "[需要翻译] Invalid project name: {filename!r}", "misc.message.invalid_wheel_filename_4": "[需要翻译] Invalid wheel filename (invalid version): {filename!r}", "misc.message.invalid_build_number": "[需要翻译] Invalid build number: {build_part} in {filename!r}", "misc.message.invalid_sdist_filename": "[需要翻译] Invalid sdist filename (extension must be '.tar.gz' or '.zip'):", "misc.error.invalid_sdist_filename": "[需要翻译] Invalid sdist filename: {filename!r}", "misc.message.invalid_sdist_filename_1": "[需要翻译] Invalid sdist filename (invalid version): {filename!r}", "misc.error.invalid_version_1": "[需要翻译] Invalid version: {version!r}", "misc.error.invalid_magic": "[需要翻译] invalid magic: {magic!r}", "misc.message.cal_combined_work": "[需要翻译] cal-1.0-combined-work-exception", "misc.message.gpl_autoconf_exception": "[需要翻译] gpl-2.0-with-autoconf-exception", "misc.message.gpl_bison_exception": "[需要翻译] gpl-2.0-with-bison-exception", "misc.message.gpl_classpath_exception": "[需要翻译] gpl-2.0-with-classpath-exception", "misc.message.gpl_font_exception": "[需要翻译] gpl-2.0-with-font-exception", "misc.message.gpl_gcc_exception": "[需要翻译] gpl-2.0-with-gcc-exception", "misc.message.gpl_autoconf_exception_1": "[需要翻译] gpl-3.0-with-autoconf-exception", "misc.message.gpl_gcc_exception_1": "[需要翻译] gpl-3.0-with-gcc-exception", "misc.message.translated_notice": "[需要翻译] latex2e-translated-notice", "misc.message.mpl_copyleft_exception": "[需要翻译] mpl-2.0-no-copyleft-exception", "misc.message.xkeyboard_config_zinoviev": "[需要翻译] xkeyboard-config-z<PERSON>viev", "misc.message.exception": "[需要翻译] 389-exception", "misc.message.asterisk_exception": "[需要翻译] asterisk-exception", "misc.message.asterisk_linking_protocols": "[需要翻译] asterisk-linking-protocols-exception", "misc.message.autoconf_exception": "[需要翻译] autoconf-exception-2.0", "misc.message.autoconf_exception_1": "[需要翻译] autoconf-exception-3.0", "misc.message.autoconf_exception_generic": "[需要翻译] autoconf-exception-generic", "misc.message.autoconf_exception_generic_1": "[需要翻译] autoconf-exception-generic-3.0", "misc.message.autoconf_exception_macro": "[需要翻译] autoconf-exception-macro", "misc.message.bison_exception": "[需要翻译] bison-exception-1.24", "misc.message.bison_exception_1": "[需要翻译] bison-exception-2.2", "misc.message.bootloader_exception": "[需要翻译] bootloader-exception", "misc.message.classpath_exception": "[需要翻译] classpath-exception-2.0", "misc.message.clisp_exception": "[需要翻译] clisp-exception-2.0", "misc.message.cryptsetup_openssl_exception": "[需要翻译] cryptsetup-openssl-exception", "misc.message.digirule_foss_exception": "[需要翻译] digirule-foss-exception", "misc.message.ecos_exception": "[需要翻译] ecos-exception-2.0", "misc.message.erlang_otp_linking": "[需要翻译] erlang-otp-linking-exception", "misc.message.fawkes_runtime_exception": "[需要翻译] fawkes-runtime-exception", "misc.message.fltk_exception": "[需要翻译] fltk-exception", "misc.message.fmt_exception": "[需要翻译] fmt-exception", "misc.message.font_exception": "[需要翻译] font-exception-2.0", "misc.message.freertos_exception": "[需要翻译] freertos-exception-2.0", "misc.message.gcc_exception": "[需要翻译] gcc-exception-2.0", "misc.message.gcc_exception_note": "[需要翻译] gcc-exception-2.0-note", "misc.message.gcc_exception_1": "[需要翻译] gcc-exception-3.1", "misc.message.gmsh_exception": "[需要翻译] gmsh-exception", "misc.message.gnat_exception": "[需要翻译] gnat-exception", "misc.message.gnome_examples_exception": "[需要翻译] gnome-examples-exception", "misc.message.gnu_compiler_exception": "[需要翻译] gnu-compiler-exception", "misc.message.gnu_javamail_exception": "[需要翻译] gnu-javamail-exception", "misc.message.gpl_interface_exception": "[需要翻译] gpl-3.0-interface-exception", "misc.message.gpl_linking_exception": "[需要翻译] gpl-3.0-linking-exception", "misc.message.gpl_linking_source": "[需要翻译] gpl-3.0-linking-source-exception", "misc.message.gstreamer_exception": "[需要翻译] gstreamer-exception-2005", "misc.message.gstreamer_exception_1": "[需要翻译] gstreamer-exception-2008", "misc.message.gpl_java_exception": "[需要翻译] i2p-gpl-java-exception", "misc.message.kicad_libraries_exception": "[需要翻译] kicad-libraries-exception", "misc.message.lgpl_linking_exception": "[需要翻译] lgpl-3.0-linking-exception", "misc.message.libpri_exception": "[需要翻译] libpri-openh323-exception", "misc.message.libtool_exception": "[需要翻译] libtool-exception", "misc.message.llvm_exception": "[需要翻译] llvm-exception", "misc.message.lzma_exception": "[需要翻译] lzma-exception", "misc.message.mif_exception": "[需要翻译] mif-exception", "misc.message.nokia_exception": "[需要翻译] nokia-qt-exception-1.1", "misc.message.ocaml_lgpl_linking": "[需要翻译] ocaml-lgpl-linking-exception", "misc.message.occt_exception": "[需要翻译] occt-exception-1.0", "misc.message.openjdk_assembly_exception": "[需要翻译] openjdk-assembly-exception-1.0", "misc.message.openvpn_openssl_exception": "[需要翻译] openvpn-openssl-exception", "misc.message.exception_1": "[需要翻译] pcre2-exception", "misc.message.pdf_font_exception": "[需要翻译] ps-or-pdf-font-exception-20170817", "misc.message.qpl_inria_exception": "[需要翻译] qpl-1.0-inria-2004-exception", "misc.message.gpl_exception": "[需要翻译] qt-gpl-exception-1.0", "misc.message.lgpl_exception": "[需要翻译] qt-lgpl-exception-1.1", "misc.message.qwt_exception": "[需要翻译] qwt-exception-1.0", "misc.message.romic_exception": "[需要翻译] romic-exception", "misc.message.rrdtool_floss_exception": "[需要翻译] rrdtool-floss-exception-2.0", "misc.message.sane_exception": "[需要翻译] sane-exception", "misc.message.stunnel_exception": "[需要翻译] stunnel-exception", "misc.message.swi_exception": "[需要翻译] swi-exception", "misc.message.swift_exception": "[需要翻译] swift-exception", "misc.message.texinfo_exception": "[需要翻译] texinfo-exception", "misc.message.boot_exception": "[需要翻译] u-boot-exception-2.0", "misc.message.ubdl_exception": "[需要翻译] ubdl-exception", "misc.message.universal_foss_exception": "[需要翻译] universal-foss-exception-1.0", "misc.message.vsftpd_openssl_exception": "[需要翻译] vsftpd-openssl-exception", "misc.message.wxwindows_exception": "[需要翻译] wxwindows-exception-3.1", "misc.message.openssl_exception": "[需要翻译] x11vnc-openssl-exception", "misc.message.invalid_license_expression": "[需要翻译] Invalid license expression: {raw_license_expression!r}", "misc.message.unknown_license_exception": "[需要翻译] Unknown license exception: {token!r}", "misc.message.invalid_licenseref": "[需要翻译] Invalid licenseref: {final_token!r}", "misc.message.requested_distribution_found": "[需要翻译] A requested distribution was not found", "misc.message.distribution_found": "[需要翻译] The '{self.req}' distribution was not found ", "misc.message.give_error_message": "[需要翻译] Give an error message for problems extracting file(s)", "misc.message.script_found_metadata": "[需要翻译] Script {script!r} not found in metadata at {self.egg_info!r}", "misc.message.base_parameter_none": "[需要翻译] `base` parameter in `_fn` is `None`. Either override this method or check the parameter first.", "misc.error.invalid_module_name": "[需要翻译] Invalid module name", "misc.error.invalid_group_name": "[需要翻译] Invalid group name", "misc.message.missing_version_header": "[需要翻译] Missing 'Version:' header and/or {} file at path: {}", "misc.error.entry_point_found": "[需要翻译] Entry point %r not found", "misc.error.invalid_section_heading": "[需要翻译] Invalid section heading", "misc.message.return_config_directory": "[需要翻译] :return: config directory shared by the users, same as `user_config_dir`", "misc.message.return_config_directory_1": "[需要翻译] :return: config directory tied to the user", "misc.message.return_config_directory_2": "[需要翻译] :return: config directory shared by the users", "misc.message.return_config_path": "[需要翻译] :return: config path tied to the user", "misc.message.return_config_path_1": "[需要翻译] :return: config path shared by the users", "misc.message.yield_user_site": "[需要翻译] :yield: all user and site configuration directories.", "misc.message.yield_user_site_1": "[需要翻译] :yield: all user and site configuration paths.", "misc.message.return_config_directory_3": "[需要翻译] :return: config directory tied to the user, same as `user_data_dir`", "misc.message.return_config_directory_4": "[需要翻译] :return: config directory shared by the users, same as `site_data_dir`", "misc.message.config_7": "[需要翻译] ~/.config", "misc.message.return_config_path_2": "[需要翻译] :return: config path shared by the users, returns the first item, even if ``multipath`` is set to ``True``", "misc.error.invalid_state_name": "[需要翻译] invalid state name {state!r}", "misc.error.invalid_argument_pygmentsdoc": "[需要翻译] invalid argument for \"pygmentsdoc\" directive", "misc.debug.warning_does_docstring": "[需要翻译] Warning: {classname} does not have a docstring.", "misc.error.missing_docstring": "[需要翻译] Missing docstring for {module}", "misc.error.value_option_must": "[需要翻译] Value for option {} must be one of {}", "misc.error.invalid_type_option": "[需要翻译] Invalid type {string!r} for option {optname}; use ", "misc.error.invalid_value_option": "[需要翻译] Invalid value {string!r} for option {optname}; use ", "misc.error.invalid_type_option_1": "[需要翻译] Invalid type {string!r} for option {optname}; you ", "misc.error.invalid_value_option_1": "[需要翻译] Invalid value {string!r} for option {optname}; you ", "misc.error.invalid_type_option_2": "[需要翻译] Invalid type {val!r} for option {optname}; you ", "misc.message.lookup_filter_name": "[需要翻译] Lookup a filter by name. Return None if not found.", "misc.error.filter_found": "[需要翻译] filter {filtername!r} not found", "misc.error.excclass_option_exception": "[需要翻译] excclass option is not an exception class", "misc.message.format_tokens_html": "[需要翻译] Format tokens as HTML 4 ``<span>`` tags. By default, the content is enclosed in a ``<pre>`` tag, itself wrapped in a ``<div>`` tag (but see the `nowrap` option). The ``<div>``'s CSS class can be set by the `cssclass` option.", "misc.error.error_when_loading": "[需要翻译] error when loading custom formatter: {err}", "misc.message.detect_syntax_error": "[需要翻译] t detect syntax error. But it", "misc.message.during_handling_above": "[需要翻译] ^During handling of the above exception, another ", "misc.message.exception_occurred": "[需要翻译] exception occurred:\\n\\n", "misc.message.above_exception_direct": "[需要翻译] ^The above exception was the direct cause of the ", "misc.message.following_exception": "[需要翻译] following exception:\\n\\n", "misc.message.error_exception_warning": "[需要翻译] (?<!\\.)[A-Z]\\w*(Error|Exception|Warning)'*(?!['\\w])", "misc.message.exception_generatorexit_keyboa": "[需要翻译] (?<!\\.)(Exception|GeneratorExit|KeyboardInterrupt|StopIteration|", "misc.message.text_dockerfile_config": "[需要翻译] text/x-dockerfile-config", "misc.message.linux_config": "[需要翻译] linux-config", "misc.message.kernel_config": "[需要翻译] kernel-config", "misc.message.config_8": "[需要翻译] *Config.in*", "misc.message.ldap_configuration_file": "[需要翻译] LDAP configuration file", "misc.message.lighttpd_configuration_file": "[需要翻译] Lighttpd configuration file", "misc.message.nginx_configuration_file": "[需要翻译] Nginx configuration file", "misc.message.unix_linux_config": "[需要翻译] Unix/Linux config files", "misc.error.error_when_loading_1": "[需要翻译] error when loading custom lexer: {err}", "misc.message.error_while_importing": "[需要翻译] Error while importing backend", "misc.message.raised_missing_hooks": "[需要翻译] Will be raised on missing hooks (if a fallback can't be used).", "misc.message.raised_hook_missing": "[需要翻译] Raised if a hook is missing and we are not executing the fallback", "misc.message.nothing_should_ever": "[需要翻译] Nothing should ever raise this exception", "misc.error.missing_dependencies_socks": "[需要翻译] Missing dependencies for SOCKS support.", "misc.message.invalid_path": "[需要翻译] invalid path: {cert_loc}", "misc.message.invalid_path_1": "[需要翻译] invalid path: {conn.cert_file}", "misc.message.could_find_tls": "[需要翻译] Could not find the TLS key file, invalid path: {conn.key_file}", "misc.message.could_missing_host": "[需要翻译] and could be missing the host.", "misc.message.invalid_timeout_pass": "[需要翻译] Invalid timeout {timeout}. Pass a (connect, read) timeout tuple, ", "misc.message.json_error_occurred": "[需要翻译] A JSON error occurred.", "misc.message.http_error_occurred": "[需要翻译] An HTTP error occurred.", "misc.message.connection_error_occurred": "[需要翻译] A Connection error occurred.", "misc.message.proxy_error_occurred": "[需要翻译] A proxy error occurred.", "misc.message.ssl_error_occurred": "[需要翻译] An SSL error occurred.", "misc.message.url_scheme_http": "[需要翻译] The URL scheme (e.g. http or https) is missing.", "misc.message.url_scheme_provided": "[需要翻译] The URL scheme provided is either invalid or unsupported.", "misc.message.url_provided_somehow": "[需要翻译] The URL provided was somehow invalid.", "misc.message.header_value_provided": "[需要翻译] The header value provided was somehow invalid.", "misc.message.proxy_url_provided": "[需要翻译] The proxy URL provided is invalid.", "misc.message.server_declared_chunked": "[需要翻译] The server declared chunked encoding but sent an invalid chunk.", "misc.message.failed_decode_response": "[需要翻译] Failed to decode response content.", "misc.message.custom_retries_logic": "[需要翻译] Custom retries logic failed", "misc.message.requests_encountered_error": "[需要翻译] Requests encountered an error when trying to rewind a body.", "misc.message.base_warning_requests": "[需要翻译] Base warning for Requests.", "misc.message.invalid_url_scheme": "[需要翻译] Invalid URL {url!r}: No scheme supplied. ", "misc.error.invalid_url_host": "[需要翻译] Invalid URL {url!r}: No host supplied", "misc.error.url_invalid_label": "[需要翻译] URL has an invalid label.", "misc.message.found": "[需要翻译] Not Found", "misc.message.client_error_url_1": "[需要翻译] {self.status_code} Client Error: {reason} for url: {self.url}", "misc.message.server_error_url_1": "[需要翻译] {self.status_code} Server Error: {reason} for url: {self.url}", "misc.message.warning_should_only": "[需要翻译]  warning should only appear once.)", "misc.error.invalid_percent_escape": "[需要翻译] Invalid percent-escape sequence: '{h}'", "misc.message.invalid_leading_whitespace": "[需要翻译] Invalid leading whitespace, reserved character(s), or return ", "misc.message.error_occurred_when": "[需要翻译] An error occurred when rewinding request body for redirect.", "misc.message.invalid_value_align": "[需要翻译] invalid value for align, expected \"left\", \"center\", or \"right\" (not {align!r})", "misc.message.invalid_value_vertical": "[需要翻译] invalid value for vertical, expected \"top\", \"middle\", or \"bottom\" (not {vertical!r})", "misc.message.content_af9b987c": "这是对亚洲语言支持的测试。面对模棱两可的想法，拒绝猜测的诱惑。", "misc.message.error_capture_context": "[需要翻译] An error in the Capture context manager.", "misc.message.failed_style": "[需要翻译] Failed to get style {name!r}; {error}", "misc.message.may_need_add": "[需要翻译] {error.reason}\\n*** You may need to add PYTHONIOENCODING=utf-8 to your environment ***", "misc.message.loading": "[需要翻译] 🚀 Loading", "misc.message.logging_level_warning": "[需要翻译] logging.level.warning", "misc.message.logging_level_error": "[需要翻译] logging.level.error", "misc.message.prompt_invalid_choice": "[需要翻译] prompt.invalid.choice", "misc.message.error_console_operation": "[需要翻译] An error in console operation.", "misc.message.error_styles": "[需要翻译] An error in styles.", "misc.message.style_stack_invalid": "[需要翻译] Style stack is invalid.", "misc.message.error_related_live": "[需要翻译] Error related to Live display.", "misc.debug.unable_read": "[需要翻译] Unable to read {args.path!r}; {error}", "misc.message.layout_related_error": "[需要翻译] Layout related error.", "misc.message.loading_configuration_file": "[需要翻译] Loading configuration file /adasd/asdasd/qeqwe/qwrqwrqwr/sdgsdgsdg/werwerwer/dfgerert/ertertert/ertetert/werwerwer", "misc.info.error_some_kind": "[需要翻译] An error of some kind occurred!", "misc.message.error_parsing_1": "[需要翻译] error parsing {parameters!r} in {open_tag.parameters!r}; {error.msg}", "misc.message.error_parsing_2": "[需要翻译] error parsing {open_tag.parameters!r}; {error}", "misc.message.warning_emoji_bold": "[需要翻译] :warning-emoji: [bold red blink] DANGER![/]", "misc.message.repr_error": "[需要翻译] <repr-error {str(error)!r}>", "misc.message.renders_completed_filesize": "[需要翻译] Renders completed filesize.", "misc.message.show_data_completed": "[需要翻译] Show data completed.", "misc.message.show_completed_total": "[需要翻译] Show completed/total.", "misc.message.content_61f5f6ca": "[需要翻译] {completed:{total_width}d}{self.separator}{total}", "misc.message.calculate_common_unit": "[需要翻译] Calculate common unit for completed and total.", "misc.message.number_steps_completed": "[需要翻译] Number of steps completed.", "misc.message.float_number_steps": "[需要翻译] float: Number of steps completed", "misc.message.float_time_task": "[需要翻译] float: Time task was finished.", "misc.message.optional_float_last": "[需要翻译] Optional[float]: The last speed for a finished task.", "misc.message.check_task_finished": "[需要翻译] Check if the task has finished.", "misc.message.check_tasks_completed": "[需要翻译] Check if all tasks have been completed.", "misc.error.invalid_mode": "[需要翻译] invalid mode {mode!r}", "misc.message.green_processing": "[需要翻译] [green]Processing", "misc.message.bar": "[需要翻译] <Bar {self.completed!r} of {self.total!r}>", "misc.message.exception_base_class": "[需要翻译] Exception base class for prompt related errors.", "misc.message.prompt_invalid_please": "[需要翻译] [prompt.invalid]Please enter a valid value", "misc.message.prompt_invalid_choice_1": "[需要翻译] [prompt.invalid.choice]Please select one of the available options", "misc.message.prompt_invalid_please_1": "[需要翻译] [prompt.invalid]Please enter a valid integer number", "misc.message.prompt_invalid_please_2": "[需要翻译] [prompt.invalid]Please enter a number", "misc.message.prompt_invalid_please_3": "[需要翻译] [prompt.invalid]Please enter Y or N", "misc.debug.prompt_invalid_number": "[需要翻译] :pile_of_poo: [prompt.invalid]Number must be between 1 and 10", "misc.debug.prompt_invalid_password": "[需要翻译] [prompt.invalid]password too short", "misc.message.error_occurred_when_1": "[需要翻译] An error occurred when attempting to build a repr.", "misc.message.failed_auto_generate": "[需要翻译] Failed to auto generate __rich_repr__; {error}", "misc.message.invalid_value_align_1": "[需要翻译] invalid value for align, expected \"left\", \"center\", \"right\" (not {align!r})", "misc.message.segment_last_step": "[需要翻译] A Segment is the last step in the Rich render process before generating text with ANSI codes.", "misc.message.unable_parse_background": "[需要翻译] unable to parse {word!r} as background color; {error}", "misc.message.unable_parse_color": "[需要翻译] unable to parse {word!r} as color; {error}", "misc.message.pygments_lexer_specified": "[需要翻译] A Pygments <PERSON><PERSON> to use if one is not specified or invalid.", "misc.message.setting_non_none": "[需要翻译] Setting a non-None self.width implies expand.", "misc.message.contents_config_file": "[需要翻译] Get contents of a config file for this theme.", "misc.message.base_exception_errors": "[需要翻译] Base exception for errors related to the theme stack.", "misc.message.exception_str_failed": "[需要翻译] <exception str() failed>", "misc.message.sub_exception": "[需要翻译] Sub-exception #{group_no}", "misc.message.above_exception_direct_1": "[需要翻译] \\n[i]The above exception was the direct cause of the following exception:\\n", "misc.message.during_handling_above_1": "[需要翻译] \\n[i]During handling of the above exception, another exception occurred:\\n", "misc.message.content_69ed96ce": "[需要翻译] \\n{error}", "misc.message.attribute_any_exception": "[需要翻译] Get attribute or any exception.", "misc.debug.content_a7f2ff4c": "TextualはPythonの高速アプリケーション開発フレームワークです", "misc.debug.content_6b57e283": "アプリケーションは1670万色を使用でき", "misc.message.content_4746014b": ":flag_for_china:  该库支持中文，日文和韩文文本！\\n:flag_for_japan:  ライブラリは中国語、日本語、韓国語のテキストをサポートしています\\n:flag_for_south_korea:  이 라이브러리는 중국어, 일본어 및 한국어 텍스트를 지원합니다", "misc.error.invalid_statement": "[需要翻译] Invalid statement", "misc.error.found_invalid_character": "[需要翻译] Found invalid character {src[pos]!r}", "misc.error.invalid_initial_character": "[需要翻译] Invalid initial character for a key part", "misc.error.invalid_hex_value": "[需要翻译] Invalid hex value", "misc.error.invalid_date_datetime": "[需要翻译] Invalid date or datetime", "misc.error.invalid_value_2": "[需要翻译] Invalid value", "misc.message.invalid_mapping_key": "[需要翻译] Invalid mapping key '{part}' of type '{type(part).__qualname__}'.", "misc.error.library_failed_load": "[需要翻译] The library {name} failed to load", "misc.error.error_initializing_ctypes": "[需要翻译] Error initializing ctypes: {e}", "misc.error.error_copying_string": "[需要翻译] Error copying C string from CFStringRef", "misc.message.invalid_trust_result": "[需要翻译] Invalid trust result type", "misc.message.recoverable_trust_failure": "[需要翻译] Recoverable trust failure occurred", "misc.message.fatal_trust_failure": "[需要翻译] Fatal trust failure occurred", "misc.message.other_error_occurred": "[需要翻译] Other error occurred, certificate may be revoked", "misc.message.certificate_verification_faile": "[需要翻译] Certificate verification failed", "misc.message.certificate_chain_policy": "[需要翻译] Certificate chain policy error {error_code:#x} [{policy_status.lElementIndex}]", "misc.message.connection_timed_connect": "[需要翻译] Connection to %s timed out. (connect timeout=%s)", "misc.message.failed_establish_connection": "[需要翻译] Failed to establish a new connection: %s", "misc.message.used_detect_failed": "[需要翻译] Used to detect a failed ConnectionCls import.", "misc.message.helper_always_returns": "[需要翻译] Helper that always returns a :class:`urllib3.util.Timeout`", "misc.message.error_actually_timeout": "[需要翻译] Is the error actually a timeout? Will raise a ReadTimeout or pass", "misc.message.read_timed_read": "[需要翻译] Read timed out. (read timeout=%s)", "misc.message.failed_parse_headers": "[需要翻译] Failed to parse headers (url=%s): %s", "misc.message.record_layer_failure": "[需要翻译] record layer failure", "misc.message.https_proxy_error": "[需要翻译] #https-proxy-error-http-proxy", "misc.message.unverified_https_connection": "[需要翻译] Unverified HTTPS connection done to an HTTPS proxy. ", "misc.message.base_exception_used": "[需要翻译] Base exception used by this module.", "misc.message.base_warning_used": "[需要翻译] Base warning used by this module.", "misc.message.base_exception_errors_1": "[需要翻译] Base exception for errors caused within a pool.", "misc.message.base_exception_poolerrors": "[需要翻译] Base exception for PoolErrors that have associated URLs.", "misc.message.raised_when_passing": "[需要翻译] Raised when passing an invalid state to a timeout", "misc.message.raised_when_socket": "[需要翻译] Raised when a socket timeout occurs while receiving data from a server", "misc.message.raised_when_socket_1": "[需要翻译] Raised when a socket timeout occurs while connecting to a server", "misc.message.failed_parse": "[需要翻译] Failed to parse: %s", "misc.message.used_container_error": "[需要翻译] Used as a container for an error reason supplied in a MaxRetryError.", "misc.message.many_error_responses": "[需要翻译] too many error responses", "misc.message.many_error_responses_1": "[需要翻译] too many {status_code} error responses", "misc.message.warned_when_connecting": "[需要翻译] Warned when connecting to a host with a certificate missing a SAN.", "misc.message.warned_when_certain": "[需要翻译] Warned when certain TLS/SSL configuration is not available on a platform.", "misc.message.invalid_chunk_length": "[需要翻译] Invalid chunk length in a chunked response.", "misc.message.header_provided_somehow": "[需要翻译] The header provided was somehow invalid.", "misc.message.raised_convert_log": "[需要翻译] Raised by assert_header_parsing, but we convert it to a log.warning statement.", "misc.message.encountered_error_when": "[需要翻译] urllib3 encountered an error when trying to rewind a body", "misc.message.failed_decode": "[需要翻译] failed to decode it.", "misc.message.header_transfer_encoding": "[需要翻译] Header 'transfer-encoding: chunked' is missing.", "misc.message.urlfetch_does_support": "[需要翻译] URLFetch does not support granular timeout settings, ", "misc.message.reverting_total_default": "[需要翻译] reverting to total or default URLFetch timeout.", "misc.message.cryptography_module_missing": "[需要翻译] 'cryptography' module missing required functionality.  ", "misc.message.pyopenssl_module_missing": "[需要翻译] 'pyOpenSSL' module missing required functionality. ", "misc.message.affect_certificate_validation": "[需要翻译] affect certificate validation. The error was %s", "misc.message.read_error": "[需要翻译] read error: %r", "misc.message.error_code": "[需要翻译] error code: %d", "misc.message.exception_2": "[需要翻译] exception: %r", "misc.message.certificate_verify_failed_1": "[需要翻译] certificate verify failed, %s", "misc.message.failed_copy_trust": "[需要翻译] Failed to copy trust reference", "misc.message.library_failed_load": "[需要翻译] The library %s failed to load", "misc.error.error_initializing_ctypes_1": "[需要翻译] Error initializing ctypes", "misc.message.lazy_loading_moved": "[需要翻译] Lazy loading of moved objects", "misc.message.lazy_loading_moved_1": "[需要翻译] Lazy loading of moved objects in six.moves.urllib_parse", "misc.message.lazy_loading_moved_2": "[需要翻译] Lazy loading of moved objects in six.moves.urllib_error", "misc.message.moves_urllib_error": "[需要翻译] .moves.urllib.error", "misc.message.moves_urllib_error_1": "[需要翻译] moves.urllib.error", "misc.message.lazy_loading_moved_3": "[需要翻译] Lazy loading of moved objects in six.moves.urllib_request", "misc.message.lazy_loading_moved_4": "[需要翻译] Lazy loading of moved objects in six.moves.urllib_response", "misc.message.lazy_loading_moved_5": "[需要翻译] Lazy loading of moved objects in six.moves.urllib_robotparser", "misc.error.invalid_keyword_arguments": "[需要翻译] invalid keyword arguments to print()", "misc.message.reraise_exception": "[需要翻译] Reraise an exception.", "misc.error.invalid_mode_only": "[需要翻译] invalid mode %r (only r, w, b allowed)", "misc.message.error_occurred_when_2": "[需要翻译] An error occurred when rewinding request body for redirect/retry.", "misc.error.invalid_retry_after": "[需要翻译] Invalid Retry-After header: %s", "misc.error.fingerprint_invalid_length": "[需要翻译] Fingerprint of invalid length: {0}", "misc.message.timeout_cannot_boolean": "[需要翻译] Timeout cannot be a boolean value. It must ", "misc.message.timeout_value_must": "[需要翻译] Timeout value %s was %s, but it must be an ", "misc.message.attempted_set_timeout": "[需要翻译] Attempted to set %s timeout to %s, but the ", "misc.message.timeout_cannot_set": "[需要翻译] timeout cannot be set to a value less ", "misc.error.timeout_timer_already": "[需要翻译] Timeout timer has already been started.", "misc.message.percent_encodes_request": "[需要翻译] Percent-encodes a request target so that there are no invalid characters", "misc.message.warning_consult_packager": "[需要翻译] this warning, consult the packager of your ", "misc.message.config_appname": "[需要翻译] ~/.config/<AppName>", "misc.message.base_exception_class": "[需要翻译] base exception class for all parsing runtime exceptions", "misc.message.exception_thrown_grammar": "[需要翻译] exception thrown by L{ParserElement.validate} if the grammar could be improperly recursive", "misc.debug.exception_raised": "[需要翻译] Exception raised:", "misc.message.failed_user_defined": "[需要翻译] failed user-defined condition", "misc.message.exception_raised_user": "[需要翻译] Exception raised in user parse action:", "misc.message.exception_raised": "[需要翻译] Exception raised", "misc.debug.failed": "[需要翻译] Failed!", "misc.message.fail_exception": "[需要翻译] FAIL-EXCEPTION: ", "misc.message.invalid_pattern_passed": "[需要翻译] invalid pattern (%s) passed to Regex", "misc.error.missing_more_required": "[需要翻译] Missing one or more required elements (%s)", "misc.message.spelling_error_login": "[需要翻译] Spelling error on Login ('log|n')", "misc.message.leaving_exception": "[需要翻译] <<leaving %s (exception: %s)\\n", "misc.message.invalid_argument_oneof": "[需要翻译] Invalid argument to oneOf, expected string or iterable", "misc.message.exception_creating_regex": "[需要翻译] Exception creating Regex for oneOf, building MatchFirst", "misc.message.invalid_marker_parse": "[需要翻译] Invalid marker: {0!r}, parse error at {1!r}", "misc.message.parse_error": "[需要翻译] Parse error at \"{0!r}\": {1}", "misc.error.invalid_url_given": "[需要翻译] Invalid URL given", "misc.error.invalid_url_1": "[需要翻译] Invalid URL: {0}", "misc.error.invalid_specifier_1": "[需要翻译] Invalid specifier: '{0}'", "misc.error.invalid_version_2": "[需要翻译] Invalid version: '{0}'", "misc.message.configuration_file_does": "[需要翻译] Configuration file %s does not exist.", "misc.message.handles_metadata_supplied": "[需要翻译] Handles metadata supplied in configuration files.", "misc.message.unable_parse_option": "[需要翻译] Unable to parse option value to dict: %s", "misc.message.unsupported_distribution_optio": "[需要翻译] Unsupported distribution option section: [%s.%s]", "misc.message.requires_parameter_deprecated": "[需要翻译] The requires parameter is deprecated, please use ", "misc.message.warning_declared_package": "[需要翻译] WARNING: %r is declared as a package namespace, but %r", "misc.error.invalid_environment_marker": "[需要翻译] Invalid environment marker: ", "misc.message.containing_valid_project": "[需要翻译] containing valid project/version requirement specifiers; {error}", "misc.message.containing_valid_version": "[需要翻译] containing valid version specifiers; {error}", "misc.message.warning_valid_package": "[需要翻译] WARNING: %r not a valid package name; please use only ", "misc.message.version_specified_invalid": "[需要翻译] The version specified (%r) is an invalid version, this ", "misc.message.setting_options_command": "[需要翻译]   setting options for '%s' command:", "misc.message.error_command_such": "[需要翻译] error in %s: command '%s' has no such option '%s'", "misc.message.setting_must_list": "[需要翻译] %s: setting must be a list or tuple (%r)", "misc.message.such_distribution_setting": "[需要翻译] %s: No such distribution setting", "misc.message.setting_cannot_changed": "[需要翻译] : this setting cannot be changed via include/exclude", "misc.message.setting_must_list_1": "[需要翻译] %s: setting must be a list (%r)", "misc.message.packages_setting_must": "[需要翻译] packages: setting must be a list or tuple (%r)", "misc.message.ensure_find_links": "[需要翻译] Ensure find-links option end-up being a list of strings.", "misc.message.warning_wheel_package": "[需要翻译] WARNING: The wheel package is not available.", "misc.error.allow_hosts_option": "[需要翻译] the `allow-hosts` option is not supported ", "misc.message.error_executing": "[需要翻译] Error executing {}", "misc.message.microsoft_visual_directory": "[需要翻译] Microsoft Visual C++ directory not found", "misc.message.download_error_some": "[需要翻译] Download error on %s: %%s -- Some packages may not be found!", "misc.message.authentication_error": "[需要翻译] Authentication error: %s", "misc.message.validating_checksum": "[需要翻译] Validating %%s checksum for %s", "misc.message.validation_failed": "[需要翻译] %s validation failed for %s; ", "misc.error.download_error": "[需要翻译] Download error for %s: %s", "misc.message.path_found": "[需要翻译] Path not found", "misc.message.restore_raise_any": "[需要翻译] restore and re-raise any exception", "misc.error.invalid_wheel_name": "[需要翻译] invalid wheel name: %r", "misc.error.unsupported_wheel_format": "[需要翻译] unsupported wheel format. .dist-info not found", "misc.message.global_config": "[需要翻译] --global-config ", "misc.message.user_config": "[需要翻译] --user-config ", "misc.message.warning_depends_txt": "[需要翻译] WARNING: 'depends.txt' will not be used by setuptools 0.6!\\n", "misc.message.setting_either_true": "[需要翻译]  setting (either True or False) in the package's setup.py", "misc.message.libraries_option_library": "[需要翻译] in 'libraries' option (library '%s'), ", "misc.info.warning_user_site": "[需要翻译] WARNING: The user site-packages directory is disabled.", "misc.message.warning_command_deprecated": "[需要翻译] WARNING: The easy_install command is deprecated ", "misc.info.test_failed_does": "[需要翻译] TEST FAILED: %s does NOT support .pth files", "misc.message.invalid_argument_filenames": "[需要翻译] Invalid argument %r: you can't use filenames or URLs ", "misc.message.editable_except_via": "[需要翻译] with --editable (except via the --find-links option).", "misc.info.processing_dependencies": "[需要翻译] Processing dependencies for %s", "misc.info.finished_processing_dependenci": "[需要翻译] Finished processing dependencies for %s", "misc.info.warning_process": "[需要翻译] WARNING: can't process %s", "misc.info.chmod_failed": "[需要翻译] chmod failed: %s", "misc.message.invalid_distribution_name": "[需要翻译] Invalid distribution name or version syntax: %s-%s", "misc.message.warning_files_found": "[需要翻译] warning: no files found matching '%s'", "misc.message.warning_previously_included": "[需要翻译] warning: no previously-included files found ", "misc.message.warning_previously_included_1": "[需要翻译] warning: no previously-included files matching ", "misc.message.warning_directories_found": "[需要翻译] warning: no directories found matching '%s'", "misc.message.cannot_happen_invalid": "[需要翻译] this cannot happen: invalid action '{action!s}'", "misc.message.standard_file_found": "[需要翻译] standard file .*not found", "misc.message.warning_depends_txt_1": "[需要翻译] WARNING: 'depends.txt' is not used by setuptools 0.6!\\n", "misc.message.deprecated_behavior_warning": "[需要翻译] Deprecated behavior warning for EggInfo, bypassing suppression.", "misc.message.standard_file_found_1": "[需要翻译] standard file not found: should have one of ", "misc.message.standard_file_found_2": "[需要翻译] standard file '%s' not found", "misc.message.save_supplied_options": "[需要翻译] save supplied options to setup.cfg or other config file", "misc.info.warning_option_malformed": "[需要翻译] warning: 'license_files' option is malformed", "misc.message.warning_option_deprecated": "[需要翻译] warning: the 'license_file' option is deprecated, ", "misc.message.notice": "[需要翻译] NOTICE*", "misc.info.reading_configuration": "[需要翻译] Reading configuration from %s", "misc.message.setting": "[需要翻译] Setting %s.%s to %r in %s", "misc.message.abstract_base_class": "[需要翻译] Abstract base class for commands that mess with config files", "misc.message.global_config_1": "[需要翻译] global-config", "misc.message.user_config_1": "[需要翻译] user-config", "misc.message.configuration_file_default": "[需要翻译] configuration file to use (default=setup.cfg)", "misc.message.must_specify_only": "[需要翻译] Must specify only one configuration file option", "misc.message.set_option_setup": "[需要翻译] set an option in setup.cfg or another config file", "misc.message.command_set_option": "[需要翻译] command to set an option for", "misc.message.option_1": "[需要翻译] option=", "misc.message.option_set": "[需要翻译] option to set", "misc.message.value_option": "[需要翻译] value of the option", "misc.error.must_specify_command": "[需要翻译] Must specify --command *and* --option", "misc.message.warning_testing_via": "[需要翻译] WARNING: Testing via this command is deprecated and will be ", "misc.message.test_failed": "[需要翻译] Test failed: %s", "misc.message.upload_successful_visit": "[需要翻译] Upload successful. Visit %s", "misc.message.upload_failed": "[需要翻译] Upload failed (%s): %s", "misc.error.invalid_macro_definition": "[需要翻译] invalid macro definition '%s': ", "misc.message.warning_2": "[需要翻译] warning: %s\\n", "misc.error.error_option": "[需要翻译] error in '%s' option: ", "misc.message.ensure_option_name": "[需要翻译] Ensure that 'option' is the name of an existing file.", "misc.info.warning": "[需要翻译] warning: %s: %s\\n", "misc.message.generating": "[需要翻译] generating %s from %s", "misc.error.error_setup_command": "[需要翻译] error in setup command: %s", "misc.error.error_setup_command_1": "[需要翻译] error in %s setup command: %s", "misc.debug.options_after_parsing": "[需要翻译] options (after parsing config files):", "misc.message.error_7": "[需要翻译] error: %s\\n", "misc.error.error": "[需要翻译] error: %s", "misc.error.invalid_value_3": "[需要翻译] invalid value for 'stop_after': %r", "misc.message.error_listing_files": "[需要翻译] error listing files in '%s': %s", "misc.info.error_removing": "[需要翻译] error removing %s: %s", "misc.message.warning_should_list": "[需要翻译] Warning: '{fieldname}' should be a list, got type '{typename}'", "misc.message.licence_distribution_option": "[需要翻译] 'licence' distribution option is deprecated; use 'license'", "misc.message.unknown_distribution_option": "[需要翻译] Unknown distribution option: %s", "misc.message.option_dictionary_hasn": "[需要翻译] s option dictionary hasn", "misc.message.option_dict_command": "[需要翻译] no option dict for '%s' command", "misc.message.option_dict_command_1": "[需要翻译] option dict for '%s' command:", "misc.message.using_config_files": "[需要翻译] using config files: %s", "misc.error.invalid_command_name": "[需要翻译] invalid command name '%s'", "misc.message.invalid_help_function": "[需要翻译] invalid help function %r for help option '%s': ", "misc.message.invalid_command_class": "[需要翻译] invalid command '%s' (no class '%s' in module '%s')", "misc.error.invalid_command": "[需要翻译] invalid command '%s'", "misc.message.option_table_provided": "[需要翻译] The option table provided to 'fancy_getopt()' is bogus.", "misc.message.syntax_error_file": "[需要翻译] Syntax error in a file list template.", "misc.message.byte_compile_error": "[需要翻译] Byte compile error.", "misc.message.some_compile_link": "[需要翻译] Some compile/link operation failed.", "misc.message.failure_preprocess_more": "[需要翻译] Failure to preprocess one or more C/C++ files.", "misc.message.failure_compile_more": "[需要翻译] Failure to compile one or more C/C++ source files.", "misc.message.option_conflict_already": "[需要翻译] option conflict: already an option '%s'", "misc.error.invalid": "[需要翻译] invalid %s '%s': ", "misc.message.option_defined": "[需要翻译] option '%s' not defined", "misc.message.aliased_option_defined": "[需要翻译] aliased option '%s' not defined", "misc.message.set_aliases_option": "[需要翻译] Set the aliases for this option parser.", "misc.error.invalid_option_tuple": "[需要翻译] invalid option tuple: %r", "misc.error.invalid_long_option": "[需要翻译] invalid long option '%s': ", "misc.error.invalid_short_option": "[需要翻译] invalid short option '%s': ", "misc.message.invalid_negative_alias": "[需要翻译] invalid negative alias '%s': ", "misc.message.aliased_option_takes": "[需要翻译] aliased option '%s' takes a value", "misc.message.invalid_alias_inconsistent": "[需要翻译] invalid alias '%s': inconsistent with ", "misc.message.aliased_option_them": "[需要翻译] aliased option '%s' (one of them takes a value, ", "misc.message.invalid_long_option": "[需要翻译] invalid long option name '%s' ", "misc.error.boolean_option_value": "[需要翻译] boolean option can't have value", "misc.message.option_summary": "[需要翻译] Option summary:", "misc.info.warning_previously_included": "[需要翻译] warning: no previously-included files ", "misc.message.cannot_happen_invalid_1": "[需要翻译] this cannot happen: invalid action '%s'", "misc.error.invalid_value_link": "[需要翻译] invalid value '%s' for 'link' argument", "misc.message.delete_failed": "[需要翻译] delete '%s' failed: %s", "misc.info.env_var_set": "[需要翻译] Env var %s is not set or invalid", "misc.info.warning_read_registry": "[需要翻译] Warning: Can't read registry to find the ", "misc.message.necessary_compiler_setting": "[需要翻译] necessary compiler setting\\n", "misc.message.command_failed": "[需要翻译] command %r failed: %s", "misc.message.command_failed_exit": "[需要翻译] command %r failed with exit code %s", "misc.message.config_9": "[需要翻译] config-{}{}", "misc.message.warn": "[需要翻译] warn()", "misc.error.invalid_textfile_option": "[需要翻译] invalid TextFile option '%s'", "misc.error.invalid_variable": "[需要翻译] invalid variable '$%s'", "misc.error.invalid_truth_value_1": "[需要翻译] invalid truth value %r", "misc.error.invalid_prefix_filename": "[需要翻译] invalid prefix: filename %r doesn't start with %r", "misc.error.invalid_version_number": "[需要翻译] invalid version number '%s'", "misc.error.invalid_format": "[需要翻译] invalid format '%s'", "misc.message.option_must_specified": "[需要翻译]  option must be specified", "misc.message.found_scripts": "[需要翻译] install_script '%s' not found in scripts", "misc.message.productname_setup_ended": "[需要翻译] [ProductName] setup ended prematurely because of an error.  Your system has not been modified.  To install this program at a later time, please run the installation again.", "misc.message.error_dialog": "[需要翻译] E<PERSON><PERSON>", "misc.message.progress_done": "[需要翻译] Progress done", "misc.error.failed_execute": "[需要翻译] Failed to execute: %s", "misc.message.libraries_option_must": "[需要翻译] 'libraries' option must be a list of tuples", "misc.message.option_must_list": "[需要翻译] 'ext_modules' option must be a list of Extension instances", "misc.message.element_option_must": "[需要翻译] each element of 'ext_modules' option must be an ", "misc.message.building_extension_failed": "[需要翻译] building extension \"%s\" failed: %s", "misc.message.option_extension": "[需要翻译] in 'ext_modules' option (extension '%s'), ", "misc.info.package_init_file": "[需要翻译] package init file '%s' not found ", "misc.info.file_module_found": "[需要翻译] file %s (for module %s) not found", "misc.message.exit_error_check": "[需要翻译] Will exit with an error if a check fails", "misc.message.missing_required_meta": "[需要翻译] missing required meta-data: %s", "misc.message.missing_meta_data": "[需要翻译] missing meta-data: if 'author' supplied, ", "misc.message.missing_meta_data_1": "[需要翻译] missing meta-data: if 'maintainer' supplied, ", "misc.message.missing_meta_data_2": "[需要翻译] missing meta-data: either (author and author_email) ", "misc.info.success": "[需要翻译] success!", "misc.info.failure": "[需要翻译] failure.", "misc.message.exec_prefix_option": "[需要翻译] exec-prefix option ignored on this platform", "misc.debug.config_vars": "[需要翻译] config vars:", "misc.message.distribution_option_deprecated": "[需要翻译] Distribution option extra_path is deprecated. ", "misc.message.option_must_list_1": "[需要翻译] 'extra_path' option must be a list, tuple, or ", "misc.error.found_pypirc": "[需要翻译] %s not found in .pypirc", "misc.message.supplied_warn_any": "[需要翻译] are supplied. Warn if any missing. [default]", "misc.message.content_00933fe7": "地址池容量信息", "misc.message.content_ff9825ea": "容量分析结果", "misc.message.content_47e5aa9d": "地址池容量分析器", "misc.message.content_cdfadb0c": "计算可用地址数量", "misc.message.content_ad390305": "计算网络效率", "misc.message.content_20ec955e": "确定网络类型", "misc.message.content_1a04c6ec": "计算大小效率因子", "misc.message.check_1": "检查子网对齐", "misc.message.content_171b099b": "计算碎片化分数（0-1，越低越好）", "misc.message.content_bf6ccf0b": "预测利用率", "misc.message.create_6": "创建空的容量信息", "misc.message.content_be55ca46": "计算容量分布", "misc.message.content_41e8d701": "识别优化机会", "misc.message.content_d7b30833": "检测容量冲突", "misc.message.check_2": "检查两个地址范围是否重叠", "misc.message.content_68ea7f21": "计算性能指标", "misc.message.content_870bd707": "地址池使用统计信息", "misc.message.content_deee8441": "使用模式", "misc.message.content_39d037c8": "池使用分析结果", "misc.message.content_852e2c7d": "地址池使用分析器", "misc.message.content_5ec5d00b": "计算单个池的统计信息", "misc.message.content_234ed199": "计算池中的总地址数", "misc.message.content_c6c1c1c0": "计算地址范围中的地址数量", "misc.message.content_447ccf47": "统计NAT规则中对池的引用次数", "misc.message.content_3af6cdf7": "估算已分配的地址数量", "misc.message.content_9273f692": "计算效率分数", "misc.message.content_92d3c58d": "识别使用模式", "misc.message.content_f45317ac": "分析单个池的使用模式", "misc.message.content_66d370fd": "计算效率排名", "misc.message.content_7e6b94e7": "计算整体指标", "service.conversion.message.content_1352ea5d": "内存优化器", "service.conversion.message.content_e936c69b": "获取当前内存使用量（MB）", "service.conversion.message.content_e39cf681": "优化内存使用", "service.conversion.debug.content_09e61af4": "🔧 内存优化 [{stage_name}]: {current_memory:.1f}MB", "service.conversion.debug.content_c4a954cd": "⚠️ 内存使用过高 ({current_memory:.1f}MB)，进行深度清理", "service.conversion.debug.success": "✅ 内存清理完成，节省 {saved_memory:.1f}MB", "service.conversion.debug.failed": "❌ 内存优化失败: {e}", "service.conversion.message.content_f110e628": "内存优化装饰器", "service.conversion.message.process": "分块配置处理器", "service.conversion.message.process_1": "分块处理配置", "service.conversion.debug.process": "🔄 分块处理配置，总行数: {len(config_lines)}", "service.conversion.debug.content_886d0b56": "📊 分为 {len(chunks)} 个块，每块最多 {self.chunk_size} 行", "service.conversion.debug.process_1": "🔄 处理第 {i+1}/{len(chunks)} 块", "service.conversion.debug.process_failed": "❌ 处理第 {i+1} 块失败: {e}", "service.conversion.message.process_2": "处理单个块", "service.conversion.message.check": "增强的接口映射验证", "service.conversion.debug.config": "⚠️ 映射的接口 {mapped_interface} 在配置中未找到，将跳过", "service.conversion.message.content_08fa4025": "没有找到有效的接口映射", "service.conversion.debug.check": "✅ 接口映射验证通过: {len(valid_mappings)} 个有效映射", "service.conversion.debug.error": "❌ 接口映射验证异常: {e}", "service.conversion.message.error": "验证异常: {str(e)}", "misc.message.content_d769cbfa": "初始化规则注册表", "misc.message.twice": "twice-nat44地址类型枚举", "misc.message.error_twice": "twice-nat44配置错误", "misc.message.error_twice_1": "twice-nat44验证错误", "misc.message.check_3": "初始化后验证", "misc.error.invalid_address": "[需要翻译] Invalid IPv4 address: {self.address_value}", "misc.message.validate": "验证IPv4地址格式", "misc.error.invalid_address_1": "[需要翻译] Invalid IPv4 address: {self.ipv4_address}", "misc.error.invalid_port_number": "[需要翻译] Invalid port number: {self.port}", "misc.error.vip_missing_mappedip": "[需要翻译] VIP {vip_name} missing mappedip", "misc.message.strategy": "FortiGate复合NAT策略 {policy_name}", "misc.error.failed_create_twice": "[需要翻译] Failed to create twice-nat44 rule: {str(e)}", "misc.message.convert_failed": "转换策略YANG验证失败: {str(e)}", "misc.message.content_b35977b4": "安全的翻译函数，确保即使原始_函数不可用也能正常工作", "misc.message.strategy_convert": "策略 {policy_id}: R10P2版本不支持NAT规则转换，仅转换安全策略", "misc.message.warning_policy_version": "[需要翻译] WARNING: Policy {policy_id} - R10P2 version does not support NAT rules, skipping NAT processing", "misc.message.debug_policy_any": "DEBUG: Policy {policy_id} - 目标接口 'any' 被接受（表示所有接口）", "misc.message.failed_load_interface": "[需要翻译] Failed to load interface mapping from file: {e}", "misc.message.error_getting_interface": "[需要翻译] Error getting interface mapping from context: {e}", "misc.message.policy_nat_rule": "[需要翻译] Policy {policy_id} NAT rule generation failed: {str(e)}", "misc.message.config_source": "[需要翻译] <config-source>", "misc.message.content_4d54e4fe": "清空策略缓存", "misc.message.content_ab545093": "性能监控器", "misc.message.start": "开始监控", "misc.message.content_d9664afc": "记录阶段耗时", "misc.message.content_791f19cb": "获取性能摘要", "misc.message.content_742578b1": "性能监控装饰器", "misc.message.config_10": "优化的配置解析器", "misc.message.configuration_file_reading": "[需要翻译] Configuration file reading", "misc.message.file_8": "优化的配置文件读取", "misc.message.file_9": "流式读取大文件", "misc.message.configuration_parsing": "[需要翻译] Configuration parsing", "misc.message.config_11": "优化的配置解析", "misc.debug.start_config": "📊 开始解析 {len(lines)} 行配置", "misc.debug.configuration_parsing_failed": "[需要翻译] ❌ Configuration parsing failed: {e}", "misc.message.config_12": "解析大型配置", "misc.debug.config": "🔄 使用大型配置解析模式", "misc.message.config_13": "解析普通配置", "misc.debug.using_standard_configuration": "[需要翻译] 🔄 Using standard configuration parsing mode", "misc.message.error_8": "管道错误处理装饰器", "misc.debug.content_aa9036eb": "🔄 执行管道阶段: {stage_name}", "misc.debug.success": "✅ 管道阶段完成: {stage_name}", "misc.debug.error": "⚠️ 管道阶段异常: {stage_name}: {str(e)}", "misc.debug.config_1": "🔄 尝试简化配置解析...", "misc.debug.failed_1": "❌ 恢复失败: {recovery_error}", "misc.message.content_b9ddc331": "安全的管道执行", "misc.debug.content_e211ef22": "🔄 执行阶段 {i+1}/{len(pipeline_stages)}: {stage_name}", "misc.debug.failed_2": "❌ 阶段 {stage_name} 执行失败: {e}", "misc.debug.success_1": "📊 管道执行完成: {successful_stages}/{len(pipeline_stages)} ({success_rate:.1f}%)", "misc.error.success": "管道执行成功率过低: {success_rate:.1f}%", "misc.message.error_9": "错误处理装饰器", "misc.debug.error_1": "工作流执行错误: {func.__name__}: {str(e)}", "misc.debug.file_config": "⚠️ 模板文件不存在: {template_path}，使用默认配置", "misc.message.file_10": "模板文件不存在: {template_path}", "misc.debug.failed_config": "⚠️ 模板准备失败: {e}，继续使用默认配置", "misc.message.failed_2": "模板准备失败: {str(e)}", "misc.debug.dir_config": "⚠️ YANG目录不存在: {yang_dir}，使用默认配置", "misc.message.dir": "YANG目录不存在: {yang_dir}", "misc.debug.failed_config_1": "⚠️ YANG准备失败: {e}，继续使用默认配置", "misc.message.failed_3": "YANG准备失败: {str(e)}", "misc.debug.dir": "✅ 输出目录准备完成: {output_dir}", "misc.debug.dir_1": "⚠️ 输出目录创建失败: {e}", "misc.message.dir_1": "输出目录创建失败: {str(e)}", "misc.debug.success_2": "⚠️ 环境准备完成，但有 {len(preparation_warnings)} 个警告", "misc.debug.success_3": "✅ 环境准备完全成功", "misc.debug.failed_3": "❌ 环境准备失败: {e}", "misc.message.error_10": "环境准备异常但已恢复: {str(e)}", "misc.message.convert_7": "厂商 '{vendor}' 暂时不支持新架构转换", "misc.message.convert_8": "当前版本只支持FortiGate设备的新架构转换", "misc.message.config_14": "Fortigate配置转换管道", "misc.message.file_11": "- 配置文件: {initial_data.get('config_file', 'unknown')}", "misc.message.content_97c0079d": "- 厂商: {initial_data.get('vendor', 'unknown')}", "misc.message.content_d5619d4b": "- 模型: {initial_data.get('model', 'unknown')}", "misc.message.content_222c0fe4": "- 版本: {initial_data.get('version', 'unknown')}", "misc.message.content_1a389ad7": "- 接口映射: {'存在' if initial_data.get('interface_mapping') else '不存在'}", "misc.message.content_d7d11654": "不存在", "misc.message.start_1": "开始执行新架构管道", "misc.message.success_debug": "🔍 DEBUG: 新架构管道执行完成，状态: {getattr(result_context, 'state', 'unknown')}", "misc.message.debug_1": "🔍 DEBUG: - 管道状态: {getattr(context, 'state', 'unknown')}", "misc.message.error_debug": "🔍 DEBUG: - 管道错误: {getattr(context, 'errors', [])}", "misc.message.debug_2": "🔍 DEBUG: - 管道数据键: {list(context._data.keys()) if hasattr(context, '_data') else 'unknown'}", "misc.message.debug_3": "🔍 DEBUG: - 无法获取管道上下文: {str(ctx_error)}", "misc.message.failed_error": "新架构管道执行失败，请检查错误信息并修复问题", "misc.message.failed_4": "新架构管道执行失败", "misc.message.failed_5": "YANG验证失败", "misc.message.failed_process": "由于YANG验证失败，跳过加密处理", "misc.message.failed_6": "YANG验证执行失败: {str(e)}", "misc.message.content_efb3f768": "未请求加密", "misc.message.process": "未请求加密处理", "misc.message.failed_7": "加密处理失败: {str(e)}", "misc.message.content_19a7e557": "旧架构已被禁用，请使用新的管道架构", "misc.message.start_2": "开始修复XML字符串中的重复xmlns属性", "misc.message.success": "XML字符串xmlns属性修复完成", "misc.message.error_11": "命名空间修复失败的详细错误: {str(ns_error)}", "misc.message.failed_8": "命名空间修复失败的堆栈跟踪: {traceback.format_exc()}", "misc.message.create_7": "桥接接口生成器类", "misc.message.create_8": "初始化桥接接口生成器", "misc.message.select_timeout": "[需要翻译] select-timeout", "misc.message.create_9": "测试桥接接口生成器功能", "misc.debug.create": "=== 桥接接口生成器测试 ===", "misc.debug.create_1": "生成的桥接接口XML:", "misc.message.config_15": "配置转换器，整合所有功能", "misc.message.error_12": "[需要翻译] config_converter.error.load_config_failed", "misc.message.error_13": "[需要翻译] config_converter.error.no_config_provided", "misc.message.error_14": "[需要翻译] config_converter.error.no_config_data", "misc.message.error_15": "[需要翻译] config_converter.error.invalid_config", "misc.message.error_16": "[需要翻译] config_converter.error.xml_generation_failed", "misc.message.error_17": "[需要翻译] config_converter.error.model_version_not_set", "misc.message.error_18": "[需要翻译] config_converter.error.save_xml_failed", "misc.message.error_19": "[需要翻译] config_converter.error.no_output_path", "misc.message.error_20": "[需要翻译] config_converter.error.save_config_failed", "misc.debug.config_2": "配置有效性: {is_valid}", "misc.debug.error_2": "错误: {error}", "misc.debug.create_2": "生成的XML:", "misc.debug.config_3": "\\nNTOS配置:", "misc.debug.convert": "\\n转换统计信息:", "misc.message.create_dns": "DNS XML生成器类", "misc.message.create_10": "初始化DNS XML生成器", "misc.message.warning_3": "[需要翻译] dns_generator.warning.empty_config", "misc.message.warning_4": "[需要翻译] dns_generator.warning.servers_exceed_limit", "misc.message.warning_5": "[需要翻译] dns_generator.warning.vrf_not_found_using_root", "misc.message.error_21": "[需要翻译] dns_generator.error.integration_failed", "misc.debug.config_4": "配置验证结果: {is_valid}", "misc.debug.create_xml": "生成的DNS XML:", "misc.debug.failed_dns": "DNS XML生成失败", "misc.message.file_12": "加密文件未生成", "misc.message.content_e181437d": "无法导入加密模块: {str(e)}", "misc.message.content_ee4fc49b": "获取默认模板路径", "misc.message.create_11": "加载模板或创建基础XML结构 - 优先使用模板信息（设计原则1）", "misc.message.create_12": "创建默认的XML结构，基于YANG模型，符合命名空间要求", "misc.message.process_create": "处理配置数据，基于YANG模型验证和生成XML", "misc.message.process_1": "处理接口配置 - 优先使用模板信息（设计原则1）", "misc.message.process_2": "处理单个接口配置 - 实现设计原则1的核心逻辑", "misc.message.config_16": "更新现有接口配置 - 保留模板信息，只修改必要字段", "misc.message.create_13": "创建新的完整接口配置块", "misc.message.create_14": "创建新接口配置块", "misc.message.config_17": "更新现有接口配置", "misc.message.process_3": "处理安全区域配置", "misc.message.create_15": "创建新安全区域配置块", "misc.message.config_18": "更新现有安全区域配置", "misc.message.process_4": "处理路由配置", "misc.message.create_16": "创建静态路由配置", "misc.message.process_5": "处理安全策略配置 - 基于模板优先原则", "misc.message.config_19": "更新现有安全策略配置 - 保留模板信息，只修改必要字段", "misc.message.create_17": "创建新的完整安全策略块", "misc.message.process_6": "处理地址对象配置 - 基于模板优先原则", "misc.message.config_20": "更新现有地址对象配置", "misc.message.create_18": "创建新的完整地址对象块", "misc.message.process_7": "处理服务对象配置 - 基于模板优先原则", "misc.message.config_21": "更新现有服务对象配置", "misc.message.create_19": "创建新的完整服务对象块", "misc.message.create_20": "获取或创建带验证的元素", "misc.message.create_validate": "创建或获取元素，带YANG验证", "misc.message.create_check": "创建叶子元素，带值验证", "misc.message.content_12c48dc0": "标准化接口名称", "misc.message.create_21": "生成最终的XML字符串", "misc.message.content_5851c9f5": "清理和优化命名空间声明", "misc.message.check_4": "获取详细的验证报告", "misc.message.content_784a0769": "导出YANG架构信息，用于调试", "misc.message.create_22": "创建元素但不设置xmlns（符合设计原则2）", "misc.message.content_9c0a325f": "清理冗余的xmlns声明，保持设计原则2的要求", "misc.message.check_5": "检查元素是否在interface标签内", "misc.message.create_23": "根据XPath查找元素，如果不存在则创建（实现设计原则1）", "misc.message.create_24": "更新现有元素文本或创建新元素（实现设计原则1的修改逻辑）", "misc.message.convert_9": "修复版本的LegacyGenerator，解决服务对象转换问题", "misc.message.content_11c808e7": "警告: FixedLegacyGenerator服务对象节点为空，没有添加任何服务集合", "misc.message.remove": "警告: FixedLegacyGenerator中的服务对象节点为空，将移除该节点", "misc.message.remove_1": "已移除空的服务对象节点", "misc.message.content_18df0272": "已重新序列化XML", "misc.message.success_1": "确认: 空的服务对象节点已成功移除", "misc.message.failed_9": "警告: 空的服务对象节点移除失败", "misc.message.process_8": "FixedLegacyGenerator处理XML时出错: {str(e)}", "misc.message.config_22": "飞塔物理接口配置转换优化器", "misc.message.warning_6": "[需要翻译] interface_handler.warning.unsupported_role", "misc.message.warning_7": "[需要翻译] interface_handler.warning.unknown_role", "misc.message.check_debug": "DEBUG: 管理接口检查 - 接口: {interface_name}, 原始列表: {is_mgmt_original}, 映射列表: {is_mgmt_mapped}, 直接匹配: {is_mgmt_direct}", "misc.message.error_22": "[需要翻译] interface_handler.error.netmask_conversion", "misc.message.warning_8": "[需要翻译] interface_handler.warning.non_root_vdom", "misc.message.warning_9": "[需要翻译] interface_handler.warning.defaultgw_disable_not_supported", "misc.message.success_debug_1": "✅ DEBUG: 成功创建address节点，IP: {final_ip_cidr}", "misc.message.negotiation_timeout": "[需要翻译] negotiation-timeout", "misc.message.warning_10": "[需要翻译] interface_handler.warning.unsupported_allowaccess", "misc.message.debug_4": "DEBUG: 最终的combined_access: {combined_access}", "misc.message.debug_https_ping": "DEBUG: 最终XML值 - https: {https_value}, ping: {ping_value}, ssh: {ssh_value}", "misc.message.config_23": "测试飞塔配置转换优化", "misc.debug.config_5": "=== 飞塔配置转换优化测试 ===", "misc.debug.parse": "\\n1. 测试IP地址解析:", "misc.debug.parse_1": "\\n2. 测试allowaccess解析:", "misc.debug.success_4": "\\n测试完成！", "misc.message.create_25": "配置生成策略基类", "misc.error.method": "子类必须实现generate方法", "misc.message.create_26": "基于YANG模型的生成策略", "misc.message.xml_version_encoding": "[需要翻译] <?xml version=\"1.0\" encoding=\"UTF-8\"?>\\n<config xmlns=\"urn:ruijie:ntos\"><vrf><name>main</name></vrf></config>", "misc.message.traditional_create": "传统XML生成策略", "misc.message.process_config": "批处理生成策略，适用于大型配置", "misc.message.config_24": "设置时间对象", "misc.message.content_b85963f2": "跳过无名称的服务对象", "misc.message.failed_error_1": "ImprovedLegacyGenerator服务对象 {service.get('name', 'unknown')} 处理失败，错误: {str(e)}", "misc.message.content_309daa79": "检测到字典格式的端口范围输入: {port_range_input}", "misc.message.convert_10": "字典格式端口范围转换结果: {result}", "misc.message.error_23": "端口范围输入类型错误，期望字符串或字典，得到: {type(port_range_input)}", "misc.message.error_24": "端口范围验证输入类型错误，期望字符串，得到: {type(port_str)}", "misc.message.content_659a2c09": "_convert_port_range检测到字典格式的端口范围输入: {port_range_str}", "misc.message.content_b102fc62": "_convert_port_range无法从字典中提取端口信息: {port_range_str}", "misc.message.convert_11": "_convert_port_range字典转换结果: {port_range_str}", "misc.message.error_25": "_convert_port_range端口范围输入类型错误，期望字符串或字典，得到: {type(port_range_str)}", "misc.message.process_9": "ImprovedLegacyGenerator处理XML时出错: {str(e)}", "misc.message.error_26": "[需要翻译] interface_handler.error.all_tunnel_ids_used", "misc.message.content_399341aa": "重置隧道ID分配记录", "misc.message.warning_11": "[需要翻译] interface_handler.warning.using_default_pppoe_username", "misc.message.warning_12": "[需要翻译] interface_handler.warning.using_default_pppoe_password", "misc.message.timeout_3": "[需要翻译] {%s}timeout", "misc.message.negotiation_timeout_1": "[需要翻译] {%s}negotiation-timeout", "misc.message.create_27": "查找或创建访问控制子元素", "misc.message.warning_13": "[需要翻译] interface_handler.warning.invalid_interface_data", "misc.message.warning_14": "[需要翻译] interface_handler.warning.generated_random_name", "misc.message.function": "向后兼容的获取下一个隧道ID函数", "misc.message.function_1": "向后兼容的重置隧道ID函数", "misc.message.create_config": "向后兼容的创建DHCP配置函数", "misc.message.create_config_1": "向后兼容的创建PPPoE配置函数", "misc.message.create_config_2": "向后兼容的创建IPv6 DHCP配置函数", "misc.message.create_28": "向后兼容的创建IPv4节点函数", "misc.message.create_29": "向后兼容的创建IPv6节点函数", "misc.message.create_30": "向后兼容的创建访问控制节点函数", "misc.message.content_39764ab5": "向后兼容的清理和标准化接口名称函数", "misc.message.process_10": "向后兼容的处理飞塔接口函数", "misc.message.content_4bd712d7": "向后兼容的获取NTOS接口名称函数", "misc.message.parse": "向后兼容的解析飞塔子接口函数", "misc.message.function_2": "向后兼容的添加接口到XML函数", "handler.interface.message.config": "向后兼容的添加默认接口配置函数", "handler.interface.message.create": "向后兼容的创建新接口函数", "handler.interface.message.create_1": "向后兼容的创建新VLAN子接口函数", "handler.interface.message.content_d0b0df0e": "向后兼容的更新VLAN子接口函数", "handler.interface.message.content_54704498": "向后兼容的更新现有接口函数", "handler.interface.message.content_30cb8177": "向后兼容的更新物理接口函数", "misc.message.traditional_create_1": "传统XML配置生成器（回退方案）", "misc.message.config_25": "设置接口信息", "misc.message.config_26": "设置地址对象", "misc.message.config_27": "设置服务对象", "misc.message.config_28": "设置策略规则", "misc.message.config_29": "设置安全区域", "misc.message.config_30": "设置静态路由", "misc.message.config_31": "设置地址组", "misc.message.config_32": "设置服务组", "misc.message.config_33": "设置服务映射关系", "misc.message.setup": "设置NAT规则", "misc.message.remove_2": "警告: XML中的服务对象节点为空，将移除该节点", "misc.message.content_ad8e08d5": "警告: XML中没有找到服务对象节点", "misc.message.config_34": "[需要翻译] <config/>", "misc.message.config_xmlns": "[需要翻译] <config xmlns", "misc.message.failed_create": " 配置生成失败，生成了默认结构。统计信息: {stats} ", "misc.message.content_f8790bd8": "调用_fix_duplicate_xmlns_in_string修复重复xmlns属性", "misc.message.success_2": "xmlns修复完成，修复前大小: {len(xml_str_before)}，修复后大小: {len(xml_str)}", "misc.message.content_6d116474": "调用_fix_namespace_prefixes修复命名空间前缀问题", "misc.message.success_3": "命名空间前缀修复完成", "misc.message.xml_version_encoding_1": "[需要翻译] <?xml version=\"1.0\" encoding=\"UTF-8\"?>\\n<config xmlns=\"urn:ruijie:ntos\"><vrf><n>main</n></vrf></config>", "misc.message.content_f4d3b68f": "最终XML字符串中包含服务对象节点", "misc.message.content_38c5a883": "服务对象节点内容: {service_obj_content[:200]}{'...' if len(service_obj_content) > 200 else ''}", "misc.message.content_f9e71b8e": "警告: 服务对象节点内容为空", "misc.message.content_7749a320": "警告: 无法提取服务对象节点内容", "misc.message.content_e62867bb": "警告: 最终XML字符串中没有找到服务对象节点", "misc.message.content_ef44e17a": "默认时间对象，表示全天候有效", "misc.message.content_b1061e92": "完整命名空间", "misc.message.content_3252c726": "标签结尾匹配", "misc.message.content_5eb4e8e0": "简单标签", "misc.message.content_37884dbf": "接口 #{intf_index}", "misc.message.content_a951d484": "区域 #{zone_index}", "misc.message.error_create": "错误: 无法创建服务对象节点", "misc.message.create_31": "服务对象节点已创建，标签名: {service_obj.tag}", "misc.message.content_df356f26": "服务对象 {service_name} 同时有TCP和UDP端口但协议类型为 {protocol}，自动修正为 tcp_udp", "misc.message.tcp": "服务对象 {service_name} 有TCP端口但协议类型为 {protocol}，自动修正为 tcp", "misc.message.udp": "服务对象 {service_name} 有UDP端口但协议类型为 {protocol}，自动修正为 udp", "misc.message.content_95d79109": "服务对象 {service_name} 未指定协议类型，根据端口自动推断为 tcp_udp", "misc.message.tcp_1": "服务对象 {service_name} 未指定协议类型，根据端口自动推断为 tcp", "misc.message.udp_1": "服务对象 {service_name} 未指定协议类型，根据端口自动推断为 udp", "misc.message.content_a1c6d464": "警告: 服务集合 {service_name} 可能未正确添加到XML", "misc.message.success_4": "服务集合 {service_name} 在第二次尝试后成功添加", "misc.message.error_27": "错误: 无法添加服务集合 {service_name}", "misc.message.success_5": "服务对象 {service_name} 添加成功", "misc.message.failed_error_2": "服务对象 {service.get('name', 'unknown')} 处理失败，错误: {str(e)}", "misc.message.content_e4a11c28": "XML中的服务集合数量: {len(service_sets)}", "misc.message.content_ba498504": "XML中的服务集合 #{i+1}: {name_elem[0].text}", "misc.message.content_93b9b408": "警告: 服务对象节点为空，没有添加任何服务集合", "misc.message.session_timeout": "[需要翻译] session-timeout", "misc.message.config_source_1": "[需要翻译] config-source", "misc.message.strategy_config_fixedport": "策略 '{policy_name}' 的 fixedport enable 配置不被NTOS系统支持", "misc.message.fortigate_warning_1": "[需要翻译] fortigate.warning.fixedport_not_supported", "misc.message.strategy_config": "策略 '{policy_name}' 的用户配置 '{users_str}' 不被NTOS系统支持", "misc.message.fortigate_warning_2": "[需要翻译] fortigate.warning.users_not_supported", "misc.message.strategy_config_1": "策略 '{policy_name}' 的用户组配置 '{groups_str}' 不被NTOS系统支持", "misc.message.fortigate_warning_3": "[需要翻译] fortigate.warning.groups_not_supported", "misc.message.content_d5aac366": "命名空间前缀", "misc.message.content_9644f8d5": "完整URI", "misc.message.content_f906271f": "简单查找", "misc.debug.debug": "[DEBUG] 发现重复xmlns模式: {pattern}, 匹配数: {len(matches)}", "misc.debug.debug_1": "[DEBUG] 在字符串级别修复了 {fixed_count} 个重复的xmlns属性", "misc.message.config_35": "[需要翻译] <config", "misc.message.config_36": "[需要翻译] </config>", "misc.message.config_37": "[需要翻译] \\s*<config\\s*/>.*", "misc.message.start_3": "开始修复所有接口的工作模式为route模式", "misc.message.content_9c137f80": "修复前统计: Bridge模式={bridge_count_before}, Route模式={route_count_before}", "misc.message.content_c1ed040b": "修复后统计: Bridge模式={bridge_count_after}, Route模式={route_count_after}", "misc.message.success_6": "成功修复了 {bridge_count_before} 个接口的工作模式", "misc.message.content_ca83ed73": "修复接口工作模式时出错: {str(e)}", "misc.message.config_create": "NAT配置XML生成器类", "misc.message.create_32": "初始化生成器", "misc.message.process_check_static": "处理NAT规则 {rule_name}, 规则类型检查: static-dnat44={('static-dnat44' in rule)}, static-snat44={('static-snat44' in rule)}, dynamic-snat44={('dynamic-snat44' in rule)}, twice-nat44={('twice-nat44' in rule)}", "misc.message.config_warning_name": "WARNING: 转换配置中缺少pool-name: {translate_config}", "generator.ntos.message.create_ntos": "NTOS XML配置生成器", "generator.ntos.message.file": "获取模板文件路径", "generator.ntos.message.config_create": "准备配置数据供YANG生成器使用", "generator.ntos.message.convert": "计算转换统计信息", "misc.message.create_33": "扩展的NTOS配置生成器，提供更多功能", "misc.message.file_13": "获取模板文件路径，优先使用自定义模板", "misc.message.config_38": "未设置", "misc.message.error_28": "[需要翻译] ntos_extensions.error.extending_xml_failed", "misc.message.error_29": "[需要翻译] ntos_extensions.error.vrf_not_found", "misc.message.error_30": "[需要翻译] ntos_extensions.error.dns_config_failed", "misc.message.physicalinterfacehandler": "PhysicalInterfaceHandler.create_new_interface 被调用，接口: {interface_name}", "misc.message.content_7524670b": "接口数据键: {list(intf_data.keys())}", "misc.message.config_39": "更新带宽配置 - 支持新架构和原始FortiGate格式", "misc.message.config_40": "智能更新IPv4配置，避免破坏现有结构，确保不会创建重复节点", "misc.message.failed_import_network": "[需要翻译] Failed to import network conflict resolver, using fallback", "misc.message.create_create": "安全地查找或创建IPv4节点，避免重复创建", "misc.error.invalid_parent_element": "[需要翻译] Invalid parent element for IPv4 creation", "misc.message.process_11": "安全地查找子元素，处理命名空间问题", "misc.message.update": "更新IPv4启用状态", "misc.message.config_41": "清除指定类型的配置，支持命名空间", "misc.message.update_1": "更新PPPoE认证信息", "misc.message.config_42": "智能更新IPv6配置", "misc.message.processing_route_group": "[需要翻译] routing_handler: processing route group - destination: {destination}, count: {len(route_group)}", "misc.message.invalid_next_hop": "[需要翻译] routing_handler: invalid next-hop data", "misc.message.invalid_next_hop_1": "[需要翻译] routing_handler: invalid next-hop data for new route", "misc.message.function_3": "向后兼容的添加路由到XML函数", "misc.message.content_0faa9356": "向后兼容的更新现有路由函数", "misc.message.create_34": "向后兼容的创建新路由函数", "misc.message.create_35": "向后兼容的创建静态路由配置函数", "misc.message.create_36": "安全策略XML生成器类", "misc.message.securitypolicygenerator": "SecurityPolicyGenerator: 添加源区域 = {policy['source-zone']}", "misc.message.securitypolicygenerator_1": "SecurityPolicyGenerator: 策略中没有source-zone字段", "misc.message.securitypolicygenerator_2": "SecurityPolicyGenerator: 添加目标区域 = {policy['dest-zone']}", "misc.message.securitypolicygenerator_3": "SecurityPolicyGenerator: 添加目标区域(兼容) = {policy['destination-zone']}", "misc.message.securitypolicygenerator_4": "SecurityPolicyGenerator: 策略中没有dest-zone字段", "misc.message.securitypolicygenerator_5": "SecurityPolicyGenerator: 添加源接口 = {policy['source-interface']}", "misc.message.securitypolicygenerator_6": "SecurityPolicyGenerator: 策略中没有source-interface字段", "misc.message.securitypolicygenerator_7": "SecurityPolicyGenerator: 添加目标接口 = {policy['dest-interface']}", "misc.message.securitypolicygenerator_8": "SecurityPolicyGenerator: 策略中没有dest-interface字段", "misc.message.securitypolicygenerator_9": "SecurityPolicyGenerator: 添加动作 = {action}", "misc.message.validation_exception": "[需要翻译] security_policy.validation.exception", "misc.message.error_31": "[需要翻译] security_zone_handler.error.invalid_zone_data_format", "misc.message.function_4": "向后兼容的添加安全区域到XML函数", "misc.message.content_b98ec5fd": "向后兼容的更新现有安全区域函数", "misc.message.create_37": "向后兼容的创建新安全区域函数", "misc.message.create_38": "向后兼容的创建安全区域配置函数", "misc.message.process_12": "初始化时间对象处理器", "misc.message.config_43": "配置数据验证器", "misc.message.content_1732cdd5": "接口 #{i+1} 不是字典类型", "misc.message.missing_1": "接口 #{i+1} 缺少name字段", "misc.message.content_0709928b": "接口 {name} 的值不是字典类型", "misc.message.content_ebfca9f6": "接口数据既不是列表也不是字典类型", "misc.message.content_820c86de": "区域数据不是列表类型", "misc.message.content_eb858730": "区域 #{i+1} 不是字典类型", "misc.message.missing_2": "区域 #{i+1} 缺少name字段", "misc.message.content_7fd36372": "地址对象数据不是列表类型", "misc.message.content_e7ffd28a": "地址对象 #{i+1} 不是字典类型", "misc.message.missing_3": "地址对象 #{i+1} 缺少name字段", "misc.message.content_7a5493b8": "服务对象数据不是列表类型", "misc.message.content_3bbe6aa6": "服务对象 #{i+1} 不是字典类型", "misc.message.missing_4": "服务对象 #{i+1} 缺少name字段", "misc.message.content_4b988130": "策略规则数据不是列表类型", "misc.message.content_c5abc299": "策略规则 #{i+1} 不是字典类型", "misc.message.missing_5": "策略规则 #{i+1} 缺少name字段", "misc.message.content_b79aeeab": "静态路由数据不是列表类型", "misc.message.content_580ffeca": "静态路由 #{i+1} 不是字典类型", "misc.message.content_c7b3f6a8": "地址组数据不是列表类型", "misc.message.content_fb7c7399": "地址组 #{i+1} 不是字典类型", "misc.message.missing_6": "地址组 #{i+1} 缺少name字段", "misc.message.content_a5395bf0": "服务组数据不是列表类型", "misc.message.content_a6327508": "服务组 #{i+1} 不是字典类型", "misc.message.missing_7": "服务组 #{i+1} 缺少name字段", "misc.message.config_44": "为子接口 {interface_name} 添加IPv4配置，模式: {interface_mode}", "misc.message.config_45": "子接口 {interface_name} 配置为PPPoE模式，用户名: {pppoe_data['user']}", "misc.message.content_e0c22f21": "子接口 {interface_name} 没有子网掩码，使用默认/24", "misc.message.config_46": "子接口 {interface_name} 配置为静态IP模式: {cidr_address}", "misc.message.failed_10": "子接口 {interface_name} IP地址转换失败: {e}", "misc.message.content_f0d0cba6": "子接口 {interface_name} 回退到DHCP模式", "misc.message.config_47": "子接口 {interface_name} 配置为DHCP模式", "misc.message.config_48": "子接口 {interface_name} 未找到明确的IP配置，默认使用DHCP模式", "misc.message.warning_15": "[需要翻译] interface_handler.warning.missing_pppoe_credentials", "misc.message.config_49": "使用通用方法配置PPPoE", "misc.message.process_process": "XML命名空间处理器，处理命名空间相关操作", "misc.message.create_39": "XML元素构建器，简化XML元素的创建和更新", "misc.message.content_9dffb0f6": "网关%接口", "misc.message.content_b0d16021": "初始化XML集成器", "misc.message.config_50": "XMLIntegrator接收到的策略配置: {policy_config}", "misc.message.process_process_1": "跳过传统接口处理，使用接口处理阶段的XML片段", "misc.message.config_51": "添加默认的网络栈配置", "misc.message.config_52": "添加默认的监控配置", "misc.message.config_53": "添加默认的流控配置", "misc.message.config_54": "添加默认的IP-MAC绑定配置", "misc.message.check_6": "添加默认的反向路径检查", "misc.message.config_55": "添加默认的snooping配置", "misc.message.warning_16": "WARNING: 未找到接口 {original_name} 的映射，使用原始名称", "misc.message.content_762b63e8": "初始化命名空间管理器", "misc.message.success_7": "[需要翻译] xml_utils.create_default_structure.success", "misc.message.check_7": "检查元素是否在vrf标签内", "misc.message.create_40": "创建清理后的元素", "misc.message.warning_17": "[需要翻译] xml_utils.warning.children_lost_during_cleanup", "misc.message.warning_18": "[需要翻译] xml_utils.warning.significant_children_loss_using_backup", "misc.message.error_32": "[需要翻译] xml_utils.error.cleanup_namespaces_failed", "misc.message.warning_19": "[需要翻译] xml_utils.warning.children_lost_during_root_replacement", "misc.message.error_33": "[需要翻译] xml_utils.error.all_children_lost_using_backup", "misc.message.create_41": "向后兼容的创建默认XML结构函数", "misc.message.content_be3cb97a": "向后兼容的命名空间清理函数", "misc.message.create_42": "向后兼容的查找或创建VRF节点函数", "misc.message.create_43": "向后兼容的查找或创建带命名空间的子节点函数", "misc.message.content_78ffa1af": "向后兼容的修复重复xmlns属性函数", "generator.yang.message.create": "初始化YANG模型生成器", "generator.yang.message.create_function": "向后兼容的生成XML函数", "generator.yang.debug.error": "错误: XML包含命名空间前缀: {has_prefix.group(0)}", "generator.yang.debug.content_9114a7e1": "通过: XML不包含命名空间前缀", "generator.yang.debug.error_1": "错误: XML包含{xmlns_count}个xmlns声明，应该只包含一个", "generator.yang.debug.content_6ba57322": "通过: XML只包含一个xmlns声明", "generator.yang.debug.error_2": "错误: XML包含XML声明", "generator.yang.debug.content_612b5b9d": "通过: XML不包含XML声明", "misc.message.error_34": "[需要翻译] yang_extensions.error.interface_missing_name", "misc.message.error_35": "[需要翻译] yang_extensions.error.invalid_ip_format", "misc.message.error_36": "[需要翻译] yang_extensions.error.invalid_ip_address", "misc.message.error_37": "[需要翻译] yang_extensions.error.invalid_prefix_length", "misc.message.error_38": "[需要翻译] yang_extensions.error.invalid_prefix_format", "misc.message.error_39": "[需要翻译] yang_extensions.error.route_missing_destination", "misc.message.error_40": "[需要翻译] yang_extensions.error.route_missing_gateway", "misc.message.error_41": "[需要翻译] yang_extensions.error.zone_missing_name", "misc.message.content_87abb0e4": "缓存条目数据类", "misc.message.content_79e1bb3f": "清空缓存", "misc.message.check_8": "检查缓存条目是否过期", "misc.message.content_e6fca047": "驱逐最近最少使用的条目", "misc.message.content_f171aa25": "获取缓存统计信息", "misc.message.content_e965ab1a": "获取所有缓存键", "misc.message.content_30800d77": "初始化缓存管理器", "misc.message.content_e9deb781": "清空所有缓存", "misc.message.content_142e7f94": "获取所有缓存的统计信息", "misc.message.content_403c6e9e": "获取所有缓存名称", "misc.message.error_42": "错误严重程度", "misc.message.error_43": "错误记录数据类", "misc.message.error_44": "初始化异常注册表", "misc.message.error_45": "注册默认异常处理器", "misc.message.file_14": "处理文件未找到异常", "misc.message.error_46": "处理权限错误异常", "misc.message.error_47": "处理连接错误异常", "misc.message.error_48": "处理超时错误异常", "misc.message.error_49": "处理值错误异常", "misc.message.error_50": "处理键错误异常", "misc.message.error_51": "处理内存错误异常", "misc.message.file_15": "创建默认文件恢复策略", "misc.message.content_e973e8f3": "重试恢复策略", "misc.message.content_0068a743": "释放内存并重试恢复策略", "misc.message.content_061eede4": "提供默认值恢复策略", "misc.message.check_9": "验证和清理输入恢复策略", "misc.message.error_52": "记录错误历史", "misc.message.content_e70311ac": "根据严重程度获取日志级别", "misc.message.error_53": "获取错误统计信息", "misc.message.success_8": "计算恢复成功率", "misc.message.error_54": "获取最近的错误记录", "misc.message.content_b0c96da9": "性能指标数据类", "misc.message.content_46dfc849": "记录性能指标", "misc.message.content_bd80ce7b": "更新操作统计", "misc.message.content_66251888": "获取当前系统指标", "misc.message.check_10": "检查内存使用情况", "misc.message.content_7dd1d983": "获取内存统计信息", "misc.message.config_56": "设置内存阈值", "misc.message.config_57": "初始化配置管理器", "misc.message.config_58": "初始化默认配置", "misc.message.config_59": "[需要翻译] Config-{model}", "misc.message.config_60": "获取配置值", "misc.message.config_61": "设置配置值", "misc.message.check_11": "检查厂商是否支持", "misc.message.check_12": "检查设备型号和版本是否支持", "misc.message.dir_2": "获取引擎根目录", "misc.message.ntos_config_converter": "[需要翻译] NTOS Config Converter", "misc.message.config_62": "初始化设置", "misc.message.config_63": "获取所有设置", "misc.message.check_13": "检查设备型号是否支持", "misc.message.content_2d01fa72": "获取支持的厂商列表", "misc.message.content_297577bb": "获取支持的设备型号列表", "misc.error.input_validation_failed": "[需要翻译] Input validation failed: {validation_result.get('error')}", "misc.error.output_validation_failed": "[需要翻译] Output validation failed: {validation_result.get('error')}", "misc.message.check_14": "默认输入验证", "misc.error.parameter_cannot_none": "[需要翻译] Parameter {key} cannot be None", "misc.message.check_15": "默认输出验证", "misc.message.create_44": "生成默认缓存键", "misc.message.content_2753f397": "清理过期缓存", "misc.message.content_94ec1a14": "驱逐最旧的缓存条目", "misc.message.error_twice_2": "twice-nat44错误类型", "misc.message.error_twice_3": "twice-nat44错误严重程度", "misc.message.error_twice_4": "twice-nat44错误上下文", "misc.message.error_twice_5": "twice-nat44错误记录", "misc.message.error_twice_6": "twice-nat44专用错误处理器", "misc.message.content_68422026": "注册默认恢复策略", "misc.message.error_55": "分类错误类型", "misc.message.error_56": "确定错误严重程度", "misc.message.error_57": "生成错误ID", "misc.message.content_cce9ddf4": "提取技术详情", "misc.message.exception_type": "[需要翻译] Exception Type: {type(exception).__name__}", "misc.message.exception_message": "[需要翻译] Exception Message: {str(exception)}", "misc.message.content_33ef6099": "评估用户影响", "misc.message.process_13": "建议处理动作", "misc.message.error_58": "尝试错误恢复", "misc.message.error_59": "恢复配置错误", "misc.message.invalid_address": "[需要翻译] Invalid IPv4 address", "misc.message.error_60": "恢复验证错误", "misc.message.error_61": "恢复转换错误", "misc.message.error_62": "恢复XML生成错误", "misc.message.error_63": "恢复模板集成错误", "misc.message.error_64": "恢复性能错误", "misc.message.error_65": "恢复资源错误", "misc.message.error_66": "恢复网络错误", "misc.message.error_67": "记录错误日志", "misc.message.error_68": "更新错误统计", "misc.message.error_69": "生成用户友好的错误消息", "misc.message.error_70": "获取最常见的错误", "misc.message.error_71": "错误上下文管理器", "misc.message.error_72": "获取全局twice-nat44错误处理器实例", "misc.message.error_73": "便捷的twice-nat44错误处理函数", "misc.message.content_f8a9b523": "性能指标数据结构", "misc.message.content_41e64d8f": "执行时间（秒）", "misc.message.content_3e1a63eb": "内存变化（MB）", "misc.message.content_3fabf302": "吞吐量（规则/秒）", "misc.message.success_9": "成功率（%）", "misc.message.twice_1": "twice-nat44对象池", "misc.message.content_37c635b2": "获取对象池统计信息", "misc.message.content_df001aee": "清空对象池", "misc.message.twice_2": "twice-nat44缓存管理器", "misc.message.remove_3": "移除缓存键", "misc.message.content_bb0882d0": "驱逐最近最少使用的缓存项", "misc.message.twice_3": "twice-nat44性能优化器", "misc.message.content_74c3c9f4": "初始化性能优化器", "misc.message.content_bf7fc612": "执行垃圾回收", "misc.message.content_3463f970": "获取性能统计信息", "misc.message.content_42e88a1b": "重置统计信息", "misc.message.content_aeb91f94": "获取全局twice-nat44性能优化器实例", "misc.message.remove_4": "移除最久未访问的缓存条目", "misc.message.template_loading_failed": "[需要翻译] Template loading failed: model={model}, version={version}, type={template_type}, error={str(e)}", "misc.message.template_root_element": "[需要翻译] Template root element is not 'config': actual='{local_tag}', expected='config'", "misc.message.warning_vrf_element": "[需要翻译] Warning: VRF element not found in template (tried both no namespace and default namespace)", "misc.message.warning_template_root": "[需要翻译] Warning: Template root element has no child elements", "misc.message.template_structure_validation": "[需要翻译] Template structure validation completed", "misc.message.content_380feec8": "清空模板缓存", "misc.message.error_74": "YANG验证错误: {str(e)}", "misc.message.error_75": "YANG验证错误详细信息\\n", "misc.message.content_ae1f6864": "时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\\n", "misc.message.file_16": "XML文件: {xml_file}\\n", "misc.message.content_4478df4f": "设备型号: {model}\\n", "misc.message.content_71309e7b": "设备版本: {version}\\n", "misc.message.error_76": "yanglint错误输出:\\n", "misc.debug.start": "脚本开始执行", "misc.debug.dir_2": "当前工作目录: {os.getcwd()}", "misc.debug.file": "语言文件目录: {LOCALE_DIR}", "misc.debug.file_1": "语言文件存在: {file_path}", "misc.debug.file_2": "语言文件不存在: {file_path}", "misc.debug.file_3": "额外文件存在: {file_path}", "misc.debug.file_4": "额外文件不存在: {file_path}", "misc.message.load_file": "加载JSON文件", "misc.debug.file_5": "文件不存在: {file_path}", "misc.debug.file_6": "加载文件成功: {file_path}, 包含 {len(data)} 个键", "misc.debug.file_error": "加载文件失败: {file_path}, 错误: {str(e)}", "misc.message.save_file": "保存JSON文件", "misc.debug.file_7": "保存文件成功: {file_path}", "misc.debug.file_error_1": "保存文件失败: {file_path}, 错误: {str(e)}", "misc.message.file_17": "获取所有语言文件中的所有键", "misc.debug.file_8": "从 {lang} 文件中获取了 {len(keys)} 个键", "misc.debug.content_869c5a70": "从 {os.path.basename(file_path)} 中获取了 {len(keys)} 个键", "misc.message.file_file": "合并所有语言文件，确保所有文件包含相同的键", "misc.debug.file_9": "开始合并语言文件...", "misc.debug.file_10": "创建新的语言文件: {file_path}", "misc.debug.content_3cda7df4": "将 {os.path.basename(file_path)} 中的 {len(file_data)} 个键添加到 {lang} 额外数据中", "misc.debug.file_11": "所有文件共有 {len(all_keys)} 个不同的键", "misc.debug.content_afad8d50": "合并额外键到 {lang}, 共 {len(data)} 个键", "misc.debug.missing": "{lang} 缺少 {len(missing)} 个键", "misc.debug.content_5859ec2d": "  缺少的键: {sorted(list(missing))}", "misc.message.content_12049571": "[需翻译] {en_value}", "misc.debug.content_e8323c3c": "  添加键到 {lang}: {key} = [需翻译] {en_value}", "misc.debug.needs_translation": "  添加键到 {lang}: {key} = [Needs Translation] {zh_value}", "misc.debug.file_12": "开始合并语言资源文件...", "misc.debug.success_5": "合并完成!", "misc.message.file_18": "自动发现所有语言文件", "misc.debug.file_13": "加载文件成功: {os.path.basename(file_path)}, 包含 {len(data)} 个键", "misc.debug.file_error_2": "加载文件失败: {os.path.basename(file_path)}, 错误: {str(e)}", "misc.debug.file_14": "保存文件成功: {os.path.basename(file_path)}", "misc.debug.file_error_3": "保存文件失败: {os.path.basename(file_path)}, 错误: {str(e)}", "misc.debug.file_15": "=== 语言文件合并工具 ===\\n", "misc.debug.file_16": "发现的语言文件: {list(locale_files.keys())}\\n", "misc.debug.file_17": "未发现任何语言文件！", "misc.debug.file_18": "\\n所有文件共有 {len(all_keys)} 个不同的键", "misc.debug.check": "\\n=== 键完整性检查 ===", "misc.debug.missing_1": "{lang}: 缺少 {len(missing)} 个键", "misc.debug.content_5e31626b": "{lang}: ✓ 包含所有键", "misc.debug.file_19": "\\n✓ 所有语言文件都包含完整的键集合！", "misc.debug.start_1": "\\n=== 开始填充缺失的键 ===", "misc.debug.content_1b3c5cce": "\\n为 {lang} 添加 {len(missing)} 个缺失的键:", "misc.message.content_eb8b97ad": "[需翻译至{lang}] {zh_value}", "misc.message.content_5e37a6be": "[需翻译至{lang}] {en_value}", "misc.message.content_1b4fefd4": "[需要{lang}翻译]", "misc.debug.content_c3a061d5": "  ... 还有 {len(missing) - 5} 个键", "misc.debug.file_20": "\\n=== 保存更新后的文件 ===", "misc.debug.content_22589202": "  ✓ {lang}: {len(data)} 个键", "misc.debug.failed_4": "  ✗ {lang}: 保存失败", "misc.debug.check_1": "\\n=== 最终验证 ===", "misc.debug.content_f9c1d6b9": "  {lang}: {len(data)} 个键", "misc.debug.success_success_file": "\\n✓ 合并完成！成功更新了 {success_count} 个语言文件。", "misc.message.content_25e46ea3": "地址池引用信息", "misc.message.check_16": "引用完整性检查结果", "misc.message.content_7e35fdad": "地址池引用管理器", "misc.message.content_7d26ba9e": "添加策略到池的引用", "misc.message.remove_5": "移除策略到池的引用", "misc.message.content_eec78217": "添加NAT规则到池的引用", "misc.message.remove_6": "移除NAT规则到池的引用", "misc.message.content_c83f8080": "=== 地址池引用关系报告 ===", "misc.message.content_cac68e55": "总计地址池: {total_pools}", "misc.message.content_07ff5065": "活跃地址池: {active_pools}", "misc.message.content_26f4e86f": "孤立地址池: {orphaned_pools}", "misc.message.content_fb7fc981": "引用次数: {pool_ref.reference_count}", "misc.message.content_68218c6b": "状态: {'孤立' if pool_ref.is_orphaned else '活跃'}", "misc.message.content_1bcbc2e3": "引用策略 ({len(pool_ref.referencing_policies)}):", "misc.message.content_07d0826e": "引用NAT规则 ({len(pool_ref.referencing_nat_rules)}):", "misc.message.content_37f57c97": "=== 优化建议 ===", "misc.message.content_9bd9ac69": "{i}. {suggestion['description']} (优先级: {suggestion['priority']})", "misc.message.content_b51470fc": "   涉及池: {', '.join(suggestion['pools'][:3])}", "misc.message.content_697eb332": "   ... 等{len(suggestion['pools'])}个", "misc.message.content_b268a111": "清除所有引用关系", "misc.message.process_14": "获取待处理的变更", "misc.message.process_15": "提交变更（清除待处理变更列表）", "misc.message.content_d875239e": "警告: 没有找到接口信息", "misc.message.content_9e628c60": "初始化服务映射器", "misc.message.file_tcp": "SMB文件共享服务 (TCP 445端口) - 现代SMB协议", "misc.message.parse_tcp_udp": "域名解析服务 (TCP+UDP 53端口) - 支持完整DNS功能", "misc.message.tcp_udp": "网络时间协议 (TCP+UDP 123端口) - 时间同步服务", "misc.message.tcp_udp_windows": "分布式计算环境RPC (TCP+UDP 135端口) - Windows AD核心服务", "misc.message.tcp_udp_1": "Kerberos认证协议 (TCP+UDP 88,464端口) - 域认证服务", "misc.message.microsoft_sql_server": "Microsoft SQL Server (TCP 1433,1434端口) - 数据库服务", "misc.message.tcp_udp_2": "简单网络管理协议 (TCP+UDP 161-162端口) - 网络管理", "misc.message.tcp_udp_3": "会话初始协议 (TCP+UDP 5060端口) - VoIP信令", "misc.message.tcp_udp_4": "H.323视频会议协议 (TCP 1720,1503 + UDP 1719) - 多媒体通信", "misc.message.tcp_udp_5": "媒体网关控制协议 (TCP 2428 + UDP 2427,2727) - VoIP网关控制", "misc.message.tcp_udp_6": "实时流协议 (TCP 554,7070,8554 + UDP 554) - 流媒体控制", "misc.message.file_tcp_1": "通用互联网文件系统 (TCP 445端口) - 等同于SMB", "misc.message.udp_2": "路径跟踪协议 (UDP 33434-33535端口) - 网络诊断", "misc.message.content_27887482": "重置自定义服务记录", "misc.message.content_6e4d6109": "优化建议", "misc.message.content_6cbd7b62": "优化计划", "misc.message.content_e0455398": "地址池优化建议引擎", "misc.message.create_45": "生成容量优化建议", "misc.message.create_46": "生成合并优化建议", "misc.message.create_47": "生成碎片化优化建议", "misc.message.create_48": "生成命名优化建议", "misc.message.create_49": "生成性能优化建议", "misc.message.content_26992a59": "查找可合并的池候选", "misc.message.content_9200e52b": "计算池的碎片化程度", "misc.message.content_23578684": "分析命名模式", "misc.message.content_b9b56d5e": "按依赖关系排序建议", "misc.message.content_f078f334": "获取优先级权重", "misc.message.content_a07e1e34": "获取下一个ID", "parser.fortigate.debug.start": "📊 性能监控开始", "parser.fortigate.debug.content_7d5fb5c9": "⏱️ {stage_name}: {duration:.2f}秒", "parser.fortigate.debug.failed": "❌ {stage_name} 失败 ({duration:.2f}秒): {e}", "parser.fortigate.message.file": "配置文件读取", "parser.fortigate.debug.file": "📄 配置文件大小: {file_size / 1024 / 1024:.1f}MB", "parser.fortigate.debug.file_1": "❌ 读取配置文件失败: {e}", "parser.fortigate.debug.file_2": "🔄 使用流式读取处理大文件", "parser.fortigate.debug.content_3f2ac17f": "⚠️ 内存使用较高 ({memory_usage:.1f}MB)，进行优化", "parser.fortigate.message.config": "配置解析", "parser.fortigate.debug.failed_1": "❌ 配置解析失败: {e}", "parser.fortigate.debug.failed_2": "❌ 大型配置解析失败: {e}", "parser.fortigate.message.failed": "大型配置解析失败: {str(e)}", "parser.fortigate.debug.config": "🔄 使用标准配置解析模式", "parser.fortigate.message.config_1": "飞塔配置解析器类", "parser.fortigate.message.parse": "初始化解析器", "parser.fortigate.message.file_1": "文件不存在", "parser.fortigate.message.file_2": "文件为空", "parser.fortigate.message.file_file": "文件过大（>10MB），可能不是正常的配置文件", "parser.fortigate.message.file_3": "文件格式不符合飞塔配置格式", "parser.fortigate.message.config_2": "[需要翻译] config\\s+([\\w\\-]+\\s+[\\w\\-]+)", "parser.fortigate.message.file_4": "配置文件缺少关键部分: {', '.join(missing_sections)}", "parser.fortigate.message.config_3": "配置包含敏感命令: {cmd}", "parser.fortigate.message.config_end": "config/end 数量不匹配: {config_count}/{end_count}", "parser.fortigate.message.edit_next": "edit/next 数量不匹配: {edit_count}/{next_count}", "parser.fortigate.message.check": "验证过程出错: {str(e)}", "parser.fortigate.debug.file_3": "解析配置文件时出错: {str(e)}", "parser.fortigate.message.file_5": "解析配置文件失败: {str(e)}", "parser.fortigate.debug.error": "[需要翻译] [ERROR] {error_msg}", "parser.fortigate.debug.file_error": "[ERROR] 解析配置文件失败", "parser.fortigate.message.content_fbbadd4a": "安全的日志函数", "parser.fortigate.message.content_6324cbf2": "安全的用户日志函数", "parser.fortigate.message.validate": "验证IP地址格式", "parser.fortigate.message.fortigate_warning": "[需要翻译] fortigate.warning.suspicious_file_extension", "parser.fortigate.message.content_36a8d7b8": "检测到敏感命令: {cmd}", "parser.fortigate.message.fortigate_warning_1": "[需要翻译] fortigate.warning.sensitive_command_detected", "parser.fortigate.message.fortigate_warning_2": "[需要翻译] fortigate.warning.unbalanced_config", "parser.fortigate.message.fortigate_warning_3": "[需要翻译] fortigate.warning.unbalanced_edit", "parser.fortigate.message.config_version": "[需要翻译] #config-version=", "parser.fortigate.message.fortigate_error": "[需要翻译] fortigate.error.invalid_format", "parser.fortigate.message.content_88f1e8a2": "行 {i} 太长 ({len(line)}字符)，已截断", "parser.fortigate.message.fortigate_warning_4": "[需要翻译] fortigate.warning.line_too_long", "parser.fortigate.message.content_0ac7ff5d": "行 {i} 包含可疑字符: {line}", "parser.fortigate.message.fortigate_warning_5": "[需要翻译] fortigate.warning.potential_command_injection", "parser.fortigate.message.content_aa52ceb5": "部分 '{section_name}' 重复出现超过100次，可能有问题", "parser.fortigate.message.fortigate_warning_6": "[需要翻译] fortigate.warning.section_repeated", "parser.fortigate.message.config_router_static": "[需要翻译] config router static", "parser.fortigate.message.config_router": "[需要翻译] config router static6", "parser.fortigate.message.config_system_zone": "[需要翻译] config system zone", "parser.fortigate.message.config_firewall_policy": "[需要翻译] config firewall policy", "parser.fortigate.message.config_firewall_vip": "[需要翻译] config firewall vip", "parser.fortigate.message.config_firewall_ippool": "[需要翻译] config firewall ippool", "parser.fortigate.message.config_vpn_ipsec": "[需要翻译] config vpn ipsec phase1-interface", "parser.fortigate.message.config_vpn_ipsec_1": "[需要翻译] config vpn ipsec phase2-interface", "parser.fortigate.message.config_vpn_ssl": "[需要翻译] config vpn ssl web portal", "parser.fortigate.message.config_system_settings": "[需要翻译] config system settings", "parser.fortigate.message.config_system_global": "[需要翻译] config system global", "parser.fortigate.message.config_system_dhcp": "[需要翻译] config system dhcp server", "parser.fortigate.message.config_system_dns": "[需要翻译] config system dns", "parser.fortigate.message.config_user_local": "[需要翻译] config user local", "parser.fortigate.message.config_user_group": "[需要翻译] config user group", "parser.fortigate.message.config_user_setting": "[需要翻译] config user setting", "parser.fortigate.message.config_log_setting": "[需要翻译] config log setting", "parser.fortigate.message.config_log_syslogd": "[需要翻译] config log syslogd", "parser.fortigate.message.config_log": "[需要翻译] config log syslogd2", "parser.fortigate.message.config_log_1": "[需要翻译] config log syslogd3", "parser.fortigate.message.config_log_2": "[需要翻译] config log syslogd4", "parser.fortigate.message.config_log_3": "[需要翻译] config log ", "parser.fortigate.message.config_log_fortianalyzer": "[需要翻译] config log fortianalyzer", "parser.fortigate.message.config_log_4": "[需要翻译] config log fortianalyzer2", "parser.fortigate.message.config_log_5": "[需要翻译] config log fortianalyzer3", "parser.fortigate.message.config_log_memory": "[需要翻译] config log memory filter", "parser.fortigate.message.config_log_disk": "[需要翻译] config log disk filter", "parser.fortigate.message.config_firewall_schedule": "[需要翻译] config firewall schedule group", "parser.fortigate.message.config_firewall_schedule_1": "[需要翻译] config firewall schedule onetime", "parser.fortigate.message.config_firewall_schedule_2": "[需要翻译] config firewall schedule recurring", "parser.fortigate.message.config_firewall_schedule_3": "[需要翻译] config firewall schedule", "parser.fortigate.message.content_cfd5c14a": "接口名称 '{interface_name[:10]}...' 过长，已截断", "parser.fortigate.message.fortigate_warning_7": "[需要翻译] fortigate.warning.interface_name_too_long", "parser.fortigate.message.content_bef01e52": "接口名称 '{interface_name}' 包含可疑字符，已被过滤", "parser.fortigate.message.fortigate_warning_8": "[需要翻译] fortigate.warning.interface_name_suspicious", "parser.fortigate.message.fortigate_warning_9": "[需要翻译] fortigate.warning.skip_interface_conversion", "parser.fortigate.message.content_11da5962": "接口 '{current.get('raw_name')}' 的IP地址 '{ip_addr}' 格式不正确", "parser.fortigate.message.fortigate_warning_10": "[需要翻译] fortigate.warning.invalid_ip_format", "parser.fortigate.message.content_afe1af34": "接口 '{current.get('raw_name')}' 的描述过长，已截断", "parser.fortigate.message.fortigate_warning_11": "[需要翻译] fortigate.warning.description_too_long", "parser.fortigate.message.config_4": "[需要翻译] config ipv6", "parser.fortigate.message.config_secondaryip": "[需要翻译] config secondaryip", "parser.fortigate.message.fortigate_warning_12": "[需要翻译] fortigate.warning.non_root_vdom_detected", "parser.fortigate.message.fortigate_warning_13": "[需要翻译] fortigate.warning.defaultgw_disable_not_supported", "parser.fortigate.message.fortigate_warning_14": "[需要翻译] fortigate.warning.unsupported_interface_type", "parser.fortigate.message.fortigate_warning_15": "[需要翻译] fortigate.warning.unsupported_allowaccess", "parser.fortigate.message.debug": "[DEBUG] 添加静态路由: {current}", "parser.fortigate.message.debug_1": "[DEBUG] 添加IPv6静态路由: {current}", "parser.fortigate.message.debug_2": "[DEBUG] 添加区域: {current}", "parser.fortigate.message.config_firewall_address": "[需要翻译] config firewall address", "parser.fortigate.message.config_firewall_addrgrp": "[需要翻译] config firewall addrgrp", "parser.fortigate.message.config_firewall_service": "[需要翻译] config firewall service group", "parser.fortigate.message.config_firewall_service_1": "[需要翻译] config firewall service custom", "parser.fortigate.message.content_250d7d84": "地址对象名称 '{address_name[:10]}...' 过长，已截断", "parser.fortigate.message.fortigate_warning_16": "[需要翻译] fortigate.warning.address_name_too_long", "parser.fortigate.message.content_3969bca5": "地址对象名称 '{address_name}' 包含可疑字符，已被过滤", "parser.fortigate.message.fortigate_warning_17": "[需要翻译] fortigate.warning.address_name_suspicious", "parser.fortigate.message.content_61081b2b": "地址对象 '{current.get('name')}' 的IP地址 '{ip}' 格式不正确", "parser.fortigate.message.fortigate_warning_18": "[需要翻译] fortigate.warning.invalid_subnet_ip", "parser.fortigate.message.content_2b35ad91": "地址对象 '{current.get('name')}' 的FQDN '{fqdn}' 格式可能不正确", "parser.fortigate.message.fortigate_warning_19": "[需要翻译] fortigate.warning.invalid_fqdn_format", "parser.fortigate.message.content_b984993b": "地址对象 '{current.get('name')}' 的起始IP '{start_ip}' 格式不正确", "parser.fortigate.message.fortigate_warning_20": "[需要翻译] fortigate.warning.invalid_start_ip", "parser.fortigate.message.content_36e6c2dc": "地址对象 '{current.get('name')}' 的结束IP '{end_ip}' 格式不正确", "parser.fortigate.message.fortigate_warning_21": "[需要翻译] fortigate.warning.invalid_end_ip", "parser.fortigate.debug.debug": "DEBUG: 过滤掉UUID: {member}", "parser.fortigate.message.strategy": "策略ID '{policy_id}' 不是有效的数字", "parser.fortigate.message.fortigate_warning_22": "[需要翻译] fortigate.warning.invalid_policy_id", "parser.fortigate.message.strategy_1": "策略 '{current.get('policyid')}' 缺少必要字段: {', '.join(missing_fields)}", "parser.fortigate.message.fortigate_warning_23": "[需要翻译] fortigate.warning.policy_missing_fields", "parser.fortigate.message.strategy_error": "策略 '{current.get('policyid')}' 的服务列表异常大 ({len(current['service'])}项)", "parser.fortigate.message.fortigate_warning_24": "[需要翻译] fortigate.warning.service_list_too_large", "parser.fortigate.message.strategy_error_1": "策略 '{current.get('policyid')}' 的源地址列表异常大 ({len(current['srcaddr'])}项)", "parser.fortigate.message.fortigate_warning_25": "[需要翻译] fortigate.warning.srcaddr_list_too_large", "parser.fortigate.message.strategy_error_2": "策略 '{current.get('policyid')}' 的目标地址列表异常大 ({len(current['dstaddr'])}项)", "parser.fortigate.message.fortigate_warning_26": "[需要翻译] fortigate.warning.dstaddr_list_too_large", "parser.fortigate.message.strategy_2": "策略 '{current.get('policyid')}' 的注释过长，已截断", "parser.fortigate.message.fortigate_warning_27": "[需要翻译] fortigate.warning.policy_comment_too_long", "parser.fortigate.message.content_76587c01": "策略缺少ID，跳过", "parser.fortigate.message.fortigate_warning_28": "[需要翻译] fortigate.warning.policy_missing_id", "parser.fortigate.message.strategy_3": "策略 '{current.get('policyid')}' 的源接口 '{intf}' 包含可疑字符", "parser.fortigate.message.fortigate_warning_29": "[需要翻译] fortigate.warning.srcintf_suspicious", "parser.fortigate.message.strategy_4": "策略 '{current.get('policyid')}' 的目标接口 '{intf}' 包含可疑字符", "parser.fortigate.message.fortigate_warning_30": "[需要翻译] fortigate.warning.dstintf_suspicious", "parser.fortigate.message.strategy_5": "策略 '{current.get('policyid')}' 的源地址 '{addr}' 包含可疑字符", "parser.fortigate.message.fortigate_warning_31": "[需要翻译] fortigate.warning.srcaddr_suspicious", "parser.fortigate.message.strategy_6": "策略 '{current.get('policyid')}' 的目标地址 '{addr}' 包含可疑字符", "parser.fortigate.message.fortigate_warning_32": "[需要翻译] fortigate.warning.dstaddr_suspicious", "parser.fortigate.message.strategy_7": "策略 '{current.get('policyid')}' 的服务 '{svc}' 包含可疑字符", "parser.fortigate.message.fortigate_warning_33": "[需要翻译] fortigate.warning.service_suspicious", "parser.fortigate.message.strategy_8": "策略 '{current.get('policyid')}' 使用了未知的动作 '{action}'", "parser.fortigate.message.fortigate_warning_34": "[需要翻译] fortigate.warning.unknown_action", "parser.fortigate.message.strategy_9": "策略 '{current.get('policyid')}' 的名称过长，已截断", "parser.fortigate.message.fortigate_warning_35": "[需要翻译] fortigate.warning.policy_name_too_long", "parser.fortigate.message.fortigate_warning_36": "[需要翻译] fortigate.warning.comment_too_long", "parser.fortigate.message.content_17774ef2": "地址组名称 '{name}' 过长", "parser.fortigate.message.fortigate_warning_37": "[需要翻译] fortigate.warning.addrgrp_name_too_long", "parser.fortigate.message.content_1d2f3e40": "地址组名称 '{name}' 包含可疑字符", "parser.fortigate.message.fortigate_warning_38": "[需要翻译] fortigate.warning.addrgrp_name_suspicious", "parser.fortigate.message.content_1019a32a": "地址组 '{current.get('name')}' 不包含任何成员", "parser.fortigate.message.fortigate_warning_39": "[需要翻译] fortigate.warning.addrgrp_no_members", "parser.fortigate.message.error": "地址组 '{current.get('name')}' 的成员数量异常大 ({len(current['member'])}个)", "parser.fortigate.message.fortigate_warning_40": "[需要翻译] fortigate.warning.addrgrp_members_too_many", "parser.fortigate.message.content_a37f3667": "地址组 '{current.get('name')}' 的成员 '{member}' 包含可疑字符", "parser.fortigate.message.fortigate_warning_41": "[需要翻译] fortigate.warning.addrgrp_member_suspicious", "parser.fortigate.message.content_a3b8ab2a": "地址组 '{current.get('name')}' 的注释过长，已截断", "parser.fortigate.message.config_range": "[需要翻译] config ip-range", "parser.fortigate.message.fortigate_warning_42": "[需要翻译] fortigate.warning.dhcp_ip_range_parse_error", "parser.fortigate.message.config_options": "[需要翻译] config options", "parser.fortigate.message.fortigate_warning_43": "[需要翻译] fortigate.warning.dhcp_options_parse_error", "parser.fortigate.message.set_auth_timeout": "[需要翻译] set auth-timeout", "parser.fortigate.message.set_auth_invalid": "[需要翻译] set auth-invalid-max", "parser.fortigate.message.set_log_invalid": "[需要翻译] set log-invalid-packet", "parser.fortigate.message.set_upload_option": "[需要翻译] set upload-option", "parser.fortigate.message.fortigate_warning_44": "[需要翻译] fortigate.warning.schedule_name_too_long_msg", "parser.fortigate.message.fortigate_warning_45": "[需要翻译] fortigate.warning.schedule_name_too_long", "parser.fortigate.message.fortigate_warning_46": "[需要翻译] fortigate.warning.schedule_name_suspicious_msg", "parser.fortigate.message.fortigate_warning_47": "[需要翻译] fortigate.warning.schedule_name_suspicious", "parser.fortigate.message.fortigate_warning_48": "[需要翻译] fortigate.warning.schedule_only_uuid_msg", "parser.fortigate.message.fortigate_warning_49": "[需要翻译] fortigate.warning.schedule_only_uuid", "parser.fortigate.message.fortigate_warning_50": "[需要翻译] fortigate.warning.schedule_empty_msg", "parser.fortigate.message.fortigate_warning_51": "[需要翻译] fortigate.warning.schedule_empty", "parser.fortigate.message.parse_parse": "检测到可能的解析循环，解析提前终止", "parser.fortigate.message.fortigate_warning_52": "[需要翻译] fortigate.warning.dns_servers_exceed_limit", "parser.fortigate.message.content_ea48b519": "DNS服务器数量({len(dns_servers)})超过NTOS限制(3个)，只转换前3个", "parser.fortigate.message.config_5": "未解析到任何配置项", "parser.fortigate.message.fortigate_warning_53": "[需要翻译] fortigate.warning.no_items_parsed", "parser.fortigate.message.error_1": "解析到的配置项数量异常（{total_items}个）", "parser.fortigate.message.fortigate_warning_54": "[需要翻译] fortigate.warning.too_many_items", "parser.fortigate.message.process_process": "无法导入接口处理工具函数，跳过PPPoE过滤和密码掩码处理", "parser.fortigate.message.error_2": "接口处理过程中出现错误: {str(e)}", "parser.fortigate.message.parse_config": "解析DHCP IP范围配置", "parser.fortigate.message.parse_config_1": "解析DHCP选项配置", "misc.message.process_16": "FortiGate配置预处理器", "misc.message.process_17": "初始化预处理器", "misc.message.config_switch_controller": "[需要翻译] ^config switch-controller.*", "misc.message.config_wireless_controller": "[需要翻译] ^config wireless-controller.*", "misc.message.config_system_automation": "[需要翻译] ^config system automation-.*", "misc.message.config_system_fabric_1": "[需要翻译] ^config system fabric.*", "misc.message.config_system_sdn": "[需要翻译] ^config system sdn-.*", "misc.message.config_system_security": "[需要翻译] ^config system security-rating.*", "misc.message.config_system_auto_1": "[需要翻译] ^config system auto-auth.*", "misc.message.config_system_fabric_2": "[需要翻译] ^config system fabric-object.*", "misc.message.config_log_threat": "[需要翻译] ^config log.*threat-weight.*", "misc.message.config_log_custom": "[需要翻译] ^config log.*custom-field.*", "misc.message.config_application_custom": "[需要翻译] ^config application.*custom.*", "misc.message.config_dlp_doc": "[需要翻译] ^config dlp.*fp-doc-source.*", "misc.message.config_webfilter_override": "[需要翻译] ^config webfilter.*override.*", "misc.message.config_antivirus_heuristic": "[需要翻译] ^config antivirus.*heuristic.*", "misc.message.config_system_global_1": "[需要翻译] ^config system global.*", "misc.message.config_system_interface_2": "[需要翻译] ^config system interface.*", "misc.message.config_firewall_address_1": "[需要翻译] ^config firewall address.*", "misc.message.config_firewall_addrgrp_1": "[需要翻译] ^config firewall addrgrp.*", "misc.message.config_firewall_service_2": "[需要翻译] ^config firewall service.*", "misc.message.config_firewall_policy_2": "[需要翻译] ^config firewall policy.*", "misc.message.config_firewall_vip": "[需要翻译] ^config firewall vip.*", "misc.message.config_firewall_ippool": "[需要翻译] ^config firewall ippool.*", "misc.message.config_system_zone_1": "[需要翻译] ^config system zone.*", "misc.message.config_router_static_1": "[需要翻译] ^config router static.*", "misc.message.config_system_dns_2": "[需要翻译] ^config system dns.*", "misc.message.config_firewall_schedule_2": "[需要翻译] ^config firewall schedule.*", "misc.message.content_d2d37a60": "检测FortiOS版本", "misc.message.config_version_2": "[需要翻译] #config-version=([^:]+):([^:]+):([^-]+)-([^-]+)", "misc.info.content_19cbf30a": "检测到build号 {build_no}，推断为FortiOS 7.6+", "misc.info.content_85f3ccef": "检测到FortiOS版本: {version}", "misc.message.config_switch_controller_1": "[需要翻译] config switch-controller", "misc.info.config": "检测到switch-controller配置，推断为FortiOS 7.6+", "misc.info.process": "无法检测FortiOS版本，使用默认处理", "misc.info.failed_2": "版本检测失败: {str(e)}", "misc.message.content_1e42e495": "检测设备主机名", "misc.info.content_0690987d": "检测到设备主机名: {hostname}", "misc.info.failed_3": "主机名检测失败: {str(e)}", "misc.message.config_64": "判断是否应该过滤某个配置段落", "misc.info.config_1": "过滤配置段落: {section_header.strip()}", "misc.message.config_65": "分析配置复杂度", "misc.message.config_66": "[需要翻译] ^config\\s+", "misc.message.config_switch_controller_2": "[需要翻译] ^config\\s+switch-controller", "misc.message.config_wireless_controller_1": "[需要翻译] ^config\\s+wireless-controller", "misc.message.config_system_automation_1": "[需要翻译] ^config\\s+system\\s+automation", "misc.info.file": "开始预处理配置文件: {config_file_path}", "misc.info.config_2": "配置复杂度分析: 评分={complexity['complexity_score']}, ", "misc.message.switch": "switch-controller段落={complexity['switch_controller_sections']}, ", "misc.message.content_34a1739e": "edit条目={complexity['edit_entries']}", "misc.info.config_process": "配置复杂度较低，跳过预处理", "misc.info.success_1": "预处理完成:", "misc.info.content_0019a3f4": "  原始行数: {len(lines)}", "misc.info.process_1": "  处理后行数: {len(processed_lines)}", "misc.info.content_a42bb5f2": "  减少比例: {reduction_rate:.1f}%", "misc.info.content_c0b56db0": "  过滤段落数: {self.statistics['removed_sections']}", "misc.info.process_2": "  处理耗时: {self.statistics['processing_time']:.3f}秒", "misc.info.file_1": "  输出文件: {output_file_path}", "misc.info.failed_4": "配置预处理失败: {str(e)}", "misc.message.config_67": "过滤配置行", "misc.info.content_d1aa8016": "过滤段落: {stripped_line}", "misc.message.config_68": "找到配置段落的结束位置", "misc.message.process_18": "获取处理统计信息", "misc.message.process_19": "生成预处理报告", "misc.message.content_5847b0f9": "未检测到", "misc.info.process_3": "预处理报告已生成: {output_path}", "misc.message.parse_1": "解析器工具类", "misc.message.config_69": "[需要翻译] config {section_name}", "misc.message.parse_2": "测试解析器工具类", "misc.message.content_b1c3f60e": "测试提取引号包围的值", "misc.message.content_5de8b9f5": "测试提取关键字后的值", "misc.message.convert_12": "测试掩码转换为前缀长度", "misc.message.start_4": "测试区段开始检测", "misc.message.content_3105e58e": "测试编辑行检测", "misc.message.content_9840fd12": "测试获取编辑名称", "misc.message.parse_3": "测试飞塔解析器", "misc.message.content_0dbceabc": "测试前准备", "misc.message.content_5a6a5532": "测试后清理", "misc.message.parse_4": "测试完整解析", "misc.message.config_70": "测试提取接口配置", "misc.message.config_71": "测试提取静态路由配置", "misc.message.config_72": "测试提取策略规则配置", "misc.message.config_73": "测试提取DHCP配置", "misc.message.config_74": "测试提取DNS配置", "misc.message.config_75": "测试提取认证配置", "misc.message.config_76": "测试提取日志配置", "misc.message.error_77": "测试错误处理", "misc.message.content_c2377022": "测试FortigateParser的安全校验机制", "misc.message.content_f04f1fc6": "测试前的准备工作", "misc.message.content_98a0b61a": "测试后的清理工作", "misc.message.file_19": "创建测试文件并返回其路径", "misc.message.file_20": "测试文件存在性检查", "misc.message.file_21": "测试文件大小检查", "misc.message.file_22": "# 这是一个大文件测试\\n", "misc.message.file_23": "测试文件扩展名检查", "misc.message.config_system_global_2": "[需要翻译] config system global\\nend", "misc.message.file_24": "可疑的文件扩展名", "misc.message.check_17": "测试敏感命令检查", "misc.message.content_c4ff7a52": "敏感命令", "misc.message.config_77": "测试配置段平衡检查", "misc.message.config_78": "配置段不平衡", "misc.message.content_a22726a3": "测试字符串清理功能", "misc.message.check_18": "测试IP地址验证功能", "misc.message.check_19": "测试CIDR验证功能", "misc.message.check_20": "测试地址对象安全检查", "misc.message.content_54f875c0": "可疑字符", "misc.message.check_21": "测试策略安全检查", "misc.message.strategy_1": "策略ID", "misc.message.content_b7fe6c86": "不是有效的数字", "misc.message.check_22": "测试命令注入检查", "misc.message.content_3a1c09fc": "测试大列表截断功能", "misc.message.error_78": "成员数量异常大", "misc.message.content_a0b09a03": "测试无效格式检测", "misc.message.content_fa8d4e05": "格式可能不正确", "misc.message.file_25": "测试配置文件验证功能", "misc.message.create_50": "初始化生成器注册表", "misc.message.create_51": "注册内置生成器", "misc.message.create_52": "清空生成器实例缓存", "misc.message.create_53": "初始化NAT生成器适配器", "misc.message.create_54": "初始化安全策略生成器适配器", "misc.message.create_55": "获取现有生成器实例", "misc.message.create_56": "初始化XML生成器适配器", "misc.message.parse_5": "初始化Fortigate解析器适配器", "misc.message.parse_6": "获取现有解析器实例", "misc.message.parse_7": "初始化解析器注册表", "misc.message.parse_8": "注册内置解析器", "misc.message.parse_9": "清空解析器实例缓存", "misc.message.content_818d8403": "数据上下文添加警告: {warning_message} (阶段: {stage})", "misc.message.pipeline_stopped_due": "[需要翻译] Pipeline stopped due to error: {self.name} -> {stage.name}", "misc.message.stage_failed_continuing": "[需要翻译] Stage failed but continuing execution: {self.name} -> {stage.name}", "misc.message.pipeline_execution_exception": "[需要翻译] Pipeline execution exception: {self.name}, error: {str(e)}", "misc.message.pipeline_execution_completed": "[需要翻译] Pipeline execution completed: {self.name}, state: {context.state}, duration: {total_duration:.2f}s, errors: {len(context.get_errors())}, warnings: {len(context.get_warnings())}", "misc.message.content_e933acda": "启用管道", "misc.message.content_b93c9ea5": "禁用管道", "misc.message.content_2c4e5c45": "启用阶段", "misc.message.content_1eb4ec28": "禁用阶段", "misc.message.content_24e2271f": "添加跳过的结果", "misc.message.failed_11": "添加失败的结果", "misc.message.failed_12": "地址对象组处理失败: {str(e)}", "misc.message.convert_13": "VIP对象转换为地址对象", "misc.message.content_fddbfced": "VIP对象 {vip_name}", "misc.message.create_missing": "动态生成的地址对象 {addr_name} 缺少start_ip或end_ip字段", "misc.message.create_57": "动态生成的地址对象 {addr_name} 包含无效的IP范围: {start_ip}-{end_ip}", "misc.message.convert_14": "从FortiGate服务对象转换的地址对象", "misc.message.success_10": "成功转换动态生成的地址对象: {addr_name} ({start_ip}-{end_ip}), 源服务: {result['source_service']}", "misc.message.create_58": "发现 {len(generated_addresses)} 个动态生成的地址对象", "misc.message.create_59": "地址对象名称冲突，动态生成的对象将覆盖原有对象: {addr_name}", "misc.message.process_20": "添加动态生成的地址对象到处理队列: {addr_name}", "misc.message.content_104dcc42": "建立服务对象映射: {source_service} -> {addr_name} (语义: {addr_obj.get('iprange_semantic', 'destination')})", "misc.message.create_vip": "地址对象统计: 原始={len(address_objects)-generated_count}, VIP={vip_count}, 动态生成={generated_count}, 总计={total_objects}", "misc.message.content_e66be1e1": "建立了 {len(service_to_address_mapping)} 个服务对象到地址对象的映射关系", "misc.message.content_d91820fa": "  服务 '{service_name}' -> {len(mappings)} 个地址对象: {[m['address_name'] for m in mappings]}", "misc.message.failed_13": "地址对象处理失败: {str(e)}", "misc.message.content_a8a6c340": "为保持与原版一致性，保留没有IP地址的地址对象", "misc.message.content_7d71e929": "返回空结果", "misc.message.failed_14": "DNS处理失败: {str(e)}", "misc.message.dns_config": "[需要翻译] dns-config", "misc.message.process_21": "加密处理阶段", "misc.message.content_b74431fb": "无法找到XML内容进行加密。generated_xml: {generated_xml is not None}, output_file: {output_file}, file_exists: {os.path.exists(output_file) if output_file else False}", "misc.message.error_79": "未知错误", "misc.message.failed_15": "加密处理失败: {encryption_result.get('error', '未知错误')}", "misc.message.failed_16": "加密模块导入失败: {str(e)}", "misc.message.failed_17": "加密执行失败: {str(e)}", "misc.message.pipeline_error_handling": "[需要翻译] Pipeline error handling decorator", "misc.message.unknown_error": "[需要翻译] Unknown error", "misc.message.warning_fortigatepolicyconvers": "[需要翻译] WARNING: FortigatePolicyConversionStage - NAT rules count seems low, checking alternative sources", "misc.message.process_22": "接口处理阶段", "misc.message.start_debug": "DEBUG: 开始生成XML片段，converted接口数量: {len(processing_result.get('converted', {}))}", "misc.message.debug_5": "DEBUG: physical_interfaces数量: {len(processing_result.get('physical_interfaces', {}))}", "misc.message.debug_6": "DEBUG: vlan_interfaces数量: {len(processing_result.get('vlan_interfaces', {}))}", "misc.message.success_debug_2": "DEBUG: XML片段生成完成，长度: {len(xml_fragment)}", "misc.message.convert_15": "确定接口类型，支持类型转换", "misc.message.config_79": "转换接口配置", "misc.message.convert_16": "转换物理接口", "misc.message.convert_process": "转换VLAN接口 - 集成旧架构的完整子接口处理逻辑", "misc.message.convert_17": "转换环回接口", "misc.message.process_config_1": "处理IP配置", "misc.message.process_23": "处理安全区域", "misc.message.content_5906ba6b": "根据接口名推断安全区域", "misc.message.create_debug": "DEBUG: XML片段生成 - converted_interfaces: {len(converted_interfaces)}", "misc.message.create_debug_1": "DEBUG: XML片段生成 - vlan_interfaces: {len(vlan_interfaces)}", "misc.message.create_debug_2": "DEBUG: XML片段生成 - physical_interfaces: {len(physical_interfaces)}", "misc.message.debug_7": "DEBUG: converted_interfaces为空，返回空XML片段", "misc.message.process_24": "操作模式检测和处理阶段", "misc.message.load_2": "加载设备接口信息", "misc.message.config_mgmt": "透明模式下必须配置'mgmt'接口的映射关系。", "misc.message.file_config_mgmt": "请在接口映射文件中添加'mgmt'接口的NTOS映射配置，", "misc.message.content_4f70e655": "例如: {\\\"mgmt\\\": \\\"Ge0/2\\\"}", "misc.message.config_mgmt_1": "透明模式下'mgmt'接口映射配置无效。", "misc.message.mgmt": "请确保'mgmt'接口映射到有效的NTOS接口名称，", "misc.message.content_1716d086": "警告: NTOS版本 {ntos_version} 不支持av功能，跳过av-profile配置", "misc.message.content_03b3d27e": "警告: NTOS版本 {ntos_version} 不支持ips功能，跳过ips-sensor配置", "misc.message.content_ace2a7b6": "警告: NTOS版本 {ntos_version} 不支持websec功能，跳过webfilter-profile配置", "misc.message.filter": "警告: NTOS版本 {ntos_version} 不支持content-filter功能，跳过content-filter-profile配置", "misc.message.filter_1": "警告: NTOS版本 {ntos_version} 不支持file-filter功能，跳过file-filter-profile配置", "misc.message.error_failed_process": "[需要翻译] ERROR: Failed to process IP pool '{pool_name}': {str(e)}", "misc.message.warning_pool_missing": "[需要翻译] WARNING: IP pool '{pool_name}' missing required field '{field}'", "misc.message.warning_pool_invalid": "[需要翻译] WARNING: IP pool '{pool_name}' has invalid range: start IP {start_ip} > end IP {end_ip}", "misc.message.warning_pool_invalid_1": "[需要翻译] WARNING: IP pool '{pool_name}' has invalid IP address format: {str(e)}", "misc.message.failed_18": "接口映射验证失败", "misc.message.process_25": "服务对象组处理阶段", "misc.message.failed_19": "服务对象组处理失败: {str(e)}", "misc.message.config_data_type": "[需要翻译] Config data type: {type(config_data)}", "misc.message.load_3": "加载服务映射配置", "misc.message.check_23": "检查是否为预定义服务", "misc.message.content_8f8b390e": "获取预定义服务映射", "misc.message.convert_18": "转换TCP服务，支持源端口和目的端口分离", "misc.message.content_7f742956": "警告: 服务 {service_name} 包含不支持的iprange字段: {iprange_value}", "misc.message.convert_19": "转换UDP服务，支持源端口和目的端口分离", "misc.message.convert_20": "转换TCP+UDP多协议服务，支持源端口和目的端口分离", "misc.message.convert_21": "转换SCTP服务（映射为TCP）", "misc.message.convert_22": "转换ICMP服务", "misc.message.convert_23": "转换IP协议服务", "misc.message.convert_24": "为保持与原版一致性，跳过服务对象iprange转换", "misc.message.start_convert": "开始处理服务对象iprange转换", "misc.message.success_process": "iprange转换完成: 处理了{iprange_stats['services_with_iprange']}个包含iprange的服务对象, ", "misc.message.success_11": "成功生成{iprange_stats['successful_conversions']}个地址对象", "misc.message.content_8be244a1": "跳过预定义服务: {service_name}", "misc.message.content_67442a4a": "添加TCP协议XML，支持源端口和目的端口", "misc.message.content_dc4f5bbc": "添加UDP协议XML，支持源端口和目的端口", "misc.message.content_8d77beff": "添加TCP+UDP多协议XML，支持源端口和目的端口", "misc.message.content_030d43c8": "添加ICMP协议XML（遵循YANG模型的type/code键约束）", "misc.message.content_e7f9075d": "添加IP协议XML", "misc.message.content_d7f43133": "一次性时间范围 {name}", "misc.message.content_b3ddde78": "时间对象组 {name}", "misc.message.content_71f85345": "不支持的时间对象类型: {schedule_type}", "misc.message.error_80": "时间对象处理错误: {str(e)}", "misc.message.process_26": "时间对象处理阶段", "misc.message.start_convert_1": "开始生成时间对象XML片段，转换结果: {time_result.get('converted_count', 0)} 个", "misc.message.success_12": "XML片段生成完成，长度: {len(xml_fragment) if xml_fragment else 0}", "misc.message.failed_20": "时间对象处理失败: {str(e)}", "misc.message.create_convert": "XML片段生成：找到 {len(converted_objects)} 个转换的时间对象", "misc.message.create_convert_1": "XML片段生成：没有转换的时间对象，返回空字符串", "misc.message.success_13": "XML片段生成成功，长度: {len(xml_string)}", "misc.message.convert_25": "XML模板集成阶段 - 将转换结果集成到XML模板", "misc.message.content_656b3c41": "XML片段", "misc.message.load_model_version": "模板加载返回空值: model={model}, version={version}", "misc.message.failed_error_model": "XML模板加载失败: model={model}, version={version}, 错误={str(e)}", "misc.message.content_09b11df5": "安全策略", "misc.message.error_81": "安全策略集成异常: {str(e)}", "misc.message.content_5f219f39": "NAT规则", "misc.message.error_82": "NAT规则集成异常: {str(e)}", "misc.message.twice_4": "twice-nat44规则", "misc.message.config_config": "XML根元素不是'config': 实际='{local_tag}', 期望='config'", "misc.message.xmlns": "XML命名空间不匹配: xmlns='{namespace}', tag_ns='{tag_namespace}', nsmap_ns='{nsmap_namespace}', 期望='{expected_namespace}'", "misc.message.content_99c01799": "XML模板中未找到VRF元素", "misc.message.time": "发现 {len(global_time_range_nodes)} 个全局time-range容器和 {len(nat_time_range_nodes)} 个NAT time-range引用", "misc.message.container": "发现 {len(global_time_range_nodes)} 个重复的全局time-range容器，进行合并", "misc.message.failed_21": "合并容器内容失败: {str(e)}", "misc.message.config_80": "接口配置", "misc.message.content_7d32595c": "找到 {len(unique_vlan_elements)} 个VLAN接口元素", "misc.message.success_14": "成功集成VLAN接口: {vlan_elem.find('name').text if vlan_elem.find('name') is not None else 'unknown'}", "misc.message.remove_7": "移除现有的地址配置", "misc.message.remove_config": "移除现有的DHCP配置", "misc.message.remove_config_1": "移除现有的PPPoE配置", "misc.message.update_config": "更新VLAN接口access-control配置时出错: {str(e)}", "misc.message.error_83": "错误详情: {traceback.format_exc()}", "misc.message.content_b5d0dc6b": "接口 {interface_name} - 未找到 {service} 元素，跳过更新", "misc.message.update_config_1": "更新VLAN接口IPv4配置时出错: {str(e)}", "misc.message.create_60": "无法为VLAN接口 {interface_name} 创建IPv4节点", "misc.message.content_6e558451": "VLAN接口 {interface_name} - 静态IP模式但未找到IP地址", "misc.message.config_81": "VLAN接口 {interface_name} - 配置静态IP: {ip_address}", "misc.message.config_82": "配置VLAN接口静态IP时出错: {str(e)}", "misc.message.config_83": "VLAN接口 {interface_name} - 配置DHCP模式", "misc.message.config_84": "配置VLAN接口DHCP时出错: {str(e)}", "misc.message.config_85": "VLAN接口 {interface_name} - 配置PPPoE模式，用户名: {username}", "misc.message.config_86": "配置VLAN接口PPPoE时出错: {str(e)}", "misc.message.config_87": "未找到VLAN接口 {interface_name} 的配置数据", "misc.message.config_88": "获取VLAN接口配置数据时出错: {str(e)}", "misc.message.content_1108bf18": "地址对象", "misc.message.error_84": "地址对象组集成异常: {str(e)}", "misc.message.content_016ca7a1": "服务对象", "misc.message.error_85": "服务对象集成异常: {str(e)}", "misc.message.error_86": "服务对象组集成异常: {str(e)}", "misc.message.content_9d951ac8": "安全区域", "misc.message.error_87": "安全区域集成异常: {str(e)}", "misc.message.file_26": "调试文件已保存: {debug_file_path}", "misc.message.file_27": "警告: 调试文件写入可能失败: {debug_file_path}", "misc.message.file_process": "调试文件写入失败: {str(file_error)}，继续处理时间对象集成", "misc.message.content_cd649f76": "时间范围", "misc.message.file_28": "时间对象集成文件操作异常: {str(e)}", "misc.message.error_88": "时间对象XML解析异常: {str(e)}", "misc.message.error_89": "时间对象集成异常: {str(e)}", "misc.message.config_89": "DNS配置", "misc.message.error_90": "DNS配置集成异常: {str(e)}", "misc.message.content_93820fd3": "静态路由", "misc.message.error_91": "静态路由集成异常: {str(e)}", "misc.message.success_15": "成功处理", "misc.message.check_check_1": "YANG验证阶段 - 使用YANG模型验证XML配置", "misc.message.check_24": "YANG验证已跳过：yanglint工具不可用", "misc.message.check_25": "YANG验证诊断信息: {diagnostic_info}", "misc.message.check_26": "内置验证规则已应用", "misc.message.https_github_com": "请从 https://github.com/CESNET/libyang 下载并安装yanglint工具", "misc.message.git_clone_https": "编译安装：git clone https://github.com/CESNET/libyang && cd libyang && mkdir build && cd build && cmake .. && make && sudo make install", "misc.message.dir_3": "YANG模型目录不存在: {yang_model_path}", "misc.message.interface_mapping_file": "[需要翻译] Interface mapping file not found: {mapping_file}", "misc.message.failed_load_interface_1": "[需要翻译] Failed to load interface mapping: {str(e)}", "misc.message.config_90": "接口 {interface} 未配置映射关系", "misc.message.content_4b46fca0": "缺少接口映射", "misc.message.file_29": "请在映射文件中添加该接口的映射关系", "misc.message.interface_mapping_completed": "[需要翻译] Interface mapping completed for zone {zone_name}: {len(valid_interfaces)} mapped, {len(unmapped_interfaces)} unmapped", "misc.message.starting_create_zone": "[需要翻译] Starting to create zone configuration: {zone_name}", "misc.message.fortigate_configuration_contai": "[需要翻译] FortiGate configuration contains {len(fortigate_config)} fields", "misc.message.using_default_zone": "[需要翻译] Using default zone configuration: {zone_name}", "misc.message.zone": "Zone {zone_name}: 使用FortiGate描述 '{fortigate_config['description']}'", "misc.message.config_91": "区域 {zone_name}: {len(unmapped_interfaces)} 个接口因未配置映射关系被跳过", "misc.message.content_bd9adffe": "{zone_name}安全域接口间的访问不会默认放行", "misc.message.zone_processing_failed": "[需要翻译] Zone processing failed: {zone_name} - {reason}", "misc.message.create_61": "自定义区域 {zone_name} 没有有效接口，但仍然创建以支持策略引用", "misc.message.config_92": "区域 {zone_name}: 接口 {', '.join(unmapped_interfaces)} 未配置映射关系，已跳过", "misc.message.content_05e903ad": " (⚠️警告)", "misc.message.zone_processing_error": "[需要翻译] Zone processing error: {str(e)}", "misc.message.zone_processing_exception": "[需要翻译] Zone processing exception: {str(e)}", "misc.message.zone_processing_failed_1": "[需要翻译] Zone processing failed: {str(e)}", "misc.message.interface_processing_result": "[需要翻译] Interface processing result type: {type(interface_result)}", "misc.message.create_62": "区域 {zone_name} 没有绑定接口，创建无接口区域以支持策略引用", "misc.message.failed_generate_zone": "[需要翻译] Failed to generate zone XML fragment: {str(e)}", "processor.address.message.process": "飞塔地址对象处理器类", "processor.address.message.process_1": "初始化处理器", "processor.address.message.process_2": "处理subnet/ipmask类型的地址对象", "processor.address.message.process_3": "处理iprange类型的地址对象", "processor.address.message.process_create": "处理range类型的地址对象（动态生成的地址对象）", "misc.message.process_nat": "NAT 规则处理器类", "misc.message.content_0b1f5700": "初始化所有增强组件", "misc.message.process_27": "处理地址池名称变更", "misc.message.pool": "[需要翻译] Pool {pool_name}: {error}", "misc.message.pool_yang_validation": "[需要翻译] Pool {pool_name} YANG validation: {error}", "misc.message.pool_processing_failed": "[需要翻译] Pool {pool_name} processing failed: {str(e)}", "misc.message.convert_26": "=== FortiGate地址池转换综合分析报告 ===", "misc.message.process_28": "处理的地址池数量: {len(nat_pools)}", "misc.message.failed_22": "使用分析失败: {str(e)}", "misc.message.check_27": "=== 引用完整性检查 ===", "misc.message.content_7b9341e0": "总池数: {integrity_result.total_pools}", "misc.message.content_0473460c": "引用池数: {integrity_result.referenced_pools}", "misc.message.content_001ceeaa": "孤立池数: {len(integrity_result.orphaned_pools)}", "misc.message.content_0d21b966": "孤立池: {', '.join(integrity_result.orphaned_pools[:5])}", "misc.message.content_cd033153": "  ... 等{len(integrity_result.orphaned_pools)}个", "misc.message.content_dc0514d4": "缺失池: {', '.join(integrity_result.missing_pools[:5])}", "misc.message.failed_23": "引用完整性检查失败: {str(e)}", "misc.message.content_42a0927a": "{i}. {suggestion.title} (优先级: {priority_text})", "misc.message.content_97aa06f9": " ... 等{len(suggestion.affected_pools)}个", "misc.message.content_b7e3f8f2": "   涉及池: {pools_text}", "misc.message.content_84b2c5c9": "... 还有{len(suggestions) - 10}个建议", "misc.message.content_761a6925": "未发现需要优化的问题", "misc.message.failed_24": "优化建议生成失败: {str(e)}", "misc.message.strategy_2": "DNAT源网络-策略{policy_name}", "misc.message.content_9ec2c04f": "{clean_intf}→未映射", "misc.message.error_92": "{clean_intf}→错误:{str(e)}", "misc.message.yang_dest": "YANG choice约束：dest-network与dest-if互斥", "misc.message.strategy_3": "SNAT源网络-策略{policy_name}", "misc.message.strategy_4": "SNAT服务-策略{policy_name}", "misc.message.strategy_load": "策略iprange集成器：加载了 {len(self.service_to_address_mapping)} 个服务映射关系", "misc.message.content_f6a94d81": "  - 服务 {service_name}: {len(mappings)} 个地址映射", "misc.message.content_b4acae3c": "无法确定策略字段映射: {field_name}", "misc.message.strategy_5": "策略 {policy.get('name', 'unknown')} 已集成iprange地址对象: {added_addresses}", "processor.policy.message.process_fortigate": "Fortigate 策略转换处理器类", "processor.policy.message.error_exception": "[需要翻译] ERROR: Exception in _is_zone_name for '{name}': {str(e)}", "processor.policy.message.content_28302901": "诊断建议: {s}", "processor.policy.message.content_d443eec5": "源接口", "processor.policy.message.content_3c27aa27": "目标接口", "processor.policy.message.config": "建议: 添加{intf_name}配置", "processor.policy.message.check_any": "建议: 检查{intf_name} '{intf}' 是否正确，建议使用有效的接口名称如 'wan1', 'lan1', 'dmz1' 或特殊接口 'any'", "processor.policy.message.error_config": "建议: {intf_name} '{intf}' 可能存在拼写错误，请检查接口映射配置", "processor.policy.message.config_poolname": "建议: NAT启用时应配置IP池名称 (poolname)", "processor.policy.message.content_d59a4004": "建议: 确认VIP对象 '{addr}' 已正确配置", "processor.policy.message.config_http_https": "建议: 添加服务配置，如 'ALL', 'HTTP', 'HTTPS' 等", "processor.policy.message.content_efac3507": "建议: 使用 'ALL' 服务时，无需配置其他具体服务", "processor.policy.message.config_accept_deny": "建议: 添加策略动作配置 (accept/deny)", "processor.policy.message.accept_deny": "建议: 策略动作 '{action}' 无效，请使用 'accept' 或 'deny'", "processor.policy.message.status_enable": "信息: 策略已禁用，如需启用请修改 status 为 'enable'", "processor.policy.message.config_check_policyid": "建议: 策略配置不完整，请检查必需字段 (policyid, srcintf, dstintf, action)", "processor.policy.message.config_config": "建议: 策略配置部分有问题，建议检查接口映射和服务配置", "processor.policy.message.content_612b1596": "修复示例: 将 'nonexistent1' 替换为 'lan1'，将 'nonexistent2' 替换为 'wan1'", "processor.policy.message.mgmt_any": "可用接口: mgmt, wan1, lan1, dmz1 或特殊接口 any", "processor.policy.message.strategy_convert_create": "策略 {policy_name} 使用区域模式转换，将生成source-zone和dest-zone字段", "processor.policy.message.process_debug_any": "DEBUG: 接口 'any' 被处理为空字符串（表示任意接口）", "processor.policy.message.content_af872cf4": "警告: 接口 '{clean_interface}' 在映射表中未找到，使用原始名称", "processor.policy.message.content_171f40f2": "警告: 接口映射表为空，接口 '{clean_interface}' 使用原始名称", "processor.policy.message.ssh": "警告: NTOS版本 {ntos_version} 不支持content-filter功能，跳过ssl-ssh-profile配置", "processor.policy.message.protocol": "警告: NTOS版本 {ntos_version} 不支持content-filter功能，跳过profile-protocol-options配置", "processor.policy.message.content_ca6e214a": "DNAT规则 - {policy_name}", "processor.policy.message.content_8e352b12": "SNAT规则 - {policy_name}", "processor.policy.message.process": "返回空的处理结果", "processor.policy.message.strategy": "策略 {policy_name} 源使用区域名称: {src_name}", "processor.policy.message.strategy_1": "策略 {policy_name} 源接口 {src_name} 映射到区域: {src_zone}", "processor.policy.message.strategy_2": "策略 {policy_name} 目标使用区域名称: {dst_name}", "processor.policy.message.strategy_3": "策略 {policy_name} 目标接口 {dst_name} 映射到区域: {dst_zone}", "processor.policy.message.strategy_4": "警告: 在接口模式下忽略区域名称 '{zone}'，策略: {policy_name}", "misc.message.process_process_2": "处理器管理器类，负责管理和调用各种处理器", "misc.message.process_29": "初始化处理器管理器", "misc.message.process_30": "注册默认的处理器", "misc.message.content_101adeac": " 类型的时间对象（全天每天）", "misc.message.content_a4f9cf4c": " 类型的时间对象（禁用）", "misc.message.create_63": "创建一次性时间对象", "misc.message.create_process": "创建周期性时间对象，处理跨天情况", "misc.message.check_28": "检查时间范围是否跨天", "misc.message.create_64": "创建普通周期性时间对象（不跨天）", "misc.message.create_65": "创建跨天周期性时间对象，拆分为两个周期", "misc.message.content_36c57724": "获取下一天", "misc.message.process_31": "飞塔服务组处理器类", "processor.service.message.process": "服务对象处理器类", "processor.service.message.content_a9e98995": "端口超出范围 (1-65535): {part}", "processor.service.message.content_d3445db8": "端口范围无效: {part}", "processor.service.message.error": "端口范围格式错误: {part}", "processor.service.message.content_4cc0ab4e": "无法识别的端口格式: {part}", "processor.service.message.content_d787fd6b": "多端口服务 '{port_range}' 中没有有效端口", "processor.service.message.parse": "无法解析的复杂端口范围：{port_range}，忽略此端口", "processor.service.message.content_2a3c5c77": "IP协议服务 '{service_name}' 缺少协议号", "processor.service.message.start": "开始处理服务对象 {service_name} 的iprange字段", "processor.service.message.success": "服务对象 {service_name} iprange转换成功: {iprange_info['original_range']} -> 地址对象 {address_name}", "processor.service.message.content_2eb7cfcf": "服务对象 {service_name} 的iprange字段格式无效: {service_obj.get('iprange', '')}", "processor.service.message.error_1": "检测iprange字段时发生错误: {str(e)}", "processor.service.message.content_f81ce7c7": "无效的iprange格式，缺少连字符: {iprange}", "processor.service.message.content_1b7c3ab2": "无效的iprange格式，分割后部分数量不正确: {iprange}", "processor.service.message.content_67536d79": "无效的IP地址格式: {start_ip} - {end_ip}", "processor.service.message.error_2": "提取iprange信息时发生错误: {str(e)}", "processor.service.message.error_3": "IP地址格式验证时发生错误: {str(e)}", "processor.service.message.failed": "从服务对象 {service_name} 生成地址对象失败: {str(e)}", "processor.service.message.error_4": "清理服务对象配置时发生错误: {str(e)}", "processor.service.message.success_1": "成功转换服务对象 {service_name} 的iprange: {iprange_info['original_range']} -> {address_obj['name']}", "processor.service.message.failed_1": "服务对象 {service_name} 的iprange转换失败", "processor.service.message.content_a7cd65ae": "服务对象 {service_name} 的iprange格式无效: {error_msg}", "processor.service.message.convert": "iprange转换统计: 发现{conversion_stats['services_with_iprange']}个包含iprange的服务对象, ", "processor.service.message.success_2": "成功转换{conversion_stats['successful_conversions']}个, ", "processor.service.message.failed_2": "失败{conversion_stats['failed_conversions']}个", "processor.service.message.process_error": "处理服务对象iprange转换时发生错误: {str(e)}", "processor.service.message.failed_3": "服务对象iprange转换处理失败: {str(e)}", "misc.message.process_32": "FortiGate透明模式处理器", "misc.debug.file_failed": "❌ 修复文件 {file_path} 失败: {e}", "misc.debug.file_21": "✅ 修复文件: {file_path} ({fixes_count} 个翻译键)", "misc.debug.file_failed_1": "❌ 添加导入到文件 {file_path} 失败: {e}", "misc.debug.content_1c478a10": "✅ 添加导入: {file_path}", "misc.message.content_2c355e79": "主函数", "misc.debug.content_241ecef9": "🔧 翻译键格式修复工具", "misc.debug.dir_3": "引擎目录: {engine_dir}", "misc.debug.content_8a64b40f": "\\n1. 修复翻译键格式...", "misc.debug.content_089a3eb3": "\\n📊 翻译键修复结果:", "misc.debug.file_22": "  - 修复的文件: {fixed_files}", "misc.debug.content_5c860cfe": "  - 修复的翻译键: {total_fixes}", "misc.debug.content_eacd6429": "\\n2. 添加缺失的导入...", "misc.debug.content_cbf0194b": "\\n📊 导入添加结果:", "misc.debug.file_23": "  - 添加导入的文件: {added_imports}", "misc.debug.success_6": "\\n✅ 修复完成!", "misc.debug.file_24": "  - 总共修复了 {fixed_files} 个文件中的 {total_fixes} 个翻译键", "misc.debug.file_25": "  - 为 {added_imports} 个文件添加了缺失的导入", "misc.debug.content_5068313d": "\\n✅ 没有发现需要修复的翻译键!", "misc.message.content_249ab0e3": "硬编码字符串信息", "misc.message.content_c844aa8f": "硬编码字符串扫描器", "misc.message.error_failed_failure": "[需要翻译] \\b(error|failed|failure|exception|invalid|missing|not found|timeout|permission denied)\\b", "misc.message.warning_warn_caution": "[需要翻译] \\b(warning|warn|caution|notice)\\b", "misc.message.success_successful_completed": "[需要翻译] \\b(success|successful|completed|finished|done)\\b", "misc.message.loading_processing_converting": "[需要翻译] \\b(loading|processing|converting|generating|validating)\\b", "misc.message.configuration_config_setting": "[需要翻译] \\b(configuration|config|setting|option|parameter)\\b", "misc.message.file_30": "检查是否应该跳过文件", "misc.message.check_29": "检查是否应该跳过字符串", "misc.message.content_c328f56f": "检测字符串的上下文", "misc.message.content_7106f626": "对字符串进行分类", "misc.message.content_863392d2": "从代码行中提取字符串", "misc.message.file_31": "扫描单个文件", "misc.debug.file_26": "扫描文件失败 {file_path}: {e}", "misc.message.dir_4": "扫描目录", "misc.message.create_66": "生成扫描报告", "misc.message.save": "保存扫描报告", "misc.message.content_80afca04": "打印扫描摘要", "misc.debug.content_e6d61cb3": "\\n📊 硬编码字符串扫描结果:", "misc.debug.content_6da1636b": "  总计: {summary['total_strings']} 个硬编码字符串", "misc.debug.file_27": "  影响文件: {summary['files_affected']} 个", "misc.debug.content_34a3ccca": "\\n📂 按类别统计:", "misc.debug.content_c430a0d6": "\\n⚠️ 按严重程度统计:", "misc.debug.content_f9267e5a": "\\n🔍 按上下文统计:", "misc.debug.content_3652d842": "🔍 扫描硬编码字符串...", "misc.debug.save": "\\n📁 详细报告已保存到: {report_file}", "misc.debug.content_61258b5e": "\\n🚨 高优先级硬编码字符串示例 (前10个):", "misc.debug.content_e6e29e9a": "     内容: {hs.content}", "misc.debug.content_18b77c16": "     类别: {hs.category}, 上下文: {hs.context}", "misc.message.content_e0ac8c91": "国际化键映射器", "misc.message.content_589d82ab": "分析旧键名，提取模块、功能、内容信息", "misc.message.create_67": "根据旧键名生成新的标准键名", "misc.message.error_93": "[需要翻译] error.", "misc.message.system_error": "[需要翻译] system.error.{content}", "misc.message.warning_20": "[需要翻译] warning.", "misc.message.system_warning": "[需要翻译] system.warning.{content}", "misc.message.create_68": "创建键名映射表", "misc.message.file_32": "保存映射表到文件", "misc.message.file_33": "将映射应用到翻译文件", "misc.message.check_30": "验证键名格式是否符合规范", "misc.message.create_69": "生成迁移脚本", "misc.message.file_34": "迁移单个文件中的键名", "misc.debug.content_0a7230a8": "已迁移: {file_path}", "misc.debug.file_28": "迁移文件失败 {file_path}: {e}", "misc.message.dir_file": "迁移目录中的所有Python文件", "misc.debug.dir_python": "用法: python migration_script.py <目录路径>", "misc.debug.content_57d704e1": "🔍 分析现有翻译键...", "misc.debug.content_174530b9": "找到 {len(mappings)} 个翻译键", "misc.debug.save_1": "📁 映射表已保存到: {mapping_file}", "misc.debug.create_3": "🔧 迁移脚本已生成: {script_file}", "misc.debug.content_4b9a1d58": "\\n📝 键名映射示例:", "misc.debug.content_05d97468": "  ... 还有 {len(mappings) - 10} 个映射", "misc.debug.file_29": "❌ 翻译文件不存在: {zh_file}", "misc.debug.file_30": "警告: 无法读取文件 {file_path}: {e}", "misc.debug.file_31": "警告: 无法加载翻译文件 {locale_file}: {e}", "misc.message.error_94": "错误: {clean_key.replace('error.', '').replace('_', ' ')}", "misc.message.content_ad05a51f": "信息: {clean_key.replace('info.', '').replace('_', ' ')}", "misc.message.content_cace38b5": "警告: {clean_key.replace('warning.', '').replace('_', ' ')}", "misc.message.content_ba94a3b5": "调试: {clean_key.replace('debug.', '').replace('_', ' ')}", "misc.message.convert_27": "转换服务: {service_key}", "misc.message.convert_28": "转换工作流: {workflow_key}", "misc.message.content_a3a678ff": "模板管理器: {template_key}", "misc.message.content_9863c0b7": "YANG管理器: {yang_key}", "misc.message.config_93": "配置管理器: {config_key}", "misc.message.content_de2e3e96": "性能监控: {perf_key}", "misc.message.content_9b780027": "内存管理: {mem_key}", "misc.message.error_95": "错误处理: {error_key}", "misc.message.content_e3e125f5": "缓存管理: {cache_key}", "misc.message.content_7eff8897": "LRU缓存: {lru_key}", "misc.message.parse_10": "解析器注册表: {parser_key}", "misc.message.create_70": "生成器注册表: {gen_key}", "misc.message.content_9ffabacc": "管道管理器: {pipeline_key}", "misc.debug.content_96155258": "🔍 扫描翻译键...", "misc.debug.content_dc4a3fff": "找到 {len(found_keys)} 个翻译键", "misc.debug.content_6b1b67f8": "现有翻译: {len(existing_translations)} 个", "misc.debug.content_a54dcde9": "缺失翻译: {len(missing_keys)} 个", "misc.debug.content_3bc542ba": "\\n📝 缺失的翻译键:", "misc.debug.create_4": "\\n🔧 生成缺失翻译...", "misc.debug.file_32": "📁 更新翻译文件: {locale_file}", "misc.debug.completed": "✅ 完成! 添加了 {len(new_translations)} 个新翻译", "misc.debug.content_bd3f2207": "✅ 所有翻译键都已存在!", "misc.debug.content_8e8b6c87": "\\n📊 统计信息:", "misc.debug.content_2a403bfd": "  - 总翻译键: {len(found_keys)}", "misc.debug.content_a63290d5": "  - 现有翻译: {len(existing_translations)}", "misc.debug.content_c51e4f0e": "  - 新增翻译: {len(missing_keys)}", "misc.debug.content_d8c40507": "  - 覆盖率: {((len(existing_translations) + len(missing_keys)) / len(found_keys) * 100):.1f}%", "misc.message.config_94": "测试ConfigManager的路径配置", "misc.debug.success_7": "✅ ConfigManager导入成功", "misc.debug.success_8": "✅ ConfigManager初始化成功", "misc.debug.dir_4": "   - 引擎目录: {config_manager.get_engine_dir()}", "misc.debug.content_f153a1ae": "\\n🔍 测试模板路径 ({model}/{version}):", "misc.debug.content_0c5d4ba8": "✅ 模板路径找到: {template_path}", "misc.debug.file_33": "   - 文件存在: {os.path.exists(template_path)}", "misc.debug.file_bytes": "   - 文件大小: {file_size} bytes", "misc.debug.failed_5": "❌ 模板路径查找失败: {e}", "misc.debug.path": "\\n🔍 测试YANG路径 ({model}/{version}):", "misc.debug.dir_5": "✅ YANG目录找到: {yang_dir}", "misc.debug.dir_6": "   - 目录存在: {os.path.exists(yang_dir)}", "misc.debug.file_34": "   - 文件数量: {len(files)}", "misc.debug.file_35": "   - 前5个文件: {files[:5]}", "misc.debug.failed_6": "❌ YANG路径查找失败: {e}", "misc.debug.failed_7": "❌ ConfigManager测试失败: {e}", "misc.message.load_4": "测试TemplateManager的模板加载", "misc.debug.success_9": "\\n✅ TemplateManager导入成功", "misc.debug.success_10": "✅ TemplateManager初始化成功", "misc.debug.load": "\\n🔍 测试模板加载 ({template_name}/{model}/{version}):", "misc.debug.success_11": "✅ 模板加载成功", "misc.debug.content_3f7a6588": "   - 内容长度: {len(template_content)} 字符", "misc.debug.content_275ac2f0": "   - 前100字符: {template_content[:100]}...", "misc.debug.content_d6c0f467": "❌ 模板内容为空", "misc.debug.failed_8": "❌ 模板加载失败: {e}", "misc.debug.failed_9": "❌ TemplateManager测试失败: {e}", "misc.message.load_5": "测试YangManager的YANG模型加载", "misc.debug.success_12": "\\n✅ YangManager导入成功", "misc.debug.success_13": "✅ YangManager初始化成功", "misc.debug.content_c72ff928": "   - yanglint可用: {yang_manager.is_yanglint_available()}", "misc.debug.failed_10": "❌ YangManager测试失败: {e}", "misc.message.file_35": "检查文件系统中的实际路径", "misc.debug.file_36": "\\n🔍 检查文件系统路径:", "misc.debug.dir_7": "   - 引擎目录: {engine_dir}", "misc.debug.dir_8": "   - 模板基础目录: {template_base}", "misc.debug.exists": "     存在: {os.path.exists(template_base)}", "misc.debug.content_0ffa2e59": "     可用型号: {models}", "misc.debug.content_6bc3cf87": "     {model} 版本: {versions}", "misc.debug.content_f869cf49": "       {model}/{version} 模板: {templates}", "misc.debug.dir_9": "   - YANG基础目录: {yang_base}", "misc.debug.exists_1": "     存在: {os.path.exists(yang_base)}", "misc.message.content_5f13931a": "测试新架构是否能正确使用模板", "misc.debug.content_e799391f": "\\n🔍 测试新架构模板使用:", "misc.debug.success_14": "✅ ConversionService初始化成功", "misc.debug.success_15": "✅ 转换执行完成", "misc.debug.success_16": "   - 成功: {result.get('success', False)}", "misc.debug.content_d493278f": "   - 工作流版本: {result.get('workflow_version', 'N/A')}", "misc.debug.error_3": "   - 错误: {error}", "misc.debug.error_file": "   - 错误类型: 模板文件未找到", "misc.debug.error_check": "   - 错误类型: YANG验证相关", "misc.debug.error_4": "   - 错误类型: 其他", "misc.debug.file_37": "   - 输出文件: {result.get('output_file', 'N/A')}", "misc.debug.failed_11": "❌ 新架构模板测试失败: {e}", "misc.message.content_a2ce6262": "主诊断函数", "misc.debug.content_58868338": "🔧 模板路径诊断工具", "misc.message.file_36": "文件系统路径检查", "misc.message.content_6087ac7a": "ConfigManager测试", "misc.message.content_0f427564": "TemplateManager测试", "misc.message.content_552b8cd9": "YangManager测试", "misc.message.content_7860db65": "新架构模板使用测试", "misc.debug.content_0803334f": "   ✅ {test_name}通过", "misc.debug.failed_12": "   ❌ {test_name}失败", "misc.debug.error_5": "   ❌ {test_name}异常: {e}", "misc.debug.content_1ce110b7": "\\n📊 诊断结果: {passed}/{total} 通过", "misc.debug.config_6": "\\n🎉 所有诊断通过！模板路径配置正常！", "misc.debug.failed_13": "\\n⚠️  {total - passed} 个诊断失败，需要修复", "misc.message.content_24f39312": "设备接口管理器类", "misc.message.content_38e3c5bc": "测试设备接口管理器功能", "misc.debug.content_5aa8828a": "=== 设备接口管理器测试 ===", "misc.debug.content_45991c07": "支持的设备型号: {manager.get_supported_models()}", "misc.debug.content_acc39514": "z5100s所有接口: {manager.get_all_physical_interfaces()}", "misc.debug.content_332f46e6": "透明模式分类结果: {classification}", "misc.debug.content_d625e4dc": "无效MGMT接口分类: {invalid_classification}", "misc.message.config_95": "调试编码设置，打印当前编码信息", "misc.debug.content_d049c4ee": "=== 编码调试信息 ===", "misc.message.content_65396ee4": "你好世界", "misc.message.convert_29": "Configuration转换工具", "misc.message.config_96": "FortiGate配置解析", "misc.debug.utf": "\\n=== UTF-8字符串测试 ===", "misc.debug.content_4e15f434": "测试字符串: {test_str}", "misc.message.content_b59dc021": "性能指标", "misc.message.error_96": "错误上下文信息", "misc.message.content_8353abc3": "增强日志记录器", "misc.message.start_5": "记录操作开始", "misc.message.content_5aed2629": "记录操作结束", "misc.message.check_31": "记录验证结果", "misc.message.process_33": "记录处理统计信息", "misc.message.error_97": "记录带上下文的错误", "misc.message.content_433d0aa1": "记录性能警告", "misc.message.content_5c06cb32": "性能监控上下文管理器", "misc.message.error_98": "获取错误摘要", "misc.message.content_796c705f": "获取增强日志记录器实例", "misc.message.content_d19d69b6": "记录系统性能摘要", "misc.message.content_685240a5": "=== 系统性能摘要 ===", "misc.message.content_638ef840": "组件: {component_name}", "misc.message.content_449f9e03": "  操作总数: {perf_summary.get('total_operations', 0)}", "misc.message.success_16": "  成功操作: {perf_summary.get('successful_operations', 0)}", "misc.message.failed_25": "  失败操作: {perf_summary.get('failed_operations', 0)}", "misc.message.content_cea01cf9": "  总耗时: {perf_summary.get('total_duration', '0s')}", "misc.message.content_7f26da4f": "  平均耗时: {perf_summary.get('average_duration', '0s')}", "misc.message.error_99": "  错误总数: {error_summary.get('total_errors', 0)}", "misc.message.content_5281bc37": "  最慢操作: {slowest['name']} ({slowest['duration']})", "misc.message.content_ec30117f": "=== 性能摘要结束 ===", "misc.message.file_37": "统一的文件路径处理工具类", "misc.error.content_b8285a05": "输出路径不能为空", "misc.message.dir_5": "没有权限创建目录", "misc.message.dir_6": "目录不可写", "misc.message.content_738c1d26": "磁盘空间不足，可用: {space_result['available_space']} 字节，需要: {space_result['required_space']} 字节", "misc.message.dir_7": "目录准备失败: {dir_result['error']}", "misc.message.dir_8": "目录不存在且无法创建", "misc.message.dir_9": "检查父目录权限或使用其他输出路径", "misc.message.dir_10": "检查目录权限或更改输出路径", "misc.message.content_25e9b3d0": "磁盘空间不足: 可用 {space_result['available_space']} 字节", "misc.message.content_dcb07169": "清理磁盘空间或使用其他输出路径", "misc.message.file_38": "文件被其他程序占用", "misc.message.file_39": "关闭占用文件的程序或使用其他文件名", "misc.message.content_9f200594": "诊断过程出错: {str(e)}", "misc.message.dir_create": "确保目录存在，如果不存在则创建", "misc.message.dir_11": "创建临时目录", "misc.message.dir_12": "清理临时目录", "misc.message.file_40": "读取JSON文件", "misc.message.file_41": "写入JSON文件", "misc.message.file_42": "读取文本文件", "misc.message.file_43": "写入文本文件", "misc.message.file_44": "复制文件", "misc.message.processing_1": "[需要翻译] processing.", "misc.message.success_17": "[需要翻译] success.", "misc.info.file_2": "创建语言文件目录: {LOCALE_DIR}", "misc.info.file_error": "创建语言文件目录失败: {LOCALE_DIR}, 错误: {str(e)}", "misc.info.file_3": "已创建空语言文件: {lang_file}", "misc.info.file_4": "创建语言文件失败: {e}", "misc.info.content_222789ea": "无法识别的语言代码: {language}，使用默认语言: {DEFAULT_LANGUAGE}", "misc.info.content_283ee378": "不支持的语言: {language}, 使用默认语言: {DEFAULT_LANGUAGE}", "misc.info.file_5": "语言文件不存在: {lang_file}", "misc.info.load": "尝试加载默认语言: {DEFAULT_LANGUAGE}", "misc.info.file_error_1": "加载语言文件失败: {lang_file}, 错误: {str(e)}", "misc.info.error": "异常堆栈: {traceback.format_exc()}", "misc.message.content_d688a3a4": "简体中文", "misc.message.content_00110af8": "日本語", "misc.info.content_f2bbc9d5": "在回退语言 {fallback_lang} 中找到翻译: {key}", "misc.info.content_493b6bd4": "翻译 '{key}' 中缺少参数 {e}", "misc.info.content_508970bc": "格式化翻译 '{key}' 时出错: {e}", "misc.message.file_45": "创建语言文件目录", "misc.info.config_3": "设置翻译回退语言: {_fallback_languages}", "misc.info.content_4b028c3d": "i18n调试模式已{'启用' if enabled else '禁用'}", "misc.message.content_e25f9cb2": "初始化i18n模块", "misc.info.load_1": "已初始化国际化模块，使用语言: {language}，加载了 {len(translations)} 个翻译项", "misc.info.content_7f336fad": "添加运行时翻译: [{language}] {key} = {value}", "misc.info.content_1ce45067": "已导出缺失翻译到: {output_file}", "misc.info.failed_5": "导出缺失翻译失败: {str(e)}", "misc.info.content_b184f359": "缺失翻译统计: {json.dumps(result, ensure_ascii=False)}", "misc.info.content_cab703da": "{lang} 没有缺失的翻译键", "misc.info.create": "{lang} 缺失翻译模板已创建: {template_file} ({len(template)} 个键)", "misc.info.file_6": "无法从文件名推断目标语言: {filename}", "misc.info.file_7": "模板文件不存在: {template_file}", "misc.info.file_8": "目标语言文件不存在: {target_file}", "misc.info.content_b594795d": "已将 {len(template_data)} 个翻译键从 {template_file} 合并到 {target_file}", "misc.info.content_e7015aac": "原始键数: {original_count}, 新增键数: {new_count}, 当前总键数: {len(target_data)}", "misc.info.file_9": "合并翻译文件时出错: {str(e)}", "misc.info.file_10": "翻译文件不存在: {target_file}", "misc.info.file_11": "已对翻译文件进行排序: {target_file}", "misc.info.file_12": "排序翻译文件时出错: {str(e)}", "misc.message.info_error_warning": "[需要翻译] ](?:i18n:)?(info\\.|error\\.|warning\\.|[^\\'", "misc.message.translate_log_info": "[需要翻译] (?:_|translate|user_log|log)\\s*\\(\\s*[\\'\"](?:i18n:)?(info\\.|error\\.|warning\\.|[^\\'\"\\s]+\\.[^\\'\"\\s]+)[\\'\"]", "misc.info.file_error_2": "处理文件时出错: {file_path}, 错误: {str(e)}", "misc.info.dir": "扫描源代码目录: {src_dir}", "misc.info.content_bef10903": "从源代码中提取到 {len(source_keys)} 个翻译键", "misc.info.file_13": "在默认语言翻译文件中找不到 {len(missing_in_default)} 个键", "misc.info.file_14": "已创建默认语言补充文件: {supplement_file}", "misc.info.file_15": "创建新语言文件: {lang_file}", "misc.info.file_16": "在 {lang} 语言文件中找不到 {len(missing_in_lang)} 个键", "misc.info.create_file": "已创建 {lang} 语言补充文件: {supplement_file}", "misc.info.start": "翻译状态分析开始", "misc.info.content_fceb3a2c": "默认语言 ({DEFAULT_LANGUAGE}) 包含 {len(default_keys)} 个翻译键", "misc.info.content_156e399d": "语言翻译覆盖率:", "misc.message.content_c33869f9": "翻译键数", "misc.message.content_00413048": "覆盖率", "misc.message.content_31e9697a": "基准语言", "misc.message.content_c095669f": "完整覆盖", "misc.message.success_18": "接近完成", "misc.message.success_19": "部分完成", "misc.message.content_8320cbf1": "需要补充", "misc.message.content_0ba443ee": "缺失键数", "misc.info.content_9de9d8b9": "{'语言':8} | {'翻译键数':8} | {'覆盖率':8} | {'状态':10} | {'缺失键数':8}", "misc.info.content_917816da": "{item['语言']:8} | {item['翻译键数']:8} | {item['覆盖率']:8} | {item['状态']:10} | {item['缺失键数']:8}", "misc.info.content_0bd9096c": "{item['语言']:8} | {item['翻译键数']:8} | {item['覆盖率']:8} | {item['状态']:10} |", "misc.message.parse_11": "主函数，解析命令行参数并执行相应功能", "misc.message.content_39fa8509": "国际化(i18n)翻译管理工具", "misc.message.content_875120cb": "子命令", "misc.message.content_fbb6465d": "分析翻译状态", "misc.message.create_71": "创建缺失翻译模板", "misc.message.dir_13": "输出目录", "misc.message.file_46": "合并翻译文件", "misc.message.file_47": "包含新翻译的模板文件", "misc.message.file_48": "目标语言文件", "misc.message.file_49": "对翻译文件进行排序", "misc.message.file_50": "要排序的翻译文件", "misc.message.file_51": "更新翻译文件", "misc.message.dir_14": "源代码目录", "misc.message.file_52": "更新所有语言文件", "misc.message.content_e194d466": "导出缺失翻译", "misc.message.content_958d2705": "语言代码", "misc.message.file_53": "输出文件路径", "misc.message.content_58595e70": "接口映射表为空", "misc.message.success_20": "接口映射成功: {clean_name} -> {mapped_name}", "misc.message.success_21": "接口映射成功: {clean_name} -> {mapping_data}", "misc.message.content_d17555e6": "未找到接口 {clean_name} 的映射，使用原始名称", "misc.message.content_819767ad": "用户名", "misc.message.user_item_success": "[需要翻译] user.item.success", "misc.message.user_item_failed": "[需要翻译] user.item.failed", "misc.message.user_item_warning": "[需要翻译] user.item.warning", "misc.message.file_54": "获取当前系统调试日志文件路径", "misc.message.file_55": "获取当前用户日志文件路径", "misc.message.file_56": "获取当前完整日志文件路径", "misc.message.error_100": "[递归错误] {message}", "misc.info.failed_import_module": "[需要翻译] Failed to import i18n module: {key}, error: {str(e)}", "misc.info.translation_key_failed": "[需要翻译] Translation key failed: {key}, error: {str(e)}", "misc.info.key_formatting_failed": "[需要翻译] Key formatting failed: {key}, error: {str(format_error)}", "misc.message.content_03ae625b": "消息中缺少参数: {', '.join(str(k) for k in missing_keys)}", "misc.info.message_missing_parameters": "[需要翻译] Message is missing parameters: {', '.join(str(k) for k in missing_keys)}", "misc.message.failed_26": "警告消息处理失败: {str(warn_e)}", "misc.info.warning_message_processing": "[需要翻译] Warning message processing failed: {str(warn_e)}", "misc.message.missing_parameter": "[需要翻译] [Missing Parameter: {missing_key}]", "misc.message.content_275172b7": "消息中缺少参数: {missing_key_str}", "misc.info.message_missing_parameter": "[需要翻译] Message is missing parameter: {missing_key_str}", "misc.message.missing_parameter_1": "[需要翻译] [Missing Parameter: {missing_key_str}]", "misc.info.error_1": "格式错误: {str(warn_e)}", "misc.message.content_cd153df6": "属性:", "misc.message.xxx": "添加接口: xxx, 属性:", "misc.message.content_fbc38abc": "添加接口:", "misc.message.content_a3d099e1": "初始化翻译器", "misc.message.content_f527a68a": "强制刷新所有日志", "misc.info.failed_import_module_1": "[需要翻译] Failed to import i18n module: {key}", "misc.info.translation_error_error": "[需要翻译] Translation error: {key}, error: {str(e)}", "misc.info.translation_formatting_error": "[需要翻译] Translation formatting error: {message}, error: {error_msg}", "misc.message.content_76ac8f69": "命名空间信息", "misc.message.content_13eb988a": "全局名称管理器", "misc.message.content_ce920917": "初始化名称管理器", "misc.message.content_25339a64": "安全策略命名空间", "misc.message.content_d3d416a1": "NAT规则命名空间", "misc.message.content_5b5c93f2": "网络对象命名空间", "misc.message.content_72c34c52": "服务对象命名空间", "misc.error.content_aff0afaf": "未知的命名空间: {namespace}", "misc.message.content_ca32150d": "确保名称在命名空间内唯一", "misc.message.content_38fe43f6": "获取原始名称对应的最终名称", "misc.message.check_32": "检查跨命名空间的名称冲突（用于警告）", "misc.message.create_72": "生成名称管理摘要报告", "misc.message.content_b676c193": "地址池名称同步管理器", "misc.message.content_42ea1838": "清除所有名称映射", "misc.message.process_34": "获取待处理的引用更新", "misc.message.content_af404227": "获取当前时间戳", "misc.message.content_df09fff7": "名称不能为空", "misc.message.content_30bfdf7b": "名称包含非法字符: {', '.join(set(invalid_chars))}", "misc.message.content_4a7ccbac": "名称过长: {len(name)} > 64", "misc.message.content_0047c4c5": "描述包含非法字符: {', '.join(set(invalid_chars))}", "misc.message.invalid_address_format": "[需要翻译] Invalid IP address format: {ip_addr}, error: {e}", "misc.message.error_processing_address": "[需要翻译] Error processing IP address {ip_addr}: {e}", "misc.message.check_33": "初始化名称验证器", "misc.message.content_dcd68eb7": "对象池实现", "misc.message.content_50f79c0c": "从池中获取对象", "misc.message.content_3f556d6e": "将对象返回池中", "misc.message.content_ab388259": "获取池统计信息", "misc.message.content_980c0032": "智能缓存实现", "misc.message.content_0c594936": "获取缓存值", "misc.message.config_97": "设置缓存值", "misc.message.remove_8": "移除缓存项", "misc.message.remove_9": "移除最久未访问的项", "misc.message.process_35": "批处理器", "misc.message.process_36": "注册批处理器", "misc.message.process_37": "添加项到批处理队列", "misc.message.process_38": "刷新批处理队列", "misc.message.process_39": "刷新所有批处理队列", "misc.message.content_3f0b9889": "记录操作性能", "misc.message.content_bc117f5e": "记录缓存命中", "misc.message.content_92375dd8": "记录缓存未命中", "misc.message.content_cd46b685": "获取操作指标", "misc.message.content_9506b88a": "获取所有指标", "misc.message.content_5def7f2b": "重置指标", "misc.message.content_f548930c": "获取性能监控器实例", "misc.message.content_092c2be2": "获取智能缓存实例", "misc.message.content_bf6bea6f": "获取对象池实例", "misc.message.content_e0746aa6": "性能优化装饰器", "misc.message.process_40": "批处理装饰器", "misc.message.content_97e4f543": "内存效率装饰器", "misc.message.content_bf89d715": "优化数据结构", "misc.message.content_b0fd21dd": "管道阶段用户日志记录器", "misc.message.user_stage_failed": "[需要翻译] user.stage.failed", "misc.message.check_34": "语义验证警告数据结构", "misc.message.create_73": "语义验证报告生成器", "misc.message.content_a6877e8d": "添加警告", "misc.message.content_f2706940": "添加接口引用警告", "misc.message.content_f2ba64f6": "添加地址对象引用警告", "misc.message.content_9e057aee": "添加服务对象引用警告", "misc.message.config_98": "添加策略日志设置警告", "misc.message.config_99": "添加DNS配置警告", "misc.message.warning_21": "[需要翻译] warning.dns_{dns_issue_type}", "misc.message.create_74": "生成统计信息", "misc.message.create_75": "生成控制台简洁汇总", "misc.message.file_57": "生成详细报告文件", "misc.message.create_76": "生成详细报告内容", "misc.message.content_8690a694": "[需要翻译] {severity_info['emoji']} {severity_info['label']} - {_('report.needs_immediate_attention' if severity == 'critical' else 'report.recommended_to_fix' if severity == 'warning' else 'report.optional_optimization')}:", "misc.message.content_84550036": "[需要翻译] ├─ {warning.message}", "misc.message.content_d7d925f0": "添加对象引用问题的详细信息", "misc.message.content_16473451": "添加策略问题的详细信息", "misc.message.content_c390b157": "[需要翻译] ├─ {_('report.issue_description')}: {warning.details.get('issue_type', 'unknown')}", "misc.debug.content_b3d42482": "保留服务对象: {service_name}", "misc.debug.content_a29a7a0d": "跳过重复服务对象: {service_name}", "misc.debug.success_17": "去重完成：{len(service_objects)} -> {len(deduped_services)}", "misc.message.check_35": "服务映射验证器", "misc.message.check_36": "初始化验证器", "misc.message.file_58": "加载服务定义文件", "misc.message.content_61fee4c7": "FortiGate服务 '{fortigate_service}' 未找到定义", "misc.message.content_d5445355": "NTOS服务 '{ntos_service}' 未找到定义", "misc.message.fortigate_ntos": "协议不匹配: FortiGate({fg_protocols}) vs NTOS({ntos_protocol})", "misc.message.fortigate_ntos_1": "端口不匹配: FortiGate({fg_def['ports']}) vs NTOS({ntos_def['ports']})", "misc.message.fortigate_ntos_2": "协议号不匹配: FortiGate({fg_def['protocol_number']}) vs NTOS({ntos_def['protocol_number']})", "misc.message.fortigate_ntos_3": "ICMP类型不匹配: FortiGate({fg_def['icmp_type']}) vs NTOS({ntos_def['type']})", "misc.message.create_77": "建议创建自定义NTOS服务以确保完全匹配", "misc.message.content_a09f3bf8": "使用协议: {fg_protocols[0] if fg_protocols else 'TCP'}", "misc.message.content_ebafac15": "使用端口: {fg_def['ports']}", "misc.message.parse_12": "解析协议字符串", "misc.message.check_37": "检查协议兼容性", "misc.message.parse_13": "解析端口字符串为端口范围列表", "misc.message.check_38": "检查两个端口范围列表是否有重叠", "misc.message.content_6055d296": "用户日志格式化器类", "misc.message.content_a817bd05": "初始化格式化器", "misc.message.convert_30": "记录转换结束时间", "misc.message.user_status_success": "[需要翻译] user.status.success", "misc.message.user_status_failed": "[需要翻译] user.status.failed", "misc.message.user_status_warning": "[需要翻译] user.status.warning", "misc.message.process_41": "无需处理", "misc.message.content_96eaf5c8": "[需要翻译]   ✗ {_('user.detail.failed_converted', language=self.language)}: {failed}{_('user.detail.items_unit', language=self.language)}", "misc.message.success_22": "获取成功转换的详细信息", "misc.message.content_8eacb84d": "获取警告项目的详细信息", "misc.message.failed_27": "获取失败项目的详细信息", "misc.message.content_6098ed1a": "获取跳过项目的详细信息", "misc.message.content_c7170585": "用户日志收集器，用于收集所有阶段的用户日志和统计信息，最后统一输出", "misc.message.content_dd5149ef": "启用日志收集", "misc.message.content_ebadf6aa": "添加阶段日志", "misc.message.content_b5d00eff": "添加阶段统计信息（始终收集，用于最后汇总）", "misc.message.content_1bbc0adc": "获取收集的日志", "misc.message.content_2f9ead65": "获取收集的统计信息", "misc.message.content_cd845d3a": "输出收集的日志", "misc.message.content_860ebd6b": "输出最终的集中统计信息（显示收集的详细日志）", "misc.message.create_78": "生成阶段详细信息（完整版本，包含具体对象和原因）", "misc.message.content_a031a4cd": "提取阶段统计信息", "misc.message.create_79": "生成阶段的详细日志信息（重用现有逻辑）", "misc.message.content_6c1bf75f": "[需要翻译]   ✗ {_('user.stage.failed_converted', count=failed)}", "misc.message.convert_31": "详细转换统计", "misc.message.content_cfd4d2ea": "启用用户日志收集模式", "misc.message.content_683a6238": "输出收集的用户日志", "misc.message.content_68e029a9": "输出最终的集中统计信息", "misc.message.content_4e23db38": "用户操作建议引擎", "misc.message.convert_32": "分析接口转换结果", "misc.message.failed_28": "生成接口失败的建议", "misc.message.create_80": "生成接口跳过的建议", "misc.message.convert_33": "分析地址对象转换结果", "misc.message.create_81": "生成地址对象相关建议", "misc.message.convert_34": "分析服务对象转换结果", "misc.message.convert_35": "分析策略规则转换结果", "misc.message.convert_36": "分析整体转换结果", "misc.message.content_2ffc0305": "无效的IP地址格式: {str(e)}", "misc.message.content_5dfc9d7e": "起始IP地址 {start_ip} 不能大于结束IP地址 {end_ip}", "misc.message.content_3696556d": "IP范围过大 ({range_size} 个地址)，最大支持65536个地址", "misc.message.validate_error": "验证IP范围时发生错误: {str(e)}", "misc.error.content_9912bad1": "IP范围字符串不能为空", "misc.error.content_9da3140b": "IP范围格式无效，缺少连字符分隔符", "misc.error.content_a913252f": "IP范围格式无效，分割后部分数量不正确", "misc.error.content_6ca059a3": "起始IP或结束IP不能为空", "misc.error.parse_failed": "解析IP范围字符串失败: {str(e)}", "misc.message.content_f1409c05": "端口范围不能为空", "misc.message.content_0d84ebbd": "无效的端口范围格式: {part}", "misc.message.content_6ddefba1": "端口范围包含非数字字符: {part}", "misc.message.content_076c95d1": "端口号超出有效范围(1-65535): {part}", "misc.message.content_b3437e58": "起始端口不能大于结束端口: {part}", "misc.message.content_6618edf9": "端口包含非数字字符: {part}", "misc.message.error_101": "验证端口范围时发生错误: {str(e)}", "misc.message.error_102": "标准化IP范围时发生错误: {str(e)}", "misc.message.error_103": "生成地址对象名称时发生错误: {str(e)}", "misc.message.process_42": "XML后处理修复器", "misc.message.start_file": "🔧 开始后处理修复XML文件: {xml_file_path}", "misc.message.success_23": "✅ XML后处理修复完成: {xml_file_path}", "misc.message.failed_29": "❌ XML后处理修复失败: {str(e)}", "misc.message.error_104": "详细错误信息: {traceback.format_exc()}", "misc.message.create_file": "创建XML文件备份", "misc.message.file_59": "📋 创建备份文件: {backup_path}", "misc.message.failed_30": "❌ 创建备份失败: {str(e)}", "misc.message.content_e41f0ad8": "修复VLAN接口结构：将VRF根级别的VLAN接口移动到interface容器内部", "misc.message.start_6": "🔧 开始修复VLAN接口结构", "misc.message.content_ea522973": "❌ 未找到VRF元素", "misc.message.container_1": "❌ 未找到interface容器", "misc.message.error_105": "🚨 发现 {len(misplaced_vlans)} 个错误放置的VLAN接口", "misc.message.content_6a00c13d": "✅ 修复VLAN接口: {vlan_name} 已移动到interface容器", "misc.message.content_07830628": "VLAN接口结构修复: {len(misplaced_vlans)}个接口", "misc.message.success_24": "🎉 VLAN接口结构修复完成，修复了 {len(misplaced_vlans)} 个接口", "misc.message.content_d1343e14": "✅ 所有VLAN接口结构正确", "misc.message.failed_31": "❌ 修复VLAN接口结构失败: {str(e)}", "misc.message.config_remove": "修复物理接口配置：移除多余的enabled和working-mode配置", "misc.message.start_7": "🔧 开始修复物理接口配置", "misc.message.config_remove_1": "物理接口配置修复: 移除{fixed_count}个多余配置", "misc.message.success_remove_config": "✅ 物理接口配置修复完成，移除了 {fixed_count} 个多余配置", "misc.message.config_100": "✅ 物理接口配置正确", "misc.message.failed_32": "❌ 修复物理接口配置失败: {str(e)}", "misc.message.config_101": "为VLAN接口添加access-control配置", "misc.message.start_config": "🔧 开始为VLAN接口添加access-control配置", "misc.message.content_73468f29": "✅ 为VLAN接口 {vlan_name} 添加access-control配置", "misc.message.config_config_vlan": "VLAN access-control配置: 添加{added_count}个配置", "misc.message.success_config_vlan": "✅ VLAN access-control配置添加完成，添加了 {added_count} 个配置", "misc.message.config_102": "✅ 所有VLAN接口已有access-control配置", "misc.message.failed_access": "❌ 添加VLAN access-control配置失败: {str(e)}", "misc.message.content_cc9e1662": "查找VRF元素", "misc.message.container_2": "查找interface容器", "misc.message.content_c302d0cd": "获取VLAN接口名称", "misc.message.save_file_1": "保存修复后的XML文件", "misc.message.save_file_2": "💾 保存修复后的XML文件: {xml_file_path}", "misc.message.save_file_3": "❌ 保存XML文件失败: {str(e)}", "misc.message.content_2dba08df": "输出修复报告", "misc.message.process_43": "📋 XML后处理修复报告:", "misc.message.content_2b37a454": "  ℹ️ 未发现需要修复的问题", "misc.message.content_6ee61b73": "主函数，用于命令行调用", "misc.debug.python": "用法: python xml_post_processor.py <xml_file_path>", "misc.debug.error_file_1": "错误: 文件不存在 {xml_file_path}", "misc.debug.success_18": "✅ XML后处理修复成功", "misc.debug.failed_14": "❌ XML后处理修复失败", "misc.message.content_516ca472": "YANG命名空间信息", "misc.message.content_89d86428": "YANG节点信息", "misc.message.content_94836034": "YANG模型架构", "misc.message.parse_parse_file": "YANG模型解析器，用于解析YANG模型文件并构建架构映射", "misc.message.dir_15": "获取YANG模型目录路径", "misc.message.parse_14": "解析模块头部信息，提取命名空间和typedef", "misc.message.typedef_type_string": "[需要翻译] typedef\\s+([^\\s{]+)\\s*{[^}]*type\\s+string\\s*{[^}]*pattern\\s+[\\'\"]([^\\'\"]*)[\\'\"]\\s*{[^}]*error-message[^}]*}[^}]*}[^}]*}", "misc.message.parse_15": "解析模块结构，提取节点信息", "misc.message.parse_16": "递归解析节点", "misc.message.content_f7bd8a27": "获取节点的命名空间", "misc.message.check_39": "验证节点值是否符合约束", "misc.message.content_51a5f358": "获取所有必需的命名空间映射", "misc.message.check_40": "检查节点路径是否有效", "misc.message.content_d75c15b4": "获取节点类型", "misc.message.content_4585e152": "导出架构为JSON格式，用于调试", "misc.message.check_create": "YANG验证上下文，用于在XML生成过程中进行实时验证", "misc.message.check_41": "获取验证摘要", "misc.message.check_42": "清除验证结果", "misc.message.failed_33": "XML解析失败: {str(e)}", "misc.message.content_1543504d": "地址对象名称 '{name}' 包含YANG模型禁止的字符", "misc.message.content_235fcbfb": "服务对象名称 '{name}' 包含YANG模型禁止的字符", "misc.message.content_b1d9b505": "安全策略名称 '{name}' 包含YANG模型禁止的字符", "misc.message.content_ba5228c4": "安全区域优先级 '{priority}' 重复，违反YANG模型唯一性约束", "misc.message.load_6": "未找到VRF节点，可能影响配置加载", "misc.message.missing_8": "缺少XML命名空间声明", "misc.message.config_103": "未找到 {element} 配置元素", "misc.message.content_8232da7d": "发现 {len(validation_errors)} 个YANG约束违规: ", "misc.message.error_106": " (还有 {len(validation_errors) - 3} 个错误)", "misc.message.content_1281736c": "发现 {len(validation_warnings)} 个警告: ", "misc.message.content_e2b94da7": " (还有 {len(validation_warnings) - 2} 个警告)", "misc.message.check_43": "验证通过但有警告: {warning_msg}", "misc.message.check_44": "内置YANG约束验证通过", "misc.message.check_45": "内置验证过程出错: {str(e)}", "misc.message.check_46": "内置验证: {builtin_message}", "misc.message.check_47": "地址池验证结果", "misc.message.error_107": "添加错误信息", "misc.message.content_e6f15ad5": "添加警告信息", "misc.message.content_148c8e73": "添加信息", "misc.message.config_104": "设置容量信息", "misc.message.content_3238a6d8": "添加优化建议", "misc.message.check_48": "增强的IP池验证器", "misc.message.content_1fb92735": "[需要翻译] validate_{name}_{hash(str(config))}", "misc.message.config_105": "验证基本配置完整性", "misc.message.validate_1": "验证IP地址范围", "misc.message.check_49": "验证池名称", "misc.message.content_abbf6853": "分析池容量", "misc.message.check_50": "验证网络安全性", "misc.message.check_51": "验证跨池冲突", "misc.message.check_52": "检查两个IP范围是否重叠", "misc.message.check_53": "=== IP池验证报告 ===", "misc.message.content_29924dbe": "总计IP池: {total_pools}", "misc.message.content_9318697d": "有效IP池: {valid_pools}", "misc.message.content_31d84157": "无效IP池: {invalid_pools}", "misc.message.content_8c55d195": "状态: {'有效' if result.is_valid else '无效'}", "misc.message.content_58b41f8d": "容量: {cap['total_ips']} IP地址", "misc.message.content_eec81cf8": "效率: {cap['efficiency']*100:.1f}%", "misc.message.error_108": "错误:", "misc.message.content_3bdf3078": "[需要翻译]   - {error}", "misc.message.content_7b85fd65": "警告:", "misc.message.content_b665e17d": "[需要翻译]   - {warning}", "misc.message.content_104aa197": "优化建议:", "misc.message.validation_failed_due": "[需要翻译] Validation failed due to error", "misc.message.check_54": "验证基本结构", "misc.message.check_55": "验证匹配条件", "misc.message.validate_config": "验证SNAT配置", "misc.message.validate_config_1": "验证DNAT配置", "misc.message.validate_2": "验证YANG模型约束", "misc.message.error_109": "YANG验证错误", "misc.message.check_56": "YANG验证结果", "misc.message.error_110": "添加错误", "misc.message.check_57": "YANG模型地址池验证器", "misc.message.config_106": "验证池地址配置", "misc.message.validate_3": "验证IPv4地址值", "misc.message.validate_4": "验证IP范围格式", "misc.message.validate_5": "验证CIDR格式", "misc.message.check_58": "验证单个IP格式", "misc.message.check_59": "验证池描述", "misc.message.check_60": "验证池整体结构", "misc.message.validate_6": "验证NAT规则名称", "misc.message.check_61": "验证动态转换参数"}