#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Multi-vendor configuration conversion tool main entry
Supports multiple vendor configuration conversion with internationalization
"""

import os
import sys
import json
import argparse
import logging
import datetime
import traceback
from pathlib import Path

# Ensure utils is in path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Redirect system logs to /dev/null to prevent direct output to console
class NullHandler(logging.Handler):
    def emit(self, record):
        pass

# Disable all module root logger output to stdout
logging.getLogger().handlers = []
logging.getLogger().addHandler(NullHandler())

# Import encoding fix tool, ensure correct encoding is set at the beginning
from engine.utils.encoding_fix import setup_encoding
setup_encoding()

# Initialize logging system
from engine.utils.logger import log, setup_logging, setup_full_logging, user_log, flush_all_logs, initialize_translator

# Import internationalization module
from engine.utils.i18n import translate, init_i18n, set_debug_mode, _

# Set i18n module to not output debug info
set_debug_mode(False)

# Initialize translator
initialize_translator()

# Import conversion related functions
from engine.verify import verify_config_file, format_verification_result, detect_fortigate_version
from engine.extract import extract_interface_info
from engine.utils.file_utils import sanitize_filename_for_logging


def main():
    """Multi-vendor configuration conversion tool main entry"""
    try:
        # Default disable stdout logging, ensure only JSON results output to stdout
        setup_logging(log_to_stdout=False)

        # Parse command line arguments
        args = parse_args()

        # Set internationalization
        # Convert zh_CN to zh-CN format
        lang = args.lang.replace('_', '-')
        init_i18n(lang)



        # Log startup info
        log(_("info.tool_started"), "info", mode=args.mode, vendor=args.vendor, language=lang)

        # Ensure log files use correct encoding
        if args.user_log:
            user_log(_("main.tool_started_new", mode=args.mode, vendor=args.vendor, language=lang))
            user_log(_("main.encoding_settings_new", pythonioencoding=os.environ.get('PYTHONIOENCODING', 'not set')))
            user_log(_("main.locale_settings_new", lang=os.environ.get('LANG', 'not set'), lc_all=os.environ.get('LC_ALL', 'not set')))
        
        # 验证参数
        if args.mode == 'convert':
            if not all([args.mapping, args.model, args.version, args.output]):
                log(_("error.missing_parameters"), "error")
                result = {
                    "success": False,
                    "error": translate("error.missing_parameters"),
                    "message": translate("error.convert_mode_requires_params")
                }
                print(json.dumps(result, ensure_ascii=False))
                sys.exit(0)
        
        # 检查输入文件是否存在
        if not os.path.isfile(args.cli):
            error_msg = translate("error.file_not_exists", file=args.cli)
            log(_("error.file_not_exists"), "error", file=args.cli)
            result = {
                "success": False,
                "error": error_msg,
                "message": translate("error.cannot_read_config")
            }
            print(json.dumps(result, ensure_ascii=False))
            sys.exit(0)
        
        # Execute different operations based on mode
        if args.mode == 'verify':        # Verify configuration file
            verify_mode(args)
        elif args.mode == 'extract':     # Extract interface information mode
            extract_mode(args)
        elif args.mode == 'convert':     # Convert configuration file mode
            convert_mode_wrapper(args)
        else:
            # Default to conversion mode
            convert_mode_wrapper(args)

    except KeyboardInterrupt:
        # User interruption
        log(_("info.user_interrupted"), "info")
        # Return status code 0 to avoid Go side treating it as error
        sys.exit(0)
    except Exception as e:
        # Global exception handling
        error_msg = _("error.unhandled_exception", error=str(e))
        log(error_msg, "error")


        # Build error JSON response
        error_result = {
            "valid": False,
            "success": False,
            "message": _("main.program_execution_error_new", error=str(e)),
            "error": str(e),
            "warnings": []
        }

        # Output JSON format error information
        print(json.dumps(error_result, ensure_ascii=False))

        # Return status code 0 to avoid Go side treating it as error
        sys.exit(0)

def verify_mode(args):
    """验证配置文件模式
    
    参数:
        args: 命令行参数
    """
    # 强制禁用标准输出日志，确保只有JSON结果输出
    setup_logging(log_to_stdout=False)
    
    log(_("info.start_verification"), "info", vendor=args.vendor, file=args.cli)
    result = run_verify_mode(args)
    

        
    # 如果指定了输出文件，将结果写入文件
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            f.write(json.dumps(result, ensure_ascii=False))
    
    # 总是输出JSON结果，这是与go侧的约定
    print(json.dumps(result, ensure_ascii=False))
    
    # 无论验证成功或失败，都以正常状态退出
    sys.exit(0)

def convert_mode_wrapper(args):
    """转换模式包装函数，处理转换结果并设置退出状态
    
    参数:
        args: 命令行参数
    """
    log(_("info.start_conversion"), "info")
    log(_("info.input_file"), "info", file=args.cli)
    log(_("info.mapping_file"), "info", file=args.mapping)
    log(_("info.target_model"), "info", model=args.model)
    log(_("info.target_version"), "info", version=args.version)
    log(_("info.output_file"), "info", file=args.output)
    
    if args.user_log:
        user_log(_("main.conversion_start"))
        user_log(_("main.input_file", file=args.cli))
        user_log(_("main.mapping_file", file=args.mapping))
        user_log(_("main.target_model", model=args.model))
        user_log(_("main.target_version", version=args.version))
        user_log(_("main.output_file", file=args.output))
    
    # 执行转换
    result = convert_mode(args)
    
    # 根据转换结果设置退出状态码
    if result != 0:
        log(_("error.conversion_failed"), "error")
        if args.user_log:
            user_log(_("error.conversion_failed"), "error")
        # 即使转换失败也返回0状态码，错误信息已在convert_mode中以JSON格式输出
        sys.exit(0)
    else:
        log(_("info.conversion_succeeded"), "info")
        if args.user_log:
            user_log(_("info.conversion_succeeded"), "info")
        sys.exit(0)

def run_verify_mode(args):
    """运行验证模式

    参数:
        args: 命令行参数

    返回:
        dict: 验证结果
    """
    # 阻止任何标准输出
    old_stdout = sys.stdout
    # os模块已在文件顶部导入，无需重复导入
    sys.stdout = open(os.devnull, 'w')
    
    try:
        from engine.verify import verify_config_file, format_verification_result
        is_valid, message, warnings = verify_config_file(
            args.cli,
            args.vendor,
            args.lang,
            args.strict_version,
            args.quiet,
            device_model=getattr(args, 'device_model', None)
        )
        
        # 检测版本信息（仅针对FortiGate）
        detected_version = None
        if args.vendor.lower() == "fortigate":
            try:
                with open(args.cli, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    from engine.verify import detect_fortigate_version
                    version_detected, version_str, unused1, unused2, unused3 = detect_fortigate_version(content)
                    if version_detected:
                        detected_version = version_str
            except Exception:
                pass
        
        # 直接构造JSON结果，避免调用翻译函数
        result = {
            "valid": is_valid,
            "message": message,
            "success": is_valid,
            "error": "" if is_valid else message,
            "warnings": warnings,
        }
        
        if detected_version:
            result["detected_version"] = detected_version
            
        # 恢复标准输出
        sys.stdout.close()
        sys.stdout = old_stdout
        
        # 注意：不在这里输出结果，避免重复输出
        # 返回结果由调用者处理
        return result
    except Exception as e:
        # 恢复标准输出
        sys.stdout.close()
        sys.stdout = old_stdout
        
        error_msg = f"验证配置文件失败: {str(e)}"
        error_result = {
            "valid": False,
            "message": error_msg,
            "success": False,
            "error": error_msg,
            "warnings": []
        }
        # 注意：不在这里输出结果，避免重复输出
        return error_result

def convert_mode(args):
    """执行配置转换
    
    Args:
        args: 命令行参数
        
    Returns:
        int: 0表示成功，非0表示失败
    """
    try:
        # 导入转换模块
        from engine.convert import convert_config, fix_common_path_errors
        # 导入验证模块
        from engine.verify import verify_config_file, detect_fortigate_version
        from engine.utils.i18n import translate
        
        cli_file = fix_common_path_errors(args.cli)
        mapping_file = fix_common_path_errors(args.mapping)
        output_file = fix_common_path_errors(args.output)
        encrypt_output = fix_common_path_errors(args.encrypt_output) if args.encrypt_output else None
        
        # 创建引擎日志文件路径
        engine_debug_log = ""
        engine_user_log = ""
        report_path = ""
        
        # 将zh_CN转换为zh-CN格式
        lang = args.lang.replace('_', '-')
        
        # 如果指定了log_dir，在log_dir中创建引擎日志文件
        if args.log_dir:
            timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
            # 使用安全的文件名处理函数，避免文件名过长或包含特殊字符
            job_id = sanitize_filename_for_logging(cli_file)
            engine_debug_log = os.path.join(args.log_dir, f"{job_id}.engine.debug.log")
            engine_user_log = os.path.join(args.log_dir, "conversion_summary.log")
            report_path = os.path.join(os.path.dirname(output_file), "conversion_report.json")
            log(_("info.engine_debug_log_created"), "info", file=engine_debug_log)
            log(_("info.engine_user_log_created"), "info", file=engine_user_log)
            log(_("info.report_path"), "info", file=report_path)
        
        # 添加版本校验功能
        log(_("info.verifying_config_before_conversion"), "info")
        if args.user_log:
            user_log(_("info.verifying_config_before_conversion"), "info")
        
        # 验证配置文件，包括版本校验
        is_valid, error_message, warnings = verify_config_file(
            cli_file, 
            args.vendor,
            lang,
            strict_version=True,  # 启用严格版本校验
            quiet=args.quiet
        )
        
        # 如果验证失败，返回错误信息
        if not is_valid:
            error_msg = _("error.verification_failed", error=error_message)
            log(error_msg, "error")
            if args.user_log:
                user_error_msg = _("error.verification_failed", error=error_message)
                user_log(user_error_msg, "error")
            
            # 检测版本信息（仅针对FortiGate）
            detected_version = None
            if args.vendor.lower() == "fortigate":
                try:
                    with open(cli_file, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                        version_detected, version_str, unused1, unused2, unused3 = detect_fortigate_version(content)
                        if version_detected:
                            detected_version = version_str
                except Exception:
                    pass
            
            # 构造错误结果
            error_result = {
                "success": False,
                "error": error_message,
                "message": translate("error.verification_failed_cannot_convert"),
                "warnings": warnings
            }
            
            # 如果检测到版本，添加到结果中
            if detected_version:
                error_result["detected_version"] = detected_version
            
            # 输出JSON格式的错误信息
            print(json.dumps(error_result, ensure_ascii=False))
            
            # 确保所有日志被刷新
            flush_all_logs()
            
            # 返回失败状态
            return 1
        
        # 验证通过，继续转换配置
        log(_("info.verification_passed"), "info")
        if args.user_log:
            user_log(_("info.verification_passed"), "info")
        
        # 确定架构选择
        use_new_architecture = None
        if hasattr(args, 'force_new_architecture') and args.force_new_architecture:
            use_new_architecture = True
        elif hasattr(args, 'force_legacy_architecture') and args.force_legacy_architecture:
            use_new_architecture = False

        # 转换配置
        result = convert_config(
            cli_file,
            mapping_file,
            args.model,
            args.version,
            output_file,
            args.vendor,    # 明确传递vendor参数
            encrypt_output,
            engine_debug_log,  # 传递引擎调试日志路径
            engine_user_log,   # 传递引擎用户日志路径
            verbose=True,      # 添加verbose参数名
            language=lang,  # 使用转换后的lang变量
            use_new_architecture=use_new_architecture  # 传递架构选择参数
        )
        
        # 在转换结果中添加报告文件路径（如果存在）
        if "report_file" in result and os.path.exists(result["report_file"]):
            report_path = result["report_file"]
        
        # 添加报告路径到结果
        result["report_path"] = report_path
        
        # 始终输出JSON结果到标准输出，无论quiet选项如何设置
        # Go端依赖这个JSON输出来判断转换是否成功
        print(json.dumps(result, ensure_ascii=False))
        
        # 确保所有日志被刷新
        flush_all_logs()
        
        return 0 if result["success"] else 1
    except Exception as e:
        # 捕获所有未处理的异常，确保以JSON格式返回错误信息
        # 确保日志不会输出到标准输出
        setup_logging(log_to_stdout=False)
        
        error_message = str(e)
        tb = traceback.format_exc()
        error_result = {
            "success": False,
            "error": f"转换过程发生系统错误: {error_message}",
            "message": f"转换过程发生系统错误: {error_message}",
            "traceback": tb
        }
        print(json.dumps(error_result, ensure_ascii=False, indent=2))
        
        # 确保所有日志被刷新
        flush_all_logs()
        
        return 1

def parse_args():
    """解析命令行参数
    
    返回:
        argparse.Namespace: 命令行参数
    """
    parser = argparse.ArgumentParser(description='多厂商配置转换工具')
    
    # 基本参数
    parser.add_argument('--mode', choices=['verify', 'extract', 'convert'], default='convert',
                        help='工作模式: verify=验证配置, extract=提取接口, convert=转换配置')
    parser.add_argument('--vendor', default='fortigate',
                        help='设备厂商 (默认: fortigate)')
    parser.add_argument('--cli', required=True,
                        help='输入配置文件路径')
    
    # 验证模式特定参数
    parser.add_argument('--json', action='store_true',
                        help='以JSON格式输出验证结果 (仅验证模式有效)')
    parser.add_argument('--device-model',
                        help='目标设备型号，用于容量限制校验 (验证模式可选，如z3200s, z5100s)')
    
    # 转换模式特定参数
    parser.add_argument('--mapping', 
                        help='接口映射文件路径 (仅转换模式需要)')
    parser.add_argument('--model', 
                        help='目标设备型号 (仅转换模式需要)')
    parser.add_argument('--version', 
                        help='目标设备版本 (仅转换模式需要)')
    parser.add_argument('--output', 
                        help='输出文件路径 (仅转换模式需要)')
    parser.add_argument('--encrypt-output', 
                        help='加密输出文件路径 (仅转换模式需要)')
    
    # 提取模式特定参数
    parser.add_argument('--output-json', 
                        help='接口信息输出JSON文件路径 (仅提取模式可用)')
    
    # 日志参数
    parser.add_argument('--log-file',
                        help='日志文件路径')
    parser.add_argument('--user-log',
                        help='用户日志文件路径')
    parser.add_argument('--log-dir',
                        help='日志目录路径，将按级别分类保存日志')
    parser.add_argument('--quiet', action='store_true',
                        help='静默模式，不输出日志到标准输出')

    # 日志级别控制参数（用户友好）
    parser.add_argument('--debug', action='store_true',
                        help='启用DEBUG级别日志，显示详细的调试信息')
    parser.add_argument('--verbose', action='store_true',
                        help='启用详细模式，等同于--debug')
    parser.add_argument('--log-level', type=int, default=logging.INFO,
                        help='日志级别 (10=DEBUG, 20=INFO, 30=WARNING, 40=ERROR, 50=CRITICAL)')
    
    # 国际化参数
    parser.add_argument('--lang', default='zh-CN',
                        help='语言 (默认: zh-CN)')

    # 架构选择参数
    parser.add_argument('--force-new-architecture', action='store_true',
                        help='强制使用新架构进行转换')
    parser.add_argument('--force-legacy-architecture', action='store_true',
                        help='强制使用传统架构进行转换')
    
    # 解析参数
    args = parser.parse_args()
    
    # 设置日志系统
    log_to_stdout = False  # 始终禁用标准输出日志，仅输出JSON

    # 🔧 日志级别优化：根据命令行参数动态设置日志级别
    if args.debug or args.verbose:
        log_level = logging.DEBUG
        user_log("启用DEBUG模式，将显示详细的调试信息")
    else:
        log_level = args.log_level  # 使用用户指定的日志级别，默认为INFO
    
    # 如果指定了日志目录，使用新的日志系统
    if args.log_dir:
        setup_logging(args.log_dir, log_to_stdout, log_level, args.quiet)
    # 否则使用传统日志文件
    elif args.log_file or args.user_log:
        setup_full_logging(args.log_file, args.user_log)
    else:
        # 默认设置：提取和验证模式不输出日志到标准输出，转换模式保持不变
        if args.mode in ['extract', 'verify']:
            setup_logging(log_to_stdout=False)
        else:
            setup_logging(log_to_stdout=log_to_stdout)
    
    # 默认启用严格版本检测        
    if args.mode in ['verify', 'convert']:
        args.strict_version = True
            
    return args

def extract_mode(args):
    """提取接口信息模式
    
    参数:
        args: 命令行参数
    """
    # 强制禁用标准输出日志，确保只有JSON结果输出
    setup_logging(log_to_stdout=False)
    
    log(_("info.extraction_start"), "info")
    if args.user_log:
        user_log(_("info.extraction_start"), "info")
    
    # 确定输出文件路径
    output_json = args.output_json
    if not output_json:
        # 默认输出到标准输出，不保存文件
        output_json = None
    
    # 将zh_CN转换为zh-CN格式
    lang = args.lang.replace('_', '-')
    
    # 提取接口信息
    result = extract_interface_info(args.cli, output_json, args.vendor, lang)
    
    # 输出JSON结果到标准输出
    if result:
        # 直接输出JSON，不使用safe_print_json，避免额外日志输出
        print(json.dumps(result, ensure_ascii=False, indent=2))
    else:
        # 输出错误信息
        error_json = {
            "success": False,
            "error": translate("error.extraction_failed"),
            "message": translate("error.extraction_failed"),
            "interfaces": [],
            "interfaces_count": 0
        }
        print(json.dumps(error_json, ensure_ascii=False, indent=2))
    
    # 无论提取成功或失败，都以正常状态退出
    sys.exit(0)

if __name__ == "__main__":
    main()
