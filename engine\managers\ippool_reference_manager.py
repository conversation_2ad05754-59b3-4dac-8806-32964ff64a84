"""
地址池引用管理器

提供地址池与NAT规则之间的引用关系管理功能，包括：
- 引用关系跟踪和维护
- 引用完整性检查和验证
- 孤立池检测和清理建议
- 引用同步更新机制
"""

from typing import Dict, List, Set, Tuple, Optional, Any
from dataclasses import dataclass, field
from collections import defaultdict
from engine.utils.logger import log
from engine.utils.i18n import _
from engine.utils.enhanced_logging import get_enhanced_logger, performance_monitor, error_handler


@dataclass
class PoolReference:
    """地址池引用信息"""
    pool_name: str
    referencing_policies: Set[str] = field(default_factory=set)
    referencing_nat_rules: Set[str] = field(default_factory=set)
    reference_count: int = 0
    is_orphaned: bool = False
    last_updated: Optional[str] = None


@dataclass
class ReferenceIntegrityResult:
    """引用完整性检查结果"""
    is_valid: bool = True
    orphaned_pools: List[str] = field(default_factory=list)
    missing_pools: List[str] = field(default_factory=list)
    inconsistent_references: List[Tuple[str, str]] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    suggestions: List[str] = field(default_factory=list)


class IPPoolReferenceManager:
    """地址池引用管理器"""
    
    def __init__(self):
        # 地址池引用映射表
        self.pool_references: Dict[str, PoolReference] = {}
        
        # 策略到池的映射
        self.policy_to_pools: Dict[str, Set[str]] = defaultdict(set)
        
        # NAT规则到池的映射
        self.nat_rule_to_pools: Dict[str, Set[str]] = defaultdict(set)
        
        # 池到策略的反向映射
        self.pool_to_policies: Dict[str, Set[str]] = defaultdict(set)
        
        # 池到NAT规则的反向映射
        self.pool_to_nat_rules: Dict[str, Set[str]] = defaultdict(set)
        
        # 变更跟踪
        self.pending_changes: List[Dict[str, Any]] = []

        # 增强日志记录器
        self.enhanced_logger = get_enhanced_logger("ippool_reference_manager")
        
    def register_pool(self, pool_name: str) -> None:
        """
        注册地址池
        
        Args:
            pool_name: 池名称
        """
        if pool_name not in self.pool_references:
            self.pool_references[pool_name] = PoolReference(pool_name=pool_name)
            log(_("reference_manager.pool_registered", pool_name=pool_name), "debug")
    
    def register_policy_pool_reference(self, policy_id: str, pool_names: List[str]) -> None:
        """
        注册策略对地址池的引用
        
        Args:
            policy_id: 策略ID
            pool_names: 引用的池名称列表
        """
        # 清理旧的引用关系
        if policy_id in self.policy_to_pools:
            old_pools = self.policy_to_pools[policy_id].copy()
            for old_pool in old_pools:
                self._remove_policy_pool_reference(policy_id, old_pool)
        
        # 建立新的引用关系
        for pool_name in pool_names:
            self._add_policy_pool_reference(policy_id, pool_name)
        
        log(_("reference_manager.policy_references_updated", 
             policy_id=policy_id, pool_count=len(pool_names)), "debug")
    
    def register_nat_rule_pool_reference(self, nat_rule_name: str, pool_names: List[str]) -> None:
        """
        注册NAT规则对地址池的引用
        
        Args:
            nat_rule_name: NAT规则名称
            pool_names: 引用的池名称列表
        """
        # 清理旧的引用关系
        if nat_rule_name in self.nat_rule_to_pools:
            old_pools = self.nat_rule_to_pools[nat_rule_name].copy()
            for old_pool in old_pools:
                self._remove_nat_rule_pool_reference(nat_rule_name, old_pool)
        
        # 建立新的引用关系
        for pool_name in pool_names:
            self._add_nat_rule_pool_reference(nat_rule_name, pool_name)
        
        log(_("reference_manager.nat_rule_references_updated", 
             nat_rule_name=nat_rule_name, pool_count=len(pool_names)), "debug")
    
    def _add_policy_pool_reference(self, policy_id: str, pool_name: str) -> None:
        """添加策略到池的引用"""
        # 确保池已注册
        self.register_pool(pool_name)
        
        # 添加引用关系
        self.policy_to_pools[policy_id].add(pool_name)
        self.pool_to_policies[pool_name].add(policy_id)
        
        # 更新池引用信息
        pool_ref = self.pool_references[pool_name]
        pool_ref.referencing_policies.add(policy_id)
        pool_ref.reference_count += 1
        pool_ref.is_orphaned = False
    
    def _remove_policy_pool_reference(self, policy_id: str, pool_name: str) -> None:
        """移除策略到池的引用"""
        if policy_id in self.policy_to_pools:
            self.policy_to_pools[policy_id].discard(pool_name)
        
        if pool_name in self.pool_to_policies:
            self.pool_to_policies[pool_name].discard(policy_id)
        
        # 更新池引用信息
        if pool_name in self.pool_references:
            pool_ref = self.pool_references[pool_name]
            pool_ref.referencing_policies.discard(policy_id)
            pool_ref.reference_count = max(0, pool_ref.reference_count - 1)
            pool_ref.is_orphaned = pool_ref.reference_count == 0
    
    def _add_nat_rule_pool_reference(self, nat_rule_name: str, pool_name: str) -> None:
        """添加NAT规则到池的引用"""
        # 确保池已注册
        self.register_pool(pool_name)
        
        # 添加引用关系
        self.nat_rule_to_pools[nat_rule_name].add(pool_name)
        self.pool_to_nat_rules[pool_name].add(nat_rule_name)
        
        # 更新池引用信息
        pool_ref = self.pool_references[pool_name]
        pool_ref.referencing_nat_rules.add(nat_rule_name)
        pool_ref.reference_count += 1
        pool_ref.is_orphaned = False
    
    def _remove_nat_rule_pool_reference(self, nat_rule_name: str, pool_name: str) -> None:
        """移除NAT规则到池的引用"""
        if nat_rule_name in self.nat_rule_to_pools:
            self.nat_rule_to_pools[nat_rule_name].discard(pool_name)
        
        if pool_name in self.pool_to_nat_rules:
            self.pool_to_nat_rules[pool_name].discard(nat_rule_name)
        
        # 更新池引用信息
        if pool_name in self.pool_references:
            pool_ref = self.pool_references[pool_name]
            pool_ref.referencing_nat_rules.discard(nat_rule_name)
            pool_ref.reference_count = max(0, pool_ref.reference_count - 1)
            pool_ref.is_orphaned = pool_ref.reference_count == 0
    
    @performance_monitor("check_reference_integrity")
    @error_handler("check_reference_integrity", reraise=False)
    def check_reference_integrity(self, available_pools: Set[str]) -> ReferenceIntegrityResult:
        """
        检查引用完整性
        
        Args:
            available_pools: 可用的池名称集合
            
        Returns:
            ReferenceIntegrityResult: 完整性检查结果
        """
        result = ReferenceIntegrityResult()
        
        # 检查孤立池（定义了但没有被引用的池）
        orphaned_pools = []
        for pool_name in available_pools:
            if pool_name in self.pool_references:
                pool_ref = self.pool_references[pool_name]
                if pool_ref.reference_count == 0:
                    orphaned_pools.append(pool_name)
            else:
                # 池存在但没有引用记录，也算孤立
                orphaned_pools.append(pool_name)
        
        result.orphaned_pools = orphaned_pools
        
        # 检查缺失池（被引用但不存在的池）
        missing_pools = []
        for pool_name in self.pool_references:
            if pool_name not in available_pools and self.pool_references[pool_name].reference_count > 0:
                missing_pools.append(pool_name)
        
        result.missing_pools = missing_pools
        
        # 检查引用不一致
        inconsistent_refs = []
        for policy_id, pools in self.policy_to_pools.items():
            for pool_name in pools:
                if policy_id not in self.pool_to_policies.get(pool_name, set()):
                    inconsistent_refs.append((policy_id, pool_name))
        
        result.inconsistent_references = inconsistent_refs
        
        # 生成警告和建议
        if orphaned_pools:
            result.warnings.append(_("reference_manager.orphaned_pools_found", count=len(orphaned_pools)))
            result.suggestions.append(_("reference_manager.suggest_remove_orphaned_pools"))
        
        if missing_pools:
            result.warnings.append(_("reference_manager.missing_pools_found", count=len(missing_pools)))
            result.suggestions.append(_("reference_manager.suggest_create_missing_pools"))
        
        if inconsistent_refs:
            result.warnings.append(_("reference_manager.inconsistent_references_found", count=len(inconsistent_refs)))
            result.suggestions.append(_("reference_manager.suggest_rebuild_references"))
        
        # 设置整体有效性
        result.is_valid = not (missing_pools or inconsistent_refs)
        
        return result
    
    def get_pool_usage_statistics(self) -> Dict[str, Dict[str, Any]]:
        """
        获取池使用统计信息
        
        Returns:
            Dict: 池使用统计
        """
        stats = {}
        
        for pool_name, pool_ref in self.pool_references.items():
            stats[pool_name] = {
                "reference_count": pool_ref.reference_count,
                "referencing_policies": list(pool_ref.referencing_policies),
                "referencing_nat_rules": list(pool_ref.referencing_nat_rules),
                "is_orphaned": pool_ref.is_orphaned,
                "policy_count": len(pool_ref.referencing_policies),
                "nat_rule_count": len(pool_ref.referencing_nat_rules)
            }
        
        return stats

    def rename_pool(self, old_name: str, new_name: str) -> bool:
        """
        重命名地址池并同步更新所有引用

        Args:
            old_name: 原池名称
            new_name: 新池名称

        Returns:
            bool: 重命名是否成功
        """
        if old_name not in self.pool_references:
            log(_("reference_manager.pool_not_found", pool_name=old_name), "warning")
            return False

        if new_name in self.pool_references:
            log(_("reference_manager.pool_name_conflict", new_name=new_name), "warning")
            return False

        try:
            # 获取旧池的引用信息
            old_pool_ref = self.pool_references[old_name]

            # 创建新池引用
            new_pool_ref = PoolReference(
                pool_name=new_name,
                referencing_policies=old_pool_ref.referencing_policies.copy(),
                referencing_nat_rules=old_pool_ref.referencing_nat_rules.copy(),
                reference_count=old_pool_ref.reference_count,
                is_orphaned=old_pool_ref.is_orphaned
            )

            # 更新池引用映射
            self.pool_references[new_name] = new_pool_ref
            del self.pool_references[old_name]

            # 更新策略到池的映射
            for policy_id in old_pool_ref.referencing_policies:
                if policy_id in self.policy_to_pools:
                    self.policy_to_pools[policy_id].discard(old_name)
                    self.policy_to_pools[policy_id].add(new_name)

            # 更新NAT规则到池的映射
            for nat_rule in old_pool_ref.referencing_nat_rules:
                if nat_rule in self.nat_rule_to_pools:
                    self.nat_rule_to_pools[nat_rule].discard(old_name)
                    self.nat_rule_to_pools[nat_rule].add(new_name)

            # 更新反向映射
            if old_name in self.pool_to_policies:
                self.pool_to_policies[new_name] = self.pool_to_policies[old_name]
                del self.pool_to_policies[old_name]

            if old_name in self.pool_to_nat_rules:
                self.pool_to_nat_rules[new_name] = self.pool_to_nat_rules[old_name]
                del self.pool_to_nat_rules[old_name]

            # 记录变更
            self.pending_changes.append({
                "type": "rename_pool",
                "old_name": old_name,
                "new_name": new_name,
                "affected_policies": list(old_pool_ref.referencing_policies),
                "affected_nat_rules": list(old_pool_ref.referencing_nat_rules)
            })

            log(_("reference_manager.pool_renamed_successfully",
                 old_name=old_name, new_name=new_name), "info")
            return True

        except Exception as e:
            log(_("reference_manager.pool_rename_failed",
                 old_name=old_name, new_name=new_name, error=str(e)), "error")
            return False

    def get_pool_dependencies(self, pool_name: str) -> Dict[str, List[str]]:
        """
        获取地址池的依赖关系

        Args:
            pool_name: 池名称

        Returns:
            Dict: 依赖关系信息
        """
        if pool_name not in self.pool_references:
            return {"policies": [], "nat_rules": []}

        pool_ref = self.pool_references[pool_name]
        return {
            "policies": list(pool_ref.referencing_policies),
            "nat_rules": list(pool_ref.referencing_nat_rules)
        }

    def suggest_pool_optimizations(self) -> List[Dict[str, Any]]:
        """
        建议池优化方案

        Returns:
            List: 优化建议列表
        """
        suggestions = []

        # 检查未使用的池
        orphaned_pools = [name for name, ref in self.pool_references.items() if ref.is_orphaned]
        if orphaned_pools:
            suggestions.append({
                "type": "remove_orphaned",
                "description": _("reference_manager.suggest_remove_orphaned"),
                "pools": orphaned_pools,
                "priority": "medium"
            })

        # 检查过度使用的池
        overused_pools = [
            name for name, ref in self.pool_references.items()
            if ref.reference_count > 10  # 阈值可配置
        ]
        if overused_pools:
            suggestions.append({
                "type": "split_overused",
                "description": _("reference_manager.suggest_split_overused"),
                "pools": overused_pools,
                "priority": "low"
            })

        # 检查单一引用的池
        single_use_pools = [
            name for name, ref in self.pool_references.items()
            if ref.reference_count == 1
        ]
        if len(single_use_pools) > 5:  # 如果单一使用的池太多
            suggestions.append({
                "type": "consolidate_single_use",
                "description": _("reference_manager.suggest_consolidate_single_use"),
                "pools": single_use_pools[:5],  # 只显示前5个作为示例
                "priority": "low"
            })

        return suggestions

    def generate_reference_report(self) -> str:
        """
        生成引用关系报告

        Returns:
            str: 格式化的引用报告
        """
        report_lines = []
        report_lines.append("=== 地址池引用关系报告 ===")
        report_lines.append("")

        # 总体统计
        total_pools = len(self.pool_references)
        active_pools = sum(1 for ref in self.pool_references.values() if not ref.is_orphaned)
        orphaned_pools = total_pools - active_pools

        report_lines.append(f"总计地址池: {total_pools}")
        report_lines.append(f"活跃地址池: {active_pools}")
        report_lines.append(f"孤立地址池: {orphaned_pools}")
        report_lines.append("")

        # 详细信息
        for pool_name, pool_ref in sorted(self.pool_references.items()):
            report_lines.append(f"--- {pool_name} ---")
            report_lines.append(f"引用次数: {pool_ref.reference_count}")
            report_lines.append(f"状态: {'孤立' if pool_ref.is_orphaned else '活跃'}")

            if pool_ref.referencing_policies:
                report_lines.append(f"引用策略 ({len(pool_ref.referencing_policies)}):")
                for policy in sorted(pool_ref.referencing_policies):
                    report_lines.append(f"  - {policy}")

            if pool_ref.referencing_nat_rules:
                report_lines.append(f"引用NAT规则 ({len(pool_ref.referencing_nat_rules)}):")
                for nat_rule in sorted(pool_ref.referencing_nat_rules):
                    report_lines.append(f"  - {nat_rule}")

            report_lines.append("")

        # 优化建议
        suggestions = self.suggest_pool_optimizations()
        if suggestions:
            report_lines.append("=== 优化建议 ===")
            for i, suggestion in enumerate(suggestions, 1):
                report_lines.append(f"{i}. {suggestion['description']} (优先级: {suggestion['priority']})")
                if suggestion.get('pools'):
                    report_lines.append(f"   涉及池: {', '.join(suggestion['pools'][:3])}")
                    if len(suggestion['pools']) > 3:
                        report_lines.append(f"   ... 等{len(suggestion['pools'])}个")
            report_lines.append("")

        return "\n".join(report_lines)

    def clear_all_references(self) -> None:
        """清除所有引用关系"""
        self.pool_references.clear()
        self.policy_to_pools.clear()
        self.nat_rule_to_pools.clear()
        self.pool_to_policies.clear()
        self.pool_to_nat_rules.clear()
        self.pending_changes.clear()
        log(_("reference_manager.all_references_cleared"), "info")

    def get_pending_changes(self) -> List[Dict[str, Any]]:
        """获取待处理的变更"""
        return self.pending_changes.copy()

    def commit_changes(self) -> None:
        """提交变更（清除待处理变更列表）"""
        change_count = len(self.pending_changes)
        self.pending_changes.clear()
        log(_("reference_manager.changes_committed", count=change_count), "info")
