import json
from engine.utils.logger import log
from engine.utils.i18n import _

def load_interface_mapping(mapping_file):
    with open(mapping_file, 'r', encoding='utf-8') as f:
        return json.load(f)

def map_interface_names(parsed_data, mapping):
    log("Mapping interface names...")
    log(f"Available mappings: {mapping}")
    
    # 确保parsed_data是字典类型，并且包含interfaces键
    if not isinstance(parsed_data, dict):
        log(_("error.parsed_data_not_dict"), "error", data_type=str(type(parsed_data)))
        return parsed_data
    
    # 获取接口列表
    interfaces = parsed_data.get('interfaces', [])
    if not interfaces:
        log("警告: 没有找到接口信息", "warning")
        return parsed_data
    
    # 创建物理接口名称到NTOS接口名称的映射
    ntos_interfaces = {}
    for intf in interfaces:
        old_name = intf.get("raw_name")
        if old_name and not intf.get("is_subinterface", False):
            new_name = mapping.get(old_name, old_name)
            ntos_interfaces[old_name] = new_name
    
    # 映射接口名称
    for intf in interfaces:
        old_name = intf.get("raw_name")
        if old_name:
            # 检查是否为无效接口
            if intf.get("invalid", False):
                invalid_reason = intf.get("invalid_reason", "未知原因")
                log(_("warning.skipping_invalid_interface_with_reason"), "warning",
                interface_name=old_name, reason=invalid_reason)
                continue
                
            # 处理子接口
            if intf.get("is_subinterface", False) and "parent_interface" in intf and "vlanid" in intf:
                parent = intf["parent_interface"]
                vlanid = intf["vlanid"]
                
                # 获取父接口映射后的NTOS名称
                parent_ntos_name = ntos_interfaces.get(parent, mapping.get(parent, parent))
                
                # 创建NTOS格式的子接口名称: 父接口名.VLANID
                subintf_name = f"{parent_ntos_name}.{vlanid}"
                
                log(f"Mapping subinterface: {old_name} -> {subintf_name} (parent: {parent} -> {parent_ntos_name}, vlanid: {vlanid})")
                
                # 保留原始名称
                intf["raw_name"] = old_name
                # 设置新名称
                intf["name"] = subintf_name
                # 保存父接口的NTOS名称，用于生成XML
                intf["ntos_parent"] = parent_ntos_name
            else:
                # 普通接口处理
                new_name = mapping.get(old_name, old_name)
                log(f"Mapping interface: {old_name} -> {new_name}")
                
                # 保留原始名称
                intf["raw_name"] = old_name
                # 设置新名称
                intf["name"] = new_name
        else:
            log(_("warning.interface_missing_raw_name_field"), "warning", interface=str(intf))
    
    # 处理静态路由中的接口引用
    if 'static_routes' in parsed_data:
        for route in parsed_data['static_routes']:
            device = route.get('device')
            if device and device in mapping:
                route['device'] = mapping[device]
                log(f"Mapping static route device: {device} -> {mapping[device]}")
    
    return parsed_data
