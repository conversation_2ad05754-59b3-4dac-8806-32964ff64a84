# -*- coding: utf-8 -*-
"""
FortiGate服务映射器
负责FortiGate预定义服务到NTOS预定义服务的映射转换
"""

from typing import Dict, List, Optional, Any
from engine.utils.logger import log
from engine.utils.i18n import _


class FortigateServiceMapper:
    """
    FortiGate服务映射器
    处理FortiGate预定义服务到NTOS预定义服务的映射转换
    """

    # 需要创建自定义服务的FortiGate服务列表
    # 这些服务由于协议复杂性、多端口或NTOS中缺失而无法直接映射
    FORTIGATE_SERVICES_REQUIRING_CUSTOM_CREATION = {
        # 多协议服务（TCP+UDP）
        "DNS",           # TCP+UDP 53
        "NTP",           # TCP+UDP 123
        "SIP",           # TCP+UDP 5060
        "SNMP",          # TCP+UDP 161-162
        "KERBEROS",      # TCP+UDP 88,464

        # 多端口服务
        "MS-SQL",        # TCP 1433,1434
        "MSSQL",         # TCP 1433,1434
        "H323",          # TCP 1720,1503 + UDP 1719
        "MGCP",          # TCP 2428 + UDP 2427,2727
        "RTSP",          # TCP 554,7070,8554 + UDP 554

        # NTOS中缺失的服务
        "SMB",           # TCP 445 (NTOS只有samba TCP 139)
        "CIFS",          # TCP 445 (同SMB)
        "DCE-RPC",       # TCP+UDP 135 (Windows AD核心)

        # 特殊协议服务
        "TRACEROUTE",    # UDP 33434-33535
    }

    # FortiGate预定义服务到NTOS预定义服务的纯净直接映射表
    # 只包含协议类型、端口号、语义完全匹配的映射关系
    FORTIGATE_TO_NTOS_SERVICE_MAPPING = {
        # 基础网络服务 - 只保留100%技术匹配的直接映射
        "ALL": "any",
        "ALL_TCP": "tcp",
        "ALL_UDP": "udp",
        "ALL_ICMP": "icmp",
        "HTTP": "http",           # TCP 80 -> TCP 80 ✅
        "HTTPS": "https",         # TCP 443 -> TCP 443 ✅
        "SSH": "ssh",             # TCP 22 -> TCP 22 ✅
        "TELNET": "telnet",       # TCP 23 -> TCP 23 ✅
        "FTP": "ftp",             # TCP 21 -> TCP 21 ✅
        "FTP_GET": "ftp",         # TCP 21 -> TCP 21 ✅
        "FTP_PUT": "ftp",         # TCP 21 -> TCP 21 ✅
        "SMTP": "smtp",           # TCP 25 -> TCP 25 ✅
        "POP3": "pop3",           # TCP 110 -> TCP 110 ✅
        "IMAP": "imap",           # TCP 143 -> TCP 143 ✅
        # DNS - 移除，需要自定义服务（TCP+UDP 53）
        "DHCP": "dhcp",           # UDP 67-68 -> UDP 67,68 ✅
        "DHCP6": "dhcp6",         # UDP 546-547 -> UDP 546,547 ✅
        # NTP - 移除，需要自定义服务（TCP+UDP 123）
        # SNMP - 移除，需要自定义服务（TCP+UDP 161-162）
        "SNMP_TRAP": "snmp_trap", # UDP 162 -> UDP 162 ✅
        "SYSLOG": "syslog",       # UDP 514 -> UDP 514 ✅
        "TFTP": "tftp",           # UDP 69 -> UDP 69 ✅

        # 数据库服务 - 只保留完全匹配的映射
        "MYSQL": "mysql",         # TCP 3306 -> TCP 3306 ✅
        # MS-SQL - 移除，需要自定义服务（TCP 1433,1434）
        # MSSQL - 移除，需要自定义服务（TCP 1433,1434）
        "ORACLE": "mysql",        # NTOS没有Oracle，映射到通用数据库服务

        # 网络协议 - 保留完全匹配的映射
        "PING": "ping",           # ICMP type 8 -> ICMP type 8 ✅
        "PING6": "ping6",         # ICMPv6 type 128 -> ICMPv6 type 128 ✅
        "ICMP": "icmp",           # ICMP -> ICMP ✅
        "ICMPV6": "icmpv6",       # ICMPv6 -> ICMPv6 ✅
        "GRE": "gre",             # IP protocol 47 -> IP protocol 47 ✅
        "ESP": "esp",             # IP protocol 50 -> IP protocol 50 ✅
        "AH": "ah",               # IP protocol 51 -> IP protocol 51 ✅
        "OSPF": "ospf",           # IP protocol 89 -> IP protocol 89 ✅

        # 远程访问 - 保留完全匹配的映射
        "RDP": "rdp",             # TCP 3389 -> TCP 3389 ✅
        "VNC": "vnc",             # TCP 5900 -> TCP 5900 ✅
        "RLOGIN": "rlogin",       # TCP 513 -> TCP 513 ✅
        "RSH": "rlogin",          # TCP 514 -> TCP 513 (需要验证)
        "REXEC": "rlogin",        # TCP 512 -> TCP 513 (需要验证)

        # Web服务 - 保留完全匹配的映射
        "SQUID": "squid",         # TCP 3128 -> TCP 3128 ✅
        # HTTP_PROXY, HTTPS_PROXY - 需要验证端口匹配

        # 邮件服务 - 保留完全匹配的映射
        "SMTPS": "smtps",         # TCP 465 -> TCP 465 ✅
        "POP3S": "pop3s",         # TCP 995 -> TCP 995 ✅
        "IMAPS": "imaps",         # TCP 993 -> TCP 993 ✅

        # 网络管理和认证 - 保留完全匹配的映射
        "RADIUS": "radius",       # UDP 1812,1813 -> UDP 1812,1813 ✅
        "RADIUS-OLD": "radius_old", # UDP 1645,1646 -> UDP 1645,1646 ✅
        "LDAP": "ldap",           # TCP 389 -> TCP 389 ✅
        "LDAP_UDP": "ldap",       # UDP 389 -> TCP 389 (需要验证协议匹配)
        # KERBEROS - 移除，需要自定义服务（TCP+UDP 88,464）
        "WINS": "wins",           # TCP+UDP 1512 -> 需要验证NTOS支持
        # DCE-RPC - 移除，需要自定义服务（TCP+UDP 135）

        # 多媒体和VoIP - 保留完全匹配的映射
        # SIP - 移除，需要自定义服务（TCP+UDP 5060）
        "SIP-MSNmessenger": "sip-msnmessenger", # TCP 1863 -> TCP 1863 ✅
        # RTSP - 移除，需要自定义服务（多端口）
        # H323 - 移除，需要自定义服务（多端口）
        "NetMeeting": "h225",     # TCP 1720 -> TCP 1720 ✅
        # MGCP - 移除，需要自定义服务（多端口）
        "MMS": "mms",             # TCP 1755 -> TCP 1755 ✅

        # 文件服务 - 保留完全匹配的映射
        "NFS": "nfs",             # TCP+UDP 2049,111 -> TCP+UDP 2049,111 ✅
        # SMB - 移除，需要自定义服务（TCP 445）
        "SAMBA": "samba",         # TCP 139 -> TCP 139 ✅
        "NETBIOS": "netbios-ns",  # UDP 137 -> UDP 137 ✅
        # CIFS - 移除，需要自定义服务（TCP 445）

        # 隧道协议 - 保留完全匹配的映射
        "L2TP": "l2tp",           # TCP+UDP 1701 -> TCP+UDP 1701 ✅
        "PPTP": "pptp",           # TCP 1723 -> TCP 1723 ✅
        "IKE": "ike",             # UDP 500,4500 -> UDP 500,4500 ✅
        "SOCKS": "socks",         # TCP+UDP 1080 -> TCP+UDP 1080 ✅

        # 其他网络服务 - 保留完全匹配的映射
        "BGP": "bgp",             # TCP 179 -> TCP 179 ✅
        "RIP": "rip",             # UDP 520 -> UDP 520 ✅
        "IRC": "irc",             # TCP 194 -> TCP 194 ✅
        "NNTP": "nntp",           # TCP 119 -> TCP 119 ✅
        # TRACEROUTE - 移除，需要自定义服务（UDP 33434-33535）

        # 特殊服务
        "NONE": "any",            # 特殊服务：无端口限制
        "webproxy": "squid"       # 代理服务映射
    }
    
    def __init__(self):
        """初始化服务映射器"""
        self.custom_services_created = []  # 记录自动创建的自定义服务
        
    def map_service(self, fortigate_service: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        映射FortiGate服务到NTOS服务

        优化后的映射逻辑：
        1. 首先检查纯净的直接映射表
        2. 然后检查是否需要创建自定义服务
        3. 最后回退到通用自定义服务创建

        Args:
            fortigate_service: FortiGate服务名称
            context: 上下文信息（暂未使用）

        Returns: Dict[str, Any]: 映射结果，包含服务名称和是否需要创建自定义服务
        """
        if not fortigate_service:
            log(_("fortigate_service_mapper.empty_service_name"), "warning")
            return {
                "ntos_service": "any",
                "needs_custom_creation": False,
                "original_service": fortigate_service
            }

        service_upper = fortigate_service.upper()

        # 第一步：检查纯净的直接映射表
        ntos_service = self.FORTIGATE_TO_NTOS_SERVICE_MAPPING.get(service_upper)

        if ntos_service:
            log(_("fortigate_service_mapper.service_mapped"), "debug",
                fortigate=fortigate_service, ntos=ntos_service)
            return {
                "ntos_service": ntos_service,
                "needs_custom_creation": False,
                "original_service": fortigate_service
            }

        # 第二步：检查是否需要创建自定义服务
        if service_upper in self.FORTIGATE_SERVICES_REQUIRING_CUSTOM_CREATION:
            log(_("fortigate_service_mapper.service_needs_custom_creation"), "info",
                service=fortigate_service)
            return {
                "ntos_service": fortigate_service,  # 保持原名称用于自定义服务创建
                "needs_custom_creation": True,
                "original_service": fortigate_service,
                "custom_service_type": "known_complex"  # 已知的复杂服务
            }

        # 第三步：回退到通用自定义服务创建
        log(_("fortigate_service_mapper.service_needs_custom_creation"), "info",
            service=fortigate_service)
        return {
            "ntos_service": fortigate_service,  # 保持原名称
            "needs_custom_creation": True,
            "original_service": fortigate_service,
            "custom_service_type": "unknown"  # 未知服务
        }
    
    def map_service_list(self, fortigate_services: List[str]) -> List[Dict[str, Any]]:
        """
        批量映射FortiGate服务列表
        
        Args:
            fortigate_services: FortiGate服务名称列表
            
        Returns: List[Dict[str, Any]]: 映射结果列表
        """
        if not fortigate_services:
            return []
        
        results = []
        for service in fortigate_services:
            result = self.map_service(service)
            results.append(result)
        
        return results
    
    def create_custom_service_config(self, service_name: str, service_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建自定义服务配置

        Args:
            service_name: 服务名称
            service_config: FortiGate服务配置

        Returns: Dict[str, Any]: NTOS自定义服务配置
        """
        # 基于FortiGate实际配置的预定义自定义服务
        # 这些配置直接对应FortiGate配置文件中的服务定义
        predefined_custom_services = {
            # 基于FortiGate配置第11623-11625行：SMB TCP 445
            "SMB": {
                "name": "SMB_TCP_445",
                "description": "SMB文件共享服务 (TCP 445端口) - 现代SMB协议",
                "protocol": "tcp",
                "source-port": "0-65535",
                "destination-port": "445"
            },

            # 基于FortiGate配置第11564-11567行：DNS TCP+UDP 53
            "DNS": {
                "name": "DNS_TCP_UDP_53",
                "description": "域名解析服务 (TCP+UDP 53端口) - 支持完整DNS功能",
                "protocol": "tcp,udp",
                "source-port": "0-65535",
                "destination-port": "53"
            },

            # 基于FortiGate配置第11725-11728行：NTP TCP+UDP 123
            "NTP": {
                "name": "NTP_TCP_UDP_123",
                "description": "网络时间协议 (TCP+UDP 123端口) - 时间同步服务",
                "protocol": "tcp,udp",
                "source-port": "0-65535",
                "destination-port": "123"
            },

            # 基于FortiGate配置第11589-11592行：DCE-RPC TCP+UDP 135
            "DCE-RPC": {
                "name": "DCE_RPC_TCP_UDP_135",
                "description": "分布式计算环境RPC (TCP+UDP 135端口) - Windows AD核心服务",
                "protocol": "tcp,udp",
                "source-port": "0-65535",
                "destination-port": "135"
            },

            # 基于FortiGate配置第11614-11617行：KERBEROS TCP+UDP 88,464
            "KERBEROS": {
                "name": "KERBEROS_TCP_UDP_88_464",
                "description": "Kerberos认证协议 (TCP+UDP 88,464端口) - 域认证服务",
                "protocol": "tcp,udp",
                "source-port": "0-65535",
                "destination-port": "88,464"
            },

            # 基于FortiGate配置第11852-11854行：MS-SQL TCP 1433,1434
            "MS-SQL": {
                "name": "MSSQL_TCP_1433_1434",
                "description": "Microsoft SQL Server (TCP 1433,1434端口) - 数据库服务",
                "protocol": "tcp",
                "source-port": "0-65535",
                "destination-port": "1433,1434"
            },

            # MSSQL别名，指向同一配置
            "MSSQL": {
                "name": "MSSQL_TCP_1433_1434",
                "description": "Microsoft SQL Server (TCP 1433,1434端口) - 数据库服务",
                "protocol": "tcp",
                "source-port": "0-65535",
                "destination-port": "1433,1434"
            },

            # 基于FortiGate配置第11802-11805行：SNMP TCP+UDP 161-162
            "SNMP": {
                "name": "SNMP_TCP_UDP_161_162",
                "description": "简单网络管理协议 (TCP+UDP 161-162端口) - 网络管理",
                "protocol": "tcp,udp",
                "source-port": "0-65535",
                "destination-port": "161-162"
            },

            # 基于FortiGate配置第11793-11796行：SIP TCP+UDP 5060
            "SIP": {
                "name": "SIP_TCP_UDP_5060",
                "description": "会话初始协议 (TCP+UDP 5060端口) - VoIP信令",
                "protocol": "tcp,udp",
                "source-port": "0-65535",
                "destination-port": "5060"
            },

            # 基于FortiGate配置第11693-11696行：H323 TCP 1720,1503 + UDP 1719
            "H323": {
                "name": "H323_MULTI_PORT",
                "description": "H.323视频会议协议 (TCP 1720,1503 + UDP 1719) - 多媒体通信",
                "protocol": "tcp,udp",
                "source-port": "0-65535",
                "destination-port": "1720,1503,1719"
            },

            # 基于FortiGate配置第11826-11829行：MGCP TCP 2428 + UDP 2427,2727
            "MGCP": {
                "name": "MGCP_MULTI_PORT",
                "description": "媒体网关控制协议 (TCP 2428 + UDP 2427,2727) - VoIP网关控制",
                "protocol": "tcp,udp",
                "source-port": "0-65535",
                "destination-port": "2428,2427,2727"
            },

            # 基于FortiGate配置第11906-11909行：RTSP TCP 554,7070,8554 + UDP 554
            "RTSP": {
                "name": "RTSP_MULTI_PORT",
                "description": "实时流协议 (TCP 554,7070,8554 + UDP 554) - 流媒体控制",
                "protocol": "tcp,udp",
                "source-port": "0-65535",
                "destination-port": "554,7070,8554"
            },

            # CIFS别名，指向SMB配置（基于语义分析）
            "CIFS": {
                "name": "SMB_TCP_445",
                "description": "通用互联网文件系统 (TCP 445端口) - 等同于SMB",
                "protocol": "tcp",
                "source-port": "0-65535",
                "destination-port": "445"
            },

            # 基于FortiGate配置第11902-11904行：TRACEROUTE UDP 33434-33535
            "TRACEROUTE": {
                "name": "TRACEROUTE_UDP_33434_33535",
                "description": "路径跟踪协议 (UDP 33434-33535端口) - 网络诊断",
                "protocol": "udp",
                "source-port": "0-65535",
                "destination-port": "33434-33535"
            }
        }

        if service_name in predefined_custom_services:
            log(_("fortigate_service_mapper.predefined_custom_service_created"), "info",
                service=service_name)
            return predefined_custom_services[service_name]

        # 基础服务配置结构
        custom_service = {
            "name": service_name,
            "description": f"Auto-created from FortiGate service: {service_name}",
            "protocol": "tcp",  # 默认TCP
            "source-port": "0-65535",  # 默认源端口范围
            "destination-port": "80"  # 默认目标端口
        }
        
        # 根据FortiGate服务配置设置具体参数
        if service_config:
            # 处理TCP端口范围
            if "tcp-portrange" in service_config:
                tcp_ports = service_config["tcp-portrange"]
                if isinstance(tcp_ports, list):
                    custom_service["destination-port"] = " ".join(map(str, tcp_ports))
                else:
                    custom_service["destination-port"] = str(tcp_ports)
            
            # 处理UDP端口范围
            elif "udp-portrange" in service_config:
                custom_service["protocol"] = "udp"
                udp_ports = service_config["udp-portrange"]
                if isinstance(udp_ports, list):
                    custom_service["destination-port"] = " ".join(map(str, udp_ports))
                else:
                    custom_service["destination-port"] = str(udp_ports)
            
            # 处理ICMP类型
            elif "icmptype" in service_config:
                custom_service["protocol"] = "icmp"
                custom_service["icmp-type"] = service_config["icmptype"]
            
            # 处理IP协议
            elif "protocol" in service_config:
                protocol_num = service_config["protocol"]
                if protocol_num == "6":
                    custom_service["protocol"] = "tcp"
                elif protocol_num == "17":
                    custom_service["protocol"] = "udp"
                elif protocol_num == "1":
                    custom_service["protocol"] = "icmp"
                else:
                    custom_service["protocol"] = f"ip-protocol-{protocol_num}"
        
        # 记录创建的自定义服务
        self.custom_services_created.append(service_name)
        
        log(_("fortigate_service_mapper.custom_service_created"), "info",
            service=service_name, config=custom_service)
        
        return custom_service
    
    def get_created_custom_services(self) -> List[str]:
        """
        获取已创建的自定义服务列表
        
        Returns: List[str]: 自定义服务名称列表
        """
        return self.custom_services_created.copy()
    
    def reset_custom_services(self):
        """重置自定义服务记录"""
        self.custom_services_created.clear()
        log(_("fortigate_service_mapper.custom_services_reset"), "debug")
