{"use_modular_system": false, "parallel_validation": true, "auto_rollback_enabled": true, "error_rate_threshold": 0.05, "performance_degradation_threshold": 1.5, "memory_usage_threshold": 2.0, "migration_phase": "validation", "selective_criteria": {"max_config_size_kb": 100, "test_mode_only": true, "specific_customers": []}, "monitoring_enabled": true, "detailed_logging": false, "metrics_collection": true, "rollback_on_error_rate": true, "rollback_on_performance": true, "rollback_on_memory": true, "manual_rollback_only": false}