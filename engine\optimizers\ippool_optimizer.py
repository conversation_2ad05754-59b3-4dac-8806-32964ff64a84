"""
地址池优化建议引擎

基于使用模式分析提供智能优化建议，包括：
- 池容量优化建议
- 地址范围重组建议
- 引用关系优化建议
- 性能提升建议
"""

import ipaddress
from typing import Dict, List, Optional, Any, Tuple, Set
from dataclasses import dataclass, field
from collections import defaultdict
from engine.utils.logger import log
from engine.utils.i18n import _


@dataclass
class OptimizationRecommendation:
    """优化建议"""
    recommendation_id: str
    type: str  # "capacity", "consolidation", "fragmentation", "naming", "performance"
    priority: str  # "critical", "high", "medium", "low"
    title: str
    description: str
    affected_pools: List[str] = field(default_factory=list)
    expected_benefits: List[str] = field(default_factory=list)
    implementation_steps: List[str] = field(default_factory=list)
    estimated_effort: str = "medium"  # "low", "medium", "high"
    risk_level: str = "low"  # "low", "medium", "high"
    prerequisites: List[str] = field(default_factory=list)


@dataclass
class OptimizationPlan:
    """优化计划"""
    plan_id: str
    recommendations: List[OptimizationRecommendation] = field(default_factory=list)
    execution_order: List[str] = field(default_factory=list)
    total_estimated_effort: str = "medium"
    expected_improvements: Dict[str, Any] = field(default_factory=dict)
    risk_assessment: Dict[str, Any] = field(default_factory=dict)


class IPPoolOptimizer:
    """地址池优化建议引擎"""
    
    def __init__(self):
        # 优化阈值配置
        self.capacity_waste_threshold = 0.7  # 容量浪费阈值
        self.fragmentation_threshold = 0.4   # 碎片化阈值
        self.consolidation_threshold = 3     # 合并候选池数量阈值
        self.efficiency_improvement_target = 20  # 效率提升目标（百分点）
        
        # 建议计数器
        self.recommendation_counter = 0
    
    def generate_optimization_recommendations(self, 
                                           pool_configs: List[Dict[str, Any]],
                                           usage_analysis_result=None,
                                           reference_manager=None) -> List[OptimizationRecommendation]:
        """
        生成优化建议
        
        Args:
            pool_configs: 地址池配置列表
            usage_analysis_result: 使用分析结果
            reference_manager: 引用管理器
            
        Returns: List[OptimizationRecommendation]: 优化建议列表
        """
        recommendations = []
        
        # 容量优化建议
        capacity_recs = self._generate_capacity_recommendations(pool_configs, usage_analysis_result)
        recommendations.extend(capacity_recs)
        
        # 合并优化建议
        consolidation_recs = self._generate_consolidation_recommendations(pool_configs, usage_analysis_result, reference_manager)
        recommendations.extend(consolidation_recs)
        
        # 碎片化优化建议
        fragmentation_recs = self._generate_fragmentation_recommendations(pool_configs)
        recommendations.extend(fragmentation_recs)
        
        # 命名优化建议
        naming_recs = self._generate_naming_recommendations(pool_configs)
        recommendations.extend(naming_recs)
        
        # 性能优化建议
        performance_recs = self._generate_performance_recommendations(pool_configs, usage_analysis_result)
        recommendations.extend(performance_recs)
        
        # 按优先级排序
        recommendations.sort(key=lambda x: self._get_priority_weight(x.priority), reverse=True)
        
        log(_("optimizer.recommendations_generated", count=len(recommendations)), "info")
        return recommendations
    
    def _generate_capacity_recommendations(self, pool_configs: List[Dict[str, Any]], 
                                         usage_analysis_result=None) -> List[OptimizationRecommendation]:
        """生成容量优化建议"""
        recommendations = []
        
        if not usage_analysis_result:
            return recommendations
        
        # 建议扩展过度使用的池
        for pool_name in usage_analysis_result.overutilized_pools:
            rec = OptimizationRecommendation(
                recommendation_id=self._get_next_id(),
                type="capacity",
                priority="high",
                title=_("optimizer.expand_overutilized_pool_title"),
                description=_("optimizer.expand_overutilized_pool_desc", pool_name=pool_name),
                affected_pools=[pool_name],
                expected_benefits=[
                    _("optimizer.benefit_avoid_exhaustion"),
                    _("optimizer.benefit_improve_performance"),
                    _("optimizer.benefit_reduce_conflicts")
                ],
                implementation_steps=[
                    _("optimizer.step_analyze_current_usage"),
                    _("optimizer.step_calculate_expansion_size"),
                    _("optimizer.step_update_pool_config"),
                    _("optimizer.step_test_configuration")
                ],
                estimated_effort="medium",
                risk_level="low"
            )
            recommendations.append(rec)
        
        # 建议缩减未充分利用的池
        for pool_name in usage_analysis_result.underutilized_pools:
            rec = OptimizationRecommendation(
                recommendation_id=self._get_next_id(),
                type="capacity",
                priority="medium",
                title=_("optimizer.shrink_underutilized_pool_title"),
                description=_("optimizer.shrink_underutilized_pool_desc", pool_name=pool_name),
                affected_pools=[pool_name],
                expected_benefits=[
                    _("optimizer.benefit_reduce_waste"),
                    _("optimizer.benefit_optimize_resources"),
                    _("optimizer.benefit_simplify_management")
                ],
                implementation_steps=[
                    _("optimizer.step_verify_minimum_requirements"),
                    _("optimizer.step_calculate_optimal_size"),
                    _("optimizer.step_update_pool_config"),
                    _("optimizer.step_monitor_usage")
                ],
                estimated_effort="low",
                risk_level="medium"
            )
            recommendations.append(rec)
        
        return recommendations
    
    def _generate_consolidation_recommendations(self, pool_configs: List[Dict[str, Any]], 
                                              usage_analysis_result=None,
                                              reference_manager=None) -> List[OptimizationRecommendation]:
        """生成合并优化建议"""
        recommendations = []
        
        # 查找可合并的池
        consolidation_candidates = self._find_consolidation_candidates(pool_configs, usage_analysis_result, reference_manager)
        
        for candidate_group in consolidation_candidates:
            if len(candidate_group) >= 2:
                rec = OptimizationRecommendation(
                    recommendation_id=self._get_next_id(),
                    type="consolidation",
                    priority="medium",
                    title=_("optimizer.consolidate_pools_title"),
                    description=_("optimizer.consolidate_pools_desc", 
                                count=len(candidate_group),
                                pools=", ".join(candidate_group[:3])),
                    affected_pools=candidate_group,
                    expected_benefits=[
                        _("optimizer.benefit_reduce_complexity"),
                        _("optimizer.benefit_improve_efficiency"),
                        _("optimizer.benefit_easier_management")
                    ],
                    implementation_steps=[
                        _("optimizer.step_analyze_dependencies"),
                        _("optimizer.step_design_merged_pool"),
                        _("optimizer.step_update_references"),
                        _("optimizer.step_migrate_configurations"),
                        _("optimizer.step_cleanup_old_pools")
                    ],
                    estimated_effort="high",
                    risk_level="medium",
                    prerequisites=[
                        _("optimizer.prereq_backup_config"),
                        _("optimizer.prereq_test_environment")
                    ]
                )
                recommendations.append(rec)
        
        return recommendations
    
    def _generate_fragmentation_recommendations(self, pool_configs: List[Dict[str, Any]]) -> List[OptimizationRecommendation]:
        """生成碎片化优化建议"""
        recommendations = []
        
        for pool in pool_configs:
            pool_name = pool.get("name", "unknown")
            fragmentation_level = self._calculate_pool_fragmentation(pool)
            
            if fragmentation_level > self.fragmentation_threshold:
                rec = OptimizationRecommendation(
                    recommendation_id=self._get_next_id(),
                    type="fragmentation",
                    priority="medium",
                    title=_("optimizer.defragment_pool_title"),
                    description=_("optimizer.defragment_pool_desc", 
                                pool_name=pool_name,
                                fragmentation=f"{fragmentation_level:.1%}"),
                    affected_pools=[pool_name],
                    expected_benefits=[
                        _("optimizer.benefit_improve_allocation_efficiency"),
                        _("optimizer.benefit_reduce_management_overhead"),
                        _("optimizer.benefit_better_performance")
                    ],
                    implementation_steps=[
                        _("optimizer.step_analyze_address_segments"),
                        _("optimizer.step_identify_consolidation_opportunities"),
                        _("optimizer.step_redesign_address_ranges"),
                        _("optimizer.step_update_pool_configuration")
                    ],
                    estimated_effort="medium",
                    risk_level="low"
                )
                recommendations.append(rec)
        
        return recommendations
    
    def _generate_naming_recommendations(self, pool_configs: List[Dict[str, Any]]) -> List[OptimizationRecommendation]:
        """生成命名优化建议"""
        recommendations = []
        
        naming_issues = self._analyze_naming_patterns(pool_configs)
        
        if naming_issues["inconsistent_names"]:
            rec = OptimizationRecommendation(
                recommendation_id=self._get_next_id(),
                type="naming",
                priority="low",
                title=_("optimizer.standardize_naming_title"),
                description=_("optimizer.standardize_naming_desc"),
                affected_pools=naming_issues["inconsistent_names"],
                expected_benefits=[
                    _("optimizer.benefit_improve_readability"),
                    _("optimizer.benefit_easier_maintenance"),
                    _("optimizer.benefit_reduce_errors")
                ],
                implementation_steps=[
                    _("optimizer.step_define_naming_convention"),
                    _("optimizer.step_rename_pools"),
                    _("optimizer.step_update_references"),
                    _("optimizer.step_document_standards")
                ],
                estimated_effort="low",
                risk_level="low"
            )
            recommendations.append(rec)
        
        if naming_issues["long_names"]:
            rec = OptimizationRecommendation(
                recommendation_id=self._get_next_id(),
                type="naming",
                priority="low",
                title=_("optimizer.shorten_names_title"),
                description=_("optimizer.shorten_names_desc"),
                affected_pools=naming_issues["long_names"],
                expected_benefits=[
                    _("optimizer.benefit_improve_readability"),
                    _("optimizer.benefit_reduce_config_size")
                ],
                implementation_steps=[
                    _("optimizer.step_identify_abbreviations"),
                    _("optimizer.step_rename_pools"),
                    _("optimizer.step_update_references")
                ],
                estimated_effort="low",
                risk_level="low"
            )
            recommendations.append(rec)
        
        return recommendations
    
    def _generate_performance_recommendations(self, pool_configs: List[Dict[str, Any]], 
                                            usage_analysis_result=None) -> List[OptimizationRecommendation]:
        """生成性能优化建议"""
        recommendations = []
        
        # 建议优化低效率池
        if usage_analysis_result and usage_analysis_result.low_efficiency_pools:
            rec = OptimizationRecommendation(
                recommendation_id=self._get_next_id(),
                type="performance",
                priority="medium",
                title=_("optimizer.optimize_low_efficiency_title"),
                description=_("optimizer.optimize_low_efficiency_desc"),
                affected_pools=usage_analysis_result.low_efficiency_pools,
                expected_benefits=[
                    _("optimizer.benefit_improve_efficiency"),
                    _("optimizer.benefit_better_resource_utilization"),
                    _("optimizer.benefit_reduce_overhead")
                ],
                implementation_steps=[
                    _("optimizer.step_analyze_efficiency_factors"),
                    _("optimizer.step_identify_bottlenecks"),
                    _("optimizer.step_implement_optimizations"),
                    _("optimizer.step_monitor_improvements")
                ],
                estimated_effort="medium",
                risk_level="low"
            )
            recommendations.append(rec)
        
        return recommendations
    
    def create_optimization_plan(self, recommendations: List[OptimizationRecommendation]) -> OptimizationPlan:
        """
        创建优化执行计划
        
        Args:
            recommendations: 优化建议列表
            
        Returns:
            OptimizationPlan: 优化计划
        """
        plan = OptimizationPlan(plan_id=f"plan_{self._get_next_id()}")
        
        # 按优先级和依赖关系排序建议
        sorted_recommendations = self._sort_recommendations_by_dependencies(recommendations)
        plan.recommendations = sorted_recommendations
        plan.execution_order = [rec.recommendation_id for rec in sorted_recommendations]
        
        # 计算总体工作量
        effort_weights = {"low": 1, "medium": 3, "high": 5}
        total_effort = sum(effort_weights.get(rec.estimated_effort, 3) for rec in recommendations)
        
        if total_effort <= 5:
            plan.total_estimated_effort = "low"
        elif total_effort <= 15:
            plan.total_estimated_effort = "medium"
        else:
            plan.total_estimated_effort = "high"
        
        # 预期改进
        plan.expected_improvements = {
            "efficiency_improvement": f"{self.efficiency_improvement_target}%",
            "management_simplification": "medium",
            "resource_optimization": "high"
        }
        
        # 风险评估
        high_risk_count = sum(1 for rec in recommendations if rec.risk_level == "high")
        plan.risk_assessment = {
            "overall_risk": "high" if high_risk_count > 0 else "medium" if len(recommendations) > 5 else "low",
            "mitigation_required": high_risk_count > 0,
            "backup_recommended": True
        }
        
        return plan
    
    def _find_consolidation_candidates(self, pool_configs: List[Dict[str, Any]], 
                                     usage_analysis_result=None,
                                     reference_manager=None) -> List[List[str]]:
        """查找可合并的池候选"""
        candidates = []
        
        # 基于使用率和引用模式查找候选
        low_usage_pools = []
        if usage_analysis_result:
            low_usage_pools = usage_analysis_result.underutilized_pools + usage_analysis_result.orphaned_pools
        
        # 将低使用率的池分组
        if len(low_usage_pools) >= 2:
            # 简单分组：每3个池为一组
            for i in range(0, len(low_usage_pools), 3):
                group = low_usage_pools[i:i+3]
                if len(group) >= 2:
                    candidates.append(group)
        
        return candidates
    
    def _calculate_pool_fragmentation(self, pool: Dict[str, Any]) -> float:
        """计算池的碎片化程度"""
        address_config = pool.get("address")
        if not address_config:
            return 0.0
        
        if isinstance(address_config, list):
            # 多个地址段表示碎片化
            return min(1.0, len(address_config) / 5.0)
        else:
            # 单个地址段，碎片化程度低
            return 0.1
    
    def _analyze_naming_patterns(self, pool_configs: List[Dict[str, Any]]) -> Dict[str, List[str]]:
        """分析命名模式"""
        issues = {
            "inconsistent_names": [],
            "long_names": [],
            "unclear_names": []
        }
        
        for pool in pool_configs:
            pool_name = pool.get("name", "")
            
            # 检查名称长度
            if len(pool_name) > 32:
                issues["long_names"].append(pool_name)
            
            # 检查命名一致性（简单启发式）
            if not any(pattern in pool_name.lower() for pattern in ["pool", "nat", "snat", "dnat"]):
                issues["inconsistent_names"].append(pool_name)
        
        return issues
    
    def _sort_recommendations_by_dependencies(self, recommendations: List[OptimizationRecommendation]) -> List[OptimizationRecommendation]:
        """按依赖关系排序建议"""
        # 简单的优先级排序
        priority_order = {"critical": 4, "high": 3, "medium": 2, "low": 1}
        return sorted(recommendations, key=lambda x: priority_order.get(x.priority, 0), reverse=True)
    
    def _get_priority_weight(self, priority: str) -> int:
        """获取优先级权重"""
        weights = {"critical": 4, "high": 3, "medium": 2, "low": 1}
        return weights.get(priority, 0)
    
    def _get_next_id(self) -> str:
        """获取下一个ID"""
        self.recommendation_counter += 1
        return f"rec_{self.recommendation_counter:04d}"
