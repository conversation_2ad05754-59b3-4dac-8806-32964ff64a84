# 配置解析器模块

## 功能概述

飞塔配置解析器模块(`fortigate_parser.py`)是一个专门用于解析飞塔(FortiGate)防火墙配置文件的工具。该解析器能够提取配置文件中的各种元素，并将其转换为结构化的Python字典数据，便于后续处理和转换。

## 支持的配置项

解析器支持提取以下配置项：

1. **接口配置** (`interfaces`): 物理接口、VLAN接口等网络接口配置
2. **静态路由** (`static_routes`): 网络静态路由配置
3. **安全区域** (`zones`): 安全区域及其成员接口
4. **地址对象** (`address_objects`): IP地址、网络、FQDN等地址对象
5. **地址组** (`address_groups`): 地址对象的分组
6. **服务对象** (`service_objects`): 自定义服务定义
7. **服务组** (`service_groups`): 服务对象的分组
8. **策略规则** (`policies`): 防火墙访问控制策略
9. **NAT规则** (`nat_rules`): 包括VIP和IP池的网络地址转换配置
10. **VPN配置** (`vpn_configs`): 包括IPsec VPN和SSL VPN配置
11. **DHCP服务器配置** (`dhcp_configs`): DHCP服务器设置，包括IP范围和选项
12. **DNS配置** (`dns_config`): DNS服务器设置
13. **认证配置** (`auth_config`): 包括本地用户、用户组和认证设置
14. **日志配置** (`log_config`): 包括日志设置、日志过滤器和日志目标

## 安全校验机制

FortiGate解析器集成了全面的安全校验机制，用于防止恶意输入、格式错误和潜在的注入攻击，提高系统的稳定性和安全性：

### 输入验证

- **文件存在性检查**：验证配置文件是否存在，避免处理不存在的文件
- **文件大小限制**：检查文件大小，防止过大文件（>10MB）导致内存溢出
- **文件扩展名检查**：验证文件扩展名，对可疑扩展名（如.exe）发出警告
- **格式验证**：确保输入是有效的FortiGate配置格式，必须以"config"开头
- **配置段平衡检查**：验证config/end和edit/next数量是否匹配，避免解析错误

### 恶意内容防护

- **敏感命令检测**：检测并警告包含敏感命令（如execute、diagnose、format）的配置
- **命令注入防护**：过滤注释和名称中的危险字符（如;、|、&等），防止命令注入
- **XSS防护**：过滤HTML和脚本标签，防止跨站脚本攻击
- **过长输入限制**：限制各种字段的最大长度，如地址对象名称（100字符）、注释（200字符）等

### 数据完整性保护

- **策略规则校验**：验证策略规则是否包含必要字段（如源接口、目的接口、动作等）
- **IP地址格式验证**：确保IP地址和CIDR格式正确，防止非法输入
- **过大列表截断**：自动截断异常大的列表（如地址组成员>500），防止资源耗尽
- **策略ID验证**：确保策略ID为有效数字，并自动修复非数字ID

### 安全报告机制

解析结果中包含以下安全相关字段：
- `errors`：致命错误列表，如文件不存在、为空等
- `warnings`：警告信息列表，如可疑命令、格式问题等
- `security_info`：安全相关信息，如检测到的敏感命令数量等

### 使用安全校验

解析配置文件时，安全校验机制自动启用：

```python
from engine.parsers.fortigate_parser import FortigateParser

parser = FortigateParser()
result = parser.parse_fortigate_cli("path/to/fortigate.conf")

# 检查是否有错误或警告
if result.get("errors"):
    print("解析错误:", result["errors"])
if result.get("warnings"):
    print("解析警告:", result["warnings"])

# 单独验证配置文件
validation = parser.validate_config_file("path/to/fortigate.conf")
if validation["valid"]:
    print("配置文件验证通过")
else:
    print("配置文件验证失败:", validation["errors"])
```

## 使用方法

### 基本用法

```python
from engine.parsers.fortigate_parser import FortigateParser

# 创建解析器实例
parser = FortigateParser()

# 解析配置文件
result = parser.parse_fortigate_cli("path/to/fortigate.conf")

# 提取特定配置
interfaces = parser.extract_interfaces("path/to/fortigate.conf")
policies = parser.extract_policies("path/to/fortigate.conf")
vpn_configs = parser.extract_vpn_configs("path/to/fortigate.conf")
dhcp_configs = parser.extract_dhcp_configs("path/to/fortigate.conf")
dns_config = parser.extract_dns_configs("path/to/fortigate.conf")
auth_config = parser.extract_auth_configs("path/to/fortigate.conf")
log_config = parser.extract_log_configs("path/to/fortigate.conf")
```

### 使用测试脚本

提供了一个测试脚本(`test_parser.py`)，用于验证解析器功能：

```bash
# 解析整个配置文件并保存结果
python -m engine.parsers.test_parser --config path/to/fortigate.conf --output result.json

# 只解析特定部分
python -m engine.parsers.test_parser --config path/to/fortigate.conf --sections interfaces policies

# 解析多个部分
python -m engine.parsers.test_parser --config path/to/fortigate.conf --sections interfaces nat_rules vpn_configs dhcp_configs dns_config auth_config log_config
```

## 输出数据结构

解析器输出的结构化数据示例：

```json
{
  "interfaces": [
    {
      "raw_name": "port1",
      "ip": "***********/24",
      "status": "up",
      "allowaccess": ["ping", "https", "ssh"],
      "description": "WAN Interface"
    }
    // 更多接口...
  ],
  "policies": [
    {
      "policyid": "1",
      "srcintf": ["port1"],
      "dstintf": ["port2"],
      "srcaddr": ["all"],
      "dstaddr": ["all"],
      "action": "accept",
      "service": ["HTTP", "HTTPS"],
      "nat": "enable"
    }
    // 更多策略...
  ],
  "dhcp_configs": [
    {
      "id": "1",
      "interface": "port2",
      "default_gateway": "***********",
      "netmask": "*************",
      "dns_server1": "*******",
      "ip_ranges": [
        {
          "start_ip": "***********00",
          "end_ip": "*************"
        }
      ]
    }
    // 更多DHCP配置...
  ],
  "dns_config": {
    "primary": "*******",
    "secondary": "*******",
    "domains": ["example.com", "local.lan"]
  },
  "auth_config": {
    "users": [
      {
        "name": "admin",
        "type": "local",
        "status": "enable",
        "password_encrypted": true
      }
      // 更多用户...
    ],
    "user_groups": [
      {
        "name": "admins",
        "members": ["admin"]
      }
      // 更多用户组...
    ]
  },
  "log_config": {
    "settings": {
      "status": "enable",
      "resolve_ip": "enable"
    },
    "filters": [
      {
        "type": "memory filter",
        "severity": "information"
      }
      // 更多过滤器...
    ],
    "targets": [
      {
        "type": "syslogd",
        "status": "enable",
        "server": "***********00",
        "port": "514"
      }
      // 更多日志目标...
    ]
  }
  // 其他配置项...
}
```

## 工具类

解析器使用`parser_utils.py`中的工具类提供通用解析功能：

```python
from engine.parsers.parser_utils import ParserUtils

# 提取引号包围的值
values = ParserUtils.extract_quoted_values('set member "addr1" "addr2"')
# 结果: ['addr1', 'addr2']

# 提取关键字后的值
value = ParserUtils.extract_value_after_keyword('set ip *********** *************', 'set ip')
# 结果: '*********** *************'

# 将掩码转换为前缀长度
prefix = ParserUtils.mask_to_prefix('*************')
# 结果: 24
```

## 扩展和定制

如需支持更多飞塔配置项，可以按照现有模式扩展`parse_fortigate_cli`函数：

1. 添加新的条件以识别配置段：`if line.lower().startswith("config new_section"):`
2. 创建新的处理逻辑来提取特定配置
3. 在结果字典中添加新的字段

也可以为其他厂商创建新的解析器，只需遵循相同的接口设计。 