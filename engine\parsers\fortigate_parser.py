
import time
from functools import wraps

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.stage_times = {}
        self.total_start_time = None
    
    def start_monitoring(self):
        """开始监控"""
        self.total_start_time = time.time()
        print("📊 性能监控开始")
    
    def record_stage(self, stage_name, duration):
        """记录阶段耗时"""
        self.stage_times[stage_name] = duration
        print(f"⏱️ {stage_name}: {duration:.2f}秒")
    
    def get_summary(self):
        """获取性能摘要"""
        if self.total_start_time:
            total_time = time.time() - self.total_start_time
            return {
                "total_time": total_time,
                "stage_times": self.stage_times,
                "stages_count": len(self.stage_times)
            }
        return {}

def monitor_performance(stage_name):
    """性能监控装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                
                # 记录性能数据
                if hasattr(wrapper, '_performance_monitor'):
                    wrapper._performance_monitor.record_stage(stage_name, duration)
                else:
                    print(f"⏱️ {stage_name}: {duration:.2f}秒")
                
                return result
                
            except Exception as e:
                duration = time.time() - start_time
                print(f"❌ {stage_name} 失败 ({duration:.2f}秒): {e}")
                raise e
        
        return wrapper
    return decorator

class OptimizedConfigParser:
    """优化的配置解析器"""
    
    def __init__(self):
        self.performance_monitor = PerformanceMonitor()
        self.memory_optimizer = MemoryOptimizer()
    
    @monitor_performance("配置文件读取")
    def read_config_file(self, file_path):
        """优化的配置文件读取"""
        try:
            # 检查文件大小
            file_size = os.path.getsize(file_path)
            print(f"📄 配置文件大小: {file_size / 1024 / 1024:.1f}MB")
            
            # 如果文件很大，使用流式读取
            if file_size > 1024 * 1024:  # 1MB
                return self.stream_read_large_file(file_path)
            else:
                with open(file_path, 'r', encoding='utf-8') as f:
                    return f.read()
                    
        except Exception as e:
            print(f"❌ 读取配置文件失败: {e}")
            raise
    
    def stream_read_large_file(self, file_path):
        """流式读取大文件"""
        print("🔄 使用流式读取处理大文件")
        
        lines = []
        with open(file_path, 'r', encoding='utf-8') as f:
            for i, line in enumerate(f):
                lines.append(line.rstrip())
                
                # 每读取1000行进行一次内存检查
                if i % 1000 == 0 and i > 0:
                    memory_usage = self.memory_optimizer.get_memory_usage()
                    if memory_usage > 300:  # 300MB
                        print(f"⚠️ 内存使用较高 ({memory_usage:.1f}MB)，进行优化")
                        self.memory_optimizer.optimize_memory(f"reading_line_{i}")
        
        return '\n'.join(lines)
    
    @monitor_performance("配置解析")
    def parse_config_optimized(self, config_content):
        """优化的配置解析"""
        try:
            lines = config_content.split('\n')
            print(f"📊 开始解析 {len(lines)} 行配置")
            
            # 如果行数很多，使用分块处理
            if len(lines) > 5000:
                return self.parse_large_config(lines)
            else:
                return self.parse_normal_config(lines)
                
        except Exception as e:
            print(f"❌ 配置解析失败: {e}")
            raise
    
    def parse_large_config(self, lines):
        """解析大型配置"""
        print("🔄 使用大型配置解析模式")

        # 对于大型配置文件，直接使用传统解析器
        # 将lines重新组合成文件内容，然后写入临时文件进行解析
        import tempfile
        import os

        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.conf', delete=False, encoding='utf-8') as temp_file:
                temp_file.write('\n'.join(lines))
                temp_file_path = temp_file.name

            # 使用传统解析器解析
            result = parse_fortigate_cli(temp_file_path)

            # 清理临时文件
            os.unlink(temp_file_path)

            return result

        except Exception as e:
            print(f"❌ 大型配置解析失败: {e}")
            # 返回空结果而不是错误
            return {
                "interfaces": [],
                "static_routes": [],
                "static_routes_ipv6": [],
                "zones": [],
                "address_objects": [],
                "address_groups": [],
                "service_objects": [],
                "service_groups": [],
                "policies": [],
                "nat_rules": [],
                "time_ranges": [],
                "vpn_configs": {"ipsec": [], "ssl_vpn": {}},
                "dhcp_configs": [],
                "dns_config": {},
                "auth_config": {"users": [], "user_groups": [], "auth_settings": {}},
                "log_config": {"settings": {}, "filters": [], "targets": []},
                "system_settings": {"opmode": "nat", "manageip": None, "hostname": None, "timezone": None},
                "warnings": [f"大型配置解析失败: {str(e)}"]
            }
    
    def parse_normal_config(self, lines):
        """解析普通配置"""
        print("🔄 使用标准配置解析模式")
        
        # 这里实现标准解析逻辑
        return {
            "status": "success",
            "total_lines": len(lines),
            "parsing_mode": "normal"
        }

# PARSING_PERFORMANCE_OPTIMIZED
#!/usr/bin/env python
# -*- coding: utf-8 -*-

# 尝试导入标准包，如果失败则使用相对导入
try:
    from engine.utils.logger import log, user_log
    from engine.utils.i18n import _
except ImportError:
    # 在测试环境中，我们可能没有正确配置导入路径
    # 使用简单的替代函数
    def log(message, level="info", **kwargs):
        prefix = f"[{level.upper()}] "
        msg = message
        for k, v in kwargs.items():
            msg = msg.replace(f"{{{k}}}", str(v))
            
        if "i18n:" in msg:
            msg = msg.replace("i18n:", "")
            
        print(f"{prefix}{msg}")
    
    def user_log(message, level="info", **kwargs):
        log(message, level, **kwargs)
    
    def _(message, **kwargs):
        for k, v in kwargs.items():
            message = message.replace(f"{{{k}}}", str(v))
        return message

import os
import re
import hashlib
import json
from collections import defaultdict

# 定义全局安全翻译函数，确保它始终可用
def safe_translate(message, **kwargs):
    """安全的翻译函数，确保即使原始_函数不可用也能正常工作"""
    try:
        # 检查_是否在全局作用域中定义并且可调用
        if '_' in globals() and callable(globals()['_']):
            # 应用参数映射修复，解决raw_name参数不匹配问题
            try:
                from engine.utils.logger import fix_translation_params
                fixed_kwargs = fix_translation_params(message, kwargs.copy())
                return globals()['_'](message, **fixed_kwargs)
            except ImportError:
                # 如果无法导入参数映射函数，使用原始参数
                return globals()['_'](message, **kwargs)
        else:
            # 如果_不可用，使用简单的字符串替换
            result = message
            for k, v in kwargs.items():
                result = result.replace(f"{{{k}}}", str(v))
            return result
    except:
        # 出现任何错误，使用简单的字符串替换
        result = message
        for k, v in kwargs.items():
            result = result.replace(f"{{{k}}}", str(v))
        return result

# NTOS支持的配置项 - 扩展支持更多访问控制服务
NTOS_SUPPORTED_ACCESS = {
    "ping", "https", "ssh", "http", "telnet", "snmp",
    "fgfm", "capwap", "radius-acct", "probe-response",
    "fabric", "ftm", "speed-test"
}
NTOS_SUPPORTED_ROLES = {"lan", "wan"}  # NTOS YANG模型只真正支持lan和wan
CONVERTIBLE_ROLES = {"dmz", "undefined"}  # 这些角色可以转换为lan
NTOS_SUPPORTED_INTERFACE_TYPES = {"physical", "vlan"}
# 可转换的接口类型 - 这些类型可以通过优雅降级转换为支持的类型
CONVERTIBLE_INTERFACE_TYPES = {
    "tunnel": "physical",    # 隧道接口转换为物理接口处理
    "loopback": "physical",  # 环回接口转换为物理接口处理
    "switch": "physical",    # 交换接口转换为物理接口处理
    "aggregate": "physical", # 聚合接口转换为物理接口处理
    "redundant": "physical"  # 冗余接口转换为物理接口处理
}

class FortigateParser:
    """飞塔配置解析器类"""
    
    def __init__(self):
        """初始化解析器"""
        self.name = "fortigate"
        self.description = _("fortigate.parser_description")
        self.version = "1.0"
    
    def parse(self, file_path):
        """
        解析飞塔配置文件
        
        Args:
            file_path (str): 配置文件路径
            
        Returns:
            dict: 解析后的配置数据，包含interfaces、static_routes和policies等列表
        """
        return parse_fortigate_cli(file_path)
    
    def extract_interfaces(self, file_path):
        """
        从配置中提取接口信息
        
        Args:
            file_path (str): 配置文件路径
            
        Returns:
            list: 接口信息列表
        """
        result = parse_fortigate_cli(file_path)
        return result.get("interfaces", [])
    
    def extract_static_routes(self, file_path):
        """
        从配置中提取静态路由信息
        
        Args:
            file_path (str): 配置文件路径
            
        Returns:
            list: 静态路由信息列表
        """
        result = parse_fortigate_cli(file_path)
        return result.get("static_routes", [])
    
    def extract_policies(self, file_path):
        """
        从配置中提取策略规则信息
        
        Args:
            file_path (str): 配置文件路径
            
        Returns:
            list: 策略规则信息列表
        """
        result = parse_fortigate_cli(file_path)
        return result.get("policies", [])
    
    def extract_nat_rules(self, file_path):
        """
        从配置中提取NAT规则信息
        
        Args:
            file_path (str): 配置文件路径
            
        Returns:
            list: NAT规则信息列表
        """
        result = parse_fortigate_cli(file_path)
        return result.get("nat_rules", [])
    
    def extract_vpn_configs(self, file_path):
        """
        从配置中提取VPN配置信息
        
        Args:
            file_path (str): 配置文件路径
            
        Returns:
            dict: VPN配置信息，包含ipsec和ssl_vpn
        """
        result = parse_fortigate_cli(file_path)
        return result.get("vpn_configs", {"ipsec": [], "ssl_vpn": {}})
    
    def extract_dhcp_configs(self, file_path):
        """
        从配置中提取DHCP服务器配置信息
        
        Args:
            file_path (str): 配置文件路径
            
        Returns:
            list: DHCP服务器配置信息列表
        """
        result = parse_fortigate_cli(file_path)
        return result.get("dhcp_configs", [])
    
    def extract_dns_configs(self, file_path):
        """
        从配置中提取DNS配置信息
        
        Args:
            file_path (str): 配置文件路径
            
        Returns:
            dict: DNS配置信息
        """
        result = parse_fortigate_cli(file_path)
        return result.get("dns_config", {})
    
    def extract_auth_configs(self, file_path):
        """
        从配置中提取认证配置信息
        
        Args:
            file_path (str): 配置文件路径
            
        Returns:
            dict: 认证配置信息，包含用户、用户组和认证设置
        """
        result = parse_fortigate_cli(file_path)
        return result.get("auth_config", {})
    
    def extract_log_configs(self, file_path):
        """
        从配置中提取日志配置信息
        
        Args:
            file_path (str): 配置文件路径
            
        Returns:
            dict: 日志配置信息，包含日志设置、日志过滤器和日志目标
        """
        result = parse_fortigate_cli(file_path)
        return result.get("log_config", {})
    
    def validate_config_file(self, file_path):
        """
        验证配置文件的格式和内容
        
        Args:
            file_path (str): 配置文件路径
            
        Returns:
            tuple: (is_valid, result_dict)，包含验证结果和详细信息
        """
        result = {
            "valid": False,
            "file_exists": False,
            "file_size": 0,
            "file_hash": "",
            "format_valid": False,
            "sections": [],
            "errors": [],
            "warnings": []
        }
        
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                result["errors"].append("文件不存在")
                return (False, result)
            
            result["file_exists"] = True
            
            # 检查文件大小
            file_size = os.path.getsize(file_path)
            result["file_size"] = file_size
            
            if file_size == 0:
                result["errors"].append("文件为空")
                return (False, result)
            
            if file_size > 10 * 1024 * 1024:  # 限制文件大小为10MB
                result["errors"].append("文件过大（>10MB），可能不是正常的配置文件")
                return (False, result)
            
            # 计算文件哈希值
            with open(file_path, 'rb') as f:
                file_content = f.read()
                result["file_hash"] = hashlib.sha256(file_content).hexdigest()
            
            # 读取文件内容进行验证
            with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
                content = f.read()
            
            # 检查文件格式
            if not content.strip().startswith("config "):
                result["errors"].append("文件格式不符合飞塔配置格式")
                return (False, result)
            
            result["format_valid"] = True
            
            # 检查配置部分
            sections = []
            section_pattern = r'config\s+([\w\-]+\s+[\w\-]+)'
            for match in re.finditer(section_pattern, content):
                section = match.group(1)
                if section not in sections:
                    sections.append(section)
            
            result["sections"] = sections
            
            # 检查是否包含关键配置部分
            required_sections = ["system interface", "router static", "firewall policy"]
            missing_sections = [section for section in required_sections if not any(section in s for s in sections)]
            
            if missing_sections:
                result["warnings"].append(f"配置文件缺少关键部分: {', '.join(missing_sections)}")
            
            # 检查敏感命令
            sensitive_commands = ["execute", "diagnose", "rm", "delete", "format"]
            for cmd in sensitive_commands:
                if re.search(r'\b' + re.escape(cmd) + r'\b', content, re.IGNORECASE):
                    result["warnings"].append(f"配置包含敏感命令: {cmd}")
            
            # 检查配置文件的平衡性（config/end 和 edit/next 的匹配）
            config_count = content.lower().count("\nconfig ")
            end_count = content.lower().count("\nend")
            edit_count = content.lower().count("\nedit ")
            next_count = content.lower().count("\nnext")
            
            if config_count != end_count:
                result["warnings"].append(f"config/end 数量不匹配: {config_count}/{end_count}")
            
            if edit_count != next_count:
                result["warnings"].append(f"edit/next 数量不匹配: {edit_count}/{next_count}")
            
            # 最终判断
            is_valid = result["file_exists"] and result["format_valid"] and len(result["errors"]) == 0
            result["valid"] = is_valid
            
            return (is_valid, result)
        
        except Exception as e:
            result["errors"].append(f"验证过程出错: {str(e)}")
            return (False, result)
    
    def verify(self, file_path):
        """
        验证配置文件格式是否正确
        
        Args:
            file_path (str): 配置文件路径
            
        Returns:
            tuple: (is_valid, error_message)
        """
        is_valid, validation_result = self.validate_config_file(file_path)
        
        # 如果文件无效，返回错误信息
        if not is_valid:
            error_messages = []
            if validation_result["errors"]:
                error_messages.extend(validation_result["errors"])
            if validation_result["warnings"]:
                error_messages.extend(validation_result["warnings"])
            
            error_msg = "、".join(error_messages) if error_messages else _("fortigate.invalid_config")
            return False, error_msg
        
        try:
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查文件是否包含基本的飞塔配置标记
            if "config system" not in content:
                return False, _("fortigate.invalid_config")
            
            # 尝试解析文件
            result = parse_fortigate_cli(file_path)
            if "error" in result:
                return False, result["error"]
            
            return True, _("fortigate.valid_config")
        except Exception as e:
            # 记录错误信息
            print(f"解析配置文件时出错: {str(e)}")
            try:
                error_msg = _("fortigate.parsing_error", error=str(e))
                log(error_msg, "error")
                user_log(safe_translate("fortigate.parsing_error_user"), "error")
            except:
                # 如果翻译函数不可用，使用默认消息
                error_msg = f"解析配置文件失败: {str(e)}"
                print(f"[ERROR] {error_msg}")
                print("[ERROR] 解析配置文件失败")
            return False, error_msg
    
    def sanitize_string(self, value, max_length=255):
        """
        净化字符串，防止XSS和注入攻击
        
        Args:
            value (str): 需要净化的字符串
            max_length (int): 最大允许长度
            
        Returns:
            str: 净化后的字符串
        """
        if not value:
            return ""
        
        # 转换为字符串
        if not isinstance(value, str):
            value = str(value)
        
        # 截断长度
        if len(value) > max_length:
            value = value[:max_length]
        
        # 移除危险字符
        value = re.sub(r'[<>\'";]', '', value)
        
        return value
    
    def validate_ip_address(self, ip):
        """
        验证IP地址格式
        
        Args:
            ip (str): IP地址
            
        Returns:
            bool: 是否是有效的IP地址
        """
        if not ip:
            return False
        
        # IPv4地址模式
        ipv4_pattern = r'^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$'
        match = re.match(ipv4_pattern, ip)
        
        if match:
            # 检查每个部分是否在0-255范围内
            for part in match.groups():
                if int(part) < 0 or int(part) > 255:
                    return False
            return True
        
        return False
    
    def validate_cidr(self, cidr):
        """
        验证CIDR格式
        
        Args:
            cidr (str): CIDR格式的IP地址
            
        Returns:
            bool: 是否是有效的CIDR
        """
        if not cidr or '/' not in cidr:
            return False
        
        try:
            ip, prefix = cidr.split('/')
            
            # 验证IP部分
            if not self.validate_ip_address(ip):
                return False
            
            # 验证前缀部分
            prefix_int = int(prefix)
            if prefix_int < 0 or prefix_int > 32:
                return False
                
            return True
        except (ValueError, TypeError):
            return False

    def extract_service_objects(self, file_path):
        """
        从配置中提取服务对象信息
        
        Args:
            file_path (str): 配置文件路径
            
        Returns:
            list: 服务对象信息列表
        """
        result = parse_fortigate_cli(file_path)
        return result.get("service_objects", [])

def parse_fortigate_cli(file_path):
    """
    解析飞塔配置文件
    
    Args:
        file_path (str): 配置文件路径
        
    Returns:
        dict: 解析后的配置数据，包含interfaces和static_routes列表
    """
    
    def safe_log(message, level="info", **kwargs):
        """安全的日志函数"""
        try:
            log(message, level, **kwargs)
        except:
            prefix = f"[{level.upper()}] "
            msg = message
            for k, v in kwargs.items():
                msg = msg.replace(f"{{{k}}}", str(v))
            print(f"{prefix}{msg}")
    
    def safe_user_log(message, level="info", **kwargs):
        """安全的用户日志函数"""
        try:
            user_log(message, level, **kwargs)
        except:
            safe_log(message, level, **kwargs)
    
    # 导入必要的标准库
    import os
    import re
    import hashlib
    import json
    from collections import defaultdict
    
    try:
        # 定义本地IP验证函数
        def validate_ip_address(ip):
            """验证IP地址格式"""
            if not ip:
                return False
            # IPv4地址模式
            ipv4_pattern = r'^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$'
            match = re.match(ipv4_pattern, ip)
            if match:
                # 检查每个部分是否在0-255范围内
                for part in match.groups():
                    if int(part) < 0 or int(part) > 255:
                        return False
                return True
            return False
        
        # 定义掩码转前缀函数
        def mask_to_prefix(mask: str) -> int:
            try:
                parts = map(int, mask.split('.'))
                binary = ''.join(f'{part:08b}' for part in parts)
                return binary.count('1')
            except Exception:
                return 24  # fallback
        
        # 开始解析过程
        safe_log(safe_translate("fortigate.parsing_cli_file", file=file_path))
        safe_user_log(safe_translate("fortigate.start_parsing_config"))
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            error_msg = safe_translate("error.file_not_exists", file=file_path)
            safe_log(error_msg, "error")
            safe_user_log(error_msg, "error")
            return {"error": error_msg, "interfaces": [], "static_routes": [], "zones": [], "address_objects": [], "address_groups": [], "service_objects": [], "service_groups": [], "policies": [], "nat_rules": [], "vpn_configs": {"ipsec": [], "ssl_vpn": {}}}
        
        # 安全检查：验证文件大小
        file_size = os.path.getsize(file_path)
        if file_size > 10 * 1024 * 1024:  # 限制为10MB
            error_msg = safe_translate("error.file_too_large", file=file_path, size=file_size)
            safe_log(error_msg, "error")
            safe_user_log(error_msg, "error")
            return {"error": error_msg, "interfaces": [], "static_routes": [], "zones": [], "address_objects": [], "address_groups": [], "service_objects": [], "service_groups": [], "policies": [], "nat_rules": [], "vpn_configs": {"ipsec": [], "ssl_vpn": {}}}
        
        # 安全检查：验证文件扩展名
        _, ext = os.path.splitext(file_path)
        allowed_exts = ['.conf', '.txt', '.cfg', '']
        if ext.lower() not in allowed_exts:
            safe_log(safe_translate("fortigate.warning.suspicious_file_extension", ext=ext), "warning")
        
        # 初始化结果字典
        result = {
            "interfaces": [],
            "static_routes": [],
            "static_routes_ipv6": [],  # 添加IPv6静态路由列表
            "zones": [],
            "address_objects": [],
            "address_groups": [],
            "service_objects": [],
            "service_groups": [],
            "policies": [],
            "nat_rules": [],
            "ippools": {},  # 添加IP池字典
            "time_ranges": [],  # 添加时间对象列表
            "vpn_configs": {
                "ipsec": [],
                "ssl_vpn": {}
            },
            "dhcp_configs": [],
            "dns_config": {},
            "auth_config": {
                "users": [],
                "user_groups": [],
                "auth_settings": {}
            },
            "log_config": {
                "settings": {},
                "filters": [],
                "targets": []
            },
            "system_settings": {  # 添加系统设置配置
                "opmode": "nat",  # 默认为NAT模式
                "manageip": None,
                "hostname": None,
                "timezone": None
            },
            "warnings": []  # 添加警告字段以记录解析过程中的问题
        }
        
        # 读取文件内容 - 使用备份代码的方式直接按行读取
        with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
            content = f.read()
        
        # 安全检查：检查敏感命令
        sensitive_commands = ["execute", "diagnose", "rm", "delete", "format"]
        for cmd in sensitive_commands:
            if re.search(r'\b' + re.escape(cmd) + r'\b', content, re.IGNORECASE):
                warning_msg = f"检测到敏感命令: {cmd}"
                safe_log(safe_translate("fortigate.warning.sensitive_command_detected", command=cmd), "warning")
                result["warnings"].append(warning_msg)
        
        # 安全检查：检查配置部分的平衡性
        config_count = content.lower().count("\nconfig ")
        end_count = content.lower().count("\nend")
        edit_count = content.lower().count("\nedit ")
        next_count = content.lower().count("\nnext")
        
        if config_count != end_count:
            warning_msg = f"config/end 数量不匹配: {config_count}/{end_count}"
            safe_log(safe_translate("fortigate.warning.unbalanced_config", config=config_count, end=end_count), "warning")
            result["warnings"].append(warning_msg)
        
        if edit_count != next_count:
            warning_msg = f"edit/next 数量不匹配: {edit_count}/{next_count}"
            safe_log(safe_translate("fortigate.warning.unbalanced_edit", edit=edit_count, next=next_count), "warning")
            result["warnings"].append(warning_msg)
        
        # 首先判断是否为实际文件内容，而不是文件路径
        if content.strip().startswith("config "):
            safe_log(safe_translate("fortigate.debug.content_read_success", file=file_path), "debug")
            lines = [line.strip() for line in content.splitlines() if line.strip()]
        else:
            # 备用方案：尝试直接按行读取
            safe_log(safe_translate("fortigate.debug.trying_line_by_line", file=file_path), "debug")
            with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
                lines = [line.strip() for line in f if line.strip()]
        
        # 安全检查：限制行数 - 提高限制以支持大型配置
        if len(lines) > 200000:  # 限制为20万行
            error_msg = safe_translate("error.file_too_many_lines", file=file_path, lines=len(lines))
            safe_log(error_msg, "error")
            safe_user_log(error_msg, "error")
            return {"error": error_msg, "interfaces": [], "static_routes": [], "zones": [], "address_objects": [], "address_groups": [], "service_objects": [], "service_groups": [], "policies": [], "nat_rules": [], "vpn_configs": {"ipsec": [], "ssl_vpn": {}}}

        # 对于大型配置文件（超过10万行），记录警告并使用优化解析
        if len(lines) > 100000:
            safe_log(safe_translate("fortigate.warning.large_config_file", lines=len(lines)), "warning")
            safe_user_log(safe_translate("fortigate.warning.large_config_processing"), "warning")
            
        safe_log(safe_translate("fortigate.read_file_success", lines=len(lines)))
        safe_user_log(safe_translate("fortigate.read_file_success_user", lines=len(lines)))
        
        # 添加调试日志 - 打印前几行内容
        if len(lines) > 0:
            safe_log(safe_translate("fortigate.debug.first_line", content=lines[0]), "debug")
        if len(lines) > 1:
            safe_log(safe_translate("fortigate.debug.second_line", content=lines[1]), "debug")
        
        if len(lines) == 0:
            error_msg = safe_translate("warning.file_empty")
            safe_log(error_msg, "warning")
            safe_user_log(error_msg, "warning")
            return result
            
        # 确保内容是配置文件，检查FortiGate配置文件的标准格式
        # FortiGate配置文件可能以#config-version开头，或者直接以config命令开头
        is_valid_fortigate_config = False
        
        # 检查前几行以确定是否为有效的FortiGate配置文件
        for i, line in enumerate(lines[:10]):  # 检查前10行
            if line.startswith("#config-version="):
                # 标准FortiGate配置文件格式
                is_valid_fortigate_config = True
                safe_log(safe_translate("fortigate.detected_config_version", version_line=line), "debug")

                # 解析配置文件头部的opmode信息 (作为辅助参考，不覆盖显式设置)
                # 格式: #config-version=FG200F-7.2.8-FW-build1639-240313:opmode=0:vdom=0:user=hakantoptas
                # opmode=0 表示透明模式，opmode=1 表示NAT/路由模式
                if ":opmode=" in line:
                    try:
                        opmode_part = line.split(":opmode=")[1].split(":")[0]
                        # 将头部opmode存储为单独字段，不覆盖显式配置
                        result["system_settings"]["header_opmode"] = opmode_part
                        if opmode_part == "0":
                            safe_log(safe_translate("fortigate.detected_transparent_mode_from_header"), "info")
                        elif opmode_part == "1":
                            safe_log(safe_translate("fortigate.detected_nat_mode_from_header"), "info")
                        elif opmode_part == "transparent":
                            safe_log(safe_translate("fortigate.detected_transparent_mode_from_header"), "info")
                        elif opmode_part == "nat":
                            safe_log(safe_translate("fortigate.detected_nat_mode_from_header"), "info")
                        else:
                            safe_log(safe_translate("fortigate.unknown_opmode_value", opmode=opmode_part), "warning")
                    except Exception as e:
                        safe_log(safe_translate("fortigate.error_parsing_opmode_header", error=str(e)), "warning")

                break
            elif line.startswith("config "):
                # 直接以config命令开头的配置文件
                is_valid_fortigate_config = True
                safe_log(safe_translate("fortigate.detected_config_command", command_line=line), "debug")
                break
            elif line.strip() == "" or line.startswith("#"):
                # 跳过空行和注释行
                continue
            else:
                # 如果遇到既不是注释也不是config命令的行，在前10行内认为可能不是配置文件
                break
        
        if not is_valid_fortigate_config:
            error_msg = safe_translate("error.invalid_format")
            safe_log(safe_translate("fortigate.error.invalid_format", first_line=lines[0]), "error")
            safe_log(error_msg, "error")
            safe_user_log(error_msg, "error")
            return {"error": error_msg, "interfaces": [], "static_routes": [], "zones": [], "address_objects": [], "address_groups": [], "service_objects": [], "service_groups": [], "policies": [], "nat_rules": [], "vpn_configs": {"ipsec": [], "ssl_vpn": {}}}
        
        # 安全检查：统计各个部分的数量
        section_counters = defaultdict(int)
        
        i = 0
        current = None
        current_section = None
        
        # 安全计数器，防止无限循环
        safety_counter = 0
        max_iterations = len(lines) * 2  # 允许的最大迭代次数

        # 添加嵌套配置处理的状态跟踪
        config_stack = []  # 跟踪嵌套的配置块
        nested_system_settings = False  # 标记是否在嵌套的system settings中

        while i < len(lines) and safety_counter < max_iterations:
            safety_counter += 1
            line = lines[i]  # 不再重复strip，因为读取时已经strip过

            # 安全检查：长度限制
            if len(line) > 1000:
                warning_msg = f"行 {i} 太长 ({len(line)}字符)，已截断"
                safe_log(safe_translate("fortigate.warning.line_too_long", line_num=i, length=len(line)), "warning")
                result["warnings"].append(warning_msg)
                line = line[:1000]  # 截断过长的行

            # 安全检查：防止命令注入
            if re.search(r'[;&|`]', line):
                warning_msg = f"行 {i} 包含可疑字符: {line}"
                safe_log(safe_translate("fortigate.warning.potential_command_injection", line_num=i, content=line), "warning")
                result["warnings"].append(warning_msg)
                i += 1
                continue

            # 添加调试日志
            safe_log(safe_translate("fortigate.debug.processing_line", line_num=i, content=line, section=current_section), "debug")

            # 处理嵌套配置结构
            if line.lower().startswith("config "):
                section_name = line[7:].strip()  # 提取部分名称
                config_stack.append(section_name)
                section_counters[section_name] += 1

                # 检查是否是嵌套的system settings
                if (len(config_stack) >= 2 and
                    config_stack[-2] == "global" and
                    section_name == "system global"):
                    current_section = "system_settings"
                    nested_system_settings = True
                    safe_log(safe_translate("fortigate.start_parsing_system_global"))
                    i += 1
                    continue
                elif (len(config_stack) >= 2 and
                      "vdom" in config_stack and
                      section_name == "system settings"):
                    current_section = "system_settings"
                    nested_system_settings = True
                    safe_log(safe_translate("fortigate.start_parsing_system_settings"))
                    i += 1
                    continue

                # 安全检查：检查部分重复次数
                if section_counters[section_name] > 100:
                    warning_msg = f"部分 '{section_name}' 重复出现超过100次，可能有问题"
                    safe_log(safe_translate("fortigate.warning.section_repeated", section=section_name, count=section_counters[section_name]), "warning")
                    result["warnings"].append(warning_msg)

            # 注释：通用的 edit 和 next 处理逻辑已移到所有特定配置部分处理逻辑之后

            # 处理end语句 - 更新配置栈
            elif line.lower() == "end":
                if config_stack:
                    ended_section = config_stack.pop()
                    # 如果结束的是嵌套的system settings，重置状态
                    if nested_system_settings and ended_section in ["system global", "system settings"]:
                        nested_system_settings = False
                        current_section = None
                        safe_log(safe_translate("fortigate.system_settings_section_end"))
                        i += 1
                        continue
            
            # 以下是原有的检测配置部分代码...
            if line.lower().startswith("config system interface"):
                current_section = "interface"
                safe_log(safe_translate("fortigate.start_parsing_interface"))
                safe_log(safe_translate("fortigate.debug.interface_section_start"), "debug")
                i += 1
                continue
            elif line.lower().startswith("config router static"):
                current_section = "static_route"
                safe_log(safe_translate("fortigate.start_parsing_route"))
                i += 1
                continue
            elif line.lower().startswith("config router static6"):
                current_section = "static_route6"
                safe_log(safe_translate("fortigate.start_parsing_ipv6_route"))
                i += 1
                continue
            elif line.lower().startswith("config system zone"):
                current_section = "zone"
                safe_log(safe_translate("fortigate.start_parsing_zone"))
                i += 1
                continue
            elif line.lower().startswith("config firewall policy"):
                current_section = "policy"
                safe_log(safe_translate("fortigate.start_parsing_policy"))
                i += 1
                continue
            elif line.lower().startswith("config firewall vip"):
                current_section = "vip"
                safe_log(safe_translate("fortigate.start_parsing_vip"))
                i += 1
                continue
            elif line.lower().startswith("config firewall ippool"):
                current_section = "ippool"
                safe_log(safe_translate("fortigate.start_parsing_ippool"))
                i += 1
                continue
            elif line.lower().startswith("config vpn ipsec phase1-interface"):
                current_section = "ipsec_phase1"
                safe_log(safe_translate("fortigate.start_parsing_ipsec_phase1"))
                i += 1
                continue
            elif line.lower().startswith("config vpn ipsec phase2-interface"):
                current_section = "ipsec_phase2"
                safe_log(safe_translate("fortigate.start_parsing_ipsec_phase2"))
                i += 1
                continue
            elif line.lower().startswith("config vpn ssl settings"):
                current_section = "ssl_vpn_settings"
                safe_log(safe_translate("fortigate.start_parsing_ssl_vpn_settings"))
                i += 1
                continue
            elif line.lower().startswith("config vpn ssl web portal"):
                current_section = "ssl_vpn_portal"
                safe_log(safe_translate("fortigate.start_parsing_ssl_vpn_portal"))
                i += 1
                continue
            elif line.lower().startswith("config system settings"):
                current_section = "system_settings"
                safe_log(safe_translate("fortigate.start_parsing_system_settings"))
                i += 1
                continue
            elif line.lower().startswith("config system global"):
                current_section = "system_settings"  # 使用相同的处理逻辑
                safe_log(safe_translate("fortigate.start_parsing_system_global"))
                i += 1
                continue
            elif line.lower().startswith("config system dhcp server"):
                current_section = "dhcp_server"
                safe_log(safe_translate("fortigate.start_parsing_dhcp_server"))
                i += 1
                continue
            elif line.lower().startswith("config system dns"):
                current_section = "dns"
                safe_log(safe_translate("fortigate.start_parsing_dns"))
                i += 1
                continue
            elif line.lower().startswith("config user local"):
                current_section = "user_local"
                safe_log(safe_translate("fortigate.start_parsing_local_users"))
                i += 1
                continue
            elif line.lower().startswith("config user group"):
                current_section = "user_group"
                safe_log(safe_translate("fortigate.start_parsing_user_groups"))
                i += 1
                continue
            elif line.lower().startswith("config user setting"):
                current_section = "user_setting"
                safe_log(safe_translate("fortigate.start_parsing_user_settings"))
                i += 1
                continue
            elif line.lower().startswith("config log setting"):
                current_section = "log_setting"
                safe_log(safe_translate("fortigate.start_parsing_log_settings"))
                i += 1
                continue
            elif line.lower().startswith("config log syslogd") or line.lower().startswith("config log syslogd2") or \
                 line.lower().startswith("config log syslogd3") or line.lower().startswith("config log syslogd4"):
                current_section = "log_syslogd"
                safe_log(safe_translate("fortigate.start_parsing_log_syslogd"))
                current_syslogd = line.lower().replace("config log ", "").strip()
                i += 1
                continue
            elif line.lower().startswith("config log fortianalyzer") or line.lower().startswith("config log fortianalyzer2") or \
                 line.lower().startswith("config log fortianalyzer3"):
                current_section = "log_fortianalyzer"
                safe_log(safe_translate("fortigate.start_parsing_log_fortianalyzer"))
                current_analyzer = line.lower().replace("config log ", "").strip()
                i += 1
                continue
            elif line.lower().startswith("config log memory filter") or line.lower().startswith("config log disk filter"):
                current_section = "log_filter"
                safe_log(safe_translate("fortigate.start_parsing_log_filter"))
                current_filter = line.lower().replace("config log ", "").strip()
                i += 1
                continue
            elif line.lower().startswith("config firewall schedule group"):
                current_section = "schedule"
                current_schedule_type = "group"  # 记录当前schedule类型
                safe_log(safe_translate("fortigate.start_parsing_schedule_group"))
                i += 1
                continue
            elif line.lower().startswith("config firewall schedule onetime"):
                current_section = "schedule"
                current_schedule_type = "onetime"  # 记录当前schedule类型
                safe_log(safe_translate("fortigate.start_parsing_schedule_onetime"))
                i += 1
                continue
            elif line.lower().startswith("config firewall schedule recurring"):
                current_section = "schedule"
                current_schedule_type = "recurring"  # 记录当前schedule类型
                safe_log(safe_translate("fortigate.start_parsing_schedule_recurring"))
                i += 1
                continue
            elif line.lower().startswith("config firewall schedule"):
                current_section = "schedule"
                current_schedule_type = "recurring"  # 默认为recurring类型
                safe_log(safe_translate("fortigate.start_parsing_schedule"))
                i += 1
                continue

            # 根据当前部分进行不同处理
            if current_section == "interface":
                if line.lower().startswith("edit"):
                    # 修复：使用正则表达式正确提取引号内的完整接口名称，支持包含空格的名称
                    match = re.search(r'edit\s+"([^"]*)"', line)
                    if match:
                        interface_name = match.group(1)
                    else:
                        # 回退到原有逻辑（处理没有引号的情况）
                        interface_name = line.split()[1].strip('"')

                    safe_log(safe_translate("fortigate.parsing_interface", name=interface_name))
                    safe_log(safe_translate("fortigate.debug.parsing_interface", name=interface_name), "debug")
                    
                    # 安全检查：验证接口名称
                    if len(interface_name) > 50:
                        warning_msg = f"接口名称 '{interface_name[:10]}...' 过长，已截断"
                        safe_log(safe_translate("fortigate.warning.interface_name_too_long", name=interface_name[:10]+"..."), "warning")
                        result["warnings"].append(warning_msg)
                        interface_name = interface_name[:50]
                    
                    # 验证接口名称是否包含危险字符
                    if re.search(r'[;<>&|]', interface_name):
                        warning_msg = f"接口名称 '{interface_name}' 包含可疑字符，已被过滤"
                        safe_log(safe_translate("fortigate.warning.interface_name_suspicious", name=interface_name), "warning")
                        result["warnings"].append(warning_msg)
                        interface_name = re.sub(r'[;<>&|]', '', interface_name)
                    
                    current = {"raw_name": interface_name}
                    i += 1
                    continue
                elif line.lower().startswith("next"):
                    if current:
                        # 添加调试日志 - 使用JSON格式化接口属性，避免字符串中的花括号被误解为占位符
                        import json
                        attrs_json = json.dumps(current, ensure_ascii=False)
                        safe_log(safe_translate("fortigate.debug.adding_interface", name=current.get('raw_name', 'unknown'), attrs=attrs_json), "debug")
                        
                        # 检查接口是否应该跳过转换
                        if current.get("skip_conversion", False):
                            skip_reason = current.get("skip_reason", safe_translate("fortigate.reason.unknown"))
                            warning_msg = safe_translate("fortigate.warning.skip_interface_conversion", name=current.get('raw_name', 'unknown'), reason=skip_reason)
                            safe_log(warning_msg, "warning")
                            result["warnings"].append(warning_msg)
                            # 将跳过的接口添加到单独的列表中用于统计
                            if "skipped_interfaces" not in result:
                                result["skipped_interfaces"] = []
                            result["skipped_interfaces"].append({
                                "name": current.get('raw_name', 'unknown'),
                                "reason": skip_reason,
                                "role": current.get('role', 'unknown')
                            })
                            safe_log(safe_translate("fortigate.interface_skipped", name=current.get('raw_name', 'unknown')))
                            current = None
                            i += 1
                            continue
                        
                        # 安全检查：验证接口IP地址
                        if "ip" in current:
                            ip_addr = current["ip"]
                            # 验证CIDR格式
                            if '/' in ip_addr:
                                ip, prefix = ip_addr.split('/')
                                if not validate_ip_address(ip) or not prefix.isdigit() or int(prefix) > 32:
                                    warning_msg = f"接口 '{current.get('raw_name')}' 的IP地址 '{ip_addr}' 格式不正确"
                                    safe_log(safe_translate("fortigate.warning.invalid_ip_format", name=current.get('raw_name'), ip=ip_addr), "warning")
                                    result["warnings"].append(warning_msg)
                        
                        # 安全检查：验证接口描述长度
                        if "description" in current and len(current["description"]) > 100:
                            current["description"] = current["description"][:100]
                            warning_msg = f"接口 '{current.get('raw_name')}' 的描述过长，已截断"
                            safe_log(safe_translate("fortigate.warning.description_too_long", name=current.get('raw_name')), "warning")
                            result["warnings"].append(warning_msg)
                        
                        # 设置接口名称字段（用于后续处理）
                        current["name"] = current.get("raw_name", "unknown")
                        result["interfaces"].append(current)
                        safe_log(safe_translate("fortigate.interface_parsing_complete", name=current.get('raw_name', 'unknown')))
                        current = None
                    i += 1
                    continue
                elif line.lower().startswith("config ipv6") and current:
                    safe_log(safe_translate("fortigate.parsing_ipv6_block"))
                    ipv6_data, i = parse_ipv6_block(lines, i + 1)
                    if ipv6_data:
                        current["ipv6"] = ipv6_data
                        if "allowaccess" in ipv6_data:
                            current.setdefault("allowaccess", [])
                            for p in ipv6_data["allowaccess"]:
                                if p in NTOS_SUPPORTED_ACCESS and p not in current["allowaccess"]:
                                    current["allowaccess"].append(p)
                    continue
                elif line.lower().startswith("config secondaryip") and current:
                    safe_log(safe_translate("fortigate.parsing_secondaryip_block"))
                    sec_ips, i = parse_secondaryip_block(lines, i + 1)
                    if sec_ips:
                        current["secondary_ips"] = sec_ips
                    continue
                elif line.lower() == "end" and current:
                    # 正在处理子块的 end
                    i += 1
                    continue
                elif line.lower() == "end":
                    # interface 块结束，但还需继续读取下一个 edit
                    safe_log(safe_translate("fortigate.interface_section_end"))
                    safe_log(safe_translate("fortigate.debug.interface_section_end"), "debug")
                    current_section = None
                    i += 1
                    continue
                elif current:
                    # 处理各种接口属性
                    try:
                        if line.lower().startswith("set ip "):
                            parts = line.replace("set ip", "").strip().split()
                            if len(parts) == 1 and '/' in parts[0]:  # CIDR格式
                                ip, prefix = parts[0].split('/')
                                current["ip"] = f"{ip}/{prefix}"
                                safe_log(safe_translate("fortigate.set_interface_ip_cidr", ip=current['ip']))
                            elif len(parts) >= 2:  # 点分掩码格式
                                ip = parts[0]
                                mask = parts[1]
                                prefix = mask_to_prefix(mask)
                                current["ip"] = f"{ip}/{prefix}"
                                safe_log(safe_translate("fortigate.set_interface_ip_mask", ip=current['ip']))
                            else:
                                safe_log(safe_translate("fortigate.unsupported_ip_format", line=line), "warning")
                        elif line.lower().startswith("set mode"):
                            current["mode"] = line.split("set mode", 1)[1].strip()
                            safe_log(safe_translate("fortigate.set_interface_mode", mode=current['mode']))
                        elif line.lower().startswith("set description"):
                            current["description"] = line.split("set description", 1)[1].strip('" ')
                            safe_log(safe_translate("fortigate.set_interface_description", desc=current['description']))
                        elif line.lower().startswith("set username"):
                            current["pppoe_username"] = line.split("set username", 1)[1].strip('" ')
                            safe_log(safe_translate("fortigate.set_pppoe_username", username=current['pppoe_username']))
                        elif line.lower().startswith("set password"):
                            # 直接使用固定密码，不解析原密码内容
                            current["pppoe_password"] = "123456"  # 使用固定密码
                            safe_log(safe_translate("fortigate.set_pppoe_password_fixed"))
                            safe_user_log(safe_translate("fortigate.pppoe_password_manual_change_required"), "warning")
                        elif line.lower().startswith("set status"):
                            status_value = line.split("set status", 1)[1].strip().lower()
                            current["status"] = status_value
                            # 转换为NTOS布尔值格式
                            current["enabled"] = status_value == "up"
                            safe_log(safe_translate("fortigate.set_interface_status", status=current['status']))
                            safe_log(safe_translate("fortigate.set_interface_enabled", enabled=current['enabled']))
                        elif line.lower().startswith("set mtu"):
                            try:
                                mtu_value = int(line.split("set mtu", 1)[1].strip())
                                current["mtu"] = mtu_value
                                safe_log(safe_translate("fortigate.set_interface_mtu", mtu=current['mtu']))
                            except (ValueError, IndexError):
                                safe_log(safe_translate("fortigate.invalid_mtu_format", line=line), "warning")
                        elif line.lower().startswith("set security-mode"):
                            current["security_mode"] = line.split("set security-mode", 1)[1].strip()
                            safe_log(safe_translate("fortigate.set_security_mode", mode=current['security_mode']))
                        elif line.lower().startswith("set snmp-index"):
                            current["snmp_index"] = int(line.split("set snmp-index", 1)[1].strip())
                            safe_log(safe_translate("fortigate.set_snmp_index", index=current['snmp_index']))
                        elif line.lower().startswith("set estimated-upstream-bandwidth"):
                            current["estimated-upstream-bandwidth"] = int(line.split("set estimated-upstream-bandwidth", 1)[1].strip())
                            safe_log(safe_translate("fortigate.set_upstream_bandwidth", bw=current['estimated-upstream-bandwidth']))
                        elif line.lower().startswith("set estimated-downstream-bandwidth"):
                            current["estimated-downstream-bandwidth"] = int(line.split("set estimated-downstream-bandwidth", 1)[1].strip())
                            safe_log(safe_translate("fortigate.set_downstream_bandwidth", bw=current['estimated-downstream-bandwidth']))
                        elif line.lower().startswith("set interface"):                            # 解析子接口所属的物理接口
                            current["parent_interface"] = line.split("set interface", 1)[1].strip('" ')
                            safe_log(safe_translate("fortigate.set_parent_interface", parent=current['parent_interface']))
                            # 标记这是一个子接口
                            current["is_subinterface"] = True
                        elif line.lower().startswith("set vlanid"):
                            # 解析子接口的VLAN ID
                            vlanid = line.split("set vlanid", 1)[1].strip()
                            try:
                                vlanid_int = int(vlanid)
                                current["vlanid"] = vlanid_int
                                safe_log(safe_translate("fortigate.set_vlanid", vlanid=current['vlanid']))
                                # 检查VLAN ID范围是否在NTOS支持的范围内 (1-4063)
                                if vlanid_int < 1 or vlanid_int > 4063:
                                    error_msg = safe_translate("fortigate.vlanid_out_of_range", vlanid=vlanid_int)
                                    safe_log(safe_translate("fortigate.error_vlanid_range", error=error_msg), "error")
                                    current["invalid"] = True
                                    current["invalid_reason"] = error_msg
                            except ValueError:
                                safe_log(safe_translate("fortigate.invalid_vlanid", vlanid=vlanid), "warning")
                                current["invalid"] = True
                                current["invalid_reason"] = safe_translate("fortigate.cannot_parse_vlanid", vlanid=vlanid)
                        elif line.lower().startswith("set vdom"):
                            vdom = line.split("set vdom", 1)[1].strip('" ')
                            current["vdom"] = vdom
                            safe_log(safe_translate("fortigate.set_interface_vdom", vdom=vdom))
                            # 验证VDOM：NTOS只支持root vdom
                            if vdom != "root":
                                warning_msg = safe_translate("fortigate.warning.non_root_vdom_detected", 
                                                           interface=current.get('raw_name', 'unknown'), 
                                                           vdom=vdom)
                                safe_log(warning_msg, "warning")
                                result["warnings"].append(warning_msg)
                        elif line.lower().startswith("set defaultgw"):
                            defaultgw = line.split("set defaultgw", 1)[1].strip()
                            current["defaultgw"] = defaultgw
                            safe_log(safe_translate("fortigate.set_defaultgw", value=defaultgw))
                            # 验证defaultgw：NTOS不支持disable
                            if defaultgw == "disable":
                                warning_msg = safe_translate("fortigate.warning.defaultgw_disable_not_supported", 
                                                           interface=current.get('raw_name', 'unknown'))
                                safe_log(warning_msg, "warning")
                                result["warnings"].append(warning_msg)
                        elif line.lower().startswith("set role"):
                            current["role"] = line.split("set role", 1)[1].strip()
                            safe_log(safe_translate("fortigate.set_interface_role", role=current['role']))
                            # 验证role：NTOS只支持lan和wan，dmz和undefined会被转换为lan
                            role = current["role"].lower()
                            if role not in NTOS_SUPPORTED_ROLES:
                                if role in CONVERTIBLE_ROLES:
                                    # 对于dmz和undefined角色，记录警告但不跳过转换
                                    # 这些角色将在fortinet_interface_optimizer.py中被转换为lan
                                    warning_msg = safe_translate("fortigate.warning.unsupported_role", 
                                                               interface=current.get('raw_name', 'unknown'), 
                                                               role=current['role'])
                                    safe_log(warning_msg, "warning")
                                    result["warnings"].append(warning_msg)
                                    # 添加转换提示，但不跳过转换
                                    safe_log(safe_translate("fortigate.info.role_will_be_converted_to_lan", 
                                                           interface=current.get('raw_name', 'unknown'), 
                                                           role=current['role']), "info")
                                else:
                                    # 对于其他不支持的角色，跳过转换
                                    warning_msg = safe_translate("fortigate.warning.unsupported_role", 
                                                               interface=current.get('raw_name', 'unknown'), 
                                                               role=current['role'])
                                    safe_log(warning_msg, "warning")
                                    result["warnings"].append(warning_msg)
                                    # 标记接口为跳过转换，因为角色不被NTOS支持
                                    current["skip_conversion"] = True
                                    current["skip_reason"] = safe_translate("fortigate.reason.unsupported_role", role=current['role'])
                        elif line.lower().startswith("set type"):
                            original_type = line.split("set type", 1)[1].strip()
                            current["type"] = original_type
                            current["original_type"] = original_type  # 保存原始类型
                            safe_log(safe_translate("fortigate.set_interface_type", type=current['type']))

                            # 验证interface type：NTOS只支持physical和vlan类型
                            if current["type"] not in NTOS_SUPPORTED_INTERFACE_TYPES:
                                # 检查是否可以转换
                                if current["type"] in CONVERTIBLE_INTERFACE_TYPES:
                                    # 可转换的接口类型，进行优雅降级
                                    converted_type = CONVERTIBLE_INTERFACE_TYPES[current["type"]]
                                    info_msg = safe_translate("fortigate.info.interface_type_converted",
                                                            interface=current.get('raw_name', 'unknown'),
                                                            original_type=current["type"],
                                                            converted_type=converted_type)
                                    safe_log(info_msg, "info")

                                    # 更新接口类型为转换后的类型
                                    current["type"] = converted_type
                                    current["type_conversion"] = {
                                        "original": original_type,
                                        "converted": converted_type,
                                        "reason": "interface_type_compatibility"
                                    }
                                else:
                                    # 不可转换的接口类型，记录警告
                                    warning_msg = safe_translate("fortigate.warning.unsupported_interface_type",
                                                               interface=current.get('raw_name', 'unknown'),
                                                               type=current['type'])
                                    safe_log(warning_msg, "warning")
                                    result["warnings"].append(warning_msg)
                        elif line.lower().startswith("set allowaccess"):
                            raw = line.split("set allowaccess", 1)[1].strip()
                            access_list = raw.split()
                            # 检查不支持的访问方式
                            unsupported_access = []
                            for access in access_list:
                                if access not in NTOS_SUPPORTED_ACCESS:
                                    unsupported_access.append(access)
                            # 只保留支持的访问方式
                            current["allowaccess"] = [p for p in access_list if p in NTOS_SUPPORTED_ACCESS]
                            safe_log(safe_translate("fortigate.set_interface_allowaccess", access=current['allowaccess']))
                            # 如果有不支持的访问方式，发出警告
                            if unsupported_access:
                                warning_msg = safe_translate("fortigate.warning.unsupported_allowaccess", 
                                                           interface=current.get('raw_name', 'unknown'), 
                                                           access=', '.join(unsupported_access))
                                safe_log(warning_msg, "warning")
                                result["warnings"].append(warning_msg)
                    except Exception as e:
                        safe_log(safe_translate("fortigate.interface_attribute_error", error=str(e)), "error")
            
            # 解析静态路由部分
            elif current_section == "static_route":
                if line.lower().startswith("edit"):
                    current = {"id": line.split()[1].strip()}
                    i += 1
                    continue
                elif line.lower().startswith("next"):
                    if current:
                        # 检查路由是否有必要的字段
                        if "destination" not in current and current.get("blackhole") != "enable":
                            safe_log(safe_translate("fortigate.route_missing_destination", id=current.get("id", "unknown")))
                            current["destination"] = "0.0.0.0/0"  # 默认路由
                            
                        # 检查路由是否有必要的配置（网关或设备）
                        has_gateway = "gateway" in current and current["gateway"]
                        has_device = "device" in current and current["device"]
                        has_blackhole = "blackhole" in current and current["blackhole"] == "enable"
                        
                        # 只有当路由有必要的配置时，才添加到结果中
                        if has_gateway or has_device or has_blackhole:
                            safe_log(f"[DEBUG] 添加静态路由: {current}", "debug")
                            result["static_routes"].append(current)
                        else:
                            safe_log(safe_translate("fortigate.skipping_empty_route", id=current.get("id", "unknown")), "warning")
                            
                        current = None
                    i += 1
                    continue
                elif line.lower() == "end":
                    current_section = None
                    i += 1
                    continue
                elif current:
                    if line.lower().startswith("set dst"):
                        parts = line.split()
                        if len(parts) >= 3:  # 确保至少有"set dst network mask"三个部分
                            # 将目标网络和掩码转换为CIDR格式
                            network = parts[2]
                            mask = parts[3] if len(parts) > 3 else "***************"
                            prefix = mask_to_prefix(mask)
                            current["destination"] = f"{network}/{prefix}"
                            safe_log(safe_translate("fortigate.route_destination", dest=current['destination']))
                        else:
                            safe_log(safe_translate("fortigate.invalid_destination_format", line=line))
                    elif line.lower().startswith("set gateway"):
                        # 直接提取网关IP地址，不包含"set gateway"
                        parts = line.split()
                        if len(parts) >= 3:
                            gateway_ip = parts[2]
                            current["gateway"] = gateway_ip
                            safe_log(safe_translate("fortigate.route_gateway", gateway=current['gateway']))
                        else:
                            safe_log(safe_translate("fortigate.invalid_gateway_format", line=line))
                    elif line.lower().startswith("set device"):
                        # 提取设备名称，去除双引号
                        parts = line.split()
                        if len(parts) >= 3:
                            device_name = parts[2].strip('"')
                            current["device"] = device_name
                            safe_log(safe_translate("fortigate.route_device", device=current['device']))
                        else:
                            safe_log(safe_translate("fortigate.invalid_device_format", line=line))
                    elif line.lower().startswith("set distance"):
                        # 提取管理距离值
                        parts = line.split()
                        if len(parts) >= 3:
                            distance_value = parts[2]
                            current["distance"] = distance_value
                            safe_log(safe_translate("fortigate.route_distance", distance=current['distance']))
                        else:
                            safe_log(safe_translate("fortigate.invalid_distance_format", line=line))
                    elif line.lower().startswith("set blackhole"):
                        # 检查是否启用黑洞路由
                        parts = line.split()
                        if len(parts) >= 3:
                            blackhole_status = parts[2]
                            current["blackhole"] = blackhole_status
                            safe_log(safe_translate("fortigate.route_blackhole", status=current['blackhole']))
                        else:
                            safe_log(safe_translate("fortigate.invalid_blackhole_format", line=line))
                    elif line.lower().startswith("set bfd"):
                        # 检查是否启用BFD
                        parts = line.split()
                        if len(parts) >= 3:
                            bfd_status = parts[2]
                            current["bfd"] = bfd_status
                            safe_log(safe_translate("fortigate.route_bfd", status=current['bfd']))
                        else:
                            safe_log(safe_translate("fortigate.invalid_bfd_format", line=line))
            
            # 解析IPv6静态路由部分
            elif current_section == "static_route6":
                if line.lower().startswith("edit"):
                    current = {"id": line.split()[1].strip(), "is_ipv6": True}
                    i += 1
                    continue
                elif line.lower().startswith("next"):
                    if current:
                        # 检查路由是否有必要的字段
                        if "destination" not in current and current.get("blackhole") != "enable":
                            safe_log(safe_translate("fortigate.route_missing_destination", id=current.get("id", "unknown")))
                            current["destination"] = "::/0"  # IPv6默认路由
                        
                        # 检查路由是否有必要的配置（网关或设备）
                        has_gateway = "gateway" in current and current["gateway"]
                        has_device = "device" in current and current["device"]
                        has_blackhole = "blackhole" in current and current["blackhole"] == "enable"
                        
                        # 只有当路由有必要的配置时，才添加到结果中
                        if has_gateway or has_device or has_blackhole:
                            safe_log(f"[DEBUG] 添加IPv6静态路由: {current}", "debug")
                            result["static_routes_ipv6"].append(current)
                        else:
                            safe_log(safe_translate("fortigate.skipping_empty_ipv6_route", id=current.get("id", "unknown")), "warning")
                        
                        current = None
                    i += 1
                    continue
                elif line.lower() == "end":
                    current_section = None
                    i += 1
                    continue
                elif current:
                    if line.lower().startswith("set dst"):
                        parts = line.split()
                        if len(parts) >= 3:
                            # IPv6地址通常已经包含前缀长度，格式为ipv6_address/prefix
                            ipv6_dst = parts[2]
                            current["destination"] = ipv6_dst
                            safe_log(safe_translate("fortigate.route_destination", dest=current['destination']))
                        else:
                            safe_log(safe_translate("fortigate.invalid_destination_format", line=line))
                    elif line.lower().startswith("set gateway"):
                        parts = line.split()
                        if len(parts) >= 3:
                            gateway_ip = parts[2]
                            current["gateway"] = gateway_ip
                            safe_log(safe_translate("fortigate.route_gateway", gateway=current['gateway']))
                        else:
                            safe_log(safe_translate("fortigate.invalid_gateway_format", line=line))
                    elif line.lower().startswith("set device"):
                        parts = line.split()
                        if len(parts) >= 3:
                            device_name = parts[2].strip('"')
                            current["device"] = device_name
                            safe_log(safe_translate("fortigate.route_device", device=current['device']))
                        else:
                            safe_log(safe_translate("fortigate.invalid_device_format", line=line))
                    elif line.lower().startswith("set distance"):
                        parts = line.split()
                        if len(parts) >= 3:
                            distance_value = parts[2]
                            current["distance"] = distance_value
                            safe_log(safe_translate("fortigate.route_distance", distance=current['distance']))
                        else:
                            safe_log(safe_translate("fortigate.invalid_distance_format", line=line))
                    elif line.lower().startswith("set blackhole"):
                        parts = line.split()
                        if len(parts) >= 3:
                            blackhole_status = parts[2]
                            current["blackhole"] = blackhole_status
                            safe_log(safe_translate("fortigate.route_blackhole", status=current['blackhole']))
                        else:
                            safe_log(safe_translate("fortigate.invalid_blackhole_format", line=line))
            
            # 解析区域配置部分
            elif current_section == "zone":
                if line.lower().startswith("edit"):
                    # 提取区域名称
                    zone_name = line.split()[1].strip('"')
                    safe_log(safe_translate("fortigate.parsing_zone", name=zone_name))
                    current = {"name": zone_name, "interfaces": []}
                    i += 1
                    continue
                elif line.lower().startswith("next"):
                    if current:
                        log(f"[DEBUG] 添加区域: {current}", "debug")
                        result["zones"].append(current)
                        log(safe_translate("fortigate.zone_parsing_complete", name=current.get('name', 'unknown'), count=len(current.get('interfaces', []))))
                        current = None
                    i += 1
                    continue
                elif line.lower().startswith("set interface"):
                    # 提取区域中的接口列表
                    if current:
                        # 移除"set interface"前缀，然后分割剩余部分以获取接口名称
                        interfaces_str = line.split("set interface", 1)[1].strip()
                        # 按空格分割并去除引号
                        interfaces = [intf.strip('"') for intf in interfaces_str.split() if intf.strip('"')]
                        current["interfaces"] = interfaces
                        log(safe_translate("fortigate.set_zone_interfaces", zone=current.get('name', 'unknown'), interfaces=interfaces))
                elif line.lower().startswith("set intrazone"):
                    # 提取区域内部通信策略
                    if current:
                        intrazone = line.split("set intrazone", 1)[1].strip()
                        current["intrazone"] = intrazone
                        log(safe_translate("fortigate.set_intrazone", zone=current.get('name', 'unknown'), policy=intrazone))
                elif line.lower().startswith("set description"):
                    # 提取区域描述
                    if current:
                        description = line.split("set description", 1)[1].strip('" ')
                        current["description"] = description
                        log(safe_translate("fortigate.set_zone_description", zone=current.get('name', 'unknown'), desc=description))
                elif line.lower() == "end":
                    current_section = None
                    i += 1
                    continue

            # 注释：通用的 end 处理逻辑已移到所有特定配置部分处理逻辑之后
            
            # 检测地址对象、地址组和服务组部分
            elif line.lower().startswith("config firewall address"):
                current_section = "address"
                log(safe_translate("fortigate.start_parsing_address"))
                i += 1
                continue
            elif line.lower().startswith("config firewall addrgrp"):
                current_section = "addrgrp"
                log(safe_translate("fortigate.start_parsing_addrgrp"))
                i += 1
                continue
            elif line.lower().startswith("config firewall service group"):
                current_section = "service_group"
                log(safe_translate("fortigate.start_parsing_service_group"))
                i += 1
                continue
            elif line.lower().startswith("config firewall service custom"):
                current_section = "service_custom"
                log(safe_translate("fortigate.start_parsing_service_custom"))
                i += 1
                continue
            # 处理地址对象部分
            elif current_section == "address":
                if line.lower().startswith("edit"):
                    address_name = line.split()[1].strip('"')
                    log(safe_translate("fortigate.parsing_address", name=address_name))
                    
                    # 安全检查：验证地址对象名称
                    if len(address_name) > 50:
                        warning_msg = f"地址对象名称 '{address_name[:10]}...' 过长，已截断"
                        log(safe_translate("fortigate.warning.address_name_too_long", name=address_name[:10]+"..."), "warning")
                        result["warnings"].append(warning_msg)
                        address_name = address_name[:50]
                    
                    # 验证地址对象名称是否包含危险字符
                    if re.search(r'[;<>&|]', address_name):
                        warning_msg = f"地址对象名称 '{address_name}' 包含可疑字符，已被过滤"
                        log(safe_translate("fortigate.warning.address_name_suspicious", name=address_name), "warning")
                        result["warnings"].append(warning_msg)
                        address_name = re.sub(r'[;<>&|]', '', address_name)
                    
                    current = {"name": address_name}
                    i += 1
                    continue
                elif line.lower().startswith("next"):
                    if current:
                        # 添加到地址对象列表 - 使用JSON格式化地址对象属性，避免字符串中的花括号被误解为占位符
                        import json
                        attrs_json = json.dumps(current, ensure_ascii=False)
                        safe_log(safe_translate("fortigate.debug.adding_address", name=current.get('name', 'unknown'), attrs=attrs_json), "debug")
                        
                        # 安全检查：验证IP地址和子网格式
                        if "type" in current and current["type"] == "subnet" and "subnet" in current:
                            subnet = current["subnet"]
                            # 检查是否是有效的CIDR格式
                            if not '/' in subnet and ' ' in subnet:
                                # 可能是IP+掩码格式，尝试转换
                                parts = subnet.split()
                                if len(parts) >= 2:
                                    ip = parts[0]
                                    mask = parts[1]
                                    if not validate_ip_address(ip):
                                        warning_msg = f"地址对象 '{current.get('name')}' 的IP地址 '{ip}' 格式不正确"
                                        log(safe_translate("fortigate.warning.invalid_subnet_ip", name=current.get('name'), ip=ip), "warning")
                                        result["warnings"].append(warning_msg)
                        
                        # 安全检查：验证FQDN格式
                        if "type" in current and current["type"] == "fqdn" and "fqdn" in current:
                            fqdn = current["fqdn"]
                            # 简单验证FQDN格式
                            if not re.match(r'^[a-zA-Z0-9][a-zA-Z0-9\-\.]{1,253}[a-zA-Z0-9]$', fqdn):
                                warning_msg = f"地址对象 '{current.get('name')}' 的FQDN '{fqdn}' 格式可能不正确"
                                log(safe_translate("fortigate.warning.invalid_fqdn_format", name=current.get('name'), fqdn=fqdn), "warning")
                                result["warnings"].append(warning_msg)
                        
                        # 安全检查：验证IP范围格式
                        if "type" in current and current["type"] == "iprange" and "start-ip" in current and "end-ip" in current:
                            start_ip = current["start-ip"]
                            end_ip = current["end-ip"]
                            if not validate_ip_address(start_ip):
                                warning_msg = f"地址对象 '{current.get('name')}' 的起始IP '{start_ip}' 格式不正确"
                                log(safe_translate("fortigate.warning.invalid_start_ip", name=current.get('name'), ip=start_ip), "warning")
                                result["warnings"].append(warning_msg)
                            if not validate_ip_address(end_ip):
                                warning_msg = f"地址对象 '{current.get('name')}' 的结束IP '{end_ip}' 格式不正确"
                                log(safe_translate("fortigate.warning.invalid_end_ip", name=current.get('name'), ip=end_ip), "warning")
                                result["warnings"].append(warning_msg)
                        
                        result["address_objects"].append(current)
                        log(safe_translate("fortigate.address_parsing_complete", name=current.get('name', 'unknown')))
                        current = None
                    i += 1
                    continue
                elif current:
                    # 处理地址对象的属性设置
                    if line.lower().startswith("set uuid"):
                        current["uuid"] = line.split()[2].strip()
                        log(safe_translate("fortigate.debug.setting_address_uuid", name=current.get('name'), uuid=current["uuid"]), "debug")
                    elif line.lower().startswith("set type"):
                        current["type"] = line.split()[2].strip()
                        log(safe_translate("fortigate.debug.setting_address_type", name=current.get('name'), type=current["type"]), "debug")
                    elif line.lower().startswith("set subnet"):
                        # 处理子网格式 (IP 掩码)
                        subnet_parts = line.split()[2:]
                        if len(subnet_parts) >= 2:
                            current["subnet"] = f"{subnet_parts[0]} {subnet_parts[1]}"
                        else:
                            current["subnet"] = " ".join(subnet_parts)
                        log(safe_translate("fortigate.debug.setting_address_subnet", name=current.get('name'), subnet=current["subnet"]), "debug")
                    elif line.lower().startswith("set start-ip"):
                        current["start-ip"] = line.split()[2].strip()
                        log(safe_translate("fortigate.debug.setting_address_start_ip", name=current.get('name'), start_ip=current["start-ip"]), "debug")
                    elif line.lower().startswith("set end-ip"):
                        current["end-ip"] = line.split()[2].strip()
                        log(safe_translate("fortigate.debug.setting_address_end_ip", name=current.get('name'), end_ip=current["end-ip"]), "debug")
                    elif line.lower().startswith("set fqdn"):
                        current["fqdn"] = line.split()[2].strip().strip('"')
                        log(safe_translate("fortigate.debug.setting_address_fqdn", name=current.get('name'), fqdn=current["fqdn"]), "debug")
                    elif line.lower().startswith("set comment"):
                        # 处理注释，可能包含引号
                        comment_parts = line.split()[2:]
                        current["comment"] = " ".join(comment_parts).strip('"')
                        log(safe_translate("fortigate.debug.setting_address_comment", name=current.get('name'), comment=current["comment"]), "debug")
                    elif line.lower().startswith("set associated-interface"):
                        current["associated-interface"] = line.split()[2].strip().strip('"')
                        log(safe_translate("fortigate.debug.setting_address_interface", name=current.get('name'), interface=current["associated-interface"]), "debug")
                    elif line.lower().startswith("set color"):
                        current["color"] = line.split()[2].strip()
                        log(safe_translate("fortigate.debug.setting_address_color", name=current.get('name'), color=current["color"]), "debug")
                    elif line.lower().startswith("set wildcard"):
                        # 处理通配符地址
                        wildcard_parts = line.split()[2:]
                        if len(wildcard_parts) >= 2:
                            current["wildcard"] = f"{wildcard_parts[0]} {wildcard_parts[1]}"
                        else:
                            current["wildcard"] = " ".join(wildcard_parts)
                        log(safe_translate("fortigate.debug.setting_address_wildcard", name=current.get('name'), wildcard=current["wildcard"]), "debug")
                    elif line.lower().startswith("set country"):
                        current["country"] = line.split()[2].strip().strip('"')
                        log(safe_translate("fortigate.debug.setting_address_country", name=current.get('name'), country=current["country"]), "debug")
                    i += 1
                    continue
            
            # 处理地址组部分
            elif current_section == "addrgrp":
                if line.lower().startswith("edit"):
                    group_name = line.split()[1].strip('"')
                    log(safe_translate("fortigate.parsing_addrgrp", name=group_name))
                    current = {"name": group_name, "members": []}
                    i += 1
                    continue
                elif line.lower().startswith("next"):
                    if current:
                        # 添加到地址组列表 - 使用JSON格式化成员列表，避免字符串中的花括号被误解为占位符
                        import json
                        members_json = json.dumps(current.get('members', []), ensure_ascii=False)
                        log(safe_translate("fortigate.debug.adding_addrgrp", name=current.get('name', 'unknown'), members=members_json), "debug")
                        # 处理成员格式，增加对IP地址范围和IP地址的直接支持
                        processed_members = []
                        for member in current.get('members', []):
                            # 检查成员是否为IP范围格式 (例如: ***********-*************)
                            if re.match(r'^\d+\.\d+\.\d+\.\d+-\d+\.\d+\.\d+\.\d+$', member):
                                processed_members.append(member)
                                # 确保这个范围地址也被添加到地址对象中
                                range_addr_exists = False
                                for addr in result["address_objects"]:
                                    if addr.get('name') == member:
                                        range_addr_exists = True
                                        break
                                
                                if not range_addr_exists:
                                    # 添加一个新的地址对象用于范围
                                    start_ip, end_ip = member.split('-')
                                    result["address_objects"].append({
                                        "name": member,
                                        "type": "range",
                                        "start-ip": start_ip,
                                        "end-ip": end_ip
                                    })
                                    log(safe_translate("fortigate.auto_adding_range_address", name=member))
                            
                            # 检查成员是否为单个IP地址或网络格式 (例如: ***********)
                            elif re.match(r'^\d+\.\d+\.\d+\.\d+$', member):
                                processed_members.append(member)
                                # 确保这个地址也被添加到地址对象中
                                ip_addr_exists = False
                                for addr in result["address_objects"]:
                                    if addr.get('name') == member:
                                        ip_addr_exists = True
                                        break
                                
                                if not ip_addr_exists:
                                    # 添加一个新的地址对象用于单个IP
                                    result["address_objects"].append({
                                        "name": member,
                                        "type": "subnet",
                                        "subnet": f"{member}/32"  # 默认作为主机地址
                                    })
                                    log(safe_translate("fortigate.auto_adding_ip_address", name=member))
                            else:
                                # 常规成员名称引用
                                processed_members.append(member)
                        
                        # 更新处理后的成员列表
                        current["members"] = processed_members
                        result["address_groups"].append(current)
                        log(safe_translate("fortigate.addrgrp_parsing_complete", name=current.get('name', 'unknown'), count=len(current.get('members', []))))
                        current = None
                    i += 1
                    continue
                elif line.lower() == "end":
                    current_section = None
                    i += 1
                    continue
                elif current:
                    # 处理地址组属性
                    if line.lower().startswith("set member"):
                        # 解析member列表，格式为: set member "addr1" "addr2" "addr3"
                        member_line = line.split("set member", 1)[1].strip()
                        # 提取引号包围的成员名称
                        import re
                        members = re.findall(r'"([^"]*)"', member_line)
                        if not members:  # 如果没有匹配到引号包围的成员，尝试按空格分割
                            members = member_line.split()
                        current["members"] = members
                        log(safe_translate("fortigate.set_addrgrp_members", count=len(members)))
                    elif line.lower().startswith("set comment"):
                        current["comment"] = line.split("set comment", 1)[1].strip('" ')
                        log(safe_translate("fortigate.set_addrgrp_comment", comment=current['comment']))
            
            # 处理服务组部分
            elif current_section == "service_group":
                if line.lower().startswith("edit"):
                    # 修复：正确提取引号中的完整名称
                    import re
                    name_match = re.search(r'edit\s+"([^"]+)"', line)
                    if name_match:
                        group_name = name_match.group(1)
                    else:
                        # 如果没有匹配到引号中的名称，使用原来的方式
                        group_name = line.split()[1].strip('"')
                    
                    log(safe_translate("fortigate.parsing_service_group", name=group_name))
                    current = {"name": group_name, "members": []}
                    i += 1
                    continue
                elif line.lower().startswith("next"):
                    if current:
                        # 添加到服务组列表 - 使用JSON格式化成员列表，避免字符串中的花括号被误解为占位符
                        import json
                        members_json = json.dumps(current.get('members', []), ensure_ascii=False)
                        log(safe_translate("fortigate.debug.adding_service_group", name=current.get('name', 'unknown'), members=members_json), "debug")
                        
                        # 检查成员是否包含IP地址格式的字符串，如果包含，则将其处理为地址组
                        ip_pattern = re.compile(r'^(\d+\.\d+\.\d+\.\d+)(-\d+\.\d+\.\d+\.\d+)?$')
                        is_address_group = False
                        
                        if 'members' in current and current['members']:
                            for member in current['members']:
                                if ip_pattern.match(member):
                                    is_address_group = True
                                    break
                        
                        if is_address_group:
                            log(safe_translate("fortigate.debug.converting_to_address_group", name=current.get('name', 'unknown')), "debug")
                            # 如果是IP地址格式，则将其添加到地址组中
                            result["address_groups"].append(current)
                            
                            # 为地址组中的每个IP成员自动创建地址对象（如果不存在）
                            for member in current.get('members', []):
                                if ip_pattern.match(member):
                                    # 检查是否已存在此地址对象
                                    addr_exists = False
                                    for addr in result["address_objects"]:
                                        if addr.get('name') == member:
                                            addr_exists = True
                                            break
                                    
                                    if not addr_exists:
                                        # 根据格式确定地址对象类型
                                        if '-' in member:  # IP范围
                                            start_ip, end_ip = member.split('-')
                                            result["address_objects"].append({
                                                "name": member,
                                                "type": "range",
                                                "start-ip": start_ip,
                                                "end-ip": end_ip
                                            })
                                            log(safe_translate("fortigate.auto_adding_range_address", name=member))
                                        else:  # 单个IP
                                            result["address_objects"].append({
                                                "name": member,
                                                "type": "subnet",
                                                "subnet": f"{member}/32"  # 默认作为主机地址
                                            })
                                            log(safe_translate("fortigate.auto_adding_ip_address", name=member))
                        
                            log(safe_translate("fortigate.address_group_from_service_group", name=current.get('name', 'unknown'), count=len(current.get('members', []))))
                        else:
                            # 如果是普通服务组，则按原样处理
                            result["service_groups"].append(current)
                            log(safe_translate("fortigate.service_group_parsing_complete", name=current.get('name', 'unknown'), count=len(current.get('members', []))))
                        
                        current = None
                    i += 1
                    continue
                elif line.lower() == "end":
                    current_section = None
                    i += 1
                    continue
                elif current:
                    # 处理服务组属性
                    if line.lower().startswith("set member"):
                        # 解析member列表，格式为: set member "svc1" "svc2" "svc3"
                        member_line = line.split("set member", 1)[1].strip()

                        # 提取引号包围的成员名称
                        import re
                        members = re.findall(r'"([^"]*)"', member_line)

                        if not members:  # 如果没有匹配到引号包围的成员，尝试按空格分割
                            members = member_line.split()
                        
                        # 检查UUID是否被错误地包含在成员中
                        filtered_members = []
                        for member in members:
                            if not re.match(r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$', member, re.IGNORECASE):
                                filtered_members.append(member)
                            else:
                                print(f"DEBUG: 过滤掉UUID: {member}")
                        
                        current["members"] = filtered_members

                        log(safe_translate("fortigate.set_service_group_members", count=len(members)))
                    elif line.lower().startswith("set comment"):
                        current["comment"] = line.split("set comment", 1)[1].strip('" ')
                        log(safe_translate("fortigate.set_service_group_comment", comment=current['comment']))
                    elif line.lower().startswith("set uuid"):
                        uuid_value = line.split("set uuid", 1)[1].strip()
                        current["uuid"] = uuid_value
                        # UUID不应该影响成员列表

            # 处理自定义服务部分
            elif current_section == "service_custom":
                if line.lower().startswith("edit"):
                    service_name = line.split()[1].strip('"')
                    log(safe_translate("fortigate.parsing_service_custom", name=service_name))
                    current = {"name": service_name}
                    i += 1
                    continue
                elif line.lower().startswith("next"):
                    if current:
                        log(safe_translate("fortigate.debug.adding_service_custom", name=current.get('name', 'unknown')), "debug")
                        result["service_objects"].append(current)
                        log(safe_translate("fortigate.service_custom_parsing_complete", name=current.get('name', 'unknown')))
                        current = None
                    i += 1
                    continue
                elif line.lower() == "end":
                    current_section = None
                    i += 1
                    continue
                elif current:
                    # 处理自定义服务属性
                    # 注意：更具体的匹配必须在前面，避免被通用匹配覆盖
                    if line.lower().startswith("set protocol-number"):
                        current["protocol-number"] = line.split("set protocol-number", 1)[1].strip()
                        log(safe_translate("fortigate.set_service_protocol_number", number=current['protocol-number']))
                    elif line.lower().startswith("set protocol"):
                        current["protocol"] = line.split("set protocol", 1)[1].strip()
                        log(safe_translate("fortigate.set_service_protocol", protocol=current['protocol']))
                    elif line.lower().startswith("set tcp-portrange"):
                        current["tcp-portrange"] = line.split("set tcp-portrange", 1)[1].strip()
                        log(safe_translate("fortigate.set_service_tcp_portrange", portrange=current['tcp-portrange']))
                    elif line.lower().startswith("set udp-portrange"):
                        current["udp-portrange"] = line.split("set udp-portrange", 1)[1].strip()
                        log(safe_translate("fortigate.set_service_udp_portrange", portrange=current['udp-portrange']))
                    elif line.lower().startswith("set icmptype"):
                        current["icmptype"] = line.split("set icmptype", 1)[1].strip()
                        log(safe_translate("fortigate.set_service_icmptype", type=current['icmptype']))
                    elif line.lower().startswith("set icmpcode"):
                        current["icmpcode"] = line.split("set icmpcode", 1)[1].strip()
                        log(safe_translate("fortigate.set_service_icmpcode", code=current['icmpcode']))
                    elif line.lower().startswith("set sctp-portrange"):
                        current["sctp-portrange"] = line.split("set sctp-portrange", 1)[1].strip()
                        log(safe_translate("fortigate.set_service_sctp_portrange", portrange=current['sctp-portrange']))
                    elif line.lower().startswith("set iprange"):
                        current["iprange"] = line.split("set iprange", 1)[1].strip()
                        log(safe_translate("fortigate.set_service_iprange", iprange=current['iprange']))
                    elif line.lower().startswith("set comment"):
                        current["comment"] = line.split("set comment", 1)[1].strip('" ')
                        log(safe_translate("fortigate.set_service_comment", comment=current['comment']))

            # 处理防火墙策略部分
            elif current_section == "policy":
                if line.lower().startswith("edit"):
                    policy_id = line.split()[1].strip('"')
                    log(safe_translate("fortigate.parsing_policy", id=policy_id))
                    
                    # 安全检查：验证策略ID
                    if not policy_id.isdigit():
                        warning_msg = f"策略ID '{policy_id}' 不是有效的数字"
                        log(safe_translate("fortigate.warning.invalid_policy_id", id=policy_id), "warning")
                        result["warnings"].append(warning_msg)
                        # 尝试清理非数字字符
                        policy_id = re.sub(r'[^\d]', '', policy_id)
                        if not policy_id:
                            policy_id = "0"  # 设置默认值
                    
                    current = {"policyid": policy_id}
                    i += 1
                    continue
                elif line.lower().startswith("next"):
                    if current:
                        log(safe_translate("fortigate.debug.adding_policy", id=current.get('policyid', 'unknown')), "debug")
                        
                        # 安全检查：验证策略是否包含必需字段
                        required_fields = ["srcintf", "dstintf", "srcaddr", "dstaddr", "action"]
                        missing_fields = [field for field in required_fields if field not in current]
                        if missing_fields:
                            warning_msg = f"策略 '{current.get('policyid')}' 缺少必要字段: {', '.join(missing_fields)}"
                            log(safe_translate("fortigate.warning.policy_missing_fields", id=current.get('policyid'), fields=missing_fields), "warning")
                            result["warnings"].append(warning_msg)
                        
                        # 安全检查：验证服务列表大小
                        if "service" in current and isinstance(current["service"], list) and len(current["service"]) > 100:
                            warning_msg = f"策略 '{current.get('policyid')}' 的服务列表异常大 ({len(current['service'])}项)"
                            log(safe_translate("fortigate.warning.service_list_too_large", id=current.get('policyid'), count=len(current['service'])), "warning")
                            result["warnings"].append(warning_msg)
                            # 截断列表
                            current["service"] = current["service"][:100]
                        
                        # 安全检查：验证地址列表大小
                        if "srcaddr" in current and isinstance(current["srcaddr"], list) and len(current["srcaddr"]) > 100:
                            warning_msg = f"策略 '{current.get('policyid')}' 的源地址列表异常大 ({len(current['srcaddr'])}项)"
                            log(safe_translate("fortigate.warning.srcaddr_list_too_large", id=current.get('policyid'), count=len(current['srcaddr'])), "warning")
                            result["warnings"].append(warning_msg)
                            # 截断列表
                            current["srcaddr"] = current["srcaddr"][:100]
                        
                        if "dstaddr" in current and isinstance(current["dstaddr"], list) and len(current["dstaddr"]) > 100:
                            warning_msg = f"策略 '{current.get('policyid')}' 的目标地址列表异常大 ({len(current['dstaddr'])}项)"
                            log(safe_translate("fortigate.warning.dstaddr_list_too_large", id=current.get('policyid'), count=len(current['dstaddr'])), "warning")
                            result["warnings"].append(warning_msg)
                            # 截断列表
                            current["dstaddr"] = current["dstaddr"][:100]
                        
                        # 安全检查：验证注释长度
                        if "comments" in current and len(current["comments"]) > 200:
                            warning_msg = f"策略 '{current.get('policyid')}' 的注释过长，已截断"
                            log(safe_translate("fortigate.warning.policy_comment_too_long", id=current.get('policyid')), "warning")
                            result["warnings"].append(warning_msg)
                            # 截断注释
                            current["comments"] = current["comments"][:200]
                        
                        result["policies"].append(current)
                        log(safe_translate("fortigate.policy_parsing_complete", id=current.get('policyid', 'unknown')))
                        current = None
                    i += 1
                    continue
                elif line.lower() == "end":
                    # 在结束策略解析部分之前，保存当前正在解析的策略
                    if current:
                        # 验证策略的必需字段
                        if "policyid" not in current:
                            warning_msg = "策略缺少ID，跳过"
                            log(safe_translate("fortigate.warning.policy_missing_id"), "warning")
                            result["warnings"].append(warning_msg)
                        else:
                            # 检查注释长度
                            if "comments" in current and len(current["comments"]) > 200:
                                warning_msg = f"策略 '{current.get('policyid')}' 的注释过长，已截断"
                                log(safe_translate("fortigate.warning.policy_comment_too_long", id=current.get('policyid')), "warning")
                                result["warnings"].append(warning_msg)
                                # 截断注释
                                current["comments"] = current["comments"][:200]

                            result["policies"].append(current)
                            log(safe_translate("fortigate.policy_parsing_complete", id=current.get('policyid', 'unknown')))
                        current = None
                    current_section = None
                    i += 1
                    continue
                elif current:
                    # 处理策略属性
                    if line.lower().startswith("set srcintf"):
                        srcintf_line = line.split("set srcintf", 1)[1].strip()
                        srcintf = re.findall(r'"([^"]*)"', srcintf_line)
                        if not srcintf:
                            srcintf = srcintf_line.split()
                        
                        # 安全检查：验证接口名称
                        for intf in srcintf:
                            if re.search(r'[<>&;`|]', intf):
                                warning_msg = f"策略 '{current.get('policyid')}' 的源接口 '{intf}' 包含可疑字符"
                                log(safe_translate("fortigate.warning.srcintf_suspicious", id=current.get('policyid'), intf=intf), "warning")
                                result["warnings"].append(warning_msg)
                                # 过滤危险字符
                                srcintf = [re.sub(r'[<>&;`|]', '', i) for i in srcintf]
                                break
                        
                        current["srcintf"] = srcintf
                        log(safe_translate("fortigate.set_policy_srcintf", interfaces=srcintf))
                    elif line.lower().startswith("set dstintf"):
                        dstintf_line = line.split("set dstintf", 1)[1].strip()
                        dstintf = re.findall(r'"([^"]*)"', dstintf_line)
                        if not dstintf:
                            dstintf = dstintf_line.split()
                        
                        # 安全检查：验证接口名称
                        for intf in dstintf:
                            if re.search(r'[<>&;`|]', intf):
                                warning_msg = f"策略 '{current.get('policyid')}' 的目标接口 '{intf}' 包含可疑字符"
                                log(safe_translate("fortigate.warning.dstintf_suspicious", id=current.get('policyid'), intf=intf), "warning")
                                result["warnings"].append(warning_msg)
                                # 过滤危险字符
                                dstintf = [re.sub(r'[<>&;`|]', '', i) for i in dstintf]
                                break
                        
                        current["dstintf"] = dstintf
                        log(safe_translate("fortigate.set_policy_dstintf", interfaces=dstintf))
                    elif line.lower().startswith("set srcaddr"):
                        srcaddr_line = line.split("set srcaddr", 1)[1].strip()
                        srcaddr = re.findall(r'"([^"]*)"', srcaddr_line)
                        if not srcaddr:
                            srcaddr = srcaddr_line.split()
                        
                        # 安全检查：过滤地址对象名称中的危险字符
                        for idx, addr in enumerate(srcaddr):
                            if re.search(r'[<>&;`|]', addr):
                                warning_msg = f"策略 '{current.get('policyid')}' 的源地址 '{addr}' 包含可疑字符"
                                log(safe_translate("fortigate.warning.srcaddr_suspicious", id=current.get('policyid'), addr=addr), "warning")
                                result["warnings"].append(warning_msg)
                                srcaddr[idx] = re.sub(r'[<>&;`|]', '', addr)
                        
                        current["srcaddr"] = srcaddr
                        log(safe_translate("fortigate.set_policy_srcaddr", addresses=srcaddr))
                    elif line.lower().startswith("set dstaddr"):
                        dstaddr_line = line.split("set dstaddr", 1)[1].strip()
                        dstaddr = re.findall(r'"([^"]*)"', dstaddr_line)
                        if not dstaddr:
                            dstaddr = dstaddr_line.split()
                        
                        # 安全检查：过滤地址对象名称中的危险字符
                        for idx, addr in enumerate(dstaddr):
                            if re.search(r'[<>&;`|]', addr):
                                warning_msg = f"策略 '{current.get('policyid')}' 的目标地址 '{addr}' 包含可疑字符"
                                log(safe_translate("fortigate.warning.dstaddr_suspicious", id=current.get('policyid'), addr=addr), "warning")
                                result["warnings"].append(warning_msg)
                                dstaddr[idx] = re.sub(r'[<>&;`|]', '', addr)
                        
                        current["dstaddr"] = dstaddr
                        log(safe_translate("fortigate.set_policy_dstaddr", addresses=dstaddr))
                    elif line.lower().startswith("set service"):
                        service_line = line.split("set service", 1)[1].strip()
                        service = re.findall(r'"([^"]*)"', service_line)
                        if not service:
                            service = service_line.split()
                        
                        # 安全检查：过滤服务名称中的危险字符
                        for idx, svc in enumerate(service):
                            if re.search(r'[<>&;`|]', svc):
                                warning_msg = f"策略 '{current.get('policyid')}' 的服务 '{svc}' 包含可疑字符"
                                log(safe_translate("fortigate.warning.service_suspicious", id=current.get('policyid'), service=svc), "warning")
                                result["warnings"].append(warning_msg)
                                service[idx] = re.sub(r'[<>&;`|]', '', svc)
                        
                        current["service"] = service
                        log(safe_translate("fortigate.set_policy_service", services=service))
                    elif line.lower().startswith("set action"):
                        action = line.split("set action", 1)[1].strip()
                        
                        # 安全检查：验证动作是否有效
                        valid_actions = ["accept", "deny", "reject", "ipsec"]
                        if action.lower() not in valid_actions:
                            warning_msg = f"策略 '{current.get('policyid')}' 使用了未知的动作 '{action}'"
                            log(safe_translate("fortigate.warning.unknown_action", id=current.get('policyid'), action=action), "warning")
                            result["warnings"].append(warning_msg)
                        
                        current["action"] = action
                        log(safe_translate("fortigate.set_policy_action", action=current['action']))
                    elif line.lower().startswith("set status"):
                        current["status"] = line.split("set status", 1)[1].strip()
                        log(safe_translate("fortigate.set_policy_status", status=current['status']))
                    elif line.lower().startswith("set uuid"):
                        current["uuid"] = line.split("set uuid", 1)[1].strip()
                        log(safe_translate("fortigate.set_policy_uuid", uuid=current['uuid']))
                    elif line.lower().startswith("set schedule"):
                        schedule_value = line.split("set schedule", 1)[1].strip()
                        # 移除可能存在的引号
                        if schedule_value.startswith('"') and schedule_value.endswith('"'):
                            schedule_value = schedule_value[1:-1]
                        current["schedule"] = schedule_value
                        log(safe_translate("fortigate.set_policy_schedule", schedule=current['schedule']))
                    elif line.lower().startswith("set nat"):
                        current["nat"] = line.split("set nat", 1)[1].strip()
                        log(safe_translate("fortigate.set_policy_nat", nat=current['nat']))
                    elif line.lower().startswith("set ippool"):
                        ippool_line = line.split("set ippool", 1)[1].strip()
                        ippool = re.findall(r'"([^"]*)"', ippool_line)
                        if not ippool:
                            ippool = ippool_line.split()
                        current["ippool"] = ippool
                        log(safe_translate("fortigate.set_policy_ippool", ippool=ippool))
                    elif line.lower().startswith("set poolname"):
                        poolname_line = line.split("set poolname", 1)[1].strip()
                        poolname = re.findall(r'"([^"]*)"', poolname_line)
                        if not poolname:
                            poolname = poolname_line.split()
                        current["poolname"] = poolname
                        log(safe_translate("fortigate.set_policy_poolname", poolname=poolname))
                    elif line.lower().startswith("set logtraffic"):
                        current["logtraffic"] = line.split("set logtraffic", 1)[1].strip()
                        log(safe_translate("fortigate.set_policy_logtraffic", logtraffic=current['logtraffic']))
                    elif line.lower().startswith("set name"):
                        # 处理策略名称
                        name = line.split("set name", 1)[1].strip('" ')
                        if len(name) > 64:  # NTOS策略名称长度限制
                            warning_msg = f"策略 '{current.get('policyid')}' 的名称过长，已截断"
                            log(safe_translate("fortigate.warning.policy_name_too_long", id=current.get('policyid')), "warning")
                            result["warnings"].append(warning_msg)
                            name = name[:64]

                        # 过滤可能的危险字符，保留中文、英文、数字、下划线、连字符
                        name = re.sub(r'[<>&;`|]', '', name)
                        current["name"] = name
                        log(safe_translate("fortigate.set_policy_name", name=current['name']))
                    elif line.lower().startswith("set comments"):
                        # 安全处理：过滤和截断注释
                        comment = line.split("set comments", 1)[1].strip('" ')
                        if len(comment) > 200:
                            warning_msg = f"策略 '{current.get('policyid')}' 的注释过长，已截断"
                            log(safe_translate("fortigate.warning.comment_too_long", id=current.get('policyid')), "warning")
                            result["warnings"].append(warning_msg)
                            comment = comment[:200]

                        # 过滤可能的危险字符
                        comment = re.sub(r'[<>&;`|]', '', comment)
                        current["comments"] = comment
                        log(safe_translate("fortigate.set_policy_comments", comments=current['comments']))
                    elif line.lower().startswith("set utm-status"):
                        current["utm_status"] = line.split("set utm-status", 1)[1].strip()
                        log(safe_translate("fortigate.set_policy_utm_status", status=current['utm_status']))
                    elif line.lower().startswith("set profile-type"):
                        current["profile_type"] = line.split("set profile-type", 1)[1].strip()
                        log(safe_translate("fortigate.set_policy_profile_type", type=current['profile_type']))
                    elif line.lower().startswith("set profile-protocol-options"):
                        current["profile_protocol_options"] = line.split("set profile-protocol-options", 1)[1].strip('" ')
                        log(safe_translate("fortigate.set_policy_profile_protocol_options", options=current['profile_protocol_options']))
                    elif line.lower().startswith("set ssl-ssh-profile"):
                        current["ssl_ssh_profile"] = line.split("set ssl-ssh-profile", 1)[1].strip('" ')
                        log(safe_translate("fortigate.set_policy_ssl_ssh_profile", profile=current['ssl_ssh_profile']))
                    elif line.lower().startswith("set av-profile"):
                        current["av_profile"] = line.split("set av-profile", 1)[1].strip('" ')
                        log(safe_translate("fortigate.set_policy_av_profile", profile=current['av_profile']))
                    elif line.lower().startswith("set webfilter-profile"):
                        current["webfilter_profile"] = line.split("set webfilter-profile", 1)[1].strip('" ')
                        log(safe_translate("fortigate.set_policy_webfilter_profile", profile=current['webfilter_profile']))
                    elif line.lower().startswith("set ips-sensor"):
                        current["ips_sensor"] = line.split("set ips-sensor", 1)[1].strip('" ')
                        log(safe_translate("fortigate.set_policy_ips_sensor", sensor=current['ips_sensor']))
                    elif line.lower().startswith("set application-list"):
                        current["application_list"] = line.split("set application-list", 1)[1].strip('" ')
                        log(safe_translate("fortigate.set_policy_application_list", list=current['application_list']))
                    elif line.lower().startswith("set file-filter-profile"):
                        current["file_filter_profile"] = line.split("set file-filter-profile", 1)[1].strip('" ')
                        log(safe_translate("fortigate.set_policy_file_filter_profile", profile=current['file_filter_profile']))
                    elif line.lower().startswith("set fixedport"):
                        current["fixedport"] = line.split("set fixedport", 1)[1].strip()
                        log(safe_translate("fortigate.set_policy_fixedport", fixedport=current['fixedport']))
                    elif line.lower().startswith("set port-preserve"):
                        current["port_preserve"] = line.split("set port-preserve", 1)[1].strip()
                        log(safe_translate("fortigate.set_policy_port_preserve", port_preserve=current['port_preserve']))
                    elif line.lower().startswith("set users"):
                        users_line = line.split("set users", 1)[1].strip()
                        users = re.findall(r'"([^"]*)"', users_line)
                        if not users:
                            users = users_line.split()
                        current["users"] = users
                        log(safe_translate("fortigate.set_policy_users", users=users))
                    elif line.lower().startswith("set groups"):
                        groups_line = line.split("set groups", 1)[1].strip()
                        groups = re.findall(r'"([^"]*)"', groups_line)
                        if not groups:
                            groups = groups_line.split()
                        current["groups"] = groups
                        log(safe_translate("fortigate.set_policy_groups", groups=groups))
                
            elif current_section == "addrgrp":
                if line.lower().startswith("edit"):
                    name = line.split()[1].strip('"')
                    log(safe_translate("fortigate.parsing_addrgrp", name=name))
                      # 安全检查：地址组名称过长或包含可疑字符
                    if len(name) > 100:
                        warning_msg = f"地址组名称 '{name}' 过长"
                        log(safe_translate("fortigate.warning.addrgrp_name_too_long", name=name), "warning")
                        result["warnings"].append(warning_msg)
                        name = name[:100]  # 截断名称
                    if re.search(r'[<>&;`|]', name):
                        warning_msg = f"地址组名称 '{name}' 包含可疑字符"
                        log(safe_translate("fortigate.warning.addrgrp_name_suspicious", name=name), "warning")
                        result["warnings"].append(warning_msg)
                        name = re.sub(r'[<>&;`|]', '', name)  # 过滤危险字符
                    
                    current = {"name": name}
                    i += 1
                    continue
                elif line.lower().startswith("next"):
                    if current:
                        # 添加到地址组列表 - 使用JSON格式化成员列表，避免字符串中的花括号被误解为占位符
                        import json
                        members_json = json.dumps(current.get('member', []), ensure_ascii=False)
                        log(safe_translate("fortigate.debug.adding_addrgrp", name=current.get('name', 'unknown'), members=members_json), "debug")
                        
                        # 安全检查：验证地址组是否包含成员
                        if "member" not in current or not current["member"]:
                            warning_msg = f"地址组 '{current.get('name')}' 不包含任何成员"
                            log(safe_translate("fortigate.warning.addrgrp_no_members", name=current.get('name')), "warning")
                            result["warnings"].append(warning_msg)
                        
                        # 安全检查：验证成员数量是否异常
                        if "member" in current and isinstance(current["member"], list) and len(current["member"]) > 500:
                            warning_msg = f"地址组 '{current.get('name')}' 的成员数量异常大 ({len(current['member'])}个)"
                            log(safe_translate("fortigate.warning.addrgrp_members_too_many", name=current.get('name'), count=len(current['member'])), "warning")
                            result["warnings"].append(warning_msg)
                            # 截断成员列表
                            current["member"] = current["member"][:500]
                            
                        result["addrgrps"].append(current)
                        log(safe_translate("fortigate.addrgrp_parsing_complete", name=current.get('name', 'unknown')))
                        current = None
                    i += 1
                    continue
                elif line.lower() == "end":
                    current_section = None
                    i += 1
                    continue
                elif current:
                    if line.lower().startswith("set member"):
                        member_line = line.split("set member", 1)[1].strip()
                        members = re.findall(r'"([^"]*)"', member_line)
                        if not members:
                            members = member_line.split()
                          # 安全检查：过滤成员名称中的危险字符
                        for idx, member in enumerate(members):
                            if re.search(r'[<>&;`|]', member):
                                warning_msg = f"地址组 '{current.get('name')}' 的成员 '{member}' 包含可疑字符"
                                log(safe_translate("fortigate.warning.addrgrp_member_suspicious", name=current.get('name'), member=member), "warning")
                                result["warnings"].append(warning_msg)
                                members[idx] = re.sub(r'[<>&;`|]', '', member)
                        current["member"] = members
                        log(safe_translate("fortigate.set_addrgrp_members", count=len(members)))
                    elif line.lower().startswith("set comment"):
                        # 安全处理：过滤和截断注释
                        comment = line.split("set comment", 1)[1].strip('" ')
                        if len(comment) > 200:
                            warning_msg = f"地址组 '{current.get('name')}' 的注释过长，已截断"
                            log(safe_translate("fortigate.warning.comment_too_long", name=current.get('name')), "warning")
                            result["warnings"].append(warning_msg)
                            comment = comment[:200]
                        
                        # 过滤可能的危险字符
                        comment = re.sub(r'[<>&;`|]', '', comment)
                        current["comment"] = comment
                        log(safe_translate("fortigate.set_addrgrp_comment", comment=current['comment']))

            # 处理NAT VIP配置
            elif current_section == "vip":
                if line.lower().startswith("edit"):
                    vip_name = line.split()[1].strip('"')
                    log(safe_translate("fortigate.parsing_vip", name=vip_name))
                    current = {"name": vip_name, "type": "vip"}
                    i += 1
                    continue
                elif line.lower().startswith("next"):
                    if current:
                        log(safe_translate("fortigate.debug.adding_vip", name=current.get('name', 'unknown')), "debug")
                        result["nat_rules"].append(current)
                        log(safe_translate("fortigate.vip_parsing_complete", name=current.get('name', 'unknown')))
                        current = None
                    i += 1
                    continue
                elif line.lower() == "end":
                    current_section = None
                    i += 1
                    continue
                elif current:
                    # 处理VIP属性
                    if line.lower().startswith("set extip"):
                        current["extip"] = line.split("set extip", 1)[1].strip()
                        log(safe_translate("fortigate.set_vip_extip", ip=current['extip']))
                    elif line.lower().startswith("set extintf"):
                        current["extintf"] = line.split("set extintf", 1)[1].strip('"')
                        log(safe_translate("fortigate.set_vip_extintf", interface=current['extintf']))
                    elif line.lower().startswith("set mappedip"):
                        mappedip_line = line.split("set mappedip", 1)[1].strip()
                        # 尝试提取引号中的IP地址
                        mappedip = re.findall(r'"([^"]*)"', mappedip_line)
                        if not mappedip:
                            # 如果没有引号，尝试直接提取IP地址
                            mappedip = [mappedip_line]
                        current["mappedip"] = mappedip[0] if mappedip else ""
                        log(safe_translate("fortigate.set_vip_mappedip", ip=current['mappedip']))
                    elif line.lower().startswith("set portforward"):
                        current["portforward"] = line.split("set portforward", 1)[1].strip()
                        log(safe_translate("fortigate.set_vip_portforward", enabled=current['portforward']))
                    elif line.lower().startswith("set protocol"):
                        current["protocol"] = line.split("set protocol", 1)[1].strip()
                        log(safe_translate("fortigate.set_vip_protocol", protocol=current['protocol']))
                    elif line.lower().startswith("set extport"):
                        current["extport"] = line.split("set extport", 1)[1].strip()
                        log(safe_translate("fortigate.set_vip_extport", port=current['extport']))
                    elif line.lower().startswith("set mappedport"):
                        current["mappedport"] = line.split("set mappedport", 1)[1].strip()
                        log(safe_translate("fortigate.set_vip_mappedport", port=current['mappedport']))
                    elif line.lower().startswith("set comment"):
                        current["comment"] = line.split("set comment", 1)[1].strip('" ')
                        log(safe_translate("fortigate.set_vip_comment", comment=current['comment']))
            
            # 处理IP池配置
            elif current_section == "ippool":
                if line.lower().startswith("edit"):
                    pool_name = line.split()[1].strip('"')
                    log(safe_translate("fortigate.parsing_ippool", name=pool_name))
                    current = {"name": pool_name, "type": "ippool"}
                    i += 1
                    continue
                elif line.lower().startswith("next"):
                    if current:
                        pool_name = current.get('name', 'unknown')
                        log(safe_translate("fortigate.debug.adding_ippool", name=pool_name), "debug")
                        result["ippools"][pool_name] = current
                        log(safe_translate("fortigate.ippool_parsing_complete", name=pool_name))
                        current = None
                    i += 1
                    continue
                elif line.lower() == "end":
                    current_section = None
                    i += 1
                    continue
                elif current:
                    # 处理IP池属性
                    if line.lower().startswith("set startip"):
                        current["startip"] = line.split("set startip", 1)[1].strip()
                        log(safe_translate("fortigate.set_ippool_startip", ip=current['startip']))
                    elif line.lower().startswith("set endip"):
                        current["endip"] = line.split("set endip", 1)[1].strip()
                        log(safe_translate("fortigate.set_ippool_endip", ip=current['endip']))
                    elif line.lower().startswith("set source-startip"):
                        current["source_startip"] = line.split("set source-startip", 1)[1].strip()
                        log(safe_translate("fortigate.set_ippool_source_startip", ip=current['source_startip']))
                    elif line.lower().startswith("set source-endip"):
                        current["source_endip"] = line.split("set source-endip", 1)[1].strip()
                        log(safe_translate("fortigate.set_ippool_source_endip", ip=current['source_endip']))
                    elif line.lower().startswith("set type"):
                        current["pool_type"] = line.split("set type", 1)[1].strip()
                        log(safe_translate("fortigate.set_ippool_type", type=current['pool_type']))
                    elif line.lower().startswith("set comment"):
                        current["comment"] = line.split("set comment", 1)[1].strip('" ')
                        log(safe_translate("fortigate.set_ippool_comment", comment=current['comment']))
            
            # 处理IPsec VPN Phase1配置
            elif current_section == "ipsec_phase1":
                if line.lower().startswith("edit"):
                    tunnel_name = line.split()[1].strip('"')
                    log(safe_translate("fortigate.parsing_ipsec_phase1", name=tunnel_name))
                    current = {"name": tunnel_name, "type": "ipsec_phase1"}
                    i += 1
                    continue
                elif line.lower().startswith("next"):
                    if current:
                        log(safe_translate("fortigate.debug.adding_ipsec_phase1", name=current.get('name', 'unknown')), "debug")
                        # 先检查是否已存在相同名称的隧道，如果存在则合并属性
                        found = False
                        for tunnel in result["vpn_configs"]["ipsec"]:
                            if tunnel.get("name") == current.get("name"):
                                tunnel.update(current)
                                found = True
                                break
                        
                        if not found:
                            result["vpn_configs"]["ipsec"].append(current)
                        
                        log(safe_translate("fortigate.ipsec_phase1_parsing_complete", name=current.get('name', 'unknown')))
                        current = None
                    i += 1
                    continue
                elif line.lower() == "end":
                    current_section = None
                    i += 1
                    continue
                elif current:
                    # 处理Phase1属性
                    if line.lower().startswith("set interface"):
                        current["interface"] = line.split("set interface", 1)[1].strip('"')
                        log(safe_translate("fortigate.set_ipsec_interface", interface=current['interface']))
                    elif line.lower().startswith("set remote-gw"):
                        current["remote_gw"] = line.split("set remote-gw", 1)[1].strip()
                        log(safe_translate("fortigate.set_ipsec_remote_gw", gw=current['remote_gw']))
                    elif line.lower().startswith("set psksecret"):
                        # 处理预共享密钥，如果是加密的则保留
                        psk_raw = line.split("set psksecret", 1)[1].strip('" ')
                        if psk_raw.startswith("ENC "):
                            current["psksecret"] = psk_raw
                        else:
                            current["psksecret"] = psk_raw
                        log(safe_translate("fortigate.set_ipsec_psksecret"))
                    elif line.lower().startswith("set proposal"):
                        current["proposal"] = line.split("set proposal", 1)[1].strip()
                        log(safe_translate("fortigate.set_ipsec_proposal", proposal=current['proposal']))
                    elif line.lower().startswith("set dhgrp"):
                        current["dhgrp"] = line.split("set dhgrp", 1)[1].strip()
                        log(safe_translate("fortigate.set_ipsec_dhgrp", dhgrp=current['dhgrp']))
                    elif line.lower().startswith("set keylife"):
                        current["keylife"] = line.split("set keylife", 1)[1].strip()
                        log(safe_translate("fortigate.set_ipsec_keylife", keylife=current['keylife']))
                    elif line.lower().startswith("set mode"):
                        current["mode"] = line.split("set mode", 1)[1].strip()
                        log(safe_translate("fortigate.set_ipsec_mode", mode=current['mode']))
                    elif line.lower().startswith("set comments"):
                        current["comments"] = line.split("set comments", 1)[1].strip('" ')
                        log(safe_translate("fortigate.set_ipsec_comments", comments=current['comments']))
            
            # 处理IPsec VPN Phase2配置
            elif current_section == "ipsec_phase2":
                if line.lower().startswith("edit"):
                    phase2_name = line.split()[1].strip('"')
                    log(safe_translate("fortigate.parsing_ipsec_phase2", name=phase2_name))
                    current = {"name": phase2_name, "type": "ipsec_phase2"}
                    i += 1
                    continue
                elif line.lower().startswith("next"):
                    if current:
                        log(safe_translate("fortigate.debug.adding_ipsec_phase2", name=current.get('name', 'unknown')), "debug")
                        # 获取关联的phase1配置
                        if "phase1name" in current:
                            phase1_name = current["phase1name"]
                            # 查找对应的phase1隧道并添加phase2信息
                            for tunnel in result["vpn_configs"]["ipsec"]:
                                if tunnel.get("name") == phase1_name:
                                    if "phase2" not in tunnel:
                                        tunnel["phase2"] = []
                                    tunnel["phase2"].append(current)
                                    break
                        
                        log(safe_translate("fortigate.ipsec_phase2_parsing_complete", name=current.get('name', 'unknown')))
                        current = None
                    i += 1
                    continue
                elif line.lower() == "end":
                    current_section = None
                    i += 1
                    continue
                elif current:
                    # 处理Phase2属性
                    if line.lower().startswith("set phase1name"):
                        current["phase1name"] = line.split("set phase1name", 1)[1].strip('"')
                        log(safe_translate("fortigate.set_ipsec_phase1name", name=current['phase1name']))
                    elif line.lower().startswith("set proposal"):
                        current["proposal"] = line.split("set proposal", 1)[1].strip()
                        log(safe_translate("fortigate.set_ipsec_phase2_proposal", proposal=current['proposal']))
                    elif line.lower().startswith("set pfs"):
                        current["pfs"] = line.split("set pfs", 1)[1].strip()
                        log(safe_translate("fortigate.set_ipsec_pfs", pfs=current['pfs']))
                    elif line.lower().startswith("set dhgrp"):
                        current["dhgrp"] = line.split("set dhgrp", 1)[1].strip()
                        log(safe_translate("fortigate.set_ipsec_phase2_dhgrp", dhgrp=current['dhgrp']))
                    elif line.lower().startswith("set keylife"):
                        current["keylife"] = line.split("set keylife", 1)[1].strip()
                        log(safe_translate("fortigate.set_ipsec_phase2_keylife", keylife=current['keylife']))
                    elif line.lower().startswith("set src-addr-type"):
                        current["src_addr_type"] = line.split("set src-addr-type", 1)[1].strip()
                        log(safe_translate("fortigate.set_ipsec_src_addr_type", type=current['src_addr_type']))
                    elif line.lower().startswith("set dst-addr-type"):
                        current["dst_addr_type"] = line.split("set dst-addr-type", 1)[1].strip()
                        log(safe_translate("fortigate.set_ipsec_dst_addr_type", type=current['dst_addr_type']))
                    elif line.lower().startswith("set src-subnet"):
                        current["src_subnet"] = line.split("set src-subnet", 1)[1].strip()
                        log(safe_translate("fortigate.set_ipsec_src_subnet", subnet=current['src_subnet']))
                    elif line.lower().startswith("set dst-subnet"):
                        current["dst_subnet"] = line.split("set dst-subnet", 1)[1].strip()
                        log(safe_translate("fortigate.set_ipsec_dst_subnet", subnet=current['dst_subnet']))
            
            # 处理SSL VPN设置
            elif current_section == "ssl_vpn_settings":
                if line.lower() == "end":
                    current_section = None
                    i += 1
                    continue
                else:
                    # 处理SSL VPN全局设置
                    if line.lower().startswith("set status"):
                        result["vpn_configs"]["ssl_vpn"]["status"] = line.split("set status", 1)[1].strip()
                        log(safe_translate("fortigate.set_ssl_vpn_status", status=result["vpn_configs"]["ssl_vpn"]["status"]))
                    elif line.lower().startswith("set servercert"):
                        result["vpn_configs"]["ssl_vpn"]["servercert"] = line.split("set servercert", 1)[1].strip('"')
                        log(safe_translate("fortigate.set_ssl_vpn_servercert", cert=result["vpn_configs"]["ssl_vpn"]["servercert"]))
                    elif line.lower().startswith("set tunnel-ip-pools"):
                        pools_line = line.split("set tunnel-ip-pools", 1)[1].strip()
                        pools = re.findall(r'"([^"]*)"', pools_line)
                        if pools:
                            result["vpn_configs"]["ssl_vpn"]["tunnel_ip_pools"] = pools
                            log(safe_translate("fortigate.set_ssl_vpn_ip_pools", pools=pools))
                    elif line.lower().startswith("set dns-server1"):
                        result["vpn_configs"]["ssl_vpn"]["dns_server1"] = line.split("set dns-server1", 1)[1].strip()
                        log(safe_translate("fortigate.set_ssl_vpn_dns_server1", server=result["vpn_configs"]["ssl_vpn"]["dns_server1"]))
                    elif line.lower().startswith("set dns-server2"):
                        result["vpn_configs"]["ssl_vpn"]["dns_server2"] = line.split("set dns-server2", 1)[1].strip()
                        log(safe_translate("fortigate.set_ssl_vpn_dns_server2", server=result["vpn_configs"]["ssl_vpn"]["dns_server2"]))
                    elif line.lower().startswith("set port"):
                        result["vpn_configs"]["ssl_vpn"]["port"] = line.split("set port", 1)[1].strip()
                        log(safe_translate("fortigate.set_ssl_vpn_port", port=result["vpn_configs"]["ssl_vpn"]["port"]))
                    elif line.lower().startswith("set source-interface"):
                        intf_line = line.split("set source-interface", 1)[1].strip()
                        intfs = re.findall(r'"([^"]*)"', intf_line)
                        if intfs:
                            result["vpn_configs"]["ssl_vpn"]["source_interface"] = intfs
                            log(safe_translate("fortigate.set_ssl_vpn_source_interface", interfaces=intfs))
            
            # 处理SSL VPN门户配置
            elif current_section == "ssl_vpn_portal":
                if line.lower().startswith("edit"):
                    portal_name = line.split()[1].strip('"')
                    log(safe_translate("fortigate.parsing_ssl_vpn_portal", name=portal_name))
                    current = {"name": portal_name}
                    i += 1
                    continue
                elif line.lower().startswith("next"):
                    if current:
                        log(safe_translate("fortigate.debug.adding_ssl_vpn_portal", name=current.get('name', 'unknown')), "debug")
                        if "portals" not in result["vpn_configs"]["ssl_vpn"]:
                            result["vpn_configs"]["ssl_vpn"]["portals"] = []
                        result["vpn_configs"]["ssl_vpn"]["portals"].append(current)
                        log(safe_translate("fortigate.ssl_vpn_portal_parsing_complete", name=current.get('name', 'unknown')))
                        current = None
                    i += 1
                    continue
                elif line.lower() == "end":
                    current_section = None
                    i += 1
                    continue
                elif current:
                    # 处理Portal属性
                    if line.lower().startswith("set tunnel-mode"):
                        current["tunnel_mode"] = line.split("set tunnel-mode", 1)[1].strip()
                        log(safe_translate("fortigate.set_ssl_vpn_tunnel_mode", mode=current['tunnel_mode']))
                    elif line.lower().startswith("set web-mode"):
                        current["web_mode"] = line.split("set web-mode", 1)[1].strip()
                        log(safe_translate("fortigate.set_ssl_vpn_web_mode", mode=current['web_mode']))
                    elif line.lower().startswith("set split-tunneling"):
                        current["split_tunneling"] = line.split("set split-tunneling", 1)[1].strip()
                        log(safe_translate("fortigate.set_ssl_vpn_split_tunneling", enabled=current['split_tunneling']))
                    elif line.lower().startswith("set split-tunneling-routing-address"):
                        current["split_tunneling_routing_address"] = line.split("set split-tunneling-routing-address", 1)[1].strip()
                        log(safe_translate("fortigate.set_ssl_vpn_split_tunneling_routes", routes=current['split_tunneling_routing_address']))
                    elif line.lower().startswith("set dns-server1"):
                        current["dns_server1"] = line.split("set dns-server1", 1)[1].strip()
                        log(safe_translate("fortigate.set_ssl_vpn_portal_dns_server1", server=current['dns_server1']))
                    elif line.lower().startswith("set dns-server2"):
                        current["dns_server2"] = line.split("set dns-server2", 1)[1].strip()
                        log(safe_translate("fortigate.set_ssl_vpn_portal_dns_server2", server=current['dns_server2']))
            
            # 处理DHCP服务器配置部分
            elif current_section == "dhcp_server":
                if line.lower().startswith("edit"):
                    dhcp_id = line.split()[1].strip('"')
                    log(safe_translate("fortigate.parsing_dhcp_server", id=dhcp_id))
                    current = {"id": dhcp_id, "ip_ranges": [], "options": []}
                    i += 1
                    continue
                elif line.lower().startswith("next"):
                    if current:
                        log(safe_translate("fortigate.debug.adding_dhcp_server", id=current.get('id', 'unknown')), "debug")
                        result["dhcp_configs"].append(current)
                        log(safe_translate("fortigate.dhcp_server_parsing_complete", id=current.get('id', 'unknown')))
                        current = None
                    i += 1
                    continue
                elif line.lower() == "end":
                    current_section = None
                    i += 1
                    continue
                elif current:
                    # 处理DHCP服务器属性
                    if line.lower().startswith("set interface"):
                        current["interface"] = line.split("set interface", 1)[1].strip('"')
                        log(safe_translate("fortigate.set_dhcp_interface", interface=current['interface']))
                    elif line.lower().startswith("set default-gateway"):
                        current["default_gateway"] = line.split("set default-gateway", 1)[1].strip()
                        log(safe_translate("fortigate.set_dhcp_default_gateway", gateway=current['default_gateway']))
                    elif line.lower().startswith("set netmask"):
                        current["netmask"] = line.split("set netmask", 1)[1].strip()
                        log(safe_translate("fortigate.set_dhcp_netmask", netmask=current['netmask']))
                    elif line.lower().startswith("set dns-server1"):
                        current["dns_server1"] = line.split("set dns-server1", 1)[1].strip()
                        log(safe_translate("fortigate.set_dhcp_dns_server1", server=current['dns_server1']))
                    elif line.lower().startswith("set dns-server2"):
                        current["dns_server2"] = line.split("set dns-server2", 1)[1].strip()
                        log(safe_translate("fortigate.set_dhcp_dns_server2", server=current['dns_server2']))
                    elif line.lower().startswith("set domain"):
                        current["domain"] = line.split("set domain", 1)[1].strip('"')
                        log(safe_translate("fortigate.set_dhcp_domain", domain=current['domain']))
                    elif line.lower().startswith("set lease-time"):
                        current["lease_time"] = line.split("set lease-time", 1)[1].strip()
                        log(safe_translate("fortigate.set_dhcp_lease_time", time=current['lease_time']))
                    elif line.lower().startswith("config ip-range"):
                        # 解析IP范围子配置
                        try:
                            ip_range, i = parse_dhcp_ip_range(lines, i + 1)
                            if ip_range:
                                # 确保ip_ranges字段存在
                                if "ip_ranges" not in current:
                                    current["ip_ranges"] = []
                                current["ip_ranges"].append(ip_range)
                        except Exception as e:
                            safe_log(safe_translate("fortigate.warning.dhcp_ip_range_parse_error", error=str(e)), "warning")
                            # 跳过这个IP范围配置，继续解析
                            while i < len(lines) and not lines[i].strip().lower() == "end":
                                i += 1
                        continue
                    elif line.lower().startswith("config options"):
                        # 解析DHCP选项子配置
                        try:
                            options, i = parse_dhcp_options(lines, i + 1)
                            if options and current:
                                # 确保options字段存在
                                if "options" not in current:
                                    current["options"] = []
                                current["options"].extend(options)
                        except Exception as e:
                            safe_log(safe_translate("fortigate.warning.dhcp_options_parse_error", error=str(e)), "warning")
                            # 跳过这个选项配置，继续解析
                            while i < len(lines) and not lines[i].strip().lower() == "end":
                                i += 1
                        continue

            # 处理系统设置配置部分
            elif current_section == "system_settings":
                if line.lower() == "end":
                    # 如果是嵌套配置，不要在这里重置current_section
                    # 因为嵌套配置的end处理已经在上面的配置栈逻辑中处理了
                    if not nested_system_settings:
                        current_section = None
                        safe_log(safe_translate("fortigate.system_settings_section_end"))
                    i += 1
                    continue
                else:
                    # 处理系统设置属性
                    if line.lower().startswith("set opmode"):
                        opmode_value = line.split("set opmode", 1)[1].strip()
                        result["system_settings"]["opmode"] = opmode_value
                        safe_log(safe_translate("fortigate.set_opmode", opmode=opmode_value))
                    elif line.lower().startswith("set manageip"):
                        manageip_value = line.split("set manageip", 1)[1].strip()
                        result["system_settings"]["manageip"] = manageip_value
                        safe_log(safe_translate("fortigate.set_manageip", manageip=manageip_value))
                    elif line.lower().startswith("set hostname"):
                        hostname_value = line.split("set hostname", 1)[1].strip().strip('"')
                        result["system_settings"]["hostname"] = hostname_value
                        safe_log(safe_translate("fortigate.set_hostname", hostname=hostname_value))
                    elif line.lower().startswith("set timezone"):
                        timezone_value = line.split("set timezone", 1)[1].strip().strip('"')
                        result["system_settings"]["timezone"] = timezone_value
                        safe_log(safe_translate("fortigate.set_timezone", timezone=timezone_value))
                    elif line.lower().startswith("set admin-sport"):
                        admin_sport_value = line.split("set admin-sport", 1)[1].strip()
                        result["system_settings"]["admin-sport"] = int(admin_sport_value) if admin_sport_value.isdigit() else 443
                        safe_log(safe_translate("fortigate.set_admin_sport", port=admin_sport_value))
                    elif line.lower().startswith("set admin-ssh-port"):
                        admin_ssh_port_value = line.split("set admin-ssh-port", 1)[1].strip()
                        result["system_settings"]["admin-ssh-port"] = int(admin_ssh_port_value) if admin_ssh_port_value.isdigit() else 22
                        safe_log(safe_translate("fortigate.set_admin_ssh_port", port=admin_ssh_port_value))
                    elif line.lower().startswith("set gui-theme"):
                        gui_theme_value = line.split("set gui-theme", 1)[1].strip().strip('"')
                        result["system_settings"]["gui-theme"] = gui_theme_value
                        safe_log(safe_translate("fortigate.set_gui_theme", theme=gui_theme_value))
                    i += 1
                    continue

            # 处理DNS配置部分
            elif current_section == "dns":
                if line.lower() == "end":
                    current_section = None
                    i += 1
                    continue
                else:
                    # 处理DNS配置属性
                    if line.lower().startswith("set primary"):
                        result["dns_config"]["primary"] = line.split("set primary", 1)[1].strip()
                        log(safe_translate("fortigate.set_dns_primary", server=result["dns_config"]["primary"]))
                    elif line.lower().startswith("set secondary"):
                        result["dns_config"]["secondary"] = line.split("set secondary", 1)[1].strip()
                        log(safe_translate("fortigate.set_dns_secondary", server=result["dns_config"]["secondary"]))
                    elif line.lower().startswith("set tertiary"):
                        result["dns_config"]["tertiary"] = line.split("set tertiary", 1)[1].strip()
                        log(safe_translate("fortigate.set_dns_tertiary", server=result["dns_config"]["tertiary"]))
                    elif line.lower().startswith("set domain"):
                        domains_line = line.split("set domain", 1)[1].strip()
                        domains = re.findall(r'"([^"]*)"', domains_line)
                        if domains:
                            result["dns_config"]["domains"] = domains
                            log(safe_translate("fortigate.set_dns_domains", domains=domains))
                    elif line.lower().startswith("set source-ip"):
                        result["dns_config"]["source_ip"] = line.split("set source-ip", 1)[1].strip()
                        log(safe_translate("fortigate.set_dns_source_ip", ip=result["dns_config"]["source_ip"]))
                    elif line.lower().startswith("set dns-over-tls"):
                        dns_over_tls_value = line.split("set dns-over-tls", 1)[1].strip()
                        result["dns_config"]["dns_over_tls"] = dns_over_tls_value
                        log(safe_translate("fortigate.set_dns_over_tls", value=dns_over_tls_value))
                    elif line.lower().startswith("set protocol"):
                        protocol_value = line.split("set protocol", 1)[1].strip()
                        result["dns_config"]["protocol"] = protocol_value
                        log(safe_translate("fortigate.set_dns_protocol", protocol=protocol_value))
                    elif line.lower().startswith("set dns-proxy"):
                        dns_proxy_value = line.split("set dns-proxy", 1)[1].strip()
                        result["dns_config"]["dns_proxy"] = dns_proxy_value
                        log(safe_translate("fortigate.set_dns_proxy", value=dns_proxy_value))

            # 处理本地用户配置
            elif current_section == "user_local":
                if line.lower().startswith("edit"):
                    user_name = line.split()[1].strip('"')
                    log(safe_translate("fortigate.parsing_local_user", name=user_name))
                    current = {"name": user_name, "type": "local"}
                    i += 1
                    continue
                elif line.lower().startswith("next"):
                    if current:
                        log(safe_translate("fortigate.debug.adding_local_user", name=current.get('name', 'unknown')), "debug")
                        result["auth_config"]["users"].append(current)
                        log(safe_translate("fortigate.local_user_parsing_complete", name=current.get('name', 'unknown')))
                        current = None
                    i += 1
                    continue
                elif line.lower() == "end":
                    current_section = None
                    i += 1
                    continue
                elif current:
                    # 处理本地用户属性
                    if line.lower().startswith("set status"):
                        current["status"] = line.split("set status", 1)[1].strip()
                        log(safe_translate("fortigate.set_user_status", status=current['status']))
                    elif line.lower().startswith("set passwd"):
                        # 密码通常是加密的，或者可能以ENC开头
                        passwd = line.split("set passwd", 1)[1].strip('" ')
                        if passwd.startswith("ENC "):
                            current["password_encrypted"] = True
                            current["password"] = passwd[4:]  # 去掉ENC前缀
                        else:
                            current["password_encrypted"] = False
                            current["password"] = passwd
                        log(safe_translate("fortigate.set_user_password"))
                    elif line.lower().startswith("set two-factor"):
                        current["two_factor"] = line.split("set two-factor", 1)[1].strip()
                        log(safe_translate("fortigate.set_user_two_factor", enabled=current['two_factor']))
                    elif line.lower().startswith("set email-to"):
                        current["email"] = line.split("set email-to", 1)[1].strip('" ')
                        log(safe_translate("fortigate.set_user_email", email=current['email']))
                    elif line.lower().startswith("set sms-phone"):
                        current["sms_phone"] = line.split("set sms-phone", 1)[1].strip('" ')
                        log(safe_translate("fortigate.set_user_sms_phone", phone=current['sms_phone']))
                    elif line.lower().startswith("set type"):
                        current["auth_type"] = line.split("set type", 1)[1].strip()
                        log(safe_translate("fortigate.set_user_auth_type", type=current['auth_type']))
            
            # 处理用户组配置
            elif current_section == "user_group":
                if line.lower().startswith("edit"):
                    group_name = line.split()[1].strip('"')
                    log(safe_translate("fortigate.parsing_user_group", name=group_name))
                    current = {"name": group_name, "members": []}
                    i += 1
                    continue
                elif line.lower().startswith("next"):
                    if current:
                        log(safe_translate("fortigate.debug.adding_user_group", name=current.get('name', 'unknown')), "debug")
                        result["auth_config"]["user_groups"].append(current)
                        log(safe_translate("fortigate.user_group_parsing_complete", name=current.get('name', 'unknown')))
                        current = None
                    i += 1
                    continue
                elif line.lower() == "end":
                    current_section = None
                    i += 1
                    continue
                elif current:
                    # 处理用户组属性
                    if line.lower().startswith("set member"):
                        # 解析member列表
                        member_line = line.split("set member", 1)[1].strip()
                        members = re.findall(r'"([^"]*)"', member_line)
                        if not members:
                            members = member_line.split()
                        current["members"] = members
                        log(safe_translate("fortigate.set_user_group_members", count=len(members)))
                    elif line.lower().startswith("set group-type"):
                        current["group_type"] = line.split("set group-type", 1)[1].strip()
                        log(safe_translate("fortigate.set_user_group_type", type=current['group_type']))
                    elif line.lower().startswith("set authtimeout"):
                        current["auth_timeout"] = line.split("set authtimeout", 1)[1].strip()
                        log(safe_translate("fortigate.set_user_group_auth_timeout", timeout=current['auth_timeout']))
            
            # 处理用户设置配置
            elif current_section == "user_setting":
                if line.lower() == "end":
                    current_section = None
                    i += 1
                    continue
                else:
                    # 处理用户设置属性
                    if line.lower().startswith("set auth-timeout"):
                        result["auth_config"]["auth_settings"]["auth_timeout"] = line.split("set auth-timeout", 1)[1].strip()
                        log(safe_translate("fortigate.set_auth_timeout", timeout=result["auth_config"]["auth_settings"]["auth_timeout"]))
                    elif line.lower().startswith("set auth-cert"):
                        result["auth_config"]["auth_settings"]["auth_cert"] = line.split("set auth-cert", 1)[1].strip('"')
                        log(safe_translate("fortigate.set_auth_cert", cert=result["auth_config"]["auth_settings"]["auth_cert"]))
                    elif line.lower().startswith("set auth-http-basic"):
                        result["auth_config"]["auth_settings"]["auth_http_basic"] = line.split("set auth-http-basic", 1)[1].strip()
                        log(safe_translate("fortigate.set_auth_http_basic", enabled=result["auth_config"]["auth_settings"]["auth_http_basic"]))
                    elif line.lower().startswith("set auth-invalid-max"):
                        result["auth_config"]["auth_settings"]["auth_invalid_max"] = line.split("set auth-invalid-max", 1)[1].strip()
                        log(safe_translate("fortigate.set_auth_invalid_max", max=result["auth_config"]["auth_settings"]["auth_invalid_max"]))
                    elif line.lower().startswith("set auth-lockout-threshold"):
                        result["auth_config"]["auth_settings"]["auth_lockout_threshold"] = line.split("set auth-lockout-threshold", 1)[1].strip()
                        log(safe_translate("fortigate.set_auth_lockout_threshold", threshold=result["auth_config"]["auth_settings"]["auth_lockout_threshold"]))
                    elif line.lower().startswith("set auth-lockout-duration"):
                        result["auth_config"]["auth_settings"]["auth_lockout_duration"] = line.split("set auth-lockout-duration", 1)[1].strip()
                        log(safe_translate("fortigate.set_auth_lockout_duration", duration=result["auth_config"]["auth_settings"]["auth_lockout_duration"]))

            # 处理日志设置配置
            elif current_section == "log_setting":
                if line.lower() == "end":
                    current_section = None
                    i += 1
                    continue
                else:
                    # 处理日志设置属性
                    if line.lower().startswith("set status"):
                        result["log_config"]["settings"]["status"] = line.split("set status", 1)[1].strip()
                        log(safe_translate("fortigate.set_log_status", status=result["log_config"]["settings"]["status"]))
                    elif line.lower().startswith("set resolve-ip"):
                        result["log_config"]["settings"]["resolve_ip"] = line.split("set resolve-ip", 1)[1].strip()
                        log(safe_translate("fortigate.set_log_resolve_ip", resolve=result["log_config"]["settings"]["resolve_ip"]))
                    elif line.lower().startswith("set log-user-in-upper"):
                        result["log_config"]["settings"]["log_user_in_upper"] = line.split("set log-user-in-upper", 1)[1].strip()
                        log(safe_translate("fortigate.set_log_user_in_upper", enabled=result["log_config"]["settings"]["log_user_in_upper"]))
                    elif line.lower().startswith("set fwpolicy-implicit-log"):
                        result["log_config"]["settings"]["fwpolicy_implicit_log"] = line.split("set fwpolicy-implicit-log", 1)[1].strip()
                        log(safe_translate("fortigate.set_fwpolicy_implicit_log", enabled=result["log_config"]["settings"]["fwpolicy_implicit_log"]))
                    elif line.lower().startswith("set log-invalid-packet"):
                        result["log_config"]["settings"]["log_invalid_packet"] = line.split("set log-invalid-packet", 1)[1].strip()
                        log(safe_translate("fortigate.set_log_invalid_packet", enabled=result["log_config"]["settings"]["log_invalid_packet"]))

            # 处理日志Syslog配置
            elif current_section == "log_syslogd":
                if line.lower().startswith("set status"):
                    current = {
                        "type": current_syslogd,
                        "status": line.split("set status", 1)[1].strip()
                    }
                    log(safe_translate("fortigate.set_syslogd_status", id=current_syslogd, status=current["status"]))
                elif line.lower().startswith("set server"):
                    if current:
                        current["server"] = line.split("set server", 1)[1].strip('"')
                        log(safe_translate("fortigate.set_syslogd_server", server=current["server"]))
                elif line.lower().startswith("set port"):
                    if current:
                        current["port"] = line.split("set port", 1)[1].strip()
                        log(safe_translate("fortigate.set_syslogd_port", port=current["port"]))
                elif line.lower().startswith("set facility"):
                    if current:
                        current["facility"] = line.split("set facility", 1)[1].strip()
                        log(safe_translate("fortigate.set_syslogd_facility", facility=current["facility"]))
                elif line.lower().startswith("set format"):
                    if current:
                        current["format"] = line.split("set format", 1)[1].strip()
                        log(safe_translate("fortigate.set_syslogd_format", format=current["format"]))
                elif line.lower().startswith("set mode"):
                    if current:
                        current["mode"] = line.split("set mode", 1)[1].strip()
                        log(safe_translate("fortigate.set_syslogd_mode", mode=current["mode"]))
                elif line.lower().startswith("set filter"):
                    if current:
                        filter_line = line.split("set filter", 1)[1].strip()
                        current["filter"] = filter_line
                        log(safe_translate("fortigate.set_syslogd_filter", filter=current["filter"]))
                elif line.lower() == "end":
                    if current:
                        result["log_config"]["targets"].append(current)
                        log(safe_translate("fortigate.syslogd_parsing_complete", id=current_syslogd))
                        current = None
                    current_section = None
                    i += 1
                    continue

            # 处理日志FortiAnalyzer配置
            elif current_section == "log_fortianalyzer":
                if line.lower().startswith("set status"):
                    current = {
                        "type": current_analyzer,
                        "status": line.split("set status", 1)[1].strip()
                    }
                    log(safe_translate("fortigate.set_fortianalyzer_status", id=current_analyzer, status=current["status"]))
                elif line.lower().startswith("set server"):
                    if current:
                        current["server"] = line.split("set server", 1)[1].strip('"')
                        log(safe_translate("fortigate.set_fortianalyzer_server", server=current["server"]))
                elif line.lower().startswith("set upload-option"):
                    if current:
                        current["upload_option"] = line.split("set upload-option", 1)[1].strip()
                        log(safe_translate("fortigate.set_fortianalyzer_upload_option", option=current["upload_option"]))
                elif line.lower().startswith("set filter"):
                    if current:
                        filter_line = line.split("set filter", 1)[1].strip()
                        current["filter"] = filter_line
                        log(safe_translate("fortigate.set_fortianalyzer_filter", filter=current["filter"]))
                elif line.lower().startswith("set reliable"):
                    if current:
                        current["reliable"] = line.split("set reliable", 1)[1].strip()
                        log(safe_translate("fortigate.set_fortianalyzer_reliable", reliable=current["reliable"]))
                elif line.lower() == "end":
                    if current:
                        result["log_config"]["targets"].append(current)
                        log(safe_translate("fortigate.fortianalyzer_parsing_complete", id=current_analyzer))
                        current = None
                    current_section = None
                    i += 1
                    continue

            # 处理日志过滤器配置
            elif current_section == "log_filter":
                if line.lower().startswith("set severity"):
                    current = {
                        "type": current_filter,
                        "severity": line.split("set severity", 1)[1].strip()
                    }
                    log(safe_translate("fortigate.set_log_filter_severity", filter=current_filter, severity=current["severity"]))
                elif line.lower().startswith("set forward-traffic"):
                    if current:
                        current["forward_traffic"] = line.split("set forward-traffic", 1)[1].strip()
                        log(safe_translate("fortigate.set_log_filter_forward_traffic", enabled=current["forward_traffic"]))
                elif line.lower().startswith("set local-traffic"):
                    if current:
                        current["local_traffic"] = line.split("set local-traffic", 1)[1].strip()
                        log(safe_translate("fortigate.set_log_filter_local_traffic", enabled=current["local_traffic"]))
                elif line.lower().startswith("set multicast-traffic"):
                    if current:
                        current["multicast_traffic"] = line.split("set multicast-traffic", 1)[1].strip()
                        log(safe_translate("fortigate.set_log_filter_multicast_traffic", enabled=current["multicast_traffic"]))
                elif line.lower().startswith("set sniffer-traffic"):
                    if current:
                        current["sniffer_traffic"] = line.split("set sniffer-traffic", 1)[1].strip()
                        log(safe_translate("fortigate.set_log_filter_sniffer_traffic", enabled=current["sniffer_traffic"]))
                elif line.lower().startswith("set anomaly"):
                    if current:
                        current["anomaly"] = line.split("set anomaly", 1)[1].strip()
                        log(safe_translate("fortigate.set_log_filter_anomaly", enabled=current["anomaly"]))
                elif line.lower().startswith("set voip"):
                    if current:
                        current["voip"] = line.split("set voip", 1)[1].strip()
                        log(safe_translate("fortigate.set_log_filter_voip", enabled=current["voip"]))
                elif line.lower().startswith("set dlp-archive"):
                    if current:
                        current["dlp_archive"] = line.split("set dlp-archive", 1)[1].strip()
                        log(safe_translate("fortigate.set_log_filter_dlp_archive", enabled=current["dlp_archive"]))
                elif line.lower().startswith("set event"):
                    if current:
                        current["event"] = line.split("set event", 1)[1].strip()
                        log(safe_translate("fortigate.set_log_filter_event", enabled=current["event"]))
                elif line.lower() == "end":
                    if current:
                        result["log_config"]["filters"].append(current)
                        log(safe_translate("fortigate.log_filter_parsing_complete", filter=current_filter))
                        current = None
                    current_section = None
                    i += 1
                    continue

            # 处理时间对象部分
            elif current_section == "schedule":
                if line.lower().startswith("edit"):
                    schedule_name = line.split()[1].strip('"')
                    safe_log(safe_translate("fortigate.parsing_schedule", name=schedule_name))
                    
                    # 安全检查：验证时间对象名称
                    if len(schedule_name) > 50:
                        warning_msg = safe_translate("fortigate.warning.schedule_name_too_long_msg", name=schedule_name[:10]+"...")
                        safe_log(safe_translate("fortigate.warning.schedule_name_too_long", name=schedule_name[:10]+"..."), "warning")
                        result["warnings"].append(warning_msg)
                        schedule_name = schedule_name[:50]
                    
                    # 验证时间对象名称是否包含危险字符
                    if re.search(r'[;<>&|]', schedule_name):
                        warning_msg = safe_translate("fortigate.warning.schedule_name_suspicious_msg", name=schedule_name)
                        safe_log(safe_translate("fortigate.warning.schedule_name_suspicious", name=schedule_name), "warning")
                        result["warnings"].append(warning_msg)
                        schedule_name = re.sub(r'[;<>&|]', '', schedule_name)
                    
                    current = {
                        "name": schedule_name,
                        "type": current_schedule_type if 'current_schedule_type' in locals() else "recurring",  # 使用配置段确定的类型
                        "weekdays": []
                    }
                    i += 1
                    continue
                elif line.lower().startswith("next"):
                    if current:
                        # 添加到时间对象列表
                        import json                        # 确保所有时间对象都有类型字段
                        if "type" not in current:
                            # 如果没有指定类型，默认为周期性（使用recurring匹配处理器期望）
                            current["type"] = "recurring"
                        
                        # 检查是否只有基本字段（name, uuid, type）而没有实际时间配置
                        basic_fields = {"name", "uuid", "type"}
                        actual_time_fields = set(current.keys()) - basic_fields
                        
                        # 检查是否有实际的时间内容
                        has_time_content = any(k in current and current[k] for k in [
                            "time_start", "time_end", "datetime_start", "datetime_end", 
                            "weekdays", "day", "start", "end", "date_start", "date_end"
                        ])
                        
                        # 特殊检查：如果weekdays或day存在但为空列表，也认为没有内容
                        if "weekdays" in current and not current["weekdays"]:
                            has_time_content = False
                        if "day" in current and not current["day"]:
                            has_time_content = False
                        
                        # 如果只有基本字段而没有实际时间内容，标记为极简对象
                        if not has_time_content or len(actual_time_fields) == 0:
                            current["is_minimal_object"] = True
                            warning_msg = safe_translate("fortigate.warning.schedule_only_uuid_msg", name=current.get('name', ''))
                            safe_log(safe_translate("fortigate.warning.schedule_only_uuid", name=current.get('name', '')), "warning")
                            result["warnings"].append(warning_msg)
                        # 只有在有实际时间内容的情况下才添加默认值
                        elif has_time_content:
                            # 添加默认值
                            if current["type"] == "recurring":
                                # 如果没有起始时间，添加默认值
                                if "time_start" not in current:
                                    current["time_start"] = "00:00:00"
                                    safe_log(safe_translate("fortigate.debug.adding_default_time_start", name=current.get('name'), time=current["time_start"]), "debug")
                                
                                # 如果没有结束时间，添加默认值
                                if "time_end" not in current:
                                    current["time_end"] = "23:59:59"
                                    safe_log(safe_translate("fortigate.debug.adding_default_time_end", name=current.get('name'), time=current["time_end"]), "debug")
                                
                                # 如果没有指定星期几，添加默认所有星期
                                if not current["weekdays"]:
                                    current["weekdays"] = ["sun", "mon", "tue", "wed", "thu", "fri", "sat"]
                                    safe_log(safe_translate("fortigate.debug.adding_default_weekdays", name=current.get('name')), "debug")
                            elif current["type"] == "onetime":
                                # 对于onetime类型，如果缺少时间，警告但尝试添加默认值
                                import datetime
                                today = datetime.date.today().strftime("%Y-%m-%d")
                                
                                if "datetime_start" not in current:
                                    current["datetime_start"] = f"{today} 00:00"
                                    safe_log(safe_translate("fortigate.debug.adding_default_datetime_start", name=current.get('name'), datetime=current["datetime_start"]), "debug")
                                
                                if "datetime_end" not in current:
                                    current["datetime_end"] = f"{today} 23:59"
                                    safe_log(safe_translate("fortigate.debug.adding_default_datetime_end", name=current.get('name'), datetime=current["datetime_end"]), "debug")
                        
                        # 检查时间对象是否有内容（时间或星期几）
                        has_time = ("time_start" in current and current["time_start"]) or \
                                  ("time_end" in current and current["time_end"]) or \
                                  ("datetime_start" in current and current["datetime_start"]) or \
                                  ("datetime_end" in current and current["datetime_end"])
                        
                        has_weekdays = "weekdays" in current and current["weekdays"] and len(current["weekdays"]) > 0
                        
                        # 如果时间对象没有时间设置也没有星期几设置，发出警告
                        if (not has_time and not has_weekdays):
                            warning_msg = safe_translate("fortigate.warning.schedule_empty_msg", name=current.get('name', ''))
                            safe_log(safe_translate("fortigate.warning.schedule_empty", name=current.get('name', '')), "warning")
                            result["warnings"].append(warning_msg)
                        
                        attrs_json = json.dumps(current, ensure_ascii=False)
                        safe_log(safe_translate("fortigate.debug.adding_schedule", name=current.get('name', 'unknown'), attrs=attrs_json), "debug")
                        
                        result["time_ranges"].append(current)
                        safe_log(safe_translate("fortigate.schedule_parsing_complete", name=current.get('name', 'unknown')))
                        current = None
                    i += 1
                    continue
                elif current:
                    # 处理时间对象的属性设置
                    if line.lower().startswith("set onetime"):
                        current["type"] = "onetime"
                        safe_log(safe_translate("fortigate.debug.setting_schedule_type", name=current.get('name'), type="onetime"), "debug")
                    elif line.lower().startswith("set start-date"):
                        current["date_start"] = line.split()[2].strip()
                        safe_log(safe_translate("fortigate.debug.setting_schedule_start_date", name=current.get('name'), date=current["date_start"]), "debug")
                    elif line.lower().startswith("set end-date"):
                        current["date_end"] = line.split()[2].strip()
                        safe_log(safe_translate("fortigate.debug.setting_schedule_end_date", name=current.get('name'), date=current["date_end"]), "debug")
                    elif line.lower().startswith("set start"):
                        # 获取完整的start值（可能包含日期和时间）
                        parts = line.split()
                        if len(parts) >= 4:
                            # 格式为 "set start YYYY-MM-DD HH:MM"
                            start_value = f"{parts[2]} {parts[3]}"
                            current["datetime_start"] = start_value
                            safe_log(safe_translate("fortigate.debug.setting_schedule_datetime_start", name=current.get('name'), datetime=current["datetime_start"]), "debug")
                        elif len(parts) >= 3:
                            # 格式为 "set start HH:MM" 或 "set start YYYY-MM-DD"
                            start_value = parts[2].strip()
                            if ":" in start_value:
                                # 时间格式
                                current["time_start"] = f"{start_value}:00"  # 添加秒
                                safe_log(safe_translate("fortigate.debug.setting_schedule_time_start", name=current.get('name'), time=current["time_start"]), "debug")
                            else:
                                # 可能是日期格式，但缺少时间
                                current["date_start"] = start_value
                                safe_log(safe_translate("fortigate.debug.setting_schedule_start_date", name=current.get('name'), date=current["date_start"]), "debug")
                    elif line.lower().startswith("set end"):
                        # 获取完整的end值（可能包含日期和时间）
                        parts = line.split()
                        if len(parts) >= 4:
                            # 格式为 "set end YYYY-MM-DD HH:MM"
                            end_value = f"{parts[2]} {parts[3]}"
                            current["datetime_end"] = end_value
                            safe_log(safe_translate("fortigate.debug.setting_schedule_datetime_end", name=current.get('name'), datetime=current["datetime_end"]), "debug")
                        elif len(parts) >= 3:
                            # 格式为 "set end HH:MM" 或 "set end YYYY-MM-DD"
                            end_value = parts[2].strip()
                            if ":" in end_value:
                                # 时间格式
                                current["time_end"] = f"{end_value}:00"  # 添加秒（修复：使用:00而不是:59）
                                safe_log(safe_translate("fortigate.debug.setting_schedule_time_end", name=current.get('name'), time=current["time_end"]), "debug")
                            else:
                                # 可能是日期格式，但缺少时间
                                current["date_end"] = end_value
                                safe_log(safe_translate("fortigate.debug.setting_schedule_end_date", name=current.get('name'), date=current["date_end"]), "debug")
                            safe_log(safe_translate("fortigate.debug.setting_schedule_time_end", name=current.get('name'), time=current["time_end"]), "debug")
                    elif line.lower().startswith("set day"):
                        # 处理星期几设置，格式可能是 "set day monday tuesday wednesday"
                        days_parts = line.split()[2:]
                        weekdays = []
                        
                        # 映射全名到三字符缩写
                        day_mapping = {
                            "sunday": "sun", "monday": "mon", "tuesday": "tue", 
                            "wednesday": "wed", "thursday": "thu", 
                            "friday": "fri", "saturday": "sat",
                            "sun": "sun", "mon": "mon", "tue": "tue",
                            "wed": "wed", "thu": "thu", "fri": "fri", "sat": "sat"
                        }
                        
                        for day in days_parts:
                            day = day.lower().strip()
                            if day in day_mapping:
                                weekdays.append(day_mapping[day])
                        
                        current["weekdays"] = weekdays
                        safe_log(safe_translate("fortigate.debug.setting_schedule_weekdays", name=current.get('name'), weekdays=",".join(weekdays)), "debug")
                    elif line.lower().startswith("set member"):
                        # 处理schedule group的成员设置
                        member_line = line.split("set member", 1)[1].strip()
                        # 提取引号包围的成员名称
                        import re
                        members = re.findall(r'"([^"]*)"', member_line)
                        if not members:  # 如果没有匹配到引号包围的成员，尝试按空格分割
                            members = member_line.split()
                        current["members"] = members
                        current["type"] = "group"  # 标记为组类型
                        safe_log(safe_translate("fortigate.debug.setting_schedule_members", name=current.get('name'), members=",".join(members)), "debug")
                    elif line.lower().startswith("set comment"):
                        # 处理注释
                        comment = line.split("set comment", 1)[1].strip('" ')
                        current["comment"] = comment
                        safe_log(safe_translate("fortigate.debug.setting_schedule_comment", name=current.get('name'), comment=comment), "debug")
                    i += 1
                    continue

            # 通用的 end 处理逻辑（放在所有特定配置部分处理逻辑之后）
            if line.lower() == "end" and current:
                # 正在处理子块的 end
                i += 1
                continue
            elif line.lower() == "end":
                # 部分配置结束
                log(safe_translate("fortigate.section_end", section=current_section if current_section else safe_translate("fortigate.unknown_section")))
                current_section = None
                i += 1
                continue

            # 通用的 edit 处理逻辑（放在所有特定配置部分处理逻辑之后）
            elif line.lower().startswith("edit "):
                edit_name = line.split("edit", 1)[1].strip().strip('"')
                config_stack.append(f"edit_{edit_name}")
                safe_log(safe_translate("fortigate.debug.processing_edit", name=edit_name), "debug")
                i += 1
                continue

            # 通用的 next 处理逻辑（放在所有特定配置部分处理逻辑之后）
            elif line.lower() == "next":
                if config_stack and config_stack[-1].startswith("edit_"):
                    ended_edit = config_stack.pop()
                    safe_log(safe_translate("fortigate.debug.ending_edit", name=ended_edit), "debug")
                i += 1
                continue

            i += 1
            
            # 安全检查：检测可能的无限循环
            if safety_counter >= max_iterations:
                error_msg = safe_translate("error.parsing_loop_detected")
                log(error_msg, "error")
                user_log(error_msg, "error")
                result["warnings"].append("检测到可能的解析循环，解析提前终止")
                break
        
        # DNS服务器数量限制处理（NTOS最多支持3个DNS服务器）
        if result["dns_config"]:
            dns_servers = []
            # 收集所有DNS服务器
            if "primary" in result["dns_config"] and result["dns_config"]["primary"]:
                dns_servers.append(result["dns_config"]["primary"])
            if "secondary" in result["dns_config"] and result["dns_config"]["secondary"]:
                dns_servers.append(result["dns_config"]["secondary"])

            # 检查DNS服务器数量限制
            if len(dns_servers) > 3:
                warning_msg = safe_translate("fortigate.warning.dns_servers_exceed_limit",
                                           count=len(dns_servers), limit=3)
                safe_log(warning_msg, "warning")
                result["warnings"].append(f"DNS服务器数量({len(dns_servers)})超过NTOS限制(3个)，只转换前3个")

                # 只保留前3个DNS服务器
                if len(dns_servers) > 0:
                    result["dns_config"]["primary"] = dns_servers[0]
                if len(dns_servers) > 1:
                    result["dns_config"]["secondary"] = dns_servers[1]
                # 如果有第三个服务器，需要添加到dns_config中
                if len(dns_servers) > 2:
                    result["dns_config"]["tertiary"] = dns_servers[2]

            # 记录DNS配置处理结果
            safe_log(safe_translate("fortigate.dns_config_processed",
                                   servers=len(dns_servers),
                                   dns_over_tls=result["dns_config"].get("dns_over_tls", "未设置")))

        # 安全检查：统计解析结果
        total_items = (len(result['interfaces']) + len(result['static_routes']) +
                      len(result['zones']) + len(result['address_objects']) +
                      len(result['address_groups']) + len(result['service_objects']) +
                      len(result['service_groups']) + len(result['policies']) +
                      len(result['nat_rules']) + len(result['vpn_configs']['ipsec']))

        if total_items == 0:
            warning_msg = "未解析到任何配置项"
            log(safe_translate("fortigate.warning.no_items_parsed"), "warning")
            result["warnings"].append(warning_msg)

        if total_items > 10000:
            warning_msg = f"解析到的配置项数量异常（{total_items}个）"
            log(safe_translate("fortigate.warning.too_many_items", count=total_items), "warning")
            result["warnings"].append(warning_msg)
        
        safe_log(safe_translate("fortigate.parsing_complete", interfaces=len(result['interfaces']), routes=len(result['static_routes']), 
                                           zones=len(result['zones']), addresses=len(result['address_objects']), 
                                           addrgroups=len(result['address_groups']), services=len(result['service_objects']),
                                           servicegroups=len(result['service_groups']), policies=len(result['policies']),
                                           nat_rules=len(result['nat_rules']), ipsec_tunnels=len(result['vpn_configs']['ipsec']),
                                           users=len(result['auth_config']['users']), user_groups=len(result['auth_config']['user_groups']),
                                           log_filters=len(result['log_config']['filters']), log_targets=len(result['log_config']['targets']),
                                           ipv6_routes=len(result['static_routes_ipv6'])))
        safe_user_log(safe_translate("fortigate.parsing_complete_user", interfaces=len(result['interfaces']), routes=len(result['static_routes']), 
                                                     zones=len(result['zones']), addresses=len(result['address_objects']), 
                                                     addrgroups=len(result['address_groups']), services=len(result['service_objects']),
                                                     servicegroups=len(result['service_groups']), policies=len(result['policies']),
                                                     nat_rules=len(result['nat_rules']), ipsec_tunnels=len(result['vpn_configs']['ipsec']),
                                                     users=len(result['auth_config']['users']), user_groups=len(result['auth_config']['user_groups']),
                                                     log_filters=len(result['log_config']['filters']), log_targets=len(result['log_config']['targets']),
                                                     ipv6_routes=len(result['static_routes_ipv6'])))
        
        # 添加接口解析结果日志
        safe_log(safe_translate("fortigate.parsing_result_summary", interfaces=len(result['interfaces']), routes=len(result['static_routes']),
              zones=len(result['zones']), addresses=len(result['address_objects']), addrgroups=len(result['address_groups']),
              services=len(result['service_objects']), servicegroups=len(result['service_groups']),
              policies=len(result['policies']), nat_rules=len(result['nat_rules'])), "debug")
        if len(result['interfaces']) > 0:
            safe_log(safe_translate("fortigate.first_interface", name=result['interfaces'][0].get('raw_name', 'unknown')), "debug")
        else:
            safe_log(safe_translate("fortigate.no_interfaces_found"), "warning")

        # 应用PPPoE过滤和密码掩码处理
        try:
            # 尝试导入接口处理工具函数
            from engine.utils.interface_utils import process_interfaces_with_pppoe_filtering_and_masking

            # 处理接口数据
            original_interfaces = result['interfaces']
            processed_interfaces, filtered_pppoe_count = process_interfaces_with_pppoe_filtering_and_masking(original_interfaces)

            # 更新结果
            result['interfaces'] = processed_interfaces
            result['filtered_pppoe_count'] = filtered_pppoe_count

            # 记录处理结果
            if filtered_pppoe_count > 0:
                safe_log(safe_translate("fortigate.pppoe_interfaces_filtered", count=filtered_pppoe_count), "info")
                safe_user_log(safe_translate("fortigate.pppoe_interfaces_filtered_user", count=filtered_pppoe_count), "info")

            safe_log(safe_translate("fortigate.interface_processing_complete",
                                  original=len(original_interfaces),
                                  processed=len(processed_interfaces),
                                  filtered=filtered_pppoe_count), "debug")
        except ImportError:
            # 如果无法导入工具函数，记录警告但继续执行
            safe_log("无法导入接口处理工具函数，跳过PPPoE过滤和密码掩码处理", "warning")
        except Exception as e:
            # 如果处理过程中出现错误，记录错误但继续执行
            safe_log(f"接口处理过程中出现错误: {str(e)}", "error")

        return result
    
    except Exception as e:
        # 记录错误信息
        print(f"解析配置文件时出错: {str(e)}")
        try:
            error_msg = safe_translate("fortigate.parsing_error", error=str(e))
            safe_log(error_msg, "error")
            safe_user_log(safe_translate("fortigate.parsing_error_user"), "error")
        except:
            # 如果翻译函数不可用，使用默认消息
            error_msg = f"解析配置文件失败: {str(e)}"
            print(f"[ERROR] {error_msg}")
            print("[ERROR] 解析配置文件失败")
        return {"error": error_msg, "interfaces": [], "static_routes": [], "zones": [], "address_objects": [], 
                "address_groups": [], "service_objects": [], "service_groups": [], "policies": [], "nat_rules": [], 
                "vpn_configs": {"ipsec": [], "ssl_vpn": {}}}

def parse_ipv6_block(lines, start_idx):
    ipv6_data = {}
    i = start_idx
    while i < len(lines):
        line = lines[i].strip()
        if line.lower().startswith("set ip6-address"):
            ipv6_data["ip6-address"] = line.split("set ip6-address", 1)[1].strip()
        elif line.lower().startswith("set ip6-mode"):
            ipv6_data["ip6-mode"] = line.split("set ip6-mode", 1)[1].strip()
            log(safe_translate("fortigate.set_ipv6_mode", mode=ipv6_data['ip6-mode']))
        elif line.lower().startswith("set ip6-allowaccess"):
            access = line.split("set ip6-allowaccess", 1)[1].strip()
            ipv6_data["allowaccess"] = [p for p in access.split() if p in NTOS_SUPPORTED_ACCESS]
            log(safe_translate("fortigate.set_ipv6_allowaccess", access=ipv6_data['allowaccess']))
        elif line.lower() == "end":
            return ipv6_data, i
        i += 1
    return ipv6_data, i

def parse_secondaryip_block(lines, start_idx):
    ips = []
    i = start_idx
    current_ip = None
    while i < len(lines):
        line = lines[i].strip()
        if line.lower().startswith("edit"):
            current_ip = {}
        elif line.lower().startswith("set ip"):
            parts = line.split()
            ip = parts[2]
            mask = parts[3]
            prefix = mask_to_prefix(mask)
            current_ip = f"{ip}/{prefix}"
        elif line.lower() == "next" and current_ip:
            ips.append(current_ip)
            current_ip = None
        elif line.lower() == "end":
            return ips, i
        i += 1
    return ips, i

def mask_to_prefix(mask: str) -> int:
    try:
        parts = map(int, mask.split('.'))
        binary = ''.join(f'{part:08b}' for part in parts)
        return binary.count('1')
    except Exception:
        return 24  # fallback

def parse_dhcp_ip_range(lines, start_idx):
    """解析DHCP IP范围配置"""
    ip_range = {}
    i = start_idx
    while i < len(lines):
        line = lines[i].strip()
        if line.lower().startswith("edit"):
            range_id = line.split()[1].strip('"')
            ip_range["id"] = range_id
            log(safe_translate("fortigate.parsing_dhcp_ip_range", id=range_id))
        elif line.lower().startswith("set start-ip"):
            ip_range["start_ip"] = line.split("set start-ip", 1)[1].strip()
            log(safe_translate("fortigate.set_dhcp_start_ip", ip=ip_range["start_ip"]))
        elif line.lower().startswith("set end-ip"):
            ip_range["end_ip"] = line.split("set end-ip", 1)[1].strip()
            log(safe_translate("fortigate.set_dhcp_end_ip", ip=ip_range["end_ip"]))
        elif line.lower() == "next":
            i += 1
            continue
        elif line.lower() == "end":
            return ip_range, i
        i += 1
    return ip_range, i

def parse_dhcp_options(lines, start_idx):
    """解析DHCP选项配置"""
    options = []
    current_option = None
    i = start_idx
    while i < len(lines):
        line = lines[i].strip()
        if line.lower().startswith("edit"):
            option_id = line.split()[1].strip('"')
            current_option = {"id": option_id}
            log(safe_translate("fortigate.parsing_dhcp_option", id=option_id))
        elif line.lower().startswith("set code"):
            current_option["code"] = line.split("set code", 1)[1].strip()
            log(safe_translate("fortigate.set_dhcp_option_code", code=current_option["code"]))
        elif line.lower().startswith("set type"):
            current_option["type"] = line.split("set type", 1)[1].strip()
            log(safe_translate("fortigate.set_dhcp_option_type", type=current_option["type"]))
        elif line.lower().startswith("set value"):
            current_option["value"] = line.split("set value", 1)[1].strip('"')
            log(safe_translate("fortigate.set_dhcp_option_value", value=current_option["value"]))
        elif line.lower() == "next":
            if current_option:
                options.append(current_option)
                current_option = None
            i += 1
            continue
        elif line.lower() == "end":
            if current_option:
                options.append(current_option)
            return options, i
        i += 1
    return options, i
