#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
FortiGate配置预处理器
专门处理FortiOS 7.6版本的复杂配置，过滤不必要的配置段落以提高解析性能
"""

import os
import re
import logging
from typing import List, Dict, Tuple, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class FortigatePreprocessor:
    """FortiGate配置预处理器"""
    
    def __init__(self):
        """初始化预处理器"""
        self.fortios_version = None
        self.hostname = None
        self.filtered_sections = []
        self.statistics = {
            'original_lines': 0,
            'filtered_lines': 0,
            'removed_sections': 0,
            'processing_time': 0
        }
        
        # 需要过滤的配置段落（FortiOS 7.6特有的复杂配置）
        self.filter_patterns = [
            r'^config switch-controller.*',
            r'^config wireless-controller.*',
            r'^config system automation-.*',
            r'^config system fabric.*',
            r'^config system sdn-.*',
            r'^config system security-rating.*',
            r'^config system auto-auth.*',
            r'^config system fabric-object.*',
            r'^config log.*threat-weight.*',
            r'^config log.*custom-field.*',
            r'^config application.*custom.*',
            r'^config dlp.*fp-doc-source.*',
            r'^config webfilter.*override.*',
            r'^config antivirus.*heuristic.*'
        ]
        
        # 保留的核心配置段落
        self.core_patterns = [
            r'^config system global.*',
            r'^config system interface.*',
            r'^config firewall address.*',
            r'^config firewall addrgrp.*',
            r'^config firewall service.*',
            r'^config firewall policy.*',
            r'^config firewall vip.*',
            r'^config firewall ippool.*',
            r'^config system zone.*',
            r'^config router static.*',
            r'^config system dns.*',
            r'^config firewall schedule.*'
        ]

    def detect_fortios_version(self, config_content: str) -> Optional[str]:
        """检测FortiOS版本"""
        try:
            # 查找版本信息
            version_patterns = [
                r'#buildno=(\d+)',  # 通过build号判断
                r'#config-version=([^:]+):([^:]+):([^-]+)-([^-]+)',
                r'set version "([^"]+)"',
                r'FortiGate-\w+\s+v([0-9]+\.[0-9]+\.[0-9]+)'
            ]

            # 首先通过build号判断版本
            build_match = re.search(r'#buildno=(\d+)', config_content)
            if build_match:
                build_no = int(build_match.group(1))
                if build_no >= 3000:  # FortiOS 7.6+ 的build号通常大于3000
                    logger.info(f"检测到build号 {build_no}，推断为FortiOS 7.6+")
                    self.fortios_version = "7.6+"
                    return "7.6+"

            # 通过其他模式检测
            for pattern in version_patterns[1:]:  # 跳过build号模式
                match = re.search(pattern, config_content)
                if match:
                    if len(match.groups()) >= 3:
                        # 格式: FGT_100F-v7-build3510-FORTINET
                        version = f"{match.group(2)}.{match.group(3)}"
                    else:
                        version = match.group(1)

                    logger.info(f"检测到FortiOS版本: {version}")
                    self.fortios_version = version
                    return version

            # 如果没有找到明确版本，通过配置特征判断
            if 'config switch-controller' in config_content:
                logger.info("检测到switch-controller配置，推断为FortiOS 7.6+")
                self.fortios_version = "7.6+"
                return "7.6+"

            logger.warning("无法检测FortiOS版本，使用默认处理")
            return None

        except Exception as e:
            logger.error(f"版本检测失败: {str(e)}")
            return None

    def detect_hostname(self, config_content: str) -> Optional[str]:
        """检测设备主机名"""
        try:
            hostname_pattern = r'set hostname\s+"?([^"\s]+)"?'
            match = re.search(hostname_pattern, config_content)
            if match:
                hostname = match.group(1)
                logger.info(f"检测到设备主机名: {hostname}")
                self.hostname = hostname
                return hostname
            return None
        except Exception as e:
            logger.error(f"主机名检测失败: {str(e)}")
            return None

    def should_filter_section(self, section_header: str) -> bool:
        """判断是否应该过滤某个配置段落"""
        # 首先检查是否是核心配置
        for core_pattern in self.core_patterns:
            if re.match(core_pattern, section_header.strip()):
                return False
        
        # 检查是否需要过滤
        for filter_pattern in self.filter_patterns:
            if re.match(filter_pattern, section_header.strip()):
                logger.debug(f"过滤配置段落: {section_header.strip()}")
                return True
        
        return False

    def analyze_config_complexity(self, config_content: str) -> Dict:
        """分析配置复杂度"""
        complexity_metrics = {
            'total_lines': len(config_content.splitlines()),
            'config_sections': len(re.findall(r'^config\s+', config_content, re.MULTILINE)),
            'edit_entries': len(re.findall(r'^\s*edit\s+', config_content, re.MULTILINE)),
            'switch_controller_sections': len(re.findall(r'^config\s+switch-controller', config_content, re.MULTILINE)),
            'wireless_controller_sections': len(re.findall(r'^config\s+wireless-controller', config_content, re.MULTILINE)),
            'automation_sections': len(re.findall(r'^config\s+system\s+automation', config_content, re.MULTILINE)),
            'complex_regex_lines': len(re.findall(r'set.*regex|set.*\\\\|set.*\(\?\:', config_content, re.MULTILINE))
        }

        # 计算复杂度评分
        complexity_score = (
            complexity_metrics['config_sections'] * 1 +
            complexity_metrics['edit_entries'] * 2 +
            complexity_metrics['switch_controller_sections'] * 20 +
            complexity_metrics['wireless_controller_sections'] * 10 +
            complexity_metrics['automation_sections'] * 15 +
            complexity_metrics['complex_regex_lines'] * 5
        )

        complexity_metrics['complexity_score'] = complexity_score
        complexity_metrics['needs_preprocessing'] = (
            complexity_score > 1000 or  # 高复杂度评分
            complexity_metrics['switch_controller_sections'] > 5 or  # 大量switch-controller配置
            complexity_metrics['edit_entries'] > 500  # 大量edit条目
        )

        return complexity_metrics

    def preprocess_config(self, config_file_path: str, output_file_path: Optional[str] = None) -> str:
        """
        预处理配置文件

        Args:
            config_file_path: 原始配置文件路径
            output_file_path: 输出文件路径，如果为None则自动生成

        Returns:
            预处理后的配置文件路径
        """
        start_time = datetime.now()

        try:
            # 读取原始配置文件
            logger.info(f"开始预处理配置文件: {config_file_path}")

            with open(config_file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            self.statistics['original_lines'] = len(lines)

            # 检测版本和主机名
            config_content = ''.join(lines)
            self.detect_fortios_version(config_content)
            self.detect_hostname(config_content)

            # 分析配置复杂度
            complexity = self.analyze_config_complexity(config_content)
            logger.info(f"配置复杂度分析: 评分={complexity['complexity_score']}, "
                       f"switch-controller段落={complexity['switch_controller_sections']}, "
                       f"edit条目={complexity['edit_entries']}")

            # 如果配置不复杂，直接返回原文件
            if not complexity['needs_preprocessing']:
                logger.info("配置复杂度较低，跳过预处理")
                return config_file_path
            
            # 预处理配置
            processed_lines = self._filter_config_lines(lines)
            
            # 生成输出文件路径
            if output_file_path is None:
                base_name = os.path.splitext(config_file_path)[0]
                output_file_path = f"{base_name}_preprocessed.conf"
            
            # 写入预处理后的配置
            with open(output_file_path, 'w', encoding='utf-8') as f:
                f.writelines(processed_lines)
            
            # 更新统计信息
            self.statistics['filtered_lines'] = len(processed_lines)
            self.statistics['processing_time'] = (datetime.now() - start_time).total_seconds()
            
            # 记录处理结果
            reduction_rate = (1 - len(processed_lines) / len(lines)) * 100
            logger.info(f"预处理完成:")
            logger.info(f"  原始行数: {len(lines)}")
            logger.info(f"  处理后行数: {len(processed_lines)}")
            logger.info(f"  减少比例: {reduction_rate:.1f}%")
            logger.info(f"  过滤段落数: {self.statistics['removed_sections']}")
            logger.info(f"  处理耗时: {self.statistics['processing_time']:.3f}秒")
            logger.info(f"  输出文件: {output_file_path}")
            
            return output_file_path
            
        except Exception as e:
            logger.error(f"配置预处理失败: {str(e)}")
            raise

    def _filter_config_lines(self, lines: List[str]) -> List[str]:
        """过滤配置行"""
        processed_lines = []
        i = 0

        while i < len(lines):
            line = lines[i]
            stripped_line = line.strip()

            # 检测配置段落开始
            if stripped_line.startswith('config '):
                # 找到段落的结束位置
                section_start = i
                section_end = self._find_section_end(lines, i)

                # 判断是否需要过滤这个段落
                if self.should_filter_section(stripped_line):
                    # 过滤掉这个段落
                    self.filtered_sections.append(stripped_line)
                    self.statistics['removed_sections'] += 1
                    logger.debug(f"过滤段落: {stripped_line}")
                    i = section_end + 1  # 跳过整个段落
                else:
                    # 保留这个段落
                    for j in range(section_start, section_end + 1):
                        processed_lines.append(lines[j])
                    i = section_end + 1
            else:
                # 不是配置段落的行，直接保留
                processed_lines.append(line)
                i += 1

        return processed_lines

    def _find_section_end(self, lines: List[str], start_index: int) -> int:
        """找到配置段落的结束位置"""
        nesting_level = 0
        i = start_index

        while i < len(lines):
            stripped_line = lines[i].strip()

            if stripped_line.startswith('config '):
                nesting_level += 1
            elif stripped_line == 'end':
                nesting_level -= 1
                if nesting_level == 0:
                    return i  # 找到段落结束

            i += 1

        # 如果没有找到匹配的end，返回文件末尾
        return len(lines) - 1

    def get_statistics(self) -> Dict:
        """获取处理统计信息"""
        return {
            'fortios_version': self.fortios_version,
            'hostname': self.hostname,
            'original_lines': self.statistics['original_lines'],
            'filtered_lines': self.statistics['filtered_lines'],
            'removed_sections': self.statistics['removed_sections'],
            'filtered_sections': self.filtered_sections,
            'processing_time': self.statistics['processing_time'],
            'reduction_rate': (1 - self.statistics['filtered_lines'] / max(self.statistics['original_lines'], 1)) * 100
        }

    def generate_report(self, output_path: Optional[str] = None) -> str:
        """生成预处理报告"""
        stats = self.get_statistics()
        
        report = f"""
# FortiGate配置预处理报告

## 基本信息
- **设备主机名**: {stats['hostname'] or '未检测到'}
- **FortiOS版本**: {stats['fortios_version'] or '未检测到'}
- **处理时间**: {stats['processing_time']:.3f}秒

## 处理统计
- **原始行数**: {stats['original_lines']:,}
- **处理后行数**: {stats['filtered_lines']:,}
- **减少行数**: {stats['original_lines'] - stats['filtered_lines']:,}
- **减少比例**: {stats['reduction_rate']:.1f}%
- **过滤段落数**: {stats['removed_sections']}

## 过滤的配置段落
"""
        
        for i, section in enumerate(stats['filtered_sections'], 1):
            report += f"{i}. {section}\n"
        
        if output_path:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(report)
            logger.info(f"预处理报告已生成: {output_path}")
        
        return report
