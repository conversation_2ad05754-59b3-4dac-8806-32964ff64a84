#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
解析器工具类
提供通用的配置解析功能
"""

import re

class ParserUtils:
    """解析器工具类"""
    
    @staticmethod
    def extract_quoted_values(line):
        """
        从配置行中提取引号包围的值
        
        Args:
            line (str): 配置行，例如 set member "addr1" "addr2"
            
        Returns:
            list: 提取的值列表
        """
        return re.findall(r'"([^"]*)"', line)
    
    @staticmethod
    def extract_value_after_keyword(line, keyword):
        """
        从配置行中提取关键字后的值
        
        Args:
            line (str): 配置行，例如 set ip *********** *************
            keyword (str): 关键字，例如 "set ip"
            
        Returns:
            str: 关键字后的值
        """
        return line.split(keyword, 1)[1].strip()
    
    @staticmethod
    def extract_quoted_string(line, keyword):
        """
        从配置行中提取关键字后的引号包围的字符串
        
        Args:
            line (str): 配置行，例如 set description "This is a description"
            keyword (str): 关键字，例如 "set description"
            
        Returns:
            str: 提取的字符串（不含引号）
        """
        value = line.split(keyword, 1)[1].strip()
        if value.startswith('"') and value.endswith('"'):
            return value[1:-1]
        return value
    
    @staticmethod
    def parse_list_values(line, keyword):
        """
        从配置行中解析列表值
        
        Args:
            line (str): 配置行，例如 set member "addr1" "addr2" "addr3"
            keyword (str): 关键字，例如 "set member"
            
        Returns:
            list: 值列表
        """
        values_str = line.split(keyword, 1)[1].strip()
        quoted_values = re.findall(r'"([^"]*)"', values_str)
        if quoted_values:
            return quoted_values
        return values_str.split()
    
    @staticmethod
    def is_section_start(line, section_name):
        """
        检查行是否为配置段开始
        
        Args:
            line (str): 配置行
            section_name (str): 段名，例如 "system interface"
            
        Returns:
            bool: 是否是段开始
        """
        return line.lower().startswith(f"config {section_name}")
    
    @staticmethod
    def is_edit_line(line):
        """
        检查行是否为编辑行
        
        Args:
            line (str): 配置行
            
        Returns:
            bool: 是否是编辑行
        """
        return line.lower().startswith("edit ")
    
    @staticmethod
    def get_edit_name(line):
        """
        从编辑行中获取名称
        
        Args:
            line (str): 配置行，例如 edit "port1"
            
        Returns:
            str: 编辑名称
        """
        parts = line.split(None, 1)
        if len(parts) < 2:
            return ""
        name = parts[1].strip()
        if name.startswith('"') and name.endswith('"'):
            return name[1:-1]
        return name
    
    @staticmethod
    def is_next_line(line):
        """
        检查行是否为next行
        
        Args:
            line (str): 配置行
            
        Returns:
            bool: 是否是next行
        """
        return line.lower() == "next"
    
    @staticmethod
    def is_end_line(line):
        """
        检查行是否为end行
        
        Args:
            line (str): 配置行
            
        Returns:
            bool: 是否是end行
        """
        return line.lower() == "end"
    
    @staticmethod
    def is_set_line(line, attribute=None):
        """
        检查行是否为set行
        
        Args:
            line (str): 配置行
            attribute (str, optional): 属性名
            
        Returns:
            bool: 是否是set行
        """
        if attribute:
            return line.lower().startswith(f"set {attribute}")
        return line.lower().startswith("set ")
    
    @staticmethod
    def mask_to_prefix(mask):
        """
        将点分掩码转换为前缀长度
        
        Args:
            mask (str): 点分掩码，例如 *************
            
        Returns:
            int: 前缀长度
        """
        try:
            parts = mask.split('.')
            if len(parts) != 4:
                return 24  # 默认返回24
            
            # 转换为二进制并计算1的个数
            binary = ''.join([bin(int(p))[2:].zfill(8) for p in parts])
            return binary.count('1')
        except (ValueError, AttributeError):
            return 24  # 默认返回24 