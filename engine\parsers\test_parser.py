#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
飞塔解析器测试脚本
用于测试和验证飞塔配置解析器的功能
"""

import os
import sys
import json
import argparse

# 添加上层目录到路径中，使得可以导入engine模块
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from engine.parsers.fortigate_parser import FortigateParser

def test_parser(config_file, output_file=None, sections=None):
    """
    测试解析器功能
    
    Args:
        config_file (str): 飞塔配置文件路径
        output_file (str, optional): 输出JSON文件路径
        sections (list, optional): 要解析的配置部分
    
    Returns:
        dict: 解析结果
    """
    parser = FortigateParser()
    print(f"开始解析飞塔配置文件: {config_file}")
    
    # 全部解析
    if not sections:
        result = parser.parse(config_file)
        print(f"解析完成，共解析到:")
        print(f"- {len(result.get('interfaces', []))} 个接口")
        print(f"- {len(result.get('static_routes', []))} 条静态路由")
        print(f"- {len(result.get('zones', []))} 个安全区域")
        print(f"- {len(result.get('address_objects', []))} 个地址对象")
        print(f"- {len(result.get('address_groups', []))} 个地址组")
        print(f"- {len(result.get('service_objects', []))} 个服务对象")
        print(f"- {len(result.get('service_groups', []))} 个服务组")
        print(f"- {len(result.get('policies', []))} 条策略规则")
        print(f"- {len(result.get('nat_rules', []))} 条NAT规则")
        print(f"- {len(result.get('vpn_configs', {}).get('ipsec', []))} 个IPsec VPN隧道")
        if 'ssl_vpn' in result.get('vpn_configs', {}) and 'portals' in result.get('vpn_configs', {}).get('ssl_vpn', {}):
            print(f"- {len(result.get('vpn_configs', {}).get('ssl_vpn', {}).get('portals', []))} 个SSL VPN门户")
        print(f"- {len(result.get('dhcp_configs', []))} 个DHCP服务器配置")
        if result.get('dns_config'):
            print(f"- DNS配置已解析")
        print(f"- {len(result.get('auth_config', {}).get('users', []))} 个本地用户")
        print(f"- {len(result.get('auth_config', {}).get('user_groups', []))} 个用户组")
        print(f"- {len(result.get('log_config', {}).get('filters', []))} 个日志过滤器")
        print(f"- {len(result.get('log_config', {}).get('targets', []))} 个日志目标")
    else:
        # 只解析指定部分
        result = {}
        if "interfaces" in sections:
            interfaces = parser.extract_interfaces(config_file)
            result["interfaces"] = interfaces
            print(f"解析到 {len(interfaces)} 个接口")
        
        if "static_routes" in sections:
            routes = parser.extract_static_routes(config_file)
            result["static_routes"] = routes
            print(f"解析到 {len(routes)} 条静态路由")
        
        if "policies" in sections:
            policies = parser.extract_policies(config_file)
            result["policies"] = policies
            print(f"解析到 {len(policies)} 条策略规则")
        
        if "nat_rules" in sections:
            nat_rules = parser.extract_nat_rules(config_file)
            result["nat_rules"] = nat_rules
            print(f"解析到 {len(nat_rules)} 条NAT规则")
        
        if "vpn_configs" in sections:
            vpn_configs = parser.extract_vpn_configs(config_file)
            result["vpn_configs"] = vpn_configs
            print(f"解析到 {len(vpn_configs.get('ipsec', []))} 个IPsec VPN隧道")
            if 'ssl_vpn' in vpn_configs and 'portals' in vpn_configs.get('ssl_vpn', {}):
                print(f"解析到 {len(vpn_configs.get('ssl_vpn', {}).get('portals', []))} 个SSL VPN门户")
        
        if "dhcp_configs" in sections:
            dhcp_configs = parser.extract_dhcp_configs(config_file)
            result["dhcp_configs"] = dhcp_configs
            print(f"解析到 {len(dhcp_configs)} 个DHCP服务器配置")
        
        if "dns_config" in sections:
            dns_config = parser.extract_dns_configs(config_file)
            result["dns_config"] = dns_config
            print(f"DNS配置已解析")
            if "primary" in dns_config:
                print(f"- 主DNS服务器: {dns_config['primary']}")
            if "secondary" in dns_config:
                print(f"- 辅DNS服务器: {dns_config['secondary']}")
        
        if "auth_config" in sections:
            auth_config = parser.extract_auth_configs(config_file)
            result["auth_config"] = auth_config
            print(f"解析到 {len(auth_config.get('users', []))} 个本地用户")
            print(f"解析到 {len(auth_config.get('user_groups', []))} 个用户组")
        
        if "log_config" in sections:
            log_config = parser.extract_log_configs(config_file)
            result["log_config"] = log_config
            print(f"解析到 {len(log_config.get('filters', []))} 个日志过滤器")
            print(f"解析到 {len(log_config.get('targets', []))} 个日志目标")
    
    # 输出到JSON文件
    if output_file:
        print(f"正在保存解析结果到: {output_file}")
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        print(f"解析结果已保存")
    
    return result

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='飞塔配置解析器测试')
    parser.add_argument('--config', required=True, help='飞塔配置文件路径')
    parser.add_argument('--output', help='解析结果输出JSON文件路径')
    parser.add_argument('--sections', nargs='+', 
                      choices=['interfaces', 'static_routes', 'policies', 'nat_rules', 'vpn_configs', 
                               'dhcp_configs', 'dns_config', 'auth_config', 'log_config'],
                      help='要解析的配置部分')
    
    args = parser.parse_args()
    
    test_parser(args.config, args.output, args.sections)

if __name__ == "__main__":
    main() 