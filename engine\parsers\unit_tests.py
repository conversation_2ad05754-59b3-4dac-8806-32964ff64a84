#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
飞塔解析器单元测试
用于验证解析器的基本功能和健壮性
"""

import os
import sys
import unittest
import tempfile
import json
from datetime import datetime
from pathlib import Path

# 添加上层目录到路径中，使得可以导入engine模块
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from engine.parsers.fortigate_parser import FortigateParser, parse_fortigate_cli
from engine.parsers.parser_utils import ParserUtils

class TestParserUtils(unittest.TestCase):
    """测试解析器工具类"""
    
    def test_extract_quoted_values(self):
        """测试提取引号包围的值"""
        line = 'set member "addr1" "addr2" "addr3"'
        result = ParserUtils.extract_quoted_values(line)
        self.assertEqual(result, ['addr1', 'addr2', 'addr3'])
    
    def test_extract_value_after_keyword(self):
        """测试提取关键字后的值"""
        line = 'set ip *********** *************'
        result = ParserUtils.extract_value_after_keyword(line, 'set ip')
        self.assertEqual(result, '*********** *************')
    
    def test_mask_to_prefix(self):
        """测试掩码转换为前缀长度"""
        result = ParserUtils.mask_to_prefix('*************')
        self.assertEqual(result, 24)
        
        result = ParserUtils.mask_to_prefix('***************')
        self.assertEqual(result, 32)
        
        result = ParserUtils.mask_to_prefix('***********')
        self.assertEqual(result, 16)
        
        # 测试异常情况
        result = ParserUtils.mask_to_prefix('invalid')
        self.assertEqual(result, 24)  # 默认返回24
    
    def test_is_section_start(self):
        """测试区段开始检测"""
        self.assertTrue(ParserUtils.is_section_start('config system interface', 'system interface'))
        self.assertFalse(ParserUtils.is_section_start('set interface port1', 'system interface'))
    
    def test_is_edit_line(self):
        """测试编辑行检测"""
        self.assertTrue(ParserUtils.is_edit_line('edit port1'))
        self.assertFalse(ParserUtils.is_edit_line('set ip *********** *************'))
    
    def test_get_edit_name(self):
        """测试获取编辑名称"""
        self.assertEqual(ParserUtils.get_edit_name('edit port1'), 'port1')
        self.assertEqual(ParserUtils.get_edit_name('edit "LAN Interface"'), 'LAN Interface')
        self.assertEqual(ParserUtils.get_edit_name('edit'), '')

class TestFortigateParser(unittest.TestCase):
    """测试飞塔解析器"""
    
    def setUp(self):
        """测试前准备"""
        self.parser = FortigateParser()
        
        # 创建临时配置文件
        self.temp_file = tempfile.NamedTemporaryFile(delete=False)
        self.temp_file.write(b"""
config system interface
    edit "port1"
        set vdom "root"
        set ip *********** *************
        set allowaccess ping https ssh
        set description "WAN Interface"
        set status up
    next
    edit "port2"
        set vdom "root"
        set ip ******** *************
        set allowaccess ping
        set description "LAN Interface"
        set status up
    next
end
config router static
    edit 1
        set dst 0.0.0.0 0.0.0.0
        set gateway *************
        set device "port1"
    next
end
config firewall policy
    edit 1
        set name "Internet Access"
        set srcintf "port2"
        set dstintf "port1"
        set srcaddr "all"
        set dstaddr "all"
        set action accept
        set schedule "always"
        set service "ALL"
        set nat enable
    next
end
config system dhcp server
    edit 1
        set default-gateway ********
        set netmask *************
        set interface "port2"
        config ip-range
            edit 1
                set start-ip ********00
                set end-ip **********
            next
        end
    next
end
config system dns
    set primary *******
    set secondary *******
end
config user local
    edit "admin"
        set status enable
        set passwd ENC XXX
    next
end
config log setting
    set status enable
    set resolve-ip enable
end
        """)
        self.temp_file.close()
    
    def tearDown(self):
        """测试后清理"""
        os.unlink(self.temp_file.name)
    
    def test_parse(self):
        """测试完整解析"""
        result = self.parser.parse(self.temp_file.name)
        
        # 检查是否包含所有配置部分
        self.assertIn("interfaces", result)
        self.assertIn("static_routes", result)
        self.assertIn("policies", result)
        self.assertIn("dhcp_configs", result)
        self.assertIn("dns_config", result)
        self.assertIn("auth_config", result)
        self.assertIn("log_config", result)
        
        # 检查解析结果
        self.assertEqual(len(result["interfaces"]), 2)
        self.assertEqual(len(result["static_routes"]), 1)
        self.assertEqual(len(result["policies"]), 1)
        self.assertEqual(len(result["dhcp_configs"]), 1)
        self.assertTrue(isinstance(result["dns_config"], dict))
        self.assertEqual(len(result["auth_config"]["users"]), 1)
        self.assertTrue(isinstance(result["log_config"]["settings"], dict))
    
    def test_extract_interfaces(self):
        """测试提取接口配置"""
        interfaces = self.parser.extract_interfaces(self.temp_file.name)
        self.assertEqual(len(interfaces), 2)
        
        # 检查第一个接口
        port1 = interfaces[0]
        self.assertEqual(port1["raw_name"], "port1")
        self.assertEqual(port1["ip"], "***********/24")
        self.assertEqual(port1["description"], "WAN Interface")
        self.assertEqual(port1["status"], "up")
        self.assertIn("ping", port1["allowaccess"])
        self.assertIn("https", port1["allowaccess"])
        self.assertIn("ssh", port1["allowaccess"])
        
        # 检查第二个接口
        port2 = interfaces[1]
        self.assertEqual(port2["raw_name"], "port2")
        self.assertEqual(port2["ip"], "********/24")
        self.assertEqual(port2["description"], "LAN Interface")
        self.assertEqual(port2["status"], "up")
        self.assertIn("ping", port2["allowaccess"])
        self.assertNotIn("https", port2["allowaccess"])
        self.assertNotIn("ssh", port2["allowaccess"])
    
    def test_extract_static_routes(self):
        """测试提取静态路由配置"""
        routes = self.parser.extract_static_routes(self.temp_file.name)
        self.assertEqual(len(routes), 1)
        
        # 检查路由
        route = routes[0]
        self.assertEqual(route["id"], "1")
        self.assertEqual(route["destination"], "0.0.0.0/0")
        self.assertEqual(route["gateway"], "*************")
        self.assertEqual(route["device"], "port1")
    
    def test_extract_policies(self):
        """测试提取策略规则配置"""
        policies = self.parser.extract_policies(self.temp_file.name)
        self.assertEqual(len(policies), 1)
        
        # 检查策略
        policy = policies[0]
        self.assertEqual(policy["policyid"], "1")
        self.assertEqual(policy["name"], "Internet Access")
        self.assertEqual(policy["srcintf"], ["port2"])
        self.assertEqual(policy["dstintf"], ["port1"])
        self.assertEqual(policy["srcaddr"], ["all"])
        self.assertEqual(policy["dstaddr"], ["all"])
        self.assertEqual(policy["action"], "accept")
        self.assertEqual(policy["nat"], "enable")
    
    def test_extract_dhcp_configs(self):
        """测试提取DHCP配置"""
        dhcp_configs = self.parser.extract_dhcp_configs(self.temp_file.name)
        self.assertEqual(len(dhcp_configs), 1)
        
        # 检查DHCP配置
        dhcp = dhcp_configs[0]
        self.assertEqual(dhcp["id"], "1")
        self.assertEqual(dhcp["default_gateway"], "********")
        self.assertEqual(dhcp["netmask"], "*************")
        self.assertEqual(dhcp["interface"], "port2")
        self.assertEqual(len(dhcp["ip_ranges"]), 1)
        self.assertEqual(dhcp["ip_ranges"][0]["start_ip"], "********00")
        self.assertEqual(dhcp["ip_ranges"][0]["end_ip"], "**********")
    
    def test_extract_dns_configs(self):
        """测试提取DNS配置"""
        dns_config = self.parser.extract_dns_configs(self.temp_file.name)
        self.assertEqual(dns_config["primary"], "*******")
        self.assertEqual(dns_config["secondary"], "*******")
    
    def test_extract_auth_configs(self):
        """测试提取认证配置"""
        auth_config = self.parser.extract_auth_configs(self.temp_file.name)
        self.assertEqual(len(auth_config["users"]), 1)
        
        # 检查用户
        user = auth_config["users"][0]
        self.assertEqual(user["name"], "admin")
        self.assertEqual(user["status"], "enable")
        self.assertTrue(user["password_encrypted"])
    
    def test_extract_log_configs(self):
        """测试提取日志配置"""
        log_config = self.parser.extract_log_configs(self.temp_file.name)
        self.assertEqual(log_config["settings"]["status"], "enable")
        self.assertEqual(log_config["settings"]["resolve_ip"], "enable")
    
    def test_error_handling(self):
        """测试错误处理"""
        # 测试不存在的文件
        result = self.parser.parse("non_existent_file.conf")
        self.assertIn("error", result)

class FortigateParserSecurityTest(unittest.TestCase):
    """测试FortigateParser的安全校验机制"""
    
    def setUp(self):
        """测试前的准备工作"""
        self.parser = FortigateParser()
        self.temp_dir = tempfile.TemporaryDirectory()
        self.test_files = []
    
    def tearDown(self):
        """测试后的清理工作"""
        for file_path in self.test_files:
            if os.path.exists(file_path):
                try:
                    os.remove(file_path)
                except:
                    pass
        self.temp_dir.cleanup()
    
    def create_test_file(self, content, filename=None):
        """创建测试文件并返回其路径"""
        if filename is None:
            filename = f"test_config_{datetime.now().strftime('%Y%m%d%H%M%S')}.conf"
        file_path = os.path.join(self.temp_dir.name, filename)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        self.test_files.append(file_path)
        return file_path
    
    def test_file_existence_check(self):
        """测试文件存在性检查"""
        # 测试不存在的文件
        non_existent_file = os.path.join(self.temp_dir.name, "non_existent.conf")
        result = self.parser.parse_fortigate_cli(non_existent_file)
        self.assertIn("errors", result)
        self.assertTrue(any("不存在" in error for error in result["errors"]))
    
    def test_file_size_check(self):
        """测试文件大小检查"""
        # 创建一个空文件
        empty_file = self.create_test_file("", "empty.conf")
        result = self.parser.parse_fortigate_cli(empty_file)
        self.assertIn("errors", result)
        self.assertTrue(any("为空" in error for error in result["errors"]))
        
        # 创建一个过大的文件（超过10MB）
        # 注意：这里我们只模拟检查，不实际创建大文件
        large_content = "# 这是一个大文件测试\n" * 500000  # 大约10MB
        large_file = self.create_test_file(large_content, "large.conf")
        result = self.parser.parse_fortigate_cli(large_file)
        self.assertIn("warnings", result)
        self.assertTrue(any("过大" in warning for warning in result["warnings"]))
    
    def test_file_extension_check(self):
        """测试文件扩展名检查"""
        # 使用可疑扩展名
        suspicious_file = self.create_test_file("config system global\nend", "test.exe")
        result = self.parser.parse_fortigate_cli(suspicious_file)
        self.assertIn("warnings", result)
        self.assertTrue(any("可疑的文件扩展名" in warning for warning in result["warnings"]))
    
    def test_sensitive_command_check(self):
        """测试敏感命令检查"""
        # 包含敏感命令的配置
        sensitive_content = """
        config system global
        end
        execute format disk
        config firewall policy
        end
        """
        sensitive_file = self.create_test_file(sensitive_content)
        result = self.parser.parse_fortigate_cli(sensitive_file)
        self.assertIn("warnings", result)
        self.assertTrue(any("敏感命令" in warning for warning in result["warnings"]))
    
    def test_config_section_balance_check(self):
        """测试配置段平衡检查"""
        # 不平衡的配置（缺少end）
        unbalanced_content = """
        config system global
        set hostname "test"
        config system interface
        edit "wan1"
        set vdom "root"
        next
        end
        """
        unbalanced_file = self.create_test_file(unbalanced_content)
        result = self.parser.parse_fortigate_cli(unbalanced_file)
        self.assertIn("warnings", result)
        self.assertTrue(any("配置段不平衡" in warning for warning in result["warnings"]))
    
    def test_sanitize_string(self):
        """测试字符串清理功能"""
        # 测试基本清理
        original = "test<script>alert(1)</script>"
        sanitized = self.parser.sanitize_string(original)
        self.assertNotIn("<script>", sanitized)
        
        # 测试长度限制
        long_string = "a" * 300
        sanitized = self.parser.sanitize_string(long_string)
        self.assertTrue(len(sanitized) <= 255)
    
    def test_validate_ip_address(self):
        """测试IP地址验证功能"""
        # 有效IP
        self.assertTrue(self.parser.validate_ip_address("***********"))
        
        # 无效IP
        self.assertFalse(self.parser.validate_ip_address("192.168.1.256"))
        self.assertFalse(self.parser.validate_ip_address("192.168.1"))
        self.assertFalse(self.parser.validate_ip_address("not_an_ip"))
    
    def test_validate_cidr(self):
        """测试CIDR验证功能"""
        # 有效CIDR
        self.assertTrue(self.parser.validate_cidr("***********/24"))
        
        # 无效CIDR
        self.assertFalse(self.parser.validate_cidr("***********/33"))
        self.assertFalse(self.parser.validate_cidr("***********/-1"))
        self.assertFalse(self.parser.validate_cidr("192.168.1.256/24"))
        self.assertFalse(self.parser.validate_cidr("not_a_cidr"))
    
    def test_address_object_security_checks(self):
        """测试地址对象安全检查"""
        # 包含可疑字符的地址对象
        address_content = """
        config firewall address
        edit "test<script>alert(1)</script>"
        set type ipmask
        set subnet *********** *************
        next
        end
        """
        address_file = self.create_test_file(address_content)
        result = self.parser.parse_fortigate_cli(address_file)
        self.assertIn("warnings", result)
        self.assertTrue(any("可疑字符" in warning for warning in result["warnings"]))
    
    def test_policy_security_checks(self):
        """测试策略安全检查"""
        # 策略ID非数字
        policy_content = """
        config firewall policy
        edit "abc"
        set srcintf "wan1"
        set dstintf "lan"
        set srcaddr "all"
        set dstaddr "all"
        set action accept
        set schedule "always"
        set service "ALL"
        next
        end
        """
        policy_file = self.create_test_file(policy_content)
        result = self.parser.parse_fortigate_cli(policy_file)
        self.assertIn("warnings", result)
        self.assertTrue(any("策略ID" in warning and "不是有效的数字" in warning for warning in result["warnings"]))
    
    def test_command_injection_check(self):
        """测试命令注入检查"""
        # 包含命令注入的配置
        injection_content = """
        config firewall address
        edit "test"
        set comment "test; rm -rf /; test"
        next
        end
        """
        injection_file = self.create_test_file(injection_content)
        result = self.parser.parse_fortigate_cli(injection_file)
        self.assertIn("warnings", result)
        # 检查注释是否被净化
        addresses = result.get("addresses", [])
        if addresses:
            comment = addresses[0].get("comment", "")
            self.assertNotIn(";", comment)
            self.assertNotIn("rm", comment)
    
    def test_large_list_truncation(self):
        """测试大列表截断功能"""
        # 创建包含大量成员的地址组
        members = ' '.join([f'"member{i}"' for i in range(600)])
        addrgrp_content = f"""
        config firewall addrgrp
        edit "large_group"
        set member {members}
        next
        end
        """
        addrgrp_file = self.create_test_file(addrgrp_content)
        result = self.parser.parse_fortigate_cli(addrgrp_file)
        self.assertIn("warnings", result)
        self.assertTrue(any("成员数量异常大" in warning for warning in result["warnings"]))
        
        # 验证成员列表是否被截断
        addrgrps = result.get("addrgrps", [])
        if addrgrps:
            members = addrgrps[0].get("member", [])
            self.assertTrue(len(members) <= 500)
    
    def test_invalid_format_detection(self):
        """测试无效格式检测"""
        # 创建非Fortigate配置格式的文件
        invalid_content = """
        This is not a valid Fortigate configuration file.
        It does not start with 'config' and contains no valid sections.
        """
        invalid_file = self.create_test_file(invalid_content)
        result = self.parser.parse_fortigate_cli(invalid_file)
        self.assertIn("warnings", result)
        self.assertTrue(any("格式可能不正确" in warning for warning in result["warnings"]))
    
    def test_validate_config_file(self):
        """测试配置文件验证功能"""
        # 有效的配置文件
        valid_content = """
        config system global
        set hostname "test-firewall"
        end
        config firewall policy
        edit 1
        set srcintf "wan1"
        set dstintf "lan"
        set srcaddr "all"
        set dstaddr "all"
        set action accept
        set schedule "always"
        set service "ALL"
        next
        end
        """
        valid_file = self.create_test_file(valid_content)
        validation_result = self.parser.validate_config_file(valid_file)
        self.assertTrue(validation_result["valid"])
        self.assertEqual(len(validation_result["errors"]), 0)

if __name__ == "__main__":
    unittest.main() 