# -*- coding: utf-8 -*-
"""
生成器接口 - 定义标准化的生成器接口
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union
from lxml import etree
from engine.utils.logger import log
from engine.utils.i18n import _


class GeneratorInterface(ABC):
    """
    生成器接口基类
    定义XML生成器的标准接口
    """
    
    def __init__(self, generator_type: str, target_format: str = "xml"):
        """
        初始化生成器
        
        Args:
            generator_type: 生成器类型（如 'security-policy', 'nat', 'interface'）
            target_format: 目标格式（默认为 'xml'）
        """
        self.generator_type = generator_type
        self.target_format = target_format
        self.generator_version = "1.0"
        
        log(_("generator_interface.initialized"), "info", type=generator_type)
    
    @abstractmethod
    def generate_xml_element(self, data: Dict[str, Any], 
                           parent_element: Optional[etree.Element] = None) -> etree.Element:
        """
        生成XML元素
        
        Args:
            data: 数据字典
            parent_element: 父元素（可选）
            
        Returns:
            etree.Element: 生成的XML元素
        """
        pass
    
    @abstractmethod
    def generate_xml_string(self, data: Union[Dict[str, Any], List[Dict[str, Any]]], 
                           pretty_print: bool = True) -> str:
        """
        生成XML字符串
        
        Args:
            data: 数据字典或数据列表
            pretty_print: 是否格式化输出
            
        Returns:
            str: XML字符串
        """
        pass
    
    def validate_input_data(self, data: Dict[str, Any]) -> bool:
        """
        验证输入数据 - 默认实现
        
        Args:
            data: 输入数据
            
        Returns:
            bool: 数据是否有效
        """
        if not isinstance(data, dict):
            log(_("generator_interface.invalid_data_type"), "error", 
                type=type(data).__name__, expected="dict")
            return False
        
        return True
    
    def get_namespace_uri(self) -> Optional[str]:
        """
        获取命名空间URI - 子类可以重写
        
        Returns:
            Optional[str]: 命名空间URI
        """
        namespace_map = {
            "security-policy": "urn:ruijie:ntos:params:xml:ns:yang:security-policy",
            "nat": "urn:ruijie:ntos:params:xml:ns:yang:nat",
            "interface": "urn:ruijie:ntos:params:xml:ns:yang:interface",
            "system": "urn:ruijie:ntos:params:xml:ns:yang:system"
        }
        
        return namespace_map.get(self.generator_type)
    
    def create_root_element(self, tag_name: str, namespace_uri: Optional[str] = None) -> etree.Element:
        """
        创建根元素
        
        Args:
            tag_name: 标签名
            namespace_uri: 命名空间URI
            
        Returns:
            etree.Element: 根元素
        """
        if namespace_uri:
            # 创建带命名空间的元素
            root = etree.Element(tag_name, xmlns=namespace_uri)
        else:
            root = etree.Element(tag_name)
        
        return root
    
    def add_text_element(self, parent: etree.Element, tag_name: str, 
                        text_value: str, namespace_uri: Optional[str] = None):
        """
        添加文本元素
        
        Args:
            parent: 父元素
            tag_name: 标签名
            text_value: 文本值
            namespace_uri: 命名空间URI
        """
        if namespace_uri:
            element = etree.SubElement(parent, tag_name, xmlns=namespace_uri)
        else:
            element = etree.SubElement(parent, tag_name)
        
        element.text = str(text_value)
    
    def add_list_elements(self, parent: etree.Element, tag_name: str, 
                         values: List[str], namespace_uri: Optional[str] = None):
        """
        添加列表元素
        
        Args:
            parent: 父元素
            tag_name: 标签名
            values: 值列表
            namespace_uri: 命名空间URI
        """
        for value in values:
            self.add_text_element(parent, tag_name, value, namespace_uri)
    
    def get_generator_info(self) -> Dict[str, Any]:
        """
        获取生成器信息
        
        Returns: Dict[str, Any]: 生成器信息
        """
        return {
            "generator_type": self.generator_type,
            "target_format": self.target_format,
            "generator_version": self.generator_version,
            "generator_class": self.__class__.__name__,
            "namespace_uri": self.get_namespace_uri(),
            "capabilities": {
                "xml_element_generation": True,
                "xml_string_generation": True,
                "namespace_support": self.get_namespace_uri() is not None,
                "validation": True
            }
        }
    
    def format_xml_output(self, element: etree.Element, pretty_print: bool = True) -> str:
        """
        格式化XML输出
        
        Args:
            element: XML元素
            pretty_print: 是否格式化
            
        Returns:
            str: 格式化的XML字符串
        """
        return etree.tostring(
            element,
            encoding='unicode',
            pretty_print=pretty_print,
            xml_declaration=False
        )
