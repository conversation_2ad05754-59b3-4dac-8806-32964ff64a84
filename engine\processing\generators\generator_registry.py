# -*- coding: utf-8 -*-
"""
生成器注册表 - 管理和发现生成器组件
"""

from typing import Dict, Optional, List, Type
from engine.utils.logger import log
from engine.utils.i18n import _
from .generator_interface import GeneratorInterface


class GeneratorRegistry:
    """
    生成器注册表
    负责管理和发现生成器组件
    """
    
    def __init__(self):
        """初始化生成器注册表"""
        self._generators: Dict[str, Type[GeneratorInterface]] = {}
        self._generator_instances: Dict[str, GeneratorInterface] = {}
        
        # 自动注册内置生成器
        self._register_builtin_generators()
        
        log(_("generator_registry.initialized"), "info")
    
    def _register_builtin_generators(self):
        """注册内置生成器"""
        try:
            # 注册安全策略生成器适配器
            from .security_policy_generator_adapter import SecurityPolicyGeneratorAdapter
            self.register_generator("security-policy", SecurityPolicyGeneratorAdapter)
            
            # 注册NAT生成器适配器
            from .nat_generator_adapter import NatGeneratorAdapter
            self.register_generator("nat", NatGeneratorAdapter)
            
            # 注册XML生成器适配器
            from .xml_generator_adapter import XmlGeneratorAdapter
            self.register_generator("xml", XmlGeneratorAdapter)
            
        except ImportError as e:
            error_msg = _("generator_registry.builtin_registration_failed", error=str(e))
            log(error_msg, "warning")
    
    def register_generator(self, generator_type: str, generator_class: Type[GeneratorInterface]):
        """
        注册生成器
        
        Args:
            generator_type: 生成器类型
            generator_class: 生成器类
            
        Raises:
            ValueError: 如果生成器类无效
        """
        if not issubclass(generator_class, GeneratorInterface):
            raise ValueError(_("generator_registry.invalid_generator_class",
                             class_name=generator_class.__name__))
        
        self._generators[generator_type] = generator_class
        
        # 清除缓存的实例
        if generator_type in self._generator_instances:
            del self._generator_instances[generator_type]
        
        log(_("generator_registry.generator_registered"), "info",
            type=generator_type, class_name=generator_class.__name__)
    
    def get_generator(self, generator_type: str) -> Optional[GeneratorInterface]:
        """
        获取生成器实例
        
        Args:
            generator_type: 生成器类型
            
        Returns:
            Optional[GeneratorInterface]: 生成器实例，如果不存在则返回None
        """
        # 检查缓存
        if generator_type in self._generator_instances:
            return self._generator_instances[generator_type]
        
        # 检查是否注册了该类型的生成器
        if generator_type not in self._generators:
            log(_("generator_registry.generator_not_found"), "error", type=generator_type)
            return None
        
        try:
            # 创建生成器实例
            generator_class = self._generators[generator_type]
            generator_instance = generator_class()
            
            # 缓存实例
            self._generator_instances[generator_type] = generator_instance
            
            log(_("generator_registry.generator_created"), "info", type=generator_type)
            return generator_instance
            
        except Exception as e:
            error_msg = _("generator_registry.generator_creation_failed",
                         type=generator_type, error=str(e))
            log(error_msg, "error")
            return None
    
    def is_generator_supported(self, generator_type: str) -> bool:
        """
        检查生成器类型是否支持
        
        Args:
            generator_type: 生成器类型
            
        Returns:
            bool: 是否支持
        """
        return generator_type in self._generators
    
    def get_supported_generators(self) -> List[str]:
        """
        获取支持的生成器类型列表
        
        Returns: List[str]: 支持的生成器类型列表
        """
        return list(self._generators.keys())
    
    def get_generator_info(self, generator_type: str) -> Optional[Dict[str, any]]:
        """
        获取生成器信息
        
        Args:
            generator_type: 生成器类型
            
        Returns:
            Optional[Dict[str, any]]: 生成器信息
        """
        generator = self.get_generator(generator_type)
        if generator:
            return generator.get_generator_info()
        return None
    
    def clear_cache(self):
        """清空生成器实例缓存"""
        self._generator_instances.clear()
        log(_("generator_registry.cache_cleared"), "info")
    
    def get_registry_stats(self) -> Dict[str, any]:
        """
        获取注册表统计信息
        
        Returns: Dict[str, any]: 统计信息
        """
        return {
            "registered_generators": len(self._generators),
            "cached_instances": len(self._generator_instances),
            "supported_types": self.get_supported_generators(),
            "generator_classes": {gen_type: cls.__name__ for gen_type, cls in self._generators.items()}
        }
