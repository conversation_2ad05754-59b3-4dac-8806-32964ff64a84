# -*- coding: utf-8 -*-
"""
NAT生成器适配器 - 将现有NAT生成器适配为标准接口
"""

from typing import Dict, Any, List, Union, Optional
from lxml import etree
from engine.utils.logger import log
from engine.utils.i18n import _
from .generator_interface import GeneratorInterface


class NatGeneratorAdapter(GeneratorInterface):
    """
    NAT生成器适配器
    将现有的NAT生成逻辑包装为标准接口
    """
    
    def __init__(self):
        """初始化NAT生成器适配器"""
        super().__init__(
            generator_type="nat",
            target_format="xml"
        )
    
    def generate_xml_element(self, data: Dict[str, Any], 
                           parent_element: Optional[etree.Element] = None) -> etree.Element:
        """
        生成NAT XML元素
        
        Args:
            data: NAT数据
            parent_element: 父元素（可选）
            
        Returns:
            etree.Element: 生成的XML元素
        """
        if not self.validate_input_data(data):
            raise ValueError(_("nat_generator_adapter.invalid_input_data"))
        
        # 创建NAT元素
        if parent_element is not None:
            nat_element = etree.SubElement(parent_element, "nat")
        else:
            nat_element = etree.Element("nat")
        
        return nat_element
    
    def generate_xml_string(self, data: Union[Dict[str, Any], List[Dict[str, Any]]], 
                           pretty_print: bool = True) -> str:
        """
        生成NAT XML字符串
        
        Args:
            data: NAT数据或数据列表
            pretty_print: 是否格式化输出
            
        Returns:
            str: XML字符串
        """
        # 创建根元素
        namespace_uri = self.get_namespace_uri()
        root = self.create_root_element("nat", namespace_uri)
        
        return self.format_xml_output(root, pretty_print)
