# -*- coding: utf-8 -*-
"""
安全策略生成器适配器 - 将现有安全策略生成器适配为标准接口
保持与现有生成逻辑的完全兼容性
"""

from typing import Dict, Any, List, Union, Optional
from lxml import etree
from engine.utils.logger import log
from engine.utils.i18n import _
from .generator_interface import GeneratorInterface


class SecurityPolicyGeneratorAdapter(GeneratorInterface):
    """
    安全策略生成器适配器
    将现有的安全策略生成逻辑包装为标准接口
    """
    
    def __init__(self):
        """初始化安全策略生成器适配器"""
        super().__init__(
            generator_type="security-policy",
            target_format="xml"
        )
        
        # 延迟导入现有生成器
        self._legacy_generator = None
    
    def _get_legacy_generator(self):
        """获取现有生成器实例"""
        if self._legacy_generator is None:
            try:
                # 尝试导入现有的安全策略生成器
                from engine.generators.security_policy_generator import SecurityPolicyGenerator
                self._legacy_generator = SecurityPolicyGenerator()
                log(_("security_policy_generator_adapter.legacy_generator_loaded"), "debug")
            except ImportError:
                # 如果没有专门的安全策略生成器，使用通用方法
                log(_("security_policy_generator_adapter.no_legacy_generator"), "info")
                self._legacy_generator = None
        
        return self._legacy_generator
    
    def generate_xml_element(self, data: Dict[str, Any], 
                           parent_element: Optional[etree.Element] = None) -> etree.Element:
        """
        生成安全策略XML元素
        
        Args:
            data: 安全策略数据
            parent_element: 父元素（可选）
            
        Returns:
            etree.Element: 生成的XML元素
        """
        if not self.validate_input_data(data):
            raise ValueError(_("security_policy_generator_adapter.invalid_input_data"))
        
        try:
            # 尝试使用现有生成器
            legacy_generator = self._get_legacy_generator()
            if legacy_generator and hasattr(legacy_generator, 'generate_policy_xml_element'):
                return legacy_generator.generate_policy_xml_element(data, parent_element)
            
            # 如果没有现有生成器，使用内置逻辑
            return self._generate_policy_element_builtin(data, parent_element)
            
        except Exception as e:
            error_msg = _("security_policy_generator_adapter.element_generation_failed", error=str(e))
            log(error_msg, "error")
            raise
    
    def generate_xml_string(self, data: Union[Dict[str, Any], List[Dict[str, Any]]], 
                           pretty_print: bool = True) -> str:
        """
        生成安全策略XML字符串
        
        Args:
            data: 安全策略数据或数据列表
            pretty_print: 是否格式化输出
            
        Returns:
            str: XML字符串
        """
        try:
            # 创建根元素
            namespace_uri = self.get_namespace_uri()
            root = self.create_root_element("security-policy", namespace_uri)
            
            # 处理数据
            if isinstance(data, dict):
                data_list = [data]
            elif isinstance(data, list):
                data_list = data
            else:
                raise ValueError(_("security_policy_generator_adapter.invalid_data_format"))
            
            # 生成策略元素
            for policy_data in data_list:
                policy_element = self.generate_xml_element(policy_data, root)
                if policy_element.getparent() is None:
                    root.append(policy_element)
            
            return self.format_xml_output(root, pretty_print)
            
        except Exception as e:
            error_msg = _("security_policy_generator_adapter.string_generation_failed", error=str(e))
            log(error_msg, "error")
            raise
    
    def _generate_policy_element_builtin(self, data: Dict[str, Any], 
                                       parent_element: Optional[etree.Element] = None) -> etree.Element:
        """
        内置的策略元素生成逻辑
        
        Args:
            data: 策略数据
            parent_element: 父元素
            
        Returns:
            etree.Element: 策略元素
        """
        # 创建策略元素
        if parent_element is not None:
            policy_element = etree.SubElement(parent_element, "policy")
        else:
            policy_element = etree.Element("policy")
        
        # 按照YANG模型顺序添加字段

        # 1. name (必需)
        self.add_text_element(policy_element, "name", data.get("name", "unnamed_policy"))

        # 2. enabled (必需)
        self.add_text_element(policy_element, "enabled", data.get("enabled", "true"))

        # 3. description (可选，但要在group-name之前)
        if "description" in data and data["description"]:
            # 清理description字段以符合YANG约束
            from engine.utils.name_validator import clean_ntos_description
            cleaned_description = clean_ntos_description(data["description"])
            self.add_text_element(policy_element, "description", cleaned_description)

        # 4. group-name (必需)
        self.add_text_element(policy_element, "group-name", data.get("group-name", "default"))
        
        # 添加源和目标信息
        self._add_source_destination_elements(policy_element, data)
        
        # 添加服务信息
        self._add_service_elements(policy_element, data)

        # 按照YANG模型顺序添加剩余的必需字段

        # 5. action (必需)
        self.add_text_element(policy_element, "action", data.get("action", "permit"))

        # 6. config-source (必需)
        self.add_text_element(policy_element, "config-source", data.get("config-source", "fortigate"))

        # 7. session-timeout (可选)
        if "session-timeout" in data and data["session-timeout"] is not None:
            self.add_text_element(policy_element, "session-timeout", str(data["session-timeout"]))

        # 8. time-range (可选)
        if "time-range" in data and data["time-range"]:
            self.add_text_element(policy_element, "time-range", data["time-range"])

        # 添加安全功能
        self._add_security_features(policy_element, data)

        return policy_element
    
    def _add_source_destination_elements(self, policy_element: etree.Element, data: Dict[str, Any]):
        """
        添加源和目标元素
        
        Args:
            policy_element: 策略元素
            data: 策略数据
        """
        # 源区域
        if "source-zone" in data and data["source-zone"]:
            zones = data["source-zone"] if isinstance(data["source-zone"], list) else [data["source-zone"]]
            for zone in zones:
                self.add_text_element(policy_element, "source-zone", zone)
        
        # 目标区域 - 使用YANG模型要求的dest-zone字段名
        if "dest-zone" in data and data["dest-zone"]:
            zones = data["dest-zone"] if isinstance(data["dest-zone"], list) else [data["dest-zone"]]
            for zone in zones:
                self.add_text_element(policy_element, "dest-zone", zone)
        elif "destination-zone" in data and data["destination-zone"]:
            # 向后兼容旧字段名
            zones = data["destination-zone"] if isinstance(data["destination-zone"], list) else [data["destination-zone"]]
            for zone in zones:
                self.add_text_element(policy_element, "dest-zone", zone)
        
        # 源接口 - 按照YANG模型要求的list结构生成
        if "source-interface" in data and data["source-interface"]:
            interfaces = data["source-interface"] if isinstance(data["source-interface"], list) else [data["source-interface"]]
            for interface in interfaces:
                # 创建source-interface元素，然后在其中创建name子元素
                source_intf_element = etree.SubElement(policy_element, "source-interface")
                if isinstance(interface, dict):
                    self.add_text_element(source_intf_element, "name", interface.get("name", ""))
                else:
                    self.add_text_element(source_intf_element, "name", str(interface))

        # 目标接口 - 按照YANG模型要求的list结构生成
        if "dest-interface" in data and data["dest-interface"]:
            interfaces = data["dest-interface"] if isinstance(data["dest-interface"], list) else [data["dest-interface"]]
            for interface in interfaces:
                # 创建dest-interface元素，然后在其中创建name子元素
                dest_intf_element = etree.SubElement(policy_element, "dest-interface")
                if isinstance(interface, dict):
                    self.add_text_element(dest_intf_element, "name", interface.get("name", ""))
                else:
                    self.add_text_element(dest_intf_element, "name", str(interface))
        
        # 源地址
        if "source-address" in data and data["source-address"]:
            addresses = data["source-address"] if isinstance(data["source-address"], list) else [data["source-address"]]
            for address in addresses:
                self.add_text_element(policy_element, "source-address", address)
        
        # 目标地址
        if "destination-address" in data and data["destination-address"]:
            addresses = data["destination-address"] if isinstance(data["destination-address"], list) else [data["destination-address"]]
            for address in addresses:
                self.add_text_element(policy_element, "destination-address", address)
    
    def _add_service_elements(self, policy_element: etree.Element, data: Dict[str, Any]):
        """
        添加服务元素
        
        Args:
            policy_element: 策略元素
            data: 策略数据
        """
        if "service" in data and data["service"]:
            services = data["service"] if isinstance(data["service"], list) else [data["service"]]
            for service in services:
                service_element = etree.SubElement(policy_element, "service")
                self.add_text_element(service_element, "name", service)
    
    def _add_security_features(self, policy_element: etree.Element, data: Dict[str, Any]):
        """
        添加安全功能元素
        
        Args:
            policy_element: 策略元素
            data: 策略数据
        """
        # 根据用户记忆中的映射规则添加安全功能
        if "av" in data and data["av"]:
            self.add_text_element(policy_element, "av", data["av"])
        
        if "ips" in data and data["ips"]:
            self.add_text_element(policy_element, "ips", data["ips"])
    
    def validate_input_data(self, data: Dict[str, Any]) -> bool:
        """
        验证安全策略输入数据
        
        Args:
            data: 输入数据
            
        Returns:
            bool: 数据是否有效
        """
        if not super().validate_input_data(data):
            return False
        
        # 检查必要字段
        required_fields = ["name", "action"]
        for field in required_fields:
            if field not in data or not data[field]:
                log(_("security_policy_generator_adapter.missing_required_field"), "error", field=field)
                return False
        
        # 验证动作值
        valid_actions = ["permit", "deny", "accept", "drop"]
        if data["action"] not in valid_actions:
            log(_("security_policy_generator_adapter.invalid_action"), "error", 
                action=data["action"], valid_actions=valid_actions)
            return False
        
        return True
