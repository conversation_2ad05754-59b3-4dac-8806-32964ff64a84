# -*- coding: utf-8 -*-
"""
XML生成器适配器 - 通用XML生成器适配器
"""

from typing import Dict, Any, List, Union, Optional
from lxml import etree
from engine.utils.logger import log
from engine.utils.i18n import _
from .generator_interface import GeneratorInterface


class XmlGeneratorAdapter(GeneratorInterface):
    """
    XML生成器适配器
    通用的XML生成器适配器
    """
    
    def __init__(self):
        """初始化XML生成器适配器"""
        super().__init__(
            generator_type="xml",
            target_format="xml"
        )
    
    def generate_xml_element(self, data: Dict[str, Any], 
                           parent_element: Optional[etree.Element] = None) -> etree.Element:
        """
        生成XML元素
        
        Args:
            data: 数据
            parent_element: 父元素（可选）
            
        Returns:
            etree.Element: 生成的XML元素
        """
        if not self.validate_input_data(data):
            raise ValueError(_("xml_generator_adapter.invalid_input_data"))
        
        # 创建通用元素
        tag_name = data.get("tag_name", "element")
        if parent_element is not None:
            element = etree.SubElement(parent_element, tag_name)
        else:
            element = etree.Element(tag_name)
        
        return element
    
    def generate_xml_string(self, data: Union[Dict[str, Any], List[Dict[str, Any]]], 
                           pretty_print: bool = True) -> str:
        """
        生成XML字符串
        
        Args:
            data: 数据或数据列表
            pretty_print: 是否格式化输出
            
        Returns:
            str: XML字符串
        """
        # 创建根元素
        root = etree.Element("root")
        
        return self.format_xml_output(root, pretty_print)
