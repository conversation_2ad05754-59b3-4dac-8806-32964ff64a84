"""
优化流程控制器
负责根据四层优化策略结果控制转换流程的执行
"""

import time
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass
from enum import Enum

from engine.processing.pipeline.data_flow import DataContext
from engine.utils.logger import log
from engine.utils.i18n import _


class ProcessingDecision(Enum):
    """处理决策枚举"""
    SKIP = "skip"
    SIMPLIFIED = "simplified"
    FULL = "full"


@dataclass
class FlowControlMetrics:
    """流程控制指标"""
    total_items: int = 0
    skipped_items: int = 0
    simplified_items: int = 0
    full_processed_items: int = 0
    time_saved: float = 0.0
    quality_preserved: float = 0.0


class OptimizationFlowController:
    """
    优化流程控制器
    
    根据四层优化策略的结果，智能控制转换流程的执行，
    实现跳过、简化和完整处理的流程控制。
    """
    
    def __init__(self):
        """初始化优化流程控制器"""
        self.optimization_enabled = False
        self.section_decisions = {}
        self.global_metrics = FlowControlMetrics()
        self.stage_metrics = {}
        
        # 跳过规则配置
        self.skip_rules = {
            'web_interface': ['webfilter', 'gui', 'web-proxy'],
            'application_control': ['application', 'app-ctrl'],
            'antivirus': ['antivirus', 'av-profile'],
            'ips': ['ips', 'intrusion-prevention'],
            'wireless': ['wireless', 'wifi', 'wtp'],
            'cache_temp': ['cache', 'temp', 'temporary'],
            'monitoring': ['monitor', 'log', 'report'],
            'unsupported': ['voip', 'dlp', 'waf']
        }
        
        # 简化处理规则
        self.simplified_rules = {
            'service_references': ['service', 'port'],
            'user_auth': ['user', 'auth', 'ldap'],
            'logging': ['log', 'syslog'],
            'language_interface': ['language', 'locale'],
            'certificate': ['certificate', 'ssl', 'pki'],
            'interface_dependency': ['vlan', 'trunk']
        }
        
        log(_("optimization_flow_controller.initialized"))
    
    def initialize_from_context(self, context: DataContext) -> bool:
        """
        从数据上下文初始化流程控制器
        
        Args:
            context: 数据上下文
            
        Returns:
            bool: 初始化是否成功
        """
        try:
            self.optimization_enabled = context.get_data("optimization_enabled", False)
            self.section_decisions = context.get_data("section_processing_decisions", {})
            
            if self.optimization_enabled:
                log(_("optimization_flow_controller.context_loaded",
                                 decisions_count=len(self.section_decisions)), "info")
                return True
            else:
                log(_("optimization_flow_controller.optimization_disabled"))
                return False
                
        except Exception as e:
            log(f"优化流程控制器初始化失败: {str(e)}")
            return False
    
    def should_skip_item(self, item_name: str, item_type: str = None) -> bool:
        """
        判断是否应该跳过处理某个项目
        
        Args:
            item_name: 项目名称
            item_type: 项目类型
            
        Returns:
            bool: 是否应该跳过
        """
        if not self.optimization_enabled:
            return False
        
        # 检查优化决策
        decision = self._get_optimization_decision(item_name)
        if decision and decision.get('skip_processing', False):
            return True
        
        # 检查跳过规则
        if self._matches_skip_rules(item_name, item_type):
            return True
        
        return False
    
    def should_use_simplified(self, item_name: str, item_type: str = None) -> bool:
        """
        判断是否应该使用简化处理
        
        Args:
            item_name: 项目名称
            item_type: 项目类型
            
        Returns:
            bool: 是否应该简化处理
        """
        if not self.optimization_enabled:
            return False
        
        # 检查优化决策
        decision = self._get_optimization_decision(item_name)
        if decision and decision.get('use_simplified', False):
            return True
        
        # 检查简化规则
        if self._matches_simplified_rules(item_name, item_type):
            return True
        
        return False
    
    def get_processing_decision(self, item_name: str, item_type: str = None) -> ProcessingDecision:
        """
        获取处理决策
        
        Args:
            item_name: 项目名称
            item_type: 项目类型
            
        Returns:
            ProcessingDecision: 处理决策
        """
        if self.should_skip_item(item_name, item_type):
            return ProcessingDecision.SKIP
        elif self.should_use_simplified(item_name, item_type):
            return ProcessingDecision.SIMPLIFIED
        else:
            return ProcessingDecision.FULL
    
    def record_processing_result(self, stage_name: str, item_name: str, 
                               decision: ProcessingDecision, processing_time: float = 0.0,
                               quality_score: float = 1.0):
        """
        记录处理结果
        
        Args:
            stage_name: 阶段名称
            item_name: 项目名称
            decision: 处理决策
            processing_time: 处理时间
            quality_score: 质量分数
        """
        # 更新全局指标
        self.global_metrics.total_items += 1
        
        if decision == ProcessingDecision.SKIP:
            self.global_metrics.skipped_items += 1
            # 估算节省的时间（假设完整处理需要0.1秒）
            self.global_metrics.time_saved += 0.1
        elif decision == ProcessingDecision.SIMPLIFIED:
            self.global_metrics.simplified_items += 1
            # 估算节省的时间（简化处理节省60%时间）
            self.global_metrics.time_saved += processing_time * 0.6
        else:
            self.global_metrics.full_processed_items += 1
        
        # 更新阶段指标
        if stage_name not in self.stage_metrics:
            self.stage_metrics[stage_name] = FlowControlMetrics()
        
        stage_metric = self.stage_metrics[stage_name]
        stage_metric.total_items += 1
        
        if decision == ProcessingDecision.SKIP:
            stage_metric.skipped_items += 1
        elif decision == ProcessingDecision.SIMPLIFIED:
            stage_metric.simplified_items += 1
        else:
            stage_metric.full_processed_items += 1
        
        stage_metric.quality_preserved = (stage_metric.quality_preserved * (stage_metric.total_items - 1) + quality_score) / stage_metric.total_items
    
    def get_optimization_summary(self) -> Dict[str, Any]:
        """
        获取优化总结
        
        Returns:
            Dict[str, Any]: 优化总结信息
        """
        if self.global_metrics.total_items == 0:
            return {"status": "no_processing"}
        
        skip_ratio = self.global_metrics.skipped_items / self.global_metrics.total_items
        simplified_ratio = self.global_metrics.simplified_items / self.global_metrics.total_items
        optimization_ratio = skip_ratio + simplified_ratio
        
        return {
            "status": "completed",
            "global_metrics": {
                "total_items": self.global_metrics.total_items,
                "skipped_items": self.global_metrics.skipped_items,
                "simplified_items": self.global_metrics.simplified_items,
                "full_processed_items": self.global_metrics.full_processed_items,
                "skip_ratio": skip_ratio,
                "simplified_ratio": simplified_ratio,
                "optimization_ratio": optimization_ratio,
                "time_saved": self.global_metrics.time_saved,
                "quality_preserved": self.global_metrics.quality_preserved
            },
            "stage_metrics": {
                stage: {
                    "total": metrics.total_items,
                    "skipped": metrics.skipped_items,
                    "simplified": metrics.simplified_items,
                    "full": metrics.full_processed_items,
                    "quality": metrics.quality_preserved
                }
                for stage, metrics in self.stage_metrics.items()
            }
        }
    
    def log_optimization_summary(self):
        """记录优化总结日志"""
        summary = self.get_optimization_summary()
        
        if summary["status"] == "no_processing":
            log("优化流程控制器：无处理项目")
            return
        
        global_metrics = summary["global_metrics"]
        
        log("=" * 60)
        log("四层优化策略执行总结")
        log("=" * 60)
        log(f"总处理项目: {global_metrics['total_items']}")
        log(f"跳过处理: {global_metrics['skipped_items']} ({global_metrics['skip_ratio']*100:.1f}%)")
        log(f"简化处理: {global_metrics['simplified_items']} ({global_metrics['simplified_ratio']*100:.1f}%)")
        log(f"完整处理: {global_metrics['full_processed_items']} ({(1-global_metrics['optimization_ratio'])*100:.1f}%)")
        log(f"总优化比例: {global_metrics['optimization_ratio']*100:.1f}%")
        log(f"预估时间节省: {global_metrics['time_saved']:.2f}秒")
        log(f"质量保持: {global_metrics['quality_preserved']*100:.1f}%")
        log("=" * 60)
        
        # 记录各阶段详情
        for stage, metrics in summary["stage_metrics"].items():
            if metrics["total"] > 0:
                log(f"阶段 {stage}: 总计{metrics['total']}, 跳过{metrics['skipped']}, 简化{metrics['simplified']}, 完整{metrics['full']}")
    
    def _get_optimization_decision(self, item_name: str) -> Optional[Dict[str, Any]]:
        """获取优化决策"""
        # 直接匹配
        if item_name in self.section_decisions:
            return self.section_decisions[item_name]
        
        # 模糊匹配
        for section_name, decision in self.section_decisions.items():
            if self._is_related_item(item_name, section_name):
                return decision
        
        return None
    
    def _is_related_item(self, item_name: str, section_name: str) -> bool:
        """判断项目是否与段落相关"""
        item_lower = item_name.lower()
        section_lower = section_name.lower()
        
        # 关键词匹配
        keywords = ['address', 'service', 'policy', 'interface', 'zone', 'route']
        for keyword in keywords:
            if keyword in section_lower and keyword in item_lower:
                return True
        
        # 包含关系
        if section_lower in item_lower or item_lower in section_lower:
            return True
        
        return False
    
    def _matches_skip_rules(self, item_name: str, item_type: str = None) -> bool:
        """检查是否匹配跳过规则"""
        item_lower = item_name.lower()
        
        for category, keywords in self.skip_rules.items():
            for keyword in keywords:
                if keyword in item_lower:
                    return True
        
        return False
    
    def _matches_simplified_rules(self, item_name: str, item_type: str = None) -> bool:
        """检查是否匹配简化规则"""
        item_lower = item_name.lower()
        
        for category, keywords in self.simplified_rules.items():
            for keyword in keywords:
                if keyword in item_lower:
                    return True
        
        return False
