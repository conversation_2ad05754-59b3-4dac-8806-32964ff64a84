# -*- coding: utf-8 -*-
"""
Fortigate解析器适配器 - 将现有FortigateParser适配为插件接口
保持与现有解析逻辑的完全兼容性
"""

import os
from typing import Dict, Any, List
from engine.utils.logger import log
from engine.utils.i18n import _
from .parser_plugin import ParserPlugin


class FortigateParserAdapter(ParserPlugin):
    """
    Fortigate解析器适配器
    将现有的FortigateParser包装为标准插件接口
    """
    
    def __init__(self):
        """初始化Fortigate解析器适配器"""
        super().__init__(
            vendor="fortigate",
            supported_versions=["6.0", "6.2", "6.4", "7.0", "7.2"]
        )
        
        # 延迟导入现有解析器
        self._legacy_parser = None
        
    def _get_legacy_parser(self):
        """获取现有解析器实例"""
        if self._legacy_parser is None:
            try:
                from engine.parsers.fortigate_parser import FortigateParser
                self._legacy_parser = FortigateParser()
                log(_("fortigate_parser_adapter.legacy_parser_loaded"), "debug")
            except ImportError as e:
                error_msg = _("fortigate_parser_adapter.legacy_parser_import_failed", error=str(e))
                log(error_msg, "error")
                raise RuntimeError(error_msg)
        
        return self._legacy_parser
    
    def parse_config_file(self, config_file_path: str) -> Dict[str, Any]:
        """
        解析Fortigate配置文件 - 委托给现有解析器
        
        Args:
            config_file_path: 配置文件路径
            
        Returns: Dict[str, Any]: 解析后的配置数据
            
        Raises:
            FileNotFoundError: 配置文件不存在
            ValueError: 配置文件格式错误
        """
        if not os.path.exists(config_file_path):
            raise FileNotFoundError(_("fortigate_parser_adapter.config_file_not_found", 
                                    file=config_file_path))
        
        try:
            legacy_parser = self._get_legacy_parser()
            
            # 委托给现有解析器
            parsed_data = legacy_parser.parse(config_file_path)
            
            log(_("fortigate_parser_adapter.config_parsed"), "info", 
                file=config_file_path, sections=len(parsed_data))
            
            return parsed_data
            
        except Exception as e:
            error_msg = _("fortigate_parser_adapter.parsing_failed", 
                         file=config_file_path, error=str(e))
            log(error_msg, "error")
            raise ValueError(error_msg)
    
    def validate_config_format(self, config_file_path: str) -> bool:
        """
        验证Fortigate配置文件格式
        
        Args:
            config_file_path: 配置文件路径
            
        Returns:
            bool: 格式是否有效
        """
        if not os.path.exists(config_file_path):
            return False
        
        try:
            # 简单的格式验证 - 检查是否包含Fortigate特征
            with open(config_file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read(1024)  # 只读取前1024字符进行快速检查
            
            # 检查Fortigate配置特征
            fortigate_indicators = [
                "config ",
                "edit ",
                "set ",
                "next",
                "end"
            ]
            
            indicator_count = sum(1 for indicator in fortigate_indicators if indicator in content)
            
            # 如果包含多个Fortigate特征，认为是有效格式
            is_valid = indicator_count >= 3
            
            log(_("fortigate_parser_adapter.format_validation"), "debug",
                file=config_file_path, valid=is_valid, indicators=indicator_count)
            
            return is_valid
            
        except Exception as e:
            log(_("fortigate_parser_adapter.format_validation_failed"), "warning",
                file=config_file_path, error=str(e))
            return False
    
    def extract_interfaces(self, config_file_path: str) -> List[Dict[str, Any]]:
        """
        提取接口信息 - 委托给现有解析器
        
        Args:
            config_file_path: 配置文件路径
            
        Returns: List[Dict[str, Any]]: 接口信息列表
        """
        try:
            legacy_parser = self._get_legacy_parser()
            
            # 如果现有解析器有专门的接口提取方法，使用它
            if hasattr(legacy_parser, 'extract_interfaces'):
                interfaces = legacy_parser.extract_interfaces(config_file_path)
            else:
                # 否则从完整解析结果中提取
                config_data = legacy_parser.parse_config_file(config_file_path)
                interfaces = config_data.get("interfaces", [])
            
            log(_("fortigate_parser_adapter.interfaces_extracted"), "info",
                file=config_file_path, count=len(interfaces))
            
            return interfaces
            
        except Exception as e:
            log(_("fortigate_parser_adapter.interface_extraction_failed"), "error",
                file=config_file_path, error=str(e))
            return []
    
    def extract_policies(self, config_file_path: str) -> List[Dict[str, Any]]:
        """
        提取策略信息 - 委托给现有解析器
        
        Args:
            config_file_path: 配置文件路径
            
        Returns: List[Dict[str, Any]]: 策略信息列表
        """
        try:
            legacy_parser = self._get_legacy_parser()
            
            # 如果现有解析器有专门的策略提取方法，使用它
            if hasattr(legacy_parser, 'extract_policies'):
                policies = legacy_parser.extract_policies(config_file_path)
            else:
                # 否则从完整解析结果中提取
                config_data = legacy_parser.parse_config_file(config_file_path)
                policies = config_data.get("policies", [])
            
            log(_("fortigate_parser_adapter.policies_extracted"), "info",
                file=config_file_path, count=len(policies))
            
            return policies
            
        except Exception as e:
            log(_("fortigate_parser_adapter.policy_extraction_failed"), "error",
                file=config_file_path, error=str(e))
            return []
    
    def get_plugin_info(self) -> Dict[str, Any]:
        """
        获取插件信息 - 增强版本

        Returns: Dict[str, Any]: 插件信息
        """
        base_info = super().get_plugin_info()

        # 添加Fortigate特定信息
        base_info.update({
            "version": base_info.get("plugin_version", "1.0"),  # 添加version字段以保持兼容性
            "legacy_parser_available": self._legacy_parser is not None,
            "fortigate_features": {
                "policy_parsing": True,
                "interface_parsing": True,
                "address_object_parsing": True,
                "service_object_parsing": True,
                "vip_parsing": True,
                "nat_parsing": True
            }
        })

        return base_info
