# -*- coding: utf-8 -*-
"""
解析器插件接口 - 定义标准化的解析器插件接口
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from engine.utils.logger import log
from engine.utils.i18n import _


class ParserPlugin(ABC):
    """
    解析器插件基类
    定义厂商特定解析器的标准接口
    """
    
    def __init__(self, vendor: str, supported_versions: Optional[List[str]] = None):
        """
        初始化解析器插件
        
        Args:
            vendor: 厂商名称
            supported_versions: 支持的版本列表
        """
        self.vendor = vendor
        self.supported_versions = supported_versions or []
        self.plugin_version = "1.0"
        
        log(_("parser_plugin.initialized"), "info", vendor=vendor)
    
    @abstractmethod
    def parse_config_file(self, config_file_path: str) -> Dict[str, Any]:
        """
        解析配置文件
        
        Args:
            config_file_path: 配置文件路径
            
        Returns: Dict[str, Any]: 解析后的配置数据
            
        Raises:
            FileNotFoundError: 配置文件不存在
            ValueError: 配置文件格式错误
        """
        pass
    
    @abstractmethod
    def validate_config_format(self, config_file_path: str) -> bool:
        """
        验证配置文件格式
        
        Args:
            config_file_path: 配置文件路径
            
        Returns:
            bool: 格式是否有效
        """
        pass
    
    def extract_interfaces(self, config_file_path: str) -> List[Dict[str, Any]]:
        """
        提取接口信息 - 默认实现
        
        Args:
            config_file_path: 配置文件路径
            
        Returns: List[Dict[str, Any]]: 接口信息列表
        """
        try:
            config_data = self.parse_config_file(config_file_path)
            return config_data.get("interfaces", [])
        except Exception as e:
            log(_("parser_plugin.interface_extraction_failed"), "error", 
                vendor=self.vendor, error=str(e))
            return []
    
    def extract_policies(self, config_file_path: str) -> List[Dict[str, Any]]:
        """
        提取策略信息 - 默认实现
        
        Args:
            config_file_path: 配置文件路径
            
        Returns: List[Dict[str, Any]]: 策略信息列表
        """
        try:
            config_data = self.parse_config_file(config_file_path)
            return config_data.get("policies", [])
        except Exception as e:
            log(_("parser_plugin.policy_extraction_failed"), "error",
                vendor=self.vendor, error=str(e))
            return []
    
    def extract_address_objects(self, config_file_path: str) -> List[Dict[str, Any]]:
        """
        提取地址对象信息 - 默认实现
        
        Args:
            config_file_path: 配置文件路径
            
        Returns: List[Dict[str, Any]]: 地址对象列表
        """
        try:
            config_data = self.parse_config_file(config_file_path)
            return config_data.get("address_objects", [])
        except Exception as e:
            log(_("parser_plugin.address_extraction_failed"), "error",
                vendor=self.vendor, error=str(e))
            return []
    
    def extract_service_objects(self, config_file_path: str) -> List[Dict[str, Any]]:
        """
        提取服务对象信息 - 默认实现
        
        Args:
            config_file_path: 配置文件路径
            
        Returns: List[Dict[str, Any]]: 服务对象列表
        """
        try:
            config_data = self.parse_config_file(config_file_path)
            return config_data.get("service_objects", [])
        except Exception as e:
            log(_("parser_plugin.service_extraction_failed"), "error",
                vendor=self.vendor, error=str(e))
            return []
    
    def get_plugin_info(self) -> Dict[str, Any]:
        """
        获取插件信息
        
        Returns: Dict[str, Any]: 插件信息
        """
        return {
            "vendor": self.vendor,
            "plugin_version": self.plugin_version,
            "supported_versions": self.supported_versions,
            "plugin_class": self.__class__.__name__,
            "capabilities": {
                "config_parsing": True,
                "interface_extraction": True,
                "policy_extraction": True,
                "address_extraction": True,
                "service_extraction": True
            }
        }
    
    def is_version_supported(self, version: str) -> bool:
        """
        检查版本是否支持
        
        Args:
            version: 版本号
            
        Returns:
            bool: 是否支持
        """
        if not self.supported_versions:
            return True  # 如果没有指定版本限制，则支持所有版本
        return version in self.supported_versions
