# -*- coding: utf-8 -*-
"""
解析器注册表 - 管理和发现解析器插件
"""

from typing import Dict, Optional, List, Type
from engine.utils.logger import log
from engine.utils.i18n import _
from .parser_plugin import ParserPlugin


class ParserRegistry:
    """
    解析器注册表
    负责管理和发现解析器插件
    """
    
    def __init__(self):
        """初始化解析器注册表"""
        self._parsers: Dict[str, Type[ParserPlugin]] = {}
        self._parser_instances: Dict[str, ParserPlugin] = {}
        
        # 自动注册内置解析器
        self._register_builtin_parsers()
        
        log(_("parser_registry.initialized"), "info")
    
    def _register_builtin_parsers(self):
        """注册内置解析器"""
        try:
            # 注册Fortigate解析器适配器
            from .fortigate_parser_adapter import FortigateParserAdapter
            self.register_parser("fortigate", FortigateParserAdapter)
            
        except ImportError as e:
            error_msg = _("parser_registry.builtin_registration_failed", error=str(e))
            log(error_msg, "warning")
    
    def register_parser(self, vendor: str, parser_class: Type[ParserPlugin]):
        """
        注册解析器插件
        
        Args:
            vendor: 厂商名称
            parser_class: 解析器类
            
        Raises:
            ValueError: 如果解析器类无效
        """
        if not issubclass(parser_class, ParserPlugin):
            raise ValueError(_("parser_registry.invalid_parser_class", 
                             class_name=parser_class.__name__))
        
        self._parsers[vendor.lower()] = parser_class
        
        # 清除缓存的实例
        if vendor.lower() in self._parser_instances:
            del self._parser_instances[vendor.lower()]
        
        log(_("parser_registry.parser_registered"), "info", 
            vendor=vendor, class_name=parser_class.__name__)
    
    def get_parser(self, vendor: str) -> Optional[ParserPlugin]:
        """
        获取解析器实例
        
        Args:
            vendor: 厂商名称
            
        Returns:
            Optional[ParserPlugin]: 解析器实例，如果不存在则返回None
        """
        vendor_key = vendor.lower()
        
        # 检查缓存
        if vendor_key in self._parser_instances:
            return self._parser_instances[vendor_key]
        
        # 检查是否注册了该厂商的解析器
        if vendor_key not in self._parsers:
            log(_("parser_registry.parser_not_found"), "error", vendor=vendor)
            return None
        
        try:
            # 创建解析器实例
            parser_class = self._parsers[vendor_key]
            parser_instance = parser_class()
            
            # 缓存实例
            self._parser_instances[vendor_key] = parser_instance
            
            log(_("parser_registry.parser_created"), "info", vendor=vendor)
            return parser_instance
            
        except Exception as e:
            error_msg = _("parser_registry.parser_creation_failed", 
                         vendor=vendor, error=str(e))
            log(error_msg, "error")
            return None
    
    def is_vendor_supported(self, vendor: str) -> bool:
        """
        检查厂商是否支持
        
        Args:
            vendor: 厂商名称
            
        Returns:
            bool: 是否支持
        """
        return vendor.lower() in self._parsers
    
    def get_supported_vendors(self) -> List[str]:
        """
        获取支持的厂商列表
        
        Returns: List[str]: 支持的厂商列表
        """
        return list(self._parsers.keys())
    
    def get_parser_info(self, vendor: str) -> Optional[Dict[str, any]]:
        """
        获取解析器信息
        
        Args:
            vendor: 厂商名称
            
        Returns:
            Optional[Dict[str, any]]: 解析器信息
        """
        parser = self.get_parser(vendor)
        if parser:
            return parser.get_plugin_info()
        return None
    
    def validate_parser_compatibility(self, vendor: str, version: str) -> bool:
        """
        验证解析器版本兼容性
        
        Args:
            vendor: 厂商名称
            version: 版本号
            
        Returns:
            bool: 是否兼容
        """
        parser = self.get_parser(vendor)
        if parser:
            return parser.is_version_supported(version)
        return False
    
    def clear_cache(self):
        """清空解析器实例缓存"""
        self._parser_instances.clear()
        log(_("parser_registry.cache_cleared"), "info")
    
    def get_registry_stats(self) -> Dict[str, any]:
        """
        获取注册表统计信息
        
        Returns: Dict[str, any]: 统计信息
        """
        return {
            "registered_parsers": len(self._parsers),
            "cached_instances": len(self._parser_instances),
            "supported_vendors": self.get_supported_vendors(),
            "parser_classes": {vendor: cls.__name__ for vendor, cls in self._parsers.items()}
        }
