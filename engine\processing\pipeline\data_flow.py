# -*- coding: utf-8 -*-
"""
数据流转上下文 - 管道中数据的传递和状态管理
"""

import time
from typing import Dict, Any, Optional, List
from engine.utils.logger import log
from engine.utils.i18n import _


class DataContext:
    """
    数据流转上下文
    在处理管道中传递数据和状态信息
    """
    
    def __init__(self, initial_data: Optional[Dict[str, Any]] = None, config_manager=None):
        """
        初始化数据上下文

        Args:
            initial_data: 初始数据
            config_manager: 配置管理器实例
        """
        self.data = initial_data or {}
        self.metadata = {
            'created_at': time.time(),
            'stage_history': [],
            'errors': [],
            'warnings': []
        }
        self.state = 'initialized'

        # 配置管理器
        self._config_manager = config_manager

        # 默认配置
        self._default_config = self._load_default_config()

    def _load_default_config(self) -> Dict[str, Any]:
        """
        加载默认配置

        Returns:
            Dict[str, Any]: 默认配置字典
        """
        default_config = {
            'nat': {
                'use_twice_nat44': True,
                'twice_nat44_fallback': True,
                'validate_twice_nat44': True,
                'twice_nat44_debug': False
            }
        }

        # 尝试从系统设置加载
        try:
            from engine.infrastructure.config.settings import Settings
            settings = Settings()

            # 合并NAT设置
            nat_settings = settings.get('nat', {})
            if nat_settings:
                default_config['nat'].update(nat_settings)

        except Exception:
            # 如果无法加载设置，使用硬编码默认值
            pass

        return default_config

        # 默认配置 - 从系统设置加载
        self._default_config = self._load_default_config()
        
    def set_data(self, key: str, value: Any):
        """
        设置数据
        
        Args:
            key: 数据键
            value: 数据值
        """
        self.data[key] = value
        
    def get_data(self, key: str, default: Any = None) -> Any:
        """
        获取数据
        
        Args:
            key: 数据键
            default: 默认值
            
        Returns:
            数据值
        """
        return self.data.get(key, default)
    
    def has_data(self, key: str) -> bool:
        """
        检查是否包含指定数据

        Args:
            key: 数据键

        Returns:
            bool: 是否包含
        """
        return key in self.data

    def get(self, key: str, default: Any = None) -> Any:
        """
        获取数据（兼容性方法）

        Args:
            key: 数据键
            default: 默认值

        Returns:
            数据值
        """
        return self.get_data(key, default)

    def get_config(self, key: str, default: Any = None) -> Any:
        """
        获取配置值

        Args:
            key: 配置键，支持点号分隔的嵌套键（如"nat.use_twice_nat44"）
            default: 默认值

        Returns:
            配置值
        """
        # 优先从配置管理器获取
        if self._config_manager and hasattr(self._config_manager, 'get_config'):
            try:
                return self._config_manager.get_config(key, default)
            except Exception:
                pass

        # 从默认配置获取
        keys = key.split('.')
        value = self._default_config

        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default

        return value

    def set_config_manager(self, config_manager):
        """
        设置配置管理器

        Args:
            config_manager: 配置管理器实例
        """
        self._config_manager = config_manager
    
    def update_data(self, data_dict: Dict[str, Any]):
        """
        批量更新数据
        
        Args:
            data_dict: 数据字典
        """
        self.data.update(data_dict)
    
    def add_stage_record(self, stage_name: str, status: str, 
                        duration: float = 0, details: Optional[Dict] = None):
        """
        添加阶段执行记录
        
        Args:
            stage_name: 阶段名称
            status: 执行状态
            duration: 执行时长
            details: 详细信息
        """
        record = {
            'stage': stage_name,
            'status': status,
            'timestamp': time.time(),
            'duration': duration,
            'details': details or {}
        }
        self.metadata['stage_history'].append(record)
    
    def add_error(self, error_message: str, stage: Optional[str] = None):
        """
        添加错误信息
        
        Args:
            error_message: 错误消息
            stage: 发生错误的阶段
        """
        error_record = {
            'message': error_message,
            'stage': stage,
            'timestamp': time.time()
        }
        self.metadata['errors'].append(error_record)
        log(_("data_context.error_added_with_stage",
              error_message=error_message, stage=stage), "error")
    
    def add_warning(self, warning_message: str, stage: Optional[str] = None):
        """
        添加警告信息
        
        Args:
            warning_message: 警告消息
            stage: 发生警告的阶段
        """
        warning_record = {
            'message': warning_message,
            'stage': stage,
            'timestamp': time.time()
        }
        self.metadata['warnings'].append(warning_record)
        # log(f"数据上下文添加警告: {warning_message} (阶段: {stage})", "warning")
    
    def set_state(self, new_state: str):
        """
        设置处理状态
        
        Args:
            new_state: 新状态
        """
        old_state = self.state
        self.state = new_state
        log(_("data_context.state_changed_with_details", old_state=old_state, new_state=new_state), "debug")
    
    def has_errors(self) -> bool:
        """
        检查是否有错误
        
        Returns:
            bool: 是否有错误
        """
        return len(self.metadata['errors']) > 0
    
    def has_warnings(self) -> bool:
        """
        检查是否有警告
        
        Returns:
            bool: 是否有警告
        """
        return len(self.metadata['warnings']) > 0
    
    def get_errors(self) -> List[Dict[str, Any]]:
        """
        获取所有错误
        
        Returns: List[Dict[str, Any]]: 错误列表
        """
        return self.metadata['errors'].copy()
    
    def get_warnings(self) -> List[Dict[str, Any]]:
        """
        获取所有警告
        
        Returns: List[Dict[str, Any]]: 警告列表
        """
        return self.metadata['warnings'].copy()
    
    def get_stage_history(self) -> List[Dict[str, Any]]:
        """
        获取阶段执行历史
        
        Returns: List[Dict[str, Any]]: 阶段历史
        """
        return self.metadata['stage_history'].copy()

    def add_generated_address(self, address_obj: Dict[str, Any], source_info: Dict[str, Any]):
        """
        添加动态生成的地址对象

        Args:
            address_obj: 生成的地址对象
            source_info: 源信息（如来源服务对象）
        """
        if 'generated_addresses' not in self.data:
            self.data['generated_addresses'] = []

        address_record = {
            'address_object': address_obj,
            'source_info': source_info,
            'timestamp': time.time()
        }

        self.data['generated_addresses'].append(address_record)
        log(_("data_context.address_object_added", name=address_obj.get('name', 'unknown')), "debug")

    def get_generated_addresses(self) -> List[Dict[str, Any]]:
        """
        获取所有动态生成的地址对象

        Returns: List[Dict[str, Any]]: 生成的地址对象列表
        """
        return self.data.get('generated_addresses', [])

    def track_service_iprange_conversion(self, service_name: str, address_name: str):
        """
        跟踪服务对象iprange转换关系

        Args:
            service_name: 原始服务对象名称
            address_name: 生成的地址对象名称
        """
        if 'service_iprange_conversions' not in self.data:
            self.data['service_iprange_conversions'] = {}

        self.data['service_iprange_conversions'][service_name] = address_name
        log(_("data_context.service_iprange_conversion_recorded", service_name=service_name, address_name=address_name), "debug")

    def get_service_iprange_conversions(self) -> Dict[str, str]:
        """
        获取服务对象iprange转换关系

        Returns: Dict[str, str]: 服务名到地址对象名的映射
        """
        return self.data.get('service_iprange_conversions', {})

    def get_total_duration(self) -> float:
        """
        获取总执行时长
        
        Returns:
            float: 总时长（秒）
        """
        return sum(record.get('duration', 0) for record in self.metadata['stage_history'])
    
    def get_summary(self) -> Dict[str, Any]:
        """
        获取上下文摘要信息
        
        Returns: Dict[str, Any]: 摘要信息
        """
        return {
            'state': self.state,
            'data_keys': list(self.data.keys()),
            'total_duration': self.get_total_duration(),
            'stages_count': len(self.metadata['stage_history']),
            'errors_count': len(self.metadata['errors']),
            'warnings_count': len(self.metadata['warnings']),
            'created_at': self.metadata['created_at']
        }
