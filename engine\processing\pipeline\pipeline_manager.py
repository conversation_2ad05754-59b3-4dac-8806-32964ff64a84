# -*- coding: utf-8 -*-
"""
处理管道管理器 - 管理和执行处理管道
"""

import time
from typing import List, Dict, Any, Optional, Callable
from engine.utils.logger import log
from engine.utils.i18n import _
from .pipeline_stage import PipelineStage, FunctionStage
from .data_flow import DataContext


class PipelineManager:
    """
    处理管道管理器
    负责管理和执行由多个阶段组成的处理管道
    """

    def __init__(self, name: str, description: Optional[str] = None, config_manager=None):
        """
        初始化管道管理器

        Args:
            name: 管道名称
            description: 管道描述
            config_manager: 配置管理器实例
        """
        self.name = name
        self.description = description or name
        self.stages: List[PipelineStage] = []
        self.error_handlers: Dict[str, Callable] = {}
        self.stop_on_error = True
        self.enabled = True
        self.config_manager = config_manager

        log(_("pipeline_manager.initialized"), "info", name=name)
    
    def add_stage(self, stage: PipelineStage) -> 'PipelineManager':
        """
        添加处理阶段
        
        Args:
            stage: 管道阶段
            
        Returns:
            PipelineManager: 支持链式调用
        """
        self.stages.append(stage)
        log(_("pipeline_manager.stage_added"), "debug",
            pipeline=self.name, stage=stage.name)
        return self

    def add_function_stage(self, name: str, process_function: Callable[[DataContext], bool],
                          description: Optional[str] = None,
                          input_validator: Optional[Callable[[DataContext], bool]] = None) -> 'PipelineManager':
        """
        添加函数式阶段 - 便捷方法
        
        Args:
            name: 阶段名称
            process_function: 处理函数
            description: 阶段描述
            input_validator: 输入验证函数
            
        Returns:
            PipelineManager: 支持链式调用
        """
        stage = FunctionStage(name, process_function, description, input_validator)
        return self.add_stage(stage)

    def remove_stage(self, stage_name: str) -> bool:
        """
        移除处理阶段
        
        Args:
            stage_name: 阶段名称
            
        Returns:
            bool: 是否成功移除
        """
        for i, stage in enumerate(self.stages):
            if stage.name == stage_name:
                removed_stage = self.stages.pop(i)
                log(_("pipeline_manager.stage_removed"), "debug",
                    pipeline=self.name, stage=removed_stage.name)
                return True

        log(_("pipeline_manager.stage_not_found"), "warning",
            pipeline=self.name, stage=stage_name)
        return False

    def get_stage(self, stage_name: str) -> Optional[PipelineStage]:
        """
        获取指定阶段
        
        Args:
            stage_name: 阶段名称
            
        Returns:
            Optional[PipelineStage]: 阶段实例，如果不存在则返回None
        """
        for stage in self.stages:
            if stage.name == stage_name:
                return stage
        return None
    
    def set_error_handler(self, stage_name: str, handler: Callable[[Exception, DataContext], bool]):
        """
        为指定阶段设置错误处理器
        
        Args:
            stage_name: 阶段名称
            handler: 错误处理函数
        """
        stage = self.get_stage(stage_name)
        if stage:
            stage.set_error_handler(handler)
            log(_("pipeline_manager.error_handler_set"), "debug",
                pipeline=self.name, stage=stage_name)
        else:
            log(_("pipeline_manager.stage_not_found_for_error_handler"), "warning",
                pipeline=self.name, stage=stage_name)

    def execute(self, initial_data: Optional[Dict[str, Any]] = None) -> DataContext:
        """
        执行管道
        
        Args:
            initial_data: 初始数据
            
        Returns:
            DataContext: 执行结果上下文
        """
        if not self.enabled:
            log(_("pipeline_manager.pipeline_disabled"), "info", pipeline=self.name)
            context = DataContext(initial_data, self.config_manager)
            context.set_state("disabled")
            return context

        start_time = time.time()
        context = DataContext(initial_data, self.config_manager)
        context.set_state("running")

        log(_("pipeline_stage.stage_starting_new", stage=f"{self.name}, stage count: {len(self.stages)}"), "info")

        try:
            for i, stage in enumerate(self.stages):
                log(_("pipeline_stage.stage_starting_new", stage=f"{self.name} -> {stage.name} ({i+1}/{len(self.stages)})"), "info")

                success = stage.execute(context)

                if not success:
                    if self.stop_on_error:
                        log(f"Pipeline stopped due to error: {self.name} -> {stage.name}", "error")
                        context.set_state("failed")
                        break
                    else:
                        log(f"Stage failed but continuing execution: {self.name} -> {stage.name}", "warning")
            else:
                # All stages completed
                context.set_state("completed")
                log(_("pipeline_stage.stage_completed_new", stage=self.name, duration=time.time() - start_time), "info")

        except Exception as e:
            error_msg = f"Pipeline execution exception: {self.name}, error: {str(e)}"
            log(error_msg, "error")
            context.add_error(error_msg)
            context.set_state("error")

        total_duration = time.time() - start_time
        log(f"Pipeline execution completed: {self.name}, state: {context.state}, duration: {total_duration:.2f}s, errors: {len(context.get_errors())}, warnings: {len(context.get_warnings())}", "info")

        return context
    
    def validate_pipeline(self) -> Dict[str, Any]:
        """
        验证管道配置
        
        Returns: Dict[str, Any]: 验证结果
        """
        issues = []
        
        if not self.stages:
            issues.append(_("pipeline_manager.no_stages"))
        
        # 检查阶段名称重复
        stage_names = [stage.name for stage in self.stages]
        duplicates = set([name for name in stage_names if stage_names.count(name) > 1])
        if duplicates:
            issues.append(_("pipeline_manager.duplicate_stage_names", names=list(duplicates)))
        
        # 检查每个阶段的配置
        for stage in self.stages:
            if not stage.name:
                issues.append(_("pipeline_manager.stage_missing_name"))
        
        return {
            "valid": len(issues) == 0,
            "issues": issues,
            "stage_count": len(self.stages),
            "stage_names": stage_names
        }
    
    def get_pipeline_info(self) -> Dict[str, Any]:
        """
        获取管道信息
        
        Returns: Dict[str, Any]: 管道信息
        """
        return {
            "name": self.name,
            "description": self.description,
            "enabled": self.enabled,
            "stop_on_error": self.stop_on_error,
            "stage_count": len(self.stages),
            "stages": [
                {
                    "name": stage.name,
                    "description": stage.description,
                    "enabled": stage.enabled,
                    "type": type(stage).__name__
                }
                for stage in self.stages
            ]
        }
    
    def enable_stage(self, stage_name: str) -> bool:
        """
        启用指定阶段
        
        Args:
            stage_name: 阶段名称
            
        Returns:
            bool: 是否成功
        """
        stage = self.get_stage(stage_name)
        if stage:
            stage.enable()
            return True
        return False
    
    def disable_stage(self, stage_name: str) -> bool:
        """
        禁用指定阶段
        
        Args:
            stage_name: 阶段名称
            
        Returns:
            bool: 是否成功
        """
        stage = self.get_stage(stage_name)
        if stage:
            stage.disable()
            return True
        return False
    
    def enable(self):
        """启用管道"""
        self.enabled = True
        log(_("pipeline_manager.pipeline_enabled"), "info", pipeline=self.name)
    
    def disable(self):
        """禁用管道"""
        self.enabled = False
        log(_("pipeline_manager.pipeline_disabled"), "info", pipeline=self.name)
    
    def set_stop_on_error(self, stop: bool):
        """
        设置是否在错误时停止
        
        Args:
            stop: 是否停止
        """
        self.stop_on_error = stop
        log(_("pipeline_manager.stop_on_error_set"), "debug",
            pipeline=self.name, stop=stop)
