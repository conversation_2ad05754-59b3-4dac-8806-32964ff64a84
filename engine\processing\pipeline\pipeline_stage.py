# -*- coding: utf-8 -*-
"""
处理管道阶段 - 管道中的单个处理步骤
"""

import time
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Callable, List
from engine.utils.logger import log
from engine.utils.i18n import _
from .data_flow import DataContext


class PipelineStage(ABC):
    """
    管道阶段基类
    定义管道中单个处理步骤的标准接口
    """
    
    def __init__(self, name: str, description: Optional[str] = None):
        """
        初始化管道阶段
        
        Args:
            name: 阶段名称
            description: 阶段描述
        """
        self.name = name
        self.description = description or name
        self.enabled = True
        self.error_handler: Optional[Callable] = None
        
    @abstractmethod
    def process(self, context: DataContext) -> bool:
        """
        处理数据 - 子类必须实现
        
        Args:
            context: 数据上下文
            
        Returns:
            bool: 处理是否成功
        """
        pass
    
    def execute(self, context: DataContext) -> bool:
        """
        执行阶段处理 - 包含错误处理和日志记录
        
        Args:
            context: 数据上下文
            
        Returns:
            bool: 执行是否成功
        """
        if not self.enabled:
            log(_("pipeline_stage.stage_disabled"), "info", stage=self.name)
            context.add_stage_record(self.name, "skipped", 0, {"reason": "disabled"})
            return True
        
        start_time = time.time()
        
        try:
            log(_("pipeline_stage.stage_starting"), "info", stage=self.name)
            
            # 验证输入
            if not self.validate_input(context):
                error_msg = _("pipeline_stage.input_validation_failed", stage=self.name)
                context.add_error(error_msg, self.name)
                context.add_stage_record(self.name, "failed", time.time() - start_time, 
                                       {"error": "input_validation_failed"})
                return False
            
            # 执行处理
            success = self.process(context)
            duration = time.time() - start_time
            
            if success:
                log(_("pipeline_stage.stage_completed"), "info", 
                    stage=self.name, duration=duration)
                context.add_stage_record(self.name, "completed", duration)
                return True
            else:
                error_msg = _("pipeline_stage.stage_failed", stage=self.name)
                context.add_error(error_msg, self.name)
                context.add_stage_record(self.name, "failed", duration, 
                                       {"error": "processing_failed"})
                return False
                
        except Exception as e:
            duration = time.time() - start_time
            error_msg = _("pipeline_stage.stage_exception", stage=self.name, error=str(e))
            log(error_msg, "error")
            context.add_error(error_msg, self.name)
            context.add_stage_record(self.name, "error", duration, 
                                   {"error": "exception", "exception": str(e)})
            
            # 尝试错误处理
            if self.error_handler:
                try:
                    return self.error_handler(e, context)
                except Exception as handler_error:
                    log(_("pipeline_stage.error_handler_failed"), "error",
                        stage=self.name, error=str(handler_error))
            
            return False
    
    def validate_input(self, context: DataContext) -> bool:
        """
        验证输入数据 - 子类可以重写
        
        Args:
            context: 数据上下文
            
        Returns:
            bool: 输入是否有效
        """
        return True
    
    def set_error_handler(self, handler: Callable[[Exception, DataContext], bool]):
        """
        设置错误处理器
        
        Args:
            handler: 错误处理函数
        """
        self.error_handler = handler
    
    def enable(self):
        """启用阶段"""
        self.enabled = True
        log(_("pipeline_stage.stage_enabled"), "debug", stage=self.name)
    
    def disable(self):
        """禁用阶段"""
        self.enabled = False
        log(_("pipeline_stage.stage_disabled"), "debug", stage=self.name)


class FunctionStage(PipelineStage):
    """
    函数式管道阶段 - 将现有函数包装为管道阶段
    用于快速将现有处理逻辑集成到管道中
    """
    
    def __init__(self, name: str, process_function: Callable[[DataContext], bool],
                 description: Optional[str] = None,
                 input_validator: Optional[Callable[[DataContext], bool]] = None):
        """
        初始化函数式阶段
        
        Args:
            name: 阶段名称
            process_function: 处理函数
            description: 阶段描述
            input_validator: 输入验证函数
        """
        super().__init__(name, description)
        self.process_function = process_function
        self.input_validator = input_validator
    
    def process(self, context: DataContext) -> bool:
        """
        执行处理函数
        
        Args:
            context: 数据上下文
            
        Returns:
            bool: 处理是否成功
        """
        return self.process_function(context)
    
    def validate_input(self, context: DataContext) -> bool:
        """
        验证输入
        
        Args:
            context: 数据上下文
            
        Returns:
            bool: 输入是否有效
        """
        if self.input_validator:
            return self.input_validator(context)
        return super().validate_input(context)


class ConditionalStage(PipelineStage):
    """
    条件管道阶段 - 根据条件决定是否执行
    """
    
    def __init__(self, name: str, condition: Callable[[DataContext], bool],
                 true_stage: PipelineStage, false_stage: Optional[PipelineStage] = None,
                 description: Optional[str] = None):
        """
        初始化条件阶段
        
        Args:
            name: 阶段名称
            condition: 条件函数
            true_stage: 条件为真时执行的阶段
            false_stage: 条件为假时执行的阶段
            description: 阶段描述
        """
        super().__init__(name, description)
        self.condition = condition
        self.true_stage = true_stage
        self.false_stage = false_stage
    
    def process(self, context: DataContext) -> bool:
        """
        根据条件执行相应阶段
        
        Args:
            context: 数据上下文
            
        Returns:
            bool: 处理是否成功
        """
        if self.condition(context):
            log(_("pipeline_stage.condition_true"), "debug", stage=self.name)
            return self.true_stage.execute(context)
        elif self.false_stage:
            log(_("pipeline_stage.condition_false"), "debug", stage=self.name)
            return self.false_stage.execute(context)
        else:
            log(_("pipeline_stage.condition_false_no_action"), "debug", stage=self.name)
            return True


class ParallelStage(PipelineStage):
    """
    并行管道阶段 - 并行执行多个子阶段
    """
    
    def __init__(self, name: str, stages: List[PipelineStage],
                 description: Optional[str] = None, fail_fast: bool = True):
        """
        初始化并行阶段
        
        Args:
            name: 阶段名称
            stages: 并行执行的阶段列表
            description: 阶段描述
            fail_fast: 是否在第一个失败时立即停止
        """
        super().__init__(name, description)
        self.stages = stages
        self.fail_fast = fail_fast
    
    def process(self, context: DataContext) -> bool:
        """
        并行执行所有子阶段
        
        Args:
            context: 数据上下文
            
        Returns:
            bool: 处理是否成功
        """
        # 注意：这里是简化的并行实现，实际生产环境可能需要真正的并发处理
        results = []
        
        for stage in self.stages:
            try:
                result = stage.execute(context)
                results.append(result)
                
                if self.fail_fast and not result:
                    log(_("pipeline_stage.parallel_fail_fast"), "warning",
                        stage=self.name, failed_stage=stage.name)
                    return False
                    
            except Exception as e:
                log(_("pipeline_stage.parallel_stage_error"), "error",
                    stage=self.name, failed_stage=stage.name, error=str(e))
                results.append(False)
                
                if self.fail_fast:
                    return False
        
        # 所有阶段都必须成功
        success = all(results)
        log(_("pipeline_stage.parallel_completed"), "info",
            stage=self.name, success=success, total=len(results), 
            successful=sum(results))
        
        return success
