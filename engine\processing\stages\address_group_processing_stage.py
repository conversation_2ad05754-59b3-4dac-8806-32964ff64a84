#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
地址对象组处理阶段
负责将FortiGate地址对象组转换为NTOS格式
依赖于地址对象处理阶段的结果
"""

from typing import Dict, Any, List
from engine.processing.pipeline.pipeline_stage import PipelineStage
from engine.processing.pipeline.data_flow import DataContext
from engine.utils.logger import log
from engine.utils.i18n import _
from lxml import etree


class AddressGroupProcessor:
    """
    地址对象组处理器 - 完全重新实现，借鉴旧架构核心逻辑
    """

    def __init__(self):
        pass

    def validate_group_members(self, members: List[str], converted_addresses: Dict) -> List[str]:
        """
        验证地址组成员是否在已转换的地址对象中存在（借鉴旧架构逻辑）

        Args:
            members: 地址组成员列表
            converted_addresses: 已转换的地址对象字典

        Returns: List[str]: 有效的成员列表
        """
        valid_members = []

        for member in members:
            if member in converted_addresses:
                # 使用转换后的地址对象名称
                valid_members.append(converted_addresses[member]['name'])
            else:
                log(_("address_group_processor.member_not_found", member=member), "warning")

        return valid_members

    def process_address_groups(self, address_groups: Dict, converted_addresses: Dict) -> Dict[str, Any]:
        """
        处理地址对象组字典

        Args:
            address_groups: 地址对象组字典
            converted_addresses: 已转换的地址对象字典

        Returns:
            Dict: 处理结果
        """
        result = {
            "converted": {},
            "skipped": {},
            "failed": {},
            "converted_count": 0,
            "skipped_count": 0,
            "failed_count": 0,
            "details": []
        }

        for group_name, group_config in address_groups.items():
            try:
                # 获取成员列表（借鉴旧架构的member字段处理）
                members = group_config.get('member', [])
                if not members:
                    # 检查是否使用了其他字段名
                    members = group_config.get('members', [])

                if not members:
                    reason = _("address_group_processing.empty_group")
                    log(_("address_group_processor.skip_empty_group", group=group_name), "warning")
                    self._add_skipped_result(result, group_name, group_config, reason)
                    continue

                # 确保members是列表格式
                if not isinstance(members, list):
                    members = [members]

                # 验证成员是否都已转换
                valid_members = self.validate_group_members(members, converted_addresses)

                # 改进的成功率逻辑：如果有部分有效成员，仍然创建组
                if not valid_members:
                    reason = _("address_group_processing.no_valid_members")
                    log(_("address_group_processor.skip_no_valid_members", group=group_name), "warning")
                    self._add_skipped_result(result, group_name, group_config, reason)
                    continue
                elif len(valid_members) < len(members):
                    # 部分成员有效的情况，记录警告但继续处理
                    invalid_count = len(members) - len(valid_members)
                    log(_("address_group_processor.partial_members_valid",
                          group=group_name,
                          valid=len(valid_members),
                          invalid=invalid_count), "warning")

                # 创建地址对象组（符合NTOS格式）
                converted_group = {
                    "name": group_name,
                    "type": "address_group",
                    "members": valid_members,
                    "description": group_config.get("comment", ""),
                    "enabled": True,
                    "original_members": members,  # 保留原始成员信息用于调试
                    "valid_members_count": len(valid_members),
                    "total_members_count": len(members)
                }

                result["converted"][group_name] = converted_group
                result["converted_count"] += 1
                result["details"].append({
                    "name": group_name,
                    "status": "success",
                    "type": "address_group",
                    "members_count": len(valid_members),
                    "converted": converted_group
                })
                log(_("address_group_processor.conversion_success",
                      group=group_name,
                      valid=len(valid_members),
                      total=len(members)), "debug")

            except Exception as e:
                reason = _("address_group_processing.processing_error", error=str(e))
                self._add_failed_result(result, group_name, group_config, reason)

        return result

    def _add_skipped_result(self, result: Dict, name: str, config: Dict, reason: str):
        """添加跳过的结果"""
        result["skipped"][name] = config
        result["skipped_count"] += 1
        result["details"].append({
            "name": name,
            "status": "skipped",
            "reason": reason,
            "original": config
        })

    def _add_failed_result(self, result: Dict, name: str, config: Dict, reason: str):
        """添加失败的结果"""
        result["failed"][name] = config
        result["failed_count"] += 1
        result["details"].append({
            "name": name,
            "status": "failed",
            "reason": reason,
            "original": config
        })


class AddressGroupProcessingStage(PipelineStage):
    """
    地址对象组处理阶段 - 完全重新实现

    负责处理FortiGate地址对象组配置，转换为NTOS格式并生成XML片段
    依赖于地址对象处理阶段的结果
    """

    def __init__(self):
        super().__init__("address_group_processing", _("address_group_processing_stage.description"))
        self.processor = AddressGroupProcessor()
    
    def validate_input(self, context: DataContext) -> bool:
        """
        验证输入数据

        Args:
            context: 数据上下文

        Returns:
            bool: 验证是否通过
        """
        # 检查是否有地址对象处理结果
        address_result = context.get_data("address_processing_result")
        if not address_result:
            log(_("address_group_processing_stage.no_address_result"), "warning")
            return False

        return True
    
    def process(self, context: DataContext) -> bool:
        """
        执行地址对象组处理
        
        Args:
            context: 数据上下文
            
        Returns:
            bool: 处理是否成功
        """
        log(_("address_group_processing_stage.starting"))
        
        try:
            # 获取配置数据
            config_data = context.get_data("config_data", {})
            address_groups_data = config_data.get("address_groups", [])
            
            # 转换数据格式：从列表转换为字典（兼容解析器输出格式）
            address_groups = {}
            if isinstance(address_groups_data, list):
                for group in address_groups_data:
                    if isinstance(group, dict) and 'name' in group:
                        address_groups[group['name']] = group
            elif isinstance(address_groups_data, dict):
                address_groups = address_groups_data
            
            log(_("address_group_processor.data_conversion_complete",
                  original_type=type(address_groups_data).__name__,
                  converted_type=type(address_groups).__name__,
                  count=len(address_groups)), "debug")
            
            if not address_groups:
                log(_("address_group_processing_stage.no_address_groups"))
                context.set_data("address_group_processing_result", self._empty_result())
                return True
            
            # 获取地址对象处理结果
            address_result = context.get_data("address_processing_result", {})
            converted_addresses = address_result.get("address_objects", {}).get("converted", {})

            if not converted_addresses:
                log(_("address_group_processor.no_converted_addresses"), "warning")
                context.set_data("address_group_processing_result", self._empty_result())
                return True

            # 使用新的处理器处理地址对象组
            group_result = self.processor.process_address_groups(address_groups, converted_addresses)

            # 生成XML片段
            xml_fragment = self._generate_address_group_xml_fragment(group_result)

            # 构建处理结果（包含地址对象组和XML片段）
            processing_result = {
                "address_groups": group_result,
                "xml_fragment": xml_fragment,
                "statistics": {
                    "total_groups": len(address_groups),
                    "converted_groups": group_result['converted_count'],
                    "skipped_groups": group_result['skipped_count'],
                    "failed_groups": group_result['failed_count']
                }
            }

            context.set_data("address_group_processing_result", processing_result)

            log(_("address_group_processor.processing_complete",
                  success=group_result['converted_count'],
                  total=len(address_groups)), "info")

            # 记录用户日志
            from engine.utils.user_log_formatter import record_stage_user_log
            record_stage_user_log("address_group_processing", processing_result)

            return True
            
        except Exception as e:
            error_msg = f"地址对象组处理失败: {str(e)}"
            error_msg = _("address_group_processing_stage.processing_failed", error=str(e))
            log(error_msg, "error")
            error_msg = _("address_group_processor.detailed_error_info",  error=error_msg)
            log(error_msg, "error")
            context.set_data("address_group_processing_result", self._empty_result())
            context.add_error(error_msg, self.name)
            return False

    def _generate_address_group_xml_fragment(self, group_result: Dict[str, Any]) -> str:
        """
        生成符合YANG模型的地址对象组XML片段

        Args:
            group_result: 地址对象组处理结果

        Returns:
            str: XML片段字符串
        """
        try:
            # 创建network-obj根元素（使用与地址对象相同的NTOS命名空间，避免命名空间前缀）
            network_obj = etree.Element("network-obj", xmlns="urn:ruijie:ntos")

            # 处理转换成功的地址对象组
            converted_groups = group_result.get("converted", {})
            if not converted_groups:
                log(_("address_group_processor.no_converted_groups"), "info")
                return ""

            # 导入名称验证工具
            from engine.utils.name_validator import clean_ntos_name, validate_ntos_name, create_unique_name, clean_ntos_description

            # 用于跟踪已使用的组名称，避免重复
            used_group_names = set()

            for group_name, group_config in converted_groups.items():
                # 清理和验证地址组名称
                original_group_name = group_name
                clean_group_name = clean_ntos_name(group_name, 64)

                # 验证名称
                is_valid, error_msg = validate_ntos_name(clean_group_name)
                if not is_valid:
                    error_msg = _("address_group_processor.invalid_group_name", group=original_group_name, error=error_msg)
                    log(error_msg, "warning")
                    clean_group_name = create_unique_name(f"group_{len(used_group_names)}", used_group_names, 64)

                # 确保组名称唯一
                if clean_group_name in used_group_names:
                    clean_group_name = create_unique_name(clean_group_name, used_group_names, 64)

                used_group_names.add(clean_group_name)

                # 创建address-group元素（符合YANG模型）
                address_group = etree.SubElement(network_obj, "address-group")

                # 添加name元素
                name_elem = etree.SubElement(address_group, "name")
                name_elem.text = clean_group_name

                # 添加description元素（如果有）
                description = group_config.get("description", "")
                if description:
                    # 清理描述
                    clean_desc = clean_ntos_description(description, 255)
                    if clean_desc:
                        desc_elem = etree.SubElement(address_group, "description")
                        desc_elem.text = clean_desc

                # 添加address-set元素（符合YANG模型约束）
                members = group_config.get("members", [])
                if not members:
                    log(_("address_group_processor.group_no_members_yang_violation", group=group_name), "warning")
                    # 移除已创建的address-group元素，因为违反YANG约束
                    network_obj.remove(address_group)
                    continue

                # 导入名称验证工具
                from engine.utils.name_validator import clean_ntos_name, validate_ntos_name, create_unique_name

                # 用于跟踪已使用的成员名称，避免重复
                used_member_names = set()

                for member in members:
                    # 清理和验证成员名称
                    original_member = member
                    clean_member = clean_ntos_name(member, 64)

                    # 验证名称
                    is_valid, error_msg = validate_ntos_name(clean_member)
                    if not is_valid:
                        error_msg = _("address_group_processor.invalid_member_name", group=group_name, member=original_member, error=error_msg)
                        log(error_msg, "warning")
                        clean_member = create_unique_name(f"member_{len(used_member_names)}", used_member_names, 64)

                    # 确保成员名称唯一
                    if clean_member in used_member_names:
                        clean_member = create_unique_name(clean_member, used_member_names, 64)

                    used_member_names.add(clean_member)

                    # 创建address-set元素
                    address_set = etree.SubElement(address_group, "address-set")

                    # 添加name元素（引用地址对象名称）
                    member_name_elem = etree.SubElement(address_set, "name")
                    member_name_elem.text = clean_member



            # 转换为字符串
            xml_str = etree.tostring(network_obj, encoding='unicode', pretty_print=True)


            log(_("address_group_processor.xml_fragment_success", count=len(converted_groups)), "info")
            return xml_str

        except Exception as e:
            log(_("address_group_processor.xml_fragment_failed", error=str(e)), "error")
            return ""

    # 旧的处理方法已被新的AddressGroupProcessor替代
    
    def _empty_result(self) -> Dict[str, Any]:
        """
        返回空的处理结果

        Returns: Dict[str, Any]: 空结果
        """
        return {
            "address_groups": {
                "converted": {},
                "skipped": {},
                "failed": {},
                "converted_count": 0,
                "skipped_count": 0,
                "failed_count": 0,
                "details": []
            },
            "xml_fragment": "",
            "statistics": {
                "total_groups": 0,
                "converted_groups": 0,
                "skipped_groups": 0,
                "failed_groups": 0
            }
        }
