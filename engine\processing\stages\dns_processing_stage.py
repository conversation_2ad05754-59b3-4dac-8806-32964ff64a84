#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
DNS处理阶段
负责处理FortiGate的DNS配置并转换为NTOS格式
"""

import re
from typing import Dict, Any, List
from engine.processing.pipeline.pipeline_stage import PipelineStage
from engine.processing.pipeline.data_flow import DataContext
from engine.utils.logger import log
from engine.utils.i18n import _
from lxml import etree


class DNSProcessor:
    """
    DNS处理器 - 完全重新实现，借鉴旧架构核心逻辑
    """

    def __init__(self):
        # NTOS DNS最大服务器数量限制（遵循NTOS YANG模型）
        self.max_dns_servers = 3

    def validate_ip_address(self, ip_address: str) -> bool:
        """
        验证IP地址格式（借鉴旧架构逻辑）

        Args:
            ip_address: IP地址字符串

        Returns:
            bool: 是否为有效IP地址
        """
        if not ip_address:
            return False

        try:
            # IPv4地址验证
            ipv4_pattern = r'^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$'
            match = re.match(ipv4_pattern, ip_address)

            if match:
                # 检查每个数字是否在0-255范围内
                for group in match.groups():
                    if int(group) > 255:
                        return False
                return True

            # 简单的IPv6地址验证
            if ':' in ip_address:
                parts = ip_address.split(':')
                if len(parts) <= 8:  # IPv6最多8个部分
                    return True

            return False
        except (ValueError, AttributeError):
            return False

    def validate_domain_name(self, domain: str) -> bool:
        """
        验证域名格式（借鉴旧架构逻辑）

        Args:
            domain: 域名字符串

        Returns:
            bool: 是否为有效域名
        """
        if not domain or len(domain) > 253:
            return False

        # 基本域名格式验证
        domain_pattern = r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$'
        return bool(re.match(domain_pattern, domain))

    def process_dns_servers(self, system_settings: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理DNS服务器配置（修复：保持FortiGate原始配置，不进行任何补全）

        Args:
            system_settings: 系统设置配置

        Returns: Dict[str, Any]: DNS服务器处理结果
        """
        result = {
            "dns_servers": [],
            "primary_dns": "",
            "secondary_dns": "",
            "tertiary_dns": "",
            "configured_count": 0,
            "valid_count": 0,
            "using_defaults": False
        }

        # 提取FortiGate DNS服务器配置
        dns_servers = []

        # 处理primary DNS（FortiGate解析器使用"primary"字段）
        primary_dns = system_settings.get("primary", "")
        if primary_dns and self.validate_ip_address(primary_dns):
            dns_servers.append(primary_dns)
            result["primary_dns"] = primary_dns  # 修复：使用正确的键名
            result["configured_count"] += 1

        # 处理secondary DNS（FortiGate解析器使用"secondary"字段）
        secondary_dns = system_settings.get("secondary", "")
        if secondary_dns and self.validate_ip_address(secondary_dns):
            dns_servers.append(secondary_dns)
            result["secondary_dns"] = secondary_dns  # 修复：使用正确的键名
            result["configured_count"] += 1

        # 处理tertiary DNS（只在实际存在时处理）
        tertiary_dns = system_settings.get("tertiary", "")
        if tertiary_dns and self.validate_ip_address(tertiary_dns):
            dns_servers.append(tertiary_dns)
            result["tertiary_dns"] = tertiary_dns  # 修复：使用正确的键名
            result["configured_count"] += 1

        # 修复：不自动补全DNS服务器，保持FortiGate原始配置
        if not dns_servers:
            log(_("dns_processor.no_dns_servers_configured"), "info")
            # 不添加任何默认DNS服务器，保持空配置
            result["using_defaults"] = False
        else:
            # 使用实际配置的DNS服务器，不进行任何补全
            log(_("dns_processor.using_configured_dns", count=len(dns_servers)), "info")
            result["using_defaults"] = False

        # 限制DNS服务器数量（最多3个）
        if len(dns_servers) > self.max_dns_servers:
            log(_("dns_processor.dns_servers_limit_exceeded", original=len(dns_servers), limit=self.max_dns_servers), "warning")
            dns_servers = dns_servers[:self.max_dns_servers]

        result["dns_servers"] = dns_servers
        result["valid_count"] = len(dns_servers)



        return result

    def process_static_hosts(self, system_settings: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理静态域名解析配置（借鉴旧架构逻辑）

        Args:
            system_settings: 系统设置配置

        Returns: Dict[str, Any]: 静态域名处理结果
        """
        result = {
            "static_hosts": [],
            "ipv6_hosts": [],
            "total_hosts": 0,
            "valid_hosts": 0
        }

        # 处理静态域名配置（如果存在）
        # FortiGate中的静态域名配置通常在system dns-database中
        dns_database = system_settings.get("dns-database", {})

        if isinstance(dns_database, dict):
            for host_name, host_config in dns_database.items():
                if isinstance(host_config, dict):
                    ip_address = host_config.get("ip", "")

                    if self.validate_domain_name(host_name) and self.validate_ip_address(ip_address):
                        if ':' in ip_address:  # IPv6
                            result["ipv6_hosts"].append({
                                "host_name": host_name,
                                "ipv6_address": ip_address
                            })
                        else:  # IPv4
                            result["static_hosts"].append({
                                "host_name": host_name,
                                "ip_address": ip_address
                            })
                        result["valid_hosts"] += 1

                    result["total_hosts"] += 1



        return result

    def process_dns_configuration(self, system_settings: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理完整的DNS配置

        Args:
            system_settings: 系统设置配置

        Returns: Dict[str, Any]: DNS配置处理结果
        """
        result = {
            "dns_enabled": True,
            "dns_servers": {},
            "static_hosts": {},
            "dns_proxy": {
                "enabled": False
            },
            "statistics": {
                "total_dns_servers": 0,
                "valid_dns_servers": 0,
                "total_static_hosts": 0,
                "valid_static_hosts": 0
            }
        }

        # 处理DNS服务器
        dns_servers_result = self.process_dns_servers(system_settings)
        result["dns_servers"] = dns_servers_result
        result["statistics"]["total_dns_servers"] = dns_servers_result["configured_count"]
        result["statistics"]["valid_dns_servers"] = dns_servers_result["valid_count"]

        # 处理静态域名
        static_hosts_result = self.process_static_hosts(system_settings)
        result["static_hosts"] = static_hosts_result
        result["statistics"]["total_static_hosts"] = static_hosts_result["total_hosts"]
        result["statistics"]["valid_static_hosts"] = static_hosts_result["valid_hosts"]

        # 处理DNS代理设置（如果存在）
        dns_proxy_value = system_settings.get("dns_proxy", "disable")
        if isinstance(dns_proxy_value, str):
            dns_proxy_enabled = dns_proxy_value.lower() == "enable"
        else:
            dns_proxy_enabled = bool(dns_proxy_value)
        result["dns_proxy"]["enabled"] = dns_proxy_enabled

        log(_("dns_processor.dns_config_processing_complete",
              servers=result['statistics']['valid_dns_servers'],
              hosts=result['statistics']['valid_static_hosts']), "info")

        return result

    def process_enhanced_dns_configuration(self, system_settings: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理增强的DNS配置 - 支持DNS代理、客户端和安全功能

        Args:
            system_settings: 系统设置配置

        Returns: Dict[str, Any]: 增强的DNS配置处理结果
        """
        # 获取基础DNS配置
        result = self.process_dns_configuration(system_settings)

        # 增强：处理DNS代理配置
        result.update(self._process_dns_proxy_config(system_settings))

        # 增强：处理DNS客户端完整配置
        result.update(self._process_dns_client_config(system_settings))

        # 增强：处理DNS安全功能
        result.update(self._process_dns_security_features(system_settings))

        # 检查是否有高级DNS功能配置
        has_advanced = (result.get("dns_proxy_enhanced", {}).get("enabled", False) or
                       result.get("dns_over_tls_enabled", False) or
                       result.get("dns_client_enhanced", {}).get("domain_lookup_enabled", False))
        result["has_advanced_features"] = has_advanced

        log(_("dns_processor.enhanced_dns_configuration_complete",
              has_advanced=has_advanced), "debug")

        return result

    def _process_dns_proxy_config(self, system_settings: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理DNS代理配置 - 新增方法

        Args:
            system_settings: 系统设置配置

        Returns: Dict[str, Any]: DNS代理配置结果
        """
        result = {
            "dns_proxy_enhanced": {
                "enabled": False,
                "forward_servers": [],
                "allow_query": [],
                "blackhole": [],
                "static_dns": []
            }
        }

        # 检查FortiGate DNS代理配置
        dns_proxy_config = system_settings.get("dns-proxy", {})
        if dns_proxy_config:
            result["dns_proxy_enhanced"]["enabled"] = dns_proxy_config.get("enabled", False)

            # 处理代理服务器配置
            forward_config = dns_proxy_config.get("forward", {})
            if forward_config:
                servers = forward_config.get("server", [])
                if servers:
                    result["dns_proxy_enhanced"]["forward_servers"] = servers

                # 处理本地转发优先级
                priority = forward_config.get("priority", "")
                if priority:
                    result["dns_proxy_enhanced"]["forward_priority"] = priority

            # 处理允许查询的客户端
            allow_query = dns_proxy_config.get("allow-query", [])
            if allow_query:
                result["dns_proxy_enhanced"]["allow_query"] = allow_query

            # 处理黑洞配置
            blackhole = dns_proxy_config.get("blackhole", [])
            if blackhole:
                result["dns_proxy_enhanced"]["blackhole"] = blackhole

            # 处理静态DNS记录
            static_dns = dns_proxy_config.get("static-dns", [])
            if static_dns:
                result["dns_proxy_enhanced"]["static_dns"] = static_dns

        return result

    def _process_dns_client_config(self, system_settings: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理DNS客户端完整配置 - 新增方法

        Args:
            system_settings: 系统设置配置

        Returns: Dict[str, Any]: DNS客户端配置结果
        """
        result = {
            "dns_client_enhanced": {
                "domain_lookup_enabled": True,
                "source_interface": "",
                "source_address": "",
                "static_ipv4_hosts": [],
                "static_ipv6_hosts": []
            }
        }

        # 处理DNS客户端配置
        dns_client_config = system_settings.get("dns-client", {})
        if dns_client_config:
            # IP域名查找配置
            ip_domain_lookup = dns_client_config.get("ip-domain-lookup", {})
            if ip_domain_lookup:
                result["dns_client_enhanced"]["domain_lookup_enabled"] = ip_domain_lookup.get("enabled", True)
                result["dns_client_enhanced"]["source_interface"] = ip_domain_lookup.get("source-interface", "")
                result["dns_client_enhanced"]["source_address"] = ip_domain_lookup.get("source-address", "")

            # 静态主机配置
            ip_hosts = dns_client_config.get("ip-host", [])
            if ip_hosts:
                result["dns_client_enhanced"]["static_ipv4_hosts"] = ip_hosts

            ipv6_hosts = dns_client_config.get("ipv6-host", [])
            if ipv6_hosts:
                result["dns_client_enhanced"]["static_ipv6_hosts"] = ipv6_hosts

        return result

    def _process_dns_security_features(self, system_settings: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理DNS安全功能配置 - 新增方法

        Args:
            system_settings: 系统设置配置

        Returns: Dict[str, Any]: DNS安全功能配置结果
        """
        result = {
            "dns_over_tls_enabled": False,
            "dns_security_features": {
                "tls_port": 853,
                "tls_certificate": "",
                "tls_verify": True,
                "dns_filtering_enabled": False,
                "malware_protection": False
            }
        }

        # 检查DNS-over-TLS配置
        dns_security = system_settings.get("dns-security", {})
        if dns_security:
            result["dns_over_tls_enabled"] = dns_security.get("tls-enabled", False)

            if result["dns_over_tls_enabled"]:
                result["dns_security_features"]["tls_port"] = dns_security.get("tls-port", 853)
                result["dns_security_features"]["tls_certificate"] = dns_security.get("certificate", "")
                result["dns_security_features"]["tls_verify"] = dns_security.get("verify-certificate", True)

            # 检查DNS过滤功能
            result["dns_security_features"]["dns_filtering_enabled"] = dns_security.get("filtering-enabled", False)
            result["dns_security_features"]["malware_protection"] = dns_security.get("malware-protection", False)

        return result


class DNSProcessingStage(PipelineStage):
    """
    DNS处理阶段 - 完全重新实现

    负责处理FortiGate DNS配置，转换为NTOS格式并生成XML片段
    支持DNS服务器、静态域名解析、DNS代理等配置
    """

    def __init__(self):
        super().__init__("dns_processing", _("dns_processing_stage.description"))
        self.processor = DNSProcessor()
    
    def validate_input(self, context: DataContext) -> bool:
        """
        验证输入数据
        
        Args:
            context: 数据上下文
            
        Returns:
            bool: 验证是否通过
        """
        config_data = context.get_data("config_data")
        if not config_data:
            context.add_error(_("dns_processing_stage.no_config_data"))
            return False
        
        return True
    
    def process(self, context: DataContext) -> bool:
        """
        执行DNS处理

        Args:
            context: 数据上下文

        Returns:
            bool: 处理是否成功
        """
        log(_("dns_processor.start_dns_processing"), "info")

        try:
            # 获取配置数据
            config_data = context.get_data("config_data", {})

            # 首先尝试从dns_config中获取DNS配置（FortiGate解析器的存储位置）
            dns_config = config_data.get("dns_config", {})
            system_settings = config_data.get("system_settings", {})

            # 如果dns_config中有配置，将其转换为system_settings格式
            if dns_config:
                # 转换字段名称以匹配处理器期望的格式
                if "primary" in dns_config:
                    system_settings["primary"] = dns_config["primary"]
                if "secondary" in dns_config:
                    system_settings["secondary"] = dns_config["secondary"]
                if "tertiary" in dns_config:
                    system_settings["tertiary"] = dns_config["tertiary"]
                if "dns_proxy" in dns_config:
                    system_settings["dns_proxy"] = dns_config["dns_proxy"]
            else:
                pass

            if not system_settings:
                log(_("dns_processor.no_system_settings_found"), "info")
                # 没有系统设置时，使用空配置（不添加默认DNS）
                system_settings = {}

            # 使用增强的处理器处理DNS配置
            dns_result = self.processor.process_enhanced_dns_configuration(system_settings)

            # 生成XML片段
            xml_fragment = self._generate_dns_xml_fragment(dns_result)

            # 构建处理结果（包含DNS配置和XML片段）
            processing_result = {
                "dns_config": dns_result,
                "xml_fragment": xml_fragment,
                "statistics": {
                    "total_dns_servers": dns_result["statistics"]["total_dns_servers"],
                    "valid_dns_servers": dns_result["statistics"]["valid_dns_servers"],
                    "total_static_hosts": dns_result["statistics"]["total_static_hosts"],
                    "valid_static_hosts": dns_result["statistics"]["valid_static_hosts"]
                }
            }

            # 存储处理结果
            context.set_data("dns_processing_result", processing_result)

            log(_("dns_processor.dns_processing_stage_complete",
                  servers=dns_result['statistics']['valid_dns_servers'],
                  hosts=dns_result['statistics']['valid_static_hosts']), "info")

            # 记录用户日志
            from engine.utils.user_log_formatter import record_stage_user_log
            record_stage_user_log("dns_processing", processing_result)

            return True

        except Exception as e:
            error_msg = f"DNS处理失败: {str(e)}"
            log(error_msg, "error")
            context.set_data("dns_processing_result", self._empty_result())
            context.add_error(error_msg, self.name)
            return False

    def _generate_dns_xml_fragment(self, dns_result: Dict) -> str:
        """
        生成DNS配置XML片段（借鉴旧架构，遵循YANG模型）

        Args:
            dns_result: DNS配置处理结果

        Returns:
            str: XML片段字符串
        """
        try:
            # 创建根容器
            root_elem = etree.Element("dns-config")

            # 生成DNS主配置（包含服务器列表）
            dns_main_xml = self._generate_dns_main_xml(dns_result)
            if dns_main_xml is not None:
                root_elem.append(dns_main_xml)

            # 生成DNS客户端配置
            dns_client_xml = self._generate_dns_client_xml(dns_result)
            if dns_client_xml is not None:
                root_elem.append(dns_client_xml)

            # 生成XML字符串
            xml_string = etree.tostring(
                root_elem,
                encoding='unicode',
                pretty_print=True
            )

            return xml_string

        except Exception as e:
            log(_("dns_processor.dns_xml_generation_failed", error=str(e)), "error")
            return ""

    def _generate_dns_main_xml(self, dns_result: Dict) -> etree.Element:
        """
        生成DNS主配置XML（遵循dns YANG模型）
        包含DNS服务器列表和代理配置

        Args:
            dns_result: DNS配置处理结果

        Returns:
            etree.Element: DNS主配置XML元素
        """
        # 创建dns元素（主DNS配置）
        dns_elem = etree.Element("dns")
        dns_elem.set("xmlns", "urn:ruijie:ntos:params:xml:ns:yang:dns")

        # 获取DNS服务器列表（修复：只处理实际配置的DNS服务器）
        dns_servers = []
        dns_servers_data = dns_result.get("dns_servers", {})

        # 修复：使用正确的键名获取DNS服务器
        if dns_servers_data.get("primary_dns"):
            dns_servers.append(dns_servers_data["primary_dns"])
        if dns_servers_data.get("secondary_dns"):
            dns_servers.append(dns_servers_data["secondary_dns"])
        if dns_servers_data.get("tertiary_dns"):
            dns_servers.append(dns_servers_data["tertiary_dns"])

        # 添加DNS服务器配置（遵循YANG模型：list server, key "address", max-elements 3）
        # 修复：只生成实际存在且非空的DNS服务器
        for seq, server_ip in enumerate(dns_servers, 1):
            if server_ip and server_ip.strip():
                server_elem = etree.SubElement(dns_elem, "server")

                # 添加address字段（YANG模型要求的key）
                address_elem = etree.SubElement(server_elem, "address")
                address_elem.text = server_ip.strip()



        # 添加增强的DNS代理配置
        proxy_elem = etree.SubElement(dns_elem, "proxy")

        # 基础代理启用配置
        proxy_enabled = (dns_result.get("dns_proxy", {}).get("enabled", False) or
                        dns_result.get("dns_proxy_enhanced", {}).get("enabled", False))
        enabled_elem = etree.SubElement(proxy_elem, "enabled")
        enabled_elem.text = "true" if proxy_enabled else "false"

        # 增强：添加代理转发服务器配置
        dns_proxy_enhanced = dns_result.get("dns_proxy_enhanced", {})
        if dns_proxy_enhanced.get("enabled", False):
            # 添加转发配置
            forward_servers = dns_proxy_enhanced.get("forward_servers", [])
            if forward_servers:
                forward_elem = etree.SubElement(proxy_elem, "query-route")
                forward_container = etree.SubElement(forward_elem, "forward")

                for i, server in enumerate(forward_servers[:3], 1):  # 最多3个转发服务器
                    server_elem = etree.SubElement(forward_container, "server")
                    seq_elem = etree.SubElement(server_elem, "seq")
                    seq_elem.text = str(i)
                    addr_elem = etree.SubElement(server_elem, "addr")
                    addr_elem.text = server.get("addr", "")

            # 添加允许查询配置
            allow_query = dns_proxy_enhanced.get("allow_query", [])
            if allow_query:
                for query_addr in allow_query:
                    allow_elem = etree.SubElement(proxy_elem, "allow-query")
                    allow_elem.text = query_addr

            # 添加黑洞配置
            blackhole = dns_proxy_enhanced.get("blackhole", [])
            if blackhole:
                for blackhole_addr in blackhole:
                    blackhole_elem = etree.SubElement(proxy_elem, "blackhole")
                    blackhole_elem.text = blackhole_addr

            # 添加静态DNS记录
            static_dns = dns_proxy_enhanced.get("static_dns", [])
            if static_dns:
                for dns_record in static_dns:
                    static_elem = etree.SubElement(proxy_elem, "static-dns")
                    domain_elem = etree.SubElement(static_elem, "domain-name")
                    domain_elem.text = dns_record.get("domain-name", "")

                    addresses = dns_record.get("address", [])
                    for addr in addresses:
                        addr_elem = etree.SubElement(static_elem, "address")
                        addr_elem.text = addr

        log(_("dns_processor.dns_main_config",
              servers=len(dns_servers),
              proxy_enabled=proxy_enabled), "debug")

        return dns_elem




    def _generate_dns_client_xml(self, dns_result: Dict) -> etree.Element:
        """
        生成DNS客户端XML配置（遵循dns-client YANG模型）

        Args:
            dns_result: DNS配置处理结果

        Returns:
            etree.Element: DNS客户端XML元素
        """
        # 创建dns-client元素
        dns_client_elem = etree.Element("dns-client")
        dns_client_elem.set("xmlns", "urn:ruijie:ntos:params:xml:ns:yang:dns-client")

        # 增强：添加ip-domain-lookup配置
        ip_domain_lookup_elem = etree.SubElement(dns_client_elem, "ip-domain-lookup")

        # 从增强配置中获取域名查找设置
        dns_client_enhanced = dns_result.get("dns_client_enhanced", {})
        domain_lookup_enabled = dns_client_enhanced.get("domain_lookup_enabled", True)
        enabled_elem = etree.SubElement(ip_domain_lookup_elem, "enabled")
        enabled_elem.text = "true" if domain_lookup_enabled else "false"

        # 增强：添加源接口配置
        source_interface = dns_client_enhanced.get("source_interface", "")
        if source_interface:
            source_if_elem = etree.SubElement(ip_domain_lookup_elem, "source-interface")
            source_if_elem.text = source_interface

        # 增强：添加源地址配置
        source_address = dns_client_enhanced.get("source_address", "")
        if source_address:
            source_addr_elem = etree.SubElement(ip_domain_lookup_elem, "source-address")
            source_addr_elem.text = source_address

        # 增强：添加静态域名配置
        static_hosts = dns_result.get("static_hosts", {})

        # 添加IPv4静态域名（从基础配置）
        ipv4_hosts = static_hosts.get("static_hosts", [])
        for host in ipv4_hosts:
            ip_host_elem = etree.SubElement(dns_client_elem, "ip-host")

            host_name_elem = etree.SubElement(ip_host_elem, "host-name")
            host_name_elem.text = host["host_name"]

            ip_address_elem = etree.SubElement(ip_host_elem, "ip-address")
            ip_address_elem.text = host["ip_address"]

        # 增强：添加IPv4静态域名（从增强配置）
        enhanced_ipv4_hosts = dns_client_enhanced.get("static_ipv4_hosts", [])
        for host in enhanced_ipv4_hosts:
            ip_host_elem = etree.SubElement(dns_client_elem, "ip-host")

            host_name_elem = etree.SubElement(ip_host_elem, "host-name")
            host_name_elem.text = host.get("host-name", "")

            ip_address_elem = etree.SubElement(ip_host_elem, "ip-address")
            ip_address_elem.text = host.get("ip-address", "")

        # 添加IPv6静态域名（从基础配置）
        ipv6_hosts = static_hosts.get("ipv6_hosts", [])
        for host in ipv6_hosts:
            ipv6_host_elem = etree.SubElement(dns_client_elem, "ipv6-host")

            host_name_elem = etree.SubElement(ipv6_host_elem, "host-name")
            host_name_elem.text = host["host_name"]

            ipv6_address_elem = etree.SubElement(ipv6_host_elem, "ipv6-address")
            ipv6_address_elem.text = host["ipv6_address"]

        # 增强：添加IPv6静态域名（从增强配置）
        enhanced_ipv6_hosts = dns_client_enhanced.get("static_ipv6_hosts", [])
        for host in enhanced_ipv6_hosts:
            ipv6_host_elem = etree.SubElement(dns_client_elem, "ipv6-host")

            host_name_elem = etree.SubElement(ipv6_host_elem, "host-name")
            host_name_elem.text = host.get("host-name", "")

            ipv6_address_elem = etree.SubElement(ipv6_host_elem, "ipv6-address")
            ipv6_address_elem.text = host.get("ipv6-address", "")

        total_ipv4 = len(ipv4_hosts) + len(enhanced_ipv4_hosts)
        total_ipv6 = len(ipv6_hosts) + len(enhanced_ipv6_hosts)


        return dns_client_elem


    
    def get_dns_servers(self, context: DataContext) -> List[str]:
        """
        获取DNS服务器列表
        
        Args:
            context: 数据上下文
            
        Returns: List[str]: DNS服务器IP地址列表
        """
        dns_result = context.get_data("dns_processing_result", {})
        dns_config = dns_result.get("dns_config", {})
        return dns_config.get("dns_servers", [])
    
    def get_primary_dns(self, context: DataContext) -> str:
        """
        获取主DNS服务器
        
        Args:
            context: 数据上下文
            
        Returns:
            str: 主DNS服务器IP地址
        """
        dns_result = context.get_data("dns_processing_result", {})
        dns_config = dns_result.get("dns_config", {})
        return dns_config.get("primary_dns", "")
    
    def get_secondary_dns(self, context: DataContext) -> str:
        """
        获取辅DNS服务器
        
        Args:
            context: 数据上下文
            
        Returns:
            str: 辅DNS服务器IP地址
        """
        dns_result = context.get_data("dns_processing_result", {})
        dns_config = dns_result.get("dns_config", {})
        return dns_config.get("secondary_dns", "")

    def _empty_result(self) -> Dict[str, Any]:
        """
        返回空的处理结果

        Returns: Dict[str, Any]: 空结果
        """
        return {
            "dns_config": {
                "dns_enabled": True,
                "dns_servers": {
                    "dns_servers": [],
                    "primary_dns": "",
                    "secondary_dns": "",
                    "tertiary_dns": "",
                    "configured_count": 0,
                    "valid_count": 0,
                    "using_defaults": False
                },
                "static_hosts": {
                    "static_hosts": [],
                    "ipv6_hosts": [],
                    "total_hosts": 0,
                    "valid_hosts": 0
                },
                "dns_proxy": {
                    "enabled": False
                },
                "statistics": {
                    "total_dns_servers": 0,
                    "valid_dns_servers": 0,
                    "total_static_hosts": 0,
                    "valid_static_hosts": 0
                }
            },
            "xml_fragment": "",
            "statistics": {
                "total_dns_servers": 0,
                "valid_dns_servers": 0,
                "total_static_hosts": 0,
                "valid_static_hosts": 0
            }
        }
