#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
加密处理阶段
负责在XML生成后进行配置文件加密处理
"""

import os
import tempfile
from typing import Dict, Any, Optional
from engine.processing.pipeline.pipeline_stage import PipelineStage
from engine.utils.i18n import _
from engine.utils.logger import log


class EncryptionStage(PipelineStage):
    """加密处理阶段"""
    
    def __init__(self):
        super().__init__("encryption_processing", _("encryption_stage.description"))
    
    def process(self, context) -> bool:
        """
        执行加密处理

        Args:
            context: 管道上下文，包含XML内容和加密参数

        Returns:
            bool: 处理是否成功
        """
        log(_("encryption_stage.starting"))

        try:
            # 获取加密参数
            encrypt_output = context.data.get('encrypt_output')
            output_file = context.data.get('output_file')
            template_path = context.data.get('template_path')

            # 检查是否需要加密
            if not encrypt_output:
                log(_("encryption_stage.no_encryption_requested"))
                context.data['encryption_result'] = self._empty_result()
                return True

            # 检查YANG验证结果
            yang_validation_result = context.data.get('yang_validation_result', {})
            if yang_validation_result.get('performed') and not yang_validation_result.get('passed'):
                warning_msg = _("encryption_stage.yang_validation_failed_skip_encryption",
                               message=yang_validation_result.get('message', ''))
                log(warning_msg, "warning")
                context.data['encryption_result'] = {
                    'success': False,
                    'skipped': True,
                    'reason': 'YANG验证失败',
                    'yang_validation_message': yang_validation_result.get('message', ''),
                    'message': '由于YANG验证失败，跳过加密处理',
                    'statistics': {
                        'encryption_requested': True,
                        'encryption_performed': False,
                        'yang_validation_failed': True
                    }
                }
                return False  # 返回False因为这是一个错误状态

            # 如果YANG验证通过或未执行，继续加密
            if yang_validation_result.get('performed'):
                log(_("encryption_stage.yang_validation_passed_proceed"), "info")
            else:
                log(_("encryption_stage.yang_validation_not_performed_proceed"), "info")

            # 新架构：优先使用内存中的XML内容，而不是依赖文件
            generated_xml = context.data.get('generated_xml')
            xml_file_path = None

            if generated_xml:
                # 创建临时XML文件用于加密
                xml_file_path = self._create_temp_xml_file(generated_xml, output_file)
                log(_("encryption_stage.using_generated_xml"), "info",
                    temp_file=xml_file_path)
            elif output_file and os.path.exists(output_file):
                # 回退：使用已存在的XML文件
                xml_file_path = output_file
                log(_("encryption_stage.using_existing_xml_file"), "info",
                    file=xml_file_path)
            else:
                # 错误：既没有生成的XML内容，也没有XML文件
                error_msg = f"无法找到XML内容进行加密。generated_xml: {generated_xml is not None}, output_file: {output_file}, file_exists: {os.path.exists(output_file) if output_file else False}"
                log(_("encryption_stage.no_xml_content"), "error", details=error_msg)
                context.add_error(error_msg, self.name)
                return False

            log(_("encryption_stage.starting_encryption",
                  xml_file=xml_file_path, output=encrypt_output))

            # 执行加密处理
            encryption_result = self._perform_encryption(
                xml_file_path, template_path, encrypt_output, context
            )

            # 清理临时文件（如果创建了的话）
            if generated_xml and xml_file_path != output_file:
                self._cleanup_temp_file(xml_file_path)

            # 保存加密结果
            context.data['encryption_result'] = encryption_result

            if encryption_result['success']:
                log(_("encryption_stage.completed",
                      output=encryption_result['encrypted_file']))
                return True
            else:
                log(_("encryption_stage.failed"), "error",
                    error=encryption_result.get('error', '未知错误'))
                context.add_error(f"加密处理失败: {encryption_result.get('error', '未知错误')}", self.name)
                return False

        except Exception as e:
            error_msg = _("encryption_stage.failed", error=str(e))
            log(error_msg, "error")
            context.data['encryption_result'] = self._empty_result()
            context.add_error(f"加密处理失败: {str(e)}", self.name)
            return False
    
    def _perform_encryption(self, xml_file: str, template_path: Optional[str], 
                          encrypt_output: str, context) -> Dict[str, Any]:
        """
        执行实际的加密操作
        
        Args:
            xml_file: XML文件路径
            template_path: 模板路径
            encrypt_output: 加密输出路径
            context: 数据上下文
            
        Returns: Dict[str, Any]: 加密结果
        """
        encryption_result = {
            'success': False,
            'encrypted_file': None,
            'error': None
        }
        
        try:
            # 导入加密模块
            from engine.encrypt import encrypt_config

            # 获取模板路径（如果没有提供）
            if not template_path:
                template_path = self._get_default_template_path(context)
                log(_("encryption_stage.using_default_template", path=template_path))

            log(_("encryption_stage.executing_encryption",
                 xml_file=xml_file, template=template_path, output=encrypt_output))

            # 执行加密操作，借鉴旧架构的调用方式
            success, message = encrypt_config(xml_file, template_path, encrypt_output)

            encryption_result['success'] = success
            encryption_result['message'] = message

            if success:
                # 验证加密文件是否存在
                if os.path.exists(encrypt_output):
                    file_size = os.path.getsize(encrypt_output)
                    encryption_result['encrypted_file'] = encrypt_output
                    encryption_result['file_size'] = file_size
                    log(_("encryption_stage.encryption_success",
                         path=encrypt_output, size=file_size))
                else:
                    encryption_result['success'] = False
                    encryption_result['error'] = "加密文件未生成"
            else:
                encryption_result['error'] = message
                log(_("encryption_stage.encryption_failed",
                      message=message), "error")
                
        except ImportError as e:
            error_msg = f"加密模块导入失败: {str(e)}"
            encryption_result['error'] = error_msg
            error_msg = _("encryption_stage.import_error", error=str(e))
            log(error_msg, "error")
            
        except Exception as e:
            error_msg = f"加密执行失败: {str(e)}"
            encryption_result['error'] = error_msg
            error_msg = _("encryption_stage.execution_error", error=str(e))
            log(error_msg, "error")
        
        return encryption_result
    
    def _get_default_template_path(self, context) -> str:
        """
        获取默认模板路径，借鉴旧架构的get_template_path函数

        Args:
            context: 数据上下文

        Returns:
            str: 默认模板路径
        """
        try:
            model = context.data.get('model', 'z3200s')
            version = context.data.get('version', 'R11')

            # 使用旧架构的get_template_path函数
            from engine.convert import get_template_path
            template_path = get_template_path(model, version)

            if template_path and os.path.exists(template_path):
                return template_path

            # 如果get_template_path返回None或文件不存在，使用备用路径
            fallback_paths = [
                f"engine/data/input/configData/{model}/{version}/running.xml",
                f"data/input/configData/{model}/{version}/running.xml",
                f"engine/data/input/configData/{model}/running.xml",
                "engine/data/input/configData/default/running.xml"
            ]

            for path in fallback_paths:
                if os.path.exists(path):
                    log(_("encryption_stage.using_fallback_template", path=path))
                    return path

            # 如果都不存在，返回第一个备用路径
            return fallback_paths[0]

        except Exception as e:
            error_msg = _("encryption_stage.template_path_error", error=str(e))
            log(error_msg, "warning")
            return "engine/data/input/configData/default/running.xml"
    
    def _create_temp_xml_file(self, xml_content: str, base_output_file: str) -> str:
        """
        创建临时XML文件用于加密

        Args:
            xml_content: XML内容
            base_output_file: 基础输出文件路径（用于确定临时文件位置）

        Returns:
            str: 临时XML文件路径
        """
        try:
            # 确定临时文件路径
            if base_output_file:
                # 在输出文件同目录下创建临时文件
                output_dir = os.path.dirname(base_output_file)
                base_name = os.path.splitext(os.path.basename(base_output_file))[0]
                temp_file_path = os.path.join(output_dir, f"{base_name}_temp_for_encryption.xml")
            else:
                # 使用系统临时目录
                temp_file_path = tempfile.mktemp(suffix='_encryption.xml')

            # 确保目录存在
            os.makedirs(os.path.dirname(temp_file_path), exist_ok=True)

            # 写入XML内容
            with open(temp_file_path, 'w', encoding='utf-8') as f:
                f.write(xml_content)

            log(_("encryption_stage.temp_xml_created"), "debug", path=temp_file_path)
            return temp_file_path

        except Exception as e:
            error_msg = _("encryption_stage.temp_xml_creation_failed", error=str(e))
            log(error_msg, "error")
            raise

    def _cleanup_temp_file(self, temp_file_path: str):
        """
        清理临时文件

        Args:
            temp_file_path: 临时文件路径
        """
        try:
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)
                log(_("encryption_stage.temp_xml_cleaned"), "debug", path=temp_file_path)
        except Exception as e:
            log(_("encryption_stage.temp_xml_cleanup_failed"), "warning",
                path=temp_file_path, error=str(e))

    def _empty_result(self) -> Dict[str, Any]:
        """
        返回空的加密结果

        Returns: Dict[str, Any]: 空结果
        """
        return {
            'success': True,
            'encrypted_file': None,
            'message': '未请求加密',
            'statistics': {
                'encryption_requested': False,
                'encryption_performed': False
            }
        }
