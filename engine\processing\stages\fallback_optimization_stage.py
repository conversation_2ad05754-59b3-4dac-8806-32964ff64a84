"""
备用优化阶段
当四层优化策略模块不可用时的备用实现
"""

import time
from typing import Dict, Any

from engine.processing.pipeline.pipeline_stage import PipelineStage
from engine.processing.pipeline.data_flow import DataContext
from engine.utils.logger import log


class FallbackOptimizationStage(PipelineStage):
    """
    备用优化阶段
    
    当四层优化策略模块不可用时，提供基本的优化功能，
    确保转换管道能够正常运行。
    """
    
    def __init__(self):
        """初始化备用优化阶段"""
        super().__init__("fallback_optimization", "备用优化策略")
        
    def execute(self, context: DataContext) -> bool:
        """
        执行备用优化逻辑
        
        Args:
            context: 数据上下文
            
        Returns:
            bool: 执行是否成功
        """
        try:
            start_time = time.time()
            log("备用优化阶段开始执行")
            
            # 设置基本的优化标志
            context.set_data("optimization_enabled", False)
            context.set_data("optimization_fallback", True)
            
            # 记录基本信息
            config_data = context.get_data("config_data", {})
            total_items = self._count_configuration_items(config_data)
            
            log(f"检测到配置项数量: {total_items}")
            log("使用传统处理模式，未应用四层优化策略")
            
            # 设置基本的处理决策（全部完整处理）
            section_decisions = {}
            context.set_data("section_processing_decisions", section_decisions)
            
            execution_time = time.time() - start_time
            log(f"备用优化阶段执行完成，耗时: {execution_time:.2f}秒")
            
            return True
            
        except Exception as e:
            error_msg = f"备用优化阶段执行失败: {str(e)}"
            log(error_msg)
            context.add_error(error_msg)
            return False
    
    def _count_configuration_items(self, config_data: Dict[str, Any]) -> int:
        """
        统计配置项数量
        
        Args:
            config_data: 配置数据
            
        Returns:
            int: 配置项数量
        """
        total = 0
        
        # 统计主要配置项
        config_sections = [
            'firewall_policies',
            'firewall_addresses', 
            'firewall_services',
            'firewall_address_groups',
            'firewall_service_groups',
            'interfaces',
            'zones',
            'static_routes',
            'nat_rules',
            'time_ranges'
        ]
        
        for section in config_sections:
            if section in config_data:
                data = config_data[section]
                if isinstance(data, list):
                    total += len(data)
                elif isinstance(data, dict):
                    total += len(data)
        
        return total
