
def pipeline_error_handler(stage_name="unknown"):
    """Pipeline error handling decorator"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                print(safe_translate("pipeline.stage_executing", stage=stage_name))
                result = func(*args, **kwargs)
                print(safe_translate("pipeline.stage_completed", stage=stage_name))
                return result
            except Exception as e:
                print(safe_translate("pipeline.stage_exception", stage=stage_name, error=str(e)))

                # 尝试恢复或提供默认结果
                try:
                    # 如果是配置解析失败，尝试使用简化解析
                    if "config_parsing" in stage_name.lower():
                        print(safe_translate("pipeline.trying_simplified_parsing"))
                        return {"status": "partial_success", "data": {}, "error": str(e)}

                    # 如果是其他阶段，提供默认结果
                    return {"status": "stage_skipped", "stage": stage_name, "error": str(e)}

                except Exception as recovery_error:
                    print(safe_translate("pipeline.recovery_failed", error=str(recovery_error)))
                    # 最后的回退：抛出原始异常以触发整体回退
                    raise e
        return wrapper
    return decorator

def safe_pipeline_execution(pipeline_stages, context=None):
    """Safe pipeline execution"""
    results = []
    successful_stages = 0

    for i, stage in enumerate(pipeline_stages):
        stage_name = getattr(stage, '__class__', {}).get('__name__', f'stage_{i}')

        try:
            print(safe_translate("pipeline.executing_stage",
                               current=i+1, total=len(pipeline_stages), stage=stage_name))

            # 使用错误处理装饰器
            @pipeline_error_handler(stage_name)
            def execute_stage():
                if hasattr(stage, 'execute'):
                    return stage.execute(context) if context else stage.execute()
                else:
                    return stage()

            result = execute_stage()
            results.append(result)
            successful_stages += 1

        except Exception as e:
            print(safe_translate("pipeline.stage_execution_failed", stage=stage_name, error=str(e)))
            # 记录失败但继续执行
            results.append({"status": "failed", "stage": stage_name, "error": str(e)})

    success_rate = (successful_stages / len(pipeline_stages)) * 100 if pipeline_stages else 0
    print(safe_translate("pipeline.execution_completed",
                       successful=successful_stages, total=len(pipeline_stages), rate=success_rate))

    # 如果成功率太低，触发回退
    if success_rate < 50:
        raise Exception(safe_translate("pipeline.low_success_rate", rate=success_rate))

    return results

# PIPELINE_ERROR_HANDLING_OPTIMIZED
# -*- coding: utf-8 -*-
"""
Fortigate Policy Conversion Stage - Pipeline stage for handling Fortigate policy conversion
Ensures reliability of policy conversion and correct integration with XML templates/YANG models
"""

from typing import Dict, Any, List, Optional
from engine.utils.logger import log
from engine.utils.i18n import _

# 定义安全翻译函数
def safe_translate(message, **kwargs):
    """Safe translation function, ensures normal operation even if original _ function is unavailable"""
    try:
        return _(message, **kwargs)
    except Exception:
        # 如果翻译失败，使用简单的字符串替换
        result = message
        for k, v in kwargs.items():
            result = result.replace(f"{{{k}}}", str(v))
        return result

from engine.processing.pipeline.pipeline_stage import PipelineStage
from engine.processing.pipeline.data_flow import DataContext
from engine.business.strategies.fortigate_strategy import FortigateConversionStrategy
from engine.processors.policy_iprange_integrator import PolicyIprangeIntegrator
from engine.mappers.fortigate_service_mapper import FortigateServiceMapper
from lxml import etree


class FortigatePolicyConversionStage(PipelineStage):
    """
    Fortigate Policy Conversion Stage
    Specialized handling of Fortigate firewall policy conversion, ensuring conversion result reliability
    """

    def __init__(self, config_manager, template_manager, yang_manager, name_mapping_manager=None):
        """
        Initialize Fortigate policy conversion stage

        Args:
            config_manager: Configuration manager
            template_manager: Template manager
            yang_manager: YANG manager
            name_mapping_manager: 名称映射管理器（可选）
        """
        super().__init__(
            name="fortigate_policy_conversion",
            description=safe_translate("fortigate_policy_stage.description")
        )

        self.config_manager = config_manager

        # 初始化名称映射管理器
        self.name_mapping_manager = name_mapping_manager
        if self.name_mapping_manager is None:
            from engine.utils.name_mapping_manager import NameMappingManager
            self.name_mapping_manager = NameMappingManager()
        self.template_manager = template_manager
        self.yang_manager = yang_manager

        # 创建Fortigate转换策略
        self.strategy = FortigateConversionStrategy(
            config_manager, template_manager, yang_manager
        )

        # 创建策略iprange集成器
        self.iprange_integrator = PolicyIprangeIntegrator()

        # 创建服务映射器
        self.service_mapper = FortigateServiceMapper()

        log(safe_translate("fortigate_policy_stage.initialized"), "info")
    
    def validate_input(self, context: DataContext) -> bool:
        """
        Validate input data

        Args:
            context: Data context

        Returns:
            bool: Whether validation passes
        """
        # 检查是否有配置数据 - 适配新的11阶段管道数据结构
        config_data = context.get_data("config_data")

        # 如果没有config_data，尝试旧的parsed_config（向后兼容）
        if not config_data:
            config_data = context.get_data("parsed_config")

        if not config_data:
            context.add_error(safe_translate("fortigate_policy_stage.no_parsed_config"))
            return False

        # 检查是否是Fortigate配置
        vendor = context.get_data("vendor", "").lower()
        if vendor != "fortigate":
            context.add_error(safe_translate("fortigate_policy_stage.not_fortigate_config", vendor=vendor))
            return False

        # 检查接口映射验证结果
        interface_mapping_valid = context.get_data("interface_mapping_valid", True)
        if not interface_mapping_valid:
            interface_mapping_error = context.get_data("interface_mapping_error", "Unknown error")
            context.add_error(safe_translate("fortigate_policy_stage.interface_mapping_validation_failed",
                              error=interface_mapping_error))
            return False

        # 使用策略进行详细验证
        return self.strategy.validate_input(context)
    
    def process(self, context: DataContext) -> bool:
        """
        Process Fortigate policy conversion

        Args:
            context: Data context

        Returns:
            bool: Whether processing succeeded
        """
        try:
            log(safe_translate("fortigate_policy_stage.start_processing"), "info")

            # 阶段1：准备转换数据
            if not self.strategy.prepare_conversion_data(context):
                context.add_error(safe_translate("fortigate_policy_stage.prepare_data_failed"))
                return False

            # 阶段2：执行策略转换
            if not self._execute_policy_conversion(context):
                context.add_error(safe_translate("fortigate_policy_stage.policy_conversion_failed"))
                return False

            # 阶段3：验证转换结果
            if not self._validate_conversion_results(context):
                context.add_error(safe_translate("fortigate_policy_stage.validation_failed"))
                return False

            # 阶段4：准备XML集成数据
            if not self._prepare_xml_integration_data(context):
                context.add_error(safe_translate("fortigate_policy_stage.xml_preparation_failed"))
                return False

            log(safe_translate("fortigate_policy_stage.processing_completed"), "info")
            return True
            
        except Exception as e:
            error_msg = safe_translate("fortigate_policy_stage.processing_exception", error=str(e))
            context.add_error(error_msg)
            log(error_msg, "error")
            return False
    
    def _execute_policy_conversion(self, context: DataContext) -> bool:
        """
        Execute policy conversion

        Args:
            context: Data context

        Returns:
            bool: Whether conversion succeeded
        """
        try:
            # 获取准备好的数据
            security_policies = context.get_data("security_policies", [])
            nat_rules = context.get_data("nat_rules", [])

            # 从config_data中获取ippools数据（与其他阶段保持一致）
            config_data = context.get_data("config_data", {})
            ippools = config_data.get("ippools", {})

            # 调试：检查ippools数据
            log(safe_translate("fortigate_policy_stage.ippools_data_retrieved", count=len(ippools)), "info")
            if ippools:
                log(safe_translate("fortigate_policy_stage.ippools_keys_list", keys=str(list(ippools.keys()))), "info")

            # 初始化iprange集成器的服务映射关系
            service_to_address_mapping = context.get_data("service_to_address_mapping", {})
            self.iprange_integrator.set_service_mapping(service_to_address_mapping)

            # 委托给现有的处理器执行实际转换
            converted_security_policies = []
            converted_nat_rules = []
            converted_nat_pools = []

            # 转换安全策略
            for policy in security_policies:
                converted_policy = self._convert_security_policy(policy, context)
                if converted_policy:
                    converted_security_policies.append(converted_policy)
                    # 同时保存原始策略用于验证
                    if not hasattr(context, '_original_security_policies'):
                        context._original_security_policies = []
                    context._original_security_policies.append(policy)
            
            # 转换NAT规则（先验证再转换）
            for nat_rule in nat_rules:
                # 在转换之前验证NAT规则结构
                if not self._validate_nat_rule_structure(nat_rule):
                    rule_name = nat_rule.get("name", "unknown")
                    log(safe_translate("fortigate_policy_stage.nat_rule_validation_failed",
                                     rule=rule_name, type=nat_rule.get('type'), name=nat_rule.get('name')), "error")
                    context.add_error(safe_translate("fortigate_policy_stage.invalid_nat_rule_structure",
                                      rule=rule_name))
                    return False

                converted_rule = self._convert_nat_rule(nat_rule, context)
                if converted_rule:
                    converted_nat_rules.append(converted_rule)

            # 处理地址池配置
            if ippools:
                log(safe_translate("fortigate_policy_stage.processing_ippools", count=len(ippools)), "info")
                converted_nat_pools = self._process_nat_pools(ippools)
                log(safe_translate("fortigate_policy_stage.nat_pools_generated", count=len(converted_nat_pools)), "info")
            
            # 存储转换结果（保持向后兼容）
            context.set_data("converted_security_policies", converted_security_policies)
            context.set_data("converted_nat_rules", converted_nat_rules)
            context.set_data("converted_nat_pools", converted_nat_pools)

            # 生成XML片段用于集成
            security_xml_fragment = self._generate_security_policy_xml_fragment(converted_security_policies)

            # 修复：确保获取所有生成的NAT规则
            # 首先尝试从context获取FortigateConversionStrategy生成的NAT规则
            original_nat_rules = context.get_data("nat_rules", [])
            log(f"INFO: FortigatePolicyConversionStage - Retrieved {len(original_nat_rules)} NAT rules from context", "info")

            # 如果没有获取到足够的NAT规则，检查是否有其他数据源
            if len(original_nat_rules) < 50:  # 基于之前分析，应该有132个规则
                log("WARNING: FortigatePolicyConversionStage - NAT rules count seems low, checking alternative sources", "warning")

                # 检查是否有转换后的NAT规则
                if converted_nat_rules:
                    log(f"INFO: FortigatePolicyConversionStage - Found {len(converted_nat_rules)} converted NAT rules, merging with original", "info")
                    # 合并原始规则和转换后的规则（去重）
                    all_nat_rules = original_nat_rules.copy()
                    for rule in converted_nat_rules:
                        if rule not in all_nat_rules:
                            all_nat_rules.append(rule)
                    original_nat_rules = all_nat_rules
                    log(f"INFO: FortigatePolicyConversionStage - After merging: {len(original_nat_rules)} total NAT rules", "info")

            nat_xml_fragment = self._generate_nat_xml_fragment(original_nat_rules, converted_nat_pools)

            # 设置策略处理结果（XML集成阶段期望的格式）
            policy_processing_result = {
                "security_xml_fragment": security_xml_fragment,
                "nat_xml_fragment": nat_xml_fragment,
                "policies": {
                    "security_policies": converted_security_policies,
                    "nat_rules": converted_nat_rules
                }
            }
            context.set_data("policy_processing_result", policy_processing_result)

            log(_("fortigate_policy_stage.conversion_completed"), "info",
                security_policies=len(converted_security_policies),
                nat_rules=len(converted_nat_rules))

            # 记录用户日志
            from engine.utils.user_log_formatter import record_stage_user_log
            policy_result = {
                "security_policies": converted_security_policies,
                "nat_rules": converted_nat_rules,
                "statistics": {
                    "total": len(converted_security_policies) + len(converted_nat_rules),
                    "converted": len(converted_security_policies) + len(converted_nat_rules),
                    "failed": 0,
                    "skipped": 0
                }
            }
            record_stage_user_log("fortigate_policy_conversion", policy_result)

            return True
            
        except Exception as e:
            error_msg = _("fortigate_policy_stage.conversion_exception", error=str(e))
            log(error_msg, "error")
            return False

    def _generate_security_policy_xml_fragment(self, security_policies: List[Dict]) -> str:
        """
        生成安全策略XML片段

        Args:
            security_policies: 安全策略列表

        Returns:
            str: XML片段字符串
        """
        try:
            if not security_policies:
                return ""

            from lxml import etree

            # 创建security-policy根元素
            security_policy_elem = etree.Element("security-policy")
            security_policy_elem.set("xmlns", "urn:ruijie:ntos:params:xml:ns:yang:security-policy")

            # 处理每个安全策略
            for policy in security_policies:
                self._add_security_policy_to_xml(security_policy_elem, policy)

            # 生成XML字符串
            xml_string = etree.tostring(
                security_policy_elem,
                encoding='unicode',
                pretty_print=True
            )

            return xml_string

        except Exception as e:
            log(_("policy_processor.security_policy_xml_failed", error=str(e)), "error")
            return ""

    def _add_security_policy_to_xml(self, security_policy_elem: etree.Element, policy: Dict):
        """
        添加安全策略到XML，使用名称映射管理器清理名称

        Args:
            security_policy_elem: security-policy XML元素
            policy: 策略配置
        """
        from lxml import etree

        # 创建policy元素（不继承父元素的xmlns属性）
        policy_elem = etree.Element("policy")  # 使用Element而不是SubElement
        security_policy_elem.append(policy_elem)  # 手动添加到父元素

        # 确保policy元素没有xmlns属性（符合NTOS YANG模型）
        if "xmlns" in policy_elem.attrib:
            del policy_elem.attrib["xmlns"]

        # 添加策略名称，使用名称映射管理器清理
        original_name = policy.get("name", "")
        if original_name:
            clean_name = self.name_mapping_manager.register_name_mapping(
                'policies',
                original_name,
                context="安全策略"
            )
        else:
            clean_name = "unnamed_policy"

        name_elem = etree.SubElement(policy_elem, "name")
        name_elem.text = clean_name

        # 添加启用状态
        enabled_elem = etree.SubElement(policy_elem, "enabled")
        enabled_elem.text = "true" if policy.get("enabled", True) else "false"

        # 添加描述字段（按照YANG模型顺序，在group-name之前）
        if "description" in policy and policy["description"]:
            # 清理description字段以符合YANG约束
            from engine.utils.name_validator import clean_ntos_description
            cleaned_description = clean_ntos_description(policy["description"])
            desc_elem = etree.SubElement(policy_elem, "description")
            desc_elem.text = cleaned_description

        # 添加组名
        group_name_elem = etree.SubElement(policy_elem, "group-name")
        group_name_elem.text = policy.get("group-name", "def-group")

        # 添加配置源
        config_source_elem = etree.SubElement(policy_elem, "config-source")
        config_source_elem.text = policy.get("config-source", "manual")

        # 添加会话超时
        session_timeout_elem = etree.SubElement(policy_elem, "session-timeout")
        session_timeout_elem.text = str(policy.get("session-timeout", 0))

        # 添加动作
        action_elem = etree.SubElement(policy_elem, "action")
        action_elem.text = policy.get("action", "permit")

        # 添加源区域 - 按照YANG模型要求生成list结构
        if "source-zone" in policy:
            source_zones = policy["source-zone"]
            if isinstance(source_zones, list):
                # 新格式：[{"name": "trust"}, {"name": "untrust"}]
                for zone in source_zones:
                    source_zone_elem = etree.SubElement(policy_elem, "source-zone")
                    source_zone_name_elem = etree.SubElement(source_zone_elem, "name")
                    if isinstance(zone, dict):
                        source_zone_name_elem.text = zone.get("name", "")
                    else:
                        source_zone_name_elem.text = str(zone)
            elif isinstance(source_zones, dict):
                # 旧格式：{"name": "trust"}
                source_zone_elem = etree.SubElement(policy_elem, "source-zone")
                source_zone_name_elem = etree.SubElement(source_zone_elem, "name")
                source_zone_name_elem.text = source_zones.get("name", "")
            else:
                # 简单字符串格式："trust"
                source_zone_elem = etree.SubElement(policy_elem, "source-zone")
                source_zone_name_elem = etree.SubElement(source_zone_elem, "name")
                source_zone_name_elem.text = str(source_zones)

        # 添加目标区域 - 按照YANG模型要求生成list结构
        if "dest-zone" in policy:
            dest_zones = policy["dest-zone"]
            if isinstance(dest_zones, list):
                # 新格式：[{"name": "trust"}, {"name": "untrust"}]
                for zone in dest_zones:
                    dest_zone_elem = etree.SubElement(policy_elem, "dest-zone")
                    dest_zone_name_elem = etree.SubElement(dest_zone_elem, "name")
                    if isinstance(zone, dict):
                        dest_zone_name_elem.text = zone.get("name", "")
                    else:
                        dest_zone_name_elem.text = str(zone)
            elif isinstance(dest_zones, dict):
                # 旧格式：{"name": "trust"}
                dest_zone_elem = etree.SubElement(policy_elem, "dest-zone")
                dest_zone_name_elem = etree.SubElement(dest_zone_elem, "name")
                dest_zone_name_elem.text = dest_zones.get("name", "")
            else:
                # 简单字符串格式："trust"
                dest_zone_elem = etree.SubElement(policy_elem, "dest-zone")
                dest_zone_name_elem = etree.SubElement(dest_zone_elem, "name")
                dest_zone_name_elem.text = str(dest_zones)

        # 添加源接口 - 按照YANG模型要求的list结构生成
        if "source-interface" in policy:
            source_interfaces = policy["source-interface"]
            if isinstance(source_interfaces, list):
                for interface in source_interfaces:
                    source_interface_elem = etree.SubElement(policy_elem, "source-interface")
                    source_interface_name = etree.SubElement(source_interface_elem, "name")
                    source_interface_name.text = interface.get("name", "") if isinstance(interface, dict) else str(interface)
            else:
                source_interface_elem = etree.SubElement(policy_elem, "source-interface")
                source_interface_name = etree.SubElement(source_interface_elem, "name")
                source_interface_name.text = source_interfaces.get("name", "") if isinstance(source_interfaces, dict) else str(source_interfaces)

        # 添加目标接口 - 按照YANG模型要求的list结构生成
        if "dest-interface" in policy:
            dest_interfaces = policy["dest-interface"]
            if isinstance(dest_interfaces, list):
                for interface in dest_interfaces:
                    dest_interface_elem = etree.SubElement(policy_elem, "dest-interface")
                    dest_interface_name = etree.SubElement(dest_interface_elem, "name")
                    dest_interface_name.text = interface.get("name", "") if isinstance(interface, dict) else str(interface)
            else:
                dest_interface_elem = etree.SubElement(policy_elem, "dest-interface")
                dest_interface_name = etree.SubElement(dest_interface_elem, "name")
                dest_interface_name.text = dest_interfaces.get("name", "") if isinstance(dest_interfaces, dict) else str(dest_interfaces)

        # 添加源地址网络 - 按照YANG模型要求的list结构生成
        if "source-network" in policy:
            source_networks = policy["source-network"]
            if isinstance(source_networks, list):
                for network in source_networks:
                    source_network_elem = etree.SubElement(policy_elem, "source-network")
                    source_network_name = etree.SubElement(source_network_elem, "name")
                    source_network_name.text = network.get("name", "") if isinstance(network, dict) else str(network)
            else:
                source_network_elem = etree.SubElement(policy_elem, "source-network")
                source_network_name = etree.SubElement(source_network_elem, "name")
                source_network_name.text = source_networks.get("name", "") if isinstance(source_networks, dict) else str(source_networks)

        # 添加目标地址网络 - 按照YANG模型要求的list结构生成
        if "dest-network" in policy:
            dest_networks = policy["dest-network"]
            if isinstance(dest_networks, list):
                for network in dest_networks:
                    dest_network_elem = etree.SubElement(policy_elem, "dest-network")
                    dest_network_name = etree.SubElement(dest_network_elem, "name")
                    dest_network_name.text = network.get("name", "") if isinstance(network, dict) else str(network)
            else:
                dest_network_elem = etree.SubElement(policy_elem, "dest-network")
                dest_network_name = etree.SubElement(dest_network_elem, "name")
                dest_network_name.text = dest_networks.get("name", "") if isinstance(dest_networks, dict) else str(dest_networks)

        # 添加服务 - 按照YANG模型要求的list结构生成
        if "service" in policy:
            services = policy["service"]
            if isinstance(services, list):
                for service in services:
                    service_elem = etree.SubElement(policy_elem, "service")
                    service_name = etree.SubElement(service_elem, "name")
                    service_name.text = str(service)
            else:
                service_elem = etree.SubElement(policy_elem, "service")
                service_name = etree.SubElement(service_elem, "name")
                service_name.text = str(services)

        # 添加时间范围
        if "time-range" in policy:
            time_range_elem = etree.SubElement(policy_elem, "time-range")
            time_range_elem.text = policy["time-range"]

        # 添加安全配置文件 - IPS和AV
        if "ips" in policy:
            ips_elem = etree.SubElement(policy_elem, "ips")
            ips_elem.text = policy["ips"]

        if "av" in policy:
            av_elem = etree.SubElement(policy_elem, "av")
            av_elem.text = policy["av"]

    def _generate_nat_xml_fragment(self, nat_rules: List[Dict], nat_pools: List[Dict] = None) -> str:
        """
        生成NAT XML片段

        Args:
            nat_rules: NAT规则列表
            nat_pools: NAT池列表

        Returns:
            str: XML片段字符串
        """
        try:
            if not nat_rules and not nat_pools:
                return ""

            from lxml import etree

            # 创建nat根元素
            nat_elem = etree.Element("nat")
            nat_elem.set("xmlns", "urn:ruijie:ntos:params:xml:ns:yang:nat")

            # 处理NAT池（如果存在）
            if nat_pools:
                for pool in nat_pools:
                    self._add_nat_pool_to_xml(nat_elem, pool)

            # 处理每个NAT规则
            for rule in nat_rules:
                self._add_nat_rule_to_xml(nat_elem, rule)

            # 生成XML字符串
            xml_string = etree.tostring(
                nat_elem,
                encoding='unicode',
                pretty_print=True
            )

            return xml_string

        except Exception as e:
            log(_("policy_processor.nat_xml_failed", error=str(e)), "error")
            return ""

    def _add_nat_pool_to_xml(self, nat_elem: etree.Element, pool: Dict):
        """
        添加NAT池到XML

        Args:
            nat_elem: nat XML元素
            pool: NAT池配置
        """
        from lxml import etree
        from engine.utils.name_validator import clean_ntos_name, clean_ntos_description

        # 验证pool配置完整性
        if not pool or "name" not in pool:
            log(safe_translate("error.nat_pool_missing_name"), "error")
            return

        # 创建pool元素
        pool_elem = etree.SubElement(nat_elem, "pool")

        # 添加池名称（必需字段，使用清理后的名称）
        clean_name = clean_ntos_name(pool["name"], 64)
        name_elem = etree.SubElement(pool_elem, "name")
        name_elem.text = clean_name

        # 添加地址范围（必需字段）
        if "address" in pool and "value" in pool["address"]:
            address_elem = etree.SubElement(pool_elem, "address")
            value_elem = etree.SubElement(address_elem, "value")
            value_elem.text = pool["address"]["value"]
        else:
            log(safe_translate("error.nat_pool_missing_address", pool_name=clean_name), "error")

        # 添加描述（如果存在，使用清理后的描述）
        if "desc" in pool and pool["desc"]:
            clean_desc = clean_ntos_description(pool["desc"], 255)
            if clean_desc:  # 只有清理后的描述不为空才添加
                desc_elem = etree.SubElement(pool_elem, "desc")
                desc_elem.text = clean_desc

    def _add_nat_rule_to_xml(self, nat_elem: etree.Element, rule: Dict):
        """
        添加NAT规则到XML - 按照YANG模型要求生成正确的XML结构

        Args:
            nat_elem: nat XML元素
            rule: NAT规则配置（XML字典格式）
        """
        from lxml import etree

        # 委托给现有的NATGenerator来生成正确的XML结构
        from engine.generators.nat_generator import NATGenerator

        try:
            generator = NATGenerator(name_mapping_manager=self.name_mapping_manager)

            # 添加调试信息
            log(safe_translate("fortigate_policy_stage.processing_nat_rule", rule=str(rule)), "debug")

            # 如果rule是XML字典格式，需要转换为NAT规则配置格式
            if self._is_xml_dict_format(rule):
                log(safe_translate("fortigate_policy_stage.xml_dict_format_detected"), "debug")
                # 将XML字典转换为NAT规则配置格式
                nat_rule_config = self._convert_xml_dict_to_nat_rule_config(rule)
            else:
                log(safe_translate("fortigate_policy_stage.using_original_nat_rule_format"), "debug")
                # 已经是NAT规则配置格式
                nat_rule_config = rule

            log(safe_translate("fortigate_policy_stage.converted_nat_rule_config", config=str(nat_rule_config)), "debug")

            # 使用NATGenerator创建正确的规则元素
            rule_element = generator._create_nat_rule_element(nat_rule_config)
            if rule_element is not None:
                nat_elem.append(rule_element)
                log(safe_translate("fortigate_policy_stage.nat_rule_xml_added_success",
                                 name=nat_rule_config.get('name', 'unknown')), "debug")
            else:
                log(safe_translate("fortigate_policy_stage.nat_rule_xml_generation_failed",
                                 name=rule.get('name', 'unknown')), "warning")

        except Exception as e:
            log(safe_translate("fortigate_policy_stage.nat_rule_xml_add_exception", error=str(e)), "error")

    def _is_xml_dict_format(self, rule: Dict) -> bool:
        """
        判断规则是否为XML字典格式

        Args:
            rule: 规则字典

        Returns:
            bool: 是否为XML字典格式
        """
        # XML字典格式通常包含tag、text、attrib等键
        xml_dict_keys = ['tag', 'text', 'attrib', 'children']
        return any(key in rule for key in xml_dict_keys)

    def _convert_xml_dict_to_nat_rule_config(self, xml_dict: Dict) -> Dict:
        """
        将XML字典格式转换为NAT规则配置格式

        Args:
            xml_dict: XML字典格式的NAT规则

        Returns:
            Dict: NAT规则配置格式
        """
        # 如果已经是NAT规则配置格式（包含static-dnat44、static-snat44或dynamic-snat44键），直接返回
        nat_rule_types = ['static-dnat44', 'static-snat44', 'dynamic-snat44']
        if any(rule_type in xml_dict for rule_type in nat_rule_types):
            return xml_dict

        # 如果是简化格式，需要转换为标准格式
        if 'type' in xml_dict:
            rule_type = xml_dict.get('type', 'static-snat44')
            nat_rule_config = {
                'name': xml_dict.get('name', 'unknown'),
                'rule_en': xml_dict.get('rule_en', True),
                rule_type: {
                    'match': xml_dict.get('match', {}),
                    'translate-to': xml_dict.get('translate-to', {})
                }
            }
            return nat_rule_config

        # 默认返回原始数据
        return xml_dict
    
    def _convert_security_policy(self, policy: Dict, context: DataContext) -> Optional[Dict]:
        """
        转换单个安全策略，并集成iprange相关的地址对象

        Args:
            policy: 安全策略配置（已经是转换后的格式）
            context: 数据上下文

        Returns:
            Optional[Dict]: 转换后的策略配置（不是XML字典格式）
        """
        try:
            # 策略已经在FortigateConversionStrategy.prepare_conversion_data中转换过了
            if not policy:
                log(_("fortigate_policy_stage.security_policy_conversion_empty"), "warning",
                    policy=policy.get("name", "unknown"))
                return None

            # 处理服务映射
            policy_with_services = self._process_policy_services(policy, context)

            # 集成iprange相关的地址对象
            policy_with_iprange = self._integrate_iprange_addresses(policy_with_services, context)

            if policy_with_iprange:
                log(_("fortigate_policy_stage.security_policy_converted"), "debug",
                    policy=policy_with_iprange.get("name", "unknown"))
                return policy_with_iprange
            else:
                log(_("fortigate_policy_stage.security_policy_conversion_empty"), "warning",
                    policy=policy.get("name", "unknown"))
                return None

        except Exception as e:
            policy_name = policy.get("name", "unknown")
            log(_("fortigate_policy_stage.security_policy_conversion_failed"), "error",
                policy=policy_name, error=str(e))
            context.add_warning(_("fortigate_policy_stage.security_policy_conversion_failed",
                                policy=policy_name, error=str(e)))
            return None

    def _process_policy_services(self, policy: Dict, context: DataContext) -> Dict:
        """
        处理策略中的服务映射

        Args:
            policy: 策略配置
            context: 数据上下文

        Returns:
            Dict: 处理后的策略配置
        """
        try:
            # 获取策略中的服务列表
            services = policy.get("service", [])
            if not services:
                return policy

            # 处理服务映射
            mapped_services = []
            custom_services_needed = []

            for service_name in services:
                mapping_result = self.service_mapper.map_service(service_name)

                if mapping_result["needs_custom_creation"]:
                    # 需要创建自定义服务
                    custom_services_needed.append({
                        "name": service_name,
                        "original_service": mapping_result["original_service"]
                    })
                    mapped_services.append(mapping_result["ntos_service"])
                else:
                    # 直接映射
                    mapped_services.append(mapping_result["ntos_service"])

            # 🔧 YANG模型合规性修复：去重服务列表，确保每个服务在策略中只出现一次
            unique_services = []
            seen_services = set()
            duplicate_count = 0

            for service in mapped_services:
                if service not in seen_services:
                    unique_services.append(service)
                    seen_services.add(service)
                else:
                    duplicate_count += 1
                    log(_("fortigate_policy_stage.duplicate_service_removed"), "warning",
                        policy=policy.get("name", "unknown"), service=service)

            if duplicate_count > 0:
                log(_("fortigate_policy_stage.services_deduplicated"), "info",
                    policy=policy.get("name", "unknown"),
                    original=len(mapped_services),
                    unique=len(unique_services),
                    duplicates=duplicate_count)

            # 更新策略中的服务列表（使用去重后的列表）
            policy["service"] = unique_services

            # 记录需要创建的自定义服务
            if custom_services_needed:
                existing_custom_services = context.get_data("custom_services_needed", [])
                existing_custom_services.extend(custom_services_needed)
                context.set_data("custom_services_needed", existing_custom_services)

                log(_("fortigate_policy_stage.custom_services_needed"), "info",
                    policy=policy.get("name", "unknown"),
                    services=", ".join([s["name"] for s in custom_services_needed]))

            return policy

        except Exception as e:
            log(_("fortigate_policy_stage.service_mapping_failed"), "error",
                policy=policy.get("name", "unknown"), error=str(e))
            return policy  # 返回原始策略，继续处理

    def _convert_nat_rule(self, nat_rule: Dict, context: DataContext) -> Optional[Dict]:
        """
        转换单个NAT规则
        
        Args:
            nat_rule: NAT规则配置
            context: 数据上下文
            
        Returns:
            Optional[Dict]: 转换后的NAT规则
        """
        try:
            # 委托给现有的nat_generator
            from engine.generators.nat_generator import NATGenerator

            generator = NATGenerator(name_mapping_manager=self.name_mapping_manager)
            converted_rule = generator.convert_nat_rule_to_xml_dict(nat_rule)
            
            return converted_rule
            
        except Exception as e:
            rule_name = nat_rule.get("name", "unknown")
            log(_("fortigate_policy_stage.nat_rule_conversion_failed"), "error",
                rule=rule_name, error=str(e))
            context.add_warning(_("fortigate_policy_stage.nat_rule_conversion_failed",
                                rule=rule_name, error=str(e)))
            return None
    
    def _validate_conversion_results(self, context: DataContext) -> bool:
        """
        验证转换结果
        
        Args:
            context: 数据上下文
            
        Returns:
            bool: 验证是否通过
        """
        try:
            converted_security_policies = context.get_data("converted_security_policies", [])
            converted_nat_rules = context.get_data("converted_nat_rules", [])

            # 验证安全策略结构 - 使用原始策略进行验证
            original_security_policies = getattr(context, '_original_security_policies', [])
            for policy in original_security_policies:
                if not self._validate_security_policy_structure(policy):
                    context.add_error(_("fortigate_policy_stage.invalid_security_policy_structure",
                                      policy=policy.get("name", "unknown")))
                    return False
            
            # NAT规则验证已在转换前完成，这里不需要重复验证
            
            # 记录验证统计
            context.set_data("validation_stats", {
                "security_policies_validated": len(converted_security_policies),
                "nat_rules_validated": len(converted_nat_rules),
                "validation_passed": True
            })
            
            return True
            
        except Exception as e:
            error_msg = _("fortigate_policy_stage.validation_exception", error=str(e))
            log(error_msg, "error")
            return False
    
    def _validate_security_policy_structure(self, policy: Dict) -> bool:
        """
        验证安全策略结构
        
        Args:
            policy: 安全策略配置
            
        Returns:
            bool: 结构是否有效
        """
        required_fields = ["name", "action", "config-source"]
        return all(field in policy for field in required_fields)
    
    def _validate_nat_rule_structure(self, rule: Dict) -> bool:
        """
        验证NAT规则结构
        
        Args:
            rule: NAT规则配置
            
        Returns:
            bool: 结构是否有效
        """
        # NAT规则应该有类型和基本配置
        return "type" in rule and "name" in rule
    
    def _prepare_xml_integration_data(self, context: DataContext) -> bool:
        """
        准备XML集成数据
        
        Args:
            context: 数据上下文
            
        Returns:
            bool: 准备是否成功
        """
        try:
            # 准备用于XML模板集成的数据结构
            xml_integration_data = {
                "security_policies": context.get_data("converted_security_policies", []),
                "nat_rules": context.get_data("converted_nat_rules", []),
                "namespaces": {
                    "security_policy": "urn:ruijie:ntos:params:xml:ns:yang:security-policy",
                    "nat": "urn:ruijie:ntos:params:xml:ns:yang:nat"
                },
                "template_integration_ready": True
            }
            
            context.set_data("xml_integration_data", xml_integration_data)
            
            log(_("fortigate_policy_stage.xml_integration_data_prepared"), "info")
            return True
            
        except Exception as e:
            error_msg = _("fortigate_policy_stage.xml_integration_preparation_failed", error=str(e))
            log(error_msg, "error")
            return False

    def _integrate_iprange_addresses(self, policy: Dict, context: DataContext) -> Optional[Dict]:
        """
        集成iprange相关的地址对象到策略中

        Args:
            policy: 策略配置字典
            context: 数据上下文

        Returns:
            Optional[Dict]: 集成后的策略配置
        """
        try:
            # 使用iprange集成器处理策略
            integration_result = self.iprange_integrator.process_policy(policy)

            # 记录集成结果
            if integration_result.get("modified", False):
                policy_name = integration_result.get("policy_name", "unknown")
                added_addresses = integration_result.get("added_addresses", [])
                services_with_iprange = integration_result.get("services_with_iprange", [])

                log(safe_translate("fortigate_policy_stage.policy_iprange_integrated", policy=policy_name), "info")
                log(safe_translate("fortigate_policy_stage.iprange_services_used", services=str(services_with_iprange)), "debug")
                log(safe_translate("fortigate_policy_stage.address_objects_added", addresses=str(added_addresses)), "debug")

            return policy

        except Exception as e:
            policy_name = policy.get("name", "unknown")
            log(safe_translate("fortigate_policy_stage.policy_iprange_integration_failed",
                             policy=policy_name, error=str(e)), "error")
            context.add_warning(safe_translate("fortigate_policy_stage.policy_iprange_integration_failed",
                                             policy=policy_name, error=str(e)))
            # 即使集成失败，也返回原始策略，避免阻塞整个转换流程
            return policy

    def _process_nat_pools(self, ippools: Dict) -> List[Dict[str, Any]]:
        """
        处理IP池配置，生成NAT池定义

        Args:
            ippools: IP池配置字典

        Returns:
            list: NAT池配置列表
        """
        from engine.utils.name_validator import clean_ntos_name, clean_ntos_description

        nat_pools = []

        for pool_name, pool_config in ippools.items():
            try:
                # 验证池配置完整性
                if not self._validate_ippool_config(pool_config, pool_name):
                    continue

                # 清理池名称
                clean_name = clean_ntos_name(pool_name, 64)

                # 创建NAT池配置
                nat_pool = {
                    "name": clean_name,
                    "address": {
                        "value": f"{pool_config['startip']}-{pool_config['endip']}"
                    }
                }

                # 添加描述信息（如果存在，使用清理后的描述）
                if "comment" in pool_config and pool_config["comment"]:
                    clean_desc = clean_ntos_description(pool_config["comment"], 255)
                    if clean_desc:  # 只有清理后的描述不为空才添加
                        nat_pool["desc"] = clean_desc

                nat_pools.append(nat_pool)
                log(safe_translate("debug.nat_pool_created",
                                 pool_name=clean_name,
                                 start_ip=pool_config['startip'],
                                 end_ip=pool_config['endip']), "debug")

                # 如果名称被清理，记录日志
                if pool_name != clean_name:
                    log(safe_translate("info.nat_pool_name_cleaned",
                                     original=pool_name,
                                     cleaned=clean_name), "info")

            except Exception as e:
                log(safe_translate("error.ippool_processing_failed",
                                 pool_name=pool_name,
                                 error=str(e)), "error")
                continue

        return nat_pools

    def _validate_ippool_config(self, pool_config: Dict, pool_name: str) -> bool:
        """
        验证IP池配置完整性

        Args:
            pool_config: IP池配置
            pool_name: 池名称

        Returns:
            bool: 配置是否有效
        """
        required_fields = ["startip", "endip"]
        for field in required_fields:
            if field not in pool_config or not pool_config[field]:
                log(safe_translate("fortigate_policy_stage.ippool_missing_field",
                                 pool=pool_name, field=field), "warning")
                return False

        # 验证IP地址格式
        try:
            import ipaddress
            start_ip = ipaddress.IPv4Address(pool_config["startip"])
            end_ip = ipaddress.IPv4Address(pool_config["endip"])

            if start_ip > end_ip:
                log(safe_translate("fortigate_policy_stage.ippool_invalid_range",
                                 pool=pool_name, start=str(start_ip), end=str(end_ip)), "warning")
                return False

        except ipaddress.AddressValueError as e:
            log(safe_translate("fortigate_policy_stage.ippool_invalid_address",
                             pool=pool_name, error=str(e)), "warning")
            return False

        return True
