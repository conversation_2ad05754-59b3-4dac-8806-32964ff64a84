#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
接口处理阶段
负责将FortiGate接口配置转换为NTOS格式
"""

import json
import os
from typing import Dict, Any, List, Optional
from engine.processing.pipeline.pipeline_stage import PipelineStage
from engine.utils.logger import log
from engine.utils.i18n import _
from engine.utils.pipeline_stage_user_logger import get_pipeline_user_logger


class InterfaceProcessingStage(PipelineStage):
    """接口处理阶段"""

    def __init__(self):
        super().__init__("interface_processing", _("interface_processing_stage.description"))

        # 接口类型映射
        self.interface_types = {
            "physical": "physical",
            "vlan": "vlan",
            "loopback": "loopback",
            "tunnel": "tunnel",
            "aggregate": "aggregate"
        }

        # 用户日志记录器（将在process方法中初始化）
        self.user_logger = None
        
        # 默认安全区域映射
        self.default_zone_mapping = {
            "lan": "trust",
            "wan": "untrust",
            "wan1": "untrust", 
            "wan2": "untrust",
            "dmz": "DMZ",
            "internal": "trust",
            "external": "untrust"
        }
    
    def process(self, context) -> bool:
        """
        执行接口处理

        Args:
            context: 管道上下文，包含解析后的配置数据

        Returns: Dict[str, Any]: 更新后的上下文
        """
        log(_("interface_processing_stage.starting"))

        # 初始化用户日志记录器
        language = context.data.get('language', 'zh-CN')
        self.user_logger = get_pipeline_user_logger(language)

        try:
            # 获取接口数据
            config_data = context.data.get('config_data', {})
            interfaces_data = config_data.get('interfaces', [])
            interface_mapping = context.data.get('interface_mapping', {})

            # 获取操作模式结果
            operation_mode_result = context.data.get('operation_mode_result', {})
            operation_mode = context.data.get('operation_mode', 'route')

            # 转换接口数据格式：从列表转换为字典
            if isinstance(interfaces_data, list):
                interfaces = {}
                for intf in interfaces_data:
                    if isinstance(intf, dict) and 'raw_name' in intf:
                        interfaces[intf['raw_name']] = intf
                    elif isinstance(intf, dict) and 'name' in intf:
                        interfaces[intf['name']] = intf
            elif isinstance(interfaces_data, dict):
                interfaces = interfaces_data
            else:
                interfaces = {}

            if not interfaces:
                log(_("interface_processing_stage.no_interfaces"))
                self.user_logger.log_stage_start("interface_processing", 0)
                self.user_logger.log_stage_complete("interface_processing", {"total": 0, "processed": 0})
                context.data['interface_processing_result'] = self._empty_result()
                return True

            # 记录阶段开始
            self.user_logger.log_stage_start("interface_processing", len(interfaces))

            # 处理接口配置
            processing_result = self._process_interfaces(interfaces, interface_mapping, operation_mode_result)

            # 生成最终接口映射（供策略转换使用）
            final_mapping = self._generate_complete_interface_mapping(processing_result['converted'])
            processing_result['final_mapping'] = final_mapping

            # 生成XML片段
            log(f"DEBUG: 开始生成XML片段，converted接口数量: {len(processing_result.get('converted', {}))}", "INFO")
            log(f"DEBUG: physical_interfaces数量: {len(processing_result.get('physical_interfaces', {}))}", "INFO")
            log(f"DEBUG: vlan_interfaces数量: {len(processing_result.get('vlan_interfaces', {}))}", "INFO")

            xml_fragment = self._generate_interface_xml_fragment(processing_result, interface_mapping, operation_mode_result)
            processing_result['xml_fragment'] = xml_fragment

            log(f"DEBUG: XML片段生成完成，长度: {len(xml_fragment)}", "INFO")

            context.data['interface_processing_result'] = processing_result

            # 保存最终接口映射文件，供策略和NAT模块使用
            self._save_final_interface_mapping(processing_result['converted'])

            # 记录阶段完成
            result_summary = {
                "total": len(interfaces),
                "converted": processing_result['statistics']['converted_interfaces'],
                "failed": processing_result['statistics'].get('failed_interfaces', 0),
                "skipped": processing_result['statistics'].get('skipped_interfaces', 0)
            }
            self.user_logger.log_stage_complete("interface_processing", result_summary)

            log(_("interface_processing_stage.completed",
                  converted=processing_result['statistics']['converted_interfaces'],
                  zones=processing_result['statistics']['zones_created']))

            # 记录用户日志
            from engine.utils.user_log_formatter import record_stage_user_log
            record_stage_user_log("interface_processing", processing_result)

            return True

        except Exception as e:
            error_msg = _("interface_processing_stage.failed", error=str(e))
            log(error_msg, "error")

            # 记录阶段失败
            if self.user_logger:
                self.user_logger.log_stage_failure("interface_processing", str(e))

            context.data['interface_processing_result'] = self._empty_result()
            context.add_error(_("interface_processing_stage.processing_failed", error=str(e)), self.name)
            return False
    
    def _process_interfaces(self, interfaces: Dict, interface_mapping: Dict, operation_mode_result: Dict = None) -> Dict[str, Any]:
        """
        处理接口配置

        Args:
            interfaces: 接口配置字典
            interface_mapping: 接口映射关系
            operation_mode_result: 操作模式处理结果

        Returns: Dict[str, Any]: 处理结果
        """
        result = {
            "physical_interfaces": {},
            "vlan_interfaces": {},
            "security_zones": {},
            "converted": {},
            "skipped": {},
            "failed": {},
            "statistics": {
                "total_interfaces": len(interfaces),
                "converted_interfaces": 0,
                "skipped_interfaces": 0,
                "failed_interfaces": 0,
                "physical_interfaces": 0,
                "vlan_interfaces": 0,
                "zones_created": 0
            },
            "details": []
        }
        
        for intf_name, intf_config in interfaces.items():
            try:
                # 借鉴旧架构：首先过滤逻辑接口（modem、tunnel等）
                if self._should_filter_interface(intf_name, intf_config):
                    result["statistics"]["skipped_interfaces"] += 1
                    result["skipped"][intf_name] = {
                        "reason": "logical_interface",
                        "message": _("interface_processing_stage.logical_interface_skip", name=intf_name)
                    }
                    continue

                # 检查接口是否有映射关系，只处理有映射关系的接口
                has_mapping = self._has_interface_mapping(intf_name, interface_mapping, intf_config)

                if not has_mapping:
                    result["statistics"]["skipped_interfaces"] += 1
                    result["skipped"][intf_name] = {
                        "reason": "no_mapping",
                        "message": _("interface_processing_stage.no_mapping_message", name=intf_name)
                    }

                    # 记录跳过的接口
                    if self.user_logger:
                        self.user_logger.log_item_warning(_("interface_processing_stage.interface_type"), intf_name,
                                                         _("interface_processing_stage.missing_interface_mapping"),
                                                         _("interface_processing_stage.add_mapping_suggestion"))
                    continue

                # 确定接口类型
                intf_type = self._determine_interface_type(intf_name, intf_config)

                # 获取映射的接口名（使用新的验证逻辑）
                mapped_name = self._get_validated_mapped_name(intf_name, interface_mapping, intf_config)
                
                # 转换接口配置
                converted_intf = self._convert_interface(intf_name, intf_config, intf_type, mapped_name, operation_mode_result, interface_mapping)
                
                if converted_intf:
                    result["converted"][intf_name] = converted_intf
                    result["statistics"]["converted_interfaces"] += 1

                    # 分类存储
                    if intf_type == "physical":
                        result["physical_interfaces"][intf_name] = converted_intf
                        result["statistics"]["physical_interfaces"] += 1
                    elif intf_type == "vlan":
                        result["vlan_interfaces"][intf_name] = converted_intf
                        result["statistics"]["vlan_interfaces"] += 1

                    # 处理安全区域
                    zone_info = self._process_security_zone(intf_name, intf_config, converted_intf)
                    if zone_info:
                        zone_name = zone_info["name"]
                        if zone_name not in result["security_zones"]:
                            result["security_zones"][zone_name] = zone_info
                            result["statistics"]["zones_created"] += 1

                    result["details"].append({
                        "name": intf_name,
                        "status": "success",
                        "type": intf_type,
                        "mapped_name": mapped_name,
                        "zone": zone_info["name"] if zone_info else None,
                        "converted": converted_intf
                    })

                    # 记录成功转换的接口
                    if self.user_logger:
                        details = {"converted_name": mapped_name, "type": intf_type}
                        self.user_logger.log_item_success(_("interface_processing_stage.interface_type"), intf_name, details)
                else:
                    reason = _("interface_processing_stage.conversion_failed")
                    self._add_failed_result(result, intf_name, intf_config, reason)

                    # 记录失败转换的接口
                    if self.user_logger:
                        self.user_logger.log_item_failure(_("interface_processing_stage.interface_type"), intf_name, reason)
                
            except Exception as e:
                reason = _("interface_processing_stage.processing_error", error=str(e))
                self._add_failed_result(result, intf_name, intf_config, reason)
        
        return result

    def _should_filter_interface(self, intf_name: str, intf_config: Dict) -> bool:
        """
        检查接口是否应该被过滤（借鉴旧架构逻辑）

        Args:
            intf_name: 接口名称
            intf_config: 接口配置

        Returns:
            bool: 是否应该过滤
        """
        # 首先检查特殊的逻辑接口（优先级最高）
        # 借鉴旧架构：过滤modem接口（逻辑接口，不需要转换）
        if intf_name.lower() == 'modem':
            log(_("interface_processor.filter_modem_interface", name=intf_name), "info")
            return True

        # 然后检查type字段：如果明确标记为physical，则不过滤
        interface_type = intf_config.get('type')
        if interface_type == 'physical':
            return False

        # 过滤tunnel接口
        if intf_name.lower().startswith('tunnel') or intf_config.get('type') == 'tunnel':
            log(_("interface_processor.filter_tunnel_interface", name=intf_name), "info")
            return True

        # 过滤虚拟接口（vw类型）
        if intf_name.lower().startswith('vw') and len(intf_name) <= 4:
            log(_("interface_processor.filter_virtual_interface", name=intf_name), "info")
            return True

        # 精确匹配已知逻辑接口模式（移除过于宽泛的'x'前缀）
        intf_name_lower = intf_name.lower()

        # NAF隧道接口
        if intf_name_lower.startswith('naf.'):
            log(_("interface_processor.filter_logical_interface", name=intf_name), "info")
            return True

        # L2TP隧道接口
        if intf_name_lower.startswith('l2t.'):
            log(_("interface_processor.filter_logical_interface", name=intf_name), "info")
            return True

        # SSL VPN接口
        if intf_name_lower.startswith('ssl.'):
            log(_("interface_processor.filter_logical_interface", name=intf_name), "info")
            return True

        # FortiLink聚合接口（精确匹配）
        if intf_name_lower == 'fortilink':
            log(_("interface_processor.filter_logical_interface", name=intf_name), "info")
            return True

        return False

    def _has_interface_mapping(self, intf_name: str, interface_mapping: Dict, intf_config: Dict = None) -> bool:
        """
        检查接口是否有映射关系（增强验证逻辑）

        Args:
            intf_name: 接口名称
            interface_mapping: 接口映射字典
            intf_config: 接口配置（可选）

        Returns:
            bool: 是否有映射关系且通过验证
        """
        if not interface_mapping:
            log(_("interface_processor.empty_mapping_skip", name=intf_name), "debug")
            return False

        # 获取接口映射配置，支持两种格式：
        # 1. 嵌套格式: {"interface_mappings": {"port1": "Ge0/0"}}
        # 2. 扁平格式: {"port1": "Ge0/0", "port2": "Ge0/1"}
        actual_mappings = {}

        if "interface_mappings" in interface_mapping:
            # 嵌套格式，提取interface_mappings部分
            actual_mappings = interface_mapping["interface_mappings"]
            log(_("interface_processor.nested_mapping_check", count=len(actual_mappings)), "debug")
        else:
            # 扁平格式，直接使用（过滤掉非接口映射的键）
            for key, value in interface_mapping.items():
                if key not in ["default_mapping"] and isinstance(value, str):
                    actual_mappings[key] = value
            log(_("interface_processor.flat_mapping_check", count=len(actual_mappings)), "debug")

        if not actual_mappings:
            log(_("interface_processor.empty_mapping_config", name=intf_name), "debug")
            return False

        # 验证接口映射条目
        return self._validate_interface_mapping_entry(intf_name, actual_mappings, intf_config)

    def _validate_interface_mapping_entry(self, intf_name: str, actual_mappings: Dict, intf_config: Dict = None) -> bool:
        """
        验证接口映射条目的有效性

        Args:
            intf_name: 接口名称
            actual_mappings: 实际的接口映射字典
            intf_config: 接口配置（可选）

        Returns:
            bool: 映射条目是否有效
        """


        # 1. 检查直接映射
        if intf_name in actual_mappings:
            target_interface = actual_mappings[intf_name]
            return self._validate_mapping_pair(intf_name, target_interface, intf_config)

        # 2. 检查子接口映射（如果父接口有映射关系）
        if '.' in intf_name:
            parent_interface = intf_name.split('.')[0]
            if parent_interface in actual_mappings:
                target_parent = actual_mappings[parent_interface]
                # 子接口的目标名称 = 父接口目标名称 + VLAN ID
                vlan_id = intf_name.split('.')[1]
                target_interface = f"{target_parent}.{vlan_id}"
                return self._validate_mapping_pair(intf_name, target_interface, intf_config)

        # 3. 检查VLAN接口映射（通过interface或parent_interface字段指向父接口）
        if intf_config and 'vlanid' in intf_config:
            # 支持两种字段名：interface（标准）和parent_interface（解析阶段使用）
            parent_interface = intf_config.get('interface') or intf_config.get('parent_interface')
            vlan_id = intf_config.get('vlanid') or intf_config.get('vlan_id')

            if parent_interface and vlan_id and parent_interface in actual_mappings:
                target_parent = actual_mappings[parent_interface]
                target_interface = f"{target_parent}.{vlan_id}"
                return self._validate_mapping_pair(intf_name, target_interface, intf_config)

        log(_("interface_processor.no_mapping_found", name=intf_name), "debug")
        return False

    def _validate_mapping_pair(self, source_interface: str, target_interface: str, intf_config: Dict = None) -> bool:
        """
        验证源接口和目标接口的映射对

        Args:
            source_interface: 源接口名称（FortiGate）
            target_interface: 目标接口名称（NTOS）
            intf_config: 接口配置（可选）

        Returns:
            bool: 映射对是否有效
        """
        # 验证源接口是否在FortiGate配置中存在
        if not self._validate_source_interface_exists(source_interface, intf_config):
            log(_("interface_processor.source_interface_validation_failed", interface=source_interface), "warning")
            return False

        # 验证目标接口格式是否符合NTOS规范
        if not self._validate_target_interface_format(target_interface):
            log(_("interface_processor.target_interface_validation_failed", interface=target_interface), "warning")
            return False

        log(_("interface_processor.mapping_validation_passed", source=source_interface, target=target_interface), "debug")
        return True

    def _validate_source_interface_exists(self, source_interface: str, intf_config: Dict = None) -> bool:
        """
        验证源接口是否在FortiGate配置中存在

        Args:
            source_interface: 源接口名称（FortiGate）
            intf_config: 接口配置（如果已知）

        Returns:
            bool: 源接口是否存在
        """
        # 如果已经有接口配置，说明接口存在
        if intf_config:
            log(_("interface_processor.source_interface_exists", interface=source_interface), "debug")
            return True

        # 从数据上下文中获取所有接口配置
        try:
            # 这里需要访问原始配置数据来验证接口是否存在
            # 由于我们在处理阶段，可以假设传入的接口都是存在的
            # 更严格的验证可以在后续版本中实现
            log(_("interface_processor.source_interface_validation_default", interface=source_interface), "debug")
            return True
        except Exception as e:
            log(_("interface_processor.source_interface_validation_error", interface=source_interface, error=str(e)), "warning")
            return False

    def _validate_target_interface_format(self, target_interface: str) -> bool:
        """
        验证目标接口格式是否符合NTOS规范

        Args:
            target_interface: 目标接口名称（NTOS）

        Returns:
            bool: 目标接口格式是否有效
        """
        if not target_interface:
            log(_("interface_processor.target_interface_empty"), "warning")
            return False

        # NTOS接口命名规范验证
        import re

        # 物理接口格式：Ge0/0, Ge0/1, TenGe0/0 等
        physical_pattern = r'^(Ge|TenGe|FortyGe|HundredGe)\d+/\d+$'

        # 子接口格式：Ge0/0.100, TenGe0/0.200 等
        subinterface_pattern = r'^(Ge|TenGe|FortyGe|HundredGe)\d+/\d+\.\d+$'

        # 环回接口格式：Loopback0, Loopback1 等
        loopback_pattern = r'^Loopback\d+$'

        # 聚合接口格式：Agg0, Agg1 等
        aggregate_pattern = r'^Agg\d+$'

        if (re.match(physical_pattern, target_interface) or
            re.match(subinterface_pattern, target_interface) or
            re.match(loopback_pattern, target_interface) or
            re.match(aggregate_pattern, target_interface)):
            log(_("interface_processor.target_interface_format_valid", interface=target_interface), "debug")
            return True

        log(_("interface_processor.target_interface_format_invalid", interface=target_interface), "warning")
        return False

    def _get_validated_mapped_name(self, intf_name: str, interface_mapping: Dict, intf_config: Dict = None) -> str:
        """
        获取经过验证的映射接口名称

        Args:
            intf_name: 接口名称
            interface_mapping: 接口映射字典
            intf_config: 接口配置（可选）

        Returns:
            str: 映射后的接口名称，如果没有有效映射则返回原始名称
        """
        if not interface_mapping:
            log(_("interface_processor.empty_mapping_use_original", name=intf_name), "debug")
            return intf_name

        # 获取接口映射配置，支持两种格式：
        # 1. 嵌套格式: {"interface_mappings": {"port1": "Ge0/0"}}
        # 2. 扁平格式: {"port1": "Ge0/0", "port2": "Ge0/1"}
        actual_mappings = {}

        if "interface_mappings" in interface_mapping:
            # 嵌套格式，提取interface_mappings部分
            actual_mappings = interface_mapping["interface_mappings"]
            log(_("interface_processor.nested_mapping_use", count=len(actual_mappings)), "debug")
        else:
            # 扁平格式，直接使用（过滤掉非接口映射的键）
            for key, value in interface_mapping.items():
                if key not in ["default_mapping"] and isinstance(value, str):
                    actual_mappings[key] = value
            log(_("interface_processor.flat_mapping_use", count=len(actual_mappings)), "debug")

        if not actual_mappings:
            log(_("interface_processor.empty_mapping_config_use_original", name=intf_name), "debug")
            return intf_name

        # 1. 检查直接映射
        if intf_name in actual_mappings:
            target_interface = actual_mappings[intf_name]
            if self._validate_mapping_pair(intf_name, target_interface, intf_config):
                log(_("interface_processor.direct_mapping_used", source=intf_name, target=target_interface), "debug")
                return target_interface

        # 2. 检查子接口映射（如果父接口有映射关系）
        if '.' in intf_name:
            parent_interface = intf_name.split('.')[0]
            if parent_interface in actual_mappings:
                target_parent = actual_mappings[parent_interface]
                vlan_id = intf_name.split('.')[1]
                target_interface = f"{target_parent}.{vlan_id}"
                if self._validate_mapping_pair(intf_name, target_interface, intf_config):
                    log(_("interface_processor.subinterface_mapping_used", source=intf_name, target=target_interface), "debug")
                    return target_interface

        # 3. 检查VLAN接口映射（通过interface或parent_interface字段指向父接口）
        if intf_config and 'vlanid' in intf_config:
            # 支持两种字段名：interface（标准）和parent_interface（解析阶段使用）
            parent_interface = intf_config.get('interface') or intf_config.get('parent_interface')
            vlan_id = intf_config.get('vlanid') or intf_config.get('vlan_id')

            if parent_interface and vlan_id and parent_interface in actual_mappings:
                target_parent = actual_mappings[parent_interface]
                target_interface = f"{target_parent}.{vlan_id}"
                if self._validate_mapping_pair(intf_name, target_interface, intf_config):
                    log(_("interface_processor.vlan_mapping_used", source=intf_name, target=target_interface), "debug")
                    return target_interface

        # 如果没有找到有效映射，返回原始名称
        log(_("interface_processor.no_valid_mapping_use_original", name=intf_name), "debug")
        return intf_name

    def _determine_interface_type(self, intf_name: str, intf_config: Dict) -> str:
        """确定接口类型，支持类型转换"""
        # 检查是否已经有类型转换信息（来自解析阶段）
        if intf_config.get("type_conversion"):
            conversion_info = intf_config["type_conversion"]
            log(_("interface_processor.using_converted_type",
                  interface=intf_name,
                  original=conversion_info["original"],
                  converted=conversion_info["converted"]), "info")
            return conversion_info["converted"]

        # 检查配置中的类型字段
        if intf_config.get("type"):
            config_type = intf_config["type"]
            # 如果是支持的类型，直接返回
            if config_type in ["physical", "vlan"]:
                return config_type
            # 如果是可转换的类型，进行转换
            elif config_type in ["tunnel", "loopback", "switch", "aggregate", "redundant"]:
                log(_("interface_processor.type_conversion_applied",
                      interface=intf_name,
                      original=config_type,
                      converted="physical"), "info")
                return "physical"

        # 基于名称的类型推断（保持原有逻辑）
        # 首先检查是否为隧道接口（优先级高，避免被误识别为VLAN）
        if (intf_name.lower().startswith('tun') or
            intf_name.lower().startswith('naf') or
            intf_name.lower().startswith('l2t') or
            intf_name.lower().startswith('ssl') or
            intf_name.lower().startswith('tunnel')):
            log(_("interface_processor.tunnel_type_converted", interface=intf_name), "info")
            return "physical"  # 转换为物理接口处理

        # 检查是否为环回接口
        if intf_name.lower().startswith('lo'):
            log(_("interface_processor.loopback_type_converted", interface=intf_name), "info")
            return "physical"  # 转换为物理接口处理

        # 检查是否为交换接口
        if intf_name.lower().startswith('bos') or intf_name.lower() == 'switch':
            log(_("interface_processor.switch_type_converted", interface=intf_name), "info")
            return "physical"  # 转换为物理接口处理

        # 检查是否为VLAN接口（在tunnel检查之后）
        if 'vlanid' in intf_config or ('.' in intf_name and not intf_name.lower().startswith(('naf', 'l2t', 'ssl'))):
            return "vlan"

        # 检查是否为聚合接口
        if intf_name.lower().startswith('agg') or 'aggregate' in intf_config:
            log(_("interface_processor.aggregate_type_converted", interface=intf_name), "info")
            return "physical"  # 转换为物理接口处理

        # 默认为物理接口
        return "physical"
    
    def _convert_interface(self, intf_name: str, intf_config: Dict, intf_type: str, mapped_name: str, operation_mode_result: Dict = None, interface_mapping: Dict = None) -> Dict[str, Any]:
        """转换接口配置"""
        base_config = {
            "name": mapped_name,
            "original_name": intf_name,
            "type": intf_type,
            # 优先使用解析器提供的enabled字段，否则从status字段转换
            "enabled": intf_config.get("enabled", intf_config.get("status", "up") == "up"),
            "description": intf_config.get("description", ""),
            "mtu": intf_config.get("mtu", 1500)
        }

        # 设置工作模式
        working_mode = self._determine_working_mode(intf_name, mapped_name, operation_mode_result)
        base_config["working_mode"] = working_mode

        # 处理接口角色（WAN/LAN）
        role = self._determine_interface_role(intf_config)
        if role:
            base_config["role"] = role

        # 处理IP配置
        ip_config = self._process_ip_configuration(intf_config, operation_mode_result, intf_name)
        if ip_config:
            base_config.update(ip_config)

        # 处理IPv6配置
        ipv6_config = self._process_ipv6_configuration(intf_config)
        if ipv6_config:
            base_config["ipv6"] = ipv6_config

        # 处理访问控制（针对管理接口）
        access_control = self._process_access_control(intf_name, intf_config, operation_mode_result)
        if access_control:
            base_config["access_control"] = access_control

        # 处理带宽配置
        bandwidth_config = self._process_bandwidth_configuration(intf_config)
        if bandwidth_config:
            base_config.update(bandwidth_config)

        # 根据接口类型添加特定配置
        if intf_type == "physical":
            return self._convert_physical_interface(base_config, intf_config)
        elif intf_type == "vlan":
            return self._convert_vlan_interface(base_config, intf_config, interface_mapping)
        elif intf_type == "loopback":
            return self._convert_loopback_interface(base_config, intf_config)
        else:
            return base_config
    
    def _convert_physical_interface(self, base_config: Dict, intf_config: Dict) -> Dict[str, Any]:
        """转换物理接口"""
        physical_config = base_config.copy()
        
        # 添加物理接口特定配置
        physical_config.update({
            "speed": intf_config.get("speed", "auto"),
            "duplex": intf_config.get("duplex", "auto"),
            "auto_negotiation": intf_config.get("auto-negotiation", "enable") == "enable"
        })
        
        return physical_config
    
    def _convert_vlan_interface(self, base_config: Dict, intf_config: Dict, interface_mapping: Dict = None) -> Dict[str, Any]:
        """转换VLAN接口 - 集成旧架构的完整子接口处理逻辑"""
        vlan_config = base_config.copy()

        # 提取VLAN ID - 支持多种字段名
        vlan_id = intf_config.get("vlanid") or intf_config.get("vlan_id") or intf_config.get("vlan-id")
        if not vlan_id and '.' in base_config["original_name"]:
            # 从接口名提取VLAN ID（如 internal.100）
            try:
                vlan_id = int(base_config["original_name"].split('.')[-1])
                log(_("interface_processor.debug_extract_vlan_id", name=base_config["original_name"], vlan_id=vlan_id), "debug")
            except ValueError:
                log(_("interface_processor.warning_extract_vlan_id_failed", name=base_config["original_name"]), "warning")
                vlan_id = None

        # 提取父接口 - 支持多种字段名
        parent_interface = (intf_config.get("interface") or
                          intf_config.get("parent_interface") or
                          intf_config.get("link-interface"))
        if not parent_interface and '.' in base_config["original_name"]:
            parent_interface = base_config["original_name"].split('.')[0]
            log(_("interface_processor.debug_extract_parent_interface", name=base_config["original_name"], parent=parent_interface), "debug")

        # 验证VLAN ID范围（NTOS支持1-4063）
        if vlan_id and (vlan_id < 1 or vlan_id > 4063):
            log(_("interface_processor.warning_vlan_id_out_of_range", vlan_id=vlan_id, name=base_config["original_name"]), "warning")

        # 生成VLAN接口的NTOS名称
        vlan_interface_name = None
        mapped_parent = None
        if parent_interface and vlan_id:
            # 优先使用传入的接口映射参数
            if interface_mapping and parent_interface in interface_mapping:
                mapped_parent = interface_mapping[parent_interface]
                vlan_interface_name = f"{mapped_parent}.{vlan_id}"
                log(_("interface_processor.debug_vlan_interface_mapping",
                      original=base_config['original_name'],
                      mapped=vlan_interface_name,
                      parent=parent_interface,
                      mapped_parent=mapped_parent), "debug")
            else:
                # 回退到原有逻辑
                parent_mapped_name = self._get_parent_interface_mapped_name(parent_interface)
                if parent_mapped_name and parent_mapped_name != parent_interface:
                    # 只有当父接口有真正的映射时才生成子接口映射
                    mapped_parent = parent_mapped_name
                    vlan_interface_name = f"{parent_mapped_name}.{vlan_id}"
                    log(_("interface_processor.debug_internal_vlan_mapping",
                          original=base_config['original_name'],
                          mapped=vlan_interface_name), "debug")
                else:
                    log(_("interface_processor.debug_parent_no_mapping",
                          parent=parent_interface,
                          original=base_config['original_name']), "debug")
                    mapped_parent = parent_interface
                    # 不设置name字段，让_generate_complete_interface_mapping方法处理

        # 更新VLAN配置
        vlan_config.update({
            "vlan_id": vlan_id,
            "parent_interface": parent_interface,
            "mapped_parent": mapped_parent or parent_interface,
            "is_subinterface": True,
            "protocol": intf_config.get("protocol", "802.1q"),  # 默认802.1q协议
            "link_interface": mapped_parent or parent_interface  # YANG模型要求的字段名，使用映射后的父接口
        })

        # 添加映射名称字段
        if vlan_interface_name:
            vlan_config["mapped_name"] = vlan_interface_name

        # 设置VLAN接口的映射名称
        if vlan_interface_name:
            vlan_config["name"] = vlan_interface_name

        # 如果有父接口，需要验证父接口是否存在
        if parent_interface:
            log(_("interface_processor.debug_vlan_interface_info",
                  original=base_config['original_name'],
                  parent=parent_interface,
                  vlan_id=vlan_id,
                  mapped=vlan_interface_name), "debug")

        return vlan_config

    def _get_parent_interface_mapped_name(self, parent_interface: str) -> str:
        """
        获取父接口的映射名称

        Args:
            parent_interface: 父接口的原始名称

        Returns:
            str: 父接口的映射名称，如果未找到则返回None
        """
        # 从接口映射中查找父接口的映射名称
        # 注意：这个方法现在主要用于向后兼容，实际映射应该通过参数传递
        log(_("interface_processor.debug_parent_mapping_method_deprecated"), "debug")
        return None  # 返回None，让调用方使用传递的interface_mapping参数

    def _convert_loopback_interface(self, base_config: Dict, intf_config: Dict) -> Dict[str, Any]:
        """转换环回接口"""
        # intf_config参数保留用于未来扩展
        return base_config

    def _determine_working_mode(self, intf_name: str, mapped_name: str, operation_mode_result: Dict = None) -> str:
        """
        确定接口的工作模式

        Args:
            intf_name: 原始接口名称
            mapped_name: 映射后接口名称
            operation_mode_result: 操作模式处理结果

        Returns:
            str: 工作模式 ('route' 或 'bridge')
        """
        if not operation_mode_result or operation_mode_result.get("mode") != "transparent":
            # 非透明模式，所有接口都是路由模式
            return "route"

        # 透明模式下，检查接口分类
        interface_classification = operation_mode_result.get("interface_classification", {})

        # 检查是否为管理接口
        mgmt_interfaces = interface_classification.get("mgmt_interfaces", [])
        mgmt_interfaces_mapped = interface_classification.get("mgmt_interfaces_mapped", [])

        if intf_name in mgmt_interfaces or mapped_name in mgmt_interfaces_mapped:
            log(_("interface_processor.debug_mgmt_interface_route_mode", name=intf_name, mapped=mapped_name), "debug")
            return "route"

        # 检查是否为桥接接口
        bridge_interfaces = interface_classification.get("bridge_interfaces", [])
        bridge_interfaces_mapped = interface_classification.get("bridge_interfaces_mapped", [])

        if intf_name in bridge_interfaces or mapped_name in bridge_interfaces_mapped:
            log(_("interface_processor.debug_bridge_interface_mode", name=intf_name, mapped=mapped_name), "debug")
            return "bridge"

        # 默认为路由模式
        log(_("interface_processor.debug_default_route_mode", name=intf_name, mapped=mapped_name), "warning")
        return "route"
    
    def _netmask_to_cidr(self, netmask: str) -> int:
        """
        将子网掩码转换为CIDR前缀长度

        Args:
            netmask: 子网掩码，如 "*************"

        Returns:
            int: CIDR前缀长度，如 24
        """
        try:
            # 将子网掩码转换为二进制，计算连续的1的个数
            parts = netmask.split('.')
            if len(parts) != 4:
                return 24  # 默认值

            binary_str = ''
            for part in parts:
                binary_str += format(int(part), '08b')

            # 计算连续的1的个数
            prefix_length = 0
            for bit in binary_str:
                if bit == '1':
                    prefix_length += 1
                else:
                    break

            return prefix_length
        except Exception:
            return 24  # 默认值

    def _process_ip_configuration(self, intf_config: Dict, operation_mode_result: Dict = None, intf_name: str = None) -> Dict[str, Any]:
        """处理IP配置"""
        ip_config = {}

        # 检查IP地址配置
        if "ip" in intf_config:
            ip_addr = intf_config["ip"]
            if ip_addr and ip_addr != "0.0.0.0":
                ip_config["ip_address"] = ip_addr

        # 检查子网掩码
        if "netmask" in intf_config:
            netmask = intf_config["netmask"]
            if netmask:
                ip_config["netmask"] = netmask

        # 透明模式下的管理IP配置
        if (operation_mode_result and
            operation_mode_result.get("mode") == "transparent" and
            operation_mode_result.get("mgmt_ip") and
            intf_name == operation_mode_result.get("mgmt_interface")):

            # 解析管理IP地址
            mgmt_ip = operation_mode_result.get("mgmt_ip")
            if "/" in mgmt_ip:
                # 检查是否为IP/子网掩码格式（如：*************/*************）
                ip_part, mask_part = mgmt_ip.split("/", 1)
                if "." in mask_part and len(mask_part.split(".")) == 4:
                    # IP/子网掩码格式：*************/*************
                    cidr_prefix = self._netmask_to_cidr(mask_part)
                    ip_config["ip_address"] = f"{ip_part}/{cidr_prefix}"
                    log(_("interface_processor.debug_mgmt_ip_mask_format",
                          name=intf_name,
                          original=f"{ip_part}/{mask_part}",
                          converted=f"{ip_part}/{cidr_prefix}"), "info")
                else:
                    # 标准CIDR格式：*************/24
                    ip_config["ip_address"] = mgmt_ip
                    log(_("interface_processor.debug_mgmt_ip_cidr_format", name=intf_name, ip=mgmt_ip), "info")
            elif " " in mgmt_ip:
                # IP + 子网掩码格式：************* *************
                parts = mgmt_ip.split()
                if len(parts) >= 2:
                    cidr_prefix = self._netmask_to_cidr(parts[1])
                    ip_config["ip_address"] = f"{parts[0]}/{cidr_prefix}"
                    log(_("interface_processor.debug_mgmt_ip_space_format",
                          name=intf_name,
                          original=f"{parts[0]} {parts[1]}",
                          converted=f"{parts[0]}/{cidr_prefix}"), "info")

        # 检查DHCP配置
        if intf_config.get("mode") == "dhcp":
            ip_config["dhcp_enabled"] = True

        # 检查PPPoE配置
        if intf_config.get("mode") == "pppoe":
            ip_config["pppoe_enabled"] = True
            # 使用解析器提供的PPPoE字段
            ip_config["pppoe_username"] = intf_config.get("pppoe_username", intf_config.get("username", ""))
            ip_config["pppoe_password"] = intf_config.get("pppoe_password", "123456")  # 使用解析器的固定密码

        return ip_config

    def _process_access_control(self, intf_name: str, intf_config: Dict, operation_mode_result: Dict = None) -> Dict[str, Any]:
        """
        处理访问控制配置 - 增强版本支持更多服务和子接口

        Args:
            intf_name: 接口名称
            intf_config: 接口配置
            operation_mode_result: 操作模式处理结果

        Returns: Dict[str, Any]: 访问控制配置
        """
        access_control = {}

        # 透明模式下的管理接口访问控制
        if (operation_mode_result and
            operation_mode_result.get("mode") == "transparent" and
            intf_name == operation_mode_result.get("mgmt_interface")):

            # 管理接口默认开放ping、https、ssh
            access_control = {
                "ping": True,
                "https": True,
                "ssh": True
            }
            log(_("interface_processor.debug_mgmt_access_control", name=intf_name), "info")

        # 增强：检查原始配置中的访问控制设置
        if "allowaccess" in intf_config:
            allowaccess = intf_config["allowaccess"]
            access_control.update(self._parse_allowaccess_enhanced(allowaccess, intf_name))

        # 增强：处理子接口访问控制继承
        if intf_config.get("is_subinterface", False):
            parent_interface = intf_config.get("parent_interface", "")
            if parent_interface:
                access_control.update(self._inherit_parent_access_control(parent_interface, access_control))

        return access_control if access_control else None

    def _parse_allowaccess_enhanced(self, allowaccess: any, intf_name: str) -> Dict[str, bool]:
        """
        增强的访问控制解析 - 支持更多服务类型

        Args:
            allowaccess: 访问控制配置（字符串或列表）
            intf_name: 接口名称

        Returns: Dict[str, bool]: 解析后的访问控制配置
        """
        access_control = {}

        # 支持的服务映射（FortiGate -> NTOS）
        # 根据ntos-local-defend.yang，只支持https、ping、ssh三个服务
        service_mapping = {
            "ping": "ping",
            "https": "https",
            "ssh": "ssh",
            "http": "https",  # HTTP映射到HTTPS
            # 其他FortiGate服务不被NTOS支持，会在日志中记录警告
        }

        if isinstance(allowaccess, str):
            # 解析访问控制字符串
            services = allowaccess.split()
            for service in services:
                service = service.strip().lower()
                if service in service_mapping:
                    ntos_service = service_mapping[service]
                    access_control[ntos_service] = True
                    log(_("interface_processor.mapped_access_service",
                          intf=intf_name, fg_service=service, ntos_service=ntos_service), "debug")
                else:
                    log(_("interface_processor.unsupported_access_service",
                          intf=intf_name, service=service), "warning")

        elif isinstance(allowaccess, list):
            # 处理列表格式的访问控制（FortiGate解析器返回的格式）
            for service in allowaccess:
                service = str(service).strip().lower()
                if service in service_mapping:
                    ntos_service = service_mapping[service]
                    access_control[ntos_service] = True
                    log(_("interface_processor.mapped_access_service",
                          intf=intf_name, fg_service=service, ntos_service=ntos_service), "debug")
                else:
                    log(_("interface_processor.unsupported_access_service",
                          intf=intf_name, service=service), "warning")

        return access_control

    def _inherit_parent_access_control(self, parent_interface: str, current_access: Dict[str, bool]) -> Dict[str, bool]:
        """
        子接口访问控制继承 - 新增方法

        Args:
            parent_interface: 父接口名称
            current_access: 当前访问控制配置

        Returns: Dict[str, bool]: 继承后的访问控制配置
        """
        inherited_access = current_access.copy()

        # 子接口默认继承父接口的基础访问控制
        # 但可以通过显式配置覆盖
        if not inherited_access:
            # 如果子接口没有显式配置访问控制，使用默认的安全配置
            inherited_access = {
                "ping": False,
                "https": False,
                "ssh": False
            }
            log(_("interface_processor.subinterface_default_access",
                  sub_intf=parent_interface, parent=parent_interface), "debug")
        else:
            log(_("interface_processor.subinterface_explicit_access",
                  sub_intf=parent_interface), "debug")

        return inherited_access

    def _determine_interface_role(self, intf_config: Dict) -> str:
        """
        确定接口角色（WAN/LAN）

        Args:
            intf_config: 接口配置

        Returns:
            str: 接口角色
        """
        role = intf_config.get("role", "")
        if role:
            # 标准化角色名称
            role_lower = role.lower()
            if role_lower in ["wan", "wan1", "wan2", "wan3", "wan4"]:
                return "wan"
            elif role_lower in ["lan", "dmz", "internal"]:
                return "lan"

        # 根据接口名称推断角色
        intf_name = intf_config.get("name", "").lower()
        if "wan" in intf_name:
            return "wan"
        elif "lan" in intf_name or "dmz" in intf_name:
            return "lan"

        return "lan"  # 默认为LAN

    def _process_ipv6_configuration(self, intf_config: Dict) -> Dict[str, Any]:
        """
        处理IPv6配置

        Args:
            intf_config: 接口配置

        Returns: Dict[str, Any]: IPv6配置
        """
        ipv6_config = {}

        # 检查IPv6配置
        ipv6_data = intf_config.get("ipv6", {})
        if ipv6_data:
            # IPv6地址配置
            if "ip6-address" in ipv6_data:
                ipv6_config["address"] = ipv6_data["ip6-address"]
                ipv6_config["enabled"] = True

            # IPv6模式配置
            if "ip6-mode" in ipv6_data:
                mode = ipv6_data["ip6-mode"]
                if mode == "dhcp":
                    ipv6_config["dhcp_enabled"] = True
                    ipv6_config["enabled"] = True

        # 默认禁用IPv6
        if not ipv6_config:
            ipv6_config["enabled"] = False

        return ipv6_config

    def _process_bandwidth_configuration(self, intf_config: Dict) -> Dict[str, Any]:
        """
        处理带宽配置

        Args:
            intf_config: 接口配置

        Returns: Dict[str, Any]: 带宽配置
        """
        bandwidth_config = {}

        # 上行带宽
        if "estimated-upstream-bandwidth" in intf_config:
            upstream_bw = int(intf_config["estimated-upstream-bandwidth"]) * 1000  # 转换为kbps
            bandwidth_config["upload_bandwidth"] = {
                "value": upstream_bw,
                "unit": "kbps"
            }

        # 下行带宽
        if "estimated-downstream-bandwidth" in intf_config:
            downstream_bw = int(intf_config["estimated-downstream-bandwidth"]) * 1000  # 转换为kbps
            bandwidth_config["download_bandwidth"] = {
                "value": downstream_bw,
                "unit": "kbps"
            }

        # 通用带宽设置
        if "bandwidth" in intf_config:
            bandwidth_config["bandwidth"] = intf_config["bandwidth"]

        return bandwidth_config
    
    def _process_security_zone(self, intf_name: str, intf_config: Dict, converted_intf: Dict) -> Dict[str, Any]:
        """处理安全区域"""
        # 检查是否有显式的安全区域配置
        zone_name = intf_config.get("zone")
        
        if not zone_name:
            # 根据接口名推断安全区域
            zone_name = self._infer_security_zone(intf_name)
        
        if zone_name:
            return {
                "name": zone_name,
                "type": "security_zone",
                "interfaces": [converted_intf["name"]],
                "description": f"Security zone for {intf_name}"
            }
        
        return None
    
    def _infer_security_zone(self, intf_name: str) -> str:
        """根据接口名推断安全区域"""
        intf_lower = intf_name.lower()
        
        # 精确匹配
        if intf_lower in self.default_zone_mapping:
            return self.default_zone_mapping[intf_lower]
        
        # 模糊匹配
        for pattern, zone in self.default_zone_mapping.items():
            if pattern in intf_lower:
                return zone
        
        # 默认规则
        if "wan" in intf_lower or "external" in intf_lower:
            return "untrust"
        else:
            return "trust"
    
    def _add_failed_result(self, result: Dict, name: str, config: Dict, reason: str):
        """添加失败的结果"""
        result["failed"][name] = config
        result["statistics"]["failed_interfaces"] += 1
        result["details"].append({
            "name": name,
            "status": "failed",
            "reason": reason,
            "original": config
        })
        log(_("interface_processing_stage.interface_failed", name=name, reason=reason), "error")
    
    def _save_final_interface_mapping(self, converted_interfaces: Dict[str, Any]):
        """
        保存最终接口映射文件，供策略和NAT模块使用
        集成旧架构的子接口映射生成逻辑

        Args:
            converted_interfaces: 转换后的接口字典
        """
        try:
            from engine.utils.interface_mapper import save_final_interface_mapping
            from engine.utils.file_utils import get_current_output_file_path, get_task_data_directory

            # 获取当前任务数据目录
            output_file = get_current_output_file_path()
            task_data_dir = get_task_data_directory(output_file)



            # 生成完整的接口映射（包括子接口）
            complete_mapping = self._generate_complete_interface_mapping(converted_interfaces)

            # 显示前几个接口的详细信息
            for i, (name, mapped_name) in enumerate(list(complete_mapping.items())[:10]):
                interface_config = converted_interfaces.get(name, {})
                interface_type = interface_config.get("type", "unknown")
                log(_("interface_processor.debug_interface_mapping_detail",
                      index=i+1,
                      original=name,
                      mapped=mapped_name,
                      type=interface_type), "info")

            # 保存最终接口映射文件
            # 注意：save_final_interface_mapping期望接收完整的接口配置字典，而不是简单的映射字典
            final_mapping_file = save_final_interface_mapping(converted_interfaces, task_data_dir)
            if final_mapping_file:
                log(_("interface_processing_stage.final_mapping_saved"), file=final_mapping_file)
                log(_("interface_processor.debug_final_mapping_saved", file=final_mapping_file), "info")

                # 验证文件是否真的存在
                import os
                if os.path.exists(final_mapping_file):
                    log(_("interface_processor.debug_file_exists_verification", file=final_mapping_file), "info")

                    # 读取文件内容进行验证
                    import json
                    with open(final_mapping_file, 'r') as f:
                        saved_content = json.load(f)
                    log(_("interface_processor.debug_saved_mapping_content", content=saved_content), "info")
                else:
                    log(_("interface_processor.debug_file_not_exists", file=final_mapping_file), "error")
            else:
                log(_("interface_processing_stage.final_mapping_save_failed"), "warning")
                log(_("interface_processor.debug_final_mapping_save_failed"), "error")

        except Exception as e:
            error_msg = _("interface_processing_stage.final_mapping_save_error", error=str(e))
            log(error_msg, "error")

    def _find_original_interface_name(self, ntos_name: str, complete_mapping: Dict[str, str]) -> Optional[str]:
        """
        通过NTOS接口名称反向查找FortiGate原始接口名称

        Args:
            ntos_name: NTOS格式的接口名称
            complete_mapping: 当前的接口映射字典 {FortiGate名称: NTOS名称}

        Returns:
            Optional[str]: 对应的FortiGate原始接口名称，如果未找到返回None
        """
        for original_name, mapped_name in complete_mapping.items():
            if mapped_name == ntos_name:
                return original_name

        return None

    def _generate_complete_interface_mapping(self, converted_interfaces: Dict[str, Any]) -> Dict[str, str]:
        """
        生成完整的接口映射，只包含有映射关系的接口和子接口
        规则：
        1. 只保存有用户提供映射的接口
        2. 子接口例外：如果父接口有映射，则子接口也保存映射
        3. 不保存没有映射关系的接口（避免自映射如port2->port2）

        Args:
            converted_interfaces: 转换后的接口字典

        Returns: Dict[str, str]: 完整的接口映射 {FortiGate接口名: NTOS接口名}
        """
        complete_mapping = {}

        log(_("interface_processor.debug_start_interface_mapping", count=len(converted_interfaces)), "debug")

        # 第一步：处理有用户映射的物理接口
        for original_name, interface_config in converted_interfaces.items():
            if not isinstance(interface_config, dict):
                continue

            mapped_name = interface_config.get("name")
            interface_type = interface_config.get("type", "unknown")

            # 只处理有映射且映射名称与原始名称不同的接口（避免自映射）
            if mapped_name and mapped_name != original_name:
                complete_mapping[original_name] = mapped_name
                log(_("interface_processor.debug_add_valid_mapping",
                      original=original_name,
                      mapped=mapped_name,
                      type=interface_type), "debug")

        # 第二步：处理VLAN子接口（修复后的逻辑）
        for original_name, interface_config in converted_interfaces.items():
            if not isinstance(interface_config, dict):
                continue

            interface_type = interface_config.get("type", "unknown")
            is_subinterface = interface_config.get("is_subinterface", False)

            # 只处理VLAN子接口
            if interface_type == "vlan" or is_subinterface:
                parent_interface = interface_config.get("parent_interface")
                vlan_id = interface_config.get("vlan_id") or interface_config.get("vlanid")

                if parent_interface and vlan_id:
                    # 检查parent_interface是否为NTOS格式名称，需要反向查找
                    parent_original_name = None

                    # 首先检查是否直接在映射中（FortiGate原始名称）
                    if parent_interface in complete_mapping:
                        parent_original_name = parent_interface
                    else:
                        # 反向查找：parent_interface可能是NTOS名称
                        parent_original_name = self._find_original_interface_name(parent_interface, complete_mapping)

                    if parent_original_name and parent_original_name in complete_mapping:
                        parent_mapped = complete_mapping[parent_original_name]
                        generated_name = f"{parent_mapped}.{vlan_id}"
                        complete_mapping[original_name] = generated_name
                        log(_("interface_processor.debug_generate_subinterface_mapping",
                              original=original_name,
                              mapped=generated_name,
                              parent=parent_original_name,
                              parent_mapped=parent_mapped), "debug")
                    else:
                        log(_("interface_processor.debug_skip_subinterface_no_parent",
                              original=original_name,
                              parent=parent_interface), "debug")
                else:
                    log(_("interface_processor.warning_vlan_missing_info", original=original_name), "warning")
                    log(_("interface_processor.debug_subinterface_detail",
                          original=original_name,
                          parent=parent_interface,
                          vlan_id=vlan_id,
                          config=interface_config), "debug")

        log(_("interface_processor.debug_mapping_generation_complete", count=len(complete_mapping)), "debug")
        log(_("interface_processor.debug_final_mapping_content", mapping=complete_mapping), "debug")

        return complete_mapping

    def _generate_interface_xml_fragment(self, processing_result: Dict[str, Any], interface_mapping: Dict = None, operation_mode_result: Dict = None) -> str:
        """
        生成接口XML片段（借鉴旧架构，遵循YANG模型）

        Args:
            processing_result: 接口处理结果
            interface_mapping: 接口映射字典
            operation_mode_result: 操作模式处理结果（包含透明模式信息）

        Returns:
            str: XML片段字符串
        """
        try:
            from lxml import etree
            from engine.generators.interface_common import InterfaceCommon
            from engine.generators.vlan_interface_handler import VlanInterfaceHandler
            from engine.generators.physical_interface_handler import PhysicalInterfaceHandler

            # 获取转换后的接口
            converted_interfaces = processing_result.get("converted", {})
            vlan_interfaces = processing_result.get("vlan_interfaces", {})
            physical_interfaces = processing_result.get("physical_interfaces", {})

            log(f"DEBUG: XML片段生成 - converted_interfaces: {len(converted_interfaces)}", "INFO")
            log(f"DEBUG: XML片段生成 - vlan_interfaces: {len(vlan_interfaces)}", "INFO")
            log(f"DEBUG: XML片段生成 - physical_interfaces: {len(physical_interfaces)}", "INFO")

            if not converted_interfaces:
                log("DEBUG: converted_interfaces为空，返回空XML片段", "INFO")
                return ""

            # 创建interface根元素（符合YANG模型）
            interface_elem = etree.Element("interface")
            interface_elem.set("xmlns", "urn:ruijie:ntos:params:xml:ns:yang:interface")

            # 创建处理器，传递透明模式信息
            common = InterfaceCommon()
            vlan_handler = VlanInterfaceHandler(common)
            physical_handler = PhysicalInterfaceHandler(common, operation_mode_result)

            # 处理VLAN接口
            for intf_name, intf_config in vlan_interfaces.items():
                if intf_config.get("is_subinterface", False):
                    # 生成VLAN接口XML，传递正确的interface_mapping
                    vlan_handler.create_new_vlan_interface(
                        interface_elem, intf_config, interface_mapping
                    )

            # 处理物理接口
            log(_("interface_processor.debug_start_physical_processing", count=len(physical_interfaces)), "info")
            for intf_name, intf_config in physical_interfaces.items():
                log(_("interface_processor.debug_process_physical_interface", name=intf_name, is_sub=intf_config.get('is_subinterface', False)), "info")
                if not intf_config.get("is_subinterface", False):
                    log(_("interface_processor.debug_generate_xml_for_interface", name=intf_name), "info")
                    # 生成物理接口XML，传递正确的interface_mapping
                    physical_handler.create_new_interface(
                        interface_elem, intf_config, interface_mapping
                    )
                    log(_("interface_processor.debug_interface_xml_complete", name=intf_name), "info")
                else:
                    log(_("interface_processor.debug_skip_subinterface", name=intf_name), "info")

            # 生成XML字符串
            xml_string = etree.tostring(
                interface_elem,
                encoding='unicode',
                pretty_print=True
            )

            log(_("interface_processor.xml_fragment_generated", vlan=len(vlan_interfaces), physical=len(physical_interfaces)), "debug")
            return xml_string

        except Exception as e:
            log(_("interface_processor.xml_fragment_generation_failed", error=str(e)), "error")
            import traceback
            traceback.print_exc()
            return ""

    def _empty_result(self) -> Dict[str, Any]:
        """返回空结果"""
        return {
            "physical_interfaces": {},
            "vlan_interfaces": {},
            "security_zones": {},
            "converted": {},
            "skipped": {},
            "failed": {},
            "xml_fragment": "",
            "statistics": {
                "total_interfaces": 0,
                "converted_interfaces": 0,
                "skipped_interfaces": 0,
                "failed_interfaces": 0,
                "physical_interfaces": 0,
                "vlan_interfaces": 0,
                "zones_created": 0
            },
            "details": []
        }
