#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
操作模式检测阶段
负责检测FortiGate的操作模式（路由模式/透明模式）并进行相应的处理
"""

from typing import Dict, Any
from engine.processing.pipeline.pipeline_stage import PipelineStage
from engine.processing.pipeline.data_flow import DataContext
from engine.utils.logger import log
from engine.utils.i18n import _


class OperationModeStage(PipelineStage):
    """操作模式检测和处理阶段"""
    
    def __init__(self):
        super().__init__("operation_mode", _("operation_mode_stage.description"))
        
        # 加载设备接口信息
        self._load_device_interfaces()

    def _load_device_interfaces(self):
        """加载设备接口信息"""
        import json
        import os

        try:
            # 获取设备接口配置文件路径
            current_dir = os.path.dirname(os.path.abspath(__file__))
            device_interfaces_file = os.path.join(current_dir, "..", "..", "data", "device_interfaces.json")
            device_interfaces_file = os.path.normpath(device_interfaces_file)

            # 读取设备接口配置
            with open(device_interfaces_file, 'r', encoding='utf-8') as f:
                self.device_interfaces = json.load(f)

            log(_("operation_mode_processor.device_interfaces_loaded", devices=list(self.device_interfaces.keys())), "debug")

        except Exception as e:
            log(_("operation_mode_processor.device_interfaces_load_failed", error=str(e)), "warning")
            # 使用默认配置
            self.device_interfaces = {
                "z5100s": {
                    "interfaces": ["Ge0/0", "Ge0/1", "Ge0/2", "Ge0/3", "Ge0/4", "Ge0/5", "Ge0/6", "Ge0/7", "Ge0/8", "Ge0/9", "TenGe0/0", "TenGe0/1", "TenGe0/2", "TenGe0/3"]
                },
                "z3200s": {
                    "interfaces": ["Ge0/0", "Ge0/1", "Ge0/2", "Ge0/3", "Ge0/4", "Ge0/5", "Ge0/6", "Ge0/7", "Ge0/8", "TenGe0/0"]
                }
            }

    def validate_input(self, context: DataContext) -> bool:
        """
        验证输入数据
        
        Args:
            context: 数据上下文
            
        Returns:
            bool: 验证是否通过
        """
        config_data = context.get_data("config_data")
        if not config_data:
            context.add_error(_("operation_mode_stage.no_config_data"))
            return False
        
        return True
    
    def process(self, context: DataContext) -> bool:
        """
        执行操作模式检测和处理

        Args:
            context: 数据上下文

        Returns:
            bool: 处理是否成功
        """
        log(_("operation_mode_stage.starting"))

        try:
            # 获取配置数据
            config_data = context.get_data("config_data")
            system_settings = config_data.get("system_settings", {})
            model = context.get_data("model", "z5100s")
            interface_mapping = context.get_data("interface_mapping", {})

            # 检测操作模式
            operation_mode = self._detect_operation_mode(system_settings, context)

            # 处理操作模式
            if operation_mode == "transparent":
                mode_result = self._process_transparent_mode(system_settings, model, interface_mapping)
            else:
                mode_result = self._process_route_mode(system_settings, model)

            # 存储处理结果
            context.set_data("operation_mode", operation_mode)
            context.set_data("operation_mode_result", mode_result)

            log(_("operation_mode_stage.completed"), "info",
                mode=operation_mode,
                hostname=mode_result.get("hostname", "unknown"))

            # 记录用户日志
            from engine.utils.user_log_formatter import record_stage_user_log
            record_stage_user_log("operation_mode", mode_result)

            return True

        except Exception as e:
            error_msg = _("operation_mode_stage.processing_failed", error=str(e))
            context.add_error(error_msg)
            log(error_msg, "error")
            return False
    
    def _detect_operation_mode(self, system_settings: Dict[str, Any], context: DataContext = None) -> str:
        """
        检测操作模式 - 基于实际配置内容判断，不依赖配置文件头部注释

        FortiGate操作模式检测标准：
        1. system global/system settings中的显式opmode设置 (最高优先级)
        2. 接口配置特征分析 (主要判断依据)
        3. 管理接口配置模式 (辅助判断)
        4. 默认路由模式

        Args:
            system_settings: 系统设置配置

        Returns:
            str: 操作模式 ('route' 或 'transparent')
        """
        if not system_settings:
            log(_("operation_mode_stage.no_system_settings"), "warning")
            return "route"  # 默认路由模式

        # 优先级1: 检查显式的opmode设置 (system global 或 system settings)
        explicit_opmode = system_settings.get("opmode", "").lower()

        # 获取接口配置用于特征分析
        interfaces = {}
        if context:
            config_data = context.get_data("config_data", {})
            interfaces_data = config_data.get("interfaces", {})

            # 处理接口数据格式：新架构返回列表，旧架构返回字典
            if isinstance(interfaces_data, list):
                # 新架构：将列表转换为字典格式
                for intf in interfaces_data:
                    if isinstance(intf, dict):
                        # 优先使用raw_name，其次使用name
                        intf_name = intf.get("raw_name") or intf.get("name")
                        if intf_name:
                            interfaces[intf_name] = intf
            elif isinstance(interfaces_data, dict):
                # 旧架构：直接使用字典格式
                interfaces = interfaces_data

        # 分析接口配置特征
        has_role_interfaces = self._analyze_interface_roles(interfaces)
        has_management_interface = self._has_dedicated_management_interface(interfaces)
        has_manageip = bool(system_settings.get("manageip"))

        log(_("operation_mode_stage.mode_detection_analysis"), "info",
            explicit_opmode=explicit_opmode,
            role_interfaces=has_role_interfaces,
            mgmt_interface=has_management_interface,
            has_manageip=has_manageip)

        # 判断逻辑 - 以实际配置为准
        if explicit_opmode == "transparent":
            log(_("operation_mode_stage.transparent_mode_detected_explicit"))
            return "transparent"
        elif explicit_opmode in ["nat", "route"]:
            log(_("operation_mode_stage.route_mode_detected_explicit"), "info", opmode=explicit_opmode)
            return "route"
        elif has_role_interfaces and has_management_interface:
            # 有明确角色划分的接口且有独立管理接口 = 路由模式
            log(_("operation_mode_stage.route_mode_detected_interface_analysis"), "info")
            return "route"
        elif has_manageip and not has_role_interfaces:
            # 有管理IP但没有角色划分的接口 = 可能是透明模式
            log(_("operation_mode_stage.transparent_mode_detected_manageip"), "info")
            return "transparent"
        else:
            # 默认路由模式
            log(_("operation_mode_stage.route_mode_detected_default"), "info")
            return "route"

    def _analyze_interface_roles(self, interfaces: Dict[str, Any]) -> bool:
        """
        分析接口是否有明确的角色划分

        Args:
            interfaces: 接口配置字典

        Returns:
            bool: 是否有明确的角色划分接口
        """
        if not interfaces:
            return False

        role_count = 0
        for interface_config in interfaces.values():
            if isinstance(interface_config, dict) and interface_config.get("role"):
                role = interface_config["role"].lower()
                if role in ["wan", "lan", "dmz"]:
                    role_count += 1

        # 如果有2个或以上的接口有明确角色，认为是路由模式特征
        return role_count >= 2

    def _has_dedicated_management_interface(self, interfaces: Dict[str, Any]) -> bool:
        """
        检查是否有独立的管理接口

        Args:
            interfaces: 接口配置字典

        Returns:
            bool: 是否有独立管理接口
        """
        if not interfaces:
            return False

        for interface_name, interface_config in interfaces.items():
            if isinstance(interface_config, dict):
                # 检查是否为管理接口
                if (interface_name.lower() == "mgmt" or
                    interface_config.get("dedicated-to") == "management" or
                    interface_config.get("role") == "management"):
                    return True

        return False

    def _process_route_mode(self, system_settings: Dict[str, Any], model: str) -> Dict[str, Any]:
        """
        处理路由模式
        
        Args:
            system_settings: 系统设置
            model: 设备型号
            
        Returns: Dict[str, Any]: 处理结果
        """
        log(_("operation_mode_stage.processing_route_mode"))
        
        result = {
            "mode": "route",
            "hostname": system_settings.get("hostname", "FortiGate"),
            "admin_sport": system_settings.get("admin-sport", 443),
            "admin_ssh_port": system_settings.get("admin-ssh-port", 22),
            "timezone": system_settings.get("timezone", "04"),
            "gui_theme": system_settings.get("gui-theme", "blue"),
            "model": model,
            "interface_mode": "route",
            "nat_enabled": True
        }
        
        log(_("operation_mode_stage.route_mode_processed"), "info", 
            hostname=result["hostname"])
        
        return result
    
    def _process_transparent_mode(self, system_settings: Dict[str, Any], model: str, interface_mapping: Dict[str, str]) -> Dict[str, Any]:
        """
        处理透明模式

        Args:
            system_settings: 系统设置
            model: 设备型号
            interface_mapping: 接口映射字典

        Returns: Dict[str, Any]: 处理结果
        """
        log(_("operation_mode_stage.processing_transparent_mode"))

        # 分类接口 - 使用接口映射文件
        interface_classification = self._classify_interfaces_for_transparent_mode_with_mapping(
            interface_mapping, model
        )

        # 获取管理接口的映射后名称
        mgmt_interface_original = interface_classification.get("mgmt_interface_original", "mgmt")
        mgmt_interface_mapped = interface_classification.get("mgmt_interface_mapped", "mgmt")

        result = {
            "mode": "transparent",
            "hostname": system_settings.get("hostname", "FortiGate"),
            "mgmt_interface": mgmt_interface_original,  # 原始名称
            "mgmt_interface_mapped": mgmt_interface_mapped,  # 映射后名称
            "mgmt_ip": system_settings.get("manageip"),
            "model": model,
            "interface_mode": "transparent",
            "nat_enabled": False,
            "interface_classification": interface_classification,
            "bridge_config": {
                "name": "br0",
                "enabled": True,
                "slave_interfaces": interface_classification["bridge_interfaces_mapped"]  # 使用映射后的接口名称
            }
        }

        log(_("operation_mode_stage.transparent_mode_processed"), "info",
            hostname=result["hostname"],
            mgmt=mgmt_interface_mapped,
            bridge_count=len(interface_classification["bridge_interfaces_mapped"]))

        return result
    
    def _classify_interfaces_for_transparent_mode(self, mgmt_interface: str,
                                                physical_interfaces: list) -> Dict[str, Any]:
        """
        为透明模式分类接口（旧方法，保持兼容性）

        Args:
            mgmt_interface: 管理接口名称
            physical_interfaces: 物理接口列表

        Returns: Dict[str, Any]: 接口分类结果
        """
        classification = {
            "mgmt_interfaces": [mgmt_interface],
            "bridge_interfaces": physical_interfaces.copy(),
            "route_interfaces": [mgmt_interface],
            "total_interfaces": len(physical_interfaces) + 1
        }

        log(_("operation_mode_stage.interface_classification"), "debug",
            mgmt_count=len(classification["mgmt_interfaces"]),
            bridge_count=len(classification["bridge_interfaces"]),
            total=classification["total_interfaces"])

        return classification

    def _classify_interfaces_for_transparent_mode_with_mapping(self, interface_mapping: Dict[str, str], model: str) -> Dict[str, Any]:
        """
        使用接口映射为透明模式分类接口

        Args:
            interface_mapping: 接口映射字典 {原始名称: 映射后名称}
            model: 设备型号

        Returns: Dict[str, Any]: 接口分类结果

        Raises:
            ValueError: 当透明模式下缺少必需的"mgmt"接口映射时
        """
        # 查找管理接口的映射
        mgmt_interface_original = None
        mgmt_interface_mapped = None
        bridge_interfaces_original = []
        bridge_interfaces_mapped = []

        # 获取实际的接口映射数据
        actual_mappings = interface_mapping.get("interface_mappings", interface_mapping)

        # 透明模式下强制要求"mgmt"接口映射存在
        if "mgmt" not in actual_mappings:
            error_msg = (
                "透明模式下必须配置'mgmt'接口的映射关系。"
                "请在接口映射文件中添加'mgmt'接口的NTOS映射配置，"
                "例如: {\"mgmt\": \"Ge0/2\"}"
            )
            log(error_msg, "error")
            raise ValueError(error_msg)

        # 获取管理接口映射
        mgmt_interface_original = "mgmt"
        mgmt_interface_mapped = actual_mappings["mgmt"]
        log(_("operation_mode_processor.mgmt_interface_mapping_found", original=mgmt_interface_original, mapped=mgmt_interface_mapped), "debug")

        # 获取设备的所有接口（除管理接口外都设为桥接模式）
        device_info = self.device_interfaces.get(model, self.device_interfaces.get("z3200s", {}))
        all_device_interfaces = device_info.get("interfaces", [])

        # 除管理接口外的所有设备接口都设为桥接模式
        for device_interface in all_device_interfaces:
            if device_interface != mgmt_interface_mapped:  # 排除管理接口
                bridge_interfaces_mapped.append(device_interface)
                log(_("operation_mode_processor.device_interface_bridge_mode", interface=device_interface), "debug")

        log(_("operation_mode_processor.transparent_mode_classification", mgmt=mgmt_interface_mapped, count=len(bridge_interfaces_mapped)), "info")

        # 验证管理接口映射是否有效
        if not mgmt_interface_original or not mgmt_interface_mapped:
            error_msg = (
                "透明模式下'mgmt'接口映射配置无效。"
                "请确保'mgmt'接口映射到有效的NTOS接口名称，"
                "例如: {\"mgmt\": \"Ge0/2\"}"
            )
            log(error_msg, "error")
            raise ValueError(error_msg)

        classification = {
            "mgmt_interface_original": mgmt_interface_original,
            "mgmt_interface_mapped": mgmt_interface_mapped,
            "mgmt_interfaces": [mgmt_interface_original],
            "mgmt_interfaces_mapped": [mgmt_interface_mapped],
            "bridge_interfaces": bridge_interfaces_original,  # 保持为空，因为我们使用设备接口
            "bridge_interfaces_mapped": bridge_interfaces_mapped,  # 设备的所有接口（除管理接口）
            "route_interfaces": [mgmt_interface_original],
            "route_interfaces_mapped": [mgmt_interface_mapped],
            "total_interfaces": len(bridge_interfaces_mapped) + 1  # 桥接接口数量 + 管理接口
        }

        log(_("operation_mode_stage.interface_classification"), "debug",
            mgmt_count=1,
            bridge_count=len(bridge_interfaces_mapped),
            total=classification["total_interfaces"])

        log(_("operation_mode_processor.transparent_mode_classification_complete", mgmt=mgmt_interface_mapped, bridge=bridge_interfaces_mapped), "info")

        return classification
