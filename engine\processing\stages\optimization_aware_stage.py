"""
优化感知的管道阶段基类
为传统处理阶段提供四层优化策略的感知能力
"""

import time
from typing import Dict, List, Optional, Any
from abc import ABC, abstractmethod

from engine.processing.pipeline.pipeline_stage import PipelineStage
from engine.processing.pipeline.data_flow import DataContext
from engine.utils.logger import log
from engine.utils.i18n import _


class OptimizationAwareStage(PipelineStage, ABC):
    """
    优化感知的管道阶段基类
    
    提供四层优化策略的感知能力，允许传统处理阶段根据优化结果
    进行智能跳过、简化处理或完整处理。
    """
    
    def __init__(self, name: str, description: str = None):
        """
        初始化优化感知阶段
        
        Args:
            name: 阶段名称
            description: 阶段描述
        """
        super().__init__(name, description)
        self.optimization_enabled = False
        self.section_decisions = {}
        self.optimization_metrics = None
        
    def execute(self, context: DataContext) -> bool:
        """
        执行优化感知的处理逻辑
        
        Args:
            context: 数据上下文
            
        Returns:
            bool: 执行是否成功
        """
        try:
            # 检查是否启用了优化
            self._check_optimization_status(context)
            
            if self.optimization_enabled:
                log(_("optimization_aware_stage.optimization_enabled", 
                                 stage=self.name), "info")
                return self._execute_with_optimization(context)
            else:
                log(_("optimization_aware_stage.optimization_disabled", 
                                 stage=self.name), "debug")
                return self._execute_traditional(context)
                
        except Exception as e:
            error_msg = f"优化感知阶段 {self.name} 执行失败: {str(e)}"
            log(error_msg)
            context.add_error(error_msg)
            return False
    
    def _check_optimization_status(self, context: DataContext):
        """检查优化状态"""
        self.optimization_enabled = context.get_data("optimization_enabled", False)
        self.section_decisions = context.get_data("section_processing_decisions", {})
        self.optimization_metrics = context.get_data("optimization_metrics")
        
        if self.optimization_enabled:
            log(_("optimization_aware_stage.optimization_status_loaded",
                             stage=self.name,
                             decisions_count=len(self.section_decisions)), "debug")
    
    def _execute_with_optimization(self, context: DataContext) -> bool:
        """
        执行优化感知的处理逻辑
        
        Args:
            context: 数据上下文
            
        Returns:
            bool: 执行是否成功
        """
        start_time = time.time()
        
        # 获取当前阶段需要处理的数据
        processing_data = self._get_processing_data(context)
        if not processing_data:
            log(_("optimization_aware_stage.no_data_to_process", 
                             stage=self.name), "debug")
            return True
        
        # 统计优化效果
        total_items = len(processing_data)
        skipped_items = 0
        simplified_items = 0
        full_processed_items = 0
        
        processed_results = []
        
        for item_name, item_data in processing_data.items():
            # 检查是否有优化决策
            decision = self._get_optimization_decision(item_name)
            
            if decision and decision.get('skip_processing', False):
                # 跳过处理
                skipped_items += 1
                log(_("optimization_aware_stage.item_skipped",
                                 stage=self.name, item=item_name), "debug")
                continue
            elif decision and decision.get('use_simplified', False):
                # 简化处理
                simplified_items += 1
                result = self._process_item_simplified(item_name, item_data, context)
                if result:
                    processed_results.append(result)
                log(_("optimization_aware_stage.item_simplified",
                                 stage=self.name, item=item_name), "debug")
            else:
                # 完整处理
                full_processed_items += 1
                result = self._process_item_full(item_name, item_data, context)
                if result:
                    processed_results.append(result)
        
        # 保存处理结果
        self._save_processing_results(processed_results, context)
        
        # 记录优化效果
        execution_time = time.time() - start_time
        self._log_optimization_effects(total_items, skipped_items, simplified_items, 
                                     full_processed_items, execution_time)
        
        return True
    
    def _execute_traditional(self, context: DataContext) -> bool:
        """
        执行传统处理逻辑（无优化）
        
        Args:
            context: 数据上下文
            
        Returns:
            bool: 执行是否成功
        """
        # 委托给子类实现的传统处理逻辑
        return self._execute_traditional_processing(context)
    
    def _get_optimization_decision(self, item_name: str) -> Optional[Dict[str, Any]]:
        """
        获取指定项目的优化决策
        
        Args:
            item_name: 项目名称
            
        Returns:
            Optional[Dict[str, Any]]: 优化决策，如果没有则返回None
        """
        # 直接匹配
        if item_name in self.section_decisions:
            return self.section_decisions[item_name]
        
        # 模糊匹配（处理段落名称变化）
        for section_name, decision in self.section_decisions.items():
            if self._is_related_section(item_name, section_name):
                return decision
        
        return None
    
    def _is_related_section(self, item_name: str, section_name: str) -> bool:
        """
        判断项目是否与段落相关
        
        Args:
            item_name: 项目名称
            section_name: 段落名称
            
        Returns:
            bool: 是否相关
        """
        # 基本的关联性判断逻辑
        item_lower = item_name.lower()
        section_lower = section_name.lower()
        
        # 检查关键词匹配
        if 'address' in section_lower and 'address' in item_lower:
            return True
        if 'service' in section_lower and 'service' in item_lower:
            return True
        if 'policy' in section_lower and 'policy' in item_lower:
            return True
        if 'interface' in section_lower and 'interface' in item_lower:
            return True
        if 'zone' in section_lower and 'zone' in item_lower:
            return True
        
        # 检查包含关系
        if section_lower in item_lower or item_lower in section_lower:
            return True
        
        return False
    
    def _log_optimization_effects(self, total: int, skipped: int, simplified: int, 
                                full: int, execution_time: float):
        """记录优化效果"""
        if total > 0:
            skip_ratio = skipped / total * 100
            simplified_ratio = simplified / total * 100
            full_ratio = full / total * 100
            
            log(f"阶段 {self.name} 优化效果:")
            log(f"  总项目: {total}")
            log(f"  跳过处理: {skipped} ({skip_ratio:.1f}%)")
            log(f"  简化处理: {simplified} ({simplified_ratio:.1f}%)")
            log(f"  完整处理: {full} ({full_ratio:.1f}%)")
            log(f"  执行时间: {execution_time:.2f}秒")
    
    # 抽象方法，需要子类实现
    
    @abstractmethod
    def _get_processing_data(self, context: DataContext) -> Dict[str, Any]:
        """
        获取当前阶段需要处理的数据
        
        Args:
            context: 数据上下文
            
        Returns:
            Dict[str, Any]: 需要处理的数据字典
        """
        pass
    
    @abstractmethod
    def _process_item_simplified(self, item_name: str, item_data: Any, 
                                context: DataContext) -> Optional[Any]:
        """
        简化处理单个项目
        
        Args:
            item_name: 项目名称
            item_data: 项目数据
            context: 数据上下文
            
        Returns:
            Optional[Any]: 处理结果，如果失败则返回None
        """
        pass
    
    @abstractmethod
    def _process_item_full(self, item_name: str, item_data: Any, 
                          context: DataContext) -> Optional[Any]:
        """
        完整处理单个项目
        
        Args:
            item_name: 项目名称
            item_data: 项目数据
            context: 数据上下文
            
        Returns:
            Optional[Any]: 处理结果，如果失败则返回None
        """
        pass
    
    @abstractmethod
    def _save_processing_results(self, results: List[Any], context: DataContext):
        """
        保存处理结果
        
        Args:
            results: 处理结果列表
            context: 数据上下文
        """
        pass
    
    @abstractmethod
    def _execute_traditional_processing(self, context: DataContext) -> bool:
        """
        执行传统处理逻辑（无优化）
        
        Args:
            context: 数据上下文
            
        Returns:
            bool: 执行是否成功
        """
        pass
