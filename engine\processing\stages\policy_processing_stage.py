#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
策略处理阶段 - 完全重新实现
负责处理FortiGate防火墙策略配置，转换为NTOS格式并生成XML片段
支持安全策略和NAT策略的统一处理
"""

import re
from typing import Dict, Any, List
from engine.processing.pipeline.pipeline_stage import PipelineStage
from engine.processing.pipeline.data_flow import DataContext
from engine.utils.logger import log, user_log
from engine.utils.i18n import _
from lxml import etree


class PolicyProcessor:
    """
    策略处理器 - 完全重新实现，借鉴旧架构核心逻辑
    """
    
    def __init__(self):
        # 默认配置（借鉴旧架构）
        self.default_group = "def-group"
        self.default_action = "permit"
        self.default_time_range = "any"
        
        # 动作映射（借鉴旧架构）
        self.action_mapping = {
            "accept": "permit",
            "deny": "deny",
            "reject": "deny"
        }
        
        # 安全功能映射（借鉴旧架构）
        self.security_profile_mapping = {
            "av-profile": "default-alert",
            "ips-sensor": "default-use-signature-action"
        }

    def _get_ntos_version(self, context=None) -> str:
        """
        获取NTOS目标版本信息 - 注意：这是NTOS的版本，不是FortiGate的版本

        Args:
            context: 数据上下文

        Returns:
            str: NTOS版本 (R10P2, R11)
        """
        if context:
            # 方法1：从CLI参数中获取NTOS版本信息
            ntos_version = context.get_data("ntos_version") or context.get_data("version")
            if ntos_version:
                # 标准化版本格式
                version_str = str(ntos_version).upper()
                if "R11" in version_str:
                    return "R11"
                elif "R10P2" in version_str:
                    return "R10P2"
                elif "R10" in version_str:
                    return "R10P2"  # R10默认为R10P2

        # 默认返回R11（最新NTOS版本，功能最完整）
        return "R11"

    def _is_version_supported(self, version: str, feature: str) -> bool:
        """
        检查指定NTOS版本是否支持特定功能 - 基于YANG模型验证结果

        Args:
            version: NTOS版本
            feature: 功能名称

        Returns:
            bool: 是否支持该功能
        """
        # NTOS版本功能兼容性映射表 - 基于YANG模型分析结果
        version_features = {
            "R10P2": {
                "av": True,           # R10P2支持av字段
                "ips": True,          # R10P2支持ips字段
                "websec": True,       # R10P2支持websec字段
                "content-filter": True, # R10P2支持content-filter字段
                "file-filter": False, # R10P2 YANG模型中不存在file-filter字段
                "nat": True,          # R10P2支持NAT功能（static-snat44、dynamic-snat44、static-dnat44）
            },
            "R11": {
                "av": True,           # R11支持av字段
                "ips": True,          # R11支持ips字段
                "websec": True,       # R11支持websec字段
                "content-filter": True, # R11支持content-filter字段
                "file-filter": True,  # R11 YANG模型中包含file-filter字段
                "nat": True,          # R11支持NAT功能（static-snat44、dynamic-snat44、static-dnat44）
            }
        }

        return version_features.get(version, {}).get(feature, False)

    def _check_version_supports_nat(self, target_version: str) -> bool:
        """
        检查目标版本是否支持NAT/SNAT规则（借鉴旧架构逻辑）

        Args:
            target_version: 目标NTOS版本，如'R10P2'或'R11'

        Returns:
            bool: 是否支持NAT/SNAT规则
        """
        if not target_version:
            # 如果没有指定版本，默认支持NAT（向后兼容）
            return True

        # 提取版本号
        version_upper = target_version.upper()

        # R10P2版本不支持NAT/SNAT规则
        if version_upper == "R10P2":
            return False

        # R11及以后版本支持NAT/SNAT规则
        if version_upper.startswith("R11") or version_upper.startswith("R12") or version_upper.startswith("R13"):
            return True

        # 对于其他版本，尝试解析版本号
        version_match = re.match(r'R(\d+)', version_upper)
        if version_match:
            major_version = int(version_match.group(1))
            # R11及以后版本支持NAT
            return major_version >= 11

        # 无法识别的版本，默认支持（向后兼容）
        return True
    
    def classify_policy(self, policy: Dict[str, Any], vips: Dict = None, ippools: Dict = None) -> Dict[str, Any]:
        """
        策略分类（借鉴旧架构逻辑）
        
        Args:
            policy: FortiGate策略配置
            vips: VIP配置字典
            ippools: IP池配置字典
            
        Returns: Dict[str, Any]: 策略分类结果
        """
        vips = vips or {}
        ippools = ippools or {}
        
        classification = {
            "is_security_policy": True,  # 所有策略都需要安全策略
            "needs_dnat": False,
            "needs_snat": False,
            "vip_targets": [],
            "ippool_targets": [],
            "nat_type": None
        }
        
        # 检查DNAT需求（目标地址包含VIP）
        dstaddr_list = policy.get("dstaddr", [])
        if isinstance(dstaddr_list, str):
            dstaddr_list = [dstaddr_list]
        
        for addr in dstaddr_list:
            if addr in vips:
                classification["needs_dnat"] = True
                classification["vip_targets"].append(addr)
                classification["nat_type"] = "dnat"
        
        # 检查SNAT需求（启用了NAT）
        if policy.get("nat") == "enable":
            classification["needs_snat"] = True
            classification["nat_type"] = "snat"
            
            # 检查IP池
            ippool = policy.get("ippool")
            if ippool and ippool in ippools:
                classification["ippool_targets"].append(ippool)
        
        log(_("policy_processor.policy_classification_complete", policy_id=policy.get("policyid", "unknown"), needs_dnat=classification["needs_dnat"], needs_snat=classification["needs_snat"]), "debug")
        
        return classification

    def _resolve_zone_to_interfaces(self, zone_name: str, interface_mapping: Dict[str, str], context=None) -> List[str]:
        """
        将FortiGate逻辑区域名称解析为NTOS接口列表

        FortiGate的区域概念：
        - LAN/DMZ/WAN等是基于接口role的逻辑区域，不是在config system zone中定义的
        - ssl.root是SSL VPN的特殊接口

        Args:
            zone_name: FortiGate逻辑区域名称
            interface_mapping: 接口映射表
            context: 数据上下文

        Returns: List[str]: 该区域包含的NTOS接口名称列表
        """
        try:
            # FortiGate逻辑区域到NTOS标准区域的映射
            fortigate_logical_zones = {
                "LAN": "trust",
                "WAN": "untrust",
                "DMZ": "DMZ",
                "ssl.root": "trust",  # SSL VPN用户归入trust区域
                "MGMT": "trust"
            }

            # 检查是否为FortiGate逻辑区域
            if zone_name in fortigate_logical_zones:
                ntos_zone = fortigate_logical_zones[zone_name]
                log(_("policy_processor.fortigate_logical_zone_mapped", fortigate_zone=zone_name, ntos_zone=ntos_zone), "info")

                # 对于逻辑区域，我们返回空列表，让策略处理器使用区域名称而不是具体接口
                # 这符合NTOS的区域使用方式
                return []

            # 如果不是逻辑区域，尝试从实际的zone配置中查找
            if not context:
                log(_("policy_processor.cannot_get_data_context_for_zone", zone=zone_name), "debug")
                return []

            # 获取解析的区域配置
            parsed_data = context.get_data("config_data")
            if not parsed_data or "zones" not in parsed_data:
                log(_("policy_processor.cannot_get_zone_config", zone=zone_name), "debug")
                return []

            zones = parsed_data["zones"]

            # 查找指定区域
            target_zone = None
            for zone in zones:
                if zone.get("name") == zone_name:
                    target_zone = zone
                    break

            if not target_zone:
                log(_("policy_processor.zone_config_not_found", zone=zone_name), "debug")
                return []

            # 获取区域中的接口列表
            zone_interfaces = target_zone.get("interfaces", [])
            if not zone_interfaces:
                log(_("policy_processor.zone_no_interfaces", zone=zone_name), "debug")
                return []

            # 将FortiGate接口名称映射为NTOS接口名称
            mapped_interfaces = []
            for intf in zone_interfaces:
                clean_intf = intf.strip('"').strip("'")
                if clean_intf in interface_mapping:
                    ntos_intf = interface_mapping[clean_intf]
                    mapped_interfaces.append(ntos_intf)
                    log(_("policy_processor.zone_interface_mapping", original=clean_intf, mapped=ntos_intf), "debug")
                else:
                    log(_("policy_processor.zone_interface_mapping_not_found", zone=zone_name, interface=clean_intf), "warning")

            log(_("policy_processor.zone_resolved_to_interfaces", zone=zone_name, interfaces=mapped_interfaces), "info")
            return mapped_interfaces

        except Exception as e:
            log(_("policy_processor.zone_parsing_exception", zone=zone_name, error=str(e)), "error")
            return []



    def _validate_zone_with_yang(self, zone_name: str, zone_interfaces: List[str], model: str, version: str) -> bool:
        """
        使用YANG模型验证区域配置

        Args:
            zone_name: 区域名称
            zone_interfaces: 区域包含的接口列表
            model: 设备型号
            version: 设备版本

        Returns:
            bool: 验证是否通过
        """
        try:
            # 获取YANG管理器
            from engine.processing.pipeline.data_flow import DataContext
            context = DataContext.get_current()

            if not context:
                return True  # 无法获取上下文时，跳过验证

            # 基本的区域名称验证（根据NTOS标准）
            # 标准区域名称通常为trust、untrust等
            standard_zones = ["trust", "untrust", "dmz", "mgmt", "internal", "external"]

            if zone_name.lower() in [z.lower() for z in standard_zones]:
                log(_("policy_processor.zone_is_standard_name", zone=zone_name), "debug")
                return True

            # 自定义区域名称验证（长度、字符等）
            if len(zone_name) > 32:  # NTOS通常限制区域名称长度
                log(_("policy_processor.zone_name_too_long", zone=zone_name), "warning")
                return False

            # 检查是否包含非法字符
            import re
            if not re.match(r'^[a-zA-Z0-9_-]+$', zone_name):
                log(_("policy_processor.zone_name_invalid_chars", zone=zone_name), "warning")
                return False

            log(_("policy_processor.zone_yang_validation_passed", zone=zone_name), "debug")
            return True

        except Exception as e:
            log(_("policy_processor.zone_validation_exception", zone=zone_name, error=str(e)), "warning")
            return True  # 验证异常时，允许通过

    def _validate_interface_name_with_yang(self, interface_name: str, model: str, version: str) -> bool:
        """
        使用YANG模型验证接口名称

        Args:
            interface_name: NTOS接口名称
            model: 设备型号
            version: 设备版本

        Returns:
            bool: 验证是否通过
        """
        try:
            # 基本的NTOS接口名称格式验证
            # 格式通常为: Ge0/0, Ge0/1.100, Lo0等
            import re

            # 物理接口格式: Ge0/0, Ge0/1等
            if re.match(r'^Ge\d+/\d+$', interface_name):
                return True

            # TenGe物理接口格式: TenGe0/0, TenGe0/1等
            if re.match(r'^TenGe\d+/\d+$', interface_name):
                return True

            # VLAN子接口格式: Ge0/0.100等
            if re.match(r'^Ge\d+/\d+\.\d+$', interface_name):
                return True

            # TenGe VLAN子接口格式: TenGe0/0.100等
            if re.match(r'^TenGe\d+/\d+\.\d+$', interface_name):
                return True

            # 环回接口格式: Lo0, Lo1等
            if re.match(r'^Lo\d+$', interface_name):
                return True

            # 其他可能的接口格式
            if re.match(r'^(Eth|FastEthernet|GigabitEthernet|TenGigabitEthernet)\d+/\d+(\.\d+)?$', interface_name):
                return True

            log(_("policy_processor.interface_format_invalid", interface=interface_name), "debug")
            return False

        except Exception as e:
            log(_("policy_processor.interface_validation_exception", interface=interface_name, error=str(e)), "warning")
            return True  # 验证异常时，允许通过

    def _validate_interface_mapping_strict(self, policy: Dict[str, Any], interface_mapping: Dict[str, str], context=None) -> bool:
        """
        严格验证策略中的接口映射（支持区域名称解析）

        Args:
            policy: FortiGate策略配置
            interface_mapping: 接口映射表
            context: 数据上下文

        Returns:
            bool: 验证是否通过
        """
        policy_id = policy.get("policyid", "unknown")

        log(_("policy_processor.strict_validation_policy_interface_mapping", policy_id=policy_id), "info")
        log(_("policy_processor.available_interface_mapping", interface_mapping=interface_mapping), "info")

        if not interface_mapping:
            log(_("policy_processor.policy_interface_mapping_empty", policy_id=policy_id), "warning")
            return False

        # 检查源接口映射
        srcintf_list = policy.get("srcintf", [])
        if isinstance(srcintf_list, str):
            srcintf_list = [srcintf_list]

        log(_("policy_processor.check_source_interfaces", srcintf_list=srcintf_list), "info")

        for intf in srcintf_list:
            if intf and intf.strip():
                clean_intf = intf.strip('"').strip("'")
                log(_("policy_processor.verify_source_interface_zone", clean_intf=clean_intf), "info")

                # 首先检查是否为具体接口名称
                if clean_intf in interface_mapping:
                    log(_("policy_processor.source_interface_found_in_mapping", clean_intf=clean_intf), "info")
                    continue

                # 检查是否为区域名称（使用新的区域识别方法）
                if self._is_zone_name(clean_intf, context):
                    log(_("policy_processor.source_zone_identified", clean_intf=clean_intf), "info")
                    continue

                # 既不是接口也不是区域
                log(_("policy_processor.source_interface_not_found_and_not_zone", policy_id=policy_id, clean_intf=clean_intf), "warning")
                log(_("policy_processor.available_interface_mapping_keys", keys=list(interface_mapping.keys())), "info")
                return False

        # 检查目标接口映射
        dstintf_list = policy.get("dstintf", [])
        if isinstance(dstintf_list, str):
            dstintf_list = [dstintf_list]

        log(_("policy_processor.check_destination_interfaces", dstintf_list=dstintf_list), "info")

        for intf in dstintf_list:
            if intf and intf.strip():
                clean_intf = intf.strip('"').strip("'")
                log(_("policy_processor.verify_destination_interface_zone", clean_intf=clean_intf), "info")

                # 首先检查是否为具体接口名称
                if clean_intf in interface_mapping:
                    log(_("policy_processor.destination_interface_found_in_mapping", clean_intf=clean_intf), "info")
                    continue

                # 检查是否为区域名称（使用新的区域识别方法）
                if self._is_zone_name(clean_intf, context):
                    log(_("policy_processor.destination_zone_identified", clean_intf=clean_intf), "info")
                    continue

                # 既不是接口也不是区域
                log(_("policy_processor.destination_interface_not_found_and_not_zone", policy_id=policy_id, clean_intf=clean_intf), "warning")
                log(_("policy_processor.available_interface_mapping_keys", keys=list(interface_mapping.keys())), "info")
                return False

        log(_("policy_processor.policy_all_interface_zone_mapping_passed", policy_id=policy_id), "info")
        return True

    def convert_security_policy(self, policy: Dict[str, Any], interface_mapping: Dict[str, str], context=None) -> Dict[str, Any]:
        """
        转换安全策略（借鉴旧架构逻辑）

        Args:
            policy: FortiGate策略配置
            interface_mapping: 接口映射表
            context: 数据上下文

        Returns: Dict[str, Any]: NTOS安全策略配置，如果验证失败返回None
        """
        policy_id = policy.get("policyid", "unknown")
        policy_name = policy.get("name", f"policy_{policy_id}")

        # 🚨 CRITICAL: 新架构严格验证接口映射 - 主要执行路径
        log(_("policy_processor.critical_new_architecture_policy_processing", policy_id=policy_id), "info")

        try:
            validation_result = self._validate_interface_mapping_strict(policy, interface_mapping, context)
            if not validation_result:
                log(_("policy_processor.policy_interface_mapping_validation_failed_skip", policy_id=policy_id, policy_name=policy_name), "warning")
                return None
            else:
                log(_("policy_processor.policy_interface_mapping_validation_passed_alt", policy_id=policy_id, policy_name=policy_name), "info")
        except Exception as e:
            log(_("policy_processor.policy_interface_mapping_validation_exception", policy_id=policy_id, policy_name=policy_name, error=str(e)), "error")
            return None

        # 构建NTOS安全策略配置
        security_policy = {
            "name": policy_name,
            "enabled": policy.get("status", "enable") == "enable",
            "group-name": self.default_group,
            "action": self.action_mapping.get(policy.get("action", "accept"), self.default_action),
            "time-range": self.default_time_range,
            "config-source": "manual",
            "session-timeout": 0
        }
        
        # 处理源区域和源接口 - 区分区域名称和接口名称
        srcintf_list = policy.get("srcintf", [])
        if isinstance(srcintf_list, str):
            srcintf_list = [srcintf_list]


        source_zones = []
        source_interfaces = []

        # 分离区域名称和接口名称
        zone_names, interface_names = self._separate_zones_and_interfaces(srcintf_list, context)

        # 处理区域名称
        for zone_name in zone_names:
            clean_zone = zone_name.strip('"').strip("'")
            source_zones.append(clean_zone)
            log(_("policy_processor.zone_identified_as_source", policy_id=policy_id, zone_name=clean_zone), "info")

        # 处理接口名称
        for intf in interface_names:
            if intf and intf.strip():
                clean_intf = intf.strip('"').strip("'")
                # 由于已通过严格验证，这里应该总是能找到映射
                if clean_intf in interface_mapping:
                    mapped_intf = interface_mapping[clean_intf]
                    source_interfaces.append(mapped_intf)
                    log(_("policy_processor.interface_identified_as_source", policy_id=policy_id, clean_intf=clean_intf, mapped_intf=mapped_intf), "info")
                else:
                    # 这种情况不应该发生，因为已经通过了严格验证
                    log(_("policy_processor.policy_source_interface_mapping_missing_after_validation", policy_id=policy_id, clean_intf=clean_intf), "error")

        # 按照NTOS YANG模型要求生成zone数据结构（list source-zone with key "name"）
        if source_zones:
            security_policy["source-zone"] = [{"name": zone} for zone in source_zones]
        if source_interfaces:
            security_policy["source-interface"] = [{"name": intf} for intf in source_interfaces]

        # 处理目标区域和目标接口 - 区分区域名称和接口名称
        dstintf_list = policy.get("dstintf", [])
        if isinstance(dstintf_list, str):
            dstintf_list = [dstintf_list]

        dest_zones = []
        dest_interfaces = []

        # 分离区域名称和接口名称
        dest_zone_names, dest_interface_names = self._separate_zones_and_interfaces(dstintf_list, context)

        # 处理区域名称
        for zone_name in dest_zone_names:
            clean_zone = zone_name.strip('"').strip("'")
            dest_zones.append(clean_zone)
            log(_("policy_processor.zone_identified_as_dest", policy_id=policy_id, zone_name=clean_zone), "info")

        # 处理接口名称
        for intf in dest_interface_names:
            if intf and intf.strip():
                clean_intf = intf.strip('"').strip("'")
                # 由于已通过严格验证，这里应该总是能找到映射
                if clean_intf in interface_mapping:
                    mapped_intf = interface_mapping[clean_intf]
                    dest_interfaces.append(mapped_intf)
                    log(_("policy_processor.interface_identified_as_dest", policy_id=policy_id, clean_intf=clean_intf, mapped_intf=mapped_intf), "info")
                else:
                    # 这种情况不应该发生，因为已经通过了严格验证
                    log(_("policy_processor.policy_destination_interface_mapping_missing_after_validation", policy_id=policy_id, clean_intf=clean_intf), "error")

        # 按照NTOS YANG模型要求生成目标zone数据结构（list dest-zone with key "name"）
        if dest_zones:
            security_policy["dest-zone"] = [{"name": zone} for zone in dest_zones]
        if dest_interfaces:
            security_policy["dest-interface"] = [{"name": intf} for intf in dest_interfaces]

        # 处理源地址（按照NTOS YANG模型：list source-network with key "name"）
        srcaddr_list = policy.get("srcaddr", [])
        if isinstance(srcaddr_list, str):
            srcaddr_list = [srcaddr_list]

        if srcaddr_list and srcaddr_list != ["all"]:
            security_policy["source-network"] = [{"name": addr} for addr in srcaddr_list]

        # 处理目标地址（按照NTOS YANG模型：list dest-network with key "name"）
        dstaddr_list = policy.get("dstaddr", [])
        if isinstance(dstaddr_list, str):
            dstaddr_list = [dstaddr_list]

        if dstaddr_list and dstaddr_list != ["all"]:
            security_policy["dest-network"] = [{"name": addr} for addr in dstaddr_list]

        # 处理服务（按照NTOS YANG模型：list service with key "name"）
        service_list = policy.get("service", [])
        if isinstance(service_list, str):
            service_list = [service_list]

        if service_list and service_list != ["ALL"]:
            security_policy["service"] = [{"name": svc} for svc in service_list]
        
        # 处理安全功能
        self._add_security_features(policy, security_policy, context)
        
        # 添加描述
        if "comments" in policy:
            # 清理description字段以符合YANG约束
            from engine.utils.name_validator import clean_ntos_description
            cleaned_comments = clean_ntos_description(policy["comments"])
            security_policy["description"] = cleaned_comments
        
        log(_("policy_processor.security_policy_conversion_success", policy_name=policy_name), "debug")
        
        return security_policy
    
    def _add_security_features(self, policy: Dict[str, Any], security_policy: Dict[str, Any], context=None):
        """
        添加安全功能映射 - 基于规范的映射规则，支持版本兼容性检测

        Args:
            policy: FortiGate策略配置
            security_policy: NTOS安全策略配置
            context: 数据上下文（用于获取版本信息）
        """
        # 获取NTOS版本信息进行兼容性检测
        ntos_version = self._get_ntos_version(context)

        # 规范：任何av-profile都映射到default-alert
        av_profile = policy.get("av_profile") or policy.get("av-profile")
        if av_profile and self._is_version_supported(ntos_version, "av"):
            security_policy["av"] = "default-alert"
        elif av_profile and not self._is_version_supported(ntos_version, "av"):
            log(f"警告: NTOS版本 {ntos_version} 不支持av功能，跳过av-profile配置", "warning")

        # 规范：任何ips-sensor都映射到default-use-signature-action
        ips_sensor = policy.get("ips_sensor") or policy.get("ips-sensor")
        if ips_sensor and self._is_version_supported(ntos_version, "ips"):
            security_policy["ips"] = "default-use-signature-action"
        elif ips_sensor and not self._is_version_supported(ntos_version, "ips"):
            log(f"警告: NTOS版本 {ntos_version} 不支持ips功能，跳过ips-sensor配置", "warning")

        # Web过滤配置文件映射到websec模板
        webfilter_profile = policy.get("webfilter_profile") or policy.get("webfilter-profile")
        if webfilter_profile and self._is_version_supported(ntos_version, "websec"):
            security_policy["websec"] = "default-websec-template"
        elif webfilter_profile and not self._is_version_supported(ntos_version, "websec"):
            log(f"警告: NTOS版本 {ntos_version} 不支持websec功能，跳过webfilter-profile配置", "warning")

        # 内容过滤配置文件映射到content-filter模板
        content_filter_profile = policy.get("content_filter_profile") or policy.get("content-filter-profile")
        if content_filter_profile and self._is_version_supported(ntos_version, "content-filter"):
            security_policy["content-filter"] = "default-content-filter-template"
        elif content_filter_profile and not self._is_version_supported(ntos_version, "content-filter"):
            log(f"警告: NTOS版本 {ntos_version} 不支持content-filter功能，跳过content-filter-profile配置", "warning")

        # 文件过滤配置文件映射到file-filter模板（仅R11支持）
        file_filter_profile = policy.get("file_filter_profile") or policy.get("file-filter-profile")
        if file_filter_profile and self._is_version_supported(ntos_version, "file-filter"):
            security_policy["file-filter"] = "default-file-filter-template"
        elif file_filter_profile and not self._is_version_supported(ntos_version, "file-filter"):
            log(f"警告: NTOS版本 {ntos_version} 不支持file-filter功能，跳过file-filter-profile配置", "warning")
    
    def convert_nat_rule(self, policy: Dict[str, Any], classification: Dict[str, Any], 
                        vips: Dict = None, ippools: Dict = None, 
                        interface_mapping: Dict[str, str] = None) -> List[Dict[str, Any]]:
        """
        转换NAT规则（借鉴旧架构逻辑）
        
        Args:
            policy: FortiGate策略配置
            classification: 策略分类结果
            vips: VIP配置字典
            ippools: IP池配置字典
            interface_mapping: 接口映射表
            
        Returns: List[Dict[str, Any]]: NTOS NAT规则列表
        """
        nat_rules = []
        vips = vips or {}
        ippools = ippools or {}
        interface_mapping = interface_mapping or {}
        
        policy_name = policy.get("name", f"policy_{policy.get('policyid', 'unknown')}")
        
        # 生成DNAT规则
        if classification["needs_dnat"]:
            for vip_name in classification["vip_targets"]:
                if vip_name in vips:
                    dnat_rule = self._create_dnat_rule(policy, vip_name, vips[vip_name], interface_mapping)
                    if dnat_rule:
                        nat_rules.append(dnat_rule)
        
        # 生成SNAT规则
        if classification["needs_snat"]:
            snat_rule = self._create_snat_rule(policy, classification, ippools, interface_mapping)
            if snat_rule:
                nat_rules.append(snat_rule)
        
        log(_("policy_processor.nat_rule_conversion_complete", policy_name=policy_name, count=len(nat_rules)), "debug")
        
        return nat_rules
    
    def _create_dnat_rule(self, policy: Dict[str, Any], vip_name: str, vip_config: Dict[str, Any], 
                         interface_mapping: Dict[str, str]) -> Dict[str, Any]:
        """
        创建DNAT规则（借鉴旧架构逻辑）
        
        Args:
            policy: FortiGate策略配置
            vip_name: VIP名称
            vip_config: VIP配置
            interface_mapping: 接口映射表
            
        Returns: Dict[str, Any]: DNAT规则配置
        """
        policy_name = policy.get("name", f"policy_{policy.get('policyid', 'unknown')}")
        
        dnat_rule = {
            "name": f"{policy_name}_{vip_name}",  # 简化命名：策略名_VIP名
            "type": "static-dnat44",
            "enabled": True,
            "time-range": self.default_time_range,
            "external-ip": vip_config.get("extip", ""),
            "internal-ip": vip_config.get("mappedip", ""),
            "external-port": vip_config.get("extport", ""),
            "internal-port": vip_config.get("mappedport", ""),
            "protocol": vip_config.get("protocol", "tcp")
        }
        
        # 处理接口
        srcintf_list = policy.get("srcintf", [])
        if isinstance(srcintf_list, str):
            srcintf_list = [srcintf_list]
        
        source_interfaces = []
        for intf in srcintf_list:
            if intf in interface_mapping:
                source_interfaces.append(interface_mapping[intf])
        
        if source_interfaces:
            dnat_rule["source-interface"] = [{"name": intf} for intf in source_interfaces]
        
        log(_("policy_processor.create_dnat_rule", name=dnat_rule['name']), "debug")
        
        return dnat_rule
    
    def _create_snat_rule(self, policy: Dict[str, Any], classification: Dict[str, Any], 
                         ippools: Dict = None, interface_mapping: Dict[str, str] = None) -> Dict[str, Any]:
        """
        创建SNAT规则（借鉴旧架构逻辑）
        
        Args:
            policy: FortiGate策略配置
            classification: 策略分类结果
            ippools: IP池配置字典
            interface_mapping: 接口映射表
            
        Returns: Dict[str, Any]: SNAT规则配置
        """
        ippools = ippools or {}
        interface_mapping = interface_mapping or {}
        
        policy_name = policy.get("name", f"policy_{policy.get('policyid', 'unknown')}")
        
        # 检查是否使用IP池
        if classification["ippool_targets"]:
            # 动态SNAT（使用IP池）
            ippool_name = classification["ippool_targets"][0]
            snat_rule = {
                "name": policy_name,  # 直接使用策略名称
                "type": "dynamic-snat44",
                "enabled": True,
                "time-range": self.default_time_range,
                "pool-name": ippool_name,
                "try-no-pat": False  # 转换源端口
            }
        else:
            # 静态SNAT（使用接口IP）
            snat_rule = {
                "name": policy_name,  # 直接使用策略名称
                "type": "static-snat44",
                "enabled": True,
                "time-range": self.default_time_range,
                "try-no-pat": True  # 优先使用原端口
            }
        
        # 处理源地址（按照NTOS YANG模型：list source-network with key "name"）
        srcaddr_list = policy.get("srcaddr", [])
        if isinstance(srcaddr_list, str):
            srcaddr_list = [srcaddr_list]

        if srcaddr_list and srcaddr_list != ["all"]:
            snat_rule["source-network"] = [{"name": addr} for addr in srcaddr_list]

        # 处理目标地址（按照NTOS YANG模型：list dest-network with key "name"）
        dstaddr_list = policy.get("dstaddr", [])
        if isinstance(dstaddr_list, str):
            dstaddr_list = [dstaddr_list]

        if dstaddr_list and dstaddr_list != ["all"]:
            snat_rule["dest-network"] = [{"name": addr} for addr in dstaddr_list]
        
        # 处理接口
        dstintf_list = policy.get("dstintf", [])
        if isinstance(dstintf_list, str):
            dstintf_list = [dstintf_list]
        
        dest_interfaces = []
        for intf in dstintf_list:
            if intf in interface_mapping:
                dest_interfaces.append(interface_mapping[intf])
        
        if dest_interfaces:
            snat_rule["dest-interface"] = [{"name": intf} for intf in dest_interfaces]
        
        log(_("policy_processor.create_snat_rule", name=snat_rule['name']), "debug")
        
        return snat_rule

    def _process_nat_pools(self, ippools: Dict) -> List[Dict[str, Any]]:
        """
        处理IP池配置，生成NAT池定义

        Args:
            ippools: IP池配置字典

        Returns:
            list: NAT池配置列表
        """
        nat_pools = []

        for pool_name, pool_config in ippools.items():
            try:
                # 验证池配置完整性
                if not self._validate_ippool_config(pool_config, pool_name):
                    continue

                # 创建NAT池配置
                nat_pool = {
                    "name": pool_name,
                    "address": {
                        "value": f"{pool_config['startip']}-{pool_config['endip']}"
                    }
                }

                # 添加描述信息（如果存在）
                if "comment" in pool_config and pool_config["comment"]:
                    nat_pool["desc"] = pool_config["comment"]

                nat_pools.append(nat_pool)

            except Exception as e:
                log(f"ERROR: Failed to process IP pool '{pool_name}': {str(e)}", "error")
                continue

        return nat_pools

    def _validate_ippool_config(self, pool_config: Dict, pool_name: str) -> bool:
        """
        验证IP池配置完整性

        Args:
            pool_config: IP池配置
            pool_name: 池名称

        Returns:
            bool: 配置是否有效
        """
        required_fields = ["startip", "endip"]
        for field in required_fields:
            if field not in pool_config or not pool_config[field]:
                log(f"WARNING: IP pool '{pool_name}' missing required field '{field}'", "warning")
                return False

        # 验证IP地址格式
        try:
            import ipaddress
            start_ip = ipaddress.IPv4Address(pool_config["startip"])
            end_ip = ipaddress.IPv4Address(pool_config["endip"])

            if start_ip > end_ip:
                log(f"WARNING: IP pool '{pool_name}' has invalid range: start IP {start_ip} > end IP {end_ip}", "warning")
                return False

        except ipaddress.AddressValueError as e:
            log(f"WARNING: IP pool '{pool_name}' has invalid IP address format: {str(e)}", "warning")
            return False

        return True

    def process_policies(self, policies: List[Dict], vips: Dict = None, ippools: Dict = None,
                        interface_mapping: Dict[str, str] = None, target_version: str = None, context=None) -> Dict[str, Any]:
        """
        处理策略列表

        Args:
            policies: FortiGate策略列表
            vips: VIP配置字典
            ippools: IP池配置字典
            interface_mapping: 接口映射表
            target_version: 目标NTOS版本（如R10P2、R11等）
            context: 数据上下文

        Returns: Dict[str, Any]: 处理结果
        """
        vips = vips or {}
        ippools = ippools or {}
        interface_mapping = interface_mapping or {}

        # 检查目标版本是否支持NAT/SNAT规则（借鉴旧架构）
        supports_nat = self._check_version_supports_nat(target_version)

        if not supports_nat:
            log(_("policy_processor.target_version_not_support_nat_skip_generation", target_version=target_version), "info")
            user_log(_("policy_processor.target_version_not_support_nat_config_skipped", target_version=target_version), "warning")

        result = {
            "security_policies": [],
            "nat_rules": [],
            "nat_pools": [],  # 新增：NAT池配置
            "converted_count": 0,
            "skipped_count": 0,
            "failed_count": 0,
            "nat_skipped_count": 0,  # 新增：NAT跳过计数
            "details": [],
            "version_info": {
                "target_version": target_version,
                "supports_nat": supports_nat
            }
        }

        # 处理地址池配置（如果版本支持NAT）
        if supports_nat and ippools:
            nat_pools = self._process_nat_pools(ippools)
            result["nat_pools"] = nat_pools
        
        for policy in policies:
            try:
                policy_id = policy.get("policyid", "unknown")
                policy_name = self._generate_intelligent_policy_name(policy, policy_id)
                
                # 策略分类
                classification = self.classify_policy(policy, vips, ippools)
                
                # 转换安全策略 - 严格验证接口映射
                security_policy = self.convert_security_policy(policy, interface_mapping, context)

                if security_policy is None:
                    # 接口映射验证失败，跳过此策略
                    result["skipped_count"] += 1
                    result["details"].append({
                        "id": policy_id,
                        "name": policy_name,
                        "status": "skipped",
                        "reason": "接口映射验证失败",
                        "security_policy": False,
                        "nat_rules": 0,
                        "classification": classification
                    })
                    log(_("policy_processor.policy_skipped_interface_mapping_validation_failed", policy_name=policy_name), "warning")
                    continue

                # 安全策略转换成功，添加到结果中
                result["security_policies"].append(security_policy)

                # 转换NAT规则（根据版本支持情况）
                nat_rules = []
                if supports_nat:
                    nat_rules = self.convert_nat_rule(policy, classification, vips, ippools, interface_mapping)
                    result["nat_rules"].extend(nat_rules)
                else:
                    # R10P2版本不支持NAT，记录警告
                    if classification["needs_dnat"] or classification["needs_snat"]:
                        result["nat_skipped_count"] += 1
                        policy_name = policy.get("name", f"policy_{policy_id}")
                        log(_("policy_processor.policy_contains_nat_but_version_not_support", policy_name=policy_name, target_version=target_version), "warning")
                        user_log(_("policy_processor.policy_contains_nat_version_warning", policy_name=policy_name, target_version=target_version), "warning")

                result["converted_count"] += 1
                result["details"].append({
                    "id": policy_id,
                    "name": policy_name,
                    "status": "success",
                    "security_policy": True,
                    "nat_rules": len(nat_rules),
                    "nat_skipped": not supports_nat and (classification["needs_dnat"] or classification["needs_snat"]),
                    "classification": classification,
                    "version_support": {
                        "target_version": target_version,
                        "supports_nat": supports_nat
                    }
                })
                
            except Exception as e:
                result["failed_count"] += 1
                result["details"].append({
                    "id": policy.get("policyid", "unknown"),
                    "name": policy.get("name", "unknown"),
                    "status": "failed",
                    "reason": str(e)
                })
                log(_("policy_processor.policy_processing_failed", policy_id=policy.get('policyid', 'unknown'), error=str(e)), "error")

        log(_("policy_processor.policy_processing_complete_detailed", converted_count=result['converted_count'], skipped_count=result['skipped_count'], failed_count=result['failed_count']), "info")

        return result

    def _is_zone_name(self, name: str, context=None) -> bool:
        """
        检查给定名称是否为已定义的区域名称

        Args:
            name: 要检查的名称
            context: 数据上下文

        Returns:
            bool: 如果是区域名称返回True，否则返回False
        """
        try:

            # 检查FortiGate逻辑区域
            fortigate_logical_zones = {"LAN", "WAN", "DMZ", "ssl.root", "MGMT"}
            if name in fortigate_logical_zones:
                return True

            # 检查用户定义的区域
            if context:
                parsed_data = context.get_data("config_data")
                if parsed_data and "system" in parsed_data and "zone" in parsed_data["system"]:
                    zones = parsed_data["system"]["zone"]
                    if name in zones:
                        return True

            return False

        except Exception as e:
            log(_("policy_processor.zone_check_exception", name=name, error=str(e)), "error")
            return False

    def _get_zone_names_from_context(self, context) -> set:
        """
        从数据上下文获取所有区域名称

        Args:
            context: 数据上下文

        Returns:
            set: 所有区域名称的集合
        """
        zone_names = set()

        try:
            # 添加FortiGate逻辑区域
            zone_names.update({"LAN", "WAN", "DMZ", "ssl.root", "MGMT"})

            # 添加用户定义的区域
            if context:
                parsed_data = context.get_data("config_data")
                if parsed_data and "system" in parsed_data and "zone" in parsed_data["system"]:
                    zones = parsed_data["system"]["zone"]
                    zone_names.update(zones.keys())

        except Exception as e:
            log(_("policy_processor.get_zone_names_exception", error=str(e)), "error")

        return zone_names

    def _separate_zones_and_interfaces(self, names: List[str], context=None) -> tuple:
        """
        分离区域名称和接口名称

        Args:
            names: 名称列表
            context: 数据上下文

        Returns:
            tuple: (zone_names, interface_names)
        """
        zones = []
        interfaces = []


        for name in names:
            is_zone = self._is_zone_name(name, context)

            if is_zone:
                zones.append(name)
                log(_("policy_processor.zone_identified", name=name), "info")
            else:
                interfaces.append(name)
                log(_("policy_processor.interface_identified", name=name), "info")

        return zones, interfaces


class PolicyProcessingStage(PipelineStage):
    """
    策略处理阶段 - 完全重新实现
    
    负责处理FortiGate防火墙策略配置，转换为NTOS格式并生成XML片段
    支持安全策略和NAT策略的统一处理
    """
    
    def __init__(self):
        super().__init__("policy_processing", _("policy_processing_stage.description"))
        self.processor = PolicyProcessor()

    def validate_input(self, context: DataContext) -> bool:
        """
        验证输入数据

        Args:
            context: 数据上下文

        Returns:
            bool: 验证是否通过
        """
        config_data = context.get_data("config_data")
        if not config_data:
            context.add_error(_("policy_processor.missing_config_data"))
            return False

        return True

    def process(self, context: DataContext) -> bool:
        """
        执行策略处理

        Args:
            context: 数据上下文

        Returns:
            bool: 处理是否成功
        """
        log(_("policy_processor.start_processing"), "info")

        try:
            # 获取配置数据
            config_data = context.get_data("config_data", {})
            policies = config_data.get("policies", [])
            vips = config_data.get("vips", {})
            ippools = config_data.get("ippools", {})

            log(_("policy_processor.found_policies_count", count=len(policies)), "info")

            if not policies:
                log(_("policy_processor.no_policies_to_process"), "info")
                context.set_data("policy_processing_result", self._empty_result())
                return True

            # 获取接口映射信息
            interface_result = context.get_data("interface_processing_result", {})
            interface_mapping = interface_result.get("interface_mapping", {})

            # 获取目标版本信息（借鉴旧架构）
            target_version = context.get_data("version") or context.get_data("target_version")
            if target_version:
                log(_("policy_processor.detected_target_version", target_version=target_version), "info")
            else:
                log(_("policy_processor.target_version_not_specified"), "info")

            # 使用新的处理器处理策略（传递版本信息）
            policy_result = self.processor.process_policies(policies, vips, ippools, interface_mapping, target_version, context)

            # 生成XML片段
            security_xml_fragment = self._generate_security_policy_xml_fragment(policy_result)

            # 根据版本支持情况生成NAT XML片段
            version_info = policy_result.get("version_info", {})
            supports_nat = version_info.get("supports_nat", True)

            if supports_nat:
                nat_xml_fragment = self._generate_nat_xml_fragment(policy_result)
            else:
                nat_xml_fragment = ""
                log(_("policy_processor.target_version_not_support_nat_skip_xml", target_version=target_version), "info")

            # 构建处理结果（包含策略和XML片段）
            processing_result = {
                "policies": policy_result,
                "security_xml_fragment": security_xml_fragment,
                "nat_xml_fragment": nat_xml_fragment,
                "statistics": {
                    "total_policies": len(policies),
                    "converted_policies": policy_result['converted_count'],
                    "skipped_policies": policy_result['skipped_count'],
                    "failed_policies": policy_result['failed_count'],
                    "security_policies": len(policy_result['security_policies']),
                    "nat_rules": len(policy_result['nat_rules']),
                    "nat_skipped": policy_result.get('nat_skipped_count', 0),
                    "target_version": target_version,
                    "supports_nat": supports_nat
                }
            }

            # 存储处理结果
            context.set_data("policy_processing_result", processing_result)

            nat_skipped = policy_result.get('nat_skipped_count', 0)
            if nat_skipped > 0:
                log(_("policy_processor.policy_processing_complete_with_nat_version", converted_count=policy_result['converted_count'], total=len(policies), security_policies=len(policy_result['security_policies']), nat_rules=len(policy_result['nat_rules']), nat_skipped=nat_skipped, target_version=target_version), "info")
            else:
                log(_("policy_processor.policy_processing_complete_without_nat_version", converted_count=policy_result['converted_count'], total=len(policies), security_policies=len(policy_result['security_policies']), nat_rules=len(policy_result['nat_rules'])), "info")

            return True

        except Exception as e:
            error_msg = _("policy_processor.processing_failed", error=str(e))
            log(error_msg, "error")
            context.set_data("policy_processing_result", self._empty_result())
            context.add_error(error_msg, self.name)
            return False

    def _generate_security_policy_xml_fragment(self, policy_result: Dict) -> str:
        """
        生成安全策略XML片段（借鉴旧架构，遵循YANG模型）

        Args:
            policy_result: 策略处理结果

        Returns:
            str: XML片段字符串
        """
        try:
            security_policies = policy_result.get("security_policies", [])

            if not security_policies:
                return ""

            # 创建security-policy根元素
            security_policy_elem = etree.Element("security-policy")
            security_policy_elem.set("xmlns", "urn:ruijie:ntos:params:xml:ns:yang:security-policy")

            # 处理每个安全策略
            for policy in security_policies:
                self._add_security_policy_to_xml(security_policy_elem, policy)

            # 生成XML字符串
            xml_string = etree.tostring(
                security_policy_elem,
                encoding='unicode',
                pretty_print=True
            )

            log(_("policy_processor.security_policy_xml_generated", count=len(security_policies)), "debug")
            return xml_string

        except Exception as e:
            log(_("policy_processor.security_policy_xml_failed", error=str(e)), "error")
            return ""

    def _add_security_policy_to_xml(self, security_policy_elem: etree.Element, policy: Dict):
        """
        添加安全策略到XML（遵循YANG模型结构）

        Args:
            security_policy_elem: security-policy XML元素
            policy: 策略配置
        """
        # 创建policy元素（不继承父元素的xmlns属性）
        policy_elem = etree.Element("policy")  # 使用Element而不是SubElement
        security_policy_elem.append(policy_elem)  # 手动添加到父元素

        # 确保policy元素没有xmlns属性（符合NTOS YANG模型）
        if "xmlns" in policy_elem.attrib:
            del policy_elem.attrib["xmlns"]

        # 添加策略名称
        name_elem = etree.SubElement(policy_elem, "name")
        name_elem.text = policy.get("name", "")

        # 添加启用状态
        enabled_elem = etree.SubElement(policy_elem, "enabled")
        enabled_elem.text = "true" if policy.get("enabled", True) else "false"

        # 添加描述（按照YANG模型顺序，在group-name之前）
        if "description" in policy and policy["description"]:
            # 清理description字段以符合YANG约束
            from engine.utils.name_validator import clean_ntos_description
            cleaned_description = clean_ntos_description(policy["description"])
            desc_elem = etree.SubElement(policy_elem, "description")
            desc_elem.text = cleaned_description

        # 添加组名
        group_name_elem = etree.SubElement(policy_elem, "group-name")
        group_name_elem.text = policy.get("group-name", "def-group")

        # 添加源区域
        source_zones = policy.get("source-zone", [])
        for zone in source_zones:
            source_zone_elem = etree.SubElement(policy_elem, "source-zone")
            zone_name_elem = etree.SubElement(source_zone_elem, "name")
            zone_name_elem.text = zone

        # 添加目标区域
        dest_zones = policy.get("dest-zone", [])
        for zone in dest_zones:
            dest_zone_elem = etree.SubElement(policy_elem, "dest-zone")
            zone_name_elem = etree.SubElement(dest_zone_elem, "name")
            zone_name_elem.text = zone

        # 添加源接口
        source_interfaces = policy.get("source-interface", [])
        for intf in source_interfaces:
            source_intf_elem = etree.SubElement(policy_elem, "source-interface")
            intf_name_elem = etree.SubElement(source_intf_elem, "name")
            intf_name_elem.text = intf

        # 添加目标接口
        dest_interfaces = policy.get("dest-interface", [])
        for intf in dest_interfaces:
            dest_intf_elem = etree.SubElement(policy_elem, "dest-interface")
            intf_name_elem = etree.SubElement(dest_intf_elem, "name")
            intf_name_elem.text = intf

        # 添加源网络
        source_networks = policy.get("source-network", [])
        for network in source_networks:
            source_network_elem = etree.SubElement(policy_elem, "source-network")
            network_name_elem = etree.SubElement(source_network_elem, "name")
            network_name_elem.text = network

        # 添加目标网络
        dest_networks = policy.get("dest-network", [])
        for network in dest_networks:
            dest_network_elem = etree.SubElement(policy_elem, "dest-network")
            network_name_elem = etree.SubElement(dest_network_elem, "name")
            network_name_elem.text = network

        # 添加服务
        services = policy.get("service", [])
        for service in services:
            service_elem = etree.SubElement(policy_elem, "service")
            service_name_elem = etree.SubElement(service_elem, "name")
            service_name_elem.text = service

        # 添加时间范围
        time_range_elem = etree.SubElement(policy_elem, "time-range")
        time_range_elem.text = policy.get("time-range", "any")

        # 添加动作
        action_elem = etree.SubElement(policy_elem, "action")
        action_elem.text = policy.get("action", "permit")

        # 添加配置源
        config_source_elem = etree.SubElement(policy_elem, "config-source")
        config_source_elem.text = policy.get("config-source", "manual")

        # 添加会话超时
        session_timeout_elem = etree.SubElement(policy_elem, "session-timeout")
        session_timeout_elem.text = str(policy.get("session-timeout", 0))

        # 添加安全功能
        if "ips" in policy:
            ips_elem = etree.SubElement(policy_elem, "ips")
            ips_elem.text = policy["ips"]

        if "av" in policy:
            av_elem = etree.SubElement(policy_elem, "av")
            av_elem.text = policy["av"]

        # 注意：description字段已经在前面按照YANG顺序添加了

        log(_("policy_processor.add_security_policy", name=policy.get('name', 'unknown')), "debug")

    def _generate_nat_xml_fragment(self, policy_result: Dict) -> str:
        """
        生成NAT XML片段（借鉴旧架构，遵循YANG模型）

        Args:
            policy_result: 策略处理结果

        Returns:
            str: XML片段字符串
        """
        try:
            nat_rules = policy_result.get("nat_rules", [])

            if not nat_rules:
                return ""

            # 创建nat根元素
            nat_elem = etree.Element("nat")
            nat_elem.set("xmlns", "urn:ruijie:ntos:params:xml:ns:yang:nat")

            # 处理每个NAT规则
            for rule in nat_rules:
                self._add_nat_rule_to_xml(nat_elem, rule)

            # 生成XML字符串
            xml_string = etree.tostring(
                nat_elem,
                encoding='unicode',
                pretty_print=True
            )

            log(_("policy_processor.nat_xml_generated", count=len(nat_rules)), "debug")
            return xml_string

        except Exception as e:
            log(_("policy_processor.nat_xml_generation_failed", error=str(e)), "error")
            return ""

    def _add_nat_rule_to_xml(self, nat_elem: etree.Element, rule: Dict):
        """
        添加NAT规则到XML（遵循YANG模型）

        Args:
            nat_elem: nat XML元素
            rule: NAT规则配置
        """
        rule_type = rule.get("type", "static-snat44")

        # 创建对应类型的NAT规则元素
        rule_elem = etree.SubElement(nat_elem, rule_type)

        # 添加规则名称
        name_elem = etree.SubElement(rule_elem, "name")
        name_elem.text = rule.get("name", "")

        # 添加启用状态
        enabled_elem = etree.SubElement(rule_elem, "enabled")
        enabled_elem.text = "true" if rule.get("enabled", True) else "false"

        # 创建match容器
        match_elem = etree.SubElement(rule_elem, "match")

        # 添加源网络
        source_networks = rule.get("source-network", [])
        for network in source_networks:
            source_network_elem = etree.SubElement(match_elem, "source-network")
            network_name_elem = etree.SubElement(source_network_elem, "name")
            network_name_elem.text = network

        # 添加目标网络
        dest_networks = rule.get("dest-network", [])
        for network in dest_networks:
            dest_network_elem = etree.SubElement(match_elem, "dest-network")
            network_name_elem = etree.SubElement(dest_network_elem, "name")
            network_name_elem.text = network

        # 添加源接口
        source_interfaces = rule.get("source-interface", [])
        for intf in source_interfaces:
            source_intf_elem = etree.SubElement(match_elem, "source-interface")
            intf_name_elem = etree.SubElement(source_intf_elem, "name")
            intf_name_elem.text = intf

        # 添加目标接口
        dest_interfaces = rule.get("dest-interface", [])
        for intf in dest_interfaces:
            dest_intf_elem = etree.SubElement(match_elem, "dest-interface")
            intf_name_elem = etree.SubElement(dest_intf_elem, "name")
            intf_name_elem.text = intf

        # 添加时间范围
        time_range_elem = etree.SubElement(match_elem, "time-range")
        time_range_value_elem = etree.SubElement(time_range_elem, "value")
        time_range_value_elem.text = rule.get("time-range", "any")

        # 创建translate-to容器
        translate_elem = etree.SubElement(rule_elem, "translate-to")

        if rule_type == "static-dnat44":
            # DNAT规则特定字段
            if "external-ip" in rule:
                external_ip_elem = etree.SubElement(translate_elem, "ipv4-address")
                external_ip_elem.text = rule["external-ip"]

            if "internal-ip" in rule:
                internal_ip_elem = etree.SubElement(translate_elem, "translated-ipv4-address")
                internal_ip_elem.text = rule["internal-ip"]

            if "external-port" in rule:
                external_port_elem = etree.SubElement(translate_elem, "port")
                external_port_elem.text = str(rule["external-port"])

            if "internal-port" in rule:
                internal_port_elem = etree.SubElement(translate_elem, "translated-port")
                internal_port_elem.text = str(rule["internal-port"])

        elif rule_type == "dynamic-snat44":
            # 动态SNAT规则特定字段
            if "pool-name" in rule:
                pool_name_elem = etree.SubElement(translate_elem, "pool-name")
                pool_name_elem.text = rule["pool-name"]

            if "try-no-pat" in rule:
                try_no_pat_elem = etree.SubElement(translate_elem, "try-no-pat")
                try_no_pat_elem.text = "true" if rule["try-no-pat"] else "false"

        elif rule_type == "static-snat44":
            # 静态SNAT规则特定字段
            if "try-no-pat" in rule:
                try_no_pat_elem = etree.SubElement(translate_elem, "try-no-pat")
                try_no_pat_elem.text = "true" if rule["try-no-pat"] else "false"

        log(_("policy_processor.add_nat_rule", name=rule.get('name', 'unknown'), rule_type=rule_type), "debug")

    def _empty_result(self) -> Dict[str, Any]:
        """
        返回空的处理结果

        Returns: Dict[str, Any]: 空结果
        """
        return {
            "policies": {
                "security_policies": [],
                "nat_rules": [],
                "converted_count": 0,
                "skipped_count": 0,
                "failed_count": 0,
                "nat_skipped_count": 0,
                "details": [],
                "version_info": {
                    "target_version": None,
                    "supports_nat": True
                }
            },
            "security_xml_fragment": "",
            "nat_xml_fragment": "",
            "statistics": {
                "total_policies": 0,
                "converted_policies": 0,
                "skipped_policies": 0,
                "failed_policies": 0,
                "security_policies": 0,
                "nat_rules": 0,
                "nat_skipped": 0,
                "target_version": None,
                "supports_nat": True
            }
        }

    def _is_zone_name(self, name: str, context=None) -> bool:
        """
        检查给定名称是否为已定义的区域名称
        代理方法，调用PolicyProcessor中的实现

        Args:
            name: 要检查的名称
            context: 数据上下文

        Returns:
            bool: 如果是区域名称返回True，否则返回False
        """
        return self.processor._is_zone_name(name, context)

    def _get_zone_names_from_context(self, context) -> set:
        """
        从数据上下文获取所有区域名称
        代理方法，调用PolicyProcessor中的实现

        Args:
            context: 数据上下文

        Returns:
            set: 所有区域名称的集合
        """
        return self.processor._get_zone_names_from_context(context)

    def _separate_zones_and_interfaces(self, names: List[str], context=None) -> tuple:
        """
        分离区域名称和接口名称
        代理方法，调用PolicyProcessor中的实现

        Args:
            names: 名称列表
            context: 数据上下文

        Returns:
            tuple: (区域名称列表, 接口名称列表)
        """
        return self.processor._separate_zones_and_interfaces(names, context)

    def _generate_intelligent_policy_name(self, policy: dict, policy_id: str) -> str:
        """
        智能生成策略名称

        优先级策略：
        1. 保留FortiGate原始名称（如果存在set name）
        2. 基于注释智能生成语义化名称
        3. 基于接口和地址生成描述性名称
        4. 使用改进的默认格式

        Args:
            policy: FortiGate策略配置
            policy_id: 策略ID

        Returns:
            str: 生成的策略名称
        """
        # 调试：记录策略157的详细信息
        if policy_id == "157":
            log(f"🔍 策略{policy_id}智能命名开始", "info")
            log(f"  - 策略数据keys: {list(policy.keys())}", "info")
            log(f"  - name字段: {policy.get('name', 'None')}", "info")
            log(f"  - comments字段: {policy.get('comments', 'None')}", "info")

        # 优先级1：保留原始名称
        if "name" in policy and policy["name"]:
            original_name = policy["name"]
            # 确保名称符合NTOS YANG模型规范
            sanitized_name = self._sanitize_policy_name(original_name)
            if policy_id == "157":
                log(f"🔍 策略{policy_id}使用原始名称: {original_name} -> {sanitized_name}", "info")
            return sanitized_name

        # 优先级2：基于注释生成语义化名称
        if "comments" in policy and policy["comments"]:
            semantic_name = self._generate_name_from_comments(policy["comments"], policy_id)
            if semantic_name:
                if policy_id == "157":
                    log(f"🔍 策略{policy_id}使用注释生成名称: {policy['comments']} -> {semantic_name}", "info")
                return semantic_name
            elif policy_id == "157":
                log(f"🔍 策略{policy_id}注释解析失败: {policy['comments']}", "info")

        # 优先级3：基于接口和地址生成描述性名称
        descriptive_name = self._generate_descriptive_name(policy, policy_id)
        if descriptive_name:
            if policy_id == "157":
                log(f"🔍 策略{policy_id}使用描述性名称: {descriptive_name}", "info")
            return descriptive_name

        # 优先级4：改进的默认格式
        default_name = f"policy_{policy_id}"
        if policy_id == "157":
            log(f"🔍 策略{policy_id}使用默认名称: {default_name}", "info")
        return default_name

    def _sanitize_policy_name(self, name: str) -> str:
        """
        清理策略名称，确保符合NTOS YANG模型规范

        NTOS不允许的字符：`~!#$%^&*+|{};:"',\\/<>?

        Args:
            name: 原始名称

        Returns:
            str: 清理后的名称
        """
        import re

        # 移除或替换不允许的字符
        # 将常见的分隔符替换为下划线
        name = re.sub(r'[/\\|]', '_', name)
        # 移除其他不允许的字符
        name = re.sub(r'[`~!#$%^&*+{};:"\'<>?]', '', name)
        # 清理多余的下划线和空格
        name = re.sub(r'[_\s]+', '_', name)
        # 移除首尾的下划线
        name = name.strip('_')

        # 确保名称不为空
        if not name:
            return "unnamed_policy"

        # 限制长度（建议不超过64个字符）
        if len(name) > 64:
            name = name[:64].rstrip('_')

        return name

    def _generate_name_from_comments(self, comments: str, policy_id: str) -> str:
        """
        基于注释生成语义化的策略名称

        解析常见的注释模式：
        - "(Copy of XXX)" -> "XXX_Copy"
        - "(Reverse of XXX)" -> "XXX_Reverse"
        - "XXX to YYY" -> "XXX_to_YYY"
        - "Allow XXX" -> "Allow_XXX"

        Args:
            comments: 策略注释
            policy_id: 策略ID

        Returns:
            str: 生成的名称，如果无法解析则返回None
        """
        import re

        if not comments:
            return None

        # 清理注释，移除多余的括号和空格
        clean_comments = comments.strip()

        # 模式1: (Copy of XXX) 或 (Reverse of XXX)
        copy_pattern = r'\(Copy of ([^)]+)\)'
        reverse_pattern = r'\(Reverse of ([^)]+)\)'

        copy_match = re.search(copy_pattern, clean_comments, re.IGNORECASE)
        reverse_match = re.search(reverse_pattern, clean_comments, re.IGNORECASE)

        if reverse_match:
            base_name = reverse_match.group(1).strip()
            generated_name = f"{base_name}_Reverse"
            return self._sanitize_policy_name(generated_name)
        elif copy_match:
            base_name = copy_match.group(1).strip()
            generated_name = f"{base_name}_Copy"
            return self._sanitize_policy_name(generated_name)

        # 模式2: XXX to YYY
        to_pattern = r'(\w+)\s+to\s+(\w+)'
        to_match = re.search(to_pattern, clean_comments, re.IGNORECASE)
        if to_match:
            source = to_match.group(1)
            dest = to_match.group(2)
            generated_name = f"{source}_to_{dest}"
            return self._sanitize_policy_name(generated_name)

        # 模式3: Allow XXX 或 Deny XXX
        action_pattern = r'(Allow|Deny|Block|Permit)\s+(\w+)'
        action_match = re.search(action_pattern, clean_comments, re.IGNORECASE)
        if action_match:
            action = action_match.group(1)
            target = action_match.group(2)
            generated_name = f"{action}_{target}"
            return self._sanitize_policy_name(generated_name)

        # 模式4: 提取第一个有意义的词作为基础
        words = re.findall(r'\b[A-Za-z_][A-Za-z0-9_]{2,}\b', clean_comments)
        if words:
            # 过滤掉常见的无意义词汇
            meaningful_words = [w for w in words if w.lower() not in
                              ['the', 'and', 'for', 'with', 'from', 'policy', 'rule']]
            if meaningful_words:
                base_word = meaningful_words[0]
                generated_name = f"{base_word}_Policy"
                return self._sanitize_policy_name(generated_name)

        return None

    def _generate_descriptive_name(self, policy: dict, policy_id: str) -> str:
        """
        基于接口和地址生成描述性策略名称

        格式：{源接口/区域}_{目标接口/区域}_{动作}_{服务}
        例如：LAN_to_DMZ_PERMIT_HTTP

        Args:
            policy: FortiGate策略配置
            policy_id: 策略ID

        Returns:
            str: 生成的描述性名称，如果无法生成则返回None
        """
        name_parts = []

        # 获取源接口/区域
        srcintf = policy.get("srcintf", [])
        if srcintf:
            # 取第一个接口作为代表
            src_name = srcintf[0] if isinstance(srcintf, list) else srcintf
            # 简化接口名称
            src_name = self._simplify_interface_name(src_name)
            name_parts.append(src_name)

        # 添加连接词
        if name_parts:
            name_parts.append("to")

        # 获取目标接口/区域
        dstintf = policy.get("dstintf", [])
        if dstintf:
            # 取第一个接口作为代表
            dst_name = dstintf[0] if isinstance(dstintf, list) else dstintf
            # 简化接口名称
            dst_name = self._simplify_interface_name(dst_name)
            name_parts.append(dst_name)

        # 获取动作
        action = policy.get("action", "permit").upper()
        if action in ["ACCEPT", "ALLOW"]:
            action = "PERMIT"
        elif action in ["DENY", "DROP"]:
            action = "DENY"
        name_parts.append(action)

        # 获取服务（可选）
        service = policy.get("service", [])
        if service and len(service) == 1:  # 只有一个服务时才添加到名称中
            service_name = service[0] if isinstance(service, list) else service
            # 简化服务名称
            service_name = self._simplify_service_name(service_name)
            if service_name and service_name != "ALL":
                name_parts.append(service_name)

        # 如果没有足够的信息，返回None
        if len(name_parts) < 3:  # 至少需要源、目标、动作
            return None

        # 组合名称
        generated_name = "_".join(name_parts)
        return self._sanitize_policy_name(generated_name)

    def _simplify_interface_name(self, interface_name: str) -> str:
        """
        简化接口名称，提取关键部分

        Args:
            interface_name: 原始接口名称

        Returns:
            str: 简化后的接口名称
        """
        if not interface_name:
            return "ANY"

        # 常见的接口名称映射
        name_mappings = {
            "any": "ANY",
            "wan": "WAN",
            "wan1": "WAN",
            "wan2": "WAN2",
            "lan": "LAN",
            "dmz": "DMZ",
            "internal": "LAN",
            "external": "WAN"
        }

        # 转换为小写进行匹配
        lower_name = interface_name.lower()

        # 直接映射
        if lower_name in name_mappings:
            return name_mappings[lower_name]

        # 提取端口号（如port1, port2等）
        import re
        port_match = re.match(r'port(\d+)', lower_name)
        if port_match:
            return f"PORT{port_match.group(1)}"

        # 其他情况，返回原名称的大写版本（限制长度）
        simplified = interface_name.upper()[:8]
        return simplified

    def _simplify_service_name(self, service_name: str) -> str:
        """
        简化服务名称，提取关键部分

        Args:
            service_name: 原始服务名称

        Returns:
            str: 简化后的服务名称
        """
        if not service_name:
            return "ANY"

        # 常见的服务名称映射
        service_mappings = {
            "all": "ALL",
            "any": "ANY",
            "http": "HTTP",
            "https": "HTTPS",
            "ftp": "FTP",
            "ssh": "SSH",
            "telnet": "TELNET",
            "smtp": "SMTP",
            "pop3": "POP3",
            "imap": "IMAP",
            "dns": "DNS",
            "dhcp": "DHCP",
            "snmp": "SNMP",
            "ntp": "NTP"
        }

        # 转换为小写进行匹配
        lower_name = service_name.lower()

        # 直接映射
        if lower_name in service_mappings:
            return service_mappings[lower_name]

        # 提取端口号（如TCP_80, UDP_53等）
        import re
        port_match = re.match(r'(tcp|udp)[_-]?(\d+)', lower_name)
        if port_match:
            protocol = port_match.group(1).upper()
            port = port_match.group(2)
            return f"{protocol}_{port}"

        # 其他情况，返回原名称的大写版本（限制长度）
        simplified = service_name.upper()[:10]
        return simplified
