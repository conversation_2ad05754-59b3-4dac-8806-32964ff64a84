#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
服务对象组处理阶段
负责将FortiGate服务对象组转换为NTOS格式
依赖于服务对象处理阶段的结果
"""

import json
import os
import re
from typing import Dict, Any, List
from engine.processing.pipeline.pipeline_stage import PipelineStage
from engine.processing.pipeline.data_flow import DataContext
from engine.utils.logger import log
from engine.utils.i18n import _
from engine.utils.pipeline_stage_user_logger import get_pipeline_user_logger
from engine.utils.ntos_name_validator import NTOSNameValidator
from lxml import etree


class ServiceGroupProcessor:
    """
    服务对象组处理器 - 完全重新实现，借鉴旧架构核心逻辑
    """

    def __init__(self):
        # 加载预定义服务映射（借鉴旧架构）
        self.service_mapping = {}
        self.fortigate_predefined_services = set()
        self._load_predefined_service_mapping()

    def _load_predefined_service_mapping(self):
        """
        加载预定义服务映射（借鉴旧架构逻辑）
        """
        try:
            # 获取当前脚本目录
            current_dir = os.path.dirname(os.path.abspath(__file__))
            # 构建映射文件路径
            mapping_file = os.path.join(current_dir, "..", "..", "data", "mappings", "service_mapping.json")

            # 检查文件是否存在
            if not os.path.exists(mapping_file):
                log(_("service_group_processor.predefined_mapping_file_not_exist", file=mapping_file), "warning")
                return

            # 加载映射文件
            with open(mapping_file, 'r', encoding='utf-8') as f:
                self.service_mapping = json.load(f)

            # 记录所有FortiGate预定义服务名称
            self.fortigate_predefined_services = set(self.service_mapping.keys())



        except Exception as e:
            log(_("service_group_processor.predefined_mapping_load_failed", error=str(e)), "warning")
            self.service_mapping = {}
            self.fortigate_predefined_services = set()

    def is_predefined_service(self, service_name: str) -> bool:
        """
        检查服务名称是否为预定义服务（借鉴旧架构逻辑）

        Args:
            service_name: 服务名称

        Returns:
            bool: 是否为预定义服务
        """
        if not service_name:
            return False

        # 先精确匹配
        if service_name in self.fortigate_predefined_services:
            return True

        # 然后忽略大小写匹配
        for predefined_name in self.fortigate_predefined_services:
            if service_name.lower() == predefined_name.lower():
                return True

        # 检查别名匹配
        for predefined_name, mapping in self.service_mapping.items():
            if "aliases" in mapping and isinstance(mapping["aliases"], list):
                # 精确别名匹配
                if service_name in mapping["aliases"]:
                    return True

                # 忽略大小写别名匹配
                for alias in mapping["aliases"]:
                    if service_name.lower() == alias.lower():
                        return True

        return False

    def get_predefined_service_mapping(self, service_name: str) -> Dict:
        """
        获取预定义服务映射信息（借鉴旧架构逻辑）

        Args:
            service_name: 服务名称

        Returns:
            Dict: 服务映射信息，如果没有映射则返回None
        """
        if not service_name:
            return None

        # 先精确匹配
        if service_name in self.service_mapping:
            return self.service_mapping[service_name]

        # 然后忽略大小写匹配
        for predefined_name, mapping in self.service_mapping.items():
            if service_name.lower() == predefined_name.lower():
                return mapping

        # 检查别名匹配
        for predefined_name, mapping in self.service_mapping.items():
            if "aliases" in mapping and isinstance(mapping["aliases"], list):
                # 精确别名匹配
                if service_name in mapping["aliases"]:
                    return mapping

                # 忽略大小写别名匹配
                for alias in mapping["aliases"]:
                    if service_name.lower() == alias.lower():
                        return mapping

        return None

    def validate_group_members(self, members: List[str], converted_services: Dict, mapped_services: Dict) -> List[str]:
        """
        验证服务组成员是否在已转换的服务对象中存在（借鉴旧架构逻辑）

        Args:
            members: 服务组成员列表
            converted_services: 已转换的服务对象字典
            mapped_services: 已映射的服务对象字典

        Returns: List[str]: 有效的成员列表
        """
        valid_members = []

        for member in members:
            # 检查是否在转换的服务对象中
            if member in converted_services:
                valid_members.append(converted_services[member]['name'])
            # 检查是否在映射的服务对象中
            elif member in mapped_services:
                valid_members.append(mapped_services[member]['name'])
            # 检查是否为预定义服务（借鉴旧架构逻辑）
            elif self.is_predefined_service(member):
                mapping = self.get_predefined_service_mapping(member)
                if mapping and "ntos_service" in mapping:
                    ntos_service_name = mapping["ntos_service"]
                    valid_members.append(ntos_service_name)
                else:
                    # 预定义服务但没有NTOS映射，使用原名
                    valid_members.append(member)
            else:
                log(_("service_group_processor.member_not_found_in_converted_or_predefined", member=member), "warning")

        return valid_members

    def process_service_groups(self, service_groups: Dict, converted_services: Dict, mapped_services: Dict) -> Dict[str, Any]:
        """
        处理服务对象组字典

        Args:
            service_groups: 服务对象组字典
            converted_services: 已转换的服务对象字典
            mapped_services: 已映射的服务对象字典

        Returns:
            Dict: 处理结果
        """
        result = {
            "converted": {},
            "skipped": {},
            "failed": {},
            "converted_count": 0,
            "skipped_count": 0,
            "failed_count": 0,
            "details": []
        }

        for group_name, group_config in service_groups.items():
            try:
                # 获取成员列表（借鉴旧架构的member字段处理）
                members = group_config.get('member', [])
                if not members:
                    # 检查是否使用了其他字段名
                    members = group_config.get('members', [])

                if not members:
                    reason = _("service_group_processing.empty_group")
                    log(_("service_group_processor.skip_empty_group", group=group_name), "warning")
                    self._add_skipped_result(result, group_name, group_config, reason)
                    continue

                # 确保members是列表格式
                if not isinstance(members, list):
                    members = [members]

                # 验证成员是否都已转换或映射
                valid_members = self.validate_group_members(members, converted_services, mapped_services)

                if not valid_members:
                    reason = _("service_group_processing.no_valid_members")
                    log(_("service_group_processor.skip_no_valid_members", group=group_name), "warning")
                    self._add_skipped_result(result, group_name, group_config, reason)
                    continue

                # 创建服务对象组（符合NTOS格式）
                converted_group = {
                    "name": group_name,
                    "type": "service_group",
                    "members": valid_members,
                    "description": group_config.get("comment", ""),
                    "enabled": True,
                    "original_members": members,  # 保留原始成员信息用于调试
                    "valid_members_count": len(valid_members),
                    "total_members_count": len(members)
                }

                result["converted"][group_name] = converted_group
                result["converted_count"] += 1
                result["details"].append({
                    "name": group_name,
                    "status": "success",
                    "type": "service_group",
                    "members_count": len(valid_members),
                    "converted": converted_group
                })
                log(_("service_group_processor.conversion_success", group=group_name, valid=len(valid_members), total=len(members)), "debug")

            except Exception as e:
                reason = _("service_group_processing.processing_error", error=str(e))
                self._add_failed_result(result, group_name, group_config, reason)

        return result

    def _add_skipped_result(self, result: Dict, name: str, config: Dict, reason: str):
        """添加跳过的结果"""
        result["skipped"][name] = config
        result["skipped_count"] += 1
        result["details"].append({
            "name": name,
            "status": "skipped",
            "reason": reason,
            "original": config
        })

    def _add_failed_result(self, result: Dict, name: str, config: Dict, reason: str):
        """添加失败的结果"""
        result["failed"][name] = config
        result["failed_count"] += 1
        result["details"].append({
            "name": name,
            "status": "failed",
            "reason": reason,
            "original": config
        })


class ServiceGroupProcessingStage(PipelineStage):
    """服务对象组处理阶段"""
    
    def __init__(self):
        super().__init__("service_group_processing", _("service_group_processing_stage.description"))
        self.processor = ServiceGroupProcessor()
        self.name_validator = NTOSNameValidator()  # 添加名称验证器
        self.user_logger = None  # 将在process方法中初始化
    
    def validate_input(self, context: DataContext) -> bool:
        """
        验证输入数据
        
        Args:
            context: 数据上下文
            
        Returns:
            bool: 验证是否通过
        """
        # 检查是否有服务对象处理结果
        service_result = context.get_data("service_processing_result")
        if not service_result:
            log(_("service_group_processing_stage.no_service_result"), "warning")
            return False
        
        return True
    
    def process(self, context: DataContext) -> bool:
        """
        执行服务对象组处理
        
        Args:
            context: 数据上下文
            
        Returns:
            bool: 处理是否成功
        """
        log(_("service_group_processing_stage.starting"))

        # 初始化用户日志记录器
        language = context.get_data('language', 'zh-CN')
        self.user_logger = get_pipeline_user_logger(language)
        
        try:
            # 获取配置数据
            config_data = context.get_data("config_data", {})
            service_groups_data = config_data.get("service_groups", [])
            
            # 转换数据格式：从列表转换为字典（兼容解析器输出格式）
            service_groups = {}
            if isinstance(service_groups_data, list):
                for group in service_groups_data:
                    if isinstance(group, dict) and 'name' in group:
                        service_groups[group['name']] = group
            elif isinstance(service_groups_data, dict):
                service_groups = service_groups_data
            
            log(_("service_group_processor.data_conversion_complete", original_type=type(service_groups_data), converted_type=type(service_groups), count=len(service_groups)), "debug")
            
            if not service_groups:
                log(_("service_group_processing_stage.no_service_groups"))
                context.set_data("service_group_processing_result", self._empty_result())
                return True
            
            # 获取服务对象处理结果
            service_result = context.get_data("service_processing_result", {})
            service_objects = service_result.get("service_objects", {})
            converted_services = service_objects.get("converted", {})
            mapped_services = service_objects.get("mapped", {})

            if not converted_services and not mapped_services:
                log(_("service_group_processor.no_converted_services"), "warning")
                context.set_data("service_group_processing_result", self._empty_result())
                return True

            log(_("service_group_processor.found_groups", count=len(service_groups)), "info")

            # 应用名称规范化处理
            log(_("service_group_processor.applying_name_sanitization"), "info")
            sanitized_service_groups = self.name_validator.validate_and_sanitize_service_group_names(service_groups)

            # 记录名称变更统计
            name_stats = self.name_validator.get_statistics()
            if name_stats['names_changed'] > 0:
                log(_("service_group_processor.name_sanitization_stats",
                      total=name_stats['total_processed'],
                      changed=name_stats['names_changed']), "info")

                # 记录具体的名称映射变更
                name_mapping = self.name_validator.get_name_mapping()
                for original, sanitized in name_mapping.items():
                    if original != sanitized:
                        log(_("service_group_processor.name_changed",
                              original=original, sanitized=sanitized), "info")

            # 使用规范化后的服务组名称进行处理
            group_result = self.processor.process_service_groups(sanitized_service_groups, converted_services, mapped_services)

            # 生成XML片段
            xml_fragment = self._generate_service_group_xml_fragment(group_result)

            # 构建处理结果（包含服务对象组和XML片段）
            processing_result = {
                "service_groups": group_result,
                "xml_fragment": xml_fragment,
                "statistics": {
                    "total_groups": len(service_groups),
                    "converted_groups": group_result['converted_count'],
                    "skipped_groups": group_result['skipped_count'],
                    "failed_groups": group_result['failed_count']
                }
            }

            context.set_data("service_group_processing_result", processing_result)

            log(_("service_group_processor.processing_complete", success=group_result["converted_count"], total=len(service_groups)), "info")

            # 记录用户日志
            from engine.utils.user_log_formatter import record_stage_user_log
            record_stage_user_log("service_group_processing", processing_result)

            return True
            
        except Exception as e:
            error_msg = f"服务对象组处理失败: {str(e)}"
            error_msg = _("service_group_processing_stage.processing_failed", error=str(e))
            log(error_msg, "error")
            error_msg = _("service_group_processor.detailed_error_info",  error=error_msg)
            log(error_msg, "error")
            log(f"Config data type: {type(config_data)}", "debug")
            log(f"Original service group data type: {type(service_groups_data)}", "debug")
            log(f"Converted service group data type: {type(service_groups)}", "debug")
            context.set_data("service_group_processing_result", self._empty_result())
            context.add_error(error_msg, self.name)
            return False

    def _generate_service_group_xml_fragment(self, group_result: Dict[str, Any]) -> str:
        """
        生成符合YANG模型的服务对象组XML片段

        Args:
            group_result: 服务对象组处理结果

        Returns:
            str: XML片段字符串
        """
        try:
            # 创建service-obj根元素（不设置命名空间，让XML模板集成阶段统一处理）
            service_obj = etree.Element("service-obj")

            # 处理转换成功的服务对象组
            converted_groups = group_result.get("converted", {})

            if not converted_groups:
                log(_("service_group_processor.no_converted_groups"), "debug")
                return ""

            for group_name, group_config in converted_groups.items():
                # 创建service-group元素（符合YANG模型）
                service_group = etree.SubElement(service_obj, "service-group")

                # 添加name元素（使用规范化后的名称）
                name_elem = etree.SubElement(service_group, "name")
                name_elem.text = group_name

                # 记录原始名称信息（如果存在）
                original_name = group_config.get("original_name")
                if original_name and original_name != group_name:
                    log(_("service_group_processor.using_sanitized_name",
                          original=original_name, sanitized=group_name), "debug")

                # 添加description元素（如果有）
                description = group_config.get("description", "")
                if description:
                    desc_elem = etree.SubElement(service_group, "description")
                    desc_elem.text = description

                # 添加service-set元素（符合YANG模型约束）
                members = group_config.get("members", [])
                if not members:
                    log(_("service_group_processor.group_no_members_warning", group=group_name), "warning")
                    continue

                for member in members:
                    # 创建service-set元素
                    service_set = etree.SubElement(service_group, "service-set")

                    # 添加name元素（引用服务对象名称）
                    member_name_elem = etree.SubElement(service_set, "name")
                    member_name_elem.text = member

                log(_("service_group_processor.group_xml_generated", group=group_name, count=len(members)), "debug")

            # 转换为字符串
            xml_str = etree.tostring(service_obj, encoding='unicode', pretty_print=True)

            log(_("service_group_processor.xml_fragment_success", count=len(converted_groups)), "debug")
            return xml_str

        except Exception as e:
            log(_("service_group_processor.xml_fragment_failed", error=str(e)), "error")
            return ""

    # 旧的处理方法已被新的ServiceGroupProcessor替代
    
    def _empty_result(self) -> Dict[str, Any]:
        """
        返回空的处理结果

        Returns: Dict[str, Any]: 空结果
        """
        return {
            "service_groups": {
                "converted": {},
                "skipped": {},
                "failed": {},
                "converted_count": 0,
                "skipped_count": 0,
                "failed_count": 0,
                "details": []
            },
            "xml_fragment": "",
            "statistics": {
                "total_groups": 0,
                "converted_groups": 0,
                "skipped_groups": 0,
                "failed_groups": 0
            }
        }
