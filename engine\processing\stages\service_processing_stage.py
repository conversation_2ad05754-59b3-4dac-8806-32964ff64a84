#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
服务对象处理阶段
负责将FortiGate服务对象转换为NTOS格式
"""

import json
import os
import re
from typing import Dict, Any, List
from engine.processing.pipeline.pipeline_stage import PipelineStage
from engine.utils.logger import log
from engine.utils.i18n import _
from engine.utils.pipeline_stage_user_logger import get_pipeline_user_logger
from lxml import etree


class ServiceObjectProcessor:
    """
    服务对象处理器 - 完全重新实现，借鉴旧架构核心逻辑
    """

    def __init__(self):
        # 预定义服务映射（借鉴旧架构）
        self.predefined_services = {
            "ALL": "any",
            "HTTP": "http",
            "HTTPS": "https",
            "FTP": "ftp",
            "SSH": "ssh",
            "TELNET": "telnet",
            "SMTP": "smtp",
            "POP3": "pop3",
            "IMAP": "imap",
            "DNS": "dns",
            "DHCP": "dhcp",
            "SNMP": "snmp",
            "NTP": "ntp",
            "PING": "icmp",
            "ALL_ICMP": "icmp"
        }

        # 加载服务映射配置
        self.service_mappings = self._load_service_mappings()

    def _load_service_mappings(self) -> Dict:
        """加载服务映射配置"""
        try:
            # 尝试加载服务映射文件
            mapping_file = os.path.join("engine", "data", "input", "service_mappings.json")
            if os.path.exists(mapping_file):
                with open(mapping_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            log(_("service_processing.load_mapping_failed", error=str(e)), "warning")

        return {}

    def parse_fortigate_port_format(self, port_range: str) -> Dict[str, str]:
        """
        解析FortiGate端口格式，正确分离源端口和目的端口

        FortiGate格式说明：
        - "80" -> 目的端口80，源端口any
        - "80:1024-65535" -> 目的端口80，源端口1024-65535
        - "513:512-1023" -> 目的端口513，源端口512-1023
        - "0-65535:0-65535" -> 目的端口0-65535，源端口0-65535

        Args:
            port_range: FortiGate格式的端口范围字符串

        Returns: Dict[str, str]: 包含dest_port和source_port的字典
        """
        if not port_range:
            return {"dest_port": "", "source_port": ""}

        # 移除首尾空格
        port_range = port_range.strip()

        # 检查是否包含冒号（FortiGate的dest:source格式）
        if ":" in port_range:
            # 分离目的端口和源端口
            parts = port_range.split(":", 1)  # 只分割第一个冒号
            dest_part = parts[0].strip()
            source_part = parts[1].strip()

            return {
                "dest_port": self._normalize_single_port_range(dest_part),
                "source_port": self._normalize_single_port_range(source_part)
            }
        else:
            # 没有冒号，只有目的端口，源端口为any
            return {
                "dest_port": self._normalize_single_port_range(port_range),
                "source_port": ""  # 空字符串表示any（不限制源端口）
            }

    def _normalize_single_port_range(self, port_range: str) -> str:
        """
        标准化单个端口范围格式

        Args:
            port_range: 单个端口范围字符串

        Returns:
            str: 标准化后的端口范围
        """
        if not port_range:
            return ""

        # 移除首尾空格
        port_range = port_range.strip()

        # 处理空格分隔的多个端口：500 4500 -> 500,4500
        if " " in port_range:
            ports = port_range.split()
            valid_ports = []
            for port in ports:
                if port.isdigit() and 0 <= int(port) <= 65535:
                    valid_ports.append(port)
                elif "-" in port:
                    try:
                        start, end = map(int, port.split("-"))
                        if 0 <= start <= end <= 65535:
                            valid_ports.append(port)
                    except ValueError:
                        continue
            if valid_ports:
                return ",".join(valid_ports)

        # 处理范围格式（如1024-2048）
        if "-" in port_range:
            try:
                start, end = map(int, port_range.split("-"))
                if 0 <= start <= end <= 65535:
                    return f"{start}-{end}"
                else:
                    log(_("service_processing.port_range_out_of_limit", part=port_range), "warning")
                    return ""
            except ValueError:
                log(_("service_processing.invalid_port_range_format", part=port_range), "warning")
                return ""

        # 处理单个端口
        if port_range.isdigit():
            port_num = int(port_range)
            if 0 <= port_num <= 65535:
                return port_range
            else:
                log(_("service_processing.port_out_of_range", port=port_range), "warning")
                return ""

        log(_("service_processing.invalid_port_format", port=port_range), "warning")
        return ""

    def normalize_port_range(self, port_range: str) -> str:
        """
        标准化端口范围格式（保持向后兼容性）

        注意：此方法保持向后兼容，新代码应使用parse_fortigate_port_format()

        Args:
            port_range: 原始端口范围字符串

        Returns:
            str: 标准化后的端口范围（仅目的端口）
        """
        if not port_range:
            return ""

        # 使用新的解析器，但只返回目的端口以保持兼容性
        parsed = self.parse_fortigate_port_format(port_range)
        return parsed.get("dest_port", "")

    def is_predefined_service(self, service_name: str) -> bool:
        """检查是否为预定义服务"""
        return service_name.upper() in self.predefined_services

    def get_predefined_service_mapping(self, service_name: str) -> str:
        """获取预定义服务映射"""
        return self.predefined_services.get(service_name.upper(), service_name)

    def process_service_objects(self, service_objects: Dict) -> Dict[str, Any]:
        """
        处理服务对象字典

        Args:
            service_objects: 服务对象字典

        Returns:
            Dict: 处理结果
        """
        result = {
            "converted": {},
            "mapped": {},
            "failed": {},
            "converted_count": 0,
            "mapped_count": 0,
            "failed_count": 0,
            "details": []
        }

        for service_name, service_config in service_objects.items():
            try:
                # 检查是否为预定义服务
                if self.is_predefined_service(service_name):
                    mapped_service = self._process_predefined_service(service_name, service_config)
                    if mapped_service:
                        result["mapped"][service_name] = mapped_service
                        result["mapped_count"] += 1
                        result["details"].append({
                            "name": service_name,
                            "status": "mapped",
                            "type": "predefined",
                            "converted": mapped_service
                        })
                    else:
                        reason = _("service_processing.predefined_mapping_failed")
                        self._add_failed_result(result, service_name, service_config, reason)
                else:
                    # 处理自定义服务
                    converted_service = self._process_custom_service(service_name, service_config)
                    if converted_service:
                        result["converted"][service_name] = converted_service
                        result["converted_count"] += 1
                        result["details"].append({
                            "name": service_name,
                            "status": "success",
                            "type": "custom",
                            "converted": converted_service
                        })
                    else:
                        reason = _("service_processing.custom_conversion_failed")
                        self._add_failed_result(result, service_name, service_config, reason)

            except Exception as e:
                reason = _("service_processing.processing_error", error=str(e))
                self._add_failed_result(result, service_name, service_config, reason)

        return result

    def _process_predefined_service(self, service_name: str, service_config: Dict) -> Dict[str, Any]:
        """
        处理预定义服务

        Args:
            service_name: 服务名称
            service_config: 服务配置

        Returns:
            Dict: 转换后的服务对象
        """
        mapped_name = self.get_predefined_service_mapping(service_name)

        return {
            "name": mapped_name,
            "type": "predefined",
            "original_name": service_name,
            "description": service_config.get("comment", f"Mapped from {service_name}"),
            "vendor": "fortigate"
        }

    def _process_custom_service(self, service_name: str, service_config: Dict) -> Dict[str, Any]:
        """
        处理自定义服务

        Args:
            service_name: 服务名称
            service_config: 服务配置

        Returns:
            Dict: 转换后的服务对象
        """
        # 检查服务类型并转换（优先检测多协议服务）
        has_tcp = 'tcp-portrange' in service_config and service_config['tcp-portrange']
        has_udp = 'udp-portrange' in service_config and service_config['udp-portrange']

        if has_tcp and has_udp:
            # 多协议服务：同时有TCP和UDP
            return self._convert_tcp_udp_service(service_name, service_config)
        elif has_tcp:
            return self._convert_tcp_service(service_name, service_config)
        elif has_udp:
            return self._convert_udp_service(service_name, service_config)
        elif 'sctp-portrange' in service_config:
            return self._convert_sctp_service(service_name, service_config)
        elif 'icmptype' in service_config or 'icmpcode' in service_config:
            return self._convert_icmp_service(service_name, service_config)
        elif 'protocol' in service_config:
            return self._convert_ip_service(service_name, service_config)
        else:
            log(_("service_processing.unknown_service_type", service=service_name), "warning")
            return None

    def _convert_tcp_service(self, service_name: str, service_config: Dict) -> Dict[str, Any]:
        """转换TCP服务，支持源端口和目的端口分离"""
        port_range = service_config.get('tcp-portrange', '')

        # 使用新的FortiGate端口格式解析器
        parsed_ports = self.parse_fortigate_port_format(port_range)

        result = {
            "name": service_name,
            "type": "custom",
            "protocol": "tcp",
            "port_range": parsed_ports.get("dest_port", ""),  # 保持向后兼容
            "dest_port_range": parsed_ports.get("dest_port", ""),
            "source_port_range": parsed_ports.get("source_port", ""),
            "has_source_port": bool(parsed_ports.get("source_port")),
            "description": service_config.get("comment", ""),
            "vendor": "fortigate"
        }

        # 检查并警告不支持的iprange字段
        if 'iprange' in service_config and service_config['iprange']:
            iprange_value = service_config['iprange']
            log(_("service_processing.iprange_not_supported",
                  service=service_name, iprange=iprange_value), "warning")
            log(f"警告: 服务 {service_name} 包含不支持的iprange字段: {iprange_value}", "warning")

        log(_("service_processing.tcp_service_converted",
              name=service_name,
              dest_port=result["dest_port_range"],
              source_port=result["source_port_range"] or "any"), "debug")

        return result

    def _convert_udp_service(self, service_name: str, service_config: Dict) -> Dict[str, Any]:
        """转换UDP服务，支持源端口和目的端口分离"""
        port_range = service_config.get('udp-portrange', '')

        # 使用新的FortiGate端口格式解析器
        parsed_ports = self.parse_fortigate_port_format(port_range)

        result = {
            "name": service_name,
            "type": "custom",
            "protocol": "udp",
            "port_range": parsed_ports.get("dest_port", ""),  # 保持向后兼容
            "dest_port_range": parsed_ports.get("dest_port", ""),
            "source_port_range": parsed_ports.get("source_port", ""),
            "has_source_port": bool(parsed_ports.get("source_port")),
            "description": service_config.get("comment", ""),
            "vendor": "fortigate"
        }

        # 检查并警告不支持的iprange字段
        if 'iprange' in service_config and service_config['iprange']:
            iprange_value = service_config['iprange']
            log(_("service_processing.iprange_not_supported",
                  service=service_name, iprange=iprange_value), "warning")
            log(f"警告: 服务 {service_name} 包含不支持的iprange字段: {iprange_value}", "warning")

        log(_("service_processing.udp_service_converted",
              name=service_name,
              dest_port=result["dest_port_range"],
              source_port=result["source_port_range"] or "any"), "debug")

        return result

    def _convert_tcp_udp_service(self, service_name: str, service_config: Dict) -> Dict[str, Any]:
        """转换TCP+UDP多协议服务，支持源端口和目的端口分离"""
        tcp_port_range = service_config.get('tcp-portrange', '')
        udp_port_range = service_config.get('udp-portrange', '')

        # 使用新的FortiGate端口格式解析器
        parsed_tcp_ports = self.parse_fortigate_port_format(tcp_port_range)
        parsed_udp_ports = self.parse_fortigate_port_format(udp_port_range)

        result = {
            "name": service_name,
            "type": "custom",
            "protocol": "tcp_udp",
            # 保持向后兼容的字段
            "tcp_port_range": parsed_tcp_ports.get("dest_port", ""),
            "udp_port_range": parsed_udp_ports.get("dest_port", ""),
            # 新增的详细端口信息
            "tcp_dest_port_range": parsed_tcp_ports.get("dest_port", ""),
            "tcp_source_port_range": parsed_tcp_ports.get("source_port", ""),
            "udp_dest_port_range": parsed_udp_ports.get("dest_port", ""),
            "udp_source_port_range": parsed_udp_ports.get("source_port", ""),
            "tcp_has_source_port": bool(parsed_tcp_ports.get("source_port")),
            "udp_has_source_port": bool(parsed_udp_ports.get("source_port")),
            "description": service_config.get("comment", ""),
            "vendor": "fortigate"
        }

        # 检查并警告不支持的iprange字段
        if 'iprange' in service_config and service_config['iprange']:
            iprange_value = service_config['iprange']
            log(_("service_processing.iprange_not_supported",
                  service=service_name, iprange=iprange_value), "warning")
            log(f"警告: 服务 {service_name} 包含不支持的iprange字段: {iprange_value}", "warning")

        log(_("service_processing.tcp_udp_service_converted",
              name=service_name,
              tcp_dest=result["tcp_dest_port_range"],
              tcp_source=result["tcp_source_port_range"] or "any",
              udp_dest=result["udp_dest_port_range"],
              udp_source=result["udp_source_port_range"] or "any"), "debug")

        return result

    def _convert_sctp_service(self, service_name: str, service_config: Dict) -> Dict[str, Any]:
        """转换SCTP服务（映射为TCP）"""
        port_range = service_config.get('sctp-portrange', '')
        normalized_port = self.normalize_port_range(port_range)

        return {
            "name": service_name,
            "type": "custom",
            "protocol": "tcp",  # SCTP映射为TCP
            "port_range": normalized_port,
            "description": service_config.get("comment", "SCTP service mapped to TCP"),
            "vendor": "fortigate"
        }

    def _convert_icmp_service(self, service_name: str, service_config: Dict) -> Dict[str, Any]:
        """转换ICMP服务"""
        icmp_type = service_config.get('icmptype', '8')  # 默认ping
        icmp_code = service_config.get('icmpcode', '0')  # 默认code

        return {
            "name": service_name,
            "type": "custom",
            "protocol": "icmp",
            "icmp_type": str(icmp_type),
            "icmp_code": str(icmp_code),
            "description": service_config.get("comment", ""),
            "vendor": "fortigate"
        }

    def _convert_ip_service(self, service_name: str, service_config: Dict) -> Dict[str, Any]:
        """转换IP协议服务"""
        # 正确获取协议号：优先使用protocol-number字段，如果没有则使用默认值
        protocol_number = service_config.get('protocol-number', '1')

        # 如果protocol-number不存在但有protocol字段，尝试从协议名称映射
        if protocol_number == '1' and 'protocol' in service_config:
            protocol_name = service_config['protocol'].lower()
            protocol_mapping = {
                'gre': '47',
                'esp': '50',
                'ah': '51',
                'ospf': '89',
                'icmp': '1',
                'tcp': '6',
                'udp': '17'
            }
            protocol_number = protocol_mapping.get(protocol_name, '1')

        return {
            "name": service_name,
            "type": "custom",
            "protocol": "ip",
            "protocol_number": str(protocol_number),
            "description": service_config.get("comment", ""),
            "vendor": "fortigate"
        }

    def _add_failed_result(self, result: Dict, name: str, config: Dict, reason: str):
        """添加失败的结果"""
        result["failed"][name] = config
        result["failed_count"] += 1
        result["details"].append({
            "name": name,
            "status": "failed",
            "reason": reason,
            "original": config
        })
        log(_("service_processing.object_processing_failed", name=name, reason=reason), "error")


class ServiceProcessingStage(PipelineStage):
    """
    服务对象处理阶段 - 完全重新实现

    负责处理FortiGate服务对象配置，转换为NTOS格式并生成XML片段
    支持预定义服务映射和自定义服务转换
    """

    def __init__(self, name_mapping_manager=None):
        super().__init__("service_processing", _("service_processing_stage.description"))
        self.processor = ServiceObjectProcessor()
        self.user_logger = None  # 将在process方法中初始化

        # 名称映射管理器
        self.name_mapping_manager = name_mapping_manager
        if self.name_mapping_manager is None:
            from engine.utils.name_mapping_manager import NameMappingManager
            self.name_mapping_manager = NameMappingManager()

        # 加载服务映射配置
        self.service_mappings = self._load_service_mappings()

        # 预定义服务映射
        self.predefined_services = {
            "ALL": "any",
            "HTTP": "http",
            "HTTPS": "https",
            "FTP": "ftp",
            "SSH": "ssh",
            "TELNET": "telnet",
            "SMTP": "smtp",
            "POP3": "pop3",
            "IMAP": "imap",
            "DNS": "dns",
            "DHCP": "dhcp",
            "SNMP": "snmp",
            "NTP": "ntp"
        }

    def _load_service_mappings(self) -> Dict:
        """
        加载服务映射配置（借鉴ServiceObjectProcessor的实现）

        Returns:
            Dict: 服务映射配置
        """
        try:
            # 尝试加载服务映射文件
            mapping_file = os.path.join("engine", "data", "input", "service_mappings.json")
            if os.path.exists(mapping_file):
                with open(mapping_file, 'r', encoding='utf-8') as f:
                    mappings = json.load(f)
                    return mappings
        except Exception as e:
            log(_("service_processing.load_mapping_failed", error=str(e)), "warning")

        # 如果加载失败，返回空字典
        return {}

    def process(self, context) -> bool:
        """
        执行服务对象处理
        
        Args:
            context: 管道上下文，包含解析后的配置数据
            
        Returns: Dict[str, Any]: 更新后的上下文
        """
        log(_("service_processing_stage.starting"), "info")

        # 初始化用户日志记录器
        language = context.get_data('language', 'zh-CN')
        self.user_logger = get_pipeline_user_logger(language)

        try:
            # 获取服务对象数据 - 修复数据访问方式
            config_data = context.get_data('config_data', {})
            service_objects_data = config_data.get('service_objects', [])
            service_groups_data = config_data.get('service_groups', [])

            # 转换数据格式：从列表转换为字典（兼容解析器输出格式）
            service_objects = {}
            if isinstance(service_objects_data, list):
                for service in service_objects_data:
                    if isinstance(service, dict) and 'name' in service:
                        service_objects[service['name']] = service
            elif isinstance(service_objects_data, dict):
                service_objects = service_objects_data

            service_groups = {}
            if isinstance(service_groups_data, list):
                for group in service_groups_data:
                    if isinstance(group, dict) and 'name' in group:
                        service_groups[group['name']] = group
            elif isinstance(service_groups_data, dict):
                service_groups = service_groups_data

            if not service_objects and not service_groups:
                log(_("service_processing_stage.no_service_objects"), "info")
                self.user_logger.log_stage_start("service_processing", 0)
                self.user_logger.log_stage_complete("service_processing", {"total": 0, "processed": 0})
                context.set_data('service_processing_result', self._empty_result())
                return True

            # 禁用iprange转换功能以与原版保持完全一致
            # 原版没有iprange转换功能，为了达到≥95%一致性要求，必须禁用此功能
            log("为保持与原版一致性，跳过服务对象iprange转换", "info")

            # 注释掉iprange转换逻辑
            # # 处理服务对象的iprange转换（在主要处理之前）
            # log("开始处理服务对象iprange转换", "info")
            # # 使用ServiceProcessor来处理iprange转换
            # from engine.processors.service_processor import ServiceProcessor
            # service_processor = ServiceProcessor()
            # iprange_stats = service_processor.process_service_iprange_conversion(service_objects, context)
            #
            # if iprange_stats['services_with_iprange'] > 0:
            #     log(f"iprange转换完成: 处理了{iprange_stats['services_with_iprange']}个包含iprange的服务对象, "
            #         f"成功生成{iprange_stats['successful_conversions']}个地址对象", "info")

            # 记录阶段开始
            total_count = len(service_objects) + len(service_groups)
            self.user_logger.log_stage_start("service_processing", total_count)

            # 使用新的处理器处理服务对象
            service_result = self.processor.process_service_objects(service_objects)

            # 生成XML片段
            xml_fragment = self._generate_service_xml_fragment(service_result)

            # 构建处理结果（包含服务对象和XML片段）
            processing_result = {
                "service_objects": service_result,
                "xml_fragment": xml_fragment,
                "statistics": {
                    "total_services": len(service_objects),
                    "converted_services": service_result['converted_count'],
                    "mapped_services": service_result['mapped_count'],
                    "failed_services": service_result['failed_count']
                },
                "iprange_conversion": {
                    "total_services": 0,
                    "services_with_iprange": 0,
                    "successful_conversions": 0,
                    "failed_conversions": 0,
                    "generated_addresses": []
                }  # 禁用iprange转换，返回空统计
            }
            
            context.set_data('service_processing_result', processing_result)

            log(_("service_processing_stage.completed",
                  converted=processing_result['statistics']['converted_services'],
                  total=processing_result['statistics']['total_services']), "info")

            # 记录阶段完成
            result_summary = {
                "total": processing_result['statistics']['total_services'],
                "converted": processing_result['statistics']['converted_services'],
                "mapped": processing_result['statistics']['mapped_services'],
                "failed": processing_result['statistics']['failed_services']
            }
            self.user_logger.log_stage_complete("service_processing", result_summary)

            # 记录用户日志
            from engine.utils.user_log_formatter import record_stage_user_log
            record_stage_user_log("service_processing", processing_result)

            return True

        except Exception as e:
            error_msg = _("service_processing_stage.processing_failed", error=str(e))
            log(_("service_processing_stage.processing_failed", error=str(e)), "error")
            error_msg = _("service_processing_stage.detailed_error",  error=error_msg)
            log(error_msg, "error")
            log(_("service_processing_stage.config_data_type", type=type(config_data)), "debug")
            log(_("service_processing_stage.service_data_type", type=type(service_objects_data)), "debug")
            context.set_data('service_processing_result', self._empty_result())
            context.add_error(error_msg, self.name)
            return False

    def _generate_service_xml_fragment(self, service_result: Dict[str, Any]) -> str:
        """
        生成符合YANG模型的服务对象XML片段

        Args:
            service_result: 服务对象处理结果

        Returns:
            str: XML片段字符串
        """
        try:
            # 创建service-obj根元素
            service_obj = etree.Element("service-obj", xmlns="urn:ruijie:ntos")

            # 处理转换成功的服务对象
            converted_services = service_result.get("converted", {})
            mapped_services = service_result.get("mapped", {})

            if not converted_services and not mapped_services:
                log(_("service_processing.no_converted_services"), "debug")
                return ""

            # 用于跟踪已添加的服务名称，避免重复（符合YANG约束）
            added_service_names = set()

            # 处理自定义服务对象
            for service_name, service_config in converted_services.items():
                if service_name not in added_service_names:
                    service_set = self._create_service_set_xml(service_name, service_config)
                    if service_set is not None:
                        service_obj.append(service_set)
                        added_service_names.add(service_name)
                else:
                    log(_("service_processing.skip_duplicate_service", service=service_name), "warning")

            # 跳过映射的服务对象（预定义服务）- 预定义服务不应该生成XML
            # 预定义服务是NTOS内置服务，只需要保存映射关系供策略引用
            if mapped_services:
                skipped_predefined = list(mapped_services.keys())
                log(_("service_processing.skip_predefined_services",
                      count=len(skipped_predefined), services=", ".join(skipped_predefined)), "info")
                # 记录跳过的预定义服务信息
                for service_name in skipped_predefined:
                    log(f"跳过预定义服务: {service_name}", "debug")

            # 转换为字符串
            xml_str = etree.tostring(service_obj, encoding='unicode', pretty_print=True)

            # 只统计实际生成XML的自定义服务数量
            total_services = len(converted_services)
            log(_("service_processor.xml_fragment_success", count=total_services), "debug")
            return xml_str

        except Exception as e:
            log(_("service_processor.xml_fragment_failed", error=str(e)), "error")
            return ""

    def _create_service_set_xml(self, service_name: str, service_config: Dict) -> etree.Element:
        """
        创建自定义服务的service-set XML元素，集成名称清理

        Args:
            service_name: 服务名称
            service_config: 服务配置

        Returns:
            etree.Element: service-set元素
        """
        try:
            # 使用名称映射管理器清理和注册服务名称
            clean_service_name = self.name_mapping_manager.register_name_mapping(
                'services',
                service_name,
                context="服务对象"
            )

            # 创建service-set元素
            service_set = etree.Element("service-set")

            # 添加name元素（必需的键）
            name_elem = etree.SubElement(service_set, "name")
            name_elem.text = clean_service_name

            # 添加description元素（如果有）- 应用YANG约束清理
            description = service_config.get("description", "")
            if description:
                # 清理描述字段中的特殊字符
                cleaned_description = self._sanitize_description(description)
                if cleaned_description:  # 只有清理后仍有内容才添加
                    desc_elem = etree.SubElement(service_set, "description")
                    desc_elem.text = cleaned_description

            # 根据协议类型添加协议容器
            protocol = service_config.get("protocol", "").lower()

            if protocol == "tcp":
                self._add_tcp_protocol_xml(service_set, service_config)
            elif protocol == "udp":
                self._add_udp_protocol_xml(service_set, service_config)
            elif protocol == "tcp_udp":
                # 处理多协议服务，同时添加TCP和UDP容器
                self._add_tcp_udp_protocol_xml(service_set, service_config)
            elif protocol == "icmp":
                self._add_icmp_protocol_xml(service_set, service_config)
            elif protocol == "ip":
                self._add_ip_protocol_xml(service_set, service_config)
            else:
                log(_("service_processor.unsupported_protocol", protocol=protocol), "warning")
                return None

            log(_("service_processor.xml_generated", name=service_name, protocol=protocol), "debug")
            return service_set

        except Exception as e:
            log(_("service_processor.xml_creation_failed", name=service_name, error=str(e)), "error")
            return None

    def _create_mapped_service_set_xml(self, service_name: str, service_config: Dict) -> etree.Element:
        """
        创建映射服务的service-set XML元素（预定义服务）

        Args:
            service_name: 原始服务名称
            service_config: 服务配置

        Returns:
            etree.Element: service-set元素
        """
        try:
            # 获取映射后的名称
            mapped_name = service_config.get("name", service_name)

            # 使用名称映射管理器清理和注册映射后的服务名称
            clean_mapped_name = self.name_mapping_manager.register_name_mapping(
                'services',
                service_name,  # 使用原始名称作为键
                preferred_name=mapped_name,  # 使用映射后的名称作为首选名称
                context="映射服务对象"
            )

            # 创建service-set元素
            service_set = etree.Element("service-set")

            # 添加name元素（必需的键）
            name_elem = etree.SubElement(service_set, "name")
            name_elem.text = clean_mapped_name

            # 添加描述 - 应用YANG约束清理
            description = f"Mapped from FortiGate service: {service_config.get('original_name', service_name)}"
            cleaned_description = self._sanitize_description(description)
            if cleaned_description:
                desc_elem = etree.SubElement(service_set, "description")
                desc_elem.text = cleaned_description

            log(_("service_processor.mapped_xml_generated", original=service_name, mapped=clean_mapped_name), "debug")
            return service_set

        except Exception as e:
            log(_("service_processor.mapped_xml_creation_failed", name=service_name, error=str(e)), "error")
            return None

    def _determine_source_port(self, service_name: str, explicit_source_port: str, protocol: str) -> str:
        """
        智能确定服务的源端口

        根据NTOS YANG模型要求，源端口是必填的。此方法根据服务特性智能确定源端口：
        1. 如果明确指定了源端口，使用指定值
        2. 对于特定服务（如RLOGIN、RSH），使用其特定的源端口范围
        3. 其他情况使用默认的全端口范围 0-65535

        Args:
            service_name: 服务名称
            explicit_source_port: 明确指定的源端口（可能为空）
            protocol: 协议类型（tcp/udp）

        Returns:
            str: 确定的源端口范围
        """
        # 如果明确指定了源端口，直接使用
        if explicit_source_port:
            log(_("service_processor.using_explicit_source_port",
                  service=service_name, port=explicit_source_port), "debug")
            return explicit_source_port

        # 特定服务的源端口映射（基于FortiGate预定义服务的特性）
        service_specific_source_ports = {
            "RLOGIN": "512-1023",
            "RSH": "512-1023",
            "rlogin": "512-1023",
            "rsh": "512-1023"
        }

        # 检查是否是特定服务
        if service_name in service_specific_source_ports:
            specific_port = service_specific_source_ports[service_name]
            log(_("service_processor.using_service_specific_source_port",
                  service=service_name, port=specific_port), "info")
            return specific_port

        # 默认使用全端口范围，符合NTOS YANG模型要求
        default_port = "0-65535"
        log(_("service_processor.using_default_source_port",
              service=service_name, port=default_port), "debug")
        return default_port

    def _add_tcp_protocol_xml(self, service_set: etree.Element, service_config: Dict):
        """添加TCP协议XML，支持源端口和目的端口"""
        tcp_elem = etree.SubElement(service_set, "tcp")

        # 优先使用新的详细端口字段，然后回退到兼容字段
        dest_port = (service_config.get("dest_port_range") or
                    service_config.get("tcp_dest_port_range") or
                    service_config.get("port_range") or
                    service_config.get("port", ""))

        explicit_source_port = (service_config.get("source_port_range") or
                               service_config.get("tcp_source_port_range", ""))

        # 添加目的端口
        if dest_port:
            normalized_dest_port = self._normalize_port_range(str(dest_port))
            if normalized_dest_port:
                dest_port_elem = etree.SubElement(tcp_elem, "dest-port")
                dest_port_elem.text = normalized_dest_port
            else:
                log(_("service_processor.invalid_tcp_dest_port_range", range=dest_port), "warning")

        # 智能源端口处理：根据NTOS YANG模型要求，源端口是必填的
        service_name = service_config.get("name", "unknown")
        final_source_port = self._determine_source_port(service_name, explicit_source_port, "tcp")

        if final_source_port:
            normalized_source_port = self._normalize_port_range(str(final_source_port))
            if normalized_source_port:
                source_port_elem = etree.SubElement(tcp_elem, "source-port")
                source_port_elem.text = normalized_source_port
                log(_("service_processor.tcp_source_port_added", port=normalized_source_port), "debug")
            else:
                log(_("service_processor.invalid_tcp_source_port_range", range=final_source_port), "warning")

    def _add_udp_protocol_xml(self, service_set: etree.Element, service_config: Dict):
        """添加UDP协议XML，支持源端口和目的端口"""
        udp_elem = etree.SubElement(service_set, "udp")

        # 优先使用新的详细端口字段，然后回退到兼容字段
        dest_port = (service_config.get("dest_port_range") or
                    service_config.get("udp_dest_port_range") or
                    service_config.get("port_range") or
                    service_config.get("port", ""))

        explicit_source_port = (service_config.get("source_port_range") or
                               service_config.get("udp_source_port_range", ""))

        # 添加目的端口
        if dest_port:
            normalized_dest_port = self._normalize_port_range(str(dest_port))
            if normalized_dest_port:
                dest_port_elem = etree.SubElement(udp_elem, "dest-port")
                dest_port_elem.text = normalized_dest_port
            else:
                log(_("service_processor.invalid_udp_dest_port_range", range=dest_port), "warning")

        # 智能源端口处理：根据NTOS YANG模型要求，源端口是必填的
        service_name = service_config.get("name", "unknown")
        final_source_port = self._determine_source_port(service_name, explicit_source_port, "udp")

        if final_source_port:
            normalized_source_port = self._normalize_port_range(str(final_source_port))
            if normalized_source_port:
                source_port_elem = etree.SubElement(udp_elem, "source-port")
                source_port_elem.text = normalized_source_port
                log(_("service_processor.udp_source_port_added", port=normalized_source_port), "debug")
            else:
                log(_("service_processor.invalid_udp_source_port_range", range=final_source_port), "warning")

    def _add_tcp_udp_protocol_xml(self, service_set: etree.Element, service_config: Dict):
        """添加TCP+UDP多协议XML，支持源端口和目的端口"""
        # 获取TCP端口信息
        tcp_dest_port = (service_config.get("tcp_dest_port_range") or
                        service_config.get("tcp_port_range"))
        tcp_explicit_source_port = service_config.get("tcp_source_port_range", "")

        # 获取UDP端口信息
        udp_dest_port = (service_config.get("udp_dest_port_range") or
                        service_config.get("udp_port_range"))
        udp_explicit_source_port = service_config.get("udp_source_port_range", "")

        service_name = service_config.get("name", "unknown")

        # 添加TCP容器（如果有TCP端口）
        if tcp_dest_port:
            tcp_elem = etree.SubElement(service_set, "tcp")

            # 添加TCP目的端口
            normalized_tcp_dest = self._normalize_port_range(str(tcp_dest_port))
            if normalized_tcp_dest:
                dest_port_elem = etree.SubElement(tcp_elem, "dest-port")
                dest_port_elem.text = normalized_tcp_dest

            # 智能TCP源端口处理
            tcp_final_source_port = self._determine_source_port(service_name, tcp_explicit_source_port, "tcp")
            if tcp_final_source_port:
                normalized_tcp_source = self._normalize_port_range(str(tcp_final_source_port))
                if normalized_tcp_source:
                    source_port_elem = etree.SubElement(tcp_elem, "source-port")
                    source_port_elem.text = normalized_tcp_source

        # 添加UDP容器（如果有UDP端口）
        if udp_dest_port:
            udp_elem = etree.SubElement(service_set, "udp")

            # 添加UDP目的端口
            normalized_udp_dest = self._normalize_port_range(str(udp_dest_port))
            if normalized_udp_dest:
                dest_port_elem = etree.SubElement(udp_elem, "dest-port")
                dest_port_elem.text = normalized_udp_dest

            # 智能UDP源端口处理
            udp_final_source_port = self._determine_source_port(service_name, udp_explicit_source_port, "udp")
            if udp_final_source_port:
                normalized_udp_source = self._normalize_port_range(str(udp_final_source_port))
                if normalized_udp_source:
                    source_port_elem = etree.SubElement(udp_elem, "source-port")
                    source_port_elem.text = normalized_udp_source

        # 向后兼容：处理旧的port字段格式
        port_info = service_config.get("port")
        if isinstance(port_info, dict) and not (tcp_port_range or udp_port_range):
            tcp_port = port_info.get("tcp")
            udp_port = port_info.get("udp")

            if tcp_port:
                tcp_elem = etree.SubElement(service_set, "tcp")
                normalized_tcp_port = self._normalize_port_range(str(tcp_port))
                if normalized_tcp_port:
                    dest_port_elem = etree.SubElement(tcp_elem, "dest-port")
                    dest_port_elem.text = normalized_tcp_port

            if udp_port:
                udp_elem = etree.SubElement(service_set, "udp")
                normalized_udp_port = self._normalize_port_range(str(udp_port))
                if normalized_udp_port:
                    dest_port_elem = etree.SubElement(udp_elem, "dest-port")
                    dest_port_elem.text = normalized_udp_port

    def _add_icmp_protocol_xml(self, service_set: etree.Element, service_config: Dict):
        """添加ICMP协议XML（遵循YANG模型的type/code键约束）"""
        port_info = service_config.get("port")

        # 默认ICMP类型和代码
        icmp_type = "8"  # Echo Request
        icmp_code = "0"

        if isinstance(port_info, dict):
            # 处理包含类型和代码的ICMP信息
            icmp_type = str(port_info.get("type", icmp_type))
            icmp_code = str(port_info.get("code", icmp_code))
        elif isinstance(port_info, str) and port_info == "1":
            # 如果port是"1"（ICMP协议ID），使用默认值
            pass
        else:
            # 向后兼容：使用旧的字段名
            icmp_type = str(service_config.get("icmp_type", icmp_type))
            icmp_code = str(service_config.get("icmp_code", icmp_code))

        # 创建icmp列表元素，key为"type code"
        icmp_elem = etree.SubElement(service_set, "icmp")

        # 添加type元素
        type_elem = etree.SubElement(icmp_elem, "type")
        type_elem.text = icmp_type

        # 添加code元素
        code_elem = etree.SubElement(icmp_elem, "code")
        code_elem.text = icmp_code

    def _add_ip_protocol_xml(self, service_set: etree.Element, service_config: Dict):
        """添加IP协议XML"""
        # 优先使用port字段，然后是protocol_number字段（向后兼容）
        protocol_number = service_config.get("port") or service_config.get("protocol_number", "1")

        # 验证和标准化协议ID格式（符合YANG约束：0-255范围）
        normalized_protocol_id = self._normalize_protocol_id(str(protocol_number))
        if normalized_protocol_id is not None:
            # 添加protocol-id元素
            protocol_id_elem = etree.SubElement(service_set, "protocol-id")
            protocol_id_elem.text = normalized_protocol_id
        else:
            log(_("service_processor.invalid_protocol_id", id=protocol_number), "warning")

    def _normalize_protocol_id(self, protocol_id: str) -> str:
        """
        标准化协议ID格式，确保符合YANG约束（0-255范围）

        Args:
            protocol_id: 原始协议ID字符串

        Returns:
            str: 标准化后的协议ID，如果无效则返回None
        """
        if not protocol_id:
            return None

        # 移除首尾空格
        protocol_id = str(protocol_id).strip()

        # 处理数字格式
        if protocol_id.isdigit():
            protocol_num = int(protocol_id)
            if 0 <= protocol_num <= 255:
                return str(protocol_num)
            else:
                log(_("service_processor.protocol_id_out_of_range", id=protocol_id), "warning")
                return None

        # 处理逗号分隔的多个协议ID
        if "," in protocol_id:
            protocol_parts = protocol_id.split(",")
            valid_protocols = []
            for part in protocol_parts:
                part = part.strip()
                if part.isdigit():
                    protocol_num = int(part)
                    if 0 <= protocol_num <= 255:
                        valid_protocols.append(str(protocol_num))
                    else:
                        log(_("service_processor.protocol_id_out_of_range", id=part), "warning")
                else:
                    log(_("service_processor.invalid_protocol_id_format", part=part), "warning")

            if valid_protocols:
                return ",".join(valid_protocols)
            else:
                return None

        # 处理范围格式（如1-10）
        if "-" in protocol_id:
            try:
                start, end = map(int, protocol_id.split("-"))
                if 0 <= start <= end <= 255:
                    # 转换为逗号分隔格式
                    return ",".join(str(i) for i in range(start, end + 1))
                else:
                    log(_("service_processor.protocol_id_range_out_of_limit", id=protocol_id), "warning")
                    return None
            except ValueError:
                log(_("service_processor.invalid_protocol_id_range_format", id=protocol_id), "warning")
                return None

        # 处理协议名称到数字的映射
        protocol_mapping = {
            "icmp": "1",
            "tcp": "6",
            "udp": "17",
            "gre": "47",
            "esp": "50",
            "ah": "51",
            "ospf": "89",
            "icmp6": "58"
        }

        protocol_lower = protocol_id.lower()
        if protocol_lower in protocol_mapping:
            return protocol_mapping[protocol_lower]

        log(_("service_processor.unrecognized_protocol_id_format", id=protocol_id), "warning")
        return None

    def _sanitize_description(self, description: str) -> str:
        """
        清理描述字段中的特殊字符，确保符合YANG约束

        Args:
            description: 原始描述字符串

        Returns:
            str: 清理后的描述字符串
        """
        if not description:
            return description

        # YANG模型禁止的特殊字符：`~!#$%^&*+|{};:"\/<>?
        forbidden_chars = "`~!#$%^&*+|{};:\"\\/<>?"

        # 替换禁止的字符为安全字符
        cleaned_description = description
        for char in forbidden_chars:
            if char in cleaned_description:
                # 将特殊字符替换为下划线或移除
                if char in "\"\\/<>":
                    cleaned_description = cleaned_description.replace(char, "_")
                else:
                    cleaned_description = cleaned_description.replace(char, "")

        # 移除多余的空格和下划线
        cleaned_description = " ".join(cleaned_description.split())
        cleaned_description = cleaned_description.replace("__", "_").strip("_")

        return cleaned_description

    def _normalize_port_range(self, port_range: str) -> str:
        """
        标准化端口范围格式，确保符合YANG约束（0-65535范围）

        Args:
            port_range: 原始端口范围字符串

        Returns:
            str: 标准化后的端口范围，如果无效则返回None
        """
        if not port_range:
            return None

        # 移除首尾空格
        port_range = str(port_range).strip()

        # 处理单个端口
        if port_range.isdigit():
            port_num = int(port_range)
            if 0 <= port_num <= 65535:
                return str(port_num)
            else:
                log(_("service_processor.port_out_of_range", range=port_range), "warning")
                return None

        # 处理逗号分隔的多个端口
        if "," in port_range:
            port_parts = port_range.split(",")
            valid_ports = []
            for part in port_parts:
                part = part.strip()
                if part.isdigit():
                    port_num = int(part)
                    if 0 <= port_num <= 65535:
                        valid_ports.append(str(port_num))
                    else:
                        log(_("service_processor.port_out_of_range", range=part), "warning")
                elif "-" in part:
                    # 处理范围格式
                    try:
                        start, end = map(int, part.split("-"))
                        if 0 <= start <= end <= 65535:
                            valid_ports.append(f"{start}-{end}")
                        else:
                            log(_("service_processor.port_range_out_of_limit", part=part), "warning")
                    except ValueError:
                        log(_("service_processor.invalid_port_range_format", part=part), "warning")
                else:
                    log(_("service_processor.invalid_port_format", part=part), "warning")

            if valid_ports:
                return ",".join(valid_ports)
            else:
                return None

        # 处理范围格式（如1024-2048）
        if "-" in port_range:
            try:
                start, end = map(int, port_range.split("-"))
                if 0 <= start <= end <= 65535:
                    return f"{start}-{end}"
                else:
                    log(_("service_processor.port_range_out_of_limit", part=port_range), "warning")
                    return None
            except ValueError:
                log(_("service_processor.invalid_port_range_format", part=port_range), "warning")
                return None

        # 处理特殊端口名称（如果需要）
        port_mapping = {
            "http": "80",
            "https": "443",
            "ftp": "21",
            "ssh": "22",
            "telnet": "23",
            "smtp": "25",
            "dns": "53",
            "dhcp": "67,68",
            "pop3": "110",
            "imap": "143",
            "snmp": "161",
            "ntp": "123"
        }

        port_lower = port_range.lower()
        if port_lower in port_mapping:
            return port_mapping[port_lower]

        log(_("service_processor.unrecognized_port_range_format", range=port_range), "warning")
        return None

    # 旧的处理方法已被新的ServiceObjectProcessor替代
    
    def _empty_result(self) -> Dict[str, Any]:
        """返回空结果"""
        return {
            "service_objects": {
                "converted": {},
                "mapped": {},
                "failed": {},
                "converted_count": 0,
                "mapped_count": 0,
                "failed_count": 0,
                "details": []
            },
            "xml_fragment": "",
            "statistics": {
                "total_services": 0,
                "converted_services": 0,
                "mapped_services": 0,
                "failed_services": 0
            }
        }
