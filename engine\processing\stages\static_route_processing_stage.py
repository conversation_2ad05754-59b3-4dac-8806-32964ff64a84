#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
静态路由处理阶段
负责处理FortiGate的静态路由配置并转换为NTOS格式
"""

import re
from typing import Dict, Any, List
from engine.processing.pipeline.pipeline_stage import PipelineStage
from engine.processing.pipeline.data_flow import DataContext
from engine.utils.logger import log
from engine.utils.i18n import _
from lxml import etree


class StaticRouteProcessor:
    """
    静态路由处理器 - 完全重新实现，借鉴旧架构核心逻辑
    """

    def __init__(self):
        # 默认路由优先级（借鉴旧架构）
        self.default_priority = 1
        self.default_distance = 1

        # 路由类型映射（借鉴旧架构）
        self.route_type_mapping = {
            "static": "gateway",
            "blackhole": "blackhole",
            "reject": "reject"
        }

    def validate_ip_address(self, ip_address: str) -> bool:
        """
        验证IP地址格式（借鉴旧架构逻辑）

        Args:
            ip_address: IP地址字符串

        Returns:
            bool: 是否为有效IP地址
        """
        if not ip_address:
            return False

        try:
            # IPv4地址验证
            ipv4_pattern = r'^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$'
            match = re.match(ipv4_pattern, ip_address)

            if match:
                # 检查每个数字是否在0-255范围内
                for group in match.groups():
                    if int(group) > 255:
                        return False
                return True

            return False
        except (ValueError, AttributeError):
            return False

    def validate_network_address(self, network: str) -> bool:
        """
        验证网络地址格式（借鉴旧架构逻辑）

        Args:
            network: 网络地址字符串（支持CIDR格式）

        Returns:
            bool: 是否为有效网络地址
        """
        if not network:
            return False

        try:
            if "/" in network:
                # CIDR格式验证
                ip, prefix = network.split("/")
                if not self.validate_ip_address(ip):
                    return False

                prefix_len = int(prefix)
                return 0 <= prefix_len <= 32
            else:
                # 单个IP地址
                return self.validate_ip_address(network)
        except (ValueError, AttributeError):
            return False

    def parse_destination(self, dst: str) -> str:
        """
        解析目标网络地址（借鉴旧架构逻辑）

        Args:
            dst: FortiGate目标地址格式

        Returns:
            str: 标准化的CIDR格式网络地址
        """
        if not dst:
            return None

        try:
            if "/" in dst:
                # 已经是CIDR格式
                network, prefix_len = dst.split("/")
                if self.validate_ip_address(network) and 0 <= int(prefix_len) <= 32:
                    return dst
            elif " " in dst:
                # 网络地址 子网掩码格式，转换为CIDR
                parts = dst.split()
                if len(parts) >= 2:
                    network = parts[0]
                    netmask = parts[1]
                    if self.validate_ip_address(network) and self.validate_ip_address(netmask):
                        prefix_len = self._netmask_to_cidr(netmask)
                        if prefix_len is not None:
                            return f"{network}/{prefix_len}"
            else:
                # 单个IP地址，默认为主机路由
                if self.validate_ip_address(dst):
                    return f"{dst}/32"

        except Exception as e:
            log(_("static_route_processor.destination_parse_error", dst=dst, error=str(e)), "warning")

        return None

    def _netmask_to_cidr(self, netmask: str) -> int:
        """
        将子网掩码转换为CIDR前缀长度

        Args:
            netmask: 子网掩码

        Returns:
            int: CIDR前缀长度
        """
        try:
            parts = netmask.split('.')
            if len(parts) != 4:
                return None

            binary = ''
            for part in parts:
                binary += format(int(part), '08b')

            # 计算连续的1的个数
            prefix_len = 0
            for bit in binary:
                if bit == '1':
                    prefix_len += 1
                else:
                    break

            # 验证是否为有效的子网掩码（连续的1后面都是0）
            if binary == '1' * prefix_len + '0' * (32 - prefix_len):
                return prefix_len

        except (ValueError, AttributeError):
            pass

        return None

    def convert_static_route(self, route_id: str, config: Dict[str, Any], interface_mapping: Dict[str, str]) -> Dict[str, Any]:
        """
        转换单个静态路由（借鉴旧架构逻辑）

        Args:
            route_id: 路由ID
            config: FortiGate路由配置
            interface_mapping: 接口映射表

        Returns: Dict[str, Any]: NTOS路由配置
        """
        # 验证必需字段 - 支持dst和destination两种字段名
        dst = config.get("destination") or config.get("dst")
        gateway = config.get("gateway")
        device = config.get("device")

        if not dst:
            log(_("static_route_processor.route_missing_destination", route_id=route_id), "warning")
            return None

        # 解析目标网络
        destination = self.parse_destination(dst)
        if not destination:
            log(_("static_route_processor.route_invalid_destination", route_id=route_id, dst=dst), "warning")
            return None

        # 构建NTOS路由配置
        ntos_route = {
            "id": route_id,
            "destination": destination,
            "enabled": True,
            "priority": config.get("priority", self.default_priority),
            "distance": config.get("distance", self.default_distance),
            # 映射FortiGate的comment字段到NTOS的descr字段（符合YANG模型）
            "descr": config.get("comment", _("static_route_processor.default_description", route_id=route_id))
        }

        # 处理下一跳
        if gateway and gateway != "0.0.0.0" and self.validate_ip_address(gateway):
            # 网关路由
            ntos_route["next_hop"] = gateway
            ntos_route["type"] = "gateway"
        elif device:
            # 接口路由
            mapped_device = interface_mapping.get(device, device)
            ntos_route["next_hop"] = mapped_device
            ntos_route["type"] = "interface"

            # 如果接口没有映射，记录警告
            if device not in interface_mapping:
                log(_("static_route_processor.route_interface_no_mapping", route_id=route_id, device=device), "warning")
        elif config.get("blackhole") == "enable":
            # 黑洞路由
            ntos_route["next_hop"] = "blackhole"
            ntos_route["type"] = "blackhole"
        else:
            log(_("static_route_processor.route_missing_nexthop", route_id=route_id), "warning")
            return None



        return ntos_route

    def group_routes_by_destination(self, routes: Dict[str, Dict]) -> Dict[str, List[Dict]]:
        """
        按目的网段分组路由（借鉴旧架构逻辑）

        Args:
            routes: 路由字典

        Returns: Dict[str, List[Dict]]: 按目的网段分组的路由
        """
        grouped = {}

        for route_id, route_config in routes.items():
            destination = route_config.get("destination")
            if destination:
                if destination not in grouped:
                    grouped[destination] = []
                grouped[destination].append(route_config)



        return grouped

    def process_static_routes(self, static_routes: Dict[str, Any], interface_mapping: Dict[str, str]) -> Dict[str, Any]:
        """
        处理静态路由配置

        Args:
            static_routes: FortiGate静态路由配置
            interface_mapping: 接口映射表

        Returns: Dict[str, Any]: 处理结果
        """
        result = {
            "routes": {},
            "grouped_routes": {},
            "converted_count": 0,
            "skipped_count": 0,
            "failed_count": 0,
            "details": []
        }

        # 确保static_routes是字典格式
        if not isinstance(static_routes, dict):
            log(_("static_route_processor.static_routes_format_error", type=type(static_routes)), "error")
            return result

        # 检查是否为空
        if not static_routes:
            log(_("static_route_processor.no_data_to_process"), "info")
            return result

        for route_id, route_config in static_routes.items():
            try:
                # 确保route_config是字典格式
                if not isinstance(route_config, dict):
                    log(_("static_route_processor.route_config_format_error", route_id=route_id, type=type(route_config)), "warning")
                    result["skipped_count"] += 1
                    result["details"].append({
                        "id": route_id,
                        "status": "skipped",
                        "reason": "invalid_config_format"
                    })
                    continue

                converted_route = self.convert_static_route(route_id, route_config, interface_mapping)

                if converted_route:
                    result["routes"][route_id] = converted_route
                    result["converted_count"] += 1
                    result["details"].append({
                        "id": route_id,
                        "status": "success",
                        "destination": converted_route["destination"],
                        "next_hop": converted_route["next_hop"],
                        "type": converted_route["type"]
                    })
                else:
                    result["skipped_count"] += 1
                    result["details"].append({
                        "id": route_id,
                        "status": "skipped",
                        "reason": "invalid_configuration"
                    })

            except Exception as e:
                result["failed_count"] += 1
                result["details"].append({
                    "id": route_id,
                    "status": "failed",
                    "reason": str(e)
                })
                log(_("static_route_processor.route_processing_failed", route_id=route_id, error=str(e)), "error")

        # 按目的网段分组路由
        result["grouped_routes"] = self.group_routes_by_destination(result["routes"])

        log(_("static_route_processor.processing_complete_detailed", converted=result["converted_count"], skipped=result["skipped_count"], failed=result["failed_count"]), "info")

        return result


class StaticRouteProcessingStage(PipelineStage):
    """
    静态路由处理阶段 - 完全重新实现

    负责处理FortiGate静态路由配置，转换为NTOS格式并生成XML片段
    支持网关路由、接口路由、黑洞路由等类型
    """

    def __init__(self):
        super().__init__("static_route_processing", _("static_route_processing_stage.description"))
        self.processor = StaticRouteProcessor()
    
    def validate_input(self, context: DataContext) -> bool:
        """
        验证输入数据
        
        Args:
            context: 数据上下文
            
        Returns:
            bool: 验证是否通过
        """
        config_data = context.get_data("config_data")
        if not config_data:
            context.add_error(_("static_route_processing_stage.no_config_data"))
            return False
        
        return True
    
    def process(self, context: DataContext) -> bool:
        """
        执行静态路由处理
        
        Args:
            context: 数据上下文
            
        Returns:
            bool: 处理是否成功
        """
        log(_("static_route_processor.start_processing"), "info")

        try:
            # 获取配置数据
            config_data = context.get_data("config_data", {})
            static_routes_data = config_data.get("static_routes", {})



            # 转换数据格式：统一转换为字典格式（兼容解析器输出格式）
            static_routes = {}

            if isinstance(static_routes_data, list):
                log(_("static_route_processor.list_format_detected"), "info")
                for i, route in enumerate(static_routes_data):
                    if isinstance(route, dict):
                        # 使用id字段作为键，如果没有id则使用索引
                        route_id = route.get('id', str(i + 1))
                        static_routes[route_id] = route

                    else:
                        log(_("static_route_processor.skip_invalid_route_data", index=i, type=type(route)), "warning")

            elif isinstance(static_routes_data, dict):
                log(_("static_route_processor.dict_format_detected"), "info")
                static_routes = static_routes_data

            else:
                log(_("static_route_processor.unsupported_data_format", type=type(static_routes_data)), "warning")
                static_routes = {}

            log(_("static_route_processor.converted_routes_found", count=len(static_routes)), "info")

            if not static_routes:
                log(_("static_route_processor.no_routes_to_process"), "info")
                context.set_data("static_route_processing_result", self._empty_result())
                return True

            # 获取接口映射信息
            interface_result = context.get_data("interface_processing_result", {})
            interface_mapping = interface_result.get("interface_mapping", {})

            # 使用新的处理器处理静态路由
            route_result = self.processor.process_static_routes(static_routes, interface_mapping)

            # 生成XML片段
            xml_fragment = self._generate_static_route_xml_fragment(route_result)

            # 构建处理结果（包含静态路由和XML片段）
            processing_result = {
                "static_routes": route_result,
                "xml_fragment": xml_fragment,
                "statistics": {
                    "total_routes": len(static_routes),
                    "converted_routes": route_result['converted_count'],
                    "skipped_routes": route_result['skipped_count'],
                    "failed_routes": route_result['failed_count'],
                    "route_groups": len(route_result['grouped_routes'])
                }
            }

            # 存储处理结果
            context.set_data("static_route_processing_result", processing_result)

            log(_("static_route_processor.processing_complete_with_groups", converted=route_result["converted_count"], total=len(static_routes), groups=len(route_result["grouped_routes"])), "info")

            # 记录用户日志
            from engine.utils.user_log_formatter import record_stage_user_log
            record_stage_user_log("static_route_processing", processing_result)

            return True

        except Exception as e:
            error_msg = _("static_route_processing_stage.processing_failed", error=str(e))
            log(error_msg, "error")
            context.set_data("static_route_processing_result", self._empty_result())
            context.add_error(error_msg, self.name)
            return False

    def _generate_static_route_xml_fragment(self, route_result: Dict) -> str:
        """
        生成静态路由XML片段（借鉴旧架构，遵循YANG模型）

        Args:
            route_result: 静态路由处理结果

        Returns:
            str: XML片段字符串
        """
        try:
            grouped_routes = route_result.get("grouped_routes", {})

            if not grouped_routes:
                return ""

            # 创建routing根元素
            routing_elem = etree.Element("routing")
            routing_elem.set("xmlns", "urn:ruijie:ntos:params:xml:ns:yang:routing")

            # 创建static子元素
            static_elem = etree.SubElement(routing_elem, "static")

            # 处理每个目的网段的路由组
            for destination, route_list in grouped_routes.items():
                self._add_ipv4_route_to_xml(static_elem, destination, route_list)

            # 生成XML字符串
            xml_string = etree.tostring(
                routing_elem,
                encoding='unicode',
                pretty_print=True
            )

            return xml_string

        except Exception as e:
            log(_("static_route_processor.xml_fragment_failed", error=str(e)), "error")
            return ""

    def _add_ipv4_route_to_xml(self, static_elem: etree.Element, destination: str, route_list: List[Dict]):
        """
        添加IPv4路由到XML（遵循YANG模型结构）

        Args:
            static_elem: static XML元素
            destination: 目的网段
            route_list: 路由列表
        """
        # 创建ipv4-route元素
        ipv4_route_elem = etree.SubElement(static_elem, "ipv4-route")

        # 添加destination元素（YANG模型要求的key字段）
        dest_elem = etree.SubElement(ipv4_route_elem, "destination")
        dest_elem.text = destination

        # 为每个路由添加next-hop元素
        for route in route_list:
            self._add_next_hop_to_xml(ipv4_route_elem, route)

    def _add_next_hop_to_xml(self, ipv4_route_elem: etree.Element, route: Dict):
        """
        添加next-hop到XML（遵循YANG模型）

        Args:
            ipv4_route_elem: ipv4-route XML元素
            route: 路由配置
        """
        # 创建next-hop元素
        next_hop_elem = etree.SubElement(ipv4_route_elem, "next-hop")

        route_type = route.get("type", "gateway")
        next_hop_value = route.get("next_hop", "")

        # 根据YANG模型，next-hop是key字段，需要设置正确的值
        if route_type == "gateway":
            # 网关路由：next-hop key字段直接使用网关地址
            next_hop_key_elem = etree.SubElement(next_hop_elem, "next-hop")
            next_hop_key_elem.text = next_hop_value
        elif route_type == "interface":
            # 接口路由：next-hop key字段使用接口名
            next_hop_key_elem = etree.SubElement(next_hop_elem, "next-hop")
            next_hop_key_elem.text = next_hop_value
        elif route_type == "blackhole":
            # 黑洞路由：next-hop key字段使用"blackhole"
            next_hop_key_elem = etree.SubElement(next_hop_elem, "next-hop")
            next_hop_key_elem.text = "blackhole"

        # 添加距离（如果存在）- 这是YANG模型中允许的字段
        distance = route.get("distance")
        if distance is not None:
            distance_elem = etree.SubElement(next_hop_elem, "distance")
            distance_elem.text = str(distance)

        # 添加描述字段（如果存在）- 符合NTOS YANG模型的descr字段
        descr = route.get("descr")
        if descr:
            descr_elem = etree.SubElement(next_hop_elem, "descr")
            descr_elem.text = descr


        # 注意：priority字段在YANG模型中不存在，已移除




    
    def _empty_result(self) -> Dict[str, Any]:
        """
        返回空的处理结果

        Returns: Dict[str, Any]: 空结果
        """
        return {
            "static_routes": {
                "routes": {},
                "grouped_routes": {},
                "converted_count": 0,
                "skipped_count": 0,
                "failed_count": 0,
                "details": []
            },
            "xml_fragment": "",
            "statistics": {
                "total_routes": 0,
                "converted_routes": 0,
                "skipped_routes": 0,
                "failed_routes": 0,
                "route_groups": 0
            }
        }
