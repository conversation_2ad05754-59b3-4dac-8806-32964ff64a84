#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
时间对象处理阶段
负责处理FortiGate的时间范围对象并转换为NTOS格式
"""

import re
from typing import Dict, Any, List
from engine.processing.pipeline.pipeline_stage import PipelineStage
from engine.processing.pipeline.data_flow import DataContext
from engine.utils.logger import log
from engine.utils.i18n import _
from lxml import etree


class TimeObjectProcessor:
    """
    时间对象处理器 - 完全重新实现，借鉴旧架构核心逻辑
    """

    def __init__(self):
        # 星期映射（借鉴旧架构）
        self.weekday_mapping = {
            "sunday": "sun",
            "monday": "mon",
            "tuesday": "tue",
            "wednesday": "wed",
            "thursday": "thu",
            "friday": "fri",
            "saturday": "sat",
            # 支持缩写形式
            "sun": "sun",
            "mon": "mon",
            "tue": "tue",
            "wed": "wed",
            "thu": "thu",
            "fri": "fri",
            "sat": "sat"
        }

    def validate_time_format(self, time_str: str) -> bool:
        """
        验证时间格式（借鉴旧架构逻辑）

        Args:
            time_str: 时间字符串

        Returns:
            bool: 是否为有效格式
        """
        if not time_str:
            return False

        # 支持的时间格式：HH:MM、HH:MM:SS
        time_patterns = [
            r'^\d{2}:\d{2}$',           # HH:MM
            r'^\d{2}:\d{2}:\d{2}$',     # HH:MM:SS
        ]

        for pattern in time_patterns:
            if re.match(pattern, time_str):
                return True

        return False

    def normalize_time_format(self, time_str: str) -> str:
        """
        标准化时间格式

        Args:
            time_str: 原始时间字符串

        Returns:
            str: 标准化后的时间字符串（HH:MM:SS格式）
        """
        if not time_str:
            return ""

        # 如果已经是HH:MM:SS格式，直接返回
        if re.match(r'^\d{2}:\d{2}:\d{2}$', time_str):
            return time_str

        # 如果是HH:MM格式，补充秒数
        if re.match(r'^\d{2}:\d{2}$', time_str):
            return f"{time_str}:00"

        # 其他格式，返回原值并记录警告
        log(_("time_range_processor.time_format_non_standard", time=time_str), "warning")
        return time_str

    def parse_weekdays(self, day_config: str) -> List[str]:
        """
        解析星期配置（借鉴旧架构逻辑）

        Args:
            day_config: FortiGate星期配置字符串

        Returns: List[str]: NTOS星期名称列表
        """
        if not day_config:
            return []

        weekdays = []
        day_config_lower = day_config.lower()

        # 检查每个星期名称
        for fg_day, ntos_day in self.weekday_mapping.items():
            if fg_day in day_config_lower:
                if ntos_day not in weekdays:  # 避免重复
                    weekdays.append(ntos_day)

        return weekdays

    def is_cross_day_time(self, start_time: str, end_time: str) -> bool:
        """
        检测时间范围是否跨天（借鉴旧架构逻辑）

        Args:
            start_time: 开始时间
            end_time: 结束时间

        Returns:
            bool: 是否跨天
        """
        if not start_time or not end_time:
            return False

        try:
            # 解析时间格式为小时和分钟
            start_parts = start_time.split(":")
            end_parts = end_time.split(":")

            if len(start_parts) >= 2 and len(end_parts) >= 2:
                start_hour, start_min = int(start_parts[0]), int(start_parts[1])
                end_hour, end_min = int(end_parts[0]), int(end_parts[1])

                # 如果结束时间小于开始时间，则跨天
                if end_hour < start_hour or (end_hour == start_hour and end_min < start_min):
                    return True
        except (ValueError, IndexError):
            log(_("time_range_processor.time_format_parse_failed", start=start_time, end=end_time), "warning")

        return False

    def is_always_schedule(self, config: Dict[str, Any]) -> bool:
        """
        检测时间对象是否为"always"类型（借鉴旧架构逻辑）

        Args:
            config: FortiGate时间对象配置

        Returns:
            bool: 是否为"always"类型
        """
        # 必须是周期性类型
        if config.get("type", "recurring") != "recurring":
            return False

        # 检查是否包含所有星期几
        day_config = config.get("day", "")
        weekdays = self.parse_weekdays(day_config)
        all_days = {"sun", "mon", "tue", "wed", "thu", "fri", "sat"}

        if set(weekdays) != all_days:
            return False

        # 检查时间范围是否为全天
        start_time = config.get("start", "00:00")
        end_time = config.get("end", "23:59")

        # 标准化时间格式
        normalized_start = self.normalize_time_format(start_time)
        normalized_end = self.normalize_time_format(end_time)

        # 允许一些常见的全天时间表示
        full_day_starts = {"00:00:00", "0:00:00"}
        full_day_ends = {"23:59:59", "23:59:00"}

        return normalized_start in full_day_starts and normalized_end in full_day_ends

    def get_next_weekday(self, weekday: str) -> str:
        """
        获取下一天的星期名称（借鉴旧架构逻辑）

        Args:
            weekday: 当前星期名称

        Returns:
            str: 下一天的星期名称
        """
        days = ["sun", "mon", "tue", "wed", "thu", "fri", "sat"]
        try:
            idx = days.index(weekday)
            return days[(idx + 1) % 7]
        except ValueError:
            log(_("time_range_processor.invalid_weekday_name", weekday=weekday), "warning")
            return weekday  # 如果找不到，返回原始值

    def split_cross_day_time(self, start_time: str, end_time: str, weekdays: List[str]) -> List[Dict]:
        """
        拆分跨天时间为两个period（借鉴旧架构逻辑）

        Args:
            start_time: 开始时间
            end_time: 结束时间
            weekdays: 星期列表

        Returns: List[Dict]: 两个period配置
        """
        periods = []

        # 第一部分：从开始时间到午夜
        period1 = {
            "start": start_time,
            "end": "23:59:59",
            "weekdays": weekdays
        }
        periods.append(period1)

        # 第二部分：从午夜到结束时间（下一天）
        next_day_weekdays = [self.get_next_weekday(day) for day in weekdays]
        period2 = {
            "start": "00:00:00",
            "end": end_time,
            "weekdays": next_day_weekdays
        }
        periods.append(period2)

        return periods

    def create_always_periods(self) -> List[Dict]:
        """
        创建"always"时间范围的完整week（借鉴旧架构逻辑）

        Returns: List[Dict]: 包含完整一周的period配置
        """
        all_weekdays = ["sun", "mon", "tue", "wed", "thu", "fri", "sat"]

        period = {
            "start": "00:00:00",
            "end": "23:59:59",
            "weekdays": all_weekdays
        }

        return [period]

    def convert_recurring_schedule(self, name: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        转换周期性时间对象（借鉴旧架构逻辑，支持跨天和always）

        Args:
            name: 时间对象名称
            config: FortiGate配置

        Returns: Dict[str, Any]: NTOS时间对象配置
        """
        ntos_config = {
            "name": name,
            "type": "recurring",
            "description": config.get("comment", f"{name}"),
            "enabled": True,
            "periods": []
        }

        # 检查是否为"always"类型
        if self.is_always_schedule(config):
            log(_("time_range_processor.always_schedule_detected", name=name), "info")
            ntos_config["periods"] = self.create_always_periods()
            return ntos_config

        # 处理星期和时间设置（兼容多种键名）
        day_config = config.get("day", "") or config.get("weekdays", [])
        start_time = config.get("time_start") or config.get("start", "00:00")
        end_time = config.get("time_end") or config.get("end", "23:59")

        if day_config:
            # 处理不同格式的weekdays
            if isinstance(day_config, list):
                # 如果是数组格式，直接使用
                weekdays = day_config
            else:
                # 如果是字符串格式，解析
                weekdays = self.parse_weekdays(day_config)
            if weekdays:
                # 标准化时间格式
                normalized_start = self.normalize_time_format(start_time)
                normalized_end = self.normalize_time_format(end_time)

                # 检查是否跨天
                if self.is_cross_day_time(normalized_start, normalized_end):
                    log(_("time_range_processor.cross_day_time_detected", name=name, start=normalized_start, end=normalized_end), "info")
                    # 拆分为两个period
                    cross_day_periods = self.split_cross_day_time(normalized_start, normalized_end, weekdays)
                    ntos_config["periods"].extend(cross_day_periods)
                else:
                    # 普通时间范围
                    ntos_config["periods"].append({
                        "start": normalized_start,
                        "end": normalized_end,
                        "weekdays": weekdays
                    })



        return ntos_config

    def convert_onetime_schedule(self, name: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        转换一次性时间对象（借鉴旧架构逻辑）

        Args:
            name: 时间对象名称
            config: FortiGate配置

        Returns: Dict[str, Any]: NTOS时间对象配置
        """
        # 获取原始时间字符串
        start_raw = config.get("start", "") or config.get("datetime_start", "")
        end_raw = config.get("end", "") or config.get("datetime_end", "")

        # 转换为YANG模型要求的格式：hh:mm:ss/YYYY-MM-DD
        start_formatted = self._format_onetime_datetime(start_raw)
        end_formatted = self._format_onetime_datetime(end_raw)

        ntos_config = {
            "name": name,
            "type": "onetime",
            "description": config.get("comment", f"一次性时间范围 {name}"),
            "enabled": True,
            "start": start_formatted,
            "end": end_formatted
        }



        return ntos_config

    def _format_onetime_datetime(self, datetime_str: str) -> str:
        """
        格式化一次性时间对象的日期时间字符串

        Args:
            datetime_str: 原始日期时间字符串（如："08:00 2024/12/31"）

        Returns:
            str: YANG模型格式的日期时间字符串（如："08:00:00/2024-12-31"）
        """
        if not datetime_str:
            return ""

        # 分割时间和日期部分
        parts = datetime_str.strip().split()
        if len(parts) != 2:
            log(_("time_range_processor.invalid_onetime_format", datetime=datetime_str), "warning")
            return datetime_str

        time_part, date_part = parts

        # 标准化时间格式（确保有秒）
        time_formatted = self.normalize_time_format(time_part)

        # 转换日期格式：YYYY/MM/DD -> YYYY-MM-DD
        date_formatted = date_part.replace("/", "-")

        # 组合为YANG模型要求的格式
        result = f"{time_formatted}/{date_formatted}"

        log(_("time_range_processor.onetime_format_converted", original=datetime_str, formatted=result), "debug")
        return result

    def convert_schedule_group(self, name: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        转换时间对象组（借鉴旧架构逻辑）

        Args:
            name: 时间对象组名称
            config: FortiGate配置

        Returns: Dict[str, Any]: NTOS时间对象组配置
        """
        ntos_config = {
            "name": name,
            "type": "group",
            "description": config.get("comment", f"时间对象组 {name}"),
            "enabled": True,
            "members": []
        }

        # 处理成员（兼容多种键名）
        members = config.get("members", []) or config.get("member", [])
        if isinstance(members, str):
            members = [members]

        ntos_config["members"] = members

        log(_("time_range_processor.time_group_conversion", name=name, members=members), "debug")

        return ntos_config

    def process_time_objects(self, time_objects: Dict) -> Dict[str, Any]:
        """
        处理时间对象字典

        Args:
            time_objects: 时间对象字典

        Returns:
            Dict: 处理结果
        """
        result = {
            "converted": {},
            "skipped": {},
            "failed": {},
            "converted_count": 0,
            "skipped_count": 0,
            "failed_count": 0,
            "details": []
        }

        for obj_name, obj_config in time_objects.items():
            try:
                schedule_type = obj_config.get("type", "recurring")

                if schedule_type == "recurring":
                    converted_obj = self.convert_recurring_schedule(obj_name, obj_config)
                elif schedule_type == "onetime":
                    converted_obj = self.convert_onetime_schedule(obj_name, obj_config)
                elif schedule_type == "group":
                    converted_obj = self.convert_schedule_group(obj_name, obj_config)
                else:
                    reason = f"不支持的时间对象类型: {schedule_type}"
                    self._add_skipped_result(result, obj_name, obj_config, reason)
                    continue

                result["converted"][obj_name] = converted_obj
                result["converted_count"] += 1
                result["details"].append({
                    "name": obj_name,
                    "status": "success",
                    "type": schedule_type,
                    "converted": converted_obj
                })

                log(_("time_range_processor.conversion_success", name=obj_name, type=schedule_type), "debug")

            except Exception as e:
                reason = f"时间对象处理错误: {str(e)}"
                self._add_failed_result(result, obj_name, obj_config, reason)

        return result

    def _add_skipped_result(self, result: Dict, name: str, config: Dict, reason: str):
        """添加跳过的结果"""
        result["skipped"][name] = config
        result["skipped_count"] += 1
        result["details"].append({
            "name": name,
            "status": "skipped",
            "reason": reason,
            "original": config
        })

    def _add_failed_result(self, result: Dict, name: str, config: Dict, reason: str):
        """添加失败的结果"""
        result["failed"][name] = config
        result["failed_count"] += 1
        result["details"].append({
            "name": name,
            "status": "failed",
            "reason": reason,
            "original": config
        })


class TimeRangeProcessingStage(PipelineStage):
    """时间对象处理阶段"""
    
    def __init__(self):
        super().__init__("time_range_processing", _("time_range_processing_stage.description"))
        self.processor = TimeObjectProcessor()
    
    def validate_input(self, context: DataContext) -> bool:
        """
        验证输入数据
        
        Args:
            context: 数据上下文
            
        Returns:
            bool: 验证是否通过
        """
        config_data = context.get_data("config_data")
        if not config_data:
            context.add_error(_("time_range_processing_stage.no_config_data"))
            return False

        return True
    
    def process(self, context: DataContext) -> bool:
        """
        执行时间对象处理
        
        Args:
            context: 数据上下文
            
        Returns:
            bool: 处理是否成功
        """
        log(_("time_range_processing_stage.starting"))
        
        try:
            # 获取配置数据（兼容多种数据源）
            config_data = context.get_data("config_data", {})
            parsed_config = context.get_data("parsed_config", {})

            # 尝试多个可能的键名和数据源来获取时间对象数据
            schedule_objects_data = (
                config_data.get("schedule_objects", []) or
                config_data.get("time_ranges", []) or
                config_data.get("schedules", []) or
                parsed_config.get("schedule_objects", []) or
                parsed_config.get("time_ranges", []) or
                parsed_config.get("schedules", [])
            )

            # 转换数据格式：从列表转换为字典（兼容解析器输出格式）
            schedule_objects = {}
            if isinstance(schedule_objects_data, list):
                for schedule in schedule_objects_data:
                    if isinstance(schedule, dict) and 'name' in schedule:
                        schedule_objects[schedule['name']] = schedule
            elif isinstance(schedule_objects_data, dict):
                schedule_objects = schedule_objects_data

            log(_("time_range_processor.found_objects", count=len(schedule_objects)), "info")

            if not schedule_objects:
                log(_("time_range_processor.no_objects_to_process"), "info")
                context.set_data("time_range_processing_result", self._empty_result())
                return True

            # 使用新的处理器处理时间对象
            time_result = self.processor.process_time_objects(schedule_objects)

            # 生成XML片段
            log(f"开始生成时间对象XML片段，转换结果: {time_result.get('converted_count', 0)} 个", "info")
            xml_fragment = self._generate_time_range_xml_fragment(time_result)
            log(f"XML片段生成完成，长度: {len(xml_fragment) if xml_fragment else 0}", "info")

            # 构建处理结果（包含时间对象和XML片段）
            processing_result = {
                "time_ranges": time_result,
                "xml_fragment": xml_fragment,
                "statistics": {
                    "total_schedules": len(schedule_objects),
                    "converted_schedules": time_result['converted_count'],
                    "skipped_schedules": time_result['skipped_count'],
                    "failed_schedules": time_result['failed_count']
                }
            }

            # 存储处理结果
            context.set_data("time_range_processing_result", processing_result)

            log(_("time_range_processor.processing_complete", success=time_result['converted_count'], total=len(schedule_objects)), "info")

            # 记录用户日志
            from engine.utils.user_log_formatter import record_stage_user_log
            record_stage_user_log("time_range_processing", processing_result)

            return True
            
        except Exception as e:
            error_msg = f"时间对象处理失败: {str(e)}"
            error_msg = _("time_range_processing_stage.processing_failed", error=str(e))
            log(error_msg, "error")
            error_msg = _("time_range_processor.detailed_error_info",  error=error_msg)
            log(error_msg, "error")
            log(f"Config data type: {type(config_data)}", "debug")
            log(f"Original time object data type: {type(schedule_objects_data)}", "debug")
            log(f"Converted time object data type: {type(schedule_objects)}", "debug")
            context.set_data("time_range_processing_result", self._empty_result())
            context.add_error(error_msg, self.name)
            return False

    def _generate_time_range_xml_fragment(self, time_result: Dict) -> str:
        """
        生成时间对象XML片段（借鉴旧架构，遵循YANG模型）

        Args:
            time_result: 时间对象处理结果

        Returns:
            str: XML片段字符串
        """
        try:
            converted_objects = time_result.get("converted", {})
            log(f"XML片段生成：找到 {len(converted_objects)} 个转换的时间对象", "info")

            if not converted_objects:
                log("XML片段生成：没有转换的时间对象，返回空字符串", "info")
                return ""

            # 创建time-range根元素
            time_range_elem = etree.Element("time-range")
            time_range_elem.set("xmlns", "urn:ruijie:ntos:params:xml:ns:yang:time-range")

            # 处理每个时间对象
            for obj_name, obj_config in converted_objects.items():
                self._add_time_range_to_xml(time_range_elem, obj_config)

            # 生成XML字符串
            xml_string = etree.tostring(
                time_range_elem,
                encoding='unicode',
                pretty_print=True
            )

            log(_("time_range_processor.xml_fragment_success", count=len(converted_objects)), "debug")
            log(f"XML片段生成成功，长度: {len(xml_string)}", "info")
            return xml_string

        except Exception as e:
            log(_("time_range_processor.xml_fragment_failed", error=str(e)), "error")
            return ""

    def _add_time_range_to_xml(self, time_range_elem: etree.Element, obj_config: Dict):
        """
        添加单个时间对象到XML（遵循YANG模型结构）

        Args:
            time_range_elem: time-range XML元素
            obj_config: 时间对象配置
        """
        # 创建range元素
        range_elem = etree.SubElement(time_range_elem, "range")

        # 添加name元素
        name_elem = etree.SubElement(range_elem, "name")
        name_elem.text = obj_config["name"]

        # 添加description元素
        if obj_config.get("description"):
            desc_elem = etree.SubElement(range_elem, "description")
            desc_elem.text = obj_config["description"]

        obj_type = obj_config.get("type", "recurring")

        if obj_type == "recurring":
            self._add_recurring_periods_to_xml(range_elem, obj_config)
        elif obj_type == "onetime":
            self._add_onetime_to_xml(range_elem, obj_config)
        elif obj_type == "group":
            # 时间对象组：NTOS YANG模型中没有独立的group类型，跳过XML生成
            # 时间对象组的功能通过策略中引用多个时间对象来实现
            log(_("time_range_processor.time_group_xml_skipped", name=obj_config['name']), "info")

    def _add_recurring_periods_to_xml(self, range_elem: etree.Element, obj_config: Dict):
        """
        添加周期性时间对象的period元素（遵循YANG模型，支持多period）

        Args:
            range_elem: range XML元素
            obj_config: 时间对象配置
        """
        periods = obj_config.get("periods", [])

        for i, period_config in enumerate(periods):
            # 创建period元素（使用start和end作为键）
            period_elem = etree.SubElement(range_elem, "period")

            # 添加start和end元素
            start_time = period_config.get("start", "00:00:00")
            end_time = period_config.get("end", "23:59:59")

            start_elem = etree.SubElement(period_elem, "start")
            start_elem.text = start_time

            end_elem = etree.SubElement(period_elem, "end")
            end_elem.text = end_time

            # 添加weekday元素
            weekdays = period_config.get("weekdays", [])
            for weekday in weekdays:
                weekday_elem = etree.SubElement(period_elem, "weekday")
                key_elem = etree.SubElement(weekday_elem, "key")
                key_elem.text = weekday

            log(_("time_range_processor.add_period", index=i+1, start=start_time, end=end_time, weekdays=weekdays), "debug")

    def _add_onetime_to_xml(self, range_elem: etree.Element, obj_config: Dict):
        """
        添加一次性时间对象的once容器（遵循YANG模型）

        Args:
            range_elem: range XML元素
            obj_config: 时间对象配置
        """
        # 创建once容器元素（符合NTOS YANG模型）
        once_elem = etree.SubElement(range_elem, "once")

        # 在once容器内添加start元素
        if obj_config.get("start"):
            start_elem = etree.SubElement(once_elem, "start")
            start_elem.text = obj_config["start"]

        # 在once容器内添加end元素
        if obj_config.get("end"):
            end_elem = etree.SubElement(once_elem, "end")
            end_elem.text = obj_config["end"]


    
    def _empty_result(self) -> Dict[str, Any]:
        """
        返回空的处理结果

        Returns: Dict[str, Any]: 空结果
        """
        return {
            "time_ranges": {
                "converted": {},
                "skipped": {},
                "failed": {},
                "converted_count": 0,
                "skipped_count": 0,
                "failed_count": 0,
                "details": []
            },
            "xml_fragment": "",
            "statistics": {
                "total_schedules": 0,
                "converted_schedules": 0,
                "skipped_schedules": 0,
                "failed_schedules": 0
            }
        }
