# -*- coding: utf-8 -*-
"""
XML模板集成阶段 - 主控制器
将转换结果集成到XML模板中，确保与现有XML模板处理逻辑的完全兼容性
"""

import os
import copy
from datetime import datetime
from typing import Dict, Any, Optional, List
from lxml import etree
from engine.utils.logger import log
from engine.utils.i18n import _
from engine.processing.pipeline.pipeline_stage import PipelineStage
from engine.processing.pipeline.data_flow import DataContext
from engine.infrastructure.error_handling import (
    twice_nat44_error_handler, twice_nat44_performance_monitor,
    twice_nat44_operation_context, TwiceNat44ErrorContext
)


class XmlTemplateIntegrationStage(PipelineStage):
    """
    XML模板集成阶段
    负责将转换后的配置数据集成到XML模板中
    """
    
    def __init__(self, config_manager, template_manager, name_mapping_manager=None):
        """
        初始化XML模板集成阶段

        Args:
            config_manager: 配置管理器
            template_manager: 模板管理器
            name_mapping_manager: 名称映射管理器（可选）
        """
        super().__init__(
            name="xml_template_integration",
            description="XML模板集成阶段 - 将转换结果集成到XML模板"
        )

        self.config_manager = config_manager
        self.template_manager = template_manager

        # 初始化名称映射管理器
        self.name_mapping_manager = name_mapping_manager
        if self.name_mapping_manager is None:
            from engine.utils.name_mapping_manager import NameMappingManager
            self.name_mapping_manager = NameMappingManager()

        log(_("xml_template_integration_stage.initialized"), "info")

    # ==================== 通用XML节点查找方法 ====================

    def _find_element_robust(self, parent: etree.Element, tag_name: str,
                           namespace: str = None, use_xpath: bool = True) -> etree.Element:
        """
        通用的XML元素查找方法，支持多种查找策略

        这个方法统一了整个文件中重复出现的XML节点查找逻辑，
        解决了命名空间处理的复杂性问题。

        查找策略（按优先级顺序）：
        1. 直接查找（无命名空间）
        2. 使用指定命名空间查找
        3. 使用XPath local-name()查找（忽略命名空间）

        Args:
            parent: 父元素
            tag_name: 要查找的标签名
            namespace: 可选的命名空间URI
            use_xpath: 是否使用XPath作为备用方法

        Returns:
            找到的元素，如果未找到返回None
        """
        if parent is None:
            return None

        # 方法1：直接查找（最常见的情况）
        element = parent.find(f".//{tag_name}")
        if element is not None:
            return element

        # 方法2：使用指定命名空间查找
        if namespace:
            try:
                element = parent.find(f".//{{{namespace}}}{tag_name}")
                if element is not None:
                    return element
            except Exception:
                pass

        # 方法3：使用XPath local-name()查找（最可靠，忽略命名空间）
        if use_xpath:
            try:
                elements = parent.xpath(f".//*[local-name()='{tag_name}']")
                if elements:
                    return elements[0]
            except Exception:
                pass

        return None

    def _find_elements_robust(self, parent: etree.Element, tag_name: str,
                            namespace: str = None, use_xpath: bool = True) -> List[etree.Element]:
        """
        通用的XML元素列表查找方法，支持多种查找策略

        Args:
            parent: 父元素
            tag_name: 要查找的标签名
            namespace: 可选的命名空间URI
            use_xpath: 是否使用XPath作为备用方法

        Returns:
            找到的元素列表
        """
        if parent is None:
            return []

        elements = []

        # 方法1：直接查找
        elements.extend(parent.findall(f".//{tag_name}"))

        # 方法2：使用指定命名空间查找
        if namespace:
            try:
                elements.extend(parent.findall(f".//{{{namespace}}}{tag_name}"))
            except Exception:
                pass

        # 方法3：使用XPath local-name()查找
        if use_xpath:
            try:
                xpath_elements = parent.xpath(f".//*[local-name()='{tag_name}']")
                elements.extend(xpath_elements)
            except Exception:
                pass

        # 去重（保持顺序）
        seen = set()
        unique_elements = []
        for elem in elements:
            elem_id = id(elem)
            if elem_id not in seen:
                seen.add(elem_id)
                unique_elements.append(elem)

        return unique_elements

    def _find_child_element_robust(self, parent: etree.Element, tag_name: str,
                                 namespace: str = None, use_xpath: bool = True) -> etree.Element:
        """
        通用的直接子元素查找方法

        Args:
            parent: 父元素
            tag_name: 要查找的标签名
            namespace: 可选的命名空间URI
            use_xpath: 是否使用XPath作为备用方法

        Returns:
            找到的直接子元素，如果未找到返回None
        """
        if parent is None:
            return None

        # 方法1：直接查找
        element = parent.find(tag_name)
        if element is not None:
            return element

        # 方法2：使用指定命名空间查找
        if namespace:
            try:
                element = parent.find(f"{{{namespace}}}{tag_name}")
                if element is not None:
                    return element
            except Exception:
                pass

        # 方法3：使用XPath查找直接子元素
        if use_xpath:
            try:
                elements = parent.xpath(f"./*[local-name()='{tag_name}']")
                if elements:
                    return elements[0]
            except Exception:
                pass

        return None

    # ==================== 通用XML片段处理方法 ====================

    def _parse_xml_fragment_robust(self, xml_fragment: str, fragment_name: str = "XML片段") -> etree.Element:
        """
        通用的XML片段解析方法，统一异常处理

        Args:
            xml_fragment: XML片段字符串
            fragment_name: 片段名称（用于日志）

        Returns:
            解析后的XML根元素，解析失败返回None
        """
        if not xml_fragment:
            log(_("xml_template_integration.empty_xml_fragment", name=fragment_name), "debug")
            return None

        try:
            fragment_root = etree.fromstring(xml_fragment)
            log(_("xml_template_integration.xml_fragment_parse_success", name=fragment_name), "debug")
            return fragment_root
        except etree.XMLSyntaxError as e:
            log(_("xml_template_integration.xml_fragment_parse_failed", name=fragment_name, error=str(e)), "error")
            # 记录片段内容的前500个字符用于调试
            log(_("xml_template_integration.xml_fragment_content", content=xml_fragment[:500]), "error")
            return None
        except Exception as e:
            log(_("xml_template_integration.xml_fragment_parse_exception", name=fragment_name, error=str(e)), "error")
            return None

    def _integrate_xml_fragment_to_container(self, fragment_root: etree.Element,
                                           container_elem: etree.Element,
                                           namespace: str = None) -> bool:
        """
        通用的XML片段集成方法，将片段内容集成到容器中

        Args:
            fragment_root: 解析后的XML片段根元素
            container_elem: 目标容器元素
            namespace: 可选的命名空间URI

        Returns:
            集成是否成功
        """
        if fragment_root is None or container_elem is None:
            return False

        try:
            # 首先清理可能的重复xmlns属性
            self._clean_duplicate_xmlns_attributes(fragment_root)

            # 智能命名空间处理：避免重复设置
            if namespace:
                existing_xmlns = fragment_root.get("xmlns")
                if existing_xmlns is None:
                    # 只有在没有xmlns属性时才设置
                    fragment_root.set("xmlns", namespace)
                    log(f"🔧 为{fragment_root.tag}元素设置xmlns属性: {namespace}", "debug")
                elif existing_xmlns == namespace:
                    # 如果已存在相同的命名空间，不需要做任何处理
                    log(f"✅ {fragment_root.tag}元素已有正确的xmlns属性: {existing_xmlns}", "debug")
                else:
                    # 如果已存在不同的命名空间，记录警告但不覆盖
                    log(_("xml_template_integration.namespace_conflict_warning",
                         existing=existing_xmlns, new=namespace), "warning")

            # 再次清理，确保没有重复属性
            self._clean_duplicate_xmlns_attributes(fragment_root)

            # 将片段根元素添加到容器中
            container_elem.append(fragment_root)
            return True
        except Exception as e:
            log(_("xml_template_integration.xml_fragment_integration_failed", error=str(e)), "error")
            return False

    def _clean_duplicate_xmlns_attributes(self, element: etree.Element):
        """
        增强的XML元素重复xmlns属性清理方法

        Args:
            element: 要清理的XML元素
        """
        try:
            if element is None:
                return

            # 方法1：检查属性字典中的重复xmlns相关属性
            attrib_dict = element.attrib
            xmlns_keys = [key for key in attrib_dict.keys() if key == 'xmlns' or key.startswith('xmlns_') or key.startswith('xmlns:')]

            if len(xmlns_keys) > 1:
                # 保留第一个有效的xmlns值（优先保留标准的xmlns属性）
                xmlns_value = None
                if 'xmlns' in xmlns_keys:
                    xmlns_value = attrib_dict['xmlns']
                else:
                    # 如果没有标准的xmlns属性，保留第一个xmlns相关属性的值
                    for key in xmlns_keys:
                        if attrib_dict[key] and not xmlns_value:
                            xmlns_value = attrib_dict[key]
                            break

                # 清除所有xmlns相关属性
                for key in list(xmlns_keys):  # 使用list()避免在迭代时修改字典
                    if key in attrib_dict:
                        del attrib_dict[key]

                # 重新设置唯一的xmlns属性
                if xmlns_value:
                    element.set("xmlns", xmlns_value)
                    log(f"🔧 清理了{element.tag}元素的重复xmlns相关属性，保留值: {xmlns_value}", "debug")

            # 方法2：检查属性字符串表示中的重复模式
            attrib_str = str(element.attrib)
            if attrib_str.count('xmlns') > 1:
                log(f"⚠️ 检测到{element.tag}元素可能存在复杂的xmlns重复模式: {attrib_str}", "warning")

                # 获取当前的xmlns值
                current_xmlns = element.get("xmlns")
                if current_xmlns:
                    # 清除所有属性并重新设置
                    all_attribs = dict(element.attrib)
                    element.clear()

                    # 重新设置非xmlns属性
                    for key, value in all_attribs.items():
                        if key != 'xmlns' and not key.endswith(':xmlns'):
                            element.set(key, value)

                    # 最后设置xmlns属性
                    element.set("xmlns", current_xmlns)
                    log(f"🔧 完全重建了{element.tag}元素的属性，xmlns值: {current_xmlns}", "debug")

        except Exception as e:
            log(f"清理{element.tag if element is not None else 'unknown'}元素xmlns属性失败: {str(e)}", "warning")

    def _clean_policy_element_attributes(self, policy_element: etree.Element):
        """
        清理policy元素的多余属性，确保符合NTOS YANG模型规范

        根据NTOS YANG模型，security-policy下的policy标签应该是纯容器元素，
        不应该包含xmlns或其他不必要的属性。

        Args:
            policy_element: policy XML元素
        """
        try:
            if policy_element is None or policy_element.tag != "policy":
                return

            # 获取当前所有属性
            current_attributes = dict(policy_element.attrib)

            # 定义policy元素允许的属性列表（根据YANG模型）
            # 对于security-policy下的policy容器，通常不应该有任何属性
            allowed_attributes = set()  # policy容器元素不应该有属性

            # 检查并移除不允许的属性
            removed_attributes = []
            for attr_name in current_attributes:
                if attr_name not in allowed_attributes:
                    del policy_element.attrib[attr_name]
                    removed_attributes.append(attr_name)

            if removed_attributes:
                log(f"🔧 清理了policy元素的多余属性: {removed_attributes}", "debug")

        except Exception as e:
            log(f"清理policy元素属性失败: {str(e)}", "warning")

    def _clean_all_duplicate_xmlns_attributes(self, root: etree.Element):
        """
        递归清理整个XML树中所有元素的重复xmlns属性

        Args:
            root: XML根元素
        """
        try:
            # 清理根元素
            self._clean_duplicate_xmlns_attributes(root)

            # 递归清理所有子元素
            for element in root.iter():
                if element != root:  # 避免重复处理根元素
                    self._clean_duplicate_xmlns_attributes(element)

            log("🔧 完成整个XML树的重复xmlns属性清理", "debug")

        except Exception as e:
            log(f"清理XML树重复xmlns属性失败: {str(e)}", "warning")

    def _clean_duplicate_xmlns_in_string(self, xml_string: str) -> str:
        """
        在XML字符串级别清理重复的xmlns属性
        这是最后的安全措施，确保即使在XML序列化过程中产生的重复xmlns也能被清理

        Args:
            xml_string: XML字符串

        Returns:
            str: 清理后的XML字符串
        """
        try:
            import re

            original_string = xml_string

            # 模式1：修复重复的相同xmlns属性: xmlns="uri" xmlns="uri"
            pattern1 = r'xmlns="([^"]+)"\s+xmlns="\1"'
            xml_string = re.sub(pattern1, r'xmlns="\1"', xml_string)

            # 模式2：修复更复杂的重复: xmlns="uri" 其他属性 xmlns="uri"
            pattern2 = r'xmlns="([^"]+)"([^>]*?)xmlns="\1"'
            xml_string = re.sub(pattern2, r'xmlns="\1"\2', xml_string)

            # 模式3：修复多个连续的相同xmlns属性
            pattern3 = r'(xmlns="[^"]+")(\s+\1)+'
            xml_string = re.sub(pattern3, r'\1', xml_string)

            if xml_string != original_string:
                log("🔧 在字符串级别清理了重复的xmlns属性", "debug")

            return xml_string

        except Exception as e:
            log(f"字符串级别xmlns清理失败: {str(e)}", "warning")
            return xml_string

    def _clean_all_policy_elements_xmlns(self, root: etree.Element):
        """
        清理所有policy元素的xmlns属性

        根据NTOS YANG模型，security-policy下的policy元素应该是纯容器元素，
        不应该包含xmlns属性。

        Args:
            root: XML根元素
        """
        try:
            # 查找所有policy元素（在security-policy容器下）
            policy_elements = root.xpath("//security-policy/policy")

            cleaned_count = 0
            for policy_elem in policy_elements:
                if "xmlns" in policy_elem.attrib:
                    del policy_elem.attrib["xmlns"]
                    cleaned_count += 1

            if cleaned_count > 0:
                log(f"🔧 清理了{cleaned_count}个policy元素的xmlns属性", "debug")

        except Exception as e:
            log(f"清理policy元素xmlns属性失败: {str(e)}", "warning")

    def _clean_policy_xmlns_in_string(self, xml_string: str) -> str:
        """
        在XML字符串级别清理policy元素的xmlns属性

        这是最终的安全措施，确保所有policy元素都不包含xmlns属性，
        符合NTOS YANG模型规范。

        Args:
            xml_string: XML字符串

        Returns:
            str: 清理后的XML字符串
        """
        try:
            import re

            # 清理policy标签中的xmlns属性
            # 匹配模式：<policy xmlns="任何内容">
            pattern = r'<policy\s+xmlns="[^"]*"([^>]*)>'
            replacement = r'<policy\1>'

            original_string = xml_string
            xml_string = re.sub(pattern, replacement, xml_string)

            if xml_string != original_string:
                # 统计清理的数量
                cleaned_count = len(re.findall(r'<policy\s+xmlns="[^"]*"[^>]*>', original_string))
                log(f"🔧 在字符串级别清理了{cleaned_count}个policy元素的xmlns属性", "debug")

            return xml_string

        except Exception as e:
            log(f"字符串级别policy xmlns清理失败: {str(e)}", "warning")
            return xml_string

    def _merge_xml_elements(self, source_elem: etree.Element, target_elem: etree.Element,
                          merge_strategy: str = "append") -> bool:
        """
        通用的XML元素合并方法

        Args:
            source_elem: 源元素
            target_elem: 目标元素
            merge_strategy: 合并策略 ("append", "replace", "merge")

        Returns:
            合并是否成功
        """
        if source_elem is None or target_elem is None:
            return False

        try:
            # 检查是否为security-zone容器，如果是，需要特殊处理
            target_is_security_zone = (
                target_elem.tag.endswith('security-zone') and
                'urn:ruijie:ntos:params:xml:ns:yang:security-zone' in target_elem.tag
            )

            if merge_strategy == "append":
                # 直接添加源元素的所有子元素
                for child in source_elem:
                    # 如果目标是security-zone容器，检查子元素是否应该被添加
                    if target_is_security_zone and self._should_skip_element_in_security_zone(child):
                        log(_("xml_template_integration.skip_invalid_element_in_security_zone",
                             element=child.tag), "debug")
                        continue
                    target_elem.append(child)
            elif merge_strategy == "replace":
                # 清空目标元素并添加源元素的内容
                target_elem.clear()
                target_elem.text = source_elem.text
                target_elem.tail = source_elem.tail
                for child in source_elem:
                    # 如果目标是security-zone容器，检查子元素是否应该被添加
                    if target_is_security_zone and self._should_skip_element_in_security_zone(child):
                        log(_("xml_template_integration.skip_invalid_element_in_security_zone",
                             element=child.tag), "debug")
                        continue
                    target_elem.append(child)
            elif merge_strategy == "merge":
                # 智能合并：检查重复元素
                existing_names = set()
                for existing_child in target_elem:
                    name_elem = self._find_child_element_robust(existing_child, "name")
                    if name_elem is not None:
                        existing_names.add(name_elem.text)

                # 只添加不重复的元素
                for new_child in source_elem:
                    # 如果目标是security-zone容器，检查子元素是否应该被添加
                    if target_is_security_zone and self._should_skip_element_in_security_zone(new_child):
                        log(_("xml_template_integration.skip_invalid_element_in_security_zone",
                             element=new_child.tag), "debug")
                        continue

                    name_elem = self._find_child_element_robust(new_child, "name")
                    if name_elem is None or name_elem.text not in existing_names:
                        target_elem.append(new_child)
                        if name_elem is not None:
                            existing_names.add(name_elem.text)

            return True
        except Exception as e:
            log(_("xml_template_integration.xml_merge_failed", error=str(e)), "error")
            return False

    def _should_skip_element_in_security_zone(self, element: etree.Element) -> bool:
        """
        检查元素是否应该在security-zone容器中被跳过

        Args:
            element: 要检查的XML元素

        Returns:
            bool: 如果应该跳过返回True
        """
        try:
            element_tag = element.tag.split('}')[-1] if '}' in element.tag else element.tag
            element_namespace = element.tag.split('}')[0][1:] if '}' in element.tag else ""

            # 跳过threat-intelligence命名空间的元素（这些不应该在security-zone容器中）
            if "threat-intelligence" in element_namespace:
                log(_("xml_template_integration.skip_threat_intelligence_element_in_security_zone",
                     element=element_tag), "debug")
                return True

            # 跳过其他明显不属于security-zone的元素
            invalid_elements_in_security_zone = ["management", "enable-status", "enable-ai"]
            if element_tag in invalid_elements_in_security_zone:
                log(_("xml_template_integration.skip_invalid_element_in_security_zone",
                     element=element_tag), "debug")
                return True

            # 对于auto元素，只有在非security-zone命名空间时才跳过
            if element_tag == "auto":
                if "security-zone" not in element_namespace and element_namespace != "":
                    log(_("xml_template_integration.skip_auto_element_wrong_namespace",
                         namespace=element_namespace), "debug")
                    return True

            return False

        except Exception as e:
            log(_("xml_template_integration.element_skip_check_failed", error=str(e)), "debug")
            return False

    def _find_or_create_child_element(self, parent: etree.Element, tag_name: str,
                                     namespace: str = None) -> etree.Element:
        """
        通用的子元素查找或创建方法

        Args:
            parent: 父元素
            tag_name: 子元素标签名
            namespace: 可选的命名空间URI

        Returns:
            找到或创建的子元素
        """
        # 先尝试查找现有元素
        existing_elem = self._find_child_element_robust(parent, tag_name, namespace)
        if existing_elem is not None:
            return existing_elem

        # 如果不存在，创建新元素
        if namespace:
            full_tag = f"{{{namespace}}}{tag_name}"
            new_elem = etree.SubElement(parent, full_tag)
        else:
            new_elem = etree.SubElement(parent, tag_name)

        return new_elem

    def _update_single_bandwidth_config(self, existing_physical: etree.Element,
                                       new_physical: etree.Element, bandwidth_type: str) -> None:
        """
        通用的单个带宽配置更新方法

        Args:
            existing_physical: 现有的物理接口节点
            new_physical: 新的物理接口节点
            bandwidth_type: 带宽类型 ("upload-bandwidth" 或 "download-bandwidth")
        """
        new_bandwidth = self._find_bandwidth_config(new_physical, bandwidth_type)
        if new_bandwidth is not None:
            log(_(f"xml_template_integration.found_new_{bandwidth_type.replace('-', '_')}"), "info")

            # 删除现有的带宽配置
            existing_bandwidth = self._find_bandwidth_config(existing_physical, bandwidth_type)
            if existing_bandwidth is not None:
                existing_physical.remove(existing_bandwidth)
                log(_(f"xml_template_integration.removed_existing_{bandwidth_type.replace('-', '_')}"), "info")

            # 添加新的带宽配置
            bandwidth_copy = copy.deepcopy(new_bandwidth)
            self._clean_element_namespace(bandwidth_copy)
            existing_physical.append(bandwidth_copy)
            log(_(f"xml_template_integration.added_new_{bandwidth_type.replace('-', '_')}"), "info")

    def _extract_ip_set_identifier(self, ip_set_elem: etree.Element) -> str:
        """
        通用的IP集合标识符提取方法

        Args:
            ip_set_elem: IP集合元素

        Returns:
            IP集合的唯一标识符，如果无法提取则返回None
        """
        # 新格式：优先使用ip-address
        ip_addr_elem = self._find_child_element_robust(ip_set_elem, "ip-address")
        if ip_addr_elem is not None:
            return ip_addr_elem.text

        # 旧格式：兼容处理
        ip_elem = self._find_child_element_robust(ip_set_elem, "ip")
        prefix_elem = self._find_child_element_robust(ip_set_elem, "prefix-length")
        start_ip_elem = self._find_child_element_robust(ip_set_elem, "start-ip")
        end_ip_elem = self._find_child_element_robust(ip_set_elem, "end-ip")

        if ip_elem is not None and prefix_elem is not None:
            return f"{ip_elem.text}/{prefix_elem.text}"
        elif start_ip_elem is not None and end_ip_elem is not None:
            return f"{start_ip_elem.text}-{end_ip_elem.text}"
        elif ip_elem is not None:
            return f"{ip_elem.text}/32"

        return None

    # ==================== 业务逻辑方法 ====================

    def validate_input(self, context: DataContext) -> bool:
        """
        验证输入数据
        
        Args:
            context: 数据上下文
            
        Returns:
            bool: 验证是否通过
        """
        # 检查是否有XML集成数据
        xml_integration_data = context.get_data("xml_integration_data")
        if not xml_integration_data:
            context.add_error(_("xml_template_integration_stage.no_integration_data"))
            return False
        
        # 检查模板参数
        model = context.get_data("model")
        version = context.get_data("version")
        if not model or not version:
            context.add_error(_("xml_template_integration_stage.missing_model_version"))
            return False
        
        return True
    
    def process(self, context: DataContext) -> bool:
        """
        处理XML模板集成 - 13阶段集成流程

        ⚠️ 核心业务逻辑 - 修改需谨慎 ⚠️

        执行顺序和依赖关系：
        1. 加载XML模板 (无依赖)
        2. 集成系统配置 (依赖: 操作模式检测结果)
        3. 集成接口配置 (依赖: 接口处理结果)
        4. 集成地址对象 (依赖: 地址处理结果)
        4.5. 集成地址对象组 (依赖: 地址组处理结果, 地址对象)
        5. 集成服务对象 (依赖: 服务处理结果)
        5.5. 集成服务对象组 (依赖: 服务组处理结果, 服务对象)
        6. 集成安全区域 (依赖: 区域处理结果, 接口配置)
        7. 集成时间对象 (依赖: 时间范围处理结果)
        8. 集成DNS配置 (依赖: DNS处理结果)
        9. 集成静态路由 (依赖: 静态路由处理结果)
        10. 集成安全策略 (依赖: 策略处理结果, 地址对象, 服务对象, 安全区域)
        11. 集成NAT规则 (依赖: NAT处理结果, 安全策略)
        11.5. 修复接口工作模式 (依赖: 操作模式结果)
        12. 验证和优化XML (依赖: 所有集成完成)
        13. 生成最终XML (依赖: XML验证通过)

        关键业务规则：
        - 每个阶段失败都会导致整个流程终止
        - 路由模式下需要修复所有接口为route工作模式
        - XML结构必须符合NTOS YANG模型规范

        Args:
            context: 数据上下文，包含所有处理阶段的结果数据

        Returns:
            bool: 处理是否成功，失败时错误信息会添加到context中
        """
        try:
            log(_("xml_template_integration_stage.start_processing"), "info")

            # 添加详细的数据流调试日志
            self._debug_data_context(context)

            # 阶段1：加载XML模板
            template_root = self._load_xml_template(context)
            if template_root is None:
                return False

            # 阶段2：集成系统配置（操作模式、主机名等）
            if not self._integrate_system_configuration(template_root, context):
                return False

            # 阶段3：集成接口配置
            if not self._integrate_interface_configuration(template_root, context):
                return False

            # 阶段4：集成地址对象
            if not self._integrate_address_objects(template_root, context):
                return False

            # 阶段4.5：集成地址对象组
            if not self._integrate_address_groups(template_root, context):
                return False

            # 阶段5：集成服务对象
            if not self._integrate_service_objects(template_root, context):
                return False

            # 阶段5.5：集成服务对象组
            if not self._integrate_service_groups(template_root, context):
                return False

            # 阶段6：集成安全区域
            if not self._integrate_security_zones(template_root, context):
                return False

            # 阶段7：集成时间对象
            if not self._integrate_time_ranges(template_root, context):
                return False

            # 阶段8：集成DNS配置
            if not self._integrate_dns_configuration(template_root, context):
                return False

            # 阶段9：集成静态路由
            if not self._integrate_static_routes(template_root, context):
                return False

            # 阶段10：集成安全策略
            if not self._integrate_security_policies(template_root, context):
                return False

            # 阶段11：集成NAT规则
            if not self._integrate_nat_rules(template_root, context):
                return False

            # 阶段11.5：修复接口工作模式（路由模式下所有接口都应该是route模式）
            operation_mode_result = context.get_data("operation_mode_result")
            if not operation_mode_result or operation_mode_result.get("mode") != "transparent":
                # 只在路由模式下执行批量修复
                if not self._fix_all_interface_working_modes(template_root, context):
                    log(_("xml_template_integration_stage.interface_working_mode_fix_failed"), "warning")

            # 阶段12：验证和优化XML
            if not self._validate_and_optimize_xml(template_root, context):
                return False

            # 阶段12.5：确保threat-intelligence结构完整性
            # 注意：FortiGate转换不涉及威胁情报功能，跳过此检查
            # if not self._ensure_threat_intelligence_integrity(template_root, context):
            #     log(_("xml_template_integration.threat_intelligence_integrity_failed"), "warning")
            #     # 不返回False，因为这不是致命错误

            # 阶段12.8：应用YANG验证修复
            if not self._apply_yang_validation_fixes(template_root, context):
                log(_("xml_template_integration_stage.yang_validation_fixes_failed"), "warning")
                # 不返回False，因为这不是致命错误，但会记录警告

            # 阶段13：生成最终XML
            if not self._generate_final_xml(template_root, context):
                return False
            
            log(_("xml_template_integration_stage.processing_completed"), "info")

            # 记录用户日志
            from engine.utils.user_log_formatter import record_stage_user_log
            xml_result = {
                "template_applied": True,
                "xml_generated": True,
                "statistics": {"total": 1, "converted": 1, "failed": 0, "skipped": 0}
            }
            record_stage_user_log("xml_template_integration", xml_result)

            return True

        except Exception as e:
            error_msg = _("xml_template_integration_stage.processing_exception", error=str(e))
            context.add_error(error_msg)
            log(error_msg, "error")
            return False

    def _apply_yang_validation_fixes(self, template_root: etree.Element, context: DataContext) -> bool:
        """
        应用YANG验证修复

        Args:
            template_root: 模板根元素
            context: 数据上下文

        Returns:
            bool: 修复是否成功
        """
        try:
            log(_("xml_template_integration_stage.applying_yang_validation_fixes"), "info")

            # 使用XML后处理器应用YANG验证修复
            from engine.utils.xml_post_processor import XmlPostProcessor
            xml_processor = XmlPostProcessor(name_mapping_manager=self.name_mapping_manager)

            # 应用修复
            fixed_root = xml_processor.fix_yang_validation_issues(template_root)

            # 更新引用关系
            self.name_mapping_manager.update_all_references(fixed_root)

            # 生成映射报告
            mapping_report = self.name_mapping_manager.generate_mapping_report()
            context.set_data("name_mapping_report", mapping_report)

            log(_("xml_template_integration_stage.yang_validation_fixes_applied"), "info")
            return True

        except Exception as e:
            error_msg = f"YANG验证修复异常: {str(e)}"
            log(error_msg, "warning")
            context.add_warning(error_msg)
            return False
    
    def _load_xml_template(self, context: DataContext) -> Optional[etree.Element]:
        """
        加载XML模板
        
        Args:
            context: 数据上下文
            
        Returns:
            Optional[etree.Element]: 模板根元素
        """
        try:
            model = context.get_data("model")
            version = context.get_data("version")

            log(_("xml_template_integration.start_loading_template", model=model, version=version), "info")

            # 使用模板管理器加载模板
            template_root = self.template_manager.get_template(model, version)

            if template_root is not None:
                # 详细的模板信息日志
                root_tag = template_root.tag
                local_tag = root_tag.split('}')[-1] if '}' in root_tag else root_tag
                namespace = template_root.get("xmlns")
                child_count = len(list(template_root))

                log(_("xml_template_integration.template_loaded_success", model=model, version=version), "info")

                context.set_data("template_root", template_root)
                return template_root
            else:
                error_msg = f"模板加载返回空值: model={model}, version={version}"
                context.add_error(error_msg)
                log(error_msg, "error")
                return None

        except Exception as e:
            error_msg = f"XML模板加载失败: model={model}, version={version}, 错误={str(e)}"
            context.add_error(error_msg)
            log(error_msg, "error")
            import traceback
            return None
    
    def _integrate_security_policies(self, template_root: etree.Element, context: DataContext) -> bool:
        """
        集成安全策略到XML模板

        ⚠️ 关键业务逻辑 - 安全策略是防火墙的核心功能 ⚠️

        依赖关系：
        - 必须在地址对象、服务对象、安全区域集成完成后执行
        - 依赖FortiGate策略转换阶段的处理结果

        处理流程：
        1. 获取策略处理结果和XML片段
        2. 验证策略引用的对象是否存在（地址、服务、区域）
        3. 解析XML片段并查找security-policy容器
        4. 将策略规则集成到模板的security-policy节点中
        5. 处理策略间的依赖关系和冲突

        关键业务规则：
        - 策略顺序必须保持（影响匹配优先级）
        - 引用的对象必须存在，否则策略无效
        - 支持both方向的策略（双向规则）
        - NAT策略和安全策略可能关联

        XML结构要求：
        - 策略必须放在VRF下的security-policy容器中
        - 每个策略包含源区域、目标区域、源地址、目标地址、服务等元素
        - 必须符合NTOS security-policy YANG模型规范

        Args:
            template_root: XML模板根元素
            context: 数据上下文，包含策略处理结果

        Returns:
            bool: 集成是否成功，失败时会在context中添加错误信息
        """
        try:
            policy_result = context.get_data("policy_processing_result")
            if not policy_result:
                log(_("xml_template_integration.no_policy_result"), "info")
                return True

            security_xml_fragment = policy_result.get("security_xml_fragment", "")
            if not security_xml_fragment:
                log(_("xml_template_integration.no_security_policy_fragment"), "info")
                return True

            # ✅ 重构：使用通用XML片段解析方法
            fragment_root = self._parse_xml_fragment_robust(security_xml_fragment, "安全策略")
            if fragment_root is None:
                return False

            # 处理命名空间问题 - 使用命名空间感知的查找
            security_policy_ns = "urn:ruijie:ntos:params:xml:ns:yang:security-policy"
            policy_elements = fragment_root.findall(f"{{{security_policy_ns}}}policy")
            if not policy_elements:
                # 如果命名空间查找失败，尝试无命名空间查找
                policy_elements = fragment_root.findall("policy")

            # 查找或创建VRF元素（使用统一的方法）
            vrf_elem = self._find_or_create_vrf_node(template_root)
            if vrf_elem is None:
                log(_("xml_template_integration.cannot_get_vrf_for_security"), "error")
                return False

            # 查找现有的security-policy元素
            existing_security_policy = vrf_elem.find(".//security-policy[@xmlns='urn:ruijie:ntos:params:xml:ns:yang:security-policy']")
            if existing_security_policy is not None:
                # 如果存在security-policy元素，合并policy子元素
                policies_added = 0
                for new_policy in policy_elements:
                    # 深拷贝策略元素以避免命名空间问题
                    policy_copy = copy.deepcopy(new_policy)
                    self._clean_element_namespaces(policy_copy)
                    # 清理policy元素的多余属性，确保符合YANG模型
                    self._clean_policy_element_attributes(policy_copy)
                    existing_security_policy.append(policy_copy)
                    policies_added += 1
            else:
                # 如果不存在security-policy元素，创建格式化的security-policy元素
                security_policy_elem = etree.SubElement(vrf_elem, "security-policy")
                security_policy_elem.set("xmlns", "urn:ruijie:ntos:params:xml:ns:yang:security-policy")

                # 逐个添加policy子元素，确保格式化
                policies_added = 0
                for new_policy in policy_elements:
                    policy_copy = copy.deepcopy(new_policy)
                    self._clean_element_namespaces(policy_copy)
                    # 清理policy元素的多余属性，确保符合YANG模型
                    self._clean_policy_element_attributes(policy_copy)
                    security_policy_elem.append(policy_copy)
                    policies_added += 1

            policies = policy_result.get("policies", {})
            security_policies_count = len(policies.get("security_policies", []))
            log(_("xml_template_integration.security_policy_integration_complete", count=security_policies_count), "info")

            return True

        except Exception as e:
            error_msg = f"安全策略集成异常: {str(e)}"
            context.add_error(error_msg)
            log(error_msg, "error")
            return False
    
    def _integrate_nat_rules(self, template_root: etree.Element, context: DataContext) -> bool:
        """
        集成NAT规则到XML模板
        
        Args:
            template_root: 模板根元素
            context: 数据上下文
            
        Returns:
            bool: 集成是否成功
        """
        try:
            policy_result = context.get_data("policy_processing_result")
            if not policy_result:
                log(_("xml_template_integration.no_policy_result"), "info")
                return True

            nat_xml_fragment = policy_result.get("nat_xml_fragment", "")
            if not nat_xml_fragment:
                log(_("xml_template_integration.no_nat_fragment"), "info")
                return True

            # ✅ 重构：使用通用XML片段解析方法
            fragment_root = self._parse_xml_fragment_robust(nat_xml_fragment, "NAT规则")
            if fragment_root is None:
                return False

            # 查找或创建VRF元素（使用统一的方法）
            vrf_elem = self._find_or_create_vrf_node(template_root)
            if vrf_elem is None:
                log(_("xml_template_integration.cannot_get_vrf_for_nat"), "error")
                return False

            # 查找现有的nat元素（不依赖命名空间属性，因为模板中的NAT元素可能没有命名空间属性）
            existing_nat = vrf_elem.find(".//nat")
            if existing_nat is None:
                # 尝试使用命名空间查找
                existing_nat = vrf_elem.find(".//{urn:ruijie:ntos:params:xml:ns:yang:nat}nat")

            if existing_nat is not None:
                # 如果存在nat元素，合并NAT规则子元素（避免重复）
                self._merge_nat_elements_safely(existing_nat, fragment_root)
            else:
                # 如果不存在nat元素，直接添加整个nat元素
                vrf_elem.append(fragment_root)

            policies = policy_result.get("policies", {})
            nat_rules_count = len(policies.get("nat_rules", []))
            log(_("xml_template_integration.nat_integration_complete", count=nat_rules_count), "info")

            # 如果启用了twice-nat44，进行额外处理
            if context.get_config("nat.use_twice_nat44", True):
                twice_nat44_success = self._integrate_twice_nat44_rules(template_root, context)
                if not twice_nat44_success:
                    log(_("xml_template_integration.twice_nat44_integration_failed"), "warning")

            return True

        except Exception as e:
            error_msg = f"NAT规则集成异常: {str(e)}"
            context.add_error(error_msg)
            log(error_msg, "error")
            return False

    def _merge_nat_elements_safely(self, existing_nat: etree.Element, fragment_root: etree.Element) -> None:
        """
        安全地合并NAT元素，避免重复

        Args:
            existing_nat: 现有的NAT元素
            fragment_root: 要合并的NAT片段根元素
        """
        try:
            # 获取现有的pool和rule名称
            existing_pools = set()
            existing_rules = set()

            for child in existing_nat:
                child_tag = child.tag.split('}')[-1] if '}' in child.tag else child.tag
                if child_tag == "pool":
                    name_elem = child.find("name")
                    if name_elem is not None and name_elem.text:
                        existing_pools.add(name_elem.text)
                elif child_tag == "rule":
                    name_elem = child.find("name")
                    if name_elem is not None and name_elem.text:
                        existing_rules.add(name_elem.text)

            # 只添加不重复的元素
            added_pools = 0
            added_rules = 0
            skipped_pools = 0
            skipped_rules = 0

            for child in fragment_root:
                child_tag = child.tag.split('}')[-1] if '}' in child.tag else child.tag
                if child_tag == "pool":
                    name_elem = child.find("name")
                    if name_elem is not None and name_elem.text:
                        pool_name = name_elem.text
                        if pool_name not in existing_pools:
                            existing_nat.append(child)
                            existing_pools.add(pool_name)
                            added_pools += 1
                        else:
                            skipped_pools += 1
                            log(_("xml_template_integration.duplicate_pool_skipped", name=pool_name), "debug")
                elif child_tag == "rule":
                    name_elem = child.find("name")
                    if name_elem is not None and name_elem.text:
                        rule_name = name_elem.text
                        if rule_name not in existing_rules:
                            existing_nat.append(child)
                            existing_rules.add(rule_name)
                            added_rules += 1
                        else:
                            skipped_rules += 1
                            log(_("xml_template_integration.duplicate_rule_skipped", name=rule_name), "debug")
                else:
                    # 其他元素直接添加（如enable等）
                    existing_nat.append(child)

            log(_("xml_template_integration.nat_merge_summary",
                  added_pools=added_pools, skipped_pools=skipped_pools,
                  added_rules=added_rules, skipped_rules=skipped_rules), "info")

        except Exception as e:
            log(_("xml_template_integration.nat_merge_error", error=str(e)), "error")
            # 如果合并失败，回退到简单添加（但记录警告）
            for child in fragment_root:
                existing_nat.append(child)

    @twice_nat44_error_handler(
        operation="integrate_twice_nat44_rules",
        max_retries=2,
        handle_exceptions=(etree.XMLSyntaxError, KeyError, AttributeError),
        fallback_value=False,
        log_errors=True,
        attempt_recovery=True
    )
    @twice_nat44_performance_monitor(
        operation="integrate_twice_nat44_rules",
        log_performance=True,
        performance_threshold=1.0
    )
    def _integrate_twice_nat44_rules(self, template_root: etree.Element, context: DataContext) -> bool:
        """
        ✅ 基于重构成果：集成twice-nat44规则到XML模板

        Args:
            template_root: 模板根元素
            context: 数据上下文

        Returns:
            bool: 集成是否成功

        Note:
            利用已重构的通用方法：
            - _parse_xml_fragment_robust()
            - _find_or_create_vrf_node()
            - _find_child_element_robust()
        """
        try:
            policy_result = context.get_data("policy_processing_result")
            if not policy_result:
                log(_("xml_template_integration.no_policy_result"), "info")
                return True

            nat_xml_fragment = policy_result.get("nat_xml_fragment", "")
            if not nat_xml_fragment:
                log(_("xml_template_integration.no_nat_fragment"), "info")
                return True

            # ✅ 使用已重构的通用XML片段解析方法
            fragment_root = self._parse_xml_fragment_robust(nat_xml_fragment, "twice-nat44规则")
            if fragment_root is None:
                return False

            # ✅ 使用已重构的VRF查找方法
            vrf_elem = self._find_or_create_vrf_node(template_root)
            if vrf_elem is None:
                log(_("xml_template_integration.cannot_get_vrf_for_nat"), "error")
                return False

            # ✅ 使用通用查找方法查找NAT元素
            existing_nat = self._find_child_element_robust(
                vrf_elem, "nat",
                namespace="urn:ruijie:ntos:params:xml:ns:yang:nat"
            )

            if existing_nat is None:
                existing_nat = etree.SubElement(vrf_elem, "nat")
                existing_nat.set("xmlns", "urn:ruijie:ntos:params:xml:ns:yang:nat")

            # 统计twice-nat44规则
            twice_nat44_count = 0
            for rule in fragment_root:
                if self._is_twice_nat44_rule(rule):
                    twice_nat44_count += 1
                existing_nat.append(rule)

            if twice_nat44_count > 0:
                log(_("xml_template_integration.twice_nat44_integrated", count=twice_nat44_count), "info")

            return True

        except Exception as e:
            log(_("xml_template_integration.twice_nat44_integration_failed", error=str(e)), "error")
            return False

    def _is_twice_nat44_rule(self, rule_element: etree.Element) -> bool:
        """
        ✅ 使用通用查找方法检查是否为twice-nat44规则

        Args:
            rule_element: 规则XML元素

        Returns:
            bool: 是否为twice-nat44规则
        """
        twice_nat_elem = self._find_child_element_robust(rule_element, "twice-nat44")
        return twice_nat_elem is not None
    
    def _validate_and_optimize_xml(self, template_root: etree.Element, context: DataContext) -> bool:
        """
        验证和优化XML结构
        
        Args:
            template_root: 模板根元素
            context: 数据上下文
            
        Returns:
            bool: 验证是否通过
        """
        try:
            # 验证XML结构
            if not self._validate_xml_structure(template_root, context):
                return False
            
            # 优化XML（移除重复节点、排序等）
            self._optimize_xml_structure(template_root, context)
            
            log(_("xml_template_integration_stage.xml_validated_optimized"), "info")
            return True
            
        except Exception as e:
            error_msg = _("xml_template_integration_stage.xml_validation_exception", error=str(e))
            context.add_error(error_msg)
            log(error_msg, "error")
            return False
    
    def _validate_xml_structure(self, template_root: etree.Element, context: DataContext) -> bool:
        """
        验证XML结构

        Args:
            template_root: 模板根元素
            context: 数据上下文

        Returns:
            bool: 结构是否有效
        """
        # 检查根元素 - 更宽容的验证（修复版本）
        root_tag = template_root.tag
        # 移除命名空间前缀，只检查本地名称
        local_tag = root_tag.split('}')[-1] if '}' in root_tag else root_tag



        if local_tag != "config":
            # 只记录警告，不阻止处理（借鉴旧架构的宽容策略）
            warning_msg = f"XML根元素不是'config': 实际='{local_tag}', 期望='config'"
            context.add_warning(warning_msg)
            log(warning_msg, "warning")
        else:
            pass

        # 检查命名空间（增强版本，支持多种命名空间检查方式）
        expected_namespace = "urn:ruijie:ntos"

        # 方法1：检查xmlns属性
        namespace = template_root.get("xmlns")

        # 方法2：检查标签中的命名空间
        tag_namespace = None
        if template_root.tag.startswith("{"):
            tag_namespace = template_root.tag.split("}")[0][1:]

        # 方法3：检查nsmap
        nsmap_namespace = None
        if hasattr(template_root, 'nsmap') and template_root.nsmap:
            nsmap_namespace = template_root.nsmap.get(None)  # 默认命名空间



        # 判断命名空间是否正确（任一方法匹配即可）
        namespace_correct = (
            namespace == expected_namespace or
            tag_namespace == expected_namespace or
            nsmap_namespace == expected_namespace
        )

        if not namespace_correct:
            warning_msg = f"XML命名空间不匹配: xmlns='{namespace}', tag_ns='{tag_namespace}', nsmap_ns='{nsmap_namespace}', 期望='{expected_namespace}'"
            context.add_warning(warning_msg)
            log(warning_msg, "warning")
        else:
            pass

        # ✅ 第三阶段重构：检查基本结构（使用通用VRF查找方法）
        vrf_element = self._find_element_robust(template_root, "vrf", namespace=expected_namespace)

        if vrf_element is None:
            warning_msg = "XML模板中未找到VRF元素"
            context.add_warning(warning_msg)
            log(warning_msg, "warning")
        else:
            pass

        # 检查命名空间 - 只记录警告
        expected_namespace = "urn:ruijie:ntos"
        actual_namespace = template_root.get("xmlns")
        if actual_namespace != expected_namespace:
            context.add_warning(_("xml_template_integration_stage.unexpected_namespace"))
            log(_("xml_template_integration_stage.namespace_warning"), "warning",
                actual=actual_namespace, expected=expected_namespace)

        # 检查是否有重复的节点 - 只记录警告
        duplicate_nodes = self._find_duplicate_nodes(template_root)
        if duplicate_nodes:
            context.add_warning(_("xml_template_integration_stage.duplicate_nodes_found",
                                nodes=", ".join(duplicate_nodes)))
            log(_("xml_template_integration_stage.duplicate_nodes_warning"), "warning",
                nodes=", ".join(duplicate_nodes))
        
        return True
    
    def _find_duplicate_nodes(self, root: etree.Element) -> List[str]:
        """
        查找重复的节点
        
        Args:
            root: 根元素
            
        Returns: List[str]: 重复节点列表
        """
        node_counts = {}
        duplicates = []
        
        for elem in root.iter():
            if elem.tag not in node_counts:
                node_counts[elem.tag] = 0
            node_counts[elem.tag] += 1
        
        for tag, count in node_counts.items():
            if count > 1 and tag not in ["policy", "rule", "interface"]:  # 这些节点允许重复
                duplicates.append(f"{tag}({count})")
        
        return duplicates

    def _remove_duplicate_nodes(self, root: etree.Element) -> int:
        """
        移除重复的容器节点，保留第一个实例

        Args:
            root: 根元素

        Returns:
            int: 移除的重复节点数量
        """
        removed_count = 0

        # 需要检查的容器节点类型
        container_nodes = ["security-zone", "service-obj", "network-obj", "time-range"]

        for container_type in container_nodes:
            # 查找所有该类型的节点（使用多种方法确保可靠性）
            nodes = []

            # 方法1：直接查找
            direct_nodes = root.findall(f".//{container_type}")
            nodes.extend(direct_nodes)

            # 方法2：遍历查找（避免命名空间问题）
            for elem in root.iter():
                if elem.tag.endswith(container_type) and elem not in nodes:
                    nodes.append(elem)

            # 特殊处理time-range节点：区分全局time-range容器和NAT规则中的time-range引用
            if container_type == "time-range" and len(nodes) > 1:
                # 分离全局time-range容器和NAT规则中的time-range引用
                global_time_range_nodes = []
                nat_time_range_nodes = []

                for node in nodes:
                    if self._is_nat_time_range_reference(node):
                        nat_time_range_nodes.append(node)
                    else:
                        global_time_range_nodes.append(node)

                # log(f"发现 {len(global_time_range_nodes)} 个全局time-range容器和 {len(nat_time_range_nodes)} 个NAT time-range引用", "info")

                # 只对全局time-range容器进行重复检测和合并
                if len(global_time_range_nodes) > 1:
                    # log(f"发现 {len(global_time_range_nodes)} 个重复的全局time-range容器，进行合并", "warning")
                    nodes = global_time_range_nodes
                else:
                    # 没有重复的全局time-range容器，跳过处理
                    continue

            if len(nodes) > 1:
                log(_("xml_template_integration.duplicate_container_nodes_found", count=len(nodes), type=container_type), "warning")

                # 保留第一个节点，移除其他重复节点
                first_node = nodes[0]

                for duplicate_node in nodes[1:]:
                    # 将重复节点的内容合并到第一个节点中
                    self._merge_container_content(first_node, duplicate_node, container_type)

                    # 移除重复节点
                    parent = duplicate_node.getparent()
                    if parent is not None:
                        parent.remove(duplicate_node)
                        removed_count += 1


        return removed_count

    def _merge_container_content(self, target_node: etree.Element, source_node: etree.Element, container_type: str) -> None:
        """
        将源容器节点的内容合并到目标容器节点中

        Args:
            target_node: 目标容器节点
            source_node: 源容器节点
            container_type: 容器类型
        """
        try:
            if container_type == "security-zone":
                # ✅ 第三阶段重构：使用通用合并方法合并zone元素
                if not self._merge_xml_elements(source_node, target_node, merge_strategy="merge"):
                    log(_("xml_template_integration.security_zone_merge_failed"), "warning")


            elif container_type == "service-obj":
                # ✅ 第三阶段重构：使用通用合并方法合并service-set和service-group元素
                if not self._merge_xml_elements(source_node, target_node, merge_strategy="merge"):
                    log(_("xml_template_integration.service_obj_merge_failed"), "warning")


            elif container_type == "time-range":
                # 检查time-range节点的上下文，区分全局time-range容器和NAT规则中的time-range引用
                is_nat_time_range = self._is_nat_time_range_reference(target_node)

                if is_nat_time_range:
                    # NAT规则中的time-range引用，不应该合并range子元素
                    return

                # 全局time-range容器，正常合并range元素
                # 定义命名空间映射
                namespaces = {
                    'tr': 'urn:ruijie:ntos:params:xml:ns:yang:time-range'
                }

                # 合并range元素（使用命名空间前缀）
                source_ranges = source_node.findall(".//tr:range", namespaces)

                for range_elem in source_ranges:
                    range_name = range_elem.find("tr:name", namespaces)
                    if range_name is not None:
                        # 检查目标节点中是否已存在同名range
                        existing_range = None
                        for existing in target_node.findall(".//tr:range", namespaces):
                            existing_name = existing.find("tr:name", namespaces)
                            if existing_name is not None and existing_name.text == range_name.text:
                                existing_range = existing
                                break

                        if existing_range is None:
                            target_node.append(range_elem)
                        else:
                            # 替换现有的range
                            parent = existing_range.getparent()
                            parent.remove(existing_range)
                            parent.append(range_elem)

                target_ranges = target_node.findall(".//tr:range", namespaces)

        except Exception as e:
            log(f"合并容器内容失败: {str(e)}", "error")

    def _is_nat_time_range_reference(self, time_range_node: etree.Element) -> bool:
        """
        检测time-range节点是否是引用类型（NAT规则或security-policy中的time-range引用）

        基于YANG模型的区别：
        1. 全局time-range容器：包含<range>子元素，命名空间为time-range
        2. NAT规则time-range引用：包含<value>子元素，在NAT match条件中
        3. security-policy中time-range引用：leaf节点，只包含字符串值

        Args:
            time_range_node: time-range XML元素

        Returns:
            bool: 如果是引用类型的time-range返回True，否则返回False
        """
        try:
            # 方法1：检查节点的命名空间
            # NAT规则中的time-range节点应该在NAT命名空间下
            if "urn:ruijie:ntos:params:xml:ns:yang:nat" in str(time_range_node.tag):
                return True

            # 方法2：检查父节点路径 - NAT规则
            # NAT规则中的time-range应该在 rule/static-*nat44/match 路径下
            current = time_range_node
            while current is not None:
                parent = current.getparent()
                if parent is None:
                    break

                # 检查是否在NAT规则的match条件中
                if (parent.tag == "match" and
                    parent.getparent() is not None and
                    parent.getparent().tag in ["static-snat44", "static-dnat44", "dynamic-snat44"]):
                    return True

                # 检查是否在security-policy中
                if (parent.tag == "policy" and
                    parent.getparent() is not None and
                    parent.getparent().tag == "security-policy"):
                    return True

                current = parent

            # 方法3：检查节点内容结构
            # NAT规则中的time-range应该包含<value>子元素，而不是<range>子元素
            has_value = time_range_node.find("value") is not None
            has_range = time_range_node.find("range") is not None or time_range_node.find(".//*[local-name()='range']") is not None

            if has_value and not has_range:
                return True

            # 方法4：检查是否是security-policy中的time-range leaf节点
            # security-policy中的time-range应该只包含文本内容，不包含子元素
            if (not has_range and
                time_range_node.text is not None and
                time_range_node.text.strip() != "" and
                len(list(time_range_node)) == 0):
                # 进一步检查父节点上下文
                parent = time_range_node.getparent()
                if (parent is not None and
                    parent.tag == "policy" and
                    parent.getparent() is not None and
                    "security-policy" in str(parent.getparent().tag)):
                    return True

            return False

        except Exception as e:
            return False

    def _find_service_obj_container(self, vrf_elem: etree.Element) -> Optional[etree.Element]:
        """
        查找service-obj容器，使用多种方法确保可靠性

        ✅ 已重构：使用通用XML查找方法

        Args:
            vrf_elem: VRF元素

        Returns:
            Optional[etree.Element]: service-obj容器元素，如果不存在则返回None
        """
        # 使用通用查找方法
        return self._find_element_robust(
            vrf_elem,
            "service-obj",
            namespace="urn:ruijie:ntos:params:xml:ns:yang:service-obj"
        )

    def _find_security_zone_container(self, vrf_elem: etree.Element) -> Optional[etree.Element]:
        """
        查找security-zone容器，使用多种方法确保可靠性

        Args:
            vrf_elem: VRF元素

        Returns:
            Optional[etree.Element]: security-zone容器元素，如果不存在则返回None
        """
        # ✅ 重构：使用通用查找方法
        return self._find_element_robust(
            vrf_elem,
            "security-zone",
            namespace="urn:ruijie:ntos:params:xml:ns:yang:security-zone"
        )

    def _optimize_xml_structure(self, template_root: etree.Element, context: DataContext):
        """
        优化XML结构

        Args:
            template_root: 模板根元素
            context: 数据上下文
        """
        # 移除重复的容器节点
        removed_count = self._remove_duplicate_nodes(template_root)
        if removed_count > 0:
            log(_("xml_template_integration.xml_optimization_removed_duplicates", count=removed_count), "info")

        # 移除空的文本节点
        for elem in template_root.iter():
            if elem.text and elem.text.strip() == "":
                elem.text = None
            if elem.tail and elem.tail.strip() == "":
                elem.tail = None

        # 记录优化统计
        context.set_data("xml_optimization_stats", {
            "total_elements": len(list(template_root.iter())),
            "removed_duplicates": removed_count,
            "optimized": True
        })

    def _ensure_threat_intelligence_integrity(self, template_root: etree.Element, context: DataContext) -> bool:
        """
        确保threat-intelligence结构完整性

        Args:
            template_root: 模板根元素
            context: 数据上下文

        Returns:
            bool: 检查和修复是否成功
        """
        try:
            log(_("xml_template_integration.checking_threat_intelligence_integrity"), "debug")

            # 查找threat-intelligence元素
            threat_intel_elem = None
            for elem in template_root.iter():
                if elem.tag.endswith('threat-intelligence'):
                    threat_intel_elem = elem
                    break

            if threat_intel_elem is None:
                log(_("xml_template_integration.threat_intelligence_not_found"), "warning")
                return True  # 不存在就不需要修复

            # 检查management子元素
            management_elem = None
            for child in threat_intel_elem:
                child_tag = child.tag.split('}')[-1] if '}' in child.tag else child.tag
                if child_tag == 'management':
                    management_elem = child
                    break

            if management_elem is None:
                log(_("xml_template_integration.threat_intelligence_missing_management"), "warning")
                return False

            # 检查security-zone子元素
            security_zone_elem = None
            for child in management_elem:
                child_tag = child.tag.split('}')[-1] if '}' in child.tag else child.tag
                if child_tag == 'security-zone':
                    security_zone_elem = child
                    break

            if security_zone_elem is None:
                log(_("xml_template_integration.threat_intelligence_missing_security_zone"), "warning")
                # 尝试修复：添加缺失的security-zone/auto结构
                return self._repair_threat_intelligence_structure(management_elem)

            # 检查auto子元素
            auto_elem = None
            for child in security_zone_elem:
                child_tag = child.tag.split('}')[-1] if '}' in child.tag else child.tag
                if child_tag == 'auto':
                    auto_elem = child
                    break

            if auto_elem is None:
                log(_("xml_template_integration.threat_intelligence_missing_auto"), "warning")
                # 尝试修复：添加缺失的auto元素
                auto_elem = etree.SubElement(security_zone_elem, "auto")
                auto_elem.text = "true"
                log(_("xml_template_integration.threat_intelligence_auto_repaired"), "info")

            log(_("xml_template_integration.threat_intelligence_integrity_verified"), "debug")
            return True

        except Exception as e:
            log(_("xml_template_integration.threat_intelligence_integrity_check_failed", error=str(e)), "error")
            return False

    def _repair_threat_intelligence_structure(self, management_elem: etree.Element) -> bool:
        """
        修复threat-intelligence结构

        Args:
            management_elem: management元素

        Returns:
            bool: 修复是否成功
        """
        try:
            log(_("xml_template_integration.repairing_threat_intelligence_structure"), "info")

            # 添加security-zone元素
            security_zone_elem = etree.SubElement(management_elem, "security-zone")

            # 添加auto元素
            auto_elem = etree.SubElement(security_zone_elem, "auto")
            auto_elem.text = "true"

            log(_("xml_template_integration.threat_intelligence_structure_repaired"), "info")
            return True

        except Exception as e:
            log(_("xml_template_integration.threat_intelligence_repair_failed", error=str(e)), "error")
            return False

    def _generate_final_xml(self, template_root: etree.Element, context: DataContext) -> bool:
        """
        生成最终XML

        Args:
            template_root: 模板根元素
            context: 数据上下文

        Returns:
            bool: 生成是否成功
        """
        try:
            # 预检查XML结构

            # 检查根元素是否有效
            if template_root is None:
                error_msg = _("xml_template_integration_stage.xml_generation_null_root")
                context.add_error(error_msg)
                log(error_msg, "error")
                return False

            # 统计元素数量
            element_count = len(list(template_root.iter()))

            # 尝试生成XML字符串 - 分步骤进行
            try:
                # 第一步：尝试简单的tostring
                xml_string = etree.tostring(template_root, encoding='unicode')


                # 第二步：尝试带格式化的tostring
                xml_string = etree.tostring(
                    template_root,
                    encoding='unicode',
                    pretty_print=True
                )

                # 第二点五步：应用额外的XML格式化修复
                xml_string = self._apply_xml_formatting_fixes(xml_string)



                # 第三步：确保不包含XML声明（按照用户要求）
                if xml_string.startswith('<?xml'):
                    # 移除XML声明行
                    lines = xml_string.split('\n')
                    if lines[0].startswith('<?xml'):
                        xml_string = '\n'.join(lines[1:])

            except Exception as tostring_error:
                # 如果格式化失败，尝试基本的tostring
                log(_("xml_template_integration_stage.xml_formatted_generation_failed"), "warning",
                    error=str(tostring_error))
                try:
                    xml_string = etree.tostring(template_root, encoding='unicode')
                    log(_("xml_template_integration_stage.xml_fallback_generation_success"), "info")
                except Exception as basic_error:
                    error_msg = _("xml_template_integration_stage.xml_basic_generation_failed",
                                error=str(basic_error))
                    context.add_error(error_msg)
                    log(error_msg, "error")
                    return False

            # 存储生成的XML
            context.set_data("generated_xml", xml_string)
            context.set_data("xml_generation_completed", True)

            # 记录XML统计信息
            xml_stats = {
                "xml_length": len(xml_string),
                "element_count": element_count,
                "has_security_policies": "<security-policy" in xml_string,
                "has_nat_rules": "<nat" in xml_string,
                "has_xml_declaration": xml_string.startswith('<?xml')
            }
            context.set_data("xml_stats", xml_stats)

            log(_("xml_template_integration_stage.xml_generated"), "info",
                length=xml_stats["xml_length"], elements=xml_stats["element_count"])

            # 备用文件保存机制：在XML生成完成后立即保存一份备份
            self._save_backup_xml_file(xml_string, context)

            return True

        except Exception as e:
            error_msg = _("xml_template_integration_stage.xml_generation_failed", error=str(e))
            context.add_error(error_msg)
            log(error_msg, "error")

            # 添加调试信息
            try:
                if template_root is not None:
                    pass
            except:
                pass

            return False

    def _save_backup_xml_file(self, xml_content: str, context: DataContext) -> None:
        """
        保存备用XML文件，确保即使后续处理失败也能找到生成的XML

        Args:
            xml_content: XML内容
            context: 处理上下文
        """
        try:
            from engine.utils.file_path_utils import FilePathUtils

            # 获取原始输出文件路径
            original_output = context.get_data("output_file")
            if not original_output:
                # 如果没有指定输出文件，使用默认路径
                original_output = "output/fortigate-conversion-result.xml"

            # 创建备份文件路径
            base_dir = os.path.dirname(original_output) or "output"
            base_name = os.path.splitext(os.path.basename(original_output))[0]
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"{base_name}_backup_{timestamp}.xml"
            backup_path = os.path.join(base_dir, backup_filename)

            # 确保目录存在
            os.makedirs(base_dir, exist_ok=True)

            # 保存备份文件
            write_result = FilePathUtils.safe_write_file(
                file_path=backup_path,
                content=xml_content,
                encoding='utf-8',
                backup=False  # 备份文件本身不需要再备份
            )

            if write_result['success']:
                log(_("xml_template_integration_stage.backup_xml_saved"), "info",
                    file=write_result['file_path'], size=write_result['bytes_written'])

                # 将备份文件路径存储到上下文中
                context.set_data("backup_xml_file", write_result['file_path'])

                # 同时尝试保存到原始路径（如果可能）
                if original_output != backup_path:
                    original_write_result = FilePathUtils.safe_write_file(
                        file_path=original_output,
                        content=xml_content,
                        encoding='utf-8',
                        backup=True
                    )

                    if original_write_result['success']:
                        log(_("xml_template_integration_stage.original_xml_saved"), "info",
                            file=original_write_result['file_path'])
                        context.set_data("original_xml_file", original_write_result['file_path'])
                    else:
                        log(_("xml_template_integration_stage.original_xml_save_failed"), "warning",
                            file=original_output, error=original_write_result['error'])
            else:
                log(_("xml_template_integration_stage.backup_xml_save_failed"), "warning",
                    file=backup_path, error=write_result['error'])

        except Exception as e:
            log(_("xml_template_integration_stage.backup_save_error"), "warning",
                error=str(e))

    def _filter_template_interfaces(self, template_root: etree.Element, context: DataContext) -> None:
        """
        过滤模板中与接口处理阶段重复的接口，避免重复实例

        Args:
            template_root: XML模板根元素
            context: 数据上下文
        """
        try:
            log(_("xml_template_integration.start_template_interface_conflict_detection"), "info")

            # 获取接口处理结果
            interface_result = context.get_data("interface_processing_result")
            if not interface_result:
                log(_("xml_template_integration.no_interface_result_skip_filtering"), "info")
                return

            # 获取接口处理阶段生成的接口列表
            converted_interfaces = interface_result.get("converted", {})
            if not converted_interfaces:
                log(_("xml_template_integration.no_converted_interfaces_skip_filtering"), "info")
                return

            # 提取已生成接口的NTOS名称
            generated_interface_names = set()
            for intf_name, intf_config in converted_interfaces.items():
                if isinstance(intf_config, dict):
                    ntos_name = intf_config.get("name")
                    if ntos_name:
                        generated_interface_names.add(ntos_name)

            log(_("xml_template_integration.interface_processing_generated_interfaces", interfaces=generated_interface_names), "info")

            # 查找模板中的接口节点
            vrf_elem = self._find_or_create_vrf_node(template_root)
            if vrf_elem is None:
                log(_("xml_template_integration.cannot_find_or_create_vrf_skip_filtering"), "error")
                return

            interface_elem = vrf_elem.find(".//interface")

            if interface_elem is None:
                log(_("xml_template_integration.no_interface_node_in_template"), "info")
                return

            # 过滤与已生成接口重复的物理接口
            physical_interfaces = interface_elem.findall(".//physical")
            log(_("xml_template_integration.template_found_physical_interfaces", count=len(physical_interfaces)), "info")

            removed_count = 0

            for physical in physical_interfaces:
                name_elem = physical.find("name")
                if name_elem is not None:
                    interface_name = name_elem.text

                    # 检查接口是否与已生成的接口重复
                    if interface_name in generated_interface_names:
                        interface_elem.remove(physical)
                        removed_count += 1
                        log(_("xml_template_integration.remove_duplicate_interface_from_template", interface=interface_name), "info")

            log(_("xml_template_integration.template_interface_conflict_filtering_complete", count=removed_count), "info")

        except Exception as e:
            log(_("xml_template_integration.template_interface_filtering_failed", error=str(e)), "error")
            import traceback
            log(_("xml_template_integration.detailed_error_info", error=traceback.format_exc()), "error")

    def _integrate_system_configuration(self, template_root: etree.Element, context: DataContext) -> bool:
        """
        集成系统配置到XML模板

        Args:
            template_root: 模板根元素
            context: 数据上下文

        Returns:
            bool: 集成是否成功
        """
        try:
            operation_mode_result = context.get_data("operation_mode_result")
            if not operation_mode_result:
                log(_("xml_template_integration_stage.no_operation_mode_result"), "info")
                return True

            # 集成主机名
            hostname = operation_mode_result.get("hostname", "FortiGate")
            # 这里可以添加具体的XML集成逻辑

            log(_("xml_template_integration_stage.system_configuration_integrated"), "info",
                hostname=hostname, mode=operation_mode_result.get("mode", "unknown"))

            return True

        except Exception as e:
            error_msg = _("xml_template_integration_stage.system_configuration_integration_exception", error=str(e))
            context.add_error(error_msg)
            log(error_msg, "error")
            return False

    def _integrate_interface_configuration(self, template_root: etree.Element, context: DataContext) -> bool:
        """
        集成接口配置到XML模板

        ⚠️ 复杂业务逻辑 - 涉及物理接口、VLAN接口、Bridge配置 ⚠️

        依赖关系：
        - 依赖接口处理阶段的结果数据
        - 依赖操作模式检测结果（路由模式 vs 透明模式）
        - 必须在系统配置集成之后执行

        处理流程：
        1. 获取接口处理结果和XML片段
        2. 过滤模板中与接口处理阶段重复的接口
        3. 解析XML片段并集成物理接口配置
        4. 处理Bridge接口配置（透明模式特有）
        5. 更新现有物理接口的配置（IPv4、访问控制等）
        6. 更新VLAN接口的访问控制和IPv4配置

        关键业务规则：
        - 路由模式：所有接口工作模式为route，不使用bridge
        - 透明模式：管理接口为route，其他接口为bridge模式
        - 物理接口映射：FortiGate接口名映射到NTOS接口名
        - VLAN接口：必须有对应的物理父接口
        - IPv4配置：支持静态IP、DHCP、PPPoE三种模式

        XML结构要求：
        - 接口配置必须放在interfaces容器中
        - 物理接口和VLAN接口有不同的XML结构
        - Bridge配置有独立的bridge容器
        - 必须符合NTOS interface YANG模型规范

        Args:
            template_root: XML模板根元素
            context: 数据上下文，包含接口处理结果

        Returns:
            bool: 集成是否成功，失败时会在context中添加错误信息
        """
        try:
            interface_result = context.get_data("interface_processing_result")
            operation_mode_result = context.get_data("operation_mode_result")

            if not interface_result:
                log(_("xml_template_integration_stage.no_interface_result"), "info")
                return True

            # 集成接口工作模式（透明模式支持）
            from engine.generators.xml_integrator import XMLIntegrator
            integrator = XMLIntegrator(name_mapping_manager=self.name_mapping_manager)

            # 集成接口工作模式
            if not integrator.integrate_interface_working_modes(template_root, interface_result):
                log(_("xml_template_integration_stage.interface_working_mode_integration_failed"), "warning")

            # 集成桥接接口（透明模式）
            if operation_mode_result and operation_mode_result.get("mode") == "transparent":
                if not integrator.integrate_bridge_interfaces(template_root, operation_mode_result):
                    log(_("xml_template_integration_stage.bridge_interface_integration_failed"), "warning")
                else:
                    log(_("xml_template_integration_stage.bridge_interface_integrated"), "info")

            # 处理模板中现有的bridge节点（路由模式和透明模式）
            if not self._integrate_bridge_configuration(template_root, context):
                log(_("xml_template_integration_stage.bridge_configuration_integration_failed"), "warning")

            # 获取XML片段
            xml_fragment = interface_result.get("xml_fragment", "")
            interfaces = interface_result.get("interfaces", {})
            zones = interface_result.get("zones", {})



            if not xml_fragment and not interfaces and not zones:
                log(_("xml_template_integration_stage.no_interface_data_to_integrate"), "info")
                return True

            # 使用统一的VRF查找方法，避免重复创建
            vrf_elem = self._find_or_create_vrf_node(template_root)

            # ✅ 第三阶段重构：集成接口XML片段（优先使用XML片段）
            integrated_interfaces = 0
            if xml_fragment:
                try:
                    log(_("xml_template_integration.start_integrating_interface_fragment"), "info")
                    # 使用通用XML片段解析方法
                    fragment_root = self._parse_xml_fragment_robust(xml_fragment, "接口配置")
                    if fragment_root is None:
                        return False

                    # 使用通用查找方法查找interface节点
                    existing_interface = self._find_element_robust(
                        vrf_elem, "interface",
                        namespace="urn:ruijie:ntos:params:xml:ns:yang:interface"
                    )

                    log(_("xml_template_integration.find_existing_interface_node_result", found=existing_interface is not None), "info")
                    if existing_interface is not None:
                        log(_("xml_template_integration.found_interface_node_details", tag=existing_interface.tag, xmlns=existing_interface.get("xmlns")), "info")

                    if existing_interface is None:
                        # 模板中不存在，使用通用片段集成方法
                        if self._integrate_xml_fragment_to_container(
                            fragment_root, vrf_elem,
                            namespace="urn:ruijie:ntos:params:xml:ns:yang:interface"
                        ):
                            integrated_interfaces = len(fragment_root.findall(".//vlan")) + len(fragment_root.findall(".//physical"))
                            log(_("xml_template_integration.insert_new_interface_node", count=integrated_interfaces), "info")
                        else:
                            return False
                    else:
                        # 模板中存在，使用通用合并方法
                        log(_("xml_template_integration.found_existing_interface_node_start_merge"), "info")
                        if not self._merge_xml_elements(fragment_root, existing_interface, merge_strategy="merge"):
                            return False

                        # 合并VLAN接口（使用多种方法确保可靠性）
                        vlan_elements = []

                        # 方法1：直接查找
                        vlan_elements.extend(fragment_root.findall(".//vlan"))

                        # 方法2：使用VLAN命名空间查找
                        try:
                            vlan_ns_elements = fragment_root.findall(".//{urn:ruijie:ntos:params:xml:ns:yang:vlan}vlan")
                            vlan_elements.extend(vlan_ns_elements)
                        except:
                            pass

                        # 方法3：使用XPath查找（忽略命名空间）
                        try:
                            xpath_vlan_elements = fragment_root.xpath(".//*[local-name()='vlan']")
                            vlan_elements.extend(xpath_vlan_elements)
                        except:
                            pass

                        # 去重（使用id()确保真正的去重）
                        unique_vlan_elements = []
                        seen_ids = set()
                        for vlan_elem in vlan_elements:
                            elem_id = id(vlan_elem)
                            if elem_id not in seen_ids:
                                unique_vlan_elements.append(vlan_elem)
                                seen_ids.add(elem_id)

                        log(f"找到 {len(unique_vlan_elements)} 个VLAN接口元素", "info")

                        for vlan_elem in unique_vlan_elements:
                            # 确保VLAN元素有正确的命名空间
                            if vlan_elem.get("xmlns") is None:
                                vlan_elem.set("xmlns", "urn:ruijie:ntos:params:xml:ns:yang:vlan")
                            existing_interface.append(vlan_elem)
                            integrated_interfaces += 1
                            log(f"成功集成VLAN接口: {vlan_elem.find('name').text if vlan_elem.find('name') is not None else 'unknown'}", "info")

                        # 处理VLAN接口的access-control配置
                        self._update_vlan_access_control_configurations(existing_interface, context)

                        # 处理VLAN接口的IPv4配置
                        self._update_vlan_ipv4_configurations(existing_interface, context)

                        # 合并物理接口（遵循XML集成原则：如果节点已存在 → 修改字段）
                        # 首先收集模板中现有的物理接口（借鉴旧架构方法）
                        existing_physical_interfaces = self._collect_existing_physical_interfaces(existing_interface)
                        log(_("xml_template_integration.template_existing_physical_interfaces_count", count=len(existing_physical_interfaces)), "info")

                        # 查找XML片段中的物理接口（考虑命名空间）
                        fragment_physical_interfaces = []

                        # 方法1：直接查找
                        fragment_physical_interfaces.extend(fragment_root.findall(".//physical"))

                        # 方法2：使用接口命名空间查找
                        try:
                            fragment_physical_interfaces.extend(fragment_root.findall(".//{urn:ruijie:ntos:params:xml:ns:yang:interface}physical"))
                        except:
                            pass

                        # 方法3：使用XPath查找
                        try:
                            xpath_results = fragment_root.xpath(".//*[local-name()='physical']")
                            fragment_physical_interfaces.extend(xpath_results)
                        except:
                            pass

                        # 去重（可能同一个接口被多种方法找到）
                        unique_interfaces = []
                        seen_names = set()
                        for intf in fragment_physical_interfaces:
                            # 使用多种方法查找name元素（考虑命名空间）
                            name_elem = None
                            name_text = None

                            # 方法1：直接查找
                            name_elem = intf.find("name")
                            if name_elem is not None:
                                name_text = name_elem.text

                            # 方法2：使用接口命名空间查找
                            if name_text is None:
                                try:
                                    name_elem = intf.find("{urn:ruijie:ntos:params:xml:ns:yang:interface}name")
                                    if name_elem is not None:
                                        name_text = name_elem.text
                                except:
                                    pass

                            # 方法3：使用XPath查找
                            if name_text is None:
                                try:
                                    name_elems = intf.xpath("./*[local-name()='name']")
                                    if name_elems:
                                        name_text = name_elems[0].text
                                except:
                                    pass

                            if name_text and name_text not in seen_names:
                                unique_interfaces.append(intf)
                                seen_names.add(name_text)
                            elif not name_text:
                                unique_interfaces.append(intf)  # 没有名称的接口也保留

                        fragment_physical_interfaces = unique_interfaces
                        log(_("xml_template_integration.fragment_physical_interfaces_count", count=len(fragment_physical_interfaces)), "info")
                        if fragment_physical_interfaces:
                            interface_names = []
                            for intf in fragment_physical_interfaces:
                                name_elem = intf.find("name")
                                if name_elem is not None:
                                    interface_names.append(name_elem.text)
                            log(_("xml_template_integration.fragment_interface_names", names=interface_names), "info")

                        for physical_elem in fragment_physical_interfaces:
                            # 使用多种方法查找name元素（考虑命名空间）
                            interface_name_text = None

                            # 方法1：直接查找
                            interface_name = physical_elem.find("name")
                            if interface_name is not None:
                                interface_name_text = interface_name.text

                            # 方法2：使用接口命名空间查找
                            if interface_name_text is None:
                                try:
                                    interface_name = physical_elem.find("{urn:ruijie:ntos:params:xml:ns:yang:interface}name")
                                    if interface_name is not None:
                                        interface_name_text = interface_name.text
                                except:
                                    pass

                            # 方法3：使用XPath查找
                            if interface_name_text is None:
                                try:
                                    name_elems = physical_elem.xpath("./*[local-name()='name']")
                                    if name_elems:
                                        interface_name_text = name_elems[0].text
                                except:
                                    pass

                            if interface_name_text:
                                log(_("xml_template_integration.processing_interface_fragment", name=interface_name_text), "info")

                                if interface_name_text in existing_physical_interfaces:
                                    # 节点已存在 → 修改字段
                                    log(_("xml_template_integration.update_existing_physical_interface", name=interface_name_text), "info")
                                    existing_physical = existing_physical_interfaces[interface_name_text]
                                    self._update_existing_physical_interface(existing_physical, physical_elem, context)
                                else:
                                    # 节点不存在 → 插入新的完整块
                                    log(_("xml_template_integration.insert_new_physical_interface", name=interface_name_text), "info")
                                    existing_interface.append(physical_elem)
                                integrated_interfaces += 1
                            else:
                                # 没有名称的接口，直接追加
                                log(_("xml_template_integration.found_interface_without_name"), "info")
                                existing_interface.append(physical_elem)
                                integrated_interfaces += 1



                except etree.XMLSyntaxError as e:
                    log(_("xml_template_integration.interface_fragment_parse_failed", error=str(e)), "error")
                    log(_("xml_template_integration.interface_fragment_content", content=xml_fragment[:500]), "error")
                    # 回退到传统方式
                    integrated_interfaces = 0
                except Exception as e:
                    log(_("xml_template_integration.interface_fragment_integration_exception", error=str(e)), "error")
                    log(_("xml_template_integration.interface_fragment_content", content=xml_fragment[:500]), "error")
                    import traceback
                    log(_("xml_template_integration.detailed_error_info", error=traceback.format_exc()), "error")
                    # 回退到传统方式
                    integrated_interfaces = 0

            # 区域配置已移至独立的安全区域处理阶段
            # 这里不再处理区域配置，避免重复创建security-zone节点
            integrated_zones = len(zones) if zones else 0

            # 借鉴旧架构：在XML片段集成完成后过滤模板中没有映射关系的接口
            # 注意：跳过过滤，因为我们已经更新了现有接口，不应该删除它们
            # self._filter_template_interfaces(template_root, context)
            log(_("xml_template_integration.skip_template_interface_filtering"), "info")

            log(_("xml_template_integration_stage.interface_configuration_integrated"), "info",
                interface_count=integrated_interfaces, zone_count=len(zones),
                integrated_interfaces=integrated_interfaces, integrated_zones=integrated_zones)

            return True

        except Exception as e:
            error_msg = _("xml_template_integration_stage.interface_configuration_integration_exception", error=str(e))
            context.add_error(error_msg)
            log(error_msg, "error")
            return False

    def _integrate_bridge_configuration(self, template_root: etree.Element, context: DataContext) -> bool:
        """
        处理模板中现有的bridge节点配置

        Args:
            template_root: XML模板根元素
            context: 数据上下文

        Returns:
            bool: 处理是否成功
        """
        try:
            operation_mode_result = context.get_data("operation_mode_result")
            if not operation_mode_result:
                return True

            # 查找模板中现有的bridge节点
            existing_bridge_nodes = self._find_existing_bridge_nodes(template_root)
            if not existing_bridge_nodes:
                return True

            log(_("xml_template_integration_stage.found_existing_bridge_nodes", count=len(existing_bridge_nodes)), "info")

            # 处理每个bridge节点
            for bridge_node in existing_bridge_nodes:
                if not self._process_bridge_link_interfaces(bridge_node, operation_mode_result, context):
                    log(_("xml_template_integration_stage.bridge_node_processing_failed"), "warning")
                    continue

            log(_("xml_template_integration_stage.bridge_configuration_integrated"), "info")
            return True

        except Exception as e:
            log(_("xml_template_integration_stage.bridge_configuration_integration_error"), "error",
                error=str(e))
            return False

    def _find_existing_bridge_nodes(self, template_root: etree.Element) -> List[etree.Element]:
        """
        查找模板中现有的bridge节点

        Args:
            template_root: XML模板根元素

        Returns: List[etree.Element]: 找到的bridge节点列表
        """
        # ✅ 第三阶段重构：使用通用查找方法查找bridge节点
        bridge_namespace = "urn:ruijie:ntos:params:xml:ns:yang:bridge"

        try:
            # 使用通用方法查找所有bridge元素
            bridge_nodes = self._find_elements_robust(template_root, "bridge", namespace=bridge_namespace)
            return bridge_nodes

        except Exception as e:
            error_msg = _("xml_template_integration_stage.bridge_node_search_error", error=str(e))
            log(error_msg, "error")
            return []

    def _process_bridge_link_interfaces(self, bridge_node: etree.Element, operation_mode_result: Dict, context: DataContext) -> bool:
        """
        处理bridge节点中的link-interface配置

        Args:
            bridge_node: bridge节点元素
            operation_mode_result: 操作模式处理结果
            context: 数据上下文

        Returns:
            bool: 处理是否成功
        """
        try:
            operation_mode = operation_mode_result.get("mode", "route")

            if operation_mode == "route":
                # 路由模式：清空所有link-interface
                return self._clear_bridge_link_interfaces(bridge_node)
            elif operation_mode == "transparent":
                # 透明模式：重建link-interface列表
                return self._rebuild_bridge_link_interfaces(bridge_node, operation_mode_result, context)
            else:
                log(_("xml_template_integration_stage.unknown_operation_mode", mode=operation_mode), "warning")
                return True

        except Exception as e:
            error_msg = _("xml_template_integration_stage.bridge_link_interface_processing_error", error=str(e))
            log(error_msg, "error")
            return False

    def _clear_bridge_link_interfaces(self, bridge_node: etree.Element) -> bool:
        """
        清空bridge节点中的所有link-interface元素（路由模式）

        Args:
            bridge_node: bridge节点元素

        Returns:
            bool: 清空是否成功
        """
        try:
            # 查找所有link-interface元素
            link_interfaces = []

            # 方法1：直接查找
            link_interfaces.extend(bridge_node.findall("link-interface"))

            # 方法2：使用XPath查找（忽略命名空间）
            try:
                xpath_links = bridge_node.xpath("./*[local-name()='link-interface']")
                link_interfaces.extend(xpath_links)
            except:
                pass

            # 去重
            unique_links = []
            seen_links = set()
            for link in link_interfaces:
                link_id = id(link)
                if link_id not in seen_links:
                    unique_links.append(link)
                    seen_links.add(link_id)

            # 删除所有link-interface元素
            removed_count = 0
            for link_interface in unique_links:
                try:
                    bridge_node.remove(link_interface)
                    removed_count += 1
                except:
                    pass

            log(_("xml_template_integration_stage.bridge_link_interfaces_cleared", count=removed_count), "info")
            return True

        except Exception as e:
            error_msg = _("xml_template_integration_stage.bridge_link_interface_clear_error", error=str(e))
            log(error_msg, "error")
            return False

    def _rebuild_bridge_link_interfaces(self, bridge_node: etree.Element, operation_mode_result: Dict, context: DataContext) -> bool:
        """
        重建bridge节点中的link-interface列表（透明模式）

        Args:
            bridge_node: bridge节点元素
            operation_mode_result: 操作模式处理结果
            context: 数据上下文

        Returns:
            bool: 重建是否成功
        """
        try:
            # 首先清空现有的link-interface
            if not self._clear_bridge_link_interfaces(bridge_node):
                log(_("xml_template_integration_stage.bridge_clear_before_rebuild_failed"), "warning")

            # 获取需要添加的接口列表
            bridge_config = operation_mode_result.get("bridge_config", {})
            slave_interfaces = bridge_config.get("slave_interfaces", [])

            if not slave_interfaces:
                log(_("xml_template_integration_stage.no_slave_interfaces_to_add"), "info")
                return True

            # 添加新的link-interface元素
            added_count = 0
            for slave_interface in slave_interfaces:
                try:
                    # 创建link-interface元素
                    link_interface = etree.SubElement(bridge_node, "link-interface")

                    # 创建slave子元素
                    slave_elem = etree.SubElement(link_interface, "slave")
                    slave_elem.text = slave_interface

                    added_count += 1

                except Exception as e:
                    log(_("xml_template_integration_stage.bridge_slave_interface_add_error"), "warning",
                        interface=slave_interface, error=str(e))
                    continue

            log(_("xml_template_integration_stage.bridge_link_interfaces_rebuilt", count=added_count), "info")
            return True
            
        except Exception as e:
            error_msg = _("xml_template_integration_stage.bridge_link_interface_rebuild_error", error=str(e))
            log(error_msg, "error")
            return False

    def _update_existing_physical_interface(self, existing_physical: etree.Element, new_physical: etree.Element, context: DataContext = None):
        """
        更新现有的物理接口节点（遵循XML集成原则：如果节点已存在 → 修改字段）
        借鉴旧架构的update_existing_interface方法

        Args:
            existing_physical: 模板中现有的物理接口节点
            new_physical: 接口处理阶段生成的新物理接口节点
            context: 数据上下文（用于访问原始接口数据）
        """
        try:
            # ✅ 第四阶段重构：使用通用查找方法获取接口名称
            interface_name_elem = self._find_child_element_robust(
                existing_physical, "name",
                namespace="urn:ruijie:ntos:params:xml:ns:yang:interface"
            )
            interface_name_text = interface_name_elem.text if interface_name_elem is not None else "unknown"

            log(_("xml_template_integration.start_updating_existing_interface", name=interface_name_text), "info")

            # ✅ 第四阶段重构：更新简单字段（使用通用查找方法）
            simple_fields = ["enabled", "working-mode", "wanlan", "description"]
            for field_name in simple_fields:
                # 使用通用查找方法查找新字段
                new_field = self._find_child_element_robust(
                    new_physical, field_name,
                    namespace="urn:ruijie:ntos:params:xml:ns:yang:interface"
                )

                if new_field is not None:
                    log(_("xml_template_integration.found_new_field", field=field_name, value=new_field.text), "info")

                    # ✅ 第四阶段重构：使用通用查找方法删除现有字段
                    existing_field = self._find_child_element_robust(
                        existing_physical, field_name,
                        namespace="urn:ruijie:ntos:params:xml:ns:yang:interface"
                    )

                    if existing_field is not None:
                        existing_physical.remove(existing_field)
                        log(_("xml_template_integration.delete_existing_field", field=field_name, value=existing_field.text), "info")

                    # 添加新字段（深拷贝并清理命名空间）
                    new_field_copy = copy.deepcopy(new_field)

                    # 清理命名空间，确保与现有XML兼容
                    if new_field_copy.tag.startswith('{'):
                        # 移除命名空间前缀，使用简单标签名
                        simple_tag = new_field_copy.tag.split('}')[-1]
                        new_field_copy.tag = simple_tag

                    # 递归清理子元素的命名空间
                    for child in new_field_copy.iter():
                        if child.tag.startswith('{'):
                            child.tag = child.tag.split('}')[-1]

                    existing_physical.append(new_field_copy)
                    log(_("xml_template_integration.update_field_complete", field=field_name, value=new_field.text, tag=new_field_copy.tag), "info")

                    # 验证字段是否真的被添加了
                    verification_field = existing_physical.find(field_name)
                    if verification_field is not None:
                        log(_("xml_template_integration.field_verification_success", field=field_name, value=verification_field.text), "info")
                    else:
                        log(_("xml_template_integration.field_verification_failed", field=field_name), "warning")
                else:
                    log(_("xml_template_integration.new_field_not_exist", field=field_name), "debug")

            # 2. 更新复杂字段（特殊处理）
            log(_("xml_template_integration.start_updating_complex_fields", name=interface_name_text), "info")
            self._update_ipv4_configuration(existing_physical, new_physical)
            self._update_access_control_configuration(existing_physical, context)
            self._update_bandwidth_configuration(existing_physical, new_physical)

            log(_("xml_template_integration.physical_interface_update_complete", name=interface_name_text), "info")

        except Exception as e:
            log(_("xml_template_integration.update_physical_interface_failed", error=str(e)), "error")
            import traceback
            log(_("xml_template_integration.detailed_error_info", error=traceback.format_exc()), "error")

    def _collect_existing_physical_interfaces(self, interface_elem: etree.Element) -> Dict[str, etree.Element]:
        """
        收集模板中现有的物理接口（借鉴旧架构方法）

        Args:
            interface_elem: 接口容器元素

        Returns: Dict[str, etree.Element]: 接口名称到接口元素的映射
        """
        existing_interfaces = {}

        # ✅ 第六阶段重构：使用通用查找方法获取所有物理接口
        physical_interfaces = self._find_elements_robust(
            interface_elem, "physical",
            namespace="urn:ruijie:ntos:params:xml:ns:yang:interface"
        )

        # ✅ 第六阶段重构：使用通用查找方法处理物理接口名称
        for physical in physical_interfaces:
            # 使用通用查找方法查找name元素
            name_elem = self._find_child_element_robust(
                physical, "name",
                namespace="urn:ruijie:ntos:params:xml:ns:yang:interface"
            )

            if name_elem is not None and name_elem.text:
                interface_name = name_elem.text
                if interface_name not in existing_interfaces:  # 避免重复
                    existing_interfaces[interface_name] = physical

        log(_("xml_template_integration.collected_existing_physical_interfaces", count=len(existing_interfaces)), "info")
        return existing_interfaces

    def _update_ipv4_configuration(self, existing_physical: etree.Element, new_physical: etree.Element):
        """
        更新IPv4配置（采用检查-修改-创建策略）

        策略：检查是否存在IPv4标签，存在则修改，不存在则创建

        根据YANG模型，接口有两种不同的IPv4容器：
        1. 直接的<ipv4>容器：用于IPv4地址é置（来自ntos-ipv4-config）
        2. <network-stack><ipv4>容器：用于IPv4网络栈参数（来自ntos-network-stack-parameters）

        这两个容器是不同的，不应该重复。

        Args:
            existing_physical: 现有的物理接口节点
            new_physical: 新的物理接口节点
        """
        try:
            log(_("xml_template_integration.update_ipv4_configuration_start"), "info")
            # 查找新接口的直接IPv4配置（用于IP地址）
            new_ipv4_direct = self._find_direct_ipv4_config(new_physical)
            log(_("xml_template_integration.new_ipv4_direct_result", result=new_ipv4_direct is not None), "info")

            if new_ipv4_direct is not None:
                log(_("xml_template_integration.found_new_direct_ipv4_config"), "debug")

                # 检查是否包含IP地址配置、DHCP配置或PPPoE配置
                new_address = self._find_ipv4_address_config(new_ipv4_direct)
                new_dhcp = self._find_ipv4_dhcp_config(new_ipv4_direct)
                new_pppoe = self._find_ipv4_pppoe_config(new_ipv4_direct)
                log(_("xml_template_integration.new_address_result", result=new_address is not None), "info")
                log(_("xml_template_integration.new_dhcp_result", result=new_dhcp is not None), "info")
                log(_("xml_template_integration.new_pppoe_result", result=new_pppoe is not None), "info")

                if new_address is not None or new_dhcp is not None or new_pppoe is not None:
                    if new_address is not None:
                        log(_("xml_template_integration.found_new_ipv4_address_config"), "info")
                    if new_dhcp is not None:
                        log(_("xml_template_integration.found_new_ipv4_dhcp_config"), "info")
                    if new_pppoe is not None:
                        log(_("xml_template_integration.found_new_ipv4_pppoe_config"), "info")

                    # 检查现有接口是否已有直接IPv4配置
                    existing_ipv4_direct = self._find_direct_ipv4_config(existing_physical)
                    log(_("xml_template_integration.existing_ipv4_direct_result", result=existing_ipv4_direct is not None), "info")

                    if existing_ipv4_direct is not None:
                        log(_("xml_template_integration.found_existing_ipv4_modify_strategy"), "info")
                        # 修改策略：更新现有IPv4配置
                        self._modify_existing_ipv4_config(existing_ipv4_direct, new_ipv4_direct)
                    else:
                        log(_("xml_template_integration.no_existing_ipv4_create_strategy"), "info")
                        # 创建策略：添加新的IPv4配置
                        new_ipv4_copy = copy.deepcopy(new_ipv4_direct)
                        self._clean_element_namespaces(new_ipv4_copy)
                        existing_physical.append(new_ipv4_copy)

                    # 提取配置信息用于日志
                    if new_address is not None:
                        ip_text = self._extract_ip_address_text(new_address)
                        log(_("xml_template_integration.ipv4_config_processing_complete", ip=ip_text), "info")
                    elif new_dhcp is not None:
                        log(_("xml_template_integration.ipv4_dhcp_config_processing_complete"), "info")
                    elif new_pppoe is not None:
                        log(_("xml_template_integration.ipv4_pppoe_config_processing_complete"), "info")
                else:
                    log(_("xml_template_integration.new_ipv4_no_address_skip"), "warning")
            else:
                log(_("xml_template_integration.new_interface_no_direct_ipv4"), "debug")

        except Exception as e:
            log(_("xml_template_integration.update_ipv4_config_failed", error=str(e)), "error")
            import traceback
            log(_("xml_template_integration.detailed_error_info", error=traceback.format_exc()), "error")

    def _find_direct_ipv4_config(self, physical_elem: etree.Element) -> etree.Element:
        """
        查找直接的IPv4配置（不包括network-stack内的IPv4）

        Args:
            physical_elem: 物理接口元素

        Returns:
            etree.Element: 直接的IPv4配置元素，如果没有则返回None
        """
        # ✅ 重构：使用XPath直接查找排除network-stack内的IPv4元素
        try:
            ipv4_elems = physical_elem.xpath(".//*[local-name()='ipv4' and not(parent::*[local-name()='network-stack'])]")
            if ipv4_elems:
                ipv4_elem = ipv4_elems[0]
                return ipv4_elem
        except Exception:
            pass

        # 备用方法：使用通用查找方法，然后验证父元素
        ipv4_elem = self._find_element_robust(
            physical_elem,
            "ipv4",
            namespace="urn:ruijie:ntos:params:xml:ns:yang:interface"
        )

        if ipv4_elem is not None:
            # 确保这不是network-stack内的IPv4
            parent = ipv4_elem.getparent()
            if parent is not None and parent.tag.endswith('network-stack'):
                ipv4_elem = None

        if ipv4_elem is None:
            log(_("xml_template_integration.no_direct_ipv4_config_found"), "debug")

        return ipv4_elem

    def _find_ipv4_address_config(self, ipv4_elem: etree.Element) -> etree.Element:
        """
        查找IPv4配置中的地址配置

        Args:
            ipv4_elem: IPv4配置元素

        Returns:
            etree.Element: 地址配置元素，如果没有则返回None
        """
        # ✅ 重构：使用通用查找方法
        address_elem = self._find_child_element_robust(
            ipv4_elem,
            "address",
            namespace="urn:ruijie:ntos:params:xml:ns:yang:interface"
        )

        if address_elem is not None:
            log(_("xml_template_integration.found_address_config_method1"), "debug")
        else:
            log(_("xml_template_integration.no_address_config_found"), "debug")

        return address_elem

    def _find_ipv4_dhcp_config(self, ipv4_elem: etree.Element) -> etree.Element:
        """
        查找IPv4配置中的DHCP配置

        ✅ 已重构：使用通用XML查找方法

        Args:
            ipv4_elem: IPv4配置元素

        Returns:
            etree.Element: DHCP配置元素，如果没有则返回None
        """
        # 使用通用查找方法
        dhcp_elem = self._find_child_element_robust(
            ipv4_elem,
            "dhcp",
            namespace="urn:ruijie:ntos:params:xml:ns:yang:interface"
        )

        if dhcp_elem is not None:
            log(_("xml_template_integration.found_dhcp_config_method1"), "debug")
        else:
            log(_("xml_template_integration.no_dhcp_config_found"), "debug")

        return dhcp_elem

    def _find_ipv4_pppoe_config(self, ipv4_elem: etree.Element) -> etree.Element:
        """
        查找IPv4配置中的PPPoE配置

        ✅ 已重构：使用通用XML查找方法

        Args:
            ipv4_elem: IPv4配置元素

        Returns:
            etree.Element: PPPoE配置元素，如果没有则返回None
        """
        # 使用通用查找方法
        pppoe_elem = self._find_child_element_robust(
            ipv4_elem,
            "pppoe",
            namespace="urn:ruijie:ntos:params:xml:ns:yang:interface"
        )

        if pppoe_elem is not None:
            log(_("xml_template_integration.found_pppoe_config_method1"), "debug")
        else:
            log(_("xml_template_integration.no_pppoe_config_found"), "debug")

        return pppoe_elem

    def _remove_existing_address_configs(self, ipv4_elem: etree.Element):
        """移除现有的地址配置"""
        for address_elem in ipv4_elem.findall("address"):
            ipv4_elem.remove(address_elem)
        # 使用XPath移除任何命名空间的address元素
        try:
            address_elems = ipv4_elem.xpath(".//*[local-name()='address']")
            for address_elem in address_elems:
                if address_elem.getparent() == ipv4_elem:
                    ipv4_elem.remove(address_elem)
        except:
            pass

    def _remove_existing_dhcp_configs(self, ipv4_elem: etree.Element):
        """移除现有的DHCP配置"""
        for dhcp_elem in ipv4_elem.findall("dhcp"):
            ipv4_elem.remove(dhcp_elem)
        # 使用XPath移除任何命名空间的dhcp元素
        try:
            dhcp_elems = ipv4_elem.xpath(".//*[local-name()='dhcp']")
            for dhcp_elem in dhcp_elems:
                if dhcp_elem.getparent() == ipv4_elem:
                    ipv4_elem.remove(dhcp_elem)
        except:
            pass

    def _remove_existing_pppoe_configs(self, ipv4_elem: etree.Element):
        """移除现有的PPPoE配置"""
        for pppoe_elem in ipv4_elem.findall("pppoe"):
            ipv4_elem.remove(pppoe_elem)
        # 使用XPath移除任何命名空间的pppoe元素
        try:
            pppoe_elems = ipv4_elem.xpath(".//*[local-name()='pppoe']")
            for pppoe_elem in pppoe_elems:
                if pppoe_elem.getparent() == ipv4_elem:
                    ipv4_elem.remove(pppoe_elem)
        except:
            pass

    def _remove_existing_direct_ipv4_configs(self, physical_elem: etree.Element):
        """
        删除现有的所有直接IPv4配置（避免重复）

        Args:
            physical_elem: 物理接口元素
        """
        # 查找所有直接的IPv4元素（排除network-stack内的）
        ipv4_elements_to_remove = []

        # 方法1：直接查找
        for ipv4_elem in physical_elem.findall("ipv4"):
            parent = ipv4_elem.getparent()
            if parent is None or not parent.tag.endswith('network-stack'):
                ipv4_elements_to_remove.append(ipv4_elem)

        # 方法2：使用XPath查找
        try:
            xpath_ipv4_elems = physical_elem.xpath(".//*[local-name()='ipv4' and not(parent::*[local-name()='network-stack'])]")
            for ipv4_elem in xpath_ipv4_elems:
                if ipv4_elem not in ipv4_elements_to_remove:
                    ipv4_elements_to_remove.append(ipv4_elem)
        except:
            pass

        # 删除找到的IPv4元素
        for ipv4_elem in ipv4_elements_to_remove:
            try:
                physical_elem.remove(ipv4_elem)
                log(_("xml_template_integration.delete_existing_direct_ipv4"), "debug")
            except:
                pass

    def _clean_element_namespaces(self, element: etree.Element):
        """
        清理元素及其子元素的命名空间

        Args:
            element: 要清理的元素
        """
        # 清理当前元素的命名空间
        if element.tag.startswith('{'):
            element.tag = element.tag.split('}')[-1]

        # 递归清理子元素的命名空间
        for child in element.iter():
            if child.tag.startswith('{'):
                child.tag = child.tag.split('}')[-1]

    def _modify_existing_ipv4_config(self, existing_ipv4: etree.Element, new_ipv4: etree.Element):
        """
        修改现有的IPv4配置（遵循模板优先原则：修改现有字段，保持模板结构）

        Args:
            existing_ipv4: 现有的IPv4配置元素
            new_ipv4: 新的IPv4配置元素
        """
        try:
            # 查找新配置中的地址、DHCP和PPPoE信息
            new_address = self._find_ipv4_address_config(new_ipv4)
            new_dhcp = self._find_ipv4_dhcp_config(new_ipv4)
            new_pppoe = self._find_ipv4_pppoe_config(new_ipv4)

            if new_address is None and new_dhcp is None and new_pppoe is None:
                log(_("xml_template_integration.new_ipv4_no_config_info_skip_modify"), "warning")
                return

            # 处理地址配置
            if new_address is not None:
                # 查找现有配置中的第一个地址元素（通常是模板默认IP）
                existing_address = existing_ipv4.find("address")
                if existing_address is None:
                    # 尝试使用命名空间
                    existing_address = existing_ipv4.find("{urn:ruijie:ntos}address")
                if existing_address is None:
                    # 尝试使用xpath查找任何命名空间的address元素
                    existing_addresses = existing_ipv4.xpath("./*[local-name()='address']")
                    if existing_addresses:
                        existing_address = existing_addresses[0]

                if existing_address is not None:
                    log(_("xml_template_integration.update_existing_address_config"), "info")
                    # 修改策略：更新现有地址配置的IP字段
                    self._update_address_element(existing_address, new_address)
                else:
                    log(_("xml_template_integration.add_new_address_to_existing_ipv4"), "info")
                    # 创建策略：添加新地址配置
                    new_address_copy = copy.deepcopy(new_address)
                    self._clean_element_namespaces(new_address_copy)
                    existing_ipv4.append(new_address_copy)

            # 处理DHCP配置
            if new_dhcp is not None:
                log(_("xml_template_integration.processing_dhcp_config"), "info")
                # 移除现有的地址配置（DHCP模式不需要静态地址）
                self._remove_existing_address_configs(existing_ipv4)
                # 移除现有的PPPoE配置（DHCP和PPPoE互斥）
                self._remove_existing_pppoe_configs(existing_ipv4)
                # 添加DHCP配置
                new_dhcp_copy = copy.deepcopy(new_dhcp)
                self._clean_element_namespaces(new_dhcp_copy)
                existing_ipv4.append(new_dhcp_copy)
                log(_("xml_template_integration.dhcp_config_added"), "info")

            # 处理PPPoE配置
            if new_pppoe is not None:
                log(_("xml_template_integration.processing_pppoe_config"), "info")
                # 移除现有的地址配置（PPPoE模式不需要静态地址）
                self._remove_existing_address_configs(existing_ipv4)
                # 移除现有的DHCP配置（DHCP和PPPoE互斥）
                self._remove_existing_dhcp_configs(existing_ipv4)
                # 添加PPPoE配置
                new_pppoe_copy = copy.deepcopy(new_pppoe)
                self._clean_element_namespaces(new_pppoe_copy)
                existing_ipv4.append(new_pppoe_copy)
                log(_("xml_template_integration.pppoe_config_added"), "info")

            # 更新其他IPv4字段（如enabled等）
            self._update_ipv4_simple_fields(existing_ipv4, new_ipv4)

        except Exception as e:
            log(_("xml_template_integration.modify_existing_ipv4_config_failed", error=str(e)), "error")

    def _update_address_element(self, existing_address: etree.Element, new_address: etree.Element):
        """
        更新地址元素

        Args:
            existing_address: 现有地址元素
            new_address: 新地址元素
        """
        # 更新IP地址
        # 尝试不同的命名空间查找方式
        new_ip = new_address.find("ip")
        if new_ip is None:
            new_ip = new_address.find("{urn:ruijie:ntos}ip")
        if new_ip is None:
            # 尝试使用xpath查找任何命名空间的ip元素
            new_ips = new_address.xpath("./*[local-name()='ip']")
            if new_ips:
                new_ip = new_ips[0]

        if new_ip is not None:
            # 尝试不同的命名空间查找方式
            existing_ip = existing_address.find("ip")
            if existing_ip is None:
                existing_ip = existing_address.find("{urn:ruijie:ntos}ip")
            if existing_ip is None:
                # 尝试使用xpath查找任何命名空间的ip元素
                existing_ips = existing_address.xpath("./*[local-name()='ip']")
                if existing_ips:
                    existing_ip = existing_ips[0]

            if existing_ip is not None:
                # 记录修改前的IP
                old_ip = existing_ip.text
                existing_ip.text = new_ip.text
                log(_("xml_template_integration.template_ip_field_updated", old_ip=old_ip, new_ip=new_ip.text), "info")
            else:
                # 添加新IP元素
                ip_copy = copy.deepcopy(new_ip)
                self._clean_element_namespaces(ip_copy)
                existing_address.append(ip_copy)

        # 更新前缀长度（如果存在）
        new_prefix = new_address.find("prefix-length")
        if new_prefix is not None:
            existing_prefix = existing_address.find("prefix-length")
            if existing_prefix is not None:
                existing_prefix.text = new_prefix.text
            else:
                prefix_copy = copy.deepcopy(new_prefix)
                self._clean_element_namespaces(prefix_copy)
                existing_address.append(prefix_copy)

    def _update_ipv4_simple_fields(self, existing_ipv4: etree.Element, new_ipv4: etree.Element):
        """
        更新IPv4的简单字段（如enabled等）

        Args:
            existing_ipv4: 现有IPv4元素
            new_ipv4: 新IPv4元素
        """
        simple_fields = ["enabled"]

        for field_name in simple_fields:
            new_field = new_ipv4.find(field_name)
            if new_field is not None:
                existing_field = existing_ipv4.find(field_name)
                if existing_field is not None:
                    existing_field.text = new_field.text
                else:
                    field_copy = copy.deepcopy(new_field)
                    self._clean_element_namespaces(field_copy)
                    existing_ipv4.append(field_copy)

    def _extract_ip_address_text(self, address_elem: etree.Element) -> str:
        """
        从地址配置中提取IP地址文本

        Args:
            address_elem: 地址配置元素

        Returns:
            str: IP地址文本
        """
        # 查找IP元素
        ip_elem = address_elem.find("ip")
        if ip_elem is None:
            try:
                ip_elems = address_elem.xpath(".//*[local-name()='ip']")
                if ip_elems:
                    ip_elem = ip_elems[0]
            except:
                pass

        return ip_elem.text if ip_elem is not None else "unknown"

    def _update_access_control_configuration(self, existing_physical: etree.Element, context: DataContext = None):
        """
        ✅ 第七阶段优化：更新访问控制配置（移除未使用参数，使用通用查找方法）

        Args:
            existing_physical: 现有的物理接口节点
            context: 数据上下文（用于访问原始接口数据）
        """
        # ✅ 使用通用查找方法获取接口名称
        interface_name_elem = self._find_child_element_robust(
            existing_physical, "name",
            namespace="urn:ruijie:ntos:params:xml:ns:yang:interface"
        )
        interface_name_text = interface_name_elem.text if interface_name_elem is not None else "unknown"

        # 根据用户建议：先找physical标签中name匹配的接口，然后在该physical标签下通过命名空间查找access-control节点

        if context is None:
            return

        # 从context中获取接口处理结果
        interface_result = context.get_data("interface_processing_result")
        if not interface_result:
            return

        # 获取转换后的接口数据
        converted_interfaces = interface_result.get("converted", {})
        if not converted_interfaces:
            return

        # ✅ 第七阶段优化：查找当前接口的access control数据
        interface_access_control_data = None
        for _, interface_config in converted_interfaces.items():
            if isinstance(interface_config, dict):
                ntos_name = interface_config.get("name")
                if ntos_name == interface_name_text:
                    interface_access_control_data = interface_config.get("access_control")
                    break

        if interface_access_control_data is None:
            return

        # 在当前physical标签下通过命名空间查找access-control节点
        # 尝试多种命名空间查找方式
        existing_access_control = None

        # 方法1：直接查找（无命名空间）
        existing_access_control = existing_physical.find("access-control")

        # 方法2：使用local-defend命名空间查找
        if existing_access_control is None:
            try:
                existing_access_control = existing_physical.find("{urn:ruijie:ntos:params:xml:ns:yang:local-defend}access-control")
            except:
                pass

        # 方法3：使用XPath在当前physical节点下查找
        if existing_access_control is None:
            try:
                access_control_nodes = existing_physical.xpath("./*[local-name()='access-control']")
                if access_control_nodes:
                    existing_access_control = access_control_nodes[0]
            except:
                pass

        # 方法4：遍历子节点查找
        if existing_access_control is None:
            for child in existing_physical:
                if child.tag.endswith("}access-control") or child.tag == "access-control":
                    existing_access_control = child
                    break

        if existing_access_control is None:
            return

        # 根据access_control数据直接修改对应值的内容

        # 支持的服务类型
        supported_services = ['https', 'ping', 'ssh']

        for service in supported_services:
            # 在access-control节点下查找服务元素，考虑命名空间问题
            service_elem = None

            # 方法1：直接查找（无命名空间）
            service_elem = existing_access_control.find(service)

            # 方法2：使用local-defend命名空间查找
            if service_elem is None:
                try:
                    service_elem = existing_access_control.find(f"{{urn:ruijie:ntos:params:xml:ns:yang:local-defend}}{service}")
                except:
                    pass

            # 方法3：使用XPath查找
            if service_elem is None:
                try:
                    service_elems = existing_access_control.xpath(f".//*[local-name()='{service}']")
                    if service_elems:
                        service_elem = service_elems[0]
                except:
                    pass

            # 方法4：遍历子节点查找
            if service_elem is None:
                for child in existing_access_control:
                    if child.tag.endswith(f"}}{service}") or child.tag == service:
                        service_elem = child
                        break

            if service_elem is not None:
                # 根据access_control数据设置值
                if isinstance(interface_access_control_data, dict):
                    new_value = "true" if interface_access_control_data.get(service, False) else "false"
                else:
                    new_value = "false"

                # ✅ 第七阶段优化：直接设置新值，移除未使用的旧值变量
                service_elem.text = new_value

    def _update_vlan_access_control_configurations(self, interface_container: etree.Element, context: DataContext = None):
        """
        更新所有VLAN接口的access-control配置

        Args:
            interface_container: 包含VLAN接口的容器节点
            context: 数据上下文（用于访问原始接口数据）
        """
        try:
            # 查找所有VLAN接口
            vlan_interfaces = self._find_vlan_interfaces(interface_container)

            for vlan_interface in vlan_interfaces:
                self._update_vlan_access_control(vlan_interface, context)

        except Exception as e:
            log(f"更新VLAN接口access-control配置时出错: {str(e)}", "error")
            import traceback
            log(f"错误详情: {traceback.format_exc()}", "error")

    def _find_vlan_interfaces(self, container: etree.Element):
        """
        查找容器中的所有VLAN接口节点

        Args:
            container: 要搜索的容器节点

        Returns:
            list: VLAN接口节点列表
        """
        # ✅ 第三阶段重构：使用通用查找方法查找VLAN接口
        return self._find_elements_robust(
            container, "vlan",
            namespace="urn:ruijie:ntos:params:xml:ns:yang:vlan"
        )

    def _update_vlan_access_control(self, vlan_interface: etree.Element, context: DataContext = None):
        """
        更新单个VLAN接口的access-control配置

        Args:
            vlan_interface: VLAN接口节点
            context: 数据上下文（用于访问原始接口数据）
        """
        # 获取VLAN接口名称用于调试
        vlan_name_text = "unknown"

        # 方法1：直接查找name元素
        name_elem = vlan_interface.find("name")
        if name_elem is not None:
            vlan_name_text = name_elem.text

        # 方法2：使用XPath查找（忽略命名空间）
        if vlan_name_text == "unknown":
            try:
                name_elems = vlan_interface.xpath(".//*[local-name()='name']")
                if name_elems:
                    vlan_name_text = name_elems[0].text
            except:
                pass

        if context is None:
            return

        # 从context中获取接口处理结果
        interface_result = context.get_data("interface_processing_result")
        if not interface_result:
            return

        # 获取转换后的接口数据
        converted_interfaces = interface_result.get("converted", {})
        if not converted_interfaces:
            return

        # ✅ 第七阶段优化：查找当前VLAN接口的access control数据
        vlan_access_control_data = None
        for _, interface_config in converted_interfaces.items():
            if isinstance(interface_config, dict):
                ntos_name = interface_config.get("name")
                if ntos_name == vlan_name_text:
                    vlan_access_control_data = interface_config.get("access_control")
                    break

        if vlan_access_control_data is None:
            return

        # 查找或创建access-control节点
        access_control_node = self._find_or_create_vlan_access_control_node(vlan_interface, vlan_name_text)

        if access_control_node is None:
            return

        # 更新access-control字段值
        self._update_access_control_fields(access_control_node, vlan_access_control_data, vlan_name_text)

    def _find_or_create_vlan_access_control_node(self, vlan_interface: etree.Element, vlan_name_text: str):
        """
        查找或创建VLAN接口的access-control节点

        Args:
            vlan_interface: VLAN接口节点
            vlan_name_text: VLAN接口名称（用于调试）

        Returns:
            etree.Element: access-control节点，如果创建失败则返回None
        """
        # 首先尝试查找现有的access-control节点
        access_control_node = None

        # 方法1：直接查找
        access_control_node = vlan_interface.find("access-control")

        # 方法2：使用local-defend命名空间查找
        if access_control_node is None:
            try:
                access_control_node = vlan_interface.find("{urn:ruijie:ntos:params:xml:ns:yang:local-defend}access-control")
            except:
                pass

        # 方法3：使用XPath查找（忽略命名空间）
        if access_control_node is None:
            try:
                access_control_elems = vlan_interface.xpath(".//*[local-name()='access-control']")
                if access_control_elems:
                    access_control_node = access_control_elems[0]
            except:
                pass

        if access_control_node is not None:
            return access_control_node

        # 如果没有找到，创建新的access-control节点

        try:
            # 创建access-control节点（使用local-defend命名空间）
            access_control_node = etree.Element("access-control")
            access_control_node.set("xmlns", "urn:ruijie:ntos:params:xml:ns:yang:local-defend")

            # 创建默认的服务子元素（初始值为false）
            services = ["https", "ping", "ssh"]
            for service in services:
                service_elem = etree.SubElement(access_control_node, service)
                service_elem.text = "false"

            # 将access-control节点添加到VLAN接口
            vlan_interface.append(access_control_node)
            return access_control_node

        except Exception:
            # ✅ 第七阶段优化：标准化异常处理
            return None

    def _update_access_control_fields(self, access_control_node: etree.Element, access_control_data: dict, interface_name: str):
        """
        更新access-control节点的字段值（通用方法，适用于physical和VLAN接口）

        Args:
            access_control_node: access-control节点
            access_control_data: access control数据字典
            interface_name: 接口名称（用于调试）
        """
        if not isinstance(access_control_data, dict):
            return

        # 定义需要更新的服务字段
        services = ["https", "ping", "ssh"]

        for service in services:
            # 获取新的值
            new_value = access_control_data.get(service, False)
            new_value_str = "true" if new_value else "false"

            # 查找服务元素（使用多种方法确保可靠性）
            service_elem = None

            # 方法1：直接查找
            service_elem = access_control_node.find(service)

            # 方法2：使用local-defend命名空间查找
            if service_elem is None:
                try:
                    service_elem = access_control_node.find(f"{{urn:ruijie:ntos:params:xml:ns:yang:local-defend}}{service}")
                except:
                    pass

            # 方法3：使用XPath查找（忽略命名空间）
            if service_elem is None:
                try:
                    service_elems = access_control_node.xpath(f".//*[local-name()='{service}']")
                    if service_elems:
                        service_elem = service_elems[0]
                except:
                    pass

            # 方法4：遍历子元素查找
            if service_elem is None:
                for child in access_control_node:
                    if child.tag == service or child.tag.endswith(f"}}{service}"):
                        service_elem = child
                        break

            if service_elem is not None:
                # ✅ 第七阶段优化：直接设置新值
                service_elem.text = new_value_str
            else:
                log(f"接口 {interface_name} - 未找到 {service} 元素，跳过更新", "warning")

    def _update_vlan_ipv4_configurations(self, interface_container: etree.Element, context: DataContext = None):
        """
        更新所有VLAN接口的IPv4配置

        Args:
            interface_container: 包含VLAN接口的容器节点
            context: 数据上下文（用于访问原始接口数据）
        """
        try:
            # 查找所有VLAN接口
            vlan_interfaces = self._find_vlan_interfaces(interface_container)

            for vlan_interface in vlan_interfaces:
                self._update_vlan_ipv4_configuration(vlan_interface, context)

        except Exception as e:
            log(f"更新VLAN接口IPv4配置时出错: {str(e)}", "error")
            import traceback
            log(f"错误详情: {traceback.format_exc()}", "error")

    def _update_vlan_ipv4_configuration(self, vlan_interface: etree.Element, context: DataContext = None):
        """
        更新单个VLAN接口的IPv4配置

        Args:
            vlan_interface: VLAN接口XML元素
            context: 数据上下文（用于访问原始接口数据）
        """
        try:
            # 获取VLAN接口名称
            name_elem = vlan_interface.find("./name")
            if name_elem is None or not name_elem.text:
                return

            interface_name = name_elem.text.strip()
            # 从context中获取接口配置数据
            interface_data = self._get_vlan_interface_data(interface_name, context)
            if not interface_data:
                return

            # 获取接口模式
            interface_mode = interface_data.get("mode", "").lower()
            has_static_ip = "ip" in interface_data or "ipv4" in interface_data

            # 查找或创建IPv4节点
            ipv4_elem = self._find_or_create_vlan_ipv4_node(vlan_interface)
            if ipv4_elem is None:
                log(f"无法为VLAN接口 {interface_name} 创建IPv4节点", "error")
                return

            # 根据模式配置IPv4
            if interface_mode == "dhcp":
                self._configure_vlan_dhcp(ipv4_elem, interface_data, interface_name)
            elif interface_mode == "pppoe":
                self._configure_vlan_pppoe(ipv4_elem, interface_data, interface_name)
            elif has_static_ip:
                self._configure_vlan_static_ip(ipv4_elem, interface_data, interface_name)
            else:
                # 默认启用IPv4但不配置特定模式
                enabled_elem = ipv4_elem.find("./enabled")
                if enabled_elem is None:
                    etree.SubElement(ipv4_elem, "enabled").text = "true"
                else:
                    enabled_elem.text = "true"

        except Exception as e:
            log(f"更新VLAN接口IPv4配置时出错: {str(e)}", "error")
            import traceback
            log(f"错误详情: {traceback.format_exc()}", "error")

    def _find_or_create_vlan_ipv4_node(self, vlan_interface: etree.Element) -> etree.Element:
        """
        查找或创建VLAN接口的IPv4节点

        Args:
            vlan_interface: VLAN接口XML元素

        Returns:
            etree.Element: IPv4节点
        """
        try:
            # 查找现有的IPv4节点
            ipv4_elem = vlan_interface.find("./ipv4")

            if ipv4_elem is not None:
                return ipv4_elem

            # 创建新的IPv4节点
            ipv4_elem = etree.SubElement(vlan_interface, "ipv4")

            # 确保enabled状态
            etree.SubElement(ipv4_elem, "enabled").text = "true"

            return ipv4_elem

        except Exception:
            # ✅ 第七阶段优化：标准化异常处理
            return None

    def _configure_vlan_static_ip(self, ipv4_elem: etree.Element, interface_data: dict, interface_name: str):
        """
        配置VLAN接口的静态IP

        Args:
            ipv4_elem: IPv4节点
            interface_data: 接口配置数据
            interface_name: 接口名称
        """
        try:
            # 确保enabled状态
            enabled_elem = ipv4_elem.find("./enabled")
            if enabled_elem is None:
                etree.SubElement(ipv4_elem, "enabled").text = "true"
            else:
                enabled_elem.text = "true"

            # 清除可能存在的DHCP和PPPoE配置
            for dhcp_elem in ipv4_elem.findall("./dhcp"):
                ipv4_elem.remove(dhcp_elem)
            for pppoe_elem in ipv4_elem.findall("./pppoe"):
                ipv4_elem.remove(pppoe_elem)

            # 获取IP地址
            ip_address = interface_data.get("ip") or interface_data.get("ipv4", {}).get("address")
            if not ip_address:
                log(f"VLAN接口 {interface_name} - 静态IP模式但未找到IP地址", "warning")
                return

            # 查找或创建address节点
            address_elem = ipv4_elem.find("./address")
            if address_elem is None:
                address_elem = etree.SubElement(ipv4_elem, "address")

            # 更新IP地址
            ip_elem = address_elem.find("./ip")
            if ip_elem is None:
                etree.SubElement(address_elem, "ip").text = ip_address
            else:
                ip_elem.text = ip_address

            log(f"VLAN接口 {interface_name} - 配置静态IP: {ip_address}", "info")

        except Exception as e:
            log(f"配置VLAN接口静态IP时出错: {str(e)}", "error")

    def _configure_vlan_dhcp(self, ipv4_elem: etree.Element, interface_data: dict, interface_name: str):
        """
        配置VLAN接口的DHCP

        Args:
            ipv4_elem: IPv4节点
            interface_data: 接口配置数据
            interface_name: 接口名称
        """
        try:
            # 确保enabled状态
            enabled_elem = ipv4_elem.find("./enabled")
            if enabled_elem is None:
                etree.SubElement(ipv4_elem, "enabled").text = "true"
            else:
                enabled_elem.text = "true"

            # 清除可能存在的静态IP和PPPoE配置
            for address_elem in ipv4_elem.findall("./address"):
                ipv4_elem.remove(address_elem)
            for pppoe_elem in ipv4_elem.findall("./pppoe"):
                ipv4_elem.remove(pppoe_elem)

            # 查找现有的DHCP配置
            dhcp_elem = ipv4_elem.find("./dhcp")
            if dhcp_elem is not None:
                return

            # 创建DHCP配置
            dhcp_elem = etree.SubElement(ipv4_elem, "dhcp")
            etree.SubElement(dhcp_elem, "enabled").text = "true"

            # 添加DHCP客户端配置参数
            etree.SubElement(dhcp_elem, "timeout").text = "60"
            etree.SubElement(dhcp_elem, "retry").text = "30"
            etree.SubElement(dhcp_elem, "select-timeout").text = "0"
            etree.SubElement(dhcp_elem, "reboot").text = "10"
            etree.SubElement(dhcp_elem, "initial-interval").text = "10"
            etree.SubElement(dhcp_elem, "dhcp-lease-time").text = "3600"

            # 添加DHCP请求选项
            dhcp_requests = [
                "subnet-mask", "broadcast-address", "time-offset", "routers",
                "domain-name", "domain-search", "domain-name-servers", "host-name",
                "nis-domain", "nis-servers", "ntp-servers", "interface-mtu"
            ]
            for request in dhcp_requests:
                etree.SubElement(dhcp_elem, "request").text = request

            log(f"VLAN接口 {interface_name} - 配置DHCP模式", "info")

        except Exception as e:
            log(f"配置VLAN接口DHCP时出错: {str(e)}", "error")

    def _configure_vlan_pppoe(self, ipv4_elem: etree.Element, interface_data: dict, interface_name: str):
        """
        配置VLAN接口的PPPoE

        Args:
            ipv4_elem: IPv4节点
            interface_data: 接口配置数据
            interface_name: 接口名称
        """
        try:
            # 确保enabled状态
            enabled_elem = ipv4_elem.find("./enabled")
            if enabled_elem is None:
                etree.SubElement(ipv4_elem, "enabled").text = "true"
            else:
                enabled_elem.text = "true"

            # 清除可能存在的静态IP和DHCP配置
            for address_elem in ipv4_elem.findall("./address"):
                ipv4_elem.remove(address_elem)
            for dhcp_elem in ipv4_elem.findall("./dhcp"):
                ipv4_elem.remove(dhcp_elem)

            # 查找现有的PPPoE配置
            pppoe_elem = ipv4_elem.find("./pppoe")
            if pppoe_elem is not None:
                return

            # 创建PPPoE配置
            pppoe_elem = etree.SubElement(ipv4_elem, "pppoe")

            # 创建connection节点
            connection_elem = etree.SubElement(pppoe_elem, "connection")
            etree.SubElement(connection_elem, "tunnel").text = "2"
            etree.SubElement(connection_elem, "enabled").text = "true"

            # PPPoE认证信息
            username = interface_data.get("pppoe_username", interface_data.get("username", "user"))
            password = interface_data.get("pppoe_password", "123456")  # 使用固定密码

            etree.SubElement(connection_elem, "user").text = username
            etree.SubElement(connection_elem, "password").text = password
            etree.SubElement(connection_elem, "gateway").text = "true"
            etree.SubElement(connection_elem, "timeout").text = "5"
            etree.SubElement(connection_elem, "retries").text = "3"

            # PPP配置
            ppp_elem = etree.SubElement(connection_elem, "ppp")
            etree.SubElement(ppp_elem, "negotiation-timeout").text = "3"
            etree.SubElement(ppp_elem, "lcp-echo-interval").text = "10"
            etree.SubElement(ppp_elem, "lcp-echo-retries").text = "10"

            # PPP MTU
            etree.SubElement(connection_elem, "ppp-mtu").text = "1492"

            log(f"VLAN接口 {interface_name} - 配置PPPoE模式，用户名: {username}", "info")

        except Exception as e:
            log(f"配置VLAN接口PPPoE时出错: {str(e)}", "error")

    def _get_vlan_interface_data(self, interface_name: str, context: DataContext = None) -> dict:
        """
        从context中获取VLAN接口的配置数据

        Args:
            interface_name: 接口名称
            context: 数据上下文

        Returns:
            dict: 接口配置数据
        """
        try:
            if not context:
                return {}

            # 从接口处理结果中查找VLAN接口数据
            interface_result = context.get_data("interface_processing_result", {})
            vlan_interfaces = interface_result.get("vlan_interfaces", {})

            # ✅ 第七阶段优化：查找匹配的VLAN接口
            for _, interface_data in vlan_interfaces.items():
                # 检查映射后的名称是否匹配
                if interface_data.get("mapped_name") == interface_name:
                    return interface_data

            # 如果没有找到映射名称匹配，尝试直接匹配原始名称
            if interface_name in vlan_interfaces:
                return vlan_interfaces[interface_name]

            # 尝试从原始接口数据中查找
            parsed_data = context.get_data("parsed_data", {})
            interfaces = parsed_data.get("interfaces", {})

            # ✅ 第七阶段优化：查找FortiGate原始接口名称
            for _, interface_data in interfaces.items():
                if interface_data.get("is_subinterface", False):
                    # 构建预期的NTOS接口名称进行匹配
                    parent_interface = interface_data.get("interface", "")
                    vlan_id = interface_data.get("vlanid", "")
                    if parent_interface and vlan_id:
                        # 查找父接口的映射名称
                        interface_mapping = context.get_data("interface_mapping", {})
                        mapped_parent = interface_mapping.get(parent_interface, "")
                        if mapped_parent:
                            expected_name = f"{mapped_parent}.{vlan_id}"
                            if expected_name == interface_name:
                                return interface_data

            log(f"未找到VLAN接口 {interface_name} 的配置数据", "warning")
            return {}

        except Exception as e:
            log(f"获取VLAN接口配置数据时出错: {str(e)}", "error")
            return {}

    def _merge_address_set(self, existing_set: etree.Element, new_set: etree.Element) -> None:
        """
        合并address-set元素（模板优先原则）

        Args:
            existing_set: 模板中已存在的address-set元素
            new_set: 新的address-set元素
        """
        try:
            # 更新描述（如果新的有描述且模板中没有）
            existing_desc = existing_set.find("description")
            new_desc = new_set.find("description")

            if new_desc is not None and existing_desc is None:
                desc_elem = etree.SubElement(existing_set, "description")
                desc_elem.text = new_desc.text

            # ✅ 第六阶段重构：使用通用方法合并ip-set元素
            existing_ip_sets = set()
            existing_ip_set_elems = self._find_elements_robust(existing_set, "ip-set")
            for ip_set in existing_ip_set_elems:
                identifier = self._extract_ip_set_identifier(ip_set)
                if identifier:
                    existing_ip_sets.add(identifier)

            # ✅ 第六阶段重构：使用通用方法处理新IP集合
            new_ip_set_elems = self._find_elements_robust(new_set, "ip-set")
            for new_ip_set in new_ip_set_elems:
                new_ip_identifier = self._extract_ip_set_identifier(new_ip_set)

                if new_ip_identifier and new_ip_identifier not in existing_ip_sets:
                    # 添加新的ip-set
                    existing_set.append(new_ip_set)
                    existing_ip_sets.add(new_ip_identifier)

        except Exception as e:
            log(_("xml_template_integration.merge_address_set_failed", error=str(e)), "error")

    def _integrate_address_objects(self, template_root: etree.Element, context: DataContext) -> bool:
        """
        集成地址对象到XML模板 - 使用管道化XML片段

        ⚠️ 核心网络对象处理 - 安全策略的基础依赖 ⚠️

        依赖关系：
        - 依赖地址处理阶段的结果数据
        - 必须在安全策略集成之前完成
        - 为地址对象组提供基础对象

        处理流程：
        1. 获取地址处理结果和XML片段
        2. 查找或创建VRF节点和network-obj容器
        3. 解析XML片段中的address-set元素
        4. 合并新的地址对象到现有的network-obj容器中
        5. 处理地址对象的冲突和重复

        关键业务规则：
        - 地址对象名称必须唯一
        - 支持多种地址类型：单IP、IP范围、子网、FQDN
        - VIP对象转换为标准地址对象
        - 地址对象必须有有效的IP地址或范围

        XML结构要求：
        - 地址对象放在VRF下的network-obj容器中
        - 每个address-set包含name和address元素
        - address元素包含ip、mask、range等子元素
        - 必须符合NTOS network-obj YANG模型规范

        合并策略：
        - 模板优先：如果模板中已存在同名对象，保留模板版本
        - 增量合并：只添加模板中不存在的新对象
        - 验证完整性：确保所有引用的地址对象都存在

        Args:
            template_root: XML模板根元素
            context: 数据上下文，包含地址处理结果

        Returns:
            bool: 集成是否成功，失败时会在context中添加错误信息
        """
        try:

            address_result = context.get_data("address_processing_result")
            if not address_result:
                log(_("xml_template_integration_stage.no_address_result"), "info")
                return True

            # 获取XML片段
            xml_fragment = address_result.get("xml_fragment", "")
            address_objects = address_result.get("address_objects", {})

            if not xml_fragment:
                return True

            # 使用XML片段集成地址对象到XML模板
            integrated_objects = 0

            # 使用统一的VRF查找方法，避免重复创建
            vrf_elem = self._find_or_create_vrf_node(template_root)

            # ✅ 已重构：使用通用XML片段处理方法
            fragment_root = self._parse_xml_fragment_robust(xml_fragment, "地址对象")
            if fragment_root is None:
                return False

            # 查找模板中是否存在network-obj节点
            existing_network_obj = self._find_element_robust(vrf_elem, "network-obj")

            if existing_network_obj is None:
                # 模板中不存在，直接插入整个network-obj节点
                # 检查插入前的address-set数量（使用正确的命名空间）
                address_sets_before = fragment_root.findall(".//{urn:ruijie:ntos}address-set")

                # 使用通用片段集成方法
                if self._integrate_xml_fragment_to_container(
                    fragment_root, vrf_elem,
                    namespace="urn:ruijie:ntos:params:xml:ns:yang:network-obj"
                ):
                    integrated_objects = len(address_sets_before)
                else:
                    return False
            else:
                # 模板中存在，使用通用合并方法
                if self._merge_xml_elements(fragment_root, existing_network_obj, merge_strategy="merge"):
                    # 统计新增的地址对象数量
                    address_sets_in_fragment = fragment_root.findall(".//{urn:ruijie:ntos}address-set")
                    integrated_objects = len(address_sets_in_fragment)
                else:
                    return False

            # 地址组处理已移至独立的AddressGroupProcessingStage
            # 这里不再处理地址组
            return True

        except Exception as e:
            error_msg = _("xml_template_integration_stage.address_objects_integration_exception", error=str(e))
            context.add_error(error_msg)
            log(error_msg, "error")
            return False

    def _integrate_address_groups(self, template_root: etree.Element, context: DataContext) -> bool:
        """
        集成地址对象组到XML模板 - 使用管道化XML片段

        Args:
            template_root: 模板根元素
            context: 数据上下文

        Returns:
            bool: 集成是否成功
        """
        try:
            address_group_result = context.get_data("address_group_processing_result")
            if not address_group_result:
                xml_fragment = address_group_result.get("xml_fragment", "")
                return True
            # 获取XML片段
            xml_fragment = address_group_result.get("xml_fragment", "")
            address_groups = address_group_result.get("address_groups", {})
            if not xml_fragment:
                log(_("xml_template_integration.no_address_group_fragment"), "info")
                return True

            # 使用XML片段集成地址对象组到XML模板
            integrated_groups = 0

            # 使用统一的VRF查找方法，避免重复创建
            vrf_elem = self._find_or_create_vrf_node(template_root)

            # 解析XML片段
            try:
                fragment_root = etree.fromstring(xml_fragment)
                # 使用命名空间查找address-group元素（现在使用与地址对象相同的命名空间）
                address_groups_in_fragment = fragment_root.findall(".//{urn:ruijie:ntos}address-group")
                if not address_groups_in_fragment:
                    # 如果命名空间查找失败，尝试不带命名空间的查找
                    address_groups_in_fragment = fragment_root.findall(".//address-group")

                # 查找模板中是否存在network-obj节点（使用命名空间感知的查找）
                # 尝试多种方式查找network-obj节点
                existing_network_obj = None

                # 方法1：使用local-name()查找（最可靠）
                network_obj_nodes = vrf_elem.xpath(".//*[local-name()='network-obj']")
                if network_obj_nodes:
                    existing_network_obj = network_obj_nodes[0]

                # 方法2：直接查找（备用）
                if existing_network_obj is None:
                    existing_network_obj = vrf_elem.find(".//network-obj")
                    if existing_network_obj is not None:
                       existing_network_obj = etree.SubElement(vrf_elem, "network-obj")

                if existing_network_obj is None:
                    # 模板中不存在，创建新的network-obj节点
                    existing_network_obj = etree.SubElement(vrf_elem, "network-obj")
                    existing_network_obj.set("xmlns", "urn:ruijie:ntos:params:xml:ns:yang:network-obj")
                    # 如果命名空间查找失败，尝试不带命名空间的查找

                # 将地址对象组添加到现有的network-obj节点中
                # 使用命名空间查找address-group元素（现在使用与地址对象相同的命名空间）
                address_groups = fragment_root.findall(".//{urn:ruijie:ntos}address-group")
                if not address_groups:
                    # 如果命名空间查找失败，尝试不带命名空间的查找
                    address_groups = fragment_root.findall(".//address-group")

                # 将所有地址对象组添加到现有的network-obj节点中
                for address_group in address_groups:
                    # 使用命名空间查找name元素（现在使用与地址对象相同的命名空间）
                    group_name_elem = address_group.find("{urn:ruijie:ntos}name")
                    if group_name_elem is None:
                        # 如果命名空间查找失败，尝试不带命名空间的查找
                        group_name_elem = address_group.find("name")

                    if group_name_elem is not None:
                        group_name_text = group_name_elem.text

                        # 检查是否已存在同名的address-group
                        existing_addr_group = None
                        for existing_group in existing_network_obj.findall(".//address-group"):
                            # 使用命名空间查找name元素（现在使用与地址对象相同的命名空间）
                            existing_name = existing_group.find("{urn:ruijie:ntos}name")
                            if existing_name is None:
                                # 如果命名空间查找失败，尝试不带命名空间的查找
                                existing_name = existing_group.find("name")
                            if existing_name is not None and existing_name.text == group_name_text:
                                existing_addr_group = existing_group
                                break

                        if existing_addr_group is not None:
                            # 存在则更新（模板优先原则）
                            self._merge_address_group(existing_addr_group, address_group)
                            log(f"DEBUG: Updated existing address group: {group_name_text}", "info")
                        else:
                            # 不存在则插入
                            existing_network_obj.append(address_group)
                            integrated_groups += 1
                            log(f"DEBUG: Inserted new address group: {group_name_text}, total integrated: {integrated_groups}", "info")
                    else:
                        log("DEBUG: Address group has no name element", "info")

            except etree.XMLSyntaxError as e:
                log(_("xml_template_integration.address_group_parse_failed", error=str(e)), "error")
                return False

            log(_("xml_template_integration.address_group_integration_complete", count=integrated_groups), "info")

            return True

        except Exception as e:
            error_msg = f"地址对象组集成异常: {str(e)}"
            context.add_error(error_msg)
            log(error_msg, "error")
            return False

    def _merge_address_group(self, existing_group: etree.Element, new_group: etree.Element) -> None:
        """
        合并address-group元素（模板优先原则）

        Args:
            existing_group: 模板中已存在的address-group元素
            new_group: 新的address-group元素
        """
        try:
            # 更新描述（如果新的有描述且模板中没有）
            existing_desc = existing_group.find("description")
            new_desc = new_group.find("description")

            if new_desc is not None and existing_desc is None:
                desc_elem = etree.SubElement(existing_group, "description")
                desc_elem.text = new_desc.text

            # 合并address-set元素
            existing_address_sets = set()
            for address_set in existing_group.findall("address-set"):
                # 构建address-set的唯一标识
                name_elem = address_set.find("name")
                if name_elem is not None:
                    existing_address_sets.add(name_elem.text)

            for new_address_set in new_group.findall("address-set"):
                # 构建新address-set的唯一标识
                name_elem = new_address_set.find("name")

                if name_elem is not None:
                    name_text = name_elem.text
                    if name_text and name_text not in existing_address_sets:
                        # 添加新的address-set
                        existing_group.append(new_address_set)
                        existing_address_sets.add(name_text)

        except Exception as e:
            log(_("xml_template_integration.merge_address_group_failed", error=str(e)), "error")

    def _integrate_service_objects(self, template_root: etree.Element, context: DataContext) -> bool:
        """
        集成服务对象到XML模板 - 使用管道化XML片段

        Args:
            template_root: 模板根元素
            context: 数据上下文

        Returns:
            bool: 集成是否成功
        """
        try:
            service_result = context.get_data("service_processing_result")
            if not service_result:
                log(_("xml_template_integration.no_service_object_result"), "info")
                return True

            # 获取XML片段
            xml_fragment = service_result.get("xml_fragment", "")
            service_objects = service_result.get("service_objects", {})
            if not xml_fragment:
                return True

            # 使用XML片段集成服务对象到XML模板
            integrated_services = 0

            # 使用统一的VRF查找方法，避免重复创建
            vrf_elem = self._find_or_create_vrf_node(template_root)

            # ✅ 已重构：使用通用XML片段处理方法
            fragment_root = self._parse_xml_fragment_robust(xml_fragment, "服务对象")
            if fragment_root is None:
                return False

            # 查找模板中是否存在service-obj节点（使用更可靠的查找方法）
            existing_service_obj = self._find_service_obj_container(vrf_elem)

            if existing_service_obj is None:
                # 模板中不存在，使用通用片段集成方法
                if self._integrate_xml_fragment_to_container(
                    fragment_root, vrf_elem,
                    namespace="urn:ruijie:ntos:params:xml:ns:yang:service-obj"
                ):
                    integrated_services = len(fragment_root.findall(".//service-set"))
                else:
                    return False
            else:
                # 模板中存在，使用通用合并方法
                if self._merge_xml_elements(fragment_root, existing_service_obj, merge_strategy="merge"):
                    # 统计新增的服务对象数量
                    service_sets_in_fragment = fragment_root.findall(".//service-set")
                    integrated_services = len(service_sets_in_fragment)
                else:
                    return False

            log(_("xml_template_integration.service_object_integration_complete", count=integrated_services), "info")

            return True

        except Exception as e:
            error_msg = f"服务对象集成异常: {str(e)}"
            context.add_error(error_msg)
            log(error_msg, "error")
            return False

    def _merge_service_set(self, existing_set: etree.Element, new_set: etree.Element) -> None:
        """
        合并service-set元素（模板优先原则），确保源端口字段一致性

        Args:
            existing_set: 模板中已存在的service-set元素
            new_set: 新的service-set元素
        """
        try:
            # ✅ 第六阶段重构：使用通用查找方法更新描述
            existing_desc = self._find_child_element_robust(existing_set, "description")
            new_desc = self._find_child_element_robust(new_set, "description")

            if new_desc is not None and existing_desc is None:
                desc_elem = etree.SubElement(existing_set, "description")
                desc_elem.text = new_desc.text

            # ✅ 第六阶段重构：使用通用查找方法合并协议元素
            protocol_elements = ["tcp", "udp", "icmp", "protocol-id"]
            for protocol in protocol_elements:
                existing_protocol = self._find_child_element_robust(existing_set, protocol)
                new_protocol = self._find_child_element_robust(new_set, protocol)

                if new_protocol is not None and existing_protocol is None:
                    # 模板中没有该协议，添加新的协议定义
                    existing_set.append(new_protocol)
                    log(_("xml_template_integration.add_new_protocol_definition", protocol=protocol), "debug")
                elif new_protocol is not None and existing_protocol is not None:
                    # 两者都存在，需要合并端口信息，确保源端口一致性
                    self._merge_protocol_ports(existing_protocol, new_protocol, protocol)

        except Exception as e:
            log(_("xml_template_integration.merge_service_set_failed", error=str(e)), "error")

    def _merge_protocol_ports(self, existing_protocol: etree.Element, new_protocol: etree.Element, protocol_name: str) -> None:
        """
        合并协议端口信息，确保源端口字段一致性

        Args:
            existing_protocol: 模板中已存在的协议元素
            new_protocol: 新的协议元素
            protocol_name: 协议名称（tcp/udp）
        """
        try:
            # 处理目的端口
            existing_dest_port = existing_protocol.find("dest-port")
            new_dest_port = new_protocol.find("dest-port")

            if new_dest_port is not None and existing_dest_port is None:
                dest_port_elem = etree.SubElement(existing_protocol, "dest-port")
                dest_port_elem.text = new_dest_port.text
                log(_("xml_template_integration.add_dest_port_to_existing_protocol",
                      protocol=protocol_name, port=new_dest_port.text), "debug")

            # 处理源端口 - 根据NTOS YANG模型要求，源端口是必填的
            existing_source_port = existing_protocol.find("source-port")
            new_source_port = new_protocol.find("source-port")

            if new_source_port is not None and existing_source_port is None:
                # 模板中没有源端口，添加新的源端口
                source_port_elem = etree.SubElement(existing_protocol, "source-port")
                source_port_elem.text = new_source_port.text
                log(_("xml_template_integration.add_source_port_to_existing_protocol",
                      protocol=protocol_name, port=new_source_port.text), "info")
            elif existing_source_port is None and new_source_port is None:
                # 两者都没有源端口，根据NTOS要求添加默认源端口
                source_port_elem = etree.SubElement(existing_protocol, "source-port")
                source_port_elem.text = "0-65535"
                log(_("xml_template_integration.add_default_source_port_to_protocol",
                      protocol=protocol_name), "info")

        except Exception as e:
            log(_("xml_template_integration.merge_protocol_ports_failed",
                  protocol=protocol_name, error=str(e)), "error")

    def _integrate_service_groups(self, template_root: etree.Element, context: DataContext) -> bool:
        """
        集成服务对象组到XML模板 - 使用管道化XML片段

        Args:
            template_root: 模板根元素
            context: 数据上下文

        Returns:
            bool: 集成是否成功
        """
        try:
            log(_("xml_template_integration.start_integrating_service_groups"), "debug")

            service_group_result = context.get_data("service_group_processing_result")

            if not service_group_result:
                log(_("xml_template_integration.no_service_group_result"), "info")
                return True

            # 获取XML片段
            xml_fragment = service_group_result.get("xml_fragment", "")
            service_groups = service_group_result.get("service_groups", {})

            log(_("xml_template_integration.service_group_integration_data", group_count=len(service_groups.get("converted", {})), has_fragment=bool(xml_fragment)), "debug")

            if not xml_fragment:
                log(_("xml_template_integration.no_service_group_fragment"), "info")
                return True

            # 使用XML片段集成服务对象组到XML模板
            integrated_groups = 0

            # 使用统一的VRF查找方法，避免重复创建
            vrf_elem = self._find_or_create_vrf_node(template_root)

            # 解析XML片段
            try:
                fragment_root = etree.fromstring(xml_fragment)
                # 查找模板中是否存在service-obj节点（使用更可靠的查找方法）
                existing_service_obj = self._find_service_obj_container(vrf_elem)

                if existing_service_obj is None:
                    # 确实不存在，创建新的service-obj节点
                    fragment_root.set("xmlns", "urn:ruijie:ntos:params:xml:ns:yang:service-obj")
                    vrf_elem.append(fragment_root)
                    integrated_groups = len(fragment_root.findall(".//service-group"))
                else:
                    # 已存在service-obj，直接合并内容
                    # 使用正确的带命名空间的查找方式
                    service_groups = fragment_root.findall(".//{urn:ruijie:ntos:params:xml:ns:yang:service-obj}service-group")

                    # 如果带命名空间查找失败，尝试不带命名空间的查找
                    if not service_groups:
                        service_groups = fragment_root.findall(".//service-group")

                    for service_group in service_groups:
                        existing_service_obj.append(service_group)
                        integrated_groups += 1

            except etree.XMLSyntaxError as e:
                log(_("xml_template_integration.service_group_parse_failed", error=str(e)), "error")
                return False

            log(_("xml_template_integration.service_group_integration_complete", count=integrated_groups), "info")

            return True

        except Exception as e:
            error_msg = f"服务对象组集成异常: {str(e)}"
            context.add_error(error_msg)
            log(error_msg, "error")
            return False

    def _merge_service_group(self, existing_group: etree.Element, new_group: etree.Element) -> None:
        """
        合并service-group元素（模板优先原则）

        Args:
            existing_group: 模板中已存在的service-group元素
            new_group: 新的service-group元素
        """
        try:
            # 更新描述（如果新的有描述且模板中没有）
            existing_desc = existing_group.find("description")
            new_desc = new_group.find("description")

            if new_desc is not None and existing_desc is None:
                desc_elem = etree.SubElement(existing_group, "description")
                desc_elem.text = new_desc.text
                log(_("xml_template_integration.add_description_to_existing_service_group"), "debug")

            # 合并service-set元素
            existing_service_sets = set()
            for service_set in existing_group.findall("service-set"):
                # 构建service-set的唯一标识
                name_elem = service_set.find("name")
                if name_elem is not None:
                    existing_service_sets.add(name_elem.text)

            for new_service_set in new_group.findall("service-set"):
                # 构建新service-set的唯一标识
                name_elem = new_service_set.find("name")

                if name_elem is not None:
                    name_text = name_elem.text
                    if name_text and name_text not in existing_service_sets:
                        # 添加新的service-set
                        existing_group.append(new_service_set)
                        existing_service_sets.add(name_text)
                        log(_("xml_template_integration.add_new_service_set", name=name_text), "debug")

        except Exception as e:
            log(_("xml_template_integration.merge_service_group_failed", error=str(e)), "error")

    def _integrate_security_zones(self, template_root: etree.Element, context: DataContext) -> bool:
        """
        集成安全区域到XML模板 - 使用管道化XML片段

        Args:
            template_root: 模板根元素
            context: 数据上下文

        Returns:
            bool: 集成是否成功
        """
        try:
            zone_result = context.get_data("zone_processing_result")
            if not zone_result:
                log(_("xml_template_integration.no_zone_result"), "info")
                return True

            # 获取XML片段
            xml_fragment = zone_result.get("xml_fragment", "")
            zones = zone_result.get("zones", {})

            log(_("xml_template_integration.security_zone_integration_data", zone_count=len(zones.get("converted", {})), has_fragment=bool(xml_fragment)), "debug")

            if not xml_fragment:
                log(_("xml_template_integration.no_security_zone_fragment"), "info")
                return True

            # 使用XML片段集成安全区域到XML模板
            integrated_zones = 0

            # 使用统一的VRF查找方法，避免重复创建
            vrf_elem = self._find_or_create_vrf_node(template_root)

            # ✅ 已重构：使用通用XML片段处理方法
            fragment_root = self._parse_xml_fragment_robust(xml_fragment, "安全区域")
            if fragment_root is None:
                return False

            log(_("xml_template_integration.zone_parse_success"), "debug")

            # 查找模板中是否存在security-zone节点（使用更可靠的查找方法）
            existing_security_zone = self._find_security_zone_container(vrf_elem)

            if existing_security_zone is None:
                # 模板中不存在，使用通用片段集成方法
                if self._integrate_xml_fragment_to_container(
                    fragment_root, vrf_elem,
                    namespace="urn:ruijie:ntos:params:xml:ns:yang:security-zone"
                ):
                    # 使用通用方法查找zone元素
                    zone_elements = self._find_elements_robust(fragment_root, "zone")
                    integrated_zones = len(zone_elements)
                    log(_("xml_template_integration.insert_new_security_zone_node_zones", count=integrated_zones), "debug")
                else:
                    return False
            else:
                # 模板中存在，使用通用合并方法
                if self._merge_xml_elements(fragment_root, existing_security_zone, merge_strategy="merge"):
                    # 统计新增的安全区域数量
                    zone_elements = self._find_elements_robust(fragment_root, "zone")
                    integrated_zones = len(zone_elements)
                else:
                    return False

            log(_("xml_template_integration.zone_integration_complete", count=integrated_zones), "info")

            return True

        except Exception as e:
            error_msg = f"安全区域集成异常: {str(e)}"
            context.add_error(error_msg)
            log(error_msg, "error")
            return False

    def _merge_security_zone(self, existing_zone: etree.Element, new_zone: etree.Element) -> None:
        """
        合并security-zone元素（模板优先原则）

        Args:
            existing_zone: 模板中已存在的zone元素
            new_zone: 新的zone元素
        """
        try:
            # 更新描述（如果新的有描述且模板中没有）
            existing_desc = existing_zone.find("description")
            new_desc = new_zone.find("description")

            if new_desc is not None and existing_desc is None:
                desc_elem = etree.SubElement(existing_zone, "description")
                desc_elem.text = new_desc.text

            # 更新优先级（如果新的有优先级且模板中没有）
            existing_priority = existing_zone.find("priority")
            new_priority = new_zone.find("priority")

            if new_priority is not None and existing_priority is None:
                priority_elem = etree.SubElement(existing_zone, "priority")
                priority_elem.text = new_priority.text


            # 合并interface元素
            existing_interfaces = set()
            for interface in existing_zone.findall("interface"):
                # 构建interface的唯一标识
                name_elem = interface.find("name")
                if name_elem is not None:
                    existing_interfaces.add(name_elem.text)

            for new_interface in new_zone.findall("interface"):
                # 构建新interface的唯一标识
                name_elem = new_interface.find("name")

                if name_elem is not None:
                    name_text = name_elem.text
                    if name_text and name_text not in existing_interfaces:
                        # 添加新的interface
                        existing_zone.append(new_interface)
                        existing_interfaces.add(name_text)


        except Exception as e:
            log(_("xml_template_integration.merge_security_zone_failed", error=str(e)), "error")

    def _integrate_time_ranges(self, template_root: etree.Element, context: DataContext) -> bool:
        """
        集成时间对象到XML模板（使用XML片段直接集成）

        Args:
            template_root: 模板根元素
            context: 数据上下文

        Returns:
            bool: 集成是否成功
        """
        try:
            time_range_result = context.get_data("time_range_processing_result")
            if not time_range_result:
                log(_("xml_template_integration.no_time_object_result"), "info")
                return True

            xml_fragment = time_range_result.get("xml_fragment", "")
            if not xml_fragment:
                log(_("xml_template_integration.no_time_object_fragment"), "info")
                return True


            # 保存XML片段到文件用于调试（使用绝对路径和异常处理）
            try:
                import os
                debug_file_path = os.path.join(os.getcwd(), "output", "debug_time_xml_fragment.xml")

                # 确保输出目录存在
                os.makedirs(os.path.dirname(debug_file_path), exist_ok=True)

                with open(debug_file_path, "w", encoding="utf-8") as f:
                    f.write(xml_fragment)

                # 验证文件是否成功写入
                if os.path.exists(debug_file_path):
                    log(f"调试文件已保存: {debug_file_path}", "debug")
                else:
                    log(f"警告: 调试文件写入可能失败: {debug_file_path}", "warning")

            except Exception as file_error:
                log(f"调试文件写入失败: {str(file_error)}，继续处理时间对象集成", "warning")

            # ✅ 已重构：使用通用XML片段处理方法
            fragment_root = self._parse_xml_fragment_robust(xml_fragment, "时间范围")
            if fragment_root is None:
                return False

            # 定义命名空间映射
            namespaces = {
                'tr': 'urn:ruijie:ntos:params:xml:ns:yang:time-range'
            }

            # 检查解析后的XML片段内容（使用命名空间前缀）
            range_elements = fragment_root.findall(".//tr:range", namespaces)
            for i, range_elem in enumerate(range_elements[:3]):  # 只显示前3个
                name_elem = range_elem.find("tr:name", namespaces)
                name = name_elem.text if name_elem is not None else "unknown"
                log(f"  Range {i+1}: {name}", "info")

            # 查找或创建VRF元素（使用统一的方法）
            vrf_elem = self._find_or_create_vrf_node(template_root)
            if vrf_elem is None:
                log(_("xml_template_integration.cannot_get_vrf_for_time_objects"), "error")
                return False


            # 查找现有的time-range元素
            existing_time_range = self._find_element_robust(vrf_elem, "time-range")

            if existing_time_range is not None:
                # 如果存在，使用通用合并方法
                if not self._merge_xml_elements(fragment_root, existing_time_range, merge_strategy="merge"):
                    return False
            else:
                # 如果不存在，使用通用片段集成方法
                if not self._integrate_xml_fragment_to_container(
                    fragment_root, vrf_elem,
                    namespace="urn:ruijie:ntos:params:xml:ns:yang:time-range"
                ):
                    return False

            time_ranges = time_range_result.get("time_ranges", {})
            converted_count = time_ranges.get("converted_count", 0)
            log(_("xml_template_integration.time_object_integration_complete_count", count=converted_count), "info")

            return True

        except Exception as e:
            import traceback
            error_details = traceback.format_exc()

            # 提供更详细和准确的错误信息
            if "No such file or directory" in str(e):
                error_msg = f"时间对象集成文件操作异常: {str(e)}"
            elif "XMLSyntaxError" in str(e):
                error_msg = f"时间对象XML解析异常: {str(e)}"
            else:
                error_msg = f"时间对象集成异常: {str(e)}"

            context.add_error(error_msg)
            log(error_msg, "error")

            # 记录当前工作目录和文件状态用于调试
            import os
            current_dir = os.getcwd()
            debug_file_path = os.path.join(current_dir, "output", "debug_time_xml_fragment.xml")

            return False

    def _integrate_dns_configuration(self, template_root: etree.Element, context: DataContext) -> bool:
        """
        集成DNS配置到XML模板（使用XML片段直接集成）

        Args:
            template_root: 模板根元素
            context: 数据上下文

        Returns:
            bool: 集成是否成功
        """
        try:
            dns_result = context.get_data("dns_processing_result")
            if not dns_result:
                log(_("xml_template_integration.no_dns_result"), "info")
                return True

            xml_fragment = dns_result.get("xml_fragment", "")
            if not xml_fragment:
                log(_("xml_template_integration.no_dns_fragment"), "info")
                return True

            # ✅ 已重构：使用通用XML片段处理方法
            fragment_root = self._parse_xml_fragment_robust(xml_fragment, "DNS配置")
            if fragment_root is None:
                return False

            # 查找或创建VRF元素（使用统一的方法）
            vrf_elem = self._find_or_create_vrf_node(template_root)
            if vrf_elem is None:
                log(_("xml_template_integration.cannot_get_vrf_for_dns"), "error")
                return False

            # ✅ 已重构：使用通用XML片段处理方法集成DNS配置
            for dns_elem in fragment_root:
                # 检查DNS元素（去除命名空间）
                local_tag = dns_elem.tag.split('}')[-1] if '}' in dns_elem.tag else dns_elem.tag

                if local_tag == "dns":
                    # 使用通用查找方法处理DNS代理配置
                    existing_dns = self._find_element_robust(
                        vrf_elem, "dns",
                        namespace="urn:ruijie:ntos:params:xml:ns:yang:dns"
                    )

                    if existing_dns is not None:
                        # 替换现有DNS配置
                        parent = existing_dns.getparent()
                        parent.remove(existing_dns)
                        parent.append(dns_elem)
                    else:
                        # 添加新的DNS配置
                        vrf_elem.append(dns_elem)


                elif local_tag == "dns-client":
                    # 使用通用查找方法处理DNS客户端配置
                    existing_dns_client = self._find_element_robust(
                        vrf_elem, "dns-client",
                        namespace="urn:ruijie:ntos:params:xml:ns:yang:dns-client"
                    )

                    if existing_dns_client is not None:
                        # 合并DNS客户端配置
                        self._merge_dns_client_config(existing_dns_client, dns_elem)
                    else:
                        # 添加新的DNS客户端配置
                        vrf_elem.append(dns_elem)

            dns_config = dns_result.get("dns_config", {})
            statistics = dns_config.get("statistics", {})
            log(_("xml_template_integration.dns_integration_complete_detailed", dns_servers=statistics.get("valid_dns_servers", 0), static_hosts=statistics.get("valid_static_hosts", 0)), "info")

            return True

        except Exception as e:
            error_msg = f"DNS配置集成异常: {str(e)}"
            context.add_error(error_msg)
            log(error_msg, "error")
            return False

    def _merge_dns_client_config(self, existing_elem: etree.Element, new_elem: etree.Element):
        """
        合并DNS客户端配置
        Args:
            existing_elem: 现有的DNS客户端元素
            new_elem: 新的DNS客户端元素
        """
        # 合并ip-host配置
        for new_ip_host in new_elem.findall("ip-host"):
            host_name = new_ip_host.find("host-name")
            if host_name is not None:
                # 检查是否已存在同名的ip-host
                existing_ip_host = existing_elem.find(f"ip-host[host-name='{host_name.text}']")
                if existing_ip_host is not None:
                    # 替换现有的ip-host
                    parent = existing_ip_host.getparent()
                    parent.remove(existing_ip_host)
                    parent.append(new_ip_host)
                else:
                    # 添加新的ip-host
                    existing_elem.append(new_ip_host)

        # 合并ipv6-host配置
        for new_ipv6_host in new_elem.findall("ipv6-host"):
            host_name = new_ipv6_host.find("host-name")
            if host_name is not None:
                # 检查是否已存在同名的ipv6-host
                existing_ipv6_host = existing_elem.find(f"ipv6-host[host-name='{host_name.text}']")
                if existing_ipv6_host is not None:
                    # 替换现有的ipv6-host
                    parent = existing_ipv6_host.getparent()
                    parent.remove(existing_ipv6_host)
                    parent.append(new_ipv6_host)
                else:
                    # 添加新的ipv6-host
                    existing_elem.append(new_ipv6_host)

    def _integrate_static_routes(self, template_root: etree.Element, context: DataContext) -> bool:
        """
        集成静态路由到XML模板

        Args:
            template_root: 模板根元素
            context: 数据上下文

        Returns:
            bool: 集成是否成功
        """
        try:
            static_route_result = context.get_data("static_route_processing_result")
            if not static_route_result:
                log(_("xml_template_integration.no_static_route_result"), "info")
                return True

            xml_fragment = static_route_result.get("xml_fragment", "")
            if not xml_fragment:
                log(_("xml_template_integration.no_static_route_fragment"), "info")
                return True

            # ✅ 已重构：使用通用XML片段处理方法
            fragment_root = self._parse_xml_fragment_robust(xml_fragment, "静态路由")
            if fragment_root is None:
                return False

            # 查找或创建VRF元素（使用统一的方法）
            vrf_elem = self._find_or_create_vrf_node(template_root)
            if vrf_elem is None:
                log(_("xml_template_integration.cannot_get_vrf_for_static_routes"), "error")
                return False

            # ✅ 已重构：使用通用查找和集成方法
            existing_routing = self._find_element_robust(
                vrf_elem, "routing",
                namespace="urn:ruijie:ntos:params:xml:ns:yang:routing"
            )

            if existing_routing is not None:
                # 如果存在routing元素，使用通用合并方法
                if not self._merge_xml_elements(fragment_root, existing_routing, merge_strategy="merge"):
                    return False
            else:
                # 如果不存在routing元素，使用通用片段集成方法
                if not self._integrate_xml_fragment_to_container(
                    fragment_root, vrf_elem,
                    namespace="urn:ruijie:ntos:params:xml:ns:yang:routing"
                ):
                    return False

            static_routes = static_route_result.get("static_routes", {})
            converted_count = static_routes.get("converted_count", 0)
            route_groups = len(static_routes.get("grouped_routes", {}))
            log(_("xml_template_integration.static_route_integration_complete_detailed", routes=converted_count, groups=route_groups), "info")

            return True

        except Exception as e:
            error_msg = f"静态路由集成异常: {str(e)}"
            context.add_error(error_msg)
            log(error_msg, "error")
            return False

    def _merge_static_routes(self, existing_static: etree.Element, new_static: etree.Element):
        """
        合并静态路由配置

        Args:
            existing_static: 现有的static元素
            new_static: 新的static元素
        """
        # 合并ipv4-route配置
        for new_ipv4_route in new_static.findall("ipv4-route"):
            dest_prefix = new_ipv4_route.find("destination-prefix")
            if dest_prefix is not None:
                destination = dest_prefix.text

                # 检查是否已存在相同目的网段的ipv4-route
                existing_ipv4_route = None
                for existing_route in existing_static.findall("ipv4-route"):
                    existing_dest = existing_route.find("destination-prefix")
                    if existing_dest is not None and existing_dest.text == destination:
                        existing_ipv4_route = existing_route
                        break

                if existing_ipv4_route is not None:
                    # 合并next-hop元素
                    for new_next_hop in new_ipv4_route.findall("next-hop"):
                        existing_ipv4_route.append(new_next_hop)
                else:
                    # 添加新的ipv4-route
                    existing_static.append(new_ipv4_route)

    def _integrate_single_interface(self, interfaces_elem: etree.Element, interface_name: str, interface_config: Dict) -> bool:
        """
        集成单个接口配置

        Args:
            interfaces_elem: 接口容器元素
            interface_name: 接口名称
            interface_config: 接口配置

        Returns:
            bool: 集成是否成功
        """
        try:
            # 检查接口类型，跳过VLAN接口（现在由XML片段处理）
            interface_type = interface_config.get("type", "unknown")
            if interface_type == "vlan":
                log(_("xml_template_integration.skip_vlan_interface", interface=interface_name), "debug")
                return True  # 返回True表示"成功处理"（通过跳过）

            # 创建interface元素
            interface_elem = etree.SubElement(interfaces_elem, "interface")

            # 添加接口名称
            name_elem = etree.SubElement(interface_elem, "name")
            name_elem.text = interface_name

            # 添加接口类型（如果有）
            if "type" in interface_config:
                type_elem = etree.SubElement(interface_elem, "type")
                type_elem.text = interface_config["type"]

            # 添加接口描述（如果有）
            if "description" in interface_config:
                desc_elem = etree.SubElement(interface_elem, "description")
                desc_elem.text = interface_config["description"]

            # 添加IP配置（如果有）
            if "ip" in interface_config:
                ip_config = interface_config["ip"]
                ipv4_elem = etree.SubElement(interface_elem, "ipv4")
                ipv4_elem.set("xmlns", "urn:ruijie:ntos:params:xml:ns:yang:ipv4")

                if "address" in ip_config:
                    addr_elem = etree.SubElement(ipv4_elem, "address")
                    ip_elem = etree.SubElement(addr_elem, "ip")
                    ip_elem.text = ip_config["address"]

                    if "netmask" in ip_config:
                        mask_elem = etree.SubElement(addr_elem, "netmask")
                        mask_elem.text = ip_config["netmask"]

            # 添加启用状态（如果有）
            if "enabled" in interface_config:
                enabled_elem = etree.SubElement(interface_elem, "enabled")
                enabled_elem.text = "true" if interface_config["enabled"] else "false"

            log(_("xml_template_integration_stage.interface_integrated"), "debug", name=interface_name)
            return True

        except Exception as e:
            log(_("xml_template_integration_stage.interface_integration_failed"), "warning",
                name=interface_name, error=str(e))
            return False

    def _integrate_single_zone(self, security_zone_elem: etree.Element, zone_name: str, zone_config: Dict) -> bool:
        """
        集成单个安全区域配置

        Args:
            security_zone_elem: 安全区域容器元素
            zone_name: 区域名称
            zone_config: 区域配置

        Returns:
            bool: 集成是否成功
        """
        try:
            # 创建zone元素
            zone_elem = etree.SubElement(security_zone_elem, "zone")

            # 添加区域名称
            name_elem = etree.SubElement(zone_elem, "name")
            name_elem.text = zone_name

            # 添加区域描述（如果有）
            if "description" in zone_config:
                desc_elem = etree.SubElement(zone_elem, "description")
                desc_elem.text = zone_config["description"]

            # 添加接口绑定（如果有）
            if "interfaces" in zone_config:
                interfaces = zone_config["interfaces"]
                if isinstance(interfaces, list):
                    for interface_name in interfaces:
                        interface_elem = etree.SubElement(zone_elem, "interface")
                        interface_elem.text = interface_name
                elif isinstance(interfaces, str):
                    interface_elem = etree.SubElement(zone_elem, "interface")
                    interface_elem.text = interfaces

            log(_("xml_template_integration_stage.zone_integrated"), "debug", name=zone_name)
            return True

        except Exception as e:
            log(_("xml_template_integration_stage.zone_integration_failed"), "warning",
                name=zone_name, error=str(e))
            return False

    def _find_or_create_vrf_node(self, template_root: etree.Element) -> etree.Element:
        """
        查找或创建VRF节点（借鉴旧架构的多重查找方法，添加详细调试）

        Args:
            template_root: XML模板根节点

        Returns:
            etree.Element: VRF节点
        """
        # 添加详细的调试信息
        log(_("xml_template_integration.start_vrf_lookup", root=template_root.tag, xmlns=template_root.get("xmlns")), "debug")

        # 输出模板根元素的子元素信息
        child_tags = [child.tag for child in template_root]
        log(_("xml_template_integration.template_child_elements", elements=", ".join(child_tags[:10])), "debug")  # 只显示前10个子元素

        # 定义命名空间映射
        nsmap = {
            'ntos': 'urn:ruijie:ntos',
            None: 'urn:ruijie:ntos'
        }

        vrf = None

        # 方法1：直接查找（最简单的方法）
        try:
            vrf = template_root.find(".//vrf")
            if vrf is not None:
                log(_("xml_template_integration.vrf_direct_lookup_success", tag=vrf.tag, xmlns=vrf.get("xmlns")), "debug")
                return vrf
            else:
                log(_("xml_template_integration.vrf_direct_lookup_no_result"), "debug")
        except Exception as e:
            log(_("xml_template_integration.vrf_direct_lookup_failed", error=str(e)), "debug")

        # 方法2：使用local-name()函数查找（最可靠的方法）
        if vrf is None:
            try:
                vrf_nodes = template_root.xpath(".//*[local-name()='vrf']")
                if vrf_nodes and len(vrf_nodes) > 0:
                    vrf = vrf_nodes[0]
                    log(_("xml_template_integration.vrf_local_name_lookup_success", tag=vrf.tag, xmlns=vrf.get("xmlns")), "debug")
                    return vrf
                else:
                    log(_("xml_template_integration.vrf_local_name_lookup_no_result"), "debug")
            except Exception as e:
                log(_("xml_template_integration.vrf_local_name_lookup_failed", error=str(e)), "debug")

        # 方法3：使用命名空间前缀查找
        if vrf is None:
            try:
                vrf = template_root.find(".//ntos:vrf", namespaces=nsmap)
                if vrf is not None:
                    log(_("xml_template_integration.vrf_namespace_lookup_success", tag=vrf.tag, xmlns=vrf.get("xmlns")), "debug")
                    return vrf
                else:
                    log(_("xml_template_integration.vrf_namespace_lookup_no_result"), "debug")
            except Exception as e:
                log(_("xml_template_integration.vrf_namespace_lookup_failed", error=str(e)), "debug")

        # 方法4：使用完整URI查找
        if vrf is None:
            try:
                vrf = template_root.find(".//{%s}vrf" % nsmap['ntos'])
                if vrf is not None:
                    log(_("xml_template_integration.vrf_full_uri_lookup_success", tag=vrf.tag, xmlns=vrf.get("xmlns")), "debug")
                    return vrf
                else:
                    log(_("xml_template_integration.vrf_full_uri_lookup_no_result"), "debug")
            except Exception as e:
                log(_("xml_template_integration.vrf_full_uri_lookup_failed", error=str(e)), "debug")

        # 方法5：直接子元素查找
        if vrf is None:
            try:
                for child in template_root:
                    if child.tag.endswith("vrf") or "vrf" in child.tag:
                        vrf = child
                        log(_("xml_template_integration.vrf_child_element_lookup_success", tag=vrf.tag, xmlns=vrf.get("xmlns")), "debug")
                        return vrf
                log(_("xml_template_integration.vrf_child_element_lookup_no_result"), "debug")
            except Exception as e:
                log(_("xml_template_integration.vrf_child_element_lookup_failed", error=str(e)), "debug")

        # 如果没有找到VRF节点，创建一个
        if vrf is None:
            log(_("xml_template_integration.vrf_not_found_creating"), "warning")

            # 创建VRF子元素（使用简单的方式，不使用命名空间前缀）
            vrf = etree.SubElement(template_root, "vrf")

            # 添加name子元素
            name_elem = etree.SubElement(vrf, "name")
            name_elem.text = "main"
            log(_("xml_template_integration.vrf_creation_complete"), "debug")
        else:
            log(_("xml_template_integration.vrf_lookup_success"), "debug")

        return vrf

    def _debug_data_context(self, context: DataContext) -> None:
        """
        调试输出DataContext中的所有数据

        Args:
            context: 数据上下文
        """

        # 检查地址对象数据
        address_result = context.get_data("address_processing_result")
        if address_result:
            address_objects = address_result.get("address_objects", {})
            address_groups = address_result.get("address_groups", {})
            log(_("xml_template_integration_stage.debug_address_data"), "debug",
                objects_count=len(address_objects), groups_count=len(address_groups))

            # 输出前5个地址对象的详细信息
            for i, (name, config) in enumerate(list(address_objects.items())[:5]):
                log(_("xml_template_integration_stage.debug_address_object"), "debug",
                    index=i+1, name=name, config=str(config)[:200])
        else:
            log(_("xml_template_integration_stage.debug_no_address_data"), "debug")

        # 检查服务对象数据
        service_result = context.get_data("service_processing_result")
        if service_result:
            service_objects = service_result.get("service_objects", {})
            service_groups = service_result.get("service_groups", {})
            log(_("xml_template_integration_stage.debug_service_data"), "debug",
                objects_count=len(service_objects), groups_count=len(service_groups))

            # 输出前5个服务对象的详细信息
            for i, (name, config) in enumerate(list(service_objects.items())[:5]):
                log(_("xml_template_integration_stage.debug_service_object"), "debug",
                    index=i+1, name=name, config=str(config)[:200])
        else:
            log(_("xml_template_integration_stage.debug_no_service_data"), "debug")

        # 检查安全策略数据 - 修复键名不匹配问题
        policy_result = context.get_data("policy_processing_result")
        if policy_result:
            policies = policy_result.get("policies", {})
            security_policies = policy_result.get("security_policies", [])
            nat_rules = policy_result.get("nat_rules", [])
            log(_("xml_template_integration_stage.debug_policy_data"), "debug",
                policies_count=len(policies), security_policies_count=len(security_policies), nat_rules_count=len(nat_rules))

            # 输出前3个策略的详细信息
            for i, (name, config) in enumerate(list(policies.items())[:3]):
                log(_("xml_template_integration_stage.debug_policy"), "debug",
                    index=i+1, name=name, config=str(config)[:300])
        else:
            log(_("xml_template_integration_stage.debug_no_policy_data"), "debug")

        # 检查接口数据
        interface_result = context.get_data("interface_processing_result")
        if interface_result:
            interfaces = interface_result.get("interfaces", {})
            log(_("xml_template_integration_stage.debug_interface_data"), "debug",
                interfaces_count=len(interfaces))
        else:
            log(_("xml_template_integration_stage.debug_no_interface_data"), "debug")

        log(_("xml_template_integration_stage.debug_data_context_end"), "debug")

    def _validate_ip_address_for_address_set(self, obj_config: Dict) -> str:
        """
        验证地址对象配置是否能生成有效的IP地址（借鉴旧架构验证逻辑）

        Args:
            obj_config: 地址对象配置

        Returns:
            str: 有效的IP地址字符串，如果无效则返回None
        """
        if "type" not in obj_config:
            return None

        addr_type = obj_config["type"]

        if addr_type == "ipv4":
            # 验证IPv4地址和子网掩码
            if "address" in obj_config and "netmask" in obj_config:
                try:
                    import ipaddress
                    network = ipaddress.IPv4Network(f"{obj_config['address']}/{obj_config['netmask']}", strict=False)
                    return str(network)
                except:
                    # 如果转换失败，使用原始格式
                    return f"{obj_config['address']}/{obj_config['netmask']}"
            elif "address" in obj_config:
                return obj_config["address"]
            else:
                return None

        elif addr_type == "range":
            # 验证IP范围
            if "start_ip" in obj_config and "end_ip" in obj_config:
                return f"{obj_config['start_ip']}-{obj_config['end_ip']}"
            else:
                return None

        elif addr_type == "fqdn":
            # 验证FQDN
            if "fqdn" in obj_config:
                return obj_config["fqdn"]
            else:
                return None

        # 其他类型暂不支持
        return None

    def _integrate_single_address_object(self, network_obj_elem: etree.Element, obj_name: str, obj_config: Dict) -> bool:
        """
        集成单个地址对象到network-obj的address-set结构（借鉴旧架构的严格验证）

        Args:
            network_obj_elem: network-obj容器元素
            obj_name: 对象名称
            obj_config: 对象配置

        Returns:
            bool: 集成是否成功
        """
        try:
            # 导入名称验证工具
            from engine.utils.name_validator import clean_ntos_name, validate_ntos_name

            # 清理和验证地址对象名称以符合YANG约束
            original_name = obj_name
            clean_name = clean_ntos_name(obj_name, 64)

            # 验证清理后的名称
            is_valid, error_msg = validate_ntos_name(clean_name)
            if not is_valid:
                log(_("xml_template_integration_stage.address_object_invalid_name"), "warning",
                    original=original_name, cleaned=clean_name, error=error_msg)
                # 使用默认名称
                clean_name = f"addr_{hash(original_name) % 10000}"
                clean_name = clean_ntos_name(clean_name, 64)

            # 记录名称清理过程（如果名称发生了变化）
            if original_name != clean_name:
                log(_("xml_template_integration_stage.address_name_cleaned"), "info",
                    original=original_name, cleaned=clean_name)

            # 借鉴旧架构：先验证IP地址有效性，无效则跳过
            ip_address = self._validate_ip_address_for_address_set(obj_config)
            if not ip_address:
                log(_("xml_template_integration_stage.address_object_invalid_ip"), "warning", name=clean_name)
                return False

            # ✅ 第四阶段重构：使用通用查找方法查找现有address-set
            existing_address_set = None
            address_sets = self._find_elements_robust(network_obj_elem, "address-set")
            for addr_set in address_sets:
                name_elem = self._find_child_element_robust(addr_set, "name")
                if name_elem is not None and name_elem.text == clean_name:
                    existing_address_set = addr_set
                    break

            if existing_address_set is not None:
                # 模板中已存在，修改字段
                address_set_elem = existing_address_set
                log(_("xml_template_integration_stage.address_set_found"), "debug", name=clean_name)
            else:
                # 模板中不存在，插入新的完整块
                address_set_elem = etree.SubElement(network_obj_elem, "address-set")
                log(_("xml_template_integration_stage.address_set_created"), "debug", name=clean_name)

            # ✅ 第四阶段重构：使用通用查找方法设置或更新对象名称
            name_elem = self._find_child_element_robust(address_set_elem, "name")
            if name_elem is None:
                name_elem = etree.SubElement(address_set_elem, "name")
            name_elem.text = clean_name

            # ✅ 第四阶段重构：使用通用查找方法设置或更新对象描述
            if "description" in obj_config:
                from engine.utils.name_validator import clean_ntos_description
                desc_elem = self._find_child_element_robust(address_set_elem, "description")
                if desc_elem is None:
                    desc_elem = etree.SubElement(address_set_elem, "description")
                # 清理描述以符合YANG约束
                clean_description = clean_ntos_description(obj_config["description"], 255)
                desc_elem.text = clean_description

            # ✅ 第四阶段重构：使用通用查找方法查找现有ip-set
            existing_ip_set = None
            ip_sets = self._find_elements_robust(address_set_elem, "ip-set")
            for ip_set in ip_sets:
                ip_addr_elem = self._find_child_element_robust(ip_set, "ip-address")
                if ip_addr_elem is not None and ip_addr_elem.text == ip_address:
                    existing_ip_set = ip_set
                    break

            if existing_ip_set is None:
                # 创建新的ip-set（确保符合YANG约束）
                ip_set_elem = etree.SubElement(address_set_elem, "ip-set")
                ip_addr_elem = etree.SubElement(ip_set_elem, "ip-address")
                ip_addr_elem.text = ip_address
                log(_("xml_template_integration_stage.ip_set_created", name=clean_name, ip=ip_address), "debug")

            log(_("xml_template_integration_stage.address_object_integrated"), "debug", name=clean_name)
            return True

        except Exception as e:
            log(_("xml_template_integration_stage.address_object_integration_failed"), "warning",
                name=clean_name if 'clean_name' in locals() else obj_name, error=str(e))
            return False

    def _integrate_single_address_group(self, network_obj_elem: etree.Element, group_name: str, group_config: Dict) -> bool:
        """
        集成单个地址组到network-obj的address-group结构

        Args:
            network_obj_elem: network-obj容器元素
            group_name: 组名称
            group_config: 组配置

        Returns:
            bool: 集成是否成功
        """
        try:
            # 导入名称验证工具
            from engine.utils.name_validator import clean_ntos_name, validate_ntos_name

            # 清理和验证地址组名称以符合YANG约束
            original_group_name = group_name
            clean_group_name = clean_ntos_name(group_name, 64)

            # 验证清理后的名称
            is_valid, error_msg = validate_ntos_name(clean_group_name)
            if not is_valid:
                log(_("xml_template_integration_stage.address_group_invalid_name"), "warning",
                    original=original_group_name, cleaned=clean_group_name, error=error_msg)
                # 使用默认名称
                clean_group_name = f"group_{hash(original_group_name) % 10000}"
                clean_group_name = clean_ntos_name(clean_group_name, 64)

            # 记录名称清理过程（如果名称发生了变化）
            if original_group_name != clean_group_name:
                log(_("xml_template_integration_stage.address_group_name_cleaned"), "info",
                    original=original_group_name, cleaned=clean_group_name)

            # 根据YANG模型，地址组应该在address-group中
            # 查找是否已存在同名的address-group（使用清理后的名称）
            existing_address_group = None
            for addr_group in network_obj_elem.findall(".//address-group"):
                name_elem = addr_group.find("name")
                if name_elem is not None and name_elem.text == clean_group_name:
                    existing_address_group = addr_group
                    break

            if existing_address_group is not None:
                # 模板中已存在，修改字段
                address_group_elem = existing_address_group
                log(_("xml_template_integration_stage.address_group_found"), "debug", name=clean_group_name)
            else:
                # 模板中不存在，插入新的完整块
                address_group_elem = etree.SubElement(network_obj_elem, "address-group")
                log(_("xml_template_integration_stage.address_group_created"), "debug", name=clean_group_name)

            # 设置或更新组名称（使用清理后的名称）
            name_elem = address_group_elem.find("name")
            if name_elem is None:
                name_elem = etree.SubElement(address_group_elem, "name")
            name_elem.text = clean_group_name

            # 设置或更新组描述（如果有）
            if "description" in group_config:
                from engine.utils.name_validator import clean_ntos_description
                desc_elem = address_group_elem.find("description")
                if desc_elem is None:
                    desc_elem = etree.SubElement(address_group_elem, "description")
                # 清理描述以符合YANG约束
                clean_description = clean_ntos_description(group_config["description"], 255)
                desc_elem.text = clean_description

            # 处理成员 - 根据YANG模型使用address-set结构
            if "members" in group_config:
                members = group_config["members"]
                if not isinstance(members, list):
                    members = [members]

                for member in members:
                    # 清理成员名称以符合YANG约束
                    original_member = member
                    clean_member = clean_ntos_name(member, 64)

                    # 验证清理后的成员名称
                    is_valid, error_msg = validate_ntos_name(clean_member)
                    if not is_valid:
                        log(_("xml_template_integration_stage.address_group_member_invalid_name"), "warning",
                            group=clean_group_name, original=original_member, cleaned=clean_member, error=error_msg)
                        # 使用默认名称
                        clean_member = f"member_{hash(original_member) % 10000}"
                        clean_member = clean_ntos_name(clean_member, 64)

                    # 记录成员名称清理过程（如果名称发生了变化）
                    if original_member != clean_member:
                        log(_("xml_template_integration_stage.address_group_member_name_cleaned"), "info",
                            group=clean_group_name, original=original_member, cleaned=clean_member)

                    # 查找是否已存在相同的address-set成员（使用清理后的名称）
                    existing_member = None
                    for addr_set in address_group_elem.findall("address-set"):
                        name_elem = addr_set.find("name")
                        if name_elem is not None and name_elem.text == clean_member:
                            existing_member = addr_set
                            break

                    if existing_member is None:
                        # 创建新的address-set成员（使用清理后的名称）
                        member_elem = etree.SubElement(address_group_elem, "address-set")
                        member_name_elem = etree.SubElement(member_elem, "name")
                        member_name_elem.text = clean_member

            return True

        except Exception as e:
            log(_("xml_template_integration_stage.address_group_integration_failed"), "warning",
                name=clean_group_name if 'clean_group_name' in locals() else group_name, error=str(e))
            return False

    def _normalize_port_range_for_ntos(self, port_range: str) -> str:
        """
        将端口范围格式标准化为NTOS要求的格式

        Args:
            port_range: 原始端口范围字符串

        Returns:
            str: 标准化后的端口范围，符合NTOS格式要求
        """
        if not port_range:
            return port_range

        # 移除首尾空格
        port_range = port_range.strip()

        # 处理空格分隔的多个端口：500 4500 -> 500,4500
        if " " in port_range and ":" not in port_range:
            ports = port_range.split()
            # 过滤有效端口
            valid_ports = []
            for port in ports:
                if port.isdigit() and 1 <= int(port) <= 65535:
                    valid_ports.append(port)
                elif "-" in port:
                    try:
                        start, end = map(int, port.split("-"))
                        if 1 <= start <= end <= 65535:
                            valid_ports.append(port)
                    except ValueError:
                        continue
            if valid_ports:
                return ",".join(valid_ports)

        # 处理冒号格式：513:512-1023 -> 513 (取目标端口)
        if ":" in port_range:
            parts = port_range.split(":")
            if len(parts) >= 2:
                # 优先使用目标端口（第一部分）
                dst_port = parts[0].strip()
                if dst_port:
                    # 特殊处理webproxy的0-65535:0-65535格式
                    if dst_port == "0-65535":
                        return "1-65535"
                    # 递归处理目标端口
                    normalized_dst = self._normalize_port_range_for_ntos(dst_port)
                    if normalized_dst:
                        return normalized_dst

                # 如果目标端口无效，尝试源端口
                src_port = parts[1].strip()
                if src_port:
                    # 特殊处理源端口的0-65535格式
                    if src_port == "0-65535":
                        return "1-65535"
                    # 递归处理源端口
                    normalized_src = self._normalize_port_range_for_ntos(src_port)
                    if normalized_src:
                        return normalized_src

        # 处理特殊的全端口范围
        if port_range == "0-65535":
            return "1-65535"

        # 处理webproxy特殊格式：0-65535:0-65535 -> 1-65535
        if port_range == "0-65535:0-65535":
            return "1-65535"

        # 对于已经是正确格式的端口范围，直接返回
        # 如：80, 80-90, 21,22,1024-2048
        return port_range

    def _integrate_single_service_object(self, service_obj_elem: etree.Element, obj_name: str, obj_config: Dict) -> bool:
        """
        集成单个服务对象到service-obj的service-set结构

        Args:
            service_obj_elem: service-obj容器元素
            obj_name: 对象名称
            obj_config: 对象配置

        Returns:
            bool: 集成是否成功
        """
        try:
            # ✅ 第四阶段重构：使用通用查找方法查找现有service-set
            existing_service_set = None
            service_sets = self._find_elements_robust(service_obj_elem, "service-set")
            for service_set in service_sets:
                name_elem = self._find_child_element_robust(service_set, "name")
                if name_elem is not None and name_elem.text == obj_name:
                    existing_service_set = service_set
                    break

            if existing_service_set is not None:
                # 模板中已存在，修改字段
                service_set_elem = existing_service_set
            else:
                # 模板中不存在，插入新的完整块
                service_set_elem = etree.SubElement(service_obj_elem, "service-set")

            # ✅ 第四阶段重构：使用通用查找方法设置或更新对象名称
            name_elem = self._find_child_element_robust(service_set_elem, "name")
            if name_elem is None:
                name_elem = etree.SubElement(service_set_elem, "name")

            # 对于mapped类型的服务对象，使用映射后的名称
            if obj_config.get("type") == "predefined" or obj_config.get("type") == "vendor_mapped":
                # 使用映射后的服务名称
                mapped_name = obj_config.get("name", obj_name)
                name_elem.text = mapped_name
                log(_("xml_template_integration_stage.using_mapped_service_name"), "debug",
                    original=obj_name, mapped=mapped_name)
            else:
                name_elem.text = obj_name

            # ✅ 第四阶段重构：使用通用查找方法设置或更新对象描述
            description = obj_config.get("description", "")
            if not description and obj_config.get("type") in ["predefined", "vendor_mapped"]:
                # 为mapped类型的服务对象生成描述
                original_name = obj_config.get("original_name", obj_name)
                description = f"Mapped from {original_name}"

            if description:
                desc_elem = self._find_child_element_robust(service_set_elem, "description")
                if desc_elem is None:
                    desc_elem = etree.SubElement(service_set_elem, "description")
                desc_elem.text = description

            # 对于预定义和映射的服务，跳过端口配置
            if obj_config.get("type") in ["predefined", "vendor_mapped"]:
                log(_("xml_template_integration_stage.service_object_mapped_skip_ports"), "debug",
                    name=obj_name, type=obj_config.get("type"))
                return True

            # 处理服务配置 - 根据YANG模型直接在service-set下创建协议容器
            if "protocol" in obj_config:
                protocol = obj_config["protocol"].lower()

                if protocol == "tcp":
                    # TCP服务 - 直接在service-set下创建tcp容器
                    tcp_elem = etree.SubElement(service_set_elem, "tcp")

                    # 添加目的端口信息 - 应用NTOS格式标准化
                    if "port_range" in obj_config:
                        port_range = obj_config["port_range"]
                        normalized_port = self._normalize_port_range_for_ntos(port_range)
                        dest_port_elem = etree.SubElement(tcp_elem, "dest-port")
                        dest_port_elem.text = normalized_port

                    # 添加源端口信息 - 根据NTOS YANG模型要求，源端口是必填的
                    source_port_elem = etree.SubElement(tcp_elem, "source-port")
                    source_port_elem.text = "0-65535"  # 默认全端口范围

                elif protocol == "udp":
                    # UDP服务 - 直接在service-set下创建udp容器
                    udp_elem = etree.SubElement(service_set_elem, "udp")

                    # 添加目的端口信息 - 应用NTOS格式标准化
                    if "port_range" in obj_config:
                        port_range = obj_config["port_range"]
                        normalized_port = self._normalize_port_range_for_ntos(port_range)
                        dest_port_elem = etree.SubElement(udp_elem, "dest-port")
                        dest_port_elem.text = normalized_port

                    # 添加源端口信息 - 根据NTOS YANG模型要求，源端口是必填的
                    source_port_elem = etree.SubElement(udp_elem, "source-port")
                    source_port_elem.text = "0-65535"  # 默认全端口范围
                elif protocol == "icmp":
                    # ICMP服务 - 直接在service-set下创建icmp列表
                    icmp_elem = etree.SubElement(service_set_elem, "icmp")

                    # 添加type和code（如果有）
                    icmp_type = obj_config.get("icmp_type", "8")  # 默认ping
                    icmp_code = obj_config.get("icmp_code", "0")  # 默认code

                    type_elem = etree.SubElement(icmp_elem, "type")
                    type_elem.text = str(icmp_type)

                    code_elem = etree.SubElement(icmp_elem, "code")
                    code_elem.text = str(icmp_code)

                elif protocol == "ip":
                    # IP协议服务 - 直接在service-set下创建protocol-id
                    if "protocol_number" in obj_config:
                        proto_id_elem = etree.SubElement(service_set_elem, "protocol-id")
                        proto_id_elem.text = str(obj_config["protocol_number"])

            # 处理同时包含TCP和UDP的服务（如DNS）- 应用NTOS格式标准化
            if "tcp_port_range" in obj_config:
                tcp_elem = etree.SubElement(service_set_elem, "tcp")
                dest_port_elem = etree.SubElement(tcp_elem, "dest-port")
                normalized_tcp_port = self._normalize_port_range_for_ntos(obj_config["tcp_port_range"])
                dest_port_elem.text = normalized_tcp_port

                # 添加TCP源端口
                source_port_elem = etree.SubElement(tcp_elem, "source-port")
                source_port_elem.text = "0-65535"

            if "udp_port_range" in obj_config:
                udp_elem = etree.SubElement(service_set_elem, "udp")
                dest_port_elem = etree.SubElement(udp_elem, "dest-port")
                normalized_udp_port = self._normalize_port_range_for_ntos(obj_config["udp_port_range"])
                dest_port_elem.text = normalized_udp_port

                # 添加UDP源端口
                source_port_elem = etree.SubElement(udp_elem, "source-port")
                source_port_elem.text = "0-65535"

            log(_("xml_template_integration_stage.service_object_integrated"), "debug", name=obj_name)
            return True

        except Exception as e:
            log(_("xml_template_integration_stage.service_object_integration_failed"), "warning",
                name=obj_name, error=str(e))
            return False

    def _integrate_single_service_group(self, service_obj_elem: etree.Element, group_name: str, group_config: Dict) -> bool:
        """
        集成单个服务组到service-obj的service-group结构

        Args:
            service_obj_elem: service-obj容器元素
            group_name: 组名称
            group_config: 组配置

        Returns:
            bool: 集成是否成功
        """
        try:
            # 根据YANG模型，服务组应该在service-group中
            # 查找是否已存在同名的service-group
            existing_service_group = None
            for service_group in service_obj_elem.findall(".//service-group"):
                name_elem = service_group.find("name")
                if name_elem is not None and name_elem.text == group_name:
                    existing_service_group = service_group
                    break

            if existing_service_group is not None:
                # 模板中已存在，修改字段
                service_group_elem = existing_service_group
                log(_("xml_template_integration_stage.service_group_found"), "debug", name=group_name)
            else:
                # 模板中不存在，插入新的完整块
                service_group_elem = etree.SubElement(service_obj_elem, "service-group")
                log(_("xml_template_integration_stage.service_group_created"), "debug", name=group_name)

            # 设置或更新组名称
            name_elem = service_group_elem.find("name")
            if name_elem is None:
                name_elem = etree.SubElement(service_group_elem, "name")
            name_elem.text = group_name

            # 设置或更新组描述（如果有）
            if "description" in group_config:
                desc_elem = service_group_elem.find("description")
                if desc_elem is None:
                    desc_elem = etree.SubElement(service_group_elem, "description")
                desc_elem.text = group_config["description"]

            # 处理成员 - 根据YANG模型使用service-set结构
            if "members" in group_config:
                members = group_config["members"]
                if not isinstance(members, list):
                    members = [members]

                for member in members:
                    # 查找是否已存在相同的service-set成员
                    existing_member = None
                    for service_set in service_group_elem.findall("service-set"):
                        name_elem = service_set.find("name")
                        if name_elem is not None and name_elem.text == member:
                            existing_member = service_set
                            break

                    if existing_member is None:
                        # 创建新的service-set成员
                        member_elem = etree.SubElement(service_group_elem, "service-set")
                        member_name_elem = etree.SubElement(member_elem, "name")
                        member_name_elem.text = member

            log(_("xml_template_integration_stage.service_group_integrated"), "debug", name=group_name)
            return True

        except Exception as e:
            log(_("xml_template_integration_stage.service_group_integration_failed"), "warning",
                name=group_name, error=str(e))
            return False

    def _apply_xml_formatting_fixes(self, xml_string: str) -> str:
        """
        应用XML格式化修复，解决压缩格式问题和重复属性问题

        Args:
            xml_string: 原始XML字符串

        Returns:
            str: 格式化修复后的XML字符串
        """
        try:
            from lxml import etree
            import re

            # 解析XML字符串
            root = etree.fromstring(xml_string.encode('utf-8'))

            # 在格式化前先清理所有元素的重复xmlns属性
            self._clean_all_duplicate_xmlns_attributes(root)

            # 特别清理所有policy元素的xmlns属性
            self._clean_all_policy_elements_xmlns(root)

            # 重新生成格式化的XML
            formatted_xml = etree.tostring(
                root,
                encoding='unicode',
                pretty_print=True,
                xml_declaration=False
            )

            # 修复YANG验证问题：移除有害的格式化修复
            # 注释掉导致布尔值和其他文本值包含换行符的正则表达式

            # 原有的格式化修复会导致YANG验证失败，因为它们在元素文本中插入换行符
            # 例如：<enabled>true</enabled> 变成 <enabled>true\n</enabled>
            # 这违反了YANG模型对布尔值的要求

            # 1. 确保每个主要元素都在新行 - 这个修复是安全的
            formatted_xml = re.sub(r'><([^/])', r'>\n<\1', formatted_xml)

            # 2. 注释掉有害的格式化修复 - 这个修复会破坏元素文本值
            # formatted_xml = re.sub(r'([^>\n])</([^>]+)>', r'\1\n</\2>', formatted_xml)

            # 3. 修复过度的换行 - 保留这个修复，因为它不会影响元素文本
            formatted_xml = re.sub(r'\n\s*\n\s*\n', r'\n\n', formatted_xml)

            # 4. 清理元素文本中的换行符和多余空白字符
            formatted_xml = self._clean_element_text_values(formatted_xml)

            # 5. 最终的重复xmlns属性清理（字符串级别）
            formatted_xml = self._clean_duplicate_xmlns_in_string(formatted_xml)

            # 6. 最终的policy元素xmlns属性清理（字符串级别）
            formatted_xml = self._clean_policy_xmlns_in_string(formatted_xml)

            # 如果格式化修复失败，返回原始字符串
            return formatted_xml

        except Exception as e:
            error_msg = _("xml_template_integration_stage.xml_formatting_fixes_failed", error=str(e))
            log(error_msg, "warning")
            # 如果格式化修复失败，返回原始字符串
            return xml_string

    def _clean_element_text_values(self, xml_string: str) -> str:
        """
        清理XML元素文本值中的换行符和多余空白字符，并修复IP地址格式

        这个方法专门用于修复YANG验证问题，确保元素文本值不包含换行符
        并且IP地址格式符合YANG模型要求

        Args:
            xml_string: 需要清理的XML字符串

        Returns:
            str: 清理后的XML字符串
        """
        try:
            from lxml import etree
            import re

            # 解析XML
            root = etree.fromstring(xml_string.encode('utf-8'))

            # 递归清理所有元素的文本值
            def clean_element_text(element):
                # 清理当前元素的文本
                if element.text:
                    # 移除换行符和多余的空白字符，但保留单个空格
                    cleaned_text = re.sub(r'\s+', ' ', element.text.strip())
                    element.text = cleaned_text

                # 清理尾部文本
                if element.tail:
                    cleaned_tail = re.sub(r'\s+', ' ', element.tail.strip())
                    element.tail = cleaned_tail if cleaned_tail else None

                # 递归处理子元素
                for child in element:
                    clean_element_text(child)

            # 开始清理
            clean_element_text(root)

            # 修复IP地址格式问题
            self._fix_ip_address_formats(root)

            # 重新生成XML字符串
            cleaned_xml = etree.tostring(
                root,
                encoding='unicode',
                pretty_print=True,
                xml_declaration=False
            )

            return cleaned_xml

        except Exception as e:
            log(_("xml_template_integration_stage.text_cleaning_error", error=str(e)), "warning")
            return xml_string

    def _fix_ip_address_formats(self, root: etree.Element):
        """
        修复XML中的IP地址格式问题，确保符合YANG模型要求

        主要修复：
        1. 将CIDR格式的IP地址拆分为IP和前缀长度
        2. 确保IP地址格式符合YANG模型规范

        Args:
            root: XML根元素
        """
        try:
            import re

            # 查找所有可能包含IP地址的元素
            ip_elements = []

            # 查找接口IP配置
            for ip_elem in root.xpath(".//ip[text()]"):
                ip_elements.append(ip_elem)

            # 查找地址对象IP配置
            for ip_addr_elem in root.xpath(".//ip-address[text()]"):
                ip_elements.append(ip_addr_elem)

            # 查找其他IP相关元素
            for elem in root.xpath(".//*[contains(local-name(), 'ip') and text()]"):
                if elem not in ip_elements:
                    ip_elements.append(elem)

            # 处理每个IP元素
            for elem in ip_elements:
                if elem.text and '/' in elem.text:
                    # 检查是否为CIDR格式
                    ip_text = elem.text.strip()
                    if self._is_cidr_format(ip_text):
                        # 根据元素类型决定如何处理
                        parent = elem.getparent()
                        if parent is not None:
                            self._convert_cidr_to_yang_format(elem, parent, ip_text)

        except Exception as e:
            log(_("xml_template_integration_stage.ip_format_fix_error", error=str(e)), "warning")

    def _is_cidr_format(self, ip_text: str) -> bool:
        """
        检查是否为CIDR格式的IP地址

        Args:
            ip_text: IP地址文本

        Returns:
            bool: 是否为CIDR格式
        """
        try:
            import re
            # 匹配 IP/前缀长度 格式
            cidr_pattern = r'^(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})/(\d{1,2})$'
            return bool(re.match(cidr_pattern, ip_text))
        except:
            return False

    def _convert_cidr_to_yang_format(self, ip_elem: etree.Element, parent: etree.Element, cidr_text: str):
        """
        将CIDR格式转换为YANG模型要求的格式

        Args:
            ip_elem: IP元素
            parent: 父元素
            cidr_text: CIDR格式的IP地址
        """
        try:
            # 拆分IP地址和前缀长度
            ip_addr, prefix_len = cidr_text.split('/')

            # 根据元素名称和上下文决定转换策略
            elem_name = ip_elem.tag.split('}')[-1] if '}' in ip_elem.tag else ip_elem.tag

            if elem_name == 'ip-address':
                # 对于地址对象，保持CIDR格式（符合ipv4-prefix类型）
                ip_elem.text = cidr_text
            elif elem_name == 'ip':
                # 对于接口IP配置，可能需要拆分为IP和前缀长度
                # 检查是否有对应的prefix-length或netmask元素
                prefix_elem = parent.find('.//prefix-length')
                netmask_elem = parent.find('.//netmask')

                if prefix_elem is not None:
                    # 如果有prefix-length元素，拆分
                    ip_elem.text = ip_addr
                    prefix_elem.text = prefix_len
                elif netmask_elem is not None:
                    # 如果有netmask元素，转换前缀长度为子网掩码
                    ip_elem.text = ip_addr
                    netmask = self._prefix_to_netmask(int(prefix_len))
                    netmask_elem.text = netmask
                else:
                    # 没有对应元素，保持CIDR格式
                    ip_elem.text = cidr_text
            else:
                # 其他情况，保持原格式
                ip_elem.text = cidr_text

        except Exception as e:
            log(_("xml_template_integration_stage.cidr_conversion_error",
                  cidr=cidr_text, error=str(e)), "warning")

    def _prefix_to_netmask(self, prefix_len: int) -> str:
        """
        将前缀长度转换为子网掩码

        Args:
            prefix_len: 前缀长度

        Returns:
            str: 子网掩码
        """
        try:
            # 创建32位的掩码
            mask = (0xffffffff >> (32 - prefix_len)) << (32 - prefix_len)
            # 转换为点分十进制格式
            return f"{(mask >> 24) & 0xff}.{(mask >> 16) & 0xff}.{(mask >> 8) & 0xff}.{mask & 0xff}"
        except:
            return "*************"  # 默认掩码

    def _integrate_single_security_policy(self, security_policy_elem: etree.Element, policy_name: str, policy_config: Dict) -> bool:
        """
        集成单个安全策略到security-policy结构

        Args:
            security_policy_elem: security-policy容器元素
            policy_name: 策略名称
            policy_config: 策略配置

        Returns:
            bool: 集成是否成功
        """
        try:
            # ✅ 第五阶段重构：使用通用查找方法查找现有rule
            existing_rule = None
            rules = self._find_elements_robust(security_policy_elem, "rule")
            for rule in rules:
                name_elem = self._find_child_element_robust(rule, "name")
                if name_elem is not None and name_elem.text == policy_name:
                    existing_rule = rule
                    break

            if existing_rule is not None:
                # 模板中已存在，修改字段
                rule_elem = existing_rule
            else:
                # 模板中不存在，插入新的完整块
                rule_elem = etree.SubElement(security_policy_elem, "rule")

            # ✅ 第五阶段重构：使用通用查找或创建方法设置策略字段
            # 设置或更新策略名称
            name_elem = self._find_or_create_child_element(rule_elem, "name")
            name_elem.text = policy_name

            # 设置或更新策略状态（enabled）
            enabled_elem = self._find_or_create_child_element(rule_elem, "enabled")
            enabled_elem.text = "true" if policy_config.get("enabled", True) else "false"

            # 设置或更新源区域
            if "source_zone" in policy_config:
                source_zone_elem = self._find_or_create_child_element(rule_elem, "source-zone")
                source_zone_elem.text = policy_config["source_zone"]

            # 设置或更新目标区域
            if "destination_zone" in policy_config:
                dest_zone_elem = self._find_or_create_child_element(rule_elem, "destination-zone")
                dest_zone_elem.text = policy_config["destination_zone"]

            # 设置或更新源地址
            if "source_address" in policy_config:
                source_addr_elem = self._find_or_create_child_element(rule_elem, "source-address")
                source_addr_elem.text = policy_config["source_address"]

            # 设置或更新目标地址
            if "destination_address" in policy_config:
                dest_addr_elem = self._find_or_create_child_element(rule_elem, "destination-address")
                dest_addr_elem.text = policy_config["destination_address"]

            # 设置或更新服务
            if "service" in policy_config:
                service_elem = self._find_or_create_child_element(rule_elem, "service")
                service_elem.text = policy_config["service"]

            # ✅ 第五阶段重构：继续使用通用方法设置策略字段
            # 设置或更新动作
            action_elem = self._find_or_create_child_element(rule_elem, "action")
            action_elem.text = policy_config.get("action", "permit")

            # 设置或更新时间范围 - security-policy中time-range是leaf元素，不应该包含子元素
            time_range_value = policy_config.get("time-range", "any")
            time_range_elem = self._find_or_create_child_element(rule_elem, "time-range")
            time_range_elem.text = time_range_value
            # 清理可能存在的错误子元素（如value）
            for child in list(time_range_elem):
                time_range_elem.remove(child)

            # 设置或更新配置源
            config_source_elem = self._find_or_create_child_element(rule_elem, "config-source")
            config_source_elem.text = "manual"

            # 设置或更新会话超时
            session_timeout_elem = self._find_or_create_child_element(rule_elem, "session-timeout")
            session_timeout_elem.text = str(policy_config.get("session_timeout", 1800))

            # ✅ 第五阶段重构：使用通用方法设置安全功能
            if "security_profiles" in policy_config:
                profiles = policy_config["security_profiles"]

                # IPS配置
                if "ips" in profiles:
                    ips_elem = self._find_or_create_child_element(rule_elem, "ips")
                    ips_elem.text = profiles["ips"]

                # AV配置
                if "av" in profiles:
                    av_elem = self._find_or_create_child_element(rule_elem, "av")
                    av_elem.text = profiles["av"]

            log(_("xml_template_integration_stage.security_policy_integrated"), "debug", name=policy_name)
            return True

        except Exception as e:
            log(_("xml_template_integration_stage.security_policy_integration_failed"), "error",
                name=policy_name, error=str(e))
            return False

    def _fix_all_interface_working_modes(self, template_root: etree.Element, context: DataContext) -> bool:
        """
        修复所有接口的工作模式为route模式（路由模式下）

        Args:
            template_root: XML模板根元素
            context: 数据上下文

        Returns:
            bool: 修复是否成功
        """
        try:
            # ✅ 第五阶段重构：使用通用查找方法和简化统计逻辑
            # 查找所有的working-mode元素
            working_mode_elements = self._find_elements_robust(template_root, "working-mode")

            # 统计修复前的数量并修复
            bridge_count_before = 0
            route_count_before = 0
            fixed_count = 0

            for elem in working_mode_elements:
                if elem.text == "bridge":
                    bridge_count_before += 1
                    elem.text = "route"
                    fixed_count += 1
                elif elem.text == "route":
                    route_count_before += 1

            # 修复后的统计（所有bridge都变成了route）
            route_count_after = route_count_before + fixed_count
            bridge_count_after = 0

            log(_("xml_template_integration.interface_mode_fix_statistics_after", bridge=bridge_count_after, route=route_count_after), "info")
            log(_("xml_template_integration.interface_mode_fix_success", count=fixed_count), "info")

            return True

        except Exception as e:
            log(_("xml_template_integration.interface_mode_fix_error", error=str(e)), "error")
            return False

    def _update_bandwidth_configuration(self, existing_physical: etree.Element, new_physical: etree.Element):
        """
        更新带宽配置（采用检查-修改-创建策略）

        策略：检查是否存在带宽标签，存在则修改，不存在则创建

        Args:
            existing_physical: 现有的物理接口节点
            new_physical: 新的物理接口节点
        """
        try:
            log(_("xml_template_integration.update_bandwidth_configuration_start"), "info")

            # ✅ 第五阶段重构：使用通用带宽配置更新方法
            self._update_single_bandwidth_config(existing_physical, new_physical, "upload-bandwidth")
            self._update_single_bandwidth_config(existing_physical, new_physical, "download-bandwidth")

            log(_("xml_template_integration.update_bandwidth_configuration_complete"), "info")

        except Exception as e:
            log(_("xml_template_integration.update_bandwidth_configuration_failed", error=str(e)), "error")
            import traceback
            log(_("xml_template_integration.detailed_error_info", error=traceback.format_exc()), "error")

    def _find_bandwidth_config(self, element: etree.Element, bandwidth_type: str) -> etree.Element:
        """
        查找带宽配置元素（支持多种查找方式）

        ✅ 已重构：使用通用XML查找方法

        Args:
            element: 要搜索的元素
            bandwidth_type: 带宽类型 ("upload-bandwidth" 或 "download-bandwidth")

        Returns:
            etree.Element: 找到的带宽配置元素，如果没找到则返回None
        """
        # 使用通用查找方法
        return self._find_child_element_robust(
            element,
            bandwidth_type,
            namespace="urn:ruijie:ntos:params:xml:ns:yang:interface"
        )

    def _clean_element_namespace(self, element: etree.Element):
        """
        清理元素及其子元素的命名空间
        Args:
            element: 要清理的元素
        """
        # 清理当前元素的命名空间
        if element.tag.startswith('{'):
            element.tag = element.tag.split('}')[-1]

        # 递归清理子元素的命名空间
        for child in element.iter():
            if child.tag.startswith('{'):
                child.tag = child.tag.split('}')[-1]
