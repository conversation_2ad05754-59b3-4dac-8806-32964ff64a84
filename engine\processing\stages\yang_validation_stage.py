# -*- coding: utf-8 -*-
"""
YANG验证阶段 - 使用YANG模型验证生成的XML配置
确保转换结果符合NTOS YANG模型规范
"""

import os
import tempfile
from typing import Dict, Any, Optional, Tuple, List
from engine.utils.logger import log
from engine.utils.i18n import _
from engine.processing.pipeline.pipeline_stage import PipelineStage
from engine.processing.pipeline.data_flow import DataContext


class YangValidationStage(PipelineStage):
    """
    YANG验证阶段
    使用YANG模型验证生成的XML配置是否符合规范
    """
    
    def __init__(self, config_manager, yang_manager):
        """
        初始化YANG验证阶段
        
        Args:
            config_manager: 配置管理器
            yang_manager: YANG管理器
        """
        super().__init__(
            name="yang_validation",
            description="YANG验证阶段 - 使用YANG模型验证XML配置"
        )
        
        self.config_manager = config_manager
        self.yang_manager = yang_manager
        
        log(_("yang_validation_stage.initialized"), "info")
    
    def validate_input(self, context: DataContext) -> bool:
        """
        验证输入数据
        
        Args:
            context: 数据上下文
            
        Returns:
            bool: 验证是否通过
        """
        # 检查是否有生成的XML
        generated_xml = context.get_data("generated_xml")
        if not generated_xml:
            context.add_error(_("yang_validation_stage.no_generated_xml"))
            return False
        
        # 检查模型和版本信息
        model = context.get_data("model")
        version = context.get_data("version")
        if not model or not version:
            context.add_error(_("yang_validation_stage.missing_model_version"))
            return False
        
        # 检查yanglint是否可用
        if not self.yang_manager.is_yanglint_available():
            context.add_warning(_("yang_validation_stage.yanglint_not_available"))
            # yanglint不可用不是致命错误，可以跳过验证
            return True
        
        return True
    
    def process(self, context: DataContext) -> bool:
        """
        处理YANG验证
        
        Args:
            context: 数据上下文
            
        Returns:
            bool: 处理是否成功
        """
        try:
            log(_("yang_validation_stage.start_processing"), "info")
            
            # 如果yanglint不可用，跳过验证
            if not self.yang_manager.is_yanglint_available():
                log(_("yang_validation_stage.skipping_validation"), "info")

                # 提供详细的诊断信息
                diagnostic_info = self._generate_yanglint_diagnostic_info(context)

                context.set_data("yang_validation_result", {
                    "performed": False,
                    "skipped": True,
                    "reason": "yanglint_not_available",
                    "diagnostic_info": diagnostic_info,
                    "validation_performed": False,  # 兼容性字段
                    "validation_passed": False,     # 兼容性字段
                    "message": "YANG验证已跳过：yanglint工具不可用"
                })

                # 记录诊断信息
                log(f"YANG验证诊断信息: {diagnostic_info}", "info")
                return True
            
            # 阶段1：准备验证环境
            temp_xml_file = self._prepare_validation_environment(context)
            if not temp_xml_file:
                return False
            
            try:
                # 阶段2：执行YANG验证
                validation_result = self._execute_yang_validation(temp_xml_file, context)
                
                # 阶段3：分析验证结果
                analysis_result = self._analyze_validation_result(validation_result, context)
                
                # 阶段4：生成验证报告
                self._generate_validation_report(validation_result, analysis_result, context)
                
                log(_("yang_validation_stage.processing_completed"), "info")
                return True
                
            finally:
                # 清理临时文件
                if os.path.exists(temp_xml_file):
                    os.unlink(temp_xml_file)
            
        except Exception as e:
            error_msg = _("yang_validation_stage.processing_exception", error=str(e))
            context.add_error(error_msg)
            log(error_msg, "error")
            return False

    def _generate_yanglint_diagnostic_info(self, context: DataContext) -> dict:
        """
        生成yanglint诊断信息

        Args:
            context: 数据上下文

        Returns:
            dict: 诊断信息
        """
        import subprocess
        import os

        diagnostic = {
            "yanglint_available": False,
            "yang_models_available": False,
            "yang_model_path": None,
            "yang_file_count": 0,
            "installation_guide": None,
            "alternative_validation": "内置验证规则已应用"
        }

        try:
            # 使用改进的yanglint检测逻辑
            yanglint_available = self.yang_manager.is_yanglint_available()
            diagnostic["yanglint_available"] = yanglint_available

            if yanglint_available:
                # 如果检测成功，尝试获取版本信息
                try:
                    result = subprocess.run(['yanglint', '--version'],
                                          capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:
                        diagnostic["yanglint_version"] = result.stdout.strip()
                except:
                    diagnostic["yanglint_version"] = "版本信息获取失败"
            else:
                diagnostic["installation_guide"] = {
                    "windows": "请从 https://github.com/CESNET/libyang 下载并安装yanglint工具",
                    "ubuntu": "sudo apt-get install libyang-tools",
                    "centos": "sudo yum install libyang-tools",
                    "manual": "编译安装：git clone https://github.com/CESNET/libyang && cd libyang && mkdir build && cd build && cmake .. && make && sudo make install"
                }

            # 检查YANG模型文件
            model = context.get_data("model", "z5100s")
            version = context.get_data("version", "R11")
            yang_model_path = os.path.join("engine", "data", "input", "yang_models", model, version)

            if os.path.exists(yang_model_path):
                diagnostic["yang_models_available"] = True
                diagnostic["yang_model_path"] = yang_model_path

                # 统计YANG文件数量
                yang_files = []
                for root, dirs, files in os.walk(yang_model_path):
                    yang_files.extend([f for f in files if f.endswith(('.yang', '.yin'))])

                diagnostic["yang_file_count"] = len(yang_files)
                diagnostic["sample_yang_files"] = yang_files[:5]  # 显示前5个文件作为示例
            else:
                diagnostic["yang_model_path"] = yang_model_path
                diagnostic["error"] = f"YANG模型目录不存在: {yang_model_path}"

        except Exception as e:
            diagnostic["diagnostic_error"] = str(e)

        return diagnostic
    
    def _prepare_validation_environment(self, context: DataContext) -> Optional[str]:
        """
        准备验证环境
        
        Args:
            context: 数据上下文
            
        Returns:
            Optional[str]: 临时XML文件路径
        """
        try:
            generated_xml = context.get_data("generated_xml")
            
            # 创建临时XML文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.xml', delete=False, encoding='utf-8') as temp_file:
                temp_file.write(generated_xml)
                temp_xml_file = temp_file.name
            
            log(_("yang_validation_stage.temp_file_created", file=temp_xml_file), "debug")
            return temp_xml_file
            
        except Exception as e:
            error_msg = _("yang_validation_stage.temp_file_creation_failed", error=str(e))
            context.add_error(error_msg)
            log(error_msg, "error")
            return None
    
    def _execute_yang_validation(self, xml_file: str, context: DataContext) -> Dict[str, Any]:
        """
        执行YANG验证
        
        Args:
            xml_file: XML文件路径
            context: 数据上下文
            
        Returns: Dict[str, Any]: 验证结果
        """
        try:
            model = context.get_data("model")
            version = context.get_data("version")
            
            # 使用YANG管理器进行验证
            validation_passed, validation_message = self.yang_manager.validate_xml_against_yang(
                xml_file, model, version
            )
            
            validation_result = {
                "performed": True,
                "passed": validation_passed,
                "message": validation_message,
                "model": model,
                "version": version,
                "xml_file": xml_file
            }
            
            log(_("yang_validation_stage.validation_executed",
                  passed=validation_passed, model=model, version=version), "info")
            
            return validation_result
            
        except Exception as e:
            error_msg = _("yang_validation_stage.validation_execution_failed", error=str(e))
            log(error_msg, "error")
            return {
                "performed": True,
                "passed": False,
                "message": error_msg,
                "error": str(e)
            }
    
    def _analyze_validation_result(self, validation_result: Dict[str, Any], context: DataContext) -> Dict[str, Any]:
        """
        分析验证结果
        
        Args:
            validation_result: 验证结果
            context: 数据上下文
            
        Returns: Dict[str, Any]: 分析结果
        """
        analysis = {
            "validation_passed": validation_result.get("passed", False),
            "has_errors": False,
            "has_warnings": False,
            "error_categories": [],
            "warning_categories": [],
            "recommendations": []
        }
        
        if not validation_result.get("passed", False):
            analysis["has_errors"] = True
            
            # 分析错误消息
            message = validation_result.get("message", "")
            analysis["error_categories"] = self._categorize_validation_errors(message)
            analysis["recommendations"] = self._generate_error_recommendations(analysis["error_categories"])
            
            # 添加错误到上下文
            context.add_error(_("yang_validation_stage.validation_failed", message=message))
        else:
            log(_("yang_validation_stage.validation_passed"), "info")
        
        return analysis
    
    def _categorize_validation_errors(self, error_message: str) -> List[str]:
        """
        分类验证错误
        
        Args:
            error_message: 错误消息
            
        Returns: List[str]: 错误分类列表
        """
        categories = []
        
        error_patterns = {
            "namespace": ["namespace", "xmlns"],
            "structure": ["element", "node", "structure"],
            "data_type": ["type", "value", "format"],
            "mandatory": ["mandatory", "required"],
            "constraint": ["constraint", "restriction"]
        }
        
        message_lower = error_message.lower()
        
        for category, patterns in error_patterns.items():
            if any(pattern in message_lower for pattern in patterns):
                categories.append(category)
        
        return categories if categories else ["unknown"]
    
    def _generate_error_recommendations(self, error_categories: List[str]) -> List[str]:
        """
        生成错误修复建议
        
        Args:
            error_categories: 错误分类列表
            
        Returns: List[str]: 修复建议列表
        """
        recommendations = []
        
        recommendation_map = {
            "namespace": _("yang_validation_stage.recommendation_namespace"),
            "structure": _("yang_validation_stage.recommendation_structure"),
            "data_type": _("yang_validation_stage.recommendation_data_type"),
            "mandatory": _("yang_validation_stage.recommendation_mandatory"),
            "constraint": _("yang_validation_stage.recommendation_constraint")
        }
        
        for category in error_categories:
            if category in recommendation_map:
                recommendations.append(recommendation_map[category])
        
        if not recommendations:
            recommendations.append(_("yang_validation_stage.recommendation_general"))
        
        return recommendations
    
    def _generate_validation_report(self, validation_result: Dict[str, Any], 
                                  analysis_result: Dict[str, Any], context: DataContext):
        """
        生成验证报告
        
        Args:
            validation_result: 验证结果
            analysis_result: 分析结果
            context: 数据上下文
        """
        report = {
            "validation_performed": validation_result.get("performed", False),
            "validation_passed": validation_result.get("passed", False),
            "validation_message": validation_result.get("message", ""),
            "model": validation_result.get("model"),
            "version": validation_result.get("version"),
            "analysis": analysis_result,
            "timestamp": context.metadata.get("created_at"),
            "yanglint_available": self.yang_manager.is_yanglint_available()
        }
        
        # 存储验证报告
        context.set_data("yang_validation_result", report)
        
        # 记录验证统计
        if validation_result.get("passed", False):
            log(_("yang_validation_stage.validation_report_success"), "info")
        else:
            log(_("yang_validation_stage.validation_report_failure"), "warning",
                errors=len(analysis_result.get("error_categories", [])))
        
        # 如果验证失败但不是致命错误，添加警告而不是错误
        if not validation_result.get("passed", False):
            context.add_warning(_("yang_validation_stage.validation_failed_warning",
                                message=validation_result.get("message", "")))
        
        log(_("yang_validation_stage.validation_report_generated"), "info")
