#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Zone Processing Stage
Handles FortiGate security zone configuration and converts to NTOS format
"""

from typing import Dict, Any, List, Set
from engine.processing.pipeline.pipeline_stage import PipelineStage
from engine.processing.pipeline.data_flow import DataContext
from engine.utils.logger import log
from engine.utils.i18n import _
from lxml import etree


class ZonePriorityManager:
    """
    区域优先级管理器 - 确保NTOS YANG模型要求的优先级唯一性
    """

    def __init__(self):
        # 已分配的优先级集合
        self.allocated_priorities = set()

        # 预定义区域的固定优先级（基于安全级别）
        self.predefined_priorities = {
            "trust": 100,      # 最高安全级别
            "untrust": 10,     # 最低安全级别
            "dmz": 50,         # 中等安全级别
            "mgmt": 90,        # 管理区域
            "internal": 80,    # 内部区域
            "external": 20     # 外部区域
        }

        # 自定义区域优先级分配范围
        self.custom_priority_range = {
            "high": (70, 89),      # 高安全级别自定义区域
            "medium": (30, 49),    # 中等安全级别自定义区域
            "low": (11, 29)        # 低安全级别自定义区域
        }

        # 初始化预定义优先级
        for priority in self.predefined_priorities.values():
            self.allocated_priorities.add(priority)

    def get_priority_for_zone(self, zone_name: str, zone_type: str = "custom") -> int:
        """
        为区域分配唯一优先级

        Args:
            zone_name: 区域名称
            zone_type: 区域类型 (predefined/custom)

        Returns:
            int: 唯一的优先级值
        """
        zone_name_lower = zone_name.lower()

        # 检查是否为预定义区域
        if zone_name_lower in self.predefined_priorities:
            priority = self.predefined_priorities[zone_name_lower]
            return priority

        # 为自定义区域分配优先级
        return self._allocate_custom_priority(zone_name)

    def _allocate_custom_priority(self, zone_name: str) -> int:
        """
        为自定义区域分配优先级

        Args:
            zone_name: 区域名称

        Returns:
            int: 分配的优先级
        """
        # 根据区域名称特征判断安全级别
        security_level = self._determine_security_level(zone_name)
        priority_range = self.custom_priority_range[security_level]

        # 在指定范围内查找可用优先级
        for priority in range(priority_range[1], priority_range[0] - 1, -1):  # 从高到低分配
            if priority not in self.allocated_priorities:
                self.allocated_priorities.add(priority)
                log(_("zone_priority_manager.allocated_custom_priority",
                     zone=zone_name, priority=priority, level=security_level), "debug")
                return priority

        # 如果指定范围内没有可用优先级，扩展搜索范围
        return self._allocate_fallback_priority(zone_name)

    def _determine_security_level(self, zone_name: str) -> str:
        """
        根据区域名称确定安全级别

        Args:
            zone_name: 区域名称

        Returns:
            str: 安全级别 (high/medium/low)
        """
        zone_name_lower = zone_name.lower()

        # 高安全级别关键词
        high_security_keywords = ["trust", "secure", "internal", "lan", "private"]
        # 低安全级别关键词
        low_security_keywords = ["untrust", "public", "wan", "internet", "external"]

        for keyword in high_security_keywords:
            if keyword in zone_name_lower:
                return "high"

        for keyword in low_security_keywords:
            if keyword in zone_name_lower:
                return "low"

        # 默认为中等安全级别
        return "medium"

    def _allocate_fallback_priority(self, zone_name: str) -> int:
        """
        当常规范围内没有可用优先级时的回退分配

        Args:
            zone_name: 区域名称

        Returns:
            int: 分配的优先级
        """
        # 在整个有效范围内查找可用优先级
        for priority in range(256, 0, -1):  # YANG模型范围：1-256
            if priority not in self.allocated_priorities:
                self.allocated_priorities.add(priority)
                log(_("zone_priority_manager.allocated_fallback_priority",
                     zone=zone_name, priority=priority), "warning")
                return priority

        # 理论上不应该到达这里，除非有超过256个区域
        log(_("zone_priority_manager.priority_exhausted", zone=zone_name), "error")
        return 1  # 返回最小值作为最后的回退

    def is_priority_available(self, priority: int) -> bool:
        """
        检查优先级是否可用

        Args:
            priority: 要检查的优先级

        Returns:
            bool: 是否可用
        """
        return priority not in self.allocated_priorities

    def reserve_priority(self, priority: int) -> bool:
        """
        预留指定优先级

        Args:
            priority: 要预留的优先级

        Returns:
            bool: 是否成功预留
        """
        if self.is_priority_available(priority):
            self.allocated_priorities.add(priority)
            return True
        return False


class ZoneProcessor:
    """
    Zone Processor - Complete reimplementation, borrowing core logic from old architecture
    """

    def __init__(self):
        # 初始化优先级管理器
        self.priority_manager = ZonePriorityManager()

        # Default zone configuration (使用优先级管理器分配优先级)
        self.default_zones = {
            "trust": {
                "name": "trust",
                "description": _("zone_processor.trust_zone_desc"),
                "priority": self.priority_manager.get_priority_for_zone("trust", "predefined"),
                "type": "security"
            },
            "untrust": {
                "name": "untrust",
                "description": _("zone_processor.untrust_zone_desc"),
                "priority": self.priority_manager.get_priority_for_zone("untrust", "predefined"),
                "type": "security"
            },
            "dmz": {
                "name": "dmz",
                "description": _("zone_processor.dmz_zone_desc"),
                "priority": self.priority_manager.get_priority_for_zone("dmz", "predefined"),
                "type": "security"
            }
        }

        # Load interface mapping file
        self.interface_mapping = {}
        self._load_interface_mapping()

    def _load_interface_mapping(self):
        """
        Load interface mapping file (borrowing from old architecture logic)
        """
        try:
            from engine.utils.interface_mapper import load_interface_mapping
            from engine.utils.file_utils import get_current_interface_mapping_file_path
            import os

            # Get current task interface mapping file path
            mapping_file = get_current_interface_mapping_file_path()

            # If file doesn't exist, try using default path
            if not os.path.exists(mapping_file):
                default_mapping_file = "data/mappings/interface_mapping.json"
                if os.path.exists(default_mapping_file):
                    mapping_file = default_mapping_file
                    log(f"Using default interface mapping file: {mapping_file}", "debug")
                else:
                    log(f"Interface mapping file not found: {mapping_file}", "warning")
                    self.interface_mapping = {}
                    return

            raw_mapping = load_interface_mapping(mapping_file)

            # 处理不同格式的映射文件
            if "interface_mappings" in raw_mapping:
                # 嵌套格式，提取interface_mappings部分
                self.interface_mapping = raw_mapping["interface_mappings"]
                log(f"Interface mapping loaded (nested format): {len(self.interface_mapping)} mappings", "info")
            elif "mapping" in raw_mapping:
                # CLI格式，提取mapping部分
                self.interface_mapping = raw_mapping["mapping"]
                log(f"Interface mapping loaded (CLI format): {len(self.interface_mapping)} mappings", "info")
            else:
                # 扁平格式，直接使用
                self.interface_mapping = raw_mapping
                log(f"Interface mapping loaded (flat format): {len(self.interface_mapping)} mappings", "info")

        except Exception as e:
            log(f"Failed to load interface mapping: {str(e)}", "warning")
            self.interface_mapping = {}

    def reload_interface_mapping(self):
        """
        Reload interface mapping file (for testing)
        """
        self._load_interface_mapping()

    def set_interface_mapping(self, interface_mapping: dict):
        """
        Set interface mapping directly (for testing or when mapping is provided via context)

        Args:
            interface_mapping: Interface mapping dictionary
        """
        if interface_mapping:
            self.interface_mapping = interface_mapping
            log(f"Interface mapping set directly: {len(interface_mapping)} mappings", "info")
        else:
            log("Empty interface mapping provided", "warning")

    def map_interface_name(self, original_name: str) -> str:
        """
        Map interface name (FortiGate -> NTOS)

        Args:
            original_name: FortiGate original interface name

        Returns:
            str: Mapped NTOS interface name, returns None if mapping not found
        """
        if not original_name:
            return None

        # Clean interface name
        clean_name = str(original_name).strip('"').strip("'")

        if not self.interface_mapping:
            log(_("zone_processor.interface_mapping_empty"), "warning")
            return None

        # Handle nested format interface mapping
        # Support two formats:
        # 1. Nested format: {"interface_mappings": {"port1": "Ge0/0"}}
        # 2. Flat format: {"port1": "Ge0/0"}
        actual_mappings = {}

        # 接口映射已经在_load_interface_mapping中处理过了，直接使用
        actual_mappings = self.interface_mapping

        # Find mapping
        if clean_name in actual_mappings:
            mapping_data = actual_mappings[clean_name]

            # If mapping data is dict, extract name field
            if isinstance(mapping_data, dict):
                mapped_name = mapping_data.get("name", None)
                if mapped_name:
                    return mapped_name
            else:
                # If it's string, return directly
                return mapping_data

        # No mapping found
        log(_("zone_processor.interface_not_mapped", interface=clean_name), "warning")
        return None

    def validate_and_map_interfaces(self, interfaces: List[str], zone_name: str = None) -> tuple:
        """
        Validate and map interface list (enhanced from old architecture logic)

        结合旧架构逻辑：允许部分接口映射失败，但仍保留区域

        Args:
            interfaces: FortiGate interface name list
            zone_name: Zone name for logging purposes

        Returns:
            tuple: (List[str] mapped interfaces, List[str] unmapped interfaces, List[dict] warnings)
        """
        valid_interfaces = []
        unmapped_interfaces = []
        interface_warnings = []

        for interface in interfaces:
            mapped_interface = self.map_interface_name(interface)
            if mapped_interface:
                valid_interfaces.append(mapped_interface)
            else:
                unmapped_interfaces.append(interface)
                warning_msg = f"接口 {interface} 未配置映射关系"
                log(warning_msg, "warning")

                # 记录详细的接口映射警告（借鉴旧架构的警告机制）
                interface_warnings.append({
                    "zone": zone_name or "unknown",
                    "interface": interface,
                    "reason": "缺少接口映射",
                    "suggestion": "请在映射文件中添加该接口的映射关系"
                })

        log(f"Interface mapping completed for zone {zone_name}: {len(valid_interfaces)} mapped, {len(unmapped_interfaces)} unmapped", "debug")
        return valid_interfaces, unmapped_interfaces, interface_warnings

    def get_required_zones(self, zones: Dict, interface_zones: Dict, operation_mode: str) -> Set[str]:
        """
        Get required zone list (borrowing from old architecture logic)

        Args:
            zones: FortiGate zone configuration
            interface_zones: Interface zone mapping
            operation_mode: Operation mode

        Returns: Set[str]: Required zone name set
        """
        required_zones = set()

        # Collect zones from FortiGate configuration
        for zone_name in zones.keys():
            required_zones.add(zone_name)

        # Collect zones from interface processing results
        for interface_name, zone_name in interface_zones.items():
            if zone_name:
                required_zones.add(zone_name)

        # Ensure basic zones exist (borrowing from old architecture logic)
        if operation_mode == "route":
            required_zones.update(["trust", "untrust"])
        else:  # transparent mode
            required_zones.add("trust")

        return required_zones

    def create_zone_config(self, zone_name: str, fortigate_config: Dict[str, Any],
                          interface_zones: Dict[str, str]) -> Dict[str, Any]:
        """
        Create zone configuration (borrowing from old architecture logic)

        Args:
            zone_name: Zone name
            fortigate_config: FortiGate zone configuration
            interface_zones: Interface zone mapping

        Returns: Dict[str, Any]: NTOS zone configuration
        """
        log(f"Starting to create zone configuration: {zone_name}", "debug")
        log(f"FortiGate configuration contains {len(fortigate_config)} fields", "debug")
        log(f"Interface zone mapping contains {len(interface_zones)} entries", "debug")

        # Use default configuration as base
        if zone_name in self.default_zones:
            base_config = self.default_zones[zone_name].copy()
            log(f"Using default zone configuration: {zone_name}", "debug")
        else:
            # 为自定义区域分配唯一优先级
            custom_priority = self.priority_manager.get_priority_for_zone(zone_name, "custom")
            base_config = {
                "name": zone_name,
                "description": _("zone_processor.custom_zone_desc", zone=zone_name),
                "priority": custom_priority,
                "type": "security"
            }
            log(_("zone_processor.creating_custom_zone", zone=zone_name, priority=custom_priority), "debug")

        # 应用FortiGate配置
        if fortigate_config:
            # 处理描述字段 - 支持FortiGate的description字段
            if "description" in fortigate_config:
                base_config["description"] = fortigate_config["description"]
                log(f"Zone {zone_name}: 使用FortiGate描述 '{fortigate_config['description']}'", "debug")

            # 处理接口列表（使用接口映射）
            # 支持两种字段名：interface（单数）和interfaces（复数）
            if "interface" in fortigate_config:
                interfaces = fortigate_config["interface"]
            elif "interfaces" in fortigate_config:
                interfaces = fortigate_config["interfaces"]
            else:
                interfaces = None

            if interfaces:
                if isinstance(interfaces, str):
                    interfaces = [interfaces]

                log(_("zone_processor.zone_fortigate_interfaces", zone=zone_name, interfaces=interfaces), "debug")

                # 验证并映射接口名称（使用增强的映射逻辑）
                mapped_interfaces, unmapped_interfaces, interface_warnings = self.validate_and_map_interfaces(interfaces, zone_name)
                base_config["interfaces"] = mapped_interfaces

                # 保存接口映射警告信息（结合旧架构的警告处理）
                base_config["interface_warnings"] = interface_warnings
                base_config["unmapped_interfaces"] = unmapped_interfaces

                log(_("zone_processor.zone_mapped_interfaces", zone=zone_name, interfaces=mapped_interfaces), "debug")

                # 记录映射结果（增强的日志信息）
                if unmapped_interfaces:
                    log(_("zone_processor.zone_interfaces_skipped", zone=zone_name, count=len(unmapped_interfaces)), "warning")
                    for unmapped_intf in unmapped_interfaces:
                        log(f"区域 {zone_name}: {len(unmapped_interfaces)} 个接口因未配置映射关系被跳过", "warning")
            else:
                log(_("zone_processor.zone_no_interface_field", zone=zone_name), "debug")
                base_config["interfaces"] = []

            # 处理intrazone配置（重要安全设置）
            if "intrazone" in fortigate_config:
                intrazone_policy = fortigate_config["intrazone"].lower().strip()
                base_config["intrazone"] = intrazone_policy

                # 如果是permit，生成安全警告
                if intrazone_policy == "permit":
                    warning_msg = f"{zone_name}安全域接口间的访问不会默认放行"
                    log(warning_msg, "warning")
                    # 记录警告到配置中，供后续处理使用
                    base_config["security_warning"] = warning_msg
                    base_config["intrazone_warning"] = True
                else:
                    base_config["intrazone_warning"] = False
            else:
                # 默认为deny（安全）
                base_config["intrazone"] = "deny"
                base_config["intrazone_warning"] = False

        # 从接口区域映射中收集属于此区域的接口（使用映射后的接口名称）
        zone_interfaces = []
        log(_("zone_processor.zone_interface_mapping_start", zone=zone_name), "debug")

        for interface_name, mapped_zone in interface_zones.items():
            if mapped_zone == zone_name:
                log(_("zone_processor.zone_interface_found", zone=zone_name, interface=interface_name), "debug")
                # 映射接口名称
                mapped_interface = self.map_interface_name(interface_name)
                if mapped_interface:
                    zone_interfaces.append(mapped_interface)
                    log(_("zone_processor.zone_interface_mapping_success", original=interface_name, mapped=mapped_interface), "debug")
                else:
                    log(_("zone_processor.zone_interface_mapping_skip", zone=zone_name, interface=interface_name), "warning")

        log(_("zone_processor.zone_interfaces_from_mapping", zone=zone_name, interfaces=zone_interfaces), "debug")

        # 合并接口列表
        all_interfaces = base_config.get("interfaces", []) + zone_interfaces
        log(_("zone_processor.zone_interfaces_before_merge", zone=zone_name, interfaces=all_interfaces), "debug")

        # 去重并保持顺序
        unique_interfaces = []
        seen = set()
        for intf in all_interfaces:
            if intf not in seen:
                unique_interfaces.append(intf)
                seen.add(intf)

        log(_("zone_processor.zone_interfaces_final", zone=zone_name, interfaces=unique_interfaces), "debug")

        # 创建NTOS区域配置
        ntos_config = {
            "name": base_config["name"],
            "description": base_config["description"],
            "priority": base_config["priority"],
            "type": base_config["type"],
            "enabled": True,
            "interfaces": unique_interfaces,
            "security_level": self._get_security_level(zone_name),
            # 保存intrazone配置信息
            "intrazone": base_config.get("intrazone", "deny"),
            "intrazone_warning": base_config.get("intrazone_warning", False)
        }

        # 如果有安全警告，添加到配置中
        if "security_warning" in base_config:
            ntos_config["security_warning"] = base_config["security_warning"]

        log(_("zone_processor.zone_config_created", zone=zone_name, count=len(ntos_config.get('interfaces', []))), "debug")
        return ntos_config

    def _get_security_level(self, zone_name: str) -> int:
        """
        获取区域安全级别（借鉴旧架构逻辑）

        Args:
            zone_name: 区域名称

        Returns:
            int: 安全级别 (0-100)
        """
        security_levels = {
            "trust": 100,
            "dmz": 50,
            "untrust": 0
        }

        return security_levels.get(zone_name.lower(), 50)

    def _add_skipped_result(self, result: Dict, zone_name: str, fortigate_config: Dict, reason: str):
        """
        添加跳过的区域结果

        Args:
            result: 结果字典
            zone_name: 区域名称
            fortigate_config: FortiGate配置
            reason: 跳过原因
        """
        result["skipped"][zone_name] = {
            "name": zone_name,
            "reason": reason,
            "original_config": fortigate_config
        }
        result["skipped_count"] += 1

        result["details"].append({
            "name": zone_name,
            "status": "skipped",
            "reason": reason,
            "original_config": fortigate_config
        })

        log(f"Zone skipped: {zone_name} - {reason}", "warning")

    def _add_failed_result(self, result: Dict, zone_name: str, fortigate_config: Dict, reason: str):
        """
        添加失败的区域结果

        Args:
            result: 结果字典
            zone_name: 区域名称
            fortigate_config: FortiGate配置
            reason: 失败原因
        """
        result["failed"][zone_name] = {
            "name": zone_name,
            "reason": reason,
            "original_config": fortigate_config
        }
        result["failed_count"] += 1

        result["details"].append({
            "name": zone_name,
            "status": "failed",
            "reason": reason,
            "original_config": fortigate_config
        })

        log(f"Zone processing failed: {zone_name} - {reason}", "error")

    def process_zones(self, zones: Dict, interface_zones: Dict, operation_mode: str) -> Dict[str, Any]:
        """
        处理区域配置

        Args:
            zones: FortiGate区域配置
            interface_zones: 接口区域映射
            operation_mode: 操作模式

        Returns:
            Dict: 处理结果
        """
        # 重新加载接口映射以确保使用最新的映射文件
        self._load_interface_mapping()

        result = {
            "converted": {},
            "skipped": {},
            "failed": {},
            "converted_count": 0,
            "skipped_count": 0,
            "failed_count": 0,
            "details": [],
            "warnings": [],  # 安全警告（intrazone permit）
            "interface_warnings": []  # 接口映射警告
        }

        try:
            # 获取需要的区域
            required_zones = self.get_required_zones(zones, interface_zones, operation_mode)

            log(_("zone_processor.required_zones", zones=required_zones), "debug")

            # 创建区域配置
            for zone_name in required_zones:
                try:
                    fortigate_config = zones.get(zone_name, {})
                    zone_config = self.create_zone_config(zone_name, fortigate_config, interface_zones)

                    log(_("zone_processor.zone_config_creation_complete", zone=zone_name, config=zone_config), "debug")

                    # 检查映射后是否还有有效接口（结合旧架构的灵活处理逻辑）
                    mapped_interfaces = zone_config.get("interfaces", [])
                    unmapped_interfaces = zone_config.get("unmapped_interfaces", [])
                    interface_warnings = zone_config.get("interface_warnings", [])

                    log(_("zone_processor.zone_mapped_interfaces", zone=zone_name, interfaces=mapped_interfaces), "debug")

                    # 新的跳过逻辑：结合旧架构和XML模板要求
                    # 1. 对于默认区域（trust/untrust），如果没有接口则跳过
                    # 2. 对于自定义区域，即使没有接口也创建（支持策略中使用区域名称）
                    should_skip_zone = False
                    skip_reason = ""

                    if not mapped_interfaces:
                        if zone_name in self.default_zones:
                            # 默认区域没有接口时跳过
                            should_skip_zone = True
                            skip_reason = _("zone_processor.zone_skipped_no_interfaces")
                        else:
                            # 自定义区域即使没有接口也保留（借鉴旧架构逻辑）
                            log(f"自定义区域 {zone_name} 没有有效接口，但仍然创建以支持策略引用", "info")

                    if should_skip_zone:
                        self._add_skipped_result(result, zone_name, fortigate_config, skip_reason)
                        continue

                    # 收集接口映射警告到结果中
                    if interface_warnings:
                        result["interface_warnings"].extend(interface_warnings)

                    # 检查是否有接口因未映射被跳过
                    original_interfaces = []
                    if "interface" in fortigate_config:
                        interfaces = fortigate_config["interface"]
                        if isinstance(interfaces, str):
                            interfaces = [interfaces]
                        original_interfaces.extend(interfaces)

                    # 从接口区域映射中收集原始接口
                    for interface_name, mapped_zone in interface_zones.items():
                        if mapped_zone == zone_name:
                            original_interfaces.append(interface_name)

                    # 统计映射失败的接口
                    mapped_interfaces = zone_config.get("interfaces", [])
                    if len(mapped_interfaces) < len(original_interfaces):
                        unmapped_interfaces = []
                        for orig_intf in original_interfaces:
                            mapped_intf = self.map_interface_name(orig_intf)
                            if not mapped_intf:
                                unmapped_interfaces.append(orig_intf)

                        if unmapped_interfaces:
                            warning_msg = f"区域 {zone_name}: 接口 {', '.join(unmapped_interfaces)} 未配置映射关系，已跳过"
                            result["interface_warnings"].append({
                                "zone": zone_name,
                                "type": "interface_mapping",
                                "unmapped_interfaces": unmapped_interfaces,
                                "message": warning_msg
                            })

                    result["converted"][zone_name] = zone_config
                    result["converted_count"] += 1

                    # 收集安全警告
                    if zone_config.get("intrazone_warning", False):
                        warning_msg = zone_config.get("security_warning", f"{zone_name}安全域接口间的访问不会默认放行")
                        result["warnings"].append({
                            "zone": zone_name,
                            "type": "intrazone_permit",
                            "message": warning_msg
                        })
                        log(_("zone_processor.security_warning",
                              message=warning_msg), "warning")

                    result["details"].append({
                        "name": zone_name,
                        "status": "success",
                        "type": "default" if zone_name in self.default_zones else "custom",
                        "interfaces_count": len(zone_config["interfaces"]),
                        "intrazone": zone_config.get("intrazone", "deny"),
                        "has_warning": zone_config.get("intrazone_warning", False),
                        "converted": zone_config
                    })

                    intrazone_info = f"intrazone={zone_config.get('intrazone', 'deny')}"
                    if zone_config.get("intrazone_warning", False):
                        intrazone_info += " (⚠️警告)"

                    log(_("zone_processor.zone_conversion_success",
                          zone=zone_name,
                          priority=zone_config['priority'],
                          interface_count=len(zone_config['interfaces']),
                          intrazone_info=intrazone_info), "debug")

                except Exception as e:
                    reason = f"Zone processing error: {str(e)}"
                    self._add_failed_result(result, zone_name, fortigate_config, reason)

        except Exception as e:
            log(f"Zone processing exception: {str(e)}", "error")

        return result

    def validate_zone_priorities(self, zone_result: Dict[str, Any]) -> bool:
        """
        验证区域优先级的唯一性

        Args:
            zone_result: 区域处理结果

        Returns:
            bool: 验证是否通过
        """
        converted_zones = zone_result.get("converted", {})
        priorities = []

        for zone_name, zone_config in converted_zones.items():
            priority = zone_config.get("priority")
            if priority:
                priorities.append((zone_name, priority))

        # 检查优先级重复
        priority_counts = {}
        for zone_name, priority in priorities:
            if priority in priority_counts:
                priority_counts[priority].append(zone_name)
            else:
                priority_counts[priority] = [zone_name]

        # 报告重复的优先级
        has_duplicates = False
        for priority, zones in priority_counts.items():
            if len(zones) > 1:
                has_duplicates = True
                log(_("zone_processor.duplicate_priority_detected",
                     priority=priority, zones=", ".join(zones)), "error")

        if not has_duplicates:
            log(_("zone_processor.priority_validation_passed", count=len(priorities)), "debug")

        return not has_duplicates

    def _add_failed_result(self, result: Dict, name: str, config: Dict, reason: str):
        """添加失败的结果"""
        result["failed"][name] = config
        result["failed_count"] += 1
        result["details"].append({
            "name": name,
            "status": "failed",
            "reason": reason,
            "original": config
        })


class ZoneProcessingStage(PipelineStage):
    """
    Security Zone Processing Stage - Complete reimplementation

    Handles FortiGate security zone configuration, converts to NTOS format and generates XML fragments
    Depends on interface processing stage results
    """

    def __init__(self):
        super().__init__("zone_processing", _("zone_processing_stage.description"))
        self.processor = ZoneProcessor()
    
    def validate_input(self, context: DataContext) -> bool:
        """
        验证输入数据
        
        Args:
            context: 数据上下文
            
        Returns:
            bool: 验证是否通过
        """
        # 检查是否有接口处理结果
        interface_result = context.get_data("interface_processing_result")
        if not interface_result:
            log(_("zone_processing_stage.no_interface_result"), "warning")
            # 接口结果不是必需的，可以继续处理
        
        return True
    
    def process(self, context: DataContext) -> bool:
        """
        执行区域处理
        
        Args:
            context: 数据上下文
            
        Returns:
            bool: 处理是否成功
        """
        log(_("zone_processing_stage.starting"))
        
        try:
            # 检查上下文中是否有接口映射数据
            context_interface_mapping = context.get_data("interface_mapping")
            if context_interface_mapping:
                log(f"Using interface mapping from context: {len(context_interface_mapping)} mappings", "info")
                self.processor.set_interface_mapping(context_interface_mapping)

            # 获取配置数据
            config_data = context.get_data("config_data", {})
            zones_data = config_data.get("zones", [])

            # 转换数据格式：从列表转换为字典（兼容解析器输出格式）
            zones = {}
            if isinstance(zones_data, list):
                for zone in zones_data:
                    if isinstance(zone, dict) and 'name' in zone:
                        zones[zone['name']] = zone
            elif isinstance(zones_data, dict):
                zones = zones_data

            log(_("zone_processor.zone_data_conversion_complete",
                  original_type=type(zones_data).__name__,
                  converted_type=type(zones).__name__,
                  count=len(zones)), "debug")
            
            # 获取接口处理结果 - 增强容错处理
            interface_result = context.get_data("interface_processing_result", {})
            interface_zones = interface_result.get("zones", {})

            # 验证接口处理结果格式
            if not isinstance(interface_zones, dict):
                log(_("zone_processing_stage.invalid_interface_zones_format"), "warning",
                    type=type(interface_zones).__name__)
                interface_zones = {}
            
            # 获取操作模式结果
            operation_mode_result = context.get_data("operation_mode_result", {})
            operation_mode = operation_mode_result.get("mode", "route")

            log(_("zone_processor.zones_found", count=len(zones), mode=operation_mode), "info")

            # 使用新的处理器处理区域
            zone_result = self.processor.process_zones(zones, interface_zones, operation_mode)

            # 验证区域优先级唯一性
            priority_validation_passed = self.processor.validate_zone_priorities(zone_result)
            if not priority_validation_passed:
                log(_("zone_processing_stage.priority_validation_failed"), "warning")

            # 生成XML片段
            xml_fragment = self._generate_zone_xml_fragment(zone_result)

            # 构建处理结果（包含区域和XML片段）
            processing_result = {
                "zones": zone_result,
                "xml_fragment": xml_fragment,
                "statistics": {
                    "total_zones": len(zones),
                    "converted_zones": zone_result['converted_count'],
                    "skipped_zones": zone_result['skipped_count'],
                    "failed_zones": zone_result['failed_count'],
                    "warnings_count": len(zone_result.get('warnings', [])),
                    "interface_warnings_count": len(zone_result.get('interface_warnings', []))
                },
                "warnings": zone_result.get('warnings', []),
                "interface_warnings": zone_result.get('interface_warnings', [])
            }

            context.set_data("zone_processing_result", processing_result)

            # 输出处理结果和警告信息
            warnings_count = len(zone_result.get('warnings', []))
            interface_warnings_count = len(zone_result.get('interface_warnings', []))
            total_warnings = warnings_count + interface_warnings_count

            if total_warnings > 0:
                log(_("zone_processor.zone_processing_complete_with_warnings",
                      success=zone_result['converted_count'],
                      total=len(zones),
                      warnings=total_warnings,
                      security=warnings_count,
                      interface=interface_warnings_count), "info")

                # 显示安全警告
                for warning in zone_result.get('warnings', []):
                    log(_("zone_processor.security_warning",
                          message=warning['message']), "warning")

                # 显示接口映射警告
                for warning in zone_result.get('interface_warnings', []):
                    log(_("zone_processor.interface_mapping_warning",
                          message=warning['message']), "warning")
            else:
                log(_("zone_processor.zone_processing_complete",
                      success=zone_result['converted_count'],
                      total=len(zones)), "info")

            # 记录用户日志
            from engine.utils.user_log_formatter import record_stage_user_log
            record_stage_user_log("zone_processing", processing_result)

            return True
            
        except Exception as e:
            error_msg = f"Zone processing failed: {str(e)}"
            error_msg = _("zone_processing_stage.processing_failed", error=str(e))
            log(error_msg, "error")
            error_msg = _("zone_processor.detailed_error_info",  error=error_msg)
            log(error_msg, "error")
            log(f"Config data type: {type(config_data)}", "debug")
            log(f"Original zone data type: {type(zones_data)}", "debug")
            log(f"Converted zone data type: {type(zones)}", "debug")
            log(f"Interface processing result type: {type(interface_result)}", "debug")
            context.set_data("zone_processing_result", self._empty_result())
            context.add_error(error_msg, self.name)
            return False

    def _generate_zone_xml_fragment(self, zone_result: Dict[str, Any]) -> str:
        """
        生成符合YANG模型的安全区域XML片段

        Args:
            zone_result: 区域处理结果

        Returns:
            str: XML片段字符串
        """
        try:
            # 创建security-zone根元素（使用正确的命名空间）
            security_zone = etree.Element("security-zone", xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-zone")

            # 处理转换成功的区域
            converted_zones = zone_result.get("converted", {})

            if not converted_zones:
                log(_("zone_processor.no_converted_zones"), "debug")
                return ""

            # 按优先级排序区域（高优先级在前）
            sorted_zones = sorted(converted_zones.items(),
                                key=lambda x: x[1].get("priority", 50),
                                reverse=True)

            for zone_name, zone_config in sorted_zones:
                # 创建zone元素（符合YANG模型）
                zone_elem = etree.SubElement(security_zone, "zone")

                # 添加name元素
                name_elem = etree.SubElement(zone_elem, "name")
                name_elem.text = zone_name

                # 添加description元素
                description = zone_config.get("description", "")
                if description:
                    desc_elem = etree.SubElement(zone_elem, "description")
                    desc_elem.text = description

                # 添加priority元素（必需，YANG约束1-256）
                priority = zone_config.get("priority", 50)
                # 确保优先级在有效范围内
                if priority < 1:
                    priority = 1
                elif priority > 256:
                    priority = 256

                priority_elem = etree.SubElement(zone_elem, "priority")
                priority_elem.text = str(priority)

                # 添加interface元素列表（符合YANG模型约束，支持无接口区域）
                interfaces = zone_config.get("interfaces", [])

                # 根据YANG模型和XML模板，interface元素是可选的
                # 如果区域没有接口，则不添加interface元素（符合旧架构处理方式）
                if interfaces:
                    for interface_name in interfaces:
                        # 创建interface元素
                        interface_elem = etree.SubElement(zone_elem, "interface")

                        # 添加name元素
                        intf_name_elem = etree.SubElement(interface_elem, "name")
                        intf_name_elem.text = interface_name
                else:
                    # 无接口区域的日志记录（借鉴旧架构的处理方式）
                    log(f"区域 {zone_name} 没有绑定接口，创建无接口区域以支持策略引用", "info")

                log(_("zone_processor.zone_xml_generated", zone=zone_name, priority=priority, count=len(interfaces)), "debug")

            # 转换为字符串
            xml_str = etree.tostring(security_zone, encoding='unicode', pretty_print=True)

            log(_("zone_processor.zone_xml_fragment_success", count=len(converted_zones)), "debug")
            return xml_str

        except Exception as e:
            log(f"Failed to generate zone XML fragment: {str(e)}", "error")
            return ""

    # 旧的处理方法已被新的ZoneProcessor替代
    
    def get_zone_for_interface(self, interface_name: str, context: DataContext) -> str:
        """
        获取接口所属的区域
        
        Args:
            interface_name: 接口名称
            context: 数据上下文
            
        Returns:
            str: 区域名称
        """
        zone_result = context.get_data("zone_processing_result", {})
        zone_interfaces = zone_result.get("zone_interfaces", {})
        
        return zone_interfaces.get(interface_name, "trust")  # 默认trust区域

    def _empty_result(self) -> Dict[str, Any]:
        """
        返回空的处理结果

        Returns: Dict[str, Any]: 空结果
        """
        return {
            "zones": {
                "converted": {},
                "skipped": {},
                "failed": {},
                "converted_count": 0,
                "skipped_count": 0,
                "failed_count": 0,
                "details": [],
                "warnings": [],
                "interface_warnings": []
            },
            "xml_fragment": "",
            "statistics": {
                "total_zones": 0,
                "converted_zones": 0,
                "skipped_zones": 0,
                "failed_zones": 0,
                "warnings_count": 0,
                "interface_warnings_count": 0
            },
            "warnings": [],
            "interface_warnings": []
        }
