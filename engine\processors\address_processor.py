"""
飞塔地址对象处理器，处理地址对象配置转换为NOST格式
"""
from engine.utils.logger import log, user_log
from engine.utils.i18n import _

class FortinetAddressProcessor:
    """飞塔地址对象处理器类"""
    
    def __init__(self):
        """初始化处理器"""
        self.name = "fortigate_address_processor"
        self.description = _("address.processor_description")
        # 不支持的地址对象类型列表
        self.unsupported_types = ["fqdn", "geography", "wildcard", "dynamic", 
                                 "interface-subnet", "mac", "route-tag"]
    
    def process(self, vrf, address_objects, address_groups=None):
        """
        处理地址对象配置
        
        Args:
            vrf: VRF元素
            address_objects (list): 解析后的地址对象列表
            address_groups (list, optional): 解析后的地址组列表
            
        Returns:
            dict: 处理结果统计信息
        """
        result = {
            "processed": 0,
            "skipped": 0,
            "failed": 0,
            "details": []
        }
        
        log(_("info.processing_address_objects"))
        user_log(_("info.processing_address_objects_user"))
        
        if not address_objects:
            log(_("info.no_address_objects_to_process"))
            return result
        
        for addr in address_objects:
            try:
                # 确保地址对象有名称
                if "name" not in addr or not addr["name"]:
                    addr_name = _("address.unnamed")
                    reason = _("reason.address_missing_name")
                    log(_("warning.skipping_address_missing_name", name=addr_name), "warning")
                    result["skipped"] += 1
                    result["details"].append({
                        "name": addr_name,
                        "status": "skipped",
                        "reason": reason,
                        "original": addr
                    })
                    continue
                
                addr_name = addr["name"]
                
                # 检查是否含有接口关联配置，如果有则跳过
                if "interface" in addr or "associated-interface" in addr:
                    reason = _("reason.interface_associated_address_not_supported")
                    log(_("warning.skipping_interface_associated_address", name=addr_name), "warning")
                    result["skipped"] += 1
                    result["details"].append({
                        "name": addr_name,
                        "status": "skipped",
                        "reason": reason,
                        "original": addr
                    })
                    continue
                
                # 检查地址对象类型
                if "type" not in addr:
                    # 默认为ipmask类型（飞塔的默认类型）
                    addr_type = "ipmask"
                    log(_("info.address_type_not_specified", name=addr_name, default=addr_type))
                else:
                    addr_type = addr["type"].lower()
                
                # 检查是否为不支持的类型
                if addr_type in self.unsupported_types:
                    reason = _("reason.address_type_not_supported", type=addr_type)
                    log(_("warning.skipping_address_unsupported_type", name=addr_name, type=addr_type), "warning")
                    result["skipped"] += 1
                    result["details"].append({
                        "name": addr_name,
                        "status": "skipped",
                        "reason": reason,
                        "original": addr
                    })
                    continue
                
                # 处理各种类型的地址对象
                if addr_type == "ipmask" or addr_type == "subnet":
                    # 验证并处理subnet格式
                    if not self._process_subnet_type(addr, addr_name, result):
                        continue
                    
                elif addr_type == "iprange":
                    # 验证并处理iprange格式
                    if not self._process_iprange_type(addr, addr_name, result):
                        continue

                elif addr_type == "range":
                    # 处理动态生成的range类型地址对象（来自服务对象iprange转换）
                    if not self._process_range_type(addr, addr_name, result):
                        continue

                else:
                    # 其他未明确支持的类型
                    reason = _("reason.address_type_not_explicitly_supported", type=addr_type)
                    log(_("warning.skipping_address_type_not_explicitly_supported", name=addr_name, type=addr_type), "warning")
                    result["skipped"] += 1
                    result["details"].append({
                        "name": addr_name,
                        "status": "skipped",
                        "reason": reason,
                        "original": addr
                    })
                    continue
                
                # 处理成功，记录到处理计数中
                result["processed"] += 1
                
            except Exception as e:
                addr_name = addr.get("name", _("address.unnamed"))
                error_msg = str(e)
                error_msg = _("error.address_object_conversion_failed", name=addr_name, error=error_msg)
                log(error_msg, "error")
                result["failed"] += 1
                result["details"].append({
                    "name": addr_name,
                    "status": "failed",
                    "reason": error_msg,
                    "original": addr
                })
        
        # 处理完成后记录统计信息
        log(_("info.address_objects_processing_complete", 
              total=len(address_objects),
              success=result["processed"],
              failed=result["failed"],
              skipped=result["skipped"]))
        
        user_log(_("info.address_objects_processing_complete_user", 
                  total=len(address_objects),
                  success=result["processed"],
                  failed=result["failed"],
                  skipped=result["skipped"]))
        
        return result
    
    def _process_subnet_type(self, addr, addr_name, result):
        """处理subnet/ipmask类型的地址对象"""
        # 确保有subnet字段
        if "subnet" not in addr or not addr["subnet"]:
            reason = _("reason.subnet_info_missing")
            log(_("warning.skipping_address_missing_subnet", name=addr_name), "warning")
            result["skipped"] += 1
            result["details"].append({
                "name": addr_name,
                "status": "skipped",
                "reason": reason,
                "original": addr
            })
            return False
        
        # 处理subnet格式，飞塔格式为"ip netmask"
        subnet_parts = addr["subnet"].split()
        if len(subnet_parts) != 2:
            reason = _("reason.invalid_subnet_format", subnet=addr['subnet'])
            log(_("warning.skipping_address_invalid_subnet", name=addr_name, subnet=addr['subnet']), "warning")
            result["skipped"] += 1
            result["details"].append({
                "name": addr_name,
                "status": "skipped",
                "reason": reason,
                "original": addr
            })
            return False
        
        ip, mask = subnet_parts
        
        # 创建NTOS格式的地址对象 - 使用IP/掩码格式
        ntos_addr = {
            "name": addr_name,
            "type": "subnet",
            "ip_address": f"{ip}/{mask}",  # NTOS格式
            "description": addr.get("comment", "")
        }
        
        # 记录转换结果
        result["details"].append({
            "name": addr_name,
            "status": "success",
            "original": addr,
            "converted": ntos_addr
        })
        
        log(_("info.address_object_converted", name=addr_name, type="subnet"))
        return True
    
    def _process_iprange_type(self, addr, addr_name, result):
        """处理iprange类型的地址对象"""
        # 确保有起始IP和结束IP
        if "start-ip" not in addr or "end-ip" not in addr:
            reason = _("reason.ip_range_missing_endpoints")
            log(_("warning.skipping_address_missing_ip_range", name=addr_name), "warning")
            result["skipped"] += 1
            result["details"].append({
                "name": addr_name,
                "status": "skipped",
                "reason": reason,
                "original": addr
            })
            return False
        
        # 创建NTOS格式的IP范围地址对象
        ntos_addr = {
            "name": addr_name,
            "type": "range",
            "ip_address": f"{addr['start-ip']}-{addr['end-ip']}",  # NTOS格式
            "description": addr.get("comment", "")
        }
        
        # 记录转换结果
        result["details"].append({
            "name": addr_name,
            "status": "success",
            "original": addr,
            "converted": ntos_addr
        })
        
        log(_("info.address_object_converted", name=addr_name, type="range"))
        return True

    def _process_range_type(self, addr, addr_name, result):
        """处理range类型的地址对象（动态生成的地址对象）"""
        # 确保有起始IP和结束IP
        if "start_ip" not in addr or "end_ip" not in addr:
            reason = _("reason.ip_range_missing_endpoints")
            log(_("warning.skipping_address_missing_ip_range", name=addr_name), "warning")
            result["skipped"] += 1
            result["details"].append({
                "name": addr_name,
                "status": "skipped",
                "reason": reason,
                "original": addr
            })
            return False

        start_ip = addr["start_ip"]
        end_ip = addr["end_ip"]

        # 验证IP地址格式
        try:
            from engine.utils.validation import validate_ip_range
            is_valid, error_msg = validate_ip_range(start_ip, end_ip)
            if not is_valid:
                reason = _("reason.invalid_ip_range", error=error_msg)
                error_msg = _("warning.skipping_address_invalid_ip_range", name=addr_name, error=error_msg)
                log(error_msg, "warning")
                result["skipped"] += 1
                result["details"].append({
                    "name": addr_name,
                    "status": "skipped",
                    "reason": reason,
                    "original": addr
                })
                return False
        except Exception as e:
            reason = _("reason.ip_validation_error", error=str(e))
            log(_("warning.skipping_address_validation_error", name=addr_name, error=str(e)), "warning")
            result["skipped"] += 1
            result["details"].append({
                "name": addr_name,
                "status": "skipped",
                "reason": reason,
                "original": addr
            })
            return False

        # 生成NTOS XML片段
        xml_fragment = f"""
        <network-obj>
            <name>{addr_name}</name>
            <ip>{start_ip}-{end_ip}</ip>
        </network-obj>"""

        # 添加到结果中
        result["converted"] += 1
        result["xml_fragments"].append(xml_fragment.strip())
        result["details"].append({
            "name": addr_name,
            "status": "converted",
            "type": "range",
            "start_ip": start_ip,
            "end_ip": end_ip,
            "source_service": addr.get("source_service", "unknown"),
            "original": addr
        })

        log(_("info.address_object_converted", name=addr_name, type="range"))
        return True

    def generate_address_from_iprange(self, name: str, iprange: str, source_service: str) -> dict:
        """
        从IP范围生成地址对象（用于服务对象iprange字段转换）

        Args:
            name: 地址对象名称
            iprange: IP范围字符串，格式为 "start_ip-end_ip"
            source_service: 源服务对象名称

        Returns:
            dict: NTOS格式的地址对象，如果生成失败则返回None
        """
        try:
            from engine.utils.validation import parse_iprange_string, validate_ip_range, normalize_ip_range

            # 解析IP范围
            start_ip, end_ip = parse_iprange_string(iprange)

            # 验证IP范围
            is_valid, error_msg = validate_ip_range(start_ip, end_ip)
            if not is_valid:
                error_msg = _("address_processor.iprange.invalid_range", iprange=iprange, error=error_msg)
                log(error_msg, "error")
                return None

            # 标准化IP范围
            normalized_range = normalize_ip_range(start_ip, end_ip)

            # 创建NTOS格式的地址对象
            address_obj = {
                "name": name,
                "type": "range",
                "start_ip": start_ip,
                "end_ip": end_ip,
                "ip_address": normalized_range,  # 兼容现有格式
                "description": _("address_processor.iprange.generated_description", source_service=source_service),
                "source_service": source_service,
                "generated": True  # 标记为动态生成的对象
            }

            log(_("address_processor.iprange.generation_success", name=name, range=normalized_range, source_service=source_service), "info")
            return address_obj

        except Exception as e:
            log(_("address_processor.iprange.generation_failed", error=str(e)), "error")
            return None

    def validate_iprange_format(self, iprange: str) -> bool:
        """
        验证IP范围格式是否有效

        Args:
            iprange: IP范围字符串

        Returns:
            bool: 是否为有效格式
        """
        try:
            from engine.utils.validation import parse_iprange_string, validate_ip_range

            start_ip, end_ip = parse_iprange_string(iprange)
            is_valid, _ = validate_ip_range(start_ip, end_ip)
            return is_valid

        except Exception:
            return False

# 注册处理器
address_processor = FortinetAddressProcessor()