#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
NAT 规则处理器模块

专门处理 NAT 相关配置的转换，包括：
1. VIP 到 DNAT 规则的转换
2. IP池 到动态 SNAT 规则的转换  
3. 静态 SNAT 规则的生成
4. NAT 池配置的处理
"""

import re
from typing import Dict, List, Any, Optional, Tuple
from engine.utils.logger import log
from engine.utils.i18n import _


class NATProcessor:
    """NAT 规则处理器类"""
    
    def __init__(self):
        """初始化处理器"""
        self.name = "nat_processor"
        self.description = _("nat.processor_description")

        # NAT 规则类型
        self.nat_rule_types = {
            "static_dnat44": "static-dnat44",
            "static_snat44": "static-snat44",
            "dynamic_snat44": "dynamic-snat44"
        }

        # 初始化所有增强组件
        self.enhanced_validator = None
        self.capacity_analyzer = None
        self.usage_analyzer = None
        self.reference_manager = None
        self.yang_validator = None
        self.optimizer = None
        self.name_sync_manager = None

        self._initialize_enhanced_components()

    def _initialize_enhanced_components(self):
        """初始化所有增强组件"""
        try:
            # 基础验证和分析组件
            from engine.validators.ippool_validator import EnhancedIPPoolValidator
            from engine.analyzers.ippool_capacity_analyzer import IPPoolCapacityAnalyzer
            from engine.analyzers.ippool_usage_analyzer import IPPoolUsageAnalyzer

            self.enhanced_validator = EnhancedIPPoolValidator()
            self.capacity_analyzer = IPPoolCapacityAnalyzer()
            self.usage_analyzer = IPPoolUsageAnalyzer()

            log(_("nat_processor.basic_enhanced_features_loaded"), "info")
        except ImportError as e:
            log(_("nat_processor.basic_enhanced_features_unavailable", error=str(e)), "debug")

        try:
            # 引用管理组件
            from engine.managers.ippool_reference_manager import IPPoolReferenceManager
            from engine.utils.name_manager import global_pool_name_sync_manager

            self.reference_manager = IPPoolReferenceManager()
            self.name_sync_manager = global_pool_name_sync_manager

            # 注册名称变更监听器
            if self.reference_manager:
                self.name_sync_manager.register_name_change_listener(
                    self._handle_pool_name_change
                )

            log(_("nat_processor.reference_management_loaded"), "info")
        except ImportError as e:
            log(_("nat_processor.reference_management_unavailable", error=str(e)), "debug")

        try:
            # YANG验证组件
            from engine.validators.yang_ippool_validator import YangIPPoolValidator
            self.yang_validator = YangIPPoolValidator()

            log(_("nat_processor.yang_validation_loaded"), "info")
        except ImportError as e:
            log(_("nat_processor.yang_validation_unavailable", error=str(e)), "debug")

        try:
            # 优化建议组件
            from engine.optimizers.ippool_optimizer import IPPoolOptimizer
            self.optimizer = IPPoolOptimizer()

            log(_("nat_processor.optimizer_loaded"), "info")
        except ImportError as e:
            log(_("nat_processor.optimizer_unavailable", error=str(e)), "debug")

    def _handle_pool_name_change(self, old_name: str, new_name: str, object_type: str):
        """处理地址池名称变更"""
        if object_type == "ippool" and self.reference_manager:
            # 更新引用管理器中的池名称
            success = self.reference_manager.rename_pool(old_name, new_name)
            if success:
                log(_("nat_processor.pool_name_change_handled",
                     old_name=old_name, new_name=new_name), "info")
            else:
                log(_("nat_processor.pool_name_change_failed",
                     old_name=old_name, new_name=new_name), "warning")
    
    def process_nat_configuration(self, policies: List[Dict], vips: Dict = None, ippools: Dict = None) -> Dict[str, Any]:
        """
        处理完整的NAT配置（增强版本，集成所有新功能）

        Args:
            policies: 策略列表
            vips: VIP配置字典
            ippools: IP池配置字典

        Returns:
            dict: NAT配置结果
                {
                    "nat_rules": [NAT规则列表],
                    "nat_pools": [NAT池列表],
                    "statistics": {统计信息},
                    "warnings": [警告信息],
                    "analysis_results": {分析结果},
                    "optimization_suggestions": [优化建议]
                }
        """
        vips = vips or {}
        ippools = ippools or {}

        result = {
            "nat_rules": [],
            "nat_pools": [],
            "statistics": {
                "total_policies": len(policies),
                "dnat_rules_created": 0,
                "snat_rules_created": 0,
                "nat_pools_created": 0,
                "validation_passed": 0,
                "validation_failed": 0,
                "yang_validation_passed": 0,
                "yang_validation_failed": 0
            },
            "warnings": [],
            "analysis_results": {},
            "optimization_suggestions": []
        }

        log(_("nat.start_processing", policies=len(policies), vips=len(vips), ippools=len(ippools)))

        # 第一阶段：处理IP池，生成NAT池配置（增强验证）
        nat_pools = self._process_nat_pools_enhanced(ippools, result)
        result["nat_pools"] = nat_pools
        result["statistics"]["nat_pools_created"] = len(nat_pools)

        # 第二阶段：建立引用关系
        self._establish_pool_references(policies, nat_pools)

        # 第三阶段：执行使用分析
        if self.usage_analyzer and nat_pools:
            try:
                usage_analysis = self.usage_analyzer.analyze_pool_usage(nat_pools, self.reference_manager)
                result["analysis_results"]["usage_analysis"] = usage_analysis
                log(_("nat_processor.usage_analysis_completed"), "info")
            except Exception as e:
                log(_("nat_processor.usage_analysis_failed", error=str(e)), "warning")

        # 第四阶段：生成优化建议
        if self.optimizer and nat_pools:
            try:
                optimization_suggestions = self.optimizer.generate_optimization_recommendations(
                    nat_pools,
                    result["analysis_results"].get("usage_analysis"),
                    self.reference_manager
                )
                result["optimization_suggestions"] = optimization_suggestions
                log(_("nat_processor.optimization_suggestions_generated",
                     count=len(optimization_suggestions)), "info")
            except Exception as e:
                log(_("nat_processor.optimization_failed", error=str(e)), "warning")
        
        # 处理策略中的NAT规则
        for policy in policies:
            try:
                nat_rules = self._extract_nat_rules_from_policy(policy, vips, ippools)
                result["nat_rules"].extend(nat_rules)
                
                # 更新统计
                for rule in nat_rules:
                    if "static-dnat44" in rule:
                        result["statistics"]["dnat_rules_created"] += 1
                    elif "static-snat44" in rule or "dynamic-snat44" in rule:
                        result["statistics"]["snat_rules_created"] += 1
                        
            except Exception as e:
                warning_msg = _("nat.policy_processing_error", 
                              policy_id=policy.get("policyid", "unknown"), 
                              error=str(e))
                result["warnings"].append(warning_msg)
                log(warning_msg, "warning")
        
        log(_("nat.processing_complete", 
              nat_rules=len(result["nat_rules"]),
              nat_pools=len(result["nat_pools"])))
        
        return result

    def _process_nat_pools_enhanced(self, ippools: Dict, result: Dict) -> List[Dict]:
        """
        增强版IP池处理方法

        Args:
            ippools: IP池配置字典
            result: 结果字典（用于记录统计信息）

        Returns: List[Dict]: 处理后的NAT池列表
        """
        nat_pools = []

        for pool_name, pool_config in ippools.items():
            try:
                # 基础验证
                if self.enhanced_validator:
                    validation_result = self.enhanced_validator.validate_pool_configuration(pool_config)
                    if validation_result.is_valid:
                        result["statistics"]["validation_passed"] += 1
                    else:
                        result["statistics"]["validation_failed"] += 1
                        result["warnings"].extend([
                            f"Pool {pool_name}: {error}" for error in validation_result.errors
                        ])
                        if not validation_result.can_proceed:
                            log(_("nat_processor.pool_validation_failed_skip",
                                 pool_name=pool_name), "warning")
                            continue

                # YANG模型预验证
                if self.yang_validator:
                    yang_result = self.yang_validator.validate_pool_configuration(pool_config)
                    if yang_result.is_valid:
                        result["statistics"]["yang_validation_passed"] += 1
                    else:
                        result["statistics"]["yang_validation_failed"] += 1
                        result["warnings"].extend([
                            f"Pool {pool_name} YANG validation: {error}"
                            for error in yang_result.errors
                        ])

                # 处理池配置
                nat_pool = self._convert_ippool_to_nat_pool(pool_name, pool_config)
                if nat_pool:
                    nat_pools.append(nat_pool)

                    # 注册池到引用管理器
                    if self.reference_manager:
                        self.reference_manager.register_pool(pool_name, pool_config)

            except Exception as e:
                log(_("nat_processor.pool_processing_error",
                     pool_name=pool_name, error=str(e)), "error")
                result["warnings"].append(f"Pool {pool_name} processing failed: {str(e)}")

        return nat_pools

    def _establish_pool_references(self, policies: List[Dict], nat_pools: List[Dict]):
        """
        建立池引用关系

        Args:
            policies: 策略列表
            nat_pools: NAT池列表
        """
        if not self.reference_manager:
            return

        # 分析策略中的池引用
        for policy in policies:
            policy_name = policy.get("name", "unknown")

            # 检查poolname引用
            poolname = policy.get("poolname")
            if poolname and isinstance(poolname, list):
                for pool_ref in poolname:
                    if isinstance(pool_ref, dict) and "name" in pool_ref:
                        pool_name = pool_ref["name"]
                        self.reference_manager.register_policy_pool_reference(
                            policy_name, pool_name
                        )

            # 检查其他可能的池引用
            # 这里可以根据实际需要扩展更多的引用检测逻辑

    def generate_comprehensive_analysis_report(self, nat_pools: List[Dict]) -> str:
        """
        生成综合分析报告

        Args:
            nat_pools: NAT池列表

        Returns:
            str: 格式化的分析报告
        """
        if not any([self.usage_analyzer, self.reference_manager, self.optimizer]):
            return _("nat_processor.analysis_features_unavailable")

        report_lines = []
        report_lines.append("=== FortiGate地址池转换综合分析报告 ===")
        report_lines.append("")

        # 基础统计
        report_lines.append(f"处理的地址池数量: {len(nat_pools)}")
        report_lines.append("")

        # 使用分析报告
        if self.usage_analyzer:
            try:
                usage_analysis = self.usage_analyzer.analyze_pool_usage(nat_pools, self.reference_manager)
                usage_report = self.usage_analyzer.generate_usage_report(usage_analysis)
                report_lines.append(usage_report)
                report_lines.append("")
            except Exception as e:
                report_lines.append(f"使用分析失败: {str(e)}")
                report_lines.append("")

        # 引用完整性报告
        if self.reference_manager:
            try:
                integrity_result = self.reference_manager.check_reference_integrity()
                report_lines.append("=== 引用完整性检查 ===")
                report_lines.append(f"总池数: {integrity_result.total_pools}")
                report_lines.append(f"引用池数: {integrity_result.referenced_pools}")
                report_lines.append(f"孤立池数: {len(integrity_result.orphaned_pools)}")

                if integrity_result.orphaned_pools:
                    report_lines.append(f"孤立池: {', '.join(integrity_result.orphaned_pools[:5])}")
                    if len(integrity_result.orphaned_pools) > 5:
                        report_lines.append(f"  ... 等{len(integrity_result.orphaned_pools)}个")

                if integrity_result.missing_pools:
                    report_lines.append(f"缺失池: {', '.join(integrity_result.missing_pools[:5])}")

                report_lines.append("")
            except Exception as e:
                report_lines.append(f"引用完整性检查失败: {str(e)}")
                report_lines.append("")

        # 优化建议
        if self.optimizer:
            try:
                usage_analysis = None
                if self.usage_analyzer:
                    usage_analysis = self.usage_analyzer.analyze_pool_usage(nat_pools, self.reference_manager)

                suggestions = self.optimizer.generate_optimization_recommendations(
                    nat_pools, usage_analysis, self.reference_manager
                )

                if suggestions:
                    report_lines.append("=== 优化建议 ===")
                    for i, suggestion in enumerate(suggestions[:10], 1):  # 只显示前10个建议
                        priority_text = {"critical": "关键", "high": "高", "medium": "中", "low": "低"}.get(
                            suggestion.priority, suggestion.priority
                        )
                        report_lines.append(f"{i}. {suggestion.title} (优先级: {priority_text})")
                        report_lines.append(f"   {suggestion.description}")
                        if suggestion.affected_pools:
                            pools_text = ", ".join(suggestion.affected_pools[:3])
                            if len(suggestion.affected_pools) > 3:
                                pools_text += f" ... 等{len(suggestion.affected_pools)}个"
                            report_lines.append(f"   涉及池: {pools_text}")
                        report_lines.append("")

                    if len(suggestions) > 10:
                        report_lines.append(f"... 还有{len(suggestions) - 10}个建议")
                        report_lines.append("")
                else:
                    report_lines.append("=== 优化建议 ===")
                    report_lines.append("未发现需要优化的问题")
                    report_lines.append("")

            except Exception as e:
                report_lines.append(f"优化建议生成失败: {str(e)}")
                report_lines.append("")

        return "\n".join(report_lines)
    
    def _extract_nat_rules_from_policy(self, policy: Dict, vips: Dict, ippools: Dict) -> List[Dict[str, Any]]:
        """
        从单个策略中提取NAT规则（支持所有策略，包括禁用的）

        Args:
            policy: 策略配置
            vips: VIP配置字典
            ippools: IP池配置字典

        Returns:
            list: NAT规则列表
        """
        nat_rules = []
        policy_name = policy.get("name", f"policy_{policy.get('policyid', 'unknown')}")

        # 获取策略状态 - 支持所有策略转换
        policy_enabled = self._get_policy_enabled_status(policy)

        # 检查不支持的功能并生成警告
        self._check_unsupported_features(policy)

        # 检查是否需要DNAT（目标地址包含VIP）
        dstaddr_list = policy.get("dstaddr", [])
        for addr in dstaddr_list:
            if addr in vips:
                dnat_rule = self._create_dnat_rule(policy, addr, vips[addr])
                if dnat_rule:
                    # 设置规则启用状态
                    dnat_rule["rule_en"] = policy_enabled
                    nat_rules.append(dnat_rule)

        # 检查是否需要SNAT（启用了NAT）- 处理所有NAT策略
        if policy.get("nat") == "enable":
            snat_rule = self._create_snat_rule(policy, ippools)
            if snat_rule:
                # 设置规则启用状态
                snat_rule["rule_en"] = policy_enabled
                nat_rules.append(snat_rule)

        return nat_rules

    def _get_policy_enabled_status(self, policy: Dict) -> bool:
        """
        获取策略启用状态

        Args:
            policy: FortiGate策略配置

        Returns:
            bool: 策略是否启用
        """
        # FortiGate策略状态逻辑：
        # - 缺少status字段 → 默认为enable
        # - set status enable → enable
        # - set status disable → disable
        status = policy.get("status", "enable")  # FortiGate默认为enable
        return status.lower() == "enable"

    def _check_unsupported_features(self, policy: Dict) -> None:
        """
        检查不支持的功能并生成警告

        Args:
            policy: FortiGate策略配置
        """
        policy_name = policy.get("name", f"policy_{policy.get('policyid', 'unknown')}")
        warnings = []

        # 检查fixedport功能
        if policy.get("fixedport") == "enable":
            warning_msg = _("nat.unsupported_fixedport", policy=policy_name)
            warnings.append(warning_msg)
            log(warning_msg, "warning")

        # 检查用户基础的NAT策略
        if policy.get("users"):
            warning_msg = _("nat.unsupported_users", policy=policy_name)
            warnings.append(warning_msg)
            log(warning_msg, "warning")

        # 检查组基础的NAT策略
        if policy.get("groups"):
            warning_msg = _("nat.unsupported_groups", policy=policy_name)
            warnings.append(warning_msg)
            log(warning_msg, "warning")

    def _get_port_preserve_setting(self, policy: Dict) -> bool:
        """
        获取端口保持设置

        Args:
            policy: FortiGate策略配置

        Returns:
            bool: try-no-pat设置值
        """
        # NTOS规范：
        # port-preserve enable → try-no-pat=false（转换源端口）
        # port-preserve disable → try-no-pat=true（优先使用原端口）
        port_preserve = policy.get("port-preserve", "disable")
        if port_preserve == "enable":
            return False  # 转换源端口
        else:
            return True   # 优先使用原端口

    def _create_dnat_rule(self, policy: Dict, vip_name: str, vip_config: Dict) -> Optional[Dict[str, Any]]:
        """
        创建DNAT规则

        Args:
            policy: 策略配置
            vip_name: VIP名称
            vip_config: VIP配置

        Returns:
            dict: DNAT规则配置
        """
        from engine.utils.name_manager import global_name_manager

        policy_name = policy.get("name", f"policy_{policy.get('policyid', 'unknown')}")

        # 验证VIP配置
        if not self._validate_vip_config(vip_config, vip_name):
            return None

        # 为DNAT规则分配唯一名称（包含VIP信息以区分多个DNAT规则）
        preferred_name = f"{policy_name}_{vip_name}"
        final_name = global_name_manager.register_name("nat", f"{policy_name}_dnat_{vip_name}", preferred_name)

        # 构建DNAT规则
        dnat_rule = {
            "name": final_name,  # 使用分配的唯一名称
            "type": "static-dnat44",  # 添加type字段用于验证
            "rule_en": True,  # 将在_extract_nat_rules_from_policy中设置正确的状态
            "static-dnat44": {
                "match": self._build_dnat_match_conditions(policy, vip_name),
                "translate-to": self._build_dnat_translation(vip_config)
            }
        }

        return dnat_rule
    
    def _create_snat_rule(self, policy: Dict, ippools: Dict) -> Optional[Dict[str, Any]]:
        """
        创建SNAT规则

        Args:
            policy: 策略配置
            ippools: IP池配置字典

        Returns:
            dict: SNAT规则配置
        """
        policy_name = policy.get("name", f"policy_{policy.get('policyid', 'unknown')}")

        # 检查是否使用IP池（动态SNAT）
        # FortiGate中ippool字段值为"enable"时表示启用IP池，真正的池名称在poolname字段中
        ippool_enabled = policy.get("ippool") == "enable"
        poolname_list = policy.get("poolname", [])

        # 确保poolname_list是列表格式
        if isinstance(poolname_list, str):
            poolname_list = [poolname_list]

        if ippool_enabled and poolname_list:
            return self._create_dynamic_snat_rule(policy, poolname_list, ippools)
        else:
            return self._create_static_snat_rule(policy)
    
    def _create_dynamic_snat_rule(self, policy: Dict, ippool_list: List[str], ippools: Dict) -> Optional[Dict[str, Any]]:
        """
        创建动态SNAT规则

        Args:
            policy: 策略配置
            ippool_list: IP池名称列表
            ippools: IP池配置字典

        Returns:
            dict: 动态SNAT规则配置
        """
        from engine.utils.name_manager import global_name_manager

        policy_name = policy.get("name", f"policy_{policy.get('policyid', 'unknown')}")

        # 使用第一个有效的IP池
        pool_name = None
        for pool in ippool_list:
            if pool in ippools:
                pool_name = pool
                break

        if not pool_name:
            log(_("nat.no_valid_ippool", policy_name=policy_name, pools=ippool_list), "warning")
            return None

        # 使用全局名称管理器分配名称
        final_name = global_name_manager.register_name("nat", policy_name, policy_name)

        snat_rule = {
            "name": final_name,  # 使用分配的唯一名称
            "type": "dynamic-snat44",  # 添加type字段用于验证
            "rule_en": True,  # 将在_extract_nat_rules_from_policy中设置正确的状态
            "dynamic-snat44": {
                "match": self._build_snat_match_conditions(policy),
                "translate-to": {
                    "pool-name": pool_name,
                    "try-no-pat": self._get_port_preserve_setting(policy)
                }
            }
        }

        return snat_rule
    
    def _create_static_snat_rule(self, policy: Dict) -> Dict[str, Any]:
        """
        创建静态SNAT规则

        Args:
            policy: 策略配置

        Returns:
            dict: 静态SNAT规则配置
        """
        from engine.utils.name_manager import global_name_manager

        policy_name = policy.get("name", f"policy_{policy.get('policyid', 'unknown')}")

        # 使用全局名称管理器分配名称
        final_name = global_name_manager.register_name("nat", policy_name, policy_name)

        # 构建静态SNAT规则
        snat_rule = {
            "name": final_name,  # 使用分配的唯一名称
            "type": "static-snat44",  # 添加type字段用于验证
            "rule_en": True,  # 将在_extract_nat_rules_from_policy中设置正确的状态
            "static-snat44": {
                "match": self._build_snat_match_conditions(policy),
                "translate-to": self._build_static_snat_translation(policy)
            }
        }

        return snat_rule
    
    def _build_dnat_match_conditions(self, policy: Dict, vip_name: str) -> Dict[str, Any]:
        """
        构建DNAT匹配条件 - 完美语义映射版本

        FortiGate语义 → NTOS语义映射:
        - srcintf → source-interface (流量入口接口)
        - dstintf → 记录但不添加到dest-interface (YANG choice约束)
        - srcaddr → source-network (源地址匹配)
        - dstaddr(VIP) → dest-network (目标网络匹配)
        - service → service (服务匹配)
        - schedule → time-range (时间范围匹配)

        Args:
            policy: FortiGate策略配置
            vip_name: VIP名称

        Returns:
            dict: NTOS NAT规则匹配条件
        """
        match_conditions = {}
        policy_name = policy.get("name", "unknown")

        # 1. 处理源接口（FortiGate srcintf → NTOS source-interface）
        # 这是流量的入口接口，语义完全对应
        source_interfaces = self._process_source_interface_for_dnat(policy)
        if source_interfaces:
            match_conditions["source-interface"] = source_interfaces

        # 2. 处理源网络（FortiGate srcaddr → NTOS source-network）
        source_networks = self._process_source_network_for_dnat(policy)
        if source_networks:
            match_conditions["source-network"] = source_networks

        # 3. 处理目标网络（FortiGate dstaddr(VIP) → NTOS dest-network）
        # 这是DNAT的核心，必须存在
        if vip_name:
            match_conditions["dest-network"] = [{"name": vip_name}]


        # 4. 处理目标接口（FortiGate dstintf → 受YANG choice约束限制）
        # 由于YANG模型中dest-network和dest-if是choice关系，不能同时存在
        # 但我们记录这个信息用于日志和可能的区域映射
        self._process_destination_interface_for_dnat(policy)

        # 5. 处理服务匹配（FortiGate service → NTOS service）
        services = self._process_service_for_dnat(policy)
        if services:
            match_conditions["service"] = services

        # 6. 处理时间范围（FortiGate schedule → NTOS time-range）
        time_range = self._process_time_range_for_dnat(policy)
        if time_range:
            match_conditions["time-range"] = time_range

        # 记录最终的匹配条件


        return match_conditions

    def _process_source_interface_for_dnat(self, policy: Dict) -> List[Dict[str, str]]:
        """
        处理DNAT规则的源接口匹配

        Args:
            policy: FortiGate策略配置

        Returns:
            list: NTOS源接口匹配条件列表
        """
        srcintf_list = policy.get("srcintf", [])
        if not srcintf_list:
            return []

        policy_name = policy.get("name", "unknown")

        # 确保是列表格式
        if isinstance(srcintf_list, str):
            srcintf_list = [srcintf_list]

        source_interfaces = []

        for intf in srcintf_list:
            if not intf or not intf.strip():
                continue

            clean_intf = intf.strip().strip('"').strip("'")

            try:
                # 尝试映射接口
                mapped_interface = self._get_mapped_interface_or_zone(clean_intf)
                if mapped_interface and self._is_interface_name(mapped_interface):
                    source_interfaces.append({"name": mapped_interface})
                    log(_("nat.dnat_source_interface_mapped",
                          policy_name=policy_name,
                          original=clean_intf,
                          mapped=mapped_interface), "info")
                elif mapped_interface:
                    # 映射到了区域，记录但不添加到接口匹配
                    log(_("nat.dnat_source_interface_to_zone",
                          policy_name=policy_name,
                          original=clean_intf,
                          zone=mapped_interface), "info")
                else:
                    log(_("nat.dnat_source_interface_mapping_failed",
                          policy_name=policy_name,
                          interface=clean_intf), "warning")

            except Exception as e:
                log(_("nat.dnat_source_interface_error",
                      policy_name=policy_name,
                      interface=clean_intf,
                      error=str(e)), "error")

        if source_interfaces:
            interface_names = [intf["name"] for intf in source_interfaces]
            log(_("nat.dnat_source_interfaces_added",
                  policy_name=policy_name,
                  count=len(source_interfaces),
                  interfaces=", ".join(interface_names)), "info")

        return source_interfaces

    def _validate_and_clean_network_name(self, name: str, context: str = "network") -> str:
        """
        验证并清理网络名称以符合YANG约束

        Args:
            name: 原始名称
            context: 上下文信息（用于日志）

        Returns:
            str: 清理后的名称
        """
        from engine.utils.name_validator import clean_ntos_name, validate_ntos_name

        # 首先验证原始名称
        is_valid, error_msg = validate_ntos_name(name)

        if is_valid:
            return name

        # 如果不符合YANG约束，进行清理
        cleaned_name = clean_ntos_name(name, 64)

        # 再次验证清理后的名称
        is_valid_after_clean, _ = validate_ntos_name(cleaned_name)
        if not is_valid_after_clean:
            # 如果清理后仍然无效，使用安全的默认名称
            cleaned_name = f"network_{hash(name) % 10000}"
            cleaned_name = clean_ntos_name(cleaned_name, 64)

        # 记录清理过程
        log(_("nat.network_name_cleaned",
              context=context,
              original=name,
              cleaned=cleaned_name,
              reason=error_msg), "info")

        return cleaned_name

    def _process_source_network_for_dnat(self, policy: Dict) -> List[Dict[str, str]]:
        """
        处理DNAT规则的源网络匹配

        Args:
            policy: FortiGate策略配置

        Returns:
            list: NTOS源网络匹配条件列表
        """
        srcaddr_list = policy.get("srcaddr", [])
        if not srcaddr_list:
            return []

        policy_name = policy.get("name", "unknown")

        # 确保是列表格式
        if isinstance(srcaddr_list, str):
            srcaddr_list = [srcaddr_list]

        source_networks = []

        for addr in srcaddr_list:
            if not addr or not addr.strip():
                continue

            clean_addr = addr.strip().strip('"').strip("'")

            # 跳过"all"地址（表示任意源地址）
            if clean_addr.lower() == "all":

                continue

            # 验证并清理网络名称以符合YANG约束
            validated_name = self._validate_and_clean_network_name(
                clean_addr, f"DNAT源网络-策略{policy_name}")

            source_networks.append({"name": validated_name})


        return source_networks

    def _process_destination_interface_for_dnat(self, policy: Dict) -> None:
        """
        处理DNAT规则的目标接口（记录模式）

        由于YANG模型中dest-network和dest-if是choice关系，
        当存在VIP时不能添加dest-interface，但我们记录这个信息

        Args:
            policy: FortiGate策略配置
        """
        dstintf_list = policy.get("dstintf", [])
        if not dstintf_list:
            return

        policy_name = policy.get("name", "unknown")

        # 确保是列表格式
        if isinstance(dstintf_list, str):
            dstintf_list = [dstintf_list]

        mapped_interfaces = []

        for intf in dstintf_list:
            if not intf or not intf.strip():
                continue

            clean_intf = intf.strip().strip('"').strip("'")

            try:
                mapped_interface = self._get_mapped_interface_or_zone(clean_intf)
                if mapped_interface:
                    mapped_interfaces.append(f"{clean_intf}→{mapped_interface}")
                else:
                    mapped_interfaces.append(f"{clean_intf}→未映射")

            except Exception as e:
                mapped_interfaces.append(f"{clean_intf}→错误:{str(e)}")

        # 记录目标接口信息（不添加到匹配条件中）
        log(_("nat.dnat_dest_interface_recorded",
              policy_name=policy_name,
              interfaces=", ".join(mapped_interfaces),
              reason="YANG choice约束：dest-network与dest-if互斥"), "info")

    def _process_service_for_dnat(self, policy: Dict) -> List[Dict[str, str]]:
        """
        处理DNAT规则的服务匹配

        Args:
            policy: FortiGate策略配置

        Returns:
            list: NTOS服务匹配条件列表
        """
        service_list = policy.get("service", [])
        if not service_list:
            return []

        policy_name = policy.get("name", "unknown")

        # 确保是列表格式
        if isinstance(service_list, str):
            service_list = [service_list]

        services = []

        for svc in service_list:
            if not svc or not svc.strip():
                continue

            clean_svc = svc.strip().strip('"').strip("'")

            # 处理"ALL"服务映射
            if clean_svc.upper() == "ALL":
                # FortiGate "ALL"服务映射到NTOS "any"服务
                services.append({"name": "any"})
                continue

            services.append({"name": clean_svc})


        return services

    def _process_time_range_for_dnat(self, policy: Dict) -> Dict[str, str]:
        """
        处理DNAT规则的时间范围匹配

        Args:
            policy: FortiGate策略配置

        Returns:
            dict: NTOS时间范围匹配条件
        """
        schedule = policy.get("schedule", "always")
        policy_name = policy.get("name", "unknown")

        if schedule and schedule.strip():
            clean_schedule = schedule.strip().strip('"').strip("'")

            return {"value": clean_schedule}

        # 默认使用"always"

        return {"value": "always"}

    def _get_mapped_source_interfaces(self, srcintf_list: List[str]) -> List[Dict[str, str]]:
        """
        获取映射后的源接口列表

        Args:
            srcintf_list: FortiGate源接口列表

        Returns:
            list: NTOS源接口匹配条件列表
        """
        if isinstance(srcintf_list, str):
            srcintf_list = [srcintf_list]

        source_interfaces = []

        for intf in srcintf_list:
            if not intf:
                continue

            try:
                # 尝试获取映射后的接口名
                mapped_interface = self._get_mapped_interface_or_zone(intf)
                if mapped_interface:
                    # 检查是否是接口名（包含数字和斜杠）还是区域名
                    if self._is_interface_name(mapped_interface):
                        source_interfaces.append({"name": mapped_interface})

                    else:
                        # 如果是区域名，记录但不添加到接口匹配（可以考虑添加到source-zone）
                        pass

                else:
                    log(_("nat.interface_mapping_failed", interface=intf), "warning")

            except Exception as e:
                log(_("nat.interface_mapping_error",
                      interface=intf, error=str(e)), "warning")

        return source_interfaces

    def _is_interface_name(self, name: str) -> bool:
        """
        判断名称是否为接口名（而不是区域名）

        Args:
            name: 接口或区域名称

        Returns:
            bool: True表示是接口名，False表示是区域名
        """
        if not name:
            return False

        # NTOS接口名通常包含数字和斜杠，如Ge0/0, TenGe0/1等
        # 区域名通常是trust, untrust等
        import re
        interface_pattern = r'^[A-Za-z]+\d+/\d+(\.\d+)?$'  # 匹配Ge0/0, TenGe0/1, Ge0/0.100等
        return bool(re.match(interface_pattern, name))

    def _build_dnat_translation(self, vip_config: Dict) -> Dict[str, Any]:
        """
        构建DNAT转换配置
        
        Args:
            vip_config: VIP配置
            
        Returns:
            dict: 转换配置
        """
        translation = {
            "ipv4-address": vip_config["mappedip"]
        }
        
        # 添加端口映射
        if "mappedport" in vip_config:
            try:
                port = int(vip_config["mappedport"])
                translation["port"] = port
            except (ValueError, TypeError):
                log(_("nat.invalid_mapped_port", port=vip_config["mappedport"]), "warning")
        
        return translation
    
    def _build_snat_match_conditions(self, policy: Dict) -> Dict[str, Any]:
        """
        构建SNAT匹配条件

        Args:
            policy: 策略配置

        Returns:
            dict: 匹配条件
        """
        match_conditions = {}
        policy_name = policy.get("name", "unknown")

        # 添加时间范围（必需）
        schedule = policy.get("schedule", "always")
        if schedule:
            match_conditions["time-range"] = {"value": schedule}

        # 添加源网络匹配（只在有源地址且不为ALL时生成）
        srcaddr_list = policy.get("srcaddr", [])
        if srcaddr_list and srcaddr_list[0] and srcaddr_list[0].upper() != "ALL":
            # 验证并清理网络名称以符合YANG约束
            validated_name = self._validate_and_clean_network_name(
                srcaddr_list[0], f"SNAT源网络-策略{policy_name}")
            match_conditions["source-network"] = {"name": validated_name}

        # 注意：根据YANG模型，dest-if字段只能在static-dnat44或twice-nat44中使用
        # SNAT规则不应该包含目标接口匹配，因此这里不添加dest-if字段

        # 添加服务匹配（增强版本支持FortiGate服务映射）
        service_list = policy.get("service", [])
        if service_list and service_list[0]:
            service_name = service_list[0].strip()

            # 处理"ALL"服务映射
            if service_name.upper() == "ALL":
                # FortiGate "ALL"服务映射到NTOS "any"服务
                match_conditions["service"] = {"name": "any"}
            else:
                # 验证并清理服务名称以符合YANG约束
                validated_service = self._validate_and_clean_network_name(
                    service_name, f"SNAT服务-策略{policy_name}")
                match_conditions["service"] = {"name": validated_service}

        return match_conditions
    
    def _build_static_snat_translation(self, policy: Dict) -> Dict[str, Any]:
        """
        构建静态SNAT转换配置
        
        Args:
            policy: 策略配置
            
        Returns:
            dict: 转换配置
        """
        translation = {
            "output-address": {},  # 使用出接口地址
            "try-no-pat": self._get_port_preserve_setting(policy)
        }

        return translation

    def _get_mapped_interface_name(self, original_interface: str) -> str:
        """
        获取映射后的接口名称

        Args:
            original_interface: 原始Fortigate接口名称

        Returns:
            str: 映射后的NTOS接口名称
        """
        try:
            from engine.utils.interface_mapper import get_mapped_interface_name, load_interface_mapping
            from engine.utils.file_utils import get_current_interface_mapping_file_path

            # 加载真正的接口映射文件（从任务目录）
            final_mapping_file = get_current_interface_mapping_file_path()
            interface_mapping = load_interface_mapping(final_mapping_file)

            # 获取映射后的接口名
            return get_mapped_interface_name(original_interface, interface_mapping)
        except Exception as e:
            # 如果接口映射失败，返回原始接口名
            log(_("nat.interface_name_mapping_failed", interface=original_interface, error=str(e)), "warning")
            return str(original_interface).strip('"').strip("'")

    def _get_mapped_interface_or_zone(self, original_interface: str) -> str:
        """
        获取映射后的接口名称或区域名称

        用于NAT配置：如果接口在接口数据中存在则使用映射后的接口名称，否则使用区域名称

        Args:
            original_interface: 原始Fortigate接口名称

        Returns:
            str: 映射后的接口名称或区域名称
        """
        try:
            # 首先尝试从全局状态获取接口映射
            interface_mapping = getattr(self, '_interface_mapping', None)

            if not interface_mapping:
                # 如果没有缓存的映射，尝试加载
                from engine.utils.interface_mapper import get_mapped_interface_or_zone, load_interface_mapping
                from engine.utils.file_utils import get_current_interface_mapping_file_path

                try:
                    final_mapping_file = get_current_interface_mapping_file_path()
                    interface_mapping = load_interface_mapping(final_mapping_file)
                    # 缓存映射以避免重复加载
                    self._interface_mapping = interface_mapping

                except Exception as file_error:
                    log(_("nat.interface_mapping_file_error", error=str(file_error)), "warning")
                    # 尝试从处理器状态获取接口映射
                    try:
                        # 检查是否有处理器级别的接口映射
                        if hasattr(self, 'interface_mapping') and self.interface_mapping:
                            interface_mapping = self.interface_mapping
                            self._interface_mapping = interface_mapping

                    except Exception:
                        pass

            if interface_mapping:
                from engine.utils.interface_mapper import get_mapped_interface_or_zone
                mapped_result = get_mapped_interface_or_zone(original_interface, interface_mapping)

                return mapped_result
            else:
                log(_("nat.interface_mapping_not_available", interface=original_interface), "warning")

        except Exception as e:
            # 如果接口映射失败，使用默认的区域映射逻辑
            log(_("nat.interface_mapping_failed", interface=original_interface, error=str(e)), "warning")

        # 默认区域映射：包含wan的为untrust，其他为trust
        clean_name = str(original_interface).strip('"').strip("'").lower()
        if "wan" in clean_name:
            log(_("nat.interface_fallback_to_untrust", interface=original_interface), "info")
            return "untrust"
        else:
            log(_("nat.interface_fallback_to_trust", interface=original_interface), "info")
            return "trust"

    def _process_nat_pools(self, ippools: Dict) -> List[Dict[str, Any]]:
        """
        处理IP池配置，生成NAT池
        
        Args:
            ippools: IP池配置字典
            
        Returns:
            list: NAT池配置列表
        """
        nat_pools = []
        
        for pool_name, pool_config in ippools.items():
            nat_pool = self._create_nat_pool(pool_name, pool_config)
            if nat_pool:
                nat_pools.append(nat_pool)
        
        return nat_pools
    
    def _create_nat_pool(self, pool_name: str, pool_config: Dict) -> Optional[Dict[str, Any]]:
        """
        创建单个NAT池配置（增强版本）

        Args:
            pool_name: 池名称
            pool_config: 池配置

        Returns:
            dict: NAT池配置
        """
        # 执行增强验证
        if not self._validate_ippool_config(pool_config, pool_name):
            return None

        # 尝试使用容量分析器获取额外信息
        try:
            from engine.analyzers.ippool_capacity_analyzer import IPPoolCapacityAnalyzer

            analyzer = IPPoolCapacityAnalyzer()
            capacity_info = analyzer.analyze_pool_capacity(pool_name, pool_config)

            # 记录容量分析结果
            if capacity_info.total_addresses > 0:
                log(_("nat.pool_capacity_analysis",
                     pool_name=pool_name,
                     total_addresses=capacity_info.total_addresses,
                     usable_addresses=capacity_info.usable_addresses,
                     efficiency=capacity_info.network_efficiency * 100,
                     alignment=capacity_info.subnet_alignment), "info")

                # 如果效率过低，记录警告
                if capacity_info.network_efficiency < 0.5:
                    log(_("nat.pool_low_efficiency_warning",
                         pool_name=pool_name,
                         efficiency=capacity_info.network_efficiency * 100), "warning")

                # 如果碎片化严重，记录警告
                if capacity_info.fragmentation_score > 0.7:
                    log(_("nat.pool_high_fragmentation_warning",
                         pool_name=pool_name,
                         fragmentation=capacity_info.fragmentation_score * 100), "warning")

        except ImportError:
            log(_("nat.capacity_analyzer_unavailable"), "debug")
        except Exception as e:
            log(_("nat.capacity_analysis_failed", pool_name=pool_name, error=str(e)), "warning")

        # 应用名称清理
        try:
            from engine.utils.name_validator import clean_ntos_name, validate_ntos_name

            clean_pool_name = clean_ntos_name(pool_name, 64)
            is_valid, error_msg = validate_ntos_name(clean_pool_name)

            if not is_valid:
                log(_("nat.pool_name_validation_failed",
                     pool_name=pool_name, clean_name=clean_pool_name, error=error_msg), "warning")
                # 使用清理后的名称，但记录原始名称
                pool_name = clean_pool_name
            elif clean_pool_name != pool_name:
                log(_("nat.pool_name_cleaned",
                     original=pool_name, cleaned=clean_pool_name), "info")
                pool_name = clean_pool_name

        except ImportError:
            log(_("nat.name_validator_unavailable"), "debug")
        except Exception as e:
            log(_("nat.name_validation_failed", pool_name=pool_name, error=str(e)), "warning")

        # 创建NAT池配置
        nat_pool = {
            "name": pool_name,
            "address": {
                "value": f"{pool_config['startip']}-{pool_config['endip']}"
            }
        }

        # 添加描述信息（如果存在）
        if "comment" in pool_config and pool_config["comment"]:
            nat_pool["desc"] = pool_config["comment"]

        return nat_pool
    
    def _validate_vip_config(self, vip_config: Dict, vip_name: str) -> bool:
        """
        验证VIP配置完整性
        
        Args:
            vip_config: VIP配置
            vip_name: VIP名称
            
        Returns:
            bool: 配置是否有效
        """
        required_fields = ["extip", "mappedip"]
        for field in required_fields:
            if field not in vip_config or not vip_config[field]:
                log(_("nat.vip_missing_field", vip_name=vip_name, field=field), "warning")
                return False
        return True
    
    def _validate_ippool_config(self, pool_config: Dict, pool_name: str) -> bool:
        """
        验证IP池配置完整性（增强版本）

        Args:
            pool_config: IP池配置
            pool_name: 池名称

        Returns:
            bool: 配置是否有效
        """
        # 导入增强验证器
        try:
            from engine.validators.ippool_validator import EnhancedIPPoolValidator

            # 使用增强验证器进行详细验证
            validator = EnhancedIPPoolValidator()
            validation_result = validator.validate_ippool(pool_name, pool_config)

            # 记录验证结果
            if not validation_result.is_valid:
                for error in validation_result.errors:
                    log(_("nat.ippool_validation_error", pool_name=pool_name, error=error), "error")
                return False

            # 记录警告信息
            for warning in validation_result.warnings:
                log(_("nat.ippool_validation_warning", pool_name=pool_name, warning=warning), "warning")

            # 记录容量信息
            if validation_result.capacity_info:
                cap_info = validation_result.capacity_info
                log(_("nat.ippool_capacity_info",
                     pool_name=pool_name,
                     total_ips=cap_info["total_ips"],
                     efficiency=cap_info["efficiency"] * 100), "info")

            # 记录优化建议
            for suggestion in validation_result.optimization_suggestions:
                log(_("nat.ippool_optimization_suggestion",
                     pool_name=pool_name, suggestion=suggestion), "info")

            return True

        except ImportError:
            # 如果增强验证器不可用，回退到基本验证
            log(_("nat.enhanced_validator_unavailable"), "warning")
            return self._basic_validate_ippool_config(pool_config, pool_name)
        except Exception as e:
            # 验证过程出错，记录错误并回退到基本验证
            log(_("nat.enhanced_validation_failed", pool_name=pool_name, error=str(e)), "warning")
            return self._basic_validate_ippool_config(pool_config, pool_name)

    def _basic_validate_ippool_config(self, pool_config: Dict, pool_name: str) -> bool:
        """
        基本IP池配置验证（原始版本）

        Args:
            pool_config: IP池配置
            pool_name: 池名称

        Returns:
            bool: 配置是否有效
        """
        required_fields = ["startip", "endip"]
        for field in required_fields:
            if field not in pool_config or not pool_config[field]:
                log(_("nat.ippool_missing_field", pool_name=pool_name, field=field), "warning")
                return False
        return True
