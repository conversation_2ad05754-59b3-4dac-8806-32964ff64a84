"""
FortiGate策略中服务对象iprange字段的关联处理器

负责在策略转换过程中自动关联动态生成的地址对象，
确保FortiGate服务对象的iprange语义在NTOS策略中得到正确体现。
"""

from typing import Dict, List, Any, Optional, Tuple
from engine.utils.logger import log
from engine.utils.i18n import _


class PolicyIprangeIntegrator:
    """
    策略iprange关联处理器
    
    负责分析策略中使用的服务对象，检测是否包含iprange字段，
    并将对应的动态地址对象自动添加到策略的地址字段中。
    """
    
    def __init__(self):
        self.service_to_address_mapping = {}
        self.integration_stats = {
            "policies_analyzed": 0,
            "policies_modified": 0,
            "addresses_integrated": 0,
            "services_with_iprange": 0
        }
    
    def set_service_mapping(self, service_mapping: Dict[str, List[Dict]]):
        """
        设置服务对象到地址对象的映射关系
        
        Args:
            service_mapping: 从地址处理阶段获取的映射关系
        """
        self.service_to_address_mapping = service_mapping or {}
        self.integration_stats["services_with_iprange"] = len(self.service_to_address_mapping)
        
        if self.service_to_address_mapping:
            log(f"策略iprange集成器：加载了 {len(self.service_to_address_mapping)} 个服务映射关系", "info")
            for service_name, mappings in self.service_to_address_mapping.items():
                log(f"  - 服务 {service_name}: {len(mappings)} 个地址映射", "debug")

    def analyze_policy_services(self, policy: Dict[str, Any]) -> List[str]:
        """
        分析策略中使用的服务对象，返回包含iprange的服务列表
        
        Args:
            policy: 策略配置字典
            
        Returns: List[str]: 包含iprange的服务对象名称列表
        """
        services_with_iprange = []
        
        # 获取策略中的服务字段
        policy_services = policy.get('service', [])
        if isinstance(policy_services, str):
            policy_services = [policy_services]
        elif not isinstance(policy_services, list):
            policy_services = []
        
        # 检查每个服务是否有对应的iprange地址对象
        for service_name in policy_services:
            if service_name in self.service_to_address_mapping:
                services_with_iprange.append(service_name)

        
        return services_with_iprange
    
    def integrate_iprange_addresses(self, policy: Dict[str, Any], 
                                  services_with_iprange: List[str]) -> Tuple[bool, List[str]]:
        """
        将iprange对应的地址对象集成到策略中
        
        Args:
            policy: 策略配置字典
            services_with_iprange: 包含iprange的服务对象列表
            
        Returns: Tuple[bool, List[str]]: (是否修改了策略, 添加的地址对象列表)
        """
        if not services_with_iprange:
            return False, []
        
        policy_modified = False
        added_addresses = []
        
        for service_name in services_with_iprange:
            mappings = self.service_to_address_mapping.get(service_name, [])
            
            for mapping in mappings:
                address_name = mapping['address_name']
                target_field = mapping.get('target_field', 'destination')
                integration_mode = mapping.get('policy_integration', 'auto')
                
                if integration_mode == 'disabled':

                    continue
                
                # 根据目标字段添加地址对象
                if target_field == 'destination':
                    if self._add_address_to_policy_field(policy, 'dstaddr', address_name):
                        policy_modified = True
                        added_addresses.append(f"{address_name}(dst)")
                elif target_field == 'source':
                    if self._add_address_to_policy_field(policy, 'srcaddr', address_name):
                        policy_modified = True
                        added_addresses.append(f"{address_name}(src)")
                elif target_field == 'both':
                    # 同时添加到源和目标地址
                    src_added = self._add_address_to_policy_field(policy, 'srcaddr', address_name)
                    dst_added = self._add_address_to_policy_field(policy, 'dstaddr', address_name)
                    if src_added or dst_added:
                        policy_modified = True
                        if src_added:
                            added_addresses.append(f"{address_name}(src)")
                        if dst_added:
                            added_addresses.append(f"{address_name}(dst)")
                

        
        return policy_modified, added_addresses
    
    def _add_address_to_policy_field(self, policy: Dict[str, Any],
                                   field_name: str, address_name: str) -> bool:
        """
        将地址对象添加到策略的指定字段中

        Args:
            policy: 策略配置字典
            field_name: 字段名称 (srcaddr/dstaddr 或 source-network/dest-network)
            address_name: 地址对象名称

        Returns:
            bool: 是否成功添加
        """
        # 检测策略格式并映射字段名称
        actual_field_name = self._map_field_name(policy, field_name)
        if not actual_field_name:
            log(f"无法确定策略字段映射: {field_name}", "warning")
            return False

        # 获取当前字段值
        current_addresses = policy.get(actual_field_name, [])

        # 处理NTOS格式的地址字段（列表中包含字典）
        if isinstance(current_addresses, list) and current_addresses and isinstance(current_addresses[0], dict):
            # NTOS格式: [{'name': 'addr1'}, {'name': 'addr2'}]
            address_names = [addr.get('name') for addr in current_addresses if isinstance(addr, dict)]
            if address_name in address_names:

                return False

            # 添加新地址对象
            current_addresses.append({'name': address_name})
            policy[actual_field_name] = current_addresses

            return True

        else:
            # FortiGate格式处理
            # 标准化为列表格式
            if isinstance(current_addresses, str):
                if current_addresses.lower() in ['all', 'any', '']:
                    current_addresses = []
                else:
                    current_addresses = [current_addresses]
            elif not isinstance(current_addresses, list):
                current_addresses = []

            # 检查是否已存在
            if address_name in current_addresses:

                return False

            # 添加地址对象
            current_addresses.append(address_name)
            policy[actual_field_name] = current_addresses


            return True

    def _map_field_name(self, policy: Dict[str, Any], field_name: str) -> Optional[str]:
        """
        根据策略格式映射字段名称

        Args:
            policy: 策略配置字典
            field_name: 原始字段名称

        Returns:
            Optional[str]: 映射后的字段名称
        """
        # 检测策略是否已转换为NTOS格式
        if 'source-network' in policy or 'dest-network' in policy:
            # NTOS格式
            field_mapping = {
                'srcaddr': 'source-network',
                'dstaddr': 'dest-network'
            }
            return field_mapping.get(field_name, field_name)
        else:
            # FortiGate格式
            return field_name
    
    def process_policy(self, policy: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理单个策略，集成iprange相关的地址对象
        
        Args:
            policy: 策略配置字典
            
        Returns: Dict[str, Any]: 处理结果统计
        """
        self.integration_stats["policies_analyzed"] += 1
        
        # 分析策略中的服务对象
        services_with_iprange = self.analyze_policy_services(policy)
        
        if not services_with_iprange:
            return {
                "policy_name": policy.get('name', 'unknown'),
                "modified": False,
                "services_with_iprange": [],
                "added_addresses": []
            }
        
        # 集成iprange地址对象
        policy_modified, added_addresses = self.integrate_iprange_addresses(policy, services_with_iprange)
        
        if policy_modified:
            self.integration_stats["policies_modified"] += 1
            self.integration_stats["addresses_integrated"] += len(added_addresses)
            
            log(f"策略 {policy.get('name', 'unknown')} 已集成iprange地址对象: {added_addresses}", "info")
        
        return {
            "policy_name": policy.get('name', 'unknown'),
            "modified": policy_modified,
            "services_with_iprange": services_with_iprange,
            "added_addresses": added_addresses
        }
    
    def get_integration_stats(self) -> Dict[str, Any]:
        """
        获取集成统计信息
        
        Returns: Dict[str, Any]: 统计信息
        """
        return self.integration_stats.copy()
    
    def reset_stats(self):
        """重置统计信息"""
        self.integration_stats = {
            "policies_analyzed": 0,
            "policies_modified": 0,
            "addresses_integrated": 0,
            "services_with_iprange": len(self.service_to_address_mapping)
        }
