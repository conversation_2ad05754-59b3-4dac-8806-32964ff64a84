#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Fortigate 策略转换处理器模块

处理 Fortigate 防火墙策略配置，转换为 NTOS 安全策略和 NAT 规则格式
支持以下转换：
1. 安全策略 (security-policy) 
2. 静态/动态 SNAT 规则
3. 静态 DNAT 规则
"""

import re
from typing import Dict, List, Any, Optional, Tuple
from engine.utils.logger import log
from engine.utils.i18n import _
from engine.utils.name_validator import clean_ntos_name
from engine.processors.service_processor import ServiceProcessor


class PolicyProcessor:
    """Fortigate 策略转换处理器类"""
    
    def __init__(self):
        """初始化处理器"""
        self.name = "policy_processor"
        self.description = _("policy.processor_description")
        
        # 初始化服务处理器用于服务映射
        self.service_processor = ServiceProcessor()
        
        # 动作映射
        self.action_mapping = {
            "accept": "permit",
            "deny": "deny",
            "reject": "deny"
        }

        # 接口到安全区域的映射（仅在zone_only或both模式下使用）
        self.interface_zone_mapping = {
            "lan": "trust",
            "wan": "untrust",
            "wan1": "untrust",
            "wan2": "untrust",
            "dmz": "DMZ",
            "internal": "trust",
            "external": "untrust"
        }

        # 策略转换模式枚举
        self.CONVERSION_MODES = {
            "interface_only": "interface_only",  # 仅生成接口字段（推荐，默认）
            "zone_only": "zone_only",           # 仅生成区域字段（兼容旧行为）
            "both": "both"                      # 同时生成接口和区域字段（当前行为）
        }

        # 不支持的特性列表
        self.unsupported_features = {
            "fixedport",  # 固定端口设置
            "users",      # 用户认证
            "groups"      # 用户组认证
        }

    def _get_conversion_mode(self, context=None) -> str:
        """
        获取策略转换模式配置

        Args:
            context: 数据上下文，可选

        Returns:
            str: 转换模式 (interface_only, zone_only, both)
        """
        # 默认使用混合模式（支持纯区域、纯接口、接口+区域混合情况）
        default_mode = self.CONVERSION_MODES["both"]

        if context:
            # 从上下文获取配置
            mode = context.get_data("policy_conversion_mode", default_mode)
            if mode in self.CONVERSION_MODES.values():
                return mode

        return default_mode

    def _is_zone_name(self, name: str, context=None) -> bool:
        """
        检查给定名称是否为已定义的区域名称

        Args:
            name: 要检查的名称
            context: 数据上下文

        Returns:
            bool: 如果是区域名称返回True，否则返回False
        """
        try:
            # 特殊处理"any"接口 - 明确标识为接口而非区域
            if name.lower() == "any":
                return False

            # 检查FortiGate逻辑区域
            fortigate_logical_zones = {"LAN", "WAN", "DMZ", "ssl.root", "MGMT"}
            if name in fortigate_logical_zones:
                return True

            # 检查用户定义的区域
            if context:
                zone_names = self._get_zone_names_from_context(context)
                if name in zone_names:
                    return True

            return False

        except Exception as e:
            log(f"ERROR: Exception in _is_zone_name for '{name}': {str(e)}", "error")
            return False

    def _get_zone_names_from_context(self, context) -> set:
        """
        从数据上下文获取所有区域名称

        Args:
            context: 数据上下文

        Returns:
            set: 所有区域名称的集合
        """
        zone_names = set()

        try:
            if not context:
                return zone_names

            # 从zone_processing_result获取区域信息
            zone_result = context.get_data("zone_processing_result")
            if zone_result and isinstance(zone_result, dict):
                zones = zone_result.get("zones", [])
                for zone in zones:
                    if isinstance(zone, dict) and "name" in zone:
                        zone_names.add(zone["name"])

            # 从zones数据获取区域信息
            zones_data = context.get_data("zones", [])
            if zones_data:
                for zone in zones_data:
                    if isinstance(zone, dict) and "name" in zone:
                        zone_names.add(zone["name"])

            # 从FortiGate配置数据中获取区域信息
            config_data = context.get_data("config_data")
            if config_data and isinstance(config_data, dict):
                # 检查system.zone配置
                if "system" in config_data and "zone" in config_data["system"]:
                    system_zones = config_data["system"]["zone"]
                    if isinstance(system_zones, dict):
                        zone_names.update(system_zones.keys())

                # 检查zones配置（另一种可能的数据结构）
                if "zones" in config_data:
                    zones = config_data["zones"]
                    if isinstance(zones, list):
                        for zone in zones:
                            if isinstance(zone, dict) and "name" in zone:
                                zone_names.add(zone["name"])
                    elif isinstance(zones, dict):
                        zone_names.update(zones.keys())



        except Exception as e:
            log(_("policy_processor.get_zone_names_exception", error=str(e)), "error")

        return zone_names

    def _separate_zones_and_interfaces(self, names: List[str], context=None) -> tuple:
        """
        分离区域名称和接口名称

        Args:
            names: 名称列表
            context: 数据上下文

        Returns:
            tuple: (zone_names, interface_names)
        """
        zones = []
        interfaces = []

        for name in names:
            is_zone = self._is_zone_name(name, context)

            if is_zone:
                zones.append(name)
                log(_("policy_processor.zone_identified", name=name), "info")
            else:
                interfaces.append(name)
                log(_("policy_processor.interface_identified", name=name), "info")
        return zones, interfaces

    def _add_mixed_fields_with_mapping(self, security_policy: Dict, srcintf_list: List, dstintf_list: List,
                                      interface_mapping: Dict, policy_name: str, context=None):
        """
        混合模式：智能处理区域和接口，为每个源/目标分别选择正确的字段类型

        Args:
            security_policy: 安全策略字典
            srcintf_list: 源接口列表
            dstintf_list: 目标接口列表
            interface_mapping: 接口映射字典
            policy_name: 策略名称（用于日志）
            context: 数据上下文
        """
        # 处理源接口/区域
        if srcintf_list:
            zone_names, interface_names = self._separate_zones_and_interfaces(srcintf_list, context)

            source_zones = []
            source_interfaces = []

            # 处理区域名称
            for zone in zone_names:
                source_zones.append({"name": zone})
                log(_("policy_processor.zone_identified", name=zone), "info")

            # 处理接口名称
            for interface in interface_names:
                mapped_name = self._get_mapped_interface_name_with_mapping(interface, interface_mapping)
                if mapped_name:
                    source_interfaces.append({"name": mapped_name})
                    log(_("policy_processor.interface_mapped", original=interface, mapped=mapped_name), "info")
                else:
                    log(_("policy_processor.interface_mapping_not_found", interface=interface, policy=policy_name), "warning")

            # 添加到安全策略
            if source_zones:
                security_policy["source-zone"] = source_zones
            if source_interfaces:
                security_policy["source-interface"] = source_interfaces

        # 处理目标接口/区域
        if dstintf_list:
            zone_names, interface_names = self._separate_zones_and_interfaces(dstintf_list, context)

            dest_zones = []
            dest_interfaces = []

            # 处理区域名称
            for zone in zone_names:
                dest_zones.append({"name": zone})
                log(_("policy_processor.zone_identified", name=zone), "info")

            # 处理接口名称
            for interface in interface_names:
                mapped_name = self._get_mapped_interface_name_with_mapping(interface, interface_mapping)
                if mapped_name:
                    dest_interfaces.append({"name": mapped_name})
                    log(_("policy_processor.interface_mapped", original=interface, mapped=mapped_name), "info")
                else:
                    log(_("policy_processor.interface_mapping_not_found", interface=interface, policy=policy_name), "warning")

            # 添加到安全策略
            if dest_zones:
                security_policy["dest-zone"] = dest_zones
            if dest_interfaces:
                security_policy["dest-interface"] = dest_interfaces

    def process_policies(self, policies: List[Dict], vips: Dict = None, ippools: Dict = None, context=None) -> Dict[str, Any]:
        """
        处理策略列表，生成安全策略和NAT规则 - 修复统计逻辑

        Args:
            policies: Fortigate 策略列表
            vips: VIP 配置字典 {name: config}
            ippools: IP池配置字典 {name: config}
            context: 数据上下文，用于获取转换模式配置

        Returns:
            dict: 处理结果
                {
                    "security_policies": [安全策略列表],
                    "nat_rules": [NAT规则列表],
                    "nat_pools": [NAT池列表],
                    "warnings": [警告信息列表],
                    "statistics": {统计信息}
                }
        """
        if not policies:
            log(_("policy.no_policies_to_process"))
            return self._empty_result()
        
        vips = vips or {}
        ippools = ippools or {}
        
        result = {
            "security_policies": [],
            "nat_rules": [],
            "nat_pools": [],
            "warnings": [],
            "statistics": {
                "total_policies": len(policies),
                "processed_policies": 0,
                "successful_policies": 0,
                "failed_policies": 0,
                "skipped_policies": 0,
                "security_policies_created": 0,
                "nat_rules_created": 0,
                "warnings_count": 0
            }
        }
        
        log(_("policy.start_processing", count=len(policies)))
        
        # 预处理IP池，生成NAT池配置
        nat_pools = self._process_ippools(ippools)
        result["nat_pools"] = nat_pools
        
        # 处理每个策略 - 异常隔离
        for policy in policies:
            try:
                policy_result = self._process_single_policy(policy, vips, ippools, context)
                result["statistics"]["processed_policies"] += 1

                # 合并结果
                if policy_result["security_policy"]:
                    result["security_policies"].append(policy_result["security_policy"])
                    result["statistics"]["security_policies_created"] += 1
                    result["statistics"]["successful_policies"] += 1
                else:
                    result["statistics"]["skipped_policies"] += 1

                if policy_result["nat_rules"]:
                    result["nat_rules"].extend(policy_result["nat_rules"])
                    result["statistics"]["nat_rules_created"] += len(policy_result["nat_rules"])

                result["warnings"].extend(policy_result["warnings"])
                result["statistics"]["warnings_count"] += len(policy_result["warnings"])

            except Exception as e:
                result["statistics"]["failed_policies"] += 1
                error_msg = _("policy.processing_failed",
                            policy_id=policy.get("policyid", "unknown"), error=str(e))
                result["warnings"].append(error_msg)
                log(error_msg, "error")
                continue  # 继续处理下一个策略
        
        # 计算成功率（修复计算逻辑）
        total = result["statistics"]["total_policies"]
        successful = result["statistics"]["successful_policies"]
        success_rate = (successful / total * 100) if total > 0 else 0

        # 生成详细统计信息
        detailed_stats = self._generate_detailed_statistics(result)
        result["statistics"] = detailed_stats

        log(_("policy.processing_complete",
              total=total,
              successful=successful,
              success_rate=success_rate,
              security_policies=result["statistics"]["security_policies_created"],
              nat_rules=result["statistics"]["nat_rules_created"]))

        # 记录详细统计信息
        log(_("policy.detailed_statistics",
              by_type=str(detailed_stats["by_type"]),
              quality_metrics=str(detailed_stats["quality_metrics"])), "info")

        return result
    
    def _process_single_policy(self, policy: Dict, vips: Dict, ippools: Dict, context=None) -> Dict[str, Any]:
        """
        处理单个策略 - 增强版本

        Args:
            policy: 策略配置
            vips: VIP配置字典
            ippools: IP池配置字典
            context: 数据上下文，用于获取转换模式配置

        Returns:
            dict: 单个策略的处理结果
        """
        result = {
            "security_policy": None,
            "nat_rules": [],
            "warnings": []
        }

        policy_id = policy.get("policyid", "unknown")
        policy_name = self._generate_intelligent_policy_name(policy, policy_id)

        # 使用增强验证
        validation_result = self._enhanced_policy_validation(policy, policy_id, context)

        if not validation_result["can_process"]:
            result["warnings"].extend(validation_result["issues"])
            log(_("policy.validation_failed", policy_id=policy_id,
                 issues="; ".join(validation_result["issues"])), "warning")

            # 添加诊断建议
            suggestions = self._diagnose_policy_issues(policy, policy_id, validation_result)
            if suggestions:
                log(_("policy.diagnostic_suggestions", policy_id=policy_id,
                     suggestions="; ".join(suggestions)), "info")
                result["warnings"].extend([f"诊断建议: {s}" for s in suggestions])

            return result

        # 记录验证级别
        if validation_result["validation_level"] != "FULL":
            log(_("policy.partial_validation", policy_id=policy_id,
                 level=validation_result["validation_level"]), "info")

        result["warnings"].extend(validation_result["issues"])


        # 检查不支持的特性
        warnings = self._check_unsupported_features(policy, policy_id)
        result["warnings"].extend(warnings)

        # 分类策略类型
        policy_classification = self._classify_policy(policy, vips, ippools)

        # 生成安全策略（所有策略都需要）
        try:
            security_policy = self._generate_security_policy(policy, policy_classification, context)
            result["security_policy"] = security_policy
        except Exception as e:
            result["warnings"].append(_("policy.security_policy_generation_failed",
                                      policy_id=policy_id, error=str(e)))
            log(_("policy.security_policy_generation_failed",
                 policy_id=policy_id, error=str(e)), "error")

        # 生成NAT规则（根据分类决定）
        try:
            nat_rules = self._generate_nat_rules(policy, policy_classification, vips, ippools)
            result["nat_rules"] = nat_rules
        except Exception as e:
            result["warnings"].append(_("policy.nat_rules_generation_failed",
                                      policy_id=policy_id, error=str(e)))
            log(_("policy.nat_rules_generation_failed",
                 policy_id=policy_id, error=str(e)), "error")

        return result

    def _validate_policy_interface_mapping(self, policy: Dict, policy_id: str, result: Dict) -> bool:
        """
        增强的策略接口映射验证 - 支持特殊接口和容错处理

        Args:
            policy: 策略配置
            policy_id: 策略ID
            result: 处理结果字典，用于添加警告信息

        Returns:
            bool: True表示策略可以处理，False表示应跳过策略处理
        """
        # 特殊接口白名单
        SPECIAL_INTERFACES = ["any", "all", "*"]
        from engine.utils.interface_mapper import load_interface_mapping
        from engine.utils.file_utils import get_current_interface_mapping_file_path

        try:
            # 加载最终接口映射文件
            final_mapping_file = get_current_interface_mapping_file_path()

            # 检查文件是否存在
            import os
            if not os.path.exists(final_mapping_file):
                warning_msg = _("policy.interface_mapping_file_not_found", policy_id=policy_id, file=final_mapping_file)
                result["warnings"].append(warning_msg)
                log(warning_msg, "warning")
                return True  # 改为True，允许继续处理

            raw_interface_mapping = load_interface_mapping(final_mapping_file)

            if not raw_interface_mapping:
                warning_msg = _("policy.no_interface_mapping_file", policy_id=policy_id)
                result["warnings"].append(warning_msg)
                log(warning_msg, "warning")
                return True  # 改为True，允许继续处理

            # 处理不同格式的接口映射文件
            interface_mapping = {}
            if "interface_mappings" in raw_interface_mapping:
                # 嵌套格式，提取interface_mappings部分
                interface_mapping = raw_interface_mapping["interface_mappings"]
            elif isinstance(raw_interface_mapping, dict):
                # 扁平格式，过滤掉非接口映射的键
                for key, value in raw_interface_mapping.items():
                    if key not in ["default_mapping", "device_info", "mapping_rules", "test_scenarios"] and isinstance(value, str):
                        interface_mapping[key] = value

            if not interface_mapping:
                warning_msg = _("policy.no_interface_mapping_file", policy_id=policy_id)
                result["warnings"].append(warning_msg)
                log(warning_msg, "warning")
                return True  # 改为True，允许继续处理

            # 检查源接口映射 - 容错处理
            validation_issues = []
            srcintf_list = policy.get("srcintf", [])
            for intf in srcintf_list:
                if intf and intf.strip():
                    clean_intf = intf.strip('"').strip("'")

                    if clean_intf in SPECIAL_INTERFACES:
                        # 特殊接口 - 记录但允许通过
                        log(_("policy.special_interface_used",
                             policy_id=policy_id, interface=clean_intf, type="源接口"), "info")
                        continue
                    elif clean_intf not in interface_mapping:
                        # 映射缺失 - 记录警告但不阻断
                        warning_msg = _("policy.interface_mapping_not_found",
                                      policy_id=policy_id, interface=clean_intf, type="源接口")
                        validation_issues.append(warning_msg)
                        log(warning_msg, "warning")

            # 检查目标接口映射 - 容错处理
            dstintf_list = policy.get("dstintf", [])
            for intf in dstintf_list:
                if intf and intf.strip():
                    clean_intf = intf.strip('"').strip("'")

                    if clean_intf in SPECIAL_INTERFACES:
                        # 特殊接口 - 记录但允许通过
                        log(_("policy.special_interface_used",
                             policy_id=policy_id, interface=clean_intf, type="目标接口"), "info")
                        continue
                    elif clean_intf not in interface_mapping:
                        # 映射缺失 - 记录警告但不阻断
                        warning_msg = _("policy.interface_mapping_not_found",
                                      policy_id=policy_id, interface=clean_intf, type="目标接口")
                        validation_issues.append(warning_msg)
                        log(warning_msg, "warning")

            # 将验证问题添加到结果中，但不阻断处理
            result["warnings"].extend(validation_issues)

            # 只有在存在严重问题时才返回False（如文件不存在）
            return True

        except Exception as e:
            # 异常情况下记录错误但允许继续处理
            warning_msg = _("policy.interface_mapping_validation_error",
                          policy_id=policy_id, error=str(e))
            result["warnings"].append(warning_msg)
            log(warning_msg, "warning")
            return True  # 改为True，允许继续处理

    def _enhanced_policy_validation(self, policy: Dict, policy_id: str, context=None) -> Dict[str, Any]:
        """
        增强的策略验证 - 分层验证策略

        Args:
            policy: 策略配置
            policy_id: 策略ID
            context: 数据上下文

        Returns:
            dict: {
                "is_valid": bool,
                "validation_level": str,  # "FULL", "PARTIAL", "MINIMAL"
                "issues": list,
                "can_process": bool
            }
        """
        validation_result = {
            "is_valid": True,
            "validation_level": "FULL",
            "issues": [],
            "can_process": True
        }

        # 第一层：基础验证
        if not policy.get("policyid"):
            validation_result["issues"].append(_("policy.validation.missing_policy_id"))
            validation_result["validation_level"] = "MINIMAL"

        policy_name = policy.get("name")
        if not policy_name:
            validation_result["issues"].append(_("policy.validation.missing_policy_name"))
            validation_result["validation_level"] = "PARTIAL"

        # 第二层：接口验证
        interface_validation = {"warnings": []}
        interface_result = self._validate_policy_interface_mapping(policy, policy_id, interface_validation)
        if interface_validation.get("warnings"):
            validation_result["issues"].extend(interface_validation["warnings"])
            if validation_result["validation_level"] == "FULL":
                validation_result["validation_level"] = "PARTIAL"

        # 第三层：配置完整性验证
        required_fields = ["srcintf", "dstintf", "action"]
        missing_fields = []
        for field in required_fields:
            if not policy.get(field):
                missing_fields.append(field)

        if missing_fields:
            validation_result["issues"].append(_("policy.validation.missing_required_fields",
                                              fields=", ".join(missing_fields)))
            validation_result["validation_level"] = "MINIMAL"

        # 第四层：动作验证
        action = policy.get("action", "").lower()
        if action not in ["accept", "deny", "reject"]:
            validation_result["issues"].append(_("policy.validation.invalid_action", action=action))
            if validation_result["validation_level"] == "FULL":
                validation_result["validation_level"] = "PARTIAL"

        # 第五层：服务验证
        services = policy.get("service", [])
        if not services:
            validation_result["issues"].append(_("policy.validation.missing_services"))
            if validation_result["validation_level"] == "FULL":
                validation_result["validation_level"] = "PARTIAL"

        # 决定是否可以处理
        critical_issues = [issue for issue in validation_result["issues"]
                          if "missing_policy_id" in str(issue) or "missing_required_fields" in str(issue)]

        if validation_result["validation_level"] == "MINIMAL" and len(critical_issues) > 1:
            validation_result["can_process"] = False
            validation_result["is_valid"] = False

        return validation_result

    def _generate_detailed_statistics(self, result: Dict) -> Dict:
        """
        生成详细统计信息 - 多维度分析

        Args:
            result: 处理结果字典

        Returns:
            dict: 增强的统计信息
        """
        stats = result["statistics"]

        # 计算各种比率
        total = stats["total_policies"]
        if total > 0:
            stats["success_rate"] = (stats["successful_policies"] / total) * 100
            stats["failure_rate"] = (stats["failed_policies"] / total) * 100
            stats["skip_rate"] = (stats["skipped_policies"] / total) * 100
            stats["processing_rate"] = (stats["processed_policies"] / total) * 100
        else:
            stats["success_rate"] = stats["failure_rate"] = stats["skip_rate"] = stats["processing_rate"] = 0

        # 按策略类型分类统计
        stats["by_type"] = {
            "security_only": 0,
            "nat_only": 0,
            "combined": 0,
            "disabled": 0
        }

        # 分析生成的策略和NAT规则
        security_policies = result.get("security_policies", [])
        nat_rules = result.get("nat_rules", [])

        for policy in security_policies:
            policy_name = policy.get("name", "")
            enabled = policy.get("enabled", True)

            if not enabled:
                stats["by_type"]["disabled"] += 1
            else:
                # 检查是否有对应的NAT规则
                has_nat = any(rule.get("name", "").startswith(policy_name.split("_")[0]) for rule in nat_rules)
                if has_nat:
                    stats["by_type"]["combined"] += 1
                else:
                    stats["by_type"]["security_only"] += 1

        # 按验证级别统计（需要在处理过程中收集）
        stats["by_validation_level"] = {
            "full": stats["successful_policies"],  # 假设成功的都是FULL级别
            "partial": 0,  # 需要在处理过程中收集
            "minimal": 0   # 需要在处理过程中收集
        }

        # 按错误类型统计
        stats["by_error_type"] = {
            "interface_mapping_errors": 0,
            "validation_errors": 0,
            "generation_errors": 0,
            "other_errors": 0
        }

        # 分析警告信息来统计错误类型
        warnings = result.get("warnings", [])
        for warning in warnings:
            warning_str = str(warning).lower()
            if "interface" in warning_str and "mapping" in warning_str:
                stats["by_error_type"]["interface_mapping_errors"] += 1
            elif "validation" in warning_str:
                stats["by_error_type"]["validation_errors"] += 1
            elif "generation" in warning_str:
                stats["by_error_type"]["generation_errors"] += 1
            else:
                stats["by_error_type"]["other_errors"] += 1

        # 质量指标
        stats["quality_metrics"] = {
            "avg_warnings_per_policy": len(warnings) / total if total > 0 else 0,
            "nat_coverage_rate": (len(nat_rules) / stats["successful_policies"]) * 100 if stats["successful_policies"] > 0 else 0,
            "policy_completeness": (stats["security_policies_created"] / total) * 100 if total > 0 else 0
        }

        return stats

    def _diagnose_policy_issues(self, policy: Dict, policy_id: str, validation_result: Dict = None) -> List[str]:
        """
        诊断策略问题，提供修复建议

        Args:
            policy: 策略配置
            policy_id: 策略ID
            validation_result: 验证结果（可选）

        Returns:
            list: 修复建议列表
        """
        suggestions = []

        # 检查接口问题
        for intf_type, intf_name in [("srcintf", "源接口"), ("dstintf", "目标接口")]:
            interfaces = policy.get(intf_type, [])
            if not interfaces:
                suggestions.append(f"建议: 添加{intf_name}配置")
            else:
                for intf in interfaces:
                    if isinstance(intf, str):
                        # 检查不存在的接口
                        if "nonexistent" in intf.lower():
                            suggestions.append(f"建议: 检查{intf_name} '{intf}' 是否正确，建议使用有效的接口名称如 'wan1', 'lan1', 'dmz1' 或特殊接口 'any'")
                        # 检查可能的拼写错误
                        elif intf not in ["any", "all", "*", "mgmt", "wan1", "wan2", "lan1", "lan2", "dmz1", "dmz2"]:
                            suggestions.append(f"建议: {intf_name} '{intf}' 可能存在拼写错误，请检查接口映射配置")

        # 检查NAT配置
        nat_enabled = policy.get("nat", "").lower() == "enable"
        poolname = policy.get("poolname")
        if nat_enabled and not poolname:
            suggestions.append("建议: NAT启用时应配置IP池名称 (poolname)")

        # 检查VIP配置
        dstaddr = policy.get("dstaddr", [])
        if dstaddr:
            for addr in dstaddr:
                if isinstance(addr, str) and "vip" in addr.lower():
                    suggestions.append(f"建议: 确认VIP对象 '{addr}' 已正确配置")

        # 检查服务配置
        services = policy.get("service", [])
        if not services:
            suggestions.append("建议: 添加服务配置，如 'ALL', 'HTTP', 'HTTPS' 等")
        elif "ALL" in services and len(services) > 1:
            suggestions.append("建议: 使用 'ALL' 服务时，无需配置其他具体服务")

        # 检查动作配置
        action = policy.get("action", "").lower()
        if not action:
            suggestions.append("建议: 添加策略动作配置 (accept/deny)")
        elif action not in ["accept", "deny", "reject"]:
            suggestions.append(f"建议: 策略动作 '{action}' 无效，请使用 'accept' 或 'deny'")

        # 检查策略状态
        status = policy.get("status", "").lower()
        if status == "disable":
            suggestions.append("信息: 策略已禁用，如需启用请修改 status 为 'enable'")

        # 基于验证结果的建议
        if validation_result:
            validation_level = validation_result.get("validation_level", "FULL")
            if validation_level == "MINIMAL":
                suggestions.append("建议: 策略配置不完整，请检查必需字段 (policyid, srcintf, dstintf, action)")
            elif validation_level == "PARTIAL":
                suggestions.append("建议: 策略配置部分有问题，建议检查接口映射和服务配置")

        # 提供具体的修复示例
        if "nonexistent" in str(policy.get("srcintf", [])) or "nonexistent" in str(policy.get("dstintf", [])):
            suggestions.append("修复示例: 将 'nonexistent1' 替换为 'lan1'，将 'nonexistent2' 替换为 'wan1'")
            suggestions.append("可用接口: mgmt, wan1, lan1, dmz1 或特殊接口 any")

        return suggestions

    def _classify_policy(self, policy: Dict, vips: Dict, ippools: Dict) -> Dict[str, Any]:
        """
        分类策略类型，确定需要生成的配置类型 - 增强SNAT类型判定算法

        Args:
            policy: 策略配置
            vips: VIP配置字典
            ippools: IP池配置字典

        Returns:
            dict: 策略分类结果
        """
        dstaddr_list = policy.get("dstaddr", [])
        has_vip = any(addr in vips for addr in dstaddr_list)
        has_snat = policy.get("nat") == "enable"

        # 增强的SNAT类型判定算法
        snat_type = "static"  # 默认为静态
        snat_config = {}

        if has_snat:
            # 检查ippool enable → dynamic-snat44
            ippool_enable = policy.get("ippool") == "enable"
            poolname_list = policy.get("poolname", [])

            # 调试日志
            log("Policy {policy.get('policyid', 'unknown')} - SNAT analysis: ippool={policy.get('ippool')}, poolname={policy.get('poolname')}", "debug")

            # 确保poolname_list是列表格式
            if isinstance(poolname_list, str):
                poolname_list = [poolname_list]

            if ippool_enable and poolname_list:
                snat_type = "dynamic"
                snat_config = {
                    "pool_names": poolname_list
                }
                log(f"DEBUG: Policy {policy.get('policyid', 'unknown')} - Dynamic SNAT detected, pools: {poolname_list}", "info")
            else:
                # 检查是否有具体的IP配置（ip-range/单IP）
                # 这里可以扩展检查其他IP配置字段
                snat_type = "static"
                snat_config = {
                    "use_output_address": True  # 使用出接口地址
                }

        return {
            "needs_security_policy": True,  # 所有策略都需要安全策略
            "needs_dnat": has_vip,
            "needs_snat": has_snat,
            "snat_type": snat_type,
            "snat_config": snat_config,
            "vip_targets": [addr for addr in dstaddr_list if addr in vips],
            "port_preserve": policy.get("port_preserve", "disable")
        }
    
    def _generate_security_policy(self, policy: Dict, classification: Dict, context=None) -> Dict[str, Any]:
        """
        生成安全策略配置

        Args:
            policy: 原始策略配置
            classification: 策略分类结果
            context: 数据上下文，用于获取转换模式配置

        Returns:
            dict: 安全策略配置
        """
        policy_id = policy.get("policyid", "unknown")
        policy_name = policy.get("name", f"policy_{policy_id}")

        # 获取转换模式
        conversion_mode = self._get_conversion_mode(context)

        # 基本安全策略结构 - 包含YANG必需字段和默认值
        security_policy = {
            "name": policy_name,
            "enabled": self._get_policy_enabled_status(policy),  # 正确处理FortiGate状态
            "group-name": "def-group",  # YANG默认值
            "config-source": "manual",  # YANG默认值，NAT相关策略会覆盖为auto
            "session-timeout": 0  # YANG默认值
        }

        # 映射动作
        security_policy["action"] = self._get_policy_action(policy)

        # 根据转换模式处理接口和区域字段
        srcintf_list = policy.get("srcintf", [])
        dstintf_list = policy.get("dstintf", [])

        # 记录转换模式决策
        if conversion_mode == self.CONVERSION_MODES["zone_only"]:
            log(f"策略 {policy_name} 使用区域模式转换，将生成source-zone和dest-zone字段", "info")

        if conversion_mode == self.CONVERSION_MODES["interface_only"]:
            # 纯接口模式：只生成接口字段，不生成区域字段
            self._add_interface_fields_only(security_policy, srcintf_list, dstintf_list, policy_name)

        elif conversion_mode == self.CONVERSION_MODES["zone_only"]:
            # 纯区域模式：只生成区域字段，不生成接口字段
            self._add_zone_fields_only(security_policy, srcintf_list, dstintf_list, policy_name, context)

        elif conversion_mode == self.CONVERSION_MODES["both"]:
            # 兼容模式：同时生成接口和区域字段（原有行为）
            self._add_both_interface_and_zone_fields(security_policy, srcintf_list, dstintf_list, policy_name, context)

        else:
            # 未知模式，使用默认的纯接口模式
            log(_("policy.unknown_conversion_mode", mode=conversion_mode, policy=policy_name), "warning")
            self._add_interface_fields_only(security_policy, srcintf_list, dstintf_list, policy_name)
        
        # 映射源地址（只在有源地址且不为ALL时生成）- 使用YANG模型要求的字段名
        srcaddr_list = policy.get("srcaddr", [])
        # 处理字符串和列表两种格式
        if isinstance(srcaddr_list, str):
            srcaddr_list = [srcaddr_list]

        if srcaddr_list and srcaddr_list[0] and srcaddr_list[0].upper() not in ["ALL", "NONE"]:
            # NTOS支持多个源地址，使用YANG模型的source-network字段
            # 清理地址名称以符合YANG约束
            source_networks = []
            for addr in srcaddr_list:
                if addr and addr.upper() not in ["ALL", "NONE"]:
                    clean_addr_name = clean_ntos_name(addr, 64)
                    if clean_addr_name != addr:
                        log(_("policy_processor.address_name_cleaned", original=addr, cleaned=clean_addr_name), "info")
                    source_networks.append({"name": clean_addr_name})
            security_policy["source-network"] = source_networks

        # 映射目标地址（只在有目标地址且不为ALL时生成）- 使用YANG模型要求的字段名
        dstaddr_list = policy.get("dstaddr", [])
        # 处理字符串和列表两种格式
        if isinstance(dstaddr_list, str):
            dstaddr_list = [dstaddr_list]

        if dstaddr_list and dstaddr_list[0] and dstaddr_list[0].upper() not in ["ALL", "NONE"]:
            # 使用YANG模型的dest-network字段
            # 清理地址名称以符合YANG约束
            dest_networks = []
            for addr in dstaddr_list:
                if addr and addr.upper() not in ["ALL", "NONE"]:
                    clean_addr_name = clean_ntos_name(addr, 64)
                    if clean_addr_name != addr:
                        log(_("policy_processor.address_name_cleaned", original=addr, cleaned=clean_addr_name), "info")
                    dest_networks.append({"name": clean_addr_name})
            security_policy["dest-network"] = dest_networks

        # 映射服务（支持多个服务对象，只在有具体服务且不为ALL时生成）
        service_list = policy.get("service", [])
        # 处理字符串和列表两种格式
        if isinstance(service_list, str):
            service_list = [service_list]

        if service_list and service_list[0] and service_list[0].upper() != "ALL":
            mapped_services = []
            for service in service_list:
                if service and service.upper() != "ALL":
                    mapped_service = self._map_service_name_enhanced(service)
                    if mapped_service and mapped_service.upper() != "ANY":
                        mapped_services.append(mapped_service)  # 直接添加字符串，不是字典

            if mapped_services:
                security_policy["service"] = mapped_services
        
        # 映射时间范围
        schedule = policy.get("schedule", "always")
        security_policy["time-range"] = schedule

        # 添加安全功能映射 - 符合规范要求
        self._add_security_features(policy, security_policy, context)



        return security_policy

    def _generate_security_policy_with_mapping(self, policy: Dict, classification: Dict, interface_mapping: Dict, context=None) -> Dict[str, Any]:
        """
        生成安全策略配置（使用传入的接口映射）

        Args:
            policy: 原始策略配置
            classification: 策略分类结果
            interface_mapping: 接口映射字典
            context: 数据上下文，用于获取转换模式配置

        Returns:
            dict: 安全策略配置
        """
        policy_id = policy.get("policyid", "unknown")
        policy_name = policy.get("name", f"policy_{policy_id}")

        # 获取转换模式
        conversion_mode = self._get_conversion_mode(context)

        # 基本安全策略结构 - 包含YANG必需字段和默认值
        security_policy = {
            "name": policy_name,
            "enabled": self._get_policy_enabled_status(policy),  # 正确处理FortiGate状态
            "group-name": "def-group",  # YANG默认值
            "config-source": "manual",  # YANG默认值，NAT相关策略会覆盖为auto
            "session-timeout": 0  # YANG默认值
        }

        # 映射动作
        security_policy["action"] = self._get_policy_action(policy)

        # 根据转换模式处理接口和区域字段 - 使用传入的接口映射
        srcintf_list = policy.get("srcintf", [])
        dstintf_list = policy.get("dstintf", [])

        # 记录转换模式决策

        if conversion_mode == self.CONVERSION_MODES["interface_only"]:
            # 纯接口模式：只生成接口字段，不生成区域字段
            self._add_interface_fields_with_mapping(security_policy, srcintf_list, dstintf_list, interface_mapping, policy_name, context)

        elif conversion_mode == self.CONVERSION_MODES["zone_only"]:
            # 纯区域模式：只生成区域字段，不生成接口字段
            self._add_zone_fields_with_mapping(security_policy, srcintf_list, dstintf_list, interface_mapping, policy_name, context)

        elif conversion_mode == self.CONVERSION_MODES["both"]:
            # 混合模式：智能处理区域和接口，为每个源/目标分别选择正确的字段类型
            self._add_mixed_fields_with_mapping(security_policy, srcintf_list, dstintf_list, interface_mapping, policy_name, context)

        else:
            # 未知模式，使用默认的纯接口模式
            log(_("policy.unknown_conversion_mode_with_mapping", mode=conversion_mode, policy=policy_name), "warning")
            self._add_interface_fields_with_mapping(security_policy, srcintf_list, dstintf_list, interface_mapping, policy_name, context)

        # 处理源地址和目标地址 - 使用YANG模型要求的字段名
        srcaddr_list = policy.get("srcaddr", [])
        # 处理字符串和列表两种格式
        if isinstance(srcaddr_list, str):
            srcaddr_list = [srcaddr_list]

        if srcaddr_list and srcaddr_list[0] and srcaddr_list[0].upper() not in ["ALL", "NONE"]:
            # NTOS支持多个源地址，使用YANG模型的source-network字段
            # 清理地址名称以符合YANG约束
            source_networks = []
            for addr in srcaddr_list:
                if addr and addr.upper() not in ["ALL", "NONE"]:
                    clean_addr_name = clean_ntos_name(addr, 64)
                    if clean_addr_name != addr:
                        log(_("policy_processor.address_name_cleaned", original=addr, cleaned=clean_addr_name), "info")
                    source_networks.append({"name": clean_addr_name})
            if source_networks:
                security_policy["source-network"] = source_networks

        # 处理目标地址 - 使用YANG模型要求的字段名
        dstaddr_list = policy.get("dstaddr", [])
        # 处理字符串和列表两种格式
        if isinstance(dstaddr_list, str):
            dstaddr_list = [dstaddr_list]

        if dstaddr_list and dstaddr_list[0] and dstaddr_list[0].upper() not in ["ALL", "NONE"]:
            # 使用YANG模型的dest-network字段
            # 清理地址名称以符合YANG约束
            dest_networks = []
            for addr in dstaddr_list:
                if addr and addr.upper() not in ["ALL", "NONE"]:
                    clean_addr_name = clean_ntos_name(addr, 64)
                    if clean_addr_name != addr:
                        log(_("policy_processor.address_name_cleaned", original=addr, cleaned=clean_addr_name), "info")
                    dest_networks.append({"name": clean_addr_name})
            if dest_networks:
                security_policy["dest-network"] = dest_networks

        # 处理服务映射（支持多个服务对象）
        service_list = policy.get("service", [])
        # 处理字符串和列表两种格式
        if isinstance(service_list, str):
            service_list = [service_list]

        if service_list and service_list[0] and service_list[0].upper() != "ALL":
            mapped_services = []
            for service in service_list:
                if service and service.upper() != "ALL":
                    mapped_service = self._map_service_name_enhanced(service)
                    if mapped_service and mapped_service.upper() != "ANY":
                        mapped_services.append(mapped_service)  # 直接添加字符串，不是字典

            if mapped_services:
                security_policy["service"] = mapped_services

        # 映射时间范围
        schedule = policy.get("schedule", "always")
        security_policy["time-range"] = schedule

        # 处理注释字段 - FortiGate comments -> NTOS description
        if "comments" in policy and policy["comments"]:
            comment_text = policy["comments"].strip()
            if comment_text:
                # 确保注释长度符合YANG模型要求（通常限制在200字符以内）
                if len(comment_text) > 200:
                    comment_text = comment_text[:200]
                    log(_("policy_processor.comment_truncated",
                          policy_name=policy_name,
                          original_length=len(policy["comments"]),
                          truncated_length=200), "warning")

                # 清理description字段以符合YANG约束
                from engine.utils.name_validator import clean_ntos_description
                cleaned_comment = clean_ntos_description(comment_text)
                security_policy["description"] = cleaned_comment
                log(_("policy_processor.comment_mapped_to_description",
                      policy_name=policy_name,
                      comment=comment_text), "debug")

        # 添加安全功能映射 - 符合规范要求
        self._add_security_features(policy, security_policy, context)



        return security_policy

    def _map_interface_to_zone_with_mapping(self, interface: str, interface_mapping: Dict) -> str:
        """
        将接口名映射到安全区域（使用传入的接口映射）

        Args:
            interface: 接口名
            interface_mapping: 接口映射字典

        Returns:
            str: 安全区域名
        """
        # 获取映射后的接口名
        mapped_interface = self._get_mapped_interface_name_with_mapping(interface, interface_mapping)

        # 标准化接口名（去除引号，转小写）
        clean_interface = mapped_interface.strip('"').lower()

        # 直接映射
        if clean_interface in self.interface_zone_mapping:
            return self.interface_zone_mapping[clean_interface]

        # 模糊匹配
        for intf_pattern, zone in self.interface_zone_mapping.items():
            if intf_pattern in clean_interface:
                return zone

        # 默认映射：包含wan的为untrust，其他为trust
        if "wan" in clean_interface:
            return "untrust"
        else:
            return "trust"

    def _get_mapped_interface_name_with_mapping(self, original_interface: str, interface_mapping: Dict) -> str:
        """
        获取映射后的接口名称（使用传入的接口映射）
        注意：这个方法应该只处理接口名称，不应该处理区域名称

        Args:
            original_interface: 原始Fortigate接口名称
            interface_mapping: 接口映射字典

        Returns:
            str: 映射后的NTOS接口名称，对于"any"接口返回空字符串
        """
        # 清理接口名称
        clean_interface = original_interface.strip('"').strip("'")

        # 特殊处理"any"接口 - 返回空字符串表示不生成接口字段
        if clean_interface.lower() == "any":
            log(f"DEBUG: 接口 'any' 被处理为空字符串（表示任意接口）", "info")
            return ""

        # 检查接口映射
        if interface_mapping and clean_interface in interface_mapping:
            mapped_interface = interface_mapping[clean_interface]

            return mapped_interface

        # 如果没有映射，记录警告并返回原始名称
        if interface_mapping:
            log(f"警告: 接口 '{clean_interface}' 在映射表中未找到，使用原始名称", "warning")

        else:
            log(f"警告: 接口映射表为空，接口 '{clean_interface}' 使用原始名称", "warning")

        return clean_interface

    def _get_ntos_version(self, context=None) -> str:
        """
        获取NTOS目标版本信息 - 注意：这是NTOS的版本，不是FortiGate的版本

        Args:
            context: 数据上下文

        Returns:
            str: NTOS版本 (R10P2, R11)
        """
        if context:
            # 方法1：从CLI参数中获取NTOS版本信息
            ntos_version = context.get_data("ntos_version") or context.get_data("version")
            if ntos_version:
                # 标准化版本格式
                version_str = str(ntos_version).upper()
                if "R11" in version_str:
                    return "R11"
                elif "R10P2" in version_str:
                    return "R10P2"
                elif "R10" in version_str:
                    return "R10P2"  # R10默认为R10P2

        # 默认返回R11（最新NTOS版本，功能最完整）
        return "R11"

    def _is_version_supported(self, version: str, feature: str) -> bool:
        """
        检查指定NTOS版本是否支持特定功能 - 基于YANG模型验证结果

        Args:
            version: NTOS版本
            feature: 功能名称

        Returns:
            bool: 是否支持该功能
        """
        # NTOS版本功能兼容性映射表 - 基于YANG模型分析结果
        version_features = {
            "R10P2": {
                "av": True,           # R10P2支持av字段
                "ips": True,          # R10P2支持ips字段
                "websec": True,       # R10P2支持websec字段
                "content-filter": True, # R10P2支持content-filter字段
                "file-filter": False, # R10P2 YANG模型中不存在file-filter字段
                "nat": True,          # R10P2支持NAT功能（static-snat44、dynamic-snat44、static-dnat44）
            },
            "R11": {
                "av": True,           # R11支持av字段
                "ips": True,          # R11支持ips字段
                "websec": True,       # R11支持websec字段
                "content-filter": True, # R11支持content-filter字段
                "file-filter": True,  # R11 YANG模型中包含file-filter字段
                "nat": True,          # R11支持NAT功能（static-snat44、dynamic-snat44、static-dnat44）
            }
        }

        return version_features.get(version, {}).get(feature, False)

    def _add_security_features(self, policy: Dict, security_policy: Dict, context=None):
        """
        添加安全功能映射 - 基于规范的映射规则，支持版本兼容性检测

        Args:
            policy: 原始策略
            security_policy: 安全策略配置
            context: 数据上下文（用于获取版本信息）
        """
        # 获取NTOS版本信息进行兼容性检测
        ntos_version = self._get_ntos_version(context)

        # 规范：任何av-profile都映射到default-alert
        av_profile = policy.get("av_profile") or policy.get("av-profile")
        if av_profile and self._is_version_supported(ntos_version, "av"):
            security_policy["av"] = "default-alert"
        elif av_profile and not self._is_version_supported(ntos_version, "av"):
            log(f"警告: NTOS版本 {ntos_version} 不支持av功能，跳过av-profile配置", "warning")

        # 规范：任何ips-sensor都映射到default-use-signature-action
        ips_sensor = policy.get("ips_sensor") or policy.get("ips-sensor")
        if ips_sensor and self._is_version_supported(ntos_version, "ips"):
            security_policy["ips"] = "default-use-signature-action"
        elif ips_sensor and not self._is_version_supported(ntos_version, "ips"):
            log(f"警告: NTOS版本 {ntos_version} 不支持ips功能，跳过ips-sensor配置", "warning")

        # Web过滤配置文件映射到websec模板
        webfilter_profile = policy.get("webfilter_profile") or policy.get("webfilter-profile")
        if webfilter_profile and self._is_version_supported(ntos_version, "websec"):
            security_policy["websec"] = "default-websec-template"
        elif webfilter_profile and not self._is_version_supported(ntos_version, "websec"):
            log(f"警告: NTOS版本 {ntos_version} 不支持websec功能，跳过webfilter-profile配置", "warning")

        # SSL/SSH配置文件映射到content-filter模板
        ssl_ssh_profile = policy.get("ssl_ssh_profile") or policy.get("ssl-ssh-profile")
        if ssl_ssh_profile and self._is_version_supported(ntos_version, "content-filter"):
            security_policy["content-filter"] = "default-content-filter-template"
        elif ssl_ssh_profile and not self._is_version_supported(ntos_version, "content-filter"):
            log(f"警告: NTOS版本 {ntos_version} 不支持content-filter功能，跳过ssl-ssh-profile配置", "warning")

        # 文件过滤配置文件映射到file-filter模板
        file_filter_profile = policy.get("file_filter_profile") or policy.get("file-filter-profile")
        if file_filter_profile and self._is_version_supported(ntos_version, "file-filter"):
            security_policy["file-filter"] = "default-file-filter-template"
        elif file_filter_profile and not self._is_version_supported(ntos_version, "file-filter"):
            log(f"警告: NTOS版本 {ntos_version} 不支持file-filter功能，跳过file-filter-profile配置", "warning")

        # 协议选项配置文件也可以映射到content-filter模板
        protocol_options = policy.get("profile_protocol_options") or policy.get("profile-protocol-options")
        if protocol_options and "content-filter" not in security_policy and self._is_version_supported(ntos_version, "content-filter"):
            security_policy["content-filter"] = "default-protocol-options-template"
        elif protocol_options and "content-filter" not in security_policy and not self._is_version_supported(ntos_version, "content-filter"):
            log(f"警告: NTOS版本 {ntos_version} 不支持content-filter功能，跳过profile-protocol-options配置", "warning")

    
    def _map_interface_to_zone(self, interface: str) -> str:
        """
        将接口名映射到安全区域

        Args:
            interface: 接口名

        Returns:
            str: 安全区域名
        """
        # 使用统一的接口映射函数获取映射后的接口名
        from engine.utils.interface_mapper import get_mapped_interface_name, load_interface_mapping
        from engine.utils.file_utils import get_current_interface_mapping_file_path

        # 加载真正的接口映射文件（从任务目录）
        final_mapping_file = get_current_interface_mapping_file_path()
        interface_mapping = load_interface_mapping(final_mapping_file)

        # 获取映射后的接口名
        mapped_interface = get_mapped_interface_name(interface, interface_mapping)

        # 标准化接口名（去除引号，转小写）
        clean_interface = mapped_interface.strip('"').lower()

        # 直接映射
        if clean_interface in self.interface_zone_mapping:
            return self.interface_zone_mapping[clean_interface]

        # 模糊匹配
        for intf_pattern, zone in self.interface_zone_mapping.items():
            if intf_pattern in clean_interface:
                return zone

        # 默认映射：包含wan的为untrust，其他为trust
        if "wan" in clean_interface:
            return "untrust"
        else:
            return "trust"

    def _get_mapped_interface_name(self, original_interface: str) -> str:
        """
        获取映射后的接口名称

        Args:
            original_interface: 原始Fortigate接口名称

        Returns:
            str: 映射后的NTOS接口名称
        """
        from engine.utils.interface_mapper import get_mapped_interface_name, load_interface_mapping
        from engine.utils.file_utils import get_current_interface_mapping_file_path

        # 加载真正的接口映射文件（从任务目录）
        final_mapping_file = get_current_interface_mapping_file_path()
        interface_mapping = load_interface_mapping(final_mapping_file)

        # 获取映射后的接口名
        return get_mapped_interface_name(original_interface, interface_mapping)

    def _get_policy_enabled_status(self, policy: Dict) -> bool:
        """
        正确处理FortiGate策略状态

        Args:
            policy: FortiGate策略配置

        Returns:
            bool: 策略是否启用
        """
        # FortiGate策略状态逻辑：
        # - 缺少status字段 → 默认为enable
        # - set status enable → enable
        # - set status disable → disable
        status = policy.get("status", "enable")  # FortiGate默认为enable
        return status.lower() == "enable"

    def _get_policy_action(self, policy: Dict) -> str:
        """
        正确处理FortiGate策略动作

        Args:
            policy: FortiGate策略配置

        Returns:
            str: NTOS策略动作 (permit/deny)
        """
        # FortiGate策略动作逻辑：
        # - 缺少action字段 → 默认为deny（FortiGate隐式拒绝）
        # - set action accept → permit
        # - set action deny → deny
        action = policy.get("action")
        if action is None:
            return "deny"  # FortiGate默认为deny
        return "permit" if action.lower() == "accept" else "deny"

    def _map_service_name_enhanced(self, service: str) -> str:
        """
        增强的服务名称映射，结合NTOS预定义服务

        Args:
            service: 原始服务名

        Returns:
            str: 映射后的服务名
        """
        if not service:
            return ""

        # 特殊服务映射
        if service.upper() == "ALL":
            return "any"

        try:
            # 1. 优先使用NTOS内置服务映射
            ntos_service = self.service_processor.get_ntos_service_from_vendor("fortigate", service)
            if ntos_service:

                return ntos_service

            # 2. 使用预定义服务映射
            predefined_mapping = self.service_processor.get_predefined_service_mapping(service)
            if predefined_mapping and "ntos_service" in predefined_mapping:
                mapped_name = predefined_mapping["ntos_service"]

                return mapped_name

            # 3. 直接名称匹配（大小写不敏感）
            service_lower = service.lower()
            if hasattr(self.service_processor, 'ntos_builtin_services') and service_lower in self.service_processor.ntos_builtin_services:

                return service_lower

        except Exception as e:
            log(_("policy.service_mapping_error", service=service, error=str(e)), "warning")

        # 4. 返回原始名称（作为自定义服务）

        return service

    def _map_service_name(self, service: str) -> str:
        """
        映射服务名称

        Args:
            service: 原始服务名

        Returns:
            str: 映射后的服务名
        """
        # 特殊服务映射
        if service.upper() == "ALL":
            return "any"

        # 使用服务处理器进行映射
        try:
            # 检查是否为预定义服务，如果是则获取映射关系
            if self.service_processor.is_predefined_service(service):
                mapping = self.service_processor.get_predefined_service_mapping(service)
                if mapping and "ntos_service" in mapping:
                    return mapping["ntos_service"]

            # 检查厂商映射
            ntos_service_name = self.service_processor.get_ntos_service_from_vendor("fortigate", service)
            if ntos_service_name:
                return ntos_service_name

        except Exception as e:
            log(_("policy.service_mapping_error", service=service, error=str(e)), "warning")

        # 如果映射失败，返回原始名称
        return service

    def _generate_nat_rules(self, policy: Dict, classification: Dict, vips: Dict, ippools: Dict) -> List[Dict[str, Any]]:
        """
        生成NAT规则配置

        Args:
            policy: 原始策略配置
            classification: 策略分类结果
            vips: VIP配置字典
            ippools: IP池配置字典

        Returns:
            list: NAT规则列表
        """
        nat_rules = []
        policy_name = policy.get("name", f"policy_{policy.get('policyid', 'unknown')}")

        # 生成DNAT规则
        if classification["needs_dnat"]:
            for vip_name in classification["vip_targets"]:
                if vip_name in vips:
                    dnat_rule = self._generate_dnat_rule(policy, vip_name, vips[vip_name])
                    if dnat_rule:
                        nat_rules.append(dnat_rule)

        # 生成SNAT规则
        if classification["needs_snat"]:
            snat_rule = self._generate_snat_rule(policy, classification, ippools)
            if snat_rule:
                nat_rules.append(snat_rule)

        return nat_rules

    def _generate_dnat_rule(self, policy: Dict, vip_name: str, vip_config: Dict) -> Optional[Dict[str, Any]]:
        """
        生成DNAT规则

        Args:
            policy: 原始策略配置
            vip_name: VIP名称
            vip_config: VIP配置

        Returns:
            dict: DNAT规则配置
        """
        policy_name = policy.get("name", f"policy_{policy.get('policyid', 'unknown')}")

        # 检查VIP配置完整性
        if "extip" not in vip_config or "mappedip" not in vip_config:
            log(_("policy.vip_incomplete", vip_name=vip_name), "warning")
            return None

        dnat_rule = {
            "name": f"{policy_name}_{vip_name}",  # 简化命名：策略名_VIP名
            "rule_en": True,
            "desc": f"DNAT规则 - {policy_name}",  # 添加描述字段
            "static-dnat44": {
                "match": {
                    "dest-network": {"name": vip_name},
                    "time-range": {"value": self._get_time_range_value(policy.get("schedule", "always"))}
                },
                "translate-to": {
                    "ipv4-address": vip_config["mappedip"]
                }
            }
        }

        # 添加服务匹配（只在有具体服务且不为ALL时生成）
        service_list = policy.get("service", [])
        if service_list and service_list[0] and service_list[0].upper() != "ALL":
            service_name = self._map_service_name(service_list[0])
            if service_name and service_name.upper() != "ANY":
                dnat_rule["static-dnat44"]["match"]["service"] = {"name": service_name}

        # 添加端口映射（如果VIP配置了端口）
        if "extport" in vip_config and "mappedport" in vip_config:
            try:
                mapped_port = int(vip_config["mappedport"])
                dnat_rule["static-dnat44"]["translate-to"]["port"] = mapped_port
            except (ValueError, TypeError):
                log(_("policy.invalid_vip_port", vip_name=vip_name, port=vip_config["mappedport"]), "warning")

        return dnat_rule

    def _generate_snat_rule(self, policy: Dict, classification: Dict, ippools: Dict) -> Optional[Dict[str, Any]]:
        """
        生成SNAT规则

        Args:
            policy: 原始策略配置
            classification: 策略分类结果
            ippools: IP池配置字典

        Returns:
            dict: SNAT规则配置
        """
        policy_name = policy.get("name", f"policy_{policy.get('policyid', 'unknown')}")

        # 基本SNAT规则结构 - 添加描述字段
        snat_rule = {
            "name": policy_name,  # 直接使用策略名称，去掉_snat后缀
            "rule_en": True,
            "desc": f"SNAT规则 - {policy_name}"  # 添加描述字段
        }

        # 构建匹配条件
        match_config = {
            "time-range": {"value": self._get_time_range_value(policy.get("schedule", "always"))}
        }

        # 添加源网络匹配（只在有源地址且不为ALL时生成）
        srcaddr_list = policy.get("srcaddr", [])
        if srcaddr_list and srcaddr_list[0] and srcaddr_list[0].upper() != "ALL":
            match_config["source-network"] = {"name": srcaddr_list[0]}

        # 添加服务匹配（只在有具体服务且不为ALL时生成）
        service_list = policy.get("service", [])
        if service_list and service_list[0] and service_list[0].upper() != "ALL":
            service_name = self._map_service_name(service_list[0])
            if service_name and service_name.upper() != "ANY":
                match_config["service"] = {"name": service_name}

        # 根据SNAT类型生成不同配置 - 使用增强的分类结果
        if classification["snat_type"] == "dynamic":
            # 动态SNAT（使用IP池）
            snat_config = classification.get("snat_config", {})
            pool_names = snat_config.get("pool_names", [])

            if not pool_names:
                log(_("policy.missing_ippool", policy_name=policy_name), "warning")
                return None

            # 使用第一个IP池
            pool_name = pool_names[0]
            if pool_name not in ippools and pool_name != "default_pool":
                log(_("policy.ippool_not_found", pool_name=pool_name), "warning")
                return None

            snat_rule["dynamic-snat44"] = {
                "match": match_config,
                "translate-to": {
                    "pool-name": pool_name
                }
            }
        else:
            # 静态SNAT
            snat_rule["static-snat44"] = {
                "match": match_config,
                "translate-to": {}
            }

            # 处理端口保持设置 - 修正映射逻辑以符合NTOS规范
            # NTOS规范：port-preserve enable → try-no-pat=true（优先使用原端口），disable → try-no-pat=false（转换源端口）
            port_preserve = classification.get("port_preserve", "disable")
            if port_preserve == "enable":
                snat_rule["static-snat44"]["translate-to"]["try-no-pat"] = True   # 优先使用原端口
            else:
                snat_rule["static-snat44"]["translate-to"]["try-no-pat"] = False  # 转换源端口

            # 使用出接口地址（output-address）
            snat_rule["static-snat44"]["translate-to"]["output-address"] = {}

        return snat_rule

    def _process_ippools(self, ippools: Dict) -> List[Dict[str, Any]]:
        """
        处理IP池配置，生成NAT池

        Args:
            ippools: IP池配置字典

        Returns:
            list: NAT池配置列表
        """
        nat_pools = []

        for pool_name, pool_config in ippools.items():
            if "startip" in pool_config and "endip" in pool_config:
                nat_pool = {
                    "name": pool_name,
                    "address": {
                        "value": f"{pool_config['startip']}-{pool_config['endip']}"
                    }
                }
                nat_pools.append(nat_pool)
            else:
                log(_("policy.incomplete_ippool", pool_name=pool_name), "warning")

        return nat_pools

    def _check_unsupported_features(self, policy: Dict, policy_id: str) -> List[str]:
        """
        检查策略中的不支持特性

        Args:
            policy: 策略配置
            policy_id: 策略ID

        Returns:
            list: 警告信息列表
        """
        warnings = []

        # 检查不支持的字段 - 精确的告警逻辑
        for feature in self.unsupported_features:
            if feature in policy and policy[feature]:
                if feature == "fixedport":
                    # 规范：只在fixedport enable时告警，disable时忽略
                    if policy[feature] == "enable":
                        warning_msg = _("policy.unsupported_fixedport", policy_id=policy_id)
                        warnings.append(warning_msg)
                        log(warning_msg, "warning")
                    # disable时不产生告警，直接忽略
                elif feature == "users":
                    # users字段存在时告警
                    users_list = policy[feature] if isinstance(policy[feature], list) else [policy[feature]]
                    warning_msg = _("policy.unsupported_users_auth",
                                  policy_id=policy_id,
                                  users=", ".join(users_list))
                    warnings.append(warning_msg)
                    log(warning_msg, "warning")
                elif feature == "groups":
                    # groups字段存在时告警
                    groups_list = policy[feature] if isinstance(policy[feature], list) else [policy[feature]]
                    warning_msg = _("policy.unsupported_groups_auth",
                                  policy_id=policy_id,
                                  groups=", ".join(groups_list))
                    warnings.append(warning_msg)
                    log(warning_msg, "warning")

        return warnings

    def _get_time_range_value(self, schedule_value: str) -> str:
        """
        获取正确的时间范围值

        Args:
            schedule_value: FortiGate中的schedule值

        Returns:
            str: 正确的时间对象名称
        """
        if not schedule_value or schedule_value.strip() == "":
            return "always"

        # 移除可能存在的引号
        if schedule_value.startswith('"') and schedule_value.endswith('"'):
            schedule_value = schedule_value[1:-1]

        # 处理特殊值 - 注意："any"可能是一个有效的时间对象名称
        # 只转换"all"，保留"any"（因为系统中可能存在名为"any"的时间对象）
        if schedule_value.lower() == "all":
            return "always"

        # 返回原始值（应该是有效的时间对象名称）
        return schedule_value

    def _empty_result(self) -> Dict[str, Any]:
        """返回空的处理结果"""
        return {
            "security_policies": [],
            "nat_rules": [],
            "nat_pools": [],
            "warnings": [],
            "statistics": {
                "total_policies": 0,
                "processed_policies": 0,
                "security_policies_created": 0,
                "nat_rules_created": 0,
                "warnings_count": 0
            }
        }

    def _add_interface_fields_only(self, security_policy: Dict, srcintf_list: List, dstintf_list: List, policy_name: str):
        """
        纯接口模式：只添加接口字段，不添加区域字段

        Args:
            security_policy: 安全策略字典
            srcintf_list: 源接口列表
            dstintf_list: 目标接口列表
            policy_name: 策略名称（用于日志）
        """
        # 映射源接口
        if srcintf_list:
            source_interfaces = []
            for intf in srcintf_list:
                if intf:  # 确保接口名称不为空
                    mapped_intf = self._get_mapped_interface_name(intf)
                    if mapped_intf:  # 确保映射后的接口名称不为空
                        source_interfaces.append({"name": mapped_intf})

            if source_interfaces:  # 只在有有效接口时添加
                security_policy["source-interface"] = source_interfaces

        # 映射目标接口
        if dstintf_list and dstintf_list[0]:  # 确保接口名称不为空
            # NTOS只支持一个目标接口，取第一个，但按照YANG模型要求使用list结构
            mapped_dst_intf = self._get_mapped_interface_name(dstintf_list[0])
            if mapped_dst_intf:  # 确保映射后的接口名称不为空
                security_policy["dest-interface"] = [{"name": mapped_dst_intf}]  # 使用list结构


    def _add_zone_fields_only(self, security_policy: Dict, srcintf_list: List, dstintf_list: List, policy_name: str, context=None):
        """
        纯区域模式：只添加区域字段，不添加接口字段
        智能处理：区域名称直接使用，接口名称进行映射

        Args:
            security_policy: 安全策略字典
            srcintf_list: 源接口列表
            dstintf_list: 目标接口列表
            policy_name: 策略名称（用于日志）
            context: 数据上下文（用于区域识别）
        """
        # 处理源区域
        if srcintf_list and srcintf_list[0]:  # 确保名称不为空
            src_name = srcintf_list[0]

            # 检查是否为已定义的区域名称
            if self._is_zone_name(src_name, context):
                # 直接使用区域名称
                security_policy["source-zone"] = [{"name": src_name}]
                log(f"策略 {policy_name} 源使用区域名称: {src_name}", "info")
            else:
                # 接口名称，需要映射到区域
                src_zone = self._map_interface_to_zone(src_name)
                if src_zone:
                    security_policy["source-zone"] = [{"name": src_zone}]
                    log(f"策略 {policy_name} 源接口 {src_name} 映射到区域: {src_zone}", "info")

        # 处理目标区域
        if dstintf_list and dstintf_list[0]:  # 确保名称不为空
            dst_name = dstintf_list[0]

            # 检查是否为已定义的区域名称
            if self._is_zone_name(dst_name, context):
                # 直接使用区域名称
                security_policy["dest-zone"] = [{"name": dst_name}]
                log(f"策略 {policy_name} 目标使用区域名称: {dst_name}", "info")
            else:
                # 接口名称，需要映射到区域
                dst_zone = self._map_interface_to_zone(dst_name)
                if dst_zone:
                    security_policy["dest-zone"] = [{"name": dst_zone}]
                    log(f"策略 {policy_name} 目标接口 {dst_name} 映射到区域: {dst_zone}", "info")

    def _add_both_interface_and_zone_fields(self, security_policy: Dict, srcintf_list: List, dstintf_list: List, policy_name: str, context=None):
        """
        兼容模式：同时添加接口和区域字段（原有行为）

        Args:
            security_policy: 安全策略字典
            srcintf_list: 源接口列表
            dstintf_list: 目标接口列表
            policy_name: 策略名称（用于日志）
            context: 数据上下文（用于区域识别）
        """
        # 先添加区域字段
        self._add_zone_fields_only(security_policy, srcintf_list, dstintf_list, policy_name, context)

        # 再添加接口字段
        self._add_interface_fields_only(security_policy, srcintf_list, dstintf_list, policy_name)



    def _add_interface_fields_with_mapping(self, security_policy: Dict, srcintf_list: List, dstintf_list: List,
                                          interface_mapping: Dict, policy_name: str, context=None):
        """
        纯接口模式：只添加接口字段，不添加区域字段（使用传入的接口映射）
        支持区域名称识别和分离

        Args:
            security_policy: 安全策略字典
            srcintf_list: 源接口列表
            dstintf_list: 目标接口列表
            interface_mapping: 接口映射字典
            policy_name: 策略名称（用于日志）
        """
        # 处理源接口 - 需要区分区域和接口
        if srcintf_list:
            # 分离区域名称和接口名称
            zone_names, interface_names = self._separate_zones_and_interfaces(srcintf_list, context)

            source_interfaces = []

            # 处理接口名称
            for intf in interface_names:
                mapped_intf = self._get_mapped_interface_name_with_mapping(intf, interface_mapping)
                # 只有非空的映射接口才添加到列表中
                if mapped_intf:
                    source_interfaces.append({"name": mapped_intf})


            # 处理区域名称 - 在接口模式下，区域名称应该被忽略或转换为接口
            for zone in zone_names:
                log(f"警告: 在接口模式下忽略区域名称 '{zone}'，策略: {policy_name}", "warning")

            if source_interfaces:
                security_policy["source-interface"] = source_interfaces

        # 处理目标接口 - 需要区分区域和接口
        if dstintf_list:
            # 分离区域名称和接口名称
            zone_names, interface_names = self._separate_zones_and_interfaces(dstintf_list, context)

            # 处理接口名称（只取第一个）
            if interface_names:
                mapped_dest_intf = self._get_mapped_interface_name_with_mapping(interface_names[0], interface_mapping)
                # 只有非空的映射接口才添加到安全策略中
                if mapped_dest_intf:
                    security_policy["dest-interface"] = [{"name": mapped_dest_intf}]  # 使用list结构符合YANG模型


            # 处理区域名称 - 在接口模式下，区域名称应该被忽略或转换为接口
            for zone in zone_names:
                log(f"警告: 在接口模式下忽略区域名称 '{zone}'，策略: {policy_name}", "warning")

    def _add_zone_fields_with_mapping(self, security_policy: Dict, srcintf_list: List, dstintf_list: List,
                                     interface_mapping: Dict, policy_name: str, context=None):
        """
        纯区域模式：只添加区域字段，不添加接口字段（使用传入的接口映射）
        支持区域名称识别和分离

        Args:
            security_policy: 安全策略字典
            srcintf_list: 源接口列表
            dstintf_list: 目标接口列表
            interface_mapping: 接口映射字典
            policy_name: 策略名称（用于日志）
        """
        # 处理源区域 - 需要区分区域和接口
        if srcintf_list:
            # 分离区域名称和接口名称
            zone_names, interface_names = self._separate_zones_and_interfaces(srcintf_list, context)

            source_zones = []

            # 处理区域名称 - 直接使用
            for zone in zone_names:
                source_zones.append({"name": zone})

            # 处理接口名称 - 转换为区域
            for intf in interface_names:
                zone = self._map_interface_to_zone_with_mapping(intf, interface_mapping)
                source_zones.append({"name": zone})


            if source_zones:
                security_policy["source-zone"] = source_zones

        # 处理目标区域 - 需要区分区域和接口
        if dstintf_list:
            # 分离区域名称和接口名称
            zone_names, interface_names = self._separate_zones_and_interfaces(dstintf_list, context)

            dest_zones = []

            # 处理区域名称 - 直接使用
            for zone in zone_names:
                dest_zones.append({"name": zone})

            # 处理接口名称 - 转换为区域
            for intf in interface_names:
                zone = self._map_interface_to_zone_with_mapping(intf, interface_mapping)
                dest_zones.append({"name": zone})


            if dest_zones:
                security_policy["dest-zone"] = dest_zones

    def _generate_intelligent_policy_name(self, policy: dict, policy_id: str) -> str:
        """
        智能生成策略名称

        优先级策略：
        1. 保留FortiGate原始名称（如果存在set name）
        2. 基于注释智能生成语义化名称
        3. 基于接口和地址生成描述性名称
        4. 使用改进的默认格式

        Args:
            policy: FortiGate策略配置
            policy_id: 策略ID

        Returns:
            str: 生成的策略名称
        """
        # 调试：记录策略157的详细信息
        if policy_id == "157":
            log(f"🔍 策略{policy_id}智能命名开始", "info")
            log(f"  - 策略数据keys: {list(policy.keys())}", "info")
            log(f"  - name字段: {policy.get('name', 'None')}", "info")
            log(f"  - comments字段: {policy.get('comments', 'None')}", "info")
        # 优先级1：保留原始名称
        if "name" in policy and policy["name"]:
            original_name = policy["name"]
            # 确保名称符合NTOS YANG模型规范
            sanitized_name = self._sanitize_policy_name(original_name)
            if policy_id == "157":
                log(f"🔍 策略{policy_id}使用原始名称: {original_name} -> {sanitized_name}", "info")
            return sanitized_name

        # 优先级2：基于注释生成语义化名称
        if "comments" in policy and policy["comments"]:
            semantic_name = self._generate_name_from_comments(policy["comments"], policy_id)
            if semantic_name:
                if policy_id == "157":
                    log(f"🔍 策略{policy_id}使用注释生成名称: {policy['comments']} -> {semantic_name}", "info")
                return semantic_name
            elif policy_id == "157":
                log(f"🔍 策略{policy_id}注释解析失败: {policy['comments']}", "info")

        # 优先级3：基于接口和地址生成描述性名称
        descriptive_name = self._generate_descriptive_name(policy, policy_id)
        if descriptive_name:
            return descriptive_name

        # 优先级4：改进的默认格式
        default_name = f"policy_{policy_id}"
        if policy_id == "157":
            log(f"🔍 策略{policy_id}使用默认名称: {default_name}", "info")
        return default_name

    def _sanitize_policy_name(self, name: str) -> str:
        """
        清理策略名称，确保符合NTOS YANG模型规范

        NTOS不允许的字符：`~!#$%^&*+|{};:"',\\/<>?

        Args:
            name: 原始名称

        Returns:
            str: 清理后的名称
        """
        import re

        # 移除或替换不允许的字符
        # 将常见的分隔符替换为下划线
        name = re.sub(r'[/\\|]', '_', name)
        # 移除其他不允许的字符
        name = re.sub(r'[`~!#$%^&*+{};:"\'<>?]', '', name)
        # 清理多余的下划线和空格
        name = re.sub(r'[_\s]+', '_', name)
        # 移除首尾的下划线
        name = name.strip('_')

        # 确保名称不为空
        if not name:
            return "unnamed_policy"

        # 限制长度（建议不超过64个字符）
        if len(name) > 64:
            name = name[:64].rstrip('_')

        return name

    def _generate_name_from_comments(self, comments: str, policy_id: str) -> str:
        """
        基于注释生成语义化的策略名称

        解析常见的注释模式：
        - "(Copy of XXX)" -> "XXX_Copy"
        - "(Reverse of XXX)" -> "XXX_Reverse"
        - "XXX to YYY" -> "XXX_to_YYY"
        - "Allow XXX" -> "Allow_XXX"

        Args:
            comments: 策略注释
            policy_id: 策略ID

        Returns:
            str: 生成的名称，如果无法解析则返回None
        """
        import re

        if not comments:
            return None

        # 清理注释，移除多余的括号和空格
        clean_comments = comments.strip()

        # 模式1: (Copy of XXX) 或 (Reverse of XXX)
        copy_pattern = r'\(Copy of ([^)]+)\)'
        reverse_pattern = r'\(Reverse of ([^)]+)\)'

        copy_match = re.search(copy_pattern, clean_comments, re.IGNORECASE)
        reverse_match = re.search(reverse_pattern, clean_comments, re.IGNORECASE)

        if reverse_match:
            base_name = reverse_match.group(1).strip()
            generated_name = f"{base_name}_Reverse"
            return self._sanitize_policy_name(generated_name)
        elif copy_match:
            base_name = copy_match.group(1).strip()
            generated_name = f"{base_name}_Copy"
            return self._sanitize_policy_name(generated_name)

        # 模式2: XXX to YYY
        to_pattern = r'(\w+)\s+to\s+(\w+)'
        to_match = re.search(to_pattern, clean_comments, re.IGNORECASE)
        if to_match:
            source = to_match.group(1)
            dest = to_match.group(2)
            generated_name = f"{source}_to_{dest}"
            return self._sanitize_policy_name(generated_name)

        # 模式3: Allow XXX 或 Deny XXX
        action_pattern = r'(Allow|Deny|Block|Permit)\s+(\w+)'
        action_match = re.search(action_pattern, clean_comments, re.IGNORECASE)
        if action_match:
            action = action_match.group(1)
            target = action_match.group(2)
            generated_name = f"{action}_{target}"
            return self._sanitize_policy_name(generated_name)

        # 模式4: 提取第一个有意义的词作为基础
        words = re.findall(r'\b[A-Za-z_][A-Za-z0-9_]{2,}\b', clean_comments)
        if words:
            # 过滤掉常见的无意义词汇
            meaningful_words = [w for w in words if w.lower() not in
                              ['the', 'and', 'for', 'with', 'from', 'policy', 'rule']]
            if meaningful_words:
                base_word = meaningful_words[0]
                generated_name = f"{base_word}_Policy"
                return self._sanitize_policy_name(generated_name)

        return None

    def _generate_descriptive_name(self, policy: dict, policy_id: str) -> str:
        """
        基于接口和地址生成描述性策略名称

        格式：{源接口/区域}_{目标接口/区域}_{动作}_{服务}
        例如：LAN_to_DMZ_PERMIT_HTTP

        Args:
            policy: FortiGate策略配置
            policy_id: 策略ID

        Returns:
            str: 生成的描述性名称，如果无法生成则返回None
        """
        name_parts = []

        # 获取源接口/区域
        srcintf = policy.get("srcintf", [])
        if srcintf:
            # 取第一个接口作为代表
            src_name = srcintf[0] if isinstance(srcintf, list) else srcintf
            # 简化接口名称
            src_name = self._simplify_interface_name(src_name)
            name_parts.append(src_name)

        # 添加连接词
        if name_parts:
            name_parts.append("to")

        # 获取目标接口/区域
        dstintf = policy.get("dstintf", [])
        if dstintf:
            # 取第一个接口作为代表
            dst_name = dstintf[0] if isinstance(dstintf, list) else dstintf
            # 简化接口名称
            dst_name = self._simplify_interface_name(dst_name)
            name_parts.append(dst_name)

        # 获取动作
        action = policy.get("action", "permit").upper()
        if action in ["ACCEPT", "ALLOW"]:
            action = "PERMIT"
        elif action in ["DENY", "DROP"]:
            action = "DENY"
        name_parts.append(action)

        # 获取服务（可选）
        service = policy.get("service", [])
        if service and len(service) == 1:  # 只有一个服务时才添加到名称中
            service_name = service[0] if isinstance(service, list) else service
            # 简化服务名称
            service_name = self._simplify_service_name(service_name)
            if service_name and service_name != "ALL":
                name_parts.append(service_name)

        # 如果没有足够的信息，返回None
        if len(name_parts) < 3:  # 至少需要源、目标、动作
            return None

        # 组合名称
        generated_name = "_".join(name_parts)
        return self._sanitize_policy_name(generated_name)

    def _simplify_interface_name(self, interface_name: str) -> str:
        """
        简化接口名称，提取关键部分

        Args:
            interface_name: 原始接口名称

        Returns:
            str: 简化后的接口名称
        """
        if not interface_name:
            return "ANY"

        # 常见的接口名称映射
        name_mappings = {
            "any": "ANY",
            "wan": "WAN",
            "wan1": "WAN",
            "wan2": "WAN2",
            "lan": "LAN",
            "dmz": "DMZ",
            "internal": "LAN",
            "external": "WAN"
        }

        # 转换为小写进行匹配
        lower_name = interface_name.lower()

        # 直接映射
        if lower_name in name_mappings:
            return name_mappings[lower_name]

        # 提取端口号（如port1, port2等）
        import re
        port_match = re.match(r'port(\d+)', lower_name)
        if port_match:
            return f"PORT{port_match.group(1)}"

        # 其他情况，返回原名称的大写版本（限制长度）
        simplified = interface_name.upper()[:8]
        return simplified

    def _simplify_service_name(self, service_name: str) -> str:
        """
        简化服务名称，提取关键部分

        Args:
            service_name: 原始服务名称

        Returns:
            str: 简化后的服务名称
        """
        if not service_name:
            return "ANY"

        # 常见的服务名称映射
        service_mappings = {
            "all": "ALL",
            "any": "ANY",
            "http": "HTTP",
            "https": "HTTPS",
            "ftp": "FTP",
            "ssh": "SSH",
            "telnet": "TELNET",
            "smtp": "SMTP",
            "pop3": "POP3",
            "imap": "IMAP",
            "dns": "DNS",
            "dhcp": "DHCP",
            "snmp": "SNMP",
            "ntp": "NTP"
        }

        # 转换为小写进行匹配
        lower_name = service_name.lower()

        # 直接映射
        if lower_name in service_mappings:
            return service_mappings[lower_name]

        # 提取端口号（如TCP_80, UDP_53等）
        import re
        port_match = re.match(r'(tcp|udp)[_-]?(\d+)', lower_name)
        if port_match:
            protocol = port_match.group(1).upper()
            port = port_match.group(2)
            return f"{protocol}_{port}"

        # 其他情况，返回原名称的大写版本（限制长度）
        simplified = service_name.upper()[:10]
        return simplified
