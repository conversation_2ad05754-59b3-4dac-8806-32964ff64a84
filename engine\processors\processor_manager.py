#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
处理器管理器模块

负责管理各种配置处理器，并协调它们的调用顺序
"""

from engine.utils.logger import log
from engine.utils.i18n import _
from engine.processors.service_group_processor import service_group_processor

class ProcessorManager:
    """处理器管理器类，负责管理和调用各种处理器"""
    
    def __init__(self):
        """初始化处理器管理器"""
        self.processors = {}
        self._register_default_processors()
    
    def _register_default_processors(self):
        """注册默认的处理器"""
        # 注册服务组处理器
        self.register_processor("service_groups", service_group_processor)
        
        # 可以在这里注册其他处理器
        # self.register_processor("address_objects", address_processor)
        # self.register_processor("policy_rules", policy_processor)
        # 等等...
    
    def register_processor(self, config_type, processor):
        """
        注册处理器
        
        Args:
            config_type (str): 配置类型，如'service_groups', 'address_objects'等
            processor (object): 处理器实例
        """
        self.processors[config_type] = processor
        log(_("info.registered_processor", type=config_type, name=processor.name))
    
    def get_processor(self, config_type):
        """
        获取处理器
        
        Args:
            config_type (str): 配置类型
            
        Returns:
            object: 处理器实例，如果没有找到则返回None
        """
        return self.processors.get(config_type)
    
    def process_all(self, root, config_data):
        """
        处理所有配置
        
        Args:
            root (Element): XML根元素
            config_data (dict): 配置数据
            
        Returns:
            dict: 各处理器的处理结果统计
        """
        stats = {}
        
        # 处理服务组
        if "service_groups" in config_data and "service_groups" in self.processors:
            processor = self.processors["service_groups"]
            log(_("info.calling_processor", type="service_groups", name=processor.name))
            stats["service_groups"] = processor.process(root, config_data["service_groups"])
        
        # 这里可以添加对其他配置类型的处理
        # if "address_objects" in config_data and "address_objects" in self.processors:
        #     processor = self.processors["address_objects"]
        #     log(_("info.calling_processor", type="address_objects", name=processor.name))
        #     stats["address_objects"] = processor.process(root, config_data["address_objects"])
        
        return stats 