#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
时间对象转换模块，将 Fortigate 时间对象转换为 NTOS 格式
"""

import re
import datetime
from engine.utils.logger import log
from engine.utils.i18n import _

def convert_fortigate_schedule(schedule_objects):
    """
    将 Fortigate 时间对象转换为 NTOS 格式
    
    Args:
        schedule_objects (list): Fortigate 时间对象列表
    
    Returns:
        list: NTOS 格式的时间对象列表
    """
    result = []
    
    log("info.schedule_conversion_start", "info", total=len(schedule_objects))
    
    for schedule in schedule_objects:
        try:
            # 记录更详细的时间对象信息
            schedule_name = schedule.get("name", "unnamed")
            schedule_keys = list(schedule.keys())

                
            # 首先检查时间对象的有效性
            if not is_valid_schedule(schedule):
                log("warning.invalid_schedule_skipped", "warning", name=schedule_name)
                continue
                
            converted = convert_single_schedule(schedule)
            if converted:
                if isinstance(converted, list):
                    # 处理跨天情况，可能返回多个对象
                    result.extend(converted)
                    log("info.cross_day_schedule_split", "info", name=schedule_name, count=len(converted))
                else:
                    result.append(converted)
                    log("info.schedule_converted_successfully", "info", name=schedule_name)
            else:
                log("warning.schedule_conversion_failed", "warning", name=schedule_name)
                
        except Exception as e:
            log("error.schedule_conversion_error", "error", 
                name=schedule.get("name", "unknown"), error=str(e))
    
    log("info.schedule_conversion_complete", "info", 
        total=len(schedule_objects), 
        success=len(result), 
        skipped=len(schedule_objects)-len(result))
    
    return result

def is_valid_schedule(schedule):
    """
    检查时间对象是否有效（包含必要的时间字段）
    
    Args:
        schedule (dict): Fortigate 时间对象
    
    Returns:
        bool: 时间对象是否有效
    """
    # 检查基本字段
    if not isinstance(schedule, dict) or "name" not in schedule:
        return False
    
    # 添加更详细的调试日志
    schedule_name = schedule.get("name", "unnamed")
    schedule_keys = list(schedule.keys())
    

    
    # 检查解析器标记的极简对象
    if "is_minimal_object" in schedule and schedule["is_minimal_object"]:
        log("warning.schedule_only_uuid_skipped", "warning", name=schedule_name)
        return False
    
    # 检查是否只包含UUID（只有name和uuid字段，没有其他内容）
    # 特别注意检查"none"对象，它可能只包含UUID
    only_uuid_fields = (len(schedule.keys()) <= 2 and 
                      "name" in schedule and 
                      "uuid" in schedule)
    if only_uuid_fields:
        log("warning.schedule_only_uuid_direct_skipped", "warning", name=schedule_name)
        return False
    
    # 定义实际时间字段及其对应的验证函数
    time_fields = {
        "time_start": lambda v: bool(v and isinstance(v, str)),
        "time_end": lambda v: bool(v and isinstance(v, str)),
        "datetime_start": lambda v: bool(v and isinstance(v, str)),
        "datetime_end": lambda v: bool(v and isinstance(v, str)),
        "weekdays": lambda v: bool(v and isinstance(v, list) and len(v) > 0),
        "day": lambda v: bool(v),  # 在有些配置中使用day而不是weekdays
    }
    
    # 检查是否有任何有实际内容的时间字段
    has_content = any(
        validator(schedule.get(field)) 
        for field, validator in time_fields.items()
        if field in schedule
    )
    
    # 如果没有有效内容且有UUID，判定为仅UUID对象
    if not has_content and "uuid" in schedule:
        log("warning.schedule_only_uuid_content_skipped", "warning", name=schedule_name)
        return False
    
    # 检查是否包含任何有效的时间字段配置
    has_time_fields = False
    
    # 检查一次性时间计划字段
    if "type" in schedule and schedule["type"] == "onetime":
        has_time_fields = (
            ("datetime_start" in schedule and schedule["datetime_start"]) or 
            ("datetime_end" in schedule and schedule["datetime_end"])
        )
    
    # 检查周期性时间计划字段
    elif "type" in schedule and schedule["type"] == "periodic":
        has_weekdays = "weekdays" in schedule and schedule["weekdays"] and len(schedule["weekdays"]) > 0
        has_times = (
            ("time_start" in schedule and schedule["time_start"]) or 
            ("time_end" in schedule and schedule["time_end"])
        )
        has_time_fields = has_weekdays or has_times
    
    # 没有type字段但有day字段的情况（递归时间对象）
    elif "day" in schedule and schedule["day"]:
        has_time_fields = True
    
    # 没有type字段但有start/end字段的情况
    elif ("start" in schedule and schedule["start"]) or ("end" in schedule and schedule["end"]):
        has_time_fields = True
    
    # 记录检测结果
    if not has_time_fields:

    
    return has_time_fields

def convert_single_schedule(schedule):
    """
    转换单个 Fortigate 时间对象
    
    Args:
        schedule (dict): Fortigate 时间对象
    
    Returns:
        dict or list: 转换后的 NTOS 时间对象，或者包含多个对象的列表（跨天情况）
    """
    name = schedule.get("name", "")
    
    # 根据内容检测是否为 "always" 类型（一周7天、每天00:00:00到23:59:59）
    if is_always_schedule(schedule):
        log("info.detected_always_schedule", "info", name=name)
        return create_always_schedule(name)
    
    # 根据内容检测是否为 "none" 类型（没有有效时间段）
    if is_none_schedule(schedule):
        log("info.detected_none_schedule", "info", name=name)
        return create_none_schedule(name)
    
    # 根据类型处理普通时间对象
    if schedule["type"] == "onetime":
        log("info.processing_onetime_schedule", "info", name=name)
        return create_onetime_schedule(schedule)
    else:  # periodic
        log("info.processing_periodic_schedule", "info", name=name)
        return create_periodic_schedule(schedule)

def is_always_schedule(schedule):
    """
    检查时间对象是否为 "always" 类型（一周7天、每天00:00:00到23:59:59）
    
    Args:
        schedule (dict): Fortigate 时间对象
    
    Returns:
        bool: 是否为 "always" 类型
    """
    # 必须是周期性类型
    if schedule.get("type") != "periodic":
        return False
    
    # 检查是否包含所有星期几
    weekdays = schedule.get("weekdays", [])
    all_days = {"sun", "mon", "tue", "wed", "thu", "fri", "sat"}
    if set(weekdays) != all_days:
        return False
    
    # 检查时间范围是否为全天
    start_time = schedule.get("time_start", "")
    end_time = schedule.get("time_end", "")
    
    # 允许一些常见的全天时间表示
    full_day_starts = {"00:00:00", "00:00", "0:00", "0:0"}
    full_day_ends = {"23:59:59", "23:59", "23:59:00"}
    
    return start_time in full_day_starts and end_time in full_day_ends

def is_none_schedule(schedule):
    """
    检查时间对象是否为 "none" 类型（无有效时间段）
    
    Args:
        schedule (dict): Fortigate 时间对象
    
    Returns:
        bool: 是否为 "none" 类型
    """
    # 检查解析器标记的极简对象
    if "is_minimal_object" in schedule and schedule["is_minimal_object"]:
        log("info.none_schedule_detected_by_content", "info", 
            name=schedule.get("name", "unnamed"),
            keys=str(list(schedule.keys())))
        return True
    
    # 检查是否有显式的禁用标记
    if "status" in schedule and schedule["status"].lower() == "disable":
        return True
    
    # 增强检测：检查是否只有基本字段和UUID
    if (len(schedule.keys()) <= 3 and 
        "name" in schedule and 
        "uuid" in schedule and
        not any(k for k in schedule.keys() 
                if k not in ["name", "uuid", "type"])):
        log("info.none_schedule_detected_by_content", "info", 
            name=schedule.get("name", "unnamed"),
            keys=str(list(schedule.keys())))
        return True
    
    # 检查是否设置了无效的时间范围
    if schedule.get("type") == "periodic":
        start_time = schedule.get("time_start", "")
        end_time = schedule.get("time_end", "")
        
        # 时间范围为0
        if start_time == end_time:
            return True
        
        # 没有选择任何星期几
        weekdays = schedule.get("weekdays", [])
        if not weekdays:
            return True
    
    return False

def create_always_schedule(name):
    """创建 "always" 类型的时间对象（全天每天）"""
    weekdays = ["sun", "mon", "tue", "wed", "thu", "fri", "sat"]
    return {
        "name": name,
        "period": {
            "start": "00:00:00",
            "end": "23:59:59",
            "weekday": [{"key": day} for day in weekdays]
        }
    }

def create_none_schedule(name):
    """创建 "none" 类型的时间对象（禁用）"""
    return {
        "name": name,
        "disabled": True,
        "period": {
            "start": "00:00:00",
            "end": "00:00:00",
            "weekday": []
        }
    }

def create_onetime_schedule(schedule):
    """创建一次性时间对象"""
    result = {"name": schedule["name"]}
    
    # 解析起始时间
    start_parts = schedule.get("datetime_start", "").split()
    if len(start_parts) >= 2:
        start_date = start_parts[0]
        start_time = start_parts[1]
        if len(start_time.split(":")) == 2:
            start_time += ":00"  # 添加秒
    else:
        # 默认值
        start_date = datetime.date.today().strftime("%Y-%m-%d")
        start_time = "00:00:00"
        log("warning.missing_start_datetime", "warning", name=schedule["name"])
    
    # 解析结束时间
    end_parts = schedule.get("datetime_end", "").split()
    if len(end_parts) >= 2:
        end_date = end_parts[0]
        end_time = end_parts[1]
        if len(end_time.split(":")) == 2:
            end_time += ":00"  # 添加秒（修复：使用:00而不是:59）
    else:
        # 默认值
        end_date = start_date
        end_time = "23:59:59"
        log("warning.missing_end_datetime", "warning", name=schedule["name"])
    
    # 转换日期格式：YYYY/MM/DD -> YYYY-MM-DD
    start_date_formatted = start_date.replace("/", "-")
    end_date_formatted = end_date.replace("/", "-")

    result["once"] = {
        "start": f"{start_time}/{start_date_formatted}",
        "end": f"{end_time}/{end_date_formatted}"
    }
    
    return result

def create_periodic_schedule(schedule):
    """创建周期性时间对象，处理跨天情况"""
    name = schedule["name"]
    start_time = schedule.get("time_start", "00:00:00")
    end_time = schedule.get("time_end", "23:59:59")
    weekdays = schedule.get("weekdays", [])
    
    # 检查是否跨天（结束时间小于开始时间）
    is_cross_day = is_time_cross_day(start_time, end_time)
    
    if is_cross_day:
        log("info.detected_cross_day_schedule", "info", name=name,
            start=start_time, end=end_time)
        return create_cross_day_schedule(name, start_time, end_time, weekdays)
    else:
        return create_normal_periodic_schedule(name, start_time, end_time, weekdays)

def is_time_cross_day(start_time, end_time):
    """检查时间范围是否跨天"""
    # 解析时间格式为小时和分钟
    start_parts = start_time.split(":")
    end_parts = end_time.split(":")
    
    if len(start_parts) >= 2 and len(end_parts) >= 2:
        start_hour, start_min = int(start_parts[0]), int(start_parts[1])
        end_hour, end_min = int(end_parts[0]), int(end_parts[1])
        
        # 如果结束时间小于开始时间，则跨天
        if end_hour < start_hour or (end_hour == start_hour and end_min < start_min):
            return True
    
    return False

def create_normal_periodic_schedule(name, start_time, end_time, weekdays):
    """创建普通周期性时间对象（不跨天）"""
    return {
        "name": name,
        "period": {
            "start": start_time,
            "end": end_time,
            "weekday": [{"key": day} for day in weekdays]
        }
    }

def create_cross_day_schedule(name, start_time, end_time, weekdays):
    """创建跨天周期性时间对象，拆分为两个周期"""
    # 第一部分：从开始时间到午夜
    schedule1 = {
        "name": f"{name}-part1",
        "period": {
            "start": start_time,
            "end": "23:59:59",
            "weekday": [{"key": day} for day in weekdays]
        }
    }
    
    # 第二部分：从午夜到结束时间
    schedule2 = {
        "name": f"{name}-part2",
        "period": {
            "start": "00:00:00",
            "end": end_time,
            "weekday": [{"key": get_next_day(day)} for day in weekdays]
        }
    }
    
    return [schedule1, schedule2]

def get_next_day(day):
    """获取下一天"""
    days = ["sun", "mon", "tue", "wed", "thu", "fri", "sat"]
    try:
        idx = days.index(day)
        return days[(idx + 1) % 7]
    except ValueError:
        return day  # 如果找不到，返回原始值 