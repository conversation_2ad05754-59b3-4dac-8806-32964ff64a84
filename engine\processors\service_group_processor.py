#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
服务组处理器模块

处理飞塔(FortiGate)格式的服务组配置，转换为NTOS YANG模型格式
"""

from lxml import etree
from engine.utils.logger import log
from engine.utils.i18n import _
import re
import html

class ServiceGroupProcessor:
    """飞塔服务组处理器类"""
    
    def __init__(self):
        """初始化处理器"""
        self.name = "service_group_processor"
        self.description = _("service_group.processor_description")
        self.yang_namespace = "urn:ruijie:ntos:params:xml:ns:yang:service-obj"
    
    def preprocess_service_members(self, members, service_objects):
        """
        预处理服务组成员，确保它们与服务对象名称格式一致
        
        Args:
            members (list): 服务组成员列表
            service_objects (list): 服务对象列表
            
        Returns:
            dict: 处理后的成员映射 {原始名称: 处理后名称}
        """
        member_mapping = {}
        for member in members:
            # 应用与服务组名称相同的处理逻辑
            safe_member_name = member.replace(" ", "-")
            safe_member_name = re.sub(r'[^\w\-]', '', safe_member_name)
            member_mapping[member] = safe_member_name
        
        return member_mapping
    
    def validate_service_member(self, member_name, service_objects, service_mapping_relationships=None):
        """
        验证服务成员是否存在于服务对象列表中，支持预定义服务映射

        Args:
            member_name (str): 成员名称
            service_objects (list): 服务对象列表
            service_mapping_relationships (dict, optional): 服务映射关系字典

        Returns:
            tuple: (是否有效, 最终使用的服务名称)
        """
        # 如果没有提供服务对象列表，假设所有成员都有效
        if not service_objects:
            return True, member_name

        # 首先检查成员是否直接存在于服务对象列表中
        for obj in service_objects:
            obj_name = obj.get("name", "")
            # 对服务对象名称应用相同的处理逻辑
            safe_obj_name = obj_name.replace(" ", "-")
            safe_obj_name = re.sub(r'[^\w\-]', '', safe_obj_name)

            if safe_obj_name == member_name:
                return True, member_name

        # 然后检查是否存在映射关系
        if service_mapping_relationships and member_name in service_mapping_relationships:
            mapped_name = service_mapping_relationships[member_name]
            # 检查映射后的名称是否存在于服务对象中
            for obj in service_objects:
                obj_name = obj.get("name", "")
                safe_obj_name = obj_name.replace(" ", "-")
                safe_obj_name = re.sub(r'[^\w\-]', '', safe_obj_name)

                if safe_obj_name == mapped_name:

                    return True, mapped_name

        return False, member_name
        
    def process(self, root, service_groups, service_objects=None, fail_on_invalid=False, service_mapping_relationships=None):
        """
        处理服务组配置

        Args:
            root (Element): XML根元素
            service_groups (list): 服务组配置列表
            service_objects (list, optional): 服务对象列表，用于验证成员
            fail_on_invalid (bool, optional): 如果为True，在发现无效成员时立即失败
            service_mapping_relationships (dict, optional): 服务映射关系字典

        Returns:
            dict: 处理结果统计信息
        """
        # 导入国际化函数，确保type_key使用正确的翻译
        from engine.utils.i18n import _
        
        stats = {
            "processed": 0,
            "updated": 0,  # 新增：记录更新的服务组数量
            "created": 0,  # 新增：记录创建的服务组数量
            "skipped": 0,
            "invalid_groups": []
        }
        
        if not service_groups:
            log(_("info.no_service_groups_to_process"))
            return stats
            
        log(_("info.processing_service_groups", count=len(service_groups)))

        # 记录服务映射关系信息
        if service_mapping_relationships:
            log(_("info.service_mapping_relationships_found", count=len(service_mapping_relationships)))

        try:
            # 检查传入的root是否已经是service-obj节点
            if root.tag.endswith('service-obj'):
                # 如果传入的是service-obj节点，直接使用作为service_obj
                service_obj = root
                log(_("info.using_provided_service_obj_node"))
            elif root.tag.endswith('vrf'):
                # 如果传入的是VRF节点，查找或创建service-obj节点
                vrf = root
                log(_("info.using_provided_vrf_node"))

                # 在VRF节点中查找service-obj节点
                service_obj_nodes = vrf.xpath("./*[local-name()='service-obj']")
                if service_obj_nodes:
                    service_obj = service_obj_nodes[0]
                    log(_("info.found_service_obj_in_vrf"))
                else:
                    # 创建service-obj节点
                    ns_map = {None: self.yang_namespace}
                    service_obj = etree.SubElement(vrf, "{%s}service-obj" % self.yang_namespace, nsmap=ns_map)
                    log(_("info.created_service_obj_node"))
            else:
                # 查找VRF节点 - 改进查找逻辑，支持多种格式
                vrf = None

                # 尝试使用XPath查找VRF节点
                vrf_nodes = root.xpath("//vrf")
                if vrf_nodes:
                    vrf = vrf_nodes[0]
                    log(_("info.found_vrf_node_by_xpath"))
                else:
                    # 尝试直接查找子节点
                    for child in root.getchildren():
                        if child.tag.endswith('vrf'):
                            vrf = child
                            log(_("info.found_vrf_node_by_tag"))
                            break

                # 如果仍未找到VRF节点，则创建一个
                if vrf is None:
                    vrf = etree.SubElement(root, "vrf")
                    # 添加名称节点
                    name_node = etree.SubElement(vrf, "name")
                    name_node.text = "main"
                    log(_("info.created_vrf_node"))

                # 在VRF节点中查找或创建service-obj节点
                service_obj_nodes = vrf.xpath("./*[local-name()='service-obj']")
                if service_obj_nodes:
                    service_obj = service_obj_nodes[0]
                    log(_("info.found_service_obj_in_vrf"))
                else:
                    # 创建service-obj节点
                    ns_map = {None: self.yang_namespace}
                    service_obj = etree.SubElement(vrf, "{%s}service-obj" % self.yang_namespace, nsmap=ns_map)
                    log(_("info.created_service_obj_node"))

            
            # 获取现有的服务组，用于检查是否需要更新
            existing_groups = {}
            # 修改：使用xpath()方法查找服务组节点
            for group_node in service_obj.xpath("./*[local-name()='service-group']"):
                # 查找名称节点，支持<n>和<n>两种格式
                # 修改：使用xpath()方法代替find()方法
                name_nodes = group_node.xpath("./*[local-name()='name']")
                if not name_nodes:
                    name_nodes = group_node.xpath("./*[local-name()='n']")
                
                if name_nodes and name_nodes[0].text:
                    group_name = name_nodes[0].text
                    log(_("info.found_existing_service_group", name=group_name))
                    existing_groups[group_name] = group_node
            
            # 调试日志：显示找到的现有服务组
            log(_("info.existing_service_groups_count", count=len(existing_groups)))
            for name in existing_groups:
                log(_("info.existing_service_group_name", name=name))
            
            # 处理每个服务组
            for group in service_groups:
                try:
                    # 获取服务组属性
                    group_name = group.get("name", "")
                    members = group.get("members", [])
                    comment = group.get("comment", "")
                    
                    # 验证服务组名称
                    if not group_name:
                        log(_("warning.service_group_missing_name"), "warning")
                        stats["skipped"] += 1
                        stats["invalid_groups"].append({"reason": "missing_name"})
                        continue
                    
                    # 处理服务组名称中的特殊字符
                    # 将空格替换为连字符
                    safe_group_name = group_name.replace(" ", "-")
                    # 移除其他不安全字符
                    safe_group_name = re.sub(r'[^\w\-]', '', safe_group_name)
                    
                    if safe_group_name != group_name:
                        log(_("info.service_group_name_sanitized", original=group_name, sanitized=safe_group_name))
                    
                    # 验证成员列表
                    if not members:
                        log(_("warning.service_group_no_members", name=safe_group_name), "warning")
                        stats["skipped"] += 1
                        stats["invalid_groups"].append({"name": safe_group_name, "reason": "no_members"})
                        continue
                    
                    # 预处理成员名称
                    processed_members = self.preprocess_service_members(members, service_objects)
                    valid_members = []
                    invalid_members = []
                    
                    for original_member, processed_member in processed_members.items():
                        # 验证成员是否存在于服务对象中，支持映射关系

                        is_valid, final_member_name = self.validate_service_member(processed_member, service_objects, service_mapping_relationships)
                        if is_valid:
                            valid_members.append((original_member, final_member_name))
                            if final_member_name != processed_member:
                                log(_("info.service_group_member_name_changed",
                                     original=processed_member, final=final_member_name))
                        else:
                            invalid_members.append(original_member)
                            log(_("warning.service_group_member_not_found", name=original_member), "warning")
                    
                    # 如果启用了严格验证模式且存在无效成员，则跳过此服务组
                    if service_objects and fail_on_invalid and invalid_members:
                        log(_("warning.service_group_invalid_members_strict_mode", 
                             name=safe_group_name, 
                             invalid_count=len(invalid_members),
                             invalid_members=", ".join(invalid_members)), "warning")
                        stats["skipped"] += 1
                        stats["invalid_groups"].append({
                            "name": safe_group_name, 
                            "reason": "invalid_members_strict_mode",
                            "invalid_members": invalid_members
                        })
                        continue
                    
                    # 如果没有有效成员，跳过此服务组
                    if not valid_members and service_objects:  # 只有当提供了服务对象列表时才进行验证
                        log(_("warning.service_group_no_valid_members", name=safe_group_name), "warning")
                        stats["skipped"] += 1
                        stats["invalid_groups"].append({"name": safe_group_name, "reason": "no_valid_members"})
                        continue
                    
                    # 调试日志：显示正在处理的服务组
                    log(_("info.processing_service_group", name=safe_group_name, original=group_name))
                    
                    # 检查服务组是否已存在
                    if safe_group_name in existing_groups:
                        # 更新现有服务组
                        service_group = existing_groups[safe_group_name]
                        log(_("info.updating_existing_service_group", name=safe_group_name))
                        
                        # 清除现有成员
                        # 修改：使用xpath()方法查找成员节点
                        for member_node in service_group.xpath("./*[local-name()='service-set']"):
                            service_group.remove(member_node)
                        
                        # 获取名称标签的本地名称，以便使用相同的格式
                        name_tag = "name"
                        # 修改：使用xpath()方法查找名称节点
                        name_nodes = service_group.xpath("./*[local-name()='name']")
                        if not name_nodes:
                            name_nodes = service_group.xpath("./*[local-name()='n']")
                            if name_nodes:
                                name_tag = "n"
                        
                        # 添加新成员，使用与现有名称节点相同的标签名
                        members_to_use = valid_members if service_objects else [(m, m) for m in members]
                        for original_member, processed_member in members_to_use:
                            try:
                                service_set = etree.SubElement(service_group, "service-set")
                                member_name = etree.SubElement(service_set, name_tag)
                                # 使用html.escape转义XML特殊字符
                                member_name.text = html.escape(processed_member)

                            except Exception as e:
                                log(_("error.failed_to_process_member", 
                                     group=safe_group_name, 
                                     member=original_member,
                                     error=str(e)), "error")
                                # 记录错误但继续处理其他成员
                        
                        # 更新注释（如果有）
                        # 修改：使用xpath()方法查找描述节点
                        desc_nodes = service_group.xpath("./*[local-name()='description']")
                        if comment:
                            if desc_nodes:
                                # 使用html.escape转义XML特殊字符
                                desc_nodes[0].text = html.escape(comment)
                            else:
                                description = etree.SubElement(service_group, "description")
                                # 使用html.escape转义XML特殊字符
                                description.text = html.escape(comment)
                        elif desc_nodes:
                            service_group.remove(desc_nodes[0])
                        
                        stats["updated"] += 1
                        stats["processed"] += 1  # 确保更新也计入处理数量
                        log(_("info.updated_service_group", name=safe_group_name, members=len(members)))
                    else:
                        # 创建新的服务组节点
                        service_group = etree.SubElement(service_obj, "service-group")
                        
                        # 确定使用哪种名称标签格式（<n>或<n>）
                        # 检查现有服务组的格式
                        name_tag = "name"
                        if existing_groups:
                            # 获取第一个现有服务组
                            first_group = next(iter(existing_groups.values()))
                            # 修改：使用xpath()方法查找名称节点
                            name_nodes = first_group.xpath("./*[local-name()='name']")
                            if not name_nodes:
                                name_nodes = first_group.xpath("./*[local-name()='n']")
                                if name_nodes:
                                    name_tag = "n"
                        
                        # 添加服务组名称
                        name_node = etree.SubElement(service_group, name_tag)
                        # 使用html.escape转义XML特殊字符
                        name_node.text = html.escape(safe_group_name)
                        
                        # 如果有注释，添加到description字段
                        if comment:
                            description = etree.SubElement(service_group, "description")
                            # 使用html.escape转义XML特殊字符
                            description.text = html.escape(comment)
                        
                        # 添加服务组成员
                        members_to_use = valid_members if service_objects else [(m, m) for m in members]
                        for original_member, processed_member in members_to_use:
                            try:
                                service_set = etree.SubElement(service_group, "service-set")
                                member_name = etree.SubElement(service_set, name_tag)
                                # 使用html.escape转义XML特殊字符
                                member_name.text = html.escape(processed_member)

                            except Exception as e:
                                log(_("error.failed_to_process_member", 
                                     group=safe_group_name, 
                                     member=original_member,
                                     error=str(e)), "error")
                                # 记录错误但继续处理其他成员
                        
                        stats["created"] += 1
                        stats["processed"] += 1
                        log(_("info.added_service_group", name=safe_group_name, members=len(members)))
                    
                except Exception as e:
                    log(_("error.failed_to_process_service_group", 
                         name=group.get("name", "unknown"), 
                         error=str(e)), "error")
                    stats["skipped"] += 1
                    stats["invalid_groups"].append({
                        "name": group.get("name", "unknown"),
                        "reason": str(e)
                    })
            
        except Exception as e:
            import traceback
            log(_("error.failed_to_process_service_groups", error=str(e)), "error")
            log(traceback.format_exc(), "error")  # 添加堆栈跟踪以便调试
            stats["skipped"] = len(service_groups)
        
        log(_("info.service_groups_processing_complete", 
             total=len(service_groups),
             success=stats["processed"],
             updated=stats["updated"],
             created=stats["created"],
             skipped=stats["skipped"]))
        
        return stats

# 创建处理器实例，便于导入
service_group_processor = ServiceGroupProcessor() 