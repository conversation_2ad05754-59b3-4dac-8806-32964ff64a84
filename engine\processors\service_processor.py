#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
服务对象处理器模块

处理多厂商格式的服务对象配置，应用预定义服务映射，转换为NTOS YANG模型格式
"""

import os
import json
import re
from engine.utils.logger import log
from engine.utils.i18n import _

class ServiceProcessor:
    """服务对象处理器类"""
    
    def __init__(self):
        """初始化处理器"""
        self.name = "service_processor"
        self.description = _("service.processor_description")
        
        # NTOS内置服务数据
        self.ntos_builtin_services = {}
        # 厂商到NTOS服务的映射
        self.vendor_mappings = {}
        # 兼容性支持 - 原始飞塔服务映射
        self.service_mapping = {}
        self.fortigate_predefined_services = set()
        
        # 加载各种数据
        self.load_ntos_builtin_services()
        self.load_vendor_mappings()
        self.load_predefined_service_mapping()  # 向后兼容
        
    def load_ntos_builtin_services(self):
        """
        加载NTOS内置服务数据
        """
        try:
            # 获取当前脚本目录
            current_dir = os.path.dirname(os.path.abspath(__file__))
            # 构建数据文件路径
            services_file = os.path.join(current_dir, "..", "data", "services", "ntos_builtin_services.json")
            
            # 检查文件是否存在
            if not os.path.exists(services_file):
                log(_("warning.ntos_services_file_not_found", path=services_file), "warning")
                return
            
            # 加载服务数据文件
            with open(services_file, 'r', encoding='utf-8') as f:
                self.ntos_builtin_services = json.load(f)
            
            log(_("info.ntos_builtin_services_loaded", count=len(self.ntos_builtin_services)))

            
        except Exception as e:
            log(_("error.ntos_services_load_failed", error=str(e)), "error")
    
    def load_vendor_mappings(self):
        """
        加载厂商服务到NTOS服务的映射
        """
        try:
            # 获取当前脚本目录
            current_dir = os.path.dirname(os.path.abspath(__file__))
            
            # 加载飞塔映射
            fortigate_mapping_file = os.path.join(current_dir, "..", "data", "mappings", "fortigate_to_ntos_mapping.json")
            
            if os.path.exists(fortigate_mapping_file):
                with open(fortigate_mapping_file, 'r', encoding='utf-8') as f:
                    self.vendor_mappings["fortigate"] = json.load(f)
                
                log(_("info.vendor_mapping_loaded", vendor="fortigate", count=len(self.vendor_mappings["fortigate"])))

            else:
                log(_("warning.vendor_mapping_file_not_found", vendor="fortigate", path=fortigate_mapping_file), "warning")
            
            # 未来可以在这里添加其他厂商的映射加载
            # 例如:
            # cisco_mapping_file = os.path.join(current_dir, "..", "data", "mappings", "cisco_to_ntos_mapping.json")
            # if os.path.exists(cisco_mapping_file):
            #     with open(cisco_mapping_file, 'r', encoding='utf-8') as f:
            #         self.vendor_mappings["cisco"] = json.load(f)
            
        except Exception as e:
            log(_("error.vendor_mapping_load_failed", error=str(e)), "error")
    
    def load_predefined_service_mapping(self):
        """
        加载预定义服务映射（向后兼容）
        """
        try:
            # 获取当前脚本目录
            current_dir = os.path.dirname(os.path.abspath(__file__))
            # 构建映射文件路径
            mapping_file = os.path.join(current_dir, "..", "data", "mappings", "service_mapping.json")
            
            # 检查文件是否存在
            if not os.path.exists(mapping_file):
                log(_("warning.service_mapping_file_not_found", path=mapping_file), "warning")
                return
            
            # 加载映射文件
            with open(mapping_file, 'r', encoding='utf-8') as f:
                self.service_mapping = json.load(f)
            
            # 记录所有飞塔预定义服务名称
            self.fortigate_predefined_services = set(self.service_mapping.keys())
            
            # 统计别名数量
            alias_count = 0
            for service_name, mapping in self.service_mapping.items():
                if "aliases" in mapping and isinstance(mapping["aliases"], list):
                    alias_count += len(mapping["aliases"])
            
            log(_("info.service_mapping_loaded", count=len(self.service_mapping)))
            if alias_count > 0:
                log(_("info.service_aliases_loaded", count=alias_count))

            
        except Exception as e:
            log(_("error.service_mapping_load_failed", error=str(e)), "error")
    
    def normalize_service_name(self, name):
        """
        增强的服务名称标准化
        
        Args:
            name (str): 服务名称
            
        Returns:
            str: 标准化后的服务名称
        """
        if not name:
            return ""
            
        # 转换为小写并移除连字符、下划线和空格
        normalized = re.sub(r'[-_\s]', '', name.lower())
        
        # 处理常见缩写和变形
        replacements = {
            'domain': 'dns',
            'webpage': 'http',
            'www': 'http',
            'secure': 'https',
            'mail': 'smtp',
            'remote': 'rdp',
            'database': 'db',
            'server': ''
        }
        
        for old, new in replacements.items():
            normalized = normalized.replace(old, new)
                
        return normalized
    
    def normalize_port_range(self, port_range):
        """
        将飞塔复杂的端口范围格式标准化为NTOS支持的格式
        
        Args:
            port_range (str): 飞塔格式的端口范围，如 "80-90:1024-2048"
            
        Returns:
            str: 标准化的端口范围，如 "80-90"，如果无法解析则返回None
        """
        if not port_range:
            return None
            
        # 处理简单的单一端口
        if port_range.isdigit():
            port_num = int(port_range)
            if 1 <= port_num <= 65535:
                return port_range
            else:
                log(_("warning.port_out_of_range"), "warning", port_range=port_range)
                return None
            
        # 处理简单的端口范围（如 "80-90"）
        if "-" in port_range and ":" not in port_range and " " not in port_range:
            # 检查是否是有效的范围格式
            try:
                start, end = map(int, port_range.split("-"))
                
                # 特殊处理全端口范围 "0-65535"，将其标准化为 "1-65535"
                if start == 0 and end == 65535:

                    return "1-65535"
                
                if 1 <= start <= end <= 65535:
                    return port_range
                elif start > end:  # 处理范围顺序错误
                    return f"{end}-{start}"
                elif start == end:  # 单一端口
                    return str(start)
                else:  # 无效的端口范围
                    log(_("warning.invalid_port_range"), "warning", port_range=port_range)
                    return None
            except ValueError:
                log(_("warning.cannot_parse_port_range"), "warning", port_range=port_range)
                return None
        
        # 处理带空格的多个端口/范围（如 "80 443 8080"）
        if " " in port_range and ":" not in port_range:
            ports = port_range.split()
            # 如果只有一个有效端口，直接返回
            if len(ports) == 1:
                return self.normalize_port_range(ports[0])
            # 如果有多个端口，取第一个范围（这是简化处理，可以根据需要调整）
            if ports:
                first_port = self.normalize_port_range(ports[0])
                if first_port:

                    return first_port
            return None
            
        # 处理冒号分隔的多端口列表（如 "80:443:8080"）
        if ":" in port_range:
            parts = port_range.split(":")
            valid_ports = []

            for part in parts:
                part = part.strip()
                if not part:
                    continue

                # 检查是否是有效的端口或端口范围
                if part.isdigit():
                    port_num = int(part)
                    if 1 <= port_num <= 65535:
                        valid_ports.append(part)
                    else:
                        log(f"端口超出范围 (1-65535): {part}", "warning")
                elif "-" in part and len(part.split("-")) == 2:
                    # 处理端口范围（如 "8080-8090"）
                    try:
                        start, end = map(int, part.split("-"))
                        if 1 <= start <= end <= 65535:
                            valid_ports.append(part)
                        else:
                            log(f"端口范围无效: {part}", "warning")
                    except ValueError:
                        log(f"端口范围格式错误: {part}", "warning")
                else:
                    log(f"无法识别的端口格式: {part}", "warning")

            if valid_ports:
                # 将多个端口用逗号连接，符合NTOS YANG模型要求
                result = ",".join(valid_ports)

                return result
            else:
                log(f"多端口服务 '{port_range}' 中没有有效端口", "warning")
                return None
            
        # 对于其他复杂情况，记录警告并返回None
        log(f"无法解析的复杂端口范围：{port_range}，忽略此端口", "warning")
        return None
    
    def get_ntos_service_from_vendor(self, vendor, service_name):
        """
        根据厂商和服务名称获取对应的NTOS内置服务
        
        Args:
            vendor (str): 厂商名称，如 'fortigate'
            service_name (str): 厂商特定的服务名称
            
        Returns:
            str: 对应的NTOS内置服务名称，如果没有匹配则返回None
        """
        if not service_name:
            return None
            
        # 检查厂商映射是否存在
        if vendor not in self.vendor_mappings:

            return None
            
        vendor_mapping = self.vendor_mappings[vendor]
        
        # 精确匹配
        if service_name in vendor_mapping:
            ntos_service = vendor_mapping[service_name]

            return ntos_service
        
        # 忽略大小写匹配
        service_lower = service_name.lower()
        for vendor_service, ntos_service in vendor_mapping.items():
            if service_lower == vendor_service.lower():

                return ntos_service
        
        # 模糊匹配
        normalized_name = self.normalize_service_name(service_name)
        for vendor_service, ntos_service in vendor_mapping.items():
            normalized_vendor = self.normalize_service_name(vendor_service)
            if normalized_name == normalized_vendor:

                return ntos_service
                

        return None
    
    def get_ntos_service_data(self, ntos_service_name):
        """
        获取NTOS内置服务的详细数据
        
        Args:
            ntos_service_name (str): NTOS内置服务名称
            
        Returns:
            dict: NTOS内置服务数据，如果不存在则返回None
        """
        if not ntos_service_name:
            return None
            
        # 精确匹配
        if ntos_service_name in self.ntos_builtin_services:
            return self.ntos_builtin_services[ntos_service_name]
            
        # 忽略大小写匹配
        for name, data in self.ntos_builtin_services.items():
            if ntos_service_name.lower() == name.lower():
                return data
                
        return None
    
    def is_predefined_service(self, service_name):
        """
        检查服务名称是否为预定义服务
        
        Args:
            service_name (str): 服务名称
            
        Returns:
            bool: 是否为预定义服务
        """
        # 先精确匹配
        if service_name in self.fortigate_predefined_services:
            return True
        
        # 然后忽略大小写匹配
        for predefined_name in self.fortigate_predefined_services:
            if service_name.lower() == predefined_name.lower():
                return True
        
        # 检查别名匹配
        for predefined_name, mapping in self.service_mapping.items():
            if "aliases" in mapping and isinstance(mapping["aliases"], list):
                # 精确别名匹配
                if service_name in mapping["aliases"]:
                    return True
                    
                # 忽略大小写别名匹配
                for alias in mapping["aliases"]:
                    if service_name.lower() == alias.lower():
                        return True
        
        # 最后模糊匹配
        normalized_name = self.normalize_service_name(service_name)
        for predefined_name in self.fortigate_predefined_services:
            normalized_predefined = self.normalize_service_name(predefined_name)
            if normalized_name == normalized_predefined:
                return True
                
            # 别名模糊匹配
            mapping = self.service_mapping.get(predefined_name, {})
            if "aliases" in mapping and isinstance(mapping["aliases"], list):
                for alias in mapping["aliases"]:
                    normalized_alias = self.normalize_service_name(alias)
                    if normalized_name == normalized_alias:
                        return True
        
        return False
    
    def get_predefined_service_mapping(self, service_name):
        """
        获取预定义服务映射信息，支持别名
        
        Args:
            service_name (str): 服务名称
            
        Returns:
            dict: 服务映射信息，如果没有映射则返回None
        """
        if not service_name:
            return None
            
        # 先精确匹配
        if service_name in self.service_mapping:

            return self.service_mapping[service_name]
        
        # 然后忽略大小写匹配
        for predefined_name, mapping in self.service_mapping.items():
            if service_name.lower() == predefined_name.lower():

                return mapping
        
        # 检查别名匹配
        for predefined_name, mapping in self.service_mapping.items():
            if "aliases" in mapping and isinstance(mapping["aliases"], list):
                # 精确别名匹配
                if service_name in mapping["aliases"]:

                    log(_("service.alias_match", service=service_name, target=predefined_name), "info")
                    return mapping
                    
                # 忽略大小写别名匹配
                for alias in mapping["aliases"]:
                    if service_name.lower() == alias.lower():

                        log(_("service.alias_match", service=service_name, target=predefined_name), "info")
                        return mapping
        
        # 最后模糊匹配
        normalized_name = self.normalize_service_name(service_name)
        for predefined_name, mapping in self.service_mapping.items():
            normalized_predefined = self.normalize_service_name(predefined_name)
            if normalized_name == normalized_predefined:

                log(_("service.fuzzy_match", service=service_name, target=predefined_name), "info")
                return mapping
                
            # 别名模糊匹配
            if "aliases" in mapping and isinstance(mapping["aliases"], list):
                for alias in mapping["aliases"]:
                    normalized_alias = self.normalize_service_name(alias)
                    if normalized_name == normalized_alias:

                        log(_("service.alias_match", service=service_name, target=predefined_name), "info")
                        return mapping
        

        return None
    
    def convert_service_object(self, service_obj):
        """
        转换服务对象到NTOS格式
        
        支持以下几种服务识别方式：
        1. 优先使用厂商服务映射到NTOS内置服务
        2. 使用飞塔预定义服务映射（向后兼容）
        3. 处理自定义服务
        
        Args:
            service_obj (dict): 飞塔格式的服务对象
            
        Returns:
            dict: NTOS格式的服务对象，如果转换失败则返回None
        """
        try:
            if not service_obj or "name" not in service_obj:
                log(_("warning.service_missing_name"), "warning")
                return None
                
            service_name = service_obj.get("name", "")
            
            # 1. 首先尝试从厂商映射获取NTOS内置服务
            ntos_service_name = self.get_ntos_service_from_vendor("fortigate", service_name)
            if ntos_service_name:
                # 获取NTOS内置服务数据
                ntos_service_data = self.get_ntos_service_data(ntos_service_name)
                if ntos_service_data:

                    
                    # 从NTOS内置服务创建NTOS格式的服务对象
                    ntos_obj = {
                        "name": ntos_service_name,
                        "type": "builtin",  # 标记为内置服务
                        "protocol": ntos_service_data.get("protocol", "").lower(),
                        "description": _(ntos_service_data.get("description", "")),
                        "original_name": service_name,  # 保留原始名称
                        "vendor": "fortigate"  # 记录原始厂商
                    }
                    
                    # 添加端口信息
                    if "port" in ntos_service_data:
                        ntos_obj["port"] = ntos_service_data["port"]
                    elif "ports" in ntos_service_data:
                        ntos_obj["port"] = ntos_service_data["ports"]
                    
                    return ntos_obj
            
            # 2. 然后使用飞塔预定义服务映射（向后兼容）
            mapping = self.get_predefined_service_mapping(service_name)
            if mapping:
                # 获取端口信息
                if "dst_port" in mapping:
                    dst_port = mapping["dst_port"]
                    # 检查是否为端口范围
                    if isinstance(dst_port, list) and len(dst_port) >= 2:
                        if dst_port[0] == dst_port[1]:  # 单一端口
                            dst_port = str(dst_port[0])
                        else:  # 端口范围
                            normalized_port = f"{dst_port[0]}-{dst_port[1]}"
                            # 如果原始端口与标准化端口不同，记录日志
                            original_port = service_obj.get("dst_port", "")
                            if original_port and original_port != normalized_port:
                                log(_("service.port_range_normalized", 
                                      original=original_port, 
                                      normalized=normalized_port), "info")
                            dst_port = normalized_port
                elif "ports" in mapping:
                    dst_port = mapping["ports"]
                else:
                    # 尝试从原始服务对象获取端口信息
                    if "tcp-portrange" in service_obj and service_obj["tcp-portrange"]:
                        dst_port = self.normalize_port_range(service_obj["tcp-portrange"])
                    elif "udp-portrange" in service_obj and service_obj["udp-portrange"]:
                        dst_port = self.normalize_port_range(service_obj["udp-portrange"])
                    else:
                        dst_port = ""
                
                # 创建NTOS格式的服务对象
                ntos_obj = {
                    "name": mapping.get("ntos_service", service_name),  # 优先使用NTOS服务名
                    "type": "predefined" if "ntos_service" in mapping else "custom",
                    "protocol": mapping.get("protocol", "").lower(),
                    "port": dst_port,
                    "description": mapping.get("description", ""),
                    "original_name": service_name,  # 保留原始名称
                    "vendor": "fortigate"  # 记录原始厂商
                }
                
                return ntos_obj
                
            # 3. 处理自定义服务
            # 从原始服务对象获取协议和端口信息
            protocol = service_obj.get("protocol", "").lower()
            
            # 如果没有显式协议字段，根据端口类型推断协议
            if not protocol:
                if "tcp-portrange" in service_obj and service_obj.get("tcp-portrange"):
                    if "udp-portrange" in service_obj and service_obj.get("udp-portrange"):
                        protocol = "tcp_udp"  # 同时有TCP和UDP端口
                    else:
                        protocol = "tcp"  # 只有TCP端口
                elif "udp-portrange" in service_obj and service_obj.get("udp-portrange"):
                    protocol = "udp"  # 只有UDP端口
                else:
                    # 检查其他协议类型
                    raw_protocol = service_obj.get("protocol", "").upper()
                    if raw_protocol in ["ICMP", "ICMP6", "IP"]:
                        protocol = raw_protocol.lower()
                    else:
                        protocol = "tcp"  # 默认为TCP
            
            # 根据协议类型获取端口信息
            dst_port = None


            if protocol == "tcp" and "tcp-portrange" in service_obj and service_obj["tcp-portrange"]:
                dst_port = self.normalize_port_range(service_obj["tcp-portrange"])
            elif protocol == "udp" and "udp-portrange" in service_obj and service_obj["udp-portrange"]:
                dst_port = self.normalize_port_range(service_obj["udp-portrange"])
            elif protocol == "tcp_udp":
                # 如果是TCP_UDP协议，同时获取TCP和UDP端口信息
                tcp_port = None
                udp_port = None
                if "tcp-portrange" in service_obj and service_obj["tcp-portrange"]:
                    tcp_port = self.normalize_port_range(service_obj["tcp-portrange"])
                if "udp-portrange" in service_obj and service_obj["udp-portrange"]:
                    udp_port = self.normalize_port_range(service_obj["udp-portrange"])

                # 对于多协议服务，我们需要保存两个端口信息
                if tcp_port or udp_port:
                    # 创建包含TCP和UDP端口信息的特殊结构
                    dst_port = {
                        "tcp": tcp_port,
                        "udp": udp_port
                    }

            elif protocol == "ip":
                # 处理IP协议，需要协议号
                if "protocol-number" in service_obj and service_obj["protocol-number"]:
                    dst_port = service_obj["protocol-number"]

                else:
                    log(f"IP协议服务 '{service_name}' 缺少协议号", "warning")
                    dst_port = None
            elif protocol == "icmp":
                # 处理ICMP协议，可能有类型和代码
                icmp_info = {}
                if "icmptype" in service_obj and service_obj["icmptype"]:
                    icmp_info["type"] = service_obj["icmptype"]
                if "icmpcode" in service_obj and service_obj["icmpcode"]:
                    icmp_info["code"] = service_obj["icmpcode"]

                if icmp_info:
                    dst_port = icmp_info

                else:
                    # 如果没有指定类型，使用默认的ICMP协议ID
                    dst_port = "1"  # ICMP协议ID

            elif protocol == "all":
                # 如果是ALL协议，优先处理TCP端口，然后是UDP端口
                if "tcp-portrange" in service_obj and service_obj["tcp-portrange"]:
                    dst_port = self.normalize_port_range(service_obj["tcp-portrange"])

                elif "udp-portrange" in service_obj and service_obj["udp-portrange"]:
                    dst_port = self.normalize_port_range(service_obj["udp-portrange"])

                
            # 创建NTOS格式的服务对象
            ntos_obj = {
                "name": service_name,
                "type": "custom",
                "protocol": protocol,
                "original_name": service_name,  # 保留原始名称
                "vendor": "fortigate"  # 记录原始厂商
            }
            
            # 只有当端口信息有效时才添加端口字段
            if dst_port is not None:
                ntos_obj["port"] = dst_port

            else:
                # 如果是需要端口的协议但没有有效端口，记录警告
                if protocol in ["tcp", "udp", "tcp_udp"]:
                    # 添加详细的调试信息

                    log("warning.service_missing_port_config", "warning", name=service_name, protocol=protocol)
            
            # 可选：添加描述
            if "comment" in service_obj:
                ntos_obj["description"] = service_obj["comment"]

            # 处理iprange字段（如果存在）
            if self._has_iprange_restriction(service_obj):
                log(f"开始处理服务对象 {service_name} 的iprange字段", "info")
                iprange_info = self._extract_iprange_info(service_obj)
                if iprange_info.get('valid'):
                    # 生成地址对象名称
                    from engine.utils.validation import generate_address_name_from_service
                    address_name = generate_address_name_from_service(service_name, "RANGE")

                    # 将iprange信息添加到服务对象中，供后续处理使用
                    ntos_obj["iprange_info"] = {
                        "has_iprange": True,
                        "original_range": iprange_info['original_range'],
                        "start_ip": iprange_info['start_ip'],
                        "end_ip": iprange_info['end_ip'],
                        "generated_address_name": address_name
                    }

                    log(f"服务对象 {service_name} iprange转换成功: {iprange_info['original_range']} -> 地址对象 {address_name}", "info")

                    # 记录详细的转换信息

                else:
                    log(f"服务对象 {service_name} 的iprange字段格式无效: {service_obj.get('iprange', '')}", "warning")
                    # 记录具体的错误原因
                    if 'error' in iprange_info:
                        log(_("warning.service_iprange_conversion_error",
                             service=service_obj.get("name", "unknown"),
                             error=iprange_info['error']), "warning")

            return ntos_obj

        except Exception as e:
            log(_("error.service_conversion_failed",
                  service=service_obj.get("name", "unknown"),
                  error=str(e)), "error")
            return None

    def _has_iprange_restriction(self, service_config: dict) -> bool:
        """
        检测服务对象是否包含iprange字段限制

        Args:
            service_config: FortiGate服务对象配置

        Returns:
            bool: 是否包含iprange字段
        """
        try:
            # 检查是否存在iprange字段且不为空
            iprange = service_config.get('iprange', '')
            if iprange and isinstance(iprange, str) and iprange.strip():

                return True
            return False
        except Exception as e:
            log(f"检测iprange字段时发生错误: {str(e)}", "error")
            return False

    def _extract_iprange_info(self, service_config: dict) -> dict:
        """
        从服务对象配置中提取IP范围信息

        Args:
            service_config: FortiGate服务对象配置

        Returns:
            dict: IP范围信息，包含start_ip, end_ip, original_range等字段
        """
        try:
            iprange = service_config.get('iprange', '').strip()
            if not iprange:
                return {}

            # 解析IP范围格式：start_ip-end_ip
            if '-' not in iprange:
                error_msg = f"无效的iprange格式，缺少连字符: {iprange}"
                log(error_msg, "warning")
                return {'valid': False, 'error': error_msg}

            parts = iprange.split('-', 1)  # 只分割第一个连字符
            if len(parts) != 2:
                error_msg = f"无效的iprange格式，分割后部分数量不正确: {iprange}"
                log(error_msg, "warning")
                return {'valid': False, 'error': error_msg}

            start_ip = parts[0].strip()
            end_ip = parts[1].strip()

            # 基本IP地址格式验证
            if not self._is_valid_ip_format(start_ip) or not self._is_valid_ip_format(end_ip):
                error_msg = f"无效的IP地址格式: {start_ip} - {end_ip}"
                log(error_msg, "warning")
                return {'valid': False, 'error': error_msg}

            return {
                'start_ip': start_ip,
                'end_ip': end_ip,
                'original_range': iprange,
                'valid': True
            }

        except Exception as e:
            error_msg = f"提取iprange信息时发生错误: {str(e)}"
            log(error_msg, "error")
            return {'valid': False, 'error': error_msg}

    def _is_valid_ip_format(self, ip_str: str) -> bool:
        """
        验证IP地址格式的基本有效性

        Args:
            ip_str: IP地址字符串

        Returns:
            bool: 是否为有效的IP地址格式
        """
        try:
            import ipaddress
            ipaddress.IPv4Address(ip_str)
            return True
        except (ipaddress.AddressValueError, ValueError):
            # 如果ipaddress模块验证失败，使用正则表达式作为备选
            import re
            ip_pattern = r'^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$'
            match = re.match(ip_pattern, ip_str)
            if match:
                # 检查每个八位组是否在0-255范围内
                octets = [int(octet) for octet in match.groups()]
                return all(0 <= octet <= 255 for octet in octets)
            return False
        except Exception as e:
            log(f"IP地址格式验证时发生错误: {str(e)}", "error")
            return False

    def _generate_address_from_iprange(self, service_name: str, iprange: str) -> dict:
        """
        从服务对象的iprange字段生成地址对象

        Args:
            service_name: 服务对象名称
            iprange: IP范围字符串

        Returns:
            dict: 生成的地址对象，失败时返回None
        """
        try:
            from engine.processors.address_processor import FortinetAddressProcessor
            from engine.utils.validation import generate_address_name_from_service

            # 生成地址对象名称
            address_name = generate_address_name_from_service(service_name, "RANGE")

            # 使用地址处理器生成地址对象
            address_processor = FortinetAddressProcessor()
            address_obj = address_processor.generate_address_from_iprange(
                address_name, iprange, service_name
            )

            return address_obj

        except Exception as e:
            log(f"从服务对象 {service_name} 生成地址对象失败: {str(e)}", "error")
            return None

    def _clean_service_config(self, service_config: dict) -> dict:
        """
        清理服务对象配置，移除iprange字段

        Args:
            service_config: 原始服务对象配置

        Returns:
            dict: 清理后的服务对象配置
        """
        try:
            # 创建配置副本
            cleaned_config = service_config.copy()

            # 移除iprange字段
            if 'iprange' in cleaned_config:
                removed_iprange = cleaned_config.pop('iprange')


            return cleaned_config

        except Exception as e:
            log(f"清理服务对象配置时发生错误: {str(e)}", "error")
            return service_config

    def process_service_iprange_conversion(self, service_objects: dict, context) -> dict:
        """
        处理所有服务对象的iprange转换，生成对应的地址对象

        Args:
            service_objects: 服务对象字典
            context: 转换上下文

        Returns:
            dict: 处理结果统计
        """
        try:
            conversion_stats = {
                'total_services': len(service_objects),
                'services_with_iprange': 0,
                'successful_conversions': 0,
                'failed_conversions': 0,
                'generated_addresses': []
            }

            for service_name, service_config in service_objects.items():
                if self._has_iprange_restriction(service_config):
                    conversion_stats['services_with_iprange'] += 1

                    # 提取iprange信息
                    iprange_info = self._extract_iprange_info(service_config)
                    if iprange_info.get('valid'):
                        # 生成地址对象
                        address_obj = self._generate_address_from_iprange(
                            service_name, iprange_info['original_range']
                        )

                        if address_obj:
                            # 添加到转换上下文
                            source_info = {
                                'service_name': service_name,
                                'iprange': iprange_info['original_range'],
                                'conversion_type': 'service_iprange'
                            }

                            context.add_generated_address(address_obj, source_info)
                            context.track_service_iprange_conversion(
                                service_name, address_obj['name']
                            )

                            conversion_stats['successful_conversions'] += 1
                            conversion_stats['generated_addresses'].append(address_obj['name'])

                            log(f"成功转换服务对象 {service_name} 的iprange: {iprange_info['original_range']} -> {address_obj['name']}", "info")
                        else:
                            conversion_stats['failed_conversions'] += 1
                            context.add_warning(
                                f"服务对象 {service_name} 的iprange转换失败",
                                "service_iprange_conversion"
                            )
                    else:
                        conversion_stats['failed_conversions'] += 1
                        error_msg = iprange_info.get('error', '未知错误')
                        context.add_warning(
                            f"服务对象 {service_name} 的iprange格式无效: {error_msg}",
                            "service_iprange_validation"
                        )

            # 记录转换统计
            if conversion_stats['services_with_iprange'] > 0:
                log(f"iprange转换统计: 发现{conversion_stats['services_with_iprange']}个包含iprange的服务对象, "
                    f"成功转换{conversion_stats['successful_conversions']}个, "
                    f"失败{conversion_stats['failed_conversions']}个", "info")

            return conversion_stats

        except Exception as e:
            log(f"处理服务对象iprange转换时发生错误: {str(e)}", "error")
            context.add_error(f"服务对象iprange转换处理失败: {str(e)}", "service_iprange_processing")
            return {
                'total_services': len(service_objects),
                'services_with_iprange': 0,
                'successful_conversions': 0,
                'failed_conversions': 0,
                'generated_addresses': []
            }
    
    def process_service_objects(self, service_objects, skip_predefined=False):
        """
        批量处理服务对象
        
        Args:
            service_objects (list): 飞塔格式的服务对象列表
            skip_predefined (bool): 是否跳过预定义服务
            
        Returns:
            dict: 处理结果统计
                {
                    "successful": [成功转换的服务对象],
                    "failed": [转换失败的服务对象],
                    "skipped": [被跳过的服务对象],
                    "details": [详细处理记录],
                    "mapping_relationships": {原始服务名: NTOS服务名} # 服务映射关系
                }
        """
        if not service_objects:
            return {
                "successful": [],
                "failed": [],
                "skipped": [],
                "details": [],
                "mapping_relationships": {}
            }
            
        result = {
            "successful": [],
            "failed": [],
            "skipped": [],
            "details": [],
            "mapping_relationships": {}
        }
        
        # 检查是否所有服务都是预定义服务
        all_predefined = True
        predefined_count = 0
        
        # 第一次遍历：检查预定义服务情况
        for service_obj in service_objects:
            if not service_obj or "name" not in service_obj:
                all_predefined = False
                continue
                
            service_name = service_obj.get("name", "")
            
            if self.is_predefined_service(service_name):
                predefined_count += 1
                # 获取映射关系并保存
                mapping = self.get_predefined_service_mapping(service_name)
                if mapping and "ntos_service" in mapping:
                    result["mapping_relationships"][service_name] = mapping["ntos_service"]

            else:
                all_predefined = False
        
        # 如果全部是预定义服务，直接返回它们
        if all_predefined and predefined_count > 0:
            log(_("info.all_predefined_services_found", count=predefined_count), "info")

            
            # 将所有服务放入successful列表
            for service_obj in service_objects:
                service_name = service_obj.get("name", "")
                mapping = self.get_predefined_service_mapping(service_name)
                
                if mapping:
                    # 创建NTOS格式的服务对象
                    ntos_obj = {
                        "name": mapping.get("ntos_service", service_name),
                        "type": "predefined",
                        "protocol": mapping.get("protocol", "").lower(),
                        "original_name": service_name,
                        "vendor": "fortigate"
                    }
                    
                    # 添加端口信息
                    if "dst_port" in mapping:
                        dst_port = mapping["dst_port"]
                        if isinstance(dst_port, list) and len(dst_port) >= 2:
                            if dst_port[0] == dst_port[1]:
                                ntos_obj["port"] = str(dst_port[0])
                            else:
                                ntos_obj["port"] = f"{dst_port[0]}-{dst_port[1]}"
                    
                    result["successful"].append(ntos_obj)
                    result["details"].append({
                        "name": service_name,
                        "status": "success",
                        "type": "predefined",
                        "ntos_name": ntos_obj["name"]
                    })
            
            return result
            
        # 如果不是全部预定义服务，则正常处理每个服务对象
        for service_obj in service_objects:
            try:
                if not service_obj or "name" not in service_obj:
                    log(_("warning.service_missing_name"), "warning")
                    result["failed"].append(service_obj if service_obj else {"name": "unknown"})
                    result["details"].append({
                        "name": "unknown",
                        "status": "failed",
                        "reason": "missing_name"
                    })
                    continue
                    
                service_name = service_obj.get("name", "")
                
                # 检查是否跳过预定义服务
                if skip_predefined and self.is_predefined_service(service_name):

                    result["skipped"].append(service_obj)
                    result["details"].append({
                        "name": service_name,
                        "status": "skipped",
                        "reason": "predefined_service"
                    })
                    continue
                
                # 转换服务对象
                ntos_obj = self.convert_service_object(service_obj)
                
                if ntos_obj:
                    result["successful"].append(ntos_obj)
                    result["details"].append({
                        "name": service_name,
                        "status": "success",
                        "type": ntos_obj.get("type", "custom"),
                        "ntos_name": ntos_obj["name"]
                    })
                    
                    # 保存映射关系
                    result["mapping_relationships"][service_name] = ntos_obj["name"]
                    

                else:
                    result["failed"].append(service_obj)
                    result["details"].append({
                        "name": service_name,
                        "status": "failed",
                        "reason": "conversion_failed"
                    })

                    
            except Exception as e:
                service_name = service_obj.get("name", "unknown") if service_obj else "unknown"
                log(_("error.service_conversion_failed", 
                      service=service_name, 
                      error=str(e)), "error")
                result["failed"].append(service_obj if service_obj else {"name": "unknown"})
                result["details"].append({
                    "name": service_name,
                    "status": "failed",
                    "reason": str(e)
                })
                
        return result 