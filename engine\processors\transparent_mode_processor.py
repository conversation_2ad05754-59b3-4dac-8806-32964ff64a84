#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
FortiGate透明模式处理器
负责检测透明模式并处理相应的接口配置策略
"""

import json
import os
from engine.utils.logger import log, _
from engine.utils.i18n import _


class TransparentModeProcessor:
    """FortiGate透明模式处理器"""
    
    def __init__(self, model, interface_mapping=None):
        """
        初始化透明模式处理器
        
        Args:
            model (str): 设备型号，如 'z5100s'
            interface_mapping (dict): 接口映射关系
        """
        self.model = model
        self.interface_mapping = interface_mapping or {}
        self.device_interfaces = self._load_device_interfaces()
        log(_("transparent_mode_processor.initialized", model=model))
    
    def _load_device_interfaces(self):
        """
        加载设备接口配置
        
        Returns:
            dict: 设备接口配置数据
        """
        try:
            # 获取device_interfaces.json文件路径
            current_dir = os.path.dirname(os.path.abspath(__file__))
            engine_dir = os.path.dirname(current_dir)
            device_interfaces_file = os.path.join(engine_dir, "data", "device_interfaces.json")
            
            if not os.path.exists(device_interfaces_file):
                log(_("transparent_mode_processor.device_file_not_found", file=device_interfaces_file), "warning")
                return {}
            
            with open(device_interfaces_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            log(_("transparent_mode_processor.device_file_loaded", file=device_interfaces_file))
            return data
            
        except Exception as e:
            log(_("transparent_mode_processor.device_file_load_error", error=str(e)), "error")
            return {}
    
    def is_transparent_mode(self, system_settings):
        """
        检测是否为透明模式
        
        Args:
            system_settings (dict): 系统设置配置
            
        Returns:
            bool: 是否为透明模式
        """
        if not system_settings:
            return False
        
        opmode = system_settings.get("opmode", "nat").lower()
        is_transparent = opmode == "transparent"
        
        log(_("transparent_mode_processor.mode_detection", 
              opmode=opmode, is_transparent=is_transparent))
        
        return is_transparent
    
    def get_device_interfaces(self):
        """
        获取设备的所有物理接口
        
        Returns:
            list: 物理接口名称列表
        """
        interfaces = self.device_interfaces.get(self.model, {}).get("interfaces", [])
        log(_("transparent_mode_processor.device_interfaces", 
              model=self.model, count=len(interfaces)))
        return interfaces
    
    def find_mgmt_interface(self):
        """
        从接口映射中查找MGMT接口
        
        Returns:
            str: MGMT接口名称，如果未找到返回None
        """
        # 查找映射中的mgmt接口
        mgmt_interface = None
        for fortigate_name, ntos_name in self.interface_mapping.items():
            if fortigate_name.lower() == "mgmt":
                mgmt_interface = ntos_name
                break
        
        if mgmt_interface:
            log(_("transparent_mode_processor.mgmt_found", interface=mgmt_interface))
        else:
            log(_("transparent_mode_processor.mgmt_not_found"), "warning")
        
        return mgmt_interface
    
    def classify_interfaces_for_transparent_mode(self, mgmt_interface):
        """
        为透明模式分类接口
        
        Args:
            mgmt_interface (str): 管理接口名称
            
        Returns:
            dict: 接口分类结果
        """
        all_device_interfaces = self.get_device_interfaces()
        
        # 验证MGMT接口是否为设备的有效接口
        if mgmt_interface and mgmt_interface not in all_device_interfaces:
            log(_("transparent_mode_processor.invalid_mgmt_interface", 
                  interface=mgmt_interface, model=self.model), "warning")
            mgmt_interface = None
        
        # 分类接口
        if mgmt_interface:
            # 正常情况：MGMT接口保持路由模式，其他接口设为桥接模式
            bridge_interfaces = [iface for iface in all_device_interfaces if iface != mgmt_interface]
            mgmt_interfaces = [mgmt_interface]
        else:
            # 异常情况：没有有效的MGMT接口，所有接口都设为桥接模式
            bridge_interfaces = all_device_interfaces
            mgmt_interfaces = []
        
        result = {
            "mgmt_interfaces": mgmt_interfaces,
            "bridge_interfaces": bridge_interfaces,
            "total_interfaces": len(all_device_interfaces),
            "mgmt_valid": mgmt_interface is not None
        }
        
        log(_("transparent_mode_processor.interface_classification", 
              mgmt_count=len(mgmt_interfaces), 
              bridge_count=len(bridge_interfaces),
              total=len(all_device_interfaces)))
        
        return result
    
    def process_transparent_mode(self, system_settings):
        """
        处理透明模式配置
        
        Args:
            system_settings (dict): 系统设置配置
            
        Returns:
            dict: 透明模式处理结果，如果不是透明模式返回None
        """
        if not self.is_transparent_mode(system_settings):
            return None
        
        log(_("transparent_mode_processor.processing_transparent_mode"))
        
        # 查找MGMT接口
        mgmt_interface = self.find_mgmt_interface()
        
        # 分类接口
        interface_classification = self.classify_interfaces_for_transparent_mode(mgmt_interface)
        
        # 构建处理结果
        result = {
            "mode": "transparent",
            "mgmt_interface": mgmt_interface,
            "mgmt_ip": system_settings.get("manageip"),
            "hostname": system_settings.get("hostname"),
            "interface_classification": interface_classification,
            "bridge_config": {
                "name": "br0",
                "enabled": True,
                "slave_interfaces": interface_classification["bridge_interfaces"]
            }
        }
        
        log(_("transparent_mode_processor.transparent_mode_processed", 
              mgmt=mgmt_interface, 
              bridge_count=len(interface_classification["bridge_interfaces"])))
        
        return result
    
    def get_interface_working_mode(self, interface_name, transparent_result):
        """
        获取指定接口在透明模式下的工作模式
        
        Args:
            interface_name (str): 接口名称
            transparent_result (dict): 透明模式处理结果
            
        Returns:
            str: 工作模式 ('route' 或 'bridge')
        """
        if not transparent_result:
            return "route"  # 默认路由模式
        
        classification = transparent_result.get("interface_classification", {})
        
        if interface_name in classification.get("mgmt_interfaces", []):
            return "route"
        elif interface_name in classification.get("bridge_interfaces", []):
            return "bridge"
        else:
            # 接口不在设备接口列表中，默认路由模式
            log(_("transparent_mode_processor.interface_not_in_device", 
                  interface=interface_name), "warning")
            return "route"


def create_transparent_mode_processor(model, interface_mapping=None):
    """
    工厂函数：创建透明模式处理器实例
    
    Args:
        model (str): 设备型号
        interface_mapping (dict): 接口映射关系
        
    Returns:
        TransparentModeProcessor: 透明模式处理器实例
    """
    return TransparentModeProcessor(model, interface_mapping)



