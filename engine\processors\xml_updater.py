from lxml import etree
from engine.utils.logger import log

NSMAP = {
    "if": "urn:ruijie:ntos:params:xml:ns:yang:interface",
}


def update_interfaces(tree, interfaces):
    log("Updating interface nodes in running XML...")
    root = tree.getroot()
    parent = root.find(".//if:interface", namespaces=NSMAP)
    if parent is None:
        parent = etree.SubElement(root, etree.QName(NSMAP["if"], "interface"))

    for intf in interfaces:
        name = intf.get("name")
        if not name:
            continue

        existing = parent.xpath(f"./if:physical[if:name='{name}']", namespaces=NSMAP)
        if existing:
            log(f"Replacing existing interface: {name}")
            parent.remove(existing[0])

        new_node = build_physical_interface(intf)
        parent.append(new_node)


def build_physical_interface(intf):
    physical = etree.Element(etree.QName(NSMAP["if"], "physical"))
    etree.SubElement(physical, etree.QName(NSMAP["if"], "name")).text = intf["name"]

    if "ip" in intf:
        ipv4 = etree.SubElement(physical, etree.QName(NSMAP["if"], "ipv4"))
        address = etree.SubElement(ipv4, etree.QName(NSMAP["if"], "address"))
        # 使用YANG模型兼容的masked-ipv4-address格式 (例如: "***********/24")
        # 而不是分离的ip和prefix-length元素
        etree.SubElement(address, etree.QName(NSMAP["if"], "ip")).text = intf["ip"]

    if "description" in intf:
        etree.SubElement(physical, etree.QName(NSMAP["if"], "description")).text = intf["description"]

    if "status" in intf:
        etree.SubElement(physical, etree.QName(NSMAP["if"], "enabled")).text = str(intf["status"] == "enable").lower()

    if "allowaccess" in intf:
        access_mode = etree.SubElement(physical, etree.QName(NSMAP["if"], "access-mode"))
        for proto in intf["allowaccess"]:
            etree.SubElement(access_mode, etree.QName(NSMAP["if"], "protocol")).text = proto

    if "security_mode" in intf:
        etree.SubElement(physical, etree.QName(NSMAP["if"], "security-mode")).text = intf["security_mode"]

    if "role" in intf:
        etree.SubElement(physical, etree.QName(NSMAP["if"], "role")).text = intf["role"]

    if "snmp_index" in intf:
        etree.SubElement(physical, etree.QName(NSMAP["if"], "snmp-index")).text = str(intf["snmp_index"])

    if "ipv6" in intf:
        ipv6 = etree.SubElement(physical, etree.QName(NSMAP["if"], "ipv6"))
        address = etree.SubElement(ipv6, etree.QName(NSMAP["if"], "address"))
        etree.SubElement(address, etree.QName(NSMAP["if"], "ip")).text = intf["ipv6"]["ip"]

    return physical