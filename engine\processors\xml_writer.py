from lxml import etree
from engine.utils.logger import log
import os
import sys

# 导入修复命名空间的函数
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from fix_xml_namespaces import fix_xml_namespaces

def save_config(tree, file_path):
    """
    保存XML配置并修复命名空间问题
    
    Args:
        tree: XML树对象
        file_path: 输出文件路径
    """
    log(_("info.saving_xml_config"), "info", file_path=file_path)

    # 先保存到临时文件（不包含XML声明）
    temp_file = file_path + ".temp"
    tree.write(temp_file, encoding="utf-8", xml_declaration=False, pretty_print=True)

    try:
        # 修复命名空间问题
        log(_("info.fixing_xml_namespaces"), "info")
        fix_xml_namespaces(temp_file, file_path)
        log(_("info.xml_config_saved_with_fixed_namespaces"), "info", file_path=file_path)
        
        # 删除临时文件
        if os.path.exists(temp_file):
            os.remove(temp_file)
    except Exception as e:
        error_msg = _("error.fix_xml_namespaces_failed", error=str(e))
        log(error_msg, "error")
        log(_("info.using_unfixed_xml_file"), "info", temp_file=temp_file)
        
        # 如果修复失败，使用原始文件
        if os.path.exists(temp_file):
            # 复制临时文件到目标文件
            import shutil
            shutil.copy2(temp_file, file_path)
            os.remove(temp_file)
