{"summary": {"original_keys": 5609, "clean_keys": 4256, "removed_keys": 1353, "removal_rate": 24.12}, "removed_categories": {"test_keys": 13, "obsolete_keys": 12, "duplicate_keys": 355, "invalid_keys": 663}, "details": {"test_keys": ["Advanced Covid AI Ready", "Found 10,000,000,000 copies of Covid32.exe", "Hello, <PERSON>!", "compress.container_subprocess_test_failed", "compress.container_subprocess_test_success", "compress.usage.decrypt_example", "compress.usage.encrypt_example", "compress.usage.examples", "debug.service_groups_data_sample", "i18n:debug.service_groups_data_sample", "user.test.count", "user.test.hello", "user.test.welcome"], "obsolete_keys": ["Cython.Compiler.Main", "_CallbackFileWrapper__fp", "_compress.so", "_distutils_hack", "_temp_for_encryption.xml", "certifi", "ctypes.wintypes", "dropbox.com", "i18n:multiline.certificate_content", "user.security.certificate_expired", "user.security.certificate_invalid", "wildcard.dropbox.com"], "duplicate_groups": {"详细错误信息: {error}": ["zone_processor.detailed_error_info", "time_range_processor.detailed_error_info", "static_route_processor.detailed_error_info", "address_group_processor.detailed_error_info", "service_group_processor.detailed_error_info", "xml_template_integration.detailed_error_info"], "地址对象": ["type.address_object", "compatibility.address", "address_processing_stage.item_type"], "没有地址对象需要处理": ["info.no_address_objects_to_process", "address_processing_stage.no_address_objects"], "跳过未明确支持的地址类型: {name} ({type})": ["address_processor.skip_not_explicitly_supported", "warning.skipping_address_type_not_explicitly_supported"], "跳过不支持的地址类型: {name} ({type})": ["address_processor.skip_unsupported_type", "warning.skipping_address_unsupported_type"], "未知": ["common.unknown", "report.unknown", "user.time.unknown", "user.common.unknown", "user.status.unknown", "fortigate.unknown_section"], "未知原因": ["error.unknown_reason", "common.unknown_reason", "report.unknown_reason", "fortigate.reason.unknown", "user.stage.unknown_reason", "user.common.unknown_reason"], "接口": ["type.interface", "detail.interface", "report.interface", "config_type.interface", "compatibility.interface", "manual_config.interface.type", "manual_config.type.interface", "interface_processing_stage.interface_type"], "接口兼容性": ["issue.interface_compatibility", "compatibility.interface_compat"], "有 {count} 个接口因配置无效而被跳过": ["issue.invalid_interfaces_desc", "compatibility.invalid_interfaces_desc", "description.interfaces_skipped_due_to_invalid_config"], "请查看详细日志，根据兼容性问题修改接口配置": ["issue.invalid_interfaces_suggestion", "compatibility.invalid_interfaces_suggestion", "suggestion.check_logs_and_fix_interface_config"], "兼容性问题": ["compatibility.issue", "report.compatibility_issues", "user.summary.compatibility_issues"], "策略规则": ["type.policy_rule", "compatibility.policy", "config_type.policy_rules", "manual_config.policy_rules.type"], "警告": ["issue.warning", "severity.warning", "compatibility.severity_warning"], "有 {count} 个区域接口因未找到映射而被跳过": ["issue.unmapped_zone_interfaces_desc", "compatibility.unmapped_zone_interfaces_desc"], "请检查区域接口名称是否正确，或添加相应的接口映射": ["suggestion.check_zone_interface_names", "issue.unmapped_zone_interfaces_suggestion", "compatibility.unmapped_zone_interfaces_suggestion"], "区域接口映射": ["issue.zone_interface_mapping", "compatibility.zone_interface_mapping"], "已清理临时目录: {dir}": ["info.cleanup_temp_dir", "compress.cleanup_temp_dir"], "当前工作目录: {dir}": ["ui.current_working_dir", "info.current_working_dir", "compress.current_working_dir"], "检测到容器环境": ["compress.detected_container_env", "config_manager.container_environment_detected_new"], "加密过程中发生错误: {error}": ["error.encryption_error", "user.error.encryption_error", "compress.error.encryption_process", "enhanced_yang_generator.encryption_error"], "方法1失败: {error}": ["compress.method1_failed", "xml_template_integration.method1_failed"], "静态路由": ["type.static_route", "config_type.static_routes", "manual_config.static_routes.type"], "时间对象": ["type.time_object", "config_type.time_range"], "区域接口": ["config_type.zone_interface", "manual_config.interface_zone.type", "manual_config.type.zone_interface"], "未请求加密处理": ["encryption_stage.no_encryption_requested", "conversion_workflow.encryption_not_requested"], "接口映射验证通过": ["info.interface_mapping_validation_passed", "conversion_workflow.interface_mapping_validation_passed"], "使用新架构进行转换": ["info.using_new_architecture", "conversion_workflow.using_new_architecture"], "服务对象节点包含 {count} 个服务集合，保留节点": ["debug.fixed_legacy_generator_keeping_service_obj_node", "debug.improved_legacy_generator_keeping_service_obj_node"], "服务对象 #{index}: 名称={name}, 类型={type}": ["debug.service_object_details", "debug.legacy_generator_service_object_details"], "名称": ["detail.name", "report.name"], "原因": ["detail.reason", "report.reason", "user.suggestion.reason"], "状态": ["detail.status", "report.status"], "区域": ["detail.zone", "report.zone"], "未找到VRF节点": ["dns_generator.error.vrf_not_found", "ntos_extensions.error.vrf_not_found"], "DNS配置为空": ["dns_generator.warning.empty_config", "dns_generator.validation.empty_config"], "没有配置数据": ["dns_processing_stage.no_config_data", "operation_mode_stage.no_config_data", "time_range_processing_stage.no_config_data", "static_route_processing_stage.no_config_data"], "加密过程中出错: {error}": ["error.encrypt_error", "encrypt.error.process_failed"], "加密失败: {message}": ["user.error.encryption_failed", "encryption_stage.encryption_failed"], "初始化增强YANG生成器，模型: {model}, 版本: {version}": ["enhanced_yang_generator.init", "info.yang_generator_initialized"], "成功加载模板文件: {path}": ["info.template_loaded_success", "info.template_loaded_successfully", "enhanced_yang_generator.template_loaded"], "验证错误: {error}": ["error.validation_error", "enhanced_yang_generator.validation_error"], "yanglint验证失败: {message}": ["error.yanglint_validation_failed", "enhanced_yang_generator.yanglint_validation_failed"], "yanglint验证通过": ["validator.validation_passed", "enhanced_yang_generator.yanglint_validation_passed"], "错误: address group conversion failed": ["error.address_group_conversion_failed", "i18n:error.address_group_conversion_failed"], "错误: all physical interfaces unmapped": ["error.all_physical_interfaces_unmapped", "i18n:error.all_physical_interfaces_unmapped"], "无法生成目标配置": ["error.generate_target_failed", "error.cannot_generate_target_config"], "无法加载接口映射文件": ["error.cannot_load_mapping", "error.cannot_load_mapping_file"], "请检查接口配置和映射文件": ["error.check_interfaces", "error.check_interface_config"], "读取配置文件失败: {error}": ["error.read_config_failed", "error.config_file_read_failed"], "转换过程中发生错误": ["error.conversion_error", "error.conversion_failed"], "创建目录失败: {error}": ["ui.dir_creation_failed", "error.create_dir_failed"], "加密失败": ["error.encryption_failed", "user.detail.encryption_failed"], "配置文件不存在: {file}": ["error.file_not_exists", "fortigate_parser_adapter.config_file_not_found"], "生成NTOS配置失败: {error}": ["error.generate_ntos_failed", "error.ntos_generation_failed"], "接口映射验证失败：发现 {count} 个无效映射": ["error.interface_mapping_validation_failed", "user.error.interface_mapping_validation_failed"], "接口型号验证失败：发现 {count} 个无效映射": ["error.interface_model_validation_failed", "user.error.interface_model_validation_failed"], "接口未映射: {interfaces}": ["error.interface_not_mapped", "reason.unmapped_interfaces"], "路由数据格式不正确，无法创建静态路由: {data}": ["error.invalid_route_data_format", "yang.error.invalid_route_data_format"], "加载接口映射文件失败: {error}": ["error.load_mapping_failed", "error.mapping_file_load_failed"], "错误: missing interface config": ["error.missing_interface_config", "i18n:error.missing_interface_config"], "缺少必要字段: {fields}": ["error.missing_required_fields", "reason.missing_required_fields"], "无法找到模板文件: {model}/{version}": ["error.no_template", "error.template_not_found"], "解析配置文件失败: {error}": ["error.parse_config_failed", "error.parsing_config_failed"], "处理接口失败: {name}, 错误: {error}": ["error.process_interface_failed", "error.processing_interface_failed"], "静态路由 {id} 缺少目的网络信息，无法处理": ["error.route_missing_destination", "reason.route_missing_destination"], "错误: service group processing error": ["error.service_group_processing_error", "i18n:error.service_group_processing_error"], "创建静态路由失败: {error}": ["yang.error.static_route_creation_failed", "error.static_route_creation_failed_with_error"], "生成目标配置失败": ["error.target_config_generation_failed", "user.error.target_config_generation_failed"], "模板文件不存在: {path}": ["error.template_not_exists", "error.template_file_not_exists"], "时间对象 {name} 转换失败: {error}": ["error.time_range_conversion_failed", "time_range_processing_stage.schedule_conversion_failed"], "无法检测配置文件的FortiOS版本": ["error.unable_to_detect_version", "warning.unable_to_detect_version"], "XML生成失败: {error}": ["error.xml_generation_failed", "refactored_wrapper.xml_generation_failed"], "[缺失:{param}]": ["format.missing_parameter", "format.missing_param_placeholder"], "未知错误": ["user.error.unknown", "format.unknown_error"], "未知接口": ["interface.type.unknown", "format.unknown_interface"], "接口配置部分结束": ["fortigate.interface_section_end", "fortigate.debug.interface_section_end"], "解析接口: {name}": ["fortigate.parsing_interface", "fortigate.debug.parsing_interface"], "解析完成，找到 {interfaces} 个接口, {routes} 个静态路由, {zones} 个区域, {addresses} 个地址对象, {addrgroups} 个地址组, {services} 个服务对象, {servicegroups} 个服务组": ["fortigate.parsing_complete", "fortigate.parsing_complete_user"], "不支持的接口角色: {role}": ["warning.unsupported_role", "fortigate.reason.unsupported_role"], "开始解析配置文件": ["user.info.start_parsing", "fortigate.start_parsing_config", "user.info.parsing_config_start"], "时间对象 '{name}' 缺少必要的起止时间": ["fortigate.warning.schedule_missing_time", "fortigate.warning.schedule_missing_time_msg"], "时间对象名称 '{name}' 包含可疑字符，已被过滤": ["fortigate.warning.schedule_name_suspicious", "fortigate.warning.schedule_name_suspicious_msg"], "时间对象名称 '{name}' 过长，已截断": ["fortigate.warning.schedule_name_too_long", "fortigate.warning.schedule_name_too_long_msg"], "验证接口名称 '{interface}' 时发生异常: {error}": ["policy_processor.interface_validation_exception", "fortigate_strategy.interface.validation_exception"], "接口名称 '{interface}' 格式不符合NTOS标准": ["policy_processor.interface_format_invalid", "fortigate_strategy.interface.yang_invalid_format"], "区域 '{zone}' 中的接口 '{interface}' 未找到映射": ["fortigate_strategy.zone.interface_no_mapping", "policy_processor.zone_interface_mapping_not_found"], "接口名称 '{interface}' 不符合YANG模型规范": ["fortigate_strategy.zone.interface_yang_invalid", "policy_processor.interface_yang_validation_failed"], "区域名称 '{zone}' 包含非法字符": ["policy_processor.zone_name_invalid_chars", "fortigate_strategy.zone.invalid_characters"], "区域名称 '{zone}' 过长（超过32字符）": ["policy_processor.zone_name_too_long", "fortigate_strategy.zone.name_too_long"], "未找到区域 '{zone}' 的配置": ["fortigate_strategy.zone.not_found", "policy_processor.zone_config_not_found"], "解析区域 '{zone}' 时发生异常: {error}": ["policy_processor.zone_parsing_exception", "fortigate_strategy.zone.resolution_exception"], "区域 '{zone}' 解析为接口: {interfaces}": ["fortigate_strategy.zone.resolved_interfaces", "policy_processor.zone_resolved_to_interfaces"], "区域 '{zone}' 是标准区域名称": ["fortigate_strategy.zone.standard_zone", "policy_processor.zone_is_standard_name"], "验证区域 '{zone}' 时发生异常: {error}": ["policy_processor.zone_validation_exception", "fortigate_strategy.zone.validation_exception"], "区域 '{zone}' YANG验证通过": ["policy_processor.zone_yang_validation_passed", "fortigate_strategy.zone.yang_validation_passed"], "信息: received enhanced generator output": ["info.received_enhanced_generator_output", "i18n:info.received_enhanced_generator_output"], "信息: using default language": ["info.using_default_language", "i18n:info.using_default_language"], "使用增强策略处理器": ["info.using_enhanced_policy_processor", "i18n:info.using_enhanced_policy_processor"], "key": ["key", "i18n:key"], "警告: skipping address group empty members": ["warning.skipping_address_group_empty_members", "i18n:warning.skipping_address_group_empty_members"], "警告: skipping address group missing name": ["warning.skipping_address_group_missing_name", "i18n:warning.skipping_address_group_missing_name"], "添加 {count} 条静态路由到NTOS生成器": ["info.add_static_routes", "info.adding_static_routes"], "地址对象处理完成: 共 {total} 个，成功 {success} 个，失败 {failed} 个，跳过 {skipped} 个": ["info.address_objects_complete", "info.address_objects_processing_complete"], "地址对象处理结果: {success} 成功, {failed} 失败": ["info.address_result", "info.address_objects_processed"], "应用接口映射: {original} -> {mapped}": ["info.apply_interface_mapping", "info.interface_mapping_applied"], "黑洞路由 {id} 缺少目的网络，使用默认值 0.0.0.0/0": ["info.route_default_destination", "info.blackhole_route_default_destination"], "检查配置与目标设备的兼容性...": ["info.check_compatibility", "info.checking_compatibility"], "检查父目录: {dir}": ["ui.check_parent_dir", "info.checking_parent_dir"], "清理接口名称引号: {name}": ["info.clean_interface_name", "info.interface_name_quotes_removed"], "检测到 {count} 个兼容性问题": ["info.compatibility_issues", "warning.compatibility_issues_detected"], "成功生成目标配置文件": ["info.config_file_generated", "info.generate_target_success"], "成功读取配置文件，大小: {size} 字节": ["info.read_config_success", "info.config_file_read_success"], "转换报告已保存到: {file}": ["info.save_report", "info.conversion_report_saved"], "转换成功完成！": ["info.conversion_success", "info.conversion_succeeded"], "创建输出目录: {dir}": ["info.create_output_dir", "info.output_dir_created"], "创建新的routing节点": ["info.create_routing_node", "yang.create_routing_node"], "创建时间对象节点": ["info.created_time_range_node", "time_range.create_time_range_node"], "创建新区域: {name}": ["info.creating_new_zone", "security_zone_handler.create_new_zone"], "启用BFD: {value}": ["info.enable_bfd", "yang.enable_bfd"], "启用路由: {value}": ["info.enable_route", "yang.enable_route"], "成功生成加密配置文件": ["info.encrypt_file_success", "info.encrypted_file_generated"], "飞塔子接口处理完成，现在共有 {count} 个接口": ["info.subinterface_mapping_complete", "info.fortinet_subinterfaces_processed"], "找到现有区域: {name}": ["info.found_existing_zone", "security_zone_handler.found_existing_zone"], "获取配置模板路径: {model} {version}": ["info.get_template_path", "info.getting_template_path"], "输入文件: {file}": ["info.input_file", "main.input_file"], "接口映射: {raw_name} -> {mapped_name}": ["info.interface_mapped", "info.interface_mapping"], "接口映射成功: {original} -> {mapped}": ["info.interface_mapped_successfully", "zone_processor.zone_interface_mapping_success"], "接口映射处理完成，共 {count} 个接口": ["info.interface_mapping_complete", "info.interface_mapping_processing_complete"], "处理接口映射...": ["info.interface_mapping_init", "info.processing_interface_mapping"], "设置 {count} 个时间对象到传统生成器": ["info.legacy_set_time_ranges", "info.setting_time_ranges_legacy", "info.setting_time_ranges_to_legacy_generator"], "成功加载接口映射文件: {count} 条映射": ["info.mapping_file_loaded", "info.load_mapping_success"], "日志将写入文件: {file}": ["info.log_to_file", "info.log_file_created"], "映射文件: {file}": ["info.mapping_file", "main.mapping_file"], "检测到NAT模式配置": ["info.nat_mode_detected", "user.info.nat_mode_detected"], "没有找到地址对象": ["info.no_address_objects", "info.no_address_objects_found"], "没有接口需要处理": ["info.no_interfaces_to_process", "interface_processing_stage.no_interfaces"], "未发现NAT规则": ["info.no_nat_rules_found", "user.info.no_nat_rules_found"], "没有找到策略规则": ["info.no_policy_rules", "info.no_policy_rules_found"], "没有找到服务对象": ["info.no_service_objects", "info.no_service_objects_found"], "没有找到静态路由": ["info.no_static_routes", "info.no_static_routes_found"], "没有静态路由需要处理": ["info.no_static_routes_to_process", "static_route_processor.no_routes_to_process", "static_route_processing_stage.no_static_routes"], "未发现系统设置配置，默认使用NAT模式": ["info.no_system_settings_nat_mode", "user.info.no_system_settings_nat_mode"], "没有时间对象需要处理": ["info.no_time_ranges_to_process", "time_range_processor.no_objects_to_process", "time_range_processing_stage.no_schedule_objects"], "输出文件: {file}": ["info.output_file", "main.output_file"], "配置解析完成": ["info.parsing_complete", "info.parsing_config_complete", "user.info.parsing_config_complete"], "开始解析配置文件...": ["info.parsing_config", "info.parsing_config_start"], "策略规则处理结果: {success} 成功, {failed} 失败": ["info.policy_result", "info.policy_rules_processed"], "策略规则 {name} 转换成功": ["info.policy_rule_success", "info.policy_rule_converted_success"], "找到可能的模板文件: {path}": ["ui.found_template", "info.possible_template_found"], "处理地址对象...": ["info.process_address_objects", "info.processing_address_objects"], "处理飞塔子接口特殊映射...": ["info.process_fortinet_subinterfaces", "info.processing_fortinet_subinterfaces"], "处理策略规则...": ["info.process_policy_rules", "info.processing_policy_rules"], "处理服务对象...": ["info.process_service_objects", "info.processing_service_objects"], "处理静态路由...": ["info.process_static_routes", "info.processing_static_routes"], "处理静态路由 {id}: {dest} -> {gateway}": ["info.route_processing", "info.processing_static_route"], "静态路由处理结果: {success} 成功, {failed} 失败": ["info.routes_result", "info.static_routes_processed"], "已添加routing节点，XML: {xml}": ["info.routing_node_added", "yang.routing_node_added"], "在整个数据目录中查找: {dir}": ["ui.search_data_dir", "info.searching_data_dir"], "服务对象处理结果: {success} 成功, {failed} 失败": ["info.service_result", "info.service_objects_processed"], "服务对象处理完成，共 {total} 个，成功 {success} 个，失败 {failed} 个，跳过 {skipped} 个": ["user.info.service_objects_processed", "info.service_objects_processing_complete_with_mapping"], "设置管理距离: {distance}": ["info.set_admin_distance", "yang.set_admin_distance"], "设置黑洞路由": ["info.set_blackhole_route", "yang.set_blackhole_route"], "设置网关: {gateway}": ["info.set_gateway", "yang.set_gateway"], "设置下一跳: {hop}": ["info.set_next_hop", "yang.set_next_hop"], "设置路由目标网络: {destination}": ["info.set_route_destination", "yang.set_route_destination"], "开始转换配置文件": ["info.start_conversion", "main.conversion_start"], "开始生成NTOS XML配置": ["info.start_generate_ntos_xml", "info.starting_ntos_xml_generation"], "开始生成XML配置": ["info.start_generate_xml", "info.starting_xml_generation"], "开始处理接口配置": ["info.start_processing_interfaces", "interface_processing_stage.starting"], "未找到static节点，创建一个新的": ["info.static_node_not_found", "yang.static_node_not_found"], "静态路由处理完成，共 {total} 条，成功 {success} 条，失败 {failed} 条，跳过 {skipped} 条": ["user.info.static_routes_processed", "info.static_routes_processing_complete"], "添加static标签到routing节点": ["info.static_tag_added", "yang.static_tag_added"], "目标型号: {model}": ["info.target_model", "main.target_model"], "目标版本: {version}": ["info.target_version", "main.target_version"], "模板文件绝对路径: {path}": ["ui.template_path", "info.template_absolute_path"], "已创建模板目录: {dir}": ["ui.template_dir_created", "info.template_dir_created"], "透明模式配置 - 管理接口: {mgmt}, 桥接接口数量: {bridge_count}": ["info.transparent_mode_config", "user.info.transparent_mode_config"], "检测到透明模式配置": ["info.transparent_mode_detected", "user.info.transparent_mode_detected"], "更新VLAN子接口配置: {name}": ["yang.update_vlan_subinterface", "info.update_vlan_subinterface_config"], "更新现有地址对象: {name}": ["info.updating_existing_address", "xml_template_integration.update_existing_address_object"], "更新现有地址组: {name}": ["info.updating_existing_address_group", "xml_template_integration.update_existing_address_group"], "更新现有接口: {name}": ["info.updating_existing_interface", "interface_integrator.updated_existing_interface"], "更新现有服务对象: {name}": ["info.updating_existing_service", "xml_template_integration.update_existing_service_object"], "更新现有服务组: {name}": ["info.updating_existing_service_group", "xml_template_integration.update_existing_service_group"], "更新现有区域: {name}": ["info.updating_existing_zone", "interface_integrator.updated_existing_zone", "security_zone_handler.update_existing_zone", "xml_template_integration.update_existing_security_zone"], "用户日志将写入文件: {file}": ["info.user_log_to_file", "info.user_log_file_created"], "使用替代模板文件: {path}": ["ui.using_alt_template", "info.using_alternative_template"], "验证通过": ["info.verification_passed", "user.detail.validation_passed"], "VRF节点已创建": ["info.vrf_node_created", "xml_template_integration_stage.vrf_created"], "成功写入XML配置文件: {file}": ["info.xml_file_written", "info.write_xml_success"], "XML验证通过": ["info.xml_validation_passed", "info.xml_validation_success"], "配置文件已通过YANG模型验证": ["info.yang_validation_passed", "user.info.yang_validation_passed"], "区域接口映射完成": ["info.zone_mapping_complete", "info.zone_interface_mapping_complete"], "LAN接口": ["interface.type.lan", "interface_processing_stage.lan_role"], "物理接口": ["interface.type.physical", "interface_processing_stage.physical_interface"], "隧道接口": ["interface.type.tunnel", "interface_processing_stage.tunnel_interface"], "VLAN接口": ["interface.type.vlan", "interface_processing_stage.vlan_interface"], "WAN接口": ["interface.type.wan", "interface_processing_stage.wan_role"], "没有接口数据": ["interface_integrator.no_interface_data", "xml_template_integration_stage.debug_no_interface_data"], "成功转换: {count}个": ["user.stage.success_converted", "interface_processing_stage.converted_interfaces"], "DMZ区域": ["zone_processor.dmz_zone_desc", "interface_processing_stage.dmz_zone"], "转换失败: {count}个": ["user.stage.failed_converted", "interface_processing_stage.failed_interfaces"], "缺少接口映射": ["user.stage.missing_interface_mapping", "interface_processing_stage.missing_interface_mapping"], "跳过转换: {count}个": ["user.stage.skipped_converted", "interface_processing_stage.skipped_interfaces"], "信任区域": ["zone_processor.trust_zone_desc", "interface_processing_stage.trust_zone"], "非信任区域": ["zone_processor.untrust_zone_desc", "interface_processing_stage.untrust_zone"], "建议": ["issue.suggestion", "report.suggestion", "user.stage.suggestion"], "VLAN ID无效或其他兼容性问题": ["manual_config.interface.reason", "manual_config.reason.invalid_vlan", "reason.invalid_vlan_id_or_compatibility_issue"], "请检查VLAN ID是否在有效范围(1-4063)内，或其他接口配置是否有效": ["suggestion.check_vlan_id_range", "manual_config.interface.suggestion", "manual_config.suggestion.check_vlan"], "接口未在映射表中找到": ["manual_config.interface_zone.reason", "reason.interface_not_found_in_mapping", "manual_config.reason.unmapped_interface"], "请检查接口名称是否正确，或添加相应的接口映射": ["manual_config.suggestion.check_mapping", "manual_config.interface_zone.suggestion", "suggestion.check_interface_name_or_add_mapping"], "包含不支持的规则类型": ["reason.unsupported_rule_type", "manual_config.policy_rules.reason"], "检查策略类型并手动配置": ["suggestion.check_policy_type", "manual_config.policy_rules.suggestion"], "包含不支持的路由类型": ["reason.unsupported_route_type", "manual_config.static_routes.reason"], "检查路由类型并手动配置": ["suggestion.check_route_type", "manual_config.static_routes.suggestion"], "接口映射成功: {original} → {mapped}": ["nat.interface_mapping_success", "nat.interface_mapped_successfully"], "没有策略需要处理": ["policy.no_policies_to_process", "policy_processor.no_policies_to_process"], "策略 {policy_id} 处理失败: {error}": ["policy.processing_failed", "policy_processor.policy_processing_failed"], "生成NAT XML片段失败: {error}": ["policy_processor.nat_xml_failed", "policy_processor.nat_xml_generation_failed"], "时间对象缺少名称": ["time_range.missing_name", "reason.time_range_missing_name"], "不支持的协议类型: {protocol}": ["reason.unsupported_protocol", "service_processor.unsupported_protocol"], "转换统计": ["report.conversion_stats", "user.summary.conversion_stats"], "详情": ["report.detail", "stats.details"], "失败": ["stats.failed", "report.failed", "status.failed", "user.status.failed"], "忽略": ["stats.skipped", "report.ignored"], "成功": ["stats.success", "report.success", "status.success", "user.status.success"], "验证失败": ["report.validation_failed", "user.error.validation_failed", "user.detail.validation_failed"], "({count}个): {categories}": ["semantic.summary.info_issues_count", "semantic.summary.warning_issues_count"], "关键问题": ["severity.critical", "severity.critical_issues"], "优化建议": ["severity.info", "severity.info_issues"], "跳过": ["status.skipped", "user.status.skipped"], "转换完成": ["success.conversion_complete", "user.summary.conversion_complete"], "子元素数量: {count}": ["template_loader.child_count", "xml_template_integration.template_child_count"], "命名空间: '{namespace}'": ["template_loader.namespace", "xml_template_integration.template_namespace", "xml_template_integration.template_namespace_alt"], "根元素: 完整='{full}', 本地='{local}'": ["template_loader.root_element", "xml_template_integration.template_root_element", "xml_template_integration.template_root_element_alt"], "模式已加载": ["yang_loader.schema_loaded", "template_manager.schema_loaded_new"], "模板目录不存在: {dir}": ["ui.template_dir_not_exists", "warning.template_dir_not_exists"], "配置验证失败: {reason}": ["validation_failed", "user.config.validation_failed"], "地址对象转换": ["user.stage.address_processing", "user.detail.address_conversion"], "地址对象组转换": ["user.stage.address_group_processing", "user.detail.address_group_conversion"], "以及其他{count}个": ["user.detail.and_more", "user.stage.and_others"], "详细转换统计": ["user.detail.conversion_statistics", "user.log.detailed_conversion_statistics"], "检测到{mode}模式": ["user.stage.mode_detected", "user.detail.detected_mode"], "DNS转换": ["user.stage.dns_processing", "user.detail.dns_conversion"], "接口转换": ["user.stage.interface_processing", "user.detail.interface_conversion"], "无需处理": ["user.stage.no_processing_needed", "user.detail.no_processing_needed"], "无统计信息可用": ["user.log.no_statistics_available", "user.detail.no_statistics_available"], "操作模式检测": ["user.stage.operation_mode", "user.flow.stage.operation_mode", "user.stage.name.operation_mode", "user.detail.operation_mode_detection"], "策略规则转换": ["user.stage.policy_conversion", "user.detail.policy_conversion"], "服务对象转换": ["user.stage.service_processing", "user.detail.service_conversion"], "服务对象组转换": ["user.stage.service_group_processing", "user.detail.service_group_conversion"], "静态路由转换": ["user.stage.static_route_processing", "user.detail.static_route_conversion"], "时间对象转换": ["user.stage.time_range_processing", "user.detail.time_range_conversion"], "XML模板集成": ["user.stage.xml_integration", "user.detail.xml_integration", "user.stage.xml_template_integration", "user.flow.stage.xml_template_integration"], "区域转换": ["user.stage.zone_processing", "user.detail.zone_conversion"], "文件不存在: {file}": ["user.file.not_found", "user.error.file_not_exists"], "地址对象组处理": ["user.flow.stage.address_group_processing", "user.stage.name.address_group_processing"], "地址对象处理": ["user.flow.stage.address_processing", "user.stage.name.address_processing"], "DNS处理": ["user.flow.stage.dns_processing", "user.stage.name.dns_processing"], "配置加密": ["user.flow.stage.encryption", "user.stage.name.encryption"], "接口处理": ["user.flow.stage.interface_processing", "user.stage.name.interface_processing"], "策略规则处理": ["user.flow.stage.policy_processing", "user.stage.name.policy_processing"], "服务对象组处理": ["user.flow.stage.service_group_processing", "user.stage.name.service_group_processing"], "服务对象处理": ["user.flow.stage.service_processing", "user.stage.name.service_processing"], "静态路由处理": ["user.flow.stage.static_route_processing", "user.stage.name.static_route_processing"], "时间对象处理": ["user.flow.stage.time_range_processing", "user.stage.name.time_range_processing"], "XML配置生成": ["user.flow.stage.xml_generation", "user.stage.name.xml_generation"], "YANG模型验证": ["user.flow.stage.yang_validation", "user.stage.name.yang_validation"], "区域处理": ["user.flow.stage.zone_processing", "user.stage.name.zone_processing"], "配置迁移后续步骤": ["user.log.post_migration_steps", "user.suggestion.post_migration_steps"], "- {type} '{name}': {reason}": ["user.summary.failed_item_detail", "user.summary.skipped_item_detail"], "验证服务已初始化": ["validation_service.initialized", "validation_service.initialized_new"], "access-control标签命名空间设置成功": ["vlan.access_control.namespace.success", "yang.access_control_namespace_success"], "ip-mac-bind标签命名空间设置成功": ["yang.ipmac_namespace_success", "vlan.ip_mac_bind.namespace.success"], "monitor标签命名空间设置成功": ["vlan.monitor.namespace.success", "yang.monitor_namespace_success"], "无法映射接口 {device}，只设置网关: {gateway}": ["warning.cannot_map_interface_for_route", "yang.warning.cannot_map_interface_for_route"], "警告: 最终XML中没有找到服务对象节点": ["warning.generator_no_service_object_node", "warning.no_service_obj_node_in_final_xml"], "警告: 接口 {interface} 没有找到对应的映射，使用原名": ["warning.interface_no_mapping", "warning.interface_mapping_not_found"], "警告: 路由使用的接口 '{interface}' 没有映射关系，请检查接口映射": ["warning.interface_not_mapped", "warning.route_interface_not_mapped_check"], "消息中缺少参数: {param}": ["warning.missing_param", "warning.missing_param_fallback"], "警告: NTOS生成器不支持添加静态路由，已跳过": ["warning.no_static_routes_support", "warning.static_routes_not_supported"], "没有地址对象处理结果": ["xml_template_integration_stage.no_address_result", "xml_template_integration.no_address_object_result"], "没有DNS处理结果": ["xml_template_integration.no_dns_result", "xml_template_integration_stage.no_dns_result"], "没有服务对象处理结果": ["xml_template_integration_stage.no_service_result", "xml_template_integration.no_service_object_result"], "没有静态路由处理结果": ["xml_template_integration.no_static_route_result", "xml_template_integration_stage.no_static_route_result"], "没有时间对象处理结果": ["xml_template_integration.no_time_object_result", "xml_template_integration_stage.no_time_range_result"], "XML模板加载成功: model={model}, version={version}": ["refactored_wrapper.template_loaded_success", "xml_template_integration.template_loaded_success"], "未找到VRF节点，正在创建": ["xml_template_integration.vrf_not_found_creating", "xml_template_integration_stage.vrf_not_found_creating"], "XML命名空间验证: 实际='{actual}', 期望='{expected}'": ["xml_template_integration.xml_namespace_validation", "xml_template_integration.xml_namespace_validation_alt"], "XML优化失败: {error}": ["refactored_xml_integration.optimization_failed", "xml_template_integration.xml_optimization_failed"], "XML根元素验证通过: '{local}'": ["xml_template_integration.xml_root_validation_passed", "xml_template_integration.xml_root_validation_passed_alt"], "XML结构验证: 完整标签='{full}', 本地标签='{local}'": ["xml_template_integration.xml_structure_validation", "xml_template_integration.xml_structure_validation_alt"], "没有接口处理结果": ["zone_processing_stage.no_interface_result", "xml_template_integration_stage.no_interface_result"], "缺少模型或版本信息": ["template_loader.missing_model_version", "yang_validation_stage.missing_model_version"], "执行集成器: {name}": ["refactored_wrapper.executing_integrator", "refactored_xml_integration.executing_integrator"], "开始生成最终XML": ["refactored_xml_integration.xml_generation_start", "refactored_xml_integration.generating_final_xml"]}, "invalid_keys": ["Advanced Covid AI Ready", "Cython.Compiler.Main", "Found 10,000,000,000 copies of Covid32.exe", "Hello, <PERSON>!", "Importing advanced AI", "Mapping interface names...", "Updating interface nodes in running XML...", "[on blue]hello", "_CallbackFileWrapper__fp", "_compress.so", "_distutils_hack", "_report.json", "_temp_for_encryption.xml", "i18n:compress.all_attempts_failed", "i18n:compress.available_symbols", "i18n:compress.backed_up_existing_file", "i18n:compress.backup_startup_created", "i18n:compress.cannot_delete_file", "i18n:compress.chdir_for_preload", "i18n:compress.checking_lib_file", "i18n:compress.cleaned_temp_lib_dir", "i18n:compress.cleanup_temp_dir", "i18n:compress.container_dlopen_error", "i18n:compress.container_dlopen_failed", "i18n:compress.container_lib_loaded_via_dlopen", "i18n:compress.container_library_path", "i18n:compress.container_load_failed", "i18n:compress.container_load_library_failed", "i18n:compress.container_load_success_method1", "i18n:compress.container_minimal_preload", "i18n:compress.container_special_handling", "i18n:compress.created_system_lib_dir", "i18n:compress.created_system_symlink", "i18n:compress.critical_lib_missing", "i18n:compress.current_working_dir", "i18n:compress.decrypted_file_saved", "i18n:compress.decrypting_file", "i18n:compress.decryption_complete", "i18n:compress.deleted_existing_output", "i18n:compress.deleted_file", "i18n:compress.dependency_already_in_memory", "i18n:compress.dependency_already_loaded", "i18n:compress.dependency_loaded_successfully", "i18n:compress.encryption_success", "i18n:compress.error.all_load_methods_failed", "i18n:compress.error.call_real_api_failed", "i18n:compress.error.cannot_delete_output", "i18n:compress.error.checking_lib_permissions", "i18n:compress.error.checking_permissions", "i18n:compress.error.cleanup_temp_dir", "i18n:compress.error.container_detection_failed", "i18n:compress.error.create_config_tar_gz", "i18n:compress.error.create_tar_file", "i18n:compress.error.creating_symlinks", "i18n:compress.error.creating_system_lib_dir", "i18n:compress.error.critical_lib_missing", "i18n:compress.error.decryption_failed", "i18n:compress.error.during_decryption", "i18n:compress.error.encrypted_file_invalid", "i18n:compress.error.encrypted_file_not_found", "i18n:compress.error.encryption_abort", "i18n:compress.error.encryption_failed", "i18n:compress.error.encryption_process", "i18n:compress.error.global_cleanup_failed", "i18n:compress.error.input_dir_not_exists", "i18n:compress.error.input_file_not_exists", "i18n:compress.error.lib_not_exists", "i18n:compress.error.lib_not_file", "i18n:compress.error.list_symbols_failed", "i18n:compress.error.load_lib_failed", "i18n:compress.error.load_lib_mode_failed", "i18n:compress.error.load_main_library_failed", "i18n:compress.error.main_lib_not_exists", "i18n:compress.error.missing_critical_libs", "i18n:compress.error.missing_main_function", "i18n:compress.error.output_file_invalid", "i18n:compress.error.preload_libs_failed", "i18n:compress.error.preparing_container_loading", "i18n:compress.error.preparing_library_files", "i18n:compress.error.process_tar_gz_failed", "i18n:compress.error.running_ldconfig", "i18n:compress.error.setting_ld_library_path", "i18n:compress.error.tar_command_failed", "i18n:compress.error.tar_gz_not_exists", "i18n:compress.error.unknown_action", "i18n:compress.error.version_file_invalid", "i18n:compress.error.version_file_not_exists", "i18n:compress.error.version_file_not_found", "i18n:compress.fixing_lib_permissions", "i18n:compress.found_dependency", "i18n:compress.global_cleanup_complete", "i18n:compress.init_complete", "i18n:compress.init_env", "i18n:compress.ld_library_path_set", "i18n:compress.ld_preload_set", "i18n:compress.ldconfig_executed", "i18n:compress.ldconfig_executed_after_preload", "i18n:compress.ldconfig_refreshed", "i18n:compress.lib_file_permissions", "i18n:compress.lib_loaded_successfully", "i18n:compress.lib_not_found", "i18n:compress.listing_available_symbols", "i18n:compress.load_main_lib_success", "i18n:compress.load_method_failed", "i18n:compress.load_with_relative_path_failed", "i18n:compress.loading_main_library", "i18n:compress.loading_with_absolute_path", "i18n:compress.main_function_verified", "i18n:compress.main_lib_loaded_successfully", "i18n:compress.method1_failed", "i18n:compress.preloaded_lib", "i18n:compress.preparing_container_loading", "i18n:compress.preparing_library_files", "i18n:compress.ran_ldconfig", "i18n:compress.restore_dir_after_preload", "i18n:compress.retry_load_main_lib", "i18n:compress.setting_ld_preload", "i18n:compress.symlink_already_exists", "i18n:compress.tar_gz_processed", "i18n:compress.traditional_load_completely_failed", "i18n:compress.trying_container_method1_rpath", "i18n:compress.trying_load_method", "i18n:compress.trying_relative_path", "i18n:compress.trying_to_load_lib", "i18n:compress.using_crypto_dir", "i18n:compress.using_crypto_lib", "i18n:compress.using_crypto_libs_path", "i18n:compress.using_lib_dir", "i18n:compress.using_version_file", "i18n:debug.added_valid_service_object", "i18n:debug.adding_service_objects_to_ntos_generator", "i18n:debug.address_object_detail", "i18n:debug.checking_service_groups_data", "i18n:debug.complex_tcp_ports_using_destination", "i18n:debug.complex_udp_ports_using_destination", "i18n:debug.detected_full_port_range", "i18n:debug.final_service_set_in_xml", "i18n:debug.final_service_sets_count", "i18n:debug.fixed_legacy_generator_added_icmp", "i18n:debug.fixed_legacy_generator_added_ip_protocol", "i18n:debug.fixed_legacy_generator_added_tcp_ports", "i18n:debug.fixed_legacy_generator_added_udp_ports", "i18n:debug.fixed_legacy_generator_final_xml_service_sets_count", "i18n:debug.fixed_legacy_generator_keeping_service_obj_node", "i18n:debug.fixed_legacy_generator_processing_service_objects", "i18n:debug.fixed_legacy_generator_service_added_success", "i18n:debug.fixed_legacy_generator_service_missing_ports", "i18n:debug.fixed_legacy_generator_service_object_detail", "i18n:debug.fixed_legacy_generator_service_port_config", "i18n:debug.fixed_legacy_generator_service_processing_failed", "i18n:debug.fixed_legacy_generator_service_protocol", "i18n:debug.fixed_legacy_generator_start_processing_service", "i18n:debug.fixed_legacy_generator_xml_service_set", "i18n:debug.fixed_legacy_generator_xml_service_sets_count", "i18n:debug.fortigate_av_profile_detected", "i18n:debug.fortigate_ips_sensor_detected", "i18n:debug.improved_legacy_generator_added_description", "i18n:debug.improved_legacy_generator_added_icmp", "i18n:debug.improved_legacy_generator_added_ip_protocol", "i18n:debug.improved_legacy_generator_complex_port_range_conversion", "i18n:debug.improved_legacy_generator_final_xml_service_sets_count", "i18n:debug.improved_legacy_generator_keeping_service_obj_node", "i18n:debug.improved_legacy_generator_port_range_conversion", "i18n:debug.improved_legacy_generator_port_range_normalization", "i18n:debug.improved_legacy_generator_service_processed_success", "i18n:debug.improved_legacy_generator_xml_service_set", "i18n:debug.improved_legacy_generator_xml_service_sets_count", "i18n:debug.legacy_generator_processing_service_objects", "i18n:debug.legacy_generator_service_object_check", "i18n:debug.legacy_generator_service_object_detail", "i18n:debug.legacy_generator_service_object_details", "i18n:debug.legacy_generator_service_objects_count", "i18n:debug.legacy_generator_set_service_mappings", "i18n:debug.legacy_policy_rule_converted_successfully", "i18n:debug.loaded_fortigate_service_mapping", "i18n:debug.loaded_ntos_builtin_services", "i18n:debug.loaded_predefined_service_mapping", "i18n:debug.multiline_content_end", "i18n:debug.multiline_content_start", "i18n:debug.multiple_tcp_ports_using_first", "i18n:debug.multiple_udp_ports_using_first", "i18n:debug.ntos_generator_set_service_mappings", "i18n:debug.policy_service_mapping_applied", "i18n:debug.port_preserve_detected", "i18n:debug.predefined_service_mapping_found", "i18n:debug.preparing_service_stats_calculation", "i18n:debug.processing_address_objects_count", "i18n:debug.processing_service_objects_count", "i18n:debug.service_auto_infer_protocol", "i18n:debug.service_converted_count", "i18n:debug.service_failed_count", "i18n:debug.service_group_member_found_directly", "i18n:debug.service_group_member_found_via_mapping", "i18n:debug.service_group_member_using_mapped_name", "i18n:debug.service_group_member_validated_via_mapping", "i18n:debug.service_group_processor_no_mappings", "i18n:debug.service_group_processor_received_mappings", "i18n:debug.service_groups_data_empty", "i18n:debug.service_groups_data_length", "i18n:debug.service_groups_data_sample", "i18n:debug.service_mapping_relationship", "i18n:debug.service_mapping_relationships_found", "i18n:debug.service_mapping_relationships_saved", "i18n:debug.service_object_details", "i18n:debug.service_objects_processing_details", "i18n:debug.service_objects_stats_calculated", "i18n:debug.service_protocol_auto_corrected_tcp", "i18n:debug.service_protocol_auto_corrected_tcp_udp", "i18n:debug.service_protocol_auto_corrected_udp", "i18n:debug.service_protocol_correction", "i18n:debug.service_set_in_xml", "i18n:debug.service_sets_count_after_adding", "i18n:debug.service_skipped_count", "i18n:debug.set_translator", "i18n:debug.setting_service_objects_to_legacy_generator", "i18n:debug.start_adding_service_objects_to_xml", "i18n:debug.using_converted_service_objects_for_validation", "i18n:debug.using_original_service_objects_for_validation", "i18n:debug.using_service_mapping_for_policies", "i18n:debug.valid_service_objects_count", "i18n:error.address_group_conversion_failed", "i18n:error.address_object_conversion_failed", "i18n:error.all_physical_interfaces_unmapped", "i18n:error.config_file_read_failed", "i18n:error.conversion_failed", "i18n:error.copy_file_failed", "i18n:error.create_dir_failed", "i18n:error.create_user_log_dir_failed", "i18n:error.duplicate_policy_id", "i18n:error.duplicate_service_object", "i18n:error.edit_without_config", "i18n:error.encrypt_error", "i18n:error.encryption_error", "i18n:error.encryption_failed", "i18n:error.end_without_config", "i18n:error.enhanced_policy_processing_failed", "i18n:error.extract_interfaces_failed", "i18n:error.extraction_failed", "i18n:error.failed_to_integrate_nat_rules", "i18n:error.failed_to_integrate_security_policies", "i18n:error.failed_to_load_xml", "i18n:error.file_empty", "i18n:error.file_not_exists", "i18n:error.file_not_exists_path", "i18n:error.file_read_failed", "i18n:error.fix_xml_namespaces_failed", "i18n:error.flush_log_handler_failed", "i18n:error.init_translator_failed", "i18n:error.interface_mapping_validation_failed", "i18n:error.interface_model_validation_failed", "i18n:error.invalid_fortigate_config", "i18n:error.invalid_fortigate_config_structure", "i18n:error.invalid_interface_mapping", "i18n:error.invalid_ip_address", "i18n:error.invalid_subnet_mask", "i18n:error.ipv6_static_route_processing_error", "i18n:error.json_print_failed", "i18n:error.legacy_policy_rule_conversion_failed", "i18n:error.mapping_file_load_failed", "i18n:error.mask_conversion_failed", "i18n:error.missing_config_version", "i18n:error.missing_interface_config", "i18n:error.missing_parameters", "i18n:error.next_without_edit", "i18n:error.ntos_generation_failed", "i18n:error.output_file_invalid", "i18n:error.parsed_data_not_dict", "i18n:error.parsing_config_failed", "i18n:error.parsing_failed_with_error", "i18n:error.policy_rule_conversion_error", "i18n:error.policy_rule_conversion_failed", "i18n:error.possible_unbalanced_nesting", "i18n:error.read_json_file_failed", "i18n:error.read_text_file_failed", "i18n:error.semantic_verification_failed", "i18n:error.service_group_processing_error", "i18n:error.service_object_conversion_failed", "i18n:error.static_route_processing_error", "i18n:error.syntax_errors_detected", "i18n:error.template_file_not_exists", "i18n:error.template_not_found", "i18n:error.time_range_conversion_failed", "i18n:error.transparent_mode_processing_failed", "i18n:error.unable_to_detect_version", "i18n:error.unclosed_config_blocks", "i18n:error.unclosed_edit_blocks", "i18n:error.unhandled_exception", "i18n:error.unknown_error", "i18n:error.unsupported_fortigate_version", "i18n:error.vendor_not_supported", "i18n:error.verification_failed", "i18n:error.vlan_extraction_failed", "i18n:error.write_interface_info_failed", "i18n:error.write_json_file_failed", "i18n:error.write_text_file_failed", "i18n:error.xml_validation_error", "i18n:error.xml_validation_failed", "i18n:format.data_object_placeholder", "i18n:format.interface_placeholder", "i18n:format.json_data_placeholder", "i18n:format.object_placeholder", "i18n:fortigate.address_interface_unsupported", "i18n:fortigate.address_type_support", "i18n:fortigate.address_type_unsupported", "i18n:fortigate.warning.fixedport_not_supported", "i18n:fortigate.warning.groups_not_supported", "i18n:fortigate.warning.unsupported_role", "i18n:fortigate.warning.users_not_supported", "i18n:info.adapting_service_processor_result_format", "i18n:info.add_interface_with_json", "i18n:info.adding_dns_config", "i18n:info.adding_static_routes", "i18n:info.adding_time_ranges", "i18n:info.address_group_converted", "i18n:info.address_groups_processed", "i18n:info.address_groups_processing_complete", "i18n:info.address_object_converted", "i18n:info.address_objects_processed", "i18n:info.address_objects_processing_complete", "i18n:info.address_type_detected_from_attributes", "i18n:info.address_type_not_specified", "i18n:info.address_type_specified", "i18n:info.all_predefined_services_detected", "i18n:info.all_zone_interfaces_mapped", "i18n:info.blackhole_route_default_destination", "i18n:info.checking_compatibility", "i18n:info.checking_parent_dir", "i18n:info.cleanup_temp_dir", "i18n:info.config_dir_found", "i18n:info.config_file_read_success", "i18n:info.conversion_succeeded", "i18n:info.converting_single_interface_to_list", "i18n:info.converting_zones_dict_to_list", "i18n:info.copy_config_dir", "i18n:info.copy_xml_to_running", "i18n:info.copy_xml_to_startup", "i18n:info.create_directory", "i18n:info.create_new_config_dir", "i18n:info.create_output_dir", "i18n:info.create_user_log_dir", "i18n:info.current_working_dir", "i18n:info.delete_existing_output", "i18n:info.detected_container_environment", "i18n:info.detected_dev_environment", "i18n:info.detected_fortigate_version", "i18n:info.directory_created", "i18n:info.dmz_interface_set_required", "i18n:info.encrypt_start", "i18n:info.encrypted_file_generated", "i18n:info.encryption_success", "i18n:info.engine_debug_log_created", "i18n:info.engine_user_log_created", "i18n:info.enhanced_policy_processing_complete", "i18n:info.error_report_saved", "i18n:info.extract_interfaces_from_config", "i18n:info.extracting_interfaces_from_config", "i18n:info.extracting_zones", "i18n:info.extraction_start", "i18n:info.extraction_success", "i18n:info.fallback_to_legacy", "i18n:info.falling_back_to_legacy_service_processing", "i18n:info.file_copied", "i18n:info.final_interface_mapping_file_saved", "i18n:info.fixing_xml_namespaces", "i18n:info.fortinet_subinterfaces_processed", "i18n:info.found_template_file", "i18n:info.found_template_files", "i18n:info.getting_template_path", "i18n:info.input_file", "i18n:info.interface_info_written", "i18n:info.interface_mapped", "i18n:info.interface_mapping_applied", "i18n:info.interface_mapping_complete", "i18n:info.interface_mapping_table", "i18n:info.interface_mapping_validation_passed", "i18n:info.interface_name_quotes_removed", "i18n:info.interface_zone_assigned", "i18n:info.interfaces_extracted", "i18n:info.json_file_saved", "i18n:info.language_set", "i18n:info.legacy_policy_rules_processing_complete", "i18n:info.min_version_requirement", "i18n:info.nat_mode_detected", "i18n:info.nat_rules_added_to_xml", "i18n:info.nat_rules_processing_complete", "i18n:info.no_address_groups_found", "i18n:info.no_address_objects_found", "i18n:info.no_compatibility_issues", "i18n:info.no_interfaces_or_mappings_to_validate", "i18n:info.no_nat_rules_found", "i18n:info.no_nat_rules_to_process", "i18n:info.no_policy_rules_found", "i18n:info.no_service_groups_found", "i18n:info.no_service_objects_found", "i18n:info.no_static_routes_found", "i18n:info.no_system_settings_nat_mode", "i18n:info.no_time_ranges_found", "i18n:info.output_dir_created", "i18n:info.output_file", "i18n:info.parsing_config_complete", "i18n:info.parsing_config_start", "i18n:info.policy_rule_converted_success", "i18n:info.policy_rules_processed", "i18n:info.policy_rules_processing_complete", "i18n:info.possible_template_found", "i18n:info.pppoe_interfaces_filtered", "i18n:info.processing_address_groups", "i18n:info.processing_address_objects", "i18n:info.processing_fortinet_subinterfaces", "i18n:info.processing_interface_mapping", "i18n:info.processing_ipv4_static_routes", "i18n:info.processing_ipv6_static_route", "i18n:info.processing_ipv6_static_routes", "i18n:info.processing_policy_rule", "i18n:info.processing_policy_rules", "i18n:info.processing_service_group", "i18n:info.processing_service_groups", "i18n:info.processing_service_objects", "i18n:info.processing_static_route", "i18n:info.processing_static_routes", "i18n:info.processing_time_ranges", "i18n:info.processing_transparent_mode", "i18n:info.processing_zone_interface_mapping", "i18n:info.processing_zone_interfaces", "i18n:info.received_enhanced_generator_output", "i18n:info.received_legacy_generator_output", "i18n:info.removed_invalid_output", "i18n:info.report_path", "i18n:info.report_saved", "i18n:info.route_destination_converted", "i18n:info.saving_xml_config", "i18n:info.searching_data_dir", "i18n:info.security_policies_added_to_xml", "i18n:info.service_group_member_mapped", "i18n:info.service_groups_processed", "i18n:info.service_groups_processing_complete", "i18n:info.service_mapping_saved", "i18n:info.service_object_converted", "i18n:info.service_objects_processed", "i18n:info.service_objects_processing_complete", "i18n:info.service_protocol_auto_detected", "i18n:info.service_protocol_specified", "i18n:info.setting_nat_rules", "i18n:info.setting_time_ranges_legacy", "i18n:info.skipping_unmapped_interface", "i18n:info.skipping_unmapped_zone_interface", "i18n:info.start_adding_nat_rules_to_xml", "i18n:info.start_adding_policy_rules_to_xml", "i18n:info.start_conversion", "i18n:info.start_encryption", "i18n:info.start_processing_nat_rules", "i18n:info.start_verification", "i18n:info.start_verify_vendor_config", "i18n:info.static_routes_processed", "i18n:info.static_routes_processing_complete", "i18n:info.subinterface_direct_mapped", "i18n:info.subinterface_generated_mapping", "i18n:info.subinterface_mapped", "i18n:info.subinterface_parent_validated", "i18n:info.target_model", "i18n:info.target_version", "i18n:info.temp_directory_created", "i18n:info.temp_directory_deleted", "i18n:info.template_absolute_path", "i18n:info.template_dir", "i18n:info.template_dir_created", "i18n:info.text_file_saved", "i18n:info.time_range_converted", "i18n:info.time_ranges_processed", "i18n:info.time_ranges_processing_complete", "i18n:info.tool_started", "i18n:info.transparent_mode_config", "i18n:info.transparent_mode_detected", "i18n:info.undefined_zone_interface_set_required", "i18n:info.user_interrupted", "i18n:info.user_log_file_created", "i18n:info.using_alternative_template", "i18n:info.using_config_root", "i18n:info.using_current_directory", "i18n:info.using_default_language", "i18n:info.using_enhanced_policy_processor", "i18n:info.using_extended_generator_for_dns", "i18n:info.using_legacy_architecture", "i18n:info.using_new_architecture", "i18n:info.using_parser", "i18n:info.using_service_mapping_for_policy_rules", "i18n:info.using_service_processor_with_mapping", "i18n:info.using_standard_generator", "i18n:info.using_template", "i18n:info.using_unfixed_xml_file", "i18n:info.validating_interface_mapping", "i18n:info.validating_xml", "i18n:info.validation_report_saved", "i18n:info.verification_passed", "i18n:info.verification_success", "i18n:info.verify_fortigate_config", "i18n:info.verifying_config_before_conversion", "i18n:info.xml_config_saved_with_fixed_namespaces", "i18n:info.xml_file_written", "i18n:info.xml_validation_passed", "i18n:info.zone_extracted", "i18n:info.zone_interface_mapped", "i18n:info.zone_interface_mapping_complete", "i18n:info.zones_extracted", "i18n:info.zones_found", "i18n:info.zones_processing_complete", "i18n:key", "i18n:multiline.buffer_content", "i18n:multiline.certificate_content", "i18n:multiline.private_key_content", "i18n:nat.statistics_summary", "i18n:user.error.conversion_failed", "i18n:user.error.duplicate_service_object", "i18n:user.error.file_not_exists", "i18n:user.error.interface_mapping_validation_failed", "i18n:user.error.interface_model_validation_failed", "i18n:user.error.parsing_config_failed", "i18n:user.error.target_config_generation_failed", "i18n:user.error.template_not_found", "i18n:user.error.transparent_mode_processing_failed", "i18n:user.error.vendor_not_supported", "i18n:user.error.yang_validation_failed", "i18n:user.info.address_groups_processed", "i18n:user.info.address_objects_processed", "i18n:user.info.config_file_generated", "i18n:user.info.config_file_read_success", "i18n:user.info.dns_config_added", "i18n:user.info.encrypted_file_generated", "i18n:user.info.interfaces_extracted", "i18n:user.info.mapping_file_loaded", "i18n:user.info.nat_mode_detected", "i18n:user.info.nat_rules_added", "i18n:user.info.no_nat_rules_found", "i18n:user.info.no_system_settings_nat_mode", "i18n:user.info.parsing_config_complete", "i18n:user.info.parsing_config_start", "i18n:user.info.policy_rules_processed", "i18n:user.info.predefined_services_detected", "i18n:user.info.service_groups_processed", "i18n:user.info.service_objects_processed", "i18n:user.info.start_parsing", "i18n:user.info.static_routes_processed", "i18n:user.info.time_ranges_processed", "i18n:user.info.transparent_mode_config", "i18n:user.info.transparent_mode_detected", "i18n:user.info.yang_validation_passed", "i18n:user.info.zones_extracted", "i18n:user.summary.address_groups_processed", "i18n:user.summary.addresses_processed", "i18n:user.summary.compatibility_issue", "i18n:user.summary.compatibility_issues", "i18n:user.summary.conversion_complete", "i18n:user.summary.conversion_stats", "i18n:user.summary.conversion_summary", "i18n:user.summary.conversion_time", "i18n:user.summary.failed_item_detail", "i18n:user.summary.failed_items_detail", "i18n:user.summary.interfaces_processed", "i18n:user.summary.manual_config_item", "i18n:user.summary.manual_config_needed", "i18n:user.summary.nat_rules_processed", "i18n:user.summary.no_compatibility_issues", "i18n:user.summary.no_manual_config_needed", "i18n:user.summary.no_unconverted_items", "i18n:user.summary.policies_processed", "i18n:user.summary.routes_processed", "i18n:user.summary.service_groups_processed", "i18n:user.summary.services_processed", "i18n:user.summary.skipped_item_detail", "i18n:user.summary.skipped_items_detail", "i18n:user.summary.time_ranges_processed", "i18n:user.summary.total_items_processed", "i18n:user.summary.unconverted_item", "i18n:user.summary.unconverted_items", "i18n:user.summary.zones_processed", "i18n:user.warning.all_physical_interfaces_unmapped", "i18n:user.warning.compatibility_issues_detected", "i18n:user.warning.nat_not_supported_in_version", "i18n:user.warning.route_interface_not_mapped_check", "i18n:user.warning.total_unmapped_zone_interfaces", "i18n:user.warning.unsupported_protocol_ntos", "i18n:user.warning.zone_unmapped_interfaces", "i18n:warning.all_physical_interfaces_unmapped", "i18n:warning.cannot_parse_port_range", "i18n:warning.cannot_process_zone_interfaces", "i18n:warning.cannot_process_zones", "i18n:warning.compatibility_issue_detail", "i18n:warning.compatibility_issues_detected", "i18n:warning.compatibility_issues_found", "i18n:warning.config_dir_not_found", "i18n:warning.failed_to_add_nat_rules_to_xml", "i18n:warning.failed_to_add_security_policies_to_xml", "i18n:warning.failed_to_convert_policy_rule", "i18n:warning.format_error", "i18n:warning.generator_empty_service_node_removal_failed", "i18n:warning.generator_no_service_object_node", "i18n:warning.generator_service_object_node_empty", "i18n:warning.interface_data_not_list", "i18n:warning.interface_mapping_not_found", "i18n:warning.interface_missing_raw_name", "i18n:warning.interface_missing_raw_name_field", "i18n:warning.interface_not_string", "i18n:warning.invalid_port_range", "i18n:warning.ipv6_route_missing_destination", "i18n:warning.ipv6_route_no_necessary_config", "i18n:warning.legacy_policy_rule_missing_name", "i18n:warning.missing_param", "i18n:warning.missing_params", "i18n:warning.nat_rule_skipped_version_not_supported", "i18n:warning.nat_rules_not_supported", "i18n:warning.new_architecture_failed", "i18n:warning.no_interfaces_to_process", "i18n:warning.no_service_obj_node_in_final_xml", "i18n:warning.path_corrected", "i18n:warning.port_out_of_range", "i18n:warning.pppoe_interface_filtered", "i18n:warning.pppoe_missing_credentials", "i18n:warning.route_interface_not_mapped", "i18n:warning.route_missing_destination", "i18n:warning.semantic_verification", "i18n:warning.service_group_member_not_found", "i18n:warning.service_group_missing_name", "i18n:warning.service_group_no_members", "i18n:warning.service_group_no_valid_members", "i18n:warning.service_missing_protocol_and_ports", "i18n:warning.service_processor_failed", "i18n:warning.skip_non_dict_service_object", "i18n:warning.skip_service_object_missing_name", "i18n:warning.skipping_address_group_empty_members", "i18n:warning.skipping_address_group_missing_name", "i18n:warning.skipping_address_invalid_subnet", "i18n:warning.skipping_address_missing_ip_range", "i18n:warning.skipping_address_missing_name", "i18n:warning.skipping_address_missing_subnet", "i18n:warning.skipping_address_type_not_explicitly_supported", "i18n:warning.skipping_address_unsupported_type", "i18n:warning.skipping_interface_associated_address", "i18n:warning.skipping_invalid_interface_with_reason", "i18n:warning.skipping_non_dict_zone", "i18n:warning.skipping_policy_rule_missing_fields", "i18n:warning.skipping_policy_rule_unmapped_interfaces", "i18n:warning.skipping_service_missing_name", "i18n:warning.skipping_service_missing_port_range", "i18n:warning.skipping_service_missing_protocol_and_ports", "i18n:warning.skipping_service_unsupported_protocol", "i18n:warning.skipping_unsupported_protocol_ntos", "i18n:warning.static_routes_not_supported", "i18n:warning.subinterface_missing_parent_or_vlanid_skipped", "i18n:warning.subinterface_parent_not_mapped", "i18n:warning.subinterface_parent_not_mapped_skipped", "i18n:warning.tcp_port_range_invalid", "i18n:warning.template_dir_not_exists", "i18n:warning.time_ranges_not_supported", "i18n:warning.total_unmapped_zone_interfaces", "i18n:warning.translation_format_error", "i18n:warning.translation_missing_param", "i18n:warning.translation_missing_params", "i18n:warning.udp_port_range_invalid", "i18n:warning.zone_data_not_list", "i18n:warning.zone_interface_not_mapped", "i18n:warning.zone_interfaces_not_list", "i18n:warning.zone_unmapped_interfaces", "i18n:warning.zones_not_list"]}}