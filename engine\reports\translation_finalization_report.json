{"statistics": {"original_keys": 8631, "final_keys": 7788, "removed_keys": 974, "invalid_keys": 974, "improved_keys": 1688}, "removed_keys": [{"key": "address_group_processor.xml_fragment_failed", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "address_group_processor.xml_fragment_success", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "address_processor.xml_fragment_failed", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "address_processor.xml_fragment_success", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "address_processor.xml_generated", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "config_converter.xml_generation_success", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "dns_generator.xml_generated", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "encryption_stage.executing_encryption", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "encryption_stage.execution_error", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "encryption_stage.xml_file_not_found", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "enhanced_yang_generator.xml_save_error", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "enhanced_yang_generator.xml_saved", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "error.json_print_failed", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "error.json_serialization_failed", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "error.source_interface_not_exists", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "error.source_interface_not_physical", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "error.xml_generation_failed", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "error.xml_generation_produced_invalid_output", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "error.xml_repair_produced_invalid_output", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "error.xml_string_too_short", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "error.xml_structure_damaged_during_fixing", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "error.xml_validation_error", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "error.xml_validation_failed", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "error.xml_validation_failed_message", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "error.xmlns_repair_lost_significant_content", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "format.json_data_placeholder", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "fortigate_policy_stage.xml_integration_data_prepared", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "fortigate_policy_stage.xml_integration_preparation_failed", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "fortigate_policy_stage.xml_preparation_failed", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "fortigate_strategy.validation.source_found", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "fortigate_strategy.validation.source_not_found", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "fortigate_strategy.validation.source_zone_resolved", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "importing_advanced_ai", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "info.json_file_saved", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "info.xml_config_saved_with_fixed_namespaces", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "info.xml_file_written", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "info.xml_generation_complete", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "info.xml_generation_complete_with_validation", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "info.xml_validation_passed", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "interface.source", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "interface_processing_stage.xml_fragment_length", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "interface_processing_stage.xml_generation_completed", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "interface_processing_stage.xml_generation_started", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "interface_processor.source_interface_exists", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "interface_processor.source_interface_validation_default", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "interface_processor.source_interface_validation_error", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "interface_processor.source_interface_validation_failed", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "interface_processor.xml_fragment_generated", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "interface_processor.xml_fragment_generation_failed", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "key", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "misc.debug.python", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "misc.info.json_fetch_failed", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "misc.info.test_failed_does", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "misc.mapping_interface_names...", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "misc.message.example_config", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "misc.message.exec_prefix_option", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "misc.message.gnome_examples_exception", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "misc.message.json_error_occurred", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "misc.message.pygments_lexer_specified", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "misc.message.pyopenssl_module_missing", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "misc.message.python_option_must", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "misc.message.python_version_warning", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "misc.message.some_build_dependencies", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "misc.message.some_compile_link", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "misc.message.test_failed", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "misc.message.warning_testing_via", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "misc.message.xml_version_encoding", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "misc.message.xml_version_encoding_1", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "misc.message.xmlns", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "misc.message.xmlrpc_request_failed", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "misc.updating_interface_nodes_in_running_xml...", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "on_blue_hello", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "pipeline_manager.executing_stage", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "pkg_resources", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "policy.source_interface_mapped", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "policy.source_interface_mapped_with_mapping", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "policy.source_zone_mapped", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "policy.source_zone_mapped_with_mapping", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "policy_processor.source_fortigate_logical_zone_accepted", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "policy_processor.source_interface_found_in_mapping", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "policy_processor.source_interface_mapping_found", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "policy_processor.source_interface_not_found_and_not_zone", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "policy_processor.source_interface_not_in_mapping", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "policy_processor.source_zone_identified", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "policy_processor.source_zone_resolved_to_interfaces", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "pydoc", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "reason.source_interface_not_found", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "reason.source_interface_not_physical", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "report.source_file", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "service.builtin.socks.description", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "service.builtin.socks_udp.description", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "service_group_processor.xml_fragment_failed", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "service_group_processor.xml_fragment_success", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "setuptools", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "static_route_processor.xml_fragment_failed", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "static_route_processor.xml_fragment_success", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "static_route_processor.xml_fragment_with_groups", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "time_range_processor.xml_fragment_failed", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "time_range_processor.xml_fragment_success", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "twice_nat44_error.user_message.xml_generation_error", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "twice_nat44_performance.execution_completed", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "twice_nat44_performance.execution_failed", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "user.flow.stage.xml_generation", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "user.stage.xml_integration", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "validator.execute_windows_command", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "validator.execute_yanglint_command", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "warning.xml_validation_skipped", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "xml_integrator.some_policies_failed", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "xml_template_integration.xml_integration_complete", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "xml_template_integration.xml_integration_failed", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "xml_template_integration.xml_namespace_validation", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "xml_template_integration.xml_namespace_validation_passed", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "xml_template_integration.xml_optimization_complete", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "xml_template_integration.xml_optimization_removed_duplicates", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "xml_template_integration.xml_root_validation_passed", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "xml_template_integration.xml_structure_validation", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "xml_template_integration.xml_structure_validation_complete", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "xml_template_integration.xml_validation_complete", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "xml_template_integration.xml_validation_failed", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "xml_template_integration_stage.xml_basic_generation_failed", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "xml_template_integration_stage.xml_basic_generation_success", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "xml_template_integration_stage.xml_fallback_generation_success", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "xml_template_integration_stage.xml_formatted_generation_failed", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "xml_template_integration_stage.xml_formatted_generation_success", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "xml_template_integration_stage.xml_generated", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "xml_template_integration_stage.xml_generation_debug", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "xml_template_integration_stage.xml_generation_null_root", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "xml_template_integration_stage.xml_generation_starting", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "xml_template_integration_stage.xml_generation_stats", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "xml_template_integration_stage.xml_validated_optimized", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "yang.xml_generation_complete", "reason": "invalid_key_format", "language": "zh-CN"}, {"key": "Found 10,000,000,000 copies of Covid32.exe", "reason": "invalid_key_format", "language": "en-US"}, {"key": "Mapping interface names...", "reason": "invalid_key_format", "language": "en-US"}, {"key": "Updating interface nodes in running XML...", "reason": "invalid_key_format", "language": "en-US"}, {"key": "_compress.so", "reason": "invalid_key_format", "language": "en-US"}, {"key": "_report.json", "reason": "invalid_key_format", "language": "en-US"}, {"key": "_temp_for_encryption.xml", "reason": "invalid_key_format", "language": "en-US"}, {"key": "about_service", "reason": "invalid_key_format", "language": "en-US"}, {"key": "address_group_processor.xml_fragment_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "address_group_processor.xml_fragment_success", "reason": "invalid_key_format", "language": "en-US"}, {"key": "address_processor.xml_fragment_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "address_processor.xml_fragment_success", "reason": "invalid_key_format", "language": "en-US"}, {"key": "address_processor.xml_generated", "reason": "invalid_key_format", "language": "en-US"}, {"key": "api_documentation", "reason": "invalid_key_format", "language": "en-US"}, {"key": "app_name", "reason": "invalid_key_format", "language": "en-US"}, {"key": "compress.container_subprocess_test_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "compress.container_subprocess_test_success", "reason": "invalid_key_format", "language": "en-US"}, {"key": "compress.usage.decrypt_example", "reason": "invalid_key_format", "language": "en-US"}, {"key": "compress.usage.encrypt_example", "reason": "invalid_key_format", "language": "en-US"}, {"key": "compress.usage.examples", "reason": "invalid_key_format", "language": "en-US"}, {"key": "config_converter.xml_generation_success", "reason": "invalid_key_format", "language": "en-US"}, {"key": "conversion_from_to", "reason": "invalid_key_format", "language": "en-US"}, {"key": "conversion_report.json", "reason": "invalid_key_format", "language": "en-US"}, {"key": "conversion_successful", "reason": "invalid_key_format", "language": "en-US"}, {"key": "convert_button", "reason": "invalid_key_format", "language": "en-US"}, {"key": "debug.service_groups_data_sample", "reason": "invalid_key_format", "language": "en-US"}, {"key": "dns_generator.xml_generated", "reason": "invalid_key_format", "language": "en-US"}, {"key": "encryption_stage.executing_encryption", "reason": "invalid_key_format", "language": "en-US"}, {"key": "encryption_stage.execution_error", "reason": "invalid_key_format", "language": "en-US"}, {"key": "encryption_stage.xml_file_not_found", "reason": "invalid_key_format", "language": "en-US"}, {"key": "enhanced_yang_generator.xml_save_error", "reason": "invalid_key_format", "language": "en-US"}, {"key": "enhanced_yang_generator.xml_saved", "reason": "invalid_key_format", "language": "en-US"}, {"key": "error.json_print_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "error.json_serialization_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "error.source_interface_not_exists", "reason": "invalid_key_format", "language": "en-US"}, {"key": "error.source_interface_not_physical", "reason": "invalid_key_format", "language": "en-US"}, {"key": "error.xml_generation_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "error.xml_generation_produced_invalid_output", "reason": "invalid_key_format", "language": "en-US"}, {"key": "error.xml_repair_produced_invalid_output", "reason": "invalid_key_format", "language": "en-US"}, {"key": "error.xml_string_too_short", "reason": "invalid_key_format", "language": "en-US"}, {"key": "error.xml_structure_damaged_during_fixing", "reason": "invalid_key_format", "language": "en-US"}, {"key": "error.xml_validation_error", "reason": "invalid_key_format", "language": "en-US"}, {"key": "error.xml_validation_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "error.xml_validation_failed_message", "reason": "invalid_key_format", "language": "en-US"}, {"key": "error.xmlns_repair_lost_significant_content", "reason": "invalid_key_format", "language": "en-US"}, {"key": "fix.xml", "reason": "invalid_key_format", "language": "en-US"}, {"key": "format.json_data_placeholder", "reason": "invalid_key_format", "language": "en-US"}, {"key": "format_not_supported", "reason": "invalid_key_format", "language": "en-US"}, {"key": "fortigate_policy_stage.xml_integration_data_prepared", "reason": "invalid_key_format", "language": "en-US"}, {"key": "fortigate_policy_stage.xml_integration_preparation_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "fortigate_policy_stage.xml_preparation_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "fortigate_strategy.validation.source_found", "reason": "invalid_key_format", "language": "en-US"}, {"key": "fortigate_strategy.validation.source_not_found", "reason": "invalid_key_format", "language": "en-US"}, {"key": "fortigate_strategy.validation.source_zone_resolved", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.all_attempts_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.available_symbols", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.backed_up_existing_file", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.backup_startup_created", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.cannot_delete_file", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.chdir_for_preload", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.checking_lib_file", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.cleaned_temp_lib_dir", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.cleanup_temp_dir", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.container_dlopen_error", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.container_dlopen_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.container_lib_loaded_via_dlopen", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.container_library_path", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.container_load_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.container_load_library_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.container_load_success_method1", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.container_minimal_preload", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.container_special_handling", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.created_system_lib_dir", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.created_system_symlink", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.critical_lib_missing", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.current_working_dir", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.decrypted_file_saved", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.decrypting_file", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.decryption_complete", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.deleted_existing_output", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.deleted_file", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.dependency_already_in_memory", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.dependency_already_loaded", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.dependency_loaded_successfully", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.encryption_success", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.error.all_load_methods_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.error.call_real_api_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.error.cannot_delete_output", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.error.checking_lib_permissions", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.error.checking_permissions", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.error.cleanup_temp_dir", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.error.container_detection_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.error.create_config_tar_gz", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.error.create_tar_file", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.error.creating_symlinks", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.error.creating_system_lib_dir", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.error.critical_lib_missing", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.error.decryption_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.error.during_decryption", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.error.encrypted_file_invalid", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.error.encrypted_file_not_found", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.error.encryption_abort", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.error.encryption_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.error.encryption_process", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.error.global_cleanup_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.error.input_dir_not_exists", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.error.input_file_not_exists", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.error.lib_not_exists", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.error.lib_not_file", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.error.list_symbols_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.error.load_lib_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.error.load_lib_mode_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.error.load_main_library_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.error.main_lib_not_exists", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.error.missing_critical_libs", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.error.missing_main_function", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.error.output_file_invalid", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.error.preload_libs_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.error.preparing_container_loading", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.error.preparing_library_files", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.error.process_tar_gz_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.error.running_ldconfig", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.error.setting_ld_library_path", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.error.tar_command_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.error.tar_gz_not_exists", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.error.unknown_action", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.error.version_file_invalid", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.error.version_file_not_exists", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.error.version_file_not_found", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.fixing_lib_permissions", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.found_dependency", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.global_cleanup_complete", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.init_complete", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.init_env", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.ld_library_path_set", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.ld_preload_set", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.ldconfig_executed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.ldconfig_executed_after_preload", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.ldconfig_refreshed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.lib_file_permissions", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.lib_loaded_successfully", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.lib_not_found", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.listing_available_symbols", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.load_main_lib_success", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.load_method_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.load_with_relative_path_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.loading_main_library", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.loading_with_absolute_path", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.main_function_verified", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.main_lib_loaded_successfully", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.method1_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.preloaded_lib", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.preparing_container_loading", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.preparing_library_files", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.ran_ldconfig", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.restore_dir_after_preload", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.retry_load_main_lib", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.setting_ld_preload", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.symlink_already_exists", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.tar_gz_processed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.traditional_load_completely_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.trying_container_method1_rpath", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.trying_load_method", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.trying_relative_path", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.trying_to_load_lib", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.using_crypto_dir", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.using_crypto_lib", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.using_crypto_libs_path", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.using_lib_dir", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:compress.using_version_file", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.added_valid_service_object", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.adding_service_objects_to_ntos_generator", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.address_object_detail", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.checking_service_groups_data", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.complex_tcp_ports_using_destination", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.complex_udp_ports_using_destination", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.detected_full_port_range", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.final_service_set_in_xml", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.final_service_sets_count", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.fixed_legacy_generator_added_icmp", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.fixed_legacy_generator_added_ip_protocol", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.fixed_legacy_generator_added_tcp_ports", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.fixed_legacy_generator_added_udp_ports", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.fixed_legacy_generator_final_xml_service_sets_count", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.fixed_legacy_generator_keeping_service_obj_node", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.fixed_legacy_generator_processing_service_objects", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.fixed_legacy_generator_service_added_success", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.fixed_legacy_generator_service_missing_ports", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.fixed_legacy_generator_service_object_detail", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.fixed_legacy_generator_service_port_config", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.fixed_legacy_generator_service_processing_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.fixed_legacy_generator_service_protocol", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.fixed_legacy_generator_start_processing_service", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.fixed_legacy_generator_xml_service_set", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.fixed_legacy_generator_xml_service_sets_count", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.fortigate_av_profile_detected", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.fortigate_ips_sensor_detected", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.improved_legacy_generator_added_description", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.improved_legacy_generator_added_icmp", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.improved_legacy_generator_added_ip_protocol", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.improved_legacy_generator_complex_port_range_conversion", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.improved_legacy_generator_final_xml_service_sets_count", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.improved_legacy_generator_keeping_service_obj_node", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.improved_legacy_generator_port_range_conversion", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.improved_legacy_generator_port_range_normalization", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.improved_legacy_generator_service_processed_success", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.improved_legacy_generator_xml_service_set", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.improved_legacy_generator_xml_service_sets_count", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.legacy_generator_processing_service_objects", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.legacy_generator_service_object_check", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.legacy_generator_service_object_detail", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.legacy_generator_service_object_details", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.legacy_generator_service_objects_count", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.legacy_generator_set_service_mappings", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.legacy_policy_rule_converted_successfully", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.loaded_fortigate_service_mapping", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.loaded_ntos_builtin_services", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.loaded_predefined_service_mapping", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.multiline_content_end", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.multiline_content_start", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.multiple_tcp_ports_using_first", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.multiple_udp_ports_using_first", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.ntos_generator_set_service_mappings", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.policy_service_mapping_applied", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.port_preserve_detected", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.predefined_service_mapping_found", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.preparing_service_stats_calculation", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.processing_address_objects_count", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.processing_service_objects_count", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.service_auto_infer_protocol", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.service_converted_count", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.service_failed_count", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.service_group_member_found_directly", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.service_group_member_found_via_mapping", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.service_group_member_using_mapped_name", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.service_group_member_validated_via_mapping", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.service_group_processor_no_mappings", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.service_group_processor_received_mappings", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.service_groups_data_empty", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.service_groups_data_length", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.service_groups_data_sample", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.service_mapping_relationship", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.service_mapping_relationships_found", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.service_mapping_relationships_saved", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.service_object_details", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.service_objects_processing_details", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.service_objects_stats_calculated", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.service_protocol_auto_corrected_tcp", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.service_protocol_auto_corrected_tcp_udp", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.service_protocol_auto_corrected_udp", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.service_protocol_correction", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.service_set_in_xml", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.service_sets_count_after_adding", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.service_skipped_count", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.set_translator", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.setting_service_objects_to_legacy_generator", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.start_adding_service_objects_to_xml", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.using_converted_service_objects_for_validation", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.using_original_service_objects_for_validation", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.using_service_mapping_for_policies", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:debug.valid_service_objects_count", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.address_group_conversion_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.address_object_conversion_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.all_physical_interfaces_unmapped", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.config_file_read_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.conversion_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.copy_file_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.create_dir_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.create_user_log_dir_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.duplicate_policy_id", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.duplicate_service_object", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.edit_without_config", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.encrypt_error", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.encryption_error", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.encryption_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.end_without_config", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.enhanced_policy_processing_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.extract_interfaces_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.extraction_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.failed_to_integrate_nat_rules", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.failed_to_integrate_security_policies", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.failed_to_load_xml", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.file_empty", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.file_not_exists", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.file_not_exists_path", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.file_read_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.fix_xml_namespaces_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.flush_log_handler_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.init_translator_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.interface_mapping_validation_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.interface_model_validation_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.invalid_fortigate_config", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.invalid_fortigate_config_structure", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.invalid_interface_mapping", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.invalid_ip_address", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.invalid_subnet_mask", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.ipv6_static_route_processing_error", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.json_print_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.legacy_policy_rule_conversion_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.mapping_file_load_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.mask_conversion_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.missing_config_version", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.missing_interface_config", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.missing_parameters", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.next_without_edit", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.ntos_generation_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.output_file_invalid", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.parsed_data_not_dict", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.parsing_config_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.parsing_failed_with_error", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.policy_rule_conversion_error", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.policy_rule_conversion_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.possible_unbalanced_nesting", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.read_json_file_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.read_text_file_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.semantic_verification_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.service_group_processing_error", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.service_object_conversion_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.static_route_processing_error", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.syntax_errors_detected", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.template_file_not_exists", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.template_not_found", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.time_range_conversion_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.transparent_mode_processing_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.unable_to_detect_version", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.unclosed_config_blocks", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.unclosed_edit_blocks", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.unhandled_exception", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.unknown_error", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.unsupported_fortigate_version", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.vendor_not_supported", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.verification_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.vlan_extraction_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.write_interface_info_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.write_json_file_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.write_text_file_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.xml_validation_error", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:error.xml_validation_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:format.data_object_placeholder", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:format.interface_placeholder", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:format.json_data_placeholder", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:format.object_placeholder", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:fortigate.address_interface_unsupported", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:fortigate.address_type_support", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:fortigate.address_type_unsupported", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:fortigate.warning.fixedport_not_supported", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:fortigate.warning.groups_not_supported", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:fortigate.warning.unsupported_role", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:fortigate.warning.users_not_supported", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.adapting_service_processor_result_format", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.add_interface_with_json", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.adding_dns_config", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.adding_static_routes", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.adding_time_ranges", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.address_group_converted", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.address_groups_processed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.address_groups_processing_complete", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.address_object_converted", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.address_objects_processed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.address_objects_processing_complete", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.address_type_detected_from_attributes", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.address_type_not_specified", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.address_type_specified", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.all_predefined_services_detected", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.all_zone_interfaces_mapped", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.blackhole_route_default_destination", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.checking_compatibility", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.checking_parent_dir", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.cleanup_temp_dir", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.config_dir_found", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.config_file_read_success", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.conversion_succeeded", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.converting_single_interface_to_list", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.converting_zones_dict_to_list", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.copy_config_dir", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.copy_xml_to_running", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.copy_xml_to_startup", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.create_directory", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.create_new_config_dir", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.create_output_dir", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.create_user_log_dir", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.current_working_dir", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.delete_existing_output", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.detected_container_environment", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.detected_dev_environment", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.detected_fortigate_version", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.directory_created", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.dmz_interface_set_required", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.encrypt_start", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.encrypted_file_generated", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.encryption_success", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.engine_debug_log_created", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.engine_user_log_created", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.enhanced_policy_processing_complete", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.error_report_saved", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.extract_interfaces_from_config", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.extracting_interfaces_from_config", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.extracting_zones", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.extraction_start", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.extraction_success", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.fallback_to_legacy", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.falling_back_to_legacy_service_processing", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.file_copied", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.final_interface_mapping_file_saved", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.fixing_xml_namespaces", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.fortinet_subinterfaces_processed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.found_template_file", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.found_template_files", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.getting_template_path", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.input_file", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.interface_info_written", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.interface_mapped", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.interface_mapping_applied", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.interface_mapping_complete", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.interface_mapping_table", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.interface_mapping_validation_passed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.interface_name_quotes_removed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.interface_zone_assigned", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.interfaces_extracted", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.json_file_saved", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.language_set", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.legacy_policy_rules_processing_complete", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.min_version_requirement", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.nat_mode_detected", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.nat_rules_added_to_xml", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.nat_rules_processing_complete", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.no_address_groups_found", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.no_address_objects_found", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.no_compatibility_issues", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.no_interfaces_or_mappings_to_validate", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.no_nat_rules_found", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.no_nat_rules_to_process", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.no_policy_rules_found", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.no_service_groups_found", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.no_service_objects_found", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.no_static_routes_found", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.no_system_settings_nat_mode", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.no_time_ranges_found", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.output_dir_created", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.output_file", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.parsing_config_complete", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.parsing_config_start", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.policy_rule_converted_success", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.policy_rules_processed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.policy_rules_processing_complete", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.possible_template_found", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.pppoe_interfaces_filtered", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.processing_address_groups", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.processing_address_objects", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.processing_fortinet_subinterfaces", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.processing_interface_mapping", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.processing_ipv4_static_routes", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.processing_ipv6_static_route", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.processing_ipv6_static_routes", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.processing_policy_rule", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.processing_policy_rules", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.processing_service_group", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.processing_service_groups", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.processing_service_objects", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.processing_static_route", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.processing_static_routes", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.processing_time_ranges", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.processing_transparent_mode", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.processing_zone_interface_mapping", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.processing_zone_interfaces", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.received_enhanced_generator_output", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.received_legacy_generator_output", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.removed_invalid_output", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.report_path", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.report_saved", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.route_destination_converted", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.saving_xml_config", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.searching_data_dir", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.security_policies_added_to_xml", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.service_group_member_mapped", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.service_groups_processed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.service_groups_processing_complete", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.service_mapping_saved", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.service_object_converted", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.service_objects_processed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.service_objects_processing_complete", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.service_protocol_auto_detected", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.service_protocol_specified", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.setting_nat_rules", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.setting_time_ranges_legacy", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.skipping_unmapped_interface", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.skipping_unmapped_zone_interface", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.start_adding_nat_rules_to_xml", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.start_adding_policy_rules_to_xml", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.start_conversion", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.start_encryption", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.start_processing_nat_rules", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.start_verification", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.start_verify_vendor_config", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.static_routes_processed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.static_routes_processing_complete", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.subinterface_direct_mapped", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.subinterface_generated_mapping", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.subinterface_mapped", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.subinterface_parent_validated", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.target_model", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.target_version", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.temp_directory_created", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.temp_directory_deleted", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.template_absolute_path", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.template_dir", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.template_dir_created", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.text_file_saved", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.time_range_converted", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.time_ranges_processed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.time_ranges_processing_complete", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.tool_started", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.transparent_mode_config", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.transparent_mode_detected", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.undefined_zone_interface_set_required", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.user_interrupted", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.user_log_file_created", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.using_alternative_template", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.using_config_root", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.using_current_directory", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.using_default_language", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.using_enhanced_policy_processor", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.using_extended_generator_for_dns", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.using_legacy_architecture", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.using_new_architecture", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.using_parser", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.using_service_mapping_for_policy_rules", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.using_service_processor_with_mapping", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.using_standard_generator", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.using_template", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.using_unfixed_xml_file", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.validating_interface_mapping", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.validating_xml", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.validation_report_saved", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.verification_passed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.verification_success", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.verify_fortigate_config", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.verifying_config_before_conversion", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.xml_config_saved_with_fixed_namespaces", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.xml_file_written", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.xml_validation_passed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.zone_extracted", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.zone_interface_mapped", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.zone_interface_mapping_complete", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.zones_extracted", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.zones_found", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:info.zones_processing_complete", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:multiline.buffer_content", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:multiline.certificate_content", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:multiline.private_key_content", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:nat.statistics_summary", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.error.conversion_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.error.duplicate_service_object", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.error.file_not_exists", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.error.interface_mapping_validation_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.error.interface_model_validation_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.error.parsing_config_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.error.target_config_generation_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.error.template_not_found", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.error.transparent_mode_processing_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.error.vendor_not_supported", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.error.yang_validation_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.info.address_groups_processed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.info.address_objects_processed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.info.config_file_generated", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.info.config_file_read_success", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.info.dns_config_added", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.info.encrypted_file_generated", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.info.interfaces_extracted", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.info.mapping_file_loaded", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.info.nat_mode_detected", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.info.nat_rules_added", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.info.no_nat_rules_found", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.info.no_system_settings_nat_mode", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.info.parsing_config_complete", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.info.parsing_config_start", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.info.policy_rules_processed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.info.predefined_services_detected", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.info.service_groups_processed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.info.service_objects_processed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.info.start_parsing", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.info.static_routes_processed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.info.time_ranges_processed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.info.transparent_mode_config", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.info.transparent_mode_detected", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.info.yang_validation_passed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.info.zones_extracted", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.summary.address_groups_processed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.summary.addresses_processed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.summary.compatibility_issue", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.summary.compatibility_issues", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.summary.conversion_complete", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.summary.conversion_stats", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.summary.conversion_summary", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.summary.conversion_time", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.summary.failed_item_detail", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.summary.failed_items_detail", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.summary.interfaces_processed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.summary.manual_config_item", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.summary.manual_config_needed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.summary.nat_rules_processed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.summary.no_compatibility_issues", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.summary.no_manual_config_needed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.summary.no_unconverted_items", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.summary.policies_processed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.summary.routes_processed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.summary.service_groups_processed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.summary.services_processed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.summary.skipped_item_detail", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.summary.skipped_items_detail", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.summary.time_ranges_processed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.summary.total_items_processed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.summary.unconverted_item", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.summary.unconverted_items", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.summary.zones_processed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.warning.all_physical_interfaces_unmapped", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.warning.compatibility_issues_detected", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.warning.nat_not_supported_in_version", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.warning.route_interface_not_mapped_check", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.warning.total_unmapped_zone_interfaces", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.warning.unsupported_protocol_ntos", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:user.warning.zone_unmapped_interfaces", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.all_physical_interfaces_unmapped", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.cannot_parse_port_range", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.cannot_process_zone_interfaces", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.cannot_process_zones", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.compatibility_issue_detail", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.compatibility_issues_detected", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.compatibility_issues_found", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.config_dir_not_found", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.failed_to_add_nat_rules_to_xml", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.failed_to_add_security_policies_to_xml", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.failed_to_convert_policy_rule", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.format_error", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.generator_empty_service_node_removal_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.generator_no_service_object_node", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.generator_service_object_node_empty", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.interface_data_not_list", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.interface_mapping_not_found", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.interface_missing_raw_name", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.interface_missing_raw_name_field", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.interface_not_string", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.invalid_port_range", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.ipv6_route_missing_destination", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.ipv6_route_no_necessary_config", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.legacy_policy_rule_missing_name", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.missing_param", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.missing_params", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.nat_rule_skipped_version_not_supported", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.nat_rules_not_supported", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.new_architecture_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.no_interfaces_to_process", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.no_service_obj_node_in_final_xml", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.path_corrected", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.port_out_of_range", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.pppoe_interface_filtered", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.pppoe_missing_credentials", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.route_interface_not_mapped", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.route_missing_destination", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.semantic_verification", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.service_group_member_not_found", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.service_group_missing_name", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.service_group_no_members", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.service_group_no_valid_members", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.service_missing_protocol_and_ports", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.service_processor_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.skip_non_dict_service_object", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.skip_service_object_missing_name", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.skipping_address_group_empty_members", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.skipping_address_group_missing_name", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.skipping_address_invalid_subnet", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.skipping_address_missing_ip_range", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.skipping_address_missing_name", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.skipping_address_missing_subnet", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.skipping_address_type_not_explicitly_supported", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.skipping_address_unsupported_type", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.skipping_interface_associated_address", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.skipping_invalid_interface_with_reason", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.skipping_non_dict_zone", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.skipping_policy_rule_missing_fields", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.skipping_policy_rule_unmapped_interfaces", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.skipping_service_missing_name", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.skipping_service_missing_port_range", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.skipping_service_missing_protocol_and_ports", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.skipping_service_unsupported_protocol", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.skipping_unsupported_protocol_ntos", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.static_routes_not_supported", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.subinterface_missing_parent_or_vlanid_skipped", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.subinterface_parent_not_mapped", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.subinterface_parent_not_mapped_skipped", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.tcp_port_range_invalid", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.template_dir_not_exists", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.time_ranges_not_supported", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.total_unmapped_zone_interfaces", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.translation_format_error", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.translation_missing_param", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.translation_missing_params", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.udp_port_range_invalid", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.zone_data_not_list", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.zone_interface_not_mapped", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.zone_interfaces_not_list", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.zone_unmapped_interfaces", "reason": "invalid_key_format", "language": "en-US"}, {"key": "i18n:warning.zones_not_list", "reason": "invalid_key_format", "language": "en-US"}, {"key": "info.json_file_saved", "reason": "invalid_key_format", "language": "en-US"}, {"key": "info.xml_config_saved_with_fixed_namespaces", "reason": "invalid_key_format", "language": "en-US"}, {"key": "info.xml_file_written", "reason": "invalid_key_format", "language": "en-US"}, {"key": "info.xml_generation_complete", "reason": "invalid_key_format", "language": "en-US"}, {"key": "info.xml_generation_complete_with_validation", "reason": "invalid_key_format", "language": "en-US"}, {"key": "info.xml_validation_passed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "info.xml_validation_success", "reason": "invalid_key_format", "language": "en-US"}, {"key": "interface.source", "reason": "invalid_key_format", "language": "en-US"}, {"key": "interface_mapping.json", "reason": "invalid_key_format", "language": "en-US"}, {"key": "interface_processing_stage.xml_fragment_length", "reason": "invalid_key_format", "language": "en-US"}, {"key": "interface_processing_stage.xml_generation_completed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "interface_processing_stage.xml_generation_started", "reason": "invalid_key_format", "language": "en-US"}, {"key": "interface_processor.source_interface_exists", "reason": "invalid_key_format", "language": "en-US"}, {"key": "interface_processor.source_interface_validation_default", "reason": "invalid_key_format", "language": "en-US"}, {"key": "interface_processor.source_interface_validation_error", "reason": "invalid_key_format", "language": "en-US"}, {"key": "interface_processor.source_interface_validation_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "interface_processor.xml_fragment_generated", "reason": "invalid_key_format", "language": "en-US"}, {"key": "interface_processor.xml_fragment_generation_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "internal_error", "reason": "invalid_key_format", "language": "en-US"}, {"key": "invalid_configuration", "reason": "invalid_key_format", "language": "en-US"}, {"key": "invalid_ini", "reason": "invalid_key_format", "language": "en-US"}, {"key": "invalid_json", "reason": "invalid_key_format", "language": "en-US"}, {"key": "invalid_properties", "reason": "invalid_key_format", "language": "en-US"}, {"key": "invalid_toml", "reason": "invalid_key_format", "language": "en-US"}, {"key": "invalid_xml", "reason": "invalid_key_format", "language": "en-US"}, {"key": "invalid_yaml", "reason": "invalid_key_format", "language": "en-US"}, {"key": "libcrypto.so", "reason": "invalid_key_format", "language": "en-US"}, {"key": "libssl.so", "reason": "invalid_key_format", "language": "en-US"}, {"key": "misc.debug.python", "reason": "invalid_key_format", "language": "en-US"}, {"key": "misc.info.json_fetch_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "misc.info.test_failed_does", "reason": "invalid_key_format", "language": "en-US"}, {"key": "misc.message.example_config", "reason": "invalid_key_format", "language": "en-US"}, {"key": "misc.message.exec_prefix_option", "reason": "invalid_key_format", "language": "en-US"}, {"key": "misc.message.gnome_examples_exception", "reason": "invalid_key_format", "language": "en-US"}, {"key": "misc.message.json_error_occurred", "reason": "invalid_key_format", "language": "en-US"}, {"key": "misc.message.pygments_lexer_specified", "reason": "invalid_key_format", "language": "en-US"}, {"key": "misc.message.pyopenssl_module_missing", "reason": "invalid_key_format", "language": "en-US"}, {"key": "misc.message.python_option_must", "reason": "invalid_key_format", "language": "en-US"}, {"key": "misc.message.python_version_warning", "reason": "invalid_key_format", "language": "en-US"}, {"key": "misc.message.some_build_dependencies", "reason": "invalid_key_format", "language": "en-US"}, {"key": "misc.message.some_compile_link", "reason": "invalid_key_format", "language": "en-US"}, {"key": "misc.message.test_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "misc.message.warning_testing_via", "reason": "invalid_key_format", "language": "en-US"}, {"key": "misc.message.xml_version_encoding", "reason": "invalid_key_format", "language": "en-US"}, {"key": "misc.message.xml_version_encoding_1", "reason": "invalid_key_format", "language": "en-US"}, {"key": "misc.message.xmlns", "reason": "invalid_key_format", "language": "en-US"}, {"key": "misc.message.xmlrpc_request_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "missing_required_field", "reason": "invalid_key_format", "language": "en-US"}, {"key": "pipeline_manager.executing_stage", "reason": "invalid_key_format", "language": "en-US"}, {"key": "policy.source_interface_mapped", "reason": "invalid_key_format", "language": "en-US"}, {"key": "policy.source_interface_mapped_with_mapping", "reason": "invalid_key_format", "language": "en-US"}, {"key": "policy.source_zone_mapped", "reason": "invalid_key_format", "language": "en-US"}, {"key": "policy.source_zone_mapped_with_mapping", "reason": "invalid_key_format", "language": "en-US"}, {"key": "policy_processor.source_fortigate_logical_zone_accepted", "reason": "invalid_key_format", "language": "en-US"}, {"key": "policy_processor.source_interface_found_in_mapping", "reason": "invalid_key_format", "language": "en-US"}, {"key": "policy_processor.source_interface_mapping_found", "reason": "invalid_key_format", "language": "en-US"}, {"key": "policy_processor.source_interface_not_found_and_not_zone", "reason": "invalid_key_format", "language": "en-US"}, {"key": "policy_processor.source_interface_not_in_mapping", "reason": "invalid_key_format", "language": "en-US"}, {"key": "policy_processor.source_zone_identified", "reason": "invalid_key_format", "language": "en-US"}, {"key": "policy_processor.source_zone_resolved_to_interfaces", "reason": "invalid_key_format", "language": "en-US"}, {"key": "reason.source_interface_not_found", "reason": "invalid_key_format", "language": "en-US"}, {"key": "reason.source_interface_not_physical", "reason": "invalid_key_format", "language": "en-US"}, {"key": "refactored_wrapper.executing_integrator", "reason": "invalid_key_format", "language": "en-US"}, {"key": "refactored_wrapper.execution_completed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "refactored_wrapper.execution_exception", "reason": "invalid_key_format", "language": "en-US"}, {"key": "refactored_wrapper.execution_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "refactored_wrapper.execution_started", "reason": "invalid_key_format", "language": "en-US"}, {"key": "refactored_wrapper.xml_file_generated", "reason": "invalid_key_format", "language": "en-US"}, {"key": "refactored_wrapper.xml_generation_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "report.source_file", "reason": "invalid_key_format", "language": "en-US"}, {"key": "request_error", "reason": "invalid_key_format", "language": "en-US"}, {"key": "reset_button", "reason": "invalid_key_format", "language": "en-US"}, {"key": "running.xml", "reason": "invalid_key_format", "language": "en-US"}, {"key": "service.builtin.socks.description", "reason": "invalid_key_format", "language": "en-US"}, {"key": "service.builtin.socks_udp.description", "reason": "invalid_key_format", "language": "en-US"}, {"key": "service_group_processor.xml_fragment_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "service_group_processor.xml_fragment_success", "reason": "invalid_key_format", "language": "en-US"}, {"key": "service_processor.xml_creation_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "service_processor.xml_fragment_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "service_processor.xml_fragment_success", "reason": "invalid_key_format", "language": "en-US"}, {"key": "service_processor.xml_generated", "reason": "invalid_key_format", "language": "en-US"}, {"key": "source_content", "reason": "invalid_key_format", "language": "en-US"}, {"key": "source_format", "reason": "invalid_key_format", "language": "en-US"}, {"key": "startup.xml", "reason": "invalid_key_format", "language": "en-US"}, {"key": "static_route_processor.xml_fragment_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "static_route_processor.xml_fragment_success", "reason": "invalid_key_format", "language": "en-US"}, {"key": "static_route_processor.xml_fragment_with_groups", "reason": "invalid_key_format", "language": "en-US"}, {"key": "success", "reason": "invalid_key_format", "language": "en-US"}, {"key": "supported_formats", "reason": "invalid_key_format", "language": "en-US"}, {"key": "target_content", "reason": "invalid_key_format", "language": "en-US"}, {"key": "target_format", "reason": "invalid_key_format", "language": "en-US"}, {"key": "time_range_processor.xml_fragment_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "time_range_processor.xml_fragment_success", "reason": "invalid_key_format", "language": "en-US"}, {"key": "timeout_error", "reason": "invalid_key_format", "language": "en-US"}, {"key": "tmpgdwacvox.xml", "reason": "invalid_key_format", "language": "en-US"}, {"key": "twice_nat44_error.user_message.xml_generation_error", "reason": "invalid_key_format", "language": "en-US"}, {"key": "twice_nat44_performance.execution_completed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "twice_nat44_performance.execution_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "user.detail.xml_integration", "reason": "invalid_key_format", "language": "en-US"}, {"key": "user.flow.stage.xml_generation", "reason": "invalid_key_format", "language": "en-US"}, {"key": "user.flow.stage.xml_template_integration", "reason": "invalid_key_format", "language": "en-US"}, {"key": "user.stage.name.xml_generation", "reason": "invalid_key_format", "language": "en-US"}, {"key": "user.stage.xml_integration", "reason": "invalid_key_format", "language": "en-US"}, {"key": "user.stage.xml_template_integration", "reason": "invalid_key_format", "language": "en-US"}, {"key": "user.test.count", "reason": "invalid_key_format", "language": "en-US"}, {"key": "user.test.hello", "reason": "invalid_key_format", "language": "en-US"}, {"key": "user.test.welcome", "reason": "invalid_key_format", "language": "en-US"}, {"key": "validation_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "validator.execute_windows_command", "reason": "invalid_key_format", "language": "en-US"}, {"key": "validator.execute_yanglint_command", "reason": "invalid_key_format", "language": "en-US"}, {"key": "version_info", "reason": "invalid_key_format", "language": "en-US"}, {"key": "warning.xml_validation_skipped", "reason": "invalid_key_format", "language": "en-US"}, {"key": "welcome", "reason": "invalid_key_format", "language": "en-US"}, {"key": "xml_integrator.some_policies_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "xml_template_integration.xml_integration_complete", "reason": "invalid_key_format", "language": "en-US"}, {"key": "xml_template_integration.xml_integration_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "xml_template_integration.xml_namespace_validation", "reason": "invalid_key_format", "language": "en-US"}, {"key": "xml_template_integration.xml_namespace_validation_alt", "reason": "invalid_key_format", "language": "en-US"}, {"key": "xml_template_integration.xml_namespace_validation_passed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "xml_template_integration.xml_optimization_complete", "reason": "invalid_key_format", "language": "en-US"}, {"key": "xml_template_integration.xml_optimization_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "xml_template_integration.xml_optimization_removed_duplicates", "reason": "invalid_key_format", "language": "en-US"}, {"key": "xml_template_integration.xml_root_validation_passed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "xml_template_integration.xml_root_validation_passed_alt", "reason": "invalid_key_format", "language": "en-US"}, {"key": "xml_template_integration.xml_structure_validation", "reason": "invalid_key_format", "language": "en-US"}, {"key": "xml_template_integration.xml_structure_validation_alt", "reason": "invalid_key_format", "language": "en-US"}, {"key": "xml_template_integration.xml_structure_validation_complete", "reason": "invalid_key_format", "language": "en-US"}, {"key": "xml_template_integration.xml_validation_complete", "reason": "invalid_key_format", "language": "en-US"}, {"key": "xml_template_integration.xml_validation_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "xml_template_integration_stage.xml_basic_generation_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "xml_template_integration_stage.xml_basic_generation_success", "reason": "invalid_key_format", "language": "en-US"}, {"key": "xml_template_integration_stage.xml_fallback_generation_success", "reason": "invalid_key_format", "language": "en-US"}, {"key": "xml_template_integration_stage.xml_formatted_generation_failed", "reason": "invalid_key_format", "language": "en-US"}, {"key": "xml_template_integration_stage.xml_formatted_generation_success", "reason": "invalid_key_format", "language": "en-US"}, {"key": "xml_template_integration_stage.xml_generated", "reason": "invalid_key_format", "language": "en-US"}, {"key": "xml_template_integration_stage.xml_generation_debug", "reason": "invalid_key_format", "language": "en-US"}, {"key": "xml_template_integration_stage.xml_generation_null_root", "reason": "invalid_key_format", "language": "en-US"}, {"key": "xml_template_integration_stage.xml_generation_starting", "reason": "invalid_key_format", "language": "en-US"}, {"key": "xml_template_integration_stage.xml_generation_stats", "reason": "invalid_key_format", "language": "en-US"}, {"key": "xml_template_integration_stage.xml_validated_optimized", "reason": "invalid_key_format", "language": "en-US"}, {"key": "yang.xml_generation_complete", "reason": "invalid_key_format", "language": "en-US"}], "summary": {"quality_improvement": 1688, "cleanup_rate": 11.28, "final_quality_score": 87.49}}