#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
import unittest
import tempfile
import xml.etree.ElementTree as ET

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from convert import convert_config, get_template_path
from generators.yang_generator import create_new_interface, reset_tunnel_ids, create_static_route_config

class TestConvertConfig(unittest.TestCase):
    """测试配置转换功能"""
    
    def setUp(self):
        # 获取测试数据目录
        self.test_data_dir = os.path.join(os.path.dirname(__file__), "test_data")
        
        # 有效的配置文件路径
        self.valid_config_path = os.path.join(self.test_data_dir, "valid_config.txt")
        
        # 确保测试数据文件存在
        self.assertTrue(os.path.exists(self.valid_config_path), "测试数据文件不存在")
        
        # 创建临时输出目录
        self.temp_dir = tempfile.mkdtemp()
        self.output_xml = os.path.join(self.temp_dir, "output.xml")
        
        # 项目根目录中的数据目录
        self.project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
        
        # 接口映射文件
        self.mapping_file = os.path.join(self.project_root, "data", "mappings", "interface_mapping.json")
        
        # 确保接口映射文件存在
        if not os.path.exists(self.mapping_file):
            # 如果不存在，创建一个简单的映射文件用于测试
            os.makedirs(os.path.dirname(self.mapping_file), exist_ok=True)
            with open(self.mapping_file, 'w', encoding='utf-8') as f:
                f.write('{\n  "port1": "Ge0/0",\n  "port2": "Ge0/1"\n}')
    
    def tearDown(self):
        # 清理临时文件
        if os.path.exists(self.output_xml):
            os.remove(self.output_xml)
        os.rmdir(self.temp_dir)
        
        # 重置PPPoE隧道ID
        reset_tunnel_ids()
    
    def test_convert_config(self):
        """测试配置转换功能"""
        # 执行转换操作
        success, message = convert_config(
            self.valid_config_path,
            self.mapping_file,
            "z5100s",
            "R10P2",
            self.output_xml
        )
        
        # 验证转换成功
        self.assertTrue(success, f"转换失败: {message}")
        
        # 验证输出文件存在
        self.assertTrue(os.path.exists(self.output_xml), "未生成XML输出文件")
        
        # 验证XML文件的基本结构
        try:
            tree = ET.parse(self.output_xml)
            root = tree.getroot()
            
            # 基本验证：检查是否有根元素
            self.assertIsNotNone(root)
            
            # 这里可以添加更详细的XML内容验证
            # 例如检查是否包含interfaces元素
            # interfaces = root.find(".//interfaces")
            # self.assertIsNotNone(interfaces)
            
        except Exception as e:
            self.fail(f"无法解析生成的XML文件: {str(e)}")
    
    def test_get_template_path(self):
        """测试获取模板路径功能"""
        # 测试获取模板路径
        template_path = get_template_path("z5100s", "R10P2")
        
        # 验证返回了有效的路径
        self.assertIsNotNone(template_path)
        self.assertTrue(len(template_path) > 0)
    
    def test_convert_nonexistent_file(self):
        """测试转换不存在的文件"""
        # 使用一个肯定不存在的文件名
        non_existent_file = os.path.join(self.test_data_dir, "this_file_definitely_does_not_exist.txt")
        
        # 确保文件真的不存在
        if os.path.exists(non_existent_file):
            os.remove(non_existent_file)
            
        success, message = convert_config(
            non_existent_file,
            self.mapping_file,
            "z5100s",
            "R10P2",
            self.output_xml
        )
        
        # 验证转换失败
        self.assertFalse(success, f"应当返回转换失败，但实际返回成功，错误信息: {message}")
        self.assertFalse(os.path.exists(self.output_xml), "不应该生成输出文件")
        self.assertIn("无法读取配置文件", message, "错误信息应当提示无法读取配置文件")
    
    def test_convert_invalid_config(self):
        """测试转换无效的配置文件"""
        invalid_config_path = os.path.join(self.test_data_dir, "invalid_config.txt")
        
        # 确保无效配置文件存在
        self.assertTrue(os.path.exists(invalid_config_path), "无效配置测试文件不存在")
        
        success, message = convert_config(
            invalid_config_path,
            self.mapping_file,
            "z5100s",
            "R10P2",
            self.output_xml
        )
        
        # 对于无效配置，可能会转换失败或生成警告
        # 这里我们不做强制性断言，因为具体行为取决于转换引擎的容错能力
        if not success:
            self.assertIn("转换失败", message)

    def test_yang_generator_interfaces(self):
        """测试YANG生成器的接口创建功能"""
        from lxml import etree
        
        # 创建一个测试接口信息字典
        intf_data = {
            "name": "test_interface",
            "status": "up",
            "description": "测试接口",
            "ip": "*************/24",
            "role": "lan",
            "allowaccess": ["ping", "https"],
            "estimated-upstream-bandwidth": 100,
            "estimated-downstream-bandwidth": 200
        }
        
        # 创建一个测试XML根节点
        root = etree.Element("interfaces")
        interface_elem = etree.SubElement(root, "interface")
        
        # 创建接口映射表
        interface_mapping = {
            "test_original": "test_interface"
        }
        
        # 调用YANG生成器的接口创建函数
        create_new_interface(interface_elem, intf_data, interface_mapping)
        
        # 转换为字符串便于断言
        xml_str = etree.tostring(root, encoding='utf-8', pretty_print=True).decode('utf-8')
        
        # 考虑命名空间的断言，使用包含NS前缀的形式或检查值而非完整的标签
        self.assertIn("test_interface", xml_str)  # 验证接口名称存在
        self.assertIn("<ns0:name>test_interface</ns0:name>", xml_str)  # 带命名空间的检查
        self.assertIn("true</ns0:enabled>", xml_str)  # 验证接口状态正确
        self.assertIn("测试接口</ns0:description>", xml_str)  # 验证接口描述正确
        self.assertIn("*************/24</ns0:ip>", xml_str)  # 验证IP地址正确
        
        # 验证带宽配置正确
        self.assertIn("100000</ns0:upload-bandwidth-value>", xml_str)
        self.assertIn("200000</ns0:download-bandwidth-value>", xml_str)
        
        # 验证访问控制正确
        self.assertIn("<ns4:ping>true</ns4:ping>", xml_str)
        self.assertIn("<ns4:https>true</ns4:https>", xml_str)
        self.assertIn("<ns4:ssh>false</ns4:ssh>", xml_str)

    def test_yang_generator_pppoe(self):
        """测试YANG生成器的PPPoE配置功能"""
        from lxml import etree
        
        # 创建一个PPPoE接口信息字典
        pppoe_data = {
            "name": "pppoe_interface",
            "status": "up",
            "role": "wan",
            "mode": "pppoe",
            "pppoe_username": "test_user",
            "pppoe_password": "test_password",
            "description": "PPPoE测试接口"
        }
        
        # 创建一个测试XML根节点
        root = etree.Element("interfaces")
        interface_elem = etree.SubElement(root, "interface")
        
        # 创建接口映射表
        interface_mapping = {
            "pppoe_orig": "pppoe_interface"
        }
        
        # 调用YANG生成器的接口创建函数
        create_new_interface(interface_elem, pppoe_data, interface_mapping)
        
        # 转换为字符串便于断言
        xml_str = etree.tostring(root, encoding='utf-8', pretty_print=True).decode('utf-8')
        
        # 考虑命名空间的断言，使用包含NS前缀的形式或检查值而非完整的标签
        self.assertIn("pppoe_interface", xml_str)  # 验证接口名存在
        self.assertIn("<ns0:name>pppoe_interface</ns0:name>", xml_str)  # 带命名空间的检查
        self.assertIn("true</ns0:enabled>", xml_str)  # 验证接口状态正确
        self.assertIn("PPPoE测试接口</ns0:description>", xml_str)  # 验证接口描述正确
        self.assertIn("<ns0:pppoe>", xml_str)  # 验证PPPoE配置存在
        self.assertIn("<ns0:user>test_user</ns0:user>", xml_str)  # 验证用户名正确
        self.assertIn("<ns0:password>", xml_str)  # 密码可能会被加密，不验证具体值
        
        # 验证隧道ID分配正确
        self.assertIn("<ns0:tunnel>0</ns0:tunnel>", xml_str)

    def test_static_route_creation(self):
        """测试静态路由配置生成功能"""
        from lxml import etree
        
        # 创建一个测试VRF根节点
        root = etree.Element("config")
        vrf_elem = etree.SubElement(root, "vrf")
        
        # 创建一个静态路由配置字典
        route_data = {
            "destination": "*************/24",
            "gateway": "********",
            "device": "port1",
            "distance": "10"
        }
        
        # 定义接口映射
        interface_mapping = {
            "port1": "Ge0/0",
            "port2": "Ge0/1"
        }
        
        # 调用静态路由创建函数
        result = create_static_route_config(vrf_elem, route_data, interface_mapping)
        
        # 验证路由创建成功
        self.assertTrue(result)
        
        # 转换为字符串便于断言
        xml_str = etree.tostring(root, encoding='utf-8', pretty_print=True).decode('utf-8')
        
        # 考虑命名空间的断言
        self.assertIn("*************/24", xml_str)  # 验证目标网络存在
        self.assertIn("********%Ge0/0", xml_str)  # 验证网关和接口名
        self.assertIn("10</", xml_str)  # 验证管理距离 
        self.assertIn("<enable>true</enable>", xml_str)  # 验证路由已启用

if __name__ == "__main__":
    unittest.main() 