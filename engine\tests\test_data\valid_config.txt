#config-version=FGVM64-7.0.0-FW-build0066-201028:opmode=0:vdom=0:user=admin
#conf_file_ver=326431177342885
#buildno=0066
#global_vdom=1

config system global
    set admintimeout 50
    set alias "FortiGate-VM64"
    set timezone 08
    set hostname "FortiGate-VM64"
end

config system interface
    edit "port1"
        set vdom "root"
        set ip *********** *************
        set allowaccess ping https ssh
        set type physical
        set description "WAN接口"
        set status up
    next
    edit "port2"
        set vdom "root"
        set ip ******** *************
        set allowaccess ping https ssh
        set type physical
        set description "LAN接口"
        set status up
    next
    edit "vlan10"
        set vdom "root"
        set ip ********** *************
        set allowaccess ping
        set type vlan
        set parent "port1"
        set vlanid 10
        set status up
    next
end

config system dns
    set primary *******
    set secondary *******
end

config system zone
    edit "WAN_Zone"
        set interface "port1"
    next
    edit "LAN_Zone"
        set interface "port2" "vlan10"
    next
end

config firewall address
    edit "Internal_Network"
        set subnet ******** *************
    next
    edit "Guest_Network"
        set subnet ********** *************
    next
end

config firewall policy
    edit 1
        set name "LAN to WAN"
        set srcintf "port2"
        set dstintf "port1"
        set srcaddr "Internal_Network"
        set dstaddr "all"
        set action accept
        set schedule "always"
        set service "ALL"
        set logtraffic all
    next
    edit 2
        set name "Guest to WAN"
        set srcintf "vlan10"
        set dstintf "port1"
        set srcaddr "Guest_Network"
        set dstaddr "all"
        set action accept
        set schedule "always"
        set service "ALL"
        set logtraffic all
    next
end