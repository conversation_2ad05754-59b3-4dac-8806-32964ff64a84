#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import unittest
from unittest.mock import patch, MagicMock

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from engine.convert import process_service_objects

class TestDuplicateServiceObjects(unittest.TestCase):
    """测试重复服务对象检测功能"""
    
    def setUp(self):
        # 测试前设置
        self.patch_log = patch('engine.convert.log')
        self.patch_user_log = patch('engine.convert.user_log')
        self.patch_translate = patch('engine.convert._')
        
        self.mock_log = self.patch_log.start()
        self.mock_user_log = self.patch_user_log.start()
        self.mock_translate = self.patch_translate.start()
        
        # 配置mock翻译函数
        self.mock_translate.side_effect = lambda key, **kwargs: f"{key}: {kwargs}" if kwargs else key
    
    def tearDown(self):
        # 测试后清理
        self.patch_log.stop()
        self.patch_user_log.stop()
        self.patch_translate.stop()
    
    def test_no_duplicate_services(self):
        """测试没有重复服务对象的情况"""
        # 创建测试数据 - 没有重复的服务对象
        config_data = {
            "service_objects": [
                {
                    "name": "HTTP",
                    "protocol": "tcp",
                    "tcp-portrange": "80"
                },
                {
                    "name": "HTTPS",
                    "protocol": "tcp",
                    "tcp-portrange": "443"
                },
                {
                    "name": "DNS",
                    "protocol": "udp",
                    "udp-portrange": "53"
                }
            ]
        }
        
        # 处理服务对象
        result = process_service_objects(config_data)
        
        # 验证结果
        self.assertEqual(len(result["converted"]), 3, "应该有3个成功转换的服务对象")
        self.assertEqual(len(result["failed"]), 0, "不应该有失败的服务对象")
        self.assertEqual(len(result["skipped"]), 0, "不应该有跳过的服务对象")
        
        # 验证日志调用
        self.mock_log.assert_any_call("i18n:info.service_objects_processing_complete", 
                                     total=3, success=3, failed=0, skipped=0)
    
    def test_duplicate_services(self):
        """测试存在重复服务对象的情况"""
        # 创建测试数据 - 包含重复的服务对象名称
        config_data = {
            "service_objects": [
                {
                    "name": "HTTP",
                    "protocol": "tcp",
                    "tcp-portrange": "80"
                },
                {
                    "name": "HTTPS",
                    "protocol": "tcp",
                    "tcp-portrange": "443"
                },
                {
                    "name": "HTTP",  # 重复的服务名称
                    "protocol": "tcp",
                    "tcp-portrange": "8080"
                }
            ]
        }
        
        # 处理服务对象
        result = process_service_objects(config_data)
        
        # 验证结果 - 应该有2个成功，1个失败（重复的服务对象）
        self.assertEqual(len(result["converted"]), 2, "应该有2个成功转换的服务对象")
        self.assertEqual(len(result["failed"]), 1, "应该有1个失败的服务对象")
        self.assertEqual(len(result["skipped"]), 0, "不应该有跳过的服务对象")
        
        # 验证失败的服务对象是否是因为重复名称而失败
        self.assertEqual(result["failed"][0]["name"], "HTTP", "失败的服务对象名称应该是HTTP")
        
        # 验证错误日志
        self.mock_log.assert_any_call("i18n:error.duplicate_service_object", "error", name="HTTP")
        self.mock_user_log.assert_any_call("i18n:user.error.duplicate_service_object", "error", name="HTTP")
    
    def test_duplicate_service_with_different_case(self):
        """测试不同大小写的重复服务对象名称"""
        # 创建测试数据 - 包含大小写不同的服务对象名称
        # 注意：目前的实现是大小写敏感的，这个测试验证这一点
        config_data = {
            "service_objects": [
                {
                    "name": "HTTP",
                    "protocol": "tcp",
                    "tcp-portrange": "80"
                },
                {
                    "name": "HTTPS",
                    "protocol": "tcp",
                    "tcp-portrange": "443"
                },
                {
                    "name": "http",  # 小写的服务名称，实际上与HTTP不同
                    "protocol": "tcp",
                    "tcp-portrange": "8080"
                }
            ]
        }
        
        # 处理服务对象
        result = process_service_objects(config_data)
        
        # 验证结果 - 应该有3个成功，因为http和HTTP被视为不同的名称
        self.assertEqual(len(result["converted"]), 3, "应该有3个成功转换的服务对象")
        self.assertEqual(len(result["failed"]), 0, "不应该有失败的服务对象")
        self.assertEqual(len(result["skipped"]), 0, "不应该有跳过的服务对象")
        
        # 验证成功的服务对象
        service_names = [svc["name"] for svc in result["converted"]]
        self.assertIn("HTTP", service_names, "HTTP应该在成功转换的服务对象中")
        self.assertIn("http", service_names, "http应该在成功转换的服务对象中")
        self.assertIn("HTTPS", service_names, "HTTPS应该在成功转换的服务对象中")

if __name__ == "__main__":
    unittest.main() 