#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
import unittest
import json
import tempfile
import shutil  # 添加shutil用于清理非空目录

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from extract import extract_interface_info, mask_to_prefix
from engine.verify import detect_fortigate_version

class TestExtractInterface(unittest.TestCase):
    """测试接口信息提取功能"""
    
    def setUp(self):
        # 获取测试数据目录
        self.test_data_dir = os.path.join(os.path.dirname(__file__), "test_data")
        
        # 有效的配置文件路径
        self.valid_config_path = os.path.join(self.test_data_dir, "valid_config.txt")
        
        # 确保测试数据文件存在
        self.assertTrue(os.path.exists(self.valid_config_path), "测试数据文件不存在")
        
        # 创建临时输出目录
        self.temp_dir = tempfile.mkdtemp()
        self.output_json = os.path.join(self.temp_dir, "interfaces.json")
        
        # 创建测试用配置文件，添加版本信息
        self.version_config_path = os.path.join(self.temp_dir, "version_config.txt")
        with open(self.version_config_path, "w", encoding="utf-8") as f:
            f.write("""
#config-version=FGVM64-7.0.0-FW-build0066-201028:opmode=0:vdom=0:user=admin
#conf_file_ver=326431177342885
#buildno=0066
#global_vdom=1

config system global
    set admintimeout 50
    set alias "FortiGate-VM64"
    set timezone 08
end

config system interface
    edit "port1"
        set vdom "root"
        set ip *********** *************
        set allowaccess ping https ssh
        set type physical
        set description "WAN接口"
    next
    edit "port2"
        set vdom "root"
        set ip ******** *************
        set allowaccess ping https ssh
        set type physical
        set description "LAN接口"
    next
end

config system zone
    edit "WAN_Zone"
        set interface "port1"
    next
    edit "LAN_Zone"
        set interface "port2"
    next
end
""")
    
    def tearDown(self):
        # 清理临时文件
        try:
            if os.path.exists(self.output_json):
                os.remove(self.output_json)
            
            if os.path.exists(self.version_config_path):
                os.remove(self.version_config_path)
                    
            # 检查是否有其他临时文件
            for filename in os.listdir(self.temp_dir):
                file_path = os.path.join(self.temp_dir, filename)
                try:
                    if os.path.isfile(file_path):
                        os.remove(file_path)
                except Exception as e:
                    print(f"清理文件失败: {file_path}: {e}")
                        
            # 使用rmdir移除现在应该为空的目录
            os.rmdir(self.temp_dir)
        except OSError:
            # 如果目录非空，使用shutil.rmtree强制删除
            shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_extract_interfaces(self):
        """测试从有效配置文件中提取接口信息"""
        # 执行提取操作
        result = extract_interface_info(self.valid_config_path, self.output_json)
        
        # 验证提取成功
        self.assertTrue(result.get("success", False))
        
        # 验证输出JSON文件存在
        self.assertTrue(os.path.exists(self.output_json), "未生成接口信息JSON文件")
        
        # 加载JSON数据并验证内容
        with open(self.output_json, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 验证基本结构
        self.assertIn('interfaces', data)
        self.assertIn('count', data)
        
        # 验证接口数量
        self.assertEqual(data['count'], len(data['interfaces']))
        self.assertGreaterEqual(data['count'], 2, "至少应提取到2个接口")
        
        # 验证接口具体信息 - 使用安全版本的字段
        interfaces = {i['name']: i for i in data['interfaces']}
        
        # 验证接口存在性
        self.assertIn('port1', interfaces)
        self.assertIn('port2', interfaces)
        
        # 验证基本字段 - 仅检查安全版本中可用的字段
        for port_name in ['port1', 'port2']:
            port = interfaces[port_name]
            self.assertIn('name', port)
            self.assertIn('type', port)
            self.assertIn('has_ip', port)
            self.assertIn('status', port)
    
    def test_extract_nonexistent_file(self):
        """测试从不存在的文件提取接口信息"""
        result = extract_interface_info("nonexistent_file.txt", self.output_json)
        self.assertFalse(result.get("success", False))
        self.assertFalse(os.path.exists(self.output_json))
    
    def test_extract_without_output(self):
        """测试不指定输出文件的情况"""
        result = extract_interface_info(self.valid_config_path)
        self.assertTrue(result.get("success", False))
    
    def test_version_detection(self):
        """测试版本检测功能"""
        result = extract_interface_info(self.version_config_path, os.path.join(self.temp_dir, "version_output.json"))
        self.assertTrue(result.get("success", False))
        self.assertIn("version", result)
        self.assertEqual(result["version"], "7.0.0")
        
        # 直接测试版本检测函数
        with open(self.version_config_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        version_detected, version_str, major, minor, patch = detect_fortigate_version(content)
        self.assertTrue(version_detected)
        self.assertEqual(version_str, "7.0.0")
        self.assertEqual(major, 7)
        self.assertEqual(minor, 0)
        self.assertEqual(patch, 0)
    
    def test_zone_extraction(self):
        """测试区域提取功能"""
        result = extract_interface_info(self.version_config_path, os.path.join(self.temp_dir, "zone_output.json"))
        self.assertTrue(result.get("success", False))
        
        # 验证区域信息
        self.assertIn("zones", result)
        self.assertIn("zones_count", result)
        self.assertEqual(result["zones_count"], 2)
        
        # 验证区域内容
        zone_names = [zone["name"] for zone in result["zones"]]
        self.assertIn("WAN_Zone", zone_names)
        self.assertIn("LAN_Zone", zone_names)
        
        # 验证接口与区域的关联
        interfaces = {i['name']: i for i in result['interfaces']}
        self.assertEqual(interfaces["port1"]["zone"], "WAN_Zone")
        self.assertEqual(interfaces["port2"]["zone"], "LAN_Zone")
    
    def test_mask_to_prefix(self):
        """测试子网掩码转CIDR前缀功能"""
        # 常见掩码测试
        self.assertEqual(mask_to_prefix("*************"), 24)
        self.assertEqual(mask_to_prefix("255.255.255.255"), 32)
        self.assertEqual(mask_to_prefix("255.255.0.0"), 16)
        self.assertEqual(mask_to_prefix("255.0.0.0"), 8)
        
        # 非标准掩码测试
        self.assertEqual(mask_to_prefix("255.255.255.128"), 25)
        self.assertEqual(mask_to_prefix("255.255.255.192"), 26)
        self.assertEqual(mask_to_prefix("***************"), 28)
        
        # 错误掩码处理测试
        try:
            mask_to_prefix("invalid_mask")
            self.fail("应当抛出异常但没有")
        except Exception as e:
            # 验证错误消息包含预期内容
            self.assertTrue("无效的子网掩码格式" in str(e) or "invalid" in str(e).lower(),
                           f"错误消息应包含'无效'或'invalid'，实际为: {str(e)}")

    def test_create_ipv6_config_file(self):
        """测试创建IPv6测试配置并提取信息"""
        # 创建一个包含IPv6配置的测试文件
        ipv6_config_path = os.path.join(self.test_data_dir, "ipv6_config.txt")
        with open(ipv6_config_path, "w", encoding="utf-8") as f:
            f.write("""
#config-version=FGVM64-7.0.0-FW-build0066-201028:opmode=0:vdom=0:user=admin
config system interface
    edit "port1"
        set vdom "root"
        set ip *********** *************
        set allowaccess ping https ssh
        set type physical
        set description "WAN接口"
        set ip6-address 2001:db8::1/64
        set ip6-allowaccess ping https ssh
    next
    edit "port2"
        set vdom "root"
        set ip ******** *************
        set allowaccess ping https ssh
        set type physical
        set description "LAN接口"
        set ip6-mode dhcp
    next
end
""")
        
        try:
            # 读取生成的配置文件内容
            with open(ipv6_config_path, 'r', encoding='utf-8') as f:
                config_content = f.read()
            
            # 验证IPv6配置信息存在于文件中
            self.assertIn("ip6-address 2001:db8::1/64", config_content)
            self.assertIn("ip6-mode dhcp", config_content)
            
            # 验证基本提取功能仍然工作
            ipv6_output_json = os.path.join(self.temp_dir, "ipv6_interfaces.json")
            result = extract_interface_info(ipv6_config_path, ipv6_output_json)
            
            # 验证提取成功
            self.assertTrue(result.get("success", False))
            self.assertTrue(os.path.exists(ipv6_output_json), "未生成IPv6接口信息JSON文件")
            
            # 验证版本信息
            self.assertIn("version", result)
            self.assertEqual(result["version"], "7.0.0")
            
        finally:
            # 清理测试文件
            if os.path.exists(ipv6_config_path):
                os.remove(ipv6_config_path)
            if os.path.exists(os.path.join(self.temp_dir, "ipv6_interfaces.json")):
                os.remove(os.path.join(self.temp_dir, "ipv6_interfaces.json"))

if __name__ == "__main__":
    unittest.main() 