#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Fortigate策略转换测试 - 验证策略转换的可靠性和XML模板/YANG模型集成
"""

import unittest
import os
import sys
import tempfile
import json

# 添加引擎路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from engine.infrastructure.config.config_manager import ConfigManager
from engine.infrastructure.templates.template_manager import TemplateManager
from engine.infrastructure.yang.yang_manager import YangManager
from engine.business.strategies.fortigate_strategy import FortigateConversionStrategy
from engine.business.workflows.conversion_workflow import ConversionWorkflow
from engine.processing.stages.fortigate_policy_stage import FortigatePolicyConversionStage
# 使用兼容性包装器替代已删除的xml_template_integration_stage
from engine.processing.stages.xml_integration.compatibility_wrapper import XmlTemplateIntegrationStageWrapper as XmlTemplateIntegrationStage
from engine.processing.stages.yang_validation_stage import YangValidationStage
from engine.processing.pipeline.pipeline_manager import PipelineManager
from engine.processing.pipeline.data_flow import DataContext


class TestFortigatePolicyConversion(unittest.TestCase):
    """Fortigate策略转换测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.config_manager = ConfigManager()
        self.template_manager = TemplateManager(self.config_manager)
        self.yang_manager = YangManager(self.config_manager)
        
        # 创建测试用的Fortigate配置数据
        self.sample_fortigate_config = {
            "interfaces": [
                {
                    "name": "port1",
                    "type": "physical",
                    "ip": "***********/24",
                    "allowaccess": ["ping", "https", "ssh"]
                },
                {
                    "name": "port2", 
                    "type": "physical",
                    "ip": "********/24",
                    "allowaccess": ["ping"]
                }
            ],
            "address_objects": [
                {
                    "name": "internal_network",
                    "type": "subnet",
                    "subnet": "***********/24"
                },
                {
                    "name": "web_server_vip",
                    "type": "vip",
                    "extip": "************",
                    "mappedip": "*************"
                }
            ],
            "service_objects": [
                {
                    "name": "HTTP",
                    "protocol": "tcp",
                    "portrange": "80"
                },
                {
                    "name": "HTTPS",
                    "protocol": "tcp", 
                    "portrange": "443"
                }
            ],
            "policies": [
                {
                    "policyid": "1",
                    "name": "Allow_Internal_to_Internet",
                    "srcintf": ["port1"],
                    "dstintf": ["port2"],
                    "srcaddr": ["internal_network"],
                    "dstaddr": ["all"],
                    "service": ["HTTP", "HTTPS"],
                    "action": "accept",
                    "nat": "enable",
                    "comments": "Allow internal users to access internet"
                },
                {
                    "policyid": "2",
                    "name": "DNAT_to_Web_Server",
                    "srcintf": ["port2"],
                    "dstintf": ["port1"],
                    "srcaddr": ["all"],
                    "dstaddr": ["web_server_vip"],
                    "service": ["HTTP", "HTTPS"],
                    "action": "accept",
                    "comments": "DNAT to internal web server"
                },
                {
                    "policyid": "3",
                    "name": "Policy_with_Security_Profiles",
                    "srcintf": ["port1"],
                    "dstintf": ["port2"],
                    "srcaddr": ["internal_network"],
                    "dstaddr": ["all"],
                    "service": ["ALL"],
                    "action": "accept",
                    "av_profile": "default",
                    "ips_sensor": "default",
                    "nat": "enable",
                    "comments": "Policy with security profiles"
                }
            ]
        }
    
    def test_fortigate_strategy_validation(self):
        """测试Fortigate策略验证"""
        strategy = FortigateConversionStrategy(
            self.config_manager, self.template_manager, self.yang_manager)
        
        # 创建测试上下文
        context = DataContext()
        context.set_data("parsed_config", self.sample_fortigate_config)
        
        # 执行验证
        validation_result = strategy.validate_input(context)
        
        # 验证结果
        self.assertTrue(validation_result)
        self.assertEqual(context.get_data("valid_policies_count"), 3)
        
        # 检查是否有适当的警告（对于不支持的特性）
        warnings = context.get_warnings()
        self.assertTrue(len(warnings) >= 0)  # 可能有关于不支持特性的警告
    
    def test_fortigate_strategy_conversion_data_preparation(self):
        """测试Fortigate策略转换数据准备"""
        strategy = FortigateConversionStrategy(
            self.config_manager, self.template_manager, self.yang_manager)
        
        # 创建测试上下文
        context = DataContext()
        context.set_data("parsed_config", self.sample_fortigate_config)
        
        # 执行数据准备
        preparation_result = strategy.prepare_conversion_data(context)
        
        # 验证结果
        self.assertTrue(preparation_result)
        
        # 检查生成的数据
        security_policies = context.get_data("security_policies", [])
        nat_rules = context.get_data("nat_rules", [])
        conversion_stats = context.get_data("conversion_stats", {})
        
        self.assertEqual(len(security_policies), 3)  # 所有策略都应该生成安全策略
        self.assertGreaterEqual(len(nat_rules), 1)  # 至少应该有一个NAT规则
        self.assertEqual(conversion_stats["total_policies"], 3)
    
    def test_fortigate_policy_conversion_stage(self):
        """测试Fortigate策略转换阶段"""
        stage = FortigatePolicyConversionStage(
            self.config_manager, self.template_manager, self.yang_manager)
        
        # 创建测试上下文
        context = DataContext()
        context.set_data("parsed_config", self.sample_fortigate_config)
        context.set_data("vendor", "fortigate")
        context.set_data("model", "z5100s")
        context.set_data("version", "R10P2")
        
        # 执行阶段
        stage_result = stage.execute(context)
        
        # 验证结果
        self.assertTrue(stage_result)
        self.assertFalse(context.has_errors())
        
        # 检查转换结果
        xml_integration_data = context.get_data("xml_integration_data")
        self.assertIsNotNone(xml_integration_data)
        self.assertTrue(xml_integration_data.get("template_integration_ready", False))
    
    def test_xml_template_integration_stage(self):
        """测试XML模板集成阶段"""
        # 首先运行策略转换阶段
        policy_stage = FortigatePolicyConversionStage(
            self.config_manager, self.template_manager, self.yang_manager)
        
        context = DataContext()
        context.set_data("parsed_config", self.sample_fortigate_config)
        context.set_data("vendor", "fortigate")
        context.set_data("model", "z5100s")
        context.set_data("version", "R10P2")
        
        policy_stage.execute(context)
        
        # 然后运行XML集成阶段
        xml_stage = XmlTemplateIntegrationStage(
            self.config_manager, self.template_manager)
        
        try:
            xml_result = xml_stage.execute(context)
            
            if xml_result:
                # 检查生成的XML
                generated_xml = context.get_data("generated_xml")
                self.assertIsNotNone(generated_xml)
                self.assertIn("<?xml", generated_xml)
                self.assertIn("<config", generated_xml)
                
                # 检查XML统计
                xml_stats = context.get_data("xml_stats", {})
                self.assertGreater(xml_stats.get("xml_length", 0), 0)
            else:
                # 如果模板不存在，这是正常的
                self.assertTrue(context.has_errors())
                
        except Exception as e:
            # 在测试环境中模板可能不存在，这是正常的
            self.assertIn("template", str(e).lower())
    
    def test_yang_validation_stage(self):
        """测试YANG验证阶段"""
        yang_stage = YangValidationStage(self.config_manager, self.yang_manager)
        
        # 创建测试上下文，包含示例XML
        context = DataContext()
        context.set_data("generated_xml", """<?xml version="1.0" encoding="UTF-8"?>
<config xmlns="urn:ruijie:ntos">
    <vrf>
        <name>default</name>
        <security-policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
            <policy>
                <name>test_policy</name>
                <action>permit</action>
                <config-source>fortigate</config-source>
            </policy>
        </security-policy>
    </vrf>
</config>""")
        context.set_data("model", "z5100s")
        context.set_data("version", "R10P2")
        
        # 执行YANG验证
        validation_result = yang_stage.execute(context)
        
        # 验证结果（yanglint可能不可用，这是正常的）
        self.assertTrue(validation_result)
        
        yang_validation_result = context.get_data("yang_validation_result")
        self.assertIsNotNone(yang_validation_result)
        
        if yang_validation_result.get("performed", False):
            # 如果执行了验证，检查结果
            self.assertIn("passed", yang_validation_result)
        else:
            # 如果跳过了验证，检查原因
            self.assertTrue(yang_validation_result.get("skipped", False))
    
    def test_complete_fortigate_conversion_pipeline(self):
        """测试完整的Fortigate转换管道"""
        # 创建完整的转换管道
        pipeline = PipelineManager("test_fortigate_conversion", "测试Fortigate转换管道")
        
        # 添加所有阶段
        policy_stage = FortigatePolicyConversionStage(
            self.config_manager, self.template_manager, self.yang_manager)
        xml_stage = XmlTemplateIntegrationStage(
            self.config_manager, self.template_manager)
        yang_stage = YangValidationStage(
            self.config_manager, self.yang_manager)
        
        pipeline.add_stage(policy_stage)
        pipeline.add_stage(xml_stage)
        pipeline.add_stage(yang_stage)
        
        # 准备输入数据
        initial_data = {
            "parsed_config": self.sample_fortigate_config,
            "vendor": "fortigate",
            "model": "z5100s",
            "version": "R10P2"
        }
        
        # 执行管道
        result_context = pipeline.execute(initial_data)
        
        # 验证管道执行结果
        self.assertIn(result_context.state, ["completed", "failed"])
        
        # 检查阶段执行历史
        stage_history = result_context.get_stage_history()
        self.assertEqual(len(stage_history), 3)
        
        # 检查每个阶段的执行状态
        for stage_record in stage_history:
            self.assertIn(stage_record["status"], ["completed", "failed", "skipped"])
    
    def test_conversion_workflow_with_fortigate_pipeline(self):
        """测试使用Fortigate管道的转换工作流"""
        workflow = ConversionWorkflow(
            self.config_manager, self.template_manager, self.yang_manager)
        
        # 创建临时配置文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.conf', delete=False) as tmp_file:
            tmp_file.write("""config firewall policy
    edit 1
        set name "test_policy"
        set srcintf "port1"
        set dstintf "port2"
        set srcaddr "all"
        set dstaddr "all"
        set service "ALL"
        set action accept
    next
end""")
            tmp_file_path = tmp_file.name
        
        try:
            # 准备转换参数
            conversion_params = {
                'cli_file': tmp_file_path,
                'vendor': 'fortigate',
                'model': 'z5100s',
                'version': 'R10P2',
                'output_file': tempfile.mktemp(suffix='.xml')
            }
            
            # 执行转换
            result = workflow.execute_conversion(conversion_params)
            
            # 验证结果
            self.assertIsInstance(result, dict)
            self.assertIn('success', result)
            self.assertIn('workflow_version', result)
            
            # 如果成功，检查详细信息
            if result.get('success'):
                self.assertEqual(result.get('conversion_method'), 'pipeline')
                self.assertIn('pipeline_info', result)
            
        finally:
            # 清理临时文件
            if os.path.exists(tmp_file_path):
                os.unlink(tmp_file_path)
            
            output_file = conversion_params.get('output_file')
            if output_file and os.path.exists(output_file):
                os.unlink(output_file)


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
