#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
国际化系统自动化测试
验证国际化功能正确性，确保中英文翻译准确性和一致性
"""

import unittest
import os
import json
import tempfile
import shutil
from pathlib import Path
from unittest.mock import patch, MagicMock

# 添加engine路径到sys.path
import sys
engine_dir = Path(__file__).parent.parent
sys.path.insert(0, str(engine_dir))

from utils.i18n import (
    translate, safe_translate, _, 
    load_translations, normalize_language_code,
    get_cache_stats, clear_translation_cache,
    validate_translation_integrity,
    init_i18n, get_current_language
)

class TestI18nSystem(unittest.TestCase):
    """国际化系统测试"""
    
    def setUp(self):
        """测试前准备"""
        self.test_dir = tempfile.mkdtemp()
        self.locale_dir = os.path.join(self.test_dir, "locales")
        os.makedirs(self.locale_dir)
        
        # 创建测试翻译文件
        self.zh_translations = {
            "test.message": "测试消息",
            "test.with_param": "参数测试: {name}",
            "test.multiple_params": "多参数: {name}, {count}",
            "error.not_found": "未找到: {item}",
            "info.success": "成功完成",
            "warning.deprecated": "已弃用功能"
        }
        
        self.en_translations = {
            "test.message": "Test message",
            "test.with_param": "Parameter test: {name}",
            "test.multiple_params": "Multiple params: {name}, {count}",
            "error.not_found": "Not found: {item}",
            "info.success": "Successfully completed",
            "warning.deprecated": "Deprecated feature"
        }
        
        # 保存测试翻译文件
        with open(os.path.join(self.locale_dir, "zh-CN.json"), 'w', encoding='utf-8') as f:
            json.dump(self.zh_translations, f, ensure_ascii=False, indent=2)
        
        with open(os.path.join(self.locale_dir, "en-US.json"), 'w', encoding='utf-8') as f:
            json.dump(self.en_translations, f, ensure_ascii=False, indent=2)
        
        # 清空缓存
        clear_translation_cache()
    
    def tearDown(self):
        """测试后清理"""
        shutil.rmtree(self.test_dir)
        clear_translation_cache()
    
    def test_language_code_normalization(self):
        """测试语言代码规范化"""
        test_cases = [
            ("zh_CN", "zh-CN"),
            ("zh-cn", "zh-CN"),
            ("ZH-CN", "zh-CN"),
            ("en", "en-US"),
            ("zh", "zh-CN"),
            ("invalid", "zh-CN"),  # 默认语言
            ("", "zh-CN"),  # 空字符串
        ]
        
        for input_lang, expected in test_cases:
            with self.subTest(input_lang=input_lang):
                result = normalize_language_code(input_lang)
                self.assertEqual(result, expected)
    
    @patch('engine.utils.i18n.LOCALE_DIR')
    def test_translation_loading(self, mock_locale_dir):
        """测试翻译文件加载"""
        mock_locale_dir.__str__ = lambda: self.locale_dir
        mock_locale_dir.__fspath__ = lambda: self.locale_dir
        
        # 测试中文翻译加载
        zh_translations = load_translations("zh-CN")
        self.assertEqual(zh_translations["test.message"], "测试消息")
        
        # 测试英文翻译加载
        en_translations = load_translations("en-US")
        self.assertEqual(en_translations["test.message"], "Test message")
    
    @patch('engine.utils.i18n.LOCALE_DIR')
    def test_basic_translation(self, mock_locale_dir):
        """测试基本翻译功能"""
        mock_locale_dir.__str__ = lambda: self.locale_dir
        mock_locale_dir.__fspath__ = lambda: self.locale_dir
        
        # 测试中文翻译
        result = translate("test.message", "zh-CN")
        self.assertEqual(result, "测试消息")
        
        # 测试英文翻译
        result = translate("test.message", "en-US")
        self.assertEqual(result, "Test message")
        
        # 测试不存在的键
        result = translate("nonexistent.key", "zh-CN")
        self.assertEqual(result, "nonexistent.key")
    
    @patch('engine.utils.i18n.LOCALE_DIR')
    def test_parameter_substitution(self, mock_locale_dir):
        """测试参数替换"""
        mock_locale_dir.__str__ = lambda: self.locale_dir
        mock_locale_dir.__fspath__ = lambda: self.locale_dir
        
        # 测试单参数
        result = translate("test.with_param", "zh-CN", name="张三")
        self.assertEqual(result, "参数测试: 张三")
        
        # 测试多参数
        result = translate("test.multiple_params", "zh-CN", name="李四", count=5)
        self.assertEqual(result, "多参数: 李四, 5")
        
        # 测试缺失参数
        result = translate("test.with_param", "zh-CN")
        self.assertIn("参数测试", result)  # 应该返回未格式化的文本
    
    @patch('engine.utils.i18n.LOCALE_DIR')
    def test_safe_translate_function(self, mock_locale_dir):
        """测试safe_translate函数"""
        mock_locale_dir.__str__ = lambda: self.locale_dir
        mock_locale_dir.__fspath__ = lambda: self.locale_dir
        
        # 测试正常翻译
        result = safe_translate("test.message", "zh-CN")
        self.assertEqual(result, "测试消息")
        
        # 测试参数替换
        result = safe_translate("test.with_param", "zh-CN", name="王五")
        self.assertEqual(result, "参数测试: 王五")
        
        # 测试错误恢复
        result = safe_translate("", "zh-CN")
        self.assertEqual(result, "")
        
        # 测试非字符串输入
        result = safe_translate(None, "zh-CN")
        self.assertEqual(result, "")
        
        result = safe_translate(123, "zh-CN")
        self.assertEqual(result, "123")
    
    @patch('engine.utils.i18n.LOCALE_DIR')
    def test_underscore_function(self, mock_locale_dir):
        """测试_()函数"""
        mock_locale_dir.__str__ = lambda: self.locale_dir
        mock_locale_dir.__fspath__ = lambda: self.locale_dir
        
        # 模拟当前语言设置
        with patch('engine.utils.i18n._current_language', 'zh-CN'):
            result = _("test.message")
            self.assertEqual(result, "测试消息")
            
            result = _("test.with_param", name="赵六")
            self.assertEqual(result, "参数测试: 赵六")
    
    @patch('engine.utils.i18n.LOCALE_DIR')
    def test_translation_cache(self, mock_locale_dir):
        """测试翻译缓存"""
        mock_locale_dir.__str__ = lambda: self.locale_dir
        mock_locale_dir.__fspath__ = lambda: self.locale_dir
        
        # 清空缓存
        clear_translation_cache()
        
        # 第一次翻译（缓存未命中）
        result1 = translate("test.message", "zh-CN")
        stats1 = get_cache_stats()
        
        # 第二次翻译（缓存命中）
        result2 = translate("test.message", "zh-CN")
        stats2 = get_cache_stats()
        
        self.assertEqual(result1, result2)
        self.assertGreater(stats2['hit_count'], stats1['hit_count'])
    
    @patch('engine.utils.i18n.LOCALE_DIR')
    def test_fallback_language(self, mock_locale_dir):
        """测试回退语言机制"""
        mock_locale_dir.__str__ = lambda: self.locale_dir
        mock_locale_dir.__fspath__ = lambda: self.locale_dir
        
        # 创建只有英文翻译的键
        en_only_key = "test.english_only"
        en_translations = load_translations("en-US")
        en_translations[en_only_key] = "English only text"
        
        with open(os.path.join(self.locale_dir, "en-US.json"), 'w', encoding='utf-8') as f:
            json.dump(en_translations, f, ensure_ascii=False, indent=2)
        
        # 清空缓存以重新加载
        clear_translation_cache()
        
        # 请求中文翻译，应该回退到英文
        with patch('engine.utils.i18n._fallback_languages', ['en-US', 'zh-CN']):
            result = translate(en_only_key, "zh-CN")
            self.assertEqual(result, "English only text")
    
    @patch('engine.utils.i18n.LOCALE_DIR')
    def test_translation_integrity_validation(self, mock_locale_dir):
        """测试翻译完整性验证"""
        mock_locale_dir.__str__ = lambda: self.locale_dir
        mock_locale_dir.__fspath__ = lambda: self.locale_dir
        
        # 创建参数不匹配的翻译
        zh_translations = self.zh_translations.copy()
        zh_translations["test.param_mismatch"] = "参数不匹配: {name}"
        
        en_translations = self.en_translations.copy()
        en_translations["test.param_mismatch"] = "Parameter mismatch: {different_name}"
        
        with open(os.path.join(self.locale_dir, "zh-CN.json"), 'w', encoding='utf-8') as f:
            json.dump(zh_translations, f, ensure_ascii=False, indent=2)
        
        with open(os.path.join(self.locale_dir, "en-US.json"), 'w', encoding='utf-8') as f:
            json.dump(en_translations, f, ensure_ascii=False, indent=2)
        
        # 验证翻译完整性
        issues = validate_translation_integrity()
        
        # 应该检测到参数不匹配
        param_mismatches = issues.get('parameter_mismatches', [])
        self.assertTrue(any(
            issue['key'] == 'test.param_mismatch' 
            for issue in param_mismatches
        ))
    
    def test_error_handling(self):
        """测试错误处理"""
        # 测试无效的翻译文件路径
        with patch('engine.utils.i18n.LOCALE_DIR', '/nonexistent/path'):
            result = translate("test.key", "zh-CN")
            self.assertEqual(result, "test.key")
        
        # 测试JSON解析错误
        invalid_json_file = os.path.join(self.locale_dir, "invalid.json")
        with open(invalid_json_file, 'w') as f:
            f.write("invalid json content")
        
        with patch('engine.utils.i18n.LOCALE_DIR', self.locale_dir):
            # 应该不会抛出异常
            result = load_translations("invalid")
            self.assertEqual(result, {})
    
    @patch('engine.utils.i18n.LOCALE_DIR')
    def test_performance(self, mock_locale_dir):
        """测试性能"""
        mock_locale_dir.__str__ = lambda: self.locale_dir
        mock_locale_dir.__fspath__ = lambda: self.locale_dir
        
        import time
        
        # 测试大量翻译的性能
        start_time = time.time()
        
        for i in range(1000):
            translate("test.message", "zh-CN")
        
        end_time = time.time()
        elapsed = end_time - start_time
        
        # 1000次翻译应该在1秒内完成（有缓存）
        self.assertLess(elapsed, 1.0)
        
        # 检查缓存命中率
        stats = get_cache_stats()
        self.assertGreater(stats['hit_rate'], 90)  # 命中率应该超过90%
    
    def test_concurrent_access(self):
        """测试并发访问"""
        import threading
        import time
        
        results = []
        errors = []
        
        def translate_worker():
            try:
                for i in range(100):
                    result = safe_translate("test.message", "zh-CN")
                    results.append(result)
            except Exception as e:
                errors.append(e)
        
        # 创建多个线程
        threads = []
        for i in range(10):
            thread = threading.Thread(target=translate_worker)
            threads.append(thread)
        
        # 启动所有线程
        for thread in threads:
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 检查结果
        self.assertEqual(len(errors), 0)  # 不应该有错误
        self.assertEqual(len(results), 1000)  # 应该有1000个结果

class TestI18nIntegration(unittest.TestCase):
    """国际化集成测试"""
    
    def test_logger_integration(self):
        """测试与日志系统的集成"""
        from utils.logger import log
        
        # 测试日志国际化
        try:
            log("test.message", "info")
            # 如果没有抛出异常，说明集成正常
            self.assertTrue(True)
        except Exception as e:
            self.fail(f"日志国际化集成失败: {str(e)}")
    
    def test_config_system_integration(self):
        """测试与配置系统的集成"""
        # 测试语言设置
        try:
            init_i18n("en-US")
            current_lang = get_current_language()
            self.assertEqual(current_lang, "en-US")
        except Exception as e:
            self.fail(f"配置系统集成失败: {str(e)}")

def run_i18n_tests():
    """运行所有国际化测试"""
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_suite.addTest(unittest.makeSuite(TestI18nSystem))
    test_suite.addTest(unittest.makeSuite(TestI18nIntegration))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 返回测试结果
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_i18n_tests()
    exit(0 if success else 1)
