#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
综合集成测试 - 验证整个重构架构的集成功能
"""

import unittest
import os
import sys
import tempfile
import time
import json

# 添加引擎路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from engine.application.conversion_service import ConversionService
from engine.business.workflows.conversion_workflow import ConversionWorkflow
from engine.infrastructure.config.config_manager import ConfigManager
from engine.infrastructure.templates.template_manager import TemplateManager
from engine.infrastructure.yang.yang_manager import YangManager
from engine.processing.pipeline.pipeline_manager import PipelineManager
from engine.processing.parsers.parser_registry import ParserRegistry
from engine.processing.generators.generator_registry import GeneratorRegistry
from engine.infrastructure.common.performance import PerformanceMonitor
from engine.infrastructure.common.error_handling import ErrorHandler
from engine.infrastructure.common.caching import CacheManager


class TestComprehensiveIntegration(unittest.TestCase):
    """综合集成测试类"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        cls.config_manager = ConfigManager()
        cls.template_manager = TemplateManager(cls.config_manager)
        cls.yang_manager = YangManager(cls.config_manager)
        cls.conversion_service = ConversionService(cls.config_manager)
        
        # 创建测试用的Fortigate配置
        cls.sample_fortigate_config = """config system global
    set hostname "integration-test-fw"
    set timezone "Asia/Shanghai"
end

config system interface
    edit "port1"
        set vdom "root"
        set ip *********** *************
        set allowaccess ping https ssh
        set type physical
        set alias "Internal"
    next
    edit "port2"
        set vdom "root"
        set ip ******** *************
        set allowaccess ping
        set type physical
        set alias "External"
    next
end

config firewall address
    edit "internal_network"
        set subnet *********** *************
        set comment "Internal network"
    next
    edit "web_server_vip"
        set type vip
        set extip ************
        set mappedip *************
        set comment "Web server VIP"
    next
end

config firewall service custom
    edit "HTTP"
        set tcp-portrange 80
        set comment "HTTP service"
    next
    edit "HTTPS"
        set tcp-portrange 443
        set comment "HTTPS service"
    next
end

config firewall policy
    edit 1
        set name "Allow_Internal_to_Internet"
        set srcintf "port1"
        set dstintf "port2"
        set srcaddr "internal_network"
        set dstaddr "all"
        set service "HTTP" "HTTPS"
        set action accept
        set nat enable
        set comments "Allow internal users to access internet with SNAT"
    next
    edit 2
        set name "DNAT_to_Web_Server"
        set srcintf "port2"
        set dstintf "port1"
        set srcaddr "all"
        set dstaddr "web_server_vip"
        set service "HTTP" "HTTPS"
        set action accept
        set comments "DNAT to internal web server"
    next
    edit 3
        set name "Policy_with_Security_Profiles"
        set srcintf "port1"
        set dstintf "port2"
        set srcaddr "internal_network"
        set dstaddr "all"
        set service "ALL"
        set action accept
        set av-profile "default"
        set ips-sensor "default"
        set nat enable
        set comments "Policy with security profiles"
    next
end"""
    
    def test_full_architecture_integration(self):
        """测试完整架构集成"""
        # 验证所有核心组件都能正常初始化
        self.assertIsNotNone(self.config_manager)
        self.assertIsNotNone(self.template_manager)
        self.assertIsNotNone(self.yang_manager)
        self.assertIsNotNone(self.conversion_service)
        
        # 验证组件间的依赖关系
        self.assertIs(self.conversion_service.config_manager, self.config_manager)
        self.assertIsNotNone(self.conversion_service.conversion_workflow)
        
        # 验证性能监控集成
        workflow = self.conversion_service.conversion_workflow
        self.assertIsNotNone(workflow.performance_monitor)
        self.assertIsNotNone(workflow.memory_manager)
        self.assertIsNotNone(workflow.error_handler)
    
    def test_parser_generator_integration(self):
        """测试解析器和生成器集成"""
        parser_registry = ParserRegistry()
        generator_registry = GeneratorRegistry()
        
        # 验证解析器注册
        self.assertTrue(parser_registry.is_vendor_supported("fortigate"))
        parser = parser_registry.get_parser("fortigate")
        self.assertIsNotNone(parser)
        
        # 验证生成器注册
        self.assertTrue(generator_registry.is_generator_supported("security-policy"))
        generator = generator_registry.get_generator("security-policy")
        self.assertIsNotNone(generator)
        
        # 验证解析器和生成器的协同工作
        parser_info = parser.get_plugin_info()
        generator_info = generator.get_generator_info()
        
        self.assertEqual(parser_info["vendor"], "fortigate")
        self.assertEqual(generator_info["generator_type"], "security-policy")
    
    def test_pipeline_processing_integration(self):
        """测试管道处理集成"""
        from engine.processing.stages.fortigate_policy_stage import FortigatePolicyConversionStage
        from engine.processing.stages.xml_template_integration_stage import XmlTemplateIntegrationStage
        from engine.processing.stages.yang_validation_stage import YangValidationStage
        
        # 创建完整的处理管道
        pipeline = PipelineManager("integration_test_pipeline", "集成测试管道")
        
        # 添加所有阶段
        fortigate_stage = FortigatePolicyConversionStage(
            self.config_manager, self.template_manager, self.yang_manager)
        xml_stage = XmlTemplateIntegrationStage(
            self.config_manager, self.template_manager)
        yang_stage = YangValidationStage(
            self.config_manager, self.yang_manager)
        
        pipeline.add_stage(fortigate_stage)
        pipeline.add_stage(xml_stage)
        pipeline.add_stage(yang_stage)
        
        # 验证管道配置
        validation = pipeline.validate_pipeline()
        self.assertTrue(validation['valid'])
        self.assertEqual(validation['stage_count'], 3)
        
        # 验证管道信息
        info = pipeline.get_pipeline_info()
        self.assertEqual(info['stage_count'], 3)
        self.assertTrue(info['enabled'])
    
    def test_performance_monitoring_integration(self):
        """测试性能监控集成"""
        monitor = PerformanceMonitor()
        
        # 测试多个操作的性能监控
        operations = []
        for i in range(10):
            context = monitor.start_operation(f"integration_test_{i}")
            
            # 模拟一些工作
            time.sleep(0.01)
            
            metrics = monitor.end_operation(context, success=True)
            operations.append(metrics)
        
        # 验证性能指标
        self.assertEqual(len(operations), 10)
        
        # 验证统计信息
        summary = monitor.get_performance_summary()
        self.assertEqual(summary['total_operations'], 10)
        self.assertGreater(summary['recent_avg_duration'], 0)
        
        # 验证操作统计
        for i in range(10):
            stats = monitor.get_operation_stats(f"integration_test_{i}")
            self.assertEqual(stats['total_count'], 1)
            self.assertEqual(stats['success_count'], 1)
    
    def test_error_handling_integration(self):
        """测试错误处理集成"""
        error_handler = ErrorHandler()
        
        # 测试不同类型的错误处理
        test_errors = [
            FileNotFoundError("Test file not found"),
            ValueError("Test value error"),
            ConnectionError("Test connection error"),
            MemoryError("Test memory error")
        ]
        
        for error in test_errors:
            result = error_handler.handle_error(error, {"test": "context"})
            
            self.assertTrue(result['handled'])
            self.assertIn('error_record', result)
            self.assertIn('severity', result)
            self.assertEqual(result['error_record'].error_type, type(error).__name__)
        
        # 验证错误统计
        stats = error_handler.get_error_stats()
        self.assertEqual(stats['total_errors'], 4)
        self.assertEqual(len(stats['error_types']), 4)
    
    def test_caching_system_integration(self):
        """测试缓存系统集成"""
        cache_manager = CacheManager()
        
        # 创建多个缓存
        cache1 = cache_manager.create_cache("test_cache_1", max_size=10)
        cache2 = cache_manager.create_cache("test_cache_2", max_size=20, default_ttl=60)
        
        # 测试缓存操作
        for i in range(15):
            cache1.put(f"key_{i}", f"value_{i}")
            cache2.put(f"key_{i}", f"value_{i}")
        
        # 验证LRU驱逐
        self.assertEqual(cache1.get_stats()['size'], 10)  # 应该被限制在10
        self.assertEqual(cache2.get_stats()['size'], 15)  # 应该是15
        
        # 验证缓存命中
        hit_count = 0
        for i in range(15):
            if cache1.get(f"key_{i}") is not None:
                hit_count += 1
        
        self.assertGreater(hit_count, 0)  # 应该有一些命中
        
        # 验证统计信息
        all_stats = cache_manager.get_all_stats()
        self.assertIn("test_cache_1", all_stats)
        self.assertIn("test_cache_2", all_stats)
    
    def test_end_to_end_conversion_workflow(self):
        """测试端到端转换工作流"""
        # 创建临时配置文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.conf', delete=False, encoding='utf-8') as tmp_file:
            tmp_file.write(self.sample_fortigate_config)
            config_file_path = tmp_file.name
        
        try:
            # 准备转换参数
            conversion_params = {
                'cli_file': config_file_path,
                'vendor': 'fortigate',
                'model': 'z5100s',
                'version': 'R10P2',
                'output_file': tempfile.mktemp(suffix='.xml'),
                'verbose': False
            }
            
            # 执行转换
            start_time = time.time()
            result = self.conversion_service.convert(**conversion_params)
            duration = time.time() - start_time
            
            # 验证结果结构
            self.assertIsInstance(result, dict)
            self.assertIn('success', result)
            self.assertIn('workflow_version', result)
            
            # 验证性能信息
            if 'performance_metrics' in result:
                perf = result['performance_metrics']
                self.assertIn('duration', perf)
                self.assertIn('memory_used_mb', perf)
                self.assertGreater(perf['duration'], 0)
            
            # 验证转换方法
            if result.get('success'):
                self.assertIn('conversion_method', result)
                # 对于Fortigate应该使用pipeline方法
                if result['conversion_method'] == 'pipeline':
                    self.assertIn('pipeline_info', result)
            
            # 记录性能指标
            print(f"\n端到端转换性能: {duration:.3f}秒")
            if 'performance_metrics' in result:
                perf = result['performance_metrics']
                print(f"内部性能指标: {perf.get('duration', 0):.3f}秒, {perf.get('memory_used_mb', 0):.2f}MB")
        
        finally:
            # 清理临时文件
            if os.path.exists(config_file_path):
                os.unlink(config_file_path)
            
            output_file = conversion_params.get('output_file')
            if output_file and os.path.exists(output_file):
                os.unlink(output_file)
    
    def test_concurrent_operations(self):
        """测试并发操作"""
        import threading
        import queue
        
        results_queue = queue.Queue()
        
        def worker_conversion(worker_id):
            """工作线程函数"""
            try:
                # 创建临时配置文件
                with tempfile.NamedTemporaryFile(mode='w', suffix='.conf', delete=False, encoding='utf-8') as tmp_file:
                    tmp_file.write(f"""config firewall policy
    edit {worker_id}
        set name "worker_policy_{worker_id}"
        set srcintf "port1"
        set dstintf "port2"
        set srcaddr "all"
        set dstaddr "all"
        set service "ALL"
        set action accept
    next
end""")
                    config_file_path = tmp_file.name
                
                # 执行转换
                conversion_params = {
                    'cli_file': config_file_path,
                    'vendor': 'fortigate',
                    'model': 'z5100s',
                    'version': 'R10P2',
                    'output_file': tempfile.mktemp(suffix='.xml'),
                    'verbose': False
                }
                
                start_time = time.time()
                result = self.conversion_service.convert(**conversion_params)
                duration = time.time() - start_time
                
                results_queue.put({
                    'worker_id': worker_id,
                    'success': result.get('success', False),
                    'duration': duration,
                    'result': result
                })
                
                # 清理
                if os.path.exists(config_file_path):
                    os.unlink(config_file_path)
                
                output_file = conversion_params.get('output_file')
                if output_file and os.path.exists(output_file):
                    os.unlink(output_file)
                    
            except Exception as e:
                results_queue.put({
                    'worker_id': worker_id,
                    'success': False,
                    'error': str(e)
                })
        
        # 启动多个工作线程
        threads = []
        num_workers = 5
        
        for i in range(num_workers):
            thread = threading.Thread(target=worker_conversion, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join(timeout=30)  # 30秒超时
        
        # 收集结果
        results = []
        while not results_queue.empty():
            results.append(results_queue.get())
        
        # 验证并发结果
        self.assertEqual(len(results), num_workers)
        
        successful_results = [r for r in results if r.get('success', False)]
        failed_results = [r for r in results if not r.get('success', False)]
        
        print(f"\n并发测试结果: {len(successful_results)}/{num_workers} 成功")
        
        if successful_results:
            avg_duration = sum(r['duration'] for r in successful_results) / len(successful_results)
            print(f"平均转换时间: {avg_duration:.3f}秒")
        
        # 至少应该有一些成功的结果
        self.assertGreater(len(successful_results), 0)
    
    def test_memory_usage_under_load(self):
        """测试负载下的内存使用"""
        from engine.infrastructure.common.performance import MemoryManager
        
        memory_manager = MemoryManager()
        
        # 记录初始内存
        initial_memory = memory_manager.check_memory_usage()
        
        # 执行多次转换操作
        for i in range(20):
            with tempfile.NamedTemporaryFile(mode='w', suffix='.conf', delete=False, encoding='utf-8') as tmp_file:
                tmp_file.write(f"""config firewall policy
    edit {i}
        set name "load_test_policy_{i}"
        set srcintf "port1"
        set dstintf "port2"
        set srcaddr "all"
        set dstaddr "all"
        set service "ALL"
        set action accept
    next
end""")
                config_file_path = tmp_file.name
            
            try:
                conversion_params = {
                    'cli_file': config_file_path,
                    'vendor': 'fortigate',
                    'model': 'z5100s',
                    'version': 'R10P2',
                    'output_file': tempfile.mktemp(suffix='.xml'),
                    'verbose': False
                }
                
                result = self.conversion_service.convert(**conversion_params)
                
                # 每5次操作检查一次内存
                if i % 5 == 0:
                    current_memory = memory_manager.check_memory_usage()
                    if current_memory['threshold_exceeded']:
                        optimization_result = memory_manager.optimize_memory()
                        if optimization_result['optimized']:
                            print(f"内存优化: 释放了 {optimization_result['memory_freed_mb']:.2f}MB")
            
            finally:
                # 清理
                if os.path.exists(config_file_path):
                    os.unlink(config_file_path)
                
                output_file = conversion_params.get('output_file')
                if output_file and os.path.exists(output_file):
                    os.unlink(output_file)
        
        # 记录最终内存
        final_memory = memory_manager.check_memory_usage()
        
        print(f"\n内存使用测试:")
        print(f"初始内存: {initial_memory['rss_mb']:.2f}MB")
        print(f"最终内存: {final_memory['rss_mb']:.2f}MB")
        print(f"内存增长: {final_memory['rss_mb'] - initial_memory['rss_mb']:.2f}MB")
        
        # 验证内存增长在合理范围内
        memory_growth = final_memory['rss_mb'] - initial_memory['rss_mb']
        self.assertLess(memory_growth, 50.0)  # 内存增长应该小于50MB


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
