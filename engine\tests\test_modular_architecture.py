#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
模块化架构测试 - 验证解析器和生成器的模块化重构
"""

import unittest
import os
import sys
import tempfile

# 添加引擎路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from engine.processing.parsers.parser_registry import ParserRegistry
from engine.processing.parsers.fortigate_parser_adapter import FortigateParserAdapter
from engine.processing.generators.generator_registry import GeneratorRegistry
from engine.processing.generators.security_policy_generator_adapter import SecurityPolicyGeneratorAdapter
from engine.business.workflows.conversion_workflow import ConversionWorkflow
from engine.infrastructure.config.config_manager import ConfigManager
from engine.infrastructure.templates.template_manager import TemplateManager
from engine.infrastructure.yang.yang_manager import YangManager


class TestModularArchitecture(unittest.TestCase):
    """模块化架构测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.config_manager = ConfigManager()
        self.template_manager = TemplateManager(self.config_manager)
        self.yang_manager = YangManager(self.config_manager)
    
    def test_parser_registry(self):
        """测试解析器注册表"""
        registry = ParserRegistry()
        
        # 测试基本功能
        self.assertTrue(registry.is_vendor_supported("fortigate"))
        self.assertIn("fortigate", registry.get_supported_vendors())
        
        # 测试解析器获取
        parser = registry.get_parser("fortigate")
        self.assertIsNotNone(parser)
        self.assertIsInstance(parser, FortigateParserAdapter)
        
        # 测试缓存功能
        parser2 = registry.get_parser("fortigate")
        self.assertIs(parser, parser2)  # 应该是同一个实例
        
        # 测试统计信息
        stats = registry.get_registry_stats()
        self.assertGreater(stats["registered_parsers"], 0)
        self.assertIn("fortigate", stats["supported_vendors"])
    
    def test_fortigate_parser_adapter(self):
        """测试Fortigate解析器适配器"""
        parser = FortigateParserAdapter()
        
        # 测试插件信息
        info = parser.get_plugin_info()
        self.assertEqual(info["vendor"], "fortigate")
        self.assertIn("6.0", info["supported_versions"])
        self.assertTrue(info["capabilities"]["config_parsing"])
        
        # 测试版本支持检查
        self.assertTrue(parser.is_version_supported("6.4"))
        self.assertTrue(parser.is_version_supported("7.0"))
        
        # 创建测试配置文件
        test_config = """config system global
    set hostname "test-firewall"
end

config firewall policy
    edit 1
        set name "test_policy"
        set srcintf "port1"
        set dstintf "port2"
        set srcaddr "all"
        set dstaddr "all"
        set service "ALL"
        set action accept
    next
end"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.conf', delete=False) as tmp_file:
            tmp_file.write(test_config)
            tmp_file_path = tmp_file.name
        
        try:
            # 测试格式验证
            is_valid = parser.validate_config_format(tmp_file_path)
            self.assertTrue(is_valid)
            
            # 测试配置解析（如果现有解析器可用）
            try:
                parsed_data = parser.parse_config_file(tmp_file_path)
                self.assertIsInstance(parsed_data, dict)
                
                # 测试策略提取
                policies = parser.extract_policies(tmp_file_path)
                self.assertIsInstance(policies, list)
                
            except Exception as e:
                # 在测试环境中现有解析器可能不可用，这是正常的
                self.assertIn("import", str(e).lower())
                
        finally:
            os.unlink(tmp_file_path)
    
    def test_generator_registry(self):
        """测试生成器注册表"""
        registry = GeneratorRegistry()
        
        # 测试基本功能
        self.assertTrue(registry.is_generator_supported("security-policy"))
        self.assertIn("security-policy", registry.get_supported_generators())
        
        # 测试生成器获取
        generator = registry.get_generator("security-policy")
        self.assertIsNotNone(generator)
        self.assertIsInstance(generator, SecurityPolicyGeneratorAdapter)
        
        # 测试缓存功能
        generator2 = registry.get_generator("security-policy")
        self.assertIs(generator, generator2)  # 应该是同一个实例
        
        # 测试统计信息
        stats = registry.get_registry_stats()
        self.assertGreater(stats["registered_generators"], 0)
        self.assertIn("security-policy", stats["supported_types"])
    
    def test_security_policy_generator_adapter(self):
        """测试安全策略生成器适配器"""
        generator = SecurityPolicyGeneratorAdapter()
        
        # 测试生成器信息
        info = generator.get_generator_info()
        self.assertEqual(info["generator_type"], "security-policy")
        self.assertEqual(info["target_format"], "xml")
        self.assertTrue(info["capabilities"]["xml_element_generation"])
        
        # 测试命名空间
        namespace_uri = generator.get_namespace_uri()
        self.assertEqual(namespace_uri, "urn:ruijie:ntos:params:xml:ns:yang:security-policy")
        
        # 测试数据验证
        valid_data = {
            "name": "test_policy",
            "action": "permit",
            "config-source": "fortigate"
        }
        self.assertTrue(generator.validate_input_data(valid_data))
        
        invalid_data = {
            "name": "test_policy"
            # 缺少action字段
        }
        self.assertFalse(generator.validate_input_data(invalid_data))
        
        # 测试XML元素生成
        try:
            element = generator.generate_xml_element(valid_data)
            self.assertEqual(element.tag, "policy")
            
            # 检查子元素
            name_element = element.find("name")
            self.assertIsNotNone(name_element)
            self.assertEqual(name_element.text, "test_policy")
            
            action_element = element.find("action")
            self.assertIsNotNone(action_element)
            self.assertEqual(action_element.text, "permit")
            
        except Exception as e:
            # 如果现有生成器不可用，这是正常的
            print(f"XML generation test skipped: {e}")
        
        # 测试XML字符串生成
        try:
            xml_string = generator.generate_xml_string(valid_data)
            self.assertIn("<security-policy", xml_string)
            self.assertIn("<policy>", xml_string)
            self.assertIn("<name>test_policy</name>", xml_string)
            
        except Exception as e:
            # 如果现有生成器不可用，这是正常的
            print(f"XML string generation test skipped: {e}")
    
    def test_integrated_workflow_with_modular_components(self):
        """测试集成工作流与模块化组件"""
        workflow = ConversionWorkflow(
            self.config_manager, self.template_manager, self.yang_manager)
        
        # 创建测试配置文件
        test_config = """config firewall policy
    edit 1
        set name "test_policy"
        set srcintf "port1"
        set dstintf "port2"
        set srcaddr "all"
        set dstaddr "all"
        set service "ALL"
        set action accept
    next
end"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.conf', delete=False) as tmp_file:
            tmp_file.write(test_config)
            tmp_file_path = tmp_file.name
        
        try:
            # 测试解析器注册表集成
            parser_registry = ParserRegistry()
            parser = parser_registry.get_parser("fortigate")
            self.assertIsNotNone(parser)
            
            # 测试生成器注册表集成
            generator_registry = GeneratorRegistry()
            generator = generator_registry.get_generator("security-policy")
            self.assertIsNotNone(generator)
            
            # 测试工作流参数验证
            invalid_params = {'cli_file': '/nonexistent/file.conf'}
            validation_result = workflow._validate_conversion_inputs(invalid_params)
            self.assertFalse(validation_result['valid'])
            
            # 测试有效参数
            valid_params = {
                'cli_file': tmp_file_path,
                'vendor': 'fortigate',
                'model': 'z5100s',
                'version': 'R10P2'
            }
            validation_result = workflow._validate_conversion_inputs(valid_params)
            self.assertTrue(validation_result['valid'])
            
        finally:
            os.unlink(tmp_file_path)
    
    def test_modular_component_compatibility(self):
        """测试模块化组件的兼容性"""
        # 测试解析器和生成器的协同工作
        parser_registry = ParserRegistry()
        generator_registry = GeneratorRegistry()
        
        # 获取组件
        parser = parser_registry.get_parser("fortigate")
        generator = generator_registry.get_generator("security-policy")
        
        if parser and generator:
            # 测试组件信息兼容性
            parser_info = parser.get_plugin_info()
            generator_info = generator.get_generator_info()
            
            self.assertEqual(parser_info["vendor"], "fortigate")
            self.assertEqual(generator_info["generator_type"], "security-policy")
            
            # 测试数据流兼容性
            test_policy_data = {
                "name": "test_policy",
                "action": "permit",
                "config-source": "fortigate",
                "source-zone": ["trust"],
                "destination-zone": ["untrust"],
                "service": ["HTTP", "HTTPS"]
            }
            
            # 验证生成器可以处理解析器输出格式的数据
            self.assertTrue(generator.validate_input_data(test_policy_data))
    
    def test_error_handling_and_logging(self):
        """测试错误处理和日志记录"""
        # 测试解析器错误处理
        parser = FortigateParserAdapter()
        
        # 测试不存在的文件
        try:
            parser.parse_config_file("/nonexistent/file.conf")
            self.fail("应该抛出FileNotFoundError")
        except FileNotFoundError:
            pass  # 预期的异常
        
        # 测试生成器错误处理
        generator = SecurityPolicyGeneratorAdapter()
        
        # 测试无效数据
        try:
            generator.generate_xml_element({})  # 缺少必要字段
            self.fail("应该抛出ValueError")
        except ValueError:
            pass  # 预期的异常
    
    def test_performance_and_caching(self):
        """测试性能和缓存机制"""
        # 测试解析器缓存
        registry = ParserRegistry()
        
        import time
        
        # 第一次获取（创建实例）
        start_time = time.time()
        parser1 = registry.get_parser("fortigate")
        first_time = time.time() - start_time
        
        # 第二次获取（从缓存）
        start_time = time.time()
        parser2 = registry.get_parser("fortigate")
        second_time = time.time() - start_time
        
        # 验证缓存效果
        self.assertIs(parser1, parser2)
        self.assertLess(second_time, first_time)  # 缓存应该更快
        
        # 测试生成器缓存
        gen_registry = GeneratorRegistry()
        
        # 第一次获取
        start_time = time.time()
        gen1 = gen_registry.get_generator("security-policy")
        first_time = time.time() - start_time
        
        # 第二次获取
        start_time = time.time()
        gen2 = gen_registry.get_generator("security-policy")
        second_time = time.time() - start_time
        
        # 验证缓存效果
        self.assertIs(gen1, gen2)
        self.assertLess(second_time, first_time)


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
