#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
新架构测试 - 验证重构后的分层架构功能
"""

import unittest
import os
import sys
import tempfile

# 添加引擎路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from engine.infrastructure.config.config_manager import ConfigManager
from engine.infrastructure.templates.template_manager import TemplateManager
from engine.infrastructure.yang.yang_manager import Yang<PERSON>anager
from engine.application.conversion_service import ConversionService
from engine.application.validation_service import ValidationService
from engine.application.extraction_service import ExtractionService
from engine.business.workflows.conversion_workflow import ConversionWorkflow
from engine.processing.pipeline.pipeline_manager import PipelineManager
from engine.processing.pipeline.pipeline_stage import FunctionStage
from engine.processing.pipeline.data_flow import DataContext


class TestNewArchitecture(unittest.TestCase):
    """新架构测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.config_manager = ConfigManager()
        self.template_manager = TemplateManager(self.config_manager)
        self.yang_manager = Yang<PERSON>anager(self.config_manager)
    
    def test_config_manager(self):
        """测试配置管理器"""
        # 测试基本功能
        self.assertIsNotNone(self.config_manager.get_engine_dir())
        self.assertTrue(self.config_manager.is_vendor_supported('fortigate'))
        self.assertTrue(self.config_manager.is_model_version_supported('z5100s', 'R10P2'))
        
        # 测试配置获取和设置
        self.config_manager.set_config('test_key', 'test_value')
        self.assertEqual(self.config_manager.get_config('test_key'), 'test_value')
    
    def test_template_manager(self):
        """测试模板管理器"""
        # 测试模板路径获取
        try:
            template_path = self.template_manager.get_template_path('z5100s', 'R10P2')
            self.assertTrue(isinstance(template_path, str))
        except FileNotFoundError:
            # 模板文件不存在是正常的，在测试环境中
            pass
        
        # 测试缓存功能
        cache_stats = self.template_manager.get_cache_stats()
        self.assertIn('size', cache_stats)
        self.assertIn('max_size', cache_stats)
    
    def test_yang_manager(self):
        """测试YANG管理器"""
        # 测试yanglint可用性检查
        yanglint_available = self.yang_manager.is_yanglint_available()
        self.assertIsInstance(yanglint_available, bool)
        
        # 测试缓存统计
        cache_stats = self.yang_manager.get_cache_stats()
        self.assertIn('schema_cache_size', cache_stats)
        self.assertIn('namespace_cache_size', cache_stats)
    
    def test_conversion_service(self):
        """测试转换服务"""
        conversion_service = ConversionService(self.config_manager)
        self.assertIsNotNone(conversion_service.config_manager)
        self.assertIsNotNone(conversion_service.template_manager)
        self.assertIsNotNone(conversion_service.yang_manager)
        self.assertIsNotNone(conversion_service.conversion_workflow)
    
    def test_validation_service(self):
        """测试验证服务"""
        validation_service = ValidationService(self.config_manager)
        
        # 测试验证能力获取
        capabilities = validation_service.get_validation_capabilities()
        self.assertIn('yang_validation', capabilities)
        self.assertIn('config_validation', capabilities)
        self.assertIn('supported_vendors', capabilities)
    
    def test_extraction_service(self):
        """测试提取服务"""
        extraction_service = ExtractionService(self.config_manager)
        
        # 测试提取能力获取
        capabilities = extraction_service.get_extraction_capabilities()
        self.assertIn('supported_vendors', capabilities)
        self.assertIn('interface_extraction', capabilities)
        
        # 测试支持的厂商列表
        vendors = extraction_service.get_supported_vendors()
        self.assertIn('fortigate', vendors)
    
    def test_conversion_workflow(self):
        """测试转换工作流"""
        workflow = ConversionWorkflow(
            self.config_manager, 
            self.template_manager, 
            self.yang_manager
        )
        
        # 测试参数验证
        invalid_params = {'cli_file': '/nonexistent/file.conf'}
        validation_result = workflow._validate_conversion_inputs(invalid_params)
        self.assertFalse(validation_result['valid'])
        
        # 测试有效参数
        with tempfile.NamedTemporaryFile(mode='w', suffix='.conf', delete=False) as tmp_file:
            tmp_file.write("config system global\nend\n")
            tmp_file_path = tmp_file.name
        
        try:
            valid_params = {
                'cli_file': tmp_file_path,
                'vendor': 'fortigate',
                'model': 'z5100s',
                'version': 'R10P2'
            }
            validation_result = workflow._validate_conversion_inputs(valid_params)
            self.assertTrue(validation_result['valid'])
        finally:
            os.unlink(tmp_file_path)
    
    def test_pipeline_system(self):
        """测试处理管道系统"""
        # 创建测试管道
        pipeline = PipelineManager("test_pipeline", "测试管道")
        
        # 添加测试阶段
        def test_stage1(context: DataContext) -> bool:
            context.set_data("stage1_result", "success")
            return True
        
        def test_stage2(context: DataContext) -> bool:
            stage1_result = context.get_data("stage1_result")
            context.set_data("stage2_result", f"processed_{stage1_result}")
            return True
        
        pipeline.add_function_stage("stage1", test_stage1)
        pipeline.add_function_stage("stage2", test_stage2)
        
        # 验证管道配置
        validation = pipeline.validate_pipeline()
        self.assertTrue(validation['valid'])
        self.assertEqual(validation['stage_count'], 2)
        
        # 执行管道
        result_context = pipeline.execute({"input": "test_data"})
        self.assertEqual(result_context.state, "completed")
        self.assertEqual(result_context.get_data("stage1_result"), "success")
        self.assertEqual(result_context.get_data("stage2_result"), "processed_success")
        
        # 检查执行历史
        history = result_context.get_stage_history()
        self.assertEqual(len(history), 2)
        self.assertEqual(history[0]['stage'], 'stage1')
        self.assertEqual(history[1]['stage'], 'stage2')
    
    def test_data_context(self):
        """测试数据上下文"""
        context = DataContext({"initial": "data"})
        
        # 测试数据操作
        context.set_data("test_key", "test_value")
        self.assertEqual(context.get_data("test_key"), "test_value")
        self.assertTrue(context.has_data("test_key"))
        self.assertFalse(context.has_data("nonexistent_key"))
        
        # 测试批量更新
        context.update_data({"key1": "value1", "key2": "value2"})
        self.assertEqual(context.get_data("key1"), "value1")
        self.assertEqual(context.get_data("key2"), "value2")
        
        # 测试错误和警告
        context.add_error("Test error", "test_stage")
        context.add_warning("Test warning", "test_stage")
        
        self.assertTrue(context.has_errors())
        self.assertTrue(context.has_warnings())
        self.assertEqual(len(context.get_errors()), 1)
        self.assertEqual(len(context.get_warnings()), 1)
        
        # 测试状态管理
        context.set_state("processing")
        self.assertEqual(context.state, "processing")
        
        # 测试阶段记录
        context.add_stage_record("test_stage", "completed", 1.5, {"detail": "test"})
        history = context.get_stage_history()
        self.assertEqual(len(history), 1)
        self.assertEqual(history[0]['stage'], "test_stage")
        self.assertEqual(history[0]['status'], "completed")
    
    def test_architecture_integration(self):
        """测试架构集成"""
        # 测试各层之间的集成
        conversion_service = ConversionService()
        
        # 验证服务间的依赖关系
        self.assertIsInstance(conversion_service.config_manager, ConfigManager)
        self.assertIsInstance(conversion_service.template_manager, TemplateManager)
        self.assertIsInstance(conversion_service.yang_manager, YangManager)
        self.assertIsInstance(conversion_service.conversion_workflow, ConversionWorkflow)
        
        # 验证工作流的依赖关系
        workflow = conversion_service.conversion_workflow
        self.assertIs(workflow.config_manager, conversion_service.config_manager)
        self.assertIs(workflow.template_manager, conversion_service.template_manager)
        self.assertIs(workflow.yang_manager, conversion_service.yang_manager)


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
