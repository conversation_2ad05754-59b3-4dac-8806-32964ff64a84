#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
性能优化测试 - 验证性能监控、内存管理和错误处理功能
"""

import unittest
import os
import sys
import time
import tempfile

# 添加引擎路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from engine.infrastructure.common.performance import PerformanceMonitor, MemoryManager
from engine.infrastructure.common.error_handling import <PERSON>rror<PERSON><PERSON><PERSON>, ErrorSeverity
from engine.infrastructure.common.caching import LRUCache, CacheManager
from engine.business.workflows.conversion_workflow import ConversionWorkflow
from engine.infrastructure.config.config_manager import ConfigManager
from engine.infrastructure.templates.template_manager import TemplateManager
from engine.infrastructure.yang.yang_manager import YangManager


class TestPerformanceOptimization(unittest.TestCase):
    """性能优化测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.config_manager = ConfigManager()
        self.template_manager = TemplateManager(self.config_manager)
        self.yang_manager = Yang<PERSON>anager(self.config_manager)
    
    def test_performance_monitor(self):
        """测试性能监控器"""
        monitor = PerformanceMonitor()
        
        # 测试操作监控
        context = monitor.start_operation("test_operation")
        self.assertIn("operation_name", context)
        self.assertIn("start_time", context)
        
        # 模拟一些工作
        time.sleep(0.1)
        
        # 结束监控
        metrics = monitor.end_operation(context, success=True)
        
        # 验证指标
        self.assertEqual(metrics.operation_name, "test_operation")
        self.assertTrue(metrics.success)
        self.assertGreater(metrics.duration, 0.1)
        self.assertGreater(metrics.memory_used_mb, 0)
        
        # 测试统计信息
        stats = monitor.get_operation_stats("test_operation")
        self.assertEqual(stats["total_count"], 1)
        self.assertEqual(stats["success_count"], 1)
        self.assertGreater(stats["avg_duration"], 0)
        
        # 测试性能摘要
        summary = monitor.get_performance_summary()
        self.assertEqual(summary["total_operations"], 1)
        self.assertEqual(summary["operation_types"], 1)
    
    def test_memory_manager(self):
        """测试内存管理器"""
        memory_manager = MemoryManager(memory_threshold_mb=100.0)
        
        # 测试内存检查
        memory_usage = memory_manager.check_memory_usage()
        self.assertIn("rss_mb", memory_usage)
        self.assertIn("percent", memory_usage)
        self.assertIn("threshold_exceeded", memory_usage)
        
        # 测试内存优化
        optimization_result = memory_manager.optimize_memory(force=True)
        self.assertIn("optimized", optimization_result)
        
        if optimization_result["optimized"]:
            self.assertIn("memory_freed_mb", optimization_result)
            self.assertIn("objects_collected", optimization_result)
        
        # 测试内存统计
        stats = memory_manager.get_memory_stats()
        self.assertIn("current_usage", stats)
        self.assertIn("threshold_mb", stats)
        self.assertIn("gc_stats", stats)
    
    def test_error_handler(self):
        """测试错误处理器"""
        error_handler = ErrorHandler()
        
        # 测试已知异常处理
        try:
            raise FileNotFoundError("Test file not found")
        except FileNotFoundError as e:
            result = error_handler.handle_error(e, {"test_context": "value"})
            
            self.assertTrue(result["handled"])
            self.assertIn("error_record", result)
            self.assertIn("user_message", result)
            self.assertEqual(result["error_record"].error_type, "FileNotFoundError")
            self.assertEqual(result["severity"], ErrorSeverity.MEDIUM)
        
        # 测试未知异常处理
        try:
            raise RuntimeError("Test runtime error")
        except RuntimeError as e:
            result = error_handler.handle_error(e)
            
            self.assertTrue(result["handled"])
            self.assertIn("error_record", result)
        
        # 测试错误统计
        stats = error_handler.get_error_stats()
        self.assertEqual(stats["total_errors"], 2)
        self.assertIn("FileNotFoundError", stats["error_types"])
        self.assertIn("RuntimeError", stats["error_types"])
        
        # 测试最近错误
        recent_errors = error_handler.get_recent_errors(5)
        self.assertEqual(len(recent_errors), 2)
    
    def test_lru_cache(self):
        """测试LRU缓存"""
        cache = LRUCache(max_size=3, default_ttl=1.0)
        
        # 测试基本操作
        cache.put("key1", "value1")
        cache.put("key2", "value2")
        cache.put("key3", "value3")
        
        self.assertEqual(cache.get("key1"), "value1")
        self.assertEqual(cache.get("key2"), "value2")
        self.assertEqual(cache.get("key3"), "value3")
        
        # 测试LRU驱逐
        cache.put("key4", "value4")  # 应该驱逐key1
        self.assertIsNone(cache.get("key1"))
        self.assertEqual(cache.get("key4"), "value4")
        
        # 测试TTL过期
        cache.put("temp_key", "temp_value", ttl=0.1)
        self.assertEqual(cache.get("temp_key"), "temp_value")
        
        time.sleep(0.2)
        self.assertIsNone(cache.get("temp_key"))  # 应该过期
        
        # 测试统计信息
        stats = cache.get_stats()
        self.assertIn("size", stats)
        self.assertIn("hit_rate", stats)
        self.assertGreater(stats["hits"], 0)
        self.assertGreater(stats["misses"], 0)
    
    def test_cache_manager(self):
        """测试缓存管理器"""
        cache_manager = CacheManager()
        
        # 创建命名缓存
        cache1 = cache_manager.create_cache("test_cache1", max_size=10)
        cache2 = cache_manager.create_cache("test_cache2", max_size=20, default_ttl=60.0)
        
        self.assertIsNotNone(cache1)
        self.assertIsNotNone(cache2)
        
        # 测试缓存获取
        retrieved_cache1 = cache_manager.get_cache("test_cache1")
        self.assertIs(cache1, retrieved_cache1)
        
        # 测试缓存操作
        cache1.put("test_key", "test_value")
        self.assertEqual(cache1.get("test_key"), "test_value")
        
        # 测试统计信息
        all_stats = cache_manager.get_all_stats()
        self.assertIn("test_cache1", all_stats)
        self.assertIn("test_cache2", all_stats)
        
        # 测试缓存名称
        cache_names = cache_manager.get_cache_names()
        self.assertIn("test_cache1", cache_names)
        self.assertIn("test_cache2", cache_names)
        
        # 测试删除缓存
        self.assertTrue(cache_manager.delete_cache("test_cache1"))
        self.assertIsNone(cache_manager.get_cache("test_cache1"))
    
    def test_workflow_performance_integration(self):
        """测试工作流性能集成"""
        workflow = ConversionWorkflow(
            self.config_manager, self.template_manager, self.yang_manager)
        
        # 验证性能监控组件已初始化
        self.assertIsNotNone(workflow.performance_monitor)
        self.assertIsNotNone(workflow.memory_manager)
        self.assertIsNotNone(workflow.error_handler)
        
        # 测试性能监控集成
        perf_context = workflow.performance_monitor.start_operation("test_workflow")
        self.assertIsNotNone(perf_context)
        
        # 模拟工作
        time.sleep(0.05)
        
        metrics = workflow.performance_monitor.end_operation(perf_context, success=True)
        self.assertEqual(metrics.operation_name, "test_workflow")
        self.assertTrue(metrics.success)
        
        # 测试内存管理集成
        memory_status = workflow.memory_manager.check_memory_usage()
        self.assertIsInstance(memory_status, dict)
        
        # 测试错误处理集成
        test_exception = ValueError("Test error")
        error_result = workflow.error_handler.handle_error(test_exception)
        self.assertTrue(error_result["handled"])
    
    def test_template_manager_caching_optimization(self):
        """测试模板管理器缓存优化"""
        # 验证模板管理器的高级缓存功能
        self.assertIsNotNone(self.template_manager.cache_manager)
        self.assertIsNotNone(self.template_manager.performance_monitor)
        self.assertIsNotNone(self.template_manager.advanced_cache)
        
        # 测试高级缓存
        cache = self.template_manager.advanced_cache
        cache.put("test_template", {"template": "data"})
        
        cached_data = cache.get("test_template")
        self.assertEqual(cached_data, {"template": "data"})
        
        # 测试缓存统计
        stats = cache.get_stats()
        self.assertGreater(stats["hits"], 0)
    
    def test_performance_under_load(self):
        """测试负载下的性能"""
        monitor = PerformanceMonitor()
        cache = LRUCache(max_size=100)
        
        # 模拟高负载操作
        operations = []
        for i in range(50):
            context = monitor.start_operation(f"load_test_{i}")
            
            # 模拟缓存操作
            cache.put(f"key_{i}", f"value_{i}")
            retrieved_value = cache.get(f"key_{i}")
            self.assertEqual(retrieved_value, f"value_{i}")
            
            # 模拟一些计算
            time.sleep(0.001)
            
            metrics = monitor.end_operation(context, success=True)
            operations.append(metrics)
        
        # 验证性能指标
        self.assertEqual(len(operations), 50)
        
        # 检查平均性能
        avg_duration = sum(op.duration for op in operations) / len(operations)
        self.assertLess(avg_duration, 0.1)  # 平均操作时间应该很短
        
        # 检查缓存性能
        cache_stats = cache.get_stats()
        self.assertEqual(cache_stats["size"], 50)
        self.assertGreater(cache_stats["hit_rate"], 0.9)  # 命中率应该很高
    
    def test_memory_optimization_under_pressure(self):
        """测试内存压力下的优化"""
        memory_manager = MemoryManager(memory_threshold_mb=50.0)  # 设置较低阈值
        
        # 创建一些内存压力
        large_data = []
        for i in range(100):
            large_data.append([0] * 1000)  # 创建一些数据
        
        # 检查内存使用
        memory_before = memory_manager.check_memory_usage()
        
        # 执行内存优化
        optimization_result = memory_manager.optimize_memory(force=True)
        
        # 验证优化结果
        self.assertTrue(optimization_result["optimized"])
        
        # 清理数据以释放内存
        del large_data
        
        # 再次优化
        final_optimization = memory_manager.optimize_memory(force=True)
        self.assertTrue(final_optimization["optimized"])


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
