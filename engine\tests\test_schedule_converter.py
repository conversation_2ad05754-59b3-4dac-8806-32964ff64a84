#!/usr/bin/env python
# -*- coding: utf-8 -*-

import unittest
import sys
import os
import json

# 确保能够导入项目模块
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from engine.processors.schedule_converter import (
    convert_fortigate_schedule, convert_single_schedule,
    create_always_schedule, create_none_schedule,
    create_onetime_schedule, create_periodic_schedule,
    is_time_cross_day, create_cross_day_schedule
)

class TestScheduleConverter(unittest.TestCase):
    """测试时间对象转换功能"""
    
    def test_always_schedule(self):
        """测试 'always' 特殊值处理"""
        schedule = {"name": "always", "type": "periodic"}
        result = convert_single_schedule(schedule)
        
        self.assertEqual(result["name"], "always")
        self.assertIn("period", result)
        self.assertEqual(result["period"]["start"], "00:00:00")
        self.assertEqual(result["period"]["end"], "23:59:59")
        self.assertEqual(len(result["period"]["weekday"]), 7)  # 所有星期几
    
    def test_none_schedule(self):
        """测试 'none' 特殊值处理"""
        schedule = {"name": "none", "type": "periodic"}
        result = convert_single_schedule(schedule)
        
        self.assertEqual(result["name"], "none")
        self.assertTrue(result["disabled"])
    
    def test_onetime_schedule(self):
        """测试一次性时间计划"""
        schedule = {
            "name": "test-onetime",
            "type": "onetime",
            "datetime_start": "2025-06-15 08:00",
            "datetime_end": "2025-06-15 17:00"
        }
        result = convert_single_schedule(schedule)
        
        self.assertEqual(result["name"], "test-onetime")
        self.assertIn("once", result)
        self.assertEqual(result["once"]["start"], "08:00:00/2025-06-15")
        self.assertEqual(result["once"]["end"], "17:00:59/2025-06-15")
    
    def test_periodic_schedule(self):
        """测试周期性时间计划"""
        schedule = {
            "name": "test-periodic",
            "type": "periodic",
            "time_start": "08:00:00",
            "time_end": "17:00:00",
            "weekdays": ["mon", "tue", "wed", "thu", "fri"]
        }
        result = convert_single_schedule(schedule)
        
        self.assertEqual(result["name"], "test-periodic")
        self.assertIn("period", result)
        self.assertEqual(result["period"]["start"], "08:00:00")
        self.assertEqual(result["period"]["end"], "17:00:00")
        self.assertEqual(len(result["period"]["weekday"]), 5)  # 工作日
    
    def test_cross_day_schedule(self):
        """测试跨天时间计划"""
        schedule = {
            "name": "test-cross-day",
            "type": "periodic",
            "time_start": "22:00:00",
            "time_end": "06:00:00",
            "weekdays": ["mon", "tue"]
        }
        result = convert_single_schedule(schedule)
        
        # 应该返回两个时间对象
        self.assertIsInstance(result, list)
        self.assertEqual(len(result), 2)
        
        # 第一部分：22:00:00 - 23:59:59
        self.assertEqual(result[0]["name"], "test-cross-day-part1")
        self.assertEqual(result[0]["period"]["start"], "22:00:00")
        self.assertEqual(result[0]["period"]["end"], "23:59:59")
        self.assertEqual(result[0]["period"]["weekday"][0]["key"], "mon")
        
        # 第二部分：00:00:00 - 06:00:00（下一天）
        self.assertEqual(result[1]["name"], "test-cross-day-part2")
        self.assertEqual(result[1]["period"]["start"], "00:00:00")
        self.assertEqual(result[1]["period"]["end"], "06:00:00")
        self.assertEqual(result[1]["period"]["weekday"][0]["key"], "tue")  # 星期一的下一天是星期二
    
    def test_is_time_cross_day(self):
        """测试跨天检测函数"""
        self.assertTrue(is_time_cross_day("23:00:00", "01:00:00"))
        self.assertFalse(is_time_cross_day("08:00:00", "17:00:00"))
        self.assertTrue(is_time_cross_day("18:00:00", "09:00:00"))
        self.assertFalse(is_time_cross_day("00:00:00", "23:59:59"))

if __name__ == "__main__":
    unittest.main() 