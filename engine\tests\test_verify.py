#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
import unittest

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from verify import verify_config_file, SUPPORTED_VENDORS

class TestVerifyConfigFile(unittest.TestCase):
    """测试配置文件验证功能"""
    
    def setUp(self):
        # 创建测试目录
        self.test_data_dir = os.path.join(os.path.dirname(__file__), "test_data")
        if not os.path.exists(self.test_data_dir):
            os.makedirs(self.test_data_dir)
        
        # 创建有效的测试配置文件
        self.valid_config_path = os.path.join(self.test_data_dir, "valid_config.txt")
        with open(self.valid_config_path, "w", encoding="utf-8") as f:
            f.write("""
# 有效的飞塔配置
config system global
    set hostname "FortiGate-VM64"
    set timezone 33
end

config firewall address
    edit "test-addr"
        set subnet *********** *************
    next
end

config vdom
    edit root
    next
end
""")
        
        # 创建无效的测试配置文件
        self.invalid_config_path = os.path.join(self.test_data_dir, "invalid_config.txt")
        with open(self.invalid_config_path, "w", encoding="utf-8") as f:
            f.write("""
# 无效的配置，不包含必要的配置命令
set hostname "FortiGate-VM64"
set timezone 33
""")
        
        # 创建空的测试配置文件
        self.empty_config_path = os.path.join(self.test_data_dir, "empty_config.txt")
        with open(self.empty_config_path, "w", encoding="utf-8") as f:
            f.write("")
        
        # 创建包含高级配置的测试文件
        self.advanced_config_path = os.path.join(self.test_data_dir, "advanced_config.txt")
        with open(self.advanced_config_path, "w", encoding="utf-8") as f:
            f.write("""
# 包含路由配置、PPPoE和IPv6配置的测试文件
config system global
    set hostname "FortiGate-Advanced"
end

config system interface
    edit "port1"
        set vdom "root"
        set mode pppoe
        set username "pppoe-user"
        set password ENC XXXX
        set description "PPPoE WAN接口"
    next
    edit "port2"
        set vdom "root"
        set ip *********** *************
        set ip6-address 2001:db8::1/64
        set ip6-allowaccess ping https ssh
        set description "IPv6 LAN接口"
    next
end

config router static
    edit 1
        set dst 0.0.0.0/0
        set gateway ********
        set device "port1"
    next
    edit 2
        set dst *************/24
        set gateway *************
        set device "port2"
    next
end

config router static6
    edit 1
        set dst ::/0
        set gateway 2001:db8::ffff
        set device "port2"
    next
end
""")
    
    def tearDown(self):
        # 清理测试文件
        if os.path.exists(self.valid_config_path):
            os.remove(self.valid_config_path)
        if os.path.exists(self.invalid_config_path):
            os.remove(self.invalid_config_path)
        if os.path.exists(self.empty_config_path):
            os.remove(self.empty_config_path)
        if os.path.exists(self.advanced_config_path):
            os.remove(self.advanced_config_path)
    
    def test_valid_config(self):
        """测试有效的配置文件"""
        is_valid, error_msg = verify_config_file(self.valid_config_path)
        self.assertTrue(is_valid)
        self.assertEqual(error_msg, "")
    
    def test_invalid_config(self):
        """测试无效的配置文件"""
        is_valid, error_msg = verify_config_file(self.invalid_config_path)
        self.assertFalse(is_valid)
        self.assertIn("不包含飞塔基本配置命令", error_msg)
    
    def test_empty_config(self):
        """测试空的配置文件"""
        is_valid, error_msg = verify_config_file(self.empty_config_path)
        self.assertFalse(is_valid)
        self.assertEqual(error_msg, "配置文件为空")
    
    def test_nonexistent_file(self):
        """测试不存在的文件"""
        is_valid, error_msg = verify_config_file("nonexistent_file.txt")
        self.assertFalse(is_valid)
        self.assertEqual(error_msg, "无法读取配置文件")
    
    def test_unsupported_vendor(self):
        """测试不支持的厂商"""
        is_valid, error_msg = verify_config_file(self.valid_config_path, vendor="unknown_vendor")
        self.assertFalse(is_valid)
        self.assertIn("不支持的厂商", error_msg)
    
    def test_supported_vendors(self):
        """测试支持的厂商列表"""
        # 确认fortigate在支持的厂商列表中
        self.assertIn("fortigate", SUPPORTED_VENDORS)
        
        # 确认每个厂商都有对应的验证函数名
        for vendor, func_name in SUPPORTED_VENDORS.items():
            self.assertTrue(isinstance(func_name, str))
            self.assertTrue(func_name.startswith("verify_"))
    
    def test_advanced_config(self):
        """测试包含高级配置的文件"""
        is_valid, error_msg = verify_config_file(self.advanced_config_path)
        self.assertTrue(is_valid)
        
        # 检查配置块是否闭合的验证
        with open(self.advanced_config_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 在文件末尾添加一个未闭合的配置块
        with open(self.advanced_config_path, 'a', encoding='utf-8') as f:
            f.write("\nconfig firewall address\n    edit \"test\"\n")
        
        # 再次验证，应该检测到未闭合的配置块
        is_valid, error_msg = verify_config_file(self.advanced_config_path)
        self.assertFalse(is_valid)
        self.assertIn("配置块不平衡", error_msg)

if __name__ == "__main__":
    unittest.main() 