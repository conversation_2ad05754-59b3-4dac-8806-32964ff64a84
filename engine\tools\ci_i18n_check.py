#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
持续集成国际化检查工具
防止新代码引入硬编码文本，确保国际化标准的持续维护
"""

import os
import re
import sys
import json
import subprocess
from typing import Dict, List, Set, Tuple, Optional
from pathlib import Path
from dataclasses import dataclass

@dataclass
class CICheckResult:
    """CI检查结果"""
    passed: bool
    error_count: int
    warning_count: int
    issues: List[Dict]
    summary: str

class CII18nChecker:
    """CI国际化检查器"""
    
    def __init__(self):
        self.issues = []
        self.config = {
            'max_errors': 0,  # 允许的最大错误数
            'max_warnings': 10,  # 允许的最大警告数
            'check_hardcoded_strings': True,
            'check_missing_translations': True,
            'check_translation_format': True,
            'check_parameter_consistency': True,
            'exclude_patterns': [
                r'test_.*\.py$',
                r'.*_test\.py$',
                r'__pycache__',
                r'\.git',
                r'\.pytest_cache',
                r'node_modules',
            ]
        }
        
        # 硬编码字符串检测模式
        self.hardcoded_patterns = [
            # 中文字符串
            re.compile(r'["\']([^"\']*[\u4e00-\u9fff][^"\']*)["\']'),
            # 英文错误消息
            re.compile(r'["\']([^"\']*(?:error|failed|failure|exception|invalid|missing|not found)[^"\']*)["\']', re.IGNORECASE),
            # 英文成功消息
            re.compile(r'["\']([^"\']*(?:success|successful|completed|finished)[^"\']*)["\']', re.IGNORECASE),
        ]
        
        # 应该跳过的字符串模式
        self.skip_patterns = [
            re.compile(r'^[a-zA-Z_][a-zA-Z0-9_]*$'),  # 变量名
            re.compile(r'^[A-Z_]+$'),  # 常量名
            re.compile(r'^\d+$'),  # 纯数字
            re.compile(r'^https?://'),  # URL
            re.compile(r'^[a-zA-Z]:\\'),  # Windows路径
            re.compile(r'^/[a-zA-Z]'),  # Unix路径
            re.compile(r'^\w+\.\w+$'),  # 文件名
        ]
    
    def should_skip_file(self, file_path: str) -> bool:
        """检查是否应该跳过文件"""
        for pattern in self.config['exclude_patterns']:
            if re.search(pattern, file_path):
                return True
        return False
    
    def should_skip_string(self, content: str) -> bool:
        """检查是否应该跳过字符串"""
        if not content.strip() or len(content.strip()) < 3:
            return True
        
        for pattern in self.skip_patterns:
            if pattern.match(content.strip()):
                return True
        
        return False
    
    def check_hardcoded_strings(self, directory: str) -> List[Dict]:
        """检查硬编码字符串"""
        if not self.config['check_hardcoded_strings']:
            return []
        
        issues = []
        
        for root, dirs, files in os.walk(directory):
            # 过滤目录
            dirs[:] = [d for d in dirs if not any(re.search(pattern, d) for pattern in self.config['exclude_patterns'])]
            
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    
                    if self.should_skip_file(file_path):
                        continue
                    
                    try:
                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                            lines = f.readlines()
                        
                        for line_num, line in enumerate(lines, 1):
                            # 检查是否已经是翻译调用
                            if re.search(r'(_\(|log\(|translate\(|safe_translate\()', line):
                                continue
                            
                            # 查找硬编码字符串
                            for pattern in self.hardcoded_patterns:
                                matches = pattern.finditer(line)
                                for match in matches:
                                    content = match.group(1)
                                    
                                    if self.should_skip_string(content):
                                        continue
                                    
                                    issues.append({
                                        'type': 'hardcoded_string',
                                        'severity': 'error',
                                        'file': file_path,
                                        'line': line_num,
                                        'content': content,
                                        'message': f"发现硬编码字符串: {content}",
                                        'suggestion': f"使用 _(\"{content}\") 替换硬编码字符串"
                                    })
                    
                    except Exception as e:
                        issues.append({
                            'type': 'file_error',
                            'severity': 'warning',
                            'file': file_path,
                            'line': 0,
                            'content': '',
                            'message': f"无法读取文件: {str(e)}",
                            'suggestion': "检查文件编码和权限"
                        })
        
        return issues
    
    def check_translation_files(self, locale_dir: str) -> List[Dict]:
        """检查翻译文件"""
        issues = []
        
        if not os.path.exists(locale_dir):
            issues.append({
                'type': 'missing_locale_dir',
                'severity': 'error',
                'file': locale_dir,
                'line': 0,
                'content': '',
                'message': "翻译文件目录不存在",
                'suggestion': f"创建目录: {locale_dir}"
            })
            return issues
        
        # 检查必需的翻译文件
        required_files = ['zh-CN.json', 'en-US.json']
        
        for file_name in required_files:
            file_path = os.path.join(locale_dir, file_name)
            
            if not os.path.exists(file_path):
                issues.append({
                    'type': 'missing_translation_file',
                    'severity': 'error',
                    'file': file_path,
                    'line': 0,
                    'content': '',
                    'message': f"缺少翻译文件: {file_name}",
                    'suggestion': f"创建翻译文件: {file_path}"
                })
                continue
            
            # 检查JSON格式
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    translations = json.load(f)
                
                # 检查翻译键格式
                if self.config['check_translation_format']:
                    valid_key_pattern = re.compile(r'^[a-z][a-z0-9_]*(\.[a-z][a-z0-9_]*)*$')
                    
                    for key in translations.keys():
                        if not valid_key_pattern.match(key):
                            issues.append({
                                'type': 'invalid_key_format',
                                'severity': 'warning',
                                'file': file_path,
                                'line': 0,
                                'content': key,
                                'message': f"无效的翻译键格式: {key}",
                                'suggestion': "使用格式: module.function.content"
                            })
            
            except json.JSONDecodeError as e:
                issues.append({
                    'type': 'json_format_error',
                    'severity': 'error',
                    'file': file_path,
                    'line': 0,
                    'content': '',
                    'message': f"JSON格式错误: {str(e)}",
                    'suggestion': "修复JSON格式错误"
                })
            
            except Exception as e:
                issues.append({
                    'type': 'file_read_error',
                    'severity': 'error',
                    'file': file_path,
                    'line': 0,
                    'content': '',
                    'message': f"读取文件失败: {str(e)}",
                    'suggestion': "检查文件权限和编码"
                })
        
        return issues
    
    def check_git_changes(self) -> List[Dict]:
        """检查Git变更中的国际化问题"""
        issues = []
        
        try:
            # 获取变更的文件
            result = subprocess.run(
                ['git', 'diff', '--name-only', 'HEAD~1', 'HEAD'],
                capture_output=True,
                text=True,
                cwd=os.getcwd()
            )
            
            if result.returncode != 0:
                # 如果没有Git历史，检查所有文件
                return []
            
            changed_files = result.stdout.strip().split('\n')
            changed_files = [f for f in changed_files if f.endswith('.py')]
            
            # 检查变更的Python文件
            for file_path in changed_files:
                if not os.path.exists(file_path) or self.should_skip_file(file_path):
                    continue
                
                # 获取文件的变更内容
                diff_result = subprocess.run(
                    ['git', 'diff', 'HEAD~1', 'HEAD', file_path],
                    capture_output=True,
                    text=True,
                    cwd=os.getcwd()
                )
                
                if diff_result.returncode == 0:
                    diff_content = diff_result.stdout
                    
                    # 检查新增的行
                    for line in diff_content.split('\n'):
                        if line.startswith('+') and not line.startswith('+++'):
                            line_content = line[1:]  # 移除+号
                            
                            # 检查是否包含硬编码字符串
                            for pattern in self.hardcoded_patterns:
                                matches = pattern.finditer(line_content)
                                for match in matches:
                                    content = match.group(1)
                                    
                                    if self.should_skip_string(content):
                                        continue
                                    
                                    # 检查是否已经是翻译调用
                                    if re.search(r'(_\(|log\(|translate\()', line_content):
                                        continue
                                    
                                    issues.append({
                                        'type': 'new_hardcoded_string',
                                        'severity': 'error',
                                        'file': file_path,
                                        'line': 0,
                                        'content': content,
                                        'message': f"新增硬编码字符串: {content}",
                                        'suggestion': f"使用 _(\"{content}\") 替换硬编码字符串"
                                    })
        
        except subprocess.SubprocessError:
            # Git不可用，跳过Git检查
            pass
        except Exception as e:
            issues.append({
                'type': 'git_check_error',
                'severity': 'warning',
                'file': '',
                'line': 0,
                'content': '',
                'message': f"Git检查失败: {str(e)}",
                'suggestion': "检查Git配置"
            })
        
        return issues
    
    def run_checks(self, project_dir: str) -> CICheckResult:
        """运行所有CI检查"""
        all_issues = []
        
        # 检查硬编码字符串
        hardcoded_issues = self.check_hardcoded_strings(project_dir)
        all_issues.extend(hardcoded_issues)
        
        # 检查翻译文件
        locale_dir = os.path.join(project_dir, 'locales')
        translation_issues = self.check_translation_files(locale_dir)
        all_issues.extend(translation_issues)
        
        # 检查Git变更
        git_issues = self.check_git_changes()
        all_issues.extend(git_issues)
        
        # 统计错误和警告
        error_count = len([issue for issue in all_issues if issue['severity'] == 'error'])
        warning_count = len([issue for issue in all_issues if issue['severity'] == 'warning'])
        
        # 判断是否通过
        passed = (
            error_count <= self.config['max_errors'] and
            warning_count <= self.config['max_warnings']
        )
        
        # 生成摘要
        summary = f"CI检查完成: {error_count} 个错误, {warning_count} 个警告"
        if not passed:
            summary += " - 检查失败"
        else:
            summary += " - 检查通过"
        
        return CICheckResult(
            passed=passed,
            error_count=error_count,
            warning_count=warning_count,
            issues=all_issues,
            summary=summary
        )
    
    def generate_report(self, result: CICheckResult, output_file: str = None):
        """生成检查报告"""
        report = {
            'summary': {
                'passed': result.passed,
                'error_count': result.error_count,
                'warning_count': result.warning_count,
                'total_issues': len(result.issues)
            },
            'issues': result.issues,
            'config': self.config
        }
        
        if output_file:
            os.makedirs(os.path.dirname(output_file), exist_ok=True)
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
        
        return report
    
    def print_result(self, result: CICheckResult):
        """打印检查结果"""
        print(f"\n📊 CI国际化检查结果:")
        print(f"  {result.summary}")
        print(f"  错误数: {result.error_count}")
        print(f"  警告数: {result.warning_count}")
        
        if result.error_count > 0:
            print(f"\n🚨 错误列表:")
            errors = [issue for issue in result.issues if issue['severity'] == 'error']
            for i, issue in enumerate(errors[:10], 1):  # 只显示前10个错误
                print(f"  {i}. {issue['file']}:{issue['line']}")
                print(f"     {issue['message']}")
                print(f"     建议: {issue['suggestion']}")
            
            if len(errors) > 10:
                print(f"     ... 还有 {len(errors) - 10} 个错误")
        
        if result.warning_count > 0:
            print(f"\n⚠️ 警告数: {result.warning_count}")
        
        if result.passed:
            print(f"\n✅ CI检查通过")
        else:
            print(f"\n❌ CI检查失败")

def main():
    """主函数"""
    # 获取项目目录
    if len(sys.argv) > 1:
        project_dir = sys.argv[1]
    else:
        project_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    
    # 创建检查器
    checker = CII18nChecker()
    
    # 运行检查
    print("🔍 开始CI国际化检查...")
    result = checker.run_checks(project_dir)
    
    # 打印结果
    checker.print_result(result)
    
    # 生成报告
    report_file = os.path.join(project_dir, 'reports', 'ci_i18n_check_report.json')
    checker.generate_report(result, report_file)
    print(f"\n📁 详细报告已保存到: {report_file}")
    
    # 返回退出代码
    return 0 if result.passed else 1

if __name__ == "__main__":
    exit(main())
