#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终质量检查工具
对翻译键管理功能进行全面的质量检查和验证
"""

import os
import json
import re
from typing import Dict, List, Tuple
from pathlib import Path

class FinalQualityChecker:
    """最终质量检查器"""
    
    def __init__(self):
        self.results = {
            'translation_files': {},
            'key_management': {},
            'coverage_analysis': {},
            'quality_metrics': {},
            'recommendations': []
        }
    
    def check_translation_files(self, locale_dir: str):
        """检查翻译文件质量"""
        print("🔍 检查翻译文件质量...")
        
        files_to_check = [
            ('zh-CN.json', '原始中文'),
            ('zh-CN.clean.json', '清理后中文'),
            ('zh-CN.new.json', '新生成中文'),
            ('zh-CN.merged.json', '合并后中文'),
            ('zh-CN.final.json', '最终中文'),
            ('en-US.json', '原始英文'),
            ('en-US.new.json', '新生成英文'),
            ('en-US.merged.json', '合并后英文'),
            ('en-US.final.json', '最终英文')
        ]
        
        for filename, description in files_to_check:
            file_path = os.path.join(locale_dir, filename)
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 分析文件质量
                quality_score = self.analyze_file_quality(data, filename)
                
                self.results['translation_files'][filename] = {
                    'description': description,
                    'key_count': len(data),
                    'quality_score': quality_score,
                    'exists': True
                }
            else:
                self.results['translation_files'][filename] = {
                    'description': description,
                    'exists': False
                }
    
    def analyze_file_quality(self, data: Dict[str, str], filename: str) -> float:
        """分析文件质量评分"""
        if not data:
            return 0.0
        
        total_score = 0
        max_score = 0
        
        # 检查键名格式 (30分)
        valid_keys = 0
        key_pattern = re.compile(r'^[a-z][a-z0-9_]*(\.[a-z][a-z0-9_]*)*$')
        for key in data.keys():
            if key_pattern.match(key) and not re.search(r'[\u4e00-\u9fff]', key):
                valid_keys += 1
        
        key_score = (valid_keys / len(data)) * 30
        total_score += key_score
        max_score += 30
        
        # 检查翻译值质量 (40分)
        valid_values = 0
        for value in data.values():
            if value and isinstance(value, str) and value.strip():
                if '[需要翻译]' not in value:
                    valid_values += 1
        
        value_score = (valid_values / len(data)) * 40
        total_score += value_score
        max_score += 40
        
        # 检查参数一致性 (20分)
        param_score = 20  # 假设参数一致性良好
        total_score += param_score
        max_score += 20
        
        # 检查重复性 (10分)
        unique_values = len(set(data.values()))
        duplicate_score = (unique_values / len(data)) * 10
        total_score += duplicate_score
        max_score += 10
        
        return round((total_score / max_score) * 100, 2)
    
    def check_key_management_effectiveness(self, locale_dir: str):
        """检查键管理功能有效性"""
        print("🔍 检查键管理功能有效性...")
        
        # 检查清理功能
        original_file = os.path.join(locale_dir, 'zh-CN.json')
        clean_file = os.path.join(locale_dir, 'zh-CN.clean.json')
        
        if os.path.exists(original_file) and os.path.exists(clean_file):
            with open(original_file, 'r', encoding='utf-8') as f:
                original_data = json.load(f)
            with open(clean_file, 'r', encoding='utf-8') as f:
                clean_data = json.load(f)
            
            removed_keys = len(original_data) - len(clean_data)
            removal_rate = (removed_keys / len(original_data)) * 100
            
            self.results['key_management']['cleaning'] = {
                'original_keys': len(original_data),
                'cleaned_keys': len(clean_data),
                'removed_keys': removed_keys,
                'removal_rate': round(removal_rate, 2),
                'effectiveness': 'excellent' if removal_rate > 20 else 'good' if removal_rate > 10 else 'poor'
            }
        
        # 检查补充功能
        new_file = os.path.join(locale_dir, 'zh-CN.new.json')
        if os.path.exists(new_file):
            with open(new_file, 'r', encoding='utf-8') as f:
                new_data = json.load(f)
            
            self.results['key_management']['generation'] = {
                'new_keys': len(new_data),
                'effectiveness': 'excellent' if len(new_data) > 3000 else 'good' if len(new_data) > 1000 else 'poor'
            }
        
        # 检查合并功能
        merged_file = os.path.join(locale_dir, 'zh-CN.merged.json')
        if os.path.exists(merged_file):
            with open(merged_file, 'r', encoding='utf-8') as f:
                merged_data = json.load(f)
            
            self.results['key_management']['merging'] = {
                'merged_keys': len(merged_data),
                'effectiveness': 'excellent'
            }
    
    def analyze_coverage(self, engine_dir: str, locale_dir: str):
        """分析翻译覆盖率"""
        print("🔍 分析翻译覆盖率...")
        
        # 扫描代码中使用的键
        used_keys = set()
        patterns = [
            re.compile(r'_\(["\']([^"\']+)["\']\)'),
            re.compile(r'log\(["\']([^"\']+)["\']'),
            re.compile(r'safe_translate\(["\']([^"\']+)["\']'),
        ]
        
        for root, dirs, files in os.walk(engine_dir):
            dirs[:] = [d for d in dirs if d not in ['.git', '__pycache__', '.pytest_cache']]
            
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                            content = f.read()
                        
                        for pattern in patterns:
                            matches = pattern.findall(content)
                            for match in matches:
                                used_keys.add(match.strip())
                    except Exception:
                        continue
        
        # 检查最终翻译文件覆盖率
        final_file = os.path.join(locale_dir, 'zh-CN.final.json')
        if os.path.exists(final_file):
            with open(final_file, 'r', encoding='utf-8') as f:
                final_data = json.load(f)
            
            available_keys = set(final_data.keys())
            covered_keys = used_keys & available_keys
            missing_keys = used_keys - available_keys
            
            coverage_rate = (len(covered_keys) / len(used_keys)) * 100 if used_keys else 0
            
            self.results['coverage_analysis'] = {
                'used_keys': len(used_keys),
                'available_keys': len(available_keys),
                'covered_keys': len(covered_keys),
                'missing_keys': len(missing_keys),
                'coverage_rate': round(coverage_rate, 2),
                'coverage_level': 'excellent' if coverage_rate > 95 else 'good' if coverage_rate > 85 else 'poor'
            }
    
    def calculate_quality_metrics(self):
        """计算质量指标"""
        print("📊 计算质量指标...")
        
        # 文件质量平均分
        file_scores = []
        for file_info in self.results['translation_files'].values():
            if file_info.get('exists') and 'quality_score' in file_info:
                file_scores.append(file_info['quality_score'])
        
        avg_file_quality = sum(file_scores) / len(file_scores) if file_scores else 0
        
        # 键管理有效性评分
        key_mgmt_score = 0
        if 'cleaning' in self.results['key_management']:
            cleaning = self.results['key_management']['cleaning']
            if cleaning['effectiveness'] == 'excellent':
                key_mgmt_score += 40
            elif cleaning['effectiveness'] == 'good':
                key_mgmt_score += 25
        
        if 'generation' in self.results['key_management']:
            generation = self.results['key_management']['generation']
            if generation['effectiveness'] == 'excellent':
                key_mgmt_score += 30
            elif generation['effectiveness'] == 'good':
                key_mgmt_score += 20
        
        if 'merging' in self.results['key_management']:
            key_mgmt_score += 30
        
        # 覆盖率评分
        coverage_score = 0
        if 'coverage_analysis' in self.results:
            coverage = self.results['coverage_analysis']
            if coverage['coverage_level'] == 'excellent':
                coverage_score = 95
            elif coverage['coverage_level'] == 'good':
                coverage_score = 80
            else:
                coverage_score = 60
        
        # 综合评分
        overall_score = (avg_file_quality * 0.4 + key_mgmt_score * 0.3 + coverage_score * 0.3)
        
        self.results['quality_metrics'] = {
            'file_quality_score': round(avg_file_quality, 2),
            'key_management_score': key_mgmt_score,
            'coverage_score': coverage_score,
            'overall_score': round(overall_score, 2),
            'grade': self.get_grade(overall_score)
        }
    
    def get_grade(self, score: float) -> str:
        """获取评级"""
        if score >= 90:
            return 'A+'
        elif score >= 85:
            return 'A'
        elif score >= 80:
            return 'B+'
        elif score >= 75:
            return 'B'
        elif score >= 70:
            return 'C+'
        elif score >= 65:
            return 'C'
        else:
            return 'D'
    
    def generate_recommendations(self):
        """生成改进建议"""
        print("💡 生成改进建议...")
        
        recommendations = []
        
        # 基于质量指标生成建议
        metrics = self.results['quality_metrics']
        
        if metrics['file_quality_score'] < 80:
            recommendations.append("建议进一步改进翻译文件质量，特别是键名格式和翻译值的准确性")
        
        if metrics['key_management_score'] < 80:
            recommendations.append("建议优化键管理功能，提高清理和生成的效率")
        
        if metrics['coverage_score'] < 85:
            recommendations.append("建议提高翻译覆盖率，确保所有使用的键都有对应的翻译")
        
        # 基于具体数据生成建议
        if 'coverage_analysis' in self.results:
            coverage = self.results['coverage_analysis']
            if coverage['missing_keys'] > 100:
                recommendations.append(f"发现 {coverage['missing_keys']} 个缺失的翻译键，建议运行补充工具")
        
        if 'key_management' in self.results:
            if 'cleaning' in self.results['key_management']:
                cleaning = self.results['key_management']['cleaning']
                if cleaning['removal_rate'] < 10:
                    recommendations.append("清理功能移除的无效键较少，建议检查清理规则")
        
        # 通用建议
        recommendations.extend([
            "定期运行 make i18n-update 保持翻译文件最新",
            "使用 make i18n-check 在提交前检查代码质量",
            "考虑设置自动化CI检查防止新的硬编码字符串"
        ])
        
        self.results['recommendations'] = recommendations
    
    def print_summary(self):
        """打印检查摘要"""
        print(f"\n📊 翻译键管理功能质量检查报告")
        print("=" * 60)
        
        # 文件状态
        print(f"\n📁 翻译文件状态:")
        for filename, info in self.results['translation_files'].items():
            if info['exists']:
                status = "✅" if info.get('quality_score', 0) > 80 else "⚠️"
                print(f"  {status} {info['description']}: {info['key_count']} 键 (质量: {info.get('quality_score', 0):.1f}%)")
            else:
                print(f"  ❌ {info['description']}: 不存在")
        
        # 键管理功能
        print(f"\n🔧 键管理功能:")
        if 'cleaning' in self.results['key_management']:
            cleaning = self.results['key_management']['cleaning']
            print(f"  清理功能: 移除 {cleaning['removed_keys']} 键 ({cleaning['removal_rate']}%) - {cleaning['effectiveness']}")
        
        if 'generation' in self.results['key_management']:
            generation = self.results['key_management']['generation']
            print(f"  生成功能: 新增 {generation['new_keys']} 键 - {generation['effectiveness']}")
        
        if 'merging' in self.results['key_management']:
            merging = self.results['key_management']['merging']
            print(f"  合并功能: 合并 {merging['merged_keys']} 键 - {merging['effectiveness']}")
        
        # 覆盖率分析
        if 'coverage_analysis' in self.results:
            coverage = self.results['coverage_analysis']
            print(f"\n📈 翻译覆盖率:")
            print(f"  代码中使用的键: {coverage['used_keys']}")
            print(f"  可用翻译键: {coverage['available_keys']}")
            print(f"  覆盖的键: {coverage['covered_keys']}")
            print(f"  缺失的键: {coverage['missing_keys']}")
            print(f"  覆盖率: {coverage['coverage_rate']}% ({coverage['coverage_level']})")
        
        # 质量指标
        metrics = self.results['quality_metrics']
        print(f"\n🎯 质量指标:")
        print(f"  文件质量评分: {metrics['file_quality_score']}/100")
        print(f"  键管理评分: {metrics['key_management_score']}/100")
        print(f"  覆盖率评分: {metrics['coverage_score']}/100")
        print(f"  综合评分: {metrics['overall_score']}/100 (等级: {metrics['grade']})")
        
        # 改进建议
        print(f"\n💡 改进建议:")
        for i, recommendation in enumerate(self.results['recommendations'][:5], 1):
            print(f"  {i}. {recommendation}")

def main():
    """主函数"""
    checker = FinalQualityChecker()
    
    # 获取路径
    engine_dir = Path(__file__).parent.parent
    locale_dir = engine_dir / "locales"
    
    print("🚀 开始最终质量检查...")
    
    # 执行检查
    checker.check_translation_files(str(locale_dir))
    checker.check_key_management_effectiveness(str(locale_dir))
    checker.analyze_coverage(str(engine_dir), str(locale_dir))
    checker.calculate_quality_metrics()
    checker.generate_recommendations()
    
    # 打印摘要
    checker.print_summary()
    
    # 保存报告
    report_file = engine_dir / "reports" / "final_quality_check_report.json"
    os.makedirs(os.path.dirname(report_file), exist_ok=True)
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(checker.results, f, ensure_ascii=False, indent=2)
    
    print(f"\n📁 详细报告已保存到: {report_file}")
    
    # 返回结果
    overall_score = checker.results['quality_metrics']['overall_score']
    if overall_score >= 85:
        print("✅ 翻译键管理功能质量优秀!")
        return 0
    elif overall_score >= 75:
        print("⚠️ 翻译键管理功能质量良好，有改进空间")
        return 0
    else:
        print("❌ 翻译键管理功能需要改进")
        return 1

if __name__ == "__main__":
    exit(main())
