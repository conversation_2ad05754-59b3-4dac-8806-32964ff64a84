#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
修复翻译键格式工具 - 将 i18n: 前缀的翻译键修复为正确格式
"""

import os
import re
import sys

def fix_i18n_keys_in_file(file_path):
    """
    修复单个文件中的翻译键格式
    
    Args:
        file_path: 文件路径
        
    Returns:
        int: 修复的翻译键数量
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 修复模式1: log(_("key"), ...) -> log(_("key"), ...)
        pattern1 = r'log\("i18n:([^"]+)"'
        replacement1 = r'log(_("\1")'
        content = re.sub(pattern1, replacement1, content)
        
        # 修复模式2: user_log(_("key"), ...) -> user_log(_("key"), ...)
        pattern2 = r'user_log\("i18n:([^"]+)"'
        replacement2 = r'user_log(_("\1")'
        content = re.sub(pattern2, replacement2, content)
        
        # 修复模式3: log(_('key'), ...) -> log(_('key'), ...)
        pattern3 = r"log\('i18n:([^']+)'"
        replacement3 = r"log(_('\1')"
        content = re.sub(pattern3, replacement3, content)
        
        # 修复模式4: user_log(_('key'), ...) -> user_log(_('key'), ...)
        pattern4 = r"user_log\('i18n:([^']+)'"
        replacement4 = r"user_log(_('\1')"
        content = re.sub(pattern4, replacement4, content)
        
        # 计算修复数量
        fixes_count = 0
        if content != original_content:
            # 计算修复的数量
            fixes_count += len(re.findall(pattern1, original_content))
            fixes_count += len(re.findall(pattern2, original_content))
            fixes_count += len(re.findall(pattern3, original_content))
            fixes_count += len(re.findall(pattern4, original_content))
            
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
        
        return fixes_count
        
    except Exception as e:
        print(f"❌ 修复文件 {file_path} 失败: {e}")
        return 0

def fix_i18n_keys_in_directory(directory):
    """
    修复目录中所有Python文件的翻译键格式
    
    Args:
        directory: 目录路径
        
    Returns:
        tuple: (修复的文件数量, 修复的翻译键总数)
    """
    fixed_files = 0
    total_fixes = 0
    
    for root, dirs, files in os.walk(directory):
        # 跳过一些目录
        dirs[:] = [d for d in dirs if d not in ['.git', '__pycache__', '.pytest_cache', 'node_modules']]
        
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                fixes_count = fix_i18n_keys_in_file(file_path)
                
                if fixes_count > 0:
                    fixed_files += 1
                    total_fixes += fixes_count
                    print(f"✅ 修复文件: {file_path} ({fixes_count} 个翻译键)")
    
    return fixed_files, total_fixes

def add_missing_import(file_path):
    """
    为文件添加缺失的 i18n 导入
    
    Args:
        file_path: 文件路径
        
    Returns:
        bool: 是否添加了导入
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经有 i18n 导入
        if 'from engine.utils.i18n import _' in content:
            return False
        
        # 检查是否使用了 _() 函数
        if '_("' not in content and "_(" not in content:
            return False
        
        # 查找合适的位置插入导入
        lines = content.split('\n')
        insert_index = 0
        
        # 找到最后一个 import 语句的位置
        for i, line in enumerate(lines):
            if line.strip().startswith('import ') or line.strip().startswith('from '):
                insert_index = i + 1
        
        # 如果没有找到 import 语句，在文件开头插入
        if insert_index == 0:
            # 跳过 shebang 和编码声明
            for i, line in enumerate(lines):
                if line.strip().startswith('#') and ('coding' in line or 'encoding' in line or line.startswith('#!')):
                    insert_index = i + 1
                else:
                    break
        
        # 插入导入语句
        lines.insert(insert_index, 'from engine.utils.i18n import _')
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines))
        
        return True
        
    except Exception as e:
        print(f"❌ 添加导入到文件 {file_path} 失败: {e}")
        return False

def add_missing_imports_in_directory(directory):
    """
    为目录中需要的文件添加缺失的 i18n 导入
    
    Args:
        directory: 目录路径
        
    Returns:
        int: 添加导入的文件数量
    """
    added_imports = 0
    
    for root, dirs, files in os.walk(directory):
        # 跳过一些目录
        dirs[:] = [d for d in dirs if d not in ['.git', '__pycache__', '.pytest_cache', 'node_modules']]
        
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                if add_missing_import(file_path):
                    added_imports += 1
                    print(f"✅ 添加导入: {file_path}")
    
    return added_imports

def main():
    """主函数"""
    print("🔧 翻译键格式修复工具")
    print("=" * 60)
    
    # 获取引擎目录
    engine_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    print(f"引擎目录: {engine_dir}")
    
    print("\n1. 修复翻译键格式...")
    fixed_files, total_fixes = fix_i18n_keys_in_directory(engine_dir)
    
    print(f"\n📊 翻译键修复结果:")
    print(f"  - 修复的文件: {fixed_files}")
    print(f"  - 修复的翻译键: {total_fixes}")
    
    print("\n2. 添加缺失的导入...")
    added_imports = add_missing_imports_in_directory(engine_dir)
    
    print(f"\n📊 导入添加结果:")
    print(f"  - 添加导入的文件: {added_imports}")
    
    if fixed_files > 0 or added_imports > 0:
        print(f"\n✅ 修复完成!")
        print(f"  - 总共修复了 {fixed_files} 个文件中的 {total_fixes} 个翻译键")
        print(f"  - 为 {added_imports} 个文件添加了缺失的导入")
    else:
        print(f"\n✅ 没有发现需要修复的翻译键!")
    
    return fixed_files > 0 or added_imports > 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
