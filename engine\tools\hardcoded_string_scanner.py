#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
硬编码字符串扫描工具
扫描代码库中的硬编码中英文字符串，特别是日志、错误信息、用户界面文本
"""

import os
import re
import json
import ast
from typing import Dict, List, Set, Tuple
from pathlib import Path
from dataclasses import dataclass

@dataclass
class HardcodedString:
    """硬编码字符串信息"""
    file_path: str
    line_number: int
    content: str
    context: str
    category: str
    severity: str

class HardcodedStringScanner:
    """硬编码字符串扫描器"""
    
    def __init__(self):
        # 中文字符模式
        self.chinese_pattern = re.compile(r'[\u4e00-\u9fff]+')
        
        # 英文消息模式（常见的错误、警告、信息模式）
        self.english_message_patterns = [
            re.compile(r'\b(error|failed|failure|exception|invalid|missing|not found|timeout|permission denied)\b', re.IGNORECASE),
            re.compile(r'\b(warning|warn|caution|notice)\b', re.IGNORECASE),
            re.compile(r'\b(success|successful|completed|finished|done)\b', re.IGNORECASE),
            re.compile(r'\b(loading|processing|converting|generating|validating)\b', re.IGNORECASE),
            re.compile(r'\b(configuration|config|setting|option|parameter)\b', re.IGNORECASE),
        ]
        
        # 需要跳过的文件和目录
        self.skip_patterns = [
            r'__pycache__',
            r'\.git',
            r'\.pytest_cache',
            r'node_modules',
            r'\.pyc$',
            r'\.pyo$',
            r'\.egg-info',
            r'test_.*\.py$',
            r'.*_test\.py$',
        ]
        
        # 需要跳过的字符串模式（不是用户可见的）
        self.skip_string_patterns = [
            r'^[a-zA-Z_][a-zA-Z0-9_]*$',  # 变量名
            r'^[A-Z_]+$',  # 常量名
            r'^\d+$',  # 纯数字
            r'^[a-f0-9]{8,}$',  # 哈希值
            r'^https?://',  # URL
            r'^[a-zA-Z]:\\',  # Windows路径
            r'^/[a-zA-Z]',  # Unix路径
            r'^\w+\.\w+$',  # 文件名
            r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',  # 邮箱
        ]
        
        # 函数调用上下文模式
        self.context_patterns = {
            'log': re.compile(r'(log|logger|logging)\s*\.\s*\w+\s*\('),
            'print': re.compile(r'print\s*\('),
            'raise': re.compile(r'raise\s+\w+\s*\('),
            'return': re.compile(r'return\s+'),
            'assert': re.compile(r'assert\s+'),
            'format': re.compile(r'\.format\s*\('),
            'f-string': re.compile(r'f["\']'),
        }
        
        self.hardcoded_strings = []
    
    def should_skip_file(self, file_path: str) -> bool:
        """检查是否应该跳过文件"""
        for pattern in self.skip_patterns:
            if re.search(pattern, file_path):
                return True
        return False
    
    def should_skip_string(self, content: str) -> bool:
        """检查是否应该跳过字符串"""
        # 跳过空字符串或只有空白字符的字符串
        if not content.strip():
            return True
        
        # 跳过太短的字符串
        if len(content.strip()) < 3:
            return True
        
        # 检查跳过模式
        for pattern in self.skip_string_patterns:
            if re.match(pattern, content.strip()):
                return True
        
        return False
    
    def detect_context(self, line: str, string_pos: int) -> str:
        """检测字符串的上下文"""
        # 获取字符串前的内容
        before_string = line[:string_pos]
        
        for context_name, pattern in self.context_patterns.items():
            if pattern.search(before_string):
                return context_name
        
        return 'unknown'
    
    def categorize_string(self, content: str, context: str) -> Tuple[str, str]:
        """对字符串进行分类"""
        # 检查是否包含中文
        has_chinese = bool(self.chinese_pattern.search(content))
        
        # 检查是否是英文消息
        has_english_message = any(pattern.search(content) for pattern in self.english_message_patterns)
        
        # 确定类别
        if has_chinese:
            category = 'chinese_text'
        elif has_english_message:
            category = 'english_message'
        elif context in ['log', 'print', 'raise']:
            category = 'potential_message'
        else:
            category = 'other_string'
        
        # 确定严重程度
        if context in ['log', 'print', 'raise'] and (has_chinese or has_english_message):
            severity = 'high'
        elif has_chinese or has_english_message:
            severity = 'medium'
        else:
            severity = 'low'
        
        return category, severity
    
    def extract_strings_from_line(self, line: str, line_number: int, file_path: str):
        """从代码行中提取字符串"""
        # 使用正则表达式查找字符串字面量
        string_patterns = [
            re.compile(r'"([^"\\]|\\.)*"'),  # 双引号字符串
            re.compile(r"'([^'\\]|\\.)*'"),  # 单引号字符串
            re.compile(r'f"([^"\\]|\\.)*"'),  # f-string双引号
            re.compile(r"f'([^'\\]|\\.)*'"),  # f-string单引号
        ]
        
        for pattern in string_patterns:
            for match in pattern.finditer(line):
                string_content = match.group(0)
                
                # 移除引号和f前缀
                clean_content = string_content
                if clean_content.startswith('f'):
                    clean_content = clean_content[1:]
                clean_content = clean_content[1:-1]  # 移除引号
                
                # 跳过不需要的字符串
                if self.should_skip_string(clean_content):
                    continue
                
                # 检测上下文
                context = self.detect_context(line, match.start())
                
                # 分类字符串
                category, severity = self.categorize_string(clean_content, context)
                
                # 只记录需要国际化的字符串
                if category in ['chinese_text', 'english_message', 'potential_message']:
                    hardcoded_string = HardcodedString(
                        file_path=file_path,
                        line_number=line_number,
                        content=clean_content,
                        context=context,
                        category=category,
                        severity=severity
                    )
                    self.hardcoded_strings.append(hardcoded_string)
    
    def scan_file(self, file_path: str):
        """扫描单个文件"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
            
            for line_number, line in enumerate(lines, 1):
                self.extract_strings_from_line(line.strip(), line_number, file_path)
        
        except Exception as e:
            print(f"扫描文件失败 {file_path}: {e}")
    
    def scan_directory(self, directory: str):
        """扫描目录"""
        for root, dirs, files in os.walk(directory):
            # 过滤目录
            dirs[:] = [d for d in dirs if not any(re.search(pattern, d) for pattern in self.skip_patterns)]
            
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    if not self.should_skip_file(file_path):
                        self.scan_file(file_path)
    
    def generate_report(self) -> Dict:
        """生成扫描报告"""
        # 按类别统计
        category_stats = {}
        severity_stats = {}
        context_stats = {}
        
        for hs in self.hardcoded_strings:
            category_stats[hs.category] = category_stats.get(hs.category, 0) + 1
            severity_stats[hs.severity] = severity_stats.get(hs.severity, 0) + 1
            context_stats[hs.context] = context_stats.get(hs.context, 0) + 1
        
        # 按文件统计
        file_stats = {}
        for hs in self.hardcoded_strings:
            if hs.file_path not in file_stats:
                file_stats[hs.file_path] = []
            file_stats[hs.file_path].append({
                'line': hs.line_number,
                'content': hs.content,
                'category': hs.category,
                'severity': hs.severity,
                'context': hs.context
            })
        
        return {
            'summary': {
                'total_strings': len(self.hardcoded_strings),
                'category_stats': category_stats,
                'severity_stats': severity_stats,
                'context_stats': context_stats,
                'files_affected': len(file_stats)
            },
            'files': file_stats,
            'strings': [
                {
                    'file': hs.file_path,
                    'line': hs.line_number,
                    'content': hs.content,
                    'category': hs.category,
                    'severity': hs.severity,
                    'context': hs.context
                }
                for hs in self.hardcoded_strings
            ]
        }
    
    def save_report(self, output_file: str):
        """保存扫描报告"""
        report = self.generate_report()
        
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
    
    def print_summary(self):
        """打印扫描摘要"""
        report = self.generate_report()
        summary = report['summary']
        
        print(f"\n📊 硬编码字符串扫描结果:")
        print(f"  总计: {summary['total_strings']} 个硬编码字符串")
        print(f"  影响文件: {summary['files_affected']} 个")
        
        print(f"\n📂 按类别统计:")
        for category, count in summary['category_stats'].items():
            print(f"  {category}: {count}")
        
        print(f"\n⚠️ 按严重程度统计:")
        for severity, count in summary['severity_stats'].items():
            print(f"  {severity}: {count}")
        
        print(f"\n🔍 按上下文统计:")
        for context, count in summary['context_stats'].items():
            print(f"  {context}: {count}")

def main():
    """主函数"""
    scanner = HardcodedStringScanner()
    
    # 获取引擎目录
    engine_dir = Path(__file__).parent.parent
    
    print("🔍 扫描硬编码字符串...")
    scanner.scan_directory(str(engine_dir))
    
    # 打印摘要
    scanner.print_summary()
    
    # 保存详细报告
    report_file = engine_dir / "reports" / "hardcoded_strings_report.json"
    scanner.save_report(str(report_file))
    print(f"\n📁 详细报告已保存到: {report_file}")
    
    # 显示一些高优先级的示例
    high_priority = [hs for hs in scanner.hardcoded_strings if hs.severity == 'high']
    if high_priority:
        print(f"\n🚨 高优先级硬编码字符串示例 (前10个):")
        for i, hs in enumerate(high_priority[:10]):
            print(f"  {i+1}. {hs.file_path}:{hs.line_number}")
            print(f"     内容: {hs.content}")
            print(f"     类别: {hs.category}, 上下文: {hs.context}")

if __name__ == "__main__":
    main()
