#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
国际化内容生成器
为硬编码字符串生成国际化键和翻译内容
"""

import os
import re
import json
from typing import Dict, List, Set, Tuple
from pathlib import Path
from dataclasses import dataclass

@dataclass
class I18nEntry:
    """国际化条目"""
    key: str
    zh_text: str
    en_text: str
    category: str
    context: str

class I18nContentGenerator:
    """国际化内容生成器"""
    
    def __init__(self):
        self.entries = []
        self.existing_keys = set()
        self.content_to_key = {}  # 内容到键的映射，用于去重

        # 模块映射
        self.module_mapping = {
            'convert.py': 'converter',
            'compress_demo.py': 'compress',
            'yang_generator.py': 'generator.yang',
            'ntos_generator.py': 'generator.ntos',
            'xml_generator.py': 'generator.xml',
            'fortigate_parser.py': 'parser.fortigate',
            'policy_processor.py': 'processor.policy',
            'address_processor.py': 'processor.address',
            'service_processor.py': 'processor.service',
            'interface_handler.py': 'handler.interface',
            'validation_service.py': 'service.validation',
            'conversion_service.py': 'service.conversion',
            'workflow.py': 'workflow',
            'pipeline.py': 'pipeline',
        }
        
        # 上下文到功能的映射
        self.context_mapping = {
            'raise': 'error',
            'log': 'info',
            'print': 'debug',
            'return': 'message',
            'assert': 'error',
            'f-string': 'message',
        }
        
        # 常见英文翻译模式
        self.translation_patterns = {
            # 错误消息
            r'(.+)失败': r'\1 failed',
            r'(.+)错误': r'\1 error',
            r'(.+)异常': r'\1 exception',
            r'无法(.+)': r'Cannot \1',
            r'不支持(.+)': r'\1 not supported',
            r'缺少(.+)': r'Missing \1',
            r'未找到(.+)': r'\1 not found',
            r'(.+)不存在': r'\1 does not exist',
            
            # 状态消息
            r'开始(.+)': r'Starting \1',
            r'完成(.+)': r'Completed \1',
            r'正在(.+)': r'Processing \1',
            r'已(.+)': r'\1 completed',
            r'成功(.+)': r'Successfully \1',
            
            # 配置消息
            r'加载(.+)': r'Loading \1',
            r'保存(.+)': r'Saving \1',
            r'生成(.+)': r'Generating \1',
            r'创建(.+)': r'Creating \1',
            r'删除(.+)': r'Deleting \1',
            r'更新(.+)': r'Updating \1',
        }
    
    def load_existing_translations(self, file_path: str):
        """加载现有翻译"""
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                translations = json.load(f)
                self.existing_keys = set(translations.keys())
    
    def generate_key(self, file_path: str, content: str, context: str) -> str:
        """生成国际化键"""
        # 获取文件名
        file_name = os.path.basename(file_path)

        # 获取模块名
        module = self.module_mapping.get(file_name, 'misc')

        # 获取功能名
        function = self.context_mapping.get(context, 'message')

        # 生成内容描述
        content_desc = self.generate_content_description(content)

        # 组合键名
        key = f"{module}.{function}.{content_desc}"

        # 确保键名唯一且规范
        key = self.normalize_key(key)

        # 避免重复
        counter = 1
        base_key = key
        while key in self.existing_keys:
            key = f"{base_key}_{counter}"
            counter += 1

        self.existing_keys.add(key)
        return key

    def normalize_key(self, key: str) -> str:
        """规范化键名"""
        # 转换为小写
        key = key.lower()

        # 替换连字符为下划线
        key = key.replace('-', '_')

        # 移除特殊字符，只保留字母、数字、下划线和点号
        key = re.sub(r'[^a-z0-9_.]', '_', key)

        # 移除多余的下划线
        key = re.sub(r'_+', '_', key)

        # 移除开头和结尾的下划线
        parts = key.split('.')
        normalized_parts = []
        for part in parts:
            part = part.strip('_')
            if part:  # 确保部分不为空
                normalized_parts.append(part)

        # 重新组合
        key = '.'.join(normalized_parts)

        # 确保符合格式 module.function.content
        if key.count('.') < 2:
            key = f"misc.message.{key.replace('.', '_')}"

        return key

    def get_content_hash(self, content: str) -> str:
        """获取内容的哈希值用于去重"""
        import hashlib
        # 规范化内容：移除多余空格，转换为小写
        normalized = re.sub(r'\s+', ' ', content.strip().lower())
        return hashlib.md5(normalized.encode('utf-8')).hexdigest()

    def generate_content_description(self, content: str) -> str:
        """生成内容描述（只使用英文字符）"""
        # 移除参数占位符
        desc = re.sub(r'\{[^}]+\}', '', content)

        # 提取关键词
        keywords = []

        # 中文关键词翻译映射
        chinese_translations = {
            '错误': 'error',
            '失败': 'failed',
            '成功': 'success',
            '完成': 'completed',
            '开始': 'start',
            '处理': 'process',
            '生成': 'generate',
            '创建': 'create',
            '删除': 'delete',
            '更新': 'update',
            '加载': 'load',
            '保存': 'save',
            '文件': 'file',
            '目录': 'directory',
            '路径': 'path',
            '配置': 'config',
            '验证': 'validate',
            '检查': 'check',
            '转换': 'convert',
            '解析': 'parse',
            '库': 'library',
            '系统': 'system',
            '环境': 'environment',
            '容器': 'container',
            '权限': 'permission',
            '临时': 'temp',
            '清理': 'cleanup',
            '搜索': 'search',
            '标准': 'standard',
            '全局': 'global',
            '特殊': 'special',
            '准备': 'prepare',
            '设置': 'setup',
            '方法': 'method',
            '策略': 'strategy',
            '冲突': 'conflict',
            '避免': 'avoid',
            '最小化': 'minimize',
            '干扰': 'interference',
            '关键': 'critical',
            '缺少': 'missing',
            '存在': 'exists',
            '指定': 'specified',
            '当前': 'current',
            '专用': 'dedicated',
            '加密': 'encryption',
            '析构': 'destructor',
            '函数': 'function',
            '传统': 'traditional',
            '针对': 'for'
        }

        # 中文关键词提取和翻译
        if re.search(r'[\u4e00-\u9fff]', desc):
            chinese_words = re.findall(r'[\u4e00-\u9fff]+', desc)
            for word in chinese_words[:3]:  # 最多取3个词
                if word in chinese_translations:
                    keywords.append(chinese_translations[word])
                else:
                    # 对于未知中文词，使用拼音或简化处理
                    simplified = self.simplify_chinese_word(word)
                    if simplified:
                        keywords.append(simplified)

        # 英文关键词提取
        english_words = re.findall(r'\b[a-zA-Z]{3,}\b', desc.lower())
        if english_words:
            # 过滤常见词
            common_words = {
                'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can',
                'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has',
                'him', 'his', 'how', 'man', 'new', 'now', 'old', 'see', 'two',
                'way', 'who', 'boy', 'did', 'its', 'let', 'put', 'say', 'she',
                'too', 'use', 'with', 'from', 'they', 'this', 'that', 'will',
                'been', 'have', 'were', 'said', 'each', 'which', 'their'
            }
            filtered_words = [w for w in english_words if w not in common_words]
            keywords.extend(filtered_words[:3])  # 最多取3个词

        # 如果没有提取到关键词，使用内容的哈希
        if not keywords:
            import hashlib
            hash_obj = hashlib.md5(content.encode('utf-8'))
            return f"content_{hash_obj.hexdigest()[:8]}"

        # 组合关键词，只保留前3个
        desc = '_'.join(keywords[:3])

        # 清理描述，只保留英文字符、数字和下划线
        desc = re.sub(r'[^a-z0-9_]', '_', desc.lower())
        desc = re.sub(r'_+', '_', desc)
        desc = desc.strip('_')

        return desc[:30]  # 限制长度

    def simplify_chinese_word(self, word: str) -> str:
        """简化中文词汇为英文描述"""
        # 基于词汇特征的简化映射
        if '文件' in word or '档案' in word:
            return 'file'
        elif '目录' in word or '文件夹' in word:
            return 'dir'
        elif '错误' in word or '异常' in word:
            return 'error'
        elif '成功' in word or '完成' in word:
            return 'success'
        elif '失败' in word:
            return 'failed'
        elif '开始' in word or '启动' in word:
            return 'start'
        elif '处理' in word:
            return 'process'
        elif '生成' in word or '创建' in word:
            return 'create'
        elif '删除' in word or '移除' in word:
            return 'remove'
        elif '加载' in word:
            return 'load'
        elif '保存' in word:
            return 'save'
        elif '配置' in word or '设置' in word:
            return 'config'
        elif '验证' in word or '检查' in word:
            return 'check'
        elif '转换' in word:
            return 'convert'
        elif '解析' in word:
            return 'parse'
        else:
            # 对于其他词汇，返回None
            return None
    
    def translate_to_english(self, chinese_text: str) -> str:
        """将中文翻译为英文"""
        # 扩展的翻译词典
        translation_dict = {
            # 基础动词
            '开始': 'start', '启动': 'start', '初始化': 'initialize',
            '完成': 'complete', '结束': 'finish', '终止': 'terminate',
            '处理': 'process', '执行': 'execute', '运行': 'run',
            '生成': 'generate', '创建': 'create', '建立': 'create',
            '删除': 'delete', '移除': 'remove', '清理': 'cleanup',
            '加载': 'load', '载入': 'load', '导入': 'import',
            '保存': 'save', '存储': 'store', '写入': 'write',
            '读取': 'read', '获取': 'get', '取得': 'obtain',
            '更新': 'update', '修改': 'modify', '变更': 'change',
            '转换': 'convert', '变换': 'transform', '翻译': 'translate',
            '解析': 'parse', '分析': 'analyze', '检查': 'check',
            '验证': 'validate', '确认': 'confirm', '测试': 'test',
            '配置': 'configure', '设置': 'setup', '安装': 'install',
            '连接': 'connect', '断开': 'disconnect', '关闭': 'close',
            '打开': 'open', '启用': 'enable', '禁用': 'disable',

            # 状态词
            '成功': 'successful', '失败': 'failed', '错误': 'error',
            '警告': 'warning', '信息': 'information', '调试': 'debug',
            '正常': 'normal', '异常': 'abnormal', '有效': 'valid',
            '无效': 'invalid', '可用': 'available', '不可用': 'unavailable',
            '存在': 'exists', '不存在': 'not exist', '缺少': 'missing',
            '找到': 'found', '未找到': 'not found', '发现': 'discovered',
            '支持': 'supported', '不支持': 'not supported',

            # 对象名词
            '文件': 'file', '目录': 'directory', '路径': 'path',
            '配置': 'configuration', '参数': 'parameter', '选项': 'option',
            '数据': 'data', '信息': 'information', '内容': 'content',
            '结果': 'result', '输出': 'output', '输入': 'input',
            '系统': 'system', '服务': 'service', '进程': 'process',
            '线程': 'thread', '任务': 'task', '作业': 'job',
            '用户': 'user', '组': 'group', '权限': 'permission',
            '网络': 'network', '接口': 'interface', '端口': 'port',
            '地址': 'address', '域名': 'domain', '主机': 'host',
            '服务器': 'server', '客户端': 'client', '连接': 'connection',

            # 技术术语
            '库': 'library', '模块': 'module', '组件': 'component',
            '插件': 'plugin', '扩展': 'extension', '驱动': 'driver',
            '缓存': 'cache', '内存': 'memory', '存储': 'storage',
            '数据库': 'database', '表': 'table', '字段': 'field',
            '索引': 'index', '查询': 'query', '事务': 'transaction',
            '备份': 'backup', '恢复': 'restore', '同步': 'synchronize',
            '压缩': 'compress', '解压': 'decompress', '加密': 'encrypt',
            '解密': 'decrypt', '签名': 'signature', '证书': 'certificate',

            # 容器和环境
            '容器': 'container', '环境': 'environment', '虚拟': 'virtual',
            '物理': 'physical', '本地': 'local', '远程': 'remote',
            '全局': 'global', '局部': 'local', '临时': 'temporary',
            '永久': 'permanent', '默认': 'default', '自定义': 'custom',

            # 修饰词
            '特殊': 'special', '普通': 'normal', '标准': 'standard',
            '高级': 'advanced', '基础': 'basic', '简单': 'simple',
            '复杂': 'complex', '快速': 'fast', '慢速': 'slow',
            '安全': 'secure', '危险': 'dangerous', '稳定': 'stable',
            '不稳定': 'unstable', '可靠': 'reliable', '不可靠': 'unreliable',

            # 数量和时间
            '所有': 'all', '部分': 'partial', '单个': 'single',
            '多个': 'multiple', '第一': 'first', '最后': 'last',
            '当前': 'current', '之前': 'previous', '下一个': 'next',
            '总计': 'total', '数量': 'count', '大小': 'size',
            '长度': 'length', '时间': 'time', '日期': 'date'
        }

        # 先应用原有的翻译模式
        english_text = chinese_text
        for zh_pattern, en_replacement in self.translation_patterns.items():
            english_text = re.sub(zh_pattern, en_replacement, english_text)

        # 如果模式匹配成功，直接返回
        if english_text != chinese_text:
            return english_text

        # 逐词翻译
        result_parts = []
        # 分割中文文本，保留标点符号和参数占位符
        parts = re.split(r'([\u4e00-\u9fff]+|[a-zA-Z]+|\{[^}]+\}|[^\u4e00-\u9fff\w\s])', chinese_text)

        for part in parts:
            if not part.strip():
                continue
            elif re.match(r'[\u4e00-\u9fff]+', part):
                # 中文部分，尝试翻译
                translated = translation_dict.get(part, part)
                result_parts.append(translated)
            elif re.match(r'\{[^}]+\}', part):
                # 参数占位符，保持不变
                result_parts.append(part)
            else:
                # 其他部分（英文、数字、标点符号），保持不变
                result_parts.append(part)

        result = ' '.join(result_parts)

        # 清理多余的空格
        result = re.sub(r'\s+', ' ', result).strip()

        # 如果仍然包含中文，标记为需要翻译
        if re.search(r'[\u4e00-\u9fff]', result):
            return f"[需要翻译] {result}"

        return result
    
    def process_hardcoded_strings(self, report_file: str):
        """处理硬编码字符串报告"""
        if not os.path.exists(report_file):
            print(f"❌ 硬编码字符串报告不存在: {report_file}")
            return
        
        with open(report_file, 'r', encoding='utf-8') as f:
            report = json.load(f)
        
        print(f"🔍 处理 {len(report['strings'])} 个硬编码字符串...")
        
        for item in report['strings']:
            file_path = item['file']
            content = item['content']
            context = item['context']
            category = item['category']

            # 跳过低优先级的字符串
            if item['severity'] == 'low':
                continue

            # 内容去重：如果相同内容已经处理过，跳过
            content_hash = self.get_content_hash(content)
            if content_hash in self.content_to_key:
                continue

            # 生成键名
            key = self.generate_key(file_path, content, context)

            # 记录内容映射
            self.content_to_key[content_hash] = key

            # 确定中英文文本
            if re.search(r'[\u4e00-\u9fff]', content):
                # 中文文本
                zh_text = content
                en_text = self.translate_to_english(content)
            else:
                # 英文文本
                en_text = content
                zh_text = f"[需要翻译] {content}"

            # 创建条目
            entry = I18nEntry(
                key=key,
                zh_text=zh_text,
                en_text=en_text,
                category=category,
                context=context
            )
            self.entries.append(entry)
        
        print(f"生成了 {len(self.entries)} 个国际化条目")
    
    def generate_translation_files(self, output_dir: str):
        """生成翻译文件"""
        os.makedirs(output_dir, exist_ok=True)
        
        # 生成中文翻译
        zh_translations = {}
        for entry in self.entries:
            zh_translations[entry.key] = entry.zh_text
        
        zh_file = os.path.join(output_dir, "zh-CN.new.json")
        with open(zh_file, 'w', encoding='utf-8') as f:
            json.dump(zh_translations, f, ensure_ascii=False, indent=2)
        
        # 生成英文翻译
        en_translations = {}
        for entry in self.entries:
            en_translations[entry.key] = entry.en_text
        
        en_file = os.path.join(output_dir, "en-US.new.json")
        with open(en_file, 'w', encoding='utf-8') as f:
            json.dump(en_translations, f, ensure_ascii=False, indent=2)
        
        print(f"📁 新翻译文件已生成:")
        print(f"  中文: {zh_file}")
        print(f"  英文: {en_file}")
    
    def generate_replacement_script(self, output_file: str):
        """生成替换脚本"""
        script_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动生成的硬编码字符串替换脚本
"""

import os
import re
from typing import Dict

# 字符串替换映射
STRING_REPLACEMENTS = {
'''
        
        for entry in self.entries:
            # 转义特殊字符
            original = entry.zh_text if re.search(r'[\u4e00-\u9fff]', entry.zh_text) else entry.en_text
            escaped_original = original.replace('\\', '\\\\').replace('"', '\\"')
            script_content += f'    "{escaped_original}": "{entry.key}",\n'
        
        script_content += '''}

def replace_hardcoded_strings(file_path: str):
    """替换文件中的硬编码字符串"""
    if not file_path.endswith('.py'):
        return
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 替换硬编码字符串
        for original, key in STRING_REPLACEMENTS.items():
            # 替换字符串字面量
            patterns = [
                rf'"{re.escape(original)}"',
                rf"'{re.escape(original)}'",
                rf'f"{re.escape(original)}"',
                rf"f'{re.escape(original)}'",
            ]
            
            for pattern in patterns:
                replacement = f'_("{key}")'
                content = re.sub(pattern, replacement, content)
        
        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"已替换: {file_path}")
    
    except Exception as e:
        print(f"替换文件失败 {file_path}: {e}")

def replace_directory(directory: str):
    """替换目录中的所有Python文件"""
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                replace_hardcoded_strings(file_path)

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1:
        replace_directory(sys.argv[1])
    else:
        print("用法: python replace_hardcoded_strings.py <目录路径>")
'''
        
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        # 设置执行权限
        os.chmod(output_file, 0o755)
        print(f"🔧 替换脚本已生成: {output_file}")
    
    def generate_summary_report(self) -> Dict:
        """生成摘要报告"""
        # 按类别统计
        category_stats = {}
        context_stats = {}
        
        for entry in self.entries:
            category_stats[entry.category] = category_stats.get(entry.category, 0) + 1
            context_stats[entry.context] = context_stats.get(entry.context, 0) + 1
        
        # 需要手动翻译的条目
        manual_translation_needed = [
            entry for entry in self.entries
            if entry.zh_text.startswith('[需要翻译]') or entry.en_text.startswith('[需要翻译]')
        ]
        
        return {
            'summary': {
                'total_entries': len(self.entries),
                'manual_translation_needed': len(manual_translation_needed),
                'category_stats': category_stats,
                'context_stats': context_stats
            },
            'manual_translation_list': [
                {
                    'key': entry.key,
                    'zh_text': entry.zh_text,
                    'en_text': entry.en_text,
                    'category': entry.category
                }
                for entry in manual_translation_needed
            ]
        }

def main():
    """主函数"""
    generator = I18nContentGenerator()
    
    # 获取路径
    engine_dir = Path(__file__).parent.parent
    locale_dir = engine_dir / "locales"
    reports_dir = engine_dir / "reports"
    
    # 加载现有翻译
    clean_file = locale_dir / "zh-CN.clean.json"
    if clean_file.exists():
        generator.load_existing_translations(str(clean_file))
    
    # 处理硬编码字符串
    report_file = reports_dir / "hardcoded_strings_report.json"
    generator.process_hardcoded_strings(str(report_file))
    
    # 生成翻译文件
    generator.generate_translation_files(str(locale_dir))
    
    # 生成替换脚本
    script_file = engine_dir / "tools" / "replace_hardcoded_strings.py"
    generator.generate_replacement_script(str(script_file))
    
    # 生成摘要报告
    summary = generator.generate_summary_report()
    summary_file = reports_dir / "i18n_generation_summary.json"
    os.makedirs(os.path.dirname(summary_file), exist_ok=True)
    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump(summary, f, ensure_ascii=False, indent=2)
    
    # 打印摘要
    print(f"\n📊 国际化内容生成摘要:")
    print(f"  总条目数: {summary['summary']['total_entries']}")
    print(f"  需要手动翻译: {summary['summary']['manual_translation_needed']}")
    
    print(f"\n📂 按类别统计:")
    for category, count in summary['summary']['category_stats'].items():
        print(f"  {category}: {count}")
    
    print(f"\n🔍 按上下文统计:")
    for context, count in summary['summary']['context_stats'].items():
        print(f"  {context}: {count}")
    
    print(f"\n📁 摘要报告已保存到: {summary_file}")

if __name__ == "__main__":
    main()
