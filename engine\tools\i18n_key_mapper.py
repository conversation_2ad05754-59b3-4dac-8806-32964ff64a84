#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
国际化键映射工具
用于将旧的翻译键映射到新的命名规范
"""

import os
import re
import json
from typing import Dict, List, Tuple, Set
from pathlib import Path

class I18nKeyMapper:
    """国际化键映射器"""
    
    def __init__(self):
        self.key_mappings = {}
        self.module_patterns = {
            # 核心处理模块
            'parser': ['parser', 'parse', 'parsing'],
            'converter': ['convert', 'conversion', 'transform'],
            'generator': ['generate', 'generation', 'create'],
            'validator': ['validate', 'validation', 'verify'],
            'processor': ['process', 'processing', 'handle'],
            
            # 功能模块
            'interface': ['interface', 'port', 'intf'],
            'address': ['address', 'addr', 'ip'],
            'service': ['service', 'svc', 'port'],
            'policy': ['policy', 'rule', 'security'],
            'nat': ['nat', 'translation'],
            'dns': ['dns', 'domain'],
            'route': ['route', 'routing', 'static'],
            'zone': ['zone', 'security_zone'],
            
            # 系统模块
            'workflow': ['workflow', 'flow', 'process'],
            'pipeline': ['pipeline', 'stage', 'step'],
            'template': ['template', 'tmpl'],
            'config': ['config', 'configuration', 'setting'],
            'cache': ['cache', 'memory'],
            'monitor': ['monitor', 'performance', 'perf']
        }
        
        self.function_patterns = {
            # 操作类型
            'start': ['start', 'begin', 'init', 'initialize'],
            'complete': ['complete', 'finish', 'done', 'completed'],
            'process': ['process', 'processing', 'handle'],
            'convert': ['convert', 'transform', 'translate'],
            'generate': ['generate', 'create', 'build'],
            'validate': ['validate', 'verify', 'check'],
            'load': ['load', 'read', 'import'],
            'save': ['save', 'write', 'export'],
            
            # 状态类型
            'success': ['success', 'successful', 'ok'],
            'failed': ['failed', 'fail', 'error'],
            'error': ['error', 'err', 'exception'],
            'warning': ['warning', 'warn'],
            'info': ['info', 'information'],
            'debug': ['debug', 'trace']
        }
        
        self.content_patterns = {
            # 消息类型
            'message': ['message', 'msg'],
            'description': ['description', 'desc'],
            'detail': ['detail', 'details'],
            'summary': ['summary'],
            'report': ['report'],
            
            # 错误类型
            'not_found': ['not_found', 'missing', 'notfound'],
            'invalid': ['invalid', 'illegal'],
            'duplicate': ['duplicate', 'dup'],
            'timeout': ['timeout'],
            'permission': ['permission', 'access'],
            
            # 数据类型
            'count': ['count', 'num', 'number'],
            'size': ['size', 'length'],
            'path': ['path', 'file', 'dir'],
            'name': ['name'],
            'type': ['type'],
            'value': ['value', 'val']
        }
    
    def analyze_old_key(self, old_key: str) -> Tuple[str, str, str]:
        """分析旧键名，提取模块、功能、内容信息"""
        # 清理键名
        key = old_key.lower().replace('-', '_')
        
        # 检测模块
        module = 'system'  # 默认模块
        for mod, patterns in self.module_patterns.items():
            if any(pattern in key for pattern in patterns):
                module = mod
                break
        
        # 检测功能
        function = 'info'  # 默认功能
        for func, patterns in self.function_patterns.items():
            if any(pattern in key for pattern in patterns):
                function = func
                break
        
        # 检测内容
        content = 'message'  # 默认内容
        for cont, patterns in self.content_patterns.items():
            if any(pattern in key for pattern in patterns):
                content = cont
                break
        
        return module, function, content
    
    def generate_new_key(self, old_key: str) -> str:
        """根据旧键名生成新的标准键名"""
        module, function, content = self.analyze_old_key(old_key)
        
        # 特殊处理某些键名
        if old_key.startswith('error.'):
            return f"system.error.{content}"
        elif old_key.startswith('warning.'):
            return f"system.warning.{content}"
        elif old_key.startswith('info.'):
            return f"system.info.{content}"
        elif old_key.startswith('debug.'):
            return f"system.debug.{content}"
        
        # 生成标准键名
        new_key = f"{module}.{function}.{content}"
        
        # 避免重复
        counter = 1
        base_key = new_key
        while new_key in self.key_mappings.values():
            new_key = f"{base_key}_{counter}"
            counter += 1
        
        return new_key
    
    def create_mapping_table(self, translation_file: str) -> Dict[str, str]:
        """创建键名映射表"""
        if not os.path.exists(translation_file):
            return {}
        
        with open(translation_file, 'r', encoding='utf-8') as f:
            translations = json.load(f)
        
        mappings = {}
        for old_key in translations.keys():
            new_key = self.generate_new_key(old_key)
            mappings[old_key] = new_key
        
        self.key_mappings = mappings
        return mappings
    
    def save_mapping_table(self, output_file: str):
        """保存映射表到文件"""
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(self.key_mappings, f, ensure_ascii=False, indent=2)
    
    def apply_mappings_to_translation_file(self, input_file: str, output_file: str):
        """将映射应用到翻译文件"""
        with open(input_file, 'r', encoding='utf-8') as f:
            translations = json.load(f)
        
        new_translations = {}
        for old_key, value in translations.items():
            new_key = self.key_mappings.get(old_key, old_key)
            new_translations[new_key] = value
        
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(new_translations, f, ensure_ascii=False, indent=2)
    
    def validate_key_format(self, key: str) -> bool:
        """验证键名格式是否符合规范"""
        pattern = r'^[a-z][a-z_]*\.[a-z][a-z_]*\.[a-z][a-z_]*$'
        return bool(re.match(pattern, key))
    
    def generate_migration_script(self, output_file: str):
        """生成迁移脚本"""
        script_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动生成的国际化键名迁移脚本
"""

import os
import re
from typing import Dict

# 键名映射表
KEY_MAPPINGS = {
'''
        
        for old_key, new_key in self.key_mappings.items():
            script_content += f'    "{old_key}": "{new_key}",\n'
        
        script_content += '''}

def migrate_file(file_path: str):
    """迁移单个文件中的键名"""
    if not file_path.endswith('.py'):
        return
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 替换翻译键
        for old_key, new_key in KEY_MAPPINGS.items():
            # 替换 _("old_key") 格式
            content = re.sub(
                rf'_\\("{re.escape(old_key)}"\\)',
                f'_("{new_key}")',
                content
            )
            
            # 替换 log("old_key") 格式
            content = re.sub(
                rf'log\\("{re.escape(old_key)}"',
                f'log("{new_key}"',
                content
            )
        
        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"已迁移: {file_path}")
    
    except Exception as e:
        print(f"迁移文件失败 {file_path}: {e}")

def migrate_directory(directory: str):
    """迁移目录中的所有Python文件"""
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                migrate_file(file_path)

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1:
        migrate_directory(sys.argv[1])
    else:
        print("用法: python migration_script.py <目录路径>")
'''
        
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        # 设置执行权限
        os.chmod(output_file, 0o755)

def main():
    """主函数"""
    mapper = I18nKeyMapper()
    
    # 获取当前目录
    current_dir = Path(__file__).parent.parent
    locale_dir = current_dir / "locales"
    
    # 创建映射表
    zh_file = locale_dir / "zh-CN.json"
    if zh_file.exists():
        print("🔍 分析现有翻译键...")
        mappings = mapper.create_mapping_table(str(zh_file))
        print(f"找到 {len(mappings)} 个翻译键")
        
        # 保存映射表
        mapping_file = locale_dir / "key_mappings.json"
        mapper.save_mapping_table(str(mapping_file))
        print(f"📁 映射表已保存到: {mapping_file}")
        
        # 生成迁移脚本
        script_file = current_dir / "tools" / "migrate_i18n_keys.py"
        mapper.generate_migration_script(str(script_file))
        print(f"🔧 迁移脚本已生成: {script_file}")
        
        # 显示一些示例映射
        print("\n📝 键名映射示例:")
        for i, (old_key, new_key) in enumerate(list(mappings.items())[:10]):
            print(f"  {old_key} -> {new_key}")
        
        if len(mappings) > 10:
            print(f"  ... 还有 {len(mappings) - 10} 个映射")
    
    else:
        print(f"❌ 翻译文件不存在: {zh_file}")

if __name__ == "__main__":
    main()
