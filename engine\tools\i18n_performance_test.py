#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
国际化性能测试工具
测试翻译系统的性能和内存使用情况
"""

import time
import psutil
import os
import json
import threading
from typing import Dict, List
from pathlib import Path
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed

# 添加engine路径
import sys
engine_dir = Path(__file__).parent.parent
sys.path.insert(0, str(engine_dir))

from utils.i18n import (
    translate, safe_translate, _,
    clear_translation_cache, get_cache_stats,
    load_translations
)

@dataclass
class PerformanceResult:
    """性能测试结果"""
    test_name: str
    duration: float
    operations_per_second: float
    memory_usage_mb: float
    cache_hit_rate: float
    error_count: int

class I18nPerformanceTester:
    """国际化性能测试器"""
    
    def __init__(self):
        self.results = []
        self.test_keys = [
            "test.message",
            "test.with_param",
            "test.multiple_params",
            "error.not_found",
            "info.success",
            "warning.deprecated",
            "address_processing_stage.completed",
            "conversion_workflow.yang_validation_completed",
            "fortigate_strategy.nat_rule_generated",
        ]
        
        # 创建测试翻译数据
        self.test_translations = {}
        for i in range(1000):
            key = f"test.key_{i}"
            self.test_translations[key] = f"测试翻译内容 {i}: {{param1}}, {{param2}}"
    
    def get_memory_usage(self) -> float:
        """获取当前内存使用量（MB）"""
        process = psutil.Process(os.getpid())
        return process.memory_info().rss / 1024 / 1024
    
    def test_basic_translation_performance(self, iterations: int = 10000) -> PerformanceResult:
        """测试基本翻译性能"""
        print(f"🔍 测试基本翻译性能 ({iterations} 次迭代)...")
        
        clear_translation_cache()
        start_memory = self.get_memory_usage()
        error_count = 0
        
        start_time = time.time()
        
        for i in range(iterations):
            try:
                key = self.test_keys[i % len(self.test_keys)]
                translate(key, "zh-CN")
            except Exception:
                error_count += 1
        
        end_time = time.time()
        end_memory = self.get_memory_usage()
        
        duration = end_time - start_time
        ops_per_second = iterations / duration if duration > 0 else 0
        memory_usage = end_memory - start_memory
        
        cache_stats = get_cache_stats()
        cache_hit_rate = cache_stats.get('hit_rate', 0)
        
        result = PerformanceResult(
            test_name="基本翻译性能",
            duration=duration,
            operations_per_second=ops_per_second,
            memory_usage_mb=memory_usage,
            cache_hit_rate=cache_hit_rate,
            error_count=error_count
        )
        
        self.results.append(result)
        return result
    
    def test_parameter_substitution_performance(self, iterations: int = 5000) -> PerformanceResult:
        """测试参数替换性能"""
        print(f"🔍 测试参数替换性能 ({iterations} 次迭代)...")
        
        clear_translation_cache()
        start_memory = self.get_memory_usage()
        error_count = 0
        
        start_time = time.time()
        
        for i in range(iterations):
            try:
                translate(
                    "test.multiple_params", 
                    "zh-CN",
                    name=f"用户{i}",
                    count=i,
                    value=f"值{i}"
                )
            except Exception:
                error_count += 1
        
        end_time = time.time()
        end_memory = self.get_memory_usage()
        
        duration = end_time - start_time
        ops_per_second = iterations / duration if duration > 0 else 0
        memory_usage = end_memory - start_memory
        
        cache_stats = get_cache_stats()
        cache_hit_rate = cache_stats.get('hit_rate', 0)
        
        result = PerformanceResult(
            test_name="参数替换性能",
            duration=duration,
            operations_per_second=ops_per_second,
            memory_usage_mb=memory_usage,
            cache_hit_rate=cache_hit_rate,
            error_count=error_count
        )
        
        self.results.append(result)
        return result
    
    def test_concurrent_translation_performance(self, threads: int = 10, iterations_per_thread: int = 1000) -> PerformanceResult:
        """测试并发翻译性能"""
        print(f"🔍 测试并发翻译性能 ({threads} 线程，每线程 {iterations_per_thread} 次迭代)...")
        
        clear_translation_cache()
        start_memory = self.get_memory_usage()
        error_count = 0
        total_iterations = threads * iterations_per_thread
        
        def worker_function(thread_id: int) -> int:
            """工作线程函数"""
            local_errors = 0
            for i in range(iterations_per_thread):
                try:
                    key = self.test_keys[(thread_id * iterations_per_thread + i) % len(self.test_keys)]
                    translate(key, "zh-CN", thread_id=thread_id, iteration=i)
                except Exception:
                    local_errors += 1
            return local_errors
        
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=threads) as executor:
            futures = [executor.submit(worker_function, i) for i in range(threads)]
            
            for future in as_completed(futures):
                try:
                    error_count += future.result()
                except Exception:
                    error_count += iterations_per_thread
        
        end_time = time.time()
        end_memory = self.get_memory_usage()
        
        duration = end_time - start_time
        ops_per_second = total_iterations / duration if duration > 0 else 0
        memory_usage = end_memory - start_memory
        
        cache_stats = get_cache_stats()
        cache_hit_rate = cache_stats.get('hit_rate', 0)
        
        result = PerformanceResult(
            test_name="并发翻译性能",
            duration=duration,
            operations_per_second=ops_per_second,
            memory_usage_mb=memory_usage,
            cache_hit_rate=cache_hit_rate,
            error_count=error_count
        )
        
        self.results.append(result)
        return result
    
    def test_cache_performance(self, iterations: int = 20000) -> PerformanceResult:
        """测试缓存性能"""
        print(f"🔍 测试缓存性能 ({iterations} 次迭代)...")
        
        # 预热缓存
        for key in self.test_keys:
            translate(key, "zh-CN")
        
        start_memory = self.get_memory_usage()
        error_count = 0
        
        start_time = time.time()
        
        # 重复访问相同的键，测试缓存命中
        for i in range(iterations):
            try:
                key = self.test_keys[i % len(self.test_keys)]
                translate(key, "zh-CN")
            except Exception:
                error_count += 1
        
        end_time = time.time()
        end_memory = self.get_memory_usage()
        
        duration = end_time - start_time
        ops_per_second = iterations / duration if duration > 0 else 0
        memory_usage = end_memory - start_memory
        
        cache_stats = get_cache_stats()
        cache_hit_rate = cache_stats.get('hit_rate', 0)
        
        result = PerformanceResult(
            test_name="缓存性能",
            duration=duration,
            operations_per_second=ops_per_second,
            memory_usage_mb=memory_usage,
            cache_hit_rate=cache_hit_rate,
            error_count=error_count
        )
        
        self.results.append(result)
        return result
    
    def test_memory_usage_under_load(self, iterations: int = 50000) -> PerformanceResult:
        """测试高负载下的内存使用"""
        print(f"🔍 测试高负载内存使用 ({iterations} 次迭代)...")
        
        clear_translation_cache()
        start_memory = self.get_memory_usage()
        error_count = 0
        
        # 记录内存使用变化
        memory_samples = []
        
        start_time = time.time()
        
        for i in range(iterations):
            try:
                # 使用不同的参数组合，增加缓存压力
                key = f"test.key_{i % 1000}"
                translate(
                    "test.multiple_params",
                    "zh-CN",
                    name=f"用户{i}",
                    count=i,
                    unique_id=f"id_{i}"
                )
                
                # 每1000次迭代记录一次内存使用
                if i % 1000 == 0:
                    memory_samples.append(self.get_memory_usage())
            
            except Exception:
                error_count += 1
        
        end_time = time.time()
        end_memory = self.get_memory_usage()
        
        duration = end_time - start_time
        ops_per_second = iterations / duration if duration > 0 else 0
        memory_usage = end_memory - start_memory
        
        cache_stats = get_cache_stats()
        cache_hit_rate = cache_stats.get('hit_rate', 0)
        
        # 检查内存是否稳定（没有内存泄漏）
        if len(memory_samples) > 2:
            memory_growth = memory_samples[-1] - memory_samples[0]
            if memory_growth > 100:  # 超过100MB增长
                print(f"⚠️ 警告：检测到可能的内存泄漏，内存增长: {memory_growth:.2f}MB")
        
        result = PerformanceResult(
            test_name="高负载内存使用",
            duration=duration,
            operations_per_second=ops_per_second,
            memory_usage_mb=memory_usage,
            cache_hit_rate=cache_hit_rate,
            error_count=error_count
        )
        
        self.results.append(result)
        return result
    
    def run_all_tests(self) -> List[PerformanceResult]:
        """运行所有性能测试"""
        print("🚀 开始国际化性能测试...")
        
        # 运行各项测试
        self.test_basic_translation_performance()
        self.test_parameter_substitution_performance()
        self.test_concurrent_translation_performance()
        self.test_cache_performance()
        self.test_memory_usage_under_load()
        
        return self.results
    
    def generate_report(self) -> Dict:
        """生成性能报告"""
        if not self.results:
            return {}
        
        # 计算总体统计
        total_ops = sum(r.operations_per_second for r in self.results)
        avg_ops = total_ops / len(self.results)
        
        total_memory = sum(r.memory_usage_mb for r in self.results)
        avg_memory = total_memory / len(self.results)
        
        total_errors = sum(r.error_count for r in self.results)
        
        # 找出最佳和最差性能
        best_performance = max(self.results, key=lambda r: r.operations_per_second)
        worst_performance = min(self.results, key=lambda r: r.operations_per_second)
        
        return {
            'summary': {
                'total_tests': len(self.results),
                'average_ops_per_second': round(avg_ops, 2),
                'average_memory_usage_mb': round(avg_memory, 2),
                'total_errors': total_errors,
                'best_performance': {
                    'test': best_performance.test_name,
                    'ops_per_second': round(best_performance.operations_per_second, 2)
                },
                'worst_performance': {
                    'test': worst_performance.test_name,
                    'ops_per_second': round(worst_performance.operations_per_second, 2)
                }
            },
            'detailed_results': [
                {
                    'test_name': r.test_name,
                    'duration': round(r.duration, 3),
                    'operations_per_second': round(r.operations_per_second, 2),
                    'memory_usage_mb': round(r.memory_usage_mb, 2),
                    'cache_hit_rate': round(r.cache_hit_rate, 2),
                    'error_count': r.error_count
                }
                for r in self.results
            ]
        }
    
    def print_report(self):
        """打印性能报告"""
        report = self.generate_report()
        
        if not report:
            print("❌ 没有测试结果")
            return
        
        summary = report['summary']
        
        print(f"\n📊 国际化性能测试报告:")
        print(f"  总测试数: {summary['total_tests']}")
        print(f"  平均操作/秒: {summary['average_ops_per_second']}")
        print(f"  平均内存使用: {summary['average_memory_usage_mb']:.2f}MB")
        print(f"  总错误数: {summary['total_errors']}")
        
        print(f"\n🏆 最佳性能: {summary['best_performance']['test']} ({summary['best_performance']['ops_per_second']} ops/s)")
        print(f"⚠️ 最差性能: {summary['worst_performance']['test']} ({summary['worst_performance']['ops_per_second']} ops/s)")
        
        print(f"\n📋 详细结果:")
        for result in report['detailed_results']:
            print(f"  {result['test_name']}:")
            print(f"    操作/秒: {result['operations_per_second']}")
            print(f"    耗时: {result['duration']}s")
            print(f"    内存: {result['memory_usage_mb']}MB")
            print(f"    缓存命中率: {result['cache_hit_rate']}%")
            print(f"    错误数: {result['error_count']}")

def main():
    """主函数"""
    tester = I18nPerformanceTester()
    
    # 运行所有测试
    results = tester.run_all_tests()
    
    # 打印报告
    tester.print_report()
    
    # 保存报告
    report = tester.generate_report()
    report_file = engine_dir / "reports" / "i18n_performance_report.json"
    os.makedirs(os.path.dirname(report_file), exist_ok=True)
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n📁 性能报告已保存到: {report_file}")
    
    # 检查性能基准
    summary = report['summary']
    if summary['average_ops_per_second'] < 1000:
        print("⚠️ 警告：平均性能低于基准 (1000 ops/s)")
        return 1
    
    if summary['total_errors'] > 0:
        print(f"⚠️ 警告：发现 {summary['total_errors']} 个错误")
        return 1
    
    print("✅ 所有性能测试通过")
    return 0

if __name__ == "__main__":
    exit(main())
