#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
国际化翻译键扫描和补充工具
扫描代码中使用的翻译键，并补充缺失的翻译
"""

import os
import re
import json
import sys
from typing import Set, Dict, List
from engine.utils.i18n import _

def scan_translation_keys(directory: str) -> Set[str]:
    """
    扫描目录中所有Python文件，提取翻译键
    
    Args:
        directory: 要扫描的目录
        
    Returns: Set[str]: 找到的所有翻译键
    """
    translation_keys = set()
    
    # 匹配翻译键的正则表达式
    patterns = [
        r'_\("([^"]+)"\)',  # _("key")
        r"_\('([^']+)'\)",  # _('key')
        r'log\("([^"]+)"',  # log("key")
        r"log\('([^']+)'",  # log('key')
        r'"(i18n:[^"]+)"',  # "i18n:key"
        r"'(i18n:[^']+)'",  # 'i18n:key'
    ]
    
    compiled_patterns = [re.compile(pattern) for pattern in patterns]
    
    for root, dirs, files in os.walk(directory):
        # 跳过一些目录
        dirs[:] = [d for d in dirs if d not in ['.git', '__pycache__', '.pytest_cache', 'node_modules']]
        
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        
                        for pattern in compiled_patterns:
                            matches = pattern.findall(content)
                            for match in matches:
                                # 清理翻译键
                                key = match.strip()
                                if key:
                                    translation_keys.add(key)
                                    
                except Exception as e:
                    print(f"警告: 无法读取文件 {file_path}: {e}")
    
    return translation_keys

def load_existing_translations(locale_file: str) -> Dict[str, str]:
    """
    加载现有的翻译文件
    
    Args:
        locale_file: 翻译文件路径
        
    Returns: Dict[str, str]: 现有翻译
    """
    if not os.path.exists(locale_file):
        return {}
    
    try:
        with open(locale_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"警告: 无法加载翻译文件 {locale_file}: {e}")
        return {}

def generate_missing_translations(missing_keys: Set[str]) -> Dict[str, str]:
    """
    为缺失的翻译键生成默认翻译
    
    Args:
        missing_keys: 缺失的翻译键
        
    Returns: Dict[str, str]: 生成的翻译
    """
    translations = {}
    
    for key in missing_keys:
        # 移除i18n:前缀
        clean_key = key.replace('i18n:', '')
        
        # 根据键的模式生成翻译
        if clean_key.startswith('error.'):
            translations[key] = f"错误: {clean_key.replace('error.', '').replace('_', ' ')}"
        elif clean_key.startswith('info.'):
            translations[key] = f"信息: {clean_key.replace('info.', '').replace('_', ' ')}"
        elif clean_key.startswith('warning.'):
            translations[key] = f"警告: {clean_key.replace('warning.', '').replace('_', ' ')}"
        elif clean_key.startswith('debug.'):
            translations[key] = f"调试: {clean_key.replace('debug.', '').replace('_', ' ')}"
        elif clean_key.startswith('conversion_service.'):
            service_key = clean_key.replace('conversion_service.', '').replace('_', ' ')
            translations[key] = f"转换服务: {service_key}"
        elif clean_key.startswith('conversion_workflow.'):
            workflow_key = clean_key.replace('conversion_workflow.', '').replace('_', ' ')
            translations[key] = f"转换工作流: {workflow_key}"
        elif clean_key.startswith('template_manager.'):
            template_key = clean_key.replace('template_manager.', '').replace('_', ' ')
            translations[key] = f"模板管理器: {template_key}"
        elif clean_key.startswith('yang_manager.'):
            yang_key = clean_key.replace('yang_manager.', '').replace('_', ' ')
            translations[key] = f"YANG管理器: {yang_key}"
        elif clean_key.startswith('config_manager.'):
            config_key = clean_key.replace('config_manager.', '').replace('_', ' ')
            translations[key] = f"配置管理器: {config_key}"
        elif clean_key.startswith('performance_monitor.'):
            perf_key = clean_key.replace('performance_monitor.', '').replace('_', ' ')
            translations[key] = f"性能监控: {perf_key}"
        elif clean_key.startswith('memory_manager.'):
            mem_key = clean_key.replace('memory_manager.', '').replace('_', ' ')
            translations[key] = f"内存管理: {mem_key}"
        elif clean_key.startswith('error_handler.'):
            error_key = clean_key.replace('error_handler.', '').replace('_', ' ')
            translations[key] = f"错误处理: {error_key}"
        elif clean_key.startswith('cache_manager.'):
            cache_key = clean_key.replace('cache_manager.', '').replace('_', ' ')
            translations[key] = f"缓存管理: {cache_key}"
        elif clean_key.startswith('lru_cache.'):
            lru_key = clean_key.replace('lru_cache.', '').replace('_', ' ')
            translations[key] = f"LRU缓存: {lru_key}"
        elif clean_key.startswith('parser_registry.'):
            parser_key = clean_key.replace('parser_registry.', '').replace('_', ' ')
            translations[key] = f"解析器注册表: {parser_key}"
        elif clean_key.startswith('generator_registry.'):
            gen_key = clean_key.replace('generator_registry.', '').replace('_', ' ')
            translations[key] = f"生成器注册表: {gen_key}"
        elif clean_key.startswith('pipeline_manager.'):
            pipeline_key = clean_key.replace('pipeline_manager.', '').replace('_', ' ')
            translations[key] = f"管道管理器: {pipeline_key}"
        else:
            # 默认翻译
            translations[key] = clean_key.replace('_', ' ').replace('.', ': ')
    
    return translations

def update_translation_file(locale_file: str, new_translations: Dict[str, str]):
    """
    更新翻译文件
    
    Args:
        locale_file: 翻译文件路径
        new_translations: 新的翻译
    """
    # 加载现有翻译
    existing = load_existing_translations(locale_file)
    
    # 合并翻译
    existing.update(new_translations)
    
    # 排序键
    sorted_translations = dict(sorted(existing.items()))
    
    # 保存文件
    os.makedirs(os.path.dirname(locale_file), exist_ok=True)
    with open(locale_file, 'w', encoding='utf-8') as f:
        json.dump(sorted_translations, f, ensure_ascii=False, indent=4)

def main():
    """主函数"""
    # 获取引擎目录
    engine_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    locale_file = os.path.join(engine_dir, 'locales', 'zh-CN.json')
    
    print("🔍 扫描翻译键...")
    
    # 扫描翻译键
    found_keys = scan_translation_keys(engine_dir)
    print(f"找到 {len(found_keys)} 个翻译键")
    
    # 加载现有翻译
    existing_translations = load_existing_translations(locale_file)
    print(f"现有翻译: {len(existing_translations)} 个")
    
    # 找出缺失的翻译键
    missing_keys = found_keys - set(existing_translations.keys())
    print(f"缺失翻译: {len(missing_keys)} 个")
    
    if missing_keys:
        print("\n📝 缺失的翻译键:")
        for key in sorted(missing_keys):
            print(f"  - {key}")
        
        print(f"\n🔧 生成缺失翻译...")
        new_translations = generate_missing_translations(missing_keys)
        
        print(f"📁 更新翻译文件: {locale_file}")
        update_translation_file(locale_file, new_translations)
        
        print(f"✅ 完成! 添加了 {len(new_translations)} 个新翻译")
    else:
        print("✅ 所有翻译键都已存在!")
    
    # 显示一些统计信息
    print(f"\n📊 统计信息:")
    print(f"  - 总翻译键: {len(found_keys)}")
    print(f"  - 现有翻译: {len(existing_translations)}")
    print(f"  - 新增翻译: {len(missing_keys)}")
    print(f"  - 覆盖率: {((len(existing_translations) + len(missing_keys)) / len(found_keys) * 100):.1f}%")

if __name__ == "__main__":
    main()
