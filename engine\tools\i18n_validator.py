#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
国际化验证工具
自动检测翻译完整性，防止翻译错误或遗漏
"""

import os
import re
import json
import ast
from typing import Dict, List, Set, Tuple, Optional
from pathlib import Path
from dataclasses import dataclass
from collections import defaultdict

@dataclass
class ValidationIssue:
    """验证问题"""
    type: str
    severity: str
    file_path: str
    line_number: int
    message: str
    suggestion: str

class I18nValidator:
    """国际化验证器"""
    
    def __init__(self):
        self.issues = []
        self.translation_keys_in_code = set()
        self.translation_keys_in_files = {}
        self.hardcoded_strings = []
        
        # 验证规则
        self.validation_rules = {
            'missing_translations': True,
            'unused_translations': True,
            'parameter_mismatches': True,
            'hardcoded_strings': True,
            'key_format_validation': True,
            'duplicate_translations': True,
            'encoding_issues': True,
        }
    
    def scan_code_for_translation_keys(self, directory: str):
        """扫描代码中使用的翻译键"""
        print("🔍 扫描代码中的翻译键...")
        
        patterns = [
            re.compile(r'_\(["\']([^"\']+)["\']\)'),  # _("key")
            re.compile(r'log\(["\']([^"\']+)["\']'),  # log("key")
            re.compile(r'safe_translate\(["\']([^"\']+)["\']'),  # safe_translate("key")
            re.compile(r'translate\(["\']([^"\']+)["\']'),  # translate("key")
            re.compile(r'["\']i18n:([^"\']+)["\']'),  # "i18n:key"
        ]
        
        for root, dirs, files in os.walk(directory):
            dirs[:] = [d for d in dirs if d not in ['.git', '__pycache__', '.pytest_cache']]
            
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                            lines = f.readlines()
                        
                        for line_num, line in enumerate(lines, 1):
                            for pattern in patterns:
                                matches = pattern.findall(line)
                                for match in matches:
                                    key = match.strip()
                                    if key.startswith('i18n:'):
                                        key = key[5:]
                                    self.translation_keys_in_code.add(key)
                    
                    except Exception as e:
                        self.add_issue(
                            'scan_error', 'warning', file_path, 0,
                            f"扫描文件失败: {str(e)}",
                            "检查文件编码和权限"
                        )
        
        print(f"找到 {len(self.translation_keys_in_code)} 个翻译键")
    
    def load_translation_files(self, locale_dir: str):
        """加载翻译文件"""
        print("📁 加载翻译文件...")
        
        for file_name in ['zh-CN.json', 'en-US.json', 'zh-CN.clean.json']:
            file_path = os.path.join(locale_dir, file_name)
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        translations = json.load(f)
                        self.translation_keys_in_files[file_name] = translations
                        print(f"  {file_name}: {len(translations)} 个翻译")
                except Exception as e:
                    self.add_issue(
                        'file_load_error', 'error', file_path, 0,
                        f"加载翻译文件失败: {str(e)}",
                        "检查JSON格式和文件编码"
                    )
    
    def validate_missing_translations(self):
        """验证缺失的翻译"""
        if not self.validation_rules['missing_translations']:
            return
        
        print("🔍 检查缺失的翻译...")
        
        # 获取主要翻译文件
        main_file = None
        for file_name in ['zh-CN.clean.json', 'zh-CN.json']:
            if file_name in self.translation_keys_in_files:
                main_file = file_name
                break
        
        if not main_file:
            self.add_issue(
                'missing_main_file', 'error', '', 0,
                "未找到主要翻译文件",
                "确保存在 zh-CN.json 或 zh-CN.clean.json 文件"
            )
            return
        
        available_keys = set(self.translation_keys_in_files[main_file].keys())
        missing_keys = self.translation_keys_in_code - available_keys
        
        for key in missing_keys:
            self.add_issue(
                'missing_translation', 'error', main_file, 0,
                f"缺失翻译键: {key}",
                f"在 {main_file} 中添加翻译: \"{key}\": \"[翻译内容]\""
            )
        
        print(f"发现 {len(missing_keys)} 个缺失的翻译")
    
    def validate_unused_translations(self):
        """验证未使用的翻译"""
        if not self.validation_rules['unused_translations']:
            return
        
        print("🔍 检查未使用的翻译...")
        
        for file_name, translations in self.translation_keys_in_files.items():
            available_keys = set(translations.keys())
            unused_keys = available_keys - self.translation_keys_in_code
            
            # 过滤掉可能的动态键
            truly_unused = []
            for key in unused_keys:
                # 检查是否有相似的键在使用中
                key_prefix = key.split('.')[0] if '.' in key else key
                if not any(used_key.startswith(key_prefix) for used_key in self.translation_keys_in_code):
                    truly_unused.append(key)
            
            for key in truly_unused:
                self.add_issue(
                    'unused_translation', 'warning', file_name, 0,
                    f"未使用的翻译键: {key}",
                    f"考虑从 {file_name} 中删除此键"
                )
            
            print(f"  {file_name}: {len(truly_unused)} 个未使用的翻译")
    
    def validate_parameter_mismatches(self):
        """验证参数不匹配"""
        if not self.validation_rules['parameter_mismatches']:
            return
        
        print("🔍 检查参数不匹配...")
        
        # 比较中英文翻译的参数
        zh_file = None
        en_file = None
        
        for file_name in self.translation_keys_in_files:
            if 'zh' in file_name.lower():
                zh_file = file_name
            elif 'en' in file_name.lower():
                en_file = file_name
        
        if not zh_file or not en_file:
            return
        
        zh_translations = self.translation_keys_in_files[zh_file]
        en_translations = self.translation_keys_in_files[en_file]
        
        common_keys = set(zh_translations.keys()) & set(en_translations.keys())
        
        for key in common_keys:
            zh_text = zh_translations[key]
            en_text = en_translations[key]
            
            # 提取参数占位符
            zh_params = set(re.findall(r'\{([^}]+)\}', zh_text))
            en_params = set(re.findall(r'\{([^}]+)\}', en_text))
            
            if zh_params != en_params:
                self.add_issue(
                    'parameter_mismatch', 'error', f"{zh_file} vs {en_file}", 0,
                    f"参数不匹配: {key} - 中文参数: {zh_params}, 英文参数: {en_params}",
                    "确保中英文翻译使用相同的参数占位符"
                )
    
    def validate_key_format(self):
        """验证键名格式"""
        if not self.validation_rules['key_format_validation']:
            return
        
        print("🔍 检查键名格式...")
        
        # 有效的键名格式
        valid_pattern = re.compile(r'^[a-z][a-z0-9_]*(\.[a-z][a-z0-9_]*)*$')
        
        for file_name, translations in self.translation_keys_in_files.items():
            for key in translations.keys():
                if not valid_pattern.match(key):
                    self.add_issue(
                        'invalid_key_format', 'warning', file_name, 0,
                        f"无效的键名格式: {key}",
                        "使用小写字母、数字、下划线和点号，格式: module.function.content"
                    )
    
    def validate_duplicate_translations(self):
        """验证重复翻译"""
        if not self.validation_rules['duplicate_translations']:
            return
        
        print("🔍 检查重复翻译...")
        
        for file_name, translations in self.translation_keys_in_files.items():
            value_to_keys = defaultdict(list)
            
            for key, value in translations.items():
                if isinstance(value, str) and value.strip():
                    value_to_keys[value].append(key)
            
            for value, keys in value_to_keys.items():
                if len(keys) > 1:
                    self.add_issue(
                        'duplicate_translation', 'warning', file_name, 0,
                        f"重复翻译值: '{value}' 对应键: {keys}",
                        "考虑合并重复的翻译或确认是否真的需要不同的键"
                    )
    
    def scan_hardcoded_strings(self, directory: str):
        """扫描硬编码字符串"""
        if not self.validation_rules['hardcoded_strings']:
            return
        
        print("🔍 扫描硬编码字符串...")
        
        # 中文字符模式
        chinese_pattern = re.compile(r'[\u4e00-\u9fff]+')
        
        # 常见英文消息模式
        english_patterns = [
            re.compile(r'\b(error|failed|failure|exception|invalid|missing|not found)\b', re.IGNORECASE),
            re.compile(r'\b(warning|warn|caution|notice)\b', re.IGNORECASE),
            re.compile(r'\b(success|successful|completed|finished)\b', re.IGNORECASE),
        ]
        
        for root, dirs, files in os.walk(directory):
            dirs[:] = [d for d in dirs if d not in ['.git', '__pycache__', '.pytest_cache']]
            
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                            lines = f.readlines()
                        
                        for line_num, line in enumerate(lines, 1):
                            # 查找字符串字面量
                            string_matches = re.finditer(r'["\']([^"\'\\]|\\.)*["\']', line)
                            
                            for match in string_matches:
                                string_content = match.group(0)[1:-1]  # 移除引号
                                
                                # 检查是否包含中文或英文消息
                                has_chinese = bool(chinese_pattern.search(string_content))
                                has_english_message = any(pattern.search(string_content) for pattern in english_patterns)
                                
                                if (has_chinese or has_english_message) and len(string_content.strip()) > 3:
                                    # 检查是否已经是翻译调用
                                    before_string = line[:match.start()]
                                    if not re.search(r'(_\(|log\(|translate\()', before_string):
                                        self.add_issue(
                                            'hardcoded_string', 'warning', file_path, line_num,
                                            f"硬编码字符串: {string_content}",
                                            f"使用 _(\"{string_content}\") 替换硬编码字符串"
                                        )
                    
                    except Exception as e:
                        continue  # 跳过无法读取的文件
    
    def add_issue(self, issue_type: str, severity: str, file_path: str, line_number: int, message: str, suggestion: str):
        """添加验证问题"""
        issue = ValidationIssue(
            type=issue_type,
            severity=severity,
            file_path=file_path,
            line_number=line_number,
            message=message,
            suggestion=suggestion
        )
        self.issues.append(issue)
    
    def generate_report(self) -> Dict:
        """生成验证报告"""
        # 按类型和严重程度统计
        type_stats = defaultdict(int)
        severity_stats = defaultdict(int)
        
        for issue in self.issues:
            type_stats[issue.type] += 1
            severity_stats[issue.severity] += 1
        
        # 按文件分组
        file_issues = defaultdict(list)
        for issue in self.issues:
            file_issues[issue.file_path].append({
                'type': issue.type,
                'severity': issue.severity,
                'line': issue.line_number,
                'message': issue.message,
                'suggestion': issue.suggestion
            })
        
        return {
            'summary': {
                'total_issues': len(self.issues),
                'type_stats': dict(type_stats),
                'severity_stats': dict(severity_stats),
                'files_with_issues': len(file_issues)
            },
            'issues_by_file': dict(file_issues),
            'all_issues': [
                {
                    'type': issue.type,
                    'severity': issue.severity,
                    'file': issue.file_path,
                    'line': issue.line_number,
                    'message': issue.message,
                    'suggestion': issue.suggestion
                }
                for issue in self.issues
            ]
        }
    
    def print_summary(self):
        """打印验证摘要"""
        report = self.generate_report()
        summary = report['summary']
        
        print(f"\n📊 国际化验证结果:")
        print(f"  总问题数: {summary['total_issues']}")
        print(f"  影响文件: {summary['files_with_issues']}")
        
        print(f"\n📂 按类型统计:")
        for issue_type, count in summary['type_stats'].items():
            print(f"  {issue_type}: {count}")
        
        print(f"\n⚠️ 按严重程度统计:")
        for severity, count in summary['severity_stats'].items():
            print(f"  {severity}: {count}")
        
        # 显示一些高优先级问题
        error_issues = [issue for issue in self.issues if issue.severity == 'error']
        if error_issues:
            print(f"\n🚨 错误问题示例 (前5个):")
            for i, issue in enumerate(error_issues[:5]):
                print(f"  {i+1}. {issue.file_path}:{issue.line_number}")
                print(f"     {issue.message}")
                print(f"     建议: {issue.suggestion}")

def main():
    """主函数"""
    validator = I18nValidator()
    
    # 获取路径
    engine_dir = Path(__file__).parent.parent
    locale_dir = engine_dir / "locales"
    
    # 执行验证
    validator.scan_code_for_translation_keys(str(engine_dir))
    validator.load_translation_files(str(locale_dir))
    
    validator.validate_missing_translations()
    validator.validate_unused_translations()
    validator.validate_parameter_mismatches()
    validator.validate_key_format()
    validator.validate_duplicate_translations()
    validator.scan_hardcoded_strings(str(engine_dir))
    
    # 生成报告
    validator.print_summary()
    
    # 保存详细报告
    report = validator.generate_report()
    report_file = engine_dir / "reports" / "i18n_validation_report.json"
    os.makedirs(os.path.dirname(report_file), exist_ok=True)
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n📁 详细报告已保存到: {report_file}")
    
    # 返回错误代码
    error_count = len([issue for issue in validator.issues if issue.severity == 'error'])
    return 1 if error_count > 0 else 0

if __name__ == "__main__":
    exit(main())
