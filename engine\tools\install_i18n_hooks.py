#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
国际化钩子安装工具
安装Git pre-commit钩子和CI配置
"""

import os
import shutil
import stat
from pathlib import Path

def install_pre_commit_hook():
    """安装pre-commit钩子"""
    print("🔧 安装Git pre-commit钩子...")
    
    # 获取项目根目录
    script_dir = Path(__file__).parent
    project_dir = script_dir.parent.parent
    
    # Git钩子目录
    git_hooks_dir = project_dir / ".git" / "hooks"
    
    if not git_hooks_dir.exists():
        print("❌ 未找到.git/hooks目录，请确保在Git仓库中运行")
        return False
    
    # 源钩子文件
    source_hook = script_dir / "pre-commit-i18n-check.sh"
    target_hook = git_hooks_dir / "pre-commit"
    
    if not source_hook.exists():
        print(f"❌ 源钩子文件不存在: {source_hook}")
        return False
    
    try:
        # 备份现有钩子
        if target_hook.exists():
            backup_hook = git_hooks_dir / "pre-commit.backup"
            shutil.copy2(target_hook, backup_hook)
            print(f"📁 已备份现有钩子到: {backup_hook}")
        
        # 复制新钩子
        shutil.copy2(source_hook, target_hook)
        
        # 设置执行权限（Unix/Linux/Mac）
        if os.name != 'nt':  # 不是Windows
            current_permissions = stat.S_IMODE(os.lstat(target_hook).st_mode)
            os.chmod(target_hook, current_permissions | stat.S_IEXEC)
        
        print(f"✅ Pre-commit钩子已安装到: {target_hook}")
        return True
    
    except Exception as e:
        print(f"❌ 安装pre-commit钩子失败: {str(e)}")
        return False

def create_i18n_config():
    """创建国际化配置文件"""
    print("📝 创建国际化配置文件...")
    
    script_dir = Path(__file__).parent
    engine_dir = script_dir.parent
    config_dir = engine_dir / "config"
    
    config_dir.mkdir(exist_ok=True)
    
    # 国际化配置
    i18n_config = {
        "enabled": True,
        "default_language": "zh-CN",
        "supported_languages": ["zh-CN", "en-US"],
        "locale_dir": "locales",
        "cache_enabled": True,
        "cache_size": 10000,
        "cache_ttl": 3600,
        "validation": {
            "check_hardcoded_strings": True,
            "check_missing_translations": True,
            "check_parameter_consistency": True,
            "check_key_format": True,
            "max_errors": 0,
            "max_warnings": 10
        },
        "ci": {
            "enabled": True,
            "fail_on_hardcoded_strings": True,
            "fail_on_missing_translations": False,
            "generate_coverage_report": True
        },
        "performance": {
            "min_ops_per_second": 1000,
            "max_memory_usage_mb": 100,
            "enable_monitoring": True
        }
    }
    
    config_file = config_dir / "i18n_config.json"
    
    try:
        import json
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(i18n_config, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 配置文件已创建: {config_file}")
        return True
    
    except Exception as e:
        print(f"❌ 创建配置文件失败: {str(e)}")
        return False

def create_makefile_targets():
    """创建Makefile目标"""
    print("📝 创建Makefile国际化目标...")
    
    script_dir = Path(__file__).parent
    project_dir = script_dir.parent.parent
    makefile_path = project_dir / "Makefile"
    
    # 国际化相关的Makefile目标
    i18n_targets = """
# 国际化相关目标
.PHONY: i18n-check i18n-validate i18n-performance i18n-clean i18n-update

i18n-check:
	@echo "🔍 运行国际化检查..."
	cd engine && python tools/ci_i18n_check.py .

i18n-validate:
	@echo "🔍 运行国际化验证..."
	cd engine && python tools/i18n_validator.py

i18n-performance:
	@echo "🔍 运行国际化性能测试..."
	cd engine && python tools/i18n_performance_test.py

i18n-scan:
	@echo "🔍 扫描硬编码字符串..."
	cd engine && python tools/hardcoded_string_scanner.py

i18n-generate:
	@echo "🔧 生成国际化内容..."
	cd engine && python tools/i18n_content_generator.py

i18n-clean:
	@echo "🧹 清理翻译文件..."
	cd engine && python tools/translation_cleaner.py

i18n-update:
	@echo "🔄 更新国际化系统..."
	$(MAKE) i18n-scan
	$(MAKE) i18n-generate
	$(MAKE) i18n-clean
	$(MAKE) i18n-validate

i18n-install:
	@echo "🔧 安装国际化钩子..."
	cd engine && python tools/install_i18n_hooks.py

i18n-test:
	@echo "🧪 运行国际化测试..."
	cd engine && python -m pytest tests/test_i18n_system.py -v

i18n-all:
	@echo "🚀 运行完整国际化检查..."
	$(MAKE) i18n-check
	$(MAKE) i18n-validate
	$(MAKE) i18n-performance
	$(MAKE) i18n-test
"""
    
    try:
        # 检查Makefile是否存在
        if makefile_path.exists():
            with open(makefile_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否已经包含国际化目标
            if 'i18n-check:' in content:
                print("⚠️ Makefile已包含国际化目标，跳过添加")
                return True
            
            # 追加国际化目标
            with open(makefile_path, 'a', encoding='utf-8') as f:
                f.write(i18n_targets)
        else:
            # 创建新的Makefile
            with open(makefile_path, 'w', encoding='utf-8') as f:
                f.write("# FortiGate转换器Makefile\n")
                f.write(i18n_targets)
        
        print(f"✅ Makefile目标已添加: {makefile_path}")
        return True
    
    except Exception as e:
        print(f"❌ 创建Makefile目标失败: {str(e)}")
        return False

def create_vscode_tasks():
    """创建VSCode任务配置"""
    print("📝 创建VSCode任务配置...")
    
    script_dir = Path(__file__).parent
    project_dir = script_dir.parent.parent
    vscode_dir = project_dir / ".vscode"
    
    vscode_dir.mkdir(exist_ok=True)
    
    # VSCode任务配置
    tasks_config = {
        "version": "2.0.0",
        "tasks": [
            {
                "label": "I18n: 检查硬编码字符串",
                "type": "shell",
                "command": "python",
                "args": ["engine/tools/ci_i18n_check.py", "."],
                "group": "test",
                "presentation": {
                    "echo": True,
                    "reveal": "always",
                    "focus": False,
                    "panel": "shared"
                },
                "problemMatcher": []
            },
            {
                "label": "I18n: 验证翻译完整性",
                "type": "shell",
                "command": "python",
                "args": ["engine/tools/i18n_validator.py"],
                "group": "test",
                "presentation": {
                    "echo": True,
                    "reveal": "always",
                    "focus": False,
                    "panel": "shared"
                },
                "problemMatcher": []
            },
            {
                "label": "I18n: 性能测试",
                "type": "shell",
                "command": "python",
                "args": ["engine/tools/i18n_performance_test.py"],
                "group": "test",
                "presentation": {
                    "echo": True,
                    "reveal": "always",
                    "focus": False,
                    "panel": "shared"
                },
                "problemMatcher": []
            },
            {
                "label": "I18n: 扫描硬编码字符串",
                "type": "shell",
                "command": "python",
                "args": ["engine/tools/hardcoded_string_scanner.py"],
                "group": "build",
                "presentation": {
                    "echo": True,
                    "reveal": "always",
                    "focus": False,
                    "panel": "shared"
                },
                "problemMatcher": []
            },
            {
                "label": "I18n: 生成翻译内容",
                "type": "shell",
                "command": "python",
                "args": ["engine/tools/i18n_content_generator.py"],
                "group": "build",
                "presentation": {
                    "echo": True,
                    "reveal": "always",
                    "focus": False,
                    "panel": "shared"
                },
                "problemMatcher": []
            },
            {
                "label": "I18n: 完整检查",
                "dependsOrder": "sequence",
                "dependsOn": [
                    "I18n: 检查硬编码字符串",
                    "I18n: 验证翻译完整性",
                    "I18n: 性能测试"
                ],
                "group": "test",
                "presentation": {
                    "echo": True,
                    "reveal": "always",
                    "focus": False,
                    "panel": "shared"
                }
            }
        ]
    }
    
    tasks_file = vscode_dir / "tasks.json"
    
    try:
        import json
        
        # 如果文件已存在，合并任务
        if tasks_file.exists():
            with open(tasks_file, 'r', encoding='utf-8') as f:
                existing_config = json.load(f)
            
            # 合并任务列表
            existing_tasks = existing_config.get('tasks', [])
            new_tasks = tasks_config['tasks']
            
            # 移除重复的任务
            existing_labels = {task.get('label') for task in existing_tasks}
            filtered_new_tasks = [task for task in new_tasks if task.get('label') not in existing_labels]
            
            existing_config['tasks'] = existing_tasks + filtered_new_tasks
            tasks_config = existing_config
        
        with open(tasks_file, 'w', encoding='utf-8') as f:
            json.dump(tasks_config, f, ensure_ascii=False, indent=2)
        
        print(f"✅ VSCode任务配置已创建: {tasks_file}")
        return True
    
    except Exception as e:
        print(f"❌ 创建VSCode任务配置失败: {str(e)}")
        return False

def print_usage_instructions():
    """打印使用说明"""
    print("\n📖 国际化系统使用说明:")
    print("=" * 50)
    
    print("\n🔧 命令行工具:")
    print("  make i18n-check          # 运行CI检查")
    print("  make i18n-validate       # 验证翻译完整性")
    print("  make i18n-performance    # 性能测试")
    print("  make i18n-scan           # 扫描硬编码字符串")
    print("  make i18n-generate       # 生成翻译内容")
    print("  make i18n-clean          # 清理翻译文件")
    print("  make i18n-update         # 更新整个系统")
    print("  make i18n-all            # 完整检查")
    
    print("\n🎯 VSCode集成:")
    print("  Ctrl+Shift+P -> Tasks: Run Task -> I18n: ...")
    
    print("\n🔄 Git集成:")
    print("  Pre-commit钩子会自动检查硬编码字符串")
    print("  使用 git commit --no-verify 跳过检查")
    
    print("\n📊 CI/CD集成:")
    print("  GitHub Actions会自动运行国际化检查")
    print("  PR会显示国际化检查结果")
    
    print("\n💡 最佳实践:")
    print("  1. 使用 _() 函数包装用户可见的字符串")
    print("  2. 定期运行 make i18n-update 更新翻译")
    print("  3. 提交前运行 make i18n-check 检查问题")
    print("  4. 查看 engine/reports/ 目录下的详细报告")

def main():
    """主函数"""
    print("🚀 开始安装国际化系统...")
    
    success_count = 0
    total_count = 5
    
    # 安装各个组件
    if install_pre_commit_hook():
        success_count += 1
    
    if create_i18n_config():
        success_count += 1
    
    if create_makefile_targets():
        success_count += 1
    
    if create_vscode_tasks():
        success_count += 1
    
    # 创建reports目录
    try:
        script_dir = Path(__file__).parent
        engine_dir = script_dir.parent
        reports_dir = engine_dir / "reports"
        reports_dir.mkdir(exist_ok=True)
        print(f"✅ 报告目录已创建: {reports_dir}")
        success_count += 1
    except Exception as e:
        print(f"❌ 创建报告目录失败: {str(e)}")
    
    # 总结
    print(f"\n📊 安装结果: {success_count}/{total_count} 个组件安装成功")
    
    if success_count == total_count:
        print("✅ 国际化系统安装完成！")
        print_usage_instructions()
        return 0
    else:
        print("⚠️ 部分组件安装失败，请检查错误信息")
        return 1

if __name__ == "__main__":
    exit(main())
