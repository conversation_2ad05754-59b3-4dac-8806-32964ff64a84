#!/bin/bash
# -*- coding: utf-8 -*-
"""
Pre-commit国际化检查钩子
在提交前检查国际化问题，防止硬编码字符串进入代码库
"""

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ENGINE_DIR="$(dirname "$SCRIPT_DIR")"
PROJECT_DIR="$(dirname "$ENGINE_DIR")"

echo -e "${BLUE}🔍 运行Pre-commit国际化检查...${NC}"

# 检查Python是否可用
if ! command -v python3 &> /dev/null; then
    echo -e "${RED}❌ Python3未找到，跳过国际化检查${NC}"
    exit 0
fi

# 检查是否有Python文件变更
CHANGED_PY_FILES=$(git diff --cached --name-only --diff-filter=ACM | grep '\.py$' || true)

if [ -z "$CHANGED_PY_FILES" ]; then
    echo -e "${GREEN}✅ 没有Python文件变更，跳过国际化检查${NC}"
    exit 0
fi

echo -e "${BLUE}📁 检查的文件:${NC}"
echo "$CHANGED_PY_FILES" | sed 's/^/  /'

# 临时文件用于存储检查结果
TEMP_REPORT="/tmp/pre_commit_i18n_check.json"

# 运行快速CI检查
echo -e "${BLUE}🔍 运行快速国际化检查...${NC}"

cd "$ENGINE_DIR"

# 创建简化的检查脚本
cat > /tmp/quick_i18n_check.py << 'EOF'
#!/usr/bin/env python3
import os
import re
import sys
import json
import subprocess
from pathlib import Path

def check_staged_files():
    """检查暂存的文件"""
    issues = []
    
    # 获取暂存的Python文件
    try:
        result = subprocess.run(
            ['git', 'diff', '--cached', '--name-only', '--diff-filter=ACM'],
            capture_output=True,
            text=True,
            cwd=os.getcwd()
        )
        
        if result.returncode != 0:
            return issues
        
        staged_files = [f for f in result.stdout.strip().split('\n') if f.endswith('.py')]
        
    except Exception:
        return issues
    
    # 硬编码字符串检测模式
    hardcoded_patterns = [
        re.compile(r'["\']([^"\']*[\u4e00-\u9fff][^"\']*)["\']'),  # 中文
        re.compile(r'["\']([^"\']*(?:error|failed|failure|exception|invalid|missing|not found)[^"\']*)["\']', re.IGNORECASE),
        re.compile(r'["\']([^"\']*(?:success|successful|completed|finished)[^"\']*)["\']', re.IGNORECASE),
    ]
    
    # 跳过的字符串模式
    skip_patterns = [
        re.compile(r'^[a-zA-Z_][a-zA-Z0-9_]*$'),  # 变量名
        re.compile(r'^[A-Z_]+$'),  # 常量名
        re.compile(r'^\d+$'),  # 纯数字
        re.compile(r'^https?://'),  # URL
        re.compile(r'^[a-zA-Z]:\\'),  # Windows路径
        re.compile(r'^/[a-zA-Z]'),  # Unix路径
    ]
    
    def should_skip_string(content):
        if not content.strip() or len(content.strip()) < 3:
            return True
        for pattern in skip_patterns:
            if pattern.match(content.strip()):
                return True
        return False
    
    # 检查每个文件
    for file_path in staged_files:
        if not os.path.exists(file_path):
            continue
        
        # 跳过测试文件
        if 'test' in file_path.lower() or file_path.endswith('_test.py'):
            continue
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
            
            for line_num, line in enumerate(lines, 1):
                # 跳过已经国际化的行
                if re.search(r'(_\(|log\(|translate\(|safe_translate\()', line):
                    continue
                
                # 检查硬编码字符串
                for pattern in hardcoded_patterns:
                    matches = pattern.finditer(line)
                    for match in matches:
                        content = match.group(1)
                        
                        if should_skip_string(content):
                            continue
                        
                        issues.append({
                            'file': file_path,
                            'line': line_num,
                            'content': content,
                            'type': 'hardcoded_string'
                        })
        
        except Exception as e:
            issues.append({
                'file': file_path,
                'line': 0,
                'content': str(e),
                'type': 'file_error'
            })
    
    return issues

def main():
    issues = check_staged_files()
    
    # 输出结果
    result = {
        'total_issues': len(issues),
        'issues': issues
    }
    
    print(json.dumps(result, ensure_ascii=False, indent=2))
    
    # 返回错误代码
    return 1 if len(issues) > 0 else 0

if __name__ == "__main__":
    exit(main())
EOF

# 运行快速检查
python3 /tmp/quick_i18n_check.py > "$TEMP_REPORT"
CHECK_RESULT=$?

# 解析结果
if [ -f "$TEMP_REPORT" ]; then
    TOTAL_ISSUES=$(python3 -c "
import json
try:
    with open('$TEMP_REPORT', 'r') as f:
        data = json.load(f)
    print(data.get('total_issues', 0))
except:
    print(0)
")
    
    if [ "$TOTAL_ISSUES" -gt 0 ]; then
        echo -e "${RED}❌ 发现 $TOTAL_ISSUES 个国际化问题:${NC}"
        
        # 显示前5个问题
        python3 -c "
import json
try:
    with open('$TEMP_REPORT', 'r') as f:
        data = json.load(f)
    
    issues = data.get('issues', [])
    for i, issue in enumerate(issues[:5], 1):
        print(f\"  {i}. {issue['file']}:{issue['line']}\")
        print(f\"     硬编码字符串: {issue['content']}\")
        print(f\"     建议: 使用 _(\\\"{issue['content']}\\\") 替换\")
        print()
    
    if len(issues) > 5:
        print(f\"     ... 还有 {len(issues) - 5} 个问题\")
except Exception as e:
    print(f\"解析报告失败: {e}\")
"
        
        echo -e "${YELLOW}💡 修复建议:${NC}"
        echo "  1. 使用 _() 函数包装用户可见的字符串"
        echo "  2. 运行 'python engine/tools/replace_hardcoded_strings.py .' 自动修复"
        echo "  3. 手动检查并修复复杂的字符串"
        echo ""
        echo -e "${YELLOW}⚠️ 如果确认这些字符串不需要国际化，可以使用 --no-verify 跳过检查${NC}"
        
        # 清理临时文件
        rm -f /tmp/quick_i18n_check.py "$TEMP_REPORT"
        
        exit 1
    else
        echo -e "${GREEN}✅ 国际化检查通过${NC}"
    fi
else
    echo -e "${YELLOW}⚠️ 无法读取检查结果，跳过验证${NC}"
fi

# 清理临时文件
rm -f /tmp/quick_i18n_check.py "$TEMP_REPORT"

echo -e "${GREEN}✅ Pre-commit国际化检查完成${NC}"
exit 0
