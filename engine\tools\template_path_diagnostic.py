#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
模板路径诊断工具 - 检查新架构的模板路径配置
"""

import os
import sys

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
engine_dir = os.path.dirname(current_dir)
parent_dir = os.path.dirname(engine_dir)
sys.path.insert(0, parent_dir)

def test_config_manager():
    """测试ConfigManager的路径配置"""
    try:
        from engine.infrastructure.config.config_manager import ConfigManager
        
        print("✅ ConfigManager导入成功")
        
        config_manager = ConfigManager()
        print(f"✅ ConfigManager初始化成功")
        print(f"   - 引擎目录: {config_manager.get_engine_dir()}")
        
        # 测试模板路径
        model = "z5100s"
        version = "R10P2"
        
        print(f"\n🔍 测试模板路径 ({model}/{version}):")
        
        try:
            template_path = config_manager.get_template_path(model, version, "running")
            print(f"✅ 模板路径找到: {template_path}")
            print(f"   - 文件存在: {os.path.exists(template_path)}")
            if os.path.exists(template_path):
                file_size = os.path.getsize(template_path)
                print(f"   - 文件大小: {file_size} bytes")
        except Exception as e:
            print(f"❌ 模板路径查找失败: {e}")
        
        # 测试YANG路径
        print(f"\n🔍 测试YANG路径 ({model}/{version}):")
        
        try:
            yang_dir = config_manager.get_yang_model_dir(model, version)
            print(f"✅ YANG目录找到: {yang_dir}")
            print(f"   - 目录存在: {os.path.exists(yang_dir)}")
            if os.path.exists(yang_dir):
                files = os.listdir(yang_dir)
                print(f"   - 文件数量: {len(files)}")
                print(f"   - 前5个文件: {files[:5]}")
        except Exception as e:
            print(f"❌ YANG路径查找失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ ConfigManager测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_template_manager():
    """测试TemplateManager的模板加载"""
    try:
        from engine.infrastructure.config.config_manager import ConfigManager
        from engine.infrastructure.templates.template_manager import TemplateManager
        
        print("\n✅ TemplateManager导入成功")
        
        config_manager = ConfigManager()
        template_manager = TemplateManager(config_manager)
        
        print(f"✅ TemplateManager初始化成功")
        
        # 测试模板加载
        model = "z5100s"
        version = "R10P2"
        template_name = "running"
        
        print(f"\n🔍 测试模板加载 ({template_name}/{model}/{version}):")
        
        try:
            template_content = template_manager.load_template(template_name, model, version)
            if template_content:
                print(f"✅ 模板加载成功")
                print(f"   - 内容长度: {len(template_content)} 字符")
                print(f"   - 前100字符: {template_content[:100]}...")
            else:
                print(f"❌ 模板内容为空")
        except Exception as e:
            print(f"❌ 模板加载失败: {e}")
            import traceback
            traceback.print_exc()
        
        return True
        
    except Exception as e:
        print(f"❌ TemplateManager测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_yang_manager():
    """测试YangManager的YANG模型加载"""
    try:
        from engine.infrastructure.config.config_manager import ConfigManager
        from engine.infrastructure.yang.yang_manager import YangManager
        
        print("\n✅ YangManager导入成功")
        
        config_manager = ConfigManager()
        yang_manager = YangManager(config_manager)
        
        print(f"✅ YangManager初始化成功")
        print(f"   - yanglint可用: {yang_manager.is_yanglint_available()}")
        
        return True
        
    except Exception as e:
        print(f"❌ YangManager测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_file_system_paths():
    """检查文件系统中的实际路径"""
    print("\n🔍 检查文件系统路径:")
    
    engine_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    print(f"   - 引擎目录: {engine_dir}")
    
    # 检查模板目录
    template_base = os.path.join(engine_dir, 'data', 'input', 'configData')
    print(f"   - 模板基础目录: {template_base}")
    print(f"     存在: {os.path.exists(template_base)}")
    
    if os.path.exists(template_base):
        models = [d for d in os.listdir(template_base) if os.path.isdir(os.path.join(template_base, d))]
        print(f"     可用型号: {models}")
        
        for model in models[:2]:  # 只检查前2个
            model_dir = os.path.join(template_base, model)
            versions = [d for d in os.listdir(model_dir) if os.path.isdir(os.path.join(model_dir, d))]
            print(f"     {model} 版本: {versions}")
            
            for version in versions[:2]:  # 只检查前2个
                config_dir = os.path.join(model_dir, version, "Config")
                if os.path.exists(config_dir):
                    templates = [f for f in os.listdir(config_dir) if f.endswith('.xml')]
                    print(f"       {model}/{version} 模板: {templates}")
    
    # 检查YANG目录
    yang_base = os.path.join(engine_dir, 'data', 'input', 'yang_models')
    print(f"   - YANG基础目录: {yang_base}")
    print(f"     存在: {os.path.exists(yang_base)}")
    
    if os.path.exists(yang_base):
        models = [d for d in os.listdir(yang_base) if os.path.isdir(os.path.join(yang_base, d))]
        print(f"     可用型号: {models}")

def test_new_architecture_with_templates():
    """测试新架构是否能正确使用模板"""
    try:
        from engine.application.conversion_service import ConversionService
        import tempfile
        
        print("\n🔍 测试新架构模板使用:")
        
        # 创建测试配置
        test_config = """config system global
    set hostname "template-test-fw"
end

config system interface
    edit "port1"
        set vdom "root"
        set ip *********** *************
        set allowaccess ping https ssh
        set type physical
        set alias "Internal"
    next
end

config firewall policy
    edit 1
        set name "template_test_policy"
        set srcintf "port1"
        set dstintf "port2"
        set srcaddr "all"
        set dstaddr "all"
        set service "ALL"
        set action accept
    next
end"""
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.conf', delete=False, encoding='utf-8') as tmp_file:
            tmp_file.write(test_config)
            config_file_path = tmp_file.name
        
        try:
            service = ConversionService()
            print("✅ ConversionService初始化成功")
            
            # 测试转换
            result = service.convert(
                cli_file=config_file_path,
                vendor="fortigate",
                model="z5100s",
                version="R10P2",
                verbose=False  # 减少输出
            )
            
            print(f"✅ 转换执行完成")
            print(f"   - 成功: {result.get('success', False)}")
            print(f"   - 工作流版本: {result.get('workflow_version', 'N/A')}")
            
            if not result.get('success'):
                error = result.get('error', 'Unknown')
                print(f"   - 错误: {error}")
                
                # 分析错误类型
                if 'template_not_found' in error:
                    print("   - 错误类型: 模板文件未找到")
                elif 'yang' in error.lower():
                    print("   - 错误类型: YANG验证相关")
                else:
                    print("   - 错误类型: 其他")
            else:
                print(f"   - 输出文件: {result.get('output_file', 'N/A')}")
            
            return result.get('success', False)
            
        finally:
            # 清理临时文件
            if os.path.exists(config_file_path):
                os.unlink(config_file_path)
        
    except Exception as e:
        print(f"❌ 新架构模板测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主诊断函数"""
    print("🔧 模板路径诊断工具")
    print("=" * 60)
    
    tests = [
        ("文件系统路径检查", check_file_system_paths),
        ("ConfigManager测试", test_config_manager),
        ("TemplateManager测试", test_template_manager),
        ("YangManager测试", test_yang_manager),
        ("新架构模板使用测试", test_new_architecture_with_templates)
    ]
    
    passed = 0
    total = len(tests)
    
    for i, (test_name, test_func) in enumerate(tests, 1):
        print(f"\n{i}. {test_name}...")
        try:
            if test_func():
                passed += 1
                print(f"   ✅ {test_name}通过")
            else:
                print(f"   ❌ {test_name}失败")
        except Exception as e:
            print(f"   ❌ {test_name}异常: {e}")
    
    print(f"\n📊 诊断结果: {passed}/{total} 通过")
    
    if passed == total:
        print("\n🎉 所有诊断通过！模板路径配置正常！")
    else:
        print(f"\n⚠️  {total - passed} 个诊断失败，需要修复")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
