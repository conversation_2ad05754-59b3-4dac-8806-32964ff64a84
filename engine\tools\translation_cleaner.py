#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
翻译文件清理工具
清理重复键、未使用的翻译键，统一命名规范，确保翻译文件精简性
"""

import os
import re
import json
from typing import Dict, List, Set, Tuple
from pathlib import Path
from collections import defaultdict

class TranslationCleaner:
    """翻译文件清理器"""
    
    def __init__(self):
        self.used_keys = set()
        self.duplicate_keys = defaultdict(list)
        self.invalid_keys = []
        self.test_keys = []
        self.obsolete_keys = []
        
        # 测试数据模式
        self.test_patterns = [
            r'covid',
            r'test',
            r'demo',
            r'example',
            r'sample',
            r'hello.*world',
            r'foo.*bar',
            r'lorem.*ipsum',
            r'10,000,000,000',
            r'advanced.*ai.*ready',
        ]
        
        # 过时的键模式
        self.obsolete_patterns = [
            r'_CallbackFileWrapper',
            r'_compress\.so',
            r'_distutils_hack',
            r'_temp_for_encryption',
            r'Cython\.Compiler',
            r'certifi',
            r'ctypes\.wintypes',
            r'dropbox\.com',
        ]
        
        # 有效的键名格式
        self.valid_key_pattern = re.compile(r'^[a-z][a-z0-9_]*(\.[a-z][a-z0-9_]*)*$')
    
    def scan_code_for_used_keys(self, directory: str):
        """扫描代码，找出实际使用的翻译键"""
        print("🔍 扫描代码中使用的翻译键...")
        
        # 翻译键使用模式
        patterns = [
            re.compile(r'_\(["\']([^"\']+)["\']\)'),  # _("key")
            re.compile(r'log\(["\']([^"\']+)["\']'),  # log("key")
            re.compile(r'user_log\(["\']([^"\']+)["\']'),  # user_log("key")
            re.compile(r'translate\(["\']([^"\']+)["\']'),  # translate("key")
            re.compile(r'safe_translate\(["\']([^"\']+)["\']'),  # safe_translate("key")
            re.compile(r'["\']i18n:([^"\']+)["\']'),  # "i18n:key"
        ]
        
        for root, dirs, files in os.walk(directory):
            # 跳过某些目录
            dirs[:] = [d for d in dirs if d not in ['.git', '__pycache__', '.pytest_cache']]
            
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                            content = f.read()
                        
                        for pattern in patterns:
                            matches = pattern.findall(content)
                            for match in matches:
                                # 清理键名
                                key = match.strip()
                                if key.startswith('i18n:'):
                                    key = key[5:]
                                self.used_keys.add(key)
                    
                    except Exception as e:
                        print(f"扫描文件失败 {file_path}: {e}")
        
        print(f"找到 {len(self.used_keys)} 个使用中的翻译键")
    
    def analyze_translation_file(self, file_path: str) -> Dict:
        """分析翻译文件"""
        if not os.path.exists(file_path):
            return {}
        
        with open(file_path, 'r', encoding='utf-8') as f:
            translations = json.load(f)
        
        print(f"📊 分析翻译文件: {file_path}")
        print(f"  总键数: {len(translations)}")
        
        # 检查重复值
        value_to_keys = defaultdict(list)
        for key, value in translations.items():
            if isinstance(value, str) and value.strip():
                value_to_keys[value].append(key)
        
        # 找出重复值
        for value, keys in value_to_keys.items():
            if len(keys) > 1:
                self.duplicate_keys[value] = keys
        
        # 检查测试键
        for key in translations.keys():
            if any(re.search(pattern, key, re.IGNORECASE) for pattern in self.test_patterns):
                self.test_keys.append(key)
        
        # 检查过时键
        for key in translations.keys():
            if any(re.search(pattern, key, re.IGNORECASE) for pattern in self.obsolete_patterns):
                self.obsolete_keys.append(key)
        
        # 检查无效键名格式
        for key in translations.keys():
            if not self.valid_key_pattern.match(key):
                self.invalid_keys.append(key)
        
        return translations
    
    def generate_clean_translations(self, original_translations: Dict) -> Dict:
        """生成清理后的翻译"""
        clean_translations = {}
        
        # 要删除的键
        keys_to_remove = set()
        keys_to_remove.update(self.test_keys)
        keys_to_remove.update(self.obsolete_keys)
        
        # 处理重复键（保留第一个，删除其他）
        for value, keys in self.duplicate_keys.items():
            if len(keys) > 1:
                # 保留最短的键名，删除其他
                keys.sort(key=len)
                keys_to_remove.update(keys[1:])
        
        # 删除未使用的键（如果有使用情况数据）
        if self.used_keys:
            unused_keys = set(original_translations.keys()) - self.used_keys
            # 只删除明显未使用的键（排除可能的动态键）
            for key in unused_keys:
                if not any(used_key.startswith(key.split('.')[0]) for used_key in self.used_keys):
                    keys_to_remove.add(key)
        
        # 生成清理后的翻译
        for key, value in original_translations.items():
            if key not in keys_to_remove:
                clean_translations[key] = value
        
        return clean_translations
    
    def normalize_key_names(self, translations: Dict) -> Dict:
        """规范化键名"""
        normalized_translations = {}
        key_mappings = {}
        
        for old_key, value in translations.items():
            # 规范化键名
            new_key = self.normalize_key(old_key)
            
            # 避免冲突
            counter = 1
            base_key = new_key
            while new_key in normalized_translations:
                new_key = f"{base_key}_{counter}"
                counter += 1
            
            normalized_translations[new_key] = value
            if old_key != new_key:
                key_mappings[old_key] = new_key
        
        return normalized_translations, key_mappings
    
    def normalize_key(self, key: str) -> str:
        """规范化单个键名"""
        # 转换为小写
        key = key.lower()
        
        # 替换连字符为下划线
        key = key.replace('-', '_')
        
        # 移除特殊字符
        key = re.sub(r'[^a-z0-9_.]', '_', key)
        
        # 移除多余的下划线
        key = re.sub(r'_+', '_', key)
        
        # 移除开头和结尾的下划线
        key = key.strip('_')
        
        # 确保符合格式
        if not self.valid_key_pattern.match(key):
            # 如果仍然不符合格式，添加前缀
            key = f"misc.{key}"
        
        return key
    
    def save_clean_translations(self, translations: Dict, output_file: str):
        """保存清理后的翻译"""
        # 按键名排序
        sorted_translations = dict(sorted(translations.items()))
        
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(sorted_translations, f, ensure_ascii=False, indent=2)
    
    def generate_cleanup_report(self, original_count: int, clean_count: int) -> Dict:
        """生成清理报告"""
        return {
            'summary': {
                'original_keys': original_count,
                'clean_keys': clean_count,
                'removed_keys': original_count - clean_count,
                'removal_rate': round((original_count - clean_count) / original_count * 100, 2)
            },
            'removed_categories': {
                'test_keys': len(self.test_keys),
                'obsolete_keys': len(self.obsolete_keys),
                'duplicate_keys': sum(len(keys) - 1 for keys in self.duplicate_keys.values()),
                'invalid_keys': len(self.invalid_keys)
            },
            'details': {
                'test_keys': self.test_keys,
                'obsolete_keys': self.obsolete_keys,
                'duplicate_groups': dict(self.duplicate_keys),
                'invalid_keys': self.invalid_keys
            }
        }
    
    def print_cleanup_summary(self, report: Dict):
        """打印清理摘要"""
        summary = report['summary']
        categories = report['removed_categories']
        
        print(f"\n📊 翻译文件清理结果:")
        print(f"  原始键数: {summary['original_keys']}")
        print(f"  清理后键数: {summary['clean_keys']}")
        print(f"  删除键数: {summary['removed_keys']}")
        print(f"  删除率: {summary['removal_rate']}%")
        
        print(f"\n🗑️ 删除分类:")
        print(f"  测试键: {categories['test_keys']}")
        print(f"  过时键: {categories['obsolete_keys']}")
        print(f"  重复键: {categories['duplicate_keys']}")
        print(f"  无效键: {categories['invalid_keys']}")

def main():
    """主函数"""
    cleaner = TranslationCleaner()
    
    # 获取路径
    engine_dir = Path(__file__).parent.parent
    locale_dir = engine_dir / "locales"
    
    # 扫描代码中使用的键
    cleaner.scan_code_for_used_keys(str(engine_dir))
    
    # 处理中文翻译文件
    zh_file = locale_dir / "zh-CN.json"
    if zh_file.exists():
        print(f"\n🧹 清理中文翻译文件...")
        
        # 分析原始文件
        original_translations = cleaner.analyze_translation_file(str(zh_file))
        original_count = len(original_translations)
        
        # 生成清理后的翻译
        clean_translations = cleaner.generate_clean_translations(original_translations)
        
        # 规范化键名
        normalized_translations, key_mappings = cleaner.normalize_key_names(clean_translations)
        
        # 保存清理后的文件
        clean_file = locale_dir / "zh-CN.clean.json"
        cleaner.save_clean_translations(normalized_translations, str(clean_file))
        
        # 生成报告
        report = cleaner.generate_cleanup_report(original_count, len(normalized_translations))
        
        # 保存报告
        report_file = engine_dir / "reports" / "translation_cleanup_report.json"
        os.makedirs(os.path.dirname(report_file), exist_ok=True)
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        # 保存键名映射
        if key_mappings:
            mapping_file = locale_dir / "key_name_mappings.json"
            with open(mapping_file, 'w', encoding='utf-8') as f:
                json.dump(key_mappings, f, ensure_ascii=False, indent=2)
            print(f"📁 键名映射已保存到: {mapping_file}")
        
        # 打印摘要
        cleaner.print_cleanup_summary(report)
        
        print(f"\n📁 清理后的文件已保存到: {clean_file}")
        print(f"📁 清理报告已保存到: {report_file}")
        
        # 显示一些删除的键示例
        if cleaner.test_keys:
            print(f"\n🧪 删除的测试键示例 (前5个):")
            for key in cleaner.test_keys[:5]:
                print(f"  - {key}")
        
        if cleaner.obsolete_keys:
            print(f"\n🗑️ 删除的过时键示例 (前5个):")
            for key in cleaner.obsolete_keys[:5]:
                print(f"  - {key}")
    
    else:
        print(f"❌ 翻译文件不存在: {zh_file}")

if __name__ == "__main__":
    main()
