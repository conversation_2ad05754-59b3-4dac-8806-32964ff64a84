#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
翻译文件最终化工具
创建生产就绪的翻译文件，移除无效键，确保质量
"""

import os
import re
import json
from typing import Dict, Set, List
from pathlib import Path

class TranslationFinalizer:
    """翻译文件最终化器"""
    
    def __init__(self):
        self.used_keys = set()
        self.final_translations = {}
        self.removed_keys = []
        self.statistics = {
            'original_keys': 0,
            'final_keys': 0,
            'removed_keys': 0,
            'invalid_keys': 0,
            'improved_keys': 0
        }
    
    def load_used_keys_from_code(self, directory: str):
        """从代码中加载实际使用的翻译键"""
        print("🔍 扫描代码中实际使用的翻译键...")
        
        patterns = [
            re.compile(r'_\(["\']([^"\']+)["\']\)'),
            re.compile(r'log\(["\']([^"\']+)["\']'),
            re.compile(r'safe_translate\(["\']([^"\']+)["\']'),
            re.compile(r'translate\(["\']([^"\']+)["\']'),
        ]
        
        for root, dirs, files in os.walk(directory):
            dirs[:] = [d for d in dirs if d not in ['.git', '__pycache__', '.pytest_cache']]
            
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                            content = f.read()
                        
                        for pattern in patterns:
                            matches = pattern.findall(content)
                            for match in matches:
                                self.used_keys.add(match.strip())
                    except Exception:
                        continue
        
        print(f"找到 {len(self.used_keys)} 个实际使用的翻译键")
    
    def is_valid_key(self, key: str) -> bool:
        """检查键名是否有效"""
        # 基本格式检查
        if not key or not isinstance(key, str):
            return False
        
        # 不能包含中文字符
        if re.search(r'[\u4e00-\u9fff]', key):
            return False
        
        # 不能是测试数据
        test_patterns = [
            r'covid', r'test', r'demo', r'example', r'sample',
            r'hello.*world', r'foo.*bar', r'lorem.*ipsum',
            r'10,000,000,000', r'advanced.*ai.*ready'
        ]
        
        for pattern in test_patterns:
            if re.search(pattern, key, re.IGNORECASE):
                return False
        
        # 不能是文件路径或系统相关
        if any(ext in key.lower() for ext in ['.exe', '.so', '.dll', '.json', '.xml', '.py']):
            return False
        
        # 不能是纯数字或特殊字符开头
        if re.match(r'^[0-9]', key) or re.match(r'^[^a-zA-Z]', key):
            return False
        
        # 标准格式检查：module.function.content
        parts = key.split('.')
        if len(parts) < 2:
            return False
        
        # 每个部分都应该是有效的标识符
        for part in parts:
            if not re.match(r'^[a-z][a-z0-9_]*$', part):
                return False
        
        return True
    
    def improve_translation_value(self, key: str, value: str, language: str) -> str:
        """改进翻译值"""
        if not value or not isinstance(value, str):
            return ""
        
        original_value = value
        
        if language == 'zh-CN':
            # 中文翻译改进
            # 移除翻译标记
            if value.startswith('[需要翻译] '):
                value = value[7:]
            
            # 清理格式
            value = re.sub(r'\s*，\s*', '，', value)
            value = re.sub(r'\s*：\s*', '：', value)
            value = re.sub(r'\s+', ' ', value).strip()
            
        elif language == 'en-US':
            # 英文翻译改进
            # 移除翻译标记
            if value.startswith('[需要翻译] '):
                value = value[7:]
            
            # 修复常见翻译错误
            replacements = {
                '库Loading': 'library loading',
                '库文件': 'library file',
                'Deleting': 'deleting',
                'Loading': 'loading',
                '文件清理': 'file cleanup',
                '容器环境': 'container environment',
                '标准库': 'standard library',
                '传统的': 'traditional',
                '针对': 'for',
                '方法': 'method',
                '处理': 'processing',
                '设置': 'setup',
                '配置': 'configuration',
                '错误': 'error',
                '成功': 'success',
                '失败': 'failed',
                '完成': 'completed',
                '开始': 'start'
            }
            
            for zh, en in replacements.items():
                value = value.replace(zh, en)
            
            # 清理格式
            value = re.sub(r'\s+', ' ', value).strip()
            
            # 首字母大写（如果是句子）
            if value and not value[0].isupper() and not value.startswith('{'):
                value = value[0].upper() + value[1:]
        
        return value if value != original_value else original_value
    
    def finalize_translations(self, input_file: str, language: str) -> Dict[str, str]:
        """最终化翻译文件"""
        print(f"\n🔧 最终化 {language} 翻译文件: {input_file}")
        
        # 加载翻译文件
        if not os.path.exists(input_file):
            print(f"❌ 文件不存在: {input_file}")
            return {}
        
        with open(input_file, 'r', encoding='utf-8') as f:
            translations = json.load(f)
        
        self.statistics['original_keys'] = len(translations)
        print(f"原始键数: {self.statistics['original_keys']}")
        
        final_translations = {}
        
        for key, value in translations.items():
            # 检查键名有效性
            if not self.is_valid_key(key):
                self.removed_keys.append({
                    'key': key,
                    'reason': 'invalid_key_format',
                    'language': language
                })
                self.statistics['invalid_keys'] += 1
                continue
            
            # 改进翻译值
            improved_value = self.improve_translation_value(key, value, language)
            if improved_value != value:
                self.statistics['improved_keys'] += 1
            
            # 只保留有效的翻译
            if improved_value and improved_value.strip():
                final_translations[key] = improved_value
            else:
                self.removed_keys.append({
                    'key': key,
                    'reason': 'empty_value',
                    'language': language
                })
        
        self.statistics['final_keys'] = len(final_translations)
        self.statistics['removed_keys'] = len(self.removed_keys)
        
        print(f"最终键数: {self.statistics['final_keys']}")
        print(f"移除键数: {self.statistics['removed_keys']}")
        print(f"改进键数: {self.statistics['improved_keys']}")
        
        return final_translations
    
    def save_final_translations(self, translations: Dict[str, str], output_file: str):
        """保存最终翻译文件"""
        # 按键名排序
        sorted_translations = dict(sorted(translations.items()))
        
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(sorted_translations, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 最终翻译文件已保存: {output_file}")
    
    def generate_finalization_report(self) -> Dict:
        """生成最终化报告"""
        return {
            'statistics': self.statistics,
            'removed_keys': self.removed_keys,
            'summary': {
                'quality_improvement': self.statistics['improved_keys'],
                'cleanup_rate': round(self.statistics['removed_keys'] / self.statistics['original_keys'] * 100, 2) if self.statistics['original_keys'] > 0 else 0,
                'final_quality_score': round((self.statistics['final_keys'] - self.statistics['invalid_keys']) / self.statistics['final_keys'] * 100, 2) if self.statistics['final_keys'] > 0 else 0
            }
        }

def main():
    """主函数"""
    finalizer = TranslationFinalizer()
    
    # 获取路径
    engine_dir = Path(__file__).parent.parent
    locale_dir = engine_dir / "locales"
    
    # 扫描实际使用的键
    finalizer.load_used_keys_from_code(str(engine_dir))
    
    # 最终化中文翻译
    zh_merged_file = locale_dir / "zh-CN.merged.json"
    zh_final_file = locale_dir / "zh-CN.final.json"
    
    if zh_merged_file.exists():
        zh_final = finalizer.finalize_translations(str(zh_merged_file), 'zh-CN')
        finalizer.save_final_translations(zh_final, str(zh_final_file))
    
    # 最终化英文翻译
    en_merged_file = locale_dir / "en-US.merged.json"
    en_final_file = locale_dir / "en-US.final.json"
    
    if en_merged_file.exists():
        en_final = finalizer.finalize_translations(str(en_merged_file), 'en-US')
        finalizer.save_final_translations(en_final, str(en_final_file))
    
    # 生成报告
    report = finalizer.generate_finalization_report()
    report_file = engine_dir / "reports" / "translation_finalization_report.json"
    os.makedirs(os.path.dirname(report_file), exist_ok=True)
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n📊 最终化完成!")
    print(f"📁 最终化报告已保存到: {report_file}")
    
    # 显示统计
    stats = report['statistics']
    summary = report['summary']
    
    print(f"\n📈 最终统计:")
    print(f"  原始键数: {stats['original_keys']}")
    print(f"  最终键数: {stats['final_keys']}")
    print(f"  移除键数: {stats['removed_keys']}")
    print(f"  无效键数: {stats['invalid_keys']}")
    print(f"  改进键数: {stats['improved_keys']}")
    print(f"  清理率: {summary['cleanup_rate']}%")
    print(f"  质量评分: {summary['final_quality_score']}%")
    
    # 显示一些移除的键示例
    if finalizer.removed_keys:
        print(f"\n🗑️ 移除的键示例 (前5个):")
        for i, item in enumerate(finalizer.removed_keys[:5], 1):
            print(f"  {i}. {item['key']} (原因: {item['reason']})")

if __name__ == "__main__":
    main()
