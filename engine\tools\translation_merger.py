#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
翻译文件合并工具
将清理后的翻译文件和新生成的翻译文件合并，创建完整的翻译文件
"""

import os
import json
from typing import Dict, Set, Tuple, List
from pathlib import Path

class TranslationMerger:
    """翻译文件合并器"""
    
    def __init__(self):
        self.merged_translations = {}
        self.conflicts = []
        self.statistics = {
            'clean_keys': 0,
            'new_keys': 0,
            'conflicts': 0,
            'total_keys': 0
        }
    
    def load_translation_file(self, file_path: str) -> Dict[str, str]:
        """加载翻译文件"""
        if not os.path.exists(file_path):
            print(f"⚠️ 文件不存在: {file_path}")
            return {}
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                translations = json.load(f)
            print(f"✅ 加载翻译文件: {file_path} ({len(translations)} 个键)")
            return translations
        except Exception as e:
            print(f"❌ 加载翻译文件失败 {file_path}: {str(e)}")
            return {}
    
    def merge_translations(self, clean_file: str, new_file: str, language: str) -> Dict[str, str]:
        """合并翻译文件"""
        print(f"\n🔄 合并 {language} 翻译文件...")
        
        # 加载文件
        clean_translations = self.load_translation_file(clean_file)
        new_translations = self.load_translation_file(new_file)
        
        self.statistics['clean_keys'] = len(clean_translations)
        self.statistics['new_keys'] = len(new_translations)
        
        # 开始合并
        merged = {}
        
        # 1. 添加清理后的翻译（优先级高）
        for key, value in clean_translations.items():
            merged[key] = value
        
        # 2. 添加新生成的翻译（避免冲突）
        for key, value in new_translations.items():
            if key in merged:
                # 检查是否有冲突
                if merged[key] != value:
                    self.conflicts.append({
                        'key': key,
                        'clean_value': merged[key],
                        'new_value': value,
                        'language': language
                    })
                    self.statistics['conflicts'] += 1
                    # 保留清理后的值
                else:
                    # 值相同，无冲突
                    pass
            else:
                # 新键，直接添加
                merged[key] = value
        
        self.statistics['total_keys'] = len(merged)
        
        print(f"📊 合并统计:")
        print(f"  清理后的键: {self.statistics['clean_keys']}")
        print(f"  新生成的键: {self.statistics['new_keys']}")
        print(f"  冲突数量: {self.statistics['conflicts']}")
        print(f"  合并后总键数: {self.statistics['total_keys']}")
        
        return merged
    
    def improve_translation_quality(self, translations: Dict[str, str], language: str) -> Dict[str, str]:
        """改进翻译质量"""
        print(f"\n🔧 改进 {language} 翻译质量...")
        
        improved = {}
        improvements = 0
        
        for key, value in translations.items():
            improved_value = value
            
            if language == 'en-US':
                # 改进英文翻译
                improved_value = self.improve_english_translation(value)
            elif language == 'zh-CN':
                # 改进中文翻译
                improved_value = self.improve_chinese_translation(value)
            
            if improved_value != value:
                improvements += 1
            
            improved[key] = improved_value
        
        print(f"✅ 改进了 {improvements} 个翻译")
        return improved
    
    def improve_english_translation(self, text: str) -> str:
        """改进英文翻译"""
        # 移除不必要的标记
        if text.startswith('[需要翻译] '):
            text = text[7:]  # 移除 "[需要翻译] "
        
        # 修复常见的翻译问题
        replacements = {
            '库Loading': 'library loading',
            '库文件': 'library file',
            '目录Loading': 'directory loading',
            'Deleting': 'deleting',
            'Loading': 'loading',
            '文件清理': 'file cleanup',
            '特别是': 'especially',
            '任何可能': 'any possible',
            '干扰的': 'interfering',
            '从当前目录': 'from current directory',
            '或专用': 'or dedicated',
            '加密库': 'encryption library',
            '容器环境': 'container environment',
            '下的': 'under',
            '方法': 'method',
            '标准库': 'standard library',
            '传统的': 'traditional',
            '针对': 'for',
            '环境的': 'environment',
            '在容器': 'in container',
            '中准备': 'prepare',
            '的特殊': 'special',
            '处理': 'processing',
            '析构函数': 'destructor',
            '清理临时': 'cleanup temporary',
            '全局': 'global',
            '设置': 'setup',
            '搜索路径': 'search path',
            '使用': 'using',
            '最小化': 'minimized',
            '策略': 'strategy',
            '避免': 'avoid',
            '系统库': 'system library',
            '冲突': 'conflict'
        }
        
        for zh, en in replacements.items():
            text = text.replace(zh, en)
        
        # 清理多余的空格
        import re
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    def improve_chinese_translation(self, text: str) -> str:
        """改进中文翻译"""
        # 移除不必要的标记
        if text.startswith('[需要翻译] '):
            text = text[7:]  # 移除 "[需要翻译] "
        
        # 修复常见问题
        import re
        
        # 清理多余的空格和标点
        text = re.sub(r'\s*，\s*', '，', text)
        text = re.sub(r'\s*：\s*', '：', text)
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    def validate_merged_translations(self, translations: Dict[str, str], language: str) -> Tuple[bool, List[str]]:
        """验证合并后的翻译"""
        print(f"\n🔍 验证 {language} 翻译...")
        
        issues = []
        
        # 检查键名格式
        import re
        key_pattern = re.compile(r'^[a-z][a-z0-9_]*(\.[a-z][a-z0-9_]*)*$')
        
        for key, value in translations.items():
            # 检查键名格式
            if not key_pattern.match(key):
                issues.append(f"无效键名格式: {key}")
            
            # 检查值是否为空
            if not value or not value.strip():
                issues.append(f"空翻译值: {key}")
            
            # 检查参数占位符
            key_params = set(re.findall(r'\{([^}]+)\}', value))
            if language == 'zh-CN':
                # 中文翻译中不应该有英文错误标记
                if '[需要翻译]' in value and len(value) > 20:
                    issues.append(f"中文翻译包含翻译标记: {key}")
        
        if issues:
            print(f"⚠️ 发现 {len(issues)} 个问题")
            for issue in issues[:5]:  # 只显示前5个问题
                print(f"  - {issue}")
            if len(issues) > 5:
                print(f"  ... 还有 {len(issues) - 5} 个问题")
        else:
            print("✅ 验证通过")
        
        return len(issues) == 0, issues
    
    def save_merged_translations(self, translations: Dict[str, str], output_file: str):
        """保存合并后的翻译"""
        # 按键名排序
        sorted_translations = dict(sorted(translations.items()))
        
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(sorted_translations, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 合并后的翻译已保存到: {output_file}")
    
    def generate_merge_report(self) -> Dict:
        """生成合并报告"""
        return {
            'statistics': self.statistics,
            'conflicts': self.conflicts,
            'summary': {
                'total_conflicts': len(self.conflicts),
                'merge_success': len(self.conflicts) == 0,
                'coverage_improvement': self.statistics['new_keys']
            }
        }

def main():
    """主函数"""
    merger = TranslationMerger()
    
    # 获取路径
    engine_dir = Path(__file__).parent.parent
    locale_dir = engine_dir / "locales"
    
    # 合并中文翻译
    print("🔄 开始合并翻译文件...")
    
    zh_clean_file = locale_dir / "zh-CN.clean.json"
    zh_new_file = locale_dir / "zh-CN.new.json"
    zh_merged_file = locale_dir / "zh-CN.merged.json"
    
    if zh_clean_file.exists() and zh_new_file.exists():
        zh_merged = merger.merge_translations(str(zh_clean_file), str(zh_new_file), 'zh-CN')
        zh_improved = merger.improve_translation_quality(zh_merged, 'zh-CN')
        
        # 验证
        zh_valid, zh_issues = merger.validate_merged_translations(zh_improved, 'zh-CN')
        
        # 保存
        merger.save_merged_translations(zh_improved, str(zh_merged_file))
    
    # 合并英文翻译
    en_clean_file = locale_dir / "en-US.json"  # 使用原始英文文件
    en_new_file = locale_dir / "en-US.new.json"
    en_merged_file = locale_dir / "en-US.merged.json"
    
    if en_clean_file.exists() and en_new_file.exists():
        en_merged = merger.merge_translations(str(en_clean_file), str(en_new_file), 'en-US')
        en_improved = merger.improve_translation_quality(en_merged, 'en-US')
        
        # 验证
        en_valid, en_issues = merger.validate_merged_translations(en_improved, 'en-US')
        
        # 保存
        merger.save_merged_translations(en_improved, str(en_merged_file))
    
    # 生成报告
    report = merger.generate_merge_report()
    report_file = engine_dir / "reports" / "translation_merge_report.json"
    os.makedirs(os.path.dirname(report_file), exist_ok=True)
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n📊 合并完成!")
    print(f"📁 合并报告已保存到: {report_file}")
    
    # 显示最终统计
    stats = report['statistics']
    print(f"\n📈 最终统计:")
    print(f"  原有翻译键: {stats['clean_keys']}")
    print(f"  新增翻译键: {stats['new_keys']}")
    print(f"  冲突数量: {stats['conflicts']}")
    print(f"  最终总键数: {stats['total_keys']}")
    print(f"  覆盖率提升: +{stats['new_keys']} 键")

if __name__ == "__main__":
    main()
