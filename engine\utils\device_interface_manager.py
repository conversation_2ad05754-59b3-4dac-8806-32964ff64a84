#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
设备接口管理器
负责管理设备接口信息和透明模式下的接口分类
"""

import json
import os
from engine.utils.logger import log, _
from engine.utils.i18n import _


class DeviceInterfaceManager:
    """设备接口管理器类"""
    
    def __init__(self, model):
        """
        初始化设备接口管理器
        
        Args:
            model (str): 设备型号，如 'z5100s'
        """
        self.model = model
        self.device_interfaces_data = self._load_device_interfaces()
        log(_("device_interface_manager.initialized", model=model))
    
    def _load_device_interfaces(self):
        """
        加载设备接口配置文件
        
        Returns:
            dict: 设备接口配置数据
        """
        try:
            # 获取device_interfaces.json文件路径
            current_dir = os.path.dirname(os.path.abspath(__file__))
            engine_dir = os.path.dirname(current_dir)
            device_interfaces_file = os.path.join(engine_dir, "data", "device_interfaces.json")
            
            if not os.path.exists(device_interfaces_file):
                log(_("device_interface_manager.file_not_found", file=device_interfaces_file), "warning")
                return {}
            
            with open(device_interfaces_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            log(_("device_interface_manager.file_loaded", file=device_interfaces_file))
            return data
            
        except Exception as e:
            log(_("device_interface_manager.load_error", error=str(e)), "error")
            return {}
    
    def get_all_physical_interfaces(self):
        """
        获取设备的所有物理接口列表
        
        Returns:
            list: 物理接口名称列表
        """
        interfaces = self.device_interfaces_data.get(self.model, {}).get("interfaces", [])
        log(_("device_interface_manager.get_interfaces", model=self.model, count=len(interfaces)))
        return interfaces
    
    def is_valid_interface(self, interface_name):
        """
        检查接口名称是否为设备的有效物理接口
        
        Args:
            interface_name (str): 接口名称
            
        Returns:
            bool: 是否为有效接口
        """
        all_interfaces = self.get_all_physical_interfaces()
        return interface_name in all_interfaces
    
    def classify_interfaces_for_transparent_mode(self, mgmt_interface):
        """
        为透明模式分类接口
        
        Args:
            mgmt_interface (str): 管理接口名称（从映射文件获取）
            
        Returns:
            dict: 接口分类结果
                {
                    "mgmt_interfaces": [管理接口列表],
                    "bridge_interfaces": [桥接接口列表],
                    "invalid_mgmt": bool  # MGMT接口是否无效
                }
        """
        all_interfaces = self.get_all_physical_interfaces()
        
        # 验证MGMT接口是否有效
        invalid_mgmt = mgmt_interface not in all_interfaces if mgmt_interface else True
        
        if invalid_mgmt:
            log(_("device_interface_manager.invalid_mgmt_interface", 
                  interface=mgmt_interface, model=self.model), "warning")
            # 如果MGMT接口无效，所有接口都设为桥接模式
            return {
                "mgmt_interfaces": [],
                "bridge_interfaces": all_interfaces,
                "invalid_mgmt": True
            }
        
        # 正常分类：MGMT接口保持路由模式，其他接口设为桥接模式
        bridge_interfaces = [iface for iface in all_interfaces if iface != mgmt_interface]
        
        log(_("device_interface_manager.interface_classification", 
              mgmt=mgmt_interface, bridge_count=len(bridge_interfaces)))
        
        return {
            "mgmt_interfaces": [mgmt_interface],
            "bridge_interfaces": bridge_interfaces,
            "invalid_mgmt": False
        }
    
    def get_device_description(self):
        """
        获取设备描述信息
        
        Returns:
            str: 设备描述
        """
        return self.device_interfaces_data.get(self.model, {}).get("description", "")
    
    def get_supported_models(self):
        """
        获取支持的设备型号列表
        
        Returns:
            list: 支持的设备型号
        """
        return list(self.device_interfaces_data.keys())


def create_device_interface_manager(model):
    """
    工厂函数：创建设备接口管理器实例
    
    Args:
        model (str): 设备型号
        
    Returns:
        DeviceInterfaceManager: 设备接口管理器实例
    """
    return DeviceInterfaceManager(model)


# 测试函数
def test_device_interface_manager():
    """测试设备接口管理器功能"""
    print("=== 设备接口管理器测试 ===")
    
    # 测试z5100s设备
    manager = DeviceInterfaceManager("z5100s")
    
    print(f"支持的设备型号: {manager.get_supported_models()}")
    print(f"z5100s所有接口: {manager.get_all_physical_interfaces()}")
    
    # 测试透明模式接口分类
    classification = manager.classify_interfaces_for_transparent_mode("Ge0/0")
    print(f"透明模式分类结果: {classification}")
    
    # 测试无效MGMT接口
    invalid_classification = manager.classify_interfaces_for_transparent_mode("invalid_interface")
    print(f"无效MGMT接口分类: {invalid_classification}")


if __name__ == "__main__":
    test_device_interface_manager()
