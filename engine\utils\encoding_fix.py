#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
编码修复工具
用于确保Python脚本在各种环境下都能正确处理UTF-8编码
"""

import os
import sys
import locale

# 调试模式开关
DEBUG_MODE = False

def setup_encoding():
    """设置正确的编码环境
    
    确保Python脚本在各种环境下都能正确处理UTF-8编码，
    包括Windows、Linux、容器环境等。
    """
    # 设置标准输入输出编码
    if hasattr(sys.stdout, 'reconfigure'):
        try:
            sys.stdout.reconfigure(encoding='utf-8')
            sys.stderr.reconfigure(encoding='utf-8')
        except Exception:
            pass
    
    # 设置环境变量
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['LANG'] = 'C.UTF-8'
    os.environ['LC_ALL'] = 'C.UTF-8'
    
    # 尝试设置系统区域设置
    try:
        locale.setlocale(locale.LC_ALL, 'C.UTF-8')
    except locale.Error:
        try:
            locale.setlocale(locale.LC_ALL, 'en_US.UTF-8')
        except locale.Error:
            try:
                locale.setlocale(locale.LC_ALL, 'utf-8')
            except locale.Error:
                # 如果都失败了，使用默认设置
                pass
    
    # 确保sys.stdout/stderr有正确的编码属性
    if not hasattr(sys.stdout, 'encoding') or sys.stdout.encoding != 'utf-8':
        try:
            import codecs
            sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer)
            sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer)
        except (AttributeError, Exception):
            # 在某些环境下可能没有buffer属性
            pass


def ensure_utf8_string(text):
    """确保字符串是UTF-8编码
    
    Args:
        text: 输入文本，可能是str或bytes
        
    Returns:
        str: UTF-8编码的字符串
    """
    if isinstance(text, bytes):
        try:
            return text.decode('utf-8')
        except UnicodeDecodeError:
            # 如果UTF-8解码失败，尝试其他编码
            encodings = ['gbk', 'gb2312', 'latin1', 'ascii']
            for encoding in encodings:
                try:
                    return text.decode(encoding)
                except UnicodeDecodeError:
                    continue
            # 如果所有编码都失败，使用错误处理
            return text.decode('utf-8', errors='replace')
    elif isinstance(text, str):
        return text
    else:
        return str(text)


def safe_print(text, file=None):
    """安全地打印文本，处理各种编码问题
    
    Args:
        text: 要打印的文本
        file: 输出目标，默认为标准输出
    """
    if not DEBUG_MODE:
        return  # 非调试模式下不输出
        
    try:
        # 尝试直接打印
        print(text, file=file)
    except UnicodeEncodeError:
        try:
            # 尝试使用UTF-8编码
            encoded_text = text.encode('utf-8', errors='replace').decode('utf-8', errors='replace')
            print(encoded_text, file=file)
        except:
            # 所有方法都失败，打印字符串的原始表示
            print(repr(text), file=file)


def get_system_encoding():
    """获取系统编码信息
    
    Returns:
        dict: 包含各种编码信息的字典
    """
    encoding_info = {
        'stdout_encoding': getattr(sys.stdout, 'encoding', 'unknown'),
        'stderr_encoding': getattr(sys.stderr, 'encoding', 'unknown'),
        'stdin_encoding': getattr(sys.stdin, 'encoding', 'unknown'),
        'filesystem_encoding': sys.getfilesystemencoding(),
        'default_encoding': sys.getdefaultencoding(),
        'locale_encoding': locale.getpreferredencoding(),
        'pythonioencoding': os.environ.get('PYTHONIOENCODING', 'not set'),
        'lang': os.environ.get('LANG', 'not set'),
        'lc_all': os.environ.get('LC_ALL', 'not set'),
    }
    
    return encoding_info


def debug_encoding():
    """调试编码设置，打印当前编码信息"""
    encoding_info = get_system_encoding()
    
    print("=== 编码调试信息 ===")
    for key, value in encoding_info.items():
        print(f"{key}: {value}")
    print("=" * 20)


if __name__ == "__main__":
    # 如果直接运行此文件，则显示编码调试信息
    setup_encoding()
    debug_encoding()
    
    # 测试UTF-8字符串
    test_strings = [
        "Hello World",
        "你好世界",
        "Configuration转换工具",
        "FortiGate配置解析",
    ]
    
    print("\n=== UTF-8字符串测试 ===")
    for test_str in test_strings:
        safe_print(f"测试字符串: {test_str}")
    print("=" * 30)
