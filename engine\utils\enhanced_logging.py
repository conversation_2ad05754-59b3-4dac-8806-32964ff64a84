"""
增强日志记录工具

为地址池转换功能提供详细的日志记录和错误处理，包括：
- 结构化日志记录
- 性能监控
- 错误追踪和分析
- 调试信息收集
"""

import time
import traceback
import functools
from typing import Dict, Any, Optional, List, Callable
from contextlib import contextmanager
from dataclasses import dataclass, field
from engine.utils.logger import log
from engine.utils.i18n import _


@dataclass
class PerformanceMetrics:
    """性能指标"""
    operation_name: str
    start_time: float = 0.0
    end_time: float = 0.0
    duration: float = 0.0
    memory_usage: Optional[int] = None
    success: bool = True
    error_message: Optional[str] = None
    additional_data: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ErrorContext:
    """错误上下文信息"""
    operation: str
    component: str
    input_data: Dict[str, Any] = field(default_factory=dict)
    stack_trace: Optional[str] = None
    timestamp: float = field(default_factory=time.time)
    severity: str = "error"  # "debug", "info", "warning", "error", "critical"


class EnhancedLogger:
    """增强日志记录器"""
    
    def __init__(self, component_name: str):
        self.component_name = component_name
        self.performance_metrics: List[PerformanceMetrics] = []
        self.error_history: List[ErrorContext] = []
        self.debug_enabled = True
        
    def log_operation_start(self, operation: str, **kwargs):
        """记录操作开始"""
        if self.debug_enabled:
            log(_("enhanced_logging.operation_start", 
                 component=self.component_name, 
                 operation=operation, 
                 params=str(kwargs)), "debug")
    
    def log_operation_end(self, operation: str, success: bool = True, **kwargs):
        """记录操作结束"""
        level = "info" if success else "warning"
        status = "成功" if success else "失败"
        log(_("enhanced_logging.operation_end", 
             component=self.component_name, 
             operation=operation, 
             status=status,
             details=str(kwargs)), level)
    
    def log_validation_result(self, item_name: str, validation_type: str, 
                            is_valid: bool, errors: List[str] = None):
        """记录验证结果"""
        errors = errors or []
        if is_valid:
            log(_("enhanced_logging.validation_passed", 
                 component=self.component_name,
                 item=item_name, 
                 type=validation_type), "info")
        else:
            log(_("enhanced_logging.validation_failed", 
                 component=self.component_name,
                 item=item_name, 
                 type=validation_type,
                 error_count=len(errors)), "warning")
            
            # 记录具体错误
            for i, error in enumerate(errors[:5], 1):  # 只记录前5个错误
                log(_("enhanced_logging.validation_error_detail", 
                     component=self.component_name,
                     item=item_name,
                     error_num=i,
                     error=error), "warning")
            
            if len(errors) > 5:
                log(_("enhanced_logging.validation_more_errors", 
                     component=self.component_name,
                     item=item_name,
                     remaining=len(errors) - 5), "warning")
    
    def log_processing_statistics(self, stats: Dict[str, Any]):
        """记录处理统计信息"""
        log(_("enhanced_logging.processing_stats", 
             component=self.component_name,
             stats=str(stats)), "info")
    
    def log_error_with_context(self, operation: str, error: Exception, 
                             input_data: Dict[str, Any] = None, 
                             severity: str = "error"):
        """记录带上下文的错误"""
        input_data = input_data or {}
        
        error_context = ErrorContext(
            operation=operation,
            component=self.component_name,
            input_data=input_data,
            stack_trace=traceback.format_exc(),
            severity=severity
        )
        
        self.error_history.append(error_context)
        
        # 记录错误日志
        log(_("enhanced_logging.error_with_context", 
             component=self.component_name,
             operation=operation,
             error_type=type(error).__name__,
             error_message=str(error)), severity)
        
        # 如果是调试模式，记录详细信息
        if self.debug_enabled:
            log(_("enhanced_logging.error_context_details", 
                 component=self.component_name,
                 operation=operation,
                 input_data=str(input_data),
                 stack_trace=error_context.stack_trace), "debug")
    
    def log_performance_warning(self, operation: str, duration: float, threshold: float = 5.0):
        """记录性能警告"""
        if duration > threshold:
            log(_("enhanced_logging.performance_warning", 
                 component=self.component_name,
                 operation=operation,
                 duration=f"{duration:.2f}",
                 threshold=f"{threshold:.2f}"), "warning")
    
    @contextmanager
    def performance_monitor(self, operation_name: str, **additional_data):
        """性能监控上下文管理器"""
        metrics = PerformanceMetrics(
            operation_name=operation_name,
            additional_data=additional_data
        )
        
        metrics.start_time = time.time()
        self.log_operation_start(operation_name, **additional_data)
        
        try:
            yield metrics
            metrics.success = True
        except Exception as e:
            metrics.success = False
            metrics.error_message = str(e)
            self.log_error_with_context(operation_name, e, additional_data)
            raise
        finally:
            metrics.end_time = time.time()
            metrics.duration = metrics.end_time - metrics.start_time
            
            self.performance_metrics.append(metrics)
            self.log_operation_end(operation_name, metrics.success, 
                                 duration=f"{metrics.duration:.3f}s")
            
            # 检查性能警告
            self.log_performance_warning(operation_name, metrics.duration)
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        if not self.performance_metrics:
            return {"total_operations": 0}
        
        total_operations = len(self.performance_metrics)
        successful_operations = sum(1 for m in self.performance_metrics if m.success)
        total_duration = sum(m.duration for m in self.performance_metrics)
        avg_duration = total_duration / total_operations if total_operations > 0 else 0
        
        # 找出最慢的操作
        slowest_operation = max(self.performance_metrics, key=lambda m: m.duration)
        
        return {
            "total_operations": total_operations,
            "successful_operations": successful_operations,
            "failed_operations": total_operations - successful_operations,
            "total_duration": f"{total_duration:.3f}s",
            "average_duration": f"{avg_duration:.3f}s",
            "slowest_operation": {
                "name": slowest_operation.operation_name,
                "duration": f"{slowest_operation.duration:.3f}s"
            }
        }
    
    def get_error_summary(self) -> Dict[str, Any]:
        """获取错误摘要"""
        if not self.error_history:
            return {"total_errors": 0}
        
        error_by_severity = {}
        error_by_operation = {}
        
        for error in self.error_history:
            # 按严重程度分组
            if error.severity not in error_by_severity:
                error_by_severity[error.severity] = 0
            error_by_severity[error.severity] += 1
            
            # 按操作分组
            if error.operation not in error_by_operation:
                error_by_operation[error.operation] = 0
            error_by_operation[error.operation] += 1
        
        return {
            "total_errors": len(self.error_history),
            "by_severity": error_by_severity,
            "by_operation": error_by_operation,
            "recent_errors": [
                {
                    "operation": error.operation,
                    "severity": error.severity,
                    "timestamp": error.timestamp
                }
                for error in self.error_history[-5:]  # 最近5个错误
            ]
        }


def performance_monitor(operation_name: str):
    """性能监控装饰器"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 尝试从第一个参数获取logger（通常是self）
            logger = None
            if args and hasattr(args[0], '_enhanced_logger'):
                logger = args[0]._enhanced_logger
            elif args and hasattr(args[0], 'enhanced_logger'):
                logger = args[0].enhanced_logger
            
            if logger and isinstance(logger, EnhancedLogger):
                with logger.performance_monitor(operation_name):
                    return func(*args, **kwargs)
            else:
                # 如果没有找到logger，直接执行函数
                return func(*args, **kwargs)
        
        return wrapper
    return decorator


def error_handler(operation_name: str, reraise: bool = True):
    """错误处理装饰器"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # 尝试从第一个参数获取logger
                logger = None
                if args and hasattr(args[0], '_enhanced_logger'):
                    logger = args[0]._enhanced_logger
                elif args and hasattr(args[0], 'enhanced_logger'):
                    logger = args[0].enhanced_logger
                
                if logger and isinstance(logger, EnhancedLogger):
                    logger.log_error_with_context(operation_name, e, {"args": str(args), "kwargs": str(kwargs)})
                
                if reraise:
                    raise
                else:
                    return None
        
        return wrapper
    return decorator


# 全局logger实例缓存
_logger_cache: Dict[str, EnhancedLogger] = {}


def get_enhanced_logger(component_name: str) -> EnhancedLogger:
    """获取增强日志记录器实例"""
    if component_name not in _logger_cache:
        _logger_cache[component_name] = EnhancedLogger(component_name)
    return _logger_cache[component_name]


def log_system_performance_summary():
    """记录系统性能摘要"""
    log("=== 系统性能摘要 ===", "info")
    
    for component_name, logger in _logger_cache.items():
        perf_summary = logger.get_performance_summary()
        error_summary = logger.get_error_summary()
        
        log(f"组件: {component_name}", "info")
        log(f"  操作总数: {perf_summary.get('total_operations', 0)}", "info")
        log(f"  成功操作: {perf_summary.get('successful_operations', 0)}", "info")
        log(f"  失败操作: {perf_summary.get('failed_operations', 0)}", "info")
        log(f"  总耗时: {perf_summary.get('total_duration', '0s')}", "info")
        log(f"  平均耗时: {perf_summary.get('average_duration', '0s')}", "info")
        log(f"  错误总数: {error_summary.get('total_errors', 0)}", "info")
        
        if perf_summary.get('slowest_operation'):
            slowest = perf_summary['slowest_operation']
            log(f"  最慢操作: {slowest['name']} ({slowest['duration']})", "info")
    
    log("=== 性能摘要结束 ===", "info")
