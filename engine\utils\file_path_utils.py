#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
统一的文件路径处理工具
提供可靠的文件路径处理、权限检查和错误诊断功能
"""

import os
import stat
import shutil
import tempfile
from pathlib import Path
from engine.utils.logger import log
from engine.utils.i18n import _

class FilePathUtils:
    """统一的文件路径处理工具类"""
    
    @staticmethod
    def normalize_output_path(output_path: str, base_dir: str = None) -> str:
        """
        标准化输出文件路径
        
        Args:
            output_path: 原始输出路径
            base_dir: 基础目录（可选）
            
        Returns:
            str: 标准化的绝对路径
        """
        if not output_path:
            raise ValueError("输出路径不能为空")
        
        # 转换为Path对象进行处理
        path = Path(output_path)
        
        # 如果是相对路径，转换为绝对路径
        if not path.is_absolute():
            if base_dir:
                path = Path(base_dir) / path
            else:
                # 使用当前工作目录
                path = Path.cwd() / path
        
        # 确保路径是绝对路径
        absolute_path = path.resolve()
        
        log(_("file_path_utils.path_normalized"), "debug", 
            original=output_path, normalized=str(absolute_path))
        
        return str(absolute_path)
    
    @staticmethod
    def check_directory_permissions(directory: str) -> dict:
        """
        检查目录权限
        
        Args:
            directory: 目录路径
            
        Returns:
            dict: 权限检查结果
        """
        result = {
            'exists': False,
            'readable': False,
            'writable': False,
            'executable': False,
            'can_create': False,
            'error': None
        }
        
        try:
            dir_path = Path(directory)
            
            if dir_path.exists():
                result['exists'] = True
                result['readable'] = os.access(directory, os.R_OK)
                result['writable'] = os.access(directory, os.W_OK)
                result['executable'] = os.access(directory, os.X_OK)
            else:
                # 检查是否可以创建目录
                parent_dir = dir_path.parent
                if parent_dir.exists():
                    result['can_create'] = os.access(str(parent_dir), os.W_OK)
                else:
                    # 递归检查父目录
                    parent_result = FilePathUtils.check_directory_permissions(str(parent_dir))
                    result['can_create'] = parent_result.get('can_create', False)
            
        except Exception as e:
            result['error'] = str(e)
            log(_("file_path_utils.permission_check_failed"), "warning",
                directory=directory, error=str(e))
        
        return result
    
    @staticmethod
    def check_disk_space(path: str, required_space: int = 10 * 1024 * 1024) -> dict:
        """
        检查磁盘空间
        
        Args:
            path: 文件路径
            required_space: 所需空间（字节），默认10MB
            
        Returns:
            dict: 磁盘空间检查结果
        """
        result = {
            'available_space': 0,
            'required_space': required_space,
            'sufficient': False,
            'error': None
        }
        
        try:
            # 获取路径所在的磁盘
            if os.path.exists(path):
                check_path = path
            else:
                # 如果路径不存在，检查父目录
                check_path = os.path.dirname(path)
                max_iterations = 10  # 防止无限循环
                iteration_count = 0

                while check_path and not os.path.exists(check_path) and iteration_count < max_iterations:
                    parent_path = os.path.dirname(check_path)
                    if parent_path == check_path:  # 已到达根目录
                        break
                    check_path = parent_path
                    iteration_count += 1

                if not check_path or not os.path.exists(check_path):
                    check_path = os.getcwd()
            
            # 获取磁盘空间信息
            statvfs = shutil.disk_usage(check_path)
            result['available_space'] = statvfs.free
            result['sufficient'] = statvfs.free >= required_space
            
            log(_("file_path_utils.disk_space_checked"), "debug",
                path=check_path, available=statvfs.free, required=required_space,
                sufficient=result['sufficient'])
            
        except Exception as e:
            result['error'] = str(e)
            log(_("file_path_utils.disk_space_check_failed"), "warning",
                path=path, error=str(e))
        
        return result
    
    @staticmethod
    def ensure_output_directory(output_path: str) -> dict:
        """
        确保输出目录存在并可写
        
        Args:
            output_path: 输出文件路径
            
        Returns:
            dict: 操作结果
        """
        result = {
            'success': False,
            'directory': None,
            'created': False,
            'error': None,
            'details': {}
        }
        
        try:
            # 标准化路径
            normalized_path = FilePathUtils.normalize_output_path(output_path)
            directory = os.path.dirname(normalized_path)
            result['directory'] = directory
            
            # 检查目录权限
            perm_result = FilePathUtils.check_directory_permissions(directory)
            result['details']['permissions'] = perm_result
            
            # 检查磁盘空间
            space_result = FilePathUtils.check_disk_space(normalized_path)
            result['details']['disk_space'] = space_result
            
            # 如果目录不存在，尝试创建
            if not perm_result['exists']:
                if perm_result['can_create']:
                    os.makedirs(directory, exist_ok=True)
                    result['created'] = True
                    log(_("file_path_utils.directory_created"), "info", directory=directory)
                else:
                    result['error'] = "没有权限创建目录"
                    return result
            
            # 再次检查权限
            if not perm_result['writable']:
                result['error'] = "目录不可写"
                return result
            
            # 检查磁盘空间
            if not space_result['sufficient']:
                result['error'] = f"磁盘空间不足，可用: {space_result['available_space']} 字节，需要: {space_result['required_space']} 字节"
                return result
            
            result['success'] = True
            log(_("file_path_utils.output_directory_ready"), "debug", directory=directory)
            
        except Exception as e:
            result['error'] = str(e)
            log(_("file_path_utils.ensure_directory_failed"), "error",
                path=output_path, error=str(e))
        
        return result
    
    @staticmethod
    def safe_write_file(file_path: str, content: str, encoding: str = 'utf-8', backup: bool = True) -> dict:
        """
        安全地写入文件
        
        Args:
            file_path: 文件路径
            content: 文件内容
            encoding: 编码格式
            backup: 是否备份现有文件
            
        Returns:
            dict: 写入结果
        """
        result = {
            'success': False,
            'file_path': file_path,
            'backup_path': None,
            'error': None,
            'bytes_written': 0
        }
        
        try:
            # 标准化路径
            normalized_path = FilePathUtils.normalize_output_path(file_path)
            result['file_path'] = normalized_path
            
            # 确保输出目录存在
            dir_result = FilePathUtils.ensure_output_directory(normalized_path)
            if not dir_result['success']:
                result['error'] = f"目录准备失败: {dir_result['error']}"
                return result
            
            # 如果文件已存在且需要备份
            if backup and os.path.exists(normalized_path):
                backup_path = f"{normalized_path}.backup"
                shutil.copy2(normalized_path, backup_path)
                result['backup_path'] = backup_path
                log(_("file_path_utils.file_backed_up"), "debug", 
                    original=normalized_path, backup=backup_path)
            
            # 写入文件
            with open(normalized_path, 'w', encoding=encoding) as f:
                f.write(content)
                result['bytes_written'] = len(content.encode(encoding))
            
            result['success'] = True
            log(_("file_path_utils.file_written_successfully"), "info",
                file=normalized_path, size=result['bytes_written'])
            
        except Exception as e:
            result['error'] = str(e)
            log(_("file_path_utils.file_write_failed"), "error",
                file=file_path, error=str(e))
            
            # 如果有备份，尝试恢复
            if result['backup_path'] and os.path.exists(result['backup_path']):
                try:
                    shutil.copy2(result['backup_path'], normalized_path)
                    log(_("file_path_utils.file_restored_from_backup"), "info",
                        file=normalized_path)
                except:
                    pass
        
        return result
    
    @staticmethod
    def create_temp_output_path(prefix: str = "config_converter_", suffix: str = ".xml") -> str:
        """
        创建临时输出路径
        
        Args:
            prefix: 文件名前缀
            suffix: 文件扩展名
            
        Returns:
            str: 临时文件路径
        """
        temp_dir = tempfile.gettempdir()
        temp_file = tempfile.NamedTemporaryFile(
            prefix=prefix,
            suffix=suffix,
            dir=temp_dir,
            delete=False
        )
        temp_path = temp_file.name
        temp_file.close()
        
        log(_("file_path_utils.temp_path_created"), "debug", path=temp_path)
        return temp_path

def diagnose_file_write_issue(file_path: str) -> dict:
    """
    诊断文件写入问题
    
    Args:
        file_path: 文件路径
        
    Returns:
        dict: 诊断结果
    """
    diagnosis = {
        'file_path': file_path,
        'issues': [],
        'recommendations': [],
        'can_write': False
    }
    
    try:
        # 标准化路径
        normalized_path = FilePathUtils.normalize_output_path(file_path)
        directory = os.path.dirname(normalized_path)
        
        # 检查目录权限
        perm_result = FilePathUtils.check_directory_permissions(directory)
        if not perm_result['exists'] and not perm_result['can_create']:
            diagnosis['issues'].append("目录不存在且无法创建")
            diagnosis['recommendations'].append("检查父目录权限或使用其他输出路径")
        
        if perm_result['exists'] and not perm_result['writable']:
            diagnosis['issues'].append("目录不可写")
            diagnosis['recommendations'].append("检查目录权限或更改输出路径")
        
        # 检查磁盘空间
        space_result = FilePathUtils.check_disk_space(normalized_path)
        if not space_result['sufficient']:
            diagnosis['issues'].append(f"磁盘空间不足: 可用 {space_result['available_space']} 字节")
            diagnosis['recommendations'].append("清理磁盘空间或使用其他输出路径")
        
        # 检查文件是否被占用
        if os.path.exists(normalized_path):
            try:
                with open(normalized_path, 'a'):
                    pass
            except PermissionError:
                diagnosis['issues'].append("文件被其他程序占用")
                diagnosis['recommendations'].append("关闭占用文件的程序或使用其他文件名")
        
        diagnosis['can_write'] = len(diagnosis['issues']) == 0
        
    except Exception as e:
        diagnosis['issues'].append(f"诊断过程出错: {str(e)}")
    
    return diagnosis
