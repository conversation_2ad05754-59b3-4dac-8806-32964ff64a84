import os
import shutil
import tempfile
import json
import hashlib
import re
from engine.utils.logger import log
from engine.utils.i18n import _

def ensure_dir_exists(directory):
    """确保目录存在，如果不存在则创建"""
    if not os.path.exists(directory):
        os.makedirs(directory)
        log(_("info.directory_created"), "info", directory=directory)
    return directory

def get_temp_dir(prefix="fortigate_temp_"):
    """创建临时目录"""
    temp_dir = tempfile.mkdtemp(prefix=prefix)
    log(_("info.temp_directory_created"), "info", directory=temp_dir)
    return temp_dir

def cleanup_temp_dir(directory):
    """清理临时目录"""
    if os.path.exists(directory):
        shutil.rmtree(directory)
        log(_("info.temp_directory_deleted"), "info", directory=directory)

def get_task_directory(output_file_path):
    """
    根据输出文件路径获取任务目录

    Args:
        output_file_path (str): 输出文件路径，例如 "data/output/result.xml" 或 "/tmp/task_dir/result.xml"

    Returns:
        str: 任务目录路径，例如 "data" 或 "/tmp/task_dir"
    """
    if not output_file_path:
        return os.getcwd()

    # 获取输出目录（输出文件所在的目录）
    output_dir = os.path.dirname(output_file_path)

    # 如果输出目录为空（相对路径文件名），使用当前工作目录
    if not output_dir or output_dir.strip() == "":
        return os.getcwd()

    # 检查是否为传统的data/output结构
    if output_dir.endswith(os.sep + "output") or output_dir.endswith("/output"):
        # 传统结构：output_dir = "data/output" -> task_dir = "data"
        task_dir = os.path.dirname(output_dir)
        # 如果任务目录为空，使用输出目录本身
        if not task_dir or task_dir.strip() == "":
            task_dir = output_dir
    else:
        # 新结构：输出文件直接在任务目录中
        # 例如："/tmp/task_dir/result.xml" -> task_dir = "/tmp/task_dir"
        task_dir = output_dir

    return task_dir

def get_task_data_directory(output_file_path):
    """
    根据输出文件路径获取任务数据目录

    Args:
        output_file_path (str): 输出文件路径，例如 "data/output/result.xml"

    Returns:
        str: 任务数据目录路径，例如 "data"
    """
    task_dir = get_task_directory(output_file_path)
    return task_dir

def get_interface_mapping_file_path(output_file_path):
    """
    根据输出文件路径获取接口映射文件路径

    Args:
        output_file_path (str): 输出文件路径，例如 "data/output/result.xml"

    Returns:
        str: 接口映射文件路径，例如 "data/interface_mapping.json"
    """
    task_data_dir = get_task_data_directory(output_file_path)
    return os.path.join(task_data_dir, "interface_mapping.json")

# 全局变量用于存储当前任务的输出文件路径
_current_output_file_path = None

def set_current_output_file_path(output_file_path):
    """
    设置当前任务的输出文件路径

    Args:
        output_file_path (str): 输出文件路径
    """
    global _current_output_file_path
    _current_output_file_path = output_file_path

def get_current_output_file_path():
    """
    获取当前任务的输出文件路径

    Returns:
        str: 当前输出文件路径，如果未设置则返回默认值
    """
    global _current_output_file_path
    return _current_output_file_path or "data/output/result.xml"

def get_current_interface_mapping_file_path():
    """
    获取当前任务的接口映射文件路径

    Returns:
        str: 当前任务的接口映射文件路径
    """
    return get_interface_mapping_file_path(get_current_output_file_path())

def read_json_file(file_path):
    """读取JSON文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        return data
    except Exception as e:
        error_msg = _("error.read_json_file_failed", error=str(e))
        log(error_msg, "error")
        return None

def write_json_file(data, file_path, indent=2):
    """写入JSON文件"""
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=indent)
        log(_("info.json_file_saved"), "info", file_path=file_path)
        return True
    except Exception as e:
        error_msg = _("error.write_json_file_failed", error=str(e))
        log(error_msg, "error")
        return False

def read_text_file(file_path, encoding='utf-8', errors='ignore'):
    """读取文本文件"""
    try:
        with open(file_path, 'r', encoding=encoding, errors=errors) as f:
            content = f.read()
        return content
    except Exception as e:
        error_msg = _("error.read_text_file_failed", error=str(e))
        log(error_msg, "error")
        return None

def write_text_file(content, file_path, encoding='utf-8'):
    """写入文本文件"""
    try:
        with open(file_path, 'w', encoding=encoding) as f:
            f.write(content)
        log(_("info.text_file_saved"), "info", file_path=file_path)
        return True
    except Exception as e:
        error_msg = _("error.write_text_file_failed", error=str(e))
        log(error_msg, "error")
        return False

def copy_file(src, dst):
    """复制文件"""
    try:
        shutil.copy2(src, dst)
        log(_("info.file_copied", src=src, dst=dst), "info")
        return True
    except Exception as e:
        error_msg = _("error.copy_file_failed", error=str(e))
        log(error_msg, "error")
        return False

def sanitize_filename_for_logging(filename, max_length=80):
    """
    为日志文件名创建安全的文件名

    Args:
        filename (str): 原始文件名
        max_length (int): 最大长度限制，默认80字符

    Returns:
        str: 安全的文件名
    """
    if not filename:
        return "unknown_file"

    # 获取文件名（不包含路径）
    base_name = os.path.basename(filename)

    # 移除文件扩展名
    name_without_ext = os.path.splitext(base_name)[0]

    # 清理特殊字符，只保留字母、数字、下划线、连字符、点号
    # 将特殊字符替换为下划线，但保留基本的文件名字符
    cleaned_name = re.sub(r'[^\w\-\.]', '_', name_without_ext)

    # 移除连续的下划线
    cleaned_name = re.sub(r'_+', '_', cleaned_name)

    # 移除开头和结尾的下划线
    cleaned_name = cleaned_name.strip('_')

    # 如果清理后为空，使用默认名称
    if not cleaned_name:
        cleaned_name = "config_file"

    # 生成原始文件名的哈希值（用于确保唯一性）
    hash_suffix = hashlib.md5(base_name.encode('utf-8')).hexdigest()[:8]

    # 计算可用于主文件名的长度（预留哈希后缀和分隔符的空间）
    available_length = max_length - len(hash_suffix) - 1  # -1 for underscore

    # 如果文件名太长，进行截断
    if len(cleaned_name) > available_length:
        # 保留前面部分和后面部分，中间用省略号连接
        if available_length > 10:
            front_part = cleaned_name[:available_length//2]
            back_part = cleaned_name[-(available_length - available_length//2 - 3):]
            cleaned_name = f"{front_part}...{back_part}"
        else:
            cleaned_name = cleaned_name[:available_length]

    # 组合最终的安全文件名
    safe_filename = f"{cleaned_name}_{hash_suffix}"


    return safe_filename