#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
国际化(i18n)工具模块
提供多语言支持功能，用于配置转换服务的本地化
"""

import os
import json
import logging
import re
import sys
import traceback
from typing import Dict, Any, Optional, List, Callable
from pathlib import Path

# 配置日志记录器 - 避免直接从utils.logger导入，防止循环导入
logger = logging.getLogger(__name__)

# 默认语言设置
DEFAULT_LANGUAGE = "zh-CN"

# 支持的语言列表 (ISO 639-1 语言代码 + ISO 3166-1 国家/地区代码)
SUPPORTED_LANGUAGES = [
    "zh-CN",  # 中文(中国)
    "en-US"  # 英文(美国)
]

# 语言文件目录
LOCALE_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), "locales")

# 全局翻译字典缓存
_translations: Dict[str, Dict[str, str]] = {}

# 全局语言设置
_current_language = DEFAULT_LANGUAGE

# 回退语言列表 - 如果在当前语言中找不到翻译，按此顺序尝试其他语言
_fallback_languages = ["en-US", "zh-CN"]

# 调试模式控制变量
_debug_mode = True

# 高性能翻译缓存
class TranslationCache:
    """高性能翻译缓存系统"""

    def __init__(self, max_size: int = 10000, ttl: int = 3600):
        self.max_size = max_size
        self.ttl = ttl  # 缓存生存时间（秒）
        self.cache = {}
        self.access_times = {}
        self.hit_count = 0
        self.miss_count = 0

    def _generate_key(self, translation_key: str, language: str, kwargs_hash: str) -> str:
        """生成缓存键"""
        return f"{language}:{translation_key}:{kwargs_hash}"

    def _hash_kwargs(self, kwargs: Dict) -> str:
        """生成kwargs的哈希值"""
        import hashlib
        import json
        try:
            # 排序kwargs以确保一致的哈希
            sorted_kwargs = json.dumps(kwargs, sort_keys=True, ensure_ascii=False)
            return hashlib.md5(sorted_kwargs.encode('utf-8')).hexdigest()[:8]
        except:
            return "no_hash"

    def get(self, translation_key: str, language: str, kwargs: Dict) -> Optional[str]:
        """从缓存获取翻译"""
        import time

        kwargs_hash = self._hash_kwargs(kwargs)
        cache_key = self._generate_key(translation_key, language, kwargs_hash)

        if cache_key in self.cache:
            entry = self.cache[cache_key]

            # 检查TTL
            if time.time() - entry['timestamp'] < self.ttl:
                self.access_times[cache_key] = time.time()
                self.hit_count += 1
                return entry['value']
            else:
                # 过期，删除
                del self.cache[cache_key]
                del self.access_times[cache_key]

        self.miss_count += 1
        return None

    def put(self, translation_key: str, language: str, kwargs: Dict, value: str):
        """将翻译放入缓存"""
        import time

        kwargs_hash = self._hash_kwargs(kwargs)
        cache_key = self._generate_key(translation_key, language, kwargs_hash)

        # 如果缓存已满，删除最旧的条目
        if len(self.cache) >= self.max_size:
            self._evict_oldest()

        self.cache[cache_key] = {
            'value': value,
            'timestamp': time.time()
        }
        self.access_times[cache_key] = time.time()

    def _evict_oldest(self):
        """删除最旧的缓存条目"""
        if not self.access_times:
            return

        oldest_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
        del self.cache[oldest_key]
        del self.access_times[oldest_key]

    def clear(self):
        """清空缓存"""
        self.cache.clear()
        self.access_times.clear()
        self.hit_count = 0
        self.miss_count = 0

    def get_stats(self) -> Dict:
        """获取缓存统计"""
        total_requests = self.hit_count + self.miss_count
        hit_rate = (self.hit_count / total_requests * 100) if total_requests > 0 else 0

        return {
            'size': len(self.cache),
            'max_size': self.max_size,
            'hit_count': self.hit_count,
            'miss_count': self.miss_count,
            'hit_rate': round(hit_rate, 2),
            'total_requests': total_requests
        }

# 全局翻译缓存实例
_translation_cache = TranslationCache()



# 翻译键前缀列表
TRANSLATION_PREFIXES = [
    "info.", "error.", "warning.", "debug.", "yang.", "fortigate.",
    "yang_parser.", "interface.", "reason.", "status.",
    "enhanced_yang_generator.", "interface_handler.", "validator.", "compress.",
    "user.", "config_type.", "detail.", "suggestion.", "severity.",
    "stats.", "common.", "issue.", "description.",
    # 核心功能前缀
    "semantic.", "main.", "encrypt.", "category.", "nat.", "policy.",
    "manual_config.", "compatibility.", "dns_generator.", "service_processing.",
    "operation_mode_processor.", "transparent_mode_processor.", "vlan.",
    "fortigate_parser_adapter.", "security_policy_generator_adapter.",
    # 新增的前缀
    "address.", "address_group.", "config_converter.", "device.", "feature.", "format.",
    "help.", "impact.", "ntos_extensions.", "physical_interface_handler.",
    "protocol.", "report.", "route.", "routing_handler.", "security_zone_handler.",
    "service.", "service_group.", "time_range.", "xml_utils.", "yang_extensions.",
    "yang_validator.", "zone.",
    # 新架构处理阶段前缀
    "address_processing_stage.", "service_processing_stage.", "interface_processing_stage.",
    "encryption_stage.", "pipeline_manager.", "pipeline_stage.", "data_context.",
    "operation_mode_stage.", "address_group_processing_stage.", "service_group_processing_stage.",
    # NTOS优化功能前缀
    "ntos_name_validator.", "xml_template_integration.",
    # 重构后的集成器前缀
    "interface_integrator.", "network_object_integrator.", "service_object_integrator.",
    "security_policy_integrator.", "refactored_wrapper.",
    "zone_processing_stage.", "time_range_processing_stage.", "dns_processing_stage.",
    # 策略区域识别功能前缀
    "policy_zone_identification.",
    "static_route_processing_stage.", "yang_validation_stage.", "fortigate_policy_stage.",
    # 工作流和转换相关前缀
    "conversion_workflow.", "fortigate_policy_stage.", "xml_template_integration_stage.",
    "template_manager.", "yang_manager.", "config_manager.", "validation_service.",
    "xml_integrator.", "xml_template_integration.", "security_policy.", "fortigate_strategy.",
    # 服务和管理器前缀
    "conversion_service.", "yang_loader.", "conversion_strategy.", "cache_manager.",
    # 地址池增强功能前缀
    "nat_processor.", "ippool_validator.", "ippool_capacity_analyzer.", "ippool_usage_analyzer.",
    "ippool_reference_manager.", "yang_ippool_validator.", "ippool_optimizer.",
    "usage_analyzer.", "optimizer.",
    # 服务映射器前缀
    "fortigate_service_mapper.",
    "performance_monitor.", "memory_manager.", "exception_registry.", "error_handler.",
    # 处理器前缀
    "interface_processor.", "address_processor.", "address_group_processor.", "service_processor.",
    "service_group_processor.", "zone_processor.", "time_range_processor.", "dns_processor.",
    "static_route_processor.", "policy_processor.", "interface_handler.", "file_path_utils.",
    # 地址处理器相关前缀
    "address_processor.iprange.", "address_processor.validation.", "address_processor.generation.",
    # 新架构管道和处理相关前缀
    "pipeline.", "stage.", "processing.", "monitor.", "optimization.", "chunk.",
    "memory.", "config_parsing.", "template_preparation.", "yang_preparation.",
    "output_preparation.", "environment.", "validation.", "mapping.", "workflow.",
    # 基础设施和管理器前缀
    "config_manager.", "template_manager.", "yang_manager.", "physical_interface_handler.",
    "template_loader.", "template_cache.", "name_manager.", "name_validator.",
    "conversion_workflow.", "cache_manager.", "performance_monitor.", "memory_manager.",
    "exception_registry.", "error_handler.", "strategy_factory.", "generator_registry.",
    "parser_registry.", "rule_registry.", "generator_interface.", "parser_plugin.",
    # 系统和工具前缀
    "ui.", "app.", "startup.", "log.", "logger.", "lru_cache.", "notifications.",
    "extraction_service.", "conversion_report.", "validation_workflow.", "bridge_generator.",
    "dns_generator.", "nat_generator_adapter.", "xml_generator_adapter.", "wildcard.",
    "type.", "success.", "running.", "fix.", "address_processing.", "interface_mapping."
]

# 确保语言目录存在
if not os.path.exists(LOCALE_DIR):
    try:
        os.makedirs(LOCALE_DIR, exist_ok=True)
        logger.info(f"创建语言文件目录: {LOCALE_DIR}")
    except Exception as e:
        logger.error(f"创建语言文件目录失败: {LOCALE_DIR}, 错误: {str(e)}")

# 处理缺失的语言文件，避免日志记录错误
for lang in SUPPORTED_LANGUAGES:
    lang_file = os.path.join(LOCALE_DIR, f"{lang}.json")
    if not os.path.exists(lang_file):
        # 输出到标准错误而不使用logger，避免循环依赖

        # 创建一个空的语言文件以避免反复报错
        try:
            with open(lang_file, 'w', encoding='utf-8') as f:
                json.dump({}, f, ensure_ascii=False, indent=2)
            logger.info(f"已创建空语言文件: {lang_file}")
        except Exception as e:
            logger.error(f"创建语言文件失败: {e}")


def normalize_language_code(language: str) -> str:
    """
    规范化语言代码格式
    
    Args:
        language: 输入的语言代码，例如 'zh_CN', 'zh-cn', 'ZH-CN'
        
    Returns:
        规范化的语言代码，例如 'zh-CN'
    """
    if not language:
        return DEFAULT_LANGUAGE
    
    # 统一使用连字符代替下划线
    language = language.replace('_', '-')
    
    # 尝试匹配语言-国家/地区格式
    if re.match(r'^[a-z]{2}-[A-Z]{2}$', language):
        return language
    
    # 尝试修正为标准格式 (例如 zh-cn -> zh-CN)
    if re.match(r'^[a-z]{2}-[a-z]{2}$', language):
        lang, region = language.split('-')
        return f"{lang}-{region.upper()}"
    
    # 如果只有语言代码没有地区代码
    if re.match(r'^[a-z]{2}$', language):
        # 常见语言的默认地区映射
        default_regions = {
            "zh": "CN",
            "en": "US",
            "ja": "JP",
            "ko": "KR",
            "fr": "FR",
            "de": "DE",
            "es": "ES",
            "ru": "RU"
        }
        if language in default_regions:
            return f"{language}-{default_regions[language]}"
    
    # 其他情况返回默认语言
    logger.warning(f"无法识别的语言代码: {language}，使用默认语言: {DEFAULT_LANGUAGE}")
    return DEFAULT_LANGUAGE

def load_translations(language: str) -> Dict[str, str]:
    """
    加载指定语言的翻译文件
    
    Args:
        language: 语言代码，例如 'zh-CN', 'en-US'
        
    Returns:
        包含翻译内容的字典
    """
    # 规范化语言代码
    language = normalize_language_code(language)
    
    if language in _translations:
        return _translations[language]
    
    # 确保语言代码合法
    if language not in SUPPORTED_LANGUAGES:
        logger.warning(f"不支持的语言: {language}, 使用默认语言: {DEFAULT_LANGUAGE}")
        language = DEFAULT_LANGUAGE
    
    # 构建语言文件路径
    lang_file = os.path.join(LOCALE_DIR, f"{language}.json")
    
    # 加载翻译文件
    try:
        if os.path.exists(lang_file):
            with open(lang_file, 'r', encoding='utf-8') as f:
                translations = json.load(f)
                _translations[language] = translations

                return translations
        else:
            logger.error(f"语言文件不存在: {lang_file}")
            # 如果指定的语言文件不存在，尝试加载默认语言
            if language != DEFAULT_LANGUAGE:
                logger.info(f"尝试加载默认语言: {DEFAULT_LANGUAGE}")
                return load_translations(DEFAULT_LANGUAGE)
            return {}
    except Exception as e:
        logger.error(f"加载语言文件失败: {lang_file}, 错误: {str(e)}")
        logger.error(f"异常堆栈: {traceback.format_exc()}")
        return {}

def get_supported_languages() -> List[Dict[str, str]]:
    """
    获取支持的语言列表，包含语言名称和代码
    
    Returns:
        语言信息列表，每项包含code和name
    """
    # 语言名称映射
    language_names = {
        "zh-CN": "简体中文",
        "en-US": "English (US)",
        "ja-JP": "日本語",
        "ko-KR": "한국어",
        "fr-FR": "Français",
        "de-DE": "Deutsch",
        "es-ES": "Español",
        "ru-RU": "Русский"
    }
    
    return [
        {"code": code, "name": language_names.get(code, code)}
        for code in SUPPORTED_LANGUAGES
    ]

def translate(key: str, language: str = None, **kwargs) -> str:
    """
    翻译指定的键（带缓存优化）

    Args:
        key: 翻译键
        language: 语言代码，如果为None则使用当前语言
        **kwargs: 格式化参数

    Returns:
        翻译后的文本，如果找到翻译则返回键本身
    """
    global _current_language, _fallback_languages, _translation_cache

    # 检查kwargs中是否有language参数，如果有则移除它以避免冲突
    if 'language' in kwargs:
        # 如果language参数为None，优先使用kwargs中的值
        if language is None:
            language = kwargs.pop('language')
        else:
            # 否则移除kwargs中的language参数以避免冲突
            kwargs.pop('language')

    if language is None:
        language = _current_language

    # 规范化语言代码
    language = normalize_language_code(language)

    # 检查缓存
    cached_result = _translation_cache.get(key, language, kwargs)
    if cached_result is not None:
        return cached_result

    # 如果key就是直接的字符串而非翻译键，尝试直接格式化
    if not any(key.startswith(prefix) for prefix in TRANSLATION_PREFIXES):
        try:
            if kwargs:
                result = key.format(**kwargs)
                _translation_cache.put(key, language, kwargs, result)
                return result
            _translation_cache.put(key, language, kwargs, key)
            return key
        except:
            _translation_cache.put(key, language, kwargs, key)
            return key

    # 尝试按优先级加载翻译
    # 先尝试指定语言
    translations = load_translations(language)

    if key in translations:
        text = translations[key]
    else:
        # 如果在指定语言中找不到，尝试回退语言
        for fallback_lang in _fallback_languages:
            if fallback_lang == language:
                continue  # 跳过已尝试的语言

            fallback_translations = load_translations(fallback_lang)
            if key in fallback_translations:
                text = fallback_translations[key]
                if _debug_mode:
                    logger.debug(f"在回退语言 {fallback_lang} 中找到翻译: {key}")
                break
        else:
            # 如果所有语言都没找到，返回键本身
            text = key

    try:
        # 尝试使用提供的参数格式化翻译文本
        if kwargs:
            # 修复翻译参数名不匹配问题
            try:
                from engine.utils.logger import fix_translation_params
                fixed_kwargs = fix_translation_params(key, kwargs.copy())
                result = text.format(**fixed_kwargs)
                _translation_cache.put(key, language, kwargs, result)
                return result
            except ImportError:
                # 如果无法导入修复函数，使用原始参数
                result = text.format(**kwargs)
                _translation_cache.put(key, language, kwargs, result)
                return result

        _translation_cache.put(key, language, kwargs, text)
        return text

    except KeyError as e:
        # 如果格式化失败，记录警告并返回未格式化的文本
        logger.warning(f"翻译 '{key}' 中缺少参数 {e}")
        _translation_cache.put(key, language, kwargs, text)
        return text
    except Exception as e:
        # 如果格式化失败，记录警告并返回未格式化的文本
        logger.warning(f"格式化翻译 '{key}' 时出错: {e}")
        _translation_cache.put(key, language, kwargs, text)
        return text

def create_locale_dir():
    """创建语言文件目录"""
    if not os.path.exists(LOCALE_DIR):
        try:
            os.makedirs(LOCALE_DIR)
            logger.info(f"创建语言文件目录: {LOCALE_DIR}")
        except Exception as e:
            logger.error(f"创建语言文件目录失败: {LOCALE_DIR}, 错误: {str(e)}")

def _(key: str, **kwargs) -> str:
    """
    简单的国际化支持函数，用于翻译文本

    Args:
        key (str): 待翻译的文本键或者文本内容
        **kwargs: 格式化参数

    Returns:
        str: 翻译后的文本
    """
    # 调用translate函数进行翻译，避免传递language参数以避免冲突
    return translate(key, **kwargs)

def safe_translate(key: str, language: str = None, **kwargs) -> str:
    """
    安全的翻译函数，提供高性能和强错误处理

    Args:
        key: 翻译键或文本内容
        language: 语言代码，如果为None则使用当前语言
        **kwargs: 格式化参数

    Returns:
        翻译后的文本

    Features:
        - 缓存优化：避免重复加载翻译文件
        - 参数验证：自动修复参数名不匹配问题
        - 错误恢复：翻译失败时提供合理的回退机制
        - 性能监控：可选的性能统计
    """
    # 性能计数器（可选）
    if _debug_mode:
        import time
        start_time = time.time()

    try:
        # 输入验证
        if not isinstance(key, str):
            key = str(key) if key is not None else ""

        if not key.strip():
            return ""

        # 语言参数处理
        if language is None:
            language = _current_language

        # 清理kwargs，避免参数冲突
        clean_kwargs = kwargs.copy()
        clean_kwargs.pop('language', None)
        clean_kwargs.pop('key', None)

        # 尝试翻译
        result = translate(key, language, **clean_kwargs)

        # 性能监控
        if _debug_mode:
            elapsed = time.time() - start_time
            if elapsed > 0.01:  # 超过10ms记录警告
                logger.warning(f"翻译性能警告: 键 '{key}' 耗时 {elapsed:.3f}s")

        return result

    except Exception as e:
        # 错误恢复机制
        logger.warning(f"safe_translate失败: key='{key}', error={str(e)}")

        # 尝试简单的字符串替换
        try:
            result = key
            for k, v in kwargs.items():
                placeholder = f"{{{k}}}"
                if placeholder in result:
                    result = result.replace(placeholder, str(v))
            return result
        except Exception as fallback_error:
            logger.error(f"safe_translate回退机制也失败: {str(fallback_error)}")
            return key

def auto_translate(text: str, **kwargs) -> str:
    """
    自动检测并翻译文本或键
    
    Args:
        text: 要翻译的文本或键
        **kwargs: 格式化参数
        
    Returns:
        翻译后的文本
    """
    # 如果文本以i18n:开头，视为翻译键
    if text.startswith("i18n:"):
        key = text[5:]  # 移除i18n:前缀
        return _(key, **kwargs)
    
    # 否则，检查文本是否符合翻译键格式
    if any(text.startswith(prefix) for prefix in TRANSLATION_PREFIXES):
        return _(text, **kwargs)
    
    # 普通文本，只进行格式化
    try:
        if kwargs:
            return text.format(**kwargs)
    except:
        pass
    return text

def format_log(key: str, **kwargs) -> str:
    """
    格式化日志消息
    
    Args:
        key: 消息键或原始消息
        **kwargs: 格式化参数
        
    Returns:
        格式化后的消息
    """
    # 使用自动翻译功能
    return auto_translate(key, **kwargs)

def set_fallback_languages(languages: List[str]):
    """
    设置翻译回退语言列表

    Args:
        languages: 语言代码列表，按优先级排序
    """
    global _fallback_languages
    _fallback_languages = [normalize_language_code(lang) for lang in languages]
    if _debug_mode:
        logger.debug(f"设置翻译回退语言: {_fallback_languages}")

def set_debug_mode(enabled: bool):
    """
    设置调试模式

    Args:
        enabled: 是否启用调试模式
    """
    global _debug_mode
    _debug_mode = enabled

    # 根据调试模式设置日志级别
    if enabled:
        logger.setLevel(logging.DEBUG)
    else:
        logger.setLevel(logging.INFO)

    if _debug_mode:
        logger.debug(f"i18n调试模式已{'启用' if enabled else '禁用'}")

def get_debug_mode() -> bool:
    """
    获取当前调试模式状态

    Returns:
        当前调试模式状态
    """
    return _debug_mode



def initialize():
    """初始化i18n模块"""
    create_locale_dir()
    # 预加载默认语言翻译
    load_translations(DEFAULT_LANGUAGE)
    # 预加载所有支持的语言
    for lang in SUPPORTED_LANGUAGES:
        load_translations(lang)

def init_i18n(language: str = DEFAULT_LANGUAGE):
    """
    初始化国际化设置
    
    Args:
        language: 语言代码，例如 'zh-CN', 'en-US'
    """
    global _current_language
    
    # 规范化语言代码
    language = normalize_language_code(language)
    
    # 设置当前语言
    _current_language = language
    
    # 预加载翻译
    translations = load_translations(language)
    
    # 打印诊断信息
    logger.info(f"已初始化国际化模块，使用语言: {language}，加载了 {len(translations)} 个翻译项")
    
    # 设置环境变量
    os.environ['LANG'] = f"{language}.UTF-8"
    os.environ['LC_ALL'] = f"{language}.UTF-8"
    os.environ['PYTHONIOENCODING'] = "utf-8"

def get_current_language() -> str:
    """
    获取当前设置的语言
    
    Returns:
        当前语言代码
    """
    return _current_language

def get_translation_coverage(language: str = None) -> Dict[str, Any]:
    """
    获取翻译覆盖率统计
    
    Args:
        language: 要检查的语言代码，如果为None则检查所有语言
        
    Returns:
        包含覆盖率信息的字典
    """
    if language:
        languages = [normalize_language_code(language)]
    else:
        languages = SUPPORTED_LANGUAGES
    
    # 确保所有语言都已加载
    base_translations = load_translations(DEFAULT_LANGUAGE)
    base_keys = set(base_translations.keys())
    
    result = {}
    
    for lang in languages:
        if lang == DEFAULT_LANGUAGE:
            continue  # 跳过基准语言
            
        translations = load_translations(lang)
        lang_keys = set(translations.keys())
        
        # 计算覆盖率
        covered = len(lang_keys.intersection(base_keys))
        total = len(base_keys)
        coverage = round(covered / total * 100, 2) if total > 0 else 0
        
        # 找出缺失的键
        missing_keys = base_keys - lang_keys
        
        result[lang] = {
            "covered": covered,
            "total": total,
            "coverage_percent": coverage,
            "missing_keys": list(missing_keys)
        }
    
    return result

def add_runtime_translation(language: str, key: str, value: str):
    """
    在运行时添加或更新翻译
    
    Args:
        language: 语言代码
        key: 翻译键
        value: 翻译值
    """
    language = normalize_language_code(language)
    
    # 确保语言翻译字典已加载
    if language not in _translations:
        _translations[language] = {}
    
    # 添加或更新翻译
    _translations[language][key] = value
    if _debug_mode:
        logger.debug(f"添加运行时翻译: [{language}] {key} = {value}")

def export_missing_translations(output_file: str = None):
    """
    导出所有缺失的翻译到文件
    
    Args:
        output_file: 输出文件路径，如果为None则输出到日志
    """
    coverage = get_translation_coverage()
    
    result = {}
    for lang, data in coverage.items():
        if data["missing_keys"]:
            result[lang] = {
                "missing_count": len(data["missing_keys"]),
                "keys": {}
            }
            
            # 添加缺失键的默认翻译作为参考
            base_translations = load_translations(DEFAULT_LANGUAGE)
            for key in data["missing_keys"]:
                result[lang]["keys"][key] = base_translations.get(key, "")
    
    if output_file:
        try:
            os.makedirs(os.path.dirname(output_file), exist_ok=True)
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            logger.info(f"已导出缺失翻译到: {output_file}")
        except Exception as e:
            logger.error(f"导出缺失翻译失败: {str(e)}")
    else:
        # 输出到日志
        logger.info(f"缺失翻译统计: {json.dumps(result, ensure_ascii=False)}")
    
    return result

def get_translation_content(key: str, language: str = None) -> str:
    """
    获取翻译键的原始内容（不进行参数替换）

    Args:
        key: 翻译键
        language: 语言代码，如果为None则使用当前语言

    Returns:
        翻译内容字符串，如果找不到则返回键本身
    """
    if language is None:
        language = _current_language

    # 加载翻译数据
    translations = load_translations(language)

    # 查找翻译
    if key in translations:
        return translations[key]

    # 如果当前语言找不到，尝试默认语言
    if language != DEFAULT_LANGUAGE:
        default_translations = load_translations(DEFAULT_LANGUAGE)
        if key in default_translations:
            return default_translations[key]

    # 都找不到，返回键本身
    return key

def clear_translation_cache():
    """清空翻译缓存"""
    global _translation_cache
    _translation_cache.clear()
    if _debug_mode:
        logger.debug("翻译缓存已清空")

def get_cache_stats() -> Dict:
    """获取缓存统计信息"""
    global _translation_cache
    return _translation_cache.get_stats()

def optimize_cache():
    """优化缓存性能"""
    global _translation_cache
    stats = _translation_cache.get_stats()

    # 如果命中率低于50%，清空缓存重新开始
    if stats['hit_rate'] < 50 and stats['total_requests'] > 100:
        _translation_cache.clear()
        if _debug_mode:
            logger.debug(f"缓存命中率过低({stats['hit_rate']}%)，已清空缓存")

    # 如果缓存使用率超过90%，提前清理
    elif stats['size'] / stats['max_size'] > 0.9:
        # 清理一半的缓存
        for _ in range(stats['size'] // 2):
            _translation_cache._evict_oldest()
        if _debug_mode:
            logger.debug(f"缓存使用率过高，已清理 {stats['size'] // 2} 个条目")

def validate_translation_integrity() -> Dict:
    """验证翻译完整性"""
    issues = {
        'missing_translations': [],
        'parameter_mismatches': [],
        'format_errors': [],
        'duplicate_keys': []
    }

    try:
        # 加载所有支持的语言
        all_translations = {}
        for lang in SUPPORTED_LANGUAGES:
            all_translations[lang] = load_translations(lang)

        # 获取基准语言的键
        base_lang = DEFAULT_LANGUAGE
        base_keys = set(all_translations[base_lang].keys())

        # 检查每种语言的翻译完整性
        for lang, translations in all_translations.items():
            if lang == base_lang:
                continue

            lang_keys = set(translations.keys())

            # 检查缺失的翻译
            missing = base_keys - lang_keys
            if missing:
                issues['missing_translations'].extend([
                    {'language': lang, 'key': key} for key in missing
                ])

            # 检查参数不匹配
            for key in base_keys.intersection(lang_keys):
                base_text = all_translations[base_lang][key]
                lang_text = translations[key]

                # 提取参数占位符
                import re
                base_params = set(re.findall(r'\{([^}]+)\}', base_text))
                lang_params = set(re.findall(r'\{([^}]+)\}', lang_text))

                if base_params != lang_params:
                    issues['parameter_mismatches'].append({
                        'language': lang,
                        'key': key,
                        'base_params': list(base_params),
                        'lang_params': list(lang_params)
                    })

        # 检查重复值
        for lang, translations in all_translations.items():
            value_to_keys = {}
            for key, value in translations.items():
                if value in value_to_keys:
                    value_to_keys[value].append(key)
                else:
                    value_to_keys[value] = [key]

            for value, keys in value_to_keys.items():
                if len(keys) > 1:
                    issues['duplicate_keys'].append({
                        'language': lang,
                        'value': value,
                        'keys': keys
                    })

    except Exception as e:
        logger.error(f"翻译完整性验证失败: {str(e)}")
        issues['validation_error'] = str(e)

    return issues

# 当模块被导入时初始化
if __name__ != "__main__":
    initialize()