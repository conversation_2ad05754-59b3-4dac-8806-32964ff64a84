#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
国际化(i18n)工具模块 - 辅助工具
用于测试、管理和更新翻译
"""

import os
import sys
import json
import argparse
from collections import defaultdict
import logging
import re

# 添加父目录到路径，确保能够导入i18n模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from engine.utils.i18n import (
    DEFAULT_LANGUAGE, SUPPORTED_LANGUAGES, LOCALE_DIR, 
    load_translations, normalize_language_code, get_translation_coverage,
    export_missing_translations
)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S"
)
logger = logging.getLogger("i18n_tools")

def create_missing_translation_template(output_dir=None):
    """
    为每种语言创建缺失翻译的模板文件
    
    Args:
        output_dir: 输出目录，默认为locales目录
    """
    if not output_dir:
        output_dir = LOCALE_DIR
    
    if not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)
    
    # 获取所有翻译覆盖率
    coverage = get_translation_coverage()
    
    for lang, data in coverage.items():
        if not data['missing_keys']:
            logger.info(f"{lang} 没有缺失的翻译键")
            continue
        
        # 创建模板文件
        template_file = os.path.join(output_dir, f"{lang}_missing.json")
        template = {}
        
        # 获取默认语言的翻译，作为参考
        base_translations = load_translations(DEFAULT_LANGUAGE)
        
        # 添加缺失的键和对应的默认翻译
        for key in data['missing_keys']:
            template[key] = base_translations.get(key, "MISSING")
        
        # 写入文件
        with open(template_file, 'w', encoding='utf-8') as f:
            json.dump(template, f, ensure_ascii=False, indent=4)
        
        logger.info(f"{lang} 缺失翻译模板已创建: {template_file} ({len(template)} 个键)")

def merge_translation_files(template_file, target_file=None):
    """
    合并翻译文件，将模板文件中的翻译合并到目标文件
    
    Args:
        template_file: 包含新翻译的模板文件
        target_file: 目标语言文件，如果为None则自动推断
    """
    # 如果目标文件未指定，尝试从模板文件名推断
    if not target_file:
        filename = os.path.basename(template_file)
        # 尝试从文件名中提取语言代码 (例如: ja-JP_missing.json -> ja-JP.json)
        match = re.match(r'([a-z]{2}-[A-Z]{2}).*\.json', filename)
        if match:
            lang_code = match.group(1)
            target_file = os.path.join(LOCALE_DIR, f"{lang_code}.json")
        else:
            logger.error(f"无法从文件名推断目标语言: {filename}")
            return False
    
    # 确保两个文件都存在
    if not os.path.exists(template_file):
        logger.error(f"模板文件不存在: {template_file}")
        return False
    
    if not os.path.exists(target_file):
        logger.error(f"目标语言文件不存在: {target_file}")
        return False
    
    # 加载文件
    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            template_data = json.load(f)
        
        with open(target_file, 'r', encoding='utf-8') as f:
            target_data = json.load(f)
        
        # 记录初始状态
        original_count = len(target_data)
        
        # 合并翻译
        target_data.update(template_data)
        
        # 计算添加的新键数量
        new_count = len(target_data) - original_count
        
        # 保存合并后的文件
        with open(target_file, 'w', encoding='utf-8') as f:
            json.dump(target_data, f, ensure_ascii=False, indent=2, sort_keys=True)
        
        logger.info(f"已将 {len(template_data)} 个翻译键从 {template_file} 合并到 {target_file}")
        logger.info(f"原始键数: {original_count}, 新增键数: {new_count}, 当前总键数: {len(target_data)}")
        return True
    
    except Exception as e:
        logger.error(f"合并翻译文件时出错: {str(e)}")
        return False

def sort_translation_file(target_file):
    """
    对翻译文件进行排序
    
    Args:
        target_file: 目标语言文件
    """
    if not os.path.exists(target_file):
        logger.error(f"翻译文件不存在: {target_file}")
        return False
    
    try:
        with open(target_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 创建排序后的字典
        sorted_data = {}
        # 按键名排序
        for key in sorted(data.keys()):
            sorted_data[key] = data[key]
        
        # 保存排序后的文件
        with open(target_file, 'w', encoding='utf-8') as f:
            json.dump(sorted_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"已对翻译文件进行排序: {target_file}")
        return True
    
    except Exception as e:
        logger.error(f"排序翻译文件时出错: {str(e)}")
        return False

def scan_source_files(src_dir):
    """
    扫描源代码文件，提取所有翻译键
    
    Args:
        src_dir: 源代码目录
        
    Returns:
        提取到的翻译键集合
    """
    translation_keys = set()
    pattern = r'(?:_|translate|user_log|log)\s*\(\s*[\'"](?:i18n:)?(info\.|error\.|warning\.|[^\'"\s]+\.[^\'"\s]+)[\'"]'
    
    # 递归遍历目录
    for root, _, files in os.walk(src_dir):
        for file in files:
            if not file.endswith('.py'):
                continue
            
            file_path = os.path.join(root, file)
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                # 使用正则表达式查找翻译键
                matches = re.findall(pattern, content)
                for match in matches:
                    # 规范化键 (去除i18n:前缀等)
                    key = match.strip()
                    if key.startswith('i18n:'):
                        key = key[5:]
                    translation_keys.add(key)
            
            except Exception as e:
                logger.warning(f"处理文件时出错: {file_path}, 错误: {str(e)}")
    
    return translation_keys

def update_translation_files(src_dir, update_all=False):
    """
    更新所有翻译文件，添加缺失的键
    
    Args:
        src_dir: 源代码目录
        update_all: 是否更新所有语言文件
    """
    # 扫描源代码，获取所有翻译键
    logger.info(f"扫描源代码目录: {src_dir}")
    source_keys = scan_source_files(src_dir)
    logger.info(f"从源代码中提取到 {len(source_keys)} 个翻译键")
    
    # 加载默认语言文件
    default_translations = load_translations(DEFAULT_LANGUAGE)
    default_keys = set(default_translations.keys())
    
    # 找出在源代码中使用但在翻译文件中不存在的键
    missing_in_default = source_keys - default_keys
    
    if missing_in_default:
        logger.info(f"在默认语言翻译文件中找不到 {len(missing_in_default)} 个键")
        
        # 创建用于更新默认语言的补充文件
        supplement_file = os.path.join(LOCALE_DIR, f"{DEFAULT_LANGUAGE}_supplement.json")
        supplement = {}
        for key in missing_in_default:
            supplement[key] = f"TRANSLATE_ME: {key}"
        
        with open(supplement_file, 'w', encoding='utf-8') as f:
            json.dump(supplement, f, ensure_ascii=False, indent=2)
        
        logger.info(f"已创建默认语言补充文件: {supplement_file}")
        
        # 合并到默认语言文件
        default_file = os.path.join(LOCALE_DIR, f"{DEFAULT_LANGUAGE}.json")
        merge_translation_files(supplement_file, default_file)
        
        # 重新加载默认语言翻译
        default_translations = load_translations(DEFAULT_LANGUAGE)
        default_keys = set(default_translations.keys())
    
    # 如果需要更新所有语言文件
    if update_all:
        for lang in SUPPORTED_LANGUAGES:
            if lang == DEFAULT_LANGUAGE:
                continue
            
            lang_file = os.path.join(LOCALE_DIR, f"{lang}.json")
            
            # 如果语言文件不存在，创建空文件
            if not os.path.exists(lang_file):
                with open(lang_file, 'w', encoding='utf-8') as f:
                    json.dump({}, f, ensure_ascii=False, indent=2)
                logger.info(f"创建新语言文件: {lang_file}")
            
            # 加载语言文件
            lang_translations = load_translations(lang)
            lang_keys = set(lang_translations.keys())
            
            # 找出在默认语言中存在但在当前语言中不存在的键
            missing_in_lang = default_keys - lang_keys
            
            if missing_in_lang:
                logger.info(f"在 {lang} 语言文件中找不到 {len(missing_in_lang)} 个键")
                
                # 创建用于更新该语言的补充文件
                supplement_file = os.path.join(LOCALE_DIR, f"{lang}_supplement.json")
                supplement = {}
                for key in missing_in_lang:
                    # 添加默认语言的翻译作为参考
                    supplement[key] = f"TRANSLATE_ME: {default_translations.get(key, key)}"
                
                with open(supplement_file, 'w', encoding='utf-8') as f:
                    json.dump(supplement, f, ensure_ascii=False, indent=2)
                
                logger.info(f"已创建 {lang} 语言补充文件: {supplement_file}")
                
                # 合并到语言文件
                merge_translation_files(supplement_file, lang_file)

def analyze_translation_status():
    """
    分析并输出翻译状态报告
    """
    logger.info("翻译状态分析开始")
    
    # 加载默认语言翻译
    default_translations = load_translations(DEFAULT_LANGUAGE)
    default_keys = set(default_translations.keys())
    logger.info(f"默认语言 ({DEFAULT_LANGUAGE}) 包含 {len(default_keys)} 个翻译键")
    
    # 分析各语言的翻译覆盖率
    logger.info("语言翻译覆盖率:")
    
    summary = []
    for lang in SUPPORTED_LANGUAGES:
        if lang == DEFAULT_LANGUAGE:
            summary.append({
                "语言": lang,
                "翻译键数": len(default_keys),
                "覆盖率": "100%",
                "状态": "基准语言"
            })
            continue
        
        lang_translations = load_translations(lang)
        lang_keys = set(lang_translations.keys())
        
        # 计算覆盖率
        covered = len(lang_keys.intersection(default_keys))
        total = len(default_keys)
        coverage = round(covered / total * 100, 2) if total > 0 else 0
        
        # 检查状态
        if coverage == 100:
            status = "完整覆盖"
        elif coverage >= 90:
            status = "接近完成"
        elif coverage >= 50:
            status = "部分完成"
        else:
            status = "需要补充"
        
        summary.append({
            "语言": lang,
            "翻译键数": len(lang_keys),
            "覆盖率": f"{coverage}%",
            "状态": status,
            "缺失键数": len(default_keys - lang_keys)
        })
    
    # 输出摘要表格
    logger.info(f"{'语言':8} | {'翻译键数':8} | {'覆盖率':8} | {'状态':10} | {'缺失键数':8}")
    logger.info("-" * 60)
    for item in summary:
        if "缺失键数" in item:
            logger.info(f"{item['语言']:8} | {item['翻译键数']:8} | {item['覆盖率']:8} | {item['状态']:10} | {item['缺失键数']:8}")
        else:
            logger.info(f"{item['语言']:8} | {item['翻译键数']:8} | {item['覆盖率']:8} | {item['状态']:10} |")
    
    return summary

def main():
    """主函数，解析命令行参数并执行相应功能"""
    parser = argparse.ArgumentParser(description="国际化(i18n)翻译管理工具")
    
    # 子命令
    subparsers = parser.add_subparsers(dest='command', help='子命令')
    
    # 分析翻译状态
    analyze_parser = subparsers.add_parser('analyze', help='分析翻译状态')
    
    # 创建缺失翻译模板
    template_parser = subparsers.add_parser('template', help='创建缺失翻译模板')
    template_parser.add_argument('--output-dir', help='输出目录', default=LOCALE_DIR)
    
    # 合并翻译文件
    merge_parser = subparsers.add_parser('merge', help='合并翻译文件')
    merge_parser.add_argument('template', help='包含新翻译的模板文件')
    merge_parser.add_argument('--target', help='目标语言文件')
    
    # 排序翻译文件
    sort_parser = subparsers.add_parser('sort', help='对翻译文件进行排序')
    sort_parser.add_argument('file', help='要排序的翻译文件')
    
    # 更新翻译文件
    update_parser = subparsers.add_parser('update', help='更新翻译文件')
    update_parser.add_argument('--src-dir', help='源代码目录', default='engine')
    update_parser.add_argument('--all', action='store_true', help='更新所有语言文件')
    
    # 导出缺失翻译
    export_parser = subparsers.add_parser('export', help='导出缺失翻译')
    export_parser.add_argument('--language', help='语言代码')
    export_parser.add_argument('--output', help='输出文件路径')
    
    # 解析参数
    args = parser.parse_args()
    
    # 根据命令执行相应功能
    if args.command == 'analyze':
        analyze_translation_status()
    
    elif args.command == 'template':
        create_missing_translation_template(args.output_dir)
    
    elif args.command == 'merge':
        merge_translation_files(args.template, args.target)
    
    elif args.command == 'sort':
        sort_translation_file(args.file)
    
    elif args.command == 'update':
        update_translation_files(args.src_dir, args.all)
    
    elif args.command == 'export':
        export_missing_translations(args.output)
    
    else:
        parser.print_help()

if __name__ == "__main__":
    main() 