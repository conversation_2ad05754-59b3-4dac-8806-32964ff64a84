#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
接口映射工具模块
提供接口名称映射相关的工具函数
"""

import json
import os
from engine.utils.logger import log, _


def load_interface_mapping(mapping_file_path):
    """
    加载接口映射文件
    
    Args:
        mapping_file_path (str): 映射文件路径
        
    Returns:
        dict: 接口映射字典，如果加载失败返回空字典
    """
    try:
        if not os.path.exists(mapping_file_path):
            log(_("warning.interface_mapping_file_not_found", file=mapping_file_path), "warning")
            return {}
        
        with open(mapping_file_path, 'r', encoding='utf-8') as f:
            mapping_data = json.load(f)
        
        log("Interface mapping loaded", "info")
        return mapping_data
    
    except Exception as e:
        log(_("error.interface_mapping_load_failed", file=mapping_file_path, error=str(e)), "error")
        return {}


def save_interface_mapping(mapping_data, mapping_file_path):
    """
    保存接口映射到文件
    
    Args:
        mapping_data (dict): 接口映射数据
        mapping_file_path (str): 映射文件路径
        
    Returns:
        bool: 是否保存成功
    """
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(mapping_file_path), exist_ok=True)
        
        with open(mapping_file_path, 'w', encoding='utf-8') as f:
            json.dump(mapping_data, f, ensure_ascii=False, indent=2)
        
        log(_("info.interface_mapping_saved", file=mapping_file_path, count=len(mapping_data)))
        return True
    
    except Exception as e:
        log(_("error.interface_mapping_save_failed", file=mapping_file_path, error=str(e)), "error")
        return False


def get_mapped_interface_name(original_name, interface_mapping):
    """
    获取映射后的接口名称
    
    统一的接口名称映射函数，用于策略和NAT渲染模块
    
    Args:
        original_name (str): 原始Fortigate接口名称
        interface_mapping (dict): 接口映射字典（来自真正的接口映射文件）
        
    Returns:
        str: 映射后的NTOS接口名称，如果未找到映射则返回原始名称并记录警告
    """
    if not original_name:
        return original_name
    
    # 清理接口名称（去除引号等）
    clean_name = str(original_name).strip('"').strip("'")
    
    if not interface_mapping:
        log("接口映射表为空", "warning")
        return clean_name
    
    # 直接查找映射
    if clean_name in interface_mapping:
        mapping_data = interface_mapping[clean_name]

        # 如果映射数据是字典，提取name字段
        if isinstance(mapping_data, dict):
            mapped_name = mapping_data.get("name", clean_name)
            log(f"接口映射成功: {clean_name} -> {mapped_name}", "debug")
            return mapped_name
        else:
            # 如果是字符串，直接返回
            log(f"接口映射成功: {clean_name} -> {mapping_data}", "debug")
            return mapping_data

    # 如果没有找到映射，记录警告并返回原始名称
    # 简化警告消息，避免复杂的参数格式化
    log(f"未找到接口 {clean_name} 的映射，使用原始名称", "warning")
    return clean_name


def create_final_interface_mapping(processed_interfaces):
    """
    根据处理后的接口列表创建最终的接口映射表
    只包含有用户映射的接口和相关子接口

    规则：
    1. 只保存有用户提供映射的接口（映射后名称与原始名称不同）
    2. 子接口例外：如果父接口有映射，则子接口也保存映射
    3. 不保存没有映射关系的接口（避免自映射如port2->port2）

    Args:
        processed_interfaces (dict): 处理后的接口字典，键为原始名称，值为接口配置

    Returns:
        dict: 最终的接口映射表，格式为 {原始名称: 映射后名称}
    """
    final_mapping = {}

    # 第一步：处理有用户映射的接口
    for original_name, interface_config in processed_interfaces.items():
        if isinstance(interface_config, dict) and "name" in interface_config:
            mapped_name = interface_config["name"]

            # 只保存有真正映射关系的接口（映射后名称与原始名称不同）
            if mapped_name and mapped_name != original_name:
                final_mapping[original_name] = mapped_name
                log(_("info.interface_mapping_created", original=original_name, mapped=mapped_name), "debug")

    # 第二步：处理子接口（如果父接口有映射）
    for original_name, interface_config in processed_interfaces.items():
        if isinstance(interface_config, dict):
            interface_type = interface_config.get("type", "unknown")

            # 处理VLAN子接口
            if interface_type == "vlan" or interface_config.get("is_subinterface"):
                parent_interface = interface_config.get("parent_interface")
                vlan_id = interface_config.get("vlan_id")
                mapped_name = interface_config.get("name")

                if parent_interface and vlan_id:
                    # 检查父接口是否在映射中
                    if parent_interface in final_mapping:
                        parent_mapped = final_mapping[parent_interface]
                        # 如果子接口没有预设的映射名称，则生成一个
                        if not mapped_name or mapped_name == original_name:
                            generated_name = f"{parent_mapped}.{vlan_id}"
                            final_mapping[original_name] = generated_name
                        else:
                            # 使用预设的映射名称
                            final_mapping[original_name] = mapped_name

    log(_("info.final_interface_mapping_created", count=len(final_mapping)))
    return final_mapping


def save_final_interface_mapping(processed_interfaces, output_dir="data"):
    """
    保存最终的接口映射文件
    
    Args:
        processed_interfaces (dict): 处理后的接口字典
        output_dir (str): 输出目录
        
    Returns:
        str: 保存的映射文件路径，如果保存失败返回None
    """
    try:
        # 创建最终映射表
        final_mapping = create_final_interface_mapping(processed_interfaces)
        
        if not final_mapping:
            log(_("warning.no_interface_mapping_to_save"), "warning")
            return None
        
        # 确定保存路径
        mapping_file_path = os.path.join(output_dir, "interface_mapping.json")
        
        # 保存映射文件
        if save_interface_mapping(final_mapping, mapping_file_path):
            log(_("info.final_interface_mapping_saved", file=mapping_file_path))
            return mapping_file_path
        else:
            return None
    
    except Exception as e:
        log(_("error.save_final_interface_mapping_failed", error=str(e)), "error")
        return None


def get_interface_zone_mapping(interface_mapping):
    """
    根据接口映射创建接口到区域的映射

    Args:
        interface_mapping (dict): 接口映射字典

    Returns:
        dict: 接口到区域的映射，格式为 {接口名: 区域名}
    """
    interface_to_zone = {}

    for original_name, interface_data in interface_mapping.items():
        if isinstance(interface_data, dict):
            # 从接口数据中提取区域信息
            zone = interface_data.get("role", interface_data.get("zone"))
            interface_name = interface_data.get("name", original_name)

            if zone:
                # 原始名称到区域的映射
                interface_to_zone[original_name] = zone
                # 映射后名称到区域的映射
                interface_to_zone[interface_name] = zone

    return interface_to_zone


def identify_subinterface_type(interface_name):
    """
    识别子接口类型

    Args:
        interface_name (str): 接口名称

    Returns:
        dict: 子接口信息，包含type, parent, identifier等
    """
    import re
    from engine.utils.i18n import _

    if not interface_name:
        return None

    # VLAN子接口：port1.10, Ge0/1.20
    vlan_match = re.match(r'^([^.]+)\.(\d+)$', interface_name)
    if vlan_match:
        return {
            'type': 'vlan',
            'parent': vlan_match.group(1),
            'vlan_id': vlan_match.group(2),
            'identifier': vlan_match.group(2)
        }

    # PPP接口：testppp91, pppoe1, ppp-wan1
    ppp_match = re.match(r'^(test)?pp(p|poe)(\d+)$', interface_name, re.IGNORECASE)
    if ppp_match:
        ppp_id = ppp_match.group(3)
        return {
            'type': 'ppp',
            'parent': None,  # PPP接口通常没有明确的父接口
            'ppp_id': ppp_id,
            'identifier': ppp_id
        }

    # 隧道接口：tunnel1, tun0
    tunnel_match = re.match(r'^(tunnel|tun)(\d+)$', interface_name, re.IGNORECASE)
    if tunnel_match:
        tunnel_id = tunnel_match.group(2)
        return {
            'type': 'tunnel',
            'parent': None,
            'tunnel_id': tunnel_id,
            'identifier': tunnel_id
        }

    # 环回接口：loopback1, lo0
    loopback_match = re.match(r'^(loopback|lo)(\d+)$', interface_name, re.IGNORECASE)
    if loopback_match:
        lo_id = loopback_match.group(2)
        return {
            'type': 'loopback',
            'parent': None,
            'loopback_id': lo_id,
            'identifier': lo_id
        }

    return None


def generate_ntos_subinterface_name(subintf_info, interface_mapping=None):
    """
    根据子接口信息生成NTOS格式的接口名称

    Args:
        subintf_info (dict): 子接口信息
        interface_mapping (dict): 接口映射字典

    Returns:
        str: NTOS格式的接口名称
    """
    if not subintf_info:
        return None

    subintf_type = subintf_info.get('type')
    identifier = subintf_info.get('identifier')

    if subintf_type == 'vlan':
        # VLAN子接口：需要映射父接口
        parent = subintf_info.get('parent')
        vlan_id = subintf_info.get('vlan_id')

        if parent and interface_mapping and parent in interface_mapping:
            # 获取父接口的映射名称
            parent_mapping = interface_mapping[parent]
            if isinstance(parent_mapping, dict):
                mapped_parent = parent_mapping.get('name', parent)
            else:
                mapped_parent = parent_mapping

            return f"{mapped_parent}.{vlan_id}"
        elif parent:
            # 如果父接口没有映射，使用原始父接口名称
            return f"{parent}.{vlan_id}"
        else:
            return None

    elif subintf_type == 'ppp':
        # PPP接口：生成标准的PPP接口名称
        ppp_id = subintf_info.get('ppp_id')
        return f"ppp{ppp_id}"

    elif subintf_type == 'tunnel':
        # 隧道接口：生成标准的隧道接口名称
        tunnel_id = subintf_info.get('tunnel_id')
        return f"tunnel{tunnel_id}"

    elif subintf_type == 'loopback':
        # 环回接口：生成标准的环回接口名称
        lo_id = subintf_info.get('loopback_id')
        return f"loopback{lo_id}"

    return None


def get_mapped_interface_or_zone(original_name, interface_mapping, interface_zone_mapping=None):
    """
    获取映射后的接口名称或区域名称

    用于NAT配置：如果接口在接口数据中存在则使用映射后的接口名称，否则使用区域名称

    Args:
        original_name (str): 原始Fortigate接口名称
        interface_mapping (dict): 接口映射字典（来自真正的接口映射文件）
        interface_zone_mapping (dict): 接口到区域的映射，如果为None则自动生成

    Returns:
        str: 映射后的接口名称或区域名称
    """
    if not original_name:
        return original_name

    # 清理接口名称（去除引号等）
    clean_name = str(original_name).strip('"').strip("'")

    if not interface_mapping:
        log("接口映射表为空", "warning")
        return clean_name

    # 检查接口是否在接口映射中存在
    if clean_name in interface_mapping:
        mapping_data = interface_mapping[clean_name]

        # 如果映射数据是字典，提取name字段
        if isinstance(mapping_data, dict):
            mapped_name = mapping_data.get("name", clean_name)
            log(_("info.interface_found_using_mapped_name", original=clean_name, mapped=mapped_name), "debug")
            return mapped_name
        else:
            # 如果是字符串，直接返回
            log(_("info.interface_found_using_mapped_name", original=clean_name, mapped=mapping_data), "debug")
            return mapping_data

    # 如果接口不在映射中，尝试使用区域名称
    if interface_zone_mapping is None:
        interface_zone_mapping = get_interface_zone_mapping(interface_mapping)

    # 检查是否有区域映射
    if clean_name in interface_zone_mapping:
        zone_name = interface_zone_mapping[clean_name]
        log(_("info.interface_not_found_using_zone", original=clean_name, zone=zone_name), "debug")
        return zone_name

    # 默认区域映射：包含wan的为untrust，其他为trust
    if "wan" in clean_name.lower():
        default_zone = "untrust"
    else:
        default_zone = "trust"

    log(_("warning.interface_not_found_using_default_zone", interface=clean_name, zone=default_zone), "warning")
    return default_zone
