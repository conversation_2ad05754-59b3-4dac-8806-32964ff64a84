#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
接口处理工具函数模块
提供接口验证、掩码处理等共享功能
"""

import os
import sys

# 添加父目录到Python路径，确保可以正确导入engine模块
parent_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from engine.utils.logger import log, user_log
from engine.utils.i18n import _

# 物理接口类型列表
PHYSICAL_INTERFACE_TYPES = [
    "physical"  # 只有physical是物理接口
]

def is_physical_interface(interface_name, interface_type, interface_config):
    """
    判断接口是否为物理接口

    Args:
        interface_name (str): 接口名称
        interface_type (str): 接口类型
        interface_config (dict): 接口配置字典

    Returns:
        bool: 如果是物理接口返回True，否则返回False
    """
    # Modem接口是逻辑接口，直接过滤掉
    if interface_name and interface_name.lower() == 'modem':
        return False

    # 检查接口类型
    if interface_type in PHYSICAL_INTERFACE_TYPES:
        return True

    # 检查是否有父接口（子接口）- 兼容不同字段名
    if interface_config.get('interface') or interface_config.get('parent_interface'):
        return False

    # 检查是否有VLAN ID（子接口）
    if interface_config.get('vlanid'):
        return False

    # 检查是否标记为子接口
    if interface_config.get('is_subinterface'):
        return False

    return False

def validate_pppoe_interface(interface):
    """
    验证PPPoE接口的完整性

    Args:
        interface (dict): 接口配置字典

    Returns:
        bool: 如果PPPoE接口配置完整返回True，否则返回False
    """
    # 检查是否为PPPoE模式
    if interface.get('mode') != 'pppoe':
        return True  # 非PPPoE接口，无需验证

    # 获取接口名称，兼容不同的字段名
    interface_name = interface.get('name', '') or interface.get('raw_name', '')
    interface_type = interface.get('type', '')
    is_physical = is_physical_interface(interface_name, interface_type, interface)

    # 如果不是物理接口，无需验证PPPoE认证信息
    if not is_physical:
        return True

    # 只对接口名称为"modem"的物理PPPoE接口进行认证信息验证
    if interface_name.lower() != 'modem':
        return True  # 非modem接口，跳过验证

    # 对于名称为"modem"的物理PPPoE接口，检查是否有用户名和密码
    has_username = bool(interface.get('username') or interface.get('pppoe_username'))
    has_password = bool(interface.get('password') or interface.get('pppoe_password'))

    # modem接口必须同时有用户名和密码
    if not (has_username and has_password):
        # 记录详细的过滤信息
        missing_fields = []
        if not has_username:
            missing_fields.append("用户名")
        if not has_password:
            missing_fields.append("密码")

        log(_("warning.pppoe_missing_credentials"), "warning",
            interface=interface_name,
            missing_fields=", ".join(missing_fields))
        user_log(_("warning.pppoe_interface_filtered"), "warning",
                interface=interface_name,
                missing_fields=", ".join(missing_fields))
        return False

    return True

def mask_interface_passwords(interface):
    """
    掩码接口数据中的密码字段
    
    Args:
        interface (dict): 接口配置字典
        
    Returns:
        dict: 掩码处理后的接口配置字典
    """
    if not isinstance(interface, dict):
        return interface
    
    # 需要掩码的密码字段列表
    password_fields = ['password', 'pppoe_password', 'pppoe-password']
    
    # 创建接口数据的副本，避免修改原始数据
    masked_interface = interface.copy()
    
    # 对密码字段进行掩码处理
    for field in password_fields:
        if field in masked_interface and isinstance(masked_interface[field], str) and masked_interface[field]:
            masked_interface[field] = '******'
    
    return masked_interface

def process_interfaces_with_pppoe_filtering_and_masking(interfaces):
    """
    处理接口列表，应用PPPoE过滤和密码掩码
    
    Args:
        interfaces (list): 接口配置列表
        
    Returns:
        tuple: (处理后的接口列表, 被过滤的PPPoE接口数量)
    """
    if not isinstance(interfaces, list):
        return interfaces, 0
    
    filtered_interfaces = []
    filtered_pppoe_count = 0
    
    for interface in interfaces:
        # 验证PPPoE接口完整性
        if validate_pppoe_interface(interface):
            # 对密码进行掩码处理
            masked_interface = mask_interface_passwords(interface)
            filtered_interfaces.append(masked_interface)
        else:
            # 如果是被过滤的PPPoE接口，增加计数
            interface_name = interface.get('name', '') or interface.get('raw_name', '')
            if (interface.get('mode') == 'pppoe' and
                is_physical_interface(interface_name,
                                    interface.get('type', ''),
                                    interface)):
                filtered_pppoe_count += 1
    
    return filtered_interfaces, filtered_pppoe_count
