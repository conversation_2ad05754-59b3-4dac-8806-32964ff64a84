#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import json
import re
from typing import List, Dict, Tuple, Optional
from engine.utils.i18n import _  # 导入国际化工具类

# 设备接口定义文件路径
DEVICE_INTERFACES_FILE = os.path.join(os.path.dirname(os.path.dirname(__file__)), 
                                     "data", "device_interfaces.json")

def load_device_interfaces() -> Dict:
    """
    加载设备接口定义文件
    
    Returns:
        Dict: 设备接口定义字典
    """
    try:
        with open(DEVICE_INTERFACES_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(_("validator.interface_error_load_failed", error=str(e)))
        return {}

def get_supported_interfaces(model: str) -> List[str]:
    """
    获取指定设备型号支持的接口列表
    
    Args:
        model (str): 设备型号，如 'z5100s'
        
    Returns: List[str]: 支持的接口列表，如果设备型号不存在，则返回空列表
    """
    device_interfaces = load_device_interfaces()
    model = model.lower()  # 转换为小写，确保匹配不区分大小写
    
    if model in device_interfaces:
        return device_interfaces[model].get("interfaces", [])
    return []

def validate_interface(interface: str, model: str) -> Tuple[bool, Optional[str]]:
    """
    验证接口是否在指定设备型号支持的接口列表中，支持子接口验证
    
    Args:
        interface (str): 接口名称，可以是物理接口（如Ge0/0）或子接口（如Ge0/0.10）
        model (str): 设备型号，如 'z5100s'
        
    Returns: Tuple[bool, Optional[str]]: (是否有效, 错误原因)，如果有效，错误原因为None
    """
    supported_interfaces = get_supported_interfaces(model)
    
    if not supported_interfaces:
        return False, _("validator.interface_error_model_not_found", model=model)
    
    # 检查是否是子接口（包含点号）
    if "." in interface:
        # 提取父接口和VLAN ID
        match = re.match(r'([^.]+)\.(\d+)', interface)
        if not match:
            return False, _("validator.interface_error_subinterface_format", interface=interface)
        
        parent_interface = match.group(1)
        vlan_id = int(match.group(2))
        
        # 验证父接口是否在支持的接口列表中
        if parent_interface not in supported_interfaces:
            return False, _("validator.interface_error_parent_not_supported", interface=interface, parent_interface=parent_interface, model=model)
        
        # 验证VLAN ID是否在有效范围内（1-4063）
        if not (1 <= vlan_id <= 4063):
            return False, _("validator.interface_error_vlan_id_range", interface=interface, vlan_id=vlan_id)
        
        return True, None
    else:
        # 物理接口验证
        if interface not in supported_interfaces:
            return False, _("validator.interface_error_not_supported", interface=interface, model=model)
        
        return True, None

def is_valid_vlan_id(vlan_id: int) -> bool:
    """
    验证VLAN ID是否在有效范围内（1-4063）
    
    Args:
        vlan_id (int): VLAN ID
        
    Returns:
        bool: 是否有效
    """
    return 1 <= vlan_id <= 4063 