#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import logging
import datetime
import time
import random
import uuid
import sys
import json
import re
import string
from .encoding_fix import setup_encoding
from logging.handlers import RotatingFileHandler

# 确保编码设置正确
setup_encoding()

# 全局日志配置
LOG_LEVEL = logging.INFO
LOG_FORMAT = '%(asctime)s - %(levelname)s - %(message)s'
DATE_FORMAT = '%Y-%m-%d %H:%M:%S'

# 全局配置：是否掩码Base64编码内容
MASK_BASE64_IN_LOGS = True

# 日志级别映射
LOG_LEVELS = {
    'debug': logging.DEBUG,
    'info': logging.INFO,
    'warning': logging.WARNING,
    'error': logging.ERROR,
    'critical': logging.CRITICAL
}

# 全局配置：是否跳过包含敏感信息的日志（设为True可跳过包含BASE64等敏感信息的日志）
SKIP_SENSITIVE_LOGS = True

# 常见的统计参数列表，用于提供默认值
COMMON_STATS = [
    # 通用统计
    'total', 'success', 'failed', 'skipped', 'processed', 'converted', 'count',
    # NAT相关统计
    'nat_rules', 'nat_pools', 'dnat_rules', 'snat_rules',
    'nat_pools_created', 'dnat_rules_created', 'snat_rules_created',
    # 策略相关统计
    'policies', 'policy_rules', 'vips', 'ippools',
    # 接口相关统计
    'interfaces', 'interface_count',
    # 对象相关统计
    'address_objects', 'service_objects', 'service_groups',
    # 时间对象相关统计
    'time_ranges', 'time_objects'
]

def fix_translation_params(key, kwargs):
    """修复翻译参数名不匹配问题

    Args:
        key (str): 翻译键
        kwargs (dict): 传递给翻译函数的参数

    Returns:
        dict: 修复后的参数字典
    """
    # 参数映射规则：当翻译键需要某个参数但调用时传递了其他名称时的映射
    param_mapping = {
        'raw_name': ['name', 'interface', 'interface_name'],
        'name': ['raw_name', 'interface', 'interface_name', 'target'],
        'mapped_name': ['target', 'target_interface', 'ntos_name'],
        'parent': ['parent_interface', 'parent_name'],
        'original_name': ['name', 'item_name', 'raw_name', 'interface_name'],
    }

    # 获取翻译键的实际内容来检查需要哪些参数
    try:
        # 尝试导入翻译函数来获取翻译内容
        from engine.utils.i18n import get_translation_content
        translation_content = get_translation_content(key)
    except:
        # 如果无法获取翻译内容，使用预定义的映射
        translation_content = None

        # 根据翻译键名称推断需要的参数
        if key in [
            'info.interface_mapped', 'info.interface_mapping', 'info.processing_interface',
            'info.subinterface_mapped', 'info.subinterface_mapping',
            'interface_handler.subinterface_mapped', 'warning.skipping_invalid_interface',
            'warning.subinterface_missing_parent_or_vlanid_skipped',
            'warning.subinterface_parent_not_mapped', 'warning.subinterface_parent_not_mapped_skipped',
            'yang.interface_missing_name', 'yang.interface_name_mapping'
        ]:
            translation_content = '{raw_name}'  # 这些键需要raw_name参数
        elif key in [
            'user.item.success_with_mapping', 'user.item.success', 'user.item.failed', 'user.item.warning'
        ]:
            translation_content = '{original_name}'  # 这些用户日志键需要original_name参数

    # 检查翻译内容，确定需要哪些参数
    if translation_content and isinstance(translation_content, str):
        # 检查是否需要raw_name参数
        if '{raw_name}' in translation_content and 'raw_name' not in kwargs:
            # 尝试从其他参数名映射
            for alt_name in param_mapping.get('raw_name', []):
                if alt_name in kwargs:
                    kwargs['raw_name'] = kwargs[alt_name]
                    break

        # 检查是否需要name参数
        if '{name}' in translation_content and 'name' not in kwargs:
            # 特殊处理：对于yang.interface_name_mapping，target应该优先映射到name
            if key == 'yang.interface_name_mapping' and 'target' in kwargs:
                kwargs['name'] = kwargs['target']
            else:
                # 尝试从其他参数名映射
                for alt_name in param_mapping.get('name', []):
                    if alt_name in kwargs:
                        kwargs['name'] = kwargs[alt_name]
                        break

        # 检查是否需要mapped_name参数
        if '{mapped_name}' in translation_content and 'mapped_name' not in kwargs:
            # 尝试从其他参数名映射
            for alt_name in param_mapping.get('mapped_name', []):
                if alt_name in kwargs:
                    kwargs['mapped_name'] = kwargs[alt_name]
                    break

        # 检查是否需要original_name参数
        if '{original_name}' in translation_content and 'original_name' not in kwargs:
            # 尝试从其他参数名映射
            for alt_name in param_mapping.get('original_name', []):
                if alt_name in kwargs:
                    kwargs['original_name'] = kwargs[alt_name]
                    break

        # 检查是否需要parent参数
        if '{parent}' in translation_content and 'parent' not in kwargs:
            # 尝试从其他参数名映射
            for alt_name in param_mapping.get('parent', []):
                if alt_name in kwargs:
                    kwargs['parent'] = kwargs[alt_name]
                    break

    return kwargs

# 确保日志目录存在
log_dir = 'logs'
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

# 生成唯一标识符，结合时间戳和随机UUID确保并行运行时文件名不冲突
timestamp = int(time.time())
unique_id = str(uuid.uuid4())[:8]  # 取UUID的前8位作为唯一标识
log_file = os.path.join(log_dir, f'converter_{timestamp}_{unique_id}.log')

# 全局静默模式标志
QUIET_MODE = False

# 当前日志目录
current_log_directory = None

# 创建日志器
logger = logging.getLogger('config_converter')
logger.setLevel(LOG_LEVEL)

# 创建用户日志器（不包含敏感信息）
user_logger = logging.getLogger('user_logger')
user_logger.setLevel(logging.INFO)

# 创建完整日志器（包含所有信息，带标记区分用户日志和系统日志）
full_logger = logging.getLogger('full_logger')
full_logger.setLevel(logging.DEBUG)

# 确保处理器不重复添加
if logger.handlers:
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

if user_logger.handlers:
    for handler in user_logger.handlers[:]:
        user_logger.removeHandler(handler)

if full_logger.handlers:
    for handler in full_logger.handlers[:]:
        full_logger.removeHandler(handler)

# 创建文件处理器
file_handler = logging.FileHandler(log_file, encoding='utf-8')
file_handler.setLevel(LOG_LEVEL)

# 创建控制台处理器
console_handler = logging.StreamHandler(sys.stderr)
console_handler.setLevel(logging.INFO)

# 创建日志格式
formatter = logging.Formatter(LOG_FORMAT, DATE_FORMAT)
file_handler.setFormatter(formatter)
console_handler.setFormatter(formatter)

# 添加处理器到日志记录器
logger.addHandler(file_handler)
logger.addHandler(console_handler)

# 默认日志文件
default_log_file = None
default_user_log_file = None
default_full_log_file = None

# 国际化翻译器实例 - 延迟初始化
_translator = None

# 控制是否允许直接打印输出
ALLOW_DIRECT_PRINT = False

def safe_print(message):
    """安全打印，只有在允许时才输出到控制台
    
    Args:
        message: 要打印的消息
    """
    if ALLOW_DIRECT_PRINT:
        print(message)

def setup_logging(log_dir=None, log_to_stdout=True, log_level=logging.INFO, quiet=False):
    """设置日志系统

    Args:
        log_dir: 日志保存目录，如果为None则不保存到文件
        log_to_stdout: 是否输出到标准输出
        log_level: 日志级别
        quiet: 是否静默模式，如果为True，则忽略log_to_stdout参数
    """
    global logger, default_log_file, QUIET_MODE, current_log_directory
    
    # 设置全局静默模式
    QUIET_MODE = quiet
    
    # 设置日志级别
    logger.setLevel(log_level)
    
    # 清除现有的处理器
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # 格式化器
    formatter = logging.Formatter(LOG_FORMAT, DATE_FORMAT)
    
    if log_dir:
        # 确保日志目录存在
        os.makedirs(log_dir, exist_ok=True)
        # 设置全局日志目录
        current_log_directory = log_dir
        

        
        # 信息日志 - INFO及以上
        info_log_file = os.path.join(log_dir, 'info.log')
        info_handler = logging.FileHandler(info_log_file, encoding='utf-8', mode='w')
        info_handler.setLevel(logging.INFO)
        info_handler.setFormatter(formatter)
        logger.addHandler(info_handler)
        
        # 错误日志 - WARNING及以上
        error_log_file = os.path.join(log_dir, 'error.log')
        error_handler = logging.FileHandler(error_log_file, encoding='utf-8', mode='w')
        error_handler.setLevel(logging.WARNING)
        error_handler.setFormatter(formatter)
        logger.addHandler(error_handler)

        # 调试日志 - DEBUG及以上（包含所有级别的日志）
        debug_log_file = os.path.join(log_dir, 'debug.log')
        debug_handler = logging.FileHandler(debug_log_file, encoding='utf-8', mode='w')
        debug_handler.setLevel(logging.DEBUG)
        debug_handler.setFormatter(formatter)
        logger.addHandler(debug_handler)

        # 用户日志 - 专门的用户可见信息
        user_log_file = os.path.join(log_dir, 'conversion_summary.log')
        default_log_file = debug_log_file
        
        # 设置用户日志
        setup_user_logging(user_log_file)
    
    # 如果需要，添加标准输出处理器
    if log_to_stdout and not quiet:
        console_handler = logging.StreamHandler(sys.stderr)  # 使用stderr而不是stdout
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
    
    return default_log_file

def setup_file_logging(log_file=None):
    """设置文件日志
    
    Args:
        log_file: 日志文件路径，如果为None则使用默认路径
    """
    if not log_file:
        # 默认日志文件路径
        log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'logs')
        os.makedirs(log_dir, exist_ok=True)
        log_file = os.path.join(log_dir, 'debug.log')
    
    # 创建文件处理器
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(LOG_LEVEL)
    file_handler.setFormatter(logging.Formatter(LOG_FORMAT, DATE_FORMAT))
    
    # 添加到日志记录器
    logger.addHandler(file_handler)
    
    return log_file

def setup_user_logging(log_file=None):
    """设置用户日志
    
    Args:
        log_file: 用户日志文件路径，如果为None则使用默认路径
    """
    global default_user_log_file, logger
    
    if not log_file:
        # 默认用户日志文件路径
        log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'logs')
        os.makedirs(log_dir, exist_ok=True)
        # 移除时间戳，使用固定的文件名
        log_file = os.path.join(log_dir, 'conversion_summary.log')
    
    # 确保日志目录存在
    log_dir = os.path.dirname(log_file)
    if not os.path.exists(log_dir):
        try:
            os.makedirs(log_dir, exist_ok=True)
            logger.debug(handle_i18n_log(_("info.create_user_log_dir"), log_dir=log_dir))
        except Exception as e:
            logger.error(handle_i18n_log(_("error.create_user_log_dir_failed"), log_dir=log_dir, error=str(e)))
    
    # 创建文件处理器，使用 'w' 模式确保文件被清空并重新写入，明确指定 UTF-8 编码
    file_handler = logging.FileHandler(log_file, encoding='utf-8', mode='w')
    file_handler.setLevel(logging.INFO)
    
    # 创建标准格式化器
    standard_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s', DATE_FORMAT)
    
    # 创建自定义格式化器，处理不同类型的日志
    class UserLogFormatter(logging.Formatter):
        def format(self, record):
            # 如果是总结信息，使用不同的格式
            if hasattr(record, 'summary') and record.summary:
                # 尝试获取语言设置
                language = None
                if hasattr(record, 'language'):
                    language = record.language
                
                # 尝试使用国际化模块
                try:
                    from .i18n import _
                    # 总结信息不需要时间戳和级别，使用格式化标题
                    message = record.getMessage()
                    return f"\n=== {message} ===\n"
                except ImportError:
                    # 如果无法导入国际化模块，使用原始格式
                    return f"\n=== {record.getMessage()} ===\n"
            # 否则使用标准格式
            return standard_formatter.format(record)
    
    # 设置自定义格式化器
    file_handler.setFormatter(UserLogFormatter())
    
    # 只处理user_log函数发出的日志
    file_handler.addFilter(lambda record: hasattr(record, 'user_log') and record.user_log)
    
    # 从日志记录器中移除旧的用户日志处理器
    for handler in logger.handlers[:]:
        if hasattr(handler, 'user_log_handler') and handler.user_log_handler:
            logger.removeHandler(handler)
    
    # 标记此处理器为用户日志处理器
    file_handler.user_log_handler = True
    
    # 添加到日志记录器
    logger.addHandler(file_handler)
    
    # 保存日志文件路径
    default_user_log_file = log_file
    
    # Record an initial log to ensure file is created
    # Note: Use direct text instead of translation since i18n may not be initialized yet
    user_log("Log initialization complete")
    
    # 强制刷新日志
    for handler in logger.handlers:
        if hasattr(handler, 'flush'):
            handler.flush()
    
    return log_file

def setup_full_logging(debug_log_file=None, user_log_file=None):
    """同时设置调试日志和用户日志
    
    Args:
        debug_log_file: 调试日志文件路径
        user_log_file: 用户日志文件路径
    """
    debug_log = setup_file_logging(debug_log_file)
    user_log = setup_user_logging(user_log_file)
    return debug_log, user_log

def get_log_file():
    """获取当前系统调试日志文件路径"""
    return default_log_file

def get_user_log_file():
    """获取当前用户日志文件路径"""
    return default_user_log_file

def get_full_log_file():
    """获取当前完整日志文件路径"""
    return default_full_log_file

def handle_i18n_log(message, language=None, **kwargs):
    """处理可能包含国际化标识的日志消息

    Args:
        message: 日志消息，可能是i18n:key形式
        language: 语言代码，如果为None则使用当前语言
        **kwargs: 用于格式化的参数

    Returns:
        转换后的日志消息
    """
    # 导入_函数，确保在整个函数中可用
    try:
        from .i18n import _
    except ImportError:
        # 如果无法导入，定义一个简单的替代函数
        def _(key, **kw):
            if isinstance(key, str) and '{' in key and '}' in key and kw:
                try:
                    return key.format(**kw)
                except:
                    pass
            return key
    
    # 添加递归深度控制
    _recursion_depth = kwargs.pop('_recursion_depth', 0)
    if _recursion_depth > 3:  # 限制递归深度为3
        return f"[递归错误] {message}"

    # 在处理之前应用参数映射修复（仅适用于翻译键，不适用于已翻译的消息）
    # 尝试从消息中提取翻译键进行参数映射
    translation_key = None
    if isinstance(message, str):
        if message.startswith("i18n:"):
            translation_key = message[5:]
        # 注意：不再尝试从已翻译的消息中推断翻译键，因为这会导致重复处理
        # 已翻译的消息应该直接传递，不需要再次应用参数映射

    # 如果找到了翻译键，应用参数映射修复
    if translation_key:
        kwargs = fix_translation_params(translation_key, kwargs)

    # 确保常见的统计参数有默认值，避免[缺失:xxx]的情况
    for param in COMMON_STATS:
        if param not in kwargs:
            kwargs[param] = 0
    
    # 处理字典参数中的敏感信息
    for key, value in list(kwargs.items()):
        if isinstance(value, dict):
            # 使用_mask_sensitive_dict函数处理字典中的敏感信息
            kwargs[key] = _mask_sensitive_dict(value)
            
            # 特殊处理: 简化警告日志中的JSON数据参数
            if isinstance(message, str) and ('warning' in message.lower() or '警告' in message):
                # 检查是否需要保留部分键值
                if 'raw_name' in value:
                    # 应用参数映射修复
                    placeholder_kwargs = fix_translation_params("format.interface_placeholder", {"name": value.get('raw_name')})
                    kwargs[key] = handle_i18n_log(_("format.interface_placeholder"), _recursion_depth=_recursion_depth+1, **placeholder_kwargs)
                elif 'name' in value:
                    # 应用参数映射修复
                    placeholder_kwargs = fix_translation_params("format.object_placeholder", {"name": value.get('name')})
                    kwargs[key] = handle_i18n_log(_("format.object_placeholder"), _recursion_depth=_recursion_depth+1, **placeholder_kwargs)
                else:
                    # 简化为通用描述
                    kwargs[key] = handle_i18n_log(_("format.data_object_placeholder"), _recursion_depth=_recursion_depth+1)
    
    # 如果消息以i18n:开头，表示这是一个需要翻译的键
    if isinstance(message, str) and message.startswith("i18n:"):
        key = message[5:]  # 移除i18n:前缀
        try:
            # 创建新的kwargs副本，确保移除可能存在的'key'参数，避免参数冲突
            translation_kwargs = kwargs.copy()
            if 'key' in translation_kwargs:
                translation_kwargs.pop('key')

            # 修复翻译参数名不匹配问题
            translation_kwargs = fix_translation_params(key, translation_kwargs)

            translated = _(key, language=language, **translation_kwargs)
            
            # 确保translated是字符串类型
            if not isinstance(translated, str):
                translated = str(translated)
                
            # 尝试再次格式化，确保所有占位符都被替换
            if '{' in translated and '}' in translated:
                try:
                    # 检查是否所有需要的参数都已提供
                    import re
                    placeholders = re.findall(r'\{([^{}]*)\}', translated)
                    missing_keys = [p for p in placeholders if p not in kwargs]
                    
                    if missing_keys:
                        # 检查是否是JSON参数导致的问题
                        json_params = [k for k in missing_keys if k.startswith('{') and k.endswith('}')]
                        if json_params:
                            # 这是JSON格式化问题，不是真正的缺失参数，跳过警告
                            pass
                        else:
                            # 使用国际化警告消息，避免在日志中显示整个JSON
                            warning_msg = handle_i18n_log(_("warning.translation_missing_params"), key=key, missing_params=', '.join(missing_keys), _recursion_depth=_recursion_depth+1)
                            if not key.startswith("warning."):  # 避免警告消息的递归警告
                                logger.warning(warning_msg)
                        # 尝试为缺失的键提供默认值，避免格式化错误
                        for missing_key in missing_keys:
                            if missing_key in COMMON_STATS:
                                kwargs[missing_key] = 0
                            else:
                                kwargs[missing_key] = _("format.missing_parameter", param=missing_key)
                    
                    # 使用所有可用参数进行格式化
                    return translated.format(**kwargs)
                except KeyError as missing_key:
                    # 直接从异常对象获取键名，避免字符串处理问题
                    if hasattr(missing_key, 'args') and missing_key.args:
                        missing_key_str = missing_key.args[0]
                        # 确保键名是字符串类型
                        if not isinstance(missing_key_str, str):
                            missing_key_str = str(missing_key_str)
                    else:
                        # 回退到原始方法，但更彻底地处理引号
                        missing_key_str = str(missing_key).strip("'\"")
                        # 使用正则表达式移除所有引号
                        import re
                        missing_key_str = re.sub(r'[\'"]', '', missing_key_str)
                    
                    warning_msg = handle_i18n_log(_("warning.translation_missing_param"), key=key, param=missing_key_str, _recursion_depth=_recursion_depth+1)
                    if not key.startswith("warning."):  # 避免警告消息的递归警告
                        logger.warning(warning_msg)
                    # 为缺失的键提供默认值
                    if missing_key_str in COMMON_STATS:
                        kwargs[missing_key_str] = 0
                    else:
                        kwargs[missing_key_str] = _("format.missing_parameter", param=missing_key_str)
                    # 再次尝试格式化
                    try:
                        return translated.format(**kwargs)
                    except:
                        # 如果仍然失败，返回部分格式化的消息
                        return translated
                except Exception as e:
                    if not key.startswith("warning."):  # 避免警告消息的递归警告
                        logger.warning(handle_i18n_log(_("warning.translation_format_error"), key=key, error=str(e), _recursion_depth=_recursion_depth+1))
                    return translated
            
            return translated
        except ImportError as e:
            logger.warning(f"Failed to import i18n module: {key}, error: {str(e)}")
            return key
        except Exception as e:
            # 翻译失败，尝试直接用key替换占位符
            logger.warning(f"Translation key failed: {key}, error: {str(e)}")
            try:
                if '{' in key and '}' in key:
                    return key.format(**kwargs)
                return key
            except Exception as format_error:
                # 如果格式化也失败，返回原始键
                logger.warning(f"Key formatting failed: {key}, error: {str(format_error)}")
                return key
    
    # 如果不是i18n标记的消息，也尝试进行格式化替换
    if isinstance(message, str) and '{' in message and '}' in message:
        try:
            # 检查是否所有需要的参数都已提供
            import re
            placeholders = re.findall(r'\{([^{}]*)\}', message)
            missing_keys = [p for p in placeholders if p not in kwargs]
            
            if missing_keys:
                # 检查是否是JSON参数或接口映射参数导致的问题
                json_params = []
                interface_mapping_params = []

                for k in missing_keys:
                    if isinstance(k, str):
                        # JSON格式化问题 - 改进检测逻辑
                        if (k.startswith('{') and k.endswith('}')) or ('"' in k and ':' in k) or ('raw_name' in k and '"' in k):
                            json_params.append(k)
                        # 接口映射参数问题（如 'name': 'trust', 'name': 'vw2'）
                        elif ("'name':" in k or '"name":' in k) and ('trust' in k or 'vw' in k or 'port' in k):
                            interface_mapping_params.append(k)

                # 如果都是JSON或接口映射参数问题，跳过警告
                if len(json_params) + len(interface_mapping_params) == len(missing_keys):
                    # 这些都是格式化问题，不是真正的缺失参数，跳过警告
                    pass
                else:
                    # 简化警告信息，避免显示整个JSON
                    is_warning_msg = "warning" in message.lower() or "警告" in message or "Warning" in message
                    if not is_warning_msg:  # 避免警告消息的递归警告
                        warn_e = None  # 初始化变量
                        try:
                            # 确保参数列表有效
                            params_str = ', '.join(str(k) for k in missing_keys if k)
                            if not params_str:
                                params_str = "unknown_parameters"

                            warning_msg = handle_i18n_log(_("warning.missing_params"), missing_params=params_str, _recursion_depth=_recursion_depth+1)
                            logger.warning(warning_msg)
                        except Exception as warn_e:
                            # 如果警告消息本身失败，使用简单的回退消息
                            try:
                                fallback_msg = f"消息中缺少参数: {', '.join(str(k) for k in missing_keys)}"
                                logger.warning(fallback_msg)
                            except:
                                logger.warning(f"Message is missing parameters: {', '.join(str(k) for k in missing_keys)}")

                        # 只有在warn_e被定义时才记录调试信息
                        if warn_e is not None:
                            try:
                                debug_msg = f"警告消息处理失败: {str(warn_e)}"
                                logger.debug(debug_msg)
                            except:
                                logger.debug(f"Warning message processing failed: {str(warn_e)}")
                # 尝试为缺失的键提供默认值，避免格式化错误
                for missing_key in missing_keys:
                    if missing_key in COMMON_STATS:
                        kwargs[missing_key] = 0
                    else:
                        # 为常见的参数提供更合适的默认值
                        if missing_key in ['interface', 'interface_name', 'name']:
                            try:
                                kwargs[missing_key] = _("format.unknown_interface")
                            except:
                                kwargs[missing_key] = "Unknown Interface"
                        elif missing_key in ['error', 'reason']:
                            try:
                                kwargs[missing_key] = _("format.unknown_error")
                            except:
                                kwargs[missing_key] = "Unknown Error"
                        elif missing_key in ['count', 'total', 'success', 'failed']:
                            kwargs[missing_key] = 0
                        elif missing_key in ['file', 'path']:
                            try:
                                kwargs[missing_key] = _("format.unknown_file")
                            except:
                                kwargs[missing_key] = "Unknown File"
                        else:
                            try:
                                kwargs[missing_key] = _("format.missing_parameter", param=missing_key)
                            except:
                                try:
                                    kwargs[missing_key] = _("format.missing_param_fallback", param=missing_key)
                                except:
                                    kwargs[missing_key] = f"[Missing Parameter: {missing_key}]"
            
            return message.format(**kwargs)
        except KeyError as missing_key:
            # 直接从异常对象获取键名，避免字符串处理问题
            if hasattr(missing_key, 'args') and missing_key.args:
                missing_key_str = missing_key.args[0]
                # 确保键名是字符串类型
                if not isinstance(missing_key_str, str):
                    missing_key_str = str(missing_key_str)
            else:
                # 回退到原始方法，但更彻底地处理引号
                missing_key_str = str(missing_key).strip("'\"")
                # 使用正则表达式移除所有引号
                import re
                missing_key_str = re.sub(r'[\'"]', '', missing_key_str)
            
            # 检查是否是JSON参数或接口映射参数导致的问题
            is_json_param = (isinstance(missing_key_str, str) and
                           ((missing_key_str.startswith('{') and missing_key_str.endswith('}')) or
                            ('"' in missing_key_str and ':' in missing_key_str) or
                            ('raw_name' in missing_key_str and '"' in missing_key_str)))

            is_interface_mapping_param = (isinstance(missing_key_str, str) and
                                        (("'name':" in missing_key_str or '"name":' in missing_key_str) and
                                         ('trust' in missing_key_str or 'vw' in missing_key_str or 'port' in missing_key_str)))

            if is_json_param or is_interface_mapping_param:
                # 这是格式化问题，不是真正的缺失参数，跳过警告
                pass
            else:
                # 简化警告信息，避免冗长输出
                is_warning_msg = "warning" in message.lower() or "警告" in message or "Warning" in message
                if not is_warning_msg:  # 避免警告消息的递归警告
                    warn_e = None  # 初始化变量
                    try:
                        # 确保参数名称有效
                        if not missing_key_str or missing_key_str.strip() == "":
                            missing_key_str = "unknown_parameter"

                        # 使用更安全的警告消息调用
                        warning_msg = handle_i18n_log(_("warning.missing_param"), param=missing_key_str, _recursion_depth=_recursion_depth+1)
                        logger.warning(warning_msg)
                    except Exception as warn_e:
                        # 如果警告消息本身失败，使用简单的回退消息
                        try:
                            fallback_msg = f"消息中缺少参数: {missing_key_str}"
                            logger.warning(fallback_msg)
                        except:
                            logger.warning(f"Message is missing parameter: {missing_key_str}")

                    # 只有在warn_e被定义时才记录调试信息
                    if warn_e is not None:
                        try:
                            debug_msg = f"警告消息处理失败: {str(warn_e)}"
                            logger.debug(debug_msg)
                        except:
                            logger.debug(f"Warning message processing failed: {str(warn_e)}")
            # 为缺失的键提供默认值
            if missing_key_str in COMMON_STATS:
                kwargs[missing_key_str] = 0
            else:
                # 为常见的参数提供更合适的默认值
                if missing_key_str in ['interface', 'interface_name', 'name']:
                    try:
                        kwargs[missing_key_str] = _("format.unknown_interface")
                    except:
                        kwargs[missing_key_str] = "Unknown Interface"
                elif missing_key_str in ['error', 'reason']:
                    try:
                        kwargs[missing_key_str] = _("format.unknown_error")
                    except:
                        kwargs[missing_key_str] = "Unknown Error"
                elif missing_key_str in ['count', 'total', 'success', 'failed']:
                    kwargs[missing_key_str] = 0
                elif missing_key_str in ['file', 'path']:
                    try:
                        kwargs[missing_key_str] = _("format.unknown_file")
                    except:
                        kwargs[missing_key_str] = "Unknown File"
                else:
                    try:
                        kwargs[missing_key_str] = _("format.missing_parameter", param=missing_key_str)
                    except:
                        try:
                            kwargs[missing_key_str] = _("format.missing_param_fallback", param=missing_key_str)
                        except:
                            kwargs[missing_key_str] = f"[Missing Parameter: {missing_key_str}]"
            # 再次尝试格式化
            try:
                return message.format(**kwargs)
            except:
                return message
        except Exception as e:
            # 格式化失败，返回原始消息
            is_warning_msg = "warning" in message.lower() or "警告" in message or "Warning" in message
            if not is_warning_msg:  # 避免警告消息的递归警告
                try:
                    logger.warning(handle_i18n_log(_("warning.format_error"), error=str(e), _recursion_depth=_recursion_depth+1))
                except Exception as warn_e:
                    logger.warning(f"格式错误: {str(warn_e)}")
            return message

    # 检查消息是否是翻译键（不以i18n:开头但符合翻译键格式）
    if isinstance(message, str):
        try:
            from .i18n import TRANSLATION_PREFIXES
            # 检查是否是翻译键
            if any(message.startswith(prefix) for prefix in TRANSLATION_PREFIXES):
                # 这是一个翻译键，尝试翻译
                try:
                    translated = _(message, language=language, **kwargs)
                    if translated != message:  # 翻译成功
                        return translated
                except Exception:
                    pass  # 翻译失败，返回原始消息
        except ImportError:
            pass  # 无法导入翻译模块，返回原始消息

    return message

def log(message, level='info', quiet=None, **kwargs):
    """记录日志
    
    Args:
        message: 日志消息，可以是字符串或翻译键
        level: 日志级别，可以是'debug', 'info', 'warning', 'error', 'critical'
        quiet: 是否静默模式，覆盖全局设置
        **kwargs: 格式化参数
    """
    # 如果指定了quiet参数且为True，或者全局静默模式为True且未指定quiet参数，则不记录日志
    if (quiet is True) or (quiet is None and QUIET_MODE and level not in ['error', 'critical']):
        return
    
    # 获取mask_sensitive参数，用于控制是否掩码敏感信息
    mask_sensitive = kwargs.pop('mask_sensitive', True)
    
    # 处理kwargs中的敏感参数
    if mask_sensitive:
        # 敏感参数列表
        sensitive_params = ['password', 'pppoe_password', 'pppoe-password', 'pass', 
                           'api_key', 'api-key', 'token', 'secret', 'key']
        
        # 掩码敏感参数
        for param in list(kwargs.keys()):
            for sensitive_param in sensitive_params:
                if sensitive_param.lower() in param.lower() and isinstance(kwargs[param], str):
                    kwargs[param] = '******'
                    break
    
    # 特殊处理：检查消息是否包含JSON字符串，如果是，则简化消息
    if isinstance(message, str):
        # 检查是否是接口属性相关警告
        if level.lower() == 'warning' and ('属性:' in message or 'attributes:' in message) and '{' in message and '}' in message:
            # 尝试提取接口名
            interface_name = None
            try:
                # 尝试从"添加接口: xxx, 属性:"格式中提取接口名
                if '添加接口:' in message:
                    parts = message.split('添加接口:', 1)[1].split(',', 1)[0].strip()
                    if parts:
                        interface_name = parts
                elif 'interface:' in message.lower():
                    # 尝试从英文格式中提取接口名
                    parts = message.lower().split('interface:', 1)[1].split(',', 1)[0].strip()
                    if parts:
                        interface_name = parts
            except Exception:
                pass
            
            # 简化JSON显示
            simplified_msg = message
            try:
                import re
                # 替换JSON对象为简化表示
                if interface_name:
                    # 应用参数映射修复
                    json_kwargs = fix_translation_params("info.add_interface_with_json", {"interface_name": interface_name})
                    simplified_msg = handle_i18n_log(_("info.add_interface_with_json"), **json_kwargs)
                else:
                    json_placeholder = handle_i18n_log(_("format.json_data_placeholder"))
                    simplified_msg = re.sub(r'(\{[^{}]*\})', json_placeholder, message)
                
                # 使用简化的消息记录日志
                message = simplified_msg
            except Exception:
                # 保持原始消息
                pass
    
    # 处理可能的i18n标记
    # 确保不会传递message参数给handle_i18n_log，避免参数冲突
    i18n_kwargs = kwargs.copy()
    if 'message' in i18n_kwargs:
        i18n_kwargs.pop('message')
    msg = handle_i18n_log(message, **i18n_kwargs)
    
    # 检查是否需要跳过包含敏感信息的日志
    if SKIP_SENSITIVE_LOGS and isinstance(msg, str) and contains_sensitive_info(msg):
        return  # 直接跳过，不记录此日志

    # 处理敏感信息
    if mask_sensitive and isinstance(msg, str):
        msg = mask_sensitive_info(msg)
    
    # 尝试使用国际化翻译
    if _translator and callable(_translator) and isinstance(msg, str) and not msg.startswith("i18n:"):
        try:
            translated_msg = _translator(msg, **kwargs)
            # 确保翻译后的消息中的占位符也被替换
            if isinstance(translated_msg, str) and '{' in translated_msg and '}' in translated_msg:
                translated_msg = translated_msg.format(**kwargs)
            msg = translated_msg
        except Exception as e:
            # 如果翻译失败，尝试直接格式化原始消息
            try:
                if isinstance(msg, str) and '{' in msg and '}' in msg:
                    msg = msg.format(**kwargs)
            except Exception:
                # 如果格式化也失败，保持原样
                pass
    
    # 如果消息不是字符串，尝试转换为字符串
    if not isinstance(msg, str):
        try:
            msg = str(msg)
        except:
            msg = repr(msg)
    
    # 检查是否需要跳过包含敏感信息的日志
    if SKIP_SENSITIVE_LOGS and contains_sensitive_info(msg):
        return  # 直接跳过，不记录此日志

    # 获取日志级别
    log_level = LOG_LEVELS.get(level.lower(), logging.INFO)

    # 记录日志
    logger.log(log_level, msg)
    
    # 强制刷新日志
    flush_all_logs()

def user_log(message, level='info', quiet=False, **kwargs):
    """记录用户可见的日志
    
    Args:
        message: 日志消息，可以是字符串或翻译键
        level: 日志级别，可以是'debug', 'info', 'warning', 'error', 'critical'
        quiet: 是否静默模式
        **kwargs: 格式化参数
    """
    # 如果指定了quiet参数且为True，则不记录日志
    if quiet:
        return
    
    # 获取summary参数，用于标记总结信息
    summary = kwargs.pop('summary', False)
    
    # 获取mask_sensitive参数，用于控制是否掩码敏感信息
    mask_sensitive = kwargs.pop('mask_sensitive', True)
    
    # 获取语言参数
    language = kwargs.pop('language', None)
    
    # 处理kwargs中的敏感参数
    if mask_sensitive:
        # 敏感参数列表
        sensitive_params = ['password', 'pppoe_password', 'pppoe-password', 'pass', 
                           'api_key', 'api-key', 'token', 'secret', 'key']
        
        # 掩码敏感参数
        for param in list(kwargs.keys()):
            for sensitive_param in sensitive_params:
                if sensitive_param.lower() in param.lower() and isinstance(kwargs[param], str):
                    kwargs[param] = '******'
                    break
    
    # 处理可能的i18n标记
    # 确保不会传递message参数给handle_i18n_log，避免参数冲突
    i18n_kwargs = kwargs.copy()
    if 'message' in i18n_kwargs:
        i18n_kwargs.pop('message')

    if isinstance(message, str) and message.startswith("i18n:"):
        # 使用handle_i18n_log函数处理i18n标记的消息
        msg = handle_i18n_log(message, language=language, **i18n_kwargs)
    else:
        # 处理可能的i18n标记
        msg = handle_i18n_log(message, language=language, **i18n_kwargs)
    
    # 检查是否需要跳过包含敏感信息的日志
    if SKIP_SENSITIVE_LOGS and isinstance(msg, str) and contains_sensitive_info(msg):
        return  # 直接跳过，不记录此日志

    # 最后一次检查消息中的敏感信息
    if mask_sensitive and isinstance(msg, str):
        # 使用集中的敏感信息掩码函数
        msg = mask_sensitive_info(msg)
    
    # 如果消息不是字符串，尝试转换为字符串
    if not isinstance(msg, str):
        try:
            msg = str(msg)
        except:
            msg = repr(msg)
    
    # 获取日志级别
    log_level = LOG_LEVELS.get(level.lower(), logging.INFO)
    
    # 创建一个额外的记录，标记为用户日志
    record = logger.makeRecord(
        logger.name, log_level, "", 0, msg, (), None, "", None
    )
    record.user_log = True  # 添加用户日志标记
    
    # 如果是总结信息，添加标记
    if summary:
        record.summary = True
    
    # 添加语言标记
    if language:
        record.language = language
    
    # 记录日志
    logger.handle(record)
    
    # 强制刷新日志
    flush_all_logs()

def log_to_json(message, log_type="system", level="info", additional_info=None, **kwargs):
    """将日志消息格式化为JSON并记录
    
    Args:
        message: 日志消息，如果以"i18n:"开头，则会被翻译
        log_type: 日志类型，可以是system或user
        level: 日志级别
        additional_info: 额外信息字典
        **kwargs: 用于国际化消息的参数
    """
    # 处理国际化消息
    # 确保不会传递message参数给handle_i18n_log，避免参数冲突
    i18n_kwargs = kwargs.copy()
    if 'message' in i18n_kwargs:
        i18n_kwargs.pop('message')
    translated_message = handle_i18n_log(message, **i18n_kwargs)
    
    # 创建日志消息
    log_data = {
        "timestamp": datetime.datetime.now().strftime(DATE_FORMAT),
        "type": log_type,
        "level": level,
        "message": translated_message
    }
    
    # 添加额外信息
    if additional_info:
        log_data.update(additional_info)
    
    # 转换为JSON并记录
    json_message = json.dumps(log_data, ensure_ascii=False)
    
    if log_type == "user":
        user_log(json_message, level)
    else:
        log(json_message, level)

def log_error(message, **kwargs):
    """记录错误日志
    
    Args:
        message: 错误消息，如果以"i18n:"开头，则会被翻译
        **kwargs: 用于国际化消息的参数
    """
    log(message, 'error', **kwargs)

def log_warning(message, **kwargs):
    """记录警告日志
    
    Args:
        message: 警告消息，如果以"i18n:"开头，则会被翻译
        **kwargs: 用于国际化消息的参数
    """
    log(message, 'warning', **kwargs)

def set_translator(translator):
    """设置全局翻译器
    
    Args:
        translator: 翻译函数，应接受key和kwargs参数
    """
    global _translator
    _translator = translator
    logger.debug(handle_i18n_log(_("debug.set_translator"), translator=translator))

def initialize_translator():
    """初始化翻译器"""
    try:
        from .i18n import translate
        set_translator(translate)
        logger.debug("Translator initialized successfully")
    except ImportError:
        logger.warning("Cannot import i18n module, translation functionality will be unavailable")
    except Exception as e:
        logger.error(handle_i18n_log(_("error.init_translator_failed"), error=str(e)))

def get_current_log_dir():
    """
    获取当前配置的日志目录

    Returns:
        str: 当前日志目录路径，如果未设置则返回None
    """
    global current_log_directory
    return current_log_directory

def flush_all_logs():
    """强制刷新所有日志"""
    # 刷新所有处理器
    for handler in logger.handlers:
        if hasattr(handler, 'flush'):
            try:
                handler.flush()
            except Exception as e:
                print(handle_i18n_log(_("error.flush_log_handler_failed"), error=str(e)))
    
    # 确保日志文件被写入磁盘
    try:
        import os
        os.fsync(handler.stream.fileno())
    except:
        pass

# 为向后兼容性设置一些属性
log.set_level = setup_logging
log.setup_file_logging = setup_file_logging
log.setup_user_logging = setup_user_logging
log.setup_full_logging = setup_full_logging
log.get_log_file = get_log_file
log.get_user_log_file = get_user_log_file
log.get_full_log_file = get_full_log_file
log.user_log = user_log
log.log_to_json = log_to_json
log.flush_all_logs = flush_all_logs  # 添加新函数到log对象

def translate(key, language=None, **kwargs):
    """根据键和参数获取翻译后的文本
    
    Args:
        key: 翻译键，格式为 "section.key"
        language: 语言代码，如果为None则使用当前设置的语言
        **kwargs: 格式化参数
        
    Returns:
        str: 翻译后的文本
    """
    try:
        # 从i18n模块导入必要的函数和变量
        from .i18n import translate as i18n_translate
        
        # 确保不会传递key和language参数，避免参数冲突
        translation_kwargs = kwargs.copy()
        if 'key' in translation_kwargs:
            translation_kwargs.pop('key')
        if 'language' in translation_kwargs:
            translation_kwargs.pop('language')
        
        # 使用i18n模块的translate函数，避免参数冲突
        return i18n_translate(key, language, **translation_kwargs)
    except ImportError:
        # 如果无法导入i18n模块，回退到简单的格式化
        logger.warning(f"Failed to import i18n module: {key}")
        if kwargs and '{' in key and '}' in key:
            try:
                return key.format(**kwargs)
            except:
                pass
        return key
    except Exception as e:
        logger.warning(f"Translation error: {key}, error: {str(e)}")
        return key

# 为方便使用，定义_函数作为translate的别名，但确保参数传递正确
def _(key, **kwargs):
    """简单的国际化支持函数，用于翻译文本
    
    Args:
        key: 待翻译的文本键或者文本内容
        **kwargs: 格式化参数
    
    Returns:
        str: 翻译后的文本
    """
    # 确保不会传递key参数，避免参数冲突
    translation_kwargs = kwargs.copy()
    if 'key' in translation_kwargs:
        translation_kwargs.pop('key')
    
    # 调用translate函数进行翻译
    return translate(key, None, **translation_kwargs)

def format_message(message, **kwargs):
    """格式化消息文本，支持i18n:前缀自动翻译
    
    Args:
        message: 消息文本或翻译键
        **kwargs: 格式化参数
        
    Returns:
        str: 格式化后的消息
    """
    try:

            
        # 检查是否需要翻译
        if isinstance(message, str) and message.startswith('i18n:'):
            # 确保不会传递key参数，避免参数冲突
            translation_kwargs = kwargs.copy()
            if 'key' in translation_kwargs:
                translation_kwargs.pop('key')
            
            # 添加递归深度控制
            if '_recursion_depth' not in translation_kwargs:
                translation_kwargs['_recursion_depth'] = 0
            
            return translate(message, None, **translation_kwargs)
        
        # 普通消息，直接格式化
        if kwargs:
            try:
                # 修复翻译参数名不匹配问题
                fixed_kwargs = fix_translation_params(message, kwargs)
                return message.format(**fixed_kwargs)
            except KeyError as e:
                # 直接从异常对象获取键名，避免字符串处理问题
                if hasattr(e, 'args') and e.args:
                    missing_key = e.args[0]
                    # 确保键名是字符串类型
                    if not isinstance(missing_key, str):
                        missing_key = str(missing_key)
                else:
                    # 回退到原始方法，但更彻底地处理引号
                    missing_key = str(e).strip("'\"")
                    # 使用正则表达式移除所有引号
                    import re
                    missing_key = re.sub(r'[\'"]', '', missing_key)
                

                return message
    except Exception as e:
        # 修复未定义变量key的问题
        error_msg = str(e)
        logger.warning(f"Translation formatting error: {message}, error: {error_msg}")
        return message
    
    return message

def contains_sensitive_info(msg):
    """
    检测消息是否包含敏感信息

    Args:
        msg: 需要检测的消息

    Returns:
        bool: 如果包含敏感信息返回True，否则返回False
    """
    if not isinstance(msg, str):
        return False

    # 检测Base64编码内容 (长度>30的Base64字符串)
    if re.search(r'(?<![a-zA-Z0-9+/=])[a-zA-Z0-9+/=]{30,}(?![a-zA-Z0-9+/=])', msg):
        return True

    # 检测其他敏感信息模式
    sensitive_patterns = [
        r'(?i)(password[=:])(\S+)',  # 密码字段
        r'(?i)(pppoe_password\s*[=:]\s*)(\S+)',  # PPPoE密码
        r'(?i)((api[_-]?key|token)[=:])(\S+)',  # API密钥
    ]

    for pattern in sensitive_patterns:
        if re.search(pattern, msg):
            return True

    return False

def mask_sensitive_info(msg, mask_base64=None):
    """
    掩码敏感信息，如密码、API密钥等

    Args:
        msg: 需要处理的消息
        mask_base64: 是否掩码Base64编码内容，None时使用全局配置

    Returns:
        处理后的消息，敏感信息被替换为******
    """
    if not isinstance(msg, str):
        return msg

    # 如果没有指定mask_base64参数，使用全局配置
    if mask_base64 is None:
        mask_base64 = MASK_BASE64_IN_LOGS
    
    # 尝试检测并处理JSON字符串中的敏感信息
    try:
        # 检查消息是否是JSON字符串
        if msg.strip().startswith('{') and msg.strip().endswith('}'):
            try:
                data = json.loads(msg)
                if isinstance(data, dict):
                    # 递归处理字典中的敏感字段
                    data = _mask_sensitive_dict(data)
                    return json.dumps(data, ensure_ascii=False)
            except json.JSONDecodeError:
                # 不是有效的JSON，继续使用正则表达式处理
                pass
    except Exception:
        # 任何异常，继续使用正则表达式处理
        pass
    
    # 掩码一般密码字段
    msg = re.sub(r'(?i)(password[=:])(\S+)', r'\1******', msg)
    
    # 掩码PPPoE密码字段 - 处理JSON格式
    msg = re.sub(r'(?i)(["\']\s*pppoe_password\s*["\']\s*:\s*["\'])([^"\']+)(["\'])', r'\1******\3', msg)
    
    # 掩码PPPoE密码字段 - 处理其他格式
    msg = re.sub(r'(?i)(pppoe_password\s*[=:]\s*)(\S+)', r'\1******', msg)
    
    # 掩码API密钥
    msg = re.sub(r'(?i)((api[_-]?key|token)[=:])(\S+)', r'\1******', msg)
    
    # 掩码可能的Base64编码内容 (长度>30的Base64字符串) - 可配置
    if mask_base64:
        msg = re.sub(r'(?<![a-zA-Z0-9+/=])[a-zA-Z0-9+/=]{30,}(?![a-zA-Z0-9+/=])', '[BASE64]', msg)
    
    return msg

def _mask_sensitive_dict(data):
    """
    递归处理字典中的敏感字段
    
    Args:
        data: 要处理的字典
        
    Returns:
        处理后的字典，敏感信息被替换为******
    """
    if not isinstance(data, dict):
        return data
    
    # 敏感字段列表
    sensitive_keys = [
        'password', 'pppoe_password', 'pppoe-password', 'pass', 
        'api_key', 'api-key', 'token', 'secret', 'key'
    ]
    
    result = {}
    for key, value in data.items():
        # 检查是否是敏感字段
        is_sensitive = False
        for sensitive_key in sensitive_keys:
            if sensitive_key.lower() in key.lower():
                is_sensitive = True
                break
        
        if is_sensitive and isinstance(value, str):
            # 掩码敏感字段的值
            result[key] = '******'
        elif isinstance(value, dict):
            # 递归处理嵌套字典
            result[key] = _mask_sensitive_dict(value)
        elif isinstance(value, list):
            # 处理列表中的字典
            result[key] = [_mask_sensitive_dict(item) if isinstance(item, dict) else item for item in value]
        else:
            # 保持其他字段不变
            result[key] = value
    
    return result