#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
全局名称管理器
负责跨命名空间的名称唯一性管理和冲突解决
"""

from typing import Dict, Set, Optional, Tuple, List
from dataclasses import dataclass
from engine.utils.logger import log
from engine.utils.i18n import _
from engine.utils.name_validator import clean_ntos_name, validate_ntos_name

@dataclass
class NamespaceInfo:
    """命名空间信息"""
    namespace: str
    description: str
    max_length: int = 64

class GlobalNameManager:
    """全局名称管理器"""
    
    def __init__(self):
        """初始化名称管理器"""
        self.namespaces = {
            "security-policy": NamespaceInfo(
                namespace="urn:ruijie:ntos:params:xml:ns:yang:security-policy",
                description="安全策略命名空间",
                max_length=64
            ),
            "nat": NamespaceInfo(
                namespace="urn:ruijie:ntos:params:xml:ns:yang:nat",
                description="NAT规则命名空间", 
                max_length=64
            ),
            "network-obj": NamespaceInfo(
                namespace="urn:ruijie:ntos:params:xml:ns:yang:network-obj",
                description="网络对象命名空间",
                max_length=64
            ),
            "service-obj": NamespaceInfo(
                namespace="urn:ruijie:ntos:params:xml:ns:yang:service-obj", 
                description="服务对象命名空间",
                max_length=64
            )
        }
        
        # 每个命名空间的已使用名称
        self.used_names: Dict[str, Set[str]] = {
            ns: set() for ns in self.namespaces.keys()
        }
        
        # 原始名称到最终名称的映射（用于追踪）
        self.name_mappings: Dict[str, Dict[str, str]] = {
            ns: {} for ns in self.namespaces.keys()
        }
    
    def register_name(self, namespace: str, original_name: str, 
                     preferred_name: Optional[str] = None) -> str:
        """
        注册名称到指定命名空间
        
        Args:
            namespace: 命名空间标识
            original_name: 原始名称
            preferred_name: 首选名称（如果不提供则使用original_name）
            
        Returns:
            str: 最终分配的唯一名称
        """
        if namespace not in self.namespaces:
            raise ValueError(f"未知的命名空间: {namespace}")
        
        ns_info = self.namespaces[namespace]
        base_name = preferred_name or original_name
        
        # 清理名称
        clean_name = clean_ntos_name(base_name, ns_info.max_length - 10)  # 预留后缀空间
        
        # 验证名称
        is_valid, error_msg = validate_ntos_name(clean_name)
        if not is_valid:
            error_msg = _("name_manager.invalid_name", name=clean_name, error=error_msg)
            log(error_msg, "warning")
            clean_name = f"obj_{len(self.used_names[namespace])}"
        
        # 确保唯一性
        final_name = self._ensure_uniqueness(namespace, clean_name)
        
        # 注册名称
        self.used_names[namespace].add(final_name)
        self.name_mappings[namespace][original_name] = final_name
        
        # 记录名称分配
        if final_name != original_name:
            log(_("name_manager.name_assigned", 
                  namespace=ns_info.description,
                  original=original_name, 
                  final=final_name), "info")
        
        return final_name
    
    def _ensure_uniqueness(self, namespace: str, base_name: str) -> str:
        """确保名称在命名空间内唯一"""
        if base_name not in self.used_names[namespace]:
            return base_name
        
        # 添加数字后缀
        counter = 1
        max_length = self.namespaces[namespace].max_length
        
        while counter <= 9999:  # 防止无限循环
            suffix = f"_{counter}"
            if len(base_name) + len(suffix) > max_length:
                # 截断基础名称
                truncated_base = base_name[:max_length - len(suffix)]
                candidate = f"{truncated_base}{suffix}"
            else:
                candidate = f"{base_name}{suffix}"
            
            if candidate not in self.used_names[namespace]:
                return candidate
            
            counter += 1
        
        # 如果仍然冲突，使用时间戳
        import time
        timestamp = str(int(time.time()))[-6:]  # 使用时间戳后6位
        return f"obj_{timestamp}"
    
    def get_final_name(self, namespace: str, original_name: str) -> Optional[str]:
        """获取原始名称对应的最终名称"""
        return self.name_mappings.get(namespace, {}).get(original_name)
    
    def check_cross_namespace_conflicts(self) -> Dict[str, list]:
        """检查跨命名空间的名称冲突（用于警告）"""
        conflicts = {}
        all_names = {}
        
        # 收集所有命名空间的名称
        for ns, names in self.used_names.items():
            for name in names:
                if name not in all_names:
                    all_names[name] = []
                all_names[name].append(ns)
        
        # 找出在多个命名空间中使用的名称
        for name, namespaces in all_names.items():
            if len(namespaces) > 1:
                conflicts[name] = namespaces
        
        return conflicts
    
    def generate_summary_report(self) -> Dict[str, any]:
        """生成名称管理摘要报告"""
        total_names = sum(len(names) for names in self.used_names.values())
        renamed_count = sum(
            len([k for k, v in mappings.items() if k != v])
            for mappings in self.name_mappings.values()
        )
        
        cross_conflicts = self.check_cross_namespace_conflicts()
        
        return {
            "total_names": total_names,
            "renamed_count": renamed_count,
            "cross_namespace_conflicts": len(cross_conflicts),
            "namespace_stats": {
                ns: len(names) for ns, names in self.used_names.items()
            },
            "conflicts_detail": cross_conflicts
        }

# 全局实例
global_name_manager = GlobalNameManager()


def create_unique_nat_name(policy_name: str, nat_type: str,
                          existing_names: Set[str], vip_name: str = None) -> str:
    """
    为NAT规则创建唯一名称

    Args:
        policy_name: 策略名称
        nat_type: NAT类型 (snat, dnat, dynamic, static)
        existing_names: 已存在的名称集合
        vip_name: VIP名称（DNAT规则使用）

    Returns:
        str: 唯一的NAT规则名称
    """
    if nat_type == "snat":
        # SNAT规则直接使用策略名称
        preferred_name = policy_name
    elif nat_type == "dnat" and vip_name:
        # DNAT规则使用策略名称_VIP名称格式
        preferred_name = f"{policy_name}_{vip_name}"
    elif nat_type in ["dynamic", "static"]:
        # 动态/静态SNAT使用策略名称_类型格式
        preferred_name = f"{policy_name}_{nat_type}"
    else:
        preferred_name = policy_name

    return global_name_manager.register_name("nat", policy_name, preferred_name)


class IPPoolNameSyncManager:
    """地址池名称同步管理器"""

    def __init__(self):
        # 名称变更监听器
        self.name_change_listeners = []

        # 池名称映射（旧名称 -> 新名称）
        self.pool_name_mappings = {}

        # 待同步的引用更新
        self.pending_reference_updates = []

    def register_name_change_listener(self, listener_func):
        """
        注册名称变更监听器

        Args:
            listener_func: 监听器函数，接收(old_name, new_name, object_type)参数
        """
        self.name_change_listeners.append(listener_func)

    def notify_pool_name_change(self, old_name: str, new_name: str):
        """
        通知地址池名称变更

        Args:
            old_name: 原名称
            new_name: 新名称
        """
        # 记录名称映射
        self.pool_name_mappings[old_name] = new_name

        # 通知所有监听器
        for listener in self.name_change_listeners:
            try:
                listener(old_name, new_name, "ippool")
            except Exception as e:
                log(_("name_manager.listener_notification_failed", error=str(e)), "warning")

        # 记录待同步的引用更新
        self.pending_reference_updates.append({
            "type": "pool_rename",
            "old_name": old_name,
            "new_name": new_name,
            "timestamp": self._get_current_timestamp()
        })

        log(_("name_manager.pool_name_change_notified", old_name=old_name, new_name=new_name), "info")

    def sync_nat_rule_pool_references(self, nat_rules: List[Dict]) -> List[Dict]:
        """
        同步NAT规则中的地址池引用

        Args:
            nat_rules: NAT规则列表

        Returns: List[Dict]: 更新后的NAT规则列表
        """
        updated_rules = []

        for rule in nat_rules:
            updated_rule = rule.copy()

            # 检查动态SNAT规则的pool-name字段
            if "dynamic-translate-parameters" in updated_rule:
                dyn_params = updated_rule["dynamic-translate-parameters"]
                if "pool-name" in dyn_params:
                    old_pool_name = dyn_params["pool-name"]
                    if old_pool_name in self.pool_name_mappings:
                        new_pool_name = self.pool_name_mappings[old_pool_name]
                        dyn_params["pool-name"] = new_pool_name
                        log(_("name_manager.nat_rule_pool_reference_updated",
                             rule_name=updated_rule.get("name", "unknown"),
                             old_pool=old_pool_name, new_pool=new_pool_name), "info")

            updated_rules.append(updated_rule)

        return updated_rules

    def sync_pool_definitions(self, pools: List[Dict]) -> List[Dict]:
        """
        同步地址池定义中的名称

        Args:
            pools: 地址池列表

        Returns: List[Dict]: 更新后的地址池列表
        """
        updated_pools = []

        for pool in pools:
            updated_pool = pool.copy()

            # 检查池名称是否需要更新
            old_name = pool.get("name")
            if old_name and old_name in self.pool_name_mappings:
                new_name = self.pool_name_mappings[old_name]
                updated_pool["name"] = new_name
                log(_("name_manager.pool_definition_name_updated",
                     old_name=old_name, new_name=new_name), "info")

            updated_pools.append(updated_pool)

        return updated_pools

    def get_pool_name_mapping(self, old_name: str) -> Optional[str]:
        """
        获取地址池的新名称

        Args:
            old_name: 原名称

        Returns:
            Optional[str]: 新名称，如果没有变更则返回None
        """
        return self.pool_name_mappings.get(old_name)

    def clear_name_mappings(self):
        """清除所有名称映射"""
        self.pool_name_mappings.clear()
        self.pending_reference_updates.clear()
        log(_("name_manager.name_mappings_cleared"), "info")

    def get_pending_updates(self) -> List[Dict]:
        """获取待处理的引用更新"""
        return self.pending_reference_updates.copy()

    def _get_current_timestamp(self) -> str:
        """获取当前时间戳"""
        import datetime
        return datetime.datetime.now().isoformat()


# 全局地址池名称同步管理器实例
global_pool_name_sync_manager = IPPoolNameSyncManager()
