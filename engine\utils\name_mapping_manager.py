#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
名称映射管理器模块

用于管理FortiGate到NTOS转换过程中的名称映射和引用完整性。
确保重命名后的配置对象间引用关系保持正确。
"""

from typing import Dict, Set, List, Optional, Any
from collections import defaultdict
from lxml import etree
from engine.utils.logger import log
from engine.utils.i18n import _
from engine.utils.name_validator import clean_ntos_name, validate_ntos_name


class NameMappingManager:
    """
    名称映射管理器
    
    负责管理配置对象的名称映射，确保重命名后的引用完整性。
    支持多种对象类型：NAT池、服务对象、地址对象、安全策略等。
    """
    
    def __init__(self):
        """初始化名称映射管理器"""
        # 各类型对象的名称映射：原名 -> 清理后名称
        self.name_mappings: Dict[str, Dict[str, str]] = {
            'interfaces': {},         # 接口映射
            'bridge_interfaces': {},  # 桥接接口映射
            'nat_pools': {},          # NAT池映射
            'nat_rules': {},          # NAT规则映射
            'services': {},           # 服务对象映射
            'service_groups': {},     # 服务组映射
            'addresses': {},          # 地址对象映射
            'address_groups': {},     # 地址组映射
            'policies': {},           # 安全策略映射
            'zones': {},              # 安全区域映射
            'time_ranges': {},        # 时间范围映射
            'routes': {},             # 路由映射
            'dns_servers': {}         # DNS服务器映射
        }
        
        # 已使用的名称集合，用于避免重名冲突
        self.used_names: Dict[str, Set[str]] = {
            obj_type: set() for obj_type in self.name_mappings.keys()
        }
        
        # 映射统计信息
        self.mapping_stats = {
            'total_mappings': 0,
            'cleaned_names': 0,
            'conflicts_resolved': 0
        }
        
        # 引用关系记录
        self.references: Dict[str, List[Dict[str, Any]]] = defaultdict(list)
        
        log(_("name_mapping_manager.initialized"), "info")
    
    def register_name_mapping(self, obj_type: str, original_name: str, 
                            preferred_name: Optional[str] = None,
                            context: Optional[str] = None) -> str:
        """
        注册名称映射
        
        Args:
            obj_type: 对象类型（如'nat_pools', 'services'等）
            original_name: 原始名称
            preferred_name: 首选名称（如果不提供则使用original_name）
            context: 上下文信息（用于日志）
            
        Returns:
            str: 最终分配的清理后名称
            
        Raises:
            ValueError: 当对象类型不支持时
        """
        if obj_type not in self.name_mappings:
            raise ValueError(f"不支持的对象类型: {obj_type}")
        
        # 如果已经映射过，直接返回
        if original_name in self.name_mappings[obj_type]:
            return self.name_mappings[obj_type][original_name]
        
        # 确定基础名称
        base_name = preferred_name or original_name
        
        # 清理名称
        cleaned_name = self._clean_and_validate_name(base_name, obj_type)
        
        # 解决重名冲突
        final_name = self._resolve_name_conflict(cleaned_name, obj_type)
        
        # 记录映射
        self.name_mappings[obj_type][original_name] = final_name
        self.used_names[obj_type].add(final_name)
        
        # 更新统计信息
        self.mapping_stats['total_mappings'] += 1
        if original_name != final_name:
            self.mapping_stats['cleaned_names'] += 1
        
        # 记录日志
        if original_name != final_name:
            log(_("name_mapping_manager.name_mapped", 
                  obj_type=obj_type,
                  original=original_name, 
                  final=final_name,
                  context=context or ""), "info")
        
        return final_name
    
    def _clean_and_validate_name(self, name: str, obj_type: str) -> str:
        """
        清理和验证名称
        
        Args:
            name: 原始名称
            obj_type: 对象类型
            
        Returns:
            str: 清理后的名称
        """
        if not name:
            return f"unnamed_{obj_type}"
        
        # 使用通用的名称清理函数
        cleaned = clean_ntos_name(name, 64)
        
        # 验证清理后的名称
        is_valid, error_msg = validate_ntos_name(cleaned)
        if not is_valid:
            log(_("name_mapping_manager.validation_failed",
                  name=cleaned, error=error_msg), "warning")
            # 使用安全的默认名称
            cleaned = f"obj_{hash(name) % 10000}"
            cleaned = clean_ntos_name(cleaned, 64)
        
        return cleaned
    
    def _resolve_name_conflict(self, name: str, obj_type: str) -> str:
        """
        解决名称冲突
        
        Args:
            name: 候选名称
            obj_type: 对象类型
            
        Returns:
            str: 解决冲突后的唯一名称
        """
        if name not in self.used_names[obj_type]:
            return name
        
        # 存在冲突，添加数字后缀
        base_name = name
        counter = 1
        max_attempts = 1000  # 防止无限循环
        
        while counter <= max_attempts:
            candidate = f"{base_name}_{counter}"
            
            # 确保候选名称不超过长度限制
            if len(candidate) > 64:
                # 截断基础名称为候选名称留出空间
                suffix = f"_{counter}"
                max_base_len = 64 - len(suffix)
                candidate = f"{base_name[:max_base_len]}{suffix}"
            
            if candidate not in self.used_names[obj_type]:
                self.mapping_stats['conflicts_resolved'] += 1
                log(_("name_mapping_manager.conflict_resolved",
                      original=name, resolved=candidate), "info")
                return candidate
            
            counter += 1
        
        # 如果仍然无法解决冲突，使用哈希值
        hash_suffix = f"_{hash(name) % 10000}"
        fallback_name = f"{base_name[:64-len(hash_suffix)]}{hash_suffix}"
        log(_("name_mapping_manager.fallback_name_used",
              original=name, fallback=fallback_name), "warning")
        return fallback_name

    def get_mapped_name(self, obj_type: str, original_name: str) -> Optional[str]:
        """
        获取映射后的名称

        Args:
            obj_type: 对象类型
            original_name: 原始名称

        Returns:
            Optional[str]: 映射后的名称，如果不存在则返回None
        """
        return self.name_mappings.get(obj_type, {}).get(original_name)

    def register_reference(self, source_obj_type: str, source_name: str,
                          target_obj_type: str, target_name: str,
                          xml_element: etree.Element, xml_path: str):
        """
        注册引用关系

        Args:
            source_obj_type: 源对象类型
            source_name: 源对象名称
            target_obj_type: 目标对象类型
            target_name: 目标对象名称
            xml_element: XML元素
            xml_path: XML路径
        """
        reference_key = f"{target_obj_type}:{target_name}"
        self.references[reference_key].append({
            'source_type': source_obj_type,
            'source_name': source_name,
            'xml_element': xml_element,
            'xml_path': xml_path
        })

    def update_all_references(self, xml_root: etree.Element) -> int:
        """
        更新所有引用关系

        Args:
            xml_root: XML根元素

        Returns:
            int: 更新的引用数量
        """
        updated_count = 0

        # 更新各类型对象的引用
        updated_count += self._update_nat_references(xml_root)
        updated_count += self._update_service_references(xml_root)
        updated_count += self._update_address_references(xml_root)
        updated_count += self._update_policy_references(xml_root)

        log(_("name_mapping_manager.references_updated", count=updated_count), "info")
        return updated_count

    def _update_nat_references(self, xml_root: etree.Element) -> int:
        """更新NAT相关引用"""
        updated_count = 0

        # 更新NAT规则中对NAT池的引用
        for rule_elem in xml_root.xpath('//ntos-nat:rule', namespaces={'ntos-nat': 'urn:ruijie:ntos:params:xml:ns:yang:nat'}):
            # 查找poolname引用
            for poolname_elem in rule_elem.xpath('.//poolname'):
                if poolname_elem.text:
                    mapped_name = self.get_mapped_name('nat_pools', poolname_elem.text)
                    if mapped_name and mapped_name != poolname_elem.text:
                        poolname_elem.text = mapped_name
                        updated_count += 1

        return updated_count

    def _update_service_references(self, xml_root: etree.Element) -> int:
        """更新服务对象引用"""
        updated_count = 0

        # 更新策略中对服务的引用
        for service_elem in xml_root.xpath('//ntos-security-policy:policy//service/name',
                                         namespaces={'ntos-security-policy': 'urn:ruijie:ntos:params:xml:ns:yang:security-policy'}):
            if service_elem.text:
                mapped_name = self.get_mapped_name('services', service_elem.text)
                if mapped_name and mapped_name != service_elem.text:
                    service_elem.text = mapped_name
                    updated_count += 1

        # 更新服务组中对服务的引用
        for service_set_elem in xml_root.xpath('//ntos-service-obj:service-group//service-set/name',
                                             namespaces={'ntos-service-obj': 'urn:ruijie:ntos:params:xml:ns:yang:service-obj'}):
            if service_set_elem.text:
                mapped_name = self.get_mapped_name('services', service_set_elem.text)
                if mapped_name and mapped_name != service_set_elem.text:
                    service_set_elem.text = mapped_name
                    updated_count += 1

        return updated_count

    def _update_address_references(self, xml_root: etree.Element) -> int:
        """更新地址对象引用"""
        updated_count = 0

        # 更新策略中对地址的引用
        for addr_elem in xml_root.xpath('//ntos-security-policy:policy//source-network/name | //ntos-security-policy:policy//dest-network/name',
                                       namespaces={'ntos-security-policy': 'urn:ruijie:ntos:params:xml:ns:yang:security-policy'}):
            if addr_elem.text:
                mapped_name = self.get_mapped_name('addresses', addr_elem.text)
                if mapped_name and mapped_name != addr_elem.text:
                    addr_elem.text = mapped_name
                    updated_count += 1

        return updated_count

    def _update_policy_references(self, xml_root: etree.Element) -> int:
        """更新策略引用"""
        updated_count = 0

        # 策略名称通常不被其他对象引用，但可以在这里处理特殊情况
        # 例如策略组或策略依赖关系
        # 当前实现中暂时不需要处理策略间引用
        _ = xml_root  # 避免未使用参数警告

        return updated_count

    def generate_mapping_report(self) -> Dict[str, Any]:
        """
        生成名称映射报告

        Returns:
            Dict[str, Any]: 包含映射统计和详细信息的报告
        """
        report = {
            'statistics': self.mapping_stats.copy(),
            'mappings_by_type': {},
            'conflicts_resolved': self.mapping_stats['conflicts_resolved']
        }

        # 添加各类型的映射详情
        for obj_type, mappings in self.name_mappings.items():
            if mappings:  # 只包含有映射的类型
                report['mappings_by_type'][obj_type] = {
                    'count': len(mappings),
                    'mappings': dict(mappings)  # 复制映射字典
                }

        log(_("name_mapping_manager.report_generated",
              total=self.mapping_stats['total_mappings']), "info")

        return report

    def clear_mappings(self):
        """清空所有映射（用于测试或重置）"""
        for mappings in self.name_mappings.values():
            mappings.clear()
        for used_names in self.used_names.values():
            used_names.clear()
        self.references.clear()
        self.mapping_stats = {
            'total_mappings': 0,
            'cleaned_names': 0,
            'conflicts_resolved': 0
        }
        log(_("name_mapping_manager.mappings_cleared"), "info")
