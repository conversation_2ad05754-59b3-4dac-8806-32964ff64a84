#!/usr/bin/env python
# -*- coding: utf-8 -*-

import re
import ipaddress
from typing import Optional, List, Tuple, Dict

# 尝试导入日志工具，如果失败则使用简单的打印
try:
    from engine.utils.logger import log
    from engine.utils.i18n import _
except ImportError:
    def log(message, level="info", **kwargs):
        print(f"[{level.upper()}] {message}")

    def _(key, **kwargs):
        return key


def clean_ip_address_name(name: str) -> str:
    """
    专门清理IP地址格式的名称以符合YANG约束

    处理规则：
    - 纯IP地址（如**************）：直接返回，符合YANG约束
    - CIDR格式（如***********/24）：将斜杠替换为下划线
    - 接口名称：保持不变

    Args:
        name: 原始IP地址名称

    Returns:
        str: 清理后的名称
    """
    if not name:
        return "unnamed_ip"

    # 检查是否是接口名称格式（如Ge0/1.1001, TenGe0/1等）
    if '/' in name and re.match(r'^(Ge|TenGe|FastEthernet|GigabitEthernet)\d+/\d+(\.\d+)?$', name):
        # 这是接口名称，保持斜杠不变
        return name

    # 检查是否是CIDR格式
    if '/' in name:
        # 是CIDR格式，将斜杠替换为下划线
        cleaned = name.replace('/', '_')
        log(_("name_validator.ip_address_cleaned", original=name, cleaned=cleaned), "debug")
        return cleaned

    # 纯IP地址，直接返回（已经符合YANG约束）
    log(_("name_validator.ip_address_preserved", name=name), "debug")
    return name


def clean_ntos_name(name: str, max_length: int = 64) -> str:
    """
    清理名称以符合NTOS YANG模型约束

    根据ntos-obj-name-type定义，不能包含: `~!#$%^&*+|{};:"',\/<>?

    Args:
        name: 原始名称
        max_length: 最大长度限制

    Returns:
        str: 清理后的名称
    """
    if not name:
        return "unnamed"

    # 特殊处理：如果是IP地址格式，使用专门的清理函数
    if _looks_like_ip_address(str(name)):
        return clean_ip_address_name(str(name))

    # 特殊处理：接口名称（如Ge0/1.1001, TenGe0/1等）应保持斜杠不变
    if '/' in str(name) and re.match(r'^(Ge|TenGe|FastEthernet|GigabitEthernet)\d+/\d+(\.\d+)?$', str(name)):
        return str(name)  # 接口名称符合YANG约束，直接返回

    # 移除YANG模型不允许的字符: `~!@#$%^&*+|{};:"',\/<>?
    # 保留字母、数字、下划线、连字符、点号
    # 重要：保留土耳其语字符（ı, ğ, ş, ç, ü, ö等），因为YANG模型原生支持这些字符
    # 只过滤YANG模型明确禁用的ASCII特殊字符
    cleaned = re.sub(r'[`~!@#$%^&*+|{};:"\'\\/<>?,]', '_', str(name))

    # 替换连续的下划线为单个下划线
    cleaned = re.sub(r'_+', '_', cleaned)

    # 替换空格为下划线
    cleaned = re.sub(r'\s+', '_', cleaned)

    # 移除开头和结尾的下划线
    cleaned = cleaned.strip('_')

    # 如果清理后为空，使用默认名称
    if not cleaned.strip():
        cleaned = "unnamed_object"

    # 限制长度
    if len(cleaned) > max_length:
        cleaned = cleaned[:max_length]
        # 确保截断后不以下划线结尾
        cleaned = cleaned.rstrip('_')
        log(_("name_validator.name_truncated", original=name, cleaned=cleaned), "warning")

    # 移除过度优化：NTOS YANG模型实际上允许以数字开头的对象名称
    # ntos-obj-name-type的模式是 [^`~!#$%^&*+/|{};:"',\\<>?]*
    # 该模式只排除特定字符，并不禁止以数字开头
    # 只有用户名（auth）才要求以字母开头：[a-zA-Z]+[a-zA-Z0-9_]*

    # 记录清理过程
    if cleaned != name:
        log(_("name_validator.name_cleaned", original=name, cleaned=cleaned), "debug")

    return cleaned.strip()


def _looks_like_ip_address(name: str) -> bool:
    """
    检查字符串是否看起来像IP地址

    Args:
        name: 待检查的字符串

    Returns:
        bool: 是否像IP地址
    """
    # 检查CIDR格式（包含斜杠）
    if '/' in name:
        parts = name.split('/')
        if len(parts) == 2:
            ip_part = parts[0]
            # 检查IP部分是否包含3个点号（IPv4格式）
            if ip_part.count('.') == 3:
                return True

    # 检查纯IPv4地址格式
    if name.count('.') == 3:
        parts = name.split('.')
        if len(parts) == 4:
            try:
                # 验证每个部分都是0-255的数字
                for part in parts:
                    if not part:  # 空字符串
                        return False
                    num = int(part)
                    if not (0 <= num <= 255):
                        return False
                return True
            except ValueError:
                return False

    return False


def validate_ntos_name(name: str) -> Tuple[bool, str]:
    """
    验证名称是否符合NTOS YANG约束

    根据ntos-obj-name-type定义验证

    Args:
        name: 待验证的名称

    Returns: Tuple[bool, str]: (是否有效, 错误信息)
    """
    if not name:
        return False, "名称不能为空"

    # YANG模型约束: 不能包含特殊字符 `~!#$%^&*+|{};:"',\/<>?
    # 修复正则表达式，确保能正确检测所有非法字符
    invalid_chars = re.findall(r'[`~!@#$%^&*+|{};:"\'\\/<>?,]', name)
    if invalid_chars:
        return False, f"名称包含非法字符: {', '.join(set(invalid_chars))}"

    # 长度检查
    if len(name) > 64:
        return False, f"名称过长: {len(name)} > 64"

    # 检查是否以下划线开头或结尾（不推荐）
    if name.startswith('_') or name.endswith('_'):
        return False, "名称不应以下划线开头或结尾"

    return True, ""


def clean_ntos_description(description: str, max_length: int = 255) -> str:
    """
    清理描述以符合NTOS YANG模型约束

    根据ntos-obj-description-type定义，不能包含: `~!#$%^&*+/|{};:"\<>?

    Args:
        description: 原始描述
        max_length: 最大长度限制

    Returns:
        str: 清理后的描述
    """
    if not description:
        return ""

    # 移除YANG模型不允许的字符: `~!#$%^&*+/|{};:"\<>?
    # 特别注意双引号、反斜杠和其他特殊字符的处理
    cleaned = re.sub(r'[`~!#$%^&*+/|{};:"\'\\<>?]', '', str(description))

    # 清理多余的空格
    cleaned = re.sub(r'\s+', ' ', cleaned).strip()

    # 限制长度
    if len(cleaned) > max_length:
        cleaned = cleaned[:max_length].rstrip()
        log(_("name_validator.description_truncated", original=description, cleaned=cleaned), "warning")

    return cleaned


def validate_ntos_description(description: str) -> Tuple[bool, str]:
    """
    验证描述是否符合NTOS YANG约束

    Args:
        description: 待验证的描述

    Returns: Tuple[bool, str]: (是否有效, 错误信息)
    """
    if not description:
        return True, ""  # 描述可以为空
    
    # YANG模型约束: 不能包含特殊字符 `~!#$%^&*+/|{};:"\<>?
    invalid_chars = re.findall(r'[`~!#$%^&*+/|{};:"\\<>?]', description)
    if invalid_chars:
        return False, f"描述包含非法字符: {', '.join(set(invalid_chars))}"
    
    return True, ""


def create_unique_name(base_name: str, existing_names: set, max_length: int = 64) -> str:
    """
    创建唯一的名称，避免重复
    
    Args:
        base_name: 基础名称
        existing_names: 已存在的名称集合
        max_length: 最大长度限制
        
    Returns:
        str: 唯一的名称
    """
    # 先清理基础名称
    clean_base = clean_ntos_name(base_name, max_length - 10)  # 预留数字后缀空间
    
    if clean_base not in existing_names:
        return clean_base
    
    # 如果重复，添加数字后缀
    counter = 1
    while True:
        candidate = f"{clean_base}_{counter}"
        if len(candidate) > max_length:
            # 如果太长，截断基础名称
            truncated_base = clean_base[:max_length - len(f"_{counter}")]
            candidate = f"{truncated_base}_{counter}"
        
        if candidate not in existing_names:
            return candidate
        
        counter += 1
        if counter > 9999:  # 防止无限循环
            break
    
    # 如果仍然重复，使用时间戳
    import time
    timestamp = str(int(time.time()))[-6:]  # 使用时间戳后6位
    return f"obj_{timestamp}"


def sanitize_address_name(name: str) -> str:
    """
    专门用于地址对象名称的清理

    Args:
        name: 原始地址对象名称

    Returns:
        str: 清理后的地址对象名称
    """
    if not name:
        return "addr_unnamed"

    # 特殊处理一些常见的地址对象名称模式
    cleaned = str(name).strip()

    # 修复：IP地址格式应该保持原样，符合YANG约束
    if re.match(r'^\d+\.\d+\.\d+\.\d+$', cleaned):
        # 纯IP地址，直接返回（点号符合YANG约束）
        return cleaned
    elif re.match(r'^\d+\.\d+\.\d+\.\d+/\d+$', cleaned):
        # CIDR格式，只替换斜杠为下划线，保留点号
        return cleaned.replace('/', '_')
    # 检查是否是接口名称格式，如果是则保持斜杠
    elif re.match(r'^(Ge|TenGe|FastEthernet|GigabitEthernet)\d+/\d+(\.\d+)?$', cleaned):
        # 这是接口名称，保持原样
        return cleaned

    # 应用通用清理规则
    cleaned = clean_ntos_name(cleaned, 64)

    return cleaned


def sanitize_service_name(name: str) -> str:
    """
    专门用于服务对象名称的清理
    
    Args:
        name: 原始服务对象名称
        
    Returns:
        str: 清理后的服务对象名称
    """
    if not name:
        return "svc_unnamed"
    
    cleaned = str(name).strip()
    
    # 处理端口号作为名称的情况
    if re.match(r'^\d+$', cleaned):
        cleaned = f"port_{cleaned}"

    # 应用通用清理规则
    cleaned = clean_ntos_name(cleaned, 64)

    return cleaned


def detect_network_overlap(ip_addresses: List[str]) -> Dict[str, List[str]]:
    """
    检测IP地址列表中的网络重叠问题

    Args:
        ip_addresses: IP地址列表（支持CIDR格式）

    Returns: Dict[str, List[str]]: 网络重叠映射 {network: [overlapping_ips]}
    """
    network_groups = {}

    for ip_addr in ip_addresses:
        if not ip_addr:
            continue

        try:
            # 解析IP地址和网络
            if "/" in ip_addr:
                network = ipaddress.IPv4Network(ip_addr, strict=False)
            else:
                network = ipaddress.IPv4Network(f"{ip_addr}/32", strict=False)

            network_str = str(network.network_address) + "/" + str(network.prefixlen)

            # 添加到网络组
            if network_str not in network_groups:
                network_groups[network_str] = []
            network_groups[network_str].append(ip_addr)

        except (ipaddress.AddressValueError, ValueError) as e:
            log(f"Invalid IP address format: {ip_addr}, error: {e}", "warning")
            continue

    # 找出有多个IP的网络（这些是重叠的）
    overlaps = {}
    for network_str, ips in network_groups.items():
        if len(ips) > 1:
            overlaps[network_str] = ips

    return overlaps


def resolve_interface_ip_conflicts(ip_addresses: List[str]) -> List[Tuple[str, bool]]:
    """
    解决接口IP地址冲突，符合YANG模型约束

    Args:
        ip_addresses: IP地址列表

    Returns: List[Tuple[str, bool]]: [(ip_address, is_secondary), ...]
    """
    if not ip_addresses:
        return []

    # 检测网络重叠
    overlaps = detect_network_overlap(ip_addresses)

    if not overlaps:
        # 没有重叠，所有IP都是主IP
        return [(ip, False) for ip in ip_addresses]

    resolved_ips = []
    processed_ips = set()

    # 处理重叠网络中的IP地址
    for network_str, overlap_ips in overlaps.items():
        for i, ip_addr in enumerate(overlap_ips):
            if ip_addr in processed_ips:
                continue

            try:
                # 解析IP地址
                if "/" in ip_addr:
                    ip_part = ip_addr.split("/")[0]
                    prefix_len = int(ip_addr.split("/")[1])
                else:
                    ip_part = ip_addr
                    prefix_len = 32

                # 第一个IP保持原始网络掩码，其他IP使用/32主机路由
                if i == 0:
                    resolved_ips.append((ip_addr, False))  # 主IP
                else:
                    # 其他IP使用/32主机路由，标记为secondary
                    host_ip = f"{ip_part}/32"
                    resolved_ips.append((host_ip, True))  # 辅助IP

                processed_ips.add(ip_addr)

            except (ValueError, IndexError) as e:
                log(f"Error processing IP address {ip_addr}: {e}", "warning")
                # 出错时保持原样
                resolved_ips.append((ip_addr, False))
                processed_ips.add(ip_addr)

    # 处理非重叠的IP地址
    for ip_addr in ip_addresses:
        if ip_addr not in processed_ips:
            resolved_ips.append((ip_addr, False))
            processed_ips.add(ip_addr)

    return resolved_ips
