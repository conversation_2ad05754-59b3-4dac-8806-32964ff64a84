#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
NTOS名称验证和规范化工具类

基于NTOS YANG模型实现服务对象和服务组名称的验证和规范化功能。
支持特殊字符清理、冲突检测、映射记录等功能。

Author: AI Assistant
Date: 2025-07-03
"""

import re
import logging
from typing import Dict, Set, Optional, Tuple
from engine.utils.logger import log
from engine.utils.i18n import _


class NTOSNameValidator:
    """
    NTOS名称验证和规范化工具类
    
    基于YANG模型 ntos-obj-name-type 的模式约束：
    pattern "[^`~!#$%^&*+/|{};:\"',\\\\<>?]*"
    不能包含字符: `~!#$%^&*+|{};:"',\/<>?
    """
    
    # YANG模型中不允许的字符（不包括在替换映射中的字符）
    FORBIDDEN_CHARS = r'`~!#$%^&*+/|{};"\'\\'
    
    # 字符替换映射表
    CHAR_REPLACEMENT_MAP = {
        ' ': '_',           # 空格替换为下划线
        '-': '_',           # 连字符替换为下划线（可选）
        '(': '_',           # 左括号
        ')': '_',           # 右括号
        '[': '_',           # 左方括号
        ']': '_',           # 右方括号
        '.': '_',           # 点号
        ':': '_',           # 冒号替换为下划线
        '<': '_',           # 小于号替换为下划线
        '>': '_',           # 大于号替换为下划线
    }
    
    def __init__(self):
        """初始化名称验证器"""
        self.name_mapping: Dict[str, str] = {}  # 原始名称到规范化名称的映射
        self.used_names: Set[str] = set()       # 已使用的规范化名称集合
        self.forbidden_pattern = re.compile(f'[{re.escape(self.FORBIDDEN_CHARS)}]')
        
        log(_("ntos_name_validator.initialized"), "debug")
    
    def is_valid_name(self, name: str) -> bool:
        """
        检查名称是否符合NTOS YANG模型要求

        Args:
            name: 待检查的名称

        Returns:
            bool: 名称是否有效
        """
        if not name:
            return False

        # 检查是否包含禁用字符
        if self.forbidden_pattern.search(name):
            return False

        # 检查是否包含需要替换的字符（如空格）
        for char in self.CHAR_REPLACEMENT_MAP.keys():
            if char in name:
                return False

        # 检查长度（通常YANG模型会有长度限制）
        if len(name) > 64:  # 假设最大长度为64
            return False

        return True
    
    def sanitize_name(self, original_name: str, prefix: str = "") -> str:
        """
        规范化名称，移除或替换不允许的字符
        
        Args:
            original_name: 原始名称
            prefix: 可选的前缀，用于避免冲突
            
        Returns:
            str: 规范化后的名称
        """
        if not original_name:
            return "unnamed_object"
            
        # 如果名称已经有效，直接返回
        if self.is_valid_name(original_name) and original_name not in self.used_names:
            self.used_names.add(original_name)
            self.name_mapping[original_name] = original_name
            return original_name
        
        # 开始规范化处理
        sanitized = original_name
        
        # 第一步：应用字符替换映射
        for old_char, new_char in self.CHAR_REPLACEMENT_MAP.items():
            sanitized = sanitized.replace(old_char, new_char)
        
        # 第二步：移除禁用字符
        sanitized = self.forbidden_pattern.sub('', sanitized)
        
        # 第三步：清理连续的下划线
        sanitized = re.sub(r'_+', '_', sanitized)
        
        # 第四步：移除开头和结尾的下划线
        sanitized = sanitized.strip('_')
        
        # 第五步：确保名称不为空
        if not sanitized:
            sanitized = "service_object"
        
        # 第六步：添加前缀（如果提供）
        if prefix:
            sanitized = f"{prefix}_{sanitized}"
        
        # 第七步：处理长度限制
        if len(sanitized) > 64:
            sanitized = sanitized[:60] + "_trunc"
        
        # 第八步：处理名称冲突
        final_name = self._resolve_name_conflict(sanitized)
        
        # 记录映射关系
        self.name_mapping[original_name] = final_name
        self.used_names.add(final_name)
        
        # 记录名称变更日志
        if original_name != final_name:
            log(_("ntos_name_validator.name_sanitized", 
                  original=original_name, sanitized=final_name), "info")
        
        return final_name
    
    def _resolve_name_conflict(self, base_name: str) -> str:
        """
        解决名称冲突，通过添加数字后缀
        
        Args:
            base_name: 基础名称
            
        Returns:
            str: 解决冲突后的名称
        """
        if base_name not in self.used_names:
            return base_name
        
        # 尝试添加数字后缀
        counter = 1
        while True:
            candidate = f"{base_name}_{counter}"
            if candidate not in self.used_names:
                log(_("ntos_name_validator.conflict_resolved", 
                      original=base_name, resolved=candidate), "debug")
                return candidate
            counter += 1
            
            # 防止无限循环
            if counter > 1000:
                log(_("ntos_name_validator.conflict_resolution_failed", name=base_name), "error")
                return f"{base_name}_{counter}"
    
    def get_sanitized_name(self, original_name: str) -> Optional[str]:
        """
        获取已规范化的名称
        
        Args:
            original_name: 原始名称
            
        Returns:
            Optional[str]: 规范化后的名称，如果不存在则返回None
        """
        return self.name_mapping.get(original_name)
    
    def get_name_mapping(self) -> Dict[str, str]:
        """
        获取完整的名称映射表
        
        Returns: Dict[str, str]: 原始名称到规范化名称的映射
        """
        return self.name_mapping.copy()
    
    def validate_and_sanitize_service_group_names(self, service_groups: Dict) -> Dict:
        """
        批量验证和规范化服务组名称
        
        Args:
            service_groups: 服务组配置字典
            
        Returns:
            Dict: 规范化后的服务组配置
        """
        if not isinstance(service_groups, dict):
            log(_("ntos_name_validator.invalid_service_groups_type"), "error")
            return service_groups
        
        sanitized_groups = {}
        
        for original_name, group_config in service_groups.items():
            # 规范化服务组名称
            sanitized_name = self.sanitize_name(original_name)
            
            # 复制配置，更新名称
            if isinstance(group_config, dict):
                new_config = group_config.copy()
                new_config['original_name'] = original_name
                new_config['sanitized_name'] = sanitized_name
            else:
                new_config = {
                    'original_name': original_name,
                    'sanitized_name': sanitized_name,
                    'config': group_config
                }
            
            sanitized_groups[sanitized_name] = new_config
        
        log(_("ntos_name_validator.service_groups_sanitized", 
              count=len(service_groups), sanitized=len(sanitized_groups)), "info")
        
        return sanitized_groups
    
    def get_statistics(self) -> Dict[str, int]:
        """
        获取名称处理统计信息
        
        Returns: Dict[str, int]: 统计信息
        """
        changed_names = sum(1 for orig, san in self.name_mapping.items() if orig != san)
        
        return {
            'total_processed': len(self.name_mapping),
            'names_changed': changed_names,
            'names_unchanged': len(self.name_mapping) - changed_names,
            'unique_names': len(self.used_names)
        }
