"""
四层优化策略专用日志记录器
提供详细的优化执行日志和效果分析
"""

import time
import json
from typing import Dict, Any, List, Optional
from datetime import datetime

from engine.utils.logger import log
from engine.utils.i18n import _


class OptimizationLogger:
    """
    四层优化策略专用日志记录器
    
    负责记录四层优化策略的执行过程、效果分析和性能指标，
    提供详细的优化日志用于调试和验证。
    """
    
    def __init__(self):
        """初始化优化日志记录器"""
        self.session_id = f"opt_{int(time.time())}"
        self.start_time = None
        self.end_time = None
        self.optimization_events = []
        self.performance_snapshots = []
        
    def start_optimization_session(self, total_sections: int, context: Dict[str, Any] = None):
        """
        开始优化会话
        
        Args:
            total_sections: 总段落数
            context: 优化上下文
        """
        self.start_time = time.time()
        
        log("🚀 四层优化策略会话开始")
        log(f"   会话ID: {self.session_id}")
        log(f"   开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        log(f"   总段落数: {total_sections}")
        
        if context:
            device_info = f"{context.get('vendor', 'unknown')} {context.get('device_model', 'unknown')} {context.get('device_version', 'unknown')}"
            log(f"   设备信息: {device_info}")
            log(f"   转换模式: {context.get('conversion_mode', 'standard')}")
        
        # 记录优化事件
        self.optimization_events.append({
            'timestamp': self.start_time,
            'event': 'session_start',
            'data': {
                'total_sections': total_sections,
                'context': context or {}
            }
        })
    
    def log_section_classification(self, section_name: str, tier: str, strategy: str, 
                                 confidence: float, reasoning: str = None):
        """
        记录段落分类结果
        
        Args:
            section_name: 段落名称
            tier: 分层结果 (Tier1-4)
            strategy: 处理策略 (SKIP/SIMPLIFIED/FULL)
            confidence: 置信度
            reasoning: 分类原因
        """
        log(f"📋 段落分类: {section_name} -> {tier} ({strategy}) 置信度:{confidence:.2f}")
        
        if reasoning:
            log(f"   分类原因: {reasoning}")
        
        # 记录分类事件
        self.optimization_events.append({
            'timestamp': time.time(),
            'event': 'section_classified',
            'data': {
                'section_name': section_name,
                'tier': tier,
                'strategy': strategy,
                'confidence': confidence,
                'reasoning': reasoning
            }
        })
    
    def log_tier_execution(self, tier: str, sections_count: int, processing_time: float, 
                          sections_processed: List[str] = None):
        """
        记录分层执行结果
        
        Args:
            tier: 分层名称
            sections_count: 处理段落数
            processing_time: 处理时间
            sections_processed: 已处理的段落列表
        """
        log(f"⚡ {tier} 执行完成: {sections_count}个段落, 耗时{processing_time:.2f}秒")
        
        if sections_processed:
            log(f"   处理段落: {', '.join(sections_processed[:5])}" + 
                (f" 等{len(sections_processed)}个" if len(sections_processed) > 5 else ""), "debug")
        
        # 记录执行事件
        self.optimization_events.append({
            'timestamp': time.time(),
            'event': 'tier_executed',
            'data': {
                'tier': tier,
                'sections_count': sections_count,
                'processing_time': processing_time,
                'sections_processed': sections_processed or []
            }
        })
    
    def log_performance_snapshot(self, stage: str, cpu_usage: float = None, 
                               memory_usage: float = None, processing_speed: float = None):
        """
        记录性能快照
        
        Args:
            stage: 阶段名称
            cpu_usage: CPU使用率
            memory_usage: 内存使用量(MB)
            processing_speed: 处理速度(项/秒)
        """
        snapshot = {
            'timestamp': time.time(),
            'stage': stage,
            'cpu_usage': cpu_usage,
            'memory_usage': memory_usage,
            'processing_speed': processing_speed
        }
        
        self.performance_snapshots.append(snapshot)
        
        log(f"📊 性能快照 [{stage}]: CPU:{cpu_usage:.1f}% 内存:{memory_usage:.1f}MB 速度:{processing_speed:.1f}项/秒")
    
    def log_quality_check(self, check_type: str, score: float, details: Dict[str, Any] = None):
        """
        记录质量检查结果
        
        Args:
            check_type: 检查类型
            score: 质量分数
            details: 详细信息
        """
        status = "✅ 通过" if score >= 0.95 else "⚠️ 需改进" if score >= 0.8 else "❌ 不合格"
        log(f"🛡️ 质量检查 [{check_type}]: {score:.3f} ({score*100:.1f}%) {status}")
        
        if details:
            for key, value in details.items():
                log(f"   {key}: {value}")
        
        # 记录质量检查事件
        self.optimization_events.append({
            'timestamp': time.time(),
            'event': 'quality_check',
            'data': {
                'check_type': check_type,
                'score': score,
                'details': details or {}
            }
        })
    
    def log_optimization_decision(self, item_name: str, decision: str, reason: str, 
                                time_saved: float = 0.0, quality_impact: float = 0.0):
        """
        记录优化决策
        
        Args:
            item_name: 项目名称
            decision: 决策结果 (SKIP/SIMPLIFIED/FULL)
            reason: 决策原因
            time_saved: 节省时间
            quality_impact: 质量影响
        """
        decision_icon = {"SKIP": "⏭️", "SIMPLIFIED": "⚡", "FULL": "🔧"}.get(decision, "❓")
        
        log(f"{decision_icon} 优化决策 [{item_name}]: {decision}")
        log(f"   原因: {reason}")
        if time_saved > 0:
            log(f"   节省时间: {time_saved:.2f}秒")
        if quality_impact != 0:
            log(f"   质量影响: {quality_impact:+.2f}")
        
        # 记录决策事件
        self.optimization_events.append({
            'timestamp': time.time(),
            'event': 'optimization_decision',
            'data': {
                'item_name': item_name,
                'decision': decision,
                'reason': reason,
                'time_saved': time_saved,
                'quality_impact': quality_impact
            }
        })
    
    def end_optimization_session(self, final_metrics: Dict[str, Any]):
        """
        结束优化会话
        
        Args:
            final_metrics: 最终指标
        """
        self.end_time = time.time()
        total_time = self.end_time - self.start_time if self.start_time else 0
        
        log("🏁 四层优化策略会话结束")
        log(f"   会话ID: {self.session_id}")
        log(f"   结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        log(f"   总耗时: {total_time:.2f}秒")
        
        # 记录最终指标
        if final_metrics:
            log("📊 最终优化指标:")
            for key, value in final_metrics.items():
                if isinstance(value, float):
                    log(f"   {key}: {value:.3f}")
                else:
                    log(f"   {key}: {value}")
        
        # 记录会话结束事件
        self.optimization_events.append({
            'timestamp': self.end_time,
            'event': 'session_end',
            'data': {
                'total_time': total_time,
                'final_metrics': final_metrics
            }
        })
    
    def generate_optimization_report(self) -> Dict[str, Any]:
        """
        生成优化报告
        
        Returns:
            Dict[str, Any]: 优化报告
        """
        if not self.start_time:
            return {"status": "no_session"}
        
        total_time = (self.end_time or time.time()) - self.start_time
        
        # 统计事件
        event_counts = {}
        for event in self.optimization_events:
            event_type = event['event']
            event_counts[event_type] = event_counts.get(event_type, 0) + 1
        
        # 分析性能快照
        performance_analysis = {}
        if self.performance_snapshots:
            cpu_values = [s['cpu_usage'] for s in self.performance_snapshots if s['cpu_usage'] is not None]
            memory_values = [s['memory_usage'] for s in self.performance_snapshots if s['memory_usage'] is not None]
            speed_values = [s['processing_speed'] for s in self.performance_snapshots if s['processing_speed'] is not None]
            
            if cpu_values:
                performance_analysis['cpu'] = {
                    'avg': sum(cpu_values) / len(cpu_values),
                    'max': max(cpu_values),
                    'min': min(cpu_values)
                }
            
            if memory_values:
                performance_analysis['memory'] = {
                    'avg': sum(memory_values) / len(memory_values),
                    'max': max(memory_values),
                    'min': min(memory_values)
                }
            
            if speed_values:
                performance_analysis['speed'] = {
                    'avg': sum(speed_values) / len(speed_values),
                    'max': max(speed_values),
                    'min': min(speed_values)
                }
        
        return {
            'session_id': self.session_id,
            'start_time': self.start_time,
            'end_time': self.end_time,
            'total_time': total_time,
            'event_counts': event_counts,
            'performance_analysis': performance_analysis,
            'total_events': len(self.optimization_events),
            'total_snapshots': len(self.performance_snapshots)
        }
    
    def save_detailed_log(self, file_path: str = None):
        """
        保存详细日志到文件
        
        Args:
            file_path: 日志文件路径
        """
        if not file_path:
            file_path = f"output/optimization_log_{self.session_id}.json"
        
        try:
            detailed_log = {
                'session_info': {
                    'session_id': self.session_id,
                    'start_time': self.start_time,
                    'end_time': self.end_time,
                    'total_time': (self.end_time or time.time()) - self.start_time if self.start_time else 0
                },
                'optimization_events': self.optimization_events,
                'performance_snapshots': self.performance_snapshots,
                'report': self.generate_optimization_report()
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(detailed_log, f, indent=2, ensure_ascii=False)
            
            log(f"📄 详细优化日志已保存: {file_path}")
            
        except Exception as e:
            log(f"保存优化日志失败: {str(e)}")
