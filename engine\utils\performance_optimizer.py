"""
性能优化工具

提供各种性能优化功能，包括：
- 对象池管理
- 缓存机制
- 批处理优化
- 内存管理
"""

import time
import threading
from typing import Dict, Any, List, Optional, Callable, TypeVar, Generic
from functools import wraps, lru_cache
from collections import defaultdict, deque
from dataclasses import dataclass
import weakref

T = TypeVar('T')


@dataclass
class PerformanceMetrics:
    """性能指标"""
    operation_count: int = 0
    total_time: float = 0.0
    average_time: float = 0.0
    max_time: float = 0.0
    min_time: float = float('inf')
    cache_hits: int = 0
    cache_misses: int = 0


class ObjectPool(Generic[T]):
    """对象池实现"""
    
    def __init__(self, factory: Callable[[], T], max_size: int = 100):
        self.factory = factory
        self.max_size = max_size
        self.pool = deque()
        self.lock = threading.Lock()
        self.created_count = 0
        self.reused_count = 0
    
    def get(self) -> T:
        """从池中获取对象"""
        with self.lock:
            if self.pool:
                self.reused_count += 1
                return self.pool.popleft()
            else:
                self.created_count += 1
                return self.factory()
    
    def put(self, obj: T) -> None:
        """将对象返回池中"""
        with self.lock:
            if len(self.pool) < self.max_size:
                # 重置对象状态（如果有reset方法）
                if hasattr(obj, 'reset'):
                    obj.reset()
                self.pool.append(obj)
    
    def get_stats(self) -> Dict[str, int]:
        """获取池统计信息"""
        with self.lock:
            return {
                "pool_size": len(self.pool),
                "created_count": self.created_count,
                "reused_count": self.reused_count,
                "reuse_rate": self.reused_count / (self.created_count + self.reused_count) if (self.created_count + self.reused_count) > 0 else 0
            }


class SmartCache:
    """智能缓存实现"""
    
    def __init__(self, max_size: int = 1000, ttl: float = 3600):
        self.max_size = max_size
        self.ttl = ttl
        self.cache = {}
        self.access_times = {}
        self.creation_times = {}
        self.lock = threading.RLock()
        self.hits = 0
        self.misses = 0
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self.lock:
            current_time = time.time()
            
            if key in self.cache:
                # 检查是否过期
                if current_time - self.creation_times[key] > self.ttl:
                    self._remove_key(key)
                    self.misses += 1
                    return None
                
                # 更新访问时间
                self.access_times[key] = current_time
                self.hits += 1
                return self.cache[key]
            
            self.misses += 1
            return None
    
    def put(self, key: str, value: Any) -> None:
        """设置缓存值"""
        with self.lock:
            current_time = time.time()
            
            # 如果缓存已满，移除最久未访问的项
            if len(self.cache) >= self.max_size and key not in self.cache:
                self._evict_lru()
            
            self.cache[key] = value
            self.access_times[key] = current_time
            self.creation_times[key] = current_time
    
    def _remove_key(self, key: str) -> None:
        """移除缓存项"""
        if key in self.cache:
            del self.cache[key]
            del self.access_times[key]
            del self.creation_times[key]
    
    def _evict_lru(self) -> None:
        """移除最久未访问的项"""
        if not self.access_times:
            return
        
        lru_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
        self._remove_key(lru_key)
    
    def clear(self) -> None:
        """清空缓存"""
        with self.lock:
            self.cache.clear()
            self.access_times.clear()
            self.creation_times.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self.lock:
            total_requests = self.hits + self.misses
            hit_rate = self.hits / total_requests if total_requests > 0 else 0
            
            return {
                "size": len(self.cache),
                "max_size": self.max_size,
                "hits": self.hits,
                "misses": self.misses,
                "hit_rate": hit_rate,
                "ttl": self.ttl
            }


class BatchProcessor:
    """批处理器"""
    
    def __init__(self, batch_size: int = 100, flush_interval: float = 1.0):
        self.batch_size = batch_size
        self.flush_interval = flush_interval
        self.batches = defaultdict(list)
        self.processors = {}
        self.last_flush = defaultdict(float)
        self.lock = threading.Lock()
    
    def register_processor(self, batch_type: str, processor: Callable[[List[Any]], Any]) -> None:
        """注册批处理器"""
        self.processors[batch_type] = processor
    
    def add_item(self, batch_type: str, item: Any) -> Optional[Any]:
        """添加项到批处理队列"""
        with self.lock:
            self.batches[batch_type].append(item)
            current_time = time.time()
            
            # 检查是否需要刷新
            should_flush = (
                len(self.batches[batch_type]) >= self.batch_size or
                current_time - self.last_flush[batch_type] >= self.flush_interval
            )
            
            if should_flush:
                return self._flush_batch(batch_type)
            
            return None
    
    def _flush_batch(self, batch_type: str) -> Optional[Any]:
        """刷新批处理队列"""
        if batch_type not in self.processors:
            return None
        
        batch = self.batches[batch_type]
        if not batch:
            return None
        
        # 处理批次
        result = self.processors[batch_type](batch)
        
        # 清空批次
        self.batches[batch_type].clear()
        self.last_flush[batch_type] = time.time()
        
        return result
    
    def flush_all(self) -> Dict[str, Any]:
        """刷新所有批处理队列"""
        results = {}
        with self.lock:
            for batch_type in list(self.batches.keys()):
                result = self._flush_batch(batch_type)
                if result is not None:
                    results[batch_type] = result
        return results


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.metrics = defaultdict(PerformanceMetrics)
        self.lock = threading.Lock()
    
    def record_operation(self, operation_name: str, execution_time: float) -> None:
        """记录操作性能"""
        with self.lock:
            metric = self.metrics[operation_name]
            metric.operation_count += 1
            metric.total_time += execution_time
            metric.average_time = metric.total_time / metric.operation_count
            metric.max_time = max(metric.max_time, execution_time)
            metric.min_time = min(metric.min_time, execution_time)
    
    def record_cache_hit(self, cache_name: str) -> None:
        """记录缓存命中"""
        with self.lock:
            self.metrics[cache_name].cache_hits += 1
    
    def record_cache_miss(self, cache_name: str) -> None:
        """记录缓存未命中"""
        with self.lock:
            self.metrics[cache_name].cache_misses += 1
    
    def get_metrics(self, operation_name: str) -> PerformanceMetrics:
        """获取操作指标"""
        with self.lock:
            return self.metrics[operation_name]
    
    def get_all_metrics(self) -> Dict[str, PerformanceMetrics]:
        """获取所有指标"""
        with self.lock:
            return dict(self.metrics)
    
    def reset_metrics(self, operation_name: Optional[str] = None) -> None:
        """重置指标"""
        with self.lock:
            if operation_name:
                if operation_name in self.metrics:
                    del self.metrics[operation_name]
            else:
                self.metrics.clear()


# 全局实例
_performance_monitor = PerformanceMonitor()
_smart_cache = SmartCache()
_object_pools = {}


def get_performance_monitor() -> PerformanceMonitor:
    """获取性能监控器实例"""
    return _performance_monitor


def get_smart_cache() -> SmartCache:
    """获取智能缓存实例"""
    return _smart_cache


def get_object_pool(pool_name: str, factory: Callable[[], T], max_size: int = 100) -> ObjectPool[T]:
    """获取对象池实例"""
    if pool_name not in _object_pools:
        _object_pools[pool_name] = ObjectPool(factory, max_size)
    return _object_pools[pool_name]


def performance_optimized(operation_name: str = None, use_cache: bool = False, cache_key_func: Callable = None):
    """性能优化装饰器"""
    def decorator(func):
        nonlocal operation_name
        if operation_name is None:
            operation_name = f"{func.__module__}.{func.__name__}"
        
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 缓存检查
            if use_cache and cache_key_func:
                cache_key = cache_key_func(*args, **kwargs)
                cached_result = _smart_cache.get(cache_key)
                if cached_result is not None:
                    _performance_monitor.record_cache_hit(operation_name)
                    return cached_result
                _performance_monitor.record_cache_miss(operation_name)
            
            # 执行操作并记录性能
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                
                # 缓存结果
                if use_cache and cache_key_func:
                    cache_key = cache_key_func(*args, **kwargs)
                    _smart_cache.put(cache_key, result)
                
                return result
            finally:
                execution_time = time.time() - start_time
                _performance_monitor.record_operation(operation_name, execution_time)
        
        return wrapper
    return decorator


def batch_process(batch_type: str, batch_size: int = 100):
    """批处理装饰器"""
    def decorator(func):
        processor = BatchProcessor(batch_size)
        processor.register_processor(batch_type, func)
        
        @wraps(func)
        def wrapper(items):
            if isinstance(items, list) and len(items) > batch_size:
                # 分批处理
                results = []
                for i in range(0, len(items), batch_size):
                    batch = items[i:i + batch_size]
                    result = func(batch)
                    results.extend(result if isinstance(result, list) else [result])
                return results
            else:
                return func(items)
        
        return wrapper
    return decorator


def memory_efficient(use_generators: bool = True):
    """内存效率装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            result = func(*args, **kwargs)
            
            # 如果结果是大列表且启用生成器，转换为生成器
            if use_generators and isinstance(result, list) and len(result) > 1000:
                return (item for item in result)
            
            return result
        
        return wrapper
    return decorator


# 性能优化工具函数
def optimize_data_structure(data: Any) -> Any:
    """优化数据结构"""
    if isinstance(data, list) and len(data) > 1000:
        # 大列表转换为生成器
        return (item for item in data)
    elif isinstance(data, dict) and len(data) > 1000:
        # 大字典考虑使用更高效的数据结构
        return data  # 暂时保持不变
    return data


def get_performance_summary() -> Dict[str, Any]:
    """获取性能摘要"""
    monitor = get_performance_monitor()
    cache = get_smart_cache()
    
    return {
        "performance_metrics": monitor.get_all_metrics(),
        "cache_stats": cache.get_stats(),
        "object_pool_stats": {name: pool.get_stats() for name, pool in _object_pools.items()},
        "timestamp": time.time()
    }
