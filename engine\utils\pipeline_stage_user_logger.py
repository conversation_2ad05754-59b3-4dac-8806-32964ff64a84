#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
管道阶段用户日志记录器
为新架构的管道阶段提供用户友好的日志记录功能
"""

import datetime
from typing import Dict, Any, List, Optional
from .logger import user_log
from .i18n import _


class PipelineStageUserLogger:
    """管道阶段用户日志记录器"""
    
    def __init__(self, language: str = "zh-CN"):
        """
        初始化用户日志记录器
        
        Args:
            language: 语言代码
        """
        self.language = language
        self.stage_start_times = {}  # 记录各阶段开始时间
        
    def log_stage_start(self, stage_name: str, input_count: int = 0, stage_description: str = None):
        """
        记录阶段开始
        
        Args:
            stage_name: 阶段名称
            input_count: 输入项目数量
            stage_description: 阶段描述
        """
        self.stage_start_times[stage_name] = datetime.datetime.now()
        
        # 获取阶段显示名称
        display_name = self._get_stage_display_name(stage_name)
        
        if input_count > 0:
            user_log(_("user.stage.start_with_count", 
                      stage=display_name, 
                      count=input_count,
                      language=self.language))
        else:
            user_log(_("user.stage.start", 
                      stage=display_name,
                      language=self.language))
    
    def log_stage_progress(self, stage_name: str, processed: int, total: int):
        """
        记录阶段进度
        
        Args:
            stage_name: 阶段名称
            processed: 已处理数量
            total: 总数量
        """
        display_name = self._get_stage_display_name(stage_name)
        progress_percent = int((processed / total) * 100) if total > 0 else 0
        
        user_log(_("user.stage.progress", 
                  stage=display_name,
                  processed=processed,
                  total=total,
                  percent=progress_percent,
                  language=self.language))
    
    def log_stage_complete(self, stage_name: str, result_summary: Dict[str, Any]):
        """
        记录阶段完成
        
        Args:
            stage_name: 阶段名称
            result_summary: 结果摘要
        """
        display_name = self._get_stage_display_name(stage_name)
        
        # 计算耗时
        duration = self._get_stage_duration(stage_name)
        
        # 提取关键统计信息
        total = result_summary.get("total", 0)
        success = result_summary.get("converted", result_summary.get("processed", 0))
        failed = result_summary.get("failed", 0)
        skipped = result_summary.get("skipped", 0)
        
        if total > 0:
            user_log(_("user.stage.complete_with_stats",
                      stage=display_name,
                      success=success,
                      total=total,
                      duration=duration,
                      language=self.language))
            
            # 如果有失败或跳过的项目，记录详细信息
            if failed > 0:
                user_log(_("user.stage.failed_items",
                          stage=display_name,
                          failed=failed,
                          language=self.language))
            
            if skipped > 0:
                user_log(_("user.stage.skipped_items",
                          stage=display_name,
                          skipped=skipped,
                          language=self.language))
        else:
            user_log(_("user.stage.complete",
                      stage=display_name,
                      duration=duration,
                      language=self.language))
    
    def log_stage_failure(self, stage_name: str, error_details: str):
        """
        记录阶段失败
        
        Args:
            stage_name: 阶段名称
            error_details: 错误详情
        """
        display_name = self._get_stage_display_name(stage_name)
        duration = self._get_stage_duration(stage_name)
        
        user_log(_("user.stage.failed",
                  stage=display_name,
                  error=error_details,
                  duration=duration,
                  language=self.language), level="error")
    
    def log_item_success(self, item_type: str, item_name: str, details: Dict[str, Any] = None):
        """
        记录单个项目转换成功
        
        Args:
            item_type: 项目类型
            item_name: 项目名称
            details: 详细信息
        """
        if details and details.get("converted_name"):
            user_log(_("user.item.success_with_mapping",
                      type=item_type,
                      original_name=item_name,
                      converted_name=details["converted_name"],
                      language=self.language), level="debug")
        else:
            user_log(_("user.item.success",
                      type=item_type,
                      name=item_name,
                      language=self.language), level="debug")
    
    def log_item_failure(self, item_type: str, item_name: str, reason: str, suggestion: str = None):
        """
        记录单个项目转换失败
        
        Args:
            item_type: 项目类型
            item_name: 项目名称
            reason: 失败原因
            suggestion: 建议解决方案
        """
        user_log(_("user.item.failed",
                  type=item_type,
                  name=item_name,
                  reason=reason,
                  language=self.language), level="warning")
        
        if suggestion:
            user_log(_("user.item.suggestion",
                      suggestion=suggestion,
                      language=self.language), level="info")
    
    def log_item_warning(self, item_type: str, item_name: str, warning: str, impact: str = None):
        """
        记录单个项目警告
        
        Args:
            item_type: 项目类型
            item_name: 项目名称
            warning: 警告信息
            impact: 影响说明
        """
        user_log(_("user.item.warning",
                  type=item_type,
                  name=item_name,
                  warning=warning,
                  language=self.language), level="warning")
        
        if impact:
            user_log(_("user.item.impact",
                      impact=impact,
                      language=self.language), level="info")
    
    def _get_stage_display_name(self, stage_name: str) -> str:
        """
        获取阶段显示名称
        
        Args:
            stage_name: 阶段名称
            
        Returns:
            str: 显示名称
        """
        stage_display_names = {
            "operation_mode": _("user.stage.name.operation_mode", language=self.language),
            "interface_processing": _("user.stage.name.interface_processing", language=self.language),
            "address_processing": _("user.stage.name.address_processing", language=self.language),
            "address_group_processing": _("user.stage.name.address_group_processing", language=self.language),
            "service_processing": _("user.stage.name.service_processing", language=self.language),
            "service_group_processing": _("user.stage.name.service_group_processing", language=self.language),
            "zone_processing": _("user.stage.name.zone_processing", language=self.language),
            "time_range_processing": _("user.stage.name.time_range_processing", language=self.language),
            "dns_processing": _("user.stage.name.dns_processing", language=self.language),
            "static_route_processing": _("user.stage.name.static_route_processing", language=self.language),
            "policy_processing": _("user.stage.name.policy_processing", language=self.language),
            "xml_generation": _("user.stage.name.xml_generation", language=self.language),
            "yang_validation": _("user.stage.name.yang_validation", language=self.language),
            "encryption": _("user.stage.name.encryption", language=self.language)
        }
        
        return stage_display_names.get(stage_name, stage_name)
    
    def _get_stage_duration(self, stage_name: str) -> str:
        """
        获取阶段持续时间
        
        Args:
            stage_name: 阶段名称
            
        Returns:
            str: 格式化的持续时间
        """
        if stage_name not in self.stage_start_times:
            return _("user.time.unknown", language=self.language)
        
        start_time = self.stage_start_times[stage_name]
        duration = datetime.datetime.now() - start_time
        seconds = duration.total_seconds()
        
        if seconds < 1:
            return f"{seconds*1000:.0f}ms"
        elif seconds < 60:
            return f"{seconds:.2f}s"
        elif seconds < 3600:
            minutes = seconds / 60
            return f"{minutes:.2f}m"
        else:
            hours = seconds / 3600
            return f"{hours:.2f}h"


# 全局实例
_pipeline_logger = None

def get_pipeline_user_logger(language: str = "zh-CN") -> PipelineStageUserLogger:
    """
    获取管道阶段用户日志记录器实例
    
    Args:
        language: 语言代码
        
    Returns:
        PipelineStageUserLogger: 记录器实例
    """
    global _pipeline_logger
    if _pipeline_logger is None or _pipeline_logger.language != language:
        _pipeline_logger = PipelineStageUserLogger(language)
    return _pipeline_logger
