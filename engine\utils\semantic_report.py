#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
语义验证报告生成器
负责生成结构化的语义验证报告，包括控制台汇总和详细报告文件
"""

import os
import json
from datetime import datetime
from typing import Dict, List, Tuple, Any
from collections import defaultdict, Counter

from engine.utils.i18n import _


class SemanticWarning:
    """语义验证警告数据结构"""
    
    def __init__(self, category: str, severity: str, message: str, 
                 policy_id: str = None, object_name: str = None, 
                 object_type: str = None, details: Dict = None):
        self.category = category  # 'interface', 'address', 'service', 'policy', 'dns'
        self.severity = severity  # 'critical', 'warning', 'info'
        self.message = message
        self.policy_id = policy_id
        self.object_name = object_name
        self.object_type = object_type
        self.details = details or {}
        self.timestamp = datetime.now()


class SemanticReportGenerator:
    """语义验证报告生成器"""
    
    def __init__(self, config_file_name: str = None):
        self.config_file_name = config_file_name or "unknown_config"
        self.warnings: List[SemanticWarning] = []
        
        # 严重程度映射
        self.severity_map = {
            'critical': {'emoji': '🔴', 'label': _('severity.critical'), 'priority': 1},
            'warning': {'emoji': '🟡', 'label': _('severity.warning'), 'priority': 2},
            'info': {'emoji': '🟢', 'label': _('severity.info'), 'priority': 3}
        }
        
        # 分类映射
        self.category_map = {
            'interface': {'label': _('category.interface_issues'), 'icon': '🔌'},
            'address': {'label': _('category.address_issues'), 'icon': '📍'},
            'service': {'label': _('category.service_issues'), 'icon': '🔧'},
            'policy': {'label': _('category.policy_issues'), 'icon': '📋'},
            'dns': {'label': _('category.dns_issues'), 'icon': '🌐'},
            'other': {'label': _('category.other_issues'), 'icon': '❓'}
        }
    
    def add_warning(self, warning: SemanticWarning):
        """添加警告"""
        self.warnings.append(warning)
    
    def add_interface_warning(self, policy_id: str, interface_type: str, interface_name: str):
        """添加接口引用警告"""
        warning = SemanticWarning(
            category='interface',
            severity='critical',
            message=_('warning.interface_reference_detailed', 
                     policy_id=policy_id, intf_type=interface_type, interface=interface_name),
            policy_id=policy_id,
            object_name=interface_name,
            object_type=interface_type,
            details={'interface_type': interface_type}
        )
        self.add_warning(warning)
    
    def add_address_warning(self, policy_id: str, address_type: str, address_name: str):
        """添加地址对象引用警告"""
        warning = SemanticWarning(
            category='address',
            severity='warning',
            message=_('warning.address_reference_detailed',
                     policy_id=policy_id, addr_type=address_type, address=address_name),
            policy_id=policy_id,
            object_name=address_name,
            object_type=address_type,
            details={'address_type': address_type}
        )
        self.add_warning(warning)
    
    def add_service_warning(self, policy_id: str, service_name: str):
        """添加服务对象引用警告"""
        warning = SemanticWarning(
            category='service',
            severity='warning',
            message=_('warning.service_reference_detailed',
                     policy_id=policy_id, service=service_name),
            policy_id=policy_id,
            object_name=service_name,
            object_type='service',
            details={}
        )
        self.add_warning(warning)
    
    def add_policy_log_warnings(self, policy_ids: List[str]):
        """添加策略日志设置警告"""
        if len(policy_ids) <= 5:
            policy_list = ", ".join(policy_ids)
            message = _('warning.policies_missing_log_setting_detailed',
                       count=len(policy_ids), policies=policy_list)
        else:
            policy_list = ", ".join(policy_ids[:5])
            message = _('warning.policies_missing_log_setting_summary',
                       count=len(policy_ids), sample_policies=policy_list)
        
        warning = SemanticWarning(
            category='policy',
            severity='info',
            message=message,
            details={'policy_ids': policy_ids, 'issue_type': 'missing_log'}
        )
        self.add_warning(warning)
    
    def add_dns_warning(self, dns_issue_type: str):
        """添加DNS配置警告"""
        message_key = f'warning.dns_{dns_issue_type}'
        warning = SemanticWarning(
            category='dns',
            severity='info',
            message=_(message_key),
            details={'dns_issue': dns_issue_type}
        )
        self.add_warning(warning)
    
    def generate_statistics(self) -> Dict[str, Any]:
        """生成统计信息"""
        stats = {
            'total_warnings': len(self.warnings),
            'by_severity': Counter(w.severity for w in self.warnings),
            'by_category': Counter(w.category for w in self.warnings),
            'affected_policies': len(set(w.policy_id for w in self.warnings if w.policy_id)),
            'missing_objects': defaultdict(set)
        }
        
        # 统计缺失对象
        for warning in self.warnings:
            if warning.object_name and warning.category in ['interface', 'address', 'service']:
                stats['missing_objects'][warning.category].add(warning.object_name)
        
        # 转换set为list以便JSON序列化
        for category in stats['missing_objects']:
            stats['missing_objects'][category] = list(stats['missing_objects'][category])
        
        return stats
    
    def generate_console_summary(self) -> str:
        """生成控制台简洁汇总"""
        if not self.warnings:
            return _('semantic.validation_passed')
        
        stats = self.generate_statistics()
        
        # 按严重程度分组
        critical_count = stats['by_severity'].get('critical', 0)
        warning_count = stats['by_severity'].get('warning', 0) 
        info_count = stats['by_severity'].get('info', 0)
        
        summary_lines = []
        summary_lines.append(_('semantic.validation_completed_with_issues'))
        summary_lines.append("")
        
        if critical_count > 0:
            critical_categories = [cat for cat, warnings in stats['by_category'].items()
                                 if any(w.severity == 'critical' and w.category == cat for w in self.warnings)]
            summary_lines.append(f"🔴 {_('severity.critical_issues')} {_('semantic.summary.critical_issues_with_policies', count=critical_count, categories=', '.join(critical_categories), affected_policies=stats['affected_policies'])}")
        
        if warning_count > 0:
            warning_categories = [cat for cat, warnings in stats['by_category'].items()
                                if any(w.severity == 'warning' and w.category == cat for w in self.warnings)]
            summary_lines.append(f"🟡 {_('severity.warning_issues')} {_('semantic.summary.warning_issues_count', count=warning_count, categories=', '.join(warning_categories))}")
        
        if info_count > 0:
            info_categories = [cat for cat, warnings in stats['by_category'].items()
                             if any(w.severity == 'info' and w.category == cat for w in self.warnings)]
            summary_lines.append(f"🟢 {_('severity.info_issues')} {_('semantic.summary.info_issues_count', count=info_count, categories=', '.join(info_categories))}")
        
        return "\n".join(summary_lines)
    
    def generate_detailed_report(self, output_dir: str = "output") -> str:
        """生成详细报告文件"""
        if not self.warnings:
            return None
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 生成报告文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_filename = f"semantic_validation_report_{timestamp}.txt"
        report_path = os.path.join(output_dir, report_filename)
        
        # 生成报告内容
        report_content = self._generate_report_content()
        
        # 写入文件
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        return report_path
    
    def _generate_report_content(self) -> str:
        """生成详细报告内容"""
        lines = []
        stats = self.generate_statistics()
        
        # 报告头部
        lines.append("=" * 60)
        lines.append(_('report.title'))
        lines.append("=" * 60)
        lines.append(f"{_('report.generated_time')}: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        lines.append(f"{_('report.config_file')}: {self.config_file_name}")
        lines.append("")
        
        # 统计信息
        lines.append(f"📊 {_('report.statistics')}:")
        lines.append(f"- {_('report.total_warnings')}: {stats['total_warnings']}")
        lines.append(f"- {_('report.affected_policies')}: {_('report.affected_policies_count', count=stats['affected_policies'])}")
        lines.append(f"- {_('report.problem_categories')}: {_('report.problem_categories_count', count=len(stats['by_category']))}")
        lines.append("")
        
        # 按严重程度分组显示
        for severity in ['critical', 'warning', 'info']:
            severity_warnings = [w for w in self.warnings if w.severity == severity]
            if not severity_warnings:
                continue
            
            severity_info = self.severity_map[severity]
            lines.append(f"{severity_info['emoji']} {severity_info['label']} - {_('report.needs_immediate_attention' if severity == 'critical' else 'report.recommended_to_fix' if severity == 'warning' else 'report.optional_optimization')}:")
            
            # 按类别分组
            category_groups = defaultdict(list)
            for warning in severity_warnings:
                category_groups[warning.category].append(warning)
            
            for category, category_warnings in category_groups.items():
                category_info = self.category_map.get(category, self.category_map['other'])
                lines.append(f"┌─ {category_info['icon']} {category_info['label']} ({_('report.category_issues_count', count=len(category_warnings))})")
                
                if category in ['interface', 'address', 'service']:
                    # 对象引用类问题的特殊处理
                    self._add_reference_issue_details(lines, category_warnings, category)
                elif category == 'policy':
                    # 策略问题的特殊处理
                    self._add_policy_issue_details(lines, category_warnings)
                else:
                    # 其他问题的通用处理
                    for warning in category_warnings:
                        lines.append(f"├─ {warning.message}")
                
                lines.append("└─")
                lines.append("")
        
        # 处理建议
        lines.append(f"💡 {_('report.recommendations')}:")
        lines.append(f"1. 【{_('report.priority_1')}】{_('report.recommendation_1')}")
        lines.append(f"2. 【{_('report.priority_2')}】{_('report.recommendation_2')}")
        lines.append(f"3. 【{_('report.priority_3')}】{_('report.recommendation_3')}")
        lines.append("")
        
        return "\n".join(lines)
    
    def _add_reference_issue_details(self, lines: List[str], warnings: List[SemanticWarning], category: str):
        """添加对象引用问题的详细信息"""
        # 统计缺失对象
        missing_objects = Counter(w.object_name for w in warnings)
        affected_policies = set(w.policy_id for w in warnings if w.policy_id)
        
        lines.append(f"├─ {_('report.missing_objects')}: {', '.join(list(missing_objects.keys())[:5])}{'...' if len(missing_objects) > 5 else ''}")
        lines.append(f"├─ {_('report.affected_policies')}: {_('report.affected_policies_count', count=len(affected_policies))}")
        lines.append(f"└─ {_('report.policy_list')}: {', '.join(sorted(affected_policies, key=int)[:10])}{'...' if len(affected_policies) > 10 else ''}")
    
    def _add_policy_issue_details(self, lines: List[str], warnings: List[SemanticWarning]):
        """添加策略问题的详细信息"""
        for warning in warnings:
            if 'policy_ids' in warning.details:
                policy_ids = warning.details['policy_ids']
                lines.append(f"├─ {_('report.issue_description')}: {warning.details.get('issue_type', 'unknown')}")
                lines.append(f"└─ {_('report.policy_ids')}: {', '.join(policy_ids[:10])}{'...' if len(policy_ids) > 10 else ''}")
            else:
                lines.append(f"├─ {warning.message}")
