#!/usr/bin/env python
# -*- coding: utf-8 -*-

def deduplicate_service_objects(service_objects):
    """
    去重服务对象，确保每个服务名称只出现一次
    
    Args:
        service_objects (list): 服务对象列表
        
    Returns:
        list: 去重后的服务对象列表
    """
    seen_names = set()
    deduped_services = []
    
    for service in service_objects:
        if not isinstance(service, dict) or 'name' not in service:
            continue
            
        service_name = service['name']
        if service_name not in seen_names:
            seen_names.add(service_name)
            deduped_services.append(service)
            print(f"保留服务对象: {service_name}")
        else:
            print(f"跳过重复服务对象: {service_name}")
    
    print(f"去重完成：{len(service_objects)} -> {len(deduped_services)}")
    return deduped_services
