# -*- coding: utf-8 -*-
"""
服务映射验证工具
验证FortiGate服务到NTOS服务映射的准确性，包括协议类型和端口号匹配
"""

import json
import os
from typing import Dict, List, Tuple, Optional, Any
from engine.utils.logger import log
from engine.utils.i18n import safe_translate


class ServiceMappingValidator:
    """服务映射验证器"""
    
    def __init__(self):
        """初始化验证器"""
        self.fortigate_services = {}
        self.ntos_services = {}
        self.validation_results = []
        self.load_service_definitions()
    
    def load_service_definitions(self):
        """加载服务定义文件"""
        try:
            # 加载FortiGate预定义服务
            current_dir = os.path.dirname(os.path.abspath(__file__))
            fortigate_file = os.path.join(current_dir, "..", "data", "services", "fortigate_predefined_services.json")
            
            if os.path.exists(fortigate_file):
                with open(fortigate_file, 'r', encoding='utf-8') as f:
                    self.fortigate_services = json.load(f)
                log(safe_translate("service_validator.fortigate_services_loaded", count=len(self.fortigate_services)), "info")
            
            # 加载NTOS内置服务
            ntos_file = os.path.join(current_dir, "..", "data", "services", "ntos_builtin_services.json")
            
            if os.path.exists(ntos_file):
                with open(ntos_file, 'r', encoding='utf-8') as f:
                    self.ntos_services = json.load(f)
                log(safe_translate("service_validator.ntos_services_loaded", count=len(self.ntos_services)), "info")
                
        except Exception as e:
            log(safe_translate("service_validator.load_error", error=str(e)), "error")
    
    def validate_mapping(self, fortigate_service: str, ntos_service: str) -> Dict[str, Any]:
        """
        验证单个服务映射的准确性
        
        Args:
            fortigate_service: FortiGate服务名称
            ntos_service: NTOS服务名称
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        result = {
            "fortigate_service": fortigate_service,
            "ntos_service": ntos_service,
            "is_valid": False,
            "protocol_match": False,
            "port_match": False,
            "issues": [],
            "recommendations": []
        }
        
        # 获取服务定义
        fg_def = self.fortigate_services.get(fortigate_service.upper())
        ntos_def = self.ntos_services.get(ntos_service.lower())
        
        if not fg_def:
            result["issues"].append(f"FortiGate服务 '{fortigate_service}' 未找到定义")
            return result
            
        if not ntos_def:
            result["issues"].append(f"NTOS服务 '{ntos_service}' 未找到定义")
            return result
        
        # 验证协议匹配
        fg_protocols = self._parse_protocols(fg_def.get("protocol", ""))
        ntos_protocol = ntos_def.get("protocol", "").upper()
        
        if ntos_protocol in fg_protocols or self._is_protocol_compatible(fg_protocols, ntos_protocol):
            result["protocol_match"] = True
        else:
            result["issues"].append(f"协议不匹配: FortiGate({fg_protocols}) vs NTOS({ntos_protocol})")
        
        # 验证端口匹配
        if "ports" in fg_def and "ports" in ntos_def:
            fg_ports = self._parse_ports(fg_def["ports"])
            ntos_ports = self._parse_ports(ntos_def["ports"])
            
            if self._ports_overlap(fg_ports, ntos_ports):
                result["port_match"] = True
            else:
                result["issues"].append(f"端口不匹配: FortiGate({fg_def['ports']}) vs NTOS({ntos_def['ports']})")
        elif "protocol_number" in fg_def and "protocol_number" in ntos_def:
            # IP协议号匹配
            if fg_def["protocol_number"] == ntos_def["protocol_number"]:
                result["port_match"] = True
            else:
                result["issues"].append(f"协议号不匹配: FortiGate({fg_def['protocol_number']}) vs NTOS({ntos_def['protocol_number']})")
        elif "icmp_type" in fg_def and "type" in ntos_def:
            # ICMP类型匹配
            if str(fg_def["icmp_type"]) == str(ntos_def["type"]):
                result["port_match"] = True
            else:
                result["issues"].append(f"ICMP类型不匹配: FortiGate({fg_def['icmp_type']}) vs NTOS({ntos_def['type']})")
        else:
            result["port_match"] = True  # 无端口信息时认为匹配
        
        # 生成建议
        if not result["protocol_match"] or not result["port_match"]:
            result["recommendations"].append("建议创建自定义NTOS服务以确保完全匹配")
            if not result["protocol_match"]:
                result["recommendations"].append(f"使用协议: {fg_protocols[0] if fg_protocols else 'TCP'}")
            if not result["port_match"] and "ports" in fg_def:
                result["recommendations"].append(f"使用端口: {fg_def['ports']}")
        
        result["is_valid"] = result["protocol_match"] and result["port_match"]
        return result
    
    def _parse_protocols(self, protocol_str: str) -> List[str]:
        """解析协议字符串"""
        if not protocol_str:
            return ["TCP"]  # 默认TCP
        
        protocols = []
        for proto in protocol_str.split(","):
            proto = proto.strip().upper()
            if proto == "IP":
                protocols.append("IP")
            elif proto in ["TCP", "UDP", "ICMP", "ICMP6", "ICMPV6"]:
                protocols.append(proto)
        
        return protocols if protocols else ["TCP"]
    
    def _is_protocol_compatible(self, fg_protocols: List[str], ntos_protocol: str) -> bool:
        """检查协议兼容性"""
        if ntos_protocol in fg_protocols:
            return True
        
        # 特殊兼容性规则
        if "IP" in fg_protocols and ntos_protocol in ["TCP", "UDP"]:
            return True
        
        if ntos_protocol == "ICMPV6" and "ICMP6" in fg_protocols:
            return True
            
        return False
    
    def _parse_ports(self, port_str: str) -> List[Tuple[int, int]]:
        """解析端口字符串为端口范围列表"""
        if not port_str:
            return []
        
        ranges = []
        for part in port_str.split(","):
            part = part.strip()
            if "-" in part:
                start, end = part.split("-", 1)
                ranges.append((int(start), int(end)))
            else:
                port = int(part)
                ranges.append((port, port))
        
        return ranges
    
    def _ports_overlap(self, ports1: List[Tuple[int, int]], ports2: List[Tuple[int, int]]) -> bool:
        """检查两个端口范围列表是否有重叠"""
        for start1, end1 in ports1:
            for start2, end2 in ports2:
                if not (end1 < start2 or end2 < start1):
                    return True
        return False
    
    def validate_mapping_table(self, mapping_table: Dict[str, str]) -> List[Dict[str, Any]]:
        """
        验证整个映射表
        
        Args:
            mapping_table: FortiGate到NTOS的服务映射表
            
        Returns:
            List[Dict[str, Any]]: 验证结果列表
        """
        results = []
        
        for fg_service, ntos_service in mapping_table.items():
            result = self.validate_mapping(fg_service, ntos_service)
            results.append(result)
            
        return results
    
    def generate_validation_report(self, results: List[Dict[str, Any]]) -> str:
        """
        生成验证报告
        
        Args:
            results: 验证结果列表
            
        Returns:
            str: 验证报告
        """
        total = len(results)
        valid = sum(1 for r in results if r["is_valid"])
        protocol_matches = sum(1 for r in results if r["protocol_match"])
        port_matches = sum(1 for r in results if r["port_match"])
        
        report = f"""
# 服务映射验证报告

## 总体统计
- 总映射数: {total}
- 完全匹配: {valid} ({valid/total*100:.1f}%)
- 协议匹配: {protocol_matches} ({protocol_matches/total*100:.1f}%)
- 端口匹配: {port_matches} ({port_matches/total*100:.1f}%)

## 问题映射
"""
        
        for result in results:
            if not result["is_valid"]:
                report += f"\n### {result['fortigate_service']} -> {result['ntos_service']}\n"
                for issue in result["issues"]:
                    report += f"- ❌ {issue}\n"
                for rec in result["recommendations"]:
                    report += f"- 💡 {rec}\n"
        
        return report
