#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
用户日志格式化器模块
提供格式化用户日志的功能，包括总结信息
"""

import logging
import datetime
from .i18n import _ # 导入翻译函数
from engine.utils.i18n import _

class UserLogFormatter:
    """用户日志格式化器类"""

    def __init__(self):
        """初始化格式化器"""
        self.conversion_start_time = None
        self.conversion_end_time = None
        self.vendor = None
        self.target_model = None
        self.target_version = None
        self.language = "zh-CN"  # 默认语言

        # 新增：管道化处理记录
        self.pipeline_stages = []  # 管道阶段记录
        self.conversion_details = {}  # 转换详情
        self.user_suggestions = []  # 用户建议
        self.stage_results = {}  # 各阶段处理结果
    
    def start_conversion(self, vendor=None, model=None, version=None, language=None):
        """记录转换开始时间和相关信息
        
        Args:
            vendor (str): 厂商名称
            model (str): 目标设备型号
            version (str): 目标设备版本
            language (str): 语言代码
        """
        self.conversion_start_time = datetime.datetime.now()
        self.vendor = vendor
        self.target_model = model
        self.target_version = version
        if language:
            self.language = language
    
    def end_conversion(self):
        """记录转换结束时间"""
        self.conversion_end_time = datetime.datetime.now()
    
    def get_conversion_duration(self):
        """获取转换持续时间
        
        Returns:
            str: 格式化的持续时间字符串
        """
        if not self.conversion_start_time or not self.conversion_end_time:
            return _("user.time.unknown", language=self.language)
        
        duration = self.conversion_end_time - self.conversion_start_time
        seconds = duration.total_seconds()
        
        if seconds < 60:
            # return _("user.time.seconds", seconds=f"{seconds:.2f}", language=self.language)
            return f"{seconds:.2f} s"
        elif seconds < 3600:
            minutes = seconds / 60
            # return _("user.time.minutes", minutes=f"{minutes:.2f}", language=self.language)
            return f"{minutes:.2f} m"
        
        else:
            hours = seconds / 3600
            # return _("user.time.hours", hours=f"{hours:.2f}", language=self.language)
            return f"{hours:.2f} h"
    
    def format_summary(self, stats):
        """格式化转换统计信息
        
        Args:
            stats (dict): 转换统计信息
            
        Returns:
            str: 格式化的统计信息
        """
        lines = []
        lines.append(f"=== {_('user.summary.conversion_stats', language=self.language)} ===")
        
        # 定义统计字段的键名
        total_key = _("stats.total", language=self.language)
        success_key = _("stats.success", language=self.language)
        failed_key = _("stats.failed", language=self.language)
        skipped_key = _("stats.skipped", language=self.language)
        
        if "interfaces" in stats:
            intf_stats = stats["interfaces"]
            lines.append(_("user.summary.interfaces_detail", 
                          total=intf_stats.get(total_key, 0), 
                          success=intf_stats.get(success_key, 0),
                          failed=intf_stats.get(failed_key, 0), 
                          skipped=intf_stats.get(skipped_key, 0),
                          language=self.language))
        
        if "zones" in stats:
            zone_stats = stats["zones"]
            lines.append(_("user.summary.zones_detail", 
                          total=zone_stats.get(total_key, 0), 
                          success=zone_stats.get(success_key, 0),
                          failed=zone_stats.get(failed_key, 0), 
                          skipped=zone_stats.get(skipped_key, 0),
                          language=self.language))
        
        if "policies" in stats:
            policy_stats = stats["policies"]
            lines.append(_("user.summary.policies_detail", 
                          total=policy_stats.get(total_key, 0), 
                          success=policy_stats.get(success_key, 0),
                          failed=policy_stats.get(failed_key, 0), 
                          skipped=policy_stats.get(skipped_key, 0),
                          language=self.language))
        
        if "static_routes" in stats:
            route_stats = stats["static_routes"]
            lines.append(_("user.summary.static_routes_detail", 
                          total=route_stats.get(total_key, 0), 
                          success=route_stats.get(success_key, 0),
                          failed=route_stats.get(failed_key, 0), 
                          skipped=route_stats.get(skipped_key, 0),
                          language=self.language))
        
        if "addresses" in stats:
            addr_stats = stats["addresses"]
            lines.append(_("user.summary.addresses_detail", 
                          total=addr_stats.get(total_key, 0), 
                          success=addr_stats.get(success_key, 0),
                          failed=addr_stats.get(failed_key, 0), 
                          skipped=addr_stats.get(skipped_key, 0),
                          language=self.language))
        
        if "address_groups" in stats:
            addr_grp_stats = stats["address_groups"]
            lines.append(_("user.summary.address_groups_detail", 
                          total=addr_grp_stats.get(total_key, 0), 
                          success=addr_grp_stats.get(success_key, 0),
                          failed=addr_grp_stats.get(failed_key, 0), 
                          skipped=addr_grp_stats.get(skipped_key, 0),
                          language=self.language))
        
        if "services" in stats:
            svc_stats = stats["services"]
            lines.append(_("user.summary.services_detail", 
                          total=svc_stats.get(total_key, 0), 
                          success=svc_stats.get(success_key, 0),
                          failed=svc_stats.get(failed_key, 0), 
                          skipped=svc_stats.get(skipped_key, 0),
                          language=self.language))
        
        if "service_groups" in stats:
            svc_grp_stats = stats["service_groups"]
            lines.append(_("user.summary.service_groups_detail", 
                          total=svc_grp_stats.get(total_key, 0), 
                          success=svc_grp_stats.get(success_key, 0),
                          failed=svc_grp_stats.get(failed_key, 0), 
                          skipped=svc_grp_stats.get(skipped_key, 0),
                          language=self.language))
        
        return "\n".join(lines)
    
    def format_manual_config_items(self, items):
        """格式化需要人工配置的项目
        
        Args:
            items (list): 需要人工配置的项目列表
            
        Returns:
            str: 格式化的项目列表
        """
        if not items:
            return _("user.summary.no_manual_config_items", language=self.language)
        
        lines = []
        lines.append(f"=== {_('user.summary.manual_config_items', language=self.language)} ===")
        
        # 定义手动配置项目的键名
        type_key = _("issue.type", language=self.language)
        reason_key = _("detail.reason", language=self.language)
        interface_list_key = "interface_list"  # 这个键不需要翻译，因为它是内部使用的
        
        for item in items:
            item_type = item.get(type_key, _("common.unknown", language=self.language))
            count = 1
            if interface_list_key in item:
                count = len(item[interface_list_key])
            reason = item.get(reason_key, _("common.unknown_reason", language=self.language))
            lines.append(_("user.summary.manual_config_item", 
                          type=item_type, 
                          count=count, 
                          reason=reason,
                          language=self.language))
        
        return "\n".join(lines)
    
    def format_compatibility_issues(self, issues):
        """格式化兼容性问题
        
        Args:
            issues (list): 兼容性问题列表
            
        Returns:
            str: 格式化的兼容性问题
        """
        if not issues:
            return _("user.summary.no_compatibility_issues", language=self.language)
        
        lines = []
        lines.append(f"=== {_('user.summary.compatibility_issues', language=self.language)} ===")
        
        # 定义兼容性问题的键名
        type_key = _("issue.type", language=self.language)
        description_key = _("issue.description", language=self.language)
        
        for issue in issues:
            issue_type = issue.get(type_key, _("common.unknown", language=self.language))
            description = issue.get(description_key, _("common.unknown", language=self.language))
            lines.append(_("user.summary.compatibility_issue", 
                          type=issue_type, 
                          description=description,
                          language=self.language))
        
        return "\n".join(lines)

    def record_pipeline_stage(self, stage_name: str, stage_result: dict):
        """
        记录管道阶段处理结果

        Args:
            stage_name: 阶段名称
            stage_result: 阶段处理结果
        """
        stage_record = {
            "name": stage_name,
            "result": stage_result,
            "timestamp": datetime.datetime.now(),
            "status": "success" if stage_result.get("success", True) else "failed"
        }
        self.pipeline_stages.append(stage_record)
        self.stage_results[stage_name] = stage_result

    def format_conversion_flow(self, pipeline_history: list = None) -> str:
        """
        格式化转换流程概览

        Args:
            pipeline_history: 管道历史记录（可选，如果不提供则使用内部记录）

        Returns:
            str: 格式化的转换流程
        """
        lines = []
        lines.append(f"=== {_('user.flow.conversion_process', language=self.language)} ===")

        # 使用提供的历史记录或内部记录
        stages_to_process = pipeline_history or self.pipeline_stages

        if not stages_to_process:
            lines.append(_("user.flow.no_stages_recorded", language=self.language))
            return "\n".join(lines)

        # 定义阶段显示名称映射
        stage_display_names = {
            "operation_mode": _("user.flow.stage.operation_mode", language=self.language),
            "interface_processing": _("user.flow.stage.interface_processing", language=self.language),
            "address_processing": _("user.flow.stage.address_processing", language=self.language),
            "address_group_processing": _("user.flow.stage.address_group_processing", language=self.language),
            "service_processing": _("user.flow.stage.service_processing", language=self.language),
            "service_group_processing": _("user.flow.stage.service_group_processing", language=self.language),
            "zone_processing": _("user.flow.stage.zone_processing", language=self.language),
            "time_range_processing": _("user.flow.stage.time_range_processing", language=self.language),
            "dns_processing": _("user.flow.stage.dns_processing", language=self.language),
            "static_route_processing": _("user.flow.stage.static_route_processing", language=self.language),
            "policy_processing": _("user.flow.stage.policy_processing", language=self.language),
            "xml_generation": _("user.flow.stage.xml_generation", language=self.language),
            "yang_validation": _("user.flow.stage.yang_validation", language=self.language),
            "encryption": _("user.flow.stage.encryption", language=self.language)
        }

        for i, stage in enumerate(stages_to_process, 1):
            stage_name = stage.get("name", "unknown")
            stage_result = stage.get("result", {})
            status = stage.get("status", "unknown")

            # 获取显示名称
            display_name = stage_display_names.get(stage_name, stage_name)

            # 确定状态图标
            if status == "success":
                icon = "✓"
                status_text = _("user.status.success", language=self.language)
            elif status == "failed":
                icon = "✗"
                status_text = _("user.status.failed", language=self.language)
            elif status == "warning":
                icon = "⚠"
                status_text = _("user.status.warning", language=self.language)
            else:
                icon = "?"
                status_text = _("user.status.unknown", language=self.language)

            # 获取阶段详细信息
            detail_info = self._get_stage_detail_info(stage_name, stage_result)

            # 格式化阶段信息
            stage_line = f"{i}. [{icon}] {display_name}"
            if detail_info:
                stage_line += f" - {detail_info}"

            lines.append(stage_line)

        return "\n".join(lines)

    def _get_stage_detail_info(self, stage_name: str, stage_result: dict) -> str:
        """
        获取阶段详细信息

        Args:
            stage_name: 阶段名称
            stage_result: 阶段结果

        Returns:
            str: 详细信息字符串
        """
        if not stage_result:
            return ""

        # 根据不同阶段类型提取关键信息
        if "processing" in stage_name:
            statistics = stage_result.get("statistics", {})
            if statistics:
                total = statistics.get("total", 0)
                success = statistics.get("converted", statistics.get("processed", 0))
                if total > 0:
                    return _("user.detail.processing_summary",
                           success=success, total=total, language=self.language)

        elif stage_name == "operation_mode":
            mode = stage_result.get("mode", "unknown")
            return _("user.detail.detected_mode", mode=mode, language=self.language)

        elif stage_name == "yang_validation":
            if stage_result.get("valid", False):
                return _("user.detail.validation_passed", language=self.language)
            else:
                return _("user.detail.validation_failed", language=self.language)

        elif stage_name == "encryption":
            if stage_result.get("encrypted", False):
                return _("user.detail.encryption_success", language=self.language)
            else:
                return _("user.detail.encryption_failed", language=self.language)

        return ""

    def format_detailed_statistics(self, stage_results: dict = None) -> str:
        """
        格式化详细转换统计信息

        Args:
            stage_results: 各阶段处理结果（可选，如果不提供则使用内部记录）

        Returns:
            str: 格式化的详细统计信息
        """
        lines = []
        lines.append(f"=== {_('user.detail.conversion_statistics', language=self.language)} ===")
        lines.append("")

        # 使用提供的结果或内部记录
        results_to_process = stage_results or self.stage_results

        if not results_to_process:
            lines.append(_("user.detail.no_statistics_available", language=self.language))
            return "\n".join(lines)

        # 按照完整管道化XML生成流程顺序处理各个阶段的统计信息
        stage_order = [
            ("operation_mode", _("user.detail.operation_mode_detection", language=self.language)),
            ("interface_processing", _("user.detail.interface_conversion", language=self.language)),
            ("address_processing", _("user.detail.address_conversion", language=self.language)),
            ("address_group_processing", _("user.detail.address_group_conversion", language=self.language)),
            ("service_processing", _("user.detail.service_conversion", language=self.language)),
            ("service_group_processing", _("user.detail.service_group_conversion", language=self.language)),
            ("zone_processing", _("user.detail.zone_conversion", language=self.language)),
            ("time_range_processing", _("user.detail.time_range_conversion", language=self.language)),
            ("dns_processing", _("user.detail.dns_conversion", language=self.language)),
            ("static_route_processing", _("user.detail.static_route_conversion", language=self.language)),
            ("fortigate_policy_conversion", _("user.detail.policy_conversion", language=self.language)),
            ("xml_template_integration", _("user.detail.xml_integration", language=self.language))
        ]

        for stage_key, stage_title in stage_order:
            if stage_key in results_to_process:
                stage_result = results_to_process[stage_key]
                stage_stats = self._format_stage_statistics(stage_title, stage_result)
                if stage_stats:
                    lines.extend(stage_stats)
                    lines.append("")  # 空行分隔

        return "\n".join(lines)

    def _format_stage_statistics(self, stage_title: str, stage_result: dict) -> list:
        """
        格式化单个阶段的统计信息

        Args:
            stage_title: 阶段标题
            stage_result: 阶段结果

        Returns:
            list: 格式化的统计信息行列表
        """
        lines = []
        lines.append(f"{stage_title}：")

        # 获取统计信息 - 支持多种数据格式
        statistics = {}

        # 首先尝试从statistics字段中获取，支持不同的键名映射
        stats = stage_result.get("statistics", {})
        if isinstance(stats, dict) and stats:
            # 处理不同的统计信息键名格式
            total_keys = ["total", "total_addresses", "total_services", "total_policies"]
            converted_keys = ["converted", "converted_addresses", "converted_services", "converted_policies", "processed"]
            failed_keys = ["failed", "failed_addresses", "failed_services", "failed_policies"]
            skipped_keys = ["skipped", "skipped_addresses", "skipped_services", "skipped_policies"]

            def get_stat_value(keys_list):
                for key in keys_list:
                    if key in stats:
                        return stats[key]
                return 0

            total_val = get_stat_value(total_keys)
            converted_val = get_stat_value(converted_keys)
            failed_val = get_stat_value(failed_keys)
            skipped_val = get_stat_value(skipped_keys)

            # 只有当至少有一个值不为0时才使用这些统计信息
            if total_val > 0 or converted_val > 0 or failed_val > 0 or skipped_val > 0:
                statistics = {
                    "total": total_val,
                    "converted": converted_val,
                    "failed": failed_val,
                    "skipped": skipped_val
                }
                # 如果没有total但有其他值，计算total
                if statistics["total"] == 0 and (converted_val > 0 or failed_val > 0 or skipped_val > 0):
                    statistics["total"] = converted_val + failed_val + skipped_val

        if not statistics:
            # 尝试从其他字段获取统计信息
            if "converted_count" in stage_result:
                statistics = {
                    "total": stage_result.get("total_count", 0),
                    "converted": stage_result.get("converted_count", 0),
                    "failed": stage_result.get("failed_count", 0),
                    "skipped": stage_result.get("skipped_count", 0)
                }

            # 尝试从嵌套的对象中获取统计信息（如address_objects.converted_count）
            elif any(key.endswith("_objects") for key in stage_result.keys()):
                for key, value in stage_result.items():
                    if key.endswith("_objects") and isinstance(value, dict):
                        statistics = {
                            "total": value.get("converted_count", 0) + value.get("failed_count", 0) + value.get("skipped_count", 0),
                            "converted": value.get("converted_count", 0),
                            "failed": value.get("failed_count", 0),
                            "skipped": value.get("skipped_count", 0)
                        }

                        break
            # 尝试从特定的阶段数据格式中推断统计信息
            else:
                statistics = self._infer_statistics_from_stage_data(stage_result)

        if statistics:
            # 检查是否为"无需处理"的情况
            if statistics.get("no_data", False) and statistics.get("total", 0) == 0:
                lines.append(f"  {_('user.detail.no_processing_needed', language=self.language)}")
                return lines

            total = statistics.get("total", 0)
            converted = statistics.get("converted", statistics.get("processed", 0))
            failed = statistics.get("failed", 0)
            skipped = statistics.get("skipped", 0)

            # 成功转换的项目
            if converted > 0:
                lines.append(f"  ✓ {_('user.detail.success_converted', language=self.language)}: {converted}{_('user.detail.items_unit', language=self.language)}")

                # 显示成功转换的具体项目（如果有详细信息）
                success_details = self._get_success_details(stage_result)
                if success_details:
                    for detail in success_details[:5]:  # 最多显示5个
                        lines.append(f"    - {detail}")
                    if len(success_details) > 5:
                        lines.append(f"    - {_('user.detail.and_more', count=len(success_details)-5, language=self.language)}")

            # 跳过的项目
            if skipped > 0:
                lines.append(f"  ⚠ {_('user.detail.skipped_converted', language=self.language)}: {skipped}{_('user.detail.items_unit', language=self.language)}")

                # 显示跳过项目的具体原因
                skipped_details = self._get_skipped_details(stage_result)
                if skipped_details:
                    for detail in skipped_details[:3]:  # 最多显示3个
                        lines.append(f"    - {detail}")
                    if len(skipped_details) > 3:
                        lines.append(f"    - {_('user.detail.and_more', count=len(skipped_details)-3, language=self.language)}")

            # 部分转换或警告的项目
            warning_details = self._get_warning_details(stage_result)
            if warning_details:
                lines.append(f"  ⚠ {_('user.detail.partial_converted', language=self.language)}: {len(warning_details)}{_('user.detail.items_unit', language=self.language)}")
                for detail in warning_details[:3]:  # 最多显示3个
                    lines.append(f"    - {detail}")
                if len(warning_details) > 3:
                    lines.append(f"    - {_('user.detail.and_more', count=len(warning_details)-3, language=self.language)}")

            # 转换失败的项目
            if failed > 0:
                lines.append(f"  ✗ {_('user.detail.failed_converted', language=self.language)}: {failed}{_('user.detail.items_unit', language=self.language)}")

                # 显示失败转换的具体项目和原因
                failure_details = self._get_failure_details(stage_result)
                if failure_details:
                    for detail in failure_details[:3]:  # 最多显示3个
                        lines.append(f"    - {detail}")
                    if len(failure_details) > 3:
                        lines.append(f"    - {_('user.detail.and_more', count=len(failure_details)-3, language=self.language)}")

        return lines

    def _get_success_details(self, stage_result: dict) -> list:
        """获取成功转换的详细信息"""
        details = []

        # 从不同的结果结构中提取成功项目
        if "converted" in stage_result:
            converted_items = stage_result["converted"]
            if isinstance(converted_items, dict):
                for name, item in list(converted_items.items())[:5]:
                    if isinstance(item, dict):
                        item_type = item.get("type", _("common.unknown", language=self.language))
                        details.append(f"{name} → {item.get('name', name)} ({item_type})")
                    else:
                        details.append(f"{name}")

        # 从details字段中提取成功项目
        elif "details" in stage_result:
            stage_details = stage_result["details"]
            if isinstance(stage_details, list):
                for detail in stage_details:
                    if isinstance(detail, dict) and detail.get("status") == "success":
                        name = detail.get("name", _("common.unknown", language=self.language))
                        item_type = detail.get("type", "")
                        if item_type:
                            details.append(f"{name} ({item_type})")
                        else:
                            details.append(name)

        return details

    def _get_warning_details(self, stage_result: dict) -> list:
        """获取警告项目的详细信息"""
        details = []

        # 从details字段中提取警告项目
        if "details" in stage_result:
            stage_details = stage_result["details"]
            if isinstance(stage_details, list):
                for detail in stage_details:
                    if isinstance(detail, dict) and detail.get("status") in ["warning", "partial"]:
                        name = detail.get("name", _("common.unknown", language=self.language))
                        reason = detail.get("reason", _("common.unknown_reason", language=self.language))
                        details.append(f"{name}: {reason}")

        return details

    def _get_failure_details(self, stage_result: dict) -> list:
        """获取失败项目的详细信息"""
        details = []

        # 从failed字段中提取失败项目
        if "failed" in stage_result:
            failed_items = stage_result["failed"]
            if isinstance(failed_items, dict):
                for name, reason in failed_items.items():
                    if isinstance(reason, dict):
                        reason_text = reason.get("reason", reason.get("message", _("common.unknown_reason", language=self.language)))
                    else:
                        reason_text = str(reason)
                    details.append(f"{name}: {reason_text}")

        # 从嵌套对象中提取失败项目（如address_objects.failed）
        for key, value in stage_result.items():
            if key.endswith("_objects") and isinstance(value, dict) and "failed" in value:
                failed_items = value["failed"]
                if isinstance(failed_items, dict):
                    for name, reason in failed_items.items():
                        if isinstance(reason, dict):
                            reason_text = reason.get("reason", reason.get("message", _("common.unknown_reason", language=self.language)))
                        else:
                            reason_text = str(reason)
                        details.append(f"{name}: {reason_text}")

        # 从details字段中提取失败项目
        if "details" in stage_result:
            stage_details = stage_result["details"]
            if isinstance(stage_details, list):
                for detail in stage_details:
                    if isinstance(detail, dict) and detail.get("status") == "failed":
                        name = detail.get("name", _("common.unknown", language=self.language))
                        reason = detail.get("reason", _("common.unknown_reason", language=self.language))
                        details.append(f"{name}: {reason}")

        return details

    def _get_skipped_details(self, stage_result: dict) -> list:
        """获取跳过项目的详细信息"""
        details = []

        # 从skipped字段中提取跳过项目
        if "skipped" in stage_result:
            skipped_items = stage_result["skipped"]
            if isinstance(skipped_items, dict):
                for name, reason in skipped_items.items():
                    if isinstance(reason, dict):
                        reason_text = reason.get("reason", reason.get("message", _("common.unknown_reason", language=self.language)))
                    else:
                        reason_text = str(reason)
                    details.append(f"{name}: {reason_text}")

        # 从嵌套对象中提取跳过项目（如address_objects.skipped）
        for key, value in stage_result.items():
            if key.endswith("_objects") and isinstance(value, dict) and "skipped" in value:
                skipped_items = value["skipped"]
                if isinstance(skipped_items, dict):
                    for name, reason in skipped_items.items():
                        if isinstance(reason, dict):
                            reason_text = reason.get("reason", reason.get("message", _("common.unknown_reason", language=self.language)))
                        else:
                            reason_text = str(reason)
                        details.append(f"{name}: {reason_text}")

        # 从details字段中提取跳过项目
        if "details" in stage_result:
            stage_details = stage_result["details"]
            if isinstance(stage_details, list):
                for detail in stage_details:
                    if isinstance(detail, dict) and detail.get("status") == "skipped":
                        name = detail.get("name", _("common.unknown", language=self.language))
                        reason = detail.get("reason", _("common.unknown_reason", language=self.language))
                        details.append(f"{name}: {reason}")

        return details

    def add_user_suggestion(self, suggestion_type: str, title: str, description: str,
                           action_steps: list = None, priority: str = "normal"):
        """
        添加用户操作建议

        Args:
            suggestion_type: 建议类型 (required, recommended, optional)
            title: 建议标题
            description: 建议描述
            action_steps: 操作步骤列表
            priority: 优先级 (high, normal, low)
        """
        suggestion = {
            "type": suggestion_type,
            "title": title,
            "description": description,
            "action_steps": action_steps or [],
            "priority": priority,
            "timestamp": datetime.datetime.now()
        }
        self.user_suggestions.append(suggestion)

    def format_user_suggestions(self, suggestions: list = None) -> str:
        """
        格式化用户操作建议

        Args:
            suggestions: 建议列表（可选，如果不提供则使用内部记录）

        Returns:
            str: 格式化的用户建议
        """
        lines = []
        lines.append(f"=== {_('user.suggestion.post_migration_steps', language=self.language)} ===")
        lines.append("")

        # 使用提供的建议或内部记录
        suggestions_to_process = suggestions or self.user_suggestions

        if not suggestions_to_process:
            lines.append(_("user.suggestion.no_suggestions", language=self.language))
            return "\n".join(lines)

        # 按类型和优先级分组建议
        required_suggestions = []
        recommended_suggestions = []
        optional_suggestions = []

        for suggestion in suggestions_to_process:
            suggestion_type = suggestion.get("type", "optional")
            if suggestion_type == "required":
                required_suggestions.append(suggestion)
            elif suggestion_type == "recommended":
                recommended_suggestions.append(suggestion)
            else:
                optional_suggestions.append(suggestion)

        # 格式化必须完成的配置
        if required_suggestions:
            lines.append(_("user.suggestion.required_configurations", language=self.language))
            for i, suggestion in enumerate(required_suggestions, 1):
                lines.extend(self._format_single_suggestion(i, suggestion))
            lines.append("")

        # 格式化建议完成的优化
        if recommended_suggestions:
            lines.append(_("user.suggestion.recommended_optimizations", language=self.language))
            for i, suggestion in enumerate(recommended_suggestions, 1):
                lines.extend(self._format_single_suggestion(i, suggestion))
            lines.append("")

        # 格式化可选的改进
        if optional_suggestions:
            lines.append(_("user.suggestion.optional_improvements", language=self.language))
            for i, suggestion in enumerate(optional_suggestions, 1):
                lines.extend(self._format_single_suggestion(i, suggestion))

        return "\n".join(lines)

    def _format_single_suggestion(self, index: int, suggestion: dict) -> list:
        """
        格式化单个建议

        Args:
            index: 建议序号
            suggestion: 建议信息

        Returns:
            list: 格式化的建议行列表
        """
        lines = []

        title = suggestion.get("title", _("common.unknown", language=self.language))
        description = suggestion.get("description", "")
        action_steps = suggestion.get("action_steps", [])
        priority = suggestion.get("priority", "normal")

        # 添加优先级图标
        priority_icon = ""
        if priority == "high":
            priority_icon = "🔴 "
        elif priority == "normal":
            priority_icon = "🟡 "
        else:
            priority_icon = "🟢 "

        # 格式化标题
        lines.append(f"{index}. {priority_icon}{title}")

        # 添加描述
        if description:
            lines.append(f"   - {_('user.suggestion.reason', language=self.language)}: {description}")

        # 添加操作步骤
        if action_steps:
            lines.append(f"   - {_('user.suggestion.action_steps', language=self.language)}:")
            for step in action_steps:
                lines.append(f"     • {step}")

        return lines

    def generate_comprehensive_report(self, conversion_context: dict) -> str:
        """
        生成综合转换报告

        Args:
            conversion_context: 转换上下文信息

        Returns:
            str: 综合报告
        """
        lines = []

        # 报告标题
        lines.append(f"=== {_('user.report.comprehensive_conversion_report', language=self.language)} ===")
        lines.append("")

        # 基本信息
        lines.append(f"{_('user.report.vendor', language=self.language)}: {self.vendor or 'Unknown'}")
        lines.append(f"{_('user.report.target_model', language=self.language)}: {self.target_model or 'Unknown'}")
        lines.append(f"{_('user.report.target_version', language=self.language)}: {self.target_version or 'Unknown'}")
        lines.append(f"{_('user.report.conversion_time', language=self.language)}: {self.get_conversion_duration()}")
        lines.append("")

        # 注释：移除转换流程概览，用户不需要这个章节

        # 详细统计信息
        detailed_stats = self.format_detailed_statistics()
        if detailed_stats:
            lines.append(detailed_stats)
            lines.append("")

        # 用户操作建议
        user_suggestions = self.format_user_suggestions()
        if user_suggestions:
            lines.append(user_suggestions)
            lines.append("")

        # 兼容性问题（如果有）
        compatibility_issues = conversion_context.get("compatibility_issues", [])
        if compatibility_issues:
            compatibility_report = self.format_compatibility_issues(compatibility_issues)
            lines.append(compatibility_report)
            lines.append("")

        # 报告结尾
        lines.append(f"=== {_('user.report.report_end', language=self.language)} ===")

        return "\n".join(lines)

    def _infer_statistics_from_stage_data(self, stage_result: dict) -> dict:
        """
        从阶段数据中推断统计信息

        Args:
            stage_result: 阶段结果数据

        Returns:
            dict: 推断的统计信息，如果无法推断则返回空字典
        """
        # 1. 检查是否有嵌套的统计信息（如zones.statistics, address_groups.statistics等）
        for key, value in stage_result.items():
            if isinstance(value, dict) and "statistics" in value:
                nested_stats = value["statistics"]
                if isinstance(nested_stats, dict) and nested_stats:
                    # 尝试从嵌套统计信息中提取
                    total_keys = ["total", "total_groups", "total_zones", "total_routes", "total_policies"]
                    converted_keys = ["converted", "converted_groups", "converted_zones", "converted_routes", "converted_policies"]
                    failed_keys = ["failed", "failed_groups", "failed_zones", "failed_routes", "failed_policies"]
                    skipped_keys = ["skipped", "skipped_groups", "skipped_zones", "skipped_routes", "skipped_policies"]

                    def get_nested_stat_value(keys_list):
                        for key in keys_list:
                            if key in nested_stats:
                                return nested_stats[key]
                        return 0

                    total_val = get_nested_stat_value(total_keys)
                    converted_val = get_nested_stat_value(converted_keys)
                    failed_val = get_nested_stat_value(failed_keys)
                    skipped_val = get_nested_stat_value(skipped_keys)

                    if total_val > 0 or converted_val > 0 or failed_val > 0 or skipped_val > 0:
                        return {
                            "total": total_val if total_val > 0 else (converted_val + failed_val + skipped_val),
                            "converted": converted_val,
                            "failed": failed_val,
                            "skipped": skipped_val
                        }

        # 2. 检查是否有嵌套的处理结果（如zones.converted_count等）
        for key, value in stage_result.items():
            if isinstance(value, dict) and any(k.endswith("_count") for k in value.keys()):
                converted_count = value.get("converted_count", 0)
                failed_count = value.get("failed_count", 0)
                skipped_count = value.get("skipped_count", 0)
                total_count = converted_count + failed_count + skipped_count

                if total_count > 0:
                    return {
                        "total": total_count,
                        "converted": converted_count,
                        "failed": failed_count,
                        "skipped": skipped_count
                    }

        # 3. 操作模式检测
        if "mode" in stage_result:
            return {"total": 1, "converted": 1, "failed": 0, "skipped": 0}

        # 4. 接口处理
        if "converted_interfaces" in stage_result or "interface_mapping" in stage_result:
            converted = len(stage_result.get("converted_interfaces", []))
            failed = len(stage_result.get("failed_interfaces", []))
            skipped = len(stage_result.get("skipped_interfaces", []))
            total = converted + failed + skipped
            if total > 0:
                return {"total": total, "converted": converted, "failed": failed, "skipped": skipped}

        # 5. XML模板集成
        if "template_applied" in stage_result or "xml_generated" in stage_result:
            applied = stage_result.get("template_applied", False)
            generated = stage_result.get("xml_generated", False)
            if applied or generated:
                return {"total": 1, "converted": 1, "failed": 0, "skipped": 0}

        # 6. 检查是否有success字段（基本成功/失败状态）
        if "success" in stage_result:
            success = stage_result.get("success", False)
            if success:
                return {"total": 1, "converted": 1, "failed": 0, "skipped": 0}
            else:
                return {"total": 1, "converted": 0, "failed": 1, "skipped": 0}

        # 7. 如果所有字段都为空或0，显示"无需处理"
        # 检查是否所有相关字段都表明没有数据需要处理
        empty_indicators = [
            stage_result.get("group_count", -1) == 0,
            stage_result.get("zone_count", -1) == 0,
            stage_result.get("route_count", -1) == 0,
            stage_result.get("policy_count", -1) == 0,
            len(stage_result.get("processed_groups", [])) == 0,
            len(stage_result.get("zones_created", [])) == 0,
            len(stage_result.get("routes", [])) == 0,
            len(stage_result.get("policies", [])) == 0,
            len(stage_result.get("time_ranges", [])) == 0,
            len(stage_result.get("dns_servers", [])) == 0
        ]

        # 如果有任何一个指标表明确实没有数据，返回特殊标记
        if any(indicator for indicator in empty_indicators if indicator is True):
            return {"total": 0, "converted": 0, "failed": 0, "skipped": 0, "no_data": True}

        # 8. 如果无法推断，返回空字典
        return {}

# 用户日志收集器
class UserLogCollector:
    """用户日志收集器，用于收集所有阶段的用户日志和统计信息，最后统一输出"""

    def __init__(self):
        self.stage_logs = []
        self.stage_statistics = {}  # 收集各阶段的统计信息
        self.enabled = True  # 默认启用收集，收集详细日志用于最后显示
        self.always_collect_stats = True  # 始终收集统计信息，用于最后的汇总

    def enable_collection(self):
        """启用日志收集"""
        self.enabled = True
        self.stage_logs = []
        self.stage_statistics = {}

    def add_stage_log(self, stage_name: str, log_content: str):
        """添加阶段日志"""
        if self.enabled:
            self.stage_logs.append({
                "stage_name": stage_name,
                "content": log_content,
                "timestamp": datetime.datetime.now()
            })

    def add_stage_statistics(self, stage_name: str, stage_result: dict):
        """添加阶段统计信息（始终收集，用于最后汇总）"""
        if self.always_collect_stats:
            self.stage_statistics[stage_name] = stage_result

    def get_collected_logs(self):
        """获取收集的日志"""
        return self.stage_logs

    def get_collected_statistics(self):
        """获取收集的统计信息"""
        return self.stage_statistics

    def output_collected_logs(self):
        """输出收集的日志"""
        if not self.enabled or not self.stage_logs:
            return

        from engine.utils.logger import user_log
        from engine.utils.i18n import _

        # 输出分隔线
        user_log("\n" + "=" * 60, summary=True)
        user_log(_("user.log.detailed_conversion_statistics"), summary=True)
        user_log("=" * 60, summary=True)

        # 输出所有收集的阶段日志
        for log_entry in self.stage_logs:
            user_log(log_entry["content"], summary=True)

        user_log("=" * 60 + "\n", summary=True)

    def output_final_statistics(self):
        """输出最终的集中统计信息（显示收集的详细日志）"""
        from engine.utils.logger import user_log
        from engine.utils.i18n import _

        if not self.stage_logs:
            user_log("\n" + "=" * 60, summary=True)
            user_log(_("user.log.detailed_conversion_statistics"), summary=True)
            user_log("=" * 60, summary=True)
            user_log(_("user.log.no_statistics_available"), summary=True)
            user_log("=" * 60 + "\n", summary=True)
            return

        # 输出分隔线
        user_log("\n" + "=" * 60, summary=True)
        user_log(_("user.log.detailed_conversion_statistics"), summary=True)
        user_log("=" * 60, summary=True)

        # 按照管道顺序输出收集的详细日志
        stage_order = [
            "operation_mode", "interface_processing", "address_processing",
            "address_group_processing", "service_processing", "service_group_processing",
            "zone_processing", "time_range_processing", "dns_processing",
            "static_route_processing", "fortigate_policy_conversion", "xml_template_integration"
        ]

        # 创建阶段日志的映射
        stage_log_map = {}
        for log_entry in self.stage_logs:
            stage_log_map[log_entry["stage_name"]] = log_entry["content"]

        # 按顺序输出各阶段的详细日志
        for stage_name in stage_order:
            if stage_name in stage_log_map:
                user_log(stage_log_map[stage_name], summary=True)

        user_log("=" * 60 + "\n", summary=True)

    def _generate_stage_summary(self, stage_name: str, stage_result: dict) -> str:
        """生成阶段详细信息（完整版本，包含具体对象和原因）"""
        from engine.utils.i18n import _

        # 获取阶段标题
        stage_title_keys = {
            "operation_mode": "user.stage.operation_mode",
            "interface_processing": "user.stage.interface_processing",
            "address_processing": "user.stage.address_processing",
            "address_group_processing": "user.stage.address_group_processing",
            "service_processing": "user.stage.service_processing",
            "service_group_processing": "user.stage.service_group_processing",
            "zone_processing": "user.stage.zone_processing",
            "time_range_processing": "user.stage.time_range_processing",
            "dns_processing": "user.stage.dns_processing",
            "static_route_processing": "user.stage.static_route_processing",
            "fortigate_policy_conversion": "user.stage.policy_conversion",
            "xml_template_integration": "user.stage.xml_integration"
        }
        title_key = stage_title_keys.get(stage_name, "user.stage.unknown")
        stage_title = _(title_key)

        # 重用现有的详细日志生成逻辑
        # 这样可以确保集中统计和实时输出的格式完全一致
        return self._generate_detailed_stage_log(stage_name, stage_result, stage_title)

    def _extract_stage_statistics(self, stage_name: str, stage_result: dict) -> dict:
        """提取阶段统计信息"""
        stats = {"total": 0, "converted": 0, "failed": 0, "skipped": 0}

        if stage_name == "operation_mode":
            # 操作模式检测总是成功1个
            if "mode" in stage_result:
                stats["total"] = 1
                stats["converted"] = 1

        elif stage_name == "interface_processing":
            # 接口处理统计
            statistics = stage_result.get("statistics", {})
            stats["total"] = statistics.get("total_interfaces", 0)
            stats["converted"] = statistics.get("converted_interfaces", 0)
            stats["failed"] = statistics.get("failed_interfaces", 0)
            stats["skipped"] = statistics.get("skipped_interfaces", 0)

        elif stage_name in ["address_processing", "service_processing"]:
            # 地址对象和服务对象处理统计
            statistics = stage_result.get("statistics", {})
            if statistics:
                if "addresses" in str(statistics):
                    stats["total"] = statistics.get("total_addresses", 0)
                    stats["converted"] = statistics.get("converted_addresses", 0)
                    stats["failed"] = statistics.get("failed_addresses", 0)
                    stats["skipped"] = statistics.get("skipped_addresses", 0)
                elif "services" in str(statistics):
                    stats["total"] = statistics.get("total_services", 0)
                    stats["converted"] = statistics.get("converted_services", 0)
                    stats["failed"] = statistics.get("failed_services", 0)
                    stats["skipped"] = statistics.get("skipped_services", 0)

            # 尝试从嵌套数据中获取
            if stats["total"] == 0:
                nested_data = None
                if stage_name == "address_processing":
                    nested_data = stage_result.get("address_objects", {})
                elif stage_name == "service_processing":
                    nested_data = stage_result.get("service_objects", {})

                if nested_data:
                    stats["converted"] = nested_data.get("converted_count", 0)
                    stats["failed"] = nested_data.get("failed_count", 0)
                    stats["skipped"] = nested_data.get("skipped_count", 0)
                    stats["total"] = stats["converted"] + stats["failed"] + stats["skipped"]

        elif stage_name in ["address_group_processing", "service_group_processing", "zone_processing"]:
            # 组和区域处理统计
            nested_result = None
            if "address_groups" in stage_result:
                nested_result = stage_result["address_groups"]
            elif "service_groups" in stage_result:
                nested_result = stage_result["service_groups"]
            elif "zones" in stage_result:
                nested_result = stage_result["zones"]

            if nested_result:
                stats["converted"] = nested_result.get("converted_count", 0)
                stats["failed"] = nested_result.get("failed_count", 0)
                stats["skipped"] = nested_result.get("skipped_count", 0)
                stats["total"] = stats["converted"] + stats["failed"] + stats["skipped"]

        elif stage_name == "xml_template_integration":
            # XML模板集成总是成功1个
            if stage_result.get("template_applied", False) or stage_result.get("xml_generated", False):
                stats["total"] = 1
                stats["converted"] = 1

        return stats

    def _generate_detailed_stage_log(self, stage_name: str, stage_result: dict, stage_title: str = None) -> str:
        """生成阶段的详细日志信息（重用现有逻辑）"""
        from engine.utils.i18n import _

        # 如果没有提供标题，使用翻译键获取标题
        if not stage_title:
            stage_title_keys = {
                "operation_mode": "user.stage.operation_mode",
                "interface_processing": "user.stage.interface_processing",
                "address_processing": "user.stage.address_processing",
                "address_group_processing": "user.stage.address_group_processing",
                "service_processing": "user.stage.service_processing",
                "service_group_processing": "user.stage.service_group_processing",
                "zone_processing": "user.stage.zone_processing",
                "time_range_processing": "user.stage.time_range_processing",
                "dns_processing": "user.stage.dns_processing",
                "static_route_processing": "user.stage.static_route_processing",
                "fortigate_policy_conversion": "user.stage.policy_conversion",
                "xml_template_integration": "user.stage.xml_integration"
            }
            title_key = stage_title_keys.get(stage_name, "user.stage.unknown")
            stage_title = _(title_key)

        log_lines = [f"{stage_title}："]

        # 重用现有的详细日志生成逻辑
        # 这里复制record_stage_user_log中的核心逻辑，但不输出，只生成内容

        # 根据不同的阶段结果格式生成统计信息
        if stage_name == "operation_mode":
            if "mode" in stage_result:
                mode = stage_result["mode"]
                log_lines.append(f"  ✓ {_('user.stage.mode_detected', mode=mode)}")

        elif stage_name == "interface_processing":
            # 实际的接口处理结果格式：converted/failed/skipped 是字典
            converted_dict = stage_result.get("converted", {})
            failed_dict = stage_result.get("failed", {})
            skipped_dict = stage_result.get("skipped", {})

            # 从统计信息中获取数量
            stats = stage_result.get("statistics", {})
            converted_count = stats.get("converted_interfaces", len(converted_dict))
            failed_count = stats.get("failed_interfaces", len(failed_dict))
            skipped_count = stats.get("skipped_interfaces", len(skipped_dict))

            if converted_count > 0:
                log_lines.append(f"  ✓ {_('user.stage.success_converted', count=converted_count)}")
                # 显示具体的接口映射
                count = 0
                for interface_name, interface_config in converted_dict.items():
                    if count >= 5:
                        break
                    mapped_name = interface_config.get("name", interface_name) if isinstance(interface_config, dict) else interface_name
                    log_lines.append(f"    - {interface_name} → {mapped_name}")
                    count += 1
                if len(converted_dict) > 5:
                    log_lines.append(f"    - {_('user.stage.and_others', count=len(converted_dict)-5)}")

            if failed_count > 0:
                log_lines.append(f"  ✗ {_('user.stage.failed_converted', count=failed_count)}")
                if failed_dict:
                    log_lines.append(f"    {_('user.stage.failed_details')}:")
                    count = 0
                    for interface_name, failure_info in failed_dict.items():
                        if count >= 5:
                            break
                        reason = failure_info.get("reason", _("user.stage.missing_interface_mapping")) if isinstance(failure_info, dict) else str(failure_info)
                        log_lines.append(f"      • {interface_name}: {reason}")
                        count += 1
                    if len(failed_dict) > 5:
                        log_lines.append(f"      • {_('user.stage.and_others', count=len(failed_dict)-5)}")
                    log_lines.append(f"    {_('user.stage.suggestion')}: {_('user.stage.interface_mapping_suggestion')}")

            if skipped_count > 0:
                log_lines.append(f"  ⚠ {_('user.stage.skipped_converted', count=skipped_count)}")
                if skipped_dict:
                    log_lines.append(f"    {_('user.stage.skipped_details')}:")
                    count = 0
                    for interface_name, skip_info in skipped_dict.items():
                        if count >= 5:
                            break
                        reason = skip_info.get("reason", _("user.stage.interface_type_unsupported")) if isinstance(skip_info, dict) else str(skip_info)
                        log_lines.append(f"      • {interface_name}: {reason}")
                        count += 1
                    if len(skipped_dict) > 5:
                        log_lines.append(f"      • {_('user.stage.and_others', count=len(skipped_dict)-5)}")
                    log_lines.append(f"    {_('user.stage.suggestion')}: {_('user.stage.interface_type_suggestion')}")

        elif stage_name in ["address_processing", "service_processing"]:
            # 处理地址对象和服务对象 - 支持嵌套数据结构
            stats = stage_result.get("statistics", {})

            # 尝试从嵌套结构中获取数据
            nested_data = None
            if stage_name == "address_processing":
                nested_data = stage_result.get("address_objects", {})
            elif stage_name == "service_processing":
                nested_data = stage_result.get("service_objects", {})

            # 获取统计数据
            if stats:
                converted_key = "converted_addresses" if "addresses" in str(stats) else "converted_services" if "services" in str(stats) else "converted"
                failed_key = "failed_addresses" if "addresses" in str(stats) else "failed_services" if "services" in str(stats) else "failed"
                skipped_key = "skipped_addresses" if "addresses" in str(stats) else "skipped_services" if "services" in str(stats) else "skipped"

                converted = stats.get(converted_key, 0)
                failed = stats.get(failed_key, 0)
                skipped = stats.get(skipped_key, 0)
            elif nested_data:
                # 从嵌套数据中获取统计
                converted = nested_data.get("converted_count", 0)
                failed = nested_data.get("failed_count", 0)
                skipped = nested_data.get("skipped_count", 0)
            else:
                converted = failed = skipped = 0

            if converted > 0:
                log_lines.append(f"  ✓ {_('user.stage.success_converted', count=converted)}")

            if skipped > 0:
                log_lines.append(f"  ⚠ {_('user.stage.skipped_converted', count=skipped)}")
                # 显示跳过的详细原因 - 支持多层嵌套
                skipped_items = stage_result.get("skipped", {})
                if not skipped_items and nested_data:
                    skipped_items = nested_data.get("skipped", {})

                if isinstance(skipped_items, dict) and skipped_items:
                    log_lines.append(f"    {_('user.stage.skipped_details')}:")
                    count = 0
                    for name, reason in skipped_items.items():
                        if count >= 5:
                            break
                        reason_text = reason.get("reason", _("user.stage.unknown_reason")) if isinstance(reason, dict) else str(reason)
                        log_lines.append(f"      • {name}: {reason_text}")
                        count += 1
                    if len(skipped_items) > 5:
                        log_lines.append(f"      • {_('user.stage.and_others', count=len(skipped_items)-5)}")

                    # 添加操作建议
                    if "FQDN" in str(skipped_items) or "dynamic" in str(skipped_items) or "fqdn" in str(skipped_items):
                        log_lines.append(f"    {_('user.stage.suggestion')}: {_('user.stage.fqdn_not_supported_suggestion')}")

            if failed > 0:
                log_lines.append(f"  ✗ {_('user.stage.failed_converted', count=failed)}")
                # 显示失败的详细原因 - 支持多层嵌套
                failed_items = stage_result.get("failed", {})
                if not failed_items and nested_data:
                    failed_items = nested_data.get("failed", {})

                if isinstance(failed_items, dict) and failed_items:
                    log_lines.append(f"    {_('user.stage.failed_details')}:")
                    count = 0
                    for name, reason in failed_items.items():
                        if count >= 5:
                            break
                        reason_text = reason.get("reason", _("user.stage.unknown_reason")) if isinstance(reason, dict) else str(reason)
                        log_lines.append(f"      • {name}: {reason_text}")
                        count += 1
                    if len(failed_items) > 5:
                        log_lines.append(f"      • {_('user.stage.and_others', count=len(failed_items)-5)}")

                    # 添加操作建议
                    if "subnet" in str(failed_items) or "地址" in str(failed_items) or "address" in str(failed_items):
                        log_lines.append(f"    {_('user.stage.suggestion')}: {_('user.stage.address_config_suggestion')}")
                    elif "映射" in str(failed_items) or "mapping" in str(failed_items):
                        log_lines.append(f"    {_('user.stage.suggestion')}: {_('user.stage.mapping_config_suggestion')}")

        elif stage_name in ["address_group_processing", "service_group_processing", "zone_processing"]:
            # 处理组和区域 - 支持嵌套数据结构
            nested_result = None
            if "address_groups" in stage_result:
                nested_result = stage_result["address_groups"]
            elif "service_groups" in stage_result:
                nested_result = stage_result["service_groups"]
            elif "zones" in stage_result:
                nested_result = stage_result["zones"]

            if nested_result:
                converted = nested_result.get("converted_count", 0)
                failed = nested_result.get("failed_count", 0)
                skipped = nested_result.get("skipped_count", 0)

                if converted > 0:
                    log_lines.append(f"  ✓ {_('user.stage.success_converted', count=converted)}")

                    # 显示具体的转换对象
                    converted_items = nested_result.get("converted", {})
                    if isinstance(converted_items, dict) and converted_items:
                        count = 0
                        for name, config in converted_items.items():
                            if count >= 3:
                                break
                            log_lines.append(f"    - {name}")
                            count += 1
                        if len(converted_items) > 3:
                            log_lines.append(f"    - {_('user.stage.and_others', count=len(converted_items)-3)}")

                elif failed == 0 and skipped == 0:
                    log_lines.append(f"  {_('user.stage.no_processing_needed')}")

                if failed > 0:
                    log_lines.append(f"  ✗ {_('user.stage.failed_converted', count=failed)}")

                    # 显示失败的详细原因
                    failed_items = nested_result.get("failed", {})
                    if isinstance(failed_items, dict) and failed_items:
                        log_lines.append(f"    {_('user.stage.failed_details')}:")
                        count = 0
                        for name, reason in failed_items.items():
                            if count >= 3:
                                break
                            reason_text = reason.get("reason", _("user.stage.unknown_reason")) if isinstance(reason, dict) else str(reason)
                            log_lines.append(f"      • {name}: {reason_text}")
                            count += 1
                        if len(failed_items) > 3:
                            log_lines.append(f"      • {_('user.stage.and_others', count=len(failed_items)-3)}")

                if skipped > 0:
                    log_lines.append(f"  ⚠ {_('user.stage.skipped_converted', count=skipped)}")

                    # 显示跳过的详细原因
                    skipped_items = nested_result.get("skipped", {})
                    if isinstance(skipped_items, dict) and skipped_items:
                        log_lines.append(f"    {_('user.stage.skipped_details')}:")
                        count = 0
                        for name, reason in skipped_items.items():
                            if count >= 3:
                                break
                            reason_text = reason.get("reason", _("user.stage.unknown_reason")) if isinstance(reason, dict) else str(reason)
                            log_lines.append(f"      • {name}: {reason_text}")
                            count += 1
                        if len(skipped_items) > 3:
                            log_lines.append(f"      • {_('user.stage.and_others', count=len(skipped_items)-3)}")
            else:
                log_lines.append(f"  {_('user.stage.no_processing_needed')}")

        elif stage_name == "xml_template_integration":
            if stage_result.get("template_applied", False) or stage_result.get("xml_generated", False):
                log_lines.append(f"  ✓ {_('user.stage.success_converted', count=1)}")
            else:
                log_lines.append(f"  ✗ {_('user.stage.failed_converted', count=1)}")

        else:
            # 其他阶段的通用处理
            if not stage_result or (isinstance(stage_result, dict) and not stage_result):
                log_lines.append(f"  {_('user.stage.no_processing_needed')}")
            else:
                # 尝试从结果中推断状态
                success = stage_result.get("success", True)
                if success:
                    log_lines.append(f"  ✓ {_('user.stage.processing_completed')}")
                else:
                    log_lines.append(f"  ✗ {_('user.stage.processing_failed')}")

        return "\n".join(log_lines)

# 全局用户日志收集器
_user_log_collector = UserLogCollector()

# 用户日志记录工具函数
def record_stage_user_log(stage_name: str, stage_result: dict, stage_title: str = None, collect_only: bool = True):
    """
    记录阶段处理结果到用户日志

    Args:
        stage_name: 阶段名称
        stage_result: 阶段处理结果
        stage_title: 阶段显示标题（可选）
    """
    from engine.utils.logger import user_log
    from engine.utils.i18n import _

    # 如果没有提供标题，使用翻译键获取标题
    if not stage_title:
        stage_title_keys = {
            "operation_mode": "user.stage.operation_mode",
            "interface_processing": "user.stage.interface_processing",
            "address_processing": "user.stage.address_processing",
            "address_group_processing": "user.stage.address_group_processing",
            "service_processing": "user.stage.service_processing",
            "service_group_processing": "user.stage.service_group_processing",
            "zone_processing": "user.stage.zone_processing",
            "time_range_processing": "user.stage.time_range_processing",
            "dns_processing": "user.stage.dns_processing",
            "static_route_processing": "user.stage.static_route_processing",
            "fortigate_policy_conversion": "user.stage.policy_conversion",
            "xml_template_integration": "user.stage.xml_integration"
        }
        title_key = stage_title_keys.get(stage_name, "user.stage.unknown")
        stage_title = _(title_key)

    # 生成用户日志内容
    log_lines = [f"{stage_title}："]

    # 根据不同的阶段结果格式生成统计信息
    if stage_name == "operation_mode":
        if "mode" in stage_result:
            mode = stage_result["mode"]
            log_lines.append(f"  ✓ {_('user.stage.mode_detected', mode=mode)}")

    elif stage_name == "interface_processing":
        # 实际的接口处理结果格式：converted/failed/skipped 是字典
        converted_dict = stage_result.get("converted", {})
        failed_dict = stage_result.get("failed", {})
        skipped_dict = stage_result.get("skipped", {})

        # 从统计信息中获取数量
        stats = stage_result.get("statistics", {})
        converted_count = stats.get("converted_interfaces", len(converted_dict))
        failed_count = stats.get("failed_interfaces", len(failed_dict))
        skipped_count = stats.get("skipped_interfaces", len(skipped_dict))

        if converted_count > 0:
            log_lines.append(f"  ✓ {_('user.stage.success_converted', count=converted_count)}")
            # 显示具体的接口映射
            count = 0
            for interface_name, interface_config in converted_dict.items():
                if count >= 5:
                    break
                mapped_name = interface_config.get("name", interface_name) if isinstance(interface_config, dict) else interface_name
                log_lines.append(f"    - {interface_name} → {mapped_name}")
                count += 1
            if len(converted_dict) > 5:
                log_lines.append(f"    - {_('user.stage.and_others', count=len(converted_dict)-5)}")

        if failed_count > 0:
            log_lines.append(f"  ✗ {_('user.stage.failed_converted', count=failed_count)}")
            if failed_dict:
                log_lines.append(f"    {_('user.stage.failed_details')}:")
                count = 0
                for interface_name, failure_info in failed_dict.items():
                    if count >= 5:
                        break
                    reason = failure_info.get("reason", _("user.stage.missing_interface_mapping")) if isinstance(failure_info, dict) else str(failure_info)
                    log_lines.append(f"      • {interface_name}: {reason}")
                    count += 1
                if len(failed_dict) > 5:
                    log_lines.append(f"      • {_('user.stage.and_others', count=len(failed_dict)-5)}")
                log_lines.append(f"    {_('user.stage.suggestion')}: {_('user.stage.interface_mapping_suggestion')}")

        if skipped_count > 0:
            log_lines.append(f"  ⚠ {_('user.stage.skipped_converted', count=skipped_count)}")
            if skipped_dict:
                log_lines.append(f"    {_('user.stage.skipped_details')}:")
                count = 0
                for interface_name, skip_info in skipped_dict.items():
                    if count >= 5:
                        break
                    reason = skip_info.get("reason", _("user.stage.interface_type_unsupported")) if isinstance(skip_info, dict) else str(skip_info)
                    log_lines.append(f"      • {interface_name}: {reason}")
                    count += 1
                if len(skipped_dict) > 5:
                    log_lines.append(f"      • {_('user.stage.and_others', count=len(skipped_dict)-5)}")
                log_lines.append(f"    {_('user.stage.suggestion')}: {_('user.stage.interface_type_suggestion')}")

    elif stage_name in ["address_processing", "service_processing"]:
        # 处理地址对象和服务对象 - 支持嵌套数据结构
        stats = stage_result.get("statistics", {})

        # 尝试从嵌套结构中获取数据
        nested_data = None
        if stage_name == "address_processing":
            nested_data = stage_result.get("address_objects", {})
        elif stage_name == "service_processing":
            nested_data = stage_result.get("service_objects", {})

        # 获取统计数据
        if stats:
            converted_key = "converted_addresses" if "addresses" in str(stats) else "converted_services" if "services" in str(stats) else "converted"
            failed_key = "failed_addresses" if "addresses" in str(stats) else "failed_services" if "services" in str(stats) else "failed"
            skipped_key = "skipped_addresses" if "addresses" in str(stats) else "skipped_services" if "services" in str(stats) else "skipped"

            converted = stats.get(converted_key, 0)
            failed = stats.get(failed_key, 0)
            skipped = stats.get(skipped_key, 0)
        elif nested_data:
            # 从嵌套数据中获取统计
            converted = nested_data.get("converted_count", 0)
            failed = nested_data.get("failed_count", 0)
            skipped = nested_data.get("skipped_count", 0)
        else:
            converted = failed = skipped = 0

        if converted > 0:
            log_lines.append(f"  ✓ {_('user.stage.success_converted', count=converted)}")

        if skipped > 0:
            log_lines.append(f"  ⚠ {_('user.stage.skipped_converted', count=skipped)}")
            # 显示跳过的详细原因 - 支持多层嵌套
            skipped_items = stage_result.get("skipped", {})
            if not skipped_items and nested_data:
                skipped_items = nested_data.get("skipped", {})

            if isinstance(skipped_items, dict) and skipped_items:
                log_lines.append(f"    {_('user.stage.skipped_details')}:")
                count = 0
                for name, reason in skipped_items.items():
                    if count >= 5:
                        break
                    reason_text = reason.get("reason", _("user.stage.unknown_reason")) if isinstance(reason, dict) else str(reason)
                    log_lines.append(f"      • {name}: {reason_text}")
                    count += 1
                if len(skipped_items) > 5:
                    log_lines.append(f"      • {_('user.stage.and_others', count=len(skipped_items)-5)}")

                # 添加操作建议
                if "FQDN" in str(skipped_items) or "dynamic" in str(skipped_items) or "fqdn" in str(skipped_items):
                    log_lines.append(f"    {_('user.stage.suggestion')}: {_('user.stage.fqdn_not_supported_suggestion')}")

        if failed > 0:
            log_lines.append(f"  ✗ {_('user.stage.failed_converted', count=failed)}")
            # 显示失败的详细原因 - 支持多层嵌套
            failed_items = stage_result.get("failed", {})
            if not failed_items and nested_data:
                failed_items = nested_data.get("failed", {})

            if isinstance(failed_items, dict) and failed_items:
                log_lines.append(f"    {_('user.stage.failed_details')}:")
                count = 0
                for name, reason in failed_items.items():
                    if count >= 5:
                        break
                    reason_text = reason.get("reason", _("user.stage.unknown_reason")) if isinstance(reason, dict) else str(reason)
                    log_lines.append(f"      • {name}: {reason_text}")
                    count += 1
                if len(failed_items) > 5:
                    log_lines.append(f"      • {_('user.stage.and_others', count=len(failed_items)-5)}")

                # 添加操作建议
                if "subnet" in str(failed_items) or "地址" in str(failed_items) or "address" in str(failed_items):
                    log_lines.append(f"    {_('user.stage.suggestion')}: {_('user.stage.address_config_suggestion')}")
                elif "映射" in str(failed_items) or "mapping" in str(failed_items):
                    log_lines.append(f"    {_('user.stage.suggestion')}: {_('user.stage.mapping_config_suggestion')}")

    elif stage_name in ["address_group_processing", "service_group_processing", "zone_processing"]:
        # 处理组和区域 - 支持嵌套数据结构
        nested_result = None
        if "address_groups" in stage_result:
            nested_result = stage_result["address_groups"]
        elif "service_groups" in stage_result:
            nested_result = stage_result["service_groups"]
        elif "zones" in stage_result:
            nested_result = stage_result["zones"]

        if nested_result:
            converted = nested_result.get("converted_count", 0)
            failed = nested_result.get("failed_count", 0)
            skipped = nested_result.get("skipped_count", 0)

            if converted > 0:
                log_lines.append(f"  ✓ {_('user.stage.success_converted', count=converted)}")

                # 显示具体的转换对象
                converted_items = nested_result.get("converted", {})
                if isinstance(converted_items, dict) and converted_items:
                    count = 0
                    for name, config in converted_items.items():
                        if count >= 3:
                            break
                        log_lines.append(f"    - {name}")
                        count += 1
                    if len(converted_items) > 3:
                        log_lines.append(f"    - {_('user.stage.and_others', count=len(converted_items)-3)}")

            elif failed == 0 and skipped == 0:
                log_lines.append(f"  {_('user.stage.no_processing_needed')}")

            if failed > 0:
                log_lines.append(f"  ✗ {_('user.stage.failed_converted', count=failed)}")

                # 显示失败的详细原因
                failed_items = nested_result.get("failed", {})
                if isinstance(failed_items, dict) and failed_items:
                    log_lines.append(f"    {_('user.stage.failed_details')}:")
                    count = 0
                    for name, reason in failed_items.items():
                        if count >= 3:
                            break
                        reason_text = reason.get("reason", _("user.stage.unknown_reason")) if isinstance(reason, dict) else str(reason)
                        log_lines.append(f"      • {name}: {reason_text}")
                        count += 1
                    if len(failed_items) > 3:
                        log_lines.append(f"      • {_('user.stage.and_others', count=len(failed_items)-3)}")

            if skipped > 0:
                log_lines.append(f"  ⚠ {_('user.stage.skipped_converted', count=skipped)}")

                # 显示跳过的详细原因
                skipped_items = nested_result.get("skipped", {})
                if isinstance(skipped_items, dict) and skipped_items:
                    log_lines.append(f"    {_('user.stage.skipped_details')}:")
                    count = 0
                    for name, reason in skipped_items.items():
                        if count >= 3:
                            break
                        reason_text = reason.get("reason", _("user.stage.unknown_reason")) if isinstance(reason, dict) else str(reason)
                        log_lines.append(f"      • {name}: {reason_text}")
                        count += 1
                    if len(skipped_items) > 3:
                        log_lines.append(f"      • {_('user.stage.and_others', count=len(skipped_items)-3)}")
        else:
            log_lines.append(f"  {_('user.stage.no_processing_needed')}")

    elif stage_name == "xml_template_integration":
        if stage_result.get("template_applied", False) or stage_result.get("xml_generated", False):
            log_lines.append(f"  ✓ {_('user.stage.success_converted', count=1)}")
        else:
            log_lines.append(f"  ✗ {_('user.stage.failed_converted', count=1)}")

    else:
        # 其他阶段的通用处理
        if not stage_result or (isinstance(stage_result, dict) and not stage_result):
            log_lines.append(f"  {_('user.stage.no_processing_needed')}")
        else:
            # 尝试从结果中推断状态
            success = stage_result.get("success", True)
            if success:
                log_lines.append(f"  ✓ {_('user.stage.processing_completed')}")
            else:
                log_lines.append(f"  ✗ {_('user.stage.processing_failed')}")

    # 生成日志内容
    log_content = "\n".join(log_lines)

    # 始终收集统计信息（用于最后的汇总）
    _user_log_collector.add_stage_statistics(stage_name, stage_result)

    # 始终收集详细日志内容（用于最后的集中显示）
    _user_log_collector.add_stage_log(stage_name, log_content)

    # 默认不立即输出，只在最后的"详细转换统计"中显示
    # 如果明确要求立即输出，则输出
    if not collect_only:
        user_log(log_content, summary=True)

# 用户日志收集器控制函数
def enable_user_log_collection():
    """启用用户日志收集模式"""
    _user_log_collector.enable_collection()

def output_collected_user_logs():
    """输出收集的用户日志"""
    _user_log_collector.output_collected_logs()

def output_final_user_statistics():
    """输出最终的集中统计信息"""
    _user_log_collector.output_final_statistics()

# 全局格式化器实例
_formatter = None

def get_user_formatter():
    """获取用户日志格式化器实例
    
    Returns:
        UserLogFormatter: 格式化器实例
    """
    global _formatter
    if _formatter is None:
        _formatter = UserLogFormatter()
    return _formatter
