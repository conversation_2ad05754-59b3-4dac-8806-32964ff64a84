#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
用户操作建议引擎
基于转换结果分析，生成智能化的用户操作建议
"""

from typing import Dict, Any, List, Optional
from .i18n import _


class UserSuggestionEngine:
    """用户操作建议引擎"""
    
    def __init__(self, language: str = "zh-CN"):
        """
        初始化建议引擎
        
        Args:
            language: 语言代码
        """
        self.language = language
        self.suggestions = []
        
    def analyze_conversion_results(self, conversion_context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        分析转换结果并生成建议
        
        Args:
            conversion_context: 转换上下文信息
            
        Returns: List[Dict]: 建议列表
        """
        self.suggestions = []
        
        # 分析各个模块的转换结果
        self._analyze_interface_results(conversion_context)
        self._analyze_address_results(conversion_context)
        self._analyze_service_results(conversion_context)
        self._analyze_policy_results(conversion_context)
        self._analyze_overall_results(conversion_context)
        
        return self.suggestions
    
    def _analyze_interface_results(self, context: Dict[str, Any]):
        """分析接口转换结果"""
        interface_result = context.get('interface_processing_result', {})
        if not interface_result:
            return
            
        statistics = interface_result.get('statistics', {})
        failed_count = statistics.get('failed_interfaces', 0)
        skipped_count = statistics.get('skipped_interfaces', 0)
        
        # 分析失败的接口
        if failed_count > 0:
            failed_interfaces = interface_result.get('failed', {})
            self._generate_interface_failure_suggestions(failed_interfaces)
        
        # 分析跳过的接口
        if skipped_count > 0:
            skipped_interfaces = interface_result.get('skipped', {})
            self._generate_interface_skipped_suggestions(skipped_interfaces)
    
    def _generate_interface_failure_suggestions(self, failed_interfaces: Dict):
        """生成接口失败的建议"""
        for intf_name, failure_info in failed_interfaces.items():
            reason = failure_info.get('reason', 'unknown')
            
            if 'mapping' in reason.lower():
                # 接口映射问题
                self.suggestions.append({
                    "type": "required",
                    "title": _("suggestion.interface.add_mapping", interface=intf_name, language=self.language),
                    "description": _("suggestion.interface.mapping_missing_desc", interface=intf_name, language=self.language),
                    "action_steps": [
                        _("suggestion.interface.mapping_step1", language=self.language),
                        _("suggestion.interface.mapping_step2", interface=intf_name, language=self.language),
                        _("suggestion.interface.mapping_step3", language=self.language)
                    ],
                    "priority": "high"
                })
            elif 'type' in reason.lower():
                # 接口类型不支持
                self.suggestions.append({
                    "type": "recommended",
                    "title": _("suggestion.interface.manual_config", interface=intf_name, language=self.language),
                    "description": _("suggestion.interface.type_unsupported_desc", interface=intf_name, language=self.language),
                    "action_steps": [
                        _("suggestion.interface.manual_step1", language=self.language),
                        _("suggestion.interface.manual_step2", interface=intf_name, language=self.language)
                    ],
                    "priority": "normal"
                })
    
    def _generate_interface_skipped_suggestions(self, skipped_interfaces: Dict):
        """生成接口跳过的建议"""
        mapping_issues = []
        logical_interfaces = []
        
        for intf_name, skip_info in skipped_interfaces.items():
            reason = skip_info.get('reason', 'unknown')
            if reason == 'no_mapping':
                mapping_issues.append(intf_name)
            elif reason == 'logical_interface':
                logical_interfaces.append(intf_name)
        
        # 批量处理映射问题
        if mapping_issues:
            self.suggestions.append({
                "type": "required",
                "title": _("suggestion.interface.batch_mapping", count=len(mapping_issues), language=self.language),
                "description": _("suggestion.interface.batch_mapping_desc", 
                               interfaces=", ".join(mapping_issues[:5]), language=self.language),
                "action_steps": [
                    _("suggestion.interface.batch_step1", language=self.language),
                    _("suggestion.interface.batch_step2", language=self.language),
                    _("suggestion.interface.batch_step3", language=self.language)
                ],
                "priority": "high"
            })
    
    def _analyze_address_results(self, context: Dict[str, Any]):
        """分析地址对象转换结果"""
        address_result = context.get('address_processing_result', {})
        if not address_result:
            return
            
        address_objects = address_result.get('address_objects', {})
        failed_count = address_objects.get('failed_count', 0)
        skipped_count = address_objects.get('skipped_count', 0)
        
        if failed_count > 0 or skipped_count > 0:
            self._generate_address_suggestions(address_objects)
    
    def _generate_address_suggestions(self, address_objects: Dict):
        """生成地址对象相关建议"""
        failed_addresses = address_objects.get('failed', {})
        skipped_addresses = address_objects.get('skipped', {})
        
        fqdn_addresses = []
        dynamic_addresses = []
        invalid_addresses = []
        
        # 分析失败和跳过的地址对象
        for addr_dict in [failed_addresses, skipped_addresses]:
            for addr_name, addr_info in addr_dict.items():
                if isinstance(addr_info, dict):
                    addr_type = addr_info.get('type', 'unknown')
                    if addr_type == 'fqdn':
                        fqdn_addresses.append(addr_name)
                    elif 'dynamic' in addr_type.lower():
                        dynamic_addresses.append(addr_name)
                    else:
                        invalid_addresses.append(addr_name)
        
        # FQDN地址对象建议
        if fqdn_addresses:
            self.suggestions.append({
                "type": "recommended",
                "title": _("suggestion.address.convert_fqdn", count=len(fqdn_addresses), language=self.language),
                "description": _("suggestion.address.fqdn_desc", 
                               addresses=", ".join(fqdn_addresses[:3]), language=self.language),
                "action_steps": [
                    _("suggestion.address.fqdn_step1", language=self.language),
                    _("suggestion.address.fqdn_step2", language=self.language),
                    _("suggestion.address.fqdn_step3", language=self.language)
                ],
                "priority": "normal"
            })
        
        # 动态地址对象建议
        if dynamic_addresses:
            self.suggestions.append({
                "type": "recommended",
                "title": _("suggestion.address.replace_dynamic", count=len(dynamic_addresses), language=self.language),
                "description": _("suggestion.address.dynamic_desc", 
                               addresses=", ".join(dynamic_addresses[:3]), language=self.language),
                "action_steps": [
                    _("suggestion.address.dynamic_step1", language=self.language),
                    _("suggestion.address.dynamic_step2", language=self.language)
                ],
                "priority": "normal"
            })
    
    def _analyze_service_results(self, context: Dict[str, Any]):
        """分析服务对象转换结果"""
        # 类似的服务对象分析逻辑
        pass
    
    def _analyze_policy_results(self, context: Dict[str, Any]):
        """分析策略规则转换结果"""
        # 类似的策略规则分析逻辑
        pass
    
    def _analyze_overall_results(self, context: Dict[str, Any]):
        """分析整体转换结果"""
        # 生成通用的后续步骤建议
        self.suggestions.append({
            "type": "recommended",
            "title": _("suggestion.general.verify_config", language=self.language),
            "description": _("suggestion.general.verify_desc", language=self.language),
            "action_steps": [
                _("suggestion.general.verify_step1", language=self.language),
                _("suggestion.general.verify_step2", language=self.language),
                _("suggestion.general.verify_step3", language=self.language)
            ],
            "priority": "normal"
        })
        
        self.suggestions.append({
            "type": "optional",
            "title": _("suggestion.general.backup_config", language=self.language),
            "description": _("suggestion.general.backup_desc", language=self.language),
            "action_steps": [
                _("suggestion.general.backup_step1", language=self.language),
                _("suggestion.general.backup_step2", language=self.language)
            ],
            "priority": "low"
        })
    
    def generate_interface_suggestions(self, interface_results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        生成接口相关建议
        
        Args:
            interface_results: 接口处理结果
            
        Returns: List[Dict]: 建议列表
        """
        suggestions = []
        self._analyze_interface_results({"interface_processing_result": interface_results})
        return self.suggestions
    
    def generate_policy_suggestions(self, policy_results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        生成策略相关建议
        
        Args:
            policy_results: 策略处理结果
            
        Returns: List[Dict]: 建议列表
        """
        suggestions = []
        # 实现策略建议逻辑
        return suggestions
    
    def generate_configuration_suggestions(self, overall_results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        生成配置相关建议
        
        Args:
            overall_results: 整体结果
            
        Returns: List[Dict]: 建议列表
        """
        suggestions = []
        # 实现配置建议逻辑
        return suggestions
    
    def generate_post_migration_steps(self, conversion_summary: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        生成迁移后步骤
        
        Args:
            conversion_summary: 转换摘要
            
        Returns: List[Dict]: 步骤列表
        """
        steps = []
        # 实现迁移后步骤逻辑
        return steps


# 全局实例
_suggestion_engine = None

def get_user_suggestion_engine(language: str = "zh-CN") -> UserSuggestionEngine:
    """
    获取用户建议引擎实例
    
    Args:
        language: 语言代码
        
    Returns:
        UserSuggestionEngine: 建议引擎实例
    """
    global _suggestion_engine
    if _suggestion_engine is None or _suggestion_engine.language != language:
        _suggestion_engine = UserSuggestionEngine(language)
    return _suggestion_engine
