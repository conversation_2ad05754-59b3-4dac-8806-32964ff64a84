# -*- coding: utf-8 -*-
"""
验证工具模块
提供各种数据验证功能，包括IP地址、端口范围等
"""

import re
import ipaddress
from typing import Tuple, Optional
from engine.utils.logger import log
from engine.utils.i18n import _


def validate_ip_range(start_ip: str, end_ip: str) -> Tuple[bool, str]:
    """
    验证IP地址范围的有效性
    
    Args:
        start_ip: 起始IP地址
        end_ip: 结束IP地址
        
    Returns: Tuple[bool, str]: (是否有效, 错误信息)
    """
    try:
        # 验证IP地址格式
        try:
            start_addr = ipaddress.IPv4Address(start_ip)
            end_addr = ipaddress.IPv4Address(end_ip)
        except ipaddress.AddressValueError as e:
            return False, f"无效的IP地址格式: {str(e)}"
        
        # 验证范围逻辑（起始IP应小于或等于结束IP）
        if start_addr > end_addr:
            return False, f"起始IP地址 {start_ip} 不能大于结束IP地址 {end_ip}"
        
        # 验证范围大小（避免过大的范围）
        range_size = int(end_addr) - int(start_addr) + 1
        if range_size > 65536:  # 限制最大范围为65536个IP
            return False, f"IP范围过大 ({range_size} 个地址)，最大支持65536个地址"
        
        return True, ""
        
    except Exception as e:
        return False, f"验证IP范围时发生错误: {str(e)}"


def parse_iprange_string(iprange: str) -> Tuple[str, str]:
    """
    解析IP范围字符串
    
    Args:
        iprange: IP范围字符串，格式为 "start_ip-end_ip"
        
    Returns: Tuple[str, str]: (起始IP, 结束IP)
        
    Raises:
        ValueError: 当格式无效时抛出异常
    """
    try:
        if not iprange or not isinstance(iprange, str):
            raise ValueError("IP范围字符串不能为空")
        
        iprange = iprange.strip()
        if '-' not in iprange:
            raise ValueError("IP范围格式无效，缺少连字符分隔符")
        
        parts = iprange.split('-', 1)  # 只分割第一个连字符
        if len(parts) != 2:
            raise ValueError("IP范围格式无效，分割后部分数量不正确")
        
        start_ip = parts[0].strip()
        end_ip = parts[1].strip()
        
        if not start_ip or not end_ip:
            raise ValueError("起始IP或结束IP不能为空")
        
        return start_ip, end_ip
        
    except Exception as e:
        raise ValueError(f"解析IP范围字符串失败: {str(e)}")


def is_valid_ip_address(ip: str) -> bool:
    """
    验证单个IP地址是否有效
    
    Args:
        ip: IP地址字符串
        
    Returns:
        bool: 是否为有效的IP地址
    """
    try:
        ipaddress.IPv4Address(ip)
        return True
    except (ipaddress.AddressValueError, ValueError):
        # 如果ipaddress模块验证失败，使用正则表达式作为备选
        try:
            ip_pattern = r'^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$'
            match = re.match(ip_pattern, ip)
            if match:
                # 检查每个八位组是否在0-255范围内
                octets = [int(octet) for octet in match.groups()]
                return all(0 <= octet <= 255 for octet in octets)
            return False
        except Exception:
            return False


def validate_port_range(port_range: str) -> Tuple[bool, str]:
    """
    验证端口范围格式
    
    Args:
        port_range: 端口范围字符串，如 "80", "80-443", "80,443,8080-8090"
        
    Returns: Tuple[bool, str]: (是否有效, 错误信息)
    """
    try:
        if not port_range or not isinstance(port_range, str):
            return False, "端口范围不能为空"
        
        port_range = port_range.strip()
        
        # 分割逗号分隔的端口
        port_parts = [part.strip() for part in port_range.split(',')]
        
        for part in port_parts:
            if not part:
                continue
                
            if '-' in part:
                # 端口范围格式
                range_parts = part.split('-', 1)
                if len(range_parts) != 2:
                    return False, f"无效的端口范围格式: {part}"
                
                try:
                    start_port = int(range_parts[0].strip())
                    end_port = int(range_parts[1].strip())
                except ValueError:
                    return False, f"端口范围包含非数字字符: {part}"
                
                if not (1 <= start_port <= 65535) or not (1 <= end_port <= 65535):
                    return False, f"端口号超出有效范围(1-65535): {part}"
                
                if start_port > end_port:
                    return False, f"起始端口不能大于结束端口: {part}"
            else:
                # 单个端口
                try:
                    port = int(part)
                except ValueError:
                    return False, f"端口包含非数字字符: {part}"
                
                if not (1 <= port <= 65535):
                    return False, f"端口号超出有效范围(1-65535): {part}"
        
        return True, ""
        
    except Exception as e:
        return False, f"验证端口范围时发生错误: {str(e)}"


def normalize_ip_range(start_ip: str, end_ip: str) -> str:
    """
    标准化IP范围格式
    
    Args:
        start_ip: 起始IP地址
        end_ip: 结束IP地址
        
    Returns:
        str: 标准化的IP范围字符串
    """
    try:
        # 验证IP地址有效性
        start_addr = ipaddress.IPv4Address(start_ip)
        end_addr = ipaddress.IPv4Address(end_ip)
        
        # 返回标准化格式
        return f"{str(start_addr)}-{str(end_addr)}"
        
    except Exception as e:
        log(f"标准化IP范围时发生错误: {str(e)}", "error")
        return f"{start_ip}-{end_ip}"  # 返回原始格式


def generate_address_name_from_service(service_name: str, suffix: str = "RANGE") -> str:
    """
    从服务名称生成地址对象名称
    
    Args:
        service_name: 原始服务名称
        suffix: 名称后缀
        
    Returns:
        str: 生成的地址对象名称
    """
    try:
        # 清理服务名称，移除不允许的字符
        clean_name = re.sub(r'[^a-zA-Z0-9_-]', '_', service_name)
        
        # 确保名称不以数字开头
        if clean_name and clean_name[0].isdigit():
            clean_name = f"SVC_{clean_name}"
        
        # 生成最终名称
        if suffix:
            return f"{clean_name}_{suffix}".upper()
        else:
            return clean_name.upper()
            
    except Exception as e:
        log(f"生成地址对象名称时发生错误: {str(e)}", "error")
        return f"SERVICE_{suffix}".upper()
