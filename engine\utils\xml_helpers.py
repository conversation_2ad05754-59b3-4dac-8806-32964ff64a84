#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
XML处理辅助函数模块
提供通用的XML元素处理功能，包括查找、创建、更新和合并XML元素
"""

import logging
from lxml import etree
from engine.utils.i18n import _

logger = logging.getLogger(__name__)

def find_or_create_xml_element(parent, tag, namespace=None, add_namespace=False):
    """
    查找或创建XML元素
    
    如果元素已存在，则返回该元素；否则创建新元素并返回
    
    Args:
        parent: 父XML元素
        tag: 元素标签名
        namespace: 命名空间URI (可选)
        add_namespace: 是否添加xmlns属性 (默认False)
    
    Returns:
        lxml.etree.Element: 查找到的或新创建的XML元素
    """
    # 首先尝试查找现有元素
    if namespace:
        ns_tag = "{{{}}}{}".format(namespace, tag)
        element = parent.find(ns_tag)
    else:
        element = parent.find(tag)
    
    # 如果找到元素，记录并返回
    if element is not None:
        logger.debug(_("info.found_existing_xml_element").format(tag=tag, method="find"))
        return element
    
    # 创建新元素
    if namespace:
        if '{' in tag:
            # 如果标签已经包含命名空间
            element = etree.SubElement(parent, tag)
        else:
            element = etree.SubElement(parent, "{{{}}}{}".format(namespace, tag))
    else:
        element = etree.SubElement(parent, tag)
    
    # 添加命名空间属性（如果需要）
    if namespace and add_namespace:
        element.set('xmlns', namespace)
    
    logger.debug(_("info.created_new_xml_element").format(tag=tag))
    return element

def append_or_update_xml_element(parent, tag, value=None, attributes=None, namespace=None):
    """
    添加或更新XML元素
    
    如果元素不存在，则创建；如果存在，则更新其值和属性
    
    Args:
        parent: 父XML元素
        tag: 元素标签名
        value: 元素值 (可选)
        attributes: 属性字典 (可选)
        namespace: 命名空间URI (可选)
    
    Returns:
        lxml.etree.Element: 更新或创建的XML元素
    """
    # 查找或创建元素
    element = find_or_create_xml_element(parent, tag, namespace)
    
    # 设置值（如果提供）
    if value is not None:
        element.text = str(value)
    
    # 设置属性（如果提供）
    if attributes:
        for attr_name, attr_value in attributes.items():
            element.set(attr_name, str(attr_value))
    
    return element

def find_xml_element_by_child_value(parent, tag, child_tag, child_value):
    """
    通过子元素值查找XML元素
    
    查找具有特定子元素值的XML元素
    
    Args:
        parent: 父XML元素
        tag: 要查找的元素标签名
        child_tag: 子元素标签名
        child_value: 子元素的值
    
    Returns:
        lxml.etree.Element: 找到的元素，如果未找到则返回None
    """
    elements = parent.findall(tag)
    for element in elements:
        child = element.find(child_tag)
        if child is not None and child.text == child_value:
            logger.debug(_("info.found_existing_xml_element").format(
                tag=tag, method="child_value"
            ))
            return element
    return None

def remove_duplicates_by_child_value(parent, tag, child_tag, preserve_first=True):
    """
    通过子元素值移除重复的XML元素
    
    Args:
        parent: 父XML元素
        tag: 元素标签名
        child_tag: 用于标识的子元素标签名
        preserve_first: 是否保留第一个元素 (默认True)
    
    Returns:
        int: 移除的重复元素数量
    """
    elements = parent.findall(tag)
    if not elements or len(elements) <= 1:
        return 0
    
    # 收集所有子元素值
    values = {}
    for idx, element in enumerate(elements):
        child = element.find(child_tag)
        if child is not None and child.text is not None:
            value = child.text
            if value in values:
                values[value].append((idx, element))
            else:
                values[value] = [(idx, element)]
    
    # 移除重复元素
    removed_count = 0
    for value, elements_list in values.items():
        if len(elements_list) > 1:
            # 排序以确保较低索引的元素在前
            elements_list.sort(key=lambda x: x[0])
            
            # 决定保留哪个元素
            start_idx = 1 if preserve_first else 0
            end_idx = len(elements_list) if preserve_first else len(elements_list) - 1
            
            # 移除重复元素
            for idx, element in elements_list[start_idx:end_idx]:
                child = element.find(child_tag)
                logger.info(_("info.removed_duplicate_element").format(
                    name=child_tag, value=child.text if child is not None else "unknown"
                ))
                parent.remove(element)
                removed_count += 1
    
    return removed_count

def merge_duplicate_xml_elements(parent, tag, id_child_tag):
    """
    合并具有相同标识的XML元素
    
    查找具有相同子元素值的元素，并将后续元素的所有子元素合并到第一个元素中
    
    Args:
        parent: 父XML元素
        tag: 元素标签名
        id_child_tag: 用于标识的子元素标签名
    
    Returns:
        int: 合并的元素数量
    """
    elements = parent.findall(tag)
    if not elements or len(elements) <= 1:
        return 0
    
    # 按标识子元素的值分组
    element_groups = {}
    for element in elements:
        id_child = element.find(id_child_tag)
        if id_child is not None and id_child.text is not None:
            id_value = id_child.text
            if id_value in element_groups:
                element_groups[id_value].append(element)
            else:
                element_groups[id_value] = [element]
    
    # 合并具有相同标识的元素
    merged_count = 0
    for id_value, group in element_groups.items():
        if len(group) > 1:
            # 第一个元素作为主要元素
            primary = group[0]
            
            # 遍历其余元素
            for duplicate in group[1:]:
                # 获取命名空间
                namespace = None
                if primary.tag.startswith('{'):
                    ns_end = primary.tag.find('}')
                    if ns_end > 0:
                        namespace = primary.tag[1:ns_end]
                
                # 从重复元素中复制所有子元素到主要元素
                for child in duplicate:
                    # 检查主要元素是否已有相同的子元素
                    child_name = child.tag
                    if namespace and not child.tag.startswith('{'):
                        child_name = "{{{}}}{}".format(namespace, child.tag)
                    
                    # 复制或合并子元素
                    existing = primary.find(child_name)
                    if existing is None:
                        # 如果子元素不存在，则复制
                        new_child = etree.SubElement(primary, child_name)
                        if child.text:
                            new_child.text = child.text
                        
                        # 复制属性
                        for attr_name, attr_value in child.attrib.items():
                            new_child.set(attr_name, attr_value)
                        
                        logger.debug(_("info.merged_time_range_from_duplicate").format(name=child_name))
                    
                # 从父元素中移除重复元素
                parent.remove(duplicate)
                merged_count += 1
                
                logger.debug(_("info.merged_duplicate_xml_element").format(
                    tag=tag, id=id_value
                ))
    
    if merged_count > 0:
        logger.info(_("info.merged_duplicate_time_range_nodes").format(count=merged_count))
    
    return merged_count

def cleanup_empty_xml_elements(parent, tag, child_tags=None):
    """
    清理空的XML元素
    
    移除没有子元素或指定子元素为空的XML元素
    
    Args:
        parent: 父XML元素
        tag: 要清理的元素标签名
        child_tags: 检查是否为空的子元素标签列表 (可选)
    
    Returns:
        int: 移除的空元素数量
    """
    elements = parent.findall(tag)
    if not elements:
        return 0
    
    removed_count = 0
    for element in elements:
        # 检查元素是否为空
        is_empty = False
        
        if child_tags:
            # 检查指定的子元素是否存在且非空
            has_valid_children = False
            for child_tag in child_tags:
                children = element.findall(child_tag)
                if children and any(child.text or len(child) > 0 for child in children):
                    has_valid_children = True
                    break
            is_empty = not has_valid_children
        else:
            # 检查是否没有子元素且没有文本
            is_empty = len(element) == 0 and not element.text
        
        # 如果为空，移除元素
        if is_empty:
            logger.info(_("info.removing_empty_xml_element").format(tag=tag))
            parent.remove(element)
            removed_count += 1
    
    return removed_count 