#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
XML后处理修复器
用于修复FortiGate转换后的XML结构问题
"""

import os
import sys
from lxml import etree
from typing import List, Optional
import shutil
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from engine.utils.logger import log


class XmlPostProcessor:
    """XML后处理修复器 - 增强版本，支持YANG模型验证修复"""

    def __init__(self, name_mapping_manager=None):
        self.fixes_applied = []
        self.backup_created = False

        # 名称映射管理器
        self.name_mapping_manager = name_mapping_manager
        if self.name_mapping_manager is None:
            try:
                from engine.utils.name_mapping_manager import NameMappingManager
                self.name_mapping_manager = NameMappingManager()
            except ImportError:
                self.name_mapping_manager = None

        # 需要name键的列表元素配置
        self.list_elements_requiring_name = {
            # NAT相关
            'pool': 'nat_pools',
            'rule': 'nat_rules',

            # 服务对象相关
            'service-set': 'services',
            'service-group': 'service_groups',

            # 地址对象相关
            'address-set': 'addresses',
            'address-group': 'address_groups',

            # 安全策略相关
            'policy': 'policies',

            # 安全区域相关
            'security-zone': 'zones',

            # 时间对象相关 - 注意：security-policy中的time-range是引用元素，不需要name子元素
            # 'time-range': 'time_ranges'  # 已禁用：time-range在security-policy中是简单引用，不应该有name子元素
        }

        # 修复统计信息
        self.fix_stats = {
            'missing_name_keys_fixed': 0,
            'invalid_names_cleaned': 0,
            'invalid_descriptions_cleaned': 0,
            'total_elements_processed': 0
        }
    
    def process_xml_file(self, xml_file_path: str) -> bool:
        """
        处理XML文件，应用所有修复
        
        Args:
            xml_file_path: XML文件路径
            
        Returns:
            bool: 处理是否成功
        """
        try:
            log(f"🔧 开始后处理修复XML文件: {xml_file_path}", "info")
            
            # 创建备份
            if not self._create_backup(xml_file_path):
                return False
            
            # 解析XML文件
            tree = etree.parse(xml_file_path)
            root = tree.getroot()
            
            # 应用所有修复
            self._fix_vlan_interface_structure(root)
            self._fix_physical_interface_config(root)
            self._add_vlan_access_control(root)
            
            # 保存修复后的XML
            self._save_fixed_xml(tree, xml_file_path)
            
            # 输出修复报告
            self._print_fix_report()
            
            log(f"✅ XML后处理修复完成: {xml_file_path}", "info")
            return True
            
        except Exception as e:
            log(f"❌ XML后处理修复失败: {str(e)}", "error")
            import traceback
            log(f"详细错误信息: {traceback.format_exc()}", "error")
            return False
    
    def _create_backup(self, xml_file_path: str) -> bool:
        """创建XML文件备份"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"{xml_file_path}.backup_{timestamp}"
            shutil.copy2(xml_file_path, backup_path)
            self.backup_created = True
            log(f"📋 创建备份文件: {backup_path}", "info")
            return True
        except Exception as e:
            log(f"❌ 创建备份失败: {str(e)}", "error")
            return False
    
    def _fix_vlan_interface_structure(self, root: etree.Element):
        """修复VLAN接口结构：将VRF根级别的VLAN接口移动到interface容器内部"""
        try:
            log("🔧 开始修复VLAN接口结构", "info")
            
            # 查找VRF元素
            vrf_elem = self._find_vrf_element(root)
            if vrf_elem is None:
                log("❌ 未找到VRF元素", "warning")
                return
            
            # 查找interface容器
            interface_elem = self._find_interface_container(vrf_elem)
            if interface_elem is None:
                log("❌ 未找到interface容器", "warning")
                return
            
            # 查找VRF根级别的VLAN接口
            misplaced_vlans = []
            for child in list(vrf_elem):
                if child.tag.endswith("vlan") and child.getparent() == vrf_elem:
                    misplaced_vlans.append(child)
            
            if misplaced_vlans:
                log(f"🚨 发现 {len(misplaced_vlans)} 个错误放置的VLAN接口", "warning")
                
                # 移动VLAN接口到interface容器内部
                for vlan_elem in misplaced_vlans:
                    # 从VRF中移除
                    vrf_elem.remove(vlan_elem)
                    
                    # 添加到interface容器中
                    interface_elem.append(vlan_elem)
                    
                    # 获取VLAN接口名称
                    vlan_name = self._get_vlan_name(vlan_elem)
                    log(f"✅ 修复VLAN接口: {vlan_name} 已移动到interface容器", "info")
                
                self.fixes_applied.append(f"VLAN接口结构修复: {len(misplaced_vlans)}个接口")
                log(f"🎉 VLAN接口结构修复完成，修复了 {len(misplaced_vlans)} 个接口", "info")
            else:
                log("✅ 所有VLAN接口结构正确", "debug")
                
        except Exception as e:
            log(f"❌ 修复VLAN接口结构失败: {str(e)}", "error")
    
    def _fix_physical_interface_config(self, root: etree.Element):
        """修复物理接口配置：移除多余的enabled和working-mode配置"""
        try:
            log("🔧 开始修复物理接口配置", "info")
            
            # 查找所有物理接口
            physical_interfaces = root.xpath(".//*[local-name()='physical']")
            
            fixed_count = 0
            for physical in physical_interfaces:
                # 移除多余的enabled元素
                enabled_elems = physical.xpath(".//*[local-name()='enabled' and parent::*[local-name()='physical']]")
                for elem in enabled_elems:
                    if elem.getparent() == physical:  # 确保是直接子元素
                        physical.remove(elem)
                        fixed_count += 1
                
                # 移除多余的working-mode元素
                working_mode_elems = physical.xpath(".//*[local-name()='working-mode' and parent::*[local-name()='physical']]")
                for elem in working_mode_elems:
                    if elem.getparent() == physical:  # 确保是直接子元素
                        physical.remove(elem)
                        fixed_count += 1
            
            if fixed_count > 0:
                self.fixes_applied.append(f"物理接口配置修复: 移除{fixed_count}个多余配置")
                log(f"✅ 物理接口配置修复完成，移除了 {fixed_count} 个多余配置", "info")
            else:
                log("✅ 物理接口配置正确", "debug")
                
        except Exception as e:
            log(f"❌ 修复物理接口配置失败: {str(e)}", "error")
    
    def _add_vlan_access_control(self, root: etree.Element):
        """为VLAN接口添加access-control配置"""
        try:
            log("🔧 开始为VLAN接口添加access-control配置", "info")
            
            # 查找所有VLAN接口
            vlan_interfaces = root.xpath(".//*[local-name()='vlan']")
            
            added_count = 0
            for vlan in vlan_interfaces:
                # 检查是否已有access-control配置
                existing_ac = vlan.xpath(".//*[local-name()='access-control']")
                if existing_ac:
                    continue  # 已有配置，跳过
                
                # 创建access-control配置
                access_control = etree.SubElement(vlan, "access-control")
                access_control.set("xmlns", "urn:ruijie:ntos:params:xml:ns:yang:local-defend")
                
                # 添加默认配置
                etree.SubElement(access_control, "https").text = "false"
                etree.SubElement(access_control, "ping").text = "true"
                etree.SubElement(access_control, "ssh").text = "false"
                
                added_count += 1
                
                # 获取VLAN接口名称用于日志
                vlan_name = self._get_vlan_name(vlan)
                log(f"✅ 为VLAN接口 {vlan_name} 添加access-control配置", "debug")
            
            if added_count > 0:
                self.fixes_applied.append(f"VLAN access-control配置: 添加{added_count}个配置")
                log(f"✅ VLAN access-control配置添加完成，添加了 {added_count} 个配置", "info")
            else:
                log("✅ 所有VLAN接口已有access-control配置", "debug")
                
        except Exception as e:
            log(f"❌ 添加VLAN access-control配置失败: {str(e)}", "error")
    
    def _find_vrf_element(self, root: etree.Element) -> Optional[etree.Element]:
        """查找VRF元素"""
        # 方法1：直接查找
        vrf_elem = root.find(".//vrf")
        if vrf_elem is not None:
            return vrf_elem
        
        # 方法2：使用XPath查找
        try:
            vrf_elements = root.xpath(".//*[local-name()='vrf']")
            if vrf_elements:
                return vrf_elements[0]
        except:
            pass
        
        return None
    
    def _find_interface_container(self, vrf_elem: etree.Element) -> Optional[etree.Element]:
        """查找interface容器"""
        # 方法1：直接查找
        interface_elem = vrf_elem.find(".//interface")
        if interface_elem is not None:
            return interface_elem
        
        # 方法2：使用XPath查找
        try:
            interface_elements = vrf_elem.xpath(".//*[local-name()='interface']")
            if interface_elements:
                return interface_elements[0]
        except:
            pass
        
        return None
    
    def _get_vlan_name(self, vlan_elem: etree.Element) -> str:
        """获取VLAN接口名称"""
        name_elem = vlan_elem.find("name")
        if name_elem is not None and name_elem.text:
            return name_elem.text
        return "unknown"
    
    def _save_fixed_xml(self, tree: etree.ElementTree, xml_file_path: str):
        """保存修复后的XML文件"""
        try:
            # 格式化XML
            etree.indent(tree, space="  ")
            
            # 保存文件
            tree.write(xml_file_path, encoding='utf-8', xml_declaration=True, pretty_print=True)
            log(f"💾 保存修复后的XML文件: {xml_file_path}", "info")
            
        except Exception as e:
            log(f"❌ 保存XML文件失败: {str(e)}", "error")
            raise
    
    def _print_fix_report(self):
        """输出修复报告"""
        log("📋 XML后处理修复报告:", "info")
        if self.fixes_applied:
            for fix in self.fixes_applied:
                log(f"  ✅ {fix}", "info")
        else:
            log("  ℹ️ 未发现需要修复的问题", "info")

    def fix_yang_validation_issues(self, root: etree.Element) -> etree.Element:
        """
        修复YANG模型验证问题

        Args:
            root: XML根元素

        Returns:
            etree.Element: 修复后的XML根元素
        """
        log("🔧 开始修复YANG模型验证问题", "info")

        # 重置统计信息
        self._reset_fix_stats()

        # 1. 修复缺失的name键
        self._fix_missing_name_keys_yang(root)

        # 1.5. 特殊处理time-range元素
        self._fix_time_range_elements_yang(root)

        # 2. 清理所有名称字段
        self._clean_all_name_fields_yang(root)

        # 3. 清理所有描述字段
        self._clean_all_description_fields_yang(root)

        # 4. 更新引用关系（如果有名称映射管理器）
        if self.name_mapping_manager:
            self.name_mapping_manager.update_all_references(root)

        # 记录修复统计信息
        self._log_fix_statistics()

        log("✅ YANG模型验证问题修复完成", "info")
        return root

    def _fix_time_range_elements_yang(self, xml_root: etree.Element):
        """
        特殊处理time-range元素的YANG验证问题

        根据NTOS YANG模型：
        1. 全局time-range容器需要name子元素
        2. security-policy中的time-range引用不应该有name子元素
        3. NAT规则中的time-range引用也不应该有name子元素

        Args:
            xml_root: XML根元素
        """
        try:
            # 查找所有time-range元素
            time_range_elements = xml_root.xpath(".//time-range")

            for time_range_elem in time_range_elements:
                # 检查是否是security-policy中的time-range引用
                if self._is_security_policy_time_range_reference(time_range_elem):
                    # security-policy中的time-range引用，移除错误的name子元素
                    name_elem = time_range_elem.find('name')
                    if name_elem is not None:
                        time_range_elem.remove(name_elem)
                        log(f"🔧 移除security-policy中time-range的错误name子元素", "info")
                        self.fixes_applied.append("移除security-policy time-range的错误name子元素")

                # 检查是否是NAT规则中的time-range引用
                elif self._is_nat_time_range_reference(time_range_elem):
                    # NAT规则中的time-range引用，移除错误的name子元素
                    name_elem = time_range_elem.find('name')
                    if name_elem is not None:
                        time_range_elem.remove(name_elem)
                        log(f"🔧 移除NAT规则中time-range的错误name子元素", "info")
                        self.fixes_applied.append("移除NAT time-range的错误name子元素")

                # 全局time-range容器需要name子元素（但这种情况很少见）
                else:
                    name_elem = time_range_elem.find('name')
                    if name_elem is None:
                        # 全局time-range容器缺少name键，添加
                        inferred_name = "global_time_range"
                        clean_name = self._simple_clean_name(inferred_name)

                        name_elem = etree.Element('name')
                        name_elem.text = clean_name
                        time_range_elem.insert(0, name_elem)

                        log(f"🔧 为全局time-range容器添加name键: {clean_name}", "info")
                        self.fixes_applied.append(f"添加全局time-range name键: {clean_name}")

        except Exception as e:
            log(f"修复time-range元素失败: {str(e)}", "warning")

    def _is_security_policy_time_range_reference(self, time_range_elem: etree.Element) -> bool:
        """
        检查time-range元素是否是security-policy中的引用

        Args:
            time_range_elem: time-range元素

        Returns:
            bool: 如果是security-policy中的引用返回True
        """
        try:
            # 检查父节点路径
            current = time_range_elem
            while current is not None:
                parent = current.getparent()
                if parent is None:
                    break

                # 检查是否在security-policy的policy元素中
                if (parent.tag == "policy" and
                    parent.getparent() is not None and
                    "security-policy" in str(parent.getparent().tag)):
                    return True

                current = parent

            return False

        except Exception:
            return False

    def _is_nat_time_range_reference(self, time_range_elem: etree.Element) -> bool:
        """
        检查time-range元素是否是NAT规则中的引用

        Args:
            time_range_elem: time-range元素

        Returns:
            bool: 如果是NAT规则中的引用返回True
        """
        try:
            # 检查命名空间
            if "urn:ruijie:ntos:params:xml:ns:yang:nat" in str(time_range_elem.tag):
                return True

            # 检查父节点路径
            current = time_range_elem
            while current is not None:
                parent = current.getparent()
                if parent is None:
                    break

                # 检查是否在NAT规则中
                if ("nat" in str(parent.tag).lower() or
                    "static-" in str(parent.tag) or
                    "dynamic-" in str(parent.tag)):
                    return True

                current = parent

            return False

        except Exception:
            return False

    def _reset_fix_stats(self):
        """重置修复统计信息"""
        self.fix_stats = {
            'missing_name_keys_fixed': 0,
            'invalid_names_cleaned': 0,
            'invalid_descriptions_cleaned': 0,
            'total_elements_processed': 0
        }

    def _fix_missing_name_keys_yang(self, xml_root: etree.Element):
        """
        修复缺失的name键（YANG版本）

        Args:
            xml_root: XML根元素
        """
        for element_tag, obj_type in self.list_elements_requiring_name.items():
            # 查找所有该类型的元素
            elements = xml_root.xpath(f".//{element_tag}")

            for element in elements:
                self.fix_stats['total_elements_processed'] += 1

                # 检查是否有name子元素
                name_elem = element.find('name')
                if name_elem is None:
                    # 缺少name键，需要修复
                    self._add_missing_name_key_yang(element, element_tag, obj_type)
                    self.fix_stats['missing_name_keys_fixed'] += 1
                elif not name_elem.text or not name_elem.text.strip():
                    # name键存在但为空，需要修复
                    self._fix_empty_name_key_yang(element, name_elem, element_tag, obj_type)
                    self.fix_stats['missing_name_keys_fixed'] += 1

    def _add_missing_name_key_yang(self, element: etree.Element, element_tag: str, obj_type: str):
        """为缺少name键的元素添加name键（YANG版本）"""
        # 尝试从其他属性推断名称
        inferred_name = self._infer_name_from_element_yang(element, element_tag)

        if not inferred_name:
            # 无法推断，生成默认名称
            inferred_name = f"unnamed_{element_tag}_{id(element) % 10000}"

        # 清理推断的名称
        if self.name_mapping_manager:
            clean_name = self.name_mapping_manager.register_name_mapping(
                obj_type,
                inferred_name,
                context=f"修复缺失name键-{element_tag}"
            )
        else:
            # 如果没有名称映射管理器，使用简单清理
            clean_name = self._simple_clean_name(inferred_name)

        # 添加name元素（插入到第一个位置）
        name_elem = etree.Element('name')
        name_elem.text = clean_name
        element.insert(0, name_elem)

        log(f"🔧 为{element_tag}元素添加缺失的name键: {clean_name}", "info")
        self.fixes_applied.append(f"添加缺失name键: {element_tag} -> {clean_name}")

    def _fix_empty_name_key_yang(self, element: etree.Element, name_elem: etree.Element,
                                element_tag: str, obj_type: str):
        """修复空的name键（YANG版本）"""
        # 尝试从其他属性推断名称
        inferred_name = self._infer_name_from_element_yang(element, element_tag)

        if not inferred_name:
            # 无法推断，生成默认名称
            inferred_name = f"unnamed_{element_tag}_{id(element) % 10000}"

        # 清理推断的名称
        if self.name_mapping_manager:
            clean_name = self.name_mapping_manager.register_name_mapping(
                obj_type,
                inferred_name,
                context=f"修复空name键-{element_tag}"
            )
        else:
            # 如果没有名称映射管理器，使用简单清理
            clean_name = self._simple_clean_name(inferred_name)

        # 更新name元素的文本
        name_elem.text = clean_name

        log(f"🔧 修复{element_tag}元素的空name键: {clean_name}", "info")
        self.fixes_applied.append(f"修复空name键: {element_tag} -> {clean_name}")

    def _infer_name_from_element_yang(self, element: etree.Element, element_tag: str) -> Optional[str]:
        """从元素的其他属性推断名称（YANG版本）"""
        # 尝试从常见的属性中推断名称
        potential_name_fields = ['id', 'identifier', 'desc', 'description', 'value']

        for field in potential_name_fields:
            field_elem = element.find(field)
            if field_elem is not None and field_elem.text and field_elem.text.strip():
                # 从字段值推断名称
                inferred = field_elem.text.strip()
                # 如果是描述字段，截取前面部分作为名称
                if field in ['desc', 'description']:
                    inferred = inferred.split()[0] if inferred.split() else inferred
                    inferred = inferred[:32]  # 限制长度
                return inferred

        # 尝试从元素的位置推断名称
        parent = element.getparent()
        if parent is not None:
            siblings = [e for e in parent if e.tag == element_tag]
            index = siblings.index(element) if element in siblings else 0
            return f"{element_tag}_{index + 1}"

        return None

    def _clean_all_name_fields_yang(self, xml_root: etree.Element):
        """清理所有名称字段（YANG版本）"""
        # 查找所有name元素
        name_elements = xml_root.xpath('.//name')

        for name_elem in name_elements:
            if name_elem.text:
                original_name = name_elem.text
                clean_name = self._simple_clean_name(original_name)

                if original_name != clean_name:
                    name_elem.text = clean_name
                    self.fix_stats['invalid_names_cleaned'] += 1
                    log(f"🔧 清理名称字段: {original_name} -> {clean_name}", "info")
                    self.fixes_applied.append(f"清理名称: {original_name} -> {clean_name}")

    def _clean_all_description_fields_yang(self, xml_root: etree.Element):
        """清理所有描述字段（YANG版本）"""
        # 查找所有desc和description元素
        desc_elements = xml_root.xpath('.//desc | .//description')

        for desc_elem in desc_elements:
            if desc_elem.text:
                original_desc = desc_elem.text
                clean_desc = self._simple_clean_description(original_desc)

                if original_desc != clean_desc:
                    desc_elem.text = clean_desc
                    self.fix_stats['invalid_descriptions_cleaned'] += 1
                    log(f"🔧 清理描述字段: {original_desc[:50]}... -> {clean_desc[:50]}...", "info")
                    self.fixes_applied.append(f"清理描述: {original_desc[:30]}...")

    def _simple_clean_name(self, name: str) -> str:
        """简单的名称清理（不依赖外部模块）"""
        import re
        if not name:
            return "unnamed"

        # 检查是否是接口名称格式（如Ge0/1.1001, TenGe0/1等）
        if re.match(r'^(Ge|TenGe|FastEthernet|GigabitEthernet)\d+/\d+(\.\d+)?$', str(name)):
            # 这是接口名称，保持斜杠不变，只清理其他非法字符
            cleaned = re.sub(r'[`~!@#$%^&*+|{};:"\'\\<>?,]', '_', str(name))
        elif re.match(r'^\d+\.\d+\.\d+\.\d+$', str(name)):
            # 这是纯IP地址格式，保持点号不变（YANG模型允许点号）
            cleaned = str(name)
        elif re.match(r'^\d+\.\d+\.\d+\.\d+/\d+$', str(name)):
            # 这是CIDR格式，只替换斜杠为下划线，保留点号
            cleaned = str(name).replace('/', '_')
        else:
            # 移除YANG模型不允许的字符（包括斜杠）
            cleaned = re.sub(r'[`~!@#$%^&*+|{};:"\'\\/<>?,]', '_', str(name))

        # 替换连续的下划线为单个下划线
        cleaned = re.sub(r'_+', '_', cleaned)

        # 移除开头和结尾的下划线
        cleaned = cleaned.strip('_')

        # 如果清理后为空，使用默认名称
        if not cleaned.strip():
            cleaned = "unnamed_object"

        # 限制长度
        if len(cleaned) > 64:
            cleaned = cleaned[:64].rstrip('_')

        return cleaned

    def _simple_clean_description(self, description: str) -> str:
        """简单的描述清理（不依赖外部模块）"""
        import re
        if not description:
            return ""

        # 移除YANG模型不允许的字符，但保留更多可读字符
        cleaned = re.sub(r'[`~!#$%^&*+|{};:"\'\\/<>?]', ' ', str(description))

        # 清理多余的空格
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()

        # 限制长度
        if len(cleaned) > 255:
            cleaned = cleaned[:255].rstrip()

        return cleaned

    def _log_fix_statistics(self):
        """记录修复统计信息"""
        log("📊 YANG验证修复统计:", "info")
        log(f"  处理元素总数: {self.fix_stats['total_elements_processed']}", "info")
        log(f"  修复缺失name键: {self.fix_stats['missing_name_keys_fixed']}", "info")
        log(f"  清理无效名称: {self.fix_stats['invalid_names_cleaned']}", "info")
        log(f"  清理无效描述: {self.fix_stats['invalid_descriptions_cleaned']}", "info")


def main():
    """主函数，用于命令行调用"""
    if len(sys.argv) != 2:
        print("用法: python xml_post_processor.py <xml_file_path>")
        sys.exit(1)
    
    xml_file_path = sys.argv[1]
    if not os.path.exists(xml_file_path):
        print(f"错误: 文件不存在 {xml_file_path}")
        sys.exit(1)
    
    processor = XmlPostProcessor()
    success = processor.process_xml_file(xml_file_path)
    
    if success:
        print("✅ XML后处理修复成功")
        sys.exit(0)
    else:
        print("❌ XML后处理修复失败")
        sys.exit(1)


if __name__ == "__main__":
    main()
