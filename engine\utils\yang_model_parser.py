#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import re
import json
from typing import Dict, List, Set, Optional, Tuple, Any
from dataclasses import dataclass, field
from engine.utils.logger import log
from engine.utils.i18n import _

@dataclass
class YangNamespace:
    """YANG命名空间信息"""
    uri: str
    prefix: str
    module_name: str

@dataclass
class YangNode:
    """YANG节点信息"""
    name: str
    type: str  # container, leaf, leaf-list, list, choice, case
    namespace: str
    path: str
    parent: Optional[str] = None
    children: Set[str] = field(default_factory=set)
    attributes: Dict[str, Any] = field(default_factory=dict)
    constraints: Dict[str, Any] = field(default_factory=dict)

@dataclass
class YangSchema:
    """YANG模型架构"""
    namespaces: Dict[str, YangNamespace] = field(default_factory=dict)
    nodes: Dict[str, YangNode] = field(default_factory=dict)
    root_containers: Set[str] = field(default_factory=set)
    typedef_patterns: Dict[str, str] = field(default_factory=dict)

class YangModelParser:
    """YANG模型解析器，用于解析YANG模型文件并构建架构映射"""
    
    def __init__(self, model: str, version: str):
        """
        初始化YANG模型解析器
        
        Args:
            model (str): 设备型号，如 'z5100s'
            version (str): 设备版本，如 'R10P2'
        """
        self.model = model
        self.version = version
        self.schema = YangSchema()
        self._yang_model_dir = self._get_yang_model_dir()
        
        log(_("yang_parser.init", model=model, version=version))
    
    def _get_yang_model_dir(self) -> str:
        """获取YANG模型目录路径"""
        engine_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        yang_model_dir = os.path.join(engine_dir, "data", "input", "yang_models", self.model, self.version)
        
        if not os.path.exists(yang_model_dir):
            raise FileNotFoundError(_("error.yang_model_dir_not_exists", dir=yang_model_dir))
        
        return yang_model_dir
    
    def parse_all_models(self) -> YangSchema:
        """
        解析所有YANG模型文件
        
        Returns:
            YangSchema: 解析后的YANG模型架构
        """
        log(_("yang_parser.start_parsing"))
        
        # 获取所有YANG文件
        yang_files = [f for f in os.listdir(self._yang_model_dir) if f.endswith('.yang')]
        
        # 首先解析命名空间和typedef
        for yang_file in yang_files:
            file_path = os.path.join(self._yang_model_dir, yang_file)
            self._parse_module_header(file_path)
        
        # 然后解析节点结构
        for yang_file in yang_files:
            file_path = os.path.join(self._yang_model_dir, yang_file)
            self._parse_module_structure(file_path)
        
        log(_("yang_parser.parsing_complete", 
             namespaces=len(self.schema.namespaces),
             nodes=len(self.schema.nodes),
             typedefs=len(self.schema.typedef_patterns)))
        
        return self.schema
    
    def _parse_module_header(self, file_path: str):
        """解析模块头部信息，提取命名空间和typedef"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 提取模块名
            module_match = re.search(r'module\s+([^\s{]+)', content)
            if not module_match:
                return
            
            module_name = module_match.group(1)
            
            # 提取命名空间
            namespace_match = re.search(r'namespace\s+"([^"]+)"', content)
            if namespace_match:
                namespace_uri = namespace_match.group(1)
                
                # 提取前缀
                prefix_match = re.search(r'prefix\s+([^\s;]+)', content)
                prefix = prefix_match.group(1) if prefix_match else module_name
                
                self.schema.namespaces[module_name] = YangNamespace(
                    uri=namespace_uri,
                    prefix=prefix,
                    module_name=module_name
                )
            
            # 提取typedef定义
            typedef_pattern = r'typedef\s+([^\s{]+)\s*{[^}]*type\s+string\s*{[^}]*pattern\s+[\'"]([^\'"]*)[\'"]\s*{[^}]*error-message[^}]*}[^}]*}[^}]*}'
            for match in re.finditer(typedef_pattern, content, re.DOTALL):
                typedef_name = match.group(1)
                pattern = match.group(2)
                self.schema.typedef_patterns[typedef_name] = pattern
                
        except Exception as e:
            log(_("yang_parser.parse_header_error", file=file_path, error=str(e)), "warning")
    
    def _parse_module_structure(self, file_path: str):
        """解析模块结构，提取节点信息"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 获取模块名和命名空间
            module_match = re.search(r'module\s+([^\s{]+)', content)
            if not module_match:
                return
                
            module_name = module_match.group(1)
            namespace = self.schema.namespaces.get(module_name)
            if not namespace:
                return
            
            # 解析容器和叶子节点
            self._parse_nodes(content, namespace.uri, "")
            
        except Exception as e:
            log(_("yang_parser.parse_structure_error", file=file_path, error=str(e)), "warning")
    
    def _parse_nodes(self, content: str, namespace: str, parent_path: str):
        """递归解析节点"""
        # 提取容器节点
        container_pattern = r'container\s+([^\s{]+)\s*{'
        for match in re.finditer(container_pattern, content):
            container_name = match.group(1)
            node_path = f"{parent_path}/{container_name}" if parent_path else container_name
            
            self.schema.nodes[node_path] = YangNode(
                name=container_name,
                type="container",
                namespace=namespace,
                path=node_path,
                parent=parent_path or None
            )
            
            if not parent_path:
                self.schema.root_containers.add(container_name)
        
        # 提取叶子节点
        leaf_pattern = r'leaf\s+([^\s{]+)\s*{'
        for match in re.finditer(leaf_pattern, content):
            leaf_name = match.group(1)
            node_path = f"{parent_path}/{leaf_name}" if parent_path else leaf_name
            
            self.schema.nodes[node_path] = YangNode(
                name=leaf_name,
                type="leaf",
                namespace=namespace,
                path=node_path,
                parent=parent_path or None
            )
    
    def get_namespace_for_node(self, node_path: str) -> Optional[str]:
        """获取节点的命名空间"""
        node = self.schema.nodes.get(node_path)
        return node.namespace if node else None
    
    def validate_node_constraints(self, node_path: str, value: str) -> Tuple[bool, str]:
        """验证节点值是否符合约束"""
        node = self.schema.nodes.get(node_path)
        if not node:
            return True, ""
        
        # 检查typedef约束
        for typedef_name, pattern in self.schema.typedef_patterns.items():
            if typedef_name in node.attributes.get('type', ''):
                if not re.match(pattern, value):
                    return False, _("yang_parser.constraint_violation", 
                                  node=node_path, pattern=pattern, value=value)
        
        return True, ""
    
    def get_required_namespaces(self) -> Dict[str, str]:
        """获取所有必需的命名空间映射"""
        return {ns.prefix: ns.uri for ns in self.schema.namespaces.values()}
    
    def is_valid_node_path(self, path: str) -> bool:
        """检查节点路径是否有效"""
        return path in self.schema.nodes
    
    def get_node_type(self, path: str) -> Optional[str]:
        """获取节点类型"""
        node = self.schema.nodes.get(path)
        return node.type if node else None
    
    def export_schema_json(self, output_path: str):
        """导出架构为JSON格式，用于调试"""
        schema_dict = {
            "namespaces": {
                name: {
                    "uri": ns.uri,
                    "prefix": ns.prefix,
                    "module_name": ns.module_name
                } for name, ns in self.schema.namespaces.items()
            },
            "nodes": {
                path: {
                    "name": node.name,
                    "type": node.type,
                    "namespace": node.namespace,
                    "parent": node.parent,
                    "children": list(node.children),
                    "attributes": node.attributes,
                    "constraints": node.constraints
                } for path, node in self.schema.nodes.items()
            },
            "root_containers": list(self.schema.root_containers),
            "typedef_patterns": self.schema.typedef_patterns
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(schema_dict, f, indent=2, ensure_ascii=False)
        
        log(_("yang_parser.schema_exported", path=output_path))


class YangValidationContext:
    """YANG验证上下文，用于在XML生成过程中进行实时验证"""
    
    def __init__(self, yang_parser: YangModelParser):
        """
        初始化验证上下文
        
        Args:
            yang_parser (YangModelParser): YANG模型解析器
        """
        self.parser = yang_parser
        self.schema = yang_parser.schema
        self.validation_errors: List[str] = []
        self.validation_warnings: List[str] = []
    
    def validate_element_creation(self, parent_path: str, element_name: str, 
                                namespace: str = None) -> bool:
        """
        验证元素创建是否符合YANG模型
        
        Args:
            parent_path (str): 父节点路径
            element_name (str): 元素名称
            namespace (str): 命名空间（可选）
            
        Returns:
            bool: 验证是否通过
        """
        element_path = f"{parent_path}/{element_name}" if parent_path else element_name
        
        # 检查节点是否在YANG模型中定义
        if not self.parser.is_valid_node_path(element_path):
            error_msg = _("yang_validator.undefined_node", path=element_path)
            self.validation_errors.append(error_msg)
            log(error_msg, "warning")
            return False
        
        # 检查命名空间是否正确
        expected_namespace = self.parser.get_namespace_for_node(element_path)
        if namespace and expected_namespace and namespace != expected_namespace:
            warning_msg = _("yang_validator.namespace_mismatch", 
                          path=element_path, expected=expected_namespace, actual=namespace)
            self.validation_warnings.append(warning_msg)
            log(warning_msg, "warning")
        
        return True
    
    def validate_element_value(self, element_path: str, value: str) -> bool:
        """
        验证元素值是否符合约束
        
        Args:
            element_path (str): 元素路径
            value (str): 元素值
            
        Returns:
            bool: 验证是否通过
        """
        is_valid, error_msg = self.parser.validate_node_constraints(element_path, value)
        if not is_valid:
            self.validation_errors.append(error_msg)
            log(error_msg, "error")
        
        return is_valid
    
    def get_validation_summary(self) -> Dict[str, Any]:
        """获取验证摘要"""
        return {
            "errors": self.validation_errors,
            "warnings": self.validation_warnings,
            "error_count": len(self.validation_errors),
            "warning_count": len(self.validation_warnings),
            "is_valid": len(self.validation_errors) == 0
        }
    
    def clear_validation_results(self):
        """清除验证结果"""
        self.validation_errors.clear()
        self.validation_warnings.clear()
