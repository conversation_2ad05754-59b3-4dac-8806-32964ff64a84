#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import subprocess
import glob
import re
from engine.utils.logger import log
from engine.utils.i18n import _  # 添加对国际化函数的导入

def validate_with_yanglint(xml_file, yang_model_dir=None, model=None, version=None):
    """
    使用yanglint工具验证XML是否符合YANG模型规范
    
    Args:
        xml_file (str): 要验证的XML文件路径
        yang_model_dir (str, optional): YANG模型目录路径
        model (str, optional): 设备型号，如'z5100s'
        version (str, optional): 设备版本，如'R10P2'
    
    Returns:
        bool: 验证是否通过
        str: 验证结果消息
    """
    try:
        # 获取引擎目录
        engine_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        
        # 如果未指定YANG模型目录，使用对应型号和版本的YANG模型目录
        if yang_model_dir is None:
            # 只使用对应型号和版本的YANG模型
            yang_model_dir = os.path.join(engine_dir, "data", "input", "yang_models", model, version)
        
        log(_("validator.validate_xml", file=xml_file))
        log(_("validator.yang_model_dir", dir=yang_model_dir))
        
        # 检查YANG模型目录是否存在
        if not os.path.exists(yang_model_dir):
            log(_("error.yang_model_dir_not_exists", dir=yang_model_dir), "error")
            return False, _("error.yang_model_dir_not_exists", dir=yang_model_dir)
        
        # 检查是否存在YANG模型文件
        yang_files = [f for f in os.listdir(yang_model_dir) if f.endswith(('.yang', '.yin'))]
        if not yang_files:
            log(_("error.no_yang_files_found"), "error")
            return False, _("error.no_yang_model_files")
        
        log(_("validator.found_yang_files", dir=yang_model_dir, count=len(yang_files)))
        
        # 使用shell命令的方式验证XML
        shell_command = f"yanglint -p {yang_model_dir} -i {os.path.join(yang_model_dir, 'ntos*.yang')} {xml_file}"
        log(_("validator.execute_yanglint_command", cmd=shell_command))
        
        try:
            # 在Linux/Unix系统上使用shell=True可以保持通配符不被展开
            if os.name != 'nt':  # 非Windows系统
                result = subprocess.run(shell_command, shell=True, capture_output=True, text=True, check=True)
                log(_("validator.validation_passed"))
                return True, _("validator.validation_passed")
            else:
                # Windows系统不支持通配符，需要列出所有文件
                # 获取所有ntos*.yang文件
                ntos_files = glob.glob(os.path.join(yang_model_dir, "ntos*.yang"))
                if not ntos_files:
                    log(_("warning.no_ntos_yang_files"), "warning")
                
                # 构建Windows兼容的命令
                cmd_parts = ["yanglint", "-p", yang_model_dir]
                for ntos_file in ntos_files:
                    cmd_parts.extend(["-i", ntos_file])
                cmd_parts.append(xml_file)
                
                cmd_str = " ".join(cmd_parts)
                log(_("validator.execute_windows_command", cmd=cmd_str))
                
                result = subprocess.run(cmd_str, shell=True, capture_output=True, text=True, check=True)
                log(_("validator.validation_passed"))
                return True, _("validator.validation_passed")
        except subprocess.CalledProcessError as e:
            error_message = e.stderr if e.stderr else e.stdout
            error_msg = _("error.yanglint_validation_failed", message=error_message)
            log(error_msg, "error")
            return False, _("error.validation_failed", message=error_message)
        except FileNotFoundError:
            log(_("error.yanglint_not_found"), "error")
            return False, _("error.yanglint_not_available")
    except Exception as e:
        error_msg = _("error.yanglint_execution_error", error=str(e))
        log(error_msg, "error")
        return False, _("error.validation_process_error", error=str(e))

def check_yanglint_available():
    """
    检查yanglint工具是否可用 - 改进版本，解决环境变量和路径问题

    Returns:
        bool: yanglint是否可用
    """
    # 尝试多种检测方法
    detection_methods = [
        _check_yanglint_direct_utils,
        _check_yanglint_with_path_utils,
        _check_yanglint_absolute_path_utils,
        _check_yanglint_with_env_utils
    ]

    for method in detection_methods:
        try:
            if method():
                return True
        except Exception as e:
            log(_("validator.yanglint_check_method_failed", method=method.__name__, error=str(e)), "debug")
            continue

    # 所有方法都失败了
    log(_("warning.yanglint_not_available_using_builtin"), "warning")
    return False

def _check_yanglint_direct_utils():
    """直接检查yanglint命令 - utils版本"""
    try:
        result = subprocess.run(["yanglint", "--version"],
                               capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            log(_("validator.yanglint_available", version=result.stdout.strip()), "debug")
            return True
        else:
            return False
    except (FileNotFoundError, subprocess.TimeoutExpired):
        return False

def _check_yanglint_with_path_utils():
    """使用shutil.which查找yanglint路径 - utils版本"""
    import shutil
    yanglint_path = shutil.which('yanglint')
    if not yanglint_path:
        return False

    try:
        result = subprocess.run([yanglint_path, "--version"],
                               capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            log(_("validator.yanglint_available", version=result.stdout.strip()), "debug")
            return True
        else:
            return False
    except (FileNotFoundError, subprocess.TimeoutExpired):
        return False

def _check_yanglint_absolute_path_utils():
    """检查常见的yanglint安装路径 - utils版本"""
    import os
    common_paths = [
        '/usr/local/bin/yanglint',
        '/usr/bin/yanglint',
        '/opt/venv/bin/yanglint',
        '/usr/sbin/yanglint',
        '/usr/local/sbin/yanglint'
    ]

    for path in common_paths:
        if os.path.exists(path) and os.access(path, os.X_OK):
            try:
                result = subprocess.run([path, "--version"],
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    log(_("validator.yanglint_available", version=result.stdout.strip()), "debug")
                    return True
            except subprocess.TimeoutExpired:
                continue
    return False

def _check_yanglint_with_env_utils():
    """使用完整环境变量检查yanglint - utils版本"""
    import os
    try:
        # 构建完整的环境变量
        env = os.environ.copy()

        # 确保PATH包含常见的bin目录
        path_additions = ['/usr/local/bin', '/usr/bin', '/opt/venv/bin']
        current_path = env.get('PATH', '')

        for path_add in path_additions:
            if path_add not in current_path:
                current_path = f"{path_add}:{current_path}"

        env['PATH'] = current_path

        # 设置LD_LIBRARY_PATH
        ld_path_additions = ['/usr/local/lib', '/usr/lib']
        current_ld_path = env.get('LD_LIBRARY_PATH', '')

        for ld_add in ld_path_additions:
            if ld_add not in current_ld_path:
                current_ld_path = f"{ld_add}:{current_ld_path}" if current_ld_path else ld_add

        env['LD_LIBRARY_PATH'] = current_ld_path

        result = subprocess.run(["yanglint", "--version"],
                               capture_output=True, text=True, timeout=10, env=env)
        if result.returncode == 0:
            log(_("validator.yanglint_available", version=result.stdout.strip()), "debug")
            return True
        else:
            return False
    except (FileNotFoundError, subprocess.TimeoutExpired):
        return False


def validate_xml_builtin_constraints(xml_file: str, model: str = None, version: str = None) -> tuple:
    """
    内置XML约束验证，检查关键YANG模型约束

    Args:
        xml_file: XML文件路径
        model: 设备型号
        version: 设备版本

    Returns:
        tuple: (验证是否通过, 验证消息)
    """
    try:
        from lxml import etree
        import re

        # 解析XML文件
        try:
            tree = etree.parse(xml_file)
            root = tree.getroot()
        except Exception as e:
            return False, f"XML解析失败: {str(e)}"

        validation_errors = []
        validation_warnings = []

        # 1. 检查地址对象名称约束 (ntos-obj-name-type)
        address_objects = root.xpath("//address-obj/address-set")
        for addr_obj in address_objects:
            name_elem = addr_obj.find("name")
            if name_elem is not None and name_elem.text:
                name = name_elem.text
                # YANG约束: 不能包含 `~!#$%^&*+|{};:"',\/<>?
                forbidden_chars = r'[`~!#$%^&*+|{};:"\'\\/<>?]'
                if re.search(forbidden_chars, name):
                    validation_errors.append(f"地址对象名称 '{name}' 包含YANG模型禁止的字符")

        # 2. 检查服务对象名称约束
        service_objects = root.xpath("//service-obj/service-set")
        for svc_obj in service_objects:
            name_elem = svc_obj.find("name")
            if name_elem is not None and name_elem.text:
                name = name_elem.text
                forbidden_chars = r'[`~!#$%^&*+|{};:"\'\\/<>?]'
                if re.search(forbidden_chars, name):
                    validation_errors.append(f"服务对象名称 '{name}' 包含YANG模型禁止的字符")

        # 3. 检查安全策略名称约束
        security_policies = root.xpath("//security-policy/policy")
        for policy in security_policies:
            name_elem = policy.find("name")
            if name_elem is not None and name_elem.text:
                name = name_elem.text
                forbidden_chars = r'[`~!#$%^&*+|{};:"\'\\/<>?]'
                if re.search(forbidden_chars, name):
                    validation_errors.append(f"安全策略名称 '{name}' 包含YANG模型禁止的字符")

        # 4. 检查安全区域优先级唯一性约束
        zones = root.xpath("//security-zone/zone")
        priorities = []
        for zone in zones:
            priority_elem = zone.find("priority")
            if priority_elem is not None and priority_elem.text:
                priority = priority_elem.text
                if priority in priorities:
                    validation_errors.append(f"安全区域优先级 '{priority}' 重复，违反YANG模型唯一性约束")
                else:
                    priorities.append(priority)

        # 5. 检查XML结构完整性
        vrf_nodes = root.xpath("//vrf")
        if not vrf_nodes:
            validation_warnings.append("未找到VRF节点，可能影响配置加载")

        # 6. 检查命名空间声明
        if root.nsmap is None or not root.nsmap:
            validation_warnings.append("缺少XML命名空间声明")

        # 7. 检查必需元素
        config_elements = ["interface", "security-policy", "address-obj", "service-obj"]
        for element in config_elements:
            if not root.xpath(f"//{element}"):
                validation_warnings.append(f"未找到 {element} 配置元素")

        # 生成验证结果
        if validation_errors:
            error_msg = f"发现 {len(validation_errors)} 个YANG约束违规: " + "; ".join(validation_errors[:3])
            if len(validation_errors) > 3:
                error_msg += f" (还有 {len(validation_errors) - 3} 个错误)"
            return False, error_msg

        if validation_warnings:
            warning_msg = f"发现 {len(validation_warnings)} 个警告: " + "; ".join(validation_warnings[:2])
            if len(validation_warnings) > 2:
                warning_msg += f" (还有 {len(validation_warnings) - 2} 个警告)"
            return True, f"验证通过但有警告: {warning_msg}"

        return True, "内置YANG约束验证通过"

    except Exception as e:
        return False, f"内置验证过程出错: {str(e)}"
        
def validate_gracefully(xml_file, yang_model_dir=None, model=None, version=None):
    """
    验证XML是否符合YANG模型规范

    Args:
        xml_file (str): 要验证的XML文件路径
        yang_model_dir (str, optional): YANG模型目录路径
        model (str, optional): 设备型号，如'z5100s'
        version (str, optional): 设备版本，如'R10P2'

    Returns:
        bool: 验证是否通过
        str: 验证结果消息
    """
    # 首先尝试内置验证
    builtin_result, builtin_message = validate_xml_builtin_constraints(xml_file, model, version)

    # 检查yanglint是否可用
    if not check_yanglint_available():
        log(_("warning.yanglint_not_available_using_builtin"), "warning")
        # 如果yanglint不可用，使用内置验证结果
        return builtin_result, f"内置验证: {builtin_message}"
    
    # 检查必要参数
    if model is None or version is None:
        error_msg = _("error.model_version_not_provided")
        log(error_msg, "error")
        return False, error_msg
    
    # 获取引擎目录
    engine_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    
    # 如果未指定YANG模型目录，使用对应型号和版本的YANG模型目录
    if yang_model_dir is None:
        # 尝试三种可能的路径
        possible_paths = [
            os.path.join(engine_dir, "data", "input", "yang_models", model, version),
            os.path.join(engine_dir, "data", "input", "yang_models"),
            os.path.join(os.getcwd(), "data", "input", "yang_models")
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                yang_model_dir = path
                log(_("validator.use_yang_model_dir", dir=yang_model_dir))
                break
    
    # 检查YANG模型目录是否存在
    if yang_model_dir is None or not os.path.exists(yang_model_dir):
        error_msg = _("error.yang_model_dir_not_exists_cannot_validate", dir=yang_model_dir)
        log(error_msg, "error")
        return False, error_msg
    
    # 检查是否存在YANG模型文件
    yang_files = [f for f in os.listdir(yang_model_dir) if f.endswith(('.yang', '.yin'))]
    if not yang_files:
        error_msg = _("error.no_yang_files_found_cannot_validate")
        log(error_msg, "error")
        return False, error_msg
    
    # 尝试执行验证
    try:
        result, message = validate_with_yanglint(xml_file, yang_model_dir, model, version)
        
        # 如果验证失败，提供详细的错误信息
        if not result:
            # 整理错误信息以提供更清晰的反馈
            error_lines = [line for line in message.split('\n') if line.strip()]
            error_count = len(error_lines)
            
            # 生成详细的错误报告
            detailed_message = _("validator.validation_failed_with_count", count=error_count)
            
            # 添加前10个错误的详细信息（如果超过10个）
            if error_count > 0:
                max_errors_to_show = min(10, error_count)
                detailed_message += "\n" + _("validator.main_errors") + "\n"
                for i in range(max_errors_to_show):
                    detailed_message += f"{i+1}. {error_lines[i]}\n"
                
                if error_count > max_errors_to_show:
                    detailed_message += "\n" + _("validator.more_errors_not_shown", count=error_count - max_errors_to_show) + "\n"
            
            log(detailed_message, "error")
            return False, detailed_message
        
        return result, message
    except Exception as e:
        error_msg = _("error.validation_exception", error=str(e))
        log(error_msg, "error")
        return False, error_msg 