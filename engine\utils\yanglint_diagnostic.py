#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
yanglint工具诊断脚本
用于诊断yanglint工具检测失败的原因
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_yanglint_availability():
    """全面检查yanglint工具的可用性"""
    print("=" * 60)
    print("yanglint工具诊断报告")
    print("=" * 60)
    
    # 1. 检查PATH环境变量
    print("\n1. 环境变量检查:")
    path_env = os.environ.get('PATH', '')
    print(f"   PATH: {path_env}")
    
    ld_library_path = os.environ.get('LD_LIBRARY_PATH', '')
    print(f"   LD_LIBRARY_PATH: {ld_library_path}")
    
    pythonpath = os.environ.get('PYTHONPATH', '')
    print(f"   PYTHONPATH: {pythonpath}")
    
    # 2. 检查工作目录
    print(f"\n2. 工作目录: {os.getcwd()}")
    
    # 3. 使用which/where命令查找yanglint
    print("\n3. yanglint位置检查:")
    try:
        if os.name == 'nt':  # Windows
            result = subprocess.run(['where', 'yanglint'], 
                                  capture_output=True, text=True, timeout=10)
        else:  # Unix/Linux
            result = subprocess.run(['which', 'yanglint'], 
                                  capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            yanglint_path = result.stdout.strip()
            print(f"   ✅ 找到yanglint: {yanglint_path}")
            
            # 检查文件权限
            if os.path.exists(yanglint_path):
                stat_info = os.stat(yanglint_path)
                print(f"   文件权限: {oct(stat_info.st_mode)}")
                print(f"   可执行: {os.access(yanglint_path, os.X_OK)}")
        else:
            print(f"   ❌ 未找到yanglint命令")
            print(f"   错误输出: {result.stderr}")
    except Exception as e:
        print(f"   ❌ 查找yanglint时出错: {e}")
    
    # 4. 使用shutil.which查找
    print("\n4. Python shutil.which检查:")
    yanglint_path = shutil.which('yanglint')
    if yanglint_path:
        print(f"   ✅ shutil.which找到: {yanglint_path}")
    else:
        print(f"   ❌ shutil.which未找到yanglint")
    
    # 5. 检查常见安装路径
    print("\n5. 常见路径检查:")
    common_paths = [
        '/usr/bin/yanglint',
        '/usr/local/bin/yanglint',
        '/opt/venv/bin/yanglint',
        '/usr/sbin/yanglint',
        '/usr/local/sbin/yanglint'
    ]
    
    for path in common_paths:
        if os.path.exists(path):
            print(f"   ✅ 找到: {path}")
            print(f"      可执行: {os.access(path, os.X_OK)}")
        else:
            print(f"   ❌ 不存在: {path}")
    
    # 6. 尝试直接执行yanglint --version
    print("\n6. 直接执行测试:")
    test_methods = [
        # 方法1: 直接使用命令名
        (['yanglint', '--version'], "使用命令名"),
        # 方法2: 使用绝对路径（如果找到的话）
        ([yanglint_path, '--version'] if yanglint_path else None, "使用绝对路径"),
        # 方法3: 使用shell=True
        ('yanglint --version', "使用shell=True"),
    ]
    
    for cmd, description in test_methods:
        if cmd is None:
            continue
            
        print(f"\n   测试方法: {description}")
        print(f"   命令: {cmd}")
        
        try:
            if isinstance(cmd, str):
                # shell=True方式
                result = subprocess.run(cmd, shell=True, capture_output=True, 
                                      text=True, timeout=10)
            else:
                # 列表方式
                result = subprocess.run(cmd, capture_output=True, text=True, 
                                      timeout=10)
            
            print(f"   返回码: {result.returncode}")
            if result.returncode == 0:
                print(f"   ✅ 成功!")
                print(f"   输出: {result.stdout.strip()}")
            else:
                print(f"   ❌ 失败")
                print(f"   标准输出: {result.stdout}")
                print(f"   错误输出: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            print(f"   ❌ 超时")
        except FileNotFoundError as e:
            print(f"   ❌ 文件未找到: {e}")
        except Exception as e:
            print(f"   ❌ 其他错误: {e}")
    
    # 7. 检查libyang相关库
    print("\n7. libyang库检查:")
    lib_paths = [
        '/usr/lib',
        '/usr/local/lib',
        '/lib',
        '/lib64',
        '/usr/lib64',
        '/usr/local/lib64'
    ]
    
    libyang_found = False
    for lib_path in lib_paths:
        if os.path.exists(lib_path):
            libyang_files = []
            try:
                for file in os.listdir(lib_path):
                    if 'libyang' in file.lower():
                        libyang_files.append(file)
                
                if libyang_files:
                    print(f"   ✅ {lib_path}: {', '.join(libyang_files)}")
                    libyang_found = True
            except PermissionError:
                print(f"   ⚠️ {lib_path}: 权限不足")
    
    if not libyang_found:
        print("   ❌ 未找到libyang相关库文件")
    
    # 8. 系统信息
    print(f"\n8. 系统信息:")
    print(f"   操作系统: {os.name}")
    print(f"   Python版本: {sys.version}")
    print(f"   当前用户: {os.environ.get('USER', os.environ.get('USERNAME', 'unknown'))}")
    
    # 9. 容器环境检查
    print(f"\n9. 容器环境检查:")
    container_indicators = [
        ('/.dockerenv', 'Docker容器'),
        ('/proc/1/cgroup', '容器cgroup'),
        ('/etc/hostname', '主机名检查')
    ]
    
    for indicator, description in container_indicators:
        if os.path.exists(indicator):
            print(f"   ✅ {description}: {indicator} 存在")
            if indicator == '/etc/hostname':
                try:
                    with open(indicator, 'r') as f:
                        hostname = f.read().strip()
                        print(f"      主机名: {hostname}")
                except:
                    pass
        else:
            print(f"   ❌ {description}: {indicator} 不存在")
    
    # 10. 模拟现有代码的检测方式
    print(f"\n10. 模拟现有代码检测:")

    # 模拟 yang_validator.py 中的检测方式
    print("   方式1 - yang_validator.py (check=True):")
    try:
        result = subprocess.run(['yanglint', '--version'],
                              capture_output=True, text=True, check=True)
        print(f"   ✅ 成功! 版本: {result.stdout.strip()}")
    except (subprocess.CalledProcessError, FileNotFoundError) as e:
        print(f"   ❌ 失败: {type(e).__name__}: {e}")
    except Exception as e:
        print(f"   ❌ 其他错误: {type(e).__name__}: {e}")

    # 模拟 utils/yang_validator.py 中的检测方式
    print("   方式2 - utils/yang_validator.py (check=False):")
    try:
        result = subprocess.run(["yanglint", "--version"],
                               capture_output=True, text=True)
        if result.returncode == 0:
            print(f"   ✅ 成功! 版本: {result.stdout.strip()}")
        else:
            print(f"   ❌ 返回码非0: {result.returncode}")
            print(f"   错误输出: {result.stderr}")
    except FileNotFoundError as e:
        print(f"   ❌ 文件未找到: {e}")
    except Exception as e:
        print(f"   ❌ 其他错误: {type(e).__name__}: {e}")

    # 11. 建议和解决方案
    print(f"\n11. 建议和解决方案:")

    if yanglint_path:
        print("   ✅ yanglint已安装，可能的问题:")
        print("   - 检查工作目录是否正确")
        print("   - 检查Python subprocess调用的环境变量")
        print("   - 检查是否有权限问题")
    else:
        print("   ❌ yanglint未找到，建议:")
        print("   - Ubuntu: sudo apt-get install libyang-tools")
        print("   - CentOS: sudo yum install libyang-tools")
        print("   - 手动编译: https://github.com/CESNET/libyang")
        print("   - 检查PATH环境变量是否包含yanglint安装路径")

    # 12. 生成修复脚本建议
    print(f"\n12. 修复建议:")
    print("   如果yanglint已安装但检测失败，可以尝试:")
    print("   1. 在subprocess.run中添加env参数指定完整PATH")
    print("   2. 使用绝对路径调用yanglint")
    print("   3. 检查工作目录设置")
    print("   4. 添加更详细的错误日志")

    print("\n" + "=" * 60)
    print("诊断完成")
    print("=" * 60)

def test_current_detection_methods():
    """测试当前代码中使用的检测方法"""
    print("\n" + "=" * 60)
    print("测试当前检测方法")
    print("=" * 60)

    # 导入当前的检测函数
    try:
        sys.path.append(os.path.dirname(os.path.dirname(__file__)))
        from infrastructure.yang.yang_validator import YangValidator
        from utils.yang_validator import check_yanglint_available

        print("\n1. 测试 YangValidator.is_yanglint_available():")
        validator = YangValidator()
        result1 = validator.is_yanglint_available()
        print(f"   结果: {result1}")

        print("\n2. 测试 check_yanglint_available():")
        result2 = check_yanglint_available()
        print(f"   结果: {result2}")

        if result1 != result2:
            print("   ⚠️ 两种方法结果不一致!")

    except ImportError as e:
        print(f"   ❌ 无法导入检测函数: {e}")
    except Exception as e:
        print(f"   ❌ 测试过程中出错: {e}")

if __name__ == "__main__":
    check_yanglint_availability()
    test_current_detection_methods()
