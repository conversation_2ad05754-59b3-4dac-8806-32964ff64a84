"""
增强的地址池验证器

提供全面的FortiGate IP池验证功能，包括：
- IP地址范围有效性验证
- 地址池容量计算和分析
- 命名规范检查
- NTOS YANG模型兼容性验证
"""

import ipaddress
import re
from typing import Dict, List, Tuple, Optional, Any
from engine.utils.logger import log
from engine.utils.i18n import _
from engine.utils.enhanced_logging import get_enhanced_logger, performance_monitor, error_handler
from engine.utils.performance_optimizer import performance_optimized, batch_process, get_smart_cache
from engine.utils.name_validator import clean_ntos_name, validate_ntos_name


class IPPoolValidationResult:
    """地址池验证结果"""
    
    def __init__(self):
        self.is_valid = True
        self.errors = []
        self.warnings = []
        self.info = {}
        self.capacity_info = {}
        self.optimization_suggestions = []
    
    def add_error(self, message: str):
        """添加错误信息"""
        self.is_valid = False
        self.errors.append(message)
    
    def add_warning(self, message: str):
        """添加警告信息"""
        self.warnings.append(message)
    
    def add_info(self, key: str, value: Any):
        """添加信息"""
        self.info[key] = value
    
    def set_capacity_info(self, total_ips: int, usable_ips: int, efficiency: float):
        """设置容量信息"""
        self.capacity_info = {
            "total_ips": total_ips,
            "usable_ips": usable_ips,
            "efficiency": efficiency
        }
    
    def add_optimization_suggestion(self, suggestion: str):
        """添加优化建议"""
        self.optimization_suggestions.append(suggestion)


class EnhancedIPPoolValidator:
    """增强的IP池验证器"""
    
    def __init__(self):
        # 地址池容量限制配置
        self.max_pool_size = 65536  # 最大支持的地址池大小
        self.recommended_pool_size = 1024  # 推荐的地址池大小
        self.min_pool_size = 1  # 最小地址池大小
        
        # 效率阈值
        self.high_efficiency_threshold = 0.8  # 高效率阈值
        self.low_efficiency_threshold = 0.3   # 低效率阈值
        
        # 私有网络范围（RFC 1918）
        self.private_networks = [
            ipaddress.IPv4Network('10.0.0.0/8'),
            ipaddress.IPv4Network('**********/12'),
            ipaddress.IPv4Network('***********/16')
        ]
        
        # 保留地址范围
        self.reserved_networks = [
            ipaddress.IPv4Network('*********/8'),    # 回环地址
            ipaddress.IPv4Network('***********/16'), # 链路本地地址
            ipaddress.IPv4Network('*********/4'),    # 多播地址
            ipaddress.IPv4Network('240.0.0.0/4')     # 保留地址
        ]

        # 增强日志记录器
        self.enhanced_logger = get_enhanced_logger("ippool_validator")
    
    @performance_optimized("validate_ippool", use_cache=True, cache_key_func=lambda self, name, config: f"validate_{name}_{hash(str(config))}")
    @performance_monitor("validate_ippool")
    @error_handler("validate_ippool", reraise=False)
    def validate_ippool(self, pool_name: str, pool_config: Dict) -> IPPoolValidationResult:
        """
        验证单个IP池配置
        
        Args:
            pool_name: 池名称
            pool_config: 池配置
            
        Returns:
            IPPoolValidationResult: 验证结果
        """
        result = IPPoolValidationResult()
        
        # 验证基本配置完整性
        if not self._validate_basic_config(pool_name, pool_config, result):
            return result
        
        # 验证IP地址范围
        start_ip = pool_config.get("startip")
        end_ip = pool_config.get("endip")
        
        if not self._validate_ip_range(start_ip, end_ip, result):
            return result
        
        # 验证池名称
        self._validate_pool_name(pool_name, result)
        
        # 计算和验证容量
        self._analyze_pool_capacity(start_ip, end_ip, result)
        
        # 验证网络类型和安全性
        self._validate_network_security(start_ip, end_ip, result)
        
        # 生成优化建议
        self._generate_optimization_suggestions(pool_name, start_ip, end_ip, result)
        
        return result
    
    def _validate_basic_config(self, pool_name: str, pool_config: Dict, 
                             result: IPPoolValidationResult) -> bool:
        """验证基本配置完整性"""
        if not pool_name:
            result.add_error(_("ippool_validator.missing_pool_name"))
            return False
        
        if not isinstance(pool_config, dict):
            result.add_error(_("ippool_validator.invalid_pool_config", name=pool_name))
            return False
        
        # 检查必需字段
        required_fields = ["startip", "endip"]
        for field in required_fields:
            if field not in pool_config or not pool_config[field]:
                result.add_error(_("ippool_validator.missing_required_field", 
                                 name=pool_name, field=field))
                return False
        
        return True
    
    def _validate_ip_range(self, start_ip: str, end_ip: str, 
                          result: IPPoolValidationResult) -> bool:
        """验证IP地址范围"""
        try:
            # 验证IP地址格式
            start_addr = ipaddress.IPv4Address(start_ip)
            end_addr = ipaddress.IPv4Address(end_ip)
            
            result.add_info("start_address", str(start_addr))
            result.add_info("end_address", str(end_addr))
            
        except ipaddress.AddressValueError as e:
            result.add_error(_("ippool_validator.invalid_ip_format", 
                             start_ip=start_ip, end_ip=end_ip, error=str(e)))
            return False
        
        # 验证范围逻辑
        if start_addr > end_addr:
            result.add_error(_("ippool_validator.invalid_ip_range_order", 
                             start_ip=start_ip, end_ip=end_ip))
            return False
        
        # 验证范围大小
        range_size = int(end_addr) - int(start_addr) + 1
        if range_size > self.max_pool_size:
            result.add_error(_("ippool_validator.pool_size_too_large", 
                             size=range_size, max_size=self.max_pool_size))
            return False
        
        if range_size < self.min_pool_size:
            result.add_error(_("ippool_validator.pool_size_too_small", 
                             size=range_size, min_size=self.min_pool_size))
            return False
        
        return True
    
    def _validate_pool_name(self, pool_name: str, result: IPPoolValidationResult):
        """验证池名称"""
        # 清理名称
        clean_name = clean_ntos_name(pool_name, 64)
        
        # 验证清理后的名称
        is_valid, error_msg = validate_ntos_name(clean_name)
        
        if not is_valid:
            result.add_warning(_("ippool_validator.invalid_pool_name", 
                               name=pool_name, clean_name=clean_name, error=error_msg))
            result.add_info("suggested_name", clean_name)
        
        # 检查名称长度
        if len(pool_name) > 64:
            result.add_warning(_("ippool_validator.pool_name_too_long", 
                               name=pool_name, length=len(pool_name)))
        
        # 记录名称信息
        result.add_info("original_name", pool_name)
        result.add_info("clean_name", clean_name)
    
    def _analyze_pool_capacity(self, start_ip: str, end_ip: str, 
                              result: IPPoolValidationResult):
        """分析池容量"""
        try:
            start_addr = ipaddress.IPv4Address(start_ip)
            end_addr = ipaddress.IPv4Address(end_ip)
            
            # 计算总IP数量
            total_ips = int(end_addr) - int(start_addr) + 1
            
            # 计算可用IP数量（排除网络地址和广播地址，如果适用）
            usable_ips = total_ips
            
            # 计算效率（简化计算，实际使用中可能需要更复杂的算法）
            efficiency = min(1.0, usable_ips / max(1, total_ips))
            
            # 设置容量信息
            result.set_capacity_info(total_ips, usable_ips, efficiency)
            
            # 容量警告
            if total_ips > self.recommended_pool_size:
                result.add_warning(_("ippool_validator.pool_size_large", 
                                   size=total_ips, recommended=self.recommended_pool_size))
            
            if efficiency < self.low_efficiency_threshold:
                result.add_warning(_("ippool_validator.low_efficiency", 
                                   efficiency=efficiency * 100))
            
        except Exception as e:
            result.add_error(_("ippool_validator.capacity_analysis_failed", error=str(e)))

    def _validate_network_security(self, start_ip: str, end_ip: str,
                                  result: IPPoolValidationResult):
        """验证网络安全性"""
        try:
            start_addr = ipaddress.IPv4Address(start_ip)
            end_addr = ipaddress.IPv4Address(end_ip)

            # 检查是否在私有网络范围内
            is_private = False
            for private_net in self.private_networks:
                if start_addr in private_net and end_addr in private_net:
                    is_private = True
                    result.add_info("network_type", "private")
                    result.add_info("private_network", str(private_net))
                    break

            if not is_private:
                result.add_info("network_type", "public")
                result.add_warning(_("ippool_validator.public_ip_range",
                                   start_ip=start_ip, end_ip=end_ip))

            # 检查是否包含保留地址
            for reserved_net in self.reserved_networks:
                if (start_addr in reserved_net or end_addr in reserved_net or
                    any(ipaddress.IPv4Address(int(start_addr) + i) in reserved_net
                        for i in range(min(100, int(end_addr) - int(start_addr) + 1)))):
                    result.add_error(_("ippool_validator.contains_reserved_addresses",
                                     reserved_network=str(reserved_net)))
                    break

            # 检查跨网段情况
            start_network = ipaddress.IPv4Network(f"{start_ip}/24", strict=False)
            end_network = ipaddress.IPv4Network(f"{end_ip}/24", strict=False)

            if start_network != end_network:
                result.add_warning(_("ippool_validator.cross_subnet_range",
                                   start_subnet=str(start_network),
                                   end_subnet=str(end_network)))

        except Exception as e:
            result.add_error(_("ippool_validator.security_validation_failed", error=str(e)))

    def _generate_optimization_suggestions(self, pool_name: str, start_ip: str, end_ip: str,
                                         result: IPPoolValidationResult):
        """生成优化建议"""
        try:
            start_addr = ipaddress.IPv4Address(start_ip)
            end_addr = ipaddress.IPv4Address(end_ip)
            total_ips = int(end_addr) - int(start_addr) + 1

            # 大小优化建议
            if total_ips > self.recommended_pool_size * 2:
                result.add_optimization_suggestion(
                    _("ippool_validator.suggestion_split_large_pool",
                      name=pool_name, size=total_ips))

            if total_ips < 10:
                result.add_optimization_suggestion(
                    _("ippool_validator.suggestion_merge_small_pool",
                      name=pool_name, size=total_ips))

            # 对齐优化建议
            if int(start_addr) % 256 != 0:
                aligned_start = (int(start_addr) // 256) * 256
                result.add_optimization_suggestion(
                    _("ippool_validator.suggestion_align_to_subnet",
                      name=pool_name, suggested_start=str(ipaddress.IPv4Address(aligned_start))))

            # 效率优化建议
            capacity_info = result.capacity_info
            if capacity_info and capacity_info.get("efficiency", 1.0) < self.high_efficiency_threshold:
                result.add_optimization_suggestion(
                    _("ippool_validator.suggestion_improve_efficiency",
                      name=pool_name, current_efficiency=capacity_info["efficiency"] * 100))

        except Exception as e:
            log(_("ippool_validator.optimization_suggestion_failed", error=str(e)), "warning")

    def validate_multiple_ippools(self, ippools: Dict[str, Dict]) -> Dict[str, IPPoolValidationResult]:
        """
        验证多个IP池配置

        Args:
            ippools: IP池配置字典

        Returns: Dict[str, IPPoolValidationResult]: 验证结果字典
        """
        results = {}

        for pool_name, pool_config in ippools.items():
            results[pool_name] = self.validate_ippool(pool_name, pool_config)

        # 执行跨池验证
        self._validate_cross_pool_conflicts(ippools, results)

        return results

    def _validate_cross_pool_conflicts(self, ippools: Dict[str, Dict],
                                     results: Dict[str, IPPoolValidationResult]):
        """验证跨池冲突"""
        pool_ranges = {}

        # 收集所有有效的池范围
        for pool_name, pool_config in ippools.items():
            if pool_name in results and results[pool_name].is_valid:
                try:
                    start_ip = pool_config.get("startip")
                    end_ip = pool_config.get("endip")
                    start_addr = ipaddress.IPv4Address(start_ip)
                    end_addr = ipaddress.IPv4Address(end_ip)
                    pool_ranges[pool_name] = (start_addr, end_addr)
                except:
                    continue

        # 检查重叠
        pool_names = list(pool_ranges.keys())
        for i, pool1 in enumerate(pool_names):
            for pool2 in pool_names[i+1:]:
                if self._check_ip_range_overlap(pool_ranges[pool1], pool_ranges[pool2]):
                    # 添加重叠警告到两个池的结果中
                    overlap_msg = _("ippool_validator.pool_range_overlap",
                                   pool1=pool1, pool2=pool2)
                    results[pool1].add_warning(overlap_msg)
                    results[pool2].add_warning(overlap_msg)

    def _check_ip_range_overlap(self, range1: Tuple, range2: Tuple) -> bool:
        """检查两个IP范围是否重叠"""
        start1, end1 = range1
        start2, end2 = range2

        # 检查重叠：range1的开始在range2内，或range2的开始在range1内
        return (start2 <= start1 <= end2 or start2 <= end1 <= end2 or
                start1 <= start2 <= end1 or start1 <= end2 <= end1)

    def generate_validation_report(self, results: Dict[str, IPPoolValidationResult]) -> str:
        """
        生成验证报告

        Args:
            results: 验证结果字典

        Returns:
            str: 格式化的验证报告
        """
        report_lines = []
        report_lines.append("=== IP池验证报告 ===")
        report_lines.append("")

        total_pools = len(results)
        valid_pools = sum(1 for r in results.values() if r.is_valid)
        invalid_pools = total_pools - valid_pools

        report_lines.append(f"总计IP池: {total_pools}")
        report_lines.append(f"有效IP池: {valid_pools}")
        report_lines.append(f"无效IP池: {invalid_pools}")
        report_lines.append("")

        for pool_name, result in results.items():
            report_lines.append(f"--- {pool_name} ---")
            report_lines.append(f"状态: {'有效' if result.is_valid else '无效'}")

            if result.capacity_info:
                cap = result.capacity_info
                report_lines.append(f"容量: {cap['total_ips']} IP地址")
                report_lines.append(f"效率: {cap['efficiency']*100:.1f}%")

            if result.errors:
                report_lines.append("错误:")
                for error in result.errors:
                    report_lines.append(f"  - {error}")

            if result.warnings:
                report_lines.append("警告:")
                for warning in result.warnings:
                    report_lines.append(f"  - {warning}")

            if result.optimization_suggestions:
                report_lines.append("优化建议:")
                for suggestion in result.optimization_suggestions:
                    report_lines.append(f"  - {suggestion}")

            report_lines.append("")

        return "\n".join(report_lines)

    @batch_process("pool_validation", batch_size=50)
    @performance_optimized("validate_batch_pools")
    def validate_batch_pools(self, pools: List[Dict]) -> List[IPPoolValidationResult]:
        """
        批量验证IP池配置

        Args:
            pools: IP池配置列表

        Returns:
            验证结果列表
        """
        results = []
        for pool in pools:
            pool_name = pool.get("name", "unknown")
            result = self.validate_ippool(pool_name, pool)
            results.append(result)

        return results
