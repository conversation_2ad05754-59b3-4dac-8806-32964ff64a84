"""
twice-nat44验证器

专门用于验证twice-nat44配置的XML结构和YANG模型符合性。
基于已重构的验证框架，提供企业级的验证能力。
"""

import os
import tempfile
from typing import Tuple, Dict, Any, List, Optional
from lxml import etree
from engine.utils.logger import log
from engine.utils.i18n import _
from engine.infrastructure.yang.yang_validator import YangVali<PERSON><PERSON>
from engine.infrastructure.error_handling import (
    twice_nat44_error_handler, twice_nat44_validation_required, TwiceNat44ErrorContext
)


class TwiceNat44Validator:
    """
    twice-nat44验证器
    
    提供twice-nat44配置的结构验证、YANG模型符合性检查和语义验证。
    """
    
    def __init__(self, config_manager=None):
        """
        初始化twice-nat44验证器
        
        Args:
            config_manager: 配置管理器（可选）
        """
        self.config_manager = config_manager
        self.yang_validator = <PERSON><PERSON>alidator(config_manager) if config_manager else None
        
        # twice-nat44 YANG模型约束
        self.yang_constraints = {
            "required_elements": ["match", "snat", "dnat"],
            "match_required": ["dest-network"],
            "snat_address_types": ["output-address", "ipv4-address", "pool-name"],
            "dnat_required": ["ipv4-address"],
            "boolean_elements": ["no-pat", "try-no-pat", "rule_en"],
            "string_max_length": {
                "name": 255,
                "desc": 255,
                "ipv4-address": 15,
                "pool-name": 255
            }
        }
        
        log(_("twice_nat44_validator.initialized"), "info")
    
    @twice_nat44_error_handler(
        operation="validate_twice_nat44_rule",
        max_retries=1,
        handle_exceptions=(etree.XMLSyntaxError, AttributeError),
        fallback_value=(False, ["Validation failed due to error"]),
        log_errors=True
    )
    @twice_nat44_validation_required(
        validate_input=True,
        input_validator=lambda self, rule_element: {'valid': rule_element is not None}
    )
    def validate_twice_nat44_rule(self, rule_element: etree.Element) -> Tuple[bool, List[str]]:
        """
        验证twice-nat44规则的完整性
        
        Args:
            rule_element: 规则XML元素
            
        Returns:
            Tuple[bool, List[str]]: (验证是否通过, 错误信息列表)
        """
        errors = []
        
        try:
            # 检查是否为twice-nat44规则
            twice_nat_elem = rule_element.find("twice-nat44")
            if twice_nat_elem is None:
                errors.append(_("twice_nat44_validator.not_twice_nat44_rule"))
                return False, errors
            
            # 验证基本结构
            structure_valid, structure_errors = self._validate_structure(rule_element, twice_nat_elem)
            if not structure_valid:
                errors.extend(structure_errors)
            
            # 验证匹配条件
            match_valid, match_errors = self._validate_match_conditions(twice_nat_elem)
            if not match_valid:
                errors.extend(match_errors)
            
            # 验证SNAT配置
            snat_valid, snat_errors = self._validate_snat_config(twice_nat_elem)
            if not snat_valid:
                errors.extend(snat_errors)
            
            # 验证DNAT配置
            dnat_valid, dnat_errors = self._validate_dnat_config(twice_nat_elem)
            if not dnat_valid:
                errors.extend(dnat_errors)
            
            # 验证数据类型和约束
            constraints_valid, constraints_errors = self._validate_yang_constraints(rule_element)
            if not constraints_valid:
                errors.extend(constraints_errors)
            
            is_valid = len(errors) == 0
            
            if is_valid:
                log(_("twice_nat44_validator.rule_validation_passed"), "debug")
            else:
                log(_("twice_nat44_validator.rule_validation_failed", count=len(errors)), "warning")
            
            return is_valid, errors
            
        except Exception as e:
            error_msg = _("twice_nat44_validator.validation_exception", error=str(e))
            log(error_msg, "error")
            errors.append(error_msg)
            return False, errors
    
    def _validate_structure(self, rule_element: etree.Element, twice_nat_elem: etree.Element) -> Tuple[bool, List[str]]:
        """验证基本结构"""
        errors = []
        
        # 检查规则基本元素
        name_elem = rule_element.find("name")
        if name_elem is None or not name_elem.text:
            errors.append(_("twice_nat44_validator.missing_rule_name"))
        
        rule_en_elem = rule_element.find("rule_en")
        if rule_en_elem is None:
            errors.append(_("twice_nat44_validator.missing_rule_en"))
        
        # 检查twice-nat44必需元素
        for required_elem in self.yang_constraints["required_elements"]:
            if twice_nat_elem.find(required_elem) is None:
                errors.append(_("twice_nat44_validator.missing_required_element", element=required_elem))
        
        return len(errors) == 0, errors
    
    def _validate_match_conditions(self, twice_nat_elem: etree.Element) -> Tuple[bool, List[str]]:
        """验证匹配条件"""
        errors = []
        
        match_elem = twice_nat_elem.find("match")
        if match_elem is None:
            errors.append(_("twice_nat44_validator.missing_match_element"))
            return False, errors
        
        # 检查必需的匹配条件
        for required_match in self.yang_constraints["match_required"]:
            match_child = match_elem.find(required_match)
            if match_child is None:
                errors.append(_("twice_nat44_validator.missing_match_condition", condition=required_match))
            else:
                # 检查dest-network是否有name子元素
                if required_match == "dest-network":
                    name_elem = match_child.find("name")
                    if name_elem is None or not name_elem.text:
                        errors.append(_("twice_nat44_validator.missing_dest_network_name"))
        
        return len(errors) == 0, errors
    
    def _validate_snat_config(self, twice_nat_elem: etree.Element) -> Tuple[bool, List[str]]:
        """验证SNAT配置"""
        errors = []
        
        snat_elem = twice_nat_elem.find("snat")
        if snat_elem is None:
            errors.append(_("twice_nat44_validator.missing_snat_element"))
            return False, errors
        
        # 检查地址类型（必须有且只有一个）
        address_type_count = 0
        for addr_type in self.yang_constraints["snat_address_types"]:
            if snat_elem.find(addr_type) is not None:
                address_type_count += 1
        
        if address_type_count == 0:
            errors.append(_("twice_nat44_validator.missing_snat_address_type"))
        elif address_type_count > 1:
            errors.append(_("twice_nat44_validator.multiple_snat_address_types"))
        
        # 验证IP地址格式（如果使用ipv4-address）
        ipv4_elem = snat_elem.find("ipv4-address")
        if ipv4_elem is not None and ipv4_elem.text:
            if not self._is_valid_ipv4(ipv4_elem.text):
                errors.append(_("twice_nat44_validator.invalid_snat_ipv4", ip=ipv4_elem.text))
        
        return len(errors) == 0, errors
    
    def _validate_dnat_config(self, twice_nat_elem: etree.Element) -> Tuple[bool, List[str]]:
        """验证DNAT配置"""
        errors = []
        
        dnat_elem = twice_nat_elem.find("dnat")
        if dnat_elem is None:
            errors.append(_("twice_nat44_validator.missing_dnat_element"))
            return False, errors
        
        # 检查必需的DNAT元素
        for required_dnat in self.yang_constraints["dnat_required"]:
            dnat_child = dnat_elem.find(required_dnat)
            if dnat_child is None or not dnat_child.text:
                errors.append(_("twice_nat44_validator.missing_dnat_element", element=required_dnat))
            elif required_dnat == "ipv4-address":
                # 验证IP地址格式
                if not self._is_valid_ipv4(dnat_child.text):
                    errors.append(_("twice_nat44_validator.invalid_dnat_ipv4", ip=dnat_child.text))
        
        # 验证端口号（如果存在）
        port_elem = dnat_elem.find("port")
        if port_elem is not None and port_elem.text:
            try:
                port = int(port_elem.text)
                if not (1 <= port <= 65535):
                    errors.append(_("twice_nat44_validator.invalid_port_range", port=port))
            except ValueError:
                errors.append(_("twice_nat44_validator.invalid_port_format", port=port_elem.text))
        
        return len(errors) == 0, errors
    
    def _validate_yang_constraints(self, rule_element: etree.Element) -> Tuple[bool, List[str]]:
        """验证YANG模型约束"""
        errors = []
        
        # 验证布尔值元素
        for bool_elem_name in self.yang_constraints["boolean_elements"]:
            bool_elem = rule_element.find(f".//{bool_elem_name}")
            if bool_elem is not None and bool_elem.text:
                if bool_elem.text.lower() not in ["true", "false"]:
                    errors.append(_("twice_nat44_validator.invalid_boolean_value", 
                                   element=bool_elem_name, value=bool_elem.text))
        
        # 验证字符串长度约束
        for elem_name, max_length in self.yang_constraints["string_max_length"].items():
            elem = rule_element.find(f".//{elem_name}")
            if elem is not None and elem.text:
                if len(elem.text) > max_length:
                    errors.append(_("twice_nat44_validator.string_too_long", 
                                   element=elem_name, length=len(elem.text), max_length=max_length))
        
        return len(errors) == 0, errors
    
    def _is_valid_ipv4(self, ip: str) -> bool:
        """验证IPv4地址格式"""
        try:
            parts = ip.split('.')
            if len(parts) != 4:
                return False
            for part in parts:
                if not (0 <= int(part) <= 255):
                    return False
            return True
        except (ValueError, AttributeError):
            return False
    
    def validate_against_yang_model(self, xml_content: str, model: str, version: str) -> Tuple[bool, str]:
        """
        使用YANG模型验证twice-nat44 XML
        
        Args:
            xml_content: XML内容
            model: 设备型号
            version: 设备版本
            
        Returns:
            Tuple[bool, str]: (验证是否通过, 验证消息)
        """
        if not self.yang_validator:
            return True, _("twice_nat44_validator.yang_validation_skipped")
        
        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.xml', delete=False, encoding='utf-8') as temp_file:
                temp_file.write(xml_content)
                temp_file_path = temp_file.name
            
            try:
                # 获取YANG模型目录
                yang_dir = self.config_manager.get_yang_model_dir(model, version) if self.config_manager else None
                if not yang_dir:
                    return True, _("twice_nat44_validator.yang_dir_not_found")
                
                # 执行YANG验证
                is_valid, message = self.yang_validator.validate_xml(temp_file_path, yang_dir, model, version)
                
                if is_valid:
                    log(_("twice_nat44_validator.yang_validation_passed"), "info")
                else:
                    log(_("twice_nat44_validator.yang_validation_failed", message=message), "warning")
                
                return is_valid, message
                
            finally:
                # 清理临时文件
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)
                    
        except Exception as e:
            error_msg = _("twice_nat44_validator.yang_validation_error", error=str(e))
            log(error_msg, "error")
            return False, error_msg
    
    def generate_validation_report(self, rule_element: etree.Element) -> Dict[str, Any]:
        """
        生成详细的验证报告
        
        Args:
            rule_element: 规则XML元素
            
        Returns:
            Dict[str, Any]: 验证报告
        """
        report = {
            "rule_name": None,
            "validation_passed": False,
            "errors": [],
            "warnings": [],
            "structure_check": False,
            "match_check": False,
            "snat_check": False,
            "dnat_check": False,
            "yang_constraints_check": False
        }
        
        try:
            # 获取规则名称
            name_elem = rule_element.find("name")
            if name_elem is not None:
                report["rule_name"] = name_elem.text
            
            # 执行验证
            is_valid, errors = self.validate_twice_nat44_rule(rule_element)
            report["validation_passed"] = is_valid
            report["errors"] = errors if isinstance(errors, list) else [errors]
            
            # 详细检查各个部分
            twice_nat_elem = rule_element.find("twice-nat44")
            if twice_nat_elem is not None:
                report["structure_check"], _ = self._validate_structure(rule_element, twice_nat_elem)
                report["match_check"], _ = self._validate_match_conditions(twice_nat_elem)
                report["snat_check"], _ = self._validate_snat_config(twice_nat_elem)
                report["dnat_check"], _ = self._validate_dnat_config(twice_nat_elem)
                report["yang_constraints_check"], _ = self._validate_yang_constraints(rule_element)
            
            log(_("twice_nat44_validator.report_generated", rule=report["rule_name"]), "debug")
            
        except Exception as e:
            error_msg = _("twice_nat44_validator.report_generation_failed", error=str(e))
            log(error_msg, "error")
            report["errors"].append(error_msg)
        
        return report
