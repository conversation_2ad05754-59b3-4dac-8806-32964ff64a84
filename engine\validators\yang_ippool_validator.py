"""
YANG模型地址池预验证器

基于NTOS YANG模型对地址池配置进行预验证，包括：
- 地址池结构验证
- 字段类型和格式验证
- 约束条件检查
- 依赖关系验证
"""

import re
import ipaddress
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from engine.utils.logger import log
from engine.utils.i18n import _


@dataclass
class YangValidationError:
    """YANG验证错误"""
    field_path: str
    error_type: str
    message: str
    severity: str = "error"  # error, warning, info


@dataclass
class YangValidationResult:
    """YANG验证结果"""
    is_valid: bool = True
    errors: List[YangValidationError] = None
    warnings: List[YangValidationError] = None
    
    def __post_init__(self):
        if self.errors is None:
            self.errors = []
        if self.warnings is None:
            self.warnings = []
    
    def add_error(self, field_path: str, error_type: str, message: str):
        """添加错误"""
        self.is_valid = False
        self.errors.append(YangValidationError(field_path, error_type, message, "error"))
    
    def add_warning(self, field_path: str, error_type: str, message: str):
        """添加警告"""
        self.warnings.append(YangValidationError(field_path, error_type, message, "warning"))


class YangIPPoolValidator:
    """YANG模型地址池验证器"""
    
    def __init__(self):
        # NTOS对象名称类型约束 (ntos-obj-name-type)
        # 基于YANG模型实际约束：pattern "[^`~!#$%^&*+/|{};:\"',\\\\<>?]*"
        self.obj_name_pattern = re.compile(r'^[^`~!#$%^&*+/|{};:"\'\\<>?,]*$')
        self.obj_name_max_length = 64
        # 只包含YANG模型明确禁用的字符，移除土耳其语字符（YANG模型原生支持）
        self.obj_name_forbidden_chars = set('`~!@#$%^&*+|{};:"\'\\/<>?,')

        # 描述字段约束 (ntos-obj-description-type)
        self.desc_forbidden_chars = set('`~!#$%^&*+/|{};:"\'\\<>?')

        # IPv4地址池地址类型约束 (pool-ipv4-address)
        self.ipv4_address_patterns = [
            re.compile(r'^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$'),  # 单个IP
            re.compile(r'^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}-\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$'),  # IP范围
            re.compile(r'^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}/\d{1,2}$')  # CIDR格式
        ]

        # 地址池配置约束
        self.pool_name_required = True
        self.pool_address_required = True
        self.pool_desc_max_length = 255

        # NAT规则约束
        self.nat_rule_name_required = True
        self.pool_name_reference_required = True
    
    def validate_pool_configuration(self, pool_config: Dict[str, Any]) -> YangValidationResult:
        """
        验证地址池配置
        
        Args:
            pool_config: 地址池配置字典
            
        Returns:
            YangValidationResult: 验证结果
        """
        result = YangValidationResult()
        
        # 验证池名称
        self._validate_pool_name(pool_config, result)
        
        # 验证地址配置
        self._validate_pool_address(pool_config, result)
        
        # 验证描述字段
        self._validate_pool_description(pool_config, result)
        
        # 验证整体结构
        self._validate_pool_structure(pool_config, result)
        
        return result
    
    def validate_nat_rule_pool_reference(self, nat_rule: Dict[str, Any]) -> YangValidationResult:
        """
        验证NAT规则中的地址池引用
        
        Args:
            nat_rule: NAT规则配置
            
        Returns:
            YangValidationResult: 验证结果
        """
        result = YangValidationResult()
        
        # 验证规则名称
        self._validate_nat_rule_name(nat_rule, result)
        
        # 验证动态转换参数中的池引用
        self._validate_dynamic_translate_parameters(nat_rule, result)
        
        return result
    
    def _validate_pool_name(self, pool_config: Dict[str, Any], result: YangValidationResult):
        """验证池名称"""
        pool_name = pool_config.get("name")
        
        # 检查是否存在
        if not pool_name:
            if self.pool_name_required:
                result.add_error("name", "missing_required_field", 
                               _("yang_validator.pool_name_required"))
            return
        
        # 检查类型
        if not isinstance(pool_name, str):
            result.add_error("name", "invalid_type", 
                           _("yang_validator.pool_name_must_be_string"))
            return
        
        # 检查长度
        if len(pool_name) > self.obj_name_max_length:
            result.add_error("name", "length_exceeded", 
                           _("yang_validator.pool_name_too_long", 
                             length=len(pool_name), max_length=self.obj_name_max_length))
        
        # 检查字符约束
        if not self.obj_name_pattern.match(pool_name):
            result.add_error("name", "invalid_characters", 
                           _("yang_validator.pool_name_invalid_characters", name=pool_name))
        
        # 检查禁用字符
        forbidden_found = set(pool_name) & self.obj_name_forbidden_chars
        if forbidden_found:
            result.add_error("name", "forbidden_characters", 
                           _("yang_validator.pool_name_forbidden_chars", 
                             name=pool_name, chars=''.join(forbidden_found)))
    
    def _validate_pool_address(self, pool_config: Dict[str, Any], result: YangValidationResult):
        """验证池地址配置"""
        address_config = pool_config.get("address")
        
        # 检查address字段是否存在
        if not address_config:
            if self.pool_address_required:
                result.add_error("address", "missing_required_field", 
                               _("yang_validator.pool_address_required"))
            return
        
        # 检查address结构
        if not isinstance(address_config, dict):
            result.add_error("address", "invalid_structure", 
                           _("yang_validator.pool_address_invalid_structure"))
            return
        
        # 检查value字段
        address_value = address_config.get("value")
        if not address_value:
            result.add_error("address.value", "missing_required_field", 
                           _("yang_validator.pool_address_value_required"))
            return
        
        # 验证地址值格式
        self._validate_ipv4_address_value(address_value, result)
    
    def _validate_ipv4_address_value(self, address_value: str, result: YangValidationResult):
        """验证IPv4地址值"""
        if not isinstance(address_value, str):
            result.add_error("address.value", "invalid_type", 
                           _("yang_validator.address_value_must_be_string"))
            return
        
        # 检查是否匹配任何有效格式
        format_matched = False
        for pattern in self.ipv4_address_patterns:
            if pattern.match(address_value):
                format_matched = True
                break
        
        if not format_matched:
            result.add_error("address.value", "invalid_format", 
                           _("yang_validator.invalid_ipv4_address_format", value=address_value))
            return
        
        # 进一步验证IP地址的有效性
        if '-' in address_value:
            # IP范围格式
            self._validate_ip_range_format(address_value, result)
        elif '/' in address_value:
            # CIDR格式
            self._validate_cidr_format(address_value, result)
        else:
            # 单个IP格式
            self._validate_single_ip_format(address_value, result)
    
    def _validate_ip_range_format(self, ip_range: str, result: YangValidationResult):
        """验证IP范围格式"""
        try:
            start_ip, end_ip = ip_range.split('-', 1)
            start_addr = ipaddress.IPv4Address(start_ip.strip())
            end_addr = ipaddress.IPv4Address(end_ip.strip())
            
            if start_addr > end_addr:
                result.add_error("address.value", "invalid_range", 
                               _("yang_validator.invalid_ip_range_order", 
                                 start=start_ip, end=end_ip))
        except ValueError as e:
            result.add_error("address.value", "invalid_ip_address", 
                           _("yang_validator.invalid_ip_in_range", range=ip_range, error=str(e)))
    
    def _validate_cidr_format(self, cidr: str, result: YangValidationResult):
        """验证CIDR格式"""
        try:
            network = ipaddress.IPv4Network(cidr, strict=False)
            # 检查前缀长度是否合理
            if network.prefixlen < 8 or network.prefixlen > 30:
                result.add_warning("address.value", "unusual_prefix_length", 
                                 _("yang_validator.unusual_cidr_prefix", 
                                   cidr=cidr, prefix=network.prefixlen))
        except ValueError as e:
            result.add_error("address.value", "invalid_cidr", 
                           _("yang_validator.invalid_cidr_format", cidr=cidr, error=str(e)))
    
    def _validate_single_ip_format(self, ip_addr: str, result: YangValidationResult):
        """验证单个IP格式"""
        try:
            ipaddress.IPv4Address(ip_addr.strip())
        except ValueError as e:
            result.add_error("address.value", "invalid_ip_address", 
                           _("yang_validator.invalid_ip_address", ip=ip_addr, error=str(e)))
    
    def _validate_pool_description(self, pool_config: Dict[str, Any], result: YangValidationResult):
        """验证池描述"""
        desc = pool_config.get("desc")

        if desc is not None:
            if not isinstance(desc, str):
                result.add_error("desc", "invalid_type",
                               _("yang_validator.pool_desc_must_be_string"))
            elif len(desc) > self.pool_desc_max_length:
                result.add_error("desc", "length_exceeded",
                               _("yang_validator.pool_desc_too_long",
                                 length=len(desc), max_length=self.pool_desc_max_length))
            else:
                # 检查描述中的禁用字符
                forbidden_found = set(desc) & self.desc_forbidden_chars
                if forbidden_found:
                    result.add_error("desc", "forbidden_characters",
                                   _("yang_validator.pool_desc_forbidden_chars",
                                     desc=desc, chars=''.join(forbidden_found)))
    
    def _validate_pool_structure(self, pool_config: Dict[str, Any], result: YangValidationResult):
        """验证池整体结构"""
        # 检查是否有未知字段
        known_fields = {"name", "desc", "address"}
        unknown_fields = set(pool_config.keys()) - known_fields
        
        if unknown_fields:
            result.add_warning("", "unknown_fields", 
                             _("yang_validator.unknown_pool_fields", 
                               fields=', '.join(unknown_fields)))
    
    def _validate_nat_rule_name(self, nat_rule: Dict[str, Any], result: YangValidationResult):
        """验证NAT规则名称"""
        rule_name = nat_rule.get("name")
        
        if not rule_name:
            if self.nat_rule_name_required:
                result.add_error("name", "missing_required_field", 
                               _("yang_validator.nat_rule_name_required"))
            return
        
        # 应用与池名称相同的验证规则
        if not isinstance(rule_name, str):
            result.add_error("name", "invalid_type", 
                           _("yang_validator.nat_rule_name_must_be_string"))
            return
        
        if len(rule_name) > self.obj_name_max_length:
            result.add_error("name", "length_exceeded", 
                           _("yang_validator.nat_rule_name_too_long", 
                             length=len(rule_name), max_length=self.obj_name_max_length))
        
        if not self.obj_name_pattern.match(rule_name):
            result.add_error("name", "invalid_characters", 
                           _("yang_validator.nat_rule_name_invalid_characters", name=rule_name))
    
    def _validate_dynamic_translate_parameters(self, nat_rule: Dict[str, Any], result: YangValidationResult):
        """验证动态转换参数"""
        dyn_params = nat_rule.get("dynamic-translate-parameters")
        
        if not dyn_params:
            return  # 不是动态SNAT规则，跳过验证
        
        if not isinstance(dyn_params, dict):
            result.add_error("dynamic-translate-parameters", "invalid_structure", 
                           _("yang_validator.dynamic_params_invalid_structure"))
            return
        
        # 验证pool-name字段
        pool_name = dyn_params.get("pool-name")
        if not pool_name:
            if self.pool_name_reference_required:
                result.add_error("dynamic-translate-parameters.pool-name", "missing_required_field", 
                               _("yang_validator.pool_name_reference_required"))
            return
        
        # 验证引用的池名称格式
        if not isinstance(pool_name, str):
            result.add_error("dynamic-translate-parameters.pool-name", "invalid_type", 
                           _("yang_validator.pool_name_reference_must_be_string"))
        elif not self.obj_name_pattern.match(pool_name):
            result.add_error("dynamic-translate-parameters.pool-name", "invalid_characters", 
                           _("yang_validator.pool_name_reference_invalid_characters", name=pool_name))
