#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import json
import re
import sys

# 添加父目录到Python路径，确保可以正确导入engine模块
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from engine.utils.logger import log, user_log
from engine.utils.i18n import _
import logging

# 支持的厂商和对应的验证函数
SUPPORTED_VENDORS = {
    "fortigate": "verify_fortigate_config",
    # 未来可以添加更多厂商支持，取消注释或添加新条目:
    # "cisco": "verify_cisco_config",
}

def verify_config_file(cli_file, vendor="fortigate", language="zh-CN", strict_version=True, quiet=False, device_model=None):
    """验证配置文件的格式和语义

    Args:
        cli_file (str): 配置文件路径
        vendor (str): 设备厂商，默认为fortigate
        language (str): 语言代码，默认为zh-CN
        strict_version (bool): 是否严格检查版本，默认为True
        quiet (bool): 是否静默模式，默认为False
        device_model (str): 目标设备型号，用于容量限制校验，默认为None

    Returns:
        tuple: (是否验证通过, 错误消息, 警告列表)
    """
    try:
        # 初始化国际化
        from engine.utils.i18n import init_i18n
        init_i18n(language)
        
        # 检查厂商是否支持
        vendor = vendor.lower()
        if vendor not in SUPPORTED_VENDORS:
            supported = list(SUPPORTED_VENDORS.keys())
            log(_("error.vendor_not_supported"), "error", vendor=vendor, supported=", ".join(supported))
            user_log(_("error.vendor_not_supported"), "error", vendor=vendor, supported=", ".join(supported))
            return False, _("error.vendor_not_supported", vendor=vendor, supported=", ".join(supported)), []
            
        log(_("info.start_verify_vendor_config"), vendor=vendor, file=cli_file)
        user_log(_("info.start_verification"))
        
        # 检查文件是否存在
        if not os.path.exists(cli_file):
            log(_("error.file_not_exists"), "error", file=cli_file)
            user_log(_("error.file_not_exists"), "error", file=cli_file)
            return False, _("error.file_not_exists", file=cli_file), []
        
        # 检查文件是否为空
        if os.path.getsize(cli_file) == 0:
            log(_("error.file_empty"), "error", file=cli_file)
            user_log(_("error.file_empty"), "error")
            return False, _("error.file_empty"), []
        
        # 设置默认最低版本为6.0.0
        min_version_parts = [6, 0, 0]
        log(_("info.min_version_requirement"), min_version="6.0.0")
        
        # 如果是FortiGate，检查配置文件内容中的版本信息
        if vendor == "fortigate" and strict_version:
            with open(cli_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # 检测FortiOS版本
            version_detected, version_str, major, minor, patch = detect_fortigate_version(content)
            
            # 如果启用严格版本检测且无法检测到版本，直接返回错误
            if not version_detected:
                log(_("error.unable_to_detect_version"), "error")
                user_log(_("error.unable_to_detect_version"), "error")
                return False, _("error.unable_to_detect_version"), []
        
        # 根据厂商获取对应的验证函数
        verify_func_name = SUPPORTED_VENDORS[vendor]
        
        # 对于FortiGate，将最低版本要求和设备型号传递给验证函数
        if vendor == "fortigate":
            verify_result = globals()[verify_func_name](cli_file, min_version_parts, device_model)
        else:
            verify_result = globals()[verify_func_name](cli_file)
        
        # 对于旧版本函数不返回警告的情况做兼容处理
        if len(verify_result) == 2:
            is_valid, error_message = verify_result
            warnings = []
        else:
            is_valid, error_message, warnings = verify_result
            
        return is_valid, error_message, warnings
    
    except Exception as e:
        error_msg = _("error.verification_failed", error=str(e))
        log(error_msg, "error")
        user_log(_("error.verification_failed"), "error")
        return False, _("error.verification_failed", error=str(e)), []

def detect_circular_references(addrgrp_members):
    """
    检测地址组之间的循环引用
    
    Args:
        addrgrp_members (dict): 地址组成员映射 {组名: [成员列表]}
        
    Returns:
        list: 包含循环引用的组列表
    """
    # 存储已访问的节点
    visited = set()
    # 存储当前路径中的节点
    path = set()
    # 存储发现的循环引用
    cycles = []
    
    def dfs(group, current_path=None):
        if current_path is None:
            current_path = []
        
        # 如果已经访问过该节点，则跳过
        if group in visited:
            return
        
        # 如果当前节点在路径中，则发现循环
        if group in path:
            cycle_start = current_path.index(group)
            cycle = current_path[cycle_start:]
            cycle.append(group)  # 完成循环
            cycles.append(cycle)
            return
        
        # 将当前节点添加到路径中
        path.add(group)
        current_path.append(group)
        
        # 递归访问所有成员
        if group in addrgrp_members:
            for member in addrgrp_members[group]:
                if member in addrgrp_members:  # 只检查是组的成员
                    dfs(member, current_path.copy())
        
        # 回溯，将当前节点从路径中移除
        path.remove(group)
        # 标记为已访问
        visited.add(group)
    
    # 对每个地址组执行DFS
    for group in addrgrp_members:
        if group not in visited:
            dfs(group)
    
    return cycles

def verify_fortigate_semantics(content, config_file_name=None):
    """验证飞塔配置文件的语义有效性

    Args:
        content (str): 配置文件内容
        config_file_name (str): 配置文件名，用于报告生成

    Returns:
        tuple: (is_valid, error_message, warnings, report_generator)
    """
    # 检查是否启用语义验证
    from engine.infrastructure.config.settings import VALIDATION_SETTINGS
    if not VALIDATION_SETTINGS.get('semantic_validation_enabled', False):
        # 语义验证已禁用，返回成功状态
        from engine.utils.logger import log
        log("info.semantic_validation_disabled", level='info')
        from engine.utils.semantic_report import SemanticReportGenerator
        return True, None, [], SemanticReportGenerator(config_file_name)

    from engine.utils.semantic_report import SemanticReportGenerator

    warnings = []
    report_generator = SemanticReportGenerator(config_file_name)

    try:
        # 1. 检查VDOM引用
        vdom_names = set()
        vdom_pattern = r'edit\s+"([^"]+)"'
        vdom_section = re.search(r'config\s+system\s+vdom(.*?)end', content, re.DOTALL)
        
        if vdom_section:
            vdom_matches = re.finditer(vdom_pattern, vdom_section.group(1))
            for match in vdom_matches:
                vdom_names.add(match.group(1))
            
            # 如果不是单VDOM配置，则应该有root
            if vdom_names and "root" not in vdom_names:
                warnings.append(_("warning.missing_root_vdom"))
        
        # 2. 接口引用检查
        interface_names = set()
        interface_pattern = r'edit\s+"([^"]+)"'
        interface_section = re.search(r'config\s+system\s+interface(.*?)end', content, re.DOTALL)
        
        if interface_section:
            interface_matches = re.finditer(interface_pattern, interface_section.group(1))
            for match in interface_matches:
                interface_names.add(match.group(1))
        
        # 3. 检查策略中的接口引用
        policy_interface_pattern = r'edit\s+(\d+).*?set\s+(src|dst)intf\s+"([^"]+)"'
        policy_section_for_intf = re.search(r'config\s+firewall\s+policy(.*?)end', content, re.DOTALL)

        if policy_section_for_intf:
            interface_ref_matches = re.finditer(policy_interface_pattern, policy_section_for_intf.group(1), re.DOTALL)

            for match in interface_ref_matches:
                policy_id = match.group(1)
                intf_type = match.group(2)
                interface = match.group(3)

                if interface != "any" and interface not in interface_names and not interface.startswith("port"):
                    warning_msg = _("warning.reference_to_undefined_interface_detailed",
                                   policy_id=policy_id, intf_type=intf_type, interface=interface)
                    warnings.append(warning_msg)
                    report_generator.add_interface_warning(policy_id, intf_type, interface)
        
        # 4. 地址对象引用检查
        address_names = set()
        address_pattern = r'edit\s+"([^"]+)"'
        address_section = re.search(r'config\s+firewall\s+address(.*?)end', content, re.DOTALL)

        if address_section:
            address_matches = re.finditer(address_pattern, address_section.group(1))
            for match in address_matches:
                address_names.add(match.group(1))
        
        # 检查地址组引用
        addrgrp_pattern = r'edit\s+"([^"]+)"'
        addrgrp_section = re.search(r'config\s+firewall\s+addrgrp(.*?)end', content, re.DOTALL)
        addrgrp_names = set()
        addrgrp_member_warnings = []
        addrgrp_members = {}  # 添加地址组成员映射
        
        if addrgrp_section:
            addrgrp_matches = re.finditer(addrgrp_pattern, addrgrp_section.group(1))
            for match in addrgrp_matches:
                group_name = match.group(1)
                addrgrp_names.add(group_name)
                addrgrp_members[group_name] = []  # 初始化成员列表
            
            # 使用正则表达式查找每个地址组的成员
            group_member_pattern = r'edit\s+"([^"]+)".*?set\s+member\s+(.*?)(?=next|end)'
            group_member_matches = re.finditer(group_member_pattern, addrgrp_section.group(1), re.DOTALL)
            
            for match in group_member_matches:
                group_name = match.group(1)
                member_line = match.group(2).strip()
                # 提取成员名称，处理引号
                members = re.findall(r'"([^"]+)"', member_line)
                addrgrp_members[group_name] = members
            
            # 检查地址组成员引用
            member_pattern = r'set\s+member\s+"([^"]+)"'
            member_matches = re.finditer(member_pattern, addrgrp_section.group(1))
            
            for match in member_matches:
                members = match.group(1).split('" "')
                for member in members:
                    if member not in address_names and member not in addrgrp_names:
                        addrgrp_member_warnings.append(_("warning.addrgrp_references_undefined_address", 
                                                         member=member))
            
            # 检测循环引用
            cycles = detect_circular_references(addrgrp_members)
            if cycles:
                for cycle in cycles:
                    cycle_str = " -> ".join(cycle)
                    warnings.append(_("warning.circular_reference_detected", cycle=cycle_str))
        
        if addrgrp_member_warnings:
            warnings.extend(addrgrp_member_warnings)
        
        # 5. 检查策略中的地址对象引用
        policy_address_pattern = r'edit\s+(\d+).*?set\s+(src|dst)addr\s+"([^"]+)"'
        policy_section = re.search(r'config\s+firewall\s+policy(.*?)end', content, re.DOTALL)

        if policy_section:
            address_ref_matches = re.finditer(policy_address_pattern, policy_section.group(1), re.DOTALL)

            for match in address_ref_matches:
                policy_id = match.group(1)
                addr_type = match.group(2)
                address = match.group(3)

                if (address != "all" and address != "any" and
                    address not in address_names and address not in addrgrp_names):
                    warning_msg = _("warning.reference_to_undefined_address_detailed",
                                   policy_id=policy_id, addr_type=addr_type, address=address)
                    warnings.append(warning_msg)
                    report_generator.add_address_warning(policy_id, addr_type, address)
        
        # 6. 服务对象引用检查
        service_names = set()
        service_pattern = r'edit\s+"([^"]+)"'
        service_section = re.search(r'config\s+firewall\s+service\s+custom(.*?)end', content, re.DOTALL)
        
        if service_section:
            service_matches = re.finditer(service_pattern, service_section.group(1))
            for match in service_matches:
                service_names.add(match.group(1))
        
        # 服务组引用检查
        service_group_names = set()
        service_group_pattern = r'edit\s+"([^"]+)"'
        service_group_section = re.search(r'config\s+firewall\s+service\s+group(.*?)end', content, re.DOTALL)
        service_group_member_warnings = []
        
        if service_group_section:
            service_group_matches = re.finditer(service_group_pattern, service_group_section.group(1))
            for match in service_group_matches:
                service_group_names.add(match.group(1))
            
            # 检查服务组成员引用
            member_pattern = r'set\s+member\s+"([^"]+)"'
            member_matches = re.finditer(member_pattern, service_group_section.group(1))
            
            for match in member_matches:
                members = match.group(1).split('" "')
                for member in members:
                    if member not in service_names and member not in service_group_names:
                        service_group_member_warnings.append(
                            _("warning.service_group_references_undefined_service", member=member)
                        )
        
        if service_group_member_warnings:
            warnings.extend(service_group_member_warnings)
        
        # 7. 检查策略中的服务引用
        policy_service_pattern = r'edit\s+(\d+).*?set\s+service\s+"([^"]+)"'

        if policy_section:
            service_ref_matches = re.finditer(policy_service_pattern, policy_section.group(1), re.DOTALL)

            for match in service_ref_matches:
                policy_id = match.group(1)
                service = match.group(2)

                if (service != "ALL" and service != "ANY" and
                    service not in service_names and service not in service_group_names):
                    warning_msg = _("warning.reference_to_undefined_service_detailed",
                                   policy_id=policy_id, service=service)
                    warnings.append(warning_msg)
                    report_generator.add_service_warning(policy_id, service)
        
        # 8. 检查路由表是否存在，但条目为空
        route_pattern = r'config\s+router\s+static(.*?)end'
        route_section = re.search(route_pattern, content, re.DOTALL)
        
        if route_section and 'edit' not in route_section.group(1):
            warnings.append(_("warning.empty_static_route_table"))
        
        # 9. 检查DNS设置
        dns_pattern = r'config\s+system\s+dns(.*?)end'
        dns_section = re.search(dns_pattern, content, re.DOTALL)

        if not dns_section:
            warning_msg = _("warning.missing_dns_config_section")
            warnings.append(warning_msg)
            report_generator.add_dns_warning("missing_config_section")
        elif 'set primary' not in dns_section.group(1):
            # 检查是否有其他DNS配置
            dns_content = dns_section.group(1)
            if 'set server' in dns_content:
                warning_msg = _("warning.dns_using_server_instead_of_primary")
                warnings.append(warning_msg)
                report_generator.add_dns_warning("using_server_instead_of_primary")
            else:
                warning_msg = _("warning.dns_missing_primary_server")
                warnings.append(warning_msg)
                report_generator.add_dns_warning("missing_primary_server")
        
        # 10. 检查策略中缺少日志设置
        policy_pattern = r'edit\s+(\d+)(.*?)next'

        if policy_section:
            policy_matches = re.finditer(policy_pattern, policy_section.group(1), re.DOTALL)
            policies_without_log = []

            for match in policy_matches:
                policy_id = match.group(1)
                policy_content = match.group(2)

                if ('set action accept' in policy_content and
                    'set logtraffic' not in policy_content):
                    policies_without_log.append(policy_id)

            # 如果有策略缺少日志设置，提供详细信息
            if policies_without_log:
                if len(policies_without_log) <= 5:
                    # 如果策略数量不多，列出所有策略ID
                    policy_list = ", ".join(policies_without_log)
                    warning_msg = _("warning.policies_missing_log_setting_detailed",
                                   count=len(policies_without_log), policies=policy_list)
                else:
                    # 如果策略很多，只显示前几个和总数
                    policy_list = ", ".join(policies_without_log[:5])
                    warning_msg = _("warning.policies_missing_log_setting_summary",
                                   count=len(policies_without_log), sample_policies=policy_list)

                warnings.append(warning_msg)
                report_generator.add_policy_log_warnings(policies_without_log)

        return True, "", warnings, report_generator

    except Exception as e:
        error_msg = _("error.semantic_verification_failed", error=str(e))
        log(error_msg, "error")
        return False, _("error.semantic_verification_failed", error=str(e)), warnings, report_generator

def detect_fortigate_version(content):
    """
    从飞塔配置文件中检测FortiOS版本
    
    Args:
        content (str): 配置文件内容
        
    Returns:
        tuple: (是否检测到版本, 版本字符串, 主版本号, 次版本号, 补丁版本号)
    """
    # 方法1: 从config-version行检测版本
    version_match = re.search(r'#config-version=\w+-(\d+\.\d+\.\d+)', content)
    if version_match:
        version_str = version_match.group(1)
        try:
            version_parts = [int(part) for part in version_str.split('.')]
            if len(version_parts) >= 3:
                return True, version_str, version_parts[0], version_parts[1], version_parts[2]
        except ValueError:
            pass
    
    # 方法2: 从buildno行检测主要版本
    buildno_match = re.search(r'#buildno=(\d+)', content)
    if buildno_match:
        build_number = int(buildno_match.group(1))
        # 根据build号估计版本范围
        if build_number >= 6000:
            # 6.0.0以上的版本
            if build_number >= 7000:
                # 估计为7.0以上版本
                return True, f"~7.x.x (build {build_number})", 7, 0, 0
            return True, f"~6.x.x (build {build_number})", 6, 0, 0
        else:
            # 低于6.0.0的版本
            return True, f"<6.0.0 (build {build_number})", 5, 0, 0
    
    # 方法3: 尝试从system global设置中找到版本信息
    global_section = re.search(r'config system global(.*?)end', content, re.DOTALL)
    if global_section:
        version_comment = re.search(r'set\s+.+\s+# FortiOS (\d+\.\d+\.\d+)', global_section.group(1))
        if version_comment:
            version_str = version_comment.group(1)
            try:
                version_parts = [int(part) for part in version_str.split('.')]
                if len(version_parts) >= 3:
                    return True, version_str, version_parts[0], version_parts[1], version_parts[2]
            except ValueError:
                pass
    
    # 方法4: 从特定功能特征推断版本
    # 根据配置特性检测版本（按照版本从新到旧排序）
    
    # FortiOS 7.4特性
    if "config system auto-script" in content or "set ai-mac " in content:
        return True, "≥7.4.0 (based on features)", 7, 4, 0
    
    # FortiOS 7.2特性
    if "config firewall policy-ddns" in content or "config user vendor-important-device" in content:
        return True, "≥7.2.0 (based on features)", 7, 2, 0
    
    # FortiOS 7.0特性
    if "config system fabric-vpn" in content or "set fabric-object" in content:
        return True, "≥7.0.0 (based on features)", 7, 0, 0
    
    # FortiOS 6.4特性
    if "config system dns-database" in content or "set ssl-min-proto-version" in content:
        return True, "≥6.4.0 (based on features)", 6, 4, 0
    
    # FortiOS 6.2特性
    if "config user radius dynamic-mapping" in content or "set fabric-device" in content:
        return True, "≥6.2.0 (based on features)", 6, 2, 0
    
    # FortiOS 6.0特性
    if "config system sdwan" in content or "set sdwan " in content:
        # SD-WAN是6.0.1引入的
        return True, "≥6.0.1 (based on SD-WAN feature)", 6, 0, 1
    
    if "set password-policy " in content or "config system password-policy" in content:
        # 密码策略在6.0中增强
        return True, "≥6.0.0 (based on password policy)", 6, 0, 0
    
    # FortiOS 5.6特性
    if "config vpn ssl settings" in content and "set crypto-algorithm " in content:
        return True, "≥5.6.0 (based on SSL VPN crypto settings)", 5, 6, 0
    
    # FortiOS 5.4特性
    if "config system virtual-wan-link" in content:
        # 早期SD-WAN的前身
        return True, "≥5.4.0 (based on virtual-wan-link)", 5, 4, 0
    
    # 通过基础特性推断是否低于6.0版本
    if "config firewall" in content and "config router" in content and "config vpn" in content:
        # 基本配置区块存在，但没有6.0+特性
        # 我们可以假设这是5.x版本
        return True, "<6.0.0 (estimated by absence of 6.0+ features)", 5, 0, 0
    
    # 无法可靠检测版本
    return False, "unknown", 0, 0, 0

def verify_fortigate_config(cli_file, min_version_parts=None, device_model=None):
    """验证飞塔配置文件格式

    Args:
        cli_file (str): 配置文件路径
        min_version_parts (list): 最低版本要求 [major, minor, patch]
        device_model (str): 目标设备型号，用于容量限制校验

    Returns:
        tuple: (is_valid, error_message, warnings)
    """
    try:
        log(_("info.verify_fortigate_config"), file=cli_file)
        warnings = []
        
        # 读取文件
        with open(cli_file, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # 语法验证部分
        
        # 1. 基础检查：关键字存在性检查
        if "config system" not in content and "config firewall" not in content:
            log(_("error.invalid_fortigate_config"), "warning")
            user_log(_("error.invalid_fortigate_config"), "warning")
            return False, _("error.invalid_fortigate_config"), []
        
        # 2. 配置版本检查
        version_match = re.search(r'#config-version=([^-]+)-', content)
        if not version_match:
            log(_("error.missing_config_version"), "warning")
            user_log(_("error.missing_config_version"), "warning")
            warnings.append(_("warning.missing_config_version_tag"))
        
        # 3. 检查飞塔版本号
        version_detected, version_str, major, minor, patch = detect_fortigate_version(content)
        
        # 设置默认最低版本要求为6.0.0
        if min_version_parts is None:
            min_version_parts = [6, 0, 0]
        
        min_major, min_minor, min_patch = min_version_parts[0], min_version_parts[1], min_version_parts[2]
        min_version_str = f"{min_major}.{min_minor}.{min_patch}"
        
        if version_detected:
            log(_("info.detected_fortigate_version"), version=version_str)
            
            # 检查版本是否符合最低要求
            if major < min_major:
                # 主版本低于要求
                log(_("error.unsupported_fortigate_version"), "error", 
                    version=version_str, min_version=min_version_str)
                user_log(_("error.unsupported_fortigate_version"), "error", 
                         version=version_str, min_version=min_version_str)
                return False, _("error.unsupported_fortigate_version", 
                               version=version_str, min_version=min_version_str), warnings
            elif major == min_major:
                if minor < min_minor:
                    # 次版本低于要求
                    log(_("error.unsupported_fortigate_version"), "error", 
                        version=version_str, min_version=min_version_str)
                    return False, _("error.unsupported_fortigate_version", 
                                   version=version_str, min_version=min_version_str), warnings
                elif minor == min_minor and patch < min_patch:
                    # 补丁版本低于要求
                    log(_("error.unsupported_fortigate_version"), "error", 
                        version=version_str, min_version=min_version_str)
                    return False, _("error.unsupported_fortigate_version", 
                                   version=version_str, min_version=min_version_str), warnings
                elif minor == min_minor and patch == min_patch:
                    # 版本刚好是最低要求，作为警告提示
                    warnings.append(_("warning.minimum_fortigate_version", version=version_str))
        else:
            # 无法检测版本，直接返回错误
            log(_("error.unable_to_detect_version"), "error")
            user_log(_("error.unable_to_detect_version"), "error")
            return False, _("error.unable_to_detect_version"), []
        
        # 4. 配置块嵌套与闭合检查
        config_blocks = []
        edit_blocks = []
        line_number = 0
        unbalanced_line = 0
        
        for line in content.split('\n'):
            line_number += 1
            line = line.strip()
            
            if line.startswith('config '):
                config_blocks.append((line, line_number))
            elif line.startswith('edit '):
                if not config_blocks:
                    log(_("error.edit_without_config"), "error", line=line_number)
                    return False, _("error.edit_without_config", line=line_number), []
                edit_blocks.append((line, line_number))
            elif line == 'next':
                if not edit_blocks:
                    log(_("error.next_without_edit"), "error", line=line_number)
                    return False, _("error.next_without_edit", line=line_number), []
                edit_blocks.pop()
            elif line == 'end':
                if not config_blocks:
                    log(_("error.end_without_config"), "error", line=line_number)
                    return False, _("error.end_without_config", line=line_number), []
                config_blocks.pop()
            
            if unbalanced_line == 0 and (len(config_blocks) > 10 or len(edit_blocks) > 15):
                # 嵌套过深可能表示配置块未正确闭合
                unbalanced_line = line_number
        
        # 检查是否有未闭合的配置块
        if config_blocks:
            log(_("error.unclosed_config_blocks"), "error", blocks=len(config_blocks), line=config_blocks[0][1])
            return False, _("error.unclosed_config_blocks", blocks=len(config_blocks), line=config_blocks[0][1]), []
        
        if edit_blocks:
            log(_("error.unclosed_edit_blocks"), "error", blocks=len(edit_blocks), line=edit_blocks[0][1])
            return False, _("error.unclosed_edit_blocks", blocks=len(edit_blocks), line=edit_blocks[0][1]), []
        
        if unbalanced_line > 0:
            log(_("error.possible_unbalanced_nesting"), "warning", line=unbalanced_line)
            warnings.append(_("warning.possible_unbalanced_nesting", line=unbalanced_line))
        
        # 1. 地址对象格式检查
        ip_pattern = r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}'
        subnet_pattern = r'set subnet\s+(' + ip_pattern + r')\s+(' + ip_pattern + r')'
        subnet_matches = re.finditer(subnet_pattern, content)
        
        for match in subnet_matches:
            ip, mask = match.groups()
            # 简单的IP地址格式验证
            ip_parts = ip.split('.')
            if any(int(part) > 255 for part in ip_parts):
                log(_("error.invalid_ip_address"), "error", ip=ip)
                return False, _("error.invalid_ip_address", ip=ip), warnings
            
            # 子网掩码格式验证
            mask_parts = mask.split('.')
            if any(int(part) > 255 for part in mask_parts):
                log(_("error.invalid_subnet_mask"), "error", mask=mask)
                return False, _("error.invalid_subnet_mask", mask=mask), warnings
        
        # 4. 检查重复的策略ID
        policy_ids = {}
        policy_id_pattern = r'edit\s+(\d+)'
        policy_section = re.search(r'config\s+firewall\s+policy(.*?)end', content, re.DOTALL)
        
        if policy_section:
            policy_matches = re.finditer(policy_id_pattern, policy_section.group(1))
            line_number = policy_section.group(0).count('\n', 0, policy_section.start()) + 1
            
            for match in policy_matches:
                policy_id = match.group(1)
                if policy_id in policy_ids:
                    log(_("error.duplicate_policy_id"), "error", id=policy_id, line1=policy_ids[policy_id], line2=line_number)
                    return False, _("error.duplicate_policy_id", id=policy_id), warnings
                policy_ids[policy_id] = line_number
                line_number += match.group(0).count('\n') + 1
        
        # 6. 检查配置语法错误
        syntax_errors = []

        # 定义多行内容类型和模式
        MULTILINE_CONTENT_PATTERNS = {
            'buffer': {
                'start': r'set\s+buffer\s+"',
                'end': r'"$',
                'description_key': 'multiline.buffer_content'
            },
            'certificate': {
                'start': r'set\s+certificate\s+"',
                'end': r'-----END CERTIFICATE-----"$',
                'description_key': 'multiline.certificate_content'
            },
            'private_key': {
                'start': r'set\s+private-key\s+"',
                'end': r'-----END (?:PRIVATE KEY|RSA PRIVATE KEY)-----"$',
                'description_key': 'multiline.private_key_content'
            }
        }

        # 跟踪多行内容状态
        multiline_state = {
            'active': False,
            'type': None,
            'start_line': 0
        }

        for line_num, line in enumerate(content.split('\n'), 1):
            line = line.strip()

            # 检查是否进入多行内容
            if not multiline_state['active']:
                for content_type, pattern in MULTILINE_CONTENT_PATTERNS.items():
                    if re.search(pattern['start'], line):
                        multiline_state['active'] = True
                        multiline_state['type'] = content_type
                        multiline_state['start_line'] = line_num
                        log(_("debug.multiline_content_start"), "debug",
                            type=_(pattern['description_key']), line=line_num)
                        break

            # 如果在多行内容中，检查是否结束
            if multiline_state['active']:
                content_type = multiline_state['type']
                end_pattern = MULTILINE_CONTENT_PATTERNS[content_type]['end']

                # 修复逻辑运算符优先级问题
                if re.search(end_pattern, line):
                    log(_("debug.multiline_content_end"), "debug",
                        type=_(MULTILINE_CONTENT_PATTERNS[content_type]['description_key']),
                        line=line_num)
                    multiline_state['active'] = False
                    multiline_state['type'] = None
                    multiline_state['start_line'] = 0
                    continue

                # 在多行内容中，跳过语法检查
                continue

            # 检查常见语法错误，如缺少引号（但排除多行内容的开始行）
            if re.search(r'set\s+\w+\s+"[^"]*$', line):
                # 检查是否是已知的多行内容开始
                is_multiline_start = False
                for pattern in MULTILINE_CONTENT_PATTERNS.values():
                    if re.search(pattern['start'], line):
                        is_multiline_start = True
                        break

                if not is_multiline_start:
                    syntax_errors.append((line_num, line))

            # 检查错误的命令格式
            elif re.search(r'^set\s*$', line) or re.search(r'^config\s*$', line) or re.search(r'^edit\s*$', line):
                syntax_errors.append((line_num, line))
        
        if syntax_errors:
            error_lines = ", ".join([f"{line_num}" for line_num, _ in syntax_errors[:3]])
            log(_("error.syntax_errors_detected"), "error", lines=error_lines)
            user_log(_("error.syntax_errors_detected"), "error", lines=error_lines)
            return False, _("error.syntax_errors_detected", lines=error_lines), warnings
        
        # 检查是否启用语义验证
        from engine.infrastructure.config.settings import VALIDATION_SETTINGS
        semantic_validation_enabled = VALIDATION_SETTINGS.get('semantic_validation_enabled', False)

        semantic_warnings = []
        if semantic_validation_enabled:
            # 进行语义验证
            config_file_name = os.path.basename(cli_file) if cli_file else "unknown_config"
            is_semantics_valid, semantics_error, semantic_warnings, report_generator = verify_fortigate_semantics(content, config_file_name)

            # 生成语义验证报告
            if semantic_warnings:
                # 输出简洁汇总到控制台
                console_summary = report_generator.generate_console_summary()
                log(console_summary, "info")

                # 生成详细报告文件
                try:
                    report_path = report_generator.generate_detailed_report()
                    if report_path:
                        log(_("semantic.report_generated"), "info", path=report_path)
                except Exception as e:
                    error_msg = _("error.report_generation_failed", error=str(e))
                    log(error_msg, "warning")

            if not is_semantics_valid:
                error_msg = _("error.semantic_verification_failed", error=semantics_error)
                log(error_msg, "error")
                user_log(_("error.semantic_verification_failed"), "error")
                return False, semantics_error, warnings + semantic_warnings
        else:
            # 语义验证已禁用，跳过
            log(_("info.semantic_validation_disabled"), "debug")

        # 记录警告信息（仅在启用语义验证时）
        if semantic_validation_enabled and semantic_warnings:
            warnings.extend(semantic_warnings)
            for warning in semantic_warnings:
                log(_("warning.semantic_verification"), "warning", warning=warning)
                user_log(_("warning.semantic_verification"), "warning", warning=warning)

        # 容量限制校验（如果指定了设备型号）
        if device_model:
            try:
                capacity_violations = perform_capacity_validation(content, device_model)
                if capacity_violations:
                    # 将容量违规转换为警告
                    for violation in capacity_violations:
                        warning_msg = _("capacity.violation_warning",
                                      resource=violation.description,
                                      current=violation.current_count,
                                      limit=violation.max_limit,
                                      suggestion=violation.suggestion)
                        warnings.append(warning_msg)
                        log(_("warning.capacity_violation"), "warning",
                            resource=violation.resource_type,
                            current=violation.current_count,
                            limit=violation.max_limit)

                    log(_("info.capacity_validation_completed"), "info",
                        violations=len(capacity_violations), device_model=device_model)
                else:
                    log(_("info.capacity_validation_passed"), "info", device_model=device_model)
            except Exception as e:
                # 容量校验失败不应该影响整体验证结果，只记录警告
                warning_msg = _("warning.capacity_validation_failed", error=str(e))
                warnings.append(warning_msg)
                log(_("warning.capacity_validation_failed"), "warning", error=str(e))

        # 验证通过
        log(_("info.verification_success"))
        return True, "", warnings
    
    except Exception as e:
        error_msg = _("error.verification_failed", error=str(e))
        log(error_msg, "error")
        return False, _("error.verification_failed", error=str(e)), []

def format_verification_result(is_valid, message, warnings=None, detected_version=None):
    """格式化验证结果为JSON字符串
    
    Args:
        is_valid (bool): 验证是否通过
        message (str): 错误信息
        warnings (list): 警告信息列表
        detected_version (str): 检测到的版本信息
        
    Returns:
        str: JSON格式的结果字符串
    """
    if warnings is None:
        warnings = []
        
    result = {
        "valid": is_valid,
        "message": message,
        "success": is_valid,  # 添加success字段表示业务结果
        "error": "" if is_valid else message,  # 添加error字段，验证成功时为空
        "warnings": warnings,  # 添加warnings字段，包含所有警告信息
    }
    
    # 如果检测到版本，添加到结果中
    if detected_version:
        result["detected_version"] = detected_version
    elif not is_valid and "version" in message.lower():
        # 如果是因为版本问题验证失败，添加一个特定的标记
        result["version_error"] = True
        
    # 移除可能包含敏感信息的字段
    if "internal_details" in result:
        del result["internal_details"]
    if "debug_info" in result:
        del result["debug_info"]
    
    return json.dumps(result, ensure_ascii=False, indent=2)

# 命令行入口
if __name__ == "__main__":
    import argparse
    import sys
    import json
    
    try:
        # 获取支持的厂商列表
        supported_vendors = list(SUPPORTED_VENDORS.keys())
        
        # 初始化国际化
        from engine.utils.i18n import init_i18n, _
        init_i18n('zh-CN')  # 默认使用中文，后续会根据参数更新
        
        parser = argparse.ArgumentParser(description=_("description.config_verification_tool"))
        parser.add_argument('--cli', required=True, help=_("help.cli_file"))
        parser.add_argument('--vendor', default="fortigate", help=_("help.supported_vendors", vendors=", ".join(supported_vendors)))
        parser.add_argument('--print', action='store_true', help=_("help.print_result"))
        parser.add_argument('--lang', default='zh-CN', help=_("help.language"))
        parser.add_argument('--log-dir', default='', help=_("help.log_dir"))
        parser.add_argument('--quiet', action='store_true', help=_("help.quiet_mode"))
        
        args = parser.parse_args()
        
        # 默认启用严格版本检测
        args.strict_version = True
        
        # 更新国际化设置
        init_i18n(args.lang)
        
        # 设置日志目录
        if args.log_dir:
            # 确保日志目录存在
            os.makedirs(args.log_dir, exist_ok=True)
            # 设置日志
            from engine.utils.logger import setup_logging
            setup_logging(args.log_dir, True, logging.DEBUG, args.quiet)
        
        # 检查厂商是否支持
        if args.vendor.lower() not in SUPPORTED_VENDORS:
            supported = list(SUPPORTED_VENDORS.keys())
            error_msg = _("error.vendor_not_supported", vendor=args.vendor, supported=", ".join(supported))
            log(_("error.vendor_not_supported"), "error", vendor=args.vendor, supported=", ".join(supported))
            # 为确保Go程序可以正确处理，返回JSON格式
            error_result = {
                "valid": False,
                "success": False,
                "message": error_msg,
                "error": error_msg,
                "warnings": []
            }
            # 输出JSON结果
            print(json.dumps(error_result, ensure_ascii=False))
            sys.exit(0)  # 修改: 总是返回0状态码，即使是参数错误
        
        is_valid, message, warnings = verify_config_file(args.cli, args.vendor, args.lang, args.strict_version, args.quiet)
        
        # 检测版本信息（仅针对FortiGate）
        detected_version = None
        if args.vendor.lower() == "fortigate":
            try:
                with open(args.cli, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    version_detected, version_str, _, _, _ = detect_fortigate_version(content)
                    if version_detected:
                        detected_version = version_str
            except Exception:
                pass
        
        # 构造验证结果
        result = {
            "valid": is_valid,
            "message": message,
            "success": is_valid,
            "error": "" if is_valid else message,
            "warnings": warnings,
        }
        
        if detected_version:
            result["detected_version"] = detected_version
        
        # 输出JSON结果
        print(json.dumps(result, ensure_ascii=False))
        
        # 无论验证成功或失败，都以正常状态退出
        # 因为验证结果已经在JSON中包含了
        sys.exit(0)
    except Exception as e:
        # 全局异常处理
        error_result = {
            "valid": False,
            "success": False,
            "message": f"验证配置失败: {str(e)}",
            "error": str(e),
            "warnings": []
        }
        
        # 输出JSON格式的错误信息
        print(json.dumps(error_result, ensure_ascii=False))
        
        # 始终返回状态码0
        sys.exit(0)


def perform_capacity_validation(config_content, device_model):
    """执行容量限制校验

    Args:
        config_content (str): FortiGate配置文件内容
        device_model (str): 目标设备型号

    Returns:
        list: 容量违规列表，如果没有违规则返回空列表
    """
    try:
        # 加载设备容量限制配置
        capacity_limits = load_capacity_limits()
        if not capacity_limits or device_model not in capacity_limits.get("device_models", {}):
            log(_("warning.unsupported_device_model"), "warning", device_model=device_model)
            return []

        device_limits = capacity_limits["device_models"][device_model]["limits"]
        violations = []

        # 统计各类资源数量
        resource_counts = count_resources(config_content)

        # 检查每种资源是否超限
        for resource_type, current_count in resource_counts.items():
            if resource_type in device_limits:
                limit_info = device_limits[resource_type]
                max_limit = limit_info["max"]

                if current_count > max_limit:
                    # 处理国际化的描述和建议文本
                    description_key = limit_info.get("description_key")
                    suggestion_key = limit_info.get("suggestion_key")

                    description = _(description_key) if description_key else limit_info.get("description", resource_type)
                    suggestion = _(suggestion_key) if suggestion_key else limit_info.get("suggestion", "")

                    violation = CapacityViolation(
                        resource_type=resource_type,
                        current_count=current_count,
                        max_limit=max_limit,
                        description=description,
                        severity=limit_info.get("severity", "error"),
                        suggestion=suggestion,
                        category=limit_info.get("category", "general"),
                        device_model=device_model
                    )
                    violations.append(violation)

        # 生成详细报告（用于调试和日志记录）
        if violations:
            reporter = CapacityReporter(device_model, violations, resource_counts)
            detailed_report = reporter.generate_detailed_report()

            # 记录详细的容量校验信息
            log(_("info.capacity_validation_detailed_report"), "info",
                device_model=device_model,
                total_violations=len(violations),
                compliance_rate=detailed_report["summary"]["compliance_rate"])

            # 记录每个违规项的详细信息
            for violation in violations:
                log(_("warning.capacity_violation_detailed"), "warning",
                    resource=violation.description,
                    current=violation.current_count,
                    limit=violation.max_limit,
                    usage_rate=f"{violation.usage_rate * 100:.1f}%",
                    risk_level=violation.risk_level,
                    category=violation.category)

        return violations

    except Exception as e:
        log(_("error.capacity_validation_error"), "error", error=str(e))
        raise


def load_capacity_limits():
    """加载设备容量限制配置"""
    try:
        # 修复路径计算问题
        config_file = os.path.join(os.path.dirname(__file__), "data", "capacity_limits.json")
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            log(_("warning.capacity_config_not_found"), "warning", file=config_file)
            return None
    except Exception as e:
        log(_("error.load_capacity_config"), "error", error=str(e))
        return None


def count_resources(config_content):
    """统计FortiGate配置中的各类资源数量"""
    resource_counts = {}

    try:
        # DNS服务器数量
        resource_counts["dns_servers"] = count_dns_servers(config_content)

        # 子接口数量
        resource_counts["sub_interfaces"] = count_sub_interfaces(config_content)

        # PPPOE会话数量
        resource_counts["pppoe_sessions"] = count_pppoe_sessions(config_content)

        # 静态路由数量
        resource_counts["static_routes_v4"] = count_static_routes(config_content)

        # NAT策略数量
        resource_counts["nat44_policies"] = count_nat_policies(config_content)

        # 安全策略数量
        resource_counts["security_policies"] = count_security_policies(config_content)

        # 地址对象数量
        resource_counts["address_objects"] = count_address_objects(config_content)

        # 地址组数量
        resource_counts["address_groups"] = count_address_groups(config_content)

        # 服务对象数量
        resource_counts["service_objects"] = count_service_objects(config_content)

        # 服务组数量
        resource_counts["service_groups"] = count_service_groups(config_content)

        # 时间对象数量
        resource_counts["time_objects"] = count_time_objects(config_content)

    except Exception as e:
        log(_("error.count_resources"), "error", error=str(e))

    return resource_counts


class CapacityViolation:
    """容量违规信息类 - 增强版本，支持详细报告和分析"""

    def __init__(self, resource_type, current_count, max_limit, description,
                 severity="error", suggestion="", category="general", device_model=""):
        self.resource_type = resource_type
        self.current_count = current_count
        self.max_limit = max_limit
        self.violation_count = current_count - max_limit
        self.description = description
        self.severity = severity
        self.suggestion = suggestion
        self.category = category
        self.device_model = device_model

        # 计算使用率和风险级别
        self.usage_rate = (current_count / max_limit) if max_limit > 0 else 0
        self.risk_level = self._calculate_risk_level()

        # 生成详细分析
        self.analysis = self._generate_analysis()

    def _calculate_risk_level(self):
        """计算风险级别"""
        if self.usage_rate >= 1.0:
            return "critical"
        elif self.usage_rate >= 0.95:
            return "high"
        elif self.usage_rate >= 0.8:
            return "medium"
        else:
            return "low"

    def _generate_analysis(self):
        """生成详细分析"""
        analysis = {
            "usage_percentage": f"{self.usage_rate * 100:.1f}%",
            "excess_count": self.violation_count,
            "recommended_action": self._get_recommended_action(),
            "impact_assessment": self._get_impact_assessment(),
            "optimization_tips": self._get_optimization_tips()
        }
        return analysis

    def _get_recommended_action(self):
        """获取推荐操作"""
        if self.violation_count > self.max_limit * 0.5:
            return "urgent_optimization"
        elif self.violation_count > self.max_limit * 0.2:
            return "moderate_optimization"
        else:
            return "minor_adjustment"

    def _get_impact_assessment(self):
        """获取影响评估"""
        impact_levels = {
            "critical": _("capacity.impact.critical"),
            "high": _("capacity.impact.high"),
            "medium": _("capacity.impact.medium"),
            "low": _("capacity.impact.low")
        }
        return impact_levels.get(self.risk_level, _("capacity.impact.low"))

    def _get_optimization_tips(self):
        """获取优化建议"""
        tips = []

        if self.category == "policy":
            tips.extend([
                _("capacity.tips.policy.merge_similar"),
                _("capacity.tips.policy.remove_unused"),
                _("capacity.tips.policy.use_groups")
            ])
        elif self.category == "object":
            tips.extend([
                _("capacity.tips.object.cleanup_unused"),
                _("capacity.tips.object.merge_duplicates"),
                _("capacity.tips.object.use_groups")
            ])
        elif self.category == "interface":
            tips.extend([
                _("capacity.tips.interface.check_unused"),
                _("capacity.tips.interface.vlan_aggregation"),
                _("capacity.tips.interface.optimize_structure")
            ])
        elif self.category == "network":
            tips.extend([
                _("capacity.tips.network.optimize_config"),
                _("capacity.tips.network.check_necessity"),
                _("capacity.tips.network.efficient_methods")
            ])

        return tips

    def to_dict(self):
        """转换为详细的字典格式"""
        return {
            "resource_type": self.resource_type,
            "description": self.description,
            "current_count": self.current_count,
            "max_limit": self.max_limit,
            "violation_count": self.violation_count,
            "usage_rate": f"{self.usage_rate * 100:.1f}%",
            "severity": self.severity,
            "risk_level": self.risk_level,
            "category": self.category,
            "device_model": self.device_model,
            "suggestion": self.suggestion,
            "analysis": self.analysis,
            "message": _("capacity.violation_message",
                        resource=self.description,
                        current=self.current_count,
                        limit=self.max_limit)
        }

    def to_summary_dict(self):
        """转换为简化的字典格式"""
        return {
            "resource": self.description,
            "current": self.current_count,
            "limit": self.max_limit,
            "excess": self.violation_count,
            "usage": f"{self.usage_rate * 100:.1f}%",
            "severity": self.severity,
            "suggestion": self.suggestion
        }


class CapacityReporter:
    """容量校验报告生成器"""

    def __init__(self, device_model, violations, resource_counts):
        self.device_model = device_model
        self.violations = violations
        self.resource_counts = resource_counts
        self.device_limits = self._load_device_limits()

    def _load_device_limits(self):
        """加载设备限制信息"""
        try:
            capacity_limits = load_capacity_limits()
            if capacity_limits and self.device_model in capacity_limits.get("device_models", {}):
                return capacity_limits["device_models"][self.device_model]["limits"]
        except Exception as e:
            log(_("error.load_capacity_config"), "error", error=str(e))
        return {}

    def generate_summary_report(self):
        """生成摘要报告"""
        total_resources = len(self.resource_counts)
        violation_count = len(self.violations)

        # 按严重级别分类违规
        critical_violations = [v for v in self.violations if v.severity == "error"]
        warning_violations = [v for v in self.violations if v.severity == "warning"]

        # 按类别分类违规
        violations_by_category = {}
        for violation in self.violations:
            category = violation.category
            if category not in violations_by_category:
                violations_by_category[category] = []
            violations_by_category[category].append(violation)

        summary = {
            "device_model": self.device_model,
            "total_resources_checked": total_resources,
            "total_violations": violation_count,
            "critical_violations": len(critical_violations),
            "warning_violations": len(warning_violations),
            "violations_by_category": {
                category: len(violations)
                for category, violations in violations_by_category.items()
            },
            "overall_status": "failed" if critical_violations else "warning" if warning_violations else "passed",
            "compliance_rate": f"{((total_resources - violation_count) / total_resources * 100):.1f}%" if total_resources > 0 else "100%"
        }

        return summary

    def generate_detailed_report(self):
        """生成详细报告"""
        report = {
            "summary": self.generate_summary_report(),
            "violations": [v.to_dict() for v in self.violations],
            "resource_usage": self._generate_resource_usage_report(),
            "recommendations": self._generate_recommendations(),
            "device_info": self._get_device_info()
        }

        return report

    def _generate_resource_usage_report(self):
        """生成资源使用情况报告"""
        usage_report = {}

        for resource_type, current_count in self.resource_counts.items():
            if resource_type in self.device_limits:
                limit_info = self.device_limits[resource_type]
                max_limit = limit_info["max"]
                usage_rate = (current_count / max_limit) if max_limit > 0 else 0

                usage_report[resource_type] = {
                    "description": limit_info.get("description", resource_type),
                    "current_count": current_count,
                    "max_limit": max_limit,
                    "usage_rate": f"{usage_rate * 100:.1f}%",
                    "status": "exceeded" if current_count > max_limit else
                             "warning" if usage_rate >= 0.8 else "normal",
                    "category": limit_info.get("category", "general")
                }

        return usage_report

    def _generate_recommendations(self):
        """生成优化建议"""
        recommendations = []

        # 按类别分组违规
        violations_by_category = {}
        for violation in self.violations:
            category = violation.category
            if category not in violations_by_category:
                violations_by_category[category] = []
            violations_by_category[category].append(violation)

        # 为每个类别生成建议
        for category, violations in violations_by_category.items():
            category_recommendations = {
                "category": category,
                "violation_count": len(violations),
                "priority": "high" if any(v.severity == "error" for v in violations) else "medium",
                "actions": []
            }

            # 收集所有优化建议
            all_tips = set()
            for violation in violations:
                all_tips.update(violation.analysis["optimization_tips"])

            category_recommendations["actions"] = list(all_tips)
            recommendations.append(category_recommendations)

        return recommendations

    def _get_device_info(self):
        """获取设备信息"""
        try:
            capacity_limits = load_capacity_limits()
            if capacity_limits and self.device_model in capacity_limits.get("device_models", {}):
                device_info = capacity_limits["device_models"][self.device_model]
                return {
                    "name": device_info.get("name", self.device_model),
                    "description": device_info.get("description", ""),
                    "vendor": device_info.get("vendor", ""),
                    "category": device_info.get("category", "")
                }
        except Exception:
            pass

        return {
            "name": self.device_model,
            "description": "未知设备型号",
            "vendor": "unknown",
            "category": "unknown"
        }

    def format_console_output(self):
        """格式化控制台输出"""
        lines = []
        lines.append("=" * 60)
        lines.append(_("capacity.report.title", device_model=self.device_model))
        lines.append("=" * 60)

        summary = self.generate_summary_report()
        lines.append(_("capacity.report.total_resources", count=summary['total_resources_checked']))
        lines.append(_("capacity.report.total_violations", count=summary['total_violations']))
        lines.append(_("capacity.report.critical_violations", count=summary['critical_violations']))
        lines.append(_("capacity.report.warning_violations", count=summary['warning_violations']))
        lines.append(_("capacity.report.compliance_rate", rate=summary['compliance_rate']))
        lines.append("")

        if self.violations:
            lines.append(_("capacity.report.violation_details"))
            lines.append("-" * 40)
            for i, violation in enumerate(self.violations, 1):
                lines.append(f"{i}. {violation.description}")
                lines.append("   " + _("capacity.report.current_count", count=violation.current_count))
                lines.append("   " + _("capacity.report.limit_count", count=violation.max_limit))
                lines.append("   " + _("capacity.report.excess_count", count=violation.violation_count))
                lines.append("   " + _("capacity.report.usage_rate", rate=f"{violation.usage_rate * 100:.1f}%"))
                lines.append("   " + _("capacity.report.severity_level", level=violation.severity))
                lines.append("   " + _("capacity.report.suggestion", suggestion=violation.suggestion))
                lines.append("")

        return "\n".join(lines)


def count_dns_servers(config_content):
    """统计DNS服务器数量"""
    count = 0
    dns_section = re.search(r'config\s+system\s+dns(.*?)end', config_content, re.DOTALL)
    if dns_section:
        dns_content = dns_section.group(1)
        # 统计primary和secondary DNS服务器
        if re.search(r'set\s+primary\s+\S+', dns_content):
            count += 1
        if re.search(r'set\s+secondary\s+\S+', dns_content):
            count += 1
        # 统计server配置的DNS服务器
        server_matches = re.findall(r'set\s+server\s+"([^"]+)"', dns_content)
        count += len(server_matches)
    return count


def count_sub_interfaces(config_content):
    """统计子接口数量"""
    count = 0
    interface_section = re.search(r'config\s+system\s+interface(.*?)end', config_content, re.DOTALL)
    if interface_section:
        # 查找包含VLAN ID的接口（子接口）
        vlan_interfaces = re.findall(r'edit\s+"([^"]+)".*?set\s+vlanid\s+\d+',
                                   interface_section.group(1), re.DOTALL)
        count = len(vlan_interfaces)
    return count


def count_pppoe_sessions(config_content):
    """统计PPPOE拨号会话数量"""
    count = 0
    interface_section = re.search(r'config\s+system\s+interface(.*?)end', config_content, re.DOTALL)
    if interface_section:
        # 查找type为pppoe的接口
        pppoe_interfaces = re.findall(r'edit\s+"([^"]+)".*?set\s+type\s+pppoe',
                                    interface_section.group(1), re.DOTALL)
        count = len(pppoe_interfaces)
    return count


def count_static_routes(config_content):
    """统计静态路由V4条数"""
    count = 0
    route_section = re.search(r'config\s+router\s+static(.*?)end', config_content, re.DOTALL)
    if route_section:
        # 统计edit条目数量
        route_entries = re.findall(r'edit\s+\d+', route_section.group(1))
        count = len(route_entries)
    return count


def count_nat_policies(config_content):
    """统计NAT44策略数量"""
    count = 0
    policy_section = re.search(r'config\s+firewall\s+policy(.*?)end', config_content, re.DOTALL)
    if policy_section:
        # 查找包含set nat enable的策略
        nat_policies = re.findall(r'edit\s+\d+.*?set\s+nat\s+enable',
                                policy_section.group(1), re.DOTALL)
        count = len(nat_policies)
    return count


def count_security_policies(config_content):
    """统计安全策略数量"""
    count = 0
    policy_section = re.search(r'config\s+firewall\s+policy(.*?)end', config_content, re.DOTALL)
    if policy_section:
        # 统计edit条目数量
        policy_entries = re.findall(r'edit\s+\d+', policy_section.group(1))
        count = len(policy_entries)
    return count


def count_address_objects(config_content):
    """统计地址对象数量"""
    count = 0
    address_section = re.search(r'config\s+firewall\s+address(.*?)end', config_content, re.DOTALL)
    if address_section:
        # 统计edit条目数量
        address_entries = re.findall(r'edit\s+"([^"]+)"', address_section.group(1))
        count = len(address_entries)
    return count


def count_address_groups(config_content):
    """统计地址对象组数量"""
    count = 0
    addrgrp_section = re.search(r'config\s+firewall\s+addrgrp(.*?)end', config_content, re.DOTALL)
    if addrgrp_section:
        # 统计edit条目数量
        group_entries = re.findall(r'edit\s+"([^"]+)"', addrgrp_section.group(1))
        count = len(group_entries)
    return count


def count_service_objects(config_content):
    """统计服务对象数量"""
    count = 0
    service_section = re.search(r'config\s+firewall\s+service\s+custom(.*?)end', config_content, re.DOTALL)
    if service_section:
        # 统计edit条目数量
        service_entries = re.findall(r'edit\s+"([^"]+)"', service_section.group(1))
        count = len(service_entries)
    return count


def count_service_groups(config_content):
    """统计服务对象组数量"""
    count = 0
    servicegrp_section = re.search(r'config\s+firewall\s+service\s+group(.*?)end', config_content, re.DOTALL)
    if servicegrp_section:
        # 统计edit条目数量
        group_entries = re.findall(r'edit\s+"([^"]+)"', servicegrp_section.group(1))
        count = len(group_entries)
    return count


def count_time_objects(config_content):
    """统计时间对象数量"""
    count = 0
    schedule_section = re.search(r'config\s+firewall\s+schedule\s+recurring(.*?)end', config_content, re.DOTALL)
    if schedule_section:
        # 统计edit条目数量
        schedule_entries = re.findall(r'edit\s+"([^"]+)"', schedule_section.group(1))
        count = len(schedule_entries)

    # 还需要统计onetime schedule
    onetime_section = re.search(r'config\s+firewall\s+schedule\s+onetime(.*?)end', config_content, re.DOTALL)
    if onetime_section:
        onetime_entries = re.findall(r'edit\s+"([^"]+)"', onetime_section.group(1))
        count += len(onetime_entries)

    return count