#!/usr/bin/env python3
"""
最终的XML验证脚本
"""

import xml.etree.ElementTree as ET
import sys

def test_xml_parsing(xml_file):
    """测试XML文件解析"""
    print(f"🔍 测试XML文件: {xml_file}")
    
    try:
        tree = ET.parse(xml_file)
        root = tree.getroot()
        print(f"✅ XML解析成功")
        print(f"   根元素: {root.tag}")
        
        # 统计元素
        total_elements = len(list(root.iter()))
        print(f"   总元素数: {total_elements:,}")
        
        # 检查threat-intelligence结构
        threat_intel_valid = False
        for elem in root.iter():
            if elem.tag.endswith('threat-intelligence'):
                print(f"🔍 检查threat-intelligence结构...")
                
                # 尝试不同的命名空间查找方式
                management = elem.find('management')
                if management is None:
                    # 尝试带命名空间的查找
                    for child in elem:
                        child_tag = child.tag.split('}')[-1] if '}' in child.tag else child.tag
                        if child_tag == 'management':
                            management = child
                            break

                if management is not None:
                    print(f"   ✅ 有management子元素")
                    
                    security_zone = management.find('security-zone')
                    if security_zone is None:
                        # 尝试带命名空间的查找
                        for child in management:
                            child_tag = child.tag.split('}')[-1] if '}' in child.tag else child.tag
                            if child_tag == 'security-zone':
                                security_zone = child
                                break

                    if security_zone is not None:
                        print(f"   ✅ 有security-zone子元素")
                        
                        auto = security_zone.find('auto')
                        if auto is None:
                            # 尝试带命名空间的查找
                            for child in security_zone:
                                child_tag = child.tag.split('}')[-1] if '}' in child.tag else child.tag
                                if child_tag == 'auto':
                                    auto = child
                                    break

                        if auto is not None:
                            print(f"   ✅ 有auto子元素: {auto.text}")
                            threat_intel_valid = True
                        else:
                            print(f"   ❌ 缺少auto子元素")
                    else:
                        print(f"   ❌ 缺少security-zone子元素")
                else:
                    print(f"   ❌ 缺少management子元素")
        
        # 检查主security-zone容器是否干净
        security_zone_clean = True
        for elem in root.iter():
            if (elem.tag.endswith('security-zone') and 
                'urn:ruijie:ntos:params:xml:ns:yang:security-zone' in elem.tag):
                
                print(f"🔍 检查主security-zone容器...")
                
                # 检查直接子元素
                for child in elem:
                    child_tag = child.tag.split('}')[-1] if '}' in child.tag else child.tag
                    if child_tag == 'auto':
                        print(f"   ❌ 发现错误的auto元素")
                        security_zone_clean = False
                
                if security_zone_clean:
                    print(f"   ✅ security-zone容器结构正确")
        
        return threat_intel_valid and security_zone_clean
        
    except ET.ParseError as e:
        print(f"❌ XML解析失败: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 验证异常: {str(e)}")
        return False

def main():
    xml_file = "output/fortigate-z5100s-R11_backup_20250801_210727.xml"
    
    if len(sys.argv) > 1:
        xml_file = sys.argv[1]
    
    print("🚀 开始最终XML验证")
    print("=" * 60)
    
    success = test_xml_parsing(xml_file)
    
    print("=" * 60)
    
    if success:
        print(f"🎉 XML验证完全成功！")
        print(f"✅ 修复总结:")
        print(f"   - XML语法正确，无解析错误")
        print(f"   - threat-intelligence结构正确")
        print(f"   - security-zone结构正确")
        print(f"   - 重复属性问题已修复")
        print(f"   - 错误放置的auto元素已修复")
        print(f"\n🚀 可以进行YANG模型验证和生产部署！")
    else:
        print(f"❌ XML验证失败，需要进一步修复")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
