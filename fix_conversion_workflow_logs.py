#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复转换工作流程中的日志调用格式
将 log("message", "level") 格式修改为 log("message") 格式
"""

import re

def fix_conversion_workflow_logs():
    """修复转换工作流程中的日志调用"""
    file_path = "engine/business/workflows/conversion_workflow.py"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 修复日志调用格式
        # 匹配各种日志调用格式
        patterns = [
            # log("message", "level")
            (r'log\(([^,]+),\s*"(info|warning|error|debug)"\)', r'log(\1)'),
            # log(f"message", "level")
            (r'log\((f"[^"]*"),\s*"(info|warning|error|debug)"\)', r'log(\1)'),
            # log('message', 'level')
            (r"log\(([^,]+),\s*'(info|warning|error|debug)'\)", r'log(\1)'),
            # log(variable, "level")
            (r'log\(([a-zA-Z_][a-zA-Z0-9_]*),\s*"(info|warning|error|debug)"\)', r'log(\1)'),
            # log(_("message"), "level")
            (r'log\((_\([^)]+\)),\s*"(info|warning|error|debug)"\)', r'log(\1)'),
            # log(f"message {variable}", "level")
            (r'log\((f"[^"]*{[^}]*}[^"]*"),\s*"(info|warning|error|debug)"\)', r'log(\1)'),
        ]
        
        fixed_count = 0
        for pattern, replacement in patterns:
            matches = re.findall(pattern, content)
            if matches:
                content = re.sub(pattern, replacement, content)
                fixed_count += len(matches)
                print(f"✅ 修复了 {len(matches)} 个匹配模式: {pattern}")
        
        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ 已修复文件: {file_path}")
            print(f"   总共修复了 {fixed_count} 个日志调用")
            return True
        else:
            print(f"⏭️ 文件无需修复: {file_path}")
            return False
            
    except Exception as e:
        print(f"❌ 修复失败 {file_path}: {e}")
        return False

def main():
    """主函数"""
    print("🔧 转换工作流程日志调用修复工具")
    print("=" * 50)
    
    if fix_conversion_workflow_logs():
        print("\n🎉 修复完成！")
        print("✅ 所有日志调用格式已统一")
        print("✅ 管道处理失败问题应该已解决")
        
        print("\n🚀 建议:")
        print("1. 重新运行转换测试")
        print("2. 检查是否还有其他错误")
        print("3. 验证四层优化策略正常工作")
    else:
        print("\n⚠️ 无需修复或修复失败")

if __name__ == "__main__":
    main()
