#!/usr/bin/env python3
"""
修复重复NAT池问题的脚本
分析并修复XML模板集成阶段中重复生成NAT池和规则的问题
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def analyze_duplicate_issue():
    """分析重复问题的根本原因"""
    print("🔍 分析重复NAT池问题...")
    
    # 分析XML文件中的重复情况
    xml_file = "output/fortigate-z5100s-R11_backup_20250801_194353.xml"
    
    if not os.path.exists(xml_file):
        print(f"❌ XML文件不存在: {xml_file}")
        return False
    
    print(f"📄 分析文件: {xml_file}")
    
    # 统计pool和rule的重复情况
    pool_names = {}
    rule_names = {}
    
    with open(xml_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    in_nat_section = False
    current_pool_name = None
    current_rule_name = None
    
    for i, line in enumerate(lines, 1):
        line = line.strip()
        
        # 检测NAT section
        if '<nat xmlns="urn:ruijie:ntos:params:xml:ns:yang:nat">' in line:
            in_nat_section = True
            continue
        elif '</nat>' in line:
            in_nat_section = False
            continue
        
        if in_nat_section:
            # 检测pool定义
            if '<pool>' in line:
                current_pool_name = None
            elif '<name>' in line and '</name>' in line and current_pool_name is None:
                # 提取pool名称
                name_start = line.find('<name>') + 6
                name_end = line.find('</name>')
                if name_start > 5 and name_end > name_start:
                    pool_name = line[name_start:name_end]
                    if pool_name not in pool_names:
                        pool_names[pool_name] = []
                    pool_names[pool_name].append(i)
                    current_pool_name = pool_name
            elif '</pool>' in line:
                current_pool_name = None
            
            # 检测rule定义
            elif '<rule>' in line:
                current_rule_name = None
            elif '<name>' in line and '</name>' in line and current_rule_name is None:
                # 提取rule名称
                name_start = line.find('<name>') + 6
                name_end = line.find('</name>')
                if name_start > 5 and name_end > name_start:
                    rule_name = line[name_start:name_end]
                    if rule_name not in rule_names:
                        rule_names[rule_name] = []
                    rule_names[rule_name].append(i)
                    current_rule_name = rule_name
            elif '</rule>' in line:
                current_rule_name = None
    
    # 统计重复情况
    duplicate_pools = {name: lines for name, lines in pool_names.items() if len(lines) > 1}
    duplicate_rules = {name: lines for name, lines in rule_names.items() if len(lines) > 1}
    
    print(f"\n📊 统计结果:")
    print(f"   总pool数: {len(pool_names)}")
    print(f"   重复pool数: {len(duplicate_pools)}")
    print(f"   总rule数: {len(rule_names)}")
    print(f"   重复rule数: {len(duplicate_rules)}")
    
    # 显示前几个重复的pool
    print(f"\n🔍 重复pool示例:")
    for i, (name, line_nums) in enumerate(list(duplicate_pools.items())[:5]):
        print(f"   {name}: 出现在行 {line_nums}")
    
    # 显示前几个重复的rule
    print(f"\n🔍 重复rule示例:")
    for i, (name, line_nums) in enumerate(list(duplicate_rules.items())[:5]):
        print(f"   {name}: 出现在行 {line_nums}")
    
    return True

def identify_root_cause():
    """识别根本原因"""
    print("\n🎯 根本原因分析:")
    print("   1. XML模板集成阶段可能多次调用NAT集成")
    print("   2. 不同的处理阶段可能重复生成相同的NAT配置")
    print("   3. 缺少重复检查机制")
    
    # 检查可能的重复调用点
    potential_issues = [
        "XML模板集成阶段的_integrate_nat_rules方法",
        "twice-nat44集成可能与常规NAT集成冲突",
        "多个VRF或NAT容器被创建",
        "XML片段被多次添加到同一个容器"
    ]
    
    print("\n🔍 可能的问题点:")
    for i, issue in enumerate(potential_issues, 1):
        print(f"   {i}. {issue}")
    
    return True

def create_fix_plan():
    """创建修复计划"""
    print("\n🔧 修复计划:")
    
    fixes = [
        {
            "issue": "XML模板集成重复调用",
            "solution": "在_integrate_nat_rules中添加重复检查",
            "file": "engine/processing/stages/xml_template_integration_stage.py",
            "priority": "高"
        },
        {
            "issue": "NAT池重复生成",
            "solution": "在添加pool前检查是否已存在",
            "file": "engine/generators/nat_generator.py",
            "priority": "高"
        },
        {
            "issue": "NAT规则重复生成",
            "solution": "在添加rule前检查是否已存在",
            "file": "engine/generators/nat_generator.py",
            "priority": "高"
        },
        {
            "issue": "twice-nat44与常规NAT冲突",
            "solution": "确保twice-nat44不与常规NAT重复处理",
            "file": "engine/processing/stages/xml_template_integration_stage.py",
            "priority": "中"
        }
    ]
    
    for i, fix in enumerate(fixes, 1):
        print(f"   {i}. 【{fix['priority']}】{fix['issue']}")
        print(f"      解决方案: {fix['solution']}")
        print(f"      文件: {fix['file']}")
        print()
    
    return fixes

def main():
    """主函数"""
    print("🚀 开始分析和修复重复NAT池问题")
    print("=" * 60)
    
    # 步骤1：分析重复问题
    if not analyze_duplicate_issue():
        print("❌ 分析失败")
        return False
    
    # 步骤2：识别根本原因
    identify_root_cause()
    
    # 步骤3：创建修复计划
    fixes = create_fix_plan()
    
    print("✅ 分析完成！")
    print("\n📋 下一步行动:")
    print("   1. 实施XML模板集成阶段的重复检查")
    print("   2. 修复NAT生成器的重复生成问题")
    print("   3. 确保twice-nat44与常规NAT不冲突")
    print("   4. 重新生成XML并验证")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
