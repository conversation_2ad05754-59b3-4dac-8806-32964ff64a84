#!/usr/bin/env python3
"""
修复现有XML文件中的地址引用不一致问题
"""

import sys
import os
import re
import shutil
from datetime import datetime

def fix_xml_address_references(xml_file_path):
    """修复XML文件中的地址引用不一致问题"""
    print(f"=== 修复XML文件: {xml_file_path} ===")
    
    if not os.path.exists(xml_file_path):
        print(f"❌ 文件不存在: {xml_file_path}")
        return False
    
    try:
        # 备份原文件
        backup_file = f"{xml_file_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(xml_file_path, backup_file)
        print(f"✅ 已备份原文件到: {backup_file}")
        
        # 读取XML内容
        with open(xml_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 分析当前状态
        print("\n分析当前状态:")
        
        # 查找IP地址格式的地址对象定义
        address_defs = re.findall(r'<address-set>.*?<name>(10\.10\.11\.15\d)</name>', content, re.DOTALL)
        print(f"  地址对象定义(点号格式): {len(address_defs)} 个")
        
        # 查找策略中的IP地址格式引用
        policy_ip_refs = re.findall(r'<(?:source|dest)-network>.*?<name>(10\.10\.11\.15\d)</name>', content, re.DOTALL)
        print(f"  策略引用(点号格式): {len(policy_ip_refs)} 个")
        
        # 查找策略中的下划线格式引用
        policy_underscore_refs = re.findall(r'<(?:source|dest)-network>.*?<name>(10_10_11_15\d)</name>', content, re.DOTALL)
        print(f"  策略引用(下划线格式): {len(policy_underscore_refs)} 个")
        
        if not policy_underscore_refs:
            print("✅ 没有发现下划线格式的引用，无需修复")
            return True
        
        # 执行修复：将策略中的下划线格式引用替换为点号格式
        print(f"\n开始修复 {len(policy_underscore_refs)} 个下划线格式的引用...")
        
        fixed_content = content
        fix_count = 0
        
        # 创建替换映射
        replacements = {}
        for underscore_ref in set(policy_underscore_refs):  # 去重
            dot_ref = underscore_ref.replace('_', '.')
            replacements[underscore_ref] = dot_ref
        
        # 执行替换
        for underscore_ref, dot_ref in replacements.items():
            # 只替换策略中的引用，不替换地址对象定义
            pattern = r'(<(?:source|dest)-network>.*?<name>)' + re.escape(underscore_ref) + r'(</name>)'
            replacement = r'\1' + dot_ref + r'\2'
            
            old_content = fixed_content
            fixed_content = re.sub(pattern, replacement, fixed_content, flags=re.DOTALL)
            
            if fixed_content != old_content:
                fix_count += 1
                print(f"  ✅ 修复引用: {underscore_ref} → {dot_ref}")
        
        # 验证修复结果
        print(f"\n验证修复结果:")
        
        # 重新分析修复后的内容
        fixed_policy_ip_refs = re.findall(r'<(?:source|dest)-network>.*?<name>(10\.10\.11\.15\d)</name>', fixed_content, re.DOTALL)
        fixed_policy_underscore_refs = re.findall(r'<(?:source|dest)-network>.*?<name>(10_10_11_15\d)</name>', fixed_content, re.DOTALL)
        
        print(f"  修复后策略引用(点号格式): {len(fixed_policy_ip_refs)} 个")
        print(f"  修复后策略引用(下划线格式): {len(fixed_policy_underscore_refs)} 个")
        
        if not fixed_policy_underscore_refs:
            print("🎉 修复成功！所有策略引用现在都使用点号格式")
            
            # 保存修复后的文件
            with open(xml_file_path, 'w', encoding='utf-8') as f:
                f.write(fixed_content)
            
            print(f"✅ 已保存修复后的文件: {xml_file_path}")
            return True
        else:
            print(f"❌ 修复不完整，仍有 {len(fixed_policy_underscore_refs)} 个下划线格式的引用")
            return False
        
    except Exception as e:
        print(f"❌ 修复过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("修复现有XML文件中的地址引用不一致问题")
    print("=" * 60)
    
    # 要修复的XML文件列表
    xml_files = [
        "output/fortigate-z5100s-R11_backup_20250804_161148.xml",
        "output/fortigate-z5100s-R11.xml",
        "output/fortigate-z3200s-R11.xml"
    ]
    
    success_count = 0
    total_count = 0
    
    for xml_file in xml_files:
        if os.path.exists(xml_file):
            total_count += 1
            if fix_xml_address_references(xml_file):
                success_count += 1
            print()  # 空行分隔
        else:
            print(f"⚠️ 文件不存在，跳过: {xml_file}")
    
    print("=" * 60)
    print(f"修复完成: {success_count}/{total_count} 个文件修复成功")
    
    if success_count == total_count and total_count > 0:
        print("🎉 所有文件修复成功！")
        return True
    elif success_count > 0:
        print("⚠️ 部分文件修复成功")
        return True
    else:
        print("❌ 没有文件修复成功")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
