#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
FortiGate转换器错误修复脚本

修复服务器运行时发现的关键错误：
1. NAT规则生成错误
2. log()函数参数冲突
3. 接口映射不完整
4. 变量未定义错误
"""

import os
import sys
import json
import re
from pathlib import Path

def fix_interface_mapping():
    """修复接口映射文件"""
    print("🔧 修复接口映射文件...")
    
    # 完整的接口映射
    complete_mapping = {
        "mgmt": "Ge0/0",
        "ha": "Ge0/9", 
        "port1": "Ge0/3",
        "port2": "Ge0/4",
        "port3": "Ge0/5",
        "port4": "Ge0/6",
        "port5": "Ge0/7",
        "port6": "Ge0/8",
        "port7": "Ge0/10",
        "port8": "Ge0/11",
        "port9": "Ge0/12",
        "port10": "Ge0/13",
        "port11": "Ge0/14",
        "port12": "Ge0/15",
        "port13": "Ge0/16",
        "port14": "Ge0/17",
        "port15": "Ge0/18",
        "port16": "Ge0/19",
        "port17": "Ge0/20",
        "port18": "Ge0/21",
        "port19": "Ge0/22",
        "port20": "Ge0/23",
        "port21": "Ge0/24",
        "port22": "Ge0/25",
        "port23": "Ge0/1",
        "port24": "Ge0/2",
        "x1": "TenGe0/0",
        "x2": "TenGe0/1",
        "x3": "TenGe0/2",
        "x4": "TenGe0/3",
        "modem": "Ge0/26",
        "naf.root": "Ge0/27",
        "l2t.root": "Ge0/28",
        "ssl.root": "Ge0/29",
        "BOS": "Ge0/30",
        "SSL_VPN": "Ge0/31"
    }
    
    # 保存完整映射文件
    mapping_file = "mappings/interface_mapping_correct.json"
    backup_file = "mappings/interface_mapping_correct_backup.json"
    
    # 备份原文件
    if os.path.exists(mapping_file):
        os.rename(mapping_file, backup_file)
        print(f"✅ 原映射文件已备份到: {backup_file}")
    
    # 写入新的完整映射
    with open(mapping_file, 'w', encoding='utf-8') as f:
        json.dump(complete_mapping, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 完整接口映射已保存到: {mapping_file}")
    print(f"   包含 {len(complete_mapping)} 个接口映射")
    
    return True

def check_nat_processor_issues():
    """检查NAT处理器相关问题"""
    print("🔍 检查NAT处理器问题...")
    
    # 查找NAT处理器相关文件
    nat_files = []
    for root, dirs, files in os.walk("engine"):
        for file in files:
            if "nat" in file.lower() and file.endswith(".py"):
                nat_files.append(os.path.join(root, file))
    
    print(f"   找到 {len(nat_files)} 个NAT相关文件:")
    for file in nat_files:
        print(f"     - {file}")
    
    # 检查常见问题
    issues_found = []
    
    for file_path in nat_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # 检查context属性问题
                if "self.context" in content and "def __init__" in content:
                    if "self.context = " not in content:
                        issues_found.append(f"{file_path}: 缺少context属性初始化")
                
                # 检查rule_name变量问题
                if "rule_name" in content and "rule_name =" not in content:
                    issues_found.append(f"{file_path}: rule_name变量可能未定义")
                
                # 检查log函数调用问题
                log_calls = re.findall(r'log\([^)]+\)', content)
                for call in log_calls:
                    if call.count(',') > 2:  # 可能有参数冲突
                        issues_found.append(f"{file_path}: log()函数调用可能有参数冲突: {call[:50]}...")
        
        except Exception as e:
            print(f"   ⚠️ 无法读取文件 {file_path}: {e}")
    
    if issues_found:
        print("   ❌ 发现以下问题:")
        for issue in issues_found[:10]:  # 只显示前10个
            print(f"     - {issue}")
        if len(issues_found) > 10:
            print(f"     ... 还有 {len(issues_found) - 10} 个问题")
    else:
        print("   ✅ 未发现明显的代码问题")
    
    return issues_found

def create_test_script():
    """创建测试脚本"""
    print("📝 创建测试脚本...")
    
    test_script = """#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_fixed_converter():
    \"\"\"测试修复后的转换器\"\"\"
    
    print("🧪 测试修复后的FortiGate转换器")
    print("=" * 50)
    
    # 测试参数
    config_file = "Pass-Mask-MTU-FW-1_7-2_1639_202506271000.conf"
    mapping_file = "mappings/interface_mapping_correct.json"
    output_file = "output/test_fixed_output.xml"
    
    # 检查文件存在性
    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        return False
    
    if not os.path.exists(mapping_file):
        print(f"❌ 映射文件不存在: {mapping_file}")
        return False
    
    try:
        # 导入转换器
        from engine.business.workflows.conversion_workflow import ConversionWorkflow
        from engine.infrastructure.config.config_manager import ConfigManager
        from engine.infrastructure.templates.template_manager import TemplateManager
        from engine.infrastructure.yang.yang_manager import YangManager
        
        print("✅ 转换器模块导入成功")
        
        # 创建管理器
        config_manager = ConfigManager()
        template_manager = TemplateManager(config_manager)
        yang_manager = YangManager(config_manager)
        workflow = ConversionWorkflow(config_manager, template_manager, yang_manager)
        
        print("✅ 转换器组件初始化成功")
        
        # 准备转换参数
        params = {
            'mode': 'convert',
            'vendor': 'fortigate',
            'cli_file': config_file,
            'mapping_file': mapping_file,
            'model': 'z5100s',
            'version': 'R11',
            'output_file': output_file,
            'lang': 'zh-CN'
        }
        
        print("🔄 开始转换测试...")
        
        # 执行转换
        result = workflow.execute_conversion(params)
        
        if result.get('success', False):
            print("✅ 转换测试成功!")
            print(f"   输出文件: {output_file}")
            
            # 检查输出文件
            if os.path.exists(output_file):
                file_size = os.path.getsize(output_file)
                print(f"   文件大小: {file_size:,} 字节")
            
            return True
        else:
            print("❌ 转换测试失败!")
            error = result.get('error', '未知错误')
            print(f"   错误: {error}")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_fixed_converter()
    sys.exit(0 if success else 1)
"""
    
    with open("test_fixed_converter.py", 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("✅ 测试脚本已创建: test_fixed_converter.py")
    
    return True

def main():
    """主函数"""
    print("🚀 FortiGate转换器错误修复工具")
    print("=" * 60)
    
    # 1. 修复接口映射
    if fix_interface_mapping():
        print("✅ 接口映射修复完成")
    else:
        print("❌ 接口映射修复失败")
        return False
    
    # 2. 检查NAT处理器问题
    issues = check_nat_processor_issues()
    if issues:
        print(f"⚠️ 发现 {len(issues)} 个潜在问题，需要进一步修复")
    else:
        print("✅ NAT处理器检查完成")
    
    # 3. 创建测试脚本
    if create_test_script():
        print("✅ 测试脚本创建完成")
    else:
        print("❌ 测试脚本创建失败")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 修复工具执行完成!")
    print("\n📋 后续步骤:")
    print("1. 运行测试脚本: python test_fixed_converter.py")
    print("2. 使用新的接口映射文件重新转换")
    print("3. 检查转换结果和日志")
    print("\n🔧 如果仍有问题，需要进一步修复代码中的NAT处理器错误")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
