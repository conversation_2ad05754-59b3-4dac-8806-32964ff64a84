#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复日志调用格式的脚本
将 log("message", "level") 格式修改为 log("message") 格式
"""

import os
import re
import sys

def fix_log_calls_in_file(file_path):
    """修复单个文件中的日志调用"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 修复日志调用格式
        # 匹配 log("message", "level") 或 log(f"message", "level") 等格式
        patterns = [
            # log("message", "info")
            (r'log\(([^,]+),\s*"(info|warning|error|debug)"\)', r'log(\1)'),
            # log(f"message", "info")
            (r'log\((f"[^"]*"),\s*"(info|warning|error|debug)"\)', r'log(\1)'),
            # log('message', 'info')
            (r"log\(([^,]+),\s*'(info|warning|error|debug)'\)", r'log(\1)'),
            # log(variable, "info")
            (r'log\(([a-zA-Z_][a-zA-Z0-9_]*),\s*"(info|warning|error|debug)"\)', r'log(\1)'),
        ]
        
        for pattern, replacement in patterns:
            content = re.sub(pattern, replacement, content)
        
        # 修复 safe_translate 调用
        content = re.sub(r'safe_translate\(([^)]+)\)', r'_(\1)', content)
        
        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ 已修复: {file_path}")
            return True
        else:
            print(f"⏭️ 无需修复: {file_path}")
            return False
            
    except Exception as e:
        print(f"❌ 修复失败 {file_path}: {e}")
        return False

def fix_log_calls_in_directory(directory):
    """修复目录中所有Python文件的日志调用"""
    fixed_count = 0
    total_count = 0
    
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                total_count += 1
                if fix_log_calls_in_file(file_path):
                    fixed_count += 1
    
    print(f"\n📊 修复统计:")
    print(f"   总文件数: {total_count}")
    print(f"   已修复: {fixed_count}")
    print(f"   无需修复: {total_count - fixed_count}")

def main():
    """主函数"""
    print("🔧 日志调用格式修复工具")
    print("=" * 50)
    
    # 需要修复的文件列表
    files_to_fix = [
        "engine/processing/stages/optimization_summary_stage.py",
        "engine/processing/stages/optimization_aware_stage.py", 
        "engine/processing/stages/optimized_address_processing_stage.py",
        "engine/processing/stages/fallback_optimization_stage.py",
        "engine/processing/optimization/optimization_flow_controller.py",
        "engine/utils/optimization_logger.py"
    ]
    
    fixed_count = 0
    
    for file_path in files_to_fix:
        if os.path.exists(file_path):
            print(f"\n🔍 修复文件: {file_path}")
            if fix_log_calls_in_file(file_path):
                fixed_count += 1
        else:
            print(f"⚠️ 文件不存在: {file_path}")
    
    print(f"\n🎉 修复完成！共修复 {fixed_count} 个文件")

if __name__ == "__main__":
    main()
