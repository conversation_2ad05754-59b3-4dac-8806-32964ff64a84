#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
批量修复time-range元素结构错误
"""

import re
import os

def fix_time_range_batch():
    """批量修复time-range元素结构错误"""
    
    xml_file = "output/fortigate-z5100s-R11_backup_20250802_112317.xml"
    
    if not os.path.exists(xml_file):
        print(f"❌ XML文件不存在: {xml_file}")
        return False
    
    try:
        print("🔧 开始批量修复time-range元素...")
        
        with open(xml_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 统计修复前的问题
        problem_pattern = r'<time-range>([^<]*)<name>([^<]*)</name></time-range>'
        matches = re.findall(problem_pattern, content)
        
        print(f"   发现 {len(matches)} 个有问题的time-range元素")
        
        if matches:
            # 修复time-range结构
            # 错误: <time-range>always<name>time-range_1</name></time-range>
            # 正确: <time-range>always</time-range>
            
            fixed_content = re.sub(problem_pattern, r'<time-range>\1</time-range>', content)
            
            # 验证修复效果
            remaining_problems = re.findall(problem_pattern, fixed_content)
            
            if len(remaining_problems) == 0:
                # 保存修复后的文件
                with open(xml_file, 'w', encoding='utf-8') as f:
                    f.write(fixed_content)
                
                print(f"   ✅ 成功修复 {len(matches)} 个time-range元素")
                print(f"   📄 文件已更新: {xml_file}")
                
                return True
            else:
                print(f"   ⚠️ 修复不完整，仍有 {len(remaining_problems)} 个问题")
                return False
        else:
            print("   ℹ️ 未发现time-range结构问题")
            return True
    
    except Exception as e:
        print(f"   ❌ 批量修复失败: {e}")
        return False

if __name__ == "__main__":
    success = fix_time_range_batch()
    if success:
        print("🎉 time-range元素批量修复完成!")
    else:
        print("❌ time-range元素批量修复失败!")
