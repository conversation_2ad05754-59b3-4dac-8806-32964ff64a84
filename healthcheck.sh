#!/bin/bash

# 健康检查脚本
# 这个脚本会检查configtrans-service服务是否正常运行

# 记录时间戳
log() {
    # 根据环境变量决定日志级别
    local level=${HEALTHCHECK_LOGLEVEL:-"INFO"}
    # 只有在详细模式下或错误日志才输出
    if [ "$level" == "DEBUG" ] || [ "$1" == *"错误"* ] || [ "$1" == *"警告"* ]; then
        echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1"
    fi
}

# 设置环境变量，如果未定义则使用默认值
PORT=${PORT:-9005}
HOST=${HOST:-0.0.0.0}
APP_NAME="configtrans-service"
API_CHECK=0

# 检查服务健康状态
# 优先使用新添加的健康检查接口
check_api_health() {
    if command -v curl &> /dev/null; then
        # 尝试直接访问健康检查端点
        if curl -s -f -m 2 "http://localhost:$PORT/health" > /dev/null 2>&1 || 
           curl -s -f -m 2 "http://localhost:$PORT/healthz" > /dev/null 2>&1; then
            return 0
        fi
    fi
    return 1
}

# 使用更可靠的方式检查进程
check_process() {
    local process_name="$1"
    # 尝试多种方法检查进程
    if pgrep -f "$process_name" > /dev/null 2>&1; then
        return 0
    elif ps -ef | grep -v grep | grep -q "$process_name"; then
        return 0
    else
        return 1
    fi
}

# 主健康检查逻辑
main_health_check() {
    # 先检查API健康
    if check_api_health; then
        log "API健康检查通过"
        return 0
    fi

    # 检查进程是否存在
    if ! check_process "$APP_NAME"; then
        log "错误: $APP_NAME进程不存在！"
        return 1
    fi

    # 获取进程ID
    PID=$(pgrep -f "$APP_NAME" | head -1)
    if [ -z "$PID" ]; then
        PID=$(ps -ef | grep -v grep | grep "$APP_NAME" | awk '{print $2}' | head -1)
        if [ -z "$PID" ]; then
            log "错误: 无法获取$APP_NAME进程ID"
            return 1
        fi
    fi

    # 检查端口是否在监听
    if command -v netstat &> /dev/null && (netstat -tuln | grep -qE ":($PORT|9005) "); then
        log "进程在运行且端口在监听，但API不响应，视为健康"
        return 0
    elif command -v ss &> /dev/null && (ss -tuln | grep -qE ":($PORT|9005) "); then
        log "进程在运行且端口在监听，但API不响应，视为健康"
        return 0
    else
        log "错误: 进程在运行但端口未监听"
        return 1
    fi
}

# 简化后的健康检查主函数
main() {
    # 只在DEBUG模式下显示开始信息
    if [ "${HEALTHCHECK_LOGLEVEL:-INFO}" == "DEBUG" ]; then
        log "开始健康检查..."
    fi

    # 执行主要健康检查
    if main_health_check; then
        # 成功时不输出详细信息，除非是DEBUG模式
        if [ "${HEALTHCHECK_LOGLEVEL:-INFO}" == "DEBUG" ]; then
            log "健康检查完成: 服务正常运行"
        fi
        exit 0
    else
        # 失败时总是输出错误
        log "健康检查失败: 服务异常"
        exit 1
    fi
}

# 执行主函数
main 