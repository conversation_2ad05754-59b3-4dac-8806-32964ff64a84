# 第2周实施计划：接口映射优化和内存管理

## 📋 任务清单

### Day 6-7: 增强接口映射器实现
- [ ] 实现 `EnhancedInterfaceMapper` 类
- [ ] 开发隧道接口自动映射
- [ ] 实现VLAN接口动态生成
- [ ] 策略接口验证优化

**具体实施步骤：**

1. **集成增强接口映射器**
```python
# 在 engine/stages/fortigate_strategy_stage.py 中修改
from optimization_design.interface_mapping_optimizer import EnhancedInterfaceMapper, PolicyInterfaceValidator

class FortigateStrategyStage(PipelineStage):
    def __init__(self, config: Dict):
        super().__init__(config)
        self.interface_mapper = EnhancedInterfaceMapper()
        self.policy_validator = PolicyInterfaceValidator(self.interface_mapper)
    
    def process(self, data: Dict) -> Dict:
        # 加载并增强接口映射
        interface_mapping_file = data.get('interface_mapping_file')
        enhanced_mappings = self.interface_mapper.load_interface_mappings(interface_mapping_file)
        
        # 获取解析的配置数据
        config_data = data.get('config_data', {})
        policies = config_data.get('policies', [])
        zones = config_data.get('zones', [])
        
        # 验证和优化策略接口映射
        validated_policies = []
        for policy in policies:
            if self.policy_validator.validate_policy_interfaces(policy, enhanced_mappings, zones):
                validated_policies.append(policy)
            else:
                logging.warning(f"策略 {policy.get('policyid')} 接口验证失败，尝试自动修复")
                # 尝试自动修复
                if self._auto_fix_policy_interfaces(policy, enhanced_mappings):
                    validated_policies.append(policy)
        
        # 更新数据
        config_data['policies'] = validated_policies
        data['enhanced_interface_mappings'] = enhanced_mappings
        data['validation_report'] = self.policy_validator.get_validation_report()
        
        return data
    
    def _auto_fix_policy_interfaces(self, policy: Dict, mappings: Dict[str, str]) -> bool:
        """自动修复策略接口映射"""
        # 实现自动修复逻辑
        pass
```

2. **ssl.root接口问题专项修复**
```python
# 在 optimization_design/ssl_interface_handler.py 中实现
class SSLInterfaceHandler:
    """SSL接口专项处理器"""
    
    def __init__(self):
        self.ssl_interface_mappings = {
            'ssl.root': 'tunnel-ssl-root',
            'ssl_tunnel': 'tunnel-ssl',
            'ssl.vpn': 'tunnel-ssl-vpn',
        }
        self.ssl_zone_mapping = 'ssl-vpn-zone'
    
    def handle_ssl_interface(self, interface_name: str, mappings: Dict[str, str]) -> str:
        """处理SSL接口映射"""
        if interface_name in self.ssl_interface_mappings:
            mapped_name = self.ssl_interface_mappings[interface_name]
            mappings[interface_name] = mapped_name
            logging.info(f"SSL接口映射: {interface_name} -> {mapped_name}")
            return mapped_name
        
        # 动态生成SSL接口映射
        if 'ssl' in interface_name.lower():
            clean_name = interface_name.replace('.', '-').replace('_', '-')
            mapped_name = f"tunnel-{clean_name}"
            mappings[interface_name] = mapped_name
            logging.info(f"动态SSL接口映射: {interface_name} -> {mapped_name}")
            return mapped_name
        
        return None
    
    def create_ssl_zone_if_needed(self, zones: List[Dict]) -> List[Dict]:
        """如果需要，创建SSL VPN区域"""
        ssl_zone_exists = any(zone.get('name') == self.ssl_zone_mapping for zone in zones)
        
        if not ssl_zone_exists:
            ssl_zone = {
                'name': self.ssl_zone_mapping,
                'description': 'SSL VPN tunnel zone (auto-generated)',
                'interfaces': list(self.ssl_interface_mappings.values())
            }
            zones.append(ssl_zone)
            logging.info(f"自动创建SSL VPN区域: {self.ssl_zone_mapping}")
        
        return zones
```

### Day 8-9: 内存优化和流式处理
- [ ] 实现 `MemoryOptimizedParser` 类
- [ ] 开发流式文件处理机制
- [ ] 实现内存监控和自动回收
- [ ] 大文件处理压力测试

**代码实现：**

```python
# 集成流式处理到主解析流程
class OptimizedFortigateParserV2:
    """第二版优化解析器，集成流式处理"""
    
    def __init__(self):
        self.intelligent_parser = IntelligentConfigParser()
        self.streaming_parser = StreamingFortigateParser(chunk_size=5000)
        self.memory_threshold = 180  # MB
    
    def parse_large_config_v2(self, file_path: str) -> Dict:
        """第二版大型配置解析"""
        file_size = os.path.getsize(file_path) / 1024 / 1024  # MB
        
        if file_size > 30:  # 大于30MB使用流式处理
            logging.info(f"大文件({file_size:.1f}MB)，启用流式处理")
            return self.streaming_parser.parse_config_file(file_path)
        else:
            logging.info(f"中等文件({file_size:.1f}MB)，使用智能解析")
            return self.intelligent_parser.parse_large_config(file_path)
```

### Day 10: 性能调优和集成测试
- [ ] 性能瓶颈分析和优化
- [ ] 内存泄漏检测和修复
- [ ] 完整的端到端测试
- [ ] 性能基准建立

**性能测试脚本：**

```python
# tests/performance/comprehensive_performance_test.py
import time
import psutil
import gc
from typing import Dict, List

class PerformanceProfiler:
    """性能分析器"""
    
    def __init__(self):
        self.metrics = {
            'parsing_time': 0,
            'memory_peak': 0,
            'memory_average': 0,
            'cpu_usage': 0,
            'policies_processed': 0,
            'interfaces_processed': 0,
            'warnings_count': 0,
            'errors_count': 0
        }
        self.memory_samples = []
    
    def start_profiling(self):
        """开始性能分析"""
        self.start_time = time.time()
        self.start_memory = psutil.Process().memory_info().rss / 1024 / 1024
        self.process = psutil.Process()
        
        # 启动内存监控线程
        import threading
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_memory)
        self.monitor_thread.start()
    
    def stop_profiling(self) -> Dict:
        """停止性能分析并返回结果"""
        self.monitoring = False
        self.monitor_thread.join()
        
        self.metrics['parsing_time'] = time.time() - self.start_time
        self.metrics['memory_peak'] = max(self.memory_samples) if self.memory_samples else 0
        self.metrics['memory_average'] = sum(self.memory_samples) / len(self.memory_samples) if self.memory_samples else 0
        
        return self.metrics
    
    def _monitor_memory(self):
        """监控内存使用"""
        while self.monitoring:
            current_memory = self.process.memory_info().rss / 1024 / 1024
            self.memory_samples.append(current_memory)
            time.sleep(1)  # 每秒采样一次

def run_comprehensive_test():
    """运行综合性能测试"""
    test_files = [
        "KHU-FGT-1_7-0_0682_202507311406.conf",  # 大型文件
        "medium_config.conf",  # 中型文件
        "small_config.conf"    # 小型文件
    ]
    
    results = {}
    
    for test_file in test_files:
        if not os.path.exists(test_file):
            continue
            
        print(f"\n测试文件: {test_file}")
        
        # 测试优化版本
        profiler = PerformanceProfiler()
        profiler.start_profiling()
        
        try:
            parser = OptimizedFortigateParserV2()
            result = parser.parse_large_config_v2(test_file)
            
            profiler.metrics['policies_processed'] = len(result.get('policies', []))
            profiler.metrics['interfaces_processed'] = len(result.get('interfaces', []))
            
        except Exception as e:
            print(f"解析失败: {e}")
            profiler.metrics['errors_count'] = 1
        
        metrics = profiler.stop_profiling()
        results[test_file] = metrics
        
        # 打印结果
        print(f"解析时间: {metrics['parsing_time']:.2f}秒")
        print(f"内存峰值: {metrics['memory_peak']:.2f}MB")
        print(f"内存平均: {metrics['memory_average']:.2f}MB")
        print(f"处理策略: {metrics['policies_processed']}个")
        print(f"处理接口: {metrics['interfaces_processed']}个")
        
        # 强制垃圾回收
        gc.collect()
        time.sleep(2)
    
    return results
```

## 📊 第2周预期成果

| 指标 | 目标值 | 当前基线 | 改进幅度 |
|------|--------|----------|----------|
| 策略验证成功率 | >95% | ~60% | +35% |
| 接口映射覆盖率 | >98% | ~85% | +13% |
| 内存使用峰值 | <200MB | 112MB | 控制增长 |
| SSL接口处理成功率 | 100% | 0% | 全新功能 |
| 警告数量 | <5,000个 | 15,626个 | -68% |

## 🔧 关键技术实现

### 1. 动态接口映射生成
```python
def generate_dynamic_mapping(self, interface_name: str) -> str:
    """动态生成接口映射"""
    # SSL隧道接口
    if 'ssl' in interface_name.lower():
        return f"tunnel-ssl-{interface_name.replace('.', '-').replace('ssl', '').strip('-')}"
    
    # VLAN子接口
    if '.' in interface_name:
        parent, vlan_id = interface_name.split('.', 1)
        parent_mapping = self.find_parent_mapping(parent)
        if parent_mapping:
            return f"{parent_mapping}.{vlan_id}"
    
    # 默认虚拟接口
    return f"virtual-{interface_name.replace('.', '-').replace('_', '-')}"
```

### 2. 内存监控和自动回收
```python
@contextmanager
def memory_guard(self, max_memory_mb: int = 200):
    """内存保护上下文管理器"""
    initial_memory = psutil.Process().memory_info().rss / 1024 / 1024
    
    try:
        yield
    finally:
        current_memory = psutil.Process().memory_info().rss / 1024 / 1024
        
        if current_memory > max_memory_mb:
            logging.warning(f"内存使用({current_memory:.1f}MB)超过限制，执行垃圾回收")
            gc.collect()
            
            # 再次检查
            after_gc_memory = psutil.Process().memory_info().rss / 1024 / 1024
            logging.info(f"垃圾回收后内存: {after_gc_memory:.1f}MB")
```

## 🚨 风险控制

### 风险1：动态映射导致配置不一致
**缓解措施：**
- 详细记录所有动态生成的映射
- 提供映射导出功能供用户审核
- 实现映射验证机制

### 风险2：内存优化影响处理速度
**缓解措施：**
- 平衡内存使用和处理速度
- 提供可配置的内存限制参数
- 实现自适应处理策略

## 📈 成功标准

1. **接口映射成功率**：策略验证成功率提升到95%以上
2. **内存控制**：峰值内存使用不超过200MB
3. **处理速度**：总体转换时间控制在10分钟以内
4. **稳定性**：连续处理20个配置文件无内存泄漏
