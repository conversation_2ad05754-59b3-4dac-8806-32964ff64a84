#!/usr/bin/env python3
"""
接口映射验证失败原因分析器
"""

import os
import json
import re
from typing import Dict, List, Set, Tuple
from collections import defaultdict

class InterfaceMappingFailureAnalyzer:
    def __init__(self):
        self.config_files = [
            "FortiGate-100F_7-6_3510_202505161613.conf",
            "FortiGate-401F_7-4_2795_202507011110.conf", 
            "FortiGate-601E_7-4_2795_202506101906.conf"
        ]
        
        self.analysis_results = {}
    
    def analyze_fortigate_config(self, config_file: str) -> Dict:
        """分析FortiGate配置文件中的接口使用情况"""
        print(f"\n🔍 分析配置文件: {config_file}")
        
        if not os.path.exists(config_file):
            print(f"❌ 配置文件不存在: {config_file}")
            return {}
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            analysis = {
                'physical_interfaces': set(),
                'vlan_interfaces': set(),
                'zone_interfaces': set(),
                'policy_interfaces': set(),
                'route_interfaces': set(),
                'all_referenced_interfaces': set()
            }
            
            # 1. 分析接口定义
            self._analyze_interface_definitions(content, analysis)
            
            # 2. 分析安全区域中的接口引用
            self._analyze_zone_interfaces(content, analysis)
            
            # 3. 分析策略中的接口引用
            self._analyze_policy_interfaces(content, analysis)
            
            # 4. 分析路由中的接口引用
            self._analyze_route_interfaces(content, analysis)
            
            # 5. 汇总所有引用的接口
            analysis['all_referenced_interfaces'] = (
                analysis['zone_interfaces'] | 
                analysis['policy_interfaces'] | 
                analysis['route_interfaces']
            )
            
            print(f"   物理接口: {len(analysis['physical_interfaces'])}个")
            print(f"   VLAN接口: {len(analysis['vlan_interfaces'])}个")
            print(f"   区域引用接口: {len(analysis['zone_interfaces'])}个")
            print(f"   策略引用接口: {len(analysis['policy_interfaces'])}个")
            print(f"   路由引用接口: {len(analysis['route_interfaces'])}个")
            print(f"   总引用接口: {len(analysis['all_referenced_interfaces'])}个")
            
            return analysis
            
        except Exception as e:
            print(f"❌ 分析失败: {str(e)}")
            return {}
    
    def _analyze_interface_definitions(self, content: str, analysis: Dict):
        """分析接口定义"""
        in_interface_section = False
        current_interface = None
        
        for line in content.split('\n'):
            line = line.strip()
            
            if line == "config system interface":
                in_interface_section = True
                continue
            elif in_interface_section and line == "end":
                in_interface_section = False
                continue
            
            if in_interface_section:
                if line.startswith('edit "') and line.endswith('"'):
                    current_interface = line[6:-1]
                    
                    # 判断接口类型
                    if '.' in current_interface or current_interface.startswith('vlan'):
                        analysis['vlan_interfaces'].add(current_interface)
                    else:
                        analysis['physical_interfaces'].add(current_interface)
    
    def _analyze_zone_interfaces(self, content: str, analysis: Dict):
        """分析安全区域中的接口引用"""
        in_zone_section = False
        current_zone = None
        
        for line in content.split('\n'):
            line = line.strip()
            
            if line == "config system zone":
                in_zone_section = True
                continue
            elif in_zone_section and line == "end":
                in_zone_section = False
                continue
            
            if in_zone_section:
                if line.startswith('edit "') and line.endswith('"'):
                    current_zone = line[6:-1]
                elif line.startswith('set interface ') and current_zone:
                    # 解析接口列表
                    interface_str = line[14:]
                    interfaces = self._parse_interface_list(interface_str)
                    analysis['zone_interfaces'].update(interfaces)
    
    def _analyze_policy_interfaces(self, content: str, analysis: Dict):
        """分析策略中的接口引用"""
        in_policy_section = False
        current_policy = None
        
        for line in content.split('\n'):
            line = line.strip()
            
            if line == "config firewall policy":
                in_policy_section = True
                continue
            elif in_policy_section and line == "end":
                in_policy_section = False
                continue
            
            if in_policy_section:
                if line.startswith('edit '):
                    current_policy = line[5:]
                elif line.startswith('set srcintf ') and current_policy:
                    interface_str = line[12:]
                    interfaces = self._parse_interface_list(interface_str)
                    analysis['policy_interfaces'].update(interfaces)
                elif line.startswith('set dstintf ') and current_policy:
                    interface_str = line[12:]
                    interfaces = self._parse_interface_list(interface_str)
                    analysis['policy_interfaces'].update(interfaces)
    
    def _analyze_route_interfaces(self, content: str, analysis: Dict):
        """分析路由中的接口引用"""
        in_route_section = False
        
        for line in content.split('\n'):
            line = line.strip()
            
            if line == "config router static":
                in_route_section = True
                continue
            elif in_route_section and line == "end":
                in_route_section = False
                continue
            
            if in_route_section:
                if line.startswith('set device '):
                    interface_str = line[11:].strip('"')
                    analysis['route_interfaces'].add(interface_str)
    
    def _parse_interface_list(self, interface_str: str) -> Set[str]:
        """解析接口列表字符串"""
        interfaces = set()
        
        # 移除引号并分割
        interface_str = interface_str.strip()
        if interface_str.startswith('"') and interface_str.endswith('"'):
            interface_str = interface_str[1:-1]
        
        # 处理多个接口（空格分隔）
        for interface in interface_str.split():
            interface = interface.strip('"').strip("'")
            if interface:
                interfaces.add(interface)
        
        return interfaces
    
    def load_interface_mapping(self, config_file: str) -> Dict[str, str]:
        """加载对应的接口映射文件"""
        base_name = os.path.splitext(config_file)[0]
        mapping_file = f"mappings/interface_mapping_{base_name}.json"
        
        if not os.path.exists(mapping_file):
            print(f"❌ 映射文件不存在: {mapping_file}")
            return {}
        
        try:
            with open(mapping_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ 加载映射文件失败: {str(e)}")
            return {}
    
    def analyze_mapping_gaps(self, config_file: str, config_analysis: Dict, interface_mapping: Dict) -> Dict:
        """分析接口映射缺口"""
        print(f"\n🔍 分析接口映射缺口: {config_file}")
        
        gaps = {
            'missing_mappings': set(),
            'unused_mappings': set(),
            'zone_missing': set(),
            'policy_missing': set(),
            'route_missing': set(),
            'coverage_stats': {}
        }
        
        # 1. 找出缺少映射的接口
        all_referenced = config_analysis['all_referenced_interfaces']
        mapped_interfaces = set(interface_mapping.keys())
        
        gaps['missing_mappings'] = all_referenced - mapped_interfaces
        gaps['unused_mappings'] = mapped_interfaces - all_referenced
        
        # 2. 按类型分析缺失
        gaps['zone_missing'] = config_analysis['zone_interfaces'] - mapped_interfaces
        gaps['policy_missing'] = config_analysis['policy_interfaces'] - mapped_interfaces
        gaps['route_missing'] = config_analysis['route_interfaces'] - mapped_interfaces
        
        # 3. 计算覆盖率统计
        total_referenced = len(all_referenced)
        mapped_referenced = len(all_referenced & mapped_interfaces)
        
        gaps['coverage_stats'] = {
            'total_referenced': total_referenced,
            'mapped_referenced': mapped_referenced,
            'coverage_rate': (mapped_referenced / total_referenced * 100) if total_referenced > 0 else 0,
            'missing_count': len(gaps['missing_mappings']),
            'unused_count': len(gaps['unused_mappings'])
        }
        
        print(f"   总引用接口: {total_referenced}")
        print(f"   已映射接口: {mapped_referenced}")
        print(f"   覆盖率: {gaps['coverage_stats']['coverage_rate']:.1f}%")
        print(f"   缺失映射: {len(gaps['missing_mappings'])}个")
        print(f"   未使用映射: {len(gaps['unused_mappings'])}个")
        
        if gaps['missing_mappings']:
            print(f"   缺失的接口: {sorted(gaps['missing_mappings'])}")
        
        return gaps
    
    def generate_mapping_recommendations(self, config_file: str, gaps: Dict) -> Dict:
        """生成接口映射推荐"""
        print(f"\n💡 生成映射推荐: {config_file}")
        
        recommendations = {
            'add_mappings': {},
            'remove_mappings': [],
            'priority_mappings': {},
            'auto_mappings': {}
        }
        
        # 1. 为缺失的接口推荐映射
        missing_interfaces = gaps['missing_mappings']
        
        # 获取可用的NTOS接口
        available_ntos_interfaces = self._get_available_ntos_interfaces()
        
        # 按优先级分配映射
        priority_interfaces = ['mgmt', 'port1', 'port23', 'port24', 'x1', 'x2']
        
        for interface in sorted(missing_interfaces):
            if interface in priority_interfaces:
                # 高优先级接口使用标准映射
                ntos_interface = self._get_standard_mapping(interface)
                if ntos_interface:
                    recommendations['priority_mappings'][interface] = ntos_interface
                    print(f"   🔥 高优先级: {interface} -> {ntos_interface}")
            else:
                # 其他接口自动分配
                if available_ntos_interfaces:
                    ntos_interface = available_ntos_interfaces.pop(0)
                    recommendations['auto_mappings'][interface] = ntos_interface
                    print(f"   🔄 自动分配: {interface} -> {ntos_interface}")
        
        # 2. 推荐移除未使用的映射
        unused_mappings = gaps['unused_mappings']
        recommendations['remove_mappings'] = list(unused_mappings)
        
        if unused_mappings:
            print(f"   🗑️ 建议移除: {sorted(unused_mappings)}")
        
        # 3. 合并所有推荐的映射
        recommendations['add_mappings'].update(recommendations['priority_mappings'])
        recommendations['add_mappings'].update(recommendations['auto_mappings'])
        
        return recommendations
    
    def _get_standard_mapping(self, interface: str) -> str:
        """获取标准接口映射"""
        standard_mappings = {
            "mgmt": "Ge0/0",
            "port1": "Ge0/3",
            "port23": "Ge0/1",
            "port24": "Ge0/2",
            "x1": "TenGe0/0",
            "x2": "TenGe0/1",
            "x3": "TenGe0/2",
            "x4": "TenGe0/3"
        }
        return standard_mappings.get(interface)
    
    def _get_available_ntos_interfaces(self) -> List[str]:
        """获取可用的NTOS接口列表"""
        interfaces = []
        
        # Gigabit接口
        for i in range(4, 24):  # Ge0/4 到 Ge0/23
            interfaces.append(f"Ge0/{i}")
        
        return interfaces
    
    def analyze_all_configs(self):
        """分析所有配置文件"""
        print("🚀 开始分析所有配置文件的接口映射问题")
        print("=" * 80)
        
        for config_file in self.config_files:
            print(f"\n{'='*60}")
            print(f"分析配置文件: {config_file}")
            print(f"{'='*60}")
            
            # 1. 分析配置文件
            config_analysis = self.analyze_fortigate_config(config_file)
            if not config_analysis:
                continue
            
            # 2. 加载接口映射
            interface_mapping = self.load_interface_mapping(config_file)
            if not interface_mapping:
                continue
            
            # 3. 分析映射缺口
            gaps = self.analyze_mapping_gaps(config_file, config_analysis, interface_mapping)
            
            # 4. 生成推荐
            recommendations = self.generate_mapping_recommendations(config_file, gaps)
            
            # 5. 保存分析结果
            self.analysis_results[config_file] = {
                'config_analysis': config_analysis,
                'interface_mapping': interface_mapping,
                'gaps': gaps,
                'recommendations': recommendations
            }
        
        return self.analysis_results
    
    def generate_summary_report(self):
        """生成汇总报告"""
        print(f"\n{'='*80}")
        print("📊 接口映射问题分析汇总报告")
        print(f"{'='*80}")
        
        if not self.analysis_results:
            print("❌ 没有分析结果")
            return
        
        total_configs = len(self.analysis_results)
        total_missing = 0
        total_coverage = 0
        
        print(f"📋 详细分析结果:")
        for config_file, result in self.analysis_results.items():
            gaps = result['gaps']
            coverage_stats = gaps['coverage_stats']
            
            print(f"\n🔹 {config_file}:")
            print(f"   覆盖率: {coverage_stats['coverage_rate']:.1f}%")
            print(f"   缺失映射: {coverage_stats['missing_count']}个")
            print(f"   未使用映射: {coverage_stats['unused_count']}个")
            
            total_missing += coverage_stats['missing_count']
            total_coverage += coverage_stats['coverage_rate']
        
        avg_coverage = total_coverage / total_configs if total_configs > 0 else 0
        
        print(f"\n📊 总体统计:")
        print(f"   平均覆盖率: {avg_coverage:.1f}%")
        print(f"   总缺失映射: {total_missing}个")
        print(f"   需要优化的配置: {total_configs}个")
        
        print(f"\n🎯 主要问题:")
        print(f"   1. 接口映射覆盖率不足（平均{avg_coverage:.1f}%）")
        print(f"   2. 策略和区域引用了未映射的接口")
        print(f"   3. 不同FortiGate型号的接口命名差异")
        
        print(f"\n💡 解决方案:")
        print(f"   1. 实现智能接口映射推荐算法")
        print(f"   2. 创建设备型号特定的映射模板")
        print(f"   3. 增强接口映射验证和错误报告")

def main():
    """主函数"""
    print("🚀 接口映射验证失败原因分析器")
    print("=" * 80)
    
    analyzer = InterfaceMappingFailureAnalyzer()
    
    # 分析所有配置文件
    results = analyzer.analyze_all_configs()
    
    # 生成汇总报告
    analyzer.generate_summary_report()
    
    print(f"\n{'='*80}")
    print("🎉 接口映射问题分析完成")
    print(f"{'='*80}")

if __name__ == "__main__":
    main()
