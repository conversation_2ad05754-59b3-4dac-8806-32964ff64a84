#!/usr/bin/env python3
"""
接口映射文件生成器 - 为不同的FortiGate配置文件生成对应的接口映射JSON文件
"""

import os
import json
import re
from typing import Dict, List, Set

class InterfaceMappingGenerator:
    def __init__(self):
        self.config_files = [
            "FortiGate-100F_7-6_3510_202505161613.conf",
            "FortiGate-401F_7-4_2795_202507011110.conf", 
            "FortiGate-601E_7-4_2795_202506101906.conf",
            "Pass-Mask-MTU-FW-1_7-2_1639_202506271000.conf"
        ]
        
        # 标准接口映射模板
        self.standard_mappings = {
            # 管理接口
            "mgmt": "Ge0/0",
            
            # 标准端口映射 (FortiGate port -> NTOS interface)
            "port1": "Ge0/3",
            "port2": "Ge0/4", 
            "port3": "Ge0/5",
            "port4": "Ge0/6",
            "port5": "Ge0/7",
            "port6": "Ge0/8",
            "port7": "Ge0/9",
            "port8": "Ge0/10",
            "port9": "Ge0/11",
            "port10": "Ge0/12",
            
            # 高速端口映射
            "port23": "Ge0/1",
            "port24": "Ge0/2",
            
            # 万兆端口映射
            "x1": "TenGe0/0",
            "x2": "TenGe0/1",
            "x3": "TenGe0/2",
            "x4": "TenGe0/3",
        }
    
    def analyze_fortigate_interfaces(self, config_file: str) -> Set[str]:
        """分析FortiGate配置文件中的接口"""
        print(f"🔍 分析配置文件: {config_file}")
        
        if not os.path.exists(config_file):
            print(f"❌ 配置文件不存在: {config_file}")
            return set()
        
        interfaces = set()
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找接口配置段
            in_interface_section = False
            
            for line in content.split('\n'):
                line = line.strip()
                
                if line == "config system interface":
                    in_interface_section = True
                    continue
                elif in_interface_section and line == "end":
                    in_interface_section = False
                    continue
                
                if in_interface_section and line.startswith('edit "') and line.endswith('"'):
                    interface_name = line[6:-1]
                    # 只收集物理接口，排除VLAN子接口
                    if not ('.' in interface_name or interface_name.startswith('vlan')):
                        interfaces.add(interface_name)
            
            print(f"   找到物理接口: {len(interfaces)}个")
            for interface in sorted(interfaces):
                print(f"      - {interface}")
            
            return interfaces
            
        except Exception as e:
            print(f"❌ 分析配置文件失败: {str(e)}")
            return set()
    
    def generate_interface_mapping(self, config_file: str, interfaces: Set[str]) -> Dict[str, str]:
        """为特定配置文件生成接口映射"""
        print(f"\n📋 生成接口映射: {config_file}")
        
        mapping = {}
        
        # 1. 添加标准映射中存在的接口
        for fg_interface, ntos_interface in self.standard_mappings.items():
            if fg_interface in interfaces:
                mapping[fg_interface] = ntos_interface
                print(f"   ✅ {fg_interface} -> {ntos_interface}")
        
        # 2. 处理未映射的接口
        unmapped_interfaces = interfaces - set(mapping.keys())
        if unmapped_interfaces:
            print(f"   ⚠️ 未映射的接口: {sorted(unmapped_interfaces)}")
            
            # 为未映射的接口分配可用的NTOS接口
            used_ntos_interfaces = set(mapping.values())
            available_ntos_interfaces = self._get_available_ntos_interfaces(used_ntos_interfaces)
            
            for fg_interface in sorted(unmapped_interfaces):
                if available_ntos_interfaces:
                    ntos_interface = available_ntos_interfaces.pop(0)
                    mapping[fg_interface] = ntos_interface
                    print(f"   🔄 {fg_interface} -> {ntos_interface} (自动分配)")
                else:
                    print(f"   ❌ {fg_interface}: 无可用NTOS接口")
        
        print(f"   📊 映射完成: {len(mapping)}个接口")
        return mapping
    
    def _get_available_ntos_interfaces(self, used_interfaces: Set[str]) -> List[str]:
        """获取可用的NTOS接口列表"""
        all_ntos_interfaces = []
        
        # Gigabit接口
        for i in range(13, 24):  # Ge0/13 到 Ge0/23
            all_ntos_interfaces.append(f"Ge0/{i}")
        
        # 10Gigabit接口 (z5100s只支持TenGe0/0-TenGe0/3)
        # 不添加TenGe0/4-TenGe0/7，因为z5100s不支持
        
        # 返回未使用的接口
        return [iface for iface in all_ntos_interfaces if iface not in used_interfaces]
    
    def save_mapping_file(self, config_file: str, mapping: Dict[str, str]) -> str:
        """保存接口映射文件"""
        # 生成映射文件名
        base_name = os.path.splitext(config_file)[0]
        mapping_file = f"mappings/interface_mapping_{base_name}.json"
        
        # 确保mappings目录存在
        os.makedirs("mappings", exist_ok=True)
        
        try:
            with open(mapping_file, 'w', encoding='utf-8') as f:
                json.dump(mapping, f, indent=2, ensure_ascii=False)
            
            print(f"✅ 映射文件已保存: {mapping_file}")
            return mapping_file
            
        except Exception as e:
            print(f"❌ 保存映射文件失败: {str(e)}")
            return ""
    
    def validate_mapping(self, mapping: Dict[str, str]) -> bool:
        """验证接口映射的有效性"""
        print(f"\n🔍 验证接口映射")
        
        issues = []
        
        # 检查重复的NTOS接口
        ntos_interfaces = list(mapping.values())
        duplicates = set([x for x in ntos_interfaces if ntos_interfaces.count(x) > 1])
        if duplicates:
            issues.append(f"重复的NTOS接口: {duplicates}")
        
        # 检查接口命名规范
        for fg_interface, ntos_interface in mapping.items():
            if not re.match(r'^(Ge\d+/\d+|TenGe\d+/\d+)$', ntos_interface):
                issues.append(f"NTOS接口命名不规范: {ntos_interface}")
        
        # 检查关键接口
        critical_interfaces = ["mgmt", "port1", "port23", "port24", "x1"]
        missing_critical = [iface for iface in critical_interfaces if iface not in mapping]
        if missing_critical:
            issues.append(f"缺少关键接口: {missing_critical}")
        
        if issues:
            print(f"❌ 发现问题:")
            for issue in issues:
                print(f"   - {issue}")
            return False
        else:
            print(f"✅ 映射验证通过")
            return True
    
    def generate_all_mappings(self):
        """为所有配置文件生成接口映射"""
        print("🚀 开始生成所有配置文件的接口映射")
        print("=" * 80)
        
        results = {}
        
        for config_file in self.config_files:
            print(f"\n{'='*60}")
            print(f"处理配置文件: {config_file}")
            print(f"{'='*60}")
            
            # 1. 分析接口
            interfaces = self.analyze_fortigate_interfaces(config_file)
            if not interfaces:
                print(f"❌ 跳过 {config_file}: 无法分析接口")
                continue
            
            # 2. 生成映射
            mapping = self.generate_interface_mapping(config_file, interfaces)
            if not mapping:
                print(f"❌ 跳过 {config_file}: 无法生成映射")
                continue
            
            # 3. 验证映射
            if not self.validate_mapping(mapping):
                print(f"⚠️ {config_file}: 映射验证失败，但仍会保存")
            
            # 4. 保存映射文件
            mapping_file = self.save_mapping_file(config_file, mapping)
            
            results[config_file] = {
                'interfaces_count': len(interfaces),
                'mapping_count': len(mapping),
                'mapping_file': mapping_file,
                'interfaces': sorted(interfaces),
                'mapping': mapping
            }
        
        return results
    
    def generate_summary_report(self, results: Dict):
        """生成汇总报告"""
        print(f"\n{'='*80}")
        print("📊 接口映射生成汇总报告")
        print(f"{'='*80}")
        
        total_configs = len(self.config_files)
        successful_configs = len(results)
        
        print(f"配置文件总数: {total_configs}")
        print(f"成功处理数: {successful_configs}")
        print(f"成功率: {successful_configs/total_configs*100:.1f}%")
        
        if results:
            print(f"\n📋 详细统计:")
            for config_file, data in results.items():
                print(f"\n🔹 {config_file}:")
                print(f"   接口数量: {data['interfaces_count']}")
                print(f"   映射数量: {data['mapping_count']}")
                print(f"   映射文件: {data['mapping_file']}")
                
                # 显示关键接口映射
                critical_mappings = {}
                for fg_interface in ["mgmt", "port1", "port23", "port24", "x1"]:
                    if fg_interface in data['mapping']:
                        critical_mappings[fg_interface] = data['mapping'][fg_interface]
                
                if critical_mappings:
                    print(f"   关键映射: {critical_mappings}")
        
        print(f"\n🎯 结论:")
        if successful_configs == total_configs:
            print("✅ 所有配置文件的接口映射生成成功")
        elif successful_configs > 0:
            print(f"⚠️ 部分配置文件处理成功 ({successful_configs}/{total_configs})")
        else:
            print("❌ 所有配置文件处理失败")
        
        print(f"\n📁 生成的映射文件位置: mappings/")

def main():
    """主函数"""
    print("🚀 FortiGate接口映射文件生成器")
    print("=" * 80)
    
    generator = InterfaceMappingGenerator()
    
    # 生成所有映射文件
    results = generator.generate_all_mappings()
    
    # 生成汇总报告
    generator.generate_summary_report(results)
    
    print(f"\n{'='*80}")
    print("🎉 接口映射文件生成完成")
    print(f"{'='*80}")

if __name__ == "__main__":
    main()
