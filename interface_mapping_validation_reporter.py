#!/usr/bin/env python3
"""
接口映射验证错误报告器 - 提供详细的错误报告和修复建议
"""

import os
import json
import re
from typing import Dict, List, Set, Tuple

class InterfaceMappingValidationReporter:
    def __init__(self):
        self.config_file = "FortiGate-100F_7-6_3510_202505161613.conf"
        self.mapping_file = "mappings/interface_mapping_FortiGate-100F_7-6_3510_202505161613.json"
        
    def analyze_detailed_validation_failure(self):
        """详细分析接口映射验证失败的原因"""
        print("🔍 详细分析接口映射验证失败原因")
        print("=" * 80)
        
        # 1. 加载配置和映射
        config_analysis = self._analyze_config_file()
        mapping_data = self._load_mapping_file()
        
        if not config_analysis or not mapping_data:
            return
        
        # 2. 分析策略中的接口引用
        policy_interfaces = self._analyze_policy_interfaces()
        
        # 3. 分析区域中的接口引用
        zone_interfaces = self._analyze_zone_interfaces()
        
        # 4. 检查映射覆盖情况
        self._check_mapping_coverage(policy_interfaces, zone_interfaces, mapping_data)
        
        # 5. 生成详细的验证报告
        self._generate_validation_report(config_analysis, mapping_data, policy_interfaces, zone_interfaces)
    
    def _analyze_config_file(self) -> Dict:
        """分析配置文件"""
        print(f"\n📋 分析配置文件: {self.config_file}")
        
        if not os.path.exists(self.config_file):
            print(f"❌ 配置文件不存在")
            return {}
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            analysis = {
                'total_lines': len(content.split('\n')),
                'has_policies': 'config firewall policy' in content,
                'has_zones': 'config system zone' in content,
                'has_interfaces': 'config system interface' in content,
                'has_routes': 'config router static' in content
            }
            
            print(f"   总行数: {analysis['total_lines']}")
            print(f"   包含策略配置: {'✅' if analysis['has_policies'] else '❌'}")
            print(f"   包含区域配置: {'✅' if analysis['has_zones'] else '❌'}")
            print(f"   包含接口配置: {'✅' if analysis['has_interfaces'] else '❌'}")
            print(f"   包含路由配置: {'✅' if analysis['has_routes'] else '❌'}")
            
            return analysis
            
        except Exception as e:
            print(f"❌ 分析失败: {str(e)}")
            return {}
    
    def _load_mapping_file(self) -> Dict:
        """加载映射文件"""
        print(f"\n📋 加载映射文件: {self.mapping_file}")
        
        if not os.path.exists(self.mapping_file):
            print(f"❌ 映射文件不存在")
            return {}
        
        try:
            with open(self.mapping_file, 'r', encoding='utf-8') as f:
                mapping = json.load(f)
            
            print(f"   映射条目数: {len(mapping)}")
            print(f"   映射的FortiGate接口: {sorted(mapping.keys())}")
            print(f"   映射的NTOS接口: {sorted(set(mapping.values()))}")
            
            return mapping
            
        except Exception as e:
            print(f"❌ 加载失败: {str(e)}")
            return {}
    
    def _analyze_policy_interfaces(self) -> Dict:
        """分析策略中的接口引用"""
        print(f"\n📋 分析策略中的接口引用")
        
        if not os.path.exists(self.config_file):
            return {}
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            policy_interfaces = {
                'source_interfaces': set(),
                'destination_interfaces': set(),
                'all_policy_interfaces': set(),
                'policy_details': []
            }
            
            in_policy_section = False
            current_policy = None
            current_policy_data = {}
            
            for line_num, line in enumerate(content.split('\n'), 1):
                line = line.strip()
                
                if line == "config firewall policy":
                    in_policy_section = True
                    continue
                elif in_policy_section and line == "end":
                    in_policy_section = False
                    continue
                
                if in_policy_section:
                    if line.startswith('edit '):
                        if current_policy and current_policy_data:
                            policy_interfaces['policy_details'].append(current_policy_data)
                        
                        current_policy = line[5:]
                        current_policy_data = {
                            'policy_id': current_policy,
                            'line_number': line_num,
                            'srcintf': [],
                            'dstintf': []
                        }
                    
                    elif line.startswith('set srcintf ') and current_policy:
                        interface_str = line[12:]
                        interfaces = self._parse_interface_list(interface_str)
                        current_policy_data['srcintf'] = list(interfaces)
                        policy_interfaces['source_interfaces'].update(interfaces)
                        policy_interfaces['all_policy_interfaces'].update(interfaces)
                    
                    elif line.startswith('set dstintf ') and current_policy:
                        interface_str = line[12:]
                        interfaces = self._parse_interface_list(interface_str)
                        current_policy_data['dstintf'] = list(interfaces)
                        policy_interfaces['destination_interfaces'].update(interfaces)
                        policy_interfaces['all_policy_interfaces'].update(interfaces)
                    
                    elif line == "next" and current_policy_data:
                        policy_interfaces['policy_details'].append(current_policy_data)
                        current_policy_data = {}
            
            print(f"   策略总数: {len(policy_interfaces['policy_details'])}")
            print(f"   源接口引用: {len(policy_interfaces['source_interfaces'])}个")
            print(f"   目标接口引用: {len(policy_interfaces['destination_interfaces'])}个")
            print(f"   总接口引用: {len(policy_interfaces['all_policy_interfaces'])}个")
            print(f"   引用的接口: {sorted(policy_interfaces['all_policy_interfaces'])}")
            
            return policy_interfaces
            
        except Exception as e:
            print(f"❌ 分析失败: {str(e)}")
            return {}
    
    def _analyze_zone_interfaces(self) -> Dict:
        """分析区域中的接口引用"""
        print(f"\n📋 分析区域中的接口引用")
        
        if not os.path.exists(self.config_file):
            return {}
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            zone_interfaces = {
                'zones': {},
                'all_zone_interfaces': set(),
                'zone_details': []
            }
            
            in_zone_section = False
            current_zone = None
            
            for line_num, line in enumerate(content.split('\n'), 1):
                line = line.strip()
                
                if line == "config system zone":
                    in_zone_section = True
                    continue
                elif in_zone_section and line == "end":
                    in_zone_section = False
                    continue
                
                if in_zone_section:
                    if line.startswith('edit "') and line.endswith('"'):
                        current_zone = line[6:-1]
                        zone_interfaces['zones'][current_zone] = set()
                    
                    elif line.startswith('set interface ') and current_zone:
                        interface_str = line[14:]
                        interfaces = self._parse_interface_list(interface_str)
                        zone_interfaces['zones'][current_zone].update(interfaces)
                        zone_interfaces['all_zone_interfaces'].update(interfaces)
                        
                        zone_interfaces['zone_details'].append({
                            'zone_name': current_zone,
                            'line_number': line_num,
                            'interfaces': list(interfaces)
                        })
            
            print(f"   区域总数: {len(zone_interfaces['zones'])}")
            print(f"   总接口引用: {len(zone_interfaces['all_zone_interfaces'])}个")
            print(f"   引用的接口: {sorted(zone_interfaces['all_zone_interfaces'])}")
            
            for zone_name, interfaces in zone_interfaces['zones'].items():
                print(f"   区域 {zone_name}: {sorted(interfaces)}")
            
            return zone_interfaces
            
        except Exception as e:
            print(f"❌ 分析失败: {str(e)}")
            return {}
    
    def _parse_interface_list(self, interface_str: str) -> Set[str]:
        """解析接口列表"""
        interfaces = set()
        interface_str = interface_str.strip()
        
        if interface_str.startswith('"') and interface_str.endswith('"'):
            interface_str = interface_str[1:-1]
        
        for interface in interface_str.split():
            interface = interface.strip('"').strip("'")
            if interface:
                interfaces.add(interface)
        
        return interfaces
    
    def _check_mapping_coverage(self, policy_interfaces: Dict, zone_interfaces: Dict, mapping_data: Dict):
        """检查映射覆盖情况"""
        print(f"\n📋 检查映射覆盖情况")
        
        all_referenced_interfaces = (
            policy_interfaces.get('all_policy_interfaces', set()) |
            zone_interfaces.get('all_zone_interfaces', set())
        )
        
        mapped_interfaces = set(mapping_data.keys())
        
        missing_mappings = all_referenced_interfaces - mapped_interfaces
        unused_mappings = mapped_interfaces - all_referenced_interfaces
        
        print(f"   总引用接口: {len(all_referenced_interfaces)}")
        print(f"   已映射接口: {len(mapped_interfaces)}")
        print(f"   缺失映射: {len(missing_mappings)}")
        print(f"   未使用映射: {len(unused_mappings)}")
        
        if missing_mappings:
            print(f"   ❌ 缺失映射的接口: {sorted(missing_mappings)}")
        
        if unused_mappings:
            print(f"   ⚠️ 未使用的映射: {sorted(unused_mappings)}")
        
        coverage_rate = len(all_referenced_interfaces & mapped_interfaces) / len(all_referenced_interfaces) * 100 if all_referenced_interfaces else 100
        print(f"   📊 映射覆盖率: {coverage_rate:.1f}%")
    
    def _generate_validation_report(self, config_analysis: Dict, mapping_data: Dict, 
                                  policy_interfaces: Dict, zone_interfaces: Dict):
        """生成详细的验证报告"""
        print(f"\n{'='*80}")
        print("📊 接口映射验证详细报告")
        print(f"{'='*80}")
        
        all_referenced = (
            policy_interfaces.get('all_policy_interfaces', set()) |
            zone_interfaces.get('all_zone_interfaces', set())
        )
        
        mapped_interfaces = set(mapping_data.keys())
        missing_mappings = all_referenced - mapped_interfaces
        
        print(f"🔍 验证失败原因分析:")
        
        if missing_mappings:
            print(f"   ❌ 主要原因: 缺少 {len(missing_mappings)} 个接口的映射")
            print(f"   缺失的接口: {sorted(missing_mappings)}")
            
            print(f"\n💡 修复建议:")
            for interface in sorted(missing_mappings):
                suggested_mapping = self._suggest_interface_mapping(interface)
                print(f"   {interface} -> {suggested_mapping}")
        
        print(f"\n📋 详细的策略接口使用情况:")
        for policy_detail in policy_interfaces.get('policy_details', [])[:5]:  # 只显示前5个
            policy_id = policy_detail['policy_id']
            srcintf = policy_detail['srcintf']
            dstintf = policy_detail['dstintf']
            
            print(f"   策略 {policy_id}:")
            print(f"      源接口: {srcintf}")
            print(f"      目标接口: {dstintf}")
            
            # 检查映射状态
            for interface in srcintf + dstintf:
                if interface in mapping_data:
                    print(f"         ✅ {interface} -> {mapping_data[interface]}")
                else:
                    print(f"         ❌ {interface} -> 缺少映射")
        
        print(f"\n🎯 解决方案:")
        print(f"   1. 运行智能接口映射推荐算法")
        print(f"   2. 手动添加缺失的接口映射")
        print(f"   3. 验证映射文件的正确性")
        print(f"   4. 重新运行转换")
    
    def _suggest_interface_mapping(self, interface: str) -> str:
        """建议接口映射"""
        # 基于接口名称模式建议映射
        if interface.startswith('wan'):
            return "Ge0/1"
        elif interface.startswith('lan') or interface == 'internal':
            return "Ge0/2"
        elif interface.startswith('dmz'):
            return "Ge0/3"
        elif interface.startswith('t') and interface[1:].isdigit():
            return f"TenGe0/{int(interface[1:]) - 1}"
        elif interface.startswith('port'):
            port_num = interface[4:]
            if port_num.isdigit():
                return f"Ge0/{int(port_num) + 3}"
        else:
            return "Ge0/4"  # 默认映射
    
    def generate_fix_script(self):
        """生成修复脚本"""
        print(f"\n💡 生成接口映射修复脚本")
        
        # 基于分析结果生成修复脚本
        missing_interfaces = {"wan1", "t1", "t2"}  # 从之前的分析得出
        
        fix_script = {
            "wan1": "Ge0/1",
            "t1": "TenGe0/0", 
            "t2": "TenGe0/1"
        }
        
        print(f"建议的修复映射:")
        for fg_interface, ntos_interface in fix_script.items():
            print(f"   {fg_interface} -> {ntos_interface}")
        
        # 保存修复脚本
        fix_file = "interface_mapping_fix.json"
        try:
            with open(fix_file, 'w', encoding='utf-8') as f:
                json.dump(fix_script, f, indent=2, ensure_ascii=False)
            print(f"✅ 修复脚本已保存: {fix_file}")
        except Exception as e:
            print(f"❌ 保存修复脚本失败: {str(e)}")

def main():
    """主函数"""
    print("🚀 接口映射验证错误报告器")
    print("=" * 80)
    
    reporter = InterfaceMappingValidationReporter()
    
    # 执行详细分析
    reporter.analyze_detailed_validation_failure()
    
    # 生成修复脚本
    reporter.generate_fix_script()
    
    print(f"\n{'='*80}")
    print("🎉 接口映射验证错误分析完成")
    print(f"{'='*80}")

if __name__ == "__main__":
    main()
