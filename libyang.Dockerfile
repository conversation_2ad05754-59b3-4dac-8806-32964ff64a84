# libyang.Dockerfile
# 用于构建 libyang 库的基础镜像
FROM python:3.9-slim

# 安装编译libyang所需依赖
RUN apt-get update && apt-get install -y \
    libpcre3-dev \
    libpcre2-dev \
    cmake \
    pkg-config \
    git \
    && rm -rf /var/lib/apt/lists/*

# 克隆和编译libyang
WORKDIR /build
RUN git clone https://github.com/CESNET/libyang.git && \
    cd libyang && \
    mkdir build && cd build && \
    cmake .. && \
    make && \
    make install && \
    ldconfig

# 清理不必要的文件以减小镜像大小
RUN rm -rf /build/libyang/.git && \
    apt-get purge -y git cmake pkg-config && \
    apt-get autoremove -y && \
    apt-get clean 