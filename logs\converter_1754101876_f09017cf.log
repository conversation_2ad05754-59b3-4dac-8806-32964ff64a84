2025-08-02 10:31:16 - INFO - 配置管理器：检测到开发环境
2025-08-02 10:31:16 - INFO - 配置管理器已初始化
2025-08-02 10:31:16 - INFO - 缓存管理器已初始化
2025-08-02 10:31:16 - INFO - 性能监控器已初始化
2025-08-02 10:31:16 - INFO - 缓存管理器：缓存已创建
2025-08-02 10:31:16 - INFO - 模板管理器已初始化
2025-08-02 10:31:16 - INFO - YANG管理器已初始化
2025-08-02 10:31:16 - INFO - 性能监控器已初始化
2025-08-02 10:31:16 - INFO - 内存管理器已初始化
2025-08-02 10:31:16 - INFO - 异常注册表已初始化
2025-08-02 10:31:16 - INFO - 错误处理器已初始化
2025-08-02 10:31:16 - INFO - 转换工作流程已初始化
2025-08-02 10:31:16 - INFO - 找到模板文件: E:\code\config-converter\engine\data\input\configData\z5100s\R11\Config\running.xml
2025-08-02 10:31:16 - INFO - 找到模板文件: E:\code\config-converter\engine\data\input\configData\z5100s\R11\Config\running.xml
2025-08-02 10:31:16 - INFO - 模板加载成功: 文件=E:\code\config-converter\engine\data\input\configData\z5100s\R11\Config\running.xml
2025-08-02 10:31:16 - WARNING - 消息中缺少参数: urn:ruijie:ntos
2025-08-02 10:31:16 - WARNING - 消息中缺少参数: urn
2025-08-02 10:31:16 - WARNING - 消息中缺少参数: urn:ruijie:ntos
2025-08-02 10:31:16 - WARNING - 消息中缺少参数: urn
2025-08-02 10:31:16 - WARNING - 消息中缺少参数: urn:ruijie:ntos
2025-08-02 10:31:16 - WARNING - 消息中缺少参数: urn
2025-08-02 10:31:16 - WARNING - 命名空间不匹配: 实际='None', 期望='urn:ruijie:ntos'
2025-08-02 10:31:16 - INFO - 模板文件命名空间保持原样，不进行修改
2025-08-02 10:31:16 - INFO - 模板加载成功: 型号=z5100s, 版本=R11, 类型=running
2025-08-02 10:31:16 - INFO - 模板准备完成: E:\code\config-converter\engine\data\input\configData\z5100s\R11\Config\running.xml
2025-08-02 10:31:16 - INFO - 找到YANG文件
2025-08-02 10:31:16 - INFO - 模式已加载
2025-08-02 10:31:16 - INFO - YANG管理器：模式已加载
2025-08-02 10:31:16 - INFO - YANG管理器：命名空间已加载
2025-08-02 10:31:16 - INFO - YANG模型准备完成: E:\code\config-converter\engine\data\input\yang_models\z5100s\R11
2025-08-02 10:31:16 - INFO - name_mapping_manager.initialized
2025-08-02 10:31:16 - INFO - 管道管理器：已初始化
2025-08-02 10:31:16 - WARNING - Interface mapping file not found: data\interface_mapping.json
2025-08-02 10:31:16 - INFO - 转换策略已初始化
2025-08-02 10:31:16 - INFO - Fortigate策略阶段已初始化
2025-08-02 10:31:16 - INFO - XML模板集成阶段已初始化
2025-08-02 10:31:16 - INFO - 设置当前输出文件路径: output/test_fixed_output.xml
2025-08-02 10:31:16 - INFO - 解析CLI文件: Pass-Mask-MTU-FW-1_7-2_1639_202506271000.conf
2025-08-02 10:31:16 - INFO - 开始解析配置文件
2025-08-02 10:31:16 - WARNING - Sensitive Command Detected
2025-08-02 10:31:17 - WARNING - Sensitive Command Detected
2025-08-02 10:31:17 - INFO - 成功读取文件，共 19633 行
2025-08-02 10:31:17 - INFO - 配置文件读取成功，共 19633 行
2025-08-02 10:31:17 - INFO - fortigate.detected_transparent_mode_from_header
2025-08-02 10:31:17 - INFO - 开始解析系统全局配置部分
2025-08-02 10:31:17 - INFO - fortigate.set_hostname
2025-08-02 10:31:17 - INFO - fortigate.set_timezone
2025-08-02 10:31:17 - INFO - fortigate.system_settings_section_end
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 开始解析接口配置部分
2025-08-02 10:31:17 - INFO - 解析接口: mgmt
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口IP (掩码): *************/24
2025-08-02 10:31:17 - INFO - 设置接口访问控制: ['ping', 'https', 'ssh']
2025-08-02 10:31:17 - INFO - 设置接口类型: physical
2025-08-02 10:31:17 - INFO - 设置接口角色: lan
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 1
2025-08-02 10:31:17 - INFO - 完成接口 mgmt 解析
2025-08-02 10:31:17 - INFO - 解析接口: ha
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口类型: physical
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 2
2025-08-02 10:31:17 - INFO - 完成接口 ha 解析
2025-08-02 10:31:17 - INFO - 解析接口: port1
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口类型: physical
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 3
2025-08-02 10:31:17 - INFO - 完成接口 port1 解析
2025-08-02 10:31:17 - INFO - 解析接口: port2
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口类型: physical
2025-08-02 10:31:17 - INFO - 设置接口角色: dmz
2025-08-02 10:31:17 - WARNING - 警告: 接口 'port2' 使用了不支持的角色 'dmz'，NTOS只支持lan和wan角色
2025-08-02 10:31:17 - INFO - 接口 'port2' 的角色 'dmz' 将被转换为 'lan'
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 4
2025-08-02 10:31:17 - INFO - 完成接口 port2 解析
2025-08-02 10:31:17 - INFO - 解析接口: port3
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口类型: physical
2025-08-02 10:31:17 - INFO - 设置接口角色: lan
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 5
2025-08-02 10:31:17 - INFO - 完成接口 port3 解析
2025-08-02 10:31:17 - INFO - 解析接口: port4
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口类型: physical
2025-08-02 10:31:17 - INFO - 设置接口角色: lan
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 6
2025-08-02 10:31:17 - INFO - 完成接口 port4 解析
2025-08-02 10:31:17 - INFO - 解析接口: port5
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口类型: physical
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 7
2025-08-02 10:31:17 - INFO - 完成接口 port5 解析
2025-08-02 10:31:17 - INFO - 解析接口: port6
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口类型: physical
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 8
2025-08-02 10:31:17 - INFO - 完成接口 port6 解析
2025-08-02 10:31:17 - INFO - 解析接口: port7
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口类型: physical
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 9
2025-08-02 10:31:17 - INFO - 完成接口 port7 解析
2025-08-02 10:31:17 - INFO - 解析接口: port8
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口类型: physical
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 10
2025-08-02 10:31:17 - INFO - 完成接口 port8 解析
2025-08-02 10:31:17 - INFO - 解析接口: port9
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口类型: physical
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 11
2025-08-02 10:31:17 - INFO - 完成接口 port9 解析
2025-08-02 10:31:17 - INFO - 解析接口: port10
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口类型: physical
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 12
2025-08-02 10:31:17 - INFO - 完成接口 port10 解析
2025-08-02 10:31:17 - INFO - 解析接口: port11
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口类型: physical
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 13
2025-08-02 10:31:17 - INFO - 完成接口 port11 解析
2025-08-02 10:31:17 - INFO - 解析接口: port12
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口类型: physical
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 14
2025-08-02 10:31:17 - INFO - 完成接口 port12 解析
2025-08-02 10:31:17 - INFO - 解析接口: port13
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口类型: physical
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 15
2025-08-02 10:31:17 - INFO - 完成接口 port13 解析
2025-08-02 10:31:17 - INFO - 解析接口: port14
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口类型: physical
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 16
2025-08-02 10:31:17 - INFO - 完成接口 port14 解析
2025-08-02 10:31:17 - INFO - 解析接口: port15
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口类型: physical
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 17
2025-08-02 10:31:17 - INFO - 完成接口 port15 解析
2025-08-02 10:31:17 - INFO - 解析接口: port16
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口类型: physical
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 18
2025-08-02 10:31:17 - INFO - 完成接口 port16 解析
2025-08-02 10:31:17 - INFO - 解析接口: port17
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口类型: physical
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 19
2025-08-02 10:31:17 - INFO - 完成接口 port17 解析
2025-08-02 10:31:17 - INFO - 解析接口: port18
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口类型: physical
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 20
2025-08-02 10:31:17 - INFO - 完成接口 port18 解析
2025-08-02 10:31:17 - INFO - 解析接口: port19
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口类型: physical
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 21
2025-08-02 10:31:17 - INFO - 完成接口 port19 解析
2025-08-02 10:31:17 - INFO - 解析接口: port20
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口类型: physical
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 22
2025-08-02 10:31:17 - INFO - 完成接口 port20 解析
2025-08-02 10:31:17 - INFO - 解析接口: port21
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口类型: physical
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 23
2025-08-02 10:31:17 - INFO - 完成接口 port21 解析
2025-08-02 10:31:17 - INFO - 解析接口: port22
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口类型: physical
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 24
2025-08-02 10:31:17 - INFO - 完成接口 port22 解析
2025-08-02 10:31:17 - INFO - 解析接口: port23
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口IP (掩码): **********/23
2025-08-02 10:31:17 - INFO - 设置接口访问控制: ['ping', 'https', 'ssh', 'snmp', 'http']
2025-08-02 10:31:17 - INFO - 设置接口类型: physical
2025-08-02 10:31:17 - INFO - 设置接口角色: dmz
2025-08-02 10:31:17 - WARNING - 警告: 接口 'port23' 使用了不支持的角色 'dmz'，NTOS只支持lan和wan角色
2025-08-02 10:31:17 - INFO - 接口 'port23' 的角色 'dmz' 将被转换为 'lan'
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 25
2025-08-02 10:31:17 - INFO - 完成接口 port23 解析
2025-08-02 10:31:17 - INFO - 解析接口: port24
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口IP (掩码): **********/20
2025-08-02 10:31:17 - INFO - 设置接口访问控制: ['ping', 'snmp']
2025-08-02 10:31:17 - INFO - 设置接口类型: physical
2025-08-02 10:31:17 - INFO - 设置安全模式: captive-portal
2025-08-02 10:31:17 - INFO - 设置接口角色: lan
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 26
2025-08-02 10:31:17 - INFO - 完成接口 port24 解析
2025-08-02 10:31:17 - INFO - 解析接口: x1
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口IP (掩码): ************/30
2025-08-02 10:31:17 - INFO - 设置接口访问控制: ['ping', 'snmp']
2025-08-02 10:31:17 - INFO - 设置接口类型: physical
2025-08-02 10:31:17 - INFO - 设置接口角色: wan
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 27
2025-08-02 10:31:17 - INFO - 完成接口 x1 解析
2025-08-02 10:31:17 - INFO - 解析接口: x2
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口访问控制: ['snmp']
2025-08-02 10:31:17 - INFO - 设置接口类型: physical
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 28
2025-08-02 10:31:17 - INFO - 完成接口 x2 解析
2025-08-02 10:31:17 - INFO - 解析接口: x3
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口类型: physical
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 29
2025-08-02 10:31:17 - INFO - 完成接口 x3 解析
2025-08-02 10:31:17 - INFO - 解析接口: x4
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口类型: physical
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 30
2025-08-02 10:31:17 - INFO - 完成接口 x4 解析
2025-08-02 10:31:17 - INFO - 解析接口: modem
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口模式: pppoe
2025-08-02 10:31:17 - INFO - 设置接口状态: down
2025-08-02 10:31:17 - INFO - 设置接口启用状态: False
2025-08-02 10:31:17 - INFO - 设置接口类型: physical
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 31
2025-08-02 10:31:17 - INFO - 完成接口 modem 解析
2025-08-02 10:31:17 - INFO - 解析接口: naf.root
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口类型: tunnel
2025-08-02 10:31:17 - INFO - 信息: 接口 'naf.root' 类型从 'tunnel' 转换为 'physical' 以兼容NTOS
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 32
2025-08-02 10:31:17 - INFO - 完成接口 naf.root 解析
2025-08-02 10:31:17 - INFO - 解析接口: l2t.root
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口类型: tunnel
2025-08-02 10:31:17 - INFO - 信息: 接口 'l2t.root' 类型从 'tunnel' 转换为 'physical' 以兼容NTOS
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 33
2025-08-02 10:31:17 - INFO - 完成接口 l2t.root 解析
2025-08-02 10:31:17 - INFO - 解析接口: ssl.root
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口类型: tunnel
2025-08-02 10:31:17 - INFO - 信息: 接口 'ssl.root' 类型从 'tunnel' 转换为 'physical' 以兼容NTOS
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 34
2025-08-02 10:31:17 - INFO - 完成接口 ssl.root 解析
2025-08-02 10:31:17 - INFO - 解析接口: BOS
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口类型: switch
2025-08-02 10:31:17 - INFO - 信息: 接口 'BOS' 类型从 'switch' 转换为 'physical' 以兼容NTOS
2025-08-02 10:31:17 - INFO - 设置接口角色: lan
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 35
2025-08-02 10:31:17 - INFO - 完成接口 BOS 解析
2025-08-02 10:31:17 - INFO - 解析接口: SSL_VPN
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口IP (掩码): *******/32
2025-08-02 10:31:17 - INFO - 设置接口类型: loopback
2025-08-02 10:31:17 - INFO - 信息: 接口 'SSL_VPN' 类型从 'loopback' 转换为 'physical' 以兼容NTOS
2025-08-02 10:31:17 - INFO - 设置接口角色: lan
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 38
2025-08-02 10:31:17 - INFO - 完成接口 SSL_VPN 解析
2025-08-02 10:31:17 - INFO - 解析接口: BTL_YSMSPR
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口IP (掩码): *********/24
2025-08-02 10:31:17 - INFO - 设置接口访问控制: ['ping', 'snmp']
2025-08-02 10:31:17 - INFO - 设置接口角色: lan
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 52
2025-08-02 10:31:17 - INFO - 设置子接口所属的物理接口: port24
2025-08-02 10:31:17 - INFO - 设置子接口VLAN ID: 2200
2025-08-02 10:31:17 - INFO - 完成接口 BTL_YSMSPR 解析
2025-08-02 10:31:17 - INFO - 解析接口: BTL_YMK
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口IP (掩码): *********/24
2025-08-02 10:31:17 - INFO - 设置接口访问控制: ['ping', 'snmp']
2025-08-02 10:31:17 - INFO - 设置接口角色: lan
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 54
2025-08-02 10:31:17 - INFO - 设置子接口所属的物理接口: port24
2025-08-02 10:31:17 - INFO - 设置子接口VLAN ID: 2500
2025-08-02 10:31:17 - INFO - 完成接口 BTL_YMK 解析
2025-08-02 10:31:17 - INFO - 解析接口: BTL_WIFI
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口IP (掩码): *********/20
2025-08-02 10:31:17 - INFO - 设置接口访问控制: ['ping', 'snmp']
2025-08-02 10:31:17 - INFO - 设置安全模式: captive-portal
2025-08-02 10:31:17 - INFO - 设置接口角色: lan
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 51
2025-08-02 10:31:17 - INFO - 设置子接口所属的物理接口: port24
2025-08-02 10:31:17 - INFO - 设置子接口VLAN ID: 2100
2025-08-02 10:31:17 - INFO - 完成接口 BTL_WIFI 解析
2025-08-02 10:31:17 - INFO - 解析接口: BTL_TEL
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口IP (掩码): *********/20
2025-08-02 10:31:17 - INFO - 设置接口访问控制: ['ping', 'snmp']
2025-08-02 10:31:17 - INFO - 设置接口角色: lan
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 39
2025-08-02 10:31:17 - INFO - 设置子接口所属的物理接口: port24
2025-08-02 10:31:17 - INFO - 设置子接口VLAN ID: 3100
2025-08-02 10:31:17 - INFO - 完成接口 BTL_TEL 解析
2025-08-02 10:31:17 - INFO - 解析接口: BTL_RKT
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口IP (掩码): *********/22
2025-08-02 10:31:17 - INFO - 设置接口访问控制: ['ping', 'snmp']
2025-08-02 10:31:17 - INFO - 设置安全模式: captive-portal
2025-08-02 10:31:17 - INFO - 设置接口角色: lan
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 50
2025-08-02 10:31:17 - INFO - 设置子接口所属的物理接口: port24
2025-08-02 10:31:17 - INFO - 设置子接口VLAN ID: 2000
2025-08-02 10:31:17 - INFO - 完成接口 BTL_RKT 解析
2025-08-02 10:31:17 - INFO - 解析接口: BTL_MYO
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口IP (掩码): *********/24
2025-08-02 10:31:17 - INFO - 设置接口访问控制: ['ping', 'snmp']
2025-08-02 10:31:17 - INFO - 设置接口角色: lan
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 55
2025-08-02 10:31:17 - INFO - 设置子接口所属的物理接口: port24
2025-08-02 10:31:17 - INFO - 设置子接口VLAN ID: 2600
2025-08-02 10:31:17 - INFO - 完成接口 BTL_MYO 解析
2025-08-02 10:31:17 - INFO - 解析接口: BTL_MGMT
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口IP (掩码): *********/20
2025-08-02 10:31:17 - INFO - 设置接口访问控制: ['ping', 'snmp']
2025-08-02 10:31:17 - INFO - 设置接口角色: lan
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 36
2025-08-02 10:31:17 - INFO - 设置子接口所属的物理接口: port24
2025-08-02 10:31:17 - INFO - 设置子接口VLAN ID: 3000
2025-08-02 10:31:17 - INFO - 完成接口 BTL_MGMT 解析
2025-08-02 10:31:17 - INFO - 解析接口: BTL_GUV
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口IP (掩码): *********/24
2025-08-02 10:31:17 - INFO - 设置接口访问控制: ['ping', 'snmp']
2025-08-02 10:31:17 - INFO - 设置接口角色: lan
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 53
2025-08-02 10:31:17 - INFO - 设置子接口所属的物理接口: port24
2025-08-02 10:31:17 - INFO - 设置子接口VLAN ID: 2400
2025-08-02 10:31:17 - INFO - 完成接口 BTL_GUV 解析
2025-08-02 10:31:17 - INFO - 解析接口: BTL_ATC
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口IP (掩码): *********/24
2025-08-02 10:31:17 - INFO - 设置接口访问控制: ['ping', 'snmp']
2025-08-02 10:31:17 - INFO - 设置接口角色: lan
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 56
2025-08-02 10:31:17 - INFO - 设置子接口所属的物理接口: port24
2025-08-02 10:31:17 - INFO - 设置子接口VLAN ID: 2700
2025-08-02 10:31:17 - INFO - 完成接口 BTL_ATC 解析
2025-08-02 10:31:17 - INFO - 解析接口: BTL_BIDB
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口IP (掩码): *********/24
2025-08-02 10:31:17 - INFO - 设置接口访问控制: ['ping', 'snmp']
2025-08-02 10:31:17 - INFO - 设置接口角色: lan
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 57
2025-08-02 10:31:17 - INFO - 设置子接口所属的物理接口: port24
2025-08-02 10:31:17 - INFO - 设置子接口VLAN ID: 1010
2025-08-02 10:31:17 - INFO - 完成接口 BTL_BIDB 解析
2025-08-02 10:31:17 - INFO - 解析接口: BTL_CAM
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口IP (掩码): *********/20
2025-08-02 10:31:17 - INFO - 设置接口访问控制: ['ping', 'snmp']
2025-08-02 10:31:17 - INFO - 设置接口角色: lan
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 48
2025-08-02 10:31:17 - INFO - 设置子接口所属的物理接口: port24
2025-08-02 10:31:17 - INFO - 设置子接口VLAN ID: 3200
2025-08-02 10:31:17 - INFO - 完成接口 BTL_CAM 解析
2025-08-02 10:31:17 - INFO - 解析接口: REAL_IP
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口IP (掩码): ************/24
2025-08-02 10:31:17 - INFO - 设置接口访问控制: ['ping', 'snmp']
2025-08-02 10:31:17 - INFO - 设置接口角色: dmz
2025-08-02 10:31:17 - WARNING - 警告: 接口 'REAL_IP' 使用了不支持的角色 'dmz'，NTOS只支持lan和wan角色
2025-08-02 10:31:17 - INFO - 接口 'REAL_IP' 的角色 'dmz' 将被转换为 'lan'
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 37
2025-08-02 10:31:17 - INFO - 设置子接口所属的物理接口: port23
2025-08-02 10:31:17 - INFO - 设置子接口VLAN ID: 135
2025-08-02 10:31:17 - INFO - 解析辅助IP配置块
2025-08-02 10:31:17 - INFO - 完成接口 REAL_IP 解析
2025-08-02 10:31:17 - INFO - 解析接口: PROXY
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口IP (掩码): ***********/24
2025-08-02 10:31:17 - INFO - 设置接口访问控制: ['ping', 'snmp']
2025-08-02 10:31:17 - INFO - 设置接口角色: lan
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 49
2025-08-02 10:31:17 - INFO - 设置子接口所属的物理接口: port23
2025-08-02 10:31:17 - INFO - 设置子接口VLAN ID: 1001
2025-08-02 10:31:17 - INFO - 完成接口 PROXY 解析
2025-08-02 10:31:17 - INFO - 解析接口: TargitasTest
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口IP (掩码): ***********/24
2025-08-02 10:31:17 - INFO - 设置接口访问控制: ['ping', 'https', 'http']
2025-08-02 10:31:17 - INFO - 设置接口角色: dmz
2025-08-02 10:31:17 - WARNING - 警告: 接口 'TargitasTest' 使用了不支持的角色 'dmz'，NTOS只支持lan和wan角色
2025-08-02 10:31:17 - INFO - 接口 'TargitasTest' 的角色 'dmz' 将被转换为 'lan'
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 58
2025-08-02 10:31:17 - INFO - 设置子接口所属的物理接口: port23
2025-08-02 10:31:17 - INFO - 设置子接口VLAN ID: 33
2025-08-02 10:31:17 - INFO - 完成接口 TargitasTest 解析
2025-08-02 10:31:17 - INFO - 解析接口: NAC_LOGIN
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口IP (掩码): ***********/24
2025-08-02 10:31:17 - INFO - 设置接口角色: lan
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 59
2025-08-02 10:31:17 - INFO - 设置子接口所属的物理接口: port24
2025-08-02 10:31:17 - INFO - 设置子接口VLAN ID: 2
2025-08-02 10:31:17 - INFO - 完成接口 NAC_LOGIN 解析
2025-08-02 10:31:17 - INFO - 解析接口: YESILTURT_RKT
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口IP (掩码): **********/20
2025-08-02 10:31:17 - INFO - 设置接口访问控制: ['ping', 'snmp']
2025-08-02 10:31:17 - INFO - 设置安全模式: captive-portal
2025-08-02 10:31:17 - INFO - 设置接口角色: lan
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 47
2025-08-02 10:31:17 - INFO - 设置子接口所属的物理接口: x2
2025-08-02 10:31:17 - INFO - 设置子接口VLAN ID: 114
2025-08-02 10:31:17 - INFO - 完成接口 YESILTURT_RKT 解析
2025-08-02 10:31:17 - INFO - 解析接口: YESILYURT_MYO
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口IP (掩码): **********/20
2025-08-02 10:31:17 - INFO - 设置接口访问控制: ['ping', 'snmp']
2025-08-02 10:31:17 - INFO - 设置安全模式: captive-portal
2025-08-02 10:31:17 - INFO - 设置接口角色: lan
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 45
2025-08-02 10:31:17 - INFO - 设置子接口所属的物理接口: x2
2025-08-02 10:31:17 - INFO - 设置子接口VLAN ID: 115
2025-08-02 10:31:17 - INFO - 完成接口 YESILYURT_MYO 解析
2025-08-02 10:31:17 - INFO - 解析接口: KALE_MYO
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口IP (掩码): **********/22
2025-08-02 10:31:17 - INFO - 设置接口访问控制: ['ping', 'snmp']
2025-08-02 10:31:17 - INFO - 设置安全模式: captive-portal
2025-08-02 10:31:17 - INFO - 设置接口角色: lan
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 46
2025-08-02 10:31:17 - INFO - 设置子接口所属的物理接口: x2
2025-08-02 10:31:17 - INFO - 设置子接口VLAN ID: 110
2025-08-02 10:31:17 - INFO - 完成接口 KALE_MYO 解析
2025-08-02 10:31:17 - INFO - 解析接口: HEKIMHAN_MYO
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口IP (掩码): **********/20
2025-08-02 10:31:17 - INFO - 设置接口访问控制: ['ping', 'snmp']
2025-08-02 10:31:17 - INFO - 设置安全模式: captive-portal
2025-08-02 10:31:17 - INFO - 设置接口角色: lan
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 44
2025-08-02 10:31:17 - INFO - 设置子接口所属的物理接口: x2
2025-08-02 10:31:17 - INFO - 设置子接口VLAN ID: 119
2025-08-02 10:31:17 - INFO - 完成接口 HEKIMHAN_MYO 解析
2025-08-02 10:31:17 - INFO - 解析接口: DOGANSEHIR_MYO
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口IP (掩码): **********/22
2025-08-02 10:31:17 - INFO - 设置接口访问控制: ['ping', 'snmp']
2025-08-02 10:31:17 - INFO - 设置安全模式: captive-portal
2025-08-02 10:31:17 - INFO - 设置接口角色: lan
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 43
2025-08-02 10:31:17 - INFO - 设置子接口所属的物理接口: x2
2025-08-02 10:31:17 - INFO - 设置子接口VLAN ID: 116
2025-08-02 10:31:17 - INFO - 完成接口 DOGANSEHIR_MYO 解析
2025-08-02 10:31:17 - INFO - 解析接口: DARENDE_MYO
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口IP (掩码): **********/20
2025-08-02 10:31:17 - INFO - 设置接口访问控制: ['ping', 'snmp']
2025-08-02 10:31:17 - INFO - 设置安全模式: captive-portal
2025-08-02 10:31:17 - INFO - 设置接口角色: lan
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 42
2025-08-02 10:31:17 - INFO - 设置子接口所属的物理接口: x2
2025-08-02 10:31:17 - INFO - 设置子接口VLAN ID: 117
2025-08-02 10:31:17 - INFO - 完成接口 DARENDE_MYO 解析
2025-08-02 10:31:17 - INFO - 解析接口: ARAPGIR_MYO
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口IP (掩码): **********/20
2025-08-02 10:31:17 - INFO - 设置接口访问控制: ['ping', 'snmp']
2025-08-02 10:31:17 - INFO - 设置安全模式: captive-portal
2025-08-02 10:31:17 - INFO - 设置接口角色: lan
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 40
2025-08-02 10:31:17 - INFO - 设置子接口所属的物理接口: x2
2025-08-02 10:31:17 - INFO - 设置子接口VLAN ID: 112
2025-08-02 10:31:17 - INFO - 完成接口 ARAPGIR_MYO 解析
2025-08-02 10:31:17 - INFO - 解析接口: AKCADAG_MYO
2025-08-02 10:31:17 - INFO - 设置接口虚拟域: root
2025-08-02 10:31:17 - INFO - 设置接口IP (掩码): **********/22
2025-08-02 10:31:17 - INFO - 设置接口访问控制: ['ping', 'snmp']
2025-08-02 10:31:17 - INFO - 设置安全模式: captive-portal
2025-08-02 10:31:17 - INFO - 设置接口角色: lan
2025-08-02 10:31:17 - INFO - 设置SNMP索引: 41
2025-08-02 10:31:17 - INFO - 设置子接口所属的物理接口: x2
2025-08-02 10:31:17 - INFO - 设置子接口VLAN ID: 120
2025-08-02 10:31:17 - INFO - 完成接口 AKCADAG_MYO 解析
2025-08-02 10:31:17 - INFO - 接口配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - WARNING - 警告: 行 1124 包含可疑字符，可能存在命令注入风险: set name "Assets & Identities"
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - WARNING - 警告: 行 1412 包含可疑字符，可能存在命令注入风险: set name "Assets & Identities"
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - WARNING - 警告: 行 1705 包含可疑字符，可能存在命令注入风险: set name "Users & Devices"
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - WARNING - 警告: 行 1992 包含可疑字符，可能存在命令注入风险: set name "Users & Devices"
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - WARNING - 警告: 行 2352 包含可疑字符，可能存在命令注入风险: set name "Users & Devices"
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 开始解析DNS配置部分
2025-08-02 10:31:17 - INFO - 设置主DNS服务器: **********0
2025-08-02 10:31:17 - INFO - 设置辅DNS服务器: 1.1.1.1
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - WARNING - 警告: 行 2647 包含可疑字符，可能存在命令注入风险: <meta http-equiv=\"X-UA-Compatible\" content=\"IE=8; IE=EDGE\">
2025-08-02 10:31:17 - WARNING - 警告: 行 2651 包含可疑字符，可能存在命令注入风险: height: 100%;
2025-08-02 10:31:17 - WARNING - 警告: 行 2652 包含可疑字符，可能存在命令注入风险: font-family: Helvetica, Arial, sans-serif;
2025-08-02 10:31:17 - WARNING - 警告: 行 2653 包含可疑字符，可能存在命令注入风险: color: #6a6a6a;
2025-08-02 10:31:17 - WARNING - 警告: 行 2654 包含可疑字符，可能存在命令注入风险: margin: 0;
2025-08-02 10:31:17 - WARNING - 警告: 行 2655 包含可疑字符，可能存在命令注入风险: display: flex;
2025-08-02 10:31:17 - WARNING - 警告: 行 2656 包含可疑字符，可能存在命令注入风险: align-items: center;
2025-08-02 10:31:17 - WARNING - 警告: 行 2657 包含可疑字符，可能存在命令注入风险: justify-content: center;
2025-08-02 10:31:17 - WARNING - 警告: 行 2660 包含可疑字符，可能存在命令注入风险: color: #262626;
2025-08-02 10:31:17 - WARNING - 警告: 行 2661 包含可疑字符，可能存在命令注入风险: vertical-align: baseline;
2025-08-02 10:31:17 - WARNING - 警告: 行 2662 包含可疑字符，可能存在命令注入风险: margin: .2em;
2025-08-02 10:31:17 - WARNING - 警告: 行 2663 包含可疑字符，可能存在命令注入风险: border-style: solid;
2025-08-02 10:31:17 - WARNING - 警告: 行 2664 包含可疑字符，可能存在命令注入风险: border-width: 1px;
2025-08-02 10:31:17 - WARNING - 警告: 行 2665 包含可疑字符，可能存在命令注入风险: border-color: #a9a9a9;
2025-08-02 10:31:17 - WARNING - 警告: 行 2666 包含可疑字符，可能存在命令注入风险: background-color: #fff;
2025-08-02 10:31:17 - WARNING - 警告: 行 2667 包含可疑字符，可能存在命令注入风险: box-sizing: border-box;
2025-08-02 10:31:17 - WARNING - 警告: 行 2668 包含可疑字符，可能存在命令注入风险: padding: 2px .5em;
2025-08-02 10:31:17 - WARNING - 警告: 行 2669 包含可疑字符，可能存在命令注入风险: appearance: none;
2025-08-02 10:31:17 - WARNING - 警告: 行 2670 包含可疑字符，可能存在命令注入风险: border-radius: 0;
2025-08-02 10:31:17 - WARNING - 警告: 行 2673 包含可疑字符，可能存在命令注入风险: border-color: #646464;
2025-08-02 10:31:17 - WARNING - 警告: 行 2674 包含可疑字符，可能存在命令注入风险: box-shadow: 0 0 1px 0 #a2a2a2;
2025-08-02 10:31:17 - WARNING - 警告: 行 2675 包含可疑字符，可能存在命令注入风险: outline: 0;
2025-08-02 10:31:17 - WARNING - 警告: 行 2678 包含可疑字符，可能存在命令注入风险: padding: .5em 1em;
2025-08-02 10:31:17 - WARNING - 警告: 行 2679 包含可疑字符，可能存在命令注入风险: border: 1px solid;
2025-08-02 10:31:17 - WARNING - 警告: 行 2680 包含可疑字符，可能存在命令注入风险: border-radius: 3px;
2025-08-02 10:31:17 - WARNING - 警告: 行 2681 包含可疑字符，可能存在命令注入风险: min-width: 6em;
2025-08-02 10:31:17 - WARNING - 警告: 行 2682 包含可疑字符，可能存在命令注入风险: font-weight: 400;
2025-08-02 10:31:17 - WARNING - 警告: 行 2683 包含可疑字符，可能存在命令注入风险: font-size: .8em;
2025-08-02 10:31:17 - WARNING - 警告: 行 2684 包含可疑字符，可能存在命令注入风险: cursor: pointer;
2025-08-02 10:31:17 - WARNING - 警告: 行 2687 包含可疑字符，可能存在命令注入风险: color: #fff;
2025-08-02 10:31:17 - WARNING - 警告: 行 2688 包含可疑字符，可能存在命令注入风险: background-color: rgb(47, 113, 178);
2025-08-02 10:31:17 - WARNING - 警告: 行 2689 包含可疑字符，可能存在命令注入风险: border-color: rgb(34, 103, 173);
2025-08-02 10:31:17 - WARNING - 警告: 行 2692 包含可疑字符，可能存在命令注入风险: height: 500px;
2025-08-02 10:31:17 - WARNING - 警告: 行 2693 包含可疑字符，可能存在命令注入风险: width: 600px;
2025-08-02 10:31:17 - WARNING - 警告: 行 2694 包含可疑字符，可能存在命令注入风险: padding: 0;
2025-08-02 10:31:17 - WARNING - 警告: 行 2695 包含可疑字符，可能存在命令注入风险: margin: 10px;
2025-08-02 10:31:17 - WARNING - 警告: 行 2698 包含可疑字符，可能存在命令注入风险: background: url(%%IMAGE:logo_v3_fguard_app%%) no-repeat left center;
2025-08-02 10:31:17 - WARNING - 警告: 行 2699 包含可疑字符，可能存在命令注入风险: height: 267px;
2025-08-02 10:31:17 - WARNING - 警告: 行 2700 包含可疑字符，可能存在命令注入风险: object-fit: contain;
2025-08-02 10:31:17 - WARNING - 警告: 行 2703 包含可疑字符，可能存在命令注入风险: background-color: #fff;
2025-08-02 10:31:17 - WARNING - 警告: 行 2704 包含可疑字符，可能存在命令注入风险: border-spacing: 0;
2025-08-02 10:31:17 - WARNING - 警告: 行 2705 包含可疑字符，可能存在命令注入风险: margin: 1em;
2025-08-02 10:31:17 - WARNING - 警告: 行 2708 包含可疑字符，可能存在命令注入风险: white-space: nowrap;
2025-08-02 10:31:17 - WARNING - 警告: 行 2709 包含可疑字符，可能存在命令注入风险: color: rgba(0,0,0,.5);
2025-08-02 10:31:17 - WARNING - 警告: 行 2712 包含可疑字符，可能存在命令注入风险: vertical-align: top;
2025-08-02 10:31:17 - WARNING - 警告: 行 2715 包含可疑字符，可能存在命令注入风险: padding: .3em .3em;
2025-08-02 10:31:17 - WARNING - 警告: 行 2718 包含可疑字符，可能存在命令注入风险: display: table-row;
2025-08-02 10:31:17 - WARNING - 警告: 行 2721 包含可疑字符，可能存在命令注入风险: display: table-cell;
2025-08-02 10:31:17 - WARNING - 警告: 行 2722 包含可疑字符，可能存在命令注入风险: width: 20%;
2025-08-02 10:31:17 - WARNING - 警告: 行 2725 包含可疑字符，可能存在命令注入风险: display: inline;
2025-08-02 10:31:17 - WARNING - 警告: 行 2728 包含可疑字符，可能存在命令注入风险: width: auto;
2025-08-02 10:31:17 - WARNING - 警告: 行 2729 包含可疑字符，可能存在命令注入风险: max-width: 100%;
2025-08-02 10:31:17 - WARNING - 警告: 行 2730 包含可疑字符，可能存在命令注入风险: display: inline-flex;
2025-08-02 10:31:17 - WARNING - 警告: 行 2731 包含可疑字符，可能存在命令注入风险: align-items: baseline;
2025-08-02 10:31:17 - WARNING - 警告: 行 2732 包含可疑字符，可能存在命令注入风险: virtical-align: top;
2025-08-02 10:31:17 - WARNING - 警告: 行 2733 包含可疑字符，可能存在命令注入风险: box-sizing: border-box;
2025-08-02 10:31:17 - WARNING - 警告: 行 2734 包含可疑字符，可能存在命令注入风险: margin: .3em;
2025-08-02 10:31:17 - WARNING - 警告: 行 2737 包含可疑字符，可能存在命令注入风险: width: 230px;
2025-08-02 10:31:17 - WARNING - 警告: 行 2740 包含可疑字符，可能存在命令注入风险: display: inline-flex;
2025-08-02 10:31:17 - WARNING - 警告: 行 2741 包含可疑字符，可能存在命令注入风险: justify-content: flex-start;
2025-08-02 10:31:17 - WARNING - 警告: 行 2744 包含可疑字符，可能存在命令注入风险: margin: 1em;
2025-08-02 10:31:17 - WARNING - 警告: 行 2747 包含可疑字符，可能存在命令注入风险: overflow: auto;
2025-08-02 10:31:17 - WARNING - 警告: 行 2748 包含可疑字符，可能存在命令注入风险: height: 150px;
2025-08-02 10:31:17 - WARNING - 警告: 行 2749 包含可疑字符，可能存在命令注入风险: border: 1px solid rgb(200, 200, 200);
2025-08-02 10:31:17 - WARNING - 警告: 行 2750 包含可疑字符，可能存在命令注入风险: padding: 5px;
2025-08-02 10:31:17 - WARNING - 警告: 行 2751 包含可疑字符，可能存在命令注入风险: font-size: 1em;
2025-08-02 10:31:17 - WARNING - 警告: 行 2754 包含可疑字符，可能存在命令注入风险: text-align: center;
2025-08-02 10:31:17 - WARNING - 警告: 行 2757 包含可疑字符，可能存在命令注入风险: margin: 1em 1.5em;
2025-08-02 10:31:17 - WARNING - 警告: 行 2760 包含可疑字符，可能存在命令注入风险: display: flex;
2025-08-02 10:31:17 - WARNING - 警告: 行 2763 包含可疑字符，可能存在命令注入风险: flex-direction: column;
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:17 - INFO - 未知 配置部分结束
2025-08-02 10:31:18 - WARNING - 警告: 行 4915 包含可疑字符，可能存在命令注入风险: edit "Botnet-C&C.Server"
2025-08-02 10:31:21 - INFO - 未知 配置部分结束
2025-08-02 10:31:21 - INFO - 未知 配置部分结束
2025-08-02 10:31:21 - INFO - 开始解析系统日志配置部分
2025-08-02 10:31:21 - INFO - 设置系统日志 syslogd setting 状态: enable
2025-08-02 10:31:21 - INFO - 设置系统日志服务器:  "10.10.10.34
2025-08-02 10:31:21 - INFO - 系统日志 syslogd setting 解析完成
2025-08-02 10:31:21 - INFO - 开始解析FortiAnalyzer日志配置部分
2025-08-02 10:31:21 - INFO - 设置FortiAnalyzer fortianalyzer setting 状态: enable
2025-08-02 10:31:21 - INFO - 设置FortiAnalyzer服务器:  "10.10.10.4
2025-08-02 10:31:21 - INFO - 设置FortiAnalyzer上传选项: realtime
2025-08-02 10:31:21 - INFO - FortiAnalyzer fortianalyzer setting 解析完成
2025-08-02 10:31:21 - INFO - 未知 配置部分结束
2025-08-02 10:31:21 - INFO - 未知 配置部分结束
2025-08-02 10:31:21 - INFO - 未知 配置部分结束
2025-08-02 10:31:21 - INFO - 未知 配置部分结束
2025-08-02 10:31:21 - INFO - 未知 配置部分结束
2025-08-02 10:31:21 - INFO - 未知 配置部分结束
2025-08-02 10:31:21 - INFO - 未知 配置部分结束
2025-08-02 10:31:21 - INFO - 未知 配置部分结束
2025-08-02 10:31:21 - INFO - 未知 配置部分结束
2025-08-02 10:31:21 - INFO - 未知 配置部分结束
2025-08-02 10:31:21 - INFO - 未知 配置部分结束
2025-08-02 10:31:21 - INFO - 未知 配置部分结束
2025-08-02 10:31:21 - INFO - 未知 配置部分结束
2025-08-02 10:31:21 - INFO - 未知 配置部分结束
2025-08-02 10:31:21 - INFO - 未知 配置部分结束
2025-08-02 10:31:21 - INFO - 未知 配置部分结束
2025-08-02 10:31:21 - INFO - 未知 配置部分结束
2025-08-02 10:31:21 - INFO - 未知 配置部分结束
2025-08-02 10:31:21 - INFO - 未知 配置部分结束
2025-08-02 10:31:21 - INFO - 未知 配置部分结束
2025-08-02 10:31:21 - INFO - 未知 配置部分结束
2025-08-02 10:31:21 - INFO - 未知 配置部分结束
2025-08-02 10:31:21 - INFO - 未知 配置部分结束
2025-08-02 10:31:21 - INFO - 未知 配置部分结束
2025-08-02 10:31:21 - INFO - 未知 配置部分结束
2025-08-02 10:31:21 - INFO - 未知 配置部分结束
2025-08-02 10:31:21 - INFO - 未知 配置部分结束
2025-08-02 10:31:21 - INFO - 未知 配置部分结束
2025-08-02 10:31:21 - INFO - 未知 配置部分结束
2025-08-02 10:31:21 - WARNING - 警告: 行 8444 包含可疑字符，可能存在命令注入风险: edit "AV & IPS DB update"
2025-08-02 10:31:21 - INFO - 未知 配置部分结束
2025-08-02 10:31:21 - INFO - 未知 配置部分结束
2025-08-02 10:31:21 - INFO - 未知 配置部分结束
2025-08-02 10:31:21 - INFO - 未知 配置部分结束
2025-08-02 10:31:21 - INFO - 未知 配置部分结束
2025-08-02 10:31:21 - INFO - 未知 配置部分结束
2025-08-02 10:31:21 - INFO - 未知 配置部分结束
2025-08-02 10:31:21 - INFO - 未知 配置部分结束
2025-08-02 10:31:21 - INFO - 未知 配置部分结束
2025-08-02 10:31:21 - INFO - 未知 配置部分结束
2025-08-02 10:31:21 - INFO - 未知 配置部分结束
2025-08-02 10:31:21 - INFO - 未知 配置部分结束
2025-08-02 10:31:21 - INFO - 未知 配置部分结束
2025-08-02 10:31:21 - INFO - 未知 配置部分结束
2025-08-02 10:31:21 - INFO - 未知 配置部分结束
2025-08-02 10:31:21 - INFO - 未知 配置部分结束
2025-08-02 10:31:21 - INFO - 未知 配置部分结束
2025-08-02 10:31:21 - INFO - 未知 配置部分结束
2025-08-02 10:31:21 - INFO - 未知 配置部分结束
2025-08-02 10:31:22 - INFO - 未知 配置部分结束
2025-08-02 10:31:22 - INFO - 开始解析系统设置配置部分
2025-08-02 10:31:22 - INFO - fortigate.system_settings_section_end
2025-08-02 10:31:22 - INFO - Start Parsing Dhcp Server
2025-08-02 10:31:22 - INFO - Parsing Dhcp Server
2025-08-02 10:31:22 - INFO - Set Dhcp Default Gateway
2025-08-02 10:31:22 - INFO - Set Dhcp Netmask
2025-08-02 10:31:22 - INFO - Set Dhcp Interface
2025-08-02 10:31:22 - INFO - Parsing Dhcp Ip Range
2025-08-02 10:31:22 - INFO - Set Dhcp Start Ip
2025-08-02 10:31:22 - INFO - Set Dhcp End Ip
2025-08-02 10:31:22 - INFO - 开始解析区域配置部分
2025-08-02 10:31:22 - INFO - 解析区域: WAN
2025-08-02 10:31:22 - INFO - 设置区域 WAN 的接口: ['x1']
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: 'name': 'WAN', 'interfaces': ['x1']
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: 'name'
2025-08-02 10:31:22 - INFO - 完成区域 WAN 解析，包含 1 个接口
2025-08-02 10:31:22 - INFO - 解析区域: LAN
2025-08-02 10:31:22 - INFO - 设置区域 LAN 的内部通信策略: allow
2025-08-02 10:31:22 - INFO - 设置区域 LAN 的接口: ['BTL_ATC', 'BTL_BIDB', 'BTL_CAM', 'BTL_GUV', 'BTL_MYO', 'BTL_RKT', 'BTL_TEL', 'BTL_WIFI', 'BTL_YMK', 'BTL_YSMSPR', 'port24', 'YESILTURT_RKT', 'YESILYURT_MYO', 'HEKIMHAN_MYO', 'KALE_MYO', 'DOGANSEHIR_MYO', 'ARAPGIR_MYO', 'DARENDE_MYO', 'AKCADAG_MYO']
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: 'name'
2025-08-02 10:31:22 - INFO - 完成区域 LAN 解析，包含 19 个接口
2025-08-02 10:31:22 - INFO - 解析区域: DMZ
2025-08-02 10:31:22 - INFO - 设置区域 DMZ 的内部通信策略: allow
2025-08-02 10:31:22 - INFO - 设置区域 DMZ 的接口: ['port23', 'REAL_IP']
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: 'name'
2025-08-02 10:31:22 - INFO - 完成区域 DMZ 解析，包含 2 个接口
2025-08-02 10:31:22 - INFO - 解析区域: MGMT
2025-08-02 10:31:22 - INFO - 设置区域 MGMT 的接口: ['BTL_MGMT']
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: 'name': 'MGMT', 'interfaces': ['BTL_MGMT']
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: 'name'
2025-08-02 10:31:22 - INFO - 完成区域 MGMT 解析，包含 1 个接口
2025-08-02 10:31:22 - INFO - 开始解析地址对象配置部分
2025-08-02 10:31:22 - INFO - 正在解析地址对象: EMS_ALL_UNKNOWN_CLIENTS
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - INFO - 完成地址对象 EMS_ALL_UNKNOWN_CLIENTS 解析
2025-08-02 10:31:22 - INFO - 正在解析地址对象: EMS_ALL_UNMANAGEABLE_CLIENTS
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - INFO - 完成地址对象 EMS_ALL_UNMANAGEABLE_CLIENTS 解析
2025-08-02 10:31:22 - INFO - 正在解析地址对象: none
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - INFO - 完成地址对象 none 解析
2025-08-02 10:31:22 - INFO - 正在解析地址对象: login.microsoftonline.com
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - INFO - 完成地址对象 login.microsoftonline.com 解析
2025-08-02 10:31:22 - INFO - 正在解析地址对象: login.microsoft.com
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - INFO - 完成地址对象 login.microsoft.com 解析
2025-08-02 10:31:22 - INFO - 正在解析地址对象: login.windows.net
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - INFO - 完成地址对象 login.windows.net 解析
2025-08-02 10:31:22 - INFO - 正在解析地址对象: gmail.com
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - INFO - 完成地址对象 gmail.com 解析
2025-08-02 10:31:22 - INFO - 正在解析地址对象: wildcard.google.com
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - WARNING - 警告: 地址对象 'wildcard.google.com' 的FQDN '*.google.com' 格式可能不正确
2025-08-02 10:31:22 - INFO - 完成地址对象 wildcard.google.com 解析
2025-08-02 10:31:22 - INFO - 正在解析地址对象: wildcard.dropbox.com
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - WARNING - 警告: 地址对象 'wildcard.dropbox.com' 的FQDN '*.dropbox.com' 格式可能不正确
2025-08-02 10:31:22 - INFO - 完成地址对象 wildcard.dropbox.com 解析
2025-08-02 10:31:22 - INFO - 正在解析地址对象: all
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - INFO - 完成地址对象 all 解析
2025-08-02 10:31:22 - INFO - 正在解析地址对象: FIREWALL_AUTH_PORTAL_ADDRESS
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - INFO - 完成地址对象 FIREWALL_AUTH_PORTAL_ADDRESS 解析
2025-08-02 10:31:22 - INFO - 正在解析地址对象: FABRIC_DEVICE
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - INFO - 完成地址对象 FABRIC_DEVICE 解析
2025-08-02 10:31:22 - INFO - 正在解析地址对象: SSLVPN_TUNNEL_ADDR1
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - INFO - 完成地址对象 SSLVPN_TUNNEL_ADDR1 解析
2025-08-02 10:31:22 - INFO - 正在解析地址对象: BATTALGAZI
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - INFO - 完成地址对象 BATTALGAZI 解析
2025-08-02 10:31:22 - INFO - 正在解析地址对象: DMZaddress
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - INFO - 完成地址对象 DMZaddress 解析
2025-08-02 10:31:22 - INFO - 正在解析地址对象: DNS_SERVER
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - INFO - 完成地址对象 DNS_SERVER 解析
2025-08-02 10:31:22 - INFO - 正在解析地址对象: KALE_IP_SANTRAL
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - INFO - 完成地址对象 KALE_IP_SANTRAL 解析
2025-08-02 10:31:22 - INFO - 正在解析地址对象: ARAPGIR_IP_SANTRAL
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - INFO - 完成地址对象 ARAPGIR_IP_SANTRAL 解析
2025-08-02 10:31:22 - INFO - 正在解析地址对象: DGNSHR_IP_SANTRAL
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - INFO - 完成地址对象 DGNSHR_IP_SANTRAL 解析
2025-08-02 10:31:22 - INFO - 正在解析地址对象: DARENDE_IP_SANTRAL
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - INFO - 完成地址对象 DARENDE_IP_SANTRAL 解析
2025-08-02 10:31:22 - INFO - 正在解析地址对象: KALE_IP_SANTRAL_2
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - INFO - 完成地址对象 KALE_IP_SANTRAL_2 解析
2025-08-02 10:31:22 - INFO - 正在解析地址对象: ARAPGIR_IP_SANTRAL_2
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - INFO - 完成地址对象 ARAPGIR_IP_SANTRAL_2 解析
2025-08-02 10:31:22 - INFO - 正在解析地址对象: YYMYO_IP_SANTRAL
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - INFO - 完成地址对象 YYMYO_IP_SANTRAL 解析
2025-08-02 10:31:22 - INFO - 正在解析地址对象: YYMYO_IP_SANTRAL_2
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - INFO - 完成地址对象 YYMYO_IP_SANTRAL_2 解析
2025-08-02 10:31:22 - INFO - 正在解析地址对象: DGNSHR_IP_SANTRAL_2
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - INFO - 完成地址对象 DGNSHR_IP_SANTRAL_2 解析
2025-08-02 10:31:22 - INFO - 正在解析地址对象: DGNSHR_IP_SANTRAL_3
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - INFO - 完成地址对象 DGNSHR_IP_SANTRAL_3 解析
2025-08-02 10:31:22 - INFO - 正在解析地址对象: DARENDE_IP_SANTRAL_2
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - INFO - 完成地址对象 DARENDE_IP_SANTRAL_2 解析
2025-08-02 10:31:22 - INFO - 正在解析地址对象: HEKIMHAN_IP_SANTRAL
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - INFO - 完成地址对象 HEKIMHAN_IP_SANTRAL 解析
2025-08-02 10:31:22 - INFO - 正在解析地址对象: HEKIMHAN_IP_SANTRAL_2
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - INFO - 完成地址对象 HEKIMHAN_IP_SANTRAL_2 解析
2025-08-02 10:31:22 - INFO - 正在解析地址对象: AKCADAG_IP_SANTRAL
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - INFO - 完成地址对象 AKCADAG_IP_SANTRAL 解析
2025-08-02 10:31:22 - INFO - 正在解析地址对象: AKCADAG_IP_SANTRAL_2
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - INFO - 完成地址对象 AKCADAG_IP_SANTRAL_2 解析
2025-08-02 10:31:22 - INFO - 正在解析地址对象: DARENDE_IP_SANTRAL_3
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - INFO - 完成地址对象 DARENDE_IP_SANTRAL_3 解析
2025-08-02 10:31:22 - INFO - 正在解析地址对象: BTTLGZ_IP_SANTRAL
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - INFO - 完成地址对象 BTTLGZ_IP_SANTRAL 解析
2025-08-02 10:31:22 - INFO - 正在解析地址对象: BTTLGZ_IP_SANTRAL_2
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - INFO - 完成地址对象 BTTLGZ_IP_SANTRAL_2 解析
2025-08-02 10:31:22 - INFO - 正在解析地址对象: BTTLGZ_IP_SANTRAL_3
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - INFO - 完成地址对象 BTTLGZ_IP_SANTRAL_3 解析
2025-08-02 10:31:22 - INFO - 正在解析地址对象: BTTLGZ_IP_SANTRAL_4
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - INFO - 完成地址对象 BTTLGZ_IP_SANTRAL_4 解析
2025-08-02 10:31:22 - INFO - 正在解析地址对象: BTTLGZ_IP_SANTRAL_5
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - INFO - 完成地址对象 BTTLGZ_IP_SANTRAL_5 解析
2025-08-02 10:31:22 - INFO - 正在解析地址对象: BTTLGZ_IP_SANTRAL_6
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - INFO - 完成地址对象 BTTLGZ_IP_SANTRAL_6 解析
2025-08-02 10:31:22 - INFO - 正在解析地址对象: BTTLGZ_IP_SANTRAL_7
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - INFO - 完成地址对象 BTTLGZ_IP_SANTRAL_7 解析
2025-08-02 10:31:22 - INFO - 正在解析地址对象: BTTLGZ_IP_SANTRAL_8
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - INFO - 完成地址对象 BTTLGZ_IP_SANTRAL_8 解析
2025-08-02 10:31:22 - INFO - 正在解析地址对象: BTTLGZ_IP_SANTRAL_9
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - INFO - 完成地址对象 BTTLGZ_IP_SANTRAL_9 解析
2025-08-02 10:31:22 - INFO - 正在解析地址对象: BTTLGZ_IP_SANTRAL_10
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - INFO - 完成地址对象 BTTLGZ_IP_SANTRAL_10 解析
2025-08-02 10:31:22 - INFO - 正在解析地址对象: BTTLGZ_IP_SANTRAL_12
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - INFO - 完成地址对象 BTTLGZ_IP_SANTRAL_12 解析
2025-08-02 10:31:22 - INFO - 正在解析地址对象: YESİLYURT_REKT
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - INFO - 完成地址对象 YESİLYURT_REKT 解析
2025-08-02 10:31:22 - INFO - 正在解析地址对象: YESILYURT_SANTRAL_IP
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - INFO - 完成地址对象 YESILYURT_SANTRAL_IP 解析
2025-08-02 10:31:22 - INFO - 正在解析地址对象: YEMEKHANE_SERVER
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - INFO - 完成地址对象 YEMEKHANE_SERVER 解析
2025-08-02 10:31:22 - INFO - 正在解析地址对象: ARAPGIR_TURNIKE
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - INFO - 完成地址对象 ARAPGIR_TURNIKE 解析
2025-08-02 10:31:22 - INFO - 正在解析地址对象: KALE_TURNIKE
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - INFO - 完成地址对象 KALE_TURNIKE 解析
2025-08-02 10:31:22 - INFO - 正在解析地址对象: YEMEKHANE_1
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - INFO - 完成地址对象 YEMEKHANE_1 解析
2025-08-02 10:31:22 - INFO - 正在解析地址对象: YEMEKHANE_3
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - INFO - 完成地址对象 YEMEKHANE_3 解析
2025-08-02 10:31:22 - INFO - 正在解析地址对象: YEMEKHANE_2
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - INFO - 完成地址对象 YEMEKHANE_2 解析
2025-08-02 10:31:22 - INFO - 正在解析地址对象: Merkez_Yemekhane_Harcama_I
2025-08-02 10:31:22 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:22 - INFO - 完成地址对象 Merkez_Yemekhane_Harcama_I 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: DGNSHR_TURNIKE
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 DGNSHR_TURNIKE 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: DARENDE_TURNIKE
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 DARENDE_TURNIKE 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: ERU_IP
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 ERU_IP 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: CompleteAnatomy1
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 CompleteAnatomy1 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: CompleteAnatomy2
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 CompleteAnatomy2 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: CompleteAnatomy3
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 CompleteAnatomy3 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: KPS_IP
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 KPS_IP 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: OSYM_IP
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 OSYM_IP 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: PROTEL_IP
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 PROTEL_IP 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: METIN_VPN_IP
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 METIN_VPN_IP 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: **************
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 ************** 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: **************
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 ************** 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: ***************
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 *************** 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: ************
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 ************ 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: 10.0.0.0/8
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 10.0.0.0/8 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: ***********/16
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 ***********/16 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: **********/16
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 **********/16 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: MTU_WAN_IPS
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 MTU_WAN_IPS 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: **************/32
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 **************/32 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: 5M_Bilisim
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 5M_Bilisim 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: **************/32
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 **************/32 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: **************/32
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 **************/32 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: ***************/32
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 ***************/32 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: ***************/32
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 ***************/32 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: ************/32
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 ************/32 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: **************/32
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 **************/32 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: *************/22
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 *************/22 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: *************/32
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 *************/32 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: **************/32
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 **************/32 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: FCTEMS_ALL_FORTICLOUD_SERVERS
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 FCTEMS_ALL_FORTICLOUD_SERVERS 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: AP_CONTROLLER
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 AP_CONTROLLER 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: KALE_AP_1
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 KALE_AP_1 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: AKCADAG_AP
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 AKCADAG_AP 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: DARENDE_KANTIN_AP_1
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 DARENDE_KANTIN_AP_1 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: DARENDE_KIZ_YURDU_AP
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 DARENDE_KIZ_YURDU_AP 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: DARENDE_KIZ_YURDU_AP_2
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 DARENDE_KIZ_YURDU_AP_2 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: DARENDE_ERKEK_YURDU_AP_1
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 DARENDE_ERKEK_YURDU_AP_1 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: METIN_GECICI
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 METIN_GECICI 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: deren_unal
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 deren_unal 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: UMRAN
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 UMRAN 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: SRV_SEMINER_IP
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 SRV_SEMINER_IP 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: SRV_UZEM_LMS
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 SRV_UZEM_LMS 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: SRV_DIS_SEMINER_IP
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 SRV_DIS_SEMINER_IP 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: SRV_DIS_UZEM_LMS_IP
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 SRV_DIS_UZEM_LMS_IP 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: SRV_WEB_ALTBIRIM_IP
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 SRV_WEB_ALTBIRIM_IP 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: SRV_DIS_WEB_ALTBIRIM_IP
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 SRV_DIS_WEB_ALTBIRIM_IP 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: SRV_YETKIM_IP
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 SRV_YETKIM_IP 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: SRV_DIS_YETKIM_IP
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 SRV_DIS_YETKIM_IP 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: SRV_OBS_IP
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 SRV_OBS_IP 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: SRV_DIS_OBS_IP
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 SRV_DIS_OBS_IP 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: SRV_OZDEGERLENDIRME_IP
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 SRV_OZDEGERLENDIRME_IP 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: SRV_DIS_OZDEGERLENDIRME_IP
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 SRV_DIS_OZDEGERLENDIRME_IP 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: SRV_WEB_ESKI_IP
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 SRV_WEB_ESKI_IP 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: SRV_DIS_WEB_ESKI_IP
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 SRV_DIS_WEB_ESKI_IP 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: SRV_TIP_OGR_BIRLIGI_IP
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 SRV_TIP_OGR_BIRLIGI_IP 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: SRV_DIS_TIP_OGR_BIRLIGI_IP
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 SRV_DIS_TIP_OGR_BIRLIGI_IP 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: SRV_SUREKLI_EGITIM_MRK_IP
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 SRV_SUREKLI_EGITIM_MRK_IP 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: SRV_DIS_SUREKLI_EGITIM_MRK_IP
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 SRV_DIS_SUREKLI_EGITIM_MRK_IP 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: SRV_DSPACE_IP
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 SRV_DSPACE_IP 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: SRV_DIS_DSPACE_IP
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 SRV_DIS_DSPACE_IP 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: SRV_AKADEMIK_TESVIK_IP
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 SRV_AKADEMIK_TESVIK_IP 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: SRV_DIS_AKADEMIK_TESVIK_IP
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 SRV_DIS_AKADEMIK_TESVIK_IP 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: SRV_WEB_YENI_IP
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 SRV_WEB_YENI_IP 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: SRV_DIS_WEB_YENI_IP
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 SRV_DIS_WEB_YENI_IP 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: SRV_PERS_OTOM_IP
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 SRV_PERS_OTOM_IP 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: SRV_DIS_PERS_OTOM_IP
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 SRV_DIS_PERS_OTOM_IP 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: BIDB_SERVER_DMZ
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 BIDB_SERVER_DMZ 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: FIREWALL_DMZ_IP
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 FIREWALL_DMZ_IP 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: DMZ_SUNUCULAR
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 DMZ_SUNUCULAR 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: OBS_PROLIZ_IP
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 OBS_PROLIZ_IP 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: ALL
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 ALL 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: AD_LOCAL_IP
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 AD_LOCAL_IP 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: REMOTE_SERVER
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 REMOTE_SERVER 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: ip-adresim.net
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 ip-adresim.net 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: Necmettin
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 Necmettin 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: KG_NIZAMIYE
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 KG_NIZAMIYE 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: KG_REKTORLUK
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 KG_REKTORLUK 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: KG_MRK_YASAM_MERKEZI
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 KG_MRK_YASAM_MERKEZI 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: KG_MRK_BATTALGAZI_MYO
2025-08-02 10:31:23 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:23 - INFO - 完成地址对象 KG_MRK_BATTALGAZI_MYO 解析
2025-08-02 10:31:23 - INFO - 正在解析地址对象: KG_AKCADAG_MYO
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 KG_AKCADAG_MYO 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: KG_ARAPGIR_MYO
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 KG_ARAPGIR_MYO 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: KG_KALE_MYO
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 KG_KALE_MYO 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: KG_HEKIMHAN_MYO
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 KG_HEKIMHAN_MYO 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: KG_DARENDE_MYO
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 KG_DARENDE_MYO 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: KG_YESILYURT_MYO
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 KG_YESILYURT_MYO 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: KG_YESILYURT_REKTORLUK
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 KG_YESILYURT_REKTORLUK 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: SRV_PERS_TKP
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 SRV_PERS_TKP 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: YESILYURT_SANTRAL_3
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 YESILYURT_SANTRAL_3 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: YESILYURT_SANTRAL_4
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 YESILYURT_SANTRAL_4 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: DSPACE_FIRMA_1
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 DSPACE_FIRMA_1 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: DSPACE_FIRMA_2
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 DSPACE_FIRMA_2 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: SRV_KIMLIK_IP
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 SRV_KIMLIK_IP 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: SRV_AD_BCK
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 SRV_AD_BCK 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: SRV_EK_DERS_IP
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 SRV_EK_DERS_IP 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: SRV_SEM
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 SRV_SEM 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: turtep.yesevi.edu.tr
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 turtep.yesevi.edu.tr 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: BackupServer
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 BackupServer 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: DataDomain
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 DataDomain 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: DataDomain2
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 DataDomain2 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: METIN_VPN_IP_2
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 METIN_VPN_IP_2 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: SRV_DIS_EK_DERS_IP
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 SRV_DIS_EK_DERS_IP 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: BAPSİS_İP
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 BAPSİS_İP 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: BAPSİS_DİS_İP
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 BAPSİS_DİS_İP 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: proxy
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 proxy 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: proxy_sunucusu
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 proxy_sunucusu 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: Proxy_Sunucusu
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 Proxy_Sunucusu 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: HEKIMHAN_AP
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 HEKIMHAN_AP 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: HEKİMHAN_AP
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 HEKİMHAN_AP 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: FILE_SERVER
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 FILE_SERVER 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: OBYS_Server
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 OBYS_Server 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: PBS_server
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 PBS_server 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: Rustdesk
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 Rustdesk 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: Rustdesk2
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 Rustdesk2 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: Rustdesk3
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 Rustdesk3 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: BlockMAC_01
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 BlockMAC_01 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: ArapgirAP
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 ArapgirAP 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: Battalgazi
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 Battalgazi 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: kartokuyucu
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 kartokuyucu 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: NMS
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 NMS 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: ************
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 ************ 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: VRPA_WAN
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 VRPA_WAN 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: VRPA_DATA
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 VRPA_DATA 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: DSpace_New_IP
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 DSpace_New_IP 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: DSpace_New_DIS_IP
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 DSpace_New_DIS_IP 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: virüs_ıp_2
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 virüs_ıp_2 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: kimlikdogrulama.nvi.gov.tr
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 kimlikdogrulama.nvi.gov.tr 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: kpsv2.nvi.gov.tr
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 kpsv2.nvi.gov.tr 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: rustdesk.com
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 rustdesk.com 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: necmettin_gul
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 necmettin_gul 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: ARIZATAKIP
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 ARIZATAKIP 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: SRV_YETENEK_SİNAVİ_OTOMASYON_IP
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 SRV_YETENEK_SİNAVİ_OTOMASYON_IP 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: SRV_YETENEK_SİNAVİ_WEB_IP
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 SRV_YETENEK_SİNAVİ_WEB_IP 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: SRV_YOS_BASVURU_IP
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 SRV_YOS_BASVURU_IP 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: osym.gov.tr
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 osym.gov.tr 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: vps.osym.gov.tr
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 vps.osym.gov.tr 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: Sunucu_PC
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 Sunucu_PC 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: qtn.mac_00:00:00:00:00:00
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 qtn.mac_00:00:00:00:00:00 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: MTN_GECICI_IP
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 MTN_GECICI_IP 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: Pts_Ozgur_Zaman_Firma
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 Pts_Ozgur_Zaman_Firma 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: REAL_IPLER
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 REAL_IPLER 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: SSL_VPN
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 SSL_VPN 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: BOS
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 BOS 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: IKCU_RPA_1
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 IKCU_RPA_1 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: IKCU_RPA_2
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 IKCU_RPA_2 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: IKCU_RPA_3
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 IKCU_RPA_3 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: IKCU_RPA_4
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 IKCU_RPA_4 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: IKCU_RPA_5
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 IKCU_RPA_5 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: IKCU_RPA_6
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 IKCU_RPA_6 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: MTU_RPA_1
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 MTU_RPA_1 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: MTU_RPA_2
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 MTU_RPA_2 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: MTU_RPA_3
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 MTU_RPA_3 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: vRPA_CLS
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 vRPA_CLS 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: vRPA_1
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 vRPA_1 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: vRPA_3
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 vRPA_3 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: vRPA_2
2025-08-02 10:31:24 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:24 - INFO - 完成地址对象 vRPA_2 解析
2025-08-02 10:31:24 - INFO - 正在解析地址对象: port15
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 port15 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: port16
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 port16 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: EBYS_US
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 EBYS_US 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: miyase_coban
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 miyase_coban 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: kamusm.gov.tr
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 kamusm.gov.tr 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: tubitak.gov.tr
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 tubitak.gov.tr 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: port24
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 port24 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: BTL_YSMSPR
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 BTL_YSMSPR 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: BTL_YMK
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 BTL_YMK 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: BTL_WIFI
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 BTL_WIFI 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: BTL_TEL
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 BTL_TEL 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: BTL_RKT
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 BTL_RKT 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: BTL_MYO
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 BTL_MYO 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: BTL_MGMT
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 BTL_MGMT 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: BTL_GUV
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 BTL_GUV 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: BTL_ATC
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 BTL_ATC 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: BTL_BIDB
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 BTL_BIDB 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: BTL_CAM
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 BTL_CAM 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: REAL_IP
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 REAL_IP 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: port23
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 port23 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: PROXY
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 PROXY 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: EBYS_DIS_IP
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 EBYS_DIS_IP 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: umran_turkmen
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 umran_turkmen 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: TargitasNAC
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 TargitasNAC 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: ibrahim_ip
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 ibrahim_ip 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: halisbozkurt
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 halisbozkurt 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: KutuphaneServer
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 KutuphaneServer 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: REKTOR_DU_MAC
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 REKTOR_DU_MAC 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: hasan_Mac
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 hasan_Mac 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: REKTOR_TEL_MAC
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 REKTOR_TEL_MAC 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: hasan_masa
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 hasan_masa 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: orhan_gunduz_masa
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 orhan_gunduz_masa 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: ahmet_ozkan_masa
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 ahmet_ozkan_masa 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: rektor
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 rektor 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: deren_masa
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 deren_masa 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: ***********
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 *********** 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: depo.kamusm.gov.tr
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 depo.kamusm.gov.tr 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: ek
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 ek 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: ma
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 ma 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: md
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 md 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: Merkez_Yemekhane_Pos
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 Merkez_Yemekhane_Pos 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: Merkez_Yemekhane_Harcama_II
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 Merkez_Yemekhane_Harcama_II 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: Arapgir_Myo_Pos
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 Arapgir_Myo_Pos 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: Arapgir_Myo_Harcama
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 Arapgir_Myo_Harcama 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: Akcadag_Myo_Pos
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 Akcadag_Myo_Pos 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: Akcadag_Myo_Harcama
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 Akcadag_Myo_Harcama 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: Darende_Myo_Pos
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 Darende_Myo_Pos 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: Darende_Myo_Harcama
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 Darende_Myo_Harcama 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: Dogansehir_Myo_Pos
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 Dogansehir_Myo_Pos 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: Dogansehir_Myo_Harcama
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 Dogansehir_Myo_Harcama 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: Hekimhan_Myo_Pos
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 Hekimhan_Myo_Pos 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: Hekimhan_Myo_Harcama
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 Hekimhan_Myo_Harcama 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: Kale_Myo_Pos
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 Kale_Myo_Pos 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: Kale_Myo_Harcama
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 Kale_Myo_Harcama 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: Yesilyurt_Myo_Harcama
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 Yesilyurt_Myo_Harcama 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: Yesilyurt_Myo_Pos
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 Yesilyurt_Myo_Pos 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: Merkez_Yemekhane_Harcama_III
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 Merkez_Yemekhane_Harcama_III 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: Merkez_Yemekhane_Harcama_IV
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 Merkez_Yemekhane_Harcama_IV 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: Kıosk_I
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 Kıosk_I 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: Kıosk_II
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 Kıosk_II 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: 3bi
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 3bi 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: Veysel_Sahin_IP
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 Veysel_Sahin_IP 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: ExtremeXIQController
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 ExtremeXIQController 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: sayistay_3
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 sayistay_3 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: sayistay_4
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 sayistay_4 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: log_dizustü
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 log_dizustü 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: LOG_D
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 LOG_D 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: santral
2025-08-02 10:31:25 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:25 - INFO - 完成地址对象 santral 解析
2025-08-02 10:31:25 - INFO - 正在解析地址对象: qtn.mac_d8:50:e6:4a:73:45
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 qtn.mac_d8:50:e6:4a:73:45 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: battalgazi_nizamiye
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 battalgazi_nizamiye 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: seyhan_dogan_kart_okuma
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 seyhan_dogan_kart_okuma 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: test_ncgul
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 test_ncgul 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: NAC_LOGIN
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 NAC_LOGIN 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: qtn.mac_a4:bb:6d:58:83:8a
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 qtn.mac_a4:bb:6d:58:83:8a 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: qtn.mac_a4:bb:6d:64:68:1b
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 qtn.mac_a4:bb:6d:64:68:1b 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: qtn.mac_50:9a:4c:2b:9f:e6
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 qtn.mac_50:9a:4c:2b:9f:e6 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: qtn.mac_b2:90:12:e5:49:af
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 qtn.mac_b2:90:12:e5:49:af 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: qtn.mac_30:85:a9:b1:f6:01
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 qtn.mac_30:85:a9:b1:f6:01 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: qtn.mac_68:da:73:a8:fe:61
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 qtn.mac_68:da:73:a8:fe:61 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: qtn.mac_6c:24:08:09:a1:d2
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 qtn.mac_6c:24:08:09:a1:d2 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: qtn.mac_00:e0:4c:68:00:86
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 qtn.mac_00:e0:4c:68:00:86 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: qtn.mac_30:85:a9:8e:de:d8
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 qtn.mac_30:85:a9:8e:de:d8 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: qtn.mac_a4:bb:6d:64:68:73
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 qtn.mac_a4:bb:6d:64:68:73 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: k_kemal
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 k_kemal 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: DARENDE_TUM_NET
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 DARENDE_TUM_NET 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: DHCP_SERVER
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 DHCP_SERVER 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: HEKIMHAN_TUM_NET
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 HEKIMHAN_TUM_NET 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: YYMYO_Santral
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 YYMYO_Santral 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: ARUBA_SW
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 ARUBA_SW 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: necmettin
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 necmettin 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: hakan_toptas_cep
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 hakan_toptas_cep 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: proxysunucu_2
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 proxysunucu_2 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: Yeşilyurt_Rektörlük_Yemekhane
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 Yeşilyurt_Rektörlük_Yemekhane 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: Merkez
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 Merkez 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: Sunucu_1
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 Sunucu_1 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: SWTCH_LOG_ULASIM
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 SWTCH_LOG_ULASIM 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: NAC_TARGTAS_PC
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 NAC_TARGTAS_PC 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: akif_ates
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 akif_ates 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: NETWORK_LAN_2
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 NETWORK_LAN_2 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: SWTCH_LOG_ULASIM1
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 SWTCH_LOG_ULASIM1 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: Santral_Merkez1
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 Santral_Merkez1 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: Santral_Merkez2
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 Santral_Merkez2 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: Santral_Merkez3
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 Santral_Merkez3 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: Santral_Ogrenci_Yasam
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 Santral_Ogrenci_Yasam 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: Santral_Y_Yurt_MYO
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 Santral_Y_Yurt_MYO 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: Santral_B_Gazi_MYO
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 Santral_B_Gazi_MYO 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: Santral_Darende
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 Santral_Darende 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: Santral_Akcadag
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 Santral_Akcadag 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: Santral_Kale
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 Santral_Kale 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: Santral_Dogansehir
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 Santral_Dogansehir 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: Santral_YYurt_Rektorluk
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 Santral_YYurt_Rektorluk 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: Kantinposchz
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 Kantinposchz 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: Kantnbarkod
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 Kantnbarkod 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: qtn.mac_1e:21:b5:80:dc:4e
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 qtn.mac_1e:21:b5:80:dc:4e 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: halife_tel
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 halife_tel 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: FILE_SERVER_II
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 FILE_SERVER_II 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: pts_sunucusu
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 pts_sunucusu 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: Rektör_Makam
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 Rektör_Makam 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: packet_deneme
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 packet_deneme 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: HLS_BZKRT
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 HLS_BZKRT 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: NCMTN_GL
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 NCMTN_GL 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: ETIK_IP
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 ETIK_IP 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: DNS_DIS_IP
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 DNS_DIS_IP 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: Yesilyurt_yemekhane_yeni
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 Yesilyurt_yemekhane_yeni 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: Yesilyurt_Utarit_X
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 Yesilyurt_Utarit_X 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: Yeşilyurt_Rektörlük_Kios
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 Yeşilyurt_Rektörlük_Kios 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: Mücahit_PC
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 Mücahit_PC 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: HakanToptasMAC
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 HakanToptasMAC 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: NecmettinGul_Mac
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 NecmettinGul_Mac 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: ISATURGUT_3
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 ISATURGUT_3 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: ISATURGUT_3_MAC
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 ISATURGUT_3_MAC 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: ISATURGUT_MAC
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 ISATURGUT_MAC 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: ISATURGUT_MAC_II
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 ISATURGUT_MAC_II 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: ISATURGUT_
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 ISATURGUT_ 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: ISATURGUT_2
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 ISATURGUT_2 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: KG_YESILYURT_KONTEYNER
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 KG_YESILYURT_KONTEYNER 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: betul_ozcinar
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 betul_ozcinar 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: ssbf_lab_1
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 ssbf_lab_1 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: ssbf_lab_2
2025-08-02 10:31:26 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:26 - INFO - 完成地址对象 ssbf_lab_2 解析
2025-08-02 10:31:26 - INFO - 正在解析地址对象: ssbf_lab_3
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 ssbf_lab_3 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: ssbf_lab_4
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 ssbf_lab_4 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: ssbf_lab_5
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 ssbf_lab_5 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: ssbf_lab_6
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 ssbf_lab_6 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: ssbf_lab_7
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 ssbf_lab_7 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: ssbf_lab_8
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 ssbf_lab_8 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: ssbf_lab_9
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 ssbf_lab_9 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: ssbf_lab_10
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 ssbf_lab_10 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: ssbf_lab_11
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 ssbf_lab_11 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: ssbf_lab_12
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 ssbf_lab_12 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: ssbf_lab_13
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 ssbf_lab_13 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: ssbf_lab_14
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 ssbf_lab_14 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: ssbf_lab_15
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 ssbf_lab_15 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: ssbf_lab_16
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 ssbf_lab_16 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: ssbf_lab_17
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 ssbf_lab_17 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: ssbf_lab_18
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 ssbf_lab_18 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: ssbf_lab_19
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 ssbf_lab_19 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: ssbf_lab_20
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 ssbf_lab_20 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: SBBF_Oracle_Opera_sunucu
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 SBBF_Oracle_Opera_sunucu 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: SBSF_LAB_LOCAL_IP
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 SBSF_LAB_LOCAL_IP 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: KG_YESILYURT_SANAT_TASARIM
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 KG_YESILYURT_SANAT_TASARIM 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: sbbf_lab_21
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 sbbf_lab_21 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: qtn.mac_36:42:d7:80:a0:76
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 qtn.mac_36:42:d7:80:a0:76 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: battalgazi_k_pos
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 battalgazi_k_pos 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: battalgazi_k_pos_2
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 battalgazi_k_pos_2 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: NECMETTIN_GUL_MOBIL_ETH
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 NECMETTIN_GUL_MOBIL_ETH 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: REKTÖR_DİZÜSTÜ
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 REKTÖR_DİZÜSTÜ 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: ISA_DURGUT_IV
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 ISA_DURGUT_IV 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: ISA_DURGUT_V
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 ISA_DURGUT_V 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: ISATURGUT_4
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 ISATURGUT_4 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: ISATURGUT_5
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 ISATURGUT_5 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: arapgit_santral
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 arapgit_santral 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: Arapgir_Santral
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 Arapgir_Santral 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: YesilyurtNizamiyePTS
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 YesilyurtNizamiyePTS 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: Bat_ana_nzm_grs
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 Bat_ana_nzm_grs 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: Bat_ana_nzm_cks
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 Bat_ana_nzm_cks 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: Bat_rkt_grs
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 Bat_rkt_grs 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: Bat_rkt_cks
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 Bat_rkt_cks 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: Btl_ana_sunucu
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 Btl_ana_sunucu 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: SRV_PROJE_PAZARİ
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 SRV_PROJE_PAZARİ 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: HAKAN_TOPTAS_MOBIL_ETH
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 HAKAN_TOPTAS_MOBIL_ETH 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: FORTILOGGER
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 FORTILOGGER 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: Yeşilyurt_Rek_Kenan_Bentli_Nejmiye_Hnm
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 Yeşilyurt_Rek_Kenan_Bentli_Nejmiye_Hnm 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: Recep_Bentli_Ysl_Yrt_mkm
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 Recep_Bentli_Ysl_Yrt_mkm 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: Yeşilyurt_Rek_Toplantı_Salonu
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 Yeşilyurt_Rek_Toplantı_Salonu 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: Yesilyurt_Rkt_Yemekhane_Pos
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 Yesilyurt_Rkt_Yemekhane_Pos 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: sahapc
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 sahapc 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: metek_grup_ıp
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 metek_grup_ıp 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: rektorluk_toplantı_salonu
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 rektorluk_toplantı_salonu 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: FortiLogger_IP
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 FortiLogger_IP 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: Yeşilyurt_Rek_Orhan_Gündüz
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 Yeşilyurt_Rek_Orhan_Gündüz 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: Yeşilyurt_Rek_İlhan_Erdem
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 Yeşilyurt_Rek_İlhan_Erdem 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: yesilyurt_kantin_pos
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 yesilyurt_kantin_pos 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: mehmet_kakiz
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 mehmet_kakiz 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: sefanur_korkmaz
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 sefanur_korkmaz 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: ssbf_lab_ANAMAKINA
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 ssbf_lab_ANAMAKINA 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: vultr
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 vultr 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: vultr2
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 vultr2 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: api.hoptodesk.com
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 api.hoptodesk.com 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: vultrusercontent.com
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - WARNING - 警告: 地址对象 'vultrusercontent.com' 的FQDN '*.vultrusercontent.com' 格式可能不正确
2025-08-02 10:31:27 - INFO - 完成地址对象 vultrusercontent.com 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: signal.hoptodesk.com
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 signal.hoptodesk.com 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: signal2.hoptodesk.com
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 signal2.hoptodesk.com 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: turn.hoptodesk.com
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 turn.hoptodesk.com 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: hoptodesk.com
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 hoptodesk.com 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: vultrusercontent
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 vultrusercontent 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: turn.hoptodesk
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 turn.hoptodesk 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: ************
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 ************ 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: 0.pool.ntp.org
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 0.pool.ntp.org 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: time.nist.gov
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 time.nist.gov 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: tr.pool.ntp.org
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 tr.pool.ntp.org 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: ncgul_tel
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 ncgul_tel 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: umran_tel
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 umran_tel 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: Ruijie_Ap1
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 Ruijie_Ap1 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: Ruijie_Ap2
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 Ruijie_Ap2 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: HekimhanSW
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:27 - INFO - 完成地址对象 HekimhanSW 解析
2025-08-02 10:31:27 - INFO - 正在解析地址对象: kenan_bentli
2025-08-02 10:31:27 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 kenan_bentli 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: KenanHoca_iPhone
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 KenanHoca_iPhone 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: rektor_mac_eski
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 rektor_mac_eski 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: rektor_mac_2
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 rektor_mac_2 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: yesilyurt_kantin_pos_mac
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 yesilyurt_kantin_pos_mac 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: yesilyurt_Deneme
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 yesilyurt_Deneme 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: battalgazi_turnike_I
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 battalgazi_turnike_I 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: battalgazi_turnike_II
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 battalgazi_turnike_II 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: battalgazi_turnike_III
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 battalgazi_turnike_III 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: SRV_TurnikeSistemleri_Lokal_IP
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 SRV_TurnikeSistemleri_Lokal_IP 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: TPLİNK_WA850RE
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 TPLİNK_WA850RE 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: mehmetvuralpc
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 mehmetvuralpc 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: mehmetvuralpc2
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 mehmetvuralpc2 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: rektor_mac_yeni
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 rektor_mac_yeni 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: DARENDE_CAM
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 DARENDE_CAM 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: Akif_Ates_Darende
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 Akif_Ates_Darende 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: cacti
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 cacti 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: IsaTurgut_Kursu_MAC
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 IsaTurgut_Kursu_MAC 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: TOPLANTISALONU_Arslantepe
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 TOPLANTISALONU_Arslantepe 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: Ali_Yuce_ProjeLoraCihaz
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 Ali_Yuce_ProjeLoraCihaz 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: Yesilşyurt_Pts_Bilgisayari
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 Yesilşyurt_Pts_Bilgisayari 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: MehmetKakizLaptop
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 MehmetKakizLaptop 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: YESILTURT_RKT
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 YESILTURT_RKT 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: YESILYURT_MYO
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 YESILYURT_MYO 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: KALE_MYO
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 KALE_MYO 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: HEKIMHAN_MYO
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 HEKIMHAN_MYO 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: DOGANSEHIR_MYO
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 DOGANSEHIR_MYO 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: DARENDE_MYO
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 DARENDE_MYO 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: ARAPGIR_MYO
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 ARAPGIR_MYO 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: AKCADAG_MYO
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 AKCADAG_MYO 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: YSLYRT_DMZ_address
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 YSLYRT_DMZ_address 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: ************
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 ************ 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: YSLYRT_DMZ
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 YSLYRT_DMZ 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: ahmet_selim_ozkan_makam_tv
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 ahmet_selim_ozkan_makam_tv 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: vrpa
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 vrpa 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: vRPAIP
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 vRPAIP 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: test
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 test 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: QNAP-STORAGE
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 QNAP-STORAGE 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: RPA_CLS
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 RPA_CLS 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: RPA_1
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 RPA_1 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: RPA_2
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 RPA_2 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: RPA_3
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 RPA_3 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: RPA_PLUGIN
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 RPA_PLUGIN 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: ETISAN_BTL_PRS_POS
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 ETISAN_BTL_PRS_POS 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: ETISAN_BTL_OGR_POS
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 ETISAN_BTL_OGR_POS 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: ETISAN_YSL_MYO_POS
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 ETISAN_YSL_MYO_POS 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: ETISAN_YSL_RKT_PRS_POS
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 ETISAN_YSL_RKT_PRS_POS 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: ETISAN_YSL_RKT_OGR1_POS
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 ETISAN_YSL_RKT_OGR1_POS 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: ETISAN_YSL_RKT_OGR_POS
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 ETISAN_YSL_RKT_OGR_POS 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: ETISAN_BTL_POS
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 ETISAN_BTL_POS 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: HALIFE_TEST
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 HALIFE_TEST 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: NAGIOS_NECMETTIN
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 NAGIOS_NECMETTIN 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: isa_Sistem_odası
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 isa_Sistem_odası 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: ETISAN_POS_BAT
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 ETISAN_POS_BAT 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: ETISAN_POS_BAT_2
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 ETISAN_POS_BAT_2 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: ORHAN_GUNDUZ_BAT.
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 ORHAN_GUNDUZ_BAT. 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: app
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 app 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: TPLINKCNTRL
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 TPLINKCNTRL 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: AP1
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 AP1 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: YSLMYOAP2
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 YSLMYOAP2 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: YSLMYOAP3
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 YSLMYOAP3 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: ************
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 ************ 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: ************
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 ************ 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: ************
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 ************ 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: ************
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 ************ 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: ************
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 ************ 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: ************
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 ************ 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: ARYOM_IP
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 ARYOM_IP 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: aryomtest
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 aryomtest 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: Nejmiye_Hanım
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 Nejmiye_Hanım 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: KALE_ETISAN_POS
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 KALE_ETISAN_POS 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: AKCADAG_POS
2025-08-02 10:31:28 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:28 - INFO - 完成地址对象 AKCADAG_POS 解析
2025-08-02 10:31:28 - INFO - 正在解析地址对象: ARAPGİR_POS
2025-08-02 10:31:29 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:29 - INFO - 完成地址对象 ARAPGİR_POS 解析
2025-08-02 10:31:29 - INFO - 正在解析地址对象: HEKİMHAN_POS
2025-08-02 10:31:29 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:29 - INFO - 完成地址对象 HEKİMHAN_POS 解析
2025-08-02 10:31:29 - INFO - 正在解析地址对象: DARENDE_POS
2025-08-02 10:31:29 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:29 - INFO - 完成地址对象 DARENDE_POS 解析
2025-08-02 10:31:29 - INFO - 正在解析地址对象: YESİLYURT_REK_POS
2025-08-02 10:31:29 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:29 - INFO - 完成地址对象 YESİLYURT_REK_POS 解析
2025-08-02 10:31:29 - INFO - 正在解析地址对象: YESİLYURT_REK_POS2
2025-08-02 10:31:29 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:29 - INFO - 完成地址对象 YESİLYURT_REK_POS2 解析
2025-08-02 10:31:29 - INFO - 正在解析地址对象: YESİLYURT_POS_3
2025-08-02 10:31:29 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:29 - INFO - 完成地址对象 YESİLYURT_POS_3 解析
2025-08-02 10:31:29 - INFO - 正在解析地址对象: BATTALGAZİ_POS
2025-08-02 10:31:29 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:29 - INFO - 完成地址对象 BATTALGAZİ_POS 解析
2025-08-02 10:31:29 - INFO - 正在解析地址对象: KALE_POS_2
2025-08-02 10:31:29 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:29 - INFO - 完成地址对象 KALE_POS_2 解析
2025-08-02 10:31:29 - INFO - 正在解析地址对象: YESİLYURT_POS_1
2025-08-02 10:31:29 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:29 - INFO - 完成地址对象 YESİLYURT_POS_1 解析
2025-08-02 10:31:29 - INFO - 正在解析地址对象: Ziraat_Fak_Scada
2025-08-02 10:31:29 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:29 - INFO - 完成地址对象 Ziraat_Fak_Scada 解析
2025-08-02 10:31:29 - INFO - 正在解析地址对象: **********6
2025-08-02 10:31:29 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:29 - INFO - 完成地址对象 **********6 解析
2025-08-02 10:31:29 - INFO - 正在解析地址对象: ETISAn_BTL_POS_
2025-08-02 10:31:29 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:29 - INFO - 完成地址对象 ETISAn_BTL_POS_ 解析
2025-08-02 10:31:29 - INFO - 正在解析地址对象: ETISAN_BTL_POS_
2025-08-02 10:31:29 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:29 - INFO - 完成地址对象 ETISAN_BTL_POS_ 解析
2025-08-02 10:31:29 - INFO - 正在解析地址对象: Ahmet_Selim_Yeşilyurt_Oda
2025-08-02 10:31:29 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:29 - INFO - 完成地址对象 Ahmet_Selim_Yeşilyurt_Oda 解析
2025-08-02 10:31:29 - INFO - 正在解析地址对象: OMÜ_Erişim
2025-08-02 10:31:29 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:29 - INFO - 完成地址对象 OMÜ_Erişim 解析
2025-08-02 10:31:29 - INFO - 正在解析地址对象: NAS_OMV
2025-08-02 10:31:29 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:29 - INFO - 完成地址对象 NAS_OMV 解析
2025-08-02 10:31:29 - INFO - address 配置部分结束
2025-08-02 10:31:29 - INFO - 未知 配置部分结束
2025-08-02 10:31:29 - INFO - 开始解析地址对象配置部分
2025-08-02 10:31:29 - INFO - 正在解析地址对象: SSLVPN_TUNNEL_IPv6_ADDR1
2025-08-02 10:31:29 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:29 - INFO - 完成地址对象 SSLVPN_TUNNEL_IPv6_ADDR1 解析
2025-08-02 10:31:29 - INFO - 正在解析地址对象: all
2025-08-02 10:31:29 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:29 - INFO - 完成地址对象 all 解析
2025-08-02 10:31:29 - INFO - 正在解析地址对象: none
2025-08-02 10:31:29 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:29 - INFO - 完成地址对象 none 解析
2025-08-02 10:31:29 - INFO - address 配置部分结束
2025-08-02 10:31:29 - INFO - 未知 配置部分结束
2025-08-02 10:31:29 - INFO - 开始解析地址组配置部分
2025-08-02 10:31:29 - INFO - 解析地址组: G
2025-08-02 10:31:29 - INFO - 设置地址组成员数: 2
2025-08-02 10:31:29 - INFO - 完成地址组 G 解析，包含 2 个成员
2025-08-02 10:31:29 - INFO - 解析地址组: Microsoft
2025-08-02 10:31:29 - INFO - 设置地址组成员数: 3
2025-08-02 10:31:29 - INFO - 完成地址组 Microsoft 解析，包含 3 个成员
2025-08-02 10:31:29 - INFO - 解析地址组: IP_SANTRAL_GRP
2025-08-02 10:31:29 - INFO - 设置地址组成员数: 32
2025-08-02 10:31:29 - INFO - 完成地址组 IP_SANTRAL_GRP 解析，包含 32 个成员
2025-08-02 10:31:29 - INFO - 解析地址组: YEMEKHANE_CIHAZLAR
2025-08-02 10:31:29 - INFO - 设置地址组成员数: 11
2025-08-02 10:31:29 - INFO - 完成地址组 YEMEKHANE_CIHAZLAR 解析，包含 11 个成员
2025-08-02 10:31:29 - INFO - 解析地址组: Complete
2025-08-02 10:31:29 - INFO - 设置地址组成员数: 3
2025-08-02 10:31:29 - INFO - 完成地址组 Complete 解析，包含 3 个成员
2025-08-02 10:31:29 - INFO - 解析地址组: PBS_FIRMA_IP
2025-08-02 10:31:29 - INFO - 设置地址组成员数: 4
2025-08-02 10:31:29 - INFO - 完成地址组 PBS_FIRMA_IP 解析，包含 4 个成员
2025-08-02 10:31:29 - INFO - 解析地址组: LOCAL_NET_IP_SCOPE
2025-08-02 10:31:29 - INFO - 设置地址组成员数: 3
2025-08-02 10:31:29 - INFO - 完成地址组 LOCAL_NET_IP_SCOPE 解析，包含 3 个成员
2025-08-02 10:31:29 - INFO - 解析地址组: FIRAT_UNI
2025-08-02 10:31:29 - INFO - 设置地址组成员数: 4
2025-08-02 10:31:29 - INFO - 完成地址组 FIRAT_UNI 解析，包含 4 个成员
2025-08-02 10:31:29 - INFO - 解析地址组: EXTREME_AP
2025-08-02 10:31:29 - INFO - 设置地址组成员数: 10
2025-08-02 10:31:29 - INFO - 完成地址组 EXTREME_AP 解析，包含 10 个成员
2025-08-02 10:31:29 - INFO - 解析地址组: HAKAN_TOPTAS_ALL
2025-08-02 10:31:29 - INFO - 设置地址组成员数: 4
2025-08-02 10:31:29 - INFO - 完成地址组 HAKAN_TOPTAS_ALL 解析，包含 4 个成员
2025-08-02 10:31:29 - INFO - 解析地址组: BIDB
2025-08-02 10:31:29 - INFO - 设置地址组成员数: 7
2025-08-02 10:31:29 - INFO - 完成地址组 BIDB 解析，包含 7 个成员
2025-08-02 10:31:29 - INFO - 解析地址组: PlakaTanımaSistemi
2025-08-02 10:31:29 - INFO - 设置地址组成员数: 6
2025-08-02 10:31:29 - INFO - 完成地址组 PlakaTanımaSistemi 解析，包含 6 个成员
2025-08-02 10:31:29 - INFO - 解析地址组: KARTLI_GECIS
2025-08-02 10:31:29 - INFO - 设置地址组成员数: 15
2025-08-02 10:31:29 - INFO - 完成地址组 KARTLI_GECIS 解析，包含 15 个成员
2025-08-02 10:31:29 - INFO - 解析地址组: DSPACE_FIRMA_IPLERI
2025-08-02 10:31:29 - INFO - 设置地址组成员数: 2
2025-08-02 10:31:29 - INFO - 完成地址组 DSPACE_FIRMA_IPLERI 解析，包含 2 个成员
2025-08-02 10:31:29 - INFO - 解析地址组: BCK_SRV
2025-08-02 10:31:29 - INFO - 设置地址组成员数: 3
2025-08-02 10:31:29 - INFO - 完成地址组 BCK_SRV 解析，包含 3 个成员
2025-08-02 10:31:29 - INFO - 解析地址组: METIN_VPN_IP_GRP
2025-08-02 10:31:29 - INFO - 设置地址组成员数: 3
2025-08-02 10:31:29 - INFO - 完成地址组 METIN_VPN_IP_GRP 解析，包含 3 个成员
2025-08-02 10:31:29 - INFO - 解析地址组: QuarantinedDevices
2025-08-02 10:31:29 - INFO - 设置地址组成员数: 14
2025-08-02 10:31:29 - INFO - 完成地址组 QuarantinedDevices 解析，包含 14 个成员
2025-08-02 10:31:29 - INFO - 解析地址组: IKCU_RPA
2025-08-02 10:31:29 - INFO - 设置地址组成员数: 6
2025-08-02 10:31:29 - INFO - 完成地址组 IKCU_RPA 解析，包含 6 个成员
2025-08-02 10:31:29 - INFO - 解析地址组: MTU_RPA
2025-08-02 10:31:29 - INFO - 设置地址组成员数: 3
2025-08-02 10:31:29 - INFO - 完成地址组 MTU_RPA 解析，包含 3 个成员
2025-08-02 10:31:29 - INFO - 解析地址组: vRPA
2025-08-02 10:31:29 - INFO - 设置地址组成员数: 4
2025-08-02 10:31:29 - INFO - 完成地址组 vRPA 解析，包含 4 个成员
2025-08-02 10:31:29 - INFO - 解析地址组: Yemekhane_Cihazlar_Guncel
2025-08-02 10:31:29 - INFO - 设置地址组成员数: 29
2025-08-02 10:31:29 - INFO - 完成地址组 Yemekhane_Cihazlar_Guncel 解析，包含 29 个成员
2025-08-02 10:31:29 - INFO - 解析地址组: isa_turgut_konferans_salonu
2025-08-02 10:31:29 - INFO - 设置地址组成员数: 10
2025-08-02 10:31:29 - INFO - 完成地址组 isa_turgut_konferans_salonu 解析，包含 10 个成员
2025-08-02 10:31:29 - INFO - 解析地址组: turnike_sistemleri
2025-08-02 10:31:29 - INFO - 设置地址组成员数: 2
2025-08-02 10:31:29 - INFO - 完成地址组 turnike_sistemleri 解析，包含 2 个成员
2025-08-02 10:31:29 - INFO - 解析地址组: ETISAN_POS
2025-08-02 10:31:29 - INFO - 设置地址组成员数: 23
2025-08-02 10:31:29 - INFO - 完成地址组 ETISAN_POS 解析，包含 23 个成员
2025-08-02 10:31:29 - INFO - 解析地址组: SifreSormaGroup
2025-08-02 10:31:29 - WARNING - 警告: 行 11379 过长 (1048 字符)，已截断
2025-08-02 10:31:29 - INFO - 设置地址组成员数: 49
2025-08-02 10:31:29 - INFO - 设置地址组备注: captive portal sormadan giris
2025-08-02 10:31:29 - INFO - 完成地址组 SifreSormaGroup 解析，包含 49 个成员
2025-08-02 10:31:29 - INFO - 解析地址组: Santraller
2025-08-02 10:31:29 - INFO - 设置地址组成员数: 15
2025-08-02 10:31:29 - INFO - 完成地址组 Santraller 解析，包含 15 个成员
2025-08-02 10:31:29 - INFO - 解析地址组: SBBF_LAB_Opera
2025-08-02 10:31:29 - INFO - 设置地址组成员数: 22
2025-08-02 10:31:29 - INFO - 完成地址组 SBBF_LAB_Opera 解析，包含 22 个成员
2025-08-02 10:31:29 - INFO - 解析地址组: hoptodesk
2025-08-02 10:31:29 - INFO - 设置地址组成员数: 5
2025-08-02 10:31:29 - INFO - 完成地址组 hoptodesk 解析，包含 5 个成员
2025-08-02 10:31:29 - INFO - 解析地址组: NTP
2025-08-02 10:31:29 - INFO - 设置地址组成员数: 3
2025-08-02 10:31:29 - INFO - 完成地址组 NTP 解析，包含 3 个成员
2025-08-02 10:31:29 - INFO - 解析地址组: RPA_GRP
2025-08-02 10:31:29 - INFO - 设置地址组成员数: 5
2025-08-02 10:31:29 - INFO - 完成地址组 RPA_GRP 解析，包含 5 个成员
2025-08-02 10:31:29 - INFO - 解析地址组: YSLMYOAP
2025-08-02 10:31:29 - INFO - 设置地址组成员数: 3
2025-08-02 10:31:29 - INFO - 完成地址组 YSLMYOAP 解析，包含 3 个成员
2025-08-02 10:31:29 - INFO - 未知 配置部分结束
2025-08-02 10:31:29 - WARNING - 警告: 行 11555 包含可疑字符，可能存在命令注入风险: edit "VoIP, Messaging & Other Applications"
2025-08-02 10:31:29 - INFO - 未知 配置部分结束
2025-08-02 10:31:29 - INFO - 开始解析自定义服务配置部分
2025-08-02 10:31:29 - INFO - 解析自定义服务: DNS
2025-08-02 10:31:29 - INFO - 设置服务TCP端口范围: 53
2025-08-02 10:31:29 - INFO - 设置服务UDP端口范围: 53
2025-08-02 10:31:29 - INFO - 完成自定义服务 DNS 解析
2025-08-02 10:31:29 - INFO - 解析自定义服务: HTTP
2025-08-02 10:31:29 - INFO - 设置服务TCP端口范围: 80
2025-08-02 10:31:29 - INFO - 完成自定义服务 HTTP 解析
2025-08-02 10:31:29 - INFO - 解析自定义服务: HTTPS
2025-08-02 10:31:29 - INFO - 设置服务TCP端口范围: 443
2025-08-02 10:31:29 - INFO - 完成自定义服务 HTTPS 解析
2025-08-02 10:31:29 - INFO - 解析自定义服务: IMAP
2025-08-02 10:31:29 - INFO - 设置服务TCP端口范围: 143
2025-08-02 10:31:29 - INFO - 完成自定义服务 IMAP 解析
2025-08-02 10:31:29 - INFO - 解析自定义服务: IMAPS
2025-08-02 10:31:29 - INFO - 设置服务TCP端口范围: 993
2025-08-02 10:31:29 - INFO - 完成自定义服务 IMAPS 解析
2025-08-02 10:31:29 - INFO - 解析自定义服务: LDAP
2025-08-02 10:31:29 - INFO - 设置服务TCP端口范围: 389
2025-08-02 10:31:29 - INFO - 完成自定义服务 LDAP 解析
2025-08-02 10:31:29 - INFO - 解析自定义服务: DCE-RPC
2025-08-02 10:31:29 - INFO - 设置服务TCP端口范围: 135
2025-08-02 10:31:29 - INFO - 设置服务UDP端口范围: 135
2025-08-02 10:31:29 - INFO - 完成自定义服务 DCE-RPC 解析
2025-08-02 10:31:29 - INFO - 解析自定义服务: POP3
2025-08-02 10:31:29 - INFO - 设置服务TCP端口范围: 110
2025-08-02 10:31:29 - INFO - 完成自定义服务 POP3 解析
2025-08-02 10:31:29 - INFO - 解析自定义服务: POP3S
2025-08-02 10:31:29 - INFO - 设置服务TCP端口范围: 995
2025-08-02 10:31:29 - INFO - 完成自定义服务 POP3S 解析
2025-08-02 10:31:29 - INFO - 解析自定义服务: SAMBA
2025-08-02 10:31:29 - INFO - 设置服务TCP端口范围: 139
2025-08-02 10:31:29 - INFO - 完成自定义服务 SAMBA 解析
2025-08-02 10:31:29 - INFO - 解析自定义服务: SMTP
2025-08-02 10:31:29 - INFO - 设置服务TCP端口范围: 25
2025-08-02 10:31:29 - INFO - 完成自定义服务 SMTP 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: SMTPS
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 465
2025-08-02 10:31:30 - INFO - 完成自定义服务 SMTPS 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: KERBEROS
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 88 464
2025-08-02 10:31:30 - INFO - 设置服务UDP端口范围: 88 464
2025-08-02 10:31:30 - INFO - 完成自定义服务 KERBEROS 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: LDAP_UDP
2025-08-02 10:31:30 - INFO - 设置服务UDP端口范围: 389
2025-08-02 10:31:30 - INFO - 完成自定义服务 LDAP_UDP 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: SMB
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 445
2025-08-02 10:31:30 - INFO - 完成自定义服务 SMB 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: FTP
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 21
2025-08-02 10:31:30 - INFO - 完成自定义服务 FTP 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: FTP_GET
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 21
2025-08-02 10:31:30 - INFO - 完成自定义服务 FTP_GET 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: FTP_PUT
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 21
2025-08-02 10:31:30 - INFO - 完成自定义服务 FTP_PUT 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: ALL
2025-08-02 10:31:30 - INFO - Setting service protocol: IP
2025-08-02 10:31:30 - INFO - 完成自定义服务 ALL 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: ALL_TCP
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 1-65535
2025-08-02 10:31:30 - INFO - 完成自定义服务 ALL_TCP 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: ALL_UDP
2025-08-02 10:31:30 - INFO - 设置服务UDP端口范围: 1-65535
2025-08-02 10:31:30 - INFO - 完成自定义服务 ALL_UDP 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: ALL_ICMP
2025-08-02 10:31:30 - INFO - Setting service protocol: ICMP
2025-08-02 10:31:30 - INFO - 完成自定义服务 ALL_ICMP 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: ALL_ICMP6
2025-08-02 10:31:30 - INFO - Setting service protocol: ICMP6
2025-08-02 10:31:30 - INFO - 完成自定义服务 ALL_ICMP6 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: GRE
2025-08-02 10:31:30 - INFO - Setting service protocol: IP
2025-08-02 10:31:30 - INFO - fortigate.set_service_protocol_number
2025-08-02 10:31:30 - INFO - 完成自定义服务 GRE 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: AH
2025-08-02 10:31:30 - INFO - Setting service protocol: IP
2025-08-02 10:31:30 - INFO - fortigate.set_service_protocol_number
2025-08-02 10:31:30 - INFO - 完成自定义服务 AH 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: ESP
2025-08-02 10:31:30 - INFO - Setting service protocol: IP
2025-08-02 10:31:30 - INFO - fortigate.set_service_protocol_number
2025-08-02 10:31:30 - INFO - 完成自定义服务 ESP 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: AOL
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 5190-5194
2025-08-02 10:31:30 - INFO - 完成自定义服务 AOL 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: BGP
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 179
2025-08-02 10:31:30 - INFO - 完成自定义服务 BGP 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: DHCP
2025-08-02 10:31:30 - INFO - 设置服务UDP端口范围: 67-68
2025-08-02 10:31:30 - INFO - 完成自定义服务 DHCP 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: FINGER
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 79
2025-08-02 10:31:30 - INFO - 完成自定义服务 FINGER 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: GOPHER
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 70
2025-08-02 10:31:30 - INFO - 完成自定义服务 GOPHER 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: H323
2025-08-02 10:31:30 - WARNING - 警告: 行 11693 包含可疑字符，可能存在命令注入风险: set category "VoIP, Messaging & Other Applications"
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 1720 1503
2025-08-02 10:31:30 - INFO - 设置服务UDP端口范围: 1719
2025-08-02 10:31:30 - INFO - 完成自定义服务 H323 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: IKE
2025-08-02 10:31:30 - INFO - 设置服务UDP端口范围: 500 4500
2025-08-02 10:31:30 - INFO - 完成自定义服务 IKE 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: Internet-Locator-Service
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 389
2025-08-02 10:31:30 - INFO - 完成自定义服务 Internet-Locator-Service 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: IRC
2025-08-02 10:31:30 - WARNING - 警告: 行 11705 包含可疑字符，可能存在命令注入风险: set category "VoIP, Messaging & Other Applications"
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 6660-6669
2025-08-02 10:31:30 - INFO - 完成自定义服务 IRC 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: L2TP
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 1701
2025-08-02 10:31:30 - INFO - 设置服务UDP端口范围: 1701
2025-08-02 10:31:30 - INFO - 完成自定义服务 L2TP 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: NetMeeting
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 1720
2025-08-02 10:31:30 - INFO - 完成自定义服务 NetMeeting 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: NFS
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 111 2049
2025-08-02 10:31:30 - INFO - 设置服务UDP端口范围: 111 2049
2025-08-02 10:31:30 - INFO - 完成自定义服务 NFS 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: NNTP
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 119
2025-08-02 10:31:30 - INFO - 完成自定义服务 NNTP 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: NTP
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 123
2025-08-02 10:31:30 - INFO - 设置服务UDP端口范围: 123
2025-08-02 10:31:30 - INFO - 完成自定义服务 NTP 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: OSPF
2025-08-02 10:31:30 - INFO - Setting service protocol: IP
2025-08-02 10:31:30 - INFO - fortigate.set_service_protocol_number
2025-08-02 10:31:30 - INFO - 完成自定义服务 OSPF 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: PC-Anywhere
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 5631
2025-08-02 10:31:30 - INFO - 设置服务UDP端口范围: 5632
2025-08-02 10:31:30 - INFO - 完成自定义服务 PC-Anywhere 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: PING
2025-08-02 10:31:30 - INFO - Setting service protocol: ICMP
2025-08-02 10:31:30 - INFO - fortigate.set_service_icmptype
2025-08-02 10:31:30 - INFO - 完成自定义服务 PING 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: TIMESTAMP
2025-08-02 10:31:30 - INFO - Setting service protocol: ICMP
2025-08-02 10:31:30 - INFO - fortigate.set_service_icmptype
2025-08-02 10:31:30 - INFO - 完成自定义服务 TIMESTAMP 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: INFO_REQUEST
2025-08-02 10:31:30 - INFO - Setting service protocol: ICMP
2025-08-02 10:31:30 - INFO - fortigate.set_service_icmptype
2025-08-02 10:31:30 - INFO - 完成自定义服务 INFO_REQUEST 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: INFO_ADDRESS
2025-08-02 10:31:30 - INFO - Setting service protocol: ICMP
2025-08-02 10:31:30 - INFO - fortigate.set_service_icmptype
2025-08-02 10:31:30 - INFO - 完成自定义服务 INFO_ADDRESS 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: ONC-RPC
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 111
2025-08-02 10:31:30 - INFO - 设置服务UDP端口范围: 111
2025-08-02 10:31:30 - INFO - 完成自定义服务 ONC-RPC 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: PPTP
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 1723
2025-08-02 10:31:30 - INFO - 完成自定义服务 PPTP 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: QUAKE
2025-08-02 10:31:30 - INFO - 设置服务UDP端口范围: 26000 27000 27910 27960
2025-08-02 10:31:30 - INFO - 完成自定义服务 QUAKE 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: RAUDIO
2025-08-02 10:31:30 - INFO - 设置服务UDP端口范围: 7070
2025-08-02 10:31:30 - INFO - 完成自定义服务 RAUDIO 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: REXEC
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 512
2025-08-02 10:31:30 - INFO - 完成自定义服务 REXEC 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: RIP
2025-08-02 10:31:30 - INFO - 设置服务UDP端口范围: 520
2025-08-02 10:31:30 - INFO - 完成自定义服务 RIP 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: RLOGIN
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 513:512-1023
2025-08-02 10:31:30 - INFO - 完成自定义服务 RLOGIN 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: RSH
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 514:512-1023
2025-08-02 10:31:30 - INFO - 完成自定义服务 RSH 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: SCCP
2025-08-02 10:31:30 - WARNING - 警告: 行 11789 包含可疑字符，可能存在命令注入风险: set category "VoIP, Messaging & Other Applications"
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 2000
2025-08-02 10:31:30 - INFO - 完成自定义服务 SCCP 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: SIP
2025-08-02 10:31:30 - WARNING - 警告: 行 11793 包含可疑字符，可能存在命令注入风险: set category "VoIP, Messaging & Other Applications"
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 5060
2025-08-02 10:31:30 - INFO - 设置服务UDP端口范围: 5060
2025-08-02 10:31:30 - INFO - 完成自定义服务 SIP 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: SIP-MSNmessenger
2025-08-02 10:31:30 - WARNING - 警告: 行 11798 包含可疑字符，可能存在命令注入风险: set category "VoIP, Messaging & Other Applications"
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 1863
2025-08-02 10:31:30 - INFO - 完成自定义服务 SIP-MSNmessenger 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: SNMP
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 161-162
2025-08-02 10:31:30 - INFO - 设置服务UDP端口范围: 161-162
2025-08-02 10:31:30 - INFO - 完成自定义服务 SNMP 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: SSH
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 22
2025-08-02 10:31:30 - INFO - 完成自定义服务 SSH 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: SYSLOG
2025-08-02 10:31:30 - INFO - 设置服务UDP端口范围: 514
2025-08-02 10:31:30 - INFO - 完成自定义服务 SYSLOG 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: TALK
2025-08-02 10:31:30 - INFO - 设置服务UDP端口范围: 517-518
2025-08-02 10:31:30 - INFO - 完成自定义服务 TALK 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: TELNET
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 23
2025-08-02 10:31:30 - INFO - 完成自定义服务 TELNET 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: TFTP
2025-08-02 10:31:30 - INFO - 设置服务UDP端口范围: 69
2025-08-02 10:31:30 - INFO - 完成自定义服务 TFTP 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: MGCP
2025-08-02 10:31:30 - WARNING - 警告: 行 11826 包含可疑字符，可能存在命令注入风险: set category "VoIP, Messaging & Other Applications"
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 2428
2025-08-02 10:31:30 - INFO - 设置服务UDP端口范围: 2427 2727
2025-08-02 10:31:30 - INFO - 完成自定义服务 MGCP 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: UUCP
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 540
2025-08-02 10:31:30 - INFO - 完成自定义服务 UUCP 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: VDOLIVE
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 7000-7010
2025-08-02 10:31:30 - INFO - 完成自定义服务 VDOLIVE 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: WAIS
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 210
2025-08-02 10:31:30 - INFO - 完成自定义服务 WAIS 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: WINFRAME
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 1494 2598
2025-08-02 10:31:30 - INFO - 完成自定义服务 WINFRAME 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: X-WINDOWS
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 6000-6063
2025-08-02 10:31:30 - INFO - 完成自定义服务 X-WINDOWS 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: PING6
2025-08-02 10:31:30 - INFO - Setting service protocol: ICMP6
2025-08-02 10:31:30 - INFO - fortigate.set_service_icmptype
2025-08-02 10:31:30 - INFO - 完成自定义服务 PING6 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: MS-SQL
2025-08-02 10:31:30 - WARNING - 警告: 行 11852 包含可疑字符，可能存在命令注入风险: set category "VoIP, Messaging & Other Applications"
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 1433 1434
2025-08-02 10:31:30 - INFO - 完成自定义服务 MS-SQL 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: MYSQL
2025-08-02 10:31:30 - WARNING - 警告: 行 11856 包含可疑字符，可能存在命令注入风险: set category "VoIP, Messaging & Other Applications"
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 3306
2025-08-02 10:31:30 - INFO - 完成自定义服务 MYSQL 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: RDP
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 3389
2025-08-02 10:31:30 - INFO - 完成自定义服务 RDP 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: VNC
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 5900
2025-08-02 10:31:30 - INFO - 完成自定义服务 VNC 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: DHCP6
2025-08-02 10:31:30 - INFO - 设置服务UDP端口范围: 546 547
2025-08-02 10:31:30 - INFO - 完成自定义服务 DHCP6 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: SQUID
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 3128
2025-08-02 10:31:30 - INFO - 完成自定义服务 SQUID 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: SOCKS
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 1080
2025-08-02 10:31:30 - INFO - 设置服务UDP端口范围: 1080
2025-08-02 10:31:30 - INFO - 完成自定义服务 SOCKS 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: WINS
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 1512
2025-08-02 10:31:30 - INFO - 设置服务UDP端口范围: 1512
2025-08-02 10:31:30 - INFO - 完成自定义服务 WINS 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: RADIUS
2025-08-02 10:31:30 - INFO - 设置服务UDP端口范围: 1812 1813
2025-08-02 10:31:30 - INFO - 完成自定义服务 RADIUS 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: RADIUS-OLD
2025-08-02 10:31:30 - INFO - 设置服务UDP端口范围: 1645 1646
2025-08-02 10:31:30 - INFO - 完成自定义服务 RADIUS-OLD 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: CVSPSERVER
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 2401
2025-08-02 10:31:30 - INFO - 设置服务UDP端口范围: 2401
2025-08-02 10:31:30 - INFO - 完成自定义服务 CVSPSERVER 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: AFS3
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 7000-7009
2025-08-02 10:31:30 - INFO - 设置服务UDP端口范围: 7000-7009
2025-08-02 10:31:30 - INFO - 完成自定义服务 AFS3 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: TRACEROUTE
2025-08-02 10:31:30 - INFO - 设置服务UDP端口范围: 33434-33535
2025-08-02 10:31:30 - INFO - 完成自定义服务 TRACEROUTE 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: RTSP
2025-08-02 10:31:30 - WARNING - 警告: 行 11906 包含可疑字符，可能存在命令注入风险: set category "VoIP, Messaging & Other Applications"
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 554 7070 8554
2025-08-02 10:31:30 - INFO - 设置服务UDP端口范围: 554
2025-08-02 10:31:30 - INFO - 完成自定义服务 RTSP 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: MMS
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 1755
2025-08-02 10:31:30 - INFO - 设置服务UDP端口范围: 1024-5000
2025-08-02 10:31:30 - INFO - 完成自定义服务 MMS 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: NONE
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 0
2025-08-02 10:31:30 - INFO - 完成自定义服务 NONE 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: webproxy
2025-08-02 10:31:30 - INFO - Setting service protocol: ALL
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 0-65535:0-65535
2025-08-02 10:31:30 - INFO - 完成自定义服务 webproxy 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: UDP_111
2025-08-02 10:31:30 - INFO - 设置服务UDP端口范围: 111
2025-08-02 10:31:30 - INFO - 完成自定义服务 UDP_111 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: TCP_587
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 587
2025-08-02 10:31:30 - INFO - 完成自定义服务 TCP_587 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: UDP_SANTRAL_SERVICE
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 30000-39999
2025-08-02 10:31:30 - INFO - 设置服务UDP端口范围: 9877 9878 30000-39999 9999-20000
2025-08-02 10:31:30 - INFO - 完成自定义服务 UDP_SANTRAL_SERVICE 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: TCP_8080
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 8080
2025-08-02 10:31:30 - INFO - 完成自定义服务 TCP_8080 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: TCP_7443
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 7443
2025-08-02 10:31:30 - INFO - 完成自定义服务 TCP_7443 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: UDP_1935
2025-08-02 10:31:30 - INFO - 设置服务UDP端口范围: 1935
2025-08-02 10:31:30 - INFO - 完成自定义服务 UDP_1935 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: UDP_16384-32768
2025-08-02 10:31:30 - INFO - 设置服务UDP端口范围: 16384-32768
2025-08-02 10:31:30 - INFO - 完成自定义服务 UDP_16384-32768 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: DSPACE_PORTS
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 2641 8000
2025-08-02 10:31:30 - INFO - 设置服务UDP端口范围: 2641
2025-08-02 10:31:30 - INFO - 完成自定义服务 DSPACE_PORTS 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: TCP_UDP_137
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 137
2025-08-02 10:31:30 - INFO - 设置服务UDP端口范围: 137
2025-08-02 10:31:30 - INFO - 完成自定义服务 TCP_UDP_137 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: TCP_636
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 636
2025-08-02 10:31:30 - INFO - 完成自定义服务 TCP_636 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: TCP_3268-3269
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 3268 3269
2025-08-02 10:31:30 - INFO - 完成自定义服务 TCP_3268-3269 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: TCP_5722
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 5722
2025-08-02 10:31:30 - INFO - 完成自定义服务 TCP_5722 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: TCP_9389
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 9389
2025-08-02 10:31:30 - INFO - 完成自定义服务 TCP_9389 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: UDP_138
2025-08-02 10:31:30 - INFO - 设置服务UDP端口范围: 138
2025-08-02 10:31:30 - INFO - 完成自定义服务 UDP_138 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: UDP_2535
2025-08-02 10:31:30 - INFO - 设置服务UDP端口范围: 2535
2025-08-02 10:31:30 - INFO - 完成自定义服务 UDP_2535 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: TCP_34210
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 34210
2025-08-02 10:31:30 - INFO - 完成自定义服务 TCP_34210 解析
2025-08-02 10:31:30 - INFO - 解析自定义服务: TCP_5003
2025-08-02 10:31:30 - INFO - 设置服务TCP端口范围: 5003
2025-08-02 10:31:31 - INFO - 完成自定义服务 TCP_5003 解析
2025-08-02 10:31:31 - INFO - 解析自定义服务: TCP_81
2025-08-02 10:31:31 - INFO - 设置服务TCP端口范围: 81
2025-08-02 10:31:31 - INFO - 完成自定义服务 TCP_81 解析
2025-08-02 10:31:31 - INFO - 解析自定义服务: TCP_82
2025-08-02 10:31:31 - INFO - 设置服务TCP端口范围: 82
2025-08-02 10:31:31 - INFO - 完成自定义服务 TCP_82 解析
2025-08-02 10:31:31 - INFO - 解析自定义服务: TCP_4444
2025-08-02 10:31:31 - INFO - 设置服务TCP端口范围: 4444
2025-08-02 10:31:31 - INFO - 完成自定义服务 TCP_4444 解析
2025-08-02 10:31:31 - INFO - 解析自定义服务: TCP_8110
2025-08-02 10:31:31 - INFO - 设置服务TCP端口范围: 8110
2025-08-02 10:31:31 - INFO - 完成自定义服务 TCP_8110 解析
2025-08-02 10:31:31 - INFO - 解析自定义服务: AhmetYesevi_Unı
2025-08-02 10:31:31 - INFO - 设置服务TCP端口范围: 4480
2025-08-02 10:31:31 - INFO - 完成自定义服务 AhmetYesevi_Unı 解析
2025-08-02 10:31:31 - INFO - 解析自定义服务: Smtp
2025-08-02 10:31:31 - INFO - 设置服务TCP端口范围: 587
2025-08-02 10:31:31 - INFO - 完成自定义服务 Smtp 解析
2025-08-02 10:31:31 - INFO - 解析自定义服务: RustdeskPorts
2025-08-02 10:31:31 - INFO - 设置服务TCP端口范围: 21114-21119
2025-08-02 10:31:31 - INFO - 设置服务UDP端口范围: 21116
2025-08-02 10:31:31 - INFO - 完成自定义服务 RustdeskPorts 解析
2025-08-02 10:31:31 - INFO - 解析自定义服务: TCP_10443
2025-08-02 10:31:31 - INFO - 设置服务TCP端口范围: 10443
2025-08-02 10:31:31 - INFO - 完成自定义服务 TCP_10443 解析
2025-08-02 10:31:31 - INFO - 解析自定义服务: TCP_8081
2025-08-02 10:31:31 - INFO - 设置服务TCP端口范围: 8081
2025-08-02 10:31:31 - INFO - 完成自定义服务 TCP_8081 解析
2025-08-02 10:31:31 - INFO - 解析自定义服务: UDP_443
2025-08-02 10:31:31 - INFO - 设置服务UDP端口范围: 443
2025-08-02 10:31:31 - INFO - 完成自定义服务 UDP_443 解析
2025-08-02 10:31:31 - INFO - 解析自定义服务: DahuaPort
2025-08-02 10:31:31 - INFO - 设置服务TCP端口范围: 37777 80 554 443
2025-08-02 10:31:31 - INFO - 设置服务UDP端口范围: 37778
2025-08-02 10:31:31 - INFO - 完成自定义服务 DahuaPort 解析
2025-08-02 10:31:31 - INFO - 解析自定义服务: hoptodesk_ports
2025-08-02 10:31:31 - INFO - 设置服务TCP端口范围: 80 443
2025-08-02 10:31:31 - INFO - 设置服务UDP端口范围: 49152-65535
2025-08-02 10:31:31 - INFO - 完成自定义服务 hoptodesk_ports 解析
2025-08-02 10:31:31 - INFO - 解析自定义服务: 9090
2025-08-02 10:31:31 - INFO - 设置服务TCP端口范围: 9090
2025-08-02 10:31:31 - INFO - 完成自定义服务 9090 解析
2025-08-02 10:31:31 - INFO - 解析自定义服务: MEZUN_PORTLAR
2025-08-02 10:31:31 - INFO - 设置服务TCP端口范围: 4848 9090 5443 8080 8181 5432
2025-08-02 10:31:31 - INFO - 完成自定义服务 MEZUN_PORTLAR 解析
2025-08-02 10:31:31 - INFO - 解析自定义服务: 12346
2025-08-02 10:31:31 - INFO - 设置服务备注: Ziraat_Scada
2025-08-02 10:31:31 - INFO - 设置服务TCP端口范围: 12346
2025-08-02 10:31:31 - INFO - 完成自定义服务 12346 解析
2025-08-02 10:31:31 - INFO - 解析自定义服务: Port_Servis
2025-08-02 10:31:31 - INFO - 设置服务TCP端口范围: 4000
2025-08-02 10:31:31 - INFO - 完成自定义服务 Port_Servis 解析
2025-08-02 10:31:31 - INFO - 解析自定义服务: tcp_8090
2025-08-02 10:31:31 - INFO - 设置服务TCP端口范围: 8090
2025-08-02 10:31:31 - INFO - 完成自定义服务 tcp_8090 解析
2025-08-02 10:31:31 - INFO - 解析自定义服务: tcp_7080
2025-08-02 10:31:31 - INFO - 设置服务TCP端口范围: 7080
2025-08-02 10:31:31 - INFO - 完成自定义服务 tcp_7080 解析
2025-08-02 10:31:31 - INFO - 开始解析服务组配置部分
2025-08-02 10:31:31 - INFO - 解析服务组: Email Access
2025-08-02 10:31:31 - INFO - 设置服务组成员数: 7
2025-08-02 10:31:31 - INFO - 完成服务组 Email Access 解析，包含 7 个成员
2025-08-02 10:31:31 - INFO - 解析服务组: Web Access
2025-08-02 10:31:31 - INFO - 设置服务组成员数: 3
2025-08-02 10:31:31 - INFO - 完成服务组 Web Access 解析，包含 3 个成员
2025-08-02 10:31:31 - INFO - 解析服务组: Windows AD
2025-08-02 10:31:31 - INFO - 设置服务组成员数: 7
2025-08-02 10:31:31 - INFO - 完成服务组 Windows AD 解析，包含 7 个成员
2025-08-02 10:31:31 - INFO - 解析服务组: Exchange Server
2025-08-02 10:31:31 - INFO - 设置服务组成员数: 3
2025-08-02 10:31:31 - INFO - 完成服务组 Exchange Server 解析，包含 3 个成员
2025-08-02 10:31:31 - INFO - 解析服务组: AD_ALL
2025-08-02 10:31:31 - INFO - 设置服务组成员数: 17
2025-08-02 10:31:31 - INFO - 完成服务组 AD_ALL 解析，包含 17 个成员
2025-08-02 10:31:31 - INFO - 解析服务组: GenelIzinler
2025-08-02 10:31:31 - INFO - 设置服务组成员数: 13
2025-08-02 10:31:31 - INFO - 完成服务组 GenelIzinler 解析，包含 13 个成员
2025-08-02 10:31:31 - INFO - 未知 配置部分结束
2025-08-02 10:31:31 - INFO - 未知 配置部分结束
2025-08-02 10:31:31 - INFO - 未知 配置部分结束
2025-08-02 10:31:31 - INFO - 未知 配置部分结束
2025-08-02 10:31:31 - INFO - 未知 配置部分结束
2025-08-02 10:31:31 - INFO - 未知 配置部分结束
2025-08-02 10:31:32 - INFO - 未知 配置部分结束
2025-08-02 10:31:32 - INFO - 未知 配置部分结束
2025-08-02 10:31:32 - INFO - 未知 配置部分结束
2025-08-02 10:31:32 - INFO - 未知 配置部分结束
2025-08-02 10:31:32 - INFO - 未知 配置部分结束
2025-08-02 10:31:32 - INFO - 未知 配置部分结束
2025-08-02 10:31:32 - INFO - 未知 配置部分结束
2025-08-02 10:31:32 - INFO - 未知 配置部分结束
2025-08-02 10:31:32 - INFO - 未知 配置部分结束
2025-08-02 10:31:32 - WARNING - 警告: 行 12718 包含可疑字符，可能存在命令注入风险: set host-regex "^([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])(\\.([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])){3}$"
2025-08-02 10:31:32 - WARNING - 消息中缺少参数: 0,4, 1,7, 1,4
2025-08-02 10:31:32 - INFO - 未知 配置部分结束
2025-08-02 10:31:32 - INFO - 未知 配置部分结束
2025-08-02 10:31:32 - WARNING - 警告: 行 12732 包含可疑字符，可能存在命令注入风险: set signature "F-SBID( --attack_id 8256; --name \"Psiphon.resume.client.tagtwo\"; --protocol tcp; --app_cat 6; --weight 20; --service ssl; --flow from_client; --seq =,1,relative; --data_size <512; --pattern \"|160301|\"; --context packet; --within 3,context; --pattern \"|01 00|\"; --context packet; --distance 2; --within 2; --pattern !\"|0000000000000000|\"; --context packet; --pcre \"/\\x00\\x23\\x00[\\x64-\\xff]/\"; --context packet; --tag set,tag.psi.resume.client.custom; --tag quiet; )"
2025-08-02 10:31:32 - WARNING - 警告: 行 12737 包含可疑字符，可能存在命令注入风险: set signature "F-SBID( --attack_id 8168; --name \"Psiphon.resume.server.tag\"; --protocol tcp; --app_cat 6; --weight 20; --service ssl; --flow from_server; --seq =,1,relative; --data_size >290; --data_size <440; --tag test,tag.psi.resume.client.custom; --pattern \"|160303|\"; --context packet; --within 3,context; --pattern \"|02 00|\"; --context packet; --distance 2; --within 2; --pattern \"|20|\"; --context packet; --distance 36; --within 1; --pattern \"|c02f0000|\"; --context packet; --distance 32; --within 4; --pattern \"|000b0002010000230000|\"; --context packet; --pattern \"|160303|\"; --context packet; --distance 0; --within 30; --pattern \"|0400|\"; --context packet; --distance 2; --within 2; --pattern \"|1403030001011603030028|\"; --context packet; --distance 100; --tag set,tag.psi.resume.server.varlength.custom; --tag quiet; )"
2025-08-02 10:31:32 - WARNING - 警告: 行 12742 包含可疑字符，可能存在命令注入风险: set signature "F-SBID( --attack_id 2651; --name \"Psiphon.resume.test\"; --protocol tcp; --app_cat 6; --weight 20; --service ssl; --flow from_client; --seq >,298,relative; --seq <,519,relative; --data_size =51; --tag test,tag.psi.resume.server.varlength.custom; --pattern \"|1403030001011603030028|\"; --context packet; --within 11,context; )"
2025-08-02 10:31:32 - INFO - 未知 配置部分结束
2025-08-02 10:31:32 - INFO - 未知 配置部分结束
2025-08-02 10:31:32 - INFO - 未知 配置部分结束
2025-08-02 10:31:32 - INFO - 未知 配置部分结束
2025-08-02 10:31:32 - INFO - 未知 配置部分结束
2025-08-02 10:31:32 - INFO - 未知 配置部分结束
2025-08-02 10:31:32 - INFO - 未知 配置部分结束
2025-08-02 10:31:32 - INFO - 未知 配置部分结束
2025-08-02 10:31:32 - INFO - 未知 配置部分结束
2025-08-02 10:31:32 - INFO - 未知 配置部分结束
2025-08-02 10:31:32 - INFO - 未知 配置部分结束
2025-08-02 10:31:32 - WARNING - 消息中缺少参数: 8, 4, 4, 4, 12
2025-08-02 10:31:32 - WARNING - 格式错误: Replacement index 8 out of range for positional args tuple
2025-08-02 10:31:32 - WARNING - 消息中缺少参数: 1, 3, 4, 2, 2, 2,4
2025-08-02 10:31:32 - WARNING - 格式错误: Replacement index 1 out of range for positional args tuple
2025-08-02 10:31:32 - WARNING - 消息中缺少参数: 3, 2, 4
2025-08-02 10:31:32 - WARNING - 格式错误: Replacement index 3 out of range for positional args tuple
2025-08-02 10:31:32 - WARNING - 警告: 行 12891 包含可疑字符，可能存在命令注入风险: set verify "(?<!-)\\b(?!666|000|9\\d{2})\\d{3}-(?!00)\\d{2}-(?!0{4})\\d{4}\\b(?!-)"
2025-08-02 10:31:32 - INFO - 未知 配置部分结束
2025-08-02 10:31:32 - INFO - 未知 配置部分结束
2025-08-02 10:31:32 - INFO - 未知 配置部分结束
2025-08-02 10:31:32 - INFO - 未知 配置部分结束
2025-08-02 10:31:32 - INFO - 未知 配置部分结束
2025-08-02 10:31:32 - INFO - 未知 配置部分结束
2025-08-02 10:31:32 - INFO - 未知 配置部分结束
2025-08-02 10:31:32 - INFO - 未知 配置部分结束
2025-08-02 10:31:32 - INFO - 未知 配置部分结束
2025-08-02 10:31:32 - INFO - 未知 配置部分结束
2025-08-02 10:31:32 - INFO - 未知 配置部分结束
2025-08-02 10:31:32 - INFO - 未知 配置部分结束
2025-08-02 10:31:32 - INFO - 未知 配置部分结束
2025-08-02 10:31:32 - INFO - 未知 配置部分结束
2025-08-02 10:31:32 - INFO - 未知 配置部分结束
2025-08-02 10:31:32 - INFO - 未知 配置部分结束
2025-08-02 10:31:32 - INFO - 未知 配置部分结束
2025-08-02 10:31:32 - INFO - 未知 配置部分结束
2025-08-02 10:31:32 - INFO - Start Parsing Local Users
2025-08-02 10:31:32 - INFO - Parsing Local User
2025-08-02 10:31:32 - INFO - Set User Auth Type
2025-08-02 10:31:32 - INFO - Set User Password
2025-08-02 10:31:32 - INFO - Local User Parsing Complete
2025-08-02 10:31:32 - INFO - Parsing Local User
2025-08-02 10:31:32 - INFO - Set User Auth Type
2025-08-02 10:31:32 - INFO - Set User Password
2025-08-02 10:31:32 - INFO - Set User Password
2025-08-02 10:31:32 - INFO - Local User Parsing Complete
2025-08-02 10:31:32 - INFO - Parsing Local User
2025-08-02 10:31:32 - INFO - Set User Auth Type
2025-08-02 10:31:32 - INFO - Set User Password
2025-08-02 10:31:32 - INFO - Set User Password
2025-08-02 10:31:32 - INFO - Local User Parsing Complete
2025-08-02 10:31:32 - INFO - Parsing Local User
2025-08-02 10:31:32 - INFO - Set User Auth Type
2025-08-02 10:31:32 - INFO - Set User Password
2025-08-02 10:31:32 - INFO - Set User Password
2025-08-02 10:31:32 - INFO - Local User Parsing Complete
2025-08-02 10:31:32 - INFO - Parsing Local User
2025-08-02 10:31:32 - INFO - Set User Auth Type
2025-08-02 10:31:32 - INFO - Set User Password
2025-08-02 10:31:32 - INFO - Set User Password
2025-08-02 10:31:32 - INFO - Local User Parsing Complete
2025-08-02 10:31:32 - INFO - Parsing Local User
2025-08-02 10:31:32 - INFO - Set User Auth Type
2025-08-02 10:31:32 - INFO - Set User Password
2025-08-02 10:31:32 - INFO - Set User Password
2025-08-02 10:31:32 - INFO - Local User Parsing Complete
2025-08-02 10:31:32 - INFO - Parsing Local User
2025-08-02 10:31:32 - INFO - Set User Auth Type
2025-08-02 10:31:32 - INFO - Set User Password
2025-08-02 10:31:32 - INFO - Set User Password
2025-08-02 10:31:32 - INFO - Local User Parsing Complete
2025-08-02 10:31:32 - INFO - Parsing Local User
2025-08-02 10:31:32 - INFO - Set User Auth Type
2025-08-02 10:31:32 - INFO - Set User Password
2025-08-02 10:31:32 - INFO - Set User Password
2025-08-02 10:31:32 - INFO - Local User Parsing Complete
2025-08-02 10:31:32 - INFO - Parsing Local User
2025-08-02 10:31:32 - INFO - Set User Auth Type
2025-08-02 10:31:32 - INFO - Set User Password
2025-08-02 10:31:32 - INFO - Set User Password
2025-08-02 10:31:32 - INFO - Local User Parsing Complete
2025-08-02 10:31:32 - INFO - Parsing Local User
2025-08-02 10:31:32 - INFO - Set User Auth Type
2025-08-02 10:31:32 - INFO - Set User Password
2025-08-02 10:31:32 - INFO - Set User Password
2025-08-02 10:31:32 - INFO - Local User Parsing Complete
2025-08-02 10:31:32 - INFO - Parsing Local User
2025-08-02 10:31:32 - INFO - Set User Auth Type
2025-08-02 10:31:32 - INFO - Set User Password
2025-08-02 10:31:32 - INFO - Set User Password
2025-08-02 10:31:32 - INFO - Local User Parsing Complete
2025-08-02 10:31:32 - INFO - Parsing Local User
2025-08-02 10:31:32 - INFO - 设置用户状态: disable
2025-08-02 10:31:32 - INFO - Set User Auth Type
2025-08-02 10:31:32 - INFO - Set User Password
2025-08-02 10:31:32 - INFO - Set User Password
2025-08-02 10:31:32 - INFO - Local User Parsing Complete
2025-08-02 10:31:32 - INFO - Parsing Local User
2025-08-02 10:31:32 - INFO - Set User Auth Type
2025-08-02 10:31:32 - INFO - Set User Password
2025-08-02 10:31:32 - INFO - Set User Password
2025-08-02 10:31:32 - INFO - Local User Parsing Complete
2025-08-02 10:31:32 - INFO - Parsing Local User
2025-08-02 10:31:32 - INFO - Set User Auth Type
2025-08-02 10:31:32 - INFO - Set User Password
2025-08-02 10:31:32 - INFO - Set User Password
2025-08-02 10:31:32 - INFO - Local User Parsing Complete
2025-08-02 10:31:32 - INFO - Parsing Local User
2025-08-02 10:31:32 - INFO - Set User Auth Type
2025-08-02 10:31:32 - INFO - Set User Password
2025-08-02 10:31:32 - INFO - Set User Password
2025-08-02 10:31:32 - INFO - Local User Parsing Complete
2025-08-02 10:31:32 - INFO - Start Parsing User Settings
2025-08-02 10:31:32 - INFO - Set Auth Cert
2025-08-02 10:31:32 - INFO - Set Auth Timeout
2025-08-02 10:31:32 - INFO - 未知 配置部分结束
2025-08-02 10:31:32 - INFO - 未知 配置部分结束
2025-08-02 10:31:33 - INFO - 未知 配置部分结束
2025-08-02 10:31:33 - INFO - 未知 配置部分结束
2025-08-02 10:31:33 - INFO - 未知 配置部分结束
2025-08-02 10:31:33 - INFO - 未知 配置部分结束
2025-08-02 10:31:33 - INFO - 未知 配置部分结束
2025-08-02 10:31:33 - INFO - 未知 配置部分结束
2025-08-02 10:31:33 - INFO - 未知 配置部分结束
2025-08-02 10:31:33 - INFO - 未知 配置部分结束
2025-08-02 10:31:33 - INFO - 未知 配置部分结束
2025-08-02 10:31:33 - INFO - 未知 配置部分结束
2025-08-02 10:31:33 - INFO - 未知 配置部分结束
2025-08-02 10:31:33 - INFO - 未知 配置部分结束
2025-08-02 10:31:33 - INFO - 未知 配置部分结束
2025-08-02 10:31:33 - INFO - Start Parsing User Groups
2025-08-02 10:31:33 - INFO - Parsing User Group
2025-08-02 10:31:33 - INFO - User Group Parsing Complete
2025-08-02 10:31:33 - INFO - Parsing User Group
2025-08-02 10:31:33 - INFO - Set User Group Members
2025-08-02 10:31:33 - INFO - User Group Parsing Complete
2025-08-02 10:31:33 - INFO - Parsing User Group
2025-08-02 10:31:33 - INFO - Set User Group Members
2025-08-02 10:31:33 - INFO - User Group Parsing Complete
2025-08-02 10:31:33 - INFO - Parsing User Group
2025-08-02 10:31:33 - INFO - Set User Group Members
2025-08-02 10:31:33 - INFO - User Group Parsing Complete
2025-08-02 10:31:33 - INFO - Parsing User Group
2025-08-02 10:31:33 - INFO - Set User Group Members
2025-08-02 10:31:33 - INFO - User Group Parsing Complete
2025-08-02 10:31:33 - INFO - Parsing User Group
2025-08-02 10:31:33 - INFO - Set User Group Members
2025-08-02 10:31:33 - INFO - User Group Parsing Complete
2025-08-02 10:31:33 - INFO - Parsing User Group
2025-08-02 10:31:33 - INFO - Set User Group Members
2025-08-02 10:31:33 - INFO - User Group Parsing Complete
2025-08-02 10:31:33 - INFO - Parsing User Group
2025-08-02 10:31:33 - INFO - Set User Group Members
2025-08-02 10:31:33 - INFO - User Group Parsing Complete
2025-08-02 10:31:33 - INFO - Parsing User Group
2025-08-02 10:31:33 - INFO - Set User Group Members
2025-08-02 10:31:33 - INFO - User Group Parsing Complete
2025-08-02 10:31:33 - INFO - Parsing User Group
2025-08-02 10:31:33 - INFO - Set User Group Members
2025-08-02 10:31:33 - INFO - User Group Parsing Complete
2025-08-02 10:31:33 - INFO - Parsing User Group
2025-08-02 10:31:33 - INFO - Set User Group Members
2025-08-02 10:31:33 - INFO - User Group Parsing Complete
2025-08-02 10:31:33 - INFO - Parsing User Group
2025-08-02 10:31:33 - INFO - Set User Group Members
2025-08-02 10:31:33 - INFO - Parsing User Group
2025-08-02 10:31:33 - INFO - User Group Parsing Complete
2025-08-02 10:31:33 - INFO - 未知 配置部分结束
2025-08-02 10:31:33 - INFO - 未知 配置部分结束
2025-08-02 10:31:33 - INFO - 未知 配置部分结束
2025-08-02 10:31:33 - INFO - 未知 配置部分结束
2025-08-02 10:31:33 - INFO - 未知 配置部分结束
2025-08-02 10:31:33 - INFO - 未知 配置部分结束
2025-08-02 10:31:33 - INFO - 未知 配置部分结束
2025-08-02 10:31:33 - INFO - 未知 配置部分结束
2025-08-02 10:31:33 - INFO - 未知 配置部分结束
2025-08-02 10:31:33 - INFO - 未知 配置部分结束
2025-08-02 10:31:33 - INFO - 未知 配置部分结束
2025-08-02 10:31:33 - INFO - 未知 配置部分结束
2025-08-02 10:31:33 - INFO - 未知 配置部分结束
2025-08-02 10:31:33 - INFO - 未知 配置部分结束
2025-08-02 10:31:33 - INFO - 未知 配置部分结束
2025-08-02 10:31:34 - INFO - 未知 配置部分结束
2025-08-02 10:31:34 - INFO - Start Parsing Ssl Vpn Portal
2025-08-02 10:31:34 - INFO - Parsing Ssl Vpn Portal
2025-08-02 10:31:34 - INFO - Set Ssl Vpn Tunnel Mode
2025-08-02 10:31:34 - INFO - Set Ssl Vpn Web Mode
2025-08-02 10:31:34 - INFO - Set Ssl Vpn Split Tunneling
2025-08-02 10:31:34 - INFO - Parsing Ssl Vpn Portal
2025-08-02 10:31:34 - INFO - Ssl Vpn Portal Parsing Complete
2025-08-02 10:31:34 - INFO - 未知 配置部分结束
2025-08-02 10:31:34 - INFO - 未知 配置部分结束
2025-08-02 10:31:34 - INFO - Start Parsing Ssl Vpn Settings
2025-08-02 10:31:34 - INFO - Set Ssl Vpn Servercert
2025-08-02 10:31:34 - INFO - Set Ssl Vpn Ip Pools
2025-08-02 10:31:34 - INFO - Set Ssl Vpn Dns Server1
2025-08-02 10:31:34 - INFO - Set Ssl Vpn Dns Server2
2025-08-02 10:31:34 - INFO - Set Ssl Vpn Source Interface
2025-08-02 10:31:34 - INFO - Set Ssl Vpn Port
2025-08-02 10:31:34 - INFO - 未知 配置部分结束
2025-08-02 10:31:34 - INFO - 未知 配置部分结束
2025-08-02 10:31:34 - INFO - 未知 配置部分结束
2025-08-02 10:31:34 - INFO - 未知 配置部分结束
2025-08-02 10:31:34 - INFO - 未知 配置部分结束
2025-08-02 10:31:34 - INFO - 未知 配置部分结束
2025-08-02 10:31:34 - INFO - 未知 配置部分结束
2025-08-02 10:31:34 - INFO - 未知 配置部分结束
2025-08-02 10:31:34 - INFO - 未知 配置部分结束
2025-08-02 10:31:34 - INFO - 未知 配置部分结束
2025-08-02 10:31:34 - INFO - 未知 配置部分结束
2025-08-02 10:31:34 - INFO - 未知 配置部分结束
2025-08-02 10:31:34 - INFO - 未知 配置部分结束
2025-08-02 10:31:34 - INFO - 未知 配置部分结束
2025-08-02 10:31:34 - INFO - 未知 配置部分结束
2025-08-02 10:31:34 - INFO - 未知 配置部分结束
2025-08-02 10:31:34 - INFO - 未知 配置部分结束
2025-08-02 10:31:34 - INFO - 未知 配置部分结束
2025-08-02 10:31:34 - INFO - 未知 配置部分结束
2025-08-02 10:31:34 - INFO - 未知 配置部分结束
2025-08-02 10:31:34 - INFO - 未知 配置部分结束
2025-08-02 10:31:34 - INFO - 未知 配置部分结束
2025-08-02 10:31:34 - INFO - 未知 配置部分结束
2025-08-02 10:31:34 - INFO - 未知 配置部分结束
2025-08-02 10:31:34 - INFO - 未知 配置部分结束
2025-08-02 10:31:34 - INFO - 未知 配置部分结束
2025-08-02 10:31:34 - INFO - 未知 配置部分结束
2025-08-02 10:31:34 - INFO - 未知 配置部分结束
2025-08-02 10:31:34 - INFO - 未知 配置部分结束
2025-08-02 10:31:34 - INFO - 未知 配置部分结束
2025-08-02 10:31:34 - INFO - 未知 配置部分结束
2025-08-02 10:31:34 - INFO - 未知 配置部分结束
2025-08-02 10:31:34 - INFO - 未知 配置部分结束
2025-08-02 10:31:34 - INFO - 未知 配置部分结束
2025-08-02 10:31:34 - INFO - 未知 配置部分结束
2025-08-02 10:31:34 - INFO - 未知 配置部分结束
2025-08-02 10:31:34 - INFO - 未知 配置部分结束
2025-08-02 10:31:35 - INFO - 未知 配置部分结束
2025-08-02 10:31:35 - INFO - 未知 配置部分结束
2025-08-02 10:31:35 - INFO - 未知 配置部分结束
2025-08-02 10:31:35 - INFO - 未知 配置部分结束
2025-08-02 10:31:35 - INFO - 未知 配置部分结束
2025-08-02 10:31:35 - INFO - 未知 配置部分结束
2025-08-02 10:31:35 - INFO - 未知 配置部分结束
2025-08-02 10:31:35 - WARNING - 警告: 行 15183 包含可疑字符，可能存在命令注入风险: set url "^\\/((custom|search|images|videosearch|webhp)\\?)"
2025-08-02 10:31:35 - WARNING - 警告: 行 15186 包含可疑字符，可能存在命令注入风险: set safesearch-str "&safe=active"
2025-08-02 10:31:35 - WARNING - 警告: 行 15190 包含可疑字符，可能存在命令注入风险: set url "^\\/search(\\/video|\\/images)[缺失:0,1](\\?|;)"
2025-08-02 10:31:35 - WARNING - 警告: 行 15193 包含可疑字符，可能存在命令注入风险: set safesearch-str "&vm=r"
2025-08-02 10:31:35 - WARNING - 警告: 行 15197 包含可疑字符，可能存在命令注入风险: set url "^(\\/images|\\/videos)?(\\/search|\\/async|\\/asyncv2)\\?"
2025-08-02 10:31:35 - WARNING - 警告: 行 15203 包含可疑字符，可能存在命令注入风险: set url "^\\/((|yand|images\\/|video\\/)(search)|search\\/)\\?"
2025-08-02 10:31:35 - WARNING - 警告: 行 15206 包含可疑字符，可能存在命令注入风险: set safesearch-str "&family=yes"
2025-08-02 10:31:35 - WARNING - 警告: 行 15219 包含可疑字符，可能存在命令注入风险: set url "^\\/(ns|q|m|i|v)\\?"
2025-08-02 10:31:35 - WARNING - 消息中缺少参数: 4,15
2025-08-02 10:31:36 - WARNING - 警告: 行 15269 包含可疑字符，可能存在命令注入风险: set safesearch-str "regex::(?:\\?|&)u=([^&]+)::\\1"
2025-08-02 10:31:36 - INFO - 未知 配置部分结束
2025-08-02 10:31:36 - INFO - 未知 配置部分结束
2025-08-02 10:31:36 - INFO - 未知 配置部分结束
2025-08-02 10:31:36 - INFO - 未知 配置部分结束
2025-08-02 10:31:36 - INFO - 未知 配置部分结束
2025-08-02 10:31:36 - INFO - 未知 配置部分结束
2025-08-02 10:31:36 - INFO - 未知 配置部分结束
2025-08-02 10:31:36 - INFO - 未知 配置部分结束
2025-08-02 10:31:36 - INFO - 未知 配置部分结束
2025-08-02 10:31:36 - INFO - 未知 配置部分结束
2025-08-02 10:31:36 - INFO - Start Parsing Log Settings
2025-08-02 10:31:36 - INFO - Set Fwpolicy Implicit Log
2025-08-02 10:31:36 - INFO - 开始解析周期性时间对象配置部分
2025-08-02 10:31:36 - INFO - 解析时间对象 always
2025-08-02 10:31:36 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:36 - INFO - 时间对象 always 解析完成
2025-08-02 10:31:36 - INFO - 解析时间对象 none
2025-08-02 10:31:36 - WARNING - 警告: 时间对象 'none' 只有UUID标识符，没有其他配置，将跳过默认值填充
2025-08-02 10:31:36 - WARNING - 警告: 时间对象 'none' 没有时间设置和星期几设置
2025-08-02 10:31:36 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:36 - INFO - 时间对象 none 解析完成
2025-08-02 10:31:36 - INFO - 解析时间对象 default-darrp-optimize
2025-08-02 10:31:36 - WARNING - 消息中缺少参数: "name"
2025-08-02 10:31:36 - INFO - 时间对象 default-darrp-optimize 解析完成
2025-08-02 10:31:36 - INFO - schedule 配置部分结束
2025-08-02 10:31:36 - INFO - Start Parsing Ippool
2025-08-02 10:31:36 - INFO - Parsing Ippool
2025-08-02 10:31:36 - INFO - Set Ippool Startip
2025-08-02 10:31:36 - INFO - Set Ippool Endip
2025-08-02 10:31:36 - INFO - Ippool Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Ippool
2025-08-02 10:31:36 - INFO - Set Ippool Startip
2025-08-02 10:31:36 - INFO - Set Ippool Endip
2025-08-02 10:31:36 - INFO - Ippool Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Ippool
2025-08-02 10:31:36 - INFO - Set Ippool Startip
2025-08-02 10:31:36 - INFO - Set Ippool Endip
2025-08-02 10:31:36 - INFO - Ippool Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Ippool
2025-08-02 10:31:36 - INFO - Set Ippool Startip
2025-08-02 10:31:36 - INFO - Set Ippool Endip
2025-08-02 10:31:36 - INFO - Ippool Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Ippool
2025-08-02 10:31:36 - INFO - Set Ippool Startip
2025-08-02 10:31:36 - INFO - Set Ippool Endip
2025-08-02 10:31:36 - INFO - Ippool Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Ippool
2025-08-02 10:31:36 - INFO - Set Ippool Startip
2025-08-02 10:31:36 - INFO - Set Ippool Endip
2025-08-02 10:31:36 - INFO - Ippool Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Ippool
2025-08-02 10:31:36 - INFO - Set Ippool Startip
2025-08-02 10:31:36 - INFO - Set Ippool Endip
2025-08-02 10:31:36 - INFO - Ippool Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Ippool
2025-08-02 10:31:36 - INFO - Set Ippool Startip
2025-08-02 10:31:36 - INFO - Set Ippool Endip
2025-08-02 10:31:36 - INFO - Ippool Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Ippool
2025-08-02 10:31:36 - INFO - Set Ippool Startip
2025-08-02 10:31:36 - INFO - Set Ippool Endip
2025-08-02 10:31:36 - INFO - Ippool Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Ippool
2025-08-02 10:31:36 - INFO - Set Ippool Startip
2025-08-02 10:31:36 - INFO - Set Ippool Endip
2025-08-02 10:31:36 - INFO - Ippool Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Ippool
2025-08-02 10:31:36 - INFO - Set Ippool Startip
2025-08-02 10:31:36 - INFO - Set Ippool Endip
2025-08-02 10:31:36 - INFO - Ippool Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Ippool
2025-08-02 10:31:36 - INFO - Set Ippool Startip
2025-08-02 10:31:36 - INFO - Set Ippool Endip
2025-08-02 10:31:36 - INFO - Ippool Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Ippool
2025-08-02 10:31:36 - INFO - Set Ippool Startip
2025-08-02 10:31:36 - INFO - Set Ippool Endip
2025-08-02 10:31:36 - INFO - Ippool Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Ippool
2025-08-02 10:31:36 - INFO - Set Ippool Startip
2025-08-02 10:31:36 - INFO - Set Ippool Endip
2025-08-02 10:31:36 - INFO - Ippool Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Ippool
2025-08-02 10:31:36 - INFO - Set Ippool Startip
2025-08-02 10:31:36 - INFO - Set Ippool Endip
2025-08-02 10:31:36 - INFO - Ippool Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Ippool
2025-08-02 10:31:36 - INFO - Set Ippool Startip
2025-08-02 10:31:36 - INFO - Set Ippool Endip
2025-08-02 10:31:36 - INFO - Ippool Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Ippool
2025-08-02 10:31:36 - INFO - Set Ippool Startip
2025-08-02 10:31:36 - INFO - Set Ippool Endip
2025-08-02 10:31:36 - INFO - Ippool Parsing Complete
2025-08-02 10:31:36 - INFO - Start Parsing Vip
2025-08-02 10:31:36 - INFO - Parsing Vip
2025-08-02 10:31:36 - INFO - Set Vip Extip
2025-08-02 10:31:36 - INFO - Set Vip Mappedip
2025-08-02 10:31:36 - INFO - Set Vip Extintf
2025-08-02 10:31:36 - INFO - Set Vip Portforward
2025-08-02 10:31:36 - INFO - Set Vip Extport
2025-08-02 10:31:36 - INFO - Set Vip Mappedport
2025-08-02 10:31:36 - INFO - Vip Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Vip
2025-08-02 10:31:36 - INFO - Set Vip Extip
2025-08-02 10:31:36 - INFO - Set Vip Mappedip
2025-08-02 10:31:36 - INFO - Set Vip Extintf
2025-08-02 10:31:36 - INFO - Set Vip Portforward
2025-08-02 10:31:36 - INFO - Set Vip Extport
2025-08-02 10:31:36 - INFO - Set Vip Mappedport
2025-08-02 10:31:36 - INFO - Vip Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Vip
2025-08-02 10:31:36 - INFO - Set Vip Extip
2025-08-02 10:31:36 - INFO - Set Vip Mappedip
2025-08-02 10:31:36 - INFO - Set Vip Extintf
2025-08-02 10:31:36 - INFO - Set Vip Portforward
2025-08-02 10:31:36 - INFO - Set Vip Extport
2025-08-02 10:31:36 - INFO - Set Vip Mappedport
2025-08-02 10:31:36 - INFO - Vip Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Vip
2025-08-02 10:31:36 - INFO - Set Vip Extip
2025-08-02 10:31:36 - INFO - Set Vip Mappedip
2025-08-02 10:31:36 - INFO - Set Vip Extintf
2025-08-02 10:31:36 - INFO - Set Vip Portforward
2025-08-02 10:31:36 - INFO - Set Vip Extport
2025-08-02 10:31:36 - INFO - Set Vip Mappedport
2025-08-02 10:31:36 - INFO - Vip Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Vip
2025-08-02 10:31:36 - INFO - Set Vip Extip
2025-08-02 10:31:36 - INFO - Set Vip Mappedip
2025-08-02 10:31:36 - INFO - Set Vip Extintf
2025-08-02 10:31:36 - INFO - Set Vip Portforward
2025-08-02 10:31:36 - INFO - Set Vip Extport
2025-08-02 10:31:36 - INFO - Set Vip Mappedport
2025-08-02 10:31:36 - INFO - Vip Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Vip
2025-08-02 10:31:36 - INFO - Set Vip Extip
2025-08-02 10:31:36 - INFO - Set Vip Mappedip
2025-08-02 10:31:36 - INFO - Set Vip Extintf
2025-08-02 10:31:36 - INFO - Set Vip Portforward
2025-08-02 10:31:36 - INFO - Set Vip Extport
2025-08-02 10:31:36 - INFO - Set Vip Mappedport
2025-08-02 10:31:36 - INFO - Vip Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Vip
2025-08-02 10:31:36 - INFO - Set Vip Extip
2025-08-02 10:31:36 - INFO - Set Vip Mappedip
2025-08-02 10:31:36 - INFO - Set Vip Extintf
2025-08-02 10:31:36 - INFO - Set Vip Portforward
2025-08-02 10:31:36 - INFO - Set Vip Extport
2025-08-02 10:31:36 - INFO - Set Vip Mappedport
2025-08-02 10:31:36 - INFO - Vip Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Vip
2025-08-02 10:31:36 - INFO - Set Vip Extip
2025-08-02 10:31:36 - INFO - Set Vip Mappedip
2025-08-02 10:31:36 - INFO - Set Vip Extintf
2025-08-02 10:31:36 - INFO - Set Vip Portforward
2025-08-02 10:31:36 - INFO - Set Vip Extport
2025-08-02 10:31:36 - INFO - Set Vip Mappedport
2025-08-02 10:31:36 - INFO - Vip Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Vip
2025-08-02 10:31:36 - INFO - Set Vip Extip
2025-08-02 10:31:36 - INFO - Set Vip Mappedip
2025-08-02 10:31:36 - INFO - Set Vip Extintf
2025-08-02 10:31:36 - INFO - Set Vip Portforward
2025-08-02 10:31:36 - INFO - Set Vip Extport
2025-08-02 10:31:36 - INFO - Set Vip Mappedport
2025-08-02 10:31:36 - INFO - Vip Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Vip
2025-08-02 10:31:36 - INFO - Set Vip Extip
2025-08-02 10:31:36 - INFO - Set Vip Mappedip
2025-08-02 10:31:36 - INFO - Set Vip Extintf
2025-08-02 10:31:36 - INFO - Set Vip Portforward
2025-08-02 10:31:36 - INFO - Set Vip Extport
2025-08-02 10:31:36 - INFO - Set Vip Mappedport
2025-08-02 10:31:36 - INFO - Vip Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Vip
2025-08-02 10:31:36 - INFO - Set Vip Extip
2025-08-02 10:31:36 - INFO - Set Vip Mappedip
2025-08-02 10:31:36 - INFO - Set Vip Extintf
2025-08-02 10:31:36 - INFO - Set Vip Portforward
2025-08-02 10:31:36 - INFO - Set Vip Extport
2025-08-02 10:31:36 - INFO - Set Vip Mappedport
2025-08-02 10:31:36 - INFO - Vip Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Vip
2025-08-02 10:31:36 - INFO - Set Vip Extip
2025-08-02 10:31:36 - INFO - Set Vip Mappedip
2025-08-02 10:31:36 - INFO - Set Vip Extintf
2025-08-02 10:31:36 - INFO - Set Vip Portforward
2025-08-02 10:31:36 - INFO - Set Vip Extport
2025-08-02 10:31:36 - INFO - Set Vip Mappedport
2025-08-02 10:31:36 - INFO - Vip Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Vip
2025-08-02 10:31:36 - INFO - Set Vip Extip
2025-08-02 10:31:36 - INFO - Set Vip Mappedip
2025-08-02 10:31:36 - INFO - Set Vip Extintf
2025-08-02 10:31:36 - INFO - Set Vip Portforward
2025-08-02 10:31:36 - INFO - Set Vip Extport
2025-08-02 10:31:36 - INFO - Set Vip Mappedport
2025-08-02 10:31:36 - INFO - Vip Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Vip
2025-08-02 10:31:36 - INFO - Set Vip Extip
2025-08-02 10:31:36 - INFO - Set Vip Mappedip
2025-08-02 10:31:36 - INFO - Set Vip Extintf
2025-08-02 10:31:36 - INFO - Set Vip Portforward
2025-08-02 10:31:36 - INFO - Set Vip Protocol
2025-08-02 10:31:36 - INFO - Set Vip Extport
2025-08-02 10:31:36 - INFO - Set Vip Mappedport
2025-08-02 10:31:36 - INFO - Vip Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Vip
2025-08-02 10:31:36 - INFO - Set Vip Extip
2025-08-02 10:31:36 - INFO - Set Vip Mappedip
2025-08-02 10:31:36 - INFO - Set Vip Extintf
2025-08-02 10:31:36 - INFO - Set Vip Portforward
2025-08-02 10:31:36 - INFO - Set Vip Protocol
2025-08-02 10:31:36 - INFO - Set Vip Extport
2025-08-02 10:31:36 - INFO - Set Vip Mappedport
2025-08-02 10:31:36 - INFO - Vip Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Vip
2025-08-02 10:31:36 - INFO - Set Vip Extip
2025-08-02 10:31:36 - INFO - Set Vip Mappedip
2025-08-02 10:31:36 - INFO - Set Vip Extintf
2025-08-02 10:31:36 - INFO - Set Vip Portforward
2025-08-02 10:31:36 - INFO - Set Vip Extport
2025-08-02 10:31:36 - INFO - Set Vip Mappedport
2025-08-02 10:31:36 - INFO - Vip Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Vip
2025-08-02 10:31:36 - INFO - Set Vip Extip
2025-08-02 10:31:36 - INFO - Set Vip Mappedip
2025-08-02 10:31:36 - INFO - Set Vip Extintf
2025-08-02 10:31:36 - INFO - Set Vip Portforward
2025-08-02 10:31:36 - INFO - Set Vip Extport
2025-08-02 10:31:36 - INFO - Set Vip Mappedport
2025-08-02 10:31:36 - INFO - Vip Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Vip
2025-08-02 10:31:36 - INFO - Set Vip Extip
2025-08-02 10:31:36 - INFO - Set Vip Mappedip
2025-08-02 10:31:36 - INFO - Set Vip Extintf
2025-08-02 10:31:36 - INFO - Set Vip Portforward
2025-08-02 10:31:36 - INFO - Set Vip Protocol
2025-08-02 10:31:36 - INFO - Set Vip Extport
2025-08-02 10:31:36 - INFO - Set Vip Mappedport
2025-08-02 10:31:36 - INFO - Vip Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Vip
2025-08-02 10:31:36 - INFO - Set Vip Extip
2025-08-02 10:31:36 - INFO - Set Vip Mappedip
2025-08-02 10:31:36 - INFO - Set Vip Extintf
2025-08-02 10:31:36 - INFO - Set Vip Portforward
2025-08-02 10:31:36 - INFO - Set Vip Protocol
2025-08-02 10:31:36 - INFO - Set Vip Extport
2025-08-02 10:31:36 - INFO - Set Vip Mappedport
2025-08-02 10:31:36 - INFO - Vip Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Vip
2025-08-02 10:31:36 - INFO - Set Vip Extip
2025-08-02 10:31:36 - INFO - Set Vip Mappedip
2025-08-02 10:31:36 - INFO - Set Vip Extintf
2025-08-02 10:31:36 - INFO - Set Vip Portforward
2025-08-02 10:31:36 - INFO - Set Vip Extport
2025-08-02 10:31:36 - INFO - Set Vip Mappedport
2025-08-02 10:31:36 - INFO - Vip Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Vip
2025-08-02 10:31:36 - INFO - Set Vip Extip
2025-08-02 10:31:36 - INFO - Set Vip Mappedip
2025-08-02 10:31:36 - INFO - Set Vip Extintf
2025-08-02 10:31:36 - INFO - Set Vip Portforward
2025-08-02 10:31:36 - INFO - Set Vip Extport
2025-08-02 10:31:36 - INFO - Set Vip Mappedport
2025-08-02 10:31:36 - INFO - Vip Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Vip
2025-08-02 10:31:36 - INFO - Set Vip Extip
2025-08-02 10:31:36 - INFO - Set Vip Mappedip
2025-08-02 10:31:36 - INFO - Set Vip Extintf
2025-08-02 10:31:36 - INFO - Set Vip Portforward
2025-08-02 10:31:36 - INFO - Set Vip Extport
2025-08-02 10:31:36 - INFO - Set Vip Mappedport
2025-08-02 10:31:36 - INFO - Vip Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Vip
2025-08-02 10:31:36 - INFO - Set Vip Extip
2025-08-02 10:31:36 - INFO - Set Vip Mappedip
2025-08-02 10:31:36 - INFO - Set Vip Extintf
2025-08-02 10:31:36 - INFO - Vip Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Vip
2025-08-02 10:31:36 - INFO - Set Vip Extip
2025-08-02 10:31:36 - INFO - Set Vip Mappedip
2025-08-02 10:31:36 - INFO - Set Vip Extintf
2025-08-02 10:31:36 - INFO - Vip Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Vip
2025-08-02 10:31:36 - INFO - Set Vip Extip
2025-08-02 10:31:36 - INFO - Set Vip Mappedip
2025-08-02 10:31:36 - INFO - Set Vip Extintf
2025-08-02 10:31:36 - INFO - Vip Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Vip
2025-08-02 10:31:36 - INFO - Set Vip Extip
2025-08-02 10:31:36 - INFO - Set Vip Mappedip
2025-08-02 10:31:36 - INFO - Set Vip Extintf
2025-08-02 10:31:36 - INFO - Vip Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Vip
2025-08-02 10:31:36 - INFO - Set Vip Extip
2025-08-02 10:31:36 - INFO - Set Vip Mappedip
2025-08-02 10:31:36 - INFO - Set Vip Extintf
2025-08-02 10:31:36 - INFO - Vip Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Vip
2025-08-02 10:31:36 - INFO - Set Vip Extip
2025-08-02 10:31:36 - INFO - Set Vip Mappedip
2025-08-02 10:31:36 - INFO - Set Vip Extintf
2025-08-02 10:31:36 - INFO - Vip Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Vip
2025-08-02 10:31:36 - INFO - Set Vip Extip
2025-08-02 10:31:36 - INFO - Set Vip Mappedip
2025-08-02 10:31:36 - INFO - Set Vip Extintf
2025-08-02 10:31:36 - INFO - Vip Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Vip
2025-08-02 10:31:36 - INFO - Set Vip Extip
2025-08-02 10:31:36 - INFO - Set Vip Mappedip
2025-08-02 10:31:36 - INFO - Set Vip Extintf
2025-08-02 10:31:36 - INFO - Vip Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Vip
2025-08-02 10:31:36 - INFO - Set Vip Extip
2025-08-02 10:31:36 - INFO - Set Vip Mappedip
2025-08-02 10:31:36 - INFO - Set Vip Extintf
2025-08-02 10:31:36 - INFO - Set Vip Portforward
2025-08-02 10:31:36 - INFO - Set Vip Extport
2025-08-02 10:31:36 - INFO - Set Vip Mappedport
2025-08-02 10:31:36 - INFO - Vip Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Vip
2025-08-02 10:31:36 - INFO - Set Vip Extip
2025-08-02 10:31:36 - INFO - Set Vip Mappedip
2025-08-02 10:31:36 - INFO - Set Vip Extintf
2025-08-02 10:31:36 - INFO - Vip Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Vip
2025-08-02 10:31:36 - INFO - Set Vip Extip
2025-08-02 10:31:36 - INFO - Set Vip Mappedip
2025-08-02 10:31:36 - INFO - Set Vip Extintf
2025-08-02 10:31:36 - INFO - Vip Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Vip
2025-08-02 10:31:36 - INFO - Set Vip Extip
2025-08-02 10:31:36 - INFO - Set Vip Mappedip
2025-08-02 10:31:36 - INFO - Set Vip Extintf
2025-08-02 10:31:36 - INFO - Vip Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Vip
2025-08-02 10:31:36 - INFO - Set Vip Extip
2025-08-02 10:31:36 - INFO - Set Vip Mappedip
2025-08-02 10:31:36 - INFO - Set Vip Extintf
2025-08-02 10:31:36 - INFO - Vip Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Vip
2025-08-02 10:31:36 - INFO - Set Vip Extip
2025-08-02 10:31:36 - INFO - Set Vip Mappedip
2025-08-02 10:31:36 - INFO - Set Vip Extintf
2025-08-02 10:31:36 - INFO - Vip Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Vip
2025-08-02 10:31:36 - INFO - Set Vip Extip
2025-08-02 10:31:36 - INFO - Set Vip Mappedip
2025-08-02 10:31:36 - INFO - Set Vip Extintf
2025-08-02 10:31:36 - INFO - Vip Parsing Complete
2025-08-02 10:31:36 - INFO - Parsing Vip
2025-08-02 10:31:36 - INFO - Set Vip Extip
2025-08-02 10:31:36 - INFO - Set Vip Mappedip
2025-08-02 10:31:36 - INFO - Set Vip Extintf
2025-08-02 10:31:36 - INFO - Vip Parsing Complete
2025-08-02 10:31:37 - INFO - Parsing Vip
2025-08-02 10:31:37 - INFO - Set Vip Extip
2025-08-02 10:31:37 - INFO - Set Vip Mappedip
2025-08-02 10:31:37 - INFO - Set Vip Extintf
2025-08-02 10:31:37 - INFO - Vip Parsing Complete
2025-08-02 10:31:37 - INFO - Parsing Vip
2025-08-02 10:31:37 - INFO - Set Vip Extip
2025-08-02 10:31:37 - INFO - Set Vip Mappedip
2025-08-02 10:31:37 - INFO - Set Vip Extintf
2025-08-02 10:31:37 - INFO - Vip Parsing Complete
2025-08-02 10:31:37 - INFO - Parsing Vip
2025-08-02 10:31:37 - INFO - Set Vip Extip
2025-08-02 10:31:37 - INFO - Set Vip Mappedip
2025-08-02 10:31:37 - INFO - Set Vip Extintf
2025-08-02 10:31:37 - INFO - Vip Parsing Complete
2025-08-02 10:31:37 - INFO - Parsing Vip
2025-08-02 10:31:37 - INFO - Set Vip Extip
2025-08-02 10:31:37 - INFO - Set Vip Mappedip
2025-08-02 10:31:37 - INFO - Set Vip Extintf
2025-08-02 10:31:37 - INFO - Vip Parsing Complete
2025-08-02 10:31:37 - INFO - Parsing Vip
2025-08-02 10:31:37 - INFO - Set Vip Extip
2025-08-02 10:31:37 - INFO - Set Vip Mappedip
2025-08-02 10:31:37 - INFO - Set Vip Extintf
2025-08-02 10:31:37 - INFO - Vip Parsing Complete
2025-08-02 10:31:37 - INFO - Parsing Vip
2025-08-02 10:31:37 - INFO - Set Vip Extip
2025-08-02 10:31:37 - INFO - Set Vip Mappedip
2025-08-02 10:31:37 - INFO - Set Vip Extintf
2025-08-02 10:31:37 - INFO - Vip Parsing Complete
2025-08-02 10:31:37 - INFO - Parsing Vip
2025-08-02 10:31:37 - INFO - Set Vip Extip
2025-08-02 10:31:37 - INFO - Set Vip Mappedip
2025-08-02 10:31:37 - INFO - Set Vip Extintf
2025-08-02 10:31:37 - INFO - Vip Parsing Complete
2025-08-02 10:31:37 - INFO - Parsing Vip
2025-08-02 10:31:37 - INFO - Set Vip Extip
2025-08-02 10:31:37 - INFO - Set Vip Mappedip
2025-08-02 10:31:37 - INFO - Set Vip Extintf
2025-08-02 10:31:37 - INFO - Set Vip Portforward
2025-08-02 10:31:37 - INFO - Set Vip Extport
2025-08-02 10:31:37 - INFO - Set Vip Mappedport
2025-08-02 10:31:37 - INFO - Vip Parsing Complete
2025-08-02 10:31:37 - INFO - Parsing Vip
2025-08-02 10:31:37 - INFO - Set Vip Extip
2025-08-02 10:31:37 - INFO - Set Vip Mappedip
2025-08-02 10:31:37 - INFO - Set Vip Extintf
2025-08-02 10:31:37 - INFO - Vip Parsing Complete
2025-08-02 10:31:37 - INFO - Parsing Vip
2025-08-02 10:31:37 - INFO - Set Vip Extip
2025-08-02 10:31:37 - INFO - Set Vip Mappedip
2025-08-02 10:31:37 - INFO - Set Vip Extintf
2025-08-02 10:31:37 - INFO - Set Vip Portforward
2025-08-02 10:31:37 - INFO - Set Vip Extport
2025-08-02 10:31:37 - INFO - Set Vip Mappedport
2025-08-02 10:31:37 - INFO - Vip Parsing Complete
2025-08-02 10:31:37 - INFO - Parsing Vip
2025-08-02 10:31:37 - INFO - Set Vip Extip
2025-08-02 10:31:37 - INFO - Set Vip Mappedip
2025-08-02 10:31:37 - INFO - Set Vip Extintf
2025-08-02 10:31:37 - INFO - Set Vip Portforward
2025-08-02 10:31:37 - INFO - Set Vip Extport
2025-08-02 10:31:37 - INFO - Set Vip Mappedport
2025-08-02 10:31:37 - INFO - Vip Parsing Complete
2025-08-02 10:31:37 - INFO - Parsing Vip
2025-08-02 10:31:37 - INFO - Set Vip Extip
2025-08-02 10:31:37 - INFO - Set Vip Mappedip
2025-08-02 10:31:37 - INFO - Set Vip Extintf
2025-08-02 10:31:37 - INFO - Set Vip Portforward
2025-08-02 10:31:37 - INFO - Set Vip Extport
2025-08-02 10:31:37 - INFO - Set Vip Mappedport
2025-08-02 10:31:37 - INFO - Vip Parsing Complete
2025-08-02 10:31:37 - INFO - Parsing Vip
2025-08-02 10:31:37 - INFO - Set Vip Extip
2025-08-02 10:31:37 - INFO - Set Vip Mappedip
2025-08-02 10:31:37 - INFO - Set Vip Extintf
2025-08-02 10:31:37 - INFO - Vip Parsing Complete
2025-08-02 10:31:37 - INFO - Parsing Vip
2025-08-02 10:31:37 - INFO - Set Vip Extip
2025-08-02 10:31:37 - INFO - Set Vip Mappedip
2025-08-02 10:31:37 - INFO - Set Vip Extintf
2025-08-02 10:31:37 - INFO - Vip Parsing Complete
2025-08-02 10:31:37 - INFO - Parsing Vip
2025-08-02 10:31:37 - INFO - Set Vip Extip
2025-08-02 10:31:37 - INFO - Set Vip Mappedip
2025-08-02 10:31:37 - INFO - Set Vip Extintf
2025-08-02 10:31:37 - INFO - Vip Parsing Complete
2025-08-02 10:31:37 - INFO - Parsing Vip
2025-08-02 10:31:37 - INFO - Set Vip Extip
2025-08-02 10:31:37 - INFO - Set Vip Mappedip
2025-08-02 10:31:37 - INFO - Set Vip Extintf
2025-08-02 10:31:37 - INFO - Set Vip Portforward
2025-08-02 10:31:37 - INFO - Set Vip Extport
2025-08-02 10:31:37 - INFO - Set Vip Mappedport
2025-08-02 10:31:37 - INFO - Vip Parsing Complete
2025-08-02 10:31:37 - INFO - Parsing Vip
2025-08-02 10:31:37 - INFO - Set Vip Extip
2025-08-02 10:31:37 - INFO - Set Vip Mappedip
2025-08-02 10:31:37 - INFO - Set Vip Extintf
2025-08-02 10:31:37 - INFO - Vip Parsing Complete
2025-08-02 10:31:37 - INFO - Parsing Vip
2025-08-02 10:31:37 - INFO - Set Vip Extip
2025-08-02 10:31:37 - INFO - Set Vip Mappedip
2025-08-02 10:31:37 - INFO - Set Vip Extintf
2025-08-02 10:31:37 - INFO - Vip Parsing Complete
2025-08-02 10:31:37 - INFO - Parsing Vip
2025-08-02 10:31:37 - INFO - Set Vip Extip
2025-08-02 10:31:37 - INFO - Set Vip Mappedip
2025-08-02 10:31:37 - INFO - Set Vip Extintf
2025-08-02 10:31:37 - INFO - Set Vip Portforward
2025-08-02 10:31:37 - INFO - Set Vip Protocol
2025-08-02 10:31:37 - INFO - Set Vip Extport
2025-08-02 10:31:37 - INFO - Set Vip Mappedport
2025-08-02 10:31:37 - INFO - Vip Parsing Complete
2025-08-02 10:31:37 - INFO - Parsing Vip
2025-08-02 10:31:37 - INFO - Set Vip Extip
2025-08-02 10:31:37 - INFO - Set Vip Mappedip
2025-08-02 10:31:37 - INFO - Set Vip Extintf
2025-08-02 10:31:37 - INFO - Vip Parsing Complete
2025-08-02 10:31:37 - INFO - Parsing Vip
2025-08-02 10:31:37 - INFO - Set Vip Extip
2025-08-02 10:31:37 - INFO - Set Vip Mappedip
2025-08-02 10:31:37 - INFO - Set Vip Extintf
2025-08-02 10:31:37 - INFO - Vip Parsing Complete
2025-08-02 10:31:37 - INFO - Parsing Vip
2025-08-02 10:31:37 - INFO - Set Vip Extip
2025-08-02 10:31:37 - INFO - Set Vip Mappedip
2025-08-02 10:31:37 - INFO - Set Vip Extintf
2025-08-02 10:31:37 - INFO - Vip Parsing Complete
2025-08-02 10:31:37 - INFO - Parsing Vip
2025-08-02 10:31:37 - INFO - Set Vip Extip
2025-08-02 10:31:37 - INFO - Set Vip Mappedip
2025-08-02 10:31:37 - INFO - Set Vip Extintf
2025-08-02 10:31:37 - INFO - Vip Parsing Complete
2025-08-02 10:31:37 - INFO - Parsing Vip
2025-08-02 10:31:37 - INFO - Set Vip Extip
2025-08-02 10:31:37 - INFO - Set Vip Mappedip
2025-08-02 10:31:37 - INFO - Set Vip Extintf
2025-08-02 10:31:37 - INFO - Vip Parsing Complete
2025-08-02 10:31:37 - INFO - Parsing Vip
2025-08-02 10:31:37 - INFO - Set Vip Extip
2025-08-02 10:31:37 - INFO - Set Vip Mappedip
2025-08-02 10:31:37 - INFO - Set Vip Extintf
2025-08-02 10:31:37 - INFO - Vip Parsing Complete
2025-08-02 10:31:37 - INFO - Parsing Vip
2025-08-02 10:31:37 - INFO - Set Vip Extip
2025-08-02 10:31:37 - INFO - Set Vip Mappedip
2025-08-02 10:31:37 - INFO - Set Vip Extintf
2025-08-02 10:31:37 - INFO - Vip Parsing Complete
2025-08-02 10:31:37 - INFO - Parsing Vip
2025-08-02 10:31:37 - INFO - Set Vip Extip
2025-08-02 10:31:37 - INFO - Set Vip Mappedip
2025-08-02 10:31:37 - INFO - Set Vip Extintf
2025-08-02 10:31:37 - INFO - Vip Parsing Complete
2025-08-02 10:31:37 - INFO - Parsing Vip
2025-08-02 10:31:37 - INFO - Set Vip Extip
2025-08-02 10:31:37 - INFO - Set Vip Mappedip
2025-08-02 10:31:37 - INFO - Set Vip Extintf
2025-08-02 10:31:37 - INFO - Vip Parsing Complete
2025-08-02 10:31:37 - INFO - Parsing Vip
2025-08-02 10:31:37 - INFO - Set Vip Extip
2025-08-02 10:31:37 - INFO - Set Vip Mappedip
2025-08-02 10:31:37 - INFO - Set Vip Extintf
2025-08-02 10:31:37 - INFO - Vip Parsing Complete
2025-08-02 10:31:37 - INFO - Parsing Vip
2025-08-02 10:31:37 - INFO - Set Vip Extip
2025-08-02 10:31:37 - INFO - Set Vip Mappedip
2025-08-02 10:31:37 - INFO - Set Vip Extintf
2025-08-02 10:31:37 - INFO - Vip Parsing Complete
2025-08-02 10:31:37 - INFO - Parsing Vip
2025-08-02 10:31:37 - INFO - Set Vip Extip
2025-08-02 10:31:37 - INFO - Set Vip Mappedip
2025-08-02 10:31:37 - INFO - Set Vip Extintf
2025-08-02 10:31:37 - INFO - Vip Parsing Complete
2025-08-02 10:31:37 - INFO - Parsing Vip
2025-08-02 10:31:37 - INFO - Set Vip Extip
2025-08-02 10:31:37 - INFO - Set Vip Mappedip
2025-08-02 10:31:37 - INFO - Set Vip Extintf
2025-08-02 10:31:37 - INFO - Vip Parsing Complete
2025-08-02 10:31:37 - INFO - Parsing Vip
2025-08-02 10:31:37 - INFO - Set Vip Extip
2025-08-02 10:31:37 - INFO - Set Vip Mappedip
2025-08-02 10:31:37 - INFO - Set Vip Extintf
2025-08-02 10:31:37 - INFO - Vip Parsing Complete
2025-08-02 10:31:37 - INFO - Parsing Vip
2025-08-02 10:31:37 - INFO - Set Vip Extip
2025-08-02 10:31:37 - INFO - Set Vip Mappedip
2025-08-02 10:31:37 - INFO - Set Vip Extintf
2025-08-02 10:31:37 - INFO - Set Vip Portforward
2025-08-02 10:31:37 - INFO - Set Vip Extport
2025-08-02 10:31:37 - INFO - Set Vip Mappedport
2025-08-02 10:31:37 - INFO - Vip Parsing Complete
2025-08-02 10:31:37 - INFO - Parsing Vip
2025-08-02 10:31:37 - INFO - Set Vip Extip
2025-08-02 10:31:37 - INFO - Set Vip Mappedip
2025-08-02 10:31:37 - INFO - Set Vip Extintf
2025-08-02 10:31:37 - INFO - Vip Parsing Complete
2025-08-02 10:31:37 - INFO - Start Parsing Vip
2025-08-02 10:31:37 - INFO - Parsing Vip
2025-08-02 10:31:37 - WARNING - 警告: 行 15932 过长 (1055 字符)，已截断
2025-08-02 10:31:37 - INFO - Vip Parsing Complete
2025-08-02 10:31:37 - INFO - Parsing Vip
2025-08-02 10:31:37 - INFO - Vip Parsing Complete
2025-08-02 10:31:37 - INFO - 未知 配置部分结束
2025-08-02 10:31:37 - INFO - 未知 配置部分结束
2025-08-02 10:31:37 - INFO - 未知 配置部分结束
2025-08-02 10:31:37 - INFO - 未知 配置部分结束
2025-08-02 10:31:37 - INFO - 未知 配置部分结束
2025-08-02 10:31:37 - INFO - 未知 配置部分结束
2025-08-02 10:31:37 - INFO - 未知 配置部分结束
2025-08-02 10:31:37 - INFO - 未知 配置部分结束
2025-08-02 10:31:37 - INFO - 未知 配置部分结束
2025-08-02 10:31:37 - INFO - 未知 配置部分结束
2025-08-02 10:31:37 - INFO - 未知 配置部分结束
2025-08-02 10:31:37 - INFO - 未知 配置部分结束
2025-08-02 10:31:37 - INFO - 未知 配置部分结束
2025-08-02 10:31:37 - INFO - 未知 配置部分结束
2025-08-02 10:31:37 - INFO - 未知 配置部分结束
2025-08-02 10:31:37 - INFO - 未知 配置部分结束
2025-08-02 10:31:37 - INFO - 未知 配置部分结束
2025-08-02 10:31:37 - INFO - 未知 配置部分结束
2025-08-02 10:31:37 - INFO - 未知 配置部分结束
2025-08-02 10:31:37 - INFO - 未知 配置部分结束
2025-08-02 10:31:37 - INFO - 未知 配置部分结束
2025-08-02 10:31:37 - INFO - 未知 配置部分结束
2025-08-02 10:31:37 - INFO - 未知 配置部分结束
2025-08-02 10:31:37 - INFO - 未知 配置部分结束
2025-08-02 10:31:37 - INFO - 未知 配置部分结束
2025-08-02 10:31:37 - INFO - 未知 配置部分结束
2025-08-02 10:31:37 - INFO - 未知 配置部分结束
2025-08-02 10:31:37 - INFO - 未知 配置部分结束
2025-08-02 10:31:37 - INFO - 未知 配置部分结束
2025-08-02 10:31:37 - INFO - 未知 配置部分结束
2025-08-02 10:31:38 - INFO - 未知 配置部分结束
2025-08-02 10:31:38 - INFO - 未知 配置部分结束
2025-08-02 10:31:38 - INFO - 未知 配置部分结束
2025-08-02 10:31:38 - INFO - 未知 配置部分结束
2025-08-02 10:31:38 - INFO - 未知 配置部分结束
2025-08-02 10:31:38 - INFO - 未知 配置部分结束
2025-08-02 10:31:38 - INFO - 未知 配置部分结束
2025-08-02 10:31:38 - INFO - 未知 配置部分结束
2025-08-02 10:31:38 - INFO - 未知 配置部分结束
2025-08-02 10:31:38 - INFO - 未知 配置部分结束
2025-08-02 10:31:38 - INFO - 未知 配置部分结束
2025-08-02 10:31:38 - INFO - 未知 配置部分结束
2025-08-02 10:31:38 - INFO - 未知 配置部分结束
2025-08-02 10:31:38 - INFO - 未知 配置部分结束
2025-08-02 10:31:38 - INFO - 未知 配置部分结束
2025-08-02 10:31:38 - INFO - 未知 配置部分结束
2025-08-02 10:31:38 - INFO - 未知 配置部分结束
2025-08-02 10:31:38 - INFO - 未知 配置部分结束
2025-08-02 10:31:38 - INFO - 未知 配置部分结束
2025-08-02 10:31:38 - INFO - 未知 配置部分结束
2025-08-02 10:31:38 - INFO - 未知 配置部分结束
2025-08-02 10:31:38 - INFO - 未知 配置部分结束
2025-08-02 10:31:38 - INFO - 未知 配置部分结束
2025-08-02 10:31:38 - INFO - 未知 配置部分结束
2025-08-02 10:31:38 - INFO - 未知 配置部分结束
2025-08-02 10:31:38 - INFO - 未知 配置部分结束
2025-08-02 10:31:38 - INFO - 未知 配置部分结束
2025-08-02 10:31:38 - INFO - 未知 配置部分结束
2025-08-02 10:31:38 - INFO - 未知 配置部分结束
2025-08-02 10:31:38 - INFO - 未知 配置部分结束
2025-08-02 10:31:38 - INFO - 未知 配置部分结束
2025-08-02 10:31:38 - INFO - 未知 配置部分结束
2025-08-02 10:31:38 - INFO - 未知 配置部分结束
2025-08-02 10:31:38 - INFO - 未知 配置部分结束
2025-08-02 10:31:38 - INFO - 未知 配置部分结束
2025-08-02 10:31:38 - INFO - 未知 配置部分结束
2025-08-02 10:31:38 - INFO - 未知 配置部分结束
2025-08-02 10:31:38 - INFO - 未知 配置部分结束
2025-08-02 10:31:38 - INFO - 未知 配置部分结束
2025-08-02 10:31:38 - INFO - 未知 配置部分结束
2025-08-02 10:31:38 - INFO - 未知 配置部分结束
2025-08-02 10:31:38 - INFO - 未知 配置部分结束
2025-08-02 10:31:38 - INFO - 未知 配置部分结束
2025-08-02 10:31:38 - INFO - 未知 配置部分结束
2025-08-02 10:31:38 - INFO - 未知 配置部分结束
2025-08-02 10:31:38 - INFO - 未知 配置部分结束
2025-08-02 10:31:38 - INFO - 未知 配置部分结束
2025-08-02 10:31:38 - INFO - 未知 配置部分结束
2025-08-02 10:31:38 - INFO - 未知 配置部分结束
2025-08-02 10:31:38 - INFO - Start Parsing Policy
2025-08-02 10:31:38 - INFO - Parsing Policy
2025-08-02 10:31:38 - INFO - fortigate.set_policy_name
2025-08-02 10:31:38 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:38 - INFO - Set Policy Srcintf
2025-08-02 10:31:38 - INFO - Set Policy Dstintf
2025-08-02 10:31:38 - INFO - Set Policy Action
2025-08-02 10:31:38 - INFO - Set Policy Srcaddr
2025-08-02 10:31:38 - INFO - Set Policy Dstaddr
2025-08-02 10:31:38 - INFO - Set Policy Schedule
2025-08-02 10:31:38 - INFO - Set Policy Service
2025-08-02 10:31:38 - INFO - Set Policy Logtraffic
2025-08-02 10:31:38 - INFO - Set Policy Nat
2025-08-02 10:31:38 - INFO - Policy Parsing Complete
2025-08-02 10:31:38 - INFO - Parsing Policy
2025-08-02 10:31:38 - INFO - fortigate.set_policy_name
2025-08-02 10:31:38 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:38 - INFO - Set Policy Srcintf
2025-08-02 10:31:38 - INFO - Set Policy Dstintf
2025-08-02 10:31:38 - INFO - Set Policy Action
2025-08-02 10:31:38 - INFO - Set Policy Srcaddr
2025-08-02 10:31:38 - INFO - Set Policy Dstaddr
2025-08-02 10:31:38 - INFO - Set Policy Schedule
2025-08-02 10:31:38 - INFO - Set Policy Service
2025-08-02 10:31:38 - INFO - Set Policy Logtraffic
2025-08-02 10:31:38 - INFO - Policy Parsing Complete
2025-08-02 10:31:38 - INFO - Parsing Policy
2025-08-02 10:31:38 - INFO - fortigate.set_policy_name
2025-08-02 10:31:38 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:38 - INFO - Set Policy Srcintf
2025-08-02 10:31:38 - INFO - Set Policy Dstintf
2025-08-02 10:31:38 - INFO - Set Policy Action
2025-08-02 10:31:38 - INFO - Set Policy Srcaddr
2025-08-02 10:31:38 - INFO - Set Policy Dstaddr
2025-08-02 10:31:38 - INFO - Set Policy Schedule
2025-08-02 10:31:38 - INFO - Set Policy Service
2025-08-02 10:31:38 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:38 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:38 - INFO - Set Policy Av Profile
2025-08-02 10:31:38 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:38 - INFO - Set Policy Logtraffic
2025-08-02 10:31:38 - INFO - Policy Parsing Complete
2025-08-02 10:31:38 - INFO - Parsing Policy
2025-08-02 10:31:38 - INFO - fortigate.set_policy_name
2025-08-02 10:31:38 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:38 - INFO - Set Policy Srcintf
2025-08-02 10:31:38 - INFO - Set Policy Dstintf
2025-08-02 10:31:38 - INFO - Set Policy Action
2025-08-02 10:31:38 - INFO - Set Policy Srcaddr
2025-08-02 10:31:38 - INFO - Set Policy Dstaddr
2025-08-02 10:31:38 - INFO - Set Policy Schedule
2025-08-02 10:31:38 - INFO - Set Policy Service
2025-08-02 10:31:38 - INFO - Set Policy Logtraffic
2025-08-02 10:31:38 - INFO - Policy Parsing Complete
2025-08-02 10:31:38 - INFO - Parsing Policy
2025-08-02 10:31:38 - INFO - fortigate.set_policy_name
2025-08-02 10:31:38 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:38 - INFO - Set Policy Srcintf
2025-08-02 10:31:38 - INFO - Set Policy Dstintf
2025-08-02 10:31:38 - INFO - Set Policy Action
2025-08-02 10:31:38 - INFO - Set Policy Srcaddr
2025-08-02 10:31:38 - INFO - Set Policy Dstaddr
2025-08-02 10:31:38 - INFO - Set Policy Schedule
2025-08-02 10:31:38 - INFO - Set Policy Service
2025-08-02 10:31:38 - INFO - Set Policy Logtraffic
2025-08-02 10:31:38 - INFO - Policy Parsing Complete
2025-08-02 10:31:38 - INFO - Parsing Policy
2025-08-02 10:31:38 - INFO - fortigate.set_policy_name
2025-08-02 10:31:38 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:38 - INFO - Set Policy Srcintf
2025-08-02 10:31:38 - INFO - Set Policy Dstintf
2025-08-02 10:31:38 - INFO - Set Policy Action
2025-08-02 10:31:38 - INFO - Set Policy Srcaddr
2025-08-02 10:31:38 - INFO - Set Policy Dstaddr
2025-08-02 10:31:38 - INFO - Set Policy Schedule
2025-08-02 10:31:38 - INFO - Set Policy Service
2025-08-02 10:31:38 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:38 - INFO - Set Policy Av Profile
2025-08-02 10:31:38 - INFO - Set Policy Webfilter Profile
2025-08-02 10:31:38 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:38 - INFO - Set Policy Application List
2025-08-02 10:31:38 - INFO - Policy Parsing Complete
2025-08-02 10:31:38 - INFO - Parsing Policy
2025-08-02 10:31:38 - INFO - fortigate.set_policy_name
2025-08-02 10:31:38 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:38 - INFO - Set Policy Srcintf
2025-08-02 10:31:38 - INFO - Set Policy Dstintf
2025-08-02 10:31:38 - INFO - Set Policy Action
2025-08-02 10:31:38 - INFO - Set Policy Srcaddr
2025-08-02 10:31:38 - INFO - Set Policy Dstaddr
2025-08-02 10:31:38 - INFO - Set Policy Schedule
2025-08-02 10:31:38 - INFO - Set Policy Service
2025-08-02 10:31:38 - INFO - Set Policy Logtraffic
2025-08-02 10:31:38 - INFO - Policy Parsing Complete
2025-08-02 10:31:38 - INFO - Parsing Policy
2025-08-02 10:31:38 - INFO - fortigate.set_policy_name
2025-08-02 10:31:38 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:38 - INFO - Set Policy Srcintf
2025-08-02 10:31:38 - INFO - Set Policy Dstintf
2025-08-02 10:31:38 - INFO - Set Policy Action
2025-08-02 10:31:38 - INFO - Set Policy Srcaddr
2025-08-02 10:31:38 - INFO - Set Policy Dstaddr
2025-08-02 10:31:38 - INFO - Set Policy Schedule
2025-08-02 10:31:38 - INFO - Set Policy Service
2025-08-02 10:31:38 - INFO - Set Policy Logtraffic
2025-08-02 10:31:38 - INFO - Set Policy Comments
2025-08-02 10:31:38 - INFO - Policy Parsing Complete
2025-08-02 10:31:38 - INFO - Parsing Policy
2025-08-02 10:31:38 - INFO - fortigate.set_policy_name
2025-08-02 10:31:38 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:38 - INFO - Set Policy Srcintf
2025-08-02 10:31:38 - INFO - Set Policy Dstintf
2025-08-02 10:31:38 - INFO - Set Policy Action
2025-08-02 10:31:38 - INFO - Set Policy Srcaddr
2025-08-02 10:31:38 - INFO - Set Policy Dstaddr
2025-08-02 10:31:38 - INFO - Set Policy Schedule
2025-08-02 10:31:38 - INFO - Set Policy Service
2025-08-02 10:31:38 - INFO - Set Policy Logtraffic
2025-08-02 10:31:38 - INFO - Policy Parsing Complete
2025-08-02 10:31:38 - INFO - Parsing Policy
2025-08-02 10:31:38 - INFO - fortigate.set_policy_name
2025-08-02 10:31:38 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:38 - INFO - Set Policy Srcintf
2025-08-02 10:31:38 - INFO - Set Policy Dstintf
2025-08-02 10:31:38 - INFO - Set Policy Action
2025-08-02 10:31:38 - INFO - Set Policy Srcaddr
2025-08-02 10:31:38 - INFO - Set Policy Dstaddr
2025-08-02 10:31:38 - INFO - Set Policy Schedule
2025-08-02 10:31:38 - INFO - Set Policy Service
2025-08-02 10:31:38 - INFO - Set Policy Logtraffic
2025-08-02 10:31:38 - INFO - Policy Parsing Complete
2025-08-02 10:31:38 - INFO - Parsing Policy
2025-08-02 10:31:38 - INFO - fortigate.set_policy_name
2025-08-02 10:31:38 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:38 - INFO - Set Policy Srcintf
2025-08-02 10:31:38 - INFO - Set Policy Dstintf
2025-08-02 10:31:38 - INFO - Set Policy Action
2025-08-02 10:31:38 - INFO - Set Policy Srcaddr
2025-08-02 10:31:38 - INFO - Set Policy Dstaddr
2025-08-02 10:31:38 - INFO - Set Policy Schedule
2025-08-02 10:31:38 - INFO - Set Policy Service
2025-08-02 10:31:38 - INFO - Set Policy Logtraffic
2025-08-02 10:31:38 - INFO - Policy Parsing Complete
2025-08-02 10:31:38 - INFO - Parsing Policy
2025-08-02 10:31:38 - INFO - fortigate.set_policy_name
2025-08-02 10:31:38 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:38 - INFO - Set Policy Srcintf
2025-08-02 10:31:38 - INFO - Set Policy Dstintf
2025-08-02 10:31:38 - INFO - Set Policy Action
2025-08-02 10:31:38 - INFO - Set Policy Srcaddr
2025-08-02 10:31:38 - INFO - Set Policy Dstaddr
2025-08-02 10:31:38 - INFO - Set Policy Schedule
2025-08-02 10:31:38 - INFO - Set Policy Service
2025-08-02 10:31:38 - INFO - Set Policy Logtraffic
2025-08-02 10:31:38 - INFO - Set Policy Comments
2025-08-02 10:31:38 - INFO - Policy Parsing Complete
2025-08-02 10:31:38 - INFO - Parsing Policy
2025-08-02 10:31:38 - INFO - fortigate.set_policy_name
2025-08-02 10:31:38 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:38 - INFO - Set Policy Srcintf
2025-08-02 10:31:38 - INFO - Set Policy Dstintf
2025-08-02 10:31:38 - INFO - Set Policy Action
2025-08-02 10:31:38 - INFO - Set Policy Srcaddr
2025-08-02 10:31:38 - INFO - Set Policy Dstaddr
2025-08-02 10:31:38 - INFO - Set Policy Schedule
2025-08-02 10:31:38 - INFO - Set Policy Service
2025-08-02 10:31:38 - INFO - Set Policy Logtraffic
2025-08-02 10:31:38 - INFO - Policy Parsing Complete
2025-08-02 10:31:38 - INFO - Parsing Policy
2025-08-02 10:31:38 - INFO - fortigate.set_policy_name
2025-08-02 10:31:38 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:38 - INFO - Set Policy Srcintf
2025-08-02 10:31:38 - INFO - Set Policy Dstintf
2025-08-02 10:31:38 - INFO - Set Policy Action
2025-08-02 10:31:38 - INFO - Set Policy Srcaddr
2025-08-02 10:31:38 - INFO - Set Policy Dstaddr
2025-08-02 10:31:38 - INFO - Set Policy Schedule
2025-08-02 10:31:38 - INFO - Set Policy Service
2025-08-02 10:31:38 - INFO - Set Policy Logtraffic
2025-08-02 10:31:38 - INFO - Policy Parsing Complete
2025-08-02 10:31:38 - INFO - Parsing Policy
2025-08-02 10:31:38 - INFO - fortigate.set_policy_name
2025-08-02 10:31:38 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:38 - INFO - Set Policy Srcintf
2025-08-02 10:31:38 - INFO - Set Policy Dstintf
2025-08-02 10:31:38 - INFO - Set Policy Action
2025-08-02 10:31:38 - INFO - Set Policy Srcaddr
2025-08-02 10:31:38 - INFO - Set Policy Dstaddr
2025-08-02 10:31:38 - INFO - Set Policy Schedule
2025-08-02 10:31:38 - INFO - Set Policy Service
2025-08-02 10:31:38 - INFO - Set Policy Logtraffic
2025-08-02 10:31:38 - INFO - Policy Parsing Complete
2025-08-02 10:31:38 - INFO - Parsing Policy
2025-08-02 10:31:38 - INFO - fortigate.set_policy_name
2025-08-02 10:31:38 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:38 - INFO - Set Policy Srcintf
2025-08-02 10:31:38 - INFO - Set Policy Dstintf
2025-08-02 10:31:38 - INFO - Set Policy Action
2025-08-02 10:31:38 - INFO - Set Policy Srcaddr
2025-08-02 10:31:38 - INFO - Set Policy Dstaddr
2025-08-02 10:31:38 - INFO - Set Policy Schedule
2025-08-02 10:31:38 - INFO - Set Policy Service
2025-08-02 10:31:38 - INFO - Set Policy Logtraffic
2025-08-02 10:31:38 - INFO - Policy Parsing Complete
2025-08-02 10:31:38 - INFO - Parsing Policy
2025-08-02 10:31:38 - INFO - fortigate.set_policy_name
2025-08-02 10:31:38 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:38 - INFO - Set Policy Srcintf
2025-08-02 10:31:38 - INFO - Set Policy Dstintf
2025-08-02 10:31:38 - INFO - Set Policy Action
2025-08-02 10:31:38 - INFO - Set Policy Srcaddr
2025-08-02 10:31:38 - INFO - Set Policy Dstaddr
2025-08-02 10:31:38 - INFO - Set Policy Schedule
2025-08-02 10:31:38 - INFO - Set Policy Service
2025-08-02 10:31:38 - INFO - Set Policy Logtraffic
2025-08-02 10:31:38 - INFO - Policy Parsing Complete
2025-08-02 10:31:38 - INFO - Parsing Policy
2025-08-02 10:31:38 - INFO - fortigate.set_policy_name
2025-08-02 10:31:38 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:38 - INFO - Set Policy Srcintf
2025-08-02 10:31:38 - INFO - Set Policy Dstintf
2025-08-02 10:31:38 - INFO - Set Policy Action
2025-08-02 10:31:38 - INFO - Set Policy Srcaddr
2025-08-02 10:31:38 - INFO - Set Policy Dstaddr
2025-08-02 10:31:38 - INFO - Set Policy Schedule
2025-08-02 10:31:38 - INFO - Set Policy Service
2025-08-02 10:31:38 - INFO - Set Policy Logtraffic
2025-08-02 10:31:38 - INFO - Policy Parsing Complete
2025-08-02 10:31:38 - INFO - Parsing Policy
2025-08-02 10:31:38 - INFO - fortigate.set_policy_name
2025-08-02 10:31:38 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:38 - INFO - Set Policy Srcintf
2025-08-02 10:31:38 - INFO - Set Policy Dstintf
2025-08-02 10:31:38 - INFO - Set Policy Action
2025-08-02 10:31:38 - INFO - Set Policy Srcaddr
2025-08-02 10:31:38 - INFO - Set Policy Dstaddr
2025-08-02 10:31:38 - INFO - Set Policy Schedule
2025-08-02 10:31:38 - INFO - Set Policy Service
2025-08-02 10:31:38 - INFO - Set Policy Logtraffic
2025-08-02 10:31:38 - INFO - Policy Parsing Complete
2025-08-02 10:31:38 - INFO - Parsing Policy
2025-08-02 10:31:38 - INFO - fortigate.set_policy_name
2025-08-02 10:31:38 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:38 - INFO - Set Policy Srcintf
2025-08-02 10:31:38 - INFO - Set Policy Dstintf
2025-08-02 10:31:38 - INFO - Set Policy Action
2025-08-02 10:31:38 - INFO - Set Policy Srcaddr
2025-08-02 10:31:38 - INFO - Set Policy Dstaddr
2025-08-02 10:31:38 - INFO - Set Policy Schedule
2025-08-02 10:31:38 - INFO - Set Policy Service
2025-08-02 10:31:38 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:38 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:38 - INFO - Set Policy Av Profile
2025-08-02 10:31:38 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:39 - INFO - Set Policy Logtraffic
2025-08-02 10:31:39 - INFO - Set Policy Comments
2025-08-02 10:31:39 - INFO - Policy Parsing Complete
2025-08-02 10:31:39 - INFO - Parsing Policy
2025-08-02 10:31:39 - INFO - fortigate.set_policy_name
2025-08-02 10:31:39 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:39 - INFO - Set Policy Srcintf
2025-08-02 10:31:39 - INFO - Set Policy Dstintf
2025-08-02 10:31:39 - INFO - Set Policy Action
2025-08-02 10:31:39 - INFO - Set Policy Srcaddr
2025-08-02 10:31:39 - INFO - Set Policy Dstaddr
2025-08-02 10:31:39 - INFO - Set Policy Schedule
2025-08-02 10:31:39 - INFO - Set Policy Service
2025-08-02 10:31:39 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:39 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:39 - INFO - Set Policy Av Profile
2025-08-02 10:31:39 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:39 - INFO - Set Policy Logtraffic
2025-08-02 10:31:39 - INFO - Set Policy Comments
2025-08-02 10:31:39 - INFO - Policy Parsing Complete
2025-08-02 10:31:39 - INFO - Parsing Policy
2025-08-02 10:31:39 - INFO - fortigate.set_policy_name
2025-08-02 10:31:39 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:39 - INFO - Set Policy Srcintf
2025-08-02 10:31:39 - INFO - Set Policy Dstintf
2025-08-02 10:31:39 - INFO - Set Policy Action
2025-08-02 10:31:39 - INFO - Set Policy Srcaddr
2025-08-02 10:31:39 - INFO - Set Policy Dstaddr
2025-08-02 10:31:39 - INFO - Set Policy Schedule
2025-08-02 10:31:39 - INFO - Set Policy Service
2025-08-02 10:31:39 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:39 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:39 - INFO - Set Policy Av Profile
2025-08-02 10:31:39 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:39 - INFO - Set Policy Logtraffic
2025-08-02 10:31:39 - INFO - Set Policy Comments
2025-08-02 10:31:39 - INFO - Policy Parsing Complete
2025-08-02 10:31:39 - INFO - Parsing Policy
2025-08-02 10:31:39 - INFO - fortigate.set_policy_name
2025-08-02 10:31:39 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:39 - INFO - Set Policy Srcintf
2025-08-02 10:31:39 - INFO - Set Policy Dstintf
2025-08-02 10:31:39 - INFO - Set Policy Action
2025-08-02 10:31:39 - INFO - Set Policy Srcaddr
2025-08-02 10:31:39 - INFO - Set Policy Dstaddr
2025-08-02 10:31:39 - INFO - Set Policy Schedule
2025-08-02 10:31:39 - INFO - Set Policy Service
2025-08-02 10:31:39 - INFO - Policy Parsing Complete
2025-08-02 10:31:39 - INFO - Parsing Policy
2025-08-02 10:31:39 - INFO - fortigate.set_policy_name
2025-08-02 10:31:39 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:39 - INFO - Set Policy Srcintf
2025-08-02 10:31:39 - INFO - Set Policy Dstintf
2025-08-02 10:31:39 - INFO - Set Policy Action
2025-08-02 10:31:39 - INFO - Set Policy Srcaddr
2025-08-02 10:31:39 - INFO - Set Policy Dstaddr
2025-08-02 10:31:39 - INFO - Set Policy Schedule
2025-08-02 10:31:39 - INFO - Set Policy Service
2025-08-02 10:31:39 - INFO - Set Policy Logtraffic
2025-08-02 10:31:39 - INFO - Policy Parsing Complete
2025-08-02 10:31:39 - INFO - Parsing Policy
2025-08-02 10:31:39 - INFO - fortigate.set_policy_name
2025-08-02 10:31:39 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:39 - INFO - Set Policy Srcintf
2025-08-02 10:31:39 - INFO - Set Policy Dstintf
2025-08-02 10:31:39 - INFO - Set Policy Action
2025-08-02 10:31:39 - INFO - Set Policy Srcaddr
2025-08-02 10:31:39 - INFO - Set Policy Dstaddr
2025-08-02 10:31:39 - INFO - Set Policy Schedule
2025-08-02 10:31:39 - INFO - Set Policy Service
2025-08-02 10:31:39 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:39 - INFO - Set Policy Logtraffic
2025-08-02 10:31:39 - INFO - Policy Parsing Complete
2025-08-02 10:31:39 - INFO - Parsing Policy
2025-08-02 10:31:39 - INFO - fortigate.set_policy_name
2025-08-02 10:31:39 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:39 - INFO - Set Policy Srcintf
2025-08-02 10:31:39 - INFO - Set Policy Dstintf
2025-08-02 10:31:39 - INFO - Set Policy Action
2025-08-02 10:31:39 - INFO - Set Policy Srcaddr
2025-08-02 10:31:39 - INFO - Set Policy Dstaddr
2025-08-02 10:31:39 - INFO - Set Policy Schedule
2025-08-02 10:31:39 - INFO - Set Policy Service
2025-08-02 10:31:39 - INFO - Set Policy Logtraffic
2025-08-02 10:31:39 - INFO - Policy Parsing Complete
2025-08-02 10:31:39 - INFO - Parsing Policy
2025-08-02 10:31:39 - INFO - fortigate.set_policy_name
2025-08-02 10:31:39 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:39 - INFO - Set Policy Srcintf
2025-08-02 10:31:39 - INFO - Set Policy Dstintf
2025-08-02 10:31:39 - INFO - Set Policy Action
2025-08-02 10:31:39 - INFO - Set Policy Srcaddr
2025-08-02 10:31:39 - INFO - Set Policy Dstaddr
2025-08-02 10:31:39 - INFO - Set Policy Schedule
2025-08-02 10:31:39 - INFO - Set Policy Service
2025-08-02 10:31:39 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:39 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:39 - INFO - Set Policy Logtraffic
2025-08-02 10:31:39 - INFO - Set Policy Comments
2025-08-02 10:31:39 - INFO - Policy Parsing Complete
2025-08-02 10:31:39 - INFO - Parsing Policy
2025-08-02 10:31:39 - INFO - fortigate.set_policy_name
2025-08-02 10:31:39 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:39 - INFO - Set Policy Srcintf
2025-08-02 10:31:39 - INFO - Set Policy Dstintf
2025-08-02 10:31:39 - INFO - Set Policy Action
2025-08-02 10:31:39 - INFO - Set Policy Srcaddr
2025-08-02 10:31:39 - INFO - Set Policy Dstaddr
2025-08-02 10:31:39 - INFO - Set Policy Schedule
2025-08-02 10:31:39 - INFO - Set Policy Service
2025-08-02 10:31:39 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:39 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:39 - INFO - Set Policy Av Profile
2025-08-02 10:31:39 - INFO - Set Policy Webfilter Profile
2025-08-02 10:31:39 - INFO - Set Policy Application List
2025-08-02 10:31:39 - INFO - Set Policy Logtraffic
2025-08-02 10:31:39 - INFO - Policy Parsing Complete
2025-08-02 10:31:39 - INFO - Parsing Policy
2025-08-02 10:31:39 - INFO - fortigate.set_policy_name
2025-08-02 10:31:39 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:39 - INFO - Set Policy Srcintf
2025-08-02 10:31:39 - INFO - Set Policy Dstintf
2025-08-02 10:31:39 - INFO - Set Policy Action
2025-08-02 10:31:39 - INFO - Set Policy Srcaddr
2025-08-02 10:31:39 - INFO - Set Policy Dstaddr
2025-08-02 10:31:39 - INFO - Set Policy Schedule
2025-08-02 10:31:39 - INFO - Set Policy Service
2025-08-02 10:31:39 - INFO - Set Policy Logtraffic
2025-08-02 10:31:39 - INFO - Policy Parsing Complete
2025-08-02 10:31:39 - INFO - Parsing Policy
2025-08-02 10:31:39 - INFO - fortigate.set_policy_name
2025-08-02 10:31:39 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:39 - INFO - Set Policy Srcintf
2025-08-02 10:31:39 - INFO - Set Policy Dstintf
2025-08-02 10:31:39 - INFO - Set Policy Action
2025-08-02 10:31:39 - INFO - Set Policy Srcaddr
2025-08-02 10:31:39 - INFO - Set Policy Dstaddr
2025-08-02 10:31:39 - INFO - Set Policy Schedule
2025-08-02 10:31:39 - INFO - Set Policy Service
2025-08-02 10:31:39 - INFO - Set Policy Logtraffic
2025-08-02 10:31:39 - INFO - Policy Parsing Complete
2025-08-02 10:31:39 - INFO - Parsing Policy
2025-08-02 10:31:39 - INFO - fortigate.set_policy_name
2025-08-02 10:31:39 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:39 - INFO - Set Policy Srcintf
2025-08-02 10:31:39 - INFO - Set Policy Dstintf
2025-08-02 10:31:39 - INFO - Set Policy Action
2025-08-02 10:31:39 - INFO - Set Policy Srcaddr
2025-08-02 10:31:39 - INFO - Set Policy Dstaddr
2025-08-02 10:31:39 - INFO - Set Policy Schedule
2025-08-02 10:31:39 - INFO - Set Policy Service
2025-08-02 10:31:39 - INFO - Set Policy Nat
2025-08-02 10:31:39 - INFO - Set Policy Ippool
2025-08-02 10:31:39 - INFO - fortigate.set_policy_poolname
2025-08-02 10:31:39 - INFO - Policy Parsing Complete
2025-08-02 10:31:39 - INFO - Parsing Policy
2025-08-02 10:31:39 - INFO - fortigate.set_policy_name
2025-08-02 10:31:39 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:39 - INFO - Set Policy Srcintf
2025-08-02 10:31:39 - INFO - Set Policy Dstintf
2025-08-02 10:31:39 - INFO - Set Policy Action
2025-08-02 10:31:39 - INFO - Set Policy Srcaddr
2025-08-02 10:31:39 - INFO - Set Policy Dstaddr
2025-08-02 10:31:39 - INFO - Set Policy Schedule
2025-08-02 10:31:39 - INFO - Set Policy Service
2025-08-02 10:31:39 - INFO - Set Policy Nat
2025-08-02 10:31:39 - INFO - Set Policy Ippool
2025-08-02 10:31:39 - INFO - fortigate.set_policy_poolname
2025-08-02 10:31:39 - INFO - Policy Parsing Complete
2025-08-02 10:31:39 - INFO - Parsing Policy
2025-08-02 10:31:39 - INFO - fortigate.set_policy_name
2025-08-02 10:31:39 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:39 - INFO - Set Policy Srcintf
2025-08-02 10:31:39 - INFO - Set Policy Dstintf
2025-08-02 10:31:39 - INFO - Set Policy Action
2025-08-02 10:31:39 - INFO - Set Policy Srcaddr
2025-08-02 10:31:39 - INFO - Set Policy Dstaddr
2025-08-02 10:31:39 - INFO - Set Policy Schedule
2025-08-02 10:31:39 - INFO - Set Policy Service
2025-08-02 10:31:39 - INFO - Set Policy Nat
2025-08-02 10:31:39 - INFO - Set Policy Ippool
2025-08-02 10:31:39 - INFO - fortigate.set_policy_poolname
2025-08-02 10:31:39 - INFO - Policy Parsing Complete
2025-08-02 10:31:39 - INFO - Parsing Policy
2025-08-02 10:31:39 - INFO - fortigate.set_policy_name
2025-08-02 10:31:39 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:39 - INFO - Set Policy Srcintf
2025-08-02 10:31:39 - INFO - Set Policy Dstintf
2025-08-02 10:31:39 - INFO - Set Policy Action
2025-08-02 10:31:39 - INFO - Set Policy Srcaddr
2025-08-02 10:31:39 - INFO - Set Policy Dstaddr
2025-08-02 10:31:39 - INFO - Set Policy Schedule
2025-08-02 10:31:39 - INFO - Set Policy Service
2025-08-02 10:31:39 - INFO - Set Policy Nat
2025-08-02 10:31:39 - INFO - Set Policy Ippool
2025-08-02 10:31:39 - INFO - fortigate.set_policy_poolname
2025-08-02 10:31:39 - INFO - Policy Parsing Complete
2025-08-02 10:31:39 - INFO - Parsing Policy
2025-08-02 10:31:39 - INFO - fortigate.set_policy_name
2025-08-02 10:31:39 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:39 - INFO - Set Policy Srcintf
2025-08-02 10:31:39 - INFO - Set Policy Dstintf
2025-08-02 10:31:39 - INFO - Set Policy Action
2025-08-02 10:31:39 - INFO - Set Policy Srcaddr
2025-08-02 10:31:39 - INFO - Set Policy Dstaddr
2025-08-02 10:31:39 - INFO - Set Policy Schedule
2025-08-02 10:31:39 - INFO - Set Policy Service
2025-08-02 10:31:39 - INFO - Set Policy Nat
2025-08-02 10:31:39 - INFO - Set Policy Ippool
2025-08-02 10:31:39 - INFO - fortigate.set_policy_poolname
2025-08-02 10:31:39 - INFO - Policy Parsing Complete
2025-08-02 10:31:39 - INFO - Parsing Policy
2025-08-02 10:31:39 - INFO - fortigate.set_policy_name
2025-08-02 10:31:39 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:39 - INFO - Set Policy Srcintf
2025-08-02 10:31:39 - INFO - Set Policy Dstintf
2025-08-02 10:31:39 - INFO - Set Policy Action
2025-08-02 10:31:39 - INFO - Set Policy Srcaddr
2025-08-02 10:31:39 - INFO - Set Policy Dstaddr
2025-08-02 10:31:39 - INFO - Set Policy Schedule
2025-08-02 10:31:39 - INFO - Set Policy Service
2025-08-02 10:31:39 - INFO - Set Policy Nat
2025-08-02 10:31:39 - INFO - Set Policy Ippool
2025-08-02 10:31:39 - INFO - fortigate.set_policy_poolname
2025-08-02 10:31:39 - INFO - Policy Parsing Complete
2025-08-02 10:31:39 - INFO - Parsing Policy
2025-08-02 10:31:39 - INFO - fortigate.set_policy_name
2025-08-02 10:31:39 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:39 - INFO - Set Policy Srcintf
2025-08-02 10:31:39 - INFO - Set Policy Dstintf
2025-08-02 10:31:39 - INFO - Set Policy Action
2025-08-02 10:31:39 - INFO - Set Policy Srcaddr
2025-08-02 10:31:39 - INFO - Set Policy Dstaddr
2025-08-02 10:31:39 - INFO - Set Policy Schedule
2025-08-02 10:31:39 - INFO - Set Policy Service
2025-08-02 10:31:39 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:39 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:39 - INFO - Set Policy Logtraffic
2025-08-02 10:31:39 - INFO - Set Policy Nat
2025-08-02 10:31:39 - INFO - Set Policy Ippool
2025-08-02 10:31:39 - INFO - fortigate.set_policy_poolname
2025-08-02 10:31:39 - INFO - Policy Parsing Complete
2025-08-02 10:31:39 - INFO - Parsing Policy
2025-08-02 10:31:39 - INFO - fortigate.set_policy_name
2025-08-02 10:31:39 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:39 - INFO - Set Policy Srcintf
2025-08-02 10:31:39 - INFO - Set Policy Dstintf
2025-08-02 10:31:39 - INFO - Set Policy Action
2025-08-02 10:31:39 - INFO - Set Policy Srcaddr
2025-08-02 10:31:39 - INFO - Set Policy Schedule
2025-08-02 10:31:39 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:39 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:39 - INFO - Set Policy Application List
2025-08-02 10:31:39 - INFO - Set Policy Logtraffic
2025-08-02 10:31:39 - INFO - Set Policy Nat
2025-08-02 10:31:39 - INFO - Set Policy Ippool
2025-08-02 10:31:39 - INFO - fortigate.set_policy_poolname
2025-08-02 10:31:39 - WARNING - Policy Missing Fields
2025-08-02 10:31:39 - INFO - Policy Parsing Complete
2025-08-02 10:31:39 - INFO - Parsing Policy
2025-08-02 10:31:39 - INFO - 设置策略状态: disable
2025-08-02 10:31:39 - INFO - fortigate.set_policy_name
2025-08-02 10:31:39 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:39 - INFO - Set Policy Srcintf
2025-08-02 10:31:39 - INFO - Set Policy Dstintf
2025-08-02 10:31:39 - INFO - Set Policy Action
2025-08-02 10:31:39 - INFO - Set Policy Srcaddr
2025-08-02 10:31:39 - INFO - Set Policy Dstaddr
2025-08-02 10:31:39 - INFO - Set Policy Schedule
2025-08-02 10:31:39 - INFO - Set Policy Service
2025-08-02 10:31:39 - INFO - Set Policy Logtraffic
2025-08-02 10:31:39 - INFO - Set Policy Nat
2025-08-02 10:31:39 - INFO - Set Policy Ippool
2025-08-02 10:31:39 - INFO - fortigate.set_policy_poolname
2025-08-02 10:31:39 - INFO - Policy Parsing Complete
2025-08-02 10:31:39 - INFO - Parsing Policy
2025-08-02 10:31:39 - INFO - fortigate.set_policy_name
2025-08-02 10:31:39 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:39 - INFO - Set Policy Srcintf
2025-08-02 10:31:39 - INFO - Set Policy Dstintf
2025-08-02 10:31:39 - INFO - Set Policy Action
2025-08-02 10:31:39 - INFO - Set Policy Srcaddr
2025-08-02 10:31:39 - INFO - Set Policy Dstaddr
2025-08-02 10:31:39 - INFO - Set Policy Schedule
2025-08-02 10:31:39 - INFO - Set Policy Service
2025-08-02 10:31:39 - INFO - Set Policy Logtraffic
2025-08-02 10:31:39 - INFO - Policy Parsing Complete
2025-08-02 10:31:39 - INFO - Parsing Policy
2025-08-02 10:31:39 - INFO - fortigate.set_policy_name
2025-08-02 10:31:39 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:39 - INFO - Set Policy Srcintf
2025-08-02 10:31:39 - INFO - Set Policy Dstintf
2025-08-02 10:31:39 - INFO - Set Policy Action
2025-08-02 10:31:39 - INFO - Set Policy Srcaddr
2025-08-02 10:31:39 - INFO - Set Policy Dstaddr
2025-08-02 10:31:39 - INFO - Set Policy Schedule
2025-08-02 10:31:39 - INFO - Set Policy Service
2025-08-02 10:31:39 - INFO - Set Policy Logtraffic
2025-08-02 10:31:39 - INFO - Policy Parsing Complete
2025-08-02 10:31:39 - INFO - Parsing Policy
2025-08-02 10:31:39 - INFO - fortigate.set_policy_name
2025-08-02 10:31:39 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:39 - INFO - Set Policy Srcintf
2025-08-02 10:31:39 - INFO - Set Policy Dstintf
2025-08-02 10:31:39 - INFO - Set Policy Action
2025-08-02 10:31:39 - INFO - Set Policy Srcaddr
2025-08-02 10:31:39 - INFO - Set Policy Dstaddr
2025-08-02 10:31:39 - INFO - Set Policy Schedule
2025-08-02 10:31:39 - INFO - Set Policy Service
2025-08-02 10:31:39 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:39 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:39 - INFO - Set Policy Av Profile
2025-08-02 10:31:39 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:39 - INFO - Set Policy Logtraffic
2025-08-02 10:31:39 - INFO - Policy Parsing Complete
2025-08-02 10:31:39 - INFO - Parsing Policy
2025-08-02 10:31:39 - INFO - 设置策略状态: disable
2025-08-02 10:31:39 - INFO - fortigate.set_policy_name
2025-08-02 10:31:39 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:39 - INFO - Set Policy Srcintf
2025-08-02 10:31:39 - INFO - Set Policy Dstintf
2025-08-02 10:31:39 - INFO - Set Policy Action
2025-08-02 10:31:39 - INFO - Set Policy Srcaddr
2025-08-02 10:31:39 - INFO - Set Policy Dstaddr
2025-08-02 10:31:39 - INFO - Set Policy Schedule
2025-08-02 10:31:39 - INFO - Set Policy Service
2025-08-02 10:31:39 - INFO - Set Policy Logtraffic
2025-08-02 10:31:39 - INFO - Policy Parsing Complete
2025-08-02 10:31:39 - INFO - Parsing Policy
2025-08-02 10:31:39 - INFO - fortigate.set_policy_name
2025-08-02 10:31:39 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:39 - INFO - Set Policy Srcintf
2025-08-02 10:31:39 - INFO - Set Policy Dstintf
2025-08-02 10:31:39 - INFO - Set Policy Action
2025-08-02 10:31:39 - INFO - Set Policy Srcaddr
2025-08-02 10:31:39 - INFO - Set Policy Dstaddr
2025-08-02 10:31:39 - INFO - Set Policy Schedule
2025-08-02 10:31:39 - INFO - Set Policy Service
2025-08-02 10:31:39 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:39 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:39 - INFO - Set Policy Av Profile
2025-08-02 10:31:39 - INFO - Set Policy Webfilter Profile
2025-08-02 10:31:39 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:39 - INFO - Set Policy Logtraffic
2025-08-02 10:31:39 - INFO - Policy Parsing Complete
2025-08-02 10:31:39 - INFO - Parsing Policy
2025-08-02 10:31:39 - INFO - fortigate.set_policy_name
2025-08-02 10:31:39 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:39 - INFO - Set Policy Srcintf
2025-08-02 10:31:39 - INFO - Set Policy Dstintf
2025-08-02 10:31:39 - INFO - Set Policy Action
2025-08-02 10:31:39 - INFO - Set Policy Srcaddr
2025-08-02 10:31:39 - INFO - Set Policy Dstaddr
2025-08-02 10:31:39 - INFO - Set Policy Schedule
2025-08-02 10:31:39 - INFO - Set Policy Service
2025-08-02 10:31:39 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:39 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:39 - INFO - Set Policy Av Profile
2025-08-02 10:31:39 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:39 - INFO - Set Policy Logtraffic
2025-08-02 10:31:39 - INFO - Set Policy Comments
2025-08-02 10:31:39 - INFO - Policy Parsing Complete
2025-08-02 10:31:39 - INFO - Parsing Policy
2025-08-02 10:31:39 - INFO - fortigate.set_policy_name
2025-08-02 10:31:39 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:39 - INFO - Set Policy Srcintf
2025-08-02 10:31:39 - INFO - Set Policy Dstintf
2025-08-02 10:31:39 - INFO - Set Policy Action
2025-08-02 10:31:39 - INFO - Set Policy Srcaddr
2025-08-02 10:31:39 - INFO - Set Policy Dstaddr
2025-08-02 10:31:39 - INFO - Set Policy Schedule
2025-08-02 10:31:39 - INFO - Set Policy Service
2025-08-02 10:31:39 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:39 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:39 - INFO - Set Policy Av Profile
2025-08-02 10:31:39 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:39 - INFO - Set Policy Logtraffic
2025-08-02 10:31:39 - INFO - Set Policy Comments
2025-08-02 10:31:39 - INFO - Policy Parsing Complete
2025-08-02 10:31:39 - INFO - Parsing Policy
2025-08-02 10:31:39 - INFO - fortigate.set_policy_name
2025-08-02 10:31:39 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:39 - INFO - Set Policy Srcintf
2025-08-02 10:31:39 - INFO - Set Policy Dstintf
2025-08-02 10:31:39 - INFO - Set Policy Action
2025-08-02 10:31:39 - INFO - Set Policy Srcaddr
2025-08-02 10:31:39 - INFO - Set Policy Dstaddr
2025-08-02 10:31:39 - INFO - Set Policy Schedule
2025-08-02 10:31:39 - INFO - Set Policy Service
2025-08-02 10:31:39 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:39 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:39 - INFO - Set Policy Logtraffic
2025-08-02 10:31:39 - INFO - Set Policy Comments
2025-08-02 10:31:39 - INFO - Policy Parsing Complete
2025-08-02 10:31:39 - INFO - Parsing Policy
2025-08-02 10:31:39 - INFO - fortigate.set_policy_name
2025-08-02 10:31:39 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:39 - INFO - Set Policy Srcintf
2025-08-02 10:31:39 - INFO - Set Policy Dstintf
2025-08-02 10:31:39 - INFO - Set Policy Action
2025-08-02 10:31:39 - INFO - Set Policy Srcaddr
2025-08-02 10:31:39 - INFO - Set Policy Dstaddr
2025-08-02 10:31:39 - INFO - Set Policy Schedule
2025-08-02 10:31:39 - INFO - Set Policy Service
2025-08-02 10:31:39 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:39 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:39 - INFO - Set Policy Webfilter Profile
2025-08-02 10:31:39 - INFO - Set Policy Logtraffic
2025-08-02 10:31:39 - INFO - Policy Parsing Complete
2025-08-02 10:31:39 - INFO - Parsing Policy
2025-08-02 10:31:39 - INFO - fortigate.set_policy_name
2025-08-02 10:31:39 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:39 - INFO - Set Policy Srcintf
2025-08-02 10:31:39 - INFO - Set Policy Dstintf
2025-08-02 10:31:39 - INFO - Set Policy Action
2025-08-02 10:31:39 - INFO - Set Policy Srcaddr
2025-08-02 10:31:39 - INFO - Set Policy Dstaddr
2025-08-02 10:31:39 - INFO - Set Policy Schedule
2025-08-02 10:31:39 - INFO - Set Policy Service
2025-08-02 10:31:39 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:39 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:39 - INFO - Set Policy Av Profile
2025-08-02 10:31:39 - INFO - Set Policy Webfilter Profile
2025-08-02 10:31:39 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:39 - INFO - Set Policy Logtraffic
2025-08-02 10:31:39 - INFO - Policy Parsing Complete
2025-08-02 10:31:39 - INFO - Parsing Policy
2025-08-02 10:31:39 - INFO - fortigate.set_policy_name
2025-08-02 10:31:39 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:39 - INFO - Set Policy Srcintf
2025-08-02 10:31:39 - INFO - Set Policy Dstintf
2025-08-02 10:31:39 - INFO - Set Policy Action
2025-08-02 10:31:39 - INFO - Set Policy Srcaddr
2025-08-02 10:31:39 - INFO - Set Policy Dstaddr
2025-08-02 10:31:39 - INFO - Set Policy Schedule
2025-08-02 10:31:39 - INFO - Set Policy Service
2025-08-02 10:31:39 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:39 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:39 - INFO - Set Policy Av Profile
2025-08-02 10:31:39 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:39 - INFO - Set Policy Logtraffic
2025-08-02 10:31:39 - INFO - Policy Parsing Complete
2025-08-02 10:31:39 - INFO - Parsing Policy
2025-08-02 10:31:39 - INFO - fortigate.set_policy_name
2025-08-02 10:31:39 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:39 - INFO - Set Policy Srcintf
2025-08-02 10:31:39 - INFO - Set Policy Dstintf
2025-08-02 10:31:39 - INFO - Set Policy Action
2025-08-02 10:31:39 - INFO - Set Policy Srcaddr
2025-08-02 10:31:39 - INFO - Set Policy Dstaddr
2025-08-02 10:31:39 - INFO - Set Policy Schedule
2025-08-02 10:31:39 - INFO - Set Policy Service
2025-08-02 10:31:39 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:39 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:39 - INFO - Set Policy Av Profile
2025-08-02 10:31:39 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:39 - INFO - Set Policy Logtraffic
2025-08-02 10:31:39 - INFO - Policy Parsing Complete
2025-08-02 10:31:39 - INFO - Parsing Policy
2025-08-02 10:31:39 - INFO - fortigate.set_policy_name
2025-08-02 10:31:39 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:39 - INFO - Set Policy Srcintf
2025-08-02 10:31:39 - INFO - Set Policy Dstintf
2025-08-02 10:31:39 - INFO - Set Policy Action
2025-08-02 10:31:39 - INFO - Set Policy Srcaddr
2025-08-02 10:31:39 - INFO - Set Policy Dstaddr
2025-08-02 10:31:39 - INFO - Set Policy Schedule
2025-08-02 10:31:39 - INFO - Set Policy Service
2025-08-02 10:31:39 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:39 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:39 - INFO - Set Policy Av Profile
2025-08-02 10:31:39 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:39 - INFO - Set Policy Logtraffic
2025-08-02 10:31:39 - INFO - Set Policy Comments
2025-08-02 10:31:39 - INFO - Policy Parsing Complete
2025-08-02 10:31:39 - INFO - Parsing Policy
2025-08-02 10:31:39 - INFO - fortigate.set_policy_name
2025-08-02 10:31:39 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:39 - INFO - Set Policy Srcintf
2025-08-02 10:31:39 - INFO - Set Policy Dstintf
2025-08-02 10:31:39 - INFO - Set Policy Action
2025-08-02 10:31:39 - INFO - Set Policy Srcaddr
2025-08-02 10:31:39 - INFO - Set Policy Dstaddr
2025-08-02 10:31:39 - INFO - Set Policy Schedule
2025-08-02 10:31:39 - INFO - Set Policy Service
2025-08-02 10:31:40 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:40 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:40 - INFO - Set Policy Av Profile
2025-08-02 10:31:40 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:40 - INFO - Set Policy Logtraffic
2025-08-02 10:31:40 - INFO - Policy Parsing Complete
2025-08-02 10:31:40 - INFO - Parsing Policy
2025-08-02 10:31:40 - INFO - fortigate.set_policy_name
2025-08-02 10:31:40 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:40 - INFO - Set Policy Srcintf
2025-08-02 10:31:40 - INFO - Set Policy Dstintf
2025-08-02 10:31:40 - INFO - Set Policy Action
2025-08-02 10:31:40 - INFO - Set Policy Srcaddr
2025-08-02 10:31:40 - INFO - Set Policy Dstaddr
2025-08-02 10:31:40 - INFO - Set Policy Schedule
2025-08-02 10:31:40 - INFO - Set Policy Service
2025-08-02 10:31:40 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:40 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:40 - INFO - Set Policy Logtraffic
2025-08-02 10:31:40 - INFO - Policy Parsing Complete
2025-08-02 10:31:40 - INFO - Parsing Policy
2025-08-02 10:31:40 - INFO - 设置策略状态: disable
2025-08-02 10:31:40 - INFO - fortigate.set_policy_name
2025-08-02 10:31:40 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:40 - INFO - Set Policy Srcintf
2025-08-02 10:31:40 - INFO - Set Policy Dstintf
2025-08-02 10:31:40 - INFO - Set Policy Action
2025-08-02 10:31:40 - INFO - Set Policy Srcaddr
2025-08-02 10:31:40 - INFO - Set Policy Dstaddr
2025-08-02 10:31:40 - INFO - Set Policy Schedule
2025-08-02 10:31:40 - INFO - Set Policy Service
2025-08-02 10:31:40 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:40 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:40 - INFO - Set Policy Logtraffic
2025-08-02 10:31:40 - INFO - Set Policy Comments
2025-08-02 10:31:40 - INFO - Policy Parsing Complete
2025-08-02 10:31:40 - INFO - Parsing Policy
2025-08-02 10:31:40 - INFO - fortigate.set_policy_name
2025-08-02 10:31:40 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:40 - INFO - Set Policy Srcintf
2025-08-02 10:31:40 - INFO - Set Policy Dstintf
2025-08-02 10:31:40 - INFO - Set Policy Action
2025-08-02 10:31:40 - INFO - Set Policy Srcaddr
2025-08-02 10:31:40 - INFO - Set Policy Dstaddr
2025-08-02 10:31:40 - INFO - Set Policy Schedule
2025-08-02 10:31:40 - INFO - Set Policy Service
2025-08-02 10:31:40 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:40 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:40 - INFO - Set Policy Logtraffic
2025-08-02 10:31:40 - INFO - Policy Parsing Complete
2025-08-02 10:31:40 - INFO - Parsing Policy
2025-08-02 10:31:40 - INFO - fortigate.set_policy_name
2025-08-02 10:31:40 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:40 - INFO - Set Policy Srcintf
2025-08-02 10:31:40 - INFO - Set Policy Dstintf
2025-08-02 10:31:40 - INFO - Set Policy Action
2025-08-02 10:31:40 - INFO - Set Policy Srcaddr
2025-08-02 10:31:40 - INFO - Set Policy Dstaddr
2025-08-02 10:31:40 - INFO - Set Policy Schedule
2025-08-02 10:31:40 - INFO - Set Policy Service
2025-08-02 10:31:40 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:40 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:40 - INFO - Set Policy Logtraffic
2025-08-02 10:31:40 - INFO - Policy Parsing Complete
2025-08-02 10:31:40 - INFO - Parsing Policy
2025-08-02 10:31:40 - INFO - fortigate.set_policy_name
2025-08-02 10:31:40 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:40 - INFO - Set Policy Srcintf
2025-08-02 10:31:40 - INFO - Set Policy Dstintf
2025-08-02 10:31:40 - INFO - Set Policy Action
2025-08-02 10:31:40 - INFO - Set Policy Srcaddr
2025-08-02 10:31:40 - INFO - Set Policy Dstaddr
2025-08-02 10:31:40 - INFO - Set Policy Schedule
2025-08-02 10:31:40 - INFO - Set Policy Service
2025-08-02 10:31:40 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:40 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:40 - INFO - Set Policy Logtraffic
2025-08-02 10:31:40 - INFO - Policy Parsing Complete
2025-08-02 10:31:40 - INFO - Parsing Policy
2025-08-02 10:31:40 - INFO - fortigate.set_policy_name
2025-08-02 10:31:40 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:40 - INFO - Set Policy Srcintf
2025-08-02 10:31:40 - INFO - Set Policy Dstintf
2025-08-02 10:31:40 - INFO - Set Policy Action
2025-08-02 10:31:40 - INFO - Set Policy Srcaddr
2025-08-02 10:31:40 - INFO - Set Policy Dstaddr
2025-08-02 10:31:40 - INFO - Set Policy Schedule
2025-08-02 10:31:40 - INFO - Set Policy Service
2025-08-02 10:31:40 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:40 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:40 - INFO - Set Policy Logtraffic
2025-08-02 10:31:40 - INFO - Policy Parsing Complete
2025-08-02 10:31:40 - INFO - Parsing Policy
2025-08-02 10:31:40 - INFO - fortigate.set_policy_name
2025-08-02 10:31:40 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:40 - INFO - Set Policy Srcintf
2025-08-02 10:31:40 - INFO - Set Policy Dstintf
2025-08-02 10:31:40 - INFO - Set Policy Action
2025-08-02 10:31:40 - INFO - Set Policy Srcaddr
2025-08-02 10:31:40 - INFO - Set Policy Dstaddr
2025-08-02 10:31:40 - INFO - Set Policy Schedule
2025-08-02 10:31:40 - INFO - Set Policy Service
2025-08-02 10:31:40 - INFO - Set Policy Logtraffic
2025-08-02 10:31:40 - INFO - Set Policy Nat
2025-08-02 10:31:40 - INFO - Set Policy Ippool
2025-08-02 10:31:40 - INFO - fortigate.set_policy_poolname
2025-08-02 10:31:40 - INFO - Policy Parsing Complete
2025-08-02 10:31:40 - INFO - Parsing Policy
2025-08-02 10:31:40 - INFO - fortigate.set_policy_name
2025-08-02 10:31:40 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:40 - INFO - Set Policy Srcintf
2025-08-02 10:31:40 - INFO - Set Policy Dstintf
2025-08-02 10:31:40 - INFO - Set Policy Action
2025-08-02 10:31:40 - INFO - Set Policy Srcaddr
2025-08-02 10:31:40 - INFO - Set Policy Dstaddr
2025-08-02 10:31:40 - INFO - Set Policy Schedule
2025-08-02 10:31:40 - INFO - Set Policy Service
2025-08-02 10:31:40 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:40 - INFO - Set Policy Logtraffic
2025-08-02 10:31:40 - INFO - Set Policy Nat
2025-08-02 10:31:40 - INFO - Set Policy Ippool
2025-08-02 10:31:40 - INFO - fortigate.set_policy_poolname
2025-08-02 10:31:40 - INFO - Set Policy Comments
2025-08-02 10:31:40 - INFO - Policy Parsing Complete
2025-08-02 10:31:40 - INFO - Parsing Policy
2025-08-02 10:31:40 - INFO - fortigate.set_policy_name
2025-08-02 10:31:40 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:40 - INFO - Set Policy Srcintf
2025-08-02 10:31:40 - INFO - Set Policy Dstintf
2025-08-02 10:31:40 - INFO - Set Policy Action
2025-08-02 10:31:40 - INFO - Set Policy Srcaddr
2025-08-02 10:31:40 - INFO - Set Policy Dstaddr
2025-08-02 10:31:40 - INFO - Set Policy Schedule
2025-08-02 10:31:40 - INFO - Set Policy Service
2025-08-02 10:31:40 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:40 - INFO - Set Policy Logtraffic
2025-08-02 10:31:40 - INFO - Set Policy Nat
2025-08-02 10:31:40 - INFO - Set Policy Ippool
2025-08-02 10:31:40 - INFO - fortigate.set_policy_poolname
2025-08-02 10:31:40 - INFO - Policy Parsing Complete
2025-08-02 10:31:40 - INFO - Parsing Policy
2025-08-02 10:31:40 - INFO - fortigate.set_policy_name
2025-08-02 10:31:40 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:40 - INFO - Set Policy Srcintf
2025-08-02 10:31:40 - INFO - Set Policy Dstintf
2025-08-02 10:31:40 - INFO - Set Policy Action
2025-08-02 10:31:40 - INFO - Set Policy Srcaddr
2025-08-02 10:31:40 - INFO - Set Policy Dstaddr
2025-08-02 10:31:40 - INFO - Set Policy Schedule
2025-08-02 10:31:40 - INFO - Set Policy Service
2025-08-02 10:31:40 - INFO - Set Policy Logtraffic
2025-08-02 10:31:40 - INFO - Set Policy Nat
2025-08-02 10:31:40 - INFO - Set Policy Ippool
2025-08-02 10:31:40 - INFO - fortigate.set_policy_poolname
2025-08-02 10:31:40 - INFO - Policy Parsing Complete
2025-08-02 10:31:40 - INFO - Parsing Policy
2025-08-02 10:31:40 - INFO - fortigate.set_policy_name
2025-08-02 10:31:40 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:40 - INFO - Set Policy Srcintf
2025-08-02 10:31:40 - INFO - Set Policy Dstintf
2025-08-02 10:31:40 - INFO - Set Policy Action
2025-08-02 10:31:40 - INFO - Set Policy Srcaddr
2025-08-02 10:31:40 - INFO - Set Policy Dstaddr
2025-08-02 10:31:40 - INFO - Set Policy Schedule
2025-08-02 10:31:40 - INFO - Set Policy Service
2025-08-02 10:31:40 - INFO - Set Policy Logtraffic
2025-08-02 10:31:40 - INFO - Policy Parsing Complete
2025-08-02 10:31:40 - INFO - Parsing Policy
2025-08-02 10:31:40 - INFO - fortigate.set_policy_name
2025-08-02 10:31:40 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:40 - INFO - Set Policy Srcintf
2025-08-02 10:31:40 - INFO - Set Policy Dstintf
2025-08-02 10:31:40 - INFO - Set Policy Action
2025-08-02 10:31:40 - INFO - Set Policy Srcaddr
2025-08-02 10:31:40 - INFO - Set Policy Dstaddr
2025-08-02 10:31:40 - INFO - Set Policy Schedule
2025-08-02 10:31:40 - INFO - Set Policy Service
2025-08-02 10:31:40 - INFO - Set Policy Logtraffic
2025-08-02 10:31:40 - INFO - Policy Parsing Complete
2025-08-02 10:31:40 - INFO - Parsing Policy
2025-08-02 10:31:40 - INFO - fortigate.set_policy_name
2025-08-02 10:31:40 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:40 - INFO - Set Policy Srcintf
2025-08-02 10:31:40 - INFO - Set Policy Dstintf
2025-08-02 10:31:40 - INFO - Set Policy Action
2025-08-02 10:31:40 - INFO - Set Policy Srcaddr
2025-08-02 10:31:40 - INFO - Set Policy Dstaddr
2025-08-02 10:31:40 - INFO - Set Policy Schedule
2025-08-02 10:31:40 - INFO - Set Policy Service
2025-08-02 10:31:40 - INFO - Set Policy Logtraffic
2025-08-02 10:31:40 - INFO - Policy Parsing Complete
2025-08-02 10:31:40 - INFO - Parsing Policy
2025-08-02 10:31:40 - INFO - fortigate.set_policy_name
2025-08-02 10:31:40 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:40 - INFO - Set Policy Srcintf
2025-08-02 10:31:40 - INFO - Set Policy Dstintf
2025-08-02 10:31:40 - INFO - Set Policy Action
2025-08-02 10:31:40 - INFO - Set Policy Srcaddr
2025-08-02 10:31:40 - INFO - Set Policy Dstaddr
2025-08-02 10:31:40 - INFO - Set Policy Schedule
2025-08-02 10:31:40 - INFO - Set Policy Service
2025-08-02 10:31:40 - INFO - Policy Parsing Complete
2025-08-02 10:31:40 - INFO - Parsing Policy
2025-08-02 10:31:40 - INFO - fortigate.set_policy_name
2025-08-02 10:31:40 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:40 - INFO - Set Policy Srcintf
2025-08-02 10:31:40 - INFO - Set Policy Dstintf
2025-08-02 10:31:40 - INFO - Set Policy Action
2025-08-02 10:31:40 - INFO - Set Policy Srcaddr
2025-08-02 10:31:40 - INFO - Set Policy Dstaddr
2025-08-02 10:31:40 - INFO - Set Policy Schedule
2025-08-02 10:31:40 - INFO - Set Policy Service
2025-08-02 10:31:40 - INFO - Set Policy Logtraffic
2025-08-02 10:31:40 - INFO - Policy Parsing Complete
2025-08-02 10:31:40 - INFO - Parsing Policy
2025-08-02 10:31:40 - INFO - fortigate.set_policy_name
2025-08-02 10:31:40 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:40 - INFO - Set Policy Srcintf
2025-08-02 10:31:40 - INFO - Set Policy Dstintf
2025-08-02 10:31:40 - INFO - Set Policy Action
2025-08-02 10:31:40 - INFO - Set Policy Srcaddr
2025-08-02 10:31:40 - INFO - Set Policy Dstaddr
2025-08-02 10:31:40 - INFO - Set Policy Schedule
2025-08-02 10:31:40 - INFO - Set Policy Service
2025-08-02 10:31:40 - INFO - Set Policy Logtraffic
2025-08-02 10:31:40 - INFO - Set Policy Comments
2025-08-02 10:31:40 - INFO - Policy Parsing Complete
2025-08-02 10:31:40 - INFO - Parsing Policy
2025-08-02 10:31:40 - INFO - fortigate.set_policy_name
2025-08-02 10:31:40 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:40 - INFO - Set Policy Srcintf
2025-08-02 10:31:40 - INFO - Set Policy Dstintf
2025-08-02 10:31:40 - INFO - Set Policy Action
2025-08-02 10:31:40 - INFO - Set Policy Srcaddr
2025-08-02 10:31:40 - INFO - Set Policy Dstaddr
2025-08-02 10:31:40 - INFO - Set Policy Schedule
2025-08-02 10:31:40 - INFO - Set Policy Service
2025-08-02 10:31:40 - INFO - Set Policy Logtraffic
2025-08-02 10:31:40 - INFO - Set Policy Comments
2025-08-02 10:31:40 - INFO - Policy Parsing Complete
2025-08-02 10:31:40 - INFO - Parsing Policy
2025-08-02 10:31:40 - INFO - fortigate.set_policy_name
2025-08-02 10:31:40 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:40 - INFO - Set Policy Srcintf
2025-08-02 10:31:40 - INFO - Set Policy Dstintf
2025-08-02 10:31:40 - INFO - Set Policy Action
2025-08-02 10:31:40 - INFO - Set Policy Srcaddr
2025-08-02 10:31:40 - INFO - Set Policy Dstaddr
2025-08-02 10:31:40 - INFO - Set Policy Schedule
2025-08-02 10:31:40 - INFO - Set Policy Service
2025-08-02 10:31:40 - INFO - Set Policy Logtraffic
2025-08-02 10:31:40 - INFO - 设置策略用户: ['hakantoptas']
2025-08-02 10:31:40 - INFO - Policy Parsing Complete
2025-08-02 10:31:40 - INFO - Parsing Policy
2025-08-02 10:31:40 - INFO - fortigate.set_policy_name
2025-08-02 10:31:40 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:40 - INFO - Set Policy Srcintf
2025-08-02 10:31:40 - INFO - Set Policy Dstintf
2025-08-02 10:31:40 - INFO - Set Policy Action
2025-08-02 10:31:40 - INFO - Set Policy Srcaddr
2025-08-02 10:31:40 - INFO - Set Policy Dstaddr
2025-08-02 10:31:40 - INFO - Set Policy Schedule
2025-08-02 10:31:40 - INFO - Set Policy Service
2025-08-02 10:31:40 - INFO - Set Policy Logtraffic
2025-08-02 10:31:40 - INFO - 设置策略用户: ['hakantoptas']
2025-08-02 10:31:40 - INFO - Set Policy Comments
2025-08-02 10:31:40 - INFO - Policy Parsing Complete
2025-08-02 10:31:40 - INFO - Parsing Policy
2025-08-02 10:31:40 - INFO - fortigate.set_policy_name
2025-08-02 10:31:40 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:40 - INFO - Set Policy Srcintf
2025-08-02 10:31:40 - INFO - Set Policy Dstintf
2025-08-02 10:31:40 - INFO - Set Policy Action
2025-08-02 10:31:40 - INFO - Set Policy Srcaddr
2025-08-02 10:31:40 - INFO - Set Policy Dstaddr
2025-08-02 10:31:40 - INFO - Set Policy Schedule
2025-08-02 10:31:40 - INFO - Set Policy Service
2025-08-02 10:31:40 - INFO - Set Policy Logtraffic
2025-08-02 10:31:40 - INFO - Set Policy Comments
2025-08-02 10:31:40 - INFO - Policy Parsing Complete
2025-08-02 10:31:40 - INFO - Parsing Policy
2025-08-02 10:31:40 - INFO - fortigate.set_policy_name
2025-08-02 10:31:40 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:40 - INFO - Set Policy Srcintf
2025-08-02 10:31:40 - INFO - Set Policy Dstintf
2025-08-02 10:31:40 - INFO - Set Policy Action
2025-08-02 10:31:40 - INFO - Set Policy Srcaddr
2025-08-02 10:31:40 - INFO - Set Policy Schedule
2025-08-02 10:31:40 - INFO - Set Policy Logtraffic
2025-08-02 10:31:40 - INFO - Set Policy Nat
2025-08-02 10:31:40 - INFO - Set Policy Ippool
2025-08-02 10:31:40 - INFO - fortigate.set_policy_poolname
2025-08-02 10:31:40 - WARNING - Policy Missing Fields
2025-08-02 10:31:40 - INFO - Policy Parsing Complete
2025-08-02 10:31:40 - INFO - Parsing Policy
2025-08-02 10:31:40 - INFO - fortigate.set_policy_name
2025-08-02 10:31:40 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:40 - INFO - Set Policy Srcintf
2025-08-02 10:31:40 - INFO - Set Policy Dstintf
2025-08-02 10:31:40 - INFO - Set Policy Action
2025-08-02 10:31:40 - INFO - Set Policy Srcaddr
2025-08-02 10:31:40 - INFO - Set Policy Dstaddr
2025-08-02 10:31:40 - INFO - Set Policy Schedule
2025-08-02 10:31:40 - INFO - Set Policy Service
2025-08-02 10:31:40 - INFO - Set Policy Logtraffic
2025-08-02 10:31:40 - INFO - Set Policy Comments
2025-08-02 10:31:40 - INFO - Policy Parsing Complete
2025-08-02 10:31:40 - INFO - Parsing Policy
2025-08-02 10:31:40 - INFO - fortigate.set_policy_name
2025-08-02 10:31:40 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:40 - INFO - Set Policy Srcintf
2025-08-02 10:31:40 - INFO - Set Policy Dstintf
2025-08-02 10:31:40 - INFO - Set Policy Action
2025-08-02 10:31:40 - INFO - Set Policy Srcaddr
2025-08-02 10:31:40 - INFO - Set Policy Dstaddr
2025-08-02 10:31:40 - INFO - Set Policy Schedule
2025-08-02 10:31:40 - INFO - Set Policy Service
2025-08-02 10:31:40 - INFO - Set Policy Logtraffic
2025-08-02 10:31:40 - INFO - Policy Parsing Complete
2025-08-02 10:31:40 - INFO - Parsing Policy
2025-08-02 10:31:40 - INFO - fortigate.set_policy_name
2025-08-02 10:31:40 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:40 - INFO - Set Policy Srcintf
2025-08-02 10:31:40 - INFO - Set Policy Dstintf
2025-08-02 10:31:40 - INFO - Set Policy Action
2025-08-02 10:31:40 - INFO - Set Policy Srcaddr
2025-08-02 10:31:40 - INFO - Set Policy Dstaddr
2025-08-02 10:31:40 - INFO - Set Policy Schedule
2025-08-02 10:31:40 - INFO - Set Policy Service
2025-08-02 10:31:40 - INFO - Set Policy Logtraffic
2025-08-02 10:31:40 - INFO - Set Policy Nat
2025-08-02 10:31:40 - INFO - Set Policy Ippool
2025-08-02 10:31:40 - INFO - fortigate.set_policy_poolname
2025-08-02 10:31:40 - INFO - Policy Parsing Complete
2025-08-02 10:31:40 - INFO - Parsing Policy
2025-08-02 10:31:40 - INFO - fortigate.set_policy_name
2025-08-02 10:31:40 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:40 - INFO - Set Policy Srcintf
2025-08-02 10:31:40 - INFO - Set Policy Dstintf
2025-08-02 10:31:40 - INFO - Set Policy Action
2025-08-02 10:31:40 - INFO - Set Policy Srcaddr
2025-08-02 10:31:40 - INFO - Set Policy Dstaddr
2025-08-02 10:31:40 - INFO - Set Policy Schedule
2025-08-02 10:31:40 - INFO - Set Policy Service
2025-08-02 10:31:40 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:40 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:40 - INFO - Set Policy Logtraffic
2025-08-02 10:31:40 - INFO - Set Policy Nat
2025-08-02 10:31:40 - INFO - Set Policy Ippool
2025-08-02 10:31:40 - INFO - fortigate.set_policy_poolname
2025-08-02 10:31:40 - INFO - Policy Parsing Complete
2025-08-02 10:31:40 - INFO - Parsing Policy
2025-08-02 10:31:40 - INFO - fortigate.set_policy_name
2025-08-02 10:31:40 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:40 - INFO - Set Policy Srcintf
2025-08-02 10:31:40 - INFO - Set Policy Dstintf
2025-08-02 10:31:40 - INFO - Set Policy Action
2025-08-02 10:31:40 - INFO - Set Policy Srcaddr
2025-08-02 10:31:40 - INFO - Set Policy Dstaddr
2025-08-02 10:31:40 - INFO - Set Policy Schedule
2025-08-02 10:31:40 - INFO - Set Policy Service
2025-08-02 10:31:40 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:40 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:40 - INFO - Set Policy Webfilter Profile
2025-08-02 10:31:40 - INFO - Set Policy Application List
2025-08-02 10:31:40 - INFO - Set Policy Logtraffic
2025-08-02 10:31:40 - INFO - Set Policy Nat
2025-08-02 10:31:40 - INFO - Set Policy Ippool
2025-08-02 10:31:40 - INFO - fortigate.set_policy_poolname
2025-08-02 10:31:40 - INFO - Policy Parsing Complete
2025-08-02 10:31:40 - INFO - Parsing Policy
2025-08-02 10:31:40 - INFO - fortigate.set_policy_name
2025-08-02 10:31:40 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:40 - INFO - Set Policy Srcintf
2025-08-02 10:31:40 - INFO - Set Policy Dstintf
2025-08-02 10:31:40 - INFO - Set Policy Action
2025-08-02 10:31:40 - INFO - Set Policy Srcaddr
2025-08-02 10:31:40 - INFO - Set Policy Dstaddr
2025-08-02 10:31:40 - INFO - Set Policy Schedule
2025-08-02 10:31:40 - INFO - Set Policy Service
2025-08-02 10:31:40 - INFO - Set Policy Logtraffic
2025-08-02 10:31:40 - INFO - Set Policy Nat
2025-08-02 10:31:40 - INFO - Set Policy Ippool
2025-08-02 10:31:40 - INFO - fortigate.set_policy_poolname
2025-08-02 10:31:40 - INFO - Policy Parsing Complete
2025-08-02 10:31:40 - INFO - Parsing Policy
2025-08-02 10:31:40 - INFO - fortigate.set_policy_name
2025-08-02 10:31:40 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:40 - INFO - Set Policy Srcintf
2025-08-02 10:31:40 - INFO - Set Policy Dstintf
2025-08-02 10:31:40 - INFO - Set Policy Action
2025-08-02 10:31:40 - INFO - Set Policy Srcaddr
2025-08-02 10:31:40 - INFO - Set Policy Dstaddr
2025-08-02 10:31:40 - INFO - Set Policy Schedule
2025-08-02 10:31:40 - INFO - Set Policy Service
2025-08-02 10:31:40 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:40 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:40 - INFO - Set Policy Av Profile
2025-08-02 10:31:40 - INFO - Set Policy Webfilter Profile
2025-08-02 10:31:40 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:40 - INFO - Set Policy Application List
2025-08-02 10:31:40 - INFO - Set Policy Logtraffic
2025-08-02 10:31:40 - INFO - Set Policy Nat
2025-08-02 10:31:40 - INFO - Set Policy Ippool
2025-08-02 10:31:40 - INFO - fortigate.set_policy_poolname
2025-08-02 10:31:40 - INFO - Set Policy Comments
2025-08-02 10:31:40 - INFO - Policy Parsing Complete
2025-08-02 10:31:40 - INFO - Parsing Policy
2025-08-02 10:31:40 - INFO - fortigate.set_policy_name
2025-08-02 10:31:40 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:40 - INFO - Set Policy Srcintf
2025-08-02 10:31:40 - INFO - Set Policy Dstintf
2025-08-02 10:31:40 - INFO - Set Policy Action
2025-08-02 10:31:40 - INFO - Set Policy Srcaddr
2025-08-02 10:31:40 - INFO - Set Policy Dstaddr
2025-08-02 10:31:40 - INFO - Set Policy Schedule
2025-08-02 10:31:40 - INFO - Set Policy Service
2025-08-02 10:31:40 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:40 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:40 - INFO - Set Policy Logtraffic
2025-08-02 10:31:40 - INFO - Set Policy Nat
2025-08-02 10:31:40 - INFO - Set Policy Ippool
2025-08-02 10:31:40 - INFO - fortigate.set_policy_poolname
2025-08-02 10:31:40 - INFO - Set Policy Comments
2025-08-02 10:31:40 - INFO - Policy Parsing Complete
2025-08-02 10:31:40 - INFO - Parsing Policy
2025-08-02 10:31:40 - INFO - fortigate.set_policy_name
2025-08-02 10:31:40 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:40 - INFO - Set Policy Srcintf
2025-08-02 10:31:40 - INFO - Set Policy Dstintf
2025-08-02 10:31:40 - INFO - Set Policy Action
2025-08-02 10:31:40 - INFO - Set Policy Srcaddr
2025-08-02 10:31:40 - INFO - Set Policy Dstaddr
2025-08-02 10:31:40 - INFO - Set Policy Schedule
2025-08-02 10:31:40 - INFO - Set Policy Service
2025-08-02 10:31:40 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:40 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:40 - INFO - Set Policy Logtraffic
2025-08-02 10:31:40 - INFO - Set Policy Nat
2025-08-02 10:31:40 - INFO - Set Policy Ippool
2025-08-02 10:31:40 - INFO - fortigate.set_policy_poolname
2025-08-02 10:31:40 - INFO - Set Policy Comments
2025-08-02 10:31:40 - INFO - Policy Parsing Complete
2025-08-02 10:31:40 - INFO - Parsing Policy
2025-08-02 10:31:40 - INFO - fortigate.set_policy_name
2025-08-02 10:31:40 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:40 - INFO - Set Policy Srcintf
2025-08-02 10:31:40 - INFO - Set Policy Dstintf
2025-08-02 10:31:40 - INFO - Set Policy Action
2025-08-02 10:31:40 - INFO - Set Policy Srcaddr
2025-08-02 10:31:40 - INFO - Set Policy Dstaddr
2025-08-02 10:31:40 - INFO - Set Policy Schedule
2025-08-02 10:31:40 - INFO - Set Policy Service
2025-08-02 10:31:40 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:40 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:40 - INFO - Set Policy Av Profile
2025-08-02 10:31:40 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:40 - INFO - Set Policy Logtraffic
2025-08-02 10:31:40 - INFO - Set Policy Nat
2025-08-02 10:31:40 - INFO - Set Policy Ippool
2025-08-02 10:31:40 - INFO - fortigate.set_policy_poolname
2025-08-02 10:31:40 - INFO - Policy Parsing Complete
2025-08-02 10:31:40 - INFO - Parsing Policy
2025-08-02 10:31:41 - INFO - fortigate.set_policy_name
2025-08-02 10:31:41 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:41 - INFO - Set Policy Srcintf
2025-08-02 10:31:41 - INFO - Set Policy Dstintf
2025-08-02 10:31:41 - INFO - Set Policy Action
2025-08-02 10:31:41 - INFO - Set Policy Srcaddr
2025-08-02 10:31:41 - INFO - Set Policy Dstaddr
2025-08-02 10:31:41 - INFO - Set Policy Schedule
2025-08-02 10:31:41 - INFO - Set Policy Service
2025-08-02 10:31:41 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:41 - INFO - Set Policy Logtraffic
2025-08-02 10:31:41 - INFO - Set Policy Nat
2025-08-02 10:31:41 - INFO - Set Policy Ippool
2025-08-02 10:31:41 - INFO - fortigate.set_policy_poolname
2025-08-02 10:31:41 - INFO - Policy Parsing Complete
2025-08-02 10:31:41 - INFO - Parsing Policy
2025-08-02 10:31:41 - INFO - fortigate.set_policy_name
2025-08-02 10:31:41 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:41 - INFO - Set Policy Srcintf
2025-08-02 10:31:41 - INFO - Set Policy Dstintf
2025-08-02 10:31:41 - INFO - Set Policy Action
2025-08-02 10:31:41 - INFO - Set Policy Srcaddr
2025-08-02 10:31:41 - INFO - Set Policy Dstaddr
2025-08-02 10:31:41 - INFO - Set Policy Schedule
2025-08-02 10:31:41 - INFO - Set Policy Service
2025-08-02 10:31:41 - INFO - Set Policy Nat
2025-08-02 10:31:41 - INFO - Set Policy Ippool
2025-08-02 10:31:41 - INFO - fortigate.set_policy_poolname
2025-08-02 10:31:41 - INFO - Policy Parsing Complete
2025-08-02 10:31:41 - INFO - Parsing Policy
2025-08-02 10:31:41 - INFO - fortigate.set_policy_name
2025-08-02 10:31:41 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:41 - INFO - Set Policy Srcintf
2025-08-02 10:31:41 - INFO - Set Policy Dstintf
2025-08-02 10:31:41 - INFO - Set Policy Action
2025-08-02 10:31:41 - INFO - Set Policy Srcaddr
2025-08-02 10:31:41 - INFO - Set Policy Dstaddr
2025-08-02 10:31:41 - INFO - Set Policy Schedule
2025-08-02 10:31:41 - INFO - Set Policy Service
2025-08-02 10:31:41 - INFO - Set Policy Logtraffic
2025-08-02 10:31:41 - INFO - Policy Parsing Complete
2025-08-02 10:31:41 - INFO - Parsing Policy
2025-08-02 10:31:41 - INFO - fortigate.set_policy_name
2025-08-02 10:31:41 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:41 - INFO - Set Policy Srcintf
2025-08-02 10:31:41 - INFO - Set Policy Dstintf
2025-08-02 10:31:41 - INFO - Set Policy Action
2025-08-02 10:31:41 - INFO - Set Policy Srcaddr
2025-08-02 10:31:41 - INFO - Set Policy Dstaddr
2025-08-02 10:31:41 - INFO - Set Policy Schedule
2025-08-02 10:31:41 - INFO - Set Policy Service
2025-08-02 10:31:41 - INFO - Set Policy Logtraffic
2025-08-02 10:31:41 - INFO - Set Policy Comments
2025-08-02 10:31:41 - INFO - Policy Parsing Complete
2025-08-02 10:31:41 - INFO - Parsing Policy
2025-08-02 10:31:41 - INFO - fortigate.set_policy_name
2025-08-02 10:31:41 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:41 - INFO - Set Policy Srcintf
2025-08-02 10:31:41 - INFO - Set Policy Dstintf
2025-08-02 10:31:41 - INFO - Set Policy Action
2025-08-02 10:31:41 - INFO - Set Policy Srcaddr
2025-08-02 10:31:41 - INFO - Set Policy Dstaddr
2025-08-02 10:31:41 - INFO - Set Policy Schedule
2025-08-02 10:31:41 - INFO - Set Policy Service
2025-08-02 10:31:41 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:41 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:41 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:41 - INFO - Set Policy Application List
2025-08-02 10:31:41 - INFO - Set Policy Logtraffic
2025-08-02 10:31:41 - INFO - Set Policy Nat
2025-08-02 10:31:41 - INFO - Set Policy Ippool
2025-08-02 10:31:41 - INFO - fortigate.set_policy_poolname
2025-08-02 10:31:41 - INFO - Policy Parsing Complete
2025-08-02 10:31:41 - INFO - Parsing Policy
2025-08-02 10:31:41 - INFO - fortigate.set_policy_name
2025-08-02 10:31:41 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:41 - INFO - Set Policy Srcintf
2025-08-02 10:31:41 - INFO - Set Policy Dstintf
2025-08-02 10:31:41 - INFO - Set Policy Action
2025-08-02 10:31:41 - INFO - Set Policy Srcaddr
2025-08-02 10:31:41 - INFO - Set Policy Dstaddr
2025-08-02 10:31:41 - INFO - Set Policy Schedule
2025-08-02 10:31:41 - INFO - Set Policy Service
2025-08-02 10:31:41 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:41 - INFO - Set Policy Logtraffic
2025-08-02 10:31:41 - INFO - Set Policy Nat
2025-08-02 10:31:41 - INFO - 设置策略固定端口: enable
2025-08-02 10:31:41 - INFO - Set Policy Ippool
2025-08-02 10:31:41 - INFO - fortigate.set_policy_poolname
2025-08-02 10:31:41 - INFO - Policy Parsing Complete
2025-08-02 10:31:41 - INFO - Parsing Policy
2025-08-02 10:31:41 - INFO - fortigate.set_policy_name
2025-08-02 10:31:41 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:41 - INFO - Set Policy Srcintf
2025-08-02 10:31:41 - INFO - Set Policy Dstintf
2025-08-02 10:31:41 - INFO - Set Policy Action
2025-08-02 10:31:41 - INFO - Set Policy Srcaddr
2025-08-02 10:31:41 - INFO - Set Policy Dstaddr
2025-08-02 10:31:41 - INFO - Set Policy Schedule
2025-08-02 10:31:41 - INFO - Set Policy Service
2025-08-02 10:31:41 - INFO - Set Policy Logtraffic
2025-08-02 10:31:41 - INFO - Policy Parsing Complete
2025-08-02 10:31:41 - INFO - Parsing Policy
2025-08-02 10:31:41 - INFO - fortigate.set_policy_name
2025-08-02 10:31:41 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:41 - INFO - Set Policy Srcintf
2025-08-02 10:31:41 - INFO - Set Policy Dstintf
2025-08-02 10:31:41 - INFO - Set Policy Action
2025-08-02 10:31:41 - INFO - Set Policy Srcaddr
2025-08-02 10:31:41 - INFO - Set Policy Dstaddr
2025-08-02 10:31:41 - INFO - Set Policy Schedule
2025-08-02 10:31:41 - INFO - Set Policy Service
2025-08-02 10:31:41 - INFO - Set Policy Logtraffic
2025-08-02 10:31:41 - INFO - Set Policy Nat
2025-08-02 10:31:41 - INFO - Set Policy Ippool
2025-08-02 10:31:41 - INFO - fortigate.set_policy_poolname
2025-08-02 10:31:41 - INFO - Policy Parsing Complete
2025-08-02 10:31:41 - INFO - Parsing Policy
2025-08-02 10:31:41 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:41 - INFO - Set Policy Srcintf
2025-08-02 10:31:41 - INFO - Set Policy Dstintf
2025-08-02 10:31:41 - INFO - Set Policy Action
2025-08-02 10:31:41 - INFO - Set Policy Srcaddr
2025-08-02 10:31:41 - INFO - Set Policy Dstaddr
2025-08-02 10:31:41 - INFO - Set Policy Schedule
2025-08-02 10:31:41 - INFO - Set Policy Service
2025-08-02 10:31:41 - INFO - Set Policy Logtraffic
2025-08-02 10:31:41 - INFO - Set Policy Comments
2025-08-02 10:31:41 - INFO - Policy Parsing Complete
2025-08-02 10:31:41 - INFO - Parsing Policy
2025-08-02 10:31:41 - INFO - fortigate.set_policy_name
2025-08-02 10:31:41 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:41 - INFO - Set Policy Srcintf
2025-08-02 10:31:41 - INFO - Set Policy Dstintf
2025-08-02 10:31:41 - INFO - Set Policy Action
2025-08-02 10:31:41 - INFO - Set Policy Srcaddr
2025-08-02 10:31:41 - INFO - Set Policy Dstaddr
2025-08-02 10:31:41 - INFO - Set Policy Schedule
2025-08-02 10:31:41 - INFO - Set Policy Service
2025-08-02 10:31:41 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:41 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:41 - INFO - Set Policy Av Profile
2025-08-02 10:31:41 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:41 - INFO - Policy Parsing Complete
2025-08-02 10:31:41 - INFO - Parsing Policy
2025-08-02 10:31:41 - INFO - fortigate.set_policy_name
2025-08-02 10:31:41 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:41 - INFO - Set Policy Srcintf
2025-08-02 10:31:41 - INFO - Set Policy Dstintf
2025-08-02 10:31:41 - INFO - Set Policy Action
2025-08-02 10:31:41 - INFO - Set Policy Srcaddr
2025-08-02 10:31:41 - INFO - Set Policy Dstaddr
2025-08-02 10:31:41 - INFO - Set Policy Schedule
2025-08-02 10:31:41 - INFO - Set Policy Service
2025-08-02 10:31:41 - INFO - Set Policy Logtraffic
2025-08-02 10:31:41 - INFO - Policy Parsing Complete
2025-08-02 10:31:41 - INFO - Parsing Policy
2025-08-02 10:31:41 - INFO - fortigate.set_policy_name
2025-08-02 10:31:41 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:41 - INFO - Set Policy Srcintf
2025-08-02 10:31:41 - INFO - Set Policy Dstintf
2025-08-02 10:31:41 - INFO - Set Policy Action
2025-08-02 10:31:41 - INFO - Set Policy Srcaddr
2025-08-02 10:31:41 - INFO - Set Policy Dstaddr
2025-08-02 10:31:41 - INFO - Set Policy Schedule
2025-08-02 10:31:41 - INFO - Set Policy Service
2025-08-02 10:31:41 - INFO - Set Policy Logtraffic
2025-08-02 10:31:41 - INFO - Policy Parsing Complete
2025-08-02 10:31:41 - INFO - Parsing Policy
2025-08-02 10:31:41 - INFO - fortigate.set_policy_name
2025-08-02 10:31:41 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:41 - INFO - Set Policy Srcintf
2025-08-02 10:31:41 - INFO - Set Policy Dstintf
2025-08-02 10:31:41 - INFO - Set Policy Action
2025-08-02 10:31:41 - INFO - Set Policy Srcaddr
2025-08-02 10:31:41 - INFO - Set Policy Dstaddr
2025-08-02 10:31:41 - INFO - Set Policy Schedule
2025-08-02 10:31:41 - INFO - Set Policy Service
2025-08-02 10:31:41 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:41 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:41 - INFO - Set Policy Av Profile
2025-08-02 10:31:41 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:41 - INFO - Set Policy Comments
2025-08-02 10:31:41 - INFO - Policy Parsing Complete
2025-08-02 10:31:41 - INFO - Parsing Policy
2025-08-02 10:31:41 - INFO - 设置策略状态: disable
2025-08-02 10:31:41 - INFO - fortigate.set_policy_name
2025-08-02 10:31:41 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:41 - INFO - Set Policy Srcintf
2025-08-02 10:31:41 - INFO - Set Policy Dstintf
2025-08-02 10:31:41 - INFO - Set Policy Action
2025-08-02 10:31:41 - INFO - Set Policy Srcaddr
2025-08-02 10:31:41 - INFO - Set Policy Dstaddr
2025-08-02 10:31:41 - INFO - Set Policy Schedule
2025-08-02 10:31:41 - INFO - Set Policy Service
2025-08-02 10:31:41 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:41 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:41 - INFO - Set Policy Logtraffic
2025-08-02 10:31:41 - INFO - Set Policy Nat
2025-08-02 10:31:41 - INFO - Policy Parsing Complete
2025-08-02 10:31:41 - INFO - Parsing Policy
2025-08-02 10:31:41 - INFO - fortigate.set_policy_name
2025-08-02 10:31:41 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:41 - INFO - Set Policy Srcintf
2025-08-02 10:31:41 - INFO - Set Policy Dstintf
2025-08-02 10:31:41 - INFO - Set Policy Srcaddr
2025-08-02 10:31:41 - INFO - Set Policy Dstaddr
2025-08-02 10:31:41 - INFO - Set Policy Schedule
2025-08-02 10:31:41 - INFO - Set Policy Service
2025-08-02 10:31:41 - INFO - Set Policy Logtraffic
2025-08-02 10:31:41 - WARNING - Policy Missing Fields
2025-08-02 10:31:41 - INFO - Policy Parsing Complete
2025-08-02 10:31:41 - INFO - Parsing Policy
2025-08-02 10:31:41 - INFO - fortigate.set_policy_name
2025-08-02 10:31:41 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:41 - INFO - Set Policy Srcintf
2025-08-02 10:31:41 - INFO - Set Policy Dstintf
2025-08-02 10:31:41 - INFO - Set Policy Action
2025-08-02 10:31:41 - INFO - Set Policy Srcaddr
2025-08-02 10:31:41 - INFO - Set Policy Dstaddr
2025-08-02 10:31:41 - INFO - Set Policy Schedule
2025-08-02 10:31:41 - INFO - Set Policy Service
2025-08-02 10:31:41 - INFO - Set Policy Logtraffic
2025-08-02 10:31:41 - INFO - Policy Parsing Complete
2025-08-02 10:31:41 - INFO - Parsing Policy
2025-08-02 10:31:41 - INFO - fortigate.set_policy_name
2025-08-02 10:31:41 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:41 - INFO - Set Policy Srcintf
2025-08-02 10:31:41 - INFO - Set Policy Dstintf
2025-08-02 10:31:41 - INFO - Set Policy Action
2025-08-02 10:31:41 - INFO - Set Policy Srcaddr
2025-08-02 10:31:41 - INFO - Set Policy Dstaddr
2025-08-02 10:31:41 - INFO - Set Policy Schedule
2025-08-02 10:31:41 - INFO - Set Policy Service
2025-08-02 10:31:41 - INFO - Set Policy Logtraffic
2025-08-02 10:31:41 - INFO - Policy Parsing Complete
2025-08-02 10:31:41 - INFO - Parsing Policy
2025-08-02 10:31:41 - INFO - fortigate.set_policy_name
2025-08-02 10:31:41 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:41 - INFO - Set Policy Srcintf
2025-08-02 10:31:41 - INFO - Set Policy Dstintf
2025-08-02 10:31:41 - INFO - Set Policy Action
2025-08-02 10:31:41 - INFO - Set Policy Srcaddr
2025-08-02 10:31:41 - INFO - Set Policy Dstaddr
2025-08-02 10:31:41 - INFO - Set Policy Schedule
2025-08-02 10:31:41 - INFO - Set Policy Service
2025-08-02 10:31:41 - INFO - Set Policy Logtraffic
2025-08-02 10:31:41 - INFO - Policy Parsing Complete
2025-08-02 10:31:41 - INFO - Parsing Policy
2025-08-02 10:31:41 - INFO - fortigate.set_policy_name
2025-08-02 10:31:41 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:41 - INFO - Set Policy Srcintf
2025-08-02 10:31:41 - INFO - Set Policy Dstintf
2025-08-02 10:31:41 - INFO - Set Policy Action
2025-08-02 10:31:41 - INFO - Set Policy Srcaddr
2025-08-02 10:31:41 - INFO - Set Policy Dstaddr
2025-08-02 10:31:41 - INFO - Set Policy Schedule
2025-08-02 10:31:41 - INFO - Set Policy Service
2025-08-02 10:31:41 - INFO - Set Policy Logtraffic
2025-08-02 10:31:41 - INFO - Policy Parsing Complete
2025-08-02 10:31:41 - INFO - Parsing Policy
2025-08-02 10:31:41 - INFO - fortigate.set_policy_name
2025-08-02 10:31:41 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:41 - INFO - Set Policy Srcintf
2025-08-02 10:31:41 - INFO - Set Policy Dstintf
2025-08-02 10:31:41 - INFO - Set Policy Action
2025-08-02 10:31:41 - INFO - Set Policy Srcaddr
2025-08-02 10:31:41 - INFO - Set Policy Dstaddr
2025-08-02 10:31:41 - INFO - Set Policy Schedule
2025-08-02 10:31:41 - INFO - Set Policy Service
2025-08-02 10:31:41 - INFO - Set Policy Logtraffic
2025-08-02 10:31:41 - INFO - Policy Parsing Complete
2025-08-02 10:31:41 - INFO - Parsing Policy
2025-08-02 10:31:41 - INFO - fortigate.set_policy_name
2025-08-02 10:31:41 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:41 - INFO - Set Policy Srcintf
2025-08-02 10:31:41 - INFO - Set Policy Dstintf
2025-08-02 10:31:41 - INFO - Set Policy Action
2025-08-02 10:31:41 - INFO - Set Policy Srcaddr
2025-08-02 10:31:41 - INFO - Set Policy Dstaddr
2025-08-02 10:31:41 - INFO - Set Policy Schedule
2025-08-02 10:31:41 - INFO - Set Policy Service
2025-08-02 10:31:41 - INFO - Set Policy Logtraffic
2025-08-02 10:31:41 - INFO - Policy Parsing Complete
2025-08-02 10:31:41 - INFO - Parsing Policy
2025-08-02 10:31:41 - INFO - 设置策略状态: disable
2025-08-02 10:31:41 - INFO - fortigate.set_policy_name
2025-08-02 10:31:41 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:41 - INFO - Set Policy Srcintf
2025-08-02 10:31:41 - INFO - Set Policy Dstintf
2025-08-02 10:31:41 - INFO - Set Policy Action
2025-08-02 10:31:41 - INFO - Set Policy Srcaddr
2025-08-02 10:31:41 - INFO - Set Policy Dstaddr
2025-08-02 10:31:41 - INFO - Set Policy Schedule
2025-08-02 10:31:41 - INFO - Set Policy Service
2025-08-02 10:31:41 - INFO - Set Policy Logtraffic
2025-08-02 10:31:41 - INFO - Policy Parsing Complete
2025-08-02 10:31:41 - INFO - Parsing Policy
2025-08-02 10:31:41 - INFO - fortigate.set_policy_name
2025-08-02 10:31:41 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:41 - INFO - Set Policy Srcintf
2025-08-02 10:31:41 - INFO - Set Policy Dstintf
2025-08-02 10:31:41 - INFO - Set Policy Action
2025-08-02 10:31:41 - INFO - Set Policy Srcaddr
2025-08-02 10:31:41 - INFO - Set Policy Dstaddr
2025-08-02 10:31:41 - INFO - Set Policy Schedule
2025-08-02 10:31:41 - INFO - Set Policy Service
2025-08-02 10:31:41 - INFO - Set Policy Logtraffic
2025-08-02 10:31:41 - INFO - Set Policy Nat
2025-08-02 10:31:41 - INFO - Set Policy Ippool
2025-08-02 10:31:41 - INFO - fortigate.set_policy_poolname
2025-08-02 10:31:41 - INFO - Policy Parsing Complete
2025-08-02 10:31:41 - INFO - Parsing Policy
2025-08-02 10:31:41 - INFO - fortigate.set_policy_name
2025-08-02 10:31:41 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:41 - INFO - Set Policy Srcintf
2025-08-02 10:31:41 - INFO - Set Policy Dstintf
2025-08-02 10:31:41 - INFO - Set Policy Action
2025-08-02 10:31:41 - INFO - Set Policy Srcaddr
2025-08-02 10:31:41 - INFO - Set Policy Dstaddr
2025-08-02 10:31:41 - INFO - Set Policy Schedule
2025-08-02 10:31:41 - INFO - Set Policy Service
2025-08-02 10:31:41 - INFO - Set Policy Logtraffic
2025-08-02 10:31:41 - INFO - Set Policy Nat
2025-08-02 10:31:41 - INFO - Set Policy Ippool
2025-08-02 10:31:41 - INFO - fortigate.set_policy_poolname
2025-08-02 10:31:41 - INFO - Policy Parsing Complete
2025-08-02 10:31:41 - INFO - Parsing Policy
2025-08-02 10:31:41 - INFO - fortigate.set_policy_name
2025-08-02 10:31:41 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:41 - INFO - Set Policy Srcintf
2025-08-02 10:31:41 - INFO - Set Policy Dstintf
2025-08-02 10:31:41 - INFO - Set Policy Action
2025-08-02 10:31:41 - INFO - Set Policy Srcaddr
2025-08-02 10:31:41 - INFO - Set Policy Dstaddr
2025-08-02 10:31:41 - INFO - Set Policy Schedule
2025-08-02 10:31:41 - INFO - Set Policy Service
2025-08-02 10:31:41 - INFO - Set Policy Logtraffic
2025-08-02 10:31:41 - INFO - Set Policy Nat
2025-08-02 10:31:41 - INFO - Set Policy Ippool
2025-08-02 10:31:41 - INFO - fortigate.set_policy_poolname
2025-08-02 10:31:41 - INFO - Policy Parsing Complete
2025-08-02 10:31:41 - INFO - Parsing Policy
2025-08-02 10:31:41 - INFO - fortigate.set_policy_name
2025-08-02 10:31:41 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:41 - INFO - Set Policy Srcintf
2025-08-02 10:31:41 - INFO - Set Policy Dstintf
2025-08-02 10:31:41 - INFO - Set Policy Action
2025-08-02 10:31:41 - INFO - Set Policy Srcaddr
2025-08-02 10:31:41 - INFO - Set Policy Dstaddr
2025-08-02 10:31:41 - INFO - Set Policy Schedule
2025-08-02 10:31:41 - INFO - Set Policy Service
2025-08-02 10:31:41 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:41 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:41 - INFO - Set Policy Av Profile
2025-08-02 10:31:41 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:41 - INFO - Set Policy Logtraffic
2025-08-02 10:31:41 - INFO - Policy Parsing Complete
2025-08-02 10:31:41 - INFO - Parsing Policy
2025-08-02 10:31:41 - INFO - 设置策略状态: disable
2025-08-02 10:31:41 - INFO - fortigate.set_policy_name
2025-08-02 10:31:41 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:41 - INFO - Set Policy Srcintf
2025-08-02 10:31:41 - INFO - Set Policy Dstintf
2025-08-02 10:31:41 - INFO - Set Policy Action
2025-08-02 10:31:41 - INFO - Set Policy Srcaddr
2025-08-02 10:31:41 - INFO - Set Policy Dstaddr
2025-08-02 10:31:41 - INFO - Set Policy Schedule
2025-08-02 10:31:41 - INFO - Set Policy Service
2025-08-02 10:31:41 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:41 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:41 - INFO - Set Policy Av Profile
2025-08-02 10:31:41 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:41 - INFO - Set Policy Logtraffic
2025-08-02 10:31:41 - INFO - Policy Parsing Complete
2025-08-02 10:31:41 - INFO - Parsing Policy
2025-08-02 10:31:41 - INFO - fortigate.set_policy_name
2025-08-02 10:31:41 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:41 - INFO - Set Policy Srcintf
2025-08-02 10:31:41 - INFO - Set Policy Dstintf
2025-08-02 10:31:41 - INFO - Set Policy Action
2025-08-02 10:31:41 - INFO - Set Policy Srcaddr
2025-08-02 10:31:41 - INFO - Set Policy Dstaddr
2025-08-02 10:31:41 - INFO - Set Policy Schedule
2025-08-02 10:31:41 - INFO - Set Policy Service
2025-08-02 10:31:41 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:41 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:41 - INFO - Set Policy Av Profile
2025-08-02 10:31:41 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:41 - INFO - Set Policy Logtraffic
2025-08-02 10:31:41 - INFO - Policy Parsing Complete
2025-08-02 10:31:41 - INFO - Parsing Policy
2025-08-02 10:31:41 - INFO - fortigate.set_policy_name
2025-08-02 10:31:41 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:41 - INFO - Set Policy Srcintf
2025-08-02 10:31:41 - INFO - Set Policy Dstintf
2025-08-02 10:31:41 - INFO - Set Policy Action
2025-08-02 10:31:41 - INFO - Set Policy Srcaddr
2025-08-02 10:31:41 - INFO - Set Policy Dstaddr
2025-08-02 10:31:41 - INFO - Set Policy Schedule
2025-08-02 10:31:41 - INFO - Set Policy Service
2025-08-02 10:31:41 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:41 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:41 - INFO - Set Policy Av Profile
2025-08-02 10:31:41 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:41 - INFO - Set Policy Logtraffic
2025-08-02 10:31:41 - INFO - Policy Parsing Complete
2025-08-02 10:31:41 - INFO - Parsing Policy
2025-08-02 10:31:41 - INFO - fortigate.set_policy_name
2025-08-02 10:31:41 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:41 - INFO - Set Policy Srcintf
2025-08-02 10:31:41 - INFO - Set Policy Dstintf
2025-08-02 10:31:41 - INFO - Set Policy Action
2025-08-02 10:31:42 - INFO - Set Policy Srcaddr
2025-08-02 10:31:42 - INFO - Set Policy Dstaddr
2025-08-02 10:31:42 - INFO - Set Policy Schedule
2025-08-02 10:31:42 - INFO - Set Policy Service
2025-08-02 10:31:42 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:42 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:42 - INFO - Set Policy Av Profile
2025-08-02 10:31:42 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:42 - INFO - Set Policy Logtraffic
2025-08-02 10:31:42 - INFO - Policy Parsing Complete
2025-08-02 10:31:42 - INFO - Parsing Policy
2025-08-02 10:31:42 - INFO - fortigate.set_policy_name
2025-08-02 10:31:42 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:42 - INFO - Set Policy Srcintf
2025-08-02 10:31:42 - INFO - Set Policy Dstintf
2025-08-02 10:31:42 - INFO - Set Policy Action
2025-08-02 10:31:42 - INFO - Set Policy Srcaddr
2025-08-02 10:31:42 - INFO - Set Policy Dstaddr
2025-08-02 10:31:42 - INFO - Set Policy Schedule
2025-08-02 10:31:42 - INFO - Set Policy Service
2025-08-02 10:31:42 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:42 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:42 - INFO - Set Policy Av Profile
2025-08-02 10:31:42 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:42 - INFO - Set Policy Logtraffic
2025-08-02 10:31:42 - INFO - Policy Parsing Complete
2025-08-02 10:31:42 - INFO - Parsing Policy
2025-08-02 10:31:42 - INFO - 设置策略状态: disable
2025-08-02 10:31:42 - INFO - fortigate.set_policy_name
2025-08-02 10:31:42 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:42 - INFO - Set Policy Srcintf
2025-08-02 10:31:42 - INFO - Set Policy Dstintf
2025-08-02 10:31:42 - INFO - Set Policy Action
2025-08-02 10:31:42 - INFO - Set Policy Srcaddr
2025-08-02 10:31:42 - INFO - Set Policy Dstaddr
2025-08-02 10:31:42 - INFO - Set Policy Schedule
2025-08-02 10:31:42 - INFO - Set Policy Service
2025-08-02 10:31:42 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:42 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:42 - INFO - Set Policy Av Profile
2025-08-02 10:31:42 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:42 - INFO - Set Policy Logtraffic
2025-08-02 10:31:42 - INFO - Policy Parsing Complete
2025-08-02 10:31:42 - INFO - Parsing Policy
2025-08-02 10:31:42 - INFO - fortigate.set_policy_name
2025-08-02 10:31:42 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:42 - INFO - Set Policy Srcintf
2025-08-02 10:31:42 - INFO - Set Policy Dstintf
2025-08-02 10:31:42 - INFO - Set Policy Action
2025-08-02 10:31:42 - INFO - Set Policy Srcaddr
2025-08-02 10:31:42 - INFO - Set Policy Dstaddr
2025-08-02 10:31:42 - INFO - Set Policy Schedule
2025-08-02 10:31:42 - INFO - Set Policy Service
2025-08-02 10:31:42 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:42 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:42 - INFO - Set Policy Av Profile
2025-08-02 10:31:42 - INFO - Set Policy Webfilter Profile
2025-08-02 10:31:42 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:42 - INFO - Set Policy Logtraffic
2025-08-02 10:31:42 - INFO - Policy Parsing Complete
2025-08-02 10:31:42 - INFO - Parsing Policy
2025-08-02 10:31:42 - INFO - 设置策略状态: disable
2025-08-02 10:31:42 - INFO - fortigate.set_policy_name
2025-08-02 10:31:42 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:42 - INFO - Set Policy Srcintf
2025-08-02 10:31:42 - INFO - Set Policy Dstintf
2025-08-02 10:31:42 - INFO - Set Policy Action
2025-08-02 10:31:42 - INFO - Set Policy Srcaddr
2025-08-02 10:31:42 - INFO - Set Policy Dstaddr
2025-08-02 10:31:42 - INFO - Set Policy Schedule
2025-08-02 10:31:42 - INFO - Set Policy Service
2025-08-02 10:31:42 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:42 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:42 - INFO - Set Policy Av Profile
2025-08-02 10:31:42 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:42 - INFO - Set Policy Logtraffic
2025-08-02 10:31:42 - INFO - Policy Parsing Complete
2025-08-02 10:31:42 - INFO - Parsing Policy
2025-08-02 10:31:42 - INFO - 设置策略状态: disable
2025-08-02 10:31:42 - INFO - fortigate.set_policy_name
2025-08-02 10:31:42 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:42 - INFO - Set Policy Srcintf
2025-08-02 10:31:42 - INFO - Set Policy Dstintf
2025-08-02 10:31:42 - INFO - Set Policy Action
2025-08-02 10:31:42 - INFO - Set Policy Srcaddr
2025-08-02 10:31:42 - INFO - Set Policy Dstaddr
2025-08-02 10:31:42 - INFO - Set Policy Schedule
2025-08-02 10:31:42 - INFO - Set Policy Service
2025-08-02 10:31:42 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:42 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:42 - INFO - Set Policy Av Profile
2025-08-02 10:31:42 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:42 - INFO - Set Policy Logtraffic
2025-08-02 10:31:42 - INFO - Set Policy Comments
2025-08-02 10:31:42 - INFO - Policy Parsing Complete
2025-08-02 10:31:42 - INFO - Parsing Policy
2025-08-02 10:31:42 - INFO - 设置策略状态: disable
2025-08-02 10:31:42 - INFO - fortigate.set_policy_name
2025-08-02 10:31:42 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:42 - INFO - Set Policy Srcintf
2025-08-02 10:31:42 - INFO - Set Policy Dstintf
2025-08-02 10:31:42 - INFO - Set Policy Action
2025-08-02 10:31:42 - INFO - Set Policy Srcaddr
2025-08-02 10:31:42 - INFO - Set Policy Dstaddr
2025-08-02 10:31:42 - INFO - Set Policy Schedule
2025-08-02 10:31:42 - INFO - Set Policy Service
2025-08-02 10:31:42 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:42 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:42 - INFO - Set Policy Av Profile
2025-08-02 10:31:42 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:42 - INFO - Set Policy Logtraffic
2025-08-02 10:31:42 - INFO - Policy Parsing Complete
2025-08-02 10:31:42 - INFO - Parsing Policy
2025-08-02 10:31:42 - INFO - fortigate.set_policy_name
2025-08-02 10:31:42 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:42 - INFO - Set Policy Srcintf
2025-08-02 10:31:42 - INFO - Set Policy Dstintf
2025-08-02 10:31:42 - INFO - Set Policy Action
2025-08-02 10:31:42 - INFO - Set Policy Srcaddr
2025-08-02 10:31:42 - INFO - Set Policy Dstaddr
2025-08-02 10:31:42 - INFO - Set Policy Schedule
2025-08-02 10:31:42 - INFO - Set Policy Service
2025-08-02 10:31:42 - INFO - Set Policy Logtraffic
2025-08-02 10:31:42 - INFO - Policy Parsing Complete
2025-08-02 10:31:42 - INFO - Parsing Policy
2025-08-02 10:31:42 - INFO - fortigate.set_policy_name
2025-08-02 10:31:42 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:42 - INFO - Set Policy Srcintf
2025-08-02 10:31:42 - INFO - Set Policy Dstintf
2025-08-02 10:31:42 - INFO - Set Policy Action
2025-08-02 10:31:42 - INFO - Set Policy Srcaddr
2025-08-02 10:31:42 - INFO - Set Policy Dstaddr
2025-08-02 10:31:42 - INFO - Set Policy Schedule
2025-08-02 10:31:42 - INFO - Set Policy Service
2025-08-02 10:31:42 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:42 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:42 - INFO - Set Policy Av Profile
2025-08-02 10:31:42 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:42 - INFO - Set Policy Logtraffic
2025-08-02 10:31:42 - INFO - Policy Parsing Complete
2025-08-02 10:31:42 - INFO - Parsing Policy
2025-08-02 10:31:42 - INFO - 设置策略状态: disable
2025-08-02 10:31:42 - INFO - fortigate.set_policy_name
2025-08-02 10:31:42 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:42 - INFO - Set Policy Srcintf
2025-08-02 10:31:42 - INFO - Set Policy Dstintf
2025-08-02 10:31:42 - INFO - Set Policy Action
2025-08-02 10:31:42 - INFO - Set Policy Srcaddr
2025-08-02 10:31:42 - INFO - Set Policy Dstaddr
2025-08-02 10:31:42 - INFO - Set Policy Schedule
2025-08-02 10:31:42 - INFO - Set Policy Service
2025-08-02 10:31:42 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:42 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:42 - INFO - Set Policy Av Profile
2025-08-02 10:31:42 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:42 - INFO - Set Policy Logtraffic
2025-08-02 10:31:42 - INFO - Policy Parsing Complete
2025-08-02 10:31:42 - INFO - Parsing Policy
2025-08-02 10:31:42 - INFO - fortigate.set_policy_name
2025-08-02 10:31:42 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:42 - INFO - Set Policy Srcintf
2025-08-02 10:31:42 - INFO - Set Policy Dstintf
2025-08-02 10:31:42 - INFO - Set Policy Action
2025-08-02 10:31:42 - INFO - Set Policy Srcaddr
2025-08-02 10:31:42 - INFO - Set Policy Dstaddr
2025-08-02 10:31:42 - INFO - Set Policy Schedule
2025-08-02 10:31:42 - INFO - Set Policy Service
2025-08-02 10:31:42 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:42 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:42 - INFO - Set Policy Av Profile
2025-08-02 10:31:42 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:42 - INFO - Set Policy Logtraffic
2025-08-02 10:31:42 - INFO - Policy Parsing Complete
2025-08-02 10:31:42 - INFO - Parsing Policy
2025-08-02 10:31:42 - INFO - fortigate.set_policy_name
2025-08-02 10:31:42 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:42 - INFO - Set Policy Srcintf
2025-08-02 10:31:42 - INFO - Set Policy Dstintf
2025-08-02 10:31:42 - INFO - Set Policy Action
2025-08-02 10:31:42 - INFO - Set Policy Srcaddr
2025-08-02 10:31:42 - INFO - Set Policy Dstaddr
2025-08-02 10:31:42 - INFO - Set Policy Schedule
2025-08-02 10:31:42 - INFO - Set Policy Service
2025-08-02 10:31:42 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:42 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:42 - INFO - Set Policy Av Profile
2025-08-02 10:31:42 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:42 - INFO - Set Policy Logtraffic
2025-08-02 10:31:42 - INFO - Set Policy Nat
2025-08-02 10:31:42 - INFO - Policy Parsing Complete
2025-08-02 10:31:42 - INFO - Parsing Policy
2025-08-02 10:31:42 - INFO - fortigate.set_policy_name
2025-08-02 10:31:42 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:42 - INFO - Set Policy Srcintf
2025-08-02 10:31:42 - INFO - Set Policy Dstintf
2025-08-02 10:31:42 - INFO - Set Policy Action
2025-08-02 10:31:42 - INFO - Set Policy Srcaddr
2025-08-02 10:31:42 - INFO - Set Policy Dstaddr
2025-08-02 10:31:42 - INFO - Set Policy Schedule
2025-08-02 10:31:42 - INFO - Set Policy Service
2025-08-02 10:31:42 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:42 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:42 - INFO - Set Policy Av Profile
2025-08-02 10:31:42 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:42 - INFO - Set Policy Logtraffic
2025-08-02 10:31:42 - INFO - Policy Parsing Complete
2025-08-02 10:31:42 - INFO - Parsing Policy
2025-08-02 10:31:42 - INFO - 设置策略状态: disable
2025-08-02 10:31:42 - INFO - fortigate.set_policy_name
2025-08-02 10:31:42 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:42 - INFO - Set Policy Srcintf
2025-08-02 10:31:42 - INFO - Set Policy Dstintf
2025-08-02 10:31:42 - INFO - Set Policy Action
2025-08-02 10:31:42 - INFO - Set Policy Srcaddr
2025-08-02 10:31:42 - INFO - Set Policy Dstaddr
2025-08-02 10:31:42 - INFO - Set Policy Schedule
2025-08-02 10:31:42 - INFO - Set Policy Service
2025-08-02 10:31:42 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:42 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:42 - INFO - Set Policy Av Profile
2025-08-02 10:31:42 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:42 - INFO - Set Policy Logtraffic
2025-08-02 10:31:42 - INFO - Policy Parsing Complete
2025-08-02 10:31:42 - INFO - Parsing Policy
2025-08-02 10:31:42 - INFO - fortigate.set_policy_name
2025-08-02 10:31:42 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:42 - INFO - Set Policy Srcintf
2025-08-02 10:31:42 - INFO - Set Policy Dstintf
2025-08-02 10:31:42 - INFO - Set Policy Action
2025-08-02 10:31:42 - INFO - Set Policy Srcaddr
2025-08-02 10:31:42 - INFO - Set Policy Dstaddr
2025-08-02 10:31:42 - INFO - Set Policy Schedule
2025-08-02 10:31:42 - INFO - Set Policy Service
2025-08-02 10:31:42 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:42 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:42 - INFO - Set Policy Av Profile
2025-08-02 10:31:42 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:42 - INFO - Set Policy Logtraffic
2025-08-02 10:31:42 - INFO - Policy Parsing Complete
2025-08-02 10:31:42 - INFO - Parsing Policy
2025-08-02 10:31:42 - INFO - fortigate.set_policy_name
2025-08-02 10:31:42 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:42 - INFO - Set Policy Srcintf
2025-08-02 10:31:42 - INFO - Set Policy Dstintf
2025-08-02 10:31:42 - INFO - Set Policy Action
2025-08-02 10:31:42 - INFO - Set Policy Srcaddr
2025-08-02 10:31:42 - INFO - Set Policy Dstaddr
2025-08-02 10:31:42 - INFO - Set Policy Schedule
2025-08-02 10:31:42 - INFO - Set Policy Service
2025-08-02 10:31:42 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:42 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:42 - INFO - Set Policy Av Profile
2025-08-02 10:31:42 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:42 - INFO - Set Policy Logtraffic
2025-08-02 10:31:42 - INFO - Policy Parsing Complete
2025-08-02 10:31:42 - INFO - Parsing Policy
2025-08-02 10:31:42 - INFO - fortigate.set_policy_name
2025-08-02 10:31:42 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:42 - INFO - Set Policy Srcintf
2025-08-02 10:31:42 - INFO - Set Policy Dstintf
2025-08-02 10:31:42 - INFO - Set Policy Action
2025-08-02 10:31:42 - INFO - Set Policy Srcaddr
2025-08-02 10:31:42 - INFO - Set Policy Dstaddr
2025-08-02 10:31:42 - INFO - Set Policy Schedule
2025-08-02 10:31:42 - INFO - Set Policy Service
2025-08-02 10:31:42 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:42 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:42 - INFO - Set Policy Av Profile
2025-08-02 10:31:42 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:42 - INFO - Set Policy Logtraffic
2025-08-02 10:31:42 - INFO - Policy Parsing Complete
2025-08-02 10:31:42 - INFO - Parsing Policy
2025-08-02 10:31:42 - INFO - fortigate.set_policy_name
2025-08-02 10:31:42 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:42 - INFO - Set Policy Srcintf
2025-08-02 10:31:42 - INFO - Set Policy Dstintf
2025-08-02 10:31:42 - INFO - Set Policy Action
2025-08-02 10:31:42 - INFO - Set Policy Srcaddr
2025-08-02 10:31:42 - INFO - Set Policy Dstaddr
2025-08-02 10:31:42 - INFO - Set Policy Schedule
2025-08-02 10:31:42 - INFO - Set Policy Service
2025-08-02 10:31:42 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:42 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:42 - INFO - Set Policy Av Profile
2025-08-02 10:31:42 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:42 - INFO - Set Policy Logtraffic
2025-08-02 10:31:42 - INFO - Set Policy Comments
2025-08-02 10:31:42 - INFO - Policy Parsing Complete
2025-08-02 10:31:42 - INFO - Parsing Policy
2025-08-02 10:31:42 - INFO - fortigate.set_policy_name
2025-08-02 10:31:42 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:42 - INFO - Set Policy Srcintf
2025-08-02 10:31:42 - INFO - Set Policy Dstintf
2025-08-02 10:31:42 - INFO - Set Policy Action
2025-08-02 10:31:42 - INFO - Set Policy Srcaddr
2025-08-02 10:31:42 - INFO - Set Policy Dstaddr
2025-08-02 10:31:42 - INFO - Set Policy Schedule
2025-08-02 10:31:42 - INFO - Set Policy Service
2025-08-02 10:31:42 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:42 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:42 - INFO - Set Policy Av Profile
2025-08-02 10:31:42 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:42 - INFO - Set Policy Logtraffic
2025-08-02 10:31:42 - INFO - Policy Parsing Complete
2025-08-02 10:31:42 - INFO - Parsing Policy
2025-08-02 10:31:42 - INFO - fortigate.set_policy_name
2025-08-02 10:31:42 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:42 - INFO - Set Policy Srcintf
2025-08-02 10:31:42 - INFO - Set Policy Dstintf
2025-08-02 10:31:42 - INFO - Set Policy Action
2025-08-02 10:31:42 - INFO - Set Policy Srcaddr
2025-08-02 10:31:42 - INFO - Set Policy Dstaddr
2025-08-02 10:31:42 - INFO - Set Policy Schedule
2025-08-02 10:31:42 - INFO - Set Policy Service
2025-08-02 10:31:42 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:42 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:42 - INFO - Set Policy Av Profile
2025-08-02 10:31:42 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:42 - INFO - Set Policy Logtraffic
2025-08-02 10:31:42 - INFO - Policy Parsing Complete
2025-08-02 10:31:42 - INFO - Parsing Policy
2025-08-02 10:31:42 - INFO - 设置策略状态: disable
2025-08-02 10:31:42 - INFO - fortigate.set_policy_name
2025-08-02 10:31:42 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:42 - INFO - Set Policy Srcintf
2025-08-02 10:31:42 - INFO - Set Policy Dstintf
2025-08-02 10:31:42 - INFO - Set Policy Action
2025-08-02 10:31:42 - INFO - Set Policy Srcaddr
2025-08-02 10:31:42 - INFO - Set Policy Dstaddr
2025-08-02 10:31:42 - INFO - Set Policy Schedule
2025-08-02 10:31:42 - INFO - Set Policy Service
2025-08-02 10:31:42 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:42 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:42 - INFO - Set Policy Av Profile
2025-08-02 10:31:42 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:42 - INFO - Set Policy Logtraffic
2025-08-02 10:31:42 - INFO - Policy Parsing Complete
2025-08-02 10:31:42 - INFO - Parsing Policy
2025-08-02 10:31:42 - INFO - 设置策略状态: disable
2025-08-02 10:31:42 - INFO - fortigate.set_policy_name
2025-08-02 10:31:42 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:42 - INFO - Set Policy Srcintf
2025-08-02 10:31:42 - INFO - Set Policy Dstintf
2025-08-02 10:31:42 - INFO - Set Policy Action
2025-08-02 10:31:42 - INFO - Set Policy Srcaddr
2025-08-02 10:31:42 - INFO - Set Policy Dstaddr
2025-08-02 10:31:42 - INFO - Set Policy Schedule
2025-08-02 10:31:42 - INFO - Set Policy Service
2025-08-02 10:31:42 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:42 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:42 - INFO - Set Policy Av Profile
2025-08-02 10:31:42 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:42 - INFO - Set Policy Logtraffic
2025-08-02 10:31:42 - INFO - Policy Parsing Complete
2025-08-02 10:31:42 - INFO - Parsing Policy
2025-08-02 10:31:42 - INFO - fortigate.set_policy_name
2025-08-02 10:31:42 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:42 - INFO - Set Policy Srcintf
2025-08-02 10:31:42 - INFO - Set Policy Dstintf
2025-08-02 10:31:42 - INFO - Set Policy Action
2025-08-02 10:31:42 - INFO - Set Policy Srcaddr
2025-08-02 10:31:42 - INFO - Set Policy Dstaddr
2025-08-02 10:31:42 - INFO - Set Policy Schedule
2025-08-02 10:31:42 - INFO - Set Policy Service
2025-08-02 10:31:42 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:42 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:42 - INFO - Set Policy Av Profile
2025-08-02 10:31:42 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:42 - INFO - Set Policy Logtraffic
2025-08-02 10:31:42 - INFO - Policy Parsing Complete
2025-08-02 10:31:42 - INFO - Parsing Policy
2025-08-02 10:31:42 - INFO - fortigate.set_policy_name
2025-08-02 10:31:42 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:42 - INFO - Set Policy Srcintf
2025-08-02 10:31:42 - INFO - Set Policy Dstintf
2025-08-02 10:31:42 - INFO - Set Policy Action
2025-08-02 10:31:42 - INFO - Set Policy Srcaddr
2025-08-02 10:31:42 - INFO - Set Policy Dstaddr
2025-08-02 10:31:42 - INFO - Set Policy Schedule
2025-08-02 10:31:42 - INFO - Set Policy Service
2025-08-02 10:31:42 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:42 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:42 - INFO - Set Policy Av Profile
2025-08-02 10:31:42 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:42 - INFO - Set Policy Logtraffic
2025-08-02 10:31:42 - INFO - Policy Parsing Complete
2025-08-02 10:31:42 - INFO - Parsing Policy
2025-08-02 10:31:42 - INFO - fortigate.set_policy_name
2025-08-02 10:31:42 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:42 - INFO - Set Policy Srcintf
2025-08-02 10:31:42 - INFO - Set Policy Dstintf
2025-08-02 10:31:42 - INFO - Set Policy Action
2025-08-02 10:31:42 - INFO - Set Policy Srcaddr
2025-08-02 10:31:42 - INFO - Set Policy Dstaddr
2025-08-02 10:31:42 - INFO - Set Policy Schedule
2025-08-02 10:31:42 - INFO - Set Policy Service
2025-08-02 10:31:42 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:42 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:42 - INFO - Set Policy Av Profile
2025-08-02 10:31:42 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:42 - INFO - Set Policy Logtraffic
2025-08-02 10:31:42 - INFO - Policy Parsing Complete
2025-08-02 10:31:42 - INFO - Parsing Policy
2025-08-02 10:31:42 - INFO - fortigate.set_policy_name
2025-08-02 10:31:42 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:42 - INFO - Set Policy Srcintf
2025-08-02 10:31:42 - INFO - Set Policy Dstintf
2025-08-02 10:31:42 - INFO - Set Policy Action
2025-08-02 10:31:42 - INFO - Set Policy Srcaddr
2025-08-02 10:31:42 - INFO - Set Policy Dstaddr
2025-08-02 10:31:42 - INFO - Set Policy Schedule
2025-08-02 10:31:42 - INFO - Set Policy Service
2025-08-02 10:31:42 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:42 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:42 - INFO - Set Policy Av Profile
2025-08-02 10:31:42 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:42 - INFO - Set Policy Logtraffic
2025-08-02 10:31:42 - INFO - Policy Parsing Complete
2025-08-02 10:31:42 - INFO - Parsing Policy
2025-08-02 10:31:42 - INFO - fortigate.set_policy_name
2025-08-02 10:31:42 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:42 - INFO - Set Policy Srcintf
2025-08-02 10:31:42 - INFO - Set Policy Dstintf
2025-08-02 10:31:42 - INFO - Set Policy Action
2025-08-02 10:31:42 - INFO - Set Policy Srcaddr
2025-08-02 10:31:42 - INFO - Set Policy Dstaddr
2025-08-02 10:31:42 - INFO - Set Policy Schedule
2025-08-02 10:31:42 - INFO - Set Policy Service
2025-08-02 10:31:42 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:42 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:42 - INFO - Set Policy Av Profile
2025-08-02 10:31:42 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:42 - INFO - Set Policy Logtraffic
2025-08-02 10:31:42 - INFO - Policy Parsing Complete
2025-08-02 10:31:42 - INFO - Parsing Policy
2025-08-02 10:31:42 - INFO - fortigate.set_policy_name
2025-08-02 10:31:42 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:42 - INFO - Set Policy Srcintf
2025-08-02 10:31:42 - INFO - Set Policy Dstintf
2025-08-02 10:31:42 - INFO - Set Policy Action
2025-08-02 10:31:42 - INFO - Set Policy Srcaddr
2025-08-02 10:31:42 - INFO - Set Policy Dstaddr
2025-08-02 10:31:42 - INFO - Set Policy Schedule
2025-08-02 10:31:42 - INFO - Set Policy Service
2025-08-02 10:31:42 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:42 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:42 - INFO - Set Policy Av Profile
2025-08-02 10:31:42 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:42 - INFO - Set Policy Logtraffic
2025-08-02 10:31:42 - INFO - Policy Parsing Complete
2025-08-02 10:31:42 - INFO - Parsing Policy
2025-08-02 10:31:42 - INFO - fortigate.set_policy_name
2025-08-02 10:31:42 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:42 - INFO - Set Policy Srcintf
2025-08-02 10:31:42 - INFO - Set Policy Dstintf
2025-08-02 10:31:42 - INFO - Set Policy Action
2025-08-02 10:31:42 - INFO - Set Policy Srcaddr
2025-08-02 10:31:42 - INFO - Set Policy Dstaddr
2025-08-02 10:31:42 - INFO - Set Policy Schedule
2025-08-02 10:31:42 - INFO - Set Policy Service
2025-08-02 10:31:42 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:42 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:42 - INFO - Set Policy Av Profile
2025-08-02 10:31:42 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:42 - INFO - Set Policy Logtraffic
2025-08-02 10:31:42 - INFO - Policy Parsing Complete
2025-08-02 10:31:42 - INFO - Parsing Policy
2025-08-02 10:31:42 - INFO - fortigate.set_policy_name
2025-08-02 10:31:42 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:42 - INFO - Set Policy Srcintf
2025-08-02 10:31:43 - INFO - Set Policy Dstintf
2025-08-02 10:31:43 - INFO - Set Policy Action
2025-08-02 10:31:43 - INFO - Set Policy Srcaddr
2025-08-02 10:31:43 - INFO - Set Policy Dstaddr
2025-08-02 10:31:43 - INFO - Set Policy Schedule
2025-08-02 10:31:43 - INFO - Set Policy Service
2025-08-02 10:31:43 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:43 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:43 - INFO - Set Policy Av Profile
2025-08-02 10:31:43 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:43 - INFO - Set Policy Logtraffic
2025-08-02 10:31:43 - INFO - Policy Parsing Complete
2025-08-02 10:31:43 - INFO - Parsing Policy
2025-08-02 10:31:43 - INFO - fortigate.set_policy_name
2025-08-02 10:31:43 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:43 - INFO - Set Policy Srcintf
2025-08-02 10:31:43 - INFO - Set Policy Dstintf
2025-08-02 10:31:43 - INFO - Set Policy Action
2025-08-02 10:31:43 - INFO - Set Policy Srcaddr
2025-08-02 10:31:43 - INFO - Set Policy Dstaddr
2025-08-02 10:31:43 - INFO - Set Policy Schedule
2025-08-02 10:31:43 - INFO - Set Policy Service
2025-08-02 10:31:43 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:43 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:43 - INFO - Set Policy Av Profile
2025-08-02 10:31:43 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:43 - INFO - Set Policy Logtraffic
2025-08-02 10:31:43 - INFO - Policy Parsing Complete
2025-08-02 10:31:43 - INFO - Parsing Policy
2025-08-02 10:31:43 - INFO - fortigate.set_policy_name
2025-08-02 10:31:43 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:43 - INFO - Set Policy Srcintf
2025-08-02 10:31:43 - INFO - Set Policy Dstintf
2025-08-02 10:31:43 - INFO - Set Policy Action
2025-08-02 10:31:43 - INFO - Set Policy Srcaddr
2025-08-02 10:31:43 - INFO - Set Policy Dstaddr
2025-08-02 10:31:43 - INFO - Set Policy Schedule
2025-08-02 10:31:43 - INFO - Set Policy Service
2025-08-02 10:31:43 - INFO - Set Policy Logtraffic
2025-08-02 10:31:43 - INFO - Policy Parsing Complete
2025-08-02 10:31:43 - INFO - Parsing Policy
2025-08-02 10:31:43 - INFO - fortigate.set_policy_name
2025-08-02 10:31:43 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:43 - INFO - Set Policy Srcintf
2025-08-02 10:31:43 - INFO - Set Policy Dstintf
2025-08-02 10:31:43 - INFO - Set Policy Action
2025-08-02 10:31:43 - INFO - Set Policy Srcaddr
2025-08-02 10:31:43 - INFO - Set Policy Dstaddr
2025-08-02 10:31:43 - INFO - Set Policy Schedule
2025-08-02 10:31:43 - INFO - Set Policy Service
2025-08-02 10:31:43 - INFO - Set Policy Logtraffic
2025-08-02 10:31:43 - INFO - Policy Parsing Complete
2025-08-02 10:31:43 - INFO - Parsing Policy
2025-08-02 10:31:43 - INFO - fortigate.set_policy_name
2025-08-02 10:31:43 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:43 - INFO - Set Policy Srcintf
2025-08-02 10:31:43 - INFO - Set Policy Dstintf
2025-08-02 10:31:43 - INFO - Set Policy Action
2025-08-02 10:31:43 - INFO - Set Policy Srcaddr
2025-08-02 10:31:43 - INFO - Set Policy Dstaddr
2025-08-02 10:31:43 - INFO - Set Policy Schedule
2025-08-02 10:31:43 - INFO - Set Policy Service
2025-08-02 10:31:43 - INFO - Set Policy Logtraffic
2025-08-02 10:31:43 - INFO - Policy Parsing Complete
2025-08-02 10:31:43 - INFO - Parsing Policy
2025-08-02 10:31:43 - INFO - fortigate.set_policy_name
2025-08-02 10:31:43 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:43 - INFO - Set Policy Srcintf
2025-08-02 10:31:43 - INFO - Set Policy Dstintf
2025-08-02 10:31:43 - INFO - Set Policy Action
2025-08-02 10:31:43 - INFO - Set Policy Srcaddr
2025-08-02 10:31:43 - INFO - Set Policy Dstaddr
2025-08-02 10:31:43 - INFO - Set Policy Schedule
2025-08-02 10:31:43 - INFO - Set Policy Service
2025-08-02 10:31:43 - INFO - Set Policy Logtraffic
2025-08-02 10:31:43 - INFO - Policy Parsing Complete
2025-08-02 10:31:43 - INFO - Parsing Policy
2025-08-02 10:31:43 - INFO - fortigate.set_policy_name
2025-08-02 10:31:43 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:43 - INFO - Set Policy Srcintf
2025-08-02 10:31:43 - INFO - Set Policy Dstintf
2025-08-02 10:31:43 - INFO - Set Policy Action
2025-08-02 10:31:43 - INFO - Set Policy Srcaddr
2025-08-02 10:31:43 - INFO - Set Policy Dstaddr
2025-08-02 10:31:43 - INFO - Set Policy Schedule
2025-08-02 10:31:43 - INFO - Set Policy Service
2025-08-02 10:31:43 - INFO - Set Policy Logtraffic
2025-08-02 10:31:43 - INFO - Policy Parsing Complete
2025-08-02 10:31:43 - INFO - Parsing Policy
2025-08-02 10:31:43 - INFO - fortigate.set_policy_name
2025-08-02 10:31:43 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:43 - INFO - Set Policy Srcintf
2025-08-02 10:31:43 - INFO - Set Policy Dstintf
2025-08-02 10:31:43 - INFO - Set Policy Action
2025-08-02 10:31:43 - INFO - Set Policy Srcaddr
2025-08-02 10:31:43 - INFO - Set Policy Dstaddr
2025-08-02 10:31:43 - INFO - Set Policy Schedule
2025-08-02 10:31:43 - INFO - Set Policy Service
2025-08-02 10:31:43 - INFO - Set Policy Logtraffic
2025-08-02 10:31:43 - INFO - Policy Parsing Complete
2025-08-02 10:31:43 - INFO - Parsing Policy
2025-08-02 10:31:43 - INFO - fortigate.set_policy_name
2025-08-02 10:31:43 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:43 - INFO - Set Policy Srcintf
2025-08-02 10:31:43 - INFO - Set Policy Dstintf
2025-08-02 10:31:43 - INFO - Set Policy Action
2025-08-02 10:31:43 - INFO - Set Policy Srcaddr
2025-08-02 10:31:43 - INFO - Set Policy Dstaddr
2025-08-02 10:31:43 - INFO - Set Policy Schedule
2025-08-02 10:31:43 - INFO - Set Policy Service
2025-08-02 10:31:43 - INFO - Set Policy Logtraffic
2025-08-02 10:31:43 - INFO - Set Policy Comments
2025-08-02 10:31:43 - INFO - Policy Parsing Complete
2025-08-02 10:31:43 - INFO - Parsing Policy
2025-08-02 10:31:43 - INFO - fortigate.set_policy_name
2025-08-02 10:31:43 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:43 - INFO - Set Policy Srcintf
2025-08-02 10:31:43 - INFO - Set Policy Dstintf
2025-08-02 10:31:43 - INFO - Set Policy Action
2025-08-02 10:31:43 - INFO - Set Policy Srcaddr
2025-08-02 10:31:43 - INFO - Set Policy Dstaddr
2025-08-02 10:31:43 - INFO - Set Policy Schedule
2025-08-02 10:31:43 - INFO - Set Policy Service
2025-08-02 10:31:43 - INFO - Set Policy Logtraffic
2025-08-02 10:31:43 - INFO - Set Policy Comments
2025-08-02 10:31:43 - INFO - Policy Parsing Complete
2025-08-02 10:31:43 - INFO - Parsing Policy
2025-08-02 10:31:43 - INFO - fortigate.set_policy_name
2025-08-02 10:31:43 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:43 - INFO - Set Policy Srcintf
2025-08-02 10:31:43 - INFO - Set Policy Dstintf
2025-08-02 10:31:43 - INFO - Set Policy Action
2025-08-02 10:31:43 - INFO - Set Policy Srcaddr
2025-08-02 10:31:43 - INFO - Set Policy Dstaddr
2025-08-02 10:31:43 - INFO - Set Policy Schedule
2025-08-02 10:31:43 - INFO - Set Policy Service
2025-08-02 10:31:43 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:43 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:43 - INFO - Set Policy Av Profile
2025-08-02 10:31:43 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:43 - INFO - Set Policy Logtraffic
2025-08-02 10:31:43 - INFO - Policy Parsing Complete
2025-08-02 10:31:43 - INFO - Parsing Policy
2025-08-02 10:31:43 - INFO - fortigate.set_policy_name
2025-08-02 10:31:43 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:43 - INFO - Set Policy Srcintf
2025-08-02 10:31:43 - INFO - Set Policy Dstintf
2025-08-02 10:31:43 - INFO - Set Policy Action
2025-08-02 10:31:43 - INFO - Set Policy Srcaddr
2025-08-02 10:31:43 - INFO - Set Policy Dstaddr
2025-08-02 10:31:43 - INFO - Set Policy Schedule
2025-08-02 10:31:43 - INFO - Set Policy Service
2025-08-02 10:31:43 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:43 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:43 - INFO - Set Policy Av Profile
2025-08-02 10:31:43 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:43 - INFO - Set Policy Logtraffic
2025-08-02 10:31:43 - INFO - Policy Parsing Complete
2025-08-02 10:31:43 - INFO - Parsing Policy
2025-08-02 10:31:43 - INFO - fortigate.set_policy_name
2025-08-02 10:31:43 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:43 - INFO - Set Policy Srcintf
2025-08-02 10:31:43 - INFO - Set Policy Dstintf
2025-08-02 10:31:43 - INFO - Set Policy Srcaddr
2025-08-02 10:31:43 - INFO - Set Policy Dstaddr
2025-08-02 10:31:43 - INFO - Set Policy Schedule
2025-08-02 10:31:43 - INFO - Set Policy Service
2025-08-02 10:31:43 - INFO - Set Policy Logtraffic
2025-08-02 10:31:43 - WARNING - Policy Missing Fields
2025-08-02 10:31:43 - INFO - Policy Parsing Complete
2025-08-02 10:31:43 - INFO - Parsing Policy
2025-08-02 10:31:43 - INFO - fortigate.set_policy_name
2025-08-02 10:31:43 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:43 - INFO - Set Policy Srcintf
2025-08-02 10:31:43 - INFO - Set Policy Dstintf
2025-08-02 10:31:43 - INFO - Set Policy Action
2025-08-02 10:31:43 - INFO - Set Policy Srcaddr
2025-08-02 10:31:43 - INFO - Set Policy Dstaddr
2025-08-02 10:31:43 - INFO - Set Policy Schedule
2025-08-02 10:31:43 - INFO - Set Policy Service
2025-08-02 10:31:43 - INFO - Set Policy Logtraffic
2025-08-02 10:31:43 - INFO - Set Policy Nat
2025-08-02 10:31:43 - INFO - Set Policy Ippool
2025-08-02 10:31:43 - INFO - fortigate.set_policy_poolname
2025-08-02 10:31:43 - INFO - Policy Parsing Complete
2025-08-02 10:31:43 - INFO - Parsing Policy
2025-08-02 10:31:43 - INFO - fortigate.set_policy_name
2025-08-02 10:31:43 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:43 - INFO - Set Policy Srcintf
2025-08-02 10:31:43 - INFO - Set Policy Dstintf
2025-08-02 10:31:43 - INFO - Set Policy Srcaddr
2025-08-02 10:31:43 - INFO - Set Policy Dstaddr
2025-08-02 10:31:43 - INFO - Set Policy Schedule
2025-08-02 10:31:43 - INFO - Set Policy Service
2025-08-02 10:31:43 - INFO - Set Policy Logtraffic
2025-08-02 10:31:43 - WARNING - Policy Missing Fields
2025-08-02 10:31:43 - INFO - Policy Parsing Complete
2025-08-02 10:31:43 - INFO - Parsing Policy
2025-08-02 10:31:43 - INFO - fortigate.set_policy_name
2025-08-02 10:31:43 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:43 - INFO - Set Policy Srcintf
2025-08-02 10:31:43 - INFO - Set Policy Dstintf
2025-08-02 10:31:43 - INFO - Set Policy Action
2025-08-02 10:31:43 - INFO - Set Policy Srcaddr
2025-08-02 10:31:43 - INFO - Set Policy Dstaddr
2025-08-02 10:31:43 - INFO - Set Policy Schedule
2025-08-02 10:31:43 - INFO - Set Policy Service
2025-08-02 10:31:43 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:43 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:43 - INFO - Set Policy Av Profile
2025-08-02 10:31:43 - INFO - Set Policy Webfilter Profile
2025-08-02 10:31:43 - INFO - fortigate.set_policy_file_filter_profile
2025-08-02 10:31:43 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:43 - INFO - Set Policy Application List
2025-08-02 10:31:43 - INFO - Set Policy Nat
2025-08-02 10:31:43 - INFO - Set Policy Ippool
2025-08-02 10:31:43 - INFO - fortigate.set_policy_poolname
2025-08-02 10:31:43 - INFO - 设置策略用户组: ['VPN_USERS']
2025-08-02 10:31:43 - INFO - Policy Parsing Complete
2025-08-02 10:31:43 - INFO - Parsing Policy
2025-08-02 10:31:43 - INFO - fortigate.set_policy_name
2025-08-02 10:31:43 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:43 - INFO - Set Policy Srcintf
2025-08-02 10:31:43 - INFO - Set Policy Dstintf
2025-08-02 10:31:43 - INFO - Set Policy Action
2025-08-02 10:31:43 - INFO - Set Policy Srcaddr
2025-08-02 10:31:43 - INFO - Set Policy Dstaddr
2025-08-02 10:31:43 - INFO - Set Policy Schedule
2025-08-02 10:31:43 - INFO - Set Policy Service
2025-08-02 10:31:43 - INFO - Set Policy Logtraffic
2025-08-02 10:31:43 - INFO - 设置策略用户: ['aryom']
2025-08-02 10:31:43 - INFO - Policy Parsing Complete
2025-08-02 10:31:43 - INFO - Parsing Policy
2025-08-02 10:31:43 - INFO - fortigate.set_policy_name
2025-08-02 10:31:43 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:43 - INFO - Set Policy Srcintf
2025-08-02 10:31:43 - INFO - Set Policy Dstintf
2025-08-02 10:31:43 - INFO - Set Policy Action
2025-08-02 10:31:43 - INFO - Set Policy Srcaddr
2025-08-02 10:31:43 - INFO - Set Policy Dstaddr
2025-08-02 10:31:43 - INFO - Set Policy Schedule
2025-08-02 10:31:43 - INFO - Set Policy Service
2025-08-02 10:31:43 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:43 - INFO - Set Policy Logtraffic
2025-08-02 10:31:43 - INFO - 设置策略用户: ['derenunal', 'hakantoptas', 'metin']
2025-08-02 10:31:43 - INFO - Policy Parsing Complete
2025-08-02 10:31:43 - INFO - Parsing Policy
2025-08-02 10:31:43 - INFO - fortigate.set_policy_name
2025-08-02 10:31:43 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:43 - INFO - Set Policy Srcintf
2025-08-02 10:31:43 - INFO - Set Policy Dstintf
2025-08-02 10:31:43 - INFO - Set Policy Action
2025-08-02 10:31:43 - INFO - Set Policy Srcaddr
2025-08-02 10:31:43 - INFO - Set Policy Dstaddr
2025-08-02 10:31:43 - INFO - Set Policy Schedule
2025-08-02 10:31:43 - INFO - Set Policy Service
2025-08-02 10:31:43 - INFO - 设置策略用户: ['derenunal', 'hakantoptas', 'metin']
2025-08-02 10:31:43 - INFO - Policy Parsing Complete
2025-08-02 10:31:43 - INFO - Parsing Policy
2025-08-02 10:31:43 - INFO - fortigate.set_policy_name
2025-08-02 10:31:43 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:43 - INFO - Set Policy Srcintf
2025-08-02 10:31:43 - INFO - Set Policy Dstintf
2025-08-02 10:31:43 - INFO - Set Policy Action
2025-08-02 10:31:43 - INFO - Set Policy Srcaddr
2025-08-02 10:31:43 - INFO - Set Policy Dstaddr
2025-08-02 10:31:43 - INFO - Set Policy Schedule
2025-08-02 10:31:43 - INFO - Set Policy Service
2025-08-02 10:31:43 - INFO - 设置策略用户: ['mvural']
2025-08-02 10:31:43 - INFO - Policy Parsing Complete
2025-08-02 10:31:43 - INFO - Parsing Policy
2025-08-02 10:31:43 - INFO - fortigate.set_policy_name
2025-08-02 10:31:43 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:43 - INFO - Set Policy Srcintf
2025-08-02 10:31:43 - INFO - Set Policy Dstintf
2025-08-02 10:31:43 - INFO - Set Policy Action
2025-08-02 10:31:43 - INFO - Set Policy Srcaddr
2025-08-02 10:31:43 - INFO - Set Policy Dstaddr
2025-08-02 10:31:43 - INFO - Set Policy Schedule
2025-08-02 10:31:43 - INFO - Set Policy Service
2025-08-02 10:31:43 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:43 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:43 - INFO - Set Policy Av Profile
2025-08-02 10:31:43 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:43 - INFO - 设置策略用户组: ['YetenekSinavi_YOS']
2025-08-02 10:31:43 - INFO - 设置策略用户: ['hucuzal']
2025-08-02 10:31:43 - INFO - Policy Parsing Complete
2025-08-02 10:31:43 - INFO - Parsing Policy
2025-08-02 10:31:43 - INFO - fortigate.set_policy_name
2025-08-02 10:31:43 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:43 - INFO - Set Policy Srcintf
2025-08-02 10:31:43 - INFO - Set Policy Dstintf
2025-08-02 10:31:43 - INFO - Set Policy Action
2025-08-02 10:31:43 - INFO - Set Policy Srcaddr
2025-08-02 10:31:43 - INFO - Set Policy Dstaddr
2025-08-02 10:31:43 - INFO - Set Policy Schedule
2025-08-02 10:31:43 - INFO - Set Policy Service
2025-08-02 10:31:43 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:43 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:43 - INFO - Set Policy Av Profile
2025-08-02 10:31:43 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:43 - INFO - 设置策略用户组: ['bingol_uni']
2025-08-02 10:31:43 - INFO - 设置策略用户: ['mvural']
2025-08-02 10:31:43 - INFO - Policy Parsing Complete
2025-08-02 10:31:43 - INFO - Parsing Policy
2025-08-02 10:31:43 - INFO - fortigate.set_policy_name
2025-08-02 10:31:43 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:43 - INFO - Set Policy Srcintf
2025-08-02 10:31:43 - INFO - Set Policy Dstintf
2025-08-02 10:31:43 - INFO - Set Policy Action
2025-08-02 10:31:43 - INFO - Set Policy Srcaddr
2025-08-02 10:31:43 - INFO - Set Policy Dstaddr
2025-08-02 10:31:43 - INFO - Set Policy Schedule
2025-08-02 10:31:43 - INFO - Set Policy Service
2025-08-02 10:31:43 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:43 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:43 - INFO - Set Policy Av Profile
2025-08-02 10:31:43 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:43 - INFO - 设置策略用户组: ['VPN_KIMLIK']
2025-08-02 10:31:43 - INFO - 设置策略用户: ['makyar']
2025-08-02 10:31:43 - INFO - Policy Parsing Complete
2025-08-02 10:31:43 - INFO - Parsing Policy
2025-08-02 10:31:43 - INFO - fortigate.set_policy_name
2025-08-02 10:31:43 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:43 - INFO - Set Policy Srcintf
2025-08-02 10:31:43 - INFO - Set Policy Dstintf
2025-08-02 10:31:43 - INFO - Set Policy Action
2025-08-02 10:31:43 - INFO - Set Policy Srcaddr
2025-08-02 10:31:43 - INFO - Set Policy Dstaddr
2025-08-02 10:31:43 - INFO - Set Policy Schedule
2025-08-02 10:31:43 - INFO - Set Policy Service
2025-08-02 10:31:43 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:43 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:43 - INFO - Set Policy Av Profile
2025-08-02 10:31:43 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:43 - INFO - 设置策略用户组: ['VPN_EKDERS']
2025-08-02 10:31:43 - INFO - 设置策略用户: ['mcitil']
2025-08-02 10:31:43 - INFO - Policy Parsing Complete
2025-08-02 10:31:43 - INFO - Parsing Policy
2025-08-02 10:31:43 - INFO - fortigate.set_policy_name
2025-08-02 10:31:43 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:43 - INFO - Set Policy Srcintf
2025-08-02 10:31:43 - INFO - Set Policy Dstintf
2025-08-02 10:31:43 - INFO - Set Policy Action
2025-08-02 10:31:43 - INFO - Set Policy Srcaddr
2025-08-02 10:31:43 - INFO - Set Policy Dstaddr
2025-08-02 10:31:43 - INFO - Set Policy Schedule
2025-08-02 10:31:43 - INFO - Set Policy Service
2025-08-02 10:31:43 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:43 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:43 - INFO - Set Policy Av Profile
2025-08-02 10:31:43 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:43 - INFO - 设置策略用户: ['aryom']
2025-08-02 10:31:43 - INFO - Set Policy Comments
2025-08-02 10:31:43 - INFO - Policy Parsing Complete
2025-08-02 10:31:43 - INFO - Parsing Policy
2025-08-02 10:31:43 - INFO - fortigate.set_policy_name
2025-08-02 10:31:43 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:43 - INFO - Set Policy Srcintf
2025-08-02 10:31:43 - INFO - Set Policy Dstintf
2025-08-02 10:31:43 - INFO - Set Policy Action
2025-08-02 10:31:43 - INFO - Set Policy Srcaddr
2025-08-02 10:31:43 - INFO - Set Policy Dstaddr
2025-08-02 10:31:43 - INFO - Set Policy Schedule
2025-08-02 10:31:43 - INFO - Set Policy Service
2025-08-02 10:31:43 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:43 - INFO - Set Policy Av Profile
2025-08-02 10:31:43 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:43 - INFO - 设置策略用户组: ['VPN_WEB']
2025-08-02 10:31:43 - INFO - 设置策略用户: ['hguler']
2025-08-02 10:31:43 - INFO - Policy Parsing Complete
2025-08-02 10:31:43 - INFO - Parsing Policy
2025-08-02 10:31:43 - INFO - fortigate.set_policy_name
2025-08-02 10:31:43 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:43 - INFO - Set Policy Srcintf
2025-08-02 10:31:43 - INFO - Set Policy Dstintf
2025-08-02 10:31:43 - INFO - Set Policy Action
2025-08-02 10:31:43 - INFO - Set Policy Srcaddr
2025-08-02 10:31:43 - INFO - Set Policy Dstaddr
2025-08-02 10:31:43 - INFO - Set Policy Schedule
2025-08-02 10:31:43 - INFO - Set Policy Service
2025-08-02 10:31:43 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:43 - INFO - Set Policy Av Profile
2025-08-02 10:31:43 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:43 - INFO - 设置策略用户: ['osmantipbirligi']
2025-08-02 10:31:43 - INFO - Policy Parsing Complete
2025-08-02 10:31:43 - INFO - Parsing Policy
2025-08-02 10:31:43 - INFO - fortigate.set_policy_name
2025-08-02 10:31:43 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:43 - INFO - Set Policy Srcintf
2025-08-02 10:31:43 - INFO - Set Policy Dstintf
2025-08-02 10:31:43 - INFO - Set Policy Action
2025-08-02 10:31:43 - INFO - Set Policy Srcaddr
2025-08-02 10:31:43 - INFO - Set Policy Dstaddr
2025-08-02 10:31:43 - INFO - Set Policy Schedule
2025-08-02 10:31:43 - INFO - Set Policy Service
2025-08-02 10:31:43 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:43 - INFO - Set Policy Av Profile
2025-08-02 10:31:43 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:43 - INFO - 设置策略用户组: ['VPN_NOMYSEM']
2025-08-02 10:31:43 - INFO - 设置策略用户: ['ahoso']
2025-08-02 10:31:43 - INFO - Policy Parsing Complete
2025-08-02 10:31:43 - INFO - Parsing Policy
2025-08-02 10:31:43 - INFO - fortigate.set_policy_name
2025-08-02 10:31:43 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:43 - INFO - Set Policy Srcintf
2025-08-02 10:31:43 - INFO - Set Policy Dstintf
2025-08-02 10:31:43 - INFO - Set Policy Action
2025-08-02 10:31:43 - INFO - Set Policy Srcaddr
2025-08-02 10:31:43 - INFO - Set Policy Dstaddr
2025-08-02 10:31:43 - INFO - Set Policy Schedule
2025-08-02 10:31:43 - INFO - Set Policy Service
2025-08-02 10:31:43 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:43 - INFO - Set Policy Av Profile
2025-08-02 10:31:43 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:43 - INFO - 设置策略用户组: ['VPN_AKD_TSV']
2025-08-02 10:31:43 - INFO - 设置策略用户: ['hdilmen']
2025-08-02 10:31:43 - INFO - Policy Parsing Complete
2025-08-02 10:31:43 - INFO - Parsing Policy
2025-08-02 10:31:43 - INFO - fortigate.set_policy_name
2025-08-02 10:31:43 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:43 - INFO - Set Policy Srcintf
2025-08-02 10:31:43 - INFO - Set Policy Dstintf
2025-08-02 10:31:43 - INFO - Set Policy Action
2025-08-02 10:31:43 - INFO - Set Policy Srcaddr
2025-08-02 10:31:43 - INFO - Set Policy Dstaddr
2025-08-02 10:31:43 - INFO - Set Policy Schedule
2025-08-02 10:31:43 - INFO - Set Policy Service
2025-08-02 10:31:43 - INFO - Set Policy Logtraffic
2025-08-02 10:31:43 - INFO - Policy Parsing Complete
2025-08-02 10:31:43 - INFO - Parsing Policy
2025-08-02 10:31:43 - INFO - fortigate.set_policy_name
2025-08-02 10:31:43 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:43 - INFO - Set Policy Srcintf
2025-08-02 10:31:43 - INFO - Set Policy Dstintf
2025-08-02 10:31:43 - INFO - Set Policy Action
2025-08-02 10:31:43 - INFO - Set Policy Srcaddr
2025-08-02 10:31:43 - INFO - Set Policy Dstaddr
2025-08-02 10:31:43 - INFO - Set Policy Schedule
2025-08-02 10:31:43 - INFO - Set Policy Service
2025-08-02 10:31:43 - INFO - Set Policy Logtraffic
2025-08-02 10:31:43 - INFO - Policy Parsing Complete
2025-08-02 10:31:43 - INFO - Parsing Policy
2025-08-02 10:31:43 - INFO - fortigate.set_policy_name
2025-08-02 10:31:43 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:43 - INFO - Set Policy Srcintf
2025-08-02 10:31:43 - INFO - Set Policy Dstintf
2025-08-02 10:31:43 - INFO - Set Policy Action
2025-08-02 10:31:43 - INFO - Set Policy Srcaddr
2025-08-02 10:31:43 - INFO - Set Policy Dstaddr
2025-08-02 10:31:43 - INFO - Set Policy Schedule
2025-08-02 10:31:43 - INFO - Set Policy Service
2025-08-02 10:31:43 - INFO - Policy Parsing Complete
2025-08-02 10:31:43 - INFO - Parsing Policy
2025-08-02 10:31:43 - INFO - fortigate.set_policy_name
2025-08-02 10:31:43 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:43 - INFO - Set Policy Srcintf
2025-08-02 10:31:44 - INFO - Set Policy Dstintf
2025-08-02 10:31:44 - INFO - Set Policy Action
2025-08-02 10:31:44 - INFO - Set Policy Srcaddr
2025-08-02 10:31:44 - INFO - Set Policy Dstaddr
2025-08-02 10:31:44 - INFO - Set Policy Schedule
2025-08-02 10:31:44 - INFO - Set Policy Service
2025-08-02 10:31:44 - INFO - Set Policy Logtraffic
2025-08-02 10:31:44 - INFO - Policy Parsing Complete
2025-08-02 10:31:44 - INFO - Parsing Policy
2025-08-02 10:31:44 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:44 - INFO - Set Policy Srcintf
2025-08-02 10:31:44 - INFO - Set Policy Dstintf
2025-08-02 10:31:44 - INFO - Set Policy Action
2025-08-02 10:31:44 - INFO - Set Policy Srcaddr
2025-08-02 10:31:44 - INFO - Set Policy Dstaddr
2025-08-02 10:31:44 - INFO - Set Policy Schedule
2025-08-02 10:31:44 - INFO - Set Policy Service
2025-08-02 10:31:44 - INFO - Set Policy Logtraffic
2025-08-02 10:31:44 - INFO - Set Policy Comments
2025-08-02 10:31:44 - INFO - Policy Parsing Complete
2025-08-02 10:31:44 - INFO - Parsing Policy
2025-08-02 10:31:44 - INFO - fortigate.set_policy_name
2025-08-02 10:31:44 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:44 - INFO - Set Policy Srcintf
2025-08-02 10:31:44 - INFO - Set Policy Dstintf
2025-08-02 10:31:44 - INFO - Set Policy Action
2025-08-02 10:31:44 - INFO - Set Policy Srcaddr
2025-08-02 10:31:44 - INFO - Set Policy Dstaddr
2025-08-02 10:31:44 - INFO - Set Policy Schedule
2025-08-02 10:31:44 - INFO - Set Policy Service
2025-08-02 10:31:44 - INFO - Set Policy Logtraffic
2025-08-02 10:31:44 - INFO - Policy Parsing Complete
2025-08-02 10:31:44 - INFO - Parsing Policy
2025-08-02 10:31:44 - INFO - fortigate.set_policy_name
2025-08-02 10:31:44 - INFO - fortigate.set_policy_uuid
2025-08-02 10:31:44 - INFO - Set Policy Srcintf
2025-08-02 10:31:44 - INFO - Set Policy Dstintf
2025-08-02 10:31:44 - INFO - Set Policy Action
2025-08-02 10:31:44 - INFO - Set Policy Srcaddr
2025-08-02 10:31:44 - INFO - Set Policy Dstaddr
2025-08-02 10:31:44 - INFO - Set Policy Schedule
2025-08-02 10:31:44 - INFO - Set Policy Service
2025-08-02 10:31:44 - INFO - 设置策略UTM状态: enable
2025-08-02 10:31:44 - INFO - Set Policy Ssl Ssh Profile
2025-08-02 10:31:44 - INFO - Set Policy Av Profile
2025-08-02 10:31:44 - INFO - Set Policy Ips Sensor
2025-08-02 10:31:44 - INFO - Policy Parsing Complete
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 开始解析静态路由配置部分
2025-08-02 10:31:44 - INFO - 静态路由网关: 193.140.0.61
2025-08-02 10:31:44 - INFO - 静态路由出接口: x1
2025-08-02 10:31:44 - INFO - 静态路由 1 缺少目标网络，将使用默认值: 0.0.0.0/0
2025-08-02 10:31:44 - WARNING - 消息中缺少参数: 'id': '1', 'gateway': '193.140.0.61', 'device': 'x1', 'destination': '0.0.0.0/0'
2025-08-02 10:31:44 - WARNING - 消息中缺少参数: 'id'
2025-08-02 10:31:44 - INFO - 静态路由目标网络: 10.117.100.0/24
2025-08-02 10:31:44 - INFO - 静态路由网关: 10.117.0.4
2025-08-02 10:31:44 - INFO - 静态路由出接口: DARENDE_MYO
2025-08-02 10:31:44 - WARNING - 消息中缺少参数: 'id': '2', 'destination': '10.117.100.0/24', 'gateway': '10.117.0.4', 'device': 'DARENDE_MYO'
2025-08-02 10:31:44 - WARNING - 消息中缺少参数: 'id'
2025-08-02 10:31:44 - INFO - 静态路由目标网络: 10.119.100.0/24
2025-08-02 10:31:44 - INFO - 静态路由网关: 10.119.0.1
2025-08-02 10:31:44 - INFO - 静态路由出接口: HEKIMHAN_MYO
2025-08-02 10:31:44 - WARNING - 消息中缺少参数: 'id': '3', 'destination': '10.119.100.0/24', 'gateway': '10.119.0.1', 'device': 'HEKIMHAN_MYO'
2025-08-02 10:31:44 - WARNING - 消息中缺少参数: 'id'
2025-08-02 10:31:44 - INFO - 静态路由目标网络: 10.112.100.0/24
2025-08-02 10:31:44 - INFO - 静态路由网关: 10.112.0.1
2025-08-02 10:31:44 - INFO - 静态路由出接口: ARAPGIR_MYO
2025-08-02 10:31:44 - WARNING - 消息中缺少参数: 'id': '5', 'destination': '10.112.100.0/24', 'gateway': '10.112.0.1', 'device': 'ARAPGIR_MYO'
2025-08-02 10:31:44 - WARNING - 消息中缺少参数: 'id'
2025-08-02 10:31:44 - INFO - 静态路由目标网络: 10.110.100.0/24
2025-08-02 10:31:44 - INFO - 静态路由网关: 10.110.0.1
2025-08-02 10:31:44 - INFO - 静态路由出接口: KALE_MYO
2025-08-02 10:31:44 - WARNING - 消息中缺少参数: 'id': '6', 'destination': '10.110.100.0/24', 'gateway': '10.110.0.1', 'device': 'KALE_MYO'
2025-08-02 10:31:44 - WARNING - 消息中缺少参数: 'id'
2025-08-02 10:31:44 - INFO - 静态路由目标网络: 10.100.100.0/23
2025-08-02 10:31:44 - INFO - 静态路由网关: 10.128.0.1
2025-08-02 10:31:44 - INFO - 静态路由出接口: YESILTURT_RKT
2025-08-02 10:31:44 - WARNING - 消息中缺少参数: 'id': '7', 'destination': '10.100.100.0/23', 'gateway': '10.128.0.1', 'device': 'YESILTURT_RKT'
2025-08-02 10:31:44 - WARNING - 消息中缺少参数: 'id'
2025-08-02 10:31:44 - INFO - 静态路由目标网络: 10.120.100.0/24
2025-08-02 10:31:44 - INFO - 静态路由网关: 10.120.0.1
2025-08-02 10:31:44 - INFO - 静态路由出接口: AKCADAG_MYO
2025-08-02 10:31:44 - WARNING - 消息中缺少参数: 'id': '8', 'destination': '10.120.100.0/24', 'gateway': '10.120.0.1', 'device': 'AKCADAG_MYO'
2025-08-02 10:31:44 - WARNING - 消息中缺少参数: 'id'
2025-08-02 10:31:44 - INFO - 静态路由网关: 10.51.212.1
2025-08-02 10:31:44 - INFO - 静态路由管理距离: 1
2025-08-02 10:31:44 - INFO - 静态路由出接口: mgmt
2025-08-02 10:31:44 - INFO - 静态路由 9 缺少目标网络，将使用默认值: 0.0.0.0/0
2025-08-02 10:31:44 - WARNING - 消息中缺少参数: 'id': '9', 'gateway': '10.51.212.1', 'distance': '1', 'device': 'mgmt', 'destination': '0.0.0.0/0'
2025-08-02 10:31:44 - WARNING - 消息中缺少参数: 'id'
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - 未知 配置部分结束
2025-08-02 10:31:44 - INFO - DNS配置处理完成，服务器数量: 2，DNS over TLS: 未设置
2025-08-02 10:31:44 - INFO - 解析完成，找到 59 个接口, 8 个静态路由, 4 个区域, 512 个地址对象, 31 个地址组, 122 个服务对象, 6 个服务组
2025-08-02 10:31:44 - INFO - 解析完成，找到 59 个接口, 8 个静态路由, 4 个区域, 512 个地址对象, 31 个地址组, 122 个服务对象, 6 个服务组
2025-08-02 10:31:44 - INFO - Interface mapping loaded
2025-08-02 10:31:44 - WARNING - 消息中缺少参数: 'mgmt': 'Ge0/0', 'ha': 'Ge0/9', 'port1': 'Ge0/3', 'port2': 'Ge0/4', 'port3': 'Ge0/5', 'port4': 'Ge0/6', 'port5': 'Ge0/7', 'port6': 'Ge0/8', 'port7': 'Ge0/10', 'port8': 'Ge0/11', 'port9': 'Ge0/12', 'port10': 'Ge0/13', 'port11': 'Ge0/14', 'port12': 'Ge0/15', 'port13': 'Ge0/16', 'port14': 'Ge0/17', 'port15': 'Ge0/18', 'port16': 'Ge0/19', 'port17': 'Ge0/20', 'port18': 'Ge0/21', 'port19': 'Ge0/22', 'port20': 'Ge0/23', 'port21': 'Ge0/24', 'port22': 'Ge0/25', 'port23': 'Ge0/1', 'port24': 'Ge0/2', 'x1': 'TenGe0/0', 'x2': 'TenGe0/1', 'x3': 'TenGe0/2', 'x4': 'TenGe0/3', 'modem': 'Ge0/26', 'naf.root': 'Ge0/27', 'l2t.root': 'Ge0/28', 'ssl.root': 'Ge0/29', 'BOS': 'Ge0/30', 'SSL_VPN': 'Ge0/31'
2025-08-02 10:31:44 - WARNING - 消息中缺少参数: 'mgmt'
2025-08-02 10:31:44 - INFO - 加载的接口映射: {'mgmt': 'Ge0/0', 'ha': 'Ge0/9', 'port1': 'Ge0/3', 'port2': 'Ge0/4', 'port3': 'Ge0/5', 'port4': 'Ge0/6', 'port5': 'Ge0/7', 'port6': 'Ge0/8', 'port7': 'Ge0/10', 'port8': 'Ge0/11', 'port9': 'Ge0/12', 'port10': 'Ge0/13', 'port11': 'Ge0/14', 'port12': 'Ge0/15', 'port13': 'Ge0/16', 'port14': 'Ge0/17', 'port15': 'Ge0/18', 'port16': 'Ge0/19', 'port17': 'Ge0/20', 'port18': 'Ge0/21', 'port19': 'Ge0/22', 'port20': 'Ge0/23', 'port21': 'Ge0/24', 'port22': 'Ge0/25', 'port23': 'Ge0/1', 'port24': 'Ge0/2', 'x1': 'TenGe0/0', 'x2': 'TenGe0/1', 'x3': 'TenGe0/2', 'x4': 'TenGe0/3', 'modem': 'Ge0/26', 'naf.root': 'Ge0/27', 'l2t.root': 'Ge0/28', 'ssl.root': 'Ge0/29', 'BOS': 'Ge0/30', 'SSL_VPN': 'Ge0/31'}
2025-08-02 10:31:44 - INFO - 配置管理器：检测到开发环境
2025-08-02 10:31:44 - INFO - 配置管理器已初始化
2025-08-02 10:31:44 - INFO - YANG管理器已初始化
2025-08-02 10:31:44 - INFO - 验证服务已初始化
2025-08-02 10:31:44 - INFO - PhysicalInterfaceHandler初始化 - operation_mode_result为空
2025-08-02 10:31:44 - INFO - PhysicalInterfaceHandler初始化 - operation_mode_result为空
2025-08-02 10:31:44 - INFO - PhysicalInterfaceHandler初始化 - operation_mode_result为空
2025-08-02 10:31:44 - INFO - NTOS内置服务已加载: 82 个
2025-08-02 10:31:44 - INFO - 厂商映射已加载: fortigate，数量: 208
2025-08-02 10:31:44 - INFO - 服务映射已加载: 104 个
2025-08-02 10:31:44 - INFO - 服务别名已加载: 69 个
2025-08-02 10:31:44 - INFO - name_mapping_manager.initialized
2025-08-02 10:31:44 - INFO - name_mapping_manager.initialized
2025-08-02 10:31:45 - ERROR - 接口映射验证失败：发现 22 个无效映射
2025-08-02 10:31:45 - ERROR - 无效的接口映射：源接口 port7，目标接口 Ge0/10，原因：接口 Ge0/10 不在设备型号 z5100s 支持的接口列表中
2025-08-02 10:31:45 - ERROR - 无效的接口映射：源接口 port8，目标接口 Ge0/11，原因：接口 Ge0/11 不在设备型号 z5100s 支持的接口列表中
2025-08-02 10:31:45 - ERROR - 无效的接口映射：源接口 port9，目标接口 Ge0/12，原因：接口 Ge0/12 不在设备型号 z5100s 支持的接口列表中
2025-08-02 10:31:45 - ERROR - 无效的接口映射：源接口 port10，目标接口 Ge0/13，原因：接口 Ge0/13 不在设备型号 z5100s 支持的接口列表中
2025-08-02 10:31:45 - ERROR - 无效的接口映射：源接口 port11，目标接口 Ge0/14，原因：接口 Ge0/14 不在设备型号 z5100s 支持的接口列表中
2025-08-02 10:31:45 - ERROR - 无效的接口映射：源接口 port12，目标接口 Ge0/15，原因：接口 Ge0/15 不在设备型号 z5100s 支持的接口列表中
2025-08-02 10:31:45 - ERROR - 无效的接口映射：源接口 port13，目标接口 Ge0/16，原因：接口 Ge0/16 不在设备型号 z5100s 支持的接口列表中
2025-08-02 10:31:45 - ERROR - 无效的接口映射：源接口 port14，目标接口 Ge0/17，原因：接口 Ge0/17 不在设备型号 z5100s 支持的接口列表中
2025-08-02 10:31:45 - ERROR - 无效的接口映射：源接口 port15，目标接口 Ge0/18，原因：接口 Ge0/18 不在设备型号 z5100s 支持的接口列表中
2025-08-02 10:31:45 - ERROR - 无效的接口映射：源接口 port16，目标接口 Ge0/19，原因：接口 Ge0/19 不在设备型号 z5100s 支持的接口列表中
2025-08-02 10:31:45 - ERROR - 无效的接口映射：源接口 port17，目标接口 Ge0/20，原因：接口 Ge0/20 不在设备型号 z5100s 支持的接口列表中
2025-08-02 10:31:45 - ERROR - 无效的接口映射：源接口 port18，目标接口 Ge0/21，原因：接口 Ge0/21 不在设备型号 z5100s 支持的接口列表中
2025-08-02 10:31:45 - ERROR - 无效的接口映射：源接口 port19，目标接口 Ge0/22，原因：接口 Ge0/22 不在设备型号 z5100s 支持的接口列表中
2025-08-02 10:31:45 - ERROR - 无效的接口映射：源接口 port20，目标接口 Ge0/23，原因：接口 Ge0/23 不在设备型号 z5100s 支持的接口列表中
2025-08-02 10:31:45 - ERROR - 无效的接口映射：源接口 port21，目标接口 Ge0/24，原因：接口 Ge0/24 不在设备型号 z5100s 支持的接口列表中
2025-08-02 10:31:45 - ERROR - 无效的接口映射：源接口 port22，目标接口 Ge0/25，原因：接口 Ge0/25 不在设备型号 z5100s 支持的接口列表中
2025-08-02 10:31:45 - ERROR - 无效的接口映射：源接口 modem，目标接口 Ge0/26，原因：接口 Ge0/26 不在设备型号 z5100s 支持的接口列表中
2025-08-02 10:31:45 - ERROR - 无效的接口映射：源接口 naf.root，目标接口 Ge0/27，原因：接口 Ge0/27 不在设备型号 z5100s 支持的接口列表中
2025-08-02 10:31:45 - ERROR - 无效的接口映射：源接口 l2t.root，目标接口 Ge0/28，原因：接口 Ge0/28 不在设备型号 z5100s 支持的接口列表中
2025-08-02 10:31:45 - ERROR - 无效的接口映射：源接口 ssl.root，目标接口 Ge0/29，原因：接口 Ge0/29 不在设备型号 z5100s 支持的接口列表中
2025-08-02 10:31:45 - ERROR - 无效的接口映射：源接口 BOS，目标接口 Ge0/30，原因：接口 Ge0/30 不在设备型号 z5100s 支持的接口列表中
2025-08-02 10:31:45 - ERROR - 无效的接口映射：源接口 SSL_VPN，目标接口 Ge0/31，原因：接口 Ge0/31 不在设备型号 z5100s 支持的接口列表中
2025-08-02 10:31:45 - ERROR - conversion_workflow.interface_mapping_validation_failed
2025-08-02 10:31:45 - WARNING - 消息中缺少参数: initial_data.get('config_file', 'unknown')
2025-08-02 10:31:45 - WARNING - 消息中缺少参数: initial_data
2025-08-02 10:31:45 - WARNING - 消息中缺少参数: initial_data.get('vendor', 'unknown')
2025-08-02 10:31:45 - WARNING - 消息中缺少参数: initial_data
2025-08-02 10:31:45 - WARNING - 消息中缺少参数: initial_data.get('model', 'unknown')
2025-08-02 10:31:45 - WARNING - 消息中缺少参数: initial_data
2025-08-02 10:31:45 - WARNING - 消息中缺少参数: initial_data.get('version', 'unknown')
2025-08-02 10:31:45 - WARNING - 消息中缺少参数: initial_data
2025-08-02 10:31:45 - WARNING - 消息中缺少参数: '存在' if initial_data.get('interface_mapping') else '不存在'
2025-08-02 10:31:45 - WARNING - 消息中缺少参数: '存在' if initial_data
2025-08-02 10:31:45 - INFO - 阶段开始: fortigate_conversion, stage count: 12
2025-08-02 10:31:45 - INFO - 阶段开始: fortigate_conversion -> operation_mode (1/12)
2025-08-02 10:31:45 - INFO - 管道阶段：阶段开始
2025-08-02 10:31:45 - INFO - 开始检测操作模式
2025-08-02 10:31:45 - INFO - Operation mode analysis: explicit=nat, role_interfaces=True, mgmt_interface=True, manageip=False
2025-08-02 10:31:45 - INFO - Route mode detected from explicit configuration: nat
2025-08-02 10:31:45 - INFO - 处理路由模式配置
2025-08-02 10:31:45 - INFO - 路由模式处理完成，主机名: MTU-FW-1
2025-08-02 10:31:45 - INFO - 操作模式检测完成，模式: route，主机名: MTU-FW-1
2025-08-02 10:31:45 - INFO - 管道阶段：阶段完成
2025-08-02 10:31:45 - INFO - 阶段开始: fortigate_conversion -> interface_processing (2/12)
2025-08-02 10:31:45 - INFO - 管道阶段：阶段开始
2025-08-02 10:31:45 - INFO - 开始处理接口配置
2025-08-02 10:31:45 - INFO - 开始接口处理，共59个项目
2025-08-02 10:31:45 - WARNING - interface_processor.unsupported_access_service
2025-08-02 10:31:45 - WARNING - interface_processor.unsupported_access_service
2025-08-02 10:31:45 - WARNING - interface_processor.unsupported_access_service
2025-08-02 10:31:45 - WARNING - interface_processor.unsupported_access_service
2025-08-02 10:31:45 - INFO - 过滤modem接口: modem
2025-08-02 10:31:45 - INFO - 接口 'naf.root' 使用转换后的类型: tunnel -> physical
2025-08-02 10:31:45 - INFO - 接口 'l2t.root' 使用转换后的类型: tunnel -> physical
2025-08-02 10:31:45 - INFO - 接口 'ssl.root' 使用转换后的类型: tunnel -> physical
2025-08-02 10:31:45 - INFO - 接口 'BOS' 使用转换后的类型: switch -> physical
2025-08-02 10:31:45 - INFO - 接口 'SSL_VPN' 使用转换后的类型: loopback -> physical
2025-08-02 10:31:45 - WARNING - interface_processor.unsupported_access_service
2025-08-02 10:31:45 - WARNING - interface_processor.unsupported_access_service
2025-08-02 10:31:45 - WARNING - interface_processor.unsupported_access_service
2025-08-02 10:31:45 - WARNING - interface_processor.unsupported_access_service
2025-08-02 10:31:45 - WARNING - interface_processor.unsupported_access_service
2025-08-02 10:31:45 - WARNING - interface_processor.unsupported_access_service
2025-08-02 10:31:45 - WARNING - interface_processor.unsupported_access_service
2025-08-02 10:31:45 - WARNING - interface_processor.unsupported_access_service
2025-08-02 10:31:45 - WARNING - interface_processor.unsupported_access_service
2025-08-02 10:31:45 - WARNING - interface_processor.unsupported_access_service
2025-08-02 10:31:45 - WARNING - interface_processor.unsupported_access_service
2025-08-02 10:31:45 - WARNING - interface_processor.unsupported_access_service
2025-08-02 10:31:45 - WARNING - interface_processor.unsupported_access_service
2025-08-02 10:31:45 - WARNING - interface_processor.unsupported_access_service
2025-08-02 10:31:45 - WARNING - interface_processor.unsupported_access_service
2025-08-02 10:31:45 - WARNING - interface_processor.unsupported_access_service
2025-08-02 10:31:45 - WARNING - interface_processor.unsupported_access_service
2025-08-02 10:31:45 - WARNING - interface_processor.unsupported_access_service
2025-08-02 10:31:45 - WARNING - interface_processor.unsupported_access_service
2025-08-02 10:31:45 - WARNING - interface_processor.unsupported_access_service
2025-08-02 10:31:45 - WARNING - interface_processor.unsupported_access_service
2025-08-02 10:31:45 - WARNING - 消息中缺少参数: 'mgmt': 'Ge0/0', 'ha': 'Ge0/9', 'port1': 'Ge0/3', 'port2': 'Ge0/4', 'port3': 'Ge0/5', 'port4': 'Ge0/6', 'port5': 'Ge0/7', 'port6': 'Ge0/8', 'port7': 'Ge0/10', 'port8': 'Ge0/11', 'port9': 'Ge0/12', 'port10': 'Ge0/13', 'port11': 'Ge0/14', 'port12': 'Ge0/15', 'port13': 'Ge0/16', 'port14': 'Ge0/17', 'port15': 'Ge0/18', 'port16': 'Ge0/19', 'port17': 'Ge0/20', 'port18': 'Ge0/21', 'port19': 'Ge0/22', 'port20': 'Ge0/23', 'port21': 'Ge0/24', 'port22': 'Ge0/25', 'port23': 'Ge0/1', 'port24': 'Ge0/2', 'x1': 'TenGe0/0', 'x2': 'TenGe0/1', 'x3': 'TenGe0/2', 'x4': 'TenGe0/3', 'naf.root': 'Ge0/27', 'l2t.root': 'Ge0/28', 'ssl.root': 'Ge0/29', 'BOS': 'Ge0/30', 'SSL_VPN': 'Ge0/31', 'BTL_YSMSPR': 'Ge0/2.2200', 'BTL_YMK': 'Ge0/2.2500', 'BTL_WIFI': 'Ge0/2.2100', 'BTL_TEL': 'Ge0/2.3100', 'BTL_RKT': 'Ge0/2.2000', 'BTL_MYO': 'Ge0/2.2600', 'BTL_MGMT': 'Ge0/2.3000', 'BTL_GUV': 'Ge0/2.2400', 'BTL_ATC': 'Ge0/2.2700', 'BTL_BIDB': 'Ge0/2.1010', 'BTL_CAM': 'Ge0/2.3200', 'REAL_IP': 'Ge0/1.135', 'PROXY': 'Ge0/1.1001', 'TargitasTest': 'Ge0/1.33', 'NAC_LOGIN': 'Ge0/2.2', 'YESILTURT_RKT': 'TenGe0/1.114', 'YESILYURT_MYO': 'TenGe0/1.115', 'KALE_MYO': 'TenGe0/1.110', 'HEKIMHAN_MYO': 'TenGe0/1.119', 'DOGANSEHIR_MYO': 'TenGe0/1.116', 'DARENDE_MYO': 'TenGe0/1.117', 'ARAPGIR_MYO': 'TenGe0/1.112', 'AKCADAG_MYO': 'TenGe0/1.120'
2025-08-02 10:31:45 - WARNING - 消息中缺少参数: 'mgmt'
2025-08-02 10:31:45 - INFO - DEBUG: 开始生成XML片段，converted接口数量: 58
2025-08-02 10:31:45 - INFO - DEBUG: physical_interfaces数量: 35
2025-08-02 10:31:45 - INFO - DEBUG: vlan_interfaces数量: 23
2025-08-02 10:31:45 - INFO - DEBUG: XML片段生成 - converted_interfaces: 58
2025-08-02 10:31:45 - INFO - DEBUG: XML片段生成 - vlan_interfaces: 23
2025-08-02 10:31:45 - INFO - DEBUG: XML片段生成 - physical_interfaces: 35
2025-08-02 10:31:45 - INFO - PhysicalInterfaceHandler初始化 - 非透明模式，transparent_mode_result保持None
2025-08-02 10:31:45 - INFO - 创建新VLAN接口: Ge0/2.2200
2025-08-02 10:31:45 - INFO - interface handler: mapped parent interface
2025-08-02 10:31:45 - INFO - 为子接口 Ge0/2.2200 添加IPv4配置，模式: 
2025-08-02 10:31:45 - INFO - 子接口 Ge0/2.2200 配置为静态IP模式: *********/24
2025-08-02 10:31:45 - INFO - VLAN接口已创建: Ge0/2.2200
2025-08-02 10:31:45 - INFO - 创建新VLAN接口: Ge0/2.2500
2025-08-02 10:31:45 - INFO - interface handler: mapped parent interface
2025-08-02 10:31:45 - INFO - 为子接口 Ge0/2.2500 添加IPv4配置，模式: 
2025-08-02 10:31:45 - INFO - 子接口 Ge0/2.2500 配置为静态IP模式: *********/24
2025-08-02 10:31:45 - INFO - VLAN接口已创建: Ge0/2.2500
2025-08-02 10:31:45 - INFO - 创建新VLAN接口: Ge0/2.2100
2025-08-02 10:31:45 - INFO - interface handler: mapped parent interface
2025-08-02 10:31:45 - INFO - 为子接口 Ge0/2.2100 添加IPv4配置，模式: 
2025-08-02 10:31:45 - INFO - 子接口 Ge0/2.2100 配置为静态IP模式: *********/20
2025-08-02 10:31:45 - INFO - VLAN接口已创建: Ge0/2.2100
2025-08-02 10:31:45 - INFO - 创建新VLAN接口: Ge0/2.3100
2025-08-02 10:31:45 - INFO - interface handler: mapped parent interface
2025-08-02 10:31:45 - INFO - 为子接口 Ge0/2.3100 添加IPv4配置，模式: 
2025-08-02 10:31:45 - INFO - 子接口 Ge0/2.3100 配置为静态IP模式: *********/20
2025-08-02 10:31:45 - INFO - VLAN接口已创建: Ge0/2.3100
2025-08-02 10:31:45 - INFO - 创建新VLAN接口: Ge0/2.2000
2025-08-02 10:31:45 - INFO - interface handler: mapped parent interface
2025-08-02 10:31:45 - INFO - 为子接口 Ge0/2.2000 添加IPv4配置，模式: 
2025-08-02 10:31:45 - INFO - 子接口 Ge0/2.2000 配置为静态IP模式: *********/22
2025-08-02 10:31:45 - INFO - VLAN接口已创建: Ge0/2.2000
2025-08-02 10:31:45 - INFO - 创建新VLAN接口: Ge0/2.2600
2025-08-02 10:31:45 - INFO - interface handler: mapped parent interface
2025-08-02 10:31:45 - INFO - 为子接口 Ge0/2.2600 添加IPv4配置，模式: 
2025-08-02 10:31:45 - INFO - 子接口 Ge0/2.2600 配置为静态IP模式: *********/24
2025-08-02 10:31:45 - INFO - VLAN接口已创建: Ge0/2.2600
2025-08-02 10:31:45 - INFO - 创建新VLAN接口: Ge0/2.3000
2025-08-02 10:31:45 - INFO - interface handler: mapped parent interface
2025-08-02 10:31:45 - INFO - 为子接口 Ge0/2.3000 添加IPv4配置，模式: 
2025-08-02 10:31:45 - INFO - 子接口 Ge0/2.3000 配置为静态IP模式: *********/20
2025-08-02 10:31:45 - INFO - VLAN接口已创建: Ge0/2.3000
2025-08-02 10:31:45 - INFO - 创建新VLAN接口: Ge0/2.2400
2025-08-02 10:31:45 - INFO - interface handler: mapped parent interface
2025-08-02 10:31:45 - INFO - 为子接口 Ge0/2.2400 添加IPv4配置，模式: 
2025-08-02 10:31:45 - INFO - 子接口 Ge0/2.2400 配置为静态IP模式: *********/24
2025-08-02 10:31:45 - INFO - VLAN接口已创建: Ge0/2.2400
2025-08-02 10:31:45 - INFO - 创建新VLAN接口: Ge0/2.2700
2025-08-02 10:31:45 - INFO - interface handler: mapped parent interface
2025-08-02 10:31:45 - INFO - 为子接口 Ge0/2.2700 添加IPv4配置，模式: 
2025-08-02 10:31:45 - INFO - 子接口 Ge0/2.2700 配置为静态IP模式: *********/24
2025-08-02 10:31:45 - INFO - VLAN接口已创建: Ge0/2.2700
2025-08-02 10:31:45 - INFO - 创建新VLAN接口: Ge0/2.1010
2025-08-02 10:31:45 - INFO - interface handler: mapped parent interface
2025-08-02 10:31:45 - INFO - 为子接口 Ge0/2.1010 添加IPv4配置，模式: 
2025-08-02 10:31:45 - INFO - 子接口 Ge0/2.1010 配置为静态IP模式: *********/24
2025-08-02 10:31:45 - INFO - VLAN接口已创建: Ge0/2.1010
2025-08-02 10:31:45 - INFO - 创建新VLAN接口: Ge0/2.3200
2025-08-02 10:31:45 - INFO - interface handler: mapped parent interface
2025-08-02 10:31:45 - INFO - 为子接口 Ge0/2.3200 添加IPv4配置，模式: 
2025-08-02 10:31:45 - INFO - 子接口 Ge0/2.3200 配置为静态IP模式: *********/20
2025-08-02 10:31:45 - INFO - VLAN接口已创建: Ge0/2.3200
2025-08-02 10:31:45 - INFO - 创建新VLAN接口: Ge0/1.135
2025-08-02 10:31:45 - INFO - interface handler: mapped parent interface
2025-08-02 10:31:45 - INFO - 为子接口 Ge0/1.135 添加IPv4配置，模式: 
2025-08-02 10:31:45 - INFO - 子接口 Ge0/1.135 配置为静态IP模式: ************/24
2025-08-02 10:31:45 - INFO - VLAN接口已创建: Ge0/1.135
2025-08-02 10:31:45 - INFO - 创建新VLAN接口: Ge0/1.1001
2025-08-02 10:31:45 - INFO - interface handler: mapped parent interface
2025-08-02 10:31:45 - INFO - 为子接口 Ge0/1.1001 添加IPv4配置，模式: 
2025-08-02 10:31:45 - INFO - 子接口 Ge0/1.1001 配置为静态IP模式: ***********/24
2025-08-02 10:31:45 - INFO - VLAN接口已创建: Ge0/1.1001
2025-08-02 10:31:45 - INFO - 创建新VLAN接口: Ge0/1.33
2025-08-02 10:31:45 - INFO - interface handler: mapped parent interface
2025-08-02 10:31:45 - INFO - 为子接口 Ge0/1.33 添加IPv4配置，模式: 
2025-08-02 10:31:45 - INFO - 子接口 Ge0/1.33 配置为静态IP模式: ***********/24
2025-08-02 10:31:45 - INFO - VLAN接口已创建: Ge0/1.33
2025-08-02 10:31:45 - INFO - 创建新VLAN接口: Ge0/2.2
2025-08-02 10:31:45 - INFO - interface handler: mapped parent interface
2025-08-02 10:31:45 - INFO - 为子接口 Ge0/2.2 添加IPv4配置，模式: 
2025-08-02 10:31:45 - INFO - 子接口 Ge0/2.2 配置为静态IP模式: ***********/24
2025-08-02 10:31:45 - INFO - VLAN接口已创建: Ge0/2.2
2025-08-02 10:31:45 - INFO - 创建新VLAN接口: TenGe0/1.114
2025-08-02 10:31:45 - INFO - interface handler: mapped parent interface
2025-08-02 10:31:45 - INFO - 为子接口 TenGe0/1.114 添加IPv4配置，模式: 
2025-08-02 10:31:45 - INFO - 子接口 TenGe0/1.114 配置为静态IP模式: **********/20
2025-08-02 10:31:45 - INFO - VLAN接口已创建: TenGe0/1.114
2025-08-02 10:31:45 - INFO - 创建新VLAN接口: TenGe0/1.115
2025-08-02 10:31:45 - INFO - interface handler: mapped parent interface
2025-08-02 10:31:45 - INFO - 为子接口 TenGe0/1.115 添加IPv4配置，模式: 
2025-08-02 10:31:45 - INFO - 子接口 TenGe0/1.115 配置为静态IP模式: **********/20
2025-08-02 10:31:45 - INFO - VLAN接口已创建: TenGe0/1.115
2025-08-02 10:31:45 - INFO - 创建新VLAN接口: TenGe0/1.110
2025-08-02 10:31:45 - INFO - interface handler: mapped parent interface
2025-08-02 10:31:45 - INFO - 为子接口 TenGe0/1.110 添加IPv4配置，模式: 
2025-08-02 10:31:45 - INFO - 子接口 TenGe0/1.110 配置为静态IP模式: **********/22
2025-08-02 10:31:45 - INFO - VLAN接口已创建: TenGe0/1.110
2025-08-02 10:31:45 - INFO - 创建新VLAN接口: TenGe0/1.119
2025-08-02 10:31:45 - INFO - interface handler: mapped parent interface
2025-08-02 10:31:45 - INFO - 为子接口 TenGe0/1.119 添加IPv4配置，模式: 
2025-08-02 10:31:45 - INFO - 子接口 TenGe0/1.119 配置为静态IP模式: **********/20
2025-08-02 10:31:45 - INFO - VLAN接口已创建: TenGe0/1.119
2025-08-02 10:31:45 - INFO - 创建新VLAN接口: TenGe0/1.116
2025-08-02 10:31:45 - INFO - interface handler: mapped parent interface
2025-08-02 10:31:45 - INFO - 为子接口 TenGe0/1.116 添加IPv4配置，模式: 
2025-08-02 10:31:45 - INFO - 子接口 TenGe0/1.116 配置为静态IP模式: **********/22
2025-08-02 10:31:45 - INFO - VLAN接口已创建: TenGe0/1.116
2025-08-02 10:31:45 - INFO - 创建新VLAN接口: TenGe0/1.117
2025-08-02 10:31:45 - INFO - interface handler: mapped parent interface
2025-08-02 10:31:45 - INFO - 为子接口 TenGe0/1.117 添加IPv4配置，模式: 
2025-08-02 10:31:45 - INFO - 子接口 TenGe0/1.117 配置为静态IP模式: **********/20
2025-08-02 10:31:45 - INFO - VLAN接口已创建: TenGe0/1.117
2025-08-02 10:31:45 - INFO - 创建新VLAN接口: TenGe0/1.112
2025-08-02 10:31:45 - INFO - interface handler: mapped parent interface
2025-08-02 10:31:45 - INFO - 为子接口 TenGe0/1.112 添加IPv4配置，模式: 
2025-08-02 10:31:45 - INFO - 子接口 TenGe0/1.112 配置为静态IP模式: **********/20
2025-08-02 10:31:45 - INFO - VLAN接口已创建: TenGe0/1.112
2025-08-02 10:31:45 - INFO - 创建新VLAN接口: TenGe0/1.120
2025-08-02 10:31:45 - INFO - interface handler: mapped parent interface
2025-08-02 10:31:45 - INFO - 为子接口 TenGe0/1.120 添加IPv4配置，模式: 
2025-08-02 10:31:45 - INFO - 子接口 TenGe0/1.120 配置为静态IP模式: **********/22
2025-08-02 10:31:45 - INFO - VLAN接口已创建: TenGe0/1.120
2025-08-02 10:31:45 - INFO - DEBUG: 开始处理物理接口，数量: 35
2025-08-02 10:31:45 - INFO - DEBUG: 处理物理接口 mgmt, is_subinterface: False
2025-08-02 10:31:45 - INFO - DEBUG: 为接口 mgmt 生成XML
2025-08-02 10:31:45 - WARNING - 消息中缺少参数: interface_name
2025-08-02 10:31:45 - WARNING - 消息中缺少参数: list(intf_data.keys())
2025-08-02 10:31:45 - WARNING - 消息中缺少参数: list(intf_data
2025-08-02 10:31:45 - INFO - interface handler: create new interface
2025-08-02 10:31:45 - INFO - interface handler: using fortinet optimizer
2025-08-02 10:31:45 - INFO - interface handler: create fortinet interface
2025-08-02 10:31:45 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-02 10:31:45 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-02 10:31:45 - INFO - interface handler: configured static ip
2025-08-02 10:31:45 - INFO - DEBUG: 最终的combined_access: ['https', 'ping', 'ssh']
2025-08-02 10:31:45 - INFO - DEBUG: 最终XML值 - https: true, ping: true, ssh: true
2025-08-02 10:31:45 - INFO - interface handler: configured access control
2025-08-02 10:31:45 - INFO - interface handler: fortinet interface created
2025-08-02 10:31:45 - INFO - DEBUG: 接口 mgmt XML生成完成
2025-08-02 10:31:45 - INFO - DEBUG: 处理物理接口 ha, is_subinterface: False
2025-08-02 10:31:45 - INFO - DEBUG: 为接口 ha 生成XML
2025-08-02 10:31:45 - WARNING - 消息中缺少参数: interface_name
2025-08-02 10:31:45 - WARNING - 消息中缺少参数: list(intf_data.keys())
2025-08-02 10:31:45 - WARNING - 消息中缺少参数: list(intf_data
2025-08-02 10:31:45 - INFO - interface handler: create new interface
2025-08-02 10:31:45 - INFO - interface handler: using fortinet optimizer
2025-08-02 10:31:45 - INFO - interface handler: create fortinet interface
2025-08-02 10:31:45 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-02 10:31:45 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-02 10:31:45 - INFO - DEBUG: 最终的combined_access: []
2025-08-02 10:31:45 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-02 10:31:45 - INFO - interface handler: configured access control
2025-08-02 10:31:45 - INFO - interface handler: fortinet interface created
2025-08-02 10:31:45 - INFO - DEBUG: 接口 ha XML生成完成
2025-08-02 10:31:45 - INFO - DEBUG: 处理物理接口 port1, is_subinterface: False
2025-08-02 10:31:45 - INFO - DEBUG: 为接口 port1 生成XML
2025-08-02 10:31:45 - WARNING - 消息中缺少参数: interface_name
2025-08-02 10:31:45 - WARNING - 消息中缺少参数: list(intf_data.keys())
2025-08-02 10:31:45 - WARNING - 消息中缺少参数: list(intf_data
2025-08-02 10:31:45 - INFO - interface handler: create new interface
2025-08-02 10:31:45 - INFO - interface handler: using fortinet optimizer
2025-08-02 10:31:45 - INFO - interface handler: create fortinet interface
2025-08-02 10:31:45 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-02 10:31:45 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-02 10:31:45 - INFO - DEBUG: 最终的combined_access: []
2025-08-02 10:31:45 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-02 10:31:45 - INFO - interface handler: configured access control
2025-08-02 10:31:45 - INFO - interface handler: fortinet interface created
2025-08-02 10:31:45 - INFO - DEBUG: 接口 port1 XML生成完成
2025-08-02 10:31:45 - INFO - DEBUG: 处理物理接口 port2, is_subinterface: False
2025-08-02 10:31:45 - INFO - DEBUG: 为接口 port2 生成XML
2025-08-02 10:31:45 - WARNING - 消息中缺少参数: interface_name
2025-08-02 10:31:45 - WARNING - 消息中缺少参数: list(intf_data.keys())
2025-08-02 10:31:45 - WARNING - 消息中缺少参数: list(intf_data
2025-08-02 10:31:45 - INFO - interface handler: create new interface
2025-08-02 10:31:45 - INFO - interface handler: using fortinet optimizer
2025-08-02 10:31:45 - INFO - interface handler: create fortinet interface
2025-08-02 10:31:45 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-02 10:31:45 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-02 10:31:45 - INFO - DEBUG: 最终的combined_access: []
2025-08-02 10:31:45 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-02 10:31:45 - INFO - interface handler: configured access control
2025-08-02 10:31:45 - INFO - interface handler: fortinet interface created
2025-08-02 10:31:45 - INFO - DEBUG: 接口 port2 XML生成完成
2025-08-02 10:31:45 - INFO - DEBUG: 处理物理接口 port3, is_subinterface: False
2025-08-02 10:31:45 - INFO - DEBUG: 为接口 port3 生成XML
2025-08-02 10:31:45 - WARNING - 消息中缺少参数: interface_name
2025-08-02 10:31:45 - WARNING - 消息中缺少参数: list(intf_data.keys())
2025-08-02 10:31:45 - WARNING - 消息中缺少参数: list(intf_data
2025-08-02 10:31:45 - INFO - interface handler: create new interface
2025-08-02 10:31:45 - INFO - interface handler: using fortinet optimizer
2025-08-02 10:31:45 - INFO - interface handler: create fortinet interface
2025-08-02 10:31:45 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-02 10:31:45 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-02 10:31:45 - INFO - DEBUG: 最终的combined_access: []
2025-08-02 10:31:45 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-02 10:31:45 - INFO - interface handler: configured access control
2025-08-02 10:31:45 - INFO - interface handler: fortinet interface created
2025-08-02 10:31:45 - INFO - DEBUG: 接口 port3 XML生成完成
2025-08-02 10:31:45 - INFO - DEBUG: 处理物理接口 port4, is_subinterface: False
2025-08-02 10:31:45 - INFO - DEBUG: 为接口 port4 生成XML
2025-08-02 10:31:45 - WARNING - 消息中缺少参数: interface_name
2025-08-02 10:31:45 - WARNING - 消息中缺少参数: list(intf_data.keys())
2025-08-02 10:31:45 - WARNING - 消息中缺少参数: list(intf_data
2025-08-02 10:31:45 - INFO - interface handler: create new interface
2025-08-02 10:31:45 - INFO - interface handler: using fortinet optimizer
2025-08-02 10:31:45 - INFO - interface handler: create fortinet interface
2025-08-02 10:31:45 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-02 10:31:45 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-02 10:31:45 - INFO - DEBUG: 最终的combined_access: []
2025-08-02 10:31:45 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-02 10:31:45 - INFO - interface handler: configured access control
2025-08-02 10:31:45 - INFO - interface handler: fortinet interface created
2025-08-02 10:31:45 - INFO - DEBUG: 接口 port4 XML生成完成
2025-08-02 10:31:45 - INFO - DEBUG: 处理物理接口 port5, is_subinterface: False
2025-08-02 10:31:45 - INFO - DEBUG: 为接口 port5 生成XML
2025-08-02 10:31:45 - WARNING - 消息中缺少参数: interface_name
2025-08-02 10:31:45 - WARNING - 消息中缺少参数: list(intf_data.keys())
2025-08-02 10:31:45 - WARNING - 消息中缺少参数: list(intf_data
2025-08-02 10:31:45 - INFO - interface handler: create new interface
2025-08-02 10:31:45 - INFO - interface handler: using fortinet optimizer
2025-08-02 10:31:45 - INFO - interface handler: create fortinet interface
2025-08-02 10:31:45 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-02 10:31:45 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-02 10:31:45 - INFO - DEBUG: 最终的combined_access: []
2025-08-02 10:31:45 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-02 10:31:45 - INFO - interface handler: configured access control
2025-08-02 10:31:45 - INFO - interface handler: fortinet interface created
2025-08-02 10:31:45 - INFO - DEBUG: 接口 port5 XML生成完成
2025-08-02 10:31:45 - INFO - DEBUG: 处理物理接口 port6, is_subinterface: False
2025-08-02 10:31:45 - INFO - DEBUG: 为接口 port6 生成XML
2025-08-02 10:31:45 - WARNING - 消息中缺少参数: interface_name
2025-08-02 10:31:45 - WARNING - 消息中缺少参数: list(intf_data.keys())
2025-08-02 10:31:45 - WARNING - 消息中缺少参数: list(intf_data
2025-08-02 10:31:45 - INFO - interface handler: create new interface
2025-08-02 10:31:45 - INFO - interface handler: using fortinet optimizer
2025-08-02 10:31:45 - INFO - interface handler: create fortinet interface
2025-08-02 10:31:45 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-02 10:31:45 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-02 10:31:45 - INFO - DEBUG: 最终的combined_access: []
2025-08-02 10:31:45 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-02 10:31:45 - INFO - interface handler: configured access control
2025-08-02 10:31:45 - INFO - interface handler: fortinet interface created
2025-08-02 10:31:45 - INFO - DEBUG: 接口 port6 XML生成完成
2025-08-02 10:31:46 - INFO - DEBUG: 处理物理接口 port7, is_subinterface: False
2025-08-02 10:31:46 - INFO - DEBUG: 为接口 port7 生成XML
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: interface_name
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data.keys())
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data
2025-08-02 10:31:46 - INFO - interface handler: create new interface
2025-08-02 10:31:46 - INFO - interface handler: using fortinet optimizer
2025-08-02 10:31:46 - INFO - interface handler: create fortinet interface
2025-08-02 10:31:46 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-02 10:31:46 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-02 10:31:46 - INFO - DEBUG: 最终的combined_access: []
2025-08-02 10:31:46 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-02 10:31:46 - INFO - interface handler: configured access control
2025-08-02 10:31:46 - INFO - interface handler: fortinet interface created
2025-08-02 10:31:46 - INFO - DEBUG: 接口 port7 XML生成完成
2025-08-02 10:31:46 - INFO - DEBUG: 处理物理接口 port8, is_subinterface: False
2025-08-02 10:31:46 - INFO - DEBUG: 为接口 port8 生成XML
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: interface_name
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data.keys())
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data
2025-08-02 10:31:46 - INFO - interface handler: create new interface
2025-08-02 10:31:46 - INFO - interface handler: using fortinet optimizer
2025-08-02 10:31:46 - INFO - interface handler: create fortinet interface
2025-08-02 10:31:46 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-02 10:31:46 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-02 10:31:46 - INFO - DEBUG: 最终的combined_access: []
2025-08-02 10:31:46 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-02 10:31:46 - INFO - interface handler: configured access control
2025-08-02 10:31:46 - INFO - interface handler: fortinet interface created
2025-08-02 10:31:46 - INFO - DEBUG: 接口 port8 XML生成完成
2025-08-02 10:31:46 - INFO - DEBUG: 处理物理接口 port9, is_subinterface: False
2025-08-02 10:31:46 - INFO - DEBUG: 为接口 port9 生成XML
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: interface_name
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data.keys())
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data
2025-08-02 10:31:46 - INFO - interface handler: create new interface
2025-08-02 10:31:46 - INFO - interface handler: using fortinet optimizer
2025-08-02 10:31:46 - INFO - interface handler: create fortinet interface
2025-08-02 10:31:46 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-02 10:31:46 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-02 10:31:46 - INFO - DEBUG: 最终的combined_access: []
2025-08-02 10:31:46 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-02 10:31:46 - INFO - interface handler: configured access control
2025-08-02 10:31:46 - INFO - interface handler: fortinet interface created
2025-08-02 10:31:46 - INFO - DEBUG: 接口 port9 XML生成完成
2025-08-02 10:31:46 - INFO - DEBUG: 处理物理接口 port10, is_subinterface: False
2025-08-02 10:31:46 - INFO - DEBUG: 为接口 port10 生成XML
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: interface_name
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data.keys())
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data
2025-08-02 10:31:46 - INFO - interface handler: create new interface
2025-08-02 10:31:46 - INFO - interface handler: using fortinet optimizer
2025-08-02 10:31:46 - INFO - interface handler: create fortinet interface
2025-08-02 10:31:46 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-02 10:31:46 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-02 10:31:46 - INFO - DEBUG: 最终的combined_access: []
2025-08-02 10:31:46 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-02 10:31:46 - INFO - interface handler: configured access control
2025-08-02 10:31:46 - INFO - interface handler: fortinet interface created
2025-08-02 10:31:46 - INFO - DEBUG: 接口 port10 XML生成完成
2025-08-02 10:31:46 - INFO - DEBUG: 处理物理接口 port11, is_subinterface: False
2025-08-02 10:31:46 - INFO - DEBUG: 为接口 port11 生成XML
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: interface_name
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data.keys())
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data
2025-08-02 10:31:46 - INFO - interface handler: create new interface
2025-08-02 10:31:46 - INFO - interface handler: using fortinet optimizer
2025-08-02 10:31:46 - INFO - interface handler: create fortinet interface
2025-08-02 10:31:46 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-02 10:31:46 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-02 10:31:46 - INFO - DEBUG: 最终的combined_access: []
2025-08-02 10:31:46 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-02 10:31:46 - INFO - interface handler: configured access control
2025-08-02 10:31:46 - INFO - interface handler: fortinet interface created
2025-08-02 10:31:46 - INFO - DEBUG: 接口 port11 XML生成完成
2025-08-02 10:31:46 - INFO - DEBUG: 处理物理接口 port12, is_subinterface: False
2025-08-02 10:31:46 - INFO - DEBUG: 为接口 port12 生成XML
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: interface_name
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data.keys())
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data
2025-08-02 10:31:46 - INFO - interface handler: create new interface
2025-08-02 10:31:46 - INFO - interface handler: using fortinet optimizer
2025-08-02 10:31:46 - INFO - interface handler: create fortinet interface
2025-08-02 10:31:46 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-02 10:31:46 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-02 10:31:46 - INFO - DEBUG: 最终的combined_access: []
2025-08-02 10:31:46 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-02 10:31:46 - INFO - interface handler: configured access control
2025-08-02 10:31:46 - INFO - interface handler: fortinet interface created
2025-08-02 10:31:46 - INFO - DEBUG: 接口 port12 XML生成完成
2025-08-02 10:31:46 - INFO - DEBUG: 处理物理接口 port13, is_subinterface: False
2025-08-02 10:31:46 - INFO - DEBUG: 为接口 port13 生成XML
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: interface_name
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data.keys())
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data
2025-08-02 10:31:46 - INFO - interface handler: create new interface
2025-08-02 10:31:46 - INFO - interface handler: using fortinet optimizer
2025-08-02 10:31:46 - INFO - interface handler: create fortinet interface
2025-08-02 10:31:46 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-02 10:31:46 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-02 10:31:46 - INFO - DEBUG: 最终的combined_access: []
2025-08-02 10:31:46 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-02 10:31:46 - INFO - interface handler: configured access control
2025-08-02 10:31:46 - INFO - interface handler: fortinet interface created
2025-08-02 10:31:46 - INFO - DEBUG: 接口 port13 XML生成完成
2025-08-02 10:31:46 - INFO - DEBUG: 处理物理接口 port14, is_subinterface: False
2025-08-02 10:31:46 - INFO - DEBUG: 为接口 port14 生成XML
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: interface_name
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data.keys())
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data
2025-08-02 10:31:46 - INFO - interface handler: create new interface
2025-08-02 10:31:46 - INFO - interface handler: using fortinet optimizer
2025-08-02 10:31:46 - INFO - interface handler: create fortinet interface
2025-08-02 10:31:46 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-02 10:31:46 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-02 10:31:46 - INFO - DEBUG: 最终的combined_access: []
2025-08-02 10:31:46 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-02 10:31:46 - INFO - interface handler: configured access control
2025-08-02 10:31:46 - INFO - interface handler: fortinet interface created
2025-08-02 10:31:46 - INFO - DEBUG: 接口 port14 XML生成完成
2025-08-02 10:31:46 - INFO - DEBUG: 处理物理接口 port15, is_subinterface: False
2025-08-02 10:31:46 - INFO - DEBUG: 为接口 port15 生成XML
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: interface_name
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data.keys())
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data
2025-08-02 10:31:46 - INFO - interface handler: create new interface
2025-08-02 10:31:46 - INFO - interface handler: using fortinet optimizer
2025-08-02 10:31:46 - INFO - interface handler: create fortinet interface
2025-08-02 10:31:46 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-02 10:31:46 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-02 10:31:46 - INFO - DEBUG: 最终的combined_access: []
2025-08-02 10:31:46 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-02 10:31:46 - INFO - interface handler: configured access control
2025-08-02 10:31:46 - INFO - interface handler: fortinet interface created
2025-08-02 10:31:46 - INFO - DEBUG: 接口 port15 XML生成完成
2025-08-02 10:31:46 - INFO - DEBUG: 处理物理接口 port16, is_subinterface: False
2025-08-02 10:31:46 - INFO - DEBUG: 为接口 port16 生成XML
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: interface_name
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data.keys())
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data
2025-08-02 10:31:46 - INFO - interface handler: create new interface
2025-08-02 10:31:46 - INFO - interface handler: using fortinet optimizer
2025-08-02 10:31:46 - INFO - interface handler: create fortinet interface
2025-08-02 10:31:46 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-02 10:31:46 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-02 10:31:46 - INFO - DEBUG: 最终的combined_access: []
2025-08-02 10:31:46 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-02 10:31:46 - INFO - interface handler: configured access control
2025-08-02 10:31:46 - INFO - interface handler: fortinet interface created
2025-08-02 10:31:46 - INFO - DEBUG: 接口 port16 XML生成完成
2025-08-02 10:31:46 - INFO - DEBUG: 处理物理接口 port17, is_subinterface: False
2025-08-02 10:31:46 - INFO - DEBUG: 为接口 port17 生成XML
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: interface_name
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data.keys())
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data
2025-08-02 10:31:46 - INFO - interface handler: create new interface
2025-08-02 10:31:46 - INFO - interface handler: using fortinet optimizer
2025-08-02 10:31:46 - INFO - interface handler: create fortinet interface
2025-08-02 10:31:46 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-02 10:31:46 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-02 10:31:46 - INFO - DEBUG: 最终的combined_access: []
2025-08-02 10:31:46 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-02 10:31:46 - INFO - interface handler: configured access control
2025-08-02 10:31:46 - INFO - interface handler: fortinet interface created
2025-08-02 10:31:46 - INFO - DEBUG: 接口 port17 XML生成完成
2025-08-02 10:31:46 - INFO - DEBUG: 处理物理接口 port18, is_subinterface: False
2025-08-02 10:31:46 - INFO - DEBUG: 为接口 port18 生成XML
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: interface_name
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data.keys())
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data
2025-08-02 10:31:46 - INFO - interface handler: create new interface
2025-08-02 10:31:46 - INFO - interface handler: using fortinet optimizer
2025-08-02 10:31:46 - INFO - interface handler: create fortinet interface
2025-08-02 10:31:46 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-02 10:31:46 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-02 10:31:46 - INFO - DEBUG: 最终的combined_access: []
2025-08-02 10:31:46 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-02 10:31:46 - INFO - interface handler: configured access control
2025-08-02 10:31:46 - INFO - interface handler: fortinet interface created
2025-08-02 10:31:46 - INFO - DEBUG: 接口 port18 XML生成完成
2025-08-02 10:31:46 - INFO - DEBUG: 处理物理接口 port19, is_subinterface: False
2025-08-02 10:31:46 - INFO - DEBUG: 为接口 port19 生成XML
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: interface_name
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data.keys())
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data
2025-08-02 10:31:46 - INFO - interface handler: create new interface
2025-08-02 10:31:46 - INFO - interface handler: using fortinet optimizer
2025-08-02 10:31:46 - INFO - interface handler: create fortinet interface
2025-08-02 10:31:46 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-02 10:31:46 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-02 10:31:46 - INFO - DEBUG: 最终的combined_access: []
2025-08-02 10:31:46 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-02 10:31:46 - INFO - interface handler: configured access control
2025-08-02 10:31:46 - INFO - interface handler: fortinet interface created
2025-08-02 10:31:46 - INFO - DEBUG: 接口 port19 XML生成完成
2025-08-02 10:31:46 - INFO - DEBUG: 处理物理接口 port20, is_subinterface: False
2025-08-02 10:31:46 - INFO - DEBUG: 为接口 port20 生成XML
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: interface_name
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data.keys())
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data
2025-08-02 10:31:46 - INFO - interface handler: create new interface
2025-08-02 10:31:46 - INFO - interface handler: using fortinet optimizer
2025-08-02 10:31:46 - INFO - interface handler: create fortinet interface
2025-08-02 10:31:46 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-02 10:31:46 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-02 10:31:46 - INFO - DEBUG: 最终的combined_access: []
2025-08-02 10:31:46 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-02 10:31:46 - INFO - interface handler: configured access control
2025-08-02 10:31:46 - INFO - interface handler: fortinet interface created
2025-08-02 10:31:46 - INFO - DEBUG: 接口 port20 XML生成完成
2025-08-02 10:31:46 - INFO - DEBUG: 处理物理接口 port21, is_subinterface: False
2025-08-02 10:31:46 - INFO - DEBUG: 为接口 port21 生成XML
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: interface_name
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data.keys())
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data
2025-08-02 10:31:46 - INFO - interface handler: create new interface
2025-08-02 10:31:46 - INFO - interface handler: using fortinet optimizer
2025-08-02 10:31:46 - INFO - interface handler: create fortinet interface
2025-08-02 10:31:46 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-02 10:31:46 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-02 10:31:46 - INFO - DEBUG: 最终的combined_access: []
2025-08-02 10:31:46 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-02 10:31:46 - INFO - interface handler: configured access control
2025-08-02 10:31:46 - INFO - interface handler: fortinet interface created
2025-08-02 10:31:46 - INFO - DEBUG: 接口 port21 XML生成完成
2025-08-02 10:31:46 - INFO - DEBUG: 处理物理接口 port22, is_subinterface: False
2025-08-02 10:31:46 - INFO - DEBUG: 为接口 port22 生成XML
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: interface_name
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data.keys())
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data
2025-08-02 10:31:46 - INFO - interface handler: create new interface
2025-08-02 10:31:46 - INFO - interface handler: using fortinet optimizer
2025-08-02 10:31:46 - INFO - interface handler: create fortinet interface
2025-08-02 10:31:46 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-02 10:31:46 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-02 10:31:46 - INFO - DEBUG: 最终的combined_access: []
2025-08-02 10:31:46 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-02 10:31:46 - INFO - interface handler: configured access control
2025-08-02 10:31:46 - INFO - interface handler: fortinet interface created
2025-08-02 10:31:46 - INFO - DEBUG: 接口 port22 XML生成完成
2025-08-02 10:31:46 - INFO - DEBUG: 处理物理接口 port23, is_subinterface: False
2025-08-02 10:31:46 - INFO - DEBUG: 为接口 port23 生成XML
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: interface_name
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data.keys())
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data
2025-08-02 10:31:46 - INFO - interface handler: create new interface
2025-08-02 10:31:46 - INFO - interface handler: using fortinet optimizer
2025-08-02 10:31:46 - INFO - interface handler: create fortinet interface
2025-08-02 10:31:46 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-02 10:31:46 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-02 10:31:46 - INFO - interface handler: configured static ip
2025-08-02 10:31:46 - INFO - DEBUG: 最终的combined_access: ['https', 'ping', 'ssh']
2025-08-02 10:31:46 - INFO - DEBUG: 最终XML值 - https: true, ping: true, ssh: true
2025-08-02 10:31:46 - INFO - interface handler: configured access control
2025-08-02 10:31:46 - INFO - interface handler: fortinet interface created
2025-08-02 10:31:46 - INFO - DEBUG: 接口 port23 XML生成完成
2025-08-02 10:31:46 - INFO - DEBUG: 处理物理接口 port24, is_subinterface: False
2025-08-02 10:31:46 - INFO - DEBUG: 为接口 port24 生成XML
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: interface_name
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data.keys())
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data
2025-08-02 10:31:46 - INFO - interface handler: create new interface
2025-08-02 10:31:46 - INFO - interface handler: using fortinet optimizer
2025-08-02 10:31:46 - INFO - interface handler: create fortinet interface
2025-08-02 10:31:46 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-02 10:31:46 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-02 10:31:46 - INFO - interface handler: configured static ip
2025-08-02 10:31:46 - INFO - DEBUG: 最终的combined_access: ['ping']
2025-08-02 10:31:46 - INFO - DEBUG: 最终XML值 - https: false, ping: true, ssh: false
2025-08-02 10:31:46 - INFO - interface handler: configured access control
2025-08-02 10:31:46 - INFO - interface handler: fortinet interface created
2025-08-02 10:31:46 - INFO - DEBUG: 接口 port24 XML生成完成
2025-08-02 10:31:46 - INFO - DEBUG: 处理物理接口 x1, is_subinterface: False
2025-08-02 10:31:46 - INFO - DEBUG: 为接口 x1 生成XML
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: interface_name
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data.keys())
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data
2025-08-02 10:31:46 - INFO - interface handler: create new interface
2025-08-02 10:31:46 - INFO - interface handler: using fortinet optimizer
2025-08-02 10:31:46 - INFO - interface handler: create fortinet interface
2025-08-02 10:31:46 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-02 10:31:46 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-02 10:31:46 - INFO - interface handler: configured static ip
2025-08-02 10:31:46 - INFO - DEBUG: 最终的combined_access: ['ping']
2025-08-02 10:31:46 - INFO - DEBUG: 最终XML值 - https: false, ping: true, ssh: false
2025-08-02 10:31:46 - INFO - interface handler: configured access control
2025-08-02 10:31:46 - INFO - interface handler: fortinet interface created
2025-08-02 10:31:46 - INFO - DEBUG: 接口 x1 XML生成完成
2025-08-02 10:31:46 - INFO - DEBUG: 处理物理接口 x2, is_subinterface: False
2025-08-02 10:31:46 - INFO - DEBUG: 为接口 x2 生成XML
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: interface_name
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data.keys())
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data
2025-08-02 10:31:46 - INFO - interface handler: create new interface
2025-08-02 10:31:46 - INFO - interface handler: using fortinet optimizer
2025-08-02 10:31:46 - INFO - interface handler: create fortinet interface
2025-08-02 10:31:46 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-02 10:31:46 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-02 10:31:46 - INFO - DEBUG: 最终的combined_access: []
2025-08-02 10:31:46 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-02 10:31:46 - INFO - interface handler: configured access control
2025-08-02 10:31:46 - INFO - interface handler: fortinet interface created
2025-08-02 10:31:46 - INFO - DEBUG: 接口 x2 XML生成完成
2025-08-02 10:31:46 - INFO - DEBUG: 处理物理接口 x3, is_subinterface: False
2025-08-02 10:31:46 - INFO - DEBUG: 为接口 x3 生成XML
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: interface_name
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data.keys())
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data
2025-08-02 10:31:46 - INFO - interface handler: create new interface
2025-08-02 10:31:46 - INFO - interface handler: using fortinet optimizer
2025-08-02 10:31:46 - INFO - interface handler: create fortinet interface
2025-08-02 10:31:46 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-02 10:31:46 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-02 10:31:46 - INFO - DEBUG: 最终的combined_access: []
2025-08-02 10:31:46 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-02 10:31:46 - INFO - interface handler: configured access control
2025-08-02 10:31:46 - INFO - interface handler: fortinet interface created
2025-08-02 10:31:46 - INFO - DEBUG: 接口 x3 XML生成完成
2025-08-02 10:31:46 - INFO - DEBUG: 处理物理接口 x4, is_subinterface: False
2025-08-02 10:31:46 - INFO - DEBUG: 为接口 x4 生成XML
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: interface_name
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data.keys())
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data
2025-08-02 10:31:46 - INFO - interface handler: create new interface
2025-08-02 10:31:46 - INFO - interface handler: using fortinet optimizer
2025-08-02 10:31:46 - INFO - interface handler: create fortinet interface
2025-08-02 10:31:46 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-02 10:31:46 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-02 10:31:46 - INFO - DEBUG: 最终的combined_access: []
2025-08-02 10:31:46 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-02 10:31:46 - INFO - interface handler: configured access control
2025-08-02 10:31:46 - INFO - interface handler: fortinet interface created
2025-08-02 10:31:46 - INFO - DEBUG: 接口 x4 XML生成完成
2025-08-02 10:31:46 - INFO - DEBUG: 处理物理接口 naf.root, is_subinterface: False
2025-08-02 10:31:46 - INFO - DEBUG: 为接口 naf.root 生成XML
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: interface_name
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data.keys())
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data
2025-08-02 10:31:46 - INFO - interface handler: create new interface
2025-08-02 10:31:46 - INFO - interface handler: using fortinet optimizer
2025-08-02 10:31:46 - INFO - interface handler: create fortinet interface
2025-08-02 10:31:46 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-02 10:31:46 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-02 10:31:46 - INFO - DEBUG: 最终的combined_access: []
2025-08-02 10:31:46 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-02 10:31:46 - INFO - interface handler: configured access control
2025-08-02 10:31:46 - INFO - interface handler: fortinet interface created
2025-08-02 10:31:46 - INFO - DEBUG: 接口 naf.root XML生成完成
2025-08-02 10:31:46 - INFO - DEBUG: 处理物理接口 l2t.root, is_subinterface: False
2025-08-02 10:31:46 - INFO - DEBUG: 为接口 l2t.root 生成XML
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: interface_name
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data.keys())
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data
2025-08-02 10:31:46 - INFO - interface handler: create new interface
2025-08-02 10:31:46 - INFO - interface handler: using fortinet optimizer
2025-08-02 10:31:46 - INFO - interface handler: create fortinet interface
2025-08-02 10:31:46 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-02 10:31:46 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-02 10:31:46 - INFO - DEBUG: 最终的combined_access: []
2025-08-02 10:31:46 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-02 10:31:46 - INFO - interface handler: configured access control
2025-08-02 10:31:46 - INFO - interface handler: fortinet interface created
2025-08-02 10:31:46 - INFO - DEBUG: 接口 l2t.root XML生成完成
2025-08-02 10:31:46 - INFO - DEBUG: 处理物理接口 ssl.root, is_subinterface: False
2025-08-02 10:31:46 - INFO - DEBUG: 为接口 ssl.root 生成XML
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: interface_name
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data.keys())
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data
2025-08-02 10:31:46 - INFO - interface handler: create new interface
2025-08-02 10:31:46 - INFO - interface handler: using fortinet optimizer
2025-08-02 10:31:46 - INFO - interface handler: create fortinet interface
2025-08-02 10:31:46 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-02 10:31:46 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-02 10:31:46 - INFO - DEBUG: 最终的combined_access: []
2025-08-02 10:31:46 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-02 10:31:46 - INFO - interface handler: configured access control
2025-08-02 10:31:46 - INFO - interface handler: fortinet interface created
2025-08-02 10:31:46 - INFO - DEBUG: 接口 ssl.root XML生成完成
2025-08-02 10:31:46 - INFO - DEBUG: 处理物理接口 BOS, is_subinterface: False
2025-08-02 10:31:46 - INFO - DEBUG: 为接口 BOS 生成XML
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: interface_name
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data.keys())
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data
2025-08-02 10:31:46 - INFO - interface handler: create new interface
2025-08-02 10:31:46 - INFO - interface handler: using fortinet optimizer
2025-08-02 10:31:46 - INFO - interface handler: create fortinet interface
2025-08-02 10:31:46 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-02 10:31:46 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-02 10:31:46 - INFO - DEBUG: 最终的combined_access: []
2025-08-02 10:31:46 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-02 10:31:46 - INFO - interface handler: configured access control
2025-08-02 10:31:46 - INFO - interface handler: fortinet interface created
2025-08-02 10:31:46 - INFO - DEBUG: 接口 BOS XML生成完成
2025-08-02 10:31:46 - INFO - DEBUG: 处理物理接口 SSL_VPN, is_subinterface: False
2025-08-02 10:31:46 - INFO - DEBUG: 为接口 SSL_VPN 生成XML
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: interface_name
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data.keys())
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: list(intf_data
2025-08-02 10:31:46 - INFO - interface handler: create new interface
2025-08-02 10:31:46 - INFO - interface handler: using fortinet optimizer
2025-08-02 10:31:46 - INFO - interface handler: create fortinet interface
2025-08-02 10:31:46 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-02 10:31:46 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-02 10:31:46 - INFO - interface handler: configured static ip
2025-08-02 10:31:46 - INFO - DEBUG: 最终的combined_access: []
2025-08-02 10:31:46 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-02 10:31:46 - INFO - interface handler: configured access control
2025-08-02 10:31:46 - INFO - interface handler: fortinet interface created
2025-08-02 10:31:46 - INFO - DEBUG: 接口 SSL_VPN XML生成完成
2025-08-02 10:31:46 - INFO - DEBUG: XML片段生成完成，长度: 68870
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: 'mgmt': 'Ge0/0', 'ha': 'Ge0/9', 'port1': 'Ge0/3', 'port2': 'Ge0/4', 'port3': 'Ge0/5', 'port4': 'Ge0/6', 'port5': 'Ge0/7', 'port6': 'Ge0/8', 'port7': 'Ge0/10', 'port8': 'Ge0/11', 'port9': 'Ge0/12', 'port10': 'Ge0/13', 'port11': 'Ge0/14', 'port12': 'Ge0/15', 'port13': 'Ge0/16', 'port14': 'Ge0/17', 'port15': 'Ge0/18', 'port16': 'Ge0/19', 'port17': 'Ge0/20', 'port18': 'Ge0/21', 'port19': 'Ge0/22', 'port20': 'Ge0/23', 'port21': 'Ge0/24', 'port22': 'Ge0/25', 'port23': 'Ge0/1', 'port24': 'Ge0/2', 'x1': 'TenGe0/0', 'x2': 'TenGe0/1', 'x3': 'TenGe0/2', 'x4': 'TenGe0/3', 'naf.root': 'Ge0/27', 'l2t.root': 'Ge0/28', 'ssl.root': 'Ge0/29', 'BOS': 'Ge0/30', 'SSL_VPN': 'Ge0/31', 'BTL_YSMSPR': 'Ge0/2.2200', 'BTL_YMK': 'Ge0/2.2500', 'BTL_WIFI': 'Ge0/2.2100', 'BTL_TEL': 'Ge0/2.3100', 'BTL_RKT': 'Ge0/2.2000', 'BTL_MYO': 'Ge0/2.2600', 'BTL_MGMT': 'Ge0/2.3000', 'BTL_GUV': 'Ge0/2.2400', 'BTL_ATC': 'Ge0/2.2700', 'BTL_BIDB': 'Ge0/2.1010', 'BTL_CAM': 'Ge0/2.3200', 'REAL_IP': 'Ge0/1.135', 'PROXY': 'Ge0/1.1001', 'TargitasTest': 'Ge0/1.33', 'NAC_LOGIN': 'Ge0/2.2', 'YESILTURT_RKT': 'TenGe0/1.114', 'YESILYURT_MYO': 'TenGe0/1.115', 'KALE_MYO': 'TenGe0/1.110', 'HEKIMHAN_MYO': 'TenGe0/1.119', 'DOGANSEHIR_MYO': 'TenGe0/1.116', 'DARENDE_MYO': 'TenGe0/1.117', 'ARAPGIR_MYO': 'TenGe0/1.112', 'AKCADAG_MYO': 'TenGe0/1.120'
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: 'mgmt'
2025-08-02 10:31:46 - INFO - DEBUG: 接口映射 1: mgmt -> Ge0/0 (类型: physical)
2025-08-02 10:31:46 - INFO - DEBUG: 接口映射 2: ha -> Ge0/9 (类型: physical)
2025-08-02 10:31:46 - INFO - DEBUG: 接口映射 3: port1 -> Ge0/3 (类型: physical)
2025-08-02 10:31:46 - INFO - DEBUG: 接口映射 4: port2 -> Ge0/4 (类型: physical)
2025-08-02 10:31:46 - INFO - DEBUG: 接口映射 5: port3 -> Ge0/5 (类型: physical)
2025-08-02 10:31:46 - INFO - DEBUG: 接口映射 6: port4 -> Ge0/6 (类型: physical)
2025-08-02 10:31:46 - INFO - DEBUG: 接口映射 7: port5 -> Ge0/7 (类型: physical)
2025-08-02 10:31:46 - INFO - DEBUG: 接口映射 8: port6 -> Ge0/8 (类型: physical)
2025-08-02 10:31:46 - INFO - DEBUG: 接口映射 9: port7 -> Ge0/10 (类型: physical)
2025-08-02 10:31:46 - INFO - DEBUG: 接口映射 10: port8 -> Ge0/11 (类型: physical)
2025-08-02 10:31:46 - INFO - 最终接口映射已创建
2025-08-02 10:31:46 - INFO - 接口映射已保存
2025-08-02 10:31:46 - INFO - 最终接口映射已保存
2025-08-02 10:31:46 - INFO - [待翻译] interface_processing_stage.final_mapping_saved
2025-08-02 10:31:46 - INFO - DEBUG: 最终映射文件已保存到: output\interface_mapping.json
2025-08-02 10:31:46 - INFO - DEBUG: 验证成功，文件确实存在: output\interface_mapping.json
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: 'mgmt': 'Ge0/0', 'ha': 'Ge0/9', 'port1': 'Ge0/3', 'port2': 'Ge0/4', 'port3': 'Ge0/5', 'port4': 'Ge0/6', 'port5': 'Ge0/7', 'port6': 'Ge0/8', 'port7': 'Ge0/10', 'port8': 'Ge0/11', 'port9': 'Ge0/12', 'port10': 'Ge0/13', 'port11': 'Ge0/14', 'port12': 'Ge0/15', 'port13': 'Ge0/16', 'port14': 'Ge0/17', 'port15': 'Ge0/18', 'port16': 'Ge0/19', 'port17': 'Ge0/20', 'port18': 'Ge0/21', 'port19': 'Ge0/22', 'port20': 'Ge0/23', 'port21': 'Ge0/24', 'port22': 'Ge0/25', 'port23': 'Ge0/1', 'port24': 'Ge0/2', 'x1': 'TenGe0/0', 'x2': 'TenGe0/1', 'x3': 'TenGe0/2', 'x4': 'TenGe0/3', 'naf.root': 'Ge0/27', 'l2t.root': 'Ge0/28', 'ssl.root': 'Ge0/29', 'BOS': 'Ge0/30', 'SSL_VPN': 'Ge0/31', 'BTL_YSMSPR': 'Ge0/2.2200', 'BTL_YMK': 'Ge0/2.2500', 'BTL_WIFI': 'Ge0/2.2100', 'BTL_TEL': 'Ge0/2.3100', 'BTL_RKT': 'Ge0/2.2000', 'BTL_MYO': 'Ge0/2.2600', 'BTL_MGMT': 'Ge0/2.3000', 'BTL_GUV': 'Ge0/2.2400', 'BTL_ATC': 'Ge0/2.2700', 'BTL_BIDB': 'Ge0/2.1010', 'BTL_CAM': 'Ge0/2.3200', 'REAL_IP': 'Ge0/1.135', 'PROXY': 'Ge0/1.1001', 'TargitasTest': 'Ge0/1.33', 'NAC_LOGIN': 'Ge0/2.2', 'YESILTURT_RKT': 'TenGe0/1.114', 'YESILYURT_MYO': 'TenGe0/1.115', 'KALE_MYO': 'TenGe0/1.110', 'HEKIMHAN_MYO': 'TenGe0/1.119', 'DOGANSEHIR_MYO': 'TenGe0/1.116', 'DARENDE_MYO': 'TenGe0/1.117', 'ARAPGIR_MYO': 'TenGe0/1.112', 'AKCADAG_MYO': 'TenGe0/1.120'
2025-08-02 10:31:46 - WARNING - 消息中缺少参数: 'mgmt'
2025-08-02 10:31:46 - INFO - DEBUG: 保存的映射内容: {'mgmt': 'Ge0/0', 'ha': 'Ge0/9', 'port1': 'Ge0/3', 'port2': 'Ge0/4', 'port3': 'Ge0/5', 'port4': 'Ge0/6', 'port5': 'Ge0/7', 'port6': 'Ge0/8', 'port7': 'Ge0/10', 'port8': 'Ge0/11', 'port9': 'Ge0/12', 'port10': 'Ge0/13', 'port11': 'Ge0/14', 'port12': 'Ge0/15', 'port13': 'Ge0/16', 'port14': 'Ge0/17', 'port15': 'Ge0/18', 'port16': 'Ge0/19', 'port17': 'Ge0/20', 'port18': 'Ge0/21', 'port19': 'Ge0/22', 'port20': 'Ge0/23', 'port21': 'Ge0/24', 'port22': 'Ge0/25', 'port23': 'Ge0/1', 'port24': 'Ge0/2', 'x1': 'TenGe0/0', 'x2': 'TenGe0/1', 'x3': 'TenGe0/2', 'x4': 'TenGe0/3', 'naf.root': 'Ge0/27', 'l2t.root': 'Ge0/28', 'ssl.root': 'Ge0/29', 'BOS': 'Ge0/30', 'SSL_VPN': 'Ge0/31', 'BTL_YSMSPR': 'Ge0/2.2200', 'BTL_YMK': 'Ge0/2.2500', 'BTL_WIFI': 'Ge0/2.2100', 'BTL_TEL': 'Ge0/2.3100', 'BTL_RKT': 'Ge0/2.2000', 'BTL_MYO': 'Ge0/2.2600', 'BTL_MGMT': 'Ge0/2.3000', 'BTL_GUV': 'Ge0/2.2400', 'BTL_ATC': 'Ge0/2.2700', 'BTL_BIDB': 'Ge0/2.1010', 'BTL_CAM': 'Ge0/2.3200', 'REAL_IP': 'Ge0/1.135', 'PROXY': 'Ge0/1.1001', 'TargitasTest': 'Ge0/1.33', 'NAC_LOGIN': 'Ge0/2.2', 'YESILTURT_RKT': 'TenGe0/1.114', 'YESILYURT_MYO': 'TenGe0/1.115', 'KALE_MYO': 'TenGe0/1.110', 'HEKIMHAN_MYO': 'TenGe0/1.119', 'DOGANSEHIR_MYO': 'TenGe0/1.116', 'DARENDE_MYO': 'TenGe0/1.117', 'ARAPGIR_MYO': 'TenGe0/1.112', 'AKCADAG_MYO': 'TenGe0/1.120'}
2025-08-02 10:31:46 - INFO - 接口处理完成：成功转换58/59，耗时1.59s
2025-08-02 10:31:46 - INFO - 接口处理：1个项目被跳过
2025-08-02 10:31:46 - INFO - 接口处理完成，转换: 58，安全区域: 1
2025-08-02 10:31:46 - INFO - 管道阶段：阶段完成
2025-08-02 10:31:46 - INFO - 阶段开始: fortigate_conversion -> service_processing (3/12)
2025-08-02 10:31:46 - INFO - 管道阶段：阶段开始
2025-08-02 10:31:46 - INFO - 开始服务对象处理
2025-08-02 10:31:46 - INFO - 为保持与原版一致性，跳过服务对象iprange转换
2025-08-02 10:31:46 - INFO - 开始服务对象处理，共128个项目
2025-08-02 10:31:47 - INFO - name_mapping_manager.name_mapped
2025-08-02 10:31:47 - INFO - name_mapping_manager.name_mapped
2025-08-02 10:31:47 - INFO - service_processing.skip_predefined_services
2025-08-02 10:31:47 - INFO - 服务对象处理完成：转换成功 106/122
2025-08-02 10:31:47 - INFO - 服务对象处理完成：成功转换106/122，耗时401ms
2025-08-02 10:31:47 - INFO - 管道阶段：阶段完成
2025-08-02 10:31:47 - INFO - 阶段开始: fortigate_conversion -> address_processing (4/12)
2025-08-02 10:31:47 - INFO - 管道阶段：阶段开始
2025-08-02 10:31:47 - INFO - 开始处理地址对象
2025-08-02 10:31:47 - INFO - 发现 0 个动态生成的地址对象
2025-08-02 10:31:47 - INFO - 地址对象统计: 原始=510, VIP=75, 动态生成=0, 总计=585
2025-08-02 10:31:47 - INFO - 找到 510 个地址对象
2025-08-02 10:31:47 - INFO - address_processor.found_vip_objects
2025-08-02 10:31:47 - INFO - 开始地址对象处理，共585个项目
2025-08-02 10:31:47 - WARNING - address_processor.vip_missing_extip
2025-08-02 10:31:47 - WARNING - address_processor.vip_missing_extip
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: EMS_ALL_UNKNOWN_CLIENTS (dynamic)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: EMS_ALL_UNMANAGEABLE_CLIENTS (dynamic)
2025-08-02 10:31:47 - WARNING - 地址对象 none 缺少subnet信息
2025-08-02 10:31:47 - WARNING - 地址对象 'none' 转换失败：[待翻译] address_processing.conversion_failed
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: login.microsoftonline.com (fqdn)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: login.microsoft.com (fqdn)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: login.windows.net (fqdn)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: gmail.com (fqdn)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: wildcard.google.com (fqdn)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: wildcard.dropbox.com (fqdn)
2025-08-02 10:31:47 - WARNING - 地址对象 all 缺少subnet信息
2025-08-02 10:31:47 - WARNING - 地址对象 'all' 转换失败：[待翻译] address_processing.conversion_failed
2025-08-02 10:31:47 - WARNING - 地址对象 FIREWALL_AUTH_PORTAL_ADDRESS 缺少subnet信息
2025-08-02 10:31:47 - WARNING - 地址对象 'FIREWALL_AUTH_PORTAL_ADDRESS' 转换失败：[待翻译] address_processing.conversion_failed
2025-08-02 10:31:47 - WARNING - 地址对象 FABRIC_DEVICE 缺少subnet信息
2025-08-02 10:31:47 - WARNING - 地址对象 'FABRIC_DEVICE' 转换失败：[待翻译] address_processing.conversion_failed
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: FCTEMS_ALL_FORTICLOUD_SERVERS (dynamic)
2025-08-02 10:31:47 - WARNING - 地址对象 DMZ_SUNUCULAR 缺少subnet信息
2025-08-02 10:31:47 - WARNING - 地址对象 'DMZ_SUNUCULAR' 转换失败：[待翻译] address_processing.conversion_failed
2025-08-02 10:31:47 - WARNING - 地址对象 ALL 缺少subnet信息
2025-08-02 10:31:47 - WARNING - 地址对象 'ALL' 转换失败：[待翻译] address_processing.conversion_failed
2025-08-02 10:31:47 - WARNING - 跳过接口关联地址对象: proxy_sunucusu
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: BlockMAC_01 (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: kimlikdogrulama.nvi.gov.tr (fqdn)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: kpsv2.nvi.gov.tr (fqdn)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: rustdesk.com (fqdn)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: osym.gov.tr (fqdn)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: vps.osym.gov.tr (fqdn)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: qtn.mac_00:00:00:00:00:00 (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: SSL_VPN (interface-subnet)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: BOS (interface-subnet)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: port15 (interface-subnet)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: port16 (interface-subnet)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: kamusm.gov.tr (fqdn)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: tubitak.gov.tr (fqdn)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: port24 (interface-subnet)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: BTL_YSMSPR (interface-subnet)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: BTL_YMK (interface-subnet)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: BTL_WIFI (interface-subnet)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: BTL_TEL (interface-subnet)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: BTL_RKT (interface-subnet)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: BTL_MYO (interface-subnet)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: BTL_MGMT (interface-subnet)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: BTL_GUV (interface-subnet)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: BTL_ATC (interface-subnet)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: BTL_BIDB (interface-subnet)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: BTL_CAM (interface-subnet)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: REAL_IP (interface-subnet)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: port23 (interface-subnet)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: PROXY (interface-subnet)
2025-08-02 10:31:47 - WARNING - 跳过接口关联地址对象: REKTOR_DU_MAC
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: hasan_Mac (mac)
2025-08-02 10:31:47 - WARNING - 跳过接口关联地址对象: REKTOR_TEL_MAC
2025-08-02 10:31:47 - WARNING - 跳过接口关联地址对象: hasan_masa
2025-08-02 10:31:47 - WARNING - 跳过接口关联地址对象: orhan_gunduz_masa
2025-08-02 10:31:47 - WARNING - 跳过接口关联地址对象: ahmet_ozkan_masa
2025-08-02 10:31:47 - WARNING - 跳过接口关联地址对象: deren_masa
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: depo.kamusm.gov.tr (fqdn)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: LOG_D (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: qtn.mac_d8:50:e6:4a:73:45 (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: NAC_LOGIN (interface-subnet)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: qtn.mac_a4:bb:6d:58:83:8a (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: qtn.mac_a4:bb:6d:64:68:1b (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: qtn.mac_50:9a:4c:2b:9f:e6 (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: qtn.mac_b2:90:12:e5:49:af (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: qtn.mac_30:85:a9:b1:f6:01 (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: qtn.mac_68:da:73:a8:fe:61 (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: qtn.mac_6c:24:08:09:a1:d2 (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: qtn.mac_00:e0:4c:68:00:86 (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: qtn.mac_30:85:a9:8e:de:d8 (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: qtn.mac_a4:bb:6d:64:68:73 (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: necmettin (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: hakan_toptas_cep (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: Sunucu_1 (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: SWTCH_LOG_ULASIM (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: NAC_TARGTAS_PC (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: akif_ates (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: NETWORK_LAN_2 (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: SWTCH_LOG_ULASIM1 (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: Kantinposchz (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: Kantnbarkod (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: qtn.mac_1e:21:b5:80:dc:4e (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: HLS_BZKRT (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: NCMTN_GL (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: HakanToptasMAC (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: NecmettinGul_Mac (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: ISATURGUT_3_MAC (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: ISATURGUT_MAC (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: ISATURGUT_MAC_II (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: qtn.mac_36:42:d7:80:a0:76 (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: battalgazi_k_pos_2 (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: NECMETTIN_GUL_MOBIL_ETH (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: REKTÖR_DİZÜSTÜ (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: HAKAN_TOPTAS_MOBIL_ETH (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: Yeşilyurt_Rek_Kenan_Bentli_Nejmiye_Hnm (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: Recep_Bentli_Ysl_Yrt_mkm (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: Yeşilyurt_Rek_Toplantı_Salonu (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: Yeşilyurt_Rek_Orhan_Gündüz (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: Yeşilyurt_Rek_İlhan_Erdem (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: api.hoptodesk.com (fqdn)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: vultrusercontent.com (fqdn)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: signal.hoptodesk.com (fqdn)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: signal2.hoptodesk.com (fqdn)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: turn.hoptodesk.com (fqdn)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: hoptodesk.com (fqdn)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: vultrusercontent (fqdn)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: turn.hoptodesk (fqdn)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: 0.pool.ntp.org (fqdn)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: time.nist.gov (fqdn)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: tr.pool.ntp.org (fqdn)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: kenan_bentli (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: KenanHoca_iPhone (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: rektor_mac_eski (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: rektor_mac_2 (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: yesilyurt_kantin_pos_mac (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: TPLİNK_WA850RE (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: rektor_mac_yeni (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: IsaTurgut_Kursu_MAC (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: TOPLANTISALONU_Arslantepe (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: Ali_Yuce_ProjeLoraCihaz (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: MehmetKakizLaptop (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: YESILTURT_RKT (interface-subnet)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: YESILYURT_MYO (interface-subnet)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: KALE_MYO (interface-subnet)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: HEKIMHAN_MYO (interface-subnet)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: DOGANSEHIR_MYO (interface-subnet)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: DARENDE_MYO (interface-subnet)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: ARAPGIR_MYO (interface-subnet)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: AKCADAG_MYO (interface-subnet)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: QNAP-STORAGE (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: ETISAN_BTL_PRS_POS (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: ETISAN_BTL_OGR_POS (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: ETISAN_YSL_MYO_POS (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: ETISAN_YSL_RKT_PRS_POS (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: ETISAN_YSL_RKT_OGR1_POS (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: isa_Sistem_odası (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: ORHAN_GUNDUZ_BAT. (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: Nejmiye_Hanım (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: ETISAN_BTL_POS_ (mac)
2025-08-02 10:31:47 - WARNING - 跳过不支持的地址类型: Ahmet_Selim_Yeşilyurt_Oda (mac)
2025-08-02 10:31:47 - WARNING - 地址对象 SSLVPN_TUNNEL_IPv6_ADDR1 缺少subnet信息
2025-08-02 10:31:47 - WARNING - 地址对象 'SSLVPN_TUNNEL_IPv6_ADDR1' 转换失败：[待翻译] address_processing.conversion_failed
2025-08-02 10:31:48 - INFO - DEBUG: Generated XML fragment length: 62596
2025-08-02 10:31:48 - INFO - DEBUG: Address result converted count: 448
2025-08-02 10:31:48 - INFO - DEBUG: Address result converted objects: ['ApiOzdegerlendirme_80', 'ApiOzdegerlendirme_443', 'ApiOzdegerlendirme_8080', 'TipOgrBirligi_80', 'TipOgrBirligi_443', 'SBSF_LAB_ORACLE_3389', 'AkademikTesvik_80', 'AkademikTesvik_443', 'ArizaTakip_80', 'ArizaTakip_443', 'SeminerSunucusu_80', 'SeminerSunucusu_443', 'SeminerSunucusu_7443', 'SeminerSunucusu_UDP_1935', 'SeminerSunucusu_UDP_16384-32768', 'WebSunucusu_80', 'WebSunucusu_443', 'WebSunucusu_UDP_53', 'DNS_UDP_53', 'Akupunktur_80', 'Akupunktur_443', 'RemoteMetin', 'Dspace', 'Active_Directory_Backup', 'Active_Directory', 'Yeteneksınavı_Otomasyon', 'Yeteneksınavı_WebServis', 'TipLMS', 'Utarit_Yemekhane', 'Uzem', 'Pbs_HTTP', 'Kimlik_Yönetim_Sistemi_SQL', 'Ek_Ders_Modülü', 'Obs', 'Nomysem_Sürekli_EgtMrk', 'Toplu_Mesaj_Ytt', 'Akademik_Performans_Sistemi', 'Web_Sunucusu', 'Web_Sitesi_AltBirimler', 'Katalog', 'Manage_Engin', 'EBYS', 'Yetkim', 'Kimlik_Yönetimi', 'Uzem_DB', 'Proxy_Sunucu_8080', 'BAPSİS', 'Pbs_HTTPS', 'Pbs_RDP', 'Pbs_MYSQL', 'Dspace_New', 'YOS_BASVURU', 'SRV_PTS', 'SSL_VPN_NAT', 'Avrupa_Birligi_Proje_Koordinatörlügü', 'EtikKurulu_VIP', 'DNS', 'Turnike_Gecis_Sistemleri', 'Proje_Pazari', 'MRTG', '************', 'dgrtnet_ddo_app', 'E_BOOK', '************50_VIP', '************51_VIP', '************52_VIP', '************53_VIP', '************54_VIP', '************55_VIP', 'Aryom', 'Mezun_Portal', 'Ziraat_Scada', 'UNIKYS_Kalite_Yazılımı', 'SSLVPN_TUNNEL_ADDR1', 'BATTALGAZI', 'DMZaddress', 'DNS_SERVER', 'KALE_IP_SANTRAL', 'ARAPGIR_IP_SANTRAL', 'DGNSHR_IP_SANTRAL', 'DARENDE_IP_SANTRAL', 'KALE_IP_SANTRAL_2', 'ARAPGIR_IP_SANTRAL_2', 'YYMYO_IP_SANTRAL', 'YYMYO_IP_SANTRAL_2', 'DGNSHR_IP_SANTRAL_2', 'DGNSHR_IP_SANTRAL_3', 'DARENDE_IP_SANTRAL_2', 'HEKIMHAN_IP_SANTRAL', 'HEKIMHAN_IP_SANTRAL_2', 'AKCADAG_IP_SANTRAL', 'AKCADAG_IP_SANTRAL_2', 'DARENDE_IP_SANTRAL_3', 'BTTLGZ_IP_SANTRAL', 'BTTLGZ_IP_SANTRAL_2', 'BTTLGZ_IP_SANTRAL_3', 'BTTLGZ_IP_SANTRAL_4', 'BTTLGZ_IP_SANTRAL_5', 'BTTLGZ_IP_SANTRAL_6', 'BTTLGZ_IP_SANTRAL_7', 'BTTLGZ_IP_SANTRAL_8', 'BTTLGZ_IP_SANTRAL_9', 'BTTLGZ_IP_SANTRAL_10', 'BTTLGZ_IP_SANTRAL_12', 'YESİLYURT_REKT', 'YESILYURT_SANTRAL_IP', 'YEMEKHANE_SERVER', 'ARAPGIR_TURNIKE', 'KALE_TURNIKE', 'YEMEKHANE_1', 'YEMEKHANE_3', 'YEMEKHANE_2', 'Merkez_Yemekhane_Harcama_I', 'DGNSHR_TURNIKE', 'DARENDE_TURNIKE', 'ERU_IP', 'CompleteAnatomy1', 'CompleteAnatomy2', 'CompleteAnatomy3', 'KPS_IP', 'OSYM_IP', 'PROTEL_IP', 'METIN_VPN_IP', '**************', '**************', '***************', '************', '10.0.0.0/8', '***********/16', '**********/16', 'MTU_WAN_IPS', '**************/32', '5M_Bilisim', '**************/32', '**************/32', '***************/32', '***************/32', '************/32', '**************/32', '*************/22', '*************/32', '**************/32', 'AP_CONTROLLER', 'KALE_AP_1', 'AKCADAG_AP', 'DARENDE_KANTIN_AP_1', 'DARENDE_KIZ_YURDU_AP', 'DARENDE_KIZ_YURDU_AP_2', 'DARENDE_ERKEK_YURDU_AP_1', 'METIN_GECICI', 'deren_unal', 'UMRAN', 'SRV_SEMINER_IP', 'SRV_UZEM_LMS', 'SRV_DIS_SEMINER_IP', 'SRV_DIS_UZEM_LMS_IP', 'SRV_WEB_ALTBIRIM_IP', 'SRV_DIS_WEB_ALTBIRIM_IP', 'SRV_YETKIM_IP', 'SRV_DIS_YETKIM_IP', 'SRV_OBS_IP', 'SRV_DIS_OBS_IP', 'SRV_OZDEGERLENDIRME_IP', 'SRV_DIS_OZDEGERLENDIRME_IP', 'SRV_WEB_ESKI_IP', 'SRV_DIS_WEB_ESKI_IP', 'SRV_TIP_OGR_BIRLIGI_IP', 'SRV_DIS_TIP_OGR_BIRLIGI_IP', 'SRV_SUREKLI_EGITIM_MRK_IP', 'SRV_DIS_SUREKLI_EGITIM_MRK_IP', 'SRV_DSPACE_IP', 'SRV_DIS_DSPACE_IP', 'SRV_AKADEMIK_TESVIK_IP', 'SRV_DIS_AKADEMIK_TESVIK_IP', 'SRV_WEB_YENI_IP', 'SRV_DIS_WEB_YENI_IP', 'SRV_PERS_OTOM_IP', 'SRV_DIS_PERS_OTOM_IP', 'BIDB_SERVER_DMZ', 'FIREWALL_DMZ_IP', 'OBS_PROLIZ_IP', 'AD_LOCAL_IP', 'REMOTE_SERVER', 'ip-adresim.net', 'Necmettin', 'KG_NIZAMIYE', 'KG_REKTORLUK', 'KG_MRK_YASAM_MERKEZI', 'KG_MRK_BATTALGAZI_MYO', 'KG_AKCADAG_MYO', 'KG_ARAPGIR_MYO', 'KG_KALE_MYO', 'KG_HEKIMHAN_MYO', 'KG_DARENDE_MYO', 'KG_YESILYURT_MYO', 'KG_YESILYURT_REKTORLUK', 'SRV_PERS_TKP', 'YESILYURT_SANTRAL_3', 'YESILYURT_SANTRAL_4', 'DSPACE_FIRMA_1', 'DSPACE_FIRMA_2', 'SRV_KIMLIK_IP', 'SRV_AD_BCK', 'SRV_EK_DERS_IP', 'SRV_SEM', 'turtep.yesevi.edu.tr', 'BackupServer', 'DataDomain', 'DataDomain2', 'METIN_VPN_IP_2', 'SRV_DIS_EK_DERS_IP', 'BAPSİS_İP', 'BAPSİS_DİS_İP', 'proxy', 'Proxy_Sunucusu', 'HEKIMHAN_AP', 'HEKİMHAN_AP', 'FILE_SERVER', 'OBYS_Server', 'PBS_server', 'Rustdesk', 'Rustdesk2', 'Rustdesk3', 'ArapgirAP', 'Battalgazi', 'kartokuyucu', 'NMS', '************', 'VRPA_WAN', 'VRPA_DATA', 'DSpace_New_IP', 'DSpace_New_DIS_IP', 'virüs_ıp_2', 'necmettin_gul', 'ARIZATAKIP', 'SRV_YETENEK_SİNAVİ_OTOMASYON_IP', 'SRV_YETENEK_SİNAVİ_WEB_IP', 'SRV_YOS_BASVURU_IP', 'Sunucu_PC', 'MTN_GECICI_IP', 'Pts_Ozgur_Zaman_Firma', 'REAL_IPLER', 'IKCU_RPA_1', 'IKCU_RPA_2', 'IKCU_RPA_3', 'IKCU_RPA_4', 'IKCU_RPA_5', 'IKCU_RPA_6', 'MTU_RPA_1', 'MTU_RPA_2', 'MTU_RPA_3', 'vRPA_CLS', 'vRPA_1', 'vRPA_3', 'vRPA_2', 'EBYS_US', 'miyase_coban', 'EBYS_DIS_IP', 'umran_turkmen', 'TargitasNAC', 'ibrahim_ip', 'halisbozkurt', 'KutuphaneServer', 'rektor', '***********', 'ek', 'ma', 'md', 'Merkez_Yemekhane_Pos', 'Merkez_Yemekhane_Harcama_II', 'Arapgir_Myo_Pos', 'Arapgir_Myo_Harcama', 'Akcadag_Myo_Pos', 'Akcadag_Myo_Harcama', 'Darende_Myo_Pos', 'Darende_Myo_Harcama', 'Dogansehir_Myo_Pos', 'Dogansehir_Myo_Harcama', 'Hekimhan_Myo_Pos', 'Hekimhan_Myo_Harcama', 'Kale_Myo_Pos', 'Kale_Myo_Harcama', 'Yesilyurt_Myo_Harcama', 'Yesilyurt_Myo_Pos', 'Merkez_Yemekhane_Harcama_III', 'Merkez_Yemekhane_Harcama_IV', 'Kıosk_I', 'Kıosk_II', '3bi', 'Veysel_Sahin_IP', 'ExtremeXIQController', 'sayistay_3', 'sayistay_4', 'log_dizustü', 'santral', 'battalgazi_nizamiye', 'seyhan_dogan_kart_okuma', 'test_ncgul', 'k_kemal', 'DARENDE_TUM_NET', 'DHCP_SERVER', 'HEKIMHAN_TUM_NET', 'YYMYO_Santral', 'ARUBA_SW', 'proxysunucu_2', 'Yeşilyurt_Rektörlük_Yemekhane', 'Merkez', 'Santral_Merkez1', 'Santral_Merkez2', 'Santral_Merkez3', 'Santral_Ogrenci_Yasam', 'Santral_Y_Yurt_MYO', 'Santral_B_Gazi_MYO', 'Santral_Darende', 'Santral_Akcadag', 'Santral_Kale', 'Santral_Dogansehir', 'Santral_YYurt_Rektorluk', 'halife_tel', 'FILE_SERVER_II', 'pts_sunucusu', 'Rektör_Makam', 'packet_deneme', 'ETIK_IP', 'DNS_DIS_IP', 'Yesilyurt_yemekhane_yeni', 'Yesilyurt_Utarit_X', 'Yeşilyurt_Rektörlük_Kios', 'Mücahit_PC', 'ISATURGUT_3', 'ISATURGUT_', 'ISATURGUT_2', 'KG_YESILYURT_KONTEYNER', 'betul_ozcinar', 'ssbf_lab_1', 'ssbf_lab_2', 'ssbf_lab_3', 'ssbf_lab_4', 'ssbf_lab_5', 'ssbf_lab_6', 'ssbf_lab_7', 'ssbf_lab_8', 'ssbf_lab_9', 'ssbf_lab_10', 'ssbf_lab_11', 'ssbf_lab_12', 'ssbf_lab_13', 'ssbf_lab_14', 'ssbf_lab_15', 'ssbf_lab_16', 'ssbf_lab_17', 'ssbf_lab_18', 'ssbf_lab_19', 'ssbf_lab_20', 'SBBF_Oracle_Opera_sunucu', 'SBSF_LAB_LOCAL_IP', 'KG_YESILYURT_SANAT_TASARIM', 'sbbf_lab_21', 'battalgazi_k_pos', 'ISA_DURGUT_IV', 'ISA_DURGUT_V', 'ISATURGUT_4', 'ISATURGUT_5', 'arapgit_santral', 'Arapgir_Santral', 'YesilyurtNizamiyePTS', 'Bat_ana_nzm_grs', 'Bat_ana_nzm_cks', 'Bat_rkt_grs', 'Bat_rkt_cks', 'Btl_ana_sunucu', 'SRV_PROJE_PAZARİ', 'FORTILOGGER', 'Yesilyurt_Rkt_Yemekhane_Pos', 'sahapc', 'metek_grup_ıp', 'rektorluk_toplantı_salonu', 'FortiLogger_IP', 'yesilyurt_kantin_pos', 'mehmet_kakiz', 'sefanur_korkmaz', 'ssbf_lab_ANAMAKINA', 'vultr', 'vultr2', '************', 'ncgul_tel', 'umran_tel', 'Ruijie_Ap1', 'Ruijie_Ap2', 'HekimhanSW', 'yesilyurt_Deneme', 'battalgazi_turnike_I', 'battalgazi_turnike_II', 'battalgazi_turnike_III', 'SRV_TurnikeSistemleri_Lokal_IP', 'mehmetvuralpc', 'mehmetvuralpc2', 'DARENDE_CAM', 'Akif_Ates_Darende', 'cacti', 'Yesilşyurt_Pts_Bilgisayari', 'YSLYRT_DMZ_address', '************', 'YSLYRT_DMZ', 'ahmet_selim_ozkan_makam_tv', 'vrpa', 'vRPAIP', 'test', 'RPA_CLS', 'RPA_1', 'RPA_2', 'RPA_3', 'RPA_PLUGIN', 'ETISAN_YSL_RKT_OGR_POS', 'ETISAN_BTL_POS', 'HALIFE_TEST', 'NAGIOS_NECMETTIN', 'ETISAN_POS_BAT', 'ETISAN_POS_BAT_2', 'app', 'TPLINKCNTRL', 'AP1', 'YSLMYOAP2', 'YSLMYOAP3', '************', '************', '************', '************', '************', '************', 'ARYOM_IP', 'aryomtest', 'KALE_ETISAN_POS', 'AKCADAG_POS', 'ARAPGİR_POS', 'HEKİMHAN_POS', 'DARENDE_POS', 'YESİLYURT_REK_POS', 'YESİLYURT_REK_POS2', 'YESİLYURT_POS_3', 'BATTALGAZİ_POS', 'KALE_POS_2', 'YESİLYURT_POS_1', 'Ziraat_Fak_Scada', '**********6', 'ETISAn_BTL_POS_', 'OMÜ_Erişim', 'NAS_OMV']
2025-08-02 10:31:48 - INFO - DEBUG: XML fragment preview: <network-obj xmlns="urn:ruijie:ntos">
  <address-set>
    <name>ApiOzdegerlendirme_80</name>
    <ip-set>
      <ip-address>*************/32</ip-address>
    </ip-set>
  </address-set>
  <address-set>
    <name>ApiOzdegerlendirme_443</name>
    <ip-set>
      <ip-address>*************/32</ip-address>
    </ip-set>
  </address-set>
  <address-set>
    <name>ApiOzdegerlendirme_8080</name>
    <ip-set>
      <ip-address>*************/32</ip-address>
    </ip-set>
  </address-set>
  <address-set>
  ...
2025-08-02 10:31:48 - INFO - 地址对象处理完成：成功转换448/585，耗时1.32s
2025-08-02 10:31:48 - INFO - 地址对象处理：7个项目转换失败
2025-08-02 10:31:48 - INFO - 地址对象处理：128个项目被跳过
2025-08-02 10:31:48 - INFO - 地址对象处理完成: 转换成功 448/585
2025-08-02 10:31:48 - INFO - 管道阶段：阶段完成
2025-08-02 10:31:48 - INFO - 阶段开始: fortigate_conversion -> address_group_processing (5/12)
2025-08-02 10:31:48 - INFO - 管道阶段：阶段开始
2025-08-02 10:31:48 - INFO - 开始处理地址对象组
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: gmail.com
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: wildcard.google.com
2025-08-02 10:31:48 - WARNING - 跳过无有效成员的地址组: G
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: login.microsoftonline.com
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: login.microsoft.com
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: login.windows.net
2025-08-02 10:31:48 - WARNING - 跳过无有效成员的地址组: Microsoft
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: hakan_toptas_cep
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: HakanToptasMAC
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: NECMETTIN_GUL_MOBIL_ETH
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: HAKAN_TOPTAS_MOBIL_ETH
2025-08-02 10:31:48 - WARNING - 跳过无有效成员的地址组: HAKAN_TOPTAS_ALL
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: deren_masa
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: NecmettinGul_Mac
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: HAKAN_TOPTAS_ALL
2025-08-02 10:31:48 - WARNING - address_group_processor.partial_members_valid
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: PlakaTanımaSistemi
2025-08-02 10:31:48 - WARNING - address_group_processor.partial_members_valid
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: qtn.mac_00:00:00:00:00:00
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: qtn.mac_d8:50:e6:4a:73:45
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: qtn.mac_a4:bb:6d:58:83:8a
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: qtn.mac_a4:bb:6d:64:68:1b
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: qtn.mac_50:9a:4c:2b:9f:e6
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: qtn.mac_b2:90:12:e5:49:af
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: qtn.mac_30:85:a9:b1:f6:01
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: qtn.mac_68:da:73:a8:fe:61
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: qtn.mac_6c:24:08:09:a1:d2
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: qtn.mac_00:e0:4c:68:00:86
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: qtn.mac_30:85:a9:8e:de:d8
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: qtn.mac_a4:bb:6d:64:68:73
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: qtn.mac_1e:21:b5:80:dc:4e
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: qtn.mac_36:42:d7:80:a0:76
2025-08-02 10:31:48 - WARNING - 跳过无有效成员的地址组: QuarantinedDevices
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: Merkez Yemekhane
2025-08-02 10:31:48 - WARNING - address_group_processor.partial_members_valid
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: ISATURGUT_MAC
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: ISATURGUT_MAC_II
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: IsaTurgut_Kursu_MAC
2025-08-02 10:31:48 - WARNING - address_group_processor.partial_members_valid
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: ETISAN_BTL_PRS_POS
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: ETISAN_BTL_OGR_POS
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: ETISAN_YSL_MYO_POS
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: ETISAN_YSL_RKT_PRS_POS
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: ETISAN_YSL_RKT_OGR1_POS
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: 0.pool.ntp.org
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: ETISAN_BTL_POS_
2025-08-02 10:31:48 - WARNING - address_group_processor.partial_members_valid
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: REKTOR_DU_MAC
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: REKTOR_TEL_MAC
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: deren_masa
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: Yemekhane_Cihazlar_Guncel
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: Sunucu_1
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: SWTCH_LOG_ULASIM
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: NAC_TARGTAS_PC
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: NETWORK_LAN_2
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: SWTCH_LOG_ULASIM1
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: Kantinposchz
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: Kantnbarkod
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: NecmettinGul_Mac
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: battalgazi_k_pos_2
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: REKTÖR_DİZÜSTÜ
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: Yeşilyurt_Rek_Kenan_Bentli_Nejmiye_Hnm
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: Recep_Bentli_Ysl_Yrt_mkm
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: Yeşilyurt_Rek_Toplantı_Salonu
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: Yeşilyurt_Rek_İlhan_Erdem
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: Yeşilyurt_Rek_Orhan_Gündüz
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: isa_turgut_konferans_salonu
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: HAKAN_TOPTAS_ALL
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: rektor_mac_eski
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: yesilyurt_kantin_pos_mac
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: 0.pool.ntp.org
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: turnike_sistemleri
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: rektor_mac_yeni
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: TOPLANTISALONU_Arslantepe
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: Ali_Yuce_ProjeLoraCihaz
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: NECMETTIN_GUL_MOBIL_ETH
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: MehmetKakizLaptop
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: ahmet_ozkan_masa
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: QNAP-STORAGE
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: ETISAN_POS
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: isa_Sistem_odası
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: ORHAN_GUNDUZ_BAT.
2025-08-02 10:31:48 - WARNING - address_group_processor.partial_members_valid
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: api.hoptodesk.com
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: hoptodesk.com
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: signal.hoptodesk.com
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: signal2.hoptodesk.com
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: turn.hoptodesk
2025-08-02 10:31:48 - WARNING - 跳过无有效成员的地址组: hoptodesk
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: 0.pool.ntp.org
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: time.nist.gov
2025-08-02 10:31:48 - WARNING - 地址组成员不存在于已转换地址对象中: tr.pool.ntp.org
2025-08-02 10:31:48 - WARNING - 跳过无有效成员的地址组: NTP
2025-08-02 10:31:48 - INFO - 地址对象组XML片段生成成功，包含 25 个地址组
2025-08-02 10:31:48 - INFO - 地址对象组处理完成: 转换成功 25/31
2025-08-02 10:31:48 - INFO - 管道阶段：阶段完成
2025-08-02 10:31:48 - INFO - 阶段开始: fortigate_conversion -> service_group_processing (6/12)
2025-08-02 10:31:48 - INFO - 管道阶段：阶段开始
2025-08-02 10:31:48 - INFO - 开始处理服务对象组
2025-08-02 10:31:48 - INFO - 找到 6 个服务对象组
2025-08-02 10:31:48 - INFO - service_group_processor.applying_name_sanitization
2025-08-02 10:31:48 - INFO - ntos_name_validator.name_sanitized
2025-08-02 10:31:48 - INFO - ntos_name_validator.name_sanitized
2025-08-02 10:31:48 - INFO - ntos_name_validator.name_sanitized
2025-08-02 10:31:48 - INFO - ntos_name_validator.name_sanitized
2025-08-02 10:31:48 - INFO - ntos_name_validator.service_groups_sanitized
2025-08-02 10:31:48 - INFO - service_group_processor.name_sanitization_stats
2025-08-02 10:31:48 - INFO - service_group_processor.name_changed
2025-08-02 10:31:48 - INFO - service_group_processor.name_changed
2025-08-02 10:31:48 - INFO - service_group_processor.name_changed
2025-08-02 10:31:48 - INFO - service_group_processor.name_changed
2025-08-02 10:31:48 - INFO - 服务对象组处理完成: 转换成功 6/6
2025-08-02 10:31:48 - INFO - 管道阶段：阶段完成
2025-08-02 10:31:48 - INFO - 阶段开始: fortigate_conversion -> zone_processing (7/12)
2025-08-02 10:31:48 - INFO - 管道阶段：阶段开始
2025-08-02 10:31:48 - INFO - 开始处理安全区域
2025-08-02 10:31:48 - INFO - Using interface mapping from context: 36 mappings
2025-08-02 10:31:48 - INFO - Interface mapping set directly: 36 mappings
2025-08-02 10:31:48 - INFO - 找到 4 个区域配置，操作模式: route
2025-08-02 10:31:48 - INFO - Interface mapping loaded
2025-08-02 10:31:48 - INFO - Interface mapping loaded (flat format): 58 mappings
2025-08-02 10:31:48 - WARNING - 消息中缺少参数: 'LAN', 'WAN', 'untrust', 'trust', 'MGMT', 'DMZ'
2025-08-02 10:31:48 - WARNING - Zone skipped: untrust - 区域的所有接口都未配置映射关系或区域没有配置接口
2025-08-02 10:31:48 - WARNING - Zone skipped: trust - 区域的所有接口都未配置映射关系或区域没有配置接口
2025-08-02 10:31:48 - INFO - 区域处理完成: 转换成功 4/4
2025-08-02 10:31:48 - INFO - 管道阶段：阶段完成
2025-08-02 10:31:48 - INFO - 阶段开始: fortigate_conversion -> time_range_processing (8/12)
2025-08-02 10:31:48 - INFO - 管道阶段：阶段开始
2025-08-02 10:31:48 - INFO - 开始处理时间对象
2025-08-02 10:31:48 - INFO - 找到 3 个时间对象
2025-08-02 10:31:48 - INFO - 开始生成时间对象XML片段，转换结果: 3 个
2025-08-02 10:31:48 - INFO - XML片段生成：找到 3 个转换的时间对象
2025-08-02 10:31:48 - INFO - XML片段生成成功，长度: 1308
2025-08-02 10:31:48 - INFO - XML片段生成完成，长度: 1308
2025-08-02 10:31:48 - INFO - 时间对象处理完成: 转换成功 3/3
2025-08-02 10:31:48 - INFO - 管道阶段：阶段完成
2025-08-02 10:31:48 - INFO - 阶段开始: fortigate_conversion -> dns_processing (9/12)
2025-08-02 10:31:48 - INFO - 管道阶段：阶段开始
2025-08-02 10:31:48 - INFO - 开始DNS处理
2025-08-02 10:31:48 - INFO - 使用已配置的DNS服务器: 2 个
2025-08-02 10:31:48 - INFO - DNS配置处理完成: DNS服务器 2 个，静态域名 0 个
2025-08-02 10:31:48 - INFO - DNS处理完成: DNS服务器 2 个，静态域名 0 个
2025-08-02 10:31:48 - INFO - 管道阶段：阶段完成
2025-08-02 10:31:48 - INFO - 阶段开始: fortigate_conversion -> static_route_processing (10/12)
2025-08-02 10:31:48 - INFO - 管道阶段：阶段开始
2025-08-02 10:31:48 - INFO - 开始静态路由处理
2025-08-02 10:31:48 - INFO - 检测到列表格式的静态路由数据，转换为字典格式
2025-08-02 10:31:48 - INFO - 转换后找到 8 个静态路由
2025-08-02 10:31:48 - INFO - 静态路由处理完成: 转换成功 8 个，跳过 0 个，失败 0 个
2025-08-02 10:31:48 - INFO - 静态路由处理完成: 转换成功 8/8, 分组 7 个
2025-08-02 10:31:48 - INFO - 管道阶段：阶段完成
2025-08-02 10:31:48 - INFO - 阶段开始: fortigate_conversion -> fortigate_policy_conversion (11/12)
2025-08-02 10:31:48 - INFO - 管道阶段：阶段开始
2025-08-02 10:31:48 - ERROR - 数据上下文添加错误: fortigate_policy_stage.interface_mapping_validation_failed (阶段: None)
2025-08-02 10:31:48 - ERROR - 数据上下文添加错误: pipeline_stage.input_validation_failed (阶段: fortigate_policy_conversion)
2025-08-02 10:31:48 - ERROR - Pipeline stopped due to error: fortigate_conversion -> fortigate_policy_conversion
2025-08-02 10:31:48 - INFO - Pipeline execution completed: fortigate_conversion, state: failed, duration: 3.81s, errors: 2, warnings: 0
2025-08-02 10:31:48 - INFO - 🔍 DEBUG: 新架构管道执行完成，状态: failed
