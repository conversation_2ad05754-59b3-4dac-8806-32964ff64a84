2025-08-04 14:37:13 - INFO - ✅ 四层优化架构导入成功
2025-08-04 14:37:13 - INFO - 🔧 开始创建四层优化组件...
2025-08-04 14:37:13 - INFO - ✅ 四层优化组件创建成功
2025-08-04 14:37:13 - INFO - ✅ 组件创建成功: processors=3个处理器
2025-08-04 14:37:13 - INFO - ❌ 四层优化架构初始化失败: 'SimpleConfigManager' object has no attribute 'get'
2025-08-04 14:37:13 - INFO - ❌ 错误类型: AttributeError
2025-08-04 14:37:13 - WARNING - 消息中缺少参数: unknown_parameters
2025-08-04 14:37:13 - WARNING - 格式错误: Replacement index 0 out of range for positional args tuple
2025-08-04 14:37:13 - INFO - ❌ 错误堆栈: Traceback (most recent call last):
  File "E:\code\config-converter\engine\processing\stages\four_tier_optimization_stage.py", line 156, in __init__
    config = self._get_optimization_config()
  File "E:\code\config-converter\engine\processing\stages\four_tier_optimization_stage.py", line 195, in _get_optimization_config
    user_config = self.config_manager.get('four_tier_optimization', {})
AttributeError: 'SimpleConfigManager' object has no attribute 'get'

2025-08-04 14:37:13 - INFO - 四层优化策略不可用，跳过优化处理
