2025-08-04 15:05:34 - INFO - ✅ 四层优化架构导入成功
2025-08-04 15:05:34 - INFO - 🔧 开始创建四层优化组件...
2025-08-04 15:05:34 - INFO - ✅ 四层优化组件创建成功
2025-08-04 15:05:34 - INFO - ✅ 组件创建成功: processors=3个处理器
2025-08-04 15:05:34 - WARNING - 消息中缺少参数: 'safe_skip': 1.0, 'conditional_skip': 0.8, 'important_retain': 0.6, 'critical_full': 0.4
2025-08-04 15:05:34 - WARNING - 消息中缺少参数: 'quality_threshold'
2025-08-04 15:05:34 - INFO - ✅ 配置获取成功: {'quality_threshold': 0.95, 'performance_target': 0.35, 'enable_parallel_processing': True, 'max_processing_time': 300, 'tier_weights': {'safe_skip': 1.0, 'conditional_skip': 0.8, 'important_retain': 0.6, 'critical_full': 0.4}, 'enabled': True, 'target_optimization_ratio': 0.4, 'performance_monitoring': True}
2025-08-04 15:05:34 - INFO - ✅ 四层优化架构初始化成功
2025-08-04 15:05:34 - INFO - 🚀 开始执行完整版四层优化策略
2025-08-04 15:05:34 - INFO - 🔍 开始提取FortiGate配置段落...
2025-08-04 15:05:34 - INFO - ✅ 找到config_data，包含 4 个配置项
2025-08-04 15:05:34 - INFO - ✅ 找到fortigate_data，包含 4 个配置项
2025-08-04 15:05:34 - INFO - 📋 配置段落提取完成，共提取 25 个段落
2025-08-04 15:05:34 - INFO -   - interface_config: 2 行配置
2025-08-04 15:05:34 - INFO -   - firewall_policy: 2 行配置
2025-08-04 15:05:34 - INFO -   - address_config: 2 行配置
2025-08-04 15:05:34 - INFO -   - service_config: 2 行配置
2025-08-04 15:05:34 - INFO -   - fortigate_interfaces: 2 行配置
2025-08-04 15:05:34 - INFO -   - gui_settings: 3 行配置
2025-08-04 15:05:34 - INFO -   - log_settings: 3 行配置
2025-08-04 15:05:34 - INFO -   - webfilter_config: 3 行配置
2025-08-04 15:05:34 - INFO -   - antivirus_config: 3 行配置
2025-08-04 15:05:34 - INFO -   - ips_config: 3 行配置
2025-08-04 15:05:34 - INFO -   - application_control: 3 行配置
2025-08-04 15:05:34 - INFO -   - dlp_config: 3 行配置
2025-08-04 15:05:34 - INFO -   - endpoint_control: 3 行配置
2025-08-04 15:05:34 - INFO -   - spam_filter: 3 行配置
2025-08-04 15:05:34 - INFO -   - dnsfilter_config: 3 行配置
2025-08-04 15:05:34 - INFO -   - interface_critical: 3 行配置
2025-08-04 15:05:34 - INFO -   - firewall_policy_critical: 3 行配置
2025-08-04 15:05:34 - INFO -   - zone_critical: 3 行配置
2025-08-04 15:05:34 - INFO -   - route_critical: 3 行配置
2025-08-04 15:05:34 - INFO -   - service_objects: 3 行配置
2025-08-04 15:05:34 - INFO -   - address_objects: 3 行配置
2025-08-04 15:05:34 - INFO -   - schedule_objects: 3 行配置
2025-08-04 15:05:34 - INFO -   - user_objects: 3 行配置
2025-08-04 15:05:34 - INFO -   - group_objects: 3 行配置
2025-08-04 15:05:34 - INFO -   - profile_objects: 3 行配置
2025-08-04 15:05:34 - INFO - 📋 已提取配置段落: 25个
2025-08-04 15:05:34 - INFO - 🔍 分析段落: interface_config (2 行)
2025-08-04 15:05:34 - INFO - 🔧 完整处理: interface_config (critical_full)
2025-08-04 15:05:34 - INFO - 🔍 分析段落: firewall_policy (2 行)
2025-08-04 15:05:34 - INFO - 🔧 完整处理: firewall_policy (critical_full)
2025-08-04 15:05:34 - INFO - 🔍 分析段落: address_config (2 行)
2025-08-04 15:05:34 - INFO - ⚡ 简化处理: address_config (conditional_skip)
2025-08-04 15:05:34 - INFO - 🔍 分析段落: service_config (2 行)
2025-08-04 15:05:34 - INFO - ⚡ 简化处理: service_config (conditional_skip)
2025-08-04 15:05:34 - INFO - 🔍 分析段落: fortigate_interfaces (2 行)
2025-08-04 15:05:34 - INFO - 🔧 完整处理: fortigate_interfaces (critical_full)
2025-08-04 15:05:34 - INFO - 🔍 分析段落: gui_settings (3 行)
2025-08-04 15:05:34 - INFO - ⏭️ 跳过段落: gui_settings (safe_skip)
2025-08-04 15:05:34 - INFO - 🔍 分析段落: log_settings (3 行)
2025-08-04 15:05:34 - INFO - ⏭️ 跳过段落: log_settings (safe_skip)
2025-08-04 15:05:34 - INFO - 🔍 分析段落: webfilter_config (3 行)
2025-08-04 15:05:34 - INFO - ⏭️ 跳过段落: webfilter_config (safe_skip)
2025-08-04 15:05:34 - INFO - 🔍 分析段落: antivirus_config (3 行)
2025-08-04 15:05:34 - INFO - ⏭️ 跳过段落: antivirus_config (safe_skip)
2025-08-04 15:05:34 - INFO - 🔍 分析段落: ips_config (3 行)
2025-08-04 15:05:34 - INFO - ⏭️ 跳过段落: ips_config (safe_skip)
2025-08-04 15:05:34 - INFO - 🔍 分析段落: application_control (3 行)
2025-08-04 15:05:34 - INFO - ⏭️ 跳过段落: application_control (safe_skip)
2025-08-04 15:05:34 - INFO - 🔍 分析段落: dlp_config (3 行)
2025-08-04 15:05:34 - INFO - ⏭️ 跳过段落: dlp_config (safe_skip)
2025-08-04 15:05:34 - INFO - 🔍 分析段落: endpoint_control (3 行)
2025-08-04 15:05:34 - INFO - ⏭️ 跳过段落: endpoint_control (safe_skip)
2025-08-04 15:05:34 - INFO - 🔍 分析段落: spam_filter (3 行)
2025-08-04 15:05:34 - INFO - ⏭️ 跳过段落: spam_filter (safe_skip)
2025-08-04 15:05:34 - INFO - 🔍 分析段落: dnsfilter_config (3 行)
2025-08-04 15:05:34 - INFO - ⏭️ 跳过段落: dnsfilter_config (safe_skip)
2025-08-04 15:05:34 - INFO - 🔍 分析段落: interface_critical (3 行)
2025-08-04 15:05:34 - INFO - 🔧 完整处理: interface_critical (critical_full)
2025-08-04 15:05:34 - INFO - 🔍 分析段落: firewall_policy_critical (3 行)
2025-08-04 15:05:34 - INFO - 🔧 完整处理: firewall_policy_critical (critical_full)
2025-08-04 15:05:34 - INFO - 🔍 分析段落: zone_critical (3 行)
2025-08-04 15:05:34 - INFO - 🔧 完整处理: zone_critical (critical_full)
2025-08-04 15:05:34 - INFO - 🔍 分析段落: route_critical (3 行)
2025-08-04 15:05:34 - INFO - 🔧 完整处理: route_critical (critical_full)
2025-08-04 15:05:34 - INFO - 🔍 分析段落: service_objects (3 行)
2025-08-04 15:05:34 - INFO - ⚡ 简化处理: service_objects (conditional_skip)
2025-08-04 15:05:34 - INFO - 🔍 分析段落: address_objects (3 行)
2025-08-04 15:05:34 - INFO - ⚡ 简化处理: address_objects (conditional_skip)
2025-08-04 15:05:34 - INFO - 🔍 分析段落: schedule_objects (3 行)
2025-08-04 15:05:34 - INFO - ⚡ 简化处理: schedule_objects (conditional_skip)
2025-08-04 15:05:34 - INFO - 🔍 分析段落: user_objects (3 行)
2025-08-04 15:05:34 - INFO - ⚡ 简化处理: user_objects (conditional_skip)
2025-08-04 15:05:34 - INFO - 🔍 分析段落: group_objects (3 行)
2025-08-04 15:05:34 - INFO - ⚡ 简化处理: group_objects (conditional_skip)
2025-08-04 15:05:34 - INFO - 🔍 分析段落: profile_objects (3 行)
2025-08-04 15:05:34 - INFO - ⚡ 简化处理: profile_objects (conditional_skip)
2025-08-04 15:05:34 - INFO - 📋 四层优化策略详细执行报告
2025-08-04 15:05:34 - INFO - 🏷️ Tier1 (安全跳过): 10个
2025-08-04 15:05:34 - INFO - 🏷️ Tier2 (条件跳过): 8个
2025-08-04 15:05:34 - INFO - 🏷️ Tier3 (重要保留): 0个
2025-08-04 15:05:34 - INFO - 🏷️ Tier4 (关键完整): 7个
2025-08-04 15:05:34 - INFO - 
📊 优化统计 - 总计: 25, 跳过: 10, 简化: 8, 完整: 7
2025-08-04 15:05:34 - INFO - 🎯 优化比例: 72.0%
2025-08-04 15:05:34 - INFO - ⏱️ 执行时间: 0.02秒
2025-08-04 15:05:34 - INFO - 💾 预计节省时间: 66.00秒
2025-08-04 15:05:34 - INFO - 🏆 质量分数: 0.94
2025-08-04 15:05:34 - INFO - ✅ 完整版四层优化策略执行成功！
