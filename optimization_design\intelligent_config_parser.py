"""
FortiGate配置解析器性能优化 - 智能配置段落识别
目标：将配置解析时间从15分钟优化到3-5分钟
"""

import re
import logging
from typing import Dict, List, Tuple, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass
from enum import Enum

class ConfigSectionType(Enum):
    """配置段落类型枚举"""
    INTERFACE = "interface"
    POLICY = "policy"
    ADDRESS = "address"
    SERVICE = "service"
    ZONE = "zone"
    ROUTE = "route"
    SYSTEM = "system"
    UNKNOWN = "unknown"
    SKIP = "skip"  # 可跳过的配置段落

@dataclass
class ConfigSection:
    """配置段落数据结构"""
    section_type: ConfigSectionType
    start_line: int
    end_line: int
    content: List[str]
    priority: int = 1  # 处理优先级，1=最高，5=最低

class IntelligentConfigParser:
    """智能配置解析器"""
    
    def __init__(self):
        self.section_patterns = self._init_section_patterns()
        self.skip_patterns = self._init_skip_patterns()
        self.batch_size = 1000
        self.max_workers = 4
        
    def _init_section_patterns(self) -> Dict[str, ConfigSectionType]:
        """初始化配置段落识别模式"""
        return {
            r'config system interface': ConfigSectionType.INTERFACE,
            r'config firewall policy': ConfigSectionType.POLICY,
            r'config firewall address': ConfigSectionType.ADDRESS,
            r'config firewall service': ConfigSectionType.SERVICE,
            r'config system zone': ConfigSectionType.ZONE,
            r'config router static': ConfigSectionType.ROUTE,
            r'config system global': ConfigSectionType.SYSTEM,
            r'config system admin': ConfigSectionType.SYSTEM,
            r'config system dns': ConfigSectionType.SYSTEM,
        }
    
    def _init_skip_patterns(self) -> List[str]:
        """初始化可跳过的配置模式"""
        return [
            r'config system fortiguard',
            r'config antivirus profile',
            r'config webfilter profile',
            r'config application list',
            r'config ips sensor',
            r'config emailfilter profile',
            r'config dlp sensor',
            r'config spamfilter profile',
            r'config icap profile',
            r'config waf profile',
            r'config voip profile',
            r'config log',
            r'config report',
            r'config alertemail',
        ]
    
    def identify_config_sections(self, lines: List[str]) -> List[ConfigSection]:
        """智能识别配置段落"""
        sections = []
        current_section = None
        section_stack = []
        
        for i, line in enumerate(lines):
            line_stripped = line.strip()
            
            # 检查是否为配置段落开始
            section_type = self._identify_section_type(line_stripped)
            
            if section_type != ConfigSectionType.UNKNOWN:
                # 结束当前段落
                if current_section:
                    current_section.end_line = i - 1
                    current_section.content = lines[current_section.start_line:i]
                    sections.append(current_section)
                
                # 开始新段落
                current_section = ConfigSection(
                    section_type=section_type,
                    start_line=i,
                    end_line=i,
                    content=[],
                    priority=self._get_section_priority(section_type)
                )
                section_stack.append(current_section)
            
            elif line_stripped == 'end' and section_stack:
                # 段落结束
                if current_section:
                    current_section.end_line = i
                    current_section.content = lines[current_section.start_line:i+1]
                    sections.append(current_section)
                
                section_stack.pop()
                current_section = section_stack[-1] if section_stack else None
        
        # 处理最后一个段落
        if current_section:
            current_section.end_line = len(lines) - 1
            current_section.content = lines[current_section.start_line:]
            sections.append(current_section)
        
        return sections
    
    def _identify_section_type(self, line: str) -> ConfigSectionType:
        """识别配置行的段落类型"""
        # 检查是否可跳过
        for pattern in self.skip_patterns:
            if re.match(pattern, line, re.IGNORECASE):
                return ConfigSectionType.SKIP
        
        # 检查已知段落类型
        for pattern, section_type in self.section_patterns.items():
            if re.match(pattern, line, re.IGNORECASE):
                return section_type
        
        return ConfigSectionType.UNKNOWN
    
    def _get_section_priority(self, section_type: ConfigSectionType) -> int:
        """获取段落处理优先级"""
        priority_map = {
            ConfigSectionType.INTERFACE: 1,
            ConfigSectionType.ADDRESS: 2,
            ConfigSectionType.SERVICE: 2,
            ConfigSectionType.ZONE: 3,
            ConfigSectionType.POLICY: 4,
            ConfigSectionType.ROUTE: 4,
            ConfigSectionType.SYSTEM: 5,
            ConfigSectionType.SKIP: 9,
            ConfigSectionType.UNKNOWN: 8,
        }
        return priority_map.get(section_type, 5)
    
    def parallel_section_processing(self, sections: List[ConfigSection]) -> Dict:
        """并行处理配置段落"""
        # 按优先级排序
        sections.sort(key=lambda x: x.priority)
        
        # 分组处理
        high_priority = [s for s in sections if s.priority <= 2]
        medium_priority = [s for s in sections if 3 <= s.priority <= 5]
        low_priority = [s for s in sections if s.priority > 5]
        
        results = {
            'interfaces': [],
            'policies': [],
            'addresses': [],
            'services': [],
            'zones': [],
            'routes': [],
            'system': [],
            'warnings': []
        }
        
        # 顺序处理高优先级（依赖关系）
        for section in high_priority:
            self._process_single_section(section, results)
        
        # 并行处理中优先级
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_section = {
                executor.submit(self._process_single_section, section, {}): section 
                for section in medium_priority
            }
            
            for future in as_completed(future_to_section):
                section = future_to_section[future]
                try:
                    section_result = future.result()
                    self._merge_results(results, section_result)
                except Exception as exc:
                    logging.error(f'Section {section.section_type} generated an exception: {exc}')
        
        # 批量处理低优先级（跳过或简单记录）
        self._batch_process_low_priority(low_priority, results)
        
        return results
    
    def _process_single_section(self, section: ConfigSection, results: Dict) -> Dict:
        """处理单个配置段落"""
        if section.section_type == ConfigSectionType.SKIP:
            # 跳过处理，仅记录
            return {'skipped': 1}
        
        if section.section_type == ConfigSectionType.UNKNOWN:
            # 批量处理未知段落，减少日志
            return {'unknown': 1}
        
        # 根据段落类型调用相应的处理器
        processor_map = {
            ConfigSectionType.INTERFACE: self._process_interface_section,
            ConfigSectionType.POLICY: self._process_policy_section,
            ConfigSectionType.ADDRESS: self._process_address_section,
            ConfigSectionType.SERVICE: self._process_service_section,
            ConfigSectionType.ZONE: self._process_zone_section,
            ConfigSectionType.ROUTE: self._process_route_section,
            ConfigSectionType.SYSTEM: self._process_system_section,
        }
        
        processor = processor_map.get(section.section_type)
        if processor:
            return processor(section.content)
        
        return {}
    
    def _process_interface_section(self, content: List[str]) -> Dict:
        """处理接口配置段落"""
        # 实现接口解析逻辑
        return {'interfaces': []}
    
    def _process_policy_section(self, content: List[str]) -> Dict:
        """处理策略配置段落"""
        # 实现策略解析逻辑
        return {'policies': []}
    
    def _process_address_section(self, content: List[str]) -> Dict:
        """处理地址配置段落"""
        # 实现地址解析逻辑
        return {'addresses': []}
    
    def _process_service_section(self, content: List[str]) -> Dict:
        """处理服务配置段落"""
        # 实现服务解析逻辑
        return {'services': []}
    
    def _process_zone_section(self, content: List[str]) -> Dict:
        """处理区域配置段落"""
        # 实现区域解析逻辑
        return {'zones': []}
    
    def _process_route_section(self, content: List[str]) -> Dict:
        """处理路由配置段落"""
        # 实现路由解析逻辑
        return {'routes': []}
    
    def _process_system_section(self, content: List[str]) -> Dict:
        """处理系统配置段落"""
        # 实现系统解析逻辑
        return {'system': []}
    
    def _batch_process_low_priority(self, sections: List[ConfigSection], results: Dict):
        """批量处理低优先级段落"""
        skip_count = sum(1 for s in sections if s.section_type == ConfigSectionType.SKIP)
        unknown_count = sum(1 for s in sections if s.section_type == ConfigSectionType.UNKNOWN)
        
        if skip_count > 0:
            logging.info(f"跳过 {skip_count} 个不需要转换的配置段落")
        
        if unknown_count > 0:
            logging.warning(f"发现 {unknown_count} 个未知配置段落")
        
        results['warnings'].append(f"批量处理了 {len(sections)} 个低优先级配置段落")
    
    def _merge_results(self, main_results: Dict, section_result: Dict):
        """合并处理结果"""
        for key, value in section_result.items():
            if key in main_results:
                if isinstance(main_results[key], list):
                    main_results[key].extend(value if isinstance(value, list) else [value])
                else:
                    main_results[key] += value
            else:
                main_results[key] = value

class OptimizedFortigateParser:
    """优化后的FortiGate解析器"""
    
    def __init__(self):
        self.intelligent_parser = IntelligentConfigParser()
        self.cache = {}
        
    def parse_large_config(self, file_path: str) -> Dict:
        """解析大型配置文件的优化入口"""
        # 1. 读取文件
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        logging.info(f"开始智能解析配置文件，共 {len(lines)} 行")
        
        # 2. 智能识别配置段落
        sections = self.intelligent_parser.identify_config_sections(lines)
        logging.info(f"识别到 {len(sections)} 个配置段落")
        
        # 3. 并行处理配置段落
        results = self.intelligent_parser.parallel_section_processing(sections)
        
        logging.info("配置解析完成")
        return results
