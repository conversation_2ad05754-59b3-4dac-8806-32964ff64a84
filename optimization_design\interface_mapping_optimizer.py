"""
FortiGate接口映射优化器
解决ssl.root等虚拟接口映射问题，减少策略验证失败
"""

import logging
from typing import Dict, List, Optional, Set
from dataclasses import dataclass
from enum import Enum

class InterfaceType(Enum):
    """接口类型枚举"""
    PHYSICAL = "physical"      # 物理接口 (port1, port2, etc.)
    VLAN = "vlan"             # VLAN接口 (Vlan100, etc.)
    TUNNEL = "tunnel"         # 隧道接口 (ssl.root, ipsec, etc.)
    LOOPBACK = "loopback"     # 环回接口
    AGGREGATE = "aggregate"   # 聚合接口
    VIRTUAL = "virtual"       # 虚拟接口
    ZONE = "zone"            # 安全区域（非真实接口）

@dataclass
class InterfaceMapping:
    """接口映射配置"""
    fortigate_name: str
    ntos_name: str
    interface_type: InterfaceType
    is_virtual: bool = False
    parent_interface: Optional[str] = None
    zone_name: Optional[str] = None

class EnhancedInterfaceMapper:
    """增强的接口映射器"""
    
    def __init__(self):
        self.physical_mappings = {}
        self.virtual_mappings = {}
        self.zone_mappings = {}
        self.tunnel_mappings = {}
        self.auto_generated_mappings = {}
        
        # 初始化默认映射规则
        self._init_default_mappings()
    
    def _init_default_mappings(self):
        """初始化默认映射规则"""
        # SSL VPN隧道接口映射
        self.tunnel_mappings.update({
            'ssl.root': InterfaceMapping(
                fortigate_name='ssl.root',
                ntos_name='tunnel-ssl-root',
                interface_type=InterfaceType.TUNNEL,
                is_virtual=True,
                zone_name='ssl-vpn-zone'
            ),
            'ssl_tunnel': InterfaceMapping(
                fortigate_name='ssl_tunnel',
                ntos_name='tunnel-ssl',
                interface_type=InterfaceType.TUNNEL,
                is_virtual=True,
                zone_name='ssl-vpn-zone'
            )
        })
        
        # IPSec隧道接口映射模式
        self.tunnel_patterns = {
            r'ipsec_\d+': 'tunnel-ipsec-{id}',
            r'vpn_\w+': 'tunnel-vpn-{name}',
            r'gre_\d+': 'tunnel-gre-{id}',
        }
        
        # 常见区域映射
        self.zone_mappings.update({
            'trust': 'internal-zone',
            'untrust': 'external-zone',
            'dmz': 'dmz-zone',
            'internal': 'internal-zone',
            'external': 'external-zone',
        })
    
    def load_interface_mappings(self, mapping_file: str) -> Dict[str, str]:
        """加载接口映射文件并增强"""
        import json
        
        with open(mapping_file, 'r', encoding='utf-8') as f:
            base_mappings = json.load(f)
        
        # 增强映射表
        enhanced_mappings = self._enhance_mappings(base_mappings)
        
        logging.info(f"加载接口映射: {len(base_mappings)} 个基础映射, {len(enhanced_mappings)} 个增强映射")
        
        return enhanced_mappings
    
    def _enhance_mappings(self, base_mappings: Dict[str, str]) -> Dict[str, str]:
        """增强接口映射表"""
        enhanced = base_mappings.copy()
        
        # 1. 添加隧道接口映射
        for tunnel_name, mapping in self.tunnel_mappings.items():
            if tunnel_name not in enhanced:
                enhanced[tunnel_name] = mapping.ntos_name
                logging.info(f"添加隧道接口映射: {tunnel_name} -> {mapping.ntos_name}")
        
        # 2. 自动生成VLAN子接口映射
        vlan_mappings = self._generate_vlan_mappings(base_mappings)
        enhanced.update(vlan_mappings)
        
        # 3. 添加区域映射（用于策略验证）
        for zone_name, ntos_zone in self.zone_mappings.items():
            zone_key = f"zone_{zone_name}"
            if zone_key not in enhanced:
                enhanced[zone_key] = ntos_zone
        
        # 4. 添加虚拟接口映射
        virtual_mappings = self._generate_virtual_mappings()
        enhanced.update(virtual_mappings)
        
        return enhanced
    
    def _generate_vlan_mappings(self, base_mappings: Dict[str, str]) -> Dict[str, str]:
        """自动生成VLAN子接口映射"""
        vlan_mappings = {}
        
        # 查找已有的VLAN接口模式
        for fg_interface, ntos_interface in base_mappings.items():
            if 'vlan' in fg_interface.lower() and '.' in ntos_interface:
                # 提取父接口和VLAN ID
                parent_interface = ntos_interface.split('.')[0]
                vlan_id = ntos_interface.split('.')[1]
                
                # 生成相关VLAN映射
                for i in range(1, 200):  # 常见VLAN范围
                    vlan_name = f"Vlan{i}"
                    if vlan_name not in base_mappings:
                        vlan_mappings[vlan_name] = f"{parent_interface}.{i}"
        
        logging.info(f"自动生成 {len(vlan_mappings)} 个VLAN接口映射")
        return vlan_mappings
    
    def _generate_virtual_mappings(self) -> Dict[str, str]:
        """生成虚拟接口映射"""
        virtual_mappings = {}
        
        # 常见虚拟接口
        virtual_interfaces = {
            'any': 'any-interface',
            'all': 'all-interfaces',
            'loopback': 'loopback0',
            'null': 'null-interface',
        }
        
        virtual_mappings.update(virtual_interfaces)
        
        return virtual_mappings
    
    def validate_interface_mapping(self, interface_name: str, mappings: Dict[str, str], 
                                 zones: List[str] = None) -> Optional[str]:
        """增强的接口映射验证"""
        zones = zones or []
        
        # 1. 直接映射查找
        if interface_name in mappings:
            return mappings[interface_name]
        
        # 2. 区域映射查找
        if interface_name in zones:
            zone_mapping = self.zone_mappings.get(interface_name)
            if zone_mapping:
                logging.info(f"接口 '{interface_name}' 识别为安全区域，映射到: {zone_mapping}")
                return zone_mapping
        
        # 3. 隧道接口模式匹配
        tunnel_mapping = self._match_tunnel_interface(interface_name)
        if tunnel_mapping:
            # 动态添加到映射表
            mappings[interface_name] = tunnel_mapping
            logging.info(f"动态生成隧道接口映射: {interface_name} -> {tunnel_mapping}")
            return tunnel_mapping
        
        # 4. VLAN接口模式匹配
        vlan_mapping = self._match_vlan_interface(interface_name, mappings)
        if vlan_mapping:
            mappings[interface_name] = vlan_mapping
            logging.info(f"动态生成VLAN接口映射: {interface_name} -> {vlan_mapping}")
            return vlan_mapping
        
        # 5. 虚拟接口处理
        if self._is_virtual_interface(interface_name):
            virtual_mapping = f"virtual-{interface_name.replace('.', '-')}"
            mappings[interface_name] = virtual_mapping
            logging.warning(f"虚拟接口映射: {interface_name} -> {virtual_mapping}")
            return virtual_mapping
        
        # 6. 最后尝试：生成默认映射
        default_mapping = self._generate_default_mapping(interface_name)
        if default_mapping:
            mappings[interface_name] = default_mapping
            logging.warning(f"生成默认接口映射: {interface_name} -> {default_mapping}")
            return default_mapping
        
        return None
    
    def _match_tunnel_interface(self, interface_name: str) -> Optional[str]:
        """匹配隧道接口模式"""
        import re
        
        for pattern, template in self.tunnel_patterns.items():
            match = re.match(pattern, interface_name)
            if match:
                # 提取参数并生成映射
                if '{id}' in template:
                    tunnel_id = re.search(r'\d+', interface_name)
                    if tunnel_id:
                        return template.format(id=tunnel_id.group())
                elif '{name}' in template:
                    name_part = interface_name.split('_', 1)[1] if '_' in interface_name else interface_name
                    return template.format(name=name_part)
        
        return None
    
    def _match_vlan_interface(self, interface_name: str, mappings: Dict[str, str]) -> Optional[str]:
        """匹配VLAN接口模式"""
        import re
        
        # 匹配VLAN接口模式
        vlan_patterns = [
            r'[Vv]lan(\d+)',
            r'vlan\.(\d+)',
            r'(\w+)\.(\d+)',  # 子接口模式
        ]
        
        for pattern in vlan_patterns:
            match = re.match(pattern, interface_name)
            if match:
                if len(match.groups()) == 1:
                    # 纯VLAN接口
                    vlan_id = match.group(1)
                    # 查找合适的父接口
                    parent_interface = self._find_suitable_parent_interface(mappings)
                    if parent_interface:
                        return f"{parent_interface}.{vlan_id}"
                elif len(match.groups()) == 2:
                    # 子接口模式
                    parent_name = match.group(1)
                    vlan_id = match.group(2)
                    # 查找父接口映射
                    parent_mapping = mappings.get(parent_name)
                    if parent_mapping:
                        return f"{parent_mapping}.{vlan_id}"
        
        return None
    
    def _find_suitable_parent_interface(self, mappings: Dict[str, str]) -> Optional[str]:
        """查找合适的父接口"""
        # 优先选择x2接口作为VLAN父接口（基于现有映射）
        for fg_name, ntos_name in mappings.items():
            if fg_name == 'x2' and '.' not in ntos_name:
                return ntos_name
        
        # 其次选择其他物理接口
        for fg_name, ntos_name in mappings.items():
            if fg_name.startswith('port') and '.' not in ntos_name:
                return ntos_name
        
        return None
    
    def _is_virtual_interface(self, interface_name: str) -> bool:
        """判断是否为虚拟接口"""
        virtual_keywords = ['ssl', 'vpn', 'tunnel', 'loopback', 'null', 'any', 'all']
        return any(keyword in interface_name.lower() for keyword in virtual_keywords)
    
    def _generate_default_mapping(self, interface_name: str) -> Optional[str]:
        """生成默认接口映射"""
        # 清理接口名称，生成NTOS兼容的名称
        clean_name = interface_name.replace('.', '-').replace('_', '-').lower()
        
        # 根据接口类型生成映射
        if 'ssl' in clean_name:
            return f"tunnel-ssl-{clean_name.replace('ssl', '').strip('-')}"
        elif 'vpn' in clean_name:
            return f"tunnel-vpn-{clean_name.replace('vpn', '').strip('-')}"
        elif 'ipsec' in clean_name:
            return f"tunnel-ipsec-{clean_name.replace('ipsec', '').strip('-')}"
        else:
            return f"virtual-{clean_name}"

class PolicyInterfaceValidator:
    """策略接口验证器"""
    
    def __init__(self, interface_mapper: EnhancedInterfaceMapper):
        self.interface_mapper = interface_mapper
        self.validation_stats = {
            'total_policies': 0,
            'successful_validations': 0,
            'failed_validations': 0,
            'auto_fixed_mappings': 0
        }
    
    def validate_policy_interfaces(self, policy: Dict, mappings: Dict[str, str], 
                                 zones: List[str] = None) -> bool:
        """验证策略的接口映射"""
        self.validation_stats['total_policies'] += 1
        
        # 获取源接口和目标接口
        src_interfaces = policy.get('srcintf', [])
        dst_interfaces = policy.get('dstintf', [])
        
        if isinstance(src_interfaces, str):
            src_interfaces = [src_interfaces]
        if isinstance(dst_interfaces, str):
            dst_interfaces = [dst_interfaces]
        
        # 验证所有接口
        all_interfaces = src_interfaces + dst_interfaces
        validation_success = True
        
        for interface in all_interfaces:
            mapped_interface = self.interface_mapper.validate_interface_mapping(
                interface, mappings, zones
            )
            
            if mapped_interface is None:
                logging.error(f"策略 {policy.get('policyid', 'unknown')}: 接口 '{interface}' 无法映射")
                validation_success = False
            else:
                if interface not in mappings:
                    # 记录自动修复的映射
                    self.validation_stats['auto_fixed_mappings'] += 1
        
        if validation_success:
            self.validation_stats['successful_validations'] += 1
        else:
            self.validation_stats['failed_validations'] += 1
        
        return validation_success
    
    def get_validation_report(self) -> Dict:
        """获取验证报告"""
        total = self.validation_stats['total_policies']
        success_rate = (self.validation_stats['successful_validations'] / total * 100) if total > 0 else 0
        
        return {
            'total_policies': total,
            'success_rate': f"{success_rate:.2f}%",
            'successful_validations': self.validation_stats['successful_validations'],
            'failed_validations': self.validation_stats['failed_validations'],
            'auto_fixed_mappings': self.validation_stats['auto_fixed_mappings']
        }

# 使用示例
def optimize_interface_mapping(mapping_file: str, policies: List[Dict], zones: List[str] = None) -> Dict:
    """接口映射优化主函数"""
    # 创建增强的接口映射器
    mapper = EnhancedInterfaceMapper()
    
    # 加载并增强接口映射
    enhanced_mappings = mapper.load_interface_mappings(mapping_file)
    
    # 创建策略验证器
    validator = PolicyInterfaceValidator(mapper)
    
    # 验证所有策略的接口映射
    for policy in policies:
        validator.validate_policy_interfaces(policy, enhanced_mappings, zones)
    
    # 生成报告
    validation_report = validator.get_validation_report()
    
    return {
        'enhanced_mappings': enhanced_mappings,
        'validation_report': validation_report,
        'total_mappings': len(enhanced_mappings)
    }
