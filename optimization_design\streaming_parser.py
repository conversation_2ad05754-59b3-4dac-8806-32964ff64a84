"""
FortiGate配置解析器 - 流式处理与内存优化
目标：处理大型配置文件时内存使用不超过200MB
"""

import os
import mmap
import logging
from typing import Iterator, Dict, List, Optional
from dataclasses import dataclass
import gc
from contextlib import contextmanager

@dataclass
class StreamChunk:
    """流式处理数据块"""
    chunk_id: int
    start_pos: int
    end_pos: int
    lines: List[str]
    section_type: str = "unknown"

class MemoryOptimizedParser:
    """内存优化的配置解析器"""
    
    def __init__(self, chunk_size: int = 10000, max_memory_mb: int = 150):
        self.chunk_size = chunk_size  # 每个数据块的行数
        self.max_memory_mb = max_memory_mb
        self.processed_chunks = 0
        self.total_chunks = 0
        
    @contextmanager
    def memory_monitor(self):
        """内存监控上下文管理器"""
        import psutil
        process = psutil.Process()
        
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        logging.info(f"初始内存使用: {initial_memory:.2f} MB")
        
        try:
            yield
        finally:
            current_memory = process.memory_info().rss / 1024 / 1024  # MB
            logging.info(f"当前内存使用: {current_memory:.2f} MB")
            
            if current_memory > self.max_memory_mb:
                logging.warning(f"内存使用超过限制 {self.max_memory_mb} MB，触发垃圾回收")
                gc.collect()
    
    def stream_parse_file(self, file_path: str) -> Iterator[Dict]:
        """流式解析配置文件"""
        file_size = os.path.getsize(file_path)
        logging.info(f"开始流式解析文件: {file_path} (大小: {file_size / 1024 / 1024:.2f} MB)")
        
        with self.memory_monitor():
            # 使用内存映射文件，减少内存占用
            with open(file_path, 'r', encoding='utf-8') as f:
                with mmap.mmap(f.fileno(), 0, access=mmap.ACCESS_READ) as mmapped_file:
                    yield from self._process_memory_mapped_file(mmapped_file)
    
    def _process_memory_mapped_file(self, mmapped_file) -> Iterator[Dict]:
        """处理内存映射文件"""
        chunk_buffer = []
        current_section = None
        section_depth = 0
        
        # 逐行读取，避免一次性加载全部内容
        for line_num, line in enumerate(self._read_lines_from_mmap(mmapped_file)):
            line_stripped = line.strip()
            
            # 检测配置段落边界
            if self._is_section_start(line_stripped):
                # 如果缓冲区有内容，先处理
                if chunk_buffer:
                    yield self._process_chunk(chunk_buffer, current_section)
                    chunk_buffer = []
                
                current_section = self._identify_section_type(line_stripped)
                section_depth = 1
            
            elif line_stripped == 'end':
                section_depth -= 1
                if section_depth == 0:
                    current_section = None
            
            elif line_stripped.startswith('edit ') or line_stripped.startswith('config '):
                section_depth += 1
            
            chunk_buffer.append(line)
            
            # 当缓冲区达到指定大小或段落结束时处理
            if (len(chunk_buffer) >= self.chunk_size or 
                (section_depth == 0 and current_section)):
                
                yield self._process_chunk(chunk_buffer, current_section)
                chunk_buffer = []
                
                # 定期触发垃圾回收
                if line_num % (self.chunk_size * 5) == 0:
                    gc.collect()
        
        # 处理最后的缓冲区内容
        if chunk_buffer:
            yield self._process_chunk(chunk_buffer, current_section)
    
    def _read_lines_from_mmap(self, mmapped_file) -> Iterator[str]:
        """从内存映射文件中逐行读取"""
        buffer = b''
        
        for chunk in iter(lambda: mmapped_file.read(8192), b''):
            buffer += chunk
            
            while b'\n' in buffer:
                line, buffer = buffer.split(b'\n', 1)
                try:
                    yield line.decode('utf-8')
                except UnicodeDecodeError:
                    # 处理编码错误
                    yield line.decode('utf-8', errors='ignore')
        
        # 处理最后一行（如果没有换行符）
        if buffer:
            try:
                yield buffer.decode('utf-8')
            except UnicodeDecodeError:
                yield buffer.decode('utf-8', errors='ignore')
    
    def _is_section_start(self, line: str) -> bool:
        """检测是否为配置段落开始"""
        section_keywords = [
            'config system interface',
            'config firewall policy',
            'config firewall address',
            'config firewall service',
            'config system zone',
            'config router static'
        ]
        
        return any(line.startswith(keyword) for keyword in section_keywords)
    
    def _identify_section_type(self, line: str) -> str:
        """识别配置段落类型"""
        if 'interface' in line:
            return 'interface'
        elif 'policy' in line:
            return 'policy'
        elif 'address' in line:
            return 'address'
        elif 'service' in line:
            return 'service'
        elif 'zone' in line:
            return 'zone'
        elif 'route' in line:
            return 'route'
        else:
            return 'unknown'
    
    def _process_chunk(self, lines: List[str], section_type: str) -> Dict:
        """处理数据块"""
        self.processed_chunks += 1
        
        # 根据段落类型选择处理策略
        if section_type == 'unknown':
            # 对于未知段落，仅统计，不详细处理
            return {
                'chunk_id': self.processed_chunks,
                'section_type': section_type,
                'line_count': len(lines),
                'processed': False,
                'summary': f"跳过未知配置段落 ({len(lines)} 行)"
            }
        
        # 处理已知段落类型
        result = {
            'chunk_id': self.processed_chunks,
            'section_type': section_type,
            'line_count': len(lines),
            'processed': True,
            'data': self._extract_config_data(lines, section_type)
        }
        
        # 清理临时数据，释放内存
        del lines
        
        return result
    
    def _extract_config_data(self, lines: List[str], section_type: str) -> Dict:
        """从配置行中提取数据"""
        # 根据段落类型实现具体的数据提取逻辑
        extractors = {
            'interface': self._extract_interface_data,
            'policy': self._extract_policy_data,
            'address': self._extract_address_data,
            'service': self._extract_service_data,
            'zone': self._extract_zone_data,
            'route': self._extract_route_data,
        }
        
        extractor = extractors.get(section_type, self._extract_generic_data)
        return extractor(lines)
    
    def _extract_interface_data(self, lines: List[str]) -> Dict:
        """提取接口配置数据"""
        interfaces = []
        current_interface = None
        
        for line in lines:
            line_stripped = line.strip()
            
            if line_stripped.startswith('edit '):
                if current_interface:
                    interfaces.append(current_interface)
                
                interface_name = line_stripped.split('"')[1] if '"' in line_stripped else line_stripped.split()[1]
                current_interface = {'name': interface_name, 'config': {}}
            
            elif current_interface and line_stripped.startswith('set '):
                parts = line_stripped.split(' ', 2)
                if len(parts) >= 3:
                    key = parts[1]
                    value = parts[2].strip('"')
                    current_interface['config'][key] = value
        
        if current_interface:
            interfaces.append(current_interface)
        
        return {'interfaces': interfaces}
    
    def _extract_policy_data(self, lines: List[str]) -> Dict:
        """提取策略配置数据"""
        policies = []
        current_policy = None
        
        for line in lines:
            line_stripped = line.strip()
            
            if line_stripped.startswith('edit '):
                if current_policy:
                    policies.append(current_policy)
                
                policy_id = line_stripped.split()[1]
                current_policy = {'id': policy_id, 'config': {}}
            
            elif current_policy and line_stripped.startswith('set '):
                parts = line_stripped.split(' ', 2)
                if len(parts) >= 3:
                    key = parts[1]
                    value = parts[2].strip('"')
                    current_policy['config'][key] = value
        
        if current_policy:
            policies.append(current_policy)
        
        return {'policies': policies}
    
    def _extract_address_data(self, lines: List[str]) -> Dict:
        """提取地址配置数据"""
        addresses = []
        current_address = None
        
        for line in lines:
            line_stripped = line.strip()
            
            if line_stripped.startswith('edit '):
                if current_address:
                    addresses.append(current_address)
                
                address_name = line_stripped.split('"')[1] if '"' in line_stripped else line_stripped.split()[1]
                current_address = {'name': address_name, 'config': {}}
            
            elif current_address and line_stripped.startswith('set '):
                parts = line_stripped.split(' ', 2)
                if len(parts) >= 3:
                    key = parts[1]
                    value = parts[2].strip('"')
                    current_address['config'][key] = value
        
        if current_address:
            addresses.append(current_address)
        
        return {'addresses': addresses}
    
    def _extract_service_data(self, lines: List[str]) -> Dict:
        """提取服务配置数据"""
        return {'services': []}  # 简化实现
    
    def _extract_zone_data(self, lines: List[str]) -> Dict:
        """提取区域配置数据"""
        return {'zones': []}  # 简化实现
    
    def _extract_route_data(self, lines: List[str]) -> Dict:
        """提取路由配置数据"""
        return {'routes': []}  # 简化实现
    
    def _extract_generic_data(self, lines: List[str]) -> Dict:
        """提取通用配置数据"""
        return {'raw_lines': len(lines)}

class StreamingFortigateParser:
    """流式FortiGate解析器主类"""
    
    def __init__(self, chunk_size: int = 10000):
        self.memory_parser = MemoryOptimizedParser(chunk_size)
        self.results_aggregator = ResultsAggregator()
    
    def parse_config_file(self, file_path: str) -> Dict:
        """解析配置文件的主入口"""
        logging.info("开始流式解析配置文件")
        
        # 流式处理文件
        for chunk_result in self.memory_parser.stream_parse_file(file_path):
            self.results_aggregator.add_chunk_result(chunk_result)
        
        # 聚合最终结果
        final_results = self.results_aggregator.get_final_results()
        
        logging.info(f"流式解析完成，处理了 {self.memory_parser.processed_chunks} 个数据块")
        return final_results

class ResultsAggregator:
    """结果聚合器"""
    
    def __init__(self):
        self.aggregated_results = {
            'interfaces': [],
            'policies': [],
            'addresses': [],
            'services': [],
            'zones': [],
            'routes': [],
            'statistics': {
                'total_chunks': 0,
                'processed_chunks': 0,
                'skipped_chunks': 0,
                'total_lines': 0
            }
        }
    
    def add_chunk_result(self, chunk_result: Dict):
        """添加数据块处理结果"""
        self.aggregated_results['statistics']['total_chunks'] += 1
        self.aggregated_results['statistics']['total_lines'] += chunk_result.get('line_count', 0)
        
        if chunk_result.get('processed', False):
            self.aggregated_results['statistics']['processed_chunks'] += 1
            
            # 聚合数据
            data = chunk_result.get('data', {})
            for key in ['interfaces', 'policies', 'addresses', 'services', 'zones', 'routes']:
                if key in data:
                    self.aggregated_results[key].extend(data[key])
        else:
            self.aggregated_results['statistics']['skipped_chunks'] += 1
    
    def get_final_results(self) -> Dict:
        """获取最终聚合结果"""
        return self.aggregated_results
