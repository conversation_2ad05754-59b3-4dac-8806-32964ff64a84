<config xmlns="urn:ruijie:ntos">
  <vrf>
    <name>main</name>
    <aaa xmlns="urn:ruijie:ntos:params:xml:ns:yang:aaad">
      <enabled>true</enabled>
      <domain-enabled>true</domain-enabled>
      <domain>
        <name>default</name>
        <authentication>
          <sslvpn>
            <method>default</method>
            <enabled>true</enabled>
          </sslvpn>
          <webauth>
            <method>default</method>
            <enabled>true</enabled>
          </webauth>
        </authentication>
        <enabled>true</enabled>
        <username-format>
          <without-domain/>
        </username-format>
        <auto-create-group>true</auto-create-group>
      </domain>
      <domain>
        <name>vpn</name>
        <authentication>
          <sslvpn>
            <method>vpn</method>
            <enabled>true</enabled>
          </sslvpn>
          <vpn>
            <method>vpn</method>
            <enabled>true</enabled>
          </vpn>
        </authentication>
        <enabled>true</enabled>
        <username-format>
          <without-domain/>
        </username-format>
        <auto-create-group>true</auto-create-group>
      </domain>
      <domain>
        <name>pppoe</name>
        <authentication>
          <pppoe>
            <method>pppoe</method>
            <enabled>true</enabled>
          </pppoe>
        </authentication>
        <enabled>true</enabled>
        <username-format>
          <without-domain/>
        </username-format>
        <auto-create-group>true</auto-create-group>
      </domain>
      <authentication>
        <sslvpn>
          <name>default</name>
          <subs/>
          <auth-order>only-subs</auth-order>
        </sslvpn>
        <sslvpn>
          <name>vpn</name>
          <subs/>
          <auth-order>only-subs</auth-order>
        </sslvpn>
        <webauth>
          <name>default</name>
          <subs/>
          <auth-order>only-subs</auth-order>
        </webauth>
        <vpn>
          <name>vpn</name>
          <subs/>
          <auth-order>only-subs</auth-order>
        </vpn>
        <pppoe>
          <name>pppoe</name>
          <subs/>
          <auth-order>only-subs</auth-order>
        </pppoe>
      </authentication>
      <accounting>
        <update>
          <periodic>5</periodic>
          <enabled>false</enabled>
        </update>
      </accounting>
    </aaa>
    <app-behavior-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:app-behavior-control">
      <qq-control>
        <control-mode>blacklist</control-mode>
      </qq-control>
    </app-behavior-control>
    <app-parse-mgmt xmlns="urn:ruijie:ntos:params:xml:ns:yang:app-parse-mgmt">
      <overload-protection>
        <enabled>false</enabled>
        <action>bypass</action>
      </overload-protection>
      <http>
        <decompress-length>2048</decompress-length>
      </http>
    </app-parse-mgmt>
    <appid xmlns="urn:ruijie:ntos:params:xml:ns:yang:appid">
      <mode>dynamic-identify</mode>
    </appid>
    <arp xmlns="urn:ruijie:ntos:params:xml:ns:yang:arp">
      <proxy-enabled>false</proxy-enabled>
      <trusted>
        <nud-probe>
          <enabled>false</enabled>
        </nud-probe>
      </trusted>
      <gratuitous-send>
        <enabled>false</enabled>
        <interval>30</interval>
      </gratuitous-send>
    </arp>
    <bridge xmlns="urn:ruijie:ntos:params:xml:ns:yang:bridge">
      <fdb>
        <aging>300</aging>
      </fdb>
    </bridge>
    <collab-disposal xmlns="urn:ruijie:ntos:params:xml:ns:yang:collab-disposal">
      <enabled>false</enabled>
      <identity-system>none</identity-system>
    </collab-disposal>
    <dhcp xmlns="urn:ruijie:ntos:params:xml:ns:yang:dhcp">
      <server>
        <enabled>true</enabled>
        <default-lease-time>43200</default-lease-time>
        <max-lease-time>86400</max-lease-time>
        <subnet>
          <prefix>***********/24</prefix>
          <interface>Ge0/0</interface>
          <default-gateway>*************</default-gateway>
          <range>
            <start-ip>***********</start-ip>
            <end-ip>*************</end-ip>
          </range>
          <ping-check>true</ping-check>
          <default-lease-time>20</default-lease-time>
          <max-lease-time>20</max-lease-time>
          <lease-id-format>hex</lease-id-format>
          <warning-high-threshold>90</warning-high-threshold>
          <warning-low-threshold>80</warning-low-threshold>
        </subnet>
      </server>
    </dhcp>
    <dns-client xmlns="urn:ruijie:ntos:params:xml:ns:yang:dns-client">
      <ip-domain-lookup>
        <enabled>true</enabled>
      </ip-domain-lookup>
    </dns-client>
    <dns-security xmlns="urn:ruijie:ntos:params:xml:ns:yang:dns-security">
      <dns-filter>
        <dns-status>false</dns-status>
      </dns-filter>
      <dns-attack-defense>
        <attack-defense>
          <protocol>true</protocol>
          <security-vul>true</security-vul>
        </attack-defense>
        <flood-defense>
          <enable>true</enable>
        </flood-defense>
      </dns-attack-defense>
      <dns-security-cloud>
        <pdns>true</pdns>
        <online-protect>true</online-protect>
      </dns-security-cloud>
    </dns-security>
    <dns-transparent-proxy xmlns="urn:ruijie:ntos:params:xml:ns:yang:dns-transparent-proxy">
      <enabled>false</enabled>
      <mode>mllb</mode>
    </dns-transparent-proxy>
    <file-filter xmlns="urn:ruijie:ntos:params:xml:ns:yang:file-filter">
      <global-config>
        <feature-identify-enabled>false</feature-identify-enabled>
      </global-config>
    </file-filter>
    <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
      <enabled>false</enabled>
    </flow-control>
    <ha xmlns="urn:ruijie:ntos:params:xml:ns:yang:ha">
      <mode>A-P</mode>
      <heart-interval>1000</heart-interval>
      <heart-alert-count>3</heart-alert-count>
      <gra-arp-count>5</gra-arp-count>
      <vmac-prefix>00:00:5e</vmac-prefix>
      <preempt>false</preempt>
      <preempt-delay>60</preempt-delay>
      <session-sync>true</session-sync>
      <neigh-sync>true</neigh-sync>
      <switch-link-time>1</switch-link-time>
      <auth-type>none</auth-type>
      <enabled>false</enabled>
      <log-level>
        <group>on</group>
        <adv>off</adv>
        <dbus>on</dbus>
        <monitor>off</monitor>
      </log-level>
    </ha>
    <ike xmlns="urn:ruijie:ntos:params:xml:ns:yang:ike">
      <proposal>
        <name>default</name>
        <prf>sha-256</prf>
        <life-seconds>86400</life-seconds>
        <encrypt-alg>des des3 aes-128 aes-192 aes-256</encrypt-alg>
        <hash-alg>md5 sha</hash-alg>
        <dh-group>group1 group2 group5</dh-group>
        <auth-mode>preshared-key</auth-mode>
      </proposal>
      <profile>
        <name>default</name>
      </profile>
      <profile>
        <name>temporary</name>
      </profile>
      <dpd>
        <interval>30</interval>
        <retry-interval>5</retry-interval>
      </dpd>
      <nat-traversal>
        <enabled>true</enabled>
      </nat-traversal>
      <nat>
        <keepalive>20</keepalive>
      </nat>
    </ike>
    <interface xmlns="urn:ruijie:ntos:params:xml:ns:yang:interface">
      <snmp>
        <if-usage-compute-interval>30</if-usage-compute-interval>
        <if-global-notify-enable>true</if-global-notify-enable>
      </snmp>
      <physical>
        <name>Ge0/0</name>
        <ipv4>
          <address>
            <ip>*************/24</ip>
          </address>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>true</https>
          <ping>true</ping>
          <ssh>true</ssh>
        </access-control>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <wanlan>lan</wanlan>
        <description/>
      </physical>
      <physical>
        <name>Ge0/1</name>
        <ipv4>
          <enabled>true</enabled>
          <address>
            <ip>**********/23</ip>
          </address>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>true</https>
          <ping>true</ping>
          <ssh>true</ssh>
        </access-control>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <wanlan>lan</wanlan>
        <description/>
      </physical>
      <physical>
        <name>Ge0/2</name>
        <ipv4>
          <enabled>true</enabled>
          <address>
            <ip>**********/20</ip>
          </address>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>true</ping>
          <ssh>false</ssh>
        </access-control>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <wanlan>lan</wanlan>
        <description/>
      </physical>
      <physical>
        <name>Ge0/3</name>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <wanlan>lan</wanlan>
        <description/>
      </physical>
      <physical>
        <name>Ge0/4</name>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <wanlan>lan</wanlan>
        <description/>
      </physical>
      <physical>
        <name>Ge0/5</name>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <wanlan>lan</wanlan>
        <description/>
      </physical>
      <physical>
        <name>Ge0/6</name>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <wanlan>lan</wanlan>
        <description/>
      </physical>
      <physical>
        <name>Ge0/7</name>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <wanlan>lan</wanlan>
        <description/>
      </physical>
      <physical>
        <name>Ge0/8</name>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <wanlan>lan</wanlan>
        <description/>
      </physical>
      <physical>
        <name>Ge0/9</name>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </physical>
      <physical>
        <name>TenGe0/0</name>
        <ipv4>
          <enabled>true</enabled>
          <address>
            <ip>************/30</ip>
          </address>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>true</ping>
          <ssh>false</ssh>
        </access-control>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <wanlan>wan</wanlan>
        <description/>
      </physical>
      <physical>
        <name>TenGe0/1</name>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </physical>
      <physical>
        <name>TenGe0/2</name>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </physical>
      <physical>
        <name>TenGe0/3</name>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </physical>
      <bridge xmlns="urn:ruijie:ntos:params:xml:ns:yang:bridge">
        <name>br0</name>
        <enabled>true</enabled>
        <ipv4>
          <enabled>true</enabled>
          <dhcp>
            <enabled>true</enabled>
            <timeout>60</timeout>
            <retry>30</retry>
            <select-timeout>0</select-timeout>
            <reboot>10</reboot>
            <initial-interval>10</initial-interval>
            <dhcp-lease-time>7200</dhcp-lease-time>
            <request>subnet-mask</request>
            <request>broadcast-address</request>
            <request>time-offset</request>
            <request>routers</request>
            <request>domain-name</request>
            <request>domain-search</request>
            <request>domain-name-servers</request>
            <request>host-name</request>
            <request>nis-domain</request>
          </dhcp>
        </ipv4>
        <ipv6>
          <enabled>true</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <session-source-check>dont-check</session-source-check>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>true</https>
          <ping>true</ping>
          <ssh>false</ssh>
        </access-control>
      </bridge>
      <vswitch xmlns="urn:ruijie:ntos:params:xml:ns:yang:vswitch">
        <name>vswitch0</name>
        <enabled>true</enabled>
        <wanlan>lan</wanlan>
        <ipv4>
          <enabled>true</enabled>
          <dhcp>
            <enabled>false</enabled>
            <timeout>60</timeout>
            <retry>30</retry>
            <select-timeout>0</select-timeout>
            <reboot>10</reboot>
            <initial-interval>10</initial-interval>
            <dhcp-lease-time>7200</dhcp-lease-time>
            <request>subnet-mask</request>
            <request>broadcast-address</request>
            <request>time-offset</request>
            <request>routers</request>
            <request>domain-name</request>
            <request>domain-search</request>
            <request>domain-name-servers</request>
            <request>host-name</request>
            <request>nis-domain</request>
          </dhcp>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <session-source-check>transparent-forward</session-source-check>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>true</https>
          <ping>true</ping>
          <ssh>true</ssh>
        </access-control>
      </vswitch>
      <vlan xmlns="urn:ruijie:ntos:params:xml:ns:yang:vlan">
        <name>Ge0/2.2200</name>
        <description/>
        <enabled>true</enabled>
        <ipv4>
          <enabled>true</enabled>
          <address>
            <ip>*********/24</ip>
          </address>
          <dhcp>
            <enabled>false</enabled>
          </dhcp>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <reverse-path>true</reverse-path>
        <vlan-id>2200</vlan-id>
        <link-interface>Ge0/2</link-interface>
        <protocol>802.1q</protocol>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>true</ping>
          <ssh>false</ssh>
        </access-control>
      </vlan>
      <vlan xmlns="urn:ruijie:ntos:params:xml:ns:yang:vlan">
        <name>Ge0/2.2500</name>
        <description/>
        <enabled>true</enabled>
        <ipv4>
          <enabled>true</enabled>
          <address>
            <ip>*********/24</ip>
          </address>
          <dhcp>
            <enabled>false</enabled>
          </dhcp>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <reverse-path>true</reverse-path>
        <vlan-id>2500</vlan-id>
        <link-interface>Ge0/2</link-interface>
        <protocol>802.1q</protocol>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>true</ping>
          <ssh>false</ssh>
        </access-control>
      </vlan>
      <vlan xmlns="urn:ruijie:ntos:params:xml:ns:yang:vlan">
        <name>Ge0/2.2100</name>
        <description/>
        <enabled>true</enabled>
        <ipv4>
          <enabled>true</enabled>
          <address>
            <ip>*********/20</ip>
          </address>
          <dhcp>
            <enabled>false</enabled>
          </dhcp>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <reverse-path>true</reverse-path>
        <vlan-id>2100</vlan-id>
        <link-interface>Ge0/2</link-interface>
        <protocol>802.1q</protocol>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>true</ping>
          <ssh>false</ssh>
        </access-control>
      </vlan>
      <vlan xmlns="urn:ruijie:ntos:params:xml:ns:yang:vlan">
        <name>Ge0/2.3100</name>
        <description/>
        <enabled>true</enabled>
        <ipv4>
          <enabled>true</enabled>
          <address>
            <ip>*********/20</ip>
          </address>
          <dhcp>
            <enabled>false</enabled>
          </dhcp>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <reverse-path>true</reverse-path>
        <vlan-id>3100</vlan-id>
        <link-interface>Ge0/2</link-interface>
        <protocol>802.1q</protocol>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>true</ping>
          <ssh>false</ssh>
        </access-control>
      </vlan>
      <vlan xmlns="urn:ruijie:ntos:params:xml:ns:yang:vlan">
        <name>Ge0/2.2000</name>
        <description/>
        <enabled>true</enabled>
        <ipv4>
          <enabled>true</enabled>
          <address>
            <ip>*********/22</ip>
          </address>
          <dhcp>
            <enabled>false</enabled>
          </dhcp>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <reverse-path>true</reverse-path>
        <vlan-id>2000</vlan-id>
        <link-interface>Ge0/2</link-interface>
        <protocol>802.1q</protocol>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>true</ping>
          <ssh>false</ssh>
        </access-control>
      </vlan>
      <vlan xmlns="urn:ruijie:ntos:params:xml:ns:yang:vlan">
        <name>Ge0/2.2600</name>
        <description/>
        <enabled>true</enabled>
        <ipv4>
          <enabled>true</enabled>
          <address>
            <ip>*********/24</ip>
          </address>
          <dhcp>
            <enabled>false</enabled>
          </dhcp>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <reverse-path>true</reverse-path>
        <vlan-id>2600</vlan-id>
        <link-interface>Ge0/2</link-interface>
        <protocol>802.1q</protocol>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>true</ping>
          <ssh>false</ssh>
        </access-control>
      </vlan>
      <vlan xmlns="urn:ruijie:ntos:params:xml:ns:yang:vlan">
        <name>Ge0/2.3000</name>
        <description/>
        <enabled>true</enabled>
        <ipv4>
          <enabled>true</enabled>
          <address>
            <ip>*********/20</ip>
          </address>
          <dhcp>
            <enabled>false</enabled>
          </dhcp>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <reverse-path>true</reverse-path>
        <vlan-id>3000</vlan-id>
        <link-interface>Ge0/2</link-interface>
        <protocol>802.1q</protocol>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>true</ping>
          <ssh>false</ssh>
        </access-control>
      </vlan>
      <vlan xmlns="urn:ruijie:ntos:params:xml:ns:yang:vlan">
        <name>Ge0/2.2400</name>
        <description/>
        <enabled>true</enabled>
        <ipv4>
          <enabled>true</enabled>
          <address>
            <ip>*********/24</ip>
          </address>
          <dhcp>
            <enabled>false</enabled>
          </dhcp>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <reverse-path>true</reverse-path>
        <vlan-id>2400</vlan-id>
        <link-interface>Ge0/2</link-interface>
        <protocol>802.1q</protocol>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>true</ping>
          <ssh>false</ssh>
        </access-control>
      </vlan>
      <vlan xmlns="urn:ruijie:ntos:params:xml:ns:yang:vlan">
        <name>Ge0/2.2700</name>
        <description/>
        <enabled>true</enabled>
        <ipv4>
          <enabled>true</enabled>
          <address>
            <ip>*********/24</ip>
          </address>
          <dhcp>
            <enabled>false</enabled>
          </dhcp>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <reverse-path>true</reverse-path>
        <vlan-id>2700</vlan-id>
        <link-interface>Ge0/2</link-interface>
        <protocol>802.1q</protocol>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>true</ping>
          <ssh>false</ssh>
        </access-control>
      </vlan>
      <vlan xmlns="urn:ruijie:ntos:params:xml:ns:yang:vlan">
        <name>Ge0/2.1010</name>
        <description/>
        <enabled>true</enabled>
        <ipv4>
          <enabled>true</enabled>
          <address>
            <ip>*********/24</ip>
          </address>
          <dhcp>
            <enabled>false</enabled>
          </dhcp>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <reverse-path>true</reverse-path>
        <vlan-id>1010</vlan-id>
        <link-interface>Ge0/2</link-interface>
        <protocol>802.1q</protocol>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>true</ping>
          <ssh>false</ssh>
        </access-control>
      </vlan>
      <vlan xmlns="urn:ruijie:ntos:params:xml:ns:yang:vlan">
        <name>Ge0/2.3200</name>
        <description/>
        <enabled>true</enabled>
        <ipv4>
          <enabled>true</enabled>
          <address>
            <ip>*********/20</ip>
          </address>
          <dhcp>
            <enabled>false</enabled>
          </dhcp>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <reverse-path>true</reverse-path>
        <vlan-id>3200</vlan-id>
        <link-interface>Ge0/2</link-interface>
        <protocol>802.1q</protocol>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>true</ping>
          <ssh>false</ssh>
        </access-control>
      </vlan>
      <vlan xmlns="urn:ruijie:ntos:params:xml:ns:yang:vlan">
        <name>Ge0/1.135</name>
        <description/>
        <enabled>true</enabled>
        <ipv4>
          <enabled>true</enabled>
          <address>
            <ip>************/24</ip>
          </address>
          <dhcp>
            <enabled>false</enabled>
          </dhcp>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <reverse-path>true</reverse-path>
        <vlan-id>135</vlan-id>
        <link-interface>Ge0/1</link-interface>
        <protocol>802.1q</protocol>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>true</ping>
          <ssh>false</ssh>
        </access-control>
      </vlan>
      <vlan xmlns="urn:ruijie:ntos:params:xml:ns:yang:vlan">
        <name>Ge0/1.1001</name>
        <description/>
        <enabled>true</enabled>
        <ipv4>
          <enabled>true</enabled>
          <address>
            <ip>***********/24</ip>
          </address>
          <dhcp>
            <enabled>false</enabled>
          </dhcp>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <reverse-path>true</reverse-path>
        <vlan-id>1001</vlan-id>
        <link-interface>Ge0/1</link-interface>
        <protocol>802.1q</protocol>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>true</ping>
          <ssh>false</ssh>
        </access-control>
      </vlan>
      <vlan xmlns="urn:ruijie:ntos:params:xml:ns:yang:vlan">
        <name>Ge0/1.33</name>
        <description/>
        <enabled>true</enabled>
        <ipv4>
          <enabled>true</enabled>
          <address>
            <ip>***********/24</ip>
          </address>
          <dhcp>
            <enabled>false</enabled>
          </dhcp>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <reverse-path>true</reverse-path>
        <vlan-id>33</vlan-id>
        <link-interface>Ge0/1</link-interface>
        <protocol>802.1q</protocol>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>true</https>
          <ping>true</ping>
          <ssh>false</ssh>
        </access-control>
      </vlan>
      <vlan xmlns="urn:ruijie:ntos:params:xml:ns:yang:vlan">
        <name>Ge0/2.2</name>
        <description/>
        <enabled>true</enabled>
        <ipv4>
          <enabled>true</enabled>
          <address>
            <ip>***********/24</ip>
          </address>
          <dhcp>
            <enabled>false</enabled>
          </dhcp>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <reverse-path>true</reverse-path>
        <vlan-id>2</vlan-id>
        <link-interface>Ge0/2</link-interface>
        <protocol>802.1q</protocol>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </vlan>
    </interface>
    <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
      <ipv4>
        <enabled>false</enabled>
        <no-match-action-drop>false</no-match-action-drop>
      </ipv4>
      <ipv6>
        <enabled>false</enabled>
        <no-match-action-drop>false</no-match-action-drop>
      </ipv6>
    </ip-mac-bind>
    <track xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-track">
      <enabled>false</enabled>
    </track>
    <ipsec xmlns="urn:ruijie:ntos:params:xml:ns:yang:ipsec">
      <anti-replay>
        <check>true</check>
        <window-size>64</window-size>
      </anti-replay>
      <df-bit>clear</df-bit>
      <prefrag>true</prefrag>
      <inbound-sp>
        <check>true</check>
      </inbound-sp>
      <spd-hash-bits>
        <src-bits>16</src-bits>
        <dst-bits>16</dst-bits>
      </spd-hash-bits>
      <hardware-crypto-offload>true</hardware-crypto-offload>
    </ipsec>
    <isp xmlns="urn:ruijie:ntos:params:xml:ns:yang:isp">
      <distance>10</distance>
    </isp>
    <lldp xmlns="urn:ruijie:ntos:params:xml:ns:yang:lldp">
      <enabled>true</enabled>
      <hello-timer>30</hello-timer>
      <tx-hold>4</tx-hold>
      <system-name>Z5100-S</system-name>
      <interface>
        <name>Ge0/0</name>
        <enabled>true</enabled>
      </interface>
      <interface>
        <name>Ge0/1</name>
        <enabled>true</enabled>
      </interface>
      <interface>
        <name>Ge0/2</name>
        <enabled>true</enabled>
      </interface>
      <interface>
        <name>Ge0/3</name>
        <enabled>true</enabled>
      </interface>
      <interface>
        <name>Ge0/4</name>
        <enabled>true</enabled>
      </interface>
      <interface>
        <name>Ge0/5</name>
        <enabled>true</enabled>
      </interface>
      <interface>
        <name>Ge0/6</name>
        <enabled>true</enabled>
      </interface>
      <interface>
        <name>Ge0/7</name>
        <enabled>true</enabled>
      </interface>
      <interface>
        <name>Ge0/8</name>
        <enabled>true</enabled>
      </interface>
      <interface>
        <name>Ge0/9</name>
        <enabled>true</enabled>
      </interface>
      <interface>
        <name>TenGe0/0</name>
        <enabled>true</enabled>
      </interface>
      <interface>
        <name>TenGe0/1</name>
        <enabled>true</enabled>
      </interface>
      <interface>
        <name>TenGe0/2</name>
        <enabled>true</enabled>
      </interface>
      <interface>
        <name>TenGe0/3</name>
        <enabled>true</enabled>
      </interface>
    </lldp>
    <local-defend xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
      <policy>
        <name>deny_all</name>
        <enabled>true</enabled>
        <action>deny</action>
        <limit>false</limit>
        <pps>600</pps>
        <description/>
      </policy>
      <policy>
        <name>limit_local</name>
        <enabled>true</enabled>
        <action>permit</action>
        <limit>true</limit>
        <pps>1500</pps>
        <description/>
      </policy>
    </local-defend>
    <misn xmlns="urn:ruijie:ntos:params:xml:ns:yang:misn">
      <enabled>true</enabled>
    </misn>
    <mllb xmlns="urn:ruijie:ntos:params:xml:ns:yang:mllb">
      <advanced-options>
        <refresh-session>false</refresh-session>
        <cache-timeout>300</cache-timeout>
        <cache-once>256</cache-once>
        <cache-disable>false</cache-disable>
        <alarm-threshold>90</alarm-threshold>
      </advanced-options>
      <all-if-switch>false</all-if-switch>
    </mllb>
    <n2n-config xmlns="urn:ruijie:ntos:params:xml:ns:yang:n2n">
      <crypto>CC20</crypto>
      <vpn-port-v4>0</vpn-port-v4>
      <vpn-port-v6>0</vpn-port-v6>
      <hang-side>false</hang-side>
      <flow-control>
        <enabled>true</enabled>
        <rate>100</rate>
        <rate-unit>kbps</rate-unit>
      </flow-control>
      <detect-policy>
        <enabled>true</enabled>
        <interval>5000</interval>
        <timeout>3000</timeout>
      </detect-policy>
    </n2n-config>
    <port-mapping xmlns="urn:ruijie:ntos:params:xml:ns:yang:nat">
      <enabled>true</enabled>
    </port-mapping>
    <nat xmlns="urn:ruijie:ntos:params:xml:ns:yang:nat">
      <enabled>true</enabled>
      <alg>ftp sip-tcp sip-udp tftp dns-udp</alg>
      <sip-port-check>
        <enabled>true</enabled>
      </sip-port-check>
      <pool>
        <name>*************</name>
        <address>
          <value>*************-*************</value>
        </address>
      </pool>
      <pool>
        <name>*************</name>
        <address>
          <value>*************-*************</value>
        </address>
      </pool>
      <pool>
        <name>*************</name>
        <address>
          <value>*************-*************</value>
        </address>
      </pool>
      <pool>
        <name>************10</name>
        <address>
          <value>************10-************10</value>
        </address>
      </pool>
      <pool>
        <name>************0</name>
        <address>
          <value>************0-************0</value>
        </address>
      </pool>
      <pool>
        <name>*************</name>
        <address>
          <value>*************-*************</value>
        </address>
      </pool>
      <pool>
        <name>YESILYURT_RKT_NAT_128</name>
        <address>
          <value>************28-************28</value>
        </address>
      </pool>
      <pool>
        <name>************1</name>
        <address>
          <value>************1-************1</value>
        </address>
      </pool>
      <pool>
        <name>************</name>
        <address>
          <value>************-************</value>
        </address>
      </pool>
      <pool>
        <name>*************0</name>
        <address>
          <value>*************0-*************0</value>
        </address>
      </pool>
      <pool>
        <name>*************</name>
        <address>
          <value>*************-*************</value>
        </address>
      </pool>
      <pool>
        <name>**************</name>
        <address>
          <value>**************-**************</value>
        </address>
      </pool>
      <pool>
        <name>**************</name>
        <address>
          <value>**************-**************</value>
        </address>
      </pool>
      <pool>
        <name>**************</name>
        <address>
          <value>**************-**************</value>
        </address>
      </pool>
      <pool>
        <name>**************</name>
        <address>
          <value>**************-**************</value>
        </address>
      </pool>
      <pool>
        <name>**************</name>
        <address>
          <value>**************-**************</value>
        </address>
      </pool>
      <pool>
        <name>**************</name>
        <address>
          <value>**************-**************</value>
        </address>
      </pool>
      <rule>
        <name>Proxy_Sunucu_to_WAN</name>
        <rule_en>true</rule_en>
        <static-snat44>
          <match>
            <source-network>
              <name>Proxy_Sunucusu</name>
            </source-network>
            <service>
              <name>any</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <output-address/>
            <no-pat>false</no-pat>
            <try-no-pat>true</try-no-pat>
          </translate-to>
        </static-snat44>
      </rule>
      <rule>
        <name>WEB_TO_Turnike_Gecis_Sistemeleri_Turnike_Gecis_Sistem</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>Turnike_Gecis_Sistemleri</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>UNIKYS_LDAP_Active_Directory</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <source-network>
              <name>OMÜ_Erişim</name>
            </source-network>
            <dest-network>
              <name>Active_Directory</name>
            </dest-network>
            <service>
              <name>LDAP</name>
            </service>
            <service>
              <name>LDAP_UDP</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>**********76</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>wantoproxywan_Proxy_Sunucu_8080</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>Proxy_Sunucu_8080</name>
            </dest-network>
            <service>
              <name>TCP_8080</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>**********16</ipv4-address>
            <port>8080</port>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>LANtoDMZ_TurnikeSistemleri_Turnike_Gecis_Sistemleri</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>Turnike_Gecis_Sistemleri</name>
            </dest-network>
            <service>
              <name>any</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>Etik_EtikKurulu_VIP</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>EtikKurulu_VIP</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>TCP_8081</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>MTUKYS_UNIKYS_Kalite_Yazılımı</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>UNIKYS_Kalite_Yazılımı</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>Mezunum_LAN_to_DMZ_Mezun_Portal</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>Mezun_Portal</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <service>
              <name>SSH</name>
            </service>
            <service>
              <name>MEZUN_PORTLAR</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>LANtoDMZ_AD_Active_Directory</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <source-network>
              <name>LOCAL_NET_IP_SCOPE</name>
            </source-network>
            <dest-network>
              <name>Active_Directory</name>
            </dest-network>
            <service>
              <name>AD_ALL</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>**********76</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>LANtoDMZ_AD_Active_Directory_Backup</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <source-network>
              <name>LOCAL_NET_IP_SCOPE</name>
            </source-network>
            <dest-network>
              <name>Active_Directory_Backup</name>
            </dest-network>
            <service>
              <name>AD_ALL</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>**********77</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>KAtalog_Katalog</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>Katalog</name>
            </dest-network>
            <service>
              <name>any</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>vrpaCLS</name>
        <rule_en>true</rule_en>
        <dynamic-snat44>
          <match>
            <source-network>
              <name>************</name>
            </source-network>
            <service>
              <name>any</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <pool-name>**************</pool-name>
          </translate-to>
        </dynamic-snat44>
      </rule>
      <rule>
        <name>vrpa1</name>
        <rule_en>true</rule_en>
        <dynamic-snat44>
          <match>
            <source-network>
              <name>************</name>
            </source-network>
            <service>
              <name>any</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <pool-name>**************</pool-name>
          </translate-to>
        </dynamic-snat44>
      </rule>
      <rule>
        <name>vrpa2</name>
        <rule_en>true</rule_en>
        <dynamic-snat44>
          <match>
            <source-network>
              <name>************</name>
            </source-network>
            <service>
              <name>any</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <pool-name>**************</pool-name>
          </translate-to>
        </dynamic-snat44>
      </rule>
      <rule>
        <name>vrpa3</name>
        <rule_en>true</rule_en>
        <dynamic-snat44>
          <match>
            <source-network>
              <name>************</name>
            </source-network>
            <service>
              <name>any</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <pool-name>**************</pool-name>
          </translate-to>
        </dynamic-snat44>
      </rule>
      <rule>
        <name>vrpa4</name>
        <rule_en>true</rule_en>
        <dynamic-snat44>
          <match>
            <source-network>
              <name>************</name>
            </source-network>
            <service>
              <name>any</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <pool-name>**************</pool-name>
          </translate-to>
        </dynamic-snat44>
      </rule>
      <rule>
        <name>vrpa5</name>
        <rule_en>true</rule_en>
        <dynamic-snat44>
          <match>
            <source-network>
              <name>************</name>
            </source-network>
            <service>
              <name>any</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <pool-name>**************</pool-name>
          </translate-to>
        </dynamic-snat44>
      </rule>
      <rule>
        <name>Hop</name>
        <rule_en>true</rule_en>
        <dynamic-snat44>
          <match>
            <service>
              <name>any</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <pool-name>*************</pool-name>
          </translate-to>
        </dynamic-snat44>
      </rule>
      <rule>
        <name>hop_to_desk_ports</name>
        <rule_en>true</rule_en>
        <dynamic-snat44>
          <match>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <pool-name>*************</pool-name>
          </translate-to>
        </dynamic-snat44>
      </rule>
      <rule>
        <name>kamusm.gov.tr</name>
        <rule_en>true</rule_en>
        <dynamic-snat44>
          <match>
            <service>
              <name>any</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <pool-name>*************</pool-name>
          </translate-to>
        </dynamic-snat44>
      </rule>
      <rule>
        <name>WANtoDMZ_DNS_DNS_UDP_53</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>DNS_UDP_53</name>
            </dest-network>
            <service>
              <name>DNS</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>**********0</ipv4-address>
            <port>53</port>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>NEWDNS_DNS</name>
        <rule_en>false</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>DNS</name>
            </dest-network>
            <service>
              <name>DNS</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>**********</ipv4-address>
            <port>53</port>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>MRTG_MRTG</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>MRTG</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WEB_TO_E_BOOK_E_BOOK</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>E_BOOK</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WEB_TO_EBOOK_LAN_E_BOOK</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>E_BOOK</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WEB_DGRTNET_DDO_APP_dgrtnet_ddo_app</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>dgrtnet_ddo_app</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>**********6</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>Proje_Pazari_Proje_Pazari</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>Proje_Pazari</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>Firma_Erisim_Turnike_Gecis_Sis_Turnike_Gecis_Sistemle</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <source-network>
              <name>metek_grup_ıp</name>
            </source-network>
            <source-network>
              <name>3bi</name>
            </source-network>
            <dest-network>
              <name>Turnike_Gecis_Sistemleri</name>
            </dest-network>
            <service>
              <name>RDP</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WEBtoEtik_EBYS_EtikKurulu_VIP</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>EtikKurulu_VIP</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>TCP_8081</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>Mezunum_WAN2DMZ_Mezun_Portal</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>Mezun_Portal</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <service>
              <name>SSH</name>
            </service>
            <service>
              <name>MEZUN_PORTLAR</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WANtoDMZ_ETIK_RDP_EtikKurulu_VIP</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <source-network>
              <name>ETIK_IP</name>
            </source-network>
            <dest-network>
              <name>EtikKurulu_VIP</name>
            </dest-network>
            <service>
              <name>RDP</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WEB_Avrupa_Birligi_Proje_Koor_Avrupa_Birligi_Proje_Koo</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>Avrupa_Birligi_Proje_Koordinatörlügü</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>Erisim_Avrupa_Birligi_Proje_Koor_Avrupa_Birligi_Proje</name>
        <rule_en>false</rule_en>
        <static-dnat44>
          <match>
            <source-network>
              <name>Veysel_Sahin_IP</name>
            </source-network>
            <dest-network>
              <name>Avrupa_Birligi_Proje_Koordinatörlügü</name>
            </dest-network>
            <service>
              <name>RDP</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>UNIKYS_Kalite_Yazılımı_UNIKYS_Kalite_Yazılımı</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>UNIKYS_Kalite_Yazılımı</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <service>
              <name>SMTP</name>
            </service>
            <service>
              <name>SMTPS</name>
            </service>
            <service>
              <name>TCP_587</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>UNIKYS_Kalite_Yaz_SSH_UNIKYS_Kalite_Yazılımı</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <source-network>
              <name>OMÜ_Erişim</name>
            </source-network>
            <dest-network>
              <name>UNIKYS_Kalite_Yazılımı</name>
            </dest-network>
            <service>
              <name>SSH</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WEBtoDMZ_EBYS_EBYS</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>EBYS</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <service>
              <name>SMTP</name>
            </service>
            <service>
              <name>SMTPS</name>
            </service>
            <service>
              <name>TCP_587</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>DNS_DIS_NAT</name>
        <rule_en>true</rule_en>
        <dynamic-snat44>
          <match>
            <source-network>
              <name>DNS_DIS_IP</name>
            </source-network>
            <service>
              <name>any</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <pool-name>*************</pool-name>
          </translate-to>
        </dynamic-snat44>
      </rule>
      <rule>
        <name>OSYM</name>
        <rule_en>true</rule_en>
        <dynamic-snat44>
          <match>
            <service>
              <name>any</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <pool-name>*************</pool-name>
          </translate-to>
        </dynamic-snat44>
      </rule>
      <rule>
        <name>NVI</name>
        <rule_en>true</rule_en>
        <dynamic-snat44>
          <match>
            <service>
              <name>any</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <pool-name>*************</pool-name>
          </translate-to>
        </dynamic-snat44>
      </rule>
      <rule>
        <name>EBYS_US</name>
        <rule_en>true</rule_en>
        <dynamic-snat44>
          <match>
            <source-network>
              <name>EBYS_US</name>
            </source-network>
            <service>
              <name>any</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <pool-name>*************</pool-name>
          </translate-to>
        </dynamic-snat44>
      </rule>
      <rule>
        <name>SSL_VPN_POLICY_SSL_VPN_NAT</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>SSL_VPN_NAT</name>
            </dest-network>
            <service>
              <name>TCP_10443</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>*******</ipv4-address>
            <port>10443</port>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>MEHMET_KAKIZ</name>
        <rule_en>true</rule_en>
        <dynamic-snat44>
          <match>
            <source-network>
              <name>mehmet_kakiz</name>
            </source-network>
            <service>
              <name>HTTP</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <pool-name>*************</pool-name>
          </translate-to>
        </dynamic-snat44>
      </rule>
      <rule>
        <name>LANtoWAN_FULL</name>
        <rule_en>true</rule_en>
        <dynamic-snat44>
          <match>
            <source-network>
              <name>rektor</name>
            </source-network>
            <service>
              <name>any</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <pool-name>*************</pool-name>
          </translate-to>
        </dynamic-snat44>
      </rule>
      <rule>
        <name>LANtoWAN_GENEL_IZINLER</name>
        <rule_en>true</rule_en>
        <dynamic-snat44>
          <match>
            <service>
              <name>GenelIzinler</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <pool-name>*************</pool-name>
          </translate-to>
        </dynamic-snat44>
      </rule>
      <rule>
        <name>BIDBtoWAN</name>
        <rule_en>true</rule_en>
        <dynamic-snat44>
          <match>
            <source-network>
              <name>BTL_BIDB address</name>
            </source-network>
            <service>
              <name>any</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <pool-name>************</pool-name>
          </translate-to>
        </dynamic-snat44>
      </rule>
      <rule>
        <name>No_Capture_Portal</name>
        <rule_en>true</rule_en>
        <dynamic-snat44>
          <match>
            <source-network>
              <name>SifreSormaGroup</name>
            </source-network>
            <service>
              <name>TCP_587</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <pool-name>*************</pool-name>
          </translate-to>
        </dynamic-snat44>
      </rule>
      <rule>
        <name>No_Capture_Portal_for_NTP</name>
        <rule_en>true</rule_en>
        <dynamic-snat44>
          <match>
            <service>
              <name>NTP</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <pool-name>*************</pool-name>
          </translate-to>
        </dynamic-snat44>
      </rule>
      <rule>
        <name>vultrusercontent.com</name>
        <rule_en>true</rule_en>
        <dynamic-snat44>
          <match>
            <service>
              <name>any</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <pool-name>************0</pool-name>
          </translate-to>
        </dynamic-snat44>
      </rule>
      <rule>
        <name>RustdeskPorts</name>
        <rule_en>true</rule_en>
        <dynamic-snat44>
          <match>
            <service>
              <name>RustdeskPorts</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <pool-name>************0</pool-name>
          </translate-to>
        </dynamic-snat44>
      </rule>
      <rule>
        <name>RUSTDESK</name>
        <rule_en>true</rule_en>
        <dynamic-snat44>
          <match>
            <service>
              <name>any</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <pool-name>************0</pool-name>
          </translate-to>
        </dynamic-snat44>
      </rule>
      <rule>
        <name>RemoteServerToInternet</name>
        <rule_en>true</rule_en>
        <dynamic-snat44>
          <match>
            <source-network>
              <name>REMOTE_SERVER</name>
            </source-network>
            <service>
              <name>any</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <pool-name>************1</pool-name>
          </translate-to>
        </dynamic-snat44>
      </rule>
      <rule>
        <name>Rustdesk_IZIN</name>
        <rule_en>true</rule_en>
        <dynamic-snat44>
          <match>
            <service>
              <name>any</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <pool-name>************0</pool-name>
          </translate-to>
        </dynamic-snat44>
      </rule>
      <rule>
        <name>DMZtoWAN_NVI_NAT</name>
        <rule_en>true</rule_en>
        <dynamic-snat44>
          <match>
            <source-network>
              <name>PBS_server</name>
            </source-network>
            <service>
              <name>any</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <pool-name>*************</pool-name>
          </translate-to>
        </dynamic-snat44>
      </rule>
      <rule>
        <name>LANtoWAN_Yesevi</name>
        <rule_en>true</rule_en>
        <dynamic-snat44>
          <match>
            <service>
              <name>any</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <pool-name>*************</pool-name>
          </translate-to>
        </dynamic-snat44>
      </rule>
      <rule>
        <name>LANtoWAN_UtaritSQL_Utarit_Yemekhane</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <source-network>
              <name>kartokuyucu</name>
            </source-network>
            <dest-network>
              <name>Utarit_Yemekhane</name>
            </dest-network>
            <service>
              <name>FTP</name>
            </service>
            <service>
              <name>MS-SQL</name>
            </service>
            <service>
              <name>SSH</name>
            </service>
            <service>
              <name>TCP_4444</name>
            </service>
            <service>
              <name>TCP_8110</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>DMZ_AKADEMIK_PERF_ERISIM_Akademik_Performans_Sistemi</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <source-network>
              <name>betul_ozcinar</name>
            </source-network>
            <dest-network>
              <name>Akademik_Performans_Sistemi</name>
            </dest-network>
            <service>
              <name>any</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>LANtoDMZ_GENEL_IZINLER</name>
        <rule_en>true</rule_en>
        <static-snat44>
          <match>
            <service>
              <name>any</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <output-address/>
            <no-pat>false</no-pat>
            <try-no-pat>true</try-no-pat>
          </translate-to>
        </static-snat44>
      </rule>
      <rule>
        <name>NAT_CompleteAnatomy</name>
        <rule_en>true</rule_en>
        <dynamic-snat44>
          <match>
            <service>
              <name>any</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <pool-name>*************</pool-name>
          </translate-to>
        </dynamic-snat44>
      </rule>
      <rule>
        <name>NAT_KPS</name>
        <rule_en>true</rule_en>
        <dynamic-snat44>
          <match>
            <service>
              <name>any</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <pool-name>*************</pool-name>
          </translate-to>
        </dynamic-snat44>
      </rule>
      <rule>
        <name>NAT_OSYM</name>
        <rule_en>true</rule_en>
        <dynamic-snat44>
          <match>
            <service>
              <name>HTTP</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <pool-name>*************</pool-name>
          </translate-to>
        </dynamic-snat44>
      </rule>
      <rule>
        <name>WANtoDMZ_Remote_Metin_RemoteMetin</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <source-network>
              <name>METIN_VPN_IP_GRP</name>
            </source-network>
            <dest-network>
              <name>RemoteMetin</name>
            </dest-network>
            <service>
              <name>RDP</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>**********1</ipv4-address>
            <port>3389</port>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WANtoDMZ_ApiOzdegerlendirme_ApiOzdegerlendirme_443</name>
        <rule_en>false</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>ApiOzdegerlendirme_443</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <service>
              <name>TCP_8080</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>**********30</ipv4-address>
            <port>443</port>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WANtoDMZ_ApiOzdegerlendirme_ApiOzdegerlendirme_80</name>
        <rule_en>false</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>ApiOzdegerlendirme_80</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <service>
              <name>TCP_8080</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>**********30</ipv4-address>
            <port>80</port>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WANtoDMZ_ApiOzdegerlendirme_ApiOzdegerlendirme_8080</name>
        <rule_en>false</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>ApiOzdegerlendirme_8080</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <service>
              <name>TCP_8080</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>**********30</ipv4-address>
            <port>8080</port>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WANtoDMZ_TIP_OGR_BIRLIGI_TipOgrBirligi_443</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>TipOgrBirligi_443</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>**********78</ipv4-address>
            <port>443</port>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WANtoDMZ_TIP_OGR_BIRLIGI_TipOgrBirligi_80</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>TipOgrBirligi_80</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>**********78</ipv4-address>
            <port>80</port>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WANtoDMZ_FIRMA_SBSF_LAB_ORACLE_SBSF_LAB_ORACLE_3389</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <source-network>
              <name>PROTEL_IP</name>
            </source-network>
            <dest-network>
              <name>SBSF_LAB_ORACLE_3389</name>
            </dest-network>
            <service>
              <name>RDP</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
            <port>3389</port>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WANtoDMZ_AKADEMIK_TESVIK_AkademikTesvik_443</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>AkademikTesvik_443</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>**********81</ipv4-address>
            <port>443</port>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WANtoDMZ_AKADEMIK_TESVIK_AkademikTesvik_80</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>AkademikTesvik_80</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>**********81</ipv4-address>
            <port>80</port>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WANtoDMZ_ARIZA_TAKIP_ArizaTakip_443</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>ArizaTakip_443</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
            <port>443</port>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WANtoDMZ_ARIZA_TAKIP_ArizaTakip_80</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>ArizaTakip_80</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
            <port>80</port>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WANtoDMZ_SEMINER_SeminerSunucusu_443</name>
        <rule_en>false</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>SeminerSunucusu_443</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <service>
              <name>TCP_7443</name>
            </service>
            <service>
              <name>UDP_16384-32768</name>
            </service>
            <service>
              <name>UDP_1935</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
            <port>443</port>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WANtoDMZ_SEMINER_SeminerSunucusu_7443</name>
        <rule_en>false</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>SeminerSunucusu_7443</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <service>
              <name>TCP_7443</name>
            </service>
            <service>
              <name>UDP_16384-32768</name>
            </service>
            <service>
              <name>UDP_1935</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
            <port>7443</port>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WANtoDMZ_SEMINER_SeminerSunucusu_80</name>
        <rule_en>false</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>SeminerSunucusu_80</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <service>
              <name>TCP_7443</name>
            </service>
            <service>
              <name>UDP_16384-32768</name>
            </service>
            <service>
              <name>UDP_1935</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
            <port>80</port>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WANtoDMZ_SEMINER_SeminerSunucusu_UDP_16384-32768</name>
        <rule_en>false</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>SeminerSunucusu_UDP_16384-32768</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <service>
              <name>TCP_7443</name>
            </service>
            <service>
              <name>UDP_16384-32768</name>
            </service>
            <service>
              <name>UDP_1935</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WANtoDMZ_SEMINER_SeminerSunucusu_UDP_1935</name>
        <rule_en>false</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>SeminerSunucusu_UDP_1935</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <service>
              <name>TCP_7443</name>
            </service>
            <service>
              <name>UDP_16384-32768</name>
            </service>
            <service>
              <name>UDP_1935</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
            <port>1935</port>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WANtoDMZ_WEB_WebSunucusu_443</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>WebSunucusu_443</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <service>
              <name>DNS</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
            <port>443</port>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WANtoDMZ_WEB_WebSunucusu_80</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>WebSunucusu_80</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <service>
              <name>DNS</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
            <port>80</port>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WANtoDMZ_WEB_WebSunucusu_UDP_53</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>WebSunucusu_UDP_53</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <service>
              <name>DNS</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
            <port>53</port>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WANtoDMZ_Akupunktur_Akupunktur_443</name>
        <rule_en>false</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>Akupunktur_443</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
            <port>443</port>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WANtoDMZ_DSPACE_FIRMA_ERISIM_Dspace</name>
        <rule_en>false</rule_en>
        <static-dnat44>
          <match>
            <source-network>
              <name>DSPACE_FIRMA_IPLERI</name>
            </source-network>
            <dest-network>
              <name>Dspace</name>
            </dest-network>
            <service>
              <name>SSH</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WANtoDMZ_DSPACE_Dspace</name>
        <rule_en>false</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>Dspace</name>
            </dest-network>
            <service>
              <name>DSPACE_PORTS</name>
            </service>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <service>
              <name>TCP_587</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>DSPACE_NEW_FİRMAERİSİM_Dspace_New</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <source-network>
              <name>DSPACE_FIRMA_IPLERI</name>
            </source-network>
            <dest-network>
              <name>Dspace_New</name>
            </dest-network>
            <service>
              <name>SSH</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>**********32</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>DSPACE_NEW_Dspace_New</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>Dspace_New</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <service>
              <name>DSPACE_PORTS</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>**********32</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WEBtoDMZ_TIP_LMS_TipLMS</name>
        <rule_en>false</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>TipLMS</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WANtoDMZ_UZEM_Uzem</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>Uzem</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <service>
              <name>ALL_ICMP</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>**********01</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WANtoDMZ_PBS_Pbs_HTTP</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>Pbs_HTTP</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
            <port>80</port>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WANtoDMZ_PBS_Pbs_HTTPS</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>Pbs_HTTPS</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
            <port>443</port>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WANtoDMZ_AD_Active_Directory</name>
        <rule_en>false</rule_en>
        <static-dnat44>
          <match>
            <source-network>
              <name>MTU_WAN_IPS</name>
            </source-network>
            <dest-network>
              <name>Active_Directory</name>
            </dest-network>
            <service>
              <name>AD_ALL</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>**********76</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WANtoDMZ_AD_Active_Directory_Backup</name>
        <rule_en>false</rule_en>
        <static-dnat44>
          <match>
            <source-network>
              <name>MTU_WAN_IPS</name>
            </source-network>
            <dest-network>
              <name>Active_Directory_Backup</name>
            </dest-network>
            <service>
              <name>AD_ALL</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>**********77</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WANtoDMZ_KIMLIK_YONETIMI_SQL_Kimlik_Yönetim_Sistemi_SQ</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>Kimlik_Yönetim_Sistemi_SQL</name>
            </dest-network>
            <service>
              <name>SMTPS</name>
            </service>
            <service>
              <name>TCP_587</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WEBtoDMZ_Ek_DERS_Ek_Ders_Modülü</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>Ek_Ders_Modülü</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WANtoDMZ_OBS_FIRMA_Obs</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <source-network>
              <name>OBS_PROLIZ_IP</name>
            </source-network>
            <dest-network>
              <name>Obs</name>
            </dest-network>
            <service>
              <name>RDP</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WANtoDMZ_ARYOM_FIRMA_Aryom</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <source-network>
              <name>ARYOM_IP</name>
            </source-network>
            <dest-network>
              <name>Aryom</name>
            </dest-network>
            <service>
              <name>RDP</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>**********6</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WEBtoDMZ_OBS_Obs</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>Obs</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <service>
              <name>SMTP</name>
            </service>
            <service>
              <name>SMTPS</name>
            </service>
            <service>
              <name>TCP_587</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WEBtoDMZ_NOMYSEM_Nomysem_Sürekli_EgtMrk</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>Nomysem_Sürekli_EgtMrk</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <service>
              <name>SMTP</name>
            </service>
            <service>
              <name>SMTPS</name>
            </service>
            <service>
              <name>TCP_587</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WANtoDMZ_TOPLUMESAJ_Toplu_Mesaj_Ytt</name>
        <rule_en>false</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>Toplu_Mesaj_Ytt</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <service>
              <name>TCP_34210</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>**********70</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WANtoDMZ_AKADEMIK_PERF_SYS_Akademik_Performans_Sistemi</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>Akademik_Performans_Sistemi</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WANtoDMZ_AKADEMIK_PERF_SYS_FIRMA_Akademik_Performans_S</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <source-network>
              <name>5M_Bilisim</name>
            </source-network>
            <dest-network>
              <name>Akademik_Performans_Sistemi</name>
            </dest-network>
            <service>
              <name>RDP</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WANtoDMZ_WEB_SUNUCUSU_FIRMA_Web_Sunucusu</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <source-network>
              <name>FIRAT_UNI</name>
            </source-network>
            <dest-network>
              <name>Web_Sunucusu</name>
            </dest-network>
            <service>
              <name>SSH</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WANtoDMZ_WEB_SUNUCUSU_FIRMA_Web_Sitesi_AltBirimler</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <source-network>
              <name>FIRAT_UNI</name>
            </source-network>
            <dest-network>
              <name>Web_Sitesi_AltBirimler</name>
            </dest-network>
            <service>
              <name>SSH</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WANtoDMZ_WEB_SUNUCUSU_FIRMA_Proje_Pazari</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <source-network>
              <name>FIRAT_UNI</name>
            </source-network>
            <dest-network>
              <name>Proje_Pazari</name>
            </dest-network>
            <service>
              <name>SSH</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WEBtoDMZ_KATALOG_Katalog</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>Katalog</name>
            </dest-network>
            <service>
              <name>ALL_ICMP</name>
            </service>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <service>
              <name>TCP_5003</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WANtoDMZ_WEB_SUNUCUSU_Web_Sunucusu</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>Web_Sunucusu</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WANtoDMZ_WEB_SUNUCUSU_Web_Sitesi_AltBirimler</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>Web_Sitesi_AltBirimler</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WEBtoBAPSİS_BAPSİS</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>BAPSİS</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <service>
              <name>Smtp</name>
            </service>
            <service>
              <name>SMTPS</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>**********25</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WEBtoDMZ_YETKIM_Yetkim</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>Yetkim</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WEBtoDMZ_KIMLIK_YONETIMI_Kimlik_Yönetimi</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>Kimlik_Yönetimi</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <service>
              <name>SMTP</name>
            </service>
            <service>
              <name>SMTPS</name>
            </service>
            <service>
              <name>TCP_587</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WEBtoDMZ_UZEM_DB_Uzem_DB</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>Uzem_DB</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>**********03</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WEBtoDMZ_UTARIT_YMK_Utarit_Yemekhane</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>Utarit_Yemekhane</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <service>
              <name>TCP_81</name>
            </service>
            <service>
              <name>TCP_82</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WEBtoDMZ_YETENEK_SINAVI_Yeteneksınavı_WebServis</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>Yeteneksınavı_WebServis</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WEBtoDMZ_YETENEK_SINAVI_OTOMASYON_Yeteneksınavı_Otomas</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>Yeteneksınavı_Otomasyon</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <service>
              <name>SMTPS</name>
            </service>
            <service>
              <name>TCP_587</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WEBtoDMZ_YOS_BASVURU_YOS_BASVURU</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>YOS_BASVURU</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <service>
              <name>SMTPS</name>
            </service>
            <service>
              <name>TCP_587</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WEBtoDMZ_PTS_SRV_PTS</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>SRV_PTS</name>
            </dest-network>
            <service>
              <name>HTTP</name>
            </service>
            <service>
              <name>HTTPS</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WEBtoDMZ_PTS_FİRMA_ERİSİM_SRV_PTS</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <source-network>
              <name>Pts_Ozgur_Zaman_Firma</name>
            </source-network>
            <dest-network>
              <name>SRV_PTS</name>
            </dest-network>
            <service>
              <name>RDP</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>***********</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>DMZtoWAN</name>
        <rule_en>true</rule_en>
        <dynamic-snat44>
          <match>
            <service>
              <name>any</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <pool-name>*************</pool-name>
          </translate-to>
        </dynamic-snat44>
      </rule>
      <rule>
        <name>VPNtoWAN</name>
        <rule_en>true</rule_en>
        <dynamic-snat44>
          <match>
            <source-network>
              <name>SSLVPN_TUNNEL_ADDR1</name>
            </source-network>
            <service>
              <name>any</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <pool-name>************0</pool-name>
          </translate-to>
        </dynamic-snat44>
      </rule>
      <rule>
        <name>VPNtoDMZ_ARYOM_dgrtnet_ddo_app</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <source-network>
              <name>ARYOM_IP</name>
            </source-network>
            <source-network>
              <name>aryomtest</name>
            </source-network>
            <dest-network>
              <name>dgrtnet_ddo_app</name>
            </dest-network>
            <service>
              <name>any</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>**********6</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WANtoPROXY_Proxy_Sunucu_8080</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>Proxy_Sunucu_8080</name>
            </dest-network>
            <service>
              <name>any</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>**********16</ipv4-address>
            <port>8080</port>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>WAN_TO_LAN_Ziraat_Ziraat_Scada</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>Ziraat_Scada</name>
            </dest-network>
            <service>
              <name>12346</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>**********0</ipv4-address>
            <port>12346</port>
          </translate-to>
        </static-dnat44>
      </rule>
    </nat>
    <netconf-server xmlns="urn:ruijie:ntos:params:xml:ns:yang:netconf-server">
      <enabled>false</enabled>
      <idle-timeout>3600</idle-timeout>
    </netconf-server>
    <network-measure xmlns="urn:ruijie:ntos:params:xml:ns:yang:network-measure">
      <enabled>true</enabled>
      <message-send-enabled>false</message-send-enabled>
      <service>
        <dhcp>
          <enabled>true</enabled>
        </dhcp>
        <dns>
          <enabled>true</enabled>
          <monitor-threshold>1000</monitor-threshold>
        </dns>
        <nat>
          <enabled>true</enabled>
        </nat>
        <ipsec>
          <enabled>true</enabled>
        </ipsec>
        <sslvpn>
          <enabled>true</enabled>
        </sslvpn>
      </service>
      <warning-config>
        <link>
          <high-load>
            <enabled>true</enabled>
            <duration>120</duration>
            <threshold>95</threshold>
          </high-load>
          <suspect-disconnected>
            <enabled>true</enabled>
            <duration>300</duration>
            <threshold>10000</threshold>
          </suspect-disconnected>
          <change-to-down>
            <enabled>true</enabled>
          </change-to-down>
        </link>
        <app>
          <change-to-poor>
            <enabled>true</enabled>
          </change-to-poor>
        </app>
        <user>
          <wan-detect-failed>
            <enabled>true</enabled>
          </wan-detect-failed>
          <lan-detect-failed>
            <enabled>true</enabled>
          </lan-detect-failed>
          <change-to-poor>
            <enabled>true</enabled>
          </change-to-poor>
        </user>
        <dhcp-server>
          <ip-conflict>
            <enabled>true</enabled>
          </ip-conflict>
          <high-load>
            <enabled>true</enabled>
            <threshold>95</threshold>
          </high-load>
        </dhcp-server>
        <dns>
          <unuseable>
            <enabled>true</enabled>
          </unuseable>
        </dns>
        <nat>
          <hit-fail>
            <enabled>false</enabled>
          </hit-fail>
          <hit-miss>
            <enabled>false</enabled>
          </hit-miss>
        </nat>
        <ipsec>
          <disconnected>
            <enabled>true</enabled>
            <duration>120</duration>
          </disconnected>
        </ipsec>
        <sslvpn>
          <lost>
            <enabled>true</enabled>
            <threshold>10</threshold>
          </lost>
          <license>
            <enabled>true</enabled>
            <threshold>20</threshold>
          </license>
        </sslvpn>
      </warning-config>
      <flow-limit>
        <enabled>true</enabled>
        <up>80</up>
        <down>80</down>
      </flow-limit>
    </network-measure>
    <nfp xmlns="urn:ruijie:ntos:params:xml:ns:yang:nfp">
      <session>
        <state-inspection>
          <tcp>true</tcp>
          <tcp-mode>standard</tcp-mode>
          <icmp>true</icmp>
        </state-inspection>
      </session>
      <tunnel-inspection>
        <bridge>
          <VLAN>true</VLAN>
          <PPPoE>false</PPPoE>
          <GRE>false</GRE>
          <L2TPv2>false</L2TPv2>
        </bridge>
      </tunnel-inspection>
    </nfp>
    <ntp xmlns="urn:ruijie:ntos:params:xml:ns:yang:ntp">
      <enabled>true</enabled>
      <server>
        <address>ntp.ntsc.ac.cn</address>
        <version>4</version>
        <association-type>SERVER</association-type>
        <iburst>false</iburst>
        <prefer>false</prefer>
      </server>
      <server>
        <address>ntp1.aliyun.com</address>
        <version>4</version>
        <association-type>SERVER</association-type>
        <iburst>false</iburst>
        <prefer>false</prefer>
      </server>
    </ntp>
    <wba-portal xmlns="urn:ruijie:ntos:params:xml:ns:yang:portal">
      <enabled>false</enabled>
      <port>8081</port>
      <ssl-enabled>false</ssl-enabled>
      <redirection-mode>no-redirection</redirection-mode>
    </wba-portal>
    <portal-languages xmlns="urn:ruijie:ntos:params:xml:ns:yang:portal">
      <language>
        <name>zh_CN</name>
        <display-name>简体中文</display-name>
        <package-name>zh_CN.json</package-name>
      </language>
      <language>
        <name>en_US</name>
        <display-name>English</display-name>
        <package-name>en_US.json</package-name>
      </language>
      <language>
        <name>tr_TR</name>
        <display-name>Türkçe</display-name>
        <package-name>tr_TR.json</package-name>
      </language>
    </portal-languages>
    <pppoe xmlns="urn:ruijie:ntos:params:xml:ns:yang:pppoe">
      <multi-dial>
        <enabled>false</enabled>
      </multi-dial>
    </pppoe>
    <pppoe-server xmlns="urn:ruijie:ntos:params:xml:ns:yang:pppoe-server">
      <enabled>false</enabled>
    </pppoe-server>
    <replacement-messages xmlns="urn:ruijie:ntos:params:xml:ns:yang:replacement-messages">
      <management>
        <cache-enable-status>true</cache-enable-status>
      </management>
    </replacement-messages>
    <reputation-center xmlns="urn:ruijie:ntos:params:xml:ns:yang:reputation-center">
      <enabled>false</enabled>
    </reputation-center>
    <security-defend xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-defend">
      <basic-protocol-control-enabled>false</basic-protocol-control-enabled>
      <enabled>false</enabled>
    </security-defend>
    <security-zone xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-zone">
      <zone>
        <name>trust</name>
        <description>Trusted Zone</description>
        <priority>85</priority>
      </zone>
      <zone>
        <name>untrust</name>
        <description>Untrusted Zone</description>
        <priority>5</priority>
      </zone>
      <zone>
        <name>DMZ</name>
        <description>DMZ</description>
        <priority>50</priority>
      </zone>
      <zone>
        <name>MGMT</name>
        <description>自定义区域 MGMT</description>
        <priority>90</priority>
        <interface>
          <name>Ge0/2.3000</name>
        </interface>
      </zone>
      <zone>
        <name>LAN</name>
        <description>自定义区域 LAN</description>
        <priority>89</priority>
        <interface>
          <name>Ge0/2.2700</name>
        </interface>
        <interface>
          <name>Ge0/2.1010</name>
        </interface>
        <interface>
          <name>Ge0/2.3200</name>
        </interface>
        <interface>
          <name>Ge0/2.2400</name>
        </interface>
        <interface>
          <name>Ge0/2.2600</name>
        </interface>
        <interface>
          <name>Ge0/2.2000</name>
        </interface>
        <interface>
          <name>Ge0/2.3100</name>
        </interface>
        <interface>
          <name>Ge0/2.2100</name>
        </interface>
        <interface>
          <name>Ge0/2.2500</name>
        </interface>
        <interface>
          <name>Ge0/2.2200</name>
        </interface>
        <interface>
          <name>Ge0/2</name>
        </interface>
      </zone>
      <zone>
        <name>WAN</name>
        <description>自定义区域 WAN</description>
        <priority>29</priority>
        <interface>
          <name>TenGe0/0</name>
        </interface>
      </zone>
    </security-zone>
    <session-limit xmlns="urn:ruijie:ntos:params:xml:ns:yang:session-limit">
      <pps-limit>
        <enabled>false</enabled>
        <global-pps>0</global-pps>
      </pps-limit>
      <sps-limit>
        <enabled>false</enabled>
        <global-sps>0</global-sps>
      </sps-limit>
      <total-session>
        <enabled>false</enabled>
      </total-session>
    </session-limit>
    <ssh-server xmlns="urn:ruijie:ntos:params:xml:ns:yang:ssh-server">
      <enabled>true</enabled>
      <port>22</port>
      <deny-count>3</deny-count>
      <unlock-time>60</unlock-time>
    </ssh-server>
    <ssl-proxy xmlns="urn:ruijie:ntos:params:xml:ns:yang:ssl-proxy">
      <profile>
        <name>default</name>
        <description>Default Template, Traffic proxy for Internet access of users.</description>
        <outbound/>
      </profile>
      <ca-cert>
        <trust-cert>default_ca</trust-cert>
      </ca-cert>
    </ssl-proxy>
    <network-stack xmlns="urn:ruijie:ntos:params:xml:ns:yang:system">
      <ipv4>
        <arp-ignore>check-interface-and-subnet</arp-ignore>
      </ipv4>
    </network-stack>
    <threat-intelligence xmlns="urn:ruijie:ntos:params:xml:ns:yang:threat-intelligence">
      <management>
        <enable-status>false</enable-status>
        <enable-ai>false</enable-ai>
      </management>
    </threat-intelligence>
    <time-range xmlns="urn:ruijie:ntos:params:xml:ns:yang:time-range">
      <range>
        <name>any</name>
        <description>Time range of all the time.</description>
        <period>
          <start>00:00:00</start>
          <end>23:59:59</end>
          <weekday>
            <key>sun</key>
          </weekday>
          <weekday>
            <key>mon</key>
          </weekday>
          <weekday>
            <key>tue</key>
          </weekday>
          <weekday>
            <key>wed</key>
          </weekday>
          <weekday>
            <key>thu</key>
          </weekday>
          <weekday>
            <key>fri</key>
          </weekday>
          <weekday>
            <key>sat</key>
          </weekday>
        </period>
      </range>
      <range>
        <name>always</name>
        <description>always</description>
        <period>
          <start>00:00:00</start>
          <end>23:59:59</end>
          <weekday>
            <key>sun</key>
          </weekday>
          <weekday>
            <key>mon</key>
          </weekday>
          <weekday>
            <key>tue</key>
          </weekday>
          <weekday>
            <key>wed</key>
          </weekday>
          <weekday>
            <key>thu</key>
          </weekday>
          <weekday>
            <key>fri</key>
          </weekday>
          <weekday>
            <key>sat</key>
          </weekday>
        </period>
      </range>
      <range>
        <name>none</name>
        <description>none</description>
      </range>
      <range>
        <name>default-darrp-optimize</name>
        <description>default-darrp-optimize</description>
        <period>
          <start>01:00:00</start>
          <end>01:30:00</end>
          <weekday>
            <key>sun</key>
          </weekday>
          <weekday>
            <key>mon</key>
          </weekday>
          <weekday>
            <key>tue</key>
          </weekday>
          <weekday>
            <key>wed</key>
          </weekday>
          <weekday>
            <key>thu</key>
          </weekday>
          <weekday>
            <key>fri</key>
          </weekday>
          <weekday>
            <key>sat</key>
          </weekday>
        </period>
      </range>
    </time-range>
    <traffic-analy xmlns="urn:ruijie:ntos:params:xml:ns:yang:traffic-analy">
      <enabled>false</enabled>
    </traffic-analy>
    <upnp-proxy xmlns="urn:ruijie:ntos:params:xml:ns:yang:upnp-proxy">
      <enabled>false</enabled>
      <bind-rule>ip</bind-rule>
      <advance>
        <automatic-enrollment>
          <enabled>false</enabled>
          <registration-time>1440</registration-time>
          <logout-check-period>3</logout-check-period>
        </automatic-enrollment>
        <terminal-authorization>
          <enabled>false</enabled>
        </terminal-authorization>
        <linkage-service>
          <enabled>false</enabled>
        </linkage-service>
        <offline-detect>
          <flow-rate>0</flow-rate>
        </offline-detect>
        <scheduled-offline>
          <enabled>false</enabled>
          <time>00:00</time>
        </scheduled-offline>
        <quick-response-code-valid-time>
          <time>480</time>
        </quick-response-code-valid-time>
      </advance>
      <reserve>
        <single-ip-process>
          <interval-time>5</interval-time>
          <max-package>40</max-package>
        </single-ip-process>
        <unicast>
          <enabled>false</enabled>
        </unicast>
        <web-url-compatible>
          <enabled>false</enabled>
        </web-url-compatible>
        <map-cover-mode>
          <enabled>false</enabled>
        </map-cover-mode>
        <server-capacity>1</server-capacity>
      </reserve>
    </upnp-proxy>
    <webauth xmlns="urn:ruijie:ntos:params:xml:ns:yang:webauth">
      <authentication-options>
        <portal-authentication>
          <portal-group>
            <name>cportal</name>
            <protocol>portal</protocol>
          </portal-group>
        </portal-authentication>
      </authentication-options>
      <single-sign-on>
        <ad>
          <method>plugin</method>
        </ad>
      </single-sign-on>
    </webauth>
    <wlan xmlns="urn:ruijie:ntos:params:xml:ns:yang:wlan">
      <web-ac>
        <topology>ac-connect</topology>
      </web-ac>
      <diag>
        <enabled>false</enabled>
      </diag>
      <ac-controller>
        <ac-name>Ruijie_Ac_V0001</ac-name>
        <acctrl-trap>
          <acap-microap-ctrl>
            <enabled>false</enabled>
          </acap-microap-ctrl>
          <acap-updown-ctrl>
            <enabled>false</enabled>
          </acap-updown-ctrl>
          <acap-joinfail-ctrl>
            <enabled>false</enabled>
          </acap-joinfail-ctrl>
          <acap-decryeroreport-ctrl>
            <enabled>false</enabled>
          </acap-decryeroreport-ctrl>
          <acap-imageupdt-ctrl>
            <enabled>false</enabled>
          </acap-imageupdt-ctrl>
          <acap-timestamp-ctrl>
            <enabled>false</enabled>
          </acap-timestamp-ctrl>
          <acsta-oper-ctrl>
            <enabled>false</enabled>
          </acsta-oper-ctrl>
        </acctrl-trap>
        <ap-auth>
          <serial>
            <enabled>false</enabled>
          </serial>
          <password>
            <enabled>false</enabled>
          </password>
          <certificate>
            <enabled>false</enabled>
          </certificate>
        </ap-auth>
        <ap-priority>
          <enabled>false</enabled>
        </ap-priority>
        <bind-ap-mac>
          <enabled>false</enabled>
        </bind-ap-mac>
        <balance-group>
          <flow-balance-group>
            <base>10</base>
          </flow-balance-group>
        </balance-group>
        <sta-balance>
          <num-limit>
            <enabled>false</enabled>
          </num-limit>
        </sta-balance>
        <sta-blacklist>
          <enabled>false</enabled>
        </sta-blacklist>
        <black-white-list>
          <blacklist>
            <enabled>true</enabled>
          </blacklist>
          <whitelist>
            <enabled>false</enabled>
          </whitelist>
        </black-white-list>
        <ac-control>
          <enabled>false</enabled>
        </ac-control>
        <ap-image>
          <auto-upgrade>
            <enabled>false</enabled>
          </auto-upgrade>
        </ap-image>
        <capwap>
          <dtls>
            <enabled>true</enabled>
          </dtls>
        </capwap>
      </ac-controller>
      <wids>
        <countermeasures>
          <enabled>false</enabled>
          <rssi-min>25</rssi-min>
          <channel-match>false</channel-match>
        </countermeasures>
      </wids>
    </wlan>
    <network-obj xmlns="urn:ruijie:ntos:params:xml:ns:yang:network-obj">
      <address-set>
        <name>ApiOzdegerlendirme_80</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ApiOzdegerlendirme_443</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ApiOzdegerlendirme_8080</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>TipOgrBirligi_80</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>TipOgrBirligi_443</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>SBSF_LAB_ORACLE_3389</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>AkademikTesvik_80</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>AkademikTesvik_443</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ArizaTakip_80</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ArizaTakip_443</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>SeminerSunucusu_80</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>SeminerSunucusu_443</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>SeminerSunucusu_7443</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>SeminerSunucusu_UDP_1935</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>SeminerSunucusu_UDP_16384-32768</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>WebSunucusu_80</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>WebSunucusu_443</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>WebSunucusu_UDP_53</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>DNS_UDP_53</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Akupunktur_80</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Akupunktur_443</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>RemoteMetin</name>
        <ip-set>
          <ip-address>************1/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Dspace</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Active_Directory_Backup</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Active_Directory</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Yeteneksınavı_Otomasyon</name>
        <ip-set>
          <ip-address>************02/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Yeteneksınavı_WebServis</name>
        <ip-set>
          <ip-address>************01/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>TipLMS</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Utarit_Yemekhane</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Uzem</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Pbs_HTTP</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Kimlik_Yönetim_Sistemi_SQL</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Ek_Ders_Modülü</name>
        <ip-set>
          <ip-address>************3/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Obs</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Nomysem_Sürekli_EgtMrk</name>
        <ip-set>
          <ip-address>************2/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Toplu_Mesaj_Ytt</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Akademik_Performans_Sistemi</name>
        <ip-set>
          <ip-address>************5/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Web_Sunucusu</name>
        <ip-set>
          <ip-address>************4/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Web_Sitesi_AltBirimler</name>
        <ip-set>
          <ip-address>************7/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Katalog</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Manage_Engin</name>
        <ip-set>
          <ip-address>************8/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>EBYS</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Yetkim</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Kimlik_Yönetimi</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Uzem_DB</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Proxy_Sunucu_8080</name>
        <ip-set>
          <ip-address>************12/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>BAPSİS</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Pbs_HTTPS</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Pbs_RDP</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Pbs_MYSQL</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Dspace_New</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>YOS_BASVURU</name>
        <ip-set>
          <ip-address>************9/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>SRV_PTS</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>SSL_VPN_NAT</name>
        <ip-set>
          <ip-address>************0/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Avrupa_Birligi_Proje_Koordinatörlügü</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>EtikKurulu_VIP</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>DNS</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Turnike_Gecis_Sistemleri</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Proje_Pazari</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>MRTG</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>************</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>dgrtnet_ddo_app</name>
        <ip-set>
          <ip-address>************6/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>E_BOOK</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>**************_VIP</name>
        <ip-set>
          <ip-address>**************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>**************_VIP</name>
        <ip-set>
          <ip-address>**************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>**************_VIP</name>
        <ip-set>
          <ip-address>**************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>**************_VIP</name>
        <ip-set>
          <ip-address>**************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>**************_VIP</name>
        <ip-set>
          <ip-address>**************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>**************_VIP</name>
        <ip-set>
          <ip-address>**************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Aryom</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Mezun_Portal</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Ziraat_Scada</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>UNIKYS_Kalite_Yazılımı</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>SSLVPN_TUNNEL_ADDR1</name>
        <ip-set>
          <ip-address>*************-*************</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>BATTALGAZI</name>
        <ip-set>
          <ip-address>**********/20</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>DMZaddress</name>
        <ip-set>
          <ip-address>**********/23</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>DNS_SERVER</name>
        <ip-set>
          <ip-address>**********0/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>KALE_IP_SANTRAL</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ARAPGIR_IP_SANTRAL</name>
        <ip-set>
          <ip-address>**********97/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>DGNSHR_IP_SANTRAL</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>DARENDE_IP_SANTRAL</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>KALE_IP_SANTRAL_2</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ARAPGIR_IP_SANTRAL_2</name>
        <ip-set>
          <ip-address>**********98/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>YYMYO_IP_SANTRAL</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>YYMYO_IP_SANTRAL_2</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>DGNSHR_IP_SANTRAL_2</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>DGNSHR_IP_SANTRAL_3</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>DARENDE_IP_SANTRAL_2</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>HEKIMHAN_IP_SANTRAL</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>HEKIMHAN_IP_SANTRAL_2</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>AKCADAG_IP_SANTRAL</name>
        <ip-set>
          <ip-address>**********86/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>AKCADAG_IP_SANTRAL_2</name>
        <ip-set>
          <ip-address>**********87/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>DARENDE_IP_SANTRAL_3</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>BTTLGZ_IP_SANTRAL</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>BTTLGZ_IP_SANTRAL_2</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>BTTLGZ_IP_SANTRAL_3</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>BTTLGZ_IP_SANTRAL_4</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>BTTLGZ_IP_SANTRAL_5</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>BTTLGZ_IP_SANTRAL_6</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>BTTLGZ_IP_SANTRAL_7</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>BTTLGZ_IP_SANTRAL_8</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>BTTLGZ_IP_SANTRAL_9</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>BTTLGZ_IP_SANTRAL_10</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>BTTLGZ_IP_SANTRAL_12</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>YESİLYURT_REKT</name>
        <ip-set>
          <ip-address>**********/20</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>YESILYURT_SANTRAL_IP</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>YEMEKHANE_SERVER</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ARAPGIR_TURNIKE</name>
        <ip-set>
          <ip-address>**********01-**********02</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>KALE_TURNIKE</name>
        <ip-set>
          <ip-address>***********-***********</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>YEMEKHANE_1</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>YEMEKHANE_3</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>YEMEKHANE_2</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Merkez_Yemekhane_Harcama_I</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>DGNSHR_TURNIKE</name>
        <ip-set>
          <ip-address>***********-***********</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>DARENDE_TURNIKE</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ERU_IP</name>
        <ip-set>
          <ip-address>************/23</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>CompleteAnatomy1</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>CompleteAnatomy2</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>CompleteAnatomy3</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>KPS_IP</name>
        <ip-set>
          <ip-address>**********/24</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>OSYM_IP</name>
        <ip-set>
          <ip-address>************/24</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>PROTEL_IP</name>
        <ip-set>
          <ip-address>***************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>METIN_VPN_IP</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>**************</name>
        <ip-set>
          <ip-address>**************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>**************</name>
        <ip-set>
          <ip-address>**************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>***************</name>
        <ip-set>
          <ip-address>***************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>************</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>10.0.0.0_8</name>
        <ip-set>
          <ip-address>10.0.0.0/8</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>***********_16</name>
        <ip-set>
          <ip-address>***********/16</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>**********_16</name>
        <ip-set>
          <ip-address>**********/16</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>MTU_WAN_IPS</name>
        <ip-set>
          <ip-address>************/24</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>**************_32</name>
        <ip-set>
          <ip-address>**************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>5M_Bilisim</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>**************_32</name>
        <ip-set>
          <ip-address>**************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>**************_32</name>
        <ip-set>
          <ip-address>**************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>***************_32</name>
        <ip-set>
          <ip-address>***************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>***************_32</name>
        <ip-set>
          <ip-address>***************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>************_32</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>**************_32</name>
        <ip-set>
          <ip-address>**************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>*************_22</name>
        <ip-set>
          <ip-address>*************/22</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>*************_32</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>**************_32</name>
        <ip-set>
          <ip-address>**************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>AP_CONTROLLER</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>KALE_AP_1</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>AKCADAG_AP</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>DARENDE_KANTIN_AP_1</name>
        <ip-set>
          <ip-address>**********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>DARENDE_KIZ_YURDU_AP</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>DARENDE_KIZ_YURDU_AP_2</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>DARENDE_ERKEK_YURDU_AP_1</name>
        <ip-set>
          <ip-address>**********0/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>METIN_GECICI</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>deren_unal</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>UMRAN</name>
        <ip-set>
          <ip-address>**********2/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>SRV_SEMINER_IP</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>SRV_UZEM_LMS</name>
        <ip-set>
          <ip-address>**********01/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>SRV_DIS_SEMINER_IP</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>SRV_DIS_UZEM_LMS_IP</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>SRV_WEB_ALTBIRIM_IP</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>SRV_DIS_WEB_ALTBIRIM_IP</name>
        <ip-set>
          <ip-address>************7/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>SRV_YETKIM_IP</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>SRV_DIS_YETKIM_IP</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>SRV_OBS_IP</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>SRV_DIS_OBS_IP</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>SRV_OZDEGERLENDIRME_IP</name>
        <ip-set>
          <ip-address>**********30/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>SRV_DIS_OZDEGERLENDIRME_IP</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>SRV_WEB_ESKI_IP</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>SRV_DIS_WEB_ESKI_IP</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>SRV_TIP_OGR_BIRLIGI_IP</name>
        <ip-set>
          <ip-address>**********78/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>SRV_DIS_TIP_OGR_BIRLIGI_IP</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>SRV_SUREKLI_EGITIM_MRK_IP</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>SRV_DIS_SUREKLI_EGITIM_MRK_IP</name>
        <ip-set>
          <ip-address>************2/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>SRV_DSPACE_IP</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>SRV_DIS_DSPACE_IP</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>SRV_AKADEMIK_TESVIK_IP</name>
        <ip-set>
          <ip-address>**********81/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>SRV_DIS_AKADEMIK_TESVIK_IP</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>SRV_WEB_YENI_IP</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>SRV_DIS_WEB_YENI_IP</name>
        <ip-set>
          <ip-address>************4/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>SRV_PERS_OTOM_IP</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>SRV_DIS_PERS_OTOM_IP</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>BIDB_SERVER_DMZ</name>
        <ip-set>
          <ip-address>**********-************</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>FIREWALL_DMZ_IP</name>
        <ip-set>
          <ip-address>**********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>OBS_PROLIZ_IP</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>AD_LOCAL_IP</name>
        <ip-set>
          <ip-address>**********76/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>REMOTE_SERVER</name>
        <ip-set>
          <ip-address>**********1/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ip-adresim.net</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Necmettin</name>
        <ip-set>
          <ip-address>**********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>KG_NIZAMIYE</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>KG_REKTORLUK</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>KG_MRK_YASAM_MERKEZI</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>KG_MRK_BATTALGAZI_MYO</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>KG_AKCADAG_MYO</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>KG_ARAPGIR_MYO</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>KG_KALE_MYO</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>KG_HEKIMHAN_MYO</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>KG_DARENDE_MYO</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>KG_YESILYURT_MYO</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>KG_YESILYURT_REKTORLUK</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>SRV_PERS_TKP</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>YESILYURT_SANTRAL_3</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>YESILYURT_SANTRAL_4</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>DSPACE_FIRMA_1</name>
        <ip-set>
          <ip-address>**************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>DSPACE_FIRMA_2</name>
        <ip-set>
          <ip-address>**************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>SRV_KIMLIK_IP</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>SRV_AD_BCK</name>
        <ip-set>
          <ip-address>**********77/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>SRV_EK_DERS_IP</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>SRV_SEM</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>turtep.yesevi.edu.tr</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>BackupServer</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>DataDomain</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>DataDomain2</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>METIN_VPN_IP_2</name>
        <ip-set>
          <ip-address>**************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>SRV_DIS_EK_DERS_IP</name>
        <ip-set>
          <ip-address>************3/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>BAPSİS_İP</name>
        <ip-set>
          <ip-address>**********25/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>BAPSİS_DİS_İP</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>proxy</name>
        <ip-set>
          <ip-address>***********/24</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Proxy_Sunucusu</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>HEKIMHAN_AP</name>
        <ip-set>
          <ip-address>**********1/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>HEKİMHAN_AP</name>
        <ip-set>
          <ip-address>**********86/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>FILE_SERVER</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>OBYS_Server</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>PBS_server</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Rustdesk</name>
        <ip-set>
          <ip-address>**************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Rustdesk2</name>
        <ip-set>
          <ip-address>**************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Rustdesk3</name>
        <ip-set>
          <ip-address>**************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ArapgirAP</name>
        <ip-set>
          <ip-address>**********0/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Battalgazi</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>kartokuyucu</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>NMS</name>
        <ip-set>
          <ip-address>**********12/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>************</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>VRPA_WAN</name>
        <ip-set>
          <ip-address>**********/24</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>VRPA_DATA</name>
        <ip-set>
          <ip-address>**********/24</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>DSpace_New_IP</name>
        <ip-set>
          <ip-address>**********32/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>DSpace_New_DIS_IP</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>virüs_ıp_2</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>necmettin_gul</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ARIZATAKIP</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>SRV_YETENEK_SİNAVİ_OTOMASYON_IP</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>SRV_YETENEK_SİNAVİ_WEB_IP</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>SRV_YOS_BASVURU_IP</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Sunucu_PC</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>MTN_GECICI_IP</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Pts_Ozgur_Zaman_Firma</name>
        <ip-set>
          <ip-address>**************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>REAL_IPLER</name>
        <ip-set>
          <ip-address>************/24</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>IKCU_RPA_1</name>
        <ip-set>
          <ip-address>**************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>IKCU_RPA_2</name>
        <ip-set>
          <ip-address>**************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>IKCU_RPA_3</name>
        <ip-set>
          <ip-address>**************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>IKCU_RPA_4</name>
        <ip-set>
          <ip-address>**************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>IKCU_RPA_5</name>
        <ip-set>
          <ip-address>**************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>IKCU_RPA_6</name>
        <ip-set>
          <ip-address>**************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>MTU_RPA_1</name>
        <ip-set>
          <ip-address>**************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>MTU_RPA_2</name>
        <ip-set>
          <ip-address>**************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>MTU_RPA_3</name>
        <ip-set>
          <ip-address>**************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>vRPA_CLS</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>vRPA_1</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>vRPA_3</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>vRPA_2</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>EBYS_US</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>miyase_coban</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>EBYS_DIS_IP</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>umran_turkmen</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>TargitasNAC</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ibrahim_ip</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>halisbozkurt</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>KutuphaneServer</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>rektor</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>***********</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ek</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ma</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>md</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Merkez_Yemekhane_Pos</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Merkez_Yemekhane_Harcama_II</name>
        <ip-set>
          <ip-address>***********0/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Arapgir_Myo_Pos</name>
        <ip-set>
          <ip-address>**********01/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Arapgir_Myo_Harcama</name>
        <ip-set>
          <ip-address>**********02/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Akcadag_Myo_Pos</name>
        <ip-set>
          <ip-address>**********48/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Akcadag_Myo_Harcama</name>
        <ip-set>
          <ip-address>**********47/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Darende_Myo_Pos</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Darende_Myo_Harcama</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Dogansehir_Myo_Pos</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Dogansehir_Myo_Harcama</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Hekimhan_Myo_Pos</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Hekimhan_Myo_Harcama</name>
        <ip-set>
          <ip-address>**********87/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Kale_Myo_Pos</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Kale_Myo_Harcama</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Yesilyurt_Myo_Harcama</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Yesilyurt_Myo_Pos</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Merkez_Yemekhane_Harcama_III</name>
        <ip-set>
          <ip-address>***********3/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Merkez_Yemekhane_Harcama_IV</name>
        <ip-set>
          <ip-address>***********4/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Kıosk_I</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Kıosk_II</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>3bi</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Veysel_Sahin_IP</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ExtremeXIQController</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>sayistay_3</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>sayistay_4</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>log_dizustü</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>santral</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>battalgazi_nizamiye</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>seyhan_dogan_kart_okuma</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>test_ncgul</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>k_kemal</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>DARENDE_TUM_NET</name>
        <ip-set>
          <ip-address>**********/16</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>DHCP_SERVER</name>
        <ip-set>
          <ip-address>***********4/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>HEKIMHAN_TUM_NET</name>
        <ip-set>
          <ip-address>**********/16</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>YYMYO_Santral</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ARUBA_SW</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>proxysunucu_2</name>
        <ip-set>
          <ip-address>***********15/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Yeşilyurt_Rektörlük_Yemekhane</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Merkez</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Santral_Merkez1</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Santral_Merkez2</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Santral_Merkez3</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Santral_Ogrenci_Yasam</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Santral_Y_Yurt_MYO</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Santral_B_Gazi_MYO</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Santral_Darende</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Santral_Akcadag</name>
        <ip-set>
          <ip-address>**********86/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Santral_Kale</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Santral_Dogansehir</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Santral_YYurt_Rektorluk</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>halife_tel</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>FILE_SERVER_II</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>pts_sunucusu</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Rektör_Makam</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>packet_deneme</name>
        <ip-set>
          <ip-address>**********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ETIK_IP</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>DNS_DIS_IP</name>
        <ip-set>
          <ip-address>**********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Yesilyurt_yemekhane_yeni</name>
        <ip-set>
          <ip-address>**********55/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Yesilyurt_Utarit_X</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Yeşilyurt_Rektörlük_Kios</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Mücahit_PC</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ISATURGUT_3</name>
        <ip-set>
          <ip-address>**********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ISATURGUT</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ISATURGUT_2</name>
        <ip-set>
          <ip-address>**********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>KG_YESILYURT_KONTEYNER</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>betul_ozcinar</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ssbf_lab_1</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ssbf_lab_2</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ssbf_lab_3</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ssbf_lab_4</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ssbf_lab_5</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ssbf_lab_6</name>
        <ip-set>
          <ip-address>**********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ssbf_lab_7</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ssbf_lab_8</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ssbf_lab_9</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ssbf_lab_10</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ssbf_lab_11</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ssbf_lab_12</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ssbf_lab_13</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ssbf_lab_14</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ssbf_lab_15</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ssbf_lab_16</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ssbf_lab_17</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ssbf_lab_18</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ssbf_lab_19</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ssbf_lab_20</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>SBBF_Oracle_Opera_sunucu</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>SBSF_LAB_LOCAL_IP</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>KG_YESILYURT_SANAT_TASARIM</name>
        <ip-set>
          <ip-address>***********1/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>sbbf_lab_21</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>battalgazi_k_pos</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ISA_DURGUT_IV</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ISA_DURGUT_V</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ISATURGUT_4</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ISATURGUT_5</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>arapgit_santral</name>
        <ip-set>
          <ip-address>**********12/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Arapgir_Santral</name>
        <ip-set>
          <ip-address>**********12/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>YesilyurtNizamiyePTS</name>
        <ip-set>
          <ip-address>***********1/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Bat_ana_nzm_grs</name>
        <ip-set>
          <ip-address>*********11/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Bat_ana_nzm_cks</name>
        <ip-set>
          <ip-address>*********12/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Bat_rkt_grs</name>
        <ip-set>
          <ip-address>*********13/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Bat_rkt_cks</name>
        <ip-set>
          <ip-address>*********14/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Btl_ana_sunucu</name>
        <ip-set>
          <ip-address>**********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>SRV_PROJE_PAZARİ</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>FORTILOGGER</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Yesilyurt_Rkt_Yemekhane_Pos</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>sahapc</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>metek_grup_ıp</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>rektorluk_toplantı_salonu</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>FortiLogger_IP</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>yesilyurt_kantin_pos</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>mehmet_kakiz</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>sefanur_korkmaz</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ssbf_lab_ANAMAKINA</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>vultr</name>
        <ip-set>
          <ip-address>***********/24</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>vultr2</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>************</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ncgul_tel</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>umran_tel</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Ruijie_Ap1</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Ruijie_Ap2</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>HekimhanSW</name>
        <ip-set>
          <ip-address>**********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>yesilyurt_Deneme</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>battalgazi_turnike_I</name>
        <ip-set>
          <ip-address>**********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>battalgazi_turnike_II</name>
        <ip-set>
          <ip-address>**********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>battalgazi_turnike_III</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>SRV_TurnikeSistemleri_Lokal_IP</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>mehmetvuralpc</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>mehmetvuralpc2</name>
        <ip-set>
          <ip-address>**********30/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>DARENDE_CAM</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Akif_Ates_Darende</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>cacti</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Yesilşyurt_Pts_Bilgisayari</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>YSLYRT_DMZ_address</name>
        <ip-set>
          <ip-address>***********/23</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>**********04</name>
        <ip-set>
          <ip-address>**********04/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>YSLYRT_DMZ</name>
        <ip-set>
          <ip-address>************/23</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ahmet_selim_ozkan_makam_tv</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>vrpa</name>
        <ip-set>
          <ip-address>************-************</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>vRPAIP</name>
        <ip-set>
          <ip-address>**************-**************</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>test</name>
        <ip-set>
          <ip-address>*************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>RPA_CLS</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>RPA_1</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>RPA_2</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>RPA_3</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>RPA_PLUGIN</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ETISAN_YSL_RKT_OGR_POS</name>
        <ip-set>
          <ip-address>**********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ETISAN_BTL_POS</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>HALIFE_TEST</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>NAGIOS_NECMETTIN</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ETISAN_POS_BAT</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ETISAN_POS_BAT_2</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>app</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>TPLINKCNTRL</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>AP1</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>YSLMYOAP2</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>YSLMYOAP3</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>************</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>************</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>************</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>************</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>************</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>************</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ARYOM_IP</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>aryomtest</name>
        <ip-set>
          <ip-address>**************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>KALE_ETISAN_POS</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>AKCADAG_POS</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ARAPGİR_POS</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>HEKİMHAN_POS</name>
        <ip-set>
          <ip-address>**********90/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>DARENDE_POS</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>YESİLYURT_REK_POS</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>YESİLYURT_REK_POS2</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>YESİLYURT_POS_3</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>BATTALGAZİ_POS</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>KALE_POS_2</name>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>YESİLYURT_POS_1</name>
        <ip-set>
          <ip-address>************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>Ziraat_Fak_Scada</name>
        <ip-set>
          <ip-address>**********0/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>**********6</name>
        <ip-set>
          <ip-address>**********6/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ETISAn_BTL_POS</name>
        <description>30.04.2025 yeni takılan cihaz</description>
        <ip-set>
          <ip-address>***********/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>OMÜ_Erişim</name>
        <ip-set>
          <ip-address>**************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>NAS_OMV</name>
        <ip-set>
          <ip-address>**********/32</ip-address>
        </ip-set>
      </address-set>
      <address-group>
        <name>IP_SANTRAL_GRP</name>
        <address-set>
          <name>ARAPGIR_IP_SANTRAL</name>
        </address-set>
        <address-set>
          <name>ARAPGIR_IP_SANTRAL_2</name>
        </address-set>
        <address-set>
          <name>DARENDE_IP_SANTRAL</name>
        </address-set>
        <address-set>
          <name>DARENDE_IP_SANTRAL_2</name>
        </address-set>
        <address-set>
          <name>DGNSHR_IP_SANTRAL</name>
        </address-set>
        <address-set>
          <name>DGNSHR_IP_SANTRAL_2</name>
        </address-set>
        <address-set>
          <name>DGNSHR_IP_SANTRAL_3</name>
        </address-set>
        <address-set>
          <name>KALE_IP_SANTRAL</name>
        </address-set>
        <address-set>
          <name>KALE_IP_SANTRAL_2</name>
        </address-set>
        <address-set>
          <name>YYMYO_IP_SANTRAL</name>
        </address-set>
        <address-set>
          <name>YYMYO_IP_SANTRAL_2</name>
        </address-set>
        <address-set>
          <name>AKCADAG_IP_SANTRAL</name>
        </address-set>
        <address-set>
          <name>HEKIMHAN_IP_SANTRAL</name>
        </address-set>
        <address-set>
          <name>HEKIMHAN_IP_SANTRAL_2</name>
        </address-set>
        <address-set>
          <name>AKCADAG_IP_SANTRAL_2</name>
        </address-set>
        <address-set>
          <name>BTTLGZ_IP_SANTRAL</name>
        </address-set>
        <address-set>
          <name>BTTLGZ_IP_SANTRAL_2</name>
        </address-set>
        <address-set>
          <name>BTTLGZ_IP_SANTRAL_3</name>
        </address-set>
        <address-set>
          <name>DARENDE_IP_SANTRAL_3</name>
        </address-set>
        <address-set>
          <name>BTTLGZ_IP_SANTRAL_10</name>
        </address-set>
        <address-set>
          <name>BTTLGZ_IP_SANTRAL_12</name>
        </address-set>
        <address-set>
          <name>BTTLGZ_IP_SANTRAL_4</name>
        </address-set>
        <address-set>
          <name>BTTLGZ_IP_SANTRAL_5</name>
        </address-set>
        <address-set>
          <name>BTTLGZ_IP_SANTRAL_6</name>
        </address-set>
        <address-set>
          <name>BTTLGZ_IP_SANTRAL_7</name>
        </address-set>
        <address-set>
          <name>BTTLGZ_IP_SANTRAL_8</name>
        </address-set>
        <address-set>
          <name>BTTLGZ_IP_SANTRAL_9</name>
        </address-set>
        <address-set>
          <name>YESILYURT_SANTRAL_IP</name>
        </address-set>
        <address-set>
          <name>YESILYURT_SANTRAL_3</name>
        </address-set>
        <address-set>
          <name>YESILYURT_SANTRAL_4</name>
        </address-set>
        <address-set>
          <name>YYMYO_Santral</name>
        </address-set>
        <address-set>
          <name>arapgit_santral</name>
        </address-set>
      </address-group>
      <address-group>
        <name>YEMEKHANE_CIHAZLAR</name>
        <address-set>
          <name>ARAPGIR_TURNIKE</name>
        </address-set>
        <address-set>
          <name>KALE_TURNIKE</name>
        </address-set>
        <address-set>
          <name>YEMEKHANE_2</name>
        </address-set>
        <address-set>
          <name>YEMEKHANE_1</name>
        </address-set>
        <address-set>
          <name>YEMEKHANE_3</name>
        </address-set>
        <address-set>
          <name>Merkez_Yemekhane_Harcama_I</name>
        </address-set>
        <address-set>
          <name>DARENDE_TURNIKE</name>
        </address-set>
        <address-set>
          <name>DGNSHR_TURNIKE</name>
        </address-set>
        <address-set>
          <name>Battalgazi</name>
        </address-set>
        <address-set>
          <name>kartokuyucu</name>
        </address-set>
        <address-set>
          <name>Yesilyurt_Rkt_Yemekhane_Pos</name>
        </address-set>
      </address-group>
      <address-group>
        <name>Complete</name>
        <address-set>
          <name>CompleteAnatomy1</name>
        </address-set>
        <address-set>
          <name>CompleteAnatomy2</name>
        </address-set>
        <address-set>
          <name>CompleteAnatomy3</name>
        </address-set>
      </address-group>
      <address-group>
        <name>PBS_FIRMA_IP</name>
        <address-set>
          <name>**************</name>
        </address-set>
        <address-set>
          <name>***************</name>
        </address-set>
        <address-set>
          <name>************</name>
        </address-set>
        <address-set>
          <name>**************</name>
        </address-set>
      </address-group>
      <address-group>
        <name>LOCAL_NET_IP_SCOPE</name>
        <address-set>
          <name>10.0.0.0_8</name>
        </address-set>
        <address-set>
          <name>**********_16</name>
        </address-set>
        <address-set>
          <name>***********_16</name>
        </address-set>
      </address-group>
      <address-group>
        <name>FIRAT_UNI</name>
        <address-set>
          <name>***************_32</name>
        </address-set>
        <address-set>
          <name>***************_32</name>
        </address-set>
        <address-set>
          <name>**************_32</name>
        </address-set>
        <address-set>
          <name>**************_32</name>
        </address-set>
      </address-group>
      <address-group>
        <name>EXTREME_AP</name>
        <address-set>
          <name>AKCADAG_AP</name>
        </address-set>
        <address-set>
          <name>KALE_AP_1</name>
        </address-set>
        <address-set>
          <name>DARENDE_KANTIN_AP_1</name>
        </address-set>
        <address-set>
          <name>DARENDE_KIZ_YURDU_AP</name>
        </address-set>
        <address-set>
          <name>DARENDE_KIZ_YURDU_AP_2</name>
        </address-set>
        <address-set>
          <name>DARENDE_ERKEK_YURDU_AP_1</name>
        </address-set>
        <address-set>
          <name>HEKIMHAN_AP</name>
        </address-set>
        <address-set>
          <name>HEKİMHAN_AP</name>
        </address-set>
        <address-set>
          <name>ArapgirAP</name>
        </address-set>
        <address-set>
          <name>yesilyurt_Deneme</name>
        </address-set>
      </address-group>
      <address-group>
        <name>BIDB</name>
        <address-set>
          <name>deren_unal</name>
        </address-set>
        <address-set>
          <name>umran_turkmen</name>
        </address-set>
        <address-set>
          <name>halisbozkurt</name>
        </address-set>
        <address-set>
          <name>necmettin_gul</name>
        </address-set>
      </address-group>
      <address-group>
        <name>PlakaTanımaSistemi</name>
        <address-set>
          <name>YesilyurtNizamiyePTS</name>
        </address-set>
        <address-set>
          <name>Bat_ana_nzm_cks</name>
        </address-set>
        <address-set>
          <name>Bat_ana_nzm_grs</name>
        </address-set>
        <address-set>
          <name>Bat_rkt_cks</name>
        </address-set>
        <address-set>
          <name>Bat_rkt_grs</name>
        </address-set>
        <address-set>
          <name>Btl_ana_sunucu</name>
        </address-set>
      </address-group>
      <address-group>
        <name>KARTLI_GECIS</name>
        <address-set>
          <name>KG_AKCADAG_MYO</name>
        </address-set>
        <address-set>
          <name>KG_ARAPGIR_MYO</name>
        </address-set>
        <address-set>
          <name>KG_DARENDE_MYO</name>
        </address-set>
        <address-set>
          <name>KG_HEKIMHAN_MYO</name>
        </address-set>
        <address-set>
          <name>KG_KALE_MYO</name>
        </address-set>
        <address-set>
          <name>KG_MRK_BATTALGAZI_MYO</name>
        </address-set>
        <address-set>
          <name>KG_MRK_YASAM_MERKEZI</name>
        </address-set>
        <address-set>
          <name>KG_NIZAMIYE</name>
        </address-set>
        <address-set>
          <name>KG_REKTORLUK</name>
        </address-set>
        <address-set>
          <name>KG_YESILYURT_MYO</name>
        </address-set>
        <address-set>
          <name>KG_YESILYURT_REKTORLUK</name>
        </address-set>
        <address-set>
          <name>AKCADAG_IP_SANTRAL</name>
        </address-set>
        <address-set>
          <name>KG_YESILYURT_KONTEYNER</name>
        </address-set>
        <address-set>
          <name>KG_YESILYURT_SANAT_TASARIM</name>
        </address-set>
      </address-group>
      <address-group>
        <name>DSPACE_FIRMA_IPLERI</name>
        <address-set>
          <name>DSPACE_FIRMA_1</name>
        </address-set>
        <address-set>
          <name>DSPACE_FIRMA_2</name>
        </address-set>
      </address-group>
      <address-group>
        <name>BCK_SRV</name>
        <address-set>
          <name>BackupServer</name>
        </address-set>
        <address-set>
          <name>DataDomain</name>
        </address-set>
        <address-set>
          <name>DataDomain2</name>
        </address-set>
      </address-group>
      <address-group>
        <name>METIN_VPN_IP_GRP</name>
        <address-set>
          <name>METIN_VPN_IP</name>
        </address-set>
        <address-set>
          <name>METIN_VPN_IP_2</name>
        </address-set>
        <address-set>
          <name>3bi</name>
        </address-set>
      </address-group>
      <address-group>
        <name>IKCU_RPA</name>
        <address-set>
          <name>IKCU_RPA_1</name>
        </address-set>
        <address-set>
          <name>IKCU_RPA_2</name>
        </address-set>
        <address-set>
          <name>IKCU_RPA_3</name>
        </address-set>
        <address-set>
          <name>IKCU_RPA_4</name>
        </address-set>
        <address-set>
          <name>IKCU_RPA_5</name>
        </address-set>
        <address-set>
          <name>IKCU_RPA_6</name>
        </address-set>
      </address-group>
      <address-group>
        <name>MTU_RPA</name>
        <address-set>
          <name>MTU_RPA_1</name>
        </address-set>
        <address-set>
          <name>MTU_RPA_2</name>
        </address-set>
        <address-set>
          <name>MTU_RPA_3</name>
        </address-set>
      </address-group>
      <address-group>
        <name>vRPA</name>
        <address-set>
          <name>vRPA_1</name>
        </address-set>
        <address-set>
          <name>vRPA_2</name>
        </address-set>
        <address-set>
          <name>vRPA_3</name>
        </address-set>
        <address-set>
          <name>vRPA_CLS</name>
        </address-set>
      </address-group>
      <address-group>
        <name>Yemekhane_Cihazlar_Guncel</name>
        <address-set>
          <name>Akcadag_Myo_Harcama</name>
        </address-set>
        <address-set>
          <name>Akcadag_Myo_Pos</name>
        </address-set>
        <address-set>
          <name>Arapgir_Myo_Harcama</name>
        </address-set>
        <address-set>
          <name>Arapgir_Myo_Pos</name>
        </address-set>
        <address-set>
          <name>Darende_Myo_Harcama</name>
        </address-set>
        <address-set>
          <name>Darende_Myo_Pos</name>
        </address-set>
        <address-set>
          <name>Merkez_Yemekhane_Harcama_II</name>
        </address-set>
        <address-set>
          <name>Merkez_Yemekhane_Pos</name>
        </address-set>
        <address-set>
          <name>Dogansehir_Myo_Harcama</name>
        </address-set>
        <address-set>
          <name>Dogansehir_Myo_Pos</name>
        </address-set>
        <address-set>
          <name>Hekimhan_Myo_Harcama</name>
        </address-set>
        <address-set>
          <name>Hekimhan_Myo_Pos</name>
        </address-set>
        <address-set>
          <name>Merkez_Yemekhane_Harcama_I</name>
        </address-set>
        <address-set>
          <name>Kale_Myo_Harcama</name>
        </address-set>
        <address-set>
          <name>Kale_Myo_Pos</name>
        </address-set>
        <address-set>
          <name>Kıosk_I</name>
        </address-set>
        <address-set>
          <name>Kıosk_II</name>
        </address-set>
        <address-set>
          <name>Merkez_Yemekhane_Harcama_III</name>
        </address-set>
        <address-set>
          <name>Merkez_Yemekhane_Harcama_IV</name>
        </address-set>
        <address-set>
          <name>Yesilyurt_Myo_Harcama</name>
        </address-set>
        <address-set>
          <name>Yesilyurt_Myo_Pos</name>
        </address-set>
        <address-set>
          <name>kartokuyucu</name>
        </address-set>
        <address-set>
          <name>Yesilyurt_Utarit_X</name>
        </address-set>
        <address-set>
          <name>Yesilyurt_yemekhane_yeni</name>
        </address-set>
        <address-set>
          <name>Yeşilyurt_Rektörlük_Kios</name>
        </address-set>
        <address-set>
          <name>Mücahit_PC</name>
        </address-set>
        <address-set>
          <name>Yeşilyurt_Rektörlük_Yemekhane</name>
        </address-set>
        <address-set>
          <name>Yesilyurt_Rkt_Yemekhane_Pos</name>
        </address-set>
      </address-group>
      <address-group>
        <name>isa_turgut_konferans_salonu</name>
        <address-set>
          <name>ISA_DURGUT_IV</name>
        </address-set>
        <address-set>
          <name>ISA_DURGUT_V</name>
        </address-set>
        <address-set>
          <name>ISATURGUT</name>
        </address-set>
        <address-set>
          <name>ISATURGUT_2</name>
        </address-set>
        <address-set>
          <name>ISATURGUT_3</name>
        </address-set>
        <address-set>
          <name>ISATURGUT_4</name>
        </address-set>
        <address-set>
          <name>ISATURGUT_5</name>
        </address-set>
      </address-group>
      <address-group>
        <name>turnike_sistemleri</name>
        <address-set>
          <name>battalgazi_turnike_I</name>
        </address-set>
        <address-set>
          <name>battalgazi_turnike_II</name>
        </address-set>
      </address-group>
      <address-group>
        <name>ETISAN_POS</name>
        <address-set>
          <name>ETISAN_YSL_RKT_OGR_POS</name>
        </address-set>
        <address-set>
          <name>ETISAN_BTL_POS</name>
        </address-set>
        <address-set>
          <name>ETISAN_POS_BAT</name>
        </address-set>
        <address-set>
          <name>ETISAN_POS_BAT_2</name>
        </address-set>
        <address-set>
          <name>KALE_ETISAN_POS</name>
        </address-set>
        <address-set>
          <name>AKCADAG_POS</name>
        </address-set>
        <address-set>
          <name>ARAPGİR_POS</name>
        </address-set>
        <address-set>
          <name>HEKİMHAN_POS</name>
        </address-set>
        <address-set>
          <name>DARENDE_POS</name>
        </address-set>
        <address-set>
          <name>YESİLYURT_REK_POS</name>
        </address-set>
        <address-set>
          <name>YESİLYURT_REK_POS2</name>
        </address-set>
        <address-set>
          <name>YESİLYURT_POS_3</name>
        </address-set>
        <address-set>
          <name>BATTALGAZİ_POS</name>
        </address-set>
        <address-set>
          <name>KALE_POS_2</name>
        </address-set>
        <address-set>
          <name>YESİLYURT_POS_1</name>
        </address-set>
        <address-set>
          <name>ETISAn_BTL_POS</name>
        </address-set>
      </address-group>
      <address-group>
        <name>SifreSormaGroup</name>
        <description>captive portal sormadan giris</description>
        <address-set>
          <name>deren_unal</name>
        </address-set>
        <address-set>
          <name>rektor</name>
        </address-set>
        <address-set>
          <name>battalgazi_nizamiye</name>
        </address-set>
        <address-set>
          <name>pts_sunucusu</name>
        </address-set>
        <address-set>
          <name>Rektör_Makam</name>
        </address-set>
        <address-set>
          <name>YEMEKHANE_SERVER</name>
        </address-set>
        <address-set>
          <name>battalgazi_k_pos</name>
        </address-set>
        <address-set>
          <name>rektorluk_toplantı_salonu</name>
        </address-set>
        <address-set>
          <name>mehmet_kakiz</name>
        </address-set>
        <address-set>
          <name>Ruijie_Ap1</name>
        </address-set>
        <address-set>
          <name>Ruijie_Ap2</name>
        </address-set>
        <address-set>
          <name>HekimhanSW</name>
        </address-set>
        <address-set>
          <name>ahmet_selim_ozkan_makam_tv</name>
        </address-set>
        <address-set>
          <name>Santral_Dogansehir</name>
        </address-set>
      </address-group>
      <address-group>
        <name>Santraller</name>
        <address-set>
          <name>Santral_Akcadag</name>
        </address-set>
        <address-set>
          <name>Santral_B_Gazi_MYO</name>
        </address-set>
        <address-set>
          <name>Santral_Darende</name>
        </address-set>
        <address-set>
          <name>Santral_Dogansehir</name>
        </address-set>
        <address-set>
          <name>Santral_Kale</name>
        </address-set>
        <address-set>
          <name>Santral_Merkez1</name>
        </address-set>
        <address-set>
          <name>Santral_Merkez2</name>
        </address-set>
        <address-set>
          <name>Santral_Merkez3</name>
        </address-set>
        <address-set>
          <name>Santral_Ogrenci_Yasam</name>
        </address-set>
        <address-set>
          <name>Santral_Y_Yurt_MYO</name>
        </address-set>
        <address-set>
          <name>Santral_YYurt_Rektorluk</name>
        </address-set>
        <address-set>
          <name>santral</name>
        </address-set>
        <address-set>
          <name>arapgit_santral</name>
        </address-set>
        <address-set>
          <name>HEKIMHAN_IP_SANTRAL</name>
        </address-set>
        <address-set>
          <name>HEKIMHAN_IP_SANTRAL_2</name>
        </address-set>
      </address-group>
      <address-group>
        <name>SBBF_LAB_Opera</name>
        <address-set>
          <name>ssbf_lab_1</name>
        </address-set>
        <address-set>
          <name>ssbf_lab_10</name>
        </address-set>
        <address-set>
          <name>ssbf_lab_11</name>
        </address-set>
        <address-set>
          <name>ssbf_lab_12</name>
        </address-set>
        <address-set>
          <name>ssbf_lab_13</name>
        </address-set>
        <address-set>
          <name>ssbf_lab_14</name>
        </address-set>
        <address-set>
          <name>ssbf_lab_15</name>
        </address-set>
        <address-set>
          <name>ssbf_lab_16</name>
        </address-set>
        <address-set>
          <name>ssbf_lab_17</name>
        </address-set>
        <address-set>
          <name>ssbf_lab_18</name>
        </address-set>
        <address-set>
          <name>ssbf_lab_19</name>
        </address-set>
        <address-set>
          <name>ssbf_lab_2</name>
        </address-set>
        <address-set>
          <name>ssbf_lab_20</name>
        </address-set>
        <address-set>
          <name>ssbf_lab_3</name>
        </address-set>
        <address-set>
          <name>ssbf_lab_4</name>
        </address-set>
        <address-set>
          <name>ssbf_lab_5</name>
        </address-set>
        <address-set>
          <name>ssbf_lab_6</name>
        </address-set>
        <address-set>
          <name>ssbf_lab_7</name>
        </address-set>
        <address-set>
          <name>ssbf_lab_8</name>
        </address-set>
        <address-set>
          <name>ssbf_lab_9</name>
        </address-set>
        <address-set>
          <name>sbbf_lab_21</name>
        </address-set>
        <address-set>
          <name>ssbf_lab_ANAMAKINA</name>
        </address-set>
      </address-group>
      <address-group>
        <name>RPA_GRP</name>
        <address-set>
          <name>RPA_1</name>
        </address-set>
        <address-set>
          <name>RPA_2</name>
        </address-set>
        <address-set>
          <name>RPA_3</name>
        </address-set>
        <address-set>
          <name>RPA_CLS</name>
        </address-set>
        <address-set>
          <name>RPA_PLUGIN</name>
        </address-set>
      </address-group>
      <address-group>
        <name>YSLMYOAP</name>
        <address-set>
          <name>AP1</name>
        </address-set>
        <address-set>
          <name>YSLMYOAP2</name>
        </address-set>
        <address-set>
          <name>YSLMYOAP3</name>
        </address-set>
      </address-group>
    </network-obj>
    <service-obj xmlns="urn:ruijie:ntos:params:xml:ns:yang:service-obj">
      <service-set>
        <name>IMAPS</name>
        <tcp>
          <dest-port>993</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>LDAP</name>
        <tcp>
          <dest-port>389</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>DCE-RPC</name>
        <tcp>
          <dest-port>135</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
        <udp>
          <dest-port>135</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>POP3S</name>
        <tcp>
          <dest-port>995</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>SAMBA</name>
        <tcp>
          <dest-port>139</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>SMTPS</name>
        <tcp>
          <dest-port>465</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>KERBEROS</name>
        <tcp>
          <dest-port>88,464</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
        <udp>
          <dest-port>88,464</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>LDAP_UDP</name>
        <udp>
          <dest-port>389</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>SMB</name>
        <tcp>
          <dest-port>445</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>FTP_GET</name>
        <tcp>
          <dest-port>21</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>FTP_PUT</name>
        <tcp>
          <dest-port>21</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>ALL_TCP</name>
        <tcp>
          <dest-port>1-65535</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>ALL_UDP</name>
        <udp>
          <dest-port>1-65535</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>ALL_ICMP6</name>
        <protocol-id>1</protocol-id>
      </service-set>
      <service-set>
        <name>GRE</name>
        <protocol-id>47</protocol-id>
      </service-set>
      <service-set>
        <name>AH</name>
        <protocol-id>51</protocol-id>
      </service-set>
      <service-set>
        <name>ESP</name>
        <protocol-id>50</protocol-id>
      </service-set>
      <service-set>
        <name>AOL</name>
        <tcp>
          <dest-port>5190-5194</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>BGP</name>
        <tcp>
          <dest-port>179</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>FINGER</name>
        <tcp>
          <dest-port>79</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>GOPHER</name>
        <tcp>
          <dest-port>70</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>H323</name>
        <tcp>
          <dest-port>1720,1503</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
        <udp>
          <dest-port>1719</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>IKE</name>
        <udp>
          <dest-port>500,4500</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>Internet-Locator-Service</name>
        <tcp>
          <dest-port>389</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>IRC</name>
        <tcp>
          <dest-port>6660-6669</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>L2TP</name>
        <tcp>
          <dest-port>1701</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
        <udp>
          <dest-port>1701</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>NetMeeting</name>
        <tcp>
          <dest-port>1720</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>NFS</name>
        <tcp>
          <dest-port>111,2049</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
        <udp>
          <dest-port>111,2049</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>NNTP</name>
        <tcp>
          <dest-port>119</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>OSPF</name>
        <protocol-id>89</protocol-id>
      </service-set>
      <service-set>
        <name>PC-Anywhere</name>
        <tcp>
          <dest-port>5631</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
        <udp>
          <dest-port>5632</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>TIMESTAMP</name>
        <icmp>
          <type>13</type>
          <code>0</code>
        </icmp>
      </service-set>
      <service-set>
        <name>INFO_REQUEST</name>
        <icmp>
          <type>15</type>
          <code>0</code>
        </icmp>
      </service-set>
      <service-set>
        <name>INFO_ADDRESS</name>
        <icmp>
          <type>17</type>
          <code>0</code>
        </icmp>
      </service-set>
      <service-set>
        <name>ONC-RPC</name>
        <tcp>
          <dest-port>111</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
        <udp>
          <dest-port>111</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>PPTP</name>
        <tcp>
          <dest-port>1723</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>QUAKE</name>
        <udp>
          <dest-port>26000,27000,27910,27960</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>RAUDIO</name>
        <udp>
          <dest-port>7070</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>REXEC</name>
        <tcp>
          <dest-port>512</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>RIP</name>
        <udp>
          <dest-port>520</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>RLOGIN</name>
        <tcp>
          <dest-port>513</dest-port>
          <source-port>512-1023</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>RSH</name>
        <tcp>
          <dest-port>514</dest-port>
          <source-port>512-1023</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>SCCP</name>
        <tcp>
          <dest-port>2000</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>SIP</name>
        <tcp>
          <dest-port>5060</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
        <udp>
          <dest-port>5060</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>SIP-MSNmessenger</name>
        <tcp>
          <dest-port>1863</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>SYSLOG</name>
        <udp>
          <dest-port>514</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>TALK</name>
        <udp>
          <dest-port>517-518</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>TFTP</name>
        <udp>
          <dest-port>69</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>MGCP</name>
        <tcp>
          <dest-port>2428</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
        <udp>
          <dest-port>2427,2727</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>UUCP</name>
        <tcp>
          <dest-port>540</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>VDOLIVE</name>
        <tcp>
          <dest-port>7000-7010</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>WAIS</name>
        <tcp>
          <dest-port>210</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>WINFRAME</name>
        <tcp>
          <dest-port>1494,2598</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>X-WINDOWS</name>
        <tcp>
          <dest-port>6000-6063</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>PING6</name>
        <icmp>
          <type>128</type>
          <code>0</code>
        </icmp>
      </service-set>
      <service-set>
        <name>MS-SQL</name>
        <tcp>
          <dest-port>1433,1434</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>MYSQL</name>
        <tcp>
          <dest-port>3306</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>RDP</name>
        <tcp>
          <dest-port>3389</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>VNC</name>
        <tcp>
          <dest-port>5900</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>DHCP6</name>
        <udp>
          <dest-port>546,547</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>SQUID</name>
        <tcp>
          <dest-port>3128</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>SOCKS</name>
        <tcp>
          <dest-port>1080</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
        <udp>
          <dest-port>1080</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>WINS</name>
        <tcp>
          <dest-port>1512</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
        <udp>
          <dest-port>1512</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>RADIUS</name>
        <udp>
          <dest-port>1812,1813</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>RADIUS-OLD</name>
        <udp>
          <dest-port>1645,1646</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>CVSPSERVER</name>
        <tcp>
          <dest-port>2401</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
        <udp>
          <dest-port>2401</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>AFS3</name>
        <tcp>
          <dest-port>7000-7009</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
        <udp>
          <dest-port>7000-7009</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>TRACEROUTE</name>
        <udp>
          <dest-port>33434-33535</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>RTSP</name>
        <tcp>
          <dest-port>554,7070,8554</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
        <udp>
          <dest-port>554</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>MMS</name>
        <tcp>
          <dest-port>1755</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
        <udp>
          <dest-port>1024-5000</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>NONE</name>
        <tcp>
          <dest-port>0</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>webproxy</name>
        <tcp>
          <dest-port>0-65535</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>UDP_111</name>
        <udp>
          <dest-port>111</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>TCP_587</name>
        <tcp>
          <dest-port>587</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>UDP_SANTRAL_SERVICE</name>
        <tcp>
          <dest-port>30000-39999</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
        <udp>
          <dest-port>9877,9878,30000-39999,9999-20000</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>TCP_8080</name>
        <tcp>
          <dest-port>8080</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>TCP_7443</name>
        <tcp>
          <dest-port>7443</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>UDP_1935</name>
        <udp>
          <dest-port>1935</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>UDP_16384-32768</name>
        <udp>
          <dest-port>16384-32768</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>DSPACE_PORTS</name>
        <tcp>
          <dest-port>2641,8000</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
        <udp>
          <dest-port>2641</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>TCP_UDP_137</name>
        <tcp>
          <dest-port>137</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
        <udp>
          <dest-port>137</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>TCP_636</name>
        <tcp>
          <dest-port>636</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>TCP_3268-3269</name>
        <tcp>
          <dest-port>3268,3269</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>TCP_5722</name>
        <tcp>
          <dest-port>5722</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>TCP_9389</name>
        <tcp>
          <dest-port>9389</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>UDP_138</name>
        <udp>
          <dest-port>138</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>UDP_2535</name>
        <udp>
          <dest-port>2535</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>TCP_34210</name>
        <tcp>
          <dest-port>34210</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>TCP_5003</name>
        <tcp>
          <dest-port>5003</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>TCP_81</name>
        <tcp>
          <dest-port>81</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>TCP_82</name>
        <tcp>
          <dest-port>82</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>TCP_4444</name>
        <tcp>
          <dest-port>4444</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>TCP_8110</name>
        <tcp>
          <dest-port>8110</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>AhmetYesevi_Unı</name>
        <tcp>
          <dest-port>4480</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>RustdeskPorts</name>
        <tcp>
          <dest-port>21114-21119</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
        <udp>
          <dest-port>21116</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>TCP_10443</name>
        <tcp>
          <dest-port>10443</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>TCP_8081</name>
        <tcp>
          <dest-port>8081</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>UDP_443</name>
        <udp>
          <dest-port>443</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>DahuaPort</name>
        <tcp>
          <dest-port>37777,80,554,443</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
        <udp>
          <dest-port>37778</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>hoptodesk_ports</name>
        <tcp>
          <dest-port>80,443</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
        <udp>
          <dest-port>49152-65535</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>9090</name>
        <tcp>
          <dest-port>9090</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>MEZUN_PORTLAR</name>
        <tcp>
          <dest-port>4848,9090,5443,8080,8181,5432</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>12346</name>
        <description>Ziraat_Scada</description>
        <tcp>
          <dest-port>12346</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>Port_Servis</name>
        <tcp>
          <dest-port>4000</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>tcp_8090</name>
        <tcp>
          <dest-port>8090</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>tcp_7080</name>
        <tcp>
          <dest-port>7080</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-group>
        <name>Email_Access</name>
        <service-set>
          <name>dns</name>
        </service-set>
        <service-set>
          <name>imap</name>
        </service-set>
        <service-set>
          <name>IMAPS</name>
        </service-set>
        <service-set>
          <name>pop3</name>
        </service-set>
        <service-set>
          <name>POP3S</name>
        </service-set>
        <service-set>
          <name>smtp</name>
        </service-set>
        <service-set>
          <name>SMTPS</name>
        </service-set>
      </service-group>
      <service-group>
        <name>Web_Access</name>
        <service-set>
          <name>dns</name>
        </service-set>
        <service-set>
          <name>http</name>
        </service-set>
        <service-set>
          <name>https</name>
        </service-set>
      </service-group>
      <service-group>
        <name>Windows_AD</name>
        <service-set>
          <name>DCE-RPC</name>
        </service-set>
        <service-set>
          <name>dns</name>
        </service-set>
        <service-set>
          <name>KERBEROS</name>
        </service-set>
        <service-set>
          <name>LDAP</name>
        </service-set>
        <service-set>
          <name>LDAP_UDP</name>
        </service-set>
        <service-set>
          <name>SAMBA</name>
        </service-set>
        <service-set>
          <name>SMB</name>
        </service-set>
      </service-group>
      <service-group>
        <name>Exchange_Server</name>
        <service-set>
          <name>DCE-RPC</name>
        </service-set>
        <service-set>
          <name>dns</name>
        </service-set>
        <service-set>
          <name>https</name>
        </service-set>
      </service-group>
      <service-group>
        <name>AD_ALL</name>
        <service-set>
          <name>DCE-RPC</name>
        </service-set>
        <service-set>
          <name>dhcp</name>
        </service-set>
        <service-set>
          <name>dns</name>
        </service-set>
        <service-set>
          <name>KERBEROS</name>
        </service-set>
        <service-set>
          <name>LDAP</name>
        </service-set>
        <service-set>
          <name>LDAP_UDP</name>
        </service-set>
        <service-set>
          <name>ntp</name>
        </service-set>
        <service-set>
          <name>SAMBA</name>
        </service-set>
        <service-set>
          <name>SMB</name>
        </service-set>
        <service-set>
          <name>smtp</name>
        </service-set>
        <service-set>
          <name>TCP_3268-3269</name>
        </service-set>
        <service-set>
          <name>TCP_5722</name>
        </service-set>
        <service-set>
          <name>TCP_636</name>
        </service-set>
        <service-set>
          <name>TCP_9389</name>
        </service-set>
        <service-set>
          <name>TCP_UDP_137</name>
        </service-set>
        <service-set>
          <name>UDP_138</name>
        </service-set>
        <service-set>
          <name>UDP_2535</name>
        </service-set>
      </service-group>
      <service-group>
        <name>GenelIzinler</name>
        <service-set>
          <name>dns</name>
        </service-set>
        <service-set>
          <name>ftp</name>
        </service-set>
        <service-set>
          <name>http</name>
        </service-set>
        <service-set>
          <name>https</name>
        </service-set>
        <service-set>
          <name>imap</name>
        </service-set>
        <service-set>
          <name>IMAPS</name>
        </service-set>
        <service-set>
          <name>icmp</name>
        </service-set>
        <service-set>
          <name>pop3</name>
        </service-set>
        <service-set>
          <name>POP3S</name>
        </service-set>
        <service-set>
          <name>SIP</name>
        </service-set>
        <service-set>
          <name>smtp</name>
        </service-set>
        <service-set>
          <name>SMTPS</name>
        </service-set>
        <service-set>
          <name>UDP_443</name>
        </service-set>
      </service-group>
    </service-obj>
    <dns xmlns="urn:ruijie:ntos:params:xml:ns:yang:dns">
      <server>
        <address>**********0</address>
      </server>
      <server>
        <address>*******</address>
      </server>
      <proxy>
        <enabled>false</enabled>
      </proxy>
    </dns>
    <routing xmlns="urn:ruijie:ntos:params:xml:ns:yang:routing" xmlns="urn:ruijie:ntos:params:xml:ns:yang:routing">
      <static>
        <ipv4-route>
          <destination>0.0.0.0/0</destination>
          <next-hop>
            <next-hop>************</next-hop>
            <distance>1</distance>
            <descr>静态路由 1</descr>
          </next-hop>
          <next-hop>
            <next-hop>***********</next-hop>
            <distance>1</distance>
            <descr>静态路由 9</descr>
          </next-hop>
        </ipv4-route>
        <ipv4-route>
          <destination>************/24</destination>
          <next-hop>
            <next-hop>**********</next-hop>
            <distance>1</distance>
            <descr>静态路由 2</descr>
          </next-hop>
        </ipv4-route>
        <ipv4-route>
          <destination>************/24</destination>
          <next-hop>
            <next-hop>**********</next-hop>
            <distance>1</distance>
            <descr>静态路由 3</descr>
          </next-hop>
        </ipv4-route>
        <ipv4-route>
          <destination>************/24</destination>
          <next-hop>
            <next-hop>**********</next-hop>
            <distance>1</distance>
            <descr>静态路由 5</descr>
          </next-hop>
        </ipv4-route>
        <ipv4-route>
          <destination>************/24</destination>
          <next-hop>
            <next-hop>**********</next-hop>
            <distance>1</distance>
            <descr>静态路由 6</descr>
          </next-hop>
        </ipv4-route>
        <ipv4-route>
          <destination>************/23</destination>
          <next-hop>
            <next-hop>**********</next-hop>
            <distance>1</distance>
            <descr>静态路由 7</descr>
          </next-hop>
        </ipv4-route>
        <ipv4-route>
          <destination>************/24</destination>
          <next-hop>
            <next-hop>**********</next-hop>
            <distance>1</distance>
            <descr>静态路由 8</descr>
          </next-hop>
        </ipv4-route>
      </static>
    </routing>
    <security-policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>Proxy_Sunucu_to_WAN</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <dest-zone>
          <name>WAN</name>
        </dest-zone>
        <source-interface>
          <name>Ge0/1.1001</name>
        </source-interface>
        <source-network>
          <name>Proxy_Sunucusu</name>
        </source-network>
        <source-network>
          <name>proxysunucu_2</name>
        </source-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>vRPA_IN</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <source-network>
          <name>IKCU_RPA</name>
        </source-network>
        <dest-network>
          <name>vRPA_VIP</name>
        </dest-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WEB_TO_Turnike_Gecis_Sistemeleri</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>Turnike_Gecis_Sistemleri</name>
        </dest-network>
        <service>
          <name>http</name>
        </service>
        <service>
          <name>https</name>
        </service>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>UNIKYS_LDAP</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <source-network>
          <name>OMÜ_Erişim</name>
        </source-network>
        <dest-network>
          <name>Active_Directory</name>
        </dest-network>
        <service>
          <name>ldap</name>
        </service>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>aaaa</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <source-network>
          <name>METIN_VPN_IP_GRP</name>
        </source-network>
        <dest-network>
          <name>test_server</name>
        </dest-network>
        <service>
          <name>rdp</name>
        </service>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>wantoproxywan</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>Proxy_Sunucu_8080</name>
        </dest-network>
        <service>
          <name>TCP_8080</name>
        </service>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>proxy_to_ldap</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <source-interface>
          <name>Ge0/1.1001</name>
        </source-interface>
        <source-network>
          <name>Proxy_Sunucusu</name>
        </source-network>
        <source-network>
          <name>proxysunucu_2</name>
        </source-network>
        <service>
          <name>dns-u</name>
        </service>
        <service>
          <name>ldap</name>
        </service>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>test_aruba</name>
        <enabled>true</enabled>
        <description>(Copy of TargitasNAC)</description>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>MGMT</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>TargitasNAC</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>MGMT</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>TargitasNAC</name>
        </dest-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>RPA</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <source-network>
          <name>YSLYRT_DMZ</name>
        </source-network>
        <source-network>
          <name>YESILTURT_RKT_address</name>
        </source-network>
        <source-network>
          <name>YESILYURT_MYO_address</name>
        </source-network>
        <dest-network>
          <name>DMZaddress</name>
        </dest-network>
        <dest-network>
          <name>REAL_IP_address</name>
        </dest-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>CAM_ERISIM</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>DMZ</name>
        </source-zone>
        <dest-zone>
          <name>LAN</name>
        </dest-zone>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>aRPA</name>
        <enabled>true</enabled>
        <description>(Copy of RPA) (Reverse of RPA)</description>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>DMZ</name>
        </source-zone>
        <dest-zone>
          <name>LAN</name>
        </dest-zone>
        <source-network>
          <name>DMZaddress</name>
        </source-network>
        <source-network>
          <name>REAL_IP_address</name>
        </source-network>
        <source-network>
          <name>YESILTURT_RKT_address</name>
        </source-network>
        <dest-network>
          <name>YSLYRT_DMZ</name>
        </dest-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>DMZtoLAN</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>DMZ</name>
        </source-zone>
        <dest-zone>
          <name>LAN</name>
        </dest-zone>
        <source-network>
          <name>DMZaddress</name>
        </source-network>
        <source-network>
          <name>REAL_IP_address</name>
        </source-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>LANtoDMZ_TurnikeSistemleri</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>Turnike_Gecis_Sistemleri</name>
        </dest-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>FORTILOGGER</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>FortiLogger_IP</name>
        </dest-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>Gecici_SBBF_LAb_İzinler</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <source-network>
          <name>SBBF_LAB_Opera</name>
        </source-network>
        <dest-network>
          <name>SBSF_LAB_LOCAL_IP</name>
        </dest-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>DARENDE_DHCP</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <source-network>
          <name>DARENDE_TUM_NET</name>
        </source-network>
        <source-network>
          <name>HEKIMHAN_TUM_NET</name>
        </source-network>
        <dest-network>
          <name>DHCP_SERVER</name>
        </dest-network>
        <service>
          <name>icmp</name>
        </service>
        <service>
          <name>dhcp</name>
        </service>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WifiControllerAllow</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>ExtremeXIQController</name>
        </dest-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>LANtoDMZ_FULL</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <source-network>
          <name>necmettin_gul</name>
        </source-network>
        <source-network>
          <name>rektor</name>
        </source-network>
        <source-network>
          <name>REKTOR_DU_MAC</name>
        </source-network>
        <source-network>
          <name>deren_masa</name>
        </source-network>
        <source-network>
          <name>HAKAN_TOPTAS_ALL</name>
        </source-network>
        <source-network>
          <name>rektor_mac_eski</name>
        </source-network>
        <source-network>
          <name>rektor_mac_2</name>
        </source-network>
        <source-network>
          <name>rektor_mac_yeni</name>
        </source-network>
        <source-network>
          <name>REKTOR_TEL_MAC</name>
        </source-network>
        <source-network>
          <name>Ali_Yuce_ProjeLoraCihaz</name>
        </source-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>Etik</name>
        <enabled>true</enabled>
        <description>(Copy of EtikWeb)</description>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>EtikKurulu_VIP</name>
        </dest-network>
        <service>
          <name>http</name>
        </service>
        <service>
          <name>TCP_8081</name>
        </service>
        <service>
          <name>https</name>
        </service>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>MTUKYS</name>
        <enabled>true</enabled>
        <description>(Copy of EtikWeb) (Copy of Etik)</description>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>UNIKYS_Kalite_Yazılımı</name>
        </dest-network>
        <service>
          <name>http</name>
        </service>
        <service>
          <name>https</name>
        </service>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>Mezunum_LAN_to_DMZ</name>
        <enabled>true</enabled>
        <description>(Copy of EtikWeb) (Copy of Etik)</description>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>Mezun_Portal</name>
        </dest-network>
        <service>
          <name>http</name>
        </service>
        <service>
          <name>https</name>
        </service>
        <service>
          <name>ssh</name>
        </service>
        <service>
          <name>MEZUN_PORTLAR</name>
        </service>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>LANtoDMZ_AD</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <source-network>
          <name>LOCAL_NET_IP_SCOPE</name>
        </source-network>
        <dest-network>
          <name>Active_Directory</name>
        </dest-network>
        <dest-network>
          <name>Active_Directory_Backup</name>
        </dest-network>
        <service>
          <name>AD_ALL</name>
        </service>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>LANtoLAN_UTARIT_REV</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <source-network>
          <name>Yemekhane_Cihazlar_Guncel</name>
        </source-network>
        <dest-network>
          <name>YEMEKHANE_SERVER</name>
        </dest-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>LANtoDMZ_DNS</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>DNS_SERVER</name>
        </dest-network>
        <service>
          <name>dns-u</name>
        </service>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>KAtalog</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>Katalog</name>
        </dest-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>test_dmz</name>
        <enabled>true</enabled>
        <description>(Copy of TEST)</description>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <source-network>
          <name>10.0.0.0_8</name>
        </source-network>
        <dest-network>
          <name>ALL_VIP</name>
        </dest-network>
        <service>
          <name>AD_ALL</name>
        </service>
        <service>
          <name>Email Access</name>
        </service>
        <service>
          <name>Web Access</name>
        </service>
        <service>
          <name>Windows AD</name>
        </service>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>test_web</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <source-network>
          <name>10.0.0.0_8</name>
        </source-network>
        <dest-network>
          <name>SRV_WEB_ESKI_IP</name>
        </dest-network>
        <service>
          <name>AD_ALL</name>
        </service>
        <service>
          <name>Web Access</name>
        </service>
        <service>
          <name>Windows AD</name>
        </service>
        <time-range>always</time-range>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>Kutuphane</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <source-network>
          <name>10.0.0.0_8</name>
        </source-network>
        <dest-network>
          <name>KutuphaneServer</name>
        </dest-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>BIDBtoDMZ</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <source-network>
          <name>BTL_BIDB_address</name>
        </source-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>vrpaCLS</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>DMZ</name>
        </source-zone>
        <dest-zone>
          <name>WAN</name>
        </dest-zone>
        <source-network>
          <name>************</name>
        </source-network>
        <dest-network>
          <name>IKCU_RPA</name>
        </dest-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>vrpa1</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>DMZ</name>
        </source-zone>
        <dest-zone>
          <name>WAN</name>
        </dest-zone>
        <source-network>
          <name>************</name>
        </source-network>
        <dest-network>
          <name>IKCU_RPA</name>
        </dest-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>vrpa2</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>DMZ</name>
        </source-zone>
        <dest-zone>
          <name>WAN</name>
        </dest-zone>
        <source-network>
          <name>************</name>
        </source-network>
        <dest-network>
          <name>IKCU_RPA</name>
        </dest-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>vrpa3</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>DMZ</name>
        </source-zone>
        <dest-zone>
          <name>WAN</name>
        </dest-zone>
        <source-network>
          <name>************</name>
        </source-network>
        <dest-network>
          <name>IKCU_RPA</name>
        </dest-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>vrpa4</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>DMZ</name>
        </source-zone>
        <dest-zone>
          <name>WAN</name>
        </dest-zone>
        <source-network>
          <name>************</name>
        </source-network>
        <dest-network>
          <name>IKCU_RPA</name>
        </dest-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>vrpa5</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>DMZ</name>
        </source-zone>
        <dest-zone>
          <name>WAN</name>
        </dest-zone>
        <source-network>
          <name>************</name>
        </source-network>
        <dest-network>
          <name>IKCU_RPA</name>
        </dest-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>Hop</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>WAN</name>
        </dest-zone>
        <dest-network>
          <name>hoptodesk</name>
        </dest-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>hop_to_desk_ports</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>WAN</name>
        </dest-zone>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>kamusm.gov.tr</name>
        <enabled>false</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>WAN</name>
        </dest-zone>
        <dest-network>
          <name>kamusm.gov.tr</name>
        </dest-network>
        <dest-network>
          <name>tubitak.gov.tr</name>
        </dest-network>
        <dest-network>
          <name>depo.kamusm.gov.tr</name>
        </dest-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>MGMT_IZIN</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>MGMT</name>
        </dest-zone>
        <source-network>
          <name>rektor</name>
        </source-network>
        <source-network>
          <name>REKTOR_DU_MAC</name>
        </source-network>
        <source-network>
          <name>NCMTN_GL</name>
        </source-network>
        <source-network>
          <name>HAKAN_TOPTAS_ALL</name>
        </source-network>
        <source-network>
          <name>rektor_mac_eski</name>
        </source-network>
        <source-network>
          <name>rektor_mac_2</name>
        </source-network>
        <source-network>
          <name>0.pool.ntp.org</name>
        </source-network>
        <source-network>
          <name>rektor_mac_yeni</name>
        </source-network>
        <source-network>
          <name>REKTOR_TEL_MAC</name>
        </source-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>BIDBtoMGMT</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>MGMT</name>
        </dest-zone>
        <source-network>
          <name>BTL_BIDB_address</name>
        </source-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WANtoDMZ_DNS</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>DNS_UDP_53</name>
        </dest-network>
        <service>
          <name>dns-u</name>
        </service>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>NEWDNS</name>
        <enabled>false</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>DNS</name>
        </dest-network>
        <service>
          <name>dns-u</name>
        </service>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>MRTG</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>MRTG</name>
        </dest-network>
        <service>
          <name>http</name>
        </service>
        <service>
          <name>https</name>
        </service>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WEB_TO_E_BOOK</name>
        <enabled>true</enabled>
        <description>(Copy of WEB_DGRTNET_DDO_APP)</description>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>E_BOOK</name>
        </dest-network>
        <service>
          <name>http</name>
        </service>
        <service>
          <name>https</name>
        </service>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WEB_TO_EBOOK_LAN</name>
        <enabled>true</enabled>
        <description>(Copy of WEB_DGRTNET_DDO_APP) (Copy of WEB_TO_E_BOOK)</description>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>E_BOOK</name>
        </dest-network>
        <service>
          <name>http</name>
        </service>
        <service>
          <name>https</name>
        </service>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>ESP32_TO_APP_Enerji</name>
        <enabled>true</enabled>
        <description>(Copy of WEB_DGRTNET_DDO_APP) (Copy of WEB_TO_E_BOOK) (Copy of WEB_TO_EBOOK_LAN)</description>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <source-network>
          <name>Ali_Yuce_ProjeLoraCihaz</name>
        </source-network>
        <dest-network>
          <name>app_Web_Server</name>
        </dest-network>
        <service>
          <name>http</name>
        </service>
        <service>
          <name>https</name>
        </service>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WEB_DGRTNET_DDO_APP</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>dgrtnet_ddo_app</name>
        </dest-network>
        <service>
          <name>http</name>
        </service>
        <service>
          <name>https</name>
        </service>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>Proje_Pazari</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>Proje_Pazari</name>
        </dest-network>
        <service>
          <name>http</name>
        </service>
        <service>
          <name>https</name>
        </service>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>Firma_Erisim_Turnike_Gecis_Sis</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <source-network>
          <name>metek_grup_ıp</name>
        </source-network>
        <source-network>
          <name>3bi</name>
        </source-network>
        <dest-network>
          <name>Turnike_Gecis_Sistemleri</name>
        </dest-network>
        <service>
          <name>rdp</name>
        </service>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WEBtoEtik_EBYS</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>EtikKurulu_VIP</name>
        </dest-network>
        <service>
          <name>http</name>
        </service>
        <service>
          <name>TCP_8081</name>
        </service>
        <service>
          <name>https</name>
        </service>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>Mezunum_WAN2DMZ</name>
        <enabled>true</enabled>
        <description>(Copy of WEBtoEtik_EBYS)</description>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>Mezun_Portal</name>
        </dest-network>
        <service>
          <name>http</name>
        </service>
        <service>
          <name>https</name>
        </service>
        <service>
          <name>ssh</name>
        </service>
        <service>
          <name>MEZUN_PORTLAR</name>
        </service>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WANtoDMZ_ETIK_RDP</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <source-network>
          <name>ETIK_IP</name>
        </source-network>
        <dest-network>
          <name>EtikKurulu_VIP</name>
        </dest-network>
        <service>
          <name>rdp</name>
        </service>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WEB_Avrupa_Birligi_Proje_Koor</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>Avrupa_Birligi_Proje_Koordinatörlügü</name>
        </dest-network>
        <service>
          <name>http</name>
        </service>
        <service>
          <name>https</name>
        </service>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>Erisim_Avrupa_Birligi_Proje_Koor</name>
        <enabled>false</enabled>
        <description>(Copy of WEB_Avrupa_Birligi_Proje_Koor)</description>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <source-network>
          <name>Veysel_Sahin_IP</name>
        </source-network>
        <dest-network>
          <name>Avrupa_Birligi_Proje_Koordinatörlügü</name>
        </dest-network>
        <service>
          <name>rdp</name>
        </service>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>UNIKYS_Kalite_Yazılımı</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>UNIKYS_Kalite_Yazılımı</name>
        </dest-network>
        <service>
          <name>http</name>
        </service>
        <service>
          <name>https</name>
        </service>
        <service>
          <name>smtp</name>
        </service>
        <service>
          <name>smtps</name>
        </service>
        <service>
          <name>TCP_587</name>
        </service>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>UNIKYS_Kalite_Yaz_SSH</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <source-network>
          <name>OMÜ_Erişim</name>
        </source-network>
        <dest-network>
          <name>UNIKYS_Kalite_Yazılımı</name>
        </dest-network>
        <service>
          <name>ssh</name>
        </service>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WEBtoDMZ_EBYS</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>EBYS</name>
        </dest-network>
        <service>
          <name>http</name>
        </service>
        <service>
          <name>https</name>
        </service>
        <service>
          <name>smtp</name>
        </service>
        <service>
          <name>smtps</name>
        </service>
        <service>
          <name>TCP_587</name>
        </service>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WEBtoDMZ_EBYS_FIRMA</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <source-network>
          <name>**************_32</name>
        </source-network>
        <dest-network>
          <name>EBYS</name>
        </dest-network>
        <service>
          <name>rdp</name>
        </service>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>DNS_DIS_NAT</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>DMZ</name>
        </source-zone>
        <dest-zone>
          <name>WAN</name>
        </dest-zone>
        <source-network>
          <name>DNS_DIS_IP</name>
        </source-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>OSYM</name>
        <enabled>true</enabled>
        <description>(Copy of NVI)</description>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>DMZ</name>
        </source-zone>
        <dest-zone>
          <name>WAN</name>
        </dest-zone>
        <dest-network>
          <name>osym.gov.tr_I</name>
        </dest-network>
        <dest-network>
          <name>vps.osym.gov.tr</name>
        </dest-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>NVI</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>DMZ</name>
        </source-zone>
        <dest-zone>
          <name>WAN</name>
        </dest-zone>
        <dest-network>
          <name>kimlikdogrulama.nvi.gov.tr</name>
        </dest-network>
        <dest-network>
          <name>kpsv2.nvi.gov.tr</name>
        </dest-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>EBYS_US</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>DMZ</name>
        </source-zone>
        <dest-zone>
          <name>WAN</name>
        </dest-zone>
        <source-network>
          <name>EBYS_US</name>
        </source-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>IKCU_RPA_2</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>DMZ</name>
        </source-zone>
        <dest-zone>
          <name>WAN</name>
        </dest-zone>
        <source-network>
          <name>MTU_RPA</name>
        </source-network>
        <dest-network>
          <name>IKCU_RPA</name>
        </dest-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>IKCU_RPA</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <source-network>
          <name>IKCU_RPA</name>
        </source-network>
        <dest-network>
          <name>MTU_RPA</name>
        </dest-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>PlakaTanımaSistemi_Erisim</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>LAN</name>
        </dest-zone>
        <source-network>
          <name>10.0.0.0_8</name>
        </source-network>
        <dest-network>
          <name>PlakaTanımaSistemi</name>
        </dest-network>
        <service>
          <name>DahuaPort</name>
        </service>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>LANtoLAN_IP_SANTRALLER</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>LAN</name>
        </dest-zone>
        <dest-network>
          <name>IP_SANTRAL_GRP</name>
        </dest-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>LANtoLAN_IP_SANTRALLER_REV</name>
        <enabled>true</enabled>
        <description>)</description>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>LAN</name>
        </dest-zone>
        <source-network>
          <name>Santraller</name>
        </source-network>
        <dest-network>
          <name>Santraller</name>
        </dest-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>LANtoLAN_FULL</name>
        <enabled>true</enabled>
        <description>(Copy of MTN_GECICI)</description>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>LAN</name>
        </dest-zone>
        <source-network>
          <name>rektor</name>
        </source-network>
        <source-network>
          <name>REKTOR_DU_MAC</name>
        </source-network>
        <source-network>
          <name>HAKAN_TOPTAS_ALL</name>
        </source-network>
        <source-network>
          <name>0.pool.ntp.org</name>
        </source-network>
        <source-network>
          <name>rektor_mac_eski</name>
        </source-network>
        <source-network>
          <name>rektor_mac_2</name>
        </source-network>
        <source-network>
          <name>rektor_mac_yeni</name>
        </source-network>
        <source-network>
          <name>REKTOR_TEL_MAC</name>
        </source-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>LAN_toCAM</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>LAN</name>
        </dest-zone>
        <source-network>
          <name>rektor</name>
        </source-network>
        <source-network>
          <name>REKTOR_DU_MAC</name>
        </source-network>
        <source-network>
          <name>HAKAN_TOPTAS_ALL</name>
        </source-network>
        <source-network>
          <name>rektor_mac_eski</name>
        </source-network>
        <source-network>
          <name>rektor_mac_2</name>
        </source-network>
        <source-network>
          <name>rektor_mac_yeni</name>
        </source-network>
        <source-network>
          <name>REKTOR_TEL_MAC</name>
        </source-network>
        <source-network>
          <name>Yesilşyurt_Pts_Bilgisayari</name>
        </source-network>
        <dest-network>
          <name>BTL_CAM_address</name>
        </dest-network>
        <dest-network>
          <name>DARENDE_CAM</name>
        </dest-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>LANtoCAM_DARENDE</name>
        <enabled>true</enabled>
        <description>(Copy of LAN_toCAM)</description>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>LAN</name>
        </dest-zone>
        <source-network>
          <name>HAKAN_TOPTAS_ALL</name>
        </source-network>
        <source-network>
          <name>akif_ates</name>
        </source-network>
        <source-network>
          <name>Akif_Ates_Darende</name>
        </source-network>
        <dest-network>
          <name>DARENDE_CAM</name>
        </dest-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>a</name>
        <enabled>true</enabled>
        <description>(Copy of LAN_toCAM) (Reverse of LAN_toCAM)</description>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>LAN</name>
        </dest-zone>
        <source-network>
          <name>BTL_CAM_address</name>
        </source-network>
        <dest-network>
          <name>rektor</name>
        </dest-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>Whatsapp</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>WAN</name>
        </dest-zone>
        <source-network>
          <name>10.0.0.0_8</name>
        </source-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>a2</name>
        <enabled>true</enabled>
        <description>(Copy of MTN_GECICI) (Copy of LANtoLAN_FULL) (Reverse of LANtoLAN_FULL)</description>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>LAN</name>
        </dest-zone>
        <dest-network>
          <name>rektor</name>
        </dest-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>RPA_TEST</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>LAN</name>
        </dest-zone>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>MEHMET_KAKIZ</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>WAN</name>
        </dest-zone>
        <source-network>
          <name>mehmet_kakiz</name>
        </source-network>
        <service>
          <name>http</name>
        </service>
        <service>
          <name>https</name>
        </service>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>LANtoWAN_FULL</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>WAN</name>
        </dest-zone>
        <source-network>
          <name>rektor</name>
        </source-network>
        <source-network>
          <name>REKTOR_DU_MAC</name>
        </source-network>
        <source-network>
          <name>HAKAN_TOPTAS_ALL</name>
        </source-network>
        <source-network>
          <name>rektor_mac_eski</name>
        </source-network>
        <source-network>
          <name>rektor_mac_2</name>
        </source-network>
        <source-network>
          <name>0.pool.ntp.org</name>
        </source-network>
        <source-network>
          <name>rektor_mac_yeni</name>
        </source-network>
        <source-network>
          <name>REKTOR_TEL_MAC</name>
        </source-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>LANtoWAN_GENEL_IZINLER</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>WAN</name>
        </dest-zone>
        <service>
          <name>GenelIzinler</name>
        </service>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>BIDBtoWAN</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>WAN</name>
        </dest-zone>
        <source-network>
          <name>BTL_BIDB_address</name>
        </source-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>No_Capture_Portal</name>
        <enabled>true</enabled>
        <description>(Copy of LANtoWAN_GENEL_IZINLER)</description>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>WAN</name>
        </dest-zone>
        <source-network>
          <name>SifreSormaGroup</name>
        </source-network>
        <service>
          <name>TCP_587</name>
        </service>
        <service>
          <name>Email Access</name>
        </service>
        <service>
          <name>Web Access</name>
        </service>
        <service>
          <name>ntp_u</name>
        </service>
        <service>
          <name>AhmetYesevi_Unı</name>
        </service>
        <service>
          <name>icmp</name>
        </service>
        <service>
          <name>dns-u</name>
        </service>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>No_Capture_Portal_for_NTP</name>
        <enabled>true</enabled>
        <description>(Copy of LANtoWAN_GENEL_IZINLER) (Copy of No_Capture_Portal)</description>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>WAN</name>
        </dest-zone>
        <service>
          <name>ntp_u</name>
        </service>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>vultrusercontent.com</name>
        <enabled>true</enabled>
        <description>(Copy of RUSTDESK)</description>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>WAN</name>
        </dest-zone>
        <dest-network>
          <name>api.hoptodesk.com</name>
        </dest-network>
        <dest-network>
          <name>signal.hoptodesk.com</name>
        </dest-network>
        <dest-network>
          <name>signal2.hoptodesk.com</name>
        </dest-network>
        <dest-network>
          <name>turn.hoptodesk.com</name>
        </dest-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>RustdeskPorts</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>WAN</name>
        </dest-zone>
        <service>
          <name>RustdeskPorts</name>
        </service>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>RUSTDESK</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>WAN</name>
        </dest-zone>
        <dest-network>
          <name>rustdesk.com</name>
        </dest-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>RemoteServerToInternet</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>DMZ</name>
        </source-zone>
        <dest-zone>
          <name>WAN</name>
        </dest-zone>
        <source-network>
          <name>REMOTE_SERVER</name>
        </source-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>remotesrv_to_switches_dmztolan</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>DMZ</name>
        </source-zone>
        <dest-zone>
          <name>LAN</name>
        </dest-zone>
        <source-network>
          <name>REMOTE_SERVER</name>
        </source-network>
        <source-network>
          <name>NMS</name>
        </source-network>
        <dest-network>
          <name>BATTALGAZI_address</name>
        </dest-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>lan_to_dmz_remotesrvtoswitches</name>
        <enabled>true</enabled>
        <description>(Copy of remotesrv_to_switches_dmztolan)</description>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <source-network>
          <name>BATTALGAZI_address</name>
        </source-network>
        <dest-network>
          <name>NMS</name>
        </dest-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>Rustdesk_IZIN</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>WAN</name>
        </dest-zone>
        <dest-network>
          <name>Rustdesk</name>
        </dest-network>
        <dest-network>
          <name>Rustdesk2</name>
        </dest-network>
        <dest-network>
          <name>Rustdesk3</name>
        </dest-network>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>DMZtoWAN_NVI_NAT</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>DMZ</name>
        </source-zone>
        <dest-zone>
          <name>WAN</name>
        </dest-zone>
        <source-network>
          <name>PBS_server</name>
        </source-network>
        <source-network>
          <name>SRV_OBS_IP</name>
        </source-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>LAN_to_Proxy</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>DMZ</name>
        </source-zone>
        <dest-interface>
          <name>Ge0/1.1001</name>
        </dest-interface>
        <dest-network>
          <name>Proxy_Sunucusu</name>
        </dest-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>LANtoWAN_Yesevi</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>WAN</name>
        </dest-zone>
        <dest-network>
          <name>turtep.yesevi.edu.tr</name>
        </dest-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>policy_155</name>
        <enabled>true</enabled>
        <description>(Copy of DMZtoLAN) (Reverse of DMZtoLAN)</description>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>DMZaddress</name>
        </dest-network>
        <dest-network>
          <name>REAL_IP_address</name>
        </dest-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>LANtoDMZ_BIDB_IZINLERI</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <source-network>
          <name>BIDB</name>
        </source-network>
        <dest-network>
          <name>DMZaddress</name>
        </dest-network>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>LANtoDMZ_KARTLI_GECIS</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <source-network>
          <name>KARTLI_GECIS</name>
        </source-network>
        <dest-network>
          <name>SRV_PERS_TKP</name>
        </dest-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>LANtoWAN_UtaritSQL</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <source-network>
          <name>kartokuyucu</name>
        </source-network>
        <dest-network>
          <name>Utarit_Yemekhane</name>
        </dest-network>
        <service>
          <name>ftp</name>
        </service>
        <service>
          <name>ms-sql-s</name>
        </service>
        <service>
          <name>ssh</name>
        </service>
        <service>
          <name>TCP_4444</name>
        </service>
        <service>
          <name>TCP_8110</name>
        </service>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>DMZ_AKADEMIK_PERF_ERISIM</name>
        <enabled>true</enabled>
        <description>255125 barkod numaralı yazı gereği.</description>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <source-network>
          <name>betul_ozcinar</name>
        </source-network>
        <dest-network>
          <name>Akademik_Performans_Sistemi</name>
        </dest-network>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>LANtoDMZ_GENEL_IZINLER</name>
        <enabled>false</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>DMZ_SUNUCULAR</name>
        </dest-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>LANtoDMZ_BIDB_SERVER</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>deny</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>BIDB_SERVER_DMZ</name>
        </dest-network>
        <dest-network>
          <name>FIREWALL_DMZ_IP</name>
        </dest-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>DMZtoLAN_KARTLI_GECIS</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>DMZ</name>
        </source-zone>
        <dest-zone>
          <name>LAN</name>
        </dest-zone>
        <source-network>
          <name>SRV_PERS_TKP</name>
        </source-network>
        <dest-network>
          <name>KARTLI_GECIS</name>
        </dest-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>LANtoLAN_FILE_SERVER</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>LAN</name>
        </dest-zone>
        <dest-network>
          <name>FILE_SERVER</name>
        </dest-network>
        <dest-network>
          <name>FILE_SERVER_II</name>
        </dest-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>LANtoLAN_WIFI</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>LAN</name>
        </dest-zone>
        <source-network>
          <name>AP_CONTROLLER</name>
        </source-network>
        <dest-network>
          <name>EXTREME_AP</name>
        </dest-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>LANtoLAN_WIFI_REV</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>LAN</name>
        </dest-zone>
        <source-network>
          <name>EXTREME_AP</name>
        </source-network>
        <dest-network>
          <name>AP_CONTROLLER</name>
        </dest-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>LANtoLAN_UTARIT</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>DMZ</name>
        </source-zone>
        <dest-zone>
          <name>LAN</name>
        </dest-zone>
        <source-network>
          <name>YEMEKHANE_SERVER</name>
        </source-network>
        <dest-network>
          <name>Yemekhane_Cihazlar_Guncel</name>
        </dest-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>LANtoDMZ_Turnike_Sistemleri</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>DMZ</name>
        </source-zone>
        <dest-zone>
          <name>LAN</name>
        </dest-zone>
        <source-network>
          <name>SRV_TurnikeSistemleri_Lokal_IP</name>
        </source-network>
        <dest-network>
          <name>turnike_sistemleri</name>
        </dest-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>Gecici_Sbbf_Lab</name>
        <enabled>false</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>DMZ</name>
        </source-zone>
        <dest-zone>
          <name>LAN</name>
        </dest-zone>
        <source-network>
          <name>SBBF_Oracle_Opera_sunucu</name>
        </source-network>
        <dest-network>
          <name>SBBF_LAB_Opera</name>
        </dest-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>NAT_CompleteAnatomy</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>WAN</name>
        </dest-zone>
        <dest-network>
          <name>Complete_Anatomy</name>
        </dest-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>NAT_KPS</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>WAN</name>
        </dest-zone>
        <dest-network>
          <name>KPS_IP</name>
        </dest-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>NAT_OSYM</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>WAN</name>
        </dest-zone>
        <dest-network>
          <name>OSYM_IP</name>
        </dest-network>
        <service>
          <name>http</name>
        </service>
        <service>
          <name>https</name>
        </service>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WANtoDMZ_Remote_Metin</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <source-network>
          <name>METIN_VPN_IP_GRP</name>
        </source-network>
        <dest-network>
          <name>RemoteMetin</name>
        </dest-network>
        <service>
          <name>rdp</name>
        </service>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WANtoDMZ_ApiOzdegerlendirme</name>
        <enabled>false</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>ApiOzdegerlendirme_443</name>
        </dest-network>
        <dest-network>
          <name>ApiOzdegerlendirme_80</name>
        </dest-network>
        <dest-network>
          <name>ApiOzdegerlendirme_8080</name>
        </dest-network>
        <service>
          <name>http</name>
        </service>
        <service>
          <name>https</name>
        </service>
        <service>
          <name>TCP_8080</name>
        </service>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WANtoDMZ_TIP_OGR_BIRLIGI</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>TipOgrBirligi_443</name>
        </dest-network>
        <dest-network>
          <name>TipOgrBirligi_80</name>
        </dest-network>
        <service>
          <name>http</name>
        </service>
        <service>
          <name>https</name>
        </service>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WANtoDMZ_FIRMA_SBSF_LAB_ORACLE</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <source-network>
          <name>PROTEL_IP</name>
        </source-network>
        <dest-network>
          <name>SBSF_LAB_ORACLE_3389</name>
        </dest-network>
        <service>
          <name>rdp</name>
        </service>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WANtoDMZ_AKADEMIK_TESVIK</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>AkademikTesvik_443</name>
        </dest-network>
        <dest-network>
          <name>AkademikTesvik_80</name>
        </dest-network>
        <service>
          <name>http</name>
        </service>
        <service>
          <name>https</name>
        </service>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WANtoDMZ_ARIZA_TAKIP</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>ArizaTakip_443</name>
        </dest-network>
        <dest-network>
          <name>ArizaTakip_80</name>
        </dest-network>
        <service>
          <name>http</name>
        </service>
        <service>
          <name>https</name>
        </service>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WANtoDMZ_SEMINER</name>
        <enabled>false</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>SeminerSunucusu_443</name>
        </dest-network>
        <dest-network>
          <name>SeminerSunucusu_7443</name>
        </dest-network>
        <dest-network>
          <name>SeminerSunucusu_80</name>
        </dest-network>
        <dest-network>
          <name>SeminerSunucusu_UDP_16384-32768</name>
        </dest-network>
        <dest-network>
          <name>SeminerSunucusu_UDP_1935</name>
        </dest-network>
        <service>
          <name>http</name>
        </service>
        <service>
          <name>https</name>
        </service>
        <service>
          <name>TCP_7443</name>
        </service>
        <service>
          <name>UDP_16384-32768</name>
        </service>
        <service>
          <name>UDP_1935</name>
        </service>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WANtoDMZ_WEB</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>WebSunucusu_443</name>
        </dest-network>
        <dest-network>
          <name>WebSunucusu_80</name>
        </dest-network>
        <dest-network>
          <name>WebSunucusu_UDP_53</name>
        </dest-network>
        <service>
          <name>http</name>
        </service>
        <service>
          <name>https</name>
        </service>
        <service>
          <name>dns-u</name>
        </service>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WANtoDMZ_Akupunktur</name>
        <enabled>false</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>Akupunktur_443</name>
        </dest-network>
        <service>
          <name>http</name>
        </service>
        <service>
          <name>https</name>
        </service>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WANtoDMZ_DSPACE_FIRMA_ERISIM</name>
        <enabled>false</enabled>
        <description>(Copy of WANtoDMZ_DSPACE)</description>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <source-network>
          <name>DSPACE_FIRMA_IPLERI</name>
        </source-network>
        <dest-network>
          <name>Dspace</name>
        </dest-network>
        <service>
          <name>ssh</name>
        </service>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WANtoDMZ_DSPACE</name>
        <enabled>false</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>Dspace</name>
        </dest-network>
        <service>
          <name>DSPACE_PORTS</name>
        </service>
        <service>
          <name>http</name>
        </service>
        <service>
          <name>https</name>
        </service>
        <service>
          <name>TCP_587</name>
        </service>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>DSPACE_NEW_FİRMAERİSİM</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <source-network>
          <name>DSPACE_FIRMA_IPLERI</name>
        </source-network>
        <dest-network>
          <name>Dspace_New</name>
        </dest-network>
        <service>
          <name>ssh</name>
        </service>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>DSPACE_NEW</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>Dspace_New</name>
        </dest-network>
        <service>
          <name>http</name>
        </service>
        <service>
          <name>https</name>
        </service>
        <service>
          <name>DSPACE_PORTS</name>
        </service>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WEBtoDMZ_TIP_LMS</name>
        <enabled>false</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>TipLMS</name>
        </dest-network>
        <service>
          <name>http</name>
        </service>
        <service>
          <name>https</name>
        </service>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WANtoDMZ_UZEM</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>Uzem</name>
        </dest-network>
        <service>
          <name>http</name>
        </service>
        <service>
          <name>https</name>
        </service>
        <service>
          <name>icmp</name>
        </service>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WEBtoDMZ_PBS_FIRMA_ERISIMLERI</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <source-network>
          <name>PBS_FIRMA_IP</name>
        </source-network>
        <dest-network>
          <name>Pbs_HTTP</name>
        </dest-network>
        <dest-network>
          <name>Pbs_HTTPS</name>
        </dest-network>
        <dest-network>
          <name>Pbs_MYSQL</name>
        </dest-network>
        <dest-network>
          <name>Pbs_RDP</name>
        </dest-network>
        <service>
          <name>mysql</name>
        </service>
        <service>
          <name>rdp</name>
        </service>
        <service>
          <name>ssh</name>
        </service>
        <service>
          <name>http</name>
        </service>
        <service>
          <name>https</name>
        </service>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WANtoDMZ_PBS</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>Pbs_HTTP</name>
        </dest-network>
        <dest-network>
          <name>Pbs_HTTPS</name>
        </dest-network>
        <service>
          <name>http</name>
        </service>
        <service>
          <name>https</name>
        </service>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WANtoDMZ_AD</name>
        <enabled>false</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <source-network>
          <name>MTU_WAN_IPS</name>
        </source-network>
        <dest-network>
          <name>Active_Directory</name>
        </dest-network>
        <dest-network>
          <name>Active_Directory_Backup</name>
        </dest-network>
        <service>
          <name>AD_ALL</name>
        </service>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WANtoDMZ_KIMLIK_YONETIMI_SQL</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>Kimlik_Yönetim_Sistemi_SQL</name>
        </dest-network>
        <service>
          <name>smtps</name>
        </service>
        <service>
          <name>TCP_587</name>
        </service>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WEBtoDMZ_Ek_DERS</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>Ek_Ders_Modülü</name>
        </dest-network>
        <service>
          <name>http</name>
        </service>
        <service>
          <name>https</name>
        </service>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WANtoDMZ_OBS_FIRMA</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <source-network>
          <name>OBS_PROLIZ_IP</name>
        </source-network>
        <dest-network>
          <name>Obs</name>
        </dest-network>
        <service>
          <name>rdp</name>
        </service>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WANtoDMZ_ARYOM_FIRMA</name>
        <enabled>true</enabled>
        <description>(Copy of WANtoDMZ_OBS_FIRMA)</description>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <source-network>
          <name>ARYOM_IP</name>
        </source-network>
        <dest-network>
          <name>Aryom</name>
        </dest-network>
        <service>
          <name>rdp</name>
        </service>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WEBtoDMZ_OBS</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>Obs</name>
        </dest-network>
        <service>
          <name>http</name>
        </service>
        <service>
          <name>https</name>
        </service>
        <service>
          <name>smtp</name>
        </service>
        <service>
          <name>smtps</name>
        </service>
        <service>
          <name>TCP_587</name>
        </service>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WEBtoDMZ_NOMYSEM</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>Nomysem_Sürekli_EgtMrk</name>
        </dest-network>
        <service>
          <name>http</name>
        </service>
        <service>
          <name>https</name>
        </service>
        <service>
          <name>smtp</name>
        </service>
        <service>
          <name>smtps</name>
        </service>
        <service>
          <name>TCP_587</name>
        </service>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WANtoDMZ_TOPLUMESAJ</name>
        <enabled>false</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>Toplu_Mesaj_Ytt</name>
        </dest-network>
        <service>
          <name>http</name>
        </service>
        <service>
          <name>https</name>
        </service>
        <service>
          <name>TCP_34210</name>
        </service>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WANtoDMZ_TOPLUMESAJ_FIRMA_ERISIMI</name>
        <enabled>false</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <source-network>
          <name>**************_32</name>
        </source-network>
        <dest-network>
          <name>Toplu_Mesaj_Ytt</name>
        </dest-network>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WANtoDMZ_AKADEMIK_PERF_SYS</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>Akademik_Performans_Sistemi</name>
        </dest-network>
        <service>
          <name>http</name>
        </service>
        <service>
          <name>https</name>
        </service>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WANtoDMZ_AKADEMIK_PERF_SYS_FIRMA</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <source-network>
          <name>5M_Bilisim</name>
        </source-network>
        <dest-network>
          <name>Akademik_Performans_Sistemi</name>
        </dest-network>
        <service>
          <name>rdp</name>
        </service>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WANtoDMZ_WEB_SUNUCUSU_FIRMA</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <source-network>
          <name>FIRAT_UNI</name>
        </source-network>
        <dest-network>
          <name>Web_Sunucusu</name>
        </dest-network>
        <dest-network>
          <name>Web_Sitesi_AltBirimler</name>
        </dest-network>
        <dest-network>
          <name>Proje_Pazari</name>
        </dest-network>
        <service>
          <name>ssh</name>
        </service>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WEBtoDMZ_KATALOG</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>Katalog</name>
        </dest-network>
        <service>
          <name>icmp</name>
        </service>
        <service>
          <name>http</name>
        </service>
        <service>
          <name>https</name>
        </service>
        <service>
          <name>TCP_5003</name>
        </service>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WEBtoDMZ_KATALOG_FIRMA</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <source-network>
          <name>************_32</name>
        </source-network>
        <dest-network>
          <name>Katalog</name>
        </dest-network>
        <service>
          <name>rdp</name>
        </service>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WANtoDMZ_WEB_SUNUCUSU</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>Web_Sunucusu</name>
        </dest-network>
        <dest-network>
          <name>Web_Sitesi_AltBirimler</name>
        </dest-network>
        <service>
          <name>http</name>
        </service>
        <service>
          <name>https</name>
        </service>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WEBtoBAPSİS</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>BAPSİS</name>
        </dest-network>
        <service>
          <name>http</name>
        </service>
        <service>
          <name>https</name>
        </service>
        <service>
          <name>smtp</name>
        </service>
        <service>
          <name>smtps</name>
        </service>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WEBtoDMZ_EBYS_KEP</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <source-network>
          <name>*************_22</name>
        </source-network>
        <dest-network>
          <name>EBYS</name>
        </dest-network>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WEBtoDMZ_YETKIM</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>Yetkim</name>
        </dest-network>
        <service>
          <name>http</name>
        </service>
        <service>
          <name>https</name>
        </service>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WEBtoDMZ_KIMLIK_YONETIMI</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>Kimlik_Yönetimi</name>
        </dest-network>
        <service>
          <name>http</name>
        </service>
        <service>
          <name>https</name>
        </service>
        <service>
          <name>smtp</name>
        </service>
        <service>
          <name>smtps</name>
        </service>
        <service>
          <name>TCP_587</name>
        </service>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WEBtoDMZ_UZEM_DB</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>Uzem_DB</name>
        </dest-network>
        <service>
          <name>http</name>
        </service>
        <service>
          <name>https</name>
        </service>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WEBtoDMZ_UTARIT_YMK</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>Utarit_Yemekhane</name>
        </dest-network>
        <service>
          <name>http</name>
        </service>
        <service>
          <name>https</name>
        </service>
        <service>
          <name>TCP_81</name>
        </service>
        <service>
          <name>TCP_82</name>
        </service>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WEBtoDMZ_UTARIT_YMK_LOCAL</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <source-network>
          <name>10.0.0.0_8</name>
        </source-network>
        <dest-network>
          <name>Utarit_Yemekhane</name>
        </dest-network>
        <service>
          <name>ftp</name>
        </service>
        <service>
          <name>ms-sql-s</name>
        </service>
        <service>
          <name>ssh</name>
        </service>
        <service>
          <name>TCP_4444</name>
        </service>
        <service>
          <name>TCP_8110</name>
        </service>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WEBtoDMZ_UTARIT_YMK_FIRMA</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <source-network>
          <name>**************_32</name>
        </source-network>
        <source-network>
          <name>*************_32</name>
        </source-network>
        <dest-network>
          <name>Utarit_Yemekhane</name>
        </dest-network>
        <service>
          <name>rdp</name>
        </service>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WEBtoDMZ_YETENEK_SINAVI</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>Yeteneksınavı_WebServis</name>
        </dest-network>
        <service>
          <name>http</name>
        </service>
        <service>
          <name>https</name>
        </service>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WEBtoDMZ_YETENEK_SINAVI_OTOMASYON</name>
        <enabled>true</enabled>
        <description>(Copy of WEBtoDMZ_YETENEK_SINAVI)</description>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>Yeteneksınavı_Otomasyon</name>
        </dest-network>
        <service>
          <name>http</name>
        </service>
        <service>
          <name>https</name>
        </service>
        <service>
          <name>smtps</name>
        </service>
        <service>
          <name>TCP_587</name>
        </service>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WEBtoDMZ_YOS_BASVURU</name>
        <enabled>true</enabled>
        <description>(Copy of WEBtoDMZ_YETENEK_SINAVI) (Copy of WEBtoDMZ_YETENEK_SINAVI_OTOMASYON)</description>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>YOS_BASVURU</name>
        </dest-network>
        <service>
          <name>http</name>
        </service>
        <service>
          <name>https</name>
        </service>
        <service>
          <name>smtps</name>
        </service>
        <service>
          <name>TCP_587</name>
        </service>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WEBtoDMZ_PTS</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>SRV_PTS</name>
        </dest-network>
        <service>
          <name>http</name>
        </service>
        <service>
          <name>https</name>
        </service>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WEBtoDMZ_PTS_FİRMA_ERİSİM</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <source-network>
          <name>Pts_Ozgur_Zaman_Firma</name>
        </source-network>
        <dest-network>
          <name>SRV_PTS</name>
        </dest-network>
        <service>
          <name>rdp</name>
        </service>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>DMZtoWEB_BCK_BLOCK</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>deny</action>
        <source-zone>
          <name>DMZ</name>
        </source-zone>
        <dest-zone>
          <name>WAN</name>
        </dest-zone>
        <source-network>
          <name>BCK_SRV</name>
        </source-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>DMZtoWAN</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>DMZ</name>
        </source-zone>
        <dest-zone>
          <name>WAN</name>
        </dest-zone>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>LANtoDMZ_YASAK_PORTLAR</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>deny</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>DMZ_SUNUCULAR</name>
        </dest-network>
        <service>
          <name>rdp</name>
        </service>
        <service>
          <name>ssh</name>
        </service>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WANtoPROXY</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-interface>
          <name>Ge0/1.1001</name>
        </dest-interface>
        <dest-network>
          <name>Proxy_Sunucu_8080</name>
        </dest-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>DMZtoMGMT</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>DMZ</name>
        </source-zone>
        <dest-zone>
          <name>MGMT</name>
        </dest-zone>
        <source-network>
          <name>REMOTE_SERVER</name>
        </source-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>NTP_ZAMAN_SUNUCUSU</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <dest-network>
          <name>AD_LOCAL_IP</name>
        </dest-network>
        <service>
          <name>ntp_u</name>
        </service>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>YSLYRT_DMZ</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>LAN</name>
        </dest-zone>
        <source-network>
          <name>REMOTE_SERVER</name>
        </source-network>
        <source-network>
          <name>**********04</name>
        </source-network>
        <dest-network>
          <name>YSLYRT_DMZ_address</name>
        </dest-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>policy_157</name>
        <enabled>true</enabled>
        <description>(Copy of YSLYRT_DMZ) (Reverse of YSLYRT_DMZ)</description>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>LAN</name>
        </source-zone>
        <dest-zone>
          <name>LAN</name>
        </dest-zone>
        <source-network>
          <name>YSLYRT_DMZ_address</name>
        </source-network>
        <dest-network>
          <name>REMOTE_SERVER</name>
        </dest-network>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>DMZtoDMZ</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>DMZ</name>
        </source-zone>
        <dest-zone>
          <name>DMZ</name>
        </dest-zone>
        <time-range>always</time-range>
      </policy>
      <policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
        <name>WAN_TO_LAN_Ziraat</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>WAN</name>
        </source-zone>
        <dest-zone>
          <name>LAN</name>
        </dest-zone>
        <dest-network>
          <name>Ziraat_Scada</name>
        </dest-network>
        <service>
          <name>12346</name>
        </service>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
    </security-policy>
  </vrf>
  <system xmlns="urn:ruijie:ntos:params:xml:ns:yang:system">
    <cp-mask>default</cp-mask>
    <nfp>
      <autoperf>
        <enabled>true</enabled>
      </autoperf>
    </nfp>
    <network-stack>
      <bridge>
        <call-ipv4-filtering>false</call-ipv4-filtering>
        <call-ipv6-filtering>false</call-ipv6-filtering>
      </bridge>
      <icmp>
        <rate-limit-icmp>1000</rate-limit-icmp>
        <rate-mask-icmp>destination-unreachable source-quench time-exceeded parameter-problem</rate-mask-icmp>
      </icmp>
      <ipv4>
        <forwarding>true</forwarding>
        <send-redirects>true</send-redirects>
        <accept-redirects>false</accept-redirects>
        <accept-source-route>false</accept-source-route>
        <arp-announce>any</arp-announce>
        <arp-filter>false</arp-filter>
        <arp-ignore>any</arp-ignore>
        <log-invalid-addresses>false</log-invalid-addresses>
      </ipv4>
      <ipv6>
        <forwarding>true</forwarding>
        <autoconfiguration>true</autoconfiguration>
        <accept-router-advert>never</accept-router-advert>
        <accept-redirects>false</accept-redirects>
        <accept-source-route>false</accept-source-route>
        <router-solicitations>-1</router-solicitations>
        <use-temporary-addresses>never</use-temporary-addresses>
      </ipv6>
    </network-stack>
    <timezone>Asia/Shanghai</timezone>
    <scheduled-restart>
      <enabled>false</enabled>
      <hour>3</hour>
      <minute>0</minute>
      <once>false</once>
    </scheduled-restart>
    <anti-virus-file-exception xmlns="urn:ruijie:ntos:params:xml:ns:yang:anti-virus">
      <enabled>true</enabled>
    </anti-virus-file-exception>
    <auth xmlns="urn:ruijie:ntos:params:xml:ns:yang:system:auth">
      <user>
        <name>admin</name>
        <role>admin</role>
        <password>$5$zHDpSFXXrdqxgOGh$uk/0SQ.WKEWWPXkHyuUnFA6Ym9sIEtnFX4bcHViEwZ1</password>
      </user>
      <user>
        <name>securityadmin</name>
        <role>admin</role>
        <lock>true</lock>
      </user>
      <user>
        <name>useradmin</name>
        <role>admin</role>
        <lock>true</lock>
      </user>
      <user>
        <name>auditadmin</name>
        <role>admin</role>
        <lock>true</lock>
      </user>
    </auth>
    <wis-service xmlns="urn:ruijie:ntos:params:xml:ns:yang:cloud-service">
      <enabled>true</enabled>
    </wis-service>
    <macc-service xmlns="urn:ruijie:ntos:params:xml:ns:yang:cloud-service">
      <enabled>true</enabled>
    </macc-service>
    <security-cloud-service xmlns="urn:ruijie:ntos:params:xml:ns:yang:cloud-service">
      <enabled>true</enabled>
    </security-cloud-service>
    <log2cloud xmlns="urn:ruijie:ntos:params:xml:ns:yang:cloud-service">
      <upload-interval>5</upload-interval>
    </log2cloud>
    <collect xmlns="urn:ruijie:ntos:params:xml:ns:yang:system:collect">
      <alarm-threshold>
        <disk-db-threshold>90</disk-db-threshold>
      </alarm-threshold>
      <enabled>true</enabled>
      <max-records>3072</max-records>
      <record-interval>300</record-interval>
      <memory-storage-threshold>90</memory-storage-threshold>
      <statistics-enabled>false</statistics-enabled>
      <record-stats-enabled>true</record-stats-enabled>
      <flow-log-enabled>true</flow-log-enabled>
      <log-language>Chinese</log-language>
    </collect>
    <dataplane xmlns="urn:ruijie:ntos:params:xml:ns:yang:dataplane-dsa">
      <hash>
        <type>hash-default</type>
        <bind>none</bind>
      </hash>
    </dataplane>
    <flow-audit xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-audit">
      <hard-disk-quota>20</hard-disk-quota>
      <refresh-time>30</refresh-time>
    </flow-audit>
    <local-defend xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
      <enabled>true</enabled>
      <host-hardening>
        <enabled>true</enabled>
        <injection-prevention>
          <enabled>false</enabled>
          <action>block</action>
        </injection-prevention>
      </host-hardening>
      <arp-monitor>
        <enabled>false</enabled>
        <scan-threshold>200</scan-threshold>
      </arp-monitor>
      <rate-limit>
        <arp>
          <req-token>5</req-token>
          <res-token>1</res-token>
          <req-threshold>100</req-threshold>
          <res-threshold>100</res-threshold>
        </arp>
      </rate-limit>
    </local-defend>
    <memory xmlns="urn:ruijie:ntos:params:xml:ns:yang:system:mem">
      <warning-threshold>95</warning-threshold>
      <critical-threshold>95</critical-threshold>
    </memory>
    <network-monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:network-monitor">
      <enabled>false</enabled>
      <wireless-gather-type>snmp</wireless-gather-type>
    </network-monitor>
    <usr-exp-plan xmlns="urn:ruijie:ntos:params:xml:ns:yang:user-experience-plan">
      <no-prompt>false</no-prompt>
      <enabled>false</enabled>
    </usr-exp-plan>
    <web-server xmlns="urn:ruijie:ntos:params:xml:ns:yang:system:web-server">
      <enabled>true</enabled>
      <port>443</port>
      <http-enabled>true</http-enabled>
      <smart-http-enabled>true</smart-http-enabled>
    </web-server>
  </system>
</config>
