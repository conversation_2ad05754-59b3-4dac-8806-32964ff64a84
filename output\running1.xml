<config xmlns="urn:ruijie:ntos">
  <vrf>
    <name>main</name>
    <aaa xmlns="urn:ruijie:ntos:params:xml:ns:yang:aaad">
      <enabled>true</enabled>
      <domain-enabled>true</domain-enabled>
      <domain>
        <name>default</name>
        <authentication>
          <sslvpn>
            <method>default</method>
            <enabled>true</enabled>
          </sslvpn>
          <webauth>
            <method>default</method>
            <enabled>true</enabled>
          </webauth>
        </authentication>
        <enabled>true</enabled>
        <username-format>
          <without-domain/>
        </username-format>
        <auto-create-group>true</auto-create-group>
      </domain>
      <domain>
        <name>vpn</name>
        <authentication>
          <sslvpn>
            <method>vpn</method>
            <enabled>true</enabled>
          </sslvpn>
          <vpn>
            <method>vpn</method>
            <enabled>true</enabled>
          </vpn>
        </authentication>
        <accounting>
          <network>vpn</network>
        </accounting>
        <enabled>true</enabled>
        <username-format>
          <without-domain/>
        </username-format>
        <auto-create-group>true</auto-create-group>
      </domain>
      <domain>
        <name>pppoe</name>
        <authentication>
          <pppoe>
            <method>pppoe</method>
            <enabled>true</enabled>
          </pppoe>
        </authentication>
        <accounting>
          <network>pppoe</network>
        </accounting>
        <enabled>true</enabled>
        <username-format>
          <without-domain/>
        </username-format>
        <auto-create-group>true</auto-create-group>
      </domain>
      <authentication>
        <sslvpn>
          <name>default</name>
          <subs/>
          <auth-order>only-subs</auth-order>
        </sslvpn>
        <sslvpn>
          <name>vpn</name>
          <subs/>
          <auth-order>only-subs</auth-order>
        </sslvpn>
        <webauth>
          <name>default</name>
          <subs/>
          <auth-order>only-subs</auth-order>
        </webauth>
        <vpn>
          <name>vpn</name>
          <subs/>
          <auth-order>only-subs</auth-order>
        </vpn>
        <pppoe>
          <name>pppoe</name>
          <subs/>
          <auth-order>only-subs</auth-order>
        </pppoe>
      </authentication>
      <accounting>
        <update>
          <periodic>5</periodic>
          <enabled>false</enabled>
        </update>
        <network>
          <name>vpn</name>
        </network>
        <network>
          <name>pppoe</name>
        </network>
      </accounting>
    </aaa>
    <anti-virus xmlns="urn:ruijie:ntos:params:xml:ns:yang:anti-virus">
      <profile>
        <name>default-alert</name>
        <template-name>default</template-name>
        <action>alert</action>
      </profile>
    </anti-virus>
    <app-behavior-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:app-behavior-control">
      <qq-control>
        <control-mode>blacklist</control-mode>
      </qq-control>
    </app-behavior-control>
    <app-parse-mgmt xmlns="urn:ruijie:ntos:params:xml:ns:yang:app-parse-mgmt">
      <overload-protection>
        <enabled>false</enabled>
        <action>bypass</action>
      </overload-protection>
      <http>
        <decompress-length>2048</decompress-length>
      </http>
    </app-parse-mgmt>
    <appid xmlns="urn:ruijie:ntos:params:xml:ns:yang:appid">
      <mode>dynamic-identify</mode>
    </appid>
    <arp xmlns="urn:ruijie:ntos:params:xml:ns:yang:arp">
      <proxy-enabled>false</proxy-enabled>
      <trusted>
        <nud-probe>
          <enabled>false</enabled>
        </nud-probe>
      </trusted>
      <gratuitous-send>
        <enabled>false</enabled>
        <interval>30</interval>
      </gratuitous-send>
    </arp>
    <bridge xmlns="urn:ruijie:ntos:params:xml:ns:yang:bridge">
      <fdb>
        <aging>300</aging>
      </fdb>
    </bridge>
    <collab-disposal xmlns="urn:ruijie:ntos:params:xml:ns:yang:collab-disposal">
      <enabled>false</enabled>
      <identity-system>none</identity-system>
    </collab-disposal>
    <ddns xmlns="urn:ruijie:ntos:params:xml:ns:yang:ddns">
      <server>
        <server-name>oray</server-name>
      </server>
    </ddns>
    <dns xmlns="urn:ruijie:ntos:params:xml:ns:yang:dns">
      <proxy>
        <enabled>false</enabled>
      </proxy>
    </dns>
    <dns-client xmlns="urn:ruijie:ntos:params:xml:ns:yang:dns-client">
      <ip-domain-lookup>
        <enabled>true</enabled>
      </ip-domain-lookup>
    </dns-client>
    <dns-security xmlns="urn:ruijie:ntos:params:xml:ns:yang:dns-security">
      <dns-filter>
        <dns-status>false</dns-status>
      </dns-filter>
      <dns-attack-defense>
        <attack-defense>
          <protocol>true</protocol>
          <security-vul>true</security-vul>
        </attack-defense>
        <flood-defense>
          <enable>true</enable>
        </flood-defense>
      </dns-attack-defense>
      <dns-security-cloud>
        <pdns>true</pdns>
        <online-protect>true</online-protect>
      </dns-security-cloud>
    </dns-security>
    <dns-transparent-proxy xmlns="urn:ruijie:ntos:params:xml:ns:yang:dns-transparent-proxy">
      <enabled>false</enabled>
      <mode>mllb</mode>
    </dns-transparent-proxy>
    <file-filter xmlns="urn:ruijie:ntos:params:xml:ns:yang:file-filter">
      <global-config>
        <feature-identify-enabled>false</feature-identify-enabled>
      </global-config>
    </file-filter>
    <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
      <enabled>true</enabled>
      <channel>
        <name>video</name>
        <shared>false</shared>
        <config-source>manual</config-source>
        <priority>4</priority>
        <fine-grained>false</fine-grained>
        <bandwidth>
          <whole>
            <maximum-bandwidth>
              <upstream>111000</upstream>
              <downstream>111000</downstream>
            </maximum-bandwidth>
            <guaranteed-bandwidth>
              <upstream>111000</upstream>
              <downstream>111000</downstream>
            </guaranteed-bandwidth>
          </whole>
          <per-ip-or-user>per-ip</per-ip-or-user>
        </bandwidth>
      </channel>
      <policy>
        <name>test</name>
        <enabled>true</enabled>
        <config-source>manual</config-source>
        <egress-interface>
          <name>Ge0/3</name>
        </egress-interface>
        <egress-interface>
          <name>Ge0/6</name>
        </egress-interface>
        <time-range>any</time-range>
        <url-pre-defined>
          <name>Agent</name>
        </url-pre-defined>
        <url-pre-defined>
          <name>VoIP</name>
        </url-pre-defined>
        <url-pre-defined>
          <name>Instant Messaging</name>
        </url-pre-defined>
        <url-pre-defined>
          <name>E-mail</name>
        </url-pre-defined>
        <url-pre-defined>
          <name>Hacker</name>
        </url-pre-defined>
        <url-pre-defined>
          <name>Software Technology</name>
        </url-pre-defined>
        <url-pre-defined>
          <name>Software Upgrade</name>
        </url-pre-defined>
        <url-pre-defined>
          <name>Network-Communication</name>
        </url-pre-defined>
        <url-pre-defined>
          <name>Network Storage</name>
        </url-pre-defined>
        <url-pre-defined>
          <name>Hardware-Electronics</name>
        </url-pre-defined>
        <url-pre-defined>
          <name>Domain Name-IDC Service</name>
        </url-pre-defined>
        <url-pre-defined>
          <name>Unknown Script</name>
        </url-pre-defined>
        <url-pre-defined>
          <name>Digital Products</name>
        </url-pre-defined>
        <url-pre-defined>
          <name>Software Download</name>
        </url-pre-defined>
        <action>
          <limit>
            <channel>video</channel>
          </limit>
        </action>
      </policy>
    </flow-control>
    <ha xmlns="urn:ruijie:ntos:params:xml:ns:yang:ha">
      <group>
        <id>0</id>
        <description/>
        <priority>100</priority>
      </group>
      <link>
        <link>Ge0/5</link>
        <local-addr>*******</local-addr>
        <peer-addr>*******</peer-addr>
      </link>
      <mgmt-link>
        <link>Ge0/0</link>
      </mgmt-link>
      <mode>A-P</mode>
      <heart-interval>1000</heart-interval>
      <heart-alert-count>3</heart-alert-count>
      <gra-arp-count>5</gra-arp-count>
      <vmac-prefix>00:00:5e</vmac-prefix>
      <preempt>false</preempt>
      <preempt-delay>60</preempt-delay>
      <session-sync>true</session-sync>
      <neigh-sync>true</neigh-sync>
      <switch-link-time>1</switch-link-time>
      <auth-type>none</auth-type>
      <enabled>false</enabled>
      <log-level>
        <group>on</group>
        <adv>off</adv>
        <dbus>on</dbus>
        <monitor>off</monitor>
      </log-level>
    </ha>
    <ike xmlns="urn:ruijie:ntos:params:xml:ns:yang:ike">
      <proposal>
        <name>default</name>
        <prf>sha-256</prf>
        <life-seconds>86400</life-seconds>
        <encrypt-alg>des des3 aes-128 aes-192 aes-256</encrypt-alg>
        <hash-alg>md5 sha</hash-alg>
        <dh-group>group1 group2 group5</dh-group>
        <auth-mode>preshared-key</auth-mode>
      </proposal>
      <proposal>
        <name>abc</name>
        <prf>sha-256</prf>
        <life-seconds>86400</life-seconds>
        <encrypt-alg>aes-128</encrypt-alg>
        <hash-alg>sha</hash-alg>
        <dh-group>group5</dh-group>
        <auth-mode>preshared-key</auth-mode>
      </proposal>
      <key>
        <type>pre-share</type>
        <ipv4-address>
          <ipv4-address>*******</ipv4-address>
          <key>=*-#!$B4Q2BCBDO_k=</key>
        </ipv4-address>
        <ipv4-address>
          <ipv4-address>********</ipv4-address>
          <key>=*-#!$B4Q2BCBDO_k=</key>
        </ipv4-address>
      </key>
      <profile>
        <name>default</name>
      </profile>
      <profile>
        <name>temporary</name>
      </profile>
      <profile>
        <name>abc</name>
        <default-psk>=*-#!$B4Q2BCBDO_k=</default-psk>
      </profile>
      <dpd>
        <interval>30</interval>
        <retry-interval>5</retry-interval>
      </dpd>
      <nat-traversal>
        <enabled>true</enabled>
      </nat-traversal>
      <nat>
        <keepalive>20</keepalive>
      </nat>
    </ike>
    <interface xmlns="urn:ruijie:ntos:params:xml:ns:yang:interface">
      <snmp>
        <if-usage-compute-interval>30</if-usage-compute-interval>
        <if-global-notify-enable>false</if-global-notify-enable>
      </snmp>
      <physical>
        <name>Ge0/0</name>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <ipv4>
          <address>
            <ip>************/24</ip>
          </address>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>true</https>
          <ping>true</ping>
          <ssh>true</ssh>
        </access-control>
      </physical>
      <physical>
        <name>Ge0/1</name>
        <mtu>1500</mtu>
        <promiscuous>false</promiscuous>
        <description/>
        <enabled>true</enabled>
        <wanlan>wan</wanlan>
        <working-mode>route</working-mode>
        <ipv4>
          <address>
            <ip>***********/24</ip>
          </address>
          <enabled>true</enabled>
          <dhcp>
            <enabled>false</enabled>
            <timeout>60</timeout>
            <retry>30</retry>
            <select-timeout>0</select-timeout>
            <reboot>10</reboot>
            <initial-interval>10</initial-interval>
            <dhcp-lease-time>7200</dhcp-lease-time>
            <request>subnet-mask</request>
            <request>broadcast-address</request>
            <request>time-offset</request>
            <request>routers</request>
            <request>domain-name</request>
            <request>domain-search</request>
            <request>domain-name-servers</request>
            <request>host-name</request>
            <request>nis-domain</request>
            <request>nis-servers</request>
            <request>ntp-servers</request>
            <request>interface-mtu</request>
          </dhcp>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <upload-bandwidth>
          <upload-bandwidth-value>1000000</upload-bandwidth-value>
          <upload-bandwidth-unit>kbps</upload-bandwidth-unit>
        </upload-bandwidth>
        <download-bandwidth>
          <download-bandwidth-value>1000000</download-bandwidth-value>
          <download-bandwidth-unit>kbps</download-bandwidth-unit>
        </download-bandwidth>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>true</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </physical>
      <physical>
        <name>Ge0/2</name>
        <description>port3-description</description>
        <enabled>false</enabled>
        <wanlan>lan</wanlan>
        <working-mode>route</working-mode>
        <ipv4>
          <address>
            <ip>*************/24</ip>
          </address>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <address>
            <ip>2003::1/96</ip>
          </address>
          <enabled>true</enabled>
          <nd>
            <dad-detect>1</dad-detect>
            <ns-interval>1000</ns-interval>
            <suppress-ra>true</suppress-ra>
            <rs-interval>3600</rs-interval>
            <ra-interval>600</ra-interval>
            <ra-lifetime>1800</ra-lifetime>
          </nd>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>true</https>
          <ping>true</ping>
          <ssh>true</ssh>
        </access-control>
      </physical>
      <physical>
        <name>Ge0/3</name>
        <description>wan1-description</description>
        <enabled>true</enabled>
        <wanlan>wan</wanlan>
        <working-mode>route</working-mode>
        <ipv4>
          <enabled>true</enabled>
          <dhcp>
            <enabled>true</enabled>
            <timeout>60</timeout>
            <retry>30</retry>
            <select-timeout>0</select-timeout>
            <reboot>10</reboot>
            <initial-interval>10</initial-interval>
            <dhcp-lease-time>3600</dhcp-lease-time>
            <request>subnet-mask</request>
            <request>broadcast-address</request>
            <request>time-offset</request>
            <request>routers</request>
            <request>domain-name</request>
            <request>domain-search</request>
            <request>domain-name-servers</request>
            <request>host-name</request>
            <request>nis-domain</request>
            <request>nis-servers</request>
            <request>ntp-servers</request>
            <request>interface-mtu</request>
          </dhcp>
        </ipv4>
        <ipv6>
          <enabled>true</enabled>
          <nd>
            <dad-detect>1</dad-detect>
            <ns-interval>1000</ns-interval>
            <suppress-ra>true</suppress-ra>
            <rs-interval>3600</rs-interval>
            <ra-interval>600</ra-interval>
            <ra-lifetime>1800</ra-lifetime>
          </nd>
          <dhcp>
            <enabled>true</enabled>
            <dad-wait-time>1</dad-wait-time>
            <prefix-request>false</prefix-request>
            <information-request>false</information-request>
          </dhcp>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <upload-bandwidth>
          <upload-bandwidth-value>3000000</upload-bandwidth-value>
          <upload-bandwidth-unit>kbps</upload-bandwidth-unit>
        </upload-bandwidth>
        <download-bandwidth>
          <download-bandwidth-value>3000000</download-bandwidth-value>
          <download-bandwidth-unit>kbps</download-bandwidth-unit>
        </download-bandwidth>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>true</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>true</https>
          <ping>true</ping>
          <ssh>true</ssh>
        </access-control>
      </physical>
      <physical>
        <name>Ge0/4</name>
        <description>pppoe1-description</description>
        <enabled>true</enabled>
        <wanlan>lan</wanlan>
        <working-mode>route</working-mode>
        <ipv4>
          <enabled>true</enabled>
          <pppoe>
            <enabled>true</enabled>
            <connection>
              <tunnel>0</tunnel>
              <enabled>true</enabled>
              <user>ruijie</user>
              <password>=*-#!$_2g+awtqJWZWf+C6zl4RLQ==</password>
              <gateway>true</gateway>
              <timeout>5</timeout>
              <retries>3</retries>
              <ppp>
                <negotiation-timeout>3</negotiation-timeout>
                <lcp-echo-interval>10</lcp-echo-interval>
                <lcp-echo-retries>10</lcp-echo-retries>
              </ppp>
              <ppp-mtu>1492</ppp-mtu>
              <dhcp>
                <prefix-request>false</prefix-request>
                <information-request>false</information-request>
              </dhcp>
              <reverse-path>true</reverse-path>
              <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
                <enabled>false</enabled>
              </flow-control>
              <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
                <https>false</https>
                <ping>false</ping>
                <ssh>false</ssh>
              </access-control>
            </connection>
          </pppoe>
        </ipv4>
        <ipv6>
          <enabled>true</enabled>
          <nd>
            <dad-detect>1</dad-detect>
            <ns-interval>1000</ns-interval>
            <suppress-ra>true</suppress-ra>
            <rs-interval>3600</rs-interval>
            <ra-interval>600</ra-interval>
            <ra-lifetime>1800</ra-lifetime>
          </nd>
          <dhcp>
            <enabled>true</enabled>
            <dad-wait-time>1</dad-wait-time>
            <prefix-request>false</prefix-request>
            <information-request>false</information-request>
          </dhcp>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <upload-bandwidth>
          <upload-bandwidth-value>123000</upload-bandwidth-value>
          <upload-bandwidth-unit>kbps</upload-bandwidth-unit>
        </upload-bandwidth>
        <download-bandwidth>
          <download-bandwidth-value>456000</download-bandwidth-value>
          <download-bandwidth-unit>kbps</download-bandwidth-unit>
        </download-bandwidth>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>true</https>
          <ping>true</ping>
          <ssh>true</ssh>
        </access-control>
      </physical>
      <physical>
        <name>Ge0/5</name>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <ipv4>
          <address>
            <ip>*******/24</ip>
          </address>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </physical>
      <physical>
        <name>Ge0/6</name>
        <mtu>1500</mtu>
        <promiscuous>false</promiscuous>
        <description/>
        <enabled>true</enabled>
        <wanlan>wan</wanlan>
        <working-mode>bridge</working-mode>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <upload-bandwidth>
          <upload-bandwidth-value>6000000</upload-bandwidth-value>
          <upload-bandwidth-unit>kbps</upload-bandwidth-unit>
        </upload-bandwidth>
        <download-bandwidth>
          <download-bandwidth-value>6000000</download-bandwidth-value>
          <download-bandwidth-unit>kbps</download-bandwidth-unit>
        </download-bandwidth>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>true</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </physical>
      <physical>
        <name>Ge0/7</name>
        <mtu>1500</mtu>
        <promiscuous>false</promiscuous>
        <description/>
        <enabled>true</enabled>
        <wanlan>wan</wanlan>
        <working-mode>route</working-mode>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
          <nd>
            <dad-detect>1</dad-detect>
            <ns-interval>1000</ns-interval>
            <suppress-ra>false</suppress-ra>
            <rs-interval>3600</rs-interval>
            <ra-interval>600</ra-interval>
            <ra-lifetime>1800</ra-lifetime>
          </nd>
          <dhcp>
            <enabled>false</enabled>
            <dad-wait-time>1</dad-wait-time>
            <prefix-request>false</prefix-request>
            <information-request>false</information-request>
          </dhcp>
          <pppoe>
            <enabled>false</enabled>
          </pppoe>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
          <ipv6>
            <autoconfiguration>false</autoconfiguration>
            <accept-router-advert>never</accept-router-advert>
            <accept-ra-default-route>false</accept-ra-default-route>
          </ipv6>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </physical>
      <physical>
        <name>Ge0/8</name>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </physical>
      <physical>
        <name>Ge0/9</name>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </physical>
      <physical>
        <name>TenGe0/0</name>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </physical>
      <physical>
        <name>TenGe0/1</name>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </physical>
      <physical>
        <name>TenGe0/2</name>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </physical>
      <physical>
        <name>TenGe0/3</name>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </physical>
      <bridge xmlns="urn:ruijie:ntos:params:xml:ns:yang:bridge">
        <name>br0</name>
        <enabled>true</enabled>
        <ipv4>
          <enabled>true</enabled>
          <dhcp>
            <enabled>false</enabled>
            <timeout>60</timeout>
            <retry>30</retry>
            <select-timeout>0</select-timeout>
            <reboot>10</reboot>
            <initial-interval>10</initial-interval>
            <dhcp-lease-time>7200</dhcp-lease-time>
            <request>subnet-mask</request>
            <request>broadcast-address</request>
            <request>time-offset</request>
            <request>routers</request>
            <request>domain-name</request>
            <request>domain-search</request>
            <request>domain-name-servers</request>
            <request>host-name</request>
            <request>nis-domain</request>
          </dhcp>
        </ipv4>
        <ipv6>
          <enabled>true</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <link-interface>
          <slave>Ge0/6</slave>
        </link-interface>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <session-source-check>dont-check</session-source-check>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </bridge>
      <vlan xmlns="urn:ruijie:ntos:params:xml:ns:yang:vlan">
        <name>Ge0/1.200</name>
        <description/>
        <enabled>true</enabled>
        <ipv4>
          <address>
            <ip>***********/24</ip>
          </address>
          <enabled>true</enabled>
          <dhcp>
            <enabled>false</enabled>
            <timeout>60</timeout>
            <retry>30</retry>
            <select-timeout>0</select-timeout>
            <reboot>10</reboot>
            <initial-interval>10</initial-interval>
            <dhcp-lease-time>7200</dhcp-lease-time>
            <request>subnet-mask</request>
            <request>broadcast-address</request>
            <request>time-offset</request>
            <request>routers</request>
            <request>domain-name</request>
            <request>domain-search</request>
            <request>domain-name-servers</request>
            <request>host-name</request>
            <request>nis-domain</request>
            <request>nis-servers</request>
            <request>ntp-servers</request>
            <request>interface-mtu</request>
          </dhcp>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <vlan-id>200</vlan-id>
        <link-interface>Ge0/1</link-interface>
        <protocol>802.1q</protocol>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </vlan>
      <vlan xmlns="urn:ruijie:ntos:params:xml:ns:yang:vlan">
        <name>Ge0/3.100</name>
        <description/>
        <enabled>true</enabled>
        <ipv4>
          <enabled>true</enabled>
          <dhcp>
            <enabled>true</enabled>
            <timeout>60</timeout>
            <retry>30</retry>
            <select-timeout>0</select-timeout>
            <reboot>10</reboot>
            <initial-interval>10</initial-interval>
            <dhcp-lease-time>3600</dhcp-lease-time>
            <request>subnet-mask</request>
            <request>broadcast-address</request>
            <request>time-offset</request>
            <request>routers</request>
            <request>domain-name</request>
            <request>domain-search</request>
            <request>domain-name-servers</request>
            <request>host-name</request>
            <request>nis-domain</request>
            <request>nis-servers</request>
            <request>ntp-servers</request>
            <request>interface-mtu</request>
          </dhcp>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <vlan-id>100</vlan-id>
        <link-interface>Ge0/3</link-interface>
        <protocol>802.1q</protocol>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>true</https>
          <ping>true</ping>
          <ssh>true</ssh>
        </access-control>
      </vlan>
      <vlan xmlns="urn:ruijie:ntos:params:xml:ns:yang:vlan">
        <name>Ge0/1.500</name>
        <mtu>1500</mtu>
        <description/>
        <enabled>true</enabled>
        <ethernet>
          <mac-address>ee:9e:5a:dd:a5:ac</mac-address>
        </ethernet>
        <ipv4>
          <enabled>true</enabled>
          <dhcp>
            <enabled>false</enabled>
            <timeout>60</timeout>
            <retry>30</retry>
            <select-timeout>0</select-timeout>
            <reboot>10</reboot>
            <initial-interval>10</initial-interval>
            <dhcp-lease-time>7200</dhcp-lease-time>
            <request>subnet-mask</request>
            <request>broadcast-address</request>
            <request>time-offset</request>
            <request>routers</request>
            <request>domain-name</request>
            <request>domain-search</request>
            <request>domain-name-servers</request>
            <request>host-name</request>
            <request>nis-domain</request>
            <request>nis-servers</request>
            <request>ntp-servers</request>
            <request>interface-mtu</request>
          </dhcp>
          <pppoe>
            <enabled>true</enabled>
            <connection>
              <tunnel>15</tunnel>
              <enabled>true</enabled>
              <user>test</user>
              <password>=*-#!$paadZtrb58s=</password>
              <gateway>true</gateway>
              <timeout>5</timeout>
              <retries>3</retries>
              <ppp>
                <negotiation-timeout>3</negotiation-timeout>
                <lcp-echo-interval>10</lcp-echo-interval>
                <lcp-echo-retries>10</lcp-echo-retries>
              </ppp>
              <ppp-mtu>1492</ppp-mtu>
              <dhcp>
                <prefix-request>false</prefix-request>
                <information-request>false</information-request>
              </dhcp>
            </connection>
          </pppoe>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <vlan-id>500</vlan-id>
        <link-interface>Ge0/1</link-interface>
        <protocol>802.1q</protocol>
        <ha-group>0</ha-group>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>true</https>
          <ping>true</ping>
          <ssh>true</ssh>
        </access-control>
      </vlan>
      <vswitch xmlns="urn:ruijie:ntos:params:xml:ns:yang:vswitch">
        <name>vswitch0</name>
        <enabled>true</enabled>
        <wanlan>lan</wanlan>
        <ipv4>
          <enabled>true</enabled>
          <dhcp>
            <enabled>false</enabled>
            <timeout>60</timeout>
            <retry>30</retry>
            <select-timeout>0</select-timeout>
            <reboot>10</reboot>
            <initial-interval>10</initial-interval>
            <dhcp-lease-time>7200</dhcp-lease-time>
            <request>subnet-mask</request>
            <request>broadcast-address</request>
            <request>time-offset</request>
            <request>routers</request>
            <request>domain-name</request>
            <request>domain-search</request>
            <request>domain-name-servers</request>
            <request>host-name</request>
            <request>nis-domain</request>
          </dhcp>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <session-source-check>transparent-forward</session-source-check>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>true</https>
          <ping>true</ping>
          <ssh>true</ssh>
        </access-control>
      </vswitch>
      <vti xmlns="urn:ruijie:ntos:params:xml:ns:yang:vti">
        <name>st</name>
        <description/>
        <enabled>false</enabled>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>true</enabled>
        </ipv6>
        <reverse-path>true</reverse-path>
        <local>*******</local>
        <is-template>false</is-template>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </vti>
      <vti xmlns="urn:ruijie:ntos:params:xml:ns:yang:vti">
        <name>vti44</name>
        <description>by tunnel wizard abc</description>
        <enabled>false</enabled>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>true</enabled>
        </ipv6>
        <reverse-path>true</reverse-path>
        <local>*******</local>
        <is-template>true</is-template>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>true</ping>
          <ssh>false</ssh>
        </access-control>
      </vti>
    </interface>
    <ips-config xmlns="urn:ruijie:ntos:params:xml:ns:yang:intrusion-prevention">
      <profile>
        <name>default-use-signature-action</name>
        <template-name>default</template-name>
        <action>use-signature-action</action>
      </profile>
    </ips-config>
    <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
      <ipv4>
        <enabled>false</enabled>
        <no-match-action-drop>false</no-match-action-drop>
      </ipv4>
      <ipv6>
        <enabled>false</enabled>
        <no-match-action-drop>false</no-match-action-drop>
      </ipv6>
    </ip-mac-bind>
    <track xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-track">
      <enabled>false</enabled>
    </track>
    <ipsec xmlns="urn:ruijie:ntos:params:xml:ns:yang:ipsec">
      <profile>
        <name>abc</name>
        <enabled>true</enabled>
        <description>by tunnel wizard abc</description>
        <create-time>1753847297</create-time>
        <version>ikev1</version>
        <exchange-mode>auto</exchange-mode>
        <autoup>true</autoup>
        <local>
          <interface>
            <interface>Ge0/2</interface>
          </interface>
        </local>
        <proxyid>abc</proxyid>
        <ike-profile>abc</ike-profile>
        <ike-proposal>abc</ike-proposal>
        <ipsec-proposal>abc</ipsec-proposal>
        <life-seconds>3600</life-seconds>
        <reverse-route>
          <enabled>false</enabled>
          <distance>5</distance>
        </reverse-route>
        <fragmentation-mtu>1400</fragmentation-mtu>
        <peer-address>
          <peer-address>********</peer-address>
        </peer-address>
        <type>static</type>
        <local-identity>
          <ipv4-address/>
        </local-identity>
        <check-id>false</check-id>
        <dpd>
          <interval>30</interval>
          <retry-interval>5</retry-interval>
          <type>periodic</type>
        </dpd>
        <tunnel-interface>vti44</tunnel-interface>
        <is-template>true</is-template>
      </profile>
      <proposal>
        <name>abc</name>
        <protocol>esp</protocol>
        <encap-mode>tunnel</encap-mode>
        <esn>true</esn>
        <esp-encrypt-alg>aes-128</esp-encrypt-alg>
        <esp-auth-alg>sha</esp-auth-alg>
      </proposal>
      <proxyid>
        <proxyid-name>abc</proxyid-name>
        <ip>
          <local>********</local>
          <remote>********</remote>
        </ip>
      </proxyid>
      <anti-replay>
        <check>true</check>
        <window-size>64</window-size>
      </anti-replay>
      <df-bit>clear</df-bit>
      <prefrag>true</prefrag>
      <inbound-sp>
        <check>true</check>
      </inbound-sp>
      <spd-hash-bits>
        <src-bits>16</src-bits>
        <dst-bits>16</dst-bits>
      </spd-hash-bits>
      <hardware-crypto-offload>true</hardware-crypto-offload>
    </ipsec>
    <isp xmlns="urn:ruijie:ntos:params:xml:ns:yang:isp">
      <distance>10</distance>
    </isp>
    <lldp xmlns="urn:ruijie:ntos:params:xml:ns:yang:lldp">
      <enabled>false</enabled>
      <hello-timer>30</hello-timer>
      <tx-hold>4</tx-hold>
      <system-name>Z5100</system-name>
      <interface>
        <name>Ge0/0</name>
        <enabled>true</enabled>
      </interface>
      <interface>
        <name>Ge0/1</name>
        <enabled>true</enabled>
      </interface>
      <interface>
        <name>Ge0/2</name>
        <enabled>true</enabled>
      </interface>
      <interface>
        <name>Ge0/3</name>
        <enabled>true</enabled>
      </interface>
      <interface>
        <name>Ge0/4</name>
        <enabled>true</enabled>
      </interface>
      <interface>
        <name>Ge0/5</name>
        <enabled>true</enabled>
      </interface>
      <interface>
        <name>Ge0/6</name>
        <enabled>true</enabled>
      </interface>
      <interface>
        <name>Ge0/7</name>
        <enabled>true</enabled>
      </interface>
      <interface>
        <name>Ge0/8</name>
        <enabled>true</enabled>
      </interface>
      <interface>
        <name>Ge0/9</name>
        <enabled>true</enabled>
      </interface>
      <interface>
        <name>TenGe0/0</name>
        <enabled>true</enabled>
      </interface>
      <interface>
        <name>TenGe0/1</name>
        <enabled>true</enabled>
      </interface>
      <interface>
        <name>TenGe0/2</name>
        <enabled>true</enabled>
      </interface>
      <interface>
        <name>TenGe0/3</name>
        <enabled>true</enabled>
      </interface>
    </lldp>
    <local-defend xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
      <policy>
        <name>deny_all</name>
        <enabled>true</enabled>
        <action>deny</action>
        <limit>false</limit>
        <pps>600</pps>
        <description/>
      </policy>
      <policy>
        <name>limit_local</name>
        <enabled>true</enabled>
        <action>permit</action>
        <limit>true</limit>
        <pps>1500</pps>
        <description/>
      </policy>
    </local-defend>
    <misn xmlns="urn:ruijie:ntos:params:xml:ns:yang:misn">
      <enabled>true</enabled>
    </misn>
    <mllb xmlns="urn:ruijie:ntos:params:xml:ns:yang:mllb">
      <arithmetic>
        <src-ip-hash/>
      </arithmetic>
      <advanced-options>
        <refresh-session>false</refresh-session>
        <cache-timeout>300</cache-timeout>
        <cache-once>256</cache-once>
        <cache-disable>false</cache-disable>
        <alarm-threshold>90</alarm-threshold>
      </advanced-options>
      <all-if-switch>false</all-if-switch>
    </mllb>
    <n2n-config xmlns="urn:ruijie:ntos:params:xml:ns:yang:n2n">
      <crypto>CC20</crypto>
      <vpn-port-v4>0</vpn-port-v4>
      <vpn-port-v6>0</vpn-port-v6>
      <hang-side>false</hang-side>
      <flow-control>
        <enabled>true</enabled>
        <rate>100</rate>
        <rate-unit>kbps</rate-unit>
      </flow-control>
      <detect-policy>
        <enabled>true</enabled>
        <interval>5000</interval>
        <timeout>3000</timeout>
      </detect-policy>
    </n2n-config>
    <port-mapping xmlns="urn:ruijie:ntos:params:xml:ns:yang:nat">
      <enabled>true</enabled>
    </port-mapping>
    <nat xmlns="urn:ruijie:ntos:params:xml:ns:yang:nat">
      <enabled>true</enabled>
      <pool>
        <name>aaa</name>
        <desc>dd</desc>
        <address>
          <value>************-**************</value>
        </address>
      </pool>
      <rule>
        <name>test01</name>
        <rule_en>true</rule_en>
        <desc>test</desc>
        <static-dnat44>
          <match>
            <dest-network>
              <name>***********-*************</name>
            </dest-network>
            <time-range>
              <value>any</value>
            </time-range>
            <service>
              <name>nfs</name>
            </service>
            <service>
              <name>server01</name>
            </service>
          </match>
          <translate-to>
            <ipv4-address>*************</ipv4-address>
            <port>80</port>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>test02</name>
        <rule_en>true</rule_en>
        <desc>test</desc>
        <static-snat44>
          <match>
            <source-network>
              <name>*************</name>
            </source-network>
            <time-range>
              <value>any</value>
            </time-range>
            <service>
              <name>ssh</name>
            </service>
          </match>
          <translate-to>
            <output-address/>
            <no-pat>false</no-pat>
            <try-no-pat>true</try-no-pat>
          </translate-to>
        </static-snat44>
      </rule>
      <rule>
        <name>test03</name>
        <rule_en>true</rule_en>
        <desc>转换源端口</desc>
        <static-snat44>
          <match>
            <source-network>
              <name>***********01</name>
            </source-network>
            <time-range>
              <value>any</value>
            </time-range>
            <service>
              <name>ping</name>
            </service>
          </match>
          <translate-to>
            <output-address/>
            <no-pat>false</no-pat>
            <try-no-pat>false</try-no-pat>
          </translate-to>
        </static-snat44>
      </rule>
      <rule>
        <name>test04</name>
        <rule_en>true</rule_en>
        <desc>地址池</desc>
        <dynamic-snat44>
          <match>
            <source-network>
              <name>*************</name>
            </source-network>
            <time-range>
              <value>any</value>
            </time-range>
            <service>
              <name>telnet</name>
            </service>
          </match>
          <translate-to>
            <pool-name>aaa</pool-name>
          </translate-to>
        </dynamic-snat44>
      </rule>
      <rule>
        <name>test05</name>
        <rule_en>true</rule_en>
        <desc>指定IP01</desc>
        <static-snat44>
          <match>
            <source-network>
              <name>*************</name>
            </source-network>
            <time-range>
              <value>any</value>
            </time-range>
            <service>
              <name>smtp</name>
            </service>
          </match>
          <translate-to>
            <ipv4-address>*************5</ipv4-address>
            <no-pat>false</no-pat>
            <try-no-pat>true</try-no-pat>
          </translate-to>
        </static-snat44>
      </rule>
      <rule>
        <name>test06</name>
        <rule_en>true</rule_en>
        <desc>指定IP02</desc>
        <static-snat44>
          <match>
            <source-network>
              <name>*************</name>
            </source-network>
            <time-range>
              <value>any</value>
            </time-range>
            <service>
              <name>http</name>
            </service>
          </match>
          <translate-to>
            <ipv4-address>*************5</ipv4-address>
            <no-pat>false</no-pat>
            <try-no-pat>false</try-no-pat>
          </translate-to>
        </static-snat44>
      </rule>
      <rule>
        <name>test07</name>
        <rule_en>true</rule_en>
        <desc>使用接口地址</desc>
        <static-dnat44>
          <match>
            <dest-if>Ge0/2</dest-if>
            <time-range>
              <value>any</value>
            </time-range>
            <service>
              <name>https</name>
            </service>
          </match>
          <translate-to>
            <ipv4-address>**************</ipv4-address>
            <port>8080</port>
          </translate-to>
        </static-dnat44>
      </rule>
      <alg>ftp sip-tcp sip-udp tftp dns-udp</alg>
      <sip-port-check>
        <enabled>true</enabled>
      </sip-port-check>
    </nat>
    <netconf-server xmlns="urn:ruijie:ntos:params:xml:ns:yang:netconf-server">
      <enabled>false</enabled>
      <idle-timeout>3600</idle-timeout>
    </netconf-server>
    <network-measure xmlns="urn:ruijie:ntos:params:xml:ns:yang:network-measure">
      <enabled>true</enabled>
      <message-send-enabled>false</message-send-enabled>
      <service>
        <dhcp>
          <enabled>true</enabled>
        </dhcp>
        <dns>
          <enabled>true</enabled>
          <monitor-threshold>1000</monitor-threshold>
        </dns>
        <nat>
          <enabled>true</enabled>
        </nat>
        <ipsec>
          <enabled>true</enabled>
        </ipsec>
        <sslvpn>
          <enabled>true</enabled>
        </sslvpn>
      </service>
      <warning-config>
        <link>
          <high-load>
            <enabled>true</enabled>
            <duration>120</duration>
            <threshold>95</threshold>
          </high-load>
          <suspect-disconnected>
            <enabled>true</enabled>
            <duration>300</duration>
            <threshold>10000</threshold>
          </suspect-disconnected>
          <change-to-down>
            <enabled>true</enabled>
          </change-to-down>
        </link>
        <app>
          <change-to-poor>
            <enabled>true</enabled>
          </change-to-poor>
        </app>
        <user>
          <wan-detect-failed>
            <enabled>true</enabled>
          </wan-detect-failed>
          <lan-detect-failed>
            <enabled>true</enabled>
          </lan-detect-failed>
          <change-to-poor>
            <enabled>true</enabled>
          </change-to-poor>
        </user>
        <dhcp-server>
          <ip-conflict>
            <enabled>true</enabled>
          </ip-conflict>
          <high-load>
            <enabled>true</enabled>
            <threshold>95</threshold>
          </high-load>
        </dhcp-server>
        <dns>
          <unuseable>
            <enabled>true</enabled>
          </unuseable>
        </dns>
        <nat>
          <hit-fail>
            <enabled>false</enabled>
          </hit-fail>
          <hit-miss>
            <enabled>false</enabled>
          </hit-miss>
        </nat>
        <ipsec>
          <disconnected>
            <enabled>true</enabled>
            <duration>120</duration>
          </disconnected>
        </ipsec>
        <sslvpn>
          <lost>
            <enabled>true</enabled>
            <threshold>10</threshold>
          </lost>
          <license>
            <enabled>true</enabled>
            <threshold>20</threshold>
          </license>
        </sslvpn>
      </warning-config>
      <flow-limit>
        <enabled>true</enabled>
        <up>80</up>
        <down>80</down>
      </flow-limit>
    </network-measure>
    <network-obj xmlns="urn:ruijie:ntos:params:xml:ns:yang:network-obj">
      <address-set>
        <name>***********-*************</name>
        <ip-set>
          <ip-address>***********-*************</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>*************</name>
        <ip-set>
          <ip-address>*************</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>***********01</name>
        <ip-set>
          <ip-address>***********01</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>*************</name>
        <ip-set>
          <ip-address>*************</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>*************</name>
        <ip-set>
          <ip-address>*************</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>*************</name>
        <ip-set>
          <ip-address>*************</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>abc_local</name>
        <description>by tunnel wizard abc</description>
        <ip-set>
          <ip-address>********</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>abc_remote</name>
        <description>by tunnel wizard abc</description>
        <ip-set>
          <ip-address>********</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>PortScan_dest_addr_obj_************</name>
        <ip-set>
          <ip-address>************</ip-address>
        </ip-set>
      </address-set>
    </network-obj>
    <nfp xmlns="urn:ruijie:ntos:params:xml:ns:yang:nfp">
      <session>
        <state-inspection>
          <tcp>true</tcp>
          <tcp-mode>standard</tcp-mode>
          <icmp>true</icmp>
        </state-inspection>
      </session>
      <tunnel-inspection>
        <bridge>
          <VLAN>true</VLAN>
          <PPPoE>false</PPPoE>
          <GRE>false</GRE>
          <L2TPv2>false</L2TPv2>
        </bridge>
      </tunnel-inspection>
    </nfp>
    <wba-portal xmlns="urn:ruijie:ntos:params:xml:ns:yang:portal">
      <enabled>false</enabled>
      <port>8081</port>
      <customized-page>
        <modify-password-enabled>false</modify-password-enabled>
      </customized-page>
      <ssl-enabled>false</ssl-enabled>
      <redirection-mode>previous-page</redirection-mode>
    </wba-portal>
    <portal-customized-template xmlns="urn:ruijie:ntos:params:xml:ns:yang:portal">
      <customized-template>
        <name>def-template</name>
        <web-page-customization>
          <logo-config>
            <display-enabled>false</display-enabled>
          </logo-config>
          <background-config>
            <default-background-enabled>true</default-background-enabled>
          </background-config>
        </web-page-customization>
      </customized-template>
    </portal-customized-template>
    <portal-languages xmlns="urn:ruijie:ntos:params:xml:ns:yang:portal">
      <language>
        <name>zh_CN</name>
        <display-name>简体中文</display-name>
        <package-name>zh_CN.json</package-name>
      </language>
      <language>
        <name>en_US</name>
        <display-name>English</display-name>
        <package-name>en_US.json</package-name>
      </language>
      <language>
        <name>tr_TR</name>
        <display-name>Türkçe</display-name>
        <package-name>tr_TR.json</package-name>
      </language>
    </portal-languages>
    <pppoe xmlns="urn:ruijie:ntos:params:xml:ns:yang:pppoe">
      <multi-dial>
        <enabled>false</enabled>
      </multi-dial>
    </pppoe>
    <pppoe-server xmlns="urn:ruijie:ntos:params:xml:ns:yang:pppoe-server">
      <enabled>false</enabled>
    </pppoe-server>
    <replacement-messages xmlns="urn:ruijie:ntos:params:xml:ns:yang:replacement-messages">
      <management>
        <rm-enable-status>true</rm-enable-status>
        <cache-enable-status>true</cache-enable-status>
        <message>
          <message-id>1</message-id>
          <message-type>usually</message-type>
          <information>
            <alarm-title>网页访问被阻断</alarm-title>
            <alarm-description>根据网络控制策略，您没有权限访问该网页。如需访问，请联系网络管理员。</alarm-description>
          </information>
        </message>
        <message>
          <message-id>2</message-id>
          <message-type>usually</message-type>
          <information>
            <alarm-title>防病毒阻断</alarm-title>
            <alarm-description>您请求的页面含有病毒，已被自动阻断。请联系网络管理员协助解决。</alarm-description>
          </information>
        </message>
        <message>
          <message-id>3</message-id>
          <message-type>usually</message-type>
          <information>
            <alarm-title>您已禁止访问网络</alarm-title>
            <alarm-description>您的设备因存在安全风险或其他管理原因，已经被禁止访问网络，请自查是否存在中毒或其他安全风险，如有疑问请联系管理人员。</alarm-description>
          </information>
        </message>
      </management>
    </replacement-messages>
    <reputation-center xmlns="urn:ruijie:ntos:params:xml:ns:yang:reputation-center">
      <enabled>false</enabled>
    </reputation-center>
    <routing xmlns="urn:ruijie:ntos:params:xml:ns:yang:routing">
      <static>
        <ipv4-route>
          <destination>0.0.0.0/0</destination>
          <next-hop>
            <next-hop>************%Ge0/0</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>************%Ge0/0</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
        </ipv4-route>
        <ipv4-route>
          <destination>**********/24</destination>
          <next-hop>
            <next-hop>*************%Ge0/1</next-hop>
            <distance>10</distance>
            <enable>true</enable>
          </next-hop>
        </ipv4-route>
        <ipv4-route>
          <destination>*************/24</destination>
          <next-hop>
            <next-hop>blackhole</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
        </ipv4-route>
        <ipv4-route>
          <destination>**********/16</destination>
          <next-hop>
            <next-hop>************00</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
        </ipv4-route>
        <ipv4-route>
          <destination>********/24</destination>
          <next-hop>
            <next-hop>*******</next-hop>
            <distance>5</distance>
            <enable>true</enable>
            <descr>vvvvvv</descr>
          </next-hop>
        </ipv4-route>
        <ipv4-route>
          <destination>********/32</destination>
          <next-hop>
            <next-hop>vti44</next-hop>
            <distance>5</distance>
            <enable>true</enable>
            <descr>by tunnel wizard abc</descr>
          </next-hop>
          <next-hop>
            <next-hop>blackhole</next-hop>
            <distance>254</distance>
            <enable>true</enable>
            <descr>by tunnel wizard abc</descr>
          </next-hop>
        </ipv4-route>
      </static>
    </routing>
    <security-defend xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-defend">
      <basic-protocol-control-enabled>false</basic-protocol-control-enabled>
      <enabled>false</enabled>
    </security-defend>
    <security-policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
      <policy>
        <name>test_PortScan_policy_************_permit_20250801134906</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <dest-network>
          <name>PortScan_dest_addr_obj_************</name>
        </dest-network>
        <service>
          <name>service_8086_TCP</name>
        </service>
        <service>
          <name>service_21_TCP</name>
        </service>
        <service>
          <name>service_22_TCP</name>
        </service>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>auto</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>IPsec_abc_in</name>
        <enabled>true</enabled>
        <description>by tunnel wizard abc</description>
        <group-name>def-group</group-name>
        <source-zone>
          <name>abc</name>
        </source-zone>
        <source-network>
          <name>abc_remote</name>
        </source-network>
        <dest-network>
          <name>abc_local</name>
        </dest-network>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>auto</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>IPsec_abc_out</name>
        <enabled>true</enabled>
        <description>by tunnel wizard abc</description>
        <group-name>def-group</group-name>
        <dest-zone>
          <name>abc</name>
        </dest-zone>
        <source-network>
          <name>abc_local</name>
        </source-network>
        <dest-network>
          <name>abc_remote</name>
        </dest-network>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>auto</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>a</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <time-range>any</time-range>
        <action>deny</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>nat_test01</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <service>
          <name>nat_test01</name>
        </service>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>auto</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>ptest01</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-zone>
          <name>trust</name>
        </source-zone>
        <service>
          <name>server01</name>
        </service>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy>
        <name>nat_test07</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <service>
          <name>nat_test07</name>
        </service>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>auto</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>test006</name>
        <enabled>true</enabled>
        <description>接口</description>
        <group-name>def-group</group-name>
        <source-interface>
          <name>Ge0/2</name>
        </source-interface>
        <source-interface>
          <name>Ge0/3</name>
        </source-interface>
        <dest-interface>
          <name>Ge0/6</name>
        </dest-interface>
        <source-network>
          <name>*************</name>
        </source-network>
        <dest-network>
          <name>*************</name>
        </dest-network>
        <service>
          <name>dns-u</name>
        </service>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
    </security-policy>
    <security-zone xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-zone">
      <zone>
        <name>trust</name>
        <description>Trust Zone.</description>
        <priority>85</priority>
        <interface>
          <name>Ge0/1</name>
        </interface>
        <interface>
          <name>Ge0/6</name>
        </interface>
      </zone>
      <zone>
        <name>untrust</name>
        <description>Untrust Zone.</description>
        <priority>5</priority>
        <interface>
          <name>Ge0/7</name>
        </interface>
      </zone>
      <zone>
        <name>DMZ</name>
        <description>Demilitarized Zone.</description>
        <priority>50</priority>
      </zone>
      <zone>
        <name>aaa</name>
        <description>test</description>
        <interface>
          <name>Ge0/3</name>
        </interface>
        <interface>
          <name>Ge0/4</name>
        </interface>
      </zone>
      <zone>
        <name>abc</name>
        <description>by tunnel wizard abc</description>
        <interface>
          <name>vti44</name>
        </interface>
      </zone>
    </security-zone>
    <service-obj xmlns="urn:ruijie:ntos:params:xml:ns:yang:service-obj">
      <service-set>
        <name>nat_test01</name>
        <description>NAT专属，谨慎修改。</description>
        <tcp>
          <source-port>0-65535</source-port>
          <dest-port>80</dest-port>
        </tcp>
        <udp>
          <source-port>0-65535</source-port>
          <dest-port>80</dest-port>
        </udp>
      </service-set>
      <service-set>
        <name>nat_test07</name>
        <description>NAT专属，谨慎修改。</description>
        <tcp>
          <source-port>0-65535</source-port>
          <dest-port>8080</dest-port>
        </tcp>
        <udp>
          <source-port>0-65535</source-port>
          <dest-port>8080</dest-port>
        </udp>
      </service-set>
      <service-set>
        <name>service_8086_TCP</name>
        <tcp>
          <source-port>1-65535</source-port>
          <dest-port>8086</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>service_21_TCP</name>
        <tcp>
          <source-port>1-65535</source-port>
          <dest-port>21</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>service_22_TCP</name>
        <tcp>
          <source-port>1-65535</source-port>
          <dest-port>22</dest-port>
        </tcp>
      </service-set>
      <service-group>
        <name>server01</name>
        <service-set>
          <name>https</name>
        </service-set>
        <service-set>
          <name>ssh</name>
        </service-set>
        <service-set>
          <name>ping</name>
        </service-set>
      </service-group>
    </service-obj>
    <session-limit xmlns="urn:ruijie:ntos:params:xml:ns:yang:session-limit">
      <pps-limit>
        <enabled>false</enabled>
        <global-pps>0</global-pps>
      </pps-limit>
      <sps-limit>
        <enabled>false</enabled>
        <global-sps>0</global-sps>
      </sps-limit>
      <total-session>
        <enabled>false</enabled>
      </total-session>
    </session-limit>
    <sim-status xmlns="urn:ruijie:ntos:params:xml:ns:yang:sim-security-policy">
      <action>free</action>
    </sim-status>
    <sim-security-policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:sim-security-policy">
      <policy>
        <name>a</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <time-range>any</time-range>
        <action>deny</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>nat_test01</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <service>
          <name>nat_test01</name>
        </service>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>auto</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>ptest01</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-zone>
          <name>trust</name>
        </source-zone>
        <service>
          <name>server01</name>
        </service>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy>
        <name>nat_test07</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <service>
          <name>nat_test07</name>
        </service>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>auto</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>test006</name>
        <enabled>true</enabled>
        <description>接口</description>
        <group-name>def-group</group-name>
        <source-interface>
          <name>Ge0/3</name>
        </source-interface>
        <source-interface>
          <name>Ge0/2</name>
        </source-interface>
        <dest-interface>
          <name>Ge0/6</name>
        </dest-interface>
        <source-network>
          <name>*************</name>
        </source-network>
        <dest-network>
          <name>*************</name>
        </dest-network>
        <service>
          <name>dns-u</name>
        </service>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
    </sim-security-policy>
    <ssh-server xmlns="urn:ruijie:ntos:params:xml:ns:yang:ssh-server">
      <enabled>true</enabled>
      <port>22</port>
      <deny-count>3</deny-count>
      <unlock-time>60</unlock-time>
    </ssh-server>
    <ssl-proxy xmlns="urn:ruijie:ntos:params:xml:ns:yang:ssl-proxy">
      <profile>
        <name>default</name>
        <description>Default Template, Traffic proxy for Internet access of users.</description>
        <outbound/>
      </profile>
      <ca-cert>
        <trust-cert>default_ca</trust-cert>
      </ca-cert>
    </ssl-proxy>
    <network-stack xmlns="urn:ruijie:ntos:params:xml:ns:yang:system">
      <ipv4>
        <arp-ignore>check-interface-and-subnet</arp-ignore>
      </ipv4>
    </network-stack>
    <threat-intelligence xmlns="urn:ruijie:ntos:params:xml:ns:yang:threat-intelligence">
      <management>
        <enable-status>true</enable-status>
        <enable-ai>false</enable-ai>
        <security-zone>
          <auto>true</auto>
        </security-zone>
      </management>
    </threat-intelligence>
    <time-range xmlns="urn:ruijie:ntos:params:xml:ns:yang:time-range">
      <range>
        <name>any</name>
        <description>Time range of all the time.</description>
        <period>
          <start>00:00:00</start>
          <end>23:59:59</end>
          <weekday>
            <key>sun</key>
          </weekday>
          <weekday>
            <key>mon</key>
          </weekday>
          <weekday>
            <key>tue</key>
          </weekday>
          <weekday>
            <key>wed</key>
          </weekday>
          <weekday>
            <key>thu</key>
          </weekday>
          <weekday>
            <key>fri</key>
          </weekday>
          <weekday>
            <key>sat</key>
          </weekday>
        </period>
      </range>
      <range>
        <name>test1</name>
        <once>
          <start>23:00:00/2025-06-15</start>
          <end>02:59:59/2025-06-16</end>
        </once>
      </range>
      <range>
        <name>test2</name>
        <period>
          <start>10:00:00</start>
          <end>23:59:59</end>
          <weekday>
            <key>sun</key>
          </weekday>
          <weekday>
            <key>sat</key>
          </weekday>
          <weekday>
            <key>wed</key>
          </weekday>
          <weekday>
            <key>tue</key>
          </weekday>
          <weekday>
            <key>mon</key>
          </weekday>
        </period>
      </range>
      <range>
        <name>test3</name>
        <start>23:00:00/2025-06-15</start>
        <end>02:00:00/2025-06-20</end>
        <period>
          <start>10:00:00</start>
          <end>23:59:59</end>
          <weekday>
            <key>mon</key>
          </weekday>
          <weekday>
            <key>tue</key>
          </weekday>
        </period>
      </range>
    </time-range>
    <traffic-analy xmlns="urn:ruijie:ntos:params:xml:ns:yang:traffic-analy">
      <enabled>false</enabled>
    </traffic-analy>
    <upnp-proxy xmlns="urn:ruijie:ntos:params:xml:ns:yang:upnp-proxy">
      <enabled>false</enabled>
      <bind-rule>ip</bind-rule>
      <advance>
        <automatic-enrollment>
          <enabled>false</enabled>
          <registration-time>1440</registration-time>
          <logout-check-period>3</logout-check-period>
        </automatic-enrollment>
        <terminal-authorization>
          <enabled>false</enabled>
        </terminal-authorization>
        <linkage-service>
          <enabled>false</enabled>
        </linkage-service>
        <offline-detect>
          <time-range>150</time-range>
          <flow-rate>0</flow-rate>
        </offline-detect>
        <scheduled-offline>
          <enabled>false</enabled>
          <time>00:00</time>
        </scheduled-offline>
        <quick-response-code-valid-time>
          <time>480</time>
        </quick-response-code-valid-time>
      </advance>
      <reserve>
        <single-ip-process>
          <interval-time>5</interval-time>
          <max-package>40</max-package>
        </single-ip-process>
        <unicast>
          <enabled>false</enabled>
        </unicast>
        <web-url-compatible>
          <enabled>false</enabled>
        </web-url-compatible>
        <map-cover-mode>
          <enabled>false</enabled>
        </map-cover-mode>
        <server-capacity>1</server-capacity>
      </reserve>
    </upnp-proxy>
    <user-management xmlns="urn:ruijie:ntos:params:xml:ns:yang:user-management">
      <user>
        <name>user1</name>
        <aaa-domain>default</aaa-domain>
        <enabled>true</enabled>
        <password>=*-#!$_2g+awtqJWZWf+C6zl4RLQ==</password>
        <parent-group-path>/default</parent-group-path>
        <ip-mac-binding>
          <no-binding/>
        </ip-mac-binding>
        <expiration-time>
          <never-expire/>
        </expiration-time>
      </user>
      <user>
        <name>abc123</name>
        <aaa-domain>vpn</aaa-domain>
        <enabled>true</enabled>
        <password>=*-#!$Yjf0lVGs99pWf+C6zl4RLQ==</password>
        <parent-group-path>/vpn</parent-group-path>
        <ip-mac-binding>
          <no-binding/>
        </ip-mac-binding>
        <expiration-time>
          <never-expire/>
        </expiration-time>
      </user>
    </user-management>
    <webauth xmlns="urn:ruijie:ntos:params:xml:ns:yang:webauth">
      <authentication-options>
        <https-redirection-enabled>true</https-redirection-enabled>
        <portal-authentication>
          <portal-group>
            <name>cportal</name>
            <protocol>portal</protocol>
          </portal-group>
        </portal-authentication>
      </authentication-options>
      <single-sign-on>
        <ad>
          <method>plugin</method>
        </ad>
      </single-sign-on>
    </webauth>
    <wlan xmlns="urn:ruijie:ntos:params:xml:ns:yang:wlan">
      <web-ac>
        <topology>ac-connect</topology>
      </web-ac>
      <diag>
        <enabled>false</enabled>
      </diag>
      <ac-controller>
        <ac-name>Ruijie_Ac_V0001</ac-name>
        <acctrl-trap>
          <acap-microap-ctrl>
            <enabled>false</enabled>
          </acap-microap-ctrl>
          <acap-updown-ctrl>
            <enabled>false</enabled>
          </acap-updown-ctrl>
          <acap-joinfail-ctrl>
            <enabled>false</enabled>
          </acap-joinfail-ctrl>
          <acap-decryeroreport-ctrl>
            <enabled>false</enabled>
          </acap-decryeroreport-ctrl>
          <acap-imageupdt-ctrl>
            <enabled>false</enabled>
          </acap-imageupdt-ctrl>
          <acap-timestamp-ctrl>
            <enabled>false</enabled>
          </acap-timestamp-ctrl>
          <acsta-oper-ctrl>
            <enabled>false</enabled>
          </acsta-oper-ctrl>
        </acctrl-trap>
        <ap-auth>
          <serial>
            <enabled>false</enabled>
          </serial>
          <password>
            <enabled>false</enabled>
          </password>
          <certificate>
            <enabled>false</enabled>
          </certificate>
        </ap-auth>
        <ap-priority>
          <enabled>false</enabled>
        </ap-priority>
        <bind-ap-mac>
          <enabled>false</enabled>
        </bind-ap-mac>
        <balance-group>
          <flow-balance-group>
            <base>10</base>
          </flow-balance-group>
        </balance-group>
        <sta-balance>
          <num-limit>
            <enabled>false</enabled>
          </num-limit>
        </sta-balance>
        <sta-blacklist>
          <enabled>false</enabled>
        </sta-blacklist>
        <black-white-list>
          <blacklist>
            <enabled>true</enabled>
          </blacklist>
          <whitelist>
            <enabled>false</enabled>
          </whitelist>
        </black-white-list>
        <ac-control>
          <enabled>false</enabled>
        </ac-control>
        <ap-image>
          <auto-upgrade>
            <enabled>false</enabled>
          </auto-upgrade>
        </ap-image>
        <capwap>
          <dtls>
            <enabled>true</enabled>
          </dtls>
        </capwap>
      </ac-controller>
      <wids>
        <countermeasures>
          <enabled>false</enabled>
          <rssi-min>25</rssi-min>
          <channel-match>false</channel-match>
        </countermeasures>
      </wids>
    </wlan>
  </vrf>
  <system xmlns="urn:ruijie:ntos:params:xml:ns:yang:system">
    <devicename>RG-WALL</devicename>
    <cp-mask>default</cp-mask>
    <nfp>
      <autoperf>
        <enabled>true</enabled>
      </autoperf>
    </nfp>
    <network-stack>
      <bridge>
        <call-ipv4-filtering>false</call-ipv4-filtering>
        <call-ipv6-filtering>false</call-ipv6-filtering>
      </bridge>
      <icmp>
        <rate-limit-icmp>1000</rate-limit-icmp>
        <rate-mask-icmp>destination-unreachable source-quench time-exceeded parameter-problem</rate-mask-icmp>
      </icmp>
      <ipv4>
        <forwarding>true</forwarding>
        <send-redirects>true</send-redirects>
        <accept-redirects>false</accept-redirects>
        <accept-source-route>false</accept-source-route>
        <arp-announce>any</arp-announce>
        <arp-filter>false</arp-filter>
        <arp-ignore>any</arp-ignore>
        <log-invalid-addresses>false</log-invalid-addresses>
      </ipv4>
      <ipv6>
        <forwarding>true</forwarding>
        <autoconfiguration>true</autoconfiguration>
        <accept-router-advert>never</accept-router-advert>
        <accept-redirects>false</accept-redirects>
        <accept-source-route>false</accept-source-route>
        <router-solicitations>-1</router-solicitations>
        <use-temporary-addresses>never</use-temporary-addresses>
      </ipv6>
    </network-stack>
    <timezone>Asia/Shanghai</timezone>
    <scheduled-restart>
      <enabled>false</enabled>
      <hour>3</hour>
      <minute>0</minute>
      <once>false</once>
    </scheduled-restart>
    <anti-virus-file-exception xmlns="urn:ruijie:ntos:params:xml:ns:yang:anti-virus">
      <enabled>true</enabled>
    </anti-virus-file-exception>
    <auth xmlns="urn:ruijie:ntos:params:xml:ns:yang:system:auth">
      <user>
        <name>admin</name>
        <role>admin</role>
        <password>=*-#!$XIYv1U3_Hn_dYt27dSgPxWL1oRjhgGOFZYlEDFIYw76GmrHZ36fXrwfevdHxAbncV9KJMPXFuLEk1FrK5JXJkw==</password>
        <network-password>=*-#!$Yjf0lVGs99pWf+C6zl4RLQ==</network-password>
      </user>
      <user>
        <name>securityadmin</name>
        <role>admin</role>
        <lock>true</lock>
      </user>
      <user>
        <name>useradmin</name>
        <role>admin</role>
        <lock>true</lock>
      </user>
      <user>
        <name>auditadmin</name>
        <role>admin</role>
        <lock>true</lock>
      </user>
    </auth>
    <wis-service xmlns="urn:ruijie:ntos:params:xml:ns:yang:cloud-service">
      <enabled>true</enabled>
    </wis-service>
    <macc-service xmlns="urn:ruijie:ntos:params:xml:ns:yang:cloud-service">
      <enabled>true</enabled>
    </macc-service>
    <security-cloud-service xmlns="urn:ruijie:ntos:params:xml:ns:yang:cloud-service">
      <enabled>true</enabled>
    </security-cloud-service>
    <log2cloud xmlns="urn:ruijie:ntos:params:xml:ns:yang:cloud-service">
      <upload-interval>5</upload-interval>
    </log2cloud>
    <collect xmlns="urn:ruijie:ntos:params:xml:ns:yang:system:collect">
      <alarm-threshold>
        <disk-db-threshold>90</disk-db-threshold>
      </alarm-threshold>
      <log-switches>
        <log>
          <log-name>flow</log-name>
          <log-module>
            <mod-name>sim-security-policy</mod-name>
            <collect-enabled>false</collect-enabled>
            <store-enabled>false</store-enabled>
          </log-module>
        </log>
      </log-switches>
      <enabled>true</enabled>
      <max-records>3072</max-records>
      <record-interval>300</record-interval>
      <memory-storage-threshold>90</memory-storage-threshold>
      <statistics-enabled>false</statistics-enabled>
      <record-stats-enabled>true</record-stats-enabled>
      <flow-log-enabled>false</flow-log-enabled>
      <log-language>English</log-language>
    </collect>
    <dataplane xmlns="urn:ruijie:ntos:params:xml:ns:yang:dataplane-dsa">
      <hash>
        <type>hash-default</type>
        <bind>none</bind>
      </hash>
    </dataplane>
    <flow-audit xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-audit">
      <flowrate>
        <enable>false</enable>
      </flowrate>
      <hard-disk-quota>20</hard-disk-quota>
      <refresh-time>30</refresh-time>
    </flow-audit>
    <local-defend xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
      <enabled>true</enabled>
      <host-hardening>
        <enabled>true</enabled>
        <injection-prevention>
          <enabled>false</enabled>
          <action>block</action>
        </injection-prevention>
      </host-hardening>
      <arp-monitor>
        <enabled>false</enabled>
        <scan-threshold>200</scan-threshold>
      </arp-monitor>
      <rate-limit>
        <arp>
          <req-token>5</req-token>
          <res-token>1</res-token>
          <req-threshold>100</req-threshold>
          <res-threshold>100</res-threshold>
        </arp>
      </rate-limit>
    </local-defend>
    <memory xmlns="urn:ruijie:ntos:params:xml:ns:yang:system:mem">
      <warning-threshold>95</warning-threshold>
      <critical-threshold>95</critical-threshold>
    </memory>
    <network-monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:network-monitor">
      <enabled>false</enabled>
      <wireless-gather-type>snmp</wireless-gather-type>
    </network-monitor>
    <srpds xmlns="urn:ruijie:ntos:params:xml:ns:yang:srpds">
      <nosync-instance>/ntos:config/vrf[name='main']/ntos-interface:interface/physical[name='Ge0/0']</nosync-instance>
      <nosync-instance>/ntos:config/vrf[name='main']/ntos-interface:interface/physical[name='Ge0/5']</nosync-instance>
    </srpds>
    <usr-exp-plan xmlns="urn:ruijie:ntos:params:xml:ns:yang:user-experience-plan">
      <no-prompt>false</no-prompt>
      <enabled>false</enabled>
    </usr-exp-plan>
    <web-server xmlns="urn:ruijie:ntos:params:xml:ns:yang:system:web-server">
      <enabled>true</enabled>
      <port>443</port>
      <http-enabled>true</http-enabled>
      <smart-http-enabled>false</smart-http-enabled>
    </web-server>
  </system>
</config>
