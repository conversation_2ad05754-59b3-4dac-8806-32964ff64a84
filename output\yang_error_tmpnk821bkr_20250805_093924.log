================================================================================
YANG验证错误详细信息
================================================================================
时间: 2025-08-05 09:39:24
XML文件: /tmp/tmpnk821bkr.xml
设备型号: z3200s
设备版本: R11
--------------------------------------------------------------------------------
yanglint错误输出:
--------------------------------------------------------------------------------
libyang err : Invalid union value "main
" - no matching subtype found:
    libyang 2 - enumeration, version 1: Invalid enumeration value "main
".
    libyang 2 - string, version 1: vrf name should only contain alphanumerical
characters, underscores and dashes.
 (/ntos:config/vrf/name) (line 4)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-aaad:aaa/enabled) (line 7)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-aaad:aaa/domain-enabled) (line 9)
libyang err : Use unsuport character. (/ntos:config/vrf/ntos-aaad:aaa/domain/name) (line 12)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-aaad:aaa/domain/authentication/sslvpn/enabled) (line 18)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-aaad:aaa/domain/authentication/webauth/enabled) (line 25)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-aaad:aaa/domain/enabled) (line 31)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-aaad:aaa/domain/auto-create-group) (line 37)
libyang err : List instance is missing its key "name". (/ntos:config/vrf/ntos-aaad:aaa/domain) (line 39)
libyang err : Use unsuport character. (/ntos:config/vrf/ntos-aaad:aaa/domain/name) (line 42)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-aaad:aaa/domain/authentication/sslvpn/enabled) (line 48)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-aaad:aaa/domain/authentication/vpn/enabled) (line 55)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-aaad:aaa/domain/enabled) (line 61)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-aaad:aaa/domain/auto-create-group) (line 67)
libyang err : List instance is missing its key "name". (/ntos:config/vrf/ntos-aaad:aaa/domain) (line 69)
libyang err : Use unsuport character. (/ntos:config/vrf/ntos-aaad:aaa/domain/name) (line 72)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-aaad:aaa/domain/authentication/pppoe/enabled) (line 78)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-aaad:aaa/domain/enabled) (line 84)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-aaad:aaa/domain/auto-create-group) (line 90)
libyang err : List instance is missing its key "name". (/ntos:config/vrf/ntos-aaad:aaa/domain) (line 92)
libyang err : Invalid enumeration value "only-subs
". (/ntos:config/vrf/ntos-aaad:aaa/authentication/sslvpn[name='default
']/auth-order) (line 99)
libyang err : Invalid enumeration value "only-subs
". (/ntos:config/vrf/ntos-aaad:aaa/authentication/sslvpn[name='vpn
']/auth-order) (line 107)
libyang err : Invalid enumeration value "only-subs
". (/ntos:config/vrf/ntos-aaad:aaa/authentication/webauth[name='default
']/auth-order) (line 115)
libyang err : Invalid enumeration value "only-subs
". (/ntos:config/vrf/ntos-aaad:aaa/authentication/vpn[name='vpn
']/auth-order) (line 123)
libyang err : Invalid enumeration value "only-subs
". (/ntos:config/vrf/ntos-aaad:aaa/authentication/pppoe[name='pppoe
']/auth-order) (line 131)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-aaad:aaa/accounting/update/enabled) (line 141)
libyang err : Invalid enumeration value "blacklist
". (/ntos:config/vrf/ntos-app-behavior-control:app-behavior-control/qq-control/control-mode) (line 151)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-app-parse-mgmt:app-parse-mgmt/overload-protection/enabled) (line 159)
libyang err : Invalid enumeration value "bypass
". (/ntos:config/vrf/ntos-app-parse-mgmt:app-parse-mgmt/overload-protection/action) (line 161)
libyang err : Invalid enumeration value "dynamic-identify
". (/ntos:config/vrf/ntos-appid:appid/mode) (line 173)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-arp:arp/proxy-enabled) (line 178)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-arp:arp/trusted/nud-probe/enabled) (line 182)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-arp:arp/gratuitous-send/enabled) (line 189)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-collab-disposal:collab-disposal/enabled) (line 206)
libyang err : Invalid enumeration value "none
". (/ntos:config/vrf/ntos-collab-disposal:collab-disposal/identity-system) (line 208)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-dhcp:dhcp/server/enabled) (line 214)
libyang err : Unsatisfied pattern - "***********/24
" does not conform to "(([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])/(([0-9])|([1-2][0-9])|(3[0-2]))". (/ntos:config/vrf/ntos-dhcp:dhcp/server/subnet/prefix) (line 221)
libyang err : Interface name can only contain characters from [-A-Za-z0-9._@/]. (/ntos:config/vrf/ntos-dhcp:dhcp/server/subnet/interface) (line 223)
libyang err : Failed to convert IPv4 address "*************
". (/ntos:config/vrf/ntos-dhcp:dhcp/server/subnet/default-gateway) (line 225)
libyang err : Failed to convert IPv4 address "***********
". (/ntos:config/vrf/ntos-dhcp:dhcp/server/subnet/range/start-ip) (line 228)
libyang err : Failed to convert IPv4 address "*************
". (/ntos:config/vrf/ntos-dhcp:dhcp/server/subnet/range/end-ip) (line 230)
libyang err : List instance is missing its key "start-ip". (/ntos:config/vrf/ntos-dhcp:dhcp/server/subnet/range) (line 232)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-dhcp:dhcp/server/subnet/ping-check) (line 234)
libyang err : Invalid enumeration value "hex
". (/ntos:config/vrf/ntos-dhcp:dhcp/server/subnet/lease-id-format) (line 240)
libyang err : List instance is missing its key "prefix". (/ntos:config/vrf/ntos-dhcp:dhcp/server/subnet) (line 246)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-dns-client:dns-client/ip-domain-lookup/enabled) (line 254)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-dns-security:dns-security/dns-filter/dns-status) (line 262)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-dns-security:dns-security/dns-attack-defense/attack-defense/protocol) (line 268)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-dns-security:dns-security/dns-attack-defense/attack-defense/security-vul) (line 270)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-dns-security:dns-security/dns-attack-defense/flood-defense/enable) (line 275)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-dns-security:dns-security/dns-security-cloud/pdns) (line 282)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-dns-security:dns-security/dns-security-cloud/online-protect) (line 284)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-dns-transparent-proxy:dns-transparent-proxy/enabled) (line 291)
libyang err : Invalid enumeration value "mllb
". (/ntos:config/vrf/ntos-dns-transparent-proxy:dns-transparent-proxy/mode) (line 293)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-file-filter:file-filter/global-config/feature-identify-enabled) (line 299)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-flow-control:flow-control/enabled) (line 306)
libyang err : Invalid enumeration value "A-P
". (/ntos:config/vrf/ntos-ha:ha/mode) (line 311)
libyang err : Unsatisfied pattern - "00:00:5e
" does not conform to "[0-9a-fA-F]{2}(:[0-9a-fA-F]{2}){2}". (/ntos:config/vrf/ntos-ha:ha/vmac-prefix) (line 319)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-ha:ha/preempt) (line 321)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-ha:ha/session-sync) (line 325)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-ha:ha/neigh-sync) (line 327)
libyang err : Invalid enumeration value "none
". (/ntos:config/vrf/ntos-ha:ha/auth-type) (line 331)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-ha:ha/enabled) (line 333)
libyang err : Invalid enumeration value "on
". (/ntos:config/vrf/ntos-ha:ha/log-level/group) (line 336)
libyang err : Invalid enumeration value "off
". (/ntos:config/vrf/ntos-ha:ha/log-level/adv) (line 338)
libyang err : Invalid enumeration value "on
". (/ntos:config/vrf/ntos-ha:ha/log-level/dbus) (line 340)
libyang err : Invalid enumeration value "off
". (/ntos:config/vrf/ntos-ha:ha/log-level/monitor) (line 342)
libyang err : Invalid enumeration value "preshared-key
". (/ntos:config/vrf/ntos-ike:ike/proposal[name='default
']/auth-mode) (line 362)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-ike:ike/nat-traversal/enabled) (line 384)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/snmp/if-global-notify-enable) (line 399)
libyang err : Interface name can only contain characters from [-A-Za-z0-9._@/]. (/ntos:config/vrf/ntos-interface:interface/physical/name) (line 404)
libyang err : Invalid union value "***********/24
" - no matching subtype found:
    libyang 2 - ipv4-address-no-zone, version 1: Failed to convert IPv4 address "***********/24
".
    libyang 2 - ipv4-prefix, version 1: Unsatisfied pattern - "***********/24
" does not conform to "(([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])/(([0-9])|([1-2][0-9])|(3[0-2]))".
 (/ntos:config/vrf/ntos-interface:interface/physical/ipv4/address/ip) (line 408)
libyang err : List instance is missing its key "ip". (/ntos:config/vrf/ntos-interface:interface/physical/ipv4/address) (line 410)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/physical/ipv4/enabled) (line 412)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ipv6/enabled) (line 417)
libyang err : Invalid enumeration value "check-interface-and-subnet
". (/ntos:config/vrf/ntos-interface:interface/physical/network-stack/ipv4/arp-ignore) (line 423)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/physical/reverse-path) (line 429)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/snooping/trust) (line 432)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/snooping/suppress) (line 434)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-flow-control:flow-control/enabled) (line 439)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-if-mon:monitor/if-notify-enable) (line 448)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-ip-mac:ip-mac-bind/ipv4/enabled) (line 462)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-ip-mac:ip-mac-bind/ipv6/enabled) (line 467)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-local-defend:access-control/https) (line 474)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-local-defend:access-control/ping) (line 476)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-local-defend:access-control/ssh) (line 478)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/physical/enabled) (line 482)
libyang err : Invalid enumeration value "route
". (/ntos:config/vrf/ntos-interface:interface/physical/working-mode) (line 484)
libyang err : Invalid enumeration value "lan
". (/ntos:config/vrf/ntos-interface:interface/physical/wanlan) (line 486)
libyang err : List instance is missing its key "name". (/ntos:config/vrf/ntos-interface:interface/physical) (line 489)
libyang err : Interface name can only contain characters from [-A-Za-z0-9._@/]. (/ntos:config/vrf/ntos-interface:interface/physical/name) (line 492)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/physical/ipv4/enabled) (line 495)
libyang err : Invalid union value "***********/26
" - no matching subtype found:
    libyang 2 - ipv4-address-no-zone, version 1: Failed to convert IPv4 address "***********/26
".
    libyang 2 - ipv4-prefix, version 1: Unsatisfied pattern - "***********/26
" does not conform to "(([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])/(([0-9])|([1-2][0-9])|(3[0-2]))".
 (/ntos:config/vrf/ntos-interface:interface/physical/ipv4/address/ip) (line 498)
libyang err : List instance is missing its key "ip". (/ntos:config/vrf/ntos-interface:interface/physical/ipv4/address) (line 500)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ipv6/enabled) (line 505)
libyang err : Invalid enumeration value "check-interface-and-subnet
". (/ntos:config/vrf/ntos-interface:interface/physical/network-stack/ipv4/arp-ignore) (line 511)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/physical/reverse-path) (line 517)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/snooping/trust) (line 520)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/snooping/suppress) (line 522)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-flow-control:flow-control/enabled) (line 527)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-if-mon:monitor/if-notify-enable) (line 536)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-ip-mac:ip-mac-bind/ipv4/enabled) (line 550)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-ip-mac:ip-mac-bind/ipv6/enabled) (line 555)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-local-defend:access-control/https) (line 562)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-local-defend:access-control/ping) (line 564)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-local-defend:access-control/ssh) (line 566)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/enabled) (line 570)
libyang err : Invalid enumeration value "route
". (/ntos:config/vrf/ntos-interface:interface/physical/working-mode) (line 572)
libyang err : Invalid enumeration value "wan
". (/ntos:config/vrf/ntos-interface:interface/physical/wanlan) (line 574)
libyang err : List instance is missing its key "name". (/ntos:config/vrf/ntos-interface:interface/physical) (line 577)
libyang err : Interface name can only contain characters from [-A-Za-z0-9._@/]. (/ntos:config/vrf/ntos-interface:interface/physical/name) (line 580)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/physical/ipv4/enabled) (line 583)
libyang err : Invalid union value "***********/24
" - no matching subtype found:
    libyang 2 - ipv4-address-no-zone, version 1: Failed to convert IPv4 address "***********/24
".
    libyang 2 - ipv4-prefix, version 1: Unsatisfied pattern - "***********/24
" does not conform to "(([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])/(([0-9])|([1-2][0-9])|(3[0-2]))".
 (/ntos:config/vrf/ntos-interface:interface/physical/ipv4/address/ip) (line 586)
libyang err : List instance is missing its key "ip". (/ntos:config/vrf/ntos-interface:interface/physical/ipv4/address) (line 588)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ipv6/enabled) (line 593)
libyang err : Invalid enumeration value "check-interface-and-subnet
". (/ntos:config/vrf/ntos-interface:interface/physical/network-stack/ipv4/arp-ignore) (line 599)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/physical/reverse-path) (line 605)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/snooping/trust) (line 608)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/snooping/suppress) (line 610)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-flow-control:flow-control/enabled) (line 615)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-if-mon:monitor/if-notify-enable) (line 624)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-ip-mac:ip-mac-bind/ipv4/enabled) (line 638)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-ip-mac:ip-mac-bind/ipv6/enabled) (line 643)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-local-defend:access-control/https) (line 650)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-local-defend:access-control/ping) (line 652)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-local-defend:access-control/ssh) (line 654)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/physical/enabled) (line 658)
libyang err : Invalid enumeration value "route
". (/ntos:config/vrf/ntos-interface:interface/physical/working-mode) (line 660)
libyang err : Invalid enumeration value "lan
". (/ntos:config/vrf/ntos-interface:interface/physical/wanlan) (line 662)
libyang err : List instance is missing its key "name". (/ntos:config/vrf/ntos-interface:interface/physical) (line 665)
libyang err : Interface name can only contain characters from [-A-Za-z0-9._@/]. (/ntos:config/vrf/ntos-interface:interface/physical/name) (line 668)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/physical/ipv4/enabled) (line 671)
libyang err : Invalid union value "**********/16
" - no matching subtype found:
    libyang 2 - ipv4-address-no-zone, version 1: Failed to convert IPv4 address "**********/16
".
    libyang 2 - ipv4-prefix, version 1: Unsatisfied pattern - "**********/16
" does not conform to "(([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])/(([0-9])|([1-2][0-9])|(3[0-2]))".
 (/ntos:config/vrf/ntos-interface:interface/physical/ipv4/address/ip) (line 674)
libyang err : List instance is missing its key "ip". (/ntos:config/vrf/ntos-interface:interface/physical/ipv4/address) (line 676)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ipv6/enabled) (line 681)
libyang err : Invalid enumeration value "check-interface-and-subnet
". (/ntos:config/vrf/ntos-interface:interface/physical/network-stack/ipv4/arp-ignore) (line 687)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/physical/reverse-path) (line 693)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/snooping/trust) (line 696)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/snooping/suppress) (line 698)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-flow-control:flow-control/enabled) (line 703)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-if-mon:monitor/if-notify-enable) (line 712)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-ip-mac:ip-mac-bind/ipv4/enabled) (line 726)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-ip-mac:ip-mac-bind/ipv6/enabled) (line 731)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-local-defend:access-control/https) (line 738)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-local-defend:access-control/ping) (line 740)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-local-defend:access-control/ssh) (line 742)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/physical/enabled) (line 746)
libyang err : Invalid enumeration value "route
". (/ntos:config/vrf/ntos-interface:interface/physical/working-mode) (line 748)
libyang err : Invalid enumeration value "lan
". (/ntos:config/vrf/ntos-interface:interface/physical/wanlan) (line 750)
libyang err : List instance is missing its key "name". (/ntos:config/vrf/ntos-interface:interface/physical) (line 753)
libyang err : Interface name can only contain characters from [-A-Za-z0-9._@/]. (/ntos:config/vrf/ntos-interface:interface/physical/name) (line 756)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/physical/enabled) (line 758)
libyang err : Invalid enumeration value "route
". (/ntos:config/vrf/ntos-interface:interface/physical/working-mode) (line 760)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/physical/ipv4/enabled) (line 763)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ipv6/enabled) (line 768)
libyang err : Invalid enumeration value "check-interface-and-subnet
". (/ntos:config/vrf/ntos-interface:interface/physical/network-stack/ipv4/arp-ignore) (line 774)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/physical/reverse-path) (line 780)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/snooping/trust) (line 783)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/snooping/suppress) (line 785)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-flow-control:flow-control/enabled) (line 790)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-if-mon:monitor/if-notify-enable) (line 799)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-ip-mac:ip-mac-bind/ipv4/enabled) (line 813)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-ip-mac:ip-mac-bind/ipv6/enabled) (line 818)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-local-defend:access-control/https) (line 825)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-local-defend:access-control/ping) (line 827)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-local-defend:access-control/ssh) (line 829)
libyang err : List instance is missing its key "name". (/ntos:config/vrf/ntos-interface:interface/physical) (line 833)
libyang err : Interface name can only contain characters from [-A-Za-z0-9._@/]. (/ntos:config/vrf/ntos-interface:interface/physical/name) (line 836)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/physical/ipv4/enabled) (line 839)
libyang err : Invalid union value "*************/30
" - no matching subtype found:
    libyang 2 - ipv4-address-no-zone, version 1: Failed to convert IPv4 address "*************/30
".
    libyang 2 - ipv4-prefix, version 1: Unsatisfied pattern - "*************/30
" does not conform to "(([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])/(([0-9])|([1-2][0-9])|(3[0-2]))".
 (/ntos:config/vrf/ntos-interface:interface/physical/ipv4/address/ip) (line 842)
libyang err : List instance is missing its key "ip". (/ntos:config/vrf/ntos-interface:interface/physical/ipv4/address) (line 844)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ipv6/enabled) (line 849)
libyang err : Invalid enumeration value "check-interface-and-subnet
". (/ntos:config/vrf/ntos-interface:interface/physical/network-stack/ipv4/arp-ignore) (line 855)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/physical/reverse-path) (line 861)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/snooping/trust) (line 864)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/snooping/suppress) (line 866)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-flow-control:flow-control/enabled) (line 871)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-if-mon:monitor/if-notify-enable) (line 880)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-ip-mac:ip-mac-bind/ipv4/enabled) (line 894)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-ip-mac:ip-mac-bind/ipv6/enabled) (line 899)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-local-defend:access-control/https) (line 906)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-local-defend:access-control/ping) (line 908)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-local-defend:access-control/ssh) (line 910)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/physical/enabled) (line 914)
libyang err : Invalid enumeration value "route
". (/ntos:config/vrf/ntos-interface:interface/physical/working-mode) (line 916)
libyang err : Invalid enumeration value "lan
". (/ntos:config/vrf/ntos-interface:interface/physical/wanlan) (line 918)
libyang err : List instance is missing its key "name". (/ntos:config/vrf/ntos-interface:interface/physical) (line 921)
libyang err : Interface name can only contain characters from [-A-Za-z0-9._@/]. (/ntos:config/vrf/ntos-interface:interface/physical/name) (line 924)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/physical/ipv4/enabled) (line 927)
libyang err : Invalid union value "***********/24
" - no matching subtype found:
    libyang 2 - ipv4-address-no-zone, version 1: Failed to convert IPv4 address "***********/24
".
    libyang 2 - ipv4-prefix, version 1: Unsatisfied pattern - "***********/24
" does not conform to "(([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])/(([0-9])|([1-2][0-9])|(3[0-2]))".
 (/ntos:config/vrf/ntos-interface:interface/physical/ipv4/address/ip) (line 930)
libyang err : List instance is missing its key "ip". (/ntos:config/vrf/ntos-interface:interface/physical/ipv4/address) (line 932)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ipv6/enabled) (line 937)
libyang err : Invalid enumeration value "check-interface-and-subnet
". (/ntos:config/vrf/ntos-interface:interface/physical/network-stack/ipv4/arp-ignore) (line 943)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/physical/reverse-path) (line 949)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/snooping/trust) (line 952)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/snooping/suppress) (line 954)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-flow-control:flow-control/enabled) (line 959)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-if-mon:monitor/if-notify-enable) (line 968)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-ip-mac:ip-mac-bind/ipv4/enabled) (line 982)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-ip-mac:ip-mac-bind/ipv6/enabled) (line 987)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-local-defend:access-control/https) (line 994)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-local-defend:access-control/ping) (line 996)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-local-defend:access-control/ssh) (line 998)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/physical/enabled) (line 1002)
libyang err : Invalid enumeration value "route
". (/ntos:config/vrf/ntos-interface:interface/physical/working-mode) (line 1004)
libyang err : Invalid enumeration value "lan
". (/ntos:config/vrf/ntos-interface:interface/physical/wanlan) (line 1006)
libyang err : List instance is missing its key "name". (/ntos:config/vrf/ntos-interface:interface/physical) (line 1009)
libyang err : Interface name can only contain characters from [-A-Za-z0-9._@/]. (/ntos:config/vrf/ntos-interface:interface/physical/name) (line 1012)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/physical/enabled) (line 1014)
libyang err : Invalid enumeration value "route
". (/ntos:config/vrf/ntos-interface:interface/physical/working-mode) (line 1016)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/physical/ipv4/enabled) (line 1019)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ipv6/enabled) (line 1024)
libyang err : Invalid enumeration value "check-interface-and-subnet
". (/ntos:config/vrf/ntos-interface:interface/physical/network-stack/ipv4/arp-ignore) (line 1030)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/physical/reverse-path) (line 1036)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/snooping/trust) (line 1039)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/snooping/suppress) (line 1041)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-flow-control:flow-control/enabled) (line 1046)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-if-mon:monitor/if-notify-enable) (line 1055)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-ip-mac:ip-mac-bind/ipv4/enabled) (line 1069)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-ip-mac:ip-mac-bind/ipv6/enabled) (line 1074)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-local-defend:access-control/https) (line 1081)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-local-defend:access-control/ping) (line 1083)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-local-defend:access-control/ssh) (line 1085)
libyang err : List instance is missing its key "name". (/ntos:config/vrf/ntos-interface:interface/physical) (line 1089)
libyang err : Interface name can only contain characters from [-A-Za-z0-9._@/]. (/ntos:config/vrf/ntos-interface:interface/physical/name) (line 1092)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/physical/ipv4/enabled) (line 1095)
libyang err : Invalid union value "*************/26
" - no matching subtype found:
    libyang 2 - ipv4-address-no-zone, version 1: Failed to convert IPv4 address "*************/26
".
    libyang 2 - ipv4-prefix, version 1: Unsatisfied pattern - "*************/26
" does not conform to "(([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])/(([0-9])|([1-2][0-9])|(3[0-2]))".
 (/ntos:config/vrf/ntos-interface:interface/physical/ipv4/address/ip) (line 1098)
libyang err : List instance is missing its key "ip". (/ntos:config/vrf/ntos-interface:interface/physical/ipv4/address) (line 1100)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ipv6/enabled) (line 1105)
libyang err : Invalid enumeration value "check-interface-and-subnet
". (/ntos:config/vrf/ntos-interface:interface/physical/network-stack/ipv4/arp-ignore) (line 1111)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/physical/reverse-path) (line 1117)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/snooping/trust) (line 1120)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/snooping/suppress) (line 1122)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-flow-control:flow-control/enabled) (line 1127)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-if-mon:monitor/if-notify-enable) (line 1136)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-ip-mac:ip-mac-bind/ipv4/enabled) (line 1150)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-ip-mac:ip-mac-bind/ipv6/enabled) (line 1155)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-local-defend:access-control/https) (line 1162)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-local-defend:access-control/ping) (line 1164)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-local-defend:access-control/ssh) (line 1166)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/physical/enabled) (line 1170)
libyang err : Invalid enumeration value "route
". (/ntos:config/vrf/ntos-interface:interface/physical/working-mode) (line 1172)
libyang err : Invalid enumeration value "wan
". (/ntos:config/vrf/ntos-interface:interface/physical/wanlan) (line 1174)
libyang err : List instance is missing its key "name". (/ntos:config/vrf/ntos-interface:interface/physical) (line 1177)
libyang err : Interface name can only contain characters from [-A-Za-z0-9._@/]. (/ntos:config/vrf/ntos-interface:interface/physical/name) (line 1180)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/physical/enabled) (line 1182)
libyang err : Invalid enumeration value "route
". (/ntos:config/vrf/ntos-interface:interface/physical/working-mode) (line 1184)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/physical/ipv4/enabled) (line 1187)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ipv6/enabled) (line 1192)
libyang err : Invalid enumeration value "check-interface-and-subnet
". (/ntos:config/vrf/ntos-interface:interface/physical/network-stack/ipv4/arp-ignore) (line 1198)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/physical/reverse-path) (line 1204)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/snooping/trust) (line 1207)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/snooping/suppress) (line 1209)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-flow-control:flow-control/enabled) (line 1214)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-if-mon:monitor/if-notify-enable) (line 1223)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-ip-mac:ip-mac-bind/ipv4/enabled) (line 1237)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-ip-mac:ip-mac-bind/ipv6/enabled) (line 1242)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-local-defend:access-control/https) (line 1249)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-local-defend:access-control/ping) (line 1251)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/physical/ntos-local-defend:access-control/ssh) (line 1253)
libyang err : List instance is missing its key "name". (/ntos:config/vrf/ntos-interface:interface/physical) (line 1257)
libyang err : Interface name can only contain characters from [-A-Za-z0-9._@/]. (/ntos:config/vrf/ntos-interface:interface/ntos-bridge:bridge/name) (line 1260)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/ntos-bridge:bridge/enabled) (line 1262)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/ntos-bridge:bridge/ipv4/enabled) (line 1265)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/ntos-bridge:bridge/ipv4/dhcp/enabled) (line 1268)
libyang err : Invalid enumeration value "subnet-mask
". (/ntos:config/vrf/ntos-interface:interface/ntos-bridge:bridge/ipv4/dhcp/request) (line 1282)
libyang err : Invalid enumeration value "broadcast-address
". (/ntos:config/vrf/ntos-interface:interface/ntos-bridge:bridge/ipv4/dhcp/request) (line 1284)
libyang err : Invalid enumeration value "time-offset
". (/ntos:config/vrf/ntos-interface:interface/ntos-bridge:bridge/ipv4/dhcp/request) (line 1286)
libyang err : Invalid enumeration value "routers
". (/ntos:config/vrf/ntos-interface:interface/ntos-bridge:bridge/ipv4/dhcp/request) (line 1288)
libyang err : Invalid enumeration value "domain-name
". (/ntos:config/vrf/ntos-interface:interface/ntos-bridge:bridge/ipv4/dhcp/request) (line 1290)
libyang err : Invalid enumeration value "domain-search
". (/ntos:config/vrf/ntos-interface:interface/ntos-bridge:bridge/ipv4/dhcp/request) (line 1292)
libyang err : Invalid enumeration value "domain-name-servers
". (/ntos:config/vrf/ntos-interface:interface/ntos-bridge:bridge/ipv4/dhcp/request) (line 1294)
libyang err : Invalid enumeration value "host-name
". (/ntos:config/vrf/ntos-interface:interface/ntos-bridge:bridge/ipv4/dhcp/request) (line 1296)
libyang err : Invalid enumeration value "nis-domain
". (/ntos:config/vrf/ntos-interface:interface/ntos-bridge:bridge/ipv4/dhcp/request) (line 1298)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/ntos-bridge:bridge/ipv6/enabled) (line 1305)
libyang err : Invalid enumeration value "check-interface-and-subnet
". (/ntos:config/vrf/ntos-interface:interface/ntos-bridge:bridge/network-stack/ipv4/arp-ignore) (line 1311)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/ntos-bridge:bridge/snooping/trust) (line 1318)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/ntos-bridge:bridge/snooping/suppress) (line 1320)
libyang err : Invalid enumeration value "dont-check
". (/ntos:config/vrf/ntos-interface:interface/ntos-bridge:bridge/session-source-check) (line 1324)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/ntos-bridge:bridge/ntos-if-mon:monitor/if-notify-enable) (line 1331)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/ntos-bridge:bridge/ntos-local-defend:access-control/https) (line 1344)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/ntos-bridge:bridge/ntos-local-defend:access-control/ping) (line 1346)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/ntos-bridge:bridge/ntos-local-defend:access-control/ssh) (line 1348)
libyang err : List instance is missing its key "name". (/ntos:config/vrf/ntos-interface:interface/ntos-bridge:bridge) (line 1352)
libyang err : Interface name can only contain characters from [-A-Za-z0-9._@/]. (/ntos:config/vrf/ntos-interface:interface/ntos-vswitch:vswitch/name) (line 1355)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/ntos-vswitch:vswitch/enabled) (line 1357)
libyang err : Invalid enumeration value "lan
". (/ntos:config/vrf/ntos-interface:interface/ntos-vswitch:vswitch/wanlan) (line 1359)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/ntos-vswitch:vswitch/ipv4/enabled) (line 1362)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/ntos-vswitch:vswitch/ipv4/dhcp/enabled) (line 1365)
libyang err : Invalid enumeration value "subnet-mask
". (/ntos:config/vrf/ntos-interface:interface/ntos-vswitch:vswitch/ipv4/dhcp/request) (line 1379)
libyang err : Invalid enumeration value "broadcast-address
". (/ntos:config/vrf/ntos-interface:interface/ntos-vswitch:vswitch/ipv4/dhcp/request) (line 1381)
libyang err : Invalid enumeration value "time-offset
". (/ntos:config/vrf/ntos-interface:interface/ntos-vswitch:vswitch/ipv4/dhcp/request) (line 1383)
libyang err : Invalid enumeration value "routers
". (/ntos:config/vrf/ntos-interface:interface/ntos-vswitch:vswitch/ipv4/dhcp/request) (line 1385)
libyang err : Invalid enumeration value "domain-name
". (/ntos:config/vrf/ntos-interface:interface/ntos-vswitch:vswitch/ipv4/dhcp/request) (line 1387)
libyang err : Invalid enumeration value "domain-search
". (/ntos:config/vrf/ntos-interface:interface/ntos-vswitch:vswitch/ipv4/dhcp/request) (line 1389)
libyang err : Invalid enumeration value "domain-name-servers
". (/ntos:config/vrf/ntos-interface:interface/ntos-vswitch:vswitch/ipv4/dhcp/request) (line 1391)
libyang err : Invalid enumeration value "host-name
". (/ntos:config/vrf/ntos-interface:interface/ntos-vswitch:vswitch/ipv4/dhcp/request) (line 1393)
libyang err : Invalid enumeration value "nis-domain
". (/ntos:config/vrf/ntos-interface:interface/ntos-vswitch:vswitch/ipv4/dhcp/request) (line 1395)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/ntos-vswitch:vswitch/ipv6/enabled) (line 1402)
libyang err : Invalid enumeration value "check-interface-and-subnet
". (/ntos:config/vrf/ntos-interface:interface/ntos-vswitch:vswitch/network-stack/ipv4/arp-ignore) (line 1408)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/ntos-vswitch:vswitch/reverse-path) (line 1414)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/ntos-vswitch:vswitch/snooping/trust) (line 1417)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/ntos-vswitch:vswitch/snooping/suppress) (line 1419)
libyang err : Invalid enumeration value "transparent-forward
". (/ntos:config/vrf/ntos-interface:interface/ntos-vswitch:vswitch/session-source-check) (line 1423)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-interface:interface/ntos-vswitch:vswitch/ntos-if-mon:monitor/if-notify-enable) (line 1430)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/ntos-vswitch:vswitch/ntos-local-defend:access-control/https) (line 1443)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/ntos-vswitch:vswitch/ntos-local-defend:access-control/ping) (line 1445)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-interface:interface/ntos-vswitch:vswitch/ntos-local-defend:access-control/ssh) (line 1447)
libyang err : List instance is missing its key "name". (/ntos:config/vrf/ntos-interface:interface/ntos-vswitch:vswitch) (line 1451)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-ip-mac:ip-mac-bind/ipv4/enabled) (line 1457)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-ip-mac:ip-mac-bind/ipv4/no-match-action-drop) (line 1459)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-ip-mac:ip-mac-bind/ipv6/enabled) (line 1464)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-ip-mac:ip-mac-bind/ipv6/no-match-action-drop) (line 1466)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-ip-track:track/enabled) (line 1473)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-ipsec:ipsec/anti-replay/check) (line 1479)
libyang err : Invalid enumeration value "64
". (/ntos:config/vrf/ntos-ipsec:ipsec/anti-replay/window-size) (line 1481)
libyang err : Invalid enumeration value "clear
". (/ntos:config/vrf/ntos-ipsec:ipsec/df-bit) (line 1485)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-ipsec:ipsec/prefrag) (line 1487)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-ipsec:ipsec/inbound-sp/check) (line 1490)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-ipsec:ipsec/hardware-crypto-offload) (line 1501)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-lldp:lldp/enabled) (line 1511)
libyang err : Interface name can only contain characters from [-A-Za-z0-9._@/]. (/ntos:config/vrf/ntos-lldp:lldp/interface/name) (line 1520)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-lldp:lldp/interface/enabled) (line 1522)
libyang err : List instance is missing its key "name". (/ntos:config/vrf/ntos-lldp:lldp/interface) (line 1524)
libyang err : Interface name can only contain characters from [-A-Za-z0-9._@/]. (/ntos:config/vrf/ntos-lldp:lldp/interface/name) (line 1527)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-lldp:lldp/interface/enabled) (line 1529)
libyang err : List instance is missing its key "name". (/ntos:config/vrf/ntos-lldp:lldp/interface) (line 1531)
libyang err : Interface name can only contain characters from [-A-Za-z0-9._@/]. (/ntos:config/vrf/ntos-lldp:lldp/interface/name) (line 1534)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-lldp:lldp/interface/enabled) (line 1536)
libyang err : List instance is missing its key "name". (/ntos:config/vrf/ntos-lldp:lldp/interface) (line 1538)
libyang err : Interface name can only contain characters from [-A-Za-z0-9._@/]. (/ntos:config/vrf/ntos-lldp:lldp/interface/name) (line 1541)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-lldp:lldp/interface/enabled) (line 1543)
libyang err : List instance is missing its key "name". (/ntos:config/vrf/ntos-lldp:lldp/interface) (line 1545)
libyang err : Interface name can only contain characters from [-A-Za-z0-9._@/]. (/ntos:config/vrf/ntos-lldp:lldp/interface/name) (line 1548)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-lldp:lldp/interface/enabled) (line 1550)
libyang err : List instance is missing its key "name". (/ntos:config/vrf/ntos-lldp:lldp/interface) (line 1552)
libyang err : Interface name can only contain characters from [-A-Za-z0-9._@/]. (/ntos:config/vrf/ntos-lldp:lldp/interface/name) (line 1555)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-lldp:lldp/interface/enabled) (line 1557)
libyang err : List instance is missing its key "name". (/ntos:config/vrf/ntos-lldp:lldp/interface) (line 1559)
libyang err : Interface name can only contain characters from [-A-Za-z0-9._@/]. (/ntos:config/vrf/ntos-lldp:lldp/interface/name) (line 1562)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-lldp:lldp/interface/enabled) (line 1564)
libyang err : List instance is missing its key "name". (/ntos:config/vrf/ntos-lldp:lldp/interface) (line 1566)
libyang err : Interface name can only contain characters from [-A-Za-z0-9._@/]. (/ntos:config/vrf/ntos-lldp:lldp/interface/name) (line 1569)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-lldp:lldp/interface/enabled) (line 1571)
libyang err : List instance is missing its key "name". (/ntos:config/vrf/ntos-lldp:lldp/interface) (line 1573)
libyang err : Interface name can only contain characters from [-A-Za-z0-9._@/]. (/ntos:config/vrf/ntos-lldp:lldp/interface/name) (line 1576)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-lldp:lldp/interface/enabled) (line 1578)
libyang err : List instance is missing its key "name". (/ntos:config/vrf/ntos-lldp:lldp/interface) (line 1580)
libyang err : Interface name can only contain characters from [-A-Za-z0-9._@/]. (/ntos:config/vrf/ntos-lldp:lldp/interface/name) (line 1583)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-lldp:lldp/interface/enabled) (line 1585)
libyang err : List instance is missing its key "name". (/ntos:config/vrf/ntos-lldp:lldp/interface) (line 1587)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-local-defend:local-defend/policy[name='deny_all
']/enabled) (line 1595)
libyang err : Invalid enumeration value "deny
". (/ntos:config/vrf/ntos-local-defend:local-defend/policy[name='deny_all
']/action) (line 1597)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-local-defend:local-defend/policy[name='deny_all
']/limit) (line 1599)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-local-defend:local-defend/policy[name='limit_local
']/enabled) (line 1609)
libyang err : Invalid enumeration value "permit
". (/ntos:config/vrf/ntos-local-defend:local-defend/policy[name='limit_local
']/action) (line 1611)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-local-defend:local-defend/policy[name='limit_local
']/limit) (line 1613)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-misn:misn/enabled) (line 1623)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-mllb:mllb/advanced-options/refresh-session) (line 1629)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-mllb:mllb/advanced-options/cache-disable) (line 1635)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-mllb:mllb/all-if-switch) (line 1641)
libyang err : Invalid enumeration value "CC20
". (/ntos:config/vrf/ntos-n2n:n2n-config/crypto) (line 1646)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-n2n:n2n-config/hang-side) (line 1652)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-n2n:n2n-config/flow-control/enabled) (line 1655)
libyang err : Invalid enumeration value "kbps
". (/ntos:config/vrf/ntos-n2n:n2n-config/flow-control/rate-unit) (line 1659)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-n2n:n2n-config/detect-policy/enabled) (line 1664)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-nat:port-mapping/enabled) (line 1675)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-nat:nat/enabled) (line 1680)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-nat:nat/sip-port-check/enabled) (line 1685)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-netconf-server:netconf-server/enabled) (line 1692)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-network-measure:network-measure/enabled) (line 1699)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-network-measure:network-measure/message-send-enabled) (line 1701)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-network-measure:network-measure/service/dhcp/enabled) (line 1705)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-network-measure:network-measure/service/dns/enabled) (line 1710)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-network-measure:network-measure/service/nat/enabled) (line 1717)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-network-measure:network-measure/service/ipsec/enabled) (line 1722)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-network-measure:network-measure/service/sslvpn/enabled) (line 1727)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-network-measure:network-measure/warning-config/link/high-load/enabled) (line 1736)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-network-measure:network-measure/warning-config/link/suspect-disconnected/enabled) (line 1745)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-network-measure:network-measure/warning-config/link/change-to-down/enabled) (line 1754)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-network-measure:network-measure/warning-config/app/change-to-poor/enabled) (line 1762)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-network-measure:network-measure/warning-config/user/wan-detect-failed/enabled) (line 1770)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-network-measure:network-measure/warning-config/user/lan-detect-failed/enabled) (line 1775)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-network-measure:network-measure/warning-config/user/change-to-poor/enabled) (line 1780)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-network-measure:network-measure/warning-config/dhcp-server/ip-conflict/enabled) (line 1788)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-network-measure:network-measure/warning-config/dhcp-server/high-load/enabled) (line 1793)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-network-measure:network-measure/warning-config/dns/unuseable/enabled) (line 1803)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-network-measure:network-measure/warning-config/nat/hit-fail/enabled) (line 1811)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-network-measure:network-measure/warning-config/nat/hit-miss/enabled) (line 1816)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-network-measure:network-measure/warning-config/ipsec/disconnected/enabled) (line 1824)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-network-measure:network-measure/warning-config/sslvpn/lost/enabled) (line 1834)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-network-measure:network-measure/warning-config/sslvpn/license/enabled) (line 1841)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-network-measure:network-measure/flow-limit/enabled) (line 1852)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-nfp:nfp/session/state-inspection/tcp) (line 1865)
libyang err : Invalid enumeration value "standard
". (/ntos:config/vrf/ntos-nfp:nfp/session/state-inspection/tcp-mode) (line 1867)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-nfp:nfp/session/state-inspection/icmp) (line 1869)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-nfp:nfp/tunnel-inspection/bridge/VLAN) (line 1877)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-nfp:nfp/tunnel-inspection/bridge/PPPoE) (line 1879)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-nfp:nfp/tunnel-inspection/bridge/GRE) (line 1881)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-nfp:nfp/tunnel-inspection/bridge/L2TPv2) (line 1883)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-ntp:ntp/enabled) (line 1892)
libyang err : Invalid union value "ntp.ntsc.ac.cn
" - no matching subtype found:
    libyang 2 - ipv4-address, version 1: Unsatisfied pattern - "ntp.ntsc.ac.cn
" does not conform to "(([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])(%[\p{N}\p{L}]+)?".
    libyang 2 - ipv6-address, version 1: Unsatisfied pattern - "ntp.ntsc.ac.cn
" does not conform to "((:|[0-9a-fA-F]{0,4}):)([0-9a-fA-F]{0,4}:){0,5}((([0-9a-fA-F]{0,4}:)?(:|[0-9a-fA-F]{0,4}))|(((25[0-5]|2[0-4][0-9]|[01]?[0-9]?[0-9])\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9]?[0-9])))(%[\p{N}\p{L}]+)?".
    libyang 2 - string, version 1: Unsatisfied pattern - "ntp.ntsc.ac.cn
" does not conform to "((([a-zA-Z0-9_]([a-zA-Z0-9\-_]){0,61})?[a-zA-Z0-9]\.)*([a-zA-Z0-9_]([a-zA-Z0-9\-_]){0,61})?[a-zA-Z0-9]\.?)|\.".
 (/ntos:config/vrf/ntos-ntp:ntp/server/address) (line 1895)
libyang err : Invalid enumeration value "SERVER
". (/ntos:config/vrf/ntos-ntp:ntp/server/association-type) (line 1899)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-ntp:ntp/server/iburst) (line 1901)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-ntp:ntp/server/prefer) (line 1903)
libyang err : List instance is missing its key "address". (/ntos:config/vrf/ntos-ntp:ntp/server) (line 1905)
libyang err : Invalid union value "ntp1.aliyun.com
" - no matching subtype found:
    libyang 2 - ipv4-address, version 1: Unsatisfied pattern - "ntp1.aliyun.com
" does not conform to "(([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])(%[\p{N}\p{L}]+)?".
    libyang 2 - ipv6-address, version 1: Unsatisfied pattern - "ntp1.aliyun.com
" does not conform to "((:|[0-9a-fA-F]{0,4}):)([0-9a-fA-F]{0,4}:){0,5}((([0-9a-fA-F]{0,4}:)?(:|[0-9a-fA-F]{0,4}))|(((25[0-5]|2[0-4][0-9]|[01]?[0-9]?[0-9])\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9]?[0-9])))(%[\p{N}\p{L}]+)?".
    libyang 2 - string, version 1: Unsatisfied pattern - "ntp1.aliyun.com
" does not conform to "((([a-zA-Z0-9_]([a-zA-Z0-9\-_]){0,61})?[a-zA-Z0-9]\.)*([a-zA-Z0-9_]([a-zA-Z0-9\-_]){0,61})?[a-zA-Z0-9]\.?)|\.".
 (/ntos:config/vrf/ntos-ntp:ntp/server/address) (line 1908)
libyang err : Invalid enumeration value "SERVER
". (/ntos:config/vrf/ntos-ntp:ntp/server/association-type) (line 1912)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-ntp:ntp/server/iburst) (line 1914)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-ntp:ntp/server/prefer) (line 1916)
libyang err : List instance is missing its key "address". (/ntos:config/vrf/ntos-ntp:ntp/server) (line 1918)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-portal:wba-portal/enabled) (line 1923)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-portal:wba-portal/ssl-enabled) (line 1927)
libyang err : Invalid enumeration value "no-redirection
". (/ntos:config/vrf/ntos-portal:wba-portal/redirection-mode) (line 1929)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-pppoe:pppoe/multi-dial/enabled) (line 1965)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-pppoe-server:pppoe-server/enabled) (line 1972)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-replacement-messages:replacement-messages/management/cache-enable-status) (line 1978)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-reputation-center:reputation-center/enabled) (line 1985)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-security-defend:security-defend/basic-protocol-control-enabled) (line 1990)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-security-defend:security-defend/enabled) (line 1992)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-session-limit:session-limit/pps-limit/enabled) (line 2028)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-session-limit:session-limit/sps-limit/enabled) (line 2035)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-session-limit:session-limit/total-session/enabled) (line 2042)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-ssh-server:ssh-server/enabled) (line 2049)
libyang err : Invalid enumeration value "check-interface-and-subnet
". (/ntos:config/vrf/ntos-system:network-stack/ipv4/arp-ignore) (line 2077)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-threat-intelligence:threat-intelligence/management/enable-status) (line 2085)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-threat-intelligence:threat-intelligence/management/enable-ai) (line 2087)
libyang err : Unsatisfied length - string "00:00:00
" length is not allowed. (/ntos:config/vrf/ntos-time-range:time-range/range[name='any
']/period/start) (line 2100)
libyang err : Unsatisfied length - string "23:59:59
" length is not allowed. (/ntos:config/vrf/ntos-time-range:time-range/range[name='any
']/period/end) (line 2102)
libyang err : Invalid enumeration value "sun
". (/ntos:config/vrf/ntos-time-range:time-range/range[name='any
']/period/weekday/key) (line 2105)
libyang err : List instance is missing its key "key". (/ntos:config/vrf/ntos-time-range:time-range/range[name='any
']/period/weekday) (line 2107)
libyang err : Invalid enumeration value "mon
". (/ntos:config/vrf/ntos-time-range:time-range/range[name='any
']/period/weekday/key) (line 2110)
libyang err : List instance is missing its key "key". (/ntos:config/vrf/ntos-time-range:time-range/range[name='any
']/period/weekday) (line 2112)
libyang err : Invalid enumeration value "tue
". (/ntos:config/vrf/ntos-time-range:time-range/range[name='any
']/period/weekday/key) (line 2115)
libyang err : List instance is missing its key "key". (/ntos:config/vrf/ntos-time-range:time-range/range[name='any
']/period/weekday) (line 2117)
libyang err : Invalid enumeration value "wed
". (/ntos:config/vrf/ntos-time-range:time-range/range[name='any
']/period/weekday/key) (line 2120)
libyang err : List instance is missing its key "key". (/ntos:config/vrf/ntos-time-range:time-range/range[name='any
']/period/weekday) (line 2122)
libyang err : Invalid enumeration value "thu
". (/ntos:config/vrf/ntos-time-range:time-range/range[name='any
']/period/weekday/key) (line 2125)
libyang err : List instance is missing its key "key". (/ntos:config/vrf/ntos-time-range:time-range/range[name='any
']/period/weekday) (line 2127)
libyang err : Invalid enumeration value "fri
". (/ntos:config/vrf/ntos-time-range:time-range/range[name='any
']/period/weekday/key) (line 2130)
libyang err : List instance is missing its key "key". (/ntos:config/vrf/ntos-time-range:time-range/range[name='any
']/period/weekday) (line 2132)
libyang err : Invalid enumeration value "sat
". (/ntos:config/vrf/ntos-time-range:time-range/range[name='any
']/period/weekday/key) (line 2135)
libyang err : List instance is missing its key "key". (/ntos:config/vrf/ntos-time-range:time-range/range[name='any
']/period/weekday) (line 2137)
libyang err : List instance is missing its key "start". (/ntos:config/vrf/ntos-time-range:time-range/range[name='any
']/period) (line 2139)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-traffic-analy:traffic-analy/enabled) (line 2146)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-upnp-proxy:upnp-proxy/enabled) (line 2151)
libyang err : Invalid enumeration value "ip
". (/ntos:config/vrf/ntos-upnp-proxy:upnp-proxy/bind-rule) (line 2153)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-upnp-proxy:upnp-proxy/advance/automatic-enrollment/enabled) (line 2157)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-upnp-proxy:upnp-proxy/advance/terminal-authorization/enabled) (line 2166)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-upnp-proxy:upnp-proxy/advance/linkage-service/enabled) (line 2171)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-upnp-proxy:upnp-proxy/advance/scheduled-offline/enabled) (line 2181)
libyang err : Unsatisfied length - string "00:00
" length is not allowed. (/ntos:config/vrf/ntos-upnp-proxy:upnp-proxy/advance/scheduled-offline/time) (line 2183)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-upnp-proxy:upnp-proxy/reserve/unicast/enabled) (line 2203)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-upnp-proxy:upnp-proxy/reserve/web-url-compatible/enabled) (line 2208)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-upnp-proxy:upnp-proxy/reserve/map-cover-mode/enabled) (line 2213)
libyang err : Invalid enumeration value "portal
". (/ntos:config/vrf/ntos-webauth:webauth/authentication-options/portal-authentication/portal-group[name='cportal
']/protocol) (line 2229)
libyang err : Invalid enumeration value "plugin
". (/ntos:config/vrf/ntos-webauth:webauth/single-sign-on/ad/method) (line 2239)
libyang err : Invalid enumeration value "ac-connect
". (/ntos:config/vrf/ntos-wlan:wlan/web-ac/topology) (line 2249)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-wlan:wlan/diag/enabled) (line 2254)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-wlan:wlan/ac-controller/acctrl-trap/acap-microap-ctrl/enabled) (line 2263)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-wlan:wlan/ac-controller/acctrl-trap/acap-updown-ctrl/enabled) (line 2268)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-wlan:wlan/ac-controller/acctrl-trap/acap-joinfail-ctrl/enabled) (line 2273)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-wlan:wlan/ac-controller/acctrl-trap/acap-decryeroreport-ctrl/enabled) (line 2278)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-wlan:wlan/ac-controller/acctrl-trap/acap-imageupdt-ctrl/enabled) (line 2283)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-wlan:wlan/ac-controller/acctrl-trap/acap-timestamp-ctrl/enabled) (line 2288)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-wlan:wlan/ac-controller/acctrl-trap/acsta-oper-ctrl/enabled) (line 2293)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-wlan:wlan/ac-controller/ap-auth/serial/enabled) (line 2301)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-wlan:wlan/ac-controller/ap-auth/password/enabled) (line 2306)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-wlan:wlan/ac-controller/ap-auth/certificate/enabled) (line 2311)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-wlan:wlan/ac-controller/ap-priority/enabled) (line 2318)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-wlan:wlan/ac-controller/bind-ap-mac/enabled) (line 2323)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-wlan:wlan/ac-controller/sta-balance/num-limit/enabled) (line 2337)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-wlan:wlan/ac-controller/sta-blacklist/enabled) (line 2344)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-wlan:wlan/ac-controller/black-white-list/blacklist/enabled) (line 2350)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-wlan:wlan/ac-controller/black-white-list/whitelist/enabled) (line 2355)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-wlan:wlan/ac-controller/ac-control/enabled) (line 2362)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-wlan:wlan/ac-controller/ap-image/auto-upgrade/enabled) (line 2368)
libyang err : Invalid boolean value "true
". (/ntos:config/vrf/ntos-wlan:wlan/ac-controller/capwap/dtls/enabled) (line 2376)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-wlan:wlan/wids/countermeasures/enabled) (line 2386)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-wlan:wlan/wids/countermeasures/channel-match) (line 2390)
libyang err : Invalid boolean value "false
". (/ntos:config/vrf/ntos-dns:dns/proxy/enabled) (line 2400)
libyang err : List instance is missing its key "name". (/ntos:config/vrf) (line 2406)
libyang err : Invalid union value "default
" - no matching subtype found:
    libyang 2 - enumeration, version 1: Invalid enumeration value "default
".
    libyang 2 - string, version 1: Invalid core list format. Example: '1,4-7,10-12'
 (/ntos:config/ntos-system:system/cp-mask) (line 2409)
libyang err : Invalid boolean value "true
". (/ntos:config/ntos-system:system/nfp/autoperf/enabled) (line 2413)
libyang err : Invalid boolean value "false
". (/ntos:config/ntos-system:system/network-stack/bridge/call-ipv4-filtering) (line 2421)
libyang err : Invalid boolean value "false
". (/ntos:config/ntos-system:system/network-stack/bridge/call-ipv6-filtering) (line 2423)
libyang err : Invalid boolean value "true
". (/ntos:config/ntos-system:system/network-stack/ipv4/forwarding) (line 2435)
libyang err : Invalid boolean value "true
". (/ntos:config/ntos-system:system/network-stack/ipv4/send-redirects) (line 2437)
libyang err : Invalid boolean value "false
". (/ntos:config/ntos-system:system/network-stack/ipv4/accept-redirects) (line 2439)
libyang err : Invalid boolean value "false
". (/ntos:config/ntos-system:system/network-stack/ipv4/accept-source-route) (line 2441)
libyang err : Invalid enumeration value "any
". (/ntos:config/ntos-system:system/network-stack/ipv4/arp-announce) (line 2443)
libyang err : Invalid boolean value "false
". (/ntos:config/ntos-system:system/network-stack/ipv4/arp-filter) (line 2445)
libyang err : Invalid enumeration value "any
". (/ntos:config/ntos-system:system/network-stack/ipv4/arp-ignore) (line 2447)
libyang err : Invalid boolean value "false
". (/ntos:config/ntos-system:system/network-stack/ipv4/log-invalid-addresses) (line 2449)
libyang err : Invalid boolean value "true
". (/ntos:config/ntos-system:system/network-stack/ipv6/forwarding) (line 2454)
libyang err : Invalid boolean value "true
". (/ntos:config/ntos-system:system/network-stack/ipv6/autoconfiguration) (line 2456)
libyang err : Invalid enumeration value "never
". (/ntos:config/ntos-system:system/network-stack/ipv6/accept-router-advert) (line 2458)
libyang err : Invalid boolean value "false
". (/ntos:config/ntos-system:system/network-stack/ipv6/accept-redirects) (line 2460)
libyang err : Invalid boolean value "false
". (/ntos:config/ntos-system:system/network-stack/ipv6/accept-source-route) (line 2462)
libyang err : Invalid enumeration value "never
". (/ntos:config/ntos-system:system/network-stack/ipv6/use-temporary-addresses) (line 2466)
libyang err : Invalid union value "Asia/Shanghai
" - no matching subtype found:
    libyang 2 - enumeration, version 1: Invalid enumeration value "Asia/Shanghai
".
    libyang 2 - enumeration, version 1: Invalid enumeration value "Asia/Shanghai
".
    libyang 2 - enumeration, version 1: Invalid enumeration value "Asia/Shanghai
".
 (/ntos:config/ntos-system:system/timezone) (line 2472)
libyang err : Invalid boolean value "false
". (/ntos:config/ntos-system:system/scheduled-restart/enabled) (line 2475)
libyang err : Invalid boolean value "false
". (/ntos:config/ntos-system:system/scheduled-restart/once) (line 2481)
libyang err : Invalid boolean value "true
". (/ntos:config/ntos-system:system/ntos-anti-virus:anti-virus-file-exception/enabled) (line 2486)
libyang err : Unsatisfied pattern - "admin
" does not conform to "[a-zA-Z]+[a-zA-Z0-9_]*". (/ntos:config/ntos-system:system/ntos-auth:auth/user/name) (line 2492)
libyang err : Invalid enumeration value "admin
". (/ntos:config/ntos-system:system/ntos-auth:auth/user/role) (line 2494)
libyang err : List instance is missing its key "name". (/ntos:config/ntos-system:system/ntos-auth:auth/user) (line 2498)
libyang err : Unsatisfied pattern - "securityadmin
" does not conform to "[a-zA-Z]+[a-zA-Z0-9_]*". (/ntos:config/ntos-system:system/ntos-auth:auth/user/name) (line 2501)
libyang err : Invalid enumeration value "admin
". (/ntos:config/ntos-system:system/ntos-auth:auth/user/role) (line 2503)
libyang err : Invalid boolean value "true
". (/ntos:config/ntos-system:system/ntos-auth:auth/user/lock) (line 2505)
libyang err : List instance is missing its key "name". (/ntos:config/ntos-system:system/ntos-auth:auth/user) (line 2507)
libyang err : Unsatisfied pattern - "useradmin
" does not conform to "[a-zA-Z]+[a-zA-Z0-9_]*". (/ntos:config/ntos-system:system/ntos-auth:auth/user/name) (line 2510)
libyang err : Invalid enumeration value "admin
". (/ntos:config/ntos-system:system/ntos-auth:auth/user/role) (line 2512)
libyang err : Invalid boolean value "true
". (/ntos:config/ntos-system:system/ntos-auth:auth/user/lock) (line 2514)
libyang err : List instance is missing its key "name". (/ntos:config/ntos-system:system/ntos-auth:auth/user) (line 2516)
libyang err : Unsatisfied pattern - "auditadmin
" does not conform to "[a-zA-Z]+[a-zA-Z0-9_]*". (/ntos:config/ntos-system:system/ntos-auth:auth/user/name) (line 2519)
libyang err : Invalid enumeration value "admin
". (/ntos:config/ntos-system:system/ntos-auth:auth/user/role) (line 2521)
libyang err : Invalid boolean value "true
". (/ntos:config/ntos-system:system/ntos-auth:auth/user/lock) (line 2523)
libyang err : List instance is missing its key "name". (/ntos:config/ntos-system:system/ntos-auth:auth/user) (line 2525)
libyang err : Invalid boolean value "true
". (/ntos:config/ntos-system:system/ntos-cloud-service:wis-service/enabled) (line 2530)
libyang err : Invalid boolean value "true
". (/ntos:config/ntos-system:system/ntos-cloud-service:macc-service/enabled) (line 2535)
libyang err : Invalid boolean value "true
". (/ntos:config/ntos-system:system/ntos-cloud-service:security-cloud-service/enabled) (line 2540)
libyang err : Invalid boolean value "true
". (/ntos:config/ntos-system:system/ntos-collect:collect/enabled) (line 2555)
libyang err : Invalid boolean value "false
". (/ntos:config/ntos-system:system/ntos-collect:collect/statistics-enabled) (line 2563)
libyang err : Invalid boolean value "true
". (/ntos:config/ntos-system:system/ntos-collect:collect/record-stats-enabled) (line 2565)
libyang err : Invalid boolean value "true
". (/ntos:config/ntos-system:system/ntos-collect:collect/flow-log-enabled) (line 2567)
libyang err : Invalid enumeration value "English
". (/ntos:config/ntos-system:system/ntos-collect:collect/log-language) (line 2569)
libyang err : Invalid enumeration value "hash-default
". (/ntos:config/ntos-system:system/ntos-dataplane:dataplane/hash/type) (line 2575)
libyang err : Invalid boolean value "true
". (/ntos:config/ntos-system:system/ntos-local-defend:local-defend/enabled) (line 2591)
libyang err : Invalid boolean value "true
". (/ntos:config/ntos-system:system/ntos-local-defend:local-defend/host-hardening/enabled) (line 2594)
libyang err : Invalid boolean value "false
". (/ntos:config/ntos-system:system/ntos-local-defend:local-defend/host-hardening/injection-prevention/enabled) (line 2597)
libyang err : Invalid enumeration value "block
". (/ntos:config/ntos-system:system/ntos-local-defend:local-defend/host-hardening/injection-prevention/action) (line 2599)
libyang err : Invalid boolean value "false
". (/ntos:config/ntos-system:system/ntos-local-defend:local-defend/arp-monitor/enabled) (line 2606)
libyang err : Invalid boolean value "false
". (/ntos:config/ntos-system:system/ntos-network-monitor:network-monitor/enabled) (line 2636)
libyang err : Invalid enumeration value "snmp
". (/ntos:config/ntos-system:system/ntos-network-monitor:network-monitor/wireless-gather-type) (line 2638)
libyang err : Invalid boolean value "false
". (/ntos:config/ntos-system:system/ntos-user-experience-plan:usr-exp-plan/no-prompt) (line 2643)
libyang err : Invalid boolean value "false
". (/ntos:config/ntos-system:system/ntos-user-experience-plan:usr-exp-plan/enabled) (line 2645)
libyang err : Invalid boolean value "true
". (/ntos:config/ntos-system:system/ntos-web-server:web-server/enabled) (line 2650)
libyang err : Invalid boolean value "true
". (/ntos:config/ntos-system:system/ntos-web-server:web-server/http-enabled) (line 2654)
libyang err : Invalid boolean value "true
". (/ntos:config/ntos-system:system/ntos-web-server:web-server/smart-http-enabled) (line 2656)
YANGLINT[E]: Failed to parse input data file "/tmp/tmpnk821bkr.xml".

================================================================================
