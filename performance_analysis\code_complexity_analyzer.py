"""
FortiGate解析器代码复杂度分析
分析解析器代码中的性能瓶颈和算法复杂度问题
"""

import ast
import re
import time
from typing import Dict, List, Tuple

class CodeComplexityAnalyzer:
    """代码复杂度分析器"""
    
    def __init__(self, parser_file_path: str):
        self.parser_file_path = parser_file_path
        with open(parser_file_path, 'r', encoding='utf-8') as f:
            self.source_code = f.read()
        self.lines = self.source_code.split('\n')
    
    def analyze_main_parsing_loop(self) -> Dict:
        """分析主解析循环的复杂度"""
        
        # 找到主解析循环
        loop_start = -1
        loop_end = -1
        
        for i, line in enumerate(self.lines):
            if 'while i < len(lines)' in line:
                loop_start = i
                break
        
        if loop_start == -1:
            return {'error': '未找到主解析循环'}
        
        # 找到循环结束
        indent_level = len(self.lines[loop_start]) - len(self.lines[loop_start].lstrip())
        for i in range(loop_start + 1, len(self.lines)):
            line = self.lines[i]
            if line.strip() and len(line) - len(line.lstrip()) <= indent_level:
                loop_end = i
                break
        
        if loop_end == -1:
            loop_end = len(self.lines)
        
        loop_lines = self.lines[loop_start:loop_end]
        
        # 分析循环内的复杂度
        complexity_metrics = {
            'total_lines': len(loop_lines),
            'elif_chains': 0,
            'nested_loops': 0,
            'regex_operations': 0,
            'string_operations': 0,
            'function_calls': 0,
            'conditional_branches': 0
        }
        
        elif_count = 0
        for line in loop_lines:
            line_stripped = line.strip()
            
            # 统计elif链
            if line_stripped.startswith('elif'):
                elif_count += 1
            elif line_stripped.startswith('if') and elif_count > 0:
                complexity_metrics['elif_chains'] = max(complexity_metrics['elif_chains'], elif_count)
                elif_count = 0
            
            # 统计嵌套循环
            if 'for ' in line_stripped or 'while ' in line_stripped:
                complexity_metrics['nested_loops'] += 1
            
            # 统计正则表达式操作
            if 're.' in line_stripped or '.match(' in line_stripped or '.search(' in line_stripped:
                complexity_metrics['regex_operations'] += 1
            
            # 统计字符串操作
            string_ops = ['.strip()', '.split()', '.startswith()', '.lower()', '.replace()', '.find(']
            for op in string_ops:
                complexity_metrics['string_operations'] += line_stripped.count(op)
            
            # 统计函数调用
            if '(' in line_stripped and ')' in line_stripped:
                complexity_metrics['function_calls'] += line_stripped.count('(')
            
            # 统计条件分支
            if line_stripped.startswith(('if ', 'elif ', 'else:')):
                complexity_metrics['conditional_branches'] += 1
        
        # 记录最后的elif链
        if elif_count > 0:
            complexity_metrics['elif_chains'] = max(complexity_metrics['elif_chains'], elif_count)
        
        return {
            'loop_start_line': loop_start + 1,
            'loop_end_line': loop_end + 1,
            'loop_length': loop_end - loop_start,
            'complexity_metrics': complexity_metrics,
            'estimated_time_complexity': self._estimate_time_complexity(complexity_metrics)
        }
    
    def _estimate_time_complexity(self, metrics: Dict) -> Dict:
        """估算时间复杂度"""
        
        # 基于代码结构估算复杂度
        base_complexity = 'O(n)'  # 基本的单次遍历
        
        # 如果有大量的elif链，复杂度会增加
        if metrics['elif_chains'] > 20:
            base_complexity = 'O(n * m)'  # n=行数, m=elif链长度
        
        # 如果有嵌套循环，复杂度会显著增加
        if metrics['nested_loops'] > 0:
            base_complexity = 'O(n²)'
        
        # 估算每行的平均操作数
        ops_per_line = (
            metrics['string_operations'] + 
            metrics['regex_operations'] * 10 +  # 正则表达式操作更昂贵
            metrics['function_calls'] * 2 +
            metrics['conditional_branches']
        ) / max(metrics['total_lines'], 1)
        
        return {
            'theoretical_complexity': base_complexity,
            'operations_per_line': ops_per_line,
            'bottleneck_factor': metrics['elif_chains'] * metrics['regex_operations']
        }
    
    def analyze_config_section_detection(self) -> Dict:
        """分析配置段落检测逻辑的效率"""
        
        # 查找所有的配置段落检测代码
        detection_patterns = []
        
        for i, line in enumerate(self.lines):
            if 'elif line.lower().startswith("config' in line:
                detection_patterns.append({
                    'line_number': i + 1,
                    'pattern': line.strip(),
                    'config_type': self._extract_config_type(line)
                })
        
        # 分析检测效率
        total_patterns = len(detection_patterns)
        
        # 估算每行需要检查的模式数量
        avg_checks_per_line = total_patterns / 2  # 平均情况下检查一半的模式
        
        return {
            'total_detection_patterns': total_patterns,
            'detection_patterns': detection_patterns,
            'avg_checks_per_line': avg_checks_per_line,
            'optimization_potential': {
                'current_approach': 'Sequential if-elif chain',
                'time_complexity': f'O(n * {total_patterns})',
                'suggested_approach': 'Dictionary lookup or trie structure',
                'improved_complexity': 'O(n * 1)'
            }
        }
    
    def _extract_config_type(self, line: str) -> str:
        """从检测行中提取配置类型"""
        match = re.search(r'"config\s+([^"]+)"', line)
        if match:
            return match.group(1)
        return 'unknown'
    
    def analyze_string_processing_overhead(self) -> Dict:
        """分析字符串处理开销"""
        
        string_operations = {
            '.strip()': 0,
            '.lower()': 0,
            '.split()': 0,
            '.startswith()': 0,
            '.replace()': 0,
            '.find(': 0,
            '.join(': 0
        }
        
        total_string_ops = 0
        
        for line in self.lines:
            for op in string_operations:
                count = line.count(op)
                string_operations[op] += count
                total_string_ops += count
        
        # 估算字符串处理的性能影响
        # 假设处理104,387行，每行平均进行的字符串操作
        lines_to_process = 104387
        estimated_total_ops = total_string_ops * lines_to_process / len(self.lines)
        
        return {
            'string_operations_breakdown': string_operations,
            'total_string_ops_in_code': total_string_ops,
            'estimated_ops_for_large_file': estimated_total_ops,
            'performance_impact': {
                'strip_operations': string_operations['.strip()'] * lines_to_process,
                'lower_operations': string_operations['.lower()'] * lines_to_process,
                'startswith_operations': string_operations['.startswith()'] * lines_to_process,
                'estimated_time_ms': estimated_total_ops * 0.001  # 假设每个操作1微秒
            }
        }
    
    def analyze_regex_usage(self) -> Dict:
        """分析正则表达式使用情况"""
        
        regex_patterns = []
        
        for i, line in enumerate(self.lines):
            # 查找正则表达式相关代码
            if 're.' in line or 'regex' in line.lower():
                regex_patterns.append({
                    'line_number': i + 1,
                    'code': line.strip(),
                    'type': self._classify_regex_usage(line)
                })
        
        # 分析正则表达式的性能影响
        compiled_regex_count = sum(1 for p in regex_patterns if 'compile' in p['code'])
        inline_regex_count = len(regex_patterns) - compiled_regex_count
        
        return {
            'total_regex_usage': len(regex_patterns),
            'compiled_regex': compiled_regex_count,
            'inline_regex': inline_regex_count,
            'regex_patterns': regex_patterns,
            'performance_analysis': {
                'compiled_regex_efficiency': 'High' if compiled_regex_count > inline_regex_count else 'Low',
                'optimization_needed': inline_regex_count > 5,
                'estimated_performance_gain': f'{inline_regex_count * 10}% if compiled'
            }
        }
    
    def _classify_regex_usage(self, line: str) -> str:
        """分类正则表达式使用类型"""
        if 'compile' in line:
            return 'compiled'
        elif 'match' in line:
            return 'match'
        elif 'search' in line:
            return 'search'
        elif 'findall' in line:
            return 'findall'
        else:
            return 'other'
    
    def generate_performance_report(self) -> Dict:
        """生成综合性能报告"""
        
        print("分析FortiGate解析器代码复杂度...")
        
        # 执行各项分析
        loop_analysis = self.analyze_main_parsing_loop()
        section_detection = self.analyze_config_section_detection()
        string_processing = self.analyze_string_processing_overhead()
        regex_analysis = self.analyze_regex_usage()
        
        # 计算总体性能影响
        total_bottleneck_score = (
            section_detection['avg_checks_per_line'] * 10 +  # 配置检测开销
            string_processing['estimated_ops_for_large_file'] / 1000 +  # 字符串处理开销
            regex_analysis['inline_regex'] * 5  # 未编译正则表达式开销
        )
        
        return {
            'file_analysis': {
                'file_path': self.parser_file_path,
                'total_lines': len(self.lines),
                'analysis_timestamp': time.time()
            },
            'main_loop_analysis': loop_analysis,
            'section_detection_analysis': section_detection,
            'string_processing_analysis': string_processing,
            'regex_analysis': regex_analysis,
            'overall_performance': {
                'bottleneck_score': total_bottleneck_score,
                'primary_bottlenecks': [
                    'Sequential config section detection',
                    'Excessive string operations per line',
                    'Uncompiled regex patterns'
                ],
                'optimization_priority': [
                    'Replace if-elif chain with dictionary lookup',
                    'Reduce redundant string operations',
                    'Pre-compile regex patterns',
                    'Implement section-level skipping'
                ]
            }
        }

def main():
    """主函数"""
    parser_file = "engine/parsers/fortigate_parser.py"
    
    print("=" * 80)
    print("FortiGate解析器代码复杂度分析")
    print("=" * 80)
    
    analyzer = CodeComplexityAnalyzer(parser_file)
    report = analyzer.generate_performance_report()
    
    # 输出分析结果
    print(f"\n📁 文件信息:")
    file_info = report['file_analysis']
    print(f"   文件: {file_info['file_path']}")
    print(f"   代码行数: {file_info['total_lines']:,}")
    
    print(f"\n🔄 主解析循环分析:")
    loop_info = report['main_loop_analysis']
    if 'error' not in loop_info:
        print(f"   循环位置: 第{loop_info['loop_start_line']}-{loop_info['loop_end_line']}行")
        print(f"   循环长度: {loop_info['loop_length']} 行")
        metrics = loop_info['complexity_metrics']
        print(f"   elif链长度: {metrics['elif_chains']}")
        print(f"   字符串操作: {metrics['string_operations']:,}")
        print(f"   正则操作: {metrics['regex_operations']}")
        print(f"   条件分支: {metrics['conditional_branches']}")
        
        complexity = loop_info['estimated_time_complexity']
        print(f"   理论复杂度: {complexity['theoretical_complexity']}")
        print(f"   每行操作数: {complexity['operations_per_line']:.1f}")
    
    print(f"\n🎯 配置段落检测分析:")
    detection = report['section_detection_analysis']
    print(f"   检测模式数量: {detection['total_detection_patterns']}")
    print(f"   平均每行检查: {detection['avg_checks_per_line']:.1f} 个模式")
    print(f"   当前方法: {detection['optimization_potential']['current_approach']}")
    print(f"   当前复杂度: {detection['optimization_potential']['time_complexity']}")
    print(f"   建议方法: {detection['optimization_potential']['suggested_approach']}")
    
    print(f"\n📝 字符串处理分析:")
    string_proc = report['string_processing_analysis']
    print(f"   代码中字符串操作: {string_proc['total_string_ops_in_code']:,}")
    print(f"   大文件预估操作: {string_proc['estimated_ops_for_large_file']:,.0f}")
    impact = string_proc['performance_impact']
    print(f"   预估处理时间: {impact['estimated_time_ms']:.1f} 毫秒")
    
    print(f"\n🔍 正则表达式分析:")
    regex_info = report['regex_analysis']
    print(f"   正则表达式使用: {regex_info['total_regex_usage']} 处")
    print(f"   已编译正则: {regex_info['compiled_regex']}")
    print(f"   内联正则: {regex_info['inline_regex']}")
    perf = regex_info['performance_analysis']
    print(f"   效率评级: {perf['compiled_regex_efficiency']}")
    if perf['optimization_needed']:
        print(f"   优化潜力: {perf['estimated_performance_gain']}")
    
    print(f"\n🚨 性能瓶颈总结:")
    overall = report['overall_performance']
    print(f"   瓶颈评分: {overall['bottleneck_score']:.1f}")
    print(f"   主要瓶颈:")
    for bottleneck in overall['primary_bottlenecks']:
        print(f"     • {bottleneck}")
    
    print(f"\n💡 优化优先级:")
    for i, optimization in enumerate(overall['optimization_priority'], 1):
        print(f"   {i}. {optimization}")

if __name__ == "__main__":
    main()
