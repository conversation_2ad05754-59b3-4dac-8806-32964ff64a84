"""
FortiGate配置文件详细段落分析
分析哪些配置段落被识别为"未知"，以及它们的处理开销
"""

import re
import time
from collections import Counter, defaultdict
from typing import Dict, List, <PERSON><PERSON>

def analyze_config_sections(config_file_path: str) -> Dict:
    """详细分析配置文件中的所有段落"""
    
    print(f"分析配置文件: {config_file_path}")
    
    with open(config_file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    print(f"总行数: {len(lines):,}")
    
    # 解析器支持的配置段落（从fortigate_parser.py提取）
    supported_patterns = [
        'system interface',
        'router static',
        'router static6', 
        'system zone',
        'firewall policy',
        'firewall vip',
        'firewall ippool',
        'vpn ipsec phase1-interface',
        'vpn ipsec phase2-interface',
        'vpn ssl settings',
        'vpn ssl web portal',
        'system settings',
        'system global',
        'system dhcp server',
        'system dns',
        'user local',
        'user group',
        'user setting',
        'log setting',
        'log syslogd',
        'log fortianalyzer',
        'log memory filter',
        'log disk filter',
        'firewall schedule group',
        'firewall schedule onetime',
        'firewall schedule recurring',
        'firewall schedule',
        'firewall address',
        'firewall addrgrp',
        'firewall service group',
        'firewall service custom',
        'system accprofile',
        'system np6',
        'system custom-language',
        'system admin',
        'system sso-admin',
        'system sso-forticloud-admin',
        'system ha',
        'system replacemsg-image'
    ]
    
    # 分析所有配置段落
    config_sections = []
    current_section = None
    section_start_line = 0
    
    config_pattern = re.compile(r'^config\s+(.+)$')
    
    for i, line in enumerate(lines):
        line = line.strip()
        
        # 检测配置段落开始
        match = config_pattern.match(line)
        if match:
            # 如果有前一个段落，记录其结束
            if current_section:
                config_sections.append({
                    'name': current_section,
                    'start_line': section_start_line,
                    'end_line': i - 1,
                    'line_count': i - section_start_line
                })
            
            # 开始新段落
            current_section = match.group(1)
            section_start_line = i
        
        # 检测段落结束
        elif line.lower() == 'end' and current_section:
            config_sections.append({
                'name': current_section,
                'start_line': section_start_line,
                'end_line': i,
                'line_count': i - section_start_line + 1
            })
            current_section = None
    
    # 处理最后一个段落
    if current_section:
        config_sections.append({
            'name': current_section,
            'start_line': section_start_line,
            'end_line': len(lines) - 1,
            'line_count': len(lines) - section_start_line
        })
    
    print(f"发现配置段落: {len(config_sections)} 个")
    
    # 分类段落
    supported_sections = []
    unknown_sections = []
    
    for section in config_sections:
        is_supported = False
        
        for pattern in supported_patterns:
            if section['name'].startswith(pattern):
                section['handler'] = pattern
                section['supported'] = True
                supported_sections.append(section)
                is_supported = True
                break
        
        if not is_supported:
            section['supported'] = False
            unknown_sections.append(section)
    
    # 统计分析
    section_stats = Counter(section['name'] for section in config_sections)
    supported_stats = Counter(section['name'] for section in supported_sections)
    unknown_stats = Counter(section['name'] for section in unknown_sections)
    
    # 计算行数统计
    total_lines = sum(section['line_count'] for section in config_sections)
    supported_lines = sum(section['line_count'] for section in supported_sections)
    unknown_lines = sum(section['line_count'] for section in unknown_sections)
    
    return {
        'total_sections': len(config_sections),
        'supported_sections_count': len(supported_sections),
        'unknown_sections_count': len(unknown_sections),
        'total_lines': total_lines,
        'supported_lines': supported_lines,
        'unknown_lines': unknown_lines,
        'section_stats': dict(section_stats),
        'supported_stats': dict(supported_stats),
        'unknown_stats': dict(unknown_stats),
        'all_sections': config_sections,
        'supported_sections': supported_sections,
        'unknown_sections': unknown_sections
    }

def analyze_parsing_bottlenecks(analysis_result: Dict) -> Dict:
    """分析解析瓶颈"""
    
    # 计算未知段落的处理开销
    unknown_sections = analysis_result['unknown_sections']
    unknown_stats = analysis_result['unknown_stats']
    
    # 按行数排序，找出最大的未知段落
    large_unknown_sections = []
    for section in unknown_sections:
        if section['line_count'] > 100:  # 超过100行的大段落
            large_unknown_sections.append(section)
    
    large_unknown_sections.sort(key=lambda x: x['line_count'], reverse=True)
    
    # 分析最频繁的未知段落类型
    frequent_unknown = []
    for section_name, count in unknown_stats.items():
        if count > 5:  # 出现超过5次的段落类型
            total_lines = sum(s['line_count'] for s in unknown_sections if s['name'] == section_name)
            frequent_unknown.append({
                'name': section_name,
                'count': count,
                'total_lines': total_lines,
                'avg_lines_per_section': total_lines / count
            })
    
    frequent_unknown.sort(key=lambda x: x['total_lines'], reverse=True)
    
    # 估算处理时间
    # 假设：支持的段落处理速度 = 1000行/秒，未知段落 = 100行/秒
    supported_time = analysis_result['supported_lines'] / 1000
    unknown_time = analysis_result['unknown_lines'] / 100
    total_estimated_time = supported_time + unknown_time
    
    return {
        'large_unknown_sections': large_unknown_sections[:10],  # 前10个最大的未知段落
        'frequent_unknown_sections': frequent_unknown[:10],     # 前10个最频繁的未知段落
        'time_estimation': {
            'supported_sections_time': supported_time,
            'unknown_sections_time': unknown_time,
            'total_time': total_estimated_time,
            'unknown_time_percentage': (unknown_time / total_estimated_time) * 100
        },
        'optimization_potential': {
            'if_skip_unknown': supported_time,  # 如果跳过所有未知段落的时间
            'time_savings': unknown_time,
            'speed_improvement': (total_estimated_time / supported_time) if supported_time > 0 else 0
        }
    }

def generate_optimization_plan(analysis_result: Dict, bottleneck_analysis: Dict) -> List[Dict]:
    """生成具体的优化计划"""
    
    optimizations = []
    
    # 1. 批量跳过未知段落
    unknown_time_percentage = bottleneck_analysis['time_estimation']['unknown_time_percentage']
    if unknown_time_percentage > 50:
        optimizations.append({
            'priority': 1,
            'name': '实现未知段落批量跳过',
            'description': f'跳过{analysis_result["unknown_sections_count"]}个未知段落({analysis_result["unknown_lines"]:,}行)',
            'expected_time_saving': bottleneck_analysis['time_estimation']['unknown_sections_time'],
            'implementation': [
                '预编译支持的配置段落模式',
                '实现快速段落跳过逻辑',
                '添加段落统计而非详细解析'
            ]
        })
    
    # 2. 优化大型未知段落处理
    large_sections = bottleneck_analysis['large_unknown_sections']
    if large_sections:
        total_large_lines = sum(s['line_count'] for s in large_sections)
        optimizations.append({
            'priority': 2,
            'name': '优化大型未知段落处理',
            'description': f'优化{len(large_sections)}个大型未知段落({total_large_lines:,}行)',
            'expected_time_saving': total_large_lines / 100 * 0.9,  # 90%时间节省
            'implementation': [
                '实现段落级别的跳过',
                '使用正则表达式快速定位段落边界',
                '避免逐行处理大型未知段落'
            ]
        })
    
    # 3. 优化频繁未知段落
    frequent_sections = bottleneck_analysis['frequent_unknown_sections']
    if frequent_sections:
        top_frequent = frequent_sections[0]
        optimizations.append({
            'priority': 3,
            'name': f'添加对"{top_frequent["name"]}"段落的支持',
            'description': f'该段落出现{top_frequent["count"]}次，共{top_frequent["total_lines"]:,}行',
            'expected_time_saving': top_frequent['total_lines'] / 100 * 0.8,  # 80%时间节省
            'implementation': [
                f'在解析器中添加对"{top_frequent["name"]}"的识别',
                '实现该段落类型的专门处理逻辑',
                '或实现该段落的快速跳过'
            ]
        })
    
    return optimizations

def main():
    """主函数"""
    config_file = "KHU-FGT-1_7-0_0682_202507311406.conf"
    
    print("=" * 80)
    print("FortiGate配置文件详细段落分析")
    print("=" * 80)
    
    # 执行分析
    start_time = time.time()
    analysis_result = analyze_config_sections(config_file)
    analysis_time = time.time() - start_time
    
    print(f"\n分析耗时: {analysis_time:.2f} 秒")
    
    # 输出基本统计
    print(f"\n📊 基本统计:")
    print(f"   总配置段落: {analysis_result['total_sections']:,} 个")
    print(f"   支持的段落: {analysis_result['supported_sections_count']:,} 个")
    print(f"   未知段落: {analysis_result['unknown_sections_count']:,} 个")
    print(f"   未知段落占比: {(analysis_result['unknown_sections_count'] / analysis_result['total_sections']) * 100:.1f}%")
    
    print(f"\n📏 行数统计:")
    print(f"   总行数: {analysis_result['total_lines']:,} 行")
    print(f"   支持段落行数: {analysis_result['supported_lines']:,} 行")
    print(f"   未知段落行数: {analysis_result['unknown_lines']:,} 行")
    print(f"   未知段落行数占比: {(analysis_result['unknown_lines'] / analysis_result['total_lines']) * 100:.1f}%")
    
    # 瓶颈分析
    bottleneck_analysis = analyze_parsing_bottlenecks(analysis_result)
    
    print(f"\n⏱️ 时间估算:")
    time_est = bottleneck_analysis['time_estimation']
    print(f"   支持段落处理时间: {time_est['supported_sections_time']:.1f} 秒")
    print(f"   未知段落处理时间: {time_est['unknown_sections_time']:.1f} 秒")
    print(f"   总估算时间: {time_est['total_time']:.1f} 秒")
    print(f"   未知段落时间占比: {time_est['unknown_time_percentage']:.1f}%")
    
    # 最大的未知段落
    print(f"\n🔍 最大的未知段落 (前5个):")
    for i, section in enumerate(bottleneck_analysis['large_unknown_sections'][:5], 1):
        print(f"   {i}. {section['name']}: {section['line_count']:,} 行 (第{section['start_line']:,}-{section['end_line']:,}行)")
    
    # 最频繁的未知段落
    print(f"\n🔄 最频繁的未知段落 (前5个):")
    for i, section in enumerate(bottleneck_analysis['frequent_unknown_sections'][:5], 1):
        print(f"   {i}. {section['name']}: 出现{section['count']}次, 共{section['total_lines']:,}行")
    
    # 优化建议
    optimizations = generate_optimization_plan(analysis_result, bottleneck_analysis)
    print(f"\n💡 优化建议:")
    for opt in optimizations:
        print(f"   优先级{opt['priority']}: {opt['name']}")
        print(f"      描述: {opt['description']}")
        print(f"      预期时间节省: {opt['expected_time_saving']:.1f} 秒")
        print(f"      实现方案: {', '.join(opt['implementation'])}")
        print()

if __name__ == "__main__":
    main()
