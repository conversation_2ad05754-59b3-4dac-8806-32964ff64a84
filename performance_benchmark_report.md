# FortiGate twice-nat44性能基准测试报告

## 测试概述
- 测试规模: 10, 50, 100, 200, 500规则
- 测试时间: 2025-07-31 20:00:35

## 性能对比结果

| 规模 | twice-nat44 | 原有方案 | 改进 |
|------|-------------|----------|------|
| 10 | 1.95ms | 0.29ms | -567.5% |
| 50 | 1.74ms | 0.21ms | -709.5% |
| 100 | 1.72ms | 0.21ms | -700.2% |
| 200 | 1.81ms | 0.20ms | -782.6% |
| 500 | 1.64ms | 0.20ms | -701.8% |

## 详细指标

### 10规则测试结果
- 规则数量减少: +50.0%
- 处理时间改进: -567.5%
- 内存使用改进: -1183.3%
- XML大小减少: +28.5%

### 50规则测试结果
- 规则数量减少: +50.0%
- 处理时间改进: -709.5%
- 内存使用改进: -65.8%
- XML大小减少: +29.7%

### 100规则测试结果
- 规则数量减少: +50.0%
- 处理时间改进: -700.2%
- 内存使用改进: -12.3%
- XML大小减少: +29.8%

### 200规则测试结果
- 规则数量减少: +50.0%
- 处理时间改进: -782.6%
- 内存使用改进: -82.2%
- XML大小减少: +29.8%

### 500规则测试结果
- 规则数量减少: +50.0%
- 处理时间改进: -701.8%
- 内存使用改进: -25.4%
- XML大小减少: +29.8%
