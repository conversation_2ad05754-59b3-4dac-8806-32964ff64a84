{"config_file": "FortiGate-100F_7-6_3510_202505161613.conf", "iterations": 3, "execution_times": [30.54884696006775, 34.11761498451233, 32.864946126937866], "optimization_metrics": [{"optimization_ratio": 0, "sections_skipped": 0, "sections_simplified": 0, "sections_full_processed": 0, "total_sections": 0, "optimization_executed": false}, {"optimization_ratio": 0, "sections_skipped": 0, "sections_simplified": 0, "sections_full_processed": 0, "total_sections": 0, "optimization_executed": false}, {"optimization_ratio": 0, "sections_skipped": 0, "sections_simplified": 0, "sections_full_processed": 0, "total_sections": 0, "optimization_executed": false}], "average_time": 32.510469357172646, "optimization_ratio": 0, "performance_improvement": 0, "detailed_stats": [{"iteration": 1, "execution_time": 30.54884696006775, "return_code": 0, "optimization_metrics": {"optimization_ratio": 0, "sections_skipped": 0, "sections_simplified": 0, "sections_full_processed": 0, "total_sections": 0, "optimization_executed": false}}, {"iteration": 2, "execution_time": 34.11761498451233, "return_code": 0, "optimization_metrics": {"optimization_ratio": 0, "sections_skipped": 0, "sections_simplified": 0, "sections_full_processed": 0, "total_sections": 0, "optimization_executed": false}}, {"iteration": 3, "execution_time": 32.864946126937866, "return_code": 0, "optimization_metrics": {"optimization_ratio": 0, "sections_skipped": 0, "sections_simplified": 0, "sections_full_processed": 0, "total_sections": 0, "optimization_executed": false}}], "report_generated": "2025-08-04T11:41:40.890746", "report_file": "performance_benchmark_report_1754278900.json"}