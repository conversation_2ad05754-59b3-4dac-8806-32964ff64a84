#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
四层优化策略性能基准测试
"""

import sys
import os
import time
import json
import statistics
from datetime import datetime
sys.path.append('.')

# 设置正确的编码
from engine.utils.encoding_fix import setup_encoding
setup_encoding()

from engine.processing.stages.four_tier_optimization_stage import FourTierOptimizationStage

# 创建简单的配置管理器和数据上下文
class SimpleConfigManager:
    def __init__(self):
        self.config = {
            'four_tier_optimization': {
                'enabled': True,
                'target_optimization_ratio': 0.4,
                'quality_threshold': 0.95,
                'performance_monitoring': True
            }
        }
    
    def get(self, key, default=None):
        return self.config.get(key, default)

class SimpleDataContext:
    def __init__(self):
        self.data = {}
    
    def set_data(self, key, value):
        self.data[key] = value
    
    def get_data(self, key, default=None):
        return self.data.get(key, default)

def create_test_data(scale_factor=1):
    """创建测试数据，scale_factor控制数据规模"""
    base_interfaces = {
        f"port{i}": {"ip": f"192.168.{i}.1/24", "type": "physical"} 
        for i in range(1, 5 * scale_factor)
    }
    
    base_policies = [
        {"id": i, "srcintf": f"port{i%4+1}", "dstintf": f"port{(i+1)%4+1}", "action": "accept"}
        for i in range(1, 10 * scale_factor)
    ]
    
    base_addresses = {
        f"SUBNET_{i}": {"subnet": f"192.168.{i}.0/24", "type": "ipmask"}
        for i in range(1, 8 * scale_factor)
    }
    
    base_services = {
        f"SERVICE_{i}": {"tcp-portrange": f"{8000+i}", "protocol": "TCP"}
        for i in range(1, 6 * scale_factor)
    }
    
    return {
        "interfaces": base_interfaces,
        "firewall_policies": base_policies,
        "address_objects": base_addresses,
        "service_objects": base_services
    }

def run_optimization_benchmark(test_data, iterations=5):
    """运行优化基准测试"""
    print(f"🔧 开始性能基准测试 (数据规模: {len(test_data['interfaces'])}个接口, {len(test_data['firewall_policies'])}个策略)")
    
    results = []
    
    for i in range(iterations):
        print(f"  执行第 {i+1}/{iterations} 次测试...")
        
        # 创建配置管理器和数据上下文
        config_manager = SimpleConfigManager()
        context = SimpleDataContext()
        
        # 设置测试数据
        context.set_data("config_data", test_data)
        context.set_data("fortigate_data", test_data)
        
        # 创建四层优化阶段实例
        stage = FourTierOptimizationStage(config_manager=config_manager)
        
        # 记录开始时间
        start_time = time.time()
        
        # 执行优化处理
        result = stage.process(context)
        
        # 记录结束时间
        end_time = time.time()
        execution_time = end_time - start_time
        
        # 获取优化指标
        optimization_metrics = context.get_data("optimization_metrics", {})
        
        results.append({
            'iteration': i + 1,
            'execution_time': execution_time,
            'optimization_executed': context.get_data("optimization_executed", False),
            'optimization_ratio': optimization_metrics.get('optimization_ratio', 0),
            'quality_score': optimization_metrics.get('quality_score', 0),
            'total_sections': optimization_metrics.get('total_sections', 0),
            'sections_skipped': optimization_metrics.get('sections_skipped', 0),
            'sections_simplified': optimization_metrics.get('sections_simplified', 0),
            'sections_full_processed': optimization_metrics.get('sections_full_processed', 0),
            'time_saved_estimated': optimization_metrics.get('time_saved_estimated', 0)
        })
    
    return results

def analyze_benchmark_results(results):
    """分析基准测试结果"""
    if not results:
        return {}
    
    execution_times = [r['execution_time'] for r in results]
    optimization_ratios = [r['optimization_ratio'] for r in results]
    quality_scores = [r['quality_score'] for r in results]
    time_saved_estimates = [r['time_saved_estimated'] for r in results]
    
    analysis = {
        'execution_time': {
            'mean': statistics.mean(execution_times),
            'median': statistics.median(execution_times),
            'min': min(execution_times),
            'max': max(execution_times),
            'stdev': statistics.stdev(execution_times) if len(execution_times) > 1 else 0
        },
        'optimization_ratio': {
            'mean': statistics.mean(optimization_ratios),
            'median': statistics.median(optimization_ratios),
            'min': min(optimization_ratios),
            'max': max(optimization_ratios),
            'stdev': statistics.stdev(optimization_ratios) if len(optimization_ratios) > 1 else 0
        },
        'quality_score': {
            'mean': statistics.mean(quality_scores),
            'median': statistics.median(quality_scores),
            'min': min(quality_scores),
            'max': max(quality_scores),
            'stdev': statistics.stdev(quality_scores) if len(quality_scores) > 1 else 0
        },
        'time_saved_estimated': {
            'mean': statistics.mean(time_saved_estimates),
            'median': statistics.median(time_saved_estimates),
            'min': min(time_saved_estimates),
            'max': max(time_saved_estimates),
            'stdev': statistics.stdev(time_saved_estimates) if len(time_saved_estimates) > 1 else 0
        },
        'consistency': {
            'all_executed': all(r['optimization_executed'] for r in results),
            'execution_count': len(results),
            'success_rate': sum(1 for r in results if r['optimization_executed']) / len(results)
        }
    }
    
    return analysis

def generate_performance_report(analysis, scale_factors_tested):
    """生成性能报告"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"performance_report_{timestamp}.json"
    
    report = {
        'timestamp': datetime.now().isoformat(),
        'test_summary': {
            'scale_factors_tested': scale_factors_tested,
            'total_tests': sum(len(results) for results in analysis.values()),
            'test_duration': 'multiple_scales'
        },
        'performance_analysis': analysis,
        'conclusions': {
            'optimization_effectiveness': 'high' if all(
                scale_analysis['optimization_ratio']['mean'] >= 0.3 
                for scale_analysis in analysis.values()
            ) else 'moderate',
            'quality_maintenance': 'excellent' if all(
                scale_analysis['quality_score']['mean'] >= 0.9 
                for scale_analysis in analysis.values()
            ) else 'good',
            'performance_consistency': 'stable' if all(
                scale_analysis['execution_time']['stdev'] < 0.01 
                for scale_analysis in analysis.values()
            ) else 'variable'
        }
    }
    
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    return report_file

def main():
    """主测试函数"""
    print("🚀 四层优化策略性能基准测试开始")
    print("=" * 60)
    
    # 测试不同规模的数据
    scale_factors = [1, 2, 3]  # 小、中、大规模
    all_analysis = {}
    
    for scale in scale_factors:
        print(f"\n📊 测试规模 {scale}x:")
        test_data = create_test_data(scale)
        results = run_optimization_benchmark(test_data, iterations=3)
        analysis = analyze_benchmark_results(results)
        all_analysis[f'scale_{scale}x'] = analysis
        
        # 显示当前规模的结果
        print(f"  ✅ 平均执行时间: {analysis['execution_time']['mean']:.4f}秒")
        print(f"  ✅ 平均优化比例: {analysis['optimization_ratio']['mean']:.1%}")
        print(f"  ✅ 平均质量分数: {analysis['quality_score']['mean']:.2f}")
        print(f"  ✅ 成功率: {analysis['consistency']['success_rate']:.1%}")
    
    # 生成综合报告
    report_file = generate_performance_report(all_analysis, scale_factors)
    
    print(f"\n📋 性能基准测试完成")
    print(f"📄 详细报告已保存到: {report_file}")
    print("=" * 60)
    
    # 显示总结
    print("\n🎯 测试总结:")
    for scale, analysis in all_analysis.items():
        print(f"  {scale}: 优化率{analysis['optimization_ratio']['mean']:.1%}, "
              f"质量{analysis['quality_score']['mean']:.2f}, "
              f"耗时{analysis['execution_time']['mean']:.4f}s")
    
    return all_analysis

if __name__ == "__main__":
    try:
        results = main()
        print("\n🎉 性能基准测试成功完成！")
    except Exception as e:
        print(f"\n❌ 性能基准测试失败: {e}")
        import traceback
        traceback.print_exc()
