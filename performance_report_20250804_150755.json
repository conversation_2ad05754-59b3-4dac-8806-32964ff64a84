{"timestamp": "2025-08-04T15:07:55.946694", "test_summary": {"scale_factors_tested": [1, 2, 3], "total_tests": 15, "test_duration": "multiple_scales"}, "performance_analysis": {"scale_1x": {"execution_time": {"mean": 0.025423844655354817, "median": 0.02498483657836914, "min": 0.02493572235107422, "max": 0.026350975036621094, "stdev": 0.0008032939124199231}, "optimization_ratio": {"mean": 0.72, "median": 0.72, "min": 0.72, "max": 0.72, "stdev": 0.0}, "quality_score": {"mean": 0.94, "median": 0.94, "min": 0.94, "max": 0.94, "stdev": 0.0}, "time_saved_estimated": {"mean": 66.0, "median": 66.0, "min": 66.0, "max": 66.0, "stdev": 0.0}, "consistency": {"all_executed": true, "execution_count": 3, "success_rate": 1.0}}, "scale_2x": {"execution_time": {"mean": 0.026096185048421223, "median": 0.023514986038208008, "min": 0.02342367172241211, "max": 0.031349897384643555, "stdev": 0.004550077423505928}, "optimization_ratio": {"mean": 0.72, "median": 0.72, "min": 0.72, "max": 0.72, "stdev": 0.0}, "quality_score": {"mean": 0.94, "median": 0.94, "min": 0.94, "max": 0.94, "stdev": 0.0}, "time_saved_estimated": {"mean": 66.0, "median": 66.0, "min": 66.0, "max": 66.0, "stdev": 0.0}, "consistency": {"all_executed": true, "execution_count": 3, "success_rate": 1.0}}, "scale_3x": {"execution_time": {"mean": 0.02623438835144043, "median": 0.02732706069946289, "min": 0.02245020866394043, "max": 0.02892589569091797, "stdev": 0.0033732892349961617}, "optimization_ratio": {"mean": 0.72, "median": 0.72, "min": 0.72, "max": 0.72, "stdev": 0.0}, "quality_score": {"mean": 0.94, "median": 0.94, "min": 0.94, "max": 0.94, "stdev": 0.0}, "time_saved_estimated": {"mean": 66.0, "median": 66.0, "min": 66.0, "max": 66.0, "stdev": 0.0}, "consistency": {"all_executed": true, "execution_count": 3, "success_rate": 1.0}}}, "conclusions": {"optimization_effectiveness": "high", "quality_maintenance": "excellent", "performance_consistency": "stable"}}