#!/usr/bin/env python3
"""
管道化系统性验证器 - 按照完整的管道化XML生成流程顺序进行系统性验证
"""

import os
import json
import time
from typing import Dict, List, Tuple
from lxml import etree

class PipelineSystematicValidator:
    def __init__(self):
        self.config_files = [
            "FortiGate-100F_7-6_3510_202505161613.conf",
            "FortiGate-401F_7-4_2795_202507011110.conf", 
            "FortiGate-601E_7-4_2795_202506101906.conf",
            "Pass-Mask-MTU-FW-1_7-2_1639_202506271000.conf"
        ]
        
        self.pipeline_stages = [
            "operation_mode",           # 1. 路由模式/透明模式检测
            "interface_processing",     # 2. 接口配置
            "service_processing",       # 3. 服务对象
            "address_processing",       # 4. 地址对象
            "address_group_processing", # 5. 地址对象组
            "service_group_processing", # 6. 服务对象组
            "zone_processing",          # 7. 安全区域
            "time_range_processing",    # 8. 时间对象
            "dns_processing",           # 9. DNS配置
            "static_route_processing",  # 10. 静态路由
            "fortigate_policy_conversion", # 11. 安全策略
            "xml_template_integration", # 12. XML模板集成
            "yang_validation"           # 13. YANG验证
        ]
        
        self.validation_results = {}
    
    def get_mapping_file(self, config_file: str) -> str:
        """获取对应的接口映射文件"""
        base_name = os.path.splitext(config_file)[0]
        return f"mappings/interface_mapping_{base_name}.json"
    
    def run_conversion(self, config_file: str) -> Dict:
        """运行单个配置文件的转换"""
        print(f"\n🚀 运行转换: {config_file}")
        print("=" * 60)
        
        mapping_file = self.get_mapping_file(config_file)
        if not os.path.exists(mapping_file):
            print(f"❌ 接口映射文件不存在: {mapping_file}")
            return {"success": False, "error": "mapping_file_not_found"}
        
        # 生成输出文件名
        base_name = os.path.splitext(config_file)[0]
        output_file = f"output/{base_name}-z5100s-R11-pipeline-validation.xml"
        
        # 构建转换命令
        cmd = (
            f"python engine/main.py --mode convert --vendor fortigate "
            f"--cli {config_file} --mapping {mapping_file} "
            f"--model z5100s --version R11 --output {output_file} "
            f"--lang zh-CN --log-dir output/logs"
        )
        
        print(f"执行命令: {cmd}")
        
        # 记录开始时间
        start_time = time.time()
        
        # 执行转换
        import subprocess
        try:
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=300)
            duration = time.time() - start_time
            
            if result.returncode == 0:
                print(f"✅ 转换成功 (耗时: {duration:.2f}秒)")
                
                # 解析输出JSON
                try:
                    output_lines = result.stdout.strip().split('\n')
                    json_line = None
                    for line in output_lines:
                        if line.startswith('{') and '"success"' in line:
                            json_line = line
                            break
                    
                    if json_line:
                        conversion_result = json.loads(json_line)
                        conversion_result['duration'] = duration
                        conversion_result['output_file'] = output_file
                        return conversion_result
                    else:
                        print(f"⚠️ 无法解析转换结果JSON")
                        return {"success": True, "duration": duration, "output_file": output_file}
                        
                except json.JSONDecodeError as e:
                    print(f"⚠️ JSON解析失败: {str(e)}")
                    return {"success": True, "duration": duration, "output_file": output_file}
            else:
                print(f"❌ 转换失败 (返回码: {result.returncode})")
                print(f"错误输出: {result.stderr}")
                return {"success": False, "error": result.stderr, "duration": duration}
                
        except subprocess.TimeoutExpired:
            print(f"❌ 转换超时 (>300秒)")
            return {"success": False, "error": "timeout", "duration": 300}
        except Exception as e:
            print(f"❌ 转换异常: {str(e)}")
            return {"success": False, "error": str(e), "duration": time.time() - start_time}
    
    def analyze_xml_output(self, output_file: str) -> Dict:
        """分析XML输出文件"""
        print(f"\n🔍 分析XML输出: {output_file}")
        
        if not os.path.exists(output_file):
            print(f"❌ XML文件不存在: {output_file}")
            return {"success": False, "error": "xml_file_not_found"}
        
        try:
            # 解析XML文件
            tree = etree.parse(output_file)
            root = tree.getroot()
            
            # 统计XML结构
            xml_stats = {
                "file_size": os.path.getsize(output_file),
                "element_count": len(list(root.iter())),
                "namespaces": list(set([elem.tag.split('}')[0][1:] for elem in root.iter() if '}' in elem.tag])),
                "root_tag": root.tag.split('}')[-1] if '}' in root.tag else root.tag
            }
            
            # 统计各模块的配置数量
            module_stats = self._count_module_elements(root)
            
            print(f"✅ XML分析完成")
            print(f"   文件大小: {xml_stats['file_size']:,} 字节")
            print(f"   元素数量: {xml_stats['element_count']:,}")
            print(f"   命名空间数: {len(xml_stats['namespaces'])}")
            
            return {
                "success": True,
                "xml_stats": xml_stats,
                "module_stats": module_stats
            }
            
        except Exception as e:
            print(f"❌ XML分析失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def _count_module_elements(self, root) -> Dict:
        """统计各模块的元素数量"""
        module_stats = {}
        
        # 定义要统计的元素类型
        element_types = {
            "interfaces": ["physical", "vlan"],
            "services": ["service"],
            "addresses": ["address"],
            "address_groups": ["address-group"],
            "service_groups": ["service-group"],
            "zones": ["security-zone"],
            "time_objects": ["time-range"],
            "dns_configs": ["dns"],
            "static_routes": ["route"],
            "security_policies": ["security-policy"],
            "nat_policies": ["nat-policy"]
        }
        
        for module, tags in element_types.items():
            count = 0
            for tag in tags:
                count += len([elem for elem in root.iter() if elem.tag.split('}')[-1] == tag])
            module_stats[module] = count
        
        return module_stats
    
    def validate_pipeline_stages(self, conversion_result: Dict) -> Dict:
        """验证管道阶段执行情况"""
        print(f"\n🔍 验证管道阶段执行情况")
        
        stage_validation = {}
        
        if "stage_history" in conversion_result:
            stage_history = conversion_result["stage_history"]
            
            for expected_stage in self.pipeline_stages:
                found_stage = None
                for stage in stage_history:
                    if stage.get("stage") == expected_stage:
                        found_stage = stage
                        break
                
                if found_stage:
                    stage_validation[expected_stage] = {
                        "executed": True,
                        "status": found_stage.get("status", "unknown"),
                        "duration": found_stage.get("duration", 0),
                        "success": found_stage.get("status") == "completed"
                    }
                    status_icon = "✅" if found_stage.get("status") == "completed" else "❌"
                    print(f"   {status_icon} {expected_stage}: {found_stage.get('status', 'unknown')}")
                else:
                    stage_validation[expected_stage] = {
                        "executed": False,
                        "status": "not_found",
                        "duration": 0,
                        "success": False
                    }
                    print(f"   ❌ {expected_stage}: 未执行")
        else:
            print(f"⚠️ 无法获取阶段执行历史")
            for stage in self.pipeline_stages:
                stage_validation[stage] = {
                    "executed": False,
                    "status": "unknown",
                    "duration": 0,
                    "success": False
                }
        
        # 统计执行情况
        total_stages = len(self.pipeline_stages)
        executed_stages = sum(1 for v in stage_validation.values() if v["executed"])
        successful_stages = sum(1 for v in stage_validation.values() if v["success"])
        
        print(f"\n📊 阶段执行统计:")
        print(f"   总阶段数: {total_stages}")
        print(f"   已执行: {executed_stages}")
        print(f"   成功: {successful_stages}")
        print(f"   成功率: {successful_stages/total_stages*100:.1f}%")
        
        return {
            "stage_validation": stage_validation,
            "total_stages": total_stages,
            "executed_stages": executed_stages,
            "successful_stages": successful_stages,
            "success_rate": successful_stages/total_stages*100
        }
    
    def validate_all_configs(self):
        """验证所有配置文件"""
        print("🚀 开始系统性管道化验证")
        print("=" * 80)
        
        overall_results = {
            "total_configs": len(self.config_files),
            "successful_conversions": 0,
            "failed_conversions": 0,
            "config_results": {}
        }
        
        for i, config_file in enumerate(self.config_files, 1):
            print(f"\n{'='*80}")
            print(f"验证配置文件 {i}/{len(self.config_files)}: {config_file}")
            print(f"{'='*80}")
            
            # 1. 运行转换
            conversion_result = self.run_conversion(config_file)
            
            if conversion_result.get("success"):
                overall_results["successful_conversions"] += 1
                
                # 2. 分析XML输出
                xml_analysis = self.analyze_xml_output(conversion_result.get("output_file", ""))
                
                # 3. 验证管道阶段
                pipeline_validation = self.validate_pipeline_stages(conversion_result)
                
                # 4. 汇总结果
                config_result = {
                    "conversion": conversion_result,
                    "xml_analysis": xml_analysis,
                    "pipeline_validation": pipeline_validation,
                    "overall_success": True
                }
                
            else:
                overall_results["failed_conversions"] += 1
                config_result = {
                    "conversion": conversion_result,
                    "xml_analysis": {"success": False},
                    "pipeline_validation": {"success_rate": 0},
                    "overall_success": False
                }
            
            overall_results["config_results"][config_file] = config_result
        
        return overall_results
    
    def generate_comprehensive_report(self, results: Dict):
        """生成综合验证报告"""
        print(f"\n{'='*80}")
        print("📊 系统性管道化验证综合报告")
        print(f"{'='*80}")
        
        # 总体统计
        total_configs = results["total_configs"]
        successful = results["successful_conversions"]
        failed = results["failed_conversions"]
        
        print(f"📋 总体统计:")
        print(f"   配置文件总数: {total_configs}")
        print(f"   转换成功: {successful}")
        print(f"   转换失败: {failed}")
        print(f"   成功率: {successful/total_configs*100:.1f}%")
        
        # 详细结果
        print(f"\n📋 详细验证结果:")
        for config_file, result in results["config_results"].items():
            print(f"\n🔹 {config_file}:")
            
            # 转换结果
            conversion = result["conversion"]
            if conversion.get("success"):
                duration = conversion.get("duration", 0)
                errors = conversion.get("pipeline_info", {}).get("errors_count", "N/A")
                warnings = conversion.get("pipeline_info", {}).get("warnings_count", "N/A")
                print(f"   ✅ 转换: 成功 (耗时: {duration:.2f}秒, 错误: {errors}, 警告: {warnings})")
            else:
                print(f"   ❌ 转换: 失败 ({conversion.get('error', 'unknown')})")
            
            # XML分析
            xml_analysis = result["xml_analysis"]
            if xml_analysis.get("success"):
                xml_stats = xml_analysis["xml_stats"]
                print(f"   ✅ XML: {xml_stats['element_count']:,}元素, {xml_stats['file_size']:,}字节")
            else:
                print(f"   ❌ XML: 分析失败")
            
            # 管道验证
            pipeline = result["pipeline_validation"]
            success_rate = pipeline.get("success_rate", 0)
            print(f"   📊 管道: {success_rate:.1f}%成功率")
        
        # 管道阶段统计
        self._generate_pipeline_stage_summary(results)
        
        # 模块配置统计
        self._generate_module_statistics(results)
        
        print(f"\n🎯 验证结论:")
        if successful == total_configs:
            print("✅ 所有配置文件验证通过，系统可以部署上线")
        elif successful > total_configs * 0.8:
            print("⚠️ 大部分配置文件验证通过，建议修复失败项后部署")
        else:
            print("❌ 多个配置文件验证失败，需要进一步修复")
    
    def _generate_pipeline_stage_summary(self, results: Dict):
        """生成管道阶段汇总"""
        print(f"\n📊 管道阶段执行汇总:")
        
        stage_summary = {}
        for stage in self.pipeline_stages:
            stage_summary[stage] = {"success": 0, "total": 0}
        
        for config_file, result in results["config_results"].items():
            pipeline = result.get("pipeline_validation", {})
            stage_validation = pipeline.get("stage_validation", {})
            
            for stage in self.pipeline_stages:
                stage_summary[stage]["total"] += 1
                if stage_validation.get(stage, {}).get("success", False):
                    stage_summary[stage]["success"] += 1
        
        for stage, stats in stage_summary.items():
            success_rate = (stats["success"] / stats["total"] * 100) if stats["total"] > 0 else 0
            status_icon = "✅" if success_rate >= 90 else "⚠️" if success_rate >= 70 else "❌"
            print(f"   {status_icon} {stage}: {success_rate:.1f}% ({stats['success']}/{stats['total']})")
    
    def _generate_module_statistics(self, results: Dict):
        """生成模块配置统计"""
        print(f"\n📊 模块配置统计:")
        
        module_totals = {}
        
        for config_file, result in results["config_results"].items():
            xml_analysis = result.get("xml_analysis", {})
            module_stats = xml_analysis.get("module_stats", {})
            
            for module, count in module_stats.items():
                if module not in module_totals:
                    module_totals[module] = []
                module_totals[module].append(count)
        
        for module, counts in module_totals.items():
            if counts:
                total = sum(counts)
                avg = total / len(counts)
                print(f"   📋 {module}: 总计{total}, 平均{avg:.1f}")

def main():
    """主函数"""
    print("🚀 FortiGate到NTOS转换器系统性管道化验证")
    print("=" * 80)
    
    validator = PipelineSystematicValidator()
    
    # 执行全面验证
    results = validator.validate_all_configs()
    
    # 生成综合报告
    validator.generate_comprehensive_report(results)
    
    print(f"\n{'='*80}")
    print("🎉 系统性管道化验证完成")
    print(f"{'='*80}")

if __name__ == "__main__":
    main()
