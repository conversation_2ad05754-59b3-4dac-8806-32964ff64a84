#!/usr/bin/env python3
"""
精确修复XML文件中的地址引用不一致问题
"""

import sys
import os
import re
import shutil
from datetime import datetime

def precise_fix_xml_references(xml_file_path):
    """精确修复XML文件中的地址引用"""
    print(f"=== 精确修复XML文件: {xml_file_path} ===")
    
    if not os.path.exists(xml_file_path):
        print(f"❌ 文件不存在: {xml_file_path}")
        return False
    
    try:
        # 读取XML内容
        with open(xml_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 分析当前状态
        print("\n分析当前状态:")
        
        # 查找IP地址格式的地址对象定义
        address_defs = re.findall(r'<address-set>.*?<name>(10\.10\.11\.15\d)</name>', content, re.DOTALL)
        print(f"  地址对象定义(点号格式): {address_defs}")
        
        # 查找策略中的下划线格式引用
        policy_underscore_refs = re.findall(r'<(?:source|dest)-network>.*?<name>(10_10_11_15\d)</name>', content, re.DOTALL)
        print(f"  策略引用(下划线格式): {policy_underscore_refs}")
        
        if not policy_underscore_refs:
            print("✅ 没有发现下划线格式的引用，无需修复")
            return True
        
        # 备份原文件
        backup_file = f"{xml_file_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(xml_file_path, backup_file)
        print(f"✅ 已备份原文件到: {backup_file}")
        
        # 执行精确修复
        print(f"\n开始精确修复 {len(set(policy_underscore_refs))} 个下划线格式的引用...")
        
        fixed_content = content
        fix_count = 0
        
        # 只修复策略中的引用，使用更精确的模式
        for underscore_ref in set(policy_underscore_refs):  # 去重
            dot_ref = underscore_ref.replace('_', '.')
            
            # 精确匹配策略中的name元素
            pattern = r'(<(?:source|dest)-network>\s*<name>)' + re.escape(underscore_ref) + r'(</name>)'
            replacement = r'\1' + dot_ref + r'\2'
            
            old_content = fixed_content
            fixed_content = re.sub(pattern, replacement, fixed_content, flags=re.DOTALL)
            
            if fixed_content != old_content:
                fix_count += 1
                print(f"  ✅ 修复引用: {underscore_ref} → {dot_ref}")
        
        # 验证修复结果
        print(f"\n验证修复结果:")
        
        # 重新分析修复后的内容
        fixed_address_defs = re.findall(r'<address-set>.*?<name>(10\.10\.11\.15\d)</name>', fixed_content, re.DOTALL)
        fixed_policy_ip_refs = re.findall(r'<(?:source|dest)-network>.*?<name>(10\.10\.11\.15\d)</name>', fixed_content, re.DOTALL)
        fixed_policy_underscore_refs = re.findall(r'<(?:source|dest)-network>.*?<name>(10_10_11_15\d)</name>', fixed_content, re.DOTALL)
        
        print(f"  修复后地址对象定义(点号格式): {fixed_address_defs}")
        print(f"  修复后策略引用(点号格式): {fixed_policy_ip_refs}")
        print(f"  修复后策略引用(下划线格式): {fixed_policy_underscore_refs}")
        
        if fixed_address_defs and fixed_policy_ip_refs and not fixed_policy_underscore_refs:
            print("🎉 修复成功！所有策略引用现在都使用点号格式")
            
            # 保存修复后的文件
            with open(xml_file_path, 'w', encoding='utf-8') as f:
                f.write(fixed_content)
            
            print(f"✅ 已保存修复后的文件: {xml_file_path}")
            return True
        elif not fixed_policy_underscore_refs:
            print("✅ 修复有效！已消除下划线格式的引用")
            
            # 保存修复后的文件
            with open(xml_file_path, 'w', encoding='utf-8') as f:
                f.write(fixed_content)
            
            print(f"✅ 已保存修复后的文件: {xml_file_path}")
            return True
        else:
            print(f"❌ 修复不完整，仍有 {len(fixed_policy_underscore_refs)} 个下划线格式的引用")
            return False
        
    except Exception as e:
        print(f"❌ 修复过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("精确修复XML文件中的地址引用不一致问题")
    print("=" * 60)
    
    # 恢复备份文件
    xml_file = "output/fortigate-z5100s-R11_backup_20250804_161148.xml"
    backup_file = "output/fortigate-z5100s-R11_backup_20250804_161148.xml.backup_20250804_171234"
    
    if os.path.exists(backup_file):
        print(f"恢复备份文件: {backup_file} → {xml_file}")
        shutil.copy2(backup_file, xml_file)
        print("✅ 备份文件已恢复")
    else:
        print(f"❌ 备份文件不存在: {backup_file}")
        return False
    
    # 执行精确修复
    success = precise_fix_xml_references(xml_file)
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 精确修复成功！")
    else:
        print("❌ 精确修复失败")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
