#!/usr/bin/env python3
"""
精确的XML验证脚本
"""

import xml.etree.ElementTree as ET
import sys

def test_xml_parsing(xml_file):
    """测试XML文件解析"""
    print(f"测试XML文件: {xml_file}")
    
    try:
        tree = ET.parse(xml_file)
        root = tree.getroot()
        print(f"✅ XML解析成功")
        print(f"   根元素: {root.tag}")
        
        # 统计元素
        total_elements = len(list(root.iter()))
        print(f"   总元素数: {total_elements:,}")
        
        # 检查security-zone容器中是否有错误的auto元素
        invalid_auto_count = 0
        valid_auto_count = 0
        
        for elem in root.iter():
            # 只检查security-zone命名空间的security-zone元素
            if (elem.tag.endswith('security-zone') and 
                'urn:ruijie:ntos:params:xml:ns:yang:security-zone' in elem.tag):
                
                print(f"   检查security-zone容器...")
                for child in elem:
                    child_tag = child.tag.split('}')[-1] if '}' in child.tag else child.tag
                    if child_tag == 'auto':
                        invalid_auto_count += 1
                        print(f"      ❌ 发现错误的auto元素")
            
            # 检查threat-intelligence中的正确auto元素
            elif (elem.tag.endswith('security-zone') and 
                  'threat-intelligence' in str(elem.getparent().getparent().tag if elem.getparent() and elem.getparent().getparent() else '')):
                
                print(f"   检查threat-intelligence中的security-zone...")
                for child in elem:
                    child_tag = child.tag.split('}')[-1] if '}' in child.tag else child.tag
                    if child_tag == 'auto':
                        valid_auto_count += 1
                        print(f"      ✅ 发现正确的auto元素: {child.text}")
        
        print(f"   错误放置的auto元素: {invalid_auto_count}")
        print(f"   正确放置的auto元素: {valid_auto_count}")
        
        # 检查threat-intelligence结构
        threat_intel_count = 0
        for elem in root.iter():
            if elem.tag.endswith('threat-intelligence'):
                threat_intel_count += 1
                print(f"   检查threat-intelligence结构...")
                
                management = elem.find('management')
                if management is not None:
                    print(f"      ✅ 有management子元素")
                    
                    security_zone = management.find('security-zone')
                    if security_zone is not None:
                        print(f"      ✅ 有security-zone子元素")
                        
                        auto = security_zone.find('auto')
                        if auto is not None:
                            print(f"      ✅ 有auto子元素: {auto.text}")
                        else:
                            print(f"      ❌ 缺少auto子元素")
                    else:
                        print(f"      ❌ 缺少security-zone子元素")
                else:
                    print(f"      ❌ 缺少management子元素")
        
        print(f"   threat-intelligence元素数: {threat_intel_count}")
        
        return invalid_auto_count == 0
        
    except ET.ParseError as e:
        print(f"❌ XML解析失败: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 验证异常: {str(e)}")
        return False

def main():
    xml_file = "output/fortigate-z5100s-R11_backup_20250801_210727.xml"
    
    if len(sys.argv) > 1:
        xml_file = sys.argv[1]
    
    success = test_xml_parsing(xml_file)
    
    if success:
        print(f"\n🎉 XML验证成功！")
        print(f"   ✅ XML语法正确")
        print(f"   ✅ threat-intelligence结构正确")
        print(f"   ✅ security-zone结构正确")
        print(f"   ✅ 没有错误放置的auto元素")
    else:
        print(f"\n❌ XML验证失败，需要进一步修复")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
