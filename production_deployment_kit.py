#!/usr/bin/env python3
"""
生产环境部署工具包
"""

import os
import json
import shutil
import subprocess
from datetime import datetime
from typing import Dict, List

class ProductionDeploymentKit:
    def __init__(self):
        self.deployment_config = {
            "service_name": "fortigate-to-ntos-converter",
            "version": "2.0.0",
            "deployment_date": datetime.now().isoformat(),
            "python_version": "3.8+",
            "required_packages": [
                "lxml>=4.6.0",
                "pyyaml>=5.4.0",
                "jsonschema>=3.2.0",
                "click>=7.0"
            ]
        }
        
        self.deployment_paths = {
            "service_root": "/opt/fortigate-converter",
            "config_dir": "/etc/fortigate-converter",
            "log_dir": "/var/log/fortigate-converter",
            "data_dir": "/var/lib/fortigate-converter",
            "temp_dir": "/tmp/fortigate-converter"
        }
    
    def create_deployment_structure(self):
        """创建部署目录结构"""
        print("🏗️ 创建生产环境部署结构")
        print("=" * 60)
        
        # 创建部署目录
        deployment_dir = "deployment"
        os.makedirs(deployment_dir, exist_ok=True)
        
        # 创建子目录
        subdirs = [
            "bin",           # 可执行脚本
            "config",        # 配置文件
            "docs",          # 文档
            "scripts",       # 部署脚本
            "systemd",       # systemd服务文件
            "nginx",         # nginx配置
            "monitoring"     # 监控配置
        ]
        
        for subdir in subdirs:
            os.makedirs(f"{deployment_dir}/{subdir}", exist_ok=True)
            print(f"   ✅ 创建目录: {deployment_dir}/{subdir}")
        
        return deployment_dir
    
    def create_service_script(self, deployment_dir: str):
        """创建服务启动脚本"""
        print(f"\n📝 创建服务启动脚本")
        
        service_script = f"""#!/bin/bash
# FortiGate到NTOS转换器服务启动脚本
# 版本: {self.deployment_config['version']}
# 创建时间: {self.deployment_config['deployment_date']}

set -e

# 服务配置
SERVICE_NAME="{self.deployment_config['service_name']}"
SERVICE_ROOT="{self.deployment_paths['service_root']}"
CONFIG_DIR="{self.deployment_paths['config_dir']}"
LOG_DIR="{self.deployment_paths['log_dir']}"
DATA_DIR="{self.deployment_paths['data_dir']}"
TEMP_DIR="{self.deployment_paths['temp_dir']}"

# Python环境
PYTHON_BIN="$SERVICE_ROOT/venv/bin/python"
MAIN_SCRIPT="$SERVICE_ROOT/engine/main.py"

# 日志配置
LOG_FILE="$LOG_DIR/converter.log"
ERROR_LOG="$LOG_DIR/error.log"

# 函数定义
log_info() {{
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] INFO: $1" | tee -a "$LOG_FILE"
}}

log_error() {{
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1" | tee -a "$ERROR_LOG"
}}

# 检查环境
check_environment() {{
    log_info "检查运行环境..."
    
    # 检查Python环境
    if [ ! -f "$PYTHON_BIN" ]; then
        log_error "Python虚拟环境不存在: $PYTHON_BIN"
        exit 1
    fi
    
    # 检查主程序
    if [ ! -f "$MAIN_SCRIPT" ]; then
        log_error "主程序不存在: $MAIN_SCRIPT"
        exit 1
    fi
    
    # 检查目录权限
    for dir in "$LOG_DIR" "$DATA_DIR" "$TEMP_DIR"; do
        if [ ! -w "$dir" ]; then
            log_error "目录不可写: $dir"
            exit 1
        fi
    done
    
    log_info "环境检查通过"
}}

# 启动服务
start_service() {{
    log_info "启动FortiGate转换器服务..."
    
    check_environment
    
    # 切换到服务目录
    cd "$SERVICE_ROOT"
    
    # 启动服务
    exec "$PYTHON_BIN" "$MAIN_SCRIPT" \\
        --mode server \\
        --config "$CONFIG_DIR/converter.yaml" \\
        --log-dir "$LOG_DIR" \\
        --data-dir "$DATA_DIR" \\
        --temp-dir "$TEMP_DIR" \\
        "$@"
}}

# 转换单个文件
convert_file() {{
    local config_file="$1"
    local mapping_file="$2"
    local output_file="$3"
    local model="$4"
    local version="$5"
    
    log_info "开始转换: $config_file"
    
    check_environment
    
    cd "$SERVICE_ROOT"
    
    "$PYTHON_BIN" "$MAIN_SCRIPT" \\
        --mode convert \\
        --vendor fortigate \\
        --cli "$config_file" \\
        --mapping "$mapping_file" \\
        --model "$model" \\
        --version "$version" \\
        --output "$output_file" \\
        --lang zh-CN \\
        --log-dir "$LOG_DIR"
}}

# 主函数
main() {{
    case "$1" in
        start)
            start_service "${{@:2}}"
            ;;
        convert)
            if [ $# -lt 6 ]; then
                echo "用法: $0 convert <config_file> <mapping_file> <output_file> <model> <version>"
                exit 1
            fi
            convert_file "$2" "$3" "$4" "$5" "$6"
            ;;
        *)
            echo "用法: $0 {{start|convert}} [参数...]"
            echo "  start                    - 启动服务模式"
            echo "  convert <args>           - 转换单个文件"
            exit 1
            ;;
    esac
}}

main "$@"
"""
        
        script_file = f"{deployment_dir}/bin/fortigate-converter"
        with open(script_file, 'w', encoding='utf-8') as f:
            f.write(service_script)
        
        # 设置执行权限
        os.chmod(script_file, 0o755)
        print(f"   ✅ 服务脚本: {script_file}")
    
    def create_systemd_service(self, deployment_dir: str):
        """创建systemd服务文件"""
        print(f"\n📝 创建systemd服务文件")
        
        systemd_service = f"""[Unit]
Description=FortiGate到NTOS转换器服务
Documentation=https://github.com/your-org/fortigate-converter
After=network.target
Wants=network.target

[Service]
Type=simple
User=converter
Group=converter
WorkingDirectory={self.deployment_paths['service_root']}
ExecStart={self.deployment_paths['service_root']}/bin/fortigate-converter start
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=fortigate-converter

# 环境变量
Environment=PYTHONPATH={self.deployment_paths['service_root']}
Environment=CONVERTER_CONFIG={self.deployment_paths['config_dir']}/converter.yaml
Environment=CONVERTER_LOG_DIR={self.deployment_paths['log_dir']}

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths={self.deployment_paths['log_dir']} {self.deployment_paths['data_dir']} {self.deployment_paths['temp_dir']}

[Install]
WantedBy=multi-user.target
"""
        
        service_file = f"{deployment_dir}/systemd/fortigate-converter.service"
        with open(service_file, 'w', encoding='utf-8') as f:
            f.write(systemd_service)
        
        print(f"   ✅ systemd服务文件: {service_file}")
    
    def create_configuration_files(self, deployment_dir: str):
        """创建配置文件"""
        print(f"\n📝 创建配置文件")
        
        # 主配置文件
        main_config = {
            "service": {
                "name": self.deployment_config["service_name"],
                "version": self.deployment_config["version"],
                "bind_host": "0.0.0.0",
                "bind_port": 8080,
                "workers": 4,
                "max_file_size": "100MB",
                "timeout": 300
            },
            "logging": {
                "level": "INFO",
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                "file": f"{self.deployment_paths['log_dir']}/converter.log",
                "max_size": "10MB",
                "backup_count": 5,
                "rotation": "daily"
            },
            "storage": {
                "temp_dir": self.deployment_paths["temp_dir"],
                "data_dir": self.deployment_paths["data_dir"],
                "cleanup_interval": 3600,
                "max_temp_age": 86400
            },
            "conversion": {
                "default_model": "z5100s",
                "default_version": "R11",
                "default_language": "zh-CN",
                "enable_yang_validation": True,
                "enable_encryption": False,
                "max_concurrent_jobs": 10
            },
            "monitoring": {
                "enable_metrics": True,
                "metrics_port": 9090,
                "health_check_interval": 30,
                "enable_alerts": True
            }
        }
        
        config_file = f"{deployment_dir}/config/converter.yaml"
        import yaml
        with open(config_file, 'w', encoding='utf-8') as f:
            yaml.dump(main_config, f, default_flow_style=False, allow_unicode=True)
        
        print(f"   ✅ 主配置文件: {config_file}")
        
        # 日志配置文件
        log_config = {
            "version": 1,
            "disable_existing_loggers": False,
            "formatters": {
                "standard": {
                    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
                },
                "detailed": {
                    "format": "%(asctime)s - %(name)s - %(levelname)s - %(module)s - %(funcName)s - %(message)s"
                }
            },
            "handlers": {
                "console": {
                    "class": "logging.StreamHandler",
                    "level": "INFO",
                    "formatter": "standard",
                    "stream": "ext://sys.stdout"
                },
                "file": {
                    "class": "logging.handlers.RotatingFileHandler",
                    "level": "DEBUG",
                    "formatter": "detailed",
                    "filename": f"{self.deployment_paths['log_dir']}/converter.log",
                    "maxBytes": 10485760,
                    "backupCount": 5
                },
                "error_file": {
                    "class": "logging.handlers.RotatingFileHandler",
                    "level": "ERROR",
                    "formatter": "detailed",
                    "filename": f"{self.deployment_paths['log_dir']}/error.log",
                    "maxBytes": 10485760,
                    "backupCount": 5
                }
            },
            "loggers": {
                "fortigate_converter": {
                    "level": "DEBUG",
                    "handlers": ["console", "file", "error_file"],
                    "propagate": False
                }
            },
            "root": {
                "level": "INFO",
                "handlers": ["console", "file"]
            }
        }
        
        log_config_file = f"{deployment_dir}/config/logging.yaml"
        with open(log_config_file, 'w', encoding='utf-8') as f:
            yaml.dump(log_config, f, default_flow_style=False, allow_unicode=True)
        
        print(f"   ✅ 日志配置文件: {log_config_file}")
    
    def create_monitoring_config(self, deployment_dir: str):
        """创建监控配置"""
        print(f"\n📝 创建监控配置")
        
        # Prometheus监控配置
        prometheus_config = """
# FortiGate转换器Prometheus监控配置
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "converter_rules.yml"

scrape_configs:
  - job_name: 'fortigate-converter'
    static_configs:
      - targets: ['localhost:9090']
    metrics_path: /metrics
    scrape_interval: 10s

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
"""
        
        prometheus_file = f"{deployment_dir}/monitoring/prometheus.yml"
        with open(prometheus_file, 'w', encoding='utf-8') as f:
            f.write(prometheus_config)
        
        # 告警规则
        alert_rules = """
groups:
  - name: fortigate_converter_alerts
    rules:
      - alert: ConverterServiceDown
        expr: up{job="fortigate-converter"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "FortiGate转换器服务停止"
          description: "FortiGate转换器服务已停止超过1分钟"
      
      - alert: HighConversionFailureRate
        expr: rate(converter_failures_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "转换失败率过高"
          description: "转换失败率超过10%，持续2分钟"
      
      - alert: HighMemoryUsage
        expr: process_resident_memory_bytes / 1024 / 1024 > 1000
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "内存使用率过高"
          description: "内存使用超过1GB，持续5分钟"
"""
        
        rules_file = f"{deployment_dir}/monitoring/converter_rules.yml"
        with open(rules_file, 'w', encoding='utf-8') as f:
            f.write(alert_rules)
        
        print(f"   ✅ Prometheus配置: {prometheus_file}")
        print(f"   ✅ 告警规则: {rules_file}")
    
    def create_deployment_guide(self, deployment_dir: str):
        """创建部署指南"""
        print(f"\n📝 创建部署指南")
        
        deployment_guide = f"""# FortiGate到NTOS转换器生产环境部署指南

## 版本信息
- 服务版本: {self.deployment_config['version']}
- Python版本: {self.deployment_config['python_version']}
- 部署日期: {self.deployment_config['deployment_date']}

## 系统要求
- 操作系统: Ubuntu 20.04+ / CentOS 8+ / RHEL 8+
- Python: 3.8+
- 内存: 最小2GB，推荐4GB+
- 磁盘: 最小10GB可用空间
- 网络: 支持HTTP/HTTPS访问

## 安装步骤

### 1. 创建服务用户
```bash
sudo useradd -r -s /bin/false converter
sudo mkdir -p {self.deployment_paths['service_root']}
sudo mkdir -p {self.deployment_paths['config_dir']}
sudo mkdir -p {self.deployment_paths['log_dir']}
sudo mkdir -p {self.deployment_paths['data_dir']}
sudo mkdir -p {self.deployment_paths['temp_dir']}
sudo chown -R converter:converter {self.deployment_paths['service_root']}
sudo chown -R converter:converter {self.deployment_paths['log_dir']}
sudo chown -R converter:converter {self.deployment_paths['data_dir']}
sudo chown -R converter:converter {self.deployment_paths['temp_dir']}
```

### 2. 部署应用代码
```bash
# 复制应用代码
sudo cp -r engine/ {self.deployment_paths['service_root']}/
sudo cp -r mappings/ {self.deployment_paths['service_root']}/
sudo cp deployment/bin/* {self.deployment_paths['service_root']}/bin/
sudo cp deployment/config/* {self.deployment_paths['config_dir']}/

# 设置权限
sudo chown -R converter:converter {self.deployment_paths['service_root']}
sudo chmod +x {self.deployment_paths['service_root']}/bin/*
```

### 3. 安装Python依赖
```bash
# 创建虚拟环境
sudo -u converter python3 -m venv {self.deployment_paths['service_root']}/venv

# 安装依赖
sudo -u converter {self.deployment_paths['service_root']}/venv/bin/pip install \\
    {' '.join(self.deployment_config['required_packages'])}
```

### 4. 配置systemd服务
```bash
sudo cp deployment/systemd/fortigate-converter.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable fortigate-converter
```

### 5. 启动服务
```bash
sudo systemctl start fortigate-converter
sudo systemctl status fortigate-converter
```

## 配置说明

### 主配置文件
配置文件位置: `{self.deployment_paths['config_dir']}/converter.yaml`

主要配置项:
- `service.bind_port`: 服务监听端口（默认8080）
- `service.workers`: 工作进程数（默认4）
- `conversion.default_model`: 默认设备型号
- `logging.level`: 日志级别

### 日志配置
- 应用日志: `{self.deployment_paths['log_dir']}/converter.log`
- 错误日志: `{self.deployment_paths['log_dir']}/error.log`
- 系统日志: `journalctl -u fortigate-converter`

## 使用方法

### 命令行转换
```bash
{self.deployment_paths['service_root']}/bin/fortigate-converter convert \\
    /path/to/fortigate.conf \\
    /path/to/mapping.json \\
    /path/to/output.xml \\
    z5100s \\
    R11
```

### API调用
```bash
curl -X POST http://localhost:8080/api/convert \\
    -F "config=@fortigate.conf" \\
    -F "mapping=@mapping.json" \\
    -F "model=z5100s" \\
    -F "version=R11"
```

## 监控和维护

### 健康检查
```bash
curl http://localhost:8080/health
```

### 查看指标
```bash
curl http://localhost:9090/metrics
```

### 日志轮转
日志文件会自动轮转，保留最近5个文件。

### 备份建议
- 定期备份配置文件
- 定期备份接口映射文件
- 监控磁盘空间使用

## 故障排除

### 常见问题
1. **服务启动失败**
   - 检查Python环境: `{self.deployment_paths['service_root']}/venv/bin/python --version`
   - 检查权限: `ls -la {self.deployment_paths['service_root']}`
   - 查看日志: `journalctl -u fortigate-converter -f`

2. **转换失败**
   - 检查接口映射文件格式
   - 查看错误日志: `tail -f {self.deployment_paths['log_dir']}/error.log`
   - 验证FortiGate配置文件格式

3. **性能问题**
   - 调整worker数量
   - 增加内存限制
   - 监控CPU和内存使用

### 联系支持
- 邮箱: <EMAIL>
- 文档: https://docs.your-company.com/fortigate-converter
- 问题反馈: https://github.com/your-org/fortigate-converter/issues
"""
        
        guide_file = f"{deployment_dir}/docs/DEPLOYMENT_GUIDE.md"
        with open(guide_file, 'w', encoding='utf-8') as f:
            f.write(deployment_guide)
        
        print(f"   ✅ 部署指南: {guide_file}")
    
    def generate_deployment_package(self):
        """生成完整的部署包"""
        print("🚀 生成生产环境部署包")
        print("=" * 80)
        
        # 1. 创建部署结构
        deployment_dir = self.create_deployment_structure()
        
        # 2. 创建服务脚本
        self.create_service_script(deployment_dir)
        
        # 3. 创建systemd服务
        self.create_systemd_service(deployment_dir)
        
        # 4. 创建配置文件
        self.create_configuration_files(deployment_dir)
        
        # 5. 创建监控配置
        self.create_monitoring_config(deployment_dir)
        
        # 6. 创建部署指南
        self.create_deployment_guide(deployment_dir)
        
        # 7. 创建部署信息文件
        deployment_info = {
            "package_version": self.deployment_config["version"],
            "created_at": self.deployment_config["deployment_date"],
            "python_requirements": self.deployment_config["required_packages"],
            "deployment_paths": self.deployment_paths,
            "files_included": [
                "bin/fortigate-converter",
                "config/converter.yaml",
                "config/logging.yaml",
                "systemd/fortigate-converter.service",
                "monitoring/prometheus.yml",
                "monitoring/converter_rules.yml",
                "docs/DEPLOYMENT_GUIDE.md"
            ]
        }
        
        info_file = f"{deployment_dir}/deployment_info.json"
        with open(info_file, 'w', encoding='utf-8') as f:
            json.dump(deployment_info, f, indent=2, ensure_ascii=False)
        
        print(f"\n✅ 部署包生成完成: {deployment_dir}/")
        print(f"📦 包含文件:")
        for file_path in deployment_info["files_included"]:
            print(f"   - {file_path}")
        
        return deployment_dir

def main():
    """主函数"""
    print("🚀 FortiGate到NTOS转换器生产环境部署工具包")
    print("=" * 80)
    
    kit = ProductionDeploymentKit()
    deployment_dir = kit.generate_deployment_package()
    
    print(f"\n{'='*80}")
    print("🎉 生产环境部署包生成完成")
    print(f"{'='*80}")
    print(f"📁 部署包位置: {deployment_dir}/")
    print(f"📖 部署指南: {deployment_dir}/docs/DEPLOYMENT_GUIDE.md")

if __name__ == "__main__":
    main()
