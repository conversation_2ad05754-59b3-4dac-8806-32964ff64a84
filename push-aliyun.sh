#!/bin/bash
# 阿里云镜像推送专用脚本

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# 设置目标信息
ALIYUN_REGISTRY="registry.cn-hangzhou.aliyuncs.com/secloud"
IMAGE_NAME="config-converter"

# 标签生成函数
generate_random_string() {
    local length=${1:-6}  # 默认6位，可以传入4-6之间的值
    local chars="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
    local result=""
    for i in $(seq 1 $length); do
        result="${result}${chars:$((RANDOM % ${#chars})):1}"
    done
    echo "$result"
}

# 生成紧凑时间戳（4个字符）
generate_compact_timestamp() {
    # 基准时间：2025-01-01 00:00:00 UTC
    local base_epoch=1735689600
    local current_epoch=$(date +%s)
    local relative_seconds=$((current_epoch - base_epoch))

    # 将相对秒数转换为Base36编码（4个字符）
    # Base36使用0-9和A-Z，共36个字符
    local chars="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"
    local result=""
    local num=$relative_seconds

    # 转换为4位Base36
    for i in {1..4}; do
        local remainder=$((num % 36))
        result="${chars:$remainder:1}${result}"
        num=$((num / 36))
    done

    echo "$result"
}

# 根据镜像类型生成标签
generate_tag() {
    local image_type="$1"
    case "$image_type" in
        "base")
            echo "2.9.1"
            ;;
        "app")
            # 紧凑格式：4字符时间戳 + 4字符随机字符串 = 8字符总长度
            local compact_timestamp=$(generate_compact_timestamp)
            local random_str=$(generate_random_string 4)
            echo "2.9.1_${compact_timestamp}${random_str}"
            ;;
        *)
            echo "2.9.1"
            ;;
    esac
}

echo -e "${GREEN}NTOS配置转换服务阿里云镜像推送工具${NC}"
echo ""

# 选择构建模式
echo -e "${YELLOW}选择操作模式${NC}"
echo "1) 应用镜像 (只构建和推送应用层)"
echo "2) 基础镜像 (构建和推送基础环境层)"
echo "3) 全部 (构建和推送基础镜像和应用镜像)"
echo "4) 仅推送已构建的镜像"
echo "q) 退出"

read -p "请选择 (默认: 1): " choice
choice=${choice:-1}

# 根据选择生成相应的标签和目标镜像
case $choice in
    1)
        # 应用镜像 - 使用动态标签
        TAG=$(generate_tag "app")
        TARGET_IMAGE="${ALIYUN_REGISTRY}/${IMAGE_NAME}:${TAG}"
        echo "应用镜像标签: ${TAG}"
        echo "目标镜像: ${TARGET_IMAGE}"
        echo ""
        echo -e "${GREEN}开始构建应用镜像...${NC}"
        ./build.sh --app-only --aliyun --app-tag "${TAG}"
        ;;
    2)
        # 基础镜像 - 使用固定标签
        TAG=$(generate_tag "base")
        TARGET_IMAGE="${ALIYUN_REGISTRY}/${IMAGE_NAME}:${TAG}"
        echo "基础镜像标签: ${TAG}"
        echo "目标镜像: ${TARGET_IMAGE}"
        echo ""
        echo -e "${GREEN}开始构建基础镜像...${NC}"
        ./build.sh --base-only --aliyun
        ;;
    3)
        # 全部镜像 - 基础镜像用固定标签，应用镜像用动态标签
        BASE_TAG=$(generate_tag "base")
        APP_TAG=$(generate_tag "app")
        BASE_TARGET_IMAGE="${ALIYUN_REGISTRY}/${IMAGE_NAME}:${BASE_TAG}"
        APP_TARGET_IMAGE="${ALIYUN_REGISTRY}/${IMAGE_NAME}:${APP_TAG}"
        echo "基础镜像标签: ${BASE_TAG}"
        echo "基础镜像: ${BASE_TARGET_IMAGE}"
        echo "应用镜像标签: ${APP_TAG}"
        echo "应用镜像: ${APP_TARGET_IMAGE}"
        echo ""
        echo -e "${GREEN}开始构建所有镜像...${NC}"
        ./build.sh --aliyun --app-tag "${APP_TAG}"
        ;;
    4)
        echo -e "${YELLOW}请选择要推送的镜像类型:${NC}"
        echo "1) 推送应用镜像"
        echo "2) 推送基础镜像"
        read -p "请选择 (默认: 1): " push_choice
        push_choice=${push_choice:-1}

        if [ "$push_choice" = "1" ]; then
            TAG=$(generate_tag "app")
            TARGET_IMAGE="${ALIYUN_REGISTRY}/${IMAGE_NAME}:${TAG}"
            echo "推送应用镜像: ${TARGET_IMAGE}"
            echo -e "${YELLOW}注意: 确保已使用相同标签构建应用镜像${NC}"
        else
            TAG=$(generate_tag "base")
            TARGET_IMAGE="${ALIYUN_REGISTRY}/${IMAGE_NAME}:${TAG}"
            echo "推送基础镜像: ${TARGET_IMAGE}"
        fi

        echo -e "${YELLOW}正在推送: ${TARGET_IMAGE}${NC}"
        docker push ${TARGET_IMAGE}
        if [ $? -ne 0 ]; then
            echo -e "${RED}推送失败，请检查镜像是否已构建${NC}"
            exit 1
        fi
        echo -e "${GREEN}推送成功!${NC}"
        ;;
    q|Q)
        echo "退出"
        exit 0
        ;;
    *)
        echo -e "${RED}无效选择${NC}"
        exit 1
        ;;
esac

echo -e "${GREEN}操作完成!${NC}"

# 根据操作类型显示使用说明
case $choice in
    1)
        echo "如需使用应用镜像，可以运行:"
        echo "docker pull ${TARGET_IMAGE}"
        echo "docker run -d -p 9005:9005 ${TARGET_IMAGE}"
        ;;
    2)
        echo "如需使用基础镜像，可以运行:"
        echo "docker pull ${TARGET_IMAGE}"
        echo "注意: 基础镜像通常用于构建其他镜像，不直接运行服务"
        ;;
    3)
        echo "如需使用镜像，可以运行:"
        echo "基础镜像: docker pull ${BASE_TARGET_IMAGE}"
        echo "应用镜像: docker pull ${APP_TARGET_IMAGE}"
        echo "运行服务: docker run -d -p 9005:9005 ${APP_TARGET_IMAGE}"
        ;;
    4)
        echo "推送的镜像可以运行:"
        echo "docker pull ${TARGET_IMAGE}"
        if [[ "$TARGET_IMAGE" == *"_"* ]]; then
            echo "docker run -d -p 9005:9005 ${TARGET_IMAGE}"
        else
            echo "注意: 基础镜像通常用于构建其他镜像，不直接运行服务"
        fi
        ;;
esac

exit 0