"""
四层优化策略模块
FortiGate到NTOS转换质量优先性能优化策略
"""

# 导出核心组件
from .four_tier_optimization_architecture import (
    FourTierOptimizationArchitecture,
    OptimizationResult,
    OptimizationTier,
    ProcessingStrategy,
    SectionAnalysisResult,
    QualityMetrics,
    PerformanceMetrics
)

from .tier1_safe_skip_processor import Tier1SafeSkipProcessor
from .tier2_conditional_skip_processor import Tier2ConditionalSkipProcessor  
from .tier3_important_retain_processor import Tier3ImportantRetainProcessor
from .tier4_critical_full_processor import Tier4CriticalFullProcessor

from .integrated_quality_assurance_system import IntegratedQualityAssuranceSystem
from .performance_monitoring_system import PerformanceMonitoringSystem

__version__ = "1.0.0"
__author__ = "FortiGate to NTOS Conversion Team"
__description__ = "四层优化策略 - 质量绝对优先的性能优化方案"

__all__ = [
    'FourTierOptimizationArchitecture',
    'OptimizationResult', 
    'OptimizationTier',
    'ProcessingStrategy',
    'SectionAnalysisResult',
    'QualityMetrics',
    'PerformanceMetrics',
    'Tier1SafeSkipProcessor',
    'Tier2ConditionalSkipProcessor',
    'Tier3ImportantRetainProcessor', 
    'Tier4CriticalFullProcessor',
    'IntegratedQualityAssuranceSystem',
    'PerformanceMonitoringSystem'
]
