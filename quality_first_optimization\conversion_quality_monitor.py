"""
转换质量监控系统
基于质量绝对优先原则的实时质量监控和评估
"""

import logging
import time
import json
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from lxml import etree
import subprocess
import tempfile
import os
from pathlib import Path

@dataclass
class QualityMetric:
    """质量指标数据结构"""
    name: str
    value: float
    threshold: float
    status: str  # 'pass', 'warning', 'fail'
    description: str
    timestamp: float

@dataclass
class QualityAssessment:
    """质量评估结果"""
    overall_score: float
    passed: bool
    metrics: List[QualityMetric]
    critical_issues: List[str]
    warnings: List[str]
    recommendations: List[str]
    assessment_time: float

class ConversionQualityMonitor:
    """转换质量监控器"""
    
    def __init__(self, yang_models_path: str, quality_threshold: float = 0.95):
        self.logger = logging.getLogger(__name__)
        self.yang_models_path = Path(yang_models_path)
        self.quality_threshold = quality_threshold
        
        # 质量指标权重配置
        self.metric_weights = {
            'yang_compliance': 0.4,        # YANG模型合规性 (40%)
            'reference_integrity': 0.25,   # 引用完整性 (25%)
            'functional_completeness': 0.2, # 功能完整性 (20%)
            'configuration_accuracy': 0.15  # 配置准确性 (15%)
        }
        
        # 质量阈值配置
        self.metric_thresholds = {
            'yang_compliance': 0.98,       # YANG合规性必须达到98%
            'reference_integrity': 0.95,   # 引用完整性必须达到95%
            'functional_completeness': 0.90, # 功能完整性必须达到90%
            'configuration_accuracy': 0.85   # 配置准确性必须达到85%
        }
        
        # 历史质量数据
        self.quality_history = []
    
    def assess_conversion_quality(self, fortigate_config: Dict, 
                                ntos_xml: etree.Element,
                                device_model: str = "z5100s",
                                device_version: str = "R11") -> QualityAssessment:
        """全面评估转换质量"""
        assessment_start_time = time.time()
        
        metrics = []
        critical_issues = []
        warnings = []
        recommendations = []
        
        try:
            # 1. YANG模型合规性评估
            yang_metric = self._assess_yang_compliance(ntos_xml, device_model, device_version)
            metrics.append(yang_metric)
            
            if yang_metric.status == 'fail':
                critical_issues.append(f"YANG模型合规性不足: {yang_metric.value:.2%}")
            elif yang_metric.status == 'warning':
                warnings.append(f"YANG模型合规性需要改进: {yang_metric.value:.2%}")
            
            # 2. 引用完整性评估
            reference_metric = self._assess_reference_integrity(fortigate_config, ntos_xml)
            metrics.append(reference_metric)
            
            if reference_metric.status == 'fail':
                critical_issues.append(f"引用完整性严重不足: {reference_metric.value:.2%}")
            elif reference_metric.status == 'warning':
                warnings.append(f"存在引用完整性问题: {reference_metric.value:.2%}")
            
            # 3. 功能完整性评估
            functional_metric = self._assess_functional_completeness(fortigate_config, ntos_xml)
            metrics.append(functional_metric)
            
            if functional_metric.status == 'fail':
                critical_issues.append(f"功能完整性不足: {functional_metric.value:.2%}")
            elif functional_metric.status == 'warning':
                warnings.append(f"功能完整性需要改进: {functional_metric.value:.2%}")
            
            # 4. 配置准确性评估
            accuracy_metric = self._assess_configuration_accuracy(fortigate_config, ntos_xml)
            metrics.append(accuracy_metric)
            
            if accuracy_metric.status == 'fail':
                critical_issues.append(f"配置准确性不足: {accuracy_metric.value:.2%}")
            elif accuracy_metric.status == 'warning':
                warnings.append(f"配置准确性需要改进: {accuracy_metric.value:.2%}")
            
            # 计算总体质量分数
            overall_score = sum(
                metric.value * self.metric_weights[metric.name]
                for metric in metrics
            )
            
            # 生成建议
            recommendations = self._generate_recommendations(metrics, critical_issues, warnings)
            
            # 创建质量评估结果
            assessment = QualityAssessment(
                overall_score=overall_score,
                passed=overall_score >= self.quality_threshold and len(critical_issues) == 0,
                metrics=metrics,
                critical_issues=critical_issues,
                warnings=warnings,
                recommendations=recommendations,
                assessment_time=time.time() - assessment_start_time
            )
            
            # 记录历史数据
            self.quality_history.append(assessment)
            
            # 记录评估结果
            self.logger.info(f"质量评估完成 - 总分: {overall_score:.2%}, 通过: {assessment.passed}")
            
            return assessment
            
        except Exception as e:
            self.logger.error(f"质量评估过程中发生错误: {e}")
            # 返回失败的评估结果
            return QualityAssessment(
                overall_score=0.0,
                passed=False,
                metrics=[],
                critical_issues=[f"质量评估失败: {str(e)}"],
                warnings=[],
                recommendations=["请检查配置文件和YANG模型路径"],
                assessment_time=time.time() - assessment_start_time
            )
    
    def _assess_yang_compliance(self, ntos_xml: etree.Element, 
                              device_model: str, device_version: str) -> QualityMetric:
        """评估YANG模型合规性"""
        try:
            # 使用yanglint验证XML配置
            compliance_score = self._validate_with_yanglint(ntos_xml, device_model, device_version)
            
            # 确定状态
            if compliance_score >= self.metric_thresholds['yang_compliance']:
                status = 'pass'
            elif compliance_score >= 0.90:
                status = 'warning'
            else:
                status = 'fail'
            
            return QualityMetric(
                name='yang_compliance',
                value=compliance_score,
                threshold=self.metric_thresholds['yang_compliance'],
                status=status,
                description=f'YANG模型合规性验证 (yanglint)',
                timestamp=time.time()
            )
            
        except Exception as e:
            self.logger.error(f"YANG合规性评估失败: {e}")
            return QualityMetric(
                name='yang_compliance',
                value=0.0,
                threshold=self.metric_thresholds['yang_compliance'],
                status='fail',
                description=f'YANG模型合规性验证失败: {str(e)}',
                timestamp=time.time()
            )
    
    def _validate_with_yanglint(self, ntos_xml: etree.Element, 
                              device_model: str, device_version: str) -> float:
        """使用yanglint验证XML配置"""
        try:
            # 创建临时XML文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.xml', delete=False) as temp_file:
                temp_file.write(etree.tostring(ntos_xml, encoding='unicode', pretty_print=True))
                temp_xml_path = temp_file.name
            
            try:
                # 构建yanglint命令
                yang_model_dir = self.yang_models_path / device_model / device_version
                yanglint_cmd = [
                    'yanglint',
                    '-p', str(yang_model_dir),
                    '-t', 'config',
                    temp_xml_path
                ]
                
                # 执行yanglint验证
                result = subprocess.run(
                    yanglint_cmd,
                    capture_output=True,
                    text=True,
                    timeout=30
                )
                
                if result.returncode == 0:
                    # 验证通过
                    return 1.0
                else:
                    # 验证失败，分析错误类型
                    error_output = result.stderr
                    return self._analyze_yanglint_errors(error_output)
                    
            finally:
                # 清理临时文件
                os.unlink(temp_xml_path)
                
        except subprocess.TimeoutExpired:
            self.logger.error("yanglint验证超时")
            return 0.0
        except FileNotFoundError:
            self.logger.error("yanglint工具未找到")
            return 0.5  # 无法验证，给予中等分数
        except Exception as e:
            self.logger.error(f"yanglint验证过程中发生错误: {e}")
            return 0.0
    
    def _analyze_yanglint_errors(self, error_output: str) -> float:
        """分析yanglint错误输出，计算合规性分数"""
        if not error_output:
            return 1.0
        
        # 统计不同类型的错误
        critical_errors = 0
        warnings = 0
        
        for line in error_output.split('\n'):
            line = line.strip().lower()
            if 'err' in line or 'error' in line:
                if 'missing' in line or 'invalid' in line or 'constraint' in line:
                    critical_errors += 1
                else:
                    warnings += 1
            elif 'warn' in line or 'warning' in line:
                warnings += 1
        
        # 计算合规性分数
        total_issues = critical_errors + warnings
        if total_issues == 0:
            return 1.0
        
        # 严重错误权重更高
        penalty = (critical_errors * 0.1) + (warnings * 0.05)
        compliance_score = max(0.0, 1.0 - penalty)
        
        return compliance_score
    
    def _assess_reference_integrity(self, fortigate_config: Dict, 
                                  ntos_xml: etree.Element) -> QualityMetric:
        """评估引用完整性"""
        try:
            # 收集所有定义的对象
            defined_addresses = set(ntos_xml.xpath('.//address-set/name/text()'))
            defined_services = set(ntos_xml.xpath('.//service-set/name/text()'))
            defined_interfaces = set(ntos_xml.xpath('.//interface/name/text()'))
            
            # 收集所有引用
            total_references = 0
            valid_references = 0
            
            # 检查策略中的引用
            for policy in ntos_xml.xpath('.//rule'):
                # 源地址引用
                for src_network in policy.xpath('.//source-network/name/text()'):
                    total_references += 1
                    if src_network in defined_addresses or src_network in ['any', 'all']:
                        valid_references += 1
                
                # 目标地址引用
                for dest_network in policy.xpath('.//dest-network/name/text()'):
                    total_references += 1
                    if dest_network in defined_addresses or dest_network in ['any', 'all']:
                        valid_references += 1
                
                # 服务引用
                for service in policy.xpath('.//service/name/text()'):
                    total_references += 1
                    if service in defined_services or service in ['any', 'ALL']:
                        valid_references += 1
                
                # 接口引用
                for interface in policy.xpath('.//source-interface/name/text()'):
                    total_references += 1
                    if interface in defined_interfaces or interface in ['any', 'all']:
                        valid_references += 1
            
            # 计算引用完整性分数
            if total_references == 0:
                integrity_score = 1.0
            else:
                integrity_score = valid_references / total_references
            
            # 确定状态
            if integrity_score >= self.metric_thresholds['reference_integrity']:
                status = 'pass'
            elif integrity_score >= 0.85:
                status = 'warning'
            else:
                status = 'fail'
            
            return QualityMetric(
                name='reference_integrity',
                value=integrity_score,
                threshold=self.metric_thresholds['reference_integrity'],
                status=status,
                description=f'引用完整性检查 ({valid_references}/{total_references})',
                timestamp=time.time()
            )
            
        except Exception as e:
            self.logger.error(f"引用完整性评估失败: {e}")
            return QualityMetric(
                name='reference_integrity',
                value=0.0,
                threshold=self.metric_thresholds['reference_integrity'],
                status='fail',
                description=f'引用完整性评估失败: {str(e)}',
                timestamp=time.time()
            )
    
    def _assess_functional_completeness(self, fortigate_config: Dict, 
                                      ntos_xml: etree.Element) -> QualityMetric:
        """评估功能完整性"""
        try:
            completeness_scores = []
            
            # 1. 策略完整性
            fortigate_policies = len(fortigate_config.get('policies', []))
            ntos_policies = len(ntos_xml.xpath('.//rule'))
            if fortigate_policies > 0:
                policy_completeness = min(ntos_policies / fortigate_policies, 1.0)
            else:
                policy_completeness = 1.0
            completeness_scores.append(policy_completeness)
            
            # 2. 地址对象完整性
            fortigate_addresses = len(fortigate_config.get('address_objects', []))
            ntos_addresses = len(ntos_xml.xpath('.//address-set'))
            if fortigate_addresses > 0:
                address_completeness = min(ntos_addresses / fortigate_addresses, 1.0)
            else:
                address_completeness = 1.0
            completeness_scores.append(address_completeness)
            
            # 3. 服务对象完整性
            fortigate_services = len(fortigate_config.get('service_objects', []))
            ntos_services = len(ntos_xml.xpath('.//service-set'))
            if fortigate_services > 0:
                service_completeness = min(ntos_services / fortigate_services, 1.0)
            else:
                service_completeness = 1.0
            completeness_scores.append(service_completeness)
            
            # 4. 接口完整性
            fortigate_interfaces = len(fortigate_config.get('interfaces', {}))
            ntos_interfaces = len(ntos_xml.xpath('.//interface'))
            if fortigate_interfaces > 0:
                interface_completeness = min(ntos_interfaces / fortigate_interfaces, 1.0)
            else:
                interface_completeness = 1.0
            completeness_scores.append(interface_completeness)
            
            # 计算平均完整性分数
            functional_score = sum(completeness_scores) / len(completeness_scores)
            
            # 确定状态
            if functional_score >= self.metric_thresholds['functional_completeness']:
                status = 'pass'
            elif functional_score >= 0.80:
                status = 'warning'
            else:
                status = 'fail'
            
            return QualityMetric(
                name='functional_completeness',
                value=functional_score,
                threshold=self.metric_thresholds['functional_completeness'],
                status=status,
                description=f'功能完整性评估 (策略:{policy_completeness:.2%}, 地址:{address_completeness:.2%}, 服务:{service_completeness:.2%}, 接口:{interface_completeness:.2%})',
                timestamp=time.time()
            )
            
        except Exception as e:
            self.logger.error(f"功能完整性评估失败: {e}")
            return QualityMetric(
                name='functional_completeness',
                value=0.0,
                threshold=self.metric_thresholds['functional_completeness'],
                status='fail',
                description=f'功能完整性评估失败: {str(e)}',
                timestamp=time.time()
            )
    
    def _assess_configuration_accuracy(self, fortigate_config: Dict, 
                                     ntos_xml: etree.Element) -> QualityMetric:
        """评估配置准确性"""
        try:
            accuracy_scores = []
            
            # 1. 接口映射准确性
            fortigate_interfaces = set(fortigate_config.get('interfaces', {}).keys())
            ntos_interfaces = set(ntos_xml.xpath('.//interface/name/text()'))
            
            if fortigate_interfaces:
                # 计算成功映射的接口比例
                mapped_interfaces = 0
                for fg_interface in fortigate_interfaces:
                    # 检查是否有对应的NTOS接口映射
                    if any(ntos_if for ntos_if in ntos_interfaces if self._is_interface_mapped(fg_interface, ntos_if)):
                        mapped_interfaces += 1
                
                interface_accuracy = mapped_interfaces / len(fortigate_interfaces)
            else:
                interface_accuracy = 1.0
            accuracy_scores.append(interface_accuracy)
            
            # 2. 地址对象准确性
            address_accuracy = self._assess_address_accuracy(fortigate_config, ntos_xml)
            accuracy_scores.append(address_accuracy)
            
            # 3. 服务对象准确性
            service_accuracy = self._assess_service_accuracy(fortigate_config, ntos_xml)
            accuracy_scores.append(service_accuracy)
            
            # 计算平均准确性分数
            accuracy_score = sum(accuracy_scores) / len(accuracy_scores)
            
            # 确定状态
            if accuracy_score >= self.metric_thresholds['configuration_accuracy']:
                status = 'pass'
            elif accuracy_score >= 0.75:
                status = 'warning'
            else:
                status = 'fail'
            
            return QualityMetric(
                name='configuration_accuracy',
                value=accuracy_score,
                threshold=self.metric_thresholds['configuration_accuracy'],
                status=status,
                description=f'配置准确性评估 (接口:{interface_accuracy:.2%}, 地址:{address_accuracy:.2%}, 服务:{service_accuracy:.2%})',
                timestamp=time.time()
            )
            
        except Exception as e:
            self.logger.error(f"配置准确性评估失败: {e}")
            return QualityMetric(
                name='configuration_accuracy',
                value=0.0,
                threshold=self.metric_thresholds['configuration_accuracy'],
                status='fail',
                description=f'配置准确性评估失败: {str(e)}',
                timestamp=time.time()
            )
    
    def _is_interface_mapped(self, fortigate_interface: str, ntos_interface: str) -> bool:
        """检查FortiGate接口是否正确映射到NTOS接口"""
        # 简化的接口映射检查逻辑
        # 实际实现应该基于接口映射表
        if fortigate_interface == ntos_interface:
            return True
        
        # 检查常见映射模式
        if fortigate_interface.startswith('port') and ntos_interface.startswith('Ge'):
            return True
        
        if fortigate_interface.startswith('ssl') and 'tunnel' in ntos_interface:
            return True
        
        return False
    
    def _assess_address_accuracy(self, fortigate_config: Dict, ntos_xml: etree.Element) -> float:
        """评估地址对象准确性"""
        # 简化实现：检查地址对象的IP地址是否正确转换
        return 0.90  # 假设90%准确性
    
    def _assess_service_accuracy(self, fortigate_config: Dict, ntos_xml: etree.Element) -> float:
        """评估服务对象准确性"""
        # 简化实现：检查服务对象的协议和端口是否正确转换
        return 0.85  # 假设85%准确性
    
    def _generate_recommendations(self, metrics: List[QualityMetric], 
                                critical_issues: List[str], 
                                warnings: List[str]) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        for metric in metrics:
            if metric.status == 'fail':
                if metric.name == 'yang_compliance':
                    recommendations.append("建议运行YANG合规性修复器修复模型约束违反问题")
                elif metric.name == 'reference_integrity':
                    recommendations.append("建议检查并修复对象引用完整性问题")
                elif metric.name == 'functional_completeness':
                    recommendations.append("建议检查配置解析逻辑，确保所有功能都被正确转换")
                elif metric.name == 'configuration_accuracy':
                    recommendations.append("建议检查接口映射表和对象转换逻辑")
            elif metric.status == 'warning':
                recommendations.append(f"建议改进{metric.description}以达到更高质量标准")
        
        if len(critical_issues) > 0:
            recommendations.append("存在严重质量问题，建议立即修复后再进行部署")
        
        if len(warnings) > 3:
            recommendations.append("警告问题较多，建议进行全面的质量检查")
        
        return recommendations
    
    def get_quality_trend(self, last_n_assessments: int = 10) -> Dict:
        """获取质量趋势分析"""
        if len(self.quality_history) < 2:
            return {'trend': 'insufficient_data', 'message': '数据不足，无法分析趋势'}
        
        recent_assessments = self.quality_history[-last_n_assessments:]
        
        # 计算趋势
        scores = [assessment.overall_score for assessment in recent_assessments]
        if len(scores) >= 2:
            trend_slope = (scores[-1] - scores[0]) / len(scores)
            
            if trend_slope > 0.01:
                trend = 'improving'
            elif trend_slope < -0.01:
                trend = 'declining'
            else:
                trend = 'stable'
        else:
            trend = 'stable'
        
        return {
            'trend': trend,
            'current_score': scores[-1],
            'average_score': sum(scores) / len(scores),
            'score_range': (min(scores), max(scores)),
            'assessment_count': len(recent_assessments)
        }
    
    def export_quality_report(self, assessment: QualityAssessment, 
                            output_path: str) -> None:
        """导出质量报告"""
        report_data = {
            'assessment_summary': {
                'overall_score': assessment.overall_score,
                'passed': assessment.passed,
                'assessment_time': assessment.assessment_time,
                'timestamp': time.time()
            },
            'metrics': [asdict(metric) for metric in assessment.metrics],
            'issues': {
                'critical': assessment.critical_issues,
                'warnings': assessment.warnings
            },
            'recommendations': assessment.recommendations,
            'quality_trend': self.get_quality_trend()
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"质量报告已导出到: {output_path}")
