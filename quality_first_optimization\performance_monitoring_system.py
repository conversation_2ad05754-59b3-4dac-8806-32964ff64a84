"""
性能监控与优化验证系统
实施性能监控机制，验证98.1%性能提升目标，建立持续优化反馈循环
"""

import logging
import time
import threading
import psutil
import gc
from typing import Dict, List, Set, Optional, Tuple, Any, Union
from dataclasses import dataclass, field
from enum import Enum
from collections import deque
import json
from pathlib import Path

from .four_tier_optimization_architecture import PerformanceMetrics, IPerformanceMonitor

class PerformanceMetricType(Enum):
    """性能指标类型"""
    PROCESSING_TIME = "processing_time"         # 处理时间
    MEMORY_USAGE = "memory_usage"              # 内存使用
    CPU_USAGE = "cpu_usage"                    # CPU使用率
    THROUGHPUT = "throughput"                  # 吞吐量
    LATENCY = "latency"                        # 延迟
    OPTIMIZATION_RATIO = "optimization_ratio"  # 优化比例
    QUALITY_SCORE = "quality_score"            # 质量分数

class MonitoringLevel(Enum):
    """监控级别"""
    BASIC = "basic"           # 基础监控
    DETAILED = "detailed"     # 详细监控
    COMPREHENSIVE = "comprehensive"  # 全面监控

@dataclass
class PerformanceSnapshot:
    """性能快照"""
    timestamp: float
    processing_time: float
    memory_usage_mb: float
    cpu_usage_percent: float
    sections_processed: int
    sections_optimized: int
    quality_score: float
    optimization_ratio: float
    details: Dict[str, Any] = field(default_factory=dict)

@dataclass
class PerformanceBenchmark:
    """性能基准"""
    baseline_processing_time: float
    target_improvement: float = 0.981  # 98.1%性能提升目标
    quality_threshold: float = 0.8275  # 82.75%质量基线
    max_memory_usage_mb: float = 1024   # 最大内存使用1GB
    max_cpu_usage_percent: float = 80   # 最大CPU使用80%

@dataclass
class OptimizationFeedback:
    """优化反馈"""
    tier: str
    strategy: str
    performance_gain: float
    quality_impact: float
    recommendation: str
    confidence: float

class PerformanceMonitoringSystem(IPerformanceMonitor):
    """性能监控与优化验证系统"""
    
    def __init__(self, monitoring_level: MonitoringLevel = MonitoringLevel.DETAILED):
        self.logger = logging.getLogger(__name__)
        self.monitoring_level = monitoring_level
        
        # 性能基准
        self.benchmark = PerformanceBenchmark(
            baseline_processing_time=8.6,  # 基于860段落 × 0.01秒的估算
            target_improvement=0.981,      # 98.1%性能提升目标
            quality_threshold=0.8275       # 82.75%质量基线
        )
        
        # 监控状态
        self.active_monitors: Dict[str, Dict[str, Any]] = {}
        self.performance_history: deque = deque(maxlen=1000)  # 保留最近1000个快照
        self.optimization_feedback: List[OptimizationFeedback] = []
        
        # 性能统计
        self.performance_stats = {
            'total_monitoring_sessions': 0,
            'average_performance_improvement': 0.0,
            'peak_performance_improvement': 0.0,
            'quality_maintained_sessions': 0,
            'optimization_feedback_count': 0,
            'tier_performance': {
                'tier1_safe_skip': {'time_saved': 0.0, 'sessions': 0},
                'tier2_conditional_skip': {'time_saved': 0.0, 'sessions': 0},
                'tier3_important_retain': {'time_saved': 0.0, 'sessions': 0},
                'tier4_critical_full': {'time_saved': 0.0, 'sessions': 0}
            }
        }
        
        # 监控线程锁
        self.monitor_lock = threading.RLock()
        
        # 自动优化建议
        self.auto_optimization_enabled = True
        self.feedback_threshold = 0.1  # 性能改进低于10%时提供反馈
    
    def start_monitoring(self, context: Dict[str, Any] = None) -> str:
        """开始性能监控"""
        monitor_id = f"monitor_{int(time.time() * 1000)}"
        
        with self.monitor_lock:
            # 初始化监控会话
            monitor_session = {
                'monitor_id': monitor_id,
                'start_time': time.time(),
                'start_memory': self._get_memory_usage(),
                'start_cpu': self._get_cpu_usage(),
                'context': context or {},
                'snapshots': [],
                'baseline_established': False,
                'performance_metrics': PerformanceMetrics()
            }
            
            self.active_monitors[monitor_id] = monitor_session
            self.performance_stats['total_monitoring_sessions'] += 1
            
            self.logger.info(f"开始性能监控会话: {monitor_id}")
            
            # 建立基线快照
            self._take_performance_snapshot(monitor_id, "baseline")
            
        return monitor_id
    
    def stop_monitoring(self, monitor_id: str) -> PerformanceMetrics:
        """停止性能监控并返回指标"""
        with self.monitor_lock:
            if monitor_id not in self.active_monitors:
                self.logger.warning(f"监控会话不存在: {monitor_id}")
                return PerformanceMetrics()
            
            monitor_session = self.active_monitors[monitor_id]
            end_time = time.time()
            
            # 最终快照
            self._take_performance_snapshot(monitor_id, "final")
            
            # 计算性能指标
            performance_metrics = self._calculate_performance_metrics(monitor_session, end_time)
            
            # 生成优化反馈
            if self.auto_optimization_enabled:
                feedback = self._generate_optimization_feedback(monitor_session, performance_metrics)
                if feedback:
                    self.optimization_feedback.append(feedback)
                    self.performance_stats['optimization_feedback_count'] += 1
            
            # 更新统计信息
            self._update_performance_stats(performance_metrics)
            
            # 保存到历史记录
            final_snapshot = PerformanceSnapshot(
                timestamp=end_time,
                processing_time=performance_metrics.optimized_processing_time,
                memory_usage_mb=self._get_memory_usage(),
                cpu_usage_percent=self._get_cpu_usage(),
                sections_processed=performance_metrics.sections_processed,
                sections_optimized=performance_metrics.sections_optimized,
                quality_score=monitor_session['context'].get('quality_score', 0.0),
                optimization_ratio=performance_metrics.optimization_ratio,
                details={
                    'monitor_id': monitor_id,
                    'performance_improvement': performance_metrics.performance_improvement,
                    'time_saved': performance_metrics.time_saved
                }
            )
            self.performance_history.append(final_snapshot)
            
            # 清理监控会话
            del self.active_monitors[monitor_id]
            
            self.logger.info(f"停止性能监控会话: {monitor_id} - 性能提升: {performance_metrics.performance_improvement:.1%}")
            
            return performance_metrics
    
    def get_realtime_metrics(self, monitor_id: str) -> PerformanceMetrics:
        """获取实时性能指标"""
        with self.monitor_lock:
            if monitor_id not in self.active_monitors:
                return PerformanceMetrics()
            
            monitor_session = self.active_monitors[monitor_id]
            current_time = time.time()
            
            # 实时快照
            self._take_performance_snapshot(monitor_id, "realtime")
            
            # 计算当前性能指标
            current_metrics = self._calculate_performance_metrics(monitor_session, current_time)
            
            return current_metrics
    
    def _take_performance_snapshot(self, monitor_id: str, snapshot_type: str) -> None:
        """拍摄性能快照"""
        try:
            monitor_session = self.active_monitors[monitor_id]
            current_time = time.time()
            
            # 收集性能数据
            memory_usage = self._get_memory_usage()
            cpu_usage = self._get_cpu_usage()
            
            # 从上下文获取处理信息
            context = monitor_session['context']
            sections_processed = context.get('sections_processed', 0)
            sections_optimized = context.get('sections_optimized', 0)
            quality_score = context.get('quality_score', 0.0)
            
            # 计算优化比例
            optimization_ratio = sections_optimized / sections_processed if sections_processed > 0 else 0.0
            
            # 计算处理时间
            processing_time = current_time - monitor_session['start_time']
            
            snapshot = PerformanceSnapshot(
                timestamp=current_time,
                processing_time=processing_time,
                memory_usage_mb=memory_usage,
                cpu_usage_percent=cpu_usage,
                sections_processed=sections_processed,
                sections_optimized=sections_optimized,
                quality_score=quality_score,
                optimization_ratio=optimization_ratio,
                details={
                    'snapshot_type': snapshot_type,
                    'monitor_id': monitor_id
                }
            )
            
            monitor_session['snapshots'].append(snapshot)
            
            # 详细监控级别记录更多信息
            if self.monitoring_level in [MonitoringLevel.DETAILED, MonitoringLevel.COMPREHENSIVE]:
                snapshot.details.update({
                    'gc_count': len(gc.get_objects()),
                    'thread_count': threading.active_count(),
                    'memory_percent': psutil.virtual_memory().percent
                })
            
            # 全面监控级别记录系统级信息
            if self.monitoring_level == MonitoringLevel.COMPREHENSIVE:
                snapshot.details.update({
                    'disk_usage': psutil.disk_usage('/').percent,
                    'network_io': dict(psutil.net_io_counters()._asdict()),
                    'process_info': {
                        'pid': psutil.Process().pid,
                        'num_threads': psutil.Process().num_threads(),
                        'memory_info': dict(psutil.Process().memory_info()._asdict())
                    }
                })
            
        except Exception as e:
            self.logger.error(f"拍摄性能快照失败: {e}")
    
    def _calculate_performance_metrics(self, monitor_session: Dict[str, Any], 
                                     end_time: float) -> PerformanceMetrics:
        """计算性能指标"""
        snapshots = monitor_session['snapshots']
        if not snapshots:
            return PerformanceMetrics()
        
        # 获取基线和最终快照
        baseline_snapshot = snapshots[0] if snapshots else None
        final_snapshot = snapshots[-1] if snapshots else None
        
        if not baseline_snapshot or not final_snapshot:
            return PerformanceMetrics()
        
        # 计算处理时间
        optimized_processing_time = final_snapshot.processing_time
        original_processing_time = self.benchmark.baseline_processing_time
        
        # 如果有实际的原始处理时间，使用实际值
        context = monitor_session['context']
        if 'original_processing_time' in context:
            original_processing_time = context['original_processing_time']
        
        # 计算性能指标
        metrics = PerformanceMetrics(
            original_processing_time=original_processing_time,
            optimized_processing_time=optimized_processing_time,
            sections_processed=final_snapshot.sections_processed,
            sections_optimized=final_snapshot.sections_optimized
        )
        
        # 计算派生指标
        metrics.calculate_metrics()
        
        return metrics
    
    def _generate_optimization_feedback(self, monitor_session: Dict[str, Any], 
                                      metrics: PerformanceMetrics) -> Optional[OptimizationFeedback]:
        """生成优化反馈"""
        try:
            # 检查是否需要反馈
            if metrics.performance_improvement >= self.feedback_threshold:
                return None  # 性能提升足够，无需反馈
            
            context = monitor_session['context']
            
            # 分析性能瓶颈
            bottleneck_analysis = self._analyze_performance_bottlenecks(monitor_session, metrics)
            
            # 生成建议
            recommendation = self._generate_performance_recommendation(bottleneck_analysis, metrics)
            
            # 确定最需要优化的层级
            tier_performance = self._analyze_tier_performance(context)
            worst_performing_tier = min(tier_performance.keys(), 
                                      key=lambda t: tier_performance[t]['efficiency'])
            
            feedback = OptimizationFeedback(
                tier=worst_performing_tier,
                strategy="performance_optimization",
                performance_gain=metrics.performance_improvement,
                quality_impact=context.get('quality_score', 0.0),
                recommendation=recommendation,
                confidence=0.8
            )
            
            return feedback
            
        except Exception as e:
            self.logger.error(f"生成优化反馈失败: {e}")
            return None
    
    def _analyze_performance_bottlenecks(self, monitor_session: Dict[str, Any], 
                                       metrics: PerformanceMetrics) -> Dict[str, Any]:
        """分析性能瓶颈"""
        snapshots = monitor_session['snapshots']
        bottlenecks = {
            'memory_pressure': False,
            'cpu_intensive': False,
            'processing_inefficient': False,
            'optimization_insufficient': False
        }
        
        if snapshots:
            # 分析内存压力
            max_memory = max(s.memory_usage_mb for s in snapshots)
            if max_memory > self.benchmark.max_memory_usage_mb:
                bottlenecks['memory_pressure'] = True
            
            # 分析CPU密集度
            avg_cpu = sum(s.cpu_usage_percent for s in snapshots) / len(snapshots)
            if avg_cpu > self.benchmark.max_cpu_usage_percent:
                bottlenecks['cpu_intensive'] = True
            
            # 分析处理效率
            if metrics.optimized_processing_time > self.benchmark.baseline_processing_time * 0.5:
                bottlenecks['processing_inefficient'] = True
            
            # 分析优化不足
            if metrics.optimization_ratio < 0.2:  # 优化比例低于20%
                bottlenecks['optimization_insufficient'] = True
        
        return bottlenecks
    
    def _generate_performance_recommendation(self, bottleneck_analysis: Dict[str, Any], 
                                           metrics: PerformanceMetrics) -> str:
        """生成性能建议"""
        recommendations = []
        
        if bottleneck_analysis['memory_pressure']:
            recommendations.append("优化内存使用，考虑增加缓存清理或减少内存占用")
        
        if bottleneck_analysis['cpu_intensive']:
            recommendations.append("优化CPU密集型操作，考虑并行处理或算法优化")
        
        if bottleneck_analysis['processing_inefficient']:
            recommendations.append("提高处理效率，扩大安全跳过和条件跳过的范围")
        
        if bottleneck_analysis['optimization_insufficient']:
            recommendations.append("增加优化覆盖率，识别更多可优化的段落类型")
        
        if not recommendations:
            if metrics.performance_improvement < 0.5:
                recommendations.append("性能提升不足，建议全面审查优化策略")
            else:
                recommendations.append("性能表现良好，可以考虑进一步微调")
        
        return "; ".join(recommendations)
    
    def _analyze_tier_performance(self, context: Dict[str, Any]) -> Dict[str, Dict[str, float]]:
        """分析各层级性能"""
        tier_performance = {
            'tier1_safe_skip': {'efficiency': 0.9, 'coverage': 0.16},
            'tier2_conditional_skip': {'efficiency': 0.8, 'coverage': 0.02},
            'tier3_important_retain': {'efficiency': 0.6, 'coverage': 0.06},
            'tier4_critical_full': {'efficiency': 0.0, 'coverage': 0.76}
        }
        
        # 从上下文中获取实际性能数据
        for tier in tier_performance.keys():
            if f'{tier}_time_saved' in context:
                time_saved = context[f'{tier}_time_saved']
                sections_count = context.get(f'{tier}_sections', 1)
                tier_performance[tier]['efficiency'] = time_saved / sections_count
        
        return tier_performance
    
    def _update_performance_stats(self, metrics: PerformanceMetrics) -> None:
        """更新性能统计信息"""
        # 更新平均性能提升
        current_avg = self.performance_stats['average_performance_improvement']
        total_sessions = self.performance_stats['total_monitoring_sessions']
        
        new_avg = (current_avg * (total_sessions - 1) + metrics.performance_improvement) / total_sessions
        self.performance_stats['average_performance_improvement'] = new_avg
        
        # 更新峰值性能提升
        if metrics.performance_improvement > self.performance_stats['peak_performance_improvement']:
            self.performance_stats['peak_performance_improvement'] = metrics.performance_improvement
        
        # 更新质量维持会话数
        if metrics.sections_processed > 0:  # 假设有处理的会话都维持了质量
            self.performance_stats['quality_maintained_sessions'] += 1
    
    def _get_memory_usage(self) -> float:
        """获取内存使用量（MB）"""
        try:
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024  # 转换为MB
        except Exception:
            return 0.0
    
    def _get_cpu_usage(self) -> float:
        """获取CPU使用率（%）"""
        try:
            return psutil.cpu_percent(interval=0.1)
        except Exception:
            return 0.0
    
    def validate_performance_target(self, metrics: PerformanceMetrics) -> Dict[str, Any]:
        """验证性能目标达成情况"""
        validation_result = {
            'target_achieved': False,
            'performance_improvement': metrics.performance_improvement,
            'target_improvement': self.benchmark.target_improvement,
            'achievement_rate': 0.0,
            'quality_maintained': True,
            'recommendations': []
        }
        
        # 计算目标达成率
        if self.benchmark.target_improvement > 0:
            validation_result['achievement_rate'] = metrics.performance_improvement / self.benchmark.target_improvement
        
        # 检查目标是否达成
        validation_result['target_achieved'] = metrics.performance_improvement >= self.benchmark.target_improvement
        
        # 生成建议
        if not validation_result['target_achieved']:
            gap = self.benchmark.target_improvement - metrics.performance_improvement
            validation_result['recommendations'].append(
                f"性能提升缺口{gap:.1%}，建议扩大优化范围或提高优化效率"
            )
        else:
            validation_result['recommendations'].append("性能目标已达成，可以考虑设定更高目标")
        
        return validation_result
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        recent_snapshots = list(self.performance_history)[-100:]  # 最近100个快照
        
        report = {
            'monitoring_summary': {
                'total_sessions': self.performance_stats['total_monitoring_sessions'],
                'average_improvement': self.performance_stats['average_performance_improvement'],
                'peak_improvement': self.performance_stats['peak_performance_improvement'],
                'quality_maintained_rate': (
                    self.performance_stats['quality_maintained_sessions'] / 
                    max(self.performance_stats['total_monitoring_sessions'], 1)
                )
            },
            'performance_benchmark': {
                'baseline_processing_time': self.benchmark.baseline_processing_time,
                'target_improvement': self.benchmark.target_improvement,
                'quality_threshold': self.benchmark.quality_threshold
            },
            'recent_performance': {
                'snapshots_count': len(recent_snapshots),
                'average_processing_time': (
                    sum(s.processing_time for s in recent_snapshots) / len(recent_snapshots)
                    if recent_snapshots else 0.0
                ),
                'average_optimization_ratio': (
                    sum(s.optimization_ratio for s in recent_snapshots) / len(recent_snapshots)
                    if recent_snapshots else 0.0
                ),
                'average_quality_score': (
                    sum(s.quality_score for s in recent_snapshots) / len(recent_snapshots)
                    if recent_snapshots else 0.0
                )
            },
            'tier_performance': self.performance_stats['tier_performance'],
            'optimization_feedback': {
                'total_feedback_count': len(self.optimization_feedback),
                'recent_feedback': [
                    {
                        'tier': f.tier,
                        'strategy': f.strategy,
                        'performance_gain': f.performance_gain,
                        'recommendation': f.recommendation,
                        'confidence': f.confidence
                    }
                    for f in self.optimization_feedback[-5:]  # 最近5个反馈
                ]
            },
            'system_resources': {
                'current_memory_mb': self._get_memory_usage(),
                'current_cpu_percent': self._get_cpu_usage(),
                'memory_limit_mb': self.benchmark.max_memory_usage_mb,
                'cpu_limit_percent': self.benchmark.max_cpu_usage_percent
            }
        }
        
        return report
    
    def establish_continuous_optimization_loop(self) -> None:
        """建立持续优化反馈循环"""
        self.logger.info("建立持续优化反馈循环")
        
        # 分析历史性能数据
        if len(self.performance_history) >= 10:
            recent_performance = list(self.performance_history)[-10:]
            
            # 计算性能趋势
            performance_trend = self._calculate_performance_trend(recent_performance)
            
            # 生成优化建议
            optimization_suggestions = self._generate_continuous_optimization_suggestions(performance_trend)
            
            # 记录优化建议
            for suggestion in optimization_suggestions:
                feedback = OptimizationFeedback(
                    tier="continuous_optimization",
                    strategy="trend_analysis",
                    performance_gain=performance_trend.get('average_improvement', 0.0),
                    quality_impact=performance_trend.get('average_quality', 0.0),
                    recommendation=suggestion,
                    confidence=0.7
                )
                self.optimization_feedback.append(feedback)
        
        self.logger.info("持续优化反馈循环已建立")
    
    def _calculate_performance_trend(self, snapshots: List[PerformanceSnapshot]) -> Dict[str, float]:
        """计算性能趋势"""
        if len(snapshots) < 2:
            return {}
        
        # 计算平均值
        avg_processing_time = sum(s.processing_time for s in snapshots) / len(snapshots)
        avg_optimization_ratio = sum(s.optimization_ratio for s in snapshots) / len(snapshots)
        avg_quality_score = sum(s.quality_score for s in snapshots) / len(snapshots)
        
        # 计算趋势（简单线性趋势）
        time_trend = (snapshots[-1].processing_time - snapshots[0].processing_time) / len(snapshots)
        optimization_trend = (snapshots[-1].optimization_ratio - snapshots[0].optimization_ratio) / len(snapshots)
        quality_trend = (snapshots[-1].quality_score - snapshots[0].quality_score) / len(snapshots)
        
        return {
            'average_processing_time': avg_processing_time,
            'average_optimization_ratio': avg_optimization_ratio,
            'average_quality': avg_quality_score,
            'time_trend': time_trend,
            'optimization_trend': optimization_trend,
            'quality_trend': quality_trend,
            'average_improvement': 1.0 - (avg_processing_time / self.benchmark.baseline_processing_time)
        }
    
    def _generate_continuous_optimization_suggestions(self, trend: Dict[str, float]) -> List[str]:
        """生成持续优化建议"""
        suggestions = []
        
        # 基于处理时间趋势
        if trend.get('time_trend', 0) > 0:
            suggestions.append("处理时间呈上升趋势，建议检查是否有性能退化")
        
        # 基于优化比例趋势
        if trend.get('optimization_trend', 0) < 0:
            suggestions.append("优化比例呈下降趋势，建议扩大优化覆盖范围")
        
        # 基于质量趋势
        if trend.get('quality_trend', 0) < 0:
            suggestions.append("质量分数呈下降趋势，建议加强质量保障措施")
        
        # 基于整体性能
        avg_improvement = trend.get('average_improvement', 0)
        if avg_improvement < self.benchmark.target_improvement:
            suggestions.append(f"平均性能提升({avg_improvement:.1%})未达到目标({self.benchmark.target_improvement:.1%})")
        
        return suggestions
    
    def save_performance_report(self, output_path: str) -> None:
        """保存性能报告"""
        try:
            report = self.get_performance_report()
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"性能报告已保存到: {output_path}")
            
        except Exception as e:
            self.logger.error(f"保存性能报告失败: {e}")
    
    def get_monitoring_statistics(self) -> Dict[str, Any]:
        """获取监控统计信息"""
        return {
            'monitoring_level': self.monitoring_level.value,
            'active_monitors_count': len(self.active_monitors),
            'performance_history_size': len(self.performance_history),
            'optimization_feedback_count': len(self.optimization_feedback),
            'performance_stats': self.performance_stats.copy(),
            'benchmark': {
                'baseline_processing_time': self.benchmark.baseline_processing_time,
                'target_improvement': self.benchmark.target_improvement,
                'quality_threshold': self.benchmark.quality_threshold,
                'max_memory_usage_mb': self.benchmark.max_memory_usage_mb,
                'max_cpu_usage_percent': self.benchmark.max_cpu_usage_percent
            },
            'auto_optimization_enabled': self.auto_optimization_enabled,
            'feedback_threshold': self.feedback_threshold
        }
