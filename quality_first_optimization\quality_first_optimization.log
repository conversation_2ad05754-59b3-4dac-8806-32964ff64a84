2025-08-03 16:11:58,394 - __main__ - INFO - ================================================================================
2025-08-03 16:11:58,394 - __main__ - INFO - 开始FortiGate到NTOS转换质量优先性能优化
2025-08-03 16:11:58,395 - __main__ - INFO - ================================================================================
2025-08-03 16:11:58,395 - __main__ - INFO - 阶段1: 配置文件解析和预处理
2025-08-03 16:11:58,501 - __main__ - INFO - 成功解析配置文件，共找到 860 个配置段落
2025-08-03 16:11:58,502 - __main__ - INFO - 段落类型分布: {'system': 35, 'fwgrp-permission': 1, 'gui-dashboard': 4, 'widget': 61, 'firewall': 27, 'webfilter': 6, 'log': 8, 'filter': 1, 'free-style': 1, 'endpoint-control': 1, 'ips': 3, 'fields': 1, 'actions': 9, 'switch-controller': 15, 'port': 1, 'ip-range': 2, 'reserved-address': 2, 'vpn': 8, 'entries': 63, 'web-proxy': 2, 'application': 1, 'dlp': 2, 'web': 20, 'icap': 1, 'user': 8, 'match': 7, 'authentication-rule': 1, 'bookmarks': 2, 'voip': 1, 'sip': 1, 'zone': 1, 'dnsfilter': 2, 'filters': 35, 'ftgd-dns': 5, 'domain-filter': 3, 'antivirus': 2, 'http': 28, 'ftp': 24, 'imap': 32, 'pop3': 32, 'smtp': 32, 'cifs': 17, 'mapi': 23, 'ssh': 43, 'file-filter': 1, 'rules': 2, 'ftgd-wf': 23, 'emailfilter': 1, 'nntp': 10, 'dns': 10, 'https': 26, 'ftps': 26, 'imaps': 26, 'pop3s': 26, 'smtps': 26, 'dot': 28, 'ssl-exempt': 3, 'ssl': 2, 'waf': 1, 'main-class': 44, 'header-length': 1, 'content-length': 1, 'param-length': 1, 'line-length': 1, 'url-param-length': 1, 'version': 1, 'method': 5, 'hostname': 1, 'malformed': 1, 'max-cookie': 1, 'max-header-line': 1, 'max-url-param': 1, 'max-range-segment': 1, 'anomaly': 2, 'med-network-policy': 1, 'med-location-service': 1, 'map': 1, 'cos-queue': 2, 'wireless-controller': 4, 'alertemail': 1, 'router': 2}
2025-08-03 16:11:58,505 - __main__ - INFO - 解析完成 - 配置段落数: 860
2025-08-03 16:11:58,505 - __main__ - INFO - 阶段2: 质量基线建立
2025-08-03 16:11:58,515 - quality_first_optimization.conversion_quality_monitor - ERROR - yanglint工具未找到
2025-08-03 16:11:58,516 - quality_first_optimization.conversion_quality_monitor - INFO - 质量评估完成 - 总分: 78.75%, 通过: False
2025-08-03 16:11:58,516 - __main__ - INFO - 基线质量评估完成 - 分数: 78.75%
2025-08-03 16:11:58,516 - __main__ - WARNING - 基线质量不达标，尝试自动修复...
2025-08-03 16:11:58,517 - quality_first_optimization.yang_compliance_fixer - INFO - 所有YANG约束修复验证通过
2025-08-03 16:11:58,524 - quality_first_optimization.conversion_quality_monitor - ERROR - yanglint工具未找到
2025-08-03 16:11:58,524 - quality_first_optimization.conversion_quality_monitor - INFO - 质量评估完成 - 总分: 78.75%, 通过: False
2025-08-03 16:11:58,525 - __main__ - INFO - 质量修复完成 - 修复问题数: 0
2025-08-03 16:11:58,525 - __main__ - INFO - 修复后质量分数: 78.75%
2025-08-03 16:11:58,525 - __main__ - WARNING - 质量基线不达标，但继续优化流程...
2025-08-03 16:11:58,525 - __main__ - INFO - 阶段3: 质量优先性能优化
2025-08-03 16:11:58,525 - quality_first_optimization.quality_first_performance_optimizer - INFO - 开始质量优先性能优化...
2025-08-03 16:11:58,532 - quality_first_optimization.conversion_quality_monitor - ERROR - yanglint工具未找到
2025-08-03 16:11:58,533 - quality_first_optimization.conversion_quality_monitor - INFO - 质量评估完成 - 总分: 78.75%, 通过: False
2025-08-03 16:11:58,533 - quality_first_optimization.quality_first_performance_optimizer - INFO - 质量基线建立完成 - 分数: 78.75%
2025-08-03 16:11:58,533 - quality_first_optimization.quality_first_performance_optimizer - WARNING - 基线质量不达标，先进行质量修复...
2025-08-03 16:11:58,534 - quality_first_optimization.yang_compliance_fixer - INFO - 所有YANG约束修复验证通过
2025-08-03 16:11:58,540 - quality_first_optimization.conversion_quality_monitor - ERROR - yanglint工具未找到
2025-08-03 16:11:58,541 - quality_first_optimization.conversion_quality_monitor - INFO - 质量评估完成 - 总分: 78.75%, 通过: False
2025-08-03 16:11:58,541 - quality_first_optimization.quality_first_performance_optimizer - INFO - 质量修复后分数: 78.75%
2025-08-03 16:11:58,555 - quality_first_optimization.conversion_quality_monitor - ERROR - yanglint工具未找到
2025-08-03 16:11:58,556 - quality_first_optimization.conversion_quality_monitor - INFO - 质量评估完成 - 总分: 78.75%, 通过: False
2025-08-03 16:11:58,556 - quality_first_optimization.quality_first_performance_optimizer - INFO - 优化后质量分数: 78.75%
2025-08-03 16:11:58,557 - quality_first_optimization.quality_first_performance_optimizer - INFO - 优化完成 - 性能提升: 100.0%, 质量保持: True
2025-08-03 16:11:58,557 - __main__ - INFO - 阶段4: 最终质量验证
2025-08-03 16:11:58,557 - __main__ - WARNING - 最终质量验证未达标 - 分数: 78.75%
2025-08-03 16:11:58,557 - __main__ - INFO - 性能提升: 100.0%
2025-08-03 16:11:58,557 - __main__ - INFO - 阶段5: 结果汇总和报告
2025-08-03 16:11:58,558 - __main__ - INFO - ================================================================================
2025-08-03 16:11:58,558 - __main__ - INFO - 质量优先性能优化实施完成
2025-08-03 16:11:58,558 - __main__ - INFO - 性能提升: 100.0%
2025-08-03 16:11:58,558 - __main__ - INFO - 质量分数: 78.75%
2025-08-03 16:11:58,558 - __main__ - INFO - ================================================================================
