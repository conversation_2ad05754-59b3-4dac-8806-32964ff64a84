"""
四层优化策略集成测试执行脚本
执行完整的集成测试并生成详细报告
"""

import logging
import sys
import argparse
from pathlib import Path
from datetime import datetime

from integration_test_suite import IntegrationTestSuite

def setup_logging(log_level: str = "INFO") -> None:
    """设置日志配置"""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler(f'integration_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
        ]
    )

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='四层优化策略集成测试')
    parser.add_argument('--log-level', default='INFO', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='日志级别')
    parser.add_argument('--output-dir', default='test_reports', 
                       help='测试报告输出目录')
    parser.add_argument('--quality-target', type=float, default=0.95,
                       help='质量目标分数')
    parser.add_argument('--performance-target', type=float, default=0.981,
                       help='性能提升目标')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.log_level)
    logger = logging.getLogger(__name__)
    
    logger.info("🚀 开始执行四层优化策略集成测试")
    
    try:
        # 创建输出目录
        output_dir = Path(args.output_dir)
        output_dir.mkdir(exist_ok=True)
        
        # 初始化测试套件
        test_suite = IntegrationTestSuite()
        test_suite.quality_target = args.quality_target
        test_suite.performance_target = args.performance_target
        
        logger.info(f"📋 测试配置:")
        logger.info(f"   质量目标: {args.quality_target:.1%}")
        logger.info(f"   性能目标: {args.performance_target:.1%}")
        logger.info(f"   测试用例数量: {len(test_suite.test_cases)}")
        
        # 执行集成测试
        logger.info("🔍 执行集成测试...")
        test_report = test_suite.run_integration_tests()
        
        # 保存测试报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_path = output_dir / f"integration_test_report_{timestamp}.json"
        test_suite.save_test_report(test_report, str(report_path))
        
        # 验证部署就绪性
        deployment_validation = test_suite.validate_deployment_readiness(test_report)
        
        # 输出测试结果摘要
        logger.info("📊 测试结果摘要:")
        logger.info(f"   总测试数: {test_report.total_tests}")
        logger.info(f"   通过测试: {test_report.passed_tests}")
        logger.info(f"   失败测试: {test_report.failed_tests}")
        logger.info(f"   跳过测试: {test_report.skipped_tests}")
        logger.info(f"   错误测试: {test_report.error_tests}")
        logger.info(f"   成功率: {test_report.overall_success_rate:.1%}")
        logger.info(f"   质量分数: {test_report.quality_score:.1%}")
        logger.info(f"   性能提升: {test_report.performance_improvement:.1%}")
        logger.info(f"   执行时间: {test_report.execution_time:.2f}秒")
        
        # 输出部署就绪性评估
        logger.info("🚀 部署就绪性评估:")
        logger.info(f"   部署就绪: {'✅ 是' if deployment_validation['deployment_ready'] else '❌ 否'}")
        logger.info(f"   就绪度分数: {deployment_validation['deployment_readiness_score']:.1%}")
        
        if deployment_validation['blocking_issues']:
            logger.warning("⚠️  阻塞问题:")
            for issue in deployment_validation['blocking_issues']:
                logger.warning(f"     - {issue}")
        
        # 输出建议
        logger.info("💡 建议:")
        for recommendation in test_report.recommendations:
            logger.info(f"   - {recommendation}")
        
        # 输出详细的失败测试信息
        failed_tests = [r for r in test_report.test_results if r.status.value == 'failed']
        if failed_tests:
            logger.warning("❌ 失败的测试:")
            for test_result in failed_tests:
                logger.warning(f"   - {test_result.test_case.name}: {test_result.error_message}")
        
        # 输出关键测试状态
        critical_tests = [r for r in test_report.test_results if r.test_case.severity.value == 'critical']
        critical_passed = len([r for r in critical_tests if r.status.value == 'passed'])
        logger.info(f"🔒 关键测试状态: {critical_passed}/{len(critical_tests)} 通过")
        
        # 根据测试结果设置退出码
        if deployment_validation['deployment_ready']:
            logger.info("✅ 集成测试完成 - 系统已准备好部署")
            sys.exit(0)
        else:
            logger.error("❌ 集成测试完成 - 系统尚未准备好部署")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"💥 集成测试执行失败: {e}")
        sys.exit(2)

if __name__ == "__main__":
    main()
