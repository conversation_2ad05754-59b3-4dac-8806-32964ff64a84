"""
第一层安全跳过策略验证器
验证164种模式的安全跳过策略实施效果
"""

import logging
import time
from typing import Dict, List, Tuple, Any, Set
from dataclasses import dataclass
import json
from pathlib import Path

from .tier1_safe_skip_processor import Tier1SafeSkipProcessor, SafeSkipCategory

@dataclass
class ValidationResult:
    """验证结果"""
    success: bool
    total_sections: int
    matched_sections: int
    match_rate: float
    time_saved: float
    quality_impact: str
    errors: List[str]
    warnings: List[str]
    recommendations: List[str]

class Tier1SafeSkipValidator:
    """第一层安全跳过策略验证器"""
    
    def __init__(self, processor: Tier1SafeSkipProcessor):
        self.logger = logging.getLogger(__name__)
        self.processor = processor
        
        # 验证标准
        self.validation_criteria = {
            'min_match_rate': 0.15,  # 最低15%匹配率（基于141/860的实际比例）
            'min_time_saved': 10.0,  # 最低10秒时间节省
            'max_false_positive_rate': 0.0,  # 最大0%误跳过率
            'required_categories': {  # 必需的类别覆盖
                SafeSkipCategory.WEB_INTERFACE: 50,  # 至少50个Web界面段落
                SafeSkipCategory.APPLICATION_CONTROL: 30,  # 至少30个应用控制段落
                SafeSkipCategory.CACHE_TEMPORARY: 50,  # 至少50个缓存段落
            }
        }
    
    def validate_implementation(self, test_sections: List[Tuple[str, List[str]]]) -> ValidationResult:
        """验证第一层安全跳过策略实施"""
        self.logger.info("开始验证第一层安全跳过策略实施")
        
        validation_start_time = time.time()
        errors = []
        warnings = []
        recommendations = []
        
        try:
            # 1. 基础功能验证
            basic_validation = self._validate_basic_functionality(test_sections)
            if not basic_validation['success']:
                errors.extend(basic_validation['errors'])
            
            # 2. 模式匹配验证
            pattern_validation = self._validate_pattern_matching(test_sections)
            if pattern_validation['match_rate'] < self.validation_criteria['min_match_rate']:
                errors.append(f"匹配率({pattern_validation['match_rate']:.1%})低于最低要求({self.validation_criteria['min_match_rate']:.1%})")
            
            # 3. 时间节省验证
            time_validation = self._validate_time_savings(test_sections)
            if time_validation['total_time_saved'] < self.validation_criteria['min_time_saved']:
                warnings.append(f"时间节省({time_validation['total_time_saved']:.1f}秒)低于目标({self.validation_criteria['min_time_saved']:.1f}秒)")
            
            # 4. 类别覆盖验证
            category_validation = self._validate_category_coverage(test_sections)
            for category, min_count in self.validation_criteria['required_categories'].items():
                actual_count = category_validation['category_counts'].get(category, 0)
                if actual_count < min_count:
                    warnings.append(f"{category.value}类别段落数({actual_count})低于预期({min_count})")
            
            # 5. 安全性验证
            safety_validation = self._validate_safety(test_sections)
            if not safety_validation['is_safe']:
                errors.extend(safety_validation['safety_issues'])
            
            # 6. 质量影响验证
            quality_validation = self._validate_quality_impact(test_sections)
            
            # 生成建议
            recommendations = self._generate_validation_recommendations(
                pattern_validation, time_validation, category_validation, safety_validation
            )
            
            # 计算总体验证结果
            success = len(errors) == 0
            
            validation_result = ValidationResult(
                success=success,
                total_sections=len(test_sections),
                matched_sections=pattern_validation['matched_sections'],
                match_rate=pattern_validation['match_rate'],
                time_saved=time_validation['total_time_saved'],
                quality_impact=quality_validation['impact_level'],
                errors=errors,
                warnings=warnings,
                recommendations=recommendations
            )
            
            validation_time = time.time() - validation_start_time
            self.logger.info(f"第一层安全跳过策略验证完成 - 成功: {success}, 耗时: {validation_time:.2f}秒")
            
            return validation_result
            
        except Exception as e:
            self.logger.error(f"验证过程中发生错误: {e}")
            return ValidationResult(
                success=False,
                total_sections=len(test_sections),
                matched_sections=0,
                match_rate=0.0,
                time_saved=0.0,
                quality_impact='unknown',
                errors=[f"验证过程异常: {str(e)}"],
                warnings=[],
                recommendations=["请检查验证器配置和测试数据"]
            )
    
    def _validate_basic_functionality(self, test_sections: List[Tuple[str, List[str]]]) -> Dict[str, Any]:
        """验证基础功能"""
        errors = []
        
        try:
            # 测试分类功能
            if test_sections:
                test_section_name, test_section_content = test_sections[0]
                classification_result = self.processor.classify_section(
                    test_section_name, test_section_content
                )
                
                # 验证分类结果结构
                required_fields = ['section_name', 'tier', 'strategy', 'confidence']
                for field in required_fields:
                    if not hasattr(classification_result, field):
                        errors.append(f"分类结果缺少必需字段: {field}")
            
            # 测试处理功能
            if test_sections and not errors:
                test_section_name, test_section_content = test_sections[0]
                classification_result = self.processor.classify_section(
                    test_section_name, test_section_content
                )
                
                if classification_result.strategy.value in [s.value for s in self.processor.get_supported_strategies()]:
                    processing_result = self.processor.process_section(
                        test_section_name, test_section_content, classification_result
                    )
                    
                    # 验证处理结果结构
                    if 'action' not in processing_result:
                        errors.append("处理结果缺少action字段")
            
            # 测试统计功能
            stats = self.processor.get_classification_statistics()
            if not isinstance(stats, dict):
                errors.append("统计信息格式错误")
            
        except Exception as e:
            errors.append(f"基础功能测试异常: {str(e)}")
        
        return {
            'success': len(errors) == 0,
            'errors': errors
        }
    
    def _validate_pattern_matching(self, test_sections: List[Tuple[str, List[str]]]) -> Dict[str, Any]:
        """验证模式匹配"""
        matched_sections = 0
        total_sections = len(test_sections)
        
        for section_name, section_content in test_sections:
            classification_result = self.processor.classify_section(section_name, section_content)
            if classification_result.tier.value == 'safe_skip':
                matched_sections += 1
        
        match_rate = matched_sections / total_sections if total_sections > 0 else 0.0
        
        return {
            'matched_sections': matched_sections,
            'total_sections': total_sections,
            'match_rate': match_rate
        }
    
    def _validate_time_savings(self, test_sections: List[Tuple[str, List[str]]]) -> Dict[str, Any]:
        """验证时间节省"""
        total_time_saved = 0.0
        section_time_savings = []
        
        for section_name, section_content in test_sections:
            classification_result = self.processor.classify_section(section_name, section_content)
            if classification_result.tier.value == 'safe_skip':
                total_time_saved += classification_result.estimated_time_saved
                section_time_savings.append({
                    'section_name': section_name,
                    'time_saved': classification_result.estimated_time_saved
                })
        
        return {
            'total_time_saved': total_time_saved,
            'section_time_savings': section_time_savings,
            'average_time_saved': total_time_saved / len(section_time_savings) if section_time_savings else 0.0
        }
    
    def _validate_category_coverage(self, test_sections: List[Tuple[str, List[str]]]) -> Dict[str, Any]:
        """验证类别覆盖"""
        category_counts = {category: 0 for category in SafeSkipCategory}
        category_sections = {category: [] for category in SafeSkipCategory}
        
        for section_name, section_content in test_sections:
            classification_result = self.processor.classify_section(section_name, section_content)
            if classification_result.tier.value == 'safe_skip':
                # 获取段落类别
                section_category = self.processor._get_section_category(section_name)
                for category in SafeSkipCategory:
                    if category.value == section_category:
                        category_counts[category] += 1
                        category_sections[category].append(section_name)
                        break
        
        return {
            'category_counts': category_counts,
            'category_sections': category_sections,
            'total_categories_covered': sum(1 for count in category_counts.values() if count > 0)
        }
    
    def _validate_safety(self, test_sections: List[Tuple[str, List[str]]]) -> Dict[str, Any]:
        """验证安全性"""
        safety_issues = []
        
        # 检查是否误跳过关键配置
        critical_keywords = [
            'firewall policy', 'firewall address', 'firewall service',
            'system interface', 'router static', 'system zone',
            'vpn ipsec', 'system dhcp', 'user local'
        ]
        
        for section_name, section_content in test_sections:
            section_name_lower = section_name.lower()
            
            # 检查是否包含关键关键词
            for keyword in critical_keywords:
                if keyword in section_name_lower:
                    classification_result = self.processor.classify_section(section_name, section_content)
                    if classification_result.tier.value == 'safe_skip':
                        safety_issues.append(f"误跳过关键配置段落: {section_name}")
        
        return {
            'is_safe': len(safety_issues) == 0,
            'safety_issues': safety_issues
        }
    
    def _validate_quality_impact(self, test_sections: List[Tuple[str, List[str]]]) -> Dict[str, Any]:
        """验证质量影响"""
        # 安全跳过策略应该对质量无影响
        impact_level = 'none'
        
        # 检查跳过的段落是否可能影响NTOS功能
        potentially_impactful = []
        
        for section_name, section_content in test_sections:
            classification_result = self.processor.classify_section(section_name, section_content)
            if classification_result.tier.value == 'safe_skip':
                # 检查是否包含可能有用的配置
                if any(keyword in section_name.lower() for keyword in ['system', 'network', 'security']):
                    # 进一步检查内容
                    content_text = ' '.join(section_content).lower()
                    if any(important in content_text for important in ['ip', 'port', 'interface', 'policy']):
                        potentially_impactful.append(section_name)
        
        if potentially_impactful:
            impact_level = 'low'
        
        return {
            'impact_level': impact_level,
            'potentially_impactful_sections': potentially_impactful
        }
    
    def _generate_validation_recommendations(self, pattern_validation: Dict, 
                                           time_validation: Dict,
                                           category_validation: Dict,
                                           safety_validation: Dict) -> List[str]:
        """生成验证建议"""
        recommendations = []
        
        # 基于匹配率的建议
        if pattern_validation['match_rate'] < 0.2:
            recommendations.append("匹配率较低，建议检查和扩展安全跳过模式库")
        elif pattern_validation['match_rate'] > 0.3:
            recommendations.append("匹配率良好，可以继续执行第二层条件跳过策略")
        
        # 基于时间节省的建议
        if time_validation['total_time_saved'] < 10.0:
            recommendations.append("时间节省不足，建议优化时间估算算法")
        elif time_validation['total_time_saved'] > 15.0:
            recommendations.append("时间节省超出预期，优化效果良好")
        
        # 基于类别覆盖的建议
        covered_categories = category_validation['total_categories_covered']
        if covered_categories < 4:
            recommendations.append("类别覆盖不足，建议检查测试数据的代表性")
        
        # 基于安全性的建议
        if not safety_validation['is_safe']:
            recommendations.append("发现安全性问题，必须修复后才能部署")
        else:
            recommendations.append("安全性验证通过，可以安全部署")
        
        # 通用建议
        recommendations.append("建议在生产环境中进行小规模测试")
        recommendations.append("建议建立持续监控机制，确保优化效果")
        
        return recommendations
    
    def generate_validation_report(self, validation_result: ValidationResult, 
                                 output_path: str = None) -> Dict[str, Any]:
        """生成验证报告"""
        report = {
            'validation_summary': {
                'strategy_name': '第一层：安全跳过段落策略',
                'validation_status': 'passed' if validation_result.success else 'failed',
                'validation_timestamp': time.time(),
                'total_sections_tested': validation_result.total_sections,
                'matched_sections': validation_result.matched_sections,
                'match_rate': validation_result.match_rate,
                'time_saved': validation_result.time_saved,
                'quality_impact': validation_result.quality_impact
            },
            'validation_criteria': self.validation_criteria,
            'validation_results': {
                'success': validation_result.success,
                'errors': validation_result.errors,
                'warnings': validation_result.warnings,
                'recommendations': validation_result.recommendations
            },
            'processor_statistics': self.processor.get_classification_statistics(),
            'implementation_details': {
                'patterns_implemented': len(self.processor.safe_skip_patterns),
                'target_patterns': 164,
                'implementation_coverage': len(self.processor.safe_skip_patterns) / 164,
                'supported_categories': [category.value for category in SafeSkipCategory]
            }
        }
        
        # 保存报告
        if output_path:
            try:
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(report, f, indent=2, ensure_ascii=False)
                self.logger.info(f"验证报告已保存到: {output_path}")
            except Exception as e:
                self.logger.error(f"保存验证报告失败: {e}")
        
        return report
    
    def run_comprehensive_validation(self, test_sections: List[Tuple[str, List[str]]],
                                   output_dir: str = None) -> Dict[str, Any]:
        """运行综合验证"""
        self.logger.info("开始运行第一层安全跳过策略综合验证")
        
        # 执行验证
        validation_result = self.validate_implementation(test_sections)
        
        # 生成报告
        if output_dir:
            output_path = Path(output_dir) / f"tier1_safe_skip_validation_report_{int(time.time())}.json"
        else:
            output_path = None
        
        validation_report = self.generate_validation_report(validation_result, str(output_path) if output_path else None)
        
        # 记录验证结果
        if validation_result.success:
            self.logger.info("✅ 第一层安全跳过策略验证通过")
            self.logger.info(f"   匹配率: {validation_result.match_rate:.1%}")
            self.logger.info(f"   时间节省: {validation_result.time_saved:.1f}秒")
            self.logger.info(f"   质量影响: {validation_result.quality_impact}")
        else:
            self.logger.error("❌ 第一层安全跳过策略验证失败")
            for error in validation_result.errors:
                self.logger.error(f"   错误: {error}")
        
        if validation_result.warnings:
            for warning in validation_result.warnings:
                self.logger.warning(f"   警告: {warning}")
        
        return {
            'validation_result': validation_result,
            'validation_report': validation_report,
            'next_steps': self._get_next_steps(validation_result)
        }
    
    def _get_next_steps(self, validation_result: ValidationResult) -> List[str]:
        """获取下一步操作建议"""
        if validation_result.success:
            return [
                "第一层安全跳过策略验证通过，可以继续实施第二层条件跳过策略",
                "建议在生产环境中进行小规模测试",
                "建立监控机制，持续跟踪优化效果"
            ]
        else:
            return [
                "修复验证中发现的错误",
                "重新运行验证测试",
                "考虑调整安全跳过模式或验证标准"
            ]
