"""
第二层：条件跳过段落策略实施
基于依赖分析实施50种模式的条件跳过策略，处理17个段落
"""

import logging
import re
import time
from typing import Dict, List, Set, Optional, Tuple, Any, Union
from dataclasses import dataclass, field
from enum import Enum

from .four_tier_optimization_architecture import (
    ISectionClassifier, IOptimizationProcessor, 
    SectionAnalysisResult, OptimizationTier, ProcessingStrategy
)

class DependencyType(Enum):
    """依赖类型"""
    SERVICE_REFERENCE = "service_reference"       # 服务引用
    ADDRESS_REFERENCE = "address_reference"       # 地址引用
    INTERFACE_DEPENDENCY = "interface_dependency" # 接口依赖
    POLICY_REFERENCE = "policy_reference"         # 策略引用
    CERTIFICATE_DEPENDENCY = "certificate_dependency"  # 证书依赖
    USER_AUTHENTICATION = "user_authentication"   # 用户认证
    LOGGING_REQUIREMENT = "logging_requirement"   # 日志需求
    LANGUAGE_REQUIREMENT = "language_requirement" # 语言需求

@dataclass
class ConditionalSkipPattern:
    """条件跳过模式"""
    pattern: str                    # 匹配模式
    dependency_types: List[DependencyType]  # 依赖类型
    max_size_threshold: int = 1000  # 最大段落大小阈值
    confidence_threshold: float = 0.8  # 置信度阈值
    description: str = ""           # 描述
    check_functions: List[str] = field(default_factory=list)  # 检查函数名

@dataclass
class DependencyAnalysisResult:
    """依赖分析结果"""
    has_dependencies: bool
    dependency_details: Dict[DependencyType, Any]
    confidence: float
    analysis_time: float
    recommendations: List[str] = field(default_factory=list)

class Tier2ConditionalSkipProcessor(ISectionClassifier, IOptimizationProcessor):
    """第二层条件跳过处理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 50种条件跳过模式定义
        self.conditional_patterns = self._initialize_conditional_patterns()
        
        # 依赖分析缓存
        self.dependency_cache: Dict[str, DependencyAnalysisResult] = {}
        
        # 统计信息
        self.processing_stats = {
            'total_sections_analyzed': 0,
            'conditional_skip_sections': 0,
            'dependency_checks_performed': 0,
            'cache_hits': 0,
            'time_saved_total': 0.0,
            'dependency_type_stats': {dep_type: 0 for dep_type in DependencyType}
        }
        
        # 实际段落映射（基于17个段落的分析）
        self.actual_conditional_sections = self._initialize_actual_conditional_mapping()
    
    def _initialize_conditional_patterns(self) -> List[ConditionalSkipPattern]:
        """初始化50种条件跳过模式"""
        patterns = []
        
        # 服务引用相关模式 (12种)
        service_patterns = [
            ('firewall internet-service-name', [DependencyType.SERVICE_REFERENCE], 10000, 
             '互联网服务名称定义', ['check_service_references']),
            ('firewall internet-service-custom', [DependencyType.SERVICE_REFERENCE], 5000,
             '自定义互联网服务', ['check_service_references']),
            ('firewall internet-service-group', [DependencyType.SERVICE_REFERENCE], 2000,
             '互联网服务组', ['check_service_references']),
            ('firewall service category', [DependencyType.SERVICE_REFERENCE], 1000,
             '服务类别定义', ['check_service_references']),
            ('firewall service protocol', [DependencyType.SERVICE_REFERENCE], 500,
             '服务协议定义', ['check_service_references']),
            ('application signature', [DependencyType.SERVICE_REFERENCE], 3000,
             '应用签名定义', ['check_application_references']),
            ('application category', [DependencyType.SERVICE_REFERENCE], 1000,
             '应用类别定义', ['check_application_references']),
            ('application filter', [DependencyType.SERVICE_REFERENCE], 500,
             '应用过滤器', ['check_application_references']),
            ('application rule', [DependencyType.SERVICE_REFERENCE], 2000,
             '应用规则定义', ['check_application_references']),
            ('service custom', [DependencyType.SERVICE_REFERENCE], 1000,
             '自定义服务扩展', ['check_service_references']),
            ('service group member', [DependencyType.SERVICE_REFERENCE], 500,
             '服务组成员', ['check_service_references']),
            ('protocol options', [DependencyType.SERVICE_REFERENCE], 800,
             '协议选项配置', ['check_protocol_references'])
        ]
        
        for pattern, dep_types, max_size, desc, check_funcs in service_patterns:
            patterns.append(ConditionalSkipPattern(
                pattern=pattern,
                dependency_types=dep_types,
                max_size_threshold=max_size,
                description=desc,
                check_functions=check_funcs
            ))
        
        # 用户认证相关模式 (8种)
        user_patterns = [
            ('user local', [DependencyType.USER_AUTHENTICATION], 2000,
             '本地用户配置', ['check_user_authentication_requirements']),
            ('user group', [DependencyType.USER_AUTHENTICATION], 1000,
             '用户组配置', ['check_user_authentication_requirements']),
            ('user setting', [DependencyType.USER_AUTHENTICATION], 500,
             '用户设置', ['check_user_authentication_requirements']),
            ('user radius', [DependencyType.USER_AUTHENTICATION], 1000,
             'RADIUS用户配置', ['check_radius_requirements']),
            ('user ldap', [DependencyType.USER_AUTHENTICATION], 1000,
             'LDAP用户配置', ['check_ldap_requirements']),
            ('user tacacs+', [DependencyType.USER_AUTHENTICATION], 800,
             'TACACS+用户配置', ['check_tacacs_requirements']),
            ('user peer', [DependencyType.USER_AUTHENTICATION], 500,
             '对等用户配置', ['check_peer_requirements']),
            ('user peergrp', [DependencyType.USER_AUTHENTICATION], 300,
             '对等用户组配置', ['check_peer_requirements'])
        ]
        
        for pattern, dep_types, max_size, desc, check_funcs in user_patterns:
            patterns.append(ConditionalSkipPattern(
                pattern=pattern,
                dependency_types=dep_types,
                max_size_threshold=max_size,
                description=desc,
                check_functions=check_funcs
            ))
        
        # 日志相关模式 (10种)
        log_patterns = [
            ('log fortianalyzer2', [DependencyType.LOGGING_REQUIREMENT], 1000,
             '辅助FortiAnalyzer日志配置', ['check_logging_requirements']),
            ('log fortianalyzer3', [DependencyType.LOGGING_REQUIREMENT], 1000,
             '第三FortiAnalyzer日志配置', ['check_logging_requirements']),
            ('log fortianalyzer-cloud', [DependencyType.LOGGING_REQUIREMENT], 800,
             'FortiAnalyzer云日志配置', ['check_cloud_logging_requirements']),
            ('log custom-field', [DependencyType.LOGGING_REQUIREMENT], 500,
             '自定义日志字段', ['check_custom_logging_requirements']),
            ('log threat-weight', [DependencyType.LOGGING_REQUIREMENT], 300,
             '威胁权重日志', ['check_threat_logging_requirements']),
            ('log eventfilter', [DependencyType.LOGGING_REQUIREMENT], 600,
             '事件过滤器日志', ['check_event_logging_requirements']),
            ('log gui-display', [DependencyType.LOGGING_REQUIREMENT], 200,
             'GUI显示日志', ['check_gui_logging_requirements']),
            ('log fortiguard', [DependencyType.LOGGING_REQUIREMENT], 400,
             'FortiGuard日志配置', ['check_fortiguard_logging_requirements']),
            ('log null-device', [DependencyType.LOGGING_REQUIREMENT], 100,
             '空设备日志', ['check_null_device_logging']),
            ('log webtrends', [DependencyType.LOGGING_REQUIREMENT], 300,
             'WebTrends日志配置', ['check_webtrends_logging'])
        ]
        
        for pattern, dep_types, max_size, desc, check_funcs in log_patterns:
            patterns.append(ConditionalSkipPattern(
                pattern=pattern,
                dependency_types=dep_types,
                max_size_threshold=max_size,
                description=desc,
                check_functions=check_funcs
            ))
        
        # 语言和界面相关模式 (8种)
        language_patterns = [
            ('system custom-language', [DependencyType.LANGUAGE_REQUIREMENT], 2000,
             '自定义语言包', ['check_language_requirements']),
            ('system replacemsg-image', [DependencyType.LANGUAGE_REQUIREMENT], 1000,
             '替换消息图片', ['check_message_requirements']),
            ('system replacemsg-group', [DependencyType.LANGUAGE_REQUIREMENT], 800,
             '替换消息组', ['check_message_requirements']),
            ('system replacemsg-mail', [DependencyType.LANGUAGE_REQUIREMENT], 600,
             '替换邮件消息', ['check_message_requirements']),
            ('system replacemsg-http', [DependencyType.LANGUAGE_REQUIREMENT], 500,
             '替换HTTP消息', ['check_message_requirements']),
            ('system replacemsg-webproxy', [DependencyType.LANGUAGE_REQUIREMENT], 400,
             '替换Web代理消息', ['check_message_requirements']),
            ('system replacemsg-ftp', [DependencyType.LANGUAGE_REQUIREMENT], 300,
             '替换FTP消息', ['check_message_requirements']),
            ('system replacemsg-fortiguard-wf', [DependencyType.LANGUAGE_REQUIREMENT], 400,
             '替换FortiGuard Web过滤消息', ['check_message_requirements'])
        ]
        
        for pattern, dep_types, max_size, desc, check_funcs in language_patterns:
            patterns.append(ConditionalSkipPattern(
                pattern=pattern,
                dependency_types=dep_types,
                max_size_threshold=max_size,
                description=desc,
                check_functions=check_funcs
            ))
        
        # 证书和加密相关模式 (6种)
        certificate_patterns = [
            ('vpn certificate setting', [DependencyType.CERTIFICATE_DEPENDENCY], 1000,
             'VPN证书设置', ['check_certificate_dependencies']),
            ('vpn certificate crl', [DependencyType.CERTIFICATE_DEPENDENCY], 800,
             'VPN证书撤销列表', ['check_certificate_dependencies']),
            ('vpn certificate ocsp', [DependencyType.CERTIFICATE_DEPENDENCY], 600,
             'VPN OCSP证书', ['check_certificate_dependencies']),
            ('system certificate ca', [DependencyType.CERTIFICATE_DEPENDENCY], 1500,
             '系统CA证书', ['check_certificate_dependencies']),
            ('system certificate local', [DependencyType.CERTIFICATE_DEPENDENCY], 1200,
             '系统本地证书', ['check_certificate_dependencies']),
            ('system certificate crl', [DependencyType.CERTIFICATE_DEPENDENCY], 800,
             '系统证书撤销列表', ['check_certificate_dependencies'])
        ]
        
        for pattern, dep_types, max_size, desc, check_funcs in certificate_patterns:
            patterns.append(ConditionalSkipPattern(
                pattern=pattern,
                dependency_types=dep_types,
                max_size_threshold=max_size,
                description=desc,
                check_functions=check_funcs
            ))
        
        # 接口依赖相关模式 (6种)
        interface_patterns = [
            ('system interface-policy', [DependencyType.INTERFACE_DEPENDENCY], 1000,
             '接口策略配置', ['check_interface_dependencies']),
            ('system virtual-wire-pair', [DependencyType.INTERFACE_DEPENDENCY], 500,
             '虚拟线对配置', ['check_interface_dependencies']),
            ('system virtual-switch', [DependencyType.INTERFACE_DEPENDENCY], 800,
             '虚拟交换机配置', ['check_interface_dependencies']),
            ('system stp', [DependencyType.INTERFACE_DEPENDENCY], 600,
             'STP配置', ['check_interface_dependencies']),
            ('system link-monitor', [DependencyType.INTERFACE_DEPENDENCY], 400,
             '链路监控配置', ['check_interface_dependencies']),
            ('system mac-address-table', [DependencyType.INTERFACE_DEPENDENCY], 300,
             'MAC地址表配置', ['check_interface_dependencies'])
        ]
        
        for pattern, dep_types, max_size, desc, check_funcs in interface_patterns:
            patterns.append(ConditionalSkipPattern(
                pattern=pattern,
                dependency_types=dep_types,
                max_size_threshold=max_size,
                description=desc,
                check_functions=check_funcs
            ))
        
        self.logger.info(f"初始化完成 - 条件跳过模式总数: {len(patterns)}")
        return patterns
    
    def _initialize_actual_conditional_mapping(self) -> Dict[str, Dict[str, Any]]:
        """初始化实际条件段落映射（基于17个段落的分析）"""
        return {
            # 用户认证相关 (8个实际段落)
            'user': {'count': 8, 'dependency_type': DependencyType.USER_AUTHENTICATION, 'avg_size': 50},
            
            # 日志相关 (8个实际段落)
            'log': {'count': 8, 'dependency_type': DependencyType.LOGGING_REQUIREMENT, 'avg_size': 30},
            
            # 应用相关 (1个实际段落)
            'application': {'count': 1, 'dependency_type': DependencyType.SERVICE_REFERENCE, 'avg_size': 100}
        }
    
    def classify_section(self, section_name: str, section_content: List[str], 
                        context: Dict[str, Any] = None) -> SectionAnalysisResult:
        """分类配置段落"""
        self.processing_stats['total_sections_analyzed'] += 1
        
        # 检查是否匹配条件跳过模式
        matched_pattern = self._find_matching_conditional_pattern(section_name)
        
        if matched_pattern:
            # 执行依赖分析
            dependency_result = self._analyze_dependencies(
                section_name, section_content, matched_pattern, context
            )
            
            section_size = len(section_content)
            
            # 判断是否可以跳过
            should_skip = self._should_skip_section(
                section_name, section_size, matched_pattern, dependency_result
            )
            
            if should_skip:
                # 计算预估时间节省
                estimated_time_saved = self._calculate_time_saved(section_size, matched_pattern)
                
                # 更新统计信息
                self.processing_stats['conditional_skip_sections'] += 1
                self.processing_stats['time_saved_total'] += estimated_time_saved
                
                for dep_type in matched_pattern.dependency_types:
                    self.processing_stats['dependency_type_stats'][dep_type] += 1
                
                return SectionAnalysisResult(
                    section_name=section_name,
                    section_size=section_size,
                    tier=OptimizationTier.CONDITIONAL_SKIP,
                    strategy=ProcessingStrategy.SKIP,
                    confidence=dependency_result.confidence,
                    estimated_time_saved=estimated_time_saved,
                    quality_impact_score=0.1,  # 低质量影响
                    dependencies=[dep.value for dep in matched_pattern.dependency_types],
                    risk_factors=self._assess_risk_factors(section_name, dependency_result),
                    recommendations=dependency_result.recommendations
                )
            else:
                # 不能跳过，需要简化处理
                return SectionAnalysisResult(
                    section_name=section_name,
                    section_size=section_size,
                    tier=OptimizationTier.CONDITIONAL_SKIP,
                    strategy=ProcessingStrategy.SIMPLIFIED,
                    confidence=dependency_result.confidence,
                    estimated_time_saved=section_size * 0.005,  # 简化处理节省时间
                    quality_impact_score=0.3,  # 中等质量影响
                    dependencies=[dep.value for dep in matched_pattern.dependency_types],
                    risk_factors=self._assess_risk_factors(section_name, dependency_result),
                    recommendations=dependency_result.recommendations + ['建议简化处理以保持依赖关系']
                )
        
        # 不匹配条件跳过模式，返回默认分类
        return SectionAnalysisResult(
            section_name=section_name,
            section_size=len(section_content),
            tier=OptimizationTier.CRITICAL_FULL,  # 默认为关键处理
            strategy=ProcessingStrategy.FULL,
            confidence=0.0,
            estimated_time_saved=0.0,
            quality_impact_score=1.0,
            dependencies=[],
            risk_factors=['未匹配条件跳过模式'],
            recommendations=['建议进一步分析段落内容和依赖关系']
        )
    
    def _find_matching_conditional_pattern(self, section_name: str) -> Optional[ConditionalSkipPattern]:
        """查找匹配的条件跳过模式"""
        section_name_lower = section_name.lower()
        
        # 精确匹配
        for pattern in self.conditional_patterns:
            if pattern.pattern.lower() == section_name_lower:
                return pattern
        
        # 前缀匹配
        for pattern in self.conditional_patterns:
            if section_name_lower.startswith(pattern.pattern.lower()):
                return pattern
        
        # 包含匹配
        for pattern in self.conditional_patterns:
            pattern_words = pattern.pattern.lower().split()
            if all(word in section_name_lower for word in pattern_words):
                return pattern
        
        return None
    
    def _analyze_dependencies(self, section_name: str, section_content: List[str],
                            pattern: ConditionalSkipPattern, 
                            context: Dict[str, Any] = None) -> DependencyAnalysisResult:
        """分析段落依赖关系"""
        analysis_start_time = time.time()
        
        # 检查缓存
        cache_key = f"{section_name}:{len(section_content)}"
        if cache_key in self.dependency_cache:
            self.processing_stats['cache_hits'] += 1
            return self.dependency_cache[cache_key]
        
        self.processing_stats['dependency_checks_performed'] += 1
        
        dependency_details = {}
        has_dependencies = False
        recommendations = []
        
        # 执行各种依赖检查
        for dep_type in pattern.dependency_types:
            if dep_type == DependencyType.SERVICE_REFERENCE:
                service_deps = self._check_service_references(section_content, context)
                dependency_details[dep_type] = service_deps
                if service_deps['has_references']:
                    has_dependencies = True
                    recommendations.append("发现服务引用，需要保留段落")
            
            elif dep_type == DependencyType.USER_AUTHENTICATION:
                user_deps = self._check_user_authentication_requirements(section_content, context)
                dependency_details[dep_type] = user_deps
                if user_deps['is_required']:
                    has_dependencies = True
                    recommendations.append("用户认证功能被使用，需要保留段落")
            
            elif dep_type == DependencyType.LOGGING_REQUIREMENT:
                log_deps = self._check_logging_requirements(section_content, context)
                dependency_details[dep_type] = log_deps
                if log_deps['is_required']:
                    has_dependencies = True
                    recommendations.append("日志功能被使用，需要保留段落")
            
            elif dep_type == DependencyType.LANGUAGE_REQUIREMENT:
                lang_deps = self._check_language_requirements(section_content, context)
                dependency_details[dep_type] = lang_deps
                if lang_deps['is_required']:
                    has_dependencies = True
                    recommendations.append("多语言功能被使用，需要保留段落")
            
            elif dep_type == DependencyType.CERTIFICATE_DEPENDENCY:
                cert_deps = self._check_certificate_dependencies(section_content, context)
                dependency_details[dep_type] = cert_deps
                if cert_deps['has_dependencies']:
                    has_dependencies = True
                    recommendations.append("证书依赖存在，需要保留段落")
            
            elif dep_type == DependencyType.INTERFACE_DEPENDENCY:
                interface_deps = self._check_interface_dependencies(section_content, context)
                dependency_details[dep_type] = interface_deps
                if interface_deps['has_dependencies']:
                    has_dependencies = True
                    recommendations.append("接口依赖存在，需要保留段落")
        
        # 计算置信度
        confidence = self._calculate_dependency_confidence(dependency_details, pattern)
        
        if not has_dependencies:
            recommendations.append("未发现关键依赖，可以安全跳过")
        
        analysis_time = time.time() - analysis_start_time
        
        result = DependencyAnalysisResult(
            has_dependencies=has_dependencies,
            dependency_details=dependency_details,
            confidence=confidence,
            analysis_time=analysis_time,
            recommendations=recommendations
        )
        
        # 缓存结果
        self.dependency_cache[cache_key] = result
        
        return result
    
    def _should_skip_section(self, section_name: str, section_size: int,
                           pattern: ConditionalSkipPattern,
                           dependency_result: DependencyAnalysisResult) -> bool:
        """判断是否应该跳过段落"""
        # 如果有依赖关系，不能跳过
        if dependency_result.has_dependencies:
            return False
        
        # 如果段落过大，不能跳过（可能包含重要配置）
        if section_size > pattern.max_size_threshold:
            return False
        
        # 如果置信度过低，不能跳过
        if dependency_result.confidence < pattern.confidence_threshold:
            return False
        
        return True
    
    def _calculate_time_saved(self, section_size: int, pattern: ConditionalSkipPattern) -> float:
        """计算时间节省"""
        # 基于段落大小和复杂度计算
        base_time_per_line = 0.01  # 基础每行处理时间
        complexity_factor = 1.0
        
        # 根据依赖类型调整复杂度
        for dep_type in pattern.dependency_types:
            if dep_type in [DependencyType.SERVICE_REFERENCE, DependencyType.CERTIFICATE_DEPENDENCY]:
                complexity_factor += 0.5
            elif dep_type in [DependencyType.USER_AUTHENTICATION, DependencyType.INTERFACE_DEPENDENCY]:
                complexity_factor += 0.3
            else:
                complexity_factor += 0.1
        
        return section_size * base_time_per_line * complexity_factor
    
    def _assess_risk_factors(self, section_name: str, 
                           dependency_result: DependencyAnalysisResult) -> List[str]:
        """评估风险因素"""
        risk_factors = []
        
        if dependency_result.confidence < 0.8:
            risk_factors.append("依赖分析置信度较低")
        
        if dependency_result.analysis_time > 0.1:
            risk_factors.append("依赖分析耗时较长")
        
        # 检查段落名称中的关键词
        critical_keywords = ['system', 'security', 'policy', 'interface']
        if any(keyword in section_name.lower() for keyword in critical_keywords):
            risk_factors.append("段落名称包含关键词，可能重要")
        
        return risk_factors
    
    def _calculate_dependency_confidence(self, dependency_details: Dict[DependencyType, Any],
                                       pattern: ConditionalSkipPattern) -> float:
        """计算依赖分析置信度"""
        if not dependency_details:
            return 0.5  # 默认置信度
        
        confidence_scores = []
        
        for dep_type, details in dependency_details.items():
            if isinstance(details, dict):
                # 基于检查结果计算置信度
                if 'confidence' in details:
                    confidence_scores.append(details['confidence'])
                elif 'has_references' in details or 'is_required' in details or 'has_dependencies' in details:
                    # 基于布尔结果计算置信度
                    confidence_scores.append(0.9 if any(details.values()) else 0.8)
                else:
                    confidence_scores.append(0.7)
            else:
                confidence_scores.append(0.6)
        
        return sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0.5
    
    # 依赖检查函数实现
    def _check_service_references(self, content: List[str], context: Dict[str, Any] = None) -> Dict[str, Any]:
        """检查服务引用"""
        if not context or 'policies' not in context:
            return {'has_references': False, 'confidence': 0.5, 'details': '缺少上下文信息'}
        
        # 提取段落中定义的服务名称
        service_names = set()
        for line in content:
            if line.strip().startswith('edit '):
                parts = line.strip().split()
                if len(parts) > 1:
                    service_name = parts[1].strip('"')
                    service_names.add(service_name)
        
        # 检查策略中是否引用了这些服务
        policies = context.get('policies', [])
        referenced_services = set()
        
        for policy in policies:
            policy_services = policy.get('service', [])
            if isinstance(policy_services, str):
                policy_services = [policy_services]
            
            for service in policy_services:
                if service in service_names:
                    referenced_services.add(service)
        
        has_references = len(referenced_services) > 0
        
        return {
            'has_references': has_references,
            'defined_services': list(service_names),
            'referenced_services': list(referenced_services),
            'confidence': 0.9 if service_names else 0.7,
            'details': f'定义了{len(service_names)}个服务，其中{len(referenced_services)}个被引用'
        }
    
    def _check_user_authentication_requirements(self, content: List[str], 
                                              context: Dict[str, Any] = None) -> Dict[str, Any]:
        """检查用户认证需求"""
        # 检查NTOS是否需要用户认证功能
        ntos_config = context.get('ntos_config', {}) if context else {}
        requires_auth = ntos_config.get('user_authentication_required', False)
        
        # 检查是否有策略引用用户或组
        has_user_references = False
        if context and 'policies' in context:
            for policy in context['policies']:
                if 'users' in policy or 'groups' in policy:
                    has_user_references = True
                    break
        
        is_required = requires_auth or has_user_references
        
        return {
            'is_required': is_required,
            'ntos_requires_auth': requires_auth,
            'has_policy_references': has_user_references,
            'confidence': 0.8,
            'details': f'NTOS认证需求: {requires_auth}, 策略引用: {has_user_references}'
        }
    
    def _check_logging_requirements(self, content: List[str], 
                                  context: Dict[str, Any] = None) -> Dict[str, Any]:
        """检查日志需求"""
        # 检查NTOS是否需要详细日志配置
        ntos_config = context.get('ntos_config', {}) if context else {}
        requires_detailed_logging = ntos_config.get('detailed_logging', False)
        
        # 检查是否为关键日志配置
        critical_log_keywords = ['syslog', 'fortianalyzer', 'security', 'traffic']
        has_critical_logging = any(
            keyword in ' '.join(content).lower() 
            for keyword in critical_log_keywords
        )
        
        is_required = requires_detailed_logging or has_critical_logging
        
        return {
            'is_required': is_required,
            'requires_detailed_logging': requires_detailed_logging,
            'has_critical_logging': has_critical_logging,
            'confidence': 0.7,
            'details': f'详细日志需求: {requires_detailed_logging}, 关键日志: {has_critical_logging}'
        }
    
    def _check_language_requirements(self, content: List[str], 
                                   context: Dict[str, Any] = None) -> Dict[str, Any]:
        """检查语言需求"""
        # 检查NTOS是否需要多语言支持
        ntos_config = context.get('ntos_config', {}) if context else {}
        requires_multilang = ntos_config.get('multi_language_support', False)
        
        # 检查是否有自定义消息被策略引用
        has_message_references = False
        if context and 'policies' in context:
            for policy in context['policies']:
                policy_str = str(policy).lower()
                if 'replacemsg' in policy_str or 'custom-message' in policy_str:
                    has_message_references = True
                    break
        
        is_required = requires_multilang or has_message_references
        
        return {
            'is_required': is_required,
            'requires_multilang': requires_multilang,
            'has_message_references': has_message_references,
            'confidence': 0.8,
            'details': f'多语言需求: {requires_multilang}, 消息引用: {has_message_references}'
        }
    
    def _check_certificate_dependencies(self, content: List[str], 
                                      context: Dict[str, Any] = None) -> Dict[str, Any]:
        """检查证书依赖"""
        # 提取证书名称
        cert_names = set()
        for line in content:
            if 'certificate' in line.lower() and 'edit' in line:
                parts = line.strip().split()
                if len(parts) > 1:
                    cert_name = parts[-1].strip('"')
                    cert_names.add(cert_name)
        
        # 检查VPN配置中的证书引用
        has_dependencies = False
        if context and 'vpn_configs' in context:
            for vpn_config in context['vpn_configs']:
                vpn_str = str(vpn_config).lower()
                if any(cert in vpn_str for cert in cert_names):
                    has_dependencies = True
                    break
        
        return {
            'has_dependencies': has_dependencies,
            'certificate_names': list(cert_names),
            'confidence': 0.9 if cert_names else 0.6,
            'details': f'发现{len(cert_names)}个证书，依赖检查: {has_dependencies}'
        }
    
    def _check_interface_dependencies(self, content: List[str], 
                                    context: Dict[str, Any] = None) -> Dict[str, Any]:
        """检查接口依赖"""
        # 检查是否包含接口性能配置
        performance_keywords = ['speed', 'duplex', 'mtu', 'offload', 'queue', 'buffer']
        content_text = ' '.join(content).lower()
        
        has_performance_config = any(keyword in content_text for keyword in performance_keywords)
        
        # 检查是否被策略引用
        has_policy_references = False
        if context and 'policies' in context:
            for policy in context['policies']:
                policy_str = str(policy).lower()
                if any(keyword in policy_str for keyword in ['interface', 'srcintf', 'dstintf']):
                    has_policy_references = True
                    break
        
        has_dependencies = has_performance_config or has_policy_references
        
        return {
            'has_dependencies': has_dependencies,
            'has_performance_config': has_performance_config,
            'has_policy_references': has_policy_references,
            'confidence': 0.8,
            'details': f'性能配置: {has_performance_config}, 策略引用: {has_policy_references}'
        }
    
    def process_section(self, section_name: str, section_content: List[str],
                       analysis_result: SectionAnalysisResult,
                       context: Dict[str, Any] = None) -> Dict[str, Any]:
        """处理配置段落（条件跳过或简化处理）"""
        start_time = time.time()
        
        if analysis_result.strategy == ProcessingStrategy.SKIP:
            # 条件跳过处理
            skip_info = {
                'section_name': section_name,
                'section_size': analysis_result.section_size,
                'skip_reason': f"条件跳过 - {', '.join(analysis_result.recommendations)}",
                'dependencies_checked': analysis_result.dependencies,
                'confidence': analysis_result.confidence,
                'time_saved': analysis_result.estimated_time_saved,
                'processing_time': time.time() - start_time
            }
            
            self.logger.debug(f"条件跳过段落: {section_name} - 节省时间: {analysis_result.estimated_time_saved:.3f}秒")
            
            return {
                'action': 'conditional_skip',
                'result': skip_info,
                'quality_impact': 'low',
                'performance_gain': analysis_result.estimated_time_saved
            }
            
        elif analysis_result.strategy == ProcessingStrategy.SIMPLIFIED:
            # 简化处理
            simplified_result = self._perform_simplified_processing(
                section_name, section_content, analysis_result, context
            )
            
            processing_time = time.time() - start_time
            
            return {
                'action': 'simplified_processing',
                'result': simplified_result,
                'quality_impact': 'medium',
                'performance_gain': analysis_result.estimated_time_saved,
                'processing_time': processing_time
            }
        
        else:
            raise ValueError(f"第二层处理器不支持策略: {analysis_result.strategy}")
    
    def _perform_simplified_processing(self, section_name: str, section_content: List[str],
                                     analysis_result: SectionAnalysisResult,
                                     context: Dict[str, Any] = None) -> Dict[str, Any]:
        """执行简化处理"""
        # 提取关键信息
        key_info = {}
        
        if 'user' in section_name.lower():
            key_info = self._extract_user_key_info(section_content)
        elif 'log' in section_name.lower():
            key_info = self._extract_log_key_info(section_content)
        elif 'certificate' in section_name.lower():
            key_info = self._extract_certificate_key_info(section_content)
        else:
            key_info = self._extract_generic_key_info(section_content)
        
        return {
            'section_name': section_name,
            'processing_type': 'simplified',
            'key_information': key_info,
            'original_size': len(section_content),
            'simplified_size': len(key_info),
            'compression_ratio': len(key_info) / len(section_content) if section_content else 0
        }
    
    def _extract_user_key_info(self, content: List[str]) -> Dict[str, Any]:
        """提取用户配置关键信息"""
        key_info = {'users': [], 'groups': [], 'settings': {}}
        
        current_user = None
        for line in content:
            line = line.strip()
            if line.startswith('edit '):
                current_user = line.split()[1].strip('"')
                key_info['users'].append({'name': current_user})
            elif line.startswith('set ') and current_user:
                parts = line.split(None, 2)
                if len(parts) >= 3:
                    key = parts[1]
                    value = parts[2].strip('"')
                    if key in ['type', 'passwd', 'group']:
                        key_info['users'][-1][key] = value
        
        return key_info
    
    def _extract_log_key_info(self, content: List[str]) -> Dict[str, Any]:
        """提取日志配置关键信息"""
        key_info = {'server': '', 'port': '', 'facility': '', 'format': ''}
        
        for line in content:
            line = line.strip()
            if line.startswith('set '):
                parts = line.split(None, 2)
                if len(parts) >= 3:
                    key = parts[1]
                    value = parts[2].strip('"')
                    if key in ['server', 'port', 'facility', 'format']:
                        key_info[key] = value
        
        return key_info
    
    def _extract_certificate_key_info(self, content: List[str]) -> Dict[str, Any]:
        """提取证书配置关键信息"""
        key_info = {'certificates': []}
        
        current_cert = None
        for line in content:
            line = line.strip()
            if line.startswith('edit '):
                current_cert = line.split()[1].strip('"')
                key_info['certificates'].append({'name': current_cert})
            elif line.startswith('set ') and current_cert:
                parts = line.split(None, 2)
                if len(parts) >= 3:
                    key = parts[1]
                    value = parts[2].strip('"')
                    if key in ['type', 'source', 'status']:
                        key_info['certificates'][-1][key] = value
        
        return key_info
    
    def _extract_generic_key_info(self, content: List[str]) -> Dict[str, Any]:
        """提取通用关键信息"""
        key_info = {'entries': [], 'settings': {}}
        
        for line in content:
            line = line.strip()
            if line.startswith('edit '):
                entry_name = line.split()[1].strip('"')
                key_info['entries'].append(entry_name)
            elif line.startswith('set '):
                parts = line.split(None, 2)
                if len(parts) >= 3:
                    key = parts[1]
                    value = parts[2].strip('"')
                    key_info['settings'][key] = value
        
        return key_info
    
    def get_supported_strategies(self) -> List[ProcessingStrategy]:
        """获取支持的处理策略"""
        return [ProcessingStrategy.SKIP, ProcessingStrategy.SIMPLIFIED]
    
    def get_classification_statistics(self) -> Dict[str, Any]:
        """获取分类统计信息"""
        total_analyzed = self.processing_stats['total_sections_analyzed']
        conditional_skip_count = self.processing_stats['conditional_skip_sections']
        
        return {
            'total_sections_analyzed': total_analyzed,
            'conditional_skip_sections': conditional_skip_count,
            'conditional_skip_ratio': conditional_skip_count / total_analyzed if total_analyzed > 0 else 0.0,
            'dependency_checks_performed': self.processing_stats['dependency_checks_performed'],
            'cache_hits': self.processing_stats['cache_hits'],
            'cache_hit_ratio': (
                self.processing_stats['cache_hits'] / self.processing_stats['dependency_checks_performed']
                if self.processing_stats['dependency_checks_performed'] > 0 else 0.0
            ),
            'total_time_saved': self.processing_stats['time_saved_total'],
            'dependency_type_distribution': dict(self.processing_stats['dependency_type_stats']),
            'patterns_count': len(self.conditional_patterns),
            'actual_conditional_mapping': self.actual_conditional_sections
        }
