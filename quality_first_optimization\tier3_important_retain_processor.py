"""
第三层：重要段落保留策略实施
简化处理15种关键类型的重要段落，确保功能完整性
"""

import logging
import re
import time
from typing import Dict, List, Set, Optional, Tuple, Any, Union
from dataclasses import dataclass, field
from enum import Enum

from .four_tier_optimization_architecture import (
    ISectionClassifier, IOptimizationProcessor, 
    SectionAnalysisResult, OptimizationTier, ProcessingStrategy
)

class ImportantSectionType(Enum):
    """重要段落类型"""
    VPN_CONFIGURATION = "vpn_configuration"           # VPN配置
    SYSTEM_CONFIGURATION = "system_configuration"     # 系统配置
    ROUTING_CONFIGURATION = "routing_configuration"   # 路由配置
    HA_CONFIGURATION = "ha_configuration"             # 高可用配置
    CERTIFICATE_MANAGEMENT = "certificate_management" # 证书管理
    DHCP_CONFIGURATION = "dhcp_configuration"         # DHCP配置
    DNS_CONFIGURATION = "dns_configuration"           # DNS配置
    NTP_CONFIGURATION = "ntp_configuration"           # NTP配置
    SNMP_CONFIGURATION = "snmp_configuration"         # SNMP配置
    LOGGING_CONFIGURATION = "logging_configuration"   # 日志配置
    AUTHENTICATION = "authentication"                 # 认证配置
    NETWORK_SERVICES = "network_services"             # 网络服务
    INTERFACE_ADVANCED = "interface_advanced"         # 高级接口配置
    SECURITY_PROFILES = "security_profiles"           # 安全配置文件
    MONITORING_HEALTH = "monitoring_health"           # 监控和健康检查

@dataclass
class ImportantSectionPattern:
    """重要段落模式"""
    pattern: str                    # 匹配模式
    section_type: ImportantSectionType  # 段落类型
    ntos_equivalent: Optional[str]  # NTOS等效配置
    simplification_level: float    # 简化程度 (0.0-1.0)
    quality_impact: float          # 质量影响 (0.0-1.0)
    description: str               # 描述
    key_fields: List[str] = field(default_factory=list)  # 关键字段
    processing_hints: List[str] = field(default_factory=list)  # 处理提示

class Tier3ImportantRetainProcessor(ISectionClassifier, IOptimizationProcessor):
    """第三层重要段落保留处理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 15种重要段落模式定义
        self.important_patterns = self._initialize_important_patterns()
        
        # 简化处理缓存
        self.simplification_cache: Dict[str, Dict[str, Any]] = {}
        
        # 统计信息
        self.processing_stats = {
            'total_sections_analyzed': 0,
            'important_sections_retained': 0,
            'simplification_applied': 0,
            'time_saved_total': 0.0,
            'quality_preserved': 0.0,
            'section_type_stats': {section_type: 0 for section_type in ImportantSectionType}
        }
        
        # 实际重要段落映射（基于50个段落的分析）
        self.actual_important_sections = self._initialize_actual_important_mapping()
    
    def _initialize_important_patterns(self) -> List[ImportantSectionPattern]:
        """初始化15种重要段落模式"""
        patterns = []
        
        # VPN配置相关 (3种模式)
        vpn_patterns = [
            ('vpn ipsec phase1-interface', ImportantSectionType.VPN_CONFIGURATION,
             'vpn ipsec tunnel', 0.3, 0.8, 'IPSec Phase1接口配置',
             ['name', 'interface', 'peertype', 'proposal', 'dhgrp', 'authmethod'],
             ['保留关键隧道参数', '简化高级选项']),
            ('vpn ipsec phase2-interface', ImportantSectionType.VPN_CONFIGURATION,
             'vpn ipsec tunnel', 0.3, 0.8, 'IPSec Phase2接口配置',
             ['name', 'phase1name', 'proposal', 'dhgrp', 'src-subnet', 'dst-subnet'],
             ['保留关键隧道参数', '简化加密选项']),
            ('vpn ssl settings', ImportantSectionType.VPN_CONFIGURATION,
             'vpn ssl global', 0.5, 0.6, 'SSL VPN全局设置',
             ['port', 'source-interface', 'source-address', 'default-portal'],
             ['保留基本SSL VPN设置', '简化高级功能'])
        ]
        
        for pattern, section_type, ntos_equiv, simp_level, quality_impact, desc, key_fields, hints in vpn_patterns:
            patterns.append(ImportantSectionPattern(
                pattern=pattern,
                section_type=section_type,
                ntos_equivalent=ntos_equiv,
                simplification_level=simp_level,
                quality_impact=quality_impact,
                description=desc,
                key_fields=key_fields,
                processing_hints=hints
            ))
        
        # 系统配置相关 (4种模式)
        system_patterns = [
            ('system global', ImportantSectionType.SYSTEM_CONFIGURATION,
             'system global', 0.4, 0.9, '系统全局配置',
             ['hostname', 'timezone', 'admin-sport', 'admin-ssh-port', 'gui-theme'],
             ['保留基本系统设置', '简化GUI相关配置']),
            ('system admin', ImportantSectionType.SYSTEM_CONFIGURATION,
             'system admin', 0.2, 0.9, '系统管理员配置',
             ['name', 'password', 'trusthost1', 'accprofile', 'vdom'],
             ['保留管理员账户信息', '简化访问控制']),
            ('system ha', ImportantSectionType.HA_CONFIGURATION,
             'system ha', 0.3, 0.8, '高可用性配置',
             ['group-name', 'mode', 'password', 'priority', 'override'],
             ['保留HA核心参数', '简化监控选项']),
            ('system accprofile', ImportantSectionType.SYSTEM_CONFIGURATION,
             'system user-role', 0.6, 0.5, '访问配置文件',
             ['name', 'secfabgrp', 'ftviewgrp', 'authgrp', 'sysgrp'],
             ['保留基本权限设置', '简化详细权限'])
        ]
        
        for pattern, section_type, ntos_equiv, simp_level, quality_impact, desc, key_fields, hints in system_patterns:
            patterns.append(ImportantSectionPattern(
                pattern=pattern,
                section_type=section_type,
                ntos_equivalent=ntos_equiv,
                simplification_level=simp_level,
                quality_impact=quality_impact,
                description=desc,
                key_fields=key_fields,
                processing_hints=hints
            ))
        
        # 网络服务相关 (3种模式)
        network_service_patterns = [
            ('system dhcp server', ImportantSectionType.DHCP_CONFIGURATION,
             'dhcp server', 0.4, 0.7, 'DHCP服务器配置',
             ['id', 'status', 'dns-service', 'default-gateway', 'netmask', 'ip-range'],
             ['保留DHCP基本参数', '简化高级选项']),
            ('system dns', ImportantSectionType.DNS_CONFIGURATION,
             'dns', 0.3, 0.8, 'DNS配置',
             ['primary', 'secondary', 'dns-over-tls', 'ssl-certificate'],
             ['保留DNS服务器设置', '简化安全选项']),
            ('system ntp', ImportantSectionType.NTP_CONFIGURATION,
             'ntp', 0.5, 0.6, 'NTP时间同步配置',
             ['ntpsync', 'type', 'syncinterval', 'server'],
             ['保留时间同步设置', '简化同步选项'])
        ]
        
        for pattern, section_type, ntos_equiv, simp_level, quality_impact, desc, key_fields, hints in network_service_patterns:
            patterns.append(ImportantSectionPattern(
                pattern=pattern,
                section_type=section_type,
                ntos_equivalent=ntos_equiv,
                simplification_level=simp_level,
                quality_impact=quality_impact,
                description=desc,
                key_fields=key_fields,
                processing_hints=hints
            ))
        
        # 监控和管理相关 (3种模式)
        monitoring_patterns = [
            ('system snmp sysinfo', ImportantSectionType.SNMP_CONFIGURATION,
             'snmp-server', 0.6, 0.4, 'SNMP系统信息',
             ['status', 'engine-id', 'description', 'contact-info', 'location'],
             ['保留基本SNMP信息', '简化详细配置']),
            ('log setting', ImportantSectionType.LOGGING_CONFIGURATION,
             'logging', 0.5, 0.6, '日志设置',
             ['resolve-ip', 'resolve-port', 'log-user-in-upper', 'fwpolicy-implicit-log'],
             ['保留基本日志设置', '简化详细选项']),
            ('system link-monitor', ImportantSectionType.MONITORING_HEALTH,
             'track', 0.7, 0.3, '链路监控配置',
             ['name', 'srcintf', 'server', 'protocol', 'interval'],
             ['保留监控目标', '简化监控参数'])
        ]
        
        for pattern, section_type, ntos_equiv, simp_level, quality_impact, desc, key_fields, hints in monitoring_patterns:
            patterns.append(ImportantSectionPattern(
                pattern=pattern,
                section_type=section_type,
                ntos_equivalent=ntos_equiv,
                simplification_level=simp_level,
                quality_impact=quality_impact,
                description=desc,
                key_fields=key_fields,
                processing_hints=hints
            ))
        
        # 路由和接口相关 (2种模式)
        routing_interface_patterns = [
            ('router static', ImportantSectionType.ROUTING_CONFIGURATION,
             'ip route', 0.2, 0.9, '静态路由配置',
             ['seq-num', 'dst', 'gateway', 'device', 'distance'],
             ['保留所有路由信息', '简化路由选项']),
            ('system interface', ImportantSectionType.INTERFACE_ADVANCED,
             'interface', 0.3, 0.8, '高级接口配置',
             ['name', 'vdom', 'ip', 'allowaccess', 'type', 'role'],
             ['保留接口基本配置', '简化高级特性'])
        ]
        
        for pattern, section_type, ntos_equiv, simp_level, quality_impact, desc, key_fields, hints in routing_interface_patterns:
            patterns.append(ImportantSectionPattern(
                pattern=pattern,
                section_type=section_type,
                ntos_equivalent=ntos_equiv,
                simplification_level=simp_level,
                quality_impact=quality_impact,
                description=desc,
                key_fields=key_fields,
                processing_hints=hints
            ))
        
        self.logger.info(f"初始化完成 - 重要段落模式总数: {len(patterns)}")
        return patterns
    
    def _initialize_actual_important_mapping(self) -> Dict[str, Dict[str, Any]]:
        """初始化实际重要段落映射（基于50个段落的分析）"""
        return {
            # 系统配置相关 (35个实际段落)
            'system': {
                'count': 35, 
                'section_type': ImportantSectionType.SYSTEM_CONFIGURATION, 
                'avg_size': 20,
                'simplification_potential': 0.4
            },
            
            # VPN配置相关 (8个实际段落)
            'vpn': {
                'count': 8, 
                'section_type': ImportantSectionType.VPN_CONFIGURATION, 
                'avg_size': 50,
                'simplification_potential': 0.3
            },
            
            # 路由配置相关 (2个实际段落)
            'router': {
                'count': 2, 
                'section_type': ImportantSectionType.ROUTING_CONFIGURATION, 
                'avg_size': 10,
                'simplification_potential': 0.2
            },
            
            # 交换机控制相关 (15个实际段落)
            'switch-controller': {
                'count': 15, 
                'section_type': ImportantSectionType.NETWORK_SERVICES, 
                'avg_size': 30,
                'simplification_potential': 0.6
            }
        }
    
    def classify_section(self, section_name: str, section_content: List[str], 
                        context: Dict[str, Any] = None) -> SectionAnalysisResult:
        """分类配置段落"""
        self.processing_stats['total_sections_analyzed'] += 1
        
        # 检查是否匹配重要段落模式
        matched_pattern = self._find_matching_important_pattern(section_name)
        
        if matched_pattern:
            section_size = len(section_content)
            
            # 计算简化处理的时间节省
            estimated_time_saved = self._calculate_simplification_time_saved(
                section_size, matched_pattern
            )
            
            # 更新统计信息
            self.processing_stats['important_sections_retained'] += 1
            self.processing_stats['simplification_applied'] += 1
            self.processing_stats['time_saved_total'] += estimated_time_saved
            self.processing_stats['quality_preserved'] += matched_pattern.quality_impact
            self.processing_stats['section_type_stats'][matched_pattern.section_type] += 1
            
            return SectionAnalysisResult(
                section_name=section_name,
                section_size=section_size,
                tier=OptimizationTier.IMPORTANT_RETAIN,
                strategy=ProcessingStrategy.SIMPLIFIED,
                confidence=0.9,  # 高置信度，因为是重要段落
                estimated_time_saved=estimated_time_saved,
                quality_impact_score=matched_pattern.quality_impact,
                dependencies=[matched_pattern.section_type.value],
                risk_factors=self._assess_simplification_risks(section_name, matched_pattern),
                recommendations=self._generate_simplification_recommendations(matched_pattern)
            )
        
        # 不匹配重要段落模式，返回默认分类
        return SectionAnalysisResult(
            section_name=section_name,
            section_size=len(section_content),
            tier=OptimizationTier.CRITICAL_FULL,  # 默认为关键处理
            strategy=ProcessingStrategy.FULL,
            confidence=0.0,
            estimated_time_saved=0.0,
            quality_impact_score=1.0,
            dependencies=[],
            risk_factors=['未匹配重要段落模式'],
            recommendations=['建议进一步分析段落重要性']
        )
    
    def _find_matching_important_pattern(self, section_name: str) -> Optional[ImportantSectionPattern]:
        """查找匹配的重要段落模式"""
        section_name_lower = section_name.lower()
        
        # 精确匹配
        for pattern in self.important_patterns:
            if pattern.pattern.lower() == section_name_lower:
                return pattern
        
        # 前缀匹配
        for pattern in self.important_patterns:
            if section_name_lower.startswith(pattern.pattern.lower()):
                return pattern
        
        # 关键词匹配
        for pattern in self.important_patterns:
            pattern_words = pattern.pattern.lower().split()
            if all(word in section_name_lower for word in pattern_words):
                return pattern
        
        return None
    
    def _calculate_simplification_time_saved(self, section_size: int, 
                                           pattern: ImportantSectionPattern) -> float:
        """计算简化处理的时间节省"""
        # 基础处理时间
        base_processing_time = section_size * 0.02  # 每行0.02秒
        
        # 简化节省的时间
        time_saved = base_processing_time * pattern.simplification_level
        
        # 根据段落类型调整
        type_multipliers = {
            ImportantSectionType.VPN_CONFIGURATION: 1.5,      # VPN配置复杂
            ImportantSectionType.SYSTEM_CONFIGURATION: 1.2,   # 系统配置中等复杂
            ImportantSectionType.ROUTING_CONFIGURATION: 1.0,  # 路由配置标准
            ImportantSectionType.HA_CONFIGURATION: 1.3,       # HA配置较复杂
            ImportantSectionType.DHCP_CONFIGURATION: 0.8,     # DHCP配置较简单
            ImportantSectionType.DNS_CONFIGURATION: 0.7,      # DNS配置简单
            ImportantSectionType.SNMP_CONFIGURATION: 0.9,     # SNMP配置标准
            ImportantSectionType.LOGGING_CONFIGURATION: 0.8,  # 日志配置较简单
        }
        
        multiplier = type_multipliers.get(pattern.section_type, 1.0)
        return time_saved * multiplier
    
    def _assess_simplification_risks(self, section_name: str, 
                                   pattern: ImportantSectionPattern) -> List[str]:
        """评估简化处理的风险"""
        risks = []
        
        # 基于质量影响评估风险
        if pattern.quality_impact > 0.8:
            risks.append("高质量影响段落，简化可能影响功能")
        elif pattern.quality_impact > 0.6:
            risks.append("中等质量影响，需要谨慎简化")
        
        # 基于简化程度评估风险
        if pattern.simplification_level > 0.6:
            risks.append("高度简化，可能丢失重要配置")
        elif pattern.simplification_level > 0.4:
            risks.append("中度简化，需要验证关键功能")
        
        # 基于段落类型评估风险
        high_risk_types = [
            ImportantSectionType.VPN_CONFIGURATION,
            ImportantSectionType.HA_CONFIGURATION,
            ImportantSectionType.ROUTING_CONFIGURATION
        ]
        
        if pattern.section_type in high_risk_types:
            risks.append(f"关键网络功能({pattern.section_type.value})，需要特别注意")
        
        return risks
    
    def _generate_simplification_recommendations(self, pattern: ImportantSectionPattern) -> List[str]:
        """生成简化处理建议"""
        recommendations = []
        
        # 基于处理提示生成建议
        recommendations.extend(pattern.processing_hints)
        
        # 基于NTOS等效配置生成建议
        if pattern.ntos_equivalent:
            recommendations.append(f"映射到NTOS配置: {pattern.ntos_equivalent}")
        
        # 基于关键字段生成建议
        if pattern.key_fields:
            recommendations.append(f"重点保留字段: {', '.join(pattern.key_fields[:3])}")
        
        # 基于简化程度生成建议
        if pattern.simplification_level > 0.5:
            recommendations.append("高度简化，建议后续验证功能完整性")
        else:
            recommendations.append("适度简化，保持核心功能")
        
        return recommendations
    
    def process_section(self, section_name: str, section_content: List[str],
                       analysis_result: SectionAnalysisResult,
                       context: Dict[str, Any] = None) -> Dict[str, Any]:
        """处理配置段落（简化处理）"""
        start_time = time.time()
        
        if analysis_result.strategy != ProcessingStrategy.SIMPLIFIED:
            raise ValueError(f"第三层处理器只支持SIMPLIFIED策略，收到: {analysis_result.strategy}")
        
        # 查找匹配的模式
        matched_pattern = self._find_matching_important_pattern(section_name)
        if not matched_pattern:
            raise ValueError(f"未找到匹配的重要段落模式: {section_name}")
        
        # 执行简化处理
        simplified_result = self._perform_important_section_simplification(
            section_name, section_content, matched_pattern, context
        )
        
        processing_time = time.time() - start_time
        
        self.logger.debug(f"简化处理重要段落: {section_name} - 节省时间: {analysis_result.estimated_time_saved:.3f}秒")
        
        return {
            'action': 'simplified_retain',
            'result': simplified_result,
            'quality_impact': 'medium',
            'performance_gain': analysis_result.estimated_time_saved,
            'processing_time': processing_time,
            'pattern_matched': matched_pattern.pattern,
            'ntos_equivalent': matched_pattern.ntos_equivalent
        }
    
    def _perform_important_section_simplification(self, section_name: str, 
                                                section_content: List[str],
                                                pattern: ImportantSectionPattern,
                                                context: Dict[str, Any] = None) -> Dict[str, Any]:
        """执行重要段落简化处理"""
        # 检查缓存
        cache_key = f"{section_name}:{len(section_content)}:{pattern.pattern}"
        if cache_key in self.simplification_cache:
            return self.simplification_cache[cache_key]
        
        # 根据段落类型执行不同的简化策略
        if pattern.section_type == ImportantSectionType.VPN_CONFIGURATION:
            simplified_result = self._simplify_vpn_configuration(section_content, pattern)
        elif pattern.section_type == ImportantSectionType.SYSTEM_CONFIGURATION:
            simplified_result = self._simplify_system_configuration(section_content, pattern)
        elif pattern.section_type == ImportantSectionType.ROUTING_CONFIGURATION:
            simplified_result = self._simplify_routing_configuration(section_content, pattern)
        elif pattern.section_type == ImportantSectionType.HA_CONFIGURATION:
            simplified_result = self._simplify_ha_configuration(section_content, pattern)
        elif pattern.section_type == ImportantSectionType.DHCP_CONFIGURATION:
            simplified_result = self._simplify_dhcp_configuration(section_content, pattern)
        elif pattern.section_type == ImportantSectionType.DNS_CONFIGURATION:
            simplified_result = self._simplify_dns_configuration(section_content, pattern)
        elif pattern.section_type == ImportantSectionType.SNMP_CONFIGURATION:
            simplified_result = self._simplify_snmp_configuration(section_content, pattern)
        elif pattern.section_type == ImportantSectionType.LOGGING_CONFIGURATION:
            simplified_result = self._simplify_logging_configuration(section_content, pattern)
        else:
            simplified_result = self._simplify_generic_important_section(section_content, pattern)
        
        # 添加通用信息
        simplified_result.update({
            'section_name': section_name,
            'section_type': pattern.section_type.value,
            'original_size': len(section_content),
            'simplification_level': pattern.simplification_level,
            'quality_impact': pattern.quality_impact,
            'ntos_equivalent': pattern.ntos_equivalent
        })
        
        # 缓存结果
        self.simplification_cache[cache_key] = simplified_result
        
        return simplified_result
    
    def _simplify_vpn_configuration(self, content: List[str], 
                                  pattern: ImportantSectionPattern) -> Dict[str, Any]:
        """简化VPN配置"""
        vpn_config = {'tunnels': [], 'global_settings': {}}
        
        current_tunnel = None
        for line in content:
            line = line.strip()
            
            if line.startswith('edit '):
                if current_tunnel:
                    vpn_config['tunnels'].append(current_tunnel)
                current_tunnel = {'name': line.split()[1].strip('"'), 'settings': {}}
            
            elif line.startswith('set ') and current_tunnel:
                parts = line.split(None, 2)
                if len(parts) >= 3:
                    key = parts[1]
                    value = parts[2].strip('"')
                    
                    # 只保留关键字段
                    if key in pattern.key_fields:
                        current_tunnel['settings'][key] = value
            
            elif line.startswith('set ') and not current_tunnel:
                # 全局设置
                parts = line.split(None, 2)
                if len(parts) >= 3:
                    key = parts[1]
                    value = parts[2].strip('"')
                    if key in pattern.key_fields:
                        vpn_config['global_settings'][key] = value
        
        if current_tunnel:
            vpn_config['tunnels'].append(current_tunnel)
        
        return {
            'simplified_config': vpn_config,
            'tunnels_count': len(vpn_config['tunnels']),
            'key_fields_preserved': len([k for tunnel in vpn_config['tunnels'] 
                                       for k in tunnel['settings'].keys() 
                                       if k in pattern.key_fields])
        }
    
    def _simplify_system_configuration(self, content: List[str], 
                                     pattern: ImportantSectionPattern) -> Dict[str, Any]:
        """简化系统配置"""
        system_config = {'global_settings': {}, 'admin_accounts': [], 'profiles': []}
        
        current_section = None
        current_item = None
        
        for line in content:
            line = line.strip()
            
            if line.startswith('config '):
                current_section = line.split()[1]
                current_item = None
            
            elif line.startswith('edit ') and current_section:
                if current_item:
                    if current_section == 'admin':
                        system_config['admin_accounts'].append(current_item)
                    elif current_section == 'accprofile':
                        system_config['profiles'].append(current_item)
                
                current_item = {'name': line.split()[1].strip('"'), 'settings': {}}
            
            elif line.startswith('set '):
                parts = line.split(None, 2)
                if len(parts) >= 3:
                    key = parts[1]
                    value = parts[2].strip('"')
                    
                    if key in pattern.key_fields:
                        if current_item:
                            current_item['settings'][key] = value
                        else:
                            system_config['global_settings'][key] = value
        
        # 处理最后一个项目
        if current_item and current_section:
            if current_section == 'admin':
                system_config['admin_accounts'].append(current_item)
            elif current_section == 'accprofile':
                system_config['profiles'].append(current_item)
        
        return {
            'simplified_config': system_config,
            'admin_accounts_count': len(system_config['admin_accounts']),
            'profiles_count': len(system_config['profiles']),
            'global_settings_count': len(system_config['global_settings'])
        }
    
    def _simplify_routing_configuration(self, content: List[str], 
                                      pattern: ImportantSectionPattern) -> Dict[str, Any]:
        """简化路由配置"""
        routes = []
        
        current_route = None
        for line in content:
            line = line.strip()
            
            if line.startswith('edit '):
                if current_route:
                    routes.append(current_route)
                current_route = {'id': line.split()[1].strip('"'), 'settings': {}}
            
            elif line.startswith('set ') and current_route:
                parts = line.split(None, 2)
                if len(parts) >= 3:
                    key = parts[1]
                    value = parts[2].strip('"')
                    
                    # 路由配置保留所有关键信息
                    if key in pattern.key_fields:
                        current_route['settings'][key] = value
        
        if current_route:
            routes.append(current_route)
        
        return {
            'simplified_config': {'routes': routes},
            'routes_count': len(routes),
            'preserved_fields': sum(len(route['settings']) for route in routes)
        }
    
    def _simplify_ha_configuration(self, content: List[str], 
                                 pattern: ImportantSectionPattern) -> Dict[str, Any]:
        """简化HA配置"""
        ha_config = {}
        
        for line in content:
            line = line.strip()
            if line.startswith('set '):
                parts = line.split(None, 2)
                if len(parts) >= 3:
                    key = parts[1]
                    value = parts[2].strip('"')
                    
                    # HA配置保留关键参数
                    if key in pattern.key_fields:
                        ha_config[key] = value
        
        return {
            'simplified_config': ha_config,
            'ha_enabled': ha_config.get('mode', 'standalone') != 'standalone',
            'key_parameters_count': len(ha_config)
        }
    
    def _simplify_dhcp_configuration(self, content: List[str], 
                                   pattern: ImportantSectionPattern) -> Dict[str, Any]:
        """简化DHCP配置"""
        dhcp_servers = []
        
        current_server = None
        for line in content:
            line = line.strip()
            
            if line.startswith('edit '):
                if current_server:
                    dhcp_servers.append(current_server)
                current_server = {'id': line.split()[1].strip('"'), 'settings': {}}
            
            elif line.startswith('set ') and current_server:
                parts = line.split(None, 2)
                if len(parts) >= 3:
                    key = parts[1]
                    value = parts[2].strip('"')
                    
                    if key in pattern.key_fields:
                        current_server['settings'][key] = value
        
        if current_server:
            dhcp_servers.append(current_server)
        
        return {
            'simplified_config': {'dhcp_servers': dhcp_servers},
            'servers_count': len(dhcp_servers),
            'active_servers': sum(1 for server in dhcp_servers 
                                if server['settings'].get('status') == 'enable')
        }
    
    def _simplify_dns_configuration(self, content: List[str], 
                                  pattern: ImportantSectionPattern) -> Dict[str, Any]:
        """简化DNS配置"""
        dns_config = {}
        
        for line in content:
            line = line.strip()
            if line.startswith('set '):
                parts = line.split(None, 2)
                if len(parts) >= 3:
                    key = parts[1]
                    value = parts[2].strip('"')
                    
                    if key in pattern.key_fields:
                        dns_config[key] = value
        
        return {
            'simplified_config': dns_config,
            'dns_servers_configured': bool(dns_config.get('primary') or dns_config.get('secondary')),
            'secure_dns_enabled': 'dns-over-tls' in dns_config
        }
    
    def _simplify_snmp_configuration(self, content: List[str], 
                                   pattern: ImportantSectionPattern) -> Dict[str, Any]:
        """简化SNMP配置"""
        snmp_config = {}
        
        for line in content:
            line = line.strip()
            if line.startswith('set '):
                parts = line.split(None, 2)
                if len(parts) >= 3:
                    key = parts[1]
                    value = parts[2].strip('"')
                    
                    if key in pattern.key_fields:
                        snmp_config[key] = value
        
        return {
            'simplified_config': snmp_config,
            'snmp_enabled': snmp_config.get('status') == 'enable',
            'basic_info_configured': bool(snmp_config.get('description') and snmp_config.get('location'))
        }
    
    def _simplify_logging_configuration(self, content: List[str], 
                                      pattern: ImportantSectionPattern) -> Dict[str, Any]:
        """简化日志配置"""
        log_config = {}
        
        for line in content:
            line = line.strip()
            if line.startswith('set '):
                parts = line.split(None, 2)
                if len(parts) >= 3:
                    key = parts[1]
                    value = parts[2].strip('"')
                    
                    if key in pattern.key_fields:
                        log_config[key] = value
        
        return {
            'simplified_config': log_config,
            'resolution_enabled': log_config.get('resolve-ip') == 'enable',
            'policy_logging_enabled': log_config.get('fwpolicy-implicit-log') == 'enable'
        }
    
    def _simplify_generic_important_section(self, content: List[str], 
                                          pattern: ImportantSectionPattern) -> Dict[str, Any]:
        """简化通用重要段落"""
        config = {'entries': [], 'global_settings': {}}
        
        current_entry = None
        for line in content:
            line = line.strip()
            
            if line.startswith('edit '):
                if current_entry:
                    config['entries'].append(current_entry)
                current_entry = {'name': line.split()[1].strip('"'), 'settings': {}}
            
            elif line.startswith('set '):
                parts = line.split(None, 2)
                if len(parts) >= 3:
                    key = parts[1]
                    value = parts[2].strip('"')
                    
                    if key in pattern.key_fields:
                        if current_entry:
                            current_entry['settings'][key] = value
                        else:
                            config['global_settings'][key] = value
        
        if current_entry:
            config['entries'].append(current_entry)
        
        return {
            'simplified_config': config,
            'entries_count': len(config['entries']),
            'global_settings_count': len(config['global_settings'])
        }
    
    def get_supported_strategies(self) -> List[ProcessingStrategy]:
        """获取支持的处理策略"""
        return [ProcessingStrategy.SIMPLIFIED]
    
    def get_classification_statistics(self) -> Dict[str, Any]:
        """获取分类统计信息"""
        total_analyzed = self.processing_stats['total_sections_analyzed']
        important_retained = self.processing_stats['important_sections_retained']
        
        return {
            'total_sections_analyzed': total_analyzed,
            'important_sections_retained': important_retained,
            'important_retain_ratio': important_retained / total_analyzed if total_analyzed > 0 else 0.0,
            'simplification_applied': self.processing_stats['simplification_applied'],
            'total_time_saved': self.processing_stats['time_saved_total'],
            'average_time_saved_per_section': (
                self.processing_stats['time_saved_total'] / important_retained 
                if important_retained > 0 else 0.0
            ),
            'quality_preserved_total': self.processing_stats['quality_preserved'],
            'average_quality_impact': (
                self.processing_stats['quality_preserved'] / important_retained 
                if important_retained > 0 else 0.0
            ),
            'section_type_distribution': dict(self.processing_stats['section_type_stats']),
            'patterns_count': len(self.important_patterns),
            'cache_size': len(self.simplification_cache),
            'actual_important_mapping': self.actual_important_sections
        }
