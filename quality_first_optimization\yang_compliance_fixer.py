"""
YANG模型合规性修复器
基于质量绝对优先原则的FortiGate到NTOS转换质量修复
"""

import logging
from typing import Dict, List, Optional, Tuple, Set
from lxml import etree
import re
from dataclasses import dataclass

@dataclass
class QualityIssue:
    """质量问题数据结构"""
    issue_type: str
    severity: str  # 'critical', 'warning', 'info'
    element_name: str
    description: str
    fix_applied: bool = False
    fix_description: str = ""

class YANGComplianceFixer:
    """YANG模型合规性修复器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.quality_issues = []
        
        # YANG约束定义
        self.yang_constraints = {
            'address-group': {
                'constraint': 'count(./address-set) > 0',
                'error_message': 'The numeration of address-set can not be zero',
                'fix_strategy': 'ensure_minimum_address_set'
            },
            'address-set': {
                'constraint': 'count(./ip-set) > 0',
                'error_message': 'Ip-set can not be zero',
                'fix_strategy': 'ensure_minimum_ip_set'
            },
            'nat-pool': {
                'constraint': 'min-elements 1 for address list',
                'error_message': 'Pool addresses cannot be empty',
                'fix_strategy': 'ensure_pool_addresses'
            },
            'service-group': {
                'constraint': 'count(./service-set) > 0',
                'error_message': 'Service group cannot be empty',
                'fix_strategy': 'ensure_minimum_service_set'
            }
        }
        
        # 标准服务定义
        self.standard_services = {
            'HTTP': {'protocol': 'tcp', 'port': '80'},
            'HTTPS': {'protocol': 'tcp', 'port': '443'},
            'SSH': {'protocol': 'tcp', 'port': '22'},
            'FTP': {'protocol': 'tcp', 'port': '21'},
            'DNS': {'protocol': 'udp', 'port': '53'},
            'ALL': {'protocol': 'any', 'port': 'any'},
            'ALL_TCP': {'protocol': 'tcp', 'port': 'any'},
            'ALL_UDP': {'protocol': 'udp', 'port': 'any'}
        }
    
    def fix_xml_yang_compliance(self, xml_root: etree.Element, 
                               fortigate_config: Dict) -> Tuple[etree.Element, List[QualityIssue]]:
        """修复XML的YANG模型合规性"""
        self.quality_issues = []
        
        # 1. 修复网络对象合规性
        self._fix_network_objects_compliance(xml_root, fortigate_config)
        
        # 2. 修复NAT配置合规性
        self._fix_nat_configuration_compliance(xml_root, fortigate_config)
        
        # 3. 修复服务对象合规性
        self._fix_service_objects_compliance(xml_root, fortigate_config)
        
        # 4. 修复策略引用完整性
        self._fix_policy_references_integrity(xml_root, fortigate_config)
        
        # 5. 验证修复结果
        self._validate_fixes(xml_root)
        
        return xml_root, self.quality_issues
    
    def _fix_network_objects_compliance(self, xml_root: etree.Element, 
                                      fortigate_config: Dict) -> None:
        """修复网络对象YANG合规性"""
        network_obj = xml_root.find('.//network-obj')
        if network_obj is None:
            return
        
        # 修复address-group约束违反
        for address_group in network_obj.xpath('.//address-group'):
            group_name_elem = address_group.find('name')
            group_name = group_name_elem.text if group_name_elem is not None else 'unknown'
            
            address_sets = address_group.xpath('./address-set')
            if len(address_sets) == 0:
                # 违反YANG约束：count(./address-set) > 0
                self._fix_empty_address_group(address_group, group_name, fortigate_config)
        
        # 修复address-set约束违反
        for address_set in network_obj.xpath('.//address-set'):
            set_name_elem = address_set.find('name')
            set_name = set_name_elem.text if set_name_elem is not None else 'unknown'
            
            ip_sets = address_set.xpath('./ip-set')
            if len(ip_sets) == 0:
                # 违反YANG约束：count(./ip-set) > 0
                self._fix_empty_address_set(address_set, set_name, fortigate_config)
    
    def _fix_empty_address_group(self, address_group: etree.Element, 
                               group_name: str, fortigate_config: Dict) -> None:
        """修复空地址组"""
        # 策略1: 从FortiGate配置中查找原始成员
        original_members = self._find_original_address_group_members(group_name, fortigate_config)
        
        if original_members:
            # 使用原始成员
            for member in original_members:
                address_set = etree.SubElement(address_group, 'address-set')
                name_elem = etree.SubElement(address_set, 'name')
                name_elem.text = self._clean_object_name(member)
            
            issue = QualityIssue(
                issue_type='address_group_empty',
                severity='warning',
                element_name=group_name,
                description=f'地址组为空，违反YANG约束',
                fix_applied=True,
                fix_description=f'从FortiGate配置恢复原始成员: {original_members}'
            )
        else:
            # 策略2: 基于策略引用推断成员
            inferred_members = self._infer_address_group_members_from_policies(group_name, fortigate_config)
            
            if inferred_members:
                for member in inferred_members:
                    address_set = etree.SubElement(address_group, 'address-set')
                    name_elem = etree.SubElement(address_set, 'name')
                    name_elem.text = member
                
                issue = QualityIssue(
                    issue_type='address_group_empty',
                    severity='warning',
                    element_name=group_name,
                    description=f'地址组为空，违反YANG约束',
                    fix_applied=True,
                    fix_description=f'基于策略引用推断成员: {inferred_members}'
                )
            else:
                # 策略3: 添加默认成员
                address_set = etree.SubElement(address_group, 'address-set')
                name_elem = etree.SubElement(address_set, 'name')
                name_elem.text = 'any'
                
                issue = QualityIssue(
                    issue_type='address_group_empty',
                    severity='critical',
                    element_name=group_name,
                    description=f'地址组为空，违反YANG约束',
                    fix_applied=True,
                    fix_description='添加默认成员 "any"，需要人工验证'
                )
        
        self.quality_issues.append(issue)
        self.logger.warning(f"修复空地址组: {group_name} - {issue.fix_description}")
    
    def _fix_empty_address_set(self, address_set: etree.Element, 
                             set_name: str, fortigate_config: Dict) -> None:
        """修复空地址集"""
        # 从FortiGate配置中查找原始IP地址
        original_ip = self._find_original_address_ip(set_name, fortigate_config)
        
        if original_ip:
            # 使用原始IP地址
            ip_set = etree.SubElement(address_set, 'ip-set')
            ip_address_elem = etree.SubElement(ip_set, 'ip-address')
            ip_address_elem.text = original_ip
            
            issue = QualityIssue(
                issue_type='address_set_empty',
                severity='warning',
                element_name=set_name,
                description=f'地址集为空，违反YANG约束',
                fix_applied=True,
                fix_description=f'从FortiGate配置恢复原始IP: {original_ip}'
            )
        else:
            # 使用默认IP地址
            ip_set = etree.SubElement(address_set, 'ip-set')
            ip_address_elem = etree.SubElement(ip_set, 'ip-address')
            ip_address_elem.text = '0.0.0.0/0'
            
            issue = QualityIssue(
                issue_type='address_set_empty',
                severity='critical',
                element_name=set_name,
                description=f'地址集为空，违反YANG约束',
                fix_applied=True,
                fix_description='添加默认IP "0.0.0.0/0"，需要人工验证'
            )
        
        self.quality_issues.append(issue)
        self.logger.warning(f"修复空地址集: {set_name} - {issue.fix_description}")
    
    def _fix_nat_configuration_compliance(self, xml_root: etree.Element, 
                                        fortigate_config: Dict) -> None:
        """修复NAT配置YANG合规性"""
        nat_config = xml_root.find('.//nat')
        if nat_config is None:
            return
        
        # 修复NAT池地址约束违反
        for pool in nat_config.xpath('.//pool'):
            pool_name_elem = pool.find('name')
            pool_name = pool_name_elem.text if pool_name_elem is not None else 'unknown'
            
            addresses = pool.xpath('./address')
            if len(addresses) == 0:
                # 违反YANG约束：min-elements 1 for address list
                self._fix_empty_nat_pool(pool, pool_name, fortigate_config)
    
    def _fix_empty_nat_pool(self, pool: etree.Element, 
                          pool_name: str, fortigate_config: Dict) -> None:
        """修复空NAT池"""
        # 从FortiGate ippool配置中查找原始地址范围
        original_addresses = self._find_original_ippool_addresses(pool_name, fortigate_config)
        
        if original_addresses:
            # 使用原始地址范围
            for addr in original_addresses:
                address = etree.SubElement(pool, 'address')
                value_elem = etree.SubElement(address, 'value')
                value_elem.text = addr
            
            issue = QualityIssue(
                issue_type='nat_pool_empty',
                severity='warning',
                element_name=pool_name,
                description=f'NAT池为空，违反YANG约束',
                fix_applied=True,
                fix_description=f'从FortiGate ippool配置恢复地址: {original_addresses}'
            )
        else:
            # 使用默认地址
            address = etree.SubElement(pool, 'address')
            value_elem = etree.SubElement(address, 'value')
            value_elem.text = '*************'
            
            issue = QualityIssue(
                issue_type='nat_pool_empty',
                severity='critical',
                element_name=pool_name,
                description=f'NAT池为空，违反YANG约束',
                fix_applied=True,
                fix_description='添加默认地址 "*************"，需要人工验证'
            )
        
        self.quality_issues.append(issue)
        self.logger.warning(f"修复空NAT池: {pool_name} - {issue.fix_description}")
    
    def _fix_service_objects_compliance(self, xml_root: etree.Element, 
                                      fortigate_config: Dict) -> None:
        """修复服务对象YANG合规性"""
        service_obj = xml_root.find('.//service-obj')
        if service_obj is None:
            return
        
        # 修复service-group约束违反
        for service_group in service_obj.xpath('.//service-group'):
            group_name_elem = service_group.find('name')
            group_name = group_name_elem.text if group_name_elem is not None else 'unknown'
            
            service_sets = service_group.xpath('./service-set')
            if len(service_sets) == 0:
                # 违反YANG约束：count(./service-set) > 0
                self._fix_empty_service_group(service_group, group_name, fortigate_config)
    
    def _fix_empty_service_group(self, service_group: etree.Element, 
                               group_name: str, fortigate_config: Dict) -> None:
        """修复空服务组"""
        # 从FortiGate配置中查找原始成员
        original_members = self._find_original_service_group_members(group_name, fortigate_config)
        
        if original_members:
            # 使用原始成员
            for member in original_members:
                service_set = etree.SubElement(service_group, 'service-set')
                name_elem = etree.SubElement(service_set, 'name')
                name_elem.text = self._clean_object_name(member)
            
            issue = QualityIssue(
                issue_type='service_group_empty',
                severity='warning',
                element_name=group_name,
                description=f'服务组为空，违反YANG约束',
                fix_applied=True,
                fix_description=f'从FortiGate配置恢复原始成员: {original_members}'
            )
        else:
            # 添加默认服务成员
            service_set = etree.SubElement(service_group, 'service-set')
            name_elem = etree.SubElement(service_set, 'name')
            name_elem.text = 'ALL'
            
            issue = QualityIssue(
                issue_type='service_group_empty',
                severity='critical',
                element_name=group_name,
                description=f'服务组为空，违反YANG约束',
                fix_applied=True,
                fix_description='添加默认服务成员 "ALL"，需要人工验证'
            )
        
        self.quality_issues.append(issue)
        self.logger.warning(f"修复空服务组: {group_name} - {issue.fix_description}")
    
    def _fix_policy_references_integrity(self, xml_root: etree.Element, 
                                       fortigate_config: Dict) -> None:
        """修复策略引用完整性"""
        # 收集所有定义的对象
        defined_addresses = set(xml_root.xpath('.//address-set/name/text()'))
        defined_services = set(xml_root.xpath('.//service-set/name/text()'))
        
        # 检查策略中的引用
        for policy in xml_root.xpath('.//rule'):
            policy_name_elem = policy.find('name')
            policy_name = policy_name_elem.text if policy_name_elem is not None else 'unknown'
            
            # 检查并修复地址引用
            self._fix_policy_address_references(policy, policy_name, defined_addresses, xml_root, fortigate_config)
            
            # 检查并修复服务引用
            self._fix_policy_service_references(policy, policy_name, defined_services, xml_root, fortigate_config)
    
    def _fix_policy_address_references(self, policy: etree.Element, policy_name: str,
                                     defined_addresses: Set[str], xml_root: etree.Element,
                                     fortigate_config: Dict) -> None:
        """修复策略地址引用"""
        # 检查源地址引用
        for src_network in policy.xpath('.//source-network/name'):
            addr_name = src_network.text
            if addr_name not in defined_addresses and addr_name not in ['any', 'all']:
                # 创建缺失的地址对象
                self._create_missing_address_object(addr_name, xml_root, fortigate_config)
                defined_addresses.add(addr_name)
                
                issue = QualityIssue(
                    issue_type='missing_address_reference',
                    severity='warning',
                    element_name=f'{policy_name}::{addr_name}',
                    description=f'策略引用未定义的地址对象',
                    fix_applied=True,
                    fix_description=f'自动创建缺失的地址对象: {addr_name}'
                )
                self.quality_issues.append(issue)
        
        # 检查目标地址引用
        for dest_network in policy.xpath('.//dest-network/name'):
            addr_name = dest_network.text
            if addr_name not in defined_addresses and addr_name not in ['any', 'all']:
                # 创建缺失的地址对象
                self._create_missing_address_object(addr_name, xml_root, fortigate_config)
                defined_addresses.add(addr_name)
                
                issue = QualityIssue(
                    issue_type='missing_address_reference',
                    severity='warning',
                    element_name=f'{policy_name}::{addr_name}',
                    description=f'策略引用未定义的地址对象',
                    fix_applied=True,
                    fix_description=f'自动创建缺失的地址对象: {addr_name}'
                )
                self.quality_issues.append(issue)
    
    def _fix_policy_service_references(self, policy: etree.Element, policy_name: str,
                                     defined_services: Set[str], xml_root: etree.Element,
                                     fortigate_config: Dict) -> None:
        """修复策略服务引用"""
        for service in policy.xpath('.//service/name'):
            service_name = service.text
            if service_name not in defined_services and service_name not in ['any', 'ALL']:
                # 创建缺失的服务对象
                self._create_missing_service_object(service_name, xml_root, fortigate_config)
                defined_services.add(service_name)
                
                issue = QualityIssue(
                    issue_type='missing_service_reference',
                    severity='warning',
                    element_name=f'{policy_name}::{service_name}',
                    description=f'策略引用未定义的服务对象',
                    fix_applied=True,
                    fix_description=f'自动创建缺失的服务对象: {service_name}'
                )
                self.quality_issues.append(issue)
    
    def _create_missing_address_object(self, addr_name: str, xml_root: etree.Element,
                                     fortigate_config: Dict) -> None:
        """创建缺失的地址对象"""
        network_obj = xml_root.find('.//network-obj')
        if network_obj is None:
            return
        
        # 从FortiGate配置中查找原始地址定义
        original_addr = self._find_original_address_definition(addr_name, fortigate_config)
        
        address_set = etree.SubElement(network_obj, 'address-set')
        name_elem = etree.SubElement(address_set, 'name')
        name_elem.text = self._clean_object_name(addr_name)
        
        ip_set = etree.SubElement(address_set, 'ip-set')
        ip_address_elem = etree.SubElement(ip_set, 'ip-address')
        
        if original_addr:
            ip_address_elem.text = original_addr
        else:
            ip_address_elem.text = '0.0.0.0/0'  # 默认地址
        
        self.logger.info(f"创建缺失的地址对象: {addr_name}")
    
    def _create_missing_service_object(self, service_name: str, xml_root: etree.Element,
                                     fortigate_config: Dict) -> None:
        """创建缺失的服务对象"""
        service_obj = xml_root.find('.//service-obj')
        if service_obj is None:
            return
        
        service_set = etree.SubElement(service_obj, 'service-set')
        name_elem = etree.SubElement(service_set, 'name')
        name_elem.text = self._clean_object_name(service_name)
        
        # 使用标准服务定义或推断定义
        if service_name in self.standard_services:
            service_def = self.standard_services[service_name]
        else:
            service_def = self._infer_service_definition(service_name)
        
        protocol_elem = etree.SubElement(service_set, 'protocol')
        protocol_elem.text = service_def['protocol']
        
        if service_def['port'] != 'any':
            port_elem = etree.SubElement(service_set, 'port')
            port_elem.text = service_def['port']
        
        self.logger.info(f"创建缺失的服务对象: {service_name}")
    
    def _validate_fixes(self, xml_root: etree.Element) -> None:
        """验证修复结果"""
        validation_passed = True
        
        # 验证address-group约束
        for address_group in xml_root.xpath('.//address-group'):
            address_sets = address_group.xpath('./address-set')
            if len(address_sets) == 0:
                validation_passed = False
                group_name = address_group.find('name').text if address_group.find('name') is not None else 'unknown'
                self.logger.error(f"修复失败: 地址组 {group_name} 仍然为空")
        
        # 验证address-set约束
        for address_set in xml_root.xpath('.//address-set'):
            ip_sets = address_set.xpath('./ip-set')
            if len(ip_sets) == 0:
                validation_passed = False
                set_name = address_set.find('name').text if address_set.find('name') is not None else 'unknown'
                self.logger.error(f"修复失败: 地址集 {set_name} 仍然为空")
        
        # 验证NAT池约束
        for pool in xml_root.xpath('.//pool'):
            addresses = pool.xpath('./address')
            if len(addresses) == 0:
                validation_passed = False
                pool_name = pool.find('name').text if pool.find('name') is not None else 'unknown'
                self.logger.error(f"修复失败: NAT池 {pool_name} 仍然为空")
        
        if validation_passed:
            self.logger.info("所有YANG约束修复验证通过")
        else:
            self.logger.error("部分YANG约束修复验证失败")
    
    # 辅助方法
    def _find_original_address_group_members(self, group_name: str, fortigate_config: Dict) -> List[str]:
        """从FortiGate配置中查找原始地址组成员"""
        address_groups = fortigate_config.get('address_groups', [])
        for group in address_groups:
            if group.get('name') == group_name:
                return group.get('members', [])
        return []
    
    def _find_original_address_ip(self, addr_name: str, fortigate_config: Dict) -> Optional[str]:
        """从FortiGate配置中查找原始地址IP"""
        address_objects = fortigate_config.get('address_objects', [])
        for addr in address_objects:
            if addr.get('name') == addr_name:
                return addr.get('ip_address', '')
        return None
    
    def _find_original_ippool_addresses(self, pool_name: str, fortigate_config: Dict) -> List[str]:
        """从FortiGate配置中查找原始ippool地址"""
        ippools = fortigate_config.get('ippools', [])
        for pool in ippools:
            if pool.get('name') == pool_name:
                start_ip = pool.get('startip', '')
                end_ip = pool.get('endip', '')
                if start_ip and end_ip:
                    if start_ip == end_ip:
                        return [start_ip]
                    else:
                        return [f"{start_ip}-{end_ip}"]
        return []
    
    def _find_original_service_group_members(self, group_name: str, fortigate_config: Dict) -> List[str]:
        """从FortiGate配置中查找原始服务组成员"""
        service_groups = fortigate_config.get('service_groups', [])
        for group in service_groups:
            if group.get('name') == group_name:
                return group.get('members', [])
        return []
    
    def _find_original_address_definition(self, addr_name: str, fortigate_config: Dict) -> Optional[str]:
        """从FortiGate配置中查找原始地址定义"""
        return self._find_original_address_ip(addr_name, fortigate_config)
    
    def _infer_address_group_members_from_policies(self, group_name: str, fortigate_config: Dict) -> List[str]:
        """基于策略引用推断地址组成员"""
        # 简化实现：返回常用地址
        return ['any']
    
    def _infer_service_definition(self, service_name: str) -> Dict[str, str]:
        """推断服务定义"""
        service_name_lower = service_name.lower()
        
        if 'http' in service_name_lower:
            if 'https' in service_name_lower or 'ssl' in service_name_lower:
                return {'protocol': 'tcp', 'port': '443'}
            else:
                return {'protocol': 'tcp', 'port': '80'}
        elif 'ftp' in service_name_lower:
            return {'protocol': 'tcp', 'port': '21'}
        elif 'ssh' in service_name_lower:
            return {'protocol': 'tcp', 'port': '22'}
        elif 'dns' in service_name_lower:
            return {'protocol': 'udp', 'port': '53'}
        else:
            # 尝试从名称中提取端口号
            port_match = re.search(r'(\d+)', service_name)
            if port_match:
                return {'protocol': 'tcp', 'port': port_match.group(1)}
            else:
                return {'protocol': 'tcp', 'port': '80'}  # 默认
    
    def _clean_object_name(self, name: str) -> str:
        """清理对象名称"""
        # 移除NTOS不支持的字符
        cleaned = re.sub(r'[~!#$%^&*+|{};:"\',\\/<>?]', '_', name)
        return cleaned[:64]  # 限制长度
    
    def get_quality_report(self) -> Dict:
        """获取质量报告"""
        critical_issues = [issue for issue in self.quality_issues if issue.severity == 'critical']
        warning_issues = [issue for issue in self.quality_issues if issue.severity == 'warning']
        
        return {
            'total_issues': len(self.quality_issues),
            'critical_issues': len(critical_issues),
            'warning_issues': len(warning_issues),
            'fixes_applied': sum(1 for issue in self.quality_issues if issue.fix_applied),
            'quality_score': max(0.0, 1.0 - (len(critical_issues) * 0.1 + len(warning_issues) * 0.05)),
            'issues_detail': [
                {
                    'type': issue.issue_type,
                    'severity': issue.severity,
                    'element': issue.element_name,
                    'description': issue.description,
                    'fix_applied': issue.fix_applied,
                    'fix_description': issue.fix_description
                }
                for issue in self.quality_issues
            ]
        }
