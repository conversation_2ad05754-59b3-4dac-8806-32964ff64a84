#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速yanglint检测测试脚本
可以直接在服务器上运行来验证修复效果
"""

import os
import sys
import subprocess
import shutil

def quick_test():
    """快速测试yanglint检测"""
    print("=" * 50)
    print("快速yanglint检测测试")
    print("=" * 50)
    
    # 1. 基本环境信息
    print(f"\n1. 环境信息:")
    print(f"   工作目录: {os.getcwd()}")
    print(f"   Python版本: {sys.version.split()[0]}")
    print(f"   用户: {os.environ.get('USER', os.environ.get('USERNAME', 'unknown'))}")
    
    # 2. 检查PATH
    path = os.environ.get('PATH', '')
    print(f"\n2. PATH环境变量:")
    for p in path.split(':'):
        if 'bin' in p:
            print(f"   {p}")
    
    # 3. 直接检查yanglint
    print(f"\n3. 直接检查yanglint:")
    try:
        result = subprocess.run(['yanglint', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"   ✅ yanglint可用: {result.stdout.strip()}")
            yanglint_works = True
        else:
            print(f"   ❌ yanglint返回码: {result.returncode}")
            print(f"   错误: {result.stderr}")
            yanglint_works = False
    except FileNotFoundError:
        print("   ❌ yanglint命令未找到")
        yanglint_works = False
    except Exception as e:
        print(f"   ❌ 检查yanglint时出错: {e}")
        yanglint_works = False
    
    # 4. 使用shutil.which查找
    print(f"\n4. shutil.which查找:")
    yanglint_path = shutil.which('yanglint')
    if yanglint_path:
        print(f"   ✅ 找到yanglint: {yanglint_path}")
        print(f"   可执行: {os.access(yanglint_path, os.X_OK)}")
    else:
        print("   ❌ shutil.which未找到yanglint")
    
    # 5. 检查常见路径
    print(f"\n5. 检查常见安装路径:")
    common_paths = [
        '/usr/local/bin/yanglint',
        '/usr/bin/yanglint',
        '/opt/venv/bin/yanglint'
    ]
    
    for path in common_paths:
        if os.path.exists(path):
            print(f"   ✅ 存在: {path}")
            print(f"      可执行: {os.access(path, os.X_OK)}")
        else:
            print(f"   ❌ 不存在: {path}")
    
    # 6. 测试改进的检测函数（如果可以导入的话）
    print(f"\n6. 测试改进的检测函数:")
    
    # 尝试添加engine目录到路径
    engine_dir = os.path.join(os.getcwd(), 'engine')
    if os.path.exists(engine_dir):
        sys.path.insert(0, engine_dir)
        print(f"   添加engine目录到Python路径: {engine_dir}")
        
        try:
            from infrastructure.yang.yang_validator import YangValidator
            validator = YangValidator()
            result = validator.is_yanglint_available()
            print(f"   YangValidator.is_yanglint_available(): {result}")
        except Exception as e:
            print(f"   无法测试YangValidator: {e}")
        
        try:
            from utils.yang_validator import check_yanglint_available
            result = check_yanglint_available()
            print(f"   check_yanglint_available(): {result}")
        except Exception as e:
            print(f"   无法测试check_yanglint_available: {e}")
    else:
        print(f"   engine目录不存在: {engine_dir}")
    
    # 7. 总结
    print(f"\n7. 总结:")
    if yanglint_works:
        print("   ✅ yanglint工具可用，修复应该有效")
    else:
        print("   ❌ yanglint工具不可用，需要安装或修复PATH")
        print("   建议:")
        print("   - Ubuntu: sudo apt-get install libyang-tools")
        print("   - 检查PATH环境变量")
        print("   - 检查yanglint文件权限")
    
    print("\n" + "=" * 50)

if __name__ == "__main__":
    quick_test()
