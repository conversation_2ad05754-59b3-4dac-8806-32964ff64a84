#!/usr/bin/env python3
"""
重现地址名称引用不一致问题
通过模拟实际的转换过程来找到问题根源
"""

import sys
import os
sys.path.append('.')
sys.path.append('./engine')

def test_actual_conversion_process():
    """测试实际的转换过程"""
    print("=== 测试实际转换过程 ===")
    
    try:
        # 运行实际的转换器
        from main import main as converter_main
        
        # 备份原始的sys.argv
        original_argv = sys.argv.copy()
        
        # 设置转换参数
        sys.argv = [
            'main.py',
            '--input', 'FortiGate-100F_7-6_3510_202505161613.conf',
            '--output', 'test_output_debug.xml',
            '--verbose'
        ]
        
        print("运行转换器...")
        try:
            result = converter_main()
            print(f"转换结果: {result}")
        except SystemExit as e:
            print(f"转换完成，退出码: {e.code}")
        
        # 恢复原始的sys.argv
        sys.argv = original_argv
        
        # 检查生成的XML文件
        if os.path.exists('test_output_debug.xml'):
            print("✅ 生成了测试XML文件")
            return 'test_output_debug.xml'
        else:
            print("❌ 未生成测试XML文件")
            return None
        
    except Exception as e:
        print(f"转换过程失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def analyze_generated_xml(xml_file):
    """分析生成的XML文件"""
    print(f"\n=== 分析生成的XML文件: {xml_file} ===")
    
    if not os.path.exists(xml_file):
        print("XML文件不存在")
        return False
    
    try:
        import xml.etree.ElementTree as ET
        
        # 解析XML文件
        tree = ET.parse(xml_file)
        root = tree.getroot()
        
        # 递归查找所有元素
        def find_elements_by_tag(element, tag_name):
            results = []
            if element.tag.endswith(tag_name):
                results.append(element)
            for child in element:
                results.extend(find_elements_by_tag(child, tag_name))
            return results
        
        # 查找地址对象定义
        address_definitions = {}
        for address_set in find_elements_by_tag(root, 'address-set'):
            for child in address_set:
                if child.tag.endswith('name') and child.text:
                    address_definitions[child.text] = address_set
                    break
        
        # 查找策略中的地址引用
        policy_references = []
        for policy in find_elements_by_tag(root, 'policy'):
            policy_name = "未知策略"
            # 获取策略名称
            for child in policy:
                if child.tag.endswith('name') and child.text:
                    policy_name = child.text
                    break
            
            # 查找源网络引用
            for src_net in find_elements_by_tag(policy, 'source-network'):
                for child in src_net:
                    if child.tag.endswith('name') and child.text:
                        policy_references.append(('source-network', policy_name, child.text))
                        break
            
            # 查找目标网络引用
            for dest_net in find_elements_by_tag(policy, 'dest-network'):
                for child in dest_net:
                    if child.tag.endswith('name') and child.text:
                        policy_references.append(('dest-network', policy_name, child.text))
                        break
        
        print(f"找到 {len(address_definitions)} 个地址对象定义")
        print(f"找到 {len(policy_references)} 个策略地址引用")
        
        # 分析IP地址格式的对象
        ip_pattern = r'^\d+\.\d+\.\d+\.\d+$'
        import re
        
        ip_definitions = {}
        ip_references = []
        
        for name in address_definitions.keys():
            if re.match(ip_pattern, name):
                ip_definitions[name] = 'dots'
            elif re.match(r'^\d+_\d+_\d+_\d+$', name):
                ip_definitions[name] = 'underscores'
        
        for ref_type, policy_name, ref_name in policy_references:
            if re.match(ip_pattern, ref_name):
                ip_references.append((ref_type, policy_name, ref_name, 'dots'))
            elif re.match(r'^\d+_\d+_\d+_\d+$', ref_name):
                ip_references.append((ref_type, policy_name, ref_name, 'underscores'))
        
        print(f"\nIP地址格式分析:")
        print(f"IP地址定义 (点号格式): {[name for name, fmt in ip_definitions.items() if fmt == 'dots']}")
        print(f"IP地址定义 (下划线格式): {[name for name, fmt in ip_definitions.items() if fmt == 'underscores']}")
        print(f"IP地址引用 (点号格式): {[ref_name for _, _, ref_name, fmt in ip_references if fmt == 'dots']}")
        print(f"IP地址引用 (下划线格式): {[ref_name for _, _, ref_name, fmt in ip_references if fmt == 'underscores']}")
        
        # 检查引用不一致问题
        inconsistencies = []
        for ref_type, policy_name, ref_name, ref_format in ip_references:
            if ref_format == 'underscores':
                # 引用使用下划线格式，检查是否有对应的点号格式定义
                dots_version = ref_name.replace('_', '.')
                if dots_version in ip_definitions and ref_name not in ip_definitions:
                    inconsistencies.append({
                        'policy': policy_name,
                        'reference_type': ref_type,
                        'reference_name': ref_name,
                        'expected_name': dots_version,
                        'issue': f"引用使用下划线格式，但定义使用点号格式"
                    })
        
        if inconsistencies:
            print(f"\n❌ 发现 {len(inconsistencies)} 个引用不一致问题:")
            for issue in inconsistencies:
                print(f"  策略 '{issue['policy']}' 的 {issue['reference_type']} 引用 '{issue['reference_name']}'")
                print(f"    应该引用: '{issue['expected_name']}'")
                print(f"    问题: {issue['issue']}")
        else:
            print("\n✅ 未发现引用不一致问题")
        
        return len(inconsistencies) == 0
        
    except Exception as e:
        print(f"分析XML文件失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def trace_name_transformation():
    """追踪名称转换过程"""
    print("\n=== 追踪名称转换过程 ===")
    
    try:
        from engine.utils.name_validator import clean_ntos_name
        from engine.utils.name_mapping_manager import NameMappingManager
        
        # 测试IP地址
        test_ip = "************"
        
        print(f"测试IP地址: {test_ip}")
        
        # 1. 测试名称清理函数
        clean_name = clean_ntos_name(test_ip, 64)
        print(f"1. clean_ntos_name: {test_ip} → {clean_name}")
        
        # 2. 测试名称映射管理器
        manager = NameMappingManager()
        mapped_name = manager.register_name_mapping('addresses', test_ip, context="测试")
        print(f"2. 名称映射管理器: {test_ip} → {mapped_name}")
        
        # 3. 模拟策略处理过程
        print(f"3. 模拟策略处理:")
        print(f"   原始地址: {test_ip}")
        print(f"   策略中清理后: {clean_ntos_name(test_ip, 64)}")
        
        # 4. 检查是否有其他转换函数
        try:
            from engine.utils.name_validator import sanitize_address_name
            sanitized_name = sanitize_address_name(test_ip)
            print(f"4. sanitize_address_name: {test_ip} → {sanitized_name}")
        except:
            print(f"4. sanitize_address_name: 函数不存在或出错")
        
        return True
        
    except Exception as e:
        print(f"追踪失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("重现地址名称引用不一致问题")
    print("=" * 60)
    
    # 1. 追踪名称转换过程
    trace_name_transformation()
    
    # 2. 运行实际转换过程
    xml_file = test_actual_conversion_process()
    
    # 3. 分析生成的XML
    if xml_file:
        analyze_generated_xml(xml_file)
    
    print("\n" + "=" * 60)
    print("测试完成")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
