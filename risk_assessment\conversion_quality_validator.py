"""
FortiGate到NTOS转换质量验证框架
确保优化后的转换质量不低于基线水平
"""

import json
import logging
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from enum import Enum
import difflib

class QualityMetric(Enum):
    """质量指标类型"""
    POLICY_COUNT = "policy_count"
    INTERFACE_COUNT = "interface_count"
    SERVICE_INTEGRITY = "service_integrity"
    ADDRESS_INTEGRITY = "address_integrity"
    ZONE_MAPPING = "zone_mapping"
    REFERENCE_INTEGRITY = "reference_integrity"

@dataclass
class QualityAssessment:
    """质量评估结果"""
    metric: QualityMetric
    score: float  # 0.0-1.0
    baseline_value: Any
    optimized_value: Any
    passed: bool
    details: str

class ConversionQualityValidator:
    """转换质量验证器"""
    
    def __init__(self, quality_threshold: float = 0.95):
        self.quality_threshold = quality_threshold
        self.logger = logging.getLogger(__name__)
        
        # 质量指标权重
        self.metric_weights = {
            QualityMetric.POLICY_COUNT: 0.25,
            QualityMetric.INTERFACE_COUNT: 0.20,
            QualityMetric.SERVICE_INTEGRITY: 0.20,
            QualityMetric.ADDRESS_INTEGRITY: 0.15,
            QualityMetric.ZONE_MAPPING: 0.10,
            QualityMetric.REFERENCE_INTEGRITY: 0.10
        }
    
    def validate_conversion_quality(self, baseline_result: Dict, 
                                  optimized_result: Dict,
                                  context: Dict = None) -> Dict:
        """验证转换质量"""
        
        self.logger.info("开始转换质量验证...")
        
        # 执行各项质量检查
        assessments = []
        
        # 1. 策略数量一致性检查
        policy_assessment = self._assess_policy_count(baseline_result, optimized_result)
        assessments.append(policy_assessment)
        
        # 2. 接口数量一致性检查
        interface_assessment = self._assess_interface_count(baseline_result, optimized_result)
        assessments.append(interface_assessment)
        
        # 3. 服务完整性检查
        service_assessment = self._assess_service_integrity(baseline_result, optimized_result)
        assessments.append(service_assessment)
        
        # 4. 地址完整性检查
        address_assessment = self._assess_address_integrity(baseline_result, optimized_result)
        assessments.append(address_assessment)
        
        # 5. 区域映射完整性检查
        zone_assessment = self._assess_zone_mapping(baseline_result, optimized_result)
        assessments.append(zone_assessment)
        
        # 6. 引用完整性检查
        reference_assessment = self._assess_reference_integrity(optimized_result, context)
        assessments.append(reference_assessment)
        
        # 计算总体质量分数
        overall_score = self._calculate_overall_score(assessments)
        
        # 生成质量报告
        quality_report = {
            'overall_score': overall_score,
            'passed': overall_score >= self.quality_threshold,
            'threshold': self.quality_threshold,
            'assessments': {assessment.metric.value: {
                'score': assessment.score,
                'passed': assessment.passed,
                'baseline_value': assessment.baseline_value,
                'optimized_value': assessment.optimized_value,
                'details': assessment.details
            } for assessment in assessments},
            'recommendations': self._generate_recommendations(assessments)
        }
        
        self.logger.info(f"质量验证完成，总体分数: {overall_score:.3f}")
        
        return quality_report
    
    def _assess_policy_count(self, baseline: Dict, optimized: Dict) -> QualityAssessment:
        """评估策略数量一致性"""
        
        baseline_policies = baseline.get('policies', [])
        optimized_policies = optimized.get('policies', [])
        
        baseline_count = len(baseline_policies)
        optimized_count = len(optimized_policies)
        
        # 计算一致性分数
        if baseline_count == 0:
            score = 1.0 if optimized_count == 0 else 0.0
        else:
            score = min(optimized_count / baseline_count, baseline_count / optimized_count)
        
        passed = abs(baseline_count - optimized_count) <= max(1, baseline_count * 0.05)  # 允许5%差异
        
        details = f"基线策略数: {baseline_count}, 优化后策略数: {optimized_count}"
        if not passed:
            details += f", 差异: {abs(baseline_count - optimized_count)}"
        
        return QualityAssessment(
            metric=QualityMetric.POLICY_COUNT,
            score=score,
            baseline_value=baseline_count,
            optimized_value=optimized_count,
            passed=passed,
            details=details
        )
    
    def _assess_interface_count(self, baseline: Dict, optimized: Dict) -> QualityAssessment:
        """评估接口数量一致性"""
        
        baseline_interfaces = baseline.get('interfaces', [])
        optimized_interfaces = optimized.get('interfaces', [])
        
        baseline_count = len(baseline_interfaces)
        optimized_count = len(optimized_interfaces)
        
        # 计算一致性分数
        if baseline_count == 0:
            score = 1.0 if optimized_count == 0 else 0.0
        else:
            score = min(optimized_count / baseline_count, baseline_count / optimized_count)
        
        passed = abs(baseline_count - optimized_count) <= max(1, baseline_count * 0.1)  # 允许10%差异
        
        details = f"基线接口数: {baseline_count}, 优化后接口数: {optimized_count}"
        
        return QualityAssessment(
            metric=QualityMetric.INTERFACE_COUNT,
            score=score,
            baseline_value=baseline_count,
            optimized_value=optimized_count,
            passed=passed,
            details=details
        )
    
    def _assess_service_integrity(self, baseline: Dict, optimized: Dict) -> QualityAssessment:
        """评估服务完整性"""
        
        baseline_services = set()
        optimized_services = set()
        
        # 提取基线服务
        for service in baseline.get('service_objects', []):
            baseline_services.add(service.get('name', ''))
        
        # 提取优化后服务
        for service in optimized.get('service_objects', []):
            optimized_services.add(service.get('name', ''))
        
        # 计算服务覆盖率
        if not baseline_services:
            score = 1.0
            coverage = 1.0
        else:
            covered_services = baseline_services.intersection(optimized_services)
            coverage = len(covered_services) / len(baseline_services)
            score = coverage
        
        passed = coverage >= 0.9  # 要求90%的服务覆盖率
        
        details = f"基线服务数: {len(baseline_services)}, 优化后服务数: {len(optimized_services)}, 覆盖率: {coverage:.1%}"
        
        return QualityAssessment(
            metric=QualityMetric.SERVICE_INTEGRITY,
            score=score,
            baseline_value=len(baseline_services),
            optimized_value=len(optimized_services),
            passed=passed,
            details=details
        )
    
    def _assess_address_integrity(self, baseline: Dict, optimized: Dict) -> QualityAssessment:
        """评估地址完整性"""
        
        baseline_addresses = set()
        optimized_addresses = set()
        
        # 提取基线地址
        for addr in baseline.get('address_objects', []):
            baseline_addresses.add(addr.get('name', ''))
        
        # 提取优化后地址
        for addr in optimized.get('address_objects', []):
            optimized_addresses.add(addr.get('name', ''))
        
        # 计算地址覆盖率
        if not baseline_addresses:
            score = 1.0
            coverage = 1.0
        else:
            covered_addresses = baseline_addresses.intersection(optimized_addresses)
            coverage = len(covered_addresses) / len(baseline_addresses)
            score = coverage
        
        passed = coverage >= 0.9  # 要求90%的地址覆盖率
        
        details = f"基线地址数: {len(baseline_addresses)}, 优化后地址数: {len(optimized_addresses)}, 覆盖率: {coverage:.1%}"
        
        return QualityAssessment(
            metric=QualityMetric.ADDRESS_INTEGRITY,
            score=score,
            baseline_value=len(baseline_addresses),
            optimized_value=len(optimized_addresses),
            passed=passed,
            details=details
        )
    
    def _assess_zone_mapping(self, baseline: Dict, optimized: Dict) -> QualityAssessment:
        """评估区域映射完整性"""
        
        baseline_zones = set()
        optimized_zones = set()
        
        # 提取基线区域
        for zone in baseline.get('zones', []):
            baseline_zones.add(zone.get('name', ''))
        
        # 提取优化后区域
        for zone in optimized.get('zones', []):
            optimized_zones.add(zone.get('name', ''))
        
        # 计算区域覆盖率
        if not baseline_zones:
            score = 1.0
            coverage = 1.0
        else:
            covered_zones = baseline_zones.intersection(optimized_zones)
            coverage = len(covered_zones) / len(baseline_zones)
            score = coverage
        
        passed = coverage >= 0.8  # 要求80%的区域覆盖率
        
        details = f"基线区域数: {len(baseline_zones)}, 优化后区域数: {len(optimized_zones)}, 覆盖率: {coverage:.1%}"
        
        return QualityAssessment(
            metric=QualityMetric.ZONE_MAPPING,
            score=score,
            baseline_value=len(baseline_zones),
            optimized_value=len(optimized_zones),
            passed=passed,
            details=details
        )
    
    def _assess_reference_integrity(self, optimized: Dict, context: Dict) -> QualityAssessment:
        """评估引用完整性"""
        
        # 收集所有定义的对象
        defined_services = set(service.get('name', '') for service in optimized.get('service_objects', []))
        defined_addresses = set(addr.get('name', '') for addr in optimized.get('address_objects', []))
        defined_interfaces = set(iface.get('name', '') for iface in optimized.get('interfaces', []))
        
        # 检查策略中的引用
        broken_references = []
        total_references = 0
        
        for policy in optimized.get('policies', []):
            # 检查服务引用
            services = policy.get('service', [])
            if isinstance(services, str):
                services = [services]
            
            for service in services:
                total_references += 1
                if service not in defined_services and service not in ['any', 'ALL', 'ALL_TCP', 'ALL_UDP']:
                    broken_references.append(f"策略 {policy.get('policyid', 'unknown')} 引用未定义服务: {service}")
            
            # 检查地址引用
            src_addresses = policy.get('srcaddr', [])
            dst_addresses = policy.get('dstaddr', [])
            
            if isinstance(src_addresses, str):
                src_addresses = [src_addresses]
            if isinstance(dst_addresses, str):
                dst_addresses = [dst_addresses]
            
            for addr in src_addresses + dst_addresses:
                total_references += 1
                if addr not in defined_addresses and addr not in ['any', 'all']:
                    broken_references.append(f"策略 {policy.get('policyid', 'unknown')} 引用未定义地址: {addr}")
        
        # 计算引用完整性分数
        if total_references == 0:
            score = 1.0
        else:
            score = (total_references - len(broken_references)) / total_references
        
        passed = len(broken_references) == 0
        
        details = f"总引用数: {total_references}, 破损引用数: {len(broken_references)}"
        if broken_references:
            details += f", 示例: {broken_references[0]}"
        
        return QualityAssessment(
            metric=QualityMetric.REFERENCE_INTEGRITY,
            score=score,
            baseline_value=total_references,
            optimized_value=total_references - len(broken_references),
            passed=passed,
            details=details
        )
    
    def _calculate_overall_score(self, assessments: List[QualityAssessment]) -> float:
        """计算总体质量分数"""
        
        weighted_score = 0.0
        total_weight = 0.0
        
        for assessment in assessments:
            weight = self.metric_weights.get(assessment.metric, 0.1)
            weighted_score += assessment.score * weight
            total_weight += weight
        
        return weighted_score / total_weight if total_weight > 0 else 0.0
    
    def _generate_recommendations(self, assessments: List[QualityAssessment]) -> List[str]:
        """生成质量改进建议"""
        
        recommendations = []
        
        for assessment in assessments:
            if not assessment.passed:
                if assessment.metric == QualityMetric.POLICY_COUNT:
                    recommendations.append("策略数量不一致，检查策略解析逻辑")
                elif assessment.metric == QualityMetric.INTERFACE_COUNT:
                    recommendations.append("接口数量不一致，检查接口映射配置")
                elif assessment.metric == QualityMetric.SERVICE_INTEGRITY:
                    recommendations.append("服务完整性不足，检查服务对象解析")
                elif assessment.metric == QualityMetric.ADDRESS_INTEGRITY:
                    recommendations.append("地址完整性不足，检查地址对象解析")
                elif assessment.metric == QualityMetric.ZONE_MAPPING:
                    recommendations.append("区域映射不完整，检查区域配置")
                elif assessment.metric == QualityMetric.REFERENCE_INTEGRITY:
                    recommendations.append("存在破损引用，检查对象定义和引用关系")
        
        if not recommendations:
            recommendations.append("转换质量良好，无需特别改进")
        
        return recommendations

class QualityRegressionTester:
    """质量回归测试器"""
    
    def __init__(self, test_configs_dir: str, baseline_results_dir: str):
        self.test_configs_dir = test_configs_dir
        self.baseline_results_dir = baseline_results_dir
        self.validator = ConversionQualityValidator()
        self.logger = logging.getLogger(__name__)
    
    def run_regression_tests(self, optimization_parser) -> Dict:
        """运行回归测试"""
        
        import os
        
        test_results = []
        
        # 获取测试配置文件列表
        config_files = [f for f in os.listdir(self.test_configs_dir) if f.endswith('.conf')]
        
        self.logger.info(f"开始回归测试，共 {len(config_files)} 个配置文件")
        
        for config_file in config_files:
            config_path = os.path.join(self.test_configs_dir, config_file)
            baseline_path = os.path.join(self.baseline_results_dir, f"{config_file}.baseline.json")
            
            if not os.path.exists(baseline_path):
                self.logger.warning(f"基线结果文件不存在: {baseline_path}")
                continue
            
            try:
                # 加载基线结果
                with open(baseline_path, 'r', encoding='utf-8') as f:
                    baseline_result = json.load(f)
                
                # 使用优化解析器解析
                optimized_result = optimization_parser.parse(config_path)
                
                # 质量验证
                quality_report = self.validator.validate_conversion_quality(
                    baseline_result, optimized_result
                )
                
                test_results.append({
                    'config_file': config_file,
                    'quality_score': quality_report['overall_score'],
                    'passed': quality_report['passed'],
                    'assessments': quality_report['assessments'],
                    'recommendations': quality_report['recommendations']
                })
                
                self.logger.info(f"{config_file}: 质量分数 {quality_report['overall_score']:.3f}")
                
            except Exception as e:
                self.logger.error(f"测试 {config_file} 失败: {e}")
                test_results.append({
                    'config_file': config_file,
                    'quality_score': 0.0,
                    'passed': False,
                    'error': str(e)
                })
        
        # 计算总体测试结果
        total_tests = len(test_results)
        passed_tests = sum(1 for result in test_results if result.get('passed', False))
        average_score = sum(result.get('quality_score', 0) for result in test_results) / total_tests if total_tests > 0 else 0
        
        return {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'failed_tests': total_tests - passed_tests,
            'pass_rate': passed_tests / total_tests if total_tests > 0 else 0,
            'average_quality_score': average_score,
            'detailed_results': test_results,
            'overall_passed': passed_tests / total_tests >= 0.9 if total_tests > 0 else False  # 要求90%通过率
        }

# 使用示例
def demonstrate_quality_validation():
    """演示质量验证"""
    
    # 模拟基线结果和优化结果
    baseline_result = {
        'policies': [{'policyid': '1', 'service': ['HTTP']}, {'policyid': '2', 'service': ['HTTPS']}],
        'interfaces': [{'name': 'port1'}, {'name': 'port2'}],
        'service_objects': [{'name': 'HTTP'}, {'name': 'HTTPS'}],
        'address_objects': [{'name': 'LAN_SUBNET'}, {'name': 'WAN_SUBNET'}],
        'zones': [{'name': 'trust'}, {'name': 'untrust'}]
    }
    
    optimized_result = {
        'policies': [{'policyid': '1', 'service': ['HTTP']}, {'policyid': '2', 'service': ['HTTPS']}],
        'interfaces': [{'name': 'port1'}, {'name': 'port2'}],
        'service_objects': [{'name': 'HTTP'}, {'name': 'HTTPS'}],
        'address_objects': [{'name': 'LAN_SUBNET'}],  # 缺少一个地址对象
        'zones': [{'name': 'trust'}, {'name': 'untrust'}]
    }
    
    validator = ConversionQualityValidator()
    quality_report = validator.validate_conversion_quality(baseline_result, optimized_result)
    
    print("=== 转换质量验证报告 ===")
    print(f"总体分数: {quality_report['overall_score']:.3f}")
    print(f"是否通过: {'是' if quality_report['passed'] else '否'}")
    print(f"质量阈值: {quality_report['threshold']}")
    
    print("\n各项指标:")
    for metric, assessment in quality_report['assessments'].items():
        print(f"  {metric}: {assessment['score']:.3f} ({'通过' if assessment['passed'] else '失败'})")
        print(f"    {assessment['details']}")
    
    print(f"\n改进建议:")
    for recommendation in quality_report['recommendations']:
        print(f"  • {recommendation}")

if __name__ == "__main__":
    demonstrate_quality_validation()
