"""
FortiGate配置段落四层跳过策略技术实施
基于深度风险评估的分层优化方案
"""

import re
import logging
from typing import Dict, List, Set, Tuple, Optional, Any
from dataclasses import dataclass
from enum import Enum
import json

class SectionTier(Enum):
    """配置段落层级"""
    CRITICAL = "critical"      # 关键段落 - 绝不跳过
    IMPORTANT = "important"    # 重要段落 - 保留但可简化
    CONDITIONAL = "conditional" # 条件段落 - 基于依赖分析
    SAFE_SKIP = "safe_skip"    # 安全跳过 - 确认无影响

@dataclass
class SectionAnalysisResult:
    """段落分析结果"""
    section_name: str
    tier: SectionTier
    should_skip: bool
    skip_reason: str
    dependency_check_required: bool
    estimated_time_saved: float
    risk_level: str
    ntos_yang_mapping: Optional[str] = None

class FourTierSkipStrategy:
    """四层跳过策略实施器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 第四层：关键段落 (绝不跳过)
        self.critical_sections = {
            'system interface': 'ntos-interface.yang',
            'firewall policy': 'ntos-security-policy.yang',
            'firewall address': 'ntos-network-obj.yang',
            'firewall addrgrp': 'ntos-network-obj.yang',
            'firewall service custom': 'ntos-service-obj.yang',
            'firewall service group': 'ntos-service-obj.yang',
            'router static': 'ntos-routing.yang',
            'router static6': 'ntos-routing.yang',
            'system zone': 'ntos-security-zone.yang',
            'system global': 'ntos-system.yang',
            'system dns': 'ntos-system.yang',
            'firewall vip': 'ntos-nat.yang',
            'firewall ippool': 'ntos-nat.yang',
            'vpn ipsec phase1-interface': 'ntos-ipsec.yang',
            'vpn ipsec phase2-interface': 'ntos-ipsec.yang',
            'system dhcp server': 'ntos-dhcp.yang',
            'user local': 'ntos-aaa.yang',
            'user group': 'ntos-aaa.yang',
            'user setting': 'ntos-aaa.yang',
            'firewall schedule group': 'ntos-time-obj.yang',
            'firewall schedule onetime': 'ntos-time-obj.yang',
            'firewall schedule recurring': 'ntos-time-obj.yang',
            'firewall schedule': 'ntos-time-obj.yang',
            'log setting': 'ntos-system.yang',
            'log syslogd': 'ntos-system.yang',
            'log fortianalyzer': 'ntos-system.yang',
            'log memory filter': 'ntos-system.yang',
            'log disk filter': 'ntos-system.yang',
            'vpn ssl settings': 'ntos-ssl-vpn.yang',
            'vpn ssl web portal': 'ntos-ssl-vpn.yang',
            'system settings': 'ntos-system.yang'
        }
        
        # 第三层：重要段落 (保留但可简化)
        self.important_sections = {
            'system ha': {
                'yang_mapping': 'ntos-ha.yang',
                'risk_level': 'high',
                'reason': '高可用性配置，影响系统可靠性'
            },
            'vpn certificate local': {
                'yang_mapping': 'ntos-pki.yang',
                'risk_level': 'high',
                'reason': 'VPN证书配置，影响VPN功能'
            },
            'vpn certificate ca': {
                'yang_mapping': 'ntos-pki.yang',
                'risk_level': 'high',
                'reason': 'CA证书配置，影响证书验证'
            },
            'system np6': {
                'yang_mapping': 'ntos-interface.yang',
                'risk_level': 'medium',
                'reason': '网络处理器配置，影响接口性能'
            },
            'system accprofile': {
                'yang_mapping': 'ntos-aaa.yang',
                'risk_level': 'medium',
                'reason': '访问控制配置文件，影响管理权限'
            },
            'system admin': {
                'yang_mapping': 'ntos-aaa.yang',
                'risk_level': 'medium',
                'reason': '管理员账户配置，影响管理访问'
            },
            'system sso-admin': {
                'yang_mapping': 'ntos-aaa.yang',
                'risk_level': 'medium',
                'reason': 'SSO管理员配置，影响单点登录'
            },
            'system sso-forticloud-admin': {
                'yang_mapping': 'ntos-aaa.yang',
                'risk_level': 'medium',
                'reason': 'FortiCloud SSO配置'
            }
        }
        
        # 第二层：条件段落 (基于依赖分析)
        self.conditional_sections = {
            'firewall internet-service-name': {
                'dependency_check': 'check_service_references',
                'max_size_threshold': 10000,
                'risk_level': 'medium',
                'reason': '互联网服务定义，可能被策略引用'
            },
            'system custom-language': {
                'dependency_check': 'check_language_requirements',
                'max_size_threshold': 1000,
                'risk_level': 'low',
                'reason': '自定义语言包，影响界面显示'
            },
            'system replacemsg-image': {
                'dependency_check': 'check_message_references',
                'max_size_threshold': 500,
                'risk_level': 'low',
                'reason': '替换消息图片，影响用户体验'
            },
            'log fortianalyzer2': {
                'dependency_check': 'check_logging_requirements',
                'max_size_threshold': 200,
                'risk_level': 'low',
                'reason': '辅助日志分析配置'
            },
            'log fortianalyzer3': {
                'dependency_check': 'check_logging_requirements',
                'max_size_threshold': 200,
                'risk_level': 'low',
                'reason': '辅助日志分析配置'
            }
        }
        
        # 第一层：安全跳过段落 (确认无影响)
        self.safe_skip_patterns = {
            # Web界面和GUI相关
            'widget', 'gui', 'dashboard', 'report', 'theme', 'icon', 'image',
            
            # 应用控制 (NTOS不支持)
            'application list', 'application name', 'application group',
            'application control', 'application rule', 'application custom',
            
            # Web过滤 (NTOS不支持)
            'webfilter', 'content-filter', 'url-filter', 'web-proxy',
            'explicit-proxy', 'proxy-policy',
            
            # 防病毒和IPS (NTOS不支持)
            'antivirus', 'virus-scan', 'malware', 'ips sensor', 'ips rule',
            'intrusion-prevention', 'ips global',
            
            # 无线控制器 (NTOS不支持)
            'wireless-controller', 'wifi', 'wtp', 'wtp-profile',
            'wireless-controller-hotspot20',
            
            # 其他安全功能 (NTOS不支持)
            'dlp', 'data-loss-prevention', 'file-filter', 'spam-filter',
            'email-filter', 'voip', 'icap',
            
            # 缓存和临时数据
            'entries', 'cache', 'session', 'temporary', 'filters',
            
            # 监控和统计
            'monitor', 'statistics', 'performance', 'health-check',
            
            # 其他非核心功能
            'switch-controller', 'endpoint-control', 'casb',
            'videofilter', 'dnsfilter', 'spamfilter'
        }
        
        # 统计信息
        self.analysis_stats = {
            'total_sections': 0,
            'critical_sections': 0,
            'important_sections': 0,
            'conditional_sections': 0,
            'safe_skip_sections': 0,
            'estimated_time_saved': 0.0
        }
    
    def analyze_section(self, section_name: str, section_content: List[str],
                       context: Dict = None) -> SectionAnalysisResult:
        """分析配置段落并确定处理策略"""
        
        self.analysis_stats['total_sections'] += 1
        section_size = len(section_content)
        
        # 第四层：检查关键段落
        if self._is_critical_section(section_name):
            self.analysis_stats['critical_sections'] += 1
            return SectionAnalysisResult(
                section_name=section_name,
                tier=SectionTier.CRITICAL,
                should_skip=False,
                skip_reason="关键配置段落，必须完整处理",
                dependency_check_required=False,
                estimated_time_saved=0.0,
                risk_level="critical",
                ntos_yang_mapping=self.critical_sections.get(
                    self._find_matching_pattern(section_name, self.critical_sections.keys())
                )
            )
        
        # 第三层：检查重要段落
        if self._is_important_section(section_name):
            self.analysis_stats['important_sections'] += 1
            section_info = self._get_important_section_info(section_name)
            return SectionAnalysisResult(
                section_name=section_name,
                tier=SectionTier.IMPORTANT,
                should_skip=False,
                skip_reason=f"重要段落: {section_info['reason']}",
                dependency_check_required=True,
                estimated_time_saved=0.0,
                risk_level=section_info['risk_level'],
                ntos_yang_mapping=section_info['yang_mapping']
            )
        
        # 第二层：检查条件段落
        if self._is_conditional_section(section_name):
            self.analysis_stats['conditional_sections'] += 1
            return self._analyze_conditional_section(section_name, section_content, context)
        
        # 第一层：检查安全跳过段落
        if self._is_safe_skip_section(section_name):
            self.analysis_stats['safe_skip_sections'] += 1
            time_saved = self._estimate_time_saved(section_size, 'safe_skip')
            self.analysis_stats['estimated_time_saved'] += time_saved
            
            return SectionAnalysisResult(
                section_name=section_name,
                tier=SectionTier.SAFE_SKIP,
                should_skip=True,
                skip_reason=f"安全跳过: 对NTOS转换无影响",
                dependency_check_required=False,
                estimated_time_saved=time_saved,
                risk_level="low",
                ntos_yang_mapping=None
            )
        
        # 默认：未知段落，保守处理
        return SectionAnalysisResult(
            section_name=section_name,
            tier=SectionTier.CONDITIONAL,
            should_skip=False,
            skip_reason="未知段落，保守处理",
            dependency_check_required=True,
            estimated_time_saved=0.0,
            risk_level="unknown",
            ntos_yang_mapping=None
        )
    
    def _is_critical_section(self, section_name: str) -> bool:
        """检查是否为关键段落"""
        return any(section_name.startswith(pattern) 
                  for pattern in self.critical_sections.keys())
    
    def _is_important_section(self, section_name: str) -> bool:
        """检查是否为重要段落"""
        return any(section_name.startswith(pattern) 
                  for pattern in self.important_sections.keys())
    
    def _is_conditional_section(self, section_name: str) -> bool:
        """检查是否为条件段落"""
        return any(section_name.startswith(pattern) 
                  for pattern in self.conditional_sections.keys())
    
    def _is_safe_skip_section(self, section_name: str) -> bool:
        """检查是否为安全跳过段落"""
        section_lower = section_name.lower()
        return any(pattern in section_lower for pattern in self.safe_skip_patterns)
    
    def _get_important_section_info(self, section_name: str) -> Dict:
        """获取重要段落信息"""
        for pattern, info in self.important_sections.items():
            if section_name.startswith(pattern):
                return info
        return {'yang_mapping': None, 'risk_level': 'unknown', 'reason': '未知重要段落'}
    
    def _analyze_conditional_section(self, section_name: str, section_content: List[str],
                                   context: Dict) -> SectionAnalysisResult:
        """分析条件段落"""
        
        section_size = len(section_content)
        section_config = self._get_conditional_section_config(section_name)
        
        # 检查段落大小阈值
        max_threshold = section_config.get('max_size_threshold', 1000)
        if section_size > max_threshold:
            time_saved = self._estimate_time_saved(section_size, 'size_threshold')
            self.analysis_stats['estimated_time_saved'] += time_saved
            
            return SectionAnalysisResult(
                section_name=section_name,
                tier=SectionTier.CONDITIONAL,
                should_skip=True,
                skip_reason=f"段落过大({section_size}行)，超过阈值{max_threshold}",
                dependency_check_required=False,
                estimated_time_saved=time_saved,
                risk_level=section_config['risk_level'],
                ntos_yang_mapping=None
            )
        
        # 执行依赖检查
        dependency_check = section_config.get('dependency_check')
        if dependency_check and context:
            has_dependencies = self._execute_dependency_check(
                dependency_check, section_content, context
            )
            
            if not has_dependencies:
                time_saved = self._estimate_time_saved(section_size, 'no_dependencies')
                self.analysis_stats['estimated_time_saved'] += time_saved
                
                return SectionAnalysisResult(
                    section_name=section_name,
                    tier=SectionTier.CONDITIONAL,
                    should_skip=True,
                    skip_reason="依赖检查通过，无关键引用关系",
                    dependency_check_required=True,
                    estimated_time_saved=time_saved,
                    risk_level=section_config['risk_level'],
                    ntos_yang_mapping=None
                )
        
        # 默认保留条件段落
        return SectionAnalysisResult(
            section_name=section_name,
            tier=SectionTier.CONDITIONAL,
            should_skip=False,
            skip_reason=f"条件段落: {section_config['reason']}",
            dependency_check_required=True,
            estimated_time_saved=0.0,
            risk_level=section_config['risk_level'],
            ntos_yang_mapping=None
        )
    
    def _get_conditional_section_config(self, section_name: str) -> Dict:
        """获取条件段落配置"""
        for pattern, config in self.conditional_sections.items():
            if section_name.startswith(pattern):
                return config
        return {
            'dependency_check': None,
            'max_size_threshold': 1000,
            'risk_level': 'unknown',
            'reason': '未知条件段落'
        }
    
    def _execute_dependency_check(self, check_type: str, content: List[str], 
                                context: Dict) -> bool:
        """执行依赖检查"""
        
        if check_type == 'check_service_references':
            return self._check_service_references(content, context)
        elif check_type == 'check_language_requirements':
            return self._check_language_requirements(content, context)
        elif check_type == 'check_message_references':
            return self._check_message_references(content, context)
        elif check_type == 'check_logging_requirements':
            return self._check_logging_requirements(content, context)
        else:
            # 未知检查类型，保守处理
            return True
    
    def _check_service_references(self, content: List[str], context: Dict) -> bool:
        """检查服务引用"""
        if not context or 'policies' not in context:
            return False
        
        # 提取段落中定义的服务名称
        service_names = set()
        for line in content:
            if line.strip().startswith('edit '):
                # 提取服务名称
                parts = line.strip().split()
                if len(parts) > 1:
                    service_name = parts[1].strip('"')
                    service_names.add(service_name)
        
        # 检查策略中是否引用了这些服务
        policies = context.get('policies', [])
        for policy in policies:
            policy_services = policy.get('service', [])
            if isinstance(policy_services, str):
                policy_services = [policy_services]
            
            for service in policy_services:
                if service in service_names:
                    return True
        
        return False
    
    def _check_language_requirements(self, content: List[str], context: Dict) -> bool:
        """检查语言需求"""
        # 如果NTOS需要多语言支持，则保留
        ntos_config = context.get('ntos_config', {})
        return ntos_config.get('multi_language_support', False)
    
    def _check_message_references(self, content: List[str], context: Dict) -> bool:
        """检查消息引用"""
        # 检查是否有自定义替换消息的引用
        policies = context.get('policies', [])
        for policy in policies:
            if 'replacemsg' in str(policy).lower():
                return True
        return False
    
    def _check_logging_requirements(self, content: List[str], context: Dict) -> bool:
        """检查日志需求"""
        # 如果NTOS需要详细日志配置，则保留
        ntos_config = context.get('ntos_config', {})
        return ntos_config.get('detailed_logging', False)
    
    def _find_matching_pattern(self, section_name: str, patterns: List[str]) -> Optional[str]:
        """查找匹配的模式"""
        for pattern in patterns:
            if section_name.startswith(pattern):
                return pattern
        return None
    
    def _estimate_time_saved(self, section_size: int, skip_type: str) -> float:
        """估算跳过段落节省的时间"""
        time_per_line = {
            'safe_skip': 0.001,      # 安全跳过：每行1毫秒
            'size_threshold': 0.01,   # 大段落跳过：每行10毫秒
            'no_dependencies': 0.005, # 无依赖跳过：每行5毫秒
        }
        
        return section_size * time_per_line.get(skip_type, 0.005)
    
    def get_analysis_statistics(self) -> Dict:
        """获取分析统计信息"""
        total = self.analysis_stats['total_sections']
        
        return {
            'total_sections_analyzed': total,
            'critical_sections': self.analysis_stats['critical_sections'],
            'important_sections': self.analysis_stats['important_sections'],
            'conditional_sections': self.analysis_stats['conditional_sections'],
            'safe_skip_sections': self.analysis_stats['safe_skip_sections'],
            'skip_percentage': (self.analysis_stats['safe_skip_sections'] / total * 100) if total > 0 else 0,
            'estimated_total_time_saved': self.analysis_stats['estimated_time_saved'],
            'tier_distribution': {
                'critical': self.analysis_stats['critical_sections'],
                'important': self.analysis_stats['important_sections'],
                'conditional': self.analysis_stats['conditional_sections'],
                'safe_skip': self.analysis_stats['safe_skip_sections']
            }
        }
    
    def generate_skip_strategy_report(self, analysis_results: List[SectionAnalysisResult]) -> Dict:
        """生成跳过策略报告"""
        
        report = {
            'strategy_summary': {
                'total_sections': len(analysis_results),
                'sections_to_skip': sum(1 for r in analysis_results if r.should_skip),
                'sections_to_process': sum(1 for r in analysis_results if not r.should_skip),
                'estimated_time_saved': sum(r.estimated_time_saved for r in analysis_results)
            },
            'tier_breakdown': {
                'critical': [r for r in analysis_results if r.tier == SectionTier.CRITICAL],
                'important': [r for r in analysis_results if r.tier == SectionTier.IMPORTANT],
                'conditional': [r for r in analysis_results if r.tier == SectionTier.CONDITIONAL],
                'safe_skip': [r for r in analysis_results if r.tier == SectionTier.SAFE_SKIP]
            },
            'risk_assessment': {
                'high_risk_sections': [r for r in analysis_results if r.risk_level == 'high'],
                'medium_risk_sections': [r for r in analysis_results if r.risk_level == 'medium'],
                'low_risk_sections': [r for r in analysis_results if r.risk_level == 'low']
            },
            'yang_mapping_coverage': {
                section.section_name: section.ntos_yang_mapping 
                for section in analysis_results 
                if section.ntos_yang_mapping
            }
        }
        
        return report

# 使用示例
def demonstrate_four_tier_strategy():
    """演示四层跳过策略"""
    
    strategy = FourTierSkipStrategy()
    
    # 模拟配置段落
    test_sections = [
        # 关键段落
        ('system interface', ['config system interface', 'edit port1', 'set ip ***********/24', 'end']),
        ('firewall policy', ['config firewall policy', 'edit 1', 'set srcintf port1', 'end']),
        
        # 重要段落
        ('system ha', ['config system ha', 'set group-name cluster1', 'end']),
        ('vpn certificate local', ['config vpn certificate local', 'edit cert1', 'end']),
        
        # 条件段落
        ('firewall internet-service-name', ['config firewall internet-service-name'] + ['entry'] * 5000),
        ('system custom-language', ['config system custom-language', 'edit zh-cn', 'end']),
        
        # 安全跳过段落
        ('widget', ['config widget', 'edit dashboard1', 'set type chart', 'end']),
        ('application list', ['config application list', 'edit app1', 'set category web', 'end']),
    ]
    
    context = {
        'policies': [
            {'service': ['HTTP', 'HTTPS']},
            {'service': ['custom-service-1']}
        ],
        'ntos_config': {
            'multi_language_support': False,
            'detailed_logging': False
        }
    }
    
    print("=" * 80)
    print("FortiGate配置段落四层跳过策略演示")
    print("=" * 80)
    
    analysis_results = []
    
    for section_name, section_content in test_sections:
        result = strategy.analyze_section(section_name, section_content, context)
        analysis_results.append(result)
        
        print(f"\n段落: {section_name}")
        print(f"  层级: {result.tier.value}")
        print(f"  大小: {len(section_content)} 行")
        print(f"  跳过决策: {'跳过' if result.should_skip else '处理'}")
        print(f"  风险等级: {result.risk_level}")
        print(f"  原因: {result.skip_reason}")
        print(f"  YANG映射: {result.ntos_yang_mapping or 'N/A'}")
        print(f"  预估时间节省: {result.estimated_time_saved:.3f} 秒")
    
    # 生成策略报告
    strategy_report = strategy.generate_skip_strategy_report(analysis_results)
    
    print(f"\n" + "=" * 80)
    print("四层跳过策略报告")
    print("=" * 80)
    
    summary = strategy_report['strategy_summary']
    print(f"总段落数: {summary['total_sections']}")
    print(f"跳过段落数: {summary['sections_to_skip']}")
    print(f"处理段落数: {summary['sections_to_process']}")
    print(f"跳过比例: {(summary['sections_to_skip'] / summary['total_sections'] * 100):.1f}%")
    print(f"预估时间节省: {summary['estimated_time_saved']:.3f} 秒")
    
    print(f"\n层级分布:")
    tier_breakdown = strategy_report['tier_breakdown']
    for tier, sections in tier_breakdown.items():
        print(f"  {tier}: {len(sections)} 个段落")
    
    print(f"\n风险评估:")
    risk_assessment = strategy_report['risk_assessment']
    for risk_level, sections in risk_assessment.items():
        print(f"  {risk_level}: {len(sections)} 个段落")

if __name__ == "__main__":
    demonstrate_four_tier_strategy()
