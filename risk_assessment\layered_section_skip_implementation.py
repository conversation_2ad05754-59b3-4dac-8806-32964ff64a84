"""
FortiGate配置段落分层跳过策略实施
基于风险评估的渐进式优化方案
"""

import re
import logging
from typing import Dict, List, Set, Tuple, Optional
from dataclasses import dataclass
from enum import Enum

class SectionImportance(Enum):
    """配置段落重要性等级"""
    CRITICAL = "critical"      # 关键段落，绝不跳过
    IMPORTANT = "important"    # 重要段落，条件跳过
    OPTIONAL = "optional"      # 可选段落，安全跳过
    IRRELEVANT = "irrelevant"  # 无关段落，总是跳过

@dataclass
class SectionSkipResult:
    """段落跳过结果"""
    should_skip: bool
    skip_reason: str
    importance_level: SectionImportance
    estimated_time_saved: float  # 预估节省的时间（秒）

class LayeredSectionSkipStrategy:
    """分层段落跳过策略"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 阶段一：安全跳过列表（确认对NTOS转换无影响）
        self.safe_skip_patterns = {
            # Web界面和GUI相关
            'widget', 'gui', 'dashboard', 'report', 'theme',
            'icon', 'image', 'replacemsg-image',
            
            # 缓存和临时数据
            'entries', 'cache', 'session', 'temporary',
            
            # 应用控制（NTOS不支持）
            'application list', 'application name', 'application group',
            'application control', 'application rule',
            
            # Web过滤（NTOS不支持）
            'webfilter', 'content-filter', 'url-filter',
            'web-proxy', 'explicit-proxy',
            
            # 防病毒和IPS（NTOS不支持）
            'antivirus', 'virus-scan', 'malware',
            'ips sensor', 'ips rule', 'intrusion-prevention',
            
            # 无线控制器（NTOS不支持）
            'wireless-controller', 'wifi', 'wtp',
            
            # 其他安全功能（NTOS不支持）
            'dlp', 'data-loss-prevention', 'file-filter',
            'spam-filter', 'email-filter'
        }
        
        # 关键段落（绝不跳过）
        self.critical_sections = {
            'system interface', 'system global', 'system zone',
            'firewall policy', 'firewall address', 'firewall addrgrp',
            'firewall service custom', 'firewall service group',
            'router static', 'router static6', 'system dns',
            'firewall vip', 'firewall ippool',
            'vpn ipsec phase1-interface', 'vpn ipsec phase2-interface',
            'system dhcp server', 'user local', 'user group',
            'firewall schedule onetime', 'firewall schedule recurring',
            'firewall schedule group'
        }
        
        # 重要段落（需要依赖分析）
        self.important_sections = {
            'firewall internet-service-name': {
                'dependency_check': 'check_service_references',
                'max_size_threshold': 10000  # 超过10000行考虑跳过
            },
            'system np6': {
                'dependency_check': 'check_interface_dependencies',
                'max_size_threshold': 1000
            },
            'system accprofile': {
                'dependency_check': 'check_admin_dependencies',
                'max_size_threshold': 500
            },
            'log setting': {
                'dependency_check': 'check_logging_requirements',
                'max_size_threshold': 200
            },
            'system admin': {
                'dependency_check': 'check_admin_requirements',
                'max_size_threshold': 100
            }
        }
        
        # 性能统计
        self.skip_statistics = {
            'total_sections_analyzed': 0,
            'sections_skipped': 0,
            'time_saved_estimate': 0.0,
            'skip_reasons': {}
        }
    
    def analyze_section_skip_decision(self, section_name: str, 
                                    section_content: List[str],
                                    context: Dict = None) -> SectionSkipResult:
        """分析是否应该跳过某个配置段落"""
        
        self.skip_statistics['total_sections_analyzed'] += 1
        section_size = len(section_content)
        
        # 第一层：检查是否为关键段落
        if self._is_critical_section(section_name):
            return SectionSkipResult(
                should_skip=False,
                skip_reason="关键配置段落，必须处理",
                importance_level=SectionImportance.CRITICAL,
                estimated_time_saved=0.0
            )
        
        # 第二层：检查是否可以安全跳过
        if self._is_safe_skip_section(section_name):
            time_saved = self._estimate_time_saved(section_size, 'safe_skip')
            self._update_skip_statistics('safe_skip', time_saved)
            
            return SectionSkipResult(
                should_skip=True,
                skip_reason=f"安全跳过：{section_name}对NTOS转换无影响",
                importance_level=SectionImportance.IRRELEVANT,
                estimated_time_saved=time_saved
            )
        
        # 第三层：检查是否为重要段落（需要条件判断）
        if section_name in self.important_sections:
            return self._analyze_important_section(section_name, section_content, context)
        
        # 第四层：默认处理未知段落
        return self._analyze_unknown_section(section_name, section_content, context)
    
    def _is_critical_section(self, section_name: str) -> bool:
        """检查是否为关键配置段落"""
        return any(section_name.startswith(critical) 
                  for critical in self.critical_sections)
    
    def _is_safe_skip_section(self, section_name: str) -> bool:
        """检查是否可以安全跳过"""
        section_lower = section_name.lower()
        return any(pattern in section_lower for pattern in self.safe_skip_patterns)
    
    def _analyze_important_section(self, section_name: str, 
                                 section_content: List[str],
                                 context: Dict) -> SectionSkipResult:
        """分析重要配置段落"""
        
        section_config = self.important_sections[section_name]
        section_size = len(section_content)
        
        # 检查段落大小阈值
        max_threshold = section_config.get('max_size_threshold', 1000)
        if section_size > max_threshold:
            time_saved = self._estimate_time_saved(section_size, 'size_threshold')
            self._update_skip_statistics('size_threshold', time_saved)
            
            return SectionSkipResult(
                should_skip=True,
                skip_reason=f"段落过大({section_size}行)，超过阈值{max_threshold}",
                importance_level=SectionImportance.IMPORTANT,
                estimated_time_saved=time_saved
            )
        
        # 执行依赖检查
        dependency_check = section_config.get('dependency_check')
        if dependency_check and context:
            has_dependencies = self._check_dependencies(dependency_check, section_content, context)
            
            if not has_dependencies:
                time_saved = self._estimate_time_saved(section_size, 'no_dependencies')
                self._update_skip_statistics('no_dependencies', time_saved)
                
                return SectionSkipResult(
                    should_skip=True,
                    skip_reason=f"无依赖关系，可以安全跳过",
                    importance_level=SectionImportance.IMPORTANT,
                    estimated_time_saved=time_saved
                )
        
        # 默认不跳过重要段落
        return SectionSkipResult(
            should_skip=False,
            skip_reason="重要段落，存在依赖关系",
            importance_level=SectionImportance.IMPORTANT,
            estimated_time_saved=0.0
        )
    
    def _analyze_unknown_section(self, section_name: str,
                               section_content: List[str],
                               context: Dict) -> SectionSkipResult:
        """分析未知配置段落"""
        
        section_size = len(section_content)
        
        # 基于段落大小和内容特征判断重要性
        if section_size > 5000:
            # 大型未知段落，可能包含重要数据，谨慎处理
            return SectionSkipResult(
                should_skip=False,
                skip_reason=f"大型未知段落({section_size}行)，需要详细分析",
                importance_level=SectionImportance.OPTIONAL,
                estimated_time_saved=0.0
            )
        
        elif section_size > 1000:
            # 中型未知段落，条件跳过
            if self._has_policy_references(section_content, context):
                return SectionSkipResult(
                    should_skip=False,
                    skip_reason="检测到策略引用，不能跳过",
                    importance_level=SectionImportance.OPTIONAL,
                    estimated_time_saved=0.0
                )
            else:
                time_saved = self._estimate_time_saved(section_size, 'medium_unknown')
                self._update_skip_statistics('medium_unknown', time_saved)
                
                return SectionSkipResult(
                    should_skip=True,
                    skip_reason=f"中型未知段落，无策略引用",
                    importance_level=SectionImportance.OPTIONAL,
                    estimated_time_saved=time_saved
                )
        
        else:
            # 小型未知段落，安全跳过
            time_saved = self._estimate_time_saved(section_size, 'small_unknown')
            self._update_skip_statistics('small_unknown', time_saved)
            
            return SectionSkipResult(
                should_skip=True,
                skip_reason=f"小型未知段落({section_size}行)，安全跳过",
                importance_level=SectionImportance.OPTIONAL,
                estimated_time_saved=time_saved
            )
    
    def _check_dependencies(self, check_type: str, content: List[str], context: Dict) -> bool:
        """检查段落依赖关系"""
        
        if check_type == 'check_service_references':
            return self._check_service_references(content, context)
        elif check_type == 'check_interface_dependencies':
            return self._check_interface_dependencies(content, context)
        elif check_type == 'check_admin_dependencies':
            return self._check_admin_dependencies(content, context)
        elif check_type == 'check_logging_requirements':
            return self._check_logging_requirements(content, context)
        else:
            return True  # 未知检查类型，保守处理
    
    def _check_service_references(self, content: List[str], context: Dict) -> bool:
        """检查服务引用"""
        if not context or 'policies' not in context:
            return True
        
        # 提取段落中定义的服务名称
        service_names = set()
        for line in content:
            if line.strip().startswith('edit '):
                service_name = line.strip().split('"')[1] if '"' in line else line.strip().split()[1]
                service_names.add(service_name)
        
        # 检查策略中是否引用了这些服务
        policies = context.get('policies', [])
        for policy in policies:
            policy_services = policy.get('service', [])
            if isinstance(policy_services, str):
                policy_services = [policy_services]
            
            for service in policy_services:
                if service in service_names:
                    return True
        
        return False
    
    def _check_interface_dependencies(self, content: List[str], context: Dict) -> bool:
        """检查接口依赖"""
        # 简化实现：如果内容中包含接口相关关键词，认为有依赖
        interface_keywords = ['interface', 'port', 'speed', 'duplex', 'mtu']
        content_text = ' '.join(content).lower()
        
        return any(keyword in content_text for keyword in interface_keywords)
    
    def _check_admin_dependencies(self, content: List[str], context: Dict) -> bool:
        """检查管理员依赖"""
        # 如果系统中只有默认管理员，可以跳过额外的管理员配置
        return len(content) > 50  # 简化判断：超过50行认为有复杂配置
    
    def _check_logging_requirements(self, content: List[str], context: Dict) -> bool:
        """检查日志需求"""
        # 如果NTOS不需要详细日志配置，可以跳过
        return False  # NTOS通常使用简化的日志配置
    
    def _has_policy_references(self, content: List[str], context: Dict) -> bool:
        """检查是否有策略引用"""
        if not context or 'policies' not in context:
            return False
        
        # 简化实现：检查内容中是否包含策略相关关键词
        policy_keywords = ['policy', 'rule', 'allow', 'deny', 'permit']
        content_text = ' '.join(content).lower()
        
        return any(keyword in content_text for keyword in policy_keywords)
    
    def _estimate_time_saved(self, section_size: int, skip_type: str) -> float:
        """估算跳过段落节省的时间"""
        
        # 基于段落大小和跳过类型估算时间节省
        time_per_line = {
            'safe_skip': 0.001,      # 安全跳过：每行1毫秒
            'size_threshold': 0.01,   # 大段落跳过：每行10毫秒
            'no_dependencies': 0.005, # 无依赖跳过：每行5毫秒
            'medium_unknown': 0.008,  # 中型未知：每行8毫秒
            'small_unknown': 0.002    # 小型未知：每行2毫秒
        }
        
        return section_size * time_per_line.get(skip_type, 0.005)
    
    def _update_skip_statistics(self, skip_reason: str, time_saved: float):
        """更新跳过统计"""
        self.skip_statistics['sections_skipped'] += 1
        self.skip_statistics['time_saved_estimate'] += time_saved
        
        if skip_reason not in self.skip_statistics['skip_reasons']:
            self.skip_statistics['skip_reasons'][skip_reason] = 0
        self.skip_statistics['skip_reasons'][skip_reason] += 1
    
    def get_skip_statistics(self) -> Dict:
        """获取跳过统计信息"""
        total_analyzed = self.skip_statistics['total_sections_analyzed']
        sections_skipped = self.skip_statistics['sections_skipped']
        
        return {
            'total_sections_analyzed': total_analyzed,
            'sections_skipped': sections_skipped,
            'sections_processed': total_analyzed - sections_skipped,
            'skip_percentage': (sections_skipped / total_analyzed * 100) if total_analyzed > 0 else 0,
            'estimated_time_saved': self.skip_statistics['time_saved_estimate'],
            'skip_reasons_breakdown': self.skip_statistics['skip_reasons']
        }

# 使用示例
def demonstrate_layered_skip_strategy():
    """演示分层跳过策略"""
    
    strategy = LayeredSectionSkipStrategy()
    
    # 模拟配置段落
    test_sections = [
        ('system interface', ['config system interface', 'edit port1', 'set ip ***********/24', 'end']),
        ('widget', ['config widget', 'edit dashboard1', 'set type chart', 'end']),
        ('firewall internet-service-name', ['config firewall internet-service-name'] + ['entry'] * 5000),
        ('application list', ['config application list', 'edit app1', 'set category web', 'end']),
        ('unknown-section', ['config unknown-section'] + ['data'] * 500)
    ]
    
    context = {
        'policies': [
            {'service': ['HTTP', 'HTTPS']},
            {'service': ['custom-service-1']}
        ]
    }
    
    print("=== 分层段落跳过策略演示 ===\n")
    
    for section_name, section_content in test_sections:
        result = strategy.analyze_section_skip_decision(section_name, section_content, context)
        
        print(f"段落: {section_name}")
        print(f"  大小: {len(section_content)} 行")
        print(f"  跳过决策: {'跳过' if result.should_skip else '处理'}")
        print(f"  重要性: {result.importance_level.value}")
        print(f"  原因: {result.skip_reason}")
        print(f"  预估时间节省: {result.estimated_time_saved:.3f} 秒")
        print()
    
    # 输出统计信息
    stats = strategy.get_skip_statistics()
    print("=== 跳过统计 ===")
    print(f"总分析段落: {stats['total_sections_analyzed']}")
    print(f"跳过段落: {stats['sections_skipped']}")
    print(f"处理段落: {stats['sections_processed']}")
    print(f"跳过比例: {stats['skip_percentage']:.1f}%")
    print(f"预估时间节省: {stats['estimated_time_saved']:.3f} 秒")
    print(f"跳过原因分布: {stats['skip_reasons_breakdown']}")

if __name__ == "__main__":
    demonstrate_layered_skip_strategy()
