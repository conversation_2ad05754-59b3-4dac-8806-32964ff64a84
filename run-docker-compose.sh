#!/bin/bash
set -e  # 遇到错误立即停止执行

# 版本信息显示
VERSION="1.0.0"
echo "NTOS配置转换工具 v${VERSION} (Docker Compose 版)"
echo "=============================================="

# 检查Docker和Docker Compose是否安装
if ! command -v docker &> /dev/null; then
    echo "错误: 未安装 Docker，请先安装 Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    # 如果没有docker-compose命令，尝试使用docker compose（新版Docker内置）
    if ! docker compose version &> /dev/null; then
        echo "错误: 未安装 Docker Compose，请先安装"
        exit 1
    else
        # 使用新版Docker内置compose
        COMPOSE_CMD="docker compose"
    fi
else
    # 使用传统的docker-compose命令
    COMPOSE_CMD="docker-compose"
fi

# 清理命令
if [ "$1" == "--clean" ]; then
    echo "清理所有Docker资源..."
    $COMPOSE_CMD down
    docker image rm config-converter:latest 2>/dev/null || true
    # 注意：不删除 libyang-builder 镜像，因为它构建耗时较长
    docker image prune -f
    echo "清理完成"
    exit 0
fi

# 检查配置文件
CONFIG_FILE="docker-application.yml"
if [ -f "$CONFIG_FILE" ]; then
    echo "使用配置文件: $CONFIG_FILE"
    export CONFIG_FILE="$(pwd)/$CONFIG_FILE"
else
    echo "警告: 未找到配置文件 $CONFIG_FILE，将使用容器内默认配置"
fi

# 确保数据目录存在
echo "创建必要的数据目录结构..."
mkdir -p data/uploads data/temp data/output data/mappings logs

echo "启动应用..."
if [ "$1" == "--rebuild" ]; then
    echo "使用 --rebuild 参数，强制重新构建镜像..."
    $COMPOSE_CMD up --build -d --force-recreate config-converter
else
    # 检查 libyang-builder 镜像是否存在
    if ! docker image inspect libyang-builder:latest >/dev/null 2>&1; then
        echo "首次运行: 构建 libyang 基础镜像 (这可能需要几分钟时间)..."
        $COMPOSE_CMD build libyang-builder
        echo "libyang 库构建完成，未来启动将复用此镜像以节省时间"
    else
        echo "复用已有的 libyang-builder 镜像"
    fi
    
    echo "构建并启动应用容器..."
    $COMPOSE_CMD up -d config-converter
fi

echo "应用启动成功！在后台运行中..."
echo "可以通过以下命令查看日志:"
echo "  $COMPOSE_CMD logs -f config-converter"
echo ""
echo "要停止应用，请运行:"
echo "  $COMPOSE_CMD down"
echo ""
echo "要清理资源，请运行:"
echo "  $0 --clean"
echo ""
echo "应用可通过 http://localhost:9005 访问"
echo "========================================" 