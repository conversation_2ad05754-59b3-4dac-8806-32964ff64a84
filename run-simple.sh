#!/bin/bash
set -e

# 配置
IMAGE_NAME="config-converter"
IMAGE_TAG="latest"
CONTAINER_NAME="configtrans-service"

# 日志函数
log() {
    echo "[$(date +"%Y-%m-%d %H:%M:%S")] $*"
}

# 参数解析
DEBUG_MODE=0
REBUILD=1
CLEAN=0
USE_DEFAULT_CONFIG=0

while [[ $# -gt 0 ]]; do
    case $1 in
        --no-rebuild)
            REBUILD=0
            shift
            ;;
        --clean)
            CLEAN=1
            shift
            ;;
        --debug)
            DEBUG_MODE=1
            shift
            ;;
        --use-default-config)
            USE_DEFAULT_CONFIG=1
            shift
            ;;
        *)
            log "未知参数: $1"
            shift
            ;;
    esac
done

# 清理操作
if [ "$CLEAN" -eq 1 ]; then
    log "清理模式: 正在停止并移除容器..."
    docker rm -f $CONTAINER_NAME 2>/dev/null || true
    exit 0
fi

# 清理旧容器（如果存在）
log "检查是否存在旧容器..."
if docker ps -a | grep -q $CONTAINER_NAME; then
    log "发现旧容器，正在移除..."
    docker rm -f $CONTAINER_NAME || true
fi

# 直接复制固定配置文件
log "准备配置文件..."
if [ "$USE_DEFAULT_CONFIG" -eq 1 ]; then
    log "使用静态配置文件，不进行环境变量替换..."
    cat > docker-application.yml.template <<EOL
debug: false
loglevel: info
host: 0.0.0.0
port: 9005
nginx:
  host: localhost
  port: 9527
  path: /api
readtimeout: 60
writetimeout: 60
maxsize: 10240
pprof: true

casbin:
  prefix: ""
  path: /app/rbac_model.conf

db:
  adapter: mysql
  conn: "root:abc.123@tcp(**************:3306)/test?parseTime=True&loc=Local"
  max_idle_conns: 10
  max_open_conns: 100
  conn_max_lifetime: 3600
  prefix: ""
  encrypt: false
  debug: false
  log_level: info
  slow_threshold: 200

redis:
  host: ***********
  port: 6379
  password: "password"
  database: 5
  dial_timeout: 10
  read_timeout: 6
  write_timeout: 6
  pool_size: 10
  min_idle_conns: 5
  max_retries: 3
  retry_timeout: 500

cache:
  driver: redis

filestorage:
  temp: /app/data/temp/
  upload: /app/data/uploads/

configtrans:
  upload: /app/data/uploads/
  tempdir: /app/data/temp/
  pythonpath: python
  enginepath: /app/engine
  mappingbasedir: /app/data/mappings/
  
  vendors:
    fortigate:
      mappingfile: interface_mapping.json
      modes:
        verify: "--mode verify --cli {file}"
        extract: "--mode extract --cli {file} --output-json {json}"
        convert: "--mode convert --cli {file} --mapping {mapping} --model {model} --version {version} --output {output} --encrypt-output {encrypt}"
EOL
    cp docker-application.yml.template docker-application.yml
fi

# 构建镜像
if [ "$REBUILD" -eq 1 ]; then
    log "构建镜像..."
    if [ "$DEBUG_MODE" -eq 1 ]; then
        log "调试模式: 不使用缓存构建镜像"
        docker build --no-cache -t $IMAGE_NAME:$IMAGE_TAG .
    else
        docker build -t $IMAGE_NAME:$IMAGE_TAG .
    fi
else
    log "跳过镜像构建..."
fi

# 创建数据目录（如果不存在）
log "创建必要的数据目录..."
mkdir -p data/uploads data/temp data/output data/mappings logs

# 设置运行参数
RUN_ARGS="-d"
ENV_ARGS=""

if [ "$DEBUG_MODE" -eq 1 ]; then
    log "调试模式: 使用交互式终端"
    RUN_ARGS="-it --rm"
    ENV_ARGS="$ENV_ARGS -e DEBUG=true"
fi

if [ "$USE_DEFAULT_CONFIG" -eq 1 ]; then
    log "使用静态配置文件，添加USE_DEFAULT_CONFIG=true环境变量"
    ENV_ARGS="$ENV_ARGS -e USE_DEFAULT_CONFIG=true"
fi

# 显示构建后的镜像信息
log "使用镜像:"
docker images | grep $IMAGE_NAME | head -1

# 启动容器
log "启动容器，使用--network=host模式连接外部服务..."
docker run $RUN_ARGS \
  --name $CONTAINER_NAME \
  --network=host \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/logs:/app/logs \
  -e TZ=Asia/Shanghai \
  $ENV_ARGS \
  $IMAGE_NAME:$IMAGE_TAG

# 如果是调试模式，直接从脚本返回（因为使用了-it）
if [ "$DEBUG_MODE" -eq 1 ]; then
    exit 0
fi

# 等待服务启动
log "等待服务启动..."
sleep 3

# 检查服务状态
log "检查容器状态..."
if ! docker ps | grep -q $CONTAINER_NAME; then
    log "错误: 容器未运行，可能启动失败"
    log "最后20行日志:"
    docker logs $CONTAINER_NAME | tail -20
    exit 1
fi

# 尝试检查API是否响应
log "检查API状态..."
MAX_RETRY=10
RETRY=0
API_RUNNING=0

while [ $RETRY -lt $MAX_RETRY ]; do
    if curl -s http://localhost:9005/ > /dev/null 2>&1; then
        API_RUNNING=1
        break
    fi
    
    RETRY=$((RETRY + 1))
    log "等待API启动... ($RETRY/$MAX_RETRY)"
    sleep 2
done

if [ $API_RUNNING -eq 1 ]; then
    log "API已成功启动"
else
    log "警告: API可能尚未完全启动，但容器正在运行"
fi

# 显示容器日志
log "最后20行容器日志:"
docker logs $CONTAINER_NAME | tail -20

# 显示访问信息
echo ""
log "服务已启动，可通过以下地址访问:"
echo "http://localhost:9005/"
echo ""
log "常用命令:"
echo "查看日志: docker logs -f $CONTAINER_NAME"
echo "停止服务: docker stop $CONTAINER_NAME"
echo "移除容器: docker rm $CONTAINER_NAME"
echo "进入容器: docker exec -it $CONTAINER_NAME /bin/bash"
echo "调试启动: $0 --debug"
echo "清理容器: $0 --clean"
echo "使用默认配置(不使用环境变量): $0 --use-default-config" 