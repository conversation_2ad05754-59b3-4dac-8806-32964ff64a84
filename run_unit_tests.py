#!/usr/bin/env python3
"""
twice-nat44单元测试运行器

简化的测试运行器，用于验证twice-nat44功能的正确性。
"""

import sys
import os
import traceback

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_data_models():
    """测试数据模型"""
    print("🧪 测试数据模型...")
    
    try:
        from engine.business.models.twice_nat44_models import (
            TwiceNat44Rule, TwiceNat44MatchConditions, TwiceNat44SnatConfig, 
            TwiceNat44DnatConfig, TwiceNat44AddressType
        )
        
        # 测试基本创建
        match_conditions = TwiceNat44MatchConditions(
            dest_network="WEB_SERVER_VIP",
            service="HTTP"
        )
        
        snat_config = TwiceNat44SnatConfig(
            address_type=TwiceNat44AddressType.INTERFACE,
            no_pat=False,
            try_no_pat=True
        )
        
        dnat_config = TwiceNat44DnatConfig(
            ipv4_address="*************",
            port=8080
        )
        
        rule = TwiceNat44Rule(
            name="test_rule",
            enabled=True,
            description="测试规则",
            match_conditions=match_conditions,
            snat_config=snat_config,
            dnat_config=dnat_config
        )
        
        # 测试验证
        assert rule.validate(), "规则验证应该通过"
        
        # 测试转换
        rule_dict = rule.to_nat_rule_dict()
        assert rule_dict["type"] == "twice-nat44", "规则类型应该是twice-nat44"
        
        print("✅ 数据模型测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 数据模型测试失败: {str(e)}")
        traceback.print_exc()
        return False


def test_fortigate_conversion():
    """测试FortiGate转换"""
    print("\n🧪 测试FortiGate转换...")
    
    try:
        from engine.business.models.twice_nat44_models import TwiceNat44Rule
        
        # FortiGate配置
        policy = {
            "name": "WEB_ACCESS_POLICY",
            "status": "enable",
            "service": ["HTTPS"],
            "schedule": "always",
            "fixedport": "disable"
        }
        
        vip = {
            "name": "WEB_SERVER_VIP",
            "extip": "************",
            "mappedip": "*************",
            "extport": "443",
            "mappedport": "8443"
        }
        
        # 转换
        rule = TwiceNat44Rule.from_fortigate_policy(policy, vip)
        
        # 验证
        assert "WEB_ACCESS_POLICY" in rule.name, "规则名称应该包含策略名称"
        assert rule.enabled, "规则应该启用"
        assert rule.match_conditions.dest_network == "WEB_SERVER_VIP", "目标网络应该正确"
        assert rule.dnat_config.ipv4_address == "*************", "DNAT IP应该正确"
        assert rule.dnat_config.port == 8443, "DNAT端口应该正确"
        
        print("✅ FortiGate转换测试通过")
        return True
        
    except Exception as e:
        print(f"❌ FortiGate转换测试失败: {str(e)}")
        traceback.print_exc()
        return False


def test_nat_generator():
    """测试NAT生成器"""
    print("\n🧪 测试NAT生成器...")
    
    try:
        from engine.generators.nat_generator import NATGenerator
        
        # 创建测试规则
        rule_dict = {
            "name": "test_nat_rule",
            "type": "twice-nat44",
            "rule_en": True,
            "desc": "测试NAT规则",
            "twice-nat44": {
                "match": {
                    "dest-network": {"name": "TEST_VIP"},
                    "service": {"name": "HTTP"}
                },
                "snat": {
                    "output-address": {},
                    "no-pat": False,
                    "try-no-pat": True
                },
                "dnat": {
                    "ipv4-address": "**********",
                    "port": 8080
                }
            }
        }
        
        # 生成XML
        nat_generator = NATGenerator()
        rule_element = nat_generator._create_nat_rule_element(rule_dict)
        
        # 验证
        assert rule_element is not None, "规则元素不应为空"
        assert rule_element.tag == "rule", "根元素应该是rule"
        
        name_elem = rule_element.find("name")
        assert name_elem is not None and name_elem.text == "test_nat_rule", "规则名称应该正确"
        
        twice_nat_elem = rule_element.find("twice-nat44")
        assert twice_nat_elem is not None, "应该包含twice-nat44配置"
        
        print("✅ NAT生成器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ NAT生成器测试失败: {str(e)}")
        traceback.print_exc()
        return False


def test_validator():
    """测试验证器"""
    print("\n🧪 测试验证器...")
    
    try:
        from engine.validators.twice_nat44_validator import TwiceNat44Validator
        from lxml import etree
        
        # 创建有效的规则XML
        valid_xml = """<rule>
            <name>valid_rule</name>
            <rule_en>true</rule_en>
            <twice-nat44>
                <match>
                    <dest-network><name>TEST_VIP</name></dest-network>
                </match>
                <snat>
                    <output-address/>
                </snat>
                <dnat>
                    <ipv4-address>*************</ipv4-address>
                </dnat>
            </twice-nat44>
        </rule>"""
        
        rule_element = etree.fromstring(valid_xml.encode('utf-8'))
        
        # 验证
        validator = TwiceNat44Validator()
        is_valid, errors = validator.validate_twice_nat44_rule(rule_element)
        
        assert is_valid, f"有效规则应该通过验证，错误: {errors}"
        assert len(errors) == 0, "有效规则不应该有错误"
        
        print("✅ 验证器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 验证器测试失败: {str(e)}")
        traceback.print_exc()
        return False


def test_config_manager():
    """测试配置管理器"""
    print("\n🧪 测试配置管理器...")
    
    try:
        from engine.infrastructure.config.config_manager import ConfigManager
        
        config_manager = ConfigManager()
        
        # 测试默认配置
        use_twice_nat44 = config_manager.get_twice_nat44_config('use_twice_nat44')
        assert use_twice_nat44 == True, "默认应该启用twice-nat44"
        
        # 测试配置修改
        config_manager.set_twice_nat44_config('twice_nat44_debug', True)
        debug_mode = config_manager.get_twice_nat44_config('twice_nat44_debug')
        assert debug_mode == True, "配置修改应该生效"
        
        # 测试便捷方法
        assert config_manager.is_twice_nat44_enabled(), "应该启用twice-nat44"
        assert config_manager.is_twice_nat44_fallback_enabled(), "应该启用回退机制"
        
        print("✅ 配置管理器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 配置管理器测试失败: {str(e)}")
        traceback.print_exc()
        return False


def test_integration():
    """测试集成功能"""
    print("\n🧪 测试集成功能...")
    
    try:
        from engine.business.models.twice_nat44_models import TwiceNat44Rule
        from engine.generators.nat_generator import NATGenerator
        from engine.validators.twice_nat44_validator import TwiceNat44Validator
        
        # 端到端测试
        policy = {
            "name": "INTEGRATION_POLICY",
            "status": "enable",
            "service": ["HTTP"],
            "fixedport": "disable"
        }
        
        vip = {
            "name": "INTEGRATION_VIP",
            "mappedip": "**********",
            "mappedport": "9000"
        }
        
        # 数据模型转换
        rule = TwiceNat44Rule.from_fortigate_policy(policy, vip)
        rule_dict = rule.to_nat_rule_dict()
        
        # XML生成
        nat_generator = NATGenerator()
        rule_element = nat_generator._create_nat_rule_element(rule_dict)
        
        # 验证
        validator = TwiceNat44Validator()
        is_valid, errors = validator.validate_twice_nat44_rule(rule_element)
        
        assert is_valid, f"集成测试应该通过验证，错误: {errors}"
        
        print("✅ 集成功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 集成功能测试失败: {str(e)}")
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🚀 开始twice-nat44单元测试")
    print("=" * 60)
    
    tests = [
        test_data_models,
        test_fortigate_conversion,
        test_nat_generator,
        test_validator,
        test_config_manager,
        test_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有单元测试通过！twice-nat44功能工作正常。")
        return True
    else:
        print("❌ 部分单元测试失败，需要检查实现。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
