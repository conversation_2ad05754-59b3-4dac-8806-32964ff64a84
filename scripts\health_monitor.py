#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FortiGate转换器重构集成阶段 - 健康监控脚本

这个脚本提供独立的健康检查和监控功能，可以用于：
1. 系统健康状态检查
2. 性能指标收集和分析
3. 日志分析和告警
4. 自动恢复机制
5. 监控报告生成
"""

import sys
import os
import time
import json
import psutil
import logging
import argparse
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from config.production_config import config
    print("✓ 配置模块导入成功")
except ImportError as e:
    print(f"✗ 配置模块导入失败: {e}")
    sys.exit(1)


class HealthMonitor:
    """健康监控器"""
    
    def __init__(self):
        self.start_time = datetime.now()
        self.check_history = []
        self.alert_history = []
        self.recovery_attempts = 0
        
        # 设置日志
        self._setup_logging()
    
    def _setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(os.path.join(config.LOG_DIR, 'health_monitor.log')),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('HealthMonitor')
    
    def check_system_health(self) -> Dict[str, Any]:
        """检查系统健康状态"""
        health_status = {
            "timestamp": datetime.now().isoformat(),
            "overall_status": "healthy",
            "checks": {},
            "alerts": [],
            "recommendations": []
        }
        
        try:
            # 1. CPU检查
            cpu_check = self._check_cpu()
            health_status["checks"]["cpu"] = cpu_check
            if not cpu_check["healthy"]:
                health_status["overall_status"] = "warning"
                health_status["alerts"].append(cpu_check["message"])
            
            # 2. 内存检查
            memory_check = self._check_memory()
            health_status["checks"]["memory"] = memory_check
            if not memory_check["healthy"]:
                health_status["overall_status"] = "critical" if memory_check["critical"] else "warning"
                health_status["alerts"].append(memory_check["message"])
            
            # 3. 磁盘检查
            disk_check = self._check_disk()
            health_status["checks"]["disk"] = disk_check
            if not disk_check["healthy"]:
                health_status["overall_status"] = "warning"
                health_status["alerts"].append(disk_check["message"])
            
            # 4. 进程检查
            process_check = self._check_processes()
            health_status["checks"]["processes"] = process_check
            if not process_check["healthy"]:
                health_status["overall_status"] = "critical"
                health_status["alerts"].append(process_check["message"])
            
            # 5. 日志检查
            log_check = self._check_logs()
            health_status["checks"]["logs"] = log_check
            if not log_check["healthy"]:
                if health_status["overall_status"] == "healthy":
                    health_status["overall_status"] = "warning"
                health_status["alerts"].append(log_check["message"])
            
            # 6. 配置检查
            config_check = self._check_configuration()
            health_status["checks"]["configuration"] = config_check
            if not config_check["healthy"]:
                health_status["overall_status"] = "critical"
                health_status["alerts"].append(config_check["message"])
            
            # 生成建议
            health_status["recommendations"] = self._generate_recommendations(health_status)
            
        except Exception as e:
            health_status["overall_status"] = "error"
            health_status["error"] = str(e)
            self.logger.error(f"健康检查异常: {str(e)}")
        
        # 记录检查历史
        self.check_history.append(health_status)
        if len(self.check_history) > 100:  # 保留最近100次检查
            self.check_history.pop(0)
        
        return health_status
    
    def _check_cpu(self) -> Dict[str, Any]:
        """检查CPU使用情况"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()
            load_avg = os.getloadavg() if hasattr(os, 'getloadavg') else [0, 0, 0]
            
            healthy = cpu_percent < config.CPU_ALERT_THRESHOLD
            critical = cpu_percent > 95
            
            return {
                "healthy": healthy,
                "critical": critical,
                "cpu_percent": cpu_percent,
                "cpu_count": cpu_count,
                "load_average": load_avg,
                "threshold": config.CPU_ALERT_THRESHOLD,
                "message": f"CPU使用率: {cpu_percent:.1f}%" + ("" if healthy else f" (超过阈值 {config.CPU_ALERT_THRESHOLD}%)")
            }
        except Exception as e:
            return {
                "healthy": False,
                "critical": True,
                "error": str(e),
                "message": f"CPU检查失败: {str(e)}"
            }
    
    def _check_memory(self) -> Dict[str, Any]:
        """检查内存使用情况"""
        try:
            memory = psutil.virtual_memory()
            swap = psutil.swap_memory()
            
            memory_percent = memory.percent
            healthy = memory_percent < config.MEMORY_ALERT_THRESHOLD
            critical = memory_percent > 95
            
            return {
                "healthy": healthy,
                "critical": critical,
                "memory_percent": memory_percent,
                "memory_total_gb": memory.total / (1024**3),
                "memory_available_gb": memory.available / (1024**3),
                "swap_percent": swap.percent,
                "threshold": config.MEMORY_ALERT_THRESHOLD,
                "message": f"内存使用率: {memory_percent:.1f}%" + ("" if healthy else f" (超过阈值 {config.MEMORY_ALERT_THRESHOLD}%)")
            }
        except Exception as e:
            return {
                "healthy": False,
                "critical": True,
                "error": str(e),
                "message": f"内存检查失败: {str(e)}"
            }
    
    def _check_disk(self) -> Dict[str, Any]:
        """检查磁盘使用情况"""
        try:
            disk_usage = psutil.disk_usage(config.BASE_DIR)
            disk_percent = (disk_usage.used / disk_usage.total) * 100
            
            healthy = disk_percent < 85  # 85%阈值
            
            return {
                "healthy": healthy,
                "disk_percent": disk_percent,
                "disk_total_gb": disk_usage.total / (1024**3),
                "disk_free_gb": disk_usage.free / (1024**3),
                "message": f"磁盘使用率: {disk_percent:.1f}%" + ("" if healthy else " (磁盘空间不足)")
            }
        except Exception as e:
            return {
                "healthy": False,
                "error": str(e),
                "message": f"磁盘检查失败: {str(e)}"
            }
    
    def _check_processes(self) -> Dict[str, Any]:
        """检查相关进程"""
        try:
            current_process = psutil.Process()
            
            # 检查当前进程状态
            process_info = {
                "pid": current_process.pid,
                "status": current_process.status(),
                "cpu_percent": current_process.cpu_percent(),
                "memory_mb": current_process.memory_info().rss / (1024 * 1024),
                "threads": current_process.num_threads(),
                "create_time": datetime.fromtimestamp(current_process.create_time()).isoformat()
            }
            
            healthy = (
                process_info["status"] == "running" and
                process_info["memory_mb"] < config.MAX_MEMORY_USAGE_MB
            )
            
            return {
                "healthy": healthy,
                "process_info": process_info,
                "message": f"进程状态: {process_info['status']}" + ("" if healthy else " (进程异常)")
            }
        except Exception as e:
            return {
                "healthy": False,
                "error": str(e),
                "message": f"进程检查失败: {str(e)}"
            }
    
    def _check_logs(self) -> Dict[str, Any]:
        """检查日志文件"""
        try:
            log_files = [
                config.LOG_FILE,
                config.ERROR_LOG_FILE,
                config.PERFORMANCE_LOG_FILE
            ]
            
            log_status = {}
            overall_healthy = True
            messages = []
            
            for log_file in log_files:
                if os.path.exists(log_file):
                    stat = os.stat(log_file)
                    size_mb = stat.st_size / (1024 * 1024)
                    modified_time = datetime.fromtimestamp(stat.st_mtime)
                    
                    # 检查日志文件大小
                    if size_mb > config.LOG_MAX_SIZE_MB:
                        overall_healthy = False
                        messages.append(f"{log_file} 文件过大: {size_mb:.1f}MB")
                    
                    # 检查最近是否有更新（如果服务在运行）
                    if datetime.now() - modified_time > timedelta(minutes=10):
                        messages.append(f"{log_file} 可能未正常更新")
                    
                    log_status[log_file] = {
                        "exists": True,
                        "size_mb": size_mb,
                        "modified": modified_time.isoformat()
                    }
                else:
                    log_status[log_file] = {"exists": False}
                    messages.append(f"{log_file} 不存在")
            
            return {
                "healthy": overall_healthy,
                "log_files": log_status,
                "message": "; ".join(messages) if messages else "日志文件正常"
            }
        except Exception as e:
            return {
                "healthy": False,
                "error": str(e),
                "message": f"日志检查失败: {str(e)}"
            }
    
    def _check_configuration(self) -> Dict[str, Any]:
        """检查配置有效性"""
        try:
            config_errors = config.validate_config()
            healthy = len(config_errors) == 0
            
            return {
                "healthy": healthy,
                "errors": config_errors,
                "version": config.VERSION,
                "environment": config.ENVIRONMENT,
                "message": "配置正常" if healthy else f"配置错误: {'; '.join(config_errors)}"
            }
        except Exception as e:
            return {
                "healthy": False,
                "error": str(e),
                "message": f"配置检查失败: {str(e)}"
            }
    
    def _generate_recommendations(self, health_status: Dict[str, Any]) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        # CPU建议
        cpu_check = health_status["checks"].get("cpu", {})
        if not cpu_check.get("healthy", True):
            recommendations.append("考虑减少并发处理数量或升级CPU")
        
        # 内存建议
        memory_check = health_status["checks"].get("memory", {})
        if not memory_check.get("healthy", True):
            if memory_check.get("critical", False):
                recommendations.append("立即释放内存或重启服务")
            else:
                recommendations.append("考虑增加内存或优化内存使用")
        
        # 磁盘建议
        disk_check = health_status["checks"].get("disk", {})
        if not disk_check.get("healthy", True):
            recommendations.append("清理日志文件或增加磁盘空间")
        
        # 日志建议
        log_check = health_status["checks"].get("logs", {})
        if not log_check.get("healthy", True):
            recommendations.append("检查日志轮转配置或手动清理日志")
        
        return recommendations
    
    def analyze_performance_trends(self, hours: int = 24) -> Dict[str, Any]:
        """分析性能趋势"""
        try:
            # 这里可以分析日志文件中的性能数据
            # 简化实现，返回基本统计信息
            
            recent_checks = [
                check for check in self.check_history
                if datetime.fromisoformat(check["timestamp"]) > datetime.now() - timedelta(hours=hours)
            ]
            
            if not recent_checks:
                return {"message": "没有足够的历史数据"}
            
            # 计算平均值
            cpu_values = [check["checks"]["cpu"]["cpu_percent"] for check in recent_checks if "cpu" in check["checks"]]
            memory_values = [check["checks"]["memory"]["memory_percent"] for check in recent_checks if "memory" in check["checks"]]
            
            trends = {
                "period_hours": hours,
                "total_checks": len(recent_checks),
                "cpu_trend": {
                    "average": sum(cpu_values) / len(cpu_values) if cpu_values else 0,
                    "max": max(cpu_values) if cpu_values else 0,
                    "min": min(cpu_values) if cpu_values else 0
                },
                "memory_trend": {
                    "average": sum(memory_values) / len(memory_values) if memory_values else 0,
                    "max": max(memory_values) if memory_values else 0,
                    "min": min(memory_values) if memory_values else 0
                },
                "health_score": self._calculate_health_score(recent_checks)
            }
            
            return trends
            
        except Exception as e:
            return {"error": str(e)}
    
    def _calculate_health_score(self, checks: List[Dict[str, Any]]) -> float:
        """计算健康评分 (0-100)"""
        if not checks:
            return 0
        
        healthy_count = sum(1 for check in checks if check["overall_status"] == "healthy")
        return (healthy_count / len(checks)) * 100
    
    def generate_report(self, output_file: Optional[str] = None) -> str:
        """生成监控报告"""
        current_status = self.check_system_health()
        trends = self.analyze_performance_trends()
        
        report = {
            "report_time": datetime.now().isoformat(),
            "monitor_uptime": str(datetime.now() - self.start_time),
            "current_status": current_status,
            "performance_trends": trends,
            "total_checks": len(self.check_history),
            "total_alerts": len(self.alert_history)
        }
        
        report_json = json.dumps(report, indent=2, ensure_ascii=False)
        
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report_json)
            self.logger.info(f"监控报告已保存到: {output_file}")
        
        return report_json
    
    def run_continuous_monitoring(self, interval: int = 60):
        """运行连续监控"""
        self.logger.info(f"开始连续监控，检查间隔: {interval}秒")
        
        try:
            while True:
                health_status = self.check_system_health()
                
                # 记录状态
                self.logger.info(f"健康检查完成: {health_status['overall_status']}")
                
                # 处理告警
                if health_status["overall_status"] in ["warning", "critical", "error"]:
                    self._handle_alerts(health_status)
                
                # 等待下次检查
                time.sleep(interval)
                
        except KeyboardInterrupt:
            self.logger.info("监控已停止")
        except Exception as e:
            self.logger.error(f"监控异常: {str(e)}")
    
    def _handle_alerts(self, health_status: Dict[str, Any]):
        """处理告警"""
        alert = {
            "timestamp": health_status["timestamp"],
            "status": health_status["overall_status"],
            "alerts": health_status["alerts"],
            "recommendations": health_status["recommendations"]
        }
        
        self.alert_history.append(alert)
        
        # 记录告警日志
        self.logger.warning(f"系统告警: {health_status['overall_status']}")
        for alert_msg in health_status["alerts"]:
            self.logger.warning(f"  - {alert_msg}")
        
        # 尝试自动恢复
        if health_status["overall_status"] == "critical" and config.ERROR_RECOVERY["auto_recovery_enabled"]:
            self._attempt_recovery(health_status)
    
    def _attempt_recovery(self, health_status: Dict[str, Any]):
        """尝试自动恢复"""
        if self.recovery_attempts >= config.ERROR_RECOVERY["max_recovery_attempts"]:
            self.logger.error("已达到最大恢复尝试次数，停止自动恢复")
            return
        
        self.recovery_attempts += 1
        self.logger.info(f"尝试自动恢复 (第{self.recovery_attempts}次)")
        
        try:
            # 内存恢复
            if "memory" in health_status["checks"] and not health_status["checks"]["memory"]["healthy"]:
                import gc
                gc.collect()
                self.logger.info("执行垃圾回收")
            
            # 这里可以添加更多恢复策略
            
        except Exception as e:
            self.logger.error(f"自动恢复失败: {str(e)}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="FortiGate转换器健康监控")
    parser.add_argument("--mode", choices=["check", "monitor", "report"], default="check",
                       help="运行模式: check=单次检查, monitor=连续监控, report=生成报告")
    parser.add_argument("--interval", type=int, default=60,
                       help="监控间隔（秒），仅在monitor模式下有效")
    parser.add_argument("--output", type=str,
                       help="报告输出文件路径")
    
    args = parser.parse_args()
    
    print("FortiGate转换器健康监控器")
    print(f"模式: {args.mode}")
    print("=" * 50)
    
    monitor = HealthMonitor()
    
    try:
        if args.mode == "check":
            # 单次健康检查
            health_status = monitor.check_system_health()
            print(json.dumps(health_status, indent=2, ensure_ascii=False))
            
            # 根据状态设置退出码
            if health_status["overall_status"] == "healthy":
                sys.exit(0)
            elif health_status["overall_status"] in ["warning"]:
                sys.exit(1)
            else:
                sys.exit(2)
        
        elif args.mode == "monitor":
            # 连续监控
            monitor.run_continuous_monitoring(args.interval)
        
        elif args.mode == "report":
            # 生成报告
            report = monitor.generate_report(args.output)
            if not args.output:
                print(report)
    
    except KeyboardInterrupt:
        print("\n监控已停止")
    except Exception as e:
        print(f"监控器异常: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
