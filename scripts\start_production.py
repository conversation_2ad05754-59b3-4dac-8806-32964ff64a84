#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FortiGate转换器重构集成阶段 - 生产环境启动脚本

这个脚本用于在生产环境中启动FortiGate转换器，包含完整的初始化、
健康检查、监控和优雅关闭功能。

功能特性：
1. 环境检查和配置验证
2. 日志系统初始化
3. 性能监控启动
4. 健康检查服务
5. 优雅关闭处理
6. 错误恢复机制
"""

import sys
import os
import signal
import time
import threading
import logging
import logging.config
import psutil
import gc
from typing import Optional, Dict, Any
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from config.production_config import config
    from engine.processing.stages.xml_integration import RefactoredXmlTemplateIntegrationStage
    from engine.processing.pipeline.data_flow import DataContext
    from engine.utils.logger import log
    print("✓ 所有模块导入成功")
except ImportError as e:
    print(f"✗ 模块导入失败: {e}")
    sys.exit(1)


class ProductionManager:
    """生产环境管理器"""
    
    def __init__(self):
        self.running = False
        self.integration_stage = None
        self.health_check_thread = None
        self.performance_monitor_thread = None
        self.shutdown_event = threading.Event()
        self.stats = {
            "start_time": None,
            "processed_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "last_request_time": None,
            "average_processing_time": 0.0
        }
        
        # 设置信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        if hasattr(signal, 'SIGHUP'):
            signal.signal(signal.SIGHUP, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n收到信号 {signum}，开始优雅关闭...")
        self.shutdown()
    
    def initialize(self) -> bool:
        """初始化生产环境"""
        try:
            print("开始初始化生产环境...")
            
            # 1. 环境检查
            if not self._check_environment():
                return False
            
            # 2. 配置日志系统
            if not self._setup_logging():
                return False
            
            # 3. 初始化集成阶段
            if not self._initialize_integration_stage():
                return False
            
            # 4. 启动监控服务
            if not self._start_monitoring():
                return False
            
            # 5. 运行健康检查
            if not self._run_health_check():
                return False
            
            self.stats["start_time"] = datetime.now()
            self.running = True
            
            logging.info("生产环境初始化完成")
            print("✓ 生产环境初始化完成")
            return True
            
        except Exception as e:
            print(f"✗ 初始化失败: {str(e)}")
            logging.error(f"初始化失败: {str(e)}")
            return False
    
    def _check_environment(self) -> bool:
        """检查环境要求"""
        try:
            print("检查环境要求...")
            
            # 检查Python版本
            if sys.version_info < (3, 8):
                print(f"✗ Python版本过低: {sys.version}")
                return False
            
            # 检查内存
            memory = psutil.virtual_memory()
            required_memory_gb = config.MAX_MEMORY_USAGE_MB / 1024
            if memory.total / (1024**3) < required_memory_gb:
                print(f"✗ 内存不足: 需要 {required_memory_gb}GB，当前 {memory.total/(1024**3):.1f}GB")
                return False
            
            # 检查磁盘空间
            disk = psutil.disk_usage(config.BASE_DIR)
            if disk.free / (1024**3) < 1:  # 至少1GB空闲空间
                print(f"✗ 磁盘空间不足: 需要至少1GB，当前 {disk.free/(1024**3):.1f}GB")
                return False
            
            # 检查配置有效性
            config_errors = config.validate_config()
            if config_errors:
                print("✗ 配置验证失败:")
                for error in config_errors:
                    print(f"    {error}")
                return False
            
            print("✓ 环境检查通过")
            return True
            
        except Exception as e:
            print(f"✗ 环境检查失败: {str(e)}")
            return False
    
    def _setup_logging(self) -> bool:
        """设置日志系统"""
        try:
            print("配置日志系统...")
            
            # 配置日志
            logging_config = config.get_logging_config()
            logging.config.dictConfig(logging_config)
            
            # 测试日志
            logging.info("日志系统初始化完成")
            logging.info(f"日志级别: {config.LOG_LEVEL}")
            logging.info(f"日志文件: {config.LOG_FILE}")
            
            print("✓ 日志系统配置完成")
            return True
            
        except Exception as e:
            print(f"✗ 日志系统配置失败: {str(e)}")
            return False
    
    def _initialize_integration_stage(self) -> bool:
        """初始化集成阶段"""
        try:
            print("初始化集成阶段...")
            
            # 创建集成阶段实例
            self.integration_stage = RefactoredXmlTemplateIntegrationStage()
            
            # 验证集成器注册
            execution_order = self.integration_stage.registry.get_execution_order()
            logging.info(f"注册的集成器数量: {len(execution_order)}")
            logging.info(f"执行顺序: {execution_order}")
            
            print(f"✓ 集成阶段初始化完成，注册了 {len(execution_order)} 个集成器")
            return True
            
        except Exception as e:
            print(f"✗ 集成阶段初始化失败: {str(e)}")
            logging.error(f"集成阶段初始化失败: {str(e)}")
            return False
    
    def _start_monitoring(self) -> bool:
        """启动监控服务"""
        try:
            print("启动监控服务...")
            
            # 启动健康检查线程
            if config.HEALTH_CHECK_ENABLED:
                self.health_check_thread = threading.Thread(
                    target=self._health_check_worker,
                    daemon=True
                )
                self.health_check_thread.start()
                logging.info("健康检查服务已启动")
            
            # 启动性能监控线程
            if config.PERFORMANCE_MONITORING_ENABLED:
                self.performance_monitor_thread = threading.Thread(
                    target=self._performance_monitor_worker,
                    daemon=True
                )
                self.performance_monitor_thread.start()
                logging.info("性能监控服务已启动")
            
            print("✓ 监控服务启动完成")
            return True
            
        except Exception as e:
            print(f"✗ 监控服务启动失败: {str(e)}")
            logging.error(f"监控服务启动失败: {str(e)}")
            return False
    
    def _run_health_check(self) -> bool:
        """运行初始健康检查"""
        try:
            print("运行健康检查...")
            
            # 检查集成阶段状态
            if self.integration_stage is None:
                print("✗ 集成阶段未初始化")
                return False
            
            # 检查内存使用
            process = psutil.Process()
            memory_mb = process.memory_info().rss / (1024 * 1024)
            if memory_mb > config.MAX_MEMORY_USAGE_MB * 0.8:  # 80%阈值
                print(f"⚠ 内存使用较高: {memory_mb:.1f}MB")
                logging.warning(f"内存使用较高: {memory_mb:.1f}MB")
            
            # 检查CPU使用
            cpu_percent = process.cpu_percent(interval=1)
            if cpu_percent > config.CPU_ALERT_THRESHOLD:
                print(f"⚠ CPU使用较高: {cpu_percent:.1f}%")
                logging.warning(f"CPU使用较高: {cpu_percent:.1f}%")
            
            print("✓ 健康检查通过")
            logging.info("初始健康检查通过")
            return True
            
        except Exception as e:
            print(f"✗ 健康检查失败: {str(e)}")
            logging.error(f"健康检查失败: {str(e)}")
            return False
    
    def _health_check_worker(self):
        """健康检查工作线程"""
        while self.running and not self.shutdown_event.is_set():
            try:
                # 检查系统资源
                process = psutil.Process()
                
                # 内存检查
                memory_mb = process.memory_info().rss / (1024 * 1024)
                memory_percent = (memory_mb / config.MAX_MEMORY_USAGE_MB) * 100
                
                if memory_percent > config.MEMORY_ALERT_THRESHOLD:
                    logging.warning(f"内存使用超过阈值: {memory_percent:.1f}%")
                    self._trigger_alert("memory", memory_percent)
                
                # CPU检查
                cpu_percent = process.cpu_percent()
                if cpu_percent > config.CPU_ALERT_THRESHOLD:
                    logging.warning(f"CPU使用超过阈值: {cpu_percent:.1f}%")
                    self._trigger_alert("cpu", cpu_percent)
                
                # 垃圾回收
                if memory_percent > 70:  # 内存使用超过70%时触发垃圾回收
                    gc.collect()
                    logging.info("执行垃圾回收")
                
                # 等待下次检查
                self.shutdown_event.wait(config.HEALTH_CHECK_INTERVAL)
                
            except Exception as e:
                logging.error(f"健康检查异常: {str(e)}")
                self.shutdown_event.wait(config.HEALTH_CHECK_INTERVAL)
    
    def _performance_monitor_worker(self):
        """性能监控工作线程"""
        while self.running and not self.shutdown_event.is_set():
            try:
                # 收集性能指标
                process = psutil.Process()
                
                metrics = {
                    "timestamp": datetime.now().isoformat(),
                    "cpu_percent": process.cpu_percent(),
                    "memory_mb": process.memory_info().rss / (1024 * 1024),
                    "threads": process.num_threads(),
                    "processed_requests": self.stats["processed_requests"],
                    "successful_requests": self.stats["successful_requests"],
                    "failed_requests": self.stats["failed_requests"],
                    "average_processing_time": self.stats["average_processing_time"]
                }
                
                # 记录性能日志
                performance_logger = logging.getLogger("performance")
                performance_logger.info(f"Performance metrics: {metrics}")
                
                # 等待下次监控
                self.shutdown_event.wait(config.PERFORMANCE_METRICS_INTERVAL)
                
            except Exception as e:
                logging.error(f"性能监控异常: {str(e)}")
                self.shutdown_event.wait(config.PERFORMANCE_METRICS_INTERVAL)
    
    def _trigger_alert(self, alert_type: str, value: float):
        """触发告警"""
        if not config.ALERT_ENABLED:
            return
        
        alert_message = f"告警: {alert_type} 超过阈值 - 当前值: {value:.1f}"
        
        # 记录告警日志
        logging.critical(alert_message)
        
        # 这里可以添加邮件、webhook等告警通知
        print(f"🚨 {alert_message}")
    
    def process_request(self, context: DataContext) -> bool:
        """处理转换请求"""
        if not self.running:
            logging.error("服务未运行，无法处理请求")
            return False
        
        start_time = time.time()
        self.stats["processed_requests"] += 1
        self.stats["last_request_time"] = datetime.now()
        
        try:
            # 执行集成处理
            success = self.integration_stage.process(context)
            
            # 更新统计信息
            processing_time = time.time() - start_time
            if success:
                self.stats["successful_requests"] += 1
            else:
                self.stats["failed_requests"] += 1
            
            # 更新平均处理时间
            total_requests = self.stats["processed_requests"]
            current_avg = self.stats["average_processing_time"]
            self.stats["average_processing_time"] = (
                (current_avg * (total_requests - 1) + processing_time) / total_requests
            )
            
            logging.info(f"请求处理完成: 成功={success}, 耗时={processing_time:.3f}秒")
            return success
            
        except Exception as e:
            self.stats["failed_requests"] += 1
            logging.error(f"请求处理异常: {str(e)}")
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        if not self.running:
            return {"status": "stopped"}
        
        process = psutil.Process()
        uptime = datetime.now() - self.stats["start_time"] if self.stats["start_time"] else None
        
        return {
            "status": "running",
            "version": config.VERSION,
            "uptime": str(uptime) if uptime else None,
            "cpu_percent": process.cpu_percent(),
            "memory_mb": process.memory_info().rss / (1024 * 1024),
            "threads": process.num_threads(),
            "stats": self.stats.copy()
        }
    
    def shutdown(self):
        """优雅关闭"""
        if not self.running:
            return
        
        print("开始优雅关闭...")
        logging.info("开始优雅关闭")
        
        # 设置关闭标志
        self.running = False
        self.shutdown_event.set()
        
        # 等待监控线程结束
        if self.health_check_thread and self.health_check_thread.is_alive():
            self.health_check_thread.join(timeout=5)
        
        if self.performance_monitor_thread and self.performance_monitor_thread.is_alive():
            self.performance_monitor_thread.join(timeout=5)
        
        # 记录最终统计信息
        final_stats = self.get_status()
        logging.info(f"最终统计信息: {final_stats}")
        
        print("✓ 优雅关闭完成")
        logging.info("优雅关闭完成")


def main():
    """主函数"""
    print("=" * 60)
    print("FortiGate转换器重构集成阶段 - 生产环境启动")
    print(f"版本: {config.VERSION}")
    print(f"构建日期: {config.BUILD_DATE}")
    print("=" * 60)
    
    # 创建生产环境管理器
    manager = ProductionManager()
    
    try:
        # 初始化
        if not manager.initialize():
            print("初始化失败，退出")
            sys.exit(1)
        
        print("\n🚀 FortiGate转换器已启动，等待处理请求...")
        print("按 Ctrl+C 优雅关闭")
        
        # 主循环（在实际应用中，这里会是Web服务器或消息队列处理）
        while manager.running:
            try:
                # 这里可以添加实际的请求处理逻辑
                # 例如：从队列中获取转换任务，调用 manager.process_request(context)
                
                time.sleep(1)  # 避免CPU占用过高
                
            except KeyboardInterrupt:
                break
            except Exception as e:
                logging.error(f"主循环异常: {str(e)}")
                time.sleep(5)  # 异常后等待5秒再继续
    
    except Exception as e:
        print(f"启动失败: {str(e)}")
        logging.error(f"启动失败: {str(e)}")
        sys.exit(1)
    
    finally:
        # 确保优雅关闭
        manager.shutdown()


if __name__ == "__main__":
    main()
