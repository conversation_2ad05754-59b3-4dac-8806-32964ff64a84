#!/usr/bin/env python3
"""
搜索可能导致点号替换为下划线的代码
"""

import os
import re

def search_dot_replacement():
    """搜索点号替换代码"""
    print("搜索可能导致点号替换为下划线的代码...")
    
    # 搜索模式
    patterns = [
        r"\.replace\(['\"]\.['\"]\s*,\s*['\"]_['\"]\)",  # .replace('.', '_')
        r"replace\(['\"]\.['\"]\s*,\s*['\"]_['\"]\)",    # replace('.', '_')
        r"re\.sub\([^)]*\.[^)]*_[^)]*\)",                # re.sub patterns
        r"addr_\d+_\d+_\d+_\d+",                         # addr_x_x_x_x pattern
    ]
    
    found_files = []
    
    # 搜索engine目录下的Python文件
    for root, dirs, files in os.walk("engine"):
        for file in files:
            if file.endswith(".py"):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        
                    for pattern in patterns:
                        matches = re.findall(pattern, content, re.IGNORECASE)
                        if matches:
                            found_files.append((file_path, pattern, matches))
                            print(f"发现可疑代码在 {file_path}:")
                            print(f"  模式: {pattern}")
                            print(f"  匹配: {matches}")
                            
                            # 显示匹配行的上下文
                            lines = content.split('\n')
                            for i, line in enumerate(lines):
                                if any(match in line for match in matches if match):
                                    start = max(0, i-2)
                                    end = min(len(lines), i+3)
                                    print(f"  上下文 (行 {start+1}-{end}):")
                                    for j in range(start, end):
                                        marker = ">>>" if j == i else "   "
                                        print(f"  {marker} {j+1:3d}: {lines[j]}")
                                    print()
                            
                except Exception:
                    continue
    
    if not found_files:
        print("未找到明显的点号替换代码")
    else:
        print(f"总共找到 {len(found_files)} 个可疑文件")
    
    return found_files

def search_specific_ip_patterns():
    """搜索特定的IP地址模式"""
    print("\n搜索特定的IP地址模式...")
    
    # 搜索特定的IP地址转换
    patterns = [
        r"10_10_11_15\d",     # 下划线格式的IP
        r"10\.10\.11\.15\d",  # 点号格式的IP
    ]
    
    found_files = []
    
    # 搜索engine目录下的Python文件
    for root, dirs, files in os.walk("engine"):
        for file in files:
            if file.endswith(".py"):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        
                    for pattern in patterns:
                        matches = re.findall(pattern, content)
                        if matches:
                            found_files.append((file_path, pattern, matches))
                            print(f"发现IP地址模式在 {file_path}:")
                            print(f"  模式: {pattern}")
                            print(f"  匹配: {matches}")
                            
                except Exception:
                    continue
    
    if not found_files:
        print("未找到特定的IP地址模式")
    else:
        print(f"总共找到 {len(found_files)} 个包含IP地址模式的文件")
    
    return found_files

def main():
    """主函数"""
    print("搜索点号替换代码")
    print("=" * 50)
    
    # 1. 搜索点号替换代码
    dot_replacement_files = search_dot_replacement()
    
    # 2. 搜索特定IP地址模式
    ip_pattern_files = search_specific_ip_patterns()
    
    print("\n" + "=" * 50)
    print("搜索完成")
    
    return len(dot_replacement_files) > 0 or len(ip_pattern_files) > 0

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
