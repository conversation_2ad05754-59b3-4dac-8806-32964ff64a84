#!/usr/bin/env python3
"""
简单的twice-nat44功能测试
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_basic_import():
    """测试基本导入"""
    print("🧪 测试基本导入...")
    
    try:
        from engine.business.models.twice_nat44_models import TwiceNat44Rule
        print("✅ 数据模型导入成功")
        
        from engine.generators.nat_generator import NATGenerator
        print("✅ NAT生成器导入成功")
        
        from engine.validators.twice_nat44_validator import TwiceNat44Validator
        print("✅ 验证器导入成功")
        
        from engine.infrastructure.config.config_manager import ConfigManager
        print("✅ 配置管理器导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {str(e)}")
        return False


def test_basic_functionality():
    """测试基本功能"""
    print("\n🧪 测试基本功能...")
    
    try:
        from engine.business.models.twice_nat44_models import (
            TwiceNat44Rule, TwiceNat44MatchConditions, TwiceNat44SnatConfig, 
            TwiceNat44DnatConfig, TwiceNat44AddressType
        )
        
        # 创建基本组件
        match_conditions = TwiceNat44MatchConditions(dest_network="TEST_VIP")
        snat_config = TwiceNat44SnatConfig(address_type=TwiceNat44AddressType.INTERFACE)
        dnat_config = TwiceNat44DnatConfig(ipv4_address="*************")
        
        # 创建规则
        rule = TwiceNat44Rule(
            name="test_rule",
            enabled=True,
            description="测试规则",
            match_conditions=match_conditions,
            snat_config=snat_config,
            dnat_config=dnat_config
        )
        
        print("✅ 规则创建成功")
        
        # 测试转换
        rule_dict = rule.to_nat_rule_dict()
        assert rule_dict["type"] == "twice-nat44"
        print("✅ 规则转换成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🚀 开始简单测试")
    print("=" * 40)
    
    tests = [
        test_basic_import,
        test_basic_functionality
    ]
    
    passed = 0
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n📊 测试结果: {passed}/{len(tests)} 通过")
    
    if passed == len(tests):
        print("🎉 基本测试通过！")
        return True
    else:
        print("❌ 基本测试失败！")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
