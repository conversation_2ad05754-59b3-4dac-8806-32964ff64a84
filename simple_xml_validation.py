#!/usr/bin/env python3
"""
简化的XML验证脚本
"""

import xml.etree.ElementTree as ET
import sys

def test_xml_parsing(xml_file):
    """测试XML文件解析"""
    print(f"测试XML文件: {xml_file}")
    
    try:
        tree = ET.parse(xml_file)
        root = tree.getroot()
        print(f"✅ XML解析成功")
        print(f"   根元素: {root.tag}")
        
        # 统计元素
        total_elements = len(list(root.iter()))
        print(f"   总元素数: {total_elements:,}")
        
        # 检查threat-intelligence结构
        threat_intel_count = 0
        for elem in root.iter():
            if elem.tag.endswith('threat-intelligence'):
                threat_intel_count += 1
        
        print(f"   threat-intelligence元素数: {threat_intel_count}")
        
        # 检查security-zone中是否有错误的auto元素
        invalid_auto_count = 0
        for elem in root.iter():
            if elem.tag.endswith('security-zone') and 'security-zone' in elem.tag:
                for child in elem:
                    child_tag = child.tag.split('}')[-1] if '}' in child.tag else child.tag
                    if child_tag == 'auto':
                        invalid_auto_count += 1
        
        if invalid_auto_count > 0:
            print(f"   ❌ 发现 {invalid_auto_count} 个错误放置的auto元素")
            return False
        else:
            print(f"   ✅ 没有发现错误放置的auto元素")
        
        return True
        
    except ET.ParseError as e:
        print(f"❌ XML解析失败: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 验证异常: {str(e)}")
        return False

def main():
    xml_file = "output/fortigate-z5100s-R11_backup_20250801_210727.xml"
    
    if len(sys.argv) > 1:
        xml_file = sys.argv[1]
    
    success = test_xml_parsing(xml_file)
    
    if success:
        print(f"\n🎉 XML验证成功！")
        print(f"   - XML语法正确")
        print(f"   - threat-intelligence结构修复完成")
        print(f"   - security-zone结构正确")
    else:
        print(f"\n❌ XML验证失败，需要进一步修复")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
