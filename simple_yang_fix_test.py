#!/usr/bin/env python3
"""
简单的YANG修复验证测试
"""

import sys
import os
sys.path.append('.')
sys.path.append('./engine')

def test_specific_fixes():
    """测试具体的修复问题"""
    print("=== 测试具体的YANG修复问题 ===")
    
    from engine.utils.name_validator import clean_ntos_name, clean_ntos_description
    
    # 测试原始错误中的具体问题
    print("1. 测试包含特殊字符的pool名称:")
    problem_name = "Idarı_************/28"
    fixed_name = clean_ntos_name(problem_name, 64)
    print(f"   原始: {problem_name}")
    print(f"   修复: {fixed_name}")
    
    # 检查是否还包含禁用字符
    forbidden_chars = set('`~!#$%^&*+|{};:"\'\\/<>?,')
    remaining_forbidden = set(fixed_name) & forbidden_chars
    if remaining_forbidden:
        print(f"   ❌ 仍包含禁用字符: {remaining_forbidden}")
        return False
    else:
        print(f"   ✅ 已清理所有禁用字符")
    
    print("\n2. 测试描述字段清理:")
    problem_desc = 's "************/28'
    fixed_desc = clean_ntos_description(problem_desc, 255)
    print(f"   原始: {problem_desc}")
    print(f"   修复: {fixed_desc}")
    
    # 检查描述中的禁用字符
    desc_forbidden_chars = set('`~!#$%^&*+/|{};:"\'\\<>?')
    remaining_desc_forbidden = set(fixed_desc) & desc_forbidden_chars
    if remaining_desc_forbidden:
        print(f"   ❌ 仍包含禁用字符: {remaining_desc_forbidden}")
        return False
    else:
        print(f"   ✅ 已清理所有禁用字符")
    
    print("\n3. 测试XML pool元素生成:")
    try:
        from lxml import etree
        
        # 创建测试NAT元素
        nat_elem = etree.Element("nat")
        
        # 模拟_add_nat_pool_to_xml方法的逻辑
        pool_data = {
            "name": problem_name,
            "address": {"value": "************-*************"},
            "desc": problem_desc
        }
        
        # 创建pool元素
        pool_elem = etree.SubElement(nat_elem, "pool")
        
        # 添加清理后的名称
        clean_name = clean_ntos_name(pool_data["name"], 64)
        name_elem = etree.SubElement(pool_elem, "name")
        name_elem.text = clean_name
        
        # 添加地址
        if "address" in pool_data and "value" in pool_data["address"]:
            address_elem = etree.SubElement(pool_elem, "address")
            value_elem = etree.SubElement(address_elem, "value")
            value_elem.text = pool_data["address"]["value"]
        
        # 添加清理后的描述
        if "desc" in pool_data and pool_data["desc"]:
            clean_desc = clean_ntos_description(pool_data["desc"], 255)
            if clean_desc:
                desc_elem = etree.SubElement(pool_elem, "desc")
                desc_elem.text = clean_desc
        
        # 验证生成的XML
        xml_str = etree.tostring(nat_elem, encoding='unicode', pretty_print=True)
        print("   生成的XML:")
        print("   " + xml_str.replace('\n', '\n   '))
        
        # 检查必需元素
        pools = nat_elem.xpath('.//pool')
        if not pools:
            print("   ❌ 没有生成pool元素")
            return False
        
        pool = pools[0]
        name_elem = pool.find('name')
        if name_elem is None or not name_elem.text:
            print("   ❌ pool缺少name元素")
            return False
        
        print(f"   ✅ pool元素生成成功，name: {name_elem.text}")
        return True
        
    except Exception as e:
        print(f"   ❌ XML生成测试失败: {e}")
        return False

def analyze_yang_errors():
    """分析YANG错误"""
    print("\n=== 分析原始YANG错误 ===")
    
    error_file = "output/yang_error_tmptqvqn0r6_20250802_130958.log"
    if not os.path.exists(error_file):
        print(f"错误文件不存在: {error_file}")
        return True
    
    try:
        with open(error_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("原始YANG错误:")
        lines = content.split('\n')
        error_lines = [line for line in lines if 'libyang err' in line]
        
        for i, line in enumerate(error_lines, 1):
            print(f"  {i}. {line.strip()}")
        
        print(f"\n总共发现 {len(error_lines)} 个YANG错误")
        
        # 分析错误类型
        name_key_errors = [line for line in error_lines if 'missing its key "name"' in line]
        char_constraint_errors = [line for line in error_lines if 'cannot include character' in line]
        
        print(f"  - 缺少name键错误: {len(name_key_errors)}")
        print(f"  - 字符约束错误: {len(char_constraint_errors)}")
        
        return True
        
    except Exception as e:
        print(f"分析YANG错误失败: {e}")
        return False

def main():
    """主函数"""
    print("开始验证YANG修复效果...")
    print("=" * 60)
    
    # 分析原始错误
    analyze_yang_errors()
    
    print("\n" + "=" * 60)
    
    # 测试修复效果
    success = test_specific_fixes()
    
    print("\n" + "=" * 60)
    print("修复验证总结:")
    
    if success:
        print("✅ 所有修复测试通过！")
        print("✅ 名称清理功能正常工作")
        print("✅ 描述清理功能正常工作") 
        print("✅ XML生成功能正常工作")
        print("\n建议：重新运行转换以验证YANG验证是否通过")
    else:
        print("❌ 部分修复测试失败")
        print("❌ 需要进一步检查和修复")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
