#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简单的YANG验证测试脚本
"""

import os
import sys
import re
from lxml import etree

def check_yang_issues(xml_file_path: str):
    """检查YANG模型验证问题"""
    
    print(f"🔍 检查XML文件: {xml_file_path}")
    
    try:
        # 解析XML文件
        tree = etree.parse(xml_file_path)
        root = tree.getroot()
        
        issues = []
        
        # 1. 检查必需的name键
        required_name_elements = ['pool', 'rule', 'service-set', 'policy']
        for element_tag in required_name_elements:
            elements = root.xpath(f".//{element_tag}")
            for i, element in enumerate(elements):
                name_elem = element.find('name')
                if name_elem is None:
                    issues.append(f"元素 {element_tag}[{i}] 缺少必需的name键")
                elif not name_elem.text or not name_elem.text.strip():
                    issues.append(f"元素 {element_tag}[{i}] 的name键为空")
        
        # 2. 检查名称字段中的非法字符
        name_elements = root.xpath('.//name')
        for i, name_elem in enumerate(name_elements):
            if name_elem.text:
                invalid_chars = re.findall(r'[`~!@#$%^&*+|{};:"\'\\/<>?,]', name_elem.text)
                if invalid_chars:
                    issues.append(f"名称[{i}] '{name_elem.text}' 包含非法字符: {', '.join(set(invalid_chars))}")
        
        # 3. 检查描述字段中的非法字符
        desc_elements = root.xpath('.//desc | .//description')
        for i, desc_elem in enumerate(desc_elements):
            if desc_elem.text:
                invalid_chars = re.findall(r'[`~!#$%^&*+|{};:"\'\\/<>?]', desc_elem.text)
                if invalid_chars:
                    issues.append(f"描述[{i}] 包含非法字符: {', '.join(set(invalid_chars))}")
        
        # 显示结果
        if issues:
            print(f"❌ 发现 {len(issues)} 个YANG验证问题:")
            for issue in issues[:20]:  # 只显示前20个问题
                print(f"   • {issue}")
            if len(issues) > 20:
                print(f"   ... 还有 {len(issues) - 20} 个问题")
            return False
        else:
            print("✅ 未发现YANG验证问题!")
            return True
            
    except Exception as e:
        print(f"❌ 检查过程中出错: {str(e)}")
        return False


def apply_simple_fixes(xml_file_path: str, output_path: str):
    """应用简单的修复"""
    
    print(f"🔧 应用简单修复: {xml_file_path} -> {output_path}")
    
    try:
        # 解析XML文件
        tree = etree.parse(xml_file_path)
        root = tree.getroot()
        
        fixes_applied = 0
        
        # 1. 修复缺失的name键
        required_name_elements = ['pool', 'rule', 'service-set', 'policy']
        for element_tag in required_name_elements:
            elements = root.xpath(f".//{element_tag}")
            for i, element in enumerate(elements):
                name_elem = element.find('name')
                if name_elem is None:
                    # 添加缺失的name键
                    name_elem = etree.Element('name')
                    name_elem.text = f"unnamed_{element_tag}_{i+1}"
                    element.insert(0, name_elem)
                    fixes_applied += 1
                    print(f"   ✅ 为 {element_tag}[{i}] 添加name键: {name_elem.text}")
                elif not name_elem.text or not name_elem.text.strip():
                    # 修复空的name键
                    name_elem.text = f"unnamed_{element_tag}_{i+1}"
                    fixes_applied += 1
                    print(f"   ✅ 修复 {element_tag}[{i}] 的空name键: {name_elem.text}")
        
        # 2. 清理名称字段中的非法字符
        name_elements = root.xpath('.//name')
        for name_elem in name_elements:
            if name_elem.text:
                original_name = name_elem.text
                # 移除非法字符
                clean_name = re.sub(r'[`~!@#$%^&*+|{};:"\'\\/<>?,]', '_', original_name)
                # 清理连续下划线
                clean_name = re.sub(r'_+', '_', clean_name).strip('_')
                # 确保不为空
                if not clean_name:
                    clean_name = "unnamed_object"
                # 限制长度
                if len(clean_name) > 64:
                    clean_name = clean_name[:64].rstrip('_')
                
                if original_name != clean_name:
                    name_elem.text = clean_name
                    fixes_applied += 1
                    print(f"   ✅ 清理名称: {original_name} -> {clean_name}")
        
        # 3. 清理描述字段中的非法字符
        desc_elements = root.xpath('.//desc | .//description')
        for desc_elem in desc_elements:
            if desc_elem.text:
                original_desc = desc_elem.text
                # 移除非法字符，但保留更多可读字符
                clean_desc = re.sub(r'[`~!#$%^&*+|{};:"\'\\/<>?]', ' ', original_desc)
                # 清理多余空格
                clean_desc = re.sub(r'\s+', ' ', clean_desc).strip()
                # 限制长度
                if len(clean_desc) > 255:
                    clean_desc = clean_desc[:255].rstrip()
                
                if original_desc != clean_desc:
                    desc_elem.text = clean_desc
                    fixes_applied += 1
                    print(f"   ✅ 清理描述: {original_desc[:30]}... -> {clean_desc[:30]}...")
        
        # 保存修复后的文件
        etree.indent(tree, space="  ")
        tree.write(output_path, encoding='utf-8', xml_declaration=True, pretty_print=True)
        
        print(f"✅ 修复完成! 应用了 {fixes_applied} 个修复")
        return True
        
    except Exception as e:
        print(f"❌ 修复过程中出错: {str(e)}")
        return False


def main():
    """主函数"""
    print("🚀 简单YANG验证测试")
    print("=" * 50)
    
    # 文件路径
    original_file = "output/fortigate-z5100s-R11_backup_20250802_020717.xml"
    fixed_file = "output/fortigate-z5100s-R11_backup_20250802_020717_fixed.xml"
    
    if not os.path.exists(original_file):
        print(f"❌ 文件不存在: {original_file}")
        return
    
    # 1. 检查原始文件
    print("\n1. 检查原始文件:")
    original_valid = check_yang_issues(original_file)
    
    # 2. 应用修复
    print("\n2. 应用修复:")
    fix_success = apply_simple_fixes(original_file, fixed_file)
    
    if not fix_success:
        print("❌ 修复失败")
        return
    
    # 3. 检查修复后的文件
    print("\n3. 检查修复后的文件:")
    fixed_valid = check_yang_issues(fixed_file)
    
    # 4. 总结
    print("\n" + "=" * 50)
    print("📋 测试结果:")
    print(f"   原始文件: {'✅ 通过' if original_valid else '❌ 失败'}")
    print(f"   修复后文件: {'✅ 通过' if fixed_valid else '❌ 失败'}")
    
    if not original_valid and fixed_valid:
        print("🎉 修复成功! YANG验证问题已解决!")
    elif not fixed_valid:
        print("⚠️ 仍有问题需要进一步修复")
    else:
        print("ℹ️ 测试完成")


if __name__ == "__main__":
    main()
