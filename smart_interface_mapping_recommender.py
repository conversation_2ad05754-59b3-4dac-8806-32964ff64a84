#!/usr/bin/env python3
"""
智能接口映射推荐算法
"""

import os
import json
import re
from typing import Dict, List, Set, Tuple
from collections import defaultdict

class SmartInterfaceMappingRecommender:
    def __init__(self):
        self.device_templates = {
            "FortiGate-100F": {
                "common_interfaces": ["wan1", "wan2", "dmz", "internal", "t1", "t2"],
                "port_pattern": r"port\d+",
                "special_interfaces": ["fortilink", "ha", "ha2"],
                "priority_mappings": {
                    "wan1": "Ge0/1",
                    "wan2": "Ge0/2", 
                    "dmz": "Ge0/3",
                    "internal": "Ge0/4",
                    "t1": "TenGe0/0",
                    "t2": "TenGe0/1"
                }
            },
            "FortiGate-401F": {
                "common_interfaces": ["wan1", "wan2", "dmz", "internal", "port1", "port2"],
                "port_pattern": r"port\d+",
                "special_interfaces": ["fortilink", "ha", "modem"],
                "priority_mappings": {
                    "wan1": "Ge0/1",
                    "wan2": "Ge0/2",
                    "dmz": "Ge0/3", 
                    "internal": "Ge0/4",
                    "port1": "Ge0/5",
                    "port2": "Ge0/6"
                }
            },
            "FortiGate-601E": {
                "common_interfaces": ["wan1", "wan2", "dmz", "internal"],
                "port_pattern": r"port\d+",
                "special_interfaces": ["fortilink", "ha", "modem", "s1", "s2", "vw1", "vw2"],
                "priority_mappings": {
                    "wan1": "Ge0/1",
                    "wan2": "Ge0/2",
                    "dmz": "Ge0/3",
                    "internal": "Ge0/4"
                }
            }
        }
        
        self.ntos_interface_pools = {
            "gigabit": [f"Ge0/{i}" for i in range(1, 24)],
            "tengigabit": [f"TenGe0/{i}" for i in range(0, 4)],
            "management": ["Ge0/0"]
        }
    
    def detect_device_model(self, config_file: str) -> str:
        """检测设备型号"""
        if "100F" in config_file:
            return "FortiGate-100F"
        elif "401F" in config_file:
            return "FortiGate-401F"
        elif "601E" in config_file:
            return "FortiGate-601E"
        else:
            return "Generic"
    
    def analyze_interface_usage_patterns(self, config_file: str) -> Dict:
        """分析接口使用模式"""
        print(f"🔍 分析接口使用模式: {config_file}")
        
        if not os.path.exists(config_file):
            return {}
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            patterns = {
                'wan_interfaces': set(),
                'lan_interfaces': set(),
                'dmz_interfaces': set(),
                'tunnel_interfaces': set(),
                'vlan_interfaces': set(),
                'high_priority': set(),
                'medium_priority': set(),
                'low_priority': set()
            }
            
            # 分析接口在不同配置段中的使用
            self._analyze_zone_usage(content, patterns)
            self._analyze_policy_usage(content, patterns)
            self._analyze_interface_config(content, patterns)
            
            # 根据使用模式分配优先级
            self._assign_interface_priorities(patterns)
            
            return patterns
            
        except Exception as e:
            print(f"❌ 分析失败: {str(e)}")
            return {}
    
    def _analyze_zone_usage(self, content: str, patterns: Dict):
        """分析区域配置中的接口使用"""
        in_zone_section = False
        current_zone = None
        
        for line in content.split('\n'):
            line = line.strip()
            
            if line == "config system zone":
                in_zone_section = True
                continue
            elif in_zone_section and line == "end":
                in_zone_section = False
                continue
            
            if in_zone_section:
                if line.startswith('edit "') and line.endswith('"'):
                    current_zone = line[6:-1].lower()
                elif line.startswith('set interface ') and current_zone:
                    interface_str = line[14:]
                    interfaces = self._parse_interface_list(interface_str)
                    
                    # 根据区域名称分类接口
                    if 'wan' in current_zone or 'external' in current_zone:
                        patterns['wan_interfaces'].update(interfaces)
                        patterns['high_priority'].update(interfaces)
                    elif 'lan' in current_zone or 'internal' in current_zone:
                        patterns['lan_interfaces'].update(interfaces)
                        patterns['medium_priority'].update(interfaces)
                    elif 'dmz' in current_zone:
                        patterns['dmz_interfaces'].update(interfaces)
                        patterns['medium_priority'].update(interfaces)
    
    def _analyze_policy_usage(self, content: str, patterns: Dict):
        """分析策略配置中的接口使用"""
        in_policy_section = False
        interface_usage_count = defaultdict(int)
        
        for line in content.split('\n'):
            line = line.strip()
            
            if line == "config firewall policy":
                in_policy_section = True
                continue
            elif in_policy_section and line == "end":
                in_policy_section = False
                continue
            
            if in_policy_section:
                if line.startswith('set srcintf ') or line.startswith('set dstintf '):
                    interface_str = line.split(' ', 2)[2]
                    interfaces = self._parse_interface_list(interface_str)
                    
                    for interface in interfaces:
                        interface_usage_count[interface] += 1
        
        # 根据使用频率分配优先级
        sorted_interfaces = sorted(interface_usage_count.items(), key=lambda x: x[1], reverse=True)
        
        for i, (interface, count) in enumerate(sorted_interfaces):
            if i < 5:  # 前5个高频接口
                patterns['high_priority'].add(interface)
            elif i < 15:  # 中频接口
                patterns['medium_priority'].add(interface)
            else:  # 低频接口
                patterns['low_priority'].add(interface)
    
    def _analyze_interface_config(self, content: str, patterns: Dict):
        """分析接口配置"""
        in_interface_section = False
        current_interface = None
        
        for line in content.split('\n'):
            line = line.strip()
            
            if line == "config system interface":
                in_interface_section = True
                continue
            elif in_interface_section and line == "end":
                in_interface_section = False
                continue
            
            if in_interface_section:
                if line.startswith('edit "') and line.endswith('"'):
                    current_interface = line[6:-1]
                    
                    # 识别VLAN接口
                    if '.' in current_interface or current_interface.startswith('vlan'):
                        patterns['vlan_interfaces'].add(current_interface)
                    
                    # 识别隧道接口
                    if current_interface.startswith(('tun', 'ipsec', 'ssl')):
                        patterns['tunnel_interfaces'].add(current_interface)
                
                elif line.startswith('set type ') and current_interface:
                    interface_type = line[9:]
                    if interface_type in ['tunnel', 'ipsec']:
                        patterns['tunnel_interfaces'].add(current_interface)
    
    def _assign_interface_priorities(self, patterns: Dict):
        """分配接口优先级"""
        # WAN接口通常是最高优先级
        patterns['high_priority'].update(patterns['wan_interfaces'])
        
        # DMZ和LAN接口是中等优先级
        patterns['medium_priority'].update(patterns['dmz_interfaces'])
        patterns['medium_priority'].update(patterns['lan_interfaces'])
        
        # VLAN和隧道接口是低优先级
        patterns['low_priority'].update(patterns['vlan_interfaces'])
        patterns['low_priority'].update(patterns['tunnel_interfaces'])
    
    def _parse_interface_list(self, interface_str: str) -> Set[str]:
        """解析接口列表"""
        interfaces = set()
        interface_str = interface_str.strip()
        
        if interface_str.startswith('"') and interface_str.endswith('"'):
            interface_str = interface_str[1:-1]
        
        for interface in interface_str.split():
            interface = interface.strip('"').strip("'")
            if interface:
                interfaces.add(interface)
        
        return interfaces
    
    def generate_smart_mapping(self, config_file: str, missing_interfaces: Set[str]) -> Dict[str, str]:
        """生成智能接口映射"""
        print(f"💡 生成智能接口映射: {config_file}")
        
        device_model = self.detect_device_model(config_file)
        usage_patterns = self.analyze_interface_usage_patterns(config_file)
        
        smart_mapping = {}
        used_ntos_interfaces = set()
        
        # 1. 处理设备模板中的优先级映射
        if device_model in self.device_templates:
            template = self.device_templates[device_model]
            priority_mappings = template["priority_mappings"]
            
            for fg_interface in missing_interfaces:
                if fg_interface in priority_mappings:
                    ntos_interface = priority_mappings[fg_interface]
                    if ntos_interface not in used_ntos_interfaces:
                        smart_mapping[fg_interface] = ntos_interface
                        used_ntos_interfaces.add(ntos_interface)
                        print(f"   🔥 模板映射: {fg_interface} -> {ntos_interface}")
        
        # 2. 处理高优先级接口
        remaining_interfaces = missing_interfaces - set(smart_mapping.keys())
        high_priority = remaining_interfaces & usage_patterns.get('high_priority', set())
        
        available_tengig = [iface for iface in self.ntos_interface_pools['tengigabit'] 
                           if iface not in used_ntos_interfaces]
        available_gig = [iface for iface in self.ntos_interface_pools['gigabit'] 
                        if iface not in used_ntos_interfaces]
        
        for interface in sorted(high_priority):
            if available_tengig:
                ntos_interface = available_tengig.pop(0)
                smart_mapping[interface] = ntos_interface
                used_ntos_interfaces.add(ntos_interface)
                print(f"   🔥 高优先级(万兆): {interface} -> {ntos_interface}")
            elif available_gig:
                ntos_interface = available_gig.pop(0)
                smart_mapping[interface] = ntos_interface
                used_ntos_interfaces.add(ntos_interface)
                print(f"   🔥 高优先级(千兆): {interface} -> {ntos_interface}")
        
        # 3. 处理中等优先级接口
        remaining_interfaces = remaining_interfaces - set(smart_mapping.keys())
        medium_priority = remaining_interfaces & usage_patterns.get('medium_priority', set())
        
        for interface in sorted(medium_priority):
            if available_gig:
                ntos_interface = available_gig.pop(0)
                smart_mapping[interface] = ntos_interface
                used_ntos_interfaces.add(ntos_interface)
                print(f"   ⚡ 中优先级: {interface} -> {ntos_interface}")
        
        # 4. 处理剩余接口
        remaining_interfaces = remaining_interfaces - set(smart_mapping.keys())
        
        for interface in sorted(remaining_interfaces):
            if available_gig:
                ntos_interface = available_gig.pop(0)
                smart_mapping[interface] = ntos_interface
                used_ntos_interfaces.add(ntos_interface)
                print(f"   🔄 自动分配: {interface} -> {ntos_interface}")
        
        return smart_mapping
    
    def update_mapping_file(self, config_file: str, smart_mapping: Dict[str, str]) -> bool:
        """更新接口映射文件"""
        base_name = os.path.splitext(config_file)[0]
        mapping_file = f"mappings/interface_mapping_{base_name}.json"
        
        if not os.path.exists(mapping_file):
            print(f"❌ 映射文件不存在: {mapping_file}")
            return False
        
        try:
            # 加载现有映射
            with open(mapping_file, 'r', encoding='utf-8') as f:
                existing_mapping = json.load(f)
            
            # 合并智能映射
            updated_mapping = existing_mapping.copy()
            updated_mapping.update(smart_mapping)
            
            # 保存更新后的映射
            with open(mapping_file, 'w', encoding='utf-8') as f:
                json.dump(updated_mapping, f, indent=2, ensure_ascii=False)
            
            print(f"✅ 映射文件已更新: {mapping_file}")
            print(f"   添加映射: {len(smart_mapping)}个")
            print(f"   总映射数: {len(updated_mapping)}个")
            
            return True
            
        except Exception as e:
            print(f"❌ 更新映射文件失败: {str(e)}")
            return False
    
    def optimize_all_mappings(self):
        """优化所有配置文件的接口映射"""
        print("🚀 开始智能接口映射优化")
        print("=" * 80)
        
        config_files = [
            "FortiGate-100F_7-6_3510_202505161613.conf",
            "FortiGate-401F_7-4_2795_202507011110.conf", 
            "FortiGate-601E_7-4_2795_202506101906.conf"
        ]
        
        optimization_results = {}
        
        for config_file in config_files:
            print(f"\n{'='*60}")
            print(f"优化配置文件: {config_file}")
            print(f"{'='*60}")
            
            # 从之前的分析结果中获取缺失的接口
            missing_interfaces = self._get_missing_interfaces(config_file)
            
            if not missing_interfaces:
                print(f"✅ 无需优化，接口映射已完整")
                optimization_results[config_file] = {"status": "complete", "added_mappings": 0}
                continue
            
            # 生成智能映射
            smart_mapping = self.generate_smart_mapping(config_file, missing_interfaces)
            
            if smart_mapping:
                # 更新映射文件
                success = self.update_mapping_file(config_file, smart_mapping)
                optimization_results[config_file] = {
                    "status": "optimized" if success else "failed",
                    "added_mappings": len(smart_mapping),
                    "smart_mapping": smart_mapping
                }
            else:
                optimization_results[config_file] = {"status": "no_recommendations", "added_mappings": 0}
        
        return optimization_results
    
    def _get_missing_interfaces(self, config_file: str) -> Set[str]:
        """获取缺失的接口（从之前的分析结果）"""
        missing_interfaces_map = {
            "FortiGate-100F_7-6_3510_202505161613.conf": {"t1", "t2", "wan1"},
            "FortiGate-401F_7-4_2795_202507011110.conf": {"tct-vlan103", "tct-zone-1"},
            "FortiGate-601E_7-4_2795_202506101906.conf": set()
        }
        
        return missing_interfaces_map.get(config_file, set())
    
    def generate_optimization_report(self, results: Dict):
        """生成优化报告"""
        print(f"\n{'='*80}")
        print("📊 智能接口映射优化报告")
        print(f"{'='*80}")
        
        total_configs = len(results)
        optimized_configs = sum(1 for r in results.values() if r["status"] == "optimized")
        total_added_mappings = sum(r["added_mappings"] for r in results.values())
        
        print(f"📋 优化统计:")
        print(f"   处理配置文件: {total_configs}个")
        print(f"   成功优化: {optimized_configs}个")
        print(f"   新增映射: {total_added_mappings}个")
        
        print(f"\n📋 详细结果:")
        for config_file, result in results.items():
            status_icon = "✅" if result["status"] == "optimized" else "⚠️" if result["status"] == "complete" else "❌"
            print(f"   {status_icon} {config_file}:")
            print(f"      状态: {result['status']}")
            print(f"      新增映射: {result['added_mappings']}个")
            
            if "smart_mapping" in result:
                for fg_interface, ntos_interface in result["smart_mapping"].items():
                    print(f"         {fg_interface} -> {ntos_interface}")
        
        print(f"\n🎯 优化效果:")
        if optimized_configs == total_configs:
            print("🎉 所有配置文件映射优化完成")
        elif optimized_configs > 0:
            print(f"✅ {optimized_configs}/{total_configs} 配置文件优化成功")
        else:
            print("❌ 没有配置文件被优化")

def main():
    """主函数"""
    print("🚀 智能接口映射推荐算法")
    print("=" * 80)
    
    recommender = SmartInterfaceMappingRecommender()
    
    # 执行智能映射优化
    results = recommender.optimize_all_mappings()
    
    # 生成优化报告
    recommender.generate_optimization_report(results)
    
    print(f"\n{'='*80}")
    print("🎉 智能接口映射优化完成")
    print(f"{'='*80}")

if __name__ == "__main__":
    main()
