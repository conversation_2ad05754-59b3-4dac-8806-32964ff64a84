#!/bin/bash
set -e

# 添加明显的开始标记
echo "=======================================" 
echo "===== NTOS ConfigTrans 配置转换服务 开始执行 $(date) ====="
echo "======================================="

# 添加调试输出
log() {
    echo "[$(date +"%Y-%m-%d %H:%M:%S")] $*"
}

# 添加敏感信息脱敏函数
mask_sensitive() {
    local value="$1"
    local masked=""
    
    if [ -z "$value" ]; then
        echo ""
    elif [ ${#value} -le 4 ]; then
        echo "****"
    else
        # 保留前2个字符和后2个字符，中间用星号代替
        local prefix="${value:0:2}"
        local suffix="${value: -2}"
        local stars=$(printf '%*s' $((${#value} - 4)) | tr ' ' '*')
        echo "${prefix}${stars}${suffix}"
    fi
}

log "启动脚本开始执行..."

# 设置健康检查日志级别
export HEALTHCHECK_LOGLEVEL="${HEALTHCHECK_LOGLEVEL:-INFO}"
log "健康检查日志级别: $HEALTHCHECK_LOGLEVEL"

# 定义配置文件路径
CONFIG_FILE=${CONFIG_FILE:-/app/application.yml}
TEMPLATE_CONFIG_FILE=${TEMPLATE_CONFIG_FILE:-/app/docker-application.yml.template}
DEFAULT_CONFIG_FILE=${DEFAULT_CONFIG_FILE:-/app/docker-application.yml}
USE_DEFAULT_CONFIG=${USE_DEFAULT_CONFIG:-false}

# 确保目录存在
log "检查必要目录..."
for dir in "/app/data/uploads" "/app/data/temp" "/app/data/output" "/app/data/mappings" "/app/logs"; do
    if [ ! -d "$dir" ]; then
        log "创建目录: $dir"
        mkdir -p "$dir"
    fi
done

# 如果使用默认配置，直接使用默认配置文件
if [ "$USE_DEFAULT_CONFIG" = "true" ]; then
    log "使用静态配置文件，不进行环境变量替换"
    
    # 检查默认配置文件是否存在
    if [ ! -f "$DEFAULT_CONFIG_FILE" ]; then
        log "错误: 默认配置文件不存在: $DEFAULT_CONFIG_FILE"
        exit 1
    fi
    
    # 复制默认配置文件到应用配置文件位置
    log "正在复制默认配置文件 $DEFAULT_CONFIG_FILE 到 $CONFIG_FILE..."
    cp "$DEFAULT_CONFIG_FILE" "$CONFIG_FILE"
    if [ $? -ne 0 ]; then
        log "错误: 复制默认配置文件失败"
        exit 1
    fi
    log "已使用静态配置文件: $DEFAULT_CONFIG_FILE"
else
    # 备份原始配置文件作为模板（如果没有模板文件）
    if [ ! -f "$TEMPLATE_CONFIG_FILE" ]; then
        log "警告: 模板文件不存在: $TEMPLATE_CONFIG_FILE"
        if [ -f "$CONFIG_FILE" ]; then
            log "使用现有配置文件作为模板..."
            cp "$CONFIG_FILE" "$TEMPLATE_CONFIG_FILE"
        else
            log "错误: 模板文件和配置文件都不存在!"
            exit 1
        fi
    else
        log "使用模板文件: $TEMPLATE_CONFIG_FILE"
    fi

    # 设置所有必要的环境变量
    log "设置必要的环境变量..."
    export DEBUG="${DEBUG:-false}"
    export LOGLEVEL="${LOGLEVEL:-info}"
    export HOST="${HOST:-0.0.0.0}"
    export PORT="${PORT:-9005}"
    export NGINX_HOST="${NGINX_HOST:-localhost}"
    export NGINX_PORT="${NGINX_PORT:-9527}"
    export NGINX_PATH="${NGINX_PATH:-/api}"
    export NGINX_MAX_BODY_SIZE="${NGINX_MAX_BODY_SIZE:-50}"
    export READ_TIMEOUT="${READ_TIMEOUT:-60}"
    export WRITE_TIMEOUT="${WRITE_TIMEOUT:-60}"
    export MAX_SIZE="${MAX_SIZE:-10240}"
    export PPROF_ENABLED="${PPROF_ENABLED:-true}"
    export CASBIN_PREFIX="${CASBIN_PREFIX:-}"
    export CASBIN_PATH="${CASBIN_PATH:-/app/rbac_model.conf}"
    export DB_ADAPTER="${DB_ADAPTER:-mysql}"
    
    # 数据库细化配置参数
    export DB_USER="${DB_USER:-root}"
    export DB_PASSWORD="${DB_PASSWORD:-abc.123}"
    export DB_HOST="${DB_HOST:-127.0.0.1}"
    export DB_PORT="${DB_PORT:-3306}"
    export DB_NAME="${DB_NAME:-test}"
    export DB_PARSE_TIME="${DB_PARSE_TIME:-True}"
    export DB_LOC="${DB_LOC:-Local}"
    export DB_EXTRA_PARAMS="${DB_EXTRA_PARAMS:-}"
    
    # 构建数据库连接字符串 - 不输出到日志
    DB_CONN_STR="${DB_USER}:${DB_PASSWORD}@tcp(${DB_HOST}:${DB_PORT})/${DB_NAME}?parseTime=${DB_PARSE_TIME}&loc=${DB_LOC}"
    
    # 如果有额外参数，添加到连接字符串
    if [ -n "$DB_EXTRA_PARAMS" ]; then
        DB_CONN_STR="${DB_CONN_STR}&${DB_EXTRA_PARAMS}"
    fi
    
    export DB_CONN="${DB_CONN:-$DB_CONN_STR}"
    # 打印脱敏后的连接串
    MASKED_DB_CONN=$(echo "${DB_CONN}" | sed -E "s/([^:]+):([^@]+)@/\1:$(mask_sensitive "$DB_PASSWORD")@/g")
    
    export DB_MAX_IDLE_CONNS="${DB_MAX_IDLE_CONNS:-10}"
    export DB_MAX_OPEN_CONNS="${DB_MAX_OPEN_CONNS:-100}"
    export DB_CONN_MAX_LIFETIME="${DB_CONN_MAX_LIFETIME:-3600}"
    export DB_PREFIX="${DB_PREFIX:-}"
    export DB_ENABLED="${DB_ENABLED:-false}"
    export DB_DEBUG="${DB_DEBUG:-false}"
    export DB_LOG_LEVEL="${DB_LOG_LEVEL:-info}"
    export DB_SLOW_THRESHOLD="${DB_SLOW_THRESHOLD:-200}"
    export REDIS_HOST="${REDIS_HOST:-127.0.0.1}"
    export REDIS_PORT="${REDIS_PORT:-6379}"
    export REDIS_PASSWORD="${REDIS_PASSWORD:-}"
    export REDIS_DATABASE="${REDIS_DATABASE:-0}"
    export REDIS_DIAL_TIMEOUT="${REDIS_DIAL_TIMEOUT:-10}"
    export REDIS_READ_TIMEOUT="${REDIS_READ_TIMEOUT:-6}"
    export REDIS_WRITE_TIMEOUT="${REDIS_WRITE_TIMEOUT:-6}"
    export REDIS_POOL_SIZE="${REDIS_POOL_SIZE:-10}"
    export REDIS_MIN_IDLE_CONNS="${REDIS_MIN_IDLE_CONNS:-5}"
    export REDIS_MAX_RETRIES="${REDIS_MAX_RETRIES:-3}"
    export REDIS_RETRY_TIMEOUT="${REDIS_RETRY_TIMEOUT:-500}"
    export CACHE_DRIVER="${CACHE_DRIVER:-redis}"
    export FILESTORAGE_TEMP="${FILESTORAGE_TEMP:-/app/data/temp/}"
    export FILESTORAGE_UPLOAD="${FILESTORAGE_UPLOAD:-/app/data/uploads/}"
    export CONFIGTRANS_UPLOAD="${CONFIGTRANS_UPLOAD:-/app/data/uploads/}"
    export CONFIGTRANS_TEMPDIR="${CONFIGTRANS_TEMPDIR:-/app/data/temp/}"
    export CONFIGTRANS_PYTHONPATH="${CONFIGTRANS_PYTHONPATH:-python}"
    export CONFIGTRANS_ENGINEPATH="${CONFIGTRANS_ENGINEPATH:-/app/engine}"
    export CONFIGTRANS_MAPPINGBASEDIR="${CONFIGTRANS_MAPPINGBASEDIR:-/app/data/mappings/}"
    export CONFIGTRANS_FORTIGATE_MAPPINGFILE="${CONFIGTRANS_FORTIGATE_MAPPINGFILE:-interface_mapping.json}"
    export CONFIGTRANS_FORTIGATE_VERIFY="${CONFIGTRANS_FORTIGATE_VERIFY:---mode verify --cli {file}}"
    export CONFIGTRANS_FORTIGATE_EXTRACT="${CONFIGTRANS_FORTIGATE_EXTRACT:---mode extract --cli {file} --output-json {json}}"
    export CONFIGTRANS_FORTIGATE_CONVERT="${CONFIGTRANS_FORTIGATE_CONVERT:---mode convert --cli {file} --mapping {mapping} --model {model} --version {version} --output {output} --encrypt-output {encrypt}}"

    # 显示重要环境变量（避免敏感信息）
    # log "环境变量设置完成，关键变量:"
    # log "DEBUG=$DEBUG"
    # log "PORT=$PORT"
    # log "DB_ENABLED=$DB_ENABLED"
    # log "PPROF_ENABLED=$PPROF_ENABLED"
    # 避免显示敏感信息，只显示连接信息
    # log "数据库配置: 用户=$(mask_sensitive "$DB_USER"), 主机=$DB_HOST, 端口=$DB_PORT, 数据库=$DB_NAME"
    # if [ -n "$REDIS_PASSWORD" ]; then
    #     log "Redis配置: 主机=$REDIS_HOST, 端口=$REDIS_PORT, 密码=********, 数据库=$REDIS_DATABASE"
    # else
    #     log "Redis配置: 主机=$REDIS_HOST, 端口=$REDIS_PORT, 无密码, 数据库=$REDIS_DATABASE"
    # fi

    # 生成配置文件
    log "从模板生成配置文件..."
    # 确保envsubst命令存在
    if ! command -v envsubst &> /dev/null; then
        log "错误: envsubst命令不存在，请安装gettext包"
        exit 1
    fi
    
    envsubst < "$TEMPLATE_CONFIG_FILE" > "$CONFIG_FILE"
    if [ $? -ne 0 ]; then
        log "错误: 替换环境变量失败"
        exit 1
    fi

    # 验证配置文件
    log "验证配置文件..."
    if grep -q '\${.*}' "$CONFIG_FILE"; then
        log "警告: 配置文件中仍存在未替换的环境变量:"
        grep '\${.*}' "$CONFIG_FILE" | grep -v -E 'password|secret|key|token|credential|conn'
    else
        log "配置文件环境变量替换完成"
    fi
fi

# 检查重要文件是否存在
if [ ! -f "$CONFIG_FILE" ]; then
    log "错误: 配置文件不存在: $CONFIG_FILE"
    exit 1
fi

if [ ! -f "/app/configtrans-service" ]; then
    log "错误: 可执行文件不存在: /app/configtrans-service"
    exit 1
fi

# 添加信号处理
trap_handler() {
    log "收到信号 $1，正在优雅停止服务..."
    # 将信号传递给子进程
    if [ -n "$CHILD_PID" ]; then
        kill -$1 $CHILD_PID 2>/dev/null || true
    fi
    wait $CHILD_PID 2>/dev/null || true
    log "服务已停止"
    exit 0
}

# 捕获常见信号
trap 'trap_handler TERM' TERM
trap 'trap_handler INT' INT
trap 'trap_handler QUIT' QUIT

# 执行主应用程序
log "启动应用程序: $*"

# 检查参数
if [ $# -eq 0 ]; then
    log "错误: 未提供应用程序路径"
    exit 1
fi

# 检查应用程序可执行性
if [ ! -x "$1" ]; then
    log "警告: 应用程序可能没有执行权限，尝试添加..."
    chmod +x "$1" || log "无法添加执行权限，但仍将尝试运行"
fi

# 运行应用程序
set +e  # 关闭错误终止，以便捕获退出码
log "运行命令: $@"
"$@" &
CHILD_PID=$!
log "应用程序进程ID: $CHILD_PID"

# 等待子进程结束
wait $CHILD_PID
EXIT_CODE=$?

log "应用程序退出，退出码: $EXIT_CODE"

# 如果是非正常退出，返回相应的退出码
if [ $EXIT_CODE -ne 0 ]; then
    log "应用程序非正常退出，将重启"
    exit $EXIT_CODE
fi

# 添加明显的结束标记
echo "======================================="
echo "===== NTOS ConfigTrans 配置转换服务 执行结束 $(date) ====="
echo "======================================="

# 如果正常退出，也返回0
exit 0 