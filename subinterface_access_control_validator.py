#!/usr/bin/env python3
"""
子接口access-control模块专项验证器
"""

import os
import json
from lxml import etree
from collections import defaultdict

class SubinterfaceAccessControlValidator:
    def __init__(self):
        self.fortigate_config = "Pass-Mask-MTU-FW-1_7-2_1639_202506271000.conf"
        self.ntos_xml = "output/fortigate-z5100s-R11-access-control-fixed.xml"
        self.interface_mapping = "mappings/interface_mapping_correct.json"
        
    def analyze_fortigate_subinterfaces(self):
        """分析FortiGate配置中的子接口"""
        print("🔍 分析FortiGate配置中的子接口")
        print("=" * 60)
        
        if not os.path.exists(self.fortigate_config):
            print(f"❌ FortiGate配置文件不存在: {self.fortigate_config}")
            return None
        
        subinterfaces = {}
        physical_interfaces = {}
        current_interface = None
        
        try:
            with open(self.fortigate_config, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            in_interface_section = False
            
            for line in lines:
                line = line.strip()
                
                if line == "config system interface":
                    in_interface_section = True
                    continue
                
                if in_interface_section and line == "end":
                    in_interface_section = False
                    continue
                
                if in_interface_section:
                    if line.startswith('edit "') and line.endswith('"'):
                        current_interface = line[6:-1]
                        interface_config = {
                            'name': current_interface,
                            'allowaccess': [],
                            'vlanid': None,
                            'interface': None,
                            'ip': None,
                            'type': None
                        }
                        
                        # 判断是否为子接口
                        if '.' in current_interface or current_interface.startswith('vlan'):
                            subinterfaces[current_interface] = interface_config
                        else:
                            physical_interfaces[current_interface] = interface_config
                    
                    elif line.startswith('set allowaccess ') and current_interface:
                        access_list = line[16:].split()
                        if current_interface in subinterfaces:
                            subinterfaces[current_interface]['allowaccess'] = access_list
                        elif current_interface in physical_interfaces:
                            physical_interfaces[current_interface]['allowaccess'] = access_list
                    
                    elif line.startswith('set vlanid ') and current_interface:
                        vlanid = line[11:]
                        if current_interface in subinterfaces:
                            subinterfaces[current_interface]['vlanid'] = vlanid
                        elif current_interface in physical_interfaces:
                            physical_interfaces[current_interface]['vlanid'] = vlanid
                    
                    elif line.startswith('set interface ') and current_interface:
                        parent_interface = line[14:].strip('"')
                        if current_interface in subinterfaces:
                            subinterfaces[current_interface]['interface'] = parent_interface
                    
                    elif line.startswith('set ip ') and current_interface:
                        ip = line[7:]
                        if current_interface in subinterfaces:
                            subinterfaces[current_interface]['ip'] = ip
                        elif current_interface in physical_interfaces:
                            physical_interfaces[current_interface]['ip'] = ip
                    
                    elif line.startswith('set type ') and current_interface:
                        interface_type = line[9:]
                        if current_interface in subinterfaces:
                            subinterfaces[current_interface]['type'] = interface_type
                        elif current_interface in physical_interfaces:
                            physical_interfaces[current_interface]['type'] = interface_type
                    
                    elif line == "next":
                        current_interface = None
            
            print(f"✅ 分析完成")
            print(f"   物理接口数: {len(physical_interfaces)}")
            print(f"   子接口数: {len(subinterfaces)}")
            
            # 统计子接口的allowaccess配置
            subinterfaces_with_access = {name: config for name, config in subinterfaces.items() 
                                       if config['allowaccess']}
            
            print(f"   有allowaccess的子接口数: {len(subinterfaces_with_access)}")
            
            if subinterfaces_with_access:
                print(f"   子接口allowaccess详情:")
                for name, config in subinterfaces_with_access.items():
                    parent = config.get('interface', 'unknown')
                    vlanid = config.get('vlanid', 'unknown')
                    access = config['allowaccess']
                    print(f"      {name}: parent={parent}, vlan={vlanid}, access={access}")
            
            return {
                'physical_interfaces': physical_interfaces,
                'subinterfaces': subinterfaces,
                'subinterfaces_with_access': subinterfaces_with_access
            }
            
        except Exception as e:
            print(f"❌ 分析FortiGate配置失败: {str(e)}")
            return None
    
    def analyze_ntos_subinterfaces(self):
        """分析NTOS XML中的子接口access-control配置"""
        print("\n🔍 分析NTOS XML中的子接口access-control配置")
        print("=" * 60)
        
        if not os.path.exists(self.ntos_xml):
            print(f"❌ NTOS XML文件不存在: {self.ntos_xml}")
            return None
        
        try:
            tree = etree.parse(self.ntos_xml)
            root = tree.getroot()
            
            physical_interfaces = {}
            vlan_interfaces = {}
            
            # 查找所有physical和vlan接口
            for elem in root.iter():
                tag = elem.tag.split('}')[-1] if '}' in elem.tag else elem.tag
                
                if tag == 'physical':
                    interface_data = self._extract_interface_data(elem)
                    if interface_data:
                        physical_interfaces[interface_data['name']] = interface_data
                
                elif tag == 'vlan':
                    interface_data = self._extract_interface_data(elem)
                    if interface_data:
                        vlan_interfaces[interface_data['name']] = interface_data
            
            print(f"✅ 分析完成")
            print(f"   物理接口数: {len(physical_interfaces)}")
            print(f"   VLAN子接口数: {len(vlan_interfaces)}")
            
            # 统计有access-control的接口
            physical_with_access = {name: data for name, data in physical_interfaces.items() 
                                  if data.get('access_control')}
            vlan_with_access = {name: data for name, data in vlan_interfaces.items() 
                              if data.get('access_control')}
            
            print(f"   有access-control的物理接口数: {len(physical_with_access)}")
            print(f"   有access-control的VLAN接口数: {len(vlan_with_access)}")
            
            if vlan_with_access:
                print(f"   VLAN接口access-control详情:")
                for name, data in list(vlan_with_access.items())[:5]:  # 只显示前5个
                    access_control = data['access_control']
                    print(f"      {name}: {access_control}")
                if len(vlan_with_access) > 5:
                    print(f"      ... 还有 {len(vlan_with_access) - 5} 个")
            
            return {
                'physical_interfaces': physical_interfaces,
                'vlan_interfaces': vlan_interfaces,
                'physical_with_access': physical_with_access,
                'vlan_with_access': vlan_with_access
            }
            
        except Exception as e:
            print(f"❌ 分析NTOS XML失败: {str(e)}")
            return None
    
    def _extract_interface_data(self, interface_elem):
        """提取接口数据"""
        interface_data = {
            'name': None,
            'access_control': None,
            'ip': None,
            'vlanid': None,
            'enabled': None
        }
        
        # 提取接口名称
        for child in interface_elem:
            child_tag = child.tag.split('}')[-1] if '}' in child.tag else child.tag
            
            if child_tag == 'name':
                interface_data['name'] = child.text
            elif child_tag == 'access-control':
                access_control = {}
                for service_elem in child:
                    service_tag = service_elem.tag.split('}')[-1] if '}' in service_elem.tag else service_elem.tag
                    access_control[service_tag] = service_elem.text == 'true'
                interface_data['access_control'] = access_control
            elif child_tag == 'vlanid':
                interface_data['vlanid'] = child.text
            elif child_tag == 'enabled':
                interface_data['enabled'] = child.text == 'true'
            elif child_tag == 'ipv4':
                # 提取IPv4地址
                for ipv4_child in child:
                    ipv4_child_tag = ipv4_child.tag.split('}')[-1] if '}' in ipv4_child.tag else ipv4_child.tag
                    if ipv4_child_tag == 'address':
                        for addr_child in ipv4_child:
                            addr_child_tag = addr_child.tag.split('}')[-1] if '}' in addr_child.tag else addr_child.tag
                            if addr_child_tag == 'ip':
                                interface_data['ip'] = addr_child.text
        
        return interface_data if interface_data['name'] else None
    
    def verify_subinterface_inheritance(self, fortigate_data, ntos_data):
        """验证子接口access-control继承"""
        print("\n🔍 验证子接口access-control继承")
        print("=" * 60)
        
        if not all([fortigate_data, ntos_data]):
            print("❌ 缺少必要数据")
            return
        
        # 加载接口映射
        interface_mapping = self._load_interface_mapping()
        if not interface_mapping:
            return
        
        # 创建反向映射
        reverse_mapping = {ntos: fg for fg, ntos in interface_mapping.items()}
        
        inheritance_results = []
        
        # 检查NTOS VLAN接口的access-control配置
        for vlan_name, vlan_data in ntos_data['vlan_interfaces'].items():
            if not vlan_data.get('access_control'):
                continue
            
            # 分析VLAN接口名称，提取父接口
            parent_interface = self._extract_parent_interface(vlan_name)
            if not parent_interface:
                continue
            
            # 查找对应的FortiGate父接口
            fg_parent = reverse_mapping.get(parent_interface)
            if not fg_parent:
                continue
            
            # 检查FortiGate父接口的allowaccess配置
            fg_parent_config = fortigate_data['physical_interfaces'].get(fg_parent, {})
            fg_allowaccess = fg_parent_config.get('allowaccess', [])
            
            # 验证继承是否正确
            expected_access_control = self._map_allowaccess_to_ntos(fg_allowaccess)
            actual_access_control = vlan_data['access_control']
            
            inheritance_correct = self._compare_access_control(expected_access_control, actual_access_control)
            
            inheritance_results.append({
                'vlan_interface': vlan_name,
                'parent_interface': parent_interface,
                'fg_parent': fg_parent,
                'fg_allowaccess': fg_allowaccess,
                'expected': expected_access_control,
                'actual': actual_access_control,
                'correct': inheritance_correct
            })
        
        # 统计结果
        total_checked = len(inheritance_results)
        correct_inheritance = sum(1 for r in inheritance_results if r['correct'])
        
        print(f"📊 继承验证结果:")
        print(f"   检查的VLAN接口数: {total_checked}")
        print(f"   正确继承的接口数: {correct_inheritance}")
        print(f"   继承成功率: {(correct_inheritance/total_checked*100):.1f}%" if total_checked > 0 else "   继承成功率: N/A")
        
        # 显示详细结果
        if inheritance_results:
            print(f"\n📋 详细继承验证:")
            for result in inheritance_results[:5]:  # 只显示前5个
                status = "✅" if result['correct'] else "❌"
                print(f"   {status} {result['vlan_interface']} <- {result['fg_parent']}")
                print(f"      FortiGate: {result['fg_allowaccess']}")
                print(f"      期望: {result['expected']}")
                print(f"      实际: {result['actual']}")
        
        return inheritance_results
    
    def _load_interface_mapping(self):
        """加载接口映射文件"""
        try:
            with open(self.interface_mapping, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ 加载接口映射失败: {str(e)}")
            return None
    
    def _extract_parent_interface(self, vlan_interface_name):
        """从VLAN接口名称提取父接口"""
        # 例如: Ge0/2.1010 -> Ge0/2
        if '.' in vlan_interface_name:
            return vlan_interface_name.split('.')[0]
        return None
    
    def _map_allowaccess_to_ntos(self, allowaccess_list):
        """将FortiGate allowaccess映射到NTOS access-control"""
        service_mapping = {
            'ping': 'ping',
            'https': 'https',
            'http': 'https',
            'ssh': 'ssh'
        }
        
        access_control = {'https': False, 'ping': False, 'ssh': False}
        
        for service in allowaccess_list:
            if service in service_mapping:
                ntos_service = service_mapping[service]
                access_control[ntos_service] = True
        
        return access_control
    
    def _compare_access_control(self, expected, actual):
        """比较access-control配置是否一致"""
        for service in ['https', 'ping', 'ssh']:
            if expected.get(service, False) != actual.get(service, False):
                return False
        return True

def main():
    """主函数"""
    print("🚀 开始子接口access-control模块专项验证")
    print("=" * 80)
    
    validator = SubinterfaceAccessControlValidator()
    
    # 1. 分析FortiGate子接口配置
    fortigate_data = validator.analyze_fortigate_subinterfaces()
    
    # 2. 分析NTOS子接口配置
    ntos_data = validator.analyze_ntos_subinterfaces()
    
    # 3. 验证子接口继承
    if fortigate_data and ntos_data:
        inheritance_results = validator.verify_subinterface_inheritance(fortigate_data, ntos_data)
    
    print(f"\n{'='*80}")
    print("📊 子接口access-control验证总结:")
    print(f"{'='*80}")
    
    if fortigate_data and ntos_data:
        fg_subinterfaces = len(fortigate_data['subinterfaces'])
        fg_with_access = len(fortigate_data['subinterfaces_with_access'])
        ntos_vlan = len(ntos_data['vlan_interfaces'])
        ntos_with_access = len(ntos_data['vlan_with_access'])
        
        print(f"✅ FortiGate子接口分析: 成功")
        print(f"   子接口总数: {fg_subinterfaces}")
        print(f"   有allowaccess的子接口: {fg_with_access}")
        
        print(f"✅ NTOS VLAN接口分析: 成功")
        print(f"   VLAN接口总数: {ntos_vlan}")
        print(f"   有access-control的VLAN接口: {ntos_with_access}")
        
        if inheritance_results:
            correct_count = sum(1 for r in inheritance_results if r['correct'])
            total_count = len(inheritance_results)
            success_rate = (correct_count / total_count * 100) if total_count > 0 else 0
            
            print(f"✅ 继承验证: 完成")
            print(f"   验证接口数: {total_count}")
            print(f"   继承成功率: {success_rate:.1f}%")
            
            if success_rate >= 90:
                print("🎉 子接口access-control继承质量: 优秀")
            elif success_rate >= 70:
                print("✅ 子接口access-control继承质量: 良好")
            elif success_rate >= 50:
                print("⚠️ 子接口access-control继承质量: 一般")
            else:
                print("❌ 子接口access-control继承质量: 需要改进")
    else:
        print("❌ 验证失败，无法完成分析")

if __name__ == "__main__":
    main()
