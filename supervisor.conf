[supervisord]
nodaemon=true
logfile=/dev/stdout
logfile_maxbytes=0
pidfile=/var/run/supervisord.pid
loglevel=info
user=root

[program:configtrans]
command=/app/start.sh /app/configtrans-service
directory=/app
autostart=true
autorestart=true
startsecs=10
startretries=5
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
# 如果进程退出，等待5秒后再重启
stopwaitsecs=5
# 当进程意外退出时，尝试重启它
stopsignal=TERM
# 优雅停止超时时间
stopasgroup=true
killasgroup=true
# 环境变量
environment=PYTHONPATH="/app",CONFIG_FILE="/app/application.yml",LD_LIBRARY_PATH="/usr/lib:/usr/local/lib:/usr/local/lib",PYTHONUNBUFFERED="1"

[program:monitor]
command=/bin/bash -c "while true; do sleep 300; /app/healthcheck.sh >> /app/logs/healthcheck.log 2>&1; done"
autostart=true
autorestart=true
startsecs=0
startretries=0
stdout_logfile=/app/logs/monitor-stdout.log
stderr_logfile=/app/logs/monitor-stderr.log

[eventlistener:process_monitor]
command=bash -c "echo READY; while read line; do echo PROCESSING; if /app/healthcheck.sh >> /app/logs/healthcheck.log 2>&1; then echo OK; else supervisorctl restart configtrans; fi; echo READY; done"
events=PROCESS_STATE_EXITED,PROCESS_STATE_FATAL
stderr_logfile=/app/logs/process-monitor-stderr.log
stdout_logfile=/app/logs/process-monitor-stdout.log
autostart=true
autorestart=true

[unix_http_server]
file=/var/run/supervisor.sock
chmod=0700

[supervisorctl]
serverurl=unix:///var/run/supervisor.sock

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface 