#!/usr/bin/env python3
"""
系统性问题排查器 - 检查XML模板集成阶段的方法调用参数错误
"""

import os
import re
import ast
from typing import List, Dict, Tuple

class SystematicIssueChecker:
    def __init__(self):
        self.xml_integration_file = "engine/processing/stages/xml_template_integration_stage.py"
        self.issues_found = []
        
    def check_method_call_parameters(self):
        """检查XML模板集成阶段的方法调用参数"""
        print("🔍 检查XML模板集成阶段的方法调用参数")
        print("=" * 60)
        
        if not os.path.exists(self.xml_integration_file):
            print(f"❌ 文件不存在: {self.xml_integration_file}")
            return None
        
        try:
            with open(self.xml_integration_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找所有_update_*_configuration方法的定义和调用
            method_definitions = self._find_method_definitions(content)
            method_calls = self._find_method_calls(content)
            
            print(f"✅ 找到方法定义: {len(method_definitions)}个")
            print(f"✅ 找到方法调用: {len(method_calls)}个")
            
            # 验证方法调用参数
            parameter_issues = self._verify_method_parameters(method_definitions, method_calls)
            
            return {
                'method_definitions': method_definitions,
                'method_calls': method_calls,
                'parameter_issues': parameter_issues
            }
            
        except Exception as e:
            print(f"❌ 检查失败: {str(e)}")
            return None
    
    def _find_method_definitions(self, content: str) -> Dict[str, Dict]:
        """查找方法定义"""
        method_definitions = {}
        
        # 匹配_update_*_configuration方法定义
        pattern = r'def\s+(_update_\w*_configuration)\s*\(([^)]*)\):'
        matches = re.finditer(pattern, content)
        
        for match in matches:
            method_name = match.group(1)
            params_str = match.group(2)
            
            # 解析参数
            params = self._parse_parameters(params_str)
            line_number = content[:match.start()].count('\n') + 1
            
            method_definitions[method_name] = {
                'parameters': params,
                'line_number': line_number,
                'param_count': len(params)
            }
        
        return method_definitions
    
    def _find_method_calls(self, content: str) -> List[Dict]:
        """查找方法调用"""
        method_calls = []
        
        # 匹配self._update_*_configuration方法调用
        pattern = r'self\.(_update_\w*_configuration)\s*\(([^)]*)\)'
        matches = re.finditer(pattern, content)
        
        for match in matches:
            method_name = match.group(1)
            args_str = match.group(2)
            
            # 解析参数
            args = self._parse_arguments(args_str)
            line_number = content[:match.start()].count('\n') + 1
            
            method_calls.append({
                'method_name': method_name,
                'arguments': args,
                'line_number': line_number,
                'arg_count': len(args)
            })
        
        return method_calls
    
    def _parse_parameters(self, params_str: str) -> List[str]:
        """解析方法参数"""
        if not params_str.strip():
            return []
        
        # 简单的参数解析
        params = []
        for param in params_str.split(','):
            param = param.strip()
            if param:
                # 移除默认值
                if '=' in param:
                    param = param.split('=')[0].strip()
                # 移除类型注解
                if ':' in param:
                    param = param.split(':')[0].strip()
                params.append(param)
        
        return params
    
    def _parse_arguments(self, args_str: str) -> List[str]:
        """解析方法调用参数"""
        if not args_str.strip():
            return []
        
        # 简单的参数解析
        args = []
        for arg in args_str.split(','):
            arg = arg.strip()
            if arg:
                args.append(arg)
        
        return args
    
    def _verify_method_parameters(self, definitions: Dict, calls: List[Dict]) -> List[Dict]:
        """验证方法调用参数"""
        issues = []
        
        for call in calls:
            method_name = call['method_name']
            
            if method_name in definitions:
                definition = definitions[method_name]
                
                # 检查参数数量
                def_param_count = definition['param_count']
                call_arg_count = call['arg_count']
                
                # 考虑self参数和可选参数
                min_params = max(0, def_param_count - 1)  # 减去self
                max_params = def_param_count - 1  # 减去self
                
                # 检查可选参数
                def_params = definition['parameters']
                optional_count = sum(1 for p in def_params if '=' in str(p) or 'None' in str(p))
                min_params = max_params - optional_count
                
                if call_arg_count < min_params or call_arg_count > max_params:
                    issues.append({
                        'method_name': method_name,
                        'call_line': call['line_number'],
                        'definition_line': definition['line_number'],
                        'expected_params': f"{min_params}-{max_params}",
                        'actual_params': call_arg_count,
                        'definition_params': def_params,
                        'call_args': call['arguments'],
                        'severity': 'error'
                    })
        
        return issues
    
    def check_log_file_errors(self):
        """检查日志文件中的错误模式"""
        print("\n🔍 检查日志文件中的错误模式")
        print("=" * 60)
        
        log_file = "output/logs/debug.log"
        if not os.path.exists(log_file):
            print(f"❌ 日志文件不存在: {log_file}")
            return None
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找常见错误模式
            error_patterns = {
                'parameter_mismatch': r'takes from \d+ to \d+ positional arguments but \d+ were given',
                'type_error': r'TypeError:.*',
                'attribute_error': r'AttributeError:.*',
                'key_error': r'KeyError:.*',
                'xml_error': r'XMLSyntaxError:.*'
            }
            
            error_summary = {}
            
            for error_type, pattern in error_patterns.items():
                matches = re.findall(pattern, content)
                if matches:
                    error_summary[error_type] = {
                        'count': len(matches),
                        'examples': matches[:3]  # 只保留前3个例子
                    }
            
            print(f"📊 错误模式统计:")
            for error_type, data in error_summary.items():
                print(f"   {error_type}: {data['count']}次")
                for example in data['examples']:
                    print(f"      - {example[:100]}...")
            
            return error_summary
            
        except Exception as e:
            print(f"❌ 检查日志失败: {str(e)}")
            return None
    
    def check_other_modules(self):
        """检查其他核心模块是否存在类似问题"""
        print("\n🔍 检查其他核心模块")
        print("=" * 60)
        
        modules_to_check = [
            "engine/processing/stages/interface_processing_stage.py",
            "engine/processing/stages/fortigate_policy_stage.py",
            "engine/generators/xml_integrator.py",
            "engine/generators/interface_common.py"
        ]
        
        module_issues = {}
        
        for module_path in modules_to_check:
            if os.path.exists(module_path):
                issues = self._check_module_for_issues(module_path)
                if issues:
                    module_issues[module_path] = issues
                    print(f"⚠️ {module_path}: 发现 {len(issues)} 个潜在问题")
                else:
                    print(f"✅ {module_path}: 未发现问题")
            else:
                print(f"❌ {module_path}: 文件不存在")
        
        return module_issues
    
    def _check_module_for_issues(self, module_path: str) -> List[Dict]:
        """检查单个模块的问题"""
        issues = []
        
        try:
            with open(module_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查常见问题模式
            issue_patterns = {
                'hardcoded_values': r'(text\s*=\s*["\']true["\']|text\s*=\s*["\']false["\'])',
                'missing_error_handling': r'except\s*:',
                'long_methods': r'def\s+\w+.*?(?=def|\Z)',
                'magic_numbers': r'\b\d{2,}\b'
            }
            
            for issue_type, pattern in issue_patterns.items():
                matches = re.finditer(pattern, content, re.DOTALL)
                for match in matches:
                    line_number = content[:match.start()].count('\n') + 1
                    issues.append({
                        'type': issue_type,
                        'line': line_number,
                        'content': match.group(0)[:50] + '...'
                    })
        
        except Exception as e:
            print(f"检查模块 {module_path} 失败: {str(e)}")
        
        return issues
    
    def generate_report(self, check_results: Dict):
        """生成检查报告"""
        print("\n📊 系统性问题排查报告")
        print("=" * 80)
        
        # 参数错误统计
        if 'parameter_issues' in check_results:
            param_issues = check_results['parameter_issues']
            if param_issues:
                print(f"❌ 发现 {len(param_issues)} 个参数错误:")
                for issue in param_issues:
                    print(f"   方法: {issue['method_name']}")
                    print(f"   调用行: {issue['call_line']}")
                    print(f"   期望参数: {issue['expected_params']}")
                    print(f"   实际参数: {issue['actual_params']}")
                    print(f"   严重程度: {issue['severity']}")
                    print()
            else:
                print("✅ 未发现参数错误")
        
        # 日志错误统计
        if 'log_errors' in check_results:
            log_errors = check_results['log_errors']
            if log_errors:
                print(f"\n📋 日志错误统计:")
                for error_type, data in log_errors.items():
                    print(f"   {error_type}: {data['count']}次")
            else:
                print("\n✅ 日志中未发现错误模式")
        
        # 其他模块问题
        if 'module_issues' in check_results:
            module_issues = check_results['module_issues']
            if module_issues:
                print(f"\n⚠️ 其他模块问题:")
                for module, issues in module_issues.items():
                    print(f"   {module}: {len(issues)}个问题")
            else:
                print(f"\n✅ 其他模块未发现问题")

def main():
    """主函数"""
    print("🚀 开始系统性问题排查")
    print("=" * 80)
    
    checker = SystematicIssueChecker()
    
    # 1. 检查方法调用参数
    method_check_results = checker.check_method_call_parameters()
    
    # 2. 检查日志文件错误
    log_errors = checker.check_log_file_errors()
    
    # 3. 检查其他模块
    module_issues = checker.check_other_modules()
    
    # 4. 生成报告
    all_results = {
        'parameter_issues': method_check_results['parameter_issues'] if method_check_results else [],
        'log_errors': log_errors,
        'module_issues': module_issues
    }
    
    checker.generate_report(all_results)
    
    print(f"\n{'='*80}")
    print("🎯 排查总结:")
    print(f"{'='*80}")
    
    total_issues = 0
    if method_check_results and method_check_results['parameter_issues']:
        total_issues += len(method_check_results['parameter_issues'])
    
    if module_issues:
        for issues in module_issues.values():
            total_issues += len(issues)
    
    if total_issues == 0:
        print("🎉 系统性问题排查完成，未发现严重问题")
        print("✅ 代码质量良好，可以安全部署")
    else:
        print(f"⚠️ 发现 {total_issues} 个潜在问题")
        print("📋 建议优先修复参数错误和关键模块问题")

if __name__ == "__main__":
    main()
