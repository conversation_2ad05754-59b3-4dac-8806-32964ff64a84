#config-version=FG5H0E-7.0.17-FW-build0682-250113:opmode=0:vdom=0:user=bbsAdmin
#conf_file_ver=1084307443800595
#buildno=0682
#global_vdom=1
config system global
    set admin-sport 44443
    set admin-ssh-port 28822
    set admin-telnet-port 28823
    set admintimeout 60
    set alias "FG5H0E5819901068"
    set dst disable
    set hostname "KHU-FGT-1"
    set management-port-use-admin-sport disable
    set proxy-auth-timeout 5
    set revision-backup-on-logout enable
    set revision-image-auto-backup enable
    set strict-dirty-session-check disable
    set tcp-halfclose-timer 40
    set tcp-halfopen-timer 25
    set timezone 85
    set traffic-priority-level high
    set udp-idle-timer 40
end
config system accprofile
    edit "prof_admin"
        set secfabgrp read-write
        set ftviewgrp read-write
        set authgrp read-write
        set sysgrp read-write
        set netgrp read-write
        set loggrp read-write
        set fwgrp read-write
        set vpngrp read-write
        set utmgrp read-write
        set wifi read-write
    next
    edit "LogSign"
        set fwgrp custom
        config fwgrp-permission
            set address read-write
        end
    next
end
config system np6
    edit "np6_0"
    next
end
config system interface
    edit "ha"
        set vdom "root"
        set type physical
        set snmp-index 10
    next
    edit "mgmt"
        set vdom "root"
        set ip *********** *************
        set allowaccess ping https
        set type physical
        set snmp-index 9
    next
    edit "port1"
        set vdom "root"
        set type physical
        set snmp-index 2
    next
    edit "port2"
        set vdom "root"
        set ip *********** *************
        set allowaccess ping https
        set vlanforward enable
        set type physical
        set alias "dmz"
        set snmp-index 12
    next
    edit "port3"
        set vdom "root"
        set ip ********** ***********
        set allowaccess ping
        set type physical
        set alias "eduroam"
        set snmp-index 19
    next
    edit "port4"
        set vdom "root"
        set allowaccess ping
        set vlanforward enable
        set type physical
        set alias "wifi"
        set monitor-bandwidth enable
        set snmp-index 14
    next
    edit "port5"
        set vdom "root"
        set ip ************* ***************
        set allowaccess ping https
        set vlanforward enable
        set type physical
        set alias "UlakNet"
        set monitor-bandwidth enable
        set snmp-index 15
    next
    edit "port6"
        set vdom "root"
        set ip *********** *************
        set allowaccess ping https
        set type physical
        set alias "DMZ2"
        set device-identification enable
        set role lan
        set snmp-index 8
    next
    edit "port7"
        set vdom "root"
        set type physical
        set snmp-index 7
    next
    edit "port8"
        set vdom "root"
        set type physical
        set snmp-index 6
    next
    edit "port9"
        set vdom "root"
        set ip *********** ***************
        set allowaccess ping
        set status down
        set type physical
        set alias "Vodafone"
        set role wan
        set snmp-index 5
    next
    edit "port10"
        set vdom "root"
        set type physical
        set snmp-index 3
    next
    edit "port11"
        set vdom "root"
        set type physical
        set snmp-index 4
    next
    edit "port12"
        set vdom "root"
        set type physical
        set snmp-index 20
    next
    edit "s1"
        set vdom "root"
        set type physical
        set snmp-index 1
    next
    edit "s2"
        set vdom "root"
        set type physical
        set snmp-index 16
    next
    edit "vw1"
        set vdom "root"
        set type physical
        set snmp-index 17
    next
    edit "vw2"
        set vdom "root"
        set type physical
        set snmp-index 18
    next
    edit "x1"
        set vdom "root"
        set ip ************* ***************
        set allowaccess ping
        set vlanforward enable
        set type physical
        set alias "outside"
        set monitor-bandwidth enable
        set role wan
        set snmp-index 13
        set mtu-override enable
    next
    edit "x2"
        set vdom "root"
        set ip *********** *************
        set allowaccess ping https ssh snmp
        set vlanforward enable
        set type physical
        set alias "inside"
        set device-identification enable
        set monitor-bandwidth enable
        set snmp-index 11
    next
    edit "modem"
        set vdom "root"
        set mode pppoe
        set allowaccess fabric
        set vlanforward enable
        set status down
        set type physical
        set snmp-index 37
        set defaultgw disable
    next
    edit "naf.root"
        set vdom "root"
        set type tunnel
        set src-check disable
        set snmp-index 55
    next
    edit "l2t.root"
        set vdom "root"
        set type tunnel
        set snmp-index 56
    next
    edit "ssl.root"
        set vdom "root"
        set allowaccess fabric
        set type tunnel
        set alias "sslvpn tunnel interface"
        set snmp-index 38
    next
    edit "Vodafone_MPLS"
        set vdom "root"
        set ip *********** *************
        set allowaccess ping
        set device-identification enable
        set role lan
        set snmp-index 21
        set interface "port1"
        set vlanid 3452
    next
    edit "Vlan55"
        set vdom "root"
        set ip ********** *************
        set allowaccess ping
        set device-identification enable
        set role lan
        set snmp-index 22
        set interface "x2"
        set vlanid 55
    next
    edit "Vlan99"
        set vdom "root"
        set dhcp-relay-service enable
        set ip ********* *************
        set allowaccess ping
        set alias "KARANTINA"
        set device-identification enable
        set role lan
        set snmp-index 23
        set dhcp-relay-ip "*********" 
        set interface "x2"
        set vlanid 99
    next
    edit "Vlan100"
        set vdom "root"
        set dhcp-relay-service enable
        set ip ********** *************
        set allowaccess ping
        set alias "WIRELES_CONTROLLER2"
        set device-identification enable
        set role lan
        set snmp-index 24
        set dhcp-relay-ip "*********" "*********8" 
        set interface "x2"
        set vlanid 100
    next
    edit "Vlan103"
        set vdom "root"
        set dhcp-relay-service enable
        set ip ******** *************
        set allowaccess ping https
        set device-identification enable
        set role lan
        set snmp-index 25
        set dhcp-relay-ip "*********" "*********8" 
        set interface "x2"
        set vlanid 103
    next
    edit "Vlan104"
        set vdom "root"
        set dhcp-relay-service enable
        set ip ******** *************
        set allowaccess ping
        set alias "GUVENLIK"
        set device-identification enable
        set monitor-bandwidth enable
        set role lan
        set snmp-index 26
        set dhcp-relay-ip "*********" "*********8" 
        set interface "x2"
        set vlanid 104
    next
    edit "Vlan105"
        set vdom "root"
        set dhcp-relay-service enable
        set ip ******** *************
        set allowaccess ping
        set alias "IDARI-AKADEMIK"
        set device-identification enable
        set role lan
        set snmp-index 27
        set dhcp-relay-ip "*********" "*********8" 
        set interface "x2"
        set vlanid 105
    next
    edit "Vlan106"
        set vdom "root"
        set dhcp-relay-service enable
        set ip ******** *************
        set allowaccess ping
        set alias "IDARI-AKADEMIK"
        set device-identification enable
        set role lan
        set snmp-index 28
        set dhcp-relay-ip "*********" "*********8" 
        set interface "x2"
        set vlanid 106
    next
    edit "Vlan107"
        set vdom "root"
        set dhcp-relay-service enable
        set ip ******** *************
        set allowaccess ping
        set alias "HOCA SUNUCULARI"
        set device-identification enable
        set role lan
        set snmp-index 29
        set dhcp-relay-ip "*********8" "*********" 
        set interface "x2"
        set vlanid 107
    next
    edit "Vlan108"
        set vdom "root"
        set dhcp-relay-service enable
        set ip ******** *************
        set allowaccess ping
        set alias "IDARI"
        set device-identification enable
        set role lan
        set snmp-index 30
        set dhcp-relay-ip "*********" "*********8" 
        set interface "x2"
        set vlanid 108
    next
    edit "Vlan109"
        set vdom "root"
        set dhcp-relay-service enable
        set ip ******** *************
        set allowaccess ping
        set alias "MALI ISLER"
        set device-identification enable
        set role lan
        set snmp-index 31
        set dhcp-relay-ip "*********" "*********8" 
        set interface "x2"
        set vlanid 109
    next
    edit "Vlan110"
        set vdom "root"
        set dhcp-relay-service enable
        set ip ********* *************
        set allowaccess ping
        set alias "OGRENCI LAB-1"
        set device-identification enable
        set role lan
        set snmp-index 32
        set dhcp-relay-ip "*********" "*********8" 
        set interface "x2"
        set vlanid 110
    next
    edit "Vlan111"
        set vdom "root"
        set dhcp-relay-service enable
        set ip ********* *************
        set allowaccess ping
        set alias "OGRENCI LAB-2"
        set device-identification enable
        set role lan
        set snmp-index 33
        set dhcp-relay-ip "*********" "*********8" 
        set interface "x2"
        set vlanid 111
    next
    edit "Vlan113"
        set vdom "root"
        set dhcp-relay-service enable
        set ip ********* *************
        set allowaccess ping
        set alias "PRINTER"
        set device-identification enable
        set role lan
        set snmp-index 34
        set dhcp-relay-ip "*********" "*********8" 
        set interface "x2"
        set vlanid 113
    next
    edit "Vlan115"
        set vdom "root"
        set dhcp-relay-service enable
        set ip ********* *************
        set allowaccess ping
        set alias "BIO INFORMATIK LAB"
        set device-identification enable
        set role lan
        set snmp-index 35
        set dhcp-relay-ip "*********" "*********8" 
        set interface "x2"
        set vlanid 115
    next
    edit "Vlan116"
        set vdom "root"
        set ip ********* *************
        set allowaccess ping
        set alias "UZAKTAN EGITIM SUNUCU"
        set device-identification enable
        set role lan
        set snmp-index 36
        set interface "x2"
        set vlanid 116
    next
    edit "Vlan117"
        set vdom "root"
        set dhcp-relay-service enable
        set ip ********* *************
        set allowaccess ping snmp radius-acct fabric
        set alias "AKADEMIK WIFI"
        set device-identification enable
        set role lan
        set snmp-index 39
        set dhcp-relay-ip "*********" "*********8" 
        set interface "x2"
        set vlanid 117
    next
    edit "Vlan120"
        set vdom "root"
        set dhcp-relay-service enable
        set ip ********* *************
        set allowaccess ping
        set alias "DIS FIRMALAR"
        set device-identification enable
        set role lan
        set snmp-index 40
        set dhcp-relay-ip "*********" "*********8" 
        set interface "x2"
        set vlanid 120
    next
    edit "Vlan136"
        set vdom "root"
        set dhcp-relay-service enable
        set ip ********* *************
        set allowaccess ping
        set alias "INTERNET"
        set device-identification enable
        set role lan
        set snmp-index 41
        set dhcp-relay-ip "*********" "*********8" 
        set interface "x2"
        set vlanid 136
    next
    edit "Vlan150"
        set vdom "root"
        set dhcp-relay-service enable
        set ip ********* *************
        set allowaccess ping ssh snmp radius-acct
        set alias "WIRELESS CONTROLLER-1"
        set device-identification enable
        set role lan
        set snmp-index 43
        set dhcp-relay-ip "*********" "*********8" 
        set interface "x2"
        set vlanid 150
    next
    edit "Vlan151"
        set vdom "root"
        set dhcp-relay-service enable
        set ip ********* *************
        set allowaccess ping
        set alias "AKILLI KURSU"
        set device-identification enable
        set role lan
        set snmp-index 44
        set dhcp-relay-ip "*********" "*********8" 
        set interface "x2"
        set vlanid 151
    next
    edit "Vlan300"
        set vdom "root"
        set dhcp-relay-service enable
        set ip ********* *************
        set allowaccess ping
        set alias "IPTELEFON"
        set device-identification enable
        set role lan
        set snmp-index 45
        set dhcp-relay-ip "*********8" "*********" 
        set interface "x2"
        set vlanid 300
    next
    edit "Vlan130"
        set vdom "root"
        set ip ********** ***********
        set allowaccess ping
        set alias "OGRENCI WIFI"
        set device-identification enable
        set monitor-bandwidth enable
        set role lan
        set snmp-index 47
        set interface "x2"
        set vlanid 130
    next
    edit "Vlan155"
        set vdom "root"
        set ip ********** *************
        set allowaccess ping https
        set alias "KAMERA SISTEMI2"
        set device-identification enable
        set role lan
        set snmp-index 48
        set interface "x2"
        set vlanid 155
    next
    edit "Vlan200"
        set vdom "root"
        set dhcp-relay-service enable
        set allowaccess ping
        set alias "EDUROAM"
        set device-identification enable
        set role lan
        set snmp-index 49
        set dhcp-relay-ip "*********" "*********8" 
        set interface "x2"
        set vlanid 200
    next
    edit "Vlan222"
        set vdom "root"
        set allowaccess ping
        set alias "GUEST_WIFI"
        set device-identification enable
        set role lan
        set snmp-index 50
        set interface "x2"
        set vlanid 222
    next
    edit "VLAN101"
        set vdom "root"
        set ip ******** *************
        set allowaccess ping https ssh snmp http radius-acct fabric
        set alias "SUNUCULAR"
        set device-identification enable
        set role lan
        set snmp-index 52
        set interface "x2"
        set vlanid 101
    next
    edit "UITSEC_Forti"
        set vdom "root"
        set type tunnel
        set snmp-index 51
        set interface "x1"
    next
    edit "UITSEC_CP"
        set vdom "root"
        set type tunnel
        set snmp-index 53
        set interface "x1"
    next
    edit "BBS_PAM"
        set vdom "root"
        set type tunnel
        set snmp-index 54
        set interface "x1"
    next
    edit "BBS_PAM1"
        set vdom "root"
        set type tunnel
        set snmp-index 57
        set interface "x1"
    next
    edit "TO-CHECKPOINT"
        set vdom "root"
        set ip ********* *************
        set allowaccess ping
        set role lan
        set snmp-index 58
        set interface "x2"
        set vlanid 145
    next
    edit "Vlan114"
        set vdom "root"
        set dhcp-relay-service enable
        set ip ********* *************
        set allowaccess ping
        set alias "SELIMPASA"
        set device-identification enable
        set role lan
        set snmp-index 46
        set dhcp-relay-ip "*********8" 
        set interface "x2"
        set vlanid 114
    next
    edit "RUIJIE-MGMT"
        set vdom "root"
        set dhcp-relay-service enable
        set ip ************* *************
        set allowaccess ping https
        set device-identification enable
        set role lan
        set snmp-index 42
        set dhcp-relay-ip "*********" "*********8" 
        set interface "x2"
        set vlanid 199
    next
end