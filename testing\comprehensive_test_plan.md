# FortiGate转换器性能优化测试验证方案

## 🎯 测试目标

1. **性能验证**：确认优化后转换时间从18.5分钟减少到5-8分钟
2. **功能完整性**：确保所有转换功能正常工作
3. **稳定性验证**：确认系统在各种条件下稳定运行
4. **兼容性测试**：确保与现有系统和配置文件兼容

## 📋 测试分类和策略

### 1. 单元测试 (Unit Tests)

**目标**：验证各个优化组件的独立功能

```python
# tests/unit/test_intelligent_parser.py
import unittest
from optimization_design.intelligent_config_parser import IntelligentConfigParser, ConfigSectionType

class TestIntelligentConfigParser(unittest.TestCase):
    
    def setUp(self):
        self.parser = IntelligentConfigParser()
    
    def test_section_identification(self):
        """测试配置段落识别"""
        test_lines = [
            "config system interface",
            "config firewall policy", 
            "config firewall address",
            "config unknown section"
        ]
        
        expected_types = [
            ConfigSectionType.INTERFACE,
            ConfigSectionType.POLICY,
            ConfigSectionType.ADDRESS,
            ConfigSectionType.UNKNOWN
        ]
        
        for line, expected in zip(test_lines, expected_types):
            result = self.parser._identify_section_type(line)
            self.assertEqual(result, expected, f"Failed for line: {line}")
    
    def test_priority_assignment(self):
        """测试优先级分配"""
        test_cases = [
            (ConfigSectionType.INTERFACE, 1),
            (ConfigSectionType.ADDRESS, 2),
            (ConfigSectionType.POLICY, 4),
            (ConfigSectionType.UNKNOWN, 8)
        ]
        
        for section_type, expected_priority in test_cases:
            priority = self.parser._get_section_priority(section_type)
            self.assertEqual(priority, expected_priority)
    
    def test_parallel_processing(self):
        """测试并行处理"""
        # 创建测试配置段落
        test_sections = [
            ConfigSection(ConfigSectionType.INTERFACE, 0, 10, ["test"], 1),
            ConfigSection(ConfigSectionType.ADDRESS, 11, 20, ["test"], 2),
            ConfigSection(ConfigSectionType.POLICY, 21, 30, ["test"], 4)
        ]
        
        result = self.parser.parallel_section_processing(test_sections)
        
        # 验证结果结构
        self.assertIn('interfaces', result)
        self.assertIn('addresses', result)
        self.assertIn('policies', result)
        self.assertIn('warnings', result)

# tests/unit/test_interface_mapper.py
class TestEnhancedInterfaceMapper(unittest.TestCase):
    
    def setUp(self):
        self.mapper = EnhancedInterfaceMapper()
    
    def test_ssl_interface_mapping(self):
        """测试SSL接口映射"""
        test_cases = [
            ('ssl.root', 'tunnel-ssl-root'),
            ('ssl_tunnel', 'tunnel-ssl'),
            ('ssl.vpn', 'tunnel-ssl-vpn')
        ]
        
        mappings = {}
        
        for input_interface, expected_output in test_cases:
            result = self.mapper.validate_interface_mapping(input_interface, mappings)
            self.assertEqual(result, expected_output)
            self.assertIn(input_interface, mappings)
    
    def test_vlan_interface_generation(self):
        """测试VLAN接口生成"""
        base_mappings = {'x2': 'Ge0/7'}
        
        test_cases = [
            ('Vlan100', 'Ge0/7.100'),
            ('Vlan55', 'Ge0/7.55'),
            ('x2.200', 'Ge0/7.200')
        ]
        
        for input_interface, expected_output in test_cases:
            result = self.mapper.validate_interface_mapping(input_interface, base_mappings)
            self.assertEqual(result, expected_output)
    
    def test_policy_validation(self):
        """测试策略验证"""
        validator = PolicyInterfaceValidator(self.mapper)
        
        test_policy = {
            'policyid': '1',
            'srcintf': ['port1'],
            'dstintf': ['port2']
        }
        
        mappings = {'port1': 'Ge0/1', 'port2': 'Ge0/2'}
        
        result = validator.validate_policy_interfaces(test_policy, mappings)
        self.assertTrue(result)
```

### 2. 集成测试 (Integration Tests)

**目标**：验证优化组件之间的协作

```python
# tests/integration/test_optimized_pipeline.py
class TestOptimizedPipeline(unittest.TestCase):
    
    def setUp(self):
        self.test_config_file = "tests/data/medium_config.conf"
        self.interface_mapping_file = "tests/data/test_interface_mapping.json"
    
    def test_end_to_end_conversion(self):
        """端到端转换测试"""
        from optimization_design.intelligent_config_parser import OptimizedFortigateParser
        
        parser = OptimizedFortigateParser()
        
        start_time = time.time()
        result = parser.parse_large_config(self.test_config_file)
        processing_time = time.time() - start_time
        
        # 验证处理时间
        self.assertLess(processing_time, 300, "处理时间应少于5分钟")
        
        # 验证结果结构
        self.assertIn('interfaces', result)
        self.assertIn('policies', result)
        self.assertIn('addresses', result)
        
        # 验证数据质量
        self.assertGreater(len(result['interfaces']), 0, "应该解析到接口")
        self.assertGreater(len(result['policies']), 0, "应该解析到策略")
    
    def test_memory_usage_optimization(self):
        """内存使用优化测试"""
        import psutil
        
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024
        
        # 执行转换
        parser = OptimizedFortigateParser()
        result = parser.parse_large_config(self.test_config_file)
        
        peak_memory = process.memory_info().rss / 1024 / 1024
        memory_increase = peak_memory - initial_memory
        
        # 验证内存使用
        self.assertLess(peak_memory, 200, "峰值内存应少于200MB")
        self.assertLess(memory_increase, 150, "内存增长应少于150MB")
    
    def test_interface_mapping_integration(self):
        """接口映射集成测试"""
        from optimization_design.interface_mapping_optimizer import optimize_interface_mapping
        
        # 模拟策略数据
        test_policies = [
            {'policyid': '1', 'srcintf': ['ssl.root'], 'dstintf': ['port1']},
            {'policyid': '2', 'srcintf': ['Vlan100'], 'dstintf': ['x2']},
        ]
        
        result = optimize_interface_mapping(
            self.interface_mapping_file, 
            test_policies
        )
        
        # 验证映射增强
        self.assertIn('ssl.root', result['enhanced_mappings'])
        self.assertIn('Vlan100', result['enhanced_mappings'])
        
        # 验证验证报告
        report = result['validation_report']
        self.assertGreater(float(report['success_rate'].rstrip('%')), 90)
```

### 3. 性能测试 (Performance Tests)

**目标**：验证性能优化效果

```python
# tests/performance/test_performance_benchmarks.py
class PerformanceBenchmarkTest(unittest.TestCase):
    
    def setUp(self):
        self.large_config_file = "KHU-FGT-1_7-0_0682_202507311406.conf"
        self.baseline_metrics = self._load_baseline_metrics()
    
    def test_parsing_speed_improvement(self):
        """解析速度改进测试"""
        # 测试优化版本
        start_time = time.time()
        optimized_parser = OptimizedFortigateParser()
        optimized_result = optimized_parser.parse_large_config(self.large_config_file)
        optimized_time = time.time() - start_time
        
        # 与基线比较
        baseline_time = self.baseline_metrics.get('parsing_time', 1110)  # 18.5分钟
        improvement_ratio = (baseline_time - optimized_time) / baseline_time
        
        # 验证改进效果
        self.assertGreater(improvement_ratio, 0.6, "解析时间应改进60%以上")
        self.assertLess(optimized_time, 480, "解析时间应少于8分钟")
        
        print(f"解析时间改进: {improvement_ratio*100:.1f}%")
        print(f"优化后时间: {optimized_time:.2f}秒")
    
    def test_memory_efficiency(self):
        """内存效率测试"""
        import psutil
        
        memory_samples = []
        
        def memory_monitor():
            while self.monitoring:
                memory_samples.append(psutil.Process().memory_info().rss / 1024 / 1024)
                time.sleep(1)
        
        # 启动内存监控
        self.monitoring = True
        monitor_thread = threading.Thread(target=memory_monitor)
        monitor_thread.start()
        
        try:
            # 执行转换
            parser = OptimizedFortigateParser()
            result = parser.parse_large_config(self.large_config_file)
            
        finally:
            self.monitoring = False
            monitor_thread.join()
        
        # 分析内存使用
        peak_memory = max(memory_samples)
        average_memory = sum(memory_samples) / len(memory_samples)
        
        self.assertLess(peak_memory, 200, f"峰值内存({peak_memory:.1f}MB)应少于200MB")
        self.assertLess(average_memory, 150, f"平均内存({average_memory:.1f}MB)应少于150MB")
    
    def test_throughput_improvement(self):
        """吞吐量改进测试"""
        config_size = os.path.getsize(self.large_config_file)
        
        start_time = time.time()
        parser = OptimizedFortigateParser()
        result = parser.parse_large_config(self.large_config_file)
        processing_time = time.time() - start_time
        
        # 计算吞吐量
        throughput_mb_per_sec = (config_size / 1024 / 1024) / processing_time
        
        # 验证吞吐量
        self.assertGreater(throughput_mb_per_sec, 0.1, "吞吐量应大于0.1MB/s")
        
        print(f"处理吞吐量: {throughput_mb_per_sec:.3f} MB/s")

# tests/performance/stress_test.py
class StressTest(unittest.TestCase):
    """压力测试"""
    
    def test_concurrent_conversions(self):
        """并发转换测试"""
        import concurrent.futures
        
        def convert_config(config_file):
            parser = OptimizedFortigateParser()
            return parser.parse_large_config(config_file)
        
        # 准备测试文件
        test_files = [self.large_config_file] * 5  # 5个并发转换
        
        start_time = time.time()
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
            futures = [executor.submit(convert_config, f) for f in test_files]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        total_time = time.time() - start_time
        
        # 验证并发性能
        self.assertEqual(len(results), 5, "应该完成5个转换")
        self.assertLess(total_time, 1800, "并发转换应在30分钟内完成")
        
        # 验证结果一致性
        for i, result in enumerate(results[1:], 1):
            self.assertEqual(
                len(result['policies']), 
                len(results[0]['policies']),
                f"结果{i}的策略数量应与结果0一致"
            )
    
    def test_memory_leak_detection(self):
        """内存泄漏检测测试"""
        import psutil
        
        initial_memory = psutil.Process().memory_info().rss / 1024 / 1024
        
        # 执行多次转换
        for i in range(10):
            parser = OptimizedFortigateParser()
            result = parser.parse_large_config(self.large_config_file)
            
            # 强制垃圾回收
            import gc
            gc.collect()
            
            current_memory = psutil.Process().memory_info().rss / 1024 / 1024
            memory_growth = current_memory - initial_memory
            
            # 检查内存增长
            self.assertLess(memory_growth, 50, f"第{i+1}次转换后内存增长应少于50MB")
        
        final_memory = psutil.Process().memory_info().rss / 1024 / 1024
        total_growth = final_memory - initial_memory
        
        self.assertLess(total_growth, 100, "10次转换后总内存增长应少于100MB")
```

### 4. 功能回归测试 (Regression Tests)

**目标**：确保优化不影响现有功能

```python
# tests/regression/test_feature_regression.py
class FeatureRegressionTest(unittest.TestCase):
    
    def setUp(self):
        self.test_configs = [
            "tests/data/small_config.conf",
            "tests/data/medium_config.conf", 
            "tests/data/complex_config.conf"
        ]
        self.baseline_results = self._load_baseline_results()
    
    def test_policy_conversion_accuracy(self):
        """策略转换准确性回归测试"""
        for config_file in self.test_configs:
            with self.subTest(config=config_file):
                # 使用优化版本转换
                parser = OptimizedFortigateParser()
                optimized_result = parser.parse_large_config(config_file)
                
                # 与基线结果比较
                baseline_key = os.path.basename(config_file)
                if baseline_key in self.baseline_results:
                    baseline = self.baseline_results[baseline_key]
                    
                    # 比较策略数量
                    opt_policy_count = len(optimized_result.get('policies', []))
                    base_policy_count = len(baseline.get('policies', []))
                    
                    self.assertEqual(
                        opt_policy_count, base_policy_count,
                        f"策略数量不匹配: {opt_policy_count} vs {base_policy_count}"
                    )
                    
                    # 比较关键策略字段
                    self._compare_policy_details(
                        optimized_result['policies'],
                        baseline['policies']
                    )
    
    def test_interface_mapping_compatibility(self):
        """接口映射兼容性测试"""
        # 测试各种接口类型
        test_interfaces = [
            'port1', 'port2', 'mgmt',
            'Vlan100', 'Vlan55', 'x1', 'x2',
            'ssl.root', 'ssl_tunnel',
            'any', 'all'
        ]
        
        mapper = EnhancedInterfaceMapper()
        mappings = {}
        
        for interface in test_interfaces:
            result = mapper.validate_interface_mapping(interface, mappings)
            self.assertIsNotNone(result, f"接口 {interface} 应该能够映射")
            self.assertIn(interface, mappings, f"接口 {interface} 应该被添加到映射表")
    
    def test_xml_output_format(self):
        """XML输出格式回归测试"""
        parser = OptimizedFortigateParser()
        result = parser.parse_large_config(self.test_configs[0])
        
        # 模拟XML生成过程
        from engine.stages.xml_template_integration_stage import XMLTemplateIntegrationStage
        
        xml_stage = XMLTemplateIntegrationStage({})
        xml_output = xml_stage.process({'config_data': result})
        
        # 验证XML结构
        self.assertIn('<?xml version', xml_output.get('xml_content', ''))
        self.assertIn('<config', xml_output.get('xml_content', ''))
        self.assertIn('</config>', xml_output.get('xml_content', ''))
```

## 📊 测试执行计划

### 测试阶段安排

| 测试阶段 | 时间安排 | 测试类型 | 通过标准 |
|---------|----------|----------|----------|
| **第1周** | Day 1-5 | 单元测试 | 覆盖率>90%, 全部通过 |
| **第2周** | Day 6-10 | 集成测试 | 功能完整性100% |
| **第3周** | Day 11-15 | 性能测试 | 性能目标达成 |
| **第4周** | Day 16-20 | 回归测试 | 兼容性100% |
| **第5周** | Day 21-25 | 压力测试 | 稳定性验证 |
| **第6周** | Day 26-30 | 验收测试 | 用户验收通过 |

### 自动化测试执行

```bash
#!/bin/bash
# tests/run_all_tests.sh

echo "=== FortiGate转换器优化测试套件 ==="

# 设置测试环境
export PYTHONPATH="${PYTHONPATH}:$(pwd)"
export TEST_DATA_DIR="tests/data"

# 创建测试报告目录
mkdir -p test_reports

# 1. 单元测试
echo "执行单元测试..."
python -m pytest tests/unit/ -v --cov=optimization_design --cov-report=html:test_reports/coverage_unit

# 2. 集成测试
echo "执行集成测试..."
python -m pytest tests/integration/ -v --html=test_reports/integration_report.html

# 3. 性能测试
echo "执行性能测试..."
python -m pytest tests/performance/ -v --benchmark-only --benchmark-html=test_reports/performance_report.html

# 4. 回归测试
echo "执行回归测试..."
python -m pytest tests/regression/ -v --html=test_reports/regression_report.html

# 5. 生成综合报告
echo "生成综合测试报告..."
python tests/generate_test_report.py

echo "测试完成! 报告位置: test_reports/"
```

## 📈 测试成功标准

### 性能指标验证

| 指标 | 基线值 | 目标值 | 验证方法 |
|------|--------|--------|----------|
| 转换时间 | 18.5分钟 | 5-8分钟 | 自动化性能测试 |
| 内存使用 | 112MB | <200MB | 内存监控测试 |
| 策略验证成功率 | ~60% | >95% | 功能测试验证 |
| 警告数量 | 15,626个 | <3,000个 | 日志分析验证 |
| 处理速度 | 94行/秒 | 300-400行/秒 | 吞吐量测试 |

### 质量保证检查点

1. **代码质量**：
   - 单元测试覆盖率 ≥ 90%
   - 代码复杂度 ≤ 10
   - 无严重代码异味

2. **功能完整性**：
   - 所有现有功能正常工作
   - 新增功能按预期工作
   - 边界条件处理正确

3. **性能达标**：
   - 所有性能指标达到目标值
   - 无内存泄漏
   - 并发处理稳定

4. **用户体验**：
   - 错误信息清晰易懂
   - 进度显示准确
   - 操作响应及时

通过这个全面的测试验证方案，我们可以确保FortiGate转换器性能优化项目的质量和可靠性，为生产环境部署提供充分的信心保障。
