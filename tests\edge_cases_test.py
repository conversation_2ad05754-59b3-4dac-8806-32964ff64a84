"""
FortiGate twice-nat44边界场景测试

测试各种边界情况和异常场景的处理，确保系统在极端条件下的稳定性：
- 极限数据测试
- 异常输入处理
- 资源限制场景
- 并发处理测试
- 回退机制验证
"""

import sys
import os
import time
import threading
import gc
from typing import Dict, List, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from engine.business.models.twice_nat44_models import (
    TwiceNat44Rule, TwiceNat44MatchConditions, TwiceNat44SnatConfig, 
    TwiceNat44DnatConfig, TwiceNat44AddressType, TwiceNat44ConfigError
)
from engine.generators.nat_generator import NATGenerator
from engine.validators.twice_nat44_validator import TwiceNat44Validator
from engine.infrastructure.config.config_manager import ConfigManager


class EdgeCasesTestSuite:
    """边界场景测试套件"""
    
    def __init__(self):
        """初始化边界场景测试"""
        self.nat_generator = NATGenerator()
        self.validator = TwiceNat44Validator()
        self.config_manager = ConfigManager()
        
        # 测试结果统计
        self.test_results = {
            "total": 0,
            "passed": 0,
            "failed": 0,
            "details": []
        }
    
    def run_all_edge_cases_tests(self) -> bool:
        """运行所有边界场景测试"""
        print("🚀 开始twice-nat44边界场景测试")
        print("=" * 80)
        
        tests = [
            ("极限数据测试", self.test_extreme_data_limits),
            ("异常输入处理测试", self.test_invalid_input_handling),
            ("资源限制场景测试", self.test_resource_limits),
            ("并发处理测试", self.test_concurrent_processing),
            ("回退机制验证测试", self.test_fallback_mechanism),
            ("内存泄漏测试", self.test_memory_leaks),
            ("长时间运行测试", self.test_long_running_stability),
            ("特殊字符处理测试", self.test_special_characters)
        ]
        
        for test_name, test_func in tests:
            self._run_single_test(test_name, test_func)
        
        self._print_summary()
        return self.test_results["failed"] == 0
    
    def _run_single_test(self, test_name: str, test_func):
        """运行单个测试"""
        print(f"\n🧪 {test_name}")
        print("-" * 60)
        
        self.test_results["total"] += 1
        
        try:
            result = test_func()
            if result:
                self.test_results["passed"] += 1
                print(f"✅ {test_name} 通过")
                self.test_results["details"].append({"name": test_name, "status": "PASSED"})
            else:
                self.test_results["failed"] += 1
                print(f"❌ {test_name} 失败")
                self.test_results["details"].append({"name": test_name, "status": "FAILED"})
                
        except Exception as e:
            self.test_results["failed"] += 1
            print(f"❌ {test_name} 异常: {str(e)}")
            self.test_results["details"].append({"name": test_name, "status": "ERROR", "error": str(e)})
    
    def test_extreme_data_limits(self) -> bool:
        """测试极限数据"""
        try:
            print("  📊 测试极限数据处理...")
            
            # 测试极长的规则名称
            long_name = "A" * 1000
            try:
                match_conditions = TwiceNat44MatchConditions(dest_network="TEST_VIP")
                snat_config = TwiceNat44SnatConfig(address_type=TwiceNat44AddressType.INTERFACE)
                dnat_config = TwiceNat44DnatConfig(ipv4_address="*************")
                
                rule = TwiceNat44Rule(
                    name=long_name,
                    enabled=True,
                    description="极长名称测试",
                    match_conditions=match_conditions,
                    snat_config=snat_config,
                    dnat_config=dnat_config
                )
                
                print(f"    ✅ 极长规则名称处理成功 (长度: {len(long_name)})")
            except Exception as e:
                print(f"    ❌ 极长规则名称处理失败: {str(e)}")
                return False
            
            # 测试极限IP地址
            edge_ips = [
                "0.0.0.0",           # 最小IP
                "***************",   # 最大IP
                "127.0.0.1",         # 回环地址
                "***********",       # 链路本地地址
                "*********"          # 组播地址
            ]
            
            for ip in edge_ips:
                try:
                    dnat_config = TwiceNat44DnatConfig(ipv4_address=ip)
                    print(f"    ✅ 边界IP地址处理成功: {ip}")
                except Exception as e:
                    print(f"    ⚠️ 边界IP地址处理: {ip} - {str(e)}")
            
            # 测试极限端口
            edge_ports = [1, 65535, 80, 443, 8080]
            
            for port in edge_ports:
                try:
                    dnat_config = TwiceNat44DnatConfig(
                        ipv4_address="*************",
                        port=port
                    )
                    print(f"    ✅ 边界端口处理成功: {port}")
                except Exception as e:
                    print(f"    ❌ 边界端口处理失败: {port} - {str(e)}")
                    return False
            
            # 测试大量规则生成
            large_rule_count = 1000
            print(f"    📦 测试大量规则生成 ({large_rule_count}规则)...")
            
            start_time = time.time()
            rules = []
            
            for i in range(large_rule_count):
                policy = {
                    "name": f"LARGE_POLICY_{i+1}",
                    "status": "enable",
                    "service": ["HTTP"],
                    "fixedport": "disable"
                }
                
                vip = {
                    "name": f"LARGE_VIP_{i+1}",
                    "mappedip": f"10.{(i//65536)%256}.{(i//256)%256}.{i%256+1}",
                    "mappedport": str((i % 64511) + 1024)
                }
                
                rule = TwiceNat44Rule.from_fortigate_policy(policy, vip)
                rule_dict = rule.to_nat_rule_dict()
                rules.append(rule_dict)
            
            creation_time = time.time() - start_time
            print(f"    ✅ 大量规则创建成功 (耗时: {creation_time:.3f}秒)")
            
            # 测试大量规则XML生成
            start_time = time.time()
            nat_xml = self.nat_generator.generate_nat_xml(rules)
            xml_time = time.time() - start_time
            
            if nat_xml is None:
                print("    ❌ 大量规则XML生成失败")
                return False
            
            print(f"    ✅ 大量规则XML生成成功 (耗时: {xml_time:.3f}秒)")
            
            return True
            
        except Exception as e:
            print(f"    ❌ 极限数据测试异常: {str(e)}")
            return False
    
    def test_invalid_input_handling(self) -> bool:
        """测试异常输入处理"""
        try:
            print("  🚫 测试异常输入处理...")
            
            # 测试None输入
            invalid_inputs = [
                {"policy": None, "vip": {"name": "TEST", "mappedip": "*******"}},
                {"policy": {"name": "TEST"}, "vip": None},
                {"policy": {}, "vip": {"name": "TEST", "mappedip": "*******"}},
                {"policy": {"name": "TEST"}, "vip": {}},
            ]
            
            for i, inputs in enumerate(invalid_inputs):
                try:
                    TwiceNat44Rule.from_fortigate_policy(inputs["policy"], inputs["vip"])
                    print(f"    ❌ 异常输入{i+1}应该抛出异常但没有")
                    return False
                except (TwiceNat44ConfigError, TypeError, AttributeError):
                    print(f"    ✅ 异常输入{i+1}正确处理")
                except Exception as e:
                    print(f"    ⚠️ 异常输入{i+1}处理异常: {type(e).__name__}")
            
            # 测试无效数据类型
            invalid_types = [
                {"name": 123, "mappedip": "*******"},  # 数字名称
                {"name": "TEST", "mappedip": 123},      # 数字IP
                {"name": "TEST", "mappedip": "*******", "mappedport": "abc"},  # 字符串端口
            ]
            
            for i, vip in enumerate(invalid_types):
                try:
                    policy = {"name": "TEST_POLICY"}
                    TwiceNat44Rule.from_fortigate_policy(policy, vip)
                    print(f"    ⚠️ 无效类型{i+1}被接受（可能的类型转换）")
                except Exception:
                    print(f"    ✅ 无效类型{i+1}正确拒绝")
            
            # 测试空字符串
            empty_inputs = [
                {"name": "", "mappedip": "*******"},
                {"name": "TEST", "mappedip": ""},
                {"name": "   ", "mappedip": "*******"},  # 空白字符
            ]
            
            for i, vip in enumerate(empty_inputs):
                try:
                    policy = {"name": "TEST_POLICY"}
                    TwiceNat44Rule.from_fortigate_policy(policy, vip)
                    print(f"    ❌ 空输入{i+1}应该被拒绝但被接受")
                    return False
                except Exception:
                    print(f"    ✅ 空输入{i+1}正确拒绝")
            
            return True
            
        except Exception as e:
            print(f"    ❌ 异常输入处理测试异常: {str(e)}")
            return False
    
    def test_resource_limits(self) -> bool:
        """测试资源限制场景"""
        try:
            print("  💾 测试资源限制场景...")
            
            # 测试内存使用
            import psutil
            process = psutil.Process()
            initial_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            print(f"    📊 初始内存使用: {initial_memory:.1f}MB")
            
            # 创建大量对象测试内存使用
            large_objects = []
            object_count = 10000
            
            for i in range(object_count):
                match_conditions = TwiceNat44MatchConditions(
                    dest_network=f"VIP_{i}",
                    service="HTTP",
                    time_range="always"
                )
                
                snat_config = TwiceNat44SnatConfig(
                    address_type=TwiceNat44AddressType.IP,
                    address_value=f"203.0.{(i//256)%256}.{i%256+1}"
                )
                
                dnat_config = TwiceNat44DnatConfig(
                    ipv4_address=f"192.168.{(i//256)%256}.{i%256+1}",
                    port=8000 + (i % 1000)
                )
                
                rule = TwiceNat44Rule(
                    name=f"RESOURCE_TEST_RULE_{i}",
                    enabled=True,
                    description=f"资源测试规则 {i}",
                    match_conditions=match_conditions,
                    snat_config=snat_config,
                    dnat_config=dnat_config
                )
                
                large_objects.append(rule)
            
            peak_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_increase = peak_memory - initial_memory
            
            print(f"    📊 峰值内存使用: {peak_memory:.1f}MB")
            print(f"    📊 内存增长: {memory_increase:.1f}MB")
            print(f"    📊 平均每对象: {memory_increase/object_count*1024:.2f}KB")
            
            # 清理内存
            del large_objects
            gc.collect()
            
            final_memory = process.memory_info().rss / 1024 / 1024  # MB
            print(f"    📊 清理后内存: {final_memory:.1f}MB")
            
            # 验证内存是否正确释放
            memory_leak = final_memory - initial_memory
            if memory_leak > 50:  # 50MB阈值
                print(f"    ⚠️ 可能存在内存泄漏: {memory_leak:.1f}MB")
            else:
                print(f"    ✅ 内存使用正常: 泄漏{memory_leak:.1f}MB")
            
            return True
            
        except Exception as e:
            print(f"    ❌ 资源限制测试异常: {str(e)}")
            return False
    
    def test_concurrent_processing(self) -> bool:
        """测试并发处理"""
        try:
            print("  🔄 测试并发处理...")
            
            # 并发创建规则
            def create_rules_worker(worker_id: int, rule_count: int, results: list):
                try:
                    worker_rules = []
                    for i in range(rule_count):
                        policy = {
                            "name": f"CONCURRENT_POLICY_{worker_id}_{i}",
                            "status": "enable",
                            "service": ["HTTP"],
                            "fixedport": "disable"
                        }
                        
                        vip = {
                            "name": f"CONCURRENT_VIP_{worker_id}_{i}",
                            "mappedip": f"10.{worker_id}.{i//256}.{i%256+1}",
                            "mappedport": str(8000 + i)
                        }
                        
                        rule = TwiceNat44Rule.from_fortigate_policy(policy, vip)
                        rule_dict = rule.to_nat_rule_dict()
                        worker_rules.append(rule_dict)
                    
                    results.append({"worker_id": worker_id, "rules": worker_rules, "success": True})
                except Exception as e:
                    results.append({"worker_id": worker_id, "error": str(e), "success": False})
            
            # 启动多个并发工作线程
            thread_count = 5
            rules_per_thread = 100
            threads = []
            results = []
            
            print(f"    🚀 启动{thread_count}个并发线程，每个创建{rules_per_thread}规则")
            
            start_time = time.time()
            
            for i in range(thread_count):
                thread = threading.Thread(
                    target=create_rules_worker,
                    args=(i, rules_per_thread, results)
                )
                threads.append(thread)
                thread.start()
            
            # 等待所有线程完成
            for thread in threads:
                thread.join()
            
            concurrent_time = time.time() - start_time
            
            # 验证结果
            successful_workers = sum(1 for r in results if r["success"])
            total_rules = sum(len(r["rules"]) for r in results if r["success"])
            
            print(f"    ✅ 并发处理完成 (耗时: {concurrent_time:.3f}秒)")
            print(f"    📊 成功线程: {successful_workers}/{thread_count}")
            print(f"    📊 总规则数: {total_rules}")
            print(f"    📊 平均速度: {total_rules/concurrent_time:.1f}规则/秒")
            
            if successful_workers != thread_count:
                print("    ❌ 部分并发线程失败")
                for result in results:
                    if not result["success"]:
                        print(f"      线程{result['worker_id']}失败: {result['error']}")
                return False
            
            return True
            
        except Exception as e:
            print(f"    ❌ 并发处理测试异常: {str(e)}")
            return False
    
    def test_fallback_mechanism(self) -> bool:
        """测试回退机制"""
        try:
            print("  🔄 测试回退机制...")
            
            # 保存原始配置
            original_enabled = self.config_manager.get_twice_nat44_config('use_twice_nat44')
            original_fallback = self.config_manager.get_twice_nat44_config('twice_nat44_fallback')
            
            # 测试禁用twice-nat44时的回退
            self.config_manager.set_twice_nat44_config('use_twice_nat44', False)
            self.config_manager.set_twice_nat44_config('twice_nat44_fallback', True)
            
            print("    📋 测试twice-nat44禁用时的回退机制")
            
            if not self.config_manager.is_twice_nat44_enabled():
                print("    ✅ twice-nat44正确禁用")
            else:
                print("    ❌ twice-nat44禁用失败")
                return False
            
            if self.config_manager.is_twice_nat44_fallback_enabled():
                print("    ✅ 回退机制正确启用")
            else:
                print("    ❌ 回退机制启用失败")
                return False
            
            # 测试回退机制禁用
            self.config_manager.set_twice_nat44_config('twice_nat44_fallback', False)
            
            if not self.config_manager.is_twice_nat44_fallback_enabled():
                print("    ✅ 回退机制正确禁用")
            else:
                print("    ❌ 回退机制禁用失败")
                return False
            
            # 恢复原始配置
            self.config_manager.set_twice_nat44_config('use_twice_nat44', original_enabled)
            self.config_manager.set_twice_nat44_config('twice_nat44_fallback', original_fallback)
            
            print("    ✅ 配置已恢复到原始状态")
            
            return True
            
        except Exception as e:
            print(f"    ❌ 回退机制测试异常: {str(e)}")
            return False
    
    def test_memory_leaks(self) -> bool:
        """测试内存泄漏"""
        try:
            print("  🔍 测试内存泄漏...")
            
            import psutil
            process = psutil.Process()
            
            # 多轮创建和销毁对象
            rounds = 10
            objects_per_round = 1000
            memory_samples = []
            
            for round_num in range(rounds):
                # 记录轮次开始内存
                gc.collect()
                start_memory = process.memory_info().rss / 1024 / 1024
                
                # 创建大量对象
                temp_objects = []
                for i in range(objects_per_round):
                    rule = TwiceNat44Rule(
                        name=f"LEAK_TEST_{round_num}_{i}",
                        enabled=True,
                        description=f"内存泄漏测试 {round_num}-{i}",
                        match_conditions=TwiceNat44MatchConditions(dest_network=f"VIP_{i}"),
                        snat_config=TwiceNat44SnatConfig(address_type=TwiceNat44AddressType.INTERFACE),
                        dnat_config=TwiceNat44DnatConfig(ipv4_address=f"192.168.{i//256}.{i%256+1}")
                    )
                    temp_objects.append(rule)
                
                # 销毁对象
                del temp_objects
                gc.collect()
                
                # 记录轮次结束内存
                end_memory = process.memory_info().rss / 1024 / 1024
                memory_samples.append(end_memory - start_memory)
                
                print(f"    📊 轮次{round_num+1}: 内存变化{end_memory - start_memory:+.1f}MB")
            
            # 分析内存趋势
            avg_memory_change = sum(memory_samples) / len(memory_samples)
            max_memory_change = max(memory_samples)
            
            print(f"    📊 平均内存变化: {avg_memory_change:+.1f}MB")
            print(f"    📊 最大内存变化: {max_memory_change:+.1f}MB")
            
            # 判断是否存在内存泄漏
            if avg_memory_change > 5:  # 5MB阈值
                print(f"    ⚠️ 可能存在内存泄漏: 平均增长{avg_memory_change:.1f}MB")
            else:
                print(f"    ✅ 内存使用正常: 平均变化{avg_memory_change:.1f}MB")
            
            return True
            
        except Exception as e:
            print(f"    ❌ 内存泄漏测试异常: {str(e)}")
            return False
    
    def test_long_running_stability(self) -> bool:
        """测试长时间运行稳定性"""
        try:
            print("  ⏱️ 测试长时间运行稳定性...")
            
            # 模拟长时间运行场景
            duration = 30  # 30秒测试
            interval = 1   # 1秒间隔
            
            print(f"    🕐 开始{duration}秒稳定性测试...")
            
            start_time = time.time()
            iteration = 0
            errors = []
            
            while time.time() - start_time < duration:
                try:
                    # 创建规则
                    policy = {
                        "name": f"STABILITY_POLICY_{iteration}",
                        "status": "enable",
                        "service": ["HTTP"],
                        "fixedport": "disable"
                    }
                    
                    vip = {
                        "name": f"STABILITY_VIP_{iteration}",
                        "mappedip": f"10.0.{(iteration//256)%256}.{iteration%256+1}",
                        "mappedport": str(8000 + (iteration % 1000))
                    }
                    
                    rule = TwiceNat44Rule.from_fortigate_policy(policy, vip)
                    rule_dict = rule.to_nat_rule_dict()
                    
                    # 生成XML
                    rule_element = self.nat_generator._create_nat_rule_element(rule_dict)
                    
                    # 验证
                    is_valid, validation_errors = self.validator.validate_twice_nat44_rule(rule_element)
                    
                    if not is_valid:
                        errors.append(f"迭代{iteration}: 验证失败 - {validation_errors}")
                    
                    iteration += 1
                    
                    if iteration % 10 == 0:
                        print(f"    📊 已完成{iteration}次迭代")
                    
                    time.sleep(interval)
                    
                except Exception as e:
                    errors.append(f"迭代{iteration}: 异常 - {str(e)}")
                    iteration += 1
            
            elapsed_time = time.time() - start_time
            
            print(f"    ✅ 稳定性测试完成")
            print(f"    📊 运行时间: {elapsed_time:.1f}秒")
            print(f"    📊 总迭代数: {iteration}")
            print(f"    📊 平均速度: {iteration/elapsed_time:.1f}次/秒")
            print(f"    📊 错误数量: {len(errors)}")
            
            if len(errors) > 0:
                print("    ⚠️ 发现错误:")
                for error in errors[:5]:  # 只显示前5个错误
                    print(f"      {error}")
                if len(errors) > 5:
                    print(f"      ... 还有{len(errors)-5}个错误")
            
            # 错误率阈值检查
            error_rate = len(errors) / iteration * 100
            if error_rate > 5:  # 5%错误率阈值
                print(f"    ❌ 错误率过高: {error_rate:.1f}%")
                return False
            else:
                print(f"    ✅ 错误率正常: {error_rate:.1f}%")
            
            return True
            
        except Exception as e:
            print(f"    ❌ 长时间运行稳定性测试异常: {str(e)}")
            return False
    
    def test_special_characters(self) -> bool:
        """测试特殊字符处理"""
        try:
            print("  🔤 测试特殊字符处理...")
            
            # 特殊字符测试用例
            special_chars_tests = [
                {"name": "test_中文_rule", "desc": "包含中文字符的规则"},
                {"name": "test-dash-rule", "desc": "包含连字符的规则"},
                {"name": "test_underscore_rule", "desc": "包含下划线的规则"},
                {"name": "test.dot.rule", "desc": "包含点号的规则"},
                {"name": "test@symbol@rule", "desc": "包含@符号的规则"},
                {"name": "test space rule", "desc": "包含空格的规则"},
                {"name": "test'quote'rule", "desc": "包含单引号的规则"},
                {"name": 'test"double"rule', "desc": "包含双引号的规则"},
            ]
            
            for i, test_case in enumerate(special_chars_tests):
                try:
                    policy = {
                        "name": test_case["name"],
                        "status": "enable",
                        "service": ["HTTP"],
                        "fixedport": "disable"
                    }
                    
                    vip = {
                        "name": f"SPECIAL_VIP_{i}",
                        "mappedip": "*************",
                        "mappedport": "8080"
                    }
                    
                    rule = TwiceNat44Rule.from_fortigate_policy(policy, vip)
                    rule_dict = rule.to_nat_rule_dict()
                    
                    # 生成XML并验证
                    rule_element = self.nat_generator._create_nat_rule_element(rule_dict)
                    is_valid, errors = self.validator.validate_twice_nat44_rule(rule_element)
                    
                    if is_valid:
                        print(f"    ✅ 特殊字符处理成功: {test_case['name']}")
                    else:
                        print(f"    ⚠️ 特殊字符验证警告: {test_case['name']} - {errors}")
                        
                except Exception as e:
                    print(f"    ❌ 特殊字符处理失败: {test_case['name']} - {str(e)}")
                    return False
            
            # 测试XML特殊字符转义
            xml_special_chars = [
                {"name": "test<xml>rule", "desc": "包含XML标签字符"},
                {"name": "test&amp;rule", "desc": "包含XML实体字符"},
            ]
            
            for test_case in xml_special_chars:
                try:
                    match_conditions = TwiceNat44MatchConditions(dest_network="XML_TEST_VIP")
                    snat_config = TwiceNat44SnatConfig(address_type=TwiceNat44AddressType.INTERFACE)
                    dnat_config = TwiceNat44DnatConfig(ipv4_address="*************")
                    
                    rule = TwiceNat44Rule(
                        name=test_case["name"],
                        enabled=True,
                        description=test_case["desc"],
                        match_conditions=match_conditions,
                        snat_config=snat_config,
                        dnat_config=dnat_config
                    )
                    
                    rule_dict = rule.to_nat_rule_dict()
                    rule_element = self.nat_generator._create_nat_rule_element(rule_dict)
                    
                    # 验证XML序列化
                    from lxml import etree
                    xml_string = etree.tostring(rule_element, encoding='unicode')
                    
                    print(f"    ✅ XML特殊字符处理成功: {test_case['name']}")
                    
                except Exception as e:
                    print(f"    ❌ XML特殊字符处理失败: {test_case['name']} - {str(e)}")
                    return False
            
            return True
            
        except Exception as e:
            print(f"    ❌ 特殊字符处理测试异常: {str(e)}")
            return False
    
    def _print_summary(self):
        """打印测试总结"""
        print("\n" + "=" * 80)
        print("📊 边界场景测试总结")
        print("=" * 80)
        
        print(f"总测试数: {self.test_results['total']}")
        print(f"通过测试: {self.test_results['passed']}")
        print(f"失败测试: {self.test_results['failed']}")
        print(f"成功率: {self.test_results['passed']/self.test_results['total']*100:.1f}%")
        
        print("\n详细结果:")
        for detail in self.test_results["details"]:
            status_icon = "✅" if detail["status"] == "PASSED" else "❌"
            print(f"  {status_icon} {detail['name']}")
            if "error" in detail:
                print(f"    错误: {detail['error']}")


def main():
    """主函数"""
    test_suite = EdgeCasesTestSuite()
    success = test_suite.run_all_edge_cases_tests()
    
    if success:
        print("\n🎉 所有边界场景测试通过！")
        return True
    else:
        print("\n❌ 部分边界场景测试失败！")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
