#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
容量校验功能集成测试
"""

import unittest
import sys
import os
import json
import tempfile
from unittest.mock import patch

# 添加engine目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'engine'))

from engine.main import verify_config_file
from engine.verify import perform_capacity_validation, load_capacity_limits


class TestCapacityValidationIntegration(unittest.TestCase):
    """容量校验集成测试"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建临时配置文件
        self.test_config_content = """
config system dns
    set primary 8.8.8.8
    set secondary 8.8.4.4
    set server "1.1.1.1" "9.9.9.9" "208.67.222.222"
end

config system interface
    edit "port1.100"
        set vlanid 100
    next
    edit "port1.200"
        set vlanid 200
    next
    edit "port1.300"
        set vlanid 300
    next
    edit "pppoe1"
        set type pppoe
    next
    edit "pppoe2"
        set type pppoe
    next
end

config router static
    edit 1
        set dst 192.168.1.0/24
        set gateway 10.0.0.1
    next
    edit 2
        set dst 192.168.2.0/24
        set gateway 10.0.0.1
    next
    edit 3
        set dst 192.168.3.0/24
        set gateway 10.0.0.1
    next
end

config firewall policy
    edit 1
        set srcintf "port1"
        set dstintf "port2"
        set srcaddr "all"
        set dstaddr "all"
        set service "ALL"
        set action accept
        set nat enable
    next
    edit 2
        set srcintf "port2"
        set dstintf "port1"
        set srcaddr "all"
        set dstaddr "all"
        set service "ALL"
        set action accept
        set nat enable
    next
    edit 3
        set srcintf "port3"
        set dstintf "port1"
        set srcaddr "all"
        set dstaddr "all"
        set service "ALL"
        set action accept
    next
end

config firewall address
    edit "host1"
        set subnet ************/32
    next
    edit "host2"
        set subnet ************/32
    next
    edit "host3"
        set subnet ************/32
    next
    edit "host4"
        set subnet ************/32
    next
    edit "host5"
        set subnet ************/32
    next
end

config firewall addrgrp
    edit "group1"
        set member "host1" "host2"
    next
    edit "group2"
        set member "host3" "host4" "host5"
    next
end

config firewall service custom
    edit "service1"
        set tcp-portrange 80
    next
    edit "service2"
        set tcp-portrange 443
    next
    edit "service3"
        set tcp-portrange 8080
    next
    edit "service4"
        set tcp-portrange 8443
    next
end

config firewall service group
    edit "web_services"
        set member "service1" "service2"
    next
    edit "proxy_services"
        set member "service3" "service4"
    next
end

config firewall schedule recurring
    edit "work_hours"
        set day monday tuesday wednesday thursday friday
        set start 09:00
        set end 17:00
    next
    edit "weekend"
        set day saturday sunday
        set start 10:00
        set end 18:00
    next
end
"""
        
        # 创建临时文件
        self.temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.conf', delete=False)
        self.temp_file.write(self.test_config_content)
        self.temp_file.close()
    
    def tearDown(self):
        """清理测试环境"""
        if os.path.exists(self.temp_file.name):
            os.unlink(self.temp_file.name)
    
    def test_verify_config_file_with_capacity_validation_z3200s(self):
        """测试verify_config_file函数集成容量校验 - Z3200S"""
        try:
            result = verify_config_file(self.temp_file.name, "fortigate", device_model="z3200s")
            
            # 验证基本结构
            self.assertIsInstance(result, dict)
            self.assertIn("valid", result)
            self.assertIn("success", result)
            
            # 应该有容量警告（DNS服务器超限：5 > 3）
            if "warnings" in result and result["warnings"]:
                capacity_warnings = [w for w in result["warnings"] if "容量" in w or "capacity" in w.lower()]
                self.assertTrue(len(capacity_warnings) > 0, "应该有容量相关的警告")
            
        except Exception as e:
            self.fail(f"verify_config_file with capacity validation failed: {e}")
    
    def test_verify_config_file_with_capacity_validation_z5100s(self):
        """测试verify_config_file函数集成容量校验 - Z5100S"""
        try:
            result = verify_config_file(self.temp_file.name, "fortigate", device_model="z5100s")
            
            # 验证基本结构
            self.assertIsInstance(result, dict)
            self.assertIn("valid", result)
            self.assertIn("success", result)
            
            # Z5100S的限制更高，可能没有违规或违规更少
            print(f"Z5100S validation result: {result}")
            
        except Exception as e:
            self.fail(f"verify_config_file with Z5100S capacity validation failed: {e}")
    
    def test_verify_config_file_without_device_model(self):
        """测试不指定设备型号的验证"""
        try:
            result = verify_config_file(self.temp_file.name, "fortigate")
            
            # 验证基本结构
            self.assertIsInstance(result, dict)
            self.assertIn("valid", result)
            self.assertIn("success", result)
            
            # 不应该有容量相关的警告
            if "warnings" in result and result["warnings"]:
                capacity_warnings = [w for w in result["warnings"] if "容量" in w or "capacity" in w.lower()]
                self.assertEqual(len(capacity_warnings), 0, "不指定设备型号时不应该有容量警告")
            
        except Exception as e:
            self.fail(f"verify_config_file without device model failed: {e}")
    
    def test_capacity_validation_with_real_config_file(self):
        """测试使用真实配置文件的容量校验"""
        try:
            # 直接调用容量校验函数
            violations = perform_capacity_validation(self.test_config_content, "z3200s")
            
            # 验证结果
            self.assertIsInstance(violations, list)
            
            # 应该至少有DNS服务器违规（5个DNS服务器 > 3个限制）
            dns_violations = [v for v in violations if v.resource_type == "dns_servers"]
            self.assertTrue(len(dns_violations) > 0, "应该有DNS服务器容量违规")
            
            if dns_violations:
                dns_violation = dns_violations[0]
                self.assertEqual(dns_violation.current_count, 5)
                self.assertEqual(dns_violation.max_limit, 3)
                self.assertEqual(dns_violation.violation_count, 2)
                self.assertEqual(dns_violation.severity, "error")
                self.assertEqual(dns_violation.category, "network")
                self.assertEqual(dns_violation.device_model, "z3200s")
            
        except Exception as e:
            self.fail(f"Direct capacity validation failed: {e}")
    
    def test_load_capacity_limits_integration(self):
        """测试容量限制配置加载"""
        try:
            limits = load_capacity_limits()
            
            # 验证配置结构
            self.assertIsInstance(limits, dict)
            self.assertIn("device_models", limits)
            self.assertIn("z3200s", limits["device_models"])
            self.assertIn("z5100s", limits["device_models"])
            
            # 验证Z3200S配置
            z3200s_limits = limits["device_models"]["z3200s"]["limits"]
            self.assertIn("dns_servers", z3200s_limits)
            self.assertIn("security_policies", z3200s_limits)
            self.assertIn("address_objects", z3200s_limits)
            
            # 验证限制值
            self.assertEqual(z3200s_limits["dns_servers"]["max"], 3)
            self.assertEqual(z3200s_limits["security_policies"]["max"], 3000)
            
            # 验证Z5100S配置
            z5100s_limits = limits["device_models"]["z5100s"]["limits"]
            self.assertEqual(z5100s_limits["security_policies"]["max"], 4000)  # 应该比Z3200S更高
            
        except Exception as e:
            self.fail(f"Load capacity limits failed: {e}")
    
    def test_end_to_end_capacity_validation_workflow(self):
        """测试端到端容量校验工作流"""
        try:
            # 1. 加载配置限制
            limits = load_capacity_limits()
            self.assertIsNotNone(limits)
            
            # 2. 执行容量校验
            violations = perform_capacity_validation(self.test_config_content, "z3200s")
            
            # 3. 验证违规结果
            self.assertIsInstance(violations, list)
            
            # 4. 检查具体违规项
            violation_types = [v.resource_type for v in violations]
            self.assertIn("dns_servers", violation_types, "应该检测到DNS服务器超限")
            
            # 5. 验证违规详细信息
            for violation in violations:
                self.assertIsInstance(violation.analysis, dict)
                self.assertIn("usage_percentage", violation.analysis)
                self.assertIn("recommended_action", violation.analysis)
                self.assertIn("optimization_tips", violation.analysis)
                self.assertTrue(len(violation.analysis["optimization_tips"]) > 0)
            
            print(f"End-to-end test completed successfully. Found {len(violations)} violations.")
            
        except Exception as e:
            self.fail(f"End-to-end capacity validation workflow failed: {e}")


if __name__ == '__main__':
    unittest.main()
