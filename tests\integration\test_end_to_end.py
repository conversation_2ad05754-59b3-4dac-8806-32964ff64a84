"""
FortiGate twice-nat44端到端集成测试

验证从FortiGate配置到NTOS XML的完整转换流程，包括：
- 完整的数据流验证
- 复杂场景处理
- 性能基准测试
- 兼容性验证
"""

import sys
import os
import time
import tempfile
from typing import List, Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from lxml import etree
from engine.business.models.twice_nat44_models import TwiceNat44Rule
from engine.generators.nat_generator import NATGenerator
from engine.validators.twice_nat44_validator import TwiceNat44Validator
from engine.infrastructure.config.config_manager import ConfigManager


class EndToEndTestSuite:
    """端到端测试套件"""
    
    def __init__(self):
        """初始化测试套件"""
        self.nat_generator = NATGenerator()
        self.validator = TwiceNat44Validator()
        self.config_manager = ConfigManager()
        
        # 测试统计
        self.test_results = {
            "total_tests": 0,
            "passed_tests": 0,
            "failed_tests": 0,
            "test_details": []
        }
    
    def run_all_tests(self) -> bool:
        """运行所有端到端测试"""
        print("🚀 开始twice-nat44端到端集成测试")
        print("=" * 80)
        
        tests = [
            ("基本转换流程测试", self.test_basic_conversion_flow),
            ("复杂场景处理测试", self.test_complex_scenarios),
            ("批量处理测试", self.test_batch_processing),
            ("错误处理测试", self.test_error_handling),
            ("配置兼容性测试", self.test_configuration_compatibility),
            ("XML格式验证测试", self.test_xml_format_validation),
            ("性能基准测试", self.test_performance_benchmark)
        ]
        
        for test_name, test_func in tests:
            self._run_single_test(test_name, test_func)
        
        self._print_summary()
        return self.test_results["failed_tests"] == 0
    
    def _run_single_test(self, test_name: str, test_func):
        """运行单个测试"""
        print(f"\n🧪 {test_name}")
        print("-" * 60)
        
        self.test_results["total_tests"] += 1
        
        try:
            start_time = time.time()
            result = test_func()
            end_time = time.time()
            
            if result:
                self.test_results["passed_tests"] += 1
                print(f"✅ {test_name} 通过 (耗时: {end_time - start_time:.3f}秒)")
                self.test_results["test_details"].append({
                    "name": test_name,
                    "status": "PASSED",
                    "duration": end_time - start_time
                })
            else:
                self.test_results["failed_tests"] += 1
                print(f"❌ {test_name} 失败")
                self.test_results["test_details"].append({
                    "name": test_name,
                    "status": "FAILED",
                    "duration": end_time - start_time
                })
                
        except Exception as e:
            self.test_results["failed_tests"] += 1
            print(f"❌ {test_name} 异常: {str(e)}")
            self.test_results["test_details"].append({
                "name": test_name,
                "status": "ERROR",
                "error": str(e)
            })
    
    def test_basic_conversion_flow(self) -> bool:
        """测试基本转换流程"""
        try:
            # 步骤1：准备FortiGate配置
            fortigate_policy = {
                "name": "WEB_ACCESS_POLICY",
                "status": "enable",
                "service": ["HTTPS"],
                "schedule": "always",
                "fixedport": "disable",
                "nat": "enable",
                "dstaddr": ["WEB_SERVER_VIP"]
            }
            
            vip_config = {
                "name": "WEB_SERVER_VIP",
                "extip": "************",
                "mappedip": "*************",
                "extport": "443",
                "mappedport": "8443",
                "protocol": "tcp"
            }
            
            print(f"  📋 FortiGate配置准备完成")
            print(f"    策略: {fortigate_policy['name']}")
            print(f"    VIP: {vip_config['name']} ({vip_config['extip']} -> {vip_config['mappedip']})")
            
            # 步骤2：数据模型转换
            rule = TwiceNat44Rule.from_fortigate_policy(fortigate_policy, vip_config)
            rule_dict = rule.to_nat_rule_dict()
            
            print(f"  ✅ 数据模型转换完成: {rule_dict['name']}")
            
            # 步骤3：XML生成
            rule_element = self.nat_generator._create_nat_rule_element(rule_dict)
            
            print(f"  ✅ XML生成完成")
            
            # 步骤4：验证
            is_valid, errors = self.validator.validate_twice_nat44_rule(rule_element)
            
            print(f"  ✅ XML验证完成: {is_valid}")
            
            if not is_valid:
                print(f"    验证错误: {errors}")
                return False
            
            # 步骤5：内容验证
            name_elem = rule_element.find("name")
            if name_elem is None or "WEB_ACCESS_POLICY" not in name_elem.text:
                print("    ❌ 规则名称验证失败")
                return False
            
            twice_nat_elem = rule_element.find("twice-nat44")
            if twice_nat_elem is None:
                print("    ❌ twice-nat44配置缺失")
                return False
            
            dnat_ip_elem = twice_nat_elem.find(".//dnat/ipv4-address")
            if dnat_ip_elem is None or dnat_ip_elem.text != "*************":
                print("    ❌ DNAT IP地址验证失败")
                return False
            
            dnat_port_elem = twice_nat_elem.find(".//dnat/port")
            if dnat_port_elem is None or dnat_port_elem.text != "8443":
                print("    ❌ DNAT端口验证失败")
                return False
            
            print(f"  ✅ 内容验证通过")
            return True
            
        except Exception as e:
            print(f"  ❌ 基本转换流程测试异常: {str(e)}")
            return False
    
    def test_complex_scenarios(self) -> bool:
        """测试复杂场景处理"""
        try:
            scenarios = [
                {
                    "name": "fixedport启用场景",
                    "policy": {
                        "name": "FIXEDPORT_POLICY",
                        "status": "enable",
                        "service": ["HTTP"],
                        "fixedport": "enable"
                    },
                    "vip": {
                        "name": "FIXEDPORT_VIP",
                        "mappedip": "********00",
                        "mappedport": "80"
                    },
                    "expected_no_pat": True
                },
                {
                    "name": "多服务场景",
                    "policy": {
                        "name": "MULTI_SERVICE_POLICY",
                        "status": "enable",
                        "service": ["HTTP", "HTTPS", "FTP"]
                    },
                    "vip": {
                        "name": "MULTI_SERVICE_VIP",
                        "mappedip": "**********"
                    },
                    "expected_service": "HTTP"  # 应该使用第一个服务
                },
                {
                    "name": "禁用状态场景",
                    "policy": {
                        "name": "DISABLED_POLICY",
                        "status": "disable",
                        "service": ["HTTP"]
                    },
                    "vip": {
                        "name": "DISABLED_VIP",
                        "mappedip": "10.0.0.300"
                    },
                    "expected_enabled": False
                }
            ]
            
            for scenario in scenarios:
                print(f"    🔍 测试{scenario['name']}")
                
                # 转换
                rule = TwiceNat44Rule.from_fortigate_policy(scenario["policy"], scenario["vip"])
                rule_dict = rule.to_nat_rule_dict()
                
                # 生成XML
                rule_element = self.nat_generator._create_nat_rule_element(rule_dict)
                
                # 验证
                is_valid, errors = self.validator.validate_twice_nat44_rule(rule_element)
                if not is_valid:
                    print(f"      ❌ {scenario['name']}验证失败: {errors}")
                    return False
                
                # 特定验证
                if "expected_no_pat" in scenario:
                    no_pat_elem = rule_element.find(".//snat/no-pat")
                    expected = "true" if scenario["expected_no_pat"] else "false"
                    if no_pat_elem is None or no_pat_elem.text != expected:
                        print(f"      ❌ {scenario['name']} no-pat验证失败")
                        return False
                
                if "expected_service" in scenario:
                    service_elem = rule_element.find(".//match/service/name")
                    if service_elem is None or service_elem.text != scenario["expected_service"]:
                        print(f"      ❌ {scenario['name']} 服务验证失败")
                        return False
                
                if "expected_enabled" in scenario:
                    rule_en_elem = rule_element.find("rule_en")
                    expected = "true" if scenario["expected_enabled"] else "false"
                    if rule_en_elem is None or rule_en_elem.text != expected:
                        print(f"      ❌ {scenario['name']} 启用状态验证失败")
                        return False
                
                print(f"      ✅ {scenario['name']} 通过")
            
            return True
            
        except Exception as e:
            print(f"  ❌ 复杂场景处理测试异常: {str(e)}")
            return False
    
    def test_batch_processing(self) -> bool:
        """测试批量处理"""
        try:
            # 创建多个规则
            rules = []
            num_rules = 50
            
            print(f"    📦 创建{num_rules}个规则进行批量测试")
            
            for i in range(num_rules):
                policy = {
                    "name": f"BATCH_POLICY_{i+1}",
                    "status": "enable",
                    "service": ["HTTP"],
                    "fixedport": "disable"
                }
                
                vip = {
                    "name": f"BATCH_VIP_{i+1}",
                    "mappedip": f"10.0.{(i//254)+1}.{(i%254)+1}",
                    "mappedport": str(8000 + i)
                }
                
                rule = TwiceNat44Rule.from_fortigate_policy(policy, vip)
                rule_dict = rule.to_nat_rule_dict()
                rules.append(rule_dict)
            
            print(f"    ✅ 规则创建完成")
            
            # 批量生成XML
            start_time = time.time()
            nat_xml = self.nat_generator.generate_nat_xml(rules)
            xml_generation_time = time.time() - start_time
            
            print(f"    ✅ XML批量生成完成 (耗时: {xml_generation_time:.3f}秒)")
            
            # 验证XML结构
            if nat_xml is None or nat_xml.tag != "nat":
                print("    ❌ NAT XML结构验证失败")
                return False
            
            rule_elements = nat_xml.findall("rule")
            if len(rule_elements) != num_rules:
                print(f"    ❌ 规则数量验证失败: 期望{num_rules}, 实际{len(rule_elements)}")
                return False
            
            # 批量验证
            start_time = time.time()
            valid_count = 0
            for rule_element in rule_elements:
                is_valid, _ = self.validator.validate_twice_nat44_rule(rule_element)
                if is_valid:
                    valid_count += 1
            
            validation_time = time.time() - start_time
            
            print(f"    ✅ 批量验证完成 (耗时: {validation_time:.3f}秒)")
            print(f"    📊 验证通过率: {valid_count}/{len(rule_elements)} ({valid_count/len(rule_elements)*100:.1f}%)")
            
            if valid_count != len(rule_elements):
                print("    ❌ 部分规则验证失败")
                return False
            
            # 性能统计
            avg_xml_time = xml_generation_time / num_rules * 1000  # 毫秒
            avg_validation_time = validation_time / num_rules * 1000  # 毫秒
            
            print(f"    📈 性能统计:")
            print(f"      平均XML生成时间: {avg_xml_time:.2f}毫秒/规则")
            print(f"      平均验证时间: {avg_validation_time:.2f}毫秒/规则")
            
            return True
            
        except Exception as e:
            print(f"  ❌ 批量处理测试异常: {str(e)}")
            return False
    
    def test_error_handling(self) -> bool:
        """测试错误处理"""
        try:
            error_scenarios = [
                {
                    "name": "缺少mappedip",
                    "policy": {"name": "ERROR_POLICY_1"},
                    "vip": {"name": "ERROR_VIP_1"},
                    "should_raise": True
                },
                {
                    "name": "无效mappedip",
                    "policy": {"name": "ERROR_POLICY_2"},
                    "vip": {"name": "ERROR_VIP_2", "mappedip": "invalid.ip"},
                    "should_raise": True
                },
                {
                    "name": "无效端口",
                    "policy": {"name": "ERROR_POLICY_3"},
                    "vip": {"name": "ERROR_VIP_3", "mappedip": "********", "mappedport": "invalid_port"},
                    "should_raise": False  # 应该忽略无效端口
                }
            ]
            
            for scenario in error_scenarios:
                print(f"    🔍 测试{scenario['name']}")
                
                try:
                    rule = TwiceNat44Rule.from_fortigate_policy(scenario["policy"], scenario["vip"])
                    
                    if scenario["should_raise"]:
                        print(f"      ❌ {scenario['name']} 应该抛出异常但没有")
                        return False
                    else:
                        print(f"      ✅ {scenario['name']} 正确处理")
                        
                except Exception as e:
                    if scenario["should_raise"]:
                        print(f"      ✅ {scenario['name']} 正确抛出异常: {type(e).__name__}")
                    else:
                        print(f"      ❌ {scenario['name']} 不应该抛出异常: {str(e)}")
                        return False
            
            return True
            
        except Exception as e:
            print(f"  ❌ 错误处理测试异常: {str(e)}")
            return False
    
    def test_configuration_compatibility(self) -> bool:
        """测试配置兼容性"""
        try:
            # 测试配置读取
            use_twice_nat44 = self.config_manager.is_twice_nat44_enabled()
            fallback_enabled = self.config_manager.is_twice_nat44_fallback_enabled()
            
            print(f"    📋 当前配置:")
            print(f"      use_twice_nat44: {use_twice_nat44}")
            print(f"      twice_nat44_fallback: {fallback_enabled}")
            
            # 测试配置修改
            original_debug = self.config_manager.get_twice_nat44_config('twice_nat44_debug')
            self.config_manager.set_twice_nat44_config('twice_nat44_debug', True)
            
            new_debug = self.config_manager.get_twice_nat44_config('twice_nat44_debug')
            if new_debug != True:
                print("    ❌ 配置修改失败")
                return False
            
            # 恢复原始配置
            self.config_manager.set_twice_nat44_config('twice_nat44_debug', original_debug)
            
            # 测试配置验证
            is_valid = self.config_manager.validate_twice_nat44_config()
            if not is_valid:
                print("    ❌ 配置验证失败")
                return False
            
            print(f"    ✅ 配置兼容性验证通过")
            return True
            
        except Exception as e:
            print(f"  ❌ 配置兼容性测试异常: {str(e)}")
            return False
    
    def test_xml_format_validation(self) -> bool:
        """测试XML格式验证"""
        try:
            # 创建测试规则
            policy = {
                "name": "XML_FORMAT_POLICY",
                "status": "enable",
                "service": ["HTTPS"],
                "fixedport": "disable"
            }
            
            vip = {
                "name": "XML_FORMAT_VIP",
                "mappedip": "***************",
                "mappedport": "9443"
            }
            
            # 生成规则
            rule = TwiceNat44Rule.from_fortigate_policy(policy, vip)
            rule_dict = rule.to_nat_rule_dict()
            rule_element = self.nat_generator._create_nat_rule_element(rule_dict)
            
            # XML序列化测试
            xml_string = etree.tostring(rule_element, encoding='unicode', pretty_print=True)
            print(f"    📄 XML序列化成功 (长度: {len(xml_string)})")
            
            # XML重新解析测试
            reparsed_element = etree.fromstring(xml_string.encode('utf-8'))
            print(f"    🔄 XML重新解析成功")
            
            # 验证重新解析的XML
            is_valid, errors = self.validator.validate_twice_nat44_rule(reparsed_element)
            if not is_valid:
                print(f"    ❌ 重新解析的XML验证失败: {errors}")
                return False
            
            # 内容一致性验证
            original_name = rule_element.find("name").text
            reparsed_name = reparsed_element.find("name").text
            if original_name != reparsed_name:
                print("    ❌ XML内容一致性验证失败")
                return False
            
            print(f"    ✅ XML格式验证通过")
            return True
            
        except Exception as e:
            print(f"  ❌ XML格式验证测试异常: {str(e)}")
            return False
    
    def test_performance_benchmark(self) -> bool:
        """测试性能基准"""
        try:
            # 性能测试参数
            test_sizes = [10, 50, 100]
            performance_results = []
            
            for size in test_sizes:
                print(f"    📊 测试{size}规则性能基准")
                
                # 创建规则
                rules = []
                start_time = time.time()
                
                for i in range(size):
                    policy = {
                        "name": f"PERF_POLICY_{i+1}",
                        "status": "enable",
                        "service": ["HTTP"],
                        "fixedport": "disable"
                    }
                    
                    vip = {
                        "name": f"PERF_VIP_{i+1}",
                        "mappedip": f"10.1.{(i//254)+1}.{(i%254)+1}",
                        "mappedport": str(9000 + i)
                    }
                    
                    rule = TwiceNat44Rule.from_fortigate_policy(policy, vip)
                    rule_dict = rule.to_nat_rule_dict()
                    rules.append(rule_dict)
                
                creation_time = time.time() - start_time
                
                # XML生成
                start_time = time.time()
                nat_xml = self.nat_generator.generate_nat_xml(rules)
                xml_time = time.time() - start_time
                
                # 验证
                start_time = time.time()
                rule_elements = nat_xml.findall("rule")
                valid_count = 0
                for rule_element in rule_elements:
                    is_valid, _ = self.validator.validate_twice_nat44_rule(rule_element)
                    if is_valid:
                        valid_count += 1
                
                validation_time = time.time() - start_time
                
                # 统计结果
                total_time = creation_time + xml_time + validation_time
                avg_time_per_rule = total_time / size * 1000  # 毫秒
                
                performance_results.append({
                    "size": size,
                    "creation_time": creation_time,
                    "xml_time": xml_time,
                    "validation_time": validation_time,
                    "total_time": total_time,
                    "avg_time_per_rule": avg_time_per_rule,
                    "valid_count": valid_count
                })
                
                print(f"      创建时间: {creation_time:.3f}秒")
                print(f"      XML生成: {xml_time:.3f}秒")
                print(f"      验证时间: {validation_time:.3f}秒")
                print(f"      总时间: {total_time:.3f}秒")
                print(f"      平均时间: {avg_time_per_rule:.2f}毫秒/规则")
                print(f"      验证通过: {valid_count}/{size}")
            
            # 性能基准验证
            for result in performance_results:
                if result["avg_time_per_rule"] > 10:  # 10毫秒/规则的基准
                    print(f"    ⚠️ {result['size']}规则性能基准未达标: {result['avg_time_per_rule']:.2f}毫秒/规则")
                
                if result["valid_count"] != result["size"]:
                    print(f"    ❌ {result['size']}规则验证失败")
                    return False
            
            print(f"    ✅ 性能基准测试通过")
            return True
            
        except Exception as e:
            print(f"  ❌ 性能基准测试异常: {str(e)}")
            return False
    
    def _print_summary(self):
        """打印测试总结"""
        print("\n" + "=" * 80)
        print("📊 端到端集成测试总结")
        print("=" * 80)
        
        print(f"总测试数: {self.test_results['total_tests']}")
        print(f"通过测试: {self.test_results['passed_tests']}")
        print(f"失败测试: {self.test_results['failed_tests']}")
        print(f"成功率: {self.test_results['passed_tests']/self.test_results['total_tests']*100:.1f}%")
        
        print("\n详细结果:")
        for detail in self.test_results["test_details"]:
            status_icon = "✅" if detail["status"] == "PASSED" else "❌"
            if "duration" in detail:
                print(f"  {status_icon} {detail['name']} ({detail['duration']:.3f}秒)")
            else:
                print(f"  {status_icon} {detail['name']}")
                if "error" in detail:
                    print(f"    错误: {detail['error']}")


def main():
    """主函数"""
    test_suite = EndToEndTestSuite()
    success = test_suite.run_all_tests()
    
    if success:
        print("\n🎉 所有端到端集成测试通过！")
        return True
    else:
        print("\n❌ 部分端到端集成测试失败！")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
