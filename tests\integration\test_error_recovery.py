"""
FortiGate twice-nat44错误恢复机制集成测试

测试错误恢复机制在实际场景中的有效性，包括：
- 配置错误的自动恢复
- 验证错误的处理和恢复
- 资源错误的恢复策略
- 网络错误的重试机制
- 复杂场景下的错误恢复
"""

import sys
import os
import time
import threading
from typing import Dict, List, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from engine.infrastructure.error_handling import (
    TwiceNat44ErrorHandler, TwiceNat44ErrorContext, TwiceNat44ErrorType,
    get_twice_nat44_error_handler, twice_nat44_operation_context
)
from engine.business.models.twice_nat44_models import (
    TwiceNat44Rule, TwiceNat44ConfigError, TwiceNat44ValidationError
)
from engine.generators.nat_generator import NATGenerator
from engine.validators.twice_nat44_validator import TwiceNat44Validator


class ErrorRecoveryIntegrationTestSuite:
    """错误恢复机制集成测试套件"""
    
    def __init__(self):
        """初始化测试套件"""
        self.error_handler = get_twice_nat44_error_handler()
        self.nat_generator = NATGenerator()
        self.validator = TwiceNat44Validator()
        
        # 测试结果统计
        self.test_results = {
            "total": 0,
            "passed": 0,
            "failed": 0,
            "recovered": 0,
            "details": []
        }
    
    def run_all_recovery_tests(self) -> bool:
        """运行所有错误恢复测试"""
        print("🚀 开始twice-nat44错误恢复机制集成测试")
        print("=" * 80)
        
        tests = [
            ("配置错误自动恢复测试", self.test_config_error_recovery),
            ("验证错误处理恢复测试", self.test_validation_error_recovery),
            ("资源错误恢复策略测试", self.test_resource_error_recovery),
            ("网络错误重试机制测试", self.test_network_error_recovery),
            ("复杂场景错误恢复测试", self.test_complex_scenario_recovery),
            ("批量处理错误恢复测试", self.test_batch_processing_recovery),
            ("并发错误恢复测试", self.test_concurrent_error_recovery),
            ("错误恢复性能测试", self.test_error_recovery_performance)
        ]
        
        for test_name, test_func in tests:
            self._run_single_test(test_name, test_func)
        
        self._print_summary()
        return self.test_results["failed"] == 0
    
    def _run_single_test(self, test_name: str, test_func):
        """运行单个测试"""
        print(f"\n🧪 {test_name}")
        print("-" * 60)
        
        self.test_results["total"] += 1
        
        try:
            result = test_func()
            if result:
                self.test_results["passed"] += 1
                print(f"✅ {test_name} 通过")
                self.test_results["details"].append({"name": test_name, "status": "PASSED"})
            else:
                self.test_results["failed"] += 1
                print(f"❌ {test_name} 失败")
                self.test_results["details"].append({"name": test_name, "status": "FAILED"})
                
        except Exception as e:
            self.test_results["failed"] += 1
            print(f"❌ {test_name} 异常: {str(e)}")
            self.test_results["details"].append({"name": test_name, "status": "ERROR", "error": str(e)})
    
    def test_config_error_recovery(self) -> bool:
        """测试配置错误自动恢复"""
        try:
            print("  🔧 测试配置错误自动恢复...")
            
            # 场景1：缺少VIP配置的恢复
            context = TwiceNat44ErrorContext(
                operation="create_twice_nat44_rule",
                policy_name="TEST_POLICY"
            )
            
            exception = TwiceNat44ConfigError("Missing VIP configuration")
            result = self.error_handler.handle_twice_nat44_error(exception, context)
            
            if result.get('recoverable', False):
                self.test_results["recovered"] += 1
                print(f"    ✅ VIP配置缺失恢复成功")
            else:
                print(f"    ⚠️ VIP配置缺失未能恢复")
            
            # 场景2：无效IP地址的恢复
            context = TwiceNat44ErrorContext(
                operation="create_twice_nat44_rule",
                policy_name="TEST_POLICY",
                vip_name="TEST_VIP"
            )
            
            exception = TwiceNat44ConfigError("Invalid IPv4 address: 999.999.999.999")
            result = self.error_handler.handle_twice_nat44_error(exception, context)
            
            if result.get('recoverable', False):
                self.test_results["recovered"] += 1
                print(f"    ✅ 无效IP地址恢复成功")
                
                # 验证恢复策略
                recovery_result = result.get('recovery_result', {})
                if recovery_result.get('strategy') == 'use_default_ip':
                    print(f"    📋 使用默认IP恢复策略: {recovery_result.get('details', {}).get('default_ip')}")
            else:
                print(f"    ⚠️ 无效IP地址未能恢复")
            
            # 场景3：端口配置错误的恢复
            try:
                # 模拟端口配置错误
                invalid_policy = {
                    "name": "PORT_ERROR_POLICY",
                    "status": "enable",
                    "service": ["HTTP"]
                }
                
                invalid_vip = {
                    "name": "PORT_ERROR_VIP",
                    "mappedip": "*************",
                    "mappedport": "invalid_port"  # 无效端口
                }
                
                rule = TwiceNat44Rule.from_fortigate_policy(invalid_policy, invalid_vip)
                
                # 如果没有抛出异常，说明错误被恢复了
                if rule.dnat_config.port is None:
                    print(f"    ✅ 无效端口恢复成功（忽略无效端口）")
                    self.test_results["recovered"] += 1
                
            except Exception as e:
                print(f"    ⚠️ 端口配置错误处理: {str(e)}")
            
            return True
            
        except Exception as e:
            print(f"    ❌ 配置错误恢复测试异常: {str(e)}")
            return False
    
    def test_validation_error_recovery(self) -> bool:
        """测试验证错误处理恢复"""
        try:
            print("  ✅ 测试验证错误处理恢复...")
            
            # 创建包含验证错误的XML
            invalid_xml = """<rule>
                <name>validation_error_rule</name>
                <rule_en>true</rule_en>
                <twice-nat44>
                    <match>
                        <dest-network><name>TEST_VIP</name></dest-network>
                    </match>
                    <!-- 缺少snat和dnat配置 -->
                </twice-nat44>
            </rule>"""
            
            from lxml import etree
            rule_element = etree.fromstring(invalid_xml.encode('utf-8'))
            
            # 使用验证器验证（应该失败）
            is_valid, errors = self.validator.validate_twice_nat44_rule(rule_element)
            
            if not is_valid and len(errors) > 0:
                print(f"    ✅ 验证错误正确检测: {len(errors)}个错误")
                
                # 测试验证错误的恢复
                context = TwiceNat44ErrorContext(
                    operation="validate_twice_nat44_rule",
                    rule_name="validation_error_rule"
                )
                
                validation_exception = TwiceNat44ValidationError(f"Validation failed: {errors}")
                result = self.error_handler.handle_twice_nat44_error(validation_exception, context)
                
                if result.get('recoverable', False):
                    self.test_results["recovered"] += 1
                    print(f"    ✅ 验证错误恢复成功")
                    
                    recovery_result = result.get('recovery_result', {})
                    if recovery_result.get('strategy') == 'sanitize_and_retry':
                        print(f"    📋 使用数据清理恢复策略")
                else:
                    print(f"    ⚠️ 验证错误未能恢复")
            else:
                print(f"    ❌ 验证错误检测失败")
                return False
            
            return True
            
        except Exception as e:
            print(f"    ❌ 验证错误恢复测试异常: {str(e)}")
            return False
    
    def test_resource_error_recovery(self) -> bool:
        """测试资源错误恢复策略"""
        try:
            print("  💾 测试资源错误恢复策略...")
            
            # 模拟内存错误
            context = TwiceNat44ErrorContext(
                operation="generate_large_xml",
                input_data={"rule_count": 10000}
            )
            
            memory_exception = MemoryError("Out of memory")
            result = self.error_handler.handle_twice_nat44_error(memory_exception, context)
            
            if result.get('recoverable', False):
                self.test_results["recovered"] += 1
                print(f"    ✅ 内存错误恢复成功")
                
                recovery_result = result.get('recovery_result', {})
                if recovery_result.get('strategy') == 'resource_cleanup_and_retry':
                    print(f"    📋 使用资源清理恢复策略")
                    print(f"    🧹 清理状态: {recovery_result.get('details', {}).get('cleanup_performed')}")
            else:
                print(f"    ⚠️ 内存错误未能恢复")
            
            # 测试实际的内存清理效果
            import gc
            import psutil
            
            process = psutil.Process()
            memory_before = process.memory_info().rss / 1024 / 1024
            
            # 创建大量对象
            large_objects = []
            for i in range(1000):
                large_objects.append(f"large_object_{i}" * 1000)
            
            memory_peak = process.memory_info().rss / 1024 / 1024
            
            # 清理对象
            del large_objects
            gc.collect()
            
            memory_after = process.memory_info().rss / 1024 / 1024
            
            print(f"    📊 内存使用: 初始{memory_before:.1f}MB -> 峰值{memory_peak:.1f}MB -> 清理后{memory_after:.1f}MB")
            
            if memory_after < memory_peak:
                print(f"    ✅ 内存清理有效，释放{memory_peak - memory_after:.1f}MB")
            
            return True
            
        except Exception as e:
            print(f"    ❌ 资源错误恢复测试异常: {str(e)}")
            return False
    
    def test_network_error_recovery(self) -> bool:
        """测试网络错误重试机制"""
        try:
            print("  🌐 测试网络错误重试机制...")
            
            # 模拟网络连接错误
            context = TwiceNat44ErrorContext(
                operation="network_operation",
                retry_count=0,
                max_retries=3
            )
            
            connection_exception = ConnectionError("Network connection failed")
            result = self.error_handler.handle_twice_nat44_error(connection_exception, context)
            
            if result.get('recoverable', False):
                self.test_results["recovered"] += 1
                print(f"    ✅ 网络错误恢复成功")
                
                recovery_result = result.get('recovery_result', {})
                if recovery_result.get('strategy') == 'exponential_backoff_retry':
                    wait_time = recovery_result.get('details', {}).get('wait_time', 0)
                    retry_count = recovery_result.get('details', {}).get('retry_count', 0)
                    print(f"    📋 使用指数退避重试策略: 等待{wait_time}秒, 重试次数{retry_count}")
            else:
                print(f"    ⚠️ 网络错误未能恢复")
            
            # 测试超时错误
            context.retry_count = 1
            timeout_exception = TimeoutError("Operation timeout")
            result = self.error_handler.handle_twice_nat44_error(timeout_exception, context)
            
            if result.get('recoverable', False):
                print(f"    ✅ 超时错误恢复成功")
                self.test_results["recovered"] += 1
            
            return True
            
        except Exception as e:
            print(f"    ❌ 网络错误恢复测试异常: {str(e)}")
            return False
    
    def test_complex_scenario_recovery(self) -> bool:
        """测试复杂场景错误恢复"""
        try:
            print("  🔄 测试复杂场景错误恢复...")
            
            # 场景：批量转换中的部分失败恢复
            policies = [
                {"name": "VALID_POLICY_1", "status": "enable", "service": ["HTTP"]},
                {"name": "INVALID_POLICY", "status": "enable"},  # 缺少service
                {"name": "VALID_POLICY_2", "status": "enable", "service": ["HTTPS"]},
            ]
            
            vips = [
                {"name": "VALID_VIP_1", "mappedip": "*************"},
                {"name": "INVALID_VIP", "mappedip": "999.999.999.999"},  # 无效IP
                {"name": "VALID_VIP_2", "mappedip": "*************"},
            ]
            
            successful_rules = []
            recovered_rules = []
            failed_rules = []
            
            for i, (policy, vip) in enumerate(zip(policies, vips)):
                try:
                    with twice_nat44_operation_context(
                        f"batch_conversion_item_{i}",
                        policy_name=policy["name"],
                        vip_name=vip["name"]
                    ) as context:
                        rule = TwiceNat44Rule.from_fortigate_policy(policy, vip)
                        successful_rules.append(rule)
                        print(f"    ✅ 规则{i+1}创建成功: {rule.name}")
                        
                except Exception as e:
                    # 尝试错误恢复
                    context = TwiceNat44ErrorContext(
                        operation=f"batch_conversion_item_{i}",
                        policy_name=policy["name"],
                        vip_name=vip["name"],
                        partial_result={"policy": policy, "vip": vip}
                    )
                    
                    result = self.error_handler.handle_twice_nat44_error(e, context)
                    
                    if result.get('recoverable', False):
                        recovered_rules.append(result)
                        print(f"    🔄 规则{i+1}恢复成功: {policy['name']}")
                        self.test_results["recovered"] += 1
                    else:
                        failed_rules.append({"policy": policy, "vip": vip, "error": str(e)})
                        print(f"    ❌ 规则{i+1}失败: {policy['name']} - {str(e)}")
            
            print(f"    📊 批量转换结果:")
            print(f"      成功: {len(successful_rules)}个")
            print(f"      恢复: {len(recovered_rules)}个")
            print(f"      失败: {len(failed_rules)}个")
            
            # 如果大部分规则成功或恢复，认为测试通过
            total_success = len(successful_rules) + len(recovered_rules)
            success_rate = total_success / len(policies) * 100
            
            if success_rate >= 66:  # 至少66%成功率
                print(f"    ✅ 复杂场景恢复成功，成功率: {success_rate:.1f}%")
                return True
            else:
                print(f"    ❌ 复杂场景恢复失败，成功率: {success_rate:.1f}%")
                return False
            
        except Exception as e:
            print(f"    ❌ 复杂场景恢复测试异常: {str(e)}")
            return False
    
    def test_batch_processing_recovery(self) -> bool:
        """测试批量处理错误恢复"""
        try:
            print("  📦 测试批量处理错误恢复...")
            
            # 创建大量规则，其中包含一些错误
            rule_count = 50
            rules = []
            
            for i in range(rule_count):
                if i % 10 == 7:  # 每10个规则中有1个错误
                    # 创建错误规则
                    rule = {
                        "name": f"ERROR_RULE_{i}",
                        "type": "twice-nat44",
                        "invalid_field": "invalid_value"  # 无效字段
                    }
                else:
                    # 创建正常规则
                    rule = {
                        "name": f"BATCH_RULE_{i}",
                        "type": "twice-nat44",
                        "rule_en": True,
                        "desc": f"批量规则 {i}",
                        "twice-nat44": {
                            "match": {
                                "dest-network": {"name": f"VIP_{i}"},
                                "service": {"name": "HTTP"}
                            },
                            "snat": {"output-address": {}},
                            "dnat": {"ipv4-address": f"192.168.{(i//256)+1}.{i%256+1}"}
                        }
                    }
                
                rules.append(rule)
            
            # 批量处理规则
            successful_count = 0
            recovered_count = 0
            failed_count = 0
            
            start_time = time.time()
            
            for i, rule in enumerate(rules):
                try:
                    # 尝试生成XML
                    rule_element = self.nat_generator._create_nat_rule_element(rule)
                    if rule_element is not None:
                        successful_count += 1
                    else:
                        # 尝试恢复
                        context = TwiceNat44ErrorContext(
                            operation="batch_xml_generation",
                            rule_name=rule.get("name", f"rule_{i}")
                        )
                        
                        exception = ValueError(f"Failed to create XML for rule {i}")
                        result = self.error_handler.handle_twice_nat44_error(exception, context)
                        
                        if result.get('recoverable', False):
                            recovered_count += 1
                        else:
                            failed_count += 1
                            
                except Exception as e:
                    # 错误处理
                    context = TwiceNat44ErrorContext(
                        operation="batch_xml_generation",
                        rule_name=rule.get("name", f"rule_{i}")
                    )
                    
                    result = self.error_handler.handle_twice_nat44_error(e, context)
                    
                    if result.get('recoverable', False):
                        recovered_count += 1
                    else:
                        failed_count += 1
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            print(f"    📊 批量处理结果:")
            print(f"      总规则数: {rule_count}")
            print(f"      成功处理: {successful_count}")
            print(f"      错误恢复: {recovered_count}")
            print(f"      处理失败: {failed_count}")
            print(f"      处理时间: {processing_time:.3f}秒")
            print(f"      平均速度: {rule_count/processing_time:.1f}规则/秒")
            
            # 更新恢复统计
            self.test_results["recovered"] += recovered_count
            
            # 如果大部分规则成功处理，认为测试通过
            success_rate = (successful_count + recovered_count) / rule_count * 100
            
            if success_rate >= 80:  # 至少80%成功率
                print(f"    ✅ 批量处理恢复成功，成功率: {success_rate:.1f}%")
                return True
            else:
                print(f"    ❌ 批量处理恢复失败，成功率: {success_rate:.1f}%")
                return False
            
        except Exception as e:
            print(f"    ❌ 批量处理恢复测试异常: {str(e)}")
            return False
    
    def test_concurrent_error_recovery(self) -> bool:
        """测试并发错误恢复"""
        try:
            print("  🔄 测试并发错误恢复...")
            
            def worker_function(worker_id: int, results: list):
                try:
                    # 每个工作线程处理一些规则，其中包含错误
                    worker_results = {"successful": 0, "recovered": 0, "failed": 0}
                    
                    for i in range(10):
                        try:
                            if i % 3 == 2:  # 每3个规则中有1个错误
                                raise ValueError(f"Worker {worker_id} rule {i} error")
                            
                            # 模拟正常处理
                            time.sleep(0.001)
                            worker_results["successful"] += 1
                            
                        except Exception as e:
                            # 错误恢复
                            context = TwiceNat44ErrorContext(
                                operation=f"concurrent_worker_{worker_id}",
                                rule_name=f"worker_{worker_id}_rule_{i}"
                            )
                            
                            result = self.error_handler.handle_twice_nat44_error(e, context)
                            
                            if result.get('recoverable', False):
                                worker_results["recovered"] += 1
                            else:
                                worker_results["failed"] += 1
                    
                    results.append(worker_results)
                    
                except Exception as e:
                    results.append({"error": str(e)})
            
            # 启动多个并发工作线程
            thread_count = 5
            threads = []
            results = []
            
            start_time = time.time()
            
            for i in range(thread_count):
                thread = threading.Thread(target=worker_function, args=(i, results))
                threads.append(thread)
                thread.start()
            
            # 等待所有线程完成
            for thread in threads:
                thread.join()
            
            end_time = time.time()
            concurrent_time = end_time - start_time
            
            # 统计结果
            total_successful = sum(r.get("successful", 0) for r in results if "error" not in r)
            total_recovered = sum(r.get("recovered", 0) for r in results if "error" not in r)
            total_failed = sum(r.get("failed", 0) for r in results if "error" not in r)
            error_threads = sum(1 for r in results if "error" in r)
            
            print(f"    📊 并发处理结果:")
            print(f"      工作线程: {thread_count}")
            print(f"      成功处理: {total_successful}")
            print(f"      错误恢复: {total_recovered}")
            print(f"      处理失败: {total_failed}")
            print(f"      线程错误: {error_threads}")
            print(f"      处理时间: {concurrent_time:.3f}秒")
            
            # 更新恢复统计
            self.test_results["recovered"] += total_recovered
            
            # 如果没有线程错误且大部分操作成功，认为测试通过
            if error_threads == 0 and (total_successful + total_recovered) > total_failed:
                print(f"    ✅ 并发错误恢复成功")
                return True
            else:
                print(f"    ❌ 并发错误恢复失败")
                return False
            
        except Exception as e:
            print(f"    ❌ 并发错误恢复测试异常: {str(e)}")
            return False
    
    def test_error_recovery_performance(self) -> bool:
        """测试错误恢复性能"""
        try:
            print("  ⚡ 测试错误恢复性能...")
            
            # 测试大量错误的恢复性能
            error_count = 100
            recovery_times = []
            
            for i in range(error_count):
                context = TwiceNat44ErrorContext(
                    operation=f"performance_test_{i}",
                    policy_name=f"PERF_POLICY_{i}"
                )
                
                exception = TwiceNat44ConfigError(f"Performance test error {i}")
                
                start_time = time.time()
                result = self.error_handler.handle_twice_nat44_error(exception, context)
                end_time = time.time()
                
                recovery_time = end_time - start_time
                recovery_times.append(recovery_time)
                
                if result.get('recoverable', False):
                    self.test_results["recovered"] += 1
            
            # 计算性能统计
            avg_recovery_time = sum(recovery_times) / len(recovery_times)
            max_recovery_time = max(recovery_times)
            min_recovery_time = min(recovery_times)
            
            print(f"    📊 错误恢复性能统计:")
            print(f"      错误数量: {error_count}")
            print(f"      平均恢复时间: {avg_recovery_time*1000:.2f}毫秒")
            print(f"      最大恢复时间: {max_recovery_time*1000:.2f}毫秒")
            print(f"      最小恢复时间: {min_recovery_time*1000:.2f}毫秒")
            print(f"      恢复速度: {error_count/sum(recovery_times):.1f}错误/秒")
            
            # 性能基准：平均恢复时间应该小于10毫秒
            if avg_recovery_time < 0.01:  # 10毫秒
                print(f"    ✅ 错误恢复性能达标")
                return True
            else:
                print(f"    ⚠️ 错误恢复性能未达标，平均时间: {avg_recovery_time*1000:.2f}毫秒")
                return False
            
        except Exception as e:
            print(f"    ❌ 错误恢复性能测试异常: {str(e)}")
            return False
    
    def _print_summary(self):
        """打印测试总结"""
        print("\n" + "=" * 80)
        print("📊 错误恢复机制集成测试总结")
        print("=" * 80)
        
        print(f"总测试数: {self.test_results['total']}")
        print(f"通过测试: {self.test_results['passed']}")
        print(f"失败测试: {self.test_results['failed']}")
        print(f"错误恢复: {self.test_results['recovered']}次")
        print(f"成功率: {self.test_results['passed']/self.test_results['total']*100:.1f}%")
        
        if self.test_results['recovered'] > 0:
            print(f"恢复率: {self.test_results['recovered']}次成功恢复")
        
        print("\n详细结果:")
        for detail in self.test_results["details"]:
            status_icon = "✅" if detail["status"] == "PASSED" else "❌"
            print(f"  {status_icon} {detail['name']}")
            if "error" in detail:
                print(f"    错误: {detail['error']}")


def main():
    """主函数"""
    test_suite = ErrorRecoveryIntegrationTestSuite()
    success = test_suite.run_all_recovery_tests()
    
    if success:
        print("\n🎉 所有错误恢复机制集成测试通过！")
        return True
    else:
        print("\n❌ 部分错误恢复机制集成测试失败！")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
