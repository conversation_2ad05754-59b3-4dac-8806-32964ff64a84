#!/usr/bin/env python3
"""
FortiGate twice-nat44性能基准测试

对比优化前后的性能指标，包括：
- 处理速度对比
- 内存使用对比
- 缓存效果验证
- 批量处理性能
- 并发处理能力
"""

import sys
import os
import time
import threading
import statistics
from typing import List, Dict, Any, Tuple
import psutil

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from engine.infrastructure.performance import get_twice_nat44_optimizer
from engine.business.models.twice_nat44_models import TwiceNat44Rule
from engine.generators.nat_generator import NATGenerator


class TwiceNat44BenchmarkSuite:
    """twice-nat44性能基准测试套件"""
    
    def __init__(self):
        """初始化基准测试套件"""
        self.optimizer = get_twice_nat44_optimizer()
        self.nat_generator = NATGenerator()
        
        # 测试数据
        self.test_rules = self._generate_test_rules(1000)
        self.small_batch = self.test_rules[:50]
        self.medium_batch = self.test_rules[:200]
        self.large_batch = self.test_rules[:500]
        
        # 基准结果
        self.benchmark_results = {}
    
    def _generate_test_rules(self, count: int) -> List[Dict[str, Any]]:
        """生成测试规则数据"""
        rules = []
        
        for i in range(count):
            policy = {
                "name": f"BENCHMARK_POLICY_{i}",
                "status": "enable",
                "service": ["HTTP", "HTTPS"][i % 2],
                "fixedport": ["enable", "disable"][i % 2]
            }
            
            vip = {
                "name": f"BENCHMARK_VIP_{i}",
                "mappedip": f"192.168.{(i//256)+1}.{i%256+1}",
                "mappedport": str(8000 + (i % 1000))
            }
            
            rules.append({"policy": policy, "vip": vip})
        
        return rules
    
    def run_all_benchmarks(self) -> Dict[str, Any]:
        """运行所有基准测试"""
        print("🚀 开始twice-nat44性能基准测试")
        print("=" * 80)
        
        benchmarks = [
            ("单规则处理性能", self.benchmark_single_rule_processing),
            ("批量处理性能对比", self.benchmark_batch_processing),
            ("内存使用效率", self.benchmark_memory_efficiency),
            ("缓存效果验证", self.benchmark_cache_effectiveness),
            ("并发处理能力", self.benchmark_concurrent_processing),
            ("大规模数据处理", self.benchmark_large_scale_processing),
            ("错误处理性能", self.benchmark_error_handling_performance)
        ]
        
        for benchmark_name, benchmark_func in benchmarks:
            print(f"\n🧪 {benchmark_name}")
            print("-" * 60)
            
            try:
                result = benchmark_func()
                self.benchmark_results[benchmark_name] = result
                self._print_benchmark_result(benchmark_name, result)
                
            except Exception as e:
                print(f"❌ {benchmark_name} 执行失败: {str(e)}")
                self.benchmark_results[benchmark_name] = {"error": str(e)}
        
        self._print_summary()
        return self.benchmark_results
    
    def benchmark_single_rule_processing(self) -> Dict[str, Any]:
        """单规则处理性能基准测试"""
        iterations = 100
        times_without_optimization = []
        times_with_optimization = []
        
        # 测试不使用优化的处理时间
        for i in range(iterations):
            rule_data = self.test_rules[i % len(self.test_rules)]
            
            start_time = time.time()
            try:
                rule = TwiceNat44Rule.from_fortigate_policy(
                    rule_data["policy"], rule_data["vip"]
                )
                # 模拟XML生成
                xml_data = self.nat_generator._create_nat_rule_element({
                    "name": rule.name,
                    "type": "twice-nat44"
                })
            except:
                pass
            end_time = time.time()
            
            times_without_optimization.append(end_time - start_time)
        
        # 测试使用优化的处理时间（通过缓存等机制）
        for i in range(iterations):
            rule_data = self.test_rules[i % len(self.test_rules)]
            
            start_time = time.time()
            try:
                # 使用缓存键检查
                cache_key = self.optimizer._generate_cache_key(rule_data)
                cached_result = self.optimizer.cache.get(cache_key)
                
                if cached_result is None:
                    rule = TwiceNat44Rule.from_fortigate_policy(
                        rule_data["policy"], rule_data["vip"]
                    )
                    xml_data = self.nat_generator._create_nat_rule_element({
                        "name": rule.name,
                        "type": "twice-nat44"
                    })
                    self.optimizer.cache.put(cache_key, xml_data)
                else:
                    xml_data = cached_result
            except:
                pass
            end_time = time.time()
            
            times_with_optimization.append(end_time - start_time)
        
        avg_without = statistics.mean(times_without_optimization) * 1000
        avg_with = statistics.mean(times_with_optimization) * 1000
        improvement = ((avg_without - avg_with) / avg_without) * 100 if avg_without > 0 else 0
        
        return {
            "iterations": iterations,
            "avg_time_without_optimization_ms": avg_without,
            "avg_time_with_optimization_ms": avg_with,
            "performance_improvement_percent": improvement,
            "throughput_without_optimization": 1000 / avg_without if avg_without > 0 else 0,
            "throughput_with_optimization": 1000 / avg_with if avg_with > 0 else 0
        }
    
    def benchmark_batch_processing(self) -> Dict[str, Any]:
        """批量处理性能基准测试"""
        batch_sizes = [10, 50, 100, 200]
        results = {}
        
        for batch_size in batch_sizes:
            batch = self.test_rules[:batch_size]
            
            # 测试传统逐个处理
            start_time = time.time()
            traditional_results = []
            for rule_data in batch:
                try:
                    rule = TwiceNat44Rule.from_fortigate_policy(
                        rule_data["policy"], rule_data["vip"]
                    )
                    traditional_results.append(rule)
                except:
                    pass
            traditional_time = time.time() - start_time
            
            # 测试优化批量处理
            def rule_processor(rule_data):
                return TwiceNat44Rule.from_fortigate_policy(
                    rule_data["policy"], rule_data["vip"]
                )
            
            start_time = time.time()
            optimized_results, metrics = self.optimizer.optimize_batch_processing(
                batch, rule_processor
            )
            optimized_time = time.time() - start_time
            
            improvement = ((traditional_time - optimized_time) / traditional_time) * 100 if traditional_time > 0 else 0
            
            results[f"batch_size_{batch_size}"] = {
                "traditional_time_s": traditional_time,
                "optimized_time_s": optimized_time,
                "improvement_percent": improvement,
                "traditional_throughput": batch_size / traditional_time if traditional_time > 0 else 0,
                "optimized_throughput": metrics.throughput,
                "success_rate": metrics.success_rate
            }
        
        return results
    
    def benchmark_memory_efficiency(self) -> Dict[str, Any]:
        """内存使用效率基准测试"""
        process = psutil.Process()
        
        # 基线内存使用
        baseline_memory = process.memory_info().rss / 1024 / 1024
        
        # 测试大量规则处理的内存使用
        batch = self.test_rules[:500]
        
        # 不使用内存优化
        memory_before = process.memory_info().rss / 1024 / 1024
        
        results_without_optimization = []
        for rule_data in batch:
            try:
                rule = TwiceNat44Rule.from_fortigate_policy(
                    rule_data["policy"], rule_data["vip"]
                )
                results_without_optimization.append(rule)
            except:
                pass
        
        memory_peak_without = process.memory_info().rss / 1024 / 1024
        
        # 清理
        del results_without_optimization
        import gc
        gc.collect()
        
        # 使用内存优化
        memory_before_optimized = process.memory_info().rss / 1024 / 1024
        
        def rule_processor(rule_data):
            return TwiceNat44Rule.from_fortigate_policy(
                rule_data["policy"], rule_data["vip"]
            )
        
        results_with_optimization, metrics = self.optimizer.optimize_batch_processing(
            batch, rule_processor
        )
        
        memory_peak_with = process.memory_info().rss / 1024 / 1024
        
        # 执行内存优化
        self.optimizer.optimize_memory_usage()
        memory_after_optimization = process.memory_info().rss / 1024 / 1024
        
        return {
            "baseline_memory_mb": baseline_memory,
            "memory_usage_without_optimization_mb": memory_peak_without - memory_before,
            "memory_usage_with_optimization_mb": memory_peak_with - memory_before_optimized,
            "memory_after_cleanup_mb": memory_after_optimization,
            "memory_efficiency_improvement_percent": (
                ((memory_peak_without - memory_before) - (memory_peak_with - memory_before_optimized)) /
                (memory_peak_without - memory_before) * 100
            ) if (memory_peak_without - memory_before) > 0 else 0
        }
    
    def benchmark_cache_effectiveness(self) -> Dict[str, Any]:
        """缓存效果验证基准测试"""
        # 重复处理相同规则以测试缓存效果
        repeated_rules = self.test_rules[:10] * 10  # 10个规则重复10次
        
        def rule_processor(rule_data):
            return TwiceNat44Rule.from_fortigate_policy(
                rule_data["policy"], rule_data["vip"]
            )
        
        # 清空缓存
        self.optimizer.cache.clear()
        
        start_time = time.time()
        results, metrics = self.optimizer.optimize_batch_processing(
            repeated_rules, rule_processor
        )
        processing_time = time.time() - start_time
        
        cache_stats = self.optimizer.cache.get_stats()
        
        return {
            "total_rules_processed": len(repeated_rules),
            "unique_rules": len(self.test_rules[:10]),
            "processing_time_s": processing_time,
            "cache_hits": cache_stats.get("hits", 0),
            "cache_misses": cache_stats.get("misses", 0),
            "cache_hit_rate_percent": cache_stats.get("hit_rate", 0),
            "throughput_with_cache": len(repeated_rules) / processing_time if processing_time > 0 else 0
        }
    
    def benchmark_concurrent_processing(self) -> Dict[str, Any]:
        """并发处理能力基准测试"""
        thread_counts = [1, 2, 4, 8]
        results = {}
        
        for thread_count in thread_counts:
            batch = self.test_rules[:100]
            
            def worker_function(worker_rules, worker_results):
                for rule_data in worker_rules:
                    try:
                        rule = TwiceNat44Rule.from_fortigate_policy(
                            rule_data["policy"], rule_data["vip"]
                        )
                        worker_results.append(rule)
                    except:
                        pass
            
            # 分割数据给不同线程
            chunk_size = len(batch) // thread_count
            chunks = [batch[i:i + chunk_size] for i in range(0, len(batch), chunk_size)]
            
            start_time = time.time()
            threads = []
            all_results = []
            
            for chunk in chunks:
                worker_results = []
                thread = threading.Thread(
                    target=worker_function,
                    args=(chunk, worker_results)
                )
                threads.append((thread, worker_results))
                thread.start()
            
            # 等待所有线程完成
            for thread, worker_results in threads:
                thread.join()
                all_results.extend(worker_results)
            
            processing_time = time.time() - start_time
            
            results[f"threads_{thread_count}"] = {
                "processing_time_s": processing_time,
                "throughput": len(all_results) / processing_time if processing_time > 0 else 0,
                "rules_processed": len(all_results)
            }
        
        return results
    
    def benchmark_large_scale_processing(self) -> Dict[str, Any]:
        """大规模数据处理基准测试"""
        large_batch = self.test_rules  # 1000个规则
        
        def rule_processor(rule_data):
            return TwiceNat44Rule.from_fortigate_policy(
                rule_data["policy"], rule_data["vip"]
            )
        
        start_time = time.time()
        memory_before = psutil.Process().memory_info().rss / 1024 / 1024
        
        results, metrics = self.optimizer.optimize_batch_processing(
            large_batch, rule_processor
        )
        
        end_time = time.time()
        memory_after = psutil.Process().memory_info().rss / 1024 / 1024
        
        return {
            "total_rules": len(large_batch),
            "processing_time_s": end_time - start_time,
            "throughput": metrics.throughput,
            "success_rate": metrics.success_rate,
            "memory_usage_mb": memory_after - memory_before,
            "average_time_per_rule_ms": (end_time - start_time) / len(large_batch) * 1000
        }
    
    def benchmark_error_handling_performance(self) -> Dict[str, Any]:
        """错误处理性能基准测试"""
        # 创建包含错误的测试数据
        error_rules = []
        for i in range(100):
            if i % 10 == 0:  # 10%的错误率
                # 创建无效规则
                policy = {"name": f"ERROR_POLICY_{i}"}  # 缺少必需字段
                vip = {"name": f"ERROR_VIP_{i}"}  # 缺少mappedip
            else:
                # 创建正常规则
                policy = {
                    "name": f"NORMAL_POLICY_{i}",
                    "status": "enable",
                    "service": ["HTTP"]
                }
                vip = {
                    "name": f"NORMAL_VIP_{i}",
                    "mappedip": f"192.168.1.{i % 254 + 1}"
                }
            
            error_rules.append({"policy": policy, "vip": vip})
        
        def error_prone_processor(rule_data):
            return TwiceNat44Rule.from_fortigate_policy(
                rule_data["policy"], rule_data["vip"]
            )
        
        start_time = time.time()
        results, metrics = self.optimizer.optimize_batch_processing(
            error_rules, error_prone_processor
        )
        processing_time = time.time() - start_time
        
        return {
            "total_rules": len(error_rules),
            "processing_time_s": processing_time,
            "success_count": metrics.success_count,
            "error_count": metrics.error_count,
            "success_rate": metrics.success_rate,
            "error_rate": (metrics.error_count / len(error_rules)) * 100,
            "throughput": metrics.throughput
        }
    
    def _print_benchmark_result(self, name: str, result: Dict[str, Any]):
        """打印基准测试结果"""
        if "error" in result:
            print(f"❌ 错误: {result['error']}")
            return
        
        print(f"📊 {name} 结果:")
        for key, value in result.items():
            if isinstance(value, dict):
                print(f"  {key}:")
                for sub_key, sub_value in value.items():
                    if isinstance(sub_value, float):
                        print(f"    {sub_key}: {sub_value:.3f}")
                    else:
                        print(f"    {sub_key}: {sub_value}")
            elif isinstance(value, float):
                print(f"  {key}: {value:.3f}")
            else:
                print(f"  {key}: {value}")
    
    def _print_summary(self):
        """打印基准测试总结"""
        print("\n" + "=" * 80)
        print("📊 twice-nat44性能基准测试总结")
        print("=" * 80)
        
        # 计算总体性能指标
        total_improvements = []
        
        for name, result in self.benchmark_results.items():
            if "error" not in result:
                if "performance_improvement_percent" in result:
                    total_improvements.append(result["performance_improvement_percent"])
                elif "improvement_percent" in result:
                    if isinstance(result["improvement_percent"], dict):
                        for improvement in result["improvement_percent"].values():
                            if isinstance(improvement, (int, float)):
                                total_improvements.append(improvement)
                    else:
                        total_improvements.append(result["improvement_percent"])
        
        if total_improvements:
            avg_improvement = statistics.mean(total_improvements)
            print(f"平均性能提升: {avg_improvement:.1f}%")
        
        print(f"完成基准测试: {len(self.benchmark_results)}项")
        
        # 性能优化器统计
        optimizer_stats = self.optimizer.get_performance_stats()
        if optimizer_stats:
            print(f"\n性能优化器统计:")
            print(f"  总操作数: {optimizer_stats.get('total_operations', 0)}")
            print(f"  总处理规则数: {optimizer_stats.get('total_rules_processed', 0)}")
            print(f"  平均吞吐量: {optimizer_stats.get('average_throughput', 0):.1f} 规则/秒")
            
            cache_stats = optimizer_stats.get('cache_stats', {})
            if cache_stats:
                print(f"  缓存命中率: {cache_stats.get('hit_rate', 0):.1f}%")


def main():
    """主函数"""
    benchmark_suite = TwiceNat44BenchmarkSuite()
    results = benchmark_suite.run_all_benchmarks()
    
    print("\n🎉 twice-nat44性能基准测试完成！")
    return results


if __name__ == "__main__":
    main()
