#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
容量校验功能单元测试
"""

import unittest
import sys
import os
import json
from unittest.mock import patch, mock_open

# 添加engine目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'engine'))

from engine.verify import (
    CapacityViolation, CapacityReporter, perform_capacity_validation,
    count_resources, count_dns_servers, count_sub_interfaces,
    count_pppoe_sessions, count_static_routes, count_nat_policies,
    count_security_policies, count_address_objects, count_address_groups,
    count_service_objects, count_service_groups, count_time_objects,
    load_capacity_limits
)


class TestCapacityViolation(unittest.TestCase):
    """测试CapacityViolation类"""
    
    def test_capacity_violation_creation(self):
        """测试CapacityViolation对象创建"""
        violation = CapacityViolation(
            resource_type="security_policies",
            current_count=3500,
            max_limit=3000,
            description="安全策略最大数量",
            severity="error",
            suggestion="建议优化安全策略配置",
            category="policy",
            device_model="z3200s"
        )
        
        self.assertEqual(violation.resource_type, "security_policies")
        self.assertEqual(violation.current_count, 3500)
        self.assertEqual(violation.max_limit, 3000)
        self.assertEqual(violation.violation_count, 500)
        self.assertEqual(violation.usage_rate, 3500/3000)
        self.assertEqual(violation.risk_level, "critical")
        self.assertEqual(violation.category, "policy")
        self.assertEqual(violation.device_model, "z3200s")
    
    def test_risk_level_calculation(self):
        """测试风险级别计算"""
        # 测试critical级别 (>= 100%)
        violation_critical = CapacityViolation("test", 1000, 800, "test", device_model="z3200s")
        self.assertEqual(violation_critical.risk_level, "critical")
        
        # 测试high级别 (>= 95%)
        violation_high = CapacityViolation("test", 950, 1000, "test", device_model="z3200s")
        self.assertEqual(violation_high.risk_level, "high")
        
        # 测试medium级别 (>= 80%)
        violation_medium = CapacityViolation("test", 850, 1000, "test", device_model="z3200s")
        self.assertEqual(violation_medium.risk_level, "medium")
        
        # 测试low级别 (< 80%)
        violation_low = CapacityViolation("test", 700, 1000, "test", device_model="z3200s")
        self.assertEqual(violation_low.risk_level, "low")
    
    def test_recommended_action(self):
        """测试推荐操作计算"""
        # 测试urgent_optimization (超出50%以上)
        violation = CapacityViolation("test", 1600, 1000, "test", device_model="z3200s")
        self.assertEqual(violation.analysis["recommended_action"], "urgent_optimization")
        
        # 测试moderate_optimization (超出20%-50%)
        violation = CapacityViolation("test", 1300, 1000, "test", device_model="z3200s")
        self.assertEqual(violation.analysis["recommended_action"], "moderate_optimization")
        
        # 测试minor_adjustment (超出20%以下)
        violation = CapacityViolation("test", 1100, 1000, "test", device_model="z3200s")
        self.assertEqual(violation.analysis["recommended_action"], "minor_adjustment")
    
    def test_optimization_tips_by_category(self):
        """测试按类别生成的优化建议"""
        # 测试policy类别
        violation_policy = CapacityViolation("test", 1100, 1000, "test", category="policy", device_model="z3200s")
        tips = violation_policy.analysis["optimization_tips"]
        self.assertIn("合并相似的策略规则", tips)
        self.assertIn("删除不再使用的策略", tips)
        
        # 测试object类别
        violation_object = CapacityViolation("test", 1100, 1000, "test", category="object", device_model="z3200s")
        tips = violation_object.analysis["optimization_tips"]
        self.assertIn("清理未使用的对象", tips)
        self.assertIn("合并重复的对象定义", tips)
        
        # 测试interface类别
        violation_interface = CapacityViolation("test", 1100, 1000, "test", category="interface", device_model="z3200s")
        tips = violation_interface.analysis["optimization_tips"]
        self.assertIn("检查是否有未使用的接口配置", tips)
    
    def test_to_dict_conversion(self):
        """测试转换为字典格式"""
        violation = CapacityViolation(
            resource_type="dns_servers",
            current_count=5,
            max_limit=3,
            description="DNS服务器最大数量",
            severity="error",
            suggestion="建议配置不超过3个DNS服务器",
            category="network",
            device_model="z3200s"
        )
        
        result_dict = violation.to_dict()
        
        self.assertEqual(result_dict["resource_type"], "dns_servers")
        self.assertEqual(result_dict["current_count"], 5)
        self.assertEqual(result_dict["max_limit"], 3)
        self.assertEqual(result_dict["violation_count"], 2)
        self.assertEqual(result_dict["usage_rate"], "166.7%")
        self.assertEqual(result_dict["severity"], "error")
        self.assertEqual(result_dict["risk_level"], "critical")
        self.assertEqual(result_dict["category"], "network")
        self.assertEqual(result_dict["device_model"], "z3200s")
        self.assertIn("analysis", result_dict)
    
    def test_to_summary_dict_conversion(self):
        """测试转换为简化字典格式"""
        violation = CapacityViolation("test", 1200, 1000, "测试资源", "error", "测试建议", device_model="z3200s")
        
        summary_dict = violation.to_summary_dict()
        
        self.assertEqual(summary_dict["resource"], "测试资源")
        self.assertEqual(summary_dict["current"], 1200)
        self.assertEqual(summary_dict["limit"], 1000)
        self.assertEqual(summary_dict["excess"], 200)
        self.assertEqual(summary_dict["usage"], "120.0%")
        self.assertEqual(summary_dict["severity"], "error")
        self.assertEqual(summary_dict["suggestion"], "测试建议")


class TestResourceCountingFunctions(unittest.TestCase):
    """测试资源统计函数"""
    
    def test_count_dns_servers(self):
        """测试DNS服务器统计"""
        config = """
config system dns
    set primary *******
    set secondary *******
    set server "*******" "*******"
end
"""
        count = count_dns_servers(config)
        self.assertEqual(count, 4)  # primary + secondary + 2 servers
    
    def test_count_dns_servers_minimal(self):
        """测试最小DNS配置"""
        config = """
config system dns
    set primary *******
end
"""
        count = count_dns_servers(config)
        self.assertEqual(count, 1)
    
    def test_count_sub_interfaces(self):
        """测试子接口统计"""
        config = """
config system interface
    edit "port1.100"
        set vlanid 100
    next
    edit "port1.200"
        set vlanid 200
    next
    edit "port2"
        set type physical
    next
end
"""
        count = count_sub_interfaces(config)
        self.assertEqual(count, 2)  # 只统计带.的子接口
    
    def test_count_pppoe_sessions(self):
        """测试PPPOE会话统计"""
        config = """
config system interface
    edit "pppoe1"
        set type pppoe
    next
    edit "pppoe2"
        set type pppoe
    next
    edit "port1"
        set type physical
    next
end
"""
        count = count_pppoe_sessions(config)
        self.assertEqual(count, 2)
    
    def test_count_static_routes(self):
        """测试静态路由统计"""
        config = """
config router static
    edit 1
        set dst ***********/24
        set gateway ********
    next
    edit 2
        set dst 192.168.2.0/24
        set gateway ********
    next
end
"""
        count = count_static_routes(config)
        self.assertEqual(count, 2)
    
    def test_count_security_policies(self):
        """测试安全策略统计"""
        config = """
config firewall policy
    edit 1
        set srcintf "port1"
        set dstintf "port2"
        set action accept
    next
    edit 2
        set srcintf "port2"
        set dstintf "port1"
        set action deny
    next
end
"""
        count = count_security_policies(config)
        self.assertEqual(count, 2)
    
    def test_count_nat_policies(self):
        """测试NAT策略统计"""
        config = """
config firewall policy
    edit 1
        set srcintf "port1"
        set dstintf "port2"
        set action accept
        set nat enable
    next
    edit 2
        set srcintf "port2"
        set dstintf "port1"
        set action accept
    next
end
"""
        count = count_nat_policies(config)
        self.assertEqual(count, 1)  # 只有一个启用了NAT
    
    def test_count_address_objects(self):
        """测试地址对象统计"""
        config = """
config firewall address
    edit "host1"
        set subnet ************/32
    next
    edit "host2"
        set subnet ************/32
    next
    edit "network1"
        set subnet ***********/24
    next
end
"""
        count = count_address_objects(config)
        self.assertEqual(count, 3)
    
    def test_count_address_groups(self):
        """测试地址组统计"""
        config = """
config firewall addrgrp
    edit "group1"
        set member "host1" "host2"
    next
    edit "group2"
        set member "network1"
    next
end
"""
        count = count_address_groups(config)
        self.assertEqual(count, 2)
    
    def test_count_service_objects(self):
        """测试服务对象统计"""
        config = """
config firewall service custom
    edit "service1"
        set tcp-portrange 80
    next
    edit "service2"
        set tcp-portrange 443
    next
end
"""
        count = count_service_objects(config)
        self.assertEqual(count, 2)
    
    def test_count_service_groups(self):
        """测试服务组统计"""
        config = """
config firewall service group
    edit "web_services"
        set member "service1" "service2"
    next
    edit "mail_services"
        set member "smtp" "pop3"
    next
end
"""
        count = count_service_groups(config)
        self.assertEqual(count, 2)
    
    def test_count_time_objects(self):
        """测试时间对象统计"""
        config = """
config firewall schedule recurring
    edit "work_hours"
        set day monday tuesday wednesday thursday friday
        set start 09:00
        set end 17:00
    next
    edit "weekend"
        set day saturday sunday
        set start 10:00
        set end 18:00
    next
end
"""
        count = count_time_objects(config)
        self.assertEqual(count, 2)


class TestCapacityReporter(unittest.TestCase):
    """测试CapacityReporter类"""

    def setUp(self):
        """设置测试数据"""
        self.violations = [
            CapacityViolation("dns_servers", 5, 3, "DNS服务器最大数量", "error", "减少DNS服务器", "network", "z3200s"),
            CapacityViolation("security_policies", 3500, 3000, "安全策略最大数量", "error", "优化策略", "policy", "z3200s")
        ]
        self.resource_counts = {
            "dns_servers": 5,
            "security_policies": 3500,
            "address_objects": 500
        }

    def test_generate_summary_report(self):
        """测试生成摘要报告"""
        reporter = CapacityReporter("z3200s", self.violations, self.resource_counts)
        summary = reporter.generate_summary_report()

        self.assertEqual(summary["device_model"], "z3200s")
        self.assertEqual(summary["total_resources_checked"], 3)
        self.assertEqual(summary["total_violations"], 2)
        self.assertEqual(summary["critical_violations"], 2)
        self.assertEqual(summary["warning_violations"], 0)
        self.assertEqual(summary["overall_status"], "failed")
        self.assertIn("violations_by_category", summary)

    def test_generate_detailed_report(self):
        """测试生成详细报告"""
        reporter = CapacityReporter("z3200s", self.violations, self.resource_counts)
        detailed = reporter.generate_detailed_report()

        self.assertIn("summary", detailed)
        self.assertIn("violations", detailed)
        self.assertIn("resource_usage", detailed)
        self.assertIn("recommendations", detailed)
        self.assertIn("device_info", detailed)

        # 检查violations格式
        self.assertEqual(len(detailed["violations"]), 2)
        self.assertIn("analysis", detailed["violations"][0])

    def test_format_console_output(self):
        """测试控制台输出格式化"""
        reporter = CapacityReporter("z3200s", self.violations, self.resource_counts)
        output = reporter.format_console_output()

        self.assertIn("设备容量校验报告", output)
        self.assertIn("z3200s", output)
        self.assertIn("检查资源数量: 3", output)
        self.assertIn("发现违规项目: 2", output)
        self.assertIn("违规详情:", output)


class TestPerformCapacityValidation(unittest.TestCase):
    """测试perform_capacity_validation函数"""

    @patch('engine.verify.load_capacity_limits')
    def test_perform_capacity_validation_with_violations(self, mock_load_limits):
        """测试有违规的容量校验"""
        # 模拟配置文件
        mock_config = {
            "device_models": {
                "z3200s": {
                    "limits": {
                        "dns_servers": {"max": 3, "description": "DNS服务器", "severity": "error", "suggestion": "减少DNS", "category": "network"},
                        "security_policies": {"max": 3000, "description": "安全策略", "severity": "error", "suggestion": "优化策略", "category": "policy"}
                    }
                }
            }
        }
        mock_load_limits.return_value = mock_config

        # 测试配置（会超出限制）
        test_config = """
config system dns
    set primary *******
    set secondary *******
    set server "*******" "*******"
end
"""

        violations = perform_capacity_validation(test_config, "z3200s")

        self.assertEqual(len(violations), 1)  # DNS服务器超限
        self.assertEqual(violations[0].resource_type, "dns_servers")
        self.assertEqual(violations[0].current_count, 4)
        self.assertEqual(violations[0].max_limit, 3)

    @patch('engine.verify.load_capacity_limits')
    def test_perform_capacity_validation_no_violations(self, mock_load_limits):
        """测试无违规的容量校验"""
        mock_config = {
            "device_models": {
                "z3200s": {
                    "limits": {
                        "dns_servers": {"max": 5, "description": "DNS服务器", "severity": "error", "suggestion": "减少DNS", "category": "network"}
                    }
                }
            }
        }
        mock_load_limits.return_value = mock_config

        test_config = """
config system dns
    set primary *******
    set secondary *******
end
"""

        violations = perform_capacity_validation(test_config, "z3200s")

        self.assertEqual(len(violations), 0)

    @patch('engine.verify.load_capacity_limits')
    def test_perform_capacity_validation_unsupported_device(self, mock_load_limits):
        """测试不支持的设备型号"""
        mock_config = {
            "device_models": {
                "z3200s": {"limits": {}}
            }
        }
        mock_load_limits.return_value = mock_config

        violations = perform_capacity_validation("test config", "unknown_device")

        self.assertEqual(len(violations), 0)

    @patch('engine.verify.load_capacity_limits')
    def test_perform_capacity_validation_load_error(self, mock_load_limits):
        """测试配置加载错误"""
        mock_load_limits.return_value = None

        violations = perform_capacity_validation("test config", "z3200s")

        self.assertEqual(len(violations), 0)


class TestCountResources(unittest.TestCase):
    """测试count_resources函数"""

    def test_count_resources_comprehensive(self):
        """测试综合资源统计"""
        config = """
config system dns
    set primary *******
    set secondary *******
end

config system interface
    edit "port1.100"
        set vlanid 100
    next
    edit "pppoe1"
        set type pppoe
    next
end

config router static
    edit 1
        set dst ***********/24
        set gateway ********
    next
end

config firewall policy
    edit 1
        set srcintf "port1"
        set dstintf "port2"
        set action accept
        set nat enable
    next
end

config firewall address
    edit "host1"
        set subnet ************/32
    next
end

config firewall addrgrp
    edit "group1"
        set member "host1"
    next
end

config firewall service custom
    edit "service1"
        set tcp-portrange 80
    next
end

config firewall service group
    edit "web_services"
        set member "service1"
    next
end

config firewall schedule recurring
    edit "work_hours"
        set day monday
        set start 09:00
        set end 17:00
    next
end
"""

        counts = count_resources(config)

        self.assertEqual(counts["dns_servers"], 2)
        self.assertEqual(counts["sub_interfaces"], 1)
        self.assertEqual(counts["pppoe_sessions"], 1)
        self.assertEqual(counts["static_routes_v4"], 1)
        self.assertEqual(counts["nat44_policies"], 1)
        self.assertEqual(counts["security_policies"], 1)
        self.assertEqual(counts["address_objects"], 1)
        self.assertEqual(counts["address_groups"], 1)
        self.assertEqual(counts["service_objects"], 1)
        self.assertEqual(counts["service_groups"], 1)
        self.assertEqual(counts["time_objects"], 1)

    def test_count_resources_empty_config(self):
        """测试空配置"""
        counts = count_resources("")

        for resource_type, count in counts.items():
            self.assertEqual(count, 0)


class TestEdgeCases(unittest.TestCase):
    """测试边界条件和异常情况"""

    def test_capacity_violation_zero_limit(self):
        """测试限制为0的情况"""
        violation = CapacityViolation("test", 5, 0, "test", device_model="z3200s")
        self.assertEqual(violation.usage_rate, 0)  # 避免除零错误

    def test_capacity_violation_zero_current(self):
        """测试当前数量为0的情况"""
        violation = CapacityViolation("test", 0, 100, "test", device_model="z3200s")
        self.assertEqual(violation.usage_rate, 0)
        self.assertEqual(violation.violation_count, -100)
        self.assertEqual(violation.risk_level, "low")

    def test_malformed_config_handling(self):
        """测试格式错误的配置处理"""
        malformed_config = """
config system dns
    set primary *******
    # 缺少end标签
config firewall policy
    edit 1
        set action accept
    # 缺少next和end标签
"""

        # 应该不会抛出异常，返回能解析的部分
        counts = count_resources(malformed_config)
        self.assertIsInstance(counts, dict)
        self.assertEqual(counts["dns_servers"], 1)  # 能解析到primary DNS


if __name__ == '__main__':
    unittest.main()
