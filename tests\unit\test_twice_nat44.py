"""
FortiGate twice-nat44转换功能单元测试

本测试模块提供了twice-nat44功能的全面单元测试，包括：
- 数据模型测试
- FortiGate策略处理测试  
- NAT生成器测试
- XML验证器测试
- 配置管理测试

基于前两个阶段完成的功能，确保100%测试覆盖率和企业级质量标准。
"""

import unittest
import sys
import os
from unittest.mock import Mock, patch, MagicMock
from lxml import etree

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from engine.business.models.twice_nat44_models import (
    TwiceNat44Rule,
    TwiceNat44MatchConditions,
    TwiceNat44SnatConfig,
    TwiceNat44DnatConfig,
    TwiceNat44AddressType,
    TwiceNat44ConfigError,
    TwiceNat44ValidationError
)
from engine.generators.nat_generator import NATGenerator
from engine.validators.twice_nat44_validator import TwiceNat44Validator
from engine.infrastructure.config.config_manager import ConfigManager


class TestTwiceNat44DataModels(unittest.TestCase):
    """twice-nat44数据模型单元测试"""
    
    def setUp(self):
        """测试初始化"""
        self.maxDiff = None
    
    def test_twice_nat44_address_type_enum(self):
        """测试地址类型枚举"""
        # 测试枚举值
        self.assertEqual(TwiceNat44AddressType.INTERFACE.value, "interface")
        self.assertEqual(TwiceNat44AddressType.IP.value, "ip")
        self.assertEqual(TwiceNat44AddressType.POOL.value, "pool")
        
        # 测试枚举数量
        self.assertEqual(len(TwiceNat44AddressType), 3)
    
    def test_twice_nat44_match_conditions_creation(self):
        """测试匹配条件创建"""
        # 测试基本创建
        match_conditions = TwiceNat44MatchConditions(
            dest_network="WEB_SERVER_VIP",
            service="HTTP",
            time_range="business_hours"
        )
        
        self.assertEqual(match_conditions.dest_network, "WEB_SERVER_VIP")
        self.assertEqual(match_conditions.service, "HTTP")
        self.assertEqual(match_conditions.time_range, "business_hours")
        self.assertIsNone(match_conditions.source_network)
        
        # 测试包含源网络的创建
        match_with_source = TwiceNat44MatchConditions(
            dest_network="WEB_SERVER_VIP",
            source_network="INTERNAL_NET",
            service="HTTPS"
        )
        
        self.assertEqual(match_with_source.source_network, "INTERNAL_NET")
    
    def test_twice_nat44_match_conditions_validation(self):
        """测试匹配条件验证"""
        # 测试空目标网络应该抛出异常
        with self.assertRaises(TwiceNat44ConfigError):
            TwiceNat44MatchConditions(dest_network="")
        
        with self.assertRaises(TwiceNat44ConfigError):
            TwiceNat44MatchConditions(dest_network=None)
    
    def test_twice_nat44_match_conditions_to_dict(self):
        """测试匹配条件字典转换"""
        match_conditions = TwiceNat44MatchConditions(
            dest_network="WEB_SERVER_VIP",
            source_network="INTERNAL_NET",
            service="HTTPS",
            time_range="always"
        )
        
        expected_dict = {
            "dest-network": {"name": "WEB_SERVER_VIP"},
            "source-network": {"name": "INTERNAL_NET"},
            "service": {"name": "HTTPS"},
            "time-range": {"value": "always"}
        }
        
        self.assertEqual(match_conditions.to_dict(), expected_dict)
        
        # 测试不包含源网络的情况
        match_no_source = TwiceNat44MatchConditions(
            dest_network="WEB_SERVER_VIP",
            service="HTTP"
        )
        
        expected_no_source = {
            "dest-network": {"name": "WEB_SERVER_VIP"},
            "service": {"name": "HTTP"},
            "time-range": {"value": "any"}
        }
        
        self.assertEqual(match_no_source.to_dict(), expected_no_source)
    
    def test_twice_nat44_snat_config_creation(self):
        """测试SNAT配置创建"""
        # 测试接口地址类型
        snat_interface = TwiceNat44SnatConfig(
            address_type=TwiceNat44AddressType.INTERFACE,
            no_pat=False,
            try_no_pat=True
        )
        
        self.assertEqual(snat_interface.address_type, TwiceNat44AddressType.INTERFACE)
        self.assertIsNone(snat_interface.address_value)
        self.assertFalse(snat_interface.no_pat)
        self.assertTrue(snat_interface.try_no_pat)
        
        # 测试IP地址类型
        snat_ip = TwiceNat44SnatConfig(
            address_type=TwiceNat44AddressType.IP,
            address_value="*************",
            no_pat=True,
            try_no_pat=False
        )
        
        self.assertEqual(snat_ip.address_type, TwiceNat44AddressType.IP)
        self.assertEqual(snat_ip.address_value, "*************")
        self.assertTrue(snat_ip.no_pat)
        self.assertFalse(snat_ip.try_no_pat)
        
        # 测试IP池类型
        snat_pool = TwiceNat44SnatConfig(
            address_type=TwiceNat44AddressType.POOL,
            address_value="NAT_POOL_1"
        )
        
        self.assertEqual(snat_pool.address_type, TwiceNat44AddressType.POOL)
        self.assertEqual(snat_pool.address_value, "NAT_POOL_1")
    
    def test_twice_nat44_snat_config_validation(self):
        """测试SNAT配置验证"""
        # 测试IP类型缺少地址值
        with self.assertRaises(TwiceNat44ConfigError):
            TwiceNat44SnatConfig(
                address_type=TwiceNat44AddressType.IP,
                address_value=None
            )
        
        # 测试池类型缺少地址值
        with self.assertRaises(TwiceNat44ConfigError):
            TwiceNat44SnatConfig(
                address_type=TwiceNat44AddressType.POOL,
                address_value=""
            )
        
        # 测试无效IP地址
        with self.assertRaises(TwiceNat44ConfigError):
            TwiceNat44SnatConfig(
                address_type=TwiceNat44AddressType.IP,
                address_value="999.999.999.999"
            )
        
        with self.assertRaises(TwiceNat44ConfigError):
            TwiceNat44SnatConfig(
                address_type=TwiceNat44AddressType.IP,
                address_value="invalid.ip.address"
            )
    
    def test_twice_nat44_snat_config_to_dict(self):
        """测试SNAT配置字典转换"""
        # 测试接口地址类型
        snat_interface = TwiceNat44SnatConfig(
            address_type=TwiceNat44AddressType.INTERFACE,
            no_pat=False,
            try_no_pat=True
        )
        
        expected_interface = {
            "output-address": {},
            "no-pat": False,
            "try-no-pat": True
        }
        
        self.assertEqual(snat_interface.to_dict(), expected_interface)
        
        # 测试IP地址类型
        snat_ip = TwiceNat44SnatConfig(
            address_type=TwiceNat44AddressType.IP,
            address_value="*************",
            no_pat=True,
            try_no_pat=False
        )
        
        expected_ip = {
            "ipv4-address": "*************",
            "no-pat": True,
            "try-no-pat": False
        }
        
        self.assertEqual(snat_ip.to_dict(), expected_ip)
        
        # 测试IP池类型
        snat_pool = TwiceNat44SnatConfig(
            address_type=TwiceNat44AddressType.POOL,
            address_value="NAT_POOL_1",
            no_pat=False,
            try_no_pat=True
        )
        
        expected_pool = {
            "pool-name": "NAT_POOL_1",
            "no-pat": False,
            "try-no-pat": True
        }
        
        self.assertEqual(snat_pool.to_dict(), expected_pool)
    
    def test_twice_nat44_dnat_config_creation(self):
        """测试DNAT配置创建"""
        # 测试基本创建
        dnat_config = TwiceNat44DnatConfig(
            ipv4_address="*************",
            port=8080
        )
        
        self.assertEqual(dnat_config.ipv4_address, "*************")
        self.assertEqual(dnat_config.port, 8080)
        
        # 测试不包含端口的创建
        dnat_no_port = TwiceNat44DnatConfig(
            ipv4_address="**********"
        )
        
        self.assertEqual(dnat_no_port.ipv4_address, "**********")
        self.assertIsNone(dnat_no_port.port)
    
    def test_twice_nat44_dnat_config_validation(self):
        """测试DNAT配置验证"""
        # 测试空IP地址
        with self.assertRaises(TwiceNat44ConfigError):
            TwiceNat44DnatConfig(ipv4_address="")
        
        with self.assertRaises(TwiceNat44ConfigError):
            TwiceNat44DnatConfig(ipv4_address=None)
        
        # 测试无效IP地址
        with self.assertRaises(TwiceNat44ConfigError):
            TwiceNat44DnatConfig(ipv4_address="999.999.999.999")
        
        with self.assertRaises(TwiceNat44ConfigError):
            TwiceNat44DnatConfig(ipv4_address="invalid.ip")
        
        # 测试无效端口
        with self.assertRaises(TwiceNat44ConfigError):
            TwiceNat44DnatConfig(
                ipv4_address="*************",
                port=0
            )
        
        with self.assertRaises(TwiceNat44ConfigError):
            TwiceNat44DnatConfig(
                ipv4_address="*************",
                port=70000
            )
    
    def test_twice_nat44_dnat_config_to_dict(self):
        """测试DNAT配置字典转换"""
        # 测试包含端口的配置
        dnat_with_port = TwiceNat44DnatConfig(
            ipv4_address="*************",
            port=8080
        )
        
        expected_with_port = {
            "ipv4-address": "*************",
            "port": 8080
        }
        
        self.assertEqual(dnat_with_port.to_dict(), expected_with_port)
        
        # 测试不包含端口的配置
        dnat_no_port = TwiceNat44DnatConfig(
            ipv4_address="**********"
        )
        
        expected_no_port = {
            "ipv4-address": "**********"
        }
        
        self.assertEqual(dnat_no_port.to_dict(), expected_no_port)


class TestTwiceNat44Rule(unittest.TestCase):
    """twice-nat44规则单元测试"""
    
    def setUp(self):
        """测试初始化"""
        self.maxDiff = None
        
        # 创建测试用的组件
        self.match_conditions = TwiceNat44MatchConditions(
            dest_network="WEB_SERVER_VIP",
            service="HTTP",
            time_range="always"
        )
        
        self.snat_config = TwiceNat44SnatConfig(
            address_type=TwiceNat44AddressType.INTERFACE,
            no_pat=False,
            try_no_pat=True
        )
        
        self.dnat_config = TwiceNat44DnatConfig(
            ipv4_address="*************",
            port=8080
        )
    
    def test_twice_nat44_rule_creation(self):
        """测试twice-nat44规则创建"""
        rule = TwiceNat44Rule(
            name="test_twice_nat44_rule",
            enabled=True,
            description="测试规则",
            match_conditions=self.match_conditions,
            snat_config=self.snat_config,
            dnat_config=self.dnat_config
        )
        
        self.assertEqual(rule.name, "test_twice_nat44_rule")
        self.assertTrue(rule.enabled)
        self.assertEqual(rule.description, "测试规则")
        self.assertEqual(rule.match_conditions, self.match_conditions)
        self.assertEqual(rule.snat_config, self.snat_config)
        self.assertEqual(rule.dnat_config, self.dnat_config)
    
    def test_twice_nat44_rule_validation(self):
        """测试twice-nat44规则验证"""
        # 测试空规则名称
        with self.assertRaises(TwiceNat44ConfigError):
            TwiceNat44Rule(
                name="",
                enabled=True,
                description="测试",
                match_conditions=self.match_conditions,
                snat_config=self.snat_config,
                dnat_config=self.dnat_config
            )
        
        # 测试空描述会自动生成
        rule_no_desc = TwiceNat44Rule(
            name="test_rule",
            enabled=True,
            description="",
            match_conditions=self.match_conditions,
            snat_config=self.snat_config,
            dnat_config=self.dnat_config
        )
        
        self.assertEqual(rule_no_desc.description, "twice-nat44 rule test_rule")
    
    def test_twice_nat44_rule_validate_method(self):
        """测试规则验证方法"""
        # 测试有效规则
        valid_rule = TwiceNat44Rule(
            name="valid_rule",
            enabled=True,
            description="有效规则",
            match_conditions=self.match_conditions,
            snat_config=self.snat_config,
            dnat_config=self.dnat_config
        )
        
        self.assertTrue(valid_rule.validate())
        
        # 测试无效规则（空DNAT IP）
        invalid_dnat = TwiceNat44DnatConfig(ipv4_address="*************")
        invalid_dnat.ipv4_address = ""  # 直接修改以绕过初始验证
        
        invalid_rule = TwiceNat44Rule(
            name="invalid_rule",
            enabled=True,
            description="无效规则",
            match_conditions=self.match_conditions,
            snat_config=self.snat_config,
            dnat_config=invalid_dnat
        )
        
        self.assertFalse(invalid_rule.validate())
    
    def test_twice_nat44_rule_to_nat_rule_dict(self):
        """测试规则转换为NAT规则字典"""
        rule = TwiceNat44Rule(
            name="test_rule",
            enabled=True,
            description="测试规则",
            match_conditions=self.match_conditions,
            snat_config=self.snat_config,
            dnat_config=self.dnat_config
        )
        
        expected_dict = {
            "name": "test_rule",
            "type": "twice-nat44",
            "rule_en": True,
            "desc": "测试规则",
            "twice-nat44": {
                "match": self.match_conditions.to_dict(),
                "snat": self.snat_config.to_dict(),
                "dnat": self.dnat_config.to_dict()
            }
        }
        
        self.assertEqual(rule.to_nat_rule_dict(), expected_dict)
    
    def test_twice_nat44_rule_get_xml_namespace(self):
        """测试获取XML命名空间"""
        rule = TwiceNat44Rule(
            name="test_rule",
            enabled=True,
            description="测试规则",
            match_conditions=self.match_conditions,
            snat_config=self.snat_config,
            dnat_config=self.dnat_config
        )
        
        expected_namespace = "urn:ruijie:ntos:params:xml:ns:yang:nat"
        self.assertEqual(rule.get_xml_namespace(), expected_namespace)


class TestTwiceNat44FortigateConversion(unittest.TestCase):
    """FortiGate配置转换为twice-nat44规则的单元测试"""

    def setUp(self):
        """测试初始化"""
        self.maxDiff = None

        # 标准FortiGate策略配置
        self.fortigate_policy = {
            "name": "WEB_ACCESS_POLICY",
            "status": "enable",
            "service": ["HTTPS"],
            "schedule": "always",
            "fixedport": "disable",
            "nat": "enable",
            "dstaddr": ["WEB_SERVER_VIP"]
        }

        # 标准VIP配置
        self.vip_config = {
            "name": "WEB_SERVER_VIP",
            "extip": "***********0",
            "mappedip": "*************",
            "extport": "443",
            "mappedport": "8443",
            "protocol": "tcp"
        }

    def test_from_fortigate_policy_basic(self):
        """测试基本FortiGate策略转换"""
        rule = TwiceNat44Rule.from_fortigate_policy(self.fortigate_policy, self.vip_config)

        # 验证规则基本信息
        expected_name = "WEB_ACCESS_POLICY_WEB_SERVER_VIP_twice_nat"
        self.assertEqual(rule.name, expected_name)
        self.assertTrue(rule.enabled)
        self.assertEqual(rule.description, "FortiGate复合NAT策略 WEB_ACCESS_POLICY")

        # 验证匹配条件
        self.assertEqual(rule.match_conditions.dest_network, "WEB_SERVER_VIP")
        self.assertEqual(rule.match_conditions.service, "HTTPS")
        self.assertEqual(rule.match_conditions.time_range, "always")

        # 验证SNAT配置
        self.assertEqual(rule.snat_config.address_type, TwiceNat44AddressType.INTERFACE)
        self.assertFalse(rule.snat_config.no_pat)  # fixedport=disable
        self.assertTrue(rule.snat_config.try_no_pat)

        # 验证DNAT配置
        self.assertEqual(rule.dnat_config.ipv4_address, "*************")
        self.assertEqual(rule.dnat_config.port, 8443)

    def test_from_fortigate_policy_with_fixedport(self):
        """测试启用fixedport的FortiGate策略转换"""
        policy_with_fixedport = self.fortigate_policy.copy()
        policy_with_fixedport["fixedport"] = "enable"

        rule = TwiceNat44Rule.from_fortigate_policy(policy_with_fixedport, self.vip_config)

        # 验证PAT配置
        self.assertTrue(rule.snat_config.no_pat)  # fixedport=enable
        self.assertFalse(rule.snat_config.try_no_pat)

    def test_from_fortigate_policy_disabled_status(self):
        """测试禁用状态的FortiGate策略转换"""
        disabled_policy = self.fortigate_policy.copy()
        disabled_policy["status"] = "disable"

        rule = TwiceNat44Rule.from_fortigate_policy(disabled_policy, self.vip_config)

        self.assertFalse(rule.enabled)

    def test_from_fortigate_policy_service_list(self):
        """测试服务列表的处理"""
        policy_with_services = self.fortigate_policy.copy()
        policy_with_services["service"] = ["HTTP", "HTTPS", "FTP"]

        rule = TwiceNat44Rule.from_fortigate_policy(policy_with_services, self.vip_config)

        # 应该使用第一个服务
        self.assertEqual(rule.match_conditions.service, "HTTP")

    def test_from_fortigate_policy_service_string(self):
        """测试服务字符串的处理"""
        policy_with_service_string = self.fortigate_policy.copy()
        policy_with_service_string["service"] = "HTTP"

        rule = TwiceNat44Rule.from_fortigate_policy(policy_with_service_string, self.vip_config)

        self.assertEqual(rule.match_conditions.service, "HTTP")

    def test_from_fortigate_policy_no_mapped_port(self):
        """测试没有映射端口的VIP配置"""
        vip_no_port = self.vip_config.copy()
        del vip_no_port["mappedport"]

        rule = TwiceNat44Rule.from_fortigate_policy(self.fortigate_policy, vip_no_port)

        self.assertIsNone(rule.dnat_config.port)

    def test_from_fortigate_policy_invalid_mapped_port(self):
        """测试无效映射端口的处理"""
        vip_invalid_port = self.vip_config.copy()
        vip_invalid_port["mappedport"] = "invalid_port"

        rule = TwiceNat44Rule.from_fortigate_policy(self.fortigate_policy, vip_invalid_port)

        # 应该忽略无效端口
        self.assertIsNone(rule.dnat_config.port)

    def test_from_fortigate_policy_missing_mappedip(self):
        """测试缺少mappedip的VIP配置"""
        vip_no_ip = self.vip_config.copy()
        del vip_no_ip["mappedip"]

        with self.assertRaises(TwiceNat44ConfigError):
            TwiceNat44Rule.from_fortigate_policy(self.fortigate_policy, vip_no_ip)

    def test_from_fortigate_policy_default_values(self):
        """测试默认值处理"""
        minimal_policy = {
            "name": "MINIMAL_POLICY"
        }

        minimal_vip = {
            "name": "MINIMAL_VIP",
            "mappedip": "**********"
        }

        rule = TwiceNat44Rule.from_fortigate_policy(minimal_policy, minimal_vip)

        # 验证默认值
        self.assertTrue(rule.enabled)  # 默认启用
        self.assertEqual(rule.match_conditions.service, "any")  # 默认服务
        self.assertEqual(rule.match_conditions.time_range, "any")  # 默认时间范围
        self.assertFalse(rule.snat_config.no_pat)  # 默认PAT设置
        self.assertTrue(rule.snat_config.try_no_pat)


class TestTwiceNat44NATGenerator(unittest.TestCase):
    """twice-nat44 NAT生成器单元测试"""

    def setUp(self):
        """测试初始化"""
        self.maxDiff = None
        self.nat_generator = NATGenerator()

        # 标准twice-nat44规则配置
        self.twice_nat44_rule = {
            "name": "test_twice_nat44_rule",
            "type": "twice-nat44",
            "rule_en": True,
            "desc": "测试twice-nat44规则",
            "twice-nat44": {
                "match": {
                    "dest-network": {"name": "WEB_SERVER_VIP"},
                    "service": {"name": "HTTPS"},
                    "time-range": {"value": "any"}
                },
                "snat": {
                    "output-address": {},
                    "no-pat": False,
                    "try-no-pat": True
                },
                "dnat": {
                    "ipv4-address": "*************",
                    "port": 8443
                }
            }
        }

    def test_create_nat_rule_element_twice_nat44(self):
        """测试创建twice-nat44规则XML元素"""
        rule_element = self.nat_generator._create_nat_rule_element(self.twice_nat44_rule)

        self.assertIsNotNone(rule_element)
        self.assertEqual(rule_element.tag, "rule")

        # 验证基本元素
        name_elem = rule_element.find("name")
        self.assertIsNotNone(name_elem)
        self.assertEqual(name_elem.text, "test_twice_nat44_rule")

        rule_en_elem = rule_element.find("rule_en")
        self.assertIsNotNone(rule_en_elem)
        self.assertEqual(rule_en_elem.text, "true")

        desc_elem = rule_element.find("desc")
        self.assertIsNotNone(desc_elem)
        self.assertEqual(desc_elem.text, "测试twice-nat44规则")

        # 验证twice-nat44配置
        twice_nat_elem = rule_element.find("twice-nat44")
        self.assertIsNotNone(twice_nat_elem)

    def test_add_twice_nat44_config(self):
        """测试添加twice-nat44配置"""
        rule_element = etree.Element("rule")
        twice_nat_config = self.twice_nat44_rule["twice-nat44"]

        self.nat_generator._add_twice_nat44_config(rule_element, twice_nat_config)

        # 验证twice-nat44元素
        twice_nat_elem = rule_element.find("twice-nat44")
        self.assertIsNotNone(twice_nat_elem)

        # 验证match元素
        match_elem = twice_nat_elem.find("match")
        self.assertIsNotNone(match_elem)

        # 验证snat元素
        snat_elem = twice_nat_elem.find("snat")
        self.assertIsNotNone(snat_elem)

        # 验证dnat元素
        dnat_elem = twice_nat_elem.find("dnat")
        self.assertIsNotNone(dnat_elem)

    def test_add_twice_nat_snat_config_interface(self):
        """测试添加接口类型SNAT配置"""
        snat_element = etree.Element("snat")
        snat_config = {
            "output-address": {},
            "no-pat": False,
            "try-no-pat": True
        }

        self.nat_generator._add_twice_nat_snat_config(snat_element, snat_config)

        # 验证output-address元素
        output_addr_elem = snat_element.find("output-address")
        self.assertIsNotNone(output_addr_elem)

        # 验证PAT配置
        no_pat_elem = snat_element.find("no-pat")
        self.assertIsNotNone(no_pat_elem)
        self.assertEqual(no_pat_elem.text, "false")

        try_no_pat_elem = snat_element.find("try-no-pat")
        self.assertIsNotNone(try_no_pat_elem)
        self.assertEqual(try_no_pat_elem.text, "true")

    def test_add_twice_nat_snat_config_ip(self):
        """测试添加IP地址类型SNAT配置"""
        snat_element = etree.Element("snat")
        snat_config = {
            "ipv4-address": "*************",
            "no-pat": True,
            "try-no-pat": False
        }

        self.nat_generator._add_twice_nat_snat_config(snat_element, snat_config)

        # 验证IP地址元素
        ip_elem = snat_element.find("ipv4-address")
        self.assertIsNotNone(ip_elem)
        self.assertEqual(ip_elem.text, "*************")

        # 验证PAT配置
        no_pat_elem = snat_element.find("no-pat")
        self.assertEqual(no_pat_elem.text, "true")

        try_no_pat_elem = snat_element.find("try-no-pat")
        self.assertEqual(try_no_pat_elem.text, "false")

    def test_add_twice_nat_snat_config_pool(self):
        """测试添加IP池类型SNAT配置"""
        snat_element = etree.Element("snat")
        snat_config = {
            "pool-name": "NAT_POOL_1",
            "no-pat": False,
            "try-no-pat": True
        }

        self.nat_generator._add_twice_nat_snat_config(snat_element, snat_config)

        # 验证池名称元素
        pool_elem = snat_element.find("pool-name")
        self.assertIsNotNone(pool_elem)
        self.assertEqual(pool_elem.text, "NAT_POOL_1")

    def test_add_twice_nat_dnat_config(self):
        """测试添加DNAT配置"""
        dnat_element = etree.Element("dnat")
        dnat_config = {
            "ipv4-address": "*************",
            "port": 8443
        }

        self.nat_generator._add_twice_nat_dnat_config(dnat_element, dnat_config)

        # 验证IP地址元素
        ip_elem = dnat_element.find("ipv4-address")
        self.assertIsNotNone(ip_elem)
        self.assertEqual(ip_elem.text, "*************")

        # 验证端口元素
        port_elem = dnat_element.find("port")
        self.assertIsNotNone(port_elem)
        self.assertEqual(port_elem.text, "8443")

    def test_add_twice_nat_dnat_config_no_port(self):
        """测试添加不包含端口的DNAT配置"""
        dnat_element = etree.Element("dnat")
        dnat_config = {
            "ipv4-address": "**********"
        }

        self.nat_generator._add_twice_nat_dnat_config(dnat_element, dnat_config)

        # 验证IP地址元素
        ip_elem = dnat_element.find("ipv4-address")
        self.assertIsNotNone(ip_elem)
        self.assertEqual(ip_elem.text, "**********")

        # 验证没有端口元素
        port_elem = dnat_element.find("port")
        self.assertIsNone(port_elem)

    def test_validate_twice_nat44_xml_valid(self):
        """测试验证有效的twice-nat44 XML"""
        rule_element = self.nat_generator._create_nat_rule_element(self.twice_nat44_rule)

        is_valid = self.nat_generator._validate_twice_nat44_xml(rule_element)
        self.assertTrue(is_valid)

    def test_validate_twice_nat44_xml_invalid(self):
        """测试验证无效的twice-nat44 XML"""
        # 创建缺少必需元素的XML
        rule_element = etree.Element("rule")
        twice_nat_elem = etree.SubElement(rule_element, "twice-nat44")

        # 只添加match元素，缺少snat和dnat
        match_elem = etree.SubElement(twice_nat_elem, "match")

        is_valid = self.nat_generator._validate_twice_nat44_xml(rule_element)
        self.assertFalse(is_valid)

    def test_generate_nat_xml_with_twice_nat44(self):
        """测试生成包含twice-nat44规则的NAT XML"""
        rules = [self.twice_nat44_rule]

        nat_xml = self.nat_generator.generate_nat_xml(rules)

        self.assertIsNotNone(nat_xml)
        self.assertEqual(nat_xml.tag, "nat")

        # 验证规则数量
        rule_elements = nat_xml.findall("rule")
        self.assertEqual(len(rule_elements), 1)

        # 验证twice-nat44配置
        rule_element = rule_elements[0]
        twice_nat_elem = rule_element.find("twice-nat44")
        self.assertIsNotNone(twice_nat_elem)


class TestTwiceNat44Validator(unittest.TestCase):
    """twice-nat44验证器单元测试"""

    def setUp(self):
        """测试初始化"""
        self.maxDiff = None
        self.validator = TwiceNat44Validator()

        # 创建有效的twice-nat44规则XML
        self.valid_rule_xml = """<rule>
            <name>valid_twice_nat44_rule</name>
            <rule_en>true</rule_en>
            <desc>有效的twice-nat44规则</desc>
            <twice-nat44>
                <match>
                    <dest-network>
                        <name>WEB_SERVER_VIP</name>
                    </dest-network>
                    <service>
                        <name>HTTPS</name>
                    </service>
                    <time-range>
                        <value>any</value>
                    </time-range>
                </match>
                <snat>
                    <output-address/>
                    <no-pat>false</no-pat>
                    <try-no-pat>true</try-no-pat>
                </snat>
                <dnat>
                    <ipv4-address>*************</ipv4-address>
                    <port>8443</port>
                </dnat>
            </twice-nat44>
        </rule>"""

        self.valid_rule_element = etree.fromstring(self.valid_rule_xml.encode('utf-8'))

    def test_validate_twice_nat44_rule_valid(self):
        """测试验证有效的twice-nat44规则"""
        is_valid, errors = self.validator.validate_twice_nat44_rule(self.valid_rule_element)

        self.assertTrue(is_valid)
        self.assertEqual(len(errors), 0)

    def test_validate_twice_nat44_rule_not_twice_nat44(self):
        """测试验证非twice-nat44规则"""
        non_twice_nat_xml = """<rule>
            <name>normal_rule</name>
            <rule_en>true</rule_en>
            <static-dnat44>
                <match>
                    <dest-network>
                        <name>NORMAL_VIP</name>
                    </dest-network>
                </match>
            </static-dnat44>
        </rule>"""

        non_twice_nat_element = etree.fromstring(non_twice_nat_xml.encode('utf-8'))
        is_valid, errors = self.validator.validate_twice_nat44_rule(non_twice_nat_element)

        self.assertFalse(is_valid)
        self.assertGreater(len(errors), 0)

    def test_validate_twice_nat44_rule_missing_elements(self):
        """测试验证缺少必需元素的规则"""
        missing_elements_xml = """<rule>
            <name>incomplete_rule</name>
            <rule_en>true</rule_en>
            <twice-nat44>
                <match>
                    <dest-network>
                        <name>TEST_VIP</name>
                    </dest-network>
                </match>
                <!-- 缺少snat和dnat元素 -->
            </twice-nat44>
        </rule>"""

        incomplete_element = etree.fromstring(missing_elements_xml.encode('utf-8'))
        is_valid, errors = self.validator.validate_twice_nat44_rule(incomplete_element)

        self.assertFalse(is_valid)
        self.assertGreater(len(errors), 0)

        # 检查是否包含缺少元素的错误
        error_text = " ".join(errors).lower()
        self.assertIn("snat", error_text)
        self.assertIn("dnat", error_text)

    def test_validate_twice_nat44_rule_invalid_ip(self):
        """测试验证包含无效IP地址的规则"""
        invalid_ip_xml = """<rule>
            <name>invalid_ip_rule</name>
            <rule_en>true</rule_en>
            <twice-nat44>
                <match>
                    <dest-network>
                        <name>TEST_VIP</name>
                    </dest-network>
                </match>
                <snat>
                    <ipv4-address>999.999.999.999</ipv4-address>
                    <no-pat>false</no-pat>
                </snat>
                <dnat>
                    <ipv4-address>invalid.ip.address</ipv4-address>
                    <port>8080</port>
                </dnat>
            </twice-nat44>
        </rule>"""

        invalid_ip_element = etree.fromstring(invalid_ip_xml.encode('utf-8'))
        is_valid, errors = self.validator.validate_twice_nat44_rule(invalid_ip_element)

        self.assertFalse(is_valid)
        self.assertGreater(len(errors), 0)

        # 检查是否包含IP地址相关的错误
        error_text = " ".join(errors).lower()
        self.assertIn("ipv4", error_text)

    def test_validate_twice_nat44_rule_invalid_boolean(self):
        """测试验证包含无效布尔值的规则"""
        invalid_boolean_xml = """<rule>
            <name>invalid_boolean_rule</name>
            <rule_en>not_a_boolean</rule_en>
            <twice-nat44>
                <match>
                    <dest-network>
                        <name>TEST_VIP</name>
                    </dest-network>
                </match>
                <snat>
                    <output-address/>
                    <no-pat>invalid_boolean</no-pat>
                </snat>
                <dnat>
                    <ipv4-address>*************</ipv4-address>
                </dnat>
            </twice-nat44>
        </rule>"""

        invalid_boolean_element = etree.fromstring(invalid_boolean_xml.encode('utf-8'))
        is_valid, errors = self.validator.validate_twice_nat44_rule(invalid_boolean_element)

        self.assertFalse(is_valid)
        self.assertGreater(len(errors), 0)

        # 检查是否包含布尔值相关的错误
        error_text = " ".join(errors).lower()
        self.assertIn("boolean", error_text)

    def test_is_valid_ipv4(self):
        """测试IPv4地址验证辅助方法"""
        # 测试有效IP地址
        valid_ips = ["***********", "********", "**********", "***********", "0.0.0.0", "***************"]
        for ip in valid_ips:
            self.assertTrue(self.validator._is_valid_ipv4(ip), f"{ip} should be valid")

        # 测试无效IP地址
        invalid_ips = ["999.999.999.999", "192.168.1", "***********.1", "invalid.ip", "", "256.1.1.1", "-*******"]
        for ip in invalid_ips:
            self.assertFalse(self.validator._is_valid_ipv4(ip), f"{ip} should be invalid")

    def test_validate_structure(self):
        """测试结构验证"""
        is_valid, errors = self.validator._validate_structure(
            self.valid_rule_element,
            self.valid_rule_element.find("twice-nat44")
        )

        self.assertTrue(is_valid)
        self.assertEqual(len(errors), 0)

        # 测试缺少规则名称的情况
        no_name_xml = """<rule>
            <rule_en>true</rule_en>
            <twice-nat44>
                <match><dest-network><name>TEST</name></dest-network></match>
                <snat><output-address/></snat>
                <dnat><ipv4-address>*******</ipv4-address></dnat>
            </twice-nat44>
        </rule>"""

        no_name_element = etree.fromstring(no_name_xml.encode('utf-8'))
        is_valid, errors = self.validator._validate_structure(
            no_name_element,
            no_name_element.find("twice-nat44")
        )

        self.assertFalse(is_valid)
        self.assertGreater(len(errors), 0)

    def test_validate_match_conditions(self):
        """测试匹配条件验证"""
        twice_nat_elem = self.valid_rule_element.find("twice-nat44")
        is_valid, errors = self.validator._validate_match_conditions(twice_nat_elem)

        self.assertTrue(is_valid)
        self.assertEqual(len(errors), 0)

        # 测试缺少dest-network的情况
        no_dest_xml = """<twice-nat44>
            <match>
                <service><name>HTTP</name></service>
            </match>
            <snat><output-address/></snat>
            <dnat><ipv4-address>*******</ipv4-address></dnat>
        </twice-nat44>"""

        no_dest_element = etree.fromstring(no_dest_xml.encode('utf-8'))
        is_valid, errors = self.validator._validate_match_conditions(no_dest_element)

        self.assertFalse(is_valid)
        self.assertGreater(len(errors), 0)

    def test_validate_snat_config(self):
        """测试SNAT配置验证"""
        twice_nat_elem = self.valid_rule_element.find("twice-nat44")
        is_valid, errors = self.validator._validate_snat_config(twice_nat_elem)

        self.assertTrue(is_valid)
        self.assertEqual(len(errors), 0)

        # 测试缺少地址类型的情况
        no_address_xml = """<twice-nat44>
            <match><dest-network><name>TEST</name></dest-network></match>
            <snat>
                <no-pat>false</no-pat>
            </snat>
            <dnat><ipv4-address>*******</ipv4-address></dnat>
        </twice-nat44>"""

        no_address_element = etree.fromstring(no_address_xml.encode('utf-8'))
        is_valid, errors = self.validator._validate_snat_config(no_address_element)

        self.assertFalse(is_valid)
        self.assertGreater(len(errors), 0)

    def test_validate_dnat_config(self):
        """测试DNAT配置验证"""
        twice_nat_elem = self.valid_rule_element.find("twice-nat44")
        is_valid, errors = self.validator._validate_dnat_config(twice_nat_elem)

        self.assertTrue(is_valid)
        self.assertEqual(len(errors), 0)

        # 测试缺少IP地址的情况
        no_ip_xml = """<twice-nat44>
            <match><dest-network><name>TEST</name></dest-network></match>
            <snat><output-address/></snat>
            <dnat>
                <port>8080</port>
            </dnat>
        </twice-nat44>"""

        no_ip_element = etree.fromstring(no_ip_xml.encode('utf-8'))
        is_valid, errors = self.validator._validate_dnat_config(no_ip_element)

        self.assertFalse(is_valid)
        self.assertGreater(len(errors), 0)


class TestTwiceNat44ConfigManager(unittest.TestCase):
    """twice-nat44配置管理单元测试"""

    def setUp(self):
        """测试初始化"""
        self.config_manager = ConfigManager()

    def test_get_twice_nat44_config(self):
        """测试获取twice-nat44配置"""
        # 测试默认配置
        use_twice_nat44 = self.config_manager.get_twice_nat44_config('use_twice_nat44')
        self.assertTrue(use_twice_nat44)

        fallback_enabled = self.config_manager.get_twice_nat44_config('twice_nat44_fallback')
        self.assertTrue(fallback_enabled)

        debug_mode = self.config_manager.get_twice_nat44_config('twice_nat44_debug')
        self.assertFalse(debug_mode)

        # 测试不存在的配置项
        unknown_config = self.config_manager.get_twice_nat44_config('unknown_key', 'default_value')
        self.assertEqual(unknown_config, 'default_value')

    def test_set_twice_nat44_config(self):
        """测试设置twice-nat44配置"""
        # 设置配置
        self.config_manager.set_twice_nat44_config('twice_nat44_debug', True)

        # 验证配置已更新
        debug_mode = self.config_manager.get_twice_nat44_config('twice_nat44_debug')
        self.assertTrue(debug_mode)

        # 恢复默认值
        self.config_manager.set_twice_nat44_config('twice_nat44_debug', False)

    def test_is_twice_nat44_enabled(self):
        """测试检查twice-nat44是否启用"""
        # 默认应该启用
        self.assertTrue(self.config_manager.is_twice_nat44_enabled())

        # 禁用后测试
        self.config_manager.set_twice_nat44_config('use_twice_nat44', False)
        self.assertFalse(self.config_manager.is_twice_nat44_enabled())

        # 恢复默认值
        self.config_manager.set_twice_nat44_config('use_twice_nat44', True)

    def test_is_twice_nat44_fallback_enabled(self):
        """测试检查twice-nat44回退机制是否启用"""
        # 默认应该启用
        self.assertTrue(self.config_manager.is_twice_nat44_fallback_enabled())

        # 禁用后测试
        self.config_manager.set_twice_nat44_config('twice_nat44_fallback', False)
        self.assertFalse(self.config_manager.is_twice_nat44_fallback_enabled())

        # 恢复默认值
        self.config_manager.set_twice_nat44_config('twice_nat44_fallback', True)

    def test_validate_twice_nat44_config(self):
        """测试验证twice-nat44配置"""
        # 默认配置应该有效
        self.assertTrue(self.config_manager.validate_twice_nat44_config())

        # 设置无效配置类型
        self.config_manager.set_twice_nat44_config('use_twice_nat44', 'invalid_type')
        self.assertFalse(self.config_manager.validate_twice_nat44_config())

        # 恢复有效配置
        self.config_manager.set_twice_nat44_config('use_twice_nat44', True)
        self.assertTrue(self.config_manager.validate_twice_nat44_config())


class TestTwiceNat44Integration(unittest.TestCase):
    """twice-nat44集成单元测试"""

    def setUp(self):
        """测试初始化"""
        self.maxDiff = None

    def test_end_to_end_data_flow(self):
        """测试端到端数据流"""
        # 步骤1：创建FortiGate配置
        fortigate_policy = {
            "name": "INTEGRATION_TEST_POLICY",
            "status": "enable",
            "service": ["HTTP"],
            "schedule": "always",
            "fixedport": "disable",
            "nat": "enable",
            "dstaddr": ["INTEGRATION_VIP"]
        }

        vip_config = {
            "name": "INTEGRATION_VIP",
            "extip": "************",
            "mappedip": "************",
            "extport": "80",
            "mappedport": "8080",
            "protocol": "tcp"
        }

        # 步骤2：使用数据模型转换
        rule = TwiceNat44Rule.from_fortigate_policy(fortigate_policy, vip_config)
        self.assertIsNotNone(rule)
        self.assertEqual(rule.name, "INTEGRATION_TEST_POLICY_INTEGRATION_VIP_twice_nat")

        # 步骤3：转换为NAT规则字典
        rule_dict = rule.to_nat_rule_dict()
        self.assertEqual(rule_dict["type"], "twice-nat44")
        self.assertIn("twice-nat44", rule_dict)

        # 步骤4：使用NAT生成器生成XML
        nat_generator = NATGenerator()
        rule_element = nat_generator._create_nat_rule_element(rule_dict)
        self.assertIsNotNone(rule_element)

        # 步骤5：使用验证器验证XML
        validator = TwiceNat44Validator()
        is_valid, errors = validator.validate_twice_nat44_rule(rule_element)
        self.assertTrue(is_valid)
        self.assertEqual(len(errors), 0)

        # 步骤6：验证XML内容
        name_elem = rule_element.find("name")
        self.assertEqual(name_elem.text, "INTEGRATION_TEST_POLICY_INTEGRATION_VIP_twice_nat")

        twice_nat_elem = rule_element.find("twice-nat44")
        self.assertIsNotNone(twice_nat_elem)

        dnat_ip_elem = twice_nat_elem.find(".//dnat/ipv4-address")
        self.assertEqual(dnat_ip_elem.text, "************")

        dnat_port_elem = twice_nat_elem.find(".//dnat/port")
        self.assertEqual(dnat_port_elem.text, "8080")

    def test_error_handling_chain(self):
        """测试错误处理链"""
        # 测试无效FortiGate配置
        invalid_policy = {
            "name": "INVALID_POLICY"
        }

        invalid_vip = {
            "name": "INVALID_VIP"
            # 缺少mappedip
        }

        with self.assertRaises(TwiceNat44ConfigError):
            TwiceNat44Rule.from_fortigate_policy(invalid_policy, invalid_vip)

    def test_configuration_integration(self):
        """测试配置集成"""
        config_manager = ConfigManager()

        # 测试配置读取
        self.assertTrue(config_manager.is_twice_nat44_enabled())
        self.assertTrue(config_manager.is_twice_nat44_fallback_enabled())

        # 测试配置修改
        config_manager.set_twice_nat44_config('use_twice_nat44', False)
        self.assertFalse(config_manager.is_twice_nat44_enabled())

        # 恢复配置
        config_manager.set_twice_nat44_config('use_twice_nat44', True)

    def test_xml_serialization_roundtrip(self):
        """测试XML序列化往返"""
        # 创建规则
        rule_dict = {
            "name": "roundtrip_test_rule",
            "type": "twice-nat44",
            "rule_en": True,
            "desc": "往返测试规则",
            "twice-nat44": {
                "match": {
                    "dest-network": {"name": "ROUNDTRIP_VIP"},
                    "service": {"name": "HTTP"}
                },
                "snat": {
                    "output-address": {},
                    "no-pat": False,
                    "try-no-pat": True
                },
                "dnat": {
                    "ipv4-address": "**********",
                    "port": 9000
                }
            }
        }

        # 生成XML
        nat_generator = NATGenerator()
        rule_element = nat_generator._create_nat_rule_element(rule_dict)

        # 序列化
        xml_string = etree.tostring(rule_element, encoding='unicode', pretty_print=True)

        # 重新解析
        reparsed_element = etree.fromstring(xml_string.encode('utf-8'))

        # 验证
        validator = TwiceNat44Validator()
        is_valid, errors = validator.validate_twice_nat44_rule(reparsed_element)
        self.assertTrue(is_valid)
        self.assertEqual(len(errors), 0)

        # 验证内容一致性
        original_name = rule_element.find("name").text
        reparsed_name = reparsed_element.find("name").text
        self.assertEqual(original_name, reparsed_name)


def create_test_suite():
    """创建测试套件"""
    suite = unittest.TestSuite()

    # 添加数据模型测试
    suite.addTest(unittest.makeSuite(TestTwiceNat44DataModels))
    suite.addTest(unittest.makeSuite(TestTwiceNat44Rule))

    # 添加FortiGate转换测试
    suite.addTest(unittest.makeSuite(TestTwiceNat44FortigateConversion))

    # 添加NAT生成器测试
    suite.addTest(unittest.makeSuite(TestTwiceNat44NATGenerator))

    # 添加验证器测试
    suite.addTest(unittest.makeSuite(TestTwiceNat44Validator))

    # 添加配置管理测试
    suite.addTest(unittest.makeSuite(TestTwiceNat44ConfigManager))

    # 添加集成测试
    suite.addTest(unittest.makeSuite(TestTwiceNat44Integration))

    return suite


def run_tests():
    """运行测试"""
    runner = unittest.TextTestRunner(verbosity=2)
    suite = create_test_suite()
    result = runner.run(suite)

    return result.wasSuccessful()


if __name__ == '__main__':
    print("🚀 开始twice-nat44单元测试")
    print("=" * 80)

    success = run_tests()

    print("\n" + "=" * 80)
    if success:
        print("🎉 所有单元测试通过！")
        exit(0)
    else:
        print("❌ 部分单元测试失败！")
        exit(1)
