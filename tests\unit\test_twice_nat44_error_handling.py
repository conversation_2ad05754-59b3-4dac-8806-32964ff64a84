"""
FortiGate twice-nat44错误处理单元测试

测试twice-nat44错误处理框架的各种功能，包括：
- 错误分类和严重程度判断
- 错误恢复机制
- 错误处理装饰器
- 性能监控
- 错误统计和报告
"""

import unittest
import sys
import os
import time
import threading
from unittest.mock import Mock, patch, MagicMock
from lxml import etree

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from engine.infrastructure.error_handling import (
    TwiceNat44Error<PERSON>and<PERSON>, TwiceNat44ErrorContext, TwiceNat44ErrorType,
    TwiceNat44ErrorSeverity, TwiceNat44ErrorRecord, get_twice_nat44_error_handler,
    handle_twice_nat44_error, twice_nat44_error_handler, twice_nat44_performance_monitor,
    twice_nat44_validation_required, twice_nat44_thread_safe, twice_nat44_cache_result,
    twice_nat44_operation_context
)
from engine.business.models.twice_nat44_models import TwiceNat44ConfigError, TwiceNat44ValidationError


class TestTwiceNat44ErrorHandler(unittest.TestCase):
    """twice-nat44错误处理器单元测试"""
    
    def setUp(self):
        """测试初始化"""
        self.maxDiff = None
        self.error_handler = TwiceNat44ErrorHandler(max_error_history=100)
    
    def test_error_handler_initialization(self):
        """测试错误处理器初始化"""
        handler = TwiceNat44ErrorHandler(max_error_history=50)
        
        self.assertEqual(handler.max_error_history, 50)
        self.assertEqual(len(handler._error_history), 0)
        self.assertEqual(len(handler._error_stats), 0)
        self.assertIsNotNone(handler._recovery_strategies)
        self.assertIsInstance(handler._lock, threading.Lock)
    
    def test_error_classification(self):
        """测试错误分类"""
        # 测试配置错误
        config_error = TwiceNat44ConfigError("Invalid configuration")
        error_type = self.error_handler._classify_error(config_error)
        self.assertEqual(error_type, TwiceNat44ErrorType.CONFIG_ERROR)
        
        # 测试验证错误
        validation_error = TwiceNat44ValidationError("Validation failed")
        error_type = self.error_handler._classify_error(validation_error)
        self.assertEqual(error_type, TwiceNat44ErrorType.VALIDATION_ERROR)
        
        # 测试转换错误
        value_error = ValueError("Invalid value")
        error_type = self.error_handler._classify_error(value_error)
        self.assertEqual(error_type, TwiceNat44ErrorType.CONVERSION_ERROR)
        
        # 测试XML生成错误
        attribute_error = AttributeError("Missing attribute")
        error_type = self.error_handler._classify_error(attribute_error)
        self.assertEqual(error_type, TwiceNat44ErrorType.XML_GENERATION_ERROR)
        
        # 测试资源错误
        memory_error = MemoryError("Out of memory")
        error_type = self.error_handler._classify_error(memory_error)
        self.assertEqual(error_type, TwiceNat44ErrorType.RESOURCE_ERROR)
        
        # 测试网络错误
        connection_error = ConnectionError("Connection failed")
        error_type = self.error_handler._classify_error(connection_error)
        self.assertEqual(error_type, TwiceNat44ErrorType.NETWORK_ERROR)
        
        # 测试未知错误
        runtime_error = RuntimeError("Unknown error")
        error_type = self.error_handler._classify_error(runtime_error)
        self.assertEqual(error_type, TwiceNat44ErrorType.UNKNOWN_ERROR)
    
    def test_severity_determination(self):
        """测试错误严重程度判断"""
        context = TwiceNat44ErrorContext(operation="test_operation")
        
        # 测试基础严重程度
        config_error = TwiceNat44ConfigError("Config error")
        severity = self.error_handler._determine_severity(
            config_error, TwiceNat44ErrorType.CONFIG_ERROR, context
        )
        self.assertEqual(severity, TwiceNat44ErrorSeverity.ERROR)
        
        # 测试重试次数影响严重程度
        context.retry_count = 3
        context.max_retries = 3
        severity = self.error_handler._determine_severity(
            config_error, TwiceNat44ErrorType.CONFIG_ERROR, context
        )
        self.assertEqual(severity, TwiceNat44ErrorSeverity.CRITICAL)
        
        # 测试关键操作影响严重程度
        context.retry_count = 0
        context.operation = "batch_conversion"
        severity = self.error_handler._determine_severity(
            config_error, TwiceNat44ErrorType.CONFIG_ERROR, context
        )
        self.assertEqual(severity, TwiceNat44ErrorSeverity.CRITICAL)
    
    def test_error_id_generation(self):
        """测试错误ID生成"""
        context = TwiceNat44ErrorContext(operation="test operation")
        error_id = self.error_handler._generate_error_id(TwiceNat44ErrorType.CONFIG_ERROR, context)
        
        self.assertIn("TN44_CONFIG_ERROR", error_id)
        self.assertIn("test_operation", error_id)
        self.assertTrue(len(error_id) > 20)  # 应该包含时间戳
    
    def test_technical_details_extraction(self):
        """测试技术详情提取"""
        exception = TwiceNat44ConfigError("Test error message")
        exception.custom_field = "custom_value"
        
        details = self.error_handler._extract_technical_details(exception)
        
        self.assertIn("Exception Type: TwiceNat44ConfigError", details)
        self.assertIn("Exception Message: Test error message", details)
        self.assertIn("custom_field: custom_value", details)
    
    def test_error_handling_workflow(self):
        """测试完整的错误处理工作流"""
        context = TwiceNat44ErrorContext(
            operation="test_operation",
            policy_name="TEST_POLICY",
            vip_name="TEST_VIP"
        )
        
        exception = TwiceNat44ConfigError("Test configuration error")
        
        result = self.error_handler.handle_twice_nat44_error(exception, context)
        
        # 验证返回结果
        self.assertIn('error_record', result)
        self.assertIn('error_id', result)
        self.assertIn('error_type', result)
        self.assertIn('severity', result)
        self.assertIn('user_message', result)
        self.assertIn('suggested_action', result)
        self.assertTrue(result['handled'])
        
        # 验证错误记录
        error_record = result['error_record']
        self.assertEqual(error_record.error_type, TwiceNat44ErrorType.CONFIG_ERROR)
        self.assertEqual(error_record.context.operation, "test_operation")
        self.assertEqual(error_record.context.policy_name, "TEST_POLICY")
        
        # 验证错误历史
        self.assertEqual(len(self.error_handler._error_history), 1)
        self.assertEqual(self.error_handler._error_history[0], error_record)
    
    def test_recovery_mechanisms(self):
        """测试错误恢复机制"""
        # 测试配置错误恢复
        context = TwiceNat44ErrorContext(
            operation="test_operation",
            policy_name="TEST_POLICY"
        )
        
        error_record = TwiceNat44ErrorRecord(
            timestamp=time.time(),
            error_id="test_error_id",
            error_type=TwiceNat44ErrorType.CONFIG_ERROR,
            error_severity=TwiceNat44ErrorSeverity.ERROR,
            error_message="Invalid IPv4 address",
            technical_details="Test details",
            context=context,
            stack_trace="Test stack trace",
            recovery_attempted=True,
            recovery_successful=False
        )
        
        recovery_result = self.error_handler._recover_config_error(error_record)
        
        self.assertTrue(recovery_result['success'])
        self.assertEqual(recovery_result['strategy'], 'use_default_ip')
        self.assertIn('default_ip', recovery_result['details'])
    
    def test_error_statistics(self):
        """测试错误统计"""
        # 添加一些错误记录
        for i in range(5):
            context = TwiceNat44ErrorContext(operation=f"test_operation_{i}")
            exception = TwiceNat44ConfigError(f"Test error {i}")
            self.error_handler.handle_twice_nat44_error(exception, context)
        
        stats = self.error_handler.get_error_statistics()
        
        self.assertEqual(stats['total_errors'], 5)
        self.assertIn('error_types', stats)
        self.assertIn('recovery_success_rate', stats)
        self.assertIn('most_common_errors', stats)
    
    def test_global_error_handler(self):
        """测试全局错误处理器"""
        handler1 = get_twice_nat44_error_handler()
        handler2 = get_twice_nat44_error_handler()
        
        # 应该返回同一个实例
        self.assertIs(handler1, handler2)
    
    def test_convenience_function(self):
        """测试便捷函数"""
        context = TwiceNat44ErrorContext(operation="test_operation")
        exception = TwiceNat44ConfigError("Test error")
        
        result = handle_twice_nat44_error(exception, context)
        
        self.assertIn('error_id', result)
        self.assertTrue(result['handled'])


class TestTwiceNat44ErrorDecorators(unittest.TestCase):
    """twice-nat44错误处理装饰器单元测试"""
    
    def setUp(self):
        """测试初始化"""
        self.maxDiff = None
    
    def test_error_handler_decorator_success(self):
        """测试错误处理装饰器成功场景"""
        @twice_nat44_error_handler(operation="test_operation", max_retries=2)
        def test_function(value):
            return value * 2
        
        result = test_function(5)
        self.assertEqual(result, 10)
    
    def test_error_handler_decorator_retry(self):
        """测试错误处理装饰器重试机制"""
        call_count = 0
        
        @twice_nat44_error_handler(
            operation="test_operation", 
            max_retries=2,
            retry_delay=0.01,
            handle_exceptions=(ValueError,)
        )
        def test_function():
            nonlocal call_count
            call_count += 1
            if call_count < 3:
                raise ValueError("Test error")
            return "success"
        
        result = test_function()
        self.assertEqual(result, "success")
        self.assertEqual(call_count, 3)
    
    def test_error_handler_decorator_fallback(self):
        """测试错误处理装饰器回退值"""
        @twice_nat44_error_handler(
            operation="test_operation",
            max_retries=1,
            fallback_value="fallback_result",
            handle_exceptions=(ValueError,)
        )
        def test_function():
            raise ValueError("Test error")
        
        result = test_function()
        self.assertEqual(result, "fallback_result")
    
    def test_performance_monitor_decorator(self):
        """测试性能监控装饰器"""
        @twice_nat44_performance_monitor(
            operation="test_operation",
            performance_threshold=0.1
        )
        def test_function():
            time.sleep(0.01)
            return "result"
        
        result = test_function()
        self.assertEqual(result, "result")
    
    def test_validation_required_decorator(self):
        """测试验证装饰器"""
        @twice_nat44_validation_required(validate_input=True)
        def test_function(value):
            return value
        
        # 正常情况
        result = test_function("valid_value")
        self.assertEqual(result, "valid_value")
        
        # None值应该抛出异常
        with self.assertRaises(ValueError):
            test_function(None)
    
    def test_thread_safe_decorator(self):
        """测试线程安全装饰器"""
        shared_value = 0
        
        @twice_nat44_thread_safe()
        def increment():
            nonlocal shared_value
            temp = shared_value
            time.sleep(0.001)  # 模拟竞态条件
            shared_value = temp + 1
        
        threads = []
        for _ in range(10):
            thread = threading.Thread(target=increment)
            threads.append(thread)
            thread.start()
        
        for thread in threads:
            thread.join()
        
        self.assertEqual(shared_value, 10)
    
    def test_cache_result_decorator(self):
        """测试结果缓存装饰器"""
        call_count = 0
        
        @twice_nat44_cache_result(cache_ttl=1)
        def expensive_function(value):
            nonlocal call_count
            call_count += 1
            return value * 2
        
        # 第一次调用
        result1 = expensive_function(5)
        self.assertEqual(result1, 10)
        self.assertEqual(call_count, 1)
        
        # 第二次调用应该使用缓存
        result2 = expensive_function(5)
        self.assertEqual(result2, 10)
        self.assertEqual(call_count, 1)  # 没有增加
        
        # 不同参数应该重新计算
        result3 = expensive_function(10)
        self.assertEqual(result3, 20)
        self.assertEqual(call_count, 2)
    
    def test_operation_context_manager(self):
        """测试操作上下文管理器"""
        with twice_nat44_operation_context("test_operation", policy_name="TEST_POLICY") as context:
            self.assertEqual(context.operation, "test_operation")
            self.assertEqual(context.policy_name, "TEST_POLICY")
            
            # 模拟一些操作
            time.sleep(0.01)
        
        # 上下文应该记录性能指标
        self.assertIn('total_time', context.performance_metrics)
        self.assertGreater(context.performance_metrics['total_time'], 0)


class TestTwiceNat44ErrorIntegration(unittest.TestCase):
    """twice-nat44错误处理集成测试"""
    
    def setUp(self):
        """测试初始化"""
        self.maxDiff = None
    
    def test_error_handling_with_data_models(self):
        """测试错误处理与数据模型的集成"""
        from engine.business.models.twice_nat44_models import TwiceNat44Rule
        
        # 测试无效配置应该被错误处理器捕获
        invalid_policy = {"name": "INVALID_POLICY"}
        invalid_vip = {"name": "INVALID_VIP"}  # 缺少mappedip
        
        with self.assertRaises(TwiceNat44ConfigError):
            TwiceNat44Rule.from_fortigate_policy(invalid_policy, invalid_vip)
    
    def test_error_handling_with_generators(self):
        """测试错误处理与生成器的集成"""
        from engine.generators.nat_generator import NATGenerator
        
        generator = NATGenerator()
        
        # 测试无效规则应该被错误处理器处理
        invalid_rule = {"name": "INVALID_RULE"}  # 缺少必需字段
        
        try:
            result = generator._create_nat_rule_element(invalid_rule)
            # 如果没有抛出异常，结果应该是None或有错误处理
            self.assertIsNone(result)
        except Exception:
            # 异常应该被装饰器处理
            pass
    
    def test_error_handling_with_validators(self):
        """测试错误处理与验证器的集成"""
        from engine.validators.twice_nat44_validator import TwiceNat44Validator
        
        validator = TwiceNat44Validator()
        
        # 测试无效XML应该被错误处理器处理
        invalid_xml = "<invalid>xml</invalid>"
        invalid_element = etree.fromstring(invalid_xml.encode('utf-8'))
        
        is_valid, errors = validator.validate_twice_nat44_rule(invalid_element)
        
        # 应该返回验证失败
        self.assertFalse(is_valid)
        self.assertGreater(len(errors), 0)


def create_error_handling_test_suite():
    """创建错误处理测试套件"""
    suite = unittest.TestSuite()
    
    # 添加错误处理器测试
    suite.addTest(unittest.makeSuite(TestTwiceNat44ErrorHandler))
    
    # 添加装饰器测试
    suite.addTest(unittest.makeSuite(TestTwiceNat44ErrorDecorators))
    
    # 添加集成测试
    suite.addTest(unittest.makeSuite(TestTwiceNat44ErrorIntegration))
    
    return suite


def run_error_handling_tests():
    """运行错误处理测试"""
    runner = unittest.TextTestRunner(verbosity=2)
    suite = create_error_handling_test_suite()
    result = runner.run(suite)
    
    return result.wasSuccessful()


if __name__ == '__main__':
    print("🚀 开始twice-nat44错误处理单元测试")
    print("=" * 80)
    
    success = run_error_handling_tests()
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 所有错误处理测试通过！")
        exit(0)
    else:
        print("❌ 部分错误处理测试失败！")
        exit(1)
