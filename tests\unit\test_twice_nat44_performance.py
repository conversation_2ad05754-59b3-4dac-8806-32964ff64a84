"""
FortiGate twice-nat44性能优化单元测试

测试twice-nat44性能优化功能，包括：
- 批量处理优化
- 内存使用优化
- 缓存机制
- 对象池管理
- 性能监控
"""

import unittest
import sys
import os
import time
import threading
from unittest.mock import Mock, patch

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from engine.infrastructure.performance import (
    PerformanceMetrics, TwiceNat44ObjectPool, TwiceNat44Cache,
    TwiceNat44PerformanceOptimizer, get_twice_nat44_optimizer
)


class TestPerformanceMetrics(unittest.TestCase):
    """性能指标单元测试"""
    
    def setUp(self):
        """测试初始化"""
        self.start_time = time.time()
        self.end_time = self.start_time + 1.5
        
        self.metrics = PerformanceMetrics(
            operation="test_operation",
            start_time=self.start_time,
            end_time=self.end_time,
            memory_before=100.0,
            memory_after=120.0,
            cpu_usage=50.0,
            rule_count=100,
            success_count=95,
            error_count=5
        )
    
    def test_execution_time(self):
        """测试执行时间计算"""
        self.assertAlmostEqual(self.metrics.execution_time, 1.5, places=1)
    
    def test_memory_delta(self):
        """测试内存变化计算"""
        self.assertEqual(self.metrics.memory_delta, 20.0)
    
    def test_throughput(self):
        """测试吞吐量计算"""
        expected_throughput = 100 / 1.5
        self.assertAlmostEqual(self.metrics.throughput, expected_throughput, places=1)
    
    def test_success_rate(self):
        """测试成功率计算"""
        self.assertEqual(self.metrics.success_rate, 95.0)
    
    def test_zero_execution_time(self):
        """测试零执行时间的处理"""
        zero_time_metrics = PerformanceMetrics(
            operation="zero_time",
            start_time=self.start_time,
            end_time=self.start_time,  # 相同时间
            memory_before=100.0,
            memory_after=100.0,
            cpu_usage=0.0,
            rule_count=10,
            success_count=10,
            error_count=0
        )
        
        self.assertEqual(zero_time_metrics.throughput, 0.0)


class TestTwiceNat44ObjectPool(unittest.TestCase):
    """twice-nat44对象池单元测试"""
    
    def setUp(self):
        """测试初始化"""
        self.pool = TwiceNat44ObjectPool(max_size=5)
    
    def test_object_creation_and_reuse(self):
        """测试对象创建和重用"""
        # 创建对象工厂
        def create_test_object():
            return {"id": time.time(), "data": "test"}
        
        # 获取新对象
        obj1 = self.pool.get_object("test_type", create_test_object)
        self.assertIsNotNone(obj1)
        
        # 返回对象到池中
        self.pool.return_object("test_type", obj1)
        
        # 再次获取应该是同一个对象
        obj2 = self.pool.get_object("test_type", create_test_object)
        self.assertEqual(obj1["id"], obj2["id"])
    
    def test_pool_size_limit(self):
        """测试对象池大小限制"""
        def create_test_object():
            return {"id": time.time()}
        
        # 创建并返回超过最大大小的对象
        objects = []
        for i in range(10):
            obj = self.pool.get_object("test_type", create_test_object)
            objects.append(obj)
        
        # 返回所有对象
        for obj in objects:
            self.pool.return_object("test_type", obj)
        
        # 验证统计信息
        stats = self.pool.get_stats()
        self.assertEqual(stats["test_type_created"], 10)
        self.assertEqual(stats["test_type_returned"], 5)  # 只有5个被保留
        self.assertEqual(stats["test_type_discarded"], 5)  # 5个被丢弃
    
    def test_object_reset(self):
        """测试对象重置功能"""
        class TestObject:
            def __init__(self):
                self.value = "initial"
            
            def reset(self):
                self.value = "reset"
        
        def create_test_object():
            return TestObject()
        
        # 获取对象并修改
        obj = self.pool.get_object("resettable", create_test_object)
        obj.value = "modified"
        
        # 返回对象
        self.pool.return_object("resettable", obj)
        
        # 再次获取，应该已经重置
        obj2 = self.pool.get_object("resettable", create_test_object)
        self.assertEqual(obj2.value, "reset")
    
    def test_clear_pool(self):
        """测试清空对象池"""
        def create_test_object():
            return {"data": "test"}
        
        # 添加一些对象
        for i in range(3):
            obj = self.pool.get_object("test_type", create_test_object)
            self.pool.return_object("test_type", obj)
        
        # 清空池
        self.pool.clear()
        
        # 验证统计信息被清空
        stats = self.pool.get_stats()
        self.assertEqual(len(stats), 0)


class TestTwiceNat44Cache(unittest.TestCase):
    """twice-nat44缓存单元测试"""
    
    def setUp(self):
        """测试初始化"""
        self.cache = TwiceNat44Cache(max_size=5, ttl=1)  # 1秒TTL
    
    def test_cache_put_and_get(self):
        """测试缓存存取"""
        # 存储值
        self.cache.put("key1", "value1")
        
        # 获取值
        value = self.cache.get("key1")
        self.assertEqual(value, "value1")
        
        # 获取不存在的键
        value = self.cache.get("nonexistent")
        self.assertIsNone(value)
    
    def test_cache_ttl_expiration(self):
        """测试缓存TTL过期"""
        # 存储值
        self.cache.put("key1", "value1")
        
        # 立即获取应该成功
        value = self.cache.get("key1")
        self.assertEqual(value, "value1")
        
        # 等待过期
        time.sleep(1.1)
        
        # 再次获取应该返回None
        value = self.cache.get("key1")
        self.assertIsNone(value)
    
    def test_cache_lru_eviction(self):
        """测试LRU驱逐机制"""
        import time

        # 填满缓存
        for i in range(5):
            self.cache.put(f"key{i}", f"value{i}")
            time.sleep(0.001)  # 确保时间戳不同

        # 访问key0以更新其访问时间
        time.sleep(0.001)
        self.cache.get("key0")

        # 添加新键，应该驱逐最久未访问的键
        self.cache.put("key5", "value5")

        # 验证缓存大小仍然是5
        stats = self.cache.get_stats()
        self.assertEqual(stats["cache_size"], 5)

        # key0应该仍然存在（因为最近被访问）
        self.assertEqual(self.cache.get("key0"), "value0")

        # key5应该存在
        self.assertEqual(self.cache.get("key5"), "value5")

        # 验证驱逐统计
        self.assertEqual(stats["evictions"], 1)
    
    def test_cache_stats(self):
        """测试缓存统计"""
        # 执行一些操作
        self.cache.put("key1", "value1")
        self.cache.get("key1")  # 命中
        self.cache.get("key2")  # 未命中
        
        stats = self.cache.get_stats()
        
        self.assertEqual(stats["hits"], 1)
        self.assertEqual(stats["misses"], 1)
        self.assertEqual(stats["cache_size"], 1)
        self.assertEqual(stats["hit_rate"], 50.0)
    
    def test_cache_clear(self):
        """测试缓存清空"""
        # 添加一些数据
        for i in range(3):
            self.cache.put(f"key{i}", f"value{i}")
        
        # 清空缓存
        self.cache.clear()
        
        # 验证缓存为空
        stats = self.cache.get_stats()
        self.assertEqual(stats["cache_size"], 0)


class TestTwiceNat44PerformanceOptimizer(unittest.TestCase):
    """twice-nat44性能优化器单元测试"""
    
    def setUp(self):
        """测试初始化"""
        self.optimizer = TwiceNat44PerformanceOptimizer()
    
    def test_optimizer_initialization(self):
        """测试优化器初始化"""
        self.assertIsNotNone(self.optimizer.object_pool)
        self.assertIsNotNone(self.optimizer.cache)
        self.assertEqual(self.optimizer.batch_size, 100)
        self.assertGreater(self.optimizer.max_workers, 0)
    
    @patch('psutil.cpu_count')
    def test_batch_processing_optimization(self, mock_cpu_count):
        """测试批量处理优化"""
        mock_cpu_count.return_value = 4
        
        # 创建测试规则
        rules = [{"id": i, "name": f"rule_{i}"} for i in range(10)]
        
        # 定义处理函数
        def test_processor(rule):
            time.sleep(0.001)  # 模拟处理时间
            return {"processed_id": rule["id"], "result": "success"}
        
        # 执行批量处理
        results, metrics = self.optimizer.optimize_batch_processing(rules, test_processor)
        
        # 验证结果
        self.assertEqual(len(results), 10)
        self.assertEqual(metrics.rule_count, 10)
        self.assertEqual(metrics.success_count, 10)
        self.assertEqual(metrics.error_count, 0)
        self.assertGreater(metrics.throughput, 0)
    
    def test_batch_processing_with_errors(self):
        """测试包含错误的批量处理"""
        rules = [{"id": i, "name": f"rule_{i}"} for i in range(5)]
        
        def error_processor(rule):
            if rule["id"] == 2:
                raise ValueError("Test error")
            return {"processed_id": rule["id"]}
        
        results, metrics = self.optimizer.optimize_batch_processing(rules, error_processor)
        
        # 验证结果
        self.assertEqual(len(results), 4)  # 4个成功
        self.assertEqual(metrics.success_count, 4)
        self.assertEqual(metrics.error_count, 1)
    
    def test_cache_integration(self):
        """测试缓存集成"""
        rules = [{"id": 1, "name": "test_rule"}]
        
        call_count = 0
        
        def cached_processor(rule):
            nonlocal call_count
            call_count += 1
            return {"processed_id": rule["id"], "call_count": call_count}
        
        # 第一次处理
        results1, _ = self.optimizer.optimize_batch_processing(rules, cached_processor)
        
        # 第二次处理相同规则（应该使用缓存）
        results2, _ = self.optimizer.optimize_batch_processing(rules, cached_processor)
        
        # 验证缓存效果
        self.assertEqual(len(results1), 1)
        self.assertEqual(len(results2), 1)
        
        # 缓存统计应该显示命中
        stats = self.optimizer.get_performance_stats()
        cache_stats = stats.get('cache_stats', {})
        self.assertGreater(cache_stats.get('hits', 0), 0)
    
    def test_memory_optimization(self):
        """测试内存优化"""
        # 添加一些缓存数据，其中一些是过期的
        import time

        # 添加一些旧数据（手动设置过期时间戳）
        for i in range(5):
            self.optimizer.cache.put(f"old_key{i}", f"old_value{i}")
            # 手动设置为过期时间戳
            self.optimizer.cache._timestamps[f"old_key{i}"] = time.time() - 3700  # 超过TTL

        # 添加一些新数据
        for i in range(5):
            self.optimizer.cache.put(f"new_key{i}", f"new_value{i}")

        initial_stats = self.optimizer.cache.get_stats()
        initial_size = initial_stats["cache_size"]

        # 执行内存优化
        self.optimizer.optimize_memory_usage()

        # 验证过期缓存被清理，但新缓存保留
        final_stats = self.optimizer.cache.get_stats()
        final_size = final_stats["cache_size"]

        # 应该清理了过期的缓存项
        self.assertLess(final_size, initial_size)
    
    def test_performance_stats(self):
        """测试性能统计"""
        # 执行一些操作以生成统计数据
        rules = [{"id": i} for i in range(5)]
        
        def simple_processor(rule):
            return {"id": rule["id"]}
        
        self.optimizer.optimize_batch_processing(rules, simple_processor)
        
        # 获取统计信息
        stats = self.optimizer.get_performance_stats()
        
        self.assertIn('total_operations', stats)
        self.assertIn('total_rules_processed', stats)
        self.assertIn('average_throughput', stats)
        self.assertIn('cache_stats', stats)
        self.assertIn('object_pool_stats', stats)
        
        self.assertEqual(stats['total_operations'], 1)
        self.assertEqual(stats['total_rules_processed'], 5)
    
    def test_global_optimizer_instance(self):
        """测试全局优化器实例"""
        optimizer1 = get_twice_nat44_optimizer()
        optimizer2 = get_twice_nat44_optimizer()
        
        # 应该返回同一个实例
        self.assertIs(optimizer1, optimizer2)


class TestPerformanceDecorators(unittest.TestCase):
    """性能优化装饰器单元测试"""
    
    def setUp(self):
        """测试初始化"""
        self.maxDiff = None
    
    def test_performance_optimized_decorator_import(self):
        """测试性能优化装饰器导入"""
        try:
            from engine.infrastructure.error_handling import (
                twice_nat44_performance_optimized, twice_nat44_memory_optimized
            )
            self.assertTrue(True)  # 导入成功
        except ImportError as e:
            self.fail(f"装饰器导入失败: {str(e)}")
    
    @patch('psutil.Process')
    def test_memory_optimized_decorator(self, mock_process):
        """测试内存优化装饰器"""
        from engine.infrastructure.error_handling import twice_nat44_memory_optimized
        
        # 模拟内存使用
        mock_memory_info = Mock()
        mock_memory_info.rss = 1024 * 1024 * 1024  # 1GB
        mock_process.return_value.memory_info.return_value = mock_memory_info
        
        call_count = 0
        
        @twice_nat44_memory_optimized(gc_threshold=2, memory_limit=500.0)
        def test_function():
            nonlocal call_count
            call_count += 1
            return f"call_{call_count}"
        
        # 调用函数多次
        result1 = test_function()
        result2 = test_function()
        result3 = test_function()
        
        self.assertEqual(result1, "call_1")
        self.assertEqual(result2, "call_2")
        self.assertEqual(result3, "call_3")


def create_performance_test_suite():
    """创建性能测试套件"""
    suite = unittest.TestSuite()
    
    # 添加性能指标测试
    suite.addTest(unittest.makeSuite(TestPerformanceMetrics))
    
    # 添加对象池测试
    suite.addTest(unittest.makeSuite(TestTwiceNat44ObjectPool))
    
    # 添加缓存测试
    suite.addTest(unittest.makeSuite(TestTwiceNat44Cache))
    
    # 添加优化器测试
    suite.addTest(unittest.makeSuite(TestTwiceNat44PerformanceOptimizer))
    
    # 添加装饰器测试
    suite.addTest(unittest.makeSuite(TestPerformanceDecorators))
    
    return suite


def run_performance_tests():
    """运行性能测试"""
    runner = unittest.TextTestRunner(verbosity=2)
    suite = create_performance_test_suite()
    result = runner.run(suite)
    
    return result.wasSuccessful()


if __name__ == '__main__':
    print("🚀 开始twice-nat44性能优化单元测试")
    print("=" * 80)
    
    success = run_performance_tests()
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 所有性能优化测试通过！")
        exit(0)
    else:
        print("❌ 部分性能优化测试失败！")
        exit(1)
