#!/usr/bin/env python3
"""
address-group详细分析工具
专门分析剩余3个不合规address-group的具体问题
"""

import xml.etree.ElementTree as ET
from collections import defaultdict

def analyze_non_compliant_address_groups(xml_file, label):
    """
    详细分析不合规的address-group对象
    
    Args:
        xml_file: XML文件路径
        label: 标签（原版/重构版）
    """
    print(f'=== {label} 不合规address-group详细分析 ===')
    
    try:
        tree = ET.parse(xml_file)
        root = tree.getroot()
        
        # 查找network-obj容器
        network_obj_containers = []
        for elem in root.iter():
            if elem.tag.endswith('network-obj') and 'network-obj' in elem.tag:
                network_obj_containers.append(elem)
        
        print(f'找到network-obj容器: {len(network_obj_containers)}个')
        
        non_compliant_groups = []
        
        for network_obj in network_obj_containers:
            # 分析address-group对象
            address_groups = []
            for child in network_obj:
                if child.tag.endswith('address-group') and 'address-group' in child.tag:
                    address_groups.append(child)
            
            print(f'  address-group对象总数: {len(address_groups)}个')
            
            # 详细分析每个address-group
            for i, addr_group in enumerate(address_groups):
                # 获取name
                name_elem = None
                for child in addr_group:
                    if child.tag.endswith('name'):
                        name_elem = child
                        break
                
                group_name = name_elem.text if name_elem is not None and name_elem.text else f"unnamed_{i}"
                
                # 检查address-set引用
                address_set_refs = []
                for child in addr_group:
                    if child.tag.endswith('address-set'):
                        address_set_refs.append(child)
                
                # 分析不合规的组
                if len(address_set_refs) == 0:
                    non_compliant_groups.append({
                        'name': group_name,
                        'index': i,
                        'children': [child.tag.split('}')[-1] if '}' in child.tag else child.tag for child in addr_group],
                        'children_with_text': [(child.tag.split('}')[-1] if '}' in child.tag else child.tag, child.text) for child in addr_group if child.text]
                    })
        
        print(f'\n不合规address-group详细信息:')
        print(f'  不合规数量: {len(non_compliant_groups)}个')
        
        for group in non_compliant_groups:
            print(f'\n  不合规组 #{group["index"]+1}: {group["name"]}')
            print(f'    子元素: {group["children"]}')
            print(f'    有文本的子元素: {group["children_with_text"]}')
            
            # 检查是否有可能的成员信息
            has_potential_members = False
            for tag, text in group["children_with_text"]:
                if tag in ['member', 'members', 'address-set-name'] and text:
                    has_potential_members = True
                    print(f'    ⚠️ 发现潜在成员信息: {tag} = {text}')
            
            if not has_potential_members:
                print(f'    ❌ 没有发现任何成员信息')
        
        return non_compliant_groups
        
    except Exception as e:
        print(f'分析失败: {e}')
        import traceback
        traceback.print_exc()
        return []

def compare_address_group_structures(orig_file, refact_file):
    """对比原版和重构版的address-group结构差异"""
    print(f'\n=== address-group结构对比分析 ===')
    
    print('分析原版结构:')
    orig_non_compliant = analyze_non_compliant_address_groups(orig_file, '原版')
    
    print('\n分析重构版结构:')
    refact_non_compliant = analyze_non_compliant_address_groups(refact_file, '重构版')
    
    print(f'\n=== 对比结果 ===')
    print(f'原版不合规数量: {len(orig_non_compliant)}个')
    print(f'重构版不合规数量: {len(refact_non_compliant)}个')
    
    if len(refact_non_compliant) > 0:
        print(f'\n重构版需要修复的address-group:')
        for group in refact_non_compliant:
            print(f'  - {group["name"]}: 缺少address-set引用')
    
    return orig_non_compliant, refact_non_compliant

def analyze_address_group_quantity_difference():
    """分析address-group数量差异的原因"""
    print(f'\n=== address-group数量差异分析 ===')
    
    # 分析原版
    orig_tree = ET.parse('output/fortigate-z3200s-R11.xml')
    orig_root = orig_tree.getroot()
    
    # 分析重构版
    refact_tree = ET.parse('data/output/test_address_group_xml_integration_fixed.xml')
    refact_root = refact_tree.getroot()
    
    # 提取所有address-group名称
    def extract_group_names(root):
        names = []
        for elem in root.iter():
            if elem.tag.endswith('address-group'):
                for child in elem:
                    if child.tag.endswith('name') and child.text:
                        names.append(child.text.strip())
                        break
        return names
    
    orig_names = extract_group_names(orig_root)
    refact_names = extract_group_names(refact_root)
    
    print(f'原版address-group名称 ({len(orig_names)}个):')
    for name in sorted(orig_names):
        print(f'  - {name}')
    
    print(f'\n重构版address-group名称 ({len(refact_names)}个):')
    for name in sorted(refact_names):
        print(f'  - {name}')
    
    # 分析差异
    orig_set = set(orig_names)
    refact_set = set(refact_names)
    
    only_in_orig = orig_set - refact_set
    only_in_refact = refact_set - orig_set
    common = orig_set & refact_set
    
    print(f'\n数量差异分析:')
    print(f'  共同对象: {len(common)}个')
    print(f'  仅在原版: {len(only_in_orig)}个')
    print(f'  仅在重构版: {len(only_in_refact)}个')
    
    if only_in_orig:
        print(f'\n仅在原版中的address-group:')
        for name in sorted(only_in_orig):
            print(f'    - {name}')
    
    if only_in_refact:
        print(f'\n仅在重构版中的address-group:')
        for name in sorted(only_in_refact):
            print(f'    - {name}')
    
    # 检查重复
    from collections import Counter
    orig_counter = Counter(orig_names)
    refact_counter = Counter(refact_names)
    
    orig_duplicates = {name: count for name, count in orig_counter.items() if count > 1}
    refact_duplicates = {name: count for name, count in refact_counter.items() if count > 1}
    
    if orig_duplicates:
        print(f'\n原版重复的address-group:')
        for name, count in orig_duplicates.items():
            print(f'    - {name}: {count}次')
    
    if refact_duplicates:
        print(f'\n重构版重复的address-group:')
        for name, count in refact_duplicates.items():
            print(f'    - {name}: {count}次')
    
    return {
        'orig_names': orig_names,
        'refact_names': refact_names,
        'only_in_orig': only_in_orig,
        'only_in_refact': only_in_refact,
        'orig_duplicates': orig_duplicates,
        'refact_duplicates': refact_duplicates
    }

def main():
    print('=== address-group详细问题分析 ===\n')
    
    # 分析不合规对象
    orig_non_compliant, refact_non_compliant = compare_address_group_structures(
        'output/fortigate-z3200s-R11.xml',
        'data/output/test_address_group_xml_integration_fixed.xml'
    )
    
    # 分析数量差异
    quantity_analysis = analyze_address_group_quantity_difference()
    
    print(f'\n=== 修复建议 ===')
    if len(refact_non_compliant) > 0:
        print(f'1. 需要修复{len(refact_non_compliant)}个不合规address-group的引用生成逻辑')
        print(f'2. 重点检查这些对象的成员数据是否正确传递到XML集成层')
        print(f'3. 确保_set_address_group_members方法正确处理所有成员类型')
    
    if quantity_analysis['refact_duplicates']:
        print(f'4. 需要解决重构版中的重复address-group问题')
    
    if len(quantity_analysis['only_in_refact']) > len(quantity_analysis['only_in_orig']):
        print(f'5. 需要分析为什么重构版生成了额外的address-group对象')

if __name__ == '__main__':
    main()
