#!/usr/bin/env python3
"""
地址对象差异分析工具
分析原版和重构版生成的地址对象的具体差异
"""

import xml.etree.ElementTree as ET
import sys
from collections import defaultdict

def analyze_address_objects(xml_file, label):
    """分析XML文件中的地址对象"""
    print(f'=== {label}地址对象分析 ===')

    try:
        tree = ET.parse(xml_file)
        root = tree.getroot()

        # 查找所有address-set元素
        address_sets = []
        for elem in root.iter():
            if elem.tag.endswith('address-set') or 'address-set' in elem.tag:
                address_sets.append(elem)
        print(f'总数量: {len(address_sets)}')

        # 统计类型分布和名称
        types = defaultdict(int)
        names = []
        name_issues = 0

        for i, addr in enumerate(address_sets):
            # 尝试多种方式查找name元素
            name_elem = addr.find('name')
            if name_elem is None:
                # 尝试使用不同的查找方式
                for child in addr:
                    if child.tag.endswith('name') or 'name' in child.tag:
                        name_elem = child
                        break

            if name_elem is not None and name_elem.text:
                name = name_elem.text
                names.append(name)

                # 分析地址类型
                ip_sets = []
                for child in addr.iter():
                    if child.tag.endswith('ip-set') or 'ip-set' in child.tag:
                        ip_sets.append(child)

                if ip_sets:
                    for ip_set in ip_sets:
                        # 查找ip-address元素
                        ip_elem = None
                        for child in ip_set.iter():
                            if child.tag.endswith('ip-address') or 'ip-address' in child.tag:
                                ip_elem = child
                                break

                        if ip_elem is not None and ip_elem.text:
                            ip_value = ip_elem.text
                            if '/' in ip_value:
                                addr_type = 'subnet'
                            elif '-' in ip_value:
                                addr_type = 'range'
                            else:
                                addr_type = 'host'
                            types[addr_type] += 1
                        else:
                            types['no_ip'] += 1
                else:
                    types['no_ip_set'] += 1
            else:
                name_issues += 1
                if i < 3:  # 只显示前3个问题对象的详细信息
                    print(f'  地址对象 {i+1} 没有有效的name元素: {addr.tag}')

        print(f'有效名称数量: {len(names)}')
        print(f'名称问题数量: {name_issues}')
        print('类型分布:')
        for addr_type, count in sorted(types.items()):
            print(f'  {addr_type}: {count}')

        return {
            'total': len(address_sets),
            'types': dict(types),
            'names': set(names),
            'name_issues': name_issues
        }

    except Exception as e:
        print(f'分析失败: {e}')
        import traceback
        traceback.print_exc()
        return None

def compare_address_objects(orig_data, refact_data):
    """对比两个版本的地址对象差异"""
    print('\n=== 差异分析 ===')
    
    # 数量差异
    print(f'总数量差异: {refact_data["total"] - orig_data["total"]}')
    
    # 类型差异
    all_types = set(orig_data['types'].keys()) | set(refact_data['types'].keys())
    print('\n类型差异:')
    for addr_type in sorted(all_types):
        orig_count = orig_data['types'].get(addr_type, 0)
        refact_count = refact_data['types'].get(addr_type, 0)
        diff = refact_count - orig_count
        print(f'  {addr_type}: 原版={orig_count}, 重构版={refact_count}, 差异={diff}')
    
    # 名称差异
    orig_names = orig_data['names']
    refact_names = refact_data['names']
    
    only_in_orig = orig_names - refact_names
    only_in_refact = refact_names - orig_names
    common = orig_names & refact_names
    
    print(f'\n名称差异:')
    print(f'  共同地址对象: {len(common)}')
    print(f'  仅在原版中: {len(only_in_orig)}')
    print(f'  仅在重构版中: {len(only_in_refact)}')
    
    if only_in_orig:
        print(f'\n仅在原版中的地址对象 (前10个):')
        for name in sorted(list(only_in_orig))[:10]:
            print(f'    {name}')
    
    if only_in_refact:
        print(f'\n仅在重构版中的地址对象 (前10个):')
        for name in sorted(list(only_in_refact))[:10]:
            print(f'    {name}')

def analyze_service_objects(xml_file, label):
    """分析XML文件中的服务对象"""
    print(f'=== {label}服务对象分析 ===')

    try:
        tree = ET.parse(xml_file)
        root = tree.getroot()

        # 查找所有service-set元素
        service_sets = []
        for elem in root.iter():
            if elem.tag.endswith('service-set') or 'service-set' in elem.tag:
                service_sets.append(elem)

        print(f'总数量: {len(service_sets)}')

        # 统计名称（使用多种方法查找name元素）
        names = []
        for service in service_sets:
            name_elem = service.find('name')
            if name_elem is None:
                # 尝试使用不同的查找方式
                for child in service:
                    if child.tag.endswith('name') or 'name' in child.tag:
                        name_elem = child
                        break

            if name_elem is not None and name_elem.text:
                names.append(name_elem.text)

        print(f'有效名称数量: {len(names)}')

        return {
            'total': len(service_sets),
            'names': set(names)
        }

    except Exception as e:
        print(f'分析失败: {e}')
        return None

def main():
    orig_file = 'data/output/test_original_correct_files.xml'
    refact_file = 'data/output/test_refactored_dedup_fixed.xml'

    # 分析原版地址对象
    orig_data = analyze_address_objects(orig_file, '原版')
    if not orig_data:
        return

    print()

    # 分析重构版地址对象
    refact_data = analyze_address_objects(refact_file, '重构版')
    if not refact_data:
        return

    # 对比地址对象差异
    compare_address_objects(orig_data, refact_data)

    print('\n' + '='*50)

    # 分析服务对象
    orig_service_data = analyze_service_objects(orig_file, '原版')
    refact_service_data = analyze_service_objects(refact_file, '重构版')

    if orig_service_data and refact_service_data:
        print('\n=== 服务对象差异分析 ===')
        orig_names = orig_service_data['names']
        refact_names = refact_service_data['names']

        only_in_orig = orig_names - refact_names
        only_in_refact = refact_names - orig_names
        common = orig_names & refact_names

        print(f'共同服务对象: {len(common)}')
        print(f'仅在原版中: {len(only_in_orig)}')
        print(f'仅在重构版中: {len(only_in_refact)}')

        if only_in_orig:
            print('仅在原版中的服务对象:')
            for name in sorted(list(only_in_orig))[:5]:
                print(f'  {name}')

        if only_in_refact:
            print('仅在重构版中的服务对象:')
            for name in sorted(list(only_in_refact))[:5]:
                print(f'  {name}')

if __name__ == '__main__':
    main()
