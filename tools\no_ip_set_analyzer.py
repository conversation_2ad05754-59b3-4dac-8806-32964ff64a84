#!/usr/bin/env python3
"""
no_ip_set对象分析工具
深入分析原版生成的no_ip_set类型地址对象的转换正确性
"""

import xml.etree.ElementTree as ET
import sys
from collections import defaultdict

def analyze_no_ip_set_objects(xml_file):
    """分析XML文件中的no_ip_set类型地址对象"""
    print(f'=== 分析 {xml_file} 中的no_ip_set对象 ===')
    
    try:
        tree = ET.parse(xml_file)
        root = tree.getroot()
        
        # 查找所有address-set元素
        address_sets = []
        for elem in root.iter():
            if elem.tag.endswith('address-set') or 'address-set' in elem.tag:
                address_sets.append(elem)
        
        print(f'总地址对象数量: {len(address_sets)}')
        
        # 分析no_ip_set对象
        no_ip_set_objects = []
        
        for addr in address_sets:
            name_elem = addr.find('name')
            if name_elem is not None and name_elem.text:
                name = name_elem.text
                
                # 检查是否有ip-set元素
                ip_sets = []
                for child in addr.iter():
                    if child.tag.endswith('ip-set') or 'ip-set' in child.tag:
                        ip_sets.append(child)

                # 也检查是否有ip-address元素（更准确的判断）
                ip_addresses = []
                for child in addr.iter():
                    if child.tag.endswith('ip-address') or 'ip-address' in child.tag:
                        if child.text and child.text.strip():
                            ip_addresses.append(child)
                
                if not ip_sets or not ip_addresses:
                    # 这是一个no_ip_set对象（没有ip-set或没有有效的ip-address）
                    no_ip_set_objects.append({
                        'name': name,
                        'element': addr,
                        'children': [child.tag for child in addr],
                        'text_content': addr.text,
                        'xml_content': ET.tostring(addr, encoding='unicode')[:200] + '...'
                    })
        
        print(f'no_ip_set对象数量: {len(no_ip_set_objects)}')
        
        # 显示前10个no_ip_set对象的详细信息
        print('\n=== 前10个no_ip_set对象详细信息 ===')
        for i, obj in enumerate(no_ip_set_objects[:10]):
            print(f'\n对象 {i+1}: {obj["name"]}')
            print(f'  子元素: {obj["children"]}')
            print(f'  文本内容: {repr(obj["text_content"])}')
            print(f'  XML内容: {obj["xml_content"]}')
        
        # 分析这些对象的结构特征
        print('\n=== no_ip_set对象结构分析 ===')
        child_patterns = defaultdict(int)
        for obj in no_ip_set_objects:
            pattern = tuple(sorted(obj['children']))
            child_patterns[pattern] += 1
        
        print('子元素模式分布:')
        for pattern, count in sorted(child_patterns.items(), key=lambda x: x[1], reverse=True):
            print(f'  {pattern}: {count}个对象')
        
        return no_ip_set_objects
        
    except Exception as e:
        print(f'分析失败: {e}')
        import traceback
        traceback.print_exc()
        return None

def check_yang_compliance(no_ip_set_objects):
    """检查no_ip_set对象是否符合NTOS YANG模型规范"""
    print('\n=== NTOS YANG模型合规性检查 ===')
    
    print('NTOS YANG模型要求:')
    print('  - 每个address-set必须包含name元素')
    print('  - 每个address-set必须包含至少一个ip-set元素')
    print('  - 每个ip-set必须包含ip-address元素')
    
    compliant_count = 0
    non_compliant_count = 0
    
    for obj in no_ip_set_objects:
        has_name = 'name' in [child.split('}')[-1] if '}' in child else child for child in obj['children']]
        has_ip_set = any('ip-set' in child for child in obj['children'])
        
        if has_name and has_ip_set:
            compliant_count += 1
        else:
            non_compliant_count += 1
            if non_compliant_count <= 5:  # 只显示前5个不合规的对象
                print(f'  不合规对象: {obj["name"]} - 缺少ip-set元素')
    
    print(f'\n合规性统计:')
    print(f'  符合YANG规范: {compliant_count}个')
    print(f'  不符合YANG规范: {non_compliant_count}个')
    print(f'  合规率: {compliant_count/(compliant_count+non_compliant_count)*100:.1f}%')
    
    return non_compliant_count

def analyze_fortigate_source(no_ip_set_objects):
    """分析这些no_ip_set对象在FortiGate配置中的原始形式"""
    print('\n=== FortiGate源配置分析 ===')
    
    # 读取FortiGate配置文件
    try:
        with open('Pass-Mask-MTU-FW-1_7-2_1639_202506271000.conf', 'r', encoding='utf-8') as f:
            fortigate_config = f.read()
    except:
        print('无法读取FortiGate配置文件')
        return
    
    print('分析前10个no_ip_set对象在FortiGate中的原始定义:')
    
    for i, obj in enumerate(no_ip_set_objects[:10]):
        name = obj['name']
        print(f'\n对象 {i+1}: {name}')
        
        # 在FortiGate配置中查找这个对象
        lines = fortigate_config.split('\n')
        found = False
        for j, line in enumerate(lines):
            if f'edit "{name}"' in line:
                found = True
                print(f'  找到定义 (行 {j+1}):')
                # 显示接下来的几行
                for k in range(j, min(j+10, len(lines))):
                    if lines[k].strip().startswith('next') and k > j:
                        break
                    print(f'    {lines[k]}')
                break
        
        if not found:
            print(f'  未在FortiGate配置中找到定义')

def main():
    orig_file = 'data/output/test_original_correct_files.xml'
    
    # 分析原版的no_ip_set对象
    no_ip_set_objects = analyze_no_ip_set_objects(orig_file)
    if not no_ip_set_objects:
        return
    
    # 检查YANG模型合规性
    non_compliant_count = check_yang_compliance(no_ip_set_objects)
    
    # 分析FortiGate源配置
    analyze_fortigate_source(no_ip_set_objects)
    
    # 总结分析结果
    print('\n=== 转换正确性分析总结 ===')
    if non_compliant_count > 0:
        print(f'发现 {non_compliant_count} 个不符合NTOS YANG规范的地址对象')
        print('这表明原版存在地址对象转换bug或容错机制')
    else:
        print('所有no_ip_set对象都符合YANG规范（意外结果）')

if __name__ == '__main__':
    main()
