#!/usr/bin/env python3
"""
原版地址对象转换正确性深度分析工具
分析原版生成的623个地址对象中224个no_ip_set类型对象的转换正确性
"""

import xml.etree.ElementTree as ET
import sys
from collections import defaultdict

def analyze_yang_compliance(xml_file):
    """NTOS YANG模型合规性验证"""
    print('=== NTOS YANG模型合规性深度验证 ===')
    
    try:
        tree = ET.parse(xml_file)
        root = tree.getroot()
        
        # 查找所有address-set元素
        address_sets = []
        for elem in root.iter():
            if 'address-set' in elem.tag:
                address_sets.append(elem)
        
        print(f'总地址对象数量: {len(address_sets)}')
        
        # 详细分析每个address-set的结构
        yang_compliant = 0
        yang_violations = []
        structure_analysis = {}
        
        for i, addr_set in enumerate(address_sets):
            # 检查name元素
            name_elem = addr_set.find('name')
            has_name = name_elem is not None and name_elem.text
            
            # 检查ip-set元素
            ip_sets = []
            for child in addr_set.iter():
                if 'ip-set' in child.tag:
                    ip_sets.append(child)
            
            # 检查ip-address元素
            ip_addresses = []
            for child in addr_set.iter():
                if 'ip-address' in child.tag and child.text and child.text.strip():
                    ip_addresses.append(child.text.strip())
            
            # YANG模型合规性检查
            is_compliant = has_name and len(ip_sets) > 0 and len(ip_addresses) > 0
            
            if is_compliant:
                yang_compliant += 1
            else:
                violation_info = {
                    'index': i + 1,
                    'name': name_elem.text if name_elem is not None else '无名称',
                    'has_name': has_name,
                    'ip_sets_count': len(ip_sets),
                    'ip_addresses_count': len(ip_addresses),
                    'violation_type': []
                }
                
                if not has_name:
                    violation_info['violation_type'].append('缺少name元素')
                if len(ip_sets) == 0:
                    violation_info['violation_type'].append('缺少ip-set元素')
                if len(ip_addresses) == 0:
                    violation_info['violation_type'].append('缺少有效ip-address')
                
                yang_violations.append(violation_info)
            
            # 结构分析
            structure_key = f'name:{has_name}_ipsets:{len(ip_sets)}_ipaddrs:{len(ip_addresses)}'
            structure_analysis[structure_key] = structure_analysis.get(structure_key, 0) + 1
        
        print(f'\nYANG模型合规性统计:')
        print(f'  符合规范: {yang_compliant}个 ({yang_compliant/len(address_sets)*100:.1f}%)')
        print(f'  违反规范: {len(yang_violations)}个 ({len(yang_violations)/len(address_sets)*100:.1f}%)')
        
        print(f'\n结构分析:')
        for structure, count in sorted(structure_analysis.items(), key=lambda x: x[1], reverse=True):
            print(f'  {structure}: {count}个对象')
        
        print(f'\n前10个违规对象详情:')
        for violation in yang_violations[:10]:
            print(f'  对象{violation["index"]}: {violation["name"]}')
            print(f'    违规类型: {violation["violation_type"]}')
            print(f'    ip-set数量: {violation["ip_sets_count"]}, ip-address数量: {violation["ip_addresses_count"]}')
        
        return yang_violations
        
    except Exception as e:
        print(f'YANG合规性分析失败: {e}')
        import traceback
        traceback.print_exc()
        return None

def analyze_fortigate_source_mapping(yang_violations):
    """FortiGate源配置追溯分析"""
    print('\n=== FortiGate源配置追溯分析 ===')
    
    # 读取FortiGate配置文件
    try:
        with open('Pass-Mask-MTU-FW-1_7-2_1639_202506271000.conf', 'r', encoding='utf-8') as f:
            fortigate_config = f.read()
    except:
        print('无法读取FortiGate配置文件')
        return
    
    print(f'分析前20个违规对象在FortiGate中的原始定义:')
    
    source_analysis = {
        'found_in_source': 0,
        'not_found': 0,
        'source_types': defaultdict(int),
        'conversion_errors': []
    }
    
    for i, violation in enumerate(yang_violations[:20]):
        name = violation['name']
        print(f'\n违规对象 {i+1}: {name}')
        
        # 在FortiGate配置中查找这个对象
        lines = fortigate_config.split('\n')
        found = False
        source_type = 'unknown'
        
        for j, line in enumerate(lines):
            if f'edit "{name}"' in line:
                found = True
                source_analysis['found_in_source'] += 1
                print(f'  ✅ 找到FortiGate源定义 (行 {j+1}):')
                
                # 分析接下来的几行确定类型
                for k in range(j, min(j+15, len(lines))):
                    if lines[k].strip().startswith('next') and k > j:
                        break
                    print(f'    {lines[k]}')
                    
                    # 确定源类型
                    if 'set subnet' in lines[k]:
                        source_type = 'subnet'
                    elif 'set start-ip' in lines[k] or 'set end-ip' in lines[k]:
                        source_type = 'range'
                    elif 'set fqdn' in lines[k]:
                        source_type = 'fqdn'
                    elif 'set type' in lines[k]:
                        if 'mac' in lines[k]:
                            source_type = 'mac'
                
                source_analysis['source_types'][source_type] += 1
                
                # 记录转换错误
                if source_type in ['subnet', 'range']:
                    source_analysis['conversion_errors'].append({
                        'name': name,
                        'source_type': source_type,
                        'error': f'{source_type}类型对象被错误转换为空address-set'
                    })
                
                break
        
        if not found:
            source_analysis['not_found'] += 1
            print(f'  ❌ 未在FortiGate配置中找到定义')
    
    print(f'\n源配置追溯统计:')
    print(f'  在源配置中找到: {source_analysis["found_in_source"]}个')
    print(f'  未找到: {source_analysis["not_found"]}个')
    print(f'  源类型分布: {dict(source_analysis["source_types"])}')
    print(f'  转换错误数量: {len(source_analysis["conversion_errors"])}个')
    
    return source_analysis

def analyze_conversion_logic_errors(source_analysis):
    """转换逻辑错误定位"""
    print('\n=== 转换逻辑错误定位分析 ===')
    
    print('转换错误类型分析:')
    error_types = defaultdict(int)
    for error in source_analysis['conversion_errors']:
        error_types[error['source_type']] += 1
    
    for error_type, count in error_types.items():
        print(f'  {error_type}类型转换错误: {count}个')
    
    # 分析可能的错误原因
    print('\n可能的错误原因分析:')
    print('1. 地址处理阶段转换错误:')
    print('   - FortiGate subnet/range对象未被正确解析')
    print('   - IP地址提取逻辑存在bug')
    print('   - 子网掩码转换失败')
    
    print('2. XML集成阶段处理问题:')
    print('   - address-set创建后未添加ip-set子元素')
    print('   - XML片段生成不完整')
    print('   - 命名空间处理错误')
    
    print('3. 容错机制设计:')
    print('   - 原版可能故意保留不完整对象')
    print('   - 为维持向后兼容性而允许违规对象存在')
    
    # 评估影响
    total_violations = len(source_analysis['conversion_errors'])
    if total_violations > 0:
        print(f'\n影响评估:')
        print(f'  YANG规范违规率: {total_violations}/623 = {total_violations/623*100:.1f}%')
        print(f'  配置有效性影响: 中等 (违规对象可能导致配置加载失败)')
        print(f'  功能影响: 低 (主要影响地址对象引用)')

def generate_repair_strategy(yang_violations, source_analysis):
    """生成修复策略建议"""
    print('\n=== 修复策略决策分析 ===')
    
    conversion_errors = len(source_analysis['conversion_errors'])
    total_violations = len(yang_violations)
    
    print(f'决策依据:')
    print(f'  总违规对象: {total_violations}个')
    print(f'  确认的转换错误: {conversion_errors}个')
    print(f'  错误率: {conversion_errors/total_violations*100:.1f}%')
    
    if conversion_errors > total_violations * 0.8:  # 80%以上是转换错误
        print('\n🔧 建议修复策略: 修复原版转换逻辑')
        print('理由:')
        print('  - 大部分违规对象是由转换错误造成的')
        print('  - 原版存在明显的地址对象转换bug')
        print('  - 修复原版逻辑可以提高配置质量')
        
        print('\n具体修复建议:')
        print('  1. 修复地址处理阶段的IP地址提取逻辑')
        print('  2. 确保所有subnet/range对象都生成有效的ip-set')
        print('  3. 加强XML片段生成的完整性检查')
        print('  4. 让重构版采用修复后的正确转换逻辑')
        
    else:
        print('\n🔄 建议修复策略: 模拟原版容错机制')
        print('理由:')
        print('  - 部分违规可能是有意的容错设计')
        print('  - 原版可能有特殊的兼容性考虑')
        print('  - 为达到≥95%一致性，需要精确模拟原版行为')
        
        print('\n具体修复建议:')
        print('  1. 让重构版也生成相同的不完整address-set对象')
        print('  2. 实现与原版相同的容错机制')
        print('  3. 保持向后兼容性')
    
    print(f'\n实施路径:')
    print(f'  目标: 实现≥95%一致性得分')
    print(f'  当前差距: {224}个no_ip_set对象缺失')
    print(f'  预期改善: 修复后一致性得分可提升至90%+')

def verify_new_baseline(xml_file):
    """验证新生成的原版基准文件"""
    print(f'=== 验证新生成的原版基准文件: {xml_file} ===')

    try:
        tree = ET.parse(xml_file)
        root = tree.getroot()

        # 查找所有address-set元素
        address_sets = []
        for elem in root.iter():
            if 'address-set' in elem.tag:
                address_sets.append(elem)

        print(f'总地址对象数量: {len(address_sets)}')

        # 检查前10个地址对象的结构
        yang_compliant = 0
        name_missing = 0
        ip_missing = 0

        for i, addr_set in enumerate(address_sets[:10]):
            name_elem = addr_set.find('name')
            has_name = name_elem is not None and name_elem.text

            ip_sets = []
            for child in addr_set.iter():
                if 'ip-set' in child.tag:
                    ip_sets.append(child)

            ip_addresses = []
            for child in addr_set.iter():
                if 'ip-address' in child.tag and child.text and child.text.strip():
                    ip_addresses.append(child.text.strip())

            is_compliant = has_name and len(ip_sets) > 0 and len(ip_addresses) > 0

            if is_compliant:
                yang_compliant += 1
            if not has_name:
                name_missing += 1
            if len(ip_addresses) == 0:
                ip_missing += 1

            print(f'地址对象 {i+1}:')
            print(f'  Name: {name_elem.text if has_name else "缺失"}')
            print(f'  IP-sets: {len(ip_sets)}个')
            print(f'  IP-addresses: {len(ip_addresses)}个')
            print(f'  YANG合规: {"✅" if is_compliant else "❌"}')

        print(f'\n前10个对象统计:')
        print(f'  YANG合规: {yang_compliant}/10')
        print(f'  缺少name: {name_missing}/10')
        print(f'  缺少IP: {ip_missing}/10')

        # 检查整体结构
        total_compliant = 0
        total_name_missing = 0
        total_ip_missing = 0

        for addr_set in address_sets:
            name_elem = addr_set.find('name')
            has_name = name_elem is not None and name_elem.text

            ip_addresses = []
            for child in addr_set.iter():
                if 'ip-address' in child.tag and child.text and child.text.strip():
                    ip_addresses.append(child.text.strip())

            ip_sets = []
            for child in addr_set.iter():
                if 'ip-set' in child.tag:
                    ip_sets.append(child)

            is_compliant = has_name and len(ip_sets) > 0 and len(ip_addresses) > 0

            if is_compliant:
                total_compliant += 1
            if not has_name:
                total_name_missing += 1
            if len(ip_addresses) == 0:
                total_ip_missing += 1

        print(f'\n整体统计:')
        print(f'  总地址对象: {len(address_sets)}个')
        print(f'  YANG合规: {total_compliant}个 ({total_compliant/len(address_sets)*100:.1f}%)')
        print(f'  缺少name: {total_name_missing}个 ({total_name_missing/len(address_sets)*100:.1f}%)')
        print(f'  缺少IP: {total_ip_missing}个 ({total_ip_missing/len(address_sets)*100:.1f}%)')

        return {
            'total': len(address_sets),
            'compliant': total_compliant,
            'name_missing': total_name_missing,
            'ip_missing': total_ip_missing
        }

    except Exception as e:
        print(f'验证失败: {e}')
        return None

def main():
    # 验证新生成的原版基准文件
    new_baseline_stats = verify_new_baseline('data/output/test_original_verified.xml')

    if new_baseline_stats:
        print('\n=== 基准文件质量评估 ===')
        if new_baseline_stats['compliant'] > new_baseline_stats['total'] * 0.8:
            print('✅ 新基准文件质量良好，可以作为对比基准使用')
        else:
            print('⚠️ 新基准文件仍有质量问题，需要进一步分析')

    print('\n=== 验证完成 ===')

if __name__ == '__main__':
    main()
