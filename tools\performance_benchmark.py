#!/usr/bin/env python3
"""
FortiGate转换器性能基准测试工具
用于对比原版和重构版的性能指标
"""

import os
import sys
import time
import psutil
import json
import argparse
import subprocess
from typing import Dict, List, Any
import logging
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

class PerformanceBenchmark:
    """性能基准测试器"""
    
    def __init__(self):
        self.logger = self._setup_logger()
        self.results = []
        
    def _setup_logger(self):
        """设置日志记录器"""
        logger = logging.getLogger('PerformanceBenchmark')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger
    
    def measure_conversion_performance(self, config_file: str, mapping_file: str, 
                                     output_file: str, version: str = "original") -> Dict[str, Any]:
        """测量转换性能"""
        self.logger.info(f"开始测量 {version} 版本的转换性能")
        
        # 准备命令
        cmd = [
            "python", "engine/main.py",
            "--mode", "convert",
            "--vendor", "fortigate",
            "--cli", config_file,
            "--mapping", mapping_file,
            "--output", output_file,
            "--model", "z5100s",
            "--version", "R11"
        ]
        
        # 记录开始时间和系统状态
        start_time = time.time()
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 监控系统资源
        max_memory = initial_memory
        cpu_samples = []
        
        try:
            # 启动转换进程
            proc = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=os.getcwd()
            )
            
            # 监控资源使用
            while proc.poll() is None:
                try:
                    current_memory = process.memory_info().rss / 1024 / 1024
                    max_memory = max(max_memory, current_memory)
                    
                    cpu_percent = process.cpu_percent()
                    cpu_samples.append(cpu_percent)
                    
                    time.sleep(0.1)  # 100ms采样间隔
                except psutil.NoSuchProcess:
                    break
            
            # 等待进程完成
            stdout, stderr = proc.communicate()
            end_time = time.time()
            
            # 计算性能指标
            duration = end_time - start_time
            avg_cpu = sum(cpu_samples) / len(cpu_samples) if cpu_samples else 0
            memory_usage = max_memory - initial_memory
            
            # 检查转换是否成功
            success = proc.returncode == 0 and os.path.exists(output_file)
            
            # 获取输出文件大小
            output_size = 0
            if success and os.path.exists(output_file):
                output_size = os.path.getsize(output_file)
            
            result = {
                'version': version,
                'config_file': config_file,
                'success': success,
                'duration': duration,
                'memory_usage_mb': memory_usage,
                'max_memory_mb': max_memory,
                'avg_cpu_percent': avg_cpu,
                'output_file_size': output_size,
                'return_code': proc.returncode,
                'stdout': stdout,
                'stderr': stderr,
                'timestamp': datetime.now().isoformat()
            }
            
            self.logger.info(f"{version} 版本转换完成:")
            self.logger.info(f"  耗时: {duration:.2f}秒")
            self.logger.info(f"  内存使用: {memory_usage:.2f}MB")
            self.logger.info(f"  平均CPU: {avg_cpu:.2f}%")
            self.logger.info(f"  转换结果: {'成功' if success else '失败'}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"性能测试异常: {e}")
            return {
                'version': version,
                'config_file': config_file,
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def compare_versions(self, config_file: str, mapping_file: str, 
                        original_output: str, refactored_output: str) -> Dict[str, Any]:
        """对比两个版本的性能"""
        self.logger.info("开始对比原版和重构版性能")
        
        # 测试原版性能
        original_result = self.measure_conversion_performance(
            config_file, mapping_file, original_output, "original"
        )
        
        # 清理内存
        time.sleep(2)
        
        # 测试重构版性能
        refactored_result = self.measure_conversion_performance(
            config_file, mapping_file, refactored_output, "refactored"
        )
        
        # 计算性能对比
        comparison = self._calculate_performance_comparison(original_result, refactored_result)
        
        return {
            'original': original_result,
            'refactored': refactored_result,
            'comparison': comparison,
            'test_config': {
                'config_file': config_file,
                'mapping_file': mapping_file,
                'test_time': datetime.now().isoformat()
            }
        }
    
    def _calculate_performance_comparison(self, original: Dict, refactored: Dict) -> Dict[str, Any]:
        """计算性能对比指标"""
        comparison = {}
        
        if original.get('success') and refactored.get('success'):
            # 时间对比
            orig_duration = original.get('duration', 0)
            refact_duration = refactored.get('duration', 0)
            
            if orig_duration > 0:
                time_ratio = (refact_duration / orig_duration) * 100
                comparison['time_ratio_percent'] = time_ratio
                comparison['time_performance'] = 'better' if time_ratio < 100 else 'worse' if time_ratio > 120 else 'acceptable'
            
            # 内存对比
            orig_memory = original.get('memory_usage_mb', 0)
            refact_memory = refactored.get('memory_usage_mb', 0)
            
            if orig_memory > 0:
                memory_ratio = (refact_memory / orig_memory) * 100
                comparison['memory_ratio_percent'] = memory_ratio
                comparison['memory_performance'] = 'better' if memory_ratio < 100 else 'worse' if memory_ratio > 150 else 'acceptable'
            
            # CPU对比
            orig_cpu = original.get('avg_cpu_percent', 0)
            refact_cpu = refactored.get('avg_cpu_percent', 0)
            
            if orig_cpu > 0:
                cpu_ratio = (refact_cpu / orig_cpu) * 100
                comparison['cpu_ratio_percent'] = cpu_ratio
                comparison['cpu_performance'] = 'better' if cpu_ratio < 100 else 'worse' if cpu_ratio > 150 else 'acceptable'
            
            # 文件大小对比
            orig_size = original.get('output_file_size', 0)
            refact_size = refactored.get('output_file_size', 0)
            
            if orig_size > 0:
                size_ratio = (refact_size / orig_size) * 100
                comparison['file_size_ratio_percent'] = size_ratio
            
            # 总体性能评估
            performance_issues = []
            if comparison.get('time_performance') == 'worse':
                performance_issues.append('转换时间超过120%')
            if comparison.get('memory_performance') == 'worse':
                performance_issues.append('内存使用超过150%')
            if comparison.get('cpu_performance') == 'worse':
                performance_issues.append('CPU使用超过150%')
            
            comparison['overall_assessment'] = 'passed' if not performance_issues else 'failed'
            comparison['performance_issues'] = performance_issues
            
        else:
            comparison['overall_assessment'] = 'failed'
            comparison['performance_issues'] = ['转换失败']
        
        return comparison
    
    def batch_performance_test(self, test_cases: List[Dict[str, str]]) -> Dict[str, Any]:
        """批量性能测试"""
        self.logger.info(f"开始批量性能测试，共 {len(test_cases)} 个测试用例")
        
        results = []
        passed_count = 0
        total_time_ratio = 0
        total_memory_ratio = 0
        
        for i, test_case in enumerate(test_cases, 1):
            self.logger.info(f"执行测试用例 {i}/{len(test_cases)}: {test_case['name']}")
            
            result = self.compare_versions(
                test_case['config_file'],
                test_case['mapping_file'],
                test_case['original_output'],
                test_case['refactored_output']
            )
            
            result['test_case_name'] = test_case['name']
            results.append(result)
            
            # 统计结果
            comparison = result.get('comparison', {})
            if comparison.get('overall_assessment') == 'passed':
                passed_count += 1
            
            time_ratio = comparison.get('time_ratio_percent', 100)
            memory_ratio = comparison.get('memory_ratio_percent', 100)
            total_time_ratio += time_ratio
            total_memory_ratio += memory_ratio
            
            self.logger.info(f"测试用例 {i} 结果: {comparison.get('overall_assessment', 'unknown')}")
        
        # 计算总体统计
        avg_time_ratio = total_time_ratio / len(test_cases) if test_cases else 100
        avg_memory_ratio = total_memory_ratio / len(test_cases) if test_cases else 100
        pass_rate = (passed_count / len(test_cases)) * 100 if test_cases else 0
        
        summary = {
            'total_test_cases': len(test_cases),
            'passed_cases': passed_count,
            'failed_cases': len(test_cases) - passed_count,
            'pass_rate': pass_rate,
            'average_time_ratio': avg_time_ratio,
            'average_memory_ratio': avg_memory_ratio,
            'performance_acceptable': avg_time_ratio <= 120 and avg_memory_ratio <= 150 and pass_rate >= 95,
            'results': results
        }
        
        self.logger.info("=== 批量性能测试总结 ===")
        self.logger.info(f"总测试用例: {summary['total_test_cases']}")
        self.logger.info(f"通过用例: {summary['passed_cases']}")
        self.logger.info(f"失败用例: {summary['failed_cases']}")
        self.logger.info(f"通过率: {summary['pass_rate']:.2f}%")
        self.logger.info(f"平均时间比率: {summary['average_time_ratio']:.2f}%")
        self.logger.info(f"平均内存比率: {summary['average_memory_ratio']:.2f}%")
        self.logger.info(f"性能评估: {'✅ 可接受' if summary['performance_acceptable'] else '❌ 不可接受'}")
        
        return summary

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='FortiGate转换器性能基准测试工具')
    parser.add_argument('--config', required=True, help='配置文件路径')
    parser.add_argument('--mapping', required=True, help='映射文件路径')
    parser.add_argument('--original-output', required=True, help='原版输出文件路径')
    parser.add_argument('--refactored-output', required=True, help='重构版输出文件路径')
    parser.add_argument('--output', help='结果输出文件路径')
    
    args = parser.parse_args()
    
    benchmark = PerformanceBenchmark()
    result = benchmark.compare_versions(
        args.config,
        args.mapping,
        args.original_output,
        args.refactored_output
    )
    
    # 输出结果
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        print(f"性能测试结果已保存到: {args.output}")
    
    # 打印总结
    comparison = result.get('comparison', {})
    print(f"\n=== 性能测试结果总结 ===")
    print(f"时间比率: {comparison.get('time_ratio_percent', 0):.2f}%")
    print(f"内存比率: {comparison.get('memory_ratio_percent', 0):.2f}%")
    print(f"总体评估: {'✅ 通过' if comparison.get('overall_assessment') == 'passed' else '❌ 失败'}")
    
    if comparison.get('performance_issues'):
        print("性能问题:")
        for issue in comparison['performance_issues']:
            print(f"  - {issue}")
    
    return 0 if comparison.get('overall_assessment') == 'passed' else 1

if __name__ == '__main__':
    sys.exit(main())
