#!/usr/bin/env python3
"""
重构版缺陷分析工具
基于核心修复原则深度分析重构版的缺陷
"""

import xml.etree.ElementTree as ET
from collections import defaultdict, Counter

def analyze_address_object_duplication():
    """分析地址对象重复问题"""
    print('=== 地址对象重复问题深度分析 ===')
    
    # 分析原版
    orig_tree = ET.parse('output/fortigate-z3200s-R11.xml')
    orig_root = orig_tree.getroot()
    
    # 分析重构版（使用接口配置修复后的文件）
    refact_tree = ET.parse('data/output/test_refactored_interface_fixed.xml')
    refact_root = refact_tree.getroot()
    
    # 提取原版地址对象名称
    orig_names = []
    for elem in orig_root.iter():
        if elem.tag.endswith('address-set'):
            for child in elem:
                if child.tag.endswith('name') and child.text:
                    orig_names.append(child.text.strip())
                    break
    
    # 提取重构版地址对象名称
    refact_names = []
    for elem in refact_root.iter():
        if elem.tag.endswith('address-set'):
            for child in elem:
                if child.tag.endswith('name') and child.text:
                    refact_names.append(child.text.strip())
                    break
    
    print(f'原版地址对象数量: {len(orig_names)}')
    print(f'重构版地址对象数量: {len(refact_names)}')
    print(f'数量差异: +{len(refact_names) - len(orig_names)}')
    
    # 分析重复情况
    orig_counter = Counter(orig_names)
    refact_counter = Counter(refact_names)
    
    print(f'\n原版重复分析:')
    orig_duplicates = {name: count for name, count in orig_counter.items() if count > 1}
    print(f'  重复对象数量: {len(orig_duplicates)}')
    print(f'  重复实例总数: {sum(orig_duplicates.values())}')
    
    print(f'\n重构版重复分析:')
    refact_duplicates = {name: count for name, count in refact_counter.items() if count > 1}
    print(f'  重复对象数量: {len(refact_duplicates)}')
    print(f'  重复实例总数: {sum(refact_duplicates.values())}')
    
    # 分析重复模式
    if refact_duplicates:
        print(f'\n重构版重复对象详情（前10个）:')
        for i, (name, count) in enumerate(list(refact_duplicates.items())[:10]):
            orig_count = orig_counter.get(name, 0)
            print(f'  {i+1}. {name}: 重构版{count}个 vs 原版{orig_count}个')
    
    # 分析唯一对象
    orig_unique = set(orig_names)
    refact_unique = set(refact_names)
    
    only_in_orig = orig_unique - refact_unique
    only_in_refact = refact_unique - orig_unique
    common = orig_unique & refact_unique
    
    print(f'\n对象集合分析:')
    print(f'  共同对象: {len(common)}个')
    print(f'  仅在原版: {len(only_in_orig)}个')
    print(f'  仅在重构版: {len(only_in_refact)}个')
    
    if only_in_refact:
        print(f'\n仅在重构版中的对象（前5个）:')
        for name in list(only_in_refact)[:5]:
            print(f'    {name}')
    
    return {
        'orig_total': len(orig_names),
        'refact_total': len(refact_names),
        'orig_duplicates': orig_duplicates,
        'refact_duplicates': refact_duplicates,
        'common': len(common),
        'only_in_orig': len(only_in_orig),
        'only_in_refact': len(only_in_refact)
    }

def analyze_interface_configuration_completeness():
    """分析接口配置完整性"""
    print('\n=== 接口配置信息缺失分析 ===')
    
    # 分析原版接口配置
    orig_tree = ET.parse('output/fortigate-z3200s-R11.xml')
    orig_root = orig_tree.getroot()
    
    # 分析重构版接口配置（使用接口配置修复后的文件）
    refact_tree = ET.parse('data/output/test_refactored_interface_fixed.xml')
    refact_root = refact_tree.getroot()
    
    # 查找接口相关元素
    orig_interfaces = []
    for elem in orig_root.iter():
        if 'interface' in elem.tag.lower():
            orig_interfaces.append(elem)
    
    refact_interfaces = []
    for elem in refact_root.iter():
        if 'interface' in elem.tag.lower():
            refact_interfaces.append(elem)
    
    print(f'原版接口相关元素: {len(orig_interfaces)}个')
    print(f'重构版接口相关元素: {len(refact_interfaces)}个')
    print(f'差异: {len(refact_interfaces) - len(orig_interfaces):+}个')
    
    # 分析接口配置内容
    def analyze_interface_content(interfaces, label):
        print(f'\n{label}接口配置内容分析:')
        
        config_types = defaultdict(int)
        for interface in interfaces[:10]:  # 只分析前10个
            for child in interface.iter():
                tag_name = child.tag.split('}')[-1] if '}' in child.tag else child.tag
                if child.text and child.text.strip():
                    config_types[tag_name] += 1
        
        print(f'  配置类型分布（前10个接口）:')
        for config_type, count in sorted(config_types.items(), key=lambda x: x[1], reverse=True)[:10]:
            print(f'    {config_type}: {count}次')
        
        return config_types
    
    orig_config_types = analyze_interface_content(orig_interfaces, '原版')
    refact_config_types = analyze_interface_content(refact_interfaces, '重构版')
    
    # 对比配置完整性
    print(f'\n接口配置完整性对比:')
    all_config_types = set(orig_config_types.keys()) | set(refact_config_types.keys())
    
    missing_in_refact = []
    for config_type in all_config_types:
        orig_count = orig_config_types.get(config_type, 0)
        refact_count = refact_config_types.get(config_type, 0)
        diff = refact_count - orig_count
        
        if diff < 0:
            missing_in_refact.append((config_type, abs(diff)))
        
        if abs(diff) > 0:
            print(f'  {config_type}: 原版{orig_count} vs 重构版{refact_count} (差异: {diff:+})')
    
    if missing_in_refact:
        print(f'\n重构版中缺失的配置类型:')
        for config_type, missing_count in sorted(missing_in_refact, key=lambda x: x[1], reverse=True):
            print(f'  {config_type}: 缺失{missing_count}个配置')
    
    return {
        'orig_interfaces': len(orig_interfaces),
        'refact_interfaces': len(refact_interfaces),
        'missing_in_refact': missing_in_refact
    }

def analyze_template_preservation_compliance():
    """分析模板保留合规性"""
    print('\n=== 模板优先原则合规性分析 ===')
    
    # 检查模板文件
    try:
        template_tree = ET.parse('engine/data/input/configData/z5100s/R11/Config/running.xml')
        template_root = template_tree.getroot()
        
        # 统计模板中的现有配置
        template_stats = defaultdict(int)
        for elem in template_root.iter():
            tag_name = elem.tag.split('}')[-1] if '}' in elem.tag else elem.tag
            template_stats[tag_name] += 1
        
        print(f'模板文件中的配置元素统计（前10个）:')
        for tag_name, count in sorted(template_stats.items(), key=lambda x: x[1], reverse=True)[:10]:
            print(f'  {tag_name}: {count}个')
        
        # 检查重构版是否保留了模板配置
        refact_tree = ET.parse('data/output/test_refactored_final.xml')
        refact_root = refact_tree.getroot()
        
        refact_stats = defaultdict(int)
        for elem in refact_root.iter():
            tag_name = elem.tag.split('}')[-1] if '}' in elem.tag else elem.tag
            refact_stats[tag_name] += 1
        
        print(f'\n模板保留情况分析:')
        template_loss = []
        for tag_name, template_count in template_stats.items():
            refact_count = refact_stats.get(tag_name, 0)
            if refact_count < template_count:
                loss = template_count - refact_count
                template_loss.append((tag_name, loss, template_count, refact_count))
        
        if template_loss:
            print(f'模板配置丢失情况（前10个）:')
            for tag_name, loss, template_count, refact_count in sorted(template_loss, key=lambda x: x[1], reverse=True)[:10]:
                print(f'  {tag_name}: 模板{template_count}个 → 重构版{refact_count}个 (丢失{loss}个)')
        else:
            print('✅ 重构版完全保留了模板配置')
        
        return template_loss
        
    except Exception as e:
        print(f'模板分析失败: {e}')
        return None

def generate_defect_analysis_report(addr_analysis, interface_analysis, template_analysis):
    """生成缺陷分析报告"""
    print('\n=== 重构版缺陷分析报告 ===')
    
    print(f'1. 地址对象重复问题:')
    if addr_analysis['refact_duplicates']:
        print(f'   ❌ 存在{len(addr_analysis["refact_duplicates"])}个重复对象')
        print(f'   ❌ 总重复实例: {sum(addr_analysis["refact_duplicates"].values())}个')
        print(f'   ❌ 违反"避免重复创建"原则')
    else:
        print(f'   ✅ 无重复对象')
    
    print(f'\n2. 接口配置完整性:')
    if interface_analysis['missing_in_refact']:
        print(f'   ❌ 存在{len(interface_analysis["missing_in_refact"])}种配置类型缺失')
        print(f'   ❌ 违反"模板优先原则"')
    else:
        print(f'   ✅ 接口配置完整')
    
    print(f'\n3. 模板保留合规性:')
    if template_analysis:
        print(f'   ❌ 存在{len(template_analysis)}种模板配置丢失')
        print(f'   ❌ 违反"模板优先原则"')
    else:
        print(f'   ✅ 完全保留模板配置')
    
    print(f'\n4. 核心修复原则合规性评估:')
    violations = 0
    if addr_analysis['refact_duplicates']:
        violations += 1
        print(f'   ❌ 违反"避免重复创建"原则')
    if interface_analysis['missing_in_refact']:
        violations += 1
        print(f'   ❌ 违反"模板优先原则"（接口配置）')
    if template_analysis:
        violations += 1
        print(f'   ❌ 违反"模板优先原则"（模板保留）')
    
    if violations == 0:
        print(f'   ✅ 完全符合核心修复原则')
    else:
        print(f'   ❌ 违反{violations}项核心修复原则')

def main():
    print('=== 基于核心修复原则的重构版缺陷深度分析 ===\n')
    
    print('核心修复原则:')
    print('1. 模板优先原则：尽可能使用XML模板中的现有信息和结构')
    print('2. 节点存在策略：如果目标节点已存在 → 修改现有字段内容')
    print('3. 节点缺失策略：如果目标节点不存在 → 插入新的完整XML块')
    print('4. 避免重复创建：不应该重复创建已存在的配置节点\n')
    
    # 执行各项分析
    addr_analysis = analyze_address_object_duplication()
    interface_analysis = analyze_interface_configuration_completeness()
    template_analysis = analyze_template_preservation_compliance()
    
    # 生成综合报告
    generate_defect_analysis_report(addr_analysis, interface_analysis, template_analysis)

if __name__ == '__main__':
    main()
