#!/usr/bin/env python3
"""
服务对象详细分析工具
分析重构版多生成3个服务对象的具体原因
"""

import xml.etree.ElementTree as ET
from collections import defaultdict, Counter

def analyze_service_objects_difference(orig_file, refact_file):
    """分析服务对象差异的具体原因"""
    print('=== 服务对象差异详细分析 ===')
    
    def extract_service_objects(file_path, label):
        tree = ET.parse(file_path)
        root = tree.getroot()
        services = []
        
        for elem in root.iter():
            if elem.tag.endswith('service-set') and 'service-set' in elem.tag:
                name_elem = None
                config_details = {}
                
                for child in elem:
                    child_tag = child.tag.split('}')[-1] if '}' in child.tag else child.tag
                    if child_tag == 'name':
                        name_elem = child
                    elif child.text and child.text.strip():
                        config_details[child_tag] = child.text.strip()
                
                if name_elem is not None and name_elem.text:
                    services.append({
                        'name': name_elem.text.strip(),
                        'config_count': len(config_details),
                        'configs': config_details
                    })
        
        print(f'{label}服务对象: {len(services)}个')
        return services
    
    orig_services = extract_service_objects(orig_file, '原版')
    refact_services = extract_service_objects(refact_file, '重构版')
    
    # 提取服务名称
    orig_names = {service['name'] for service in orig_services}
    refact_names = {service['name'] for service in refact_services}
    
    # 分析差异
    only_in_orig = orig_names - refact_names
    only_in_refact = refact_names - orig_names
    common = orig_names & refact_names
    
    print(f'\n差异分析:')
    print(f'  共同服务对象: {len(common)}个')
    print(f'  仅在原版: {len(only_in_orig)}个')
    print(f'  仅在重构版: {len(only_in_refact)}个')
    
    if only_in_orig:
        print(f'\n仅在原版中的服务对象:')
        for name in sorted(only_in_orig):
            print(f'    - {name}')
    
    if only_in_refact:
        print(f'\n仅在重构版中的服务对象:')
        for name in sorted(only_in_refact):
            print(f'    - {name}')
    
    # 检查重复
    orig_counter = Counter([service['name'] for service in orig_services])
    refact_counter = Counter([service['name'] for service in refact_services])
    
    orig_duplicates = {name: count for name, count in orig_counter.items() if count > 1}
    refact_duplicates = {name: count for name, count in refact_counter.items() if count > 1}
    
    if orig_duplicates:
        print(f'\n原版重复的服务对象:')
        for name, count in orig_duplicates.items():
            print(f'    - {name}: {count}次')
    
    if refact_duplicates:
        print(f'\n重构版重复的服务对象:')
        for name, count in refact_duplicates.items():
            print(f'    - {name}: {count}次')
    
    # 分析可能的错误生成对象
    suspicious_names = []
    for name in only_in_refact:
        # 检查是否是处理状态名称
        if name.lower() in ['converted', 'skipped', 'failed', 'details', 'success', 'error']:
            suspicious_names.append(name)
        # 检查是否是数字或统计信息
        elif name.isdigit() or 'count' in name.lower():
            suspicious_names.append(name)
    
    if suspicious_names:
        print(f'\n可疑的错误生成服务对象:')
        for name in suspicious_names:
            print(f'    - {name} (可能是处理状态或统计信息)')
    
    return {
        'orig_services': orig_services,
        'refact_services': refact_services,
        'only_in_orig': only_in_orig,
        'only_in_refact': only_in_refact,
        'orig_duplicates': orig_duplicates,
        'refact_duplicates': refact_duplicates,
        'suspicious_names': suspicious_names
    }

def analyze_service_object_configs(orig_file, refact_file):
    """分析服务对象的配置内容差异"""
    print('\n=== 服务对象配置内容分析 ===')
    
    def extract_service_configs(file_path, label):
        tree = ET.parse(file_path)
        root = tree.getroot()
        service_configs = {}
        
        for elem in root.iter():
            if elem.tag.endswith('service-set') and 'service-set' in elem.tag:
                name_elem = None
                config_details = {}
                
                for child in elem:
                    child_tag = child.tag.split('}')[-1] if '}' in child.tag else child.tag
                    if child_tag == 'name':
                        name_elem = child
                    else:
                        # 收集所有子元素信息
                        if child.text and child.text.strip():
                            config_details[child_tag] = child.text.strip()
                        else:
                            # 收集子元素的子元素
                            sub_configs = {}
                            for sub_child in child:
                                sub_tag = sub_child.tag.split('}')[-1] if '}' in sub_child.tag else sub_child.tag
                                if sub_child.text and sub_child.text.strip():
                                    sub_configs[sub_tag] = sub_child.text.strip()
                            if sub_configs:
                                config_details[child_tag] = sub_configs
                
                if name_elem is not None and name_elem.text:
                    service_configs[name_elem.text.strip()] = config_details
        
        print(f'{label}服务配置: {len(service_configs)}个')
        return service_configs
    
    orig_configs = extract_service_configs(orig_file, '原版')
    refact_configs = extract_service_configs(refact_file, '重构版')
    
    # 分析配置类型分布
    def analyze_config_types(configs, label):
        config_types = defaultdict(int)
        for service_name, config in configs.items():
            for config_type in config.keys():
                config_types[config_type] += 1
        
        print(f'\n{label}配置类型分布（前10个）:')
        for config_type, count in sorted(config_types.items(), key=lambda x: x[1], reverse=True)[:10]:
            print(f'    {config_type}: {count}个服务')
        
        return config_types
    
    orig_config_types = analyze_config_types(orig_configs, '原版')
    refact_config_types = analyze_config_types(refact_configs, '重构版')
    
    # 对比配置类型差异
    orig_types = set(orig_config_types.keys())
    refact_types = set(refact_config_types.keys())
    
    missing_types = orig_types - refact_types
    extra_types = refact_types - orig_types
    
    if missing_types:
        print(f'\n重构版缺失的配置类型:')
        for config_type in sorted(missing_types):
            print(f'    - {config_type}')
    
    if extra_types:
        print(f'\n重构版额外的配置类型:')
        for config_type in sorted(extra_types):
            print(f'    - {config_type}')
    
    return {
        'orig_configs': orig_configs,
        'refact_configs': refact_configs,
        'orig_config_types': orig_config_types,
        'refact_config_types': refact_config_types,
        'missing_types': missing_types,
        'extra_types': extra_types
    }

def main():
    print('=== 服务对象数量差异分析 ===\n')
    
    # 分析服务对象差异（使用修复后的文件）
    diff_analysis = analyze_service_objects_difference(
        'output/fortigate-z3200s-R11.xml',
        'data/output/test_service_object_fixed.xml'
    )
    
    # 分析服务对象配置（使用修复后的文件）
    config_analysis = analyze_service_object_configs(
        'output/fortigate-z3200s-R11.xml',
        'data/output/test_service_object_fixed.xml'
    )
    
    print(f'\n=== 修复建议 ===')
    if diff_analysis['suspicious_names']:
        print(f'1. 需要过滤掉可疑的错误生成服务对象: {diff_analysis["suspicious_names"]}')
    
    if diff_analysis['refact_duplicates']:
        print(f'2. 需要解决重构版中的重复服务对象问题')
    
    if len(diff_analysis['only_in_refact']) > len(diff_analysis['only_in_orig']):
        print(f'3. 需要分析为什么重构版生成了额外的服务对象')
    
    print(f'4. 目标：将重构版服务对象数量从{len(diff_analysis["refact_services"])}个调整到{len(diff_analysis["orig_services"])}个')

if __name__ == '__main__':
    main()
