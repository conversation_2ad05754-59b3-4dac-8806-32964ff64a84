#!/usr/bin/env python3
"""
系统性模块化对比分析工具
按照11个模块顺序对比原版和重构版的功能等价性
"""

import xml.etree.ElementTree as ET
from collections import defaultdict, Counter
import json

class SystematicModuleAnalyzer:
    """系统性模块分析器"""
    
    def __init__(self, orig_file, refact_file):
        self.orig_file = orig_file
        self.refact_file = refact_file
        self.orig_tree = ET.parse(orig_file)
        self.refact_tree = ET.parse(refact_file)
        self.orig_root = self.orig_tree.getroot()
        self.refact_root = self.refact_tree.getroot()
        
        # 模块分析顺序
        self.modules = [
            "路由模式/透明模式配置",
            "接口配置", 
            "地址对象",
            "地址对象组",
            "服务对象",
            "服务对象组", 
            "安全区域",
            "时间对象",
            "DNS配置",
            "静态路由",
            "安全策略"
        ]
        
        self.analysis_results = {}
    
    def analyze_module_1_routing_mode(self):
        """模块1：路由模式/透明模式配置分析"""
        print('=== 模块1：路由模式/透明模式配置分析 ===')
        
        def extract_routing_config(root, label):
            configs = {}
            
            # 查找路由相关配置
            for elem in root.iter():
                tag_name = elem.tag.split('}')[-1] if '}' in elem.tag else elem.tag
                
                if tag_name in ['routing-mode', 'transparent-mode', 'forwarding-mode']:
                    configs[tag_name] = elem.text if elem.text else 'present'
                
                # 查找VRF配置
                if tag_name == 'vrf':
                    vrf_name = None
                    for child in elem:
                        child_tag = child.tag.split('}')[-1] if '}' in child.tag else child.tag
                        if child_tag == 'name':
                            vrf_name = child.text
                            break
                    if vrf_name:
                        configs[f'vrf_{vrf_name}'] = 'present'
            
            print(f'  {label}路由模式配置: {len(configs)}个')
            for config_type, value in configs.items():
                print(f'    {config_type}: {value}')
            
            return configs
        
        orig_configs = extract_routing_config(self.orig_root, '原版')
        refact_configs = extract_routing_config(self.refact_root, '重构版')
        
        # 对比分析
        missing_in_refact = set(orig_configs.keys()) - set(refact_configs.keys())
        extra_in_refact = set(refact_configs.keys()) - set(orig_configs.keys())
        
        result = {
            'orig_count': len(orig_configs),
            'refact_count': len(refact_configs),
            'missing_in_refact': list(missing_in_refact),
            'extra_in_refact': list(extra_in_refact),
            'equivalence': len(missing_in_refact) == 0 and len(extra_in_refact) == 0
        }
        
        print(f'  对比结果: 原版{len(orig_configs)}个 vs 重构版{len(refact_configs)}个')
        if missing_in_refact:
            print(f'  重构版缺失: {missing_in_refact}')
        if extra_in_refact:
            print(f'  重构版额外: {extra_in_refact}')
        
        self.analysis_results['routing_mode'] = result
        return result
    
    def analyze_module_2_interface_config(self):
        """模块2：接口配置分析"""
        print('\n=== 模块2：接口配置分析 ===')

        def extract_interface_config(root, label):
            interfaces = {}

            # 查找interface容器下的所有接口配置
            for elem in root.iter():
                tag_name = elem.tag.split('}')[-1] if '}' in elem.tag else elem.tag

                # 查找physical、vlan、vswitch等接口类型
                if tag_name in ['physical', 'vlan', 'vswitch']:
                    # 获取接口名称
                    interface_name = None
                    config_details = {}

                    for child in elem:
                        child_tag = child.tag.split('}')[-1] if '}' in child.tag else child.tag
                        if child_tag == 'name':
                            interface_name = child.text
                        elif child.text and child.text.strip():
                            config_details[child_tag] = child.text.strip()

                    if interface_name:
                        interfaces[interface_name] = {
                            'type': tag_name,
                            'config_count': len(config_details),
                            'configs': config_details
                        }

                # 查找安全区域中的接口引用
                elif tag_name == 'zone':
                    zone_name = None
                    zone_interfaces = []

                    for child in elem:
                        child_tag = child.tag.split('}')[-1] if '}' in child.tag else child.tag
                        if child_tag == 'name':
                            zone_name = child.text
                        elif child_tag == 'interface':
                            # 查找接口名称
                            for grandchild in child:
                                grandchild_tag = grandchild.tag.split('}')[-1] if '}' in grandchild.tag else grandchild.tag
                                if grandchild_tag == 'name' and grandchild.text:
                                    zone_interfaces.append(grandchild.text)

                    # 记录区域接口绑定
                    for interface_name in zone_interfaces:
                        if interface_name not in interfaces:
                            interfaces[interface_name] = {
                                'type': 'zone_reference',
                                'config_count': 1,
                                'configs': {'zone': zone_name}
                            }
                        else:
                            interfaces[interface_name]['configs']['zone'] = zone_name

            print(f'  {label}接口配置: {len(interfaces)}个接口')

            # 统计接口类型
            interface_types = defaultdict(int)
            config_types = defaultdict(int)

            for interface_data in interfaces.values():
                interface_types[interface_data['type']] += 1
                for config_type in interface_data['configs'].keys():
                    config_types[config_type] += 1

            print(f'    接口类型分布:')
            for interface_type, count in sorted(interface_types.items(), key=lambda x: x[1], reverse=True):
                print(f'      {interface_type}: {count}个')

            print(f'    配置类型分布（前10个）:')
            for config_type, count in sorted(config_types.items(), key=lambda x: x[1], reverse=True)[:10]:
                print(f'      {config_type}: {count}个接口')

            return interfaces, config_types
        
        orig_interfaces, orig_config_types = extract_interface_config(self.orig_root, '原版')
        refact_interfaces, refact_config_types = extract_interface_config(self.refact_root, '重构版')
        
        # 对比分析
        missing_interfaces = set(orig_interfaces.keys()) - set(refact_interfaces.keys())
        extra_interfaces = set(refact_interfaces.keys()) - set(orig_interfaces.keys())
        
        # 配置类型对比
        missing_config_types = []
        for config_type, orig_count in orig_config_types.items():
            refact_count = refact_config_types.get(config_type, 0)
            if refact_count < orig_count:
                missing_config_types.append((config_type, orig_count - refact_count))
        
        result = {
            'orig_interface_count': len(orig_interfaces),
            'refact_interface_count': len(refact_interfaces),
            'missing_interfaces': list(missing_interfaces),
            'extra_interfaces': list(extra_interfaces),
            'missing_config_types': missing_config_types,
            'equivalence': len(missing_interfaces) == 0 and len(missing_config_types) == 0
        }
        
        print(f'  对比结果: 原版{len(orig_interfaces)}个 vs 重构版{len(refact_interfaces)}个接口')
        if missing_interfaces:
            print(f'  重构版缺失接口: {list(missing_interfaces)[:5]}...')
        if missing_config_types:
            print(f'  重构版缺失配置类型: {missing_config_types[:5]}...')
        
        self.analysis_results['interface_config'] = result
        return result
    
    def analyze_module_3_address_objects(self):
        """模块3：地址对象分析"""
        print('\n=== 模块3：地址对象分析 ===')
        
        def extract_address_objects(root, label):
            address_sets = []
            
            for elem in root.iter():
                if elem.tag.endswith('address-set') and 'address-set' in elem.tag:
                    # 确保这是独立的address-set，不是address-group内的引用
                    parent = elem.getparent() if hasattr(elem, 'getparent') else None
                    if parent is None or not parent.tag.endswith('address-group'):
                        name_elem = None
                        ip_sets = []
                        
                        for child in elem:
                            if child.tag.endswith('name'):
                                name_elem = child
                            elif child.tag.endswith('ip-set'):
                                ip_sets.append(child)
                        
                        if name_elem is not None and name_elem.text:
                            address_sets.append({
                                'name': name_elem.text,
                                'ip_set_count': len(ip_sets),
                                'yang_compliant': len(ip_sets) > 0
                            })
            
            yang_compliant = sum(1 for addr in address_sets if addr['yang_compliant'])
            
            print(f'  {label}独立address-set: {len(address_sets)}个')
            print(f'    YANG合规: {yang_compliant}个 ({yang_compliant/len(address_sets)*100:.1f}%)' if address_sets else '    YANG合规: 0个')
            
            return address_sets
        
        orig_address_sets = extract_address_objects(self.orig_root, '原版')
        refact_address_sets = extract_address_objects(self.refact_root, '重构版')
        
        # 对比分析
        orig_names = {addr['name'] for addr in orig_address_sets}
        refact_names = {addr['name'] for addr in refact_address_sets}
        
        result = {
            'orig_count': len(orig_address_sets),
            'refact_count': len(refact_address_sets),
            'orig_yang_compliant': sum(1 for addr in orig_address_sets if addr['yang_compliant']),
            'refact_yang_compliant': sum(1 for addr in refact_address_sets if addr['yang_compliant']),
            'missing_in_refact': len(orig_names - refact_names),
            'extra_in_refact': len(refact_names - orig_names),
            'equivalence': orig_names == refact_names
        }
        
        print(f'  对比结果: 原版{len(orig_address_sets)}个 vs 重构版{len(refact_address_sets)}个')
        print(f'  YANG合规性: 原版{result["orig_yang_compliant"]}个 vs 重构版{result["refact_yang_compliant"]}个')
        
        self.analysis_results['address_objects'] = result
        return result

    def analyze_module_4_address_groups(self):
        """模块4：地址对象组分析"""
        print('\n=== 模块4：地址对象组分析 ===')

        def extract_address_groups(root, label):
            address_groups = []

            for elem in root.iter():
                if elem.tag.endswith('address-group') and 'address-group' in elem.tag:
                    name_elem = None
                    address_set_refs = []

                    for child in elem:
                        if child.tag.endswith('name'):
                            name_elem = child
                        elif child.tag.endswith('address-set'):
                            address_set_refs.append(child)

                    if name_elem is not None and name_elem.text:
                        address_groups.append({
                            'name': name_elem.text,
                            'address_set_count': len(address_set_refs),
                            'yang_compliant': len(address_set_refs) > 0
                        })

            yang_compliant = sum(1 for group in address_groups if group['yang_compliant'])

            print(f'  {label}address-group: {len(address_groups)}个')
            print(f'    YANG合规: {yang_compliant}个 ({yang_compliant/len(address_groups)*100:.1f}%)' if address_groups else '    YANG合规: 0个')

            return address_groups

        orig_address_groups = extract_address_groups(self.orig_root, '原版')
        refact_address_groups = extract_address_groups(self.refact_root, '重构版')

        # 对比分析
        orig_names = {group['name'] for group in orig_address_groups}
        refact_names = {group['name'] for group in refact_address_groups}

        result = {
            'orig_count': len(orig_address_groups),
            'refact_count': len(refact_address_groups),
            'orig_yang_compliant': sum(1 for group in orig_address_groups if group['yang_compliant']),
            'refact_yang_compliant': sum(1 for group in refact_address_groups if group['yang_compliant']),
            'missing_in_refact': len(orig_names - refact_names),
            'extra_in_refact': len(refact_names - orig_names),
            'equivalence': orig_names == refact_names and len(orig_address_groups) == len(refact_address_groups)
        }

        print(f'  对比结果: 原版{len(orig_address_groups)}个 vs 重构版{len(refact_address_groups)}个')
        print(f'  YANG合规性: 原版{result["orig_yang_compliant"]}个 vs 重构版{result["refact_yang_compliant"]}个')

        self.analysis_results['address_groups'] = result
        return result

    def analyze_module_5_service_objects(self):
        """模块5：服务对象分析"""
        print('\n=== 模块5：服务对象分析 ===')

        def extract_service_objects(root, label):
            services = []

            for elem in root.iter():
                if elem.tag.endswith('service-set') and 'service-set' in elem.tag:
                    name_elem = None
                    config_details = {}

                    for child in elem:
                        child_tag = child.tag.split('}')[-1] if '}' in child.tag else child.tag
                        if child_tag == 'name':
                            name_elem = child
                        elif child.text and child.text.strip():
                            config_details[child_tag] = child.text.strip()

                    if name_elem is not None and name_elem.text:
                        services.append({
                            'name': name_elem.text,
                            'config_count': len(config_details),
                            'configs': config_details
                        })

            print(f'  {label}service-set: {len(services)}个')

            # 统计服务类型
            service_types = defaultdict(int)
            for service in services:
                for config_type in service['configs'].keys():
                    service_types[config_type] += 1

            print(f'    配置类型分布（前5个）:')
            for config_type, count in sorted(service_types.items(), key=lambda x: x[1], reverse=True)[:5]:
                print(f'      {config_type}: {count}个服务')

            return services, service_types

        orig_services, orig_service_types = extract_service_objects(self.orig_root, '原版')
        refact_services, refact_service_types = extract_service_objects(self.refact_root, '重构版')

        # 对比分析
        orig_names = {service['name'] for service in orig_services}
        refact_names = {service['name'] for service in refact_services}

        result = {
            'orig_count': len(orig_services),
            'refact_count': len(refact_services),
            'missing_in_refact': len(orig_names - refact_names),
            'extra_in_refact': len(refact_names - orig_names),
            'equivalence': orig_names == refact_names
        }

        print(f'  对比结果: 原版{len(orig_services)}个 vs 重构版{len(refact_services)}个')

        self.analysis_results['service_objects'] = result
        return result

    def analyze_module_6_service_groups(self):
        """模块6：服务对象组分析"""
        print('\n=== 模块6：服务对象组分析 ===')

        def extract_service_groups(root, label):
            service_groups = []

            for elem in root.iter():
                if elem.tag.endswith('service-group') and 'service-group' in elem.tag:
                    name_elem = None
                    service_set_refs = []

                    for child in elem:
                        if child.tag.endswith('name'):
                            name_elem = child
                        elif child.tag.endswith('service-set-name'):
                            service_set_refs.append(child)

                    if name_elem is not None and name_elem.text:
                        service_groups.append({
                            'name': name_elem.text,
                            'service_set_count': len(service_set_refs),
                            'yang_compliant': len(service_set_refs) > 0
                        })

            yang_compliant = sum(1 for group in service_groups if group['yang_compliant'])

            print(f'  {label}service-group: {len(service_groups)}个')
            print(f'    YANG合规: {yang_compliant}个 ({yang_compliant/len(service_groups)*100:.1f}%)' if service_groups else '    YANG合规: 0个')

            return service_groups

        orig_service_groups = extract_service_groups(self.orig_root, '原版')
        refact_service_groups = extract_service_groups(self.refact_root, '重构版')

        # 对比分析
        orig_names = {group['name'] for group in orig_service_groups}
        refact_names = {group['name'] for group in refact_service_groups}

        result = {
            'orig_count': len(orig_service_groups),
            'refact_count': len(refact_service_groups),
            'orig_yang_compliant': sum(1 for group in orig_service_groups if group['yang_compliant']),
            'refact_yang_compliant': sum(1 for group in refact_service_groups if group['yang_compliant']),
            'missing_in_refact': len(orig_names - refact_names),
            'extra_in_refact': len(refact_names - orig_names),
            'equivalence': orig_names == refact_names and len(orig_service_groups) == len(refact_service_groups)
        }

        print(f'  对比结果: 原版{len(orig_service_groups)}个 vs 重构版{len(refact_service_groups)}个')
        print(f'  YANG合规性: 原版{result["orig_yang_compliant"]}个 vs 重构版{result["refact_yang_compliant"]}个')

        self.analysis_results['service_groups'] = result
        return result

    def analyze_module_7_security_zones(self):
        """模块7：安全区域分析 - 修正版本
        基于YANG模型正确识别独立的安全区域定义，不包括策略中的区域引用
        """
        print('\n=== 模块7：安全区域分析 ===')

        def extract_security_zones(root, label):
            zones = []

            # 查找security-zone容器
            for elem in root.iter():
                if elem.tag.endswith('security-zone') and 'security-zone' in elem.tag:
                    # 在security-zone容器内查找独立的zone定义
                    for zone_elem in elem:
                        if zone_elem.tag.endswith('zone') and 'zone' in zone_elem.tag:
                            name_elem = None
                            interface_refs = []
                            config_details = {}

                            for child in zone_elem:
                                child_tag = child.tag.split('}')[-1] if '}' in child.tag else child.tag
                                if child_tag == 'name':
                                    name_elem = child
                                elif child_tag == 'interface':
                                    interface_refs.append(child)
                                elif child.text and child.text.strip():
                                    config_details[child_tag] = child.text.strip()

                            if name_elem is not None and name_elem.text:
                                zones.append({
                                    'name': name_elem.text,
                                    'interface_count': len(interface_refs),
                                    'config_count': len(config_details),
                                    'configs': config_details
                                })

            print(f'  {label}安全区域: {len(zones)}个')

            # 显示区域名称
            zone_names = [zone['name'] for zone in zones]
            print(f'    区域名称: {sorted(zone_names)}')

            # 统计配置类型
            config_types = defaultdict(int)
            for zone in zones:
                for config_type in zone['configs'].keys():
                    config_types[config_type] += 1

            if config_types:
                print(f'    配置类型分布（前5个）:')
                for config_type, count in sorted(config_types.items(), key=lambda x: x[1], reverse=True)[:5]:
                    print(f'      {config_type}: {count}个区域')

            return zones, config_types

        orig_zones, orig_zone_types = extract_security_zones(self.orig_root, '原版')
        refact_zones, refact_zone_types = extract_security_zones(self.refact_root, '重构版')

        # 对比分析
        orig_names = {zone['name'] for zone in orig_zones}
        refact_names = {zone['name'] for zone in refact_zones}

        result = {
            'orig_count': len(orig_zones),
            'refact_count': len(refact_zones),
            'missing_in_refact': len(orig_names - refact_names),
            'extra_in_refact': len(refact_names - orig_names),
            'equivalence': orig_names == refact_names
        }

        print(f'  对比结果: 原版{len(orig_zones)}个 vs 重构版{len(refact_zones)}个')
        if not result['equivalence']:
            if orig_names - refact_names:
                print(f'    重构版缺失: {sorted(orig_names - refact_names)}')
            if refact_names - orig_names:
                print(f'    重构版额外: {sorted(refact_names - orig_names)}')

        self.analysis_results['security_zones'] = result
        return result

    def analyze_module_8_time_objects(self):
        """模块8：时间对象分析"""
        print('\n=== 模块8：时间对象分析 ===')

        def extract_time_objects(root, label):
            time_objects = []

            for elem in root.iter():
                if elem.tag.endswith('time-range') and 'time-range' in elem.tag:
                    name_elem = None
                    config_details = {}

                    for child in elem:
                        child_tag = child.tag.split('}')[-1] if '}' in child.tag else child.tag
                        if child_tag == 'name':
                            name_elem = child
                        elif child.text and child.text.strip():
                            config_details[child_tag] = child.text.strip()

                    if name_elem is not None and name_elem.text:
                        time_objects.append({
                            'name': name_elem.text,
                            'config_count': len(config_details),
                            'configs': config_details
                        })

            print(f'  {label}时间对象: {len(time_objects)}个')
            return time_objects

        orig_time_objects = extract_time_objects(self.orig_root, '原版')
        refact_time_objects = extract_time_objects(self.refact_root, '重构版')

        # 对比分析
        orig_names = {obj['name'] for obj in orig_time_objects}
        refact_names = {obj['name'] for obj in refact_time_objects}

        result = {
            'orig_count': len(orig_time_objects),
            'refact_count': len(refact_time_objects),
            'missing_in_refact': len(orig_names - refact_names),
            'extra_in_refact': len(refact_names - orig_names),
            'equivalence': orig_names == refact_names
        }

        print(f'  对比结果: 原版{len(orig_time_objects)}个 vs 重构版{len(refact_time_objects)}个')

        self.analysis_results['time_objects'] = result
        return result

    def analyze_module_9_dns_config(self):
        """模块9：DNS配置分析"""
        print('\n=== 模块9：DNS配置分析 ===')

        def extract_dns_config(root, label):
            dns_configs = []

            for elem in root.iter():
                if elem.tag.endswith('dns') and 'dns' in elem.tag:
                    config_details = {}

                    for child in elem:
                        child_tag = child.tag.split('}')[-1] if '}' in child.tag else child.tag
                        if child.text and child.text.strip():
                            config_details[child_tag] = child.text.strip()

                    if config_details:
                        dns_configs.append(config_details)

            print(f'  {label}DNS配置: {len(dns_configs)}个')
            return dns_configs

        orig_dns_configs = extract_dns_config(self.orig_root, '原版')
        refact_dns_configs = extract_dns_config(self.refact_root, '重构版')

        result = {
            'orig_count': len(orig_dns_configs),
            'refact_count': len(refact_dns_configs),
            'equivalence': len(orig_dns_configs) == len(refact_dns_configs)
        }

        print(f'  对比结果: 原版{len(orig_dns_configs)}个 vs 重构版{len(refact_dns_configs)}个')

        self.analysis_results['dns_config'] = result
        return result

    def analyze_module_10_static_routes(self):
        """模块10：静态路由分析"""
        print('\n=== 模块10：静态路由分析 ===')

        def extract_static_routes(root, label):
            routes = []

            for elem in root.iter():
                if elem.tag.endswith('route') and 'route' in elem.tag:
                    config_details = {}

                    for child in elem:
                        child_tag = child.tag.split('}')[-1] if '}' in child.tag else child.tag
                        if child.text and child.text.strip():
                            config_details[child_tag] = child.text.strip()

                    if config_details:
                        routes.append(config_details)

            print(f'  {label}静态路由: {len(routes)}个')
            return routes

        orig_routes = extract_static_routes(self.orig_root, '原版')
        refact_routes = extract_static_routes(self.refact_root, '重构版')

        result = {
            'orig_count': len(orig_routes),
            'refact_count': len(refact_routes),
            'equivalence': len(orig_routes) == len(refact_routes)
        }

        print(f'  对比结果: 原版{len(orig_routes)}个 vs 重构版{len(refact_routes)}个')

        self.analysis_results['static_routes'] = result
        return result

    def analyze_module_11_security_policies(self):
        """模块11：安全策略分析"""
        print('\n=== 模块11：安全策略分析 ===')

        def extract_security_policies(root, label):
            policies = []

            for elem in root.iter():
                if elem.tag.endswith('rule') and 'rule' in elem.tag:
                    name_elem = None
                    config_details = {}

                    for child in elem:
                        child_tag = child.tag.split('}')[-1] if '}' in child.tag else child.tag
                        if child_tag == 'name':
                            name_elem = child
                        elif child.text and child.text.strip():
                            config_details[child_tag] = child.text.strip()

                    if name_elem is not None and name_elem.text:
                        policies.append({
                            'name': name_elem.text,
                            'config_count': len(config_details),
                            'configs': config_details
                        })

            print(f'  {label}安全策略: {len(policies)}个')
            return policies

        orig_policies = extract_security_policies(self.orig_root, '原版')
        refact_policies = extract_security_policies(self.refact_root, '重构版')

        # 对比分析
        orig_names = {policy['name'] for policy in orig_policies}
        refact_names = {policy['name'] for policy in refact_policies}

        result = {
            'orig_count': len(orig_policies),
            'refact_count': len(refact_policies),
            'missing_in_refact': len(orig_names - refact_names),
            'extra_in_refact': len(refact_names - orig_names),
            'equivalence': orig_names == refact_names
        }

        print(f'  对比结果: 原版{len(orig_policies)}个 vs 重构版{len(refact_policies)}个')

        self.analysis_results['security_policies'] = result
        return result

    def run_systematic_analysis(self):
        """运行系统性分析"""
        print('=== FortiGate转换器系统性模块化对比分析 ===\n')

        # 执行所有11个模块的分析
        self.analyze_module_1_routing_mode()
        self.analyze_module_2_interface_config()
        self.analyze_module_3_address_objects()
        self.analyze_module_4_address_groups()
        self.analyze_module_5_service_objects()
        self.analyze_module_6_service_groups()
        self.analyze_module_7_security_zones()
        self.analyze_module_8_time_objects()
        self.analyze_module_9_dns_config()
        self.analyze_module_10_static_routes()
        self.analyze_module_11_security_policies()

        # 生成总结
        print(f'\n=== 全部{len(self.analysis_results)}个模块分析总结 ===')
        equivalence_count = sum(1 for result in self.analysis_results.values() if result['equivalence'])
        print(f'功能等价模块: {equivalence_count}/{len(self.analysis_results)}个')

        module_names = {
            'routing_mode': '路由模式配置',
            'interface_config': '接口配置',
            'address_objects': '地址对象',
            'address_groups': '地址对象组',
            'service_objects': '服务对象',
            'service_groups': '服务对象组',
            'security_zones': '安全区域',
            'time_objects': '时间对象',
            'dns_config': 'DNS配置',
            'static_routes': '静态路由',
            'security_policies': '安全策略'
        }

        for module_key, result in self.analysis_results.items():
            module_name = module_names.get(module_key, module_key)
            status = "✅ 等价" if result['equivalence'] else "❌ 需修复"
            print(f'  {module_name}: {status}')

        return self.analysis_results

def main():
    # 使用最新的重构版文件（wanlan配置修复完成版本）
    analyzer = SystematicModuleAnalyzer(
        'output/fortigate-z3200s-R11.xml',
        'data/output/test_wanlan_child_debug.xml'
    )
    
    results = analyzer.run_systematic_analysis()
    
    print('\n=== 下一步行动计划 ===')
    for module_name, result in results.items():
        if not result['equivalence']:
            print(f'需要修复: {module_name}')
            if 'missing_in_refact' in result and result['missing_in_refact']:
                print(f'  - 缺失项目: {result["missing_in_refact"][:3]}...')
            if 'missing_config_types' in result and result['missing_config_types']:
                print(f'  - 缺失配置类型: {result["missing_config_types"][:3]}...')

if __name__ == '__main__':
    main()
