#!/usr/bin/env python3
"""
模板结构分析工具
分析模板文件中security-zone的确切结构
"""

import xml.etree.ElementTree as ET

def analyze_template_structure():
    """分析模板文件中的security-zone结构"""
    print('=== 模板文件中的security-zone结构分析 ===')
    
    try:
        tree = ET.parse('engine/data/input/configData/z5100s/R11/Config/running.xml')
        root = tree.getroot()
        
        # 查找VRF节点
        vrf_count = 0
        for elem in root.iter():
            if 'vrf' in elem.tag:
                vrf_count += 1
                if vrf_count == 1:  # 只分析第一个VRF
                    print(f'VRF节点: {elem.tag}')
                    namespace = elem.tag.split('}')[0] + '}' if '}' in elem.tag else '无'
                    print(f'VRF命名空间: {namespace}')
                    
                    # 在VRF内查找security-zone
                    sz_count = 0
                    for child in elem.iter():
                        if 'security-zone' in child.tag:
                            sz_count += 1
                            if sz_count <= 2:  # 只显示前2个
                                print(f'  Security-zone {sz_count}: {child.tag}')
                                sz_namespace = child.tag.split('}')[0] + '}' if '}' in child.tag else '无'
                                print(f'  Security-zone命名空间: {sz_namespace}')
                                
                                # 查看zone子元素
                                zone_count = 0
                                for zone in child.iter():
                                    if 'zone' in zone.tag and zone != child:
                                        zone_count += 1
                                        if zone_count <= 3:
                                            name_elem = zone.find('name')
                                            zone_name = name_elem.text if name_elem is not None else '未知'
                                            print(f'    Zone {zone_count}: {zone_name}')
                    
                    print(f'VRF内security-zone总数: {sz_count}')
                    break
        
        print(f'总VRF数量: {vrf_count}')
        
    except Exception as e:
        print(f'分析失败: {e}')
        import traceback
        traceback.print_exc()

def analyze_refactored_output():
    """分析重构版输出中的security-zone结构"""
    print('\n=== 重构版输出中的security-zone结构分析 ===')
    
    try:
        tree = ET.parse('data/output/test_refactored_dedup_fixed.xml')
        root = tree.getroot()
        
        # 查找所有security-zone
        sz_count = 0
        for elem in root.iter():
            if 'security-zone' in elem.tag:
                sz_count += 1
                print(f'Security-zone {sz_count}: {elem.tag}')
                
                # 查看zone子元素
                zone_count = 0
                for zone in elem.iter():
                    if 'zone' in zone.tag and zone != elem:
                        zone_count += 1
                        if zone_count <= 5:
                            name_elem = zone.find('name')
                            zone_name = name_elem.text if name_elem is not None else '未知'
                            print(f'  Zone {zone_count}: {zone_name}')
                
                print(f'  该security-zone包含 {zone_count} 个zone')
        
        print(f'重构版总security-zone数量: {sz_count}')
        
    except Exception as e:
        print(f'分析重构版失败: {e}')

def main():
    analyze_template_structure()
    analyze_refactored_output()

if __name__ == '__main__':
    main()
