#!/usr/bin/env python3
"""
验证原版基准文件工具
确认fortigate-z3200s-R11.xml确实由原版XmlTemplateIntegrationStage生成
"""

import xml.etree.ElementTree as ET
import os
from datetime import datetime

def verify_original_baseline():
    """验证原版基准文件的生成和质量"""
    baseline_file = 'output/fortigate-z3200s-R11.xml'
    
    print('=== 验证原版基准文件 ===')
    print(f'文件: {baseline_file}')
    
    # 检查文件基本信息
    if os.path.exists(baseline_file):
        file_size = os.path.getsize(baseline_file)
        file_time = datetime.fromtimestamp(os.path.getmtime(baseline_file))
        print(f'文件大小: {file_size:,} 字节')
        print(f'修改时间: {file_time}')
    else:
        print('❌ 文件不存在')
        return None
    
    try:
        tree = ET.parse(baseline_file)
        root = tree.getroot()
        
        # 分析XML结构
        print(f'\n=== XML结构分析 ===')
        print(f'根元素: {root.tag}')
        print(f'根命名空间: {root.tag.split("}")[0] + "}" if "}" in root.tag else "无"}')
        
        # 统计各类配置元素
        stats = {
            'address_sets': 0,
            'service_sets': 0,
            'security_zones': 0,
            'policies': 0,
            'interfaces': 0
        }
        
        for elem in root.iter():
            if 'address-set' in elem.tag:
                stats['address_sets'] += 1
            elif 'service-set' in elem.tag:
                stats['service_sets'] += 1
            elif 'security-zone' in elem.tag:
                stats['security_zones'] += 1
            elif 'policy' in elem.tag and 'security' in elem.tag:
                stats['policies'] += 1
            elif 'interface' in elem.tag:
                stats['interfaces'] += 1
        
        print(f'\n=== 配置元素统计 ===')
        for key, value in stats.items():
            print(f'{key}: {value}个')
        
        # 详细分析地址对象
        print(f'\n=== 地址对象详细分析 ===')
        address_sets = []
        for elem in root.iter():
            if 'address-set' in elem.tag:
                address_sets.append(elem)
        
        print(f'总地址对象数量: {len(address_sets)}')
        
        # 检查前10个地址对象的结构
        yang_compliant = 0
        name_missing = 0
        ip_missing = 0
        
        print(f'\n前10个地址对象详情:')
        for i, addr_set in enumerate(address_sets[:10]):
            # 修正name元素查找逻辑 - 考虑命名空间
            name_elem = None
            for child in addr_set:
                if child.tag.endswith('name') or 'name' in child.tag:
                    name_elem = child
                    break

            has_name = name_elem is not None and name_elem.text and name_elem.text.strip()

            # 修正ip-set查找逻辑
            ip_sets = []
            for child in addr_set:
                if child.tag.endswith('ip-set') or 'ip-set' in child.tag:
                    ip_sets.append(child)

            # 修正ip-address查找逻辑 - 在ip-set内查找
            ip_addresses = []
            for ip_set in ip_sets:
                for ip_child in ip_set:
                    if ip_child.tag.endswith('ip-address') or 'ip-address' in ip_child.tag:
                        if ip_child.text and ip_child.text.strip():
                            ip_addresses.append(ip_child.text.strip())

            is_compliant = has_name and len(ip_sets) > 0 and len(ip_addresses) > 0
            
            if is_compliant:
                yang_compliant += 1
            if not has_name:
                name_missing += 1
            if len(ip_addresses) == 0:
                ip_missing += 1
            
            print(f'  地址对象 {i+1}:')
            print(f'    Name: {name_elem.text if has_name else "❌ 缺失"}')
            print(f'    IP-sets: {len(ip_sets)}个')
            print(f'    IP-addresses: {len(ip_addresses)}个')
            print(f'    YANG合规: {"✅" if is_compliant else "❌"}')
        
        # 整体YANG合规性统计
        total_compliant = 0
        total_name_missing = 0
        total_ip_missing = 0
        
        for addr_set in address_sets:
            # 修正name元素查找逻辑 - 考虑命名空间
            name_elem = None
            for child in addr_set:
                if child.tag.endswith('name') or 'name' in child.tag:
                    name_elem = child
                    break

            has_name = name_elem is not None and name_elem.text and name_elem.text.strip()

            # 修正ip-set查找逻辑
            ip_sets = []
            for child in addr_set:
                if child.tag.endswith('ip-set') or 'ip-set' in child.tag:
                    ip_sets.append(child)

            # 修正ip-address查找逻辑 - 在ip-set内查找
            ip_addresses = []
            for ip_set in ip_sets:
                for ip_child in ip_set:
                    if ip_child.tag.endswith('ip-address') or 'ip-address' in ip_child.tag:
                        if ip_child.text and ip_child.text.strip():
                            ip_addresses.append(ip_child.text.strip())

            is_compliant = has_name and len(ip_sets) > 0 and len(ip_addresses) > 0
            
            if is_compliant:
                total_compliant += 1
            if not has_name:
                total_name_missing += 1
            if len(ip_addresses) == 0:
                total_ip_missing += 1
        
        print(f'\n=== YANG模型合规性统计 ===')
        print(f'总地址对象: {len(address_sets)}个')
        print(f'YANG合规: {total_compliant}个 ({total_compliant/len(address_sets)*100:.1f}%)')
        print(f'缺少name: {total_name_missing}个 ({total_name_missing/len(address_sets)*100:.1f}%)')
        print(f'缺少IP: {total_ip_missing}个 ({total_ip_missing/len(address_sets)*100:.1f}%)')
        
        # 质量评估
        print(f'\n=== 基准文件质量评估 ===')
        if total_compliant > len(address_sets) * 0.8:
            print('✅ 基准文件质量良好，符合YANG规范')
            quality = 'good'
        elif total_name_missing == len(address_sets):
            print('❌ 基准文件存在严重问题：100%缺少name元素')
            quality = 'poor_name'
        else:
            print('⚠️ 基准文件质量一般，存在部分问题')
            quality = 'medium'
        
        return {
            'file_size': file_size,
            'file_time': file_time,
            'stats': stats,
            'address_analysis': {
                'total': len(address_sets),
                'compliant': total_compliant,
                'name_missing': total_name_missing,
                'ip_missing': total_ip_missing
            },
            'quality': quality
        }
        
    except Exception as e:
        print(f'❌ 分析失败: {e}')
        import traceback
        traceback.print_exc()
        return None

def compare_with_previous_baseline():
    """与之前的基准文件对比"""
    print(f'\n=== 与之前基准文件对比 ===')
    
    previous_files = [
        'data/output/test_original_verified.xml',
        'data/output/test_original_correct_files.xml'
    ]
    
    current_file = 'output/fortigate-z3200s-R11.xml'
    
    for prev_file in previous_files:
        if os.path.exists(prev_file):
            print(f'\n对比文件: {prev_file}')
            
            # 文件大小对比
            current_size = os.path.getsize(current_file)
            prev_size = os.path.getsize(prev_file)
            
            print(f'  当前文件大小: {current_size:,} 字节')
            print(f'  之前文件大小: {prev_size:,} 字节')
            print(f'  大小差异: {current_size - prev_size:+,} 字节')
            
            # 简单元素数量对比
            try:
                tree1 = ET.parse(current_file)
                tree2 = ET.parse(prev_file)
                
                current_addr = len([e for e in tree1.getroot().iter() if 'address-set' in e.tag])
                prev_addr = len([e for e in tree2.getroot().iter() if 'address-set' in e.tag])
                
                print(f'  当前地址对象: {current_addr}个')
                print(f'  之前地址对象: {prev_addr}个')
                print(f'  地址对象差异: {current_addr - prev_addr:+}个')
                
            except Exception as e:
                print(f'  对比分析失败: {e}')

def main():
    print('开始验证原版基准文件...\n')
    
    # 验证当前基准文件
    baseline_info = verify_original_baseline()
    
    if baseline_info:
        # 与之前的基准文件对比
        compare_with_previous_baseline()
        
        # 总结建议
        print(f'\n=== 验证总结和建议 ===')
        if baseline_info['quality'] == 'good':
            print('✅ 建议使用此文件作为原版基准进行对比分析')
        elif baseline_info['quality'] == 'poor_name':
            print('❌ 确认原版存在严重的name元素缺失bug')
            print('💡 建议重构版生成正确的、符合YANG规范的地址对象')
        else:
            print('⚠️ 需要进一步分析基准文件质量')
    
    print('\n=== 验证完成 ===')

if __name__ == '__main__':
    main()
