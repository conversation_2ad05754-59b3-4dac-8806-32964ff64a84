#!/usr/bin/env python3
"""
XML输出一致性验证工具
用于对比重构版与原版生成的XML文件，量化一致性得分
"""

import os
import sys
import json
import argparse
from typing import Dict, List, Tuple, Any
from lxml import etree
from difflib import SequenceMatcher
import logging

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

class XMLConsistencyValidator:
    """XML一致性验证器"""
    
    def __init__(self):
        self.logger = self._setup_logger()
        self.validation_results = {}
        
    def _setup_logger(self):
        """设置日志记录器"""
        logger = logging.getLogger('XMLValidator')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger
    
    def validate_xml_syntax(self, xml_file: str) -> bool:
        """验证XML语法正确性"""
        try:
            with open(xml_file, 'r', encoding='utf-8') as f:
                etree.parse(f)
            self.logger.info(f"XML语法验证通过: {xml_file}")
            return True
        except Exception as e:
            self.logger.error(f"XML语法验证失败: {xml_file}, 错误: {e}")
            return False
    
    def extract_configuration_sections(self, xml_file: str) -> Dict[str, Any]:
        """提取主要配置节"""
        try:
            with open(xml_file, 'r', encoding='utf-8') as f:
                root = etree.parse(f).getroot()
            
            sections = {}
            
            # 提取安全区域
            security_zones = root.xpath(".//*[local-name()='security-zone']")
            sections['security_zones'] = len(security_zones)
            
            # 提取网络对象
            network_objs = root.xpath(".//*[local-name()='network-obj']")
            sections['network_objects'] = len(network_objs)
            
            # 提取服务对象
            service_objs = root.xpath(".//*[local-name()='service-obj']")
            sections['service_objects'] = len(service_objs)
            
            # 提取安全策略
            security_policies = root.xpath(".//*[local-name()='security-policy']")
            sections['security_policies'] = len(security_policies)
            
            # 提取地址对象数量
            address_sets = root.xpath(".//*[local-name()='address-set']")
            sections['address_sets'] = len(address_sets)
            
            # 提取服务集数量
            service_sets = root.xpath(".//*[local-name()='service-set']")
            sections['service_sets'] = len(service_sets)
            
            # 提取策略数量
            policies = root.xpath(".//*[local-name()='policy']")
            sections['policies'] = len(policies)
            
            # 检查AAA配置
            aaa_configs = root.xpath(".//*[local-name()='aaa']")
            sections['aaa_present'] = len(aaa_configs) > 0
            
            # 检查根命名空间
            sections['root_namespace'] = root.get('xmlns', '')
            
            return sections
            
        except Exception as e:
            self.logger.error(f"配置节提取失败: {xml_file}, 错误: {e}")
            return {}
    
    def calculate_semantic_similarity(self, original_sections: Dict, refactored_sections: Dict) -> float:
        """计算语义相似度得分"""
        total_score = 0
        max_score = 0
        
        # 数值型指标对比
        numeric_keys = ['security_zones', 'network_objects', 'service_objects', 
                       'security_policies', 'address_sets', 'service_sets', 'policies']
        
        for key in numeric_keys:
            max_score += 1
            orig_val = original_sections.get(key, 0)
            refact_val = refactored_sections.get(key, 0)
            
            if orig_val == 0 and refact_val == 0:
                total_score += 1  # 都为0，完全匹配
            elif orig_val == 0 or refact_val == 0:
                total_score += 0  # 一个为0一个不为0，完全不匹配
            else:
                # 计算相对差异
                similarity = 1 - abs(orig_val - refact_val) / max(orig_val, refact_val)
                total_score += max(0, similarity)
        
        # 布尔型指标对比
        max_score += 1
        if original_sections.get('aaa_present') == refactored_sections.get('aaa_present'):
            total_score += 1
        
        # 命名空间对比
        max_score += 1
        if original_sections.get('root_namespace') == refactored_sections.get('root_namespace'):
            total_score += 1
        
        return (total_score / max_score) * 100 if max_score > 0 else 0
    
    def calculate_file_similarity(self, original_file: str, refactored_file: str) -> float:
        """计算文件内容相似度"""
        try:
            with open(original_file, 'r', encoding='utf-8') as f:
                original_content = f.read()
            with open(refactored_file, 'r', encoding='utf-8') as f:
                refactored_content = f.read()
            
            # 使用SequenceMatcher计算相似度
            matcher = SequenceMatcher(None, original_content, refactored_content)
            return matcher.ratio() * 100
            
        except Exception as e:
            self.logger.error(f"文件相似度计算失败: {e}")
            return 0
    
    def validate_consistency(self, original_file: str, refactored_file: str) -> Dict[str, Any]:
        """验证两个XML文件的一致性"""
        result = {
            'original_file': original_file,
            'refactored_file': refactored_file,
            'validation_passed': False,
            'consistency_score': 0,
            'details': {}
        }
        
        # 1. XML语法验证
        original_syntax_ok = self.validate_xml_syntax(original_file)
        refactored_syntax_ok = self.validate_xml_syntax(refactored_file)
        
        result['details']['original_syntax_valid'] = original_syntax_ok
        result['details']['refactored_syntax_valid'] = refactored_syntax_ok
        
        if not (original_syntax_ok and refactored_syntax_ok):
            self.logger.error("XML语法验证失败，无法继续一致性验证")
            return result
        
        # 2. 提取配置节
        original_sections = self.extract_configuration_sections(original_file)
        refactored_sections = self.extract_configuration_sections(refactored_file)
        
        result['details']['original_sections'] = original_sections
        result['details']['refactored_sections'] = refactored_sections
        
        # 3. 计算语义相似度
        semantic_score = self.calculate_semantic_similarity(original_sections, refactored_sections)
        result['details']['semantic_similarity'] = semantic_score
        
        # 4. 计算文件相似度
        file_score = self.calculate_file_similarity(original_file, refactored_file)
        result['details']['file_similarity'] = file_score
        
        # 5. 计算综合一致性得分
        # 语义相似度权重70%，文件相似度权重30%
        consistency_score = semantic_score * 0.7 + file_score * 0.3
        result['consistency_score'] = consistency_score
        
        # 6. 判断是否通过验证（≥95%）
        result['validation_passed'] = consistency_score >= 95.0
        
        # 7. 记录详细对比信息
        self._log_detailed_comparison(original_sections, refactored_sections)
        
        return result
    
    def _log_detailed_comparison(self, original: Dict, refactored: Dict):
        """记录详细对比信息"""
        self.logger.info("=== 详细配置对比 ===")
        
        for key in original.keys():
            orig_val = original.get(key, 'N/A')
            refact_val = refactored.get(key, 'N/A')
            
            if orig_val == refact_val:
                status = "✅ 匹配"
            else:
                status = "❌ 不匹配"
            
            self.logger.info(f"{key}: 原版={orig_val}, 重构版={refact_val} {status}")
    
    def batch_validate(self, test_cases: List[Dict[str, str]]) -> Dict[str, Any]:
        """批量验证多个测试用例"""
        results = []
        total_score = 0
        passed_count = 0
        
        self.logger.info(f"开始批量验证 {len(test_cases)} 个测试用例")
        
        for i, test_case in enumerate(test_cases, 1):
            self.logger.info(f"验证测试用例 {i}/{len(test_cases)}: {test_case['name']}")
            
            result = self.validate_consistency(
                test_case['original_file'],
                test_case['refactored_file']
            )
            result['test_case_name'] = test_case['name']
            results.append(result)
            
            total_score += result['consistency_score']
            if result['validation_passed']:
                passed_count += 1
            
            self.logger.info(f"测试用例 {i} 一致性得分: {result['consistency_score']:.2f}%")
        
        # 计算总体统计
        average_score = total_score / len(test_cases) if test_cases else 0
        pass_rate = (passed_count / len(test_cases)) * 100 if test_cases else 0
        
        summary = {
            'total_test_cases': len(test_cases),
            'passed_cases': passed_count,
            'failed_cases': len(test_cases) - passed_count,
            'pass_rate': pass_rate,
            'average_consistency_score': average_score,
            'overall_validation_passed': average_score >= 95.0 and pass_rate >= 95.0,
            'results': results
        }
        
        self.logger.info("=== 批量验证总结 ===")
        self.logger.info(f"总测试用例: {summary['total_test_cases']}")
        self.logger.info(f"通过用例: {summary['passed_cases']}")
        self.logger.info(f"失败用例: {summary['failed_cases']}")
        self.logger.info(f"通过率: {summary['pass_rate']:.2f}%")
        self.logger.info(f"平均一致性得分: {summary['average_consistency_score']:.2f}%")
        self.logger.info(f"总体验证结果: {'✅ 通过' if summary['overall_validation_passed'] else '❌ 失败'}")
        
        return summary

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='XML输出一致性验证工具')
    parser.add_argument('--original', required=True, help='原版XML文件路径')
    parser.add_argument('--refactored', required=True, help='重构版XML文件路径')
    parser.add_argument('--output', help='结果输出文件路径')
    
    args = parser.parse_args()
    
    validator = XMLConsistencyValidator()
    result = validator.validate_consistency(args.original, args.refactored)
    
    # 输出结果
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        print(f"验证结果已保存到: {args.output}")
    
    # 打印总结
    print(f"\n=== 验证结果总结 ===")
    print(f"一致性得分: {result['consistency_score']:.2f}%")
    print(f"验证结果: {'✅ 通过' if result['validation_passed'] else '❌ 失败'}")
    
    return 0 if result['validation_passed'] else 1

if __name__ == '__main__':
    sys.exit(main())
