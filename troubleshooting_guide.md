# 四层优化策略故障排除指南

## 🚨 常见问题诊断

### 1. 优化未执行问题

#### 问题症状
```json
{
  "optimization_executed": false,
  "optimization_ratio": 0,
  "stage_status": "completed"
}
```

#### 诊断步骤

**Step 1: 检查初始化状态**
```python
# 检查脚本
python -c "
from engine.processing.stages.four_tier_optimization_stage import FourTierOptimizationStage
from engine.business.config.config_manager import ConfigManager

stage = FourTierOptimizationStage(ConfigManager())
print(f'optimization_enabled: {stage.optimization_enabled}')
print(f'architecture: {stage.optimization_architecture is not None}')
"
```

**Step 2: 检查配置段落提取**
```python
# 调试脚本
def debug_section_extraction():
    from engine.processing.stages.four_tier_optimization_stage import FourTierOptimizationStage
    from engine.business.config.config_manager import ConfigManager
    
    stage = FourTierOptimizationStage(ConfigManager())
    
    # 创建测试上下文
    class TestContext:
        def __init__(self):
            self.data = {
                'config_data': {
                    'interfaces': {'port1': {'ip': '***********'}},
                    'firewall_policies': [{'id': 1, 'action': 'accept'}]
                }
            }
        def get_data(self, key, default=None):
            return self.data.get(key, default)
    
    context = TestContext()
    sections = stage._extract_configuration_sections(context)
    
    print(f"提取的段落数: {len(sections)}")
    for name, content in sections.items():
        print(f"  {name}: {len(content)} 行")
    
    return len(sections) > 0

debug_section_extraction()
```

**Step 3: 检查分类器工作状态**
```python
def debug_classifier():
    from engine.processing.stages.four_tier_optimization_stage import FourTierOptimizationStage
    from engine.business.config.config_manager import ConfigManager
    
    stage = FourTierOptimizationStage(ConfigManager())
    
    # 测试分类器
    test_sections = {
        'gui_settings': ['set theme dark'],
        'firewall_policy': ['set srcintf any']
    }
    
    for name, content in test_sections.items():
        try:
            result = stage.optimization_architecture.classifier.classify_section(name, content)
            print(f"{name}: {result.tier.value} -> {result.strategy.value}")
        except Exception as e:
            print(f"分类器错误 {name}: {e}")

debug_classifier()
```

#### 解决方案

1. **配置问题**
   ```python
   # 确保配置正确
   config = {
       'four_tier_optimization': {
           'enabled': True,
           'target_optimization_ratio': 0.4,
           'quality_threshold': 0.95
       }
   }
   ```

2. **数据源问题**
   ```python
   # 确保上下文包含有效数据
   context.set_data("config_data", valid_config_data)
   context.set_data("fortigate_data", valid_fortigate_data)
   ```

3. **组件初始化问题**
   ```python
   # 重新初始化组件
   stage = FourTierOptimizationStage(config_manager)
   assert stage.optimization_enabled == True
   ```

### 2. 质量分数过低问题

#### 问题症状
```json
{
  "quality_score": 0.85,
  "target_quality": 0.95
}
```

#### 诊断步骤

**Step 1: 检查质量影响分数**
```python
def debug_quality_calculation():
    # 检查各层级的质量影响分数设置
    tier_impacts = {
        'safe_skip': 0.02,      # 应该很小
        'conditional_skip': 0.05, # 应该较小
        'important_retain': 0.0,   # 应该为0
        'critical_full': 0.0       # 应该为0
    }
    
    for tier, impact in tier_impacts.items():
        print(f"{tier}: {impact}")
        if impact > 0.1:
            print(f"  ⚠️ {tier} 质量影响过大")

debug_quality_calculation()
```

**Step 2: 分析段落分布**
```python
def analyze_section_distribution():
    # 分析各层级段落分布
    from collections import Counter
    
    # 模拟分析结果
    tier_distribution = {
        'safe_skip': 10,      # 40%
        'conditional_skip': 8, # 32%
        'important_retain': 0, # 0%
        'critical_full': 7     # 28%
    }
    
    total = sum(tier_distribution.values())
    
    for tier, count in tier_distribution.items():
        percentage = count / total * 100
        print(f"{tier}: {count} ({percentage:.1f}%)")
        
        # 检查是否合理
        if tier == 'critical_full' and percentage < 20:
            print(f"  ⚠️ 关键段落比例过低")
        elif tier == 'safe_skip' and percentage > 50:
            print(f"  ⚠️ 跳过段落比例过高")

analyze_section_distribution()
```

#### 解决方案

1. **调整质量影响分数**
   ```python
   # 降低质量影响分数
   QUALITY_IMPACT_SCORES = {
       'safe_skip': 0.01,      # 从0.02降到0.01
       'conditional_skip': 0.03, # 从0.05降到0.03
       'important_retain': 0.0,
       'critical_full': 0.0
   }
   ```

2. **优化质量计算公式**
   ```python
   # 增加完整处理加分
   base_quality = 0.96  # 从0.95提高到0.96
   full_processing_bonus = (sections_full_processed / total_sections) * 0.08  # 从0.05提高到0.08
   ```

3. **调整分类策略**
   ```python
   # 将更多段落归类为关键完整处理
   CRITICAL_PATTERNS = [
       'firewall', 'policy', 'interface', 'zone', 'route',
       'security', 'nat', 'vpn'  # 添加更多关键模式
   ]
   ```

### 3. 性能问题

#### 问题症状
```
执行时间: 500ms (目标: <100ms)
内存使用: 1GB (目标: <512MB)
```

#### 诊断步骤

**Step 1: 性能分析**
```python
import time
import psutil
import tracemalloc

def performance_analysis():
    # 启动内存跟踪
    tracemalloc.start()
    
    # 记录开始状态
    start_time = time.time()
    start_memory = psutil.Process().memory_info().rss / 1024 / 1024
    
    # 执行优化
    result = run_optimization_test()
    
    # 记录结束状态
    end_time = time.time()
    end_memory = psutil.Process().memory_info().rss / 1024 / 1024
    current, peak = tracemalloc.get_traced_memory()
    tracemalloc.stop()
    
    # 分析结果
    execution_time = end_time - start_time
    memory_used = end_memory - start_memory
    peak_memory = peak / 1024 / 1024
    
    print(f"执行时间: {execution_time:.3f}s")
    print(f"内存使用: {memory_used:.1f}MB")
    print(f"峰值内存: {peak_memory:.1f}MB")
    
    # 性能警告
    if execution_time > 0.1:
        print("⚠️ 执行时间过长")
    if memory_used > 100:
        print("⚠️ 内存使用过多")

performance_analysis()
```

**Step 2: 瓶颈识别**
```python
import cProfile
import pstats

def profile_optimization():
    # 性能分析
    profiler = cProfile.Profile()
    profiler.enable()
    
    # 执行优化
    run_optimization_test()
    
    profiler.disable()
    
    # 分析结果
    stats = pstats.Stats(profiler)
    stats.sort_stats('cumulative')
    stats.print_stats(10)  # 显示前10个最耗时的函数

profile_optimization()
```

#### 解决方案

1. **算法优化**
   ```python
   # 使用更高效的数据结构
   from collections import defaultdict
   
   # 缓存分类结果
   classification_cache = {}
   
   def cached_classify(section_name, section_content):
       cache_key = f"{section_name}_{len(section_content)}"
       if cache_key not in classification_cache:
           classification_cache[cache_key] = classify_section(section_name, section_content)
       return classification_cache[cache_key]
   ```

2. **内存优化**
   ```python
   # 及时释放大对象
   def process_sections(sections):
       for section_name, section_content in sections.items():
           # 处理单个段落
           result = process_single_section(section_name, section_content)
           
           # 立即释放内存
           del section_content
           
           yield result
   ```

3. **并行处理**
   ```python
   from concurrent.futures import ThreadPoolExecutor
   
   def parallel_classification(sections):
       with ThreadPoolExecutor(max_workers=4) as executor:
           futures = {
               executor.submit(classify_section, name, content): name 
               for name, content in sections.items()
           }
           
           results = {}
           for future in futures:
               section_name = futures[future]
               results[section_name] = future.result()
           
           return results
   ```

### 4. 日志编码问题

#### 问题症状
```
日志输出: �🔧开始创建四层优化组件件...
期望输出: 🔧 开始创建四层优化组件...
```

#### 解决方案

1. **设置正确编码**
   ```python
   # 在程序开始时设置编码
   import sys
   import locale
   
   # 设置默认编码
   if sys.platform.startswith('win'):
       import codecs
       sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
       sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())
   
   # 设置locale
   locale.setlocale(locale.LC_ALL, 'zh_CN.UTF-8')
   ```

2. **日志配置优化**
   ```python
   import logging
   
   # 配置日志编码
   logging.basicConfig(
       level=logging.INFO,
       format='%(asctime)s - %(levelname)s - %(message)s',
       handlers=[
           logging.FileHandler('app.log', encoding='utf-8'),
           logging.StreamHandler(sys.stdout)
       ]
   )
   ```

3. **环境变量设置**
   ```bash
   # Windows
   set PYTHONIOENCODING=utf-8
   
   # Linux/Mac
   export PYTHONIOENCODING=utf-8
   export LANG=zh_CN.UTF-8
   export LC_ALL=zh_CN.UTF-8
   ```

## 🔧 调试工具

### 1. 诊断脚本

```python
# diagnosis.py
def full_system_diagnosis():
    """完整系统诊断"""
    
    print("🔍 开始系统诊断...")
    
    # 1. 环境检查
    print("\n1. 环境检查")
    import sys
    print(f"  Python版本: {sys.version}")
    print(f"  平台: {sys.platform}")
    
    # 2. 依赖检查
    print("\n2. 依赖检查")
    try:
        from engine.processing.stages.four_tier_optimization_stage import FourTierOptimizationStage
        print("  ✅ 四层优化模块导入成功")
    except Exception as e:
        print(f"  ❌ 四层优化模块导入失败: {e}")
    
    # 3. 配置检查
    print("\n3. 配置检查")
    try:
        from engine.business.config.config_manager import ConfigManager
        config = ConfigManager()
        print("  ✅ 配置管理器初始化成功")
    except Exception as e:
        print(f"  ❌ 配置管理器初始化失败: {e}")
    
    # 4. 功能检查
    print("\n4. 功能检查")
    try:
        stage = FourTierOptimizationStage(ConfigManager())
        print(f"  optimization_enabled: {stage.optimization_enabled}")
        print(f"  architecture: {stage.optimization_architecture is not None}")
    except Exception as e:
        print(f"  ❌ 功能检查失败: {e}")
    
    print("\n🎉 诊断完成")

if __name__ == "__main__":
    full_system_diagnosis()
```

### 2. 性能监控脚本

```python
# monitor.py
import time
import psutil
import threading

class PerformanceMonitor:
    def __init__(self):
        self.monitoring = False
        self.metrics = []
    
    def start_monitoring(self):
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop)
        self.monitor_thread.start()
    
    def stop_monitoring(self):
        self.monitoring = False
        self.monitor_thread.join()
        return self.metrics
    
    def _monitor_loop(self):
        while self.monitoring:
            cpu_percent = psutil.cpu_percent()
            memory_mb = psutil.Process().memory_info().rss / 1024 / 1024
            
            self.metrics.append({
                'timestamp': time.time(),
                'cpu_percent': cpu_percent,
                'memory_mb': memory_mb
            })
            
            time.sleep(1)

# 使用示例
monitor = PerformanceMonitor()
monitor.start_monitoring()

# 执行优化测试
run_optimization_test()

metrics = monitor.stop_monitoring()
print(f"平均CPU使用率: {sum(m['cpu_percent'] for m in metrics) / len(metrics):.1f}%")
print(f"平均内存使用: {sum(m['memory_mb'] for m in metrics) / len(metrics):.1f}MB")
```

## 📞 技术支持

### 联系方式

- **技术支持邮箱**: <EMAIL>
- **紧急联系电话**: +86-xxx-xxxx-xxxx
- **在线文档**: https://docs.company.com/four-tier-optimization

### 问题报告模板

```
问题标题: [简短描述问题]

环境信息:
- 操作系统: 
- Python版本: 
- 部署环境: [测试/生产]

问题描述:
[详细描述问题现象]

重现步骤:
1. 
2. 
3. 

期望结果:
[描述期望的正确行为]

实际结果:
[描述实际发生的错误行为]

错误日志:
[粘贴相关错误日志]

已尝试的解决方案:
[列出已经尝试过的解决方法]
```

---

**文档版本**: v1.0  
**最后更新**: 2025-08-04  
**维护团队**: 四层优化开发团队
