# FortiGate twice-nat44转换API设计文档

## 📋 API设计概述

本文档详细定义了FortiGate复合NAT转换为twice-nat44所需的API接口、方法签名、数据结构和XML生成规范。

## 🔧 核心API接口设计

### 1. 数据模型API

#### TwiceNat44Rule类
```python
class TwiceNat44Rule:
    """twice-nat44规则数据模型"""
    
    def __init__(self, name: str, enabled: bool, description: str,
                 match_conditions: TwiceNat44MatchConditions,
                 snat_config: TwiceNat44SnatConfig,
                 dnat_config: TwiceNat44DnatConfig):
        """
        初始化twice-nat44规则
        
        Args:
            name: 规则名称
            enabled: 是否启用
            description: 规则描述
            match_conditions: 匹配条件
            snat_config: SNAT配置
            dnat_config: DNAT配置
        """
    
    @classmethod
    def from_fortigate_policy(cls, policy: Dict[str, Any], vip_config: Dict[str, Any]) -> 'TwiceNat44Rule':
        """
        从FortiGate策略创建twice-nat44规则
        
        Args:
            policy: FortiGate策略配置
            vip_config: VIP对象配置
            
        Returns:
            TwiceNat44Rule: 创建的规则对象
            
        Raises:
            ValueError: 当策略配置无效时
            KeyError: 当必需的配置项缺失时
        """
    
    def to_nat_rule_dict(self) -> Dict[str, Any]:
        """
        转换为NAT规则字典格式
        
        Returns:
            Dict[str, Any]: NAT规则字典
        """
    
    def validate(self) -> bool:
        """
        验证规则配置的有效性
        
        Returns:
            bool: 配置是否有效
        """
    
    def get_xml_namespace(self) -> str:
        """
        获取XML命名空间
        
        Returns:
            str: XML命名空间URI
        """
```

#### 匹配条件API
```python
class TwiceNat44MatchConditions:
    """twice-nat44匹配条件"""
    
    def __init__(self, dest_network: str, source_network: Optional[str] = None,
                 service: str = "any", time_range: str = "any"):
        """
        初始化匹配条件
        
        Args:
            dest_network: 目标网络对象名称
            source_network: 源网络对象名称（可选）
            service: 服务对象名称
            time_range: 时间范围名称
        """
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式
        
        Returns:
            Dict[str, Any]: 匹配条件字典
        """
    
    def validate_network_objects(self, available_objects: List[str]) -> bool:
        """
        验证网络对象是否存在
        
        Args:
            available_objects: 可用的网络对象列表
            
        Returns:
            bool: 验证是否通过
        """
```

### 2. FortiGate策略处理API

#### FortigateStrategy扩展方法
```python
class FortigateStrategy:
    """FortiGate策略处理策略（扩展twice-nat44支持）"""
    
    def _supports_twice_nat44(self, policy: Dict[str, Any], vips: Dict[str, Any]) -> bool:
        """
        检查策略是否支持twice-nat44转换
        
        Args:
            policy: FortiGate策略配置
            vips: VIP对象字典
            
        Returns:
            bool: 是否支持twice-nat44转换
            
        Note:
            检查条件包括：
            1. 策略包含VIP对象
            2. 策略启用了NAT
            3. 不使用IP池（暂不支持）
            4. VIP配置完整
        """
    
    def _generate_twice_nat44_rule(self, policy: Dict[str, Any], vip_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成twice-nat44规则
        
        Args:
            policy: FortiGate策略配置
            vip_config: VIP对象配置
            
        Returns:
            Dict[str, Any]: twice-nat44规则配置
            
        Raises:
            ValueError: 当配置无效时
            KeyError: 当必需配置缺失时
        """
    
    def _generate_compound_nat_rules(self, policy: Dict[str, Any], vips: Dict[str, Any], 
                                   ippools: Dict[str, Any], processor: Any) -> List[Dict[str, Any]]:
        """
        生成复合NAT规则（支持twice-nat44和回退机制）
        
        Args:
            policy: 策略配置
            vips: VIP对象字典
            ippools: IP池字典
            processor: NAT处理器
            
        Returns:
            List[Dict[str, Any]]: NAT规则列表
            
        Note:
            优先使用twice-nat44，失败时回退到原有逻辑
        """
    
    def _validate_twice_nat44_config(self, rule_config: Dict[str, Any]) -> bool:
        """
        验证twice-nat44配置的有效性
        
        Args:
            rule_config: twice-nat44规则配置
            
        Returns:
            bool: 配置是否有效
        """
```

### 3. NAT生成器API

#### NatGenerator扩展方法
```python
class NatGenerator:
    """NAT配置生成器（扩展twice-nat44支持）"""
    
    def _add_twice_nat44_config(self, rule_element: etree.Element, twice_nat_config: Dict[str, Any]) -> None:
        """
        添加twice-nat44配置到XML元素
        
        Args:
            rule_element: 规则XML元素
            twice_nat_config: twice-nat44配置字典
            
        Raises:
            ValueError: 当配置格式无效时
            etree.XMLSyntaxError: 当XML生成失败时
        """
    
    def _add_twice_nat_snat_config(self, snat_element: etree.Element, snat_config: Dict[str, Any]) -> None:
        """
        添加twice-nat44 SNAT配置
        
        Args:
            snat_element: SNAT XML元素
            snat_config: SNAT配置字典
            
        Note:
            支持的地址类型：
            - interface: 使用出接口地址
            - ip: 使用指定IP地址
            - pool: 使用IP地址池
        """
    
    def _add_twice_nat_dnat_config(self, dnat_element: etree.Element, dnat_config: Dict[str, Any]) -> None:
        """
        添加twice-nat44 DNAT配置
        
        Args:
            dnat_element: DNAT XML元素
            dnat_config: DNAT配置字典
        """
    
    def _validate_twice_nat44_xml(self, rule_element: etree.Element) -> bool:
        """
        验证twice-nat44 XML结构的正确性
        
        Args:
            rule_element: 规则XML元素
            
        Returns:
            bool: XML结构是否正确
        """
```

### 4. XML模板集成API

#### XMLTemplateIntegrationStage扩展方法
```python
class XMLTemplateIntegrationStage:
    """XML模板集成阶段（扩展twice-nat44支持）"""
    
    def _integrate_twice_nat44_rules(self, template_root: etree.Element, context: DataContext) -> bool:
        """
        集成twice-nat44规则到XML模板
        
        Args:
            template_root: 模板根元素
            context: 数据上下文
            
        Returns:
            bool: 集成是否成功
            
        Note:
            利用已重构的通用方法：
            - _parse_xml_fragment_robust()
            - _find_or_create_vrf_node()
            - _find_child_element_robust()
        """
    
    def _is_twice_nat44_rule(self, rule_element: etree.Element) -> bool:
        """
        检查是否为twice-nat44规则
        
        Args:
            rule_element: 规则XML元素
            
        Returns:
            bool: 是否为twice-nat44规则
        """
    
    def _validate_twice_nat44_integration(self, nat_elem: etree.Element) -> bool:
        """
        验证twice-nat44规则集成的正确性
        
        Args:
            nat_elem: NAT配置XML元素
            
        Returns:
            bool: 集成是否正确
        """
```

## 📊 数据结构规范

### 1. twice-nat44规则字典格式
```python
TWICE_NAT44_RULE_SCHEMA = {
    "name": str,                    # 规则名称
    "type": "twice-nat44",         # 规则类型（固定值）
    "rule_en": bool,               # 是否启用
    "desc": str,                   # 规则描述
    "twice-nat44": {
        "match": {
            "dest-network": {
                "name": str        # 目标网络对象名称
            },
            "source-network": {    # 可选
                "name": str        # 源网络对象名称
            },
            "service": {
                "name": str        # 服务对象名称
            },
            "time-range": {
                "value": str       # 时间范围值
            }
        },
        "snat": {
            # 地址类型（三选一）
            "output-address": {},   # 使用出接口地址
            "ipv4-address": str,   # 使用指定IP地址
            "pool-name": str,      # 使用IP地址池
            
            # PAT配置
            "no-pat": bool,        # 是否禁用端口转换
            "try-no-pat": bool     # 是否尝试不转换端口
        },
        "dnat": {
            "ipv4-address": str,   # 目标IP地址
            "port": int            # 目标端口（可选）
        }
    }
}
```

### 2. XML生成规范
```xml
<!-- twice-nat44规则XML结构 -->
<rule>
    <name>POLICY_NAME_twice_nat</name>
    <rule_en>true</rule_en>
    <desc>FortiGate复合NAT策略 POLICY_NAME</desc>
    <twice-nat44>
        <match>
            <dest-network>
                <name>VIP_ADDRESS_OBJECT</name>
            </dest-network>
            <service>
                <name>SERVICE_NAME</name>
            </service>
            <time-range>
                <value>any</value>
            </time-range>
        </match>
        <snat>
            <output-address/>
            <no-pat>false</no-pat>
            <try-no-pat>true</try-no-pat>
        </snat>
        <dnat>
            <ipv4-address>*************</ipv4-address>
            <port>8080</port>
        </dnat>
    </twice-nat44>
</rule>
```

## 🔧 配置API

### 配置参数定义
```python
TWICE_NAT44_CONFIG_SCHEMA = {
    "nat": {
        "use_twice_nat44": {
            "type": bool,
            "default": True,
            "description": "启用twice-nat44转换"
        },
        "twice_nat44_fallback": {
            "type": bool,
            "default": True,
            "description": "twice-nat44失败时回退到原有逻辑"
        },
        "validate_twice_nat44": {
            "type": bool,
            "default": True,
            "description": "启用twice-nat44配置验证"
        },
        "twice_nat44_debug": {
            "type": bool,
            "default": False,
            "description": "启用twice-nat44调试模式"
        }
    }
}
```

### 配置访问API
```python
class ConfigManager:
    """配置管理器"""
    
    def get_twice_nat44_config(self, key: str, default: Any = None) -> Any:
        """
        获取twice-nat44相关配置
        
        Args:
            key: 配置键名（如"use_twice_nat44"）
            default: 默认值
            
        Returns:
            Any: 配置值
        """
    
    def set_twice_nat44_config(self, key: str, value: Any) -> None:
        """
        设置twice-nat44相关配置
        
        Args:
            key: 配置键名
            value: 配置值
        """
    
    def validate_twice_nat44_config(self) -> bool:
        """
        验证twice-nat44配置的有效性
        
        Returns:
            bool: 配置是否有效
        """
```

## 🧪 测试API

### 单元测试接口
```python
class TwiceNat44TestCase(unittest.TestCase):
    """twice-nat44测试基类"""
    
    def setUp(self) -> None:
        """测试初始化"""
    
    def create_test_policy(self, **kwargs) -> Dict[str, Any]:
        """
        创建测试用的FortiGate策略
        
        Args:
            **kwargs: 策略配置参数
            
        Returns:
            Dict[str, Any]: 测试策略配置
        """
    
    def create_test_vip(self, **kwargs) -> Dict[str, Any]:
        """
        创建测试用的VIP对象
        
        Args:
            **kwargs: VIP配置参数
            
        Returns:
            Dict[str, Any]: 测试VIP配置
        """
    
    def assert_twice_nat44_rule_valid(self, rule: Dict[str, Any]) -> None:
        """
        断言twice-nat44规则有效
        
        Args:
            rule: twice-nat44规则配置
            
        Raises:
            AssertionError: 当规则无效时
        """
    
    def assert_xml_structure_valid(self, xml_element: etree.Element) -> None:
        """
        断言XML结构有效
        
        Args:
            xml_element: XML元素
            
        Raises:
            AssertionError: 当XML结构无效时
        """
```

## 📈 性能监控API

### 性能指标收集
```python
class TwiceNat44PerformanceMonitor:
    """twice-nat44性能监控器"""
    
    def start_timing(self, operation: str) -> str:
        """
        开始计时
        
        Args:
            operation: 操作名称
            
        Returns:
            str: 计时器ID
        """
    
    def end_timing(self, timer_id: str) -> float:
        """
        结束计时
        
        Args:
            timer_id: 计时器ID
            
        Returns:
            float: 耗时（秒）
        """
    
    def record_rule_count(self, original_count: int, twice_nat44_count: int) -> None:
        """
        记录规则数量变化
        
        Args:
            original_count: 原始规则数量
            twice_nat44_count: twice-nat44规则数量
        """
    
    def get_performance_report(self) -> Dict[str, Any]:
        """
        获取性能报告
        
        Returns:
            Dict[str, Any]: 性能指标报告
        """
```

## 🔄 错误处理API

### 异常定义
```python
class TwiceNat44Error(Exception):
    """twice-nat44相关异常基类"""
    pass

class TwiceNat44ConfigError(TwiceNat44Error):
    """twice-nat44配置错误"""
    pass

class TwiceNat44ValidationError(TwiceNat44Error):
    """twice-nat44验证错误"""
    pass

class TwiceNat44XMLError(TwiceNat44Error):
    """twice-nat44 XML生成错误"""
    pass
```

### 错误处理接口
```python
class TwiceNat44ErrorHandler:
    """twice-nat44错误处理器"""
    
    def handle_config_error(self, error: TwiceNat44ConfigError, context: Dict[str, Any]) -> bool:
        """
        处理配置错误
        
        Args:
            error: 配置错误
            context: 错误上下文
            
        Returns:
            bool: 是否可以继续处理
        """
    
    def handle_validation_error(self, error: TwiceNat44ValidationError, context: Dict[str, Any]) -> bool:
        """
        处理验证错误
        
        Args:
            error: 验证错误
            context: 错误上下文
            
        Returns:
            bool: 是否可以继续处理
        """
```

---

**本API设计文档为twice-nat44转换功能的实现提供了完整的接口规范，确保代码的一致性和可维护性。**
