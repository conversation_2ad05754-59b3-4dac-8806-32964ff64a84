#!/usr/bin/env python3
"""
验证网络对象引用修复效果
"""

import sys
import os
import xml.etree.ElementTree as ET
from typing import Dict, List, Set, Tuple
from collections import defaultdict

def parse_xml_file(xml_file_path: str) -> ET.Element:
    """解析XML文件"""
    try:
        tree = ET.parse(xml_file_path)
        return tree.getroot()
    except Exception as e:
        print(f"❌ 解析XML文件失败: {e}")
        return None

def extract_network_object_definitions(root: ET.Element) -> Set[str]:
    """提取所有网络对象定义的名称"""
    definitions = set()

    # 递归查找所有元素
    def find_elements_by_tag(element, tag_name):
        results = []
        if element.tag.endswith(tag_name):
            results.append(element)
        for child in element:
            results.extend(find_elements_by_tag(child, tag_name))
        return results

    # 查找所有address-set定义
    for address_set in find_elements_by_tag(root, 'address-set'):
        for child in address_set:
            if child.tag.endswith('name') and child.text:
                definitions.add(child.text)
                break

    # 查找所有address-group定义
    for address_group in find_elements_by_tag(root, 'address-group'):
        for child in address_group:
            if child.tag.endswith('name') and child.text:
                definitions.add(child.text)
                break

    return definitions

def extract_security_policy_references(root: ET.Element) -> Dict[str, List[str]]:
    """提取所有安全策略中的网络对象引用"""
    references = defaultdict(list)

    # 递归查找所有元素
    def find_elements_by_tag(element, tag_name):
        results = []
        if element.tag.endswith(tag_name):
            results.append(element)
        for child in element:
            results.extend(find_elements_by_tag(child, tag_name))
        return results

    # 查找所有安全策略
    for policy in find_elements_by_tag(root, 'policy'):
        policy_name = "未知策略"
        # 获取策略名称
        for child in policy:
            if child.tag.endswith('name') and child.text:
                policy_name = child.text
                break

        # 提取源网络对象引用
        for src_net in find_elements_by_tag(policy, 'source-network'):
            for child in src_net:
                if child.tag.endswith('name') and child.text:
                    references[policy_name].append(("source-network", child.text))
                    break

        # 提取目标网络对象引用
        for dest_net in find_elements_by_tag(policy, 'dest-network'):
            for child in dest_net:
                if child.tag.endswith('name') and child.text:
                    references[policy_name].append(("dest-network", child.text))
                    break

    return references

def check_reference_integrity(definitions: Set[str], references: Dict[str, List[str]]) -> List[Dict]:
    """检查引用完整性"""
    issues = []
    
    for policy_name, policy_refs in references.items():
        for ref_type, ref_name in policy_refs:
            if ref_name not in definitions:
                issues.append({
                    "type": "missing_reference",
                    "policy": policy_name,
                    "reference_type": ref_type,
                    "reference_name": ref_name,
                    "description": f"策略 '{policy_name}' 引用的 {ref_type} '{ref_name}' 不存在"
                })
    
    return issues

def check_ip_address_format_consistency(definitions: Set[str], references: Dict[str, List[str]]) -> List[Dict]:
    """检查IP地址格式一致性"""
    import re
    issues = []
    
    # IP地址格式模式
    ip_with_dots = re.compile(r'^\d+\.\d+\.\d+\.\d+$')
    ip_with_underscores = re.compile(r'^\d+_\d+_\d+_\d+$')
    
    # 检查定义中的IP地址格式
    ip_definitions = {}
    for def_name in definitions:
        if ip_with_dots.match(def_name):
            ip_definitions[def_name] = "dots"
        elif ip_with_underscores.match(def_name):
            ip_definitions[def_name] = "underscores"
    
    # 检查引用中的IP地址格式
    for policy_name, policy_refs in references.items():
        for ref_type, ref_name in policy_refs:
            if ip_with_dots.match(ref_name):
                # 引用使用点号格式，检查是否有对应的定义
                underscore_version = ref_name.replace('.', '_')
                if underscore_version in definitions and ref_name not in definitions:
                    issues.append({
                        "type": "format_inconsistency",
                        "policy": policy_name,
                        "reference_type": ref_type,
                        "reference_name": ref_name,
                        "expected_name": underscore_version,
                        "description": f"策略 '{policy_name}' 引用 '{ref_name}' 使用点号格式，但定义使用下划线格式 '{underscore_version}'"
                    })
            elif ip_with_underscores.match(ref_name):
                # 引用使用下划线格式，检查是否有对应的点号格式定义
                dots_version = ref_name.replace('_', '.')
                if dots_version in definitions and ref_name not in definitions:
                    issues.append({
                        "type": "format_inconsistency",
                        "policy": policy_name,
                        "reference_type": ref_type,
                        "reference_name": ref_name,
                        "expected_name": dots_version,
                        "description": f"策略 '{policy_name}' 引用 '{ref_name}' 使用下划线格式，但定义使用点号格式 '{dots_version}'"
                    })
    
    return issues

def analyze_specific_case(definitions: Set[str], references: Dict[str, List[str]]) -> Dict:
    """分析特定案例：************ vs 10_128_0_104"""
    result = {
        "target_ip": "************",
        "definition_exists": False,
        "underscore_definition_exists": False,
        "references": [],
        "underscore_references": []
    }
    
    # 检查定义
    if "************" in definitions:
        result["definition_exists"] = True
    if "10_128_0_104" in definitions:
        result["underscore_definition_exists"] = True
    
    # 检查引用
    for policy_name, policy_refs in references.items():
        for ref_type, ref_name in policy_refs:
            if ref_name == "************":
                result["references"].append((policy_name, ref_type))
            elif ref_name == "10_128_0_104":
                result["underscore_references"].append((policy_name, ref_type))
    
    return result

def main():
    """主函数"""
    xml_file = "output/fortigate-z5100s-R11_backup_20250804_161148.xml"
    
    if not os.path.exists(xml_file):
        print(f"❌ XML文件不存在: {xml_file}")
        return False
    
    print("网络对象引用修复效果验证")
    print("=" * 60)
    
    # 解析XML文件
    print("📄 解析XML文件...")
    root = parse_xml_file(xml_file)
    if root is None:
        return False
    
    # 提取网络对象定义和引用
    print("🔍 提取网络对象定义...")
    definitions = extract_network_object_definitions(root)
    print(f"   找到 {len(definitions)} 个网络对象定义")
    
    print("🔍 提取安全策略引用...")
    references = extract_security_policy_references(root)
    total_refs = sum(len(refs) for refs in references.values())
    print(f"   找到 {len(references)} 个策略，共 {total_refs} 个网络对象引用")
    
    # 执行检查
    print("\n🔍 检查引用完整性...")
    integrity_issues = check_reference_integrity(definitions, references)
    print(f"   发现 {len(integrity_issues)} 个引用完整性问题")
    
    print("🔍 检查IP地址格式一致性...")
    format_issues = check_ip_address_format_consistency(definitions, references)
    print(f"   发现 {len(format_issues)} 个格式一致性问题")
    
    print("🔍 分析特定案例...")
    specific_analysis = analyze_specific_case(definitions, references)
    
    # 生成报告
    print("\n" + "=" * 60)
    print("📊 验证结果报告")
    print("=" * 60)
    
    # 特定案例分析
    print("\n📋 特定案例分析 (************):")
    print("-" * 40)
    print(f"定义存在 (点号格式): {specific_analysis['definition_exists']}")
    print(f"定义存在 (下划线格式): {specific_analysis['underscore_definition_exists']}")
    print(f"引用 (点号格式): {len(specific_analysis['references'])} 个")
    print(f"引用 (下划线格式): {len(specific_analysis['underscore_references'])} 个")
    
    if specific_analysis['references']:
        print("  点号格式引用详情:")
        for policy, ref_type in specific_analysis['references']:
            print(f"    - 策略 '{policy}' 的 {ref_type}")
    
    if specific_analysis['underscore_references']:
        print("  下划线格式引用详情:")
        for policy, ref_type in specific_analysis['underscore_references']:
            print(f"    - 策略 '{policy}' 的 {ref_type}")
    
    # 引用完整性问题
    if integrity_issues:
        print(f"\n📋 引用完整性问题 ({len(integrity_issues)} 个):")
        print("-" * 40)
        for i, issue in enumerate(integrity_issues, 1):
            print(f"{i:2d}. {issue['description']}")
    else:
        print("\n✅ 引用完整性检查通过")
    
    # 格式一致性问题
    if format_issues:
        print(f"\n📋 格式一致性问题 ({len(format_issues)} 个):")
        print("-" * 40)
        for i, issue in enumerate(format_issues, 1):
            print(f"{i:2d}. {issue['description']}")
            print(f"     建议修复: 将引用 '{issue['reference_name']}' 改为 '{issue['expected_name']}'")
    else:
        print("\n✅ 格式一致性检查通过")
    
    # 总结
    total_issues = len(integrity_issues) + len(format_issues)
    print("\n" + "=" * 60)
    print(f"📈 验证总结: 发现 {total_issues} 个问题")
    
    if total_issues == 0:
        print("✅ 所有检查通过！网络对象引用修复成功")
        print("✅ 引用完整性正常")
        print("✅ 格式一致性正常")
        print("✅ 特定案例修复成功")
    else:
        print("❌ 发现问题需要进一步修复")
        if integrity_issues:
            print(f"   - 引用完整性问题: {len(integrity_issues)} 个")
        if format_issues:
            print(f"   - 格式一致性问题: {len(format_issues)} 个")
    
    return total_issues == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
